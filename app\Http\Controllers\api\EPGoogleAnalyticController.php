<?php

namespace App\Http\Controllers\api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

class EPGoogleAnalyticController extends Controller
{
    public function getGoogleAnalyticsData()
    {
        try {
            // Set the working directory to the directory of the Python script
            chdir(base_path('app/Scripts'));
            // Path to your Python script
            $pythonScriptPath = base_path('app/Scripts/GoogleAnalyticService.py');

            // Check if the Python script file exists
            if (!file_exists($pythonScriptPath)) {
                throw new \Exception('Python script file not found.');
            }

            // Execute the Python script and capture the output
            $output = shell_exec("python3 {$pythonScriptPath} 2>&1");
            // dd($output);

            // Check if the execution was successful
            if ($output === null) {
                throw new \Exception('Failed to execute the Python script.');
            }

            // Process the output as needed
            $data = json_decode($output, true);

            // dd($data['data']['page_views']);

            // Check if the JSON decoding was successful
            if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Error decoding JSON data.');
            }

            return response()->json($data);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }
}
