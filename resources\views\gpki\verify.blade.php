@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-notes_2"></i>GPKI Roaming Verify<br>
                <small>To Run Verify testing with GPKI Roaming</small>
            </h1>
        </div>
    </div>
    <div class="block-alt-noborder full block">
        <header class="w3-container w3-theme w3-padding" id="myHeader">
            <div class="w3-center" style="text-align: center;">
                <img src="{{ asset('img/GPKI.png') }}" alt="GPKI AGENT">
                <div class="w3-padding-30">
                    <button class="w3-btn w3-large w3-dark-grey w3-hover-light-grey">VERIFY API</button>
                    <br><br>
                </div>
            </div>
        </header>
        <br>

        <div class="wrapper">
            <form align="center" autocomplete="off">
                <!-- for all medium -->
                <div id="reqNric">
                    Nama Aplikasi:
                    <input type="text" id="applicationCode" name="applicationCode" value="ePerolehan"><br><br>
                    ID Sijil Digital:
                    <input type="text" id="nric" name="nric" value="" maxlength="12"><br><br>
                    <input type="checkbox" id="mode" name="mode" value="Training">Mode Latihan<br><br>
                    <input type="button" id="requestMedium" value="Submit">
                </div>


                <div id="listMedium">
                    ID Sijil Digital:
                    <input type="text" id="nric2" name="nric2" readonly type="text"><br><br>
                    Medium:
                    <select id="mediumId" name="mediumId"></select><br><br>

                    <input type="button" id="nextStep" value="Submit">
                </div>

                <!-- for roaming otp only -->
                <div id="reqOtp">
                    ID Pengguna: (Berdaftar Mobile)<br>
                    <input type="text" id="userId" name="userId" value="" maxlength="12"><br>
                    ID Sijil Digital:<br>
                    <input type="text" id="nric3" name="nric3" value=""><br>

                    Agency ID: <br>
                    <input type="text" id="agencyId" name="agencyId" value="GOVICT_617244"><br>

                    Tempoh Tamat OTP: (Saat) <br>
                    <input type="text" id="otpExp" name="otpExp" value="120" pattern="[0-9]+"><br>

                    Saluran Penerimaan OTP<br>
                    <input type="radio" id="1" name="saluran" value="mobil" checked>
                    <label for="mobil">Mobile</label>
                    <input type="radio" id="2" name="saluran" value="email">
                    <label for="email">E-mel</label><br><br>

                    <div id="reqEmail">
                        E-mel:<br>
                        <input type="text" id="emailuser" name="emailuser" value=""><br><br>
                    </div>

                    <input type="hidden" id="mediumId2" name="mediumId2">
                    <input type="button" id="requestOtp" value="Generate OTP">
                </div>

                <!-- request verify roaming otp only -->
                <div id="reqVerify">

                    Id Transaksi : <br>
                    <input type="text" id="nonce" name="nonce" value="" disabled><br>
                    Kod OTP: <br>
                    <input type="text" id="otpCode" name="otpCode" value=""><br>
                    Original Data: <br>
                    <textarea id="plaintext" rows="10" cols="100">data</textarea> <br>
                    Signed Data: <br>
                    <textarea id="signature" rows="10" cols="100"></textarea>
                    <input type="hidden" id="userId2" name="userId2">
                    <input type="hidden" id="nric4" name="nric4">
                    <br>

                    <br>
                    <input type="button" id="requestVerify" value="Verify">
                </div>

                <!-- request verify softcert, roaming & token -->
                <div id="reqVerifySoftToken">
                    <br>
                    originalText: <br>
                    <textarea name="originalText" id="originalText" rows="5" cols="100">data</textarea><br>
                    signedText: <br>
                    <textarea name="signedText" id="signedText" rows="25" cols="100"></textarea><br>
                    <br>
                    <input type="button" name="verifyButton" id="verifyButton" value="Verify">
                </div>

                <div id="gpki-data"></div>
            </form>
        </div>

        <br>
        
        <div class="wrapper">
            <button onClick="window.location.reload();">Refresh</button>
        </div>
    </div>
@endsection


@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
@endsection