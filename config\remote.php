<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Remote Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify the default connection that will be used for SSH
    | operations. This name should correspond to a connection name below
    | in the server list. Each connection will be manually accessible.
    |
    */

    'default' => 'production',

    /*
    |--------------------------------------------------------------------------
    | Remote Server Connections
    |--------------------------------------------------------------------------
    |
    | These are the servers that will be accessible via the SSH task runner
    | facilities of Laravel. This feature radically simplifies executing
    | tasks on your servers, such as deploying out these applications.
    |
    */

    'connections' => [
        'osb' => [
            'host'      => env('SSH_OSB_HOST', '*************'),
            'username'  => env('SSH_OSB_USERNAME', 'epss_support'),
            'password'  => env('SSH_OSB_PASSWORD', 'cDc@2023'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 15,
        ],
        'portal' => [
            'host'      => env('SSH_EPPORTAL_HOST', '*************'),
            'username'  => env('SSH_EPPORTAL_USERNAME', 'epss_support'),
            'password'  => env('SSH_EPPORTAL_PASSWORD', 'cDc@2023'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],
        'bpm' => [
            'host'      => env('SSH_BPM_HOST', '*************'),
            'username'  => env('SSH_BPM_USERNAME', 'support'),
            'password'  => env('SSH_BPM_PASSWORD', '5uPP0rt!@#'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],
        'epss-prod' => [
            'host'      => env('SSH_EPSS_HOST', '**************'),
            'username'  => env('SSH_EPSS_USERNAME', 'root'),
            'password'  => env('SSH_EPSS_PASSWORD', 'xs2root'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 30,
        ],
        'log-trace' => [
            'host'      => env('SSH_LOGTRACE_HOST', '*************'),
            'username'  => env('SSH_LOGTRACE_USERNAME', 'dyna'),
            'password'  => env('SSH_LOGTRACE_PASSWORD', 'cDc@2021'),
            'key'       => '',
            'keytext'   => '',
            'keyphrase' => '',
            'agent'     => '',
            'timeout'   => 120,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Remote Server Groups
    |--------------------------------------------------------------------------
    |
    | Here you may list connections under a single group name, which allows
    | you to easily access all of the servers at once using a short name
    | that is extremely easy to remember, such as "web" or "database".
    |
    */

    'groups' => [
        'web' => ['production'],
    ],

];


