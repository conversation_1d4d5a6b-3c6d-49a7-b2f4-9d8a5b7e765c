<!-- MODAL: LIST Transaction DO/FN -->
@if(count($listDataDoFn) > 0)
<div class="row">
    <div class="col-sm-12">
        <div class="table-responsive">
            <table id="items-supplier-datatable" class="table table-bordered table-vcenter">
                <thead>
                    <tr>
                        <th class="text-center">PO /CO Number</th>
                        <th class="text-center">DO Number</th>
                        <th class="text-center">DO Status</th>
                        <th class="text-center">FRN Number</th>
                        <th class="text-center">FRN Status</th>
                        <th class="text-center">Delivery Order ID</th>
                        <th class="text-center">Fulfilment Note ID</th>
                    </tr>
                </thead>
                <tbody>
                
                    @foreach ($listDataDoFn as  $indexKey => $data)   
                    <tr>
                        <td class="text-center">{{ $data->poco_no}}</td>
                        <td class="text-center">{{ $data->delivery_order_no }}</td>
                        <td class="text-center">{{ $data->do_status }} - {{ $data->do_status_name }}</td>
                        <td class="text-center">{{ $data->frn_no }}</td>
                        <td class="text-center">{{ $data->frn_status }} - {{ $data->frn_status_name }}</td>
                        <td class="text-center">{{ $data->delivery_order_id }}</td>
                        <td class="text-center">{{ $data->fulfilment_note_id }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>    
    </div>
</div>
@else
<div class="">
    <div class="block-title">
        <h2><i class="fa fa-ban"></i> <strong>No transactions. </strong></h2>
    </div>
</div>
@endif