<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use DateTime;
use DateInterval;
use DatePeriod;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use Guzzle;
use GuzzleHttp\Client;


class ResendGFMASIntegrationPayload {

    public static function resendMM501Payload(){

        //Requested on IGFMAS on 14/2/2020 to resend back. 418 docNos
        $listPocoNo = array(
            'PO210000000057084',
            'CO210000000104649',
            'CO210000000104661',
            'PO210000000057458',
            'CO210000000101964',
            'CO210000000104670',
            'CO210000000102838',
            'CO210000000104293',
            'PO210000000054443',
            'PO210000000054444',
            'CO210000000103135',
            'PO210000000057784',
            'PO210000000053348',
            'PO210000000053334',
            'PO210000000054273',
            'PO210000000050996',
            'PO210000000050999',
            'PO210000000052148'

        );
        foreach ($listPocoNo as $pocoNo) {
            self::resendMM501PayloadByDocNo($pocoNo);
        }
    }

    public static function resendMM506Payload(){

        //Requested on IGFMAS on 15/2/2020 to resend back. 418 docNos
        $listPocoNo = array(
            'PO210000000001074',
'PO210000000001156',
'PO210000000001160',
'PO210000000001161',
'PO210000000001457',
'PO210000000003418',
'PO210000000005573',
'PO210000000004789',
'PO210000000007581',
'PO210000000007891',
'PO210000000007237',
'PO210000000008788',
'PO210000000008929',
'PO210000000009124',
'PO210000000009170',
'PO210000000007546',
'PO210000000009392',
'PO210000000009604',
'PO210000000009931',
'PO210000000010417',
'PO210000000010555',
'PO210000000010208',
'PO210000000013161',
'PO210000000013899',
'PO210000000013995',
'PO210000000015908',
'PO210000000016088',
'PO210000000016260',
'PO210000000017114',
'PO210000000018006',
'PO210000000018500',
'PO210000000019381',
'PO210000000019371',
'PO210000000019102',
'PO210000000020517',
'PO210000000021958',
'PO210000000022814',
'PO210000000023135',
'PO210000000023192',
'PO210000000026162',
'PO210000000026616',
'PO210000000028312',
'PO210000000028793',
'PO210000000019396',
'PO210000000030439',
'PO210000000031228',
'PO210000000032863',
'PO210000000033259',
'PO210000000033491',
'PO210000000031950',
'PO210000000034789',
'PO210000000034926',
'PO210000000035105',
'PO210000000031171',
'PO210000000036450',
'PO210000000037330',
'PO210000000037633',
'PO210000000038421',
'PO210000000038719',
'PO210000000039104',
'PO210000000041673',
'PO210000000043301',
'PO210000000047585',
'PO210000000045924',
'CO210000000002330',
'CO210000000010820',
'CO210000000010859',
'CO210000000010940',
'CO210000000014252',
'CO210000000009919',
'CO210000000016247',
'CO210000000018631',
'CO210000000019421',
'CO210000000024217',
'CO210000000027493',
'CO210000000029054',
'CO210000000029649',
'CO210000000032644',
'CO210000000034847',
'CO210000000036139',
'CO210000000039107',
'CO210000000038706',
'CO210000000041667',
'CO210000000042025',
'CO210000000044099',
'CO210000000044554',
'CO210000000044496',
'CO210000000045427',
'CO210000000046424',
'CO210000000046585',
'CO210000000048056',
'CO210000000049589',
'CO210000000050078',
'CO210000000051118',
'CO210000000050468',
'CO210000000051667',
'CO210000000050017',
'CO210000000056523',
'CO210000000056552',
'CO210000000056541',
'CO210000000056538',
'CO210000000056266',
'CO210000000058074',
'CO210000000059529',
'CO210000000058163',
'CO210000000060340',
'CO210000000060274',
'CO210000000061497',
'CO210000000062654',
'CO210000000065239',
'CO210000000067880',
'CO210000000068354',
'CO210000000069053',
'CO210000000069440',
'CO210000000070705',
'CO210000000072267',
'CO210000000072597',
'CO210000000072984',
'CO210000000074318',
'CO210000000074324',
'CO210000000075130',
'CO210000000076140',
'CO210000000079049',
'CO210000000079780',
'CO210000000079972',
'CO210000000082863',
'CO210000000083609',
'CO210000000083606',
'CO210000000086624',
'CO210000000087701',
'CO210000000089543',
'CO210000000089624'


        );
        foreach ($listPocoNo as $pocoNo) {
            self::resendMM506PayloadByDocNo($pocoNo);
        }
    }

    public static function resendMM501PayloadByDocNo($pocoNo) {
        dump('check doc no : '.$pocoNo);
        $resStatus = self::getActiveStatusPrCr(null,$pocoNo);
        if(count($resStatus) > 0 ){
            $obj = $resStatus[0];
            dump('  Active Status as :'.$obj->status_name);
            /**
             * -- Pending CR Review from 1GFMAS , 40610
             * -- Pending PR Review from 1GFMAS , 40110
             */
            if($obj->status_id == '40610' || $obj->status_id == '40110' ){
               // check OSB is last resend date
               $objOsb = self::getOsbLogMM501ByIBReq($pocoNo);
               
               if(count($objOsb) > 0){
                   //dump($objOsb);
                   $transDate = Carbon::parse($objOsb[0]->trans_date);
                   //dump($transDate);
                   if(carbon::now()->diffInDays($transDate) > 0){
                        dump("  we should resend MM501");
                        $xml = self::wrapPayloadMM501Osb($objOsb[0]->payload_body);
                        //dump($xml);
                        self::resendMM501Service($xml);
                        sleep(10);
                        $objOsbCheck = self::getOsbLogMM501ByIBReq($pocoNo);
                        dump("  latest transdate : ".$objOsbCheck[0]->trans_date);
                        $objOsbResp = self::getOsbLogIbResByTransId($objOsbCheck[0]->trans_id,"GFM-100");
                        if(count($objOsbResp) > 0){
                            dump('  Response IFGMAS : '.$objOsbResp[0]->status_desc);
                        }
                        
                   }else{
                       dump("   No need to resend. Last send MM501 on ".$objOsb[0]->trans_date);
                   }
               }

            }
        }
        
    }

    /**
     * POCO status :  Pending Payment Instruction Query from 1GFMAS 
     */
    public static function resendMM506PayloadByDocNo($pocoNo) {
        dump('check doc no : '.$pocoNo);
        $resStatus = self::getActiveStatusPoco($pocoNo);
        if(count($resStatus) > 0 ){
            $obj = $resStatus[0];
            dump('  Active Status as :'.$obj->status_name);
            /**
             *-- Pending Payment Instruction Query from 1GFMAS , 41526
             *-- Pending Payment Instruction Query from 1GFMAS , 41026
             */
            dump('  RESEND WILL TRIGGER IF STATUS : Pending Payment Instruction Query from 1GFMAS');
            if($obj->status_id == '41526' || $obj->status_id == '41026' ){
               // check OSB is last resend date
               $objOsb = self::getOsbLogMM506ByIBReq($pocoNo);
               
               if(count($objOsb) > 0){
                   //dump($objOsb);
                   $transDate = Carbon::parse($objOsb[0]->trans_date);
                   //dump($transDate);
                   if(carbon::now()->diffInDays($transDate) > 0){
                        dump("  we should resend MM506");
                        $xml = self::wrapPayloadMM506Osb($objOsb[0]->payload_body);
                        //dump($xml);
                        self::resendMM506Service($xml);
                        sleep(10);
                        $objOsbCheck = self::getOsbLogMM506ByIBReq($pocoNo);
                        dump("  latest transdate : ".$objOsbCheck[0]->trans_date);
                        $objOsbResp = self::getOsbLogIbResByTransId($objOsbCheck[0]->trans_id,"GFM-120");
                        if(count($objOsbResp) > 0){
                            dump('  Response IFGMAS : '.$objOsbResp[0]->status_desc);
                        }
                        
                   }else{
                       dump("   No need to resend. Last send MM506 on ".$objOsb[0]->trans_date);
                   }
               }

            }
        }
        
    }
    
    protected static function wrapPayloadMM501Osb($xml){
        $xmlFullPayload    = 
        '<env:Envelope  xmlns:env="http://schemas.xmlsoap.org/soap/envelope/"  xmlns:ful1="http://www.ep.gov.my/Schema/1-0/FulfillmentReceivingNote">'. 
        '<env:Header/>'.
        $xml.
        '</env:Envelope>';

        return  $xmlFullPayload;
    }

    protected static function wrapPayloadMM506Osb($xml){
        $xmlFullPayload    = 
        '<env:Envelope xmlns:env="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns6="http://www.ep.gov.my/Schema/1-0/PaymentInstruction">'. 
        '<env:Header/>'.
        $xml.
        '</env:Envelope>';

        return  $xmlFullPayload;
    }

    protected static  function resendMM501Service($xmlData) {
        dump('  Start resendMM501Service...');

        $client = new Client([
            'base_uri' => 'http://192.168.63.205:7011',
          ]);
        $payload = $xmlData;
        $response = $client->post('http://192.168.63.205:7011/POContractForGoodsAndServices/v1.4', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/POContractForGoodsAndServices/inquire',
            ]
        ]);
        $body = $response->getStatusCode();
        dump("  status code send MM501: ".$body);

    }

    protected static  function resendMM506Service($xmlData) {
        dump('  Start resendMM506Service...');

        $client = new Client([
            'base_uri' => 'http://192.168.63.205:7011',
          ]);
        $payload = $xmlData;
        $response = $client->post('http://192.168.63.205:7011/PaymentInstruction/v1.6', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'http://www.ep.gov.my/Schema/1-0/PaymentInstruction/inquire',
            ]
        ]);
        $body = $response->getStatusCode();
        dump("  status code send MM501: ".$body);

    }

    protected static function getActiveStatusPrCr($prcrNo,$pocoNo=null) {

        $query = "SELECT
                fr.fulfilment_req_id,
                fr.DOC_NO AS prcr_no,
                fo.DOC_NO AS poco_no,
                psd.status_name,
                psd.status_id,
                ws.is_current,
                ws.created_date
            FROM
                fl_fulfilment_request fr,
                fl_fulfilment_order fo,
                fl_workflow_status ws,
                pm_status_desc psd
            WHERE
                fr.FULFILMENT_req_id = fo.FULFILMENT_req_id 
                AND fr.FULFILMENT_REQ_ID = ws.DOC_ID 
                AND ws.DOC_TYPE = fr.DOC_TYPE
                AND ws.STATUS_ID = psd.STATUS_ID
                AND psd.LANGUAGE_CODE = 'en' 
                AND ws.IS_CURRENT = 1 ";
        if($prcrNo != null){
            $query = $query. " AND fr.doc_no = ?  ";
            return  DB::connection('oracle_nextgen_rpt')->select( $query, array($prcrNo));
        } 
        if($pocoNo != null){
            $query = $query. " AND fo.doc_no = ?  ";
            return  DB::connection('oracle_nextgen_rpt')->select( $query, array($pocoNo));
        } 
        return null;
    }

    protected static function getActiveStatusPoco($pocoNo) {

        $query = "SELECT
        fo.DOC_NO AS poco_no,
        psd.status_name,
        psd.status_id,
        ws.is_current,
        ws.created_date
    FROM
        fl_fulfilment_order fo,
        fl_workflow_status ws,
        pm_status_desc psd
    WHERE
        fo.FULFILMENT_ORDER_ID = ws.DOC_ID 
        AND ws.DOC_TYPE = fo.DOC_TYPE
        AND ws.STATUS_ID = psd.STATUS_ID
        AND psd.LANGUAGE_CODE = 'en' 
        AND ws.IS_CURRENT = 1 ";
        if($pocoNo != null){
            $query = $query. " AND fo.doc_no = ?  ";
            return  DB::connection('oracle_nextgen_rpt')->select( $query, array($pocoNo));
        } 
        return null;
    }

    protected static function getOsbLogMM501ByIBReq($docNo) {

        $query = "SELECT  a.trans_date,
                    a.trans_id,
                    a.trans_type,
                    a.remarks_1,
                    a.remarks_2,
                    b.payload_body
                    FROM osb_logging a,
                    osb_logging_dtl b 
                    WHERE 
                    a.logging_id = b.logging_id 
                    AND a.service_code = 'GFM-100' 
                    AND a.TRANS_TYPE  = 'IBReq' 
                    AND a.remarks_1 = ? 
                    ORDER BY a.TRANS_DATE  DESC 
                    ";
        return  DB::connection('oracle_nextgen_rpt')->select( $query, array($docNo));
      
    }

    protected static function getOsbLogMM506ByIBReq($docNo) {

        $query = "SELECT  a.trans_date,
                    a.trans_id,
                    a.trans_type,
                    a.remarks_1,
                    a.remarks_2,
                    b.payload_body
                    FROM osb_logging a,
                    osb_logging_dtl b 
                    WHERE 
                    a.logging_id = b.logging_id 
                    AND a.service_code = 'GFM-120' 
                    AND a.TRANS_TYPE  = 'IBReq' 
                    AND a.remarks_2 = ? 
                    ORDER BY a.TRANS_DATE  DESC 
                    ";
        return  DB::connection('oracle_nextgen_rpt')->select( $query, array($docNo));
      
    }

    protected static function getOsbLogIbResByTransId($transId,$serviceCode) {

        $query = "SELECT  a.trans_date,
                    a.trans_id,
                    a.trans_type,
                    a.remarks_1,
                    a.remarks_2,
                    a.remarks_3,
                    a.status_desc,
                    a.status_code 
                    FROM osb_logging a,
                    osb_logging_dtl b 
                    WHERE 
                    a.logging_id = b.logging_id 
                    AND a.TRANS_TYPE  = 'IBRes' 
                    AND a.trans_id = ? 
                    AND a.service_code = ? 
                    ORDER BY a.TRANS_DATE  DESC 
                    ";
        return  DB::connection('oracle_nextgen_rpt')->select( $query, array($transId,$serviceCode));
      
    }


}
