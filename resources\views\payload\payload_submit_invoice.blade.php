@extends('layouts.guest-dash')

@section('header')
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }

    .table tbody > tr > td {
        font-size: 10px;
    }
</style>
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Submit Invoice<br>
            <small>Masukkan <span class="text-info">PO/CO No.</span> pada carian berikut</small>
        </h1>
    </div>
    <div class="block">
        <form id="form-search" action="{{url("/find/fulfilment/submitinv")}}/" method="get" class="form-horizontal" onsubmit="return true;">
            <div class="form-group">
                <label class="col-md-3 control-label" for="doc_no">PO/CO No. <span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input id="doc_no" name="doc_no" class="form-control" placeholder="PO/CO No.." type="text" required
                           value="{{ old('doc_no') }}">
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
</div>

@if($listdata != null && count($listdata) > 0)

@if(isset($listdata["xml_payload"]))
<div class="block">
    <h4>Payload</h4>
    <pre class="line-numbers">
        <code class="language-markup">{{ htmlentities($listdata["xml_payload"]) }}</code>
    </pre>
</div>
@else
<div class="block">
    <h5 class="alert alert-danger"> {{ $listdata["remarks"] }} </h5>
</div>
@endif
@endif

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>
<script>
    $('#page-container').removeAttr('class');
</script>
@endsection



