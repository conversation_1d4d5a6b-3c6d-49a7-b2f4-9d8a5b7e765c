<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use DateTime;
use DateInterval;
use DatePeriod;
use Guzzle;
use GuzzleHttp\Client;

class ClientRazerApi {
    
    public static function getRazerUrl(){
        return env("RAZER_URL_API","https://api.merchant.razer.com");
    }
    public static function getMerchantEp(){
        return env('RAZER_MERCHANT_EP','cdc4_Dev');
    }
    public static function getMerchantEpVkey(){
        return env('RAZER_MERCHANT_EP_VKEY','11504645159a52c481f04ded493c8c13');
    }
    public static function getCalbackUrlEp(){
        return env('RAZER_EP_CALLBACK_URL','http://ngepuat.eperolehan.com.my/NGeP-portlet/molpayCallBack');
    }

    public static function run() {

        $preLog = self::class . ' Completed ' . __FUNCTION__ . ' >> ' ;
        MigrateUtils::logDump($preLog.'Starting ... ');
        
        // 655232	1315827800
        //self::checkOrderDetail('751768','120.00');
        //self::getApiQueryDetail('1603076032','120.00');
        //self::dailyReportTrans('2023-10-25 09:36:50','30'); 

        self::dailyReportTrans('2024-09-18 21:56:30','10'); 
        //self::dailyReportTrans('2022-04-25');
        
       
        $collect = collect();
        $collect->put('amount','400.00');
        $collect->put('orderid','902438');
        $collect->put('tranID','****************');  // If credit should same with MOLPay_tranID
        $collect->put('status','00');
        $collect->put('appcode','');
        $collect->put('currency','MYR');
        $collect->put('channel','fpx');  // fpx / credit
        $collect->put('paydate','2024-05-15 21:05:09'); 
        //$collect->put('extraP','{"ccbrand":"VISA","cctype":"CREDIT","cclast4":"7209","MOLPay_tranID":"**********","bank_datetime":"2023-10-16 13:50:24"}');
        $collect->put('extraP','{"ccbrand":"","cctype":"","cclast4":"","MOLPay_tranID":"**********","bank_datetime":"2024-05-15 21:06:01"}');
        $collect->put('domain',self::getMerchantEp());
        $collect->put('vkey',self::getMerchantEpVkey());
        $collect->put('urlCallback',self::getCalbackUrlEp());
        //$collect->put('urlCallback','http://localhost:8000/payment/response-callback');
        
        // enable this method to trigger callback eP
        //self::sendCallback($collect);

    }

    public static function sendCallback($collect){

        $nbcb = 1;  // Default for Callback
        $amount = $collect->get('amount') ; // '50.00';
        $orderid = $collect->get('orderid') ; // '16514';
        $tranID = $collect->get('tranID') ; // '****************';
        $domain = $collect->get('domain') ; // 'cdc4_Dev';
        $status = $collect->get('status') ; // '00';
        $appcode = $collect->get('appcode') ; // '123456';
        $error_code = $collect->get('error_code') ; // '';
        $error_desc = $collect->get('error_desc = ') ; // '';
        $currency = $collect->get('currency') ; // 'MYR';
        $channel = $collect->get('channel') ; // 'fpx'; //channel credit , fpx
        $paydate = $collect->get('paydate') ; // '2022-03-16 15:59:09' ; //Date/Time( YYYY-MM-DD HH:mm:ss)
        $extraP = $collect->get('extraP') ; // 
        // $extraP = '{"MOLPay_tranID":"**********","bank_datetime":"2022-03-16 15:59:10"}';
        // "ccbrand":"Visa","cclast4":"3335","cctype":"Credit"
        // "bank_datetime":"2022-03-16 11:00:00"
        // "bank_datetime":"0000-00-00 00:00:00"

        //  Field : bank_datetime   format value : Date/Time( YYYY-MM-DD HH:mm:ss)
        $vkey = $collect->get('vkey') ; //'11504645159a52c481f04ded493c8c13';  //merchant code key 
        // ***********************************************************/
        $key0 = md5( $tranID.$orderid.$status.$domain.$amount.$currency );
        $skey = md5( $paydate.$domain.$key0.$appcode.$vkey );

        //$urlCallback = 'http://ngepdevwps01.eperolehan.com.my:8888/NGeP-portlet/molpayCallBack';
        //$urlCallback = 'http://ngepuat.eperolehan.com.my/NGeP-portlet/molpayCallBack';
        $urlCallback = $collect->get('urlCallback') ;
        $parameterUrl = "?nbcb=$nbcb&amount=$amount&orderid=$orderid&tranID=$tranID&domain=$domain&status=$status&appcode=$appcode";
        $parameterUrl  = $parameterUrl . "&error_code=$error_code&error_desc=$error_desc&skey=$skey&currency=$currency&channel=$channel&paydate=$paydate&extraP=$extraP";
        $detailUrl = $urlCallback.$parameterUrl;

        MigrateUtils::logDump(__CLASS__." > ".__FUNCTION__." >> URL CALLBACK: ".$detailUrl);

        $response = Guzzle::post($detailUrl);
        $data = utf8_decode($response->getBody()->getContents());
        MigrateUtils::logDump($data);
    }


    /**
     * Daily Transaction Report (Reconciliation)
     */
    public static function dailyReportTrans($dateSearch,$duration='') {
        try {
            //https://api.merchant.razer.com/RMS/API/PSQ/psq-daily.php
            $urlRequest = self::getRazerUrl(). "/RMS/API/PSQ/psq-daily.php";

            $merchantID = self::getMerchantEp(); //Merchant ID in PG system.
            $rdate = $dateSearch;
            $rduration = $duration;
            $version = 4;
            $response_type = 'json';
            $additional_fields = 'StatusDescription,PaidDate,ApprovalCode,BankTransactionID,BankDateTime,BuyerName,MerchantID,ResponseCode,BillingMobileNumber,BillingEmail,CardScheme,CardType'; //
            //$additional_fields = 'all'; //
            $verifyKey = self::getMerchantEpVkey();
            $skey = md5( $rdate . $merchantID . $verifyKey); 
            $param = "?merchantID=$merchantID&rdate=$rdate&rduration=$rduration&skey=$skey&version=$version&response_type=$response_type&additional_fields=$additional_fields";

            $urlDetail = $urlRequest.$param;
            Log::info('UrL Detail: '.$urlDetail);
            
            $data = null;
            try {
             
                $response = Guzzle::get($urlDetail,[
                    'verify' => false,  // This disables SSL verification
                ]);
                $data = json_decode($response->getBody()->getContents());
            }catch (\Exception $e){
                sleep(15);
                Log::info('Failed 1st attemp request.. try 2nd time (final)');
                Log::info('UrL Detail: '.$urlDetail);
                $response = Guzzle::get($urlDetail);
                $data = json_decode($response->getBody()->getContents());
            }
            if($data == null || is_array($data) == false || $data==''){
                Log::info('response is empty'); 
                Log::info($data); 
            }
            Log::info('total records found: '.count($data));
            //dd($data);
            foreach($data as $row){
                
                $dataReceipt = [
                    'order_id' => $row->OrderID,
                    'billing_date' => $row->BillingDate,
                    'tran_id' => $row->TranID,
                    'channel' => $row->Channel,
                    'amount' => $row->Amount,
                    'stat_code' => $row->StatCode,
                    'stat_name' => $row->StatName,
                    'billing_name' => $row->BillingName,
                    'service_item' => $row->ServiceItem,
                    'status_description' => $row->StatusDescription,
                    'paid_date' => self::parseStrDateToCarbon($row->PaidDate),
                    'bank_transaction_id' => $row->BankTransactionID,
                    'approval_code' => $row->ApprovalCode,
                    'billing_email' => $row->BillingEmail,
                    'billing_mobile_no' => $row->BillingMobileNumber,
                    'buyer_name' => $row->BuyerName,
                    'merchant_id' => $row->MerchantID,
                    'card_type' => $row->CardType,
                    'card_scheme' => $row->CardScheme,
                    'card_bin' => $row->BIN,
                    'response_code' => $row->ResponseCode,
                    'last_updated' => Carbon::now(),
                ];

                $bankDateTime = self::parseStrDateToCarbon($row->BankDateTime);
                // BankDateTime will update if date is invalid only
                if($bankDateTime != null && $bankDateTime->year != 1970){
                    $dataReceipt['bank_date_time'] = $bankDateTime ;
                }
               
                $pymtObj = DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')
                    ->where('order_id',$row->OrderID)->where('tran_id',$row->TranID)
                    ->first();
                if($pymtObj){
                    try {
                        $pymtObj = DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')
                                ->where('order_id',$row->OrderID)->where('tran_id',$row->TranID)
                                ->update($dataReceipt);
                    }catch(\Exception $e){
                        Log::info($e->getMessage());
                    }
                    
                }else{
                    try {
                        $pymtObj = DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')
                            ->insert($dataReceipt);
                    }catch(\Exception $e){
                        Log::info($e->getMessage());
                    }
                    
                }
            }
            Log::info('Done save with total '.count($data));

        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            MigrateUtils::logDump($ex->getMessage());
        }
    }

    protected static function checkOrderDetail($orderId,$amount) {
        try {

            $url = self::getRazerUrl(). "/RMS/query/q_by_oid.php";


            $oId = $orderId; // 32 chars Merchant order ID, which might be duplicated.
            $domain = self::getMerchantEp(); //Merchant ID in PG system.
            //$urlResponse = 'https://www.eperolehan.gov.my/NGeP-portlet/molpayCallBack';

            //$skey =md5( oID & domain & verify_key & amount )
            $verifyKey = self::getMerchantEpVkey();
            $skey = md5( $oId . $domain . $verifyKey . $amount ); // 32 chars hexadecimal string This is the data integrity protection hash string.
            $param = "?amount=$amount&oID=$oId&domain=$domain&skey=$skey";

            $urlDetail = $url.$param;
            MigrateUtils::logDump('UrL Detail:'.$urlDetail);
            $response = Guzzle::get($urlDetail);
            $data = utf8_decode($response->getBody()->getContents());
            //dump($data );
            $data=str_replace("\n",",",$data);
            $data=explode(',',$data);
            $collect = collect();
            foreach($data as $v){
                $arr=explode(':',$v); 
                $collect->put($arr[0],$arr[1]);
            }
            MigrateUtils::logDump($collect);
            

            $vrfKey=md5( $amount . $verifyKey . $domain . $oId . '00' );
            MigrateUtils::logDump('vrfKey : '.$vrfKey);

        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            MigrateUtils::logDump($ex->getMessage());
        }
    }


    protected static function getApiQueryDetail($transactionId,$amountRm) {
        try {

            $urlRequest = self::getRazerUrl(). "/RMS/API/gate-query/index.php";
            $urlResponse = 'https://epss.eperolehan.gov.my/payment/response-callback';
            $type = 1;  // 1 = result via POST method 
            $amount = $amountRm;
            $txId = $transactionId; // Unique transaction ID for tracking purpose
            $domain = self::getMerchantEp(); //Merchant ID in PG system.

            $verifyKey = self::getMerchantEpVkey();
            $skey = md5( $txId . $domain . $verifyKey . $amount ); // 32 chars hexadecimal string This is the data integrity protection hash string.
            $param = "?amount=$amount&txID=$txId&domain=$domain&skey=$skey";

            $urlDetail = $urlRequest.$param;
            MigrateUtils::logDump('UrL Detail: '.$urlDetail);

            
            $response = Guzzle::post($urlDetail);
            $data = utf8_decode($response->getBody()->getContents());
            dd($data );
            $data=str_replace("\n",",",$data);
            $data=explode(',',$data);
            $collect = collect();
            foreach($data as $v){
                $arr=explode('=',$v); 
                $collect->put($arr[0],$arr[1]);
            }
            MigrateUtils::logDump($collect);
            $respStatCode = $collect->get('StatCode');

            $vrfKey=md5( $amount . $verifyKey . $domain . $txId . $respStatCode );
            MigrateUtils::logDump('vrfKey : '.$vrfKey);

        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            MigrateUtils::logDump($ex->getMessage());
        }
    }

    /**
     * Tested
     */
    protected static function getApiReturnIPN() {
        try {

            $urlRequest = "https://www.onlinepayment.com.my/MOLPay/API/chkstat/returnipn.php";
      
            $vkey ="f590da2324e64b86bc713cb228fd7c5d"; //eperolehanlms
            $_POST[treq] = 1; // Additional parameter for IPN
            // Value always 1. Do not change this value.
            $tranID = $_POST['tranID'];
            $orderid = $_POST['orderid'];
            $status = $_POST['status'];
            $domain = $_POST['domain'];
            $amount = $_POST['amount'];
            $currency = $_POST['currency'];
            $appcode = $_POST['appcode'];
            $paydate = $_POST['paydate'];
            $skey = $_POST['skey'];
            /***********************************************************
            * Snippet code in purple color is the enhancement required
            * by merchant to add into their return script in order to
            * implement backend acknowledge method for IPN
            ************************************************************/
            while ( list($k,$v) = each($_POST) ) {
            $postData[]= $k."=".$v;
            }
            $postdata = implode("&",$postData);
            $url = "https://www.onlinepayment.com.my/MOLPay/API/chkstat/returnipn.php";
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_POST , 1 );
            curl_setopt($ch, CURLOPT_POSTFIELDS , $postdata );
            curl_setopt($ch, CURLOPT_URL , $url );
            curl_setopt($ch, CURLOPT_HEADER , 1 );
            curl_setopt($ch, CURLINFO_HEADER_OUT , TRUE );
            curl_setopt($ch, CURLOPT_RETURNTRANSFER , 1 );
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER , FALSE );
            curl_setopt($ch, CURLOPT_SSLVERSION , 6 ); // use only TLSv1.2
            $result = curl_exec( $ch );
            curl_close( $ch );
            /***********************************************************
            * To verify the data integrity sending by PG
            ************************************************************/
            $key0 = md5( $tranID.$orderid.$status.$domain.$amount.$currency );
            $key1 = md5( $paydate.$domain.$key0.$appcode.$vkey );
            if( $skey != $key1 ) $status= -1; // Invalid transaction
            if ( $status == "00" ) {
            if ( check_cart_amt($orderid, $amount) ) {
            // write your script here .....
            }
            } else {
            // failure action
            }
            $nbcb = 1;  // Default for Callback
            $amount = $collect->get('amount') ; // '50.00';
            $orderid = $collect->get('orderid') ; // '16514';
            $tranID = $collect->get('tranID') ; // '****************';
            $domain = $collect->get('domain') ; // 'cdc4_Dev';
            $status = $collect->get('status') ; // '00';
            $appcode = $collect->get('appcode') ; // '123456';
            $error_code = $collect->get('error_code') ; // '';
            $error_desc = $collect->get('error_desc = ') ; // '';
            $currency = $collect->get('currency') ; // 'MYR';
            $channel = $collect->get('channel') ; // 'fpx'; //channel credit , fpx
            $paydate = $collect->get('paydate') ; // '2022-03-16 15:59:09' ; //Date/Time( YYYY-MM-DD HH:mm:ss)
            $extraP = $collect->get('extraP') ; // 

            $amount = $amountRm;
            $txId = $transactionId; // Unique transaction ID for tracking purpose
            $domain = self::getMerchantEp(); //Merchant ID in PG system.

            $verifyKey = self::getMerchantEpVkey();
            $skey = md5( $txId . $domain . $verifyKey . $amount ); // 32 chars hexadecimal string This is the data integrity protection hash string.
            $param = "?amount=$amount&txID=$txId&domain=$domain&skey=$skey&url=$urlResponse";

            $urlDetail = $urlRequest.$param;
            MigrateUtils::logDump('UrL Detail: '.$urlDetail);

            
            $response = Guzzle::post($urlDetail);
            $data = utf8_decode($response->getBody()->getContents());
            dump($data );
            $data=str_replace("\n",",",$data);
            $data=explode(',',$data);
            $collect = collect();
            foreach($data as $v){
                $arr=explode('=',$v); 
                $collect->put($arr[0],$arr[1]);
            }
            MigrateUtils::logDump($collect);
            $respStatCode = $collect->get('StatCode');

            $vrfKey=md5( $amount . $verifyKey . $domain . $txId . $respStatCode );
            MigrateUtils::logDump('vrfKey : '.$vrfKey);

        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            MigrateUtils::logDump($ex->getMessage());
        }
    }


     /**
     * Parse to Carbon or null
     * @param type $day
     * @return string
     */
    protected static function parseStrDateToCarbon($strDate) {
        try {
            if ($strDate != null && $strDate != '0000-00-00 00:00:00') {
                return Carbon::parse($strDate);
            }
        } catch (Exception $ex) {
            return null;
        }
        return null;
    }

}
