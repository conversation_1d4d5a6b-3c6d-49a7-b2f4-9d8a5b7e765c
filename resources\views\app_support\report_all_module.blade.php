@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> Report Searching</strong></h1> 
    </div> 
    <form class="form-horizontal form-bordered patch-form" id="patch-form" action="{{url('/app-support/report/all/module')}}" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading">
            <label class="col-md-1 text-left" for="module">Module<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <select id="module_name" name="module_name" required class="form-control" style="width: 700px;">
                    <option value="">Please Select</option>
                    @foreach($moduleList as $list)
                        <option value="{{ $list->parameter_code }}">{{ $list->code_name }}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-md-1 text-left" for="report_name">Report Name<span class="text-danger">*</span></label>
            <div class="col-md-6">
                <select id="report_name" name="report_name" required class="form-control" style="width: 700px;">
                    <option value="">Please Select</option>
                </select>
            </div>
        </div>
        <div class='text-center' style="background: #ffffff; padding: 5px;">
            <button type="button" id="searching_info" name="searching_info"
                class="btn btn-sm btn-info text-center">
                <div class="h5 mb-0" style="font-weight: 800">Search</div>
            </button>
        </div>
    </form>
</div>

<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><strong> Report List </strong></h1> 
    </div>
    <div class="table-responsive">
        <table id="reporting_list_datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Module</th>
                    <th class="text-center">File Name</th>
                    <th class="text-center">Doc Code</th>
                    <th class="text-center">Report Link</th>
                </tr>
            </thead>
            <tbody>
                <!-- Rows will be added dynamically by AJAX -->
            </tbody>
        </table>
    </div>
</div>



@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    App.datatables();
    var APP_URL = {!! json_encode(url('/')) !!}

    $(document).ready(function () {
    $('#module_name').change(function () {
        const moduleCode = $(this).val();
        console.log(moduleCode);
        $('#report_name').empty().append('<option value="">Please Select</option>');

        if (moduleCode) {
            $.ajax({
                url: `/app-support/report/all/module/${moduleCode}`, 
                type: 'GET',
                success: function (data) {
                    $.each(data, function (index, report) {
                        $('#report_name').append(
                            `<option value="${report.report_code}">${report.report_title}</option>`
                        );
                    });
                },
                error: function (error) {
                    console.error('Error fetching reports:', error);
                    alert('Failed to fetch reports. Please try again.');
                }
            });
        }
    });
});

$(document).ready(function () {
    $('#searching_info').click(function () {
        const moduleName = $('#module_name').val(); 
        const reportName = $('#report_name').val(); 

        if (!moduleName || !reportName) {
            alert('Please select both Module and Report!');
            return;
        }

        $.ajax({
            url: "{{ route('search.reports') }}",
            type: 'POST',
            data: {
                module_name: moduleName,
                report_name: reportName,
                _token: "{{ csrf_token() }}"
            },
            success: function (response) {
                console.log('Search results:', response);
                if (response.error) {
                    alert(response.error);
                    return;
                }
                $('#reporting_list_datatable tbody').empty();
                if (Array.isArray(response) && response.length > 0) {
                    $.each(response, function(index, item) {
                        const reportLink = `http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd&destype=cache&desformat=pdf&report='${item.report_filename}'&paramform=yes`;

                        const row = `<tr>
                                        <td class="text-center">${item.code_name}</td>
                                        <td class="text-center">${item.report_title}</td>
                                        <td class="text-center">${item.report_code}</td>
                                        <td class="text-center"><a href="${reportLink}" target="_blank">View Report</a></td>
                                    </tr>`;
                        $('#reporting_list_datatable tbody').append(row);

                        $('#reporting_list_datatable').DataTable({
                                    ordering: false,
                                    lengthMenu: [
                                        [10, 20, 30, -1],
                                        [10, 20, 30, 'All']
                                    ]
                                });
                    });
                } else {
                    $('#reporting_list_datatable tbody').append('<tr><td colspan="4" class="text-center">No reports found</td></tr>');
                }
            },
            error: function (xhr, status, error) {
                console.error('Error during search:', error);
                alert('An error occurred. Please try again.');
                console.log('Response:', xhr.responseText);
            }
        });
    });
});
</script>   
    

@endsection



