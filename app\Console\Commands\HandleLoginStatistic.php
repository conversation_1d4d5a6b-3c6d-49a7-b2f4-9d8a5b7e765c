<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use DB;
use App\EpSupportActionLog;
use App\Services\Traits\ePLoginStatisticService;

class HandleLoginStatistic extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    use ePLoginStatisticService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep_login_statistic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Show statistic of PTJ & Supplier Login in daily & monthly';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }
    
    // to get data stored in ep_login_statistic based on date & time_login = null
    public function getePLoginDataDaily(){
        MigrateUtils::logDump(__METHOD__.' startingzz .. ');
        $dtStartTimeLog = Carbon::now();                    
        $dtStartTime = Carbon::now();
        $dtEndTime = Carbon::now();
        $dateStartD = $dtStartTime->subDay()->format('Y-m-d').' 00:00:00';
        $dateEndD = $dtEndTime->subDay()->format('Y-m-d').' 23:59:59';
       
        MigrateUtils::logDump(__METHOD__.' Date Start:- '.$dateStartD.', Date End:- '.$dateEndD);
        try {
            $list = $this->getLoginDailyStatistic($dateStartD, $dateEndD);
            $listData = collect($list);            
          
            foreach ($listData as $objFile){       
                    $createdDate = Carbon::now();
                    $byScheduler = 'by SchedulerSystem';
                    DB::connection('mysql_ep_support')
                    ->insert('insert into ep_login_statistic 
                        (user_type,user_name,user_group,date_login,login_total,created_at,created_by) 
                        values (?, ?, ?, ?, ?, ?, ?)', 
                        [   
                            $objFile->user_type, 
                            $objFile->user_name,
                            $objFile->user_group,
                            $objFile->date_login,
                            $objFile->login_total,
                            $createdDate,
                            $byScheduler
                            ]);
           
            }
            
            MigrateUtils::logDump(__METHOD__.' Total Login: '.count($listData));

            $logsdata = __METHOD__ .' Query Date Startz : '.$dateStartD.' , Query Date End : '.$dateEndD.' , '
                    . 'COMPLETED --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            MigrateUtils::logDump($logsdata);

       } 
        catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
        }
    }

    // to get data stored in ep_login_statistic based on date & time_login = null
    public function getePLoginDataMonthly(){
        MigrateUtils::logDump(__METHOD__.' starting .. ');
        $dtStartTimeLog = Carbon::now();   
        

        $firstDayOfLastMonth = Carbon::now()->subMonthNoOverflow()->startOfMonth();
        $endDayOfLastMonth = Carbon::now()->subMonthNoOverflow()->endOfMonth();

        $dateStartD = $firstDayOfLastMonth->format('Y-m-d').' 00:00:00';
        $dateEndD = $endDayOfLastMonth->format('Y-m-d').' 23:59:59';
       
        MigrateUtils::logDump(__METHOD__.' Date Start:- '.$dateStartD.', Date End:- '.$dateEndD);
        try {
            $list = $this->getLoginMonthlyStatistic($dateStartD, $dateEndD);
            $listData = collect($list);            
          
            foreach ($listData as $objFile){       
                    $createdDate = Carbon::now();
                    $byScheduler = 'by SchedulerSystem';
                    DB::connection('mysql_ep_support')
                    ->insert('insert into ep_login_statistic 
                        (user_type,user_name,user_group,year,month,login_total,created_at,created_by) 
                        values (?, ?, ?, ?, ?, ?, ?, ?)', 
                        [   
                            $objFile->user_type, 
                            $objFile->user_name,
                            $objFile->user_group,
                            $objFile->year_login,
                            $objFile->month_login,
                            $objFile->login_total,
                            $createdDate,
                            $byScheduler
                            ]);
           
            }
            
            MigrateUtils::logDump(__METHOD__.' Total Login: '.count($listData));

            $logsdata = __METHOD__ .' Query Date Startz : '.$dateStartD.' , Query Date End : '.$dateEndD.' , '
                    . 'COMPLETED --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            MigrateUtils::logDump($logsdata);

       } 
        catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail($exc->getTraceAsString());
        }
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__.' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        
        /** Start monthly checking statistic */
        $yearLogin = Carbon::now()->subMonthNoOverflow()->startOfMonth()->year;
        $monthLogin = Carbon::now()->subMonthNoOverflow()->startOfMonth()->month;
        MigrateUtils::logDump(__METHOD__.' Check statistic monthly... Year:'.$yearLogin.' Month : '.$monthLogin);
        $checkMonthlyYesterday = DB::connection('mysql_ep_support')
            ->table('ep_login_statistic')
            ->where('year',$yearLogin )
            ->where('month',$monthLogin)
            ->whereNull('time_login')
            ->whereNull('date_login')->count();
        if($checkMonthlyYesterday == 0){
            MigrateUtils::logDump(__METHOD__.' Check statistic daily still not capture.. Year:'.$yearLogin.' Month : '.$monthLogin);
            $this->getePLoginDataMonthly();
        }


        /** Start daily checking statistic */
        $dayLogin = Carbon::yesterday()->format('Y-m-d');
        MigrateUtils::logDump(__METHOD__.' Check statistic daily... Date: '.$dayLogin);
        $checkDailyYesterday = DB::connection('mysql_ep_support')
            ->table('ep_login_statistic')
            ->whereDate('date_login',$dayLogin)->whereNull('time_login')->count();
        if($checkDailyYesterday == 0){
            MigrateUtils::logDump(__METHOD__.' Check statistic daily still not capture.. :'.$dayLogin);
            $this->getePLoginDataDaily();
        }
        // once dh pkol 12;00 AM  METHOD NI JALAN SIMPAN DLM DB : $dtStartTimeLog->format('H');
        // $hourNow = $dtStartTimeLog->hour;
        // if($hourNow == 0){ 
        //    $this->getePLoginDataDaily();
        //}
        

        
        $dtStartTime = Carbon::now();
        $dateEnd = $dtStartTime->format('Y-m-d H').':00:00';  // getting  hour now
        
        // Getting time hour minus 1 hour.. 
        $dateCheck = $dtStartTime->subHour(1);
        $dateLogin = $dateCheck->format('Y-m-d');
        $timeLogin = $dateCheck->format('H').':00:00';
        $dateStart = $dateCheck->format('Y-m-d H').':00:00';
        
        
        MigrateUtils::logDump(__METHOD__.' Check statistic Hourly >> Date Start:- '.$dateLogin.', Date End:- '.$timeLogin);
        try {
            
            MigrateUtils::logDump(__METHOD__.' dateLogin : '.$dateLogin. ' >> Time: '.$timeLogin);

            $checkHourly = DB::connection('mysql_ep_support')
            ->table('ep_login_statistic')
            ->whereDate('date_login',$dateLogin)
            ->where('time_login',$timeLogin)
            ->count();
            if($checkHourly == 0){
                MigrateUtils::logDump(__METHOD__.' Check statistic hourly still not capture.. ');
                $list = $this->getLoginStatistic($dateStart, $dateEnd);
                $listData = collect($list);            
            
                foreach ($listData as $objFile){       
                        $createdDate = Carbon::now();
                        $byScheduler = 'by SchedulerSystem';
                        DB::connection('mysql_ep_support')
                        ->insert('insert into ep_login_statistic 
                            (user_type,user_name,user_group,date_login,time_login,login_total,created_at,created_by) 
                            values (?, ?, ?, ?, ?, ?, ?, ?)', 
                            [   
                                $objFile->user_type, 
                                $objFile->user_name,
                                $objFile->user_group,
                                $objFile->date_login,
                                $objFile->time_login.':00:00',
                                $objFile->login_total,
                                $createdDate,
                                $byScheduler
                                ]);
            
                }
                
                MigrateUtils::logDump(__METHOD__.' Total Login: '.count($listData));

                $logsdata = __METHOD__.' Query Date Startz : '.$dateStart.' , Query Date End : '.$dateEnd.' , '
                        . 'COMPLETED --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
                Log::info($logsdata);
                MigrateUtils::logDump($logsdata);
            }
            

        } 
        catch (\Exception $ex) {
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $ex->getMessage());
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $ex->getTraceAsString());
        }
        
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleLoginStatistic'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
