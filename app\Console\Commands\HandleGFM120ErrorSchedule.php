<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use App\EpSupportActionLog;

class HandleGFM120ErrorSchedule extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleGFM120Error';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Any error response Bank Pembekal tidak wujud di 1GFMAS, will auto trigger sent APIVE';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d H').':00:00';
        $dateStart = $dtStartTime->subHour(1)->format('Y-m-d H').':00:00';
        
        //$dateStart = '2019-05-13 00:00:00';
        //$dateEnd = '2019-05-13 18:15:00';

        dump('Date Start: '.$dateStart);
        dump('Date End: '.$dateEnd);
        Log::info('Start in '.self::class .'Date Start: '.$dateStart.', Date End: '.$dateEnd);
        try {
            $list = $this->getListWsErrBankPembekalTidakWujudInGFM120ByDateRange($dateStart, $dateEnd);
            $listData = collect($list);
            

            /* get all list request item id in PR/CR  and trigger MMINF */
            $listRequestEpNoIdAll = $this->getAllRequestEpNoByDocNo($listData);
            
            
            dump('Total DocNo Error Bank Pembekal Tidak Wujud: '.count($listRequestEpNoIdAll));
            Log::info('Total DocNo Error Bank Pembekal Tidak Wujud: '.count($listRequestEpNoIdAll));
            
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
            dump($logsdata);

        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }
    
    
    
    protected function getAllRequestEpNoByDocNo($listData) {
        $listResult = $listData->pluck('remarks_2')->diff(["NA"]);
            
        Log::info('Total Doc PR (Got Error Bank Pembekal Tidak Wujud) : '.count($listResult));
        dump('Total Doc PR (Got Error Bank Pembekal Tidak Wujud) : '.count($listResult));
        
        $listSupplierEpNo = collect([]);
        foreach ($listResult as $docNo){
            $typeDoc   = substr($docNo, 0, 2);
            if($typeDoc == 'PO' || $typeDoc == 'CO'){
                $docObj = $this->getDocNoPRPOorCRCO($docNo);
                if($docObj){
                   $docNo =  $docObj->fr_doc_no;
                }
                $listResultSuppInfo = $this->getInfoSupplierAndContractByErrorGFM120($docNo);
                if(count($listResultSuppInfo) > 0){
                    $objRes = $listResultSuppInfo[0];
                    $epNo = $objRes->ep_no;
                    dump('Doc No: '.$docNo. '  eP No: '.$epNo);
                    $this->apiveTrigger($epNo);
                    $listSupplierEpNo->push($epNo);
                }
                
            }
        }
        
        return $listSupplierEpNo;
    }
    
    
    protected function apiveTrigger($ePNo) {

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePNo</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerAPIVE/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        $actionLog =  EpSupportActionLog::createActionLog('TriggerAPIVE','Web Service',$commands[0],$ePNo,'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){
            $line.PHP_EOL;
        });
        sleep(10);
        
        $resultSupplier = $this->getApiveTriggerInfo($ePNo);
        
        $checkChangeDate = Carbon::parse($resultSupplier[0]->changed_date);
        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
            EpSupportActionLog::updateActionLog($actionLog, 'Completed','SchedulerAdmin');
            
        } else {
            $status = 'Failed';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed','SchedulerAdmin');
        }
        
        dump($ePNo. ': '.$status. ' Date Change Supplier: '.$checkChangeDate);
        Log::info('         '.$ePNo. ': '.$status. ' Date Change Supplier: '.$checkChangeDate);

    }
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM120ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
