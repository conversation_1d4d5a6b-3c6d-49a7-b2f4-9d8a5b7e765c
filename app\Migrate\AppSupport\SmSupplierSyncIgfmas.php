<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmSupplierSyncIgfmas {

    use SupplierService;

    /** Get info supplier */
    public static function syncSupplierWithIgfmasInfo(){
        MigrateUtils::logDump(__METHOD__ .' entering..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmSupplierSyncIgfmas();
        $batch5 = 'SUPP eP BATCH 5 - ******** unlocked';
        $listSupp = $thisCls->getListSupplierIgfmas($batch5 );
        foreach ($listSupp as $row) {
            $sapVendorCode = trim($row->sap_vendor_code);
            try {
                
                $objSuppVendorCode = $thisCls->getDetailSapVendorCode($sapVendorCode);
                
                if($objSuppVendorCode){
                    $objSuppEp = $thisCls->getDetailSupplier($objSuppVendorCode->ep_no);
                    $dataUpdate = [];
                    $dataUpdate['response'] = 'Done checked in eP';
                    $dataUpdate['sap_vendor_code'] = $sapVendorCode;  //reupdate to make sure no space 
                    $dataUpdate['ep_company_name'] = $objSuppEp->company_name;
                    $dataUpdate['ep_registration_no'] = $objSuppEp->reg_no;
                    $dataUpdate['ep_record_status'] = $objSuppEp->record_status;
                    if(strlen($objSuppVendorCode->branch_code) > 1){
                        // THIS IS BRANCH 
                        $dataUpdate['ep_is_hq'] = 'NO';
                        $dataUpdate['ep_branch_code'] = $objSuppVendorCode->branch_code;
                        $branchObj = $thisCls->getDetailBranchSupplier($objSuppEp->latest_appl_id,$objSuppVendorCode->branch_code);
                        if($branchObj){
                            if($branchObj->record_status == 1){
                                $dataUpdate['remarks'] = 'active as branch';
                            }else{
                                $dataUpdate['remarks'] = 'deleted';
                            }
                        }else{
                            $dataUpdate['remarks'] = 'deleted';
                        }
                        
                        $listBank =$thisCls->getListBankBranchSupplier($objSuppEp->latest_appl_id,$objSuppVendorCode->branch_code);
                        foreach($listBank as $obj){
                            $thisCls->updateInsertSupplierBank($sapVendorCode,$obj,$objSuppVendorCode->branch_code);
                        }

                        $dataUpdate['last_updated'] = Carbon::now();
                        $dataUpdate['ep_total_bank'] = count($listBank);
                        $thisCls->updateSupplierIgfmas($row->id,$dataUpdate);

                    }else{
                        // HQ 
                        $dataUpdate['ep_is_hq'] = 'YES';

                        if($objSuppEp->record_status == 1 || $objSuppEp->record_status == 5){
                            $dataUpdate['remarks'] = 'active';
                        }else{
                            $dataUpdate['remarks'] = 'deleted';
                        }

                        $listBank =$thisCls->getListBankHqSupplier($objSuppEp->latest_appl_id);
                        foreach($listBank as $obj){
                            $thisCls->updateInsertSupplierBank($sapVendorCode,$obj,null);
                        }

                        $dataUpdate['last_updated'] = Carbon::now();
                        $dataUpdate['ep_total_bank'] = count($listBank);
                        $thisCls->updateSupplierIgfmas($row->id,$dataUpdate);
                    }
                }else{
                    /*** SAP VENDOR CODE NOT FOUND ***/

                    $dataUpdate = [
                        'sap_vendor_code' => $sapVendorCode,
                        'response' => 'Done checked in eP',
                        'remarks' => 'no longer existed in ep',
                        'last_updated' => Carbon::now(),
                    ];
                    $thisCls->updateSupplierIgfmas($row->id,$dataUpdate);
                }
            } catch (\Exception $e) {
                MigrateUtils::logDump(__METHOD__."ERROR!!!!!!!   sapVendorCode : $sapVendorCode #### ".$e->getMessage());
            }
            

        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }


    public static function runReportSupplierIfgmas(){
        MigrateUtils::logDump(__METHOD__ .' entering..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmSupplierSyncIgfmas();

        /*
        $batch1 = 'SUPP eP BATCH 1 - ******** unlocked';
        $listSupp = $thisCls->getListSupplierIgfmas($batch1);
        $thisCls->executeProcessCompareBank($listSupp,$batch1);

        $batch2 = 'SUPP eP BATCH 2 - ******** unlocked';
        $listSuppBatch2 = $thisCls->getListSupplierIgfmas($batch2);
        $thisCls->executeProcessCompareBank($listSuppBatch2,$batch2);

        $batch3 = 'SUPP eP BATCH 3 - ******** unlocked';
        $listSuppBatch3 = $thisCls->getListSupplierIgfmas($batch3);
        $thisCls->executeProcessCompareBank($listSuppBatch3,$batch3);

        $batch4 = 'SUPP eP BATCH 4 - ******** unlocked';
        $listSuppBatch4 = $thisCls->getListSupplierIgfmas($batch4);
        $thisCls->executeProcessCompareBank($listSuppBatch4,$batch4);
        **/
        $batch5 = 'SUPP eP BATCH 5 - ******** unlocked';
        $listSuppBatch5 = $thisCls->getListSupplierIgfmas($batch5);
        $thisCls->executeProcessCompareBank($listSuppBatch5,$batch5);

        MigrateUtils::logDump(__METHOD__.' --- Taken Time : '.MigrateUtils::getTakenTime($dtStartTime)['TakenTime']);
    }
    protected function executeProcessCompareBank($listSupp,$batch ){
        $thisCls = new SmSupplierSyncIgfmas();

        /** to reset delete first all record by batch file */
        DB::connection('mysql_ep_support')
                ->table('ep_igfmas.ep_supplier_igfmas_sync')
                ->where('batch_file',$batch)
                ->delete();

        MigrateUtils::logDump(__METHOD__."> $batch entering.. : total " . count($listSupp));

        foreach ($listSupp as $row) {
            try {
                //Check bank_no record.
                $sapVendorCode = trim($row->sap_vendor_code);
                $listSuppBank = collect($thisCls->getListDetailBank($sapVendorCode));
                $listTempBankNo = array();

                //Check bank_name same with bank_no from igfmas_server
                $bankPurposeBoth = $listSuppBank->where('bank_acc_no',$row->account_no)->where('bank_swiftcode',$row->swiftcode)->where('acc_purpose','B')->first();
                if($bankPurposeBoth && strlen($bankPurposeBoth->bank_acc_no) > 1){
                    MigrateUtils::logDump(__METHOD__." found as both purpose");
                    $row->bank_remark_check = 'Bank Nombor Sama';
                    $row->ep_bank_name = $bankPurposeBoth->bank_name;
                    $row->ep_bank_no = $bankPurposeBoth->bank_acc_no;
                    $row->ep_bank_swiftcode = $bankPurposeBoth->bank_swiftcode;
                    $row->ep_bank_purpose = $bankPurposeBoth->acc_purpose;
                    $row->ep_bank_supplier_status = $bankPurposeBoth->bank_supplier_status;
                    $thisCls->insertSupplierIgfmasResult($row);
                    $thisCls->updateSupplierBankCheck($sapVendorCode,$row->ep_bank_no,$row->ep_bank_purpose,$row->ep_bank_swiftcode);
                }else {
                    $bankPurposeP = $listSuppBank->where('bank_acc_no',$row->account_no)->where('bank_swiftcode',$row->swiftcode)->where('acc_purpose','P')->first();
                    if($bankPurposeP && strlen($bankPurposeP->bank_acc_no) > 1){
                        MigrateUtils::logDump(__METHOD__." found as both P");
                        $row->bank_remark_check = 'Bank Nombor Sama';
                        $row->ep_bank_name = $bankPurposeP->bank_name;
                        $row->ep_bank_no = $bankPurposeP->bank_acc_no;
                        $row->ep_bank_swiftcode = $bankPurposeP->bank_swiftcode;
                        $row->ep_bank_purpose = $bankPurposeP->acc_purpose;
                        $row->ep_bank_supplier_status = $bankPurposeP->bank_supplier_status;
                        $thisCls->insertSupplierIgfmasResult($row);
                        $thisCls->updateSupplierBankCheck($sapVendorCode,$row->ep_bank_no,$row->ep_bank_purpose,$row->ep_bank_swiftcode);
                    }else{
                        MigrateUtils::logDump(__METHOD__." bank is not found in eP");
                        $row->bank_remark_check = 'Tidak dijumpai';
                        $row->ep_bank_name = null;
                        $row->ep_bank_no = null;
                        $row->ep_bank_swiftcode = null;
                        $row->ep_bank_purpose = null;
                        $row->ep_bank_supplier_status = null;
                        $thisCls->insertSupplierIgfmasResult($row);
                    }
                }


            } catch (\Exception $e) {
                MigrateUtils::logDump(__METHOD__."ERROR!!!!!!! #### ".$e->getMessage());

            }
        }

    }

    public static function executeBankExistInEpOnly(){
        MigrateUtils::logDump(__METHOD__ .' entering..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmSupplierSyncIgfmas();


        $listBanks = DB::connection('mysql_ep_support')
                ->table('ep_igfmas.ep_supplier_bank_info')
                ->whereNull('is_check')
                ->whereIn('acc_purpose',['B','P'])
                ->get();
        MigrateUtils::logDump(__METHOD__ .' total count '.count($listBanks));
        foreach($listBanks as $row){
            $sapVendorCode = trim($row->sap_vendor_code);
            $checkExist  = DB::connection('mysql_ep_support')
            ->table('ep_igfmas.ep_supplier_igfmas_sync')
            ->where('sap_vendor_code', $sapVendorCode)
            ->where('ep_bank_no',$row->bank_acc_no)
            ->where('ep_bank_swiftcode',$row->bank_swiftcode)
            ->count();
            if($checkExist == 0 ){
                //INSERT AS DETECT NEW BANK IN EP
                $obj = DB::connection('mysql_ep_support')
                    ->table('ep_igfmas.ep_supplier_igfmas_sync')
                    ->where('sap_vendor_code', $sapVendorCode)
                    ->first();
                $obj->batch_file = 'Batch Bank Wujud di Ep Sahaja';
                //$obj->bil = null;
                //$obj->company_name; 
                $obj->company_name_check = null; 
                //$obj->registration_no; 
                $obj->registration_no_check = null; 
                //$obj->id_type; 
                $obj->id_type_check = null; 
                $obj->account_no = null; 
                $obj->account_no_check = null; 
                $obj->bank_name = null; 
                $obj->swiftcode = null; 
                $obj->swiftcode_check = null; 
                $obj->record_status = null; 
                $obj->record_status_check = null; 
                //$obj->sap_vendor_code; 
                //$obj->account_group; 
                //$obj->source_data; 
                //$obj->response; 
                //$obj->remarks; 
                //$obj->ep_company_name; 
                //$obj->ep_registration_no; 
                //$obj->ep_record_status; 
                //$obj->ep_is_hq; 
                //$obj->ep_branch_code; 
                $obj->ep_bank_name = $row->bank_name;
                $obj->ep_bank_no = $row->bank_acc_no; 
                $obj->ep_bank_swiftcode = $row->bank_swiftcode; 
                $obj->ep_bank_purpose = $row->acc_purpose; 
                $obj->ep_bank_supplier_status = $row->bank_supplier_status; 
                $obj->bank_remark_check = 'Bank Pembekal Wujud Di eP Sahaja';

                $thisCls->insertSupplierIgfmasResult($obj);
                $thisCls->updateSupplierBankCheck($sapVendorCode,$row->bank_acc_no,$row->acc_purpose,$row->bank_swiftcode);
                
            }else{
                MigrateUtils::logDump(__METHOD__ ." already exist $sapVendorCode > $row->bank_acc_no > $row->bank_swiftcode");
                $thisCls->updateSupplierBankCheck($sapVendorCode,$row->bank_acc_no,$row->acc_purpose,$row->bank_swiftcode);
            }
        }        
        MigrateUtils::logDump(__METHOD__.' --- Taken Time : '.MigrateUtils::getTakenTime($dtStartTime)['TakenTime']);
    }

    protected function getListSupplierIgfmas($batchFile){
        $listSupp = DB::connection('mysql_ep_support')->table('ep_igfmas.ep_supplier_validate_igfmas')
            ->where('batch_file',$batchFile)
            //->where('id','53045')
            //->take('10')
            //->whereNull('response')
            ->get();
        MigrateUtils::logDump(__METHOD__.' Total result : '.count($listSupp));
        return $listSupp;

    }

    /** Getting result from MySQL data checking */
    protected function getListDetailBank($sapVendorCode){
        $listSuppBank = DB::connection('mysql_ep_support')->table('ep_igfmas.ep_supplier_bank_info')
            //->where('batch_file','SUPP eP BATCH 1 - ******** unlocked')
            ->where('sap_vendor_code',$sapVendorCode)
            ->get();
        MigrateUtils::logDump(__METHOD__.' Total result : '.count($listSuppBank));
        return $listSuppBank;

    }

    
    protected function updateSupplierIgfmas($id,$data){
        DB::connection('mysql_ep_support')
        ->table('ep_igfmas.ep_supplier_validate_igfmas')
        ->where('id', $id)
        ->update($data);
    }

    protected function updateInsertSupplierBank($sapVendorCode,$obj,$branchCode){
        $dataUpdateBank = [];
        $dataUpdateBank['bank_name'] = $obj->bank_name;
        $dataUpdateBank['bank_swiftcode'] = $obj->bank_code;
        $dataUpdateBank['acc_purpose'] = $obj->account_purpose;
        $dataUpdateBank['is_for_hq'] = $branchCode != null ? 'YES' : 'NO';
        $dataUpdateBank['branch_code'] = $branchCode;
        $dataUpdateBank['bank_supplier_status'] = $obj->bank_supplier_status;
        $dataUpdateBank['last_updated'] = Carbon::now();
        

        $bankAccNo = trim($obj->account_no);

        $check = DB::connection('mysql_ep_support')
        ->table('ep_igfmas.ep_supplier_bank_info')
        ->whereRaw("sap_vendor_code='$sapVendorCode'")
        ->whereRaw("bank_acc_no='$bankAccNo'")
        ->whereRaw("acc_purpose='".$dataUpdateBank['acc_purpose']."'")
        ->whereRaw("is_for_hq='".$dataUpdateBank['is_for_hq']."'")
        ->count();

        if($check > 0){
            $dataUpdateBank['is_check_updated'] = Carbon::now();
            DB::connection('mysql_ep_support')
                ->table('ep_igfmas.ep_supplier_bank_info')
                ->whereRaw("sap_vendor_code='$sapVendorCode'")
                ->whereRaw("bank_acc_no='$bankAccNo'")
                ->whereRaw("acc_purpose='".$dataUpdateBank['acc_purpose']."'")
                ->whereRaw("is_for_hq='".$dataUpdateBank['is_for_hq']."'")
                ->update($dataUpdateBank);
        }else{
            $dataUpdateBank['sap_vendor_code'] = $sapVendorCode;
            $dataUpdateBank['bank_acc_no'] = $bankAccNo;
            DB::connection('mysql_ep_support')
                ->table('ep_igfmas.ep_supplier_bank_info')
                ->insert($dataUpdateBank);
        }

    }

    protected function updateSupplierBankCheck($sapVendorCode,$bankAccNo,$accPurpose,$bankSwiftcode){
        DB::connection('mysql_ep_support')
            ->table('ep_igfmas.ep_supplier_bank_info')
            ->whereRaw("sap_vendor_code='$sapVendorCode'")
            ->where('bank_acc_no',$bankAccNo)
            ->whereRaw("acc_purpose='$accPurpose'")
            ->whereRaw("bank_swiftcode='$bankSwiftcode'")
            ->update([
                'is_check' => 1,
                'is_check_updated' => Carbon::now()
            ]);

    }

    protected function insertSupplierIgfmasResult($obj){
        $dataUpdate = [];
        $dataUpdate['batch_file'] =  $obj->batch_file;
        $dataUpdate['bil'] =  trim($obj->bil);
        $dataUpdate['company_name'] =  trim($obj->company_name); 
        $dataUpdate['company_name_check'] =  trim($obj->company_name_check); 
        $dataUpdate['registration_no'] =   trim($obj->registration_no); 
        $dataUpdate['registration_no_check'] =   trim($obj->registration_no_check); 
        $dataUpdate['id_type'] =   trim($obj->id_type); 
        $dataUpdate['id_type_check'] =   trim($obj->id_type_check); 
        $dataUpdate['account_no'] =   trim($obj->account_no); 
        $dataUpdate['account_no_check'] =   trim($obj->account_no_check); 
        $dataUpdate['bank_name'] =   trim($obj->bank_name); 
        $dataUpdate['swiftcode'] =   trim($obj->swiftcode); 
        $dataUpdate['swiftcode_check'] =   trim($obj->swiftcode_check); 
        $dataUpdate['record_status'] =   trim($obj->record_status); 
        $dataUpdate['record_status_check'] =   trim($obj->record_status_check); 
        $dataUpdate['sap_vendor_code'] =   trim($obj->sap_vendor_code); 
        $dataUpdate['account_group'] =   trim($obj->account_group); 
        $dataUpdate['source_data'] =   trim($obj->source_data); 
        $dataUpdate['response'] =   trim($obj->response); 
        $dataUpdate['last_updated'] = Carbon::now();
        $dataUpdate['remarks'] =   trim($obj->remarks); 
        $dataUpdate['ep_company_name'] =   $obj->ep_company_name; 
        $dataUpdate['ep_registration_no'] =   $obj->ep_registration_no; 
        $dataUpdate['ep_record_status'] =   $obj->ep_record_status; 
        $dataUpdate['ep_is_hq'] =   $obj->ep_is_hq; 
        $dataUpdate['ep_branch_code'] =   $obj->ep_branch_code; 
        $dataUpdate['ep_bank_name'] =   $obj->ep_bank_name; 
        $dataUpdate['ep_bank_no'] =   $obj->ep_bank_no; 
        $dataUpdate['ep_bank_swiftcode'] =   $obj->ep_bank_swiftcode; 
        $dataUpdate['ep_bank_purpose'] =   $obj->ep_bank_purpose; 
        $dataUpdate['ep_bank_supplier_status'] =   $obj->ep_bank_supplier_status;  
        $dataUpdate['bank_remark_check'] = $obj->bank_remark_check;

        /*
        $check = DB::connection('mysql_ep_support')
        ->table('ep_igfmas.ep_supplier_igfmas_sync')
        ->whereRaw("sap_vendor_code='".$dataUpdate['sap_vendor_code']."'")
        ->whereRaw("ep_bank_no='".$dataUpdate['ep_bank_no']."'")
        ->whereRaw("ep_bank_purpose='".$dataUpdate['ep_bank_purpose']."'")
        ->whereRaw("ep_bank_swiftcode='".$dataUpdate['ep_bank_swiftcode']."'")
        ->count();
        */
        try{
            DB::connection('mysql_ep_support')
                    ->table('ep_igfmas.ep_supplier_igfmas_sync')
                    ->insert($dataUpdate);
        }catch (\Exception $e) {
            MigrateUtils::logDump(__METHOD__."ERROR!!!!!!! #### ".$e->getMessage());
            $sapVendorCode = $dataUpdate['sap_vendor_code'];
            unset($dataUpdate['sap_vendor_code']);
            $bankNo = $dataUpdate['ep_bank_no'];
            unset($dataUpdate['ep_bank_no']);
            $bankPurpose = $dataUpdate['ep_bank_purpose'];
            unset($dataUpdate['ep_bank_purpose']);
            $swiftCode = $dataUpdate['ep_bank_swiftcode'];
            unset($dataUpdate['ep_bank_swiftcode']);
            $bil = $dataUpdate['bil'];
            unset($dataUpdate['bil']);
            $batchFile = $dataUpdate['batch_file'];
            unset($dataUpdate['batch_file']);

            DB::connection('mysql_ep_support')
            ->table('ep_igfmas.ep_supplier_igfmas_sync')
            ->whereRaw("sap_vendor_code='$sapVendorCode'")
            ->where('ep_bank_no',$bankNo )
            ->whereRaw("ep_bank_purpose='$bankPurpose'")
            ->where('ep_bank_swiftcode',$swiftCode)
            ->where('bil',$bil )
            ->where('batch_file',$batchFile )
            ->update($dataUpdate);
            MigrateUtils::logDump(__METHOD__."successfully update $sapVendorCode > $swiftCode > $bankNo > $bankPurpose" );
        }
            
        

    }

    protected function getDetailSapVendorCode($sapVendorCode){
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as A');
        $query->where('A.SAP_VENDOR_CODE', $sapVendorCode);
        $objSuppVendorCode =  $query->first();
        //MigrateUtils::logDump(__METHOD__.'SM_SAP_VENDOR_CODE');
        //MigrateUtils::logDump(__METHOD__.$objSuppVendorCode );
        return $objSuppVendorCode;
    }

    protected function getDetailSupplier($epNo){
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->where('A.EP_NO', $epNo);
        $objSuppEp = $query->first();
        //MigrateUtils::logDump(__METHOD__.'SM_SUPPLIER');
        //MigrateUtils::logDump(__METHOD__.$objSuppEp);
        return $objSuppEp;
    }

    protected function getDetailBranchSupplier($applId,$branchCode){
        $branchInfoList = DB::connection('oracle_nextgen_rpt')->select("select * from SM_SUPPLIER_BRANCH b 
                        where  b.appl_id = ? 
                        and b.branch_code = ? 
                        and b.REV_NO = (select max(x.rev_no) from  SM_SUPPLIER_BANK x where x.appl_id = b.appl_id )
                        ", array($applId,$branchCode));
        $branchSuppObj = null;
        if(count($branchInfoList) > 0){
            $branchSuppObj = $branchInfoList[0];
        }
        //MigrateUtils::logDump(__METHOD__.'SM_SUPPLIER_BRANCH');
        //MigrateUtils::logDump(__METHOD__.$branchSuppObj);
        return $branchSuppObj ;
    }

    protected function getListBankBranchSupplier($applId,$branchCode){
        $branchBankInfoList = DB::connection('oracle_nextgen_rpt')->select("SELECT
                    fo.FIN_ORG_NAME as bank_name,
                    (SELECT bank_code FROM PM_FINANCIAL_SVC fs WHERE fs.FINANCIAL_ORG_ID = fo.FINANCIAL_ORG_ID AND rownum < 2) AS bank_code ,
                    b.appl_id,
                    b.account_no,
                    b.account_purpose,
                    b.is_default_account,
                    b.IS_FOR_HQ,
                    b.BANK_BRANCH,
                    b.changed_date,
                    sb.branch_name,
                    sb.BRANCH_CODE,
                    sb.APPL_ID,
                    b.RECORD_STATUS  AS bank_supplier_status,
                    fo.record_status AS bank_status
                FROM
                    SM_SUPPLIER_BANK b
                INNER JOIN PM_FINANCIAL_ORG fo ON
                    fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
                INNER JOIN SM_SUPPLIER_BRANCH_BANK bb ON
                    bb.SUPPLIER_BANK_ID = b.SUPPLIER_BANK_ID AND bb.record_status = 1
                INNER JOIN SM_SUPPLIER_BRANCH sb ON
                    sb.SUPPLIER_BRANCH_ID = bb.SUPPLIER_BRANCH_ID AND sb.record_status = 1
                WHERE
                    b.appl_id = ?
                    AND b.REV_NO = (SELECT max(x.rev_no) FROM SM_SUPPLIER_BANK x WHERE x.appl_id = b.appl_id )
                    AND branch_code = ?
                        ", array($applId,$branchCode));
        //MigrateUtils::logDump(__METHOD__.'SM_SUPPLIER_BANK');
        //MigrateUtils::logDump(__METHOD__.$branchBankInfoList);
        return $branchBankInfoList ;
    }
    
    protected function getListBankHqSupplier($applId){
        $branchBankInfoList = DB::connection('oracle_nextgen_rpt')->select("SELECT
                    fo.FIN_ORG_NAME  as bank_name,
                    (SELECT bank_code FROM PM_FINANCIAL_SVC fs WHERE fs.FINANCIAL_ORG_ID = fo.FINANCIAL_ORG_ID AND rownum < 2) AS bank_code ,
                    b.appl_id,
                    b.account_no,
                    b.account_purpose,
                    b.is_default_account,
                    b.IS_FOR_HQ,
                    b.BANK_BRANCH,
                    b.changed_date,
                    b.RECORD_STATUS  AS bank_supplier_status,
                    fo.record_status AS bank_status
                FROM
                    SM_SUPPLIER_BANK b
                INNER JOIN PM_FINANCIAL_ORG fo ON
                    fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
                WHERE
                    b.appl_id = ?
                    AND b.REV_NO = (SELECT max(x.rev_no) FROM SM_SUPPLIER_BANK x WHERE x.appl_id = b.appl_id )
                        ", array($applId));
        //MigrateUtils::logDump(__METHOD__.'SM_SUPPLIER_BRANCH');
        //MigrateUtils::logDump(__METHOD__.$branchBankInfoList);
        return $branchBankInfoList ;
    }
    
}
