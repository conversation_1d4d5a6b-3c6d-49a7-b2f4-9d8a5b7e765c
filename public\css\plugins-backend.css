/*
 *  Document   : plugins.css
 *  Author     : Various
 *  Description: Stylesheet of various plugins in one file for consistency.
 *  Most styles are altered to match template's style. Feel free to remove the
 *  plugin styles you won't use (along with their plugins in js/plugins.js)
 *  or include them separately if you are going to use them only in few pages
 *  of your project. I've included this file along with js/plugins.js in all pages
 *  to make all the plugins available everywhere with the minimum cost (few http requests).
 *
 *  Includes (with shortcode):
 *      (#01fas) Font Awesome
 *      (#02gps) Glyphicons PRO
 *      (#03fcs) FullCalendar
 *      (#04djs) Dropzone.js
 *      (#05chs) Chosen
 *      (#06dps) Datepicker for Bootstrap
 *      (#07bcs) Bootstrap Colorpicker
 *      (#08prs) PRISM.js
 *      (#09mps) Magnific Popup CSS
 *      (#10dts) Datatables
 *      (#11eps) Easy Pie Chart
 *      (#12cas) CSS3 ANIMATION CHEAT SHEET
 *      (#13tps) Bootstrap Timepicker
 *      (#14tis) Jquery Tags Input
 *      (#15sbs) Slider for Bootstrap
 *      (#16nps) NProgress
 *      (#17s2s) Select2
 */

/*
=================================================================
(#01fas)  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome

License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
=================================================================
 */
@font-face{font-family:'FontAwesome';src:url('fonts/fontawesome/fontawesome-webfont.eot?v=4.5.0');src:url('fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.5.0') format('embedded-opentype'),url('fonts/fontawesome/fontawesome-webfont.woff2?v=4.5.0') format('woff2'),url('fonts/fontawesome/fontawesome-webfont.woff?v=4.5.0') format('woff'),url('fonts/fontawesome/fontawesome-webfont.ttf?v=4.5.0') format('truetype'),url('fonts/fontawesome/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular') format('svg');font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}

/*
=================================================================
(#02gps) Glyphicons PRO

Project:  GLYPHICONS
Author:   Jan Kovarik - www.glyphicons.com
Twitter:  @jankovarik
=================================================================
*/
@font-face{font-family:'Glyphicons Regular';src:url('fonts/glyphicons.pro/glyphicons-regular.eot');src:url('fonts/glyphicons.pro/glyphicons-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.pro/glyphicons-regular.woff') format('woff'),url('fonts/glyphicons.pro/glyphicons-regular.ttf') format('truetype'),url('fonts/glyphicons.pro/glyphicons-regular.svg#glyphiconsregular') format('svg')}@font-face{font-family:'Glyphicons Halflings Regular';src:url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.eot');src:url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.woff') format('woff'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.ttf') format('truetype'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg')}@font-face{font-family:'Glyphicons Social Regular';src:url('fonts/glyphicons.social.pro/glyphicons-social-regular.eot');src:url('fonts/glyphicons.social.pro/glyphicons-social-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.woff') format('woff'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.ttf') format('truetype'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.svg#glyphicons_socialregular') format('svg')}@font-face{font-family:'Glyphicons Filetypes Regular';src:url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.eot');src:url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.woff') format('woff'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.ttf') format('truetype'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.svg#glyphicons_filetypesregular') format('svg')}.fi,.gi,.hi,.si{display:inline-block;font-style:normal;font-weight:400;line-height:.8;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.gi{font-family:'Glyphicons Regular'}.hi{font-family:'Glyphicons Halflings Regular'}.si{font-family:'Glyphicons Social Regular'}.fi{font-family:'Glyphicons Filetypes Regular'}.gi-white{color:#fff}.gi-glass:before{content:"\E001"}.gi-leaf:before{content:"\E002"}.gi-dog:before{content:"\E003"}.gi-user:before{content:"\E004"}.gi-girl:before{content:"\E005"}.gi-car:before{content:"\E006"}.gi-user_add:before{content:"\E007"}.gi-user_remove:before{content:"\E008"}.gi-film:before{content:"\E009"}.gi-magic:before{content:"\E010"}.gi-envelope:before{content:"\2709"}.gi-camera:before{content:"\E011"}.gi-heart:before{content:"\E013"}.gi-beach_umbrella:before{content:"\E014"}.gi-train:before{content:"\E015"}.gi-print:before{content:"\E016"}.gi-bin:before{content:"\E017"}.gi-music:before{content:"\E018"}.gi-note:before{content:"\E019"}.gi-heart_empty:before{content:"\E020"}.gi-home:before{content:"\E021"}.gi-snowflake:before{content:"\2744"}.gi-fire:before{content:"\E023"}.gi-magnet:before{content:"\E024"}.gi-parents:before{content:"\E025"}.gi-binoculars:before{content:"\E026"}.gi-road:before{content:"\E027"}.gi-search:before{content:"\E028"}.gi-cars:before{content:"\E029"}.gi-notes_2:before{content:"\E030"}.gi-pencil:before{content:"\270F"}.gi-bus:before{content:"\E032"}.gi-wifi_alt:before{content:"\E033"}.gi-luggage:before{content:"\E034"}.gi-old_man:before{content:"\E035"}.gi-woman:before{content:"\E036"}.gi-file:before{content:"\E037"}.gi-coins:before{content:"\E038"}.gi-airplane:before{content:"\2708"}.gi-notes:before{content:"\E040"}.gi-stats:before{content:"\E041"}.gi-charts:before{content:"\E042"}.gi-pie_chart:before{content:"\E043"}.gi-group:before{content:"\E044"}.gi-keys:before{content:"\E045"}.gi-calendar:before{content:"\E046"}.gi-router:before{content:"\E047"}.gi-camera_small:before{content:"\E048"}.gi-dislikes:before{content:"\E049"}.gi-star:before{content:"\E050"}.gi-link:before{content:"\E051"}.gi-eye_open:before{content:"\E052"}.gi-eye_close:before{content:"\E053"}.gi-alarm:before{content:"\E054"}.gi-clock:before{content:"\E055"}.gi-stopwatch:before{content:"\E056"}.gi-projector:before{content:"\E057"}.gi-history:before{content:"\E058"}.gi-truck:before{content:"\E059"}.gi-cargo:before{content:"\E060"}.gi-compass:before{content:"\E061"}.gi-keynote:before{content:"\E062"}.gi-paperclip:before{content:"\E063"}.gi-power:before{content:"\E064"}.gi-lightbulb:before{content:"\E065"}.gi-tag:before{content:"\E066"}.gi-tags:before{content:"\E067"}.gi-cleaning:before{content:"\E068"}.gi-ruller:before{content:"\E069"}.gi-gift:before{content:"\E070"}.gi-umbrella:before{content:"\2602"}.gi-book:before{content:"\E072"}.gi-bookmark:before{content:"\E073"}.gi-wifi:before{content:"\E074"}.gi-cup:before{content:"\E075"}.gi-stroller:before{content:"\E076"}.gi-headphones:before{content:"\E077"}.gi-headset:before{content:"\E078"}.gi-warning_sign:before{content:"\E079"}.gi-signal:before{content:"\E080"}.gi-retweet:before{content:"\E081"}.gi-refresh:before{content:"\E082"}.gi-roundabout:before{content:"\E083"}.gi-random:before{content:"\E084"}.gi-heat:before{content:"\E085"}.gi-repeat:before{content:"\E086"}.gi-display:before{content:"\E087"}.gi-log_book:before{content:"\E088"}.gi-address_book:before{content:"\E089"}.gi-building:before{content:"\E090"}.gi-eyedropper:before{content:"\E091"}.gi-adjust:before{content:"\E092"}.gi-tint:before{content:"\E093"}.gi-crop:before{content:"\E094"}.gi-vector_path_square:before{content:"\E095"}.gi-vector_path_circle:before{content:"\E096"}.gi-vector_path_polygon:before{content:"\E097"}.gi-vector_path_line:before{content:"\E098"}.gi-vector_path_curve:before{content:"\E099"}.gi-vector_path_all:before{content:"\E100"}.gi-font:before{content:"\E101"}.gi-italic:before{content:"\E102"}.gi-bold:before{content:"\E103"}.gi-text_underline:before{content:"\E104"}.gi-text_strike:before{content:"\E105"}.gi-text_height:before{content:"\E106"}.gi-text_width:before{content:"\E107"}.gi-text_resize:before{content:"\E108"}.gi-left_indent:before{content:"\E109"}.gi-right_indent:before{content:"\E110"}.gi-align_left:before{content:"\E111"}.gi-align_center:before{content:"\E112"}.gi-align_right:before{content:"\E113"}.gi-justify:before{content:"\E114"}.gi-list:before{content:"\E115"}.gi-text_smaller:before{content:"\E116"}.gi-text_bigger:before{content:"\E117"}.gi-embed:before{content:"\E118"}.gi-embed_close:before{content:"\E119"}.gi-table:before{content:"\E120"}.gi-message_full:before{content:"\E121"}.gi-message_empty:before{content:"\E122"}.gi-message_in:before{content:"\E123"}.gi-message_out:before{content:"\E124"}.gi-message_plus:before{content:"\E125"}.gi-message_minus:before{content:"\E126"}.gi-message_ban:before{content:"\E127"}.gi-message_flag:before{content:"\E128"}.gi-message_lock:before{content:"\E129"}.gi-message_new:before{content:"\E130"}.gi-inbox:before{content:"\E131"}.gi-inbox_plus:before{content:"\E132"}.gi-inbox_minus:before{content:"\E133"}.gi-inbox_lock:before{content:"\E134"}.gi-inbox_in:before{content:"\E135"}.gi-inbox_out:before{content:"\E136"}.gi-cogwheel:before{content:"\E137"}.gi-cogwheels:before{content:"\E138"}.gi-picture:before{content:"\E139"}.gi-adjust_alt:before{content:"\E140"}.gi-database_lock:before{content:"\E141"}.gi-database_plus:before{content:"\E142"}.gi-database_minus:before{content:"\E143"}.gi-database_ban:before{content:"\E144"}.gi-folder_open:before{content:"\E145"}.gi-folder_plus:before{content:"\E146"}.gi-folder_minus:before{content:"\E147"}.gi-folder_lock:before{content:"\E148"}.gi-folder_flag:before{content:"\E149"}.gi-folder_new:before{content:"\E150"}.gi-edit:before{content:"\E151"}.gi-new_window:before{content:"\E152"}.gi-check:before{content:"\E153"}.gi-unchecked:before{content:"\E154"}.gi-more_windows:before{content:"\E155"}.gi-show_big_thumbnails:before{content:"\E156"}.gi-show_thumbnails:before{content:"\E157"}.gi-show_thumbnails_with_lines:before{content:"\E158"}.gi-show_lines:before{content:"\E159"}.gi-playlist:before{content:"\E160"}.gi-imac:before{content:"\E161"}.gi-macbook:before{content:"\E162"}.gi-ipad:before{content:"\E163"}.gi-iphone:before{content:"\E164"}.gi-iphone_transfer:before{content:"\E165"}.gi-iphone_exchange:before{content:"\E166"}.gi-ipod:before{content:"\E167"}.gi-ipod_shuffle:before{content:"\E168"}.gi-ear_plugs:before{content:"\E169"}.gi-record:before{content:"\E170"}.gi-step_backward:before{content:"\E171"}.gi-fast_backward:before{content:"\E172"}.gi-rewind:before{content:"\E173"}.gi-play:before{content:"\E174"}.gi-pause:before{content:"\E175"}.gi-stop:before{content:"\E176"}.gi-forward:before{content:"\E177"}.gi-fast_forward:before{content:"\E178"}.gi-step_forward:before{content:"\E179"}.gi-eject:before{content:"\E180"}.gi-facetime_video:before{content:"\E181"}.gi-download_alt:before{content:"\E182"}.gi-mute:before{content:"\E183"}.gi-volume_down:before{content:"\E184"}.gi-volume_up:before{content:"\E185"}.gi-screenshot:before{content:"\E186"}.gi-move:before{content:"\E187"}.gi-more:before{content:"\E188"}.gi-brightness_reduce:before{content:"\E189"}.gi-brightness_increase:before{content:"\E190"}.gi-circle_plus:before{content:"\E191"}.gi-circle_minus:before{content:"\E192"}.gi-circle_remove:before{content:"\E193"}.gi-circle_ok:before{content:"\E194"}.gi-circle_question_mark:before{content:"\E195"}.gi-circle_info:before{content:"\E196"}.gi-circle_exclamation_mark:before{content:"\E197"}.gi-remove:before{content:"\E198"}.gi-ok:before{content:"\E199"}.gi-ban:before{content:"\E200"}.gi-download:before{content:"\E201"}.gi-upload:before{content:"\E202"}.gi-shopping_cart:before{content:"\E203"}.gi-lock:before{content:"\E204"}.gi-unlock:before{content:"\E205"}.gi-electricity:before{content:"\E206"}.gi-ok_2:before{content:"\E207"}.gi-remove_2:before{content:"\E208"}.gi-cart_out:before{content:"\E209"}.gi-cart_in:before{content:"\E210"}.gi-left_arrow:before{content:"\E211"}.gi-right_arrow:before{content:"\E212"}.gi-down_arrow:before{content:"\E213"}.gi-up_arrow:before{content:"\E214"}.gi-resize_small:before{content:"\E215"}.gi-resize_full:before{content:"\E216"}.gi-circle_arrow_left:before{content:"\E217"}.gi-circle_arrow_right:before{content:"\E218"}.gi-circle_arrow_top:before{content:"\E219"}.gi-circle_arrow_down:before{content:"\E220"}.gi-play_button:before{content:"\E221"}.gi-unshare:before{content:"\E222"}.gi-share:before{content:"\E223"}.gi-chevron-right:before{content:"\E224"}.gi-chevron-left:before{content:"\E225"}.gi-bluetooth:before{content:"\E226"}.gi-euro:before{content:"\20AC"}.gi-usd:before{content:"\E228"}.gi-gbp:before{content:"\E229"}.gi-retweet_2:before{content:"\E230"}.gi-moon:before{content:"\E231"}.gi-sun:before{content:"\2609"}.gi-cloud:before{content:"\2601"}.gi-direction:before{content:"\E234"}.gi-brush:before{content:"\E235"}.gi-pen:before{content:"\E236"}.gi-zoom_in:before{content:"\E237"}.gi-zoom_out:before{content:"\E238"}.gi-pin:before{content:"\E239"}.gi-albums:before{content:"\E240"}.gi-rotation_lock:before{content:"\E241"}.gi-flash:before{content:"\E242"}.gi-google_maps:before{content:"\E243"}.gi-anchor:before{content:"\2693"}.gi-conversation:before{content:"\E245"}.gi-chat:before{content:"\E246"}.gi-male:before{content:"\E247"}.gi-female:before{content:"\E248"}.gi-asterisk:before{content:"\002A"}.gi-divide:before{content:"\00F7"}.gi-snorkel_diving:before{content:"\E251"}.gi-scuba_diving:before{content:"\E252"}.gi-oxygen_bottle:before{content:"\E253"}.gi-fins:before{content:"\E254"}.gi-fishes:before{content:"\E255"}.gi-boat:before{content:"\E256"}.gi-delete:before{content:"\E257"}.gi-sheriffs_star:before{content:"\E258"}.gi-qrcode:before{content:"\E259"}.gi-barcode:before{content:"\E260"}.gi-pool:before{content:"\E261"}.gi-buoy:before{content:"\E262"}.gi-spade:before{content:"\E263"}.gi-bank:before{content:"\E264"}.gi-vcard:before{content:"\E265"}.gi-electrical_plug:before{content:"\E266"}.gi-flag:before{content:"\E267"}.gi-credit_card:before{content:"\E268"}.gi-keyboard-wireless:before{content:"\E269"}.gi-keyboard-wired:before{content:"\E270"}.gi-shield:before{content:"\E271"}.gi-ring:before{content:"\02DA"}.gi-cake:before{content:"\E273"}.gi-drink:before{content:"\E274"}.gi-beer:before{content:"\E275"}.gi-fast_food:before{content:"\E276"}.gi-cutlery:before{content:"\E277"}.gi-pizza:before{content:"\E278"}.gi-birthday_cake:before{content:"\E279"}.gi-tablet:before{content:"\E280"}.gi-settings:before{content:"\E281"}.gi-bullets:before{content:"\E282"}.gi-cardio:before{content:"\E283"}.gi-t-shirt:before{content:"\E284"}.gi-pants:before{content:"\E285"}.gi-sweater:before{content:"\E286"}.gi-fabric:before{content:"\E287"}.gi-leather:before{content:"\E288"}.gi-scissors:before{content:"\E289"}.gi-bomb:before{content:"\E290"}.gi-skull:before{content:"\E291"}.gi-celebration:before{content:"\E292"}.gi-tea_kettle:before{content:"\E293"}.gi-french_press:before{content:"\E294"}.gi-coffee_cup:before{content:"\E295"}.gi-pot:before{content:"\E296"}.gi-grater:before{content:"\E297"}.gi-kettle:before{content:"\E298"}.gi-hospital:before{content:"\E299"}.gi-hospital_h:before{content:"\E300"}.gi-microphone:before{content:"\E301"}.gi-webcam:before{content:"\E302"}.gi-temple_christianity_church:before{content:"\E303"}.gi-temple_islam:before{content:"\E304"}.gi-temple_hindu:before{content:"\E305"}.gi-temple_buddhist:before{content:"\E306"}.gi-bicycle:before{content:"\E307"}.gi-life_preserver:before{content:"\E308"}.gi-share_alt:before{content:"\E309"}.gi-comments:before{content:"\E310"}.gi-flower:before{content:"\2698"}.gi-baseball:before{content:"\26BE"}.gi-rugby:before{content:"\E313"}.gi-ax:before{content:"\E314"}.gi-table_tennis:before{content:"\E315"}.gi-bowling:before{content:"\E316"}.gi-tree_conifer:before{content:"\E317"}.gi-tree_deciduous:before{content:"\E318"}.gi-more_items:before{content:"\E319"}.gi-sort:before{content:"\E320"}.gi-filter:before{content:"\E321"}.gi-gamepad:before{content:"\E322"}.gi-playing_dices:before{content:"\E323"}.gi-calculator:before{content:"\E324"}.gi-tie:before{content:"\E325"}.gi-wallet:before{content:"\E326"}.gi-piano:before{content:"\E327"}.gi-sampler:before{content:"\E328"}.gi-podium:before{content:"\E329"}.gi-soccer_ball:before{content:"\E330"}.gi-blog:before{content:"\E331"}.gi-dashboard:before{content:"\E332"}.gi-certificate:before{content:"\E333"}.gi-bell:before{content:"\E334"}.gi-candle:before{content:"\E335"}.gi-pushpin:before{content:"\E336"}.gi-iphone_shake:before{content:"\E337"}.gi-pin_flag:before{content:"\E338"}.gi-turtle:before{content:"\E339"}.gi-rabbit:before{content:"\E340"}.gi-globe:before{content:"\E341"}.gi-briefcase:before{content:"\E342"}.gi-hdd:before{content:"\E343"}.gi-thumbs_up:before{content:"\E344"}.gi-thumbs_down:before{content:"\E345"}.gi-hand_right:before{content:"\E346"}.gi-hand_left:before{content:"\E347"}.gi-hand_up:before{content:"\E348"}.gi-hand_down:before{content:"\E349"}.gi-fullscreen:before{content:"\E350"}.gi-shopping_bag:before{content:"\E351"}.gi-book_open:before{content:"\E352"}.gi-nameplate:before{content:"\E353"}.gi-nameplate_alt:before{content:"\E354"}.gi-vases:before{content:"\E355"}.gi-bullhorn:before{content:"\E356"}.gi-dumbbell:before{content:"\E357"}.gi-suitcase:before{content:"\E358"}.gi-file_import:before{content:"\E359"}.gi-file_export:before{content:"\E360"}.gi-bug:before{content:"\E361"}.gi-crown:before{content:"\E362"}.gi-smoking:before{content:"\E363"}.gi-cloud-download:before{content:"\E364"}.gi-cloud-upload:before{content:"\E365"}.gi-restart:before{content:"\E366"}.gi-security_camera:before{content:"\E367"}.gi-expand:before{content:"\E368"}.gi-collapse:before{content:"\E369"}.gi-collapse_top:before{content:"\E370"}.gi-globe_af:before{content:"\E371"}.gi-global:before{content:"\E372"}.gi-spray:before{content:"\E373"}.gi-nails:before{content:"\E374"}.gi-claw_hammer:before{content:"\E375"}.gi-classic_hammer:before{content:"\E376"}.gi-hand_saw:before{content:"\E377"}.gi-riflescope:before{content:"\E378"}.gi-electrical_socket_eu:before{content:"\E379"}.gi-electrical_socket_us:before{content:"\E380"}.gi-message_forward:before{content:"\E381"}.gi-coat_hanger:before{content:"\E382"}.gi-dress:before{content:"\E383"}.gi-bathrobe:before{content:"\E384"}.gi-shirt:before{content:"\E385"}.gi-underwear:before{content:"\E386"}.gi-log_in:before{content:"\E387"}.gi-log_out:before{content:"\E388"}.gi-exit:before{content:"\E389"}.gi-new_window_alt:before{content:"\E390"}.gi-video_sd:before{content:"\E391"}.gi-video_hd:before{content:"\E392"}.gi-subtitles:before{content:"\E393"}.gi-sound_stereo:before{content:"\E394"}.gi-sound_dolby:before{content:"\E395"}.gi-sound_5_1:before{content:"\E396"}.gi-sound_6_1:before{content:"\E397"}.gi-sound_7_1:before{content:"\E398"}.gi-copyright_mark:before{content:"\E399"}.gi-registration_mark:before{content:"\E400"}.gi-radar:before{content:"\E401"}.gi-skateboard:before{content:"\E402"}.gi-golf_course:before{content:"\E403"}.gi-sorting:before{content:"\E404"}.gi-sort-by-alphabet:before{content:"\E405"}.gi-sort-by-alphabet-alt:before{content:"\E406"}.gi-sort-by-order:before{content:"\E407"}.gi-sort-by-order-alt:before{content:"\E408"}.gi-sort-by-attributes:before{content:"\E409"}.gi-sort-by-attributes-alt:before{content:"\E410"}.gi-compressed:before{content:"\E411"}.gi-package:before{content:"\E412"}.gi-cloud_plus:before{content:"\E413"}.gi-cloud_minus:before{content:"\E414"}.gi-disk_save:before{content:"\E415"}.gi-disk_open:before{content:"\E416"}.gi-disk_saved:before{content:"\E417"}.gi-disk_remove:before{content:"\E418"}.gi-disk_import:before{content:"\E419"}.gi-disk_export:before{content:"\E420"}.gi-tower:before{content:"\E421"}.gi-send:before{content:"\E422"}.gi-git_branch:before{content:"\E423"}.gi-git_create:before{content:"\E424"}.gi-git_private:before{content:"\E425"}.gi-git_delete:before{content:"\E426"}.gi-git_merge:before{content:"\E427"}.gi-git_pull_request:before{content:"\E428"}.gi-git_compare:before{content:"\E429"}.gi-git_commit:before{content:"\E430"}.gi-construction_cone:before{content:"\E431"}.gi-shoe_steps:before{content:"\E432"}.gi-plus:before{content:"\002B"}.gi-minus:before{content:"\2212"}.gi-redo:before{content:"\E435"}.gi-undo:before{content:"\E436"}.gi-golf:before{content:"\E437"}.gi-hockey:before{content:"\E438"}.gi-pipe:before{content:"\E439"}.gi-wrench:before{content:"\E440"}.gi-folder_closed:before{content:"\E441"}.gi-phone_alt:before{content:"\E442"}.gi-earphone:before{content:"\E443"}.gi-floppy_disk:before{content:"\E444"}.gi-floppy_saved:before{content:"\E445"}.gi-floppy_remove:before{content:"\E446"}.gi-floppy_save:before{content:"\E447"}.gi-floppy_open:before{content:"\E448"}.gi-translate:before{content:"\E449"}.gi-fax:before{content:"\E450"}.gi-factory:before{content:"\E451"}.gi-shop_window:before{content:"\E452"}.gi-shop:before{content:"\E453"}.gi-kiosk:before{content:"\E454"}.gi-kiosk_wheels:before{content:"\E455"}.gi-kiosk_light:before{content:"\E456"}.gi-kiosk_food:before{content:"\E457"}.gi-transfer:before{content:"\E458"}.gi-money:before{content:"\E459"}.gi-header:before{content:"\E460"}.gi-blacksmith:before{content:"\E461"}.gi-saw_blade:before{content:"\E462"}.gi-basketball:before{content:"\E463"}.gi-server:before{content:"\E464"}.gi-server_plus:before{content:"\E465"}.gi-server_minus:before{content:"\E466"}.gi-server_ban:before{content:"\E467"}.gi-server_flag:before{content:"\E468"}.gi-server_lock:before{content:"\E469"}.gi-server_new:before{content:"\E470"}.hi-glass:before{content:"\E001"}.hi-music:before{content:"\E002"}.hi-search:before{content:"\E003"}.hi-envelope:before{content:"\2709"}.hi-heart:before{content:"\E005"}.hi-star:before{content:"\E006"}.hi-star-empty:before{content:"\E007"}.hi-user:before{content:"\E008"}.hi-film:before{content:"\E009"}.hi-th-large:before{content:"\E010"}.hi-th:before{content:"\E011"}.hi-th-list:before{content:"\E012"}.hi-ok:before{content:"\E013"}.hi-remove:before{content:"\E014"}.hi-zoom-in:before{content:"\E015"}.hi-zoom-out:before{content:"\E016"}.hi-off:before{content:"\E017"}.hi-signal:before{content:"\E018"}.hi-cog:before{content:"\E019"}.hi-trash:before{content:"\E020"}.hi-home:before{content:"\E021"}.hi-file:before{content:"\E022"}.hi-time:before{content:"\E023"}.hi-road:before{content:"\E024"}.hi-download-alt:before{content:"\E025"}.hi-download:before{content:"\E026"}.hi-upload:before{content:"\E027"}.hi-inbox:before{content:"\E028"}.hi-play-circle:before{content:"\E029"}.hi-repeat:before{content:"\E030"}.hi-refresh:before{content:"\E031"}.hi-list-alt:before{content:"\E032"}.hi-lock:before{content:"\E033"}.hi-flag:before{content:"\E034"}.hi-headphones:before{content:"\E035"}.hi-volume-off:before{content:"\E036"}.hi-volume-down:before{content:"\E037"}.hi-volume-up:before{content:"\E038"}.hi-qrcode:before{content:"\E039"}.hi-barcode:before{content:"\E040"}.hi-tag:before{content:"\E041"}.hi-tags:before{content:"\E042"}.hi-book:before{content:"\E043"}.hi-bookmark:before{content:"\E044"}.hi-print:before{content:"\E045"}.hi-camera:before{content:"\E046"}.hi-font:before{content:"\E047"}.hi-bold:before{content:"\E048"}.hi-italic:before{content:"\E049"}.hi-text-height:before{content:"\E050"}.hi-text-width:before{content:"\E051"}.hi-align-left:before{content:"\E052"}.hi-align-center:before{content:"\E053"}.hi-align-right:before{content:"\E054"}.hi-align-justify:before{content:"\E055"}.hi-list:before{content:"\E056"}.hi-indent-left:before{content:"\E057"}.hi-indent-right:before{content:"\E058"}.hi-facetime-video:before{content:"\E059"}.hi-picture:before{content:"\E060"}.hi-pencil:before{content:"\270F"}.hi-map-marker:before{content:"\E062"}.hi-adjust:before{content:"\E063"}.hi-tint:before{content:"\E064"}.hi-edit:before{content:"\E065"}.hi-share:before{content:"\E066"}.hi-check:before{content:"\E067"}.hi-move:before{content:"\E068"}.hi-step-backward:before{content:"\E069"}.hi-fast-backward:before{content:"\E070"}.hi-backward:before{content:"\E071"}.hi-play:before{content:"\E072"}.hi-pause:before{content:"\E073"}.hi-stop:before{content:"\E074"}.hi-forward:before{content:"\E075"}.hi-fast-forward:before{content:"\E076"}.hi-step-forward:before{content:"\E077"}.hi-eject:before{content:"\E078"}.hi-chevron-left:before{content:"\E079"}.hi-chevron-right:before{content:"\E080"}.hi-plus-sign:before{content:"\E081"}.hi-minus-sign:before{content:"\E082"}.hi-remove-sign:before{content:"\E083"}.hi-ok-sign:before{content:"\E084"}.hi-question-sign:before{content:"\E085"}.hi-info-sign:before{content:"\E086"}.hi-screenshot:before{content:"\E087"}.hi-remove-circle:before{content:"\E088"}.hi-ok-circle:before{content:"\E089"}.hi-ban-circle:before{content:"\E090"}.hi-arrow-left:before{content:"\E091"}.hi-arrow-right:before{content:"\E092"}.hi-arrow-up:before{content:"\E093"}.hi-arrow-down:before{content:"\E094"}.hi-share-alt:before{content:"\E095"}.hi-resize-full:before{content:"\E096"}.hi-resize-small:before{content:"\E097"}.hi-plus:before{content:"\002B"}.hi-minus:before{content:"\2212"}.hi-asterisk:before{content:"\002A"}.hi-exclamation-sign:before{content:"\E101"}.hi-gift:before{content:"\E102"}.hi-leaf:before{content:"\E103"}.hi-fire:before{content:"\E104"}.hi-eye-open:before{content:"\E105"}.hi-eye-close:before{content:"\E106"}.hi-warning-sign:before{content:"\E107"}.hi-plane:before{content:"\E108"}.hi-calendar:before{content:"\E109"}.hi-random:before{content:"\E110"}.hi-comments:before{content:"\E111"}.hi-magnet:before{content:"\E112"}.hi-chevron-up:before{content:"\E113"}.hi-chevron-down:before{content:"\E114"}.hi-retweet:before{content:"\E115"}.hi-shopping-cart:before{content:"\E116"}.hi-folder-close:before{content:"\E117"}.hi-folder-open:before{content:"\E118"}.hi-resize-vertical:before{content:"\E119"}.hi-resize-horizontal:before{content:"\E120"}.hi-hdd:before{content:"\E121"}.hi-bullhorn:before{content:"\E122"}.hi-bell:before{content:"\E123"}.hi-certificate:before{content:"\E124"}.hi-thumbs-up:before{content:"\E125"}.hi-thumbs-down:before{content:"\E126"}.hi-hand-right:before{content:"\E127"}.hi-hand-left:before{content:"\E128"}.hi-hand-top:before{content:"\E129"}.hi-hand-down:before{content:"\E130"}.hi-circle-arrow-right:before{content:"\E131"}.hi-circle-arrow-left:before{content:"\E132"}.hi-circle-arrow-top:before{content:"\E133"}.hi-circle-arrow-down:before{content:"\E134"}.hi-globe:before{content:"\E135"}.hi-wrench:before{content:"\E136"}.hi-tasks:before{content:"\E137"}.hi-filter:before{content:"\E138"}.hi-briefcase:before{content:"\E139"}.hi-fullscreen:before{content:"\E140"}.hi-dashboard:before{content:"\E141"}.hi-paperclip:before{content:"\E142"}.hi-heart-empty:before{content:"\E143"}.hi-link:before{content:"\E144"}.hi-phone:before{content:"\E145"}.hi-pushpin:before{content:"\E146"}.hi-euro:before{content:"\20AC"}.hi-usd:before{content:"\E148"}.hi-gbp:before{content:"\E149"}.hi-sort:before{content:"\E150"}.hi-sort-by-alphabet:before{content:"\E151"}.hi-sort-by-alphabet-alt:before{content:"\E152"}.hi-sort-by-order:before{content:"\E153"}.hi-sort-by-order-alt:before{content:"\E154"}.hi-sort-by-attributes:before{content:"\E155"}.hi-sort-by-attributes-alt:before{content:"\E156"}.hi-unchecked:before{content:"\E157"}.hi-expand:before{content:"\E158"}.hi-collapse:before{content:"\E159"}.hi-collapse-top:before{content:"\E160"}.hi-log_in:before{content:"\E161"}.hi-flash:before{content:"\E162"}.hi-log_out:before{content:"\E163"}.hi-new_window:before{content:"\E164"}.hi-record:before{content:"\E165"}.hi-save:before{content:"\E166"}.hi-open:before{content:"\E167"}.hi-saved:before{content:"\E168"}.hi-import:before{content:"\E169"}.hi-export:before{content:"\E170"}.hi-send:before{content:"\E171"}.hi-floppy_disk:before{content:"\E172"}.hi-floppy_saved:before{content:"\E173"}.hi-floppy_remove:before{content:"\E174"}.hi-floppy_save:before{content:"\E175"}.hi-floppy_open:before{content:"\E176"}.hi-credit_card:before{content:"\E177"}.hi-transfer:before{content:"\E178"}.hi-cutlery:before{content:"\E179"}.hi-header:before{content:"\E180"}.hi-compressed:before{content:"\E181"}.hi-earphone:before{content:"\E182"}.hi-phone_alt:before{content:"\E183"}.hi-tower:before{content:"\E184"}.hi-stats:before{content:"\E185"}.hi-sd_video:before{content:"\E186"}.hi-hd_video:before{content:"\E187"}.hi-subtitles:before{content:"\E188"}.hi-sound_stereo:before{content:"\E189"}.hi-sound_dolby:before{content:"\E190"}.hi-sound_5_1:before{content:"\E191"}.hi-sound_6_1:before{content:"\E192"}.hi-sound_7_1:before{content:"\E193"}.hi-copyright_mark:before{content:"\E194"}.hi-registration_mark:before{content:"\E195"}.hi-cloud:before{content:"\2601"}.hi-cloud_download:before{content:"\E197"}.hi-cloud_upload:before{content:"\E198"}.hi-tree_conifer:before{content:"\E199"}.hi-tree_deciduous:before{content:"\E200"}.si-pinterest:before{content:"\E001"}.si-dropbox:before{content:"\E002"}.si-google_plus:before{content:"\E003"}.si-jolicloud:before{content:"\E004"}.si-yahoo:before{content:"\E005"}.si-blogger:before{content:"\E006"}.si-picasa:before{content:"\E007"}.si-amazon:before{content:"\E008"}.si-tumblr:before{content:"\E009"}.si-wordpress:before{content:"\E010"}.si-instapaper:before{content:"\E011"}.si-evernote:before{content:"\E012"}.si-xing:before{content:"\E013"}.si-zootool:before{content:"\E014"}.si-dribbble:before{content:"\E015"}.si-deviantart:before{content:"\E016"}.si-read_it_later:before{content:"\E017"}.si-linked_in:before{content:"\E018"}.si-forrst:before{content:"\E019"}.si-pinboard:before{content:"\E020"}.si-behance:before{content:"\E021"}.si-github:before{content:"\E022"}.si-youtube:before{content:"\E023"}.si-skitch:before{content:"\E024"}.si-foursquare:before{content:"\E025"}.si-quora:before{content:"\E026"}.si-badoo:before{content:"\E027"}.si-spotify:before{content:"\E028"}.si-stumbleupon:before{content:"\E029"}.si-readability:before{content:"\E030"}.si-facebook:before{content:"\E031"}.si-twitter:before{content:"\E032"}.si-instagram:before{content:"\E033"}.si-posterous_spaces:before{content:"\E034"}.si-vimeo:before{content:"\E035"}.si-flickr:before{content:"\E036"}.si-last_fm:before{content:"\E037"}.si-rss:before{content:"\E038"}.si-skype:before{content:"\E039"}.si-e-mail:before{content:"\E040"}.si-vine:before{content:"\E041"}.si-myspace:before{content:"\E042"}.si-goodreads:before{content:"\E043"}.si-apple:before{content:"\F8FF"}.si-windows:before{content:"\E045"}.si-yelp:before{content:"\E046"}.si-playstation:before{content:"\E047"}.si-xbox:before{content:"\E048"}.si-android:before{content:"\E049"}.si-ios:before{content:"\E050"}.fi-txt:before{content:"\E001"}.fi-doc:before{content:"\E002"}.fi-rtf:before{content:"\E003"}.fi-log:before{content:"\E004"}.fi-tex:before{content:"\E005"}.fi-msg:before{content:"\E006"}.fi-text:before{content:"\E007"}.fi-wpd:before{content:"\E008"}.fi-wps:before{content:"\E009"}.fi-docx:before{content:"\E010"}.fi-page:before{content:"\E011"}.fi-csv:before{content:"\E012"}.fi-dat:before{content:"\E013"}.fi-tar:before{content:"\E014"}.fi-xml:before{content:"\E015"}.fi-vcf:before{content:"\E016"}.fi-pps:before{content:"\E017"}.fi-key:before{content:"\E018"}.fi-ppt:before{content:"\E019"}.fi-pptx:before{content:"\E020"}.fi-sdf:before{content:"\E021"}.fi-gbr:before{content:"\E022"}.fi-ged:before{content:"\E023"}.fi-mp3:before{content:"\E024"}.fi-m4a:before{content:"\E025"}.fi-waw:before{content:"\E026"}.fi-wma:before{content:"\E027"}.fi-mpa:before{content:"\E028"}.fi-iff:before{content:"\E029"}.fi-aif:before{content:"\E030"}.fi-ra:before{content:"\E031"}.fi-mid:before{content:"\E032"}.fi-m3v:before{content:"\E033"}.fi-e_3gp:before{content:"\E034"}.fi-shf:before{content:"\E035"}.fi-avi:before{content:"\E036"}.fi-asx:before{content:"\E037"}.fi-mp4:before{content:"\E038"}.fi-e_3g2:before{content:"\E039"}.fi-mpg:before{content:"\E040"}.fi-asf:before{content:"\E041"}.fi-vob:before{content:"\E042"}.fi-wmv:before{content:"\E043"}.fi-mov:before{content:"\E044"}.fi-srt:before{content:"\E045"}.fi-m4v:before{content:"\E046"}.fi-flv:before{content:"\E047"}.fi-rm:before{content:"\E048"}.fi-png:before{content:"\E049"}.fi-psd:before{content:"\E050"}.fi-psp:before{content:"\E051"}.fi-jpg:before{content:"\E052"}.fi-tif:before{content:"\E053"}.fi-tiff:before{content:"\E054"}.fi-gif:before{content:"\E055"}.fi-bmp:before{content:"\E056"}.fi-tga:before{content:"\E057"}.fi-thm:before{content:"\E058"}.fi-yuv:before{content:"\E059"}.fi-dds:before{content:"\E060"}.fi-ai:before{content:"\E061"}.fi-eps:before{content:"\E062"}.fi-ps:before{content:"\E063"}.fi-svg:before{content:"\E064"}.fi-pdf:before{content:"\E065"}.fi-pct:before{content:"\E066"}.fi-indd:before{content:"\E067"}.fi-xlr:before{content:"\E068"}.fi-xls:before{content:"\E069"}.fi-xlsx:before{content:"\E070"}.fi-db:before{content:"\E071"}.fi-dbf:before{content:"\E072"}.fi-mdb:before{content:"\E073"}.fi-pdb:before{content:"\E074"}.fi-sql:before{content:"\E075"}.fi-aacd:before{content:"\E076"}.fi-app:before{content:"\E077"}.fi-exe:before{content:"\E078"}.fi-com:before{content:"\E079"}.fi-bat:before{content:"\E080"}.fi-apk:before{content:"\E081"}.fi-jar:before{content:"\E082"}.fi-hsf:before{content:"\E083"}.fi-pif:before{content:"\E084"}.fi-vb:before{content:"\E085"}.fi-cgi:before{content:"\E086"}.fi-css:before{content:"\E087"}.fi-js:before{content:"\E088"}.fi-php:before{content:"\E089"}.fi-xhtml:before{content:"\E090"}.fi-htm:before{content:"\E091"}.fi-html:before{content:"\E092"}.fi-asp:before{content:"\E093"}.fi-cer:before{content:"\E094"}.fi-jsp:before{content:"\E095"}.fi-cfm:before{content:"\E096"}.fi-aspx:before{content:"\E097"}.fi-rss:before{content:"\E098"}.fi-csr:before{content:"\E099"}.fi-less:before{content:"\003C"}.fi-otf:before{content:"\E101"}.fi-ttf:before{content:"\E102"}.fi-font:before{content:"\E103"}.fi-fnt:before{content:"\E104"}.fi-eot:before{content:"\E105"}.fi-woff:before{content:"\E106"}.fi-zip:before{content:"\E107"}.fi-zipx:before{content:"\E108"}.fi-rar:before{content:"\E109"}.fi-targ:before{content:"\E110"}.fi-sitx:before{content:"\E111"}.fi-deb:before{content:"\E112"}.fi-e_7z:before{content:"\E113"}.fi-pkg:before{content:"\E114"}.fi-rpm:before{content:"\E115"}.fi-cbr:before{content:"\E116"}.fi-gz:before{content:"\E117"}.fi-dmg:before{content:"\E118"}.fi-cue:before{content:"\E119"}.fi-bin:before{content:"\E120"}.fi-iso:before{content:"\E121"}.fi-hdf:before{content:"\E122"}.fi-vcd:before{content:"\E123"}.fi-bak:before{content:"\E124"}.fi-tmp:before{content:"\E125"}.fi-ics:before{content:"\E126"}.fi-msi:before{content:"\E127"}.fi-cfg:before{content:"\E128"}.fi-ini:before{content:"\E129"}.fi-prf:before{content:"\E130"}

/*
=================================================================
(#03fcs) FullCalendar

Docs & License: http://arshaw.com/fullcalendar/
(c) 2013 Adam Shaw
=================================================================
*/

.fc {
    direction: ltr;
    text-align: left;
}

.fc-rtl {
    text-align: right;
}

body .fc { /* extra precedence to overcome jqui */
    font-size: 1em;
}


/* Colors
--------------------------------------------------------------------------------------------------*/

.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed hr,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
    border-color: #ddd;
}

.fc-unthemed .fc-popover {
    background-color: #fff;
}

.fc-unthemed .fc-divider,
.fc-unthemed .fc-popover .fc-header {
    background: #eee;
}

.fc-unthemed .fc-popover .fc-header .fc-close {
    color: #666;
}

.fc-unthemed .fc-today {
    background: #fcf8e3;
}

.fc-highlight { /* when user is selecting cells */
    background: #bce8f1;
    opacity: .3;
    filter: alpha(opacity=30); /* for IE */
}

.fc-bgevent { /* default look for background events */
    background: #5ccdde;
    opacity: .3;
    filter: alpha(opacity=30); /* for IE */
}

.fc-nonbusiness { /* default look for non-business-hours areas */
    /* will inherit .fc-bgevent's styles */
    background: #d7d7d7;
}


/* Icons (inline elements with styled text that mock arrow icons)
--------------------------------------------------------------------------------------------------*/

.fc-icon {
    display: inline-block;
    width: 1em;
    height: 1em;
    line-height: 1em;
    font-size: 1em;
    text-align: center;
    overflow: hidden;
    font-family: "Courier New", Courier, monospace;

    /* don't allow browser text-selection */
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/*
Acceptable font-family overrides for individual icons:
        "Arial", sans-serif
        "Times New Roman", serif

NOTE: use percentage font sizes or else old IE chokes
*/

.fc-icon:after {
    position: relative;
    margin: 0 -1em; /* ensures character will be centered, regardless of width */
}

.fc-icon-left-single-arrow:after {
    content: "\02039";
    font-weight: bold;
    font-size: 200%;
    top: -7%;
    left: 3%;
}

.fc-icon-right-single-arrow:after {
    content: "\0203A";
    font-weight: bold;
    font-size: 200%;
    top: -7%;
    left: -3%;
}

.fc-icon-left-double-arrow:after {
    content: "\000AB";
    font-size: 160%;
    top: -7%;
}

.fc-icon-right-double-arrow:after {
    content: "\000BB";
    font-size: 160%;
    top: -7%;
}

.fc-icon-left-triangle:after {
    content: "\25C4";
    font-size: 125%;
    top: 3%;
    left: -2%;
}

.fc-icon-right-triangle:after {
    content: "\25BA";
    font-size: 125%;
    top: 3%;
    left: 2%;
}

.fc-icon-down-triangle:after {
    content: "\25BC";
    font-size: 125%;
    top: 2%;
}

.fc-icon-x:after {
    content: "\000D7";
    font-size: 200%;
    top: 6%;
}


/* Buttons (styled <button> tags, normalized to work cross-browser)
--------------------------------------------------------------------------------------------------*/

.fc button {
    padding: 0 .6em;
    height: 34px;
    margin-top: 1px !important;
    line-height: 34px;
    white-space: nowrap;
    cursor: pointer;
}

/* Firefox has an annoying inner border */
.fc button::-moz-focus-inner { margin: 0; padding: 0; }

.fc-state-default { /* non-theme */
    border: 1px solid;
}

.fc-state-default.fc-corner-left { /* non-theme */
    border-top-left-radius: 3px;
    border-bottom-left-radius: 3px;
}

.fc-state-default.fc-corner-right { /* non-theme */
    border-top-right-radius: 3px;
    border-bottom-right-radius: 3px;
}

/* icons in buttons */

.fc button .fc-icon { /* non-theme */
    position: relative;
    top: .05em; /* seems to be a good adjustment across browsers */
    margin: 0 .1em;
}

/*
  button states
  borrowed from twitter bootstrap (http://twitter.github.com/bootstrap/)
*/

.fc-state-default {
    background-color: #6ad2eb;
    color: #ffffff;
    border: 1px solid #1bbae1;
}

.fc-state-hover,
.fc-state-down,
.fc-state-active,
.fc-state-disabled {
    color: #ffffff;
    background-color: #1bbae1;
}

.fc-state-hover {
    text-decoration: none;
    background-color: #1bbae1;
}

.fc-state-down,
.fc-state-active {
    background-color: #1bbae1;
    border-color: #1593b3;
    outline: 0;
}

.fc-state-disabled {
    cursor: default;
    opacity: .65;
    filter: alpha(opacity=65);
}


/* Buttons Groups
--------------------------------------------------------------------------------------------------*/

.fc-button-group {
    display: inline-block;
}

/*
every button that is not first in a button group should scootch over one pixel and cover the
previous button's border...
*/

.fc .fc-button-group > * { /* extra precedence b/c buttons have margin set to zero */
    float: left;
    margin: 0 0 0 -1px;
}

.fc .fc-button-group > :first-child { /* same */
    margin-left: 0;
}


/* Popover
--------------------------------------------------------------------------------------------------*/

.fc-popover {
    position: absolute;
    box-shadow: 0 2px 6px rgba(0,0,0,.15);
}

.fc-popover .fc-header {
    padding: 2px 4px;
}

.fc-popover .fc-header .fc-title {
    margin: 0 2px;
}

.fc-popover .fc-header .fc-close {
    cursor: pointer;
}

.fc-ltr .fc-popover .fc-header .fc-title,
.fc-rtl .fc-popover .fc-header .fc-close {
    float: left;
}

.fc-rtl .fc-popover .fc-header .fc-title,
.fc-ltr .fc-popover .fc-header .fc-close {
    float: right;
}

/* unthemed */

.fc-unthemed .fc-popover {
    border-width: 1px;
    border-style: solid;
}

.fc-unthemed .fc-popover .fc-header .fc-close {
    font-size: 25px;
    margin-top: 4px;
}

/* jqui themed */

.fc-popover > .ui-widget-header + .ui-widget-content {
    border-top: 0; /* where they meet, let the header have the border */
}


/* Misc Reusable Components
--------------------------------------------------------------------------------------------------*/

.fc-divider {
    border-style: solid;
    border-width: 1px;
}

hr.fc-divider {
    height: 0;
    margin: 0;
    padding: 0 0 2px; /* height is unreliable across browsers, so use padding */
    border-width: 1px 0;
}

.fc-clear {
    clear: both;
}

.fc-bg,
.fc-highlight-skeleton,
.fc-helper-skeleton {
    /* these element should always cling to top-left/right corners */
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

.fc-bg {
    bottom: 0; /* strech bg to bottom edge */
}

.fc-bg table {
    height: 100%; /* strech bg to bottom edge */
}


/* Tables
--------------------------------------------------------------------------------------------------*/

.fc table {
    width: 100%;
    table-layout: fixed;
    border-collapse: collapse;
    border-spacing: 0;
    font-size: 1em; /* normalize cross-browser */
}

.fc th {
    text-align: center;
}

.fc th,
.fc td {
    border-style: solid;
    border-width: 1px;
    padding: 0;
    vertical-align: top;
}

.fc td.fc-today {
    border-style: double; /* overcome neighboring borders */
}


/* Fake Table Rows
--------------------------------------------------------------------------------------------------*/

.fc .fc-row { /* extra precedence to overcome themes w/ .ui-widget-content forcing a 1px border */
    /* no visible border by default. but make available if need be (scrollbar width compensation) */
    border-style: solid;
    border-width: 0;
}

.fc-row table {
    /* don't put left/right border on anything within a fake row.
       the outer tbody will worry about this */
    border-left: 0 hidden transparent;
    border-right: 0 hidden transparent;

    /* no bottom borders on rows */
    border-bottom: 0 hidden transparent;
}

.fc-row:first-child table {
    border-top: 0 hidden transparent; /* no top border on first row */
}


/* Day Row (used within the header and the DayGrid)
--------------------------------------------------------------------------------------------------*/

.fc-row {
    position: relative;
}

.fc-row .fc-bg {
    z-index: 1;
}

/* highlighting cells & background event skeleton */

.fc-row .fc-bgevent-skeleton,
.fc-row .fc-highlight-skeleton {
    bottom: 0; /* stretch skeleton to bottom of row */
}

.fc-row .fc-bgevent-skeleton table,
.fc-row .fc-highlight-skeleton table {
    height: 100%; /* stretch skeleton to bottom of row */
}

.fc-row .fc-highlight-skeleton td,
.fc-row .fc-bgevent-skeleton td {
    border-color: transparent;
}

.fc-row .fc-bgevent-skeleton {
    z-index: 2;

}

.fc-row .fc-highlight-skeleton {
    z-index: 3;
}

/*
row content (which contains day/week numbers and events) as well as "helper" (which contains
temporary rendered events).
*/

.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 4;
    padding-bottom: 2px; /* matches the space above the events */
}

.fc-row .fc-helper-skeleton {
    z-index: 5;
}

.fc-row .fc-content-skeleton td,
.fc-row .fc-helper-skeleton td {
    /* see-through to the background below */
    background: none; /* in case <td>s are globally styled */
    border-color: transparent;

    /* don't put a border between events and/or the day number */
    border-bottom: 0;
}

.fc-row .fc-content-skeleton tbody td, /* cells with events inside (so NOT the day number cell) */
.fc-row .fc-helper-skeleton tbody td {
    /* don't put a border between event cells */
    border-top: 0;
}


/* Scrolling Container
--------------------------------------------------------------------------------------------------*/

.fc-scroller { /* this class goes on elements for guaranteed vertical scrollbars */
    overflow-y: scroll;
    overflow-x: hidden;
}

.fc-scroller > * { /* we expect an immediate inner element */
    position: relative; /* re-scope all positions */
    width: 100%; /* hack to force re-sizing this inner element when scrollbars appear/disappear */
    overflow: hidden; /* don't let negative margins or absolute positioning create further scroll */
}


/* Global Event Styles
--------------------------------------------------------------------------------------------------*/

.fc-event {
    position: relative; /* for resize handle and other inner positioning */
    display: block; /* make the <a> tag block */
    font-size: .85em;
    line-height: 1.3;
    border-radius: 3px;
    border: 1px solid #5ccdde; /* default BORDER color */
    background-color: #5ccdde; /* default BACKGROUND color */
    font-weight: normal; /* undo jqui's ui-widget-header bold */
}

/* overpower some of bootstrap's and jqui's styles on <a> tags */
.fc-event,
.fc-event:hover,
.ui-widget .fc-event {
    color: #fff; /* default TEXT color */
    text-decoration: none; /* if <a> has an href */
}

.fc-event[href],
.fc-event.fc-draggable {
    cursor: pointer; /* give events with links and draggable events a hand mouse pointer */
}

.fc-not-allowed, /* causes a "warning" cursor. applied on body */
.fc-not-allowed .fc-event { /* to override an event's custom cursor */
    cursor: not-allowed;
}

.fc-event .fc-bg { /* the generic .fc-bg already does position */
    z-index: 1;
    background: #fff;
    opacity: .25;
    filter: alpha(opacity=25); /* for IE */
}

.fc-event .fc-content {
    position: relative;
    z-index: 2;
}

.fc-event .fc-resizer {
    position: absolute;
    z-index: 3;
}

/* Horizontal Events
--------------------------------------------------------------------------------------------------*/

/* events that are continuing to/from another week. kill rounded corners and butt up against edge */

.fc-ltr .fc-h-event.fc-not-start,
.fc-rtl .fc-h-event.fc-not-end {
    margin-left: 0;
    border-left-width: 0;
    padding-left: 1px; /* replace the border with padding */
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.fc-ltr .fc-h-event.fc-not-end,
.fc-rtl .fc-h-event.fc-not-start {
    margin-right: 0;
    border-right-width: 0;
    padding-right: 1px; /* replace the border with padding */
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

/* resizer */

.fc-h-event .fc-resizer { /* positioned it to overcome the event's borders */
    top: -1px;
    bottom: -1px;
    left: -1px;
    right: -1px;
    width: 5px;
}

/* left resizer  */
.fc-ltr .fc-h-event .fc-start-resizer,
.fc-ltr .fc-h-event .fc-start-resizer:before,
.fc-ltr .fc-h-event .fc-start-resizer:after,
.fc-rtl .fc-h-event .fc-end-resizer,
.fc-rtl .fc-h-event .fc-end-resizer:before,
.fc-rtl .fc-h-event .fc-end-resizer:after {
    right: auto; /* ignore the right and only use the left */
    cursor: w-resize;
}

/* right resizer */
.fc-ltr .fc-h-event .fc-end-resizer,
.fc-ltr .fc-h-event .fc-end-resizer:before,
.fc-ltr .fc-h-event .fc-end-resizer:after,
.fc-rtl .fc-h-event .fc-start-resizer,
.fc-rtl .fc-h-event .fc-start-resizer:before,
.fc-rtl .fc-h-event .fc-start-resizer:after {
    left: auto; /* ignore the left and only use the right */
    cursor: e-resize;
}


/* DayGrid events
----------------------------------------------------------------------------------------------------
We use the full "fc-day-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/

.fc-day-grid-event {
    margin: 1px 2px 0; /* spacing between events and edges */
    padding: 0 1px;
}


.fc-day-grid-event .fc-content { /* force events to be one-line tall */
    white-space: nowrap;
    overflow: hidden;
}

.fc-day-grid-event .fc-time {
    font-weight: bold;
}

.fc-day-grid-event .fc-resizer { /* enlarge the default hit area */
    left: -3px;
    right: -3px;
    width: 7px;
}


/* Event Limiting
--------------------------------------------------------------------------------------------------*/

/* "more" link that represents hidden events */

a.fc-more {
    margin: 1px 3px;
    font-size: .85em;
    cursor: pointer;
    text-decoration: none;
}

a.fc-more:hover {
    text-decoration: underline;
}

.fc-limited { /* rows and cells that are hidden because of a "more" link */
    display: none;
}

/* popover that appears when "more" link is clicked */

.fc-day-grid .fc-row {
    z-index: 1; /* make the "more" popover one higher than this */
}

.fc-more-popover {
    z-index: 2;
    width: 220px;
}

.fc-more-popover .fc-event-container {
    padding: 10px;
}

/* Toolbar
--------------------------------------------------------------------------------------------------*/

.fc-toolbar {
    text-align: center;
    margin-bottom: 1em;
}

.fc-toolbar .fc-left {
    float: left;
}

.fc-toolbar .fc-right {
    float: right;
}

.fc-toolbar .fc-center {
    display: inline-block;
}

/* the things within each left/right/center section */
.fc .fc-toolbar > * > * { /* extra precedence to override button border margins */
    float: left;
    margin-left: .75em;
}

/* the first thing within each left/center/right section */
.fc .fc-toolbar > * > :first-child { /* extra precedence to override button border margins */
    margin-left: 0;
}

/* title text */

.fc-toolbar h2 {
    margin: 0;
}

/* button layering (for border precedence) */

.fc-toolbar button {
    position: relative;
}

.fc-toolbar .fc-state-hover,
.fc-toolbar .ui-state-hover {
    z-index: 2;
}

.fc-toolbar .fc-state-down {
    z-index: 3;
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active {
    z-index: 4;
}

.fc-toolbar button:focus {
    z-index: 5;
}


/* View Structure
--------------------------------------------------------------------------------------------------*/

/* undo twitter bootstrap's box-sizing rules. normalizes positioning techniques */
/* don't do this for the toolbar because we'll want bootstrap to style those buttons as some pt */
.fc-view-container *,
.fc-view-container *:before,
.fc-view-container *:after {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

.fc-view, /* scope positioning and z-index's for everything within the view */
.fc-view > table { /* so dragged elements can be above the view's main element */
    position: relative;
    z-index: 1;
}

/* BasicView
--------------------------------------------------------------------------------------------------*/

/* day row structure */

.fc-basicWeek-view .fc-content-skeleton,
.fc-basicDay-view .fc-content-skeleton {
    /* we are sure there are no day numbers in these views, so... */
    padding-top: 1px; /* add a pixel to make sure there are 2px padding above events */
    padding-bottom: 1em; /* ensure a space at bottom of cell for user selecting/clicking */
}

.fc-basic-view tbody .fc-row {
    min-height: 4em; /* ensure that all rows are at least this tall */
}

/* a "rigid" row will take up a constant amount of height because content-skeleton is absolute */

.fc-row.fc-rigid {
    overflow: hidden;
}

.fc-row.fc-rigid .fc-content-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

/* week and day number styling */

.fc-basic-view .fc-week-number,
.fc-basic-view .fc-day-number {
    padding: 0 2px;
}

.fc-basic-view td.fc-week-number span,
.fc-basic-view td.fc-day-number {
    padding-top: 2px;
    padding-bottom: 2px;
}

.fc-basic-view .fc-week-number {
    text-align: center;
}

.fc-basic-view .fc-week-number span {
    /* work around the way we do column resizing and ensure a minimum width */
    display: inline-block;
    min-width: 1.25em;
}

.fc-ltr .fc-basic-view .fc-day-number {
    text-align: right;
}

.fc-rtl .fc-basic-view .fc-day-number {
    text-align: left;
}

.fc-day-number.fc-other-month {
    opacity: 0.3;
    filter: alpha(opacity=30); /* for IE */
    /* opacity with small font can sometimes look too faded
       might want to set the 'color' property instead
       making day-numbers bold also fixes the problem */
}

/* AgendaView all-day area
--------------------------------------------------------------------------------------------------*/

.fc-agenda-view .fc-day-grid {
    position: relative;
    z-index: 2; /* so the "more.." popover will be over the time grid */
}

.fc-agenda-view .fc-day-grid .fc-row {
    min-height: 3em; /* all-day section will never get shorter than this */
}

.fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
    padding-top: 1px; /* add a pixel to make sure there are 2px padding above events */
    padding-bottom: 1em; /* give space underneath events for clicking/selecting days */
}


/* TimeGrid axis running down the side (for both the all-day area and the slot area)
--------------------------------------------------------------------------------------------------*/

.fc .fc-axis { /* .fc to overcome default cell styles */
    vertical-align: middle;
    padding: 0 4px;
    white-space: nowrap;
}

.fc-ltr .fc-axis {
    text-align: right;
}

.fc-rtl .fc-axis {
    text-align: left;
}

.ui-widget td.fc-axis {
    font-weight: normal; /* overcome jqui theme making it bold */
}


/* TimeGrid Structure
--------------------------------------------------------------------------------------------------*/

.fc-time-grid-container, /* so scroll container's z-index is below all-day */
.fc-time-grid { /* so slats/bg/content/etc positions get scoped within here */
    position: relative;
    z-index: 1;
}

.fc-time-grid {
    min-height: 100%; /* so if height setting is 'auto', .fc-bg stretches to fill height */
}

.fc-time-grid table { /* don't put outer borders on slats/bg/content/etc */
    border: 0 hidden transparent;
}

.fc-time-grid > .fc-bg {
    z-index: 1;
}

.fc-time-grid .fc-slats,
.fc-time-grid > hr { /* the <hr> AgendaView injects when grid is shorter than scroller */
    position: relative;
    z-index: 2;
}

.fc-time-grid .fc-bgevent-skeleton,
.fc-time-grid .fc-content-skeleton {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
}

.fc-time-grid .fc-bgevent-skeleton {
    z-index: 3;
}

.fc-time-grid .fc-highlight-skeleton {
    z-index: 4;
}

.fc-time-grid .fc-content-skeleton {
    z-index: 5;
}

.fc-time-grid .fc-helper-skeleton {
    z-index: 6;
}


/* TimeGrid Slats (lines that run horizontally)
--------------------------------------------------------------------------------------------------*/

.fc-time-grid .fc-slats td {
    height: 1.5em;
    border-bottom: 0; /* each cell is responsible for its top border */
}

.fc-time-grid .fc-slats .fc-minor td {
    border-top-style: dotted;
}

.fc-time-grid .fc-slats .ui-widget-content { /* for jqui theme */
    background: none; /* see through to fc-bg */
}


/* TimeGrid Highlighting Slots
--------------------------------------------------------------------------------------------------*/

.fc-time-grid .fc-highlight-container { /* a div within a cell within the fc-highlight-skeleton */
    position: relative; /* scopes the left/right of the fc-highlight to be in the column */
}

.fc-time-grid .fc-highlight {
    position: absolute;
    left: 0;
    right: 0;
    /* top and bottom will be in by JS */
}


/* TimeGrid Event Containment
--------------------------------------------------------------------------------------------------*/

.fc-time-grid .fc-event-container, /* a div within a cell within the fc-content-skeleton */
.fc-time-grid .fc-bgevent-container { /* a div within a cell within the fc-bgevent-skeleton */
    position: relative;
}

.fc-ltr .fc-time-grid .fc-event-container { /* space on the sides of events for LTR (default) */
    margin: 0 2.5% 0 2px;
}

.fc-rtl .fc-time-grid .fc-event-container { /* space on the sides of events for RTL */
    margin: 0 2px 0 2.5%;
}

.fc-time-grid .fc-event,
.fc-time-grid .fc-bgevent {
    position: absolute;
    z-index: 1; /* scope inner z-index's */
}

.fc-time-grid .fc-bgevent {
    /* background events always span full width */
    left: 0;
    right: 0;
}

/* Generic Vertical Event
--------------------------------------------------------------------------------------------------*/

.fc-v-event.fc-not-start { /* events that are continuing from another day */
    /* replace space made by the top border with padding */
    border-top-width: 0;
    padding-top: 1px;

    /* remove top rounded corners */
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.fc-v-event.fc-not-end {
    /* replace space made by the top border with padding */
    border-bottom-width: 0;
    padding-bottom: 1px;

    /* remove bottom rounded corners */
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}


/* TimeGrid Event Styling
----------------------------------------------------------------------------------------------------
We use the full "fc-time-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/

.fc-time-grid-event {
    overflow: hidden; /* don't let the bg flow over rounded corners */
}

.fc-time-grid-event .fc-time,
.fc-time-grid-event .fc-title {
    padding: 0 1px;
}

.fc-time-grid-event .fc-time {
    font-size: .85em;
    white-space: nowrap;
}

/* short mode, where time and title are on the same line */

.fc-time-grid-event.fc-short .fc-content {
    /* don't wrap to second line (now that contents will be inline) */
    white-space: nowrap;
}

.fc-time-grid-event.fc-short .fc-time,
.fc-time-grid-event.fc-short .fc-title {
    /* put the time and title on the same line */
    display: inline-block;
    vertical-align: top;
}

.fc-time-grid-event.fc-short .fc-time span {
    display: none; /* don't display the full time text... */
}

.fc-time-grid-event.fc-short .fc-time:before {
    content: attr(data-start); /* ...instead, display only the start time */
}

.fc-time-grid-event.fc-short .fc-time:after {
    content: "\000A0-\000A0"; /* seperate with a dash, wrapped in nbsp's */
}

.fc-time-grid-event.fc-short .fc-title {
    font-size: .85em; /* make the title text the same size as the time */
    padding: 0; /* undo padding from above */
}

/* resizer */

.fc-time-grid-event .fc-resizer {
    left: 0;
    right: 0;
    bottom: 0;
    height: 8px;
    overflow: hidden;
    line-height: 8px;
    font-size: 11px;
    font-family: monospace;
    text-align: center;
    cursor: s-resize;
}

.fc-time-grid-event .fc-resizer:after {
    content: "=";
}

/* Custom */
.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed hr,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
    border-color: #dddddd;
}

.fc-unthemed .fc-today,
.fc-state-highlight { /* <td> today cell */ /* TODO: add .fc-today to <th> */
    background: #f9fafc;
}

.fc-highlight { /* when user is selecting cells */
    background: #999999;
    opacity: .1;
    filter: alpha(opacity=10); /* for IE */
}

thead th.fc-widget-header {
    padding-top: 10px !important;
    padding-bottom: 10px !important;
    font-size: 18px;
    font-weight: 600;
}

/* Small devices, Tablets (>768px) */
@media screen and (min-width: 768px) {

    .fc-toolbar {
        margin-bottom: 20px;
    }
}

/*
=================================================================
(#04djs) Dropzone.js

MIT License
=================================================================
*/

.dropzone {
    position: relative;
    border: 2px dashed #eaedf1;
    background-color: #f9fafc;
    padding: 10px;
}
.dropzone.dz-clickable {
    cursor: pointer;
}
.dropzone.dz-clickable * {
    cursor: default;
}
.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
    cursor: pointer;
}
.dropzone.dz-started .dz-message {
    display: none;
}
.dropzone.dz-drag-hover {
    border-style: solid;
}
.dropzone.dz-drag-hover .dz-message {
    opacity: 0.5;
}
.dropzone .dz-message {
    text-align: center;
    margin: 60px 0;
    font-size: 26px;
    font-weight: 300;
}
.dropzone .dz-preview {
    position: relative;
    display: inline-block;
    vertical-align: top;
    margin: 16px;
    min-height: 100px;
}
.dropzone .dz-preview:hover {
    z-index: 1000;
}
.dropzone .dz-preview:hover .dz-details {
    opacity: 1;
}
.dropzone .dz-preview.dz-file-preview .dz-image {
    border-radius: 20px;
    background: #999;
    background: linear-gradient(to bottom, #eee, #ddd);
}
.dropzone .dz-preview.dz-file-preview .dz-details {
    opacity: 1;
}
.dropzone .dz-preview.dz-image-preview {
    background: white;
}
.dropzone .dz-preview.dz-image-preview .dz-details {
    -webkit-transition: opacity 0.2s linear;
    -moz-transition: opacity 0.2s linear;
    -ms-transition: opacity 0.2s linear;
    -o-transition: opacity 0.2s linear;
    transition: opacity 0.2s linear;
}
.dropzone .dz-preview .dz-remove {
    font-size: 14px;
    text-align: center;
    display: block;
    cursor: pointer;
    border: none;
}
.dropzone .dz-preview .dz-remove:hover {
    text-decoration: underline;
}
.dropzone .dz-preview:hover .dz-details {
    opacity: 1;
}
.dropzone .dz-preview .dz-details {
    z-index: 20;
    position: absolute;
    top: 0;
    left: 0;
    opacity: 0;
    font-size: 13px;
    min-width: 100%;
    max-width: 100%;
    padding: 2em 1em;
    text-align: center;
    color: rgba(0, 0, 0, 0.9);
    line-height: 150%;
}
.dropzone .dz-preview .dz-details .dz-size {
    margin-bottom: 1em;
    font-size: 16px;
}
.dropzone .dz-preview .dz-details .dz-filename {
    white-space: nowrap;
}
.dropzone .dz-preview .dz-details .dz-filename:hover span {
    border: 1px solid rgba(200, 200, 200, 0.8);
    background-color: rgba(255, 255, 255, 0.8);
}
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
    overflow: hidden;
    text-overflow: ellipsis;
}
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
    border: 1px solid transparent;
}
.dropzone .dz-preview .dz-details .dz-filename span,
.dropzone .dz-preview .dz-details .dz-size span {
    background-color: rgba(255, 255, 255, 0.4);
    padding: 0 0.4em;
    border-radius: 3px;
}
.dropzone .dz-preview:hover .dz-image img {
    -webkit-transform: scale(1.05, 1.05);
    -moz-transform: scale(1.05, 1.05);
    -ms-transform: scale(1.05, 1.05);
    -o-transform: scale(1.05, 1.05);
    transform: scale(1.05, 1.05);
    -webkit-filter: blur(8px);
    filter: blur(8px);
}
.dropzone .dz-preview .dz-image {
    border-radius: 20px;
    overflow: hidden;
    width: 120px;
    height: 120px;
    position: relative;
    display: block;
    z-index: 10;
}
.dropzone .dz-preview .dz-image img {
    display: block;
}
.dropzone .dz-preview.dz-success .dz-success-mark {
    -webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
    -moz-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
    -ms-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
    -o-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
    animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
}
.dropzone .dz-preview.dz-error .dz-error-mark {
    opacity: 1;
    -webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
    -moz-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
    -ms-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
    -o-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
    animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
}
.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
    pointer-events: none;
    opacity: 0;
    z-index: 500;
    position: absolute;
    display: block;
    top: 50%;
    left: 50%;
    margin-left: -27px;
    margin-top: -27px;
}
.dropzone .dz-preview .dz-success-mark svg,
.dropzone .dz-preview .dz-error-mark svg {
    display: block;
    width: 54px;
    height: 54px;
}
.dropzone .dz-preview.dz-processing .dz-progress {
    opacity: 1;
    -webkit-transition: all 0.2s linear;
    -moz-transition: all 0.2s linear;
    -ms-transition: all 0.2s linear;
    -o-transition: all 0.2s linear;
    transition: all 0.2s linear;
}
.dropzone .dz-preview.dz-complete .dz-progress {
    opacity: 0;
    -webkit-transition: opacity 0.4s ease-in;
    -moz-transition: opacity 0.4s ease-in;
    -ms-transition: opacity 0.4s ease-in;
    -o-transition: opacity 0.4s ease-in;
    transition: opacity 0.4s ease-in;
}
.dropzone .dz-preview:not(.dz-processing) .dz-progress {
    -webkit-animation: pulse 6s ease infinite;
    -moz-animation: pulse 6s ease infinite;
    -ms-animation: pulse 6s ease infinite;
    -o-animation: pulse 6s ease infinite;
    animation: pulse 6s ease infinite;
}
.dropzone .dz-preview .dz-progress {
    opacity: 1;
    z-index: 1000;
    pointer-events: none;
    position: absolute;
    height: 16px;
    left: 50%;
    top: 50%;
    margin-top: -8px;
    width: 80px;
    margin-left: -40px;
    background: rgba(255, 255, 255, 0.9);
    -webkit-transform: scale(1);
    border-radius: 8px;
    overflow: hidden;
}
.dropzone .dz-preview .dz-progress .dz-upload {
    background: #333;
    background: linear-gradient(to bottom, #666, #444);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 0;
    -webkit-transition: width 300ms ease-in-out;
    -moz-transition: width 300ms ease-in-out;
    -ms-transition: width 300ms ease-in-out;
    -o-transition: width 300ms ease-in-out;
    transition: width 300ms ease-in-out;
}
.dropzone .dz-preview.dz-error .dz-error-message {
    display: block;
}
.dropzone .dz-preview.dz-error:hover .dz-error-message {
    opacity: 1;
    pointer-events: auto;
}
.dropzone .dz-preview .dz-error-message {
    pointer-events: none;
    z-index: 1000;
    position: absolute;
    display: block;
    display: none;
    opacity: 0;
    -webkit-transition: opacity 0.3s ease;
    -moz-transition: opacity 0.3s ease;
    -ms-transition: opacity 0.3s ease;
    -o-transition: opacity 0.3s ease;
    transition: opacity 0.3s ease;
    border-radius: 8px;
    font-size: 13px;
    top: 130px;
    left: -10px;
    width: 140px;
    background: #be2626;
    background: linear-gradient(to bottom, #be2626, #a92222);
    padding: 0.5em 1.2em;
    color: white;
}
.dropzone .dz-preview .dz-error-message:after {
    content: '';
    position: absolute;
    top: -6px;
    left: 64px;
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #be2626;
}

/*
=================================================================
(#05chs) Chosen

MIT License
=================================================================
*/

/* @group Base */
.chosen-container {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    font-size: 13px;
    zoom: 1;
    *display: inline;
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.chosen-container .chosen-drop {
    position: absolute;
    top: 100%;
    left: -9999px;
    z-index: 1041;
    width: 100%;
    border: 1px solid #1bbae1;
    border-top: 0;
    background: #ffffff;
    border-bottom-left-radius: 4px;
    border-bottom-right-radius: 4px;
}
.chosen-container.chosen-with-drop .chosen-drop {
    left: 0;
}
.chosen-container a {
    cursor: pointer;
}

/* @end */
/* @group Single Chosen */
.chosen-container-single .chosen-single {
    position: relative;
    display: block;
    overflow: hidden;
    padding: 0 0 0 8px;
    height: 34px;
    border: 1px solid #dbe1e8;
    background-color: #ffffff;
    text-decoration: none;
    white-space: nowrap;
    line-height: 24px;
    border-radius: 4px;
    color: #394263;
}
.chosen-container-single .chosen-default span {
    color: #999;
}
.chosen-container-single .chosen-single span {
    height: 34px;
    line-height: 34px;
    display: block;
    overflow: hidden;
    margin-right: 26px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.chosen-container-single .chosen-single-with-deselect span {
    margin-right: 38px;
}
.chosen-container-single .chosen-single abbr {
    position: absolute;
    top: 6px;
    right: 26px;
    display: block;
    width: 12px;
    height: 12px;
    background: url('../img/jquery.chosen/chosen-sprite.png') -42px 1px no-repeat;
    font-size: 1px;
}
.chosen-container-single .chosen-single abbr:hover {
    background-position: -42px -10px;
}
.chosen-container-single.chosen-disabled .chosen-single abbr:hover {
    background-position: -42px -10px;
}
.chosen-container-single .chosen-single div {
    position: absolute;
    top: 0;
    right: 0;
    display: block;
    width: 18px;
    height: 100%;
}
.chosen-container-single .chosen-single div b {
    display: block;
    margin-top: 6px;
    width: 100%;
    height: 100%;
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat 0px 2px;
}
.chosen-container-single .chosen-search {
    position: relative;
    z-index: 1010;
    margin: 0;
    padding: 3px 4px;
    white-space: nowrap;
}
.chosen-container-single .chosen-search input[type="text"] {
    margin: 1px 0;
    padding: 4px 20px 4px 5px;
    width: 100%;
    height: auto;
    outline: 0;
    border: 1px solid #dbe1e8;
    background: #ffffff url('../img/jquery.chosen/chosen-sprite.png') no-repeat 100% -20px;
    border-radius: 4px;
    font-size: 1em;
    line-height: normal;
}
.chosen-container-single .chosen-drop {
    margin-top: -1px;
    background-clip: padding-box;
}
.chosen-container-single.chosen-container-single-nosearch .chosen-search {
    position: absolute;
    left: -9999px;
}

/* @end */
/* @group Results */
.chosen-container .chosen-results {
    position: relative;
    overflow-x: hidden;
    overflow-y: auto;
    margin: 0 4px 4px 0;
    padding: 0 0 0 4px;
    max-height: 240px;
    -webkit-overflow-scrolling: touch;
}
.chosen-container .chosen-results li {
    display: none;
    margin: 0;
    padding: 5px 6px;
    list-style: none;
    line-height: 15px;
}
.chosen-container .chosen-results li.active-result {
    display: list-item;
    cursor: pointer;
}
.chosen-container .chosen-results li.disabled-result {
    display: list-item;
    color: #ccc;
    cursor: default;
}
.chosen-container .chosen-results li.highlighted {
    background-color: #1bbae1;
    color: #fff;
}
.chosen-container .chosen-results li.no-results {
    display: list-item;
    background: #f4f4f4;
}
.chosen-container .chosen-results li.group-result {
    display: list-item;
    font-weight: bold;
    cursor: default;
}
.chosen-container .chosen-results li.group-option {
    padding-left: 15px;
}
.chosen-container .chosen-results li em {
    font-style: normal;
    text-decoration: underline;
}

/* @end */
/* @group Multi Chosen */
.chosen-container-multi .chosen-choices {
    position: relative;
    overflow: hidden;
    margin: 0;
    padding: 0;
    width: 100%;
    height: auto !important;
    height: 1%;
    border: 1px solid #dbe1e8;
    background-color: #ffffff;
    cursor: text;
    border-radius: 4px;
}
.chosen-container-multi .chosen-choices li {
    float: left;
    list-style: none;
}
.chosen-container-multi .chosen-choices li.search-field {
    margin: 0;
    padding: 0;
    white-space: nowrap;
}
.chosen-container-multi .chosen-choices li.search-field input[type="text"] {
    margin: 1px 0;
    padding: 5px 8px;
    height: 30px;
    outline: 0;
    border: 0 !important;
    background: transparent !important;
    box-shadow: none;
    font-size: 100%;
    line-height: normal;
}
.chosen-container-multi .chosen-choices li.search-field .default {
    color: #999;
}
.chosen-container-multi .chosen-choices li.search-choice {
    position: relative;
    margin: 7px 0 6px 5px;
    padding: 2px 20px 2px 5px;
    background: #f9fafc;
    border: 1px solid #1bbae1;
    background-color: #1bbae1;
    color: #ffffff;
    font-weight: 600;
    font-size: 12px;
    border-radius: 2px;
    line-height: 13px;
    cursor: default;
}
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close {
    position: absolute;
    top: 1px;
    right: 2px;
    display: block;
    color: #ffffff;
    width: 12px;
    height: 12px;
    font-size: 12px;
}
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:before {
    content: "x";
}
.chosen-container-multi .chosen-choices li.search-choice .search-choice-close:hover {
    text-decoration: none;
}
.chosen-container-multi .chosen-choices li.search-choice-disabled {
    padding-right: 5px;
    border: 1px solid #ccc;
    background-color: #e4e4e4;
    color: #666;
}
.chosen-container-multi .chosen-choices li.search-choice-focus {
    background: #d4d4d4;
}
.chosen-container-multi .chosen-choices li.search-choice-focus .search-choice-close {
    background-position: -42px -10px;
}
.chosen-container-multi .chosen-results {
    margin: 0;
    padding: 0;
}
.chosen-container-multi .chosen-drop .result-selected {
    display: list-item;
    color: #ccc;
    cursor: default;
}

/* @end */
/* @group Active  */
.chosen-container-active .chosen-single {
    border: 1px solid #1bbae1;
}
.chosen-container-active.chosen-with-drop .chosen-single {
    border: 1px solid #1bbae1;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.chosen-container-active.chosen-with-drop .chosen-single div {
    border-left: none;
    background: transparent;
}
.chosen-container-active.chosen-with-drop .chosen-single div b {
    background-position: -18px 2px;
}
.chosen-container-active .chosen-choices {
    border: 1px solid #1bbae1;
}
.chosen-container-active.chosen-with-drop .chosen-choices {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.chosen-container-active .chosen-choices li.search-field input[type="text"] {
    color: #111 !important;
}

/* @end */
/* @group Disabled Support */
.chosen-disabled {
    opacity: 0.5 !important;
    cursor: default;
}
.chosen-disabled .chosen-single {
    cursor: default;
}
.chosen-disabled .chosen-choices .search-choice .search-choice-close {
    cursor: default;
}

/* @end */
/* @group Right to Left */
.chosen-rtl {
    text-align: right;
}
.chosen-rtl .chosen-single {
    overflow: visible;
    padding: 0 8px 0 0;
}
.chosen-rtl .chosen-single span {
    margin-right: 0;
    margin-left: 26px;
    direction: rtl;
}
.chosen-rtl .chosen-single-with-deselect span {
    margin-left: 38px;
}
.chosen-rtl .chosen-single div {
    right: auto;
    left: 3px;
}
.chosen-rtl .chosen-single abbr {
    right: auto;
    left: 26px;
}
.chosen-rtl .chosen-choices li {
    float: right;
}
.chosen-rtl .chosen-choices li.search-field input[type="text"] {
    direction: rtl;
}
.chosen-rtl .chosen-choices li.search-choice {
    margin: 3px 5px 3px 0;
    padding: 3px 5px 3px 19px;
}
.chosen-rtl .chosen-choices li.search-choice .search-choice-close {
    right: auto;
    left: 4px;
}
.chosen-rtl.chosen-container-single-nosearch .chosen-search,
.chosen-rtl .chosen-drop {
    left: 9999px;
}
.chosen-rtl.chosen-container-single .chosen-results {
    margin: 0 0 4px 4px;
    padding: 0 4px 0 0;
}
.chosen-rtl .chosen-results li.group-option {
    padding-right: 15px;
    padding-left: 0;
}
.chosen-rtl.chosen-container-active.chosen-with-drop .chosen-single div {
    border-right: none;
}
.chosen-rtl .chosen-search input[type="text"] {
    padding: 4px 5px 4px 20px;
    background: white url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px;
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px, -webkit-gradient(linear, 50% 0%, 50% 100%, color-stop(1%, #eeeeee), color-stop(15%, #ffffff));
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px, -webkit-linear-gradient(#eeeeee 1%, #ffffff 15%);
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px, -moz-linear-gradient(#eeeeee 1%, #ffffff 15%);
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px, -o-linear-gradient(#eeeeee 1%, #ffffff 15%);
    background: url('../img/jquery.chosen/chosen-sprite.png') no-repeat -30px -20px, linear-gradient(#eeeeee 1%, #ffffff 15%);
    direction: rtl;
}
.chosen-rtl.chosen-container-single .chosen-single div b {
    background-position: 6px 2px;
}
.chosen-rtl.chosen-container-single.chosen-with-drop .chosen-single div b {
    background-position: -12px 2px;
}

/* @end */
/* @group Retina compatibility */
@media only screen and (-webkit-min-device-pixel-ratio: 1.5),
only screen and (-moz-min-device-pixel-ratio: 1.5),
only screen and (-o-min-device-pixel-ratio: 3/2),
only screen and (min-device-pixel-ratio: 1.5) {
    .chosen-rtl .chosen-search input[type="text"],
    .chosen-container-single .chosen-single abbr,
    .chosen-container-single .chosen-single div b,
    .chosen-container-single .chosen-search input[type="text"],
    .chosen-container-multi .chosen-choices .search-choice .search-choice-close,
    .chosen-container .chosen-results-scroll-down span,
    .chosen-container .chosen-results-scroll-up span {
        background-image: url('../img/jquery.chosen/<EMAIL>') !important;
        background-size: 52px 37px !important;
        background-repeat: no-repeat !important;
    }
}
/* @end */

/*
=================================================================
(#06dps) Datepicker for Bootstrap
Copyright 2012 Stefan Petre

Licensed under the Apache License v2.0
http://www.apache.org/licenses/LICENSE-2.0
=================================================================
*/

.datepicker {
    padding: 5px;
    direction: ltr;
    z-index: 1051 !important;
}
.datepicker-inline {
    width: 220px;
}
.datepicker.datepicker-rtl {
    direction: rtl;
}
.datepicker.datepicker-rtl table tr td span {
    float: right;
}
.datepicker-dropdown {
    top: 0;
    left: 0;
    padding: 5px !important;
}
.datepicker-dropdown:before {
    content: '';
    display: inline-block;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-top: 0;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    position: absolute;
}
.datepicker-dropdown:after {
    content: '';
    display: inline-block;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #ffffff;
    border-top: 0;
    position: absolute;
}
.datepicker-dropdown.datepicker-orient-left:before {
    left: 6px;
}
.datepicker-dropdown.datepicker-orient-left:after {
    left: 7px;
}
.datepicker-dropdown.datepicker-orient-right:before {
    right: 6px;
}
.datepicker-dropdown.datepicker-orient-right:after {
    right: 7px;
}
.datepicker-dropdown.datepicker-orient-bottom:before {
    top: -7px;
}
.datepicker-dropdown.datepicker-orient-bottom:after {
    top: -6px;
}
.datepicker-dropdown.datepicker-orient-top:before {
    bottom: -7px;
    border-bottom: 0;
    border-top: 7px solid rgba(0, 0, 0, 0.15);
}
.datepicker-dropdown.datepicker-orient-top:after {
    bottom: -6px;
    border-bottom: 0;
    border-top: 6px solid #ffffff;
}
.datepicker > div {
    display: none;
}
.datepicker.days div.datepicker-days {
    display: block;
}
.datepicker.months div.datepicker-months {
    display: block;
}
.datepicker.years div.datepicker-years {
    display: block;
}
.datepicker table {
    margin: 0;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}
.datepicker td,
.datepicker th {
    text-align: center;
    width: 20px;
    height: 20px;
    border: none;
    border-radius: 3px;
}
.table-striped .datepicker table tr td,
.table-striped .datepicker table tr th {
    background-color: transparent;
}
.datepicker table tr td.day:hover {
    background: #eeeeee;
    cursor: pointer;
}
.datepicker table tr td.old,
.datepicker table tr td.new {
    color: #999999;
}
.datepicker table tr td.disabled,
.datepicker table tr td.disabled:hover {
    background: none;
    color: #999999;
    cursor: default;
}
.datepicker table tr td.today,
.datepicker table tr td.today:hover,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today.disabled:hover {
    background-color: #fde19a;
    color: #000;
}
.datepicker table tr td.today:hover,
.datepicker table tr td.today:hover:hover,
.datepicker table tr td.today.disabled:hover,
.datepicker table tr td.today.disabled:hover:hover,
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active,
.datepicker table tr td.today.disabled,
.datepicker table tr td.today:hover.disabled,
.datepicker table tr td.today.disabled.disabled,
.datepicker table tr td.today.disabled:hover.disabled,
.datepicker table tr td.today[disabled],
.datepicker table tr td.today:hover[disabled],
.datepicker table tr td.today.disabled[disabled],
.datepicker table tr td.today.disabled:hover[disabled] {
    background-color: #fdf59a;
}
.datepicker table tr td.today:active,
.datepicker table tr td.today:hover:active,
.datepicker table tr td.today.disabled:active,
.datepicker table tr td.today.disabled:hover:active,
.datepicker table tr td.today.active,
.datepicker table tr td.today:hover.active,
.datepicker table tr td.today.disabled.active,
.datepicker table tr td.today.disabled:hover.active {
    background-color: #fbf069 \9;
}
.datepicker table tr td.today:hover:hover {
    color: #000;
}
.datepicker table tr td.today.active:hover {
    color: #fff;
}
.datepicker table tr td.range,
.datepicker table tr td.range:hover,
.datepicker table tr td.range.disabled,
.datepicker table tr td.range.disabled:hover {
    background: #eeeeee;
    border-radius: 0;
}
.datepicker table tr td.range.today,
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today.disabled:hover {
    background-color: #f3d17a;
}
.datepicker table tr td.range.today:hover,
.datepicker table tr td.range.today:hover:hover,
.datepicker table tr td.range.today.disabled:hover,
.datepicker table tr td.range.today.disabled:hover:hover,
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active,
.datepicker table tr td.range.today.disabled,
.datepicker table tr td.range.today:hover.disabled,
.datepicker table tr td.range.today.disabled.disabled,
.datepicker table tr td.range.today.disabled:hover.disabled,
.datepicker table tr td.range.today[disabled],
.datepicker table tr td.range.today:hover[disabled],
.datepicker table tr td.range.today.disabled[disabled],
.datepicker table tr td.range.today.disabled:hover[disabled] {
    background-color: #f3e97a;
}
.datepicker table tr td.range.today:active,
.datepicker table tr td.range.today:hover:active,
.datepicker table tr td.range.today.disabled:active,
.datepicker table tr td.range.today.disabled:hover:active,
.datepicker table tr td.range.today.active,
.datepicker table tr td.range.today:hover.active,
.datepicker table tr td.range.today.disabled.active,
.datepicker table tr td.range.today.disabled:hover.active {
    background-color: #efe24b \9;
}
.datepicker table tr td.selected,
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected.disabled:hover {
    background-color: #9e9e9e;
    color: #ffffff;
}
.datepicker table tr td.selected:hover,
.datepicker table tr td.selected:hover:hover,
.datepicker table tr td.selected.disabled:hover,
.datepicker table tr td.selected.disabled:hover:hover,
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active,
.datepicker table tr td.selected.disabled,
.datepicker table tr td.selected:hover.disabled,
.datepicker table tr td.selected.disabled.disabled,
.datepicker table tr td.selected.disabled:hover.disabled,
.datepicker table tr td.selected[disabled],
.datepicker table tr td.selected:hover[disabled],
.datepicker table tr td.selected.disabled[disabled],
.datepicker table tr td.selected.disabled:hover[disabled] {
    background-color: #808080;
}
.datepicker table tr td.selected:active,
.datepicker table tr td.selected:hover:active,
.datepicker table tr td.selected.disabled:active,
.datepicker table tr td.selected.disabled:hover:active,
.datepicker table tr td.selected.active,
.datepicker table tr td.selected:hover.active,
.datepicker table tr td.selected.disabled.active,
.datepicker table tr td.selected.disabled:hover.active {
    background-color: #666666 \9;
}
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover {
    background-color: #1bbae1;
    color: #ffffff;
}
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled] {
    background-color: #1bbae1;
}
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active {
    background-color: #003399 \9;
}
.datepicker table tr td span {
    display: block;
    width: 23%;
    height: 54px;
    line-height: 54px;
    float: left;
    margin: 1%;
    cursor: pointer;
}
.datepicker table tr td span:hover {
    background: #eeeeee;
}
.datepicker table tr td span.disabled,
.datepicker table tr td span.disabled:hover {
    background: none;
    color: #999999;
    cursor: default;
}
.datepicker table tr td span.active,
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active.disabled:hover {
    background-color: #006dcc;
    color: #ffffff;
}
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled] {
    background-color: #1bbae1;
}
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active {
    background-color: #003399 \9;
}
.datepicker table tr td span.old,
.datepicker table tr td span.new {
    color: #999999;
}
.datepicker th.datepicker-switch {
    width: 145px;
}
.datepicker thead tr:first-child th,
.datepicker tfoot tr th {
    cursor: pointer;
}
.datepicker thead tr:first-child th:hover,
.datepicker tfoot tr th:hover {
    background: #eeeeee;
}
.datepicker .cw {
    font-size: 10px;
    width: 12px;
    padding: 0 2px 0 5px;
    vertical-align: middle;
}
.datepicker thead tr:first-child th.cw {
    cursor: default;
    background-color: transparent;
}

/*
=================================================================
(#07bcs) Bootstrap Colorpicker

http://mjolnic.github.io/bootstrap-colorpicker/

Originally written by (c) 2012 Stefan Petre
Licensed under the Apache License v2.0
http://www.apache.org/licenses/LICENSE-2.0.txt
=================================================================
*/

.colorpicker-saturation {
    float: left;
    width: 100px;
    height: 100px;
    cursor: crosshair;
    background-image: url("../img/jquery.colorpicker/saturation.png");
}

.colorpicker-saturation i {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 5px;
    height: 5px;
    margin: -4px 0 0 -4px;
    border: 1px solid #000;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}

.colorpicker-saturation i b {
    display: block;
    width: 5px;
    height: 5px;
    border: 1px solid #fff;
    -webkit-border-radius: 5px;
    -moz-border-radius: 5px;
    border-radius: 5px;
}

.colorpicker-hue,
.colorpicker-alpha {
    float: left;
    width: 15px;
    height: 100px;
    margin-bottom: 4px;
    margin-left: 4px;
    cursor: row-resize;
}

.colorpicker-hue i,
.colorpicker-alpha i {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 100%;
    height: 1px;
    margin-top: -1px;
    background: #000;
    border-top: 1px solid #fff;
}

.colorpicker-hue {
    background-image: url("../img/jquery.colorpicker/hue.png");
}

.colorpicker-alpha {
    display: none;
    background-image: url("../img/jquery.colorpicker/alpha.png");
}

.colorpicker {
    top: 0;
    left: 0;
    z-index: 2500;
    min-width: 130px;
    padding: 4px;
    margin-top: 1px;
    -webkit-border-radius: 4px;
    -moz-border-radius: 4px;
    border-radius: 4px;
    *zoom: 1;
}

.colorpicker:before,
.colorpicker:after {
    display: table;
    line-height: 0;
    content: "";
}

.colorpicker:after {
    clear: both;
}

.colorpicker:before {
    position: absolute;
    top: -7px;
    left: 6px;
    display: inline-block;
    border-right: 7px solid transparent;
    border-bottom: 7px solid #ccc;
    border-left: 7px solid transparent;
    border-bottom-color: rgba(0, 0, 0, 0.2);
    content: '';
}

.colorpicker:after {
    position: absolute;
    top: -6px;
    left: 7px;
    display: inline-block;
    border-right: 6px solid transparent;
    border-bottom: 6px solid #ffffff;
    border-left: 6px solid transparent;
    content: '';
}

.colorpicker div {
    position: relative;
}

.colorpicker.colorpicker-with-alpha {
    min-width: 140px;
}

.colorpicker.colorpicker-with-alpha .colorpicker-alpha {
    display: block;
}

.colorpicker-color {
    height: 10px;
    margin-top: 5px;
    clear: both;
    background-image: url("../img/jquery.colorpicker/alpha.png");
    background-position: 0 100%;
}

.colorpicker-color div {
    height: 10px;
}

.colorpicker-element .input-group-addon i,
.colorpicker-element .add-on i {
    display: inline-block;
    width: 16px;
    height: 16px;
    vertical-align: text-top;
    cursor: pointer;
}

.colorpicker.colorpicker-inline {
    position: relative;
    z-index: auto;
    display: inline-block;
    float: none;
}

.colorpicker.colorpicker-horizontal {
    width: 110px;
    height: auto;
    min-width: 110px;
}

.colorpicker.colorpicker-horizontal .colorpicker-saturation {
    margin-bottom: 4px;
}

.colorpicker.colorpicker-horizontal .colorpicker-color {
    width: 100px;
}

.colorpicker.colorpicker-horizontal .colorpicker-hue,
.colorpicker.colorpicker-horizontal .colorpicker-alpha {
    float: left;
    width: 100px;
    height: 15px;
    margin-bottom: 4px;
    margin-left: 0;
    cursor: col-resize;
}

.colorpicker.colorpicker-horizontal .colorpicker-hue i,
.colorpicker.colorpicker-horizontal .colorpicker-alpha i {
    position: absolute;
    top: 0;
    left: 0;
    display: block;
    width: 1px;
    height: 15px;
    margin-top: 0;
    background: #ffffff;
    border: none;
}

.colorpicker.colorpicker-horizontal .colorpicker-hue {
    background-image: url("../img/jquery.colorpicker/hue-horizontal.png");
}

.colorpicker.colorpicker-horizontal .colorpicker-alpha {
    background-image: url("../img/jquery.colorpicker/alpha-horizontal.png");
}

.colorpicker.colorpicker-hidden {
    display: none;
}

.colorpicker.colorpicker-visible {
    display: block;
}

.colorpicker-inline.colorpicker-visible {
    display: inline-block;
}

/*
=================================================================
(#08prs) prism.js okaidia theme for JavaScript, CSS and HTML

Loosely based on Monokai textmate theme by http://www.monokai.nl/
<AUTHOR>
=================================================================
*/

code[class^="language-"],
code[class*=" language-"],
pre[class^="language-"],
pre[class*=" language-"] {
    color: #f8f8f2;
    font-family: Consolas, Monaco, 'Andale Mono', monospace;
    direction: ltr;
    text-align: left;
    white-space: pre;
    word-spacing: normal;

    -moz-tab-size: 4;
    -o-tab-size: 4;
    tab-size: 4;

    -webkit-hyphens: none;
    -moz-hyphens: none;
    -ms-hyphens: none;
    hyphens: none;
}

/* Code blocks */
pre[class^="language-"],
pre[class*=" language-"] {
    padding: 1em;
    margin: 0 0 15px;
    overflow: auto;
    border-radius: 3px;
    border: none;
}

:not(pre) > code[class^="language-"],
:not(pre) > code[class*=" language-"],
pre[class^="language-"],
pre[class*=" language-"] {
    background: #151515;
}

/* Inline code */
:not(pre) > code[class^="language-"],
:not(pre) > code[class*=" language-"] {
    padding: .1em;
    border-radius: .3em;
}

pre code {
    border: 0;
}

.token.comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: slategray;
}

.token.punctuation {
    color: #f8f8f2;
}

.namespace {
    opacity: .7;
}

.token.property,
.token.tag {
    color: #f92672;
}

.token.boolean,
.token.number{
    color: #ae81ff;
}

.token.selector,
.token.attr-name,
.token.string {
    color: #a6e22e;
}


.token.operator,
.token.entity,
.token.url,
.language-css .token.string,
.style .token.string {
    color: #f8f8f2;
}

.token.atrule,
.token.attr-value
{
    color: #e6db74;
}


.token.keyword{
    color: #66d9ef;
}

.token.regex,
.token.important {
    color: #fd971f;
}

.token.important {
    font-weight: bold;
}

.token.entity {
    cursor: help;
}
pre.line-numbers {
    position: relative;
    padding-left: 3.8em;
    counter-reset: linenumber;
}

pre.line-numbers > code {
    position: relative;
}

.line-numbers .line-numbers-rows {
    position: absolute;
    pointer-events: none;
    top: 0;
    font-size: 100%;
    left: -3.8em;
    width: 3em; /* works for line-numbers below 1000 lines */
    letter-spacing: -1px;
    border-right: 1px solid #999;

    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;

}

.line-numbers-rows > span {
    pointer-events: none;
    display: block;
    counter-increment: linenumber;
}

.line-numbers-rows > span:before {
    content: counter(linenumber);
    color: #999;
    display: block;
    padding-right: 0.8em;
    text-align: right;
}

/*
=================================================================
(#09mps) Magnific Popup CSS
=================================================================
*/

.mfp-bg {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1042;
    overflow: hidden;
    position: fixed;
    background: #0b0b0b;
    opacity: 0.8;
    filter: alpha(opacity=80);
}
.mfp-wrap {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1043;
    position: fixed;
    outline: none !important;
    -webkit-backface-visibility: hidden;
}
.mfp-container {
    text-align: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    padding: 0 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.mfp-container:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}
.mfp-align-top .mfp-container:before {
    display: none;
}
.mfp-content {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin: 0 auto;
    text-align: left;
    z-index: 1045;
}
.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
    width: 100%;
    cursor: auto;
}
.mfp-ajax-cur {
    cursor: progress;
}
.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
    cursor: -moz-zoom-out;
    cursor: -webkit-zoom-out;
    cursor: zoom-out;
}
.mfp-zoom {
    cursor: pointer;
    cursor: -webkit-zoom-in;
    cursor: -moz-zoom-in;
    cursor: zoom-in;
}
.mfp-auto-cursor .mfp-content {
    cursor: auto;
}
.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.mfp-loading.mfp-figure {
    display: none;
}
.mfp-hide {
    display: none !important;
}
.mfp-preloader {
    color: #CCC;
    position: absolute;
    top: 50%;
    width: auto;
    text-align: center;
    margin-top: -0.8em;
    left: 8px;
    right: 8px;
    z-index: 1044;
}
.mfp-preloader a {
    color: #CCC;
}
.mfp-preloader a:hover {
    color: #FFF;
}
.mfp-s-ready .mfp-preloader {
    display: none;
}
.mfp-s-error .mfp-content {
    display: none;
}
button.mfp-close,
button.mfp-arrow {
    overflow: visible;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
    display: block;
    outline: none;
    padding: 0;
    z-index: 1046;
    -webkit-box-shadow: none;
    box-shadow: none;
}
button::-moz-focus-inner {
    padding: 0;
    border: 0;
}
.mfp-close {
    width: 44px;
    height: 44px;
    line-height: 44px;
    position: absolute;
    right: 0;
    top: 0;
    text-decoration: none;
    text-align: center;
    opacity: 0.65;
    filter: alpha(opacity=65);
    padding: 0 0 18px 10px;
    color: #FFF;
    font-style: normal;
    font-size: 28px;
    font-family: Arial, Baskerville, monospace;
}
.mfp-close:hover,
.mfp-close:focus {
    opacity: 1;
    filter: alpha(opacity=100);
}
.mfp-close:active {
    top: 1px;
}
.mfp-close-btn-in .mfp-close {
    color: #333;
}
.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
    color: #FFF;
    right: -6px;
    text-align: right;
    padding-right: 6px;
    width: 100%;
}
.mfp-counter {
    position: absolute;
    top: 0;
    right: 0;
    color: #CCC;
    font-size: 12px;
    line-height: 18px;
    white-space: nowrap;
}
.mfp-arrow {
    position: absolute;
    opacity: 0.65;
    filter: alpha(opacity=65);
    margin: 0;
    top: 50%;
    margin-top: -55px;
    padding: 0;
    width: 90px;
    height: 110px;
    -webkit-tap-highlight-color: transparent;
}
.mfp-arrow:active {
    margin-top: -54px;
}
.mfp-arrow:hover,
.mfp-arrow:focus {
    opacity: 1;
    filter: alpha(opacity=100);
}
.mfp-arrow:before,
.mfp-arrow:after,
.mfp-arrow .mfp-b,
.mfp-arrow .mfp-a {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    margin-top: 35px;
    margin-left: 35px;
    border: medium inset transparent;
}
.mfp-arrow:after,
.mfp-arrow .mfp-a {
    border-top-width: 13px;
    border-bottom-width: 13px;
    top: 8px;
}
.mfp-arrow:before,
.mfp-arrow .mfp-b {
    border-top-width: 21px;
    border-bottom-width: 21px;
    opacity: 0.7;
}
.mfp-arrow-left {
    left: 0;
}
.mfp-arrow-left:after,
.mfp-arrow-left .mfp-a {
    border-right: 17px solid #FFF;
    margin-left: 31px;
}
.mfp-arrow-left:before,
.mfp-arrow-left .mfp-b {
    margin-left: 25px;
    border-right: 27px solid #3F3F3F;
}
.mfp-arrow-right {
    right: 0;
}
.mfp-arrow-right:after,
.mfp-arrow-right .mfp-a {
    border-left: 17px solid #FFF;
    margin-left: 39px;
}
.mfp-arrow-right:before,
.mfp-arrow-right .mfp-b {
    border-left: 27px solid #3F3F3F;
}
.mfp-iframe-holder {
    padding-top: 40px;
    padding-bottom: 40px;
}
.mfp-iframe-holder .mfp-content {
    line-height: 0;
    width: 100%;
    max-width: 900px;
}
.mfp-iframe-holder .mfp-close {
    top: -40px;
}
.mfp-iframe-scaler {
    width: 100%;
    height: 0;
    overflow: hidden;
    padding-top: 56.25%;
}
.mfp-iframe-scaler iframe {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #000;
}
/* Main image in popup */

img.mfp-img {
    width: auto;
    max-width: 100%;
    height: auto;
    display: block;
    line-height: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 40px 0 40px;
    margin: 0 auto;
}
/* The shadow behind the image */

.mfp-figure {
    line-height: 0;
}
.mfp-figure:after {
    content: '';
    position: absolute;
    left: 0;
    top: 40px;
    bottom: 40px;
    display: block;
    right: 0;
    width: auto;
    height: auto;
    z-index: -1;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #444;
}
.mfp-figure small {
    color: #BDBDBD;
    display: block;
    font-size: 12px;
    line-height: 14px;
}
.mfp-figure figure {
    margin: 0;
}
.mfp-bottom-bar {
    margin-top: -36px;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    cursor: auto;
}
.mfp-title {
    text-align: left;
    line-height: 18px;
    color: #F3F3F3;
    word-wrap: break-word;
    padding-right: 36px;
}
.mfp-image-holder .mfp-content {
    max-width: 100%;
}
.mfp-gallery .mfp-image-holder .mfp-figure {
    cursor: pointer;
}
@media screen and (max-width: 800px) and (orientation: landscape),
screen and (max-height: 300px) {
    /**
       * Remove all paddings around the image on small screen
       */

    .mfp-img-mobile .mfp-image-holder {
        padding-left: 0;
        padding-right: 0;
    }
    .mfp-img-mobile img.mfp-img {
        padding: 0;
    }
    .mfp-img-mobile .mfp-figure:after {
        top: 0;
        bottom: 0;
    }
    .mfp-img-mobile .mfp-figure small {
        display: inline;
        margin-left: 5px;
    }
    .mfp-img-mobile .mfp-bottom-bar {
        background: rgba(0, 0, 0, 0.6);
        bottom: 0;
        margin: 0;
        top: auto;
        padding: 3px 5px;
        position: fixed;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
    .mfp-img-mobile .mfp-bottom-bar:empty {
        padding: 0;
    }
    .mfp-img-mobile .mfp-counter {
        right: 5px;
        top: 3px;
    }
    .mfp-img-mobile .mfp-close {
        top: 0;
        right: 0;
        width: 35px;
        height: 35px;
        line-height: 35px;
        background: rgba(0, 0, 0, 0.6);
        position: fixed;
        text-align: center;
        padding: 0;
    }
}
@media all and (max-width: 900px) {
    .mfp-arrow {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }
    .mfp-arrow-left {
        -webkit-transform-origin: 0;
        transform-origin: 0;
    }
    .mfp-arrow-right {
        -webkit-transform-origin: 100%;
        transform-origin: 100%;
    }
    .mfp-container {
        padding-left: 6px;
        padding-right: 6px;
    }
}
.mfp-ie7 .mfp-img {
    padding: 0;
}
.mfp-ie7 .mfp-bottom-bar {
    width: 600px;
    left: 50%;
    margin-left: -300px;
    margin-top: 5px;
    padding-bottom: 5px;
}
.mfp-ie7 .mfp-container {
    padding: 0;
}
.mfp-ie7 .mfp-content {
    padding-top: 44px;
}
.mfp-ie7 .mfp-close {
    top: 0;
    right: 0;
    padding-top: 0;
}

/*
=================================================================
(#10dts) Datatables
=================================================================
*/

.dataTables_wrapper > div {
    background-color: #f9fafc;
    padding: 8px 0 5px;
    width: auto;
    border: 1px solid #eaedf1;
    border-top-width: 0;
}

.dataTables_wrapper > div:first-child {
    border-top-width: 1px;
    border-bottom-width: 0;
}

.dataTables_wrapper .row {
    margin: 0;
}

.dataTables_filter label,
.dataTables_length label,
.dataTables_info,
.dataTables_paginate {
    margin: 0;
    padding: 0;
}

.dataTables_filter label {
    font-weight: normal;
    float: left;
    text-align: left;
}

div.dataTables_length select {
    width: 75px;
}

div.dataTables_filter label {
    font-weight: normal;
    float: right;
}

div.dataTables_filter input {
    width: 150px;
}

.dataTables_info {
    padding-top: 8px;
}

.dataTables_paginate {
    float: right;
    margin: 0;
}

div.dataTables_paginate ul.pagination {
    margin: 2px 0;
    white-space: nowrap;
}

table.dataTable td,
table.dataTable th {
    -webkit-box-sizing: content-box;
    -moz-box-sizing: content-box;
    box-sizing: content-box;
}

table.dataTable {
    clear: both;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
    max-width: none !important;
}

table.table thead .sorting,
table.table thead .sorting_asc,
table.table thead .sorting_desc,
table.table thead .sorting_asc_disabled,
table.table thead .sorting_desc_disabled {
    cursor: pointer;
    *cursor: hand;
    padding-right: 20px;
}

.table thead .sorting,
.table thead .sorting_asc,
.table thead .sorting_desc,
.table thead .sorting_asc_disabled,
.table thead .sorting_desc_disabled {
    background-position: center right;
    background-repeat: no-repeat;
    background-size: 19px 19px;
}

.table thead .sorting { background-image: url('../img/jquery.datatables/sort_both.png'); }
.table thead .sorting_asc { background-image: url('../img/jquery.datatables/sort_asc.png'); }
.table thead .sorting_desc { background-image: url('../img/jquery.datatables/sort_desc.png'); }
.table thead .sorting_asc_disabled { background-image: url('../img/jquery.datatables/sort_asc_disabled.png'); }
.table thead .sorting_desc_disabled { background-image: url('../img/jquery.datatables/sort_desc_disabled.png'); }

table.dataTable thead > tr > th {
    padding-left: 18px;
    padding-right: 18px;
}

table.dataTable th:active {
    outline: none;
}

div.dataTables_scrollHead table {
    margin-bottom: 0 !important;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

div.dataTables_scrollHead table thead tr:last-child th:first-child,
div.dataTables_scrollHead table thead tr:last-child td:first-child {
    border-bottom-left-radius: 0 !important;
    border-bottom-right-radius: 0 !important;
}

div.dataTables_scrollBody table {
    border-top: none;
    margin-top: 0 !important;
    margin-bottom: 0 !important;
}

div.dataTables_scrollBody tbody tr:first-child th,
div.dataTables_scrollBody tbody tr:first-child td {
    border-top: none;
}

div.dataTables_scrollFoot table {
    margin-top: 0 !important;
    border-top: none;
}

div.dataTables_scrollHead table.table-bordered {
    border-bottom-width: 0;
}

@media only screen and (-Webkit-min-device-pixel-ratio: 1.5),
only screen and (-moz-min-device-pixel-ratio: 1.5),
only screen and (-o-min-device-pixel-ratio: 3/2),
only screen and (min-device-pixel-ratio: 1.5) {

    .table thead .sorting { background-image: url('../img/jquery.datatables/<EMAIL>'); }
    .table thead .sorting_asc { background-image: url('../img/jquery.datatables/<EMAIL>'); }
    .table thead .sorting_desc { background-image: url('../img/jquery.datatables/<EMAIL>'); }
    .table thead .sorting_asc_disabled { background-image: url('../img/jquery.datatables/<EMAIL>'); }
    .table thead .sorting_desc_disabled { background-image: url('../img/jquery.datatables/<EMAIL>'); }
}

/*
==============================================
(#11eps) Easy Pie Chart
==============================================
*/

.easyPieChart {
    position: relative;
    text-align: center;
    margin: 0 auto;
    font-size: 24px;
    font-weight: 300;
}

.easyPieChart small {
    font-size: 14px;
}

.easyPieChart canvas {
    position: absolute;
    top: 0;
    left: 0;
}

/*
==============================================
(#12cas) CSS3 ANIMATION CHEAT SHEET

Made by Justin Aguilar

www.justinaguilar.com/animations/

Questions, comments, concerns, love letters:
<EMAIL>
==============================================
*/

/*
==============================================
slideDown
==============================================
*/

.animation-slideDown {
    animation-name: slideDown;
    -webkit-animation-name: slideDown;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes slideDown {
    0% {
        transform: translateY(-100%);
    }
    50%{
        transform: translateY(8%);
    }
    65%{
        transform: translateY(-4%);
    }
    80%{
        transform: translateY(4%);
    }
    95%{
        transform: translateY(-2%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes slideDown {
    0% {
        -webkit-transform: translateY(-100%);
    }
    50%{
        -webkit-transform: translateY(8%);
    }
    65%{
        -webkit-transform: translateY(-4%);
    }
    80%{
        -webkit-transform: translateY(4%);
    }
    95%{
        -webkit-transform: translateY(-2%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
slideUp
==============================================
*/

.animation-slideUp {
    animation-name: slideUp;
    -webkit-animation-name: slideUp;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes slideUp {
    0% {
        transform: translateY(100%);
    }
    50%{
        transform: translateY(-8%);
    }
    65%{
        transform: translateY(4%);
    }
    80%{
        transform: translateY(-4%);
    }
    95%{
        transform: translateY(2%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes slideUp {
    0% {
        -webkit-transform: translateY(100%);
    }
    50%{
        -webkit-transform: translateY(-8%);
    }
    65%{
        -webkit-transform: translateY(4%);
    }
    80%{
        -webkit-transform: translateY(-4%);
    }
    95%{
        -webkit-transform: translateY(2%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
slideLeft
==============================================
*/

.animation-slideLeft {
    animation-name: slideLeft;
    -webkit-animation-name: slideLeft;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes slideLeft {
    0% {
        transform: translateX(150%);
    }
    50%{
        ransform: translateX(-8%);
    }
    65%{
        transform: translateX(4%);
    }
    80%{
        transform: translateX(-4%);
    }
    95%{
        transform: translateX(2%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideLeft {
    0% {
        -webkit-transform: translateX(150%);
    }
    50%{
        -webkit-transform: translateX(-8%);
    }
    65%{
        -webkit-transform: translateX(4%);
    }
    80%{
        -webkit-transform: translateX(-4%);
    }
    95%{
        -webkit-transform: translateX(2%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
slideRight
==============================================
*/

.animation-slideRight {
    animation-name: slideRight;
    -webkit-animation-name: slideRight;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes slideRight {
    0% {
        transform: translateX(-150%);
    }
    50%{
        transform: translateX(8%);
    }
    65%{
        transform: translateX(-4%);
    }
    80%{
        transform: translateX(4%);
    }
    95%{
        transform: translateX(-2%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideRight {
    0% {
        -webkit-transform: translateX(-150%);
    }
    50%{
        -webkit-transform: translateX(8%);
    }
    65%{
        -webkit-transform: translateX(-4%);
    }
    80%{
        -webkit-transform: translateX(4%);
    }
    95%{
        -webkit-transform: translateX(-2%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
slideExpandUp
==============================================
*/

.animation-slideExpandUp {
    animation-name: slideExpandUp;
    -webkit-animation-name: slideExpandUp;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease -out;
    visibility: visible !important;
}

@keyframes slideExpandUp {
    0% {
        transform: translateY(100%) scaleX(0.5);
    }
    30%{
        transform: translateY(-8%) scaleX(0.5);
    }
    40%{
        transform: translateY(2%) scaleX(0.5);
    }
    50%{
        transform: translateY(0%) scaleX(1.1);
    }
    60%{
        transform: translateY(0%) scaleX(0.9);
    }
    70% {
        transform: translateY(0%) scaleX(1.05);
    }
    80%{
        transform: translateY(0%) scaleX(0.95);
    }
    90% {
        transform: translateY(0%) scaleX(1.02);
    }
    100%{
        transform: translateY(0%) scaleX(1);
    }
}

@-webkit-keyframes slideExpandUp {
    0% {
        -webkit-transform: translateY(100%) scaleX(0.5);
    }
    30%{
        -webkit-transform: translateY(-8%) scaleX(0.5);
    }
    40%{
        -webkit-transform: translateY(2%) scaleX(0.5);
    }
    50%{
        -webkit-transform: translateY(0%) scaleX(1.1);
    }
    60%{
        -webkit-transform: translateY(0%) scaleX(0.9);
    }
    70% {
        -webkit-transform: translateY(0%) scaleX(1.05);
    }
    80%{
        -webkit-transform: translateY(0%) scaleX(0.95);
    }
    90% {
        -webkit-transform: translateY(0%) scaleX(1.02);
    }
    100%{
        -webkit-transform: translateY(0%) scaleX(1);
    }
}

/*
==============================================
expandUp
==============================================
*/

.animation-expandUp {
    animation-name: expandUp;
    -webkit-animation-name: expandUp;
    animation-duration: 0.7s;
    -webkit-animation-duration: 0.7s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes expandUp {
    0% {
        transform: translateY(100%) scale(0.6) scaleY(0.5);
    }
    60%{
        transform: translateY(-7%) scaleY(1.12);
    }
    75%{
        transform: translateY(3%);
    }
    100% {
        transform: translateY(0%) scale(1) scaleY(1);
    }
}

@-webkit-keyframes expandUp {
    0% {
        -webkit-transform: translateY(100%) scale(0.6) scaleY(0.5);
    }
    60%{
        -webkit-transform: translateY(-7%) scaleY(1.12);
    }
    75%{
        -webkit-transform: translateY(3%);
    }
    100% {
        -webkit-transform: translateY(0%) scale(1) scaleY(1);
    }
}

/*
==============================================
fadeIn
==============================================
*/

.animation-fadeIn {
    animation-name: fadeIn;
    -webkit-animation-name: fadeIn;
    animation-duration: 1.0s;
    -webkit-animation-duration: 1.0s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes fadeIn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeIn {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
expandOpen
==============================================
*/

.animation-expandOpen {
    animation-name: expandOpen;
    -webkit-animation-name: expandOpen;
    animation-duration: 1.2s;
    -webkit-animation-duration: 1.2s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes expandOpen {
    0% {
        transform: scale(1.8);
    }
    50% {
        transform: scale(0.95);
    }
    80% {
        transform: scale(1.05);
    }
    90% {
        transform: scale(0.98);
    }
    100% {
        transform: scale(1);
    }
}

@-webkit-keyframes expandOpen {
    0% {
        -webkit-transform: scale(1.8);
    }
    50% {
        -webkit-transform: scale(0.95);
    }
    80% {
        -webkit-transform: scale(1.05);
    }
    90% {
        -webkit-transform: scale(0.98);
    }
    100% {
        -webkit-transform: scale(1);
    }
}

/*
==============================================
bigEntrance
==============================================
*/

.animation-bigEntrance {
    animation-name: bigEntrance;
    -webkit-animation-name: bigEntrance;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes bigEntrance {
    0% {
        transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
        opacity: 0.2;
    }
    30% {
        transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
        opacity: 1;
    }
    45% {
        transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    60% {
        transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    75% {
        transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    90% {
        transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
}

@-webkit-keyframes bigEntrance {
    0% {
        -webkit-transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
        opacity: 0.2;
    }
    30% {
        -webkit-transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
        opacity: 1;
    }
    45% {
        -webkit-transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    60% {
        -webkit-transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    75% {
        -webkit-transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    90% {
        -webkit-transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
}

/*
==============================================
hatch
==============================================
*/

.animation-hatch {
    animation-name: hatch;
    -webkit-animation-name: hatch;
    animation-duration: 2s;
    -webkit-animation-duration: 2s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
    visibility: visible !important;
}

@keyframes hatch {
    0% {
        transform: rotate(0deg) scaleY(0.6);
    }
    20% {
        transform: rotate(-2deg) scaleY(1.05);
    }
    35% {
        transform: rotate(2deg) scaleY(1);
    }
    50% {
        transform: rotate(-2deg);
    }
    65% {
        transform: rotate(1deg);
    }
    80% {
        transform: rotate(-1deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

@-webkit-keyframes hatch {
    0% {
        -webkit-transform: rotate(0deg) scaleY(0.6);
    }
    20% {
        -webkit-transform: rotate(-2deg) scaleY(1.05);
    }
    35% {
        -webkit-transform: rotate(2deg) scaleY(1);
    }
    50% {
        -webkit-transform: rotate(-2deg);
    }
    65% {
        -webkit-transform: rotate(1deg);
    }
    80% {
        -webkit-transform: rotate(-1deg);
    }
    100% {
        -webkit-transform: rotate(0deg);
    }
}

/*
==============================================
bounce
==============================================
*/

.animation-bounce {
    animation-name: bounce;
    -webkit-animation-name: bounce;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
}

@keyframes bounce {
    0% {
        transform: translateY(0%) scaleY(0.6);
    }
    60%{
        transform: translateY(-100%) scaleY(1.1);
    }
    70%{
        transform: translateY(0%) scaleY(0.95) scaleX(1.05);
    }
    80%{
        transform: translateY(0%) scaleY(1.05) scaleX(1);
    }
    90%{
        transform: translateY(0%) scaleY(0.95) scaleX(1);
    }
    100%{
        transform: translateY(0%) scaleY(1) scaleX(1);
    }
}

@-webkit-keyframes bounce {
    0% {
        -webkit-transform: translateY(0%) scaleY(0.6);
    }
    60%{
        -webkit-transform: translateY(-100%) scaleY(1.1);
    }
    70%{
        -webkit-transform: translateY(0%) scaleY(0.95) scaleX(1.05);
    }
    80%{
        -webkit-transform: translateY(0%) scaleY(1.05) scaleX(1);
    }
    90%{
        -webkit-transform: translateY(0%) scaleY(0.95) scaleX(1);
    }
    100%{
        -webkit-transform: translateY(0%) scaleY(1) scaleX(1);
    }
}

/*
==============================================
pulse
==============================================
*/

.animation-pulse {
    animation-name: pulse;
    -webkit-animation-name: pulse;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.9);
        opacity: 0.7;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0.7;
    }
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(0.95);
        opacity: 0.7;
    }
}

/*
==============================================
floating
==============================================
*/

.animation-floating {
    animation-name: floating;
    -webkit-animation-name: floating;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes floating {
    0% {
        transform: translateY(0%);
    }
    50% {
        transform: translateY(8%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes floating {
    0% {
        -webkit-transform: translateY(0%);
    }
    50% {
        -webkit-transform: translateY(8%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
tossing
==============================================
*/

.animation-tossing {
    animation-name: tossing;
    -webkit-animation-name: tossing;
    animation-duration: 2.5s;
    -webkit-animation-duration: 2.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes tossing {
    0% {
        transform: rotate(-4deg);
    }
    50% {
        transform: rotate(4deg);
    }
    100% {
        transform: rotate(-4deg);
    }
}

@-webkit-keyframes tossing {
    0% {
        -webkit-transform: rotate(-4deg);
    }
    50% {
        -webkit-transform: rotate(4deg);
    }
    100% {
        -webkit-transform: rotate(-4deg);
    }
}

/*
==============================================
pullUp
==============================================
*/

.animation-pullUp {
    animation-name: pullUp;
    -webkit-animation-name: pullUp;
    animation-duration: 1.1s;
    -webkit-animation-duration: 1.1s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
}

@keyframes pullUp {
    0% {
        transform: scaleY(0.1);
    }
    40% {
        transform: scaleY(1.02);
    }
    60% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(1);
    }
}

@-webkit-keyframes pullUp {
    0% {
        -webkit-transform: scaleY(0.1);
    }
    40% {
        -webkit-transform: scaleY(1.02);
    }
    60% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(1);
    }
}

/*
==============================================
pullDown
==============================================
*/

.animation-pullDown {
    animation-name: pullDown;
    -webkit-animation-name: pullDown;
    animation-duration: 1.1s;
    -webkit-animation-duration: 1.1s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 50% 0%;
    -ms-transform-origin: 50% 0%;
    -webkit-transform-origin: 50% 0%;
}

@keyframes pullDown {
    0% {
        transform: scaleY(0.1);
    }
    40% {
        transform: scaleY(1.02);
    }
    60% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(1);
    }
}

@-webkit-keyframes pullDown {
    0% {
        -webkit-transform: scaleY(0.1);
    }
    40% {
        -webkit-transform: scaleY(1.02);
    }
    60% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(1);
    }
}

/*
==============================================
stretchLeft
==============================================
*/

.animation-stretchLeft {
    animation-name: stretchLeft;
    -webkit-animation-name: stretchLeft;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 100% 0%;
    -ms-transform-origin: 100% 0%;
    -webkit-transform-origin: 100% 0%;
}

@keyframes stretchLeft {
    0% {
        transform: scaleX(0.3);
    }
    40% {
        transform: scaleX(1.02);
    }
    60% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(1);
    }
}

@-webkit-keyframes stretchLeft {
    0% {
        -webkit-transform: scaleX(0.3);
    }
    40% {
        -webkit-transform: scaleX(1.02);
    }
    60% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(1);
    }
}

/*
==============================================
stretchRight
==============================================
*/

.animation-stretchRight {
    animation-name: stretchRight;
    -webkit-animation-name: stretchRight;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 0% 0%;
    -ms-transform-origin: 0% 0%;
    -webkit-transform-origin: 0% 0%;
}

@keyframes stretchRight {
    0% {
        transform: scaleX(0.3);
    }
    40% {
        transform: scaleX(1.02);
    }
    60% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(1);
    }
}

@-webkit-keyframes stretchRight {
    0% {
        -webkit-transform: scaleX(0.3);
    }
    40% {
        -webkit-transform: scaleX(1.02);
    }
    60% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(1);
    }
}

/* Extend with more animations */

/*
==============================================
pulseSlow
==============================================
*/

.animation-pulseSlow {
    animation-name: pulseSlow;
    -webkit-animation-name: pulseSlow;
    animation-duration: 30s;
    -webkit-animation-duration: 30s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    animation-timing-function: linear;
    -webkit-animation-timing-function: linear;
}

@keyframes pulseSlow {
    0% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}

@-webkit-keyframes pulseSlow {
    0% {
        -webkit-transform: scale(1.1);
    }
    50% {
        -webkit-transform: scale(1);
    }
    100% {
        -webkit-transform: scale(1.1);
    }
}

/*
==============================================
floatingHor
==============================================
*/

.animation-floatingHor {
    animation-name: floatingHor;
    -webkit-animation-name: floatingHor;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes floatingHor {
    0% {
        transform: translateX(0%);
    }
    50% {
        transform: translateX(8%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes floatingHor {
    0% {
        -webkit-transform: translateX(0%);
    }
    50% {
        -webkit-transform: translateX(8%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
fadeInQuick
==============================================
*/

.animation-fadeInQuick {
    animation-name: fadeInQuick;
    -webkit-animation-name: fadeInQuick;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInQuick {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInQuick {
    0% {
        -webkit-transform: scale(0.9);
        opacity: 0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
fadeInQuickInv
==============================================
*/

.animation-fadeInQuickInv {
    animation-name: fadeInQuickInv;
    -webkit-animation-name: fadeInQuickInv;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInQuickInv {
    0% {
        transform: scale(1.1);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInQuickInv {
    0% {
        -webkit-transform: scale(1.1);
        opacity: 0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
fadeIn360
==============================================
*/

.animation-fadeIn360 {
    animation-name: fadeIn360;
    -webkit-animation-name: fadeIn360;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeIn360 {
    0% {
        transform: rotate(0deg) scale(1.3);
        opacity: 0;
    }
    100% {
        transform: rotate(360deg) scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeIn360 {
    0% {
        -webkit-transform: rotate(0deg) scale(1.3);
        opacity: 0;
    }
    100% {
        -webkit-transform: rotate(360deg) scale(1);
        opacity: 1;
    }
}

/*
==============================================
FadeInRight
==============================================
*/

.animation-fadeInRight {
    animation-name: fadeInRight;
    -webkit-animation-name: fadeInRight;
    animation-duration: 0.75s;
    -webkit-animation-duration: 0.75s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInRight {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0%);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInRight {
    0% {
        -webkit-transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0%);
        opacity: 1;
    }
}

/*
==============================================
FadeInLeft
==============================================
*/

.animation-fadeInLeft {
    animation-name: fadeInLeft;
    -webkit-animation-name: fadeInLeft;
    animation-duration: 0.75s;
    -webkit-animation-duration: 0.75s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInLeft {
    0% {
        transform: translateX(+100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0%);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInLeft {
    0% {
        -webkit-transform: translateX(+100%);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0%);
        opacity: 1;
    }
}

/*
==============================================
(#13tps) Timepicker Component for Twitter Bootstrap

Copyright 2013 Joris de Wit

Contributors https://github.com/jdewit/bootstrap-timepicker/graphs/contributors
==============================================
*/

.bootstrap-timepicker {
    position: relative;
}
.bootstrap-timepicker input {
    border-top-left-radius: 4px !important;
    border-bottom-left-radius: 4px !important;
}
.bootstrap-timepicker .input-group-addon {
    cursor: pointer;
}
.bootstrap-timepicker.pull-right .bootstrap-timepicker-widget.dropdown-menu {
    left: auto;
    right: 0;
}
.bootstrap-timepicker.pull-right .bootstrap-timepicker-widget.dropdown-menu:before {
    left: auto;
    right: 12px;
}
.bootstrap-timepicker.pull-right .bootstrap-timepicker-widget.dropdown-menu:after {
    left: auto;
    right: 13px;
}
.bootstrap-timepicker-widget.dropdown-menu {
    padding: 2px 3px 2px 2px;
}
.bootstrap-timepicker-widget.dropdown-menu.open {
    display: inline-block;
}
.bootstrap-timepicker-widget.dropdown-menu:before {
    border-bottom: 7px solid rgba(0, 0, 0, 0.2);
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    content: "";
    display: inline-block;
    left: 9px;
    position: absolute;
    top: -7px;
}
.bootstrap-timepicker-widget.dropdown-menu:after {
    border-bottom: 6px solid #FFFFFF;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    content: "";
    display: inline-block;
    left: 10px;
    position: absolute;
    top: -6px;
}
.bootstrap-timepicker-widget a.btn,
.bootstrap-timepicker-widget input {
    border-radius: 4px;
}
.bootstrap-timepicker-widget table {
    width: 100%;
    margin: 0;
}
.bootstrap-timepicker-widget table td {
    text-align: center;
    height: 30px;
    margin: 0;
    padding: 2px;
}
.bootstrap-timepicker-widget table td:not(.separator) {
    min-width: 30px;
}
.bootstrap-timepicker-widget table td span {
    width: 100%;
}
.bootstrap-timepicker-widget table td a {
    width: 100%;
    display: inline-block;
    margin: 0;
    padding: 8px 0;
    outline: 0;
    color: #000;
    border-radius: 3px;
}
.bootstrap-timepicker-widget table td a:hover {
    text-decoration: none;
    background-color: #1bbae1;
    color: #ffffff;
}
.bootstrap-timepicker-widget table td a i {
    margin-top: 2px;
    font-size: 18px;
}
.bootstrap-timepicker-widget table td input {
    width: 25px;
    margin: 0;
    text-align: center;
}
.bootstrap-timepicker-widget .modal-content {
    padding: 4px;
}
@media (min-width: 767px) {
    .bootstrap-timepicker-widget.modal {
        width: 200px;
        margin-left: -100px;
    }
}
@media (max-width: 767px) {
    .bootstrap-timepicker {
        width: 100%;
    }
    .bootstrap-timepicker .dropdown-menu {
        width: 100%;
    }
}

/*
==============================================
(#14tis) Jquery Tags Input
==============================================
*/

div.tagsinput {
    background: #ffffff;
    width: 100%;
    height: auto;
    padding: 6px 8px 0;
    border: 1px solid #dbe1e8;
    border-radius: 4px;
}

div.tagsinput span.tag {
    border: 1px solid #1bbae1;
    background-color: #1bbae1;
    color: #ffffff;
    font-weight: 600;
    border-radius: 2px;
    display: block;
    float: left;
    padding: 0 20px 0 5px;
    height: 20px;
    line-height: 18px;
    text-decoration: none;
    margin-right: 4px;
    margin-bottom: 6px;
    font-size: 12px;
    position: relative;
}

div.tagsinput span.tag a {
    position: absolute;
    color: #ffffff;
    display: block;
    top: 0;
    right: 5px;
    font-weight: bold;
    text-decoration: none;
    font-size: 12px;
    line-height: 16px;
    height: 20px;
    width: 10px;
    text-align: center;
}

div.tagsinput span.tag a,
div.tagsinput span.tag a:hover,
div.tagsinput span.tag a:focus {
    color: #ffffff;
    text-decoration: none;
}

div.tagsinput input {
    width: 80px;
    margin: 0px;
    font-family: helvetica;
    font-size: 12px;
    border: 1px solid transparent;
    padding: 0 5px;
    height: 20px;
    line-height: 20px;
    background: transparent;
    outline: 0;
    margin-right: 4px;
    margin-bottom: 6px;
}

div.tagsinput div {
    display: block;
    float: left;
}

div.tagsinput:before,
div.tagsinput:after {
    content:" ";
    display:table;
}

div.tagsinput:after {
    clear:both;
}

.not_valid {
    background: #fbd8db !important;
    color: #90111a !important;
}

/*
==============================================
(#15sbs) Slider for Bootstrap

Copyright 2012 Stefan Petre
Licensed under the Apache License v2.0
http://www.apache.org/licenses/LICENSE-2.0
==============================================
*/

.slider {
    display: inline-block;
    vertical-align: middle;
    position: relative;
    margin: 1px 0;
}

.slider.slider-horizontal {
    width: 100% !important;
    height: 34px;
}

.slider.slider-horizontal .slider-track {
    height: 6px;
    width: 100%;
    margin-top: -8px;
    top: 50%;
    left: 0;
}

.slider.slider-horizontal .slider-selection,
.slider.slider-horizontal .slider-track-low,
.slider.slider-horizontal .slider-track-high {
  height: 100%;
  top: 0;
  bottom: 0;
}

.slider.slider-horizontal .slider-tick,
.slider.slider-horizontal .slider-handle {
    margin-left: -12px;
    margin-top: -9px;
}

.slider.slider-horizontal .slider-tick.triangle,
.slider.slider-horizontal .slider-handle.triangle {
    border-width: 0 10px 10px 10px;
    width: 0;
    height: 0;
    border-color: transparent;
    border-bottom-color: #ffffff;
    margin-top: 0;
}

.slider.slider-horizontal .slider-tick-label-container {
    white-space: nowrap;
    margin-top: 20px;
}

.slider.slider-horizontal .slider-tick-label-container .slider-tick-label {
    padding-top: 4px;
    display: inline-block;
    text-align: center;
}

.slider.slider-vertical {
    height: 210px;
    width: 34px;
}

.slider.slider-vertical .slider-track {
    width: 6px;
    height: 100%;
    margin-left: -8px;
    left: 50%;
    top: 0;
}

.slider.slider-vertical .slider-selection {
    width: 100%;
    left: 0;
    top: 0;
    bottom: 0;
}

.slider.slider-vertical .slider-track-low,
.slider.slider-vertical .slider-track-high {
    width: 100%;
    left: 0;
    right: 0;
}

.slider.slider-vertical .slider-tick,
.slider.slider-vertical .slider-handle {
    margin-left: -9px;
    margin-top: -14px;
}

.slider.slider-vertical .slider-tick.triangle,
.slider.slider-vertical .slider-handle.triangle {
    border-width: 10px 0 10px 10px;
    width: 1px;
    height: 1px;
    border-color: transparent;
    border-left-color: #ffffff;
    margin-left: 0;
}

.slider.slider-disabled .slider-handle {
    background-image: -webkit-linear-gradient(top, #dfdfdf 0%, #bebebe 100%);
    background-image: -o-linear-gradient(top, #dfdfdf 0%, #bebebe 100%);
    background-image: linear-gradient(to bottom, #dfdfdf 0%, #bebebe 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffdfdfdf', endColorstr='#ffbebebe', GradientType=0);
}

.slider.slider-disabled .slider-track {
    background-image: -webkit-linear-gradient(top, #e5e5e5 0%, #e9e9e9 100%);
    background-image: -o-linear-gradient(top, #e5e5e5 0%, #e9e9e9 100%);
    background-image: linear-gradient(to bottom, #e5e5e5 0%, #e9e9e9 100%);
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffe5e5e5', endColorstr='#ffe9e9e9', GradientType=0);
    cursor: not-allowed;
}

.slider input {
    display: none;
}

.slider .tooltip.top {
    margin-top: -36px;
}

.slider .tooltip-inner {
    white-space: nowrap;
}

.slider .hide {
    display: none;
}

.slider-track {
    position: absolute;
    cursor: pointer;
    background-color: #eaedf1;
    border-radius: 4px;
}

.slider-selection {
    position: absolute;
    background-color: #1bbae1;
    border-radius: 4px;
}

.slider-selection.tick-slider-selection {
    background-color: #1bbae1;
}

.slider-track-low,
.slider-track-high {
    position: absolute;
    background: transparent;
    border-radius: 4px;
}

.slider-handle {
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: #ffffff;
    border: 1px solid #aaaaaa;
    -webkit-box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

.slider-handle.round {
    border-radius: 50%;
}

.slider-handle.triangle {
    background: transparent none;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.slider-handle:focus {
    border-color: #333333;
    -webkit-box-shadow: none;
    box-shadow: 0 0 5px rgba(0, 0, 0, .25);
}

.slider-handle.custom {
    background: transparent none;
}

.slider-handle.custom::before {
    line-height: 24px;
    font-size: 24px;
    width: 24px;
    height: 24px;
    content: '\2605';
    color: #726204;
}

.slider-tick {
    position: absolute;
    width: 24px;
    height: 24px;
    background-color: #ffffff;
    border: 1px solid #999999;
    opacity: .8;
}

.slider-tick.round {
    border-radius: 50%;
}

.slider-tick.triangle {
    background: transparent none;
}

.slider-tick.custom {
    background: transparent none;
}

.slider-tick.custom::before {
    line-height: 24px;
    font-size: 24px;
    width: 24px;
    height: 24px;
    content: '\2605';
    color: #726204;
}

.slider-tick.in-selection {
    opacity: 1;
}

.input-slider-danger .slider-selection {
    background-color: #e74c3c;
}

.input-slider-warning .slider-selection {
    background-color: #e67e22;
}

.input-slider-info .slider-selection {
    background-color: #2980b9;
}

.input-slider-success .slider-selection {
    background-color: #27ae60;
}

/*
==============================================
(#16nps) NProgress (c) 2013, Rico Sta. Cruz

http://ricostacruz.com/nprogress
==============================================
*/

/* Make clicks pass-through */
#nprogress {
    pointer-events: none;
    -webkit-pointer-events: none;
}

#nprogress .bar {
    background-color: #1bbae1;
    position: fixed;
    z-index: 1050;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
}

/* Fancy blur effect */
#nprogress .peg {
    display: block;
    position: absolute;
    right: 0px;
    width: 100px;
    height: 100%;
    box-shadow: 0 0 10px #1bbae1, 0 0 5px #1bbae1;
    opacity: 1.0;
    -webkit-transform: rotate(3deg) translate(0px, -4px);
    -moz-transform: rotate(3deg) translate(0px, -4px);
    -ms-transform: rotate(3deg) translate(0px, -4px);
    -o-transform: rotate(3deg) translate(0px, -4px);
    transform: rotate(3deg) translate(0px, -4px);
}

/* Remove these to get rid of the spinner */
#nprogress .spinner {
    display: block;
    position: fixed;
    z-index: 1050;
    top: 15px;
    left: 50%;
    margin-left: -10px;
}

#nprogress .spinner-icon {
    width: 20px;
    height: 20px;
    border:  solid 2px transparent;
    border-top-color:  #1bbae1;
    border-left-color: #1bbae1;
    border-radius: 10px;
    -webkit-animation: nprogress-spinner 400ms linear infinite;
    -moz-animation:    nprogress-spinner 400ms linear infinite;
    -ms-animation:     nprogress-spinner 400ms linear infinite;
    -o-animation:      nprogress-spinner 400ms linear infinite;
    animation:         nprogress-spinner 400ms linear infinite;
}

@-webkit-keyframes nprogress-spinner {
    0%   { -webkit-transform: rotate(0deg);   transform: rotate(0deg); }
    100% { -webkit-transform: rotate(360deg); transform: rotate(360deg); }
}
@-moz-keyframes nprogress-spinner {
    0%   { -moz-transform: rotate(0deg);   transform: rotate(0deg); }
    100% { -moz-transform: rotate(360deg); transform: rotate(360deg); }
}
@-o-keyframes nprogress-spinner {
    0%   { -o-transform: rotate(0deg);   transform: rotate(0deg); }
    100% { -o-transform: rotate(360deg); transform: rotate(360deg); }
}
@-ms-keyframes nprogress-spinner {
    0%   { -ms-transform: rotate(0deg);   transform: rotate(0deg); }
    100% { -ms-transform: rotate(360deg); transform: rotate(360deg); }
}
@keyframes nprogress-spinner {
    0%   { transform: rotate(0deg);   transform: rotate(0deg); }
    100% { transform: rotate(360deg); transform: rotate(360deg); }
}


/*
==============================================
(#17s2s) Select2
==============================================
*/

.select2-container {
    box-sizing: border-box;
    display: inline-block;
    margin: 0;
    position: relative;
    vertical-align: middle;
}
.select2-container .select2-selection--single {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    height: 28px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-container .select2-selection--single .select2-selection__rendered {
    display: block;
    padding-left: 8px;
    padding-right: 20px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.select2-container[dir="rtl"] .select2-selection--single .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 20px;
}
.select2-container .select2-selection--multiple {
    box-sizing: border-box;
    cursor: pointer;
    display: block;
    min-height: 32px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-container .select2-selection--multiple .select2-selection__rendered {
    display: inline-block;
    overflow: hidden;
    padding-left: 8px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.select2-container .select2-search--inline {
    float: left;
}
.select2-container .select2-search--inline .select2-search__field {
    box-sizing: border-box;
    border: none;
    font-size: 100%;
    margin-top: 5px;
}
.select2-container .select2-search--inline .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none;
}
.select2-dropdown {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    box-sizing: border-box;
    display: block;
    position: absolute;
    left: -100000px;
    width: 100%;
    z-index: 1051;
}
.select2-results {
    display: block;
}
.select2-results__options {
    list-style: none;
    margin: 0;
    padding: 0;
}
.select2-results__option {
    padding: 6px;
    user-select: none;
    -webkit-user-select: none;
}
.select2-results__option[aria-selected] {
    cursor: pointer;
}
.select2-container--open .select2-dropdown {
    left: 0;
}
.select2-container--open .select2-dropdown--above {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--open .select2-dropdown--below {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-search--dropdown {
    display: block;
    padding: 4px;
}
.select2-search--dropdown .select2-search__field {
    padding: 4px;
    width: 100%;
    box-sizing: border-box;
}
.select2-search--dropdown .select2-search__field::-webkit-search-cancel-button {
    -webkit-appearance: none;
}
.select2-search--dropdown.select2-search--hide {
    display: none;
}
.select2-close-mask {
    border: 0;
    margin: 0;
    padding: 0;
    display: block;
    position: fixed;
    left: 0;
    top: 0;
    min-height: 100%;
    min-width: 100%;
    height: auto;
    width: auto;
    opacity: 0;
    z-index: 99;
    background-color: #fff;
    filter: alpha(opacity=0);
}
.select2-hidden-accessible {
    border: 0 !important;
    clip: rect(0 0 0 0) !important;
    height: 1px !important;
    margin: -1px !important;
    overflow: hidden !important;
    padding: 0 !important;
    position: absolute !important;
    width: 1px !important;
}
.select2-container--default .select2-selection--single {
    background-color: #fff;
    border: 1px solid #aaa;
    border-radius: 4px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px;
}
.select2-container--default .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
}
.select2-container--default .select2-selection--single .select2-selection__placeholder {
    color: #999;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
}
.select2-container--default .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left;
}
.select2-container--default[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    left: 1px;
    right: auto;
}
.select2-container--default.select2-container--disabled .select2-selection--single {
    background-color: #eee;
    cursor: default;
}
.select2-container--default.select2-container--disabled .select2-selection--single .select2-selection__clear {
    display: none;
}
.select2-container--default.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px;
}
.select2-container--default .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
}
.select2-container--default .select2-selection--multiple .select2-selection__rendered {
    box-sizing: border-box;
    list-style: none;
    margin: 0;
    padding: 0 5px;
    width: 100%;
}
.select2-container--default .select2-selection--multiple .select2-selection__placeholder {
    color: #999;
    margin-top: 5px;
    float: left;
}
.select2-container--default .select2-selection--multiple .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-top: 5px;
    margin-right: 10px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    color: #999;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #333;
}
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice,
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__placeholder,
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-search--inline {
    float: right;
}
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto;
}
.select2-container--default[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto;
}
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border: solid black 1px;
    outline: 0;
}
.select2-container--default.select2-container--disabled .select2-selection--multiple {
    background-color: #eee;
    cursor: default;
}
.select2-container--default.select2-container--disabled .select2-selection__choice__remove {
    display: none;
}
.select2-container--default.select2-container--open.select2-container--above .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-container--default.select2-container--open.select2-container--below .select2-selection--single,
.select2-container--default.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
}
.select2-container--default .select2-search--inline .select2-search__field {
    background: transparent;
    border: none;
    outline: 0;
    box-shadow: none;
}
.select2-container--default .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}
.select2-container--default .select2-results__option[role=group] {
    padding: 0;
}
.select2-container--default .select2-results__option[aria-disabled=true] {
    color: #999;
}
.select2-container--default .select2-results__option[aria-selected=true] {
    background-color: #ddd;
}
.select2-container--default .select2-results__option .select2-results__option {
    padding-left: 1em;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__group {
    padding-left: 0;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -1em;
    padding-left: 2em;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -2em;
    padding-left: 3em;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -3em;
    padding-left: 4em;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -4em;
    padding-left: 5em;
}
.select2-container--default .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option .select2-results__option {
    margin-left: -5em;
    padding-left: 6em;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #5897fb;
    color: white;
}
.select2-container--default .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px;
}
.select2-container--classic .select2-selection--single {
    background-color: #f7f7f7;
    border: 1px solid #aaa;
    border-radius: 4px;
    outline: 0;
    background-image: -webkit-linear-gradient(top, white 50%, #eeeeee 100%);
    background-image: -o-linear-gradient(top, white 50%, #eeeeee 100%);
    background-image: linear-gradient(to bottom, white 50%, #eeeeee 100%);
    background-repeat: repeat-x;
}
.select2-container--classic .select2-selection--single:focus {
    border: 1px solid #5897fb;
}
.select2-container--classic .select2-selection--single .select2-selection__rendered {
    color: #444;
    line-height: 28px;
}
.select2-container--classic .select2-selection--single .select2-selection__clear {
    cursor: pointer;
    float: right;
    font-weight: bold;
    margin-right: 10px;
}
.select2-container--classic .select2-selection--single .select2-selection__placeholder {
    color: #999;
}
.select2-container--classic .select2-selection--single .select2-selection__arrow {
    background-color: #ddd;
    border: none;
    border-left: 1px solid #aaa;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    height: 26px;
    position: absolute;
    top: 1px;
    right: 1px;
    width: 20px;
    background-image: -webkit-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
    background-image: -o-linear-gradient(top, #eeeeee 50%, #cccccc 100%);
    background-image: linear-gradient(to bottom, #eeeeee 50%, #cccccc 100%);
    background-repeat: repeat-x;
}
.select2-container--classic .select2-selection--single .select2-selection__arrow b {
    border-color: #888 transparent transparent transparent;
    border-style: solid;
    border-width: 5px 4px 0 4px;
    height: 0;
    left: 50%;
    margin-left: -4px;
    margin-top: -2px;
    position: absolute;
    top: 50%;
    width: 0;
}
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__clear {
    float: left;
}
.select2-container--classic[dir="rtl"] .select2-selection--single .select2-selection__arrow {
    border: none;
    border-right: 1px solid #aaa;
    border-radius: 0;
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    left: 1px;
    right: auto;
}
.select2-container--classic.select2-container--open .select2-selection--single {
    border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow {
    background: transparent;
    border: none;
}
.select2-container--classic.select2-container--open .select2-selection--single .select2-selection__arrow b {
    border-color: transparent transparent #888 transparent;
    border-width: 0 4px 5px 4px;
}
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--single {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    background-image: -webkit-linear-gradient(top, white 0%, #eeeeee 50%);
    background-image: -o-linear-gradient(top, white 0%, #eeeeee 50%);
    background-image: linear-gradient(to bottom, white 0%, #eeeeee 50%);
    background-repeat: repeat-x;
}
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--single {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
    background-image: -webkit-linear-gradient(top, #eeeeee 50%, white 100%);
    background-image: -o-linear-gradient(top, #eeeeee 50%, white 100%);
    background-image: linear-gradient(to bottom, #eeeeee 50%, white 100%);
    background-repeat: repeat-x;
}
.select2-container--classic .select2-selection--multiple {
    background-color: white;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: text;
    outline: 0;
}
.select2-container--classic .select2-selection--multiple:focus {
    border: 1px solid #5897fb;
}
.select2-container--classic .select2-selection--multiple .select2-selection__rendered {
    list-style: none;
    margin: 0;
    padding: 0 5px;
}
.select2-container--classic .select2-selection--multiple .select2-selection__clear {
    display: none;
}
.select2-container--classic .select2-selection--multiple .select2-selection__choice {
    background-color: #e4e4e4;
    border: 1px solid #aaa;
    border-radius: 4px;
    cursor: default;
    float: left;
    margin-right: 5px;
    margin-top: 5px;
    padding: 0 5px;
}
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove {
    color: #888;
    cursor: pointer;
    display: inline-block;
    font-weight: bold;
    margin-right: 2px;
}
.select2-container--classic .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: #555;
}
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    float: right;
}
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice {
    margin-left: 5px;
    margin-right: auto;
}
.select2-container--classic[dir="rtl"] .select2-selection--multiple .select2-selection__choice__remove {
    margin-left: 2px;
    margin-right: auto;
}
.select2-container--classic.select2-container--open .select2-selection--multiple {
    border: 1px solid #5897fb;
}
.select2-container--classic.select2-container--open.select2-container--above .select2-selection--multiple {
    border-top: none;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}
.select2-container--classic.select2-container--open.select2-container--below .select2-selection--multiple {
    border-bottom: none;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}
.select2-container--classic .select2-search--dropdown .select2-search__field {
    border: 1px solid #aaa;
    outline: 0;
}
.select2-container--classic .select2-search--inline .select2-search__field {
    outline: 0;
    box-shadow: none;
}
.select2-container--classic .select2-dropdown {
    background-color: white;
    border: 1px solid transparent;
}
.select2-container--classic .select2-dropdown--above {
    border-bottom: none;
}
.select2-container--classic .select2-dropdown--below {
    border-top: none;
}
.select2-container--classic .select2-results > .select2-results__options {
    max-height: 200px;
    overflow-y: auto;
}
.select2-container--classic .select2-results__option[role=group] {
    padding: 0;
}
.select2-container--classic .select2-results__option[aria-disabled=true] {
    color: grey;
}
.select2-container--classic .select2-results__option--highlighted[aria-selected] {
    background-color: #3875d7;
    color: white;
}
.select2-container--classic .select2-results__group {
    cursor: default;
    display: block;
    padding: 6px;
}
.select2-container--classic.select2-container--open .select2-dropdown {
    border-color: #5897fb;
}
.select2-container .select2-selection--single {
    height: 34px;
}
.select2-container .select2-dropdown {
    border-color: #dbe1e8;
    border-bottom-right-radius: 3px;
    border-bottom-left-radius: 3px;
}
.select2-container--default .select2-selection--single {
    border-color: #dbe1e8;
    border-radius: 3px;
}
.select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-left: 8px;
    line-height: 34px;
}
.form-material .select2-container--default .select2-selection--single .select2-selection__rendered {
    padding-left: 0;
}
.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 34px;
}
.select2-container--default .select2-selection--multiple,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #dbe1e8;
    border-radius: 3px;
}
.select2-container--default .select2-selection--multiple .select2-selection__rendered,
.select2-container--default.select2-container--focus .select2-selection--multiple .select2-selection__rendered {
    padding-right: 8px;
    padding-left: 8px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice {
    height: 22px;
    line-height: 22px;
    color: #fff;
    font-size: 13px;
    font-weight: 600;
    background-color: #1bbae1;
    border: none;
    border-radius: 3px;
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
    margin-right: 5px;
    color: rgba(255, 255, 255, 0.5);
}
.select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
    color: rgba(255, 255, 255, 0.75);
}
.select2-container--default .select2-search--dropdown .select2-search__field {
    border-color: #dbe1e8;
}
.select2-container--default .select2-results__option--highlighted[aria-selected] {
    background-color: #1bbae1;
}
.select2-container--default .select2-search--inline .select2-search__field {
    padding-right: 0;
    padding-left: 0;
    font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
    -webkit-box-shadow: none;
    box-shadow: none;
}
.select2-search--dropdown .select2-search__field {
    padding: 6px 12px;
    font-family: "Lato", "Helvetica Neue", Helvetica, Arial, sans-serif;
    border-radius: 3px;
    -webkit-box-shadow: none;
    box-shadow: none;
}

.select2-container.select2-container--open .select2-dropdown,
.select2-container--default.select2-container--open .select2-selection--single,
.select2-container--default.select2-container--open .select2-selection--multiple,
.select2-container--default.select2-container--focus.select2-container--open .select2-selection--multiple {
    border-color: #1bbae1;
}

.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #1bbae1;
}

.fc-content {
    font-size: 1.2em !important;
}
