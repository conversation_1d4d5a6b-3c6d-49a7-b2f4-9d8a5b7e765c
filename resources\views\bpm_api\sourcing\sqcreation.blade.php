@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>SOURCING DP BPM TASK<br>
                <small>Refire Simple Qoute Creation</small>
            </h1>
        </div>
    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="row" >
                <div class="col-sm-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>Search</strong> SQ Number</h2>
                        </div>

                        @if($status_api != null)
                            <h5 class="alert alert-danger">{{$status_api}}</h5>
                        @endif   
                        
                        @if($checkTask != null )
                        <div class="block">
                            <h5 class="alert alert-danger">Running Instance Detected.Do Not Refire</h5>
                            <ul>
                                <li>Composite : {{ $checkTask['compositeDN'] }}</li>
                                <li>Instance Id : {{ $checkTask['instanceId'] }}</li>
                                <li>State Task : {{ $checkTask['state'] }}</li>
                            </ul>
                        </div>
                        @endif
                        
                        @if($dataTask != null && isset($dataTask['compositeDN']) && isset($dataTask['instanceId']) > 0)

                        <div class="block">
                            <h5 class="alert alert-success">Success Refire Simple Quote Creation</h5>
                            <b>
                                <ul>
                                    <li>Composite : {{ $dataTask['compositeDN'] }}</li>
                                    <li>Instance Id : {{ $dataTask['instanceId'] }}</li>
                                </ul>
                            </b>
                        </div>
                        
                        @elseif($dataTask != null  && isset($dataTask['remarks']) )
                        <div class="block">
                            <h5 class="alert alert-success">{{ $dataTask['remarks'] }}</h5>
                        </div>
                        @endif
                        
                        <form action="{{url('/bpm/sourcing/task/sqcreation')}}" method="post" class="form-horizontal form-bordered" >
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="sq_no" name="sq_no" class="form-control" placeholder="Simple Quote Number .. SourcingDP modules only" 
                                           required="required" value="{{old('sq_no')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Document Number <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="action_task_sqcreation" name="action_task_sqcreation" class="select-chosen" data-placeholder="Choose Type Action Task .." style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="initiate-task-sq" @if(old('action_task_sqcreation') === "initiate-task-sq") selected @endif>Initiate Simple Qoute Creation</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-gears"></i>  Choose Type Action Task <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="is_trigger_bpm_sqcreation" name="is_trigger_bpm_sqcreation" class="select-chosen" data-placeholder="True (program will execute trigger to BPM) , False (program will not execute trigger to BPM)" style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="true" @if(old('is_trigger_bpm_sqcreation') === "true") selected @endif>True</option>
                                    </select>
                                    <span class="input-group-addon"><i class="hi hi-transfer"></i>  Is Trigger BPM </span>
                                </div>
                            </div>
                            
                            <div class="form-group form-actions">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-sm btn-info pull-right">Submit</button>
                                </div>
                            </div>
                        </form>
                        
                        @if($listdata != null && count($listdata) > 0)

                        <div class="block">
                            @if($action === 'initiate-update-task-cr')
                                @foreach($listdata["result"]["result"] as $data) 
                              
                                    @if(isset($data["is_trigger_bpm"]))
                                        @if($data["is_trigger_bpm"] == 'true')
                                        <div class="alert alert-success">
                                            <b><h5>Success refire task..</h5>
                                            <ul>
                                                    <li>Composite : {{$data["bpm_version"]}}</li>
                                                    <li>Instance Id : {{$data["bpm_instance_id"]}} </li>
                                                    <li>CR Number : {{$data["doc_no"]}} </li>
                                                </b>
                                            </ul>
                                        </div>
                                        @endif
                                    @endif
                                    
                                    <h4>Payload</h4>
                                    @if(isset($data["xml_payload"]))
                                    <pre class="line-numbers">
                                        <code class="language-markup">{{ htmlentities($data["xml_payload"]) }}</code>
                                    </pre>
                                    @else
                                        <h5 class="alert alert-danger">{{$data["remarks"]}}</h5>
                                    @endif
                                @endforeach
                                
                            @else   
                                @if(isset($listdata["result"]["is_trigger_bpm"]) && isset($listdata["result"]["bpm_version"]) && isset($listdata["result"]["bpm_instance_id"]) && isset($listdata["result"]["doc_no"]))
                                    @if($listdata["result"]["is_trigger_bpm"] == 'true')
                                        <div class="alert alert-success">
                                            <b><h5>Success refire task..</h5>
                                            <ul>
                                                    <li>Composite : {{$listdata["result"]["bpm_version"]}}</li>
                                                    <li>Instance Id : {{$listdata["result"]["bpm_instance_id"]}} </li>
                                                    <li>CR Number : {{$listdata["result"]["doc_no"]}} </li>
                                                </b>
                                            </ul>
                                        </div>
                                    @endif
                                @endif
                                
                                @if(isset($listdata["result"]["xml_payload"]))
                                <h4>Payload</h4>
                                <pre class="line-numbers">
                                    <code class="language-markup">{{ htmlentities($listdata["result"]["xml_payload"]) }}</code>
                                </pre>
                                @endif
                                
                                @if(isset($listdata["result"]["remarks"]))
                                    <div class="block">
                                        <h5 class="alert alert-danger">{{$listdata["result"]["remarks"]}}</h5>
                                    </div>
                                @endif
                            @endif
                        </div>
                        
                        @endif

                    </div>

                </div>
            </div>

        </div>
    </div>
    



@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



