<?php

namespace App\Services\Traits;

use Log;
use App\Services\EPService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait TechRefreshIssueService
{

    
    /**
     * Get list of QT: Create SST
     * @return type
     */
    public function getQtCreateSST()
    {
        $q = "SELECT DISTINCT f.QT_FINALIZATION_ID,
            b.is_current,
            a.qt_id,
            a.qt_no,
            b.status_id,
            c.status_name,
            b.created_date,
            b.doc_type,
            a.PROCUREMENT_MODE_ID,
            a.PROPOSAL_VALIDITY_END_DATE
        FROM
            sc_qt a,
            sc_workflow_status b,
            pm_status_desc c,
            sc_loi_loa d,
            sc_qt_finalization e,
            sc_qt_recommend f,
            sc_qt_recommend_dtl g
        WHERE
            a.qt_id = b.doc_id
            AND b.status_id = c.status_id
            --AND a.qt_no LIKE '%QT190000000053207%'
            AND b.IS_CURRENT = 1
            AND c.language_code = 'en'
            AND b.status_id = 62213
            AND b.doc_type <> 'SQ'
            AND a.QT_ID = d.DOC_ID
            AND d.IS_LOI_REQ = 0
            AND a.QT_ID = e.QT_ID
            AND e.QT_FINALIZATION_ID = f.QT_FINALIZATION_ID
            AND f.QT_RECOMMEND_ID = g.QT_RECOMMEND_ID
            AND g.IS_LOI_AWARDED = 1
            AND d.SUPPLIER_ID = g.SUPPLIER_ID
            AND a.PROPOSAL_VALIDITY_END_DATE >= sysdate
        ORDER BY
            b.created_date DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of FL - Invoice Cancellation
     * @return type
     */
    public function getFlInvoiceCancellation()
    {
        $dateFilter = '24-jan-2023';
        $env = env('APP_ENV');
        if( $env  != null && ($env =='SIT' || $env =='UAT') ){
            $dateFilter  = '08-sep-2023';
        }

        $q = "SELECT * FROM
            (
            SELECT
                b.CREATED_DATE REVISED_DATE,
                (
                SELECT
                    max(TO_CHAR (inn.CREATED_DATE, 'DD/MM/YYYY HH12:MI:SS'))
                FROM
                    fl_workflow_status inn
                WHERE
                    inn.DOC_ID = a.FULFILMENT_ORDER_ID
                    AND inn.DOC_TYPE IN ('PO', 'CO')
                    AND inn.status_id IN (41515, 41015)
                    AND TO_CHAR (inn.CREATED_DATE,
                    'mm') = '01' ) inv_created_date,
                a.doc_no poco_no,
                (
                SELECT
                    IS_SENT_IDD_CRE
                FROM
                    fl_invoice inv
                WHERE
                    inv.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    AND record_status = 1) MM506,
                (
                SELECT
                    adj2.DOC_TYPE
                FROM
                    fl_workflow_status adj ,
                    fl_adjustment adj2
                WHERE
                    adj.DOC_ID = adj2.ADJUSTMENT_ID
                    AND adj2.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    AND adj.DOC_TYPE IN ('DN')
                        AND adj.status_id IN (45300)
                            AND adj.IS_CURRENT = 1 ) MM503,
                (
                SELECT
                    SUPPLIER_NAME
                FROM
                    fl_supplier_dtl
                WHERE
                    FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID ) SUPPLIER_NAME,
                d.status_name ,
                (
                SELECT
                    max(ORG_CODE)
                FROM
                    pm_org_validity ptj
                WHERE
                    ptj.ORG_PROFILE_ID = (
                    SELECT
                        pr.prepared_ORG_PROFILE_ID
                    FROM
                        fl_fulfilment_request pr
                    WHERE
                        pr.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID ) ) ptj_code,
                a.FULFILMENT_ORDER_ID,
                c.STATUS_ID
            FROM
                FL_FULFILMENT_ORDER A,
                FL_WORKFLOW_STATUS B,
                PM_STATUS C,
                PM_STATUS_DESC D
            WHERE
                A.FULFILMENT_ORDER_ID = B.DOC_ID
                AND B.STATUS_ID = C.STATUS_ID
                AND C.STATUS_ID = D.STATUS_ID
                AND D.LANGUAGE_CODE = 'en'
                AND B.DOC_TYPE IN ('PO', 'CO')
                AND B.IS_CURRENT = 1
                AND B.STATUS_ID IN (41017, 41517)
                AND TO_CHAR (A.CREATED_DATE,
                'yyyy') IN ('2023')
                --ORDER BY TO_CHAR (B.CREATED_DATE, 'mm'),TO_CHAR (B.CREATED_DATE, 'dd')   asc 
        )
        WHERE inv_CREATED_DATE <= '$dateFilter'
        ORDER BY TO_CHAR (REVISED_DATE, 'mm'), TO_CHAR (REVISED_DATE, 'dd') ASC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of FL - POCO Cancellation
     * @return type
     */
    public function getFlPocoCancellation()
    {
        $q = "SELECT fr.CREATED_DATE  AS poco_created, s.created_date AS wf_created,  fr.FULFILMENT_REQ_ID, 
            fr.DOC_NO, s.STATUS_ID,  s.IS_CURRENT, fr.RECORD_STATUS
        FROM fl_workflow_status s, FL_FULFILMENT_order fr
        WHERE s.doc_type in ( 'PO' ,'CO')
        and fr.FULFILMENT_order_ID = s.doc_id
        and s.IS_CURRENT = 1
        and s.STATUS_ID in (41500,41000)
        --and fr.DOC_NO in ('PR190000000690557')
        and fr.CREATED_DATE >= '01-jan-2023'
        and exists (
        SELECT tt.TRACKING_DIARY_ID
        FROM pm_tracking_diary tt
        WHERE tt.tracking_diary_id IN (SELECT MAX (t.tracking_diary_id)
                                        FROM pm_tracking_diary t
                                        WHERE t.doc_no = fr.DOC_NO )
        AND tt.status_id in (41600,41100)
        ) order by 1 DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of QT - QT Closed
     * @return type
     */
    public function getQtClosed()
    {
        $dateFilter = '24/01/2023 12:00:00';
        $env = env('APP_ENV');
        if( $env  != null && ($env =='SIT' || $env =='UAT') ){
            $dateFilter  = '08/09/2023 12:00:00';
        }

        $q = "SELECT   'Completed' AS committed_done, a.publish_date, a.closing_date,
                a.qt_id, a.qt_no, a.evaluation_type eval_type, b.doc_type,
                d.status_name, d.status_id,
                TO_CHAR (a.created_date, 'DD/MM/YYYY') qt_created
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                sc_workflow_status f,
                pm_status_desc e
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 60022
            AND b.doc_type = 'QT'
            AND a.qt_id = f.doc_id
            AND f.status_id = e.status_id
            AND f.is_current = 1
            AND f.status_id = 60202
            AND f.doc_type = 'CMT'
            AND e.language_code = 'en'
            AND a.closing_date >=
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.created_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.publish_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
        UNION ALL
        SELECT   'Pending Assign' committed_done, a.publish_date, a.closing_date,
                a.qt_id, a.qt_no, a.evaluation_type eval_type, b.doc_type,
                d.status_name, d.status_id,
                TO_CHAR (a.created_date, 'DD/MM/YYYY') qt_created
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                sc_workflow_status f,
                pm_status_desc e
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 60022
            AND b.doc_type = 'QT'
            AND a.qt_id = f.doc_id
            AND f.status_id = e.status_id
            AND f.is_current = 1
            AND f.status_id = 60200
            AND f.doc_type = 'CMT'
            AND e.language_code = 'en'
            AND a.closing_date >=
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.created_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.publish_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND f.CREATED_DATE <=
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
        UNION ALL
        SELECT   'Pending Approval' committed_done, a.publish_date, a.closing_date,
                a.qt_id, a.qt_no, a.evaluation_type eval_type, b.doc_type,
                d.status_name, d.status_id,
                TO_CHAR (a.created_date, 'DD/MM/YYYY') qt_created
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                sc_workflow_status f,
                pm_status_desc e
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 60022
            AND b.doc_type = 'QT'
            AND a.qt_id = f.doc_id
            AND f.status_id = e.status_id
            AND f.is_current = 1
            AND f.status_id = 60201
            AND f.doc_type = 'CMT'
            AND e.language_code = 'en'
            AND a.closing_date >=
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.created_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
            AND a.publish_date <
                            TO_DATE ('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
        ORDER BY 5";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

        /**
     * Get list of QT - QT Closed
     * @return type
     */
    public function getQtSpecStuck()
    {
        $q = "SELECT
            b.workflow_status_id,
            a.qt_id,
            a.procurement_mode_id,
            a.qt_no,
            b.doc_type, 
            d.status_name AS status_workflow,
            d.status_id AS status_id_workflow, 
            f.status_name AS status_tracking,
            f.status_id AS status_id_tracking,
            a.created_date AS qt_created_date,
            TO_CHAR (b.created_date, 'DD/MM/YYYY HH12:MI:SS') workflow_created_date,
            TO_CHAR (b.changed_date, 'DD/MM/YYYY HH12:MI:SS') workflow_changed_date
        FROM
            sc_qt a,
            sc_workflow_status b,
            pm_status_desc d,
            pm_tracking_diary e,
            pm_status_desc f
        WHERE
            a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 60003
            AND b.doc_type = 'QT'
            AND a.qt_id = e.doc_id
            AND e.status_id = f.status_id
            AND f.language_code = 'en'
            AND f.status_id = 60025
        ORDER BY
            a.qt_no DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get BPM instance
     * @return type
     */
    public function getBpmInstance($docNo, $module)
    {
        $q = "WITH cte AS (
            SELECT COMPOSITEINSTANCEID, FLOW_ID, CUSTOMATTRIBUTESTRING1, COMPOSITENAME, COMPOSITEVERSION, STATE,
            (SELECT i.CREATED_TIME FROM SCA_FLOW_INSTANCE i WHERE i.FLOW_ID = w.flow_id) AS instance_created_date
            FROM wftask w 
            WHERE CUSTOMATTRIBUTESTRING1 = ?
            AND COMPOSITENAME = ?
            ORDER BY (CASE WHEN state = 'ASSIGNED' THEN 1 ELSE 2 END)
        )
        SELECT * FROM cte
        WHERE ROWNUM = 1";

        $result = DB::connection('oracle_bpm_rpt')->select($q, array($docNo, $module));

        return $result;
    }

    /**
     * Get list of QT - Stuck Task EC
     * @return type
     */
    public function getQtSpecStuckEC()
    {
        $q = "SELECT   b.workflow_status_id, a.qt_id, a.procurement_mode_id, a.qt_no,
                b.doc_type, d.status_name AS status_workflow,
                d.status_id AS status_id_workflow, f.status_name AS status_tracking,
                f.status_id AS status_id_tracking, a.created_date AS qt_created_date,
                TO_CHAR (b.created_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_created_date,
                TO_CHAR (b.changed_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_changed_date
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                pm_tracking_diary e,
                pm_status_desc f,
                sc_workflow_status g,
                pm_status_desc h 
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 62051  
            AND b.doc_type = 'EC'
            --and a.qt_no = 'QT230000000001327'
            AND a.qt_id = e.doc_id
            AND e.status_id = f.status_id
            AND f.language_code = 'en'
            AND f.status_id = 62053 
            and a.qt_id = g.doc_id
            AND g.status_id = h.status_id
            AND h.language_code = 'en'
            AND g.is_current = 1
            and g.STATUS_ID = 60010 
            AND g.status_id NOT IN
                (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
                    60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
                    60046)
            AND g.doc_type = 'QT'
        ORDER BY a.qt_no DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of QT - Stuck Task OC
     * @return type
     */
    public function getQtSpecStuckOC()
    {
        $q = "SELECT b.workflow_status_id, a.qt_id, a.procurement_mode_id, a.qt_no,
                 b.doc_type, d.status_name AS status_workflow,
                 d.status_id AS status_id_workflow, f.status_name AS status_tracking,
                 f.status_id AS status_id_tracking, a.created_date AS qt_created_date,
                 TO_CHAR (b.created_date,
                          'DD/MM/YYYY HH12:MI:SS'
                         ) workflow_created_date,
                 TO_CHAR (b.changed_date,
                          'DD/MM/YYYY HH12:MI:SS'
                         ) workflow_changed_date
            FROM sc_qt a,
                 sc_workflow_status b,
                 pm_status_desc d,
                 pm_tracking_diary e,
                 pm_status_desc f,
                 sc_workflow_status g,
                 pm_status_desc h 
           WHERE a.qt_id = b.doc_id
             AND b.status_id = d.status_id
             AND d.language_code = 'en'
             AND b.is_current = 1
             AND b.status_id = 62001 
             AND b.doc_type = 'OC'
             --and a.qt_no = 'QT230000000001327'
             AND a.qt_id = e.doc_id
             AND e.status_id = f.status_id
             AND f.language_code = 'en'
             AND f.status_id = 62002
             and a.qt_id = g.doc_id
             AND g.status_id = h.status_id
             AND h.language_code = 'en'
             AND g.is_current = 1
             and g.STATUS_ID = 60010 
             AND g.status_id NOT IN
                    (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
                     60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
                     60046)
             AND g.doc_type = 'QT'
        ORDER BY a.qt_no DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of QT - Stuck Task TEC
     * @return type
     */
    public function getQtSpecStuckTEC()
    {
        $q = "SELECT   b.workflow_status_id, a.qt_id, a.procurement_mode_id, a.qt_no,
                b.doc_type, d.status_name AS status_workflow,
                d.status_id AS status_id_workflow, f.status_name AS status_tracking,
                f.status_id AS status_id_tracking, a.created_date AS qt_created_date,
                TO_CHAR (b.created_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_created_date,
                TO_CHAR (b.changed_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_changed_date
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                pm_tracking_diary e,
                pm_status_desc f,
                sc_workflow_status g,
                pm_status_desc h 
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 62101   
            AND b.doc_type = 'TEC'
            AND a.qt_id = e.doc_id
            AND e.status_id = f.status_id
            AND f.language_code = 'en'
            AND f.status_id = 62103   
            and a.qt_id = g.doc_id
            AND g.status_id = h.status_id
            AND h.language_code = 'en'
            AND g.is_current = 1
            and g.STATUS_ID = 60010 
            AND g.status_id NOT IN
                (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
                    60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
                    60046)
            AND g.doc_type = 'QT'
        ORDER BY a.qt_no DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of QT - Stuck Task FEC
     * @return type
     */
    public function getQtSpecStuckFEC()
    {
        $q = "SELECT   b.workflow_status_id, a.qt_id, a.procurement_mode_id, a.qt_no,
                b.doc_type, d.status_name AS status_workflow,
                d.status_id AS status_id_workflow, f.status_name AS status_tracking,
                f.status_id AS status_id_tracking, a.created_date AS qt_created_date,
                TO_CHAR (b.created_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_created_date,
                TO_CHAR (b.changed_date,
                        'DD/MM/YYYY HH12:MI:SS'
                        ) workflow_changed_date
        FROM sc_qt a,
                sc_workflow_status b,
                pm_status_desc d,
                pm_tracking_diary e,
                pm_status_desc f,
                sc_workflow_status g,
                pm_status_desc h 
        WHERE a.qt_id = b.doc_id
            AND b.status_id = d.status_id
            AND d.language_code = 'en'
            AND b.is_current = 1
            AND b.status_id = 62151    
            AND b.doc_type = 'FEC'
            AND a.qt_id = e.doc_id
            AND e.status_id = f.status_id
            AND f.language_code = 'en'
            AND f.status_id = 62152    
            and a.qt_id = g.doc_id
            AND g.status_id = h.status_id
            AND h.language_code = 'en'
            AND g.is_current = 1
            and g.STATUS_ID = 60010 
            AND g.status_id NOT IN
                (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
                    60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
                    60046)
            AND g.doc_type = 'QT'
        ORDER BY a.qt_no DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of QT - Stuck Task FEC
     * @return type
     */
    public function getQtSpecStuckAwarded()
    {
        $dateFilter = '24/01/2023 12:00:00';
        $env = env('APP_ENV');
        if( $env  != null && ($env =='SIT' || $env =='UAT') ){
            $dateFilter  = '08/09/2023 12:00:00';
        }

        $q = "SELECT
        aa.qt_no,
        aa.procurement_mode_id AS Procurement,
        gg.status_name AS qt_status,
        ee.loa_no,
        dd.status_name AS loa_status,
        to_char(cc.CREATED_DATE, 'dd-mm-yyyy') AS loa_completed_date,
        to_char(aa.proposal_validity_end_date, 'dd-mm-yyyy') AS validity_date
    FROM
        sc_qt aa,
        sc_loi_loa bb,
        sc_workflow_status cc,
        pm_status_desc dd,
        sc_loa ee,
        sc_workflow_status ff,
        pm_status_desc gg,
        sc_qt_finalization hh
    WHERE
        aa.qt_id = bb.doc_id
        AND bb.loi_loa_id = ee.loi_loa_id
        AND ee.loa_id = cc.doc_id
        AND cc.status_id = dd.status_id
        AND cc.is_current = 1
        AND cc.doc_type = 'LA'
        AND cc.status_id = 62506
        AND dd.language_code = 'en'
        AND aa.qt_id = ff.doc_id
        AND ff.status_id = gg.status_id
        AND gg.language_code = 'en'
        AND ff.is_current = 1
        AND ff.doc_type = 'QT'
        AND ff.status_id = 60013
        AND cc.CREATED_DATE >= to_date('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
        AND aa.qt_id = hh.qt_id
        AND hh.finalization_method_id <> 368
        AND aa.qt_no NOT IN (
        SELECT
            DISTINCT d.qt_no
        FROM
            sc_qt_finalization a,
            sc_qt_recommend_dtl b,
            sc_qt_recommend c,
            sc_qt d,
            sc_request_item e,
            sc_loi_loa_item f,
            sc_loa g,
            sc_loi_loa h
        WHERE
            d.qt_id = a.qt_id
            AND h.loi_loa_id = g.loi_loa_id
            AND c.qt_finalization_id = a.qt_finalization_id
            AND c.qt_recommend_id = b.qt_recommend_id
            AND c.request_item_id = f.request_item_id
            AND f.request_item_id = e.request_item_id
            AND f.loa_id = g.loa_id
            AND d.qt_no IN (
            SELECT
                a.qt_no
            FROM
                sc_qt a,
                sc_loi_loa b,
                sc_workflow_status c,
                pm_status_desc d,
                sc_loa e,
                sc_workflow_status f,
                pm_status_desc g,
                sc_qt_finalization h
            WHERE
                a.qt_id = b.doc_id
                AND b.loi_loa_id = e.loi_loa_id
                AND e.loa_id = c.doc_id
                AND c.status_id = d.status_id
                AND c.is_current = 1
                AND c.doc_type = 'LA'
                AND c.status_id = 62506
                AND d.language_code = 'en'
                AND a.qt_id = f.doc_id
                AND f.status_id = g.status_id
                AND g.language_code = 'en'
                AND f.is_current = 1
                AND f.doc_type = 'QT'
                AND f.status_id = 60013
                AND c.CREATED_DATE >= to_date('$dateFilter', 'DD/MM/YYYY HH24:MI:SS')
                    AND a.qt_id = h.qt_id
                    AND h.finalization_method_id <> 368)
            AND b.record_status = 1
            AND c.is_cancelled = 0
            AND b.is_loa_awarded IS NULL)";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    /**
     * Get list of PO expired exceed 14days but status still Pending Supplier Acknowledgement
     * @return type
     */
    public function getFlPoExpired()
    {
        $q = "SELECT *
        FROM (
            SELECT TO_CHAR(a.CREATED_DATE, 'DD/MM/YYYY HH12:MI:SS') po_date_created
                ,(
                    SELECT AG_APPROVED_DATE
                    FROM fl_fulfilment_request pr
                    WHERE pr.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    ) AG_APPR
                ,(
                    SELECT TO_DATE(AG_APPROVED_DATE) + 14
                    FROM fl_fulfilment_request pr
                    WHERE pr.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    ) PO_exp
                ,a.doc_no
                ,(
                    SELECT doc_no
                    FROM fl_fulfilment_request o
                    WHERE o.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    ) AS pr_no
                ,(
                    SELECT max(ORG_CODE)
                    FROM pm_org_validity ptj
                    WHERE ptj.ORG_PROFILE_ID = (
                            SELECT pr.prepared_ORG_PROFILE_ID
                            FROM fl_fulfilment_request pr
                            WHERE pr.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                            )
                    ) ptj_code
                ,(
                    SELECT SUPPLIER_NAME
                    FROM fl_supplier_dtl
                    WHERE FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                    ) SUPPLIER_NAME
                ,a.FULFILMENT_ORDER_ID
            FROM fl_fulfilment_order a
                ,fl_workflow_status b
                ,pm_status c
                ,pm_status_desc d
            WHERE a.fulfilment_order_id = b.doc_id
                AND b.status_id = c.status_id
                AND c.status_id = d.status_id
                AND d.language_code = 'en'
                AND b.doc_type IN ('PO')
                AND b.is_current = 1
                AND b.STATUS_ID IN (
                    41500
                    ,41000
                    )
                AND TO_CHAR(a.Created_DATE, 'yyyy') = '2023'
            ORDER BY 1 DESC
            )
        WHERE PO_exp <= SYSDATE
        ORDER BY TO_CHAR(PO_EXP, 'mm')
            ,TO_CHAR(PO_EXP, 'dd') DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }
}
