<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;

class EgpaController extends Controller {

    use OSBService;
    use SSHService;

    public function getDashboardEgpa() {
        return view('dashboard.egpa', []);
    }

    public function checkMonitoringEgpa() {
        $listStatisticTotalETLEgpa = $this->getListStatisticTotalETLEgpa();

        $collect = collect();
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('EPS-001', 'EGPA_SYARIKAT')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('EPS-002', 'EGPA_BIDANG')[0]);

        $html = "";
        $html .= "
                <div class='row'>
                    <div class='col-lg-6'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Data Egpa</strong> Generated today</h2>
                            </div>
                            <table class='table table-borderless table-striped table-vcenter'>
                                <thead>
                                    <tr>
                                        <th>Module</th>
                                        <th>Service Code</th>
                                        <th>Date</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>";

        foreach ($listStatisticTotalETLEgpa as $data) {
            $html .= "
                                    <tr>
                                        <td style='width: 35%;'><strong>$data->modul</strong></td>
                                        <td>$data->service_code</td>
                                        <td>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total</span></td>
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class='col-lg-6'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Files Created & Transferred</strong> (1am every days except Saturday on 7am )</h2>
                            </div>
                                <table class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th>Module</th>
                                            <th>Service Code</th>
                                            <th>Date</th>
                                            <th>Total Created</th>
                                            <th>Total Transferred</th>
                                        </tr>
                                    </thead>
                                <tbody>";

        foreach ($collect as $data) {
            $html .= "
                                    <tr>
                                        <td style='width: 35%;'><strong>$data->modul</strong></td>
                                        <td>$data->service_code</td>
                                        <td style='width:75px'>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total_created</span></td>
                                        <td class='text-center'><span class='badge label-danger'>$data->total_transferred</span></td>    
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function getDashboardBatchOutFolder() {
        $batchName = request()->batch_name;
        $lengthFileName = request()->length_filename;

        if ($batchName == null && $lengthFileName == null) {
            return "Invalid Request";
        }
        $outboundFiles = null; // $this->getListEpBatchFolderOUT($batchName, $lengthFileName);
        $totalOutboundFilesPending = 0; // count($outboundFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        $batchName Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound?batch_name=$batchName&length_filename=$lengthFileName'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP transfer files to Integration Server</small></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

}
