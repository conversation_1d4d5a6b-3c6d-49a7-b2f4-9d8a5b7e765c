@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/interfacelog') }}"> INTERFACE LOG </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/ptj') }}"> PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/kumpptj') }}"> KUMP. PTJ </a>
            </li>
            <li class="active">
                <a href="{{ url('/find/masterdata/pegpengawal') }}"> PEG. PENGAWAL </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/vot') }}"> VOT </a>
            </li>
        </ul>
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/glaccount') }}"> GL ACCOUNT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/programactivity') }}"> PROGRAM ACTIVITY </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/project') }}"> PROJECT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/agoffice') }}"> AG OFFICE </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/dana') }}"> DANA </a>
            </li>
        </ul>
    </div>
</div>
<div class="widget">
    <div class="block-title widget-extra themed-background-dark">
        <div class="widget-extra themed-background-dark">
            <h5 class='widget-content-light'>
                PEGAWAI PENGAWAL - <strong>Master Data</strong>
            </h5>
        </div>
        <div class="block">
            <form id="form-project" action="{{url("/find/masterdata/pegpengawal")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST"/>
                <input name="master_data_type" id="master_data_type" type="hidden" value="PEG_PENGAWAL"/>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="ppcode">Peg. Pengawal Code</label>
                    <div class="col-md-5">
                        <input id="ppcode" name="ppcode" class="form-control" placeholder="Peg. Pengawal Code" type="text" value="{{ old('ppcode') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="ppname">Peg. Pengawal Name </label>
                    <div class="col-md-5">
                        <input id="ppname" name="ppname" class="form-control" placeholder="Peg. Pengawal Name" type="text"  value="{{ old('ppname') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="created_date">Created Date </label>
                    <div class="col-md-5">
                        <input id="created_date" name="created_date" class="form-control" placeholder="Created Date" type="date"
                               value="{{ $date }}" />  
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @if(isset($result))
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="mastertable-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Peg Pengawal ID</th>
                    <th class="text-center">Peg Pengawal Code</th>
                    <th class="text-center">Peg Pengawal Desc</th>
                    <th class="text-center">Code Status</th>
                    <th class="text-center">Process Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Service Code</th>
                    <th class="text-center">File Name</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($result as $data)
                <tr>
                    <td class="text-center"><a href="{{url("/find/masterdata/ep?code=")}}{{ $data->pp_code }}&type=ptj" target="_blank">{{ $data->im_pp_id }}</a></td>
                    <td class="text-center"><a href="{{url("/find/orgcode/")}}/{{ $data->pp_code }}" target="_blank">{{ $data->pp_code }}</a></td>
                    <td class="text-center">{{ $data->pp_desc }}</td>
                    <td class="text-center">{{ $data->code_status }}</td>
                    <td class="text-center">{{ $data->process_status }}</td>
                    <td class="text-center">{{ $data->record_status }}</td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center">{{ $data->changed_date }}</td>
                    <td class="text-center">{{ $data->service_code }} - {{ $data->process_id }}</td>
                    <td class="text-center"><a href="{{url("/find/osb/batch/file")}}/{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</div>

@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
    $('#mastertable-datatable').dataTable({
        order: [0, "desc"],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
</script>  
<script>
    $(document).ready(function () {
        $('.select2').select2();
    });

</script>
@endsection