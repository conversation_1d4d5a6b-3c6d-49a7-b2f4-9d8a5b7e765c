<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Model\Notify\NotifyModel;
use Mail;
use Log;
use Config;
use DB;

class HandleAlertBatchOutboundFileDailySchedule extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleAlertBatchOutboundFileDaily';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This daily monitor - to check files outbound is successfully generated to send Third party Integration ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__ . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $dateTodayFormatted = Carbon::now()->format('Y-m-d');
            $timeHourNow = Carbon::now()->format('H');
            $dayNow = Carbon::now()->format('l');
            $groupWhatAppNotification = 'MONITORING_INTEGRATION';

            $listData = collect($this->listStatisticDailyFileCreatedOutbound());
            MigrateUtils::logDump(__METHOD__ . ' total : '.$listData->count());

            $igfmasList = collect($listData->where('integration_name','IGFMAS')->all());
            if($igfmasList->count() == 0 && $timeHourNow >= 6 ){
                MigrateUtils::logDump(__METHOD__ . 'need alert igfmas, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'IGFMAS');
                self::saveNotify($groupWhatAppNotification,$msg);
            }

            $mygpisList = collect($listData->where('integration_name','MyGPIS')->all());
            if($mygpisList->count() == 0 && $timeHourNow >= 3  && $dayNow != 'Saturday' ){
                MigrateUtils::logDump(__METHOD__ . 'need alert mygpis, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'MyGPIS');
                self::saveNotify($groupWhatAppNotification,$msg);
            }elseif($mygpisList->count() == 0 && $timeHourNow >= 10  && $dayNow == 'Saturday' ){
                MigrateUtils::logDump(__METHOD__ . 'need alert mygpis, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'MyGPIS');
                self::saveNotify($groupWhatAppNotification,$msg);
            }

            $egpaList = collect($listData->where('integration_name','eGPA')->all());
            if($egpaList->count() == 0 && $timeHourNow >= 2 && $dayNow != 'Saturday'  ){
                MigrateUtils::logDump(__METHOD__ . 'need alert egpa, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'eGPA');
                self::saveNotify($groupWhatAppNotification,$msg);
            }elseif($egpaList->count() == 0 && $timeHourNow >= 10 && $dayNow == 'Saturday'  ){
                MigrateUtils::logDump(__METHOD__ . 'need alert egpa, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'eGPA');
                self::saveNotify($groupWhatAppNotification,$msg);
            }

            $epolList = collect($listData->where('integration_name','ePOL')->all());
            if($epolList->count() == 0 && $timeHourNow >= 2 ){
                MigrateUtils::logDump(__METHOD__ . 'need alert epol, no file created');
                $msg = self::msgNotificationAlert($dateTodayFormatted,'ePOL');
                self::saveNotify($groupWhatAppNotification,$msg);
            }

        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));

        }
        
    }
    protected static function msgNotificationAlert($dateProcess,$batchName) {

       //ALERT : This format contains to send in whatapp.
       $msg = "
*[ALERT] INTEGRATION BATCH FILE SCHEDULER*
*DATE CHECKING TIME*
  $dateProcess
*INTEGRATION BATCH NAME*
  $batchName
*ISSUE*
  Please check, as the scheduler did not generate any creation file.";
        return $msg;
    }
    
    /**
     * Just checking log, list batch with total files generated
     */
    protected static function listStatisticDailyFileCreatedOutbound() {
        return $res = DB::connection('oracle_nextgen_rpt')->select("SELECT integration_name,sum(total_files) AS total 
            FROM (
                SELECT 
                CASE 
                    WHEN service_code LIKE 'GFM%' THEN 'IGFMAS'
                    WHEN service_code LIKE 'EPS%' THEN 'eGPA'
                    WHEN service_code LIKE 'GPI%' THEN 'MyGPIS'
                    WHEN service_code LIKE 'LMS%' THEN 'ePOL'
                END integration_name,
                service_code, count(*)  AS total_files 
                FROM OSB_BATCH_FILE 
                WHERE created_date >= trunc(sysdate)
                AND service_code IN (
                    'GFM-350',	'GFM-010',	'GFM-380',
                    'EPS-001',	'EPS-002',
                    'LMS-110',	'LMS-120',	'LMS-130',	'LMS-140',	'LMS-150',
                    'GPI-010',	'GPI-020',	'GPI-030',	'GPI-040',	'GPI-050',	'GPI-070',
                    'GPI-080',	'GPI-090',	'GPI-100',	'GPI-110',	'GPI-120')
                GROUP  BY service_code 
            ) tmp 
            GROUP BY tmp.integration_name");
    }

    protected static function saveNotify($receiver,$msg){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $totNotify = NotifyModel::where('message',$msg)->count();
        if($totNotify > 0){
            MigrateUtils::logDump($clsInfo.'Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring daily eGPA';
            $nty->save();
        }
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected static function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' >> '. $e->getMessage());
            return $e;
        }
    }
    
}
