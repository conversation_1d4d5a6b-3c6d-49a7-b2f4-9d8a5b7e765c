@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>QT Stuck BPM <br>
                <small>QT Stuck List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tiada rekod</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>QT Stuck List </strong>
                        <small></small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">QT ID</th>
                            <th class="text-center">QT NO.</th>
                            <th class="text-center">BPM INSTANCE ID</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">BPM FLOW ID</th>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">STATUS</th>
                            <th class="text-center">TRACKING DIARY STATUS</th>
                            <th class="text-center">DATE PENDING QT APPROVAL</th>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">ACTIONED DATE</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->qt_id }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/bpm/task/docno')}}/{{ $data->qt_no }}" >{{ $data->qt_no }}</a></td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></td>
                                <td class="text-center">{{ $data->composite_bpm }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center">{{ $data->status_name }}</td>
                                <td class="text-center">{{ $data->tracking_diary_status_name }}</td>
                                <td class="text-center">{{ $data->date_pending_qt_approval }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->actioned_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



