<!DOCTYPE html>
<html>

<head>
    <title>QT Accept History Import Notification</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
        }

        .success {
            color: #28a745;
            font-weight: bold;
        }

        .error {
            color: #dc3545;
            font-weight: bold;
        }

        .info-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .info-table th,
        .info-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }

        .info-table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }

        .info-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }

        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 12px;
            color: #666;
        }

        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .badge-success {
            background-color: #d4edda;
            color: #155724;
        }

        .badge-error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>

<body>
    <div class="header">
        <h2>QT Accept History Import Notification</h2>
        <p><strong>Server:</strong> {{ $server_env }}</p>
        <p><strong>Time:</strong> {{ $timestamp }}</p>
    </div>

    @if($is_success)
        <div class="success">
            <span class="status-badge badge-success">Success</span>
            <p>✅ The QT Accept History import completed successfully!</p>
        </div>

        <table class="info-table">
            <thead>
                <tr>
                    <th>Detail</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Records Found</td>
                    <td>{{ number_format($total_records) }}</td>
                </tr>
                <tr>
                    <td>Records Processed</td>
                    <td>{{ number_format($processed_count) }}</td>
                </tr>
                <tr>
                    <td>Success Rate</td>
                    <td>{{ $success_rate }}%</td>
                </tr>
                <tr>
                    <td>Execution Time</td>
                    <td>{{ $execution_time }}</td>
                </tr>
                <tr>
                    <td>Status</td>
                    <td class="success">
                        @if($total_records == 0)
                            No records to process
                        @elseif($processed_count == $total_records)
                            All records processed successfully
                        @else
                            {{ $processed_count }} of {{ $total_records }} records processed
                        @endif
                    </td>
                </tr>
            </tbody>
        </table>
    @else
        <div class="error">
            <span class="status-badge badge-error">Failed</span>
            <p>❌ The QT Accept History import failed!</p>
        </div>

        <table class="info-table">
            <thead>
                <tr>
                    <th>Detail</th>
                    <th>Value</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Command</td>
                    <td><code>{{ $command }}</code></td>
                </tr>
                <tr>
                    <td>Error Message</td>
                    <td class="error">{{ $error_message }}</td>
                </tr>
                <tr>
                    <td>Failed At</td>
                    <td>{{ $timestamp }}</td>
                </tr>
            </tbody>
        </table>

        <div
            style="margin-top: 20px; padding: 15px; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px;">
            <strong>⚠️ Action Required:</strong> Please check the logs for more details and resolve the issue.
        </div>
    @endif

    <div class="footer">
        <hr>
        <p>This is an automated notification from your Laravel application.</p>
        <p>Please do not reply to this email.</p>
    </div>
</body>

</html>