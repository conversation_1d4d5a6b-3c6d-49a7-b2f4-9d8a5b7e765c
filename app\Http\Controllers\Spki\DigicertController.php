<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Spki;

use App\Http\Controllers\Controller;
use Log;
use Illuminate\Http\Request;
use App\Services\Traits\SPKIService;
use App\EpSupportActionLog;
use App\Services\Traits\SupplierService;

class DigicertController extends Controller {

    use SPKIService;
    use SupplierService;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function getChallengeQuestion(Request $request) {

        session()->flashInput(request()->input());
        
        $status = null;
        $resultAction = null;
        $service = 'GetChallengeQuestion';
        $supplierInfo = null;
                
        if ($request->isMethod("POST")) {

            $this->validate($request, [
                'ic_no' => 'required',
                'ep_no' => 'required',
            ]);
            
            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->ic_no;
            
            $result = $this->digicertService($service, $icNumber);
            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            
            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-DG-'.$service, 'Web Service', $actionData, $actionParameter, $status);

        }
        
        if(isset($request->ic_no)){
            $supplierInfo = $this->getSMSupplierUsersByParam($request->ic_no,$request->ep_no);
        }
        

        return view('spki.digicert.challenge_question',[
            'status' => $status,
            'result' => $resultAction,
            'supplier' => $supplierInfo
        ]);
    }

    public function updateChallengeQuestion(Request $request) {
        
        session()->flashInput(request()->input());

        $status = null;
        $resultAction = null;
        $service = 'UpdateChallengeQuestion';

        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ic_no' => 'required',
                'ep_no' => 'required',
            ]);

            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->ic_no;
            $epNumber = $request->ep_no;
            
            $result = $this->digicertService($service, $icNumber, $epNumber);
            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            $actionData->put('ep_number', $epNumber);

            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-DG-'.$service, 'Web Service', $actionData, $actionParameter, $status);

            if ($result["status"] === 'Success') {
                return $result;
            }
        }
        
        return view('spki.digicert.update_challenge_question',[
            'status' => $status,
            'result' => $resultAction
        ]);
    }
    
    public function changePin (Request $request) {
        
        session()->flashInput(request()->input());
        
        $status = null;
        $resultAction = null;
        $service = 'ChangePin';
        
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ic_no' => 'required',
                'ep_no' => 'required',
            ]);
            
            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->ic_no;
            $epNumber = $request->ep_no;

            $result = $this->digicertService($service, $icNumber, $epNumber);
            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            $actionData->put('ep_number', $epNumber);

            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-DG-'.$service, 'Web Service', $actionData, $actionParameter, $status);
            
            if ($result["status"] === 'Success') {
                return $result; 
            }
        }
        
        return view('spki.digicert.change_pin',[
            'status' => $status,
            'result' => $resultAction
        ]);
    }
    
    public function resetPin (Request $request) {
        
        session()->flashInput(request()->input());
        
        $status = null;
        $resultAction = null;
        $service = 'ResetPin';
        
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ic_no' => 'required',
                'ep_no' => 'required',
            ]);
            
            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->ic_no;
            $epNumber = $request->ep_no;
            $email = '<EMAIL>';

            $result = $this->digicertService($service, $icNumber, $epNumber, $email);
            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            $actionData->put('ep_number', $epNumber);
            $actionData->put('email', $email);

            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-DG-'.$service, 'Web Service', $actionData, $actionParameter, $status);

            if ($result["status"] === 'Success') {
                return $result;
            }
        }
        
        return view('spki.digicert.reset_pin',[
            'status' => $status,
            'result' => $resultAction
        ]);
    }
    
    public function signing (Request $request) {
        
        session()->flashInput(request()->input());
        
        $status = null;
        $resultAction = null;
        $service = 'SignIn';
        
        if ($request->isMethod("POST")) {

            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->icNumber;
            $epNumber = $request->epNumber;
            $selectedQuestion = $request->selectedQuestion;

            $result = $this->digicertService('GetChallengeQuestion', $icNumber, $selectedQuestion, $epNumber);

            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            $actionData->put('ep_number', $epNumber);
            $actionData->put('selected_question', $selectedQuestion);

            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-TG-'.$service, 'Web Service', $actionData, $actionParameter, $status);
            
            return $result;
        }
        
        return view('spki.digicert.signing',[
            'status' => $status,
            'result' => $resultAction
        ]);
    }
    
    public function signingData(Request $request) {

        session()->flashInput(request()->input());

        $actionData = collect([]);
        $actionParameter = collect([]);
        $service = 'SignIn';

        $icNumber = $request->icNumber;
        $epNumber = $request->epNumber;
        $question = $request->question;
        $answer = $request->answer;
        $pin = $request->pin;
        $data = $request->data;
        
        $result = $this->digicertService($service, $icNumber, $epNumber,null, $pin, $question, $answer, $data);
        $status = $result["status"];
        
        $actionData->put('ic_number',$icNumber);
        $actionData->put('ep_number',$epNumber);
        $actionData->put('question',$question);
        $actionData->put('answer',$answer);
        $actionData->put('pin',$pin);
        $actionData->put('data',$data);

        $actionParameter->put('result', $result);
        
        EpSupportActionLog::saveActionLog('SPKI-TG-'.$service, 'Web Service', $actionData, $actionParameter, $status);

        return $result;
    }
    
    public function revoke (Request $request) {
        
        session()->flashInput(request()->input());
        
        $status = null;
        $resultAction = null;
        $service = 'RevokeCert';
        
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ic_no' => 'required',
                'ep_no' => 'required',
            ]);
            
            $actionData = collect([]);
            $actionParameter = collect([]);

            $icNumber = $request->ic_no;
            $epNumber = $request->ep_no;

            $result = $this->digicertService($service, $icNumber, $epNumber);
            $status = $result["status"];
            $resultAction = $result["result"];

            $actionData->put('ic_number', $icNumber);
            $actionData->put('ep_number', $epNumber);

            $actionParameter->put('result', $result);

            EpSupportActionLog::saveActionLog('SPKI-DG-'.$service, 'Web Service', $actionData, $actionParameter, $status);

            if ($result["status"] === 'Success') {
                return $result;
            }
        }
        
        return view('spki.digicert.revoke',[
            'status' => $status,
            'result' => $resultAction
        ]);
    }
}
