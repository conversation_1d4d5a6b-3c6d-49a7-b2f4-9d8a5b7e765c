<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SourcingService;
use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\SSHService;
use App\Services\Traits\BPMService;
use Carbon\Carbon;
use SSH;
use Log;
use DB;
use Illuminate\Http\Request;
use App\EpSupportActionLog;
use App\EpSupportOsbLog;
use GuzzleHttp\Client;
use Auth;
use App\Migrate\MigrateUtils;

class GFMASController extends Controller {

    use SupplierService;
    use OSBService;
    use ProfileService;
    use FulfilmentService;
    use SSHService;
    use OSBWebService;
    use SourcingService;
    use BPMService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function wsOSBLog() {
        return view('list_1gfmas', [
            'listdata' => null,
            'listXml' => null,
            'carian' => '']);
    }

    public function searchWsOSBLog($search) {
        $list = array();
        
        /** Ignored search 18 digit refer Item Kod **/
//        if(strlen(trim($search)) == 18){
//            return view('list_1gfmas', [
//                        'listdata' => $list,
//                        'carian' => $search]);
//        }
        
        $checkPRCR = substr($search, 0, 2);
        if($checkPRCR == 'PR' || $checkPRCR == 'CR'){
            $search = $this->getDocNoByPrCr($search);
            if($search == null){
                return view('list_1gfmas', [
                        'listdata' => array(),
                        'carian' => $search]);
            }
        }
        $list = $this->getListOSB1GFMASWebServiceDetails($search);
        return view('list_1gfmas', [
            'listdata' => $list,
            'carian' => $search]);
    }
    
    protected function setApiveData($resSent){
        $collects = collect([]);
        $apiveFilename = $resSent->remarks_1;
        $apoveFileName = $this->getApoveFilename($apiveFilename);
        $apoveServiceCode = 'GFM-370';
        $apoveFileBatch = $this->getBatchFileLogDetail($apoveFileName,$apoveServiceCode); // File received in OSB
        $apoveResp = $this->getApove1GFMASResponseByApivefile($apoveFileName);  // DI_INTERFACE_LOG -> if exist mean file processed in eP.
        
        $aperrFileName = $this->getAperrFilename($apiveFilename);
        $aperrServiceCode = 'GFM-090';
        $aperrFileBatch = $this->getBatchFileLogDetail($aperrFileName,$aperrServiceCode); // File received in OSB

        $status = "No logs info. sent to 1GFMAS";
        if($resSent && strlen($resSent->remarks_1)> 0 ){
            $status = "Successfully sent file to 1GFMAS";    
        }
        //$statusApove = "1GFMAS is not updated yet. Checking file is not found :: ".$apoveFileName;
        $statusApove = "";
        $statusAperr = "";
        $filenameApove = "";
        $filenameAperr = "";
        $isApoveFailedUpdatedInEp = false;
        
        if($apoveFileBatch){
            $statusApove = $apoveFileName." > 1GFMAS success process but this record FAILED updated back in eP."; 
            $isApoveFailedUpdatedInEp = true;
            $filenameApove = $apoveFileName;
            if($apoveResp && strlen($apoveResp->file_name)> 22 ){
                $isApoveFailedUpdatedInEp = false;
                $statusApove = "SUCCESS! ".$apoveResp->file_name." > record has been updated in 1GFMAS and also updated back in eP.";    
                
            }
        }
        if($aperrFileBatch){
            $aperrResp = $this->getAperr1GFMASResponseByApivefile($aperrFileName);
            if($aperrResp && strlen($aperrResp->file_name)> 22 ){
               $statusAperr = "Record has been updated in 1GFMAS as Business Error. Refer file (".$aperrResp->file_name.")";    
               $filenameAperr = $aperrResp->file_name;
            }
        }
        
        if($statusApove == ""){
            $statusApove = "Pending response file APOVE from IGFMAS";
        }
        $collects->put('key', str_replace(".GPG", "",$resSent->remarks_1));
        $collects->put('FileName', $resSent->remarks_1);
        $collects->put('StatusSent', $status);
        $collects->put('StatusApove', $statusApove);
        $collects->put('StatusAperr', $statusAperr);
        $collects->put('InfoDetails', $resSent);
        $collects->put('ApoveDetails', $apoveResp);
        $collects->put('ApoveFileName', $filenameApove);
        $collects->put('ApoveFailedUpdatedInEp', $isApoveFailedUpdatedInEp);
        $collects->put('AperrFileName', $filenameAperr);
        return $collects;
    }
    
    public function getApiveDetails($epNo){

        if($epNo == null || strlen($epNo) == 0 || $epNo == 'insert_epno'){
            return view('apive', [
                'type' => 'apive',
                'carian' => null,
                'result' => null
            ]);
        }
        
        $checkEPNO = substr($epNo, 0, 2);
        if($checkEPNO != 'eP'){
            return view('apive', [
                'type' => 'apive',
                'carian' => $epNo,
                'result' => collect([])
            ]);
        }
        $collection = collect([]);
         
        $listResSentFiles = $this->getApive1GFMASResponseByEpNo($epNo);
        if($listResSentFiles != null && count($listResSentFiles) > 0){
            foreach ($listResSentFiles as $resSent){
                $collects = $this->setApiveData($resSent);
                $collection->push($collects);
            }
        }
        
        if(count($collection) > 0 ){
            /** Getting content file **/
            foreach($collection as $data){
                $filename = $data["FileName"];
                $epNoCheck = $data["InfoDetails"]->remarks_3;
                $data['contents'] = '';
                $data['contentsHasSpecialChar'] = false;
                /***
                 * disabled on 28/01/2024  
                if($epNoCheck != null && strlen($epNoCheck) > 11){
                    $transID = $data["InfoDetails"]->trans_id;
                    $contentsObj = $this->getApiveContents1GFMAS($transID);
                    $data['contentsHasSpecialChar'] = false;
                    if(mb_check_encoding($contentsObj->file_data , 'ASCII') === false){
                        $data['contentsHasSpecialChar'] = true;
                    }
                    $data['contents'] = $contentsObj->file_data;
                }else{

                    $contents = SSH::into('portal')->getString("/batch/Temp/". $filename);
                    $data['contentsHasSpecialChar'] = false;
                    if(mb_check_encoding($contents , 'ASCII') === false){
                        $data['contentsHasSpecialChar'] = true;
                    }
                    $data['contents'] = $contents;
                }
                ***/
            }
        }
        
        return view('apive', [
            'type' => 'apive',
            'carian' => $epNo,
            'result' => $collection,
        ]);
    }
    
    public function checkConnectionGFMAS(){
        $data = array();
        $commands  = [
            "echo 'exit' | sftp -oPort=2022 eperolehan@10.38.206.73",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            //var_dump($result);
            if(trim($result) != 'stdin: is not a tty'){
                array_push($data,trim($result));
            }
            
        });
        return view('telnet', [
            'result' => $data,
        ]);
    }

    public function logGfmasDetailsPoCo(){
        return view('list_gfmas', [
            'listdata' => null,
            'type' => 'poco',
            'carian' => '']);
    }
    public function getLogGfmasDetailsPoCo($docNo) {
        $list = $this->getListOSB1GFMASWebServiceDetails($docNo);
        return $this->getLogGfmasDetails($list,$docNo,'poco');
        
    }

    public function logGfmasDetailsPrCr(){
        return view('list_gfmas', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'prcr',
            'carian' => '']);
    }
    public function getLogGfmasDetailsPrCr($docNo) {
        $pocono = $this->getPoCoNo($docNo);
        if($pocono != null){
            $list = $this->getListOSB1GFMASWebServiceDetails($pocono);
            return $this->getLogGfmasDetails($list,$docNo,'prcr');
        }
        return $this->getLogGfmasDetails(array(),$docNo,'prcr');
    }

    public function logGfmasDetailsMMINF(){
        return view('list_gfmas', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mminf',
            'carian' => '']);
    }
    public function getLogGfmasDetailsMMINF($itemCode) {
        $list = $this->getListOSB1GFMASWebServiceDetails($itemCode);
        return $this->getLogGfmasDetails($list,$itemCode,'mminf');
    }

    protected function getLogGfmasDetails($list, $docNo, $type) {
        $listXml = array();
        foreach($list as $obj){
            if($obj->trans_type == 'IBReq'){
                array_push($listXml,$obj);
            }
        }
        return view('list_gfmas', [
            'listdata' => $list,
            'listXml' => $listXml,
            'type' => $type,
            'carian' => $docNo]);
    }
    
    public function listLogOSB($name) {
        $list = array();
        if($name == 'checkWsValidation'){
          $list = $this->getListWsValidationException();  
        }
        if($name == 'checkWsOsbEjbNoReceiver'){
          $list = $this->getListWsErrNoEJBReceiver();
        }
        if($name == 'checkWsOsbEjbTimeOut'){
          $list = $this->getListWsErrEJBTimeOut(); 
        }
        if($name == 'checkWsItemCodeErrorInGFM100'){
          $list = $this->getListWsErrItemCodeInGFM100(); 
        }
        if($name == 'checkWsItemCodeErrorInMMINF'){
          $list = $this->getListWsErrItemCodeInMMINF();
        }
        return view('list_osb', [
            'listdata' => $list ]);
    }
 
    public function listPendingBatchHTML($type){
        $dataList = array();
        if($type == 'ep-inbound'){
            $dataList = $this->getList1GfmasFolderIN();
        }else if($type == 'ep-outbound'){
            $dataList = $this->getList1GfmasFolderOUT();
            
            $batchName = request()->batch_name;
            $lengthFileName = request()->length_filename;
            if($batchName != null && $lengthFileName != null){
                $dataList = $this->getListEpBatchFolderOUT($batchName, $lengthFileName);
            }
            
        }else if($type == '1gfmas-inbound'){ // Refer to Server 1GFMAS  Folder IN
            $dataList = $this->getList1GfmasServerFolderIN();
        }else if($type == '1gfmas-outbound'){ // Refer to Server 1GFMAS  Folder OUT
            $dataList = $this->getList1GfmasServerFolderOUT();
        }else if($type == 'ep-apove-inbound'){ 
            $dataList = $this->getListAPOVEInFolder();
        }else if($type == 'ep-apive-outbound'){
            $dataList = $this->getListAPIVEOutFolder();
        }else{
           return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Batch is not define properly!  $type</th>
                            </tr>
                        </thead>"; 
        }
        
        return $this->populateBatchFilesHTML($dataList);
    }
    
    public function listBatchHTML($type,$serviceCode,$transDate){
        $dataList = array();
        if($type == 'ep-file-error-inbound'){
            $list = $this->getListFileErrProcessing($serviceCode, $transDate);
            $collection = collect($list);
            $dataList = $collection->pluck('file_name');
        }
        
        if(count($dataList) > 0){
            return $this->populateBatchFilesHTML($dataList);
        }else{
            return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Batch is not define properly!</th>
                            </tr>
                        </thead>";
        }
    }
    
    protected function populateBatchFilesHTML($dataList){
        
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>FileName</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
                $url = url('/find/osb/batch/file');
                $data = "
                    <tr>
                        <td class='text-center'><strong>".++$counter."</strong></td>
                        <td class='text-left'><strong><a href='$url/$value' target='_blank'>
                                        $value</a></strong></td>
                    </tr>";
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
    }
    
    
       public function listBatchFilesPendingHTML($processID){
        $dataList = array();
        if($processID == 'GLSEG'){
            $list = $this->getListFilesPendingGLSEG($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLPTJ'){
            $list = $this->getListFilesPendingGLPTJ($processID);
            $dataList = collect($list);
        }elseif($processID == 'CMBNK'){
            $list = $this->getListFilesPendingCMBNK($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLPRJ'){
            $list = $this->getListFilesPendingGLPRJ($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLVOT'){
            $list = $this->getListFilesPendingGLVOT($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLPRG'){
            $list = $this->getListFilesPendingGLPRG($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLGLC'){
            $list = $this->getListFilesPendingGLGLC($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLPCG'){
            $list = $this->getListFilesPendingGLPCG($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLBAC'){
            $list = $this->getListFilesPendingGLBAC($processID);
            $dataList = collect($list);
        }elseif($processID == 'GLDNA'){
            $list = $this->getListFilesPendingGLDNA();
            $dataList = collect($list);
        }
        
        if(count($dataList) > 0){           
            return $this->populateBatchFilesPendingHTML($dataList);
           
        }else{
            return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Process ID is not define properly!</th>
                            </tr>
                        </thead>";
        }
    }
    
    protected function populateBatchFilesPendingHTML($dataList){
        
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>FileName</th>
                            <th class='text-center'>Total Data</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
                $url = url('/find/osb/batch/file');
                $urlShowDecrypt = url('/find/osb/decrypt/pendingfile');
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a href='$url/$value->file_name' target='_blank'>
                                        $value->file_name</a></strong></td>
                        <td class='text-center'><strong><a href='$urlShowDecrypt/$value->process_id/$value->file_name' target='_blank'>$value->total_data</a></strong></td>  
                    </tr>";
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
    }
        
    public function listOsbServiceRetryHTML($serviceName){
        $dataList = array();
        
            if ($serviceName == 'RequestCertificate') {
                $list = $this->getDetailsOfRequestCertificate($serviceName);
                $dataList = collect($list);
            } else if ($serviceName == 'UpdateSoftCert') {
                $list = $this->getDetailsOfUpdateSoftCert($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'ContractStopInstruction') {
                $list = $this->getDetailsOfContractStopInstruction($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'PHISMasterDataMaterialInformation') {
                $list = $this->getDetailsOfPHISMasterDataMaterialInformation($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'ContractOrderInformation') {
                $list = $this->getDetailsOfContractOrderInformation($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'PaymentInstruction') {
                $list = $this->getDetailsOfContractPaymentInstruction($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'ContractFulfillmentReceivedNote') {
                $list = $this->getDetailsOfContractFulfillmentReceivedNote($serviceName);
                $dataList = collect($list);
            } elseif ($serviceName == 'PHISMasterDataDeliveryAddress') {
                $list = $this->getDetailsOfPHISMasterDataDeliveryAddress($serviceName);
                $dataList = collect($list);
            }elseif ($serviceName == 'AllBatch') {
                $list = $this->getTotalListBatchRetry();
                $dataList = collect($list);
            }elseif ($serviceName == 'AllService') {
                $list = $this->getTotalListServiceRetry();
                $dataList = collect($list);
            } else {
                $list = $this->getDetailsOSBLogByRetryLog($serviceName);
                $dataList = collect($list);
            }

        //  dump($dataList);
        if(count($dataList) > 0){       
            
            return $this->populateOsbServiceRetryHTML($dataList,$serviceName);

        }else{
            return $html =" <thead>
                            <tr>
                                <th class='text-center'>Sorry! Type of Service Name is not define properly!</th>
                            </tr>
                        </thead>";
        }
    }
    
    protected function populateOsbServiceRetryHTML($dataList,$serviceName){
        
        if($serviceName === 'AllService') {
            $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>ID</th>
                            <th class='text-center'>TransID</th>
                            <th class='text-center'>Service Name</th>
                            <th class='text-center'>Target System</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
            $url = url('/find/osb/retry/transid');
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-center'>$value->id</td>
                        <td class='text-center'><strong><a href='$url/$value->trans_id' target='_blank'>$value->trans_id</a></strong></td>
                        <td class='text-center'>$value->service_name</td>
                        <td class='text-center'>$value->target_system</td>
                    </tr>";
                
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
        }elseif($serviceName === 'AllBatch') {
            $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>ID</th>
                            <th class='text-center'>TransID</th>
                            <th class='text-center'>Target System</th>
                            <th class='text-center'>Service Code</th>
                            <th class='text-center'>Service Name</th>
                            <th class='text-center'>File Name</th>
                            <th class='text-center'>Status Desc</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
            $url = url('/find/osb/retry/transid');
            $urlPatch = url('find/patch-ep?patch=OSB_BATCH_RETRY_DTL');
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-center'><strong><a href='$urlPatch&record=$value->id' target='_blank'>$value->id</a></strong></td>
                        <td class='text-center'><strong>$value->trans_id</td>
                        <td class='text-center'>$value->target_system</td>
                        <td class='text-center'>$value->service_code</td> 
                        <td class='text-center'>$value->service_name</td> 
                        <td class='text-center'>$value->file_name</td> 
                        <td class='text-center'>$value->status_desc</td> 
                    </tr>";
                
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
        }else{

            $html =    "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>ID</th>
                            <th class='text-center'>TransID</th>
                            <th class='text-center'>Service Name</th>
                            <th class='text-center'>Service Code</th>
                            <th class='text-center'>Retry Count</th>
                            <th class='text-center'>Date Created</th>
                            <th class='text-center'>Remark 1</th>
                            <th class='text-center'>Remark 2</th>
                            <th class='text-center'>Remark 3</th>
                        </tr>
                    </thead>";
        $html = $html."<tbody>";
        $counter = 0;
        foreach ($dataList as $value){
            $url = url('/find/osb/retry/transid');
            $urlTracking = url('/find/trans/track/docno');
            $urlfindICNoSupplier = url('/find/icno');
            $remarks1 = $value->remarks_1;
            if($value->service_name==='trustGateRequestCertificate' || $value->service_name==='trustGateRequestRenewal'){
               $remarks1 = "<strong><a href='https://digitalid.msctrustgate.com/v2/ePRA/request/detail_request?requestid=$value->remarks_1' target='_blank'>$value->remarks_1</a></strong>"; 
            }
            
            if($value->service_name == 'UpdateSoftCert' || $value->service_name == 'trustGateRequestRenewal' || $value->service_name == 'trustGateRequestCertificate'){
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-center'>$value->id</td> 
                        <td class='text-center'><strong><a href='$url/$value->trans_id' target='_blank'>$value->trans_id</a></strong></td>
                        <td class='text-center'>$value->service_name</td> 
                        <td class='text-center'>$value->service_code</td>
                        <td class='text-center'>$value->retry_count</td>
                        <td class='text-center'>$value->created_date</td>
                        <td class='text-center'>$remarks1</td> 
                        <td class='text-center'><strong><a href='$urlfindICNoSupplier/$value->remarks_2' target='_blank'>$value->remarks_2</a></strong></td>
                        <td class='text-center'><strong><a href='$urlfindICNoSupplier/$value->remarks_3' target='_blank'>$value->remarks_3</a></strong></td>   
                    </tr>";
            }else{
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-center'>$value->id</td> 
                        <td class='text-center'><strong><a href='$url/$value->trans_id' target='_blank'>$value->trans_id</a></strong></td>
                        <td class='text-center'>$value->service_name</td> 
                        <td class='text-center'>$value->service_code</td>
                        <td class='text-center'>$value->retry_count</td>
                        <td class='text-center'>$value->created_date</td>
                        <td class='text-center'>$remarks1</td> 
                        <td class='text-center'><strong><a href='$urlTracking/$value->remarks_2' target='_blank'>$value->remarks_2</a></strong></td>
                        <td class='text-center'>$value->remarks_3</td>
                    </tr>";
            }
                
                $html = $html.$data;
        }
        $html = $html."<tbody>";
        return $html;
        }
        
    }

    public function mminfTriggerView() {
        return view('mminf_trigger', [
            'result' => null
        ]);
    }

    public function searchPreMminf(Request $request) {
        $searchType = $request->searchType;
        $tabName = '';

        $result = null;
        if($searchType == 'search_docno'){
            $docNo = $request->doc_no;
            $itemCode = $request->item_code;
            $tabName = 'search-tab-docno';
            if($docNo) {
                $typeDoc   = substr($docNo, 0, 2);
                if($typeDoc == 'PO' || $typeDoc == 'CO'){
                    $docObj = $this->getDocNoPRPOorCRCO($docNo);
                    if($docObj){
                       $docNo =  $docObj->fr_doc_no;
                    }
                }
                $listReqItem = $this->getMminfReqItemId($docNo, $itemCode);
                $listRequestItemId = collect($listReqItem)->pluck('request_item_id');
                if(count($listRequestItemId) > 0) {
                    $result = $this->getMminfSearchDetailByListReqItemId($listRequestItemId);
                    foreach($result as $objRes){
                        $objMminf = $this->getMminfCodeDetailsByUOM($objRes->extension_code, $objRes->uom_code);
                        $objRes->mminf = $objMminf ;
                    }
                }

            }
        } elseif ($searchType == 'search_reqitemid'){
            $reqItemId = $request->req_item_id;
            $tabName = 'search-tab-reqitemid';
            if($reqItemId) {
                $listDetail = $this->getMminfSearchDetail($reqItemId);
                $result = $listDetail;
                foreach($result as $objRes){
                    $objMminf = $this->getMminfCodeDetailsByUOM($objRes->extension_code, $objRes->uom_code);
                    $objRes->mminf = $objMminf ;
                }
            }
        }

        if($result == null) {
            $result = 'notfound';
        }

        return view('mminf_trigger', [
            'result' => $result,
            'msg' => null,
            'tabName' => $tabName,
            'formSearch' => $request->all()
        ]);
    }
    
    public function mminfTrigger(Request $request) {
        
        $reqItemId = $request->request_item_id;
        $curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(4);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'><trig:ScRequestItem><trig:requestItemId>$reqItemId</trig:requestItemId><trig:changedDate>$now</trig:changedDate></trig:ScRequestItem></trig:ScRequestItemCollection></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';
        $urlBatchOsb = env("BASE_URL_WS_OSB","http://192.168.62.15:8011");
        $urlIdentity = "$urlBatchOsb/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],$reqItemId);
        $result = null;
        SSH::into('osb')->run($commands, function($line) use (&$msg, &$reqItemId, &$result) {});

        sleep(3);
        $result = $this->getMminfSearchDetail($reqItemId);

        $checkChangeDate = Carbon::parse($result[0]->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'success';
            EpSupportActionLog::updateActionLog($actionLog, 'Completed');
        } else {
            $status = 'fail';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed');
        }
        return array('status'=>$status,'value'=>$result[0]->changed_date);

    }

    public function  mminfTriggerByDocNo(Request $request) {
        $docNo = $request->request_item_id;
        Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo);
        $typeDoc   = substr($docNo, 0, 2);
        if($typeDoc == 'PO' || $typeDoc == 'CO'){
            $docObj = $this->getDocNoPRPOorCRCO($docNo);
            if($docObj){
               $docNo =  $docObj->fr_doc_no;
            }
        }
        $listReqItem = $this->getMminfReqItemId($docNo, null);
        $listRequestItemId = collect($listReqItem)->pluck('request_item_id');

        Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));

        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listRequestItemId);

        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->diffInMinutes(Carbon::now()) > 10) {
            /* Trigger Item All to changed date */
            $status = $this->mminfTriggerByListID($listRequestItemId);
            
            if($status == 'Completed'){
                Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: success');
                return array('status'=>'success','value'=>'','total_items'=>count($listRequestItemId));
            }else{
                $status_error = 'Trigger all items failed!';
                Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: '.$status_error);
                return array('status'=>'fail','value'=>'','total_items'=>count($listRequestItemId),'status_error'=>$status_error);
            }
            
        }else{
            $status_error = 'Failed because already updated';
            Log::info(self::class .'>'.__FUNCTION__. ':      DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId). ' status: '.$status_error);
            return array('status'=>'fail','value'=>'','total_items'=>count($listRequestItemId),'status_error'=>$status_error);
        }
            
        
    }
    
    protected function mminfTriggerByListID($listReqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(10);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $ScRequestItemXML = "";
        foreach ($listReqItemId as $reqItemId){
            $ScRequestItemXML = $ScRequestItemXML.
                                "<trig:ScRequestItem>
                                    <trig:requestItemId>$reqItemId</trig:requestItemId>
                                 </trig:ScRequestItem>";
        }

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . $ScRequestItemXML
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';
        $urlBatchOsb = env("BASE_URL_WS_OSB","http://192.168.62.15:8011");
        $urlIdentity = "$urlBatchOsb/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],json_encode($listReqItemId));

        SSH::into('osb')->run($commands, function($line){$line.PHP_EOL; });
        
        sleep(10);
        
        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listReqItemId);

        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
        }else{
            $status = 'Failed';
        }
        
        EpSupportActionLog::updateActionLog($actionLog, $status);
        Log::info(self::class .'>'.__FUNCTION__. ': '.$status);
        
        return $status;
    }
    
    public function displayDashboardMminfDiInterface() {
        $data = $this->getDashboardMminfDiInterfaceLog();

        return view('include.mminf_dash_diinterface', [
            'data' => $data
        ]);

    }

    public function displayDashboardMminfQuartz() {
        $dataFired = $this->getDashboardMMinfQuartz();
        $dataExecution = $this->getDashboardMMinfQuartzExecution();

        if ($dataFired) {
            $dataFired[0]->prev_fired = Carbon::parse($dataFired[0]->prev_fired)->format('d/m/Y H:i:s A');
            $dataFired[0]->next_fired = Carbon::parse($dataFired[0]->next_fired)->format('d/m/Y H:i:s A');
            if($dataFired[0]->trigger_state === 'WAITING') {
                $dataFired[0]->state_color = 'label-warning';
            } elseif ($dataFired[0]->trigger_state === 'BLOCKED') {
                $dataFired[0]->state_color = 'label-danger';
            } else {
                $dataFired[0]->state_color = 'label-info';
            }
        }

        if ($dataExecution) {
            $dataExecution[0]->finish_execution_date = Carbon::parse($dataExecution[0]->finish_execution_date)->format('d/m/Y H:i:s A');
        }

        return view('include.mminf_dash_quartz', [
            'dataFired' => $dataFired,
            'dataExecution' => $dataExecution
        ]);
    }

    public function apiveTriggerView(Request $request) {
        $array = array();
        if(!isset($request->ep_no)){
            $array =  array(
                'ep_no' => ''
            );
        }else{
            $array =  array(
                'ep_no' => $request->ep_no
            );
        }
        return view('apive_trigger', [
            'result' => null,
            'formSearch' => $array 
        ]);
    }

    public function searchPreApive(Request $request) {
        $ePno = $request->ep_no;

        $result = null;
        if($ePno) {
            $listDetail = $this->getApiveTriggerInfo($ePno);
            $result = $listDetail;
        }

        if($result == null) {
            $result = 'notfound';
        }

        return view('apive_trigger', [
            'result' => $result,
            'msg' => null,
            'formSearch' => $request->all()
        ]);
    }

    public function apiveTrigger(Request $request) {
        $ePno = $request->ep_no;
        $curChangedDate = $request->current_changed_date;

        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePno</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';
        $urlBatchOsb = env("BASE_URL_WS_OSB","http://192.168.62.15:8011");
        $urlIdentity = "$urlBatchOsb/TriggerAPIVE/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        $actionLog =  EpSupportActionLog::createActionLog('TriggerAPIVE','Web Service',$commands[0],$request->ep_no);
        $result = null;
        SSH::into('osb')->run($commands, function($line) use (&$msg, &$result) {});

        sleep(3);
        $result = $this->getApiveTriggerInfo($ePno);
        if($result){
            $updatedChangedDate = $result[0]->changed_date;
            if($curChangedDate == $updatedChangedDate) {
                $status = 'fail';
                EpSupportActionLog::updateActionLog($actionLog, 'Failed');
            } else {
                $status = 'success';
                EpSupportActionLog::updateActionLog($actionLog, 'Completed');
            }
        } else {
            $status = 'fail';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed');
        }

        return array('status'=>$status,'value'=>$updatedChangedDate);

    }
    
    public function searchStuckPHIS160() {
        $list = $this->getListStuckTaskChargingStatusPHIS();

        Log::info(self::class .'>'.__FUNCTION__. ' > total Status Pending PR/CR Review from IGFMAS : '.count($list));
        foreach ($list as $objData) {
            $objData->stuck_charging_status = 'NO';
            $objData->bpm_instance = '';
            $objData->bpm_composite = '';
            $objData->transDate100 = '';
            $objData->transDate013 = '';
            $objData->sap_order_no = '';
            
            $epp013Obj = $this->getFirstOsbLogDetail('remarks_1', 'EPP-013', 'OBRes', $objData->doc_no_order);

            if($epp013Obj){
                if ($epp013Obj->status_code == '70022' ) {
  
                    if($epp013Obj->remarks_2 != null && strlen($epp013Obj->remarks_2) == 10){
                        $phs160Obj = $this->getFirstOsbLogDetail('remarks_2', 'PHS-160', 'IBReq', $objData->doc_no_order);
                        if(!$phs160Obj){
                            $objData->stuck_phs160 = 'YES';
                            $objData->sap_order_no = $epp013Obj->remarks_2;
                        }
                    }
                    /*
                    $dateToday = Carbon::now();
                    if($dateToday->hour > 7 && $dateToday->hour < 19){
                        $resListCr = $this->getTaskBpmByDocNo($objData->doc_no_request);
                        if ($resListCr && count($resListCr) > 0) {
                            $objBpm2 = $resListCr->first();
                            if($objBpm2){
                                $objData->bpm_instance = $objBpm2->compositeinstanceid;
                                $objData->bpm_composite = $objBpm2->taskdefinitionid;
                            }
                        }
                    } 
                     
                     */  
                }
            }   
        }
        
        $listData = collect($list);
        $listFilter = $listData->where('stuck_phs160','YES');
        Log::info(self::class .'>'.__FUNCTION__. ' > total stuck task EPP-013 : '.count($listFilter));
        //dump($listData);
        return view('list_gfmas_chargingstatus', ['listdata' => $listFilter]);
    }
    
    public function searchStuckTaskChargingStatus() {
        $list = $this->getListStuckTaskChargingStatus();

        Log::info(self::class .'>'.__FUNCTION__. ' > total Status Pending PR/CR Review from IGFMAS : '.count($list));
        foreach ($list as $objData) {
            $objData->stuck_charging_status = 'NO';
            $objData->bpm_instance = '';
            $objData->bpm_composite = '';
            $objData->transDate100 = '';
            $objData->transDate013 = '';
            $objData->sap_order_no = '';
            
            $queryTracking = DB::connection('oracle_nextgen_rpt')->table('PM_TRACKING_DIARY');
            $queryTracking->where('doc_no', $objData->doc_no_order);
            $checkExistPOCO = $queryTracking->count();
            
            // $checkExistPOCO  to check in tracking diary, should be no POCO doc no existed.
            if($checkExistPOCO == 0 ){
                
                $epp013Obj = $this->getFirstOsbLogDetail('remarks_1', 'EPP-013', 'OBRes', $objData->doc_no_order);

                if($epp013Obj){
                    
                    if ($epp013Obj->status_code == '70022' ) {
                        
                        $gfm100Obj = $this->getFirstOsbLogDetail('remarks_1', 'GFM-100', 'OBRes', $objData->doc_no_order);
                        if ($gfm100Obj) {

                           
                            $transDate100 = Carbon::parse($gfm100Obj->trans_date);
                            $objData->transDate100  = $transDate100;

                            $transDate013 = Carbon::parse($epp013Obj->trans_date);
                            $objData->transDate013 = $transDate013;

                            
                            if ($transDate013->gt($transDate100)) {
                                $objData->stuck_charging_status = 'YES';
                                if($epp013Obj->remarks_2 != null && strlen($epp013Obj->remarks_2) == 10){
                                    $objData->sap_order_no = $epp013Obj->remarks_2;
                                }
                            }
                            
                            /*
                            if($epp013Obj->remarks_2 != null && strlen($epp013Obj->remarks_2) == 10){
                                $objData->stuck_charging_status = 'YES';
                                $objData->sap_order_no = $epp013Obj->remarks_2;
                            }
                            */
                            $dateToday = Carbon::now();
                            //if($dateToday->hour > 7 && $dateToday->hour < 19){
                                $resListCr = $this->getTaskBpmByDocNo($objData->doc_no_request);
                                if ($resListCr && count($resListCr) > 0) {
                                    $objBpm2 = $resListCr->first();
                                    if($objBpm2){
                                        $objData->bpm_instance = $objBpm2->compositeinstanceid;
                                        $objData->bpm_composite = $objBpm2->taskdefinitionid;
                                        $objData->flow_id = $objBpm2->flow_id;
                                    }
                                }
                            //}
                        }    
                    }
                }
            }
            
              
        }
        $listData = collect($list);
        $listFilter = $listData->where('stuck_charging_status','YES');
        Log::info(self::class .'>'.__FUNCTION__. ' > total stuck task EPP-013 : '.count($listFilter));
        return view('list_gfmas_chargingstatus', ['listdata' => $listFilter]);
    }

    public function triggerBPMCallback(Request $request) {
        $docno = $request->doc_no;

        //Status on the list
        //Check GFM100
        //Check EPP-013
        
        
        /*
         curl http://192.168.63.205:7011/TriggerBPMCallback/v1.0 --header "Content-Type: application/xml" -d "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><quer:QueryBPMCallbackInput xmlns:quer='http://xmlns.oracle.com/pcbpel/adapter/db/QueryBPMCallback'><quer:PoCoNo>CO180000000000000,PO180000000000000</quer:PoCoNo></quer:QueryBPMCallbackInput></soapenv:Body></soapenv:Envelope>"
         */
        if ($docno && strlen($docno) > 8) {
            $checkPOCO = substr($docno, 0, 2);
            if($checkPOCO == 'PO' || $checkPOCO == 'CO'){
                
                
                $checkingValid = false;

                /*
                $gfm100Obj = $this->getFirstOsbLogDetail('remarks_1', 'GFM-100', 'OBRes', $docno);
                if ($gfm100Obj) {
                    $transDate100 = Carbon::parse($gfm100Obj->trans_date);
                }
                */

                $epp013Obj = $this->getFirstOsbLogDetail('remarks_1', 'EPP-013', 'OBRes', $docno);
                if ($epp013Obj) {
                    if($epp013Obj->remarks_2 != null && strlen($epp013Obj->remarks_2) == 10){
                        $checkingValid = true;
                    }else{
                        return array('status' => 'failed','doc_no' => $docno,'data'=>'Checking..EPP-013 not as Y (SUCCESS APPROVED), Kindly check manual. trans_id: '.$epp013Obj->trans_id);
                    }
                }

                if($checkingValid == true){
                    $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><quer:QueryBPMCallbackInput xmlns:quer='http://xmlns.oracle.com/pcbpel/adapter/db/QueryBPMCallback'><quer:PoCoNo>$docno</quer:PoCoNo></quer:QueryBPMCallbackInput></soapenv:Body></soapenv:Envelope>";
                    $xmlContents = '"' . $xmlContents . '"';
                    $urlBatchOsb = env("BASE_URL_WS_OSB","http://192.168.62.15:8011");
                    $urlIdentity = "$urlBatchOsb/TriggerBPMCallback/v1.0";
                    $urlHeader = "'Content-Type: application/xml'";

                    $commands = [
                        "curl -k " . $urlIdentity . " --header  " . $urlHeader . "  -d " . $xmlContents,
                    ];
                    $actionLog = EpSupportActionLog::createActionLog('TriggerBPMCallback', 'Web Service', $commands[0], $docno);
                    SSH::into('osb')->run($commands);
                    //SSH::into('osb')->run($commands, function($line)  {
                    //    $data = $line.PHP_EOL;
                    //});

                    sleep(2);
                    $status = 'success';
                    EpSupportActionLog::updateActionLog($actionLog, 'Completed');

                    $objResult = DB::connection('oracle_nextgen_rpt')
                    ->table('PM_TRACKING_DIARY')
                    ->where('DOC_NO',$docno)
                    ->select('tracking_diary_id','action_desc','doc_no','actioned_date')
                    ->orderBy('tracking_diary_id','desc')
                    ->first();
                    return array('status' => $status,'doc_no' => $docno,'data' => $objResult);
                }
                return array('status' => 'failed','doc_no' => $docno,'data'=>'Checking.. EPP-013 is not found');
                
            }
            return array('status' => 'failed','doc_no' => $docno,'data'=>'Not valid as doc type PO/CO');
        }
        return array('status' => 'failed','doc_no' => $docno,'data'=>'');
  
    }

    public function fetchFromFolderDisplay() {
        return view('fetch_1gfmas', [
            'dataList' => null
        ]);
    }

    public function fetchFromFolderList() {
        $dataList = $this->getList1GfmasServerFolderOUT();

        $html = "   <thead>
                    <tr>
                        <th class=\"text-center\">No.</th>
                        <th class=\"text-center\">File Name</th>
                    </tr>
                    </thead>
                    <tbody>";
        $count = 1;
        foreach ($dataList as $data) {
            $html .= "
                        <tr>
                            <td class=\"text-center\"> ". $count++ ." </td>
                            <td class=\"text-center\">". $data ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }
    
    public function fetch1GfmasOutFolder(Request $request) {
        $processId = $request->process_id;
        return $this->callWSProcessFileTransfer($processId, "1GFMAS");
    }
    
    public function processInOutFolder1GFMAS(Request $request) {
        $processId = $request->process_id;
        return $this->callWSProcessFileTransfer($processId, "eP");
    }
    
    
    public function searchListMaterialCode($carian = null){
        $list = array();       
        $carbonToday = Carbon::now();
        $defaultToday = $carbonToday->format('d-m-y');
        if($carian == null){
            //Get list by today created
            $list = $this->getMminfCreatedByToday($defaultToday);  
            $carianTemp = 'Data Hari ini (Terkini 300 rekod)';
        }else{
        
            $carianTemp = trim(strtoupper($carian));
            if(strlen($carianTemp) == 18){
              $list = $this->MminfCodeDetails($carianTemp); 
              
            }
           //  dump($carianTemp. 'Test');
        }
        
        if($list && count($list) > 0){
            foreach($list as $obj){
                $obj->notAsciiMaterialDesc = false;
                if(mb_check_encoding($obj->material_desc, 'ASCII') == false){
                    $obj->notAsciiMaterialDesc   = true;
                }

                $obj->notAsciiMaterialAddDesc  = false;
                if(mb_check_encoding($obj->material_add_desc, 'ASCII') == false){
                    $obj->notAsciiMaterialAddDesc  = true;
                }

            }
        }
        
        return view('list_mminf_material_code', [
            'listdata' => $list,
            'carian' => $carianTemp]);
    }
    
    public function searchMminfDetails($mminfId, $materialCode){
        
        $MminfIDDetails = $this->getDetailMminfID($mminfId, $materialCode);
       
        foreach($MminfIDDetails as $obj){
            $obj->notAsciiMaterialDesc = false;
            if(mb_check_encoding($obj->material_desc, 'ASCII') == false){
                $obj->notAsciiMaterialDesc   = true;
            }
            
            $obj->notAsciiMaterialAddDesc  = false;
            if(mb_check_encoding($obj->material_add_desc, 'ASCII') == false){
                $obj->notAsciiMaterialAddDesc  = true;
            }
            
        }
       
        return view('patch.mminf_details_patch', [
            'listdata' => $MminfIDDetails   
                ]);
    }    
    
    public function searchStuckTaskPendingPRCRReview() {
        $list = $this->getListStuckTaskPendingPRCRReviewFromIgfmas();
       
        return view('list_pending_prcr_review', ['listdata' => $list]);
    }
    
    public function searchStuckTaskPaymentQueryFromIgfmas() {
        $list = $this->getListStuckTaskPaymentQueryFromIgfmas();
       
        return view('list_pending_payment_query_igfmas', ['listdata' => $list]);
    }

    public function searchRedundantSapOrderNo($year, $month) {
        $list = $this->getListRedundantSapOrderNoFromIgfmas($year, $month, $this);
       
        return view('list_redundant_saporderno', ['listdata' => $list]);
    }
    
    public function triggerOsbRetry(Request $request, $type) {
        //dump("triggerOsbRetry");
        $list = null;
        $dataList = null;
        $listTrigger = null;
        $result = null;
        $logStatus = null;
        $arrServiceName = null;
        $listRunning = null;
        $masterStatus = null;
        $paramLog = null;
        $total = 0;
        $array = array();
        $triggerResult = null;
        $status = 'Completed';
        $listTargetWithService = array();
        
        if ($request->isMethod("GET")) {
            //dump('GET triggerOsbRetry');
            //checking running trigger process
            $listRunning = EpSupportOsbLog::checkingRunningTriggerOsb(null, 'Running', $type);
            $paramLog = $listRunning;
            //dump('$paramLog');
            //dump($paramLog);
            if (count($listRunning) > 0) {
                $masterStatus = 'Running';
                
                Log::info(self::class . '> Total Running Process : ' .count($listRunning));
                foreach($listRunning as $list){
                    
                    if(Carbon::now()->diffInMinutes(Carbon::parse($list->created_at)) > 10) {
                        Log::info(self::class . '> Running Process Exceed 10 minutes!! Update Status To Done. ID : ' .$list->id);
                        EpSupportOsbLog::updateOsbLog($list->id, Auth::user()->user_name, 'Done', null, null, 1);
                    }
                    
                }
            }
            if ($type === 'service') {
                $list = $this->getTotalListServiceRetryByServiceName();
            } else {
                $list = $this->getTotalListBatchRetryByServiceName();
            }

            $dataList = collect($list);
            //dump('$dataList');
            //dump($dataList);

            if (count($dataList) > 0) {
                foreach ($dataList as $val) {
                    $total = $total + $val->total;
                    $listService = $this->getServiceByTarget($type,$val->target_system);
                    $target = $val->target_system;
                    array_push($listTargetWithService, array($target => $listService));
                }
            }
            //dump('$listTargetWithService');
            //dump($listTargetWithService);
        } else {
//            dump('POST triggerOsbRetry');
            $srvName = $request->service_name;
            $targetSys = $request->target_system;
            $osbType = $request->type;
            $limit = $request->limit_service;

            //osb service retry
            if ($osbType === 'service') {

                $dataList = collect($list);
                if ($targetSys !== 'All') {
                    if($srvName !== 'All'){
                        $listTrigger = $this->getTotalListServiceRetry($targetSys,$srvName);
                    }else{
                        $listTrigger = $this->getTotalListServiceRetry($targetSys);
                    }
                    
                } else {
                    $listTrigger = $this->getTotalListServiceRetry();
                }

                $dataTrigger = collect($listTrigger);

                $totalRecord = count($dataTrigger);

                $newLimit = 0;
                if ($limit === null) {
                    $newLimit = $totalRecord;
                } else {
                    $newLimit = $limit;
                }
                $masterStatus = 'Running';
                $createRunningLog = EpSupportOsbLog::createRunningLog(Auth::user()->user_name, $newLimit, $masterStatus, $type);
                $paramLog = $createRunningLog;

                $x = 1;
                //$val = $dataTrigger[0];
                foreach ($dataTrigger as $val) {

                    $res = $this->getTotalListServiceRetry(null, null, $val->trans_id);
                    $transId = $res[0]->trans_id;
                    $dtlId = $res[0]->retry_dtl_id;
                    $payload = $res[0]->payload;
                    $serviceName = $res[0]->service_name;
                    
                    if ($x <= $newLimit) {
                        //checking running process in epss log db. if exist, skip 
                        $runningTrans = EpSupportOsbLog::checkingRunningTriggerOsb($transId, 'Processing', $type);

                        if (count($runningTrans) == 0) {
                            $status = 'Processing';
                            array_push($array, $dtlId);
                            
                            //insert to table log first before trigger
                            $osbLog = EpSupportOsbLog::createOsbLog(Auth::user()->user_name, $dtlId, $transId, null, $status, $osbType);

                            $dateTrigger = Carbon::parse($osbLog->updated_at)->format('Y-m-d H:i');

                            $stringPayload = htmlspecialchars($payload);
                            Log::info(__METHOD__ . '  Trigger Service transID :  ' . $transId);
                            //trigger service
                            $result = self::triggerOsbService($stringPayload);
                            sleep(1);
                            if ($result === 'Error') {
                                $masterStatus = 'Done';
                                $status = 'Error';
                            }else{
                                $masterStatus = 'Done';
                                $status = 'Completed';
                            }
                            Log::info(__METHOD__ . '  Status Result trigger  :  ' . $result);
                            //checking in osb_logging ep, to check success trigger or not. 
                            //if record exist within time triggered,then delete record in table osb_retry_dtl

                            $listSuccess = $this->checkingOsbTigger($transId, $dateTrigger);
                            $collect = collect($listSuccess);
                            if (count($collect) > 0) {
                                $masterStatus = 'Done';
                                //delete record 
                                $delete = $this->deleteServiceRetry($dtlId, $transId);
                                if ($delete === 0) {
                                    $status = 'Error';
                                } else {
                                    $status = 'Completed';
                                }
                                //sleep(1);
                                //update log table epss
                                EpSupportOsbLog::updateOsbLog($osbLog->id, Auth::user()->user_name, $status, $transId, $result, $serviceName);
                                $logStatus = 'Success';
                            } else {

                                EpSupportOsbLog::updateOsbLog($osbLog->id, Auth::user()->user_name, $status, $transId, $result, $serviceName);
                                $logStatus = 'Error';
                            }
                        } else {
                            //skip 
                            Log::info(__METHOD__ . ' Skip Trigger Service :  ' . $transId);
                        }
                    }

                    $x++;
                }
            } //osb batch retry
            else {
                $dataList = collect($list);
                if ($targetSys !== 'All') {
                    if($srvName !== 'All'){
                        $listTrigger = $this->getTotalListBatchRetry($targetSys,$srvName);
                    }else {
                        $listTrigger = $this->getTotalListBatchRetry($targetSys);
                    }
                    
                } else {
                    $listTrigger = $this->getTotalListBatchRetry();
                }

                $dataTrigger = collect($listTrigger);

                $totalRecord = count($dataTrigger);

                $newLimit = 0;
                if ($limit === null) {
                    $newLimit = $totalRecord;
                } else {
                    $newLimit = $limit;
                }
                
                $masterStatus = 'Running';
                $createRunningLog = EpSupportOsbLog::createRunningLog(Auth::user()->user_name, $newLimit, 'Running', $type);
                $paramLog = $createRunningLog;

                $x = 1;
                foreach ($dataTrigger as $val) {
                    $res = $this->getTotalListBatchRetry(null ,null, $val->trans_id);
                    $uuid = \Ramsey\Uuid\Uuid::uuid4();
                    $transId = $res[0]->trans_id;
                    $dtlId = $res[0]->batch_retry_dtl_id;
                    $serviceName = $res[0]->service_name;
                    $serviceCode = $res[0]->service_code;
                    $target = $res[0]->target_system;
                    $fileName = $res[0]->file_name;
                    
                    if ($x <= $newLimit) {
                        //checking running process in epss log db. if exist, skip 
                        $runningTrans = EpSupportOsbLog::checkingRunningTriggerOsb($transId, null, $type);

                        if (count($runningTrans) == 0) {
                            $status = 'Processing';
                            array_push($array, $dtlId);
                            //insert to table log first before trigger
                            $osbLog = EpSupportOsbLog::createOsbLog(Auth::user()->user_name, $dtlId, $transId, null, $status, $osbType);

                            $dateTrigger = Carbon::parse($osbLog->updated_at)->format('Y-m-d H:i');

                            //trigger batch here........
                            if ($target === '1GFMAS') { // folder out ep, ep will transfer file to gfmas
                                $resp = self::triggerOsbBatchService($uuid, $serviceCode, $fileName);
                                sleep(1);
                                if ($resp === 'Error') {
                                    $status = 'Error';
                                }else{
                                    $status = 'Completed';
                                }
                            } else if ($target === 'eP') {
                                $resp = self::triggerProcessFileOSB($fileName, $serviceCode);
                                sleep(1);
                                if (isset($resp["error"])) {
                                    $status = 'Error';
                                }else{
                                    $status = 'Completed';
                                }
                            }else{
                                $resp  = json_encode(array(
                                          "target" => $target,
                                          "description"  => "Invalid to process" 
                                        ));
                                $status = 'Error';
                            }
                            
                            $masterStatus = 'Done';
                            //checking in osb_logging ep, to check success trigger or not. 
                            //if record exist within time triggered,then delete record in table osb_retry_dtl

                            $listSuccess = $this->checkingOsbBatchTrigger($uuid, $dateTrigger);
                            $collect = collect($listSuccess);
                            if (count($collect) > 0) {
                                $masterStatus = 'Done';
                                //delete record 
                                $delete = $this->deleteBatchRetry($dtlId, $transId);
                                if ($delete === 0) {
                                    $masterStatus = 'Done';
                                    $status = 'Error';
                                } else {
                                    $status = 'Completed';
                                }
//                                sleep(1);
                                //update log table epss
                                EpSupportOsbLog::updateOsbLog($osbLog->id, Auth::user()->user_name, $status, $transId, $resp, $serviceName);
                                $logStatus = 'Success';
                            } else {

                                EpSupportOsbLog::updateOsbLog($osbLog->id, Auth::user()->user_name, $status, $transId, $resp, $serviceName);
                                $logStatus = 'Error';
                            }
                        } else {
                            //skip 
                            Log::error('skip ' . $runningTrans->trans_id);
                        }
                    }
                    $x++;
                }
                
                
            }
            $completed = 0;
                $error = 0;
                
                foreach($array as $val) {
                    $res = DB::connection('mysql_ep_support')->table('ep_osb_batch_trigger')->where('batch_retry_dtl_id','=',$val)->first();
                    
                    if ($res) {
                        if ($res->status === 'Completed') {
                            $completed++;
                        } else {
                            $error++;
                        }
                    }
                }

                $triggerResult = 'Total : ' .count($array) .' Completed : ' .$completed .' Error ' .$error;
                
                EpSupportOsbLog::updateRunningLog($createRunningLog->id, Auth::user()->user_name, $masterStatus, json_encode($array));
        }

        //json_decode($paramLog)
//        Log::info($paramLog);
        //dump('$masterStatus');
        //dump($masterStatus);
        
        $dataResult = [
            'datalist' => $dataList,
            'total' => $total,
            'type' => $type,
            'arrServiceName' => $arrServiceName,
            'status' => $logStatus,
            'masterStatus' => $masterStatus,
            'process' => json_decode($paramLog),
            'triggerResult' => $triggerResult,
            'listTargetWithService' => $listTargetWithService
        ];
        //dump('$dataResult');
        //dump($dataResult);
        return view('osb.trigger_osb', $dataResult);
    }

    public static function handleOsbServiceRetry($createdDate=null,$target=null,$serviceName=null,$limitRecord=null,$fromDateTime=null,$toDateTime=null) {
        MigrateUtils::logDump(__METHOD__ . " entering.. >> createdDate: $createdDate , target: $target, serviceName: $serviceName, limitRecord: $limitRecord, fromDateTime: $fromDateTime, toDateTime: $toDateTime");
        $thisClass = new GFMASController;
        if($thisClass->isConnectionIgfmasWsConnected() === false){
            MigrateUtils::logErrorDump(__METHOD__ . ' >> Check Connection WS IGFMAS Is Failed!');
            return "Failed to do retry!";
        }

        $now = Carbon::now()->format('Y-m-d H:i');

        $whereRangeDateTime = '';
        if($fromDateTime != null && strlen($fromDateTime)==16 
            && $toDateTime != null && strlen($toDateTime)==16 ){
            
            $whereRangeDateTime = "AND CREATED_DATE >= to_date('$fromDateTime','YYYY-MM-DD HH24:MI') AND  CREATED_DATE <= to_date('$toDateTime','YYYY-MM-DD HH24:MI') ";
            
        }
        $whereDate = '';
        if($createdDate !== '') {
            $whereDate = " AND to_char(CREATED_DATE, 'YYYY-MM-DD') = '$createdDate'";
        }
        
        if($limitRecord !== '') {
            $take = $limitRecord;
        }else {
            $take = 200;
        }
        if ($target !== null) {
            if ($serviceName !== null) {
                $query = "SELECT distinct TRANS_ID, SERVICE_NAME, TARGET_SYSTEM,CREATED_DATE "
                    . "FROM OSB_RETRY_DTL WHERE TARGET_SYSTEM = '$target' AND SERVICE_NAME = '$serviceName'
                    $whereDate  $whereRangeDateTime AND ROWNUM < $take ORDER BY CREATED_DATE";
            } else {
                $query = "SELECT distinct TRANS_ID, SERVICE_NAME, TARGET_SYSTEM, CREATED_DATE "
                    . "FROM OSB_RETRY_DTL WHERE TARGET_SYSTEM = '$target' $whereDate  $whereRangeDateTime AND ROWNUM <= $take  ORDER BY CREATED_DATE";
            }
        } else {
            $query = "SELECT distinct TRANS_ID, SERVICE_NAME, TARGET_SYSTEM , CREATED_DATE FROM OSB_RETRY_DTL WHERE ROWNUM < $take $whereDate  $whereRangeDateTime  ORDER BY CREATED_DATE";
        }
        //MigrateUtils::logDump(__METHOD__ . ' Query : '.$query);
        $results = DB::connection('oracle_nextgen_fullgrant')->select($query);
        $totalResult = count($results);
        MigrateUtils::logDump(__METHOD__ . ' Total result found : '.$totalResult);
       
        if($totalResult > 0) {
            $counter = 1;
            foreach ($results as $val) { 
                MigrateUtils::logDump(__METHOD__ . " $counter/$totalResult)  Get Payload Trans ID: $val->trans_id with service name: $val->service_name");
                $counter++;

                $querys = "SELECT TRANS_ID, PAYLOAD, SERVICE_NAME, TARGET_SYSTEM FROM OSB_RETRY_DTL WHERE TRANS_ID = '$val->trans_id' AND SERVICE_NAME = '$serviceName' $whereDate";
                $resultTransID = DB::connection('oracle_nextgen_fullgrant')->select($querys);
                MigrateUtils::logDump(__METHOD__ . ' Total Trans ID in osb_retry_table : '.count($resultTransID));
                if(count($resultTransID) > 0) {
                    $stringPayload = htmlspecialchars($resultTransID[0]->payload);
                    MigrateUtils::logDump(__METHOD__ . ' Trigger Trans ID '.$val->trans_id);
                    try {
                        $uuid = \Ramsey\Uuid\Uuid::uuid4();
            
                        $client = new Client([
                            'base_uri' => 'http://192.168.63.205:7011',
                        ]);
            
                        $body = '
                                        <x:Envelope
                                        xmlns:x="http://schemas.xmlsoap.org/soap/envelope/"
                                        xmlns:ser="http://www.ep.gov.my/Schema/1-0/ServiceRetry"
                                        xmlns:epm="http://www.ep.gov.my/Schema/1-0/epmf">
                                        <x:Header/>
                                        <x:Body>
                                        <ser:EPMFRq>
                                                <epm:RqHeader>
                                                    <epm:ConsumerID>EPP-001</epm:ConsumerID>
                                                    <epm:UID>
                                                        <epm:RqUID>' . $uuid . '</epm:RqUID>
                                                    </epm:UID>
                                                </epm:RqHeader>
                                                <ser:ServiceRetryRq>
                                                    <ser:XMLString>' . $stringPayload . '</ser:XMLString></ser:ServiceRetryRq>
                                            </ser:EPMFRq>
                                            </x:Body>
                                    </x:Envelope>';
            
                        $response = $client->post('/ServiceRetry/v1.0', [
                            //'debug' => TRUE,
                            'body' => $body, //payload must be auto convert/format before trigger.. not complete yet
                            'headers' => [
                                'Content-Type' => 'text/xml; charset=utf-8',
                                'SOAPAction' => 'http://www.ep.gov.my/Schema/1-0/ServiceRetry',
                            ]
                        ]);
            
                        //$resultTrigger = $response->getBody();
                        $resultTrigger = json_decode($response->getBody(), true);
                    } catch (\Exception $ex) {
                        MigrateUtils::logErrorDump(__METHOD__ . ' ::  ERROR' . $ex->getMessage());
                        $resultTrigger = 'Error';
                    }
                    //var_dump(__METHOD__ . ' resultTrigger : '.$resultTrigger );
                    //sleep(1);
                    if ($resultTrigger === 'Error') { 
                    }else{
                        sleep(1);
                        $queryCheckSuccess = "SELECT trans_id FROM OSB_LOGGING WHERE TRANS_ID = '".$resultTransID[0]->trans_id."'
                        AND trans_type = 'OBRes'
                        AND service_code = 'EPP-600'
                        AND to_char(trans_date,'YYYY-MM-DD HH24:MI') >= '$now'"; 
                        $resultSuccess = DB::connection('oracle_nextgen_fullgrant')->select($queryCheckSuccess);
                        MigrateUtils::logDump(__METHOD__ . ' Check Total Trans ID in osb_logging : '.count($resultSuccess));
                        if(count($resultSuccess) > 0) {
                            MigrateUtils::logDump(__METHOD__ . ' Success Trigger Trans ID '.$resultTransID[0]->trans_id);
                            // delete duplicate trans_id also.
                            $queryDeleteService = DB::connection('oracle_nextgen_fullgrant')
                                ->table('OSB_RETRY_DTL')
                                //->where('retry_dtl_id', $resultTransID[0]->retry_dtl_id)
                                ->where('service_name',$serviceName)
                                ->where('trans_id', $resultTransID[0]->trans_id);
                            $deleteRes = $queryDeleteService->delete();
                            MigrateUtils::logDump(__METHOD__ . ' Delete Trans ID '.$resultTransID[0]->trans_id .'. in osb_retry_dtl table >> ' .$deleteRes);
                            sleep(1);
                        }else{
                            MigrateUtils::logDump(__METHOD__ . ' Failed Trigger Trans ID not found in OSB_LOGGING '.$val->trans_id);
                        }
                    } 
                }
                
            }
            
        }
    }
    
    public static function handleOsbServiceRetryByAgo($target=null,$agoCode=null,$serviceName=null,$limit=null,$docNo=null) {
        MigrateUtils::logDump(__METHOD__ . " entering.. >> target: $target, agoCode: $agoCode, serviceName: $serviceName, limit: $limit, docNo: $docNo");
        $thisClass = new GFMASController;
        if($thisClass->isConnectionIgfmasWsConnected() === false){
            MigrateUtils::logErrorDump(__METHOD__ . ' >> Check Connection WS IGFMAS Is Failed!');
            return "Failed to do retry!";
        }
        $whereDocNo = "";
        if($limit == ''){
            $whereLimit = " AND ROWNUM <= 50";
        }else{
            $whereLimit = " AND ROWNUM <= $limit";
        }
        if($serviceName == 'FulfillmentReceivingNote' || $serviceName == 'POContractForGoodsAndServices') {
            if($docNo != '') {
                $whereDocNo = " AND o.remarks_1 = '$docNo'";
            }
            $query = "SELECT 
                ao.OFFICE_code,av.OFFICE_NAME ,
                d.TRANS_ID, d.RETRY_DTL_ID, d.SERVICE_NAME, d.TARGET_SYSTEM, d.payload,
                o.remarks_1,o.remarks_2,o.remarks_3 
                FROM OSB_RETRY_DTL d,
                OSB_LOGGING o,
                fl_fulfilment_request pr ,
                fl_fulfilment_order po,
                pm_ag_office ao ,
                pm_ag_validity av
                WHERE o.TRANS_ID = d.TRANS_ID 
                AND o.trans_type = 'IBReq' 
                AND d.target_system = '$target'
                AND d.SERVICE_NAME = '$serviceName'
                AND ao.OFFICE_code = '$agoCode'
                $whereDocNo
                $whereLimit
                AND pr.ag_office_id = ao.ag_office_id
                AND ao.ag_office_id = av.ag_office_id
                AND pr.fulfilment_req_id = po.fulfilment_req_id
                AND ao.record_status = 1
                AND av.record_status = 1
                AND po.doc_no = o.remarks_1";
        } else if($serviceName == 'DebitAdviceNote' || $serviceName == 'StopInstruction' || $serviceName == 'PaymentInstruction'){
            if($docNo != '') {
                $whereDocNo = " AND o.remarks_2 = '$docNo'";
            }
             $query = "SELECT 
                ao.OFFICE_code,av.OFFICE_NAME ,
                d.TRANS_ID, d.RETRY_DTL_ID, d.SERVICE_NAME, d.TARGET_SYSTEM, d.payload,
                o.remarks_1,o.remarks_2,o.remarks_3 
                FROM OSB_RETRY_DTL d,
                OSB_LOGGING o,
                fl_fulfilment_request pr ,
                fl_fulfilment_order po,
                pm_ag_office ao ,
                pm_ag_validity av
                WHERE o.TRANS_ID = d.TRANS_ID 
                AND o.trans_type = 'IBReq' 
                AND d.target_system = '$target'
                AND d.SERVICE_NAME = '$serviceName'
                AND ao.OFFICE_code = '$agoCode'
                $whereDocNo
                $whereLimit
                AND pr.ag_office_id = ao.ag_office_id
                AND ao.ag_office_id = av.ag_office_id
                AND pr.fulfilment_req_id = po.fulfilment_req_id
                AND ao.record_status = 1
                AND av.record_status = 1
                AND po.doc_no = o.remarks_2";
        } else {
            MigrateUtils::logDump(__METHOD__ . " Invalid Service Name: $serviceName");
            return "Invalid Service Name!";
        }  
        $result = DB::connection('oracle_nextgen_rpt')->select($query); 
        $now = Carbon::now()->format('Y-m-d H:i');
        $totalResult = count($result);
        if($totalResult> 0) {
            $counter = 1;
            foreach($result as $res) {
                $payload = htmlspecialchars($res->payload);
                $transId = $res->trans_id;
                $serviceName = $res->service_name;
                $agoCode = $res->office_code;

                MigrateUtils::logDump(__METHOD__ . " $counter/$totalResult)  Trans ID: $transId with service name: $serviceName for AGO: $agoCode");
                $counter++;
                
                $triggerService = $thisClass->triggerOsbService($payload);
                if ($triggerService === 'Error') { 
                    MigrateUtils::logDump(__METHOD__ . " Error Trigger Service For Trans ID: $transId");
                }else{
                    sleep(3);
                    $queryCheckSuccess = "SELECT trans_id FROM OSB_LOGGING WHERE TRANS_ID = '".$transId."'
                    AND trans_type = 'OBRes'
                    AND service_code = 'EPP-600'
                    AND to_char(trans_date,'YYYY-MM-DD HH24:MI') >= '$now'"; 
                    $resultSuccess = DB::connection('oracle_nextgen_fullgrant')->select($queryCheckSuccess);
                    MigrateUtils::logDump(__METHOD__ . ' Check Total Trans ID in osb_logging : '.count($resultSuccess));
                    if(count($resultSuccess) > 0) {
                        MigrateUtils::logDump(__METHOD__ . ' Success Trigger Trans ID '.$transId);
                        // delete duplicate trans_id also.
                        $queryDeleteService = DB::connection('oracle_nextgen_fullgrant')
                            ->table('OSB_RETRY_DTL')
                            //->where('retry_dtl_id', $resultTransID[0]->retry_dtl_id)
                            ->where('service_name',$serviceName)
                            ->where('trans_id', $transId);
                        $deleteRes = $queryDeleteService->delete();
                        MigrateUtils::logDump(__METHOD__ . ' Delete Trans ID '.$transId .'. in osb_retry_dtl table >> ' .$deleteRes);
                        sleep(1);
                    }else{
                        MigrateUtils::logDump(__METHOD__ . ' Failed Trigger Trans ID not found in OSB_LOGGING '.$transId);
                    }
                }
            } 
        } 
    }

    protected function isConnectionIgfmasWsConnected(){

        $serviceUrl = "https://10.38.238.69:6030/ws/ag.eai.common.ws.pub.provider.wsProcessCommonReqSvc/ag_eai_common_ws_pub_provider_wsProcessCommonReqSvc_Port?wsdl";
        MigrateUtils::logDump(__METHOD__ . ' Check WSDL service IGFMAS '.$serviceUrl);
        $resultData = $this->checkConnectionService($serviceUrl);
        
        $collection = collect($resultData);

        $filtered = $collection->filter(function ($value, $key) {
            return $value != 'stdin: is not a tty';
        });

        $result = $filtered->all();

        foreach ($result as $data) {
            if (str_contains($data, 'connected')) {
                return true;
            } 
        }

        return false;
    }

    protected function triggerOsbService($payload) {
        $result = null;
        try {
            $uuid = \Ramsey\Uuid\Uuid::uuid4();

            $client = new Client([
                'base_uri' => 'http://192.168.63.205:7011',
            ]);

            $body = '
                            <x:Envelope
                            xmlns:x="http://schemas.xmlsoap.org/soap/envelope/"
                            xmlns:ser="http://www.ep.gov.my/Schema/1-0/ServiceRetry"
                            xmlns:epm="http://www.ep.gov.my/Schema/1-0/epmf">
                            <x:Header/>
                            <x:Body>
                            <ser:EPMFRq>
                                    <epm:RqHeader>
                                        <epm:ConsumerID>EPP-001</epm:ConsumerID>
                                        <epm:UID>
                                            <epm:RqUID>' . $uuid . '</epm:RqUID>
                                        </epm:UID>
                                    </epm:RqHeader>
                                    <ser:ServiceRetryRq>
                                        <ser:XMLString>' . $payload . '</ser:XMLString></ser:ServiceRetryRq>
                                </ser:EPMFRq>
                                </x:Body>
                        </x:Envelope>';

            $response = $client->post('/ServiceRetry/v1.0', [
                //'debug' => TRUE,
                'body' => $body, //payload must be auto convert/format before trigger.. not complete yet
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction' => 'http://www.ep.gov.my/Schema/1-0/ServiceRetry',
                ]
            ]);

            $result = $response->getBody();
        } catch (\Exception $ex) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ERROR' . $ex->getMessage());
            $result = 'Error';
        }

        return $result;
    }

    public function triggerOsbBatchService($uuid, $serviceCode, $fileName) {
        $result = null;
        try {
            Log::debug(self::class . '>' . __FUNCTION__ . ':      File Name ' . $fileName . ' Service Code : ' . $serviceCode . ' UUID : ' . $uuid);

                $client = new Client([
                'base_uri' => 'http://192.168.63.205:7012',
            ]);

            $body = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
                    . "<x:Header/>"
                    . "<x:Body>"
                    . "<bat:EPMFRq>"
                    . "<epm:RqHeader>"
                    . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                    . "<epm:UID>"
                    . "<epm:RqUID>$uuid</epm:RqUID>"
                    . "</epm:UID>"
                    . "</epm:RqHeader>"
                    . "<bat:BatchOutboundRq>"
                    . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                    . "<bat:Filename>$fileName</bat:Filename>"
                    . "</bat:BatchOutboundRq>"
                    . "</bat:EPMFRq>"
                    . "</x:Body>"
                    . "</x:Envelope>";

            $response = $client->post('/Batch/Generic/Outbound/v1.4', [
                //'debug' => TRUE,
                'body' => $body,
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction' => 'http://www.ep.gov.my/Service/1-0/Batch/invoke',
                ]
            ]);
//            Log::info('try triggerOsbBatchService');
            $result = $response->getBody();
        } catch (\Exception $ex) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ERROR'.$ex->getMessage());
            $result = 'Error';
        }        
        
        return $result;
    }

    public function triggerProcessFileOSB($fileName, $serviceCode) {
        
        $result = collect([]);
        $result->put("file_name",$fileName);  
        $result->put("service_code",$serviceCode );  
        
        $interfaceLog = $this->getDetailDiInterfaceLog($fileName);
        if($interfaceLog == null){
            //2nd check filename is exist in BatchFile Log
            $objFile = $this->getBatchFileLog($fileName);
            if($objFile != null){
                $totalFound = $this->countFiles1GFMASFolderIN($fileName);
                if($totalFound > 0){
                    $listTransId = $this->getTransIDInterfaceLog($serviceCode);
                    if(count($listTransId) > 0){
                        $transInterfaceLog = $listTransId->random();
                        $result->put("trans_id",$transInterfaceLog->trans_id ); 
                        //$xmlContents = $thisClass->callCurlWSCallBackGfmasIN($interfaceLog->trans_id, $serviceCode, $fileName); 
                        $xmlContents = $this->callWSCallBackGfmasIN($transInterfaceLog->trans_id, $serviceCode, $fileName); 
                       
                        sleep(15);
                        $result->put("success","trigger process file in 'batch>>1GFMAS>>IN' " );  
                        $result->put("xml",$xmlContents );  
                        $result->put('data',  $this->getDetailDiInterfaceLog($fileName)); // Get response after success in di_interface_log
                    }else{
                       $result->put("error","Not trans_id available to pickup in di_interface_log " );    
                    }
                }else{
                  $result->put("error","Not found in folder 'batch/1GFMAS/IN' " );  
                }
            }else{
                $result->put("error","Not found in osb_batch_file" );
            }
        }else{
            $result->put("error","Data already exist in di_interface_log" );
        }

        return $result;
    }

}
