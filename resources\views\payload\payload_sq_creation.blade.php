@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianforms" action="{{url('/find/simplequote')}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection


@section('content')
    <style>
        .table thead > tr > th {
            font-size: 11px;
        }

        .table tbody > tr > td {
            font-size: 10px;
        }
    </style>
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Simple Quote Creation<br>
                <small>Masukkan <span class="text-info">No. SQ </span> pada carian diatas...</small>
            </h1>
        </div>
    </div>

    @if($simpleQuoteDetails == null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-list-alt"></i>
                        <strong>Payload bagi Carian: </strong>{{$carian}}
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">

                    </div>
                </div>
            </div>

        </div>
        </div>
    @endif


    @if($simpleQuoteDetails != null)
        <div class="block block-alt-noborder full">

            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-list-alt"></i>
                        <strong>Payload bagi Carian: </strong>{{$carian}}
                    </h1>
                </div>

                <!--SC_Direct_Purchase Block-->
                <div class="block">

                    <div class="block-title">
                        <h2>SCDirectPurchaseData </h2>
                    </div>


                    <?php

                    $title = str_replace("&", "&amp;", $simpleQuoteDetails->title);
                    $ptjName = str_replace("&", "&amp;", $orgDetails[0]->ptjname);
                    $ptjAddress = str_replace("&", "&amp;", $orgDetails[0]->ptjaddress);
                    $xmlOrder = '<SC_DirectPurchase_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_DirectPurchase_Data">
    <title>' . $title . '</title>
    <dp_id>' . $simpleQuoteDetails->dp_id . '</dp_id>
    <rn_id>' . $simpleQuoteDetails->rn_id . '</rn_id>
    <pid_id/>
    <dp_mode>SQ</dp_mode>
    <url_link/>
    <ptj_name>' . $ptjName . '</ptj_name>
    <ptj_address>' . $ptjAddress . '</ptj_address>
    <recommend>user011</recommend>
    <contract_id/>
    <contract_type/>
    <frequency/>
    <loi_loa_creator/>
    <start_date>' . $simpleQuoteDetails->start_date . '+08:00</start_date>
    <end_date>' . $simpleQuoteDetails->end_date . '+08:00</end_date>
    <rn_document_number>' . $simpleQuoteDetails->request_note_no . '</rn_document_number>
    <contract_number/>
    <contract_request/>
    <sq_request/>
    <pid_expiry_date/>
    <pid_expiry_duration/>
    <pid_approver/>
    <kpi_value/>
    <sq_document_number/>
    <pid_document_number/>
    <desk_officer/>
    <sq_expiry_date/>
    <sq_expiry_duration/>
</SC_DirectPurchase_Data>';

                    ?>
                    <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($xmlOrder) }}</code>
            </pre>
                    <br/><br/>
                </div>

                <div class="block">
                    <div class="block-title">
                        <h2>document_number</h2>
                    </div>

                    {{$simpleQuoteDetails->quote_no}}<br/><br/>
                </div>

                <div class="block">
                    <div class="block-title">
                        <h2>task_performer</h2>
                    </div>

                    {{$taskPerformerDetails->login_id}}<br/><br/>
                </div>

            </div>


        </div>

    @endif


@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function () {
            TablesDatatables.init();
        });</script>
    <script>

        $('#page-container').removeAttr('class');
    </script>
@endsection



