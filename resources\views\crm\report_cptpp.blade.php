@extends('layouts.guest-dash')

@section('header')
<style type="text/css">  
.dt-buttons {
    display: none;
}

</style>
@endsection

@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">  
        <div class="block">
            <div class="block-title">
                <h2><strong>PEMANTAUAN CPTPP</strong></h2>  
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card"> 
                        <div class="card-header"> 
                        </div>
                        <div class="card-body">
                            <p>Pelaporan jumlah aduan berkenaan CPTPP</p> 
                            <a href="#">
                                <input type="button" style="background-color: black; color: white; padding: 5px" id="download" name="download" value="DOWNLOAD" />
                            </a>
                            <div class="table-responsive">
                                <table class="table" id="cptpptable">
                                    <thead class="text-darken">
                                        <tr>
                                            <th class="text-left" style="width:5%">No.</th> 
                                            <th class="text-left">CRM#</th>
                                            <th class="text-left">SIR#</th>
                                            <th class="text-left">Account Type</th>
                                            <th class="text-left">Aduan Type</th>
                                            <th class="text-left">Module</th>
                                            <th class="text-left">Problem</th>
                                            <th class="text-left">Created Date</th>
                                            <th class="text-left">Status</th> 
                                            <th class="text-left not-export-col">Cptpp Flag</th> 
                                        </tr> 
                                    </thead>
                                    <tbody>
                                        @foreach ($data as $indexKey => $row)
                                        <tr>
                                            <td class="text-left"> {{ ++$indexKey }} </td>
                                            <td class="text-left"> {{ $row->caseNo }} </td>
                                            <td class="text-left"> {{ $row->redmine }} </td>
                                            <td class="text-left"> {{ $row->acctTypeName }} </td>
                                            <td class="text-left"> {{ $row->catName }} </td>
                                            <td class="text-left"> {{ $row->subCatName }} </td>
                                            <td class="text-left"> {{ $row->problem }} </td>
                                            <td class="text-left"> {{ $row->dateCreated }} </td>
                                            <td class="text-left"> {{ $row->statusName }} </td>
                                            <td class="text-left"> {{ $row->cptppFlag }} </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> 
    </div> 
</div> 
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page --> 

<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();</script>  
<script>

    $('#page-container').removeAttr('class');
    $('#cptpptable').DataTable({
        dom: "Blfrtip",
        buttons: [
            {
                text: 'csv',
                extend: 'csvHtml5',
                exportOptions: {
                    columns: ':visible:not(.not-export-col)'
                }
            },
            {
                text: 'excel',
                extend: 'excelHtml5',
                exportOptions: {
                    columns: ':visible:not(.not-export-col)'
                }
            }
        ],
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']],
    });

    $('#download').on('click', function () {
        var table = $('#cptpptable').DataTable();
        table.button('.buttons-csv').trigger();
    });
</script>

@endsection