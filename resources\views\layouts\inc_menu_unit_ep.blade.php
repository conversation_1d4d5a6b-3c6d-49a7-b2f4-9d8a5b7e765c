
{{-- GROUP UNIT EP  ONLY --}}
@if(Auth::user()->isUsersEpOperation()) 
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Support"><i class="gi gi-global"></i></a></span>
        <span class="sidebar-header-title">eP </span>
    </li>
    @if(Auth::user()->checkIfUserHasRole('Approver'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-building sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Pembekal</span></a>
        <ul>
            <li>
                <a href="{{url("/find/mofno")}}"><PERSON><PERSON></a>
            </li>
            <li>
                <a href="{{url("/find/icno")}}">Carian IC No.</a>
            </li>
            <li>
                <a href="{{url("/find/byname")}}">Carian Nama Pembekal</a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="hi hi-user sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Carian Pengguna</span></a>
        <ul>
            <li>
                <a href="{{url("/find/identity")}}">Carian Identity</a>
            </li>
            <li>
                <a href="{{url("/find/userlogin")}}">Carian User Login</a>
            </li>
        </ul>
    </li>
    @endif

    @if(Auth::user()->checkIfUserHasRole('Approver') || Auth::user()->checkIfUserHasRole('Group ePA2') || Auth::user()->checkIfUserHasRole('Group ePA3'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-building sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Organisasi</span></a>
        <ul>
            <li>
                <a href="{{url("/find/orgcode")}}">Carian Org Code</a>
            </li>
            <li>
                <a href="{{url("/find/org/icno")}}">Carian IC No.</a>
            </li>
        </ul>
    </li>
    <li class="{{ Request::is('dashboard/data/lookup') ? 'active' : '' }}">
        <a href="{{url("/dashboard/data/lookup")}}"><i class="fa fa-table sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Data Lookup</span></a>
    </li> 
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">GPKI</span></a>
        <ul>
            <li>
                <a href="{{url("gpki/test")}}">Test</a>
            </li>
            <li>
                <a href="{{url("gpki/sign")}}">Sign</a>
            </li>
            <li>
                <a href="{{url("gpki/verify")}}">Verify</a>
            </li>
        </ul>
    </li>
    @endif 

    @if(Auth::user()->checkIfUserHasRole('Approver'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-list sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Item</span></a>
        <ul>
            <li>
                <a href="{{url("/find/uom")}}">Carian UOM</a>
            </li>
            <li>
                <a href="{{url("/find/item")}}">Carian Items </a>
            </li>
            <li>
                <a href="{{url("/find/items/unspsc")}}">Carian UNSPSC Items </a>
            </li>
            <li>
                <a href="{{url("/find/items/supplier")}}">Carian Item Pembekal </a>
            </li>
            <li>
                <a href="{{url("/find/items/codi-task")}}">Item Task History </a>
            </li>
        </ul>
    </li>
    @endif

    @if(Auth::user()->checkIfUserHasRole('Approver') || Auth::user()->checkIfUserHasRole('Group ePA2') || Auth::user()->checkIfUserHasRole('Group ePA3'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Sourcing & Fulfillment</span></a>
        <ul>
            <li class="{{ Request::is('find/trans/track/docno/') ? 'active' : '' }}">
                <a href="{{url("/find/trans/track/docno")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Tracking Diary Document</span></a>
            </li>
            <li class="{{ Request::is('find/trans/track/user/') ? 'active' : '' }}">
                <a href="{{url("/find/trans/track/user")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Tracking Diary User</span></a>
            </li>
            <li class="{{ Request::is('find/dp-summary/') ? 'active' : '' }}">
                <a href="{{url("/find/dp-summary/")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">DP Summary</span></a>
            </li>
            <li class="{{ Request::is('find/fn-summary/') ? 'active' : '' }}">
                <a href="{{url("/find/fn-summary/")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL Summary</span></a>
            </li>
            <li class="{{ Request::is('trace/log/') ? 'active' : '' }} hide">
                <a href="{{url("/trace/log/")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">User Activity Log</span></a>
            </li>
        </ul>
    </li>
    @endif

    @if(Auth::user()->checkIfUserHasRole('Approver') || Auth::user()->checkIfUserHasRole('Group ePA2') || Auth::user()->checkIfUserHasRole('Group ePA3'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-file-text sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Quotation Tender</span></a>
        <ul>
            <li class="">
                <a href="{{url("/qt/dashboard")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT Dashboard</span></a>
            </li>                                        
            <li class="">
                <a href="{{url("/find/qt/summary")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Summary QT</span></a>
            </li>
            <li class="">
                <a href="{{url("/find/qt/loastatus")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Check LOA Status</span></a>
            </li>
            <li class="">
                <a href="{{url("/find/qt/suppresp")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Check Extend QT </span></a>
            </li>
            <li class="">
                <a href="{{url("/find/qt/stucksummary")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT User Involvement</span></a>
            </li>
        </ul>
    </li>
    @endif

    @if(Auth::user()->checkIfUserHasRole('Approver'))
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-file-text sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Contract</span></a>
        <ul>

            <li class="{{ Request::is('find/contract/') ? 'active' : '' }}">
                <a href="{{url("/find/contract")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Carian Kontrak</span></a>
            </li>
            <li class="">
                <a href="{{url("/find/contract/committee")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Ahli Jawatan Kuasa</span></a>
            </li>

        </ul>
    </li>


    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Prod Support</span></a>
        <ul>
            <li class="{{ Request::is('prod-support/data-patching') ? 'active' : '' }}">
                <a href="{{url("/prod-support/data-patching")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Data Patch Request</span></a>
            </li>
        </ul>
    </li>

    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Laporan</span></a>
        <ul>
            <li class="{{ Request::is('find/prod-support/report_001_byyear') ? 'active' : '' }}">
                <a href="{{url("/prod-support/rpt/report_001_byyear")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Laporan Perbendaharaan</span></a>
            </li>
            <li class="{{ Request::is('prod-support/rpt/summary') ? 'active' : '' }}">
                <a href="{{url("/prod-support/rpt/summary")}}"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Laporan Permohonan</span></a>
            </li>
            <li class="{{ Request::is('report/loginSummaryReport') ? 'active' : '' }}">
                <a href="{{url("/report/loginSummaryReport")}}">
                    <i class="gi gi-charts sidebar-nav-icon"></i>
                    Laporan eP Login</a>
            </li>
            <li class="{{ Request::is('activity/admin/ep') ? 'active' : '' }}">
                <a href="{{url("/activity/admin/ep")}}">
                    <i class="gi gi-notes_2 sidebar-nav-icon"></i>
                    Activity Admin eP</a>
            </li>
            <li class="{{ Request::is('report/ep/integration') ? 'active' : '' }}">
                <a href="{{url("/report/ep/integration")}}">
                    <i class="gi gi-charts sidebar-nav-icon"></i>
                    Laporan Integrasi</a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">eP Log Trace</span></a>
        <ul>
            <li class="{{ Request::is('/log/dashboard') ? 'active' : '' }}">
                <a href="{{url("/log/dashboard")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Dashboard</span></a>
            </li>
        </ul>
    </li>
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Integration"><i class="fa fa-wrench"></i></a></span>
        <span class="sidebar-header-title">eP Integration</span>
    </li>
    <li class="{{ Request::is('dashboard/main') ? 'active' : '' }}">
        <a href="{{url("/dashboard/main")}}"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Dashboard</span></a>
    </li>


    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">OSB</span></a>
        <ul>

            <li>
                <a href="{{url("/find/osb/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/batch/file")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Batch File Log</span></a>
            </li>
            <li>
                <a href="{{url("/osb/file/content/search")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Find Content File</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/detail/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log Details</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/detail-rquid/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log RQ-UID</span></a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Master Data</span></a>
        <ul>
            <li class="{{ Request::is('find/masterdata/ptj') ? 'active' : '' }}">
                <a href="{{url("/find/masterdata/ptj")}}"><i class="gi gi-package sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Master Data</span></a>
            </li>
            <li class="{{ Request::is('find/masterdata/ep') ? 'active' : '' }}">
                <a href="{{url("/find/masterdata/ep")}}"><i class="gi gi-package sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">EP Data</span></a>
            </li>
        </ul>    
    </li>
    @endif
</ul>
@endif
{{-- END GROUP IT SUPPORT  ONLY --}}




