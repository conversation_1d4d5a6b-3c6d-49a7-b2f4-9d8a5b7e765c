ssh epssadmin@192.168.62.132
# su root


# yum --enablerepo=remi,remi-php56 install php php-xml
# yum --enablerepo=remi,remi-php56 install php php-mysql
# yum --enablerepo=remi,remi-php56 install php php-mbstring
# yum --enablerepo=remi,remi-php56 install php php-mysqlnd
# yum --enablerepo=remi,remi-php56 install php php-mssql
# yum --enablerepo=remi,remi-php56 install php php-imap
# yum --enablerepo=remi,remi-php56 install php php-gd php-xmlrpc php-mcrypt php-fpm php-opcache php-apcu
# yum --enablerepo=remi,remi-php56 install php pcre-devel gcc make
# yum --enablerepo=remi,remi-php56 install php php-devel
# yum --enablerepo=remi,remi-php56 install php php-pear


# vi /etc/php.d/40-apcu.ini

								apc.enabled=1
								apc.ttl=72000
								apc.gc_ttl=3600
								apc.shm_size=512M
					[SET BASED ON ABOVE : SAVE]
					
# sudo vi /etc/php.ini
find upload_max_filesize & post_max_size
		change to 
		upload_max_filesize = 50M
		post_max_size = 50M
[SAVE FILE]

After running sudo yum --enablerepo=remi,remi-php56 install php-devel
open the file in the following location: /usr/include/php/main/php_config.h
A solution would be to comment it out again.

/* Defined to 1 if PHP OCI8 DTrace support was enabled during configuration */
/* #define HAVE_OCI8_DTRACE 1 */



Download package rpm oracle :-
	http://www.oracle.com/technetwork/topics/linuxx86-64soft-092277.html
		 oracle-instantclient12.2-basic-12.2.0.1.0-1.x86_64.rpm
		 oracle-instantclient12.2-devel-12.2.0.1.0-1.x86_64.rpm

Manual download and copy paste to server at /root/Download 

# cd /root/Download		 
# rpm -Uvh oracle-instantclient12.2-basic-12.2.0.1.0-1.x86_64.rpm
# rpm -Uvh oracle-instantclient12.2-devel-12.2.0.1.0-1.x86_64.rpm

# pecl install oci8-2.0.12

After successful # pecl install oci8-2.0.12
configuration option "php_ini" is not set to php.ini location
You should add "extension=oci8.so" to php.ini


# curl -sS https://getcomposer.org/installer | php

Move it to /usr/local/bin/
# mv composer.phar /usr/local/bin/composer

# cd /srv/www/
# sudo git clone https://<EMAIL>/cdc-middleware/ep-support.git
# chown -R epssadmin:epssadmin ep-support
# su epssadmin
# cd ep-support
# composer install
# cp .env.example .env


CREATE DATABASE 
# mysql -u epssadmin -p
mysql> show databases;
mysql> create database ep_support;
Query OK, 1 row affected (0.00 sec)

mysql> create user 'epss_user'@'%' identified by 'ePss@2018';
mysql>	grant SELECT on ep_support.* to 'epss_user' identified by 'ePss@2018';

TO avoid error  " which is not functionally dependent on columns in GROUP BY clause; this is incompatible with sql_mode=only_full_group_by " : use this command. by default mysql 5.7 will set as sql_mode=only_full_group_by
mysql> SET GLOBAL sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''));
			
			
mysql> exit
Bye

# cd /srv/www/ep-support
# mysql -u epssadmin -p ep_support < ep-support-structure.sql
mysql> use ep_support;
mysql> show tables;
+----------------------+
| Tables_in_ep_support |
+----------------------+
| ep_action_log        |
| ep_activity_access   |
| ep_login_history     |
| ep_monitor_qt        |
| ep_payment_failed    |
| ep_task              |
| ep_task_category     |
| ep_task_missing      |
+----------------------+
8 <USER> <GROUP> set (0.00 sec)

# mysql -u root -p 
mysql> SHOW VARIABLES LIKE "query_cache%";
			query_cache_type             | OFF     
			query_cache_wlock_invalidate | OFF 
mysql> SHOW STATUS LIKE "Qcache%";


# sudo cp /etc/my.cnf /etc/my.cnf.ori
# sudo vi /etc/my.cnf

## Added for perf.
query-cache-type = 1
query-cache-size = 32000000

innodb_buffer_pool_size = 250000000
innodb_file_per_table

long-query-time = 0
slow-query-log = 1
slow-query-log-file = /var/log/mysql/queries.log



# sudo systemctl stop mysqld
# sudo systemctl start mysqld
# mysql -u root -p 
mysql> SHOW VARIABLES LIKE "query_cache%";
		query_cache_type             | OFF     
		query_cache_wlock_invalidate | OFF 
mysql> SHOW STATUS LIKE "Qcache%";


ADDED CONFIG
# sudo vi /etc/my.cnf

## Added for perf.
query-cache-type = 1
query-cache-size = 32000000

innodb_buffer_pool_size = 250000000
innodb_file_per_table

long-query-time = 0
slow-query-log = 1
slow-query-log-file = /var/log/mysql/queries.log


#  sudo systemctl stop mysqld
#  sudo systemctl start mysqld


# mysql -u root -p 
Try Recheck Again
mysql> SHOW VARIABLES LIKE "query_cache%";
+------------------------------+----------+
| Variable_name                | Value    |
+------------------------------+----------+
| query_cache_limit            | 1048576  |
| query_cache_min_res_unit     | 4096     |
| query_cache_size             | 32000000 |
| query_cache_type             | ON       |
| query_cache_wlock_invalidate | OFF      |
+------------------------------+----------+
5 <USER> <GROUP> set (0.01 sec)



EDIT CONNECTION DB 
# vi .env

DB_MYSQL_EP_SUPPORT_HOST=localhost
DB_MYSQL_EP_SUPPORT_PORT=3306
DB_MYSQL_EP_SUPPORT_DATABASE=ep_support
DB_MYSQL_EP_SUPPORT_USERNAME=epssadmin
DB_MYSQL_EP_SUPPORT_PASSWORD=cDc@2018


DB_HOST=***************
DB_PORT=3306
DB_DATABASE=cdccrm
DB_USERNAME=crm_user
DB_PASSWORD=cDccRm@2017


TESTING CONNECTION to MYSQL DB

[epssadmin@epss01 ep-support]$ php artisan tinker
Psy Shell v0.9.6 (PHP 5.6.36 — cli) by Justin Hileman
>>> DB::connection('mysql_ep_support')->table('ep_login_history')->count();
=> 0
>>> DB::table('users')->count();
=> 663


TEST EMAIL 
[epssadmin@epss01 ep-support]$ php artisan test-mail
"done send"




SETUP NGINX WITH PHP

as reference Follow instruction https://www.digitalocean.com/community/tutorials/how-to-install-linux-nginx-mysql-php-lemp-stack-on-centos-7 

# sudo vi /etc/php.ini
Change to 
cgi.fix_pathinfo=0
[ SAVE ]

# sudo vi /etc/php-fpm.d/www.conf

[main-site]
;listen = 127.0.0.1:9001
listen = /var/run/php-fpm/php-fpm.sock
#listen.owner = nginx
#listen.group = nginx
#user = nginx
#group = nginx
listen.owner = epssadmin
listen.group = epssadmin
user = epssadmin
group = epssadmin
request_slowlog_timeout = 5s
slowlog = /var/log/php-fpm/main.log
listen.allowed_clients = 127.0.0.1
pm = dynamic
pm.max_children = 10
pm.start_servers = 3
pm.min_spare_servers = 2
pm.max_spare_servers = 4
pm.max_requests = 400
listen.backlog = -1
pm.status_path = /status
request_terminate_timeout = 120s
#rlimit_files = 65536
rlimit_core = unlimited
catch_workers_output = yes
php_value[session.save_handler] = files
php_value[session.save_path] = /var/lib/php/session
php_admin_value[error_log] = /var/log/php-fpm/main-error.log
php_admin_flag[log_errors] = on
request_terminate_timeout = 800


[SAVE]

# sudo systemctl start php-fpm

Next, enable php-fpm to start on boot:
# sudo systemctl enable php-fpm



# cd /etc/nginx/
# mkdir conf.extra
# mkdir sites-available
# mkdir sites-enabled

Backup original 
# cp nginx.conf  nginx.conf.ori

Copy setting below: 
# vi nginx.conf 



user epssadmin;
worker_processes auto;
pid /run/nginx.pid;

# Load dynamic modules. See /usr/share/nginx/README.dynamic.
include /usr/share/nginx/modules/*.conf;

events {
    worker_connections 1024;
}

http {
    server_names_hash_bucket_size 64;

    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    access_log  /var/log/nginx/access.log  main;

    sendfile            on;
    tcp_nopush          on;
    tcp_nodelay         on;
    keepalive_timeout   65;
    types_hash_max_size 2048;
    server_tokens off;

    gzip on;
    gzip_disable "MSIE [1-6]\.(?!.*SV1)";
    gzip_http_version 1.1;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_buffers 16 8k;
    gzip_types text/plain text/css application/json application/x-javascript text/xml application/xml application/xml+rss text/javascript application/javascript text/x-js;


    include             /etc/nginx/mime.types;
    default_type        application/octet-stream;


    # enabled sites
    fastcgi_read_timeout 800;
    include /etc/nginx/sites-enabled/*;


}


[ SAVE ]

# cd conf.extra/
# vi caches.conf

## caches
location ~* \.(jpg|jpeg|gif|css|png|js|ico|html)$ {
    access_log off;
    expires max;
}
location ~* \.(js)$ {
    access_log  off;
    log_not_found   off;
    expires     1d;
}
location ~* \.(woff|svg)$ {
    access_log  off;
    log_not_found   off;
    expires     30d;
}


[ SAVE ]


# cd sites-available/
# vi default

server {
        listen       80 default_server;

        server_name  _;
        root         /usr/share/nginx/html;
        index index.php index.html index.htm

        # Load configuration files for the default server block.
        include /etc/nginx/default.d/*.conf;

        location / {
                try_files $uri $uri/ =404;
        }

        error_page 404 /404.html;
            location = /40x.html {
        }

        error_page 500 502 503 504 /50x.html;
            location = /50x.html {
        }

        location ~ \.php$ {
                try_files $uri =404;
                fastcgi_pass unix:/var/run/php-fpm/php-fpm.sock;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                include fastcgi_params;
        }
    }

		
[ SAVE ]	

	
# vi ep_support

#server {
        #listen 80;
        #server_name ep_support;
#}


server {
        listen 80;

        #listen 443 ssl;
        #ssl     on;
        #ssl_certificate /etc/nginx/cert/star_eperolehan_com_my.pem;
        #ssl_certificate_key     /etc/nginx/cert/star_eperolehan_com_my.key;

        server_name epss;
        access_log /var/log/nginx/epss.log;
        error_log /var/log/nginx/epss-error error;

        root /srv/www/ep-support/public;
        index  index.html index.php;

        ### root directory ###
        location / {
            try_files $uri $uri/ /index.php?$args;
            #try_files $uri $uri/ /index.php?is_args$args;
        }

        ### security ###
        error_page 403 =404;

        ### security ###
        location ~* ^/uploads/.*.(html|htm|shtml|php)$ {
                types { }
                default_type text/plain;
        }

        ### disable logging ###
        location = /robots.txt { access_log off; log_not_found off; }
        location = /favicon.ico { access_log off; log_not_found off; }

        ### caches ###
        include /etc/nginx/conf.extra/caches.conf;

        ### php block ###
        location ~ \.php?$ {
                try_files $uri /index.php =404;

                fastcgi_split_path_info ^(.+\.php)(.*)$;
                fastcgi_pass unix:/var/run/php-fpm/php-fpm.sock;
                fastcgi_index index.php;
                fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
                include fastcgi_params;
                fastcgi_intercept_errors on;

                #fastcgi_buffering on;
                #fastcgi_keep_conn on;
                #Prevent version info leakage
                fastcgi_hide_header X-Powered-By;
        }
}


[ SAVE ]


# cd ../sites-enabled/ 

# ln -s ../sites-available/ep_support


# sudo systemctl restart nginx 


2) /var/run/php-fpm/php-fpm.sock  -set this execution permission. 
# chown epssadmin:epssadmin /var/run/php-fpm/php-fpm.sock   
			 
3) please check Selinux should be permission
# setenforce Permissive  


Test DB Connection 
# cd /srv/www/ep-support 
# php artisan tinker 

--- Testing on ngeprpt user:ngep_crm
>>> DB::connection('oracle_nextgen');
=> Yajra\Oci8\Oci8Connection {#1069}       --> Expected Result Success 

--- Testing on ngeprpt user:NGEP_READ
>>> DB::connection('oracle_nextgen_rpt');
=> Yajra\Oci8\Oci8Connection {#1062}       --> Expected Result Success 

--- Testing on PDS APP user:ngep_pds
>>> DB::connection('oracle_nextgen_fullgrant');
=> Yajra\Oci8\Oci8Connection {#1082}       --> Expected Result Success 

--- Testing on PDS SOA user:pdssoa 
>>> DB::connection('oracle_bpm_rpt');
=> Yajra\Oci8\Oci8Connection {#1055}       --> Expected Result Success 

>>>  exit
