@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class="widget-extra themed-background-dark">
                <h5 class='widget-content-light'>
                    Service Management - <strong>IT Coordinator</strong>
                </h5>
            </div>
            <div id="dash_service_itcoord">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
 </div>
 <div class="row">
     <div class="col-lg-12">
         <div class='widget'>
             <div class='widget-extra themed-background-dark center-block'>
                 <h5 class='widget-content-light'>
                     Case Assigned to <strong>Group IT Specialist : </strong>      
                 </h5>
             </div>
             <div class='text-center' style="background: #ffffff; padding: 5px;">  
                <button type="submit" id="button_submit_NA" name="button_submit_M" class="btn btn-sm btn-info text-center" value="bb59c521-4a4d-a001-a8aa-58d09f694ae7">
                        <div class="h5 mb-0" style="font-weight: 800">NA</div>
                        <span>Network Admin</span>
                    </button>                 
                    <button type="submit" id="button_submit_D" name="button_submit_D" class="btn btn-sm btn-info text-center" value="5dac7b92-18d0-4beb-b600-413957aa4c26">
                        <div class="h5 mb-0" style="font-weight: 800">D</div>
                            <span>Developer</span>
                    </button>
                    <button type="submit" id="button_submit_PS" name="button_submit_PS" class="btn btn-sm btn-info text-center" value="d3bf216c-122b-4ce5-9410-899317762b60"> 
                        <div class="h5 mb-0" style="font-weight: 800">PS</div>
                            <span>Production Support</span>
                    </button>
                    <button type="submit" id="button_submit_M" name="button_submit_M" class="btn btn-sm btn-info text-center" value="5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e">
                        <div class="h5 mb-0" style="font-weight: 800">M</div>
                            <span>Middleware</span>
                    </button>
                    <button type="submit" id="button_submit_SA" name="button_submit_SA" class="btn btn-sm btn-info text-center" value="825b06f5-d0b6-e6da-0d56-58d0a0bca1e4">
                        <div class="h5 mb-0" style="font-weight: 800">SA</div>
                        <span>Server Admin</span>
                    </button>
                    <button type="submit" id="button_submit_DA" name="button_submit_DA" class="btn btn-sm btn-info text-center" value="2d567f7a-1cf1-9892-ac97-58d0a0eac2ad">
                        <div class="h5 mb-0" style="font-weight: 800">DA</div>
                        <span>Database Admin</span>
                    </button>
                    <button type="submit" id="button_submit_DM" name="button_submit_DM" class="btn btn-sm btn-info text-center" value="3d74f2af-f673-4263-afc0-d3f89aadf0ef">
                        <div class="h5 mb-0" style="font-weight: 800">DM</div>
                        <span>Database Management</span>
                    </button>
                    <button type="submit" id="button_submit_S" name="button_submit_S" class="btn btn-sm btn-info text-center" value="86dde498-da2d-4208-a7dd-786980e6827a">
                        <div class="h5 mb-0" style="font-weight: 800">S</div>
                        <span>Security</span>
                    </button>
                </div>
             <div id="dash_other_assignedto_itspec">
                 <div class="text-center"><i class=""></i></div>
             </div>
         </div>
     </div>
     </div>
     
    
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $.ajax({
            url: APP_URL + '/dashboard/crm/incident',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_incident').hide().html($data).fadeIn();
            }
        });   
        $.ajax({
            url: APP_URL + '/dashboard/crm/service/itcoord',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_service_itcoord').hide().html($data).fadeIn();
            }
        }); 
        $.ajax({
            url: APP_URL + '/dashboard/crm/incident/itspec',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_incident_itspec').hide().html($data).fadeIn();
            }
        }); 
//        $.ajax({            
//            url: APP_URL + '/dashboard/crm/other/itspec',
//            type: "GET",
//            success: function (data) {
//                $data = $(data);
//                $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
//            }
//        });
        $('#button_submit_D').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_M').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_NA').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_PS').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_SA').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_DA').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_DM').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_S').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/other/itspec',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_other_assignedto_itspec').hide().html($data).fadeIn();
            }
            });
        });
        $.ajax({
            url: APP_URL + '/dashboard/crm/incident/itspec/redmine',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_incident_redmine').hide().html($data).fadeIn();
            }
        });        
    });

</script>
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
