<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Tasks extends Model {
    protected $table = "tasks";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    public function taskCustom() {
        return $this->hasOne('App\TaskCustom', 'id_c');
    }
    
    public function cases()
    {
        return $this->hasOne('App\Cases','id');
    }
}