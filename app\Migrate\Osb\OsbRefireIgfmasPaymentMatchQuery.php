<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Osb;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\FulfilmentService;

class OsbRefireIgfmasPaymentMatchQuery {
    use BpmApiService;
    use OSBWebService;
    use FulfilmentService;

    public static function resendStuckPaymentMatchQuery() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $listPoco = OsbRefireIgfmasPaymentMatchQuery::getListPaymentMatchQueryStuck();
        $totalRec = count($listPoco);
        MigrateUtils::logDump(__METHOD__ . ' total stuck  :  '.$totalRec);
        $counterFoundResend = 1;
        foreach($listPoco as $obj){
            self::resendPaymentMatchQueryByDocNo($obj->doc_no,$counterFoundResend);
            sleep(3);
        }
        MigrateUtils::logDump(__METHOD__ . ' completed');
        
    }

    public static function resendPaymentMatchQueryByDocNo($docNo,&$counterFoundResend) {
        MigrateUtils::logDump(__METHOD__ . ' with  : '.$docNo);
        $listDocs = OsbRefireIgfmasPaymentMatchQuery::getListOsbCorrelation($docNo);
        $totalRec = count($listDocs);
        MigrateUtils::logDump(__METHOD__ . ' found correlation record docno  : '.$docNo. ' >> '.$totalRec );
        // If two or more records, We want to make sure only one record exists in docNo in corr1 fields.
        if($totalRec > 1){
            MigrateUtils::logDump(__METHOD__ . ' found correlation record docno  : '.$docNo. ' >> '.$totalRec . ' ready to update append docNo DEL');
            $counter = 1;
            foreach($listDocs as $row ){
                if($counter < $totalRec ){
                    // Update append doc number extra DEL. This to make sure correlation program in OSB only take one record to send response in queue OSB.
                    $dateCreatedObj = Carbon::parse($row->created_date);
                    $dateFormatted = $dateCreatedObj->format('Y-m-d H:i');
                    $isPermenantDelete = false;
                    self::deleteOsbCorrelation($docNo,$dateFormatted,$isPermenantDelete);
                    $counter++;
                }
            }
        }

        if($totalRec > 0){
            $list = OsbRefireIgfmasPaymentMatchQuery::getListPaymentMatchQueryStuck($docNo);
            if(count($list) > 0 ){
                $obj = $list[0];
                MigrateUtils::logDump(__METHOD__ . "$counterFoundResend) resend epp-017 doc number : ".$docNo);
                self::sendWebServicePaymentInstructionsQueryEpp017($obj->payload);
                $counterFoundResend++;
            }else{
                MigrateUtils::logDump(__METHOD__ . ' please check not found get payload for doc number : '.$docNo);
            }
        }else{
            MigrateUtils::logDump(__METHOD__ . ' please check, not found get osb correlation for : '.$docNo);
        }

        MigrateUtils::logDump(__METHOD__ . ' completed resend for doc number : '.$docNo);
    }

    public static function resendPaymentInstructionsQueryEpp017($docNo) {
        MigrateUtils::logDump(__METHOD__ . ' docNo : '.$docNo);

        //checking created date status  ('41026','41526')
        
        $currentflowObj = $cls ->getCurrentWorkflowByPoCo($docNo);
        if ($currentflowObj != null 
                && ( $currentflowObj->status_id == 41026 || $currentflowObj->status_id == 41526  ) 
            ) {
                
                $objEpp017 = self::getLatestPayloadEpp017($docNo);
                if($objEpp017  != null ){
                    $transDate = Carbon::parse($objEpp017->trans_date);
                    $statusCreatedDate = Carbon::parse($currentflowObj->status_created_date);
                    if($transDate->gt($statusCreatedDate)){
                        self::sendWebServicePaymentInstructionsQueryEpp017($objEpp017->payload);
                    }else{
                        MigrateUtils::logDump(__METHOD__ . ' payload created is old than status PO/CO created : '.$docNo);
                    }
                }
        } else {
            MigrateUtils::logDump(__METHOD__ . ' workflow not valid to trigger : '.$docNo);
        }
          

        
    }  
    
    public static function sendWebServicePaymentInstructionsQueryEpp017($payload) {
        MigrateUtils::logDump(__METHOD__ . ' start sending.... ');
         // resend web service http://192.168.63.205:7011/PaymentInstructionsQuery/v1.0?wsdl
         $cls = new OsbRefireIgfmasPaymentMatchQuery;
         $urlWebService = '/PaymentInstructionsQuery/v1.0';
         $soapAction = 'urn:PaymentInstructionsQuery/inquire';
         $res = $cls->sendWebServicePayloadBody($urlWebService,$soapAction,$payload);
         MigrateUtils::logDump(__METHOD__ . ' result response  :  '.json_encode($res));
    }  

    /**
     * program to handle all record duplicate, then remove duplicate
     */
    public static function removeDuplicateOsbCorrelation($year, $month) {
        MigrateUtils::logDump(__METHOD__ . ' entering... ');
        $listDuplicate = self::getListOsbCorrelationDuplicate($year, $month);
        $totalDuplDocno = count($listDuplicate);
        MigrateUtils::logDump(__METHOD__ . ' found correlation total duplicate ' .$totalDuplDocno);
        foreach($listDuplicate as $objDup ){
            $docNo = $objDup->doc_no;
            OsbRefireIgfmasPaymentMatchQuery::removeDuplicateOsbCorrelationDocNo($docNo);
        }
        MigrateUtils::logDump(__METHOD__ . ' completed with total ' .$totalDuplDocno);
        
    }

    public static function deleteAllInvalidDocNoOsbCorrelation($year) {
        MigrateUtils::logDump(__METHOD__ . ' entering... ');
        $listObjOsbCorr = self::getListOsbCorrelationByYear($year);
        MigrateUtils::logDump(__METHOD__ . " total found by year : $year >> ".count($listObjOsbCorr));
        foreach($listObjOsbCorr as $row){
            self::deleteInvalidDocNoOsbCorrelation($row->corr1);
        }
    }
    

     /**
     * program to handle to delete after check the docno status in Fulfilment as Pending Payment Closed
     */
    public static function deleteInvalidDocNoOsbCorrelation($docNo) {
        MigrateUtils::logDump(__METHOD__ . ' entering... docNo: '.$docNo);
        $isProceedDelete = false;
        //Checking is valid to delete the docno in osb_correlation
        $cls = new OsbRefireIgfmasPaymentMatchQuery;
        /**
            * Closed          41535
            * Closed          41035
            * Pending Payment 41530
            * Pending Payment 41030
            * Cancelled 41400
            * Cancelled 41900
         */
        $currentflowObj = $cls ->getCurrentWorkflowByPoCo($docNo);
        if ($currentflowObj != null 
                && ( $currentflowObj->status_id == 41530 || $currentflowObj->status_id == 41030  
                    || $currentflowObj->status_id == 41535  || $currentflowObj->status_id == 41035 
                    || $currentflowObj->status_id == 41400  || $currentflowObj->status_id == 41900 
                ) 
            ) {
            $isProceedDelete = true;
        } elseif($currentflowObj != null) {
            MigrateUtils::logDump(__METHOD__ . " Skip delete! $docNo status as ".$currentflowObj->status_id. ' >> '.$currentflowObj->status_name);
        } else {
            $isProceedDelete = true;
            MigrateUtils::logDump(__METHOD__ . " checking.. $docNo not exists in FL_FULFILMENT_ORDER" );
        }

        // process deleted
        if( $isProceedDelete === true){
            MigrateUtils::logDump(__METHOD__ . " $docNo valid to proceed delete OSB_CORRELATION ");
            $listDocs = OsbRefireIgfmasPaymentMatchQuery::getListOsbCorrelation($docNo);
            $totalRec = count($listDocs);
            if($totalRec > 0){
                foreach($listDocs as $row ){
                    $dateCreatedObj = Carbon::parse($row->created_date);
                    $dateFormatted = $dateCreatedObj->format('Y-m-d H:i');
                    $isPermenantDelete = true;
                    self::deleteOsbCorrelation($docNo,$dateFormatted,$isPermenantDelete);
                }
            }
        }
        
        MigrateUtils::logDump(__METHOD__ . ' completed ');
    }

    public static function removeDuplicateOsbCorrelationDocNo($docNo) {
        MigrateUtils::logDump(__METHOD__ . ' entering... ');
        
        $listDocs = OsbRefireIgfmasPaymentMatchQuery::getListOsbCorrelation($docNo);
        $totalRec = count($listDocs);
        // If two or more records, We want to make sure only one record exists in docNo in corr1 fields.
        if($totalRec > 1){
            $counter = 1;
            foreach($listDocs as $row ){
                if($counter < $totalRec ){
                    $dateCreatedObj = Carbon::parse($row->created_date);
                    $dateFormatted = $dateCreatedObj->format('Y-m-d H:i');
                    $isPermenantDelete = true;
                    self::deleteOsbCorrelation($docNo,$dateFormatted,$isPermenantDelete);
                    $counter++;
                }
            }
        }else{
            MigrateUtils::logDump(__METHOD__ . ' no duplicate. ');
        }

    }

    public static function deleteOsbCorrelation($docNo,$dateFormatted,$isPermenantDelete = false) {
        MigrateUtils::logDump(__METHOD__ . " docNo: $docNo >> date: $dateFormatted >> isPermenantDelete: $isPermenantDelete" );
        
        if($isPermenantDelete === true){
            DB::connection('oracle_nextgen_fullgrant')->table('osb_correlation')
                ->where('corr1',$docNo)
                ->whereRaw("to_char(created_date,'YYYY-MM-DD HH24:MI') = '$dateFormatted' " )
                ->delete();
        }else{
            $docNoX =  $docNo;
            if(str_contains($docNo, 'DEL') === false){
                $docNoX =  $docNo.'DEL';
                MigrateUtils::logDump(__METHOD__ . ' docno  : '.$docNo. ' >> append to DEL >> '.$docNoX);
            }
            DB::connection('oracle_nextgen_fullgrant')->table('osb_correlation')
                ->where('corr1',$docNo)
                ->whereRaw("to_char(created_date,'YYYY-MM-DD HH24:MI') = '$dateFormatted' " )
                ->update([
                    'corr1'=>$docNoX
                    ]);
        }    
        MigrateUtils::logDump(__METHOD__ . " done");
    }

    
    public static function getListOsbCorrelation($docNo){
        return DB::connection('oracle_nextgen_fullgrant')->table('osb_correlation')
            ->where('corr1',$docNo)
            ->orderBy('created_date','asc')
            ->get();
    }

    public static function getListOsbCorrelationByYear($year){
        return DB::connection('oracle_nextgen_fullgrant')->table('osb_correlation')
            ->whereRaw("to_char(created_date,'YYYY') = '$year' " )
            ->take(200)
            ->orderBy('created_date','asc')
            ->get();
    }

    public static function getListOsbCorrelationDuplicate($year,$month){
        return DB::connection('oracle_nextgen_fullgrant')
            ->select("SELECT 
                oc.corr1 AS doc_no, to_char(oc.created_date,'YYYY') AS year_created,
                count(*) AS total_duplicate,
                ( SELECT distinct REMARKS_3 AS is_pmq
                    FROM   osb_logging x 
                    WHERE  service_code = 'EPP-017' 
                    AND remarks_2 = oc.corr1
                    AND trans_type = 'IBReq-DEC' 
                    AND trans_date IN (
                        SELECT max(trans_date) FROM osb_logging g WHERE g.SERVICE_CODE = x.SERVICE_CODE 
                        AND g.REMARKS_2  =x.REMARKS_2  AND g.TRANS_TYPE = x.TRANS_TYPE  ) 
                    AND rownum < 2) AS is_pmq,
                ( SELECT distinct sd.STATUS_NAME  FROM FL_FULFILMENT_ORDER  fo, 
                    FL_WORKFLOW_STATUS ws , PM_STATUS_DESC sd 
                    WHERE fo.FULFILMENT_ORDER_ID  = ws.DOC_ID  
                    AND fo.DOC_TYPE  = ws.DOC_TYPE  
                    AND ws.STATUS_ID  = sd.STATUS_ID  
                    AND sd.LANGUAGE_CODE = 'en' 
                    AND fo.DOC_NO  =  oc.corr1
                    AND ws.IS_CURRENT = 1
                    AND rownum < 2) AS latest_status
            FROM osb_correlation oc
            WHERE  to_char(oc.created_date,'YYYY') = $year 
            AND to_char(oc.created_date,'MM') = $month 
            AND oc.corr1 NOT LIKE '%DEL'
            GROUP BY oc.corr1 ,to_char(oc.created_date,'YYYY')
            HAVING count(*) > 1
            ORDER BY 2");
    }

    public static function getLatestPayloadEpp017($docNo){
        $query = "SELECT o.TRANS_DATE ,d.PAYLOAD_BODY as payload FROM OSB_LOGGING o, OSB_LOGGING_DTL d 
            WHERE o.LOGGING_ID  = d.LOGGING_ID  
            AND o.TRANS_TYPE = 'IBReq' 
            AND o.service_code = 'EPP-017' 
            AND o.TRANS_ID IN (SELECT trans_id FROM OSB_LOGGING x WHERE x.service_code = o.SERVICE_CODE AND x.remarks_2 = ?
                AND trans_date IN (SELECT max(trans_date) FROM OSB_LOGGING y WHERE y.service_code = x.SERVICE_CODE AND y.remarks_2 =  x.remarks_2 )
            )";
        
        $res = DB::connection('oracle_nextgen_fullgrant')->select($query,array($docNo));
        if(count($res)>0){
            return $res[0];
        }
        return null;
    }


    public static function getListPaymentMatchQueryStuck($docNo = null){
        $queryWhere = '';
        if($docNo != null){
            $queryWhere = " and a.doc_no  in ('$docNo') ";
        }
            // sample date : 2018-04-19
        $query = "SELECT 
                (SELECT d.PAYLOAD_BODY FROM OSB_LOGGING o, OSB_LOGGING_DTL d WHERE o.LOGGING_ID  = d.LOGGING_ID  AND o.TRANS_TYPE = 'IBReq' AND o.trans_id  = y.trans_id ) AS payload,
                y.trans_id,
                 (SELECT count(*) FROM osb_correlation WHERE corr1 = remarks_2) AS total_correlation,
                mm,
                dd,
                remarks_2 AS doc_no,
                remarks_3,
                poco.status_name,
                wf_id,
                poco.fws_date_created,
                To_char (trans_date, 'dd')  hb,
                To_char (trans_date, 'mm')  bulan,
                To_char (trans_date, 'yyyy')year
            FROM   osb_logging Y,
                (SELECT To_char (b.created_date, 'mm')                          mm,
                        To_char (b.created_date, 'dd')                          dd,
                        To_char (b.created_date, 'DD/MM/YYYY HH24:MI:SS')
                        fws_date_created
                        ,
                        To_char (a.created_date, 'DD/MM/YYYY HH24:MI:SS')
                        po_date_created,
                        b.doc_type,
                        a.doc_no                                                pono,
                        (SELECT doc_no
                            FROM   fl_fulfilment_request o
                            WHERE  o.fulfilment_req_id = a.fulfilment_req_id)      AS pr_no,
                        (SELECT financial_year
                            FROM   fl_fulfilment_request o
                            WHERE  o.fulfilment_req_id = a.fulfilment_req_id)      AS
                        FINANCIAL_YEAR,
                        d.status_name,
                        c.status_id,
                        b.is_current,
                        b.workflow_status_id                                    wf_id,
                        (SELECT Nvl(SUM (ffid.ordered_amt), 0)
                            FROM   fl_fulfilment_item_addr ffid,
                                fl_delivery_address fda
                            WHERE  ffid.fulfilment_addr_id = fda.delivery_address_id
                                AND fda.fulfilment_req_id = a.fulfilment_req_id)AS
                        PRCR_AMOUNT,
                        (SELECT invoice_amt_new
                            FROM   fl_invoice inv
                            WHERE  inv.fulfilment_req_id = a.fulfilment_req_id
                                AND record_status = 1)
                        INVOICE_AMT_NEW,
                        a.fulfilment_req_id,
                        a.fulfilment_order_id,
                        a.sap_order_no
                    FROM   fl_fulfilment_order a,
                        fl_workflow_status b,
                        pm_status c,
                        pm_status_desc d
                    WHERE  a.fulfilment_order_id = b.doc_id
                        AND b.status_id = c.status_id
                        AND c.status_id = d.status_id
                        AND d.language_code = 'en'
                        AND b.doc_type IN ( 'PO', 'CO', 'FC' )
                        ---PO200000000050769  (PMQ)     -- data patch manual-29may2023
                        AND b.is_current = 1
                        AND b.status_id NOT IN (41530, 41535, 41030, 41035,49120,41932,41422)  --49120 | Acknowledged (49120)-FACTORING
                        --49120 | Acknowledged (49120)-FACTORING
                        $queryWhere
                        --and a.doc_no  in ('PO200000000537001')  --CO180000000000269 --popo poco  -- coco
                        -- AND To_char (b.created_date, 'yyyy') IN ( '2023', '2024' )
                        ) poco
            WHERE  
                y.trans_id IN (SELECT DISTINCT( trans_id )
                                FROM   osb_logging
                                WHERE  service_code = 'EPP-017'
                                        -- AND trans_date >= To_date('2024-10-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                                        )
                --and remarks_2 in ('PO220000000010712')
                AND remarks_3 = 'Y'
                AND trans_type = 'OBRes'
                AND Extract(year FROM trans_date) = Extract(year FROM SYSDATE)
                AND Y.remarks_2 = poco.pono
            GROUP  BY y.trans_id,poco.fws_date_created,
                    remarks_2,
                    remarks_3,
                    To_char (trans_date, 'dd'),
                    To_char (trans_date, 'mm'),
                    To_char (trans_date, 'yyyy'),
                    poco.status_name,
                    mm,
                    dd,
                    wf_id
            ORDER  BY mm,
                    dd ASC   ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

}
