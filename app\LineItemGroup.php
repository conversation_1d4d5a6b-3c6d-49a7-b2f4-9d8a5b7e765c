<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class LineItemGroup extends Model {
    protected $table = "aos_line_item_groups";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
   
//    public function invoice()
//    {
//        return $this->hasOne('App\Invoices','id');
//    }
//    
//    public function lineitem()
//    {
//        return $this->hasMany('App\LineItem','id');
//    }
}


