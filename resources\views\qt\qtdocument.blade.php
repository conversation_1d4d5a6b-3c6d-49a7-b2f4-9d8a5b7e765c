@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/logtrace-v1.0.css') }}" rel="stylesheet" />
    <style>
        div#qt-doc-header {
            padding: 25px;
            height: 80vh;
        }

        .qt-doc-box {
            padding: 25px;
            box-shadow: 0 0 15px 3px rgb(198, 199, 198);
            border-style: none;
            border-radius: 5px;
        }

        .qt-icon {
            padding: 15px;
            color: rgb(55, 247, 17);
            border: 5px solid rgb(55, 247, 17);
            border-radius: 20px;
            font-size: 24px;
            font-weight: bold;
            background: #262d47;
        }

        div.proposal_popup,
        div.modal-footer {
            background: #fff;
        }

    </style>

@endsection

@section('content')
    <div class="block" style="height: 100%; background: rgb(241, 240, 240);">
        <div class="card">
            <div class="card-header">
                <h1><b class="qt-icon">Qt</b> Quotation Document Checklist</h1>
            </div>
            <div class="card-body">
                <div class="row" id="qt-doc-header">
                    <div class="col-md-3 qt-doc-box">
                        <form action="{{ url('qt/doc/view') }}" method="post" class="form-horizontal">
                            {{ csrf_field() }}
                            <h3><strong>Search Qt Documents</strong></h3><br />
                            <span id="search_fail" style="display: none; color: red;"><b><i
                                        class="fa fa-times-circle"></i>Oops! <span id="search_msg_fail"></span></b></span>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Qt No</label>
                                    <div class="col-md-6">
                                        <input type="text" id="qtNo" name="qtNo" class="form-control" required>
                                    </div>
                                    <button class="btn col-md-1" type="button" name="searchpmno" id="searchpmno"><i
                                            class="fa fa-search"></i></button>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Proposal No</label>
                                    <div class="col-md-8">
                                        <input type="text" id="pmNo" name="pmNo" class="form-control" required>
                                    </div>
                                </div>
                            </div>
                            <br />
                            <div class="row">
                                <span class="pull-right">
                                    <button type="submit" name="searchdoc" id="searchdoc" class="btn btn-sm"><i
                                            class="fa fa-search"></i>
                                        Search</button>
                                </span>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-9">
                        <div class="table-responsive qt-doc-box">
                            <div>
                                <span id="download-spin"></span>
                                <input type="hidden" id="totaldoc" name="totaldoc"
                                    value="{{ isset($documents) ? count($documents) : 0 }}">
                                <span><button name="downloadsel" id="downloadsel" type="button">Download
                                        Selected</button></span>
                                <span><button name="downloadall" id="downloadall" type="button">Download All</button></span>
                                <span id="doc_code_succ" style="display: none; color: rgb(6, 231, 81);"><b><i
                                            class="fa fa-check-circle"></i><span id="doc_msg_succ"></span></b></span>
                                <span id="doc_code_fail" style="display: none; color: red;"><b><i
                                            class="fa fa-times-circle"></i>Oops! <span id="doc_msg_fail"></span></b></span>
                            </div>
                            <table id="search_doc_result" class="table table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th style="text-align: center;"><input type="checkbox" name="checkalldoc"
                                                onchange="checkAllDoc(this)"></th>
                                        <th style="text-align: center;">Qt No</th>
                                        <th style="text-align: center;">Doc Type</th>
                                        <th style="text-align: center;">Proposal No</th>
                                        <th style="text-align: center;">Checklist Act. ID</th>
                                        <th style="text-align: center;">Doc Category ID</th>
                                        <th style="text-align: center;">Filename</th>
                                        <th style="text-align: center;">Filepath</th>
                                        <th style="text-align: center;">Is Archive?</th>
                                        <th style="text-align: center;" class='hide'>Download</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if (isset($documents) && $documents != null)
                                        @foreach ($documents as $doc)
                                            <tr>
                                                <td style="text-align: center;"><input type="checkbox"
                                                        value="{{ $doc->file_path . ',' . ($doc->file_arc_flag != 1 ? 'No' : 'Yes') }}"
                                                        name="checkdoc"></td>
                                                <td style="text-align: center;">{{ $doc->qt_no }}</td>
                                                <td style="text-align: center;">{{ $doc->doc_type }}</td>
                                                <td style="text-align: center;">{{ $doc->proposal_no }}</td>
                                                <td style="text-align: center;">{{ $doc->checklist_action_id }}</td>
                                                <td style="text-align: center;">{{ $doc->doc_category }}</td>
                                                <td>{{ $doc->file_name }}</td>
                                                <td>{{ $doc->file_path }}</td>
                                                <td style="text-align: center;">
                                                    {{ $doc->file_arc_flag != 1 ? 'No' : 'Yes' }}</td>
                                                <td class="selected_doc hide" style="text-align: center;">
                                                    <div class="btn-group">
                                                        <div class='btn-group btn-group-xs'><a
                                                                data-qtno="{{ $doc->qt_no }}"
                                                                data-filename="{{ $doc->file_path }}"
                                                                data-isarchive="{{ $doc->file_arc_flag }}"
                                                                id="download_doc" class='badge badge-info'
                                                                title="Download Document"><i
                                                                    class="fa fa-cloud-download"></i></a></div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    @endif
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" name="qtno" id="qtno" value="{{ $qt_no == null || $qt_no === '' ? '' : $qt_no }}">

    <div class="modal fade" id="proposal_nos" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content proposal_popup">
                <div class="modal-header text-center">
                    <h2 class="modal-title" style="color: #262d47;"><i class="fa fa-info-circle"></i> List of Proposal No
                        in <span id="showqtno"></span></h2>
                </div>
                <div class="modal-body">
                    <table id="proposal_tbl" class="table table-bordered table-condensed table-sm">
                        <thead>
                            <tr>
                                <td></td>
                                <td style="text-align: center;">Proposal No</td>
                                <td style="text-align: center;">Submitted Date</td>
                                <td style="text-align: center;">Mof No</td>
                            </tr>
                        </thead>
                    </table>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-sm" type="button" id="okpmNo">Ok</button>
                    <button class="btn btn-sm" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!};

        function download(fileUrl, fileName) {
            try {
                var a = document.createElement("a");
                a.href = fileUrl;
                a.setAttribute("download", fileName);
                a.click();
            } catch (error) {
                showHideAlertMessage('doc_code_fail', 'doc_msg_fail',
                    `Download ${fileName} has error: ${error.message}`);
            }
        }

        function downloadDocument(filename, qtno) {
            let url = APP_URL + '/qt/doc/download/single?filename=' + filename + '&qtno=' + qtno;

            const pathArray = filename.split("/");
            const lastIndex = pathArray.length - 1;
            filename = pathArray[lastIndex];

            download(url, filename);
        }

        function downloadDocuments(filenames, qtno) {
            let url = APP_URL + '/qt/doc/download/multiple';
            let csrf = $("input[name=_token]").val();

            let form = $('<form></form>').attr('action', url).attr('method', 'post');
            form.append($("<input></input>").attr('type', 'hidden').attr('name', '_token').attr('value',
                csrf));
            form.append($("<input></input>").attr('type', 'hidden').attr('name', 'filenames').attr('value',
                JSON.stringify(filenames)));
            form.append($("<input></input>").attr('type', 'hidden').attr('name', 'qtno').attr('value',
                qtno));

            //send request
            form.appendTo('body').submit().remove();
        }

        function checkAllDoc(ele) {
            var checkboxes = document.getElementsByName('checkdoc');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type == 'checkbox') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type == 'checkbox') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        function showHideAlertMessage(id, element, msg) {
            $('#' + id).css('display', 'block');
            document.getElementById(element).innerHTML = msg;
            setTimeout(function() {
                $('#' + id).css('display', 'none');
            }, 5000);
        }

        function transferAndDecryptDoc(filename, qtno, isarchive) {
            return $.ajax({
                url: APP_URL + '/qt/doc/extract',
                type: "GET",
                data: {
                    filename,
                    qtno,
                    isarchive
                },
                async: false
            });
        }

        function extractDocument(docs, qtno) {
            let filenames = [];

            for (doc of docs) {
                let response = null;

                // Find the last comma index
                const lastCommaIndex = doc.lastIndexOf(',');

                // Split the string based on the last comma
                const fileNamePath = doc.substring(0, lastCommaIndex);
                const isArchiveFile = doc.substring(lastCommaIndex + 1);

                console.log("fileNamePath : "+fileNamePath+" , isArchiveFile : "+isArchiveFile);
                transferAndDecryptDoc(fileNamePath, qtno, isArchiveFile).done(data => {
                    response = data;
                });
                if (response == 0) {
                    showHideAlertMessage('doc_code_fail', 'doc_msg_fail',
                        `${doc} Failed! Please contact system administrator.`
                    );
                } else {
                    filenames.push(fileNamePath);
                }
            }

            downloadDocuments(filenames, qtno);
        }

        $(document).ready(function() {
            $('#page-container').removeAttr('class');

            $('#search_doc_result').dataTable({
                destroy: true,
                order: [
                    [3, "desc"]
                ]
            });

            $('#okpmNo').on('click', function() {
                let proposal_no = $('input[name="radioproposalno"]:checked').val();

                $('#pmNo').val(proposal_no);
                $('#proposal_nos').modal('hide');
            });

            $('#searchpmno').on('click', function() {
                let qtno = $('#qtNo').val();

                if (qtno === '') {
                    showHideAlertMessage('search_fail', 'search_msg_fail',
                        'Please fill in Qt No.');
                    return;
                }

                $('#proposal_nos').modal('show');
                document.getElementById('showqtno').textContent = qtno;

                $.ajax({
                    url: APP_URL + '/qt/doc/proposal/list',
                    type: "GET",
                    data: {
                        qtno
                    },
                    success: function(pmlist) {
                        let content = `<thead>
                                            <tr>
                                                <td></td>
                                                <td style="text-align: center;">Proposal No</td>
                                                <td style="text-align: center;">Submitted Date</td>
                                                <td style="text-align: center;">Mof No</td>
                                            </tr>
                                        </thead>`;
                        content += `<tbody>`;
                        if (pmlist) {
                            pmlist.forEach(p => {
                                content +=
                                    `<tr><td style="text-align: center;"><input type="radio" value="${p.proposal_no}" name="radioproposalno"></td>`;
                                content +=
                                    `<td style="text-align: center;">${p.proposal_no}</td>`;
                                content +=
                                    `<td style="text-align: center;">${p.proposal_submit_date}</td>`;
                                content +=
                                    `<td style="text-align: center;">${p.mof_no}</td></tr>`;
                            });
                        }

                        content += `</tbody>`;
                        $('#proposal_tbl').hide().html(content).fadeIn();
                        $('#proposal_tbl').dataTable({
                            destroy: true,
                            order: [
                                [1, "asc"]
                            ]
                        });
                    },
                    error: function(error) {

                    }
                });
            })

            $('#search_doc_result tbody').on('click', 'td a#download_doc', function() {
                let filename = $(this).attr('data-filename');
                let qtno = $(this).attr('data-qtno');
                let isarchive = $(this).attr('data-isarchive');
                let totaldoc = $('#totaldoc').val();

                if (totaldoc > 0) {
                    $('#download-spin').hide().html(
                            "<div class='text-center' style='padding: 20px;color:#0470DC;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>"
                        )
                        .fadeIn();

                    transferAndDecryptDoc(filename, qtno, isarchive)
                        .then(response => {
                            if (response == 0) {
                                showHideAlertMessage('doc_code_fail', 'doc_msg_fail',
                                    'Download Failed! Document Not Found.');
                            } else {
                                downloadDocument(filename, qtno);
                            }

                            $('#download-spin').hide().html("").fadeIn();
                        });
                }
            });

            $('#downloadsel').on('click', function() {
                let qtno = $('#qtno').val();
                let totaldoc = $('#totaldoc').val();
                let docs = $('input:checked').map(function() {
                    return this.value;
                }).get();

                if (totaldoc > 0) {
                    $('#download-spin').hide().html(
                            "<div class='text-center' style='padding: 20px;color:#0470DC;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>"
                        )
                        .fadeIn();

                    docs = docs.filter(id => id !== "0" && id !== 'on');

                    if (docs && docs.length > 0) {
                        extractDocument(docs, qtno);
                    } else {
                        showHideAlertMessage('doc_code_fail', 'doc_msg_fail',
                            'No Document Selected');
                    }

                    $('#download-spin').hide().html("").fadeIn();
                }
            })

            $('#downloadall').on('click', function() {
                let qtno = $('#qtno').val();
                let totaldoc = $('#totaldoc').val();
                let docs = $('input:checked').map(function() {
                    return this.value;
                }).get();

                if (totaldoc > 0) {
                    $('#download-spin').hide().html(
                            "<div class='text-center' style='padding: 20px;color:#0470DC;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>"
                        )
                        .fadeIn();

                    docs = docs.filter(id => id !== "0" && id !== 'on');

                    if (docs.length != 0 && docs.length == totaldoc) {
                        extractDocument(docs, qtno);
                    } else {
                        showHideAlertMessage('doc_code_fail', 'doc_msg_fail',
                            'Please tick all documents.');
                    }

                    $('#download-spin').hide().html("").fadeIn();
                }
            })
        });
    </script>
@endsection
