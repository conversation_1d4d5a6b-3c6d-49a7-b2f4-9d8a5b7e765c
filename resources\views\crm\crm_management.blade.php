@extends('layouts.guest-dash')

@section('header')
<style type="text/css">
    .highlight {
        background: yellow;
    }
</style>
@endsection

@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">
        <div class="block">
        @if (session('deactivateUserResult'))
            <div class="alert alert-success">
                User has been successfully deactivated.
            </div>
        @endif
            <div class="block-title">
                <h2><strong>CRM MANAGEMENT FORM</strong></h2>
                <div class="block-options pull-right action-today">
                    <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-yellow " data-toggle="modal" data-url="{{url('/support/report/log/case-update')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" data-title="List Action Update Case ">View Today Action</a>
                </div>
            </div>
            <form id="form-search-task" action="{{url("/crmmanagement/main")}}" method="post" class="form-horizontal form-bordered">
                {{ csrf_field() }}
                <div class="row" style="display: flex; flex-wrap: nowrap;">
                    <div class="col-lg-3">
                        <div class="form-group" style="display:block;">
                            <label class="col-md-3 control-label" for="type_crm">CRM</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="type_crm" name="type_crm" class="form-control typeCrm" required>
                                        <option value="">Please select</option>
                                        @foreach ($array_data as $key)
                                        <option value="{{$key}}" @if($key==$crm_type ) selected @endif>{{$key}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group" id="div_modules" style="display:block;">
                            <label class="col-md-3 control-label" for="modules">Module</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="modules" name="modules" class="form-control modules" required>
                                        <option value="">Please select</option>
                                        @foreach ($arrayModule as $key)
                                        <option value="{{$key}}" @if($key==$module ) selected @endif>{{$key}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3">
                        <div class="form-group" id="div_user_category" style="display:block;">
                            <label class="col-md-3 control-label" for="user_category">Search by</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="user_category" name="user_category" class="form-control userCategory" required>
                                        <option value="">Please select</option>
                                        @foreach ($arrayUserCategory as $key)
                                        <option value="{{$key}}" @if($key==$selectedUserCategory ) selected @endif>{{$key}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="form-group" id="div_search_query" style="display:block;">
                            <label class="col-md-3 control-label" for="value" id="label_value">Value</label>
                            <div class="input-group">
                                <input id="value" name="value" class="form-control" required value="{{$value}}" placeholder="Value" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2">
                        <div class="form-actions form-actions-button text-right" style="margin-right:30px;">
                            <br /><button type="submit" class="btn btn-sm btn-info">Search</button>
                        </div>
                    </div>
                </div>
            </form>
            @if($result != null)
            @if($module == 'Cases')
            <div class="row">
                <div class="block">
                    <div id="response" class="table-options clearfix display-none">
                        <div id="response-msg" class="text-center text-light" colspan="6"></div>
                    </div>
                    <table class="table table-bordered" id='caseTable'>
                        <tr>
                            <th class="text-left">Case Number</th>
                            <td class="text-left tcase_number">
                                <input id="tcase_number" name="tcase_number" class="form-control" readonly="true" value="{{$result->case_number}}" placeholder="Case Number" type="text">
                            </td>
                            <th class="text-left">Channel</th>
                            <td class="text-left">
                                <select id="channel" name="channel" class="select2">
                                    @foreach ($listChannel as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->channel ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Date Entered</th>
                            <td class="text-left">{{$result->case_created}}</td>
                            <th class="text-left">Date Modified</th>
                            <td class="text-left">{{$result->case_modified}}</td>
                        </tr>
                        <tr>
                            <th class="text-left">State</th>
                            <td class="text-left">
                                <select id="state" name="state" class="select2">
                                    @foreach ($listState as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->state ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                            <th class="text-left">Status</th>
                            <td class="text-left">
                                <select id="status" name="status" class="select2">
                                    @foreach ($listStatus as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->status ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        @if($crm_type == 'CRM eP')
                        <tr>
                            <th class="text-left">Request Type</th>
                            <td class="text-left">
                                <select id="type" name="type" class="select2">
                                    @foreach ($listType as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->request_type_c ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                            <th class="text-left">Incident/Service Type</th>
                            <td class="text-left">
                                <select id="incident_service" name="incident_service" class="select2">
                                    @foreach ($listIncidentType as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->incident_service_type_c ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">CPTPP</th>
                            <td class="text-left">
                                <select id="cptpp" name="cptpp" class="select2" style="width:100px">
                                    <option value="1" @if($result->cptpp_flag == 1 ) selected @endif> 1 </option>
                                    <option value="0" @if($result->cptpp_flag == 0 ) selected @endif> 0 </option>
                                </select>
                            </td>
                            <th class="text-left">Redmine Number</th>
                            <td class="text-left">
                                <input id="tredmine_number" name="tredmine_number" class="form-control" value="{{$result->redmine_number}}" placeholder="Redmine Number" type="number">
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category</th>
                            <td class="text-left">
                                <select id="category_1" name="category_1" class="select2">
                                    @foreach ($listCategory1 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_c ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                            <th class="text-left">Sub Category</th>
                            <td class="text-left" colspan="3">
                                <select id="category_2" name="category_2" class="select2">
                                    @foreach ($listCategory2 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->sub_category_c ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Sub Category 2</th>
                            <td class="text-left" colspan="3">
                                <select id="category_3" name="category_3" class="select2">
                                    @foreach ($listCategory3 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->sub_category_2_c ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        @elseif($crm_type == 'CRM SSM')
                        <tr>
                            <th class="text-left">Case Type</th>
                            <td class="text-left" colspan="3">
                                <select id="type" name="type" class="select2">
                                    @foreach ($listType as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->type ) selected @endif>{{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category</th>
                            <td class="text-left" colspan="3">
                                <select id="category_1" name="category_1" class="select2">
                                    @foreach ($listCategory1 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category 2</th>
                            <td class="text-left" colspan="3">
                                <select id="category_2" name="category_2" class="select2">
                                    @foreach ($listCategory2 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_2 ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category 3</th>
                            <td class="text-left" colspan="3">
                                <select id="category_3" name="category_3" class="select2">
                                    @foreach ($listCategory3 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_3 ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category 4</th>
                            <td class="text-left" colspan="3">
                                <select id="category_4" name="category_4" class="select2">
                                    @foreach ($listCategory4 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_4 ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category 5</th>
                            <td class="text-left" colspan="3">
                                <select id="category_5" name="category_5" class="select2">
                                    @foreach ($listCategory5 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_5 ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <th class="text-left">Category 6</th>
                            <td class="text-left" colspan="3">
                                <select id="category_6" name="category_6" class="select2">
                                    @foreach ($listCategory6 as $data)
                                    <option value="{{$data->value_code}}" @if($data->value_code == $result->category_6 ) selected @endif>{{$data->value_code}} - {{$data->value_name}}</option>
                                    @endforeach
                                </select>
                            </td>
                        </tr>
                        @endif
                        <tr>
                            <th colspan="2"></th>
                            <td><button type="button" class="btn btn-sm btn-primary action_update">Update</button></td>
                        </tr>
                    </table>
                </div>
            </div>
            @elseif($module == 'Users')
            <div class="row">
                <div class="block">
                    <div id="response" class="table-options clearfix display-none">
                        <div id="response-msg" class="text-center text-light" colspan="6"></div>
                    </div>
                    <table class="table table-bordered" id='caseTable'>
                        <tr>
                            <th class="text-left">Username</th>
                            <td class="text-left tcase_number">
                                <input id="tcase_number" name="tcase_number" class="form-control" readonly="true" value="" placeholder="Case Number" type="text">
                            </td>
                            <th class="text-left">Email Address</th>
                            <td class="text-left tcase_number">
                                <input id="tcase_number" name="tcase_number" class="form-control" readonly="true" value="" placeholder="Case Number" type="text">
                            </td>
                        </tr>
                        <tr>
                            <th colspan="2"></th>
                            <td><button type="button" class="btn btn-sm btn-primary action_update">Update</button></td>
                        </tr>
                    </table>
                </div>
            </div>
            @endif

            @endif
            @if($users != null)
            <div class="row">
                <div class="block">
                    <table class="table table-bordered" id='userListTable'>
                        <tr>
                            <th class="text-left">CRM</th>
                            <th class="text-left">Name</th>
                            <th class="text-left">Username</th>
                            <th class="text-left">Email</th>
                            <th class="text-left">Status</th>
                            <th class="text-left">Description</th>
                            <th class="text-left">Date Modified</th>
                            <th class="text-left">Modified By</th>
                            <th class="text-left">Action</th>
                        </tr>
                        @foreach ($users as $crm => $user)
                            @if (is_array(reset($user)))
                                @if (empty($user))
                                    <tr>
                                        <td colspan="9" class="text-center">No users found for {{$crm}}</td>
                                    </tr>
                                @else
                                    @foreach ($user as $u)
                                        <tr>
                                            <td class="text-left">{{$crm}}</td>
                                            <td class="text-left">{{$u['first_name'] .' '. $u['last_name']}}</td>
                                            <td class="text-left">{{$u['user_name']}}</td>
                                            <td class="text-left">{{$u['email_address']}}</td>
                                            <td class="text-left">{{$u['status']}}</td>
                                            <td class="text-left">{{isset($u['description']) ? $u['description'] : 'N/A'}}</td>
                                            <td class="text-left">{{isset($u['date_modified']) ? $u['date_modified'] : 'N/A'}}</td>
                                            <td class="text-left">{{isset($u['modified_user_name']) ? $u['modified_user_name'] : 'N/A'}}</td>
                                            <td class="text-left">
                                                <button type="button" class="btn btn-info" data-toggle="modal" data-target="#detailUserModal" data-crm="{{$crm}}" data-user="{{json_encode($u)}}" data-action="view">View</button>
                                                @if($u['status'] == 'Active')
                                                <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#actionModal" data-crm="{{$crm}}" data-user="{{json_encode($u)}}" data-action="setInactive">Set Inactive</button>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                @endif
                            @else
                                @if (empty($user))
                                    <tr>
                                        <td colspan="9" class="text-center">No users found for {{$crm}}</td>
                                    </tr>
                                @else
                                    <tr>
                                        <td class="text-left">{{$crm}}</td>
                                        <td class="text-left">{{$user['first_name'] .' '. $user['last_name']}}</td>
                                        <td class="text-left">{{$user['user_name']}}</td>
                                        <td class="text-left">{{$user['email_address']}}</td>
                                        <td class="text-left">{{$user['status']}}</td>
                                        <td class="text-left">{{isset($user['description']) ? $user['description'] : 'N/A'}}</td>
                                        <td class="text-left">{{isset($user['date_modified']) ? $user['date_modified'] : 'N/A'}}</td>
                                        <td class="text-left">{{isset($user['modified_user_name']) ? $user['modified_user_name'] : 'N/A'}}</td>
                                        <td class="text-left">
                                            <button type="button" class="btn btn-info" data-toggle="modal" data-target="#detailUserModal" data-crm="{{$crm}}" data-user="{{json_encode($user)}}" data-action="view">View</button>
                                            @if($user['status'] == 'Active')
                                            <button type="button" class="btn btn-danger" data-toggle="modal" data-target="#actionModal" data-crm="{{$crm}}" data-user="{{json_encode($user)}}" data-action="setInactive">Set Inactive</button>
                                            @endif
                                        </td>
                                    </tr>
                                @endif
                            @endif
                        @endforeach
                    </table>
                </div>
            </div>
        @endif
    </div>
</div>

@endif

<div class="modal fade" id="combinedModal" tabindex="-1" role="dialog" aria-labelledby="combinedModalLabel">
  <div class="modal-dialog modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        <h4 class="modal-title text-center" id="combinedModalLabel"></h4>
      </div>
      <div class="modal-body" id="combinedModalBody" style="overflow-x: scroll;">
        <!-- Content will be inserted here -->
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary" id="combinedModalAction">Action</button>
      </div>
    </div>
  </div>
</div>

@include('_shared._modalListLogAction')
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    $(function() {
        TablesDatatables.init();
    });
    App.datatables();
</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>
    $(function() {
        ModalListActionLogDatatable.init();
    });
</script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
<script>
$(document).ready(function() {
    $('[data-toggle="modal"]').click(function() {
        var action = $(this).data('action');
        if (action !== 'view' && action !== 'setInactive') {
            return;
        }
        var crm = $(this).data('crm');
        var user = $(this).data('user');
        var modalLabel = $('#combinedModalLabel');
        var modalBody = $('#combinedModalBody');
        var modalAction = $('#combinedModalAction');

        if (action === 'view') {
            modalLabel.text('View User Details');
            var keys = ['first_name', 'last_name', 'user_name', 'email_address', 'status', 'description', 'date_modified', 'modified_user_name', 'securitygroup_names'];
            var keyMap = {
                'first_name': 'First Name',
                'last_name': 'Last Name',
                'user_name': 'Username',
                'email_address': 'Email',
                'status': 'Status',
                'description': 'Description',
                'date_modified': 'Date Modified',
                'modified_user_name': 'Modified By',
                'securitygroup_names': 'Security Group'
            };
            var rows = ['<tr><th class="text-left">CRM</th><td class="text-left">' + crm + '</td></tr>']
                .concat(keys.map(key => `<tr><th class="text-left">${keyMap[key]}</th><td class="text-left">${user[key] || 'N/A'}</td></tr>`))
                .join('');

            modalBody.html(`
                <table class="table table-bordered" id='userDetailTable'>
                    ${rows}
                </table>
            `);
            modalAction.hide();
        } else if (action === 'setInactive') {
            // Existing code for setInactive action
            modalLabel.text(`Deactivate User (${user.id})`);
            modalBody.html(`
            <form id="deactivateUserForm" action="{{url("/crmmanagement/deactivate-user")}}" method="post">
            {{ csrf_field() }}
                <div class="form-group">
                    <label for="crm">CRM</label>
                    <input type="text" class="form-control" id="crm" name="crm" value="${crm}" readonly>
                </div>
                <div class="form-group">
                    <label for="userId">User ID</label>
                    <input type="text" class="form-control" id="userId" name="userId" value="${user.id}" readonly>
                    <label for="username">Username</label>
                    <input type="text" class="form-control" id="username" name="username" value="${user.user_name}" readonly>
                </div>
                <div class="form-group">
                    <label for="ticketHelpdesk">Ticket Helpdesk</label>
                    <input type="text" class="form-control" id="ticketHelpdesk" name="ticketHelpdesk">
                </div>
                <div class="form-group">
                    <label for="remark">Remark</label>
                    <textarea class="form-control" id="remark" name="remark" rows="3"></textarea>
                </div>
            </form>
            `);
            modalAction.text('Deactivate User');
            modalAction.show();
            modalAction.click(function() {
                $('#deactivateUserForm').submit();
            });
        }

        $('#combinedModal').modal('show');
    });
});
</script>
<script>
    // Below is just the code to make sure the input hide and shows properly
    (function() {
    var module = $('#modules').val();
    var userCategory = $('#user_category').find(":selected").val();
    if (module == 'Users') {
            $('#type_crm').val('');
            $('#type_crm').attr('disabled', true);
            $('#type_crm').attr('required', false);
            $('#div_search_query').show();
            $('#div_user_category').show();
            $('#label_value').text(userCategory);
            $('#value').attr('placeholder', 'Type '+userCategory+' Here');
        } else if (module == 'Cases') {
            $('#type_crm').attr('disabled', false);
            $('#type_crm').attr('required', true);
            $('#div_search_query').show();
            $('#div_user_category').hide();
            $('#label_value').text('Case No.');
            $('#value').attr('placeholder', 'Case Number');
    }
    
    if ($('#modules').val() == 'Users') {
        $('#type_crm').attr('required', false);
    }
    if ($('#modules').val() == 'Cases') {
        $('#type_crm').attr('required', true);
    }
    if (!$('#value').val()) {
        $('#div_search_query').hide();
    }
    if (!$('#user_category').val() || !$('#modules').val()) {
        $('#div_user_category').hide();
    }
})();
    $("#modules").bind("change", function() {
        $('#value').val('');
        var module = $(this).find(":selected").val();
        var userCategory = $('#user_category').find(":selected").val();
        if (module == 'Users') {
            $('#type_crm').val('');
            $('#type_crm').attr('disabled', true);
            $('#type_crm').attr('required', false);
            $('#div_search_query').show();
            $('#div_user_category').show();
            $('#label_value').text(userCategory);
            $('#value').attr('placeholder', 'Type '+userCategory+' Here');
        } else if (module == 'Cases') {
            $('#type_crm').attr('disabled', false);
            $('#type_crm').attr('required', true);
            $('#div_search_query').show();
            $('#div_user_category').hide();
            $('#label_value').text('Case No.');
            $('#value').attr('placeholder', 'Case Number');
        }
    });
    $('#user_category').bind('change', function() {
        var userCategory = $(this).find(":selected").val();
        $('#label_value').text(userCategory);
        $('#value').attr('placeholder', 'Type '+userCategory+' Here');
    });
</script>
{{-- @if($users != null)
<script>
    console.log('Users Found: ', {!! json_encode($users) !!});
</script>
@endif --}}
<script>
    var oldTypeCrm = $('#type_crm option:selected').text();
    var oldCaseNumber = $("input[name=tcase_number]").val();
    var oldState = $('#state option:selected').val();
    var oldStatus = $('#status option:selected').val();
    var oldChannel = $('#channel option:selected').val();
    var oldType = $('#type option:selected').val();
    var oldIncidentType = $('#incident_service option:selected').val();
    var oldCategory1 = $('#category_1 option:selected').val();
    var oldCategory2 = $('#category_2 option:selected').val();
    var oldCategory3 = $('#category_3 option:selected').val();
    var oldCategory4 = $('#category_4 option:selected').val();
    var oldCategory5 = $('#category_5 option:selected').val();
    var oldCategory6 = $('#category_6 option:selected').val();
    var oldRedmineNumber = $("input[name=tredmine_number]").val();
    var oldCptpp = $('#cptpp option:selected').val();

    $(".action_update").on('click', function() {

        var csrf = $("input[name=_token]").val();
        var typeCrm = $('#type_crm option:selected').text();
        var caseNumber = $("input[name=tcase_number]").val();
        var state = $('#state option:selected').val();
        var status = $('#status option:selected').val();
        var channel = $('#channel option:selected').val();
        var type = $('#type option:selected').val();
        var incidentType = $('#incident_service option:selected').val();
        var category1 = $('#category_1 option:selected').val();
        var category2 = $('#category_2 option:selected').val();
        var category3 = $('#category_3 option:selected').val();
        var category4 = $('#category_4 option:selected').val();
        var category5 = $('#category_5 option:selected').val();
        var category6 = $('#category_6 option:selected').val();
        var redmineNumber = $("input[name=tredmine_number]").val();
        var cptpp = $('#cptpp option:selected').val();

        var proceedUpdate = '1'; //1 is yes
        if (oldTypeCrm == typeCrm && oldCaseNumber == caseNumber) {
            if (typeCrm == 'CRM eP') {
                if (oldState == state && oldStatus == status && oldChannel == channel && oldType == type && oldRedmineNumber == redmineNumber &&
                    oldIncidentType == incidentType && oldCategory1 == category1 && oldCategory2 == category2 && oldCategory3 == category3 && oldCptpp == cptpp) {
                    proceedUpdate = '0';
                }
            } else if (typeCrm == 'CRM SSM') {
                if (oldState == state && oldStatus == status && oldChannel == channel && oldType == type &&
                    oldIncidentType == incidentType && oldCategory1 == category1 && oldCategory2 == category2 && oldCategory3 == category3 &&
                    oldCategory4 == category4 && oldCategory5 == category5 && oldCategory6 == category6) {
                    proceedUpdate = '0';
                }
            } else {
                if (oldState == state && oldStatus == status && oldChannel == channel) {
                    proceedUpdate = '0';
                }
            }
            if (proceedUpdate == '1') {
                $.ajax({
                    type: "POST",
                    url: "/crmmanagement/updatecrm",
                    data: {
                        "_token": csrf,
                        "typeCrm": typeCrm,
                        "caseNumber": caseNumber,
                        "state": state,
                        "status": status,
                        "channel": channel,
                        "type": type,
                        "incidentType": incidentType,
                        "category1": category1,
                        "category2": category2,
                        "category3": category3,
                        "category4": category4,
                        "category5": category5,
                        "category6": category6,
                        "redmineNumber": redmineNumber,
                        "cptpp": cptpp
                    }
                }).done(function(resp) {
                    if (resp.status == 'Success') {
                        $("#response").show();
                        $("#response").addClass("label-success");
                        $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                        setTimeout(location.reload.bind(location), 5000);
                    } else {
                        $("#response").show();
                        $("#response").addClass("label-danger");
                        $("#response-msg").html(resp.msg);
                    }
                });
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("No changes detected. Data will not be update.");
            }
        }


    });
</script>
@if($result != null)
@if($crm_type == 'CRM SSM')
<script>
    const dictionaryCaseType = { !!$listType!! };
    const dictionaryCategory1 = { !!$listCategory1!! };
    const dictionaryCategory2 = { !!$listCategory2!! };
    const dictionaryCategory3 = { !!$listCategory3!! };
    const dictionaryCategory4 = { !!$listCategory4!! };
    const dictionaryCategory5 = { !!$listCategory5!! };
    const dictionaryCategory6 = { !!$listCategory6!! };
    // Can refactor to recursive for better readability if you want
    $(document).ready(function() {
        updateCategoryDropdown();
        updateCategory2Dropdown();
        updateCategory3Dropdown();
        updateCategory4Dropdown();
        updateCategory5Dropdown();
        updateCategory6Dropdown();

        $('#type').on('change', function() {
            updateCategoryDropdown();
            updateCategory2Dropdown();
            updateCategory3Dropdown();
            updateCategory4Dropdown();
            updateCategory5Dropdown();
            updateCategory6Dropdown();
        })

        $('#category_1').on('change', function() {
            updateCategory2Dropdown();
            updateCategory3Dropdown();
            updateCategory4Dropdown();
            updateCategory5Dropdown();
            updateCategory6Dropdown();
        });
        $('#category_2').on('change', function() {
            updateCategory3Dropdown();
            updateCategory4Dropdown();
            updateCategory5Dropdown();
            updateCategory6Dropdown();
        });
        $('#category_3').on('change', function() {
            updateCategory4Dropdown();
            updateCategory5Dropdown();
            updateCategory6Dropdown();
        });
        $('#category_4').on('change', function() {
            updateCategory5Dropdown();
            updateCategory6Dropdown();
        });
        $('#category_5').on('change', function() {
            updateCategory6Dropdown();
        });
    });

    function updateCategoryDropdown() {
        const typeCode = $('#type').val();
        const filteredCategory1 = dictionaryCategory1.filter(category1 => category1.value_code.startsWith(typeCode));
        const category1Select = $('#category_1');

        category1Select.empty();

        filteredCategory1.forEach(category1 => {
            const option = $('<option>')
                .val(category1.value_code)
                .text(category1.value_code + ' - ' + category1.value_name);

            category1Select.append(option);
        });

        const selectedCategory1 = { !!json_encode($result - > category) !! };
        category1Select.val(selectedCategory1);
        category1Select.trigger('change');
    }

    function updateCategory2Dropdown() {
        const category1Code = $('#category_1').val();
        const filteredCategory2 = dictionaryCategory2.filter(category2 => category2.value_code.startsWith(category1Code));
        const category2Select = $('#category_2');

        category2Select.empty();

        filteredCategory2.forEach(category2 => {
            const option = $('<option>')
                .val(category2.value_code)
                .text(category2.value_code + ' - ' + category2.value_name);

            category2Select.append(option);
        });

        const selectedCategory2 = { !!json_encode($result - > category_2) !! };
        category2Select.val(selectedCategory2);
        category2Select.trigger('change');
    }

    function updateCategory3Dropdown() {
        const category2Code = $('#category_2').val();
        const filteredCategory3 = dictionaryCategory3.filter(category3 => category3.value_code.startsWith(category2Code));
        const category3Select = $('#category_3');

        category3Select.empty();

        filteredCategory3.forEach(category3 => {
            const option = $('<option>')
                .val(category3.value_code)
                .text(category3.value_code + ' - ' + category3.value_name);

            category3Select.append(option);
        });

        const selectedCategory3 = { !!json_encode($result - > category_3) !! };
        category3Select.val(selectedCategory3);
        category3Select.trigger('change');
    }

    function updateCategory4Dropdown() {
        const category3Code = $('#category_3').val();
        const filteredCategory4 = dictionaryCategory4.filter(category4 => category4.value_code.startsWith(category3Code));
        const category4Select = $('#category_4');

        category4Select.empty();

        filteredCategory4.forEach(category4 => {
            const option = $('<option>')
                .val(category4.value_code)
                .text(category4.value_code + ' - ' + category4.value_name);

            category4Select.append(option);
        });

        const selectedCategory4 = { !!json_encode($result - > category_4) !! };
        category4Select.val(selectedCategory4);
        category4Select.trigger('change');
    }

    function updateCategory5Dropdown() {
        const category4Code = $('#category_4').val();
        const filteredCategory5 = dictionaryCategory5.filter(category5 => category5.value_code.startsWith(category4Code));
        const category5Select = $('#category_5');

        category5Select.empty();

        filteredCategory5.forEach(category5 => {
            const option = $('<option>')
                .val(category5.value_code)
                .text(category5.value_code + ' - ' + category5.value_name);

            category5Select.append(option);
        });

        const selectedCategory5 = { !!json_encode($result - > category_5) !! };
        category5Select.val(selectedCategory5);
        category5Select.trigger('change');
    }

    function updateCategory6Dropdown() {
        const category5Code = $('#category_5').val();
        const filteredCategory6 = dictionaryCategory6.filter(category6 => category6.value_code.startsWith(category5Code));
        const category6Select = $('#category_6');

        category6Select.empty();

        filteredCategory6.forEach(category6 => {
            const option = $('<option>')
                .val(category6.value_code)
                .text(category6.value_code + ' - ' + category6.value_name);

            category6Select.append(option);
        });

        const selectedCategory6 = { !!json_encode($result - > category_6) !! };
        category6Select.val(selectedCategory6);
        category6Select.trigger('change');
    }
</script>
@endif
@endif
@endsection