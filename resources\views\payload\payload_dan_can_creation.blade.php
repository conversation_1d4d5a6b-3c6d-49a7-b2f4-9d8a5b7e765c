@extends('layouts.guest-dash')

@section('header')
<form id="carianforms" action="{{url('/find/fulfilment/dan')}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="dncn_no" name="dncn_no" @if(isset($formSearch)) value="{{ $formSearch["dncn_no"] }}" @endif class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }

    .table tbody > tr > td {
        font-size: 10px;
    }
</style>
<div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Debit Advanced Note<br>
                <small>Masukkan <span class="text-info">No. DAN </span> pada carian diatas...</small>
            </h1>
        </div>
    </div>
@if($data == null || !isset($data))
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-list-alt"></i>
                <strong>Payload bagi Carian: </strong>@if(isset($formSearch["dncn_no"])){{$formSearch["dncn_no"]}} @endif
            </h1>
        </div>
        <div class="row">
            <div class="col-sm-6">{{ $data }}</div>
        </div>
    </div>
</div>
@endif

@if(isset($data))
<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-list-alt"></i>
                <strong>Payload bagi Carian: </strong>{{$formSearch["dncn_no"]}}
            </h1>
        </div>

        <!--DN/CN Block-->
        <div class="block">
            <div class="block-title">
                <h2>Data </h2>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($data) }}</code>
            </pre>
            <br/><br/>
        </div>
    </div>
</div>
@endif

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>
<script>
    $('#page-container').removeAttr('class');
</script>
@endsection



