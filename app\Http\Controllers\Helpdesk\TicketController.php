<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Helpdesk;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\Helpdesk\HelpdeskService;
use App\EpSupportActionLog;
use Log;
use Illuminate\Support\Str;

class TicketController extends Controller {

    public static function helpdeskService() {
        return new HelpdeskService;
    }

    public function __construct() {
        $this->middleware('auth');
    }

    public function checkTicket(Request $request) {
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ticket_number' => 'required'
            ]);
        }
        $ticketDetail = null;
        $listStatus = null;
        $threadActivity = null;
        $ticketNumber = $request->ticket_number;
        if ($ticketNumber) {
            $ticketDetail = self::HelpdeskService()->getTicketDetail($ticketNumber);
            $listStatus = self::helpdeskService()->listStatus();
            $threadActivity = self::helpdeskService()->searchItTicket($ticketNumber);
        }

        return view('helpdesk.ticket', [
            'ticket_number' => $ticketNumber,
            'data' => $ticketDetail,
            'list_status' => $listStatus,
            'threadActivity' => $threadActivity
        ]);
    }

    public function updateTicket($ticketId, Request $request) {
        session()->flashInput(request()->input());

        $currentStatus = $request->current_status;
        $updateStatusId = $request->update_status_id;
        $updateStatusName = $request->update_status_name;
        $ticketNumber = $request->ticket_number;
        $CR = Str::contains($ticketNumber, 'CR-');
        $status = 'Failed';
        $userLogin = auth()->user()->id;
        $data = collect([]);

        if ($ticketId && $updateStatusId != '') {
            $updateTicket = self::HelpdeskService()->updateTicketDetail($ticketId, $updateStatusId);
            if ($CR == 1) {
                $ITNumber = str_replace('CR-', 'IT-', $ticketNumber);
                $searchTicket = self::HelpdeskService()->searchItTicket($ticketId);
                if (count($searchTicket) > 0) {
                    Log::info($ITNumber);
                    Log::info($searchTicket);
                    $deleteItTicket = self::HelpdeskService()->deleteItTicket($ITNumber);
                    if ($deleteItTicket == 1) {
                        Log::info('Success Delete Tables Related to : ' . $ITNumber);
                    }
                }
            }
            if ($updateTicket == 'Success') {
                $status = 'Success';
            }
            $parameters = collect([]);
            $parameters->put("ticket_id", $ticketId);
            $parameters->put("current_status", $currentStatus);
            $parameters->put("update_status", $updateStatusName);
            $parameters->put("user_login", $userLogin);
            EpSupportActionLog::saveActionLog('Update-Ticket', 'HELPDESK', $parameters, $parameters, $status);
        }
        $data->put('status', $status);
        return $data;
    }

    public function updateTicketSubject($ticketId, Request $request) {
        session()->flashInput(request()->input());

        $currentSubject = $request->current_subject;
        $updateSubject = $request->update_subject;
        $status = 'Failed';
        $userLogin = auth()->user()->id;
        $data = collect([]);

        if ($ticketId && $updateSubject != '') {
            $updateTicket = self::HelpdeskService()->updateTicketDetailSubject($ticketId, $updateSubject);
            if ($updateTicket == 'Success') {
                $status = 'Success';
            }
            $parameters = collect([]);
            $parameters->put("ticket_id", $ticketId);
            $parameters->put("current_subject", $currentSubject);
            $parameters->put("update_subject", $updateSubject);
            $parameters->put("user_login", $userLogin);
            EpSupportActionLog::saveActionLog('Update-Ticket', 'HELPDESK', $parameters, $parameters, $status);
        }
        $data->put('status', $status);
        return $data;
    }

    public function addThreadEntry($ticketId, Request $request) {
        Log::info(__CLASS__ .' > ' . __FUNCTION__);
        session()->flashInput(request()->input());
        $newThread = $request->thread;
        $userLogin = auth()->user()->id;
        $data = collect([]);
        $status = "Failed";
        if ($ticketId && $newThread != '') {
            $addThread = self::HelpdeskService()->addNewThread($ticketId, $newThread);
            Log::info(__CLASS__ .' > ' . __FUNCTION__ . ' > ' .$addThread);
            if ($addThread == 'Success') {
                $status = 'Success';
            }
            $parameters = collect([]);
            $parameters->put("ticket_id", $ticketId);
            $parameters->put("new_thread", $newThread);
            $parameters->put("user_login", $userLogin);
            EpSupportActionLog::saveActionLog('Add-Thread', 'HELPDESK', $parameters, $parameters, $status);
        }else{
            Log::info(__CLASS__ .' > ' . __FUNCTION__ . ' > Error To Add Entry.Please Check..');
        }
        $data->put('status', $status);
        return $data;
    }
    
    public function updateThreadEntry($entryId, Request $request) {
        session()->flashInput(request()->input());
        $currentBody = $request->currentBody;
        $updateBody = $request->updateBody;
        $userLogin = auth()->user()->id;
        $data = collect([]);
        $status = "Failed";
        if ($entryId && $updateBody != '') {
            $updateTicket = self::HelpdeskService()->updateThreadEntry($entryId, nl2br($updateBody));
            if ($updateTicket == 'Success') {
                $status = 'Success';
            }
            $parameters = collect([]);
            $parameters->put("entry_id", $entryId);
            $parameters->put("current_body", $currentBody);
            $parameters->put("update_body", $updateBody);
            $parameters->put("user_login", $userLogin);
            EpSupportActionLog::saveActionLog('Update-Thread-Entry', 'HELPDESK', $parameters, $parameters, $status);
        }
        $data->put('status', $status);
        return $data;
    }

    public function deleteThreadEntry(Request $request) {
        session()->flashInput(request()->input());
        $ticketId = $request->ticketId;
        $threadId = $request->threadId;
        $entryId = $request->entryId;
        $entryData = $request->entryData;
        $staffId = $request->staffId;
        $userLogin = auth()->user()->id;
        $data = collect([]);
        $status = "Failed";  
        
        if ($ticketId != '' && $threadId != '') {
            $deleteEntry = self::HelpdeskService()->deleteEntry($ticketId, $threadId, $entryId, $staffId);
            if ($deleteEntry == 'Success') {
                $status = 'Success';
            }
            // $parameters = collect([]);
            // $parameters->put("ticket_id", $ticketId);
            // $parameters->put("entry_id", $threadId); 
            // $parameters->put("user_login", $userLogin);
            // EpSupportActionLog::saveActionLog('Delete-Thread-Entry', 'HELPDESK', $parameters, $parameters, $status);
        }
        $data->put('status', $status);
        return $data;
    }

    public function deleteThreadEvent(Request $request) {
        session()->flashInput(request()->input());
        $ticketId = $request->ticketId;
        $entryid = $request->entryid;
        $eventId = $request->eventId; 
        $threadId = $request->threadid;
        $staffId = $request->staffId;
        $userLogin = auth()->user()->id;
        $data = collect([]);
        $status = "Failed"; 
        if ($ticketId != '' && $entryid != '' && $eventId != '') {
            $deleteEntry = self::HelpdeskService()->deleteEvent($ticketId, $entryid, $eventId, $threadId, $staffId);
            if ($deleteEntry == 'Success') {
                $status = 'Success';
            }
            $parameters = collect([]);
            $parameters->put("ticket_id", $ticketId);
            $parameters->put("thread_id", $threadId);
            $parameters->put("entryid", $entryid); 
            $parameters->put("event_id", $eventId); 
            $parameters->put("user_login", $userLogin);
            EpSupportActionLog::saveActionLog('Delete-Thread-Event', 'HELPDESK', $parameters, $parameters, $status);
        }
        $data->put('status', $status);
        return $data;
    }
}
