@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="block">
    <div class="widget" class="inset">
        <div class="content-header">
            <div class="header-section">
                <ul class="nav-horizontal text-center">
                    <li>
                        <a href="{{ url('/prod-support/rpt/report_001_byyear') }}"><i class="fa fa-list-alt"></i><PERSON><PERSON><PERSON></a>
                    </li>
                    <li>
                        <a href="{{ url('/prod-support/rpt/report_001_updatedform') }}"><i class="fa fa-cubes"></i>Kemaskini Data</a>
                    </li>
                    <li class="active">
                        <a href="{{ url('/prod-support/rpt/report_001_statistic') }}"><i class="fa fa-area-chart"></i>Statistik</a>
                    </li>
                </ul>
            </div>
        </div>
        <form action="{{ url('/prod-support/rpt/report_001_statistic') }}" method="post">
            {{ csrf_field() }}
            <div class="row">
                <label class="col-md-1 text-right by_year_label" for="by_year">Year<span class="text-danger">*</span></label>
                <div class="col-md-2">
                    <select id="by_year" name = "by_year" required class="form-control" style="width: 200px;">
                        <option value="">Please Select</option>
                        @foreach($listYear as  $list)
                        <option value="{{$list->year}}">{{$list->year}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="col-md-7">
                    <label class="col-md-2 text-left by_service_label" for="by_year">Service Name<span class="text-danger">*</span></label>
                    <select id="by_name" name = "by_name" required class="form-control" style="width: 400px;">
                        <option value="">Please Select</option>
                        @foreach($listService as  $list)
                        <option value="{{$list->bil_no}}">{{$list->service_name_bm}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-actions form-actions-button text-right ">
                    <button type="submit" id="searchbutton" name="searchbutton" class="btn btn btn-primary" style="float: left;"><i class="fa fa-search"> Search</i></button>
                </div>
            </div>
            <br />
            <div class="widget">
                <div id="chartContainerStack" style="height: 400px; width: 100%; border-style: solid"></div>
            </div>
        </form>
    </div>
</div>

@endsection

@section('jsprivate')
<script type="text/javascript">

    window.onload = function () {
    var chart = new CanvasJS.Chart("chartContainerStack", {
    animationEnabled: true,
            animationDuration: 1000,
            title: {
            text: "Statistik " + {!! json_encode($serviceName) !!},
                    fontSize: 30,
                    fontFamily: "calibri",
            },
            theme: "light3",
            toolTip:{
            shared: true,
                    reversed: true
            },
            axisY: {
            title: "Total Transaction"
            },
            axisX: {
            title: "Month"
            },
            legend: {
            cursor: "pointer",
                    itemclick: toggleDataSeries
            },
            data: [
            {
            type: "column",
                    name: "Value",
                    showInLegend: true,
                    indexLabel : "{y}" ,
                    indexLabelPlacement: "outside",
                    indexLabelFontSize: 10,
                    indexLabelFontWeight: "bold",
                    dataPoints : {!! json_encode($collectDataAll) !!},
            },
            ],
    });
            chart.render();
            function toggleDataSeries(e) {
            if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
            e.dataSeries.visible = false;
            } else {
            e.dataSeries.visible = true;
            }
            e.chart.render();
            }
    }
</script>
<script type="text/javascript" src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
@endsection


