<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;

/**
 * Description : Get list pending MMINF , is_sent = 0
 * <AUTHOR>
 */
trait GFMASService {

    /**
     * Get list using collect([])
     * @param type $supplierId
     * @return results
     */
    protected function getListBackLogMMINF() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                   "select count(*) as total
                    from NGEP_PRD.DI_MMINF 
                    where is_sent =0
                    order by created_date desc");
        return $results;
    }

}
