<?php

namespace App\Services\Ep;

use App\Constants\SmActionStatus;
use App\Model\Ep\SMSupplier;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use stdClass;
use App\Migrate\MigrateUtils;

class SmSupplierService
{
    public function endpoint_ssm()
    {
        return 'http://192.168.63.205:7011/RetrieveSSMInfo/v1.0?wsdl';
    }

    
    public function get_sm_supplier_international()
    {
        return DB::connection('oracle_nextgen_rpt')
            ->select("SELECT
                            *
                        FROM
                            sm_supplier ss,
                            sm_company_basic scb
                        WHERE
                            ss.latest_appl_id = scb.appl_id 
                            
                            AND (lower(ss.company_name) LIKE ('% limited%')
                                OR lower(ss.company_name) LIKE ('% pte%')
                                    OR lower(ss.company_name) LIKE ('% ltd%'))
                            "
                            , array()); //3033
    }

    public function runUpdateSupplierInternational(){
        $listSuppliers = $this->get_sm_supplier_international();
        $total = count($listSuppliers);
        foreach ($listSuppliers as $key => $sm){
            $ssm_array = $this->retrieve_sm_info($sm->reg_no, $sm->business_type);
                    
            $ssm_reg_no = '';
            $company_name = '';
            $check_digit = '';
            $country= '';
            foreach ($ssm_array as $s) {
                if(isset($s['tag'])){
                    $v = isset($s['value'])? $s['value']: '';
                    switch($s['tag']){
                        case 'EPMF:STATUSCODE': { $status_cd = $v; break;}
                        case 'EPMF:STATUSDESC': { $status_desc = $v; break;}
                        case 'RET:BUSINESSREGISTRATIONNO': { $ssm_reg_no = $v; break;}
                        case 'RET:BUSINESSCOMPANYNAME': { $company_name = $v; break;}
                        case 'RET:CHECKDIGIT': { $check_digit = $v; break;}
                        case 'RET:COUNTRY': { $country = $v; break;}
                        default: 'Do Nothing!';
                    }
                }
            }
            if($status_cd == '70067' || $status_cd=='70002'){
                if($sm->business_type == 'ROC'){
                    $sm->business_type = 'ROB';
                }else{
                    $sm->business_type = 'ROC';
                }
                $ssm_array = $this->retrieve_sm_info($sm->reg_no, $sm->business_type);
                foreach ($ssm_array as $s) {
                    if(isset($s['tag'])){
                        $v = isset($s['value'])? $s['value']: '';
                        switch($s['tag']){
                            case 'EPMF:STATUSCODE': { $status_cd = $v; break;}
                            case 'EPMF:STATUSDESC': { $status_desc = $v; break;}
                            case 'RET:BUSINESSREGISTRATIONNO': { $ssm_reg_no = $v; break;}
                            case 'RET:BUSINESSCOMPANYNAME': { $company_name = $v; break;}
                            case 'RET:CHECKDIGIT': { $check_digit = $v; break;}
                            case 'RET:COUNTRY': { $country = $v; break;}
                            default: 'Do Nothing!';
                        }
                    }
                }
            }
            if(strlen($country) > 0 && strlen($sm->ssm_company_country) == 0){
                MigrateUtils::logDump(($key+1)."/$total >> WS Response:".$status_cd . ' - ' . $status_desc);
                MigrateUtils::logDump('*** company_basic_id: '.$sm->company_basic_id. ' >> '.$sm->reg_no . '| ' . $sm->company_name . '>> country : '.$country);

                $fields = [
                    'ssm_company_country' => $country
                ];
                DB::connection('oracle_nextgen_fullgrant')->table('sm_company_basic')->where('company_basic_id', $sm->company_basic_id)->update($fields);
            }
            
            //dd('done');
        }
    }

    public function get_sm_supplier_with_missing_dash_on_ssmno()
    {
        return DB::connection('oracle_nextgen_rpt')
            ->select("SELECT supplier_id, company_name, reg_no, ep_no, business_type, record_status,
                        created_date AS sm_created_date, created_by AS sm_created_by,
                        changed_date AS sm_changed_date, changed_by AS sm_changed_by,
                        latest_appl_id
                FROM sm_supplier ss
                WHERE ss.business_type IN ('C', 'J', 'F', 'I')
                    AND ss.record_status = 1
                    AND REGEXP_LIKE (ss.reg_no, '^[^\/\\()-]+$', 'i')", array()); //3033
    }

    public function save_sm_supplier($objs)
    {
        if ($objs) {
            foreach ($objs as $obj) {
                $s = SMSupplier::find($obj->supplier_id);
                try {
                    $isNew = false;
                    if( $s == null){
                        $isNew = true;
                        $s = new SMSupplier();
                    }
                    
                    $s->company_name = $obj->company_name;
                    $s->reg_no = $obj->reg_no;
                    $s->ep_no = $obj->ep_no;
                    $s->business_type = $obj->business_type;
                    $s->supplier_type = $obj->supplier_type;
                    $s->record_status = $obj->record_status;
                    $s->sm_created_date = $obj->sm_created_date;
                    $s->sm_created_by = $obj->sm_created_by;
                    $s->sm_changed_date = $obj->sm_changed_date;
                    $s->sm_changed_by = $obj->sm_changed_by;
                    $s->latest_appl_id = $obj->latest_appl_id;
                    $s->created_at = Carbon::now();
                    $s->status_action = SmActionStatus::IN_PROGRESS;
                    $s->remarks = '';
                    if($isNew == true){
                        $s->supplier_id = $obj->supplier_id;
                        $s->save();
                    }else{
                        $s->update();
                    }
                    
                    
                } catch (Exception $e) {
                    MigrateUtils::logDump($e->getMessage(), array(json_encode($obj)));
                }
            }
        }
    }

    public function update_epss_sm_supplier($id, $status, $remarks)
    {
        $s = SMSupplier::find($id);
        $s->status_action = $status;
        $s->changed_date = Carbon::now();
        $s->remarks = $remarks;
        $s->save();
    }

    public function update_sm_supplier($id, $reg_no, $company)
    {
        $fields = [
            'reg_no' => $reg_no,
            'company_name' => $company,
            'changed_by' => 1,
            'changed_date' => Carbon::now(),
        ];

        DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER')->where('supplier_id', $id)->update($fields);
    }

    public function is_character($reg_no){
        $lchar = substr($reg_no, strlen($reg_no) - 1, 1);

        return is_numeric($lchar) == 1 ? false: true;
    }

    public function retrieve_sm_info($reg_no, $type)
    {
        //MigrateUtils::logDump(" >> retrieve_sm_info : before: ".$reg_no);
        $reg_no = str_replace('-','',$reg_no);
        if($this->is_character($reg_no)){
            $reg_no = substr($reg_no, 0, -1);
        }
        $business_type = 'ROC';
        switch ($type) {
            case 'I': {
                $business_type = 'ROC';
                break;
            }
            case 'J': {
                    $business_type = 'ROC';
                    break;
                }
            case 'F':
            case 'C': {
                    $business_type = 'ROB';
                    break;
                }
            default:
        }
        MigrateUtils::logDump(" >> retrieve_sm_info :check: ".$reg_no.' , business_type: '.$business_type);
        $uuid  = \Ramsey\Uuid\Uuid::uuid4();
        $xml_content    =
            "<x:Envelope
            xmlns:x='http://schemas.xmlsoap.org/soap/envelope/'
            xmlns:ret='http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo'>
            <x:Header/>
            <x:Body>
                <ret:EPMFRq>
                    <ns1:RqHeader xmlns:ns1='http://www.ep.gov.my/Schema/1-0/epmf'>
                         <ns1:ConsumerID>EPP-001</ns1:ConsumerID>
                         <ns1:UID>
                             <ns1:RqUID>$uuid</ns1:RqUID>
                         </ns1:UID>
                    </ns1:RqHeader>
                    <ret:RetrieveSSMInfoRq>
                        <ret:BusinessRegistrationNo>$reg_no</ret:BusinessRegistrationNo>
                        <ret:Type>$business_type</ret:Type>
                    </ret:RetrieveSSMInfoRq>
                </ret:EPMFRq>
            </x:Body>
        </x:Envelope>";

        $headers = ['Content-Type: application/xml'];

        try {
            $soap_do = curl_init();
            curl_setopt($soap_do, CURLOPT_URL, $this->endpoint_ssm());
            curl_setopt($soap_do, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($soap_do, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($soap_do, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($soap_do, CURLOPT_POST,           true);
            curl_setopt($soap_do, CURLOPT_POSTFIELDS,     $xml_content);

            $output = curl_exec($soap_do);
            curl_close($soap_do);
            $p = xml_parser_create();
            xml_parse_into_struct($p, $output, $vals, $index);
            xml_parser_free($p);

            $ssm = new stdClass;
            if ($vals) {
                $ssm = json_decode(json_encode($vals), true);
            }

            return $ssm;
        } catch (Exception $ex) {
            MigrateUtils::logDump($ex->getMessage());
            return new stdClass;
        }
    }

    public function is_ct_mod_exist($id)
    {
        $result = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT CV.reg_no
                        FROM ct_contract_value CV, ct_contract con, sm_supplier sm
                    WHERE CV.contract_ver_id = con.latest_contract_ver_id
                        AND sm.supplier_id = con.supplier_id
                        AND sm.supplier_id = ? ", array($id));

        return $result ? true : false;
    }

    public function is_supplier_exist($ssm_no, $supplierType)
    {
        $result = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT 1
                        FROM sm_supplier s, SM_APPL a
                    WHERE 
                    s.latest_appl_id = a.APPL_ID
                    AND s.reg_no = ? 
                    AND a.SUPPLIER_TYPE = ? 
                    AND s.record_status = 1", array($ssm_no,$supplierType));

        return $result ? true : false;
    }

    public function fetch_and_save_sm_supplier()
    {
        // $objs = $this->get_sm_supplier_with_missing_dash_on_ssmno();
        $objs = $this->get_sm_supplier_with_missing_dash_on_ssmno_version2();
        MigrateUtils::logDump(__CLASS__." >> ".__FUNCTION__." Total to import : ".count($objs));
        $this->save_sm_supplier($objs);
    }

    public function validate_and_update_sm_supplier($limit)
    {
        try {
            $all_sm = SMSupplier::where('status_action', SmActionStatus::IN_PROGRESS)->take($limit)->get();
            $total = $all_sm->count();
            MigrateUtils::logDump('To Update SSM >> Total Suppliers - ' . $total );
            if ($all_sm) {
                foreach ($all_sm as $key => $sm) {
                    $ssm_array = $this->retrieve_sm_info($sm->reg_no, $sm->business_type);
                    
                    $ssm_reg_no = '';
                    $company_name = '';
                    $check_digit = '';
                    foreach ($ssm_array as $s) {
                        if(isset($s['tag'])){
                            $v = isset($s['value'])? $s['value']: '';
                            switch($s['tag']){
                                case 'EPMF:STATUSCODE': { $status_cd = $v; break;}
                                case 'EPMF:STATUSDESC': { $status_desc = $v; break;}
                                case 'RET:BUSINESSREGISTRATIONNO': { $ssm_reg_no = $v; break;}
                                case 'RET:BUSINESSCOMPANYNAME': { $company_name = $v; break;}
                                case 'RET:CHECKDIGIT': { $check_digit = $v; break;}
                                default: 'Do Nothing!';
                            }
                        }
                    }
                    MigrateUtils::logDump(($key+1)."/$total >> WS Response:".$status_cd . ' - ' . $status_desc);
                    MigrateUtils::logDump('*** SupplierID:'.$sm->supplier_id. ', compare [ '.$sm->reg_no . '|' . $ssm_reg_no.'-'.$check_digit . '  ,  ' . $sm->company_name . '|' . $company_name. ' ]');

                    if ($status_cd === '70060') {
                        $ssm_reg_no = $ssm_reg_no.'-'.$check_digit;
                        MigrateUtils::logDump('***New SSM No. = '.$ssm_reg_no);
                        if ($this->is_supplier_exist($ssm_reg_no,$sm->supplier_type)) {
                            $this->update_epss_sm_supplier($sm->supplier_id, SmActionStatus::REGNO_EXISTS, 'Supplier Exist!');
                            MigrateUtils::logDump('***Checking Supplier Exist!');
                        } else {
                            if ($this->is_ct_mod_exist($sm->supplier_id)) {
                                $this->update_epss_sm_supplier($sm->supplier_id, SmActionStatus::CONTRACT_ACTIVE, 'CT Module Exist!');
                                MigrateUtils::logDump('***CT Module Exist!');
                            } else {
                                $this->update_sm_supplier($sm->supplier_id, $ssm_reg_no, $company_name);
                                $this->update_epss_sm_supplier($sm->supplier_id, SmActionStatus::COMPLETED, 'Both Supplier & CT Module Not Exist!');
                                MigrateUtils::logDump('***Both Supplier & CT Module Not Exist! >> Updated SSM No. in SM_SUPPLIER');
                            }
                        }
                    } else {
                        $this->update_epss_sm_supplier($sm->supplier_id, SmActionStatus::SSM_NOT_FOUND, $status_cd . ' - ' . $status_desc);
                    }

                    sleep ( rand ( 5, 20));
                }
            }
        } catch (Exception $e) {
            MigrateUtils::logErrorDump('Oops! Error - ' . $e->getMessage());
        }
    }

    public function get_sm_supplier_with_missing_dash_on_ssmno_version2()
    {
        return DB::connection('oracle_nextgen_rpt')
            ->select("SELECT * FROM (
            
            SELECT
                            'ROB Wrong format' AS remarks,
                            ss.company_name as company_name,
                            decode(sma.supplier_type, 'K', 'Contractor', 'Consultant') As supplier_type_desc,
                            sma.supplier_type,
                            ss.reg_no AS reg_no,
                            CASE
                                ss.business_type
                        WHEN 'A' THEN 'Individual'
                                WHEN 'B' THEN 'Limited Liability Partnership (LLP)'
                                WHEN 'C' THEN 'ROB - Partnership'
                                WHEN 'D' THEN 'Business Registration in Sabah (partnership)'
                                WHEN 'E' THEN 'Business Registration in Sarawak (partnership)'
                                WHEN 'F' THEN 'ROB - Sole-Proprietorship'
                                WHEN 'G' THEN 'Business Registration in Sabah (Sole-Proprietorship)'
                                WHEN 'H' THEN 'Business Registration in Sarawak (Sole-Proprietorship)'
                                WHEN 'I' THEN 'ROC - Sdn Bhd'
                                WHEN 'J' THEN 'ROC - Bhd'
                                WHEN 'K' THEN 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)'
                                WHEN 'L' THEN 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)'
                                WHEN 'M' THEN 'Society'
                                WHEN 'O' THEN 'Organization (Others)'
                                WHEN 'P' THEN 'Cooperative (Others)'
                                WHEN 'R' THEN 'Professional Body'
                                WHEN 'Q' THEN 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)'
                                WHEN 'T' THEN 'PTJ Govt. Seller'
                            END AS business_type_desc,
                            ss.business_type,
                            psd1.status_name AS supplier_status,
                            sma.mof_no AS mof_number,
                            sma.exp_date AS mof_expiry_date,
                            psd.status_name AS mof_registration_status,
                            ss.supplier_id,
                            ss.ep_no,
                            ss.created_date AS sm_created_date ,
                            ss.created_by AS sm_created_by,
                            ss.changed_date AS sm_changed_date,
                            ss.changed_by AS sm_changed_by,
                            ss.RECORD_STATUS ,
                            ss.latest_appl_id 
                        FROM
                            sm_supplier ss,
                            sm_mof_account sma,
                            pm_status_desc psd,
                            pm_status_desc psd1
                        WHERE
                            ss.supplier_id = sma.supplier_id
                            AND sma.record_status = psd.status_id
                            AND ss.record_status = psd1.status_id
                            AND psd.language_code = 'en'
                            AND psd1.language_code = 'en'
                            AND ss.record_status IN (1, 5, 8)
                            AND ss.business_type IN ('C', 'F')
                            AND
                            NOT REGEXP_LIKE(ss.reg_no, '^[a-z]|^[00]', 'i')
                        UNION
                        SELECT
                            'ROB No Dash' AS remarks,
                            ss.company_name as company_name,
                            decode(sma.supplier_type, 'K', 'Contractor', 'Consultant') As supplier_type_desc,
                            sma.supplier_type,
                            ss.reg_no AS reg_no,
                            CASE
                                ss.business_type
                        WHEN 'A' THEN 'Individual'
                                WHEN 'B' THEN 'Limited Liability Partnership (LLP)'
                                WHEN 'C' THEN 'ROB - Partnership'
                                WHEN 'D' THEN 'Business Registration in Sabah (partnership)'
                                WHEN 'E' THEN 'Business Registration in Sarawak (partnership)'
                                WHEN 'F' THEN 'ROB - Sole-Proprietorship'
                                WHEN 'G' THEN 'Business Registration in Sabah (Sole-Proprietorship)'
                                WHEN 'H' THEN 'Business Registration in Sarawak (Sole-Proprietorship)'
                                WHEN 'I' THEN 'ROC - Sdn Bhd'
                                WHEN 'J' THEN 'ROC - Bhd'
                                WHEN 'K' THEN 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)'
                                WHEN 'L' THEN 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)'
                                WHEN 'M' THEN 'Society'
                                WHEN 'O' THEN 'Organization (Others)'
                                WHEN 'P' THEN 'Cooperative (Others)'
                                WHEN 'R' THEN 'Professional Body'
                                WHEN 'Q' THEN 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)'
                                WHEN 'T' THEN 'PTJ Govt. Seller'
                            END AS business_type_desc,
                            ss.business_type,
                            psd1.status_name AS supplier_status,
                            sma.mof_no AS mof_number,
                            sma.exp_date AS mof_expiry_date,
                            psd.status_name AS mof_registration_status,
                            ss.supplier_id,
                            ss.ep_no,
                            ss.created_date AS sm_created_date,
                            ss.created_by AS sm_created_by,
                            ss.changed_date AS sm_changed_date,
                            ss.changed_by AS sm_changed_by,
                            ss.RECORD_STATUS ,
                            ss.latest_appl_id 
                        FROM
                            sm_supplier ss,
                            sm_mof_account sma,
                            pm_status_desc psd,
                            pm_status_desc psd1
                        WHERE
                            ss.supplier_id = sma.supplier_id
                            AND sma.record_status = psd.status_id
                            AND ss.record_status = psd1.status_id
                            AND psd.language_code = 'en'
                            AND psd1.language_code = 'en'
                            AND ss.record_status IN (1, 5, 8)
                            AND ss.business_type IN ('C', 'F')
                            AND
                            NOT REGEXP_LIKE(ss.reg_no, '[-]')
                        UNION
                        SELECT
                            'ROC Wrong format' AS remarks,
                            ss.company_name as company_name,
                            decode(sma.supplier_type, 'K', 'Contractor', 'Consultant') As supplier_type_desc,
                            sma.supplier_type,
                            ss.reg_no AS reg_no,
                            CASE
                                ss.business_type
                        WHEN 'A' THEN 'Individual'
                                WHEN 'B' THEN 'Limited Liability Partnership (LLP)'
                                WHEN 'C' THEN 'ROB - Partnership'
                                WHEN 'D' THEN 'Business Registration in Sabah (partnership)'
                                WHEN 'E' THEN 'Business Registration in Sarawak (partnership)'
                                WHEN 'F' THEN 'ROB - Sole-Proprietorship'
                                WHEN 'G' THEN 'Business Registration in Sabah (Sole-Proprietorship)'
                                WHEN 'H' THEN 'Business Registration in Sarawak (Sole-Proprietorship)'
                                WHEN 'I' THEN 'ROC - Sdn Bhd'
                                WHEN 'J' THEN 'ROC - Bhd'
                                WHEN 'K' THEN 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)'
                                WHEN 'L' THEN 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)'
                                WHEN 'M' THEN 'Society'
                                WHEN 'O' THEN 'Organization (Others)'
                                WHEN 'P' THEN 'Cooperative (Others)'
                                WHEN 'R' THEN 'Professional Body'
                                WHEN 'Q' THEN 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)'
                                WHEN 'T' THEN 'PTJ Govt. Seller'
                            END AS business_type_desc,
                            ss.business_type,
                            psd1.status_name AS supplier_status,
                            sma.mof_no AS mof_number,
                            sma.exp_date AS mof_expiry_date,
                            psd.status_name AS mof_registration_status,
                            ss.supplier_id,
                            ss.ep_no,
                            ss.created_date AS sm_created_date,
                            ss.created_by AS sm_created_by,
                            ss.changed_date AS sm_changed_date,
                            ss.changed_by AS sm_changed_by,
                            ss.RECORD_STATUS ,
                            ss.latest_appl_id 
                        FROM
                            sm_supplier ss,
                            sm_mof_account sma,
                            pm_status_desc psd,
                            pm_status_desc psd1
                        WHERE
                            ss.supplier_id = sma.supplier_id
                            AND sma.record_status = psd.status_id
                            AND ss.record_status = psd1.status_id
                            AND psd.language_code = 'en'
                            AND psd1.language_code = 'en'
                            AND ss.record_status IN (1, 5, 8)
                            AND ss.business_type IN ('I', 'J')
                            AND
                            NOT REGEXP_LIKE(ss.reg_no, '^[1-9]', 'i')
                        UNION
                            SELECT
                            'ROC No Dash' AS remarks,
                            ss.company_name as company_name,
                            decode(sma.supplier_type, 'K', 'Contractor', 'Consultant') As supplier_type_desc,
                            sma.supplier_type,
                            ss.reg_no AS reg_no,
                            CASE
                                ss.business_type
                        WHEN 'A' THEN 'Individual'
                                WHEN 'B' THEN 'Limited Liability Partnership (LLP)'
                                WHEN 'C' THEN 'ROB - Partnership'
                                WHEN 'D' THEN 'Business Registration in Sabah (partnership)'
                                WHEN 'E' THEN 'Business Registration in Sarawak (partnership)'
                                WHEN 'F' THEN 'ROB - Sole-Proprietorship'
                                WHEN 'G' THEN 'Business Registration in Sabah (Sole-Proprietorship)'
                                WHEN 'H' THEN 'Business Registration in Sarawak (Sole-Proprietorship)'
                                WHEN 'I' THEN 'ROC - Sdn Bhd'
                                WHEN 'J' THEN 'ROC - Bhd'
                                WHEN 'K' THEN 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)'
                                WHEN 'L' THEN 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)'
                                WHEN 'M' THEN 'Society'
                                WHEN 'O' THEN 'Organization (Others)'
                                WHEN 'P' THEN 'Cooperative (Others)'
                                WHEN 'R' THEN 'Professional Body'
                                WHEN 'Q' THEN 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)'
                                WHEN 'T' THEN 'PTJ Govt. Seller'
                            END AS business_type_desc,
                            ss.business_type,
                            psd1.status_name AS supplier_status,
                            sma.mof_no AS mof_number,
                            sma.exp_date AS mof_expiry_date,
                            psd.status_name AS mof_registration_status,
                            ss.supplier_id,
                            ss.ep_no,
                            ss.created_date AS sm_created_date,
                            ss.created_by AS sm_created_by,
                            ss.changed_date AS sm_changed_date,
                            ss.changed_by AS sm_changed_by,
                            ss.RECORD_STATUS ,
                            ss.latest_appl_id 
                        FROM
                            sm_supplier ss,
                            sm_mof_account sma,
                            pm_status_desc psd,
                            pm_status_desc psd1
                        WHERE
                            ss.supplier_id = sma.supplier_id
                            AND sma.record_status = psd.status_id
                            AND ss.record_status = psd1.status_id
                            AND psd.language_code = 'en'
                            AND psd1.language_code = 'en'
                            AND ss.record_status IN (1, 5, 8)
                            AND ss.business_type IN ('I', 'J')
                            AND
                            NOT REGEXP_LIKE(ss.reg_no, '[-]')
            ) tmp 
            WHERE  REGEXP_LIKE (tmp.reg_no, '^[^\/\\()-]+$', 'i')"); //3033
    }
}
