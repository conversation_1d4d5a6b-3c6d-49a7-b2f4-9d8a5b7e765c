<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\PaymentReceiptService;
use Carbon\Carbon;
use DB;
use File;
use Log;
use Guzzle;
use App\Migrate\ClientRazerApi;
class PaymentReceiptController extends Controller {

    use OSBService;
    use SSHService;
    use PaymentReceiptService;

    public function getDashboardPaymentReceipt() {
        return view('dashboard.paymentreceipt', []);
    }

    public function checkSyncPaymentReceiptEpRazer() {

        $time1hourBefore = Carbon::now()->subHours(1);
        $noOfDay = 14;
        ClientRazerApi::dailyReportTrans($time1hourBefore->format('Y-m-d H:i:s'),'7200');

        $yesterday = Carbon::yesterday()->format('Y-m-d');
        $today = Carbon::now()->format('Y-m-d');
        $listPaymentReceiptEp = $this->listPaymentReceiptEpByLastNoOfDay($noOfDay);

        $listPaymentReceiptRazer  = $this->listPaymentReceiptRazerByLastNoOfDay($noOfDay);

        $listPaymentReceiptAr502 = $this->listPaymentReceiptAr502ByLastNoOfDay($noOfDay);

        $listPaymentReceiptMaybank = $this->listPaymentReceiptMaybankByLastNoOfDay($noOfDay);

        foreach ($listPaymentReceiptEp  as $objPay){
            $objRazer  = $listPaymentReceiptRazer->where('payment_date',$objPay->payment_date)->first();
            $objPay->total_razer = null;
            $objPay->sync_desc = 'N/A';
            $objPay->is_sync = false;
            if($objRazer){
                $objPay->total_razer = $objRazer->total;
                $diffTotal = $objPay->total_razer - $objPay->total;

                if($diffTotal == 0){
                    $objPay->diff_total = 0;
                    $objPay->sync_desc = "Synced" ;
                    $objPay->is_sync = true;
                }else{
                    $objPay->diff_total = $diffTotal;
                    $objPay->sync_desc = "Found $diffTotal not proper sync" ;
                    $objPay->is_sync = false;
                }
            } 

            $objAr502  = $listPaymentReceiptAr502->where('payment_date',$objPay->payment_date)->first();
            $objPay->total_ar502 = 0;
            $objPay->is_ar502_sync = false;
            $objPay->sync_ar502_desc = 'N/A';
            if($objAr502){
                $objPay->total_ar502 = $objAr502->total;
                $diffTotalAr502 = $objPay->total - $objPay->total_ar502;
                if($diffTotalAr502 == 0){
                    $objPay->diff_ar502_total = 0;
                    $objPay->sync_ar502_desc = "All sent" ;
                    $objPay->is_ar502_sync = true;
                }else{
                    $objPay->diff_ar502_total = $diffTotalAr502;
                    $objPay->sync_ar502_desc = "Found $diffTotalAr502 not send by AR502 File" ;
                    $objPay->is_ar502_sync = false;

                    //Checking if today. Just for information. Data today will be sent on 6AM tomorrrow.
                    if(Carbon::parse($objPay->payment_date)->format('Y-m-d') == Carbon::now()->format('Y-m-d') ){
                        $objPay->sync_ar502_desc = "Found $diffTotalAr502 not send by AR502 File. Data will send on 6AM tomorrow" ;
                    }
                }
            }

            $objMaybank  = $listPaymentReceiptMaybank->where('payment_date',$objPay->payment_date)->first();
            $objPay->total_maybank = null;
            $objPay->sync_maybank_desc = 'N/A';
            $objPay->is_maybank_sync = false;
            if($objMaybank){
                $objPay->total_maybank = $objMaybank->total;
                $diffTotal = abs($objPay->total_maybank - $objPay->total);

                if($diffTotal == 0){
                    $objPay->diff_maybank_total = 0;
                    $objPay->sync_maybank_desc = "Synced" ;
                    $objPay->is_maybank_sync = true;
                }else{
                    $objPay->diff_maybank_total = $diffTotal;
                    $objPay->sync_maybank_desc = "different: $diffTotal" ;
                    $objPay->is_maybank_sync = false;
                }
            }

        }

        $html = "";
        $html .= "<div class='row'>
                    <div class='col-lg-12'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Daily Payment Receipt Statistic</strong></h2>
                            </div>
                                <table class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th class='text-center'>Date</th>
                                            <th class='text-center'>Total eP Receipt</th>
                                            <th class='text-center'>Total Razer Receipt</th>
                                            <th class='text-center' style='border:1px solid;'>Sync eP <> Razer </th>
                                            <th class='text-center'>Total Maybank Receipt</th>
                                            <th class='text-center'>Sync eP <> Maybank Report</th>
                                            <th class='text-center'>Total AR502 (After 6AM)</th>
                                            <th class='text-center'>Sync eP <> IGFMAS AR502 File</th>
                                        </tr>
                                    </thead>
                                <tbody>";

        foreach ($listPaymentReceiptEp as $data) {
            if ($data->is_sync === false) {
                //overwrite value to put modalbox display list
                $data->sync_desc = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/paymentreceipt/failed-sync-payment/$data->payment_date'
                            data-title='List Order ID Not Successfully Sync' >$data->sync_desc</a>";
            }else{
                $data->sync_desc =  "<a href='#modal-list-data' 
                class='modal-list-data-action label label-success' 
                data-isremoveDatatable='true' 
                data-toggle='modal' data-url='/dashboard/paymentreceipt/summary-transaction-daily/$data->payment_date'
                data-title='Summary Payment eP ($data->payment_date)' >$data->sync_desc</a>";
            }

            if ($data->is_ar502_sync === false) {
                //overwrite value to put modalbox display list
                $data->sync_ar502_desc = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/paymentreceipt/failed-sync-ar502/$data->payment_date'
                            data-title='List Receipt No. Not Successfuly Sent By AR502' >$data->sync_ar502_desc</a>";
            }else{
                $data->sync_ar502_desc =  "<a href='#modal-list-data' 
                class='modal-list-data-action label label-success' 
                data-isremoveDatatable='true' 
                data-toggle='modal' data-url='/dashboard/paymentreceipt/summary-transaction-daily/$data->payment_date'
                data-title='Summary Payment eP ($data->payment_date)' >$data->sync_ar502_desc</a>";
            }
            if ($data->is_maybank_sync === false) {
                //overwrite value to put modalbox display list
                $data->sync_maybank_desc = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/paymentreceipt/failed-sync-maybank/$data->payment_date'
                            data-title='List Payment Transaction ' >$data->sync_maybank_desc</a>";
            }else{
                $data->sync_maybank_desc =  "<a href='#modal-list-data' 
                class='modal-list-data-action label label-success' 
                data-isremoveDatatable='true' 
                data-toggle='modal' data-url='/dashboard/paymentreceipt/summary-transaction-daily/$data->payment_date'
                data-title='Summary Payment Maybank ($data->payment_date)' >$data->sync_maybank_desc</a>";
            }
            $html .= "
                                    <tr>
                                        <td class='text-center'><strong>$data->payment_date</strong></td>
                                        <td class='text-center'><a href='#modal-list-data' 
                                            class='modal-list-data-action label label-info' 
                                            data-isbtndownload='true' 
                                            data-urldownloadall='/dashboard/paymentreceipt/download-all-receipts/$data->payment_date'
                                            data-toggle='modal' data-url='/dashboard/paymentreceipt/list-order-payment/eP/$data->payment_date'
                                            data-title='List Order Payment eP' >$data->total</a></td>
                                        <td class='text-center'><a href='#modal-list-data' 
                                            class='modal-list-data-action label label-info' 
                                            data-toggle='modal' data-url='/dashboard/paymentreceipt/list-order-payment/RAZER/$data->payment_date'
                                            data-title='List Order Payment Razer' >$data->total_razer</a></td> 
                                        <td class='text-center'>$data->sync_desc</td> 
                                        <td class='text-center'><a href='#modal-list-data' 
                                            class='modal-list-data-action label label-info' 
                                            data-toggle='modal' data-url='/dashboard/paymentreceipt/list-order-payment/MAYBANK/$data->payment_date'
                                            data-title='List Order Payment Maybank' >$data->total_maybank</a></td> 
                                        <td class='text-center'>$data->sync_maybank_desc</td>  
                                        <td class='text-center'><a href='#modal-list-data' 
                                            class='modal-list-data-action label label-info' 
                                            data-toggle='modal' data-url='/dashboard/paymentreceipt/list-order-payment/AR502/$data->payment_date'
                                            data-title='List Order Payment eP' >$data->total_ar502</a></td>
                                        <td class='text-center'>$data->sync_ar502_desc</td>   
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function listOrderByDate($sourcePayment,$paymentDate) {
        $epReportUrl = env('EP_REPORT_URL','http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd');
        $list = array();
        if($sourcePayment=='RAZER'){
            $list  = $this->listPaymentReceiptRazerByDate($paymentDate);
                $html = "   <thead>
                        <tr>
                            <th class='text-center'>No.</th>
                            <th class='text-center'>Date</th>
                            <th class='text-center'>Order ID</th>
                            <th class='text-center'>Trans ID</th>
                            <th class='text-center'>Channel</th>
                            <th class='text-center'>Card Type</th>
                            <th class='text-center'>Amount</th>
                            <th class='text-center'>Bank Trans ID</th>
                            <th class='text-center'>Billing Name</th>
                        </tr>
                        </thead>
                        <tbody>";
                $count = 1;
                foreach ($list as $row) {
                    $html .= "
                                <tr>
                                    <td style='width:10px' class='text-center'> ". $count++ ." </td>
                                    <td class='text-center'>". $row->billing_date ."</td>
                                    <td class='text-center'>". $row->order_id ."</td>
                                    <td class='text-center'><a href='https://portal.merchant.razer.com/index.php?mod=transactions&opt=view&tid=$row->tran_id' target='blank'>$row->tran_id</a></td>
                                    <td class='text-center'>". $row->channel ."</td>
                                    <td class='text-center'>". $row->card_type ."</td>
                                    <td class='text-center'>". $row->amount ."</td>
                                    <td class='text-center'>". $row->bank_transaction_id ."</td>
                                    <td class='text-center'>". $row->service_item ."</td>
                                </tr>";
                }

                $html .= "
                            </tbody>";
                return $html;
        }else if($sourcePayment=='eP'){
            $list = $this->listPaymentReceiptEpByDate($paymentDate);
            $html = "   <thead>
                    <tr>
                        <th class='text-center'>No.</th>
                        <th class='text-center'>Payment ID / Order ID</th>
                        <th class='text-center'>Payment Date</th>
                        <th class='text-center'>Receipt No.</th>
                        <th class='text-center'>Bill Type</th>
                        <th class='text-center'>Channel</th>
                        <th class='text-center'>Amount</th>
                    </tr>
                    </thead>
                    <tbody>";
            $count = 1;
            foreach ($list as $row) {
                $html .= "
                            <tr>
                                <td style='width:10px' class='text-center'> ". $count++ ." </td>
                                <td class='text-center'>". $row->payment_id ."</td>
                                <td class='text-center'>". $row->payment_date ."</td>
                                <td class='text-center'><a target='_blank' href='$epReportUrl&report=sm_payment_receipt.jsp&pmntid=$row->payment_id&destype=cache&desformat=pdf' ><i class='fa fa-download'></i> $row->receipt_no</a></td>
                                <td class='text-center'>". strtoupper($row->bill_type) ."</td>
                                <td class='text-center'>". strtoupper($row->channel_name) ."</td>
                                <td class='text-center'>". $row->payment_amt ."</td>
                            </tr>";
            }

            $html .= "
                        </tbody>";
            return $html;
        }else if($sourcePayment=='AR502'){
            $list  = $this->listPaymentReceiptAr502ByDate($paymentDate);
                $html = "   <thead>
                        <tr>
                            <th class='text-center'>No.</th>
                            <th class='text-center'>Date</th>
                            <th class='text-center'>Receipt No.</th>
                            <th class='text-center'>Amount</th>
                            <th class='text-center'>Billing Type</th>
                            <th class='text-center'>File Name</th>
                        </tr>
                        </thead>
                        <tbody>";
                $count = 1;
                foreach ($list as $row) {
                    $linkUrlFile = url('/find/osb/batch/file/'.$row->file_name); 
                    $html .= "
                                <tr>
                                    <td style='width:10px' class='text-center'> ". $count++ ." </td>
                                    <td class='text-center'>". $row->payment_date ."</td>
                                    <td class='text-center'>". $row->receipt_no ."</td>
                                    <td class='text-center'>". $row->amount ."</td>
                                    <td class='text-center'>". $row->payment_type ."</td>
                                    <td class='text-center'><a href='$linkUrlFile' target='blank'>$row->file_name</a></td>
                            
                                </tr>";
                }

                $html .= "
                            </tbody>";
                return $html;
        }else if($sourcePayment=='MAYBANK'){
            $list  = $this->listPaymentReceiptMaybankByDate($paymentDate);
                $html = "   <thead>
                        <tr>
                            <th class='text-center'>No.</th>
                            <th class='text-center'>Date</th>
                            <th class='text-center'>Order ID</th>
                            <th class='text-center'>Razer ID</th>
                            <th class='text-center'>Channel</th>
                            <th class='text-center'>Amount</th>
                            <th class='text-center'>Bank Ref ID</th>
                            <th class='text-center'>Fee</th>
                            <th class='text-center'>File Name</th>
                        </tr>
                        </thead>
                        <tbody>";
                $count = 1;
                foreach ($list as $row) {
                    if($row->payment_type === 'CREDIT' && $row->credit_fee == 0){
                        $row->payment_type = 'DEBIT';
                    }
                    $html .= "
                                <tr>
                                    <td style='width:10px' class='text-center'> ". $count++ ." </td>
                                    <td class='text-center'>". $row->transaction_date ."</td>
                                    <td class='text-center'>". $row->order_id ."</td>
                                    <td class='text-center'>$row->razer_transaction_id</td>
                                    <td class='text-center'>". $row->payment_type ."</td>
                                    <td class='text-center'>". $row->amount ."</td>
                                    <td class='text-center'>". $row->reference_id ."</td>
                                    <td class='text-center'>". $row->credit_fee ."</td>
                                    <td class='text-center'>". $row->file_name ."</td>
                                </tr>";
                }

                $html .= "
                            </tbody>";
                return $html;
        }

        
    }

    public function showFailedSyncOrderId($paymentDate) {
        $listPaymentEp = $this->listPaymentIdEpByDate($paymentDate);
        $listPaymentIdArr = $listPaymentEp->pluck('payment_id')->toArray();

        $listPaymentReceiptRazer  = $this->listPaymentRazerNotInWithReceiptEp($paymentDate,$listPaymentIdArr);
        $html = "   <thead>
                <tr>
                    <th class='text-center'>No.</th>
                    <th class='text-center'>Billing Date</th>
                    <th class='text-center'>Order ID</th>
                    <th class='text-center'>Trans ID</th>
                    <th class='text-center'>Channel</th>
                    <th class='text-center'>Amount</th>
                    <th class='text-center'>Stat Code</th>
                    <th class='text-center'>Billing Name</th>
                </tr>
                </thead>
                <tbody>";
        $count = 1;
        foreach ($listPaymentReceiptRazer as $row) {
            $html .= "
                        <tr>
                            <td class='text-center'> ". $count++ ." </td>
                            <td class='text-center'>". $row->billing_date ."</td>
                            <td class='text-center'>". $row->order_id ."</td>
                            <td class='text-center'><a href='https://portal.merchant.razer.com/index.php?mod=transactions&opt=view&tid=$row->tran_id' target='blank'>$row->tran_id</a></td>
                            <td class='text-center'>". $row->channel ."</td>
                            <td class='text-center'>". $row->amount ."</td>
                            <td class='text-center'>". $row->stat_code ."</td>
                            <td class='text-center'>". $row->billing_name ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }

    public function showFailedSyncAr502($paymentDate) {
        $listPaymentReceiptAr502  = $this->listReceiptNoAr502ByDate($paymentDate);
        $listReceiptNoArr = $listPaymentReceiptAr502->pluck('receipt_no')->toArray();
        
        $listPaymentEp = $this->listPaymentEpNotInWithReceiptAr502($paymentDate,$listReceiptNoArr);
        
        $html = "   <thead>
                <tr>
                    <th class='text-center'>No.</th>
                    <th class='text-center'>Payment Date</th>
                    <th class='text-center'>Receipt No.</th>
                    <th class='text-center'>Order ID</th>
                    <th class='text-center'>Amount</th>
                    <th class='text-center'>Bill Type</th>
                </tr>
                </thead>
                <tbody>";
        $count = 1;
        foreach ($listPaymentEp as $row) {
            $html .= "
                        <tr>
                            <td class='text-center'> ". $count++ ." </td>
                            <td class='text-center'>". $row->payment_date ."</td>
                            <td class='text-center'>". $row->receipt_no ."</td>
                            <td class='text-center'>". $row->payment_id ."</td>
                            <td class='text-center'>". $row->payment_amt ."</td>
                            <td class='text-center'>". $row->bill_type ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }

    public function showFailedRazerWithMaybankSuccess() {
        $list  = $this->listPaymentMaybankButFailedInRazer();
        
        $html = "   <thead>
                <tr>
                    <th class='text-center'>No.</th>
                    <th class='text-center'>Maybank Date</th>
                    <th class='text-center'>Razer Date</th>
                    <th class='text-center'>Order ID</th>
                    <th class='text-center'>Razer ID</th>
                    <th class='text-center'>Ref Trans</th>
                    <th class='text-center'>Payment Type</th>
                    <th class='text-center'>Amount</th>
                    <th class='text-center'>Maybank File Name</th>
                    <th class='text-center'>Action</th>
                    <th class='text-center'>Receipt No</th>
                </tr>
                </thead>
                <tbody>";
        $count = 1;
        foreach ($list as $row) {
            $rcpObj  = DB::connection('oracle_nextgen_rpt')
                        ->table('py_payment')->where('payment_id',$row->order_id)
                        ->first();
            $receiptNo = null;
            $isAction = "Pending Fix  <i class='fa fa-flag'></i>";
            if( $rcpObj != null && strlen($rcpObj->receipt_no) > 5){
                $isAction = "Success Fix <i class='fa fa-check-circle'></i>";
                $receiptNo = $rcpObj->receipt_no;
            }
            $html .= "
                        <tr>
                            <td class='text-center'> ". $count++ ." </td>
                            <td class='text-center'>". $row->mbb_payment_date ."</td>
                            <td class='text-center'>". $row->razer_payment_date ."</td>
                            <td class='text-center'>". $row->order_id ."</td>
                            <td class='text-center'>". $row->razer_transaction_id ."</td>
                            <td class='text-center'>". $row->reference_id ."</td>
                            <td class='text-center'>". $row->payment_type ."</td>
                            <td class='text-center'>". $row->amount ."</td>
                            <td class='text-center'>". $row->file_name ."</td>
                            <td class='text-center'>". $isAction ."</td>
                            <td class='text-center'>". $receiptNo  ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }

    public function showWrongCardTypeRazerWithMaybank() {
        $list  = $this->listPaymentCardTypeWrongRazerMaybank();
        
        $html = "   <thead>
                <tr>
                    <th class='text-center'>No.</th>
                    <th class='text-center'>Issue</th>
                    <th class='text-center'>MBB Posting Date</th>
                    <th class='text-center'>Razer Bank Date</th>
                    <th class='text-center'>Order ID</th>
                    <th class='text-center'>Razer ID</th>
                    <th class='text-center'>Amount</th>
                    <th class='text-center'>MBB Fee</th>
                    <th class='text-center'>Razer Card Type</th>
                    <th class='text-center'>Razer Card Scheme</th>
                    <th class='text-center'>eP Card Type</th>
                    <th class='text-center'>Maybank File Name</th>
                    <th class='text-center'>Action</th>
                    <th class='text-center'>Receipt No</th>
                </tr>
                </thead>
                <tbody>";
        $count = 1;
        foreach ($list as $row) {
            $rcpObj  = DB::connection('oracle_nextgen_rpt')
                        ->table('py_payment')->where('payment_id',$row->order_id)
                        ->first();
            $receiptNo = null;
            
            if( $rcpObj != null && strlen($rcpObj->receipt_no) > 5){
                $epReportUrl = env('EP_REPORT_URL','http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd');
                $receiptNo = "<a target='_blank' href='$epReportUrl&report=sm_payment_receipt.jsp&pmntid=$row->order_id&destype=cache&desformat=pdf' ><i class='fa fa-download'></i> $rcpObj->receipt_no</a>";
            }

            $isAction = "<span class='text-danger'>Pending Fix </span> <i class='fa fa-flag'></i>";
            if($row->issue_name != 'MBB AS DEBIT BUT EP AS CREDIT' && strtolower($row->ep_payment_channel) != strtolower($row->card_type) ){
                $isAction = "Success Fix  <i class='fa fa-check-circle'></i>"; 
            }
            if($row->issue_name == 'MBB AS DEBIT BUT EP AS CREDIT' && strtolower($row->ep_payment_channel) == strtolower($row->card_type) ){
                $isAction = "Success Fix  <i class='fa fa-check-circle'></i>"; 
            }
            $html .= "
                        <tr>
                            <td class='text-center'> ". $count++ ." </td>
                            <td class='text-center'>". $row->issue_name ."</td>
                            <td class='text-center'>". $row->transaction_date ."</td>
                            <td class='text-center'>". $row->bank_date_time ."</td>
                            <td class='text-center'>". $row->order_id ."</td>
                            <td class='text-center'>". $row->tran_id ."</td>
                            <td class='text-center'>". $row->amount ."</td>
                            <td class='text-center'>". $row->credit_fee ."</td>
                            <td class='text-center'>". $row->card_type ."</td>
                            <td class='text-center'>". $row->card_scheme ."</td>
                            <td class='text-center'>". $row->ep_payment_channel ."</td>
                            <td class='text-center'>". $row->file_name ."</td>
                            <td class='text-center'>". $isAction ."</td>
                            <td class='text-center'>". $receiptNo  ."</td>
                        </tr>";
        }

        $html .= "
                    </tbody>";
        return $html;
    }


    public function summaryTransactionDaily($paymentDate) {
        $listPaymentEp = $this->summaryReceiptEpByDate($paymentDate);
        $paymentChannelFpx = $listPaymentEp->where('channel_name','fpx')->first();
        $paymentChannelCredit = $listPaymentEp->where('channel_name','credit')->first();
        $paymentChannelDebit = $listPaymentEp->where('channel_name','debit')->first();
           
        $fpxTotalPayment = $paymentChannelFpx  ? $paymentChannelFpx->total_payment : 0;
        $fpxAmountPayment = $paymentChannelFpx  ? $paymentChannelFpx->amount_payment : 0;
        $creditTotalPayment = $paymentChannelCredit  ? $paymentChannelCredit->total_payment : 0;
        $creditAmountPayment = $paymentChannelCredit  ? $paymentChannelCredit->amount_payment : 0;
        $debitTotalPayment = $paymentChannelDebit  ? $paymentChannelDebit->total_payment : 0;
        $debitAmountPayment = $paymentChannelDebit ? $paymentChannelDebit->amount_payment : 0;

        $html = "<thead>
                <tr>
                    <th style='width:100px;'rowspan='2' class='text-center'>Date</th>
                    <th colspan='2' class='text-center'>Credit Card</th>
                    <th colspan='2' class='text-center'>Debit Card</th>
                    <th colspan='2' class='text-center'>FPX</th>
                    <th rowspan='2' class='text-center'>Total Amount</th>
                </tr>
                <tr>
                    <th class='text-center'>No. Transaction</th>
                    <th class='text-center'>Amount (RM)</th>
                    <th class='text-center'>No. Transaction</th>
                    <th class='text-center'>Amount (RM)</th>
                    <th class='text-center'>No. Transaction</th>
                    <th class='text-center'>Amount (RM)</th>     
                </tr>
                </thead>
                <tbody> 
                <tr>
                    <td class='text-center'> ". $paymentDate." </td>
                    <td class='text-center'>". $creditTotalPayment ."</td>
                    <td class='text-center'>". $creditAmountPayment."</td>
                    <td class='text-center'>". $debitTotalPayment ."</td>
                    <td class='text-center'>". $debitAmountPayment."</td>
                    <td class='text-center'>". $fpxTotalPayment ."</td>
                    <td class='text-center'>". $fpxAmountPayment ."</td>
                    <td class='text-center'>". ($fpxAmountPayment+$creditAmountPayment+$debitAmountPayment) ."</td>
                </tr>
                </tbody>";
        return $html;
    }

    public function downloadAllReceiptPaymentSR($paymentDate) {
        $dateFolder = str_replace("-","",$paymentDate); 
        $list = $this->listReceiptEpByDate($paymentDate);
        
        $path = storage_path('app/receipt-ep');
        $fullFolderPath = $path.'/'.$dateFolder;
        
        //Storage::makeDirectory($fullFolderPath);
        if (!file_exists($fullFolderPath)) {
            File::makeDirectory($fullFolderPath);
        }
        $epReportUrl = env('EP_REPORT_URL','http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd');
        foreach($list as $rec){
    
            $rctNo = $rec->receipt_no;
            $fileNameReceipt = $rctNo.'_receipt.pdf';
            $filePath = $fullFolderPath.'/'.$fileNameReceipt;
            if (!file_exists($filePath)) {
                $urlSync = $epReportUrl."&report=sm_payment_receipt.jsp&pmntid=$rec->payment_id&destype=cache&desformat=pdf";
                $response = Guzzle::get($urlSync, ['save_to' => $filePath]);
                $resultResp = $response->getStatusCode();
                Log::info($urlSync .' :-- status code'.$resultResp);
            }else{
                Log::info(' Existed : '.$filePath);
            }
        }
        
        $files = glob($fullFolderPath); //Get all folder n files under Path:FolderName
        $folderZipPath = storage_path('app/receipt-ep').'/zip';
        $zipPathName = $folderZipPath . '/' . $dateFolder . '.zip';
        Log::info('ZipPath: '.$zipPathName );
        if (file_exists($zipPathName)) {
            File::delete($zipPathName);
        }
        \Zipper::make($zipPathName)->add($files)->close();

        return response()->download($zipPathName);
    }
}
