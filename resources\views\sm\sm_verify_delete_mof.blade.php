@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('find/sm/verifydel')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <!--<input type="text" id="mof_no" name="mof_no" value="{{$carian}}" class="form-control" onfocus="this.select();"-->
        <input type="text" id="supp_id" name="supp_id" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Sila Masukkan Supplier ID">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <h1>
            <small>Semakan akan mengambil sedikit masa. Harap Maklum.</small>
        </h1>
    </div>
</div>

@if($infosm == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Rekod Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif

@if($infosm != null)
<div class="block block-alt-noborder full">
    <!--AKMAL REGION DETAILS INFO-->
    <div class="row">
        <div class="block">
            <div class="block-title epss-title-s1">
                <h6><i class="fa fa-users"></i> SEMAKAN TUGASAN BELUM SELESAI (PERMOHONAN DELETE AKAUN MOF)</h6>
            </div> 
            <div class="row" >
                <div class="col-md-12">
                    @if($infosm!=null)
                    <h6> <strong> {{ $infosm->company_name }} | 
                            @if($infosm->mofnoo !=null)
                                {{ $infosm->mofnoo }}
                            @endif
                        </strong></h6>                    
                    @endif
                    <br>
                    <table class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr style="background-color:#6ad2eb">      
                                <td class="text-center"><strong>BIL</strong></td>
                                <td class="text-left"><strong>TYPE OF TRANSACTION</strong></td>
                                <td class="text-center"><strong>TOTAL PENDING TASK</strong></td>
                                <td class="text-center"><strong>STATUS</strong></td>
                                <td class="text-center"><strong>DETAILS</strong></td>
                                <!--<td class="text-center"><strong>SCRIPT</strong></td>-->
                            </tr>
                            <!--1.0 Pending Transaction kemaskini on 05042021-->
                            <tr>
                                <td class="text-center">01</td>
                                <td class="text-left"><strong>Pending Transaction</strong></td>
                                <td class="text-center"> {{ $cr001->resultc }} </td>    
                                @if($cr001->resultc != null && $cr001->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr001->resultc != null && $cr001->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr001->resultc != null && $cr001->resultc != '0')
                                    <a href='#modal-list-trans-01'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='Active Cancel Application'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="Active Cancel Application"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-01" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Active Cancel Application</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">                  
                                                        <table id="smdel01-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Supplier Id</th>
                                                                    <th class="text-center">Appl Id</th>
                                                                    <th class="text-center">Appl No</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr001 != null)
                                                                @foreach($vr001 as $data1)
                                                                <tr>
                                                                    <td class="text-center">{{ $data1->supplier_id }}</td>
                                                                    <td class="text-center">{{ $data1->appl_id }}</td>
                                                                    <td class="text-center">{{ $data1->appl_no }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 

                                </td> 
    <!--                            <td class="text-center"><strong>Get Query  </strong><i class="gi gi-circle_exclamation_mark  text-info" " title="Semak!"></i>  -->
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_1" >View Query</button>
                                </td>-->
                            </tr>
                            <tr id="row_1" class="collapse">
                                <td colspan="4"></td>
                                <td colspan="2">
                                    <code class="language-markup"  > 
                                        SELECT appl_id 
                                        from sm_appl where is_active_appl = 1 and supplier_id = '{{ $infosm->supplier_id }}'
                                    </code>
                                </td>
                            </tr>
                            <!--2.0 QT TRANSACTION-->
                            <tr style="background-color:#F3F3F3">  
                                <td class="text-center">02</td>
                                <td class="text-left"><strong>QT transactions</strong></td>
                                <td class="text-center">{{ $cr002->resultc }} </td> 
                                @if($cr002->resultc != null && $cr002->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr002->resultc != null && $cr002->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr002->resultc != null && $cr002->resultc != '0')
                                    <a href='#modal-list-trans-02'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='QT transaction'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="QT transaction"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-02" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">QT transaction</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel02-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">QT Id</th>
                                                                    <th class="text-center">QT No</th>
                                                                    <th class="text-center">Expiry Date</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr002 != null)
                                                                @foreach($vr002 as $data2)
                                                                <tr>
                                                                    <td class="text-center">{{ $data2->qt_id }}</td>
                                                                    <td class="text-center">{{ $data2->qt_no }}</td>
                                                                    <td class="text-center">{{ $data2->proposal_validity_end_date }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td> 
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_2" >View Query</button>
                                </td>-->
                            </tr>
                            <!--QT (LA) transactions-->
                            <tr>
                                <td class="text-center">03</td>
                                <td class="text-left"><strong>QT (LA) transactions</strong></td>
                                <td class="text-center">{{ $cr009->resultc }} </td> 
                                @if($cr009->resultc != null && $cr009->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr009->resultc != null && $cr009->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr009->resultc != null && $cr009->resultc != '0')
                                    <a href='#modal-list-trans-07'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='QT LA transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="QT LA transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-07" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">QT LA transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel07-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Supplier Id</th>
                                                                    <th class="text-center">Supplier Name</th>
                                                                    <th class="text-center">MOF No</th>
                                                                    <th class="text-center">LOA No</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr009 != null)
                                                                @foreach($vr009 as $data3)
                                                                <tr>
                                                                    <td class="text-center">{{ $data3->supplier_id }}</td>
                                                                    <td class="text-center">{{ $data3->supplier_name }}</td>
                                                                    <td class="text-center">{{ $data3->mof_no }}</td>
                                                                    <td class="text-center">{{ $data3->loa_no }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td> 
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_7" >View Query</button>
                                </td>-->
                            </tr>
                            <!--4.0 CT transactions-->
                            <tr style="background-color:#F3F3F3">  
                                <td class="text-center">04</td>
                                <td class="text-left"><strong>CT transactions</strong></td>
                                <td class="text-center">{{ $cr003->resultc }} </td> 
                                @if($cr003->resultc != null && $cr003->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr003->resultc != null && $cr003->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr003->resultc != null && $cr003->resultc != '0')
                                    <a href='#modal-list-trans-03'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal' 
                                       data-url='/find/sm/verifydel/'
                                       data-title='CT transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="CT transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-03" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">CT transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel03-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Contract No</th>
                                                                    <th class="text-center">Contract Name</th>
                                                                    <th class="text-center">PTJ</th>
                                                                    <th class="text-center">Expiry Date</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr003 != null)
                                                                @foreach($vr003 as $data4)
                                                                <tr>
                                                                    <td class="text-center">{{ $data4->contract_no }}</td>
                                                                    <td class="text-center">{{ $data4->contract_name }}</td>
                                                                    <td class="text-center">{{ $data4->ptj_code }} | {{ $data4->ptj_name }}</td>
                                                                    <td class="text-center">{{ $data4->contract_expiry_date }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td> 
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_3" >View Query</button>
                                </td>-->
                            </tr>
                            <!--5.0 DP (SQ) transactions-->
                            <tr>
                                <td class="text-center">05</td>
                                <td class="text-left"><strong>DP (SQ) transactions</strong></td>
                                <td class="text-center">{{ $cr006->resultc }} </td> 
                                @if($cr006->resultc != null && $cr006->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr006->resultc != null && $cr006->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr006->resultc != null && $cr006->resultc != '0')
                                    <a href='#modal-list-trans-04'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='DP SQ transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="DP SQ transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-04" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">DP SQ transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel04-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Quote No</th>
                                                                    <th class="text-center">Supplier ID</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr006 != null)
                                                                @foreach($vr006 as $data5)
                                                                <tr>
                                                                    <td class="text-center">{{ $data5->quote_no }}</td>
                                                                    <td class="text-center">{{ $data5->supplier_id }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td> 

<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_4" >View Query</button>
                                </td>-->
                            </tr>
                            <tr style="background-color:#F3F3F3">  
                                <td class="text-center">06</td>
                                <td class="text-left"><strong>SC (RN) transactions</strong></td>
                                <td class="text-center">{{ $cr007->resultc }} </td> 
                                @if($cr007->resultc != null && $cr007->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr007->resultc != null && $cr007->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr007->resultc != null && $cr007->resultc != '0')
                                    <a href='#modal-list-trans-05'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='SC RN transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="SC RN transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-05" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">SC RN transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel05-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Request Note No</th>
                                                                    <th class="text-center">Org Profile ID</th>
                                                                    <th class="text-center">Title</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr007 != null)
                                                                @foreach($vr007 as $data6)
                                                                <tr>
                                                                    <td class="text-center">{{ $data6->request_note_no }}</td>
                                                                    <td class="text-center">{{ $data6->org_profile_id }}</td>
                                                                    <td class="text-center">{{ $data6->title }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td>  
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_5" >View Query</button>
                                </td>-->
                            </tr>
                            <!--7.0 PI transactions-->
                            <tr>
                                <td class="text-center">07</td>
                                <td class="text-left"><strong>PI transactions</strong></td>
                                <td class="text-center">{{ $cr008->resultc }} </td> 
                                @if($cr008->resultc != null && $cr008->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr008->resultc != null && $cr008->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr008->resultc != null && $cr008->resultc != '0')
                                    <a href='#modal-list-trans-06'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='PI transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="PI transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-06" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">PI transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel06-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Purchase Inquiry ID</th>
                                                                    <th class="text-center">Purchase Inquiry No</th>
                                                                    <th class="text-center">Title</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr008 != null)
                                                                @foreach($vr008 as $data7)
                                                                <tr>
                                                                    <td class="text-center">{{ $data7->purchase_inquiry_id }}</td>
                                                                    <td class="text-center">{{ $data7->purchase_inquiry_no }}</td>
                                                                    <td class="text-center">{{ $data7->title }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td>
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_6" >View Query</button>
                                </td>-->
                            </tr>
                            <!--8.0 FL transactions (CR)-->
                            <tr style="background-color:#F3F3F3">  
                                <td class="text-center">08</td>
                                <td class="text-left"><strong>FL transactions (CR)</strong></td>
                                <td class="text-center">{{ $cr004->resultc }} </td> 
                                @if($cr004->resultc != null && $cr004->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr004->resultc != null && $cr004->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr004->resultc != null && $cr004->resultc != '0' && $cr004->resultc < '200')
                                    <a href='#modal-list-trans-08'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='FL CR transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="FL CR transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-08" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">FL CR transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel08-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Fulfilment Req ID</th>
                                                                    <th class="text-center">Doc No</th>
                                                                    <th class="text-center">Office Name</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr004 != null)
                                                                @foreach($vr004 as $data8)
                                                                <tr>
                                                                    <td class="text-center">{{ $data8->fulfilment_req_id }}</td>
                                                                    <td class="text-center">{{ $data8->doc_no }}</td>
                                                                    <td class="text-center">{{ $data8->ag_office_name }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td> 
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_8" >View Query</button>
                                </td>-->
                            </tr>
                            <!--9.0 FL transactions (PR)-->
                            <tr>
                                <td class="text-center">09</td>
                                <td class="text-left"><strong>FL transactions (PR)</strong></td>
                                <td class="text-center">{{ $cr005->resultc }} </td> 
                                @if($cr005->resultc != null && $cr005->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr005->resultc != null && $cr005->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr005->resultc != null && $cr005->resultc != '0' && $cr005->resultc < '200')
                                    <a href='#modal-list-trans-10'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='FL PR transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="FL PR transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-10" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">FL PR transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        <table id="smdel10-datatable" class="table table-vcenter table-condensed table-bordered">
                                                            <thead>
                                                                <tr>
                                                                    <th class="text-center">Supplier ID</th>
                                                                    <th class="text-center">Doc No</th>
                                                                    <th class="text-center">Office Name</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                @if($vr005 != null)
                                                                @foreach($vr005 as $data9)
                                                                <tr>
                                                                    <td class="text-center">{{ $data9->supplier_id }}</td>
                                                                    <td class="text-center">{{ $data9->doc_no }}</td>
                                                                    <td class="text-center">{{ $data9->ag_office_name }}</td>
                                                                </tr>
                                                                @endforeach
                                                                @endif
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td>  
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_10" >View Query</button>
                                </td>-->
                            </tr>
                            <!--10. FL transactions (PO)-->
                            <tr style="background-color:#F3F3F3">  
                                <td class="text-center">10</td>
                                <td class="text-left"><strong>FL transactions (PO)</strong></td>
                                <td class="text-center">{{ $cr0052->resultc }} </td> 
                                @if($cr0052->resultc != null && $cr0052->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr0052->resultc != null && $cr0052->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr0052->resultc != null && $cr0052->resultc != '0' && $cr0052->resultc < '200')
                                    <a href='#modal-list-trans-11'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='FL PO transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="FL PO transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-11" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">FL PO transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        @if($vr0052 != null)
                                                        @foreach($vr0052 as $data10)
                                                        {{ $loop->first ? '' : '| ' }}
                                                        <span class="nice">{{ $data10->doc_no }}</span>
                                                        @endforeach
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td>  
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_11" >View Query</button>
                                </td>-->
                            </tr>
                            <!--11. FL transactions (CO)-->
                            <tr>
                                <td class="text-center">11</td>
                                <td class="text-left"><strong>FL transactions (CO)</strong></td>
                                <td class="text-center">{{ $cr0042->resultc }} </td> 
                                @if($cr0042->resultc != null && $cr0042->resultc == '0')
                                <td class="text-center">OK <i class="gi gi-thumbs_up text-success" style="font-size: 10pt;" title=""></td> 
                                @elseif($cr0042->resultc != null && $cr0042->resultc != '0')
                                <td class="text-center">Pending Task <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title=""></td> 
                                @endif
                                <td class="text-center">View Details
                                    @if($cr0042->resultc != null && $cr0042->resultc != '0' && $cr0042->resultc < '200')
                                    <a href='#modal-list-trans-09'
                                       class='modal-list-data-action ' 
                                       data-toggle='modal'  
                                       data-url='/find/sm/verifydel/'
                                       data-title='FL CO transactions'>
                                        <strong style="font-weight: bolder;">
                                            <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="FL CO transactions"></i>
                                        </strong>
                                    </a> 
                                    @endif
                                    <div id="modal-list-trans-09" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                                        <div class="modal-dialog modal-lg">
                                            <div class="modal-content">
                                                <div class="modal-header text-center">
                                                    <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">FL CO transactions</span></h2>
                                                </div>
                                                <div class="modal-body">
                                                    <div class="row">
                                                        @if($vr0042 != null)
                                                        @foreach($vr0042 as $data11)
                                                        {{ $loop->first ? '' : '| ' }}
                                                        <span class="nice">{{ $data11->doc_no }}</span>
                                                        @endforeach
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </td>  
<!--                                <td class="text-center">
                                    <button class="btn btn-info btn-xs"data-toggle="collapse" data-target="#row_9" >View Query</button>
                                </td>-->
                            </tr>
                        </thead>
                    </table>
                </div>  
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>

<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });
    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();
            $('#modal-list-data-header').text($(this).attr('data-title'));
            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();
            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();
                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });
                    $('.spinner-loading').hide();
                }
            });
        });
    });
    });

</script>

@endsection