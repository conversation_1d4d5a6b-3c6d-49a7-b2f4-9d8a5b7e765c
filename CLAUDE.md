# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

EP-SUPPORT is an **eP Support System** built with Laravel 5.4 and PHP >=5.6.4. This system allows technical support teams to access and filter eP (electronic Procurement) system data for support operations. It's a comprehensive government procurement support platform with multiple integrated modules.

## Technology Stack

- **Framework**: Laravel 5.4
- **PHP Version**: >=5.6.4
- **Database**: Oracle (using yajra/laravel-oci8)
- **Frontend**: Laravel Mix with Vue.js 2.1, Bootstrap 3, jQuery
- **Key Dependencies**:
  - Oracle database connectivity (yajra/laravel-oci8)
  - PDF generation (barryvdh/laravel-dompdf)
  - Excel handling (maatwebsite/excel)
  - Remote SSH operations (laravelcollective/remote)
  - UUID generation (webpatser/laravel-uuid)

## Essential Commands

### Development
```bash
# Install dependencies
composer install
npm install

# Build assets for development
npm run dev
npm run watch      # Watch for changes

# Build for production
npm run prod
```

### Laravel Artisan Commands
```bash
# Clear application cache
php artisan cache:clear
php artisan config:clear
php artisan view:clear

# Optimize application
php artisan optimize

# Run scheduled tasks manually
php artisan schedule:run
```

### ⚠️ PRODUCTION ENVIRONMENT RESTRICTIONS
```bash
# NEVER run these commands in production:
# php artisan migrate          # Can cause data loss or corruption
# php artisan key:generate     # Will break existing encrypted data
# php artisan db:seed          # Can overwrite production data
# php artisan migrate:reset    # Will delete all data
# php artisan migrate:fresh    # Will delete all data
```

### Testing
```bash
# Run PHPUnit tests
vendor/bin/phpunit

# Run specific test
vendor/bin/phpunit tests/Feature/ExampleTest.php
```

## Architecture Overview

### Core Module Structure
This is a multi-module support system with the following key areas:

**Business Process Management (BPM)**
- BPM API integration and monitoring
- Workflow instance management
- Task management and error handling
- Located in: `app/Http/Controllers/BPMController.php`, `app/Services/BPMAPIService.php`

**eP Integration Modules**
- **Sourcing/Quotation (QT)**: Tender and quotation management
- **Supplier Management (SM)**: Supplier registration and verification
- **Fulfillment (FL)**: Order fulfillment and delivery tracking  
- **Contract Management (CT)**: Contract agreements and amendments
- **PHIS**: Pharmaceutical inventory system integration
- **GFMAS/IGFMAS**: Government financial management integration

**Support Systems**
- **CRM**: Customer relationship management for support cases
- **OSB**: Oracle Service Bus integration and monitoring
- **SPKI**: Digital certificate and signing services
- **Log Trace**: System logging and monitoring
- **IT Support**: Infrastructure monitoring and checklists

### Service Layer Architecture
The application uses a comprehensive service layer pattern:

- **Base Services**: Core business logic in `app/Services/`
- **Trait Services**: Reusable service components in `app/Services/Traits/`
- **Specialized Services**: Module-specific services (EpNotify, Helpdesk, LogTrace)

### Scheduled Tasks System
Extensive automated task scheduling via `app/Console/Kernel.php`:
- **Integration monitoring**: File processing, error handling, service retries
- **Data synchronization**: Cross-system data updates
- **Alert systems**: WhatsApp notifications, email reports
- **Maintenance tasks**: Database cleanup, cache management

### Database Architecture
- **Oracle Database**: Primary data store using Laravel OCI8
- **Multiple Schemas**: Separate schemas for different modules (eP, SM, QT, etc.)
- **Integration Tables**: Tables for cross-system data exchange
- **Audit/Logging**: Comprehensive logging tables for all operations

### Key Controllers Pattern
Controllers are organized by functional area:
- **EpController**: Main eP system operations
- **CRMController**: Support case management
- **OSBController**: Service bus monitoring
- **BPMController**: Business process management
- **Module-specific controllers**: QT, SM, FL, CT, PHIS controllers

### Authentication & Authorization
- Laravel's built-in authentication
- Role-based access control via middleware
- Integration with external SSO systems
- User activity logging and monitoring

### File Processing & Integration
- **Batch file processing**: Automated file handling for integration
- **OSB integration**: Service-oriented architecture for external systems
- **API integrations**: RESTful APIs for various government systems
- **File monitoring**: Automated monitoring of file processing pipelines

## Development Guidelines

### ⚠️ CRITICAL: Production Environment Safety
**This is a PRODUCTION environment. Exercise extreme caution:**
- NEVER run database migrations (`php artisan migrate`)
- NEVER modify database schema without proper approval
- NEVER run seeding or data manipulation commands
- Always backup before making any changes
- Test all changes in development environment first

### Database Connections
The system uses Oracle database connections. Ensure proper Oracle client setup and connection configuration in `config/database.php`.

### Service Integration
When working with external integrations, check existing service traits in `app/Services/Traits/` before creating new implementations.

### Scheduled Commands
All background processing uses Laravel's task scheduler. New automated tasks should be added as Artisan commands in `app/Console/Commands/` and registered in `app/Console/Kernel.php`.

### Error Handling
The system has comprehensive error handling and logging. Check `app/Services/Traits/NotificationService.php` for alert mechanisms.

### Testing Environment
Use the existing PHPUnit configuration. The system includes both Feature and Unit test directories.