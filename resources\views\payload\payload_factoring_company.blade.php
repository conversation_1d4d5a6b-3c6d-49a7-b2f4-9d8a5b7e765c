@extends('layouts.guest-dash')

@section('header')
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }

    .table tbody > tr > td {
        font-size: 10px;
    }
</style>
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Factoring Company<br>
            <small>Masukkan <span class="text-info">CT No.</span> pada carian berikut</small>
        </h1>
    </div>
    <div class="block">
        <form id="form-search" action="{{url("/find/contract/factoring")}}/" method="get" class="form-horizontal" onsubmit="return true;">
            <div class="form-group">
                <label class="col-md-3 control-label" for="doc_no">CT No. <span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input id="doc_no" name="doc_no" class="form-control" placeholder="CT No.." type="text" required
                           value="{{ old('doc_no') }}">
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
</div>

@if($factoringData != null && $factoringUser != null) 
<div class="block">
    <h4>Payload</h4>
    <?php
            $remove = 'false';
            $checkRemove = $factoringData[0]->remove_file_name;
            if($checkRemove !== null){
                $remove = 'true';
            }
                
            $xml = '<CT_Factoring_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/CT_Factoring_Data" xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/CT_User_Data">
    <factoringId>'.$factoringData[0]->factoring_id.'</factoringId>
    <contractId>'.$factoringData[0]->contract_id.'</contractId>
    <title>'.$factoringData[0]->contract_name.'</title>
    <contractAdmin ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <docId>'.$factoringData[0]->factoring_id.'</docId>
    <supplier>
        <ns2:userId>'.$factoringData[0]->supplier_userid.'</ns2:userId>
        <ns2:userName>'.$factoringData[0]->supplier_loginid.'</ns2:userName>
        <ns2:calendarName>PUTRAJAYA</ns2:calendarName>
        <ns2:userDisplayName>'.$factoringData[0]->supplier_username.'</ns2:userDisplayName>
    </supplier>
    <approver ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <docNo>'.$factoringData[0]->loa_no.'</docNo>
    <docType>RF</docType>
    <statusId ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <loaNo>'.$factoringData[0]->loa_no.'</loaNo>
    <factoringName>'.$factoringData[0]->financial_org_name.'</factoringName>
    <fcUILink>https://www.eperolehan.gov.my/web/epapp/review-factoring-request?id='.$factoringData[0]->factoring_id.'</fcUILink>
    <fcRemove>false</fcRemove>
    <contractAdminList>
        <ns2:userId>'.$factoringData[0]->ptj_userid.'</ns2:userId>
        <ns2:userName>'.$factoringData[0]->ptj_loginid.'</ns2:userName>
        <ns2:calendarName>PUTRAJAYA</ns2:calendarName>
        <ns2:userDisplayName>'.$factoringData[0]->ptj_username.'</ns2:userDisplayName>
    </contractAdminList>
    <contractNo>'.$factoringData[0]->contract_no.'</contractNo>
    <kpi_value ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <ptjName>'.$orgDetails[0]->ptjname.'</ptjName>
    <ministry>'.$orgDetails[0]->minname.'</ministry>';
    
    foreach($factoringUser as $data){
        $xml = $xml .'
    <fcUserList>
        <ns2:userId>'.$data->user_id.'</ns2:userId>
        <ns2:userName>'.$data->identification_no.'</ns2:userName>
        <ns2:calendarName>CALENDAR_PUTRAJAYA</ns2:calendarName>
        <ns2:userDisplayName>'.$data->user_name.'</ns2:userDisplayName>
    </fcUserList>'; } 
        
    $fullXml = $xml .
        '
    <approverName ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <loaPhysicalNo>'.$factoringData[0]->loa_physical_no.'</loaPhysicalNo>
    <contractPhysicalNo>'.$factoringData[0]->contract_physical_no.'</contractPhysicalNo>
    <fcTemplateLink ns3:nil="true" xmlns:ns3="http://www.w3.org/2001/XMLSchema-instance"/>
    <fcRequestType>A</fcRequestType>
</CT_Factoring_Data>';
 
            ?>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($fullXml) }}</code> 
            </pre>
</div>
@endif 

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>
<script>
    $('#page-container').removeAttr('class');
</script>
@endsection



