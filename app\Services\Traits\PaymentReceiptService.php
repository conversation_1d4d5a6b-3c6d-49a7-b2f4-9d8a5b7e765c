<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait PaymentReceiptService {

    protected function listPaymentReceiptEpByLastNoOfDay($noOfDay) { 
        $listPaymentReceiptEp = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT to_char(pp.payment_date, 'YYYY-MM-DD') AS payment_date , count(distinct pp.payment_id) AS total
                        FROM  py_payment pp, py_bill pb 
                        WHERE
                        pp.BILL_ID  = pb.bill_id
                        AND pp.receipt_no IS NOT NULL
                        AND trunc(pp.payment_date) > trunc(sysdate-$noOfDay)
                        AND pb.BILL_TYPE  IN ('P','R')
                        GROUP BY to_char(pp.payment_date, 'YYYY-MM-DD')
                        ORDER BY 1 DESC");
        return $listPaymentReceiptEp;
    }

    protected function listPaymentReceiptRazerByLastNoOfDay($noOfDay) { 
        $listPaymentReceiptRazer  = collect(DB::connection('mysql_ep_support')
        ->select("SELECT DATE(billing_date) AS payment_date, COUNT(*) AS total 
                    FROM ep_supplier_payment_razer 
                    WHERE DATE(billing_date) > DATE_SUB(DATE(NOW()), INTERVAL $noOfDay DAY)
                    AND stat_code = '00'
                    GROUP BY DATE(billing_date) 
                    ORDER BY 1 DESC ") );
        return $listPaymentReceiptRazer;
    }

    protected function listPaymentReceiptAr502ByLastNoOfDay($noOfDay) { 
        $listPaymentReceiptAr502 = collect(DB::connection('mysql_ep_support')
                        ->select("SELECT DATE(payment_date) AS payment_date, COUNT(DISTINCT receipt_no) AS total 
                        FROM ep_supplier_ar502
                        WHERE DATE(payment_date) > DATE_SUB(DATE(NOW()), INTERVAL $noOfDay DAY)
                        GROUP BY DATE(payment_date) 
                        ORDER BY 1 DESC ") );
        return $listPaymentReceiptAr502;
    }

    protected function listPaymentReceiptMaybankByLastNoOfDay($noOfDay) { 
        $listPaymentReceiptMaybank  = collect(DB::connection('mysql_ep_support')
        ->select("SELECT DATE(transaction_date) AS payment_date, COUNT(DISTINCT razer_transaction_id) AS total 
                FROM ep_supplier_payment_maybank
                WHERE DATE(transaction_date) > DATE_SUB(DATE(NOW()), INTERVAL $noOfDay DAY) 
                AND `is_ep_transaction` = 1 
                GROUP BY DATE(transaction_date) 
                ORDER BY 1 DESC ") );
        return $listPaymentReceiptMaybank;
    }

    protected function listPaymentReceiptRazerByDate($paymentDate) { 
        $list  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_payment_razer')
                ->whereDate('billing_date',$paymentDate)
                ->where('stat_code','00')
                ->get();
        return $list;
    }

    protected function listPaymentReceiptEpByDate($paymentDate) { 
        $list = DB::connection('oracle_nextgen_rpt')
        ->select("SELECT DISTINCT 
            pp.PAYMENT_ID ,pp.PAYMENT_DATE ,pp.PAYMENT_AMT ,pp.created_date,pb.BILL_TYPE ,pb.BILL_NO ,pp.RECEIPT_NO,
            ppo.CHANNEL_NAME , ppr.transaction_id ,ppr.PAYMENT_STATUS 
            FROM  py_payment pp, py_payment_order ppo,py_payment_response ppr, py_bill pb 
            WHERE receipt_no IS NOT NULL
            AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
            AND pp.BILL_ID  = pb.bill_id
            AND ppo.payment_order_id = ppr.payment_order_id
            AND pb.BILL_TYPE  IN ('P','R')
            AND ppr.payment_status = '00' 
            AND pp.STATUS_ID = '20405'
            AND trunc(pp.payment_date) = to_date(?,'YYYY-MM-DD')",array($paymentDate));
        return $list;
    }

    protected function listPaymentReceiptAr502ByDate($paymentDate) { 
        $list  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_ar502')
                ->whereDate('payment_date',$paymentDate)
                ->get();
        return $list;
    }

    protected function listPaymentReceiptMaybankByDate($paymentDate) { 
        $list  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_payment_maybank')
                ->whereDate('transaction_date',$paymentDate)
                ->where('is_ep_transaction',1)
                ->get();
        return $list;
    }

    protected function listPaymentIdEpByDate($paymentDate) { 
        $listPaymentEp = collect(DB::connection('oracle_nextgen_rpt')
                ->select("SELECT DISTINCT pp.payment_id FROM  py_payment pp, py_bill pb 
                WHERE pp.BILL_ID  = pb.bill_id
                    AND pp.receipt_no IS NOT NULL
                    AND trunc(pp.payment_date) = to_date(?,'YYYY-MM-DD')
                    AND pb.BILL_TYPE  IN ('P','R')",array($paymentDate)));
        return $listPaymentEp;
    }

    protected function listPaymentRazerNotInWithReceiptEp($paymentDate,$listPaymentIdArr) { 
        $listPaymentReceiptRazer  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_payment_razer')
                ->whereDate('billing_date',$paymentDate)
                ->whereNotIn('order_id',$listPaymentIdArr)
                ->where('stat_code','00')
                ->get();
        return $listPaymentReceiptRazer;
    }

    protected function listReceiptNoAr502ByDate($paymentDate) { 
        $listPaymentReceiptAr502  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_ar502')
                ->whereDate('payment_date',$paymentDate)
                ->distinct()
                ->select('receipt_no')
                ->get();
        return $listPaymentReceiptAr502;
    }

    protected function listReceiptNoAr502ByFileName($fileName) { 
        $listPaymentReceiptAr502  = DB::connection('mysql_ep_support')
                ->table('ep_supplier_ar502')
                ->where('file_name',$fileName)
                ->distinct()
                ->select('receipt_no')
                ->get();
        return $listPaymentReceiptAr502;
    }

    protected function listPaymentEpNotInWithReceiptAr502($paymentDate,$listReceiptNoArr) { 
        $listPaymentEp = DB::connection('oracle_nextgen_rpt')
                ->table('py_payment')
                ->join('py_bill', 'py_payment.bill_id', '=', 'py_bill.bill_id')
                ->whereIn('py_bill.bill_type',['P','R'])
                ->whereDate('py_payment.payment_date',$paymentDate)
                ->whereNotIn('receipt_no',$listReceiptNoArr)
                ->whereNotNull('receipt_no')
                ->get();
        return $listPaymentEp;
    }

    protected function listPaymentMaybankButFailedInRazer() { 
        $listPaymentReceipMaybank = collect(DB::connection('mysql_ep_support')
                        ->select("SELECT m.`transaction_date` AS mbb_payment_date,r.`billing_date` AS razer_payment_date,
                        m.`order_id`, m.`razer_transaction_id`,m.file_name,m.reference_id,
                        m.`payment_type`,m.`amount` FROM ep_supplier_payment_maybank m , ep_supplier_payment_razer r 
                        WHERE m.razer_transaction_id = r.`tran_id` AND m.`order_id` = r.`order_id` 
                        AND r.`stat_code` <> '00' 
                        ORDER BY 1 DESC ") );
        return $listPaymentReceipMaybank;
    }

    protected function listPaymentCardTypeWrongRazerMaybank() { 
        $listPaymentReceipMaybank = collect(DB::connection('mysql_ep_support')
                        ->select("SELECT 'MBB AS CREDIT BUT RAZER AS DEBIT' AS issue_name, m.file_name,
                        m.transaction_date,m.amount,m.order_id,m.razer_transaction_id,m.credit_authcode,m.credit_fee,
                        r.billing_date,r.bank_date_time,r.tran_id,r.bank_transaction_id,r.channel,
                        r.card_type,r.card_scheme ,e.payment_channel AS ep_payment_channel
                        FROM ep_supplier_payment_maybank m , ep_supplier_payment_razer r , ep_supplier_payment_ep e
                        WHERE 
                        m.razer_transaction_id = r.tran_id 
                        AND e.payment_id = r.order_id
                        -- AND m.credit_fee > 0  
                        AND IF(m.credit_fee > 0, 'CREDIT',IF (m.card_type IN ('FOREIGNAMEX'),'CREDIT','DEBIT'  )) = 'CREDIT'
                        AND r.card_type = 'DEBIT' 
                        AND r.is_card_resolved IS NULL  
                        
                        UNION ALL 
                        SELECT 'MBB AS DEBIT BUT RAZER AS CREDIT' AS issue_name, m.file_name,
                        m.transaction_date,m.amount,m.order_id,m.razer_transaction_id,m.credit_authcode,m.credit_fee,
                        r.billing_date,r.bank_date_time,r.tran_id,r.bank_transaction_id,r.channel,
                        r.card_type,r.card_scheme ,e.payment_channel AS ep_payment_channel
                        FROM ep_supplier_payment_maybank m , ep_supplier_payment_razer r , ep_supplier_payment_ep e
                        WHERE 
                        m.razer_transaction_id = r.tran_id 
                        AND e.payment_id = r.order_id
                        -- AND m.credit_fee = 0  
                        AND IF(m.credit_fee > 0, 'CREDIT',IF (m.card_type IN ('FOREIGNAMEX'),'CREDIT','DEBIT'  )) = 'DEBIT'
                        AND r.is_card_resolved IS NULL  
                        AND r.card_type = 'CREDIT'
                        
                        UNION ALL 
                        
                        SELECT 'MBB AS DEBIT BUT EP AS CREDIT' AS issue_name, m.file_name,
                        m.transaction_date,m.amount,m.order_id,m.razer_transaction_id,m.credit_authcode,m.credit_fee,
                        r.billing_date,r.bank_date_time,r.tran_id,r.bank_transaction_id,r.channel,
                        r.card_type,r.card_scheme ,e.payment_channel AS ep_payment_channel
                        FROM ep_supplier_payment_maybank m , ep_supplier_payment_razer r , ep_supplier_payment_ep e
                        WHERE 
                        m.razer_transaction_id = r.tran_id 
                        AND e.payment_id = r.order_id
                        -- AND m.credit_fee = 0  
                        AND IF(m.credit_fee > 0, 'CREDIT',IF (m.card_type IN ('FOREIGNAMEX'),'CREDIT','DEBIT'  )) = 'DEBIT'
                        AND r.is_card_resolved IS NULL  
                        AND r.card_type = 'DEBIT' 
                        AND e.payment_channel = 'credit';
") );
        return $listPaymentReceipMaybank;
    }

    
    

    protected function summaryReceiptEpByDate($paymentDate) { 
        $listPaymentEp = collect(DB::connection('oracle_nextgen_rpt')
                ->select("SELECT to_char(PAYMENT_DATE,'YYYY-MM-DD') AS date_payment,
                    CHANNEL_NAME,
                    count(PAYMENT_ID) AS total_payment, sum(PAYMENT_AMT) AS amount_payment 
                    FROM (
                        SELECT DISTINCT pp.PAYMENT_ID ,pp.PAYMENT_DATE ,pp.created_date,pp.PAYMENT_AMT ,pb.BILL_TYPE ,pb.BILL_NO ,pp.RECEIPT_NO,
                        ppo.CHANNEL_NAME , ppr.transaction_id ,ppr.PAYMENT_STATUS 
                        FROM  py_payment pp, py_payment_order ppo,py_payment_response ppr, py_bill pb 
                                        WHERE receipt_no IS NOT NULL
                                        AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
                                        AND pp.BILL_ID  = pb.bill_id
                                        AND ppo.payment_order_id = ppr.payment_order_id
                                        AND pb.BILL_TYPE  IN ('P','R') 
                                        AND ppr.payment_status = '00' 
                                        AND pp.STATUS_ID = '20405'
                                        AND trunc(pp.payment_date) = to_date(?,'YYYY-MM-DD')
                    ) tmp 
                    GROUP BY to_char(PAYMENT_DATE,'YYYY-MM-DD') ,CHANNEL_NAME",array($paymentDate)));
        return $listPaymentEp;
    }

    protected function listReceiptEpByDate($paymentDate) { 
        $list = DB::connection('oracle_nextgen_rpt')
                    ->select("SELECT DISTINCT 
                        pp.PAYMENT_ID ,pp.PAYMENT_DATE ,pp.created_date,pp.PAYMENT_AMT ,pb.BILL_TYPE ,pb.BILL_NO ,pp.RECEIPT_NO,
                        ppo.CHANNEL_NAME , ppr.transaction_id  ,ppr.PAYMENT_STATUS 
                        FROM  py_payment pp, py_payment_order ppo,py_payment_response ppr, py_bill pb 
                        WHERE receipt_no IS NOT NULL
                        AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
                        AND pp.BILL_ID  = pb.bill_id
                        AND ppo.payment_order_id = ppr.payment_order_id
                        AND pb.BILL_TYPE  IN ('P','R') 
                        AND ppr.payment_status = '00' 
                        AND pp.STATUS_ID = '20405'
                        AND trunc(pp.payment_date) = to_date(?,'YYYY-MM-DD')",array($paymentDate));
        return $list;
    }

    /**
     * This issue to capture receipt eP already success suddenly the status re-update as  failed instead on success
     */
    protected function listPaymentReceiptWithStatusFailed() {
        $query = "SELECT DISTINCT 
                    pp.PAYMENT_ID ,pp.PAYMENT_DATE ,pp.created_date,pp.PAYMENT_AMT ,pb.BILL_TYPE ,pb.BILL_NO ,pp.RECEIPT_NO,pp.CHANGED_DATE ,pp.STATUS_ID ,
                    ppo.CHANNEL_NAME , ppr.transaction_id  ,ppr.PAYMENT_STATUS , ppr.CREATED_DATE 
                    FROM  py_payment pp, py_payment_order ppo,py_payment_response ppr, py_bill pb 
                    WHERE receipt_no IS NOT NULL
                    AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
                    AND pp.BILL_ID  = pb.bill_id
                    AND ppo.payment_order_id = ppr.payment_order_id
                    AND pb.BILL_TYPE  IN ('P','R')
                    AND ppr.payment_status = '00' 
                    AND pp.STATUS_ID <> '20405'";
        $result =  DB::connection('oracle_nextgen_rpt')->select($query);
        if (count($result) > 0) {
            return $result;
        }
        return null;
    }

    /**
     * This issue transaction date from Molpay as Success Payment not same with PaymentDate in PY_PAYMENT.
     */
    protected function listPaymentDateDiffWithTrasanctionPaidDate() {
        $query = "SELECT distinct ss.supplier_id,ss.company_name,ss.ep_no,
                    pp.payment_id AS order_id,
                    pb.bill_no,pb.bill_type,
                    pp.CREATED_DATE AS order_created_date,
                    pp.CHANGED_DATE AS order_changed_date,
                    pp.payment_date,pp.payment_id,pp.bill_id,
                    CONCAT(CONCAT((select code_desc from pm_parameter_desc where parameter_id = pp.payment_mode_id and language_code = 'ms'), ' - '), pp.payment_mode_id) as payment_mode,
                    pp.payment_amt,
                    CONCAT(CONCAT((select status_name from PM_STATUS_DESC where status_id = pp.status_id and language_code = 'ms'), ' - '), pp.status_id) as status,
                    pp.receipt_no , ppo.payment_gateway,
                    ppr.transaction_id 
                from sm_supplier ss , 
                    py_bill pb ,
                    py_payment pp ,
                    py_payment_order ppo,
                    PY_PAYMENT_RESPONSE ppr
                    where
                    ss.supplier_id = pb.ORG_PROFILE_ID
                    and pb.BILL_ID = pp.BILL_ID 
                    AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
                    AND ppo.payment_order_id = ppr.payment_order_id 
                    AND pp.receipt_no IS NOT NULL 
                    AND pb.bill_type in ('R','P')
                    AND ppr.payment_status = '00' 
                    AND trunc(ppr.created_date) >= trunc(sysdate-1)
                    AND TRUNC(ppr.TRANSACTION_DATE)  > trunc(pp.PAYMENT_DATE)
                ORDER BY pp.payment_date desc";
        $result =  DB::connection('oracle_nextgen_rpt')->select($query);
        if (count($result) > 0) {
            return $result;
        }
        return null;
    }


    /**
     * To get list of razer payment success using card and not yet checking bin check integration 
     * exclude if already exist in ep_bin_info
     */
    protected function listPaymentCardRazerNotIntegrationBinCheck() {
        $query = "SELECT * FROM ep_supplier_payment_razer WHERE 
                card_type IS NOT NULL
                AND stat_code = '00' 
                AND card_bin is NOT NULL  
                -- AND api_card_type IS NULL 
                AND bin_card IS NULL 
                ";
        $result =  DB::connection('mysql_ep_support')->select($query);

        return $result;
    }
    
}
