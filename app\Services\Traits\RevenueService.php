<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait RevenueService {

    /**
     * Get list of records for 0.4 percentage revenue
     * @param type 
     * @return type
     */
    public function getPaymentStatPercentage($yearSelect) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select 'Transaction Revenue (TR)' as name, a.MONTH_NUM ,a.YEAR , a.MONTH, SUM(a.cdc_fee_amount) AS SUM_AMOUNT, count(*) as total_doc from
                    (
                      SELECT distinct
                        TO_CHAR(pa.created_date, 'mm')   AS                                          MONTH_NUM,
                        TO_CHAR(pa.created_date, 'yyyy') AS                                          YEAR,
                        TO_CHAR(TRUNC(pa.created_date, 'mm'), 'month', 'nls_date_language=american') MONTH,
                        pa.created_date,
                        fr.doc_no,
                        pa.payment_advice_no,
                        v.INVOICE_ID,
                        v.supplier_or_factor_amt as supplier_amount,
                        v.fee_amt as cdc_fee_amount,
                        v.invoice_amt_new as invoice_amount
                      FROM fl_payment_advice pa, fl_workflow_status a, fl_fulfilment_request fr , fl_invoice v
                      WHERE a.doc_id = pa.fulfilment_order_id
                            AND fr.fulfilment_req_id = pa.fulfilment_req_id
                            AND pa.fulfilment_order_id = v.fulfilment_order_id
                            AND a.status_id IN (41530, 41535, 41030, 41035)
                            AND a.doc_type IN ('PO', 'CO')
                            -- and trunc(fr.AG_APPROVED_DATE) between '01-Mar-2018' and '31-Mar-2018'
                            AND fr.financial_year = ?
                            AND TO_CHAR(pa.created_date, 'yyyy') = ? 
                            AND a.IS_CURRENT = 1
                            AND pa.record_status = 1
                            AND v.record_status = 1
                            -- and fr.doc_no = 'PR200000001589769'
                    ) a
                  group by a.MONTH_NUM ,a.YEAR , a.MONTH
                  ORDER BY a.MONTH_NUM ASC", array($yearSelect, $yearSelect));

        return $results;
    }

    /**
     * Get list of records for 0.4 percentage revenue
     * @param type 
     * @return type
     */
    public function getStatPVTRDailyToday() {
      $results = DB::connection('oracle_nextgen_rpt')->select("SELECT 'Procurement Value (PV)' as name, trunc(sysdate) AS date_created_trans, sum(total_amount) AS SUM_AMOUNT, count(*) as total_doc 
      from (
              select
                TO_CHAR(fo.created_date, 'mm')   AS                                          MONTH_NUM,
                TO_CHAR(fo.created_date, 'yyyy') AS                                          YEAR,
                TO_CHAR(TRUNC(fo.created_date, 'mm'), 'month', 'nls_date_language=american') MONTH,
                fo.created_date,
                fo.doc_no,
                (SELECT nvl(SUM (ffid.ordered_amt),0)
                                      FROM fl_fulfilment_item_addr ffid,
                                           fl_delivery_address fda
                                      WHERE ffid.fulfilment_addr_id = fda.delivery_address_id
                                        AND fda.fulfilment_req_id = fr.fulfilment_req_id ) AS total_amount
              from
                FL_FULFILMENT_REQUEST fr ,
                FL_FULFILMENT_ORDER fo
              where
              fr.FULFILMENT_REQ_ID = fo.FULFILMENT_REQ_ID
              and fr.FINANCIAL_YEAR = to_char(sysdate,'YYYY') 
              AND trunc(fo.created_date) = trunc(sysdate)
              -- AND fo.created_date BETWEEN to_date('2021-11-15 00:00','YYYY-MM-DD HH24:MI') AND to_date('2021-11-15 19:00','YYYY-MM-DD HH24:MI') 
              -- and fr.FULFILMENT_REQ_ID not in (select fulfilment_req_id from fl_payment_advice where record_status = 1  )
       ) 
       UNION                   
      select 'Transaction Revenue (TR)' as name, trunc(sysdate) AS date_created_trans, SUM(a.cdc_fee_amount) AS SUM_AMOUNT, count(*) as total_doc 
      from
          (
            SELECT distinct
              TO_CHAR(pa.created_date, 'mm')   AS                                          MONTH_NUM,
              TO_CHAR(pa.created_date, 'yyyy') AS                                          YEAR,
              TO_CHAR(TRUNC(pa.created_date, 'mm'), 'month', 'nls_date_language=american') MONTH,
              pa.created_date,
              fr.doc_no,
              pa.payment_advice_no,
              v.INVOICE_ID,
              v.supplier_or_factor_amt as supplier_amount,
              v.fee_amt as cdc_fee_amount,
              v.invoice_amt_new as invoice_amount
            FROM fl_payment_advice pa, fl_workflow_status a, fl_fulfilment_request fr , fl_invoice v
            WHERE a.doc_id = pa.fulfilment_order_id
                  AND fr.fulfilment_req_id = pa.fulfilment_req_id
                  AND pa.fulfilment_order_id = v.fulfilment_order_id
                  AND a.status_id IN (41030,41530) -- (41530, 41535, 41030, 41035)
                  AND a.doc_type IN ('PO', 'CO')
                  AND fr.financial_year = to_char(sysdate,'YYYY') 
                  AND trunc(a.created_date) = trunc(sysdate)
                  -- AND a.created_date BETWEEN to_date('2021-11-15 00:00','YYYY-MM-DD HH24:MI') AND to_date('2021-11-15 19:00','YYYY-MM-DD HH24:MI') 
                  -- AND a.IS_CURRENT = 1
                  AND pa.record_status = 1
                  AND v.record_status = 1
          ) a
      
      UNION 
      
      SELECT 'SR Registration' as name, trunc(sysdate) AS date_created_trans, SUM(PAYMENT_AMT) AS SUM_AMOUNT, count(*) as total_doc 
      FROM (
      SELECT
            TO_CHAR(pp.payment_date, 'YYYY') AS YEAR,
            TO_CHAR(pp.payment_date, 'MM')   AS MONTH,
            pb.bill_type,
            decode(pb.bill_type,
                   'R', 'Registration Fee',
                   'P', 'Processing Fee',
                   'S', 'Softcert Fee')      as bill_type,
            pp.PAYMENT_AMT
          FROM py_bill pb,
            py_payment pp
          WHERE pb.bill_id = pp.bill_id
                AND pp.receipt_no IN (
            select distinct receipt_no
            FROM py_bill pb,
              py_payment pp
            WHERE pb.bill_id = pp.bill_id
              AND pp.receipt_no IS NOT NULL
               AND trunc(pp.payment_date) = trunc(sysdate) 
               AND trunc(pp.changed_date) = trunc(sysdate) 
               -- AND pp.changed_date BETWEEN to_date('2021-11-15 00:00','YYYY-MM-DD HH24:MI') AND to_date('2021-11-15 19:00','YYYY-MM-DD HH24:MI') 
               AND pb.bill_type = 'R'
          )
      ) s");

      return $results;
  }

    /**
     * Get list of amount PV by Year
     * PV (Procurement Value) : We consider as creation PO/CO record.
     * @param type 
     * @return type
     */
    public function getAmountStatPV($yearSelect) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select 'Procurement Value (PV)' as name,  YEAR,MONTH_NUM,MONTH,sum(total_amount) AS SUM_AMOUNT, count(*) as total_doc from (
                    select
                      TO_CHAR(fo.created_date, 'mm')   AS                                          MONTH_NUM,
                      TO_CHAR(fo.created_date, 'yyyy') AS                                          YEAR,
                      TO_CHAR(TRUNC(fo.created_date, 'mm'), 'month', 'nls_date_language=american') MONTH,
                      fo.doc_no,
                      (SELECT nvl(SUM (ffid.ordered_amt),0)
                                            FROM fl_fulfilment_item_addr ffid,
                                                 fl_delivery_address fda
                                            WHERE ffid.fulfilment_addr_id = fda.delivery_address_id
                                              AND fda.fulfilment_req_id = fr.fulfilment_req_id ) AS total_amount
                    from
                      FL_FULFILMENT_REQUEST fr ,
                      FL_FULFILMENT_ORDER fo
                    where
                    fr.FULFILMENT_REQ_ID = fo.FULFILMENT_REQ_ID
                    and fr.FINANCIAL_YEAR = ? 
                    and to_char(fo.created_date,'YYYY') = ? 
                    ) p
                    group by YEAR,MONTH_NUM,MONTH
                    order by MONTH_NUM asc ", array($yearSelect,$yearSelect));

        return $results;
    }
    
    /**
     * Get list of records payment statistics
     * @param type $supplierID
     * @return type
     */
    public function getPaymentStatInfo($yearSelect) {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
              TO_CHAR(pp.payment_date, 'YYYY') AS YEAR,
              TO_CHAR(pp.payment_date, 'MM')   AS MONTH,
              pb.bill_type,
              decode(pb.bill_type,
                     'R', 'Registration Fee',
                     'P', 'Processing Fee',
                     'S', 'Softcert Fee')      as bill_type,
              -- SUM(pp.PAYMENT_AMT)              as TOTAL_AMT 
              SUM(decode(pp.PAYMENT_AMT,
                     '400', '100',pp.PAYMENT_AMT))              as TOTAL_AMT 
               
            FROM py_bill pb,
              py_payment pp
            WHERE pb.bill_id = pp.bill_id
                  AND pp.receipt_no IN (
              select distinct receipt_no
              FROM py_bill pb,
                py_payment pp
              WHERE pb.bill_id = pp.bill_id
                    AND pp.receipt_no IS NOT NULL
                    AND TO_CHAR(pp.payment_date, 'YYYY') = ?
            )
            GROUP BY TO_CHAR(pp.payment_date, 'YYYY'), TO_CHAR(pp.payment_date, 'MM'), pb.bill_type
            order by year, month", array($yearSelect));

        return $results;
    }
    
    public function cmsListSummaryPVDailyByDate($dateSearch) {
        //dump($dateSearch);
        $crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        $q = "
              /*
                PROCUREMENT VALUE (PV)
                PV 20 Feb : 63.96m
                PV Week 08 : 340.16m
                PV Target Feb : 1.43b
                PV Actual Feb : 1.03b
                Variance : -401.22m   (Actual Feb - Target Feb)
                Daily Target to achieve : 66.87m  ( Variance / Day Left )
                Day(s) left Feb : 6 Days
                Average Daily PV (01-20 Feb) : 73.34m
                No of Working Day(s) : 14 Days
                Target PV 2020 : 22b
                Target PV As Of Feb : 2.53b
                Actual PV Todate : 2.23b
                TQ.
                */

              SELECT
                CONCAT('PV ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
                'daily_pv' AS code_name,
                1 AS code_seq,
                format(SUM(fl_total_amount),2) AS total
              FROM ep_fulfilment_dtl_$year
              WHERE
                -- date(fl_ag_approved_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) -- Set PV by ag_approved_date)
                DATE(fl_created_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  -- Set PV by PO created
                AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

              UNION

              SELECT
                CONCAT('PV Working Days ',(SELECT DISTINCT CONCAT(gm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
                'daily_working_day_pv' AS code_name,
                1.1 AS code_seq,
                format(SUM(fl_total_amount),2) AS total
              FROM ep_fulfilment_dtl_$year
              WHERE
                DATE(fl_created_date)  IN  (
                      SELECT DATE FROM ep_yearly_calendar WHERE gm_group_working_day = (
                              SELECT gm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
                )
                AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

               UNION

              SELECT
                CONCAT('PV Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ),' (',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'),')  (RM) : ') AS name,
                'weekly_pv' AS code_name,
                2 AS code_seq,
                format(SUM(p.fl_total_amount),2) AS total
              FROM ep_fulfilment_dtl_$year p, ep_yearly_calendar cal
              WHERE
                -- cal.date = DATE(p.fl_ag_approved_date)  -- Set PV by ag_approved_date)
                DATE(cal.date) = DATE(p.fl_created_date)  -- Set PV by PO created
                AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
                AND p.fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND MONTH(p.fl_created_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
				AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

              UNION


              select a.name,a.code_name,a.code_seq,case when a.total is null then  
                (SELECT FORMAT(SUM(a.`amount`),2) AS total 
                FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'PV'
                AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                ELSE a.total
                end as total from        
                (SELECT
                CONCAT('PV Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS name,
                'monthly_target' AS code_name,
                3 AS code_seq,
                (SELECT total
                      FROM (SELECT
                              COUNT(DISTINCT ptj_id) total_ptj,
                              format(SUM(target_month),2) AS total
                              FROM ep_ref_target_gm
                              WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND target_name = 'PV' ) temp ) AS total
              FROM DUAL) a

              UNION

              SELECT
                    CONCAT('PV Actual ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'), ' (RM) : ') AS NAME,
                    'monthly_pv' AS code_name,
                    4 AS code_seq,
                    FORMAT(SUM(fl_total_amount),2) AS total
                FROM ep_fulfilment_dtl_$year
                WHERE
                    MONTH(fl_created_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
                    AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                    AND DATE(fl_created_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
        
              UNION

              SELECT 'Variance (RM) : ' AS NAME,
              'monthly_variance' AS code_name,
              5 AS code_seq,
              FORMAT((
                (SELECT
                  SUM(fl_total_amount) AS total
                 FROM ep_fulfilment_dtl_$year
                 WHERE
                 MONTH(fl_created_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
                    AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                    AND DATE(fl_created_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))) 
                -
                (SELECT CASE WHEN a.total IS NULL THEN  
                (SELECT SUM(a.`amount`)AS total 
                FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'PV'
                AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                ELSE a.total
                END AS total FROM        
                (SELECT
                CONCAT('PV Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS NAME,
                'monthly_target' AS code_name,
                3 AS code_seq,
                (SELECT total
                      FROM (SELECT
                              COUNT(DISTINCT ptj_id) total_ptj,
                              SUM(target_month) AS total
                              FROM ep_ref_target_gm
                              WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND target_name = 'PV' ) temp ) AS total
              FROM DUAL) a)
              ),2) AS total
              FROM DUAL

              UNION

              SELECT 'Daily Target To Achieve (RM) : ' AS NAME, 
              'daily_target_archive' AS code_name,
              6 AS code_seq,
               FORMAT(ABS(
               (SELECT
                 (
                  (SELECT
                  SUM(fl_total_amount) AS total
                 FROM ep_fulfilment_dtl_$year
                 WHERE
                 MONTH(fl_created_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
                    AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                    AND DATE(fl_created_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                   -
                  (SELECT CASE WHEN a.total IS NULL THEN  
                (SELECT SUM(a.`amount`)AS total 
                FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'PV'
                AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                ELSE a.total
                END AS total FROM        
                (SELECT
                CONCAT('PV Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS NAME,
                'monthly_target' AS code_name,
                3 AS code_seq,
                (SELECT total
                      FROM (SELECT
                              COUNT(DISTINCT ptj_id) total_ptj,
                              SUM(target_month) AS total
                              FROM ep_ref_target_gm
                              WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND target_name = 'PV' ) temp ) AS total
              FROM DUAL) a)
                   ) AS total
                 FROM DUAL )
                /
                IF((SELECT
                      COUNT(DISTINCT gm_group_working_day) AS total
                      FROM ep_yearly_calendar c
		      WHERE
		      c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
		      AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
		      AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
					WHERE d.year = c.year AND d.month = c.month 
					AND d.gm_group_working_day IN 
					    ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
					      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
					    )
				    )
                )=0,1,(SELECT
                      COUNT(DISTINCT gm_group_working_day) AS total
                      FROM ep_yearly_calendar c
		      WHERE
		      c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
		      AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
		      AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
					WHERE d.year = c.year AND d.month = c.month 
					AND d.gm_group_working_day IN 
					    ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
					      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
					    )
				    )
                ))
              ),2) AS total
              FROM DUAL

              UNION

              SELECT
               CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'),' : '  ) AS NAME,
              'day_left' AS code_name,
              7 AS code_seq,
              cOUNT(DISTINCT gm_group_working_day) AS total
              FROM ep_yearly_calendar c
              WHERE
              c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
              AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
              AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                                WHERE d.year = c.year AND d.month = c.month 
				AND d.gm_group_working_day IN 
                                    ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
                                      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                    )
                            )

              UNION

              SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS NAME,
              'daily_average_collection' AS code_name,
              8 AS code_seq,
               format(ABS(
               (SELECT
                  SUM(fl_total_amount) AS total
                 FROM ep_fulfilment_dtl_$year
                 WHERE
                 MONTH(fl_created_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
                    AND fl_financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                    AND DATE(fl_created_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                /
                (SELECT
                      COUNT(DISTINCT gm_group_working_day) AS total
                      FROM ep_yearly_calendar
                      WHERE
                      YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
                      AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                )
              ),2) AS total
              FROM DUAL

              UNION

              SELECT
               CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' : ' ) AS NAME,
              'working_day' AS code_name,
              9 AS code_seq,
              short_amount_format(COUNT(DISTINCT gm_group_working_day)) AS total
              FROM ep_yearly_calendar
              WHERE
              YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
              AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
              AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))

              UNION

              /*SELECT
                CONCAT('Target PV ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS NAME,
              'yearly_target' AS code_name,
              10 AS code_seq,
                (SELECT total
                      FROM (SELECT
                              COUNT(DISTINCT ptj_id) total_ptj,
                              format(SUM(target_month),2) AS total
                              FROM ep_ref_target_gm
                              WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND target_name = 'PV' ) temp ) AS total
              FROM DUAL*/
              select CONCAT('Target PV ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS NAME,
                'yearly_target' AS code_name,
                10 AS code_seq,
                format(sum(a.`amount`),2) AS total 
                from ep_ref_target a where a.`module` = 'GM' and a.`group` = 'PV'
                and a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

              UNION

              /*SELECT CONCAT('Target PV As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS NAME,
              'monthly_accumulate_target' AS code_name,
              11 AS code_seq,
              (SELECT total
                      FROM (SELECT
                              COUNT(DISTINCT ptj_id) total_ptj,
                              format(SUM(target_month),2) AS total
                              FROM ep_ref_target_gm
                              WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                              AND target_name = 'PV' ) temp ) AS total
              FROM DUAL*/
              select CONCAT('Target PV As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS NAME,
                'monthly_accumulate_target' AS code_name,
                11 AS code_seq,
                format(sum(a.`amount`),2) 
                from ep_ref_target a where a.`module` = 'GM' and a.`group` = 'PV'
                and a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                and a.`month` <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

              UNION

              SELECT
                CONCAT('Actual PV To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS NAME,
                'actual_todate_revenue' AS code_name,
                12 AS code_seq,
                format(SUM(amount_pv),2) AS total
              FROM ep_fulfilment_summary
              WHERE
                financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND SUBSTRING(mm_pv,4,4) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 

              UNION

              SELECT
                CONCAT('Actual PV Trans. No. To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , '  : ') AS NAME,
                'actualno_todate_revenue' AS code_name,
                13 AS code_seq,
                CAST(SUM(total_transaction) AS CHAR)  AS total
              FROM ep_fulfilment_summary
              WHERE
                financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND SUBSTRING(mm_pv,4,4) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result;
    }
    
    public function cmsListSummaryTRDailyByDate($dateSearch) {
        $crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        /*
            TRANSACTION REVENUE (TR)
           TR 20 Feb : 200.17k
           TR Week 08 : 1.09m
           TR Target Feb : 3.12m
           TR Actual Feb : 2.17m
           Variance : -951.36k
           Daily Target to achieve : 158.56k
           Day(s) left Feb : 6 Days
           Average Daily TR (01-20 Feb) : 154.90k
           No of Working Day(s) : 14 Days
           Target TR 2020 : 90m
           Target TR As Of Feb : 4.17m
           Actual TR Todate : 2.90m
           TQ.
           */
        $q = "SELECT name,code_name,code_seq,FORMAT(SUM(total),2) as total FROM (
  SELECT
     CONCAT('TR ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
     'daily_tr' AS code_name,
     1 AS code_seq,
     SUM(fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_dtl_$year
   WHERE
     DATE(fl_trans_revenue_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  -- Set Pending Payment Date
     AND YEAR(fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
  UNION ALL
  SELECT
     CONCAT('TR ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
     'daily_tr' AS code_name,
     1 AS code_seq,
     SUM(fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_carry_year_dtl
   WHERE
     DATE(fl_trans_revenue_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  -- Set Pending Payment Date
     AND YEAR(fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
   ) tmp 
GROUP BY 1,2,3
-- 1) DAILY TR

UNION

-- 2) WORKING DAY TR
SELECT name,code_name,code_seq,FORMAT(SUM(total),2) as total FROM (
		
   SELECT
     CONCAT('TR Working Days ',(SELECT DISTINCT CONCAT(gm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
     'daily_working_day_pv' AS code_name,
     1.1 AS code_seq,
     SUM(fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_dtl_$year
   WHERE
     DATE(fl_trans_revenue_date)  IN  (
           SELECT DATE FROM ep_yearly_calendar WHERE gm_group_working_day = (
                   SELECT gm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
     )
     AND YEAR(fl_trans_revenue_date)  = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
   UNION ALL 
   SELECT
     CONCAT('TR Working Days ',(SELECT DISTINCT CONCAT(gm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
     'daily_working_day_pv' AS code_name,
     1.1 AS code_seq,
     SUM(fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_carry_year_dtl
   WHERE
     DATE(fl_trans_revenue_date)  IN  (
           SELECT DATE FROM ep_yearly_calendar WHERE gm_group_working_day = (
                   SELECT gm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
     )
     AND YEAR(fl_trans_revenue_date)  = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
      ) tmp 
GROUP BY 1,2,3

UNION

-- 3) WEEK TR
SELECT name,code_name,code_seq,FORMAT(SUM(total),2) as total FROM (
   SELECT
     CONCAT('TR Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ),' (',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'),') (RM) : ') AS name,
     'weekly_tr' AS code_name,
     2 AS code_seq,
     SUM(p.fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_dtl_$year p, ep_yearly_calendar cal
   WHERE
     DATE(cal.date) = DATE(p.fl_trans_revenue_date)  -- Set Pending Payment Date
     AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
     AND YEAR(p.fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
     AND MONTH(p.fl_trans_revenue_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	 AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
   UNION ALL 
   SELECT
     CONCAT('TR Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ),' (',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'),') (RM) : ') AS name,
     'weekly_tr' AS code_name,
     2 AS code_seq,
     SUM(p.fl_total_cdc_fee_amount) AS total
   FROM ep_fulfilment_carry_year_dtl p, ep_yearly_calendar cal
   WHERE
     DATE(cal.date) = DATE(p.fl_trans_revenue_date)  -- Set Pending Payment Date
     AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
     AND YEAR(p.fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     -- AND fl_latest_status  IN ('Pending Payment','Closed')
     AND MONTH(p.fl_trans_revenue_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	 AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	) tmp 
GROUP BY 1,2,3
           
UNION

-- 4) MONTHLY TARGET
select a.name,a.code_name,a.code_seq,case when a.total is null then  
        (SELECT FORMAT(SUM(a.`amount`),2) AS total 
    FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'TR'
    AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
    ELSE a.total
    end as total from        
    (SELECT
    CONCAT('TR Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS name,
    'monthly_target' AS code_name,
    3 AS code_seq,
    (SELECT total
          FROM (SELECT
                  COUNT(DISTINCT ptj_id) total_ptj,
                  format(SUM(target_month),2) AS total
                  FROM ep_ref_target_gm
                  WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                  AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                  AND target_name = 'TR' ) temp ) AS total
FROM DUAL) a

UNION

-- 5) MONTHLY TR
SELECT name,code_name,code_seq,FORMAT(SUM(total),2) as total FROM (
    SELECT
        CONCAT('TR Actual ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'), ' (RM) : ') AS NAME,
        'monthly_tr' AS code_name,
        4 AS code_seq,
        SUM(fl_total_cdc_fee_amount) AS total
    FROM ep_fulfilment_dtl_$year
    WHERE
        MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
        AND YEAR(fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        -- AND fl_latest_status  IN ('Pending Payment','Closed')
    UNION ALL
    SELECT
        CONCAT('TR Actual ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b'), ' (RM) : ') AS NAME,
        'monthly_tr' AS code_name,
        4 AS code_seq,
        SUM(fl_total_cdc_fee_amount) AS total
    FROM ep_fulfilment_carry_year_dtl
    WHERE
        MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
        AND YEAR(fl_trans_revenue_date)= YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        -- AND fl_latest_status  IN ('Pending Payment','Closed')
 ) tmp GROUP BY 1,2,3    
                  
UNION

-- 5) VARIANCE TR
SELECT 'Variance (RM) : ' AS NAME,
   'monthly_variance' AS code_name,
   5 AS code_seq,
   FORMAT((
     (
      SELECT SUM(total) as total FROM (
	      SELECT
	       SUM(fl_total_cdc_fee_amount) AS total
	      FROM ep_fulfilment_dtl_$year
	      WHERE
	       MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
	        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	       -- AND fl_latest_status  IN ('Pending Payment','Closed') 
	      UNION ALL  
	      SELECT
	       SUM(fl_total_cdc_fee_amount) AS total
	      FROM ep_fulfilment_carry_year_dtl
	      WHERE
	       MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
	        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	       -- AND fl_latest_status  IN ('Pending Payment','Closed') 
      ) tmp 
     )
     -
     (SELECT CASE WHEN a.total IS NULL THEN  
        (SELECT SUM(a.`amount`) AS total 
        FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'TR'
        AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
        ELSE a.total
        END AS total FROM        
        (SELECT
        CONCAT('TR Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS NAME,
        'monthly_target' AS code_name,
        3 AS code_seq,
        (SELECT total
              FROM (SELECT
                      COUNT(DISTINCT ptj_id) total_ptj,
                      SUM(target_month) AS total
                      FROM ep_ref_target_gm
                      WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      AND target_name = 'TR' ) temp ) AS total
      FROM DUAL) a)
   ),2) AS total
FROM DUAL

UNION

-- 6) DAILY TARGET TR
SELECT 'Daily Target To Achieve (RM) : ' AS NAME,
   'daily_target_archive' AS code_name,
   6 AS code_seq,
    FORMAT(ABS(
    (SELECT
      (
       (SELECT SUM(total) as total FROM ( 
          SELECT
	       SUM(fl_total_cdc_fee_amount) AS total
	      FROM ep_fulfilment_dtl_$year
	      WHERE
	       MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
	        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	       -- AND fl_latest_status  IN ('Pending Payment','Closed') 
	      UNION ALL 
	      SELECT
	       SUM(fl_total_cdc_fee_amount) AS total
	      FROM ep_fulfilment_carry_year_dtl
	      WHERE
	       MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
	        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
	       -- AND fl_latest_status  IN ('Pending Payment','Closed') 
	    ) tmp 
       )
        -
       (SELECT CASE WHEN a.total IS NULL THEN  
        (SELECT SUM(a.`amount`) AS total 
        FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'TR'
        AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND a.`month` =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
        ELSE a.total
        END AS total FROM        
        (SELECT
        CONCAT('TR Target ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%b') , ' (RM) : ') AS NAME,
        'monthly_target' AS code_name,
        3 AS code_seq,
        (SELECT total
              FROM (SELECT
                      COUNT(DISTINCT ptj_id) total_ptj,
                      SUM(target_month) AS total
                      FROM ep_ref_target_gm
                      WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                      AND target_name = 'TR' ) temp ) AS total
      FROM DUAL) a)
        ) AS total
      FROM DUAL )
     /
     IF((SELECT
           COUNT(DISTINCT gm_group_working_day) AS total
           FROM ep_yearly_calendar c
      WHERE
      c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
      AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
      AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
			WHERE d.year = c.year AND d.month = c.month 
			AND d.gm_group_working_day IN 
			    ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
			      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
			    )
		    )
     )=0,1,(SELECT
           COUNT(DISTINCT gm_group_working_day) AS total
           FROM ep_yearly_calendar c
      WHERE
      c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
      AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
      AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
			WHERE d.year = c.year AND d.month = c.month 
			AND d.gm_group_working_day IN 
			    ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
			      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
			    )
		    )
     ))
   ),2)AS total
FROM DUAL

UNION

-- 7) DAY LEFT TR
SELECT
    CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'), ' : '  ) AS name,
   'day_left' AS code_name,
   7 AS code_seq,
   short_amount_format(COUNT(DISTINCT gm_group_working_day)) AS total
FROM ep_yearly_calendar c
WHERE
   c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
   AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
   AND c.DATE > ( 
   	SELECT MAX(DATE) FROM ep_yearly_calendar d 
    	WHERE d.year = c.year AND d.month = c.month 
   		AND d.gm_group_working_day IN 
                            ( SELECT gm_group_working_day FROM ep_yearly_calendar d 
                              WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                            )
   )

UNION

-- 8) AVERAGE COLLECTION TR
SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS name,
   'daily_average_collection' AS code_name,
   8 AS code_seq,
    format(ABS(
    (SELECT SUM(total) as total FROM (
       SELECT
        SUM(fl_total_cdc_fee_amount) AS total
       FROM ep_fulfilment_dtl_$year
       WHERE
        MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        -- AND fl_latest_status  IN ('Pending Payment','Closed') 
        UNION ALL 
       SELECT
        SUM(fl_total_cdc_fee_amount) AS total
       FROM ep_fulfilment_carry_year_dtl
       WHERE
        MONTH(fl_trans_revenue_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))  -- Set PV by PO created
        AND YEAR(fl_trans_revenue_date) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        AND DATE(fl_trans_revenue_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
        -- AND fl_latest_status  IN ('Pending Payment','Closed') 
      ) tmp
     )
     /
     (SELECT
           COUNT(DISTINCT gm_group_working_day) AS total
           FROM ep_yearly_calendar
           WHERE
           YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
           AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     )
   ),2) AS total
FROM DUAL

UNION

-- 9) WORKING DAY
           SELECT
            CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' : ' ) AS name,
           'working_day' AS code_name,
           9 AS code_seq,
           short_amount_format(COUNT(DISTINCT gm_group_working_day)) AS total
           FROM ep_yearly_calendar
           WHERE
           YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
           AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))

UNION

-- 10) YEARLY TARGET
/*SELECT
     CONCAT('Target TR ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS name,
   'yearly_target' AS code_name,
   10 AS code_seq,
     format((SELECT total
           FROM (SELECT
                   COUNT(DISTINCT ptj_id) total_ptj,
                   ROUND(SUM(target_month)) AS total
                   FROM ep_ref_target_gm
                   WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                   AND target_name = 'TR' ) temp ),2) AS total
FROM DUAL*/
SELECT CONCAT('Target TR ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS NAME,
        'yearly_target' AS code_name,
10 AS code_seq,
FORMAT(SUM(a.`amount`),2) AS total 
FROM ep_ref_target a WHERE a.`module` = 'GM' AND a.`group` = 'TR'
AND a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

UNION

-- 11) MONTHLY ACCUMULATED TARGET 
/*SELECT CONCAT('Target TR As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS name,
   'monthly_accumulate_target' AS code_name,
   11 AS code_seq,
   format((SELECT total
           FROM (SELECT
                   COUNT(DISTINCT ptj_id) total_ptj,
                   ROUND(SUM(target_month)) AS total
                   FROM ep_ref_target_gm
                   WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                   AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                   AND target_name = 'TR' ) temp ),2) AS total
FROM DUAL*/
SELECT CONCAT('Target TR As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS NAME,
                'monthly_accumulate_target' AS code_name,
                11 AS code_seq,
                format(sum(a.`amount`),2)
                from ep_ref_target a where a.`module` = 'GM' and a.`group` = 'TR'
                and a.`year` = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                and a.`month` <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

UNION

-- 12) ACTUAL REVENUE 
SELECT
     CONCAT('Actual TR To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS name,
     'actual_todate_revenue' AS code_name,
     12 AS code_seq,
     format(SUM(amount_tr_cdc),2) AS total
FROM ep_fulfilment_summary
WHERE
 -- financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
 SUBSTRING(mm_tr,4,4) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
 -- AND STATUS  IN ('Pending Payment','Closed')

UNION

-- 13) ACTUAL TRANS NUMBER REVENUE 
SELECT
 CONCAT('Actual TR Trans. No. To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , '  : ') AS name,
 'actualno_todate_revenue' AS code_name,
     13 AS code_seq,
     CAST(SUM(total_transaction) AS CHAR)  AS total
FROM ep_fulfilment_summary
 WHERE
 -- financial_year = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
 SUBSTRING(mm_tr,4,4) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
 -- AND STATUS  IN ('Pending Payment','Closed')
    ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result; 
    }
    
    /**
     * Supplier Revenue
     * (Registration & Training & Softcert)
     * @param type $dateSearch
     * @return type
     */
    public function cmsListSummarySmSRDailyByDate($dateSearch) {
		$crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        $effDate = '01-04-2022';
		$q = "
           
    /*
    SUPPLIER REVENUE COLLECTION (SR)
    (Registration + Training + Softcert )
    SR 17 Feb:68.04k
    SR Week 08:149.32k
    SR Target Feb:1.66m
    SR Actual Feb:661.62k
    Variance:-1.00m  (Actual - Target)
    Daily Target Feb to achieve :110.75k     (Variance / Day Left)
    Day(s) left Feb:9 Days
    Average Daily SR (01-17 Feb) :60.15k    ( Actual  / Working Day )
    No of Working Day(s) :11 Days
    Target SR 2020:21.14m
    Target SR As Of Feb:2.90m
    Actual SR Todate:2.02m
    */

    SELECT CONCAT('SR ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d %b') ,' (RM)') AS name, 
     'sr_revenue' AS code_name,
     1 AS code_seq,
     (
       -- Get Amount Registered (Renew + New)
       IFNULL(
            (SELECT 
            CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
            FROM ep_supplier_payment 
            WHERE 
            YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL
            AND bill_type = 'R' 
            AND receipt_no IS NOT NULL )
       ,0)
       +
       -- Get Amount Training
       IFNULL(
            (SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice 
            WHERE invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND DATE(invoice_closed) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND product_category_name IN ('Training','Membership'))
        ,0)
       +
       -- Get Amount Softcert
       IFNULL(
            (SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment 
            WHERE bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL )
        ,0)	
    ) AS total 
    FROM DUAL 

    UNION 

    SELECT CONCAT('SR Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) ,' (RM)') AS name, 
     'sr_revenue_workingday' AS code_name,
     1.1 AS code_seq,
     (
       -- Get Amount Registered (Renew + New)
       IFNULL(
            (SELECT 
            CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
            FROM ep_supplier_payment 
            WHERE 
            YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND DATE(payment_date)  IN  (
                    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
            )
            AND receipt_no IS NOT NULL
            AND bill_type = 'R' 
            AND receipt_no IS NOT NULL )
       ,0)
       +
       -- Get Amount Training
       IFNULL(
            (SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice 
            WHERE invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND DATE(invoice_closed)  IN  (
                    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
            )
            AND product_category_name IN ('Training','Membership'))
        ,0)
       +
       -- Get Amount Softcert
       IFNULL(
            (SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment 
            WHERE bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND DATE(payment_date)  IN  (
                    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
            )
            AND receipt_no IS NOT NULL )
        ,0)	
    ) AS total 
    FROM DUAL 

    UNION

    SELECT CONCAT('SR Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ),' (RM)') AS name, 
     'sr_week' AS code_name,
     2 AS code_seq,
     (
       -- Get Amount Registered (Renew + New)
       IFNULL(
            (
             SELECT 
             CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
             FROM ep_supplier_payment , ep_yearly_calendar cal 
             WHERE
             cal.date = DATE(payment_date) 
             AND bill_type = 'R' 
             AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
             AND receipt_no IS NOT NULL 
             AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
             AND MONTH(payment_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))) 
   AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
            )
       ,0)		
       +
       -- Get Amount Training
       IFNULL(
            (SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice , ep_yearly_calendar cal 
            WHERE 
            cal.date = DATE(invoice_closed) 
            AND invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND product_category_name IN ('Training','Membership') 
            AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
            AND MONTH(invoice_closed) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
  AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
            )
       ,0)
       +
       -- Get Amount Softcert
       IFNULL((SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment , ep_yearly_calendar cal 
            WHERE 
            cal.date = DATE(payment_date) 
            AND bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL 
            AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) )
            AND MONTH(payment_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
  AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
            )
       ,0)	
    ) AS total 
    FROM DUAL 

    UNION 

    SELECT CONCAT('SR Target ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM)' ) AS name,  
     'sr_monthly_target' AS code_name,
     3 AS code_seq,
     SUM(amount) AS total  
    FROM ep_ref_target  
     WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
     AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
     AND code_name = 'TOTAL_TARGET_SM_$year'

    UNION 



    SELECT CONCAT('SR Actual ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'),' (RM)' ) AS name, 
     'sr_monthly_revenue' AS code_name,
     4 AS code_seq,
    (
       -- Get Amount Registered (Renew + New)
       IFNULL((SELECT 
            CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
            FROM ep_supplier_payment
            WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND DATE(payment_date)  <=  DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND bill_type = 'R' 
            AND receipt_no IS NOT NULL
       ),0)
       +
       -- Get Amount Training
       IFNULL((SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice 
            WHERE invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(invoice_closed) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND DATE(invoice_closed)  <=  DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND product_category_name IN ('Training','Membership')
        ),0)
       +
       -- Get Amount Softcert
       IFNULL((SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment 
            WHERE bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(payment_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND DATE(payment_date)  <=  DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL
        ),0)	
    ) AS total 
    FROM DUAL 

    UNION 

    SELECT 'Variance (RM)' AS name, 
     'sr_monthly_variance' AS code_name,
     5 AS code_seq,
    (
       (SELECT  (
          -- Get Amount Registered (Renew + New)
          IFNULL(
          (SELECT 
            CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
            FROM ep_supplier_payment
            WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND bill_type = 'R'  
            AND receipt_no IS NOT NULL
            ),0)
          +
          -- Get Amount Training
          IFNULL(
          (SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice 
            WHERE invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(invoice_closed) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND product_category_name IN ('Training','Membership')
            ),0)
          +
          -- Get Amount Softcert
          IFNULL(
          (SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment 
            WHERE bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
            AND MONTH(payment_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL
            ),0)

        ) AS total 
        FROM DUAL ) 
      - 
      IFNULL((SELECT SUM(amount) AS total  
            FROM ep_ref_target  
             WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
             AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
             AND code_name = 'TOTAL_TARGET_SM_$year'
             ),0) 	
    ) AS total 
    FROM DUAL 

    UNION 

    SELECT 'Daily Target To Achieve (RM)' AS name, 
     'sr_daily_target_archive' AS code_name,
     6 AS code_seq,
     ABS( 
     (SELECT 
      (
       (SELECT 
          (
            -- Get Amount Registered (Renew + New)
            IFNULL((SELECT 
              CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total 
              FROM ep_supplier_payment
              WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND bill_type = 'R'  
              AND receipt_no IS NOT NULL
             ),0)
            +
            -- Get Amount Training
            IFNULL((SELECT 
              SUM(unit_price) AS total 
              FROM crm_invoice 
              WHERE invoice_status = 'Paid' 
              AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND MONTH(invoice_closed) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
              AND product_category_name IN ('Training','Membership')
            ),0)
            +
            -- Get Amount Softcert
            IFNULL((SELECT 
              SUM(payment_amount) AS total 
              FROM ep_supplier_payment 
              WHERE bill_type = 'S' 
              AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
              AND MONTH(payment_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND receipt_no IS NOT NULL
            ),0)

          ) AS total 
        FROM DUAL 
        ) 
        - 
       IFNULL((SELECT SUM(amount) AS total  
            FROM ep_ref_target  
             WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
             AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
             AND code_name = 'TOTAL_TARGET_SM_$year'
             ) ,0)	
       ) AS total 
       FROM DUAL )	
      / 
      IFNULL(
      (SELECT 
            COUNT(DISTINCT sm_group_working_day) AS total
            FROM ep_yearly_calendar c
  WHERE
  c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
  AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
  AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
  WHERE d.year = c.year AND d.month = c.month 
  AND d.sm_group_working_day IN 
      ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
        WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
      )
    )
      ),0)	
    ) AS total 
    FROM DUAL 

    UNION 

    SELECT 
     CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  ) AS name, 
     'sr_day_left' AS code_name,
     7 AS code_seq,
     COUNT(DISTINCT sm_group_working_day) AS total
    FROM ep_yearly_calendar c
    WHERE 
     c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
      AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
      AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                        WHERE d.year = c.year AND d.month = c.month 
AND d.sm_group_working_day IN 
                            ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                              WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                            )
                    )

    UNION 


    SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM)' )  AS name, 
     'daily_average_collection' AS code_name,
     8 AS code_seq,
     ABS( 
     (SELECT 
          (
            -- Get Amount Registered (Renew + New)
            IFNULL((SELECT 
              CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
              FROM ep_supplier_payment
              WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND bill_type = 'R'  
              AND receipt_no IS NOT NULL
             ),0)
            +
            -- Get Amount Training
            IFNULL((SELECT 
              SUM(unit_price) AS total 
              FROM crm_invoice 
              WHERE invoice_status = 'Paid' 
              AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND MONTH(invoice_closed) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))  
              AND product_category_name IN ('Training','Membership')
            ),0)
            +
            -- Get Amount Softcert
            IFNULL((SELECT 
              SUM(payment_amount) AS total 
              FROM ep_supplier_payment 
              WHERE bill_type = 'S' 
              AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND MONTH(payment_date) = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND receipt_no IS NOT NULL
            ),0)

          ) AS total 
        FROM DUAL  ) 	
      / 
      IFNULL((SELECT
            COUNT(DISTINCT sm_group_working_day) AS total
            FROM ep_yearly_calendar 
            WHERE 
            YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
            AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
      ),0)	
    ) AS total 
    FROM DUAL 

    UNION

    SELECT 
     CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  ) AS name, 
     'working_day' AS code_name,
     9 AS code_seq,
     COUNT(DISTINCT sm_group_working_day) AS total
    FROM ep_yearly_calendar 
     WHERE 
     YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
     AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
     AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y')) 

    UNION 

    SELECT CONCAT('Target SR ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')), ' (RM)' ) AS name,  
     'sr_monthly_target' AS code_name,
     10 AS code_seq,
     SUM(amount) AS total  
    FROM ep_ref_target  
     WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
     AND code_name = 'TOTAL_TARGET_SM_$year'

     UNION

     SELECT CONCAT('Target SR As Of ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'), ' (RM)' ) AS name,  
     'sr_monthly_target' AS code_name,
     11 AS code_seq,
     SUM(amount) AS total  
    FROM ep_ref_target  
     WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
     AND code_name = 'TOTAL_TARGET_SM_$year'
     AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

    UNION 

    SELECT CONCAT('Actual SR To Date ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' (RM)') AS name, 
     'sr_todate_revenue' AS code_name,
     12 AS code_seq,
    (
       -- Get Amount Registered (Renew + New)
       IFNULL((SELECT SUM(total) AS total FROM(SELECT 
              CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN SUM(100.00)
                ELSE SUM(payment_amount) END AS total
              FROM ep_supplier_payment
              WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
              AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
              AND bill_type = 'R'  
              AND receipt_no IS NOT NULL
              GROUP BY MONTH(payment_date))a
       ),0)
       +
       -- Get Amount Training
       IFNULL((SELECT 
            SUM(unit_price) AS total 
            FROM crm_invoice 
            WHERE invoice_status = 'Paid' 
            AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(invoice_closed)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND product_category_name IN ('Training','Membership')
        ),0)
       +
       -- Get Amount Softcert
       IFNULL((SELECT 
            SUM(payment_amount) AS total 
            FROM ep_supplier_payment 
            WHERE bill_type = 'S' 
            AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND receipt_no IS NOT NULL
        ),0)	
    ) AS total 
           ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result; 
    }
    
    /**
     * Revenue Registration
     * @param type $dateSearch
     * @return type
     */
    public function cmsListSummarySmRegistrationDailyByDate($dateSearch) {
		$crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        $effDate = '01-04-2022';
		$q = "
    /*
REGISTRATION COLLECTION (MOF)
Registration 17 Feb:57.60k
MOF Week 08:130.40k
Target Feb:1.44m
Actual Feb:617.60k
Variance:-822.40k   (Actual Feb - Target Feb)
Daily Target to achieve :91.38k  ( Variance / Day Left )
Day(s) left Feb:9 Days
Average Daily Collection (01-17 Feb) :56.15k   ( Actual Feb / Working Day )
No of Working Day(s) :11 Days
Target Registration 2020 :18m
Target Registration As Of Feb:2.52m
Actual Registration Todate :1.90m
TQ.
*/

SELECT CONCAT('Registration ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
'daily_revenue' AS code_name,
1 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total 
FROM ep_supplier_payment WHERE 
DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND bill_type = 'R' 
AND receipt_no IS NOT NULL 

UNION

SELECT CONCAT('Registration Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
'daily_workingday_revenue' AS code_name,
1.1 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total 
FROM ep_supplier_payment WHERE   
DATE(payment_date)  IN  (
    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  AND  DATE(DATE) <= STR_TO_DATE('$dateSearch','%d-%m-%Y')
)
AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND receipt_no IS NOT NULL 

UNION
/*
SELECT CONCAT('... New ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
'daily_revenue_new' AS code_name,
1.2 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment WHERE 
DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND bill_type = 'R' 
AND doc_type IN ('KN','JN')
AND receipt_no IS NOT NULL 

UNION 


SELECT CONCAT('... Renew ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
'daily_revenue_renew' AS code_name,
1.3 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment WHERE 
DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND bill_type = 'R' 
AND doc_type IN ('KR','JR')
AND receipt_no IS NOT NULL 

*/


SELECT CONCAT('...  New ',DATE_FORMAT(payment_date, '%d %b'), ' (RM)') AS name,
'daily_revenue_new' AS code_name,
ROUND((1.400+DATE_FORMAT(payment_date, '0.0%d') ),4) AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment WHERE 
DATE_FORMAT(payment_date, '%Y-%m-%d')  IN  (
    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')   AND  DATE(DATE) <= STR_TO_DATE('$dateSearch','%d-%m-%Y') 
)
AND bill_type = 'R' 
AND doc_type IN ('KN','JN')
AND receipt_no IS NOT NULL 
GROUP BY DATE_FORMAT(payment_date, '%Y-%m-%d')

UNION 

SELECT CONCAT('...  Renew ',DATE_FORMAT(payment_date, '%d %b'), ' (RM)') AS name,
'daily_revenue_new' AS code_name,
ROUND((1.500+DATE_FORMAT(payment_date, '0.0%d') ),4) AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment WHERE 
DATE_FORMAT(payment_date, '%Y-%m-%d')  IN  (
    SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
            SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')   AND  DATE(DATE) <= STR_TO_DATE('$dateSearch','%d-%m-%Y') 
)
AND bill_type = 'R' 
AND doc_type IN ('KR','JR')
AND receipt_no IS NOT NULL 
GROUP BY DATE_FORMAT(payment_date, '%Y-%m-%d')


UNION 

SELECT CONCAT('MOF Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS name, 
'weekly_revenue' AS code_name,
2 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment p, ep_yearly_calendar cal WHERE 
cal.date = DATE(p.payment_date)
AND YEAR(payment_date) = $year
AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
AND MONTH(p.payment_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND p.bill_type = 'R'  
AND receipt_no IS NOT NULL 

UNION 

SELECT CONCAT('Target ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH,'-1')),'%b') , ' (RM) : ') AS name,  
'monthly_target' AS code_name,
3 AS code_seq,
amount AS total  
FROM ep_ref_target 
WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND code_name = 'TARGET_SR_$year'

UNION 

SELECT CONCAT('Actual ',DATE_FORMAT(payment_date,'%b') , ' (RM) : ') AS name, 
'monthly_revenue' AS code_name,
4 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment WHERE 
YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND DATE(payment_date)  <=  DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R'  
AND receipt_no IS NOT NULL 

UNION 

SELECT CONCAT('Actual (New) ',DATE_FORMAT(payment_date,'%b') , ' (RM) : ') AS NAME, 
'monthly_revenue_new' AS code_name,
4.2 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment 
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND DATE(payment_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND bill_type = 'R' 
AND doc_type IN ('KN','JN')
AND receipt_no IS NOT NULL 

UNION

SELECT CONCAT('Actual (Renew) ',DATE_FORMAT(payment_date,'%b') , ' (RM) : ') AS NAME, 
'monthly_revenue_renew' AS code_name,
4.3 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment  
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND DATE(payment_date) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND bill_type = 'R' 
AND doc_type IN ('KR','JR')
AND receipt_no IS NOT NULL 

UNION 

SELECT 'Variance (RM) : ' AS name, 
'monthly_variance' AS code_name,
5 AS code_seq,
(
(SELECT 
    CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
        ELSE sum(payment_amount) END AS total
    FROM ep_supplier_payment 
    WHERE 
    YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
    AND bill_type = 'R' 
    AND receipt_no IS NOT NULL )
- 
(SELECT 
    amount AS total  
    FROM ep_ref_target 
    WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
    AND code_name = 'TARGET_SR_$year')	
) AS total 
FROM DUAL 

UNION 

SELECT 'Daily Target To Achieve (RM) : ' AS name, 
'daily_target_archive' AS code_name,
6 AS code_seq,
ABS( 
(SELECT  
    (
     (SELECT 
            CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
                ELSE sum(payment_amount) END AS total
            FROM ep_supplier_payment 
            WHERE 
            YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
            AND bill_type = 'R' 
            AND receipt_no IS NOT NULL )
      - 
      (SELECT 
            amount AS total  
            FROM ep_ref_target 
            WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
            AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
            AND code_name = 'TARGET_SR_$year')	
    ) AS total 
    FROM DUAL )	
/ 
(SELECT 
    COUNT(DISTINCT sm_group_working_day) AS total
    FROM ep_yearly_calendar c
WHERE
c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
WHERE d.year = c.year AND d.month = c.month 
AND d.sm_group_working_day IN 
( SELECT sm_group_working_day FROM ep_yearly_calendar d 
WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
)
)
)	
) AS total 
FROM DUAL 

UNION 

SELECT 
CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'),' ; '  ) AS name, 
'day_left' AS code_name,
7 AS code_seq,
COUNT(DISTINCT sm_group_working_day) AS total
FROM ep_yearly_calendar c
WHERE 
c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                WHERE d.year = c.year AND d.month = c.month 
AND d.sm_group_working_day IN 
                    ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                      WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                    )
            )

UNION 

SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS name, 
'daily_average_collection' AS code_name,
8 AS code_seq,
ABS( 
(SELECT 
    CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
        ELSE sum(payment_amount) END AS total
    FROM ep_supplier_payment 
    WHERE 
    YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
    AND bill_type = 'R' 
    AND receipt_no IS NOT NULL ) 	
/ 
(SELECT
    COUNT(DISTINCT sm_group_working_day) AS total
    FROM ep_yearly_calendar 
    WHERE 
    YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
    AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
    AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
)	
) AS total 
FROM DUAL 

UNION

SELECT 
CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' : ' ) AS name, 
'working_day' AS code_name,
9 AS code_seq,
COUNT(DISTINCT sm_group_working_day) AS total
FROM ep_yearly_calendar 
WHERE 
YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))

UNION 

SELECT CONCAT('Target Registration ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS name,  
'yearly_target' AS code_name,
10 AS code_seq,
SUM(amount) AS total  
FROM ep_ref_target 
WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND code_name = 'TARGET_SR_$year'

UNION 

SELECT CONCAT('Target Registration As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS name,  
'monthly_accumulate_target' AS code_name,
11 AS code_seq,
SUM(amount) AS total  
FROM ep_ref_target 
WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
AND code_name = 'TARGET_SR_$year'

UNION 

SELECT a.name, a.code_name, a.code_seq, SUM(total) AS total FROM (
SELECT CONCAT('Actual Registration To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),
'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS NAME, 
        'actual_todate_revenue' AS code_name,
        12 AS code_seq,
        CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN SUM(100.00)
         ELSE SUM(payment_amount) END AS total
        FROM ep_supplier_payment 
        WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
        AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
        AND bill_type = 'R' 
        AND receipt_no IS NOT NULL
        GROUP BY MONTH(payment_date)) a

UNION 

SELECT CONCAT('Actual No. Registration To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , '  : ') AS NAME, 
'actual_no_todate_revenue' AS code_name,
12.1 AS code_seq,
COUNT(*) AS total
FROM ep_supplier_payment 
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND receipt_no IS NOT NULL

UNION 
SELECT a.name, a.code_name, a.code_seq, SUM(total) AS total FROM (
SELECT CONCAT('Actual Registration (New) To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS name, 
'actual_todate_revenue_new' AS code_name,
13 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment 
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND doc_type IN ('KN','JN')
AND receipt_no IS NOT NULL 
GROUP BY MONTH(payment_date)) a

UNION

SELECT CONCAT('Actual No. Registration (New) To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , '  : ') AS NAME, 
'actual_no_todate_revenue_new' AS code_name,
13.1 AS code_seq,
COUNT(*) AS total
FROM ep_supplier_payment 
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND doc_type IN ('KN','JN')
AND receipt_no IS NOT NULL 

UNION

SELECT a.name, a.code_name, a.code_seq, SUM(total) AS total FROM (
SELECT CONCAT('Actual Registration (Renew) To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS name, 
'actual_todate_revenue_renew' AS code_name,
14 AS code_seq,
CASE WHEN DATE(payment_date) >= DATE(STR_TO_DATE('$effDate','%d-%m-%Y')) THEN sum(100.00)
ELSE sum(payment_amount) END AS total
FROM ep_supplier_payment  
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND doc_type IN ('KR','JR')
AND receipt_no IS NOT NULL 
GROUP BY MONTH(payment_date)) a

UNION 

SELECT CONCAT('Actual No. Registration (Renew) To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , '  : ') AS NAME, 
'actual_no_todate_revenue_renew' AS code_name,
14.1 AS code_seq,
COUNT(*)  AS total
FROM ep_supplier_payment  
WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
AND bill_type = 'R' 
AND doc_type IN ('KR','JR')
AND receipt_no IS NOT NULL  

";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result; 
    }
    
    /**
     * Revenue Training
     * @param type $dateSearch
     * @return type
     */
    public function cmsListSummarySmTrainingDailyByDate($dateSearch) {
		$crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        $q = "
        /*
         TRAINING COLLECTION 
         Training 18 Feb : 6.90k
         Training Week 08 : 22.70k
         Training Target Feb : 210.00k
         Training Actual Feb : 43.60k
         Variance : -166.40k
         Daily Target Feb to achieve : 20.80k
         Day(s) left Feb : 8 Days
         Average Daily Training (01-18 Feb) : 3.63k
         No of Working Day(s) : 12 Days
         Target Training 2020 : 3.00m
         Target Training As Of Feb : 360.00k_
         Actual Training Todate : 114.00k_
         */


         SELECT CONCAT('Training ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
         'daily_revenue' AS code_name,
         1 AS code_seq,
         SUM(unit_price) AS total
         FROM crm_invoice WHERE 
         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND DATE(invoice_closed) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION

         SELECT CONCAT('Training No. ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
         'daily_qty_revenue' AS code_name,
         1.1 AS code_seq,
         SUM(product_qty) AS total
         FROM crm_invoice WHERE 
         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND DATE(invoice_closed) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 


         UNION

         SELECT CONCAT('Training Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
         'daily_workingday_revenue' AS code_name,
         1.2 AS code_seq,
         SUM(unit_price) AS total
         FROM crm_invoice WHERE   
         DATE(invoice_closed)  IN  (
                 SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                         SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
         )
         AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid'  

         UNION

         SELECT CONCAT('Training No. Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
         'daily_qty_workingday_revenue' AS code_name,
         1.3 AS code_seq,
         SUM(product_qty) AS total
         FROM crm_invoice WHERE   
         DATE(invoice_closed)  IN  (
                 SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                         SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
         )
         AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION 

         SELECT CONCAT('Training Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS name, 
         'weekly_revenue' AS code_name,
         2 AS code_seq,
         SUM(p.unit_price) AS total
         FROM crm_invoice p, ep_yearly_calendar cal WHERE 
         cal.date = DATE(p.invoice_closed)
         AND YEAR(invoice_closed) = $year
         AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
         AND MONTH(p.invoice_closed) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
   AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION 

         SELECT CONCAT('Training No. Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS name, 
         'weekly_qty_revenue' AS code_name,
         2.1 AS code_seq,
         SUM(product_qty)AS total
         FROM crm_invoice p, ep_yearly_calendar cal WHERE 
         cal.date = DATE(p.invoice_closed)
         AND YEAR(invoice_closed) = $year
         AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
         AND MONTH(p.invoice_closed) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
   AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION 

         SELECT CONCAT('Training Target ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH,'-1')),'%b') , ' (RM) : ') AS name,  
         'monthly_target' AS code_name,
         3 AS code_seq,
         amount AS total  
         FROM ep_ref_target 
         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND code_name = CONCAT('TOTAL_TRAINING_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))

         UNION 

         SELECT CONCAT('Training Actual ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  , ' (RM) : ') AS name, 
         'monthly_revenue' AS code_name,
         4 AS code_seq,
         SUM(unit_price) AS total
         FROM crm_invoice WHERE 
         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION

         SELECT CONCAT('Training No. Actual ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  , ' (RM) : ') AS name, 
         'monthly_qty_revenue' AS code_name,
         4.1 AS code_seq,
         SUM(product_qty)  AS total
         FROM crm_invoice WHERE 
         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
         AND invoice_status = 'Paid' 

         UNION 

         SELECT 'Variance (RM) : ' AS name, 
         'monthly_variance' AS code_name,
         5 AS code_seq,
         (
          (SELECT 
                 SUM(unit_price) AS total
                 FROM crm_invoice 
                 WHERE 
                 YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                 AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
                 AND invoice_status = 'Paid'  )
           - 
           (SELECT 
                 amount AS total  
                 FROM ep_ref_target 
                 WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                 AND code_name = CONCAT('TOTAL_TRAINING_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  )	
         ) AS total 
         FROM DUAL 

         UNION 

         SELECT 'Daily Target To Achieve (RM) : ' AS name, 
         'daily_target_archive' AS code_name,
         6 AS code_seq,
          ABS( 
          (SELECT  
                 (
                  (SELECT 
                         SUM(unit_price) AS total
                         FROM crm_invoice 
                         WHERE 
                         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
                         AND invoice_status = 'Paid'  )
                   - 
                   (SELECT 
                         amount AS total  
                         FROM ep_ref_target 
                         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         AND code_name = CONCAT('TOTAL_TRAINING_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  )		
                 ) AS total 
                 FROM DUAL )	
           / 
           (SELECT 
                 COUNT(DISTINCT sm_group_working_day) AS total
                 FROM ep_yearly_calendar c
       WHERE
       c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
       AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
       AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
       WHERE d.year = c.year AND d.month = c.month 
       AND d.sm_group_working_day IN 
           ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
             WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           )
         )
           )	
         ) AS total 
         FROM DUAL 

         UNION 

         SELECT 
          CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' : ' ) AS name, 
         'day_left' AS code_name,
         7 AS code_seq,
         COUNT(DISTINCT sm_group_working_day) AS total
         FROM ep_yearly_calendar c
         WHERE 
         c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
           AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                             WHERE d.year = c.year AND d.month = c.month 
     AND d.sm_group_working_day IN 
                                 ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                                   WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                 )
                         )

         UNION 

         SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS name, 
         'daily_average_collection' AS code_name,
         8 AS code_seq,
          ABS( 
          (SELECT 
                         SUM(unit_price) AS total
                         FROM crm_invoice 
                         WHERE 
                         YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'
                         AND invoice_status = 'Paid'  
                         AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         )
           / 
           (SELECT
                 COUNT(DISTINCT sm_group_working_day) AS total
                 FROM ep_yearly_calendar 
                 WHERE 
                 YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
                 AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           )	
         ) AS total 
         FROM DUAL 

         UNION

         SELECT 
          CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') , ' : ' ) AS name, 
          'working_day' AS code_name,
          9 AS code_seq,
          COUNT(DISTINCT sm_group_working_day) AS total
         FROM ep_yearly_calendar 
          WHERE 
          YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
          AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
          AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y')) 

         UNION 

         SELECT CONCAT('Target Training ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')), ' (RM) : ' ) AS name,  
          'trg_year_target' AS code_name,
          10 AS code_seq,
          SUM(amount) AS total  
         FROM ep_ref_target  
          WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
          AND code_name = CONCAT('TOTAL_TRAINING_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  

         UNION

         SELECT CONCAT('Target Training As Of ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'), ' (RM) : ' ) AS name,  
          'sr_monthly_target' AS code_name,
          11 AS code_seq,
          SUM(amount) AS total  
         FROM ep_ref_target  
          WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
          AND code_name = CONCAT('TOTAL_TRAINING_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  
          AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))

         UNION 

         SELECT CONCAT('Actual Training To Date ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' (RM) : ') AS name, 
          'trg_todate_revenue' AS code_name,
          12 AS code_seq,
          SUM(unit_price)
         FROM crm_invoice 
         WHERE invoice_status = 'Paid' 
         AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH(invoice_closed) <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'

         UNION 

         SELECT CONCAT('Actual No. Training To Date ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') ,' : ') AS name, 
          'trg_todate_total_item' AS code_name,
          13 AS code_seq,
         SUM(product_qty) 
         FROM crm_invoice 
         WHERE invoice_status = 'Paid' 
         AND YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND MONTH(invoice_closed) <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND product_category_name IN ('Training') AND product_name <> 'Elite_CatDP_QT'

        ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result; 
    }
    
    /**
     * Revenue Softcert
     * @param type $dateSearch
     * @return type
     */
    public function cmsListSummarySmSoftcertDailyByDate($dateSearch) {
		$crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
        $year = $crbnDate->year;
        $q = "
        /*
         SOFTCERT COLLECTION
         Softcert 02 Jun	: 1.20k
         Softcert Week 23 : 2.64k
         Softcert Target Jun : 13.00k
         Softcert Actual Jun : 2.64k
         Variance : -10.36k
         */
         -- SET '$dateSearch' = '31-05-2020';

         SELECT CONCAT('Softcert ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS name,
         'daily_revenue' AS code_name,
         1 AS code_seq,
         SUM(payment_amount) AS total
         FROM ep_supplier_payment WHERE 
         DATE(payment_date) = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND bill_type = 'S' 
         AND receipt_no IS NOT NULL 

         UNION

         SELECT CONCAT('Softcert Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS name,
         'daily_workingday_revenue' AS code_name,
         1.1 AS code_seq,
         SUM(payment_amount) AS total
         FROM ep_supplier_payment WHERE   
         DATE(payment_date)  IN  (
                 SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                         SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
         )
         AND YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND bill_type = 'S' 
         AND receipt_no IS NOT NULL 


         UNION 

         SELECT CONCAT('Softcert Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS name, 
         'weekly_revenue' AS code_name,
         2 AS code_seq,
         SUM(p.payment_amount) AS total
         FROM ep_supplier_payment p, ep_yearly_calendar cal WHERE 
         cal.date = DATE(p.payment_date)
         AND YEAR(payment_date) = $year
         AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
         AND MONTH(p.payment_date) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
   AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND p.bill_type = 'S'  
         AND receipt_no IS NOT NULL 

         UNION 

         SELECT CONCAT('Target ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH,'-1')),'%b') , ' (RM) : ') AS name,  
         'monthly_target' AS code_name,
         3 AS code_seq,
         amount AS total  
         FROM ep_ref_target 
         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND code_name = CONCAT('TARGET_SOFTCERT_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  	

         UNION 

         SELECT CONCAT('Actual ',DATE_FORMAT(payment_date,'%b') , ' (RM) : ') AS name, 
         'monthly_revenue' AS code_name,
         4 AS code_seq,
         SUM(payment_amount) AS total
         FROM ep_supplier_payment WHERE 
         YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND DATE(payment_date)  <=  DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND bill_type = 'S'  
         AND receipt_no IS NOT NULL 

         UNION 

         SELECT 'Variance (RM) : ' AS name, 
         'monthly_variance' AS code_name,
         5 AS code_seq,
         (
          (SELECT 
                 SUM(payment_amount) AS total
                 FROM ep_supplier_payment 
                 WHERE 
                 YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                 AND bill_type = 'S' 
                 AND receipt_no IS NOT NULL )
           - 
           (SELECT 
                 amount AS total  
                 FROM ep_ref_target 
                 WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                 AND code_name = CONCAT('TARGET_SOFTCERT_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  )		
         ) AS total 
         FROM DUAL 

         UNION 

         SELECT 'Daily Target To Achieve (RM) : ' AS name, 
         'daily_target_archive' AS code_name,
         6 AS code_seq,
          ABS( 
          (SELECT  
                 (
                  (SELECT 
                         SUM(payment_amount) AS total
                         FROM ep_supplier_payment 
                         WHERE 
                         YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         AND bill_type = 'S' 
                         AND receipt_no IS NOT NULL )
                   - 
                   (SELECT 
                         amount AS total  
                         FROM ep_ref_target 
                         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                         AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                         AND code_name = CONCAT('TARGET_SOFTCERT_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  )		
                 ) AS total 
                 FROM DUAL )	
           / 
           (SELECT 
                 COUNT(DISTINCT sm_group_working_day) AS total
                 FROM ep_yearly_calendar c
       WHERE
       c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
       AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
       AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
       WHERE d.year = c.year AND d.month = c.month 
       AND d.sm_group_working_day IN 
           ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
             WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           )
         )
           )	
         ) AS total 
         FROM DUAL 

         UNION 

         SELECT 
          CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') , ' ; ' ) AS name, 
         'day_left' AS code_name,
         7 AS code_seq,
         COUNT(DISTINCT sm_group_working_day) AS total
         FROM ep_yearly_calendar c
         WHERE 
         c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
           AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                             WHERE d.year = c.year AND d.month = c.month 
     AND d.sm_group_working_day IN 
                                 ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                                   WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                 )
                         )

         UNION 

         SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS name, 
         'daily_average_collection' AS code_name,
         8 AS code_seq,
          ABS( 
          (SELECT 
                 SUM(payment_amount) AS total
                 FROM ep_supplier_payment 
                 WHERE 
                 YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH(payment_date)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                 AND bill_type = 'S' 
                 AND receipt_no IS NOT NULL ) 	
           / 
           (SELECT
                 COUNT(DISTINCT sm_group_working_day) AS total
                 FROM ep_yearly_calendar 
                 WHERE 
                 YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                 AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
                 AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
           )	
         ) AS total 
         FROM DUAL 

         UNION

         SELECT 
          CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'),' : '  ) AS name, 
         'working_day' AS code_name,
         9 AS code_seq,
         COUNT(DISTINCT sm_group_working_day) AS total
         FROM ep_yearly_calendar 
         WHERE 
         YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
         AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))

         UNION 

         SELECT CONCAT('Target Softcert ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS name,  
         'yearly_target' AS code_name,
         10 AS code_seq,
         SUM(amount) AS total  
         FROM ep_ref_target 
         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND code_name = CONCAT('TARGET_SOFTCERT_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  	

         UNION 

         SELECT CONCAT('Target Softcert As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS name,  
         'monthly_accumulate_target' AS code_name,
         11 AS code_seq,
         SUM(amount) AS total  
         FROM ep_ref_target 
         WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND code_name = CONCAT('TARGET_SOFTCERT_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  	

         UNION 

         SELECT CONCAT('Actual Softcert To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS name, 
         'actual_todate_revenue' AS code_name,
         12 AS code_seq,
         SUM(payment_amount) AS total
         FROM ep_supplier_payment 
         WHERE YEAR(payment_date)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
         AND MONTH(payment_date)  <=  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
         AND bill_type = 'S' 
         AND receipt_no IS NOT NULL

        ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result; 
    }

    /**
     * Revenue Softcert
     * @param type $dateSearch
     * @return type
     */
    public function cmsListPendingTransactionStatistic($year) {
          $q = "
          SELECT *, 'Pending' AS subject_name FROM 
              (
                SELECT COUNT(*) AS total_prcr_created, 
                  SUM(fl_total_amount) AS pv_prcr_created,
                  (SUM(fl_total_amount) * 0.004)  AS tr_prcr_created  
                  FROM ep_fulfilment_dtl_$year   
                  WHERE fl_request_latest_status NOT IN (
                    'Cancelled',
                    'Cancelled by System',
                    'Cancelled Due To Ministry Restructuring',
                    'Cancelled by YEP',
                    'Cancelled by Year End Process',
                    'Rejected'
                  )
                  AND fl_doc_no IS NULL 
                  AND fl_financial_year = $year
              ) AS prcr_created,
              (
                SELECT COUNT(*) AS total_pend_supp_ack , 
                  SUM(fl_total_amount) AS pv_pend_supp_ack ,
                  (SUM(fl_total_amount) * 0.004)  AS tr_pend_supp_ack  
                  FROM ep_fulfilment_dtl_$year   
                  WHERE fl_latest_status = 'Pending Supplier Acknowledgement' 
                  AND fl_financial_year = $year
              ) AS pend_supp_ack ,
              (
                SELECT COUNT(*) AS total_pend_do , 
                  SUM(total_amount) AS pv_pend_do ,
                  (SUM(total_amount) * 0.004)  AS tr_pend_do  
                  FROM `ep_fulfilment_pending_grn`   
                  WHERE do_id IS NULL AND financial_year = $year
              ) AS pend_do,
              (
                SELECT COUNT(*) AS total_pend_grn , 
                  SUM(total_amount) AS pv_pend_grn ,
                  (SUM(total_amount) * 0.004)  AS tr_pend_grn  
                  FROM `ep_fulfilment_pending_grn`   
                  WHERE do_id IS NOT NULL AND financial_year = $year
              ) AS pend_grn,
              (
                SELECT COUNT(*) AS total_pend_invoice, 
                  SUM(fl_total_amount) AS pv_pend_invoice, 
                  SUM(IF(fl_total_cdc_fee_amount IS NULL, (fl_total_amount * 0.004), fl_total_cdc_fee_amount)) AS tr_pend_invoice 
                  FROM ep_fulfilment_dtl_$year   
                  WHERE fl_latest_status IN (
                    'Pending Invoice'
                  ) AND fl_financial_year = $year
              ) AS pend_invoice,
              (
                SELECT COUNT(*) AS total_pend_payment_match, 
                  SUM(fl_total_amount) AS pv_pend_payment_match , 
                  SUM(fl_total_cdc_fee_amount) AS tr_pend_payment_match 
                  FROM ep_fulfilment_dtl_$year   
                  WHERE fl_latest_status IN (
                    'Pending Payment Instruction Query Verification',
                    'Pending Payment Instruction Query from 1GFMAS',
                    'Pending Debit Note Approval',
                    'Pending Payment Match',
                    'Awaiting 1GFMAS Response'  
                  ) AND fl_financial_year = $year
              ) AS pend_payment_match 
            
              UNION ALL 

                      
              SELECT *, 'Convert' AS subject_name FROM 
                            (
                              SELECT COUNT(*) AS total_prcr_created, 
                                SUM(fl_total_amount) AS pv_prcr_created,
                                (SUM(fl_total_amount) * 0.004)  AS tr_prcr_created  
                                FROM ep_fulfilment_dtl_$year  
                                WHERE fl_request_latest_status NOT IN (
                                  'Cancelled',
                                  'Cancelled by System',
                                  'Cancelled Due To Ministry Restructuring',
                                  'Cancelled by YEP',
                                  'Cancelled by Year End Process'
                                )
                                AND fl_doc_no IS NULL 
                                AND fl_financial_year = $year
                                AND DATE(fl_request_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS prcr_created,
                            (
                              SELECT COUNT(*) AS total_pend_supp_ack , 
                                SUM(fl_total_amount) AS pv_pend_supp_ack ,
                                (SUM(fl_total_amount) * 0.004)  AS tr_pend_supp_ack  
                                FROM ep_fulfilment_dtl_$year  
                                WHERE fl_latest_status = 'Pending Supplier Acknowledgement' 
                                AND fl_financial_year = $year 
                                AND DATE(fl_latest_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS pend_supp_ack ,
                            (
                              SELECT COUNT(*) AS total_pend_do , 
                                SUM(total_amount) AS pv_pend_do ,
                                (SUM(total_amount) * 0.004)  AS tr_pend_do  
                                FROM `ep_fulfilment_pending_grn`   
                                WHERE do_id IS NULL AND financial_year = $year
                                AND DATE(order_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS pend_do,
                            (
                              SELECT COUNT(*) AS total_pend_grn , 
                                SUM(total_amount) AS pv_pend_grn ,
                                (SUM(total_amount) * 0.004)  AS tr_pend_grn  
                                FROM `ep_fulfilment_pending_grn`   
                                WHERE do_id IS NOT NULL AND financial_year = $year
                                AND DATE(do_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS pend_grn,
                            (
                              SELECT COUNT(*) AS total_pend_invoice, 
                                SUM(fl_total_amount) AS pv_pend_invoice, 
                                SUM(IF(fl_total_cdc_fee_amount IS NULL, (fl_total_amount * 0.004), fl_total_cdc_fee_amount)) AS tr_pend_invoice 
                                FROM ep_fulfilment_dtl_$year  
                                WHERE fl_latest_status IN (
                                  'Pending Invoice'
                                ) AND fl_financial_year = $year
                                 AND DATE(fl_latest_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS pend_invoice,
                            (
                              SELECT COUNT(*) AS total_pend_payment_match, 
                                SUM(fl_total_amount) AS pv_pend_payment_match , 
                                SUM(fl_total_cdc_fee_amount) AS tr_pend_payment_match 
                                FROM ep_fulfilment_dtl_$year  
                                WHERE fl_latest_status IN (
                                  'Pending Payment Instruction Query Verification',
                                  'Pending Payment Instruction Query from 1GFMAS',
                                  'Pending Debit Note Approval',
                                  'Pending Payment Match',
                                  'Awaiting 1GFMAS Response'  
                                ) AND fl_financial_year = $year
                                AND DATE(fl_latest_status_created_date) =  DATE_SUB(CURDATE(), INTERVAL 1 DAY) 
                            ) AS pend_payment_match 
            
             ";
          //dump($q);
          $result = DB::connection('mysql_cms')->select($q);
  
          return $result; 
      }
      
      public function cmsListSummarySmMembershipDailyByDate($dateSearch) {
          $crbnDate = Carbon::createFromFormat('d-m-Y', $dateSearch);
         $year = $crbnDate->year;
        $q = " 		SELECT CONCAT('EM ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS 'name',
                'daily_revenue' AS code_name,
                1 AS code_seq, 
                SUM(sum_amount_before_tax) AS total 
                FROM crm_invoice
                WHERE invoice_closed = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')               

                UNION

                SELECT CONCAT('EM No. ',DATE_FORMAT(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'%d %b'), ' (RM) : ') AS 'name',
                'daily_qty_revenue' AS code_name,
                2 AS code_seq, 
                SUM(product_qty) AS total 
                FROM crm_invoice
                WHERE invoice_closed = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                
                UNION 

                SELECT CONCAT('EM Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS 'name',
                'daily_workingday_revenue' AS code_name,
                3 AS code_seq,
                SUM(sum_amount_before_tax) AS total 
                FROM crm_invoice
                WHERE invoice_closed IN (
                        SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                                SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
                         )
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')                

                UNION 

                SELECT CONCAT('EM No. Working Days ',(SELECT DISTINCT CONCAT(sm_group_working_day,' ',MONTH) FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) , ' (RM) : ') AS 'name',
                'daily_qty_workingday_revenue' AS code_name,
                4 AS code_seq,
                SUM(product_qty) AS total  
                FROM crm_invoice
                WHERE invoice_closed IN (
                        SELECT DATE FROM ep_yearly_calendar WHERE sm_group_working_day = (
                                SELECT sm_group_working_day FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                ) AND YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  
                         )
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                
                UNION

                SELECT CONCAT('EM Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS 'name', 
                'weekly_revenue' AS code_name,
                5 AS code_seq,
                SUM(sum_amount_before_tax) AS total 
                FROM crm_invoice p, ep_yearly_calendar cal WHERE 
                cal.date = DATE(p.invoice_closed)
                AND YEAR(invoice_closed) = $year
                AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
                AND MONTH(p.invoice_closed) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT') 
                AND invoice_status = 'Paid' 
                
                UNION

                SELECT CONCAT('EM No. Week ',(SELECT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ), ' (RM) : ') AS 'name', 
                'weekly_qty_revenue' AS code_name,
                6 AS code_seq,
                SUM(product_qty) AS total 
                FROM crm_invoice p, ep_yearly_calendar cal WHERE 
                cal.date = DATE(p.invoice_closed)
                AND YEAR(invoice_closed) = $year
                AND cal.week = (SELECT DISTINCT WEEK FROM ep_yearly_calendar WHERE DATE = DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')) ) 
                AND MONTH(p.invoice_closed) = MONTH(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))
                AND cal.date <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT') 
                AND invoice_status = 'Paid' 
                
                UNION

                SELECT CONCAT('EM Target ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH,'-1')),'%b') , ' (RM) : ') AS 'name',  
                'monthly_target' AS code_name,
                7 AS code_seq,
                amount AS total  
                FROM ep_ref_target 
                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND code_name = CONCAT('TOTAL_MEMBERSHIP_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))

                UNION

                SELECT CONCAT('EM Actual ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  , ' (RM) : ') AS 'name', 
                'monthly_revenue' AS code_name,
                8 AS code_seq,
                SUM(sum_amount_before_tax) AS total 
                FROM crm_invoice
                WHERE YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                
                UNION

                SELECT CONCAT('EM No. Actual ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')  , ' (RM) : ') AS 'name', 
                'monthly_qty_revenue' AS code_name,
                9 AS code_seq,
                SUM(product_qty) AS total 
                FROM crm_invoice
                WHERE YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                 
                UNION

                SELECT 'Variance (RM) : ' AS 'name', 
                'monthly_variance' AS code_name,
                10 AS code_seq,
                         (
                          (SELECT SUM(sum_amount_before_tax) AS total 
                                FROM crm_invoice
                                WHERE YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT'))
                           - 
                           (SELECT amount AS total  
                                FROM ep_ref_target 
                                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND code_name = CONCAT('TOTAL_MEMBERSHIP_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))))	
                         ) AS total 
                        FROM DUAL

                UNION

                SELECT 'Daily Target To Achieve (RM) : ' AS 'name', 
                'daily_target_archive' AS code_name,
                11 AS code_seq,
                ABS( (SELECT  (
                        (SELECT SUM(sum_amount_before_tax) AS total 
                                FROM crm_invoice WHERE YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT'))
                        - 
                        (SELECT amount AS total  
                                FROM ep_ref_target 
                                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND MONTH = MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND code_name = CONCAT('TOTAL_MEMBERSHIP_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))))			
                        ) AS total  FROM DUAL )	
                        / 
                        (SELECT COUNT(DISTINCT sm_group_working_day) AS total
                                FROM ep_yearly_calendar c
                                WHERE c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
                                AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                                WHERE d.year = c.year AND d.month = c.month 
                                AND d.sm_group_working_day IN 
                                   ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                                     WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                   ) )
                           ) ) AS total  FROM DUAL

                UNION

                SELECT CONCAT('Day(s) Left  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') , ' ; ' ) AS 'name', 
                'day_left' AS code_name,
                12 AS code_seq,
                COUNT(DISTINCT sm_group_working_day) AS total
                FROM ep_yearly_calendar c WHERE  c.YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND c.MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b')
                AND c.DATE > ( SELECT MAX(DATE) FROM ep_yearly_calendar d 
                WHERE d.year = c.year AND d.month = c.month 
                AND d.sm_group_working_day IN ( SELECT sm_group_working_day FROM ep_yearly_calendar d 
                        WHERE DATE = (STR_TO_DATE('$dateSearch','%d-%m-%Y')) ))

                UNION

                SELECT CONCAT('Average Daily Collection (01',IF(DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%d') > 1,DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'- %d %b)'),''), ' (RM) : ' )  AS 'name', 
                'daily_average_collection' AS code_name,
                13 AS code_seq,
                ABS( 
                          (SELECT SUM(sum_amount_before_tax) AS total 
                                FROM crm_invoice WHERE YEAR(invoice_closed)  =  YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND MONTH(invoice_closed)  =  MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                AND DATE(invoice_closed) <= DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')) 	
                           / 
                           (SELECT
                                 COUNT(DISTINCT sm_group_working_day) AS total
                                 FROM ep_yearly_calendar 
                                 WHERE 
                                 YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                                 AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
                                 AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                           )	
                         ) AS total 
                         FROM DUAL

                UNION

                SELECT  CONCAT('No of Working Day(s)  ',DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b'),' : '  ) AS 'name', 
                'working_day' AS code_name,
                14 AS code_seq,
                COUNT(DISTINCT sm_group_working_day) AS total
                FROM ep_yearly_calendar 
                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH = DATE_FORMAT(STR_TO_DATE('$dateSearch','%d-%m-%Y'),'%b') 
                AND DATE <= (STR_TO_DATE('$dateSearch','%d-%m-%Y'))

                UNION

                SELECT CONCAT('Target EM ',YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) , ' (RM) : ' ) AS 'name',  
                'yearly_target' AS code_name,
                15 AS code_seq,
                SUM(amount) AS total  
                FROM ep_ref_target 
                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND code_name = CONCAT('TOTAL_MEMBERSHIP_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y'))))  

                UNION

                SELECT CONCAT('Target EM As Of ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b'), ' (RM) : ' ) AS NAME,  
                'monthly_accumulate_target' AS code_name,
                16 AS code_seq,
                SUM(amount) AS total  
                FROM ep_ref_target 
                WHERE YEAR = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y'))
                AND code_name = CONCAT('TOTAL_MEMBERSHIP_',YEAR(DATE(STR_TO_DATE('$dateSearch','%d-%m-%Y')))) 

                UNION 

                SELECT CONCAT('Actual EM To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS 'name', 
                'actual_todate_revenue' AS code_name,
                13 AS code_seq,
                SUM(sum_amount_before_tax) AS total 
                FROM crm_invoice
                WHERE YEAR(invoice_closed) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH(invoice_closed) <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                
                UNION

                SELECT CONCAT('Actual No. EM To Date ',DATE_FORMAT(DATE(CONCAT(YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-',MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')),'-1')),'%b') , ' (RM) : ') AS 'name', 
                'actual_qty_todate_revenue' AS code_name,
                14 AS code_seq,
                SUM(product_qty) AS total 
                FROM crm_invoice
                WHERE YEAR(invoice_closed) = YEAR(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND MONTH(invoice_closed) <= MONTH(STR_TO_DATE('$dateSearch','%d-%m-%Y')) 
                AND invoice_status = 'Paid' AND (product_category_name = 'Membership' OR product_name = 'Elite_CatDP_QT')
                ";
        //dump($q);
        $result = DB::connection('mysql_cms')->select($q);

        return $result;
      }
}
