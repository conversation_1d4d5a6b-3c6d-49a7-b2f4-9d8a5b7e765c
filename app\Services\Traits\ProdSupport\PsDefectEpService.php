<?php

namespace App\Services\Traits\ProdSupport;

use Log;
use DB;
use Carbon\Carbon;
use Hash;

trait PsDefectEpService {

    protected function getDetailDefectTesting() {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' order by urgent_testing desc;
                ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailDefectTestingWithClosedStatus() {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status = '12 - Closed';
                ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailDefectTestingForDownload() {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select *,pdd.created_by as tester,  DATEDIFF(test_end_date, test_start_date) + 1 as masa
           from ps_defect_ep pde, ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id
           order by pde.defect_id 
                ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailDefectDateTesting() {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd 
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_date pdd , ps_defect_ep pde where pde.defect_id = pdd.defect_id;
                ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailDefectDateTestingMax($defect_id_fk) {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select max(seq_id) as seq from ps_defect_date where defect_id = ?;
                ", array($defect_id_fk));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listRedmineStatusDetails() {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select distinct redmine_status from ps_defect_ep where redmine_status != '12 - Closed' order by redmine_status;
                ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function checkingExistingRedmine($redmine) {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select distinct redmine_no from ps_defect_ep where redmine_no = ?;
                ", array($redmine));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailsByStatus($status, $carian, $deploy) {
        $query = null;

        if ($status == 'ALL' && $carian != null && $carian != 'ALL' && $deploy == 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and test_status = ? order by urgent_testing desc;
                ", array($carian));
        } else if ($status != null && $status != 'ALL' && $carian == 'ALL' && $deploy == 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and redmine_status = ? order by urgent_testing desc;
                ", array($status));
        } else if ($status == 'ALL' && $carian == 'ALL' && $deploy != null && $deploy != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and deploy_status = ? order by urgent_testing desc;
                ", array($deploy));
        } else if ($status != null && $status != 'ALL' && $carian != null && $carian != 'ALL' && $deploy != null && $deploy != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and redmine_status = ? and test_status = ? and deploy_status = ? order by urgent_testing desc;
                ", array($status, $carian, $deploy));
        } else if ($status != null && $status != 'ALL' && $carian != null && $carian != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and redmine_status = ? and test_status = ? order by urgent_testing desc;
                ", array($status, $carian));
        } else if ($carian != null && $carian != 'ALL' && $deploy != null && $deploy != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and test_status = ? and deploy_status = ? order by urgent_testing desc;
                ", array($carian, $deploy));
        } else if ($status != null && $status != 'ALL' && $deploy != null && $deploy != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' and redmine_status = ? and deploy_status = ? order by urgent_testing desc;
                ", array($status, $deploy));
        } else {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status != '12 - Closed' order by urgent_testing desc;
                ");
        }
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailsByStatusClosed($carian) {
        $query = null;

        if ($carian != null && $carian != 'ALL') {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status = '12 - Closed' and test_status = ? ;
                ", array($carian));
        } else {
            $query = DB::connection('mysql_ep_prod_support')->select("
            select *, (select count(pdd.defect_id) 
           from ps_defect_date pdd
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_ep pde where redmine_status = '12 - Closed';
                ");
        }
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailEditDefectTesting($defectId) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_defect_ep')
                        ->where('defect_id', $defectId)
                        ->first();
    }

    protected function getDetailDateTesting($defectdateid) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_defect_date')
                        ->where('defect_date_id', $defectdateid)
                        ->first();
    }

    protected function getDetailDateAllTesting($defectid) {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select *,pdd.created_by as created_date_by, (select count(pdd.defect_id) 
           from ps_defect_date pdd 
           where pdd.test_end_date IS NOT NULL 
           and pde.defect_id = pdd.defect_id ) as counttest from ps_defect_date pdd , ps_defect_ep pde where pde.defect_id = pdd.defect_id and pdd.defect_id = ? order by pdd.seq_id;
                ", array($defectid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountTestingPerRedmine($defectid) {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select created_by, sum(DATEDIFF(test_end_date, test_start_date) + 1)  as Testing
           from ps_defect_date pdd 
           where pdd.test_end_date IS NOT NULL 
           and  pdd.defect_id = ? 
           group by pdd.created_by;
                ", array($defectid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

}
