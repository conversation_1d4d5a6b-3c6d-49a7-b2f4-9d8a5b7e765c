<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmDashboard;

use App\Http\Controllers\Controller;
use Log;
use App\Services\CRMService;
use Carbon\Carbon;
use DateTime;

class CustomerService extends Controller {

    static $url = 'https://epss.eperolehan.gov.my/support/crm/case?case_number';
    
    public static function crmService() {
        return new CRMService;
    }

    public function getDashboardCS() {
        return view('crmdashboard.dashboard_cs', []);
    }

    public function getDashboardCSPendInput() {
        $totalAllEmail = 0;
        $totalPendActionEmail = 0;
        $totalPendMoreInfoEmail = 0;
        $totalAllOP = 0;
        $totalPendActionOP = 0;
        $totalPendMoreInfoOP = 0;

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered' >
                <thead>                  
                   </tr>
                    <tr>
                        <th><center>Contact Mode</center></th>
                        <th><center>Total Cases</center></th>     
                        <th><center>Pending Input</center></th>     
                        <th><center>Pending More Info</center></th>     
                    </tr>
                </thead>
                <tbody>";

        $listCase = self::crmService()->getDashboardCS();
        if (count($listCase) > 0) {
            foreach ($listCase as $data) {
                if ($data->contact_mode === 'Email') {
                    if ($data->case_info === 'info_notcompleted') {
                        $totalPendMoreInfoEmail++;
                    } else {
                        $totalPendActionEmail++;
                    }
                    $totalAllEmail = $totalPendMoreInfoEmail + $totalPendActionEmail;
                } else {
                    if ($data->case_info === 'info_notcompleted') {
                        $totalPendMoreInfoOP++;
                    } else {
                        $totalPendActionOP++;
                    }
                    $totalAllOP = $totalPendMoreInfoOP + $totalPendActionOP;
                }
            }
        }


        $html .= "<tr>
            <td style='width: 10%;' align='center'><strong>Email</strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalAllEmail} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Email/all'
                            data-title='Total Cases (Email)'><strong>{$totalAllEmail}</strong></a></strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalPendActionEmail} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Email/action'
                            data-title='Total Cases With Pending Action (Email)'><strong>{$totalPendActionEmail}</strong></a></strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalPendMoreInfoEmail} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Email/info'
                            data-title='Total Cases With Pending More Info (Email)'><strong>{$totalPendMoreInfoEmail}</strong></a></strong></td>
         </tr>
         <tr>
            <td style='width: 10%;' align='center'><strong>Open Portal</strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalAllOP} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Open Portal/all'
                            data-title='Total Cases (Email)'><strong>{$totalAllOP}</strong></a></strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalPendActionOP} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Open Portal/action'
                            data-title='Total Cases (Email)'><strong>{$totalPendActionOP}</strong></a></strong></td>
            <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$totalPendMoreInfoOP} btn-green btn-sm shadow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/dashboard/crm/cs/pendinginput/Open Portal/info'
                            data-title='Total Cases (Email)'><strong>{$totalPendMoreInfoOP}</strong></a></strong></td>
         </tr></tbody> </table> </div>";
        return $html;
    }

    public function getDashboardCSPendInputDetail($contactMode,$info) {

        $listCase = self::crmService()->getDashboardCS($contactMode,$info);  
        
        $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');

        $html = "";
        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>&nbsp;</th>
                        <th class='text-center'>Case Number</th>
                        <th class='text-center'>Aging (Days)</th>
                        <th class='text-center'>Created Date</th>
                        <th class='text-center'>Modified Date</th>
                        <th class='text-center'>State</th>
                        <th class='text-center'>Status</th>
                        <th class='text-center'>Contact Mode</th>
                        <th class='text-center'>Case Information</th> 
                    </tr>
                </thead><tbody>";
        
        $counter = 0; 
        $listA = array(NULL,'');
        foreach ($listCase as $value) { 
            $createdDate = Carbon::parse($value->created_date)->addHour(8)->format("Y-m-d H:i:s");
            $modifiedDate = Carbon::parse($value->modified_date)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now(); 
            if (in_array($value->case_status, $open)) {
                $ageing = $current->diff(new DateTime($createdDate));
            } else {
                $ageing = $modifiedDate->diff(new DateTime($createdDate));
            }
            
            if($info === 'action') {
                if (in_array($value->case_info, $listA)) {
                    $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->case_number' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$createdDate</strong></td>
                        <td class='text-left'><strong>$modifiedDate</strong></td>
                        <td class='text-left'><strong>$value->case_state</strong></td>
                        <td class='text-left'><strong>$value->case_status</strong></td>
                        <td class='text-left'><strong>$value->contact_mode</strong></td>
                        <td class='text-left'><strong>$value->case_info</strong></td>
                    </tr>";
                    $html = $html . $data; 
                }
            }else {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->case_number' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$createdDate</strong></td>
                        <td class='text-left'><strong>$modifiedDate</strong></td>
                        <td class='text-left'><strong>$value->case_state</strong></td>
                        <td class='text-left'><strong>$value->case_status</strong></td>
                        <td class='text-left'><strong>$value->contact_mode</strong></td>
                        <td class='text-left'><strong>$value->case_info</strong></td>
                    </tr>";
                    $html = $html . $data; 
            }
             
        }
        $html = $html . "<tbody>";
        return $html;
    }

}
