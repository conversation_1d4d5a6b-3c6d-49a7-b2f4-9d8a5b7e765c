<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/14/2018
 * Time: 12:32 PM
 */

namespace App\Http\Controllers;

use App\Services\Traits\OrganizationService;


class EpOrganizationController extends Controller
{
    use OrganizationService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function searchByOrgCode($orgCode)
    {
        if(strlen($orgCode) == 12){
            return redirect('/find/org/icno/'.$orgCode);  
        }
        $org_code_type = 'org_code';
        $data = $this->getPmOrganization($org_code_type,$orgCode);
        $this->setPmOrgHierarchy($data);

        if (count($data) > 0) {
            $orgProfileId =  $data[0]->org_profile_id;
            $org_profile_type = 'org_profile_id';
            $list = $this->getDetailUserList($org_profile_type, $orgProfileId);
            $resultCount = $this->getListDeliveryAddressByPtj("COUNT", $orgProfileId);
            $totalDeliveryAddress = $resultCount[0]->total;

            return view('user_details_orgcode', [
                'orginfo' => $data,
                'listdata' => $list,
                'totalDeliveryAddress' => $totalDeliveryAddress,
                'carian' => $orgCode,
                'type' => 'orgcode']);
        }
        return view('user_details_orgcode', [
            'orginfo' => null,
            'listdata' => null,
            'totalDeliveryAddress' => 0,
            'result' => 'notfound',
            'carian' => $orgCode,
            'type' => 'orgcode']);

    }

    public function searchByIdentificationNo($icNo)
    {
        $type = 'identification_no';
        $list = $this->getDetailUserList($type,$icNo);
     
        if (count($list) > 0) {
            
            //Detect Found as Supplier User
            if($list[0]->org_type_id == 15){
                return redirect('/find/icno/'.$icNo);
            }
            
            $orgProfileId =  $list[0]->org_profile_id;
            $type = 'org_profile_id';
            $orginfo = $this->getPmOrganization($type,$orgProfileId);
            $this->setPmOrgHierarchy($orginfo);
       
            $resultCount = $this->getListDeliveryAddressByPtj("COUNT", $orgProfileId);
            
            $totalDeliveryAddress = $resultCount[0]->total;
            
            return view('user_details_orgcode', [
                'orginfo' => $orginfo,
                'listdata' => $list,
                'totalDeliveryAddress' => $totalDeliveryAddress,
                'carian' => $icNo,
                'type' => 'icno']);
        }
        return view('user_details_orgcode', [
            'orginfo' => null,
            'listdata' => null,
            'result' => 'notfound',
            'totalDeliveryAddress' => 0,
            'carian' => $icNo,
            'type' => 'icno']);
    }


    public function getDetailUserList($type,$value)
    {
        
        
        $list = $this->getUserList($type,$value);
        if (count($list) > 0) {
            foreach ($list as $data) {
                $listUserRole = $this->getUserRole($data->user_id);
                $isAdmin = '';
                $totalDeliveryAddressOfficer = 0;
                $isDeliveryAddressOfficer = false;
                foreach ($listUserRole as $userRole) {
                    $roleDescBM = $this->getRoleDesc($userRole->role_code, 'ms');
                    if ($roleDescBM) {
                        $userRole->role_name = $userRole->role_name . ' , ' . $roleDescBM->role_name;
                    }

                    if ($userRole->role_code == 'PTJ_ADMIN' || $userRole->role_code == 'MINISTRY_ADMIN' || $userRole->role_code == 'JABATAN_ADMIN') {
                        $isAdmin = $roleDescBM->role_code . ' , ' . $roleDescBM->role_name;
                    }
                    
                    if($userRole->role_code == 'RECEIVING_OFFICER' ){
                        $isDeliveryAddressOfficer = true;
                        $result = $this->getListReceivingAddressByUser("COUNT", $data->org_profile_id, $data->identification_no);
                        $totalDeliveryAddressOfficer = $result[0]->total;
                    }
                    
                    if($userRole->approval_limit != null && $userRole->approval_limit > 0){
                        $userRole->approval_limit = '(RM'.$userRole->approval_limit.')';
                    }
                }

                // gpki roles 
                $gpkiRole = collect(['CT_APPROVER','BPK_CC_APPROVER','RN_APPROVER','FL_APPROVER','ACKNOWLEDGE_OFFICER','PAY_OFFICER','BPK_CQ_APPROVER','BPK_PQ_APPROVER','COMMITTEE_APPROVER','PUBLICATION_APPROVER','SC_CHAIRPERSON','EC_CHAIRPERSON','OC_CHAIRPERSON','TEC_CHAIRPERSON','FEC_CHAIRPERSON','QCA_CHAIRPERSON','QCB_CHAIRPERSON','PPB_CHAIRPERSON','FPB_CHAIRPERSON','FPB_CHAIRPERSON']);
           
                $isGpkiValid = false;
                $isGpkiValid = collect($listUserRole)->contains(function ($value, $key)  use ( $gpkiRole) {
                    return $gpkiRole->contains($value->role_code);
                });
                $data->is_gpki_user = $isGpkiValid;

                $data->role_admin = $isAdmin;
                $data->isDeliveryAddressOfficer = $isDeliveryAddressOfficer;
                $data->totalDeliveryAddressOfficer = $totalDeliveryAddressOfficer;
                
                
                $data->listUserRole = $listUserRole;

                $listApprover = $this->getPmUserGroupByUserId($data->user_id);
                $data->listApprover = $listApprover;
            }
        }
        return $list; 
    }
    

    /**
     * Set hierarchy for Org Gov profile.  KEMENTERIAN >> PEGAWAI PENGAWAL >> KUMPULAN PTJ >> PTJ
     * @param type $data
     * @return type
     */
    public function setPmOrgHierarchy(&$data)
    {
        if (count($data) > 0) {
            if ($data[0]->parent_org_profile_id) {
                $orgProfileId = $data[0]->parent_org_profile_id;
                $hierarchy = array();
                for ($x = 0; $x < $data[0]->org_type_id - 2; $x++) {
                    if ($x == 0) {
                        ${"id" . $x} = $this->getPmOrgValidityByParentId($orgProfileId);
                    } else {
                        ${"id" . $x} = $this->getPmOrgValidityByParentId(${"id" . ($x - 1)}[0]->parent_org_profile_id);
                    }
                    $hierarchy[] = array('org_name' => ${"id" . $x}[0]->org_name, 'org_code' => ${"id" . $x}[0]->org_code,'org_type' => ${"id" . $x}[0]->code_desc );
                }
                $data[0]->hierarchy = array_reverse($hierarchy);
            } else {
                $data[0]->hierarchy = null;
            }
        }
        return $data;
    }

    public function searchByOrgCodeDefault()
    {
        return view('user_details_orgcode', [
            'orginfo' => null,
            'type' => 'orgcode',
            'result' => null,
            'carian' => '']);
    }

    public function searchByIdentificationNoDefault()
    {
        return view('user_details_orgcode', [
            'orginfo' => null,
            'type' => 'icno',
            'result' => null,
            'carian' => '']);
    }
    
    public function searchDeliveryAddressByOrgProfileId($orgProfileId){
        $list_data = $this->getListDeliveryAddressByPtj("SELECT", $orgProfileId);
        return view('include.list_address', [
            'addresses' => $list_data]);
    }
    public function searchReceivingAddress($orgProfileId,$icno){
        $list_data = $this->getListReceivingAddressByUser("SELECT", $orgProfileId, $icno);
        return view('include.list_address', [
            'addresses' => $list_data]);
    }
    
    public function searchUserGroupOrg($orgProfileId){
        $list_data = $this->getPmUserGroupByOrgProfileId($orgProfileId);
        return view('include.list_user_group', [
            'listUserGroup' => $list_data]);
    }
    
}