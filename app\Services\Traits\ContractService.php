<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Log;

trait ContractService
{



    protected function getContractDetails($contractNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CT_CONTRACT as C');
        $query->where('C.CONTRACT_NO', $contractNo);
        return  $query->first();
    }

    protected function getContractDetailsByContractID($contractID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CT_CONTRACT as C');
        $query->join('PM_ORG_VALIDITY as OV', 'C.OWNER_ORG_PROFILE_ID', '=', 'OV.ORG_PROFILE_ID');
        $query->join('SM_SUPPLIER as S', 'C.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->where('C.CONTRACT_ID', $contractID);
        $query->whereDate('OV.EXP_DATE', '>', Carbon::now());
        $query->select('C.*');
        $query->addSelect('OV.ORG_NAME', 'OV.ORG_CODE', 'OV.EXP_DATE');
        $query->addSelect('S.COMPANY_NAME', 'S.EP_NO');
        return  $query->first();
    }


    /**
     * 
     * @param type $search
     * @return type
     */
    protected function getListContract($search)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CT_CONTRACT as C');
        $query->join('CT_CONTRACT_VER as CV', 'C.CONTRACT_ID', '=', 'CV.CONTRACT_ID');
        $query->join('CT_WORKFLOW_STATUS as CW', 'CV.CONTRACT_VER_ID', '=', 'CW.DOC_ID');
        $query->join('PM_STATUS as S', 'CW.STATUS_ID', '=', 'S.STATUS_ID');
        $query->join('PM_STATUS_DESC as SD', 'S.STATUS_ID', '=', 'SD.STATUS_ID');
        $query->where('SD.language_code', 'ms');
        $query->where('C.CONTRACT_NO', $search);
        $query->orWhere('C.LOA_PHYSICAL_NO', $search);
        $query->orWhere('CV.CONTRACT_PHYSICAL_NO', $search);
        $query->select('C.*');
        $query->addSelect('CV.CONTRACT_VER_ID', 'CV.DOC_TYPE', 'CV.AGREEMENT_SIGNED_DATE', 'CV.CONTRACT_PHYSICAL_NO', 'CV.CREATED_DATE AS CREATED_DATE_VER');
        $query->addSelect(DB::raw("(SELECT decode (record_status, '1', 'ACTIVE', '0', 'TERMINATE', '3', 'EXPIRED') FROM ct_contract WHERE contract_id = C.contract_id ) ct_status "));
        $query->addSelect('S.STATUS_ID', 'SD.STATUS_NAME');
        $query->addSelect('CW.CREATED_DATE as WF_CREATED_DATE', 'CW.IS_CURRENT');
        $query->distinct();
        $query->orderBy('CW.created_date', 'desc');
        return  $query->get();
    }

    /**
     * 
     * @param type $search
     * @return type
     */
    protected function getListContractFromEkontrak($search)
    {
        $query = DB::connection('mysql_ep_kontrak')->table('mc_contract_hdr as C');
        $query->where('C.deleted', 0);
        $query->where(function ($query) use ($search) {
            $query->orWhere('C.loa_physical_no', $search)
                ->orWhere('C.contract_physical_no', $search);
        });
        return  $query->get();
    }

    /**
     *
     * @param type $contractId
     * @return type
     */
    protected function getEkontrakDetails($contractId)
    {
        $contractHdr = DB::connection('mysql_ep_kontrak')->table('mc_contract_hdr')->where('contract_id', $contractId)->first();

        $query = DB::connection('mysql_ep_kontrak')->table('mc_contract_hdr as A');
        if ($contractHdr->template == 'TEMPLATE_A') {
            $query->join('mc_contract_dtl as B', 'A.contract_id', '=', 'B.contract_id');
        }
        $query->where('A.deleted', 0);
        $query->where('A.contract_id', $contractId);
        return $query->first();
    }

    /**
     *
     * @param type $contractId
     * @return type
     */
    protected function getEkontrakAgency($contractId)
    {
        $query = DB::connection('mysql_ep_kontrak')->table('mc_agency as A');
        $query->where('A.contract_id', $contractId);
        return $query->first();
    }

    /**
     *
     * @param type $contractId
     * @return type
     */
    protected function getEkontrakItem($contractId)
    {
        $query = DB::connection('mysql_ep_kontrak')->table('mc_contract_item as A');
        $query->where('A.contract_id', $contractId);
        return $query->get();
    }

    /**
     *
     * @param type $contractId
     * @return type
     */
    protected function getEkontrakBond($contractId)
    {
        $query = DB::connection('mysql_ep_kontrak')->table('mc_bond as A');
        $query->where('A.contract_id', $contractId);
        return $query->first();
    }

    protected function getListAllCommitteeMembersCTByLatestVersion($search)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   ct.contract_no,ct.latest_contract_ver_id as contract_ver, com.ic_passport, com.member_name,
                    pp2.parameter_code role_code, ppd2.code_name role_desc,
                    ppd1.code_name role_name, com.committee_member_id
               FROM ct_contract ct,
                    ct_amendment amt,
                    ct_committee_member com,
                    sc_committee sc,
                    sc_committee_member sm,
                    pm_parameter pp1,
                    pm_parameter_desc ppd1,
                    pm_parameter pp2,
                    pm_parameter_desc ppd2,
                    pm_user pm,
                    pm_user_org org,
                    pm_user_role rl,
                    pm_role_desc rd
              WHERE ct.latest_contract_ver_id = amt.contract_ver_id
                AND com.committee_member_id = sm.committee_member_id
                AND amt.amendment_id = com.amendment_id
                AND pm.user_id = com.user_id
                AND rl.role_code = rd.role_code
                AND pm.user_id = org.user_id
                AND org.user_org_id = rl.user_org_id
                AND sm.committee_id = sc.committee_id
                AND com.member_role_id = pp2.parameter_id
                AND pp1.parameter_id = ppd1.parameter_id
                AND pp2.parameter_id = ppd2.parameter_id
                AND sc.committee_type_id = pp1.parameter_id
                AND ct.record_status = 1
                AND ppd1.language_code = 'ms'
                AND ppd2.language_code = 'ms'
                AND rd.language_code = 'ms'
                AND ct.contract_no = ? 
           GROUP BY ct.contract_no,
                    ct.latest_contract_ver_id,
                    com.committee_member_id,
                    com.member_name,
                    com.ic_passport,
                    ppd1.code_name,
                    ppd2.code_name,
                    pp2.parameter_code  ",
            array($search)
        );


        return $results;
    }

    protected function getListAllCommitteeMembersCTByCurrentVersion($search)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   ct.contract_no,ct.current_contract_ver_id  as contract_ver, com.ic_passport, com.member_name,
                        pp2.parameter_code role_code, ppd2.code_name role_desc,
                        ppd1.code_name role_name, com.committee_member_id
                   FROM ct_contract ct,
                        ct_amendment amt,
                        ct_committee_member com,
                        sc_committee sc,
                        sc_committee_member sm,
                        pm_parameter pp1,
                        pm_parameter_desc ppd1,
                        pm_parameter pp2,
                        pm_parameter_desc ppd2,
                        pm_user pm,
                        pm_user_org org,
                        pm_user_role rl,
                        pm_role_desc rd
                  WHERE ct.current_contract_ver_id = amt.contract_ver_id
                    AND com.committee_member_id = sm.committee_member_id
                    AND amt.amendment_id = com.amendment_id
                    AND pm.user_id = com.user_id
                    AND rl.role_code = rd.role_code
                    AND pm.user_id = org.user_id
                    AND org.user_org_id = rl.user_org_id
                    AND sm.committee_id = sc.committee_id
                    AND com.member_role_id = pp2.parameter_id
                    AND pp1.parameter_id = ppd1.parameter_id
                    AND pp2.parameter_id = ppd2.parameter_id
                    AND sc.committee_type_id = pp1.parameter_id
                    AND ct.record_status = 1
                    AND ppd1.language_code = 'ms'
                    AND ppd2.language_code = 'ms'
                    AND rd.language_code = 'ms' 
                    AND ct.contract_no = ? 
               GROUP BY ct.contract_no,
                        ct.current_contract_ver_id,
                        com.committee_member_id,
                        com.member_name,
                        com.ic_passport,
                        ppd1.code_name,
                        ppd2.code_name,
                        pp2.parameter_code  ",
            array($search)
        );


        return $results;
    }

    protected function getContractItems($contractNo, $contractVersion)
    {

        $list = DB::connection('oracle_nextgen_rpt')
            ->table('ct_contract a')
            ->join('ct_contract_amount b', 'a.latest_contract_ver_id', '=', 'b.contract_ver_id')
            ->join('ct_contract_item c', 'a.latest_contract_ver_id', '=', 'c.contract_ver_id')
            ->join('ct_item_price d', 'c.contract_item_id', '=', 'd.contract_item_id')
            ->join('sc_request_item e', 'c.request_item_id', '=', 'e.request_item_id')
            ->join('cm_item f', 'e.ITEM_ID', '=', 'f.ITEM_ID')
            ->where('a.contract_no', $contractNo)
            ->where('a.latest_contract_ver_id', $contractVersion)
            ->select('e.item_name', 'f.extension_code', 'd.unit_price', 'd.item_price_id', 'e.request_item_id', 'b.contract_amount', 'b.gst_amt', 'a.latest_contract_ver_id', 'a.contract_no')
            ->orderBy('a.contract_no', 'desc')
            ->get();
        return $list;
    }

    protected function getContractItemsLatestVersion($contractNo)
    {

        $list = DB::connection('oracle_nextgen_rpt')
            ->table('ct_contract a')
            ->join('ct_contract_amount b', 'a.latest_contract_ver_id', '=', 'b.contract_ver_id')
            ->join('ct_contract_item c', 'a.latest_contract_ver_id', '=', 'c.contract_ver_id')
            ->join('ct_item_price d', 'c.contract_item_id', '=', 'd.contract_item_id')
            ->join('sc_request_item e', 'c.request_item_id', '=', 'e.request_item_id')
            ->join('cm_item f', 'e.ITEM_ID', '=', 'f.ITEM_ID')
            ->where('a.contract_no', $contractNo)
            ->select('e.item_name', 'f.extension_code', 'd.unit_price', 'd.item_price_id', 'e.request_item_id', 'b.contract_amount', 'b.gst_amt', 'a.latest_contract_ver_id')
            ->orderBy('a.contract_no', 'desc')
            ->get();
        return $list;
    }

    protected function getListContractAgreementStatus($contractId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT to_char (b.created_date, 'DD/MM/YYYY HH24:MI:SS') wf_created_date,
                    a.created_date,
                    a.contract_id,

               (SELECT contract_no
                FROM ct_contract
                WHERE a.contract_id = contract_id ) ct_no,

               (SELECT decode (record_status, '1', 'ACTIVE', '0', 'TERMINATE', '3', 'EXPIRED')
                FROM ct_contract
                WHERE a.contract_id = contract_id ) status_ct,
                    a.agreement_id,
                    b.doc_id,
                    b.doc_type,
                    d.status_name,
                    c.status_id,
                    b.is_current,
                    a.created_by,
                    a.changed_by,
                    a.*
             FROM ct_agreement a,
                  ct_workflow_status b,
                  pm_status c,
                  pm_status_desc d
             WHERE a.agreement_id = b.doc_id
               AND b.status_id = c.status_id
               AND c.status_id = d.status_id
               AND d.language_code ='ms'
               AND b.doc_type IN ('AC','SA')
               AND a.contract_id  = ? 
             ORDER BY a.contract_id,b.created_date DESC  ",
            array($contractId)
        );


        return $results;
    }

    protected function getContractOverview($contractId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT contract_no AS contract_no,
                    contract_name AS contract_title,

                   (SELECT org_code
                    FROM pm_org_validity
                    WHERE org_profile_id = owner_org_profile_id
                      AND sysdate BETWEEN eff_date AND exp_date ) AS ptj_code,

                   (SELECT org_name
                    FROM pm_org_validity
                    WHERE org_profile_id = owner_org_profile_id
                      AND sysdate BETWEEN eff_date AND exp_date ) AS ptj_name,
                        company_name AS company_name,
                        ep_no,
                        loa_no AS loa_no,
                        loa_physical_no AS file_ref_no,
                        contract_physical_no AS physical_contract_no,
                        latest_contract_ver_id,
                        current_contract_ver_id,
                        eff_date AS eff_date,
                        exp_date AS exp_date,
                        decode(contract_status, 1, 'Effective', 2, 'Inactive', 3, 'Expired', 4, 'Terminated', 'no_status') AS contract_status,

                   (SELECT status_name
                    FROM pm_status_desc st
                    WHERE ct_status = st.status_id
                      AND st.language_code = 'en' ) AS current_status,
                        decode(fd_status, 1, 'Complete', 'Incomplete') AS fl_details_status,
                        decode(ag_status, 1, 'Complete', 0, 'NA', 'Incomplete') AS agreement_status,
                        (CASE
                             WHEN fd_status = 1
                                  AND (ag_status = 1
                                       OR ag_status = 0) THEN 'Complete'
                             ELSE 'Incomplete'
                         END) AS doc_contract_status
                 FROM
                   (SELECT a.contract_no,
                           a.contract_id,
                           a.contract_name,
                           d.supplier_name AS company_name,
                           b.ep_no AS ep_no,
                           d.eff_date,
                           d.exp_date,
                           d.loa_no,
                           d.loa_physical_no,
                           a.latest_contract_ver_id,
                           a.current_contract_ver_id,

                        -- c.contract_physical_no,
                        ( SELECT listagg(contract_physical_no, '') within GROUP (
                                                                            ORDER BY contract_physical_no)
                         FROM
                           (SELECT DISTINCT cag.contract_physical_no,
                                            cag.contract_id
                            FROM ct_contract_ver cag
                            WHERE cag.contract_physical_no IS NOT NULL
                              AND cag.record_status = 1
                            ORDER BY cag.created_date)
                         WHERE contract_id = a.contract_id ) AS contract_physical_no, 
                       

                       
                           f.status_id AS ct_status,
                           (CASE
                                WHEN a.latest_contract_ver_id IS NOT NULL THEN (CASE
                                                                                    WHEN i.is_bond_req = 0 THEN 1
                                                                                    WHEN (i.is_bond_req = 1
                                                                                          AND i.is_bond_exempted = 1) THEN 1
                                                                                    WHEN (i.is_bond_req = 1
                                                                                          AND i.is_bond_exempted = 0)
                                                                                         AND (
                                                                                                (SELECT sum(cpb.bond_amount)
                                                                                                 FROM ct_performance_bond cpb
                                                                                                 WHERE cpb.contract_amount_id = i.contract_amount_id) >= i.total_bond_amt) THEN 1
                                                                                    ELSE 2
                                                                                END)
                                ELSE 2
                            END) AS fd_status,
                           (CASE
                                WHEN (e.is_agreement = 0) THEN 0
                                WHEN (e.is_agreement = 1
                                      AND
                                        (SELECT count(*)
                                         FROM ct_workflow_status cws
                                         WHERE cws.doc_id IN
                                             (SELECT ag.agreement_id
                                              FROM ct_agreement ag
                                              WHERE ag.contract_id = a.contract_id
                                                AND ag.exp_date = d.exp_date )
                                           AND ((cws.doc_type = 'AC'
                                                 AND cws.status_id = 56600
                                                 AND cws.is_current = 1)
                                                OR (cws.doc_type = 'SA'
                                                    AND cws.status_id = 57600
                                                    AND cws.is_current = 1))) > 0) THEN 1
                                ELSE 2
                            END) AS ag_status,
                           (CASE
                                WHEN (a.record_status = 1
                                      AND d.eff_date <= trunc(sysdate)) THEN 1
                                WHEN (a.record_status = 1
                                      AND d.eff_date > trunc(sysdate)) THEN 2
                                WHEN a.record_status = 3 THEN 3
                                WHEN a.record_status = 0 THEN 4
                                ELSE NULL
                            END) AS contract_status,
                           a.owner_org_profile_id
                    FROM ct_contract a
                    LEFT JOIN sm_supplier b ON a.supplier_id = b.supplier_id
                    LEFT JOIN ct_contract_ver c ON a.current_contract_ver_id = c.contract_ver_id
                    LEFT JOIN ct_contract_value d ON c.contract_ver_id = d.contract_ver_id
                    LEFT JOIN ct_contract_amount e ON c.contract_ver_id = e.contract_ver_id
                    AND d.exp_date = e.exp_date
                    LEFT JOIN ct_workflow_status f ON c.contract_ver_id = f.doc_id
                    AND c.doc_type = f.doc_type
                    AND f.is_current = 1
                    LEFT JOIN ct_contract_ver g ON a.latest_contract_ver_id = g.contract_ver_id
                    LEFT JOIN ct_contract_value h ON g.contract_ver_id = h.contract_ver_id
                    LEFT JOIN ct_contract_amount i ON g.contract_ver_id = i.contract_ver_id
                    AND h.exp_date = i.exp_date
                    ORDER BY a.created_date DESC)
                 WHERE contract_id = ?  ",
            array($contractId)
        );

        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    // contract page

    protected function getDetailContract($ver1, $ver2, $docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT contract_id, current_contract_ver_id, latest_contract_ver_id , login_id,user_name, contract_no, qt_no, EFF_DATE, EXP_DATE, DELIVERY_TERM, loa_no, LOA_PHYSICAL_NO, CONTRACT_NAME, Kaedah_Perolehan,
  Jenis_Kontrak, Jenis_Pemenuhan, jenis_category_perolehan, Tarikh_Pengesahan_LOA, PUBLISH_DATE, IS_CONCESSION, IS_BOND_EXEMPTED,
  IS_AGREEMENT, IS_AGREEMENT_LOA, IS_BOND_REQ, IS_BOND_WAIVED, IS_BOND_REQ_LOA, ORG_CODE, ORG_NAME
FROM (
  SELECT ct.contract_id, ct.current_contract_ver_id, ct.latest_contract_ver_id, pu.login_id, pu.user_name, ct.contract_no, qt.qt_no,  to_char(ccv.EFF_DATE,'dd-mm-YYYY') AS eff_date, to_char(ccv.EXP_DATE,'dd-mm-YYYY') AS exp_date , ccv.DELIVERY_TERM , ccv.loa_no, ccv.LOA_PHYSICAL_NO , CONTRACT_NAME , 
    CASE WHEN ct.PROCUREMENT_MODE_ID = 185 THEN 'Sebut Harga' when ct.PROCUREMENT_MODE_ID = 186 THEN 'Tender' END AS Kaedah_Perolehan, 
    CASE WHEN loa.PROCUREMENT_TYPE_CAT_ID = 815 THEN 'Bekalan' when loa.PROCUREMENT_TYPE_CAT_ID = 816 THEN 'Perkhidmatan' END AS jenis_category_perolehan,
    CASE WHEN ct.CONTRACT_TYPE_ID = 146 THEN 'Kementerian' WHEN ct.CONTRACT_TYPE_ID = 147 THEN 'Pusat' END AS Jenis_Kontrak, 
    CASE WHEN ct.FULFILMENT_TYPE_ID = 148 THEN 'One-Off' WHEN ct.FULFILMENT_TYPE_ID = 149 THEN 'Bermasa (Berjadual)' WHEN ct.FULFILMENT_TYPE_ID = 150 THEN 'Bermasa (Bila Perlu)' END AS Jenis_Pemenuhan, 
    to_char(ccv.ACK_DATE,'dd-mm-YYYY') AS Tarikh_Pengesahan_LOA, to_char(ct.PUBLISH_DATE,'dd-mm-YYYY') AS publish_date, CASE WHEN ccv.IS_CONCESSION = 1 THEN 'Yes' ELSE 'No' END AS IS_CONCESSION, 
    CASE WHEN IS_BOND_EXEMPTED  = 1 THEN 'Yes' ELSE 'No' END AS IS_BOND_EXEMPTED , CASE WHEN IS_AGREEMENT  = 1 THEN 'Yes' ELSE 'No' END AS IS_AGREEMENT, CASE WHEN IS_AGREEMENT_REQ  = 1 THEN 'Yes' ELSE 'No' END AS IS_AGREEMENT_LOA, CASE WHEN cca.IS_BOND_REQ  = 1 THEN 'Yes' ELSE 'No' END AS IS_BOND_REQ, CASE WHEN loa.IS_BOND_REQ  = 1 THEN 'Yes' ELSE 'No' END AS IS_BOND_REQ_LOA, 
    CASE WHEN IS_BOND_WAIVED  = 1 THEN 'Yes' ELSE 'No' END AS IS_BOND_WAIVED,
    ROW_NUMBER() OVER (PARTITION BY ct.contract_no ORDER BY cca.EFF_DATE DESC) AS rn, pov.ORG_CODE , pov.ORG_NAME 
  FROM sc_loa loa, ct_contract ct
  JOIN SC_QT qt ON qt.QT_ID(+) = ct.QT_ID 
  JOIN CT_CONTRACT_VALUE ccv ON ccv.CONTRACT_VER_ID = ?
  JOIN CT_CONTRACT_AMOUNT cca ON cca.CONTRACT_VER_ID = ?
  JOIN  PM_USER pu  ON ct.created_by = pu.USER_ID 
  JOIN PM_ORG_VALIDITY pov ON pov.ORG_PROFILE_ID = ct.owner_org_profile_id
  WHERE ct.CONTRACT_NO = ?
  AND ct.loa_id = loa.loa_id
)
WHERE rn =  1   ", array($ver1, $ver2, $docNo));
        return $query;
    }

    protected function getListAgencies($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ca.AGENCY_ID ,  ca.ceiling_amount, to_char(ca.EFF_DATE,'dd-mm-YYYY') AS eff_date , to_char(ca.EXP_DATE,'dd-mm-YYYY') AS exp_date , CASE WHEN ca.RECORD_STATUS = 1 THEN 'Active' ELSE 'Inactive' END AS RECORD_STATUS , pov.ORG_CODE , pov.ORG_NAME AS PTJ, pov1.ORG_NAME AS kumpulan_ptj, pov2.ORG_NAME AS kementerian
FROM CT_AGENCY ca , PM_ORG_PROFILE pop, PM_ORG_VALIDITY pov, PM_ORG_VALIDITY pov1, PM_ORG_PROFILE pop1, PM_ORG_PROFILE pop2,PM_ORG_VALIDITY pov2
WHERE ca.ORG_PROFILE_ID = pop.ORG_PROFILE_ID 
AND pop.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
AND pov.RECORD_STATUS = 1
AND pop.PARENT_ORG_PROFILE_ID = pov1.ORG_PROFILE_ID 
AND pov1.record_status = 1
AND pop.PARENT_ORG_PROFILE_ID  = pop1.ORG_PROFILE_ID 
AND pop1.PARENT_ORG_PROFILE_ID  = pop2.ORG_PROFILE_ID 
AND pov2.ORG_PROFILE_ID = pop2.PARENT_ORG_PROFILE_ID 
AND pov2.RECORD_STATUS = 1
AND ca.CONTRACT_VER_ID = ? ", array($docNo));
        return $query;
    }

    protected function getListAgencyAddress($docNo, $ver)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ca.address_id, ca.ADDRESS_NAME , ca.ADDRESS_1 , ca.ADDRESS_2 , ca.ADDRESS_3 , (SELECT DISTRICT_NAME FROM PM_DISTRICT pd WHERE pd.DISTRICT_ID= ca.DISTRICT_ID) AS DISTRICT_NAME, ca.POSTCODE , (SELECT pc.CITY_NAME  FROM PM_CITY pc WHERE pc.CITY_ID = ca.CITY_ID) AS CITY_NAME, (SELECT ps.STATE_NAME FROM PM_STATE ps WHERE ps.STATE_ID = ca.STATE_ID) AS STATE_NAME
        FROM CT_ADDRESS ca , CT_AGENCY_ADDRESS caa , CT_AGENCY ca2 
        WHERE ca.ADDRESS_ID = caa.DELIVERY_ADDRESS_ID 
        AND caa.AGENCY_ID = ?
        AND CONTRACT_VER_ID  = ? ", array($docNo, $ver));
        return $query;
    }

    protected function getListItem($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ci.EXTENSION_CODE , sri.REQUEST_ITEM_ID , sri.ITEM_NAME , COALESCE(SUM(fis.ITEM_QTY), 0) AS qty, to_char(cimq.EFF_DATE,'dd-mm-YYYY') as EFF_DATE ,to_char(cimq.EXP_DATE,'dd-mm-YYYY') as EXP_DATE , CASE WHEN item.RECORD_STATUS = 1 THEN 'Aktif' WHEN item.RECORD_STATUS = 0 THEN 'Tak Aktif' END AS status, 
CASE WHEN item.ITEM_TYPE_ID = 198 THEN 'Produk' WHEN item.ITEM_TYPE_ID = 199 THEN 'Perkhidmatan' WHEN item.ITEM_TYPE_ID = 200 THEN 'Produk & Perkhidmatan' END AS Jenis_Item,
CASE WHEN item.ITEM_ORIGIN_ID = 195 THEN 'Tempatan' WHEN item.ITEM_ORIGIN_ID = 196 THEN 'Import' WHEN item.ITEM_ORIGIN_ID = 197 THEN 'Tempatan & Import' END AS Jenis_Barang, max_quantity, item.MINIMUM_ORDER_QTY ,
(max_quantity-COALESCE(SUM(fis.ITEM_QTY), 0)) AS balance
FROM ct_contract_item item,  SC_REQUEST_ITEM sri , CM_ITEM ci , CT_ITEM_MAX_QTY cimq, fl_item_summary fis
WHERE item.CONTRACT_VER_ID in ?
AND sri.REQUEST_ITEM_ID = item.REQUEST_ITEM_ID 
AND ci.ITEM_ID = sri.ITEM_ID 
AND fis.REQUEST_ITEM_ID(+) = item.REQUEST_ITEM_ID 
AND item.CONTRACT_ITEM_ID = cimq.CONTRACT_ITEM_ID
AND cimq.EFF_DATE < SYSDATE 
AND cimq.EXP_DATE > SYSDATE  
GROUP BY  ci.EXTENSION_CODE, sri.REQUEST_ITEM_ID, sri.ITEM_NAME , cimq.EFF_DATE, cimq.EXP_DATE, item.RECORD_STATUS, item.ITEM_TYPE_ID, item.ITEM_ORIGIN_ID, max_quantity, item.MINIMUM_ORDER_QTY 
ORDER BY sri.ITEM_NAME ", array($docNo));
        return $query;
    }

    protected function getListItemZone($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT DISTINCT ccz.ZONE_NAME , ccz.DELIVERY_TERM , pd2.DIVISION_NAME , pd.DISTRICT_NAME , pc.CITY_NAME , ps.STATE_NAME , ci.EXTENSION_CODE , sri.ITEM_NAME , COALESCE(SUM(fis.ITEM_QTY), 0) AS qty, to_char(cimq.EFF_DATE,'dd-mm-YYYY') as EFF_DATE ,to_char(cimq.EXP_DATE,'dd-mm-YYYY') as EXP_DATE , CASE WHEN item.RECORD_STATUS = 1 THEN 'Aktif' WHEN item.RECORD_STATUS = 0 THEN 'Tak Aktif' END AS status, 
        CASE WHEN item.ITEM_TYPE_ID = 198 THEN 'Produk' WHEN item.ITEM_TYPE_ID = 199 THEN 'Perkhidmatan' WHEN item.ITEM_TYPE_ID = 200 THEN 'Produk & Perkhidmatan' END AS Jenis_Item,
        CASE WHEN item.ITEM_ORIGIN_ID = 195 THEN 'Tempatan' WHEN item.ITEM_ORIGIN_ID = 196 THEN 'Import' WHEN item.ITEM_ORIGIN_ID = 197 THEN 'Tempatan & Import' END AS Jenis_Barang, max_quantity, item.MINIMUM_ORDER_QTY ,
        (max_quantity-COALESCE(SUM(fis.ITEM_QTY), 0)) AS balance
        FROM ct_contract_item item,  SC_REQUEST_ITEM sri , CM_ITEM ci , CT_ITEM_MAX_QTY cimq,
        CT_CONTRACT_ZONE ccz , CT_CONTRACT_ZONE_DTL cczd , PM_STATE ps ,PM_CITY pc , PM_DISTRICT pd , PM_DIVISION pd2 , fl_item_summary fis
        WHERE item.CONTRACT_VER_ID in ?
        AND sri.REQUEST_ITEM_ID = item.REQUEST_ITEM_ID 
        AND ci.ITEM_ID = sri.ITEM_ID 
        AND ccz.CONTRACT_VER_ID = item.CONTRACT_VER_ID 
        AND ccz.CONTRACT_ZONE_ID = cczd.CONTRACT_ZONE_ID 
        AND fis.REQUEST_ITEM_ID(+) = item.REQUEST_ITEM_ID 
        AND cczd.STATE_ID = ps.STATE_ID 
        AND cczd.CITY_ID = pc.CITY_ID(+)  
        AND cczd.DIVISION_ID = PD2.DIVISION_ID(+)  
        AND cczd.DISTRICT_ID = pd.DISTRICT_ID(+) 
        AND item.CONTRACT_ITEM_ID = cimq.CONTRACT_ITEM_ID 
        AND cimq.CONTRACT_ZONE_ID = ccz.CONTRACT_ZONE_ID
        AND cimq.EFF_DATE < SYSDATE 
        AND cimq.EXP_DATE > SYSDATE  
        GROUP BY ccz.ZONE_NAME , ccz.DELIVERY_TERM , pd2.DIVISION_NAME , pd.DISTRICT_NAME , pc.CITY_NAME , ps.STATE_NAME , ci.EXTENSION_CODE , sri.ITEM_NAME, cimq.EFF_DATE, cimq.EXP_DATE, item.RECORD_STATUS, item.ITEM_TYPE_ID, item.ITEM_ORIGIN_ID, max_quantity, item.MINIMUM_ORDER_QTY 
        ORDER BY sri.ITEM_NAME asc", array($docNo));
        return $query;
    }

    protected function getUnitPriceItem($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT cip.unit_price, ci.EXTENSION_CODE , sri.ITEM_NAME , to_char(cip.EFF_DATE,'dd-mm-YYYY') as EFF_DATE ,to_char(cip.EXP_DATE,'dd-mm-YYYY') as EXP_DATE , CASE WHEN item.RECORD_STATUS = 1 THEN 'Aktif' WHEN item.RECORD_STATUS = 0 THEN 'Tak Aktif' END AS status, 
        CASE WHEN item.ITEM_TYPE_ID = 198 THEN 'Produk' WHEN item.ITEM_TYPE_ID = 199 THEN 'Perkhidmatan' WHEN item.ITEM_TYPE_ID = 200 THEN 'Produk & Perkhidmatan' END AS Jenis_Item,
        CASE WHEN item.ITEM_ORIGIN_ID = 195 THEN 'Tempatan' WHEN item.ITEM_ORIGIN_ID = 196 THEN 'Import' WHEN item.ITEM_ORIGIN_ID = 197 THEN 'Tempatan & Import' END AS Jenis_Barang,  item.MINIMUM_ORDER_QTY 
        FROM ct_contract_item item,  SC_REQUEST_ITEM sri , CM_ITEM ci , CT_ITEM_PRICE cip
        WHERE item.CONTRACT_VER_ID = ?
        AND sri.REQUEST_ITEM_ID = item.REQUEST_ITEM_ID 
        AND ci.ITEM_ID = sri.ITEM_ID 
        AND item.CONTRACT_ITEM_ID = cip.CONTRACT_ITEM_ID ", array($docNo));
        return $query;
    }

    protected function getListDeduct($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ppd.CODE_NAME , DEDUCTION_DESC , cdc.CLAUSE_DESCRIPTION 
FROM CT_DEDUCTION cd ,PM_PARAMETER_DESC ppd , CT_DEDUCTION_CLAUSE cdc
WHERE cd.RECORD_STATUS  = 1
AND ppd.PARAMETER_ID = DEDUCTION_TYPE_ID 
AND ppd.LANGUAGE_CODE = 'ms'
AND cdc.DEDUCTION_ID = cd.DEDUCTION_ID 
AND cd.CONTRACT_VER_ID = ?  ", array($docNo));
        return $query;
    }

    protected function getListBond($docNo, $no)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT cpb.REF_NO , cpb.FINANCIAL_ORG_NAME , to_char(cpb.EFF_DATE,'dd-mm-YYYY') as EFF_DATE , to_char(cpb.EXP_DATE,'dd-mm-YYYY') as EXP_DATE , IS_BOND_WAIVED
FROM CT_PERFORMANCE_BOND cpb , CT_CONTRACT_AMOUNT cca
WHERE cca.CONTRACT_AMOUNT_ID = cpb.CONTRACT_AMOUNT_ID 
AND cca.CONTRACT_VER_ID = ?
AND cca.EXP_DATE = (SELECT MAX(EXP_DATE) FROM CT_CONTRACT_AMOUNT WHERE CONTRACT_VER_ID = ?)
AND cpb.RECORD_STATUS = 1 ", array($docNo, $no));
        return $query;
    }

    protected function getListNotification($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT CT_NOTIFY_DAYS , AG_NOTIFY_DAYS_B4_AG , BOND_NOTIFY_DAYS , TERMINATION_NOTIFY_DAYS , SUPPLIER_DO_DAYS , APPROVER_MTO_DAYS , LAST_CO_B4_EXP_DAYS 
FROM CT_SETTING cs 
WHERE cs.CONTRACT_VER_ID = ?  ", array($docNo));
        return $query;
    }

    protected function getListVerContract($docId)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT cv.CONTRACT_VER_ID, 
        CASE WHEN cv.DOC_TYPE = 'FU' THEN 'FU - Fulfilment Details Update'
WHEN cv.DOC_TYPE = 'NT' THEN 'NT - Notification'
WHEN cv.DOC_TYPE = 'FT' THEN 'FT - Contract Termination'
WHEN cv.DOC_TYPE = 'FA' THEN 'FA - Fulfilment Details Amendment'
WHEN cv.DOC_TYPE = 'FW' THEN 'FW - Amendment without Supplement' END AS doc_type,
         cv.CREATED_DATE
FROM ct_contract_ver cv
JOIN (
    SELECT CONTRACT_ID, max(CREATED_DATE) AS max_created_date
    FROM ct_contract_ver
    WHERE CONTRACT_ID = ?
    GROUP BY CONTRACT_ID
) max_dates ON cv.CONTRACT_ID = max_dates.CONTRACT_ID AND cv.CREATED_DATE = max_dates.max_created_date
ORDER BY cv.CREATED_DATE DESC ", array($docId));
        return $query;
    }

    protected function getListContractAdmin($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pur.ROLE_CODE , user_name, login_id 
FROM PM_USER_ROLE pur, pm_user_org pug, ct_contract ct, pm_user pu
WHERE pug.USER_ORG_ID = pur.USER_ORG_ID 
AND ct.OWNER_ORG_PROFILE_ID  = pug.ORG_PROFILE_ID 
AND pug.record_status = 1
AND pur.RECORD_STATUS = 1
AND pu.USER_ID = pug.user_id
AND pur.role_code = 'MIN_CONTRACT_ADMIN'
AND ct.CONTRACT_NO = ?  ", array($docNo));
        return $query;
    }

    protected function getListContractApprover($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pur.ROLE_CODE , user_name, login_id 
FROM PM_USER_ROLE pur, pm_user_org pug, ct_contract ct, pm_user pu
WHERE pug.USER_ORG_ID = pur.USER_ORG_ID 
AND ct.OWNER_ORG_PROFILE_ID  = pug.ORG_PROFILE_ID 
AND pug.record_status = 1
AND pur.RECORD_STATUS = 1
AND pu.USER_ID = pug.user_id
AND pur.role_code = 'CT_APPROVER'
AND ct.CONTRACT_NO = ?  ", array($docNo));
        return $query;
    }

    protected function getListContractApproverSelected($ver)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
       SELECT * FROM CT_APPROVER ca , PM_USER pu 
WHERE ca.APPROVER_USER_ID = pu.USER_ID 
AND DOC_ID = ?  ", array($ver));
        return $query;
    }

    protected function getListAmendmentWithSupp($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT user_name, case when IS_CONTRACT_EXTEND =1 then 'Yes' else 'No' end as IS_CONTRACT_EXTEND, EXTEND_PERIOD , to_char(EXTEND_START_DATE,'dd-mm-YYYY') AS EXTEND_START_DATE , to_char(EXTEND_END_DATE,'dd-mm-YYYY') AS EXTEND_END_DATE, 
        case when IS_ITEM_PRICE_CHANGE =1 then 'Yes' else 'No' end as  IS_ITEM_PRICE_CHANGE, case when IS_QUANTITY_CHANGE =1 then 'Yes' else 'No' end as IS_QUANTITY_CHANGE, case when IS_VALUE_CHANGE =1 then 'Yes' else 'No' end as  IS_VALUE_CHANGE, case when IS_CLAUSE_CHANGE =1 then 'Yes' else 'No' end as IS_CLAUSE_CHANGE , case when IS_OTHER_CHANGE =1 then 'Yes' else 'No' end as IS_OTHER_CHANGE, to_char(AMENDMENT_EFF_DATE,'dd-mm-YYYY') as AMENDMENT_EFF_DATE , 
        FILE_REF_NO , TITLE , DESCRIPTION , case when is_mof_authority_req =1 then 'Yes' else 'No' end as is_mof_authority_req , case when IS_PBQC_AUTHORITY_REQ =1 then 'Yes' else 'No' end as IS_PBQC_AUTHORITY_REQ, case when is_approver_authority_req =1 then 'Yes' else 'No' end as is_approver_authority_req
        FROM CT_AMENDMENT ca, pm_user pu
        WHERE ca.secretary_id = pu.user_id(+)
        and ca.contract_ver_id = ?  ", array($docNo));
        return $query;
    }

    protected function getListSchedule($ver1, $docNo, $ver2, $ct, $no)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT cs.SCHEDULE_NO , pr.doc_no, to_char(cs.FROM_DATE,'dd-mm-yyyy') AS from_date , to_char(cs.TO_DATE,'dd-mm-yyyy') AS to_date , CASE WHEN cs.SCHEDULE_TYPE_ID = 313 THEN 'Bermasa (Berjadual)' WHEN cs.SCHEDULE_TYPE_ID = 314 THEN 'Bermasa (Bila Perlu)' END AS  SCHEDULE_TYPE_ID,  cs.record_status 
FROM CT_SCHEDULE cs, FL_FULFILMENT_REQUEST pr, CT_CONTRACT ct
WHERE CONTRACT_VER_ID  = ?
AND pr.CONTRACT_ID = ct.CONTRACT_ID 
AND cs.SCHEDULE_NO = pr.SCHEDULE_NO
AND pr.RECORD_STATUS = 1
AND contract_no = ?
UNION 
SELECT SCHEDULE_NO  , NULL, to_char(csm.FROM_DATE,'dd-mm-yyyy') AS from_date , to_char(csm.TO_DATE,'dd-mm-yyyy') AS to_date, CASE WHEN csm.SCHEDULE_TYPE_ID = 313 THEN 'Bermasa (Berjadual)' WHEN csm.SCHEDULE_TYPE_ID = 314 THEN 'Bermasa (Bila Perlu)' END AS  SCHEDULE_TYPE_ID, NULL
FROM CT_SCHEDULE csm
WHERE CONTRACT_VER_ID = ? 
AND SCHEDULE_NO NOT IN (SELECT cs.SCHEDULE_NO
FROM CT_SCHEDULE cs, FL_FULFILMENT_REQUEST pr, CT_CONTRACT ct
WHERE CONTRACT_VER_ID  = ?
AND pr.CONTRACT_ID = ct.CONTRACT_ID 
AND cs.SCHEDULE_NO = pr.SCHEDULE_NO 
AND pr.RECORD_STATUS = 1
AND contract_no = ?)
", array($ver1, $docNo, $ver2, $ct, $no));
        return $query;
    }

    protected function getListFactoring($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT FINANCIAL_ORG_NAME , BANK_NAME , BANK_ACCOUNT_NO , AUTH_REF_NO , to_char(EFF_DATE,'dd-mm-yyyy') as EFF_DATE , to_char(EXP_DATE,'dd-mm-yyyy') as EXP_DATE , to_char(AGREEMENT_DATE, 'dd-mm-yyyy') as AGREEMENT_DATE , CASE WHEN RECORD_STATUS = 1 THEN 'Active' WHEN RECORD_STATUS = 0 THEN 'Inactive' WHEN RECORD_STATUS = 3 THEN 'Expired' END AS RECORD_STATUS
FROM CT_FACTORING cf 
WHERE contract_id = ?
ORDER BY AGREEMENT_DATE desc ", array($docNo));
        return $query;
    }

    protected function getSupplierList($ver,$docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ca.CONTACT_PERSON , ca.CONTACT_EMAIL , ad.ADDRESS_ID , ss.company_name, ccv.reg_no ,mof.MOF_NO  , EP_NO , ca.ADDRESS_1,  ca.ADDRESS_2 , ca.POSTCODE , (SELECT pc2.CITY_NAME  FROM PM_CITY pc2 WHERE pc2.CITY_ID = ca.CITY_ID) AS city, (SELECT pd.DISTRICT_NAME  FROM PM_DISTRICT pd WHERE pd.DISTRICT_ID = ca.DISTRICT_ID) AS district, (SELECT ps.STATE_NAME  FROM PM_STATE ps WHERE ps.STATE_ID = ca.STATE_ID) AS state, (SELECT pc.COUNTRY_NAME  FROM PM_COUNTRY pc WHERE pc.COUNTRY_ID= ca.COUNTRY_ID) AS country,
pfo.FIN_ORG_NAME , ssb.ACCOUNT_NO , ssb.BANK_BRANCH , sa1.ADDRESS_1 AS address1_bank,  sa1.ADDRESS_2 AS address2_bank, sa1.POSTCODE postcode_bank, (SELECT pc2.CITY_NAME  FROM PM_CITY pc2 WHERE pc2.CITY_ID = sa1.CITY_ID) AS city_bank, (SELECT pd.DISTRICT_NAME  FROM PM_DISTRICT pd WHERE pd.DISTRICT_ID = sa1.DISTRICT_ID) AS district_bank, (SELECT ps.STATE_NAME  FROM PM_STATE ps WHERE ps.STATE_ID = sa1.STATE_ID) AS state_bank, (SELECT pc.COUNTRY_NAME  FROM PM_COUNTRY pc WHERE pc.COUNTRY_ID= sa1.COUNTRY_ID) AS country_bank
FROM SM_SUPPLIER ss , SM_COMPANY_BASIC bas, SM_COMPANY_ADDRESS ad, SM_ADDRESS sa, CT_CONTRACT cc , SM_SUPPLIER_BANK ssb, PM_FINANCIAL_ORG pfo , SM_ADDRESS sa1, SM_MOF_ACCOUNT mof, CT_CONTRACT_VALUE ccv, CT_ADDRESS ca
WHERE bas.APPL_ID = LATEST_APPL_ID 
AND ad.COMPANY_BASIC_ID = bas.COMPANY_BASIC_ID 
AND sa.ADDRESS_ID = ad.ADDRESS_ID 
AND ss.SUPPLIER_ID = cc.SUPPLIER_ID 
AND ssb.APPL_ID = LATEST_APPL_ID
AND ssb.FINANCIAL_ORG_ID = pfo.FINANCIAL_ORG_ID 
AND sa1.ADDRESS_ID = ssb.ADDRESS_ID 
AND mof.SUPPLIER_ID = cc.SUPPLIER_ID 
AND ccv.CONTRACT_VER_ID = ?
AND ccv.BANK_ACCOUNT_NO = ssb.ACCOUNT_NO  
AND ccv.SUPPLIER_ADDRESS_ID = ca.ADDRESS_ID 
AND cc.CONTRACT_NO = ?", array($ver,$docNo));
        return $query;
    }

    protected function getAgreement($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT  psd.STATUS_NAME , ca.AGREEMENT_ID , ca.CREATED_DATE, cws.CREATED_DATE , to_char(EFF_DATE, 'dd-mm-yyyy') AS EFF_DATE, to_char(EXP_DATE,'dd-mm-yyyy') AS EXP_DATE, ca.RECORD_STATUS 
FROM CT_AGREEMENT ca , CT_CONTRACT ct, CT_WORKFLOW_STATUS cws , PM_STATUS_DESC psd 
WHERE ca.CONTRACT_ID = ct.CONTRACT_ID 
AND ca.AGREEMENT_ID = cws.DOC_ID 
AND cws.DOC_TYPE in ('AC','SA')
AND cws.IS_CURRENT = 1
AND cws.STATUS_ID = psd.STATUS_ID 
AND psd.LANGUAGE_CODE = 'ms'
AND CONTRACT_NO = ?
ORDER BY AGREEMENT_ID desc", array($docNo));
        return $query;
    }

    protected function getFulfilmentStatus($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT status_name
FROM CT_WORKFLOW_STATUS cws , PM_STATUS_DESC psd 
WHERE CWS.STATUS_ID  = psd.STATUS_ID 
AND cws.IS_CURRENT = 1
AND psd.LANGUAGE_CODE = 'ms'
AND cws.DOC_ID = ?", array($docNo));
        return $query;
    }

    protected function getListContractVer($docNo, $ver)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ccv.CREATED_DATE, ccv.CONTRACT_VER_ID, ccv.LOA_NO, ccv.LOA_PHYSICAL_NO
FROM CT_CONTRACT_VALUE ccv
JOIN CT_CONTRACT_VER cc ON cc.CONTRACT_VER_ID = ccv.CONTRACT_VER_ID
JOIN CT_CONTRACT ct ON ct.CONTRACT_ID = cc.CONTRACT_ID 
WHERE ct.CONTRACT_NO  = ?
AND ccv.CONTRACT_VER_ID = (
    SELECT MAX(CONTRACT_VER_ID)
    FROM CT_CONTRACT_VALUE
    WHERE LOA_NO = ccv.LOA_NO
)
AND ccv.CONTRACT_VER_ID NOT IN ?
ORDER BY  ccv.CONTRACT_VER_ID desc", array($docNo, $ver));
        return $query;
    }

    protected function getContractVer($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT cc.CONTRACT_NO 
FROM CT_CONTRACT_VER ccv , CT_CONTRACT cc 
WHERE ccv.CONTRACT_ID = cc.CONTRACT_ID 
AND ccv.CONTRACT_VER_ID = ?", array($docNo));
        return $query;
    }

    protected function getQtKodBidang($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pcl.CATEGORY_NAME as a1, pcl2.CATEGORY_NAME as a2, pcl3.CATEGORY_NAME as a3, pcl.CATEGORY_L1_CODE as c1, pcl2.CATEGORY_L2_CODE as c2, pcl3.CATEGORY_L3_CODE as c3 
        FROM SC_QT_CATEGORY sqc , SC_QT qt, PM_CATEGORY_L1 pcl , PM_CATEGORY_L2 pcl2 , PM_CATEGORY_L3 pcl3 
        WHERE qt.QT_ID = sqc.QT_ID 
        AND sqc.CATEGORY_L1_ID = pcl.CATEGORY_L1_ID 
        AND sqc.CATEGORY_L2_ID = pcl2.CATEGORY_L2_ID 
        AND sqc.CATEGORY_L3_ID = pcl3.CATEGORY_L3_ID 
        AND qt.qt_id = ?", array($docNo));
        return $query;
    }

    protected function getContractTerminateion($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT CASE WHEN DECISION_TYPE_ID = 136 THEN 'Ditamatkan' WHEN DECISION_TYPE_ID = 137 THEN 'Penamatan Bersama' WHEN DECISION_TYPE_ID = 138 THEN 'Notis Remedi' END AS keputusan_penamatan, FILE_REF_NO , to_char(DECISION_DATE, 'dd-mm-yyyy') as DECISION_DATE ,
        to_char(REMEDY_START_DATE, 'dd-mm-yyyy') as REMEDY_START_DATE , to_char(REMEDY_END_DATE, 'dd-mm-yyyy') as REMEDY_END_DATE , ctr.CLAUSE_NO , ctr.DESCRIPTION 
        FROM CT_TERMINATION cn , CT_TERMINATION_REASON ctr , CT_CONTRACT ct 
        WHERE cn.TERMINATION_ID = ctr.TERMINATION_ID 
        AND cn.contract_ver_id = ct.LATEST_CONTRACT_VER_ID 
        AND ct.CONTRACT_NO = ?", array($docNo));
        return $query;
    }


    protected function listCtCancelAgreementStatBatchNoQuery($batchNo){
        $whereBatchNo = "";
        if(strlen($batchNo) >  0) {
            $whereBatchNo = "WHERE BATCH_NO LIKE '%$batchNo%'";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT PROGRESS_STATUS,BATCH_NO, min(changed_date) AS min_date,max(changed_date) AS max_date,
                    count(*) AS total 
                    FROM CT_AGREEMENT_CANCEL_TEMP 
                    $whereBatchNo 
                    GROUP BY PROGRESS_STATUS,BATCH_NO
                    ORDER BY 1,BATCH_NO ,PROGRESS_STATUS ");
        return $query;
    }

    protected function listCtCancelAgreementQuery($carian){
        $whereCarian = "";
        if(strlen($carian) >  0) {
            $whereCarian = " AND ( e.doc_no = '$carian' OR e.batch_no ='$carian' OR e.loa_no ='$carian' )";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT 
            e.doc_no AS contract_no,e.loa_no, e.instance_id, e.batch_no,e.progress_status,
            TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,a.CONTRACT_ID,a.AGREEMENT_ID ,
            d.status_name AS latest_status ,c.STATUS_ID,b.is_current , a.CREATED_BY , a.CHANGED_BY
            from ct_agreement a, ct_workflow_status b, pm_status c, pm_status_desc d,
            CT_AGREEMENT_CANCEL_TEMP e
            where a.AGREEMENT_ID = b.doc_id
            and b.status_id = c.status_id    
            and c.status_id = d.status_id
            AND a.AGREEMENT_ID = e.AGREEMENT_ID 
            and d.language_code in ('en','')
            and b.doc_type in ('AC','SA')
            AND b.is_current = 1
            $whereCarian
            order by  a.contract_id, b.CREATED_DATE desc");
        return $query;
    }

    protected function listCtAgreementNullQuery($carian){
        $whereCarian = "";
        if(strlen($carian) >  0) {
            $whereCarian = " AND ( ct.contract_no = '$carian'  )";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT 
            DISTINCT
            ct.CONTRACT_NO,ct.CONTRACT_ID, 
            ag.AGREEMENT_ID
            -- a.compositeinstanceid AS task_instance_id, 
            -- a.state AS task_state, a.assignees AS task_assignees
            from ct_contract_ver cv,  
            ct_workflow_status wf, ct_contract_value val, ct_amendment ca 
            left join ct_agreement ag on ag.AMENDMENT_ID = ca.AMENDMENT_ID,
            ct_contract ct
            /*join wftask@NGEPSOA a on a.CUSTOMATTRIBUTESTRING1 = ct.CONTRACT_NO and a.state = 'ASSIGNED' 
                AND a.compositeversion IN ('1.0.9',
                    '1.0.10.1',
                    '1.0.10',
                    '1.0.10.2')*/
            where ct.CURRENT_CONTRACT_VER_ID = cv.CONTRACT_VER_ID
            and cv.DOC_TYPE = 'FA'
            -- and a.CUSTOMATTRIBUTESTRING2 in ('FA','SA')
            and cv.CONTRACT_VER_ID = ca.CONTRACT_VER_ID
            and ca.CONTRACT_VER_ID = wf.DOC_ID
            and (wf.DOC_TYPE = cv.DOC_TYPE 
                    and wf.IS_CURRENT = 1
                    and wf.STATUS_ID in (51500)
                    )
            $whereCarian 
            and ca.CONTRACT_VER_ID = val.CONTRACT_VER_ID
            and ag.AGREEMENT_ID is null
            order by ct.CONTRACT_NO desc
            ");

            $result = array();

            foreach($query as $res) {
                $contractNo = $res->contract_no;
                $list = DB::connection('oracle_bpm_rpt')->select("
                    select a.compositeinstanceid AS task_instance_id, 
                    a.state AS task_state, a.assignees AS task_assignees
                    from wftask a WHERE a.CUSTOMATTRIBUTESTRING1 = '$contractNo' and a.state = 'ASSIGNED' 
                    AND a.compositeversion IN ('1.0.9', '1.0.10.1', '1.0.10', '1.0.10.2') ");
                if(isset($list)) {
                    foreach($list as $list) {
                        $v = array('contract_no' => $contractNo,
                        'contract_id' => $res->contract_id,
                        'agreement_id' => $res->agreement_id,
                        'task_instance_id' => $list->task_instance_id,
                        'task_state' => $list->task_state,
                        'task_assignees' => $list->task_assignees);
                        array_push($result, $v);
                    } 
                }
            }
        return $result;
    }
    
    protected function listCtPendingAmendmentQuery($carian){
        $whereCarian = "";
        if(strlen($carian) >  0) {
            $whereCarian = " AND ( con.contract_no = '$carian'  )";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT DISTINCT 
            ministry_code,ministry_name,
            jab_code,jab_name,
            ptj_code,ptj_name,
            ptj_addr.state_name AS state,
            con.contract_no, con.contract_id,
            ver.contract_physical_no, con.loa_physical_no,
            h.eff_date, h.exp_date, h.loa_no,
            -- usr.user_name AS ca, usr.email AS ca_email,
            d.status_name ,
            acs.STATUS_ID ,acs.CREATED_DATE AS wf_created_date
            --, con.OWNER_ORG_PROFILE_ID
            FROM ct_contract con
                JOIN ct_contract_amount amt ON con.current_contract_ver_id = amt.contract_ver_id
                JOIN ct_contract_ver ver ON con.current_contract_ver_id = ver.contract_ver_id
                JOIN ct_contract_value h ON  ver.contract_ver_id = h.contract_ver_id
                JOIN pm_user usr ON con.created_by = usr.user_id 
                LEFT JOIN ( 
                    SELECT DISTINCT vmin.org_code as ministry_code, vmin.org_name as ministry_name, 
                        vkptj.org_code as jab_code, vkptj.org_name as jab_name,
                        vptj.org_code as ptj_code, vptj.org_name as ptj_name , vptj.org_profile_id AS ptj_profile_id
                        FROM pm_org_validity  vptj
                        JOIN pm_org_profile  pptj ON  vptj.org_profile_id =  pptj.org_profile_id  AND pptj.org_type_id = 5 
                        JOIN pm_org_validity vkptj ON vkptj.org_profile_id = pptj.parent_org_profile_id  AND vkptj.record_status = 1
                        JOIN pm_org_profile  pkptj ON pkptj.org_profile_id = vkptj.org_profile_id AND pkptj.org_type_id = 4 
                        JOIN pm_org_profile  ppp ON ppp.org_profile_id = pkptj.parent_org_profile_id AND ppp.org_type_id = 3
                        JOIN pm_org_validity vmin ON vmin.org_profile_id = ppp.parent_org_profile_id AND vmin.record_status = 1
                        JOIN pm_org_profile  pmin ON pmin.org_profile_id = vmin.org_profile_id AND pmin.org_type_id = 2 
                        WHERE vptj.record_status = 1
                ) org ON con.owner_org_profile_id = org.ptj_profile_id
                LEFT JOIN (
                    SELECT pat.ORG_PROFILE_ID,
                    adr.address_name||', '||adr.address_1||' '||adr.address_2||' '||adr.address_3||
                    ', '||adr.postcode||' '||c.city_name||', '||
                    DECODE(s.state_name, 'SARAWAK', dv.division_name||', ', '') ||
                    s.state_name AS address ,
                    s.state_code,s.state_name,
                    d.district_name, d.district_code,
                    dv.division_name, dv.division_code, 
                    c.city_name, c.CITY_CODE 
                    FROM     pm_address_type pat,pm_address adr , pm_state s, pm_district d,pm_division dv, pm_city c 
                    WHERE pat.ADDRESS_ID  = adr.ADDRESS_ID 
                    AND adr.state_id = s.state_id 
                    AND adr.district_id = d.district_id 
                    AND adr.division_id = dv.division_id (+)
                    AND adr.city_id = c.city_id 
                    AND pat.ADDRESS_TYPE = 'B'
                    AND pat.RECORD_STATUS = 1  
                ) ptj_addr ON  con.owner_org_profile_id =  ptj_addr.org_profile_id
                JOIN CT_AMENDMENT am ON  con.current_contract_ver_id = am.contract_ver_id
                left join ct_agreement ag on ag.AMENDMENT_ID = am.AMENDMENT_ID
                JOIN CT_Workflow_Status acs ON am.contract_ver_id = acs.doc_id AND acs.is_current = 1 
                    AND acs.record_status = 1 AND acs.doc_type IN ('FA')
                    AND acs.status_id IN (51500,51450,51420,51410,51405,51400,51320,51300,51260,51250,51210,51200,51150,51100,51050,51000,51800) 
                    and ag.AGREEMENT_ID is null
                JOIN pm_status_desc d ON acs.status_id = d.status_id AND d.language_code = 'en'     
            WHERE 
            con.CONTRACT_NO in (
                            SELECT  DISTINCT a.CUSTOMATTRIBUTESTRING1 AS DOC_NO 
                            FROM wftask@NGEPSOA a 
                            WHERE state = 'ASSIGNED' 
                            AND  a.CUSTOMATTRIBUTESTRING2  in ('FA','SA')
                            AND a.compositename = 'Contract_Management' 
                            and a.state = 'ASSIGNED' 
                            AND a.compositeversion IN ('1.0.9',
                                '1.0.10.1',
                                '1.0.10',
                                '1.0.10.2')
                            AND a.CUSTOMATTRIBUTESTRING1 IN 
                            (
                                SELECT DISTINCT t.contract_no  AS doc_no 
                                FROM CT_Workflow_Status acs
                                JOIN CT_AMENDMENT amd ON acs.doc_id = amd.contract_ver_id AND acs.is_current = 1 AND acs.doc_type = 'FA'
                                left join ct_agreement ag on ag.AMENDMENT_ID = amd.AMENDMENT_ID,
                                pm_status_desc d, ct_contract t
                                WHERE 
                                acs.status_id IN (51500,51450,51420,51410,51405,51400,51320,51300,51260,51250,51210,51200,51150,51100,51050,51000,51800)
                                AND acs.record_status = 1 
                                AND acs.status_id = d.status_id
                                AND d.language_code = 'en'
                                AND amd.contract_ver_id = t.current_contract_ver_id
                            -- AND a.CUSTOMATTRIBUTESTRING2 = acs.doc_type
                                and ag.AGREEMENT_ID is null
                            )
            )");
        return $query;
    }

    public function listCtAgreementNotNullQuery($carian) {
        $whereCarian = "";
        if(strlen($carian) >  0) {
            $whereCarian = " AND ct.CONTRACT_NO = '$carian' ";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct(ct.CONTRACT_NO),ct.CONTRACT_ID,ca.*, ca.AMENDMENT_ID as amid 
                --    a.compositeinstanceid AS task_instance_id, a.state AS task_state, a.assignees AS task_assignees 
                    from ct_contract_ver cv 
                    join ct_amendment ca on cv.CONTRACT_VER_ID = ca.CONTRACT_VER_ID
                    join ct_contract_value val on ca.CONTRACT_VER_ID = val.CONTRACT_VER_ID
                    join ct_workflow_status wf on (ca.CONTRACT_VER_ID = wf.DOC_ID and wf.DOC_TYPE = cv.DOC_TYPE 
                            and wf.IS_CURRENT = 1 and wf.STATUS_ID in (51700))
                    join ct_agreement ag on ag.AMENDMENT_ID = ca.AMENDMENT_ID 
                    join ct_contract ct on ct.CURRENT_CONTRACT_VER_ID = cv.CONTRACT_VER_ID
                    /*join wftask@NGEPSOA a on ( a.CUSTOMATTRIBUTESTRING1 = ct.CONTRACT_NO and a.state = 'ASSIGNED' 
                                AND  a.CUSTOMATTRIBUTESTRING2  in ('FA','SA') AND a.compositename = 'Contract_Management'  
                                AND a.compositeversion IN ('1.0.9', '1.0.10.1',
                                    '1.0.10', '1.0.10.2') )*/
                where cv.DOC_TYPE = 'FA' and ag.AGREEMENT_ID is not null
                AND ct.contract_no in ('CT190000000020906',
                'CT190000000030603',
                'CT190000000031932',
                'CT190000000047097',
                'CT190000000048634',
                'CT190000000050209',
                'CT190000000053645',
                'CT190000000053646',
                'CT190000000053648',
                'CT190000000053664',
                'CT190000000053695',
                'CT190000000053793',
                'CT200000000002426',
                'CT200000000002427',
                'CT200000000003005',
                'CT200000000005171',
                'CT200000000006860',
                'CT200000000008531',
                'CT200000000008533',
                'CT200000000008905',
                'CT200000000011301',
                'CT200000000012001',
                'CT200000000014486',
                'CT200000000015664',
                'CT200000000016480',
                'CT200000000024759',
                'CT200000000026605',
                'CT200000000029518',
                'CT200000000030614',
                'CT200000000031116',
                'CT200000000031515',
                'CT200000000031662',
                'CT200000000032613',
                'CT200000000033009',
                'CT200000000033094',
                'CT200000000033125',
                'CT200000000033183',
                'CT200000000033193',
                'CT210000000000456',
                'CT210000000001247',
                'CT210000000001930',
                'CT210000000002376',
                'CT210000000002626',
                'CT210000000003253',
                'CT210000000003698',
                'CT210000000004554',
                'CT210000000004724',
                'CT210000000006224',
                'CT210000000007360',
                'CT210000000007526',
                'CT210000000008380',
                'CT210000000009943',
                'CT210000000010471',
                'CT210000000011653',
                'CT210000000011971',
                'CT210000000011972',
                'CT210000000012248',
                'CT210000000012453',
                'CT210000000012493',
                'CT210000000012593',
                'CT210000000012598',
                'CT210000000013834',
                'CT210000000013864',
                'CT210000000014665',
                'CT210000000015690',
                'CT210000000016886',
                'CT210000000018009',
                'CT210000000018467',
                'CT210000000018596',
                'CT210000000019621',
                'CT210000000020154',
                'CT210000000020402',
                'CT210000000020892',
                'CT210000000020952',
                'CT210000000021185',
                'CT210000000021460',
                'CT210000000021504',
                'CT210000000023724',
                'CT210000000023755',
                'CT210000000024914',
                'CT210000000025041',
                'CT210000000025222',
                'CT210000000025364',
                'CT210000000025401',
                'CT210000000025613',
                'CT210000000026267',
                'CT210000000026462',
                'CT210000000027209',
                'CT210000000027217',
                'CT210000000027275',
                'CT210000000027756',
                'CT210000000029994',
                'CT210000000030080',
                'CT210000000030431',
                'CT210000000030821',
                'CT210000000031025',
                'CT210000000031036',
                'CT210000000031566',
                'CT210000000031742',
                'CT210000000032285',
                'CT210000000032316',
                'CT210000000032884',
                'CT210000000032902',
                'CT210000000032907',
                'CT210000000033014',
                'CT210000000033152',
                'CT210000000033175',
                'CT210000000033200',
                'CT210000000033394',
                'CT210000000033449',
                'CT210000000033525',
                'CT210000000033596',
                'CT220000000000088',
                'CT220000000000204',
                'CT220000000000212',
                'CT220000000000967',
                'CT220000000001381',
                'CT220000000001666',
                'CT220000000002417',
                'CT220000000002419',
                'CT220000000002490',
                'CT220000000002963',
                'CT220000000003015',
                'CT220000000003210',
                'CT220000000003285',
                'CT220000000003752',
                'CT220000000004094',
                'CT220000000004153',
                'CT220000000004261',
                'CT220000000004370',
                'CT220000000004462',
                'CT220000000004547',
                'CT220000000004685',
                'CT220000000004697',
                'CT220000000004752',
                'CT220000000005019',
                'CT220000000005345',
                'CT220000000005513',
                'CT220000000005610',
                'CT220000000005617',
                'CT220000000005995',
                'CT220000000006016',
                'CT220000000006020',
                'CT220000000006187',
                'CT220000000006637',
                'CT220000000006774',
                'CT220000000006854',
                'CT220000000007089',
                'CT220000000007602',
                'CT220000000007639',
                'CT220000000007670',
                'CT220000000007862',
                'CT220000000008030',
                'CT220000000008198',
                'CT220000000008450',
                'CT220000000008453',
                'CT220000000008523',
                'CT220000000008548',
                'CT220000000008604',
                'CT220000000008766',
                'CT220000000008911',
                'CT220000000008916',
                'CT220000000008923',
                'CT220000000008927',
                'CT220000000009526',
                'CT220000000010134',
                'CT220000000010257',
                'CT220000000010527',
                'CT220000000010835',
                'CT220000000011215',
                'CT220000000011237',
                'CT220000000011238',
                'CT220000000012105',
                'CT220000000012404',
                'CT220000000013284',
                'CT220000000013420',
                'CT220000000013461',
                'CT220000000013767',
                'CT220000000013874',
                'CT220000000013965',
                'CT220000000014141',
                'CT220000000014142',
                'CT220000000014250',
                'CT220000000015118',
                'CT220000000015213',
                'CT220000000015279',
                'CT220000000015494',
                'CT220000000015548',
                'CT220000000015566',
                'CT220000000015594',
                'CT220000000015626',
                'CT220000000015739',
                'CT220000000015889',
                'CT220000000015913',
                'CT220000000015956',
                'CT220000000017037',
                'CT220000000017039',
                'CT220000000017184',
                'CT220000000017427',
                'CT220000000017642',
                'CT220000000017662',
                'CT220000000018001',
                'CT220000000018196',
                'CT220000000018595',
                'CT220000000018728',
                'CT220000000018752',
                'CT220000000018862',
                'CT220000000018905',
                'CT220000000019020',
                'CT220000000019087',
                'CT220000000019099',
                'CT220000000019248',
                'CT220000000019365',
                'CT220000000019403',
                'CT220000000019443',
                'CT220000000019481',
                'CT220000000019663',
                'CT220000000020327',
                'CT220000000020328',
                'CT220000000020370',
                'CT220000000020793',
                'CT220000000021020',
                'CT220000000022946',
                'CT220000000022994',
                'CT220000000023187',
                'CT220000000023324',
                'CT220000000023723',
                'CT220000000023725',
                'CT220000000023726',
                'CT220000000023746',
                'CT220000000024239',
                'CT220000000024269',
                'CT220000000024299',
                'CT220000000024665',
                'CT220000000024755',
                'CT220000000024966',
                'CT220000000025465',
                'CT220000000025745',
                'CT220000000026705',
                'CT220000000026709',
                'CT220000000028006',
                'CT220000000028197',
                'CT220000000028438',
                'CT220000000028703',
                'CT220000000029120',
                'CT220000000029499',
                'CT220000000029681',
                'CT220000000030691',
                'CT220000000030760',
                'CT220000000031070',
                'CT220000000031082',
                'CT220000000031123',
                'CT220000000031125',
                'CT220000000031128',
                'CT220000000031131',
                'CT220000000031176',
                'CT220000000031468',
                'CT220000000031482',
                'CT220000000032572',
                'CT220000000032583',
                'CT220000000032589',
                'CT220000000032636',
                'CT220000000032666',
                'CT220000000032679',
                'CT220000000032710',
                'CT220000000032716',
                'CT220000000032719',
                'CT220000000032724',
                'CT220000000032727',
                'CT220000000032772',
                'CT220000000032786',
                'CT220000000032800',
                'CT220000000032881',
                'CT220000000032901',
                'CT220000000032917',
                'CT220000000032932',
                'CT220000000032937',
                'CT220000000032951',
                'CT220000000032963',
                'CT220000000032968',
                'CT220000000032976',
                'CT220000000033002',
                'CT220000000033007',
                'CT220000000033030',
                'CT220000000033485',
                'CT220000000034013',
                'CT220000000034219',
                'CT220000000034263',
                'CT220000000034723',
                'CT220000000035445',
                'CT220000000035450',
                'CT220000000035729',
                'CT220000000036259',
                'CT220000000036355',
                'CT220000000036440',
                'CT220000000036612',
                'CT220000000036678',
                'CT220000000037003',
                'CT220000000037065',
                'CT220000000037226',
                'CT220000000037236',
                'CT220000000037237',
                'CT220000000037238',
                'CT220000000037372',
                'CT220000000037401',
                'CT230000000000029',
                'CT230000000000042',
                'CT230000000000211',
                'CT230000000000254',
                'CT230000000000484',
                'CT230000000000525',
                'CT230000000000544',
                'CT230000000000546',
                'CT230000000000587',
                'CT230000000000779',
                'CT230000000000916',
                'CT230000000001057',
                'CT230000000001091',
                'CT230000000001552',
                'CT230000000001781',
                'CT230000000001788',
                'CT230000000001843',
                'CT230000000001905',
                'CT230000000001973',
                'CT230000000002081',
                'CT230000000002131',
                'CT230000000002242',
                'CT230000000002584',
                'CT230000000002855',
                'CT230000000002881',
                'CT230000000002907',
                'CT230000000002944',
                'CT230000000002986',
                'CT230000000003038',
                'CT230000000003067',
                'CT230000000003133',
                'CT230000000003216',
                'CT230000000003271',
                'CT230000000003381',
                'CT230000000003464',
                'CT230000000003603',
                'CT230000000003978',
                'CT230000000004171',
                'CT230000000004227',
                'CT230000000004404',
                'CT230000000004562',
                'CT230000000004565',
                'CT230000000004566',
                'CT230000000004567',
                'CT230000000004568',
                'CT230000000004572',
                'CT230000000004581',
                'CT230000000004624',
                'CT230000000004628',
                'CT230000000004629',
                'CT230000000004630',
                'CT230000000004634',
                'CT230000000004635',
                'CT230000000004636',
                'CT230000000004642',
                'CT230000000004643',
                'CT230000000004644',
                'CT230000000004645',
                'CT230000000004648',
                'CT230000000004666',
                'CT230000000004675',
                'CT230000000004676',
                'CT230000000004678',
                'CT230000000004683',
                'CT230000000004685',
                'CT230000000004693',
                'CT230000000004698',
                'CT230000000004701',
                'CT230000000004706',
                'CT230000000004710',
                'CT230000000004711',
                'CT230000000004713',
                'CT230000000004714',
                'CT230000000004736',
                'CT230000000004742',
                'CT230000000004767',
                'CT230000000004769',
                'CT230000000004770',
                'CT230000000004772',
                'CT230000000004773',
                'CT230000000004774',
                'CT230000000004777',
                'CT230000000004803',
                'CT230000000004804',
                'CT230000000004835',
                'CT230000000004839',
                'CT230000000004840',
                'CT230000000004841',
                'CT230000000004842',
                'CT230000000004847',
                'CT230000000004896',
                'CT230000000004897',
                'CT230000000004913',
                'CT230000000004914',
                'CT230000000004916',
                'CT230000000004923',
                'CT230000000004924',
                'CT230000000004926',
                'CT230000000004929',
                'CT230000000004930',
                'CT230000000004931',
                'CT230000000004932',
                'CT230000000004934',
                'CT230000000004945',
                'CT230000000004973',
                'CT230000000005025',
                'CT230000000005029',
                'CT230000000005030',
                'CT230000000005037',
                'CT230000000005038',
                'CT230000000005057',
                'CT230000000005088',
                'CT230000000005094',
                'CT230000000005168',
                'CT230000000005246',
                'CT230000000005247',
                'CT230000000005366',
                'CT230000000005432',
                'CT230000000005590',
                'CT230000000005599',
                'CT230000000005688',
                'CT230000000005991',
                'CT230000000005995',
                'CT230000000006072',
                'CT230000000006417',
                'CT230000000006490',
                'CT230000000006675',
                'CT230000000007090',
                'CT230000000007142',
                'CT230000000007150',
                'CT230000000007507',
                'CT230000000007558',
                'CT230000000007879',
                'CT230000000007906',
                'CT230000000008022',
                'CT230000000008056',
                'CT230000000008174',
                'CT230000000008392',
                'CT230000000008571',
                'CT230000000008610',
                'CT230000000008619',
                'CT230000000008901',
                'CT230000000009188',
                'CT230000000009493',
                'CT230000000009591',
                'CT230000000009684',
                'CT230000000009688',
                'CT230000000009700',
                'CT230000000009795',
                'CT230000000009899',
                'CT230000000010070',
                'CT230000000010288',
                'CT230000000010314',
                'CT230000000010317',
                'CT230000000010393',
                'CT230000000010409',
                'CT230000000010527',
                'CT230000000010876',
                'CT230000000011563',
                'CT230000000011584',
                'CT230000000011929',
                'CT230000000012016',
                'CT230000000012409',
                'CT230000000012957',
                'CT230000000013015',
                'CT230000000013055',
                'CT230000000013552',
                'CT230000000013735',
                'CT230000000013803',
                'CT230000000013929',
                'CT230000000013992',
                'CT230000000014146',
                'CT230000000014712',
                'CT230000000014775',
                'CT230000000014990',
                'CT230000000014995',
                'CT230000000015037',
                'CT230000000015344',
                'CT230000000015488',
                'CT230000000015489',
                'CT230000000015719',
                'CT230000000015886',
                'CT230000000016405',
                'CT230000000016407',
                'CT230000000016410',
                'CT230000000016412',
                'CT230000000016639',
                'CT230000000016655',
                'CT230000000016769',
                'CT230000000017345',
                'CT230000000017385',
                'CT230000000017663',
                'CT230000000017921',
                'CT230000000018125',
                'CT230000000018375',
                'CT230000000018676',
                'CT230000000019322',
                'CT230000000019351',
                'CT230000000019436',
                'CT230000000019597',
                'CT230000000019808',
                'CT230000000019881',
                'CT230000000020040',
                'CT230000000020227',
                'CT230000000020243',
                'CT230000000020420',
                'CT230000000020632',
                'CT230000000020877',
                'CT230000000021415',
                'CT230000000021594',
                'CT230000000021766',
                'CT230000000021852',
                'CT230000000021881',
                'CT230000000021929',
                'CT230000000021934',
                'CT230000000022110',
                'CT230000000022466',
                'CT230000000022548',
                'CT230000000022741',
                'CT230000000022990',
                'CT230000000023009',
                'CT230000000023046',
                'CT230000000023425',
                'CT230000000023594',
                'CT230000000023596',
                'CT230000000023698',
                'CT230000000023765',
                'CT230000000024077',
                'CT230000000024256',
                'CT230000000024262',
                'CT230000000024315',
                'CT230000000024411',
                'CT230000000024421',
                'CT230000000024572',
                'CT230000000024587',
                'CT230000000024613',
                'CT230000000024656',
                'CT230000000024757',
                'CT230000000024761',
                'CT230000000024973',
                'CT230000000025049',
                'CT230000000025124',
                'CT230000000025380',
                'CT230000000025488',
                'CT230000000025717',
                'CT230000000025822',
                'CT230000000025838',
                'CT230000000025844',
                'CT230000000025862',
                'CT230000000026065',
                'CT230000000026290',
                'CT230000000026437',
                'CT230000000026460',
                'CT230000000026502',
                'CT230000000026709',
                'CT230000000026780',
                'CT230000000027618',
                'CT230000000027785',
                'CT230000000027789',
                'CT230000000027872',
                'CT240000000000754',
                'CT240000000000978',
                'CT240000000001090',
                'CT240000000001150',
                'CT240000000001405',
                'CT240000000001573',
                'CT240000000001586',
                'CT240000000001791',
                'CT240000000001998',
                'CT240000000002052',
                'CT240000000002577',
                'CT240000000002694',
                'CT240000000002695',
                'CT240000000002961',
                'CT240000000003331',
                'CT240000000003494',
                'CT240000000004020',
                'CT240000000004043',
                'CT240000000004562',
                'CT240000000004867',
                'CT240000000005786',
                'CT240000000006084',
                'CT240000000006119',
                'CT240000000006307',
                'CT240000000006751',
                'CT240000000006947',
                'CT240000000008274',
                'CT240000000008842',
                'CT240000000010197',
                'CT240000000010667',
                'CT240000000010926',
                'CT240000000010964',
                'CT240000000011223',
                'CT240000000011273',
                'CT240000000011462',
                'CT240000000011876',
                'CT240000000012015',
                'CT240000000012245',
                'CT240000000015890',
                'CT240000000016700',
                'CT240000000018032',
                'CT240000000018072',
                'CT240000000018136',
                'CT240000000019138',
                'CT240000000019139',
                'CT240000000019427',
                'CT240000000024486',
                'CT240000000025802',
                'CT240000000026014',
                'CT240000000026697',
                'CT240000000026755',
                'CT240000000028641',
                'CT250000000000011',
                'M30101501220001',
                'M31030400220002',
                'M31030400230003',
                'M35203000190002',
                'M35203000210001',
                'M38050100180041',
                'M4159T501190001',
                'M42062801220008',
                'M42149901220001',
                'M48101010180006',
                'M48101010190001',
                'M48201070230003',
                'M51120501200053',
                'M51120501210010',
                'M52010600210003',
                'M53030201240003',
                'M53030201240007',
                'M53152002210001',
                'M53153003210002',
                'M62300201200001',
                'Z0267010600170033')
                 $whereCarian
            order by ca.CREATED_DATE desc");
         $result = array();
            foreach($query as $res) {
                $contractNo = $res->contract_no;
                $list = DB::connection('oracle_bpm_rpt')->select("
                select a.compositeinstanceid AS task_instance_id, a.state AS task_state, a.assignees AS task_assignees 
                from wftask a WHERE a.CUSTOMATTRIBUTESTRING1 = '$contractNo' and a.state = 'ASSIGNED' 
                AND  a.CUSTOMATTRIBUTESTRING2  in ('FA','SA') AND a.compositename = 'Contract_Management'  
                AND a.compositeversion IN ('1.0.9', '1.0.10.1',
                '1.0.10', '1.0.10.2') ");
                if(isset($list)) {
                    foreach($list as $list) {
                        $v = array('contract_no' => $contractNo,
                        'contract_id' => $res->contract_id,
                        'amid' => $res->amid,
                        'task_instance_id' => $list->task_instance_id,
                        'task_state' => $list->task_state,
                        'task_assignees' => $list->task_assignees);
                        array_push($result, $v);
                    } 
                }
            }
        return $result;
    } 

    public function listCtAgreementMissingQuery($carian) {
        $whereCarian = "";
        if(strlen($carian) >  0) {
            $whereCarian = " AND ct.CONTRACT_NO = '$carian' ";
        }
        $query = DB::connection('oracle_nextgen_rpt')->select("
                        select distinct(ct.CONTRACT_NO),ct.CONTRACT_ID, ct.latest_contract_ver_id, ct.current_contract_ver_id, ca.* 
                        from ct_contract_ver cv join ct_amendment ca on cv.CONTRACT_VER_ID = ca.CONTRACT_VER_ID
                        join ct_contract_value val on ca.CONTRACT_VER_ID = val.CONTRACT_VER_ID
                        join ct_workflow_status wf on (ca.CONTRACT_VER_ID = wf.DOC_ID and wf.DOC_TYPE = cv.DOC_TYPE 
                        and wf.IS_CURRENT = 1 and wf.STATUS_ID in (51700))
                        join ct_agreement ag on ag.AMENDMENT_ID = ca.AMENDMENT_ID 
                        join ct_contract ct on ct.CURRENT_CONTRACT_VER_ID = cv.CONTRACT_VER_ID   
                        where ct.CONTRACT_ID = cv.CONTRACT_ID and cv.DOC_TYPE = 'FA'
                        and ag.AGREEMENT_ID is not null 
                        and ct.CONTRACT_NO IN ('CT210000000002399',
                        'CT210000000028212',
                        'CT210000000029680',
                        'CT210000000031887',
                        'CT210000000033152',
                        'CT220000000001225',
                        'CT220000000001373',
                        'CT220000000002674',
                        'CT220000000004153',
                        'CT220000000006016',
                        'CT220000000015956',
                        'CT220000000018595',
                        'CT220000000018626',
                        'CT220000000019631',
                        'CT220000000027763',
                        'CT220000000031200',
                        'CT220000000035631',
                        'CT220000000037237',
                        'CT230000000000537',
                        'CT230000000000759',
                        'CT230000000000779',
                        'CT230000000000808',
                        'CT230000000000820',
                        'CT230000000001508',
                        'CT230000000001511',
                        'CT230000000001535',
                        'CT230000000001716',
                        'CT230000000003038',
                        'CT230000000003381',
                        'CT230000000004742',
                        'CT230000000005247',
                        'CT230000000005590',
                        'CT230000000006717',
                        'CT230000000007090',
                        'CT230000000007346',
                        'CT230000000007562',
                        'CT230000000011126',
                        'CT230000000018454',
                        'CT230000000019606',
                        'CT230000000020877',
                        'CT240000000000598',
                        'CT240000000000601',
                        'CT240000000000870',
                        'CT240000000000946',
                        'CT240000000001227',
                        'CT240000000004132',
                        'CT240000000006095',
                        'CT240000000006470',
                        'CT240000000011826',
                        'CT240000000013622',
                        'CT240000000015890',
                        'CT240000000018408',
                        'CT240000000024486',
                        'CT240000000026697',
                        'M53250101230001',
                        'M54010200190007',
                        'M73200101240002')
                        and ct.CONTRACT_NO NOT IN (select doc_no from CT_AGREEMENT_CANCEL_TEMP where doc_type in 'SA')
                        and trunc(ca.CREATED_DATE)  < to_date('22/02/2025', 'DD/MM/YYYY')
                        $whereCarian order by ca.CREATED_DATE desc");
              
                $result = array();
                foreach($query as $res) {
                    $contractNo = $res->contract_no;
                    $list = DB::connection('oracle_bpm_rpt')->select("
                    select a.compositeinstanceid AS task_instance_id, a.state AS task_state, a.assignees AS task_assignees 
                    from wftask a WHERE a.CUSTOMATTRIBUTESTRING1 = '$contractNo' and a.state = 'ASSIGNED'
                    and a.CUSTOMATTRIBUTESTRING2  in ('SA','') ");
                    if(isset($list)) {
                        foreach($list as $list) {
                            $v = array('contract_no' => $contractNo,
                            'contract_id' => $res->contract_id,
                            'task_instance_id' => $list->task_instance_id,
                            'task_state' => $list->task_state,
                            'task_assignees' => $list->task_assignees);
                            array_push($result, $v);
                        } 
                    }
                } 
            return $result;
    }
}
