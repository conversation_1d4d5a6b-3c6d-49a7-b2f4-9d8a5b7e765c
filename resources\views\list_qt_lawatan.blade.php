@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/qt/lawatan')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Klik carian di sini ...">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Maklumat Taklimat atau Lawatan Tapak Bagi Sebut Harga <br>
            <small>Senarai semua pembekal bagi sebut harga.</small>
        </h1>
    </div>
</div>
@if($qtinfo == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif
@if($qtinfo)
<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Quotation/Tender : {{ $qtinfo->qt_no }}</strong></h1>
        </div>
        <div class="row">
            <div class="col-md-9">
                <h6><strong>{{ $qtinfo->qt_title }}</strong></h6>
                <address>
                    <strong>QT No.</strong> : {{ $qtinfo->qt_no }}<br />
                    <strong>File No.</strong> : {{ $qtinfo->file_no }}<br />
                    <strong>Publish Date</strong> : {{ $qtinfo->publish_date }}<br />
                    <strong>Proposal Start Date</strong> : {{ $qtinfo->proposal_start_date }}<br />
                    <strong>Closing Date</strong> : {{ $qtinfo->closing_date }}<br />
                </address>
            </div>
            <!--div class="col-md-3">
                <div class="table-responsive">
                    <table id="general-table" class="table table-vcenter table-hover table-borderless">
                        <tbody>
                        <tr>
                            <td class="text-center"><span class="badge label-danger"><strong>B</strong></span></td>
                            <td class="text-left">Failed to attend briefing and site visit</td>
                        </tr>
                        <tr>
                            <td class="text-center"><span class="badge label-danger"><strong>C</strong></span></td>
                            <td class="text-left">Supplier Criteria</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div -->
        </div>
        <div class="row">
            @if($listdata)
            <div class="table-responsive">
                <table id="tracking-datatable" class="table table-striped table-vcenter">
                    <thead>
                        <tr>
                            <th class="text-left">Company Name</th>
                            <th class="text-left">MOF No.</th>
                            <th class="text-center">Disqualified Stage</th>
                            <th class="text-center">Attended</th>
                            <th class="text-center">BSV Type</th>
                            <th class="text-center">Zone</th>   
                            <th class="text-center">Description</th>
                            <th class="text-center">Post Registered</th>
                            <th class="text-center">Approval Request ID</th>
                            <th class="text-center">Approver Action ID</th>
                        </tr>
                    </thead>

                    <tbody>
                        @foreach ($listdata as  $indexKey => $data)
                        <tr>
                            <td class="text-left">{{ $data->company_name }}</td>
                            <td class="text-left">{{ $data->mof_no }}</td>
                            <td class="text-center">{{ $data->disqualified_stage }}</td>
                            <td class="text-center">{{ $data->is_attended }}</td>
                            <td class="text-center">{{ $data->bsv_type }}</td>
                            <td class="text-center">{{ $data->zonename }}</td>
                            <!--<td class="text-center">{{ $data->bsv_desc }}</td>-->
                            <td class="text-center">{{ str_limit($data->bsv_desc, $limit = 50, $end = '...') }}</td>
                            <td class="text-center">{{ $data->is_post_registered }}</td>
                            <td class="text-center">{{ $data->qt_approval_request_id }}</td>
                            <td class="text-center">{{ $data->approver_action_id }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @endif
        </div>
    </div>
</div>
@endif


@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                    TablesDatatables.init();
                });</script>
<script>

</script>
@endsection