<?php

namespace App\Console\Commands;

use App\Services\Traits\BpmApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class HandleBPMQTMaintenanceStatusReport extends Command
{
    use BpmApiService;

    protected $signature = 'bpm:distinct-qt-maintenance-status';
    protected $description = 'Report the distinct statuses of Quotation Tender Maintenance BPM instances';
    private $processableRecords = [];
    private $statusCounts = [];
    private $eligibilityInfo = [
        'eligible' => 0,
        'non_eligible' => 0,
        'total_processable' => 0
    ];

    public function handle()
    {
        $this->info('Starting quotation workflow status analysis...');

        try {
            // First Query - Get Flow IDs - EXACTLY the same as original
            $flowInstances = DB::connection('oracle_bpm_rpt')
                ->select("SELECT DISTINCT
                    c.CIKEY,
                    c.COMPONENT_NAME,
                    c.CMPST_ID,
                    c.COMPOSITE_NAME,
                    c.COMPOSITE_REVISION,
                    c.FLOW_ID,
                    c.TITLE,
                    c.STATE,
                    c.MODIFY_DATE
                FROM cube_instance c, SCA_FLOW_INSTANCE s
                WHERE c.FLOW_ID = s.FLOW_ID
                AND s.ACTIVE_COMPONENT_INSTANCES <> 0
                AND COMPOSITE_NAME = 'SourcingQT'
                AND c.STATE NOT IN (5, 8)
                AND c.FLOW_ID NOT IN (-1,-2)
                AND c.COMPONENT_NAME = 'QuotationTenderMaintenance'
                AND NOT EXISTS (
                    SELECT 1
                    FROM wftask w
                    WHERE w.COMPOSITENAME = c.COMPOSITE_NAME
                    AND w.STATE IN ('ASSIGNED', 'SUSPENDED')
                    AND w.COMPOSITEINSTANCEID = c.CMPST_ID
                )
                ORDER BY C.MODIFY_DATE DESC");

            foreach ($flowInstances as $instance) {
                $this->processFlowInstance($instance);
            }

            // Check eligibility for all processable records
            if (!empty($this->processableRecords)) {
                $this->checkEligibilityForRecords();
            }

            $this->displayResults();

        } catch (\Exception $e) {
            $this->error('Error occurred: ' . $e->getMessage());
            Log::error('Error in quotation workflow status analysis: ' . $e->getMessage());
        }
    }

    private function processFlowInstance($instance)
    {
        // Second Query - Get Document Numbers - EXACTLY the same as original
        $documents = DB::connection('oracle_bpm_rpt')
            ->select("SELECT DISTINCT 
                CUSTOMATTRIBUTESTRING1 AS doc_no,
                CUSTOMATTRIBUTESTRING2 AS doc_type,
                PROTECTEDTEXTATTRIBUTE1 AS doc_no_other
            FROM wftask
            WHERE flow_id = ?
            AND (CUSTOMATTRIBUTESTRING1 IS NOT NULL
            OR PROTECTEDTEXTATTRIBUTE1 IS NOT NULL)",
                [$instance->flow_id]
            );

        if (empty($documents)) {
            Log::info("No documents found for Flow ID: {$instance->flow_id}");
            return;
        }

        // Validate that all documents have matching document numbers - EXACTLY the same as original
        $firstDoc = $documents[0];
        $firstDocNo = $firstDoc->doc_no ?? $firstDoc->doc_no_other;

        foreach ($documents as $document) {
            $currentDocNo = $document->doc_no ?? $document->doc_no_other;

            if ($currentDocNo !== $firstDocNo) {
                Log::warning("Flow ID {$instance->flow_id} has inconsistent document numbers", [
                    'first_doc_no' => $firstDocNo,
                    'current_doc_no' => $currentDocNo
                ]);
                return;
            }
        }

        $this->processDocument($documents[0], $instance);
    }

    private function processDocument($document, $instance)
    {
        try {
            $docNo = $document->doc_no ?? $document->doc_no_other;

            if (!$docNo) {
                Log::info('No document number found, skipping...');
                return;
            }

            // Query to get Status - EXACTLY the same as original
            $status = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT
                    B.DOC_TYPE,
                    B.DOC_ID,
                    A.QT_NO,
                    A.CLOSING_DATE,
                    C.STATUS_ID,
                    D.STATUS_NAME,
                    A.QT_ID
                FROM SC_QT A
                INNER JOIN SC_WORKFLOW_STATUS B ON A.QT_ID = B.DOC_ID
                INNER JOIN PM_STATUS C ON B.STATUS_ID = C.STATUS_ID
                INNER JOIN PM_STATUS_DESC D ON C.STATUS_ID = D.STATUS_ID
                WHERE D.LANGUAGE_CODE = 'en'
                AND B.IS_CURRENT = 1
                AND B.DOC_TYPE = 'QT'
                AND A.QT_NO = ?
                ORDER BY B.CREATED_DATE DESC",
                    [$docNo]
                );

            if (empty($status)) {
                Log::info("No status found for document {$docNo}", [
                    'flow_id' => $instance->flow_id,
                    'instance_id' => $instance->cmpst_id
                ]);
                return;
            }

            $currentStatus = $status[0];

            // Track the status for our later report
            $statusId = $currentStatus->status_id;
            $statusName = $currentStatus->status_name;

            if (!isset($this->statusCounts[$statusId])) {
                $this->statusCounts[$statusId] = [
                    'status_id' => $statusId,
                    'status_name' => $statusName,
                    'count' => 0
                ];
            }
            $this->statusCounts[$statusId]['count']++;

            // Format dates for display
            $modifyDate = $instance->modify_date ?
                Carbon::parse($instance->modify_date)->format('Y-m-d H:i:s') :
                null;

            // Use same logic as original for identifying processable records
            if (
                !in_array($currentStatus->status_id, [60008, 60009]) &&
                in_array($currentStatus->status_id, [60014, 60015])
            ) {
                // If we reach here, this record needs processing
                $this->processableRecords[] = [
                    'cmpst_id' => $instance->cmpst_id,
                    'flow_id' => $instance->flow_id,
                    'component_name' => $instance->component_name,
                    'composite_name' => $instance->composite_name,
                    'doc_no' => $docNo,
                    'doc_id' => $currentStatus->doc_id,
                    'qt_id' => $currentStatus->qt_id,
                    'state' => $instance->state,
                    'modify_date' => $modifyDate,
                    'status_id' => $currentStatus->status_id,
                    'status_name' => $currentStatus->status_name,
                    'closing_date' => null,
                    'eligible_for_termination' => false // Default to false, will be set later
                ];
            }

        } catch (\Exception $e) {
            Log::error("Error processing document", [
                'doc_no' => $docNo ?? 'unknown',
                'flow_id' => $instance->flow_id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Check eligibility for each processable record
     */
    private function checkEligibilityForRecords()
    {
        $this->info("\nChecking eligibility of " . count($this->processableRecords) . " records...");
        $bar = $this->output->createProgressBar(count($this->processableRecords));
        $bar->start();

        foreach ($this->processableRecords as &$record) {
            try {
                $instanceDetails = $this->findAPIProcessManagerBPMByInstance($record['cmpst_id']);

                // Log the raw instance details response
                Log::info("Instance details response for {$record['doc_no']}", [
                    'instanceDetails' => $instanceDetails
                ]);

                $record['module'] = $instanceDetails['status'] === 'Success'
                    ? ($instanceDetails['result']['compositeModule'] ?? 'Unknown')
                    : 'Error getting module';

                // Check if this instance contains the ApproveSupplierAdd component
                $record['eligible_for_termination'] = $this->hasApproveSupplierAddComponent($instanceDetails);

                // Update counters
                if ($record['eligible_for_termination']) {
                    $this->eligibilityInfo['eligible']++;
                } else {
                    $this->eligibilityInfo['non_eligible']++;
                }

                // Log eligibility
                Log::info("Termination eligibility for {$record['doc_no']}", [
                    'eligible' => $record['eligible_for_termination'] ? 'Yes' : 'No',
                    'reason' => $record['eligible_for_termination'] ?
                        'Contains ApproveSupplierAdd component' :
                        'Missing ApproveSupplierAdd component'
                ]);

            } catch (\Exception $e) {
                $record['module'] = 'Error: ' . $e->getMessage();
                $record['eligible_for_termination'] = false;
                $this->eligibilityInfo['non_eligible']++;
                Log::error("Error getting module information", [
                    'composite_id' => $record['cmpst_id'],
                    'document_no' => $record['doc_no'],
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
            $bar->advance();
        }

        $bar->finish();
        $this->eligibilityInfo['total_processable'] = count($this->processableRecords);
        $this->info("\nEligibility check completed!");
    }

    /**
     * Check if instance details contain the ApproveSupplierAdd component
     */
    private function hasApproveSupplierAddComponent($instanceDetails)
    {
        // Check if response is valid
        if (!isset($instanceDetails['status']) || $instanceDetails['status'] !== 'Success') {
            return false;
        }

        // Check if childs array exists
        if (!isset($instanceDetails['result']['childs']) || !is_array($instanceDetails['result']['childs'])) {
            return false;
        }

        // Function to recursively search for component name in the hierarchy
        $findComponent = function ($items, $targetComponent) use (&$findComponent) {
            foreach ($items as $item) {
                // Check current item
                if (isset($item['componentName']) && $item['componentName'] === $targetComponent) {
                    return true;
                }

                // Check children of current item if they exist
                if (isset($item['childs']) && is_array($item['childs']) && count($item['childs']) > 0) {
                    if ($findComponent($item['childs'], $targetComponent)) {
                        return true;
                    }
                }
            }
            return false;
        };

        // Search for ApproveSupplierAdd in the component hierarchy
        return $findComponent($instanceDetails['result']['childs'], 'ApproveSupplierAdd');
    }

    /**
     * Display component hierarchy in readable format
     */
    private function displayComponentHierarchy($components, $level = 0)
    {
        $indent = str_repeat("  ", $level);
        foreach ($components as $component) {
            $this->line($indent . "▸ " . ($component['componentName'] ?? 'Unknown') .
                " (" . ($component['componentType'] ?? 'Unknown') . ")" .
                " [Status: " . ($component['componentStatus'] ?? 'Unknown') . "]");

            if (isset($component['childs']) && is_array($component['childs']) && count($component['childs']) > 0) {
                $this->displayComponentHierarchy($component['childs'], $level + 1);
            }
        }
    }

    private function displayResults()
    {
        // First display the same table as the original
        if (empty($this->processableRecords)) {
            $this->info('No records require processing.');
            Log::info('No records found for processing');
        } else {
            $this->info("\nFound " . count($this->processableRecords) . " records that require processing:");

            // Updated table to include module and eligibility
            $this->table(
                [
                    'Composite ID',
                    'Flow ID',
                    'Component Name',
                    'Document No',
                    'State',
                    'Modified Date',
                    'Status',
                    'Module',
                    'Eligible'
                ],
                array_map(function ($record) {
                    return [
                        $record['cmpst_id'],
                        $record['flow_id'],
                        $record['component_name'],
                        $record['doc_no'],
                        $record['state'],
                        $record['modify_date'],
                        $record['status_name'],
                        $record['module'] ?? 'Unknown',
                        $record['eligible_for_termination'] ? 'Yes' : 'No'
                    ];
                }, $this->processableRecords)
            );

            // Display eligibility summary
            $this->info("\n=== ELIGIBILITY SUMMARY ===");
            $this->info("Total processable records: {$this->eligibilityInfo['total_processable']}");
            $this->info("Eligible for termination: {$this->eligibilityInfo['eligible']}");
            $this->info("Not eligible for termination: {$this->eligibilityInfo['non_eligible']}");

            if ($this->eligibilityInfo['total_processable'] > 0) {
                // Calculate percentage
                $eligiblePercent = ($this->eligibilityInfo['eligible'] / $this->eligibilityInfo['total_processable']) * 100;
                $this->info("Percentage eligible: " . number_format($eligiblePercent, 2) . "%");
            }
        }

        // NOW DISPLAY THE DISTINCT QT STATUSES
        $this->info("\n=== DISTINCT QT STATUSES FOUND ===");

        if (empty($this->statusCounts)) {
            $this->info("No QT statuses found.");
            return;
        }

        // Sort status counts by ID for consistent display
        $statusRows = $this->statusCounts;
        usort($statusRows, function ($a, $b) {
            return $a['status_id'] <=> $b['status_id'];
        });

        $this->table(
            ['Status ID', 'Status Name', 'Count'],
            array_map(function ($status) {
                return [
                    $status['status_id'],
                    $status['status_name'],
                    $status['count']
                ];
            }, $statusRows)
        );

        // Show which statuses would be processed by the original command
        $this->info("\n=== PROCESSABLE QT STATUSES ===");
        $this->info("These statuses (60014 or 60015) would be processed by the bpm:terminate-qt-maintenance-stuck command:");

        $processableStatuses = array_filter($statusRows, function ($status) {
            return in_array($status['status_id'], [60014, 60015]);
        });

        if (empty($processableStatuses)) {
            $this->info("No processable statuses found (60014 or 60015).");
        } else {
            $this->table(
                ['Status ID', 'Status Name', 'Count'],
                array_map(function ($status) {
                    return [
                        $status['status_id'],
                        $status['status_name'],
                        $status['count']
                    ];
                }, $processableStatuses)
            );
        }

        // Display count of eligible for termination by status
        if (!empty($this->processableRecords)) {
            $this->info("\n=== ELIGIBILITY BY STATUS ===");

            $eligibilityByStatus = [];
            foreach ($this->processableRecords as $record) {
                $statusId = $record['status_id'];
                $statusName = $record['status_name'];

                if (!isset($eligibilityByStatus[$statusId])) {
                    $eligibilityByStatus[$statusId] = [
                        'status_id' => $statusId,
                        'status_name' => $statusName,
                        'eligible' => 0,
                        'not_eligible' => 0,
                        'total' => 0
                    ];
                }

                if ($record['eligible_for_termination']) {
                    $eligibilityByStatus[$statusId]['eligible']++;
                } else {
                    $eligibilityByStatus[$statusId]['not_eligible']++;
                }
                $eligibilityByStatus[$statusId]['total']++;
            }

            // Convert to array and sort
            $statusEligibility = array_values($eligibilityByStatus);
            usort($statusEligibility, function ($a, $b) {
                return $a['status_id'] <=> $b['status_id'];
            });

            $this->table(
                ['Status ID', 'Status Name', 'Eligible', 'Not Eligible', 'Total', 'Eligible %'],
                array_map(function ($status) {
                    $percentEligible = $status['total'] > 0
                        ? number_format(($status['eligible'] / $status['total']) * 100, 2) . '%'
                        : 'N/A';

                    return [
                        $status['status_id'],
                        $status['status_name'],
                        $status['eligible'],
                        $status['not_eligible'],
                        $status['total'],
                        $percentEligible
                    ];
                }, $statusEligibility)
            );
        }
    }
}