@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianformparameter" action="{{url('/find/identity')}}" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="ic_no" name="ic_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if (Auth::user())
        @if($status == 'success' && $result != null && array_key_exists('messagecode', $result))
        
        <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Identity<br>
                <small>Semakan Identity pengguna</small>
            </h1>
        </div>
        </div>    
        <div class="block block-alt-noborder full">
            <div class="block">
                <!-- Block Title -->
                <div class="block-title">
                    <h2><strong>@if(isset($result['name'])) {{($result['name'])}} @endif</strong></h2>
                </div>
                <!-- END Block Title -->

                <!-- Block Content -->
                <address>
                @if(isset($result['icno']))<strong>IC No. : </strong> {{($result['icno'])}} <br/>@endif
                @if(isset($result['postcode']))    <strong>Postcode : </strong> {{($result['postcode'])}} <br/>@endif
                @if(isset($result['city']))    <strong>City : </strong> {{($result['city'])}} <br/>@endif
                @if(Auth::user()->isPatcherRolesEp())
                    @if(isset($result['addr1']))    <strong>Address 1 : </strong> {{($result['addr1'])}} <br/>@endif
                    @if(isset($result['addr2']))    <strong>Address 2 : </strong> {{($result['addr2'])}} <br/>@endif
                    @if(isset($result['addr3']))    <strong>Address 3 : </strong> {{($result['addr3'])}} <br/>@endif
                    @if(isset($result['city']))    <strong>City : </strong> {{($result['city'])}} <br/>@endif
                    @if(isset($result['statecode']))    <strong>State : </strong> {{strtoupper(($result['statecode']))}} <br/>@endif
                    @if(isset($result['gender']))     <strong>Gender : </strong>{{($result['gender'])}} <br/>@endif
                    @if(isset($result['residential_status']))    <strong>Residential Status : </strong> {{($result['residential_status'])}} <br/>@endif
                    @if(isset($result['citizenship_status']))    <strong style='display:none;'>Citizenship Status : </strong> {{($result['citizenship_status'])}} <br/>@endif
                    @if(isset($result['race']))    <strong>Race : </strong> {{($result['race'])}} <br/>@endif
                    @if(isset($result['religion']))    <strong>Religion : </strong> {{($result['religion'])}} <br/>@endif
                    @if(isset($result['mobile']))    <strong style='display:none;'>Phone No. : </strong> {{strtoupper(($result['mobile']))}} <br/>@endif
                    @if(isset($result['email']))     <strong style='display:none;'>Email: </strong>{{strtoupper(($result['email']))}} <br/>@endif
                    @if(isset($result['record_status']))    <strong>Record Status: </strong> {{strtoupper(($result['record_status']))}} <br/>@endif
                    @if(isset($result['verify_status']))    <strong style='display:none;'>Verify Status: </strong> {{strtoupper(($result['verify_status']))}} <br/>@endif
                @endif
                    
                @if(isset($result['severity']) && $result['severity'] == 'ERR')
                <strong>Message Code : </strong>@if(isset($result['messagecode'])) {{($result['messagecode'])}} <br/>@endif
                <strong>Message : </strong>@if(isset($result['message'])) {{($result['message'])}} <br/>@endif
                @endif
                   
                </address>
                <!-- END Block Content -->
            </div>
        </div>
        @elseif($status == 'error')
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian identity<br>
                    <small>Maaf ada issue technical! <strong class="text-danger">{{$statusDesc}}</strong></small>
                </h1>
            </div>
        </div>
        @else
            <div class="content-header">
                <div class="header-section">
                    <h1>
                        <i class="gi gi-search"></i>Carian identity<br>
                        <small>Tidak dijumpai! Masukkan no. IC pada carian diatas...</small>
                    </h1>
                </div>
            </div>
        @endif
    @endif
@endsection

@section('jsprivate')
<script>
    $( "form#carianformparameter" ).on("submit", function(){
        var gourl = $(this).attr('action')+'?ic_no='+$('#cari').val();
        $(this).attr('action',gourl);
    });
</script>
@endsection