<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use DB;

class CrmController extends Controller {

    public function getDashboardCrm() {
        return view('dashboard.crm', []);
    }

    public function checkMOnitoringIntegrationCRMeP() {

        $updateSupplierInfo = DB::table('integ_logs_scheduler')->where('name', 'UpdateSupplierInfo')->orderBy('date_entered', 'desc')->first();
        $updateSupplierUserInfo = DB::table('integ_logs_scheduler')->where('name', 'UpdateSupplierUserInfo')->orderBy('date_entered', 'desc')->first();
        $updateGovernmentInfo = DB::table('integ_logs_scheduler')->where('name', 'UpdateGovernmentInfo')->orderBy('date_entered', 'desc')->first();
        $updateGovernmentUsersInfo = DB::table('integ_logs_scheduler')->where('name', 'UpdateGovernmentUsersInfo')->orderBy('date_entered', 'desc')->first();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service</th>
                        <th>From Period</th>
                        <th>To Period</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>";
        $html .= "
                <tr>
                    <td style='width: 30%;'><strong>$updateSupplierInfo->name</strong></td>
                    <td><strong>$updateSupplierInfo->date_from</strong></td>    
                    <td><strong>$updateSupplierInfo->date_to</strong></td>  
                    <td class='text-center'><span class='badge label-danger'>$updateSupplierInfo->status</span></td>
                </tr>"
                . "<tr>
                <td style='width: 30%;'><strong>$updateSupplierUserInfo->name</strong></td>
                <td><strong>$updateSupplierUserInfo->date_from</strong></td>    
                <td><strong>$updateSupplierUserInfo->date_to</strong></td>  
                <td class='text-center'><span class='badge label-danger'>$updateSupplierUserInfo->status</span></td>
                </tr>"
                . "<tr>
                    <td style='width: 30%;'><strong>$updateGovernmentInfo->name</strong></td>
                    <td><strong>$updateGovernmentInfo->date_from</strong></td>    
                    <td><strong>$updateGovernmentInfo->date_to</strong></td>  
                    <td class='text-center'><span class='badge label-danger'>$updateGovernmentInfo->status</span></td>
                </tr>"
                . "<tr>
                <td style='width: 30%;'><strong>$updateGovernmentUsersInfo->name</strong></td>
                <td><strong>$updateGovernmentUsersInfo->date_from</strong></td>    
                <td><strong>$updateGovernmentUsersInfo->date_to</strong></td>  
                <td class='text-center'><span class='badge label-danger'>$updateGovernmentUsersInfo->status</span></td>
                </tr>";


        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

}
