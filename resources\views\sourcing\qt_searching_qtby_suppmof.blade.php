@extends('layouts.guest-dash')

@section('content')

    <div class="content-header">
        <div class="header-section">
            <h4>
                <i class="gi gi-search"></i>&nbsp&nbsp&nbsp&nbsp Senarai QT Berdasarkan Penyer<PERSON>
            </h4>
        </div>
    </div>
    <div class="block block-alt-noborder full">
        <div class="block-title epss-title-s1">
        @if ($verifysubmit != null && count($verifysubmit) > 0)
        <h6><i class="block-title"></i>
        &nbsp&nbsp&nbsp&nbsp&nbsp
        &nbsp&nbsp&nbsp&nbsp&nbsp
        &nbsp&nbsp&nbsp&nbsp&nbsp
        <font color="yellow">{{ $verifysubmit[0]->supplier_name }}</font> | <font color="yellow">{{ $verifysubmit[0]->mof_no }}</font> </h6>
                @else
                    <label class="col-md-1 control-label" for="module"> No Records </label>
                @endif 
        </div>
        <div class="block">
            <div class="block-title panel-heading epss-title-s1"><br>
                <form id="form-search-task" action="{{ url('find/qt/findqtbysupp') }}" method="get"
                    class="form-horizontal form-bordered">
                    <label class="col-md-1 control-label" for="module">MOF NO <span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="mof_no" name="mof_no" value="{{ $carianmofno }}" required
                            class="form-control">   
                    </div>
                    <div class="form-actions form-actions-button text-right " style="margin-right:30px;">
                        <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>   
                   </div>
                </form>
            </div>
        </div>
        <!--    AKMAL PEMBEKAL SUBMIT-->
        <!-- AKMAL DISABLE FUNCTION DOWNLOAD 16112023 WILL USE LATER 
        -->
        @if ($verifysubmit != null)

            <div class="block-title">
                <form action="{{ url('/qt/pembekal/submit/download') }}" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" id="mof_no_input" name="mof_no" value="">
                    <div class="form-actions form-actions-button text-right ">
                        <button type="submit" value="1" id="downloadfromdbsubmit" class="btn btn btn-primary"
                            style="float: right;"><i class="fa fa-download"> Download</i></button>
                    </div>
                </form>
                <h1><i class="fa fa-tasks"></i> <strong>Pembekal Submit</strong></h1>
            </div>
            
            
            <div class="table-responsive">
            <label class="col-md-5 control-label" for="module"> SUBMIT PROPOSAL</label>
                <table id="basic-datatable" class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">QT No</th>
                            <th class="text-center">Proposal No</th>
                            <th class="text-center">Submitted</th>
                            <th class="text-center">Proposal Submit Date No</th>
                            <th class="text-center">QT Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($verifysubmit != null)
                            @if (count($verifysubmit) > 0)
                                @foreach ($verifysubmit as $data)
                                    <tr>
                                        <td class="text-center">{{ $data->qt_no }}</td>
                                        <td class="text-center">{{ $data->proposal_no }}</td>
                                        <td class="text-center">{{ $data->submitted }}</td>
                                        <td class="text-center">{{ $data->proposal_submit_date }}</td>
                                        <td class="text-center">{{ $data->qtstatus }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="12">No Records</td>
                                </tr>
                            @endif
                        @endif
                    </tbody>
                </table>
            </div>
        @endif
        <br><br>
        @if ($verifynotsubmit != null)
           <!-- AKMAL DISABLE FUNCTION DOWNLOAD 16112023
                <div class="block-title">
                <form action="{{ url('/qt/pembekal/gagal/download') }}" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" id="doc_no_gagal" name="doc_no" value="">
                    <input type="hidden" id="kategori_gagal" name="kategori" value="">
                    <div class="form-actions form-actions-button text-right ">
                        <button type="submit" value="1" id="downloadfromdbGagal" class="btn btn btn-primary"
                            style="float: right;"><i class="fa fa-download"> Download</i></button>
                    </div>
                </form>
                <h1><i class="fa fa-tasks"></i> <strong>Pembekal Gagal</strong></h1>
            </div>-->
            <!--    AKMAL PEMBEKAL GAGAL-->
            <div class="table-responsive">
            <label class="col-md-5 control-label" for="module">NOT SUBMIT PROPOSAL</label>
                <table id="statuswork-datatable" class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">QT No</th>
                            <th class="text-center">Proposal No</th>
                            <th class="text-center">Submitted</th>
                            <th class="text-center">QT Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($verifynotsubmit != null)
                            @if (count($verifynotsubmit) > 0)
                                @foreach ($verifynotsubmit as $data)
                                    <tr>
                                        <td class="text-center">{{ $data->qt_no }}</td>
                                        <td class="text-center">{{ $data->proposal_no }}</td>
                                        <td class="text-center">{{ $data->submitted }}</td>
                                        <td class="text-center">{{ $data->qtstatus }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="12">No Records</td>
                                </tr>
                            @endif
                        @endif
                    </tbody>
                </table>
            </div>
        @endif
    </div>
    <!-- END Customer Addresses Block -->
    </div>

@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            TablesDatatables.init();
        });
    </script>
    <script>
        $("#downloadfromdbsubmit").on("click", function () {
        $(document).ready(function() {
    
                var mof_no = $("#mof_no").val();
                $("#mof_no_input").val(mof_no);
        });
    });
    $("#downloadfromdbnotsubmit").on("click", function () {
        $(document).ready(function() {
    
                var doc_no = $("#mof_no").val();
                $("#mof_no_notsubmit").val(mof_no);
        });
    });
    </script>
@endsection
