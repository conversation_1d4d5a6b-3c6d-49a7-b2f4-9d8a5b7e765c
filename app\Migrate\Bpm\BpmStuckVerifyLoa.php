<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService; 
use App\Services\Traits\BpmApiService;
use App\Services\Traits\SourcingService;
use Illuminate\Support\Arr;
use App\Http\Controllers\ActionEpController;
use App\Services\EPTableService;
use App\EpSupportActionLog;

class BpmStuckVerifyLoa {

    use BPMService; 
    use BpmApiService;
    use SourcingService; 

    /***
     * 
     *  1. Login user using worklist manager 
        2. Find instance id with activity name Verify LOA 
        3. Try open instance, should be error record not found
        4. Get task id at payload, then patch wftask state to null 
        5. Open service manager > Sourcing QT > LOAAcceptance > start
            SC_AwardSupplier_Data > payload at worklist manager
            document_number > loa number
            task_performer > login id
        6. Find task using loa number 
        7. Open instance, check assignee. If one, proceed to accept and submit.
            If multiple, acquire first then accept 
        8. Check instance, task should be at verify letter with status ASSIGNED/COMPLETED/HANDLE ERROR
     * 
     */
    public static function runStuckVerifyLoa() {
        MigrateUtils::logDump(__METHOD__ . ' > Starting ... ');
        $thisCls = new BpmStuckVerifyLoa;  
        $offset = 0;
        $limit = 50; 
        
        $list = $thisCls->findListStuckVerifyLoa();
        $total = count($list);
        
        if($total > 0){
            foreach ($list as $data) {
                $assignees = $data->assignees;
                $assignee = substr($assignees, 0, strpos($assignees, ",")); 
    
                //assignee login using worklist manager 
                MigrateUtils::logDump(__METHOD__ . ' > LOGIN ID: '. $assignee.' > Find BPM MY Task ');
                $worklistAPI = $thisCls->findApiWorklist($assignee, 'ASSIGNED', 'MY', $offset, $limit); 
    
                if ($worklistAPI && $worklistAPI["status"] != null && $worklistAPI["status"] === 'Success') {
                    $resultAPIWorklist = $worklistAPI["result"];
                    $resultWorklistAPI = $resultAPIWorklist["worklistItem"];
    
                    foreach($resultWorklistAPI as $task){
                        $instanceId = $task['instanceId'];
                        $activityName = $task['taskName'];
                        $taskId = $task['taskId'];
                        $processName = $task['process'];
     
                        if($activityName === 'Verify LOA' && $processName ==='LOA Acceptance'){
                            sleep(5);
                            $processAPI = $thisCls->findAPIProcessManagerBPMByInstance($instanceId); 
                                
                            //get instance with null data (stuck)
                            if($processAPI && $processAPI["status"] === 'Success' &&  $processAPI["status"] != null && $processAPI["result"] == ''){
                                MigrateUtils::logDump(__METHOD__ . ' > START INSTANCE ID: ' .$instanceId);
                                 
                                //get task detail payload and document number
                                sleep(5);
                                $taskDetailAPI = $thisCls->findApiWorklistTaskDetail($taskId);                                
                                if($taskDetailAPI && $taskDetailAPI["status"] === 'Success' &&  $taskDetailAPI["status"] != null){
                                    $resultAPITaskDetail = $taskDetailAPI["result"];
                                        
                                    if ($resultAPITaskDetail["payload"]) {
                                            $payload = str_replace('<?xml version = \'1.0\' encoding = \'UTF-8\'?>',"",$resultAPITaskDetail["payload"][0]);
    
                                            if($resultAPITaskDetail["docNumber"]){
                                                $documentNumber = $resultAPITaskDetail["docNumber"]; 
                                                // patch state wftask table  
                                                $isSuccess = self::patchEpTable($taskId,$documentNumber);
                                                //  $isSuccess = true;
                                                if($isSuccess === true){
                                                    MigrateUtils::logDump(__METHOD__ . ' > Instance ID: ' .$instanceId .' . Task ID: '.$taskId .'. Success Patch State to null. Find BPM Task Payload');
                                                     
                                                    $name = 'SourcingQT';
                                                    $process = 'LOAAcceptance';
                                                    $trigger = 'start';
                                                    $elements = [
                                                        ['name'=>'SC_AwardSupplier_Data','value'=>$payload,'type'=>'object'],
                                                        ['name'=>'document_number','value'=>$documentNumber,'type'=>'string'],
                                                        ['name'=>'task_performer','value'=>$assignee,'type'=>'string'], 
                                                    ];
    
                                                    sleep(5);
                                                    //refire task using service manager
                                                    $createServiceManagerAPI = $thisCls->findApiBPMCreateServiceManager($name,$process,$trigger,$elements);
                                                    if($createServiceManagerAPI && $createServiceManagerAPI["status"] === 'Success' && $createServiceManagerAPI["status"] != null){
                                                        sleep(5);
                                                        $findInstanceBpmAPI = $thisCls->findAPITaskBPMListDocAndModule($documentNumber, 'SourcingQT');                                            
                                                        if($findInstanceBpmAPI && $findInstanceBpmAPI["status"] === 'Success' &&  $findInstanceBpmAPI["status"] != null){
                                                            $listTaskResult = collect($findInstanceBpmAPI['result']);
                                                            $taskLoa = $listTaskResult->where('state','ASSIGNED')->where('docNumber',$documentNumber)->first();
    
                                                            if($taskLoa && $taskLoa['componentName'] == 'AcceptLetter' && $taskLoa['activityName'] == 'Accept LOA'){ 
                                                                $taskLoaInstanceId = $taskLoa['instanceId'];
                                                                $taskLoaAssignees = $taskLoa['assignees']; 
                                                                $taskLoaTaskId = $taskLoa['taskId'];
                                                                $taskLoaTaskNumber = $taskLoa['taskNumber'];
                                                                $totalAssignee = count($taskLoaAssignees);
    
                                                                MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Task ID: '.$taskLoaTaskId.' > Total Task Assignee: '.$totalAssignee);
                                                                if($totalAssignee == 1) { 
                                                                    $assigneeLoginId =  $taskLoa['assignees'][0];                                                                    
                                                                }else {
                                                                    //acquire task first
                                                                    sleep(5);
                                                                    $claimTaskAPI = $thisCls->actionWorklistTaskAPI('Claim-Task',$taskLoaTaskId,$taskLoa['assignees'][0]);
                                                                    if($claimTaskAPI && $claimTaskAPI["status"] === 'Success' &&  $claimTaskAPI["status"] != null){
                                                                        MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Assignee: '.$taskLoa['assignees'][0] .' claim the task');
                                                                        //get acquired by
                                                                        sleep(5);
                                                                        $taskAcquired = $thisCls->findApiWorklistTaskDetail($taskLoaTaskId);
                                                                        if($taskAcquired && $taskAcquired["status"] === 'Success' &&  $taskAcquired["status"] != null){
                                                                            $resultAPITaskAcquired = $taskAcquired["result"];
                                                                            $assigneeLoginId = $resultAPITaskAcquired['acquiredBy'];
                                                                        }
                                                                        
                                                                    }
                                                                }
    
                                                                if($assigneeLoginId != ''){
                                                                    
                                                                    //proceed to accept task 
                                                                    MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Assignee: '.$assigneeLoginId .'. Find Task Detail');  
                                                                    sleep(5);
                                                                    $taskBpmDetailAPI = $thisCls->findAPITaskIDBPMList($taskLoaTaskNumber);                                                                
                                                                    if($taskBpmDetailAPI && $taskBpmDetailAPI["status"] === 'Success' &&  $taskBpmDetailAPI["status"] != null){
                                                                        MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.'. Proceed To Accept Task');  
                                                                        $resultAPITaskBpm = $taskBpmDetailAPI["result"];
                                                                        $payloadTaskBpm = $resultAPITaskBpm['payload'][0];
                                                                        $updatePayloadTaskBpm = str_replace('<?xml version = \'1.0\' encoding = \'UTF-8\'?>',"",$payloadTaskBpm);
                                                                        
                                                                        $docNumberTaskBpm = $resultAPITaskBpm['docNumber']; 
                                                                        $docTypeTaskBpm = $resultAPITaskBpm['docType'];
                                                                        $docIdTaskBpm = (string)$resultAPITaskBpm['docId'];
                                                                        $docStatus = (string)$resultAPITaskBpm['docStatus']; 
     
                                                                        $param = array();
                                                                        array_push($param,$docNumberTaskBpm,$docTypeTaskBpm,$docIdTaskBpm,$docStatus);  
                                                                        
                                                                        //accept task
                                                                        sleep(5);
                                                                        $acceptTask = $thisCls->updateExecuteActionAPI($taskLoaTaskId, $assigneeLoginId, $updatePayloadTaskBpm, 'ACCEPT', $param);
                                                                        if($acceptTask && $acceptTask["status"] === 'Success' &&  $acceptTask["status"] != null){
                                                                            MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId .'. Check Status Task.');  
                                                                            //find instance again to check status 
                                                                            //3 possible outcome. VerifyLetter task status ASSIGNED/COMPLETED/ERROR HANDLER
                                                                            sleep(5);
                                                                            $findNewInstanceBpmAPI = $thisCls->findAPITaskBPMListDocAndModule($documentNumber, 'SourcingQT');
                                                                            $listNewTaskResult = collect($findNewInstanceBpmAPI['result']);
                                                                            $taskVerifyLetter = $listNewTaskResult->where('state','ASSIGNED')->first();
                                                                            if($taskVerifyLetter){
                                                                                $taskVerifyLetterTaskId = $taskVerifyLetter['taskId'];
                                                                                $taskVerifyLetterState = $taskVerifyLetter['state'];
                                                                                $taskVerifyLetterActivityName = $taskVerifyLetter['activityName'];
                                                                                $taskVerifyLetterComponentName = $taskVerifyLetter['componentName'];
    
                                                                                if($taskVerifyLetterActivityName != 'Verify LOA' && $taskVerifyLetterComponentName != 'VerifyLetter'){
                                                                                    $thisCls->saveFailedActionLog($documentNumber,$taskLoaInstanceId,$taskVerifyLetterActivityName,$taskVerifyLetterComponentName);
    
                                                                                }
                                                                                MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Current Task State: '.$taskVerifyLetterState .' > Activity Name: '.$taskVerifyLetterActivityName . ' > Component Name:' .$taskVerifyLetterComponentName );  
                                                                            }else{
                                                                                MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId .' > Current Task Verify Letter Not Found.Please Check...');
                                                                            }
                                                                        }else{
                                                                            MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Task ID: '.$taskLoaTaskId .' > Assignee: '.$assigneeLoginId .'. Accept Task Failed. Please Check...');  
                                                                        }
                                                                    }else{
                                                                        MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.' > Assignee: '.$assigneeLoginId .'. Task Detail By Task Number Not Found. Please Check...');
                                                                    }
                                                                }else {
                                                                    MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.' > New Instance Created: '.$taskLoaInstanceId.'. No Assignee Found.Please Check...');  
                                                                }
                                                            }else { 
                                                                if($taskLoa['componentName'] && $taskLoa['activityName'] && $taskLoa['instanceId']){
                                                                    $thisCls->saveFailedActionLog($documentNumber,$taskLoa['instanceId'],$taskLoa['activityName'],$taskLoa['componentName']);
                                                                    MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.'. New Assigned Task Is > '.$taskLoa['componentName']. ' > '.$taskLoa['activityName'].'. Please Check...');
                                                                }else{
                                                                    MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.'. New Assigned Task Not Found. Please Check...'); 
                                                                }
                                                                
                                                            }
                                                        }else{
                                                            MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.'. New Instance Created Not Found.Please Check...');
                                                        } 
                                                    }else{
                                                        MigrateUtils::logDump(__METHOD__ . ' > Document Number: '.$documentNumber.'. Failed To Fire LOAAcceptance Task.Please Check...');
                                                    }
                                                }else{
                                                    MigrateUtils::logDump(__METHOD__ . ' > Task ID: '.$taskId .'.Error To Patch WFTASK State. Please Check...'); 
                                                }  
                                            }else{
                                                MigrateUtils::logDump(__METHOD__ . ' > Task ID: '.$taskId .'. Document Number Not Found.Please Check...');
                                            }
                                        }else{
                                            MigrateUtils::logDump(__METHOD__ . ' > Task ID: '.$taskId .'. Payload Not Found.Please Check...');
                                        } 
                                    }else{
                                        MigrateUtils::logDump(__METHOD__ . ' > Task ID: '.$taskId .'. Error To Find BPM Task Paylod. Please Check...'); 
                                    } 
                                    MigrateUtils::logDump(__METHOD__ . ' > FINISH INSTANCE ID: ' .$instanceId); 
                                    MigrateUtils::logDump(__METHOD__); 
                                    MigrateUtils::logDump(__METHOD__); 
                            }else {
                                MigrateUtils::logDump(__METHOD__ . ' > LOGIN ID: '.$assignee.' > Instance ID '.$instanceId.' Not Empty. Skip... ');
                            }
                        } 
                    }
                    
                }else{
                    MigrateUtils::logDump(__METHOD__ . ' > LOGIN ID: ' .$assignee.' > Worklist MY Task Not Found.Please Check...');
                }
            }
        }
        
        MigrateUtils::logDump(__METHOD__ . ' > Completed... ');
    } 
  
    public static function patchEpTable($taskId,$documentNumber){
        $databaseConnection = 'oracle_nextgen_soa_fullgrant';
        $tableName = 'WFTASK';
        $primaryField = 'TASKID';
        $primaryValue = $taskId;
        $selectedField = 'state';
        $valueField = null;
        $logs = collect([]);
        $isSuccessSave = false;
        $addWheres = null;
        $updateScriptFields = [$selectedField => $valueField]; 
            
        DB::connection($databaseConnection)->transaction(function() use ($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields,&$logs,&$isSuccessSave,$addWheres) {
            self::updateTableEp($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields, $logs,$addWheres);
            $isSuccessSave = true;
        });

        $actionTypeLog = 'Script';
        $actionName = 'PatchDataTable';

        $parameters =  collect([]);
        $parameters->put("remarks", $documentNumber);
        $parameters->put("table", $tableName);
        $parameters->put("reference_id", array($primaryField => $primaryValue));
        $parameters->put("update_data", $updateScriptFields);
        $parameters->put("patching", $tableName);

        $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

        return $isSuccessSave;
    }

     public static function updateTableEp($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,&$logs,$addWheres = null) {
        $logQuery = self::updateRecordTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,$addWheres);
        $logs->put('action_patch_update_table',$logQuery);
     }

     public static function updateRecordTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,$addWheres = null) {
        if($tableName == null || strlen(trim($tableName)) == 0 
        || $primaryField == null || strlen(trim($primaryField)) == 0 
        || $primaryValue == null || strlen(trim($primaryValue)) == 0 
        || count($updateScriptFields) == 0) { 
            $logQuery = collect([]);
            $logQuery->put('status','Query UPDATE is not execute. Not match with requirement!');  
            return $logQuery;
        }
               
        $query =  DB::connection($databaseConnection)
                ->table($tableName)
                ->where($primaryField, $primaryValue);
        if($addWheres != null && count($addWheres) > 0){
            foreach ($addWheres as $key => $value) {
                $query->where($key,$value);
            }
        }
        $dataBefore =  $query->first();

        if($dataBefore  != null){
            $query->update($updateScriptFields);
        }

        return self::createLogQuery($dataBefore, $query, $updateScriptFields);
     }

     public static function createLogQuery($objBeforeUpdate,$queryObj,$fields) {
        //Extract to get Old Data
        $oldObj = collect([]);
        if($objBeforeUpdate != null){
            foreach ($fields as $key=>$value){
                //Get old value
                $oldObj->put($key,$objBeforeUpdate->{$key});
            }
        }
        
        
        $logQuery = collect([]);
        $logQuery->put('data_before',$oldObj);   
        $logQuery->put('table',$queryObj->from);      
        $logQuery->put('where',$queryObj->wheres);
        $logQuery->put('update',$fields);
        if($oldObj->count() > 0){
            $logQuery->put('status','Success update the record.');
        }else{
            $logQuery->put('status','Failed update the record. Get the record is not found');
        }
        return $logQuery;
    }

    public static function saveFailedActionLog($documentNumber,$instanceId,$activityName,$componentName){
        $actionTypeLog = 'Bpm';
        $actionName = 'StuckVerifyLoa';

        $parameters =  collect([]);
        $parameters->put("remarks", $documentNumber);
        $parameters->put("new_instance", $instanceId);
        $parameters->put("activuty_name", $activityName);
        $parameters->put("component_name", $componentName);
        $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$parameters,$parameters,'Completed');
    }
 
}