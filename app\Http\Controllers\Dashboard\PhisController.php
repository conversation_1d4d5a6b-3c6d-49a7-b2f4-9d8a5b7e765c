<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\OSBWebService;
use Carbon\Carbon;
use DB;

class PhisController extends Controller {

    use OSBService;
    use SSHService;
    use OSBWebService;

    public function getDashboardPhis() {
        return view('dashboard.phis', []);
    }

    public function checkMonitoringPhisWsFailedSent() {
        $excludeDocNo = ['FN230000000015591','FN230000000334227'];
        $listTransFailedPhis = $this->getListTransWsPhisFailedSend();
        $total = count( $listTransFailedPhis );
        $html = "";
        $html .= "<div class='row'>
                    <div class='col-lg-12'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Web Service Failed Sent To PHIS</strong> ($total)</h2>
                            </div>
                                <table id='basic-datatable' class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th>Trans Date</th>    
                                            <th>Doc No</th>
                                            <th>Service Code</th>
                                            <th>Service Name</th>
                                            <th>Trans ID</th>
                                            <th>Status Code</th>
                                            <th>Status Desc</th>
                                            <th>&nbsp;</th>
                                        </tr>
                                    </thead>
                                <tbody>";
        //$listTransFailedPhis = collect($listTransFailedPhis)->whereNotIn('doc_no',$excludeDocNo)->all();

        foreach ($listTransFailedPhis as $data) {
            $osbServObj = DB::connection('oracle_nextgen_rpt')->table('OSB_SERVICE')->where('SERVICE_CODE',$data->service_code)->first();
            $serviceName = $osbServObj != null ? $osbServObj->service_name:'';

            $html .= "
                                    <tr>
                                        <td class='text-center'>$data->trans_date</td>
                                        <td><strong><a href='".url('find/phis/ws/search')."?doc_no=$data->doc_no&service_code=$data->service_code' target='_blank'>$data->doc_no</a></strong></td>
                                        <td>$data->service_code</td>
                                        <td>$serviceName </td>
                                        <td><a href='".url('find/osb/detail/log')."?cari=$data->trans_id' target='_blank'>$data->trans_id</a></td>    
                                        <td class='text-center'>$data->status_code</td> 
                                        <td class='text-center'>$data->status_desc</td> 
                                        <td class='text-center'><a href='".url('/dashboard/phis/resendWebServicePHIS')."/$data->service_code/$data->doc_no' target='_blank'>[RESEND]</a></strong></td>    
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function resendWebServicePHIS($serviceCode,$docNo) {
        $fieldRemark = 'remarks_1';
        $transType = 'IBReq';
        $osbObj = $this->getFirstOsbLogDetail($fieldRemark,$serviceCode,$transType,$docNo) ;
        if($osbObj){
            $osbObjPayload = $this->getOsbLoggingPayload($osbObj->logging_id);

            $soapAction = null;
            $urlWs = null;
            
            if($serviceCode == 'PHS-080'){
                $soapAction = 'http://www.ep.gov.my/Service/1-0/PHISMasterDataDeliveryAddress/inquire';
                $urlWs = '/PHISMasterDataDeliveryAddress/v1.1';
            }
            if($serviceCode == 'PHS-160'){
                $soapAction = 'http://www.ep.gov.my/Service/1-0/ContractOrderInformation/inquire';
                $urlWs = '/ContractOrderInformation/v1.1';
            }
            if($serviceCode == 'PHS-170'){
                $soapAction = 'http://www.ep.gov.my/Service/1-0/ContractFulfillmentReceivedNote/inquire';
                $urlWs = '/ContractFulfillmentReceivedNote/v1.1';
            }
            if($serviceCode == 'PHS-180'){
                $soapAction = 'http://www.ep.gov.my/Service/1-0/ContractStopInstruction/inquire';
                $urlWs = '/ContractStopInstruction/v1.1';
            }
            if($serviceCode == 'PHS-190'){
                $soapAction = 'http://www.ep.gov.my/Service/1-0/ContractPaymentInstruction/inquire';
                $urlWs = '/ContractPaymentInstruction/v1.1';
            }
            $res =$this->sendWebServicePayload($urlWs,$soapAction,$osbObjPayload->payload_body) ;
            return $res;
        }
    }

}
