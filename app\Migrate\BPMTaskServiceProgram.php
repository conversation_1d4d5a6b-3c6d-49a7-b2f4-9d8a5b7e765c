<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\BPMService;
use Guzzle;
use GuzzleHttp\Client;

class BPMTaskServiceProgram {

    use PayloadGeneratorService;
    use BPMService;
    use BpmApiService;
    use FulfilmentService;
    

    public static function mergeBpmInstanceOldVersion(){
        MigrateUtils::logDump(__METHOD__ .' Starting ... ');

        /****
          AND SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1)  NOT IN (
                    'default/Codification!1.0.1',
                    'default/Contract_Management!1.0.9',
                    'default/Fulfilment!1.0.8',
                    'default/Order!1.0.11',
                    'default/Procurement_Plan!1.0.1',
                    'default/Profile_Management!1.0.1',
                    'default/SourcingDP!1.0.10',
                    'default/SourcingDP!1.0.11',
                    'default/SourcingQT!1.0.16',
                    'default/SourcingQT!1.0.16.1',
                    'default/SourcingQT!1.0.14',
                    'default/SourcingQT!1.0.15',
                    'default/Supplier_Management!1.0.6',
                    'default/YEP_Fulfilment!1.0.0',
                    'default/YEP_Order!1.0.0'
                )

        */

        /** Get list Old version */
        $listCompositeVer = DB::connection('oracle_bpm_rpt')
                ->select("SELECT  SUBSTR(c.COMPOSITE_DN, 1, Instr(c.COMPOSITE_DN, '*', -1, 1) -1) AS composite_version   , c.COMPOSITE_DN,
                count(c.id) AS TOTAL_COMPOSITE_INSTANCE
                FROM COMPOSITE_INSTANCE c
                WHERE 
                c.state NOT IN (1,16,17,18,19,20,22,23,64) 
                AND SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1)  NOT IN (
                    'default/Codification!1.0.1',
                    'default/Contract_Management!1.0.9',
                    'default/Fulfilment!1.0.8',
                    'default/Order!1.0.11',
                    'default/Procurement_Plan!1.0.1',
                    'default/Profile_Management!1.0.1',
                    'default/SourcingDP!1.0.10',
                    'default/SourcingDP!1.0.11',
                    'default/SourcingQT!1.0.16',
                    'default/SourcingQT!1.0.16.1',
                    'default/SourcingQT!1.0.14',
                    'default/SourcingQT!1.0.15',
                    'default/Supplier_Management!1.0.6',
                    'default/YEP_Fulfilment!1.0.0',
                    'default/YEP_Order!1.0.0'
                ) 
                GROUP BY SUBSTR(c.COMPOSITE_DN, 1, Instr(c.COMPOSITE_DN, '*', -1, 1) -1) , c.COMPOSITE_DN 
                ORDER BY 1");

        MigrateUtils::logDump(__METHOD__ .' Total result listCompositeVer ... '.count($listCompositeVer));
        foreach ($listCompositeVer as $compVer ){
            MigrateUtils::logDump(__METHOD__ .' composite version : '.json_encode($compVer));


            // Get list instance duplicate 
            $listCompIdDup =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver as d')
                ->whereNull('doc_number')
                ->whereRaw("EXISTS (SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver xx WHERE xx.`composite_instance_id` = d.composite_instance_id AND xx.doc_number IS NOT NULL)")
                ->select('composite_instance_id')
                ->get();
                
            //Remove duplicate instance   
            MigrateUtils::logDump(__METHOD__ .' Remove duplicate instance total : '.collect($listCompIdDup)->count()); 
            foreach (collect($listCompIdDup)->pluck('composite_instance_id')->toArray() as $id){
                DB::connection('mysql_ep_support')
                    ->table('ep_bpm_support.bpm_instance_old_ver')
                    ->whereNull('doc_number')
                    ->where('composite_instance_id', $id)
                    ->delete();
            }
            
            MigrateUtils::logDump(__METHOD__ ." $compVer->composite_version >> get query details instance ... ");
            // Get list instance with tasks by each version instance
            $queryListInstance = "SELECT DISTINCT * FROM ( SELECT
                        c.id AS COMPOSITE_INSTANCE_ID,
                        SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1) AS  COMPOSITE_VERSION, 
                        c.created_time as COMPOSITE_CREATED_TIME ,
                        c.state AS COMPOSITE_STATE,
                        w.taskid as bpm_task_id,
                        DECODE(w.CUSTOMATTRIBUTESTRING1,NULL,w.protectedtextattribute1,w.CUSTOMATTRIBUTESTRING1) AS DOC_NUMBER,
                        w.CUSTOMATTRIBUTESTRING2 AS doc_type,
                        w.CUSTOMATTRIBUTENUMBER1 as doc_id,
                        w.CUSTOMATTRIBUTENUMBER2 as status_id,
                        w.state as task_state 
                    FROM COMPOSITE_INSTANCE c , WFTASK w 
                    WHERE c.id = w.COMPOSITEINSTANCEID (+) 
                    AND c.state NOT IN (1,16,17,18,19,20,22,23,64) 
                    -- AND w.CUSTOMATTRIBUTESTRING2 = 'AC'
                    -- AND w.CUSTOMATTRIBUTENUMBER2 in ('57000','57050','57100','57250','57300')
                    AND c.COMPOSITE_DN = '$compVer->composite_dn' ) tmp ORDER BY 1 ";
            MigrateUtils::logDump(__METHOD__ ." QUERY => $queryListInstance");

            $listInstance = DB::connection('oracle_bpm_rpt')->select($queryListInstance);

            MigrateUtils::logDump(__METHOD__ ." $compVer->composite_version >> Total result : ".count($listInstance));
            foreach($listInstance  as $instObj){
                $checkExist = DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$instObj->composite_instance_id)
                    ->where('doc_number',$instObj->doc_number)
                    ->where('doc_type',$instObj->doc_type)
                    ->count();
                if($checkExist == 0 ){
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->insert(
                        [
                            'composite_instance_id' =>  $instObj->composite_instance_id,
                            'bpm_task_id'  =>  $instObj->bpm_task_id,
                            'composite_created' =>  $instObj->composite_created_time,
                            'composite_version' =>  $instObj->composite_version,
                            'composite_state' =>  $instObj->composite_state,
                            'task_state'  =>  $instObj->task_state,
                            'doc_number' =>  $instObj->doc_number,
                            'doc_type' =>  $instObj->doc_type,
                            'doc_status_id' =>  $instObj->status_id,
                            'created_at' => Carbon::now()
                        ]
                    );
                }else{
                    if($instObj->task_state == 'ASSIGNED'){
                        DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                        ->where('composite_instance_id',$instObj->composite_instance_id)
                        ->where('doc_number',$instObj->doc_number)
                        ->where('doc_type',$instObj->doc_type)
                        // ->where('task_state','ASSIGNED')
                        ->update([
                                'composite_instance_id' =>  $instObj->composite_instance_id,
                                'bpm_task_id'  =>  $instObj->bpm_task_id,
                                'composite_created' =>  $instObj->composite_created_time,
                                'composite_version' =>  $instObj->composite_version,
                                'composite_state' =>  $instObj->composite_state,
                                'task_state'  =>  $instObj->task_state,
                                'doc_number' =>  $instObj->doc_number,
                                'doc_type' =>  $instObj->doc_type,
                                'doc_status_id' =>  $instObj->status_id,
                                'created_at' => Carbon::now()
                            ]);
                    }
                    
                }
                
            }
        } 

        MigrateUtils::logDump(__METHOD__ .' Completed');
    }

    public static function reUpdateStatusDocBpmInstanceOldVersion(){


        // Get list instance duplicate 
        $listCompIdDup =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver as d')
        ->whereNull('doc_number')
        ->whereRaw("EXISTS (SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver xx WHERE xx.`composite_instance_id` = d.composite_instance_id AND xx.doc_number IS NOT NULL)")
        ->select('composite_instance_id')
        ->get();
        
        //Remove duplicate instance   
        MigrateUtils::logDump(__METHOD__ .' Remove duplicate instance total : '.collect($listCompIdDup)->count()); 
        foreach (collect($listCompIdDup)->pluck('composite_instance_id')->toArray() as $id){
            DB::connection('mysql_ep_support')
                ->table('ep_bpm_support.bpm_instance_old_ver')
                ->whereNull('doc_number')
                ->where('composite_instance_id', $id)
                ->delete();
        }


        $checkTotal = DB::connection('mysql_ep_support')
                    ->table('ep_bpm_support.bpm_instance_old_ver')
                    ->whereNotNull('doc_number')
                    ->whereNull('doc_status')
                    ->count();
        MigrateUtils::logDump(__METHOD__ .' Total result ... '.$checkTotal);
        $counterMax = 1;  
        $limit = 1000;          
        if( $checkTotal > $limit){
            $counterMax = intval($checkTotal / $limit)+1;
        }

        MigrateUtils::logDump(__METHOD__ .' counterMax ... '.$counterMax);
        for($i = 1; $i <= $counterMax ; $i++ ){
            MigrateUtils::logDump(__METHOD__ .' Loop ... '.$i);
            $listInstances = DB::connection('mysql_ep_support')
                ->table('ep_bpm_support.bpm_instance_old_ver')
                ->whereNotNull('doc_number')
                ->whereNull('doc_status')
                ->take($limit)
                ->get();
            MigrateUtils::logDump(__METHOD__ .' Loop ... '.$i.'). total: '.count($listInstances));
     
            foreach($listInstances  as $inst){


                $docStatus = null;
                if($inst->doc_status_id != null && strlen($inst->doc_status_id) > 0){
                    $statusObj = DB::connection('oracle_nextgen_rpt')->table('pm_status_desc')->where('status_id',$inst->doc_status_id)
                        ->where('language_code','en')->first();
                    if($statusObj != null){
                        $docStatus = $statusObj->status_name;
                    }
                }


                $statusDtl = DB::connection('oracle_nextgen_rpt')
                            ->select("SELECT td.status_id,td.doc_type,
                            psd.status_name AS status_name ,
                            td.actioned_date 
                            FROM pm_tracking_diary td, pm_status_desc psd 
                            WHERE 
                            td.status_id = psd.status_id  
                            AND psd.language_code = 'en' 
                            AND td.actioned_date = (SELECT max(actioned_date) FROM pm_tracking_diary  WHERE doc_no = td.doc_no)
                            AND doc_no  = ? ",array($inst->doc_number));

                if($statusDtl){
                    $instObj = $statusDtl[0];
                    
                    MigrateUtils::logDump(__METHOD__ ." docNumber:$inst->doc_number , docStatus : $docStatus");
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$inst->composite_instance_id)
                    ->where('doc_number',$inst->doc_number)
                    ->update(
                        [
                            'doc_status' =>   $docStatus,
                            'doc_tracking_status_id' =>  $instObj->status_id,
                            'doc_tracking_status' =>  $instObj->status_name,
                            'doc_tracking_type' =>  $instObj->doc_type,
                            'doc_tracking_last_updated' =>  $instObj->actioned_date,
                            'remark_status_doc' => 'Successfully get status doc number',
                            'changed_at' => Carbon::now()
                        ]
                    );
                }else{
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$inst->composite_instance_id)
                    ->where('doc_number',$inst->doc_number)
                    ->update(
                        [
                            'doc_status' =>   $docStatus,
                            'remark_status_doc' => 'Result not found when find status doc. number',
                            'changed_at' => Carbon::now()
                        ]
                    );
                }
            }
        }
        MigrateUtils::logDump(__METHOD__ .' Completed');
        //40000

    }

    public static function reUpdateDocNoInstanceAsOne( ){
        $listComposites = DB::connection('mysql_ep_support')
                            ->select("SELECT composite_version, COUNT(*) as total FROM (

            SELECT composite_version,composite_instance_id , COUNT(*) 
                FROM ep_bpm_support.bpm_instance_old_ver a  
                WHERE EXISTS (SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver b WHERE b.`composite_instance_id` = a.`composite_instance_id` AND b.`task_state` = 'ASSIGNED' )
                GROUP BY composite_version,composite_instance_id 
                HAVING COUNT(*) > 1
            ) t 
            GROUP BY composite_version 
            ORDER BY 2 DESC");
        
        foreach($listComposites  as $compObj){
            $compositeName = $compObj->composite_version;
            MigrateUtils::logDump(__METHOD__ ." total more than one docno for  $compositeName  is ".$compObj->total );
            $listInstances = DB::connection('mysql_ep_support')
                            ->select(" SELECT composite_version,composite_instance_id , COUNT(*) 
                            FROM ep_bpm_support.bpm_instance_old_ver a  
                            WHERE  composite_version = ? 
                            AND EXISTS (SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver b WHERE b.`composite_instance_id` = a.`composite_instance_id` AND b.`task_state` = 'ASSIGNED' )
                            GROUP BY composite_version,composite_instance_id 
                            HAVING COUNT(*) > 1",array($compositeName));
            $totalDeleted = 0;
            foreach($listInstances  as $inst){
                // Check this instance must have ASSIGNED task
                $checkCountAssigned =  DB::connection('mysql_ep_support')
                    ->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$inst->composite_instance_id)
                    ->where('task_state','ASSIGNED')
                    ->count();

                if($checkCountAssigned > 0 ){
                    DB::connection('mysql_ep_support')
                        ->table('ep_bpm_support.bpm_instance_old_ver')
                        ->where('composite_instance_id',$inst->composite_instance_id)
                        ->whereNotIn('task_state',['ASSIGNED'])
                        ->delete();
                    DB::connection('mysql_ep_support')
                        ->table('ep_bpm_support.bpm_instance_old_ver')
                        ->where('composite_instance_id',$inst->composite_instance_id)
                        ->whereNull('task_state')
                        ->delete();
                    
                    $totalDeleted++;
                }
            } 
            MigrateUtils::logDump(__METHOD__ ." Completed $compositeName =>  total deleted : ".$totalDeleted);
        }


    }


    public static function reUpdateDocNoContractInfo( ){
        MigrateUtils::logDump(__METHOD__ ." entering.." );
        $listCt = DB::connection('mysql_ep_support')
                            ->select("SELECT * FROM ep_bpm_support.bpm_instance_old_ver 
                            WHERE 
                            -- task_state = 'ASSIGNED'
                            is_terminated IS NULL 
                            AND composite_version like 'default/Contract_Management%' 
                            -- AND doc_status IS NOT NULL 
                            -- AND SUBSTR(doc_number,1,2) = 'LA' 
                            AND ct_no IS NULL  
                            AND (
                                ( doc_number IS NOT NULL AND doc_status IS NOT NULL )  
                                OR ( doc_number IS NOT NULL AND doc_status IS NULL )
                            )
                            
                            ");
        MigrateUtils::logDump(__METHOD__ ." total ". count($listCt));
        foreach($listCt  as $obj){
            $checkDocType = substr($obj->doc_number, 0, 2);
            $checkDocTypeNW = substr($obj->doc_number, 0, 1);
            $objRes = null;
            if($checkDocType == 'LA' || $checkDocTypeNW == 'N' || $checkDocTypeNW == 'W'){
                $objRes =  self::getContractInfo(null,$obj->doc_number);
            }else{
                $objRes =  self::getContractInfo($obj->doc_number,null);
            }
           
            
            
            if($objRes != null){

                $objPtj  =  self::getProfilePtj($objRes->owner_org_profile_id);
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update(
                    [
                        'remark_status_doc' =>  'Successfully update contract info',
                        'ct_no' =>  $objRes->contract_no,
                        'ct_exp_date' =>  $objRes->exp_date,
                        'ct_status' =>  $objRes->contract_status,
                        'ct_fulfilment_type' => $objRes->fulfilment_type,
                        'ct_extend_day' => $objRes->extend_period,
                        'ct_ag_status' =>  $objRes->ct_ag_status,
                        'ct_ag_type' =>  $objRes->ct_ag_type,
                        'ct_extend_date' => $objRes->extend_end_date != null ? $objRes->extend_end_date : null,
                        'ministry_name' =>  $objPtj != null ? $objPtj->kementerian_name : null,
                        'ministry_code' =>  $objPtj != null ? $objPtj->kementerian_code : null,
                        'ptj_name' => $objPtj != null ? $objPtj->ptj_name : null,
                        'ptj_code' => $objPtj != null ? $objPtj->ptj_code : null,
                        'changed_at' => Carbon::now()
                    ]
                );
            }else {
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update(
                    [
                        'remark_status_doc' =>  'Not found to get info contract detail',
                        'changed_at' => Carbon::now()
                    ]
                );
            }

        }
        MigrateUtils::logDump(__METHOD__ ." Completed .. ");
    }

    public static function reUpdateDocNoQuotationTenderInfo( ){
        MigrateUtils::logDump(__METHOD__ ." entering.." );
        $listCt = DB::connection('mysql_ep_support')
                            ->select("SELECT * FROM ep_bpm_support.bpm_instance_old_ver 
                            WHERE task_state = 'ASSIGNED'
                            AND composite_version like 'default/SourcingQT%'
                            AND (
                                ( doc_number IS NOT NULL AND doc_status IS NOT NULL )  
                                OR ( doc_number IS NOT NULL AND doc_status IS NULL )
                            )
                            ");
        MigrateUtils::logDump(__METHOD__ ." total ". count($listCt));
        foreach($listCt  as $obj){
            $checkDocType = substr($obj->doc_number, 0, 2);
            $objRes = null;
            if($checkDocType == 'LA'){
                $objRes =  self::getQuotationTenderInfo(null,$obj->doc_number);
            }else{
                $objRes =  self::getQuotationTenderInfo($obj->doc_number,null);
            }
           

            if($objRes != null){

                $objPtj  =  self::getProfilePtj($objRes->org_profile_id);
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update(
                    [
                        'remark_status_doc' =>  'Successfully update qt info',
                        'qt_no' =>  $objRes->qt_no,
                        'qt_publish_date' =>  $objRes->publish_date,
                        'qt_closing_date' =>  $objRes->closing_date,
                        'qt_bsv_type' => $objRes->bsv_type != null ? $objRes->bsv_type : null,
                        'qt_bsv_date' => $objRes->bsv_date != null ? $objRes->bsv_date: null,
                        'ministry_name' =>  $objPtj != null ? $objPtj->kementerian_name : null,
                        'ministry_code' =>  $objPtj != null ? $objPtj->kementerian_code : null,
                        'ptj_name' => $objPtj != null ? $objPtj->ptj_name : null,
                        'ptj_code' => $objPtj != null ? $objPtj->ptj_code : null,
                        'changed_at' => Carbon::now()
                    ]
                );
            }else {
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update(
                    [
                        'remark_status_doc' =>  'Not found to get info qt detail',
                        'changed_at' => Carbon::now()
                    ]
                );
            }

        }
        MigrateUtils::logDump(__METHOD__ ." Completed .. ");
    }

    public static function reUpdateDocNoContractInfoCms( ){
        MigrateUtils::logDump(__METHOD__ ." entering.." );
        $listCt = DB::connection('mysql_ep_support')
                            ->select("SELECT composite_instance_id,doc_number,ct_no FROM ep_bpm_support.bpm_instance_old_ver 
                            WHERE 
                            -- task_state = 'ASSIGNED' 
                            is_terminated IS NULL 
                            AND is_terminated IS NULL 
                            AND composite_version like 'default/Contract_Management%' 
                            AND doc_status IS NOT NULL 
                            AND doc_number IS  NOT NULL 
                            AND ct_fl_details_status IS NULL 
                            -- AND SUBSTR(doc_number,1,2) = 'LA'

                            ");
        MigrateUtils::logDump(__METHOD__ ." total ". count($listCt));
        foreach($listCt  as $obj){
            
            $checkDocType = substr($obj->doc_number, 0, 2);
            $objRes = null;
            if($checkDocType == 'LA'){
                $objRes =  self::getContractInfoCms(null,$obj->doc_number);
            }else{
                $objRes =  self::getContractInfoCms($obj->doc_number,null);
            }
            //$objRes =  self::getContractInfoCms($obj->ct_no);
            
            if($objRes != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update(
                    [
                        'remark_status_doc' =>  'Successfully update contract info',
                        'ct_current_status' =>  $objRes->ct_current_status,
                        'ct_current_status_doc_type' =>  $objRes->ct_current_status_doc_type,
                        'ct_fl_details_status' =>  $objRes->ct_fl_details_status,
                        'ct_contract_amount' =>  $objRes->ct_contract_amount,
                        'ct_latest_contract_ver_id' =>  $objRes->ct_latest_contract_ver_id,
                        'ct_current_contract_ver_id' =>  $objRes->ct_current_contract_ver_id,
                        'ct_agreement_status' =>  $objRes->ct_agreement_status,
                        'ct_doc_contract_status' =>  $objRes->ct_doc_contract_status,
                        'ct_fulfilment_type' =>  $objRes->ct_fulfilment_type,
                        'ministry_name' =>  $objRes->kementerian_name,
                        'ministry_code' =>  $objRes->kementerian_code,
                        'ptj_name' => $objRes->ptj_name,
                        'ptj_code' => $objRes->ptj_code,
                        'state' => $objRes->ptj_state_name,
                        'changed_at' => Carbon::now()
                    ]
                );
            }

        }
        MigrateUtils::logDump(__METHOD__ ." Completed .. ");
    }

    public static function terminateTasksPOCOCauseTimeOut() {

        MigrateUtils::logDump(self::class . ' Starting ... ' . __METHOD__);
        $dtStartTime = Carbon::now();


        /** USing list doc_no * */
        $listDocNo = array(
            'CO200000000574196',
            'PO200000000464335' 
        );
        MigrateUtils::logDump("TOTAL FOUND: " . count($listDocNo));
        foreach ($listDocNo as $docNo) {
            self::terminateTasksPOCOClosed($docNo);
        }

        Log::info(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
        MigrateUtils::logDump(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    /**
     * Task notification not take action after 14 days will be empty page on user page after click detail task.
     */
    public static function terminateTasksNotificationCodification() {
        $expiredDay = 15;

        MigrateUtils::logDump(self::class . ' > '.__METHOD__.' Entering ... ');
        $dtStartTime = Carbon::now();
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        
        
        $listAll = DB::connection('oracle_bpm_rpt')->select("SELECT w.UPDATEDDATE ,w.CUSTOMATTRIBUTESTRING1 ,CUSTOMATTRIBUTENUMBER1 ,w.CUSTOMATTRIBUTENUMBER2, w.ACTIVITYNAME ,w.PROCESSNAME ,
                    w.COMPOSITEINSTANCEID ,w.COMPOSITENAME ,w.COMPOSITEVERSION ,w.COMPOSITEINSTANCEID  ,
                    w.COMPONENTNAME ,w.TASKID ,w.STATE ,w.ASSIGNEES  ,
                    w.CREATEDDATE ,w.EXPIRATIONDATE  FROM WFTASK  w 
                    WHERE w.STATE = 'ASSIGNED' AND w.ACTIVITYNAME = 'Recommend Result Notification' 
                    -- AND ( w.PROCESSNAME = 'UNSPSC Code Request'  OR w.PROCESSNAME = 'UNSPSCCodeRequest' )
                    AND trunc(w.CREATEDDATE) < trunc(sysdate-$expiredDay)
            UNION ALL 
            SELECT w.UPDATEDDATE ,w.CUSTOMATTRIBUTESTRING1 ,CUSTOMATTRIBUTENUMBER1 ,w.CUSTOMATTRIBUTENUMBER2, w.ACTIVITYNAME ,w.PROCESSNAME ,
                    w.COMPOSITEINSTANCEID ,w.COMPOSITENAME ,w.COMPOSITEVERSION ,w.COMPOSITEINSTANCEID  ,
                    w.COMPONENTNAME ,w.TASKID ,w.STATE ,w.ASSIGNEES  ,
                    w.CREATEDDATE ,w.EXPIRATIONDATE  FROM WFTASK  w 
                    WHERE w.STATE = 'ASSIGNED' AND w.ACTIVITYNAME = 'Publish Result Notification' 
                    AND (w.PROCESSNAME = 'ExtensionCodeRequest'  OR  w.PROCESSNAME = 'Extension Code Request' )
                    AND trunc(w.CREATEDDATE) < trunc(sysdate-$expiredDay)
            UNION ALL               
            SELECT w.UPDATEDDATE ,w.CUSTOMATTRIBUTESTRING1 ,CUSTOMATTRIBUTENUMBER1 ,w.CUSTOMATTRIBUTENUMBER2, w.ACTIVITYNAME ,w.PROCESSNAME ,
                    w.COMPOSITEINSTANCEID ,w.COMPOSITENAME ,w.COMPOSITEVERSION ,w.COMPOSITEINSTANCEID  ,
                    w.COMPONENTNAME ,w.TASKID ,w.STATE ,w.ASSIGNEES  ,
                    w.CREATEDDATE ,w.EXPIRATIONDATE  FROM WFTASK  w 
                    WHERE w.STATE = 'ASSIGNED' AND w.ACTIVITYNAME = 'Recommend Result Notification' 
                    AND (w.PROCESSNAME = 'ExtensionCodeRequest'  OR  w.PROCESSNAME = 'Extension Code Request' ) 
                    AND trunc(w.CREATEDDATE) < trunc(sysdate-$expiredDay)     ");
        
        MigrateUtils::logDump('   Total All >> '.count($listAll)); 
        //dd("OK");
        foreach($listAll as $row){
            $instanceId = $row->compositeinstanceid;
            // default/Codification!1.0.1
            $module = 'default/'.$row->compositename.'!'.$row->compositeversion;
            MigrateUtils::logDump('   >> Start to terminate this task. , instanceID : '.$instanceId. ' module: '.$module.' on created task '.$row->createddate);
            $res = $bpmTaskServiceProgram->submitTerminateInstance($module, $instanceId);
            MigrateUtils::logDump($res);
        }

        

        Log::info(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
        MigrateUtils::logDump(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    /**
     * To get list for POCO already 'closed' as active record but missing 'Pending Payment' status.
     * From list, will check by each no at BPM task, if found will terminate
     */
    public static function terminateTasksPOCOClosedButMissingPendingPayment() {

        MigrateUtils::logDump(self::class . ' Starting ... ' . __METHOD__);
        $dtStartTime = Carbon::now();
        

        /** Using list doc_no * */
        $listDocNo  = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
                    pav.PAYMENT_ADVICE_ID,
                    pav.FULFILMENT_ORDER_ID,
                    pav.PAYMENT_ADVICE_NO,
                    pav.RECORD_STATUS,
                    ws.IS_CURRENT,
                    ws.DOC_TYPE,
                    ws.DOC_ID,
                    ws.STATUS_ID status_pa,
                    x.doc_no,
                    x.doc_type   poco_type,
                    x.status_id  status_poco
                  FROM FL_WORKFLOW_STATUS ws, fl_payment_advice pav,
                    (
                      SELECT
                        o.fulfilment_order_id,
                        o.doc_no,
                        o.doc_type,
                        w.status_id
                      FROM FL_FULFILMENT_ORDER o, FL_WORKFLOW_STATUS w
                      WHERE o.fulfilment_order_id = w.doc_id
                            AND o.doc_type = w.doc_type
                            AND w.status_id IN (41035, 41535) --41035 - Closed , 41535 - Closed
                            AND w.is_current = 1
                            AND NOT exists(SELECT *
                                           FROM FL_WORKFLOW_STATUS pas, fl_payment_advice pa
                                           WHERE pas.DOC_ID = pa.PAYMENT_ADVICE_ID AND pas.status_id IN (46005) AND pas.is_current = 1
                                                 AND pas.doc_type = 'PA' AND pa.FULFILMENT_ORDER_ID = o.fulfilment_order_id AND
                                                 pa.RECORD_STATUS = 1)
                            AND EXTRACT(YEAR FROM w.created_date) = EXTRACT(YEAR FROM sysdate)
                      ORDER BY w.CREATED_DATE DESC
                    ) x
                  WHERE
                    pav.FULFILMENT_ORDER_ID = x.fulfilment_order_id AND pav.RECORD_STATUS = 1 AND ws.DOC_ID = pav.PAYMENT_ADVICE_ID AND
                    ws.is_current = 1
                    AND ws.doc_type = 'PA'
                  ORDER BY doc_no ASC");
        MigrateUtils::logDump(__METHOD__." > TOTAL FOUND: " . count($listDocNo));
        foreach ($listDocNo as $rawData) {
            self::terminateTasksPOCOClosed($rawData->doc_no);
        }

        Log::info(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
        MigrateUtils::logDump(self::class . ' Completed ' . __METHOD__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }
    
    public static function terminateTasksPOCOClosed($docNo){
        MigrateUtils::logDump('DocNo Find: ' . $docNo);
        Log::info(__METHOD__.' > DocNo Find: ' . $docNo);
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $listDataResult = $bpmTaskServiceProgram->findAPITaskBPMListDocAndModule($docNo, "Fulfilment");
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
            MigrateUtils::logDump('   >> found tasks : ' . count($listdata));

            $collectAssignedTask = collect();
            foreach ($listdata as $key => $row) {
                $listComposite = explode("*", $row['compositeDN']);
                //$listdata[$key]['composite'] = $listComposite[0];
                $row['composite'] = $listComposite[0];
                //if($row['state'] == 'ASSIGNED'){
                    $collectAssignedTask->push($row);
                //}
            }
            //MigrateUtils::logDump($collectAssignedTask);
            // Must be only one instance active with  assigned task.
            // 23/06/2022 remove to check task is assigned or not. 
            //if($collectAssignedTask->count() ==  1){
                $taskDetail = $collectAssignedTask->first();
                $instanceId = $taskDetail['instanceId'];
                $module = $taskDetail['composite'];
                MigrateUtils::logDump('   >> Start to terminate this task. , instanceID : '.$instanceId. ' module: '.$module);
                Log::info(__METHOD__.' >> Start to terminate this task. , instanceID : '.$instanceId. ' module: '.$module);
                $res = $bpmTaskServiceProgram->submitTerminateInstance($module, $instanceId);
                MigrateUtils::logDump($res);
            //}else{
            //    MigrateUtils::logDump('   >> Invalid criteria! No termination instance');
            //    Log::info(__METHOD__.' >> Invalid criteria! No termination instance');
            //}
        }else{
            MigrateUtils::logDump(__METHOD__.' >> ########## not found! '.json_encode($listDataResult));
        } 
    }
    
    public static function terminateTasksBySelectionComposite(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver as d')
            //->whereNull('task_state')
            ->whereNull('is_terminated')
           // ->whereNull('doc_number')
           //->whereNull('doc_status')
           ->where('doc_tracking_status','Cancelled by Year End Process')
            //->whereNotNull('doc_status')
            //->whereRaw("SUBSTR(doc_number,1,2) = 'LA'")
           // ->where('action_step','TERMINATE TASK ONLY')
            //->whereRaw("YEAR(ct_exp_date) <= 2021")
            //->where('ct_status','EXPIRED')
            //->whereNotNull('action_step')
            //->whereRaw("NOT EXISTS (SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver xx WHERE xx.`composite_instance_id` = d.composite_instance_id AND xx.doc_number IS NOT NULL)")
             ->whereIn('composite_version',['default/Order!1.0.9','default/Order!1.0.8'])
            /*
            ->whereIn('composite_version',[
                'default/Codification!1.0.1',
                //'default/Contract_Management!1.0.6',
                //'default/Contract_Management!1.0.7'
                ])
            */
            // ->whereRaw("YEAR(composite_created) = 2018 ")
            //->where('remark_status_doc','Not found to get info contract detail' )
            //->where('composite_state','<>','16' )
            //->whereNull('qt_no')
            //->whereNull('doc_status')
            // ->where('doc_status','Registered')
            //->whereRaw("year(composite_created) = 2022")
            //->whereRaw("month(composite_created) <= 7")
            ->select('composite_instance_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status','action_step')
            ->take(1000)
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $counter  = 1;
        foreach ($listInstances as $inst){
            if($inst->composite_instance_id === 20869237) {
               dump('OK skip instance ID: '.$inst->composite_instance_id) ;
            }else{
                //dd($inst);
                MigrateUtils::logDump(__METHOD__.'   >> Start to terminate this task. , Year Created : '.$inst->composite_created. ', instanceID : '.$inst->composite_instance_id. ' module: '.$inst->composite_version);
                
                $res = $bpmTaskServiceProgram->submitTerminateInstance($inst->composite_version, $inst->composite_instance_id);
                MigrateUtils::logDump($res);
                if($res != null){
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$inst->composite_instance_id)
                    ->update(
                        [
                            'remark_status_doc' =>  'Successfully terminated instance',
                            'is_terminated' => 1,
                            'changed_at' => Carbon::now()
                        ]
                    );
                    if($counter === 1000){
                        MigrateUtils::logDump(__METHOD__.'Done Completed 1000 terminated!');
                        //return;
                    }
                    $counter ++;
                    
                }
                
                
        
            }
            
            //dd($inst);
        }
        MigrateUtils::logDump(__METHOD__.' > Completed terminated:  '.count($listInstances) );
       
    }


    public static function findPayloadCT(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver_arc as d')
            //->where('action_step','like','TERMINATE TASK, REFIRE TASK%')
            ->where('action_remark','like','E06: CT STATUS AS EXPIRED AND EXPIRED YEAR ON 2022%')
            //->whereNotNull('action_step')
            ->whereNotNull('bpm_task_id')
            ->whereNull('ct_payload')
            ->select('composite_instance_id','bpm_task_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        foreach($listInstances as $inst){
            $compositeInstanceId = $inst->composite_instance_id ;
            $bpmTaskId = $inst->bpm_task_id ;
            $bpmTaskServiceProgram = new BPMTaskServiceProgram();
            $res = $bpmTaskServiceProgram->findApiWorklistTaskDetail($bpmTaskId);
            if($res != null && $res['status'] === 'Success' && isset($res['result']['payload']) && count($res['result']['payload']) > 0){

                $payload = $res['result']['payload'][0];
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$compositeInstanceId )
                    ->where('bpm_task_id',$bpmTaskId)
                    ->update([
                            'ct_payload' =>  $payload
                        ]);
                MigrateUtils::logDump('Updated payload :: '.$res['result']['taskId']);
            }
        }
    }

    public static function refireCTSA(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver_arc as d')
            //->where('action_step','TERMINATE TASK, REFIRE TASK SA')
            //->where('composite_instance_id',63703294)
            ->where('action_remark','like','E06: CT STATUS AS EXPIRED AND EXPIRED YEAR ON 2022%')
            ->whereNotNull('action_step')
            ->whereNotNull('bpm_task_id')
            ->whereNotNull('ct_payload')
            //->where('doc_status_id','57000')
            ->whereNull('act_is_refire')
            ->where('doc_type','SA')
            ->select('composite_instance_id','bpm_task_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status','ct_payload','act_is_refire')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        dd('done');
        $listInstances = collect($listInstances)->take(1);
        MigrateUtils::logDump(__METHOD__.' > total after limit '.count($listInstances) );
        //dd('ok');
        foreach($listInstances as $inst){
            //dd($inst);
            $compositeInstanceId = $inst->composite_instance_id ;
            $bpmTaskId = $inst->bpm_task_id ;
            $bpmTaskServiceProgram = new BPMTaskServiceProgram();

            $checklistTasksByInst = collect($bpmTaskServiceProgram->listTaskDetailBpmByInstanceId($inst->composite_instance_id));
            $totalTaskAssigned = $checklistTasksByInst->where('state','ASSIGNED')->count();
            if($totalTaskAssigned > 8){
                dump('Total task assigned: '.$totalTaskAssigned. ', docNo: '.$inst->doc_number );
            }else{
            
                
                $res = $bpmTaskServiceProgram->submitTerminateInstance($inst->composite_version, $inst->composite_instance_id);
                MigrateUtils::logDump(json_encode($res) .' instanceID: '.$inst->composite_instance_id);
                if($res != null){
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$inst->composite_instance_id)
                    ->update([
                            'remark_status_doc' =>  'Successfully terminated instance', 'is_terminated' => 1, 'changed_at' => Carbon::now() ]
                    );
                }

                $checkDocType = substr($inst->doc_number, 0, 1);
                $objRes = null;
                $statusId = '57000';
                $docType = 'SA';
                if($checkDocType=='L' || $checkDocType=='N'){
                    $objRes = self::getContractDocACWorkFlowInfoUser(null,$inst->doc_number,$statusId,$docType);
                }else{
                    $objRes = self::getContractDocACWorkFlowInfoUser($inst->doc_number,null,$statusId,$docType);
                }

                if($objRes == null){
                    $objRes = self::getContractDocACWorkFlowInfoUser($inst->doc_number,null,56000,'AC');
                }
                MigrateUtils::logDump(__METHOD__.' Find ministry contract admin:  '.json_encode($objRes) );
                $loginCtAdminId = null;
               
                if($objRes != null){

                    if($objRes->is_contract_admin === 'MIN_CONTRACT_ADMIN'){
                        $loginCtAdminId = $objRes->login_id;
                    }else{
                        $loginCtAdminId = $objRes->active_contract_admin;
                    }
                }    
                $url = 'Contract_Management';
                $trigger = 'startAgreement';
                $process = 'AgreementCreation';
                $elements = [
                    [
                        "name"=>"CT_Agreement_Data",
                        "value"=>$inst->ct_payload,
                        "type"=>"string"
                    ]
                ]; 
                
                $createService = $bpmTaskServiceProgram->findApiBPMCreateServiceManager($url, $process, $trigger, $elements);

                MigrateUtils::logDump('refire status :: '.json_encode($createService).' , DocNo: '.$inst->doc_number);
            
                if($createService  != null && $createService['status'] === 'Success'){
                    sleep(5);
                    $activityName = 'Create Draft AC/SA';
                    $taskState = 'ASSIGNED';
                    $listTasksByInst = collect($bpmTaskServiceProgram->listTaskDetailBpmByDocStateActivity($inst->doc_number,$taskState,$activityName));
                    $taskObj = $listTasksByInst->where('activityname',$activityName)->where('state',$taskState)->first();
                    sleep(2);
                    $resultExecReAssign = $bpmTaskServiceProgram->reassignTask($taskObj->taskid,$loginCtAdminId);
                    MigrateUtils::logDump(__METHOD__.'  >> Success re-assigned task '.json_encode($resultExecReAssign).' to '.$loginCtAdminId);

                    $bpmTaskServiceProgram->updateRefireCompleted($compositeInstanceId,$inst->doc_number,$taskObj->compositeinstanceid);
                    MigrateUtils::logDump(__METHOD__.' Done refire :: compositeInstanceId:'.$compositeInstanceId.' ,TaskID: '.$bpmTaskId. ' , DocNo: '.$inst->doc_number. ' , NewCompositeId: '.$taskObj->compositeinstanceid);
                }
            //dd('ok');
            }
           
        }
        
        
    }

    public static function refireCTAC(){
        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver_arc as d')
            //->where('action_step','TERMINATE TASK, REFIRE TASK AC')
            ->whereNotNull('action_step')
            //->whereNotNull('bpm_task_id')
            ->whereNotNull('ct_payload')
            ->where('doc_number','LA220000000025192')
            ->where('doc_type','AC')
            //->whereNull('act_is_refire')
            //->whereRaw("NOT EXISTS ( SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver  g WHERE g.doc_number = d.doc_number AND g.doc_type != d.doc_type)")
            ->select('composite_instance_id','bpm_task_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status','ct_payload','act_is_refire','ct_no')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        $listInstances = collect($listInstances)->take(500);
        MigrateUtils::logDump(__METHOD__.' > total after limit '.count($listInstances) );

        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $counter = 1;
        foreach($listInstances as $inst){

            $res = $bpmTaskServiceProgram->submitTerminateInstance($inst->composite_version, $inst->composite_instance_id);
            MigrateUtils::logDump("$counter) ".json_encode($res) .' instanceID: '.$inst->composite_instance_id);
            $counter++;
            if($res != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$inst->composite_instance_id)
                ->update([
                        'remark_status_doc' =>  'Successfully terminated instance', 'is_terminated' => 1, 'changed_at' => Carbon::now() ]
                );
            }

            /*** PLEASE CHECK CAREFULLY ON THIS ID TO CONFIRM USE THIS. ***/
            /*
            SELECT creationdate,PROCESSID ,PROCESSNAME ,COMPOSITENAME ,REVISION ,
                label,APPLICATIONLINKDISPLAYNAME  
                    FROM  BPM_CUBE_PROCESS bcp  WHERE COMPOSITENAME  = 'Contract_Management' 
                    AND processname = 'StartAgreementCreation'
            */
            $processId=348339;  //process initiate StartAgreementCreation

            // To get Contract Admin login ID 
            $checkDocType = substr($inst->doc_number, 0, 1);
            $objRes = null;
            $statusId = '56000';
            $docType = 'AC';
            if($checkDocType=='L' || $checkDocType=='N'){
                $objRes = self::getContractDocACWorkFlowInfoUser(null,$inst->doc_number,$statusId,$docType);
            }else{
                $objRes = self::getContractDocACWorkFlowInfoUser($inst->doc_number,null,$statusId,$docType);
            }
            $loginCtAdminId = null;
            if($objRes != null && $objRes->is_contract_admin == 'MIN_CONTRACT_ADMIN'){
                MigrateUtils::logDump(__METHOD__.'CtAdminLoginID: '.$objRes->login_id.' ,  docNo: '.$inst->doc_number.' , contractNo: '.$inst->ct_no.' , LoaNo: '.$objRes->loa_no  );
                $loginCtAdminId = $objRes->login_id;
                
            }else{
                if($objRes != null){
                    if(strlen($objRes->active_contract_admin) > 0 ){
                        $loginCtAdminId = $objRes->active_contract_admin;
                        //MigrateUtils::logDump(__METHOD__.' > using active contract admin, current contract admin is not valid: '.$objRes->login_id );
                    }else{
                        MigrateUtils::logDump(__METHOD__.' > not found get contract admin user, docNo: '.$inst->doc_number.' , contractNo: '.$inst->ct_no );
                    }
                }else{
                    MigrateUtils::logDump(__METHOD__.' > not found by search docNo: '.$inst->doc_number );
                }
            }
            
            if(strlen($loginCtAdminId ) > 0 ){
                $payload =  str_replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>","",$inst->ct_payload);
                //MigrateUtils::logDump(__METHOD__.' > ready to refire : '.$loginCtAdminId.' , docNo: '.$inst->doc_number );
                $res = $bpmTaskServiceProgram->initiateProcessAPI($processId,$loginCtAdminId);
                MigrateUtils::logDump(__METHOD__."  docNo: $inst->doc_number initiateProcessAPI [$processId,$loginCtAdminId] >> result : ".json_encode($res));
                if($res && $res['status'] === "Success"){
                    //MigrateUtils::logDump(__METHOD__.'  Successfully initiate task');
                    $offset = 0;
                    $limit = 5;
                    $assignment = "MY";
                    $resList = $bpmTaskServiceProgram->findApiWorklist($loginCtAdminId, "ASSIGNED", $assignment, $offset, $limit);
                    //MigrateUtils::logDump(__METHOD__.' LOGIN ID: '.$loginCtAdminId.' for appl no. : '.$inst->doc_number);
                    if($resList && $resList['status'] === "Success" && isset($resList['result']['worklistItem']) ){
                        //MigrateUtils::logDump(__METHOD__.'  Found task initiate');
                        $listTask = collect($resList['result']['worklistItem']);
                        if($listTask->count() === 0){
                            MigrateUtils::logDump(__METHOD__.'  Not found task initiate. Stop!');
                            return "#### Need to check.. task already initiated. must do manual to update payload";
                        }
                        
                        $label = 'Start Agreement Creation';
                        $taskName = "Create AC";
                        $task = $listTask->where('process',$label)->where('taskName',$taskName)->where('state','ASSIGNED')->first();
                        MigrateUtils::logDump(__METHOD__.' Found task ID: '.$task['taskId'].' process: '.$task['process'].' , InstanceID: '.$task['instanceId']);
                        
                        
                        $executeAction='DRAFT';
                        $param = array(
                            0 => $inst->doc_number,
                            1 => 'AC',
                            2 => $objRes->agreement_id,
                            3 => '56000',  // Pending Draft Agreement Creation
                        );

                        $resultExec = $bpmTaskServiceProgram->updateExecuteActionAPI($task['taskId'], $loginCtAdminId, $payload, $executeAction, $param);
                        MigrateUtils::logDump(__METHOD__.' STATUS EXECUTE TASK AS DRAFT >> '.json_encode($resultExec ));
                     
                        if($resultExec && $resultExec['status'] === "Success" ){
                            //MigrateUtils::logDump(__METHOD__.' Success submit as DRAFT');
                            sleep(5);
                            $listTasksByInst = collect($bpmTaskServiceProgram->listTaskDetailBpmByInstanceId($task['instanceId']));

                            $processId = 'StartAgreementCreation';
                            $activityName = "Submit AC";
                            $taskState = 'ASSIGNED';
                            //MigrateUtils::logDump(__METHOD__.' > '.json_encode($listTasksByInst->where('state',$taskState)->first()));
                            $taskObj = $listTasksByInst->where('processid',$processId)->where('activityname',$activityName)->where('state',$taskState)->first();
                            
                            $resultExecReAssign = $bpmTaskServiceProgram->reassignTask($taskObj->taskid,$loginCtAdminId);
                            MigrateUtils::logDump(__METHOD__.' Success re-assigned task '.json_encode($resultExecReAssign));
                            $bpmTaskServiceProgram->updateRefireCompleted($inst->composite_instance_id,$inst->doc_number,$task['instanceId']);
                            MigrateUtils::logDump("#################  <><><><>  #################");
                            MigrateUtils::logDump("");
                        }
                        
                    }else{
                        MigrateUtils::logDump(__METHOD__." #### Need to check.. task already initiated. must do manual to update payload");
                    }
        
                }else{
                    MigrateUtils::logDump(__METHOD__." Failed initiate to create task");
                }
            }
            //dd('ok');
            
        }

    }
   
    public static function refireCTFD(){
        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver as d')
            ->where('action_step','TERMINATE TASK, REFIRE TASK FD')
            ->whereNotNull('action_step')
            ->whereNotNull('bpm_task_id')
            ->whereNotNull('ct_payload')
            //->where('doc_status_id','50050')
            ->where('doc_type','FD')
            ->whereNull('act_is_refire')
            //->whereRaw("NOT EXISTS ( SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver  g WHERE g.doc_number = d.doc_number AND g.doc_type != d.doc_type)")
            ->select('composite_instance_id','bpm_task_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status','ct_payload','act_is_refire','ct_no')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        $listInstances = collect($listInstances)->take(500);
        MigrateUtils::logDump(__METHOD__.' > total after limit '.count($listInstances) );

        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        foreach($listInstances as $inst){

            $res = $bpmTaskServiceProgram->submitTerminateInstance($inst->composite_version, $inst->composite_instance_id);
            MigrateUtils::logDump(json_encode($res) .' instanceID: '.$inst->composite_instance_id);
            if($res != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$inst->composite_instance_id)
                ->update([
                        'remark_status_doc' =>  'Successfully terminated instance', 'is_terminated' => 1, 'changed_at' => Carbon::now() ]
                );
            }

            /*** PLEASE CHECK CAREFULLY ON THIS ID TO CONFIRM USE THIS. ***/
            /*
            SELECT creationdate,PROCESSID ,PROCESSNAME ,COMPOSITENAME ,REVISION ,
                label,APPLICATIONLINKDISPLAYNAME  
                    FROM  BPM_CUBE_PROCESS bcp  WHERE COMPOSITENAME  = 'Contract_Management' 
                    AND processname = 'StartContractCreation'
            */
            $processId=348220;  //process initiate StartContractCreation

            // To get Contract Admin login ID 
            $checkDocType = substr($inst->doc_number, 0, 1);
            $objRes = null;
            if($checkDocType=='L' || $checkDocType=='N'){
                $objRes = self::getContractDocFDWorkFlowInfoUser(null,$inst->doc_number);
            }else{
                $objRes = self::getContractDocFDWorkFlowInfoUser($inst->doc_number);
            }
            $loginCtAdminId = null;
            if($objRes != null){
                MigrateUtils::logDump(__METHOD__.'  >> CtAdminLoginID: '.$objRes->login_id.' ,  docNo: '.$inst->doc_number.' , contractNo: '.$inst->ct_no.' , LoaNo: '.$objRes->loa_no,' , current_contract_ver_id: '.  $objRes->current_contract_ver_id);
                if( $objRes->is_contract_admin == 'MIN_CONTRACT_ADMIN'){
                    $loginCtAdminId = $objRes->login_id;
                }elseif(strlen($objRes->active_contract_admin) > 0 ){
                            $loginCtAdminId = $objRes->active_contract_admin;
                            MigrateUtils::logDump(__METHOD__.' > using active contract admin, current contract admin is not valid: '.$objRes->login_id );
                }else{
                            MigrateUtils::logDump(__METHOD__.'  >> not found get contract admin user, docNo: '.$inst->doc_number.' , contractNo: '.$inst->ct_no );
                }
            }else{
                MigrateUtils::logDump(__METHOD__.'  >> not found by search docNo: '.$inst->doc_number );
            }
            
            if(strlen($loginCtAdminId ) > 0 ){
                $payload =  str_replace("<?xml version=\"1.0\" encoding=\"UTF-8\"?>","",$inst->ct_payload);
                MigrateUtils::logDump(__METHOD__.'  >> ready to refire : '.$loginCtAdminId.' , docNo: '.$inst->doc_number );
                $res = $bpmTaskServiceProgram->initiateProcessAPI($processId,$loginCtAdminId);
                MigrateUtils::logDump(__METHOD__."  >> initiateProcessAPI [$processId,$loginCtAdminId] >> result : ".json_encode($res));
                if($res && $res['status'] === "Success"){
                    MigrateUtils::logDump(__METHOD__.'  >> Successfully initiate task');
                    $offset = 0;
                    $limit = 5;
                    $assignment = "MY";
                    $resList = $bpmTaskServiceProgram->findApiWorklist($loginCtAdminId, "ASSIGNED", $assignment, $offset, $limit);
                    MigrateUtils::logDump(__METHOD__.'  >> LOGIN ID: '.$loginCtAdminId.' for appl no. : '.$inst->doc_number);
                    if($resList && $resList['status'] === "Success" && isset($resList['result']['worklistItem']) ){
           
                        MigrateUtils::logDump(__METHOD__.'  >> Found task initiate');
                        $listTask = collect($resList['result']['worklistItem']);
                        if($listTask->count() === 0){
                            MigrateUtils::logDump(__METHOD__.'  >> Not found task initiate. Stop!');
                            return "#### Need to check.. task already initiated. must do manual to update payload";
                        }
                        
                        $label = 'Start Contract Creation';
                        $taskName = "Create FD";
                        $task = $listTask->where('process',$label)->where('taskName',$taskName)->where('state','ASSIGNED')->first();
                        MigrateUtils::logDump(__METHOD__.'  >> Found task ID: '.$task['taskId'].' process: '.$task['process'].' , InstanceID: '.$task['instanceId']);
                        
                        
                        $executeAction='DRAFT';
                        $param = array(
                            0 => $inst->doc_number,
                            1 => 'FD',
                            2 => $objRes->current_contract_ver_id,
                            3 => '50050',  // Pending Draft Agreement Creation
                        );

                        $resultExec = $bpmTaskServiceProgram->updateExecuteActionAPI($task['taskId'], $loginCtAdminId, $payload, $executeAction, $param);
                        MigrateUtils::logDump(__METHOD__.'  >> STATUS EXECUTE TASK AS DRAFT >> '.json_encode($resultExec ));
                        
                        if($resultExec && $resultExec['status'] === "Success" ){
                            MigrateUtils::logDump(__METHOD__.'  >> Success submit as DRAFT');
                            sleep(5);
                            $listTasksByInst = collect($bpmTaskServiceProgram->listTaskDetailBpmByInstanceId($task['instanceId']));
                            
                            $processId = 'StartContractCreation';
                            $activityName = "Submit FD";
                            $taskState = 'ASSIGNED';
                            $taskObj = $listTasksByInst->where('processid',$processId)->where('activityname',$activityName)->where('state',$taskState)->first();
                            
                            if($taskObj != null){
                                $resultExecReAssign = $bpmTaskServiceProgram->reassignTask($taskObj->taskid,$loginCtAdminId);
                                MigrateUtils::logDump(__METHOD__.'  >> Success re-assigned task '.json_encode($resultExecReAssign));
                                $bpmTaskServiceProgram->updateRefireCompleted($inst->composite_instance_id,$inst->doc_number,$task['instanceId']);
                                MigrateUtils::logDump("#################  <><><><>  #################");
                            
                            }else{
                                MigrateUtils::logDump(__METHOD__.'  >> Failed to re-assign. task not found '.json_encode($listTasksByInst )); 
                            }
                           
                        }
                        
                    }else{
                        MigrateUtils::logDump(__METHOD__."  >> #### Need to check.. task already initiated. must do manual to update payload");
                    }
        
                }else{
                    MigrateUtils::logDump(__METHOD__."  >> Failed initiate to create task");
                }
            }
            //dd('ok');
            
        }

    }

    public static function terminateUpdateVerCT($limit){
        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver as d')
            ->where('action_step','TERMINATE TASK & UPDATE VER')
            ->whereNull('is_update_ver')
            //->whereRaw("NOT EXISTS ( SELECT 1 FROM ep_bpm_support.bpm_instance_old_ver  g WHERE g.doc_number = d.doc_number AND g.doc_type != d.doc_type)")
            ->select('composite_instance_id','bpm_task_id','composite_created','composite_version','composite_state','task_state','doc_number','doc_status','ct_payload','act_is_refire','ct_no')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        
        $listInstances = collect($listInstances)->take($limit);
        MigrateUtils::logDump(__METHOD__.' > total after limit '.count($listInstances) );
        //dd('done');
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        foreach($listInstances as $inst){

            $res = $bpmTaskServiceProgram->submitTerminateInstance($inst->composite_version, $inst->composite_instance_id);
            MigrateUtils::logDump(json_encode($res) .' instanceID: '.$inst->composite_instance_id. ' versionCt : '.$inst->composite_version);
            if($res != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$inst->composite_instance_id)
                ->update([
                        'remark_status_doc' =>  'Successfully terminated instance', 'is_terminated' => 1, 'changed_at' => Carbon::now() ]
                );
            }
            $ctObj = $bpmTaskServiceProgram->updateToResetCTVersion($inst->ct_no);
            if($ctObj != null ){
                $bpmTaskServiceProgram->updateTerminateSyncVerCompleted($inst->composite_instance_id,$inst->doc_number);
            }

            //dd('ok');
            
        }

    }

    public static function initiateTaskSmApplication($docNo){
        MigrateUtils::logDump('DocNo Find: ' . $docNo);
        $result = collect([]);
        $result->put('status','Failed');
        $data = collect([]);
        $data->put('doc_no',$docNo);
        $data->put('task_performer','');

        if(self::isStuckInitiateSmApplTask($docNo) === false){
            $result->put('status','Failed');
            $errorResp = "Invalid. Appl No is not as stuck task";
            $data->put('response',$errorResp);
            $result->put('result',$data);
            return $result;
        }

        $bpmTaskServiceProgram = new BPMTaskServiceProgram();

        $checkDocType = substr($docNo, 0, 2);
        $isPayment = 'false';
        if($checkDocType==='KN' || $checkDocType==='KR' || $checkDocType==='JN' || $checkDocType==='JR' ){
            $isPayment = 'true';
        }

        $payload = null;
        $loginId=null;
        $listDataResult = $bpmTaskServiceProgram->doAPIgetPayloadSMTaskApplication($docNo, $isPayment);
        if($listDataResult['status']=='Success' && isset($listDataResult['result']) && isset($listDataResult['result']['payload'])){
            
            $payload = str_replace('\"', '"', $listDataResult['result']['payload']);
            $loginId = $listDataResult['result']['task_performer'];
            MigrateUtils::logDump('loginId '.$loginId );
            //MigrateUtils::logDump($payload);
        }else{
            $result->put('status','Failed');
            $errorResp = "Invalid. Could not get payload!";
            $data->put('response',$errorResp);
            $result->put('result',$data);
            return $result;
        }
        $data->put('task_performer',$loginId);

        /*** PLEASE CHECK CAREFULLY ON THIS ID TO CONFIRM USE THIS. ***/
        /*
        SELECT PROCESSID ,PROCESSNAME ,COMPOSITENAME ,REVISION ,
            label,APPLICATIONLINKDISPLAYNAME  FROM  BPM_CUBE_PROCESS bcp  WHERE 
            label  = 'Start MOF Supplier Registration' 
            AND COMPOSITENAME  = 'Supplier_Management' 
            AND REVISION  = '1.0.8'
        */
        $processId=null;  //process initiate Start MOF Registration

        // ## Get Process ID for Initiate : StartMOFSupplierRegistration
        $resListInitiate = $bpmTaskServiceProgram->worklistTaskInitiable($loginId);
        if($resListInitiate['status']=='Success' && isset($resListInitiate['result']) && count($resListInitiate['result']) > 0){
            
            $collectList = collect($resListInitiate['result']);
            try {
                $objStartMofInit = $collectList->where('compositeName','Supplier_Management')->where('processName','StartMOFSupplierRegistration')->where('default',true)->first();
                $processId=$objStartMofInit['id'];
                MigrateUtils::logDump('Found process initiate Start MOF Registration :  '.$processId );
            } catch (\Throwable $th) {
                MigrateUtils::logDump('Error getting process initiate Start MOF Registration :   '.$th->getMessage() );

                $result->put('status','Failed');
                $errorResp = "Please check role this user : $loginId . Could not get task initiate StartMOFSupplierRegistration ";
                $data->put('response',$errorResp);
                $result->put('result',$data);
                return $result;
            }
        }else{
            $result->put('status','Failed');
            $errorResp = "Invalid. Could not get initiable task for user : $loginId";
            $data->put('response',$errorResp);
            $result->put('result',$data);
            return $result;
        }

        $res = $bpmTaskServiceProgram->initiateProcessAPI($processId,$loginId);
        //MigrateUtils::logDump($res);
        if($res && $res['status'] === "Success"){
            MigrateUtils::logDump(' Successfully initiate task');
            $offset = 0;
            $limit = 5;
            $assignment = "MY";
            sleep(3);
            $resList = $bpmTaskServiceProgram->findApiWorklist($loginId, "ASSIGNED", $assignment, $offset, $limit);
            //MigrateUtils::logDump($resList);
            MigrateUtils::logDump('LOGIN ID: '.$loginId.' for appl no. : '.$docNo);
            //dd($resList);
            if($resList && $resList['status'] === "Success" && isset($resList['result']['worklistItem']) ){
                MigrateUtils::logDump(' Found task..');
                $listTask = collect($resList['result']['worklistItem']);
                if($listTask->count() === 0){
                    $result->put('status','Failed');
                    $errorResp = "#### Need to check.. task already initiated. must do manual to update payload or loginID not sufficient role";
                    $data->put('response',$errorResp);
                    $result->put('result',$data);
                    return $result;
                }
                $label = 'StartMOFSupplierRegistration';
                $task = $listTask->where('process',$label)->first();
                //MigrateUtils::logDump($task); $task['instanceId']
                MigrateUtils::logDump('Found task ID: '.$task['taskId'].' process: '.$task['process'].' , InstanceID:'.$task['instanceId']);
                $executeAction='DRAFT';
                $param = array(
                    0 => $docNo,
                    1 => '',
                    2 => '0',
                    3 => '0',
                );

                
                $resultExec = $bpmTaskServiceProgram->updateExecuteActionAPI($task['taskId'], $loginId, $payload, $executeAction, $param);
                MigrateUtils::logDump('STATUS EXECUTE TASK AS DRAFT');
                MigrateUtils::logDump($resultExec);

                $result->put('status','Success');
                $resp = "Success & Completed : ".json_encode($resultExec);
                $data->put('response',$resp);
                $result->put('result',$data);
                return $result;
            }else{
                $result->put('status','Failed');
                $errorResp = "#### Need to check.. task already trigger initiate. Maybe failed! ";
                $data->put('response',$errorResp);
                $result->put('result',$data);
                return $result;
            }

        }else{
            $result->put('status','Failed');
            $errorResp = "Failed initiate to create task";
            $data->put('response',$errorResp);
            $result->put('result',$data);
            return $result;
        }

    }

    public static function checkAndrefirePaidSoftcertTaskSmApplication($docNo){
        MigrateUtils::logDump('DocNo Find: ' . $docNo);

        $objSuppSoftcert = self::isStuckPaidSoftcertTask($docNo);
        if( $objSuppSoftcert === null){
             return "Invalid. Appl No is not as stuck task";
        }else{
            $objData = collect([]);
            $objData->put('login_id',$objSuppSoftcert->login_id);
            $objData->put('appl_id',$objSuppSoftcert->appl_id);
            $objData->put('user_id',$objSuppSoftcert->user_id);
            $objData->put('pending_process_id',$objSuppSoftcert->pending_process_id);
            $objData->put('personnel_id',$objSuppSoftcert->personnel_id);
            $objData->put('doc_no',$docNo);
            return self::refirePaidSoftcertTaskSmApplication($objData);
        }
    }

    public static function refirePaidSoftcertTaskSmApplication($objData){
        
        if( $objData->count() < 5){
            return "Invalid Parameter";
        }

        $loginId = $objData->get('login_id');
        $applId = $objData->get('appl_id');
        $userId = $objData->get('user_id');
        $pendingProcessId = $objData->get('pending_process_id');
        $personnelId = $objData->get('personnel_id');
        $docNo = $objData->get('doc_no');

        $bpmTaskServiceProgram = new BPMTaskServiceProgram();

        $module = 'Supplier_Management';
        $isTaskSoftcertNeedRefire= true; //set default as need refire true
        $isSuccessSubmit = false;

        // Checking task payment is Assigned 
        $listTask = $bpmTaskServiceProgram->findAPITaskBPMListDocAndModule($docNo,$module);
        if($listTask && $listTask['status']=='Success' && isset($listTask['result']) && count($listTask['result']) > 0){
            $collectTasks = collect($listTask['result']);
            try {
                MigrateUtils::logDump("Login ID : ".$loginId);
                $objTaskPaymentSoftcert = $collectTasks->where('compositeName',$module)->where('process','UserAddAdditional')
                    ->where('state','ASSIGNED')->first();
                if($objTaskPaymentSoftcert != null){
                    MigrateUtils::logDump('Task Payment Exist : Softcert UserAddAdditional:  '.$objTaskPaymentSoftcert->taskId. ' , InstanceID: '.$objTaskPaymentSoftcert->instanceId );
                    $isTaskSoftcertNeedRefire = false;
                }else{
                    MigrateUtils::logDump('Task Payment Softcert UserAddAdditional not exist');
                }
               
            } catch (\Throwable $th) {
                MigrateUtils::logDump("Error getting find task by docNumber :  $docNo , error >>  ".$th->getMessage() );
                return "Error getting find task by docNumber :  $docNo , error >>  ".$th->getMessage() ;
            }
        }
  
        // Need to refire task payment softcert
        if( $isTaskSoftcertNeedRefire === true){
            $processId=null;  //process initiate UserAddAdditional

            // ## Get Process ID for Initiate : UserAddAdditional
            $resListInitiate = $bpmTaskServiceProgram->worklistTaskInitiable($loginId);
            if($resListInitiate && $resListInitiate['status']=='Success' && isset($resListInitiate['result']) && count($resListInitiate['result']) > 0){
                
                $collectList = collect($resListInitiate['result']);
                try {
                    $objStartMofInit = $collectList->where('compositeName',$module)->where('processName','UserAddAdditional')->where('default',true)->first();
                    $processId=$objStartMofInit['id'];
                    MigrateUtils::logDump('Found process initiate UserAddAdditional:  '.$processId );
                } catch (\Throwable $th) {
                    MigrateUtils::logDump('Error getting process initiate UserAddAdditional:   '.$th->getMessage() );
                    return "Please check role this user : $loginId . Could not get task initiate UserAddAdditional ";
                }
            }else{
                return "Invalid. Could not get initiable task for user : $loginId";
            }


            $res = $bpmTaskServiceProgram->initiateProcessAPI($processId,$loginId);
            //MigrateUtils::logDump($res);
            if($res && $res['status'] === "Success"){
                MigrateUtils::logDump(' Successfully initiate task : UserAddAdditional');
                $offset = 0;
                $limit = 5;
                $assignment = "MY";
                sleep(3);
                $resList = $bpmTaskServiceProgram->findApiWorklist($loginId, "ASSIGNED", $assignment, $offset, $limit);
                //MigrateUtils::logDump($resList);
                MigrateUtils::logDump('LOGIN ID: '.$loginId.' for appl no. : '.$docNo);
                //dd($resList);
                if($resList && $resList['status'] === "Success" && isset($resList['result']['worklistItem']) ){
                    MigrateUtils::logDump(' Found task..');
                    $listTask = collect($resList['result']['worklistItem']);
                    if($listTask->count() === 0){
                        return "#### Need to check.. task already initiated. must do manual to update payload or loginID not sufficient role";
                    }
                    $label = 'UserAddAdditional';
                    $task = $listTask->where('process',$label)->first();
                    //MigrateUtils::logDump($task); $task['instanceId']
                    MigrateUtils::logDump('Found task ID: '.$task['taskId'].' process: '.$task['process'].' , InstanceID:'.$task['instanceId']);
                    $executeAction='SUBMIT';
                    $param = array(
                        0 => $docNo,
                        1 => '',
                        2 => '0',
                        3 => '0',
                    );
                    $payload = '<SM_UserAdd_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SM_UserAdd_Data">
                    <ptj xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:nil="true"/>
                    <document_id>'.$applId.'</document_id>
                    <user_id>'.$userId.'</user_id>
                    <supplier_list>'.$loginId.'</supplier_list>
                    </SM_UserAdd_Data>';
                    
                    $resultExec = $bpmTaskServiceProgram->updateExecuteActionAPI($task['taskId'], $loginId, $payload, $executeAction, $param);
                    MigrateUtils::logDump('STATUS EXECUTE TASK AS SUBMIT');
                    MigrateUtils::logDump($resultExec);
                    $isSuccessSubmit = true;
                    
                }else{
                    return "#### Need to check.. task already trigger initiate. Maybe failed! ";
                }

            }else{
                return "Failed initiate to create task";
            }
        }

        if($isSuccessSubmit == true){
            // Update SM_PENDING_PROCESS AS attempt = 0 
            DB::connection('oracle_nextgen_fullgrant')
            ->table('SM_PENDING_PROCESS')->where('pending_process_id',$pendingProcessId)
            ->update(
                [
                    'attempt' => 0,
                    'record_status' => 8,
                    'is_pickup' => 0
                ] 
            );
            MigrateUtils::logDump('Done update SM_PENDING_PROCESS > pending_process_id >> '.$pendingProcessId. ' to attemp : 0 and record_status = 8');

            // Update SM_PERSONNEL is_softcert = 2
            DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_PERSONNEL')->where('personnel_id',$personnelId)
                    ->update(
                        [
                            'is_softcert' => 2
                        ] 
                    );
            MigrateUtils::logDump('Done update SM_PERSONNEL > personnel_id >> '.$personnelId. ' to is_softcert : 2');        
            return "Completed";
        }else{
            return "Failed get any information about task";
        }
        

    }

    protected static function isStuckInitiateSmApplTask($applNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT appl_id,appl_no,is_active_appl FROM sm_appl WHERE appl_no = ?  AND is_active_appl = 1 
                AND NOT EXISTS (SELECT * FROM sm_workflow_status WHERE doc_type = 'SR' AND doc_id = appl_id AND status_id = '20100') ",array($applNo));

        if(count($results) > 0){
            return true;
        }
        return false;
    }

    protected static function isStuckPaidSoftcertTask ($applNo) {
        /*** Issue stuck on softcert request after payment success paid. ****/
        $results = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT
            DISTINCT 
            ss.supplier_id,
            ss.company_name,
            ss.ep_no,
            a.appl_id,
            pb.bill_no,
            pb.bill_type,
            pb.bill_date,
            pb.bill_ref_id,
            pb.payment_due_date,
            pb.bill_amt,
            pp.payment_date,
            pp.payment_id,
            pp.bill_id,
            CONCAT(CONCAT((SELECT code_desc FROM pm_parameter_desc WHERE parameter_id = pp.payment_mode_id AND language_code = 'ms'), ' - '), pp.payment_mode_id) AS payment_mode,
            pp.payment_amt,
            CONCAT(CONCAT((SELECT status_name FROM PM_STATUS_DESC WHERE status_id = pp.status_id AND language_code = 'ms'), ' - '), pp.status_id) AS status,
            pp.receipt_no ,
            ppo.payment_gateway,
            pbd.bill_dtl_ref_id AS ref_id,
            pp.CREATED_BY ,
            u.login_id,
            sp.personnel_id,
            sp.user_id,
            sp.identification_no,
            sp.NAME,
            sp.IS_SOFTCERT ,
            spp.pending_process_id,
            spp.attempt AS pending_process_attempt,
            spp.err_msg AS pending_process_err_msg,
            spp.record_status AS pending_process_status
        FROM
            sm_supplier ss ,
            sm_appl a,
            pm_user u,
            py_bill pb ,
            py_payment pp ,
            py_payment_dtl ppd ,
            py_payment_order ppo,
            PY_BILL_DTL pbd,
            SM_PENDING_PROCESS spp,
            sm_personnel sp
        WHERE
            ss.supplier_id = pb.ORG_PROFILE_ID
            AND sp.PERSONNEL_ID = pbd.BILL_DTL_REF_ID
            AND a.APPL_NO	 = pb.BILL_NO 
            AND pp.CREATED_BY  = u.USER_ID 
            AND pp.PAYMENT_ID = ppd.PAYMENT_ID
            AND ppd.BILL_DTL_ID = pbd.BILL_DTL_ID
            AND pb.BILL_ID = pp.BILL_ID (+)
            AND pb.BILL_ID = pbd.BILL_ID (+)
            AND pp.payment_id = spp.payment_id (+)
            AND pp.PAYMENT_ID = ppo.PAYMENT_ID (+)
            AND spp.ATTEMPT = 3
            AND spp.record_status = 8
            AND pb.bill_type = 'S'
                AND pb.bill_no = ? 
                AND NOT EXISTS (SELECT 1
                    FROM
                        SM_SOFTCERT_REQUEST ssr
                    WHERE
                        ssr.SUPPLIER_ID = ss.SUPPLIER_ID
                        AND ssr.USER_ID = sp.USER_ID
                        AND ssr.PAYMENT_DTL_ID = ppd.PAYMENT_DTL_ID )
            ORDER BY
                pp.payment_date DESC ",array($applNo));

        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }

    public static function initiateTaskFlDeliveryOrder($deliveryOrder){
        MigrateUtils::logDump('DeliveryOrder Find: ' . $deliveryOrder);


        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $objDo = $bpmTaskServiceProgram->getFlDeliveryOrder($deliveryOrder);
        if($objDo === null){
            return "Invalid. Doc No is not as stuck task"; 
        }

        $payload = $bpmTaskServiceProgram->getXMLDeliveryOrder($deliveryOrder,$objDo->poco_no);
        $loginId = $bpmTaskServiceProgram->getDOSupplierList($objDo->poco_no)[0]->user_login_id;
        if($payload === null  || $loginId === null){
            return "Invalid. Could not get payload!";
        }


        //MigrateUtils::logDump($payload);
        //dd($loginId);

        /*** PLEASE CHECK CAREFULLY ON THIS ID TO CONFIRM USE THIS. ***/
        /*
        SELECT PROCESSID ,PROCESSNAME ,COMPOSITENAME ,REVISION ,
        label,APPLICATIONLINKDISPLAYNAME  FROM  BPM_CUBE_PROCESS bcp  WHERE 
        label  = 'Delivery Order List Initiation' 
        AND COMPOSITENAME  = 'Fulfilment' 
        AND REVISION  = '1.0.7.2'
        */
        $processId=296255;  //process initiate Start MOF Registration

        $res = $bpmTaskServiceProgram->initiateProcessAPI($processId,$loginId);
        //MigrateUtils::logDump($res);
        if($res && $res['status'] === "Success"){
            MigrateUtils::logDump(' Successfully initiate task');
            sleep(2);
            $offset = 0;
            $limit = 5;
            $assignment = "MY";
            sleep(2);
            $resList = $bpmTaskServiceProgram->findApiWorklist($loginId, "ASSIGNED", $assignment, $offset, $limit);
            //MigrateUtils::logDump($resList);
            MigrateUtils::logDump('LOGIN ID: '.$loginId.' for doc no. : '.$deliveryOrder);
            if($resList && $resList['status'] === "Success" && isset($resList['result']['worklistItem']) ){
                MigrateUtils::logDump(' Found task..');
                $listTask = collect($resList['result']['worklistItem']);
                if($listTask->count() === 0){
                    return "#### Need to check.. task already initiated. must do manual to update payload";
                }
                $label = 'Delivery Order List Initiation';
                $task = $listTask->where('process',$label)->first();
                 //MigrateUtils::logDump($task);
                if($task != null){
                    MigrateUtils::logDump('Found task ID: '.$task['taskId'].' process: '.$task['process'].' , InstanceID:'.$task['instanceId']);
                    $executeAction='SUBMIT';
                    $param = array(
                        0 => $deliveryOrder,
                        1 => '',
                        2 => '0',
                        3 => '0',
                    );

                    
                    $resultExec = $bpmTaskServiceProgram->updateExecuteActionAPI($task['taskId'], $loginId, $payload, $executeAction, $param);
                    MigrateUtils::logDump('STATUS EXECUTE TASK AS SUBMIT');
                    MigrateUtils::logDump($resultExec);
                    return "Completed";
                }else{
                    return "#### Need to check.. task already initiated. but not found. must do manual to update payload";
                }
               
                
            }else{
                return "#### Need to check.. task already initiated. must do manual to update payload";
            }

        }else{
            return "Could not initiate task by loginID : ".$loginId;
        }

    }

    protected  function getFlDeliveryOrder($docNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
                SELECT d.fulfilment_order_id,d.delivery_order_no,o.doc_no as poco_no FROM fl_delivery_order d , FL_FULFILMENT_ORDER  o
                WHERE 
                d.fulfilment_order_id = o.fulfilment_order_id 
                AND d.delivery_order_no = ?  AND d.record_status = 1 
                AND NOT EXISTS (SELECT * FROM fl_workflow_status WHERE doc_type = 'DO' AND doc_id = delivery_order_id) 
                ",
                array($docNo));

        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }
    
    protected  function getXMLDeliveryOrder($do_no,$poco_no,$prio=1) {
        
        $listSupplierDetail = $this->getDOSupplierList($poco_no);
        $listDeliveryOrderDetail = $this->getDeliveryOrderDetail($poco_no, $do_no);
        $listReceivingOfficerDetail = $this->getReceivingOfficerList($poco_no, $do_no, $prio);
        if(count( $listReceivingOfficerDetail) === 0){
            $listReceivingOfficerDetail = $this->getReceivingOfficerList($poco_no, $do_no, 2);
            MigrateUtils::logDump('Could not get exact receiving officer.. try get by priority 2 '.count( $listReceivingOfficerDetail));
        }
        
        $listDODetail = $this->getDODetail($poco_no);

        if( count($listSupplierDetail) === 0 || 
            count($listDeliveryOrderDetail) === 0 ||
            count($listReceivingOfficerDetail) === 0 ||
            count($listDODetail) === 0){
                return null;
        }

        $xmlSupplier = '';
        foreach ($listSupplierDetail as $supplierdata) {
            $designationSupplier = str_replace("&", "&amp;", $supplierdata->designation);
            $xmlDOFull = '<FL_Delivery_Order_List_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/DoFulfillment/FL_Delivery_Order_List_Data" xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/DoFulfillment/FL_DeliveryOrder_Data" xmlns:user="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">';

            $xmlReceivingOfficer = '';

            $xmlSupplier = $xmlSupplier . '
            <supplierList>
                <user:userId>' . $supplierdata->user_id . '</user:userId>
                <user:userLoginId>' . $supplierdata->user_login_id . '</user:userLoginId>
                <user:userName>' . $supplierdata->username . '</user:userName>
                <user:calendarName>PUTRAJAYA</user:calendarName>
                <user:designation>' . $designationSupplier . '</user:designation>
            </supplierList>';
        }
        foreach ($listDeliveryOrderDetail as $deliveryorderdata) {
            $date = $deliveryorderdata->supplier_do_date;
            $xmlDeliveryOrder = '
                <ns3:doId>' . $deliveryorderdata->do_id . '</ns3:doId>
                <ns3:doDocType ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
                <ns3:doDocNo>' . $deliveryorderdata->do_doc_no . '</ns3:doDocNo>
                <ns3:supplierDoNo>' . $deliveryorderdata->supplier_do_no . '</ns3:supplierDoNo>
                <ns3:supplierDoDate>' . $deliveryorderdata->supplier_do_date . 'T' . $deliveryorderdata->supplier_do_time . '+08:00' . '</ns3:supplierDoDate>';

            foreach ($listReceivingOfficerDetail as $receivingofficerdata) {
                $designation = str_replace("&", "&amp;", $receivingofficerdata->designation);
                $xmlReceivingOfficer = $xmlReceivingOfficer . '
                <ns3:receivingOfficerList>
                    <user:userId>' . $receivingofficerdata->user_id . '</user:userId>
                    <user:userLoginId>' . $receivingofficerdata->user_login_id . '</user:userLoginId>
                    <user:userName>' . $receivingofficerdata->username . '</user:userName>
                    <user:calendarName>PUTRAJAYA</user:calendarName>
                    <user:designation>' . $designation . '</user:designation>
                </ns3:receivingOfficerList>';
            }

            foreach ($listDODetail as $dodata) {
                $ordername = str_replace("&", "&amp;", $dodata->order_name);
                $xmlDO = '
                <ns3:status ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
                <ns3:orderId>' . $dodata->order_id . '</ns3:orderId>
                <ns3:requestId>' . $dodata->request_id . '</ns3:requestId>
                <ns3:orderName>' . $ordername . '</ns3:orderName>
                <ns3:orderDocNo>' . $dodata->order_doc_no . '</ns3:orderDocNo>
                <ns3:sapOrderNo>' . $dodata->sap_order_no . '</ns3:sapOrderNo>
                <ns3:orderType>' . $dodata->order_type . '</ns3:orderType>
                <ns3:supplier ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
                <ns3:isPhis>' . $dodata->isphis . '</ns3:isPhis>
                <ns3:businessArea>' . $dodata->business_area . '</ns3:businessArea>';
            }
        }

        $xml ="";
        $xml = $xmlDOFull . $xmlSupplier.'
            <deliveryOrderList>' .$xmlDeliveryOrder .$xmlReceivingOfficer .$xmlDO.'
                </deliveryOrderList>
        </FL_Delivery_Order_List_Data>';
        
        return $xml;
    }


    /**
     * Do not use this method unless you know what u doing. This will terminate all instances in BPM by selected year
     */
    public static function terminateBPMInstanceCreatedByYear($year) {
        MigrateUtils::logDump(__METHOD__.' entering... Year: '.$year);
        $listResult = self::getQueryComposite($year);

        MigrateUtils::logDump(__METHOD__.' result total  : '. $listResult->count());
       
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $counter = 0;
        foreach ($listResult as $data){
            $counter++;
            MigrateUtils::logDump(__METHOD__.' >> '.$counter.") >> createdDate=".$data->created_time.", componentInstanceID=".$data->id." ,compositeModule=".$data->composite_dn." ,state=".$data->state);
           
            $response = $bpmTaskServiceProgram->submitTerminateInstance($data->composite_dn, $data->id);
            MigrateUtils::logDump(__METHOD__.' >> '.json_encode($response));
            
        }
        

        
    }

    protected static function getQueryComposite($year) {
        MigrateUtils::logDump(__METHOD__.' entering... Year: '.$year);

        $sql = DB::connection('oracle_bpm_rpt')->table('COMPOSITE_INSTANCE')
                ->whereRaw("to_char(CREATED_TIME,'YYYY') = $year")
                ->whereNotIn('state',[16])
                ->select('id','COMPOSITE_DN' ,'CREATED_TIME','state');
        $result = $sql->get();
        return $result;
    }


     /**
     * terminate list instance ID
     */
    public static function terminateBPMInstanceList() {
        MigrateUtils::logDump(__METHOD__.' entering... ');
        $collectInstances = collect([
            collect(['composite_instance_id' => '71377343', 'composite_version' => 'default/Contract_Management!1.0.7.1' ]),
collect(['composite_instance_id' => '78646140', 'composite_version' => 'default/Contract_Management!1.0.8' ]),
collect(['composite_instance_id' => '63065358', 'composite_version' => 'default/Contract_Management!1.0.7.1' ]),
collect(['composite_instance_id' => '78646140', 'composite_version' => 'default/Contract_Management!1.0.8' ])
        ]);
        MigrateUtils::logDump(__METHOD__.' result total  : '. $collectInstances->count());
       
        $bpmTaskServiceProgram = new BPMTaskServiceProgram();
        $counter = 0;
        foreach ($collectInstances as $data){
            $counter++;
            MigrateUtils::logDump(__METHOD__.' >> '.$counter.")  componentInstanceID=".$data->get('composite_instance_id')." ,compositeModule=".$data->get('composite_version'));
           
            $response = $bpmTaskServiceProgram->submitTerminateInstance($data->get('composite_version'), $data->get('composite_instance_id'));
            MigrateUtils::logDump(__METHOD__.' >> '.json_encode($response));
            //dd('done');
        }
        

        
    }


    private static function getContractInfo($contractNo,$loaNo) { 
        $contractNoWhere = '';
        if($contractNo != null && strlen($contractNo) > 0){
            $contractNoWhere = " AND c.CONTRACT_NO = '$contractNo' ";
        }

        $loaNoWhere = '';
        if($loaNo != null && strlen($loaNo) > 0){
           
            $ctValueObj = DB::connection('oracle_nextgen_rpt')->table('CT_CONTRACT_VALUE')
                ->where('LOA_NO',$loaNo)->first();
            if($ctValueObj != null){
                // if found replace get CT NO.
                $ctObj = DB::connection('oracle_nextgen_rpt')->table('CT_CONTRACT')
                ->where('CURRENT_CONTRACT_VER_ID',$ctValueObj->contract_ver_id)
                ->orWhere('LATEST_CONTRACT_VER_ID',$ctValueObj->contract_ver_id)->first();
                if($ctObj != null){
                    $contractNoWhere = " AND c.CONTRACT_NO = '$ctObj->contract_no' ";
                }
            }
            
            if(strlen($contractNoWhere) == 0 ){
                $loaObj = DB::connection('mysql_cms')->table('cdccms.ep_contract')
                            ->where('ct_loa_no',$loaNo)->first();
                if($loaObj != null){
                    $contractNoWhere = " AND c.CONTRACT_NO = '$loaObj->ct_contract_no' ";
                }else{
                    $loaNoWhere = " AND v.LOA_NO = '$loaNo' ";
                }
            }
        }

        if(strlen($contractNoWhere) == 0 && strlen($loaNoWhere) == 0){
            MigrateUtils::logDump(__METHOD__." (issue not found root data .. contractNo : $contractNo , loaNo : $loaNo");
            return null;
        }
        
        $query = "SELECT  DECODE(CONTRACT_STATUS_ID, 1, 'EFFECTIVE', 2, 'INACTIVE', 3, 'EXPIRED', 4, 'TERMINATED', 'NO_STATUS') AS CONTRACT_STATUS ,
        tmp.* 
        FROM (
            SELECT 
            CASE
                WHEN (c.record_status = 1
                      AND v.eff_date <= trunc(sysdate)) THEN 1
                WHEN (c.record_status = 1
                      AND v.eff_date > trunc(sysdate)) THEN 2
                WHEN c.record_status = 3 THEN 3
                WHEN c.record_status = 0 THEN 4
                ELSE NULL
            END  AS contract_status_id,
            ( select STATUS_NAME from 
                    ct_agreement ag , ct_workflow_status ag2 , pm_status_desc ag3 
                    where ag.AGREEMENT_ID = ag2.DOC_ID and ag2.STATUS_ID = ag3.STATUS_ID
                        and ag3.LANGUAGE_CODE in ('en') and ag2.IS_CURRENT = 1 and ag2.DOC_TYPE in ('AC', 'SA') 
                        and ag.agreement_id in (select max(agreement_id) from ct_agreement agr 
                            where agr.contract_id = ag.contract_id and ag.CONTRACT_ID = c.CONTRACT_ID)  
            )  ct_ag_status,
            ( select ag2.DOC_TYPE from 
                ct_agreement ag , ct_workflow_status ag2 , pm_status_desc ag3 
                where ag.AGREEMENT_ID = ag2.DOC_ID and ag2.STATUS_ID = ag3.STATUS_ID
                and ag3.LANGUAGE_CODE in ('en') and ag2.IS_CURRENT = 1 and
                 ag2.DOC_TYPE in ('AC', 'SA') 
                and ag.agreement_id in (select max(agreement_id) from ct_agreement agr where agr.contract_id = ag.contract_id and ag.CONTRACT_ID = c.CONTRACT_ID)  
                )  ct_ag_type,
            c.FULFILMENT_TYPE_ID,
                                ( select code_name
                                    from PM_PARAMETER_DESC where PARAMETER_ID = c.FULFILMENT_TYPE_ID
                                    and LANGUAGE_CODE = 'en' ) as fulfilment_type,
            c.owner_org_profile_id,
            c.CONTRACT_ID ,c.CONTRACT_NO ,c.LATEST_CONTRACT_VER_ID ,c.CURRENT_CONTRACT_VER_ID ,                                      
            v.EXP_DATE , m.IS_CONTRACT_EXTEND ,m.EXTEND_PERIOD ,m.EXTEND_END_DATE  
            FROM CT_CONTRACT c, CT_CONTRACT_VALUE v , CT_AMENDMENT m 
            WHERE c.CURRENT_CONTRACT_VER_ID  = v.CONTRACT_VER_ID 
            AND c.CURRENT_CONTRACT_VER_ID = m.CONTRACT_VER_ID (+)
            $contractNoWhere 
            $loaNoWhere  
        ) tmp";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);
        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }


    private static function getQuotationTenderInfo($qtNo,$loaNo) { 
        $qtNoWhere = '';
        if($qtNo != null && strlen($qtNo) > 1){
            $qtNoWhere = " AND q.qt_no = '$qtNo' ";
        }

        $loaNoWhere = '';
        if($loaNo != null && strlen($loaNo) > 1){
            $loaNoWhere = " AND la.loa_no = '$loaNo' ";
        }

        $query = "SELECT 
        q.qt_no,q.ORG_PROFILE_ID ,q.PUBLISH_DATE,q.CLOSING_DATE , q.PROPOSAL_VALIDITY_END_DATE  ,q.IS_BSV_REQ ,la.loa_no,
        b.qt_bsv_id, 
        bd.bsv_type,max(bd.bsv_date) AS bsv_date, count(bd.qt_bsv_id) AS total_bsv
        FROM sc_qt q, sc_qt_bsv b, sc_qt_bsv_dtl bd, sc_loi_loa li, sc_loa la 
        WHERE q.qt_id = b.qt_id (+)
        AND b.qt_bsv_id = bd.qt_bsv_id (+)
        AND q.qt_id = li.doc_id (+)
        AND li.loi_loa_id = la.loi_loa_id (+)
        $qtNoWhere $loaNoWhere 
        GROUP BY q.qt_no,q.ORG_PROFILE_ID ,q.PUBLISH_DATE,q.CLOSING_DATE , q.PROPOSAL_VALIDITY_END_DATE  ,q.IS_BSV_REQ ,la.loa_no,
        b.qt_bsv_id, 
        bd.bsv_type 
        ORDER BY bsv_date desc ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }

    private static function getContractInfoCms($contractNo,$loaNo) { 
        $whereClause = "";
        $docno = '';
        if($contractNo != null){
            $docno  = $contractNo;
            $whereClause = "AND c.ct_contract_no = ? "; 
        }elseif($loaNo != null){
            $docno  = $loaNo;
            $whereClause = "AND c.ct_loa_no = ? "; 
        }
       
        $query = "SELECT c.ct_current_status,  c.ct_current_status_doc_type,c.ct_contract_amount,c.ct_latest_contract_ver_id,c.ct_current_contract_ver_id,
        c.ct_contract_no,c.ct_procurement_mode,c.ct_fulfilment_type,c.ct_procurement_type_cat,
        c.ct_is_agreement,c.ct_fl_details_status,c.ct_agreement_status,c.ct_doc_contract_status,
        p.ptj_profile_id,p.ptj_name,p.ptj_code,p.kementerian_name,p.kementerian_code ,p.ptj_state_name  
        FROM cdccms.`ep_contract` c , cdccms.`ep_org_profile_ptj` p 
        WHERE c.ct_created_ptj_id = p.ptj_profile_id 
        $whereClause ";
        $results = DB::connection('mysql_cms')->select($query,array($docno));

        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }

    private static function getProfilePtj($profileId) { 

        $query = "SELECT * FROM cdccms.`ep_org_profile_ptj` p 
        WHERE p.ptj_profile_id  = ? ";
        $results = DB::connection('mysql_cms')->select($query,array($profileId));

        if(count($results) > 0){
            return $results[0];
        }
        return null;
    }

    private static function getContractDocACWorkFlowInfoUser($contractNo,$loaNo = null,$statusId,$docType){
        $whereContractNo = '';
        $whereLoaNo = '';
      
        if($contractNo != null){
            $whereContractNo = " and cc.contract_no = '$contractNo'  ";
        }
        if($loaNo != null){
            $whereLoaNo = " and sl.loa_no = '$loaNo'  ";
        }
        
        $query = "SELECT u.LOGIN_ID ,
                    (SELECT ur.ROLE_CODE  FROM pm_user u1, pm_user_org uo, pm_user_role ur 
                    WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                    AND ur.ROLE_CODE = 'MIN_CONTRACT_ADMIN' 
                    AND u1.RECORD_STATUS  = 1
                    AND uo.RECORD_STATUS  = 1
                    AND ur.RECORD_STATUS  = 1 
                    AND u1.USER_ID = u.USER_ID) AS is_contract_admin,
                    (SELECT u1.login_id   FROM pm_user u1, pm_user_org uo, pm_user_role ur , PM_LOGIN_HISTORY lh
                    WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                    AND u1.USER_ID = lh.USER_ID 
                    AND ur.ROLE_CODE = 'MIN_CONTRACT_ADMIN' 
                    AND u1.RECORD_STATUS  = 1
                    AND uo.RECORD_STATUS  = 1
                    AND ur.RECORD_STATUS  = 1    
                    AND uo.org_profile_id = cc.owner_org_profile_id 
                    AND rownum < 2) AS active_contract_admin,
                TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,a.CONTRACT_ID,
                cc.contract_no, b.doc_type, 
                a.AGREEMENT_ID, b.doc_id, cc.loa_id, sl.loa_no, cc.owner_org_profile_id,
                d.status_name ,c.STATUS_ID,b.is_current , a.CREATED_BY , a.CHANGED_BY
                    from ct_contract cc,ct_agreement a, ct_workflow_status b, pm_status c, pm_status_desc d, sc_loa sl, pm_user u
                    where a.AGREEMENT_ID = b.doc_id
                    and b.status_id = c.status_id    
                    and c.status_id = d.status_id
                    and cc.contract_id = a.contract_id
                    and sl.loa_id = cc.loa_id
                    AND b.CREATED_BY = u.user_id
                    and d.language_code ='en'
                    AND b.STATUS_ID = '$statusId'
                    and b.doc_type = '$docType'
                    $whereContractNo $whereLoaNo
                ORDER BY  a.contract_id, b.CREATED_DATE DESC";
          
        $res = DB::connection('oracle_nextgen_rpt')->select($query);
   
        if(count($res) > 0){
            return  $res[0];
        }
        return null;
    }

    private static function getContractDocFDWorkFlowInfoUser($contractNo,$loaNo = null){
        $whereContractNo = '';
        $whereLoaNo = '';
        if($contractNo != null){
            $whereContractNo = " and cc.contract_no = '$contractNo'  ";
        }
        if($loaNo != null){
            $whereLoaNo = " and sl.loa_no = '$loaNo'  ";
        }
        
        $query = "SELECT u.LOGIN_ID ,
                    (SELECT ur.ROLE_CODE  FROM pm_user u1, pm_user_org uo, pm_user_role ur 
                    WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                    AND ur.ROLE_CODE = 'MIN_CONTRACT_ADMIN' 
                    AND u1.RECORD_STATUS  = 1
                    AND uo.RECORD_STATUS  = 1
                    AND ur.RECORD_STATUS  = 1 
                    AND u1.USER_ID = u.USER_ID) AS is_contract_admin,
                    (SELECT u1.login_id   FROM pm_user u1, pm_user_org uo, pm_user_role ur , PM_LOGIN_HISTORY lh
                    WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                    AND u1.USER_ID = lh.USER_ID 
                    AND ur.ROLE_CODE = 'MIN_CONTRACT_ADMIN' 
                    AND u1.RECORD_STATUS  = 1
                    AND uo.RECORD_STATUS  = 1
                    AND ur.RECORD_STATUS  = 1    
                    AND uo.org_profile_id = cc.owner_org_profile_id 
                    AND rownum < 2) AS active_contract_admin,
                TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,cc.CONTRACT_ID,
                cc.contract_no, b.doc_type, cc.current_contract_ver_id, b.doc_id, cc.loa_id, sl.loa_no, cc.owner_org_profile_id,
                d.status_name ,c.STATUS_ID,b.is_current 
                    from ct_contract cc, ct_workflow_status b, pm_status c, pm_status_desc d, sc_loa sl, pm_user u
                    where  
                    b.doc_id = cc.current_contract_ver_id  
                    AND b.status_id = c.status_id    
                    AND c.status_id = d.status_id
                    AND sl.loa_id = cc.loa_id
                    AND b.CREATED_BY = u.user_id
                    AND d.language_code ='en'
                    AND b.doc_type = 'FD' 
                    AND b.STATUS_ID = '50050' 
                    $whereContractNo $whereLoaNo
                ORDER BY  cc.contract_id, b.CREATED_DATE DESC";
        $res = DB::connection('oracle_nextgen_rpt')->select($query);
        if(count( $res) > 0){
            return  $res[0];
        }
        return null;
    }

    private static function updateToResetCTVersion($ctNo) { 


        $ctObj = DB::connection('oracle_nextgen_fullgrant')->table('CT_CONTRACT')
                ->where('contract_no',$ctNo)->first();
        
        if($ctObj != null && strlen($ctObj->latest_contract_ver_id) > 0){
            DB::connection('oracle_nextgen_fullgrant')->table('CT_CONTRACT')
            ->where('contract_no',$ctNo)->update(
                [
                    'current_contract_ver_id' => $ctObj->latest_contract_ver_id, 
                    'changed_date' => Carbon::now(),
                    'changed_by' => 1
                ]);
            $ctObjNew = DB::connection('oracle_nextgen_fullgrant')->table('CT_CONTRACT')
                ->where('contract_no',$ctNo)->select('contract_id','contract_no','latest_contract_ver_id','current_contract_ver_id','changed_date','changed_by')->first();

            MigrateUtils::logDump(__METHOD__.' done updated ' . json_encode($ctObjNew));

            return $ctObjNew;
        }else{
            MigrateUtils::logDump(__METHOD__." Contract not found!!! $ctNo " );
        }
        return $ctObj;
    }

    private static function updateRefireCompleted($instanceId,$taskDocNo,$newInstanceId = null) { 


        DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
        ->where('composite_instance_id',$instanceId)
        ->where('doc_number',$taskDocNo)
        ->update(
            [
                'act_is_refire' =>   1,
                'refire_instance_id' => $newInstanceId,
                'changed_at' => Carbon::now()
            ]
        );
        
    }

    private static function updateTerminateSyncVerCompleted($instanceId,$taskDocNo) { 


        DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
        ->where('composite_instance_id',$instanceId)
        ->where('doc_number',$taskDocNo)
        ->update(
            [
                'is_update_ver' =>   1,
                'act_is_update_ver'=>   1,
                'changed_at' => Carbon::now()
            ]
        );
        
    }


    

    /**
     * Get list all instances CT to do terminatation task, fix patch data CT, refire CT
     */
    public static function updatePreparationContractTechRefresh( ){
        MigrateUtils::logDump(__METHOD__ ." entering.." );
        $listCt = DB::connection('mysql_ep_support')
                            ->select("SELECT `task_state`,
                            YEAR(ct_exp_date) AS year_expired,
                            `doc_number`,
                            `doc_type`,
                            `doc_status_id`,
                            `doc_status`,
                            `ct_status`,
                            `ct_fl_details_status`,
                            `ct_agreement_status`,
                            `ct_doc_contract_status`,
                            `ct_no`,
                            `ct_exp_date`,
                            `doc_tracking_status_id`,
                            `doc_tracking_status`,
                            `doc_tracking_last_updated`,
                            `doc_tracking_type`,
                            `composite_instance_id`,
                            `composite_created`,
                            `composite_version`,
                            `composite_state`,
                            `ct_contract_amount`,
                            `ct_current_status`,
                            `ct_current_status_doc_type`,
                            `ct_fulfilment_type`,
                            `ct_latest_contract_ver_id`,
                            `ct_current_contract_ver_id`,
                            `ct_ag_status`,
                            `ct_ag_type` FROM ep_bpm_support.bpm_instance_old_ver 
                            WHERE 
                            doc_number is not null 
                            AND composite_version in  (
                                'default/Contract_Management!1.0.0',
                                'default/Contract_Management!1.0.1',
                                'default/Contract_Management!1.0.2',
                                'default/Contract_Management!1.0.3',
                                'default/Contract_Management!1.0.4',
                                'default/Contract_Management!1.0.5',
                                'default/Contract_Management!1.0.6',
                                'default/Contract_Management!1.0.7',
                                'default/Contract_Management!1.0.7.1',
                                'default/Contract_Management!1.0.8'
                            )
                            AND ct_status IS  NOT NULL  
                            ");
        MigrateUtils::logDump(__METHOD__ ." total ". count($listCt));
        foreach($listCt  as $obj){
            
            /* 
                RULES : 
                Refire : Contract must be Active, InActive, Expired (2022)
                Terminate Instance : Contract old version BPM before 1/10 creation
                Update Ver : Contract terminate will update ver if version is not same.
            */
            $dataUpd = array();
            $isUpdate = false;
            /** CHECKING EXPIRED  */
            if($obj->ct_status == 'TERMINATED'){
                $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                $dataUpd['action_remark'] = "T01: CT STATUS AS TERMINATED AND EXPIRED YEAR ON $obj->year_expired";
                $isUpdate = true;
            }
            if($obj->ct_status == 'EXPIRED'){
                if($obj->year_expired < 2022){
                    $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                    $dataUpd['action_remark'] = "E01: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired";
                    $isUpdate = true;
                }else{

                    // Most of tasks is pending Agreement. CT alreadt expired. Agreement is not valid to continue task.
                    if($obj->ct_fl_details_status == 'Complete' && ( $obj->ct_agreement_status == 'Complete' || $obj->ct_agreement_status == 'NA' )){
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "E02: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        
                        $isUpdate = true;
                    }

                    if($obj->ct_fl_details_status == 'Incomplete' && $obj->ct_agreement_status == 'Incomplete' ){
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "E03: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        
                        $isUpdate = true;
                    }
                    
                    /** IF REFIRE TASK. DO NOT UPDATE VER */
                    // Most of tasks is pending Agreement. CT alreadt expired. Agreement is not valid to continue task.
                    if($obj->ct_fl_details_status == 'Complete' && $obj->ct_agreement_status == 'Incomplete'){
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "E04: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        
                        $isUpdate = true;
                    }

                    // Task on CT Factoring , Bank Information (CB)
                    if($obj->doc_type == 'XF' || $obj->doc_type == 'RF' || $obj->doc_type == 'UF' || $obj->doc_type == 'CB' ){
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "E05: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired. PENDING $obj->doc_type, Status: $obj->doc_status ";
                        $isUpdate = true;
                    }

                    // Task on CT EXPIRED but still on creation or with agreement or Supplemental Agreement
                    if($obj->doc_type == 'AC' || $obj->doc_type == 'FD'  || $obj->doc_type == 'SA' ){
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "E06: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        $isUpdate = true;
                    }

                    // For DocType SA, IF Bond completed.. check expired data from Sept until Dec. We set as Refire Task
                    if( $obj->doc_type == 'SA' && $obj->ct_fl_details_status == 'Complete'){
                        $expDateCt = Carbon::parse($obj->ct_exp_date);
                        if($expDateCt->month >= 9){
                            $dataUpd['action_step'] = "TERMINATE TASK, REFIRE TASK $obj->doc_type";
                            $dataUpd['action_remark'] = "E07: CT STATUS AS EXPIRED AND EXPIRED YEAR ON $obj->year_expired , MONTH EXPIRED SEPT AND ONWARDS, PENDING $obj->doc_type, Status: $obj->doc_status. Checking ct_fl_details_status : $obj->ct_fl_details_status ";
                            $isUpdate = true;
                        }
                        
                    }

                    // Task on CT EXPIRED but still on creation or with agreement or Supplemental Agreement
                    if($obj->doc_type == 'FA' ){
                        if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                            $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                            $dataUpd['action_remark'] = "E08: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                            $isUpdate = true;
                        }else{
                            $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                            $dataUpd['action_remark'] = "E09: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                        }
                    }

                    // Task on CT EXPIRED but pending Amendment CT 
                    $arrStatusPendCT = array(
                        'Pending Verification Acknowledgement of Supplemental Contract Letter',
                        'Pending Amendment Without Supplemental Revision',
                        'Pending Supplemental Contract Letter Submission',
                        'Pending Meeting Confirmation',
                        'Pending Supplemental Contract Letter Acknowledgement',
                        'Pending Amendment Without Supplemental Submission',
                        'Pending Decision by the Approving Authority at the Ministry / Jabatan Level',
                        'Pending Amendment Without Supplemental Approval',
                        'Pending Decision by the Approving Authority at the Ministry of Finance Level',
                        'Pending Amendment With Supplemental Approval',
                        'Pending Chairperson Sign Off',
                        'Pending Amendment With Supplemental Submission',
                        'Pending Amendment With Supplemental Revision'
                    );
                    if(in_array($obj->doc_status, $arrStatusPendCT )){
                        if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                            $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                            $dataUpd['action_remark'] = "E10: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                            $isUpdate = true;
                        }else{
                            $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                            $dataUpd['action_remark'] = "E11: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                            $isUpdate = true;
                        }
                    }
                    
                    
                }
            }

            if($obj->ct_status == 'INACTIVE'){

                // Task on CT on creation or with agreement
                if($obj->doc_type == 'AC' || $obj->doc_type == 'FD' ){
                    $dataUpd['action_step'] = "TERMINATE TASK, REFIRE TASK $obj->doc_type";
                    $dataUpd['action_remark'] = "I01: CT EXPIRED ON $obj->year_expired , CT_TYPE as $obj->doc_type  and ct_doc_status as $obj->doc_status";
                    $isUpdate = true;
                }else{
                    MigrateUtils::logDump(__METHOD__ ." Need to check. Not include in scenario matrix INACTIVE CT >.   .. ".json_encode($obj));
                }
            }

            if($obj->ct_status == 'EFFECTIVE'){

                // Most of tasks is pending Agreement. CT alreadt expired. Agreement is not valid to continue task.
                if($obj->ct_fl_details_status == 'Complete' && ( $obj->ct_agreement_status == 'Complete' || $obj->ct_agreement_status == 'NA' ) ){
                    if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                        $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                        $dataUpd['action_remark'] = "F01: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        $isUpdate = true;
                    }else{
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "F02: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                        $isUpdate = true;
                    }
                }

                if($obj->ct_fl_details_status == 'Incomplete' && $obj->ct_agreement_status == 'Incomplete' ){
                    if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                        $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                        $dataUpd['action_remark'] = "F03: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        $isUpdate = true;
                    }else{
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "F04: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                        $isUpdate = true;
                    }
                }

                if($obj->ct_fl_details_status == 'Complete' && $obj->ct_agreement_status == 'Incomplete'){
                    $arrDocTypeCT = array(
                        'FD',
                        'SA',
                        'AC'
                    );
                    if(in_array($obj->doc_type, $arrDocTypeCT) === false){
                        if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                            $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                            $dataUpd['action_remark'] = "F05: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                            $isUpdate = true;
                        }else{
                            $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                            $dataUpd['action_remark'] = "F06: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                            $isUpdate = true;
                        }
                    }
                }

                // Task on CT Factoring 
                if($obj->doc_type == 'XF' || $obj->doc_type == 'RF' || $obj->doc_type == 'UF' ){
                    $dataUpd['action_step'] = "TERMINATE TASK, REFIRE TASK $obj->doc_type";
                    $dataUpd['action_remark'] = "F07: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired. PENDING: $obj->doc_type, Status: $obj->doc_status ";
                    $isUpdate = true;
                }

                // Task on CT on creation or with agreement
                if($obj->doc_type == 'AC' || $obj->doc_type == 'FD' || $obj->doc_type == 'SA'  ){
                    $dataUpd['action_step'] = "TERMINATE TASK, REFIRE TASK $obj->doc_type";
                    $dataUpd['action_remark'] = "F08: CT EFFECTIVE UNTIL $obj->year_expired , PENDING: $obj->doc_type, Status: $obj->doc_status";
                    $isUpdate = true;
                }

                // Task on CT EXPIRED but pending Amendment CT 
                $arrStatusPendCT = array(
                    'Pending Verification Acknowledgement of Supplemental Contract Letter',
                    'Pending Amendment Without Supplemental Revision',
                    'Pending Supplemental Contract Letter Submission',
                    'Pending Meeting Confirmation',
                    'Pending Supplemental Contract Letter Acknowledgement',
                    'Pending Amendment Without Supplemental Submission',
                    'Pending Decision by the Approving Authority at the Ministry / Jabatan Level',
                    'Pending Amendment Without Supplemental Approval',
                    'Pending Decision by the Approving Authority at the Ministry of Finance Level',
                    'Pending Amendment With Supplemental Approval',
                    'Pending Chairperson Sign Off',
                    'Pending Amendment With Supplemental Submission',
                    'Pending Amendment With Supplemental Revision'
                );
                if(in_array($obj->doc_status, $arrStatusPendCT )){
                    if($obj->ct_latest_contract_ver_id != $obj->ct_current_contract_ver_id ){
                        $dataUpd['action_step'] = 'TERMINATE TASK & UPDATE VER';
                        $dataUpd['action_remark'] = "F09: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status ";
                        $isUpdate = true;
                    }else{
                        $dataUpd['action_step'] = 'TERMINATE TASK ONLY';
                        $dataUpd['action_remark'] = "F10: CT STATUS AS EFFECTIVE AND EXPIRED YEAR ON $obj->year_expired , PENDING $obj->doc_type, Status: $obj->doc_status,  ct_latest_contract_ver_id: $obj->ct_latest_contract_ver_id same with ct_current_contract_ver_id: $obj->ct_current_contract_ver_id";
                        $isUpdate = true;
                    }
                    
                }

            }

            if($isUpdate === true){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                ->where('composite_instance_id',$obj->composite_instance_id)
                ->where('doc_number',$obj->doc_number)
                ->update($dataUpd);
            }

        }
        MigrateUtils::logDump(__METHOD__ ." Completed .. ");
    }

}
