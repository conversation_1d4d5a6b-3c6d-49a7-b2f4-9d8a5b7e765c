@extends('layouts.guest-dash')

@section('content')

<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li  class="active  @if(!Auth::user()->isPatcherRolesEp()) hide @endif">
                <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li>
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                 <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                 <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div> 
@if(isset($successSave))
<h1>{{$successSave}}</h1>
@endif
<form class="form-horizontal form-bordered port-form-urgent" action="{{url('prod-support/urgent-patching')}}" method="post" >
    {{ csrf_field() }}
    <input name="_method" id="_method" type="hidden" value="POST">
    @if($getpatch)
    <input name="data_fix_porting_id" id="data_fix_porting_id" type="hidden" value="{{$getpatch->data_fix_id}}">
    @endif
    <div class="block block-alt-noborder full dateform-urgent">
        <div class="form-group" >
            <div class="col-md-4 date-port">
                <label class="col-md-4 control-label" for="date_porting">Datepicker</label>
                <div class="col-md-6">
                    <input readonly="" id="date_porting" name="date_porting" type="text" required class="form-control" value="@if($getpatch){{Carbon\Carbon::parse($getpatch->datetime_porting)->format('Y-m-d')}} @else {{Carbon\Carbon::now()->format('Y-m-d')}} @endif" />
                </div>
            </div>
            <div class="col-md-4 date-time-port">
                <label class="col-md-4 control-label" for="exampletimepicker">Time Porting</label>
                <div class="col-md-6">
                    <div class="input-group bootstrap-timepicker">

                        <input type="text" id="time_porting" name="time_porting" class="form-control input-timepicker24"
                               value = "@if($getpatch){{Carbon\Carbon::parse($getpatch->datetime_porting)->format('H:i')}}@else <?php
                               $date = date("H", strtotime("+1 hours")) . '00';
                               echo "$date";
                               ?> @endif">
                        <span class="input-group-btn">
                            <a href="javascript:void(0)" class="btn btn-primary"><i class="fa fa-clock-o"></i></a>
                        </span>
                    </div>
                </div>
            </div>
            <div class="col-md-4 date-port">
                <label class="col-md-3 text-right"> Urgent Type</label>
                <div class="col-md-4">
                    <input type="radio" name="radio_urgent_type" value="internal" @if($getpatch == null OR ($getpatch->urgent_type == 'internal') ) checked="checked" @endif> Internal
                           <input type="radio" name="radio_urgent_type" value="external" @if($getpatch && $getpatch->urgent_type == 'external') checked="checked" @endif> External
                </div>
                <span data-placement="top" tabindex="0" role="button"  data-trigger="focus" data-html="true"  data-toggle="popover"
                      data-content="
                      <table class='table table-borderless table-striped'>
                      <tr><th>Name</th><th>Description</th></tr>
                      <tr><td>Internal </td><td>Urgent Patch no need eP approval</td></tr>
                      <tr><td>External </td><td>Urgent Patch need eP approval</td></tr>
                      </table>"><i class="gi gi-circle_info"></i></span>
            </div>

        </div>
        @if(isset($getpatch))
        <div class="form-group">
            <div class="col-md-4 status-port" @if(!$getpatch) style = 'display:none' @endif>
                 <label class="col-md-4 control-label" for="statusport"><i class="fa fa-pencil-square"></i> Status : </label>

                <div class="col-md-6">
                    <input readonly="" type="text" id="statusport" name="statusport" class="form-control" value= "{{$getpatch->status}}">
                </div>

            </div>
            <div class="col-md-4 createdby" @if(!$getpatch) style = 'display:none' @endif>
                 <label class="col-md-4 control-label" for="createdport"><i class="fa fa-pencil-square"></i> Created by : </label>
                <div class="col-md-6">
                    <input readonly="" type="text" id="createdport" name="createdport" class="form-control" value = "{{$getpatch->created_by}}">
                </div>
            </div>
        </div>
        @endif
        <div class="form-group">
            @if(isset($getpatch))
            @if($getpatch->status == 'Open' || $getpatch->status == 'Submit')
            <button type="button" class="btn btn-sm right btn-danger cancel-port"><i class="fa fa-close"></i> CANCEL PORTING</button>
            @endif
            @if($getpatch->status == 'Open')
            <button type="submit" class="btn btn-sm right btn-primary" name="update_button"><i class="fa fa-save"></i> UPDATE PORTING</button>
            <button type="button" class="btn btn-sm btn-primary add-new-list-urgent"><i class="fa fa-save"></i> Add New List</button>
            @endif
            @if($getpatch->status == 'Submit')
            <button type="button" class="btn btn-sm btn-primary cancel-submit"><i class="fa fa-save"></i> CANCEL SUBMIT</button>
            @endif
            @endif
            @if(!$getpatch || $getpatch->status === 'Closed' || $getpatch->status === 'Cancelled')
            <button type="button" class="btn btn-sm right btn-primary add-new-seq"><i class="fa fa-save"></i> ADD NEW PORTING</button>
            @endif 
        </div>


        <div id="modal_confirm_newport-urgent" class="modal fade">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5> CONFIRMATION</h5>
                    </div>
                    <div class="modal-body text-center">
                        <label>Are you sure to create new porting? </label> &nbsp;&nbsp;&nbsp;
                    </div> 
                    <br/><br/>
                    <div class="modal-footer">
                        <button type="button" id="submit_confirm_newport-urgent" name="submit_confirm_newport-urgent" class="btn btn-sm btn-info pull-left">YES</button>
                        <button type="button" id="cancel_submit_newport-urgent" name="cancel_submit_newport-urgent"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                    </div>
                </div>
            </div>
        </div>
        <div id="modal_confirm_cancelport" class="modal fade">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5> CONFIRMATION</h5>
                    </div>
                    <div class="modal-body text-center">
                        <label>Are you sure to Cancel Porting? </label> &nbsp;&nbsp;&nbsp;
                    </div> 
                    <br/><br/>
                    <div class="modal-footer">
                        <button type="submit" id="submit_confirm_cancelport" name="submit_confirm_cancelport" class="btn btn-sm btn-info pull-left">YES</button>
                        <button type="button" id="cancel_submit_cancelport" name="cancel_submit_cancelport"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</form>

@if($getpatch != null || $closedPatch != null)
<div class="block panel-heading add-new-form" style="display: none">@include('prod_support.addnewform1')</div>

@foreach($listUrgentToday as $listdata)
<div class ="block block-alt-noborder full tablelist" @if(!$listUrgentToday) style = 'display:none' @endif>
     <div class="form-group" >
        @if($listdata != null)
        @if($listdata[0]->status == 'Open' && $status == 'Open' && $script == 'Completed')
        <a href="{{url('/prod-support/data-patching/download')}}/{{$listdata[0]->data_fix_id}}" 
           target="_blank" class="btn btn-sm btn-primary submitpatch"><i class="fa fa-download"></i> Download</a>


        <button type="submit" class="btn btn-sm btn-primary closeport" title="Set close patch today."><i class="fa fa-lock"></i> Close</button>
        @endif

        @if($listdata[0]->status == 'Closed' && $listdata[0]->is_sent != 1)
        <button type="button" class="btn btn-sm btn-primary emailtoapprover"><i class="fa fa-envelope"></i> Email To Approver</button>
        @endif

        <div class="table-responsive table-port">
            <h5><strong><font color="black">(ID:{{$listdata[0]->data_fix_id}}) {{$listdata[0]->name}}({{$listdata[0]->urgent_type}}) || {{$listdata[0]->status}} 
                    @if($listdata[0]->closed_by && strlen($listdata[0]->closed_by) > 0)
                    on {{$listdata[0]->changed_date}} by {{$listdata[0]->closed_by}}
                    @endif </font></strong></h5>
            <div class="col-md-3">
                <input id="datafixid" name="datafixid" type="hidden" value="{{$listdata[0]->data_fix_id}}" class="form-control" style="width: 700px;">
            </div>
            <table id="datalistport-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No.</th>
                        <th class="text-center">Date Porting</th>
                        @if($listdata[0]->closed_by)
                        <th class="text-center">ID</th>
                        @endif
                        <th class="text-center">CRM Number</th>
                        <th class="text-center">Redmine Number</th>
                        <th class="text-center">Module</th>
                        <th class="text-center">Problem Description</th>
                        <th class="text-center">Problem Type</th>
                        <th class="text-center">Group Type</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Endorsed By</th>
                        <th class="text-center">Endorsement Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Total Valid CR</th>
                        <th class="text-center">Total Valid Script</th>
                        <th class="text-center">Created</th>
                        <th class="text-center">Action</th>
                        @if($listdata[0]->closed_by)
                        <th class="text-center">HelpDesk</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @foreach($listdata as $data => $rowData)
                    <tr>
                        <td class="text-center">{{++$data}}</td>
                        <td class="text-center">{{$rowData->dateport}}</td>
                        @if($listdata[0]->closed_by && strlen($listdata[0]->closed_by) > 0)
                        <td class="text-center">{{$rowData->data_fix_dtl_id}}</td>
                        @endif
                        <td class="text-center">{{$rowData->crm_no}}</td>
                        <td class="text-center">{{$rowData->redmineno}}</td>
                        <td class="text-center">{{$rowData->module}}</td>
                        <td class="text-center">{{$rowData->prob_desc}}</td>
                        <td class="text-center">{{$rowData->probtype}}</td>
                        <td class="text-center">{{$rowData->requester}}</td>
                        <td class="text-center">{{$rowData->requester_name}}</td>
                        <td class="text-center">@if($rowData->endorsement_by == null)-@else{{$rowData->endorsement_by}}@endif</td>
                        <td class="text-center">@if($rowData->endorsement_date == null)-@else{{$rowData->endorsement_date}}@endif</td>
                        <td class="text-center">@if(($rowData->patchstatus) == 'Rejected'){{$rowData->patchstatus}} @else @if(($rowData->CRstatus) == null){{$rowData->patchstatus}} @elseif(($rowData->CRstatus) != null && ($rowData->scriptstatus) == null) {{$rowData->CRstatus}} @else{{$rowData->scriptstatus}} @endif @endif</td>
                        <td class="text-center">{{$rowData->total_valid_cr}}</td>
                        <td class="text-center">{{$rowData->total_valid_script}}</td>
                        <td class="text-center">{{$rowData->dtl_created_by}} ({{$rowData->dtl_created_date}})</td>
                        @if($rowData->status == 'Open')
                        <td class="text-center">
                            <div class="btn-group btn-group-xs">
                                <a href="/prod-support/edit-patch-urgent/{{ $rowData->data_fix_dtl_id }}" 
                                   data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                                @if($rowData->total_valid_script > 0 || $rowData->total_valid_cr > 0)<a href="/prod-support/history-data-patching/download/{{ $rowData->data_fix_dtl_id }}"data-toggle="tooltip" title="Download" style="alignment-adjust: middle" class="btn btn-xs btn-info"><i class="fa fa-download"></i></a>@endif
                                <a idnom ="{{$rowData->data_fix_dtl_id}}" 
                                   accesskey=""data-toggle="tooltip" title="Delete" class="btn btn-sm btn-danger delete_details"><i class="fa fa-times"></i></a>
                            </div>
                        </td>
                        @else <td></td> 
                        @endif

                        @if($listdata[0]->closed_by && strlen($listdata[0]->closed_by) > 0)
                        <td class="text-center">@if($rowData->sent_helpdesk===1) SENT @elseif($rowData->sent_helpdesk===0) FAILED @else NOT YET @endif</td>
                        @endif
                    </tr>
                    @endforeach
                </tbody>
            </table>
            <input type="hidden" id="delid" name="delid" value="" class="form-control" style="width: 100px;">
            <div id="modal_confirm_delete_details" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Delete Data Patch Details? </label> &nbsp;&nbsp;&nbsp;
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endforeach
@endif

<div id="modal_confirm_submit" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <label>Are you sure to Close Porting? </label> &nbsp;&nbsp;&nbsp;
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="button" id="submit_confirm_closedport" name="submit_confirm_closedport" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_closedport" name="cancel_submit_closedport"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>



<div id="modal_confirm_cancelsubmit" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <label>Are you sure to Cancel Submit Porting? </label> &nbsp;&nbsp;&nbsp;
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="submit" id="submit_confirm_cancelsubmit" name="submit_confirm_cancelsubmit" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_cancelsubmit" name="cancel_submit_cancelsubmit"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>

<div id="modal_confirm_email" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <h2>Are you sure to e-mail to approver? </h2> &nbsp;&nbsp;&nbsp;

                <!-- Select Components Block -->
                <div class="block text-left">

                    <div class="form-group" style="height:50px;">
                        <label class="col-md-4 control-label" for="emailApprovers">Send To</label>
                        <div class="col-md-6">
                            <select id="emailApprovers" name="emailApprovers" class="select-chosen" data-placeholder="Choose .." style="width: 100%;" multiple>
                                @foreach($usersManager as $rowData)
                                <option value="{{$rowData->data_fix_user_id}}">{{$rowData->fullname}} ({{$rowData->email}})</option>
                                @endforeach
                            </select>
                            @if($getEmailuser != null && $getEmailuser->urgent_type == 'External')
                                   @foreach($listGroupMo as $rowData)
                                {{$rowData->full_name}} ({{$rowData->email}})
                                @endforeach
                            @endif
                            <br/><br/>
                        </div>
                        <label class="col-md-4 control-label" for="emailApproversCC">CC To</label>
                        <div class="col-md-6">
                            @foreach($listGroupCc as $rowData)
                                        {{$rowData->full_name}} ({{$rowData->email}});
                                    @endforeach
                            
                            @if($getEmailuser != null && $getEmailuser->urgent_type == 'External')
                                    @foreach($listGroupePCc as $rowData)
                                        {{$rowData->full_name}} ({{$rowData->email}});<br/> 
                                    @endforeach
                            @endif
                            <br/><br/>
                        </div>
                    </div>
                    <br/><br/>

                </div>

            </div>
            <div class="modal-footer">
                <button type="button" id="submit_confirm_email" name="submit_confirm_email" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_email" name="cancel_submit_email"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>

<script>
    $(function () {
        ModalListActionLogDatatable.init();
        TablesDatatables.init();
    });
    App.datatables();
    $("#crmno").on("focusout", function () {
        console.log("try");
        $.ajax({
            url: APP_URL + '/prod-support/data-patching/crmnumber/' + $('#crmno').val(),
            dataType: 'json',
            type: "GET",
            success: function (data) {
                console.log('success');
                $data = $(data)
                console.log($data);
                $('#portmodule').val($data[0][0].id).trigger('change');
                $("#orgname").val($data[1].name);
                $("#grouptype").val($data[1].orgname);
            }
        })

    });
    $(".prob").on("focusout", function () {
        $.ajax({
            url: APP_URL + '/prod-support/data-patching/problemtype/' + $('#crmno1').val(),
            dataType: 'json',
            type: "GET",
            success: function (data) {
                console.log('success');
                if (data.id == '167') {
                    $('#endorse').show();
                }
            }
        })
    });
    $(".add-new-seq").on("click", function () {
        $(".add-new-seq").show();
        $('#modal_confirm_newport-urgent').modal('show');
    });

    $(".add-new-list-urgent").on("click", function () {
        $(".add-new-seq").hide();
        $(".add-new-form").show();
        $(".add-new-port-urgent").show();
        $(".add-new-list-urgent").hide();
        $("#patch-form-urgent").show();
//        $('#dateport').val("");
        $("#crmno").val("");
        $("#portmodule").val("");
        $("#prodesc").val("");
        $("#probtype").val("");
        $("#grouptype").val("");
        $("#endorse").val("");
//        var time = moment().format('YYYY-MM-DD HH:mm:ss');
//        $('#dateport').val(time);
    });

    $('.resetbutton').on('click', function () {
        $(".add-new-form").hide();
        $(".add-new-list-urgent").show();
    });

    $('#submit_confirm_newport-urgent').on('click', function () {
        $('#modal_confirm_newport-urgent').modal('hide');
        $.ajax({
            type: "POST",
            url: "/prod-support/urgent-patching/createport",
            data: $('.port-form-urgent').serialize(),
        }).done(function (resp) {
            console.log('okay');
            location.reload();
            if (resp.status_code === 'SUCCESS') {
                $('#successResolution').show();
                setTimeout(location.reload.bind(location), 1000);
            }
        });
    });


    var APP_URL = {!! json_encode(url('/')) !!}

    $('.type').on("change", '#portmodule', function () {
        $('#portmodule').val();
        console.log($('#portmodule').val());
        $('#date_endorse').val("");
        $module = $('#portmodule').val();
        $('#prodesc').empty();
        $.ajax({
            url: APP_URL + '/prod-support/data-patching/module/' + $module,
            dataType: 'json',
            type: "GET",
            success: function (data) {
                $data = $(data)
                console.log($data);

                $($data).each(function ()
                {
                    console.log(this.id);
                    console.log(this.name);
                    var option = $('<option />');
                    option.attr('value', this.id).text(this.name);

                    $('#prodesc').append(option);
                });
            }

        });

    });

    $("#probtype").change(function () {
        if ($(this).val() === "167") {
            $('#endorse').show();
            $('.endorse1').show();
            $('#date_endorse1').show();
            $('#endorse').attr('required', '');
            $('#endorse').attr('data-error', 'This field is required.');
            $('#date_endorse1').attr('required', '');
            $('#date_endorse1').attr('data-error', 'This field is required.');
        } else {
            $('#endorse').hide();
            $('.endorse1').hide();
            $('#date_endorse1').hide();
            $('#endorse').removeAttr('required');
            $('#endorse').removeAttr('data-error');
            $('#date_endorse1').removeAttr('required');
            $('#date_endorse1').removeAttr('data-error');
        }
    });
    
    $('#reset_date').on("click", function () {
        $('#date_endorse').val("");
    });
    $("#probtype").trigger("change");

    $(".cancel-port").on("click", function () {
        $(".cancel-port").show();
        $('#modal_confirm_cancelport').modal('show');
    });

    $('#redmineid').hide();
    $('.redmine').hide();
    $('#redmineid').removeAttr('required');
    $('#checkbox').click(function () {

        if ($('#checkbox').is(":checked")) {
            $('.redmine').show();
            $('#redmineid').show();
            $('#redmineid').Attr('required');
            $('#redmineid').attr('data-error', 'This field is required.');
        } else {
            $('#redmineid').hide();
            $('.redmine').hide();
            $('#redmineid').removeAttr('required');
        }
    });

    $('#checkboxcase').click(function () {

        if ($('#checkboxcase').is(":checked")) {
            $('#exampletags2').show();
            $('#exampletags').Attr('required');
            $('#exampletags').attr('data-error', 'This field is required.');
        } else {
            $('#exampletags2').hide();
            $('#exampletags').val("");
            $('#exampletags').removeAttr('required');
        }
    });

    $('.submitpatch').on('click', function () {
        $Id = $("#datafixid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching/download/" + $Id,
        });
    });

    $('.closeport').on('click', function () {
        $(".closeport").show();
        $('#modal_confirm_submit').modal('show');
    });
    $('#submit_confirm_closedport').on('click', function () {
        $('#modal_confirm_submit').modal('hide');
        $Id = $("#datafixid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching/closed/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            window.location.replace("/prod-support/urgent-patching");
            if (resp.status_code === 'SUCCESS') {
                $('#successResolution').show();
            }
        });
    });

    $(".cancel-submit").on("click", function () {
        $(".cancel-submit").show();
        $('#modal_confirm_cancelsubmit').modal('show');
    });

    $('#submit_confirm_cancelsubmit').on('click', function () {
        $('#modal_confirm_cancelsubmit').modal('hide');
        $Id = $("#datafixid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching-urgent/cancelsubmit/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
            if (resp.status_code === 'SUCCESS') {
                $('#successResolution').show();
            }
        });
    });

    $('.emailtoapprover').on('click', function () {
        $(".emailtoapprover").show();
        $('#modal_confirm_email').modal('show');
    });
    $('#submit_confirm_email').on('click', function () {
        $('#modal_confirm_email').modal('hide');
        var dataFixId = $("#datafixid").val();
        var emailApprovers = $("#emailApprovers").val();
        //console.log(dataFixId);
        //console.log(emailApprovers);
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching/emailtoapprover/?dataFixId=" + dataFixId + "&emailApprovers=" + emailApprovers,
        }).done(function (resp) {
            console.log(resp);
            if (resp.status_code === 'SUCCESS') {
                location.reload();
                $('#emailtoapprover').hide();
                $('#notificationDiv').show();
                $('#notificationDiv').addClass("alert-success");
                $('#notificationDiv').removeClass("alert-warning");
                $('#notificationDiv').append(
                        "<button type='button' class='close' data-dismiss='alert' aria-hidden='true'>×</button>"
                        + "<h4><i class='fa fa-check-circle'></i> " + resp.status_code + "</h4>  <a href='javascript:void(0)' class='alert-link'>" + resp.status_desc + "</a>"
                        );
            } else {
                $('#notificationDiv').show();
                $('#notificationDiv').addClass("alert-warning");
                $('#notificationDiv').removeClass("alert-success");
                $('#notificationDiv').append(
                        "<button type='button' class='close' data-dismiss='alert' aria-hidden='true'>×</button>"
                        + "<h4><i class='fa fa-times-circle'></i> " + resp.status_code + "</h4>  <a href='javascript:void(0)' class='alert-link'>" + resp.status_desc + "</a>"
                        );
            }
        });

    });

    $("#endorse").on("click", function () {
        editButtonChanged(this);
    });

    $("#prodesc").on("change",function () {
        editButtonChanged(this);
    });

    function editButtonChanged(a) {
        $idModule = $('#prodesc').val()
        $idEndorse = $('#endorse').val()
        if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.3 (1)") {
            $('#date_endorse').val("2023-11-03");
        } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.4 (64)") {
            $('#date_endorse').val("2025-04-07");
        } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S).600-22/10/9 JLD 21 (21)") {
            $('#date_endorse').val("2024-07-03");
        } else if($idEndorse == "Tuan Haji Khairuddin Bin Bakar - TSK (S)" && $idModule == 213 ) {
            $('#date_endorse').val("2022-12-02");
        } else if($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 213 ) {
            $('#date_endorse').val("2022-05-11");
        } else if($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 206 ) {
            $('#date_endorse').val("2022-03-22");
        } else if($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 207 ) {
            $('#date_endorse').val("2022-03-22");
        } else if($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 226 ) {
            $('#date_endorse').val("2023-03-02");
        } else if($idEndorse == "Kelulusan Pukal MOF.BPK.600-22/10/17 JLD 5 (42)") {
            $('#date_endorse').val("2024-11-29");
        } else if($idEndorse == "Kelulusan Pukal MOF.BPK(S)600-22/10/13 JLD.4 (65)") {
            $('#date_endorse').val("2025-05-30");
        }
        else {
            $('#date_endorse').val("");
        }
    }

    $(".delete_details").on("click", function () {
        $("#modal_confirm_delete_details").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
        console.log(delid);
    });

    $('#submit_confirm_delete').on('click', function () {
        $('#modal_confirm_delete_details').modal('hide');
        $Id = $("#delid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patch/remove/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });
</script>  
@endsection



