{"name": "laravel/laravel", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "type": "project", "require": {"php": ">=5.6.4", "barryvdh/laravel-dompdf": "0.8.2", "chumper/zipper": "^1.0", "kozz/laravel-guzzle-provider": "~6.0", "laravel/framework": "5.4.*", "laravel/tinker": "~1.0", "laravelcollective/remote": "~5.0", "maatwebsite/excel": "~2.1.0", "phpoffice/phpword": "^0.17.0", "symfony/psr-http-message-bridge": "^1.0", "webpatser/laravel-uuid": "2.*", "yajra/laravel-oci8": "5.4.*", "zendframework/zend-diactoros": "^1.8"}, "require-dev": {"fzaninotto/faker": "~1.4", "mockery/mockery": "0.9.*", "phpunit/phpunit": "~5.7"}, "autoload": {"classmap": ["database"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-root-package-install": ["php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["php artisan key:generate"], "post-install-cmd": ["Illuminate\\Foundation\\ComposerScripts::postInstall", "php artisan optimize"], "post-update-cmd": ["Illuminate\\Foundation\\ComposerScripts::postUpdate", "php artisan optimize"]}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true, "allow-plugins": {"kylekatarnls/update-helper": true}}}