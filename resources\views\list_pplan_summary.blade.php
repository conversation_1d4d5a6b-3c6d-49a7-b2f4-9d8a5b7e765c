@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="cariandpform" action="{{ url('/find/pplan') }}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{ $carian }}" class="form-control"
                onfocus="this.select();" placeholder="Klik carian di sini (Perancangan Perolehan Sahaja)... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if ($carian == null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{ $carian }} {{ $carian }}</strong>
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>Tidak dijumpai!</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if (!empty($getPpInfo) && $getPpInfo !== null)
        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">

                <div class="row align-items-center">

                    <div class="col-md-5 d-flex align-items-center">
                        <h1><i class="fa fa-building-o"></i></h1>
                        <h1>
                            <strong> Doc Number :
                                <font color="yellow">{{ $docNo }}</font>
                                ({{ $getPpInfo[0]->plan_id }})
                            </strong>
                        </h1>
                    </div>

                    <div class="col-md-5 text-end">
                        <h1>
                            <strong>Plan Type :
                                <font color="yellow">{{ $getPpInfo[0]->type_pp }}</font>
                            </strong>
                        </h1>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-5">
                    <address>
                        <strong>Prepared For PTJ</strong> : <font><a class="modal-list-data-action"
                                href="{{ url('/find/orgcode/') }}/{{ $getPpInfo[0]->org_code }}" target='_blank'>
                                ({{ $getPpInfo[0]->org_code }})</a>
                        </font>
                        <font color="black">{{ $getPpInfo[0]->org_name }}</font><br />
                        <strong>Kumpulan PTJ</strong> : <font><a class="modal-list-data-action"
                                href="{{ url('/find/orgcode/') }}/{{ $getPpInfo[0]->kptjcode }}" target='_blank'>
                                ({{ $getPpInfo[0]->kptjcode }})</a>
                            <font color="black">
                                {{ $getPpInfo[0]->kptjname }}</font><br />
                            <strong>Pegawai Pengawal</strong> : <font><a class="modal-list-data-action"
                                    href="{{ url('/find/orgcode/') }}/{{ $getPpInfo[0]->pptjcode }}" target='_blank'>
                                    ({{ $getPpInfo[0]->pptjcode }})</a>
                                <font color="black">{{ $getPpInfo[0]->pptjname }}</font>
                                <br />
                                <strong>Kementerian</strong> : <font><a class="modal-list-data-action"
                                        href="{{ url('/find/orgcode/') }}/{{ $getPpInfo[0]->mptjcode }}" target='_blank'>
                                        ({{ $getPpInfo[0]->mptjcode }})</a>
                                    <font color="black">{{ $getPpInfo[0]->mptjname }}</font>
                                    <br />
                    </address>
                </div>

            </div>
        </div>
        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">
                <h1><strong> PLAN USER </strong></h1>
            </div>

            <div class="row">
                <div class="col-md-4">
                <h6><strong> PLAN ACTOR </strong></h6>
                <div class="table-responsive">
                    <table id="actor_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Login Id</th>
                                <th class="text-center">User Name</th>
                                <th class="text-center">User Role</th>
                                <th class="text-center">Changed Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($getPpActor as $list)
                                <tr>
                                    <td class="text-left">{{ $list->login_id }}</td>
                                    <td class="text-left">({{ $list->user_id }}) {{ $list->user_name }}</td>
                                    <td class="text-left">{{ $list->role_code }}</td>
                                    <td class="text-left">{{ $list->action_date }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            @if (!empty($getPpInfo) && $getPpInfo !== null && $getPpInfo[0]->type_pp !== 'Perbelanjaan')
            <div class="col-md-4">
                <h6><strong> PLAN VERIFICATION OFFICER (Selected) </strong></h6>
                <div class="table-responsive">
                    <table id="select_veri_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Login Id</th>
                                <th class="text-center">User Name</th>
                                <th class="text-center">User Role</th>
                                <th class="text-center">Record Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($getVeriGroup as $list)
                                <tr>
                                    <td class="text-left">{{ $list->login_id }}</td>
                                    <td class="text-left">({{ $list->user_id }}) {{ $list->user_name }}</td>
                                    <td class="text-left">{{ $list->role_code }}</td>
                                    <td class="text-left">{{ $list->record_status }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif

            <div class="col-md-4">
                <h6><strong> PLAN APPROVER (Selected) </strong></h6>
                <div class="table-responsive">
                    <table id="select_app_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Login Id</th>
                                <th class="text-center">User Name</th>
                                <th class="text-center">User Role</th>
                                <th class="text-center">Record Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($getAppGroup as $list)
                                <tr>
                                    <td class="text-left">{{ $list->login_id }}</td>
                                    <td class="text-left">({{ $list->user_id }})  {{ $list->user_name }}</td>
                                    <td class="text-left">{{ $list->role_code }}</td>
                                    <td class="text-left">{{ $list->record_status }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>

            </div>

            <div class="row">
                <div class="col-md-4">
                    <h6><strong> Plan Officer </strong></h6>
                    <div class="table-responsive">
                        <table id="actor_plan_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Login Id</th>
                                    <th class="text-center">User Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getPpOff as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>

                @if (!empty($getPpInfo) && $getPpInfo !== null && $getPpInfo[0]->type_pp !== 'Perbelanjaan')
                <div class="col-md-4">
                    <h6><strong> Plan Verification Officer Group</strong></h6>
                    <div class="table-responsive">
                        <table id="actor_veri_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Login Id</th>
                                    <th class="text-center">User Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getPpVeriOff as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif

                <div class="col-md-4">
                    <h6><strong> Plan Approver Officer Group</strong></h6>
                    <div class="table-responsive">
                        <table id="actor_app_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Login Id</th>
                                    <th class="text-center">User Name</th>
                                    <th class="text-center">Org Name</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getPpApp as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left"><a class="modal-list-data-action"
                                                href="{{ url('/find/orgcode/') }}/{{ $list->org_code }}" target='_blank'>
                                                ({{ $list->org_code }})</a> {{ $list->org_name }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

        </div>

        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">
                <h1><strong> STATUS INFO </strong></h1>
            </div>
            <div class="table-responsive">
                <table id="status_datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Tracking Id</th>
                            <th class="text-center">Doc Type</th>
                            <th class="text-center">Doc No</th>
                            <th class="text-center">Action Description</th>
                            <th class="text-center">Action Date</th>
                            <th class="text-center">Role</th>
                            <th class="text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($getTrackingDiary as $list)
                            <tr>
                                <td class="text-left">{{ $list->tracking_diary_id }}</td>
                                <td class="text-left">{{ $list->doc_type }}</td>
                                <td class="text-center list-trans-doc-no">
                                    <a href='#modal-list-trans-doc-no' class='modal-list-data-action_wf'
                                        data-toggle='modal' data-url='/find/pplan/docno/workflow/{{ $list->doc_no }}'
                                        data-title='Status WorkFlow Search By {{ $list->doc_no }}'>
                                        <strong style="font-weight: bolder;">
                                            {{ $list->doc_no }} </strong><br />
                                    </a>

                                </td>
                                <td class="text-left">{{ $list->action_desc }}</td>
                                <td class="text-left">{{ $list->actioned_date }}</td>
                                <td class="text-left">{{ $list->role_code }}</td>
                                <td class="text-left">{{ $list->status_name }} ({{ $list->status_id }})</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>

        <div id="modal-list-trans-doc-no" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Workflow
                            Status</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">
                <h1><strong> PLAN INFO </strong></h1>
            </div>
            @if ($getItemListPP != null)
                <div class="table-responsive">
                    <table id="pp_item_datatable" class="table table-vcenter table-condensed table-bordered">

                        <thead>
                            <tr>
                                <th class="text-center">Tajuk Pelan</th>
                                <th class="text-center">Perihal Pelan</th>
                                <th class="text-center">Kategori Jenis Perolehan</th>
                                <th class="text-center">Kaedah Perolehan</th>
                                <th class="text-center">Tarikh Jangkaan Pelawaan</th>
                                <th class="text-center">Amaun Anggaran (RM)</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($getItemListPP as $list)
                                <tr>
                                    <td class="text-left">{{ $list->tajuk_pelan }}</td>
                                    <td class="text-left">{{ $list->perihal_pelan }}</td>
                                    <td class="text-left">{{ $list->kategori_jenis_perolehan }}</td>
                                    <td class="text-left">{{ $list->kaedah_perolehan }}</td>
                                    <td class="text-left">{{ $list->tarikh_jangkaan_pelawaan }}</td>
                                    <td class="text-left">{{ $list->estimated_amt }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>

                    </table>
                </div>
            @endif

            @if ($getItemListPE != null)
                <div class="table-responsive">
                    <h4>Belanja Mengurus (VOT B)</h4>
                    <table id="vot_b_item_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Kod Akaun</th>
                                <th class="text-center">Jenis Perbelanjaan</th>
                                <th class="text-center">Jumlah bajet yang diluluskan (RM)</th>
                                <th class="text-center">Bajet untuk kegunaan eP (RM)</th>
                                <th class="text-center">Peruntukan tidak menggunakan eP (RM)</th>
                                <th class="text-center">Justifikasi kerana tidak menggunakan eP</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($votBItems as $list)
                                <tr>
                                    <td class="text-left">{{ $list->gl_acc_code }}</td>
                                    <td class="text-left">{{ $list->description }}</td>
                                    <td class="text-left">{{ $list->budget_amt }}</td>
                                    <td class="text-left">{{ $list->plan_amt }}</td>
                                    <td class="text-left">{{ $list->non_ep_amt }}</td>
                                    <td class="text-left">{{ $list->remark }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <h4>Belanja Mengurus (VOT T)</h4>
                    <table id="vot_t_item_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Kod Akaun</th>
                                <th class="text-center">Jenis Perbelanjaan</th>
                                <th class="text-center">Jumlah bajet yang diluluskan (RM)</th>
                                <th class="text-center">Bajet untuk kegunaan eP (RM)</th>
                                <th class="text-center">Peruntukan tidak menggunakan eP (RM)</th>
                                <th class="text-center">Justifikasi kerana tidak menggunakan eP</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($votTItems as $list)
                                <tr>
                                    <td class="text-left">{{ $list->gl_acc_code }}</td>
                                    <td class="text-left">{{ $list->description }}</td>
                                    <td class="text-left">{{ $list->budget_amt }}</td>
                                    <td class="text-left">{{ $list->plan_amt }}</td>
                                    <td class="text-left">{{ $list->non_ep_amt }}</td>
                                    <td class="text-left">{{ $list->remark }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <h4>Belanja Mengurus (VOT P)</h4>
                    <table id="vot_p_item_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Kod Akaun</th>
                                <th class="text-center">Jenis Perbelanjaan</th>
                                <th class="text-center">Jumlah bajet yang diluluskan (RM)</th>
                                <th class="text-center">Bajet untuk kegunaan eP (RM)</th>
                                <th class="text-center">Peruntukan tidak menggunakan eP (RM)</th>
                                <th class="text-center">Justifikasi kerana tidak menggunakan eP</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($votPItems as $list)
                                <tr>
                                    <td class="text-left">{{ $list->gl_acc_code }}</td>
                                    <td class="text-left">{{ $list->description }}</td>
                                    <td class="text-left">{{ $list->budget_amt }}</td>
                                    <td class="text-left">{{ $list->plan_amt }}</td>
                                    <td class="text-left">{{ $list->non_ep_amt }}</td>
                                    <td class="text-left">{{ $list->remark }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <h4>Belanja Mengurus (VOT S)</h4>
                    <table id="vot_s_item_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Kod Akaun</th>
                                <th class="text-center">Jenis Perbelanjaan</th>
                                <th class="text-center">Jumlah bajet yang diluluskan (RM)</th>
                                <th class="text-center">Bajet untuk kegunaan eP (RM)</th>
                                <th class="text-center">Peruntukan tidak menggunakan eP (RM)</th>
                                <th class="text-center">Justifikasi kerana tidak menggunakan eP</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($votSItems as $list)
                                <tr>
                                    <td class="text-left">{{ $list->gl_acc_code }}</td>
                                    <td class="text-left">{{ $list->description }}</td>
                                    <td class="text-left">{{ $list->budget_amt }}</td>
                                    <td class="text-left">{{ $list->plan_amt }}</td>
                                    <td class="text-left">{{ $list->non_ep_amt }}</td>
                                    <td class="text-left">{{ $list->remark }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <h4>Dana</h4>
                    <table id="dana_item_datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Kod Akaun</th>
                                <th class="text-center">Jenis Perbelanjaan</th>
                                <th class="text-center">Jumlah bajet yang diluluskan (RM)</th>
                                <th class="text-center">Bajet untuk kegunaan eP (RM)</th>
                                <th class="text-center">Peruntukan tidak menggunakan eP (RM)</th>
                                <th class="text-center">Justifikasi kerana tidak menggunakan eP</th>
                                <th class="text-center">Status</th>
                                <th class="text-center">Tarikh Akhir Kemaskini</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($danaItems as $list)
                                <tr>
                                    <td class="text-left">{{ $list->gl_acc_code }}</td>
                                    <td class="text-left">{{ $list->description }}</td>
                                    <td class="text-left">{{ $list->budget_amt }}</td>
                                    <td class="text-left">{{ $list->plan_amt }}</td>
                                    <td class="text-left">{{ $list->non_ep_amt }}</td>
                                    <td class="text-left">{{ $list->remark }}</td>
                                    <td class="text-left">{{ $list->status }}</td>
                                    <td class="text-left">{{ $list->tarikh_akhir_kemaskini }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif
        </div>
        </div>
    @endif
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        App.datatables();

        $('#status_datatable').dataTable({
                order: [
                    [0, "desc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });

            $('#actor_app_datatable').dataTable({
                order: [
                    [1, "asc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });

        function initializeDataTable(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });
        }

        initializeDataTable('#actor_datatable');
        initializeDataTable('#select_veri_datatable');
        initializeDataTable('#select_app_datatable');
        initializeDataTable('#actor_plan_datatable');
        initializeDataTable('#actor_veri_datatable');
        initializeDataTable('#pp_item_datatable');
        initializeDataTable('#vot_b_item_datatable');
        initializeDataTable('#vot_t_item_datatable');
        initializeDataTable('#vot_p_item_datatable');
        initializeDataTable('#vot_s_item_datatable');
        initializeDataTable('#dana_item_datatable');

        var APP_URL = {!! json_encode(url('/')) !!}

        $(document).ready(function() {
                $(document).on("click", ".modal-list-data-action_wf", function() {

                    $('.spinner-loading').show();
                    $('.trans-div-detail').html('Please wait ...').fadeIn();

                    $('#modal-list-data-header').text($(this).attr('data-title'));

                    $.ajax({
                        url: APP_URL + $(this).attr('data-url'),
                        type: "GET",
                        success: function(data) {
                            $data = $(data)
                            console.log($data)
                            $('.spinner-loading').hide();
                            $('.trans-div-detail').html($data).fadeIn();
                        }
                    });

                });

            });
    </script>
@endsection
