@if($dataAll)
<table style="height: 38px; width: 1200px;" border="0">
    <tbody>
        <tr>
            <td style="width: 110%; text-align: right;"><img src= "{{public_path('img/my_logo.png')}}" alt="" width="118" height="60" /></td>
        </tr>
        <tr>
            <td style="text-align: left;" colspan="10" height="40"><strong> Change and Release Request Form</strong></td>
        </tr>
    </tbody>
</table>
<table style="width: 100%; border: 1px solid black; border-collapse: collapse;">
    <tbody>
        <tr>
            <td style="text-align: left; padding: 2px; width: 33%;" colspan="8"><strong> Change and Release Details (For Production use only)</strong></td>
        </tr>
        <tr>
            @if($getValueRequestType)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8">
                <p><span style="font-size: xx-small;">Request Type </span></p>
                <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="172" @foreach($getValueRequestType as $data) @if($data == 172) checked="checked" @endif @endforeach> Deployment System/Server

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="173" @foreach($getValueRequestType as $data) @if($data == 173) checked="checked" @endif @endforeach> Database

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="174" @foreach($getValueRequestType as $data) @if($data == 174) checked="checked" @endif @endforeach> Network

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="175" @foreach($getValueRequestType as $data) @if($data == 175) checked="checked" @endif @endforeach> Application

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="176" @foreach($getValueRequestType as $data) @if($data == 176) checked="checked" @endif @endforeach> Others

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="177" @foreach($getValueRequestType as $data) @if($data == 177) checked="checked" @endif @endforeach> Data Fix

                       <input type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]" value="178" @foreach($getValueRequestType as $data) @if($data == 178) checked="checked" @endif @endforeach> Document/Content
            </td>
            @endif
        </tr>
        <tr>
            @if($dataAll[0]->request_category)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8">
                <p><span style="font-size: xx-small;">Request Category </span></p>
                <input type="radio" name="radio reqcategory" value="{{$dataAll[0]->request_category}}" @if($dataAll[0]->request_category == 179) checked="checked" @endif>Scheduled Change
                       <input type="radio" name="radio reqcategory" value="{{$dataAll[0]->request_category}}" @if($dataAll[0]->request_category == 180) checked="checked" @endif>Urgent Change
            </td>
            @endif
        </tr>

        <tr>
            @if($dataAll[0]->description)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8">
                <p><span style="font-size: xx-small;">Description of Request </span></p>
                <p>{{$dataAll[0]->description}} </p>
            </td>
            @endif
        </tr>
        <tr>
            @if($dataAll[0]->reason)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8">
                <p><span style="font-size: xx-small;">Reason of Request </span></p>
                <p>{{$dataAll[0]->reason}} </p>
            </td>
            @endif
        </tr>


        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">Requested By</span></p>
            </td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" rowspan="2">
                <p><span style="font-size: xx-small;">Recommended By </span></p>
            </td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" rowspan="2" colspan="6">
                <p><span style="font-size: xx-small;">Approved / Not Approved By </span></p>
            </td>
        </tr>
        <tr>
            @if($dataAll[0]->requester_name)
            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">Name : </span><span style="font-size: xx-small;">{{$dataAll[0]->requester_name}}<br /></span></p>
            </td>
            @endif
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Contact No : </span></td>
            @if($dataAll[0]->recommender_name)
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Name : {{$dataAll[0]->recommender_name}}</span></td>
            @else 
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Name : </span></td>
            @endif
            @if($dataAll[0]->approver_name)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="6"><span style="font-size: xx-small;">Name : {{$dataAll[0]->approver_name}}</span></td>
            @else 
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="6"><span style="font-size: xx-small;">Name : </span></td>
            @endif
        </tr>
        <tr>
            @if($dataAll[0]->requester_date)
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Date : {{$dataAll[0]->requester_date}}</span></td>
            @else
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Date : </span></td>
            @endif
            @if($dataAll[0]->recommender_date)
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Date : {{$dataAll[0]->recommender_date}}</span></td>
            @else
            <td style="border: 1px solid black; padding: 2px; width: 33%;"><span style="font-size: xx-small;">Date : </span></td>
            @endif
            @if($dataAll[0]->approver_name)
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="6"><span style="font-size: xx-small;">Date : {{$dataAll[0]->approver_name}}</span></td>
            @else
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="6"><span style="font-size: xx-small;">Date : </span></td>
            @endif
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8"><span style="font-size: xx-small;">Disclaimer : No signature needed for Schedule Change/Data Fix and Emergency Change/Urgent Data Fix if thr approval email has been attached along with the requested activities.</span></td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8" >
                <p><span style="font-size: xx-small;">Remarks </span></p>
                @if($dataAll[0]->remarks)
                <p>{{$dataAll[0]->remarks}}</p>
                @else 
                <p> </P>
                @endif
            </td>
        </tr>
    </tbody>
</table>
<p>  </p>

<table style="width: 100%; border: 1px solid black; border-collapse: collapse;">
    <tbody>

        <tr>
            <td style="border: 1px solid black; padding: 3px; width: 33%;">
                <p><span style="font-size: xx-small;">Document No : eP-ISMS-PSM-CHANGE AND RELEASE REQUEST FORM-V5.4</span></p>
            </td>
            <td style="border: 1px solid black; padding: 3px; width: 33%;" rowspan="3">
                <p><span style="font-size: xx-small;">Page</span></p>
            </td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 3px; width: 33%;">
                <p><span style="font-size: xx-small;">Document Effective Date :</span><span style="font-size: xx-small;"><br /></span></p>
            </td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 3px; width: 33%;">
                <p><span style="font-size: xx-small;">Template Version : 5.4</span><span style="font-size: xx-small;"><br /></span></p>
            </td>
        </tr>
    </tbody>
</table>
<p>  </p>
<table style="width: 100%; border: 1px solid black; border-collapse: collapse;">
    <tbody>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">System/Application Affected</span></p>
                @if($dataAll[0]->system_affected)
                <p>{{$dataAll[0]->system_affected}}</p>
                @else 
                <p> </p>
                @endif
            </td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" rowspan="2">
                <p><span style="font-size: xx-small;">Impact Assessment </span></p>
                @if($dataAll[0]->impact_assessment)
                <p>{{$dataAll[0]->impact_assessment}}</p>
                @else 
                <p> </p>
                @endif
            </td>
        </tr>
        <tr>

            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">Impact Category</span><span style="font-size: xx-small;"><br /></span></p>
                @if($dataAll[0]->impact_category)
                <input type="radio" name="radio impact" value="{{$dataAll[0]->impact_category}}" @if($dataAll[0]->impact_category == 181) checked="checked" @endif> Major
                       <input type="radio" name="radio impact" value="{{$dataAll[0]->impact_category}}" @if($dataAll[0]->impact_category == 182) checked="checked" @endif> Minor
                       @endif
            </td>

        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">Activity/Plan</span></p>
                @if($dataAll[0]->activity_plan)
                <p>{{$dataAll[0]->activity_plan}}</p>
                @else 
                <p><br /> </p>
                @endif
            </td>

            <td style="border: 1px solid black; padding: 2px; width: 33%;">
                <p><span style="font-size: xx-small;">Expected Completion Time/date</span></p>
                @if($dataAll[0]->expected_complete_date)
                <p>{{$dataAll[0]->expected_complete_date}}</p>
                @else 
                <p><br /> </p>
                @endif
            </td>
        </tr>
    </tbody>
</table>
<p> </p>
<table style="width: 100%; border: 1px solid black; border-collapse: collapse;">
    <tbody>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8"><span style="font-size: xx-small;">For Data Centre Use Only</span></td>
        </tr>

        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="3" rowspan="2">
                <p><span style="font-size: xx-small;">Completed By</span></p>
                <p> </p>
            </td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="5" rowspan="2">
                <p><span style="font-size: xx-small;">Acknowledged By </span></p>
                <p> </p>
            </td>
        </tr>
        <tr>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="3"><span style="font-size: xx-small;">Name : </span></td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="5"><span style="font-size: xx-small;">Name : </span></td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="3"><span style="font-size: xx-small;">Date : </span></td>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="5"><span style="font-size: xx-small;">Date : </span></td>
        </tr>
        <tr>
            <td style="border: 1px solid black; padding: 2px; width: 33%;" colspan="8">
                <p><span style="font-size: xx-small;">Remarks </span></p>
                <p> </p>
            </td>
        </tr>
    </tbody>
</table>
@endif



