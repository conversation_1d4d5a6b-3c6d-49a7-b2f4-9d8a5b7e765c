@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
            <i class="gi gi-charts"></i>Pending Transaction as of {{Carbon\Carbon::yesterday()->format('d M Y')}}<br><small>Analysis data from CMS. </small>
        </h1>
    </div>
@endsection

@section('content')
<div class="row">
   <div class="col-md-12">
       <!-- Row Styles Block -->
    <div class="block">
        <!-- Row Styles Title -->
        <div class="block-title">
            <h2><strong>&nbsp;</strong> &nbsp;</h2>
        </div>
        <!-- END Row Styles Title -->

        <!-- Row Styles Content -->
        <div class="table-responsive">
            @foreach($listDataResults as $dataResult)
            <table class="table table-vcenter table-bordered">
                <thead>
                    <tr style="background-color : blue; color:#fff; height:40px;">
                        <th class="text-center" colspan="2">&nbsp;</th>
                        <th class="text-center">(A) PRCR Created</th>
                        <th class="text-center">(B) Pending Supplier Acknowledment</th>
                        <th class="text-center">(C) Pending DO</th>
                        <th class="text-center">(D) Pending GRN</th>
                        <th class="text-center">(E) Pending Invoice</th>
                        <th class="text-center">(F) Pending Payment Match</th>
                        <th class="text-center" style="background-color : green" >(B + C + D) Total</th>
                        <th class="text-center" style="background-color : green" >(E + F) Total</th>
                        <th class="text-center" style="background-color : green" >(B + C + D + E + F) Total</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="active">
                        <td class="text-center bolder" rowspan="3" style="background-color:blue;color:#fff;">{{$dataResult->subject_name}}</td>
                        <td class="text-left bolder">NO.</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_prcr_created:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_pend_supp_ack:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_pend_do:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_pend_grn:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_pend_invoice:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->total_pend_payment_match:0)}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->total_pend_supp_ack:0) + ($dataResult?$dataResult->total_pend_do:0) + ($dataResult?$dataResult->total_pend_grn:0) )}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->total_pend_invoice:0) + ($dataResult?$dataResult->total_pend_payment_match:0) )}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->total_pend_supp_ack:0) + ($dataResult?$dataResult->total_pend_do:0) + ($dataResult?$dataResult->total_pend_grn:0) + ($dataResult?$dataResult->total_pend_invoice:0) + ($dataResult?$dataResult->total_pend_payment_match:0) )}}</td>
                    </tr>
                    <tr class="active">
                        
                        <td class="text-left bolder">PV AMOUNT (RM)</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_prcr_created:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_pend_supp_ack:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_pend_do:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_pend_grn:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_pend_invoice:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->pv_pend_payment_match:0)}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->pv_pend_supp_ack:0) + ($dataResult?$dataResult->pv_pend_do:0) + ($dataResult?$dataResult->pv_pend_grn:0))}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->pv_pend_invoice:0) + ($dataResult?$dataResult->pv_pend_payment_match:0) )}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->pv_pend_supp_ack:0) + ($dataResult?$dataResult->pv_pend_do:0) + ($dataResult?$dataResult->pv_pend_grn:0) + ($dataResult?$dataResult->pv_pend_invoice:0) + ($dataResult?$dataResult->pv_pend_payment_match:0) )}}</td>
                   
                    </tr>
                    <tr class="success">
                        
                        <td class="text-left bolder">EXPECTED TR AMOUNT (RM)</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_prcr_created:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_pend_supp_ack:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_pend_do:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_pend_grn:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_pend_invoice:0)}}</td>
                        <td class="text-center">{{number_format($dataResult?$dataResult->tr_pend_payment_match:0)}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->tr_pend_supp_ack:0) + ($dataResult?$dataResult->tr_pend_do:0) + ($dataResult?$dataResult->tr_pend_grn:0))}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->tr_pend_invoice:0) + ($dataResult?$dataResult->tr_pend_payment_match:0) )}}</td>
                        <td class="text-center">{{number_format( ($dataResult?$dataResult->tr_pend_supp_ack:0) + ($dataResult?$dataResult->tr_pend_do:0) + ($dataResult?$dataResult->tr_pend_grn:0) + ($dataResult?$dataResult->tr_pend_invoice:0) + ($dataResult?$dataResult->tr_pend_payment_match:0) )}}</td>
                    </tr> 
                </tbody>
            </table>
            @endforeach
        </div>
        <!-- END Row Styles Content -->
    </div>
    <!-- END Row Styles Block -->

    </div> 
</div>
@endsection

@section('jsprivate')
<script>
    // Clear 
    $('#page-container').removeAttr('class');
</script>
            
</script>
@endsection