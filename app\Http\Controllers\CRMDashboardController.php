<?php

namespace App\Http\Controllers;

use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Auth;
use App\Services\Traits\SSHService;
use App\Services\Traits\EpSupportTaskService;
use SSH;
use Illuminate\Http\Request;
use DateTime;
use Log;
use Excel;

class CRMDashboardController extends Controller {

    static $url = 'https://epss.eperolehan.gov.my/support/crm/case?case_number';

    public static function crmService() {
        return new CRMService;
    }

    use SSHService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function getDashboardCRM() {
        return $this->getDashboardDetailCRM();
    }

    public function getDashboardCS() {
        return $this->getDashboardCSDetailCRM();
    }

    public function getDashboardCRMOthers() {
        return $this->getDashboardDetailCRMOthers();
    }

    
    public function getDashboardCRMPMO() {
        return $this->getDashboardDetailCRMPMO();
    }

    public function getDashboardCRMTopCases() {
        return $this->getDashboardDetailCRMTopCases();
    }

    public function getDashboardCRMTotalAgeing() {
        return $this->getDashboardDetailCRMTotalAgeing();
    }

    public function getDashboardCRMIncident() {
        return $this->getDashboardDetailCRMIncident();
    }

    public function getDashboardDetailCRMOthers() {
        return view('dashboard_crm_others', []);
    }

    public function getDashboardDetailCRMPMO() {
        return view('dashboard_crm_pmo', []);
    }

    public function getDashboardDetailCRM() {
        return view('dashboard_crm', []);
    }

    public function getDashboardCSDetailCRM() {
        return view('dashboard_cs', []);
    }

    public function getDashboardDetailCRMTopCases() {
        return view('dashboard_crm_top_cases', []);
    }

    public function getDashboardDetailCRMTotalAgeing() {
        return view('dashboard_crm_total_ageing', []);
    }

    public function getDashboardDetailCRMPOMSView() {
        return view('dashboard_crm_poms_view', []);
    }

    public function dashboardCRMIncident() {

        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;
        $countItSpecPendingAck = 0;
        $countItSpecAck = 0;
        $countApprover = 0;
        $countSpecAfterApprover = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;
        $countItSpecPendingAckExceed = 0;
        $countItSpecAckExceed = 0;
        $countApproverExceed = 0;
        $countSpecAfterApproverExceed = 0;

        $current = Carbon::now();

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');
        $APPROVER_TASK = 'Assigned to Approver';
        $FLAG_SPECIALIST = array(2, 3);
        $FLAG_SPECIALIS_AFTER_APPROVER = 4;
        $LIST_GROUP_SPECIALIST = array(
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRM();

        foreach ($listTasks as $tasks) {

            $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if (in_array($tasks->taskname, $INITIAL_TASK)) {

                if ($tasks->status == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                    } else {
                        $countItIncidentPendAckExceed++;
                    }
                }if ($tasks->status == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItIncidentAck++;
                    } else {
                        $countItIncidentAckExceed++;
                    }
                }
            }

//            
            if ($tasks->taskname == $APPROVER_TASK) {
                if ($datedue > $current) {
                    $countApprover++;
                } else {
                    $countApproverExceed++;
                }
            }
            if ($tasks->flag == $FLAG_SPECIALIS_AFTER_APPROVER) {
                if ($datedue > $current) {
                    $countSpecAfterApprover++;
                } else {
                    $countSpecAfterApproverExceed++;
                }
            }
        }

        $listTasksSpecialist = self::crmService()->getDashboardCRMSpecialist();


        foreach ($listTasksSpecialist as $tasks) {
            $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if (in_array($tasks->taskname, $LIST_GROUP_SPECIALIST) && in_array($tasks->flag, $FLAG_SPECIALIST)) {
                if ($tasks->status == $PENDING_ACK) {
                    if ($datedue > $current) {
                        $countItSpecPendingAck++;
                    } else {
                        $countItSpecPendingAckExceed++;
                    }
                } else {
                    if ($datedue > $current) {
                        $countItSpecAck++;
                    } else {
                        $countItSpecAckExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td style='width: 60%;'><strong>Pending Acknowledgement IT Coordinator</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itIncPendAck'
                            data-title='List Pending Acknowledgement for IT Incident (15 Minute)' >{$countItIncidentPendAck}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itIncPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Incident (15 Minute)' >{$countItIncidentPendAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 60%;'><strong>Acknowledged for IT Coordinator</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itIncAcknowledge'
                            data-title='List IT Coordinator Not Completed' >{$countItIncidentAck}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itIncAcknowledgeExceed'
                            data-title='List IT Coordinator Not Completed' >{$countItIncidentAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 60%;'><strong>Pending Acknowledgement for IT Specialist</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecPendingAck'
                            data-title='List Pending Acknowledgement for IT Specialist (4 Hour)' >{$countItSpecPendingAck}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecPendingAckExceed'
                            data-title='List Pending Acknowledgement for IT Specialist (4 Hour)' >{$countItSpecPendingAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 60%;'><strong>Acknowledged for IT Specialist</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecAcknowledge'
                            data-title='List IT Specialist Not Completed' >{$countItSpecAck}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecAcknowledgeExceed'
                            data-title='List IT Specialist Not Completed' >{$countItSpecAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 60%;'><strong>Pending Approver Group</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itApprover'
                            data-title='List Pending Approver' >{$countApprover}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itApproverExceed'
                            data-title='List Pending Approver' >{$countApproverExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 60%;'><strong>IT Specialist After Approver (More than 4 Hour)</strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecAfterApprover'
                            data-title='List Pending Specialist (After Approver)' >{$countSpecAfterApprover}</a></strong></td>
                <td style='width: 60%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/crm/itSpecAfterApproverExceed'
                            data-title='List Pending Specialist (After Approver)' >{$countSpecAfterApproverExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItIncPendAck() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');

        $list = self::crmService()->getDashboardCRM();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->status == $PENDING_ACK && in_array($value->taskname, $INITIAL_TASK)) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItIncAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');

        $list = self::crmService()->getDashboardCRM();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->status == $ACKNOWLEDGE && in_array($value->taskname, $INITIAL_TASK)) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecPendingAck() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $FLAG_SPECIALIST = array(2, 3);
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRMSpecialist();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($datedue > $current && $value->status == $PENDING_ACK && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {


                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->taskname</strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';
        $FLAG_SPECIALIST = array(2, 3);
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRMSpecialist();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {
            $taskdue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            if ($taskdue > $current && $value->status == $ACKNOWLEDGE && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {
                $slaStart = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                $slaEnd = Carbon::parse($value->acknowledgetime)->addHour(12)->format("Y-m-d H:i:s");
                $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                $dateDiff = $current->diff(new DateTime($slaEnd));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$slaStart</strong></td>
                        <td class='text-left'><strong>$slaEnd</strong></td>
                        <td class='text-left'><strong>$acknowledgeTime</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApprover() {
        $APPROVER_TASK = 'Assigned to Approver';

        $list = self::crmService()->getDashboardCRM();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($datedue > $current && $value->taskname == $APPROVER_TASK) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskSpecAfterApprover() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $FLAG_SPECIALIS_AFTER_APPROVER = 4;
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRM();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {
            $dateStart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $dateEnd = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $slaStart = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $taskDuration = $value->taskduration + 8;
            $slaEnd = Carbon::parse($value->acknowledgetime)->addHour($taskDuration)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            $start = '';
            $end = '';

            if ($dateEnd > $current && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && $value->flag == $FLAG_SPECIALIS_AFTER_APPROVER) {
                if ($value->status == $PENDING_ACK) {
                    $start = $start . "$dateStart";
                    $end = $end . "$dateEnd";
                } else {
                    $start = $start . "$slaStart";
                    $end = $end . "$slaEnd";
                }

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$start</strong></td>
                        <td class='text-left'><strong>$end</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItIncPendAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRM();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->status == $PENDING_ACK && in_array($value->taskname, $INITIAL_TASK)) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItIncAcknowledgeExceed() {
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRM();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->status == $ACKNOWLEDGE && in_array($value->taskname, $INITIAL_TASK)) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecPendingAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $FLAG_SPECIALIST = array(2, 3);
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRMSpecialist();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {
            $taskdue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            if ($taskdue < $current && $value->status == $PENDING_ACK && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {
                $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
                $dateend = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $dateDiff = $current->diff(new DateTime($dateend));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->taskname</strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$dateend</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';
        $FLAG_SPECIALIST = array(2, 3);
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRMSpecialist();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $taskdue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            if ($taskdue < $current && $value->status == $ACKNOWLEDGE && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && in_array($value->flag, $FLAG_SPECIALIST)) {
                $slaStart = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                $slaEnd = Carbon::parse($value->acknowledgetime)->addHour(12)->format("Y-m-d H:i:s");
                $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                $dateDiff = $current->diff(new DateTime($slaEnd));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$slaStart</strong></td>
                        <td class='text-left'><strong>$slaEnd</strong></td>
                        <td class='text-left'><strong>$acknowledgeTime</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApproverExceed() {
        $APPROVER_TASK = 'Assigned to Approver';

        $list = self::crmService()->getDashboardCRM();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($datedue < $current && $value->taskname == $APPROVER_TASK) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskSpecAfterApproverExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $FLAG_SPECIALIS_AFTER_APPROVER = 4;
        $LIST_GROUP_SPECIALIST = array('Assigned to Archisoft Team',
            'Assigned to Archissoft Build Team',
            'Assigned to Group Archisoft',
            'Assigned to Group Archisoft Build Team',
            'Assigned to Group IT Specialist(Database Admin)',
            'Assigned to Group IT Specialist(Database Management)',
            'Assigned to Group IT Specialist(Developer)',
            'Assigned to Group IT Specialist(Network Admin)',
            'Assigned to Group IT Specialist(Production Support)',
            'Assigned to Group IT Specialist(Security)',
            'Assigned to Group IT Specialist(Server Admin)',
            'Assigned to Group Middleware');

        $list = self::crmService()->getDashboardCRM();
        $current = Carbon::now();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {

            $dateStart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $dateEnd = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $slaStart = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $taskDuration = $value->taskduration + 8;
            $slaEnd = Carbon::parse($value->acknowledgetime)->addHour($taskDuration)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            $start = '';
            $end = '';

            if ($dateEnd < $current && in_array($value->taskname, $LIST_GROUP_SPECIALIST) && $value->flag == $FLAG_SPECIALIS_AFTER_APPROVER) {
                if ($value->status == $PENDING_ACK) {
                    $start = $start . "$dateStart";
                    $end = $end . "$dateEnd";
                } else {
                    $start = $start . "$slaStart";
                    $end = $end . "$slaEnd";
                }

                $data = "
                    <tr>
                        <td class='text-left'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' '" . self::$url . "=$value->caseno' title='View Detail Case in CRM'>$value->caseno</a></strong></td>
                        <td class='text-left'><strong>$value->status</strong></td>
                        <td class='text-left'><strong>$start</strong></td>
                        <td class='text-left'><strong>$end</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function showCrmSchedulerEmailInbound() {

        $data = self::crmService()->getLatestJobQue('Check Inbound Mailboxes');
        $current = Carbon::now();

        $html = "<table class='table table-borderless table-vcenter table-striped table-bordered' >
                    <thead>
                        <tr>
                            <th class='text-left'>Name</th>
                            <th class='text-left'>Latest Execute Time</th>
                            <th class='text-left'>Status</th>
                            <th class='text-left'>Resolution</th>
                            <th class='text-left'>Message</th>
                            <th class='text-left'>State</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";

        $executeTime = Carbon::parse($data->execute_time)->addHour(8)->format("Y-m-d H:i:s");
        $dateDiff = $current->diff(new DateTime($executeTime));

        if ($dateDiff->i < 5) {
            $state = "<span class='label label-success'>GOOD</span>";
        } else {
            $state = "<a href='#confirm-modal' 
                            class='modal-confirm-action label label-danger' 
                            data-toggle='modal'
                            data-title='test title'
                            data-id='" . $data->id . "'>BAD</a>";
        }

        //$data->message = 'this is a test';

        if (strlen($data->message) > 0) {
            $message = '<span class="d-inline-block" tabindex="0" data-toggle="tooltip" title="' . $data->message . '">
                        <span class="label label-default" style="pointer-events: none;">' . substr($data->message, 0, 10) . '...</span>
                        </span>';
        } else {
            $message = '';
        }

        $data = "
                <tr>
                    <td class='text-left'><strong>$data->name</strong></td>
                    <td class='text-left'><strong>$executeTime</strong></td>
                    <td class='text-left'><strong>$data->status</strong></td>
                    <td class='text-left'><strong>$data->resolution</strong></td>
                    <td class='text-left'>$message</td>
                    <td class='text-left'><strong>$state</strong></td>
                </tr>";
        $html = $html . $data;
        $html = $html . "</tbody>";
        return $html;
    }

    public function dashboardCRMServiceITCoord() {
        $countItServicePendAck = 0;
        $countItServiceAck = 0;

        $countItServicePendAckExceed = 0;
        $countItServiceAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr>
                        <th>Group Assigned</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITServiceCoord();
        // dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");



                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItServicePendAck++;
                    } else {
                        $countItServicePendAckExceed++;
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItServiceAck++;
                    } else {
                        $countItServiceAckExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Group IT Coordinator</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItServicePendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itCoordServPendAck'
                            data-title='List Pending Acknowledgement for IT Coordinator (Within 15 Minute)' >{$countItServicePendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItServicePendAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itCoordServPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Coordinator (Exceed 15 Minute)' >{$countItServicePendAckExceed}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItServiceAck)}' 
                            data-toggle='modal' data-url='/list/crm/itCoordServAcknowledge'
                            data-title='List IT Coordinator Acknowledge (Within 15 Minute)' >{$countItServiceAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItServiceAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itCoordServAcknowledgeExceed'
                            data-title='List IT Coordinator Acknowledge (Exceed 15 Minute)' >{$countItServiceAckExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItCoordServPendAck() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $INITIAL_TASK = array('Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident');

        $list = self::crmService()->getDashboardCRMITServiceCoord();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItCoordServPendAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITServiceCoord();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItCoordServAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITServiceCoord();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItCoordServAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITServiceCoord();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMOthersAssignedToITSpec(Request $request) {
        $countItSpecOthersPendAck = 0;
        $countItSpecOthersAck = 0;
        $countItSpecOthersPendAckExceed = 0;
        $countItSpecOthersAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();
        $GroupID = $request->groupid;

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMOthersAssignedToITSpec($GroupID);
        //  dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItSpecOthersPendAck++;
                    } else {
                        $countItSpecOthersPendAckExceed++;
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItSpecOthersAck++;
                    } else {
                        $countItSpecOthersAckExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>               
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecOthersPendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecOthersPendAck/$GroupID'
                            data-title='List Pending Acknowledgement for IT Specialist' >{$countItSpecOthersPendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecOthersPendAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecOthersPendAckExceed/$GroupID'
                            data-title='List Pending Acknowledgement for IT Specialist - Exceed' >{$countItSpecOthersPendAckExceed}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data'                              
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecOthersAck)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecOthersAcknowledge/$GroupID'
                            data-title='List IT Specialist Acknowledge' >{$countItSpecOthersAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecOthersAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecOthersAcknowledgeExceed/$GroupID'
                            data-title='List IT Specialist Acknowledge - Exceed' >{$countItSpecOthersAckExceed}</a></strong></td>
            </tr>            
            </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItSpecOthersPendAck($GroupID) {

        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMOthersAssignedToITSpec($GroupID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecOthersPendAckExceed($GroupID) {
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMOthersAssignedToITSpec($GroupID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecOthersAcknowledge($GroupID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMOthersAssignedToITSpec($GroupID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecOthersAcknowledgeExceed($GroupID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMOthersAssignedToITSpec($GroupID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>  
                        <td class='text-left'><strong>$value->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMIncidentITCoord() {
        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $WITHIN = 'within';
        $EXCEED = 'exceed';
        $current = Carbon::now();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr>
                        <th>Group Assigned</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITCoord();
        // dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");



                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                    } else {
                        $countItIncidentPendAckExceed++;
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($acknowledgetime <= $datedue) {
                        $countItIncidentAck++;
                    } else {
                        $countItIncidentAckExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Group IT Coordinator</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentPendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itcoord/within/{$PENDING_ACK}'
                            data-title='List Pending Acknowledgement for IT Coordinator (Within 15 Minute)' >{$countItIncidentPendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItIncidentPendAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itcoord/exceed/{$PENDING_ACK}'
                            data-title='List Pending Acknowledgement for IT Coordinator (Exceed 15 Minute)' >{$countItIncidentPendAckExceed}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentAck)}' 
                            data-toggle='modal' data-url='/list/crm/itcoord/within/{$ACKNOWLEDGE}'
                            data-title='List IT Coordinator Acknowledge (Within 15 Minute)' >{$countItIncidentAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItIncidentAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itcoord/exceed/{$ACKNOWLEDGE}'
                            data-title='List IT Coordinator Acknowledge (Exceed 15 Minute)' >{$countItIncidentAckExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItCoordWithin($status) {
        $list = self::crmService()->getDashboardCRMITCoord($status);
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Ageing (Days)</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');
            $caseEntered = Carbon::parse($value->caseEntered)->addHour(8)->format("Y-m-d H:i:s");
            $caseModified = Carbon::parse($value->caseModified)->addHour(8)->format("Y-m-d H:i:s");
            if (in_array($value->caseStatus, $open)) {
                $ageing = $current->diff(new DateTime($caseEntered));
            } else {
                $ageing = $caseModified->diff(new DateTime($caseEntered));
            }
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $acknowledgeTime != '') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong> Not applicable </strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItCoordExceed($status) {
        $list = self::crmService()->getDashboardCRMITCoord($status);
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Ageing (Days)</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $ackTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');
            $caseEntered = Carbon::parse($value->caseEntered)->addHour(8)->format("Y-m-d H:i:s");
            $caseModified = Carbon::parse($value->caseModified)->addHour(8)->format("Y-m-d H:i:s");
            if (in_array($value->caseStatus, $open)) {
                $ageing = $current->diff(new DateTime($caseEntered));
            } else {
                $ageing = $caseModified->diff(new DateTime($caseEntered));
            }
            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->acknowledgetime == '' && $datedue <= $current && $status == 'Pending Acknowledgement') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }else if ($value->acknowledgetime != '' && $ackTime > $datedue && $status == 'Acknowledge') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMIncidentITSpec() {
        $countItSpecIncidentPendAck = 0;
        $countItSpecIncidentAck = 0;

        $countItSpecIncidentPendAckExceed = 0;
        $countItSpecIncidentAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Group Assigned</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpec();
        //   dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {
                $current = Carbon::now();
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

                if ($tasks->taskStatus == $PENDING_ACK) {
                    $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                    if ($datedue > $current) {
                        $countItSpecIncidentPendAck++;
                    } else {
                        $countItSpecIncidentPendAckExceed++;
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE) {
                    $datedue = Carbon::parse($tasks->actualstop)->addHour(8)->format("Y-m-d H:i:s");
                    if ($current <= $datedue) {
                        $countItSpecIncidentAck++;
                    } else {
                        $countItSpecIncidentAckExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Group IT Specialist(Production Support)</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecIncidentPendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecIncPendAck'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 4 Hour)' >{$countItSpecIncidentPendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecIncidentPendAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecIncPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 4 Hour)' >{$countItSpecIncidentPendAckExceed}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecIncidentAck)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecIncAcknowledge'
                            data-title='List IT Specialist Acknowledge (Within 4 Hour)' >{$countItSpecIncidentAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecIncidentAckExceed)}' 
                            data-toggle='modal' data-url='/list/crm/itSpecIncAcknowledgeExceed'
                            data-title='List IT Specialist Acknowledge (Exceed 4 Hour)' >{$countItSpecIncidentAckExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItSpecIncPendAck() {
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpec();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == null) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecIncPendAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpec();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == null) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecIncAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpec();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                            <th class='text-center'>Remaining Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $dueTime = Carbon::parse($value->acknowledgetime)->addHour(12)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            if ($datedue >= $current && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == null && $value->taskFlag == 3) {

                $dateDiff = $current->diff(new DateTime($dueTime));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><strong>$acknowledgeTime</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItSpecIncAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpec();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                            <th class='text-center'>Exceed Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $dueTime = Carbon::parse($value->acknowledgetime)->addHour(12)->format("Y-m-d H:i:s");
            if ($value->acknowledgetime != '' && $datedue <= $current && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == null && $value->taskFlag == 3) {

                $dateDiff = $current->diff(new DateTime($dueTime));
                $exceedRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><strong>$acknowledgeTime</strong></td>
                        <td class='text-left'><font color='red'><strong>$exceedRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMIncidentITSpecSeverity(Request $request) {
        $countItSpecSeverityIncidentPendAck_S1 = 0;
        $countItSpecSeverityIncidentPendAck_S2 = 0;
        $countItSpecSeverityIncidentPendAck_S3 = 0;
        $countItSpecSeverityIncidentAck_S1 = 0;
        $countItSpecSeverityIncidentAck_S2 = 0;
        $countItSpecSeverityIncidentAck_S3 = 0;

        $countItSpecSeverityIncidentPendAckExceed_S1 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S2 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S3 = 0;
        $countItSpecSeverityIncidentAckExceed_S1 = 0;
        $countItSpecSeverityIncidentAckExceed_S2 = 0;
        $countItSpecSeverityIncidentAckExceed_S3 = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();
        $GroupID = $request->groupid;
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Day of Severity</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecSeverity($GroupID);
//        Log::info($listTasks);
        //   dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {
                $severity = $tasks->taskSeverity;
                if ($tasks->taskStatus == $PENDING_ACK) {
                    $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                    if ($severity == 's1') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S1++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S1++;
                        }
                    } else if ($severity == 's2') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S2++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S2++;
                        }
                    } else {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S3++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S3++;
                        }
                    }
                } else {
                    if ($severity == 's1') {
                        $datedue = Carbon::parse($tasks->actualstart)->addDay(1)->addHour(8)->format("Y-m-d H:i:s");
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentAck_S1++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S1++;
                        }
                    } else if ($severity == 's2') {
                        $datedue = Carbon::parse($tasks->actualstart)->addDay(3)->addHour(8)->format("Y-m-d H:i:s");
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentAck_S2++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S2++;
                        }
                    } else {
                        $datedue = Carbon::parse($tasks->actualstart)->addDay(5)->addHour(8)->format("Y-m-d H:i:s");
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentAck_S3++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S3++;
                        }
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>1 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$PENDING_ACK/s1'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 1 Day)' >{$countItSpecSeverityIncidentPendAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$PENDING_ACK/s1'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 1 Day)' >{$countItSpecSeverityIncidentPendAckExceed_S1}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$ACKNOWLEDGE/s1'
                            data-title='List IT Specialist Acknowledge (Within 1 Day)' >{$countItSpecSeverityIncidentAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$ACKNOWLEDGE/s1'
                            data-title='List IT Specialist Acknowledge (Exceed 1 Day)' >{$countItSpecSeverityIncidentAckExceed_S1}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>3 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$PENDING_ACK/s2'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 3 Days)' >{$countItSpecSeverityIncidentPendAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$PENDING_ACK/s2'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 3 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S2}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$ACKNOWLEDGE/s2'
                            data-title='List IT Specialist Acknowledge (Within 3 Days)' >{$countItSpecSeverityIncidentAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$ACKNOWLEDGE/s2'
                            data-title='List IT Specialist Acknowledge (Exceed 3 Days)' >{$countItSpecSeverityIncidentAckExceed_S2}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>5 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$PENDING_ACK/s3'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 5 Days)' >{$countItSpecSeverityIncidentPendAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$PENDING_ACK/s3'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 5 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S3}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/detail/within/$GroupID/$ACKNOWLEDGE/s3'
                            data-title='List IT Specialist Acknowledge (Within 5 Days)' >{$countItSpecSeverityIncidentAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/detail/exceed/$GroupID/$ACKNOWLEDGE/s3'
                            data-title='List IT Specialist Acknowledge (Exceed 5 Days)' >{$countItSpecSeverityIncidentAckExceed_S3}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function dashboardSlaDetailWithin($groupId, $status, $severity) {
        $list = self::crmService()->getDashboardCRMITSpecSeverity($groupId, $status, $severity);
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Ageing (Days)</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>";

        $html = $html . "</tr></thead><tbody>";
        $counter = 0;
        foreach ($list as $tasks) {
            $current = Carbon::now();
            $severity = $tasks->taskSeverity;
            if ($tasks->taskStatus == 'Pending Acknowledgement') {
                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $datestart = Carbon::parse($tasks->datestart)->addHour(8)->format("Y-m-d H:i:s");
            } else {
                $datestart = Carbon::parse($tasks->actualstart)->addHour(8)->format("Y-m-d H:i:s");
                if ($severity == 's1') {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(1)->addHour(8)->format("Y-m-d H:i:s");
                } else if ($severity == 's2') {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(3)->addHour(8)->format("Y-m-d H:i:s");
                } else {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(5)->addHour(8)->format("Y-m-d H:i:s");
                }
            }

            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');
            $caseEntered = Carbon::parse($tasks->caseEntered)->addHour(8)->format("Y-m-d H:i:s");
            $caseModified = Carbon::parse($tasks->caseModified)->addHour(8)->format("Y-m-d H:i:s");
            if (in_array($tasks->caseStatus, $open)) {
                $ageing = $current->diff(new DateTime($caseEntered));
            } else {
                $ageing = $caseModified->diff(new DateTime($caseEntered));
            }

            if ($datedue > Carbon::now()) {
                $color = 'green';
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$tasks->caseNumber' title='View Detail Case in CRM'>$tasks->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$tasks->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$tasks->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$tasks->redmine</strong></td>
                        <td class='text-left'><strong>$tasks->subCategory</strong></td>
                        <td class='text-left'><strong>$tasks->caseName</strong></td>
                        <td class='text-left'><strong>$tasks->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='$color'><strong>$timeRemaining</strong></td>
                    </tr>";
            $html = $html . $data;
            }else {
                $color = 'green';
                
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function dashboardSlaDetailExceed($groupId, $status, $severity) {
        $list = self::crmService()->getDashboardCRMITSpecSeverity($groupId, $status, $severity);
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Ageing (Days)</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>PIC Specialist</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>";

        $html = $html . "</tr></thead><tbody>";
        $counter = 0;
        foreach ($list as $tasks) {
            $current = Carbon::now();
            $severity = $tasks->taskSeverity;
            if ($tasks->taskStatus == 'Pending Acknowledgement') {
                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $datestart = Carbon::parse($tasks->datestart)->addHour(8)->format("Y-m-d H:i:s");
            } else {
                $datestart = Carbon::parse($tasks->actualstart)->addHour(8)->format("Y-m-d H:i:s");
                if ($severity == 's1') {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(1)->addHour(8)->format("Y-m-d H:i:s");
                } else if ($severity == 's2') {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(3)->addHour(8)->format("Y-m-d H:i:s");
                } else {
                    $datedue = Carbon::parse($tasks->actualstart)->addDay(5)->addHour(8)->format("Y-m-d H:i:s");
                }
            }

            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            $open = array('Open_New', 'Open_Assigned', 'Open_Pending Input', 'Open_Pending_Approval');
            $caseEntered = Carbon::parse($tasks->caseEntered)->addHour(8)->format("Y-m-d H:i:s");
            $caseModified = Carbon::parse($tasks->caseModified)->addHour(8)->format("Y-m-d H:i:s");
            if (in_array($tasks->caseStatus, $open)) {
                $ageing = $current->diff(new DateTime($caseEntered));
            } else {
                $ageing = $caseModified->diff(new DateTime($caseEntered));
            }

            if (Carbon::now() >= $datedue) {
                $color = 'red';
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$tasks->caseNumber' title='View Detail Case in CRM'>$tasks->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$ageing->days</strong></td>
                        <td class='text-left'><strong>$tasks->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$tasks->IndividualAssigned</strong></td>
                        <td class='text-left'><strong>$tasks->redmine</strong></td>
                        <td class='text-left'><strong>$tasks->subCategory</strong></td>
                        <td class='text-left'><strong>$tasks->caseName</strong></td>
                        <td class='text-left'><strong>$tasks->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='$color'><strong>$timeRemaining</strong></td>
                    </tr>";
            $html = $html . $data;
            } 
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMIncidentITSpecRedmine() {
        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecRedmine();
        // dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                //   $AssignedGroup = $tasks->assignedGroup;
                $redmine = $tasks->redmine;


                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '' && $redmine != null) {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                        //    $AssignedGroup = $tasks->assignedGroup;
                    } else {
                        $countItIncidentPendAckExceed++;
                        //  $AssignedGroup = $tasks->assignedGroup;
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '' && $redmine != null) {
                    if ($acknowledgetime <= $datedue) {
                        $countItIncidentAck++;
                        //  $AssignedGroup = $tasks->assignedGroup;
                    } else {
                        $countItIncidentAckExceed++;
                        //   $AssignedGroup = $tasks->assignedGroup;
                    }
                }
            }
        }

        $html .= "
            <tr>
                
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentPendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itRedmineIncPendAck'
                            data-title='List Pending Acknowledgement for IT Specialist (Redmine)' >{$countItIncidentPendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItIncidentPendAckExceed)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itRedmineIncPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Specialist (Redmine)' >{$countItIncidentPendAckExceed}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentAck)}' 
                            data-toggle='modal' data-url='/list/crm/itRedmineIncAcknowledge'
                            data-title='List IT Specialist Acknowledge (Redmine)' >{$countItIncidentAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItIncidentAckExceed)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itRedmineIncAcknowledgeExceed'
                            data-title='List IT Specialist Acknowledge (Redmine)' >{$countItIncidentAckExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItRedmineIncPendAck() {
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecRedmine();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $redmine = $value->redmine;

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $redmine != null) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItRedmineIncPendAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecRedmine();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $redmine = $value->redmine;

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $redmine != null) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItRedmineIncAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecRedmine();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $redmine = $value->redmine;
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $redmine != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItRedmineIncAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecRedmine();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            $redmine = $value->redmine;
            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $redmine != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='" . self::$url . "=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMIncidentITSpecApproval() {
        $countItIncidentPendAck = 0;
        $countItIncidentAck = 0;
        $countItIncidentReassigned = 0;

        $countItIncidentPendAckExceed = 0;
        $countItIncidentAckExceed = 0;
        $countItIncidentReassignedExceed = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $REASSIGNED = 'Reassigned';
        $current = Carbon::now();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecApproval();
        // dump($listTasks);
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
                $estimateDue = date('Y-m-d H:i:s', strtotime($tasks->acknowledgetime . "+ $tasks->taskduration"));
                $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
                $redmine = $tasks->redmine;
                $taskFlag = $tasks->taskFlag;

                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '' && $redmine != null && $taskFlag == 4) {
                    if ($datedue > $current) {
                        $countItIncidentPendAck++;
                    } else {
                        $countItIncidentPendAckExceed++;
                    }
                }
                if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '' && $redmine != null && $taskFlag == 4) {
                    if ($current <= $estimateDueDate) {
                        $countItIncidentAck++;
                        //   Log::info('Test Acknowledge : '.$acknowledgetime.' = '.$estimateDueDate .'Curr Now : '. $current);
                    } else {
                        //  Log::info('Test Acknowledge Overdue : '.$acknowledgetime.' = '.$estimateDueDate.'Curr Now : '. $current);
                        $countItIncidentAckExceed++;
                    }
                }
                if ($tasks->taskStatus == $REASSIGNED && $tasks->acknowledgetime != '' && $redmine != null && $taskFlag == 4) {
                    if ($current <= $estimateDueDate) {
                        $countItIncidentReassigned++;
                        //  Log::info('Test Reassigned : '.$acknowledgetime.' = '.$estimateDueDate .'Curr Now : '. $current . ' Duration : '.$tasks->taskduration);  
                    } else {
                        //  Log::info('Test Reassigned Overdue : '.$acknowledgetime.' = '.$estimateDueDate.'Curr Now : '. $current. ' Duration : '.$tasks->taskduration); 
                        $countItIncidentReassignedExceed++;
                    }
                }
            }
        }

        $html .= "
            <tr>
                
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentPendAck)}' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncPendAck'
                            data-title='List Pending Acknowledgement for IT Specialist (After Approval)' >{$countItIncidentPendAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItIncidentPendAckExceed)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Specialist (Exceed Time After Approval)' >{$countItIncidentPendAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentAck)}' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncAcknowledge'
                            data-title='List IT Specialist Acknowledge (Approved)' >{$countItIncidentAck}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItIncidentAckExceed)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncAcknowledgeExceed'
                            data-title='List IT Specialist Acknowledge (Exceed Time Approved)' >{$countItIncidentAckExceed}</a></strong></td>
            </tr>
            <tr>
                <td style='width: 20%;'><strong>{$REASSIGNED}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItIncidentReassigned)}' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncReassigned'
                            data-title='List IT Specialist Reassigned (Reword)' >{$countItIncidentReassigned}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItIncidentReassignedExceed)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itApprovalIncReassignedExceed'
                            data-title='List IT Specialist Reassigned (Exceed Time Reword)' >{$countItIncidentReassignedExceed}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listTaskItApproverIncPendAck() {
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Duration Approved</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $redmine = $value->redmine;
            $taskFlag = $value->taskFlag;

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $redmine != null && $taskFlag == 4) {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$value->itapprover_duration</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApproverIncPendAckExceed() {
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Duration Approved</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $redmine = $value->redmine;
            $taskFlag = $value->taskFlag;

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $redmine != null && $taskFlag == 4) {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$value->itapprover_duration</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApprovalIncAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Duration Time</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>     
                            <th class='text-center'>Time Remain</th> 
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $redmine = $value->redmine;
            $actualSLAStart = Carbon::parse($value->sla_start)->addHour(8)->format("Y-m-d H:i:s");
            $duration = $value->taskduration;
            $estimateDue = date('Y-m-d H:i:s', strtotime($value->acknowledgetime . "+ $value->taskduration"));
            $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $taskFlag = $value->taskFlag;
            $dateDiff = $current->diff(new DateTime($estimateDueDate));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            if ($current <= $estimateDueDate && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $redmine != null && $taskFlag == 4) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$duration</strong></td>
                        <td class='text-left'><strong>$actualSLAStart</strong></td>
                        <td class='text-left'><strong>$estimateDueDate</strong></td>  
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td> 
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApprovalIncAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Duration Time</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>     
                            <th class='text-center'>Time Exceed</th> 
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $duration = $value->taskduration;
            $actualSLAStart = Carbon::parse($value->sla_start)->addHour(8)->format("Y-m-d H:i:s");
            $redmine = $value->redmine;
            $estimateDue = date('Y-m-d H:i:s', strtotime($value->acknowledgetime . "+ $value->taskduration"));
            $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $taskFlag = $value->taskFlag;
            $dateDiff = $current->diff(new DateTime($estimateDueDate));
            $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            if ($value->acknowledgetime != '' && $estimateDueDate < $current && $value->taskStatus == $ACKNOWLEDGE && $redmine != null && $taskFlag == 4) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$duration</strong></td>
                        <td class='text-left'><strong>$actualSLAStart</strong></td>
                        <td class='text-left'><strong>$estimateDueDate</strong></td>  
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApprovalIncReassigned() {
        $REASSIGNED = 'Reassigned';

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Duration Time</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>     
                            <th class='text-center'>Time Remain</th> 
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $redmine = $value->redmine;
            $actualSLAStart = Carbon::parse($value->sla_start)->addHour(8)->format("Y-m-d H:i:s");
            $duration = $value->taskduration;
            $estimateDue = date('Y-m-d H:i:s', strtotime($value->acknowledgetime . "+ $value->taskduration"));
            $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $taskFlag = $value->taskFlag;
            $dateDiff = $current->diff(new DateTime($estimateDueDate));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
            if ($current <= $estimateDueDate && $value->acknowledgetime != '' && $value->taskStatus == $REASSIGNED && $redmine != null && $taskFlag == 4) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$duration</strong></td>
                        <td class='text-left'><strong>$actualSLAStart</strong></td>
                        <td class='text-left'><strong>$estimateDueDate</strong></td>  
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>                       
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItApprovalIncReassignedExceed() {
        $REASSIGNED = 'Reassigned';

        $list = self::crmService()->getDashboardCRMITSpecApproval();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Duration Time</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>     
                            <th class='text-center'>Time Exceed</th>  
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $duration = $value->taskduration;
            $actualSLAStart = Carbon::parse($value->sla_start)->addHour(8)->format("Y-m-d H:i:s");
            $redmine = $value->redmine;
            $estimateDue = date('Y-m-d H:i:s', strtotime($value->acknowledgetime . "+ $value->taskduration"));
            $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();
            $taskFlag = $value->taskFlag;
            $dateDiff = $current->diff(new DateTime($estimateDueDate));
            $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->acknowledgetime != '' && $estimateDueDate < $current && $value->taskStatus == $REASSIGNED && $redmine != null && $taskFlag == 4) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong>$value->caseNumber</strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong><a target='_blank' href='https://redmine.eperolehan.gov.my/issues/$value->redmine' title='View Detail Redmine No'>$value->redmine</a></strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$duration</strong></td>
                        <td class='text-left'><strong>$actualSLAStart</strong></td>
                        <td class='text-left'><strong>$estimateDueDate</strong></td>  
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardCRMCS() {

        $current = Carbon::now();

        $getCurr = strtotime($current);
        $TodayDate = date('d/m/Y', $getCurr);

        $countTotalCasesfromOpenPortal = 0;
        $countOpenPortalAssignedWithinSLA = 0;
        $countOpenPortalPendingResponseWithinSLA = 0;
        $countOpenPortalPendingRespondTakenbyScheduler = 0; // scheduler run but cs still not respond to the case
        $countOpenPortalAssignedExceedSLA = 0;
        $countOpenPortalPendingResponseExceedSLA = 0;
        $countOpenPortalWithinSLA = 0;
        $countOpenPortalExceedSLA = 0;
        $countOpenPortalRespondWithinSLA = 0;

        $countTotalCasesfromEmail = 0;
        $countEmailAssignedWithinSLA = 0;
        $countEmailPendingResponseWithinSLA = 0;
        $countEmailPendingRespondTakenbyScheduler = 0;
        $countEmailAssignedExceedSLA = 0;
        $countEmailPendingResponseExceedSLA = 0;
        $countEmailWithinSLA = 0;
        $countEmailRespondWithinSLA = 0;
        $countEmailExceedSLA = 0;

        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered' >
                <thead>
                    <tr border='1'>                    
                    <th colspan='3'><center>Total cases for {$TodayDate}</td>
                    <th colspan='2' class='text-danger'><center>Exceed</center></td>
                    <th colspan='3'><center>Not Exceed</center></td>                    
                   </tr>
                    <tr border='1'>
                        <th><center>Contact Mode</center></th>
                        <th colspan='2'><center>Total Cases</center></th>
                        <th class='text-danger'><center>Assigned</center></th>
                        <th class='text-danger'><center>Pending Response(New Case)</center></th>
                        <th><center>Assigned</center></th>                        
                        <th><center>Pending Response(New Case)</center></th>
                        <th><center>Taken By Scheduler(New case)</center></th>                        
                    </tr>
                </thead>
                <tbody>";


        $processDate = Carbon::now()->format('Y-m-d');
        $listCase = self::crmService()->getDashboardCS(null,null,$processDate); 
        //  dump($listCase);
        if (count($listCase) > 0) {
            foreach ($listCase as $cases) {

                $processDate = Carbon::now()->toDateString();
                $csCompleted = Carbon::parse($cases->cs_completed_datetime)->addHour(8)->format("Y-m-d H:i:s");
                $csDue = Carbon::parse($cases->cs_due_datetime)->addHour(8)->format("Y-m-d H:i:s");
                $csPickup = Carbon::parse($cases->case_pickupdate)->addHour(8)->format("Y-m-d H:i:s");
                if ($cases->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                    if ($cases->cs_completed_datetime != null) {
                        if ($csCompleted <= $csDue) {
                            if ($cases->case_pickupdate == null && $cases->request_type == '' && $cases->case_info == '' && $cases->case_status == 'Open_Pending Input') {
                                $countOpenPortalPendingRespondTakenbyScheduler++;
                            }
                            $countOpenPortalAssignedWithinSLA++;
                        } else {
                            $countOpenPortalAssignedExceedSLA++;
                        }
                    }
                    if ($cases->cs_completed_datetime == null) {
                        if ($csDue > $current) {
                            $countOpenPortalPendingResponseWithinSLA++;
                        }
                        if ($csDue < $current) {
                            $countOpenPortalPendingResponseExceedSLA++;
                        }
                    }
                    $countTotalCasesfromOpenPortal++;
                    $countOpenPortalRespondWithinSLA = $countTotalCasesfromOpenPortal - ($countOpenPortalPendingResponseWithinSLA + $countOpenPortalPendingRespondTakenbyScheduler + $countOpenPortalAssignedExceedSLA + $countOpenPortalPendingResponseExceedSLA);
                    $countOpenPortalWithinSLA = $countTotalCasesfromOpenPortal - ($countOpenPortalPendingResponseExceedSLA + $countOpenPortalAssignedExceedSLA);
                    $countOpenPortalExceedSLA = $countTotalCasesfromOpenPortal - $countOpenPortalWithinSLA;
                }

                if ($cases->contact_mode == $CASE_CONTACT_MODE_EMAIL) {
                    if ($cases->cs_completed_datetime != null) {
                        if ($csCompleted <= $csDue) {
                            if ($csPickup == null && $cases->request_type == '' && $cases->case_info == '' && $cases->case_status == 'Open_Pending Input') {
                                $countEmailPendingRespondTakenbyScheduler++;
                            }
                            $countEmailAssignedWithinSLA++;
                        } else {
                            $countEmailAssignedExceedSLA++;
                        }
                    }
                    if ($cases->cs_completed_datetime == null) {
                        if ($csDue > $current) {
                            $countEmailPendingResponseWithinSLA++;
                        }
                        if ($csDue < $current) {
                            $countEmailPendingResponseExceedSLA++;
                        }
                    }
                    $countTotalCasesfromEmail++;
                    $countEmailRespondWithinSLA = $countTotalCasesfromEmail - ($countEmailPendingResponseWithinSLA + $countEmailPendingRespondTakenbyScheduler + $countEmailAssignedExceedSLA + $countEmailPendingResponseExceedSLA);
                    $countEmailWithinSLA = $countTotalCasesfromEmail - ($countEmailPendingResponseExceedSLA + $countEmailAssignedExceedSLA);
                    $countEmailExceedSLA = $countTotalCasesfromEmail - $countEmailWithinSLA;
                }
            }
        }

        $html .= "
            <tr>
                <td style='width: 10%;' align='center'><strong>Open Portal</strong></td>
                <td style='width: 10%;' align='center'><strong><a 
                            class='btn btn-green btn-sm shadow'>{$countOpenPortalWithinSLA}</a></strong></td>
                <td style='width: 10%;' align='center'><strong><a  
                            class='btn btn-secondary text-danger btn-sm font-weight-bold shadow'>{$countOpenPortalExceedSLA}</a></strong></td>
                <td style='width: 10%;' align='center'><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-secondary text-danger btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/openportal/responseexceedsla'
                            data-title='List of Cases Response > 15 Minute' ><strong>{$countOpenPortalAssignedExceedSLA}</strong></a></strong></td>                
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countOpenPortalPendingResponseExceedSLA)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/openportal/noresponseexceedsla'
                            data-title='List of Cases Pending Response > 15 Minute' ><strong>{$countOpenPortalPendingResponseExceedSLA}</strong></a></td>
                <td style='width: 10%;'align='center' ><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-secondary btn-sm shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/openportal/responsewithinsla'
                            data-title='List of Cases Response within 15 Minute' >{$countOpenPortalRespondWithinSLA}</a></strong></td>
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-yellow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/openportal/noresponsewithinsla'
                            data-title='List of Cases Pending Response within 15 Minute' >{$countOpenPortalPendingResponseWithinSLA}</a></strong></td>
                <td style='width: 20%;'align='center' ><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-yellow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/openportal/noresponsewithinslabyScheduler'
                            data-title='List of Pending Response Cases Taken by Scheduler' >{$countOpenPortalPendingRespondTakenbyScheduler}</a></strong></td>                      
            </tr>
            <tr>
                <td style='width: 10%;' align='center'><strong>Email</strong></td>
                <td style='width: 10%;' align='center'><strong><a 
                            class='btn btn-green btn-sm shadow'>{$countEmailWithinSLA}</a></strong></td>
                <td style='width: 10%;' align='center'><strong><a  
                            class='btn btn-secondary text-danger btn-sm font-weight-bold shadow'>{$countEmailExceedSLA}</a></strong></td>
                <td style='width: 10%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-secondary text-danger btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/email/responseexceedsla'
                            data-title='List of Cases Response > 15 Minute' >{$countEmailAssignedExceedSLA}</a></strong></td>               
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countEmailPendingResponseExceedSLA)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/email/noresponseexceedsla'
                            data-title='List of Cases Pending Response > 15 Minute' >{$countEmailPendingResponseExceedSLA}</a></strong></td>
                <td style='width: 10%;'align='center' ><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-secondary btn-sm shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/email/responsewithinsla'
                            data-title='List of Cases Response within 15 Minute' >{$countEmailRespondWithinSLA}</a></strong></td>
                <td style='width: 20%;'align='center' ><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-yellow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/email/noresponsewithinsla'
                            data-title='List of Cases Pending Response within 15 Minute' >{$countEmailPendingResponseWithinSLA}</a></strong></td>
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-yellow btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/totalcases/email/noresponsewithinslabyScheduler'
                            data-title='List of Pending Response Cases Taken by Scheduler' >{$countEmailPendingRespondTakenbyScheduler}</a></strong></td>                
            </tr>
           
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function listofCasesCreatedOpenPortal() {

        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $incidentType = $value->type_of_incident;
            if ($incidentType == 'service_business') {
                $typeOfIncident = 'Business Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'service_it') {
                $typeOfIncident = 'IT Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'incident_business') {
                $typeOfIncident = 'Business Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == 'incident_it') {
                $typeOfIncident = 'IT Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == '') {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {

                $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofCasesWithinCreatedOpenPortal() {

        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $current = Carbon::now();
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $incidentType = $value->type_of_incident;
            if ($incidentType == 'service_business') {
                $typeOfIncident = 'Business Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'service_it') {
                $typeOfIncident = 'IT Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'incident_business') {
                $typeOfIncident = 'Business Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == 'incident_it') {
                $typeOfIncident = 'IT Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == '') {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                if ($value->cs_completed_datetime != null) {
                    if ($value->cs_completed_datetime <= $value->cs_due_datetime) {
                        $caseId = $value->case_id;
                        $caseNum = $value->case_number;
                        $caseState = $value->case_state;
                        $caseSubStatus = $caseStatus;
                        $caseRequestType = $requestType;
                        $casetypeOfIncident = $typeOfIncident;
                        $caseslaStart = $slaStart;
                        $caseslaStop = $slaStop;
                    }
                } else {
                    if ($current <= $value->cs_due_datetime) {
                        $caseId = $value->case_id;
                        $caseNum = $value->case_number;
                        $caseState = $value->case_state;
                        $caseSubStatus = $caseStatus;
                        $caseRequestType = $requestType;
                        $casetypeOfIncident = $typeOfIncident;
                        $caseslaStart = $slaStart;
                        $caseslaStop = $slaStop;
                    }
                }
            }

            $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$caseId' title='View Detail Case in CRM'>$caseNum</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseState</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseSubStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseRequestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$casetypeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseslaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseslaStop</strong></td>
                    </tr>";
            $html = $html . $data;
            // }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofCasesExceedCreatedOpenPortal() {

        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $current = Carbon::now();
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $incidentType = $value->type_of_incident;
            if ($incidentType == 'service_business') {
                $typeOfIncident = 'Business Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'service_it') {
                $typeOfIncident = 'IT Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'incident_business') {
                $typeOfIncident = 'Business Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == 'incident_it') {
                $typeOfIncident = 'IT Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == '') {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $ID = $value->case_id;

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                if ($value->cs_completed_datetime != null) {
                    if ($value->cs_completed_datetime > $value->cs_due_datetime) {
                        $caseId = $ID;
                        $caseNum = $value->case_number;
                        $caseState = $value->case_state;
                        $caseSubStatus = $caseStatus;
                        $caseRequestType = $requestType;
                        $casetypeOfIncident = $typeOfIncident;
                        $caseslaStart = $slaStart;
                        $caseslaStop = $slaStop;
                    }
                } else {
                    if ($current > $value->cs_due_datetime) {
                        $caseId = $ID;
                        $caseNum = $value->case_number;
                        $caseState = $value->case_state;
                        $caseSubStatus = $caseStatus;
                        $caseRequestType = $requestType;
                        $casetypeOfIncident = $typeOfIncident;
                        $caseslaStart = $slaStart;
                        $caseslaStop = $slaStop;
                    }
                }
            }

            $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$caseId' title='View Detail Case in CRM'>$caseNum</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseState</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseSubStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseRequestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$casetypeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseslaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseslaStop</strong></td>
                    </tr>";
            $html = $html . $data;
            // }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofCasesCreatedEmail() {

        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $requestType = $value->request_type;
            if ($requestType == 'enquiry') {
                $typeOfIncident = '';
                $requestType = 'enquiry';
            }
            if ($requestType == 'service') {
                if ($value->type_of_incident == 'service_it') {
                    $typeOfIncident = 'IT Service';
                    $requestType = 'Service';
                }
                if ($value->type_of_incident == 'service_business') {
                    $typeOfIncident = 'Business Service';
                    $requestType = 'Service';
                }
            }
            if ($requestType == 'incident') {
                if ($value->type_of_incident == 'incident_it') {
                    $typeOfIncident = 'IT Incident';
                    $requestType = 'Incident';
                }
                if ($value->type_of_incident == 'incident_business') {
                    $typeOfIncident = 'Business Incident';
                    $requestType = 'Incident';
                }
            }
            if ($requestType == '' || $requestType == null) {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL) {

                $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_status</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofOpenPortalCasesResponseWithinSLA() {
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Information</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Respond By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $incidentType = $value->type_of_incident;
            if ($incidentType == 'service_business') {
                $typeOfIncident = 'Business Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'service_it') {
                $typeOfIncident = 'IT Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'incident_business') {
                $typeOfIncident = 'Business Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == 'incident_it') {
                $typeOfIncident = 'IT Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == '') {
                $typeOfIncident = '';
                $requestType = 'Enquiry';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $infoC = $value->case_info;
            if ($infoC == 'info_completed') {
                $caseInfoC = 'Complete';
            }
            if ($infoC == 'info_notcompleted') {
                $caseInfoC = 'Pending More Info';
            }
            if ($infoC == 'rejected') {
                $caseInfoC = 'Rejected';
            }
            if ($infoC == '') {
                $caseInfoC = '';
            }

            if ($value->csName == null || $value->csName == '') {
                $pickupBy = $value->csNama;
            } else {
                $pickupBy = $value->csName;
            }

            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL && $actualSLAEnd != null) {
                if ($actualSLAEnd <= $availableSLAEnd) {
                    if (($value->request_type != '' || $value->case_info != '') || ($value->case_status == 'Closed_Cancelled_Eaduan')) {

                        $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseInfoC</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;' class='text-left'><strong>$pickupBy</strong></td>
                    </tr>";
                        $html = $html . $data;
                    }
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofEmailCasesResponseWithinSLA() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Information</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Respond By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $requestType = $value->request_type;
            if ($requestType == 'enquiry') {
                $typeOfIncident = '';
                $requestType = 'enquiry';
            }
            if ($requestType == 'service') {
                if ($value->type_of_incident == 'service_it') {
                    $typeOfIncident = 'IT Service';
                    $requestType = 'Service';
                }
                if ($value->type_of_incident == 'service_business') {
                    $typeOfIncident = 'Business Service';
                    $requestType = 'Service';
                }
            }
            if ($requestType == 'incident') {
                if ($value->type_of_incident == 'incident_it') {
                    $typeOfIncident = 'IT Incident';
                    $requestType = 'Incident';
                }
                if ($value->type_of_incident == 'incident_business') {
                    $typeOfIncident = 'Business Incident';
                    $requestType = 'Incident';
                }
            }
            if ($requestType == '' || $requestType == null) {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $infoC = $value->case_info;
            if ($infoC == 'info_completed') {
                $caseInfoC = 'Complete';
            }
            if ($infoC == 'info_notcompleted') {
                $caseInfoC = 'Pending More Info';
            }
            if ($infoC == 'rejected') {
                $caseInfoC = 'Rejected';
            }
            if ($infoC == '') {
                $caseInfoC = '';
            }

            if ($value->csName == null || $value->csName == '') {
                $pickupBy = $value->csNama;
            } else {
                $pickupBy = $value->csName;
            }


            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL && $actualSLAEnd != null) {
                if ($actualSLAEnd <= $availableSLAEnd) {
                    if ($value->request_type != '' || $value->case_info != '') {

                        $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseInfoC</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;' class='text-left'><strong>$pickupBy</strong></td>
                    </tr>";
                        $html = $html . $data;
                    }
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofOpenPortalCasesNoResponseWithinSLA() {
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Remaining Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '< ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL && $actualSLAEnd == null) {
                if ($availableSLAEnd > $current) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofEmailCasesNoResponseWithinSLA() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Remaining Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $requestType = $value->request_type;
            if ($requestType == 'enquiry') {
                $typeOfIncident = '';
                $requestType = 'enquiry';
            }
            if ($requestType == 'service') {
                if ($value->type_of_incident == 'service_it') {
                    $typeOfIncident = 'IT Service';
                    $requestType = 'Service';
                }
                if ($value->type_of_incident == 'service_business') {
                    $typeOfIncident = 'Business Service';
                    $requestType = 'Service';
                }
            }
            if ($requestType == 'incident') {
                if ($value->type_of_incident == 'incident_it') {
                    $typeOfIncident = 'IT Incident';
                    $requestType = 'Incident';
                }
                if ($value->type_of_incident == 'incident_business') {
                    $typeOfIncident = 'Business Incident';
                    $requestType = 'Incident';
                }
            }
            if ($requestType == '' || $requestType == null) {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $infoC = $value->case_info;
            if ($infoC == 'info_completed') {
                $caseInfoC = 'Complete';
            }
            if ($infoC == 'info_notcompleted') {
                $caseInfoC = 'Pending More Info';
            }
            if ($infoC == 'rejected') {
                $caseInfoC = 'Rejected';
            }
            if ($infoC == '') {
                $caseInfoC = '';
            }

            if ($value->csName == null || $value->csName == '') {
                $pickupBy = $value->csNama;
            } else {
                $pickupBy = $value->csName;
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '< ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL && $actualSLAEnd == null) {
                if ($availableSLAEnd > $current) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofOpenPortalCasesResponseExceedSLA() {
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Information</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Respond By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $incidentType = $value->type_of_incident;
            if ($incidentType == 'service_business') {
                $typeOfIncident = 'Business Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'service_it') {
                $typeOfIncident = 'IT Service';
                $requestType = 'Service';
            }
            if ($incidentType == 'incident_business') {
                $typeOfIncident = 'Business Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == 'incident_it') {
                $typeOfIncident = 'IT Incident';
                $requestType = 'Incident';
            }
            if ($incidentType == '') {
                $typeOfIncident = '';
                $requestType = 'Enquiry';
            }
            if ($value->case_status == 'Closed_Cancelled_Eaduan' && ($value->request_type == null || $value->request_type == '')) {
                $typeOfIncident = '';
                $requestType = '';
                $respondBy = 'eAduan User';
            } else {
                $respondBy = $value->csName;
            }


            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $infoC = $value->case_info;
            if ($infoC == 'info_completed') {
                $caseInfoC = 'Complete';
            }
            if ($infoC == 'info_notcompleted') {
                $caseInfoC = 'Pending More Info';
            }
            if ($infoC == 'rejected') {
                $caseInfoC = 'Rejected';
            }
            if ($infoC == '') {
                $caseInfoC = '';
            }

            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;
            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL && $actualSLAEnd != null) {
                if ($actualSLAEnd > $availableSLAEnd) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseInfoC</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;' class='text-left'><strong>$respondBy</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofEmailCasesResponseExceedSLA() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>Information</th>
                            <th class='text-center'>Request Type</th>
                            <th class='text-center'>Incident or Service Type</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Respond By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $requestType = $value->request_type;
            if ($requestType == 'enquiry') {
                $typeOfIncident = '';
                $requestType = 'enquiry';
            }
            if ($requestType == 'service') {
                if ($value->type_of_incident == 'service_it') {
                    $typeOfIncident = 'IT Service';
                    $requestType = 'Service';
                }
                if ($value->type_of_incident == 'service_business') {
                    $typeOfIncident = 'Business Service';
                    $requestType = 'Service';
                }
            }
            if ($requestType == 'incident') {
                if ($value->type_of_incident == 'incident_it') {
                    $typeOfIncident = 'IT Incident';
                    $requestType = 'Incident';
                }
                if ($value->type_of_incident == 'incident_business') {
                    $typeOfIncident = 'Business Incident';
                    $requestType = 'Incident';
                }
            }
            if ($requestType == '' || $requestType == null) {
                $typeOfIncident = '';
                $requestType = '';
            }

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $infoC = $value->case_info;
            if ($infoC == 'info_completed') {
                $caseInfoC = 'Complete';
            }
            if ($infoC == 'info_notcompleted') {
                $caseInfoC = 'Pending More Info';
            }
            if ($infoC == 'rejected') {
                $caseInfoC = 'Rejected';
            }
            if ($infoC == '') {
                $caseInfoC = '';
            }

            if ($value->csName == null) {
                $respondBy = $value->csName;
            }
            $respondBy = $value->csNama;


            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;
            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL && $actualSLAEnd != null) {
                if ($actualSLAEnd > $availableSLAEnd) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseInfoC</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$requestType</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$typeOfIncident</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;' class='text-left'><strong>$respondBy</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofOpenPortalCasesNoResponseExceedSLA() {
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceeded</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL && $actualSLAEnd == null) {
                if ($availableSLAEnd < $current) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;color:red;' class='text-left' ><strong>$timeRemaining</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofEmailCasesNoResponseExceedSLA() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceeded</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($availableSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL && $actualSLAEnd == null) {
                if ($availableSLAEnd < $current) {

                    $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td>
                        <td style='width: 5%;color:red;' class='text-left' ><strong>$timeRemaining</strong></td>
                    </tr>";
                    $html = $html . $data;
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofOpenPortalCasesNoResponseWithinSLAByScheduler() {
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Scheduler Action</th>
                            <th class='text-center'>Time Running with no action</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
            $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Closed_Cancelled_Eaduan') {
                $caseStatus = 'Cancelled by eAduan';
            }
            if ($subStatus == 'Closed_Verified_Eaduan') {
                $caseStatus = 'Closed by eAduan';
            }
            if ($subStatus == 'Closed_Rejected_Eaduan') {
                $caseStatus = 'Rejected by eAduan';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($actualSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL && $actualSLAEnd != null) {
                if ($availableSLAEnd > $actualSLAEnd) {
                    if ($value->case_pickupdate == null && $value->request_type == '' && $value->case_info == '' && $value->case_status == 'Open_Pending Input') {

                        $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td> 
                            <td style='width: 10%;' class='text-left'><strong>$slaStopbyScheduler</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                        $html = $html . $data;
                    }
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listofEmailCasesNoResponseWithinSLAByScheduler() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardCS();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Scheduler Action</th>
                            <th class='text-center'>Time Running with no action</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
            $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $availableSLAEnd = $value->cs_due_datetime;
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($actualSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL && $actualSLAEnd != null) {
                if ($availableSLAEnd > $actualSLAEnd) {
                    if ($value->case_pickupdate == null && $value->request_type == '' && $value->case_info == '' && $value->case_status == 'Open_Pending Input') {

                        $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td> 
                            <td style='width: 10%;' class='text-left'><strong>$slaStopbyScheduler</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                        $html = $html . $data;
                    }
                }
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function dashboardTopEnqSubCategory() {
        $i = 0;
        $listCase = self::crmService()->getDashboardTopEnqSubCategory();
        // dump($listCase);
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>                    
                    <tr border='1'>
                        <th><center>No.</center></th>
                        <th><center>Sub Category</center></th>
                        <th><center>Sub Category 2</center></th>
                        <th><center>Total Cases</center></th> 
                        <th><center>Download</center></th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($listCase as $data) {
            $i++;
            $html .= "
            <tr>
                <td style='width: 10%;'><center><strong>{$i}</strong></center></td>
                <td style='width: 30%;'><strong>$data->subCategory</strong></td>
                <td style='width: 40%;'><strong>$data->subCategory2</strong></td>
                <td style='width: 20%;'><font color='red'><center><strong>$data->totalCases</strong></center></td>
                <td><a href='" . url('/dashboard/top-enq/download') . "/" . $data->subCatCode . "/" . $data->subCatCode2 . "' target='_blank' id='downloadExcel' class='btn btn btn-default pull-right'  style='margin:3px;'>Download to Excel</a></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    // download
    public function downloadTopEnq($subCat, $subCat2) {

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'Top10_Enquiry_' . $dateNow;
        $list = self::crmService()->getDetailsTopEnqSubCategory($subCat, $subCat2);
        Excel::create($fileName, function($excel)use($list) {
            $excel->setTitle('List of Top 10 Enquiry');

            $excel->sheet('TopEnquiry', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));

                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => '@',
                    'D' => '@',
                    'E' => '@',
                    'F' => '@',
                ));

                $sheet->row(1, array(
                    'CRM CASE NO', 'DATE CREATED', 'CONTACT MODE', 'REQUEST TYPE', 'SUB CATEGORY', 'SUB CATEGORY 2'
                ));
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:F1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });

                $count = 2;

                foreach ($list as $obj) {

                    $dateCaseCreated = Carbon::parse($obj->caseCreated)->addHour(8)->format("Y-m-d H:i");

                    $sheet->row($count, array(
                        $obj->caseNum,
                        $dateCaseCreated,
                        $obj->contactMode,
                        $obj->requestType,
                        $obj->subCategory,
                        $obj->subCategory2,
                            )
                    );
                    $count++;
                }
            });
        })->store('xlsx', storage_path('app/Report'));

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/Report/' . $fileName . '.xlsx');
        return response()->download($fullPath, $fileName . '.xlsx', $headers);
    }

    public function dashboardTopIncServSubCategory() {
        $i = 0;
        $listCase = self::crmService()->getDashboardTopIncServSubCategory();
        // dump($listCase);
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>                    
                    <tr border='1'>
                        <th><center>No.</center></th>
                        <th><center>Sub Category</center></th>
                        <th><center>Sub Category 2</center></th>
                        <th><center>Total Cases</center></th>    
                        <th><center>Download</center></th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($listCase as $data) {
            $i++;
            $html .= "
            <tr>
                <td style='width: 10%;'><center><strong>{$i}</strong></center></td>
                <td style='width: 30%;'><strong>$data->subCategory</strong></td>
                <td style='width: 40%;'><strong>$data->subCategory2</strong></td>
                <td style='width: 20%;'><font color='red'><center><strong>$data->totalCases</strong></center></td>
                <td><a href='" . url('/dashboard/top-incserv/download') . "/" . $data->subCatCode . "/" . $data->subCatCode2 . "' target='_blank' id='downloadExcel' class='btn btn btn-default pull-right'  style='margin:3px;'>Download to Excel</a></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    // download
    public function downloadTopIncServ($subCat, $subCat2) {

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'Top10_Incident_Service_' . $dateNow;
        $list = self::crmService()->getDetailsTopIncServSubCategory($subCat, $subCat2);
        Excel::create($fileName, function($excel)use($list) {
            $excel->setTitle('List of Top 10 Incident Service');

            $excel->sheet('TopIncServ', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));

                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => '@',
                    'D' => '@',
                    'E' => '@',
                    'F' => '@',
                ));

                $sheet->row(1, array(
                    'CRM CASE NO', 'DATE CREATED', 'CONTACT MODE', 'REQUEST TYPE', 'SUB CATEGORY', 'SUB CATEGORY 2'
                ));
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:F1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });

                $count = 2;

                foreach ($list as $obj) {

                    $dateCaseCreated = Carbon::parse($obj->caseCreated)->addHour(8)->format("Y-m-d H:i");

                    $sheet->row($count, array(
                        $obj->caseNum,
                        $dateCaseCreated,
                        $obj->contactMode,
                        $obj->requestType,
                        $obj->subCategory,
                        $obj->subCategory2,
                            )
                    );
                    $count++;
                }
            });
        })->store('xlsx', storage_path('app/Report'));

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/Report/' . $fileName . '.xlsx');
        return response()->download($fullPath, $fileName . '.xlsx', $headers);
    }

    public function dashboardPendingResponse() {

        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $CASE_CONTACT_MODE_OPEN_PORTAL = 'Open Portal';
        $countPendingResponseCSOpenPortal = 0;
        $countPendingResponseCSEmail = 0;
        $colorButton = "modal-list-data-action btn-sm shadow";
        $html = "";
        $listCase = self::crmService()->getDashboardPendingResponse();
        //  dump($listCase);
        if (count($listCase) > 0) {
            foreach ($listCase as $cases) {

                if ($cases->contact_mode == $CASE_CONTACT_MODE_OPEN_PORTAL) {
                    $countPendingResponseCSOpenPortal++;
                    $colorButton = "modal-list-data-action btn buttonblink btn-sm shadow";
                }

                if ($cases->contact_mode == $CASE_CONTACT_MODE_EMAIL) {
                    $countPendingResponseCSEmail++;
                    $colorButton = "modal-list-data-action btn buttonblink btn-sm shadow";
                }
            }
        }
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered' >
                <thead>
                    <tr border='1'>                    
                    <th colspan='2'><center>Total cases Pending Response</th>                   
                   </tr>
                    <tr border='1'>
                        <th><center>Contact Mode</center></th>
                        <th><center>Total Cases</center></th>                    
                    </tr>
                </thead>
                <tbody>          
            <tr>
                <td style='width: 10%;' align='center'><strong>Open Portal</strong></td>
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                    class='$colorButton' data-toggle='modal' 
                    data-url='/list/crm/totalcases/openportal/backdatedpendingresponse' 
                    data-title='List of Cases Pending Response - Open Portal' >{$countPendingResponseCSOpenPortal}</a></strong></td> 
            </tr> 
            <tr> 
                <td style='width: 10%;' align='center'><strong>Email</strong></td>
                <td style='width: 20%;' align='center'><strong><a href='#modal-list-data' 
                    class='$colorButton' data-toggle='modal' 
                    data-url='/list/crm/totalcases/email/backdatedpendingresponse' 
                    data-title='List of Cases Pending Response - Email' >{$countPendingResponseCSEmail}</a></strong></td>  
            </tr>
           
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function backdatedPendingResponseOP() {
        $CASE_CONTACT_MODE_OP = 'Open Portal';
        $list = self::crmService()->getDashboardPendingResponse();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Scheduler Action</th>
                            <th class='text-center'>Time Running with no action</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
            $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($actualSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_OP) {

                $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td> 
                            <td style='width: 10%;' class='text-left'><strong>$slaStopbyScheduler</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function backdatedPendingResponseEmail() {
        $CASE_CONTACT_MODE_EMAIL = 'Email';
        $list = self::crmService()->getDashboardPendingResponse();
        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Sub Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Scheduler Action</th>
                            <th class='text-center'>Time Running with no action</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $slaStart = Carbon::parse($value->cs_start_datetime)->format("h:i A");
            $slaStop = Carbon::parse($value->cs_due_datetime)->format("h:i A");
            $slaStopbyScheduler = Carbon::parse($value->cs_completed_datetime)->format("h:i A");

            $subStatus = $value->case_status;
            if ($subStatus == 'Open_Pending Input') {
                $caseStatus = 'Pending Input';
            }
            if ($subStatus == 'Closed_Closed') {
                $caseStatus = 'Closed';
            }
            if ($subStatus == 'Open_Assigned') {
                $caseStatus = 'Assigned';
            }
            if ($subStatus == 'Pending_User_Verification') {
                $caseStatus = 'Pending User Verification';
            }
            if ($subStatus == 'Closed_Rejected') {
                $caseStatus = 'Rejected';
            }
            if ($subStatus == 'Open_New') {
                $caseStatus = 'New';
            }
            if ($subStatus == 'Closed_Duplicate') {
                $caseStatus = 'Duplicate';
            }
            if ($subStatus == 'Open_Pending_Approval') {
                $caseStatus = 'Pending Approval';
            }
            if ($subStatus == 'Closed_Approved') {
                $caseStatus = 'Approved';
            }
            if ($subStatus == 'In_Progress') {
                $caseStatus = 'In Progress';
            }
            if ($subStatus == 'Open_Resolved') {
                $caseStatus = 'Resolved';
            }

            $current = Carbon::now();
            $actualSLAEnd = $value->cs_completed_datetime;

            $slaEnd = Carbon::parse($actualSLAEnd)->format("Y-m-d H:i:s");
            $dateDiff = $current->diff(new DateTime($slaEnd));
            $timeRemaining = '> ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($value->contact_mode == $CASE_CONTACT_MODE_EMAIL) {

                $data = "
                    <tr>
                        <td style='width: 5%;' class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td style='width: 10%;' class='text-left'><strong><a target='_blank' href='http://192.168.120.205/crm/index.php?module=Cases&action=DetailView&record=$value->case_id' title='View Detail Case in CRM'>$value->case_number</a></strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$value->case_state</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$caseStatus</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStart</strong></td>
                        <td style='width: 10%;' class='text-left'><strong>$slaStop</strong></td> 
                            <td style='width: 10%;' class='text-left'><strong>$slaStopbyScheduler</strong></td>
                        <td style='width: 5%;color:green;' class='text-left'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function setDisplayBtnClass($count) {

        if ($count > 0) {
            return 'btn-red';
        }

        return 'btn-secondary';
    }

    public function setDisplayBtnWarningClass($count) {

        if ($count > 0) {
            return 'btn-yellow';
        }

        return 'btn-secondary';
    }

    public function setDisplayBtnCompleteClass($count) {

        if ($count > 0) {
            return 'btn-green';
        }

        return 'btn-secondary';
    }

    public function setDisplayBtnTotalClass($count) {

        if ($count > 0) {
            return 'btn-secondary';
        }

        return 'btn-secondary';
    }

    public function dashboardCRMIncidentcaseAgeing() {
        $countGovNormalSeverityItIncidentPending = 0;
        $countGovNormalSeverityItIncidentResolve = 0;
        $countGovNormalSeverityItIncidentClosed = 0;

        $countGovApproverSeverityItIncidentPending = 0;
        $countGovApproverSeverityItIncidentResolve = 0;
        $countGovApproverSeverityItIncidentClosed = 0;

        $countSuppNormalSeverityItIncidentPending = 0;
        $countSuppNormalSeverityItIncidentResolve = 0;
        $countSuppNormalItIncidentClosed = 0;

        $countSuppApproverSeverityItIncidentPending = 0;
        $countSuppApproverSeverityItIncidentResolve = 0;
        $countSuppApproverItIncidentClosed = 0;

        $NORMALSEVERITY = 'CRM Cases';
        $SEVERITYAPPROVER = 'Redmine Recorded in CRM';

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr border='1'> 
                       <th colspan='2'><center></th>
                        <th colspan='4'><center>Cases Status</th>
                     </tr>
                     <tr border='1'> 
                        <th rowspan='2'>Incidents Raised By</th>
                        <th rowspan='2'>CRM Cases/Redmine</th>
                        <th>Pending Resolution</th>
                        <th>Resolved/Pending User Verification</th>
                        <th>Closed</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasksNormalPending = self::crmService()->getDashboardCRMITIncidentNormalPendingSummary();
        $listTasksApproverPending = self::crmService()->getDashboardCRMITIncidentApproverPendingSummary();
        $listTasksNormalResolved = self::crmService()->getDashboardCRMITIncidentNormalResolvedSummary();
        $listTasksApproverResolved = self::crmService()->getDashboardCRMITIncidentApproverResolvedSummary();
        $listTasksNormalClosed = self::crmService()->getDashboardCRMITIncidentNormalClosedSummary();
        $listTasksApproverClosed = self::crmService()->getDashboardCRMITIncidentApproverClosedSummary();

        if (count($listTasksNormalPending) > 0) {
            $pageNos = array();
            foreach ($listTasksNormalPending as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentPending++;
                    }
                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentPending++;
                    }
                }
            }
        }

        if (count($listTasksApproverPending) > 0) {
            $pageNos = array();
            foreach ($listTasksApproverPending as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentPending++;
                    }
                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentPending++;
                    }
                }
            }
        }

        if (count($listTasksNormalResolved) > 0) {
            $pageNos = array();
            foreach ($listTasksNormalResolved as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentResolve++;
                    }

                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentResolve++;
                    }
                }
            }
        }

        if (count($listTasksApproverResolved) > 0) {
            $pageNos = array();
            foreach ($listTasksApproverResolved as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentResolve++;
                    }

                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentResolve++;
                    }
                }
            }
        }

        if (count($listTasksNormalClosed) > 0) {
            $pageNos = array();
            foreach ($listTasksNormalClosed as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentClosed++;
                    }

                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppNormalItIncidentClosed++;
                    }
                }
            }
        }

        if (count($listTasksApproverClosed) > 0) {
            $pageNos = array();
            foreach ($listTasksApproverClosed as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT') {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentClosed++;
                    }

                    if ($tasks->accType == 'SUPPLIER') {
                        array_push($pageNos, $pageno);
                        $countSuppApproverItIncidentClosed++;
                    }
                }
            }
        }

        $totalGovNormalSeverity = $countGovNormalSeverityItIncidentPending + $countGovNormalSeverityItIncidentResolve + $countGovNormalSeverityItIncidentClosed;
        $totalGovApproverSeverity = $countGovApproverSeverityItIncidentPending + $countGovApproverSeverityItIncidentResolve + $countGovApproverSeverityItIncidentClosed;
        $totalSuppNormalSeverity = $countSuppNormalSeverityItIncidentPending + $countSuppNormalSeverityItIncidentResolve + $countSuppNormalItIncidentClosed;
        $totalSuppApproverSeverity = $countSuppApproverSeverityItIncidentPending + $countSuppApproverSeverityItIncidentResolve + $countSuppApproverItIncidentClosed;

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Government</strong></td>
                <td style='width: 20%;'><strong>{$NORMALSEVERITY}</strong></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnClass($countGovNormalSeverityItIncidentPending)}'><strong>{$countGovNormalSeverityItIncidentPending}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnWarningClass($countGovNormalSeverityItIncidentResolve)}'><strong>{$countGovNormalSeverityItIncidentResolve}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnCompleteClass($countGovNormalSeverityItIncidentClosed)}'><strong>{$countGovNormalSeverityItIncidentClosed}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalGovNormalSeverity)}'><strong>{$totalGovNormalSeverity}</strong></button></td>                
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$SEVERITYAPPROVER}</strong></td>  
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnClass($countGovApproverSeverityItIncidentPending)}'><strong>{$countGovApproverSeverityItIncidentPending}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnWarningClass($countGovApproverSeverityItIncidentResolve)}'><strong>{$countGovApproverSeverityItIncidentResolve}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnCompleteClass($countGovApproverSeverityItIncidentClosed)}'><strong>{$countGovApproverSeverityItIncidentClosed}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalGovApproverSeverity)}'><strong>{$totalGovApproverSeverity}</strong></button></td>                 
            </tr>
            
             <td rowspan='2' style='width: 20%;'><strong>Supplier</strong></td>
                <td style='width: 20%;'><strong>{$NORMALSEVERITY}</strong></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnClass($countSuppNormalSeverityItIncidentPending)}'><strong>{$countSuppNormalSeverityItIncidentPending}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnWarningClass($countSuppNormalSeverityItIncidentResolve)}'><strong>{$countSuppNormalSeverityItIncidentResolve}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnCompleteClass($countSuppNormalItIncidentClosed)}'><strong>{$countSuppNormalItIncidentClosed}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalSuppNormalSeverity)}'><strong>{$totalSuppNormalSeverity}</strong></button></td>                   
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$SEVERITYAPPROVER}</strong></td>  
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnClass($countSuppApproverSeverityItIncidentPending)}'><strong>{$countSuppApproverSeverityItIncidentPending}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnWarningClass($countSuppApproverSeverityItIncidentResolve)}'><strong>{$countSuppApproverSeverityItIncidentResolve}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnCompleteClass($countSuppApproverItIncidentClosed)}'><strong>{$countSuppApproverItIncidentClosed}</strong></button></td>
                <td style='width: 15%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalSuppApproverSeverity)}'><strong>{$totalSuppApproverSeverity}</strong></button></td>
                
            </tr>
           
        
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function dashboardCRMIncidentcaseSummaryAgeing() {
        $countGovNormalSeverityItIncidentPendingLessThan3Days = 0;
        $countGovNormalSeverityItIncidentResolveLessThan3Days = 0;
        $countGovNormalSeverityItIncidentPending4To7Days = 0;
        $countGovNormalSeverityItIncidentResolve4To7Days = 0;
        $countGovNormalSeverityItIncidentPendingMoreThan7Days = 0;
        $countGovNormalSeverityItIncidentResolveMoreThan7Days = 0;

        $countGovApproverSeverityItIncidentPendingLessThan3Days = 0;
        $countGovApproverSeverityItIncidentResolveLessThan3Days = 0;
        $countGovApproverSeverityItIncidentPending4To7Days = 0;
        $countGovApproverSeverityItIncidentResolve4To7Days = 0;
        $countGovApproverSeverityItIncidentPendingMoreThan7Days = 0;
        $countGovApproverSeverityItIncidentResolveMoreThan7Days = 0;

        $countSuppNormalSeverityItIncidentPendingLessThan3Days = 0;
        $countSuppNormalSeverityItIncidentResolveLessThan3Days = 0;
        $countSuppNormalSeverityItIncidentPending4To7Days = 0;
        $countSuppNormalSeverityItIncidentResolve4To7Days = 0;
        $countSuppNormalSeverityItIncidentPendingMoreThan7Days = 0;
        $countSuppNormalSeverityItIncidentResolveMoreThan7Days = 0;

        $countSuppApproverSeverityItIncidentPendingLessThan3Days = 0;
        $countSuppApproverSeverityItIncidentResolveLessThan3Days = 0;
        $countSuppApproverSeverityItIncidentPending4To7Days = 0;
        $countSuppApproverSeverityItIncidentResolve4To7Days = 0;
        $countSuppApproverSeverityItIncidentPendingMoreThan7Days = 0;
        $countSuppApproverSeverityItIncidentResolveMoreThan7Days = 0;



        $NORMALSEVERITY = 'CRM Cases';
        $SEVERITYAPPROVER = 'Redmine Recorded in CRM';

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr border='1'> 
                       <th rowspan='2'><center>Incidents Raised By</center></th>
                       <th rowspan='2'><center>CRM Cases/Redmine</center></th>
                       <th colspan='2'><center>Less Than 3 days</center></th>
                       <th colspan='2'><center>4 - 7 days</center></th>
                       <th colspan='2'><center>More Than 7 days</center></th>
                       <th colspan='2'><center>Total</center></th>
                     </tr>
                     <tr border='1'> 
                       <th><center>Pending Resolution</center></th>
                       <th><center>Resolved & Pending User Verification</center></th>
                       <th><center>Pending Resolution</center></th>
                       <th><center>Resolved & Pending User Verification</center></th>
                       <th><center>Pending Resolution</center></th>
                       <th><center>Resolved & Pending User Verification</center></th>
                       <th><center>Pending Resolution</center></th>
                       <th><center>Resolved & Pending User Verification</center></th>
                    </tr>
                </thead>
                <tbody>";

        $listTasksNormalPendingAgeing = self::crmService()->getDashboardCRMITIncidentNormalPendingAgeingSummary();
        $listTasksApproverPendingAgeing = self::crmService()->getDashboardCRMITIncidentApproverPendingAgeingSummary();
        $listTasksNormalResolveAgeing = self::crmService()->getDashboardCRMITIncidentNormalResolveAgeingSummary();
        $listTasksApproverResolveAgeing = self::crmService()->getDashboardCRMITIncidentApproverResolveAgeingSummary();

        if (count($listTasksNormalPendingAgeing) > 0) {
            $pageNos = array();
            foreach ($listTasksNormalPendingAgeing as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentPendingLessThan3Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentPendingLessThan3Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentPending4To7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentPending4To7Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentPendingMoreThan7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentPendingMoreThan7Days++;
                    }
                }
            }
        }

        if (count($listTasksApproverPendingAgeing) > 0) {
            $pageNos = array();
            foreach ($listTasksApproverPendingAgeing as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentPendingLessThan3Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentPendingLessThan3Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentPending4To7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentPending4To7Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentPendingMoreThan7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentPendingMoreThan7Days++;
                    }
                }
            }
        }

        if (count($listTasksNormalResolveAgeing) > 0) {
            $pageNos = array();
            foreach ($listTasksNormalResolveAgeing as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentResolveLessThan3Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentResolveLessThan3Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentResolve4To7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentResolve4To7Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countGovNormalSeverityItIncidentResolveMoreThan7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countSuppNormalSeverityItIncidentResolveMoreThan7Days++;
                    }
                }
            }
        }

        if (count($listTasksApproverResolveAgeing) > 0) {
            $pageNos = array();
            foreach ($listTasksApproverResolveAgeing as $tasks) {
                $pageno = $tasks->caseNum;
                if (!in_array($pageno, $pageNos)) {
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentResolveLessThan3Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing <= 3) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentResolveLessThan3Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentResolve4To7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && ($tasks->ageing >= 4 && $tasks->ageing <= 7)) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentResolve4To7Days++;
                    }
                    if ($tasks->accType == 'GOVERNMENT' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countGovApproverSeverityItIncidentResolveMoreThan7Days++;
                    }
                    if ($tasks->accType == 'SUPPLIER' && $tasks->ageing > 7) {
                        array_push($pageNos, $pageno);
                        $countSuppApproverSeverityItIncidentResolveMoreThan7Days++;
                    }
                }
            }
        }

        $totalPendingGov = $countGovNormalSeverityItIncidentPendingLessThan3Days + $countGovNormalSeverityItIncidentPending4To7Days + $countGovNormalSeverityItIncidentPendingMoreThan7Days;
        $totalResolveGov = $countGovNormalSeverityItIncidentResolveLessThan3Days + $countGovNormalSeverityItIncidentResolve4To7Days + $countGovNormalSeverityItIncidentResolveMoreThan7Days;

        $totalPendingApproverGov = $countGovApproverSeverityItIncidentPendingLessThan3Days + $countGovApproverSeverityItIncidentPending4To7Days + $countGovApproverSeverityItIncidentPendingMoreThan7Days;
        $totalResolveApproverGov = $countGovApproverSeverityItIncidentResolveLessThan3Days + $countGovApproverSeverityItIncidentResolve4To7Days + $countGovApproverSeverityItIncidentResolveMoreThan7Days;

        $totalPendingSupp = $countSuppNormalSeverityItIncidentPendingLessThan3Days + $countSuppNormalSeverityItIncidentPending4To7Days + $countSuppNormalSeverityItIncidentPendingMoreThan7Days;
        $totalResolveSupp = $countSuppNormalSeverityItIncidentResolveLessThan3Days + $countSuppNormalSeverityItIncidentResolve4To7Days + $countSuppNormalSeverityItIncidentResolveMoreThan7Days;

        $totalPendingApproverSupp = $countSuppApproverSeverityItIncidentPendingLessThan3Days + $countSuppApproverSeverityItIncidentPending4To7Days + $countSuppApproverSeverityItIncidentPendingMoreThan7Days;
        $totalResolveApproverSupp = $countSuppApproverSeverityItIncidentResolveLessThan3Days + $countSuppApproverSeverityItIncidentResolve4To7Days + $countSuppApproverSeverityItIncidentResolveMoreThan7Days;

        $html .= "
            <tr>
                <td rowspan='2' style='width: 10%;'><strong>Government</strong></td>
                <td style='width: 10%;'><strong>{$NORMALSEVERITY}</strong></td>                    
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentPendingLessThan3Days)}'><strong>{$countGovNormalSeverityItIncidentPendingLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentResolveLessThan3Days)}'><strong>{$countGovNormalSeverityItIncidentResolveLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentPending4To7Days)}'><strong>{$countGovNormalSeverityItIncidentPending4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentResolve4To7Days)}'><strong>{$countGovNormalSeverityItIncidentResolve4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentPendingMoreThan7Days)}'><strong>{$countGovNormalSeverityItIncidentPendingMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovNormalSeverityItIncidentResolveMoreThan7Days)}'><strong>{$countGovNormalSeverityItIncidentResolveMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalPendingGov)}'><strong>{$totalPendingGov}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalResolveGov)}'><strong>{$totalResolveGov}</strong></button></td>               
            </tr>
             <tr>
                <td style='width: 10%;'><strong>{$SEVERITYAPPROVER}</strong></td>   
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentPendingLessThan3Days)}'><strong>{$countGovApproverSeverityItIncidentPendingLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentResolveLessThan3Days)}'><strong>{$countGovApproverSeverityItIncidentResolveLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentPending4To7Days)}'><strong>{$countGovApproverSeverityItIncidentPending4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentResolve4To7Days)}'><strong>{$countGovApproverSeverityItIncidentResolve4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentPendingMoreThan7Days)}'><strong>{$countGovApproverSeverityItIncidentPendingMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countGovApproverSeverityItIncidentResolveMoreThan7Days)}'><strong>{$countGovApproverSeverityItIncidentResolveMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalPendingApproverGov)}'><strong>{$totalPendingApproverGov}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalResolveApproverGov)}'><strong>{$totalResolveApproverGov}</strong></button></td>                  
            </tr>
            
             <td rowspan='2' style='width: 10%;'><strong>Supplier</strong></td>
                <td style='width: 10%;'><strong>{$NORMALSEVERITY}</strong></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentPendingLessThan3Days)}'><strong>{$countSuppNormalSeverityItIncidentPendingLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentResolveLessThan3Days)}'><strong>{$countSuppNormalSeverityItIncidentResolveLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentPending4To7Days)}'><strong>{$countSuppNormalSeverityItIncidentPending4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentResolve4To7Days)}'><strong>{$countSuppNormalSeverityItIncidentResolve4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentPendingMoreThan7Days)}'><strong>{$countSuppNormalSeverityItIncidentPendingMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppNormalSeverityItIncidentResolveMoreThan7Days)}'><strong>{$countSuppNormalSeverityItIncidentResolveMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalPendingSupp)}'><strong>{$totalPendingSupp}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalResolveSupp)}'><strong>{$totalResolveSupp}</strong></button></td>                
            </tr>
             <tr>
                <td style='width: 10%;'><strong>{$SEVERITYAPPROVER}</strong></td>  
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentPendingLessThan3Days)}'><strong>{$countSuppApproverSeverityItIncidentPendingLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentResolveLessThan3Days)}'><strong>{$countSuppApproverSeverityItIncidentResolveLessThan3Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentPending4To7Days)}'><strong>{$countSuppApproverSeverityItIncidentPending4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentResolve4To7Days)}'><strong>{$countSuppApproverSeverityItIncidentResolve4To7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentPendingMoreThan7Days)}'><strong>{$countSuppApproverSeverityItIncidentPendingMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($countSuppApproverSeverityItIncidentResolveMoreThan7Days)}'><strong>{$countSuppApproverSeverityItIncidentResolveMoreThan7Days}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalPendingApproverSupp)}'><strong>{$totalPendingApproverSupp}</strong></button></td>
                <td style='width: 10%;'><button class = 'btn btn-sm {$this->setDisplayBtnTotalClass($totalResolveApproverSupp)}'><strong>{$totalResolveApproverSupp}</strong></button></td>                 
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function dashboardCRMIncidentITSpecAgeing() {
        
        $html = "
                <thead>
                    <tr class=border='1'>
                        <th>Case Number</th>
                        <th>Redmine Number</th>
                        <th>Date Created</th>
                        <th>Case Status</th>
                        <th>Incident Service Type</th>
                        <th>Request Type</th>
                        <th>Module</th>
                        <th>Severity</th>
                        <th>IT Specialist Start Datetime</th>
                        <th>Ageing Day Case</th>
                        <th>Ageing Day Specialist</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecAgeing();

        // dd($listTasks);

        if (count($listTasks) > 0) {
            $modules = array();
            $severities = array();
            foreach ($listTasks as $tasks) {
                
                $html .= "
                <tr>
                    <td><strong><a target='_blank' href='" . self::$url . "={$tasks->case_number}' title='View Detail Case in CRM'>{$tasks->case_number}</a></strong></td>
                    <td><strong>{$tasks->redmine_number}</strong></td>
                    <td><strong>{$tasks->date_created}</strong></td>
                    <td><strong>{$tasks->case_status}</strong></td>
                    <td><strong>{$tasks->incident_service_type}</strong></td>
                    <td><strong>{$tasks->request_type}</strong></td>
                    <td><strong>{$tasks->module}</strong></td>
                    <td><strong>{$tasks->severity}</strong></td>
                    <td><strong>{$tasks->itspec_start_datetime}</strong></td>
                    <td><strong>{$tasks->ageing_day_case}</strong></td>
                    <td><strong>{$tasks->ageing_day_specialist}</strong></td>
                </tr>
                ";

                if (in_array($tasks->module, $modules)) {
                    continue;
                }
                $modules[] = $tasks->module;

                if (in_array($tasks->severity, $severities)) {
                    continue;
                }
                $severities[] = $tasks->severity;

            }
        }

        $html_filter = '<div class="form-inline" style="float: right; padding: 8px;">';

        $moduleSelect = '<div class="form-group"><label for="select-module">Module:</label>
        <select id="select-module" class="form-control" name="title">';
        $moduleSelect .= "<option value='' selected> All </option>";
        foreach ($modules as $module) {
            $moduleSelect .= "<option value='{$module}'> $module </option>";
        }
        $moduleSelect .= "</select></div>";

        $severitySelect = '<div class="form-group" style="padding-left: 10px;"><label for="select-severity">Severity:</label>
        <select id="select-severity" class="form-control" name="title">';
        $severitySelect .= "<option value='' selected> All </option>";
        foreach ($severities as $severity) {
            $severitySelect .= "<option value='{$severity}'> $severity </option>";
        }
        $severitySelect .= "</select></div>";

        $html_filter .= $moduleSelect . $severitySelect;

        $html_filter .= '</div>';

        $html .= "</tbody>";

        return [
            'ageing_table' => $html,
            'filter' => $html_filter
        ];
    }

    public function dashboardCRMIncidentPMOAgeing(Request $request) {
        
        $html = "
                <thead>
                    <tr class=border='1'>
                        <th>Case Number</th>
                        <th>Redmine Number</th>
                        <th>Implementation Issue</th>
                        <th>Task Name</th>
                        <th>Date Created</th>
                        <th>Case Status</th>
                        <th>Module</th>
                        <th>Severity</th>
                        <th>PMO Start Datetime</th>
                        <th>Ageing Day Case</th>
                        <th>Ageing Day PMO</th>";
                        if($request->type === 'after') {
                            $html .= "
                            <th>Approved Duration</th>
                            <th>Time Remaining</th>
                            ";
                        }
                        $html .= "
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMPMOAgeing($request->type);

        // Always initialize these arrays to avoid undefined variable errors
        $modules = array();
        $severities = array();

        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {

                $timeRemaining = '';
                $dueClass = 'green';
                $current = Carbon::now();
                if($tasks->taskduration) {
                    $estimateDue = date('Y-m-d H:i:s', strtotime($tasks->pmo_start_datetime . "+ $tasks->taskduration"));
                    $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
                    $dateDiff = $current->diff(new DateTime($estimateDueDate));
                    if(!$dateDiff->invert) {
                        $timeRemaining = $dateDiff->days . 'D ' . $dateDiff->h . 'H ' . $dateDiff->i . 'm ' . $dateDiff->s . 's';
                    } else {
                        $dueClass = 'red';
                        $timeRemaining = '-' . $dateDiff->days . 'D ' . $dateDiff->h . 'H ' . $dateDiff->i . 'm ' . $dateDiff->s . 's';
                    }
                }
                // $redmineImplementationIssue = $this::$REDMINE_IMPLEMENTATION[$this->getRedmineInfo($tasks->redmine_number)];implementation_issue
                
                $html .= "
                <tr>
                    <td><strong><a target='_blank' href='" . self::$url . "={$tasks->case_number}' title='View Detail Case in CRM'>{$tasks->case_number}</a></strong></td>
                    <td><strong>{$tasks->redmine_number}</strong></td>
                    <td><strong>{$tasks->implementation_issue}</strong></td>
                    <td><strong>{$tasks->name}</strong></td>
                    <td><strong>{$tasks->date_created}</strong></td>
                    <td><strong>{$tasks->case_status}</strong></td>
                    <td><strong>{$tasks->module}</strong></td>
                    <td><strong>{$tasks->severity}</strong></td>
                    <td><strong>{$tasks->pmo_start_datetime}</strong></td>
                    <td><strong>{$tasks->ageing_day_case}</strong></td>
                    <td><strong>{$tasks->ageing_day_pmo}</strong></td>";
                    if($request->type === 'after') {
                        $html .= "
                        <td><strong>{$tasks->taskduration}</strong></td>
                        <td><strong><font color='{$dueClass}'>{$timeRemaining}</font></strong></td>
                        ";
                    }
                    $html .= "
                </tr>
                ";

                if (in_array($tasks->module, $modules)) {
                    continue;
                }
                $modules[] = $tasks->module;

                if (in_array($tasks->severity, $severities)) {
                    continue;
                }
                $severities[] = $tasks->severity;

            }
        }

        $html_filter = '<div class="form-inline" style="float: right; padding: 8px;">';

        $moduleSelect = '<div class="form-group"><label for="select-module">Module:</label>
        <select id="select-module" class="form-control" name="title">';
        $moduleSelect .= "<option value='' selected> All </option>";
        foreach ($modules as $module) {
            $moduleSelect .= "<option value='{$module}'> $module </option>";
        }
        $moduleSelect .= "</select></div>";

        $severitySelect = '<div class="form-group" style="padding-left: 10px;"><label for="select-severity">Severity:</label>
        <select id="select-severity" class="form-control" name="title">';
        $severitySelect .= "<option value='' selected> All </option>";
        foreach ($severities as $severity) {
            $severitySelect .= "<option value='{$severity}'> $severity </option>";
        }
        $severitySelect .= "</select></div>";

        $html_filter .= $moduleSelect . $severitySelect;

        $html_filter .= '</div>';

        $html .= "</tbody>";

        return [
            'ageing_table' => $html,
            'filter' => $html_filter
        ];
    }

    public function getRedmineInfo($redmine_no) {

        try {
            $urlRedmine = env("URL_REDMINE", "https://192.168.120.102");
            $keyClientRedmine = env("KEY_CLIENT_REDMINE", "62d875bd246828ad033b54bf5b39a9a50c3aa1bb");

            $client = new \GuzzleHttp\Client([
                'base_uri' => $urlRedmine,
            ]);
            $data = [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'key' => $keyClientRedmine
                ],
                'verify' => false
            ];
            $response = $client->request('GET', '/issues/' . $redmine_no . '.json', $data);
            $resultResp = json_decode($response->getBody(), true);

            $customFields = $resultResp['issue']['custom_fields'];
            $fImplementationIssue = null;
            foreach ($customFields as $key => $try) {
                $try = $key;
                if ($customFields[$key]['name'] == 'Implementation Issue') {
                    $fImplementationIssue = ($customFields[$key]['value']);
                }
            }
            return $fImplementationIssue;
        } catch (Exception $ex) {
            return array(
                "status" => "Error",
                "status_desc" => 'Error getting result',
                "result" => $ex->getMessage()
            );
        }
    }

    public static $REDMINE_IMPLEMENTATION = array(
        '' => '-',
        '0' => 'No',
        '1' => 'Yes',
    );

}
