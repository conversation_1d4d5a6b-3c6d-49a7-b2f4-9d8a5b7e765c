@extends('layouts.guest-dash')
@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-4 log-header-title">
            <span>WIP : List CT Agreement Null Data<br /></span>
            <small>To View List Detail - Pending user to click task list to initiate create agreement</small>
        </div>
        <div class="col-md-8 log-header-menu">
            <a href="{{url('ep/ct/stat-ct-cancel-batch-no')}}"><span class="{{ Request::is('ep/ct/stat-ct-cancel-batch-no') ? 'active' : '' }}">Statistic By Batch No</span></a> |
            <a href="{{url('ep/ct/list-ct-cancel-agreement')}}"><span class="{{ Request::is('ep/ct/list-ct-cancel-agreement') ? 'active' : '' }}">List CT Agreement Cancellation</span></a> |
            <a href="{{url('ep/ct/list-ct-agreement-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-null') ? 'active' : '' }}">WIP List CT Agreement Null</span></a> |
            <br/><a href="{{url('ep/ct/list-ct-pending-amendment')}}"><span class="{{ Request::is('ep/ct/list-ct-pending-amendment') ? 'active' : '' }}">WIP List CT Pending Amendment</span></a> | 
            <a href="{{url('ep/ct/list-ct-agreement-not-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-not-null') ? 'active' : '' }}">WIP List CT Agreement Not Null</span></a> |
            <a href="{{url('ep/ct/list-ct-agreement-missing')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-missing') ? 'active' : '' }}">WIP List CT Agreement Missing</span></a>            
        </div>
    </div>
</div>
@endsection
@section('content')
<div class="block">
    <form class="form-horizontal" id="carian-form" action="{{url('/ep/ct/list-ct-cancel-agreement')}}/" method="get" >
        <div class="row">
            <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cari">Carian / Search (Contract No.) </label>
                        <div class="col-md-6">
                            <input type="text" id="cari" name="cari" class="form-control" value="{{$carian}}"  onfocus="this.select();" placeholder="Klik carian contract no.  di sini ... ">
                        </div>
                    </div>
            </div>
        </div>
    </form>
</div>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> </h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              
              <p>Tiada rekod!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title">
            <h1><i class="fa fa-building-o"></i> <strong>WIP : List CT Agreement Null Data</strong></h1>
        </div>

        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        
                        <th class="text-center">CONTRACT NO</th>
                        <th class="text-center">CONTRACT ID</th>
                        <th class="text-center">AGREEMENT ID</th>
                        <th class="text-center">TASK INSTANCE ID</th>
                        <th class="text-center">TASK STATE ID</th>
                        <th class="text-center">TASK ASSIGNEES</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">
                        <a href="{{ url("/find/contract/") }}?cari={{$data['contract_no']}}" target="_blank" >
                            {{$data['contract_no'] }} </a>
                        </td>
                        <td class="text-center">
                        {{$data['contract_id'] }} 
                        </td>
                        <td class="text-center">{{$data['agreement_id'] }}</td>
                        <td class="text-center">
                            <a href="{{ url("/bpm/instance/find") }}?composite_instance_id={{$data['task_instance_id']}}" target="_blank" >
                                {{$data['task_instance_id'] }} </a>
                        </td>
                        <td class="text-center">{{$data['task_state'] }}</td>
                        <td class="text-center">{{$data['task_assignees'] }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


@endsection
