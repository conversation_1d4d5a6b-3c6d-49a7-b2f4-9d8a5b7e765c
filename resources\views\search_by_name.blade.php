@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianformpembekal" action="{{url('/find/byname')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="nama_pembekal" name="nama_pembekal" value="{{$carianPembekal}}" 
               min="4"
               class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... Nama Pembekal / No. Pendaftaran Syarikat">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
    @if($listdata == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result != null)
                        <i class="gi gi-ban"></i>Carian: {{$carianPembekal}}<br>
                        <small>{{$result}}</small>
                    @else
                        <i class="gi gi-search"></i><PERSON><PERSON> / No. Pendaftaran Syarikat <br>
                        <small>Masukkan <PERSON>a P<PERSON>bekal / SSM No. pada carian diatas.. 
                            <br /> Carian ini untuk dapatkan kepastian syarikat ini sudah didaftarkan di eP ataupun tidak. Maklumat terhad</small>
                    @endif
                </h1>
            </div>
        </div>
    @endif

    @if($listdata != null)   
    <div class="content-header">
        <div class="header-section">
            <h1>
                
                <i class="gi gi-search"></i>Carian Nama Pembekal / No. Pendaftaran Syarikat <br>
                <small>Hasil carian hanya mengeluarkan maksimum 50 pembekal sahaja. 
                <br /> Carian ini untuk dapatkan kepastian syarikat ini sudah didaftarkan di eP ataupun tidak. Maklumat terhad</small>
               
            </h1>
        </div>
    </div>
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i><strong> CARIAN NAMA PEMBEKAL / NO. PENDAFTARAN SYARIKAT </strong></h1>
        </div>
        <div class="row">
            <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        @if(Auth::user()->isAdvRolesEp())
                        <th class="text-center">SUPPLIER ID</th>
                        @endif
                        <th class="text-center">COMPANY NAME</th>
                        <th class="text-center">BUSINESS TYPE</th>
                        <th class="text-center">SUPPLIER TYPE</th>
                        <th class="text-center">REG NO</th>
                        <th class="text-center">EP NO</th>
                        <th class="text-center">APPL ID</th>
                        <th class="text-center">RECORD STATUS</th>
                        <th class="text-center">CREATED DATE </th>
                        <th class="text-center">CHANGED DATE </th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($listdata as $data)
                    <tr>
                        @if(Auth::user()->isAdvRolesEp())
                        <td class="text-center"><a href="{{url('find/supplier')}}/?carian={{ $data->company_name }}">{{ $data->supplier_id }}</a></td>
                        @endif
                        <td class="text-left">
                            @if(Auth::user()->isAdvRolesEp())
                                <a href="{{url('/find/epno')}}/{{ $data->ep_no }}">
                            @elseif(Auth::user()->isCodiUsersEp())
                                <a href="{{url('/find/codi/supplier')}}/?carian={{ $data->ep_no }}">
                            @elseif(Auth::user()->isCodiUsersEp())
                                <a href="{{url('find/supplier')}}/?carian={{ $data->ep_no }}">
                            @endif  
                                 {{ strtoupper($data->company_name) }}</a></td>                  
                        <td class="text-left">{{ $data->business_type }}  @if($data->business_type != null) &raquo;  ({{ App\Services\EPService::$BUSINESS_TYPE[$data->business_type] }}) @endif </td>                        
                        <td class="text-center">{{ $data->supplier_type }} @if($data->supplier_type != null)  &raquo;   ({{ App\Services\EPService::$SUPPLIER_TYPE[$data->supplier_type] }}) @endif </td>
                        <td class="text-center">{{ $data->reg_no }}</td>
                        <td class="text-center">{{ $data->ep_no }}</td>
                        <td class="text-center">{{ $data->latest_appl_id }}</td>
                        <td class="text-left"><span>{{ $data->record_status }}  @if($data->record_status != null) &raquo;   {{ App\Services\EPService::$RECORD_STATUS[$data->record_status]  }} @endif </span> </td>
                        <td class="text-center">{{ $data->created_date }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif

@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection
