@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> Reporting List</strong></h1> 
    </div> 
    <form class="form-horizontal form-bordered patch-form" id="patch-form" action="{{url('/app-support/reporting')}}" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading">
            <label class="col-md-1 text-left" for="module">Module<span class="text-danger">*</span></label>
            <div class="col-md-3 type">
                <select id="module1" name = "module1" required class="form-control" style="width: 700px;">
                    <option value="">Please Select</option>
                    @foreach($getModule as  $key)
                    <option value="{{$key->rpt_module}}">{{$key->rpt_module}}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-md-1 text-left" for="report_name">Report Name<span class="text-danger">*</span></label>
            <div class="col-md-3 reportname">
                <select id="report_name" name="report_name" required class="form-control" style="width: 700px;">
                </select>
            </div>
        </div>

        <div class="form-group" id="fieldView"></div>

        <div class="form-actions form-actions-button text-right buttonSearch" style="display:none; width: 1000px;">
            <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
        </div>

        <div class="form-group hide" >
            <div class="col-md-3">
                <input id="rpt_code" name="rpt_code" readonly="" type="text" class="form-control" style="width: 700px;">
            </div>
            <div class="col-md-3">
                <input id="rpt_file_name" name="rpt_file_name" readonly="" type="text" class="form-control" style="width: 700px;">
            </div>
        </div>
    </form>
    <div class="table-responsive">
        <table id="search_list-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Document Number</th>
                    <th class="text-center">Report Name</th>
                    <th class="text-center">View</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    @if($number != null)<td class="text-center">{{$number}}</td>@endif
                    @if($file_name != null)<td class="text-center">{{$file_name}}</td>@endif
                    @if($link != null)<td class="text-center">
                        <a href='{{ $link }}' data-toggle="tooltip" title="View" target="{{ $link }}" class="btn btn-default"><i class="fa fa-edit"></i></a></td>@endif
                </tr>
            </tbody>
        </table>
    </div>

</div>

<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><strong> Log Action </strong></h1> 
    </div>
    <div class="table-responsive">
        <table id="reporting_list_log-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Username</th>
                    <th class="text-center">Module</th>
                    <th class="text-center">File Name</th>
                    <th class="text-center">Login Id</th>
                    <th class="text-center">Doc No</th>
                    <th class="text-center">Date From</th>
                    <th class="text-center">Date To</th>
                    <th class="text-center">Appl Id</th>
                    <th class="text-center">Zone</th>
                    <th class="text-center">Date Searching</th>
                </tr>
            </thead>
            <tbody>
                @foreach($log_list as $list)
                <tr>
                    <td class="text-center">{{$list->log_user_name}}</td>
                    <td class="text-center">{{$list->log_module}}</td>
                    <td class="text-center">{{$list->log_file_name}}</td>
                    <td class="text-center">{{$list->log_login_id}}</td>
                    <td class="text-center">{{$list->log_doc_no}}</td>
                    <td class="text-center">{{$list->log_date_from}}</td>
                    <td class="text-center">{{$list->log_date_to}}</td>
                    <td class="text-center">{{$list->log_appl_id}}</td>
                    <td class="text-center">{{$list->log_zone}}</td>
                    <td class="text-center">{{$list->log_created_date}}</td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>


@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>
App.datatables();
$(document).ready(function () {
    $('#reporting_list_log-datatable').DataTable({
                order: [[9,"desc"]],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        
    });
});
    </script> 
<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    $('.type').on("change", '#module1', function () {
        $('#fieldView').html("<div></div>");
        $module = $('#module1').val();
        if ($module === "") {
            $('.buttonSearch').hide();
        }
        $('#report_name').empty();
        $.ajax({
            url: APP_URL + '/app-support/reporting/list/module/' + $module, dataType: 'json',
            type: "GET",
            success: function (data) {
                $data = $(data)
                $($data).each(function ()
                {
                    var option = $('<option />');
                    option.attr('value', this.rpt_name).text(this.rpt_name);
                    $('#report_name').append(option);
                });
                $("#report_name").trigger("click");
            }
        });
    });

    $('#report_name').on('click', function () {
        $('.buttonSearch').show();
        $('#fieldView').html("<div></div>");
        $rpt_name = $('#report_name').val();
        $.ajax({
            url: APP_URL + '/app-support/reporting/list/report/name/' + $rpt_name, dataType: 'json',
            type: "GET",
            success: function (data) {
                $data = $(data)
                console.log(data[0].code)
                console.log(data[0].file_name)
                $("#rpt_code").val(data[0].code);
                $("#rpt_file_name").val(data[0].file_name);
                var list = data[0].fields
                list.forEach(myFunction);
                function myFunction(item) {
                    if (item.field_type === 'input_text') {
                        $('#fieldView').append(createInputField(item));
                    }
                    if (item.field_type === 'multi_select') {
                        $('#fieldView').append(createSelectField(item));
                    }
                    if (item.field_type === 'date') {
                        $('#fieldView').append(createDateField(item));
                    }
                }
            }
        });
    });
    function createInputField(item) {
        return "<label class='col-md-1 text-left'>" + item.field_label + "<span class='text-danger'>*</span></label><div class='col-md-3'><input id='" + item.field_name + "'  name= '" + item.field_name + "' value='' style='width: 700px;' required class='form-control'></div>";
    }
    function createSelectField(item) {
        var lab = "<label class='col-md-1 text-left'>" + item.field_label + "<span class='text-danger'>*</span></label><div class='col-md-3'><select id='" + item.field_name + "'  name= '" + item.field_name + "' value='' style='width: 700px;' required class='form-control'>"
        var menu = item.field_value
        $.each(menu, function (key, value) {
            val = value;
            console.log(val)
            lab += "<option value=" + value + ">" + value + "</option>"
        });
        lab += "</select></div>";

        return lab
    }
    function createDateField(data) {
        return "<label class='col-md-1 text-left'>" + data.field_label + "<span class='text-danger'>*</span></label><div class='col-md-3'><input id='" + data.field_name + "'  name= '" + data.field_name + "' type='date' value='' value='' style='width: 700px;' required class='form-control'></div>";
    }
</script>    
    

@endsection



