@extends('layouts.guest-dash')

<style> 
    #table-scroll {
        height:96%;
        width:100%;
        overflow:auto;
    } 
    .tdR {
        overflow: hidden;
        white-space: nowrap;
        width: 100px;
    }
    .hover { background-color: yellow; }

</style>
<link rel="stylesheet" href="/css/jquery.treegrid.css">

@section('content')

<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
            </li>
            <li class="active">
                <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
            </li>
            <li >
                <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
            </li>
        </ul>
    </div>
</div> 

@if ($errors->any())
<div class="alert alert-danger">
    <ul>
        @foreach ($errors->all() as $error)
        <li>{{ $error }}</li>
        @endforeach
    </ul>
</div>
@endif
<div class="col-lg-7">
    <div class="row">

        @if($status_api != null)
        <h5 class="alert alert-danger">{{$status_api}}</h5>
        @endif

        <h5 class="alert alert-success" id="success" style="display:none;"></h5>
        <p id="failed_ul" class="faultedClass" style="background-color: #FFD1D1;font-size: 15px; list-style-type: none;padding: 0;display:none">&nbsp;&nbsp; <a style="color:red;"> Oops! An exception has occured. </a></p>
        <!-- <ul id="failed_ul" style="list-style-type: none;padding: 0;display:none">
            <li  style="background-color: #FFD1D1;font-size: 15px;overflow-x:scroll;overflow-y:hidden;">&nbsp;&nbsp;<i style="color:red;" class="caret-icon fa fa-caret-right failedClass"></i> <span style="color:red">Oops! An exception has occured. </span>
                <br/><textarea id="failed" name="failed" style="display:none;background-color: white; margin: 15px;font-size: 12px;width:95%;height:50%;resize:none;"></textarea>
            </li>
        </ul> -->

        <div class="block" style="background-color: #f2f2f2">
            <form id="form-search-task" action="{{url("/bpm/instance/find")}}" method="post" class="form-horizontal form-bordered">
                {{ csrf_field() }}
                <div class="form-group">
                    <label class="col-md-2 control-label" for="composite_module" style="color:#003d7a;">Composite </label> 
                    <div class="input-group" style="width: 80%;padding-left: 20px;">
                        <select id="composite_module" name="composite_module" class="form-control select2">
                            @if(isset($listdata["compositeModule"]))
                            <option value="{{$listdata["compositeModule"]}}">{{$listdata["compositeModule"]}}</option>
                            @foreach($listdataComposite as  $key => $value)
                            <option value="{{$key}}" @if($key == old('composite_module') ) selected @endif>{{$value}}</option>
                            @endforeach
                            @else
                            <option value="">Please select</option>
                            @foreach($listdataComposite as  $key => $value)
                            <option value="{{$key}}" @if($key == old('composite_module') ) selected @endif>{{$value}}</option>
                            @endforeach
                            @endif
                        </select>
                    </div>
                </div>
                <div class="form-group form-inline">
                    <label class="col-md-2 control-label" for="composite_instance_id" style="color:#003d7a;">Instance ID</label>
                    <div class="input-group" style="width:66%;padding-left: 20px">
                        <input id="composite_instance_id" name="composite_instance_id" required class="form-control" value="{{$instanceId}}" placeholder="Instance ID.." type="number" >
                    </div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <button type="submit" class="btn btn-sm btn-info"><i class="gi gi-search"></i> Search</button>       
                </div>
            </form>
        </div>
    </div>

    <div class="row">
        @if(isset($listdata["compositeId"]) && isset($listdata["compositeModule"]))
        <center>
            <div id="spinner" style="display:none;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>
        </center>
        <div class="block" id="mainContent" style="display:block;">   
            <div class="form-inline">
                <label>STATUS : &nbsp;&nbsp;&nbsp;<i class="fa fa-{{ App\Services\BPMAPIService::$BPM_STATUS_ICON[$listdata["compositeState"]] }}" style="{{ App\Services\BPMAPIService::$BPM_STATUS_STYLE[$listdata["compositeState"]] }}"></i><span style="{{ App\Services\BPMAPIService::$BPM_STATUS_STYLE[$listdata["compositeState"]] }}"> {{ App\Services\BPMAPIService::$BPM_STATUS[$listdata["compositeState"]] }}</span></label>
                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                <label>FLOW ID : &nbsp;&nbsp;&nbsp; <span style="color:purple" > {{ $flowId }} </span></label>
                <div class="input-group dropdown pull-right">
                    <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        <span class="caret"></span>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a id="terminate_instance" instanceID="{{$listdata["compositeId"]}}" compModule="{{$listdata["compositeModule"]}}">Terminate</a></li>
                    </ul>
                </div>
            </div>
            <br/> 

            <div class="table-responsive">    

                <table class="tree table  table-condensed table-bordered ">
                    <thead style="background-color:#f2f2f2;">
                        <tr>    
                            <th class="text-center">Name</th>
                            <th class="text-center">Id</th>
                            <th class="text-center">State</th>
                            <th class="text-center">Created</th>
                        </tr>
                    </thead>
                    <tbody style="font-size:80%;">
                        <?php

                        function myview($child, $counterParent, $date, $data,$compositeId) {
                            ?>
                            <tr  class="{{$counterParent}} {{$child}}">  
                                @if($data["componentType"] === 'bpmn')
                                <td class="tdR"><i class="fa fa-sitemap"></i> {{ $data["componentName"] }}</td>
                                <td data-id="{{$data["componentInstanceId"]}}" data-status="{{$data["componentState"]}}" data-cmpstId="{{$compositeId}}" status="{{ $data["componentState"] }}" class="bpmnProcess tdR" data-target="task-flow"><a style="color:black;">{{ $data["componentInstanceId"] }} </a></td>
                                <td style="{{ App\Services\BPMAPIService::$BPM_STATE_STYLE[$data["componentState"]] }}"><i class="fa fa-{{ App\Services\BPMAPIService::$BPM_STATE_ICON[$data["componentState"]] }} ]"></i> {{ App\Services\BPMAPIService::$BPM_STATE[$data["componentState"]] }}</td>
                                <td>{{ $date }}</td>
                                @elseif($data["componentType"] === 'workflow')
                                <td class="tdR"><i class="fa fa-user" style="color:brown;"></i> {{ $data["componentName"] }}</td>
                                <td data-id="{{$data["componentInstanceId"]}}" data-status="{{$data["componentState"]}}" data-cmpstId="{{$compositeId}}" status="{{ $data["componentState"] }}" class="workflowProcess tdR" data-target="task-flow"> <a style="color:black;">{{ $data["componentInstanceId"] }} </a> </td>
                                <td style="{{ App\Services\BPMAPIService::$WORKFLOW_STATE_STYLE[$data["componentState"]] }}"><i class="fa fa-{{ App\Services\BPMAPIService::$WORKFLOW_STATE_ICON[$data["componentState"]] }}"></i> {{ App\Services\BPMAPIService::$WORKFLOW_STATE[$data["componentState"]] }}</td>
                                <td>{{ $date }}</td>
                                @elseif($data["componentType"] === 'decision')
                                <td class="tdR"><i class="fa fa-gears" style="color: lightblue"></i> {{ $data["componentName"] }}</td>
                                <td data-id="{{$data["componentInstanceId"]}}" data-status="{{$data["componentState"]}}"  status="{{ $data["componentState"] }}" class="workflowProcess tdR" data-target="task-flow"> <a style="color:black;">{{ $data["componentInstanceId"] }} </a> </td>
                                <td style="{{ App\Services\BPMAPIService::$WORKFLOW_STATE_STYLE[$data["componentState"]] }}"><i class="fa fa-{{ App\Services\BPMAPIService::$WORKFLOW_STATE_ICON[$data["componentState"]] }}"></i> {{ App\Services\BPMAPIService::$WORKFLOW_STATE[$data["componentState"]] }}</td>
                                <td>{{ $date }}</td>
                                @endif
                            </tr>
                            <?php
                        }

                        function recursive($array, $parent, $child,$compositeId) {

//                                Log::info('parent ' .$parent .' child ' .$child);
                            foreach ($array as $data) {

                                $child++;
                                $result = str_replace(" MYT", "", $data["componentDateCreation"]);
                                $date = date('d/m/y h:i:s A', strtotime($result));
                                $childs = rand(20, 2000);
                                
                                if (isset($data["childs"])) {
                                    $child++;
                                    myview('treegrid-parent-' . $parent, 'treegrid-' . $child, $date, $data,$compositeId);
                                    $sortedArray = sortArray($data["childs"]);
                                    array_multisort($sortedArray['componentDateCreationTime'], SORT_ASC, $data["childs"]);
//                                        $childs = $childs + $child;
//                                        Log::info(' childs ' .$childs );
                                    recursive($data["childs"], $child, $childs++,$compositeId);
                                } else {
                                    $child = $childs + $child;
                                    myview('treegrid-parent-' . $parent, 'treegrid-' . $child, $date, $data,$compositeId);
                                }
                            }
                            return $childs;
                        }

                        function sortArray($array) {
                            $sort = array();
                            ###sorting here
                            foreach ($array as $k => $v) {
                                $sort['componentDateCreationTime'][$k] = $v['componentDateCreationTime'];
                            }

                            return $sort;
                        }

                        if (isset($listdata["childs"])) {
                            $counterParent = 1;
                            $counterChild = 1;
                            $compositeId = $listdata["compositeId"];
                            $sortedArray = sortArray($listdata["childs"]);
                            if (isset($sortedArray['componentDateCreationTime'])) {
                                array_multisort($sortedArray['componentDateCreationTime'], SORT_ASC, $listdata["childs"]);

                                foreach ($listdata["childs"] as $data) {

                                    $result = str_replace(" MYT", "", $data["componentDateCreation"]);
                                    $date = date('d/m/y h:i:s A', strtotime($result));

                                    myview('treegrid-' . $counterParent, '', $date, $data,$compositeId);

                                    if (isset($data["childs"])) {
                                        $sortedArray = sortArray($data["childs"]);
                                        array_multisort($sortedArray['componentDateCreationTime'], SORT_ASC, $data["childs"]);

                                        recursive($data["childs"], $counterParent, $counterChild++,$compositeId);
                                    }
                                }
                            }
                        }
                        ?>
                    </tbody>
                </table>
            </div>

        </div>
        @else
        <div class="block">
            <h4 class=" alert alert-danger">Ooppss Sorry. Record Not Found!</h4>
        </div>
        @endif  
    </div>
</div>
<div class="row">
    @if($instanceId !== null)
    <div class="col-lg-5">
        <div class="block">
            <div class="table-responsive">
                <h6 class="bolder">Document No. Related</h6>
                <table id="basic-datatable" class="table table table-vcenter table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center" style="font-size:70%;">Component Type</th>
                            <th class="text-center" style="font-size:70%;">Document Number 1</th>
                            <th class="text-center" style="font-size:70%;">Document Number 2</th>
                        </tr>
                    </thead>
                    <tbody style="font-size:70%;">
                        @if(count($listDocNo) > 0)     
                        @foreach ($listDocNo as $data)
                        @if($data->customattributestring1 !== null || $data->protectedtextattribute1 !== null)
                        <tr>
                            <td class="text-center">{{ $data->componenttype }}</td>
                            <td class="text-center"><a href="{{url('/find/trans/track/docno')}}/{{$data->customattributestring1 }}" target=_blank" >{{ $data->customattributestring1 }}</a></td>
                            <td class="text-center"><a href="{{url('/find/trans/track/docno')}}/{{$data->protectedtextattribute1 }}" target=_blank" >{{ $data->protectedtextattribute1}}</a></td>
                        </tr>
                        @endif
                        @endforeach
                        @else
                        <tr>
                            <td class="text-center" colspan="12">No Records</td>
                        </tr>    
                        @endif
                    </tbody>
                </table>

            </div>
            <div class="table-responsive ">
                    <h6 class="bolder btn btn-info btn-xs"  data-toggle="collapse" data-target=".corr_dlv_id">View List Correlation ID </h6>
               
                <table id="basic-datatable" class="corr_dlv_id table table table-vcenter table-bordered collapse">
                    <thead>
                        <tr>
                            <th class="text-center" style="font-size:70%;">Partner Link</th>
                            <th class="text-center" style="font-size:70%;">Component Id</th>
                            <th class="text-center" style="font-size:70%;">Operation Name</th>
                            <th class="text-center" style="font-size:70%;">Doc Type</th>
                            <th class="text-center" style="font-size:70%;">Doc Id</th>
                            <th class="text-center" style="font-size:70%;">State</th>
                        </tr>
                    </thead>
                    <tbody style="font-size:70%;">
                        @if(count($listDlvSubscribCorr) > 0)     
                        @foreach ($listDlvSubscribCorr as $data)
                        <tr>
                            <td class="text-center">{{ $data->partner_link }}</td>
                            <td class="text-center">{{ $data->cikey }}</td>
                            <td class="text-center">{{ $data->operation_name }}</td>
                            <td class="text-center">{{ $data->module_doc_type }}</td>
                            <td class="text-center">{{ $data->doc_id }}</td>
                            <td class="text-center">{{ $data->state }}</td>
                        </tr>
                        @endforeach
                        @else
                        <tr>
                            <td class="text-center" colspan="6">No Records</td>
                        </tr>    
                        @endif
                    </tbody>
                </table>

            </div>
        </div>
        @include('_shared._taskFlowContainer')
        @include('_shared._bpmnFlowContainer')
    </div>
    @endif
</div>

<div id="modal_terminate_instance" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h4> Are you sure you want to terminate this instance? </h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12">
                        <label id="terminate_instance_id"></label>
                        <input type="hidden" name="flow_id" value="{{ $flowId }}">
                    </div>
                </div>
                
                @if(isset($listdata["compositeModule"]) && str_contains($listdata["compositeModule"], 'default/SourcingQT!'))
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-warning">
                            <strong><i class="fa fa-exclamation-triangle"></i> Warning!</strong>
                            @php
                                $firstDocNo = null;
                                $allSame = true;
                                
                                // Get the first non-null document number
                                foreach ($listDocNo as $data) {
                                    if ($data->customattributestring1 !== null) {
                                        $firstDocNo = $data->customattributestring1;
                                        break;
                                    }
                                }
                                
                                // Check if all document numbers (both 1 and 2) are the same
                                foreach ($listDocNo as $data) {
                                    if (($data->customattributestring1 !== null && $data->customattributestring1 !== $firstDocNo) ||
                                        ($data->protectedtextattribute1 !== null && $data->protectedtextattribute1 !== $firstDocNo)) {
                                        $allSame = false;
                                        break;
                                    }
                                }
                            @endphp
                
                            @if (!$allSame)
                                <p class="text-danger"><strong>Error: Document numbers are not consistent! Both Document Number 1 and Document Number 2 must be identical.</strong></p>
                            @else
                                <input type="hidden" name="doc_no" value="{{ $firstDocNo }}">
                                <p>You are about to terminate a SourcingQT instance for document number: {{ $firstDocNo }}</p>
                                <p class="text-danger"><strong>Database Impact:</strong></p>
                                <ul>
                                    <li>This action will modify records in the <code>WFTASK</code> table that are associated with:</li>
                                    <ul>
                                        <li>Current <code>Flow ID</code> ({{ $flowId }})</li>
                                        <li>Records where <code>CUSTOMATTRIBUTESTRING1 = {{ $firstDocNo }}</code></li>
                                    </ul>
                                    <li>Modification details:</li>
                                    <ul>
                                        <li>All CUSTOMATTRIBUTESTRING1 that starts with "QT" will be appended "X"</li>
                                        <li>Example: "{{ $firstDocNo }}" will become "QTX{{ substr($firstDocNo, 2) }}"</li>
                                    </ul>
                                </ul>
                            @endif
                        </div>
                    </div>
                </div>
                @endif
            </div>
            <div class="modal-footer">
                <button type="button" id="submit_terminate" class="btn btn-sm btn-info">Ok</button>
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div id="modal_spinner" class="modal fade">
    <div class="modal-dialog modal-sm" style="width: 10%; transform: translate(0, -50%); top: 50%; margin: 0 auto">
        <div class="modal-content">
            <div class="modal-body">
                <center>
                <i class="fa fa-spinner fa-3x fa-spin" style="color: red"></i><br/><br/>Loading..
            </center>
            </div>    
        </div>
    </div>
</div>
<div id="modalFaulted" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body text-center">
            <textarea id="fault_body" name="fault_body" style="background-color: #FFD1D1; font-size: 12px;width:95%;height:50%;resize:none;"></textarea>
            </div>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-danger">CLOSE</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>$('#page-container').removeAttr('class');</script>

<script src="/js/vendor/jquery.treegrid.min.js"></script>
<script src="/js/vendor/jquery.treegrid.bootstrap3.js"></script>
<script src="/js/bpm/process/bpmnflow.js"></script>
<script src="/js/bpm/process/taskflow.js"></script>
<script src="/js/bpm/process/action.js"></script>
<script src="/js/bpm/process/populatetable.js"></script>

<script>
    $(document).ready(function () {
        $('.tree').treegrid({
            expanderExpandedClass: 'fa fa-angle-down',
            expanderCollapsedClass: 'fa fa-angle-right'
        });
        $('.select2').select2();
    });

</script>
<script>
    var toggler = document.getElementsByClassName("fa-caret-right");
    var i;

    for (i = 0; i < toggler.length; i++) {
        toggler[i].addEventListener("click", function () {
            $(this).closest("li").find("[id='fault_body']").slideToggle();
            $(this).toggleClass('fa-caret-down fa-caret-right');
        });
    }

    var failedClass = document.getElementsByClassName("failedClass");
    var j;

    for (j = 0; j < failedClass.length; j++) {
        failedClass[j].addEventListener("click", function () {
            $(this).closest("li").find("[id='failed']").slideToggle();
            $(this).toggleClass('fa-caret-down fa-caret-right');
        });
    }

    
    $('.faultedClass').on("click", function () {
        $('#modalFaulted').modal('show');
    });
</script>
@endsection



