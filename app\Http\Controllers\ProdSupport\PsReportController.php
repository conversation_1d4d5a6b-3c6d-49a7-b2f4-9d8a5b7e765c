<?php

namespace App\Http\Controllers\ProdSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\ProdSupport\RptStatTransactionEpService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use SSH;
use stdClass;
use Exception;
use DB;
use Excel;
use File;
use Validator;
use Storage;

class PsReportController extends Controller
{

    use RptStatTransactionEpService;

    public function ReadStatbyyear(Request $request)
    {
        $listDataForYear = null;
        $listYear = $this->findYear();

        if ($request->by_year != null) {
            $listDataForYear = $this->listForYear($request->by_year);
        }

        return view('prod_support.rpt.report_001_byyear', [
            'listDataForYear' => $listDataForYear,
            'listYear' => $listYear
        ]);
    }

    public function actionForSearchOrDownload(Request $request)
    {

        if (isset($_POST['searchbutton'])) {
            return $this->ReadStatbyyear($request);
        } elseif (isset($_POST['downloadfromdb'])) {
            return $this->downloadFromList($request);
        }
    }

    public function downloadFromList(Request $request)
    {
        $listDataForYear = $this->listForYear($request->by_year);
        if ($listDataForYear !== null) {
            $collectlistReporting = collect($listDataForYear)->groupBy('year');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'LAPORAN PERKHIDMATAN PERBENDAHARAAN DARIPADA BULAN JANUARI HINGGA DISEMBER (DALAM TALIAN)';
            $yearToDownload = ($listDataForYear[0]->year);
            Excel::create($fileName, function ($excel) use ($collectlistReporting, $yearToDownload) {
                $excel->setTitle('Report');

                $excel->sheet('Report Tahunan ' . $yearToDownload, function ($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@',
                            'G' => '@',
                            'H' => '@',
                            'I' => '@',
                            'J' => '@',
                            'K' => '@',
                            'L' => '@',
                            'M' => '@',
                            'N' => '@',
                        ));

                        $sheet->row(1, array(
                            'No', 'Category Online', 'January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'
                        ));

                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:N1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('#070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;

                        foreach ($list as $obj) {
                            //                            dump($obj);
                            $sheet->row(
                                $count,
                                array(
                                    $obj->bil_no,
                                    $obj->service_name_bm,
                                    $obj->Jan,
                                    $obj->Feb,
                                    $obj->March,
                                    $obj->April,
                                    $obj->May,
                                    $obj->June,
                                    $obj->July,
                                    $obj->Aug,
                                    $obj->Sep,
                                    $obj->October,
                                    $obj->Nov,
                                    $obj->December,
                                )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function ReadStatbyMonthYear(Request $request)
    {
        $listMonth = $this->findMonth();
        $listYear = $this->findYear();
        $readAllonline = null;

        $getyear = $request->by_year;
        $getMonth = $request->pickmonth;

        if ($getMonth != null || $getyear != null) {
            $readAllonline = $this->Readfact_yearmonth_online($getyear, $getMonth);
        }

        return view('prod_support.rpt.report_001_updatedform', [
            'listMonth' => $listMonth,
            'listYear' => $listYear,
            'readAllonline' => $readAllonline,
        ]);
    }

    public function UpdatedStatReport_001(Request $request)
    {
        $user = auth()->user()->first_name;
        $getidno = $request->idno;

        if ($request->newtotal != null) {
            if ($request->isMethod("POST")) {
                $updateData = [
                    'total' => $request->newtotal,
                    'changed_at' => Carbon::now(),
                    'changed_by' => $user,
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_fact_ep_service_rpt')
                    ->where('id', $getidno)
                    ->update($updateData);

                return redirect()->action('ProdSupport\PsReportController@ReadStatbyMonthYear');
            }
        }
        return redirect()->action('ProdSupport\PsReportController@ReadStatbyMonthYear');
    }

    public function reportDataLookup(Request $request)
    {
        $oriname = null;
        $oridesc = null;
        $oricategory = null;
        $user = auth()->user()->first_name;
        $getLookupdate = $this->listDataLookup();

        if ($request->id != null) {
            $listdata = $this->listDataLook($request->id);

            if ($request->name != "") {
                if (!$request->name) {
                    $oriname = $listdata->name;
                } else
                    $oriname = $request->name;
            } else {
                $oriname = "";
            }

            if ($request->description != "") {
                if (!$request->description) {
                    $oridesc = $listdata->description;
                } else
                    $oridesc = $request->description;
            } else {
                $oridesc = "";
            }

            if ($request->grouptype != "") {
                if (!$request->grouptype) {
                    $oricategory = $listdata->category;
                } else
                    $oricategory = $request->grouptype;
            } else {
                $oricategory = "";
            }

            if ($request->isMethod("POST")) {
                $updateData = [
                    'name' => $oriname,
                    'description' => $oridesc,
                    'category' => $oricategory,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_report_datalookup')
                    ->where('data_id', $request->id)
                    ->update($updateData);
            }
            return back();
        }

        if ($request->name != null) {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_report_datalookup')->insert([
                ['name' => $request->name, 'description' => $request->description, 'category' => $request->grouptype, 'created_by' => $user, 'created_date' => carbon::now()],
            ]);
            return back();
        }

        return view('prod_support.rpt.report_data_lookup', [
            'getLookupdate' => $getLookupdate,
        ]);
    }

    public function cancelDataLookup($id)
    {
        $user = auth()->user()->first_name;

        DB::connection('mysql_ep_prod_support')->table('ps_report_datalookup')
            ->where('data_id', $id)
            ->delete();

        return back();
    }

    public function createStatisticInfo(Request $request)
    {
        $listDataForYear = null;
        $serviceName = "";
        $try = 0;
        $listYear = $this->findYear();
        $listService = $this->findServiceName();
        $listMonth = $this->findMonth();
        $collectDataAll = array();
        $arrayName = array();


        if ($request->by_year != null && $request->by_name != null) {
            $serviceName = $this->getNameService($request->by_name);
            array_push($arrayName, $serviceName[0]->service_name_bm);
            $listDataForYear = $this->listStatistic($request->by_year, $request->by_name);
            foreach ($listDataForYear as $list) {
                if ($list->January) {
                    $try = ['label' => $listMonth[0]->month, 'y' => (int) $list->January];
                }
                if ($list->January == 0) {
                    $try = ['label' => $listMonth[0]->month, 'y' => $list->January];
                }
                array_push($collectDataAll, $try);
                if ($list->Feb) {
                    $try = ['label' => $listMonth[1]->month, 'y' => (int) $list->Feb];
                }
                if ($list->Feb == 0) {
                    $try = ['label' => $listMonth[1]->month, 'y' => $list->Feb];
                }
                array_push($collectDataAll, $try);
                if ($list->March) {
                    $try = ['label' => $listMonth[2]->month, 'y' => (int) $list->March];
                }
                if ($list->March == 0) {
                    $try = ['label' => $listMonth[2]->month, 'y' => $list->March];
                }
                array_push($collectDataAll, $try);
                if ($list->April) {
                    $try = ['label' => $listMonth[3]->month, 'y' => (int) $list->April];
                }
                if ($list->April == 0) {
                    $try = ['label' => $listMonth[3]->month, 'y' => $list->April];
                }
                array_push($collectDataAll, $try);
                if ($list->May) {
                    $try = ['label' => $listMonth[4]->month, 'y' => (int) $list->May];
                }
                if ($list->May == 0) {
                    $try = ['label' => $listMonth[4]->month, 'y' => $list->May];
                }
                array_push($collectDataAll, $try);
                if ($list->June) {
                    $try = ['label' => $listMonth[5]->month, 'y' => (int) $list->June];
                }
                if ($list->June == 0) {
                    $try = ['label' => $listMonth[5]->month, 'y' => $list->June];
                }
                array_push($collectDataAll, $try);
                if ($list->July) {
                    $try = ['label' => $listMonth[6]->month, 'y' => (int) $list->July];
                }
                if ($list->July == 0) {
                    $try = ['label' => $listMonth[6]->month, 'y' => $list->July];
                }
                array_push($collectDataAll, $try);
                if ($list->Aug) {
                    $try = ['label' => $listMonth[7]->month, 'y' => (int) $list->Aug];
                }
                if ($list->Aug == 0) {
                    $try = ['label' => $listMonth[7]->month, 'y' => $list->Aug];
                }
                array_push($collectDataAll, $try);
                if ($list->Sep) {
                    $try = ['label' => $listMonth[8]->month, 'y' => (int) $list->Sep];
                }
                if ($list->Sep == 0) {
                    $try = ['label' => $listMonth[8]->month, 'y' => $list->Sep];
                }
                array_push($collectDataAll, $try);
                if ($list->October) {
                    $try = ['label' => $listMonth[9]->month, 'y' => (int) $list->October];
                }
                if ($list->October == 0) {
                    $try = ['label' => $listMonth[9]->month, 'y' => $list->October];
                }
                array_push($collectDataAll, $try);
                if ($list->Nov) {
                    $try = ['label' => $listMonth[10]->month, 'y' => (int) $list->Nov];
                }
                if ($list->Nov == 0) {
                    $try = ['label' => $listMonth[10]->month, 'y' => $list->Nov];
                }
                array_push($collectDataAll, $try);
                if ($list->December) {
                    $try = ['label' => $listMonth[11]->month, 'y' => (int) $list->December];
                }
                if ($list->December == 0) {
                    $try = ['label' => $listMonth[11]->month, 'y' => $list->December];
                }
                array_push($collectDataAll, $try);
            }
        }



        return view('prod_support.rpt.report_001_statistic', [
            'listService' => $listService,
            'listYear' => $listYear,
            'collectDataAll' => $collectDataAll,
            'serviceName' => $arrayName,
        ]);
    }

    public function reportSummary(Request $request)
    {
        $user = auth()->user()->first_name;
        $listTindakan = $this->listTindakan();
        $listPengguna = $this->listPengguna();
        $listStatus = $this->listStatus();
        $listReport = $this->listReportSummary();

        if ($request->received_date_apply != null) {
            $dateDiterima = carbon::parse($request->received_date_apply);
            $received_date_apply = $dateDiterima->format('Y/m/d');
        } else
            $received_date_apply = null;
        if ($request->received_date != null) {
            $dateDiterima = carbon::parse($request->received_date);
            $received_date_string = $dateDiterima->format('Y/m/d');
        } else
            $received_date_string = null;
        if ($request->data_date_req != null) {
            $dateDiperlukan = carbon::parse($request->data_date_req);
            $required_date_string = $dateDiperlukan->format('Y/m/d');
        } else
            $required_date_string = null;
        if ($request->submit_d5_date != null) {
            $submit_d5 = carbon::parse($request->submit_d5_date);
            $submit_d5_date_string = $submit_d5->format('Y/m/d');
        } else
            $submit_d5_date_string = null;
        if ($request->return_d5_date != null) {
            $return_d5 = carbon::parse($request->return_d5_date);
            $return_d5_date_string = $return_d5->format('Y/m/d');
        } else
            $submit_d5_date_string = null;
        if ($request->submit_apply_date != null) {
            $submit_cdc = carbon::parse($request->submit_apply_date);
            $submit_cdc_date_string = $submit_cdc->format('Y/m/d');
        } else
            $submit_cdc_date_string = null;

        if ($request->editid != null) {
            $listdata = $this->listSummary($request->editid);

            if ($request->subject != "") {
                if (!$request->subject) {
                    $oriSubject = $listdata->perkara;
                } else
                    $oriSubject = $request->subject;
            } else {
                $oriSubject = "";
            }

            if ($request->remarks != "") {
                if (!$request->remarks) {
                    $oriRemarks = $listdata->remarks;
                } else
                    $oriRemarks = $request->remarks;
            } else {
                $oriRemarks = "";
            }

            if ($request->pemohon != "") {
                if (!$request->pemohon) {
                    $oripemohon = $listdata->pemohon;
                } else
                    $oripemohon = $request->pemohon;
            } else {
                $oripemohon = "";
            }

            if ($request->tindakan != "") {
                if (!$request->tindakan) {
                    $oritindakan = $listdata->tindakan;
                } else
                    $oritindakan = $request->tindakan;
            } else {
                $oritindakan = "";
            }

            if ($request->prepared_by != "") {
                if (!$request->tindakan) {
                    $oriprepared = $listdata->completed_by;
                } else
                    $oriprepared = $request->prepared_by;
            } else {
                $oriprepared = "";
            }

            if ($request->received_date_apply != null) {
                if (!$request->received_date_apply) {
                    $orireceived_date_apply = $listdata->received_date_apply;
                } else
                    $orireceived_date_apply = $received_date_apply;
            } else {
                $orireceived_date_apply = null;
            }

            if ($request->received_date != null) {
                if (!$request->received_date) {
                    $orireceived_date = $listdata->received_date;
                } else
                    $orireceived_date = $received_date_string;
            } else {
                $orireceived_date = null;
            }

            if ($request->data_date_req != null) {
                if (!$request->data_date_req) {
                    $oridata_date_req = $listdata->data_required_date;
                } else
                    $oridata_date_req = $required_date_string;
            } else {
                $oridata_date_req = null;
            }

            if ($request->statusterkini != "") {
                if (!$request->statusterkini) {
                    $oristatusterkini = $listdata->status;
                } else
                    $oristatusterkini = $request->statusterkini;
            } else {
                $oristatusterkini = "";
            }

            if ($request->submit_d5_date != "") {
                if (!$request->submit_d5_date) {
                    $orisubmit_d5 = $listdata->submit_d5_date;
                } else
                    $orisubmit_d5 = $submit_d5_date_string;
            } else {
                $orisubmit_d5 = null;
            }

            if ($request->return_d5_date != "") {
                if (!$request->return_d5_date) {
                    $orireturn_d5 = $listdata->return_d5_date;
                } else
                    $orireturn_d5 = $return_d5_date_string;
            } else {
                $orireturn_d5 = null;
            }

            if ($request->submit_apply_date != "") {
                if (!$request->submit_apply_date) {
                    $orisubmit_cdc = $listdata->submit_cdc_date;
                } else
                    $orisubmit_cdc = $submit_cdc_date_string;
            } else {
                $orisubmit_cdc = null;
            }

            if ($request->result_d5 != "") {
                if (!$request->result_d5) {
                    $oriresult_d5 = $listdata->d5_result;
                } else
                    $oriresult_d5 = $request->result_d5;
            } else {
                $oriresult_d5 = "";
            }

            if ($request->apply_type != "") {
                if (!$request->apply_type) {
                    $oriapply_type = $listdata->apply_type;
                } else
                    $oriapply_type = $request->apply_type;
            } else {
                $oriapply_type = "";
            }


            if ($request->isMethod("POST")) {
                $updateData = [
                    'perkara' => $oriSubject,
                    'remarks' => $oriRemarks,
                    'pemohon' => $oripemohon,
                    'tindakan' => $oritindakan,
                    'completed_by' => $oriprepared,
                    'received_date' => $orireceived_date,
                    'received_date_apply' => $orireceived_date_apply,
                    'data_required_date' => $oridata_date_req,
                    'status' => $oristatusterkini,
                    'submit_d5_date' => $orisubmit_d5,
                    'return_d5_date' => $orireturn_d5,
                    'submit_cdc_date' => $orisubmit_cdc,
                    'apply_type' => $oriapply_type,
                    'd5_result' => $oriresult_d5,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_report_summary')
                    ->where('id', $request->editid)
                    ->update($updateData);
            }

            if ($request->editid != null) {
                $listdata = $this->listSummary($request->editid);
                if ($listdata->status != 'Selesai') {
                    $current_date = Carbon::now()->format('Y-m-d');
                    $diff = abs(strtotime($current_date) - strtotime($submit_cdc_date_string));
                    $years = floor($diff / (365 * 60 * 60 * 24));
                    $days = floor(($diff - $years * 365 * 60 * 60 * 24) / (60 * 60 * 24));
                } else
                    $days = null;

                if ($request->isMethod("POST")) {
                    $updateData = [
                        'bilangan_hari_process' => $days,
                        'changed_by' => $user,
                        'changed_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_report_summary')
                        ->where('id', $request->editid)
                        ->update($updateData);
                }

                if ($listdata->status != 'Selesai' && $received_date_string != null) {
                    $diff = abs(strtotime($received_date_string) - strtotime($submit_cdc_date_string));
                    $years = floor($diff / (365 * 60 * 60 * 24));
                    $daysAceepts = floor(($diff - $years * 365 * 60 * 60 * 24) / (60 * 60 * 24));
                    if ($request->isMethod("POST")) {
                        $updateData = [
                            'date_count_process' => $daysAceepts,
                            'changed_by' => $user,
                            'changed_date' => Carbon::now()
                        ];
                        DB::connection('mysql_ep_prod_support')->table('ps_report_summary')
                            ->where('id', $request->editid)
                            ->update($updateData);
                    }
                }
            }
            return back();
        }

        if ($request->subject != null) {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_report_summary')->insert([
                ['perkara' => $request->subject, 'remarks' => $request->remarks, 'pemohon' => $request->pemohon, 'tindakan' => $request->tindakan, 'completed_by' => $request->prepared_by, 'received_date_apply' => $received_date_apply, 'received_date' => $received_date_string, 'data_required_date' => $required_date_string, 'status' => $request->statusterkini, 'submit_d5_date' => $submit_d5_date_string, 'return_d5_date' => $return_d5_date_string, 'submit_cdc_date' => $submit_cdc_date_string, 'd5_result' => $request->result_d5, 'apply_type' => $request->apply_type, 'created_by' => $user, 'created_date' => carbon::now()],
            ]);
            return back();
        }

        return view('prod_support.rpt.report_summary', [
            'listTindakan' => $listTindakan,
            'listPengguna' => $listPengguna,
            'listReport' => $listReport,
            'listStatus' => $listStatus,
        ]);
    }

    protected function detailMoreInTable($id)
    {
        $listReport = $this->listReportSummaryInDetails($id);
        $html = "<table  id='details_table' class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Tindakan</th>
                            <th class='text-center'>Disediakan Oleh (CDC)</th>
                            <th class='text-center'>Bilangan Hari Proses</th>
                            <th class='text-center'>Jumlah Hari Data Diproses</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($listReport as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->tindakan</strong></td> 
                        <td class='text-center'><strong>$value->completed_by</strong></td>
                        <td class='text-center'><strong>$value->bilangan_hari_process</strong></td>
                        <td class='text-center'><strong>$value->date_count_process</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }

    public function reportAddScript(Request $request)
    {
        $reportid = request()->reportid;
        $scriptName = rtrim(request()->scriptName, '.sql');
        $script = request()->script;
        if (strlen($reportid) == 0 || strlen($script) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'reportId or script is required'
            );
        } else {
            $encodescript = base64_decode($script);
            $user = $user = auth()->user()->first_name;
            DB::connection('mysql_ep_prod_support')->table('ps_report_script')->insert([
                [
                    'report_id' => $reportid,
                    'name' => $scriptName . '.sql',
                    'script' => $encodescript,
                    'type_format' => 'SQL File',
                    'created_by' => $user,
                    'created_date' => Carbon::Now()
                ],
            ]);

            $result = array(
                'status' => 'Success',
                'description' => 'Insert'
            );
        }
        return response()->json($result, 200);
    }

    public function reportAddExcel(Request $request)
    {
        $reportid = request()->reportid;
        $file = $request->file('upload_excel_file');
        $path_name = $file->getRealPath();
        $file_name = $file->getClientOriginalName();
        $file_type = substr($file_name, strrpos($file_name, '.') + 1);
        $file_type_name = null;
        if ($file_type == 'csv' || $file_type == 'xlsx' || $file_type == 'xls' || $file_type == 'xml') {
            $file_type_name = 'Microsoft Excel Worksheet';
        } else if ($file_type == 'doc' || $file_type == 'docm' || $file_type == 'docx') {
            $file_type_name = 'Microsoft Word Document';
        } else if ($file_type == 'pdf') {
            $file_type_name = 'Adobe Acrobat(PDF)';
        } else {
            $file_type_name = 'Other';
        }
        $folder_path = storage_path('prod_support/report/' . $reportid);
        Storage::makeDirectory($folder_path);
        $path = Storage::putFileAs('prod_support/report/' . $reportid, $file, $file_name);

        $user = $user = auth()->user()->first_name;

        if ($request != null) {
            DB::connection('mysql_ep_prod_support')->table('ps_report_script')->insert([
                [
                    'report_id' => $reportid,
                    'name' => $file_name,
                    'folder_path' => $path_name,
                    'type_format' => $file_type_name,
                    'created_by' => $user,
                    'created_date' => Carbon::Now()
                ],
            ]);

            $result = array(
                'status' => 'Success',
                'description' => 'Insert'
            );
            return response()->json($result, 200);
        }
    }

    protected function listAllDocumentByReportId($reportid)
    {
        $listScript = $this->listScriptByReport($reportid);
        if ($listScript != null) {
            $html = "<table  id='report_document_datatable' class='table table-bordered table-vcenter'>
                    <thead>
                            <tr>
                            <th class='text-center'>No</th>
                            <th class='text-center'>Nama Dokumen</th>
                            <th class='text-center'>Jenis Dokumen</th>
                            <th class='text-center'>Muat Naik Oleh</th>
                            <th class='text-center'>Tarikh Muat Naik</th>
                            <th class='text-center'>Muat Turun</th>
                        </tr>
                    </thead>";
            $html = $html . "<tbody>";
            foreach ($listScript as $rowcount => $value) {
                $data = "
                    <tr>
                    <td class='text-center'>" . ($rowcount + 1) . "</td>
                        <td class='text-center'>$value->name</td>
                        <td class='text-center'>$value->type_format</td>
                        <td class='text-center'>$value->created_by</td>
                        <td class='text-center'>$value->created_date</td>
                        <td class='text-center'><a href = '/prod-support/reporting/download_file/{$value->script_id}' data-toggle='tooltip' title='Download' style='alignment-adjust: middle' class='btn btn-xs btn-success'><i class='fa fa-download'></i></a>
                            <a id='deletebutton' docid='{$value->script_id}' file_name ='{$value->name}' data-toggle='tooltip' title='Delete' style='alignment-adjust: middle' class='btn btn-xs btn-danger'><i class='fa fa-times'></i></a></td>
                    </tr>";
                $html = $html . $data;
            }
            $html = $html . "<tbody></table>";
            return $html;
        }
    }

    public function deleteDocumentforReporting($docid)
    {
        if ($docid != null) {
            DB::connection('mysql_ep_prod_support')->table('ps_report_script')
                ->where('script_id', $docid)
                ->delete();
            return back();
        }
    }

    public function deleteReporting(Request $request, $idno)
    {
        $user = $user = auth()->user()->first_name;
        if ($request->isMethod("GET")) {
            $updateData = [
                'status' => 'Deleted',
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_report_summary')
                ->where('id', $idno)
                ->update($updateData);
        }
        return back();
    }

    public function downloadFile($scriptid)
    {
        $listData = $this->getFileForDownload($scriptid);
        if ($listData[0] && count($listData) > 0) {
            if ($listData[0]->type_format == 'SQL File') {
                $headers = ['Content-Type' => 'text/plain', 'Cache-Control' => 'max-age=0',];
                $fileName = $listData[0]->name;
                $sqlPath = storage_path('app/prod_support/report/' . $listData[0]->report_id . '/' . $fileName);
                File::put($sqlPath, $listData[0]->script);
            } else if ($listData[0]->type_format == 'Microsoft Excel Worksheet' || $listData[0]->type_format == 'Microsoft Word Document' || $listData[0]->type_format == 'Adobe Acrobat(PDF)') {
                $headers = ['Content-Type' => 'application/vnd.ms-excel', 'Cache-Control' => 'max-age=0',];
                $fileName = $listData[0]->name;
                $sqlPath = storage_path('app/prod_support/report/' . $listData[0]->report_id . '/' . $fileName);
            } else {
                return 'Jenis Dokumen semasa muat naik tidak tepat!';
            }
        }
        return response()->download($sqlPath, $fileName, $headers);
    }

    public function listDataByFilterDateApply($carian)
    {
        $listData = null;
        if ($carian != 'null') {
            $listData = $this->getDetailsByDateApply($carian);
        } else {
            $listData = $this->getDetailsByDateApplyFull();
        }
        return response()->json($listData, 200);
    }

    public function downloadReport()
    {
        $listdata = $this->downloadDetailsCurrentYear();
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Senarai Permohonan Data';

            Excel::create($fileName, function ($excel) use ($collectlistReporting) {
                $excel->setTitle('Senarai Permohonan Data');
                $tryje = 1;
                $excel->sheet('Senarai Permohonan Data' . $tryje, function ($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@',
                            'G' => '@',
                        ));

                        $sheet->row(1, array(
                            'Bil', 'Perkara', 'Catatan', 'Pemohon', 'Tarikh Terima Permohonan', 'Tarikh Data Diperlukan', 'Status'
                        ));

                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:G1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $data => $obj) {
                            $sheet->row(
                                $count,
                                array(
                                    ++$data,
                                    $obj->perkara,
                                    $obj->remarks,
                                    $obj->pemohon,
                                    $obj->received_date_apply,
                                    $obj->data_required_date,
                                    $obj->status
                                )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function getAdminEpActivity()
    {
        $logList = null;
        $listActivity = null;
        $start_date = null;
        $end_date = null;
        $log_action = $this->listLogAction();
        if($log_action != null){
            $logList = $log_action;
        }
        return view('prod_support.list_activity_admin_ep', [
            'listActivity' => $listActivity,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'logList' => $logList,
        ]);
    }

    public function listAdminEpActivity(Request $request)
    {
        $user = auth()->user()->first_name;
        $start_date1 = carbon::parse($request->startdate);
        $start_date = $start_date1->format('Y-m-d');
        $end_date1 = carbon::parse($request->enddate);
        $end_date = $end_date1->format('Y-m-d');
        $listActivity = $this->listActivityByAdminEp($start_date, $end_date);

        DB::connection('mysql_ep_support')->table('ep_report_log_action')->insert([
            ['log_user_name' => $user, 'log_date_from' => $start_date, 'log_date_to' => $end_date, 'log_type' => 'E', 'log_created_date' => Carbon::now()],
        ]);

        $log_action = $this->listLogAction();
        if($log_action != null){
            $logList = $log_action;
        }
        return view('prod_support.list_activity_admin_ep', [
            'listActivity' => $listActivity,
            'start_date' => $start_date,
            'end_date' => $end_date,
            'logList' => $logList,
        ]);
    }

    public function downloadListAdminEpActivity($startDate, $endDate)
    {
        $start_date1 = carbon::parse($startDate);
        $start_date = $start_date1->format('Y-m-d');
        $end_date1 = carbon::parse($endDate);
        $end_date = $end_date1->format('Y-m-d');
        $listActivity = $this->downloadListActivityByAdminEp($start_date, $end_date);

        if ($listActivity != null) {
            $collectlistReporting = collect($listActivity)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'List Activity By Ep Admin';

            Excel::create($fileName, function ($excel) use ($collectlistReporting) {
                $excel->setTitle('List Activity By Ep Admin');

                $excel->sheet('List Activity By Ep Admin', function ($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@',
                            'G' => '@',
                            'H' => '@',
                        ));

                        $sheet->row(1, array(
                            'No', 'Ep Admin', 'Module/Document', 'Document Number', 'Action Desc', 'Action By Id', 'Action Date', 'Document Status'
                        ));

                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:G1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $data => $obj) {
                            $sheet->row(
                                $count,
                                array(
                                    ++$data,
                                    $obj->ep_admin,
                                    $obj->module_document,
                                    $obj->document_number,
                                    $obj->action_desc,
                                    $obj->actioned_by_id,
                                    $obj->action_date,
                                    $obj->document_status,
                                )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function integrationReporteP(Request $request){
        $listSSM = $listmyIdentity = null;
        
        switch ($request->by_info) {
            case 'ssm':
                $listSSM = $this->findIntegrationSSM($request->by_year);
                break;
            case 'myidentity':
                $listmyIdentity = $this->findIntegrationmyIdentity($request->by_year);
                break;
        }
        
        return view('prod_support.rpt.list_integration_report_ep', [
            'filterBy' => $request->by_info,
            'filterYear' => $request->by_year,
            'listSSM' => $listSSM,
            'listmyIdentity' => $listmyIdentity,
        ]);
    }
    
}
