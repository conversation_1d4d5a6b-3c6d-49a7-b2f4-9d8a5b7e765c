<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PomService {

    public function getSlaCsPoms() {
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::now()->format('Y-m-d');

        $results = DB::connection('mysql_poms')
                        ->table('sla_cs')
                        ->whereBetween(DB::raw("DATE(created_date)"), [
            $startOfMonth,
            $yesterday
        ])->select('sla_cs.*')->get();
        
        return $results;
    }
    
    public function getSlaItCoordPoms() {
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::yesterday()->format('Y-m-d');

        $results = DB::connection('mysql_poms')
                        ->table('sla_itcoord')
                        ->whereBetween(DB::raw("DATE(itcoord_case_created)"), [
            $startOfMonth,
            $yesterday
        ])->select('sla_itcoord.*')->get();
        
        return $results;
    }
    
    public function getSlaItSpecPoms() {
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::yesterday()->format('Y-m-d');

        $results = DB::connection('mysql_poms')
                        ->table('sla_itspec')
                        ->whereBetween(DB::raw("DATE(itspec_case_created)"), [
            $startOfMonth,
            $yesterday
        ])->select('sla_itspec.*')->get();
        
        return $results;
    }
    
    public function getSlaSeverityPoms() {
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::yesterday()->format('Y-m-d');

        $results = DB::connection('mysql_poms')
                        ->table('sla_itseverity')
                        ->whereBetween(DB::raw("DATE(itseverity_case_created)"), [
            $startOfMonth,
            $yesterday
        ])->select('sla_itseverity.*')->get();
        
        return $results;
    }

    public function getSlaS4Poms() {
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::yesterday()->format('Y-m-d');

        $results = DB::connection('mysql_poms')
                        ->table('sla_byapprover')
                        ->whereBetween(DB::raw("DATE(itapprover_case_created)"), [
            $startOfMonth,
            $yesterday
        ])->select('sla_byapprover.*')->get();
        
        return $results;
    }

    public function getMonthlySlaCounts($year, $month)
    {
        return [
            'YEAR' => $year,
            'MONTH' => $month,
            'ACK_COUNT' => $this->getItCoordCount($year, $month),
            'RIT_COUNT' => $this->getItSpecCount($year, $month),
            'S123_COUNT' => $this->getItSeverityCount($year, $month),
            'ACK_DUPLICATE' => $this->getItCoordDuplicateCount($year, $month),
            'RIT_DUPLICATE' => $this->getItSpecDuplicateCount($year, $month),
            'S123_DUPLICATE' => $this->getItSeverityDuplicateCount($year, $month)
        ];
    }

    public function getCaseListItCoord($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itcoord
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itcoord_completed_datetime) = $year
                                AND MONTH(itcoord_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->get();
    }

    public function getCaseListItSpec($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itspec
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itspec_completed_datetime) = $year
                                AND MONTH(itspec_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->get();
    }

    public function getCaseListItSeverity($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itseverity
                            WHERE YEAR(itseverity_case_created) = $year
                                AND MONTH(itseverity_case_created) = $month
                                AND YEAR(itseverity_insert_data_datetime) = $year
                                AND MONTH(itseverity_insert_data_datetime) = $month
                                AND YEAR(itseverity_completed_datetime) = $year
                                AND MONTH(itseverity_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->get();
    }

    private function getItCoordCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT *
                            FROM sla_itcoord
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itcoord_completed_datetime) = $year
                                AND MONTH(itcoord_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->count();
    }

    private function getItSpecCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT *
                            FROM sla_itspec
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itspec_completed_datetime) = $year
                                AND MONTH(itspec_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->count();
    }

    private function getItSeverityCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT *
                            FROM sla_itseverity
                            WHERE YEAR(itseverity_case_created) = $year
                                AND MONTH(itseverity_case_created) = $month
                                AND YEAR(itseverity_insert_data_datetime) = $year
                                AND MONTH(itseverity_insert_data_datetime) = $month
                                AND YEAR(itseverity_completed_datetime) = $year
                                AND MONTH(itseverity_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->count();
    }

    private function getItCoordDuplicateCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT case_number
                            FROM sla_itcoord
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itcoord_completed_datetime) = $year
                                AND MONTH(itcoord_completed_datetime) = $month
                                AND deleted = 0
                            GROUP BY case_number
                            HAVING COUNT(*) > 1
                            ) AS duplicate_cases"))
            ->count();
    }

    private function getItSpecDuplicateCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT case_number
                            FROM sla_itspec
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itspec_completed_datetime) = $year
                                AND MONTH(itspec_completed_datetime) = $month
                                AND deleted = 0
                            GROUP BY case_number
                            HAVING COUNT(*) > 1
                            ) AS duplicate_cases"))
            ->count();
    }

    private function getItSeverityDuplicateCount($year, $month)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT case_number
                            FROM sla_itseverity
                            WHERE YEAR(itseverity_case_created) = $year
                                AND MONTH(itseverity_case_created) = $month
                                AND YEAR(itseverity_insert_data_datetime) = $year
                                AND MONTH(itseverity_insert_data_datetime) = $month
                                AND YEAR(itseverity_completed_datetime) = $year
                                AND MONTH(itseverity_completed_datetime) = $month
                                AND deleted = 0
                            GROUP BY case_number
                            HAVING COUNT(*) > 1
                            ) AS duplicate_cases"))
            ->count();
    }

    public function getDuplicateCaseListItCoord($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itcoord
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itcoord_completed_datetime) = $year
                                AND MONTH(itcoord_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->whereIn('case_number', function ($query) use ($year, $month) {
                $query->select('case_number')
                    ->from(DB::raw("(SELECT case_number
                                    FROM sla_itcoord
                                    WHERE YEAR(itcoord_insert_data_datetime) = $year
                                    AND MONTH(itcoord_insert_data_datetime) = $month
                                    AND YEAR(itcoord_completed_datetime) = $year
                                    AND MONTH(itcoord_completed_datetime) = $month
                                    AND deleted = 0
                                    GROUP BY case_number
                                    HAVING COUNT(*) > 1
                                    ) AS duplicate_cases"));
            })
            ->orderBy('case_number')
            ->get();
    }

    public function getDuplicateCaseListItSpec($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itspec
                            WHERE YEAR(itcoord_insert_data_datetime) = $year
                                AND MONTH(itcoord_insert_data_datetime) = $month
                                AND YEAR(itspec_completed_datetime) = $year
                                AND MONTH(itspec_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->whereIn('case_number', function ($query) use ($year, $month) {
                $query->select('case_number')
                    ->from(DB::raw("(SELECT case_number
                                    FROM sla_itspec
                                    WHERE YEAR(itcoord_insert_data_datetime) = $year
                                    AND MONTH(itcoord_insert_data_datetime) = $month
                                    AND YEAR(itspec_completed_datetime) = $year
                                    AND MONTH(itspec_completed_datetime) = $month
                                    AND deleted = 0
                                    GROUP BY case_number
                                    HAVING COUNT(*) > 1
                                    ) AS duplicate_cases"));
            })
            ->orderBy('case_number')
            ->get();
    }

    public function getDuplicateCaseListItSeverity($month, $year)
    {
        return DB::connection('mysql_poms')
            ->table(DB::raw("(SELECT * FROM sla_itseverity
                            WHERE YEAR(itseverity_case_created) = $year
                                AND MONTH(itseverity_case_created) = $month
                                AND YEAR(itseverity_insert_data_datetime) = $year
                                AND MONTH(itseverity_insert_data_datetime) = $month
                                AND YEAR(itseverity_completed_datetime) = $year
                                AND MONTH(itseverity_completed_datetime) = $month
                                AND deleted = 0
                            ) AS filtered"))
            ->whereIn('case_number', function ($query) use ($year, $month) {
                $query->select('case_number')
                    ->from(DB::raw("(SELECT case_number
                                    FROM sla_itseverity
                                    WHERE YEAR(itseverity_case_created) = $year
                                    AND MONTH(itseverity_case_created) = $month
                                    AND YEAR(itseverity_insert_data_datetime) = $year
                                    AND MONTH(itseverity_insert_data_datetime) = $month
                                    AND YEAR(itseverity_completed_datetime) = $year
                                    AND MONTH(itseverity_completed_datetime) = $month
                                    AND deleted = 0
                                    GROUP BY case_number
                                    HAVING COUNT(*) > 1
                                    ) AS duplicate_cases"));
            })
            ->orderBy('case_number')
            ->get();
    }
}
