@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
@if($connectionType === 'ep')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/ep') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/ep') }}">Receiver</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/receivergroup/ep') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@elseif($connectionType === 'crm')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/crm') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/crm') }}">Receiver</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/receivergroup/crm') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@endif
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="block">
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        @if($connectionType === 'ep')
            <h3>EP NOTIFICATION: LIST OF GROUP RECEIVERS</h3>
        @elseif($connectionType === 'crm')
            <h3>CRM JBAL NOTIFICATION: LIST OF GROUP RECEIVERS</h3>
        @endif
        <table id="basic-datatable" class="table table-vcenter table-striped table-bordered">
            <thead>
                <tr>
                    <th class="text-center">GROUP NAME</th>
                    <th class="text-center">GROUP DESCRIPTION</th>
                    <th class="text-center">RECEIVER NAME</th>
                    <th class="text-center">STATUS</th>
                    <th class="text-center">ACTION</th>
                </tr>
            </thead>
            @if($listReceiverGroup != null)
            <tbody>
                @foreach($listReceiverGroup as $obj)
                <tr>
                    <td class="text-left">{{ $obj->group_name }}</td>
                    <td class="text-left">{{ $obj->group_description }}</td>
                    <td class="text-left">{{ $obj->receiver_name }}</td>
                    <td class="text-left">{{ $obj->group_status }}</td>
                    <td class="text-center action_receiver">
                        <div class="btn-group btn-group-xs">
                            <a class="btn btn-yellow update_receiver_group"  title="" data-original-title="Update"
                               href="#modal_update_receiver_group" data-toggle="modal" data-id="{{ $obj->receiver_group_id }}" data-status="{{ $obj->group_status }}"
                               data-group="{{ $obj->group_name }}" data-receiverid="{{ $obj->receiver_id }}" data-receivername="{{ $obj->receiver_name }}"
                               data-description="{{$obj->group_description}}" data-connection="{{$connectionType}}"> Update</a>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
            @endif
        </table>
    </div>
    <button class="btn btn-sm btn-green pull-right add-receiver-group" style="margin-right: 10px">Add Receiver Group </button>
    <br/><br/>
</div>
<div id="modal_update_receiver_group" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> UPDATE RECEIVER GROUP </h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <input type="hidden" id="update_receiver_group_id" name="update_receiver_group_id"/>
                            <input type="hidden" id="update_connection" name="update_connection"/>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group Name </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="update_group_name" name="update_group_name" value=""/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group Description </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="update_group_description" name="update_group_description" value=""/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Receiver Name </label>
                                <div class="input-group">
                                    @if($listReceiver != null)
                                    <select id="update_receiver_name" name="update_receiver_name" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        @foreach($listReceiver as $obj)
                                        <option value="{{$obj->receiver_name}}" @if(old('update_receiver_name') == $obj->receiver_name) selected @endif>{{$obj->receiver_name}}</option>
                                        @endforeach
                                    </select>
                                    @endif                                    
                                </div> 
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Status </label>
                                <div class="input-group">
                                    <select id="update_group_status" name="update_group_status" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        <option value="Active" >Active</option>
                                        <option value="In-Active" >In-Active</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_receiver_group"><i class="gi gi-ok_2"></i> Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modal_add_receiver_group" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> ADD NEW RECEIVER GROUP </h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <input type="hidden" id="receiver_group_id" name="receiver_group_id"/>
                            <input type="hidden" id="connections" name="connections" value="{{$connectionType}}"/>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group Name </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="add_group_name" name="add_group_name" value=""/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group Description </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="add_group_description" name="add_group_description" value=""/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Receiver Name </label>
                                <div class="input-group">
                                    @if($listReceiver != null)
                                    <select id="add_group_receiver" name="add_group_receiver" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        @foreach($listReceiver as $obj)
                                        <option value="{{$obj->id}}" >{{$obj->receiver_name}}</option>
                                        @endforeach
                                    </select>
                                    @endif                                    
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_add_receiver_group"><i class="gi gi-ok_2"></i> Add</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
<!-- END Content -->
@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });</script>
<script>
    $('td.action_receiver').on("click", 'a.update_receiver_group', function () {
        var receiverGroupId = $(this).attr('data-id');
        var groupName = $(this).attr('data-group');
        var groupDescription = $(this).attr('data-description');
        var receiverName = $(this).attr('data-receivername');
        var groupStatus = $(this).attr('data-status');
        var connection = $(this).attr('data-connection');
        $("#update_receiver_group_id").val(receiverGroupId);
        $("#update_group_name").val(groupName);
        $("#update_group_description").val(groupDescription);
        $("#update_receiver_name").val(receiverName);
        $("#update_group_status").val(groupStatus);
        $("#update_connection").val(connection);
    });

    $('div.form-actions').on("click", 'button.action_update_receiver_group', function () {
        var receiverGroupId = $("#update_receiver_group_id").val();
        var groupName = $("#update_group_name").val();
        var groupDescription = $("#update_group_description").val();
        var receiverName = $("#update_receiver_name option:selected").val();
        var groupStatus = $("#update_group_status option:selected").val();
        var csrf = $("input[name=_token]").val();
        var action = 'update';
        var connection = $("#update_connection").val();

        $('#modal_update_receiver_group').modal('hide');

        $.ajax({
            url: "/wsnotify/receivergroup/"+connection,
            type: "POST",
            data: {"_token": csrf, "action": action, "receiverGroupId": receiverGroupId, "groupDescription":groupDescription, 
                "groupName": groupName, "receiverName": receiverName, "groupStatus": groupStatus},
        }).done(function (resp) {
            console.log(resp);
            if (resp.status === 'success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Successfully Updated!");
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Updated Failed! Please try again.");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
    $('button.add-receiver-group').on("click", function () {
        $('#modal_add_receiver_group').modal('show');

        $("#add_group_receiver").bind("change", function () {
            var typeGroup = $(this).find(":selected").val();

            if (typeGroup === 'personal') {
                $('#div_group').hide();
                $('#div_personal').show();

            } else {
                $('#div_personal').hide();
                $('#div_group').show();
            }
        });
    });
    $('div.form-actions').on("click", 'button.action_add_receiver_group', function () {
        var groupName = $("#add_group_name").val();
        var groupDescription = $("#add_group_description").val();
        var receiverId = $("#add_group_receiver option:selected").val();
        var csrf = $("input[name=_token]").val();
        var action = 'add';
        var connections = $("#connections").val();

        $('#modal_add_receiver_group').modal('hide');

        $.ajax({
            url: "/wsnotify/receivergroup/"+connections,
            type: "POST",
            data: {"_token": csrf, "action": action,
                "groupName": groupName, "receiverId": receiverId, "groupDescription": groupDescription},
        }).done(function (resp) {
            console.log(resp);
            if (resp.status === 'success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Successfully Add New Receiver Group!");
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Add Failed! Please try again.");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
</script>
@endsection