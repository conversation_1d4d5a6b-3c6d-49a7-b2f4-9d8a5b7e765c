<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;

class HandleSmRoleDuplicateRoleSameUserOrgId extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sm-role-user-duplicate';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To find role duplicate inserted and to update record status from 0 to 9.  This to make sure there will be no error when eP changed record status from 1 to 0. There has a contraint record for role_code and record_status';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $script = " 
                    SELECT
                    USER_ORG_ID,
                    role_code,
                    count(*) ,
                    max(CREATED_DATE),
                    (SELECT user_role_id FROM  pm_user_role  x WHERE x.USER_ORG_ID = r.USER_ORG_ID AND x.role_code = r.role_code AND x.record_status = 0)  AS user_role_id_inactive
                FROM
                    pm_user_role r
                WHERE
                    role_code IN ('MOF_SUPPLIER_ADMIN', 'BASIC_SUPPLIER_ADMIN', 'SUPPLIER_TEMP', 'BASIC_SUPPLIER_USER', 'MOF_SUPPLIER_USER','FL_USER','CM_USER') 
                    AND NOT EXISTS (SELECT 1 FROM pm_user_role x WHERE x.USER_ORG_ID = r.USER_ORG_ID AND x.role_code = r.role_code AND x.record_status = 9 )
                GROUP BY
                    USER_ORG_ID,
                    role_code
                HAVING
                    count(*) > 1
                   
                ";
            $listRecords = DB::connection('oracle_nextgen_rpt')->select($script);
            
            MigrateUtils::logDump('Total records roles have duplicate more than one: '.count($listRecords));

            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listRecords) { 
                $listId = collect($listRecords)->pluck('user_role_id_inactive')->toArray();
                //dump($listId);
                DB::connection('oracle_nextgen_fullgrant')->table('PM_USER_ROLE')
                        ->whereIn('user_role_id',$listId)
                        ->update(['record_status'=> 9,'changed_date'=>Carbon::now()]);
                MigrateUtils::logDump('Done updated records in PM_USER_ROLE.');
            }); 
            
            $actionName = 'PatchData';
            $actionType = 'Script';
            
            $dataParam = collect();
            $dataParam->put("action","Update records in table PM_USER_ROLE");
            $dataParam->put("table","PM_USER_ROLE");
            $dataParam->put("list_user_role_id",collect($listRecords)->pluck('user_role_id_inactive')->toArray());
            $dataParam->put("criteria","Duplicate role inserted in PM_USER_ROLE.");
            $dataParam->put("remark","List of duplicate role to be update record_status from 0 to 9. This to make sure there will be no error when eP changed record status from 1 to 0.");
            $dataParam->put("script_sql",$script);
            //dump($dataParam);
            $dataLog = collect();
            $dataLog->put("remark","List of duplicate role to be update record_status from 0 to 9. This to make sure there will be no error when eP changed record status from 1 to 0.");
            $dataLog->put("data",$listRecords);
            $dataLog->put("script_sql",$script);
            //dump($dataLog);
            EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");
            MigrateUtils::logDump('Completed');
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
