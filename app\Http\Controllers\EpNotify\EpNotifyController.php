<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\EpNotify;

use App\Http\Controllers\Controller;
use App\Services\EpNotify\EpNotifyService;
use Illuminate\Http\Request;
use Log;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class EpNotifyController extends Controller
{

    public static $statusPending = 0;
    public static $statusCompleted = 1;
    public static $statusError = 2;
    public static $statusDescCompleted = 'Completed';
    public static $statusDescFailed = 'Failed';
    public static $sentPending = 0;
    public static $sentCompleted = 1;
    public static $epNotifyConnection = 'mysql_notify';
    public static $crmNotifyConnection = 'mysql_crm_notify';

    public static function EpNotifyService()
    {
        return new EpNotifyService;
    }

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dashboard($connectionType)
    {

        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }
        $pendingNotification = self::EpNotifyService()->getPendingNotification($dbConnection);
        $lastJobFailed = self::EpNotifyService()->getJobStatus($dbConnection, self::$statusDescFailed);
        $lastJobCompleted = self::EpNotifyService()->getJobStatus($dbConnection, self::$statusDescCompleted);
        $whatsAppStatus = self::EpNotifyService()->getWhatsAppStatus($dbConnection);

        return view('wsnotify.dashboard', [
            'pendingNotification' => $pendingNotification,
            'lastJobFailed' => $lastJobFailed,
            'lastJobCompleted' => $lastJobCompleted,
            'whatsAppStatus' => $whatsAppStatus,
            'connectionType' => $connectionType
        ]);
    }

    public function reminder($connectionType, Request $request)
    {
        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }

        $currentUserId = Auth::user()->getAuthIdentifier();

        if ($request->isMethod('get')) {
            $reminders = DB::connection($dbConnection)->table('reminders')->where('created_by', $currentUserId)->get();

            foreach ($reminders as $reminder) {
                $receiverGroups = json_decode($reminder->receiver_group);
                $groupNames = [];
                foreach ($receiverGroups as $id) {
                    $group = DB::connection($dbConnection)->table('receiver_group')->where('id', $id)->first();
                    if ($group) {
                        $groupNames[$group->group_name] = true;
                    }
                }
                $reminder->receiver_group = array_keys($groupNames);
            }

            $receivers_group = DB::connection($dbConnection)
                ->table('receiver_group')
                ->where('group_status', 'Active')
                ->select('group_name')
                ->distinct()
                ->get();
            $remindTypes = ['once', 'periodic'];
            $remindIntervals = ['', 'daily', 'weekly', 'monthly', 'yearly', 'custom'];
            $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            $months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December'];
            $statuses = ['Active', 'Completed'];

            foreach ($reminders as $reminder) {
                if ($reminder->remind_type == 'once') {
                    $reminder->schedule = 'Once at ' . date('F j, Y, g:i A', strtotime($reminder->next_remind_time));
                } else {
                    switch ($reminder->remind_interval) {
                        case 'daily':
                            $reminder->schedule = 'Daily';
                            break;
                        case 'weekly':
                            $reminder->schedule = 'Every ' . $reminder->remind_day;
                            break;
                        case 'monthly':
                            $reminder->schedule = 'Every month on the ' . $reminder->remind_day;
                            break;
                        case 'yearly':
                            $reminder->schedule = 'Every year on ' . $reminder->remind_month . ' ' . $reminder->remind_day;
                            break;
                        case 'custom':
                            $reminder->schedule = 'Every ' . $reminder->custom_interval . ' minutes';
                            break;
                    }
                    $reminder->schedule .= ' at ' . date('g:i A', strtotime($reminder->remind_hour . ':' . $reminder->remind_minute));
                }
            }

            return view('wsnotify.reminder', [
                'reminders' => $reminders,
                'receivers_group' => $receivers_group,
                'remindTypes' => $remindTypes,
                'remindIntervals' => $remindIntervals,
                'days' => $days,
                'months' => $months,
                'statuses' => $statuses,
                'connectionType' => $connectionType
            ]);
        } else if ($request->isMethod('post')) {
            $data = $request->all();

            if (isset($data['create_new_receiver']) && $data['create_new_receiver'] == 'on') {
                $phoneNumbers = array_map('intval', $data['phone_number']);

                foreach ($phoneNumbers as $phoneNumber) {
                    $receiverGroup = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $phoneNumber)->first();

                    if (!$receiverGroup) {
                        $receiver = DB::connection($dbConnection)->table('receiver')->where('receiver_name', $phoneNumber)->first();

                        if (!$receiver) {
                            $this->addReceiver($dbConnection, $phoneNumber);
                            $receiver = DB::connection($dbConnection)->table('receiver')->where('receiver_name', $phoneNumber)->first();
                        }

                        $this->addReceiverGroup($dbConnection, $phoneNumber, $receiver->id, $phoneNumber);
                        $receiverGroup = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $phoneNumber)->first();
                    }
                    $data['receiver_group'][] = $receiverGroup->id;
                }
                unset($data['create_new_receiver']);
                unset($data['phone_number']);
            }

            unset($data['_token']);
            unset($data['schedule']);

            $validator = Validator::make($data, [
                'title' => 'required|string',
                'description' => 'required|string',
                'receiver_group' => 'required|array',
                'detail_notification' => 'required|string',
                'remind_type' => 'required|string',
                'remind_interval' => 'nullable|string',
                'custom_interval' => 'nullable|string',
                'remind_day' => 'nullable|string',
                'remind_month' => 'nullable|string',
                'next_remind_time' => 'required|date',
                'final_remind_time' => 'nullable|date',
                'expired_at' => 'nullable|date',
                'status' => 'required|string',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $groupNames = $data['receiver_group'];
            $groupIds = [];
            foreach ($groupNames as $groupName) {
                $groups = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $groupName)->get();
                foreach ($groups as $group) {
                    $groupIds[] = $group->id;
                }
            }
            $data['receiver_group'] = json_encode($groupIds);
            $data['created_by'] = $currentUserId;
            $data['updated_by'] = $currentUserId;
            $data['created_at'] = Carbon::now();
            $data['updated_at'] = Carbon::now();

            DB::connection($dbConnection)->table('reminders')->insert($data);

            return back()->with('message', 'Reminder added successfully');
        } else if ($request->isMethod('put')) {
            $data = $request->all();

            if (isset($data['create_new_receiver']) && $data['create_new_receiver'] == 'on') {
                $phoneNumbers = array_map('intval', $data['phone_number']);

                foreach ($phoneNumbers as $phoneNumber) {
                    $receiverGroup = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $phoneNumber)->first();

                    if (!$receiverGroup) {
                        $receiver = DB::connection($dbConnection)->table('receiver')->where('receiver_name', $phoneNumber)->first();

                        if (!$receiver) {
                            $this->addReceiver($dbConnection, $phoneNumber);
                            $receiver = DB::connection($dbConnection)->table('receiver')->where('receiver_name', $phoneNumber)->first();
                        }

                        $this->addReceiverGroup($dbConnection, $phoneNumber, $receiver->id, $phoneNumber);
                        $receiverGroup = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $phoneNumber)->first();
                    }
                    $data['receiver_group'][] = $receiverGroup->id;
                }
                unset($data['create_new_receiver']);
                unset($data['phone_number']);
            }

            unset($data['_token']);
            unset($data['_method']);
            unset($data['schedule']);

            $validator = Validator::make($data, [
                'id' => 'required|integer',
                'title' => 'required|string',
                'description' => 'required|string',
                'receiver_group' => 'required|array',
                'detail_notification' => 'required|string',
                'remind_type' => 'required|string',
                'remind_interval' => 'nullable|string',
                'custom_interval' => 'nullable|string',
                'remind_day' => 'nullable|string',
                'remind_month' => 'nullable|string',
                'next_remind_time' => 'required|date',
                'final_remind_time' => 'nullable|date',
                'expired_at' => 'nullable|date',
                'status' => 'required|string',
            ]);

            if ($validator->fails()) {
                return back()->withErrors($validator)->withInput();
            }

            $groupNames = $data['receiver_group'];
            $groupIds = [];
            foreach ($groupNames as $groupName) {
                $groups = DB::connection($dbConnection)->table('receiver_group')->where('group_name', $groupName)->get();
                foreach ($groups as $group) {
                    $groupIds[] = $group->id;
                }
            }
            $data['receiver_group'] = json_encode($groupIds);
            $data['updated_by'] = $currentUserId;
            $data['updated_at'] = Carbon::now();

            DB::connection($dbConnection)->table('reminders')->where('id', $data['id'])->update($data);

            return back()->with('message', 'Reminder updated successfully');
        }
    }

    public function receiver($connectionType, Request $request)
    {
        session()->flashInput(request()->input());
        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }
        $listReceiver = self::EpNotifyService()->getListReceiver($dbConnection);
        $data = collect([]);

        if ($request->isMethod("POST")) {

            $receiverId = $request->receiverId;
            $updateGroup = $request->groupPersonal;
            $updateName = $request->receiverName;
            $receiverStatus = $request->receiverStatus;
            $whatappId = $request->whatappId;
            $action = $request->action;
            $whatappId = $request->whatappId;

            if ($action == 'update') {
                if ($updateGroup == 'personal') {
                    $firstNumber = substr($updateName, 0, 1);
                    if ($firstNumber != '6') {
                        $updateName = '6' . $updateName;
                    }
                 }
                self::EpNotifyService()->updateReceiver($dbConnection, $receiverId, $updateGroup, $updateName, $receiverStatus, $whatappId);
            } else {
                if ($updateGroup == 'personal') {
                    $firstNumber = substr($updateName, 0, 1);
                    if ($firstNumber != '6') {
                        $updateName = '6' . $updateName;
                    }
                    $whatappId = $updateName .'@s.whatsapp.net';
                }
                
                self::EpNotifyService()->addReceiver($dbConnection, $updateGroup, $updateName,$whatappId);
            }

            $data->put('status', 'success');
            return $data;
        }

        return view('wsnotify.receiver', [
            'listReceiver' => $listReceiver,
            'connectionType' => $connectionType
        ]);
    }

    public function receiverGroup($connectionType, Request $request)
    {
        session()->flashInput(request()->input());
        $data = collect([]);

        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }

        $listReceiverGroup = self::EpNotifyService()->getListReceiverGroup($dbConnection);
        $listReceiver = self::EpNotifyService()->getListReceiver($dbConnection);

        if ($request->isMethod("POST")) {
            $groupName = $request->groupName;
            $groupDescription = $request->groupDescription;
            $receiverId = $request->receiverId;
            $action = $request->action;
            $receiverGroupId = $request->receiverGroupId;
            $receiverName = $request->receiverName;
            $groupStatus = $request->groupStatus;

            if ($action == 'add') {
                self::EpNotifyService()->addReceiverGroup($dbConnection, $groupName, $receiverId, $groupDescription);
            } else {
                self::EpNotifyService()->updateReceiverGroup($dbConnection, $groupName, $receiverGroupId, $receiverName, $groupStatus, $groupDescription);
            }
            $data->put('status', 'success');
            return $data;
        }
        return view('wsnotify.receiver_group', [
            'listReceiverGroup' => $listReceiverGroup,
            'listReceiver' => $listReceiver,
            'connectionType' => $connectionType
        ]);
    }

    public function totalPendingNotifications($connectionType)
    {
        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }

        $list = self::EpNotifyService()->getPendingNotification($dbConnection);
        $header = [
            "JOB NAME",
            "PERSONAL/GROUP",
            "Receiver",
            "Date Entered"
        ];
        $html = "<table style='width:100%'>";
        $html .= "<tr><th>" . implode("</th><th>", $header) . "</th></tr>";
        foreach ($list as $rowData) {
            $id = $rowData->id;
            $sourceRemark = $rowData->source_remark;
            $row = [
                $rowData->is_group_personal,
                $rowData->receiver_name,
                $rowData->date_notified
            ];
            $html .= "<tr><td style='padding: 0.5rem;'>
            <a href='#modal-list-data' 
            class='modal-list-data-action label label-primary' 
            data-toggle='modal' data-url='/wsnotify/detail-pending-notifications/" . $connectionType . "?receiver-name=" . $rowData->receiver_name . "&date-entered=" . $rowData->date_notified . "'
            data-title='Detail Pending Notifications' >$sourceRemark</a>
            </td>";
            $html .= "<td>" . implode("</td><td>", $row) . "</td></tr>";
        }
        $html .= "</table>";
        return $html;
    }


    public function detailPendingNotifications(Request $request, $connectionType)
    {
        $receiverName = $request->query("receiver_name");
        $dateEntered = $request->query("date-entered");
        if ($connectionType == 'ep') {
            $dbConnection = self::$epNotifyConnection;
        } else if ($connectionType == 'crm') {
            $dbConnection = self::$crmNotifyConnection;
        }

        $list = self::EpNotifyService()->getDetailPendingNotification($dbConnection, $receiverName, $dateEntered);

        $formData = [];
        // Add or remove fields here
        foreach ($list as $row) {
            $formData[] = [
                'id' => $row->id,
                'message' => $row->message,
                'receiver_group' => $row->receiver_group,
                'process' => $row->process,
                "status" => $row->status,
                "status_desc" => $row->status_desc,
                "sent" => $row->sent,
                "retry" => $row->retry,
                "date_entered" => $row->date_entered,
                "date_modified" => $row->date_modified,
                "source_app" => $row->source_app,
                "source_class" => $row->source_class,
                "source_remark" => $row->source_remark,
                "group_name" => $row->group_name,
                "receiver_id" => $row->receiver_id,
                "group_status" => $row->group_status,
                "group_description" => $row->group_description,
                "is_group_personal" => $row->is_group_personal,
                "receiver_name" => $row->receiver_name,
                "date_created" => $row->date_created,
                "date_notified" => $row->date_notified,
            ];
        }
        // BELOW IS THE TEMPLATE. DO NOT MODIFY UNLESS NECESSARY
        $html = "
        <div id='dynamic-form' style='max-width: 100%; margin: 0 auto; padding: 20px; border: 1px solid #ccc; border-radius: 5px;'>
        <form>
          <div style='display: flex; flex-wrap: wrap' id='field-container'>
            <!-- JavaScript will dynamically generate the fields here -->
          </div>
        </form>
      </div>
        ";

        $html .= "
        <script>
        const formContainer = document.getElementById('dynamic-form');
        const fieldContainer = document.getElementById('field-container');
        
        const formData = " . json_encode($formData) . ";
        
        for (const fieldData of formData) {
          const fieldDiv = document.createElement('div');
          fieldDiv.classList.add('field-wrapper');
          fieldDiv.style.cssText = 'margin-bottom: 15px; flex-basis: calc(100% - 10px); box-sizing: border-box;'
        
          for (const [key, value] of Object.entries(fieldData)) {
            const label = document.createElement('label');
            label.classList.add('field-label');
            label.style.cssText = 'display: block; margin-bottom: 5px;font-weight: bold;'
            label.textContent = key + ':';
        
            const input = (value && value.length !== undefined && value.length > 40)
              ? createTextarea(value)
              : createInput(value);
        
            fieldDiv.appendChild(label);
            fieldDiv.appendChild(input);
          }
        
          fieldContainer.appendChild(fieldDiv);
        }
        
        function createInput(value) {
          const input = document.createElement('input');
          input.classList.add('form-field');
          input.style.cssText = 'width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; resize: vertical;';
          input.setAttribute('type', 'text');
          input.setAttribute('value', value);
          input.setAttribute('readonly', 'readonly');
          return input;
        }
        
        function createTextarea(value) {
          const textarea = document.createElement('textarea');
          textarea.classList.add('form-field');
          textarea.style.cssText = 'width: 100%; padding: 8px; border: 1px solid #ccc; border-radius: 4px; box-sizing: border-box; resize: vertical; height: 150px';
          textarea.value = value;
          textarea.readOnly = true;
          return textarea;
        }
        </script>
        ";
        return $html;
    }

    private function addReceiver($dbConn, $receiverName)
    {
        Log::info(self::class . ' > ' . __FUNCTION__);
        $groupPersonal = 'group';
        $firstNumber = substr($receiverName, 0, 1);
        if ($firstNumber == '6') {
            $groupPersonal = 'personal';
            $whatsappId = $receiverName . '@s.whatsapp.net';
        }
        DB::connection($dbConn)
            ->insert('insert into receiver
                    (is_group_personal,receiver_name,status,date_created,date_modified,whatapp_id)
                    values (?, ?, ?, ?, ?, ?)', [
                $groupPersonal,
                $receiverName,
                'Active',
                Carbon::now(),
                Carbon::now(),
                $whatsappId
            ]);
    }

    private function addReceiverGroup($dbConn, $groupName, $receiverId, $remarks)
    {
        Log::info(self::class . ' > ' . __FUNCTION__);
        return DB::connection($dbConn)
            ->insert('insert into receiver_group
                    (group_name,receiver_id,date_entered,group_description)
                    values (?, ?, ?, ?)', [
                $groupName,
                $receiverId,
                Carbon::now(),
                $remarks
            ]);
    }
}
