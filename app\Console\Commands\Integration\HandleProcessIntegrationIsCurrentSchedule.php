<?php

namespace App\Console\Commands\Integration;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use Mail;
use Log;
use Config;
use DB;

class HandleProcessIntegrationIsCurrentSchedule extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleProcessIntegrationIsCurrent';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To check process_id in table di_interface_log to make sure set record is_current = 1 for each process_d must be 1 record only';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__ . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $listData = $this->listStatisticTotalIsCurrentByProcessId();
            foreach ($listData  as $obj) {
                if($obj->total > 1 
                    && strlen($obj->max_data_end_time)> 10
                    && strlen($obj->interface_log_id) > 2 ){
                        MigrateUtils::logDump(__METHOD__ . ' Start reupdate to reset is_current record: '.json_encode($obj));
                    DB::connection('oracle_nextgen_fullgrant')->table('di_interface_log')
                        ->where('process_id',$obj->process_id)
                        ->where('is_current',1)
                        ->update(['is_current' => 0]);
                    DB::connection('oracle_nextgen_fullgrant')->table('di_interface_log')
                        ->where('interface_log_id',$obj->interface_log_id)
                        ->update(['is_current'=>1]);
                }
            }
           

        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());
           // $this->sendErrorEmail(json_encode($exc->getTrace()));

        }
        
    }
    

    protected static function listStatisticTotalIsCurrentByProcessId() {
        return $res = DB::connection('oracle_nextgen_fullgrant')->select("SELECT tmp.*,   
                (SELECT interface_log_id  FROM di_interface_log x 
                    WHERE x.process_id = tmp.process_id  
                        AND x.is_current = tmp.is_current 
                        AND x.DATA_END_TIME = tmp.max_data_end_time 
                        AND rownum < 2) AS interface_log_id
                FROM (
                    SELECT PROCESS_ID, i.is_current ,max(data_end_time) AS max_data_end_time, count(*) AS total 
                    FROM di_interface_log i
                    WHERE i.is_current = 1  
                    GROUP BY i.PROCESS_ID,i.is_current 
                ) tmp 
                ORDER BY tmp.total desc");
    }


    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected static function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' >> '. $e->getMessage());
            return $e;
        }
    }
    
}
