<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use App\Model\Notify\NotifyModel;
use Illuminate\Support\Facades\DB;

class HandleMonitorQTSpecCount extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-qt-spec-count {--test : Run in test mode and send notifications to TEST_PERSONAL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor quotations with high specification item count (more than 1000) and send notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $isTest = $this->option('test');
        $mode = $isTest ? 'TEST' : 'PRODUCTION';

        Log::info(self::class . ' starting in ' . $mode . ' mode..', [
            'Date' => Carbon::now()
        ]);

        try {
            $results = $this->getQuotationsWithHighSpecCount();
            $totalCount = count($results);

            Log::info(self::class . ' Total count of quotations with high spec count: ' . $totalCount);

            if ($totalCount > 0) {
                // Split results into chunks of 5 records
                $chunks = array_chunk($results, 5);
                $totalChunks = count($chunks);

                Log::info(self::class . ' Will send ' . $totalChunks . ' notifications');

                // Use TEST_PERSONAL as receiver if in test mode
                $receiver = $isTest ? 'TEST_PERSONAL' : 'QT_EXCEEDED_ITEM';

                foreach ($chunks as $chunkIndex => $chunk) {
                    // Add dynamic timestamp at the top of the notification
                    $currentTime = Carbon::now()->format('Y-m-d g:i A');
                    $msg = "*Notification at {$currentTime}* (Part " . ($chunkIndex + 1) . " of {$totalChunks})\n\n";

                    foreach ($chunk as $index => $qt) {
                        $msg .= $this->formatQuotationMessage($qt);

                        // Add newlines only if it's not the last record in this chunk
                        if ($index < count($chunk) - 1) {
                            $msg .= "\n\n";
                        }
                    }

                    $collect = collect(['msg' => $msg]);
                    Log::info(self::class . ' >> Notification ' . ($chunkIndex + 1) . '/' . $totalChunks . ' created');

                    $this->saveNotify($receiver, $collect);
                }

                if ($isTest) {
                    Log::info(self::class . ' ' . $totalChunks . ' notifications sent to TEST_PERSONAL');
                }
            }
        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
    }

    /**
     * Get quotations with specification item count greater than 1000
     * 
     * @return array
     */
    private function getQuotationsWithHighSpecCount()
    {
        return DB::connection('oracle_nextgen_rpt')
            ->select("
                SELECT qt.qt_no, qt.publish_date, qt.closing_date, s.status_id, sd.status_name,
                DECODE(tsm.ITEM_TYPE_ID, 199, 'Perkhidmatan', 198, 'Produk') AS item_type,
                DECODE(tsm.template_status, 'D', 'Draft', 'F', 'Final') status,
                count(*) total_spec_item,
                NVL((
                    SELECT count(*) 
                    FROM SC_QT_SUPPLIER B 
                    JOIN SC_QT_PROPOSAL C ON B.QT_SUPPLIER_ID = C.QT_SUPPLIER_ID
                    WHERE B.QT_ID = qt.qt_id
                    AND C.IS_SUBMITTED = 1
                    GROUP BY C.IS_SUBMITTED
                ), 0) as total_submit_proposal
                FROM sc_qt qt,
                sc_qt_checklist qc,
                sc_qt_doc doc,
                tpl_spec_master tsm,
                tpl_spec_question tsq,
                tpl_spec_estimation tse,
                sc_workflow_status s,
                pm_status_desc sd
                WHERE qt.qt_id = doc.qt_id
                AND qt.qt_id = qc.qt_id
                AND qc.qt_doc_id = doc.qt_doc_id
                AND qt.qt_id = s.doc_id
                AND doc.spec_master_id = tsm.spec_master_id
                AND tsm.spec_master_id = tsq.spec_master_id
                AND tsq.spec_question_id = tse.spec_question_id(+)
                AND tsm.SPEC_TYPE_ID = 159
                AND s.doc_type = 'QT'
                AND s.is_current = 1
                AND s.STATUS_ID = 60009
                AND s.STATUS_ID = sd.STATUS_ID
                AND sd.LANGUAGE_CODE='en'
                GROUP BY qt.qt_id, qt.qt_no, tsm.item_type_id,
                tsm.template_status,
                s.status_id, sd.status_name, qt.publish_date, qt.closing_date
                HAVING count(*) > 1000
                ORDER BY tsm.template_status
            ");
    }

    /**
     * Format the message for a single quotation with WhatsApp formatting
     * 
     * @param object $qt
     * @return string
     */
    private function formatQuotationMessage($qt)
    {
        $publishDate = $qt->publish_date ? Carbon::parse($qt->publish_date)->format('Y-m-d H:i:s') : 'N/A';
        $closingDate = $qt->closing_date ? Carbon::parse($qt->closing_date)->format('Y-m-d H:i:s') : 'N/A';

        return "*[ALERT] Quotation Tender Exceed Item.*\n" .
            "*QT No :* " . $qt->qt_no . "\n" .
            "*Publish Date :* " . $publishDate . "\n" .
            "*Closing Date :* " . $closingDate . "\n" .
            "*Status ID :* " . $qt->status_id . "\n" .
            "*Status Name :* " . $qt->status_name . "\n" .
            "*Item Type :* " . $qt->item_type . "\n" .
            "*Status :* " . $qt->status . "\n" .
            "*Total Spec Item :* " . $qt->total_spec_item . "\n" .
            "*Total Submitted Proposals :* " . $qt->total_submit_proposal;
    }

    /**
     * Save notification to the database
     * 
     * @param string $receiver
     * @param \Illuminate\Support\Collection $collect
     * @return void
     */
    public function saveNotify($receiver, $collect)
    {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group';
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring quotations with high specification count';
        $nty->save();
    }
}