@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/find/masterdata/interfacelog') }}"> INTERFACE LOG </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/ptj') }}"> PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/kumpptj') }}"> KUMP. PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/pegpengawal') }}"> PEG. PENGAWAL </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/vot') }}"> VOT </a>
            </li>
        </ul>
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/glaccount') }}"> GL ACCOUNT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/programactivity') }}"> PROGRAM ACTIVITY </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/project') }}"> PROJECT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/agoffice') }}"> AG OFFICE </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/dana') }}"> DANA </a>
            </li>
        </ul>
    </div>
</div>
<div class="widget">
    <div class="block-title widget-extra themed-background-dark">
        <div class="widget-extra themed-background-dark">
            <h5 class='widget-content-light'>
                INTERFACE LOG - <strong>Master Data</strong>
            </h5>
        </div>
        <div class="block">
            <form id="form-project" action="{{url("/find/masterdata/interfacelog")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST"/>
                <input name="master_data_type" id="master_data_type" type="hidden" value="INTERFACE_LOG"/>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="service_code">Service Code </label>
                    <div class="col-md-5">
                        <select id="service_code" name="service_code" class="form-control select select2">
                            <option value=""></option>
                            @foreach($listServiceCode as $val)
                            <option value="{{$val->service_code}}"  data-val ="{{old('service_code')}}"
                                    @if(old('service_code') == $val->service_code) selected @endif >{{$val->service_code}}</option>
                            @endforeach 
                        </select>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="process_id">Process ID </label>
                    <div class="col-md-5">
                        <select id="process_id" name="process_id" class="form-control select select2">
                            <option value=""></option>
                            @foreach($listProcessId as $val)
                            <option value="{{$val->process_id}}"  data-val ="{{old('process_id')}}"
                                    @if(old('process_id') == $val->process_id) selected @endif >{{$val->process_id}}</option>
                            @endforeach 
                        </select>
                    </div>
                </div>
                
                <div class="form-group" id="div_date">
                    <label class="col-md-3 control-label" for="created_date">Created Date <span class="text-danger">*</span> </label>
                    <div class="col-md-5">
                        <input id="created_date" name="created_date" class="form-control" placeholder="Created Date" type="date"  
                               value="{{ old('created_date') }}" required="false"/>
                    </div>
                </div>
                
                <div class="form-group">
                    <label class="col-md-3 control-label" for="filename">File Name </label>
                    <div class="col-md-5">
                        <input id="filename" name="filename" class="form-control" placeholder="File Name" type="text"  value="{{ old('filename') }}"/>
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>
        </div>
    </div> 
    @if(isset($result))
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="mastertable-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Service Code</th>
                    <th class="text-center">Process ID</th>
                    <th class="text-center">File Name</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Process Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($result as $data)
                <tr>
                    <td class="text-center">{{ $data->service_code }}</td>
                    <td class="text-center">{{ $data->process_id }}</td>
                    <td class="text-center"><a href="{{url("/find/osb/batch/file")}}/{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center">{{ $data->changed_date }}</td>
                    <td class="text-center">{{ $data->process_status }}</td>

                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</div>

@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    $(function () {
        TablesDatatables.init();
    });
    App.datatables();
    $('#mastertable-datatable').dataTable({
        order: [0, "desc"],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
    $("#service_code").bind("change", function () {
        var serviceCode = $(this).find(":selected").val();
        var processId = $("#process_id option:selected").attr("value");

        if (serviceCode !== '' && processId !== '') {
            $("[name='created_date']").prop("required", true);
            //$("#created_date").val('');
        } 
    });
    $("#process_id").bind("change", function () {
        var processId = $(this).find(":selected").val();
        var serviceCode = $("#service_code option:selected").attr("value");

        if (processId !== '' && serviceCode !== '') {
            $("[name='created_date']").prop("required", true);
            //$("#created_date").val('');
        } 
    });
    $(document).on('change', '#filename', function() {
        $("[name='created_date']").prop("required", false);
    });
</script>  


@endsection