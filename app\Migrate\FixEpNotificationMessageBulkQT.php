<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use App\Migrate\MigrateUtils;
use App\Services\Traits\NotificationService;
use App\Services\Traits\SupplierFullGrantService;
use App\EpSupportActionLog;

class FixEpNotificationMessageBulkQT {
    use NotificationService;
    use SupplierFullGrantService;
    
    static $RECORDS_NO = 30000;  //30000
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::startSchedulerNotifyBulkMessageBatchNoQT();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    /**
     * To handle Notification QT  more than 30k users to populate in notify_dashboard
     */
    protected static function startSchedulerNotifyBulkMessageBatchNoQT() {
        $th = new FixEpNotificationMessageBulkQT;
        $listNotifyBulkBigRecords = $th->getQueryListNotificationBulkMessageHugeRecords(self::$RECORDS_NO);
        dump("Total listNotifyBulk : ".count($listNotifyBulkBigRecords));
        Log::info(self::class . ' > ' . __FUNCTION__ . " Total listNotifyBulk : ".count($listNotifyBulkBigRecords));
        
            
        /** Start to set all list as IS_SENT = 1 **/
        foreach ($listNotifyBulkBigRecords as $objData) {
            $notifyMessageId = $objData->notify_message_id;
            $th->updateSkipNotifyMessage($notifyMessageId);
        }
        
        $listBackLogSkipNotifyBulkBigRecords = $th->getQueryBackLogSkipListNotificationBulkMessageHugeRecords(self::$RECORDS_NO);
        dump("Total listBackLogSkipNotifyBulkBigRecords : ".count($listBackLogSkipNotifyBulkBigRecords));
        Log::info(self::class . ' > ' . __FUNCTION__ . " Total listBackLogSkipNotifyBulkBigRecords : ".count($listBackLogSkipNotifyBulkBigRecords));
        
        $isSelectedOne = true;
        if($isSelectedOne == true){
            if(count($listBackLogSkipNotifyBulkBigRecords) > 0 
                // && $th->getCountBulkMessageNotQT() == 0 
                ){
                $collectQt = collect($listBackLogSkipNotifyBulkBigRecords);
                foreach($collectQt->take(1) as $row ){
                    //$firstRecord = $listBackLogSkipNotifyBulkBigRecords[0];
                    $skipNotifyMessageId = $row->notify_message_id;
                    dump('ID '.$skipNotifyMessageId);
                    $th->updateNotifyMessagePutInScheduler($skipNotifyMessageId);
                    dump("Done updated as IS_SENT = 0 for notify_message_id: ".$row->notify_message_id.' ,  batch_no: '.$row->batch_no.' , batch_record_no:  '.$row->batch_record_no.' , DocNo: '.$row->subject_param);
                    Log::info(self::class . ' >  ' . __FUNCTION__ . " Done updated as IS_SENT = 0 for notify_message_id: ".$row->notify_message_id.' ,  batch_no: '.$row->batch_no.' , batch_record_no:  '.$row->batch_record_no.' , DocNo: '.$row->subject_param);
                }
                
            }
        }
        
    }
    
    
    public function updateSkipNotifyMessage($notifyMessageId){
        $databaseConnection = 'oracle_nextgen_fullgrant';
        $tableName = 'pm_notify_message';
        $actionTypeLog = 'Script';
        $actionName = 'PacthDataSchedulerNotificationMessageBulkSkip';
        $logs = collect([]);

        $primaryField = 'notify_message_id';
        $primaryValue = $notifyMessageId;
        $updateScriptFields = ['is_sent' => 1];

        $th = new FixEpNotificationMessageBulkQT;
        $th->saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields, $logs);

        $parameters =  collect([]);
        $parameters->put("remarks", request()->remarks);
        $parameters->put("table", $tableName);
        $parameters->put("reference_id", array($primaryField => $primaryValue));
        $parameters->put("update_data", $updateScriptFields);
        $parameters->put("patching", $tableName);

        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,'Completed','Scheduler');
    }
    
    public function updateNotifyMessagePutInScheduler($notifyMessageId){
        $databaseConnection = 'oracle_nextgen_fullgrant';
        $tableName = 'pm_notify_message';
        $actionTypeLog = 'Script';
        $actionName = 'PacthDataSchedulerNotificationMessageBulkPutIn';
        $logs = collect([]);

        $primaryField = 'notify_message_id';
        $primaryValue = $notifyMessageId;
        $updateScriptFields = ['is_sent' => 0];

        $th = new FixEpNotificationMessageBulkQT;
        $th->saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields, $logs);

        $parameters =  collect([]);
        $parameters->put("remarks", request()->remarks);
        $parameters->put("table", $tableName);
        $parameters->put("reference_id", array($primaryField => $primaryValue));
        $parameters->put("update_data", $updateScriptFields);
        $parameters->put("patching", $tableName);

        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,'Completed','Scheduler');
    }
    
    public function saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,&$logs){
        $th = new FixEpNotificationMessageBulkQT;
        $logQuery = $th->updateRecordByTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields);
        $logs->put('action_patch_update_table',$logQuery);
    }
    
}
