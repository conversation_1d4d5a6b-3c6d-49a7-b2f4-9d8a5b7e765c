<?php

namespace App\Http\Controllers\AppSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\ContractService;


class CtCancelAgreementController extends Controller
{
    use ContractService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function listCtCancelAgreementStatBatchNo()
    {
        $carian = request()->cari;
        $result = $this->listCtCancelAgreementStatBatchNoQuery($carian);
        return view('ct_cancel_agreement.stat_ct_batch_no', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }

    public function listCtCancelAgreement()
    {
        $carian = request()->cari;
        $result = $this->listCtCancelAgreementQuery($carian);
        return view('ct_cancel_agreement.list_ct_cancel_agreement', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }

    public function listCtAgreementNull()
    {
        $carian = request()->cari;
        $result = $this->listCtAgreementNullQuery($carian);
        return view('ct_cancel_agreement.list_ct_null_agreement', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }

    public function listCtPendingAmendment()
    {
        $carian = request()->cari;
        $result = $this->listCtPendingAmendmentQuery($carian);
        return view('ct_cancel_agreement.list_ct_pending_amendment', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }

    public function listCtAgreementNotNull()
    {
        $carian = request()->cari;
        $result = $this->listCtAgreementNotNullQuery($carian);
        return view('ct_cancel_agreement.list_ct_not_null_agreement', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }

    public function listCtAgreementMissing() {
        $carian = request()->cari;
        $result = $this->listCtAgreementMissingQuery($carian);
        return view('ct_cancel_agreement.list_ct_agreement_missing', [
            'listdata' => $result,
            'carian' =>$carian 
        ]);
    }
   
}