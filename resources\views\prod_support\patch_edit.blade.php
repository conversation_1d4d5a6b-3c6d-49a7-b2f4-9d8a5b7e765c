@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
                </li>
                <li @if (!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                    <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent
                        Patching</a>
                </li>
                <li>
                    <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
                </li>
                <li class="">
                    <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
                </li>
                <li @if (!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                    <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
                <li @if (!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                    <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block">
        <!-- Basic Wizard Block -->
        <div class="block">

            <!-- Basic Wizard Content -->
            <form id="editpatch" action="/prod-support/edit-patch/update" method="post"
                class="form-horizontal form-bordered">
                {{ csrf_field() }}
                <!-- First Step -->
                <div id="first" class="step">
                    <!-- Step Info -->
                    <div class="wizard-steps">
                        <div class="row">
                            <div class="col-xs-4 active">
                                <span>1. PATCHING DETAILS</span>
                            </div>
                            <div class="col-xs-4">
                                <span>2. CHANGE REQUEST</span>
                            </div>
                            <div class="col-xs-4">
                                <span>3. SCRIPT</span>
                            </div>
                        </div>
                    </div>

                    <!-- END Step Info -->
                    <div class="col-md-3">
                        <input id="changerequestid" name="changerequestid" type="hidden"
                            value="{{ $changeRequestData ? $changeRequestData->changed_request_id : '' }}"
                            class="form-control" style="width: 700px;">
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="crmno1">CRM Number<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3 carianform">
                            <input type="number" id="crmno1" name="crmno1"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                value="{{ $dataFixDetail ? $dataFixDetail->crm_no : '' }}" required class="form-control"
                                style="width: 700px;">
                        </div>
                        <label class="col-md-1 text-left" for="dateport1">Date Porting</label>
                        <div class="col-md-3">
                            <input readonly="" id="dateport1" name="dateport1" type="text" required
                                class="form-control" style="width: 700px;"
                                value="{{ $dataFixPorting->datetime_porting }}" />
                        </div>
                        <label class="col-md-1 text-left" for="seq">Porting Sequence</label>
                        <div class="col-md-3">
                            <input readonly="" id="seq" name="seq" type="text"
                                value="{{ $dataFixPorting->porting_seq }}" class="form-control" style="width: 700px;">
                        </div>
                        <div class="col-md-3">
                            <input id="datafixid" name="datafixid" type="hidden"
                                value="{{ $dataFixDetail ? $dataFixDetail->data_fix_dtl_id : '' }}" class="form-control"
                                style="width: 700px;">
                        </div>
                    </div>

                    <div class="form-group panel-heading add-new-port">
                        <label class="col-md-1 text-left" for="portmodule1">Module<span class="text-danger">*</span></label>
                        <div class="col-md-3 type">
                            <select id="portmodule1" name = "portmodule1"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif class="form-control"
                                style="width: 700px;">
                                @foreach ($getModule as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->id }}" @if ($dataFixDetail->module == $key->id) selected @endif>
                                        {{ $key->name }}</option>
                                @endforeach
                            </select>
                        </div>
                        <label class="col-md-1 text-left" for="prodesc1">Problem Description<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3 probdesc">
                            <select id="prodesc1" name="prodesc1" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                class="form-control" style="width: 700px;">
                                @foreach ($getProbDescription as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->id }}"@if ($dataFixDetail->problem_description == $key->id) selected @endif>
                                        {{ $key->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <label class="col-md-1 text-left" for="probtype1">Problem Type<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3 prob">
                            <select id="probtype1" name="probtype1" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                class="form-control" style="width: 700px;">
                                @foreach ($getProblemtype as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->id }}"@if ($dataFixDetail->problem_type == $key->id) selected @endif>
                                        {{ $key->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="grouptype1">Group Type<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3">
                            <select id="grouptype1" name ="grouptype1"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif class="form-control" value=""
                                style="width: 700px;">
                                @foreach ($getRequesterType as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->id }}"@if ($dataFixDetail->requester_type == $key->id) selected @endif>
                                        {{ $key->name }}</option>
                                @endforeach
                            </select>
                        </div>

                        <label class="col-md-1 text-left" for="orgname1">Name<span class="text-danger">*</span></label>
                        <div class="col-md-3">
                            <input id="orgname1" name="orgname1" type="text"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                value="{{ $dataFixDetail ? $dataFixDetail->requester_name : '' }}" required
                                class="form-control" style="width: 700px;">
                        </div>

                        <label class="col-md-1 text-left endorse1"
                            @if ($dataFixDetail->endorsement_by == null) style='display: none' @endif for="endorse">Endorsed
                            By<span class="text-danger">*</span></label>
                        <div class="col-md-3">
                            <select id="endorse" name="endorse" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                class="form-control" style="width: 700px;"
                                @if ($dataFixDetail->endorsement_by == null) style='display: none' @endif>
                                @foreach ($usersEndorsed as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->full_name }}"@if ($dataFixDetail->endorsement_by == $key->full_name) selected @endif>
                                        {{ $key->full_name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-1 text-left"></label>
                        <div class="col-md-3"></div>
                        <label class="col-md-1 text-left"></label>
                        <div class="col-md-3"></div>
                        <label class="col-md-1 text-left endorse1" for="endorse">Endorsement Date<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3" id="date_endorse1">
                            <input id = 'date_endorse' name="date_endorse" type="date"
                                @if ($dataFixPorting->status !== 'Open') readonly="" class="form-control"  @else required class="form-control" @endif
                                value="{{ $dataFixDetail ? $dataFixDetail->endorsement_date : '' }}"
                                style="width: 700px;">
                            <strong><label class="text-danger" id="reset_date">Click to RESET</label></strong>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="createdby">Created By</label>
                        <div class="col-md-3">
                            <input readonly="" id="createdby" name="createdby" type="text"
                                value="{{ $dataFixDetail ? $dataFixDetail->created_by : '' }}" class="form-control"
                                style="width: 700px;">
                        </div>

                        <label class="col-md-1 text-left" for="createdate">Created Date<span
                                class="text-danger">*</span></label>
                        <div class="col-md-3">
                            <input readonly="" id="createdate" name="createdate" type="text"
                                value="{{ $dataFixDetail ? $dataFixDetail->created_date : '' }}" class="form-control"
                                style="width: 700px;">
                        </div>
                    </div>

                    @if ($dataFixDetail->changed_by != null)
                        <div class="form-group">
                            <label class="col-md-1 text-left" for="changedby">Changed By</label>
                            <div class="col-md-3">
                                <input readonly="" id="changedby" name="changedby" type="text"
                                    value="{{ $dataFixDetail->changed_by }}" class="form-control" style="width: 700px;">
                            </div>

                            <label class="col-md-1 text-left" for="changeddate">Changed Date<span
                                    class="text-danger">*</span></label>
                            <div class="col-md-3">
                                <input readonly="" id="changeddate" name="changeddate" type="text"
                                    value="{{ $dataFixDetail->changed_date }}" class="form-control"
                                    style="width: 700px;">
                            </div>
                        </div>
                    @endif

                    <div class="form-group">
                        <div class="col-md-4">
                            <label for="type-checkbox1">
                                <input @if ($dataFixPorting->status !== 'Open') style="display:none" @endif type="checkbox"
                                    id="checkbox" name="checkbox" value=""
                                    @if ($dataFixDetail->redmineno != null) checked="checked" @endif> Redmine
                            </label>
                        </div>

                        <div class="col-md-4">
                            <label for="type-checkbox2">
                                <input @if ($dataFixPorting->status !== 'Open') style="display:none" @endif type="checkbox"
                                    id="checkboxcase" name="checkboxcase" value=""
                                    @if ($dataFixDetail->crm_tag_no != null) checked="checked" @endif> If many same case
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-1">
                            <label class="redmine"
                                @if ($dataFixPorting->status !== 'Open') disabled="disabled" @else style="display:none" @endif
                                @if ($dataFixDetail->redmineno == null) style='display: none' @endif for="redmine">Redmine
                                Number<span class="text-danger">*</span></label>
                        </div>
                        <div class="col-md-3">
                            <input @if ($dataFixPorting->status !== 'Open') readonly="" @endif type="number" id="redmineid"
                                name="redmineid" @if ($dataFixDetail->redmineno == null) style='display: none' @endif
                                value="{{ $dataFixDetail ? $dataFixDetail->redmineno : '' }}" required
                                class="form-control" style="width: 700px;">
                        </div>

                        <fieldset @if ($dataFixPorting->status !== 'Open') disabled="disabled" @endif id="exampletags2"
                            @if ($dataFixDetail->crm_tag_no == null) style="display: none" @endif>
                            <label class="col-md-1 text-left exampletags1" for="casetag">Case Number<span
                                    class="text-danger">*</span></label>
                            <div class="col-md-3">
                                <input @if ($dataFixPorting->status !== 'Open') disabled="disabled" class="removed" @endif
                                    type="text" id="exampletags" name="exampletags" class="input-tags"
                                    value="{{ $dataFixDetail ? $dataFixDetail->crm_tag_no : '' }}" required
                                    class="form-control">
                            </div>
                        </fieldset>
                    </div>



                    <div class="form-group">
                        <label class="col-md-1 text-left" for="remarksedit">Remarks</label>
                        <div class="col-md-12">
                            <textarea type="text" id="remarksedit" name="remarksedit" rows="5"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif class="form-control">{{ $dataFixDetail ? $dataFixDetail->remarks : '' }}</textarea>
                        </div>
                    </div>

                </div>
                <!-- END First Step -->

                <!-- Second Step -->
                <div id="second" class="step">
                    <!-- Step Info -->
                    <div class="wizard-steps">
                        <div class="row">
                            <div class="col-xs-4 done">
                                <span><i class="fa fa-check"></i></span>
                            </div>
                            <div class="col-xs-4 active">
                                <span>2. CHANGE REQUEST</span>
                            </div>
                            <div class="col-xs-4">
                                <span>3. SCRIPT</span>
                            </div>
                        </div>
                    </div>
                    <!-- END Step Info -->

                    <div class="form-group">
                        <label class="col-md-2 text-center">Request Type</label>

                        <div class="col-md-2">
                            <div class="checkbox">
                                <label for="type-checkbox1">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 172) checked="checked" @endif
                                        @endforeach @endif> Deployment
                                    System/Server
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox2">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="173"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 173) checked="checked" @endif
                                        @endforeach
                                @else
                                    checked="checked" @endif> Database
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox3">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="174"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 174) checked="checked" @endif
                                        @endforeach @endif> Network
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox4">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="175"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 175) checked="checked" @endif
                                        @endforeach @endif> Application
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox5">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="176"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 176) checked="checked" @endif
                                        @endforeach @endif> Others
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox6">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="177"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 177) checked="checked" @else checked="checked" @endif
                                        @endforeach
                                @else
                                    checked="checked" @endif> Data Fix
                                </label>
                            </div>
                            <div class="checkbox">
                                <label for="type-checkbox7">
                                    <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif
                                        type="checkbox" id="request_type_checkbox" name="request_type_checkbox[]"
                                        value="178"
                                        @if ($getValueRequestType) @foreach ($getValueRequestType as $data) @if ($data == 178) checked="checked" @endif
                                        @endforeach @endif> Document/Content
                                </label>
                            </div>
                        </div>

                        <label class="col-md-2 text-center">Request Category</label>

                        <div class="col-md-2">
                            <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif type="radio"
                                name="radio reqcategory" value="179"
                                @if (
                                    ($changeRequestData && $changeRequestData->request_category == 179) ||
                                        ($dataFixPorting && $dataFixPorting->type_porting === 'S')) checked="checked" @endif>Scheduled Change <br />
                            <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif type="radio"
                                name="radio reqcategory" value="180"
                                @if (
                                    ($changeRequestData && $changeRequestData->request_category == 180) ||
                                        ($dataFixPorting && $dataFixPorting->type_porting === 'U')) checked="checked" @endif>Urgent Change
                        </div>

                        <label class="col-md-2 text-center" for="affectedat">System/Application Affected</label>
                        <div class="col-md-2">
                            <input type="text" @if ($dataFixPorting->status !== 'Open') readonly="" @endif id="affectedat1"
                                name="affectedat1"
                                value="@if (!$changeRequestData) {{ $dataFixDetail ? $dataFixDetail->module_code : '' }} @else {{ $changeRequestData->system_affected }} @endif"
                                class="form-control col-md-2">
                            <input type="text" @if ($dataFixPorting->status !== 'Open') readonly="" @endif id="affectedat2"
                                name="affectedat2" value="" class="form-control col-md-2">
                            <input type="text" @if ($dataFixPorting->status !== 'Open') readonly="" @endif id="affectedat3"
                                name="affectedat3" value="" class="form-control col-md-2">
                        </div>
                        <label class="col-md-2 text-center" for="affectedat">Impact Category</label>

                        <div class="col-md-2">
                            <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif type="radio"
                                name="radio impact" value="181"
                                @if ($changeRequestData && $changeRequestData->impact_category == 181) checked="checked" @endif> Major <br />
                            <input @if ($dataFixPorting->status !== 'Open') onclick="return false;" @endif type="radio"
                                name="radio impact" value="182"
                                @if ($changeRequestData == null or $changeRequestData && $changeRequestData->impact_category == 182) checked="checked" @endif> Minor
                        </div>

                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-md-1 control-label" for="descreq">Description of Request<span
                                    class="text-danger">*</span></label>
                            <div class="col-md-3">
                                <textarea type="text" id="descreq" name="descreq" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                    rows="8" class="form-control" minlength= "10">
@if ($changeRequestData){{ $changeRequestData ? $changeRequestData->description : '' }}
@elseif($case)
{{ $case->description }} 

                                      {{ $case->doc_no }}@endif
</textarea>
                            </div>
                            <label class="col-md-1 control-label" for="reareq">Reason for Request<span
                                    class="text-danger">*</span></label>
                            <div class="col-md-3">
                                <textarea type="text" id="reareq" name="reareq" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                    rows="8" class="form-control" minlength= "8">
@if ($changeRequestData){{ $changeRequestData ? $changeRequestData->reason : '' }}
@else
{{ $dataFixDetail->problem_description_name }} @endif
</textarea>
                            </div>
                            <label class="col-md-1 text-left" for="impactassement">Impact Assessment<span
                                    class="text-danger">*</span></label>
                            <label for="type-checkbox3">
                                <input @if ($dataFixPorting->status !== 'Open') style="display:none" @endif type="checkbox"
                                    id="attach_output" name="attach_output" value=""
                                    @if ($listdata_upload != null && $listdata_upload[0]->impact_attachment_id != null) checked="checked" @endif> If attachment any
                            </label>
                            <div class="col-md-3">
                                @if ($listdata_upload == null)
                                    <textarea type="text" id="impactassement" name="impactassement"
                                        @if ($dataFixPorting->status !== 'Open') readonly="" @endif rows="8" class="form-control" minlength= "8">{{ $changeRequestData ? $changeRequestData->impact_assessment : '' }}</textarea>
                                @else
                                    <textarea type="text" id="impactassement" name="impactassement" style="display:none" readonly=""
                                        @if ($listdata_upload == null) @else readonly="" @endif @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                        rows="8" class="form-control" minlength= "8">{{ $changeRequestData ? $changeRequestData->impact_assessment : '' }}</textarea>
                                @endif
                            </div>
                            <div class="ux-a-file-upload" id ="upload_attach"
                                @if ($listdata_upload != null && $listdata_upload[0]->impact_attachment_id != null) @else style="display:none" @endif>
                                <input id="choosefileUpload" type='file'
                                    onchange='onChooseFileUpload(event, onFileLoad.bind(this, "contents"))' />
                                <p id="contents" style="display:none" spellcheck="false"></p>
                                <div class="col-md-4">
                                    <button type="button" class="btn btn-sm left btn-primary save_attachment"
                                        style="display:none"><i class="fa fa-save"></i> Upload</button>
                                </div>
                            </div>
                            <div class="col-md-4 list_attach"
                                @if ($listdata_upload != null && $listdata_upload[0]->impact_attachment_id != null) @else style="display:none" @endif>

                                <div id="upload_attachment_data" class="panel-group">
                                    @if (isset($listdata_upload) && count($listdata_upload) > 0)
                                        @foreach ($listdata_upload as $key => $patchRow)
                                            <div class='panel panel-default'>
                                                <div class='panel-heading'>
                                                    <h4 class='panel-title'>{{ $key + 1 }} <i
                                                            class='fa fa-angle-right'></i>
                                                        <a class='accordion-toggle' data-toggle='collapse'
                                                            data-parent='#faq1'
                                                            href='#uploadAttached_{{ $patchRow->impact_attachment_id }}'>
                                                            {{ $patchRow->file_name }}</a>

                                                        <div class="pull-right">
                                                            <a href="javascript:void(0)" data-toggle="tooltip"
                                                                onclick="onClickDeleteAttachment(this)"
                                                                data-impact_attachment_id="{{ $patchRow->impact_attachment_id }}"
                                                                data-fix-dtl-id="{{ $patchRow->data_fix_dtl_id }}"
                                                                data-file_name="{{ $patchRow->file_name }}"
                                                                title="" data-original-title=""><i
                                                                    class="fa fa-times"></i></a>
                                                        </div>
                                                    </h4>
                                                </div>
                                            </div>
                                        @endforeach
                                    @endif
                                </div>


                            </div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="requestedby">Request By</label>
                        <div class="col-md-3">
                            <input readonly="" id="requestedby" name="requestedby" type="text"
                                value="{{ $dataFixDetail ? $dataFixDetail->created_by : '' }}" class="form-control"
                                style="width: 700px;">
                            <input readonly="" id="requesteddate" name="requesteddate" type="text"
                                value="{{ Carbon\Carbon::NOW()->format('Y/m/d') }}" class="form-control"
                                style="width: 700px;">
                        </div>

                        <label class="col-md-1 text-left" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                            for="recommendedby">Recommended By</label>
                        <div class="col-md-3">
                            <select id="recommendedby" name="recommendedby"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif class="form-control"
                                style="width: 700px;">
                                @foreach ($usersManager as $key)
                                    <option @if ($dataFixPorting->status !== 'Open') disabled="" @endif
                                        value="{{ $key->full_name }}"@if ($changeRequestData && $changeRequestData->recommender_name == $key->full_name) selected @endif>
                                        {{ $key->full_name }}</option>
                                @endforeach
                            </select>
                            <input readonly="" id="recommendeddate" name="recommendeddate" type="text"
                                value="{{ Carbon\Carbon::NOW()->format('Y/m/d') }}" class="form-control"
                                style="width: 700px;">
                        </div>

                        <label class="col-md-1 text-left" for="approvedby">Approved / Not Approved By</label>

                        <div class="col-md-3">
                            <input id="approvedby" name="approvedby" type="text"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                value="{{ $changeRequestData ? $changeRequestData->approver_name : '' }}"
                                class="form-control" style="width: 700px;">
                            <input id="approveddate" name="approveddate" type="text"
                                @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                value="{{ Carbon\Carbon::NOW()->format('Y/m/d') }}" class="form-control"
                                style="width: 700px;">
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="col-md-16 text-center">
                            Disclaimer: No signature needed for Data Fix and Urgent Data Fix if the approval email has been
                            attached along with the requested activities.
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="remarksCR">Remarks</label>

                        <div class="col-md-3">
                            <textarea type="text" id="remarksCR" name="remarksCR" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                rows="4" class="form-control">{{ $changeRequestData ? $changeRequestData->remarks : '' }}</textarea>
                        </div>

                        <label class="col-md-1 text-left" for="activityplan">Activity Plan</label>

                        <div class="col-md-3">
                            <textarea type="text" id="activityplan" name="activityplan" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                rows="4" class="form-control">{{ $changeRequestData ? $changeRequestData->activity_plan : '' }}</textarea>
                        </div>

                        <label class="col-md-1 text-left" for="expected">Expected Completion Time/Date</label>

                        <div class="col-md-3">
                            <textarea type="text" id="expected" name="expected" @if ($dataFixPorting->status !== 'Open') readonly="" @endif
                                rows="4" class="form-control">{{ $changeRequestData ? $changeRequestData->expected_complete_date : '' }}</textarea>
                        </div>

                    </div>


                </div>
                <!-- END Second Step -->

                <!-- Third Step -->
                <div id="third" class="step">
                    <div class="wizard-steps">
                        <div class="row">
                            <div class="col-xs-4 done">
                                <span><i class="fa fa-check"></i></span>
                            </div>
                            <div class="col-xs-4 done">
                                <span><i class="fa fa-check"></i></span>
                            </div>
                            <div class="col-xs-4 active">
                                <span>3. SCRIPT</span>
                            </div>
                        </div>
                    </div>
                    <!-- END Step Info -->

                    <div class="block-section">
                        <div class="col-md-3">
                            <input id="datafixid" name="datafixid" type="hidden"
                                value="{{ $dataFixDetail ? $dataFixDetail->data_fix_dtl_id : '' }}" class="form-control"
                                style="width: 700px;">
                        </div>
                        <div class="form-group">
                            <div class="col-md-6">
                                <label class="col-md-2 text-left" for="copy_crm_no">CRM Number :</label>
                                <div class="col-md-2">
                                    <input id="copy_crm_no" name="copy_crm_no" type="text"
                                        value="{{ $dataFixDetail ? $dataFixDetail->crm_no : '' }}" class="form-control"
                                        readonly="">
                                </div>
                                <label class="col-md-2 text-right" for="copy_redmine_no">Redmine No :</label>
                                <div class="col-md-2">
                                    <input type="text" id="copy_redmine_no" name="copy_redmine_no"
                                        value="{{ $dataFixDetail ? $dataFixDetail->redmineno : '' }}"
                                        class="form-control"readonly="">
                                </div>
                            </div>
                        </div>

                        <div class="col-md-12">
                            @if ($dataFixPorting->status !== 'Open')
                            @else
                                <input id="choosefile" type='file'
                                    onchange='onChooseFile(event, onFileLoad.bind(this, "contents"))'
                                    accept=".sql" /><br />
                                <p id="contents"></p>
                                <br />
                                <button type="button" class="btn btn-sm right btn-primary savescript"
                                    style="display: none"><i class="fa fa-save"></i> SAVE SCRIPT</button>
                            @endif
                        </div>
                        <div class="block" id="panel_selected_script" style="display:none;">
                            <div class="block-title">
                                <h2><strong>Script</strong></h2>
                                <div class="block-options pull-right action-today">
                                    <a href="#pre_json_desc" data-toggle="collapse" class="btn btn-alt btn-default"><i
                                            class="fa fa-angle-down"></i></a>
                                </div>
                            </div>
                            <pre id="pre_json_desc" class="" class="collapse show">
                         <code style="float: left; color:#fff;" id="selected_script_table_json_desc"></code>
                        </pre>
                        </div>

                        <div class="col-md-12">
                            <h3 class="sub-header"><strong>List of script</strong></h3>
                            <div id="patch-script-data" class="panel-group">
                                @if ($listScriptPatchData && count($listScriptPatchData) > 0)
                                    @foreach ($listScriptPatchData as $key => $patchRow)
                                        <div class='panel panel-default'>
                                            <div class='panel-heading'>

                                                <h4 class='panel-title'>{{ $key + 1 }} <i
                                                        class='fa fa-angle-right'></i>
                                                    <a class='accordion-toggle' data-toggle='collapse'
                                                        data-parent='#faq1' href='#script_{{ $patchRow->id }}'>
                                                        {{ $patchRow->name }}</a>
                                                    @if ($dataFixPorting->status !== 'Open')
                                                    @else
                                                        <div class="pull-right">
                                                            <a href="javascript:void(0)" data-toggle="tooltip"
                                                                onclick="onClickDeleteScript(this)"
                                                                data-script-id="{{ $patchRow->id }}"
                                                                data-fix-dtl-id="{{ $patchRow->data_fix_dtl_id }}"
                                                                title="" data-original-title=""><i
                                                                    class="fa fa-times"></i></a>
                                                        </div>
                                                    @endif
                                                </h4>

                                            </div>
                                            <div id='script_{{ $patchRow->id }}' class='panel-collapse collapse'>
                                                <div class='panel-body'>
                                                    <pre>
                                            <code style='float: left; color:#fff;' id='selected_script_table_json_desc'>{{ $patchRow->sql }}</code>
                                        </pre>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Third Step -->

                <!-- Form Buttons -->
                <div class="form-group form-actions">
                    <div class="col-md-12 col-md-offset-5">
                        <input type="reset" class="btn btn-sm btn-warning" id="back" value="Back">
                        @if ($dataFixPorting->status !== 'Open')
                            <input type="submit" name="Closed" value="Closed" class="btn btn-sm btn-primary "
                                id="close_view" value="Next">
                        @else
                            <input type="submit" name="Submit" value="Submit"
                                class="btn btn-sm btn-primary save-script" id="save-script" value="Next">
                        @endif

                    </div>
                </div>
                <!-- END Form Buttons -->
            </form>
            <!-- END Basic Wizard Content -->
        </div>
        <!-- END Basic Wizard Block -->
    </div>

    <!-- END Form Buttons -->
@endsection

@section('jsprivate')
    <script src="/js/pages/psPatchDetailWizardForm.js"></script>
    <script>
        document.getElementById('page-container').classList = [];

        document.addEventListener('DOMContentLoaded', function() {
            FormsWizard.init();
        });

        document.getElementById('probtype1').addEventListener('change', function() {
            var probtype1Value = document.getElementById('probtype1').value;
            if (probtype1Value === "167") {
                document.getElementById('endorse').style.display = 'block';
                document.querySelectorAll('.endorse1').forEach(function(element) {
                    element.style.display = 'block';
                });
                document.getElementById('date_endorse1').style.display = 'block';
                // document.getElementById('endorse').setAttribute('required', '');
                document.getElementById('endorse').setAttribute('data-error', 'This field is required.');
            } else {
                document.getElementById('endorse').style.display = 'none';
                document.querySelectorAll('.endorse1').forEach(function(element) {
                    element.style.display = 'none';
                });
                document.getElementById('date_endorse1').style.display = 'none';
                document.getElementById('endorse').removeAttribute('required');
                document.getElementById('endorse').removeAttribute('data-error');
            }
        });

        document.getElementById('endorse').addEventListener('click', function() {
            editButtonChanged(this);
        });

        document.getElementById('prodesc1').addEventListener('change', function() {
            editButtonChanged(this);
        });

        function editButtonChanged(a) {
            $idModule = $('#prodesc1').val()
            $idEndorse = $('#endorse').val()
            if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.3 (1)") {
                $('#date_endorse').val("2023-11-03");
            } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.4 (64)") {
                $('#date_endorse').val("2025-04-07");
            } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S).600-22/10/9 JLD 21 (21)") {
                $('#date_endorse').val("2024-07-03");
            } else if ($idEndorse == "Tuan Haji Khairuddin Bin Bakar - TSK (S)" && $idModule == 213) {
                $('#date_endorse').val("2022-12-02");
            } else if ($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 213) {
                $('#date_endorse').val("2022-05-11");
            } else if ($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 206) {
                $('#date_endorse').val("2022-03-22");
            } else if ($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 207) {
                $('#date_endorse').val("2022-03-22");
            } else if ($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 226) {
                $('#date_endorse').val("2023-03-02");
            } else if ($idEndorse == "Kelulusan Pukal MOF.BPK.600-22/10/17 JLD 5 (42)") {
                $('#date_endorse').val("2024-11-29");
            } else if ($idEndorse == "Kelulusan Pukal MOF.BPK(S)600-22/10/13 JLD.4 (65)") {
                $('#date_endorse').val("2025-05-30");
            } else {
                $('#date_endorse').val("");
            }
        }

        document.getElementById('reset_date').addEventListener('click', function() {
            $('#date_endorse').val("");
        });

        var probtype1 = document.getElementById('probtype1');
        var changeEvent = new Event('change');

        probtype1.dispatchEvent(changeEvent);

        document.addEventListener('DOMContentLoaded', function() {
            var carianFormElement = document.querySelector('.carianform');
            console.log(carianFormElement);
            if (carianFormElement) {
                carianFormElement.addEventListener('focusout', function() {
                    console.log('jap');
                    fetch(APP_URL + '/prod-support/data-patching/crmnumber/' + document.getElementById(
                            'crmno1').value)
                        .then(response => response.json())
                        .then(data => {
                            console.log('success');
                            var $data = data;
                            console.log($data);

                            document.getElementById('portmodule1').value = $data[0][0].id;
                            var changeEvent = new Event('change');
                            document.getElementById('portmodule1').dispatchEvent(changeEvent);

                            document.getElementById('orgname1').value = $data[1].name;
                            document.getElementById('grouptype1').value = $data[1].orgname;
                        })
                        .catch(error => console.error('Error:', error));
                });
            }
        });
        var APP_URL = {!! json_encode(url('/')) !!}

        document.querySelector('.type').addEventListener('change', function(event) {
            if (event.target.id === 'portmodule1') {
                var portmodule1Value = document.getElementById('portmodule1').value;
                console.log(portmodule1Value);
                document.getElementById('date_endorse').value = "";
                var module = portmodule1Value;
                var prodesc1 = document.getElementById('prodesc1');
                while (prodesc1.firstChild) {
                    prodesc1.removeChild(prodesc1.firstChild);
                }
                fetch(APP_URL + '/prod-support/data-patching/module/' + module)
                    .then(response => response.json())
                    .then(data => {
                        console.log(data[1].code);
                        data.forEach(function(item) {
                            var option = document.createElement('option');
                            option.value = item.id;
                            option.text = item.name;
                            prodesc1.appendChild(option);
                        });
                        document.getElementById('affectedat1').value = data[1].code;
                    })
                    .catch(error => console.error('Error:', error));
            }
        });
        document.getElementById('checkbox').addEventListener('click', function() {
            var checkbox = document.getElementById('checkbox');
            var redmineId = document.getElementById('redmineid');

            if (checkbox.checked) {
                redmineId.style.display = 'block';
                document.querySelector('.redmine').style.display = 'block';
                redmineId.setAttribute('required', '');
                redmineId.setAttribute('data-error', 'This field is required.');
            } else {
                redmineId.style.display = 'none';
                document.querySelector('.redmine').style.display = 'none';
                redmineId.removeAttribute('required');
            }
        });

        document.getElementById('checkboxcase').addEventListener('click', function() {
            var checkboxcase = document.getElementById('checkboxcase');
            var exampletags = document.getElementById('exampletags');
            var exampletags2 = document.getElementById('exampletags2');

            if (checkboxcase.checked) {
                exampletags2.style.display = 'block';
                exampletags.setAttribute('required', '');
                exampletags.setAttribute('data-error', 'This field is required.');
            } else {
                exampletags2.style.display = 'none';
                exampletags.value = "";
                exampletags.removeAttribute('required');
            }
        });

        var crmno1 = document.getElementById('crmno1');
        var copyCrmNo = document.getElementById('copy_crm_no');

        crmno1.addEventListener('keyup', updateCopyCrmNo);
        crmno1.addEventListener('keypress', updateCopyCrmNo);
        crmno1.addEventListener('blur', updateCopyCrmNo);

        function updateCopyCrmNo() {
            copyCrmNo.value = crmno1.value;
        }

        var redmineid = document.getElementById('redmineid');
        var copyRedmineNo = document.getElementById('copy_redmine_no');

        redmineid.addEventListener('keyup', updateCopyRedmineNo);
        redmineid.addEventListener('keypress', updateCopyRedmineNo);
        redmineid.addEventListener('blur', updateCopyRedmineNo);

        function updateCopyRedmineNo() {
            copyRedmineNo.value = redmineid.value;
        }

        document.getElementById('attach_output').addEventListener('click', function() {
            var attachOutput = document.getElementById('attach_output');
            var impactassement = document.getElementById('impactassement');
            var uploadAttach = document.getElementById('upload_attach');
            var listAttach = document.querySelectorAll('.list_attach');

            if (attachOutput.checked) {
                impactassement.style.display = 'none';
                uploadAttach.style.display = 'block';
                listAttach.forEach(function(element) {
                    element.style.display = 'block';
                });
                uploadAttach.setAttribute('required', '');
                uploadAttach.setAttribute('data-error', 'This field is required.');
            } else {
                impactassement.style.display = 'block';
                uploadAttach.style.display = 'none';
                listAttach.forEach(function(element) {
                    element.style.display = 'none';
                });
                uploadAttach.removeAttribute('required');
            }
        });

        function onClickDeleteAttachment(obj) {
            var dataFixDtlId = $(obj).data('fix-dtl-id');
            var attach_id = $(obj).data('impact_attachment_id');
            var fileName = $(obj).data('file_name');

            $.ajax({
                type: "POST",
                url: "/prod-support/upload_attachment/delete_attachment/" + dataFixDtlId,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'dataFixDtlId': dataFixDtlId,
                    'attachId': attach_id,
                    'fileName': fileName
                },
                success: function(data) {
                    $("upload_attachment_data").text($("editpatch").serialize());
                    $("impactassement").text($("editpatch").serialize());
                    if (data.hasOwnProperty('data_upload')) {
                        $("#upload_attachment_data").html("");
                        if (data.hasOwnProperty('data_upload')) {
                            $("#upload_attachment_data").append(data.data_upload);
                            document.getElementById("impactassement").readOnly = false;
                        }
                    } else {
                        if ($('#attach_output').is(":unchecked")) {
                            $("upload_attachment_data").text($("editpatch").serialize());
                            $("impactassement").text($("editpatch").serialize());
                            document.getElementById("impactassement").readOnly = true;
                        }
                    }
                }
            });
        }

        function onChooseFileUpload(event, onLoadFileHandler) {
            if (typeof window.FileReader !== 'function')
                throw ("The file API isn't supported on this browser.");
            let input = event.target;
            if (!input)
                throw ("The browser does not properly implement the event object");
            if (!input.files)
                throw ("This browser does not support the `files` property of the file input.");
            if (!input.files[0])
                return undefined;
            let file = input.files[0];
            let fr = new FileReader();
            fr.onload = onLoadFileHandler;
            fileNameScript = file.name;
            fr.readAsText(file);
            $('.save_attachment').show();
        }

        document.querySelector('.save_attachment').addEventListener('click', function() {
            var documentData = new FormData();
            var crmIdElement = document.getElementById('datafixid');
            var attachIdElement = document.getElementById('impact_attachment_id');

            var crmId = crmIdElement ? crmIdElement.value : null;
            var attachId = attachIdElement ? attachIdElement.value : null;
            var saveAttachmentBtn = document.querySelector('.save_attachment');
            saveAttachmentBtn.style.display = 'none';

            if (document.getElementById('attach_output').checked) {
                documentData.append('upload_attachment', document.querySelector('input#choosefileUpload').files[0]);
                documentData.append('_token', "{{ csrf_token() }}");
                documentData.append('crm_id', crmId);
                documentData.append('attachId', attachId);
            }

            fetch("/prod-support/upload_attachment/" + crmId, {
                    method: "POST",
                    body: documentData,
                    cache: 'no-store',
                })
                .then(response => response.json())
                .then(data => {
                    const serializedData = $('#editpatch').serialize();
                    document.getElementById('upload_attachment_data').innerText = serializedData;
                    document.getElementById('impactassement').innerText = serializedData;
                    // document.getElementById('upload_attachment_data').innerText = document.getElementById('editpatch').serialize();
                    // document.getElementById('impactassement').innerText = document.getElementById('editpatch').serialize();
                    document.getElementById('upload_attachment_data').innerHTML = "";

                    if (data.hasOwnProperty('data_upload')) {
                        document.getElementById('upload_attachment_data').innerHTML = data.data_upload;
                        if (document.getElementById('attach_output').checked) {
                            document.getElementById('impactassement').readOnly = true;
                        }
                    }
                })
                .catch(error => console.error('Error:', error));
        });

        let
            contentScript = '';
        let
            fileNameScript = '';

        function onFileLoad(elementId, event) {
            document.getElementById(elementId).innerText = event.target.result;
            contentScript = event.target.result;
        }

        function onChooseFile(event, onLoadFileHandler) {
            if (typeof window.FileReader !== 'function')
                throw ("The file API isn't supported on this browser.");
            let input = event.target;
            if (!input)
                throw ("The browser does not properly implement the event object");
            if (!input.files)
                throw ("This browser does not support the `files` property of the file input.");
            if (!input.files[0])
                return undefined;
            let file = input.files[0];
            let fr = new FileReader();
            fr.onload = onLoadFileHandler;
            fileNameScript = file.name;
            fr.readAsText(file);
            $('.savescript').show();
        }

        function onClickDeleteScript(obj) {
            var dataFixDtlId = $(obj).data('fix-dtl-id');
            var scriptId = $(obj).data('script-id');

            $.ajax({
                type: "POST",
                url: "/prod-support/edit-patching/delete-script/" + dataFixDtlId,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'dataFixDtlId': dataFixDtlId,
                    'scriptId': scriptId
                },
                success: function(data) {
                    $("#patch-script-data").html("");
                    if (data.hasOwnProperty('dataHtml')) {
                        $("#patch-script-data").append(data.dataHtml);
                    }

                }
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            var savescriptButton = document.querySelector('.savescript');
            var scriptIdElement = document.getElementById('scriptid');

            if (savescriptButton) {
                savescriptButton.addEventListener('click', function() {
                    var dataFixDtlId = document.getElementById('datafixid').value;
                    if (scriptIdElement !== null) {
                        var scriptId = scriptIdElement.value;
                        console.log(scriptId);
                    }


                    var savescriptBtn = document.querySelector('.savescript');
                    savescriptBtn.style.display = 'none';
                    document.getElementById('panel_selected_script').style.display = 'none';

                    var script = contentScript;
                    var encodedString = btoa(script);

                    document.getElementById('contents').innerHTML = "";

                    fetch("/prod-support/edit-patching/createscript/" + dataFixDtlId, {
                            method: "POST",
                            headers: {
                                'Content-Type': 'application/x-www-form-urlencoded',
                            },
                            body: new URLSearchParams({
                                '_token': document.querySelector('input[name=_token]').value,
                                'script': encodedString,
                                'scriptName': fileNameScript,
                                'dataFixDtlId': dataFixDtlId,
                                'scriptId': scriptId
                            }),
                        })
                        .then(response => response.json())
                        .then(data => {
                            document.getElementById('patch-script-data').innerHTML = "";
                            if (data.hasOwnProperty('dataHtml')) {
                                document.getElementById('patch-script-data').innerHTML = data.dataHtml;
                            }
                        })
                        .catch(error => console.error('Error:', error));
                });
            }
        });

        document.querySelector('.probdesc').addEventListener('change', function(event) {
            if (event.target.id === 'prodesc1') {
                console.log(document.getElementById('prodesc1').value);
                var probdescId = document.getElementById('prodesc1').value;

                fetch(APP_URL + '/prod-support/data-lookup/' + probdescId, {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                    })
                    .then(response => response.json())
                    .then(data => {
                        console.log(data);
                        data.forEach(function(item) {
                            console.log(item.name);
                            document.getElementById('reareq').value = item.name;
                        });
                    })
                    .catch(error => console.error('Error:', error));
            }
        });
    </script>
@endsection
