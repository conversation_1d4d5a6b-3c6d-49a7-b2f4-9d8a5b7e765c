@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/icno')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... IC No.">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
@if ($listdata == null)
<div class="content-header">
    <div class="header-section">
        <h1>
            @if($result == 'notfound')
            <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
            <small>Rekod tidak dijumpai!</small>
            @else
            <i class="gi gi-search"></i>Carian IC No.<br>
            <small>Masukkan no. IC pada carian diatas...</small>
            @endif
        </h1>
    </div>
</div>
@endif

@if($listdata != null)
<div class="block block-alt-noborder full">
    @foreach ($listdata as $data)
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : {{ $data->company_name }}</strong></h1>
        </div>
        <div class="row">
            @if ($data->is_activate_key == true)
            <div class="col-sm-12">
                <div class="notify-alert alert alert-success alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-check-circle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4>
                    Sila semak <a href="javascript:void(0)" class="alert-link">e-mel {{$data->p_email}} di INBOX / SPAM</a> untuk aktifkan login ID.
                    <br /> <br />
                    <strong>Link Activation </strong> : <a target="_blank" href="{{  $data->link }}">{{ $data->link }}</a> <br />
                    <strong>Activation Key </strong> : {{ $data->activation_key }}<br />
                    <strong>Status Success Send </strong> : {{ $data->is_sent }} <br />
                    <strong>Last Date Changed </strong> : {{ $data->changed_date }} <br />

                </div>

            </div>
            @endif
            <div class="col-sm-4">
                <div class="block">

                    <div class="block-title">
                        <h2>Maklumat Syarikat </h2>
                    </div>

                    <h6><strong>{{ $data->company_name }}</strong></h6>
                    <address>
                        <strong>Supplier ID </strong> :
                        @if(Auth::user()->isAdvRolesEp())
                        <a target="_blank" href="{{url('/find/supplier/')}}/{{ $data->appl_id }}/{{ $data->supplier_id  }}">{{ $data->supplier_id  }}</a>
                        @else
                        {{ $data->supplier_id }}
                        @endif
                        <br />
                        <strong>Appl ID </strong> : {{ $data->appl_id }}<br />
                        <strong>Business Type </strong> : {{ $data->business_type }}<br />
                        <strong>SSM No </strong> : {{ $data->reg_no }}<br />
                        <strong>eP No </strong> : <a href="{{ url('/find/epno') }}/{{ $data->ep_no }}">{{$data->ep_no}}</a><br />
                        <strong>Establish Date </strong> : {{ $data->establish_date }} <br />
                        <strong>Last Date Changed </strong> : {{ $data->s_changed_date }} <br />
                        <strong>Record Status </strong> : {{ $data->s_record_status }}<br />
                    </address>
                </div>
            </div>
            <div class="col-sm-4">
                <div class="block">

                    <div class="block-title">
                        <h2>Maklumat MOF</h2>
                    </div>

                    <address>
                        <strong>MOF NO </strong> : <a href="{{ url('/find/mofno') }}/{{ $data->mof_no }}">{{$data->mof_no}}</a><br />
                        <strong>Expired Date </strong> : {{ $data->ma_exp_date }}<br />
                        <strong>Record Status </strong> : {{ $data->ma_record_status }}<br />
                    </address>
                </div>
            </div>
            @if($data->is_business_network == true)
            <div class="col-sm-4">
                <div class="block">

                    <div class="block-title">
                        <h2>Business Network</h2>
                    </div>

                    <address>
                        <strong>Federal? </strong> : {{ $data->is_with_federal  }}<br />
                        <strong>State? </strong> : {{ $data->is_with_state  }}<br />
                        <strong>Local Council? </strong> : {{ $data->is_with_statutory  }}<br />
                        <strong>GLC? </strong> : {{ $data->is_with_glc  }}<br />
                        <strong>Others? </strong> : {{ $data->is_with_others  }}<br />
                    </address>
                </div>
            </div>
            @endif




        </div>

        <div class="row">
            <div class="col-sm-12">

                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-striped table-vcenter">
                        <thead>
                            <tr>
                                <th class="text-center">Name</th>
                                <th class="text-center">Identification No.</th>
                                <th class="text-center">Email</th>
                                <th class="text-center">Role</th>
                                <th class="text-center">Softcert Status</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>

                        <tbody>

                            <tr>
                                <td class="text-center">{{ $data->p_name }}</td>
                                <td class="text-center">{{ $data->p_identification_no }}</td>
                                <td class="text-center">{{ $data->p_email }}</td>
                                <td class="text-center">{{ $data->p_ep_role }}</td>
                                <td class="text-center">{{ $data->p_is_softcert }}</td>
                                <td class="text-center">
                                    <button class="btn btn-info btn-xs" data-toggle="collapse" data-target="#row_{{$data->personnel_id}}">
                                        Details</button>
                                </td>
                            </tr>
                            <tr id="row_{{$data->personnel_id}}" @if(strlen($data->p_ep_role) > 0)class="collapsed" @else class="collapse" @endif>
                                <td class="text-center" colspan="7">
                                    <!-- Customer Addresses Block -->
                                    <div class="block" @if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN')style="background-color: inherit;"@endif>
                                        <div class="row">
                                            @if ($data->is_activate_key == true)
                                            <div class="col-sm-12" class="text-left">
                                                <div class="notify-alert alert alert-success alert-dismissable">
                                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                    <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4>
                                                    Sila semak <a href="javascript:void(0)" class="alert-link">e-mel {{$data->p_email}} di INBOX / SPAM</a> untuk aktifkan login ID.
                                                    <br /> <br />
                                                    <strong>Link Activation </strong> : <a target="_blank" href="{{  $data->link }}">{{ $data->link }}</a> <br />
                                                    <strong>Activation Key </strong> : {{ $data->activation_key }}<br />
                                                    <strong>Status Success Send </strong> : {{ $data->is_sent }} <br />
                                                    <strong>Last Date Changed </strong> : {{ $data->activation_changed_date }} <br />

                                                </div>

                                            </div>
                                            @endif

                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat User Login </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                        <strong>Login ID </strong> : {{ $data->login_id  }}<br />
                                                        <strong>Email </strong> : {{ $data->email  }}<br />
                                                        <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                                                        <strong>Record Status </strong> : {{ $data->u_record_status }}<br />
                                                        <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->changed_date }} <br />

                                                        <br />
                                                        <strong>Peranan</strong> : <br />
                                                        @if(!empty($data->roles))
                                                        @foreach ($data->roles as $role)
                                                        {{ $role->role_code }} <br />
                                                        @endforeach
                                                        @else
                                                        Tiada
                                                        @endif
                                                    </address>
                                                </div>
                                            </div>

                                            <div class="col-sm-4">
                                                <div class="block maklumat-diri">
                                                    <div class="block-title">
                                                        <h2>Maklumat Diri Staf Syarikat </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                                                        <strong>Designation </strong> : {{ $data->p_designation  }}<br />
                                                        <strong>Email </strong> : {{ $data->p_email  }}<br />
                                                        <strong>Role </strong> : {{ $data->p_ep_role }}<br />
                                                        <strong>SoftCert Status </strong> : {{ $data->p_is_softcert  }}<br />
                                                        <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                                                        <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                                                        <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                                                        <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Record Status </strong> : {{ $data->p_record_status }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->p_changed_date }} <br />
                                                        <div id="total-address" class="widget-extra-full">
                                                            <strong>Total Address </strong> :
                                                            <a href='#modal-list-data-total-address' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/find/supplier/address/{{$data->personnel_id}}' data-title='List Personnel Address'>
                                                                {{ $data->total_address }}
                                                            </a>
                                                            @if($data->total_address == 0 && strlen($data->p_ep_role) > 0 ) <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="This required if apply softcert"></i>
                                                            <span style="font-weight: bolder; color:red;">Required apply softcert!</span @endif <br />
                                                        </div>
                                                    </address>
                                                </div>
                                            </div>

                                            @if(count($data->listSoftCert ) > 0)
                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>SoftCert Request</h2>
                                                    </div>

                                                    @foreach ($data->listSoftCert as $sCert)
                                                    <address class="text-left">
                                                        <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br />
                                                        @if($sCert->softcert_provider == 'TG')
                                                        <a target='_blank' style="color:darkblue;font-weight: bolder;text-decoration: underline;" href="https://digitalid.msctrustgate.com/v2/ePRA/request/detail_request?requestid={{$sCert->softcert_request_id}}">
                                                            Integrate Trustgate Portal </a><br />
                                                        @endif
                                                        <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                                                        <strong>Softcert Provider </strong> : {{ $sCert->softcert_provider }}<br />
                                                        <strong>Request Mode </strong> : {{ $sCert->request_mode  }}<br />
                                                        <strong>Date Created </strong> : {{ $sCert->created_date }} <br />
                                                        <strong>Last Date Changed </strong> : {{ $sCert->changed_date }} <br />
                                                        <strong>Using Free SoftCert</strong> : {{ $sCert->is_free }} <br />
                                                        {{--
                                                                <strong>Response Status</strong> :  {{  $sCert->response_status }} <br />
                                                        <strong>Request Mode</strong> : {{ $sCert->request_mode }} <br />
                                                        <strong>Remark </strong> : {{ $sCert->remark  }}<br />
                                                        <strong>Reason Code </strong> : {{ $sCert->reason_code  }}<br />
                                                        --}}
                                                        @if(strlen($sCert->cert_serial_no)>0)
                                                        <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                                                        <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') TrustGate @else Digicert @endif<br />
                                                        <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                                                        <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}<br />
                                                        <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                                                        <strong>Cert Last Date Changed </strong> : {{ $sCert->pdc_changed_date }} <br />
                                                        @else
                                                        <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />
                                                        @endif
                                                    </address>
                                                    @endforeach
                                                </div>
                                            </div>
                                            @endif

                                        </div>
                                    </div>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>


                @foreach ($listdata as $data)





                @endforeach
            </div>
        </div>

    </div>
    <!-- END Customer Addresses Block -->

    @endforeach
</div>

<!-- MODAL: LIST Address -->
<div id="modal-list-data-total-address" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-total-address">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-total-address" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
@endif


@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    var APP_URL = {!!json_encode(url('/')) !!}

    /** initialize datatable **/
    App.datatables();

    var tableListData = $('#basic-datatable-total-address').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });


    $(document).ready(function() {

        $('.maklumat-diri').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('#basic-datatable-total-address').html('').fadeIn();

            $('#modal-list-data-header-total-address').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-total-address').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-total-address').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
    })
</script>

@endsection