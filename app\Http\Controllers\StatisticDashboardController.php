<?php

namespace App\Http\Controllers;

use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Auth;
use App\Services\Traits\SSHService;
use SSH;
use Illuminate\Http\Request;
use DateTime;
use Log;
use App\EpCrmMonitoringStatistic;

class StatisticDashboardController extends Controller {

    public static function crmService() {
        return new CRMService;
    }

    use SSHService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function getDashboardStatistic() {
        return $this->getDashboardDetailStatistic();
    }
    
   
    public function getDashboardDetailStatistic() {
        return view('dashboard_statistic', []);
    }
    
    public function dashboardStatisticDailyCasesResolved(Request $request){
 
        $GroupID = $request->groupid;
        $yesterday = Carbon::now()->yesterday()->toDateString();
        $Month = Carbon::now()->format('F');
        $Yearly = Carbon::now()->year;
        $resultList = self::crmService()->getDashboardStatisticCaseAssignedbyModule($GroupID);
        $i = 0;  
        
        $html = "";
        $html .= "
        <div>
            <h5>&nbsp;&nbsp;&nbsp;<strong>Statistic for Case CRM</strong></h5>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>                    
                    <tr border='1'>
                        <th><center>No.</center></th>
                        <th><center>Name</center></th>
                        <th><center> Total for today</center></th> 
                        <th><center>Total on $yesterday</center></th>
                        <th><center>Total in $Month</center></th>
                        <th><center>Total in $Yearly</center></th>
                        <th><center>Total for Last Year</center></th>
                    </tr>
                </thead>
                <tbody>";
        
        foreach ($resultList as $data) {
            $resultSecondList = self::crmService()->getDashboardTotalStatisticCaseAssignedbyModule($data->securityID);
            foreach ($resultSecondList as $resultData) {
                $i++;
                $html .= "
            <tr>
                    <td style='width: 10%;'><center><strong>{$i}</strong></center></td>
                    <td style='width: 30%; text-transform: uppercase;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action' 
                            data-toggle='modal' data-url='/list/individualmodule/resolved/$resultData->userId' data-title='List Details Module Resolved by Individual' >$resultData->fullname</a></strong></td>
                    <td style='width: 10%;'><center><strong>$resultData->totalCaseResolvedDaily</strong></center></td>
                    <td style='width: 10%;'><center><strong>$resultData->yesterday</strong></center></td>
                    <td style='width: 10%;'><center><strong>$resultData->totalCaseResolvedMonthly</strong></center></td>
                    <td style='width: 15%;'><center><strong>$resultData->totalCaseResolvedYearly</strong></center></td>
                    <td style='width: 15%;'><center><strong>$resultData->totalCaseResolvedLastYear</strong></center></td>
            </tr>";
            }
        }

        $html .= "
                </tbody>
            </table>
        </div>";        
        
        return $html;
    }   
    
    public function dashboardStatisticDailyCasesResolvedSTL(Request $request){

        $yesterday = Carbon::now()->yesterday()->toDateString();
        $Month = Carbon::now()->format('F');
        $Yearly = Carbon::now()->year;
        $j = 0;
        $html = "";
        
            $html .= "
        <div>
            <h5>&nbsp;&nbsp;&nbsp;<strong>Statistic (Other Than CRM Cases)</strong></h5>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>                    
                    <tr border='1'>
                        <th><center>No.</center></th>
                        <th><center>Name</center></th>
                        <th><center> Total for today</center></th> 
                        <th><center>Total on $yesterday</center></th>
                        <th><center>Total in $Month</center></th>
                        <th><center>Total in $Yearly</center></th>
                        <th><center>Total for Last Year</center></th>
                        <th><center>Action</center></th>
                    </tr>
                </thead>
                <tbody>";
        
        
        $middlewareUsers = self::crmService()->getMiddlewareUsers();
        foreach ($middlewareUsers as $data) {
            $userStatistic = EpCrmMonitoringStatistic::getUserStatistic($data->userid);
            foreach ($userStatistic as $resultData) {
                $j++;
                $html .= "
            <tr>
                    <td style='width: 10%;'><center><strong>{$j}</strong></center></td>
                    <td style='width: 20%; text-transform: uppercase;'><strong>$data->fullname</strong></td>
                    <td style='width: 15%;'><center><strong><a href='#modal-list-data' 
                    class='modal-list-data-action' 
                    data-toggle='modal' data-url='/list/individualmodule/statistic/$data->userid/today' data-title='List Details Module Resolved by Individual' >$resultData->totalresolvedaily</a></strong></center></td>
                    <td style='width: 15%;'><center><strong><a href='#modal-list-data' 
                    class='modal-list-data-action' 
                    data-toggle='modal' data-url='/list/individualmodule/statistic/$data->userid/yesterday' data-title='List Details Module Resolved by Individual' >$resultData->totalresolveyesterday</a></strong></center></td>
                    <td style='width: 15%;'><center><strong><a href='#modal-list-data' 
                    class='modal-list-data-action' 
                    data-toggle='modal' data-url='/list/individualmodule/statistic/$data->userid/month' data-title='List Details Module Resolved by Individual' >$resultData->totalresolvemonthly</a></strong></center></td>
                    <td style='width: 15%;'><center><strong><a href='#modal-list-data' 
                    class='modal-list-data-action' 
                    data-toggle='modal' data-url='/list/individualmodule/statistic/$data->userid/year' data-title='List Details Module Resolved by Individual' >$resultData->totalresolveyearly</a></strong></center></td>
                    <td style='width: 15%;'><center><strong><a href='#modal-list-data' 
                    class='modal-list-data-action' 
                    data-toggle='modal' data-url='/list/individualmodule/statistic/$data->userid/lastyear' data-title='List Details Module Resolved by Individual' >$resultData->totalresolvelastyear</a></strong></center></td>
                    <td style='width: 20%;'><a href='#modal-list-statistic' 
                            class='modal-list-statistic-action' 
                            data-toggle='modal' data-url='/list/individualmodule/updateStatistic/$data->userid/$data->fullname' data-title='Update Statistic By Individual' style='color:black;'><i class='fa fa-pencil'></i></a></td>
            </tr>";
            }
        }
        
        $html .= "
                </tbody>
            </table>
        </div>";        
        
        return $html;
    }
    
    public function getDetailsStatistic($userId) {
        $yesterday = Carbon::now()->yesterday()->toDateString();
        $Month = Carbon::now()->format('F');
        $Yearly = Carbon::now()->year;

        $list = self::crmService()->getDetailsStatisticModuleResolved($userId);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Name</th>
                            <th class='text-center'>Module</th>
                            <th class='text-center'>Total for today</th>
                            <th class='text-center'>$yesterday</th>
                            <th class='text-center'>$Month</th>
                            <th class='text-center'>$Yearly</th>
                            <th class='text-center'>Total for Last Year</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
           $moduleName = str_replace('/',' ' ,$value->moduleName);

           $htmlToday = "<td class='text-left'><strong>$value->totalCaseResolvedDaily</strong></td>";
           $htmlYesterday = "<td class='text-left'><strong>$value->yesterday</strong></td>";
           
           if($value->totalCaseResolvedDaily > 0){
               $htmlToday = "<td class='text-left'><strong><a href='/list/individualmodule/resolvedbydate/$userId/$moduleName/today' target='_blank'>$value->totalCaseResolvedDaily</a></strong></td>"; 
           }
           if($value->yesterday > 0){
               $htmlYesterday = "<td class='text-left'><strong><a href='/list/individualmodule/resolvedbydate/$userId/$moduleName/yesterday' target='_blank'>$value->yesterday</a></strong></td>"; 
           }
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left' style='text-transform: uppercase;'><strong>$value->fullname</strong></td>
                        <td class='text-left'><strong>$value->moduleName</strong></td>
                        $htmlToday 
                        $htmlYesterday
                        <td class='text-left'><strong>$value->totalCaseResolvedMonthly</strong></td>
                        <td class='text-left'><strong>$value->totalCaseResolvedYearly</strong></td>
                        <td class='text-left'><strong>$value->totalCaseResolvedLastYear</strong></td>
                    </tr>";
                $html = $html . $data;
          
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function getDetailsStatisticByUserAndDate($userId, $module, $date) {

        if ($date === 'today') {
            $sqlDate = " DAY(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = DAY(NOW()) 
                AND (MONTH(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = MONTH(NOW())) 
                AND (YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = YEAR(NOW()))";
        } else if ($date === 'yesterday') {
            $sqlDate = " (DAY(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = DAY(NOW() - INTERVAL 1 DAY)) 
                AND (MONTH(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = MONTH(NOW() - INTERVAL 1 DAY)) 
                AND (YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = YEAR(NOW()))";
        } else {
            $sqlDate = " DAY(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = DAY(NOW()) 
                AND (MONTH(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = MONTH(NOW())) 
                AND (YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) = YEAR(NOW()))";
        }

        $subCategory = $module;
        if($module == 'Quotation Tender'){
            $subCategory = 'Quotation/Tender';
        }
        $list = self::crmService()->getDetailsStatisticModuleResolvedByDate($userId, $subCategory, $sqlDate);

        return view('case_crm_module', ['list' => $list]);
    }

    public function updateDetailsStatistic($userId,$fullName){

        $html = "       <h5 class='alert alert-danger' id='errorMsg' style='display:none;'></h5>
                        <label class='col-md-2 control-label' for='fullname'>Name </label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='fullname' name='fullname' class='form-control' value='$fullName' type='text' readonly='true'>
                            <input id='userid' name='userid' value='$userId' type='hidden'>
                        </div> <br/>
                    <label class='col-md-2 control-label' for='category'>Category <span class='text-danger'>*</span></label>
                        <div class='input-group' style='width: 66%;padding-left: 20px;'>
                            <select id='category' name='category' required class='form-control categoryForm'>
                            <option value=''>Please Select</option>
                            <option value='STL'>STL</option>
                            <option value='ASSIST'>ASSIST</option>
                            <option value='ERROR HANDLER'>ERROR HANDLER</option>
                            </select>
                        </div><br/>
                        <label class='col-md-2 control-label' for='module'>Module <span class='text-danger'>*</span></label>
                        <div class='input-group' style='width: 66%;padding-left: 20px;'>
                            <select id='module' name='module' required class='form-control'>
                            <option value=''>Please Select</option>
                            </select>
                        </div><br/>
                     <div id='div_doc_no' style='display: none'>
                        <label class='col-md-2 control-label' for='doc_no'>Document No. <span class='text-danger'>*</span></label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='doc_no' name='doc_no' class='form-control' type='text' required='true'> <span id='requiredDocNo' style='display:none' class='text-danger'>This field is required</span>
                        </div><br/>
                    </div>
                    <div id='div_case_no' style='display: none'>
                        <label class='col-md-2 control-label' for='case_no'>Case No.</label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='case_no' name='case_no' class='form-control' type='text'>
                        </div><br/>
                    </div>
                    <label class='col-md-2 control-label' for='totaldocno'>Total <span class='text-danger'>*</span></label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='totaldocno' name='totaldocno' class='form-control' type='text' required='true'> <span id='requiredTotal' style='display:none' class='text-danger'>This field is required</span>
                        </div> <br/>
                    <label class='col-md-2 control-label' for='date_resolve'>Date <span class='text-danger'>*</span></label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='date_resolve' name='date_resolve' type='date' data-date-inline-picker='true'> <span id='requiredDate' style='display:none' class='text-danger'>This field is required</span>
                        </div><br/>
                    <label class='col-md-2 control-label' for='remarks'>Remarks </label>
                        <div class='input-group' style='width:66%;padding-left: 20px'>
                            <input id='remarks' name='remarks' class='form-control' type='text'>
                        </div><br/>
                    <center><button type='submit' id='btn_update_stats' name='btn_update_stats' class='btn btn-sm btn-info'> Update </button></center>
                "; 
        echo "<script>
                var data = {'STL':{'Stuck Task SM':'Stuck Task SM',
                                        'Stuck Task QT':'Stuck Task QT',
                                        'Stuck Task DP SQ':'Stuck Task DP SQ',
                                        'Stuck Task DP RN':'Stuck Task DP RN',
                                        'Stuck Task DP Codify':'Stuck Task DP Codify',
                                        'Stuck Task RN Trigger Order':'Stuck Task RN Trigger Order',
                                        'Stuck Task PR CR Initiate':'Stuck Task PR CR Initiate',
                                        'Stuck Task FL Initiate DO':'Stuck Task FL Initiate DO',
                                        'Stuck Task Integration PRCR':'Stuck Task Integration PRCR',
                                        'Stuck Task Query PRCR':'Stuck Task Query PRCR',
                                        'Stuck Task Awaiting IGFMAS PRCR':'Stuck Task Awaiting IGFMAS PRCR',
                                        'Stuck Task Order POCO':'Stuck Task Order POCO',
                                        'Stuck Task FL Debit':'Stuck Task FL Debit',
                                        'Stuck Task Integration FRN':'Stuck Task Integration FRN',
                                        'Stuck Task Integration DAN':'Stuck Task Integration DAN',
                                        'Stuck Task Integration SD':'Stuck Task Integration SD',
                                        'Stuck Task Integration PA':'Stuck Task Integration PA', 
                                        'Stuck Task FL Approver PRCR':'Stuck Task FL Approver PRCR',
                                        'Stuck Task FL Pending Invoice':'Stuck Task FL Pending Invoice',
                                        'Stuck Task FL Pending Payment Match':'Stuck Task FL Pending Payment Match',
                                        'Stuck Task FL FRN Awaiting IGFMAS':'Stuck Task FL FRN Awaiting IGFMAS',
                                        'Stuck Task Pending Revision Approval PRCR':'Stuck Task Pending Revision Approval PRCR',
                                        'Stuck Task Integration EPP-013':'Stuck Task PRCR (EPP-013 = Y)',
                                        'Stuck Task PRCR (MM501-DEL)':'Stuck Task PRCR (MM501-DEL)',
                                        'FL Pending Payment (EPP-017 = Y)':'FL Pending Payment (EPP-017 = Y)',
                                        'PHIS':'PHIS',
                                        'AP511':'AP511',
                                        'Pending PR/CR Review':'Pending PR/CR Review',
                                        'Redundant SAP Order No':'Redundant SAP Order No'},
                             'ASSIST':{'Order':'Order',
                                        'SourcingDP':'SourcingDP',
                                        'Fulfilment':'Fulfilment',
                                        'Codification':'Codification',
                                        'Profile Management':'Profile Management',
                                        'SourcingQT':'SourcingQT',
                                        'Supplier Management':'Supplier Management',
                                        'Contract Management':'Contract Management',
                                        'Procument_Plan':'Procument Plan',
                                        'SC BusinessRule':'SC BusinessRule',
                                        'Integration':'Integration',
                                        'Batch':'Batch'},
                             'ERROR HANDLER':{'DP':'DP',
                                        'RN':'RN',
                                        'CT':'CT',
                                        'SM':'SM',
                                        'FL':'FL',
                                        'QT':'QT'},
                             };
                $('.categoryForm').change(function () {
                    var category = $('#category option:selected').text();
                    
                    if(category === 'ASSIST'){
                        document.getElementById('div_doc_no').style.display = 'block';
                        document.getElementById('div_case_no').style.display = 'block';
                    }else{
                        document.getElementById('div_doc_no').style.display = 'none';
                        document.getElementById('div_case_no').style.display = 'none';
                    }
                    var module = $('#module');
                    $(module).empty();
                    if(typeof(data[category]) != 'undefined'){
                        var options = (typeof(data[category]) != 'undefined') ? data[category] : {};
                        $.each( options , function(value, index) {
                                $('<option value=";
                echo"' + value + '"; 
                echo">' + index + '</option>').appendTo(module);
                        });
                    }
                });
             </script>";
        return $html;
    }
    
    public function updateDetailsStatisticValue(Request $request){
        $userId = $request->user_id;
        $fullName = $request->full_name;
        $total = $request->total;
        $module = $request->module;
        $category = $request->category;
        $caseNo = $request->case_no;
        $docNo = $request-> doc_no;
        $dateResolve = $request->date_resolve;
        $remarks = $request->remarks;
        $loginId = Auth::user()->user_name;
        $status = null;
        
        $parameters = collect([]);
        $parameters->put("user_id", $userId);
        $parameters->put("full_name", $fullName);
        $parameters->put("total", $total);
        $parameters->put("category", $category);
        $parameters->put("module", $module);
        $parameters->put("date_resolve", $dateResolve);
        
        if($userId != null && $total != null && $category != 'Please Select' && $module !='Please Select'){
            $status = 'Success';
            EpCrmMonitoringStatistic::createCrmMonitoring($userId, $fullName, $total, $category, $module, $docNo, $caseNo, $dateResolve, $remarks, $loginId);
        }else{
            $status = 'Please Fill All Required Fields';
        }
        
        return $status;
        
    }
    
    public function getDetailsStatisticSTL($userId,$day) {
        $yesterday = Carbon::now()->yesterday()->toDateString();
        $Month = Carbon::now()->format('F');
        $Yearly = Carbon::now()->year;
        $lastYear = $Yearly - 1;

        $list = EpCrmMonitoringStatistic::getUserStatisticByModule($userId,$day);
        $title = "Total for today";
        if($day == 'yesterday') {
            $title = "Total for yesterday";
        } else if($day == 'month') {
            $title = "Total for $Month";
        } else if($day == 'year') {
            $title = "Total for $Yearly";
        } else if($day == 'lastyear') {
            $title = "Total for $lastYear";
        }

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Name</th>
                            <th class='text-center'>Category</th>
                            <th class='text-center'>Module</th>
                            <th class='text-center'>$title</th> 
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
           
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left' style='text-transform: uppercase;'><strong>$value->full_name</strong></td>
                        <td class='text-left'><strong>$value->category</strong></td>
                        <td class='text-left'><strong>$value->module</strong></td>
                        <td class='text-left'><strong>$value->total</strong></td> 
                    </tr>";
                $html = $html . $data;
          
        }
        $html = $html . "<tbody>";
        return $html;
    }
  
}