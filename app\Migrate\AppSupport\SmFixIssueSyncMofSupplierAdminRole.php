<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmFixIssueSyncMofSupplierAdminRole {

    use SupplierService;

    public static function fixIssueToUpdateMofSupplierAdmin(){
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmFixIssueSyncMofSupplierAdminRole();
        $listObjAppl = $thisCls->getListAdminSupplierRoleNotAsMofSupplierAdmin();
        MigrateUtils::logDump(' total found : '.count($listObjAppl));
        foreach($listObjAppl as $obj){
            dump($obj);
            if($obj->total_basic_supplier_admin > 1 ){
                MigrateUtils::logDump(' checking role basic_supplier_admin more than 2 ..');
                $userRoleId = $obj->user_role_id;
                if($userRoleId){
                    // Checking ada 2 role sama , amik yang current active nih tukarkan ke MOF_SUPPLIER_ADMIN 
                    MigrateUtils::logDump(' this user_role_id : '.$userRoleId. ' to update to rolecode : MOF_SUPPLIER_ADMIN');
                    $userRole =  DB::connection('oracle_nextgen_fullgrant')
                        ->table('PM_USER_ROLE')->where('user_role_id',$userRoleId)
                        ->update(
                            [
                                'role_code' => 'MOF_SUPPLIER_ADMIN'
                            ] 
                        );
                }
                
            }    
            if( $obj->total_mof_supplier_admin > 1){
                MigrateUtils::logDump(' checking role mof_supplier_admin more than 2 ..');
                $userOrgId = $obj->user_org_id;
                $userRoleId = $obj->user_role_id;
                if($userOrgId && $userRoleId){
                    MigrateUtils::logDump(' this user_org_id : '.$userOrgId. ' with record_status = 9 to update to record_status : 1');
                    DB::connection('oracle_nextgen_fullgrant')
                    ->table('PM_USER_ROLE')->where('user_org_id',$userOrgId)
                    ->where('record_status',9)
                    ->update(
                        [
                            'record_status' => 1
                        ] 
                    );
                    MigrateUtils::logDump(' this user_role_id : '.$userRoleId. ' with record_status = 1 to update to record_status : 9');
                    DB::connection('oracle_nextgen_fullgrant')
                    ->table('PM_USER_ROLE')->where('user_role_id',$userRoleId)
                    ->where('record_status',1)
                    ->update(
                        [
                            'record_status' => 9
                        ] 
                    );
                }
            }
            
            self::triggerSyncRole($obj->personnel_id);
            dd('done');
        }
        //$thisCls->fixIssueRejectBumiStatus("KB-05052020-0046");

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    public static function fixIssueToDeleteDuplicateRole($limit=null){
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        /* Rules record_status
                0, 1 = tinggalkan record_status:1
                0, 9 = tinggalkan record_status:0
                0, 1, 9 = tinggalkan record_status:1
                1, 9 = tinggalkan record_status:1
         */
        $rules1 = array(0,1);
        $rules2 = array(0,9);
        $rules3 = array(1,9);
        $rules4 = array(0,1,9);
                    
        $thisCls = new SmFixIssueSyncMofSupplierAdminRole();
        $listDuplicate = $thisCls->getDuplicateRole('null');
        MigrateUtils::logDump(' total found : '.count($listDuplicate));
        MigrateUtils::logDump(' proceed to fix : '.$limit.' users');
        $actionName = 'DeleteDuplicateRoleSm';
        $actionTypeLog = 'Update Table Ep';
        if (count($listDuplicate) > 0) {
            foreach (collect($listDuplicate)->take($limit) as $obj) {  
                $userOrgId = $obj->user_org_id;
                $roleCode = $obj->role_code;
                MigrateUtils::logDump(' checking duplicate role... user_org_id : ' .$userOrgId .' role_code : ' .$roleCode);
                if($userOrgId && $roleCode){
                    $parameters =  collect([]);            
                    $parameters->put("user_org_id", $userOrgId);
                    $parameters->put("role_code", $roleCode);
                    $parameters->put("action_task", 'Delete duplicate role for sm profile user'); 
                    
                    $getDetail = $thisCls->getPmUserRoleDetail($userOrgId,$roleCode);
                    dump('record before');
                    dump($getDetail);
                    $parameters->put("record_before", $getDetail); 
                    $collect = collect($getDetail);
                    $recordStatus = $collect->pluck('record_status')->toArray();
                    if(count(array_intersect($recordStatus, $rules1)) === count($recordStatus)){
                        MigrateUtils::logDump($rules1);
                        MigrateUtils::logDump(' record_status in rules1 >> user_org_id : '.$userOrgId. ' with role_code : ' .$roleCode );
                        $thisCls->deletePmUserRole($userOrgId, $roleCode, [0]);
                        MigrateUtils::logDump(' done delete record 0' );
                    }else if(count(array_intersect($recordStatus, $rules2)) === count($recordStatus)){
                        MigrateUtils::logDump($rules2);
                        MigrateUtils::logDump(' record_status in rules2 >> user_org_id : '.$userOrgId. ' with role_code : ' .$roleCode );
                        $thisCls->deletePmUserRole($userOrgId, $roleCode, [9]);
                        MigrateUtils::logDump(' done delete record 9' );
                    }else if(count(array_intersect($recordStatus, $rules3)) === count($recordStatus)){
                        MigrateUtils::logDump($rules3);
                        MigrateUtils::logDump(' record_status in rules3 >> user_org_id : '.$userOrgId. ' with role_code : ' .$roleCode );
                        $thisCls->deletePmUserRole($userOrgId, $roleCode, [9]);
                        MigrateUtils::logDump(' done delete record 9' );
                    }else if(count(array_intersect($recordStatus, $rules4)) === count($recordStatus)){
                        MigrateUtils::logDump($rules4);
                        MigrateUtils::logDump(' record_status in rules4 >> user_org_id : '.$userOrgId. ' with role_code : ' .$roleCode );
                        $thisCls->deletePmUserRole($userOrgId, $roleCode, [0,9]);
                        MigrateUtils::logDump(' done delete record 0,9' );
                    }else{
                        dump('no rules match.. please check >> user_org_id : '.$userOrgId. ' with role_code : ' .$roleCode);
                    }
                    $getDetailAfter = $thisCls->getPmUserRoleDetail($userOrgId,$roleCode);
                    dump('record after');
                    dump($getDetailAfter);
                    $parameters->put("record_after", $getDetailAfter); 
                    EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed');
                }
            }
            MigrateUtils::logDump('done'); 
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }
    
    public static function triggerSyncRole($personnelId) {
        try {
            $client = new \GuzzleHttp\Client([
                'base_uri' => 'https://epss.eperolehan.gov.my',
            ]);
            $data = [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'verify' => false
            ];
            $fullUrl =  "https://epss.eperolehan.gov.my/find/userpersonnel-sync-role/".$personnelId;
            MigrateUtils::logDump('Sync user : '.$fullUrl);
            //$response = $client->request('GET', $fullUrl, $data);
            $response = Guzzle::get($fullUrl,$data);
            //dump($response);
            $resultResp = json_decode($response->getBody(), true);
            MigrateUtils::logDump($resultResp);
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            MigrateUtils::logDump(self::class .' > '.__FUNCTION__.' > '.$ex->getMessage());
            MigrateUtils::logDump(self::class .' > '.__FUNCTION__.' > '.$ex->getTraceAsString());
        }      
    }

    protected function getListAdminSupplierRoleNotAsMofSupplierAdmin(){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT s.COMPANY_NAME ,s.EP_NO ,
                uo.user_org_id,
                ur.user_role_id,
                p.personnel_id,
                p.NAME , p.EP_ROLE , p.RECORD_STATUS  AS pers_record_status,ur.ROLE_CODE , ur.RECORD_STATUS ,
                (SELECT count(*) FROM pm_user_role WHERE user_org_id = ur.USER_ORG_ID AND role_code = 'BASIC_SUPPLIER_ADMIN') AS total_basic_supplier_admin,
                (SELECT count(*) FROM pm_user_role WHERE user_org_id = ur.USER_ORG_ID AND role_code = 'MOF_SUPPLIER_ADMIN') AS total_mof_supplier_admin
                FROM sm_personnel p , SM_MOF_ACCOUNT sma ,
                sm_supplier s , pm_user_org uo, 
                pm_user_role ur
                WHERE p.APPL_ID  = s.LATEST_APPL_ID  
                AND p.EP_ROLE = 'MOF_SUPPLIER_ADMIN' 
                AND uo.USER_ID  = p.USER_ID  
                AND ur.USER_ORG_ID = uo.USER_ORG_ID 
                AND s.SUPPLIER_ID = sma.SUPPLIER_ID 
                AND sma.RECORD_STATUS   = 1 
                AND ur.ROLE_CODE  = 'BASIC_SUPPLIER_ADMIN'
                AND s.RECORD_STATUS  = 1
                AND ur.RECORD_STATUS = 1 ");
        return $dataList;
    }

    protected function getDuplicateRole($limit){
         
        $query = "SELECT user_org_id,role_code,count(*) as total FROM pm_user_role WHERE 
                    role_code IN ('MOF_SUPPLIER_ADMIN','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','SUPPLIER_TEMP','MOF_SUPPLIER_USER') 
                    GROUP BY user_org_id ,role_code
                    HAVING count(*) > 1";
        if($limit !== 'null'){
            $query = "select * from ($query) a where rownum <= $limit";
        }
        $dataList = DB::connection('oracle_nextgen_fullgrant')->select(
                $query);
        return $dataList;
    }
    
    protected function getPmUserRoleDetail($userOrgId, $roleCode){
        $dataList = DB::connection('oracle_nextgen_fullgrant')->select(
                                "SELECT user_org_id,user_role_id,role_code,record_status FROM pm_user_role WHERE 
                                user_org_id = ? and role_code = ?", array($userOrgId, $roleCode));
        return $dataList;
    }
    
        protected function deletePmUserRole($userOrgId, $roleCode, $recordStatus){
        $dataList = DB::connection('oracle_nextgen_fullgrant')
                        ->table('PM_USER_ROLE')->where('user_org_id',$userOrgId)
                                ->where('role_code',$roleCode)
                                ->whereIn('record_status',$recordStatus)
                        ->delete();
        return $dataList;
    }
}
