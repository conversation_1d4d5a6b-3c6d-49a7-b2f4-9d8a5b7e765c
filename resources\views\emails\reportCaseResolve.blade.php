<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title> Cases Solution Statistics (CDC CRM)</title>
        <meta name="viewport" content="width=device-width" />
       <style type="text/css">
            @media only screen and (max-width: 550px), screen and (max-device-width: 550px) {
                body[yahoo] .buttonwrapper { background-color: transparent !important; }
                body[yahoo] .button { padding: 0 !important; }
                body[yahoo] .button a { background-color: #fff; padding: 15px 25px !important; }
            }

            @media only screen and (min-device-width: 601px) {
                .content { width: 600px !important; }
                .col387 { width: 387px !important; }
            }
        </style>
    </head>
    <body bgcolor="#fff" style="margin: 0; padding: 0; font-family: tahoma;" yahoo="fix">
        <h3>CRM Cases Resolution Statistic on  {{$date}}</h3>
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:lightgreen;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Total pending input cases (Jan 1, 2018 to {{$date}})
                </th>
            </tr>
            </thead>
            @foreach($totalPendingInputCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px;  "> 
                    CASES AS PENDING INPUT 
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;"> 
                    {{$obj->total}}
                </td>
            </tr>
            @endforeach
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:lightgreen;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Total pending IT cases (Jan 1, 2018 to {{$date}})
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($totalPendingCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px;  "> 
                    {{strtoupper($obj->case_type)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;"> 
                    {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />
        
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:lightgreen;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Total In Progress (Assigned to Build) IT cases (Jan 1, 2018 to {{$date}})
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($totalInProgressCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px;  "> 
                    {{strtoupper($obj->case_type)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;"> 
                    {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:lightgreen;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Total Resolved (have been resolved by support & pending user verification for CS to close) IT cases (Jan 1, 2018 to {{$date}})
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($totalVerifyCSCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px;  "> 
                    {{strtoupper($obj->case_type)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;"> 
                    {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:lightgreen;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Total closed IT cases (Jan 1, 2018 to {{$date}})
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($totalCloseCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px;  "> 
                    {{strtoupper($obj->case_type)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;"> 
                    {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />

        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#31b0d5;">
            <tr>
                <th align="left" colspan="3" style="padding-left: 5px;padding-right: 5px; ">
                  Daily total IT cases statistic ({{$date}})
                </th>
            </tr> 
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Case Type
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  Total New Created
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  Total Resolved
                </th>
            </tr>
            </thead>
            @php($sum_total_created = 0)
            @php($sum_total_resolved = 0)
            @foreach($totalStatisticCases as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->case_type)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_created}} @php($sum_total_created = $sum_total_created + $obj->total_created)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_resolved}} @php($sum_total_resolved = $sum_total_resolved + $obj->total_resolved)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_created}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  {{$sum_total_resolved}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        <br />
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#31b0d5;">
            <tr>
                <th align="left" colspan="5" style="padding-left: 5px;padding-right: 5px; ">
                  Daily total IT cases statistic by priority module ({{$date}})
                </th>
            </tr>    
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Module
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total All Pending (assigned/in progress)
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total Pending (assigned/in progress)
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total New Created
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;">
                  Total Resolved
                </th>
            </tr>
            </thead>
            @php($sum_total_all_pending = 0)
            @php($sum_total_pending = 0)
            @php($sum_total_created = 0)
            @php($sum_total_resolved = 0)
            @foreach($totalStatisticByModule as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->module)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_all_pending}} @php($sum_total_all_pending = $sum_total_all_pending + $obj->total_all_pending)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_pending}} @php($sum_total_pending = $sum_total_pending + $obj->total_pending)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_created}} @php($sum_total_created = $sum_total_created + $obj->total_created)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_resolved}} @php($sum_total_resolved = $sum_total_resolved + $obj->total_resolved)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_all_pending}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_pending}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_created}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  {{$sum_total_resolved}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#31b0d5;">
            <tr>
                <th align="left" colspan="5" style="padding-left: 5px;padding-right: 5px; ">
                  Daily total IT cases statistic by others module ({{$date}})
                </th>
            </tr> 
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Module
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total All (assigned/in progress)
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total Pending (assigned/in progress)
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  Total New Created
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  Total Resolved
                </th>
            </tr>
            </thead>
            @php($sum_total_all_pending = 0)
            @php($sum_total_pending = 0)
            @php($sum_total_created = 0)
            @php($sum_total_resolved = 0)
            @foreach($totalStatisticByModuleOther as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->module)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_all_pending}} @php($sum_total_all_pending = $sum_total_all_pending + $obj->total_all_pending)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_pending}} @php($sum_total_pending = $sum_total_pending + $obj->total_pending)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_created}} @php($sum_total_created = $sum_total_created + $obj->total_created)
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total_resolved}} @php($sum_total_resolved = $sum_total_resolved + $obj->total_resolved)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_all_pending}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_pending}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total_created}}
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px;  font-weight:normal;">
                  {{$sum_total_resolved}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        
        
        <br />
        
        @if($bigboss == false)
       
        {{--
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#fbf069;">
            <tr>
                <th align="left" width="250px" colspan="2" style="padding-left: 5px;padding-right: 5px; ">
                   Cases resolved by users
                </th>
            </tr>    
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Name
                </th>
                <th align="left"  style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Total
                </th>
            </tr>
            </thead>
            @foreach($data as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->fullname)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total}}
                </td>
            </tr>
            @endforeach
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />

        --}}
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#fbf069;">
            <tr>
                <th align="left" width="250px" colspan="3"  style="padding-left: 5px;padding-right: 5px; ">
                   Daily cases resolved by users based on priority module
                </th>
            </tr>    
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Module
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Name
                </th>
                <th align="left"  style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Total
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($dataStatisticUsersResolveByModule as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->module)}}
                </td>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->fullname)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" colspan="2" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        <br />
        
        <!--[if (gte mso 9)|(IE)]>
        <table width="600"  cellpadding="0" cellspacing="0" border="0">
          <tr>
            <td>
        <![endif]-->
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%; max-width: 600px;" class="content">
            <thead style="background-color:#fbf069;">
            <tr>
                <th align="left" width="250px"  colspan="3" style="padding-left: 5px;padding-right: 5px; ">
                   Daily cases resolved by users based on others module
                </th>
            </tr>    
            <tr style="background-color:#f0f5f2;">
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Module
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Name
                </th>
                <th align="left"  style="padding-left: 5px;padding-right: 5px; font-weight:normal;  ">
                   Total
                </th>
            </tr>
            </thead>
            @php($sum_total = 0)
            @foreach($dataStatisticUsersResolveByModuleOther as $obj)
            <tr>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->module)}}
                </td>
                <td align="left" width="250px" style="padding-left: 5px;padding-right: 5px; font-size:13px; ">
                   {{strtoupper($obj->fullname)}}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                   {{$obj->total}} @php($sum_total = $sum_total + $obj->total)
                </td>
            </tr>
            @endforeach
            <tr style="background-color:#f0f5f2;">
                <th align="left" colspan="2" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  TOTAL
                </th>
                <th align="left" style="padding-left: 5px;padding-right: 5px; font-weight:normal; ">
                  {{$sum_total}}
                </th>
            </tr>
        </table>
        <!--[if (gte mso 9)|(IE)]>
                </td>
            </tr>
        </table>
        <![endif]-->
        
        <br />

        
        
        
        @endif

    </body>
</html>
