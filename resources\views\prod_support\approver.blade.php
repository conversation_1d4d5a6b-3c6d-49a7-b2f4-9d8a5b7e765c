@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li>
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li  class="active  @if(!Auth::user()->isPatcherRolesEp()) hide @endif ">
                <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div>

<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> APPROVER USER </strong></h1> 
    </div>   
    <div class="form-group">
        <button type="button" class="btn btn-sm btn-primary add-new-approver"><i class="fa fa-save"></i> Add New Approver</button>
    </div>
    <form class="form-horizontal form-bordered insert-approver-form" id="insert-approver-form" style="display:none" action="{{url('/prod-support/approver/create')}}" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading add-new-port">
            <input type="hidden" id="id" name="id" value="" class="form-control" style="width: 100px;">
            <label class="col-md-1 text-left" for="name">FULL NAME<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <input type="text" id="name" name="name" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="email">EMAIL<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <input type="text" id="email" name="email" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="role">ROLE<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <select id="role" name = "role" required class="form-control" style="width: 700px;">
                    <option value="1">Manager Datafix</option>
                    <option value="2">Endorsement Officer</option>
                    <option value="3">Email CC Group</option>
                    <option value="4">Email eP CC Group</option>
                    <option value="5">Email eP MO Group</option>
                </select>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="pull-right">
                <button type = "submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i> Save</button>
                <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
            </div>
        </div>
    </form>
    <form class="form-horizontal form-bordered edit-approver-form" id="edit-approver-form" style="display:none" action="/prod-support/edit-approver" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading add-new-port">
            <input type="hidden" id="editid" name="editid" value="" class="form-control" style="width: 100px;">
            <label class="col-md-1 text-left" for="code">FULL NAME<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="editname" name="editname" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="name">EMAIL<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="editemail" name="editemail" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="role">ROLE<span class="text-danger">*</span></label>
            <div class="col-md-2">   
                <select id="editrole" name="editrole" value="" required class="form-control" style="width: 500px;" style="display:none">
                    <option value="1">Manager Datafix</option>
                    <option value="2">Endorsement Officer</option>
                    <option value="3">Email CC Group</option>
                    <option value="4">Email eP CC Group</option>
                    <option value="5">Email eP MO Group</option>
                </select>
            </div>
            <label class="col-md-1 text-left" for="statusedit">STATUS<span class="text-danger">*</span></label>
            <div class="col-md-2">   
                <select id="statusedit" name="statusedit" value="" required class="form-control" style="width: 500px;" style="display:none">
                    <option value="1">Active</option>
                    <option value="2">Inactive</option>
                </select>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="pull-right">
                <button type = "submit" class="btn btn-sm btn-primary save-edit"><i class="fa fa-save"></i> Save</button>
                <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
            </div>
        </div>
    </form>

    <table id="datalookup-datatable" class="table table-vcenter table-condensed table-bordered usertable">
        <thead>
            <tr>
                <th class="text-center">NO</th>
                <th class="text-center">FULL NAME</th>
                <th class="text-center">EMAIL</th>
                <th class="text-center">ROLE</th>
                <th class="text-center">STATUS</th>
                <th class="text-center">ACTION</th>
            </tr>                
        </thead>    
        <tbody>
            @if($getApprover != null)
            @foreach($getApprover as $rowData => $data)
            <tr>
                <td class="text-center">{{ ++$rowData }}</td>
                <td class="text-center">{{ $data->full_name }}</td>
                <td class="text-center">{{ $data->email }}</td>
                <td class="text-center">@if($data->is_manager_datafix == 1){{ $data->manager }} @elseif ($data->is_ep_endorsed == 1) {{ $data->ep_endorser }} @elseif ($data->is_group_cc == 1) {{$data->email_cc_group}} @elseif ($data->is_ep_group_cc == 1) {{$data->email_eP_cc_group}} @else {{$data->email_mo_group}} @endif</td>
                <td class="text-center">{{ $data->record }}</td>
                <td class="text-center">
                    <div class="btn-group btn-group-xs">
                        <a idno ="{{$data->data_fix_user_id}}" 
                           fullname ="{{ $data->full_name }}"
                           emailuser ="{{ $data->email }}"
                           ismanager ="{{ $data->manager }}"
                           ismanagernumber ="{{ $data->is_manager_datafix }}"
                           isependorser ="{{ $data->ep_endorser }}"
                           isependorserno ="{{ $data->is_ep_endorsed }}"
                           emailcc ="{{ $data->email_cc_group }}"
                           emailccno ="{{ $data->is_group_cc }}"
                           emailePccno ="{{ $data->is_ep_group_cc }}"
                           emailtomo ="{{ $data->is_mo_group }}"
                           statususer ="{{$data->record_status}}"
                           data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                        <a idnom ="{{$data->data_fix_user_id}}" 
                           data-toggle="tooltip" title="Delete" class="btn btn-sm btn-danger delete_user"><i class="fa fa-times"></i></a>
                    </div> 
                </td>
            </tr>
            @endforeach
            @endif
        </tbody>
    </table>

    <input type="hidden" id="delid" name="delid" value="" class="form-control" style="width: 100px;">
    <div id="modal_confirm_delete_user" class="modal fade">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h5> CONFIRMATION</h5>
                </div>
                <div class="modal-body text-center">
                    <label>Are You Sure To Delete User? </label> &nbsp;&nbsp;&nbsp;
                </div> 
                <br/><br/>
                <div class="modal-footer">
                    <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                    <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                </div>
            </div>
        </div>
    </div>
</DIV> 

@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        $('#to-top').click();
        TablesDatatables.init();
    });
    App.datatables();


    $(".add-new-approver").on("click", function () {
        $("#insert-approver-form").show();
        $("#id").val("");
        $("#name").val("");
        $("#email").val("");
        $("#role").val("");
        $("#edit-approver-form").hide();
    });

    $(".resetbutton").on("click", function () {
        $("#editid").val("");
        $("#editname").val("");
        $("#editemail").val("");
        $("#editrole").val("");
        $("#edit-approver-form").hide();
        $("#insert-approver-form").hide();
    });

    $(".editbutton").on("click", function () {
        $('#to-top').click();
        $("#insert-approver-form").hide();
        $("#edit-approver-form").show();
        let
        id = $(this).attr('idno');
        let
        name = $(this).attr('fullname');
        let
        email = $(this).attr('emailuser');
        let
        statususers = $(this).attr('statususer');
        let
        ori_is_manager = $(this).attr('ismanagernumber');
        let
        ori_ep_manager = $(this).attr('isependorserno');
        let
        ori_email_cc = $(this).attr('emailccno');
        let
        ori_email_ep_cc = $(this).attr('emailePccno');
        let
        ori_email_to_mo = $(this).attr('emailtomo');

        console.log(statususers);
        if (ori_is_manager == "1") {
            $("#editrole").val('1');
        }
        else if (ori_ep_manager == "1") {
            $("#editrole").val('2');
        }
        else if (ori_email_cc == "1") {
            $("#editrole").val('3');
        }
        else if (ori_email_ep_cc == "1") {
            $("#editrole").val('4');
        }
        else if (ori_email_to_mo == "1") {
            $("#editrole").val('5');
        }
        if (statususers == "1") {
            $("#statusedit").val('1');
        }
        else {
            $("#statusedit").val('2');
        }
        $('#editid').val(id);
        $("#editname").val(name);
        $("#editemail").val(email);
    });

    $(".delete_user").on("click", function () {
        $("#modal_confirm_delete_user").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
        console.log(delid);
    });

    $('#submit_confirm_delete').on('click', function () {
        $('#modal_confirm_delete_user').modal('hide');
        $Id = $("#delid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/delete-approver/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });
</script>
@endsection        

