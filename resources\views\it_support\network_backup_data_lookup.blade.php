@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/it_support/network/backup/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/network/backup/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/network/backup/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> DATA LOOKUP </strong></h1>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-sm btn-primary add_new_lookup"><i class="fa fa-save"></i> Add New
                Lookup</button>
        </div>
        <form class="form-horizontal form-bordered insert_lookup_form" id="insert_lookup_form" style="display:none"
            action="{{ url('/it_support/network/backup/add_data_lookup/create') }}" method="post">
            {{ csrf_field() }}
            <div class="row">
                <div class="form-group">
                    <input type="hidden" id="task_id" name="task_id" value="" class="form-control"
                        style="width: 100px;">
                    <label class="col-md-1 text-center" for="name">Device<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="device_name" name="device_name" value="" required
                            class="form-control">
                    </div>
                    <label class="col-md-1 text-center" for="name">Description<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="device_desc" name="device_desc" value="" required
                            class="form-control">
                    </div>
                    <label class="col-md-1 text-center" for="name">Hostname<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="hostname" name="hostname" value="" required class="form-control">
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-md-1 text-center" for="name">IP Address<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="ip_address" name="ip_address" value="" required class="form-control">
                    </div>
                    <label class="col-md-1 text-center" for="name">Location<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="device_location" name="device_location" value="" required
                            class="form-control">
                    </div>
                    <label class="col-md-1 text-center" for="status">Status<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <select id="task_status" name = "task_status" class="form-control" style="width: 700px;">
                            <option value="active">Active</option>
                            <option value="inactive">In Active</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="pull-right">
                    <button type="submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i>
                        Save</button>
                    <button type="reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i
                            class="fa fa-repeat"></i> Reset</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table id="datalookup_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Device</th>
                        <th class="text-center">Description</th>
                        <th class="text-center">Hostname</th>
                        <th class="text-center">IP Address</th>
                        <th class="text-center">Location</th>
                        <th class="text-center">Created By</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed By</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Task Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($getLookupdate != null)
                        @foreach ($getLookupdate as $rowData => $data)
                            <tr>
                                <td class="text-center">{{ ++$rowData }}</td>
                                <td class="text-center">{{ $data->nbl_device }}</td>
                                <td class="text-center">{{ $data->nbl_description }}</td>
                                <td class="text-center">{{ $data->nbl_hostname }}</td>
                                <td class="text-center">{{ $data->nbl_ip_address }}</td>
                                <td class="text-center">{{ $data->nbl_location }}</td>
                                <td class="text-center">{{ $data->nbl_created_by }}</td>
                                <td class="text-center">{{ $data->nbl_created_date }}</td>
                                <td class="text-center">{{ $data->nbl_changed_by }}</td>
                                <td class="text-center">{{ $data->nbl_changed_date }}</td>
                                <td class="text-center">{{ $data->nbl_status }}</td>
                                <td class="text-center" action_table_task>
                                    <div class="btn-group btn-group-xs">
                                        <a idno="{{ $data->nbl_data_id }}" deviceName="{{ $data->nbl_device }}"
                                            deviceDesc="{{ $data->nbl_description }}"
                                            deviceHostname="{{ $data->nbl_hostname }}"
                                            deviceIp="{{ $data->nbl_ip_address }}" deviceLoc="{{ $data->nbl_location }}"
                                            statusName="{{ $data->nbl_status }}" data-toggle="tooltip" title="Edit"
                                            class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </DIV>

@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
        $(document).ready(function() {
            $('#datalookup_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
        });
    </script>
    <script>
        $(function() {
            $('#to-top').click();
        });


        $(".add_new_lookup").on("click", function() {
            $("#insert_lookup_form").show();
            resetFormFields();
        });

        $(".resetbutton").on("click", resetFormFields);

        function resetFormFields() {
            $("#task_id, #device_name, #device_desc, #hostname, #ip_address, #device_location").val("");
        }

        $(".editbutton").on("click", function() {
            $('#to-top').click();
            $("#insert_lookup_form").show();
            let idno = $(this).attr('idno');
            let deviceName = $(this).attr('deviceName');
            let deviceDesc = $(this).attr('deviceDesc');
            let deviceHostname = $(this).attr('deviceHostname');
            let deviceIp = $(this).attr('deviceIp');
            let deviceLoc = $(this).attr('deviceLoc');
            let statusName = $(this).attr('statusName');

            $("#task_id").val(idno);
            $("#device_name").val(deviceName);
            $("#device_desc").val(deviceDesc);
            $("#hostname").val(deviceHostname);
            $("#ip_address").val(deviceIp);
            $("#device_location").val(deviceLoc);
            $("#task_status").val(statusName);
        });
    </script>
@endsection
