@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form action="{{url('/find/supplier')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="carian" name="carian" value="@if(isset($carian)){{$carian}}@endif" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

<!-- Courses Header -->
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="fa fa-globe"></i>Search <strong>Supplier</strong><br><small>MOF NO. or eP No. or IC No. or Appl No.</small>
        </h1>
    </div>
</div>

@if(isset($data))
<!-- Success Alert Content -->
<div class="alert alert-success alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-check-circle"></i> Found {{$data->count()}} records supplier</h4>
</div>
@foreach ($data as $key => $supplierInfo)
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <div class="block-options pull-right">
            <a href="javascript:void(0)" class="btn btn-alt btn-sm btn-primary" data-toggle="block-toggle-content"><i class="fa fa-arrows-v"></i></a>
        </div>
        <h1><i class="fa fa-building-o"></i> <strong>{{$key+1}}) ePerolehan (Pembekal) : {{$supplierInfo['supplier']->company_name}}</strong></h1>
    </div>
    <div class="row block-content">

        <!-- Main Row -->
        <div class="row">
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Supplier</strong> Info</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Company Name</td>
                                    <td>
                                        <strong>{{$supplierInfo['supplier']->company_name}}
                                            @if($supplierInfo['basicCompInfo'] != null && $supplierInfo['basicCompInfo']->is_name_hq_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Company name has special character Non-ASCII"></i>@endif</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">eP No.</td>
                                    <td>
                                        <strong>{{$supplierInfo['supplier']->ep_no}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">SSM No.</td>
                                    <td>
                                        <strong>{{$supplierInfo['supplier']->reg_no}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Business Type</td>
                                    <td>
                                        <strong>{{$supplierInfo['supplier']->business_type}} &raquo; ({{ App\Services\EPService::$BUSINESS_TYPE[$supplierInfo['supplier']->business_type] }}) </strong>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="text-right">SAP Vendor Code</td>
                                    <td>
                                        <strong>@if($supplierInfo['sapVendorCode']) {{$supplierInfo['sapVendorCode']->sap_vendor_code}} @endif</strong>
                                    </td>
                                </tr>
                                @if($supplierInfo['basicCompInfo'] != null)
                                <tr>
                                    <td class="text-right">Country Origin</td>
                                    <td>
                                        <strong>
                                            {{$supplierInfo['basicCompInfo']->ssm_company_country}}
                                            @if( array_key_exists($supplierInfo['basicCompInfo']->ssm_company_country, App\Services\EPService::$CPTPP_COUNTRY) == true)
                                            - {{App\Services\EPService::$CPTPP_COUNTRY[$supplierInfo['basicCompInfo']->ssm_company_country]}}
                                            <i class="fa fa-info-circle" title='CPTPP Country'></i><br /> (CPTTP Country)</span>
                                            @endif
                                        </strong>
                                    </td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="text-right">Established Date</td>
                                    <td>
                                        <strong>{{null != $supplierInfo['supplier']->establish_date? Carbon\Carbon::parse($supplierInfo['supplier']->establish_date)->format('d-m-Y'):''}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Record Status</td>
                                    <td>
                                        <strong>{{ $supplierInfo['supplier']->s_record_status }} &raquo; <span @if($supplierInfo['supplier']->s_record_status == 9) class="text-danger" @endif>({{ App\Services\EPService::$RECORD_STATUS[$supplierInfo['supplier']->s_record_status] }})</span></strong>
                                    </td>
                                </tr>
                                @if($supplierInfo['suppMofStatus'] != null)
                                <tr>
                                    <td class="text-right">Bumi Status</td>
                                    <td>
                                        <strong>{{ $supplierInfo['suppMofStatus']->bumi_status }}</strong>
                                    </td>
                                </tr>
                                @endif
                                @if($supplierInfo['applDetail'] != null)
                                <tr>
                                    <td class="text-right">Supplier Type</td>
                                    <td>
                                        <strong>{{ $supplierInfo['applDetail']->supplier_type }} &raquo; {{ App\Services\EPService::$SUPPLIER_TYPE[$supplierInfo['applDetail']->supplier_type] }}</strong>
                                    </td>
                                </tr>
                                @endif

                                <tr>
                                    <td class="text-right">Total Items</td>
                                    <td>
                                        <strong>{{ $supplierInfo['totalItems']}}</strong>
                                    </td>
                                </tr>

                                @if($supplierInfo['basicCompInfo'] != null)
                                <tr>
                                    <td class="text-right">Is Federal?</td>
                                    <td>
                                        <strong>{{$supplierInfo['basicCompInfo']->is_with_federal}}</strong>
                                    </td>
                                </tr>
                                @endif
                                <tr>
                                    <td class="text-right">MOF No.</td>
                                    <td>
                                        <strong>{{$supplierInfo['supplier']->mof_no}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">MOF Exp Date</td>
                                    <td>
                                        <strong>{{null != $supplierInfo['supplier']->ma_exp_date? Carbon\Carbon::parse($supplierInfo['supplier']->ma_exp_date)->format('d-m-Y'):''}}</strong>
                                    </td>
                                </tr>
                                @if($supplierInfo['basicCompInfo'] != null)
                                <tr>
                                    <td class="text-right">Address</td>
                                    <td>
                                        <strong>{{ $supplierInfo['basicCompInfo']->address_1 }}<br />
                                            {{ $supplierInfo['basicCompInfo']->address_2 }} {{ $supplierInfo['basicCompInfo']->address_3 }}<br />
                                            {{ $supplierInfo['basicCompInfo']->postcode }} {{ $supplierInfo['basicCompInfo']->city_name }}, {{ $supplierInfo['basicCompInfo']->district_name }}<br />
                                            {{ $supplierInfo['basicCompInfo']->state_name }}, {{ $supplierInfo['basicCompInfo']->country_name }}
                                            @if($supplierInfo['basicCompInfo']->nonAsciiHqDetected)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Address has special character Non-ASCII"></i>@endif </strong>
                                    </td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Application</strong> Registered</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        @if($supplierInfo['applDetail'] != null)
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Appl No</td>
                                    <td>
                                        <strong>{{$supplierInfo['applDetail']->appl_no}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Created</td>
                                    <td>
                                        <strong>{{$supplierInfo['applDetail']->created_date}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Changed</td>
                                    <td>
                                        <strong>{{$supplierInfo['applDetail']->changed_date}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Appl Status</td>
                                    <td>
                                        <strong>({{$supplierInfo['applDetail']->status_id}}) - {{$supplierInfo['applDetail']->status_name}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Supporting Documents</td>
                                    <td>
                                        <strong>{{ App\Services\EPService::$SUPPORTING_DOC_MODE[$supplierInfo['applDetail']->supporting_doc_mode]  }}</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        @endif

                        @if(isset($supplierInfo['listApplSectionReview']) && count($supplierInfo['listApplSectionReview']) > 0)
                        <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                        @foreach($supplierInfo['listApplSectionReview'] as $key => $objRev )
                        @if($objRev->remark_to_approver && strlen($objRev->remark_to_approver) > 0)
                        <strong>Action By User Role: {{$objRev->role_type}}</strong> <br />
                        <strong>Recommendation: {{$objRev->recommendation}}</strong> on {{$objRev->remark_date}} <br />
                        <strong>Remark:</strong><span style="font-style: italic">{{ $objRev->remark_to_approver  }}</span> <br />
                        @endif
                        @endforeach
                        <br /><br />
                        @endif

                        @if(isset($supplierInfo['listApplRejectReason']) && count($supplierInfo['listApplRejectReason']) > 0)
                        <strong><span class="bolder">Reject Reason </span></strong><br />
                        @foreach($supplierInfo['listApplRejectReason'] as $key => $objRej )
                        <strong>{{$key+1}} : on {{$objRev->changed_date}} </strong><br />
                        <strong>Remark:</strong><span style="font-style: italic">{{ $objRej->reason_desc  }}</span> <br />
                        @endforeach
                        <br /><br />
                        @endif

                        @if(isset($supplierInfo['listRemarksCancelReject']) && count($supplierInfo['listRemarksCancelReject']) > 0)
                        <strong><span class="bolder">Remarks </span></strong><br />
                        @foreach($supplierInfo['listRemarksCancelReject'] as $objRemark)
                        <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span> <br />
                        @endforeach
                        <br /><br />
                        @endif

                        @if(isset($supplierInfo['listAttachmentCancelReject']) && count($supplierInfo['listAttachmentCancelReject']) > 0)
                        <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                        @foreach($supplierInfo['listAttachmentCancelReject'] as $objAttReject)
                        <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank">{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                        @endforeach
                        <br /><br />
                        @endif

                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Application</strong> In Progress</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        @if(isset($supplierInfo['listinProgressSuppProcessAppl']) && $supplierInfo['listinProgressSuppProcessAppl'] != null)
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Appl No</td>
                                    <td>
                                        <strong>{{$supplierInfo['listinProgressSuppProcessAppl'][0]->appl_no}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Created</td>
                                    <td>
                                        <strong>{{$supplierInfo['listinProgressSuppProcessAppl'][0]->appl_created_date}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Changed</td>
                                    <td>
                                        <strong>{{$supplierInfo['listinProgressSuppProcessAppl'][0]->appl_change_date}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Appl Status</td>
                                    <td>
                                        <strong>({{$supplierInfo['listinProgressSuppProcessAppl'][0]->appl_status_id}}) - {{$supplierInfo['listinProgressSuppProcessAppl'][0]->appl_status}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">WorkFlow Status</td>
                                    <td>
                                        <strong>({{$supplierInfo['listinProgressSuppProcessAppl'][0]->wf_status_id}}) - {{$supplierInfo['listinProgressSuppProcessAppl'][0]->wf_status}}</strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Supporting Documents</td>
                                    <td>
                                        <strong>{{ App\Services\EPService::$SUPPORTING_DOC_MODE[$supplierInfo['listinProgressSuppProcessAppl'][0]->supporting_doc_mode]  }}</strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        @else
                        <p>Currently no application in progress.</p>
                        @endif

                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
        </div>
        <!-- Main Row -->
        <div class="row">
            <div class="col-md-12">
                <!-- Most Viewed Courses Block -->
                <div class="block">
                    <!-- Most Viewed Courses Title -->
                    <div class="block-title text-info">
                        <div class="block-options pull-right">
                            <a href="javascript:void(0)" class="btn btn-alt btn-sm btn-primary" data-toggle="block-toggle-content"><i class="fa fa-arrows-v"></i></a>
                        </div>
                        <h2><strong>Latest Application </strong> History
                            <small> <a href="javascript:void(0)" class="modal-list-data-action btn btn-info btn-xs">
                                    @if(isset($supplierInfo['listApplHistoryDetails']) && $supplierInfo['listApplHistoryDetails'] != null )
                                    {{count($supplierInfo['listApplHistoryDetails'])}}
                                    @endif
                                </a> </small>
                        </h2>
                    </div>
                    <!-- END Most Viewed Courses Title -->
                    <div class="block-content">
                        <div class="row">
                            <div class="col-md-12">
                                @if(isset($supplierInfo['listApplHistoryDetails']) && $supplierInfo['listApplHistoryDetails'] != null )
                                <div class="table-responsive">
                                    <table class="table  table-condensed table-bordered table-vcenter">
                                        <thead>
                                            <tr>
                                                <th width="10%">Appl No</th>
                                                <th width="13%">Appl Status</th>
                                                <th>Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($supplierInfo['listApplHistoryDetails'] as $key => $appHistory)
                                            <tr>
                                                <td>{{$appHistory->appl_no}}</td>
                                                <td>
                                                    {{$appHistory->status_name}}
                                                </td>
                                                <td>
                                                    @if(isset($appHistory->listApplSectionReview ) && count($appHistory->listApplSectionReview ) > 0)
                                                    <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                                                    @foreach($appHistory->listApplSectionReview as $key => $objRev )
                                                    @if($objRev->remark_to_approver && strlen($objRev->remark_to_approver) > 0)
                                                    <strong>Action By User Role: {{$objRev->role_type}}</strong> <br />
                                                    <strong>Recommendation: {{$objRev->recommendation}}</strong> on {{$objRev->remark_date}} <br />
                                                    <strong>Remark:</strong><span style="font-style: italic">{{ $objRev->remark_to_approver  }}</span> <br />
                                                    @endif
                                                    @endforeach
                                                    <br />
                                                    @endif

                                                    @if(isset($appHistory->listApplRejectReason) && count($appHistory->listApplRejectReason) > 0)
                                                    <strong><span class="bolder">Reject Reason </span></strong><br />
                                                    @foreach($appHistory->listApplRejectReason as $key => $objRej )
                                                    <strong>{{$key+1}} : on {{$objRev->changed_date}} </strong><br />
                                                    <strong>Remark:</strong><span style="font-style: italic">{{ $objRej->reason_desc  }}</span> <br />
                                                    @endforeach
                                                    <br />
                                                    @endif

                                                    @if(isset($appHistory->listRemarksCancelReject) && count($appHistory->listRemarksCancelReject) > 0)
                                                    <strong><span class="bolder">Remarks </span></strong><br />
                                                    @foreach($appHistory->listRemarksCancelReject as $objRemark)
                                                    <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span> <br />
                                                    @endforeach
                                                    <br />
                                                    @endif

                                                    @if(isset($appHistory->listAttachmentCancelReject) && count($appHistory->listAttachmentCancelReject) > 0)
                                                    <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                                                    @foreach($appHistory->listAttachmentCancelReject as $objAttReject)
                                                    <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank">{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                                                    @endforeach
                                                    @endif
                                                    <div class='widget'>
                                                        <div id="count-application-inquiry-{{$appHistory->appl_id}}">
                                                            <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                                                        </div>
                                                    </div>
                                                    <div class='widget'>
                                                        <div id="count-application-rejected-{{$appHistory->appl_id}}">
                                                            <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- END Most Viewed Courses Content -->
                </div>
                <!-- END Most Viewed Courses Block -->
            </div>

        </div>
        <!-- END Main Row -->

        <!-- Main Row -->
        <div class="row">
            <div class="col-md-12">
                <!-- Most Viewed Courses Block -->
                <div class="block">
                    <!-- Most Viewed Courses Title -->
                    <div class="block-title text-info">
                        <h2><strong>Users </strong> Info </h2>
                    </div>
                    <!-- END Most Viewed Courses Title -->
                    <div class="block-section">
                        <div class="row">
                            <div class="col-md-12">
                                @if(isset($supplierInfo['listPersonnel']) && $supplierInfo['listPersonnel'] != null )
                                @foreach ($supplierInfo['listPersonnel'] as $key => $personnel)
                                <div class="block">
                                    <!-- Your Account Title -->
                                    <div class="block-title">
                                        <h2><strong>{{$key+1}}) {{$personnel->p_name}}</h2>
                                        <span>{{$personnel->p_ep_role}}</span>
                                    </div>
                                    <!-- END Your Account Title -->

                                    <!-- Your Account Content -->
                                    <div class="block-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Login</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        @if($personnel->login_id != null)
                                                        <table class="table table-borderless table-striped table-vcenter">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="text-right">Login ID</td>
                                                                    <td>
                                                                        <strong>{{$personnel->login_id}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Name</td>
                                                                    <td>
                                                                        <strong>{{$personnel->fullname}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Email</td>
                                                                    <td>
                                                                        <strong>{{$personnel->email}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">IC No.</td>
                                                                    <td>
                                                                        <strong>{{$personnel->identification_no}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Mobile</td>
                                                                    <td>
                                                                        <strong>{{$personnel->mobile_country}}{{$personnel->mobile_area}}{{$personnel->mobile_no}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Last Login Date</td>
                                                                    <td>
                                                                        <strong>{{$personnel->login_date}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Record Status</td>
                                                                    <td>
                                                                        <strong>{{$personnel->u_record_status}}</strong>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        @else
                                                        <p>This personnel do not has login info.</p>
                                                        @endif
                                                    </div>
                                                    <!-- END Your Account Content -->
                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Personnel</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <table class="table table-borderless table-striped table-vcenter">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="text-right">Name</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_name}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Email</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_email}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">IC No.</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_identification_no}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Mobile</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_mobile_country}}{{$personnel->p_mobile_area}}{{$personnel->p_mobile_no}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Designation</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_designation}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Record Status</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_record_status}}</strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Softcert Status</td>
                                                                    <td>
                                                                        <strong>{{$personnel->p_is_softcert}}</strong>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <!-- END Your Account Content -->
                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>

                                            @if(isset($personnel->listSoftCert) && $personnel->listSoftCert != null )
                                            <div class="col-md-12">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Softcert</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <div class="table-responsive">
                                                            <table class="table  table-condensed table-bordered table-vcenter">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Request ID</th>
                                                                        <th>Provider</th>
                                                                        <th>Record Status</th>
                                                                        <th>Created</th>
                                                                        <th>Is Free</th>
                                                                        <th>Apply Document</th>
                                                                        <th>Issuer</th>
                                                                        <th>Valid From</th>
                                                                        <th>Valid To</th>
                                                                        <th>Cert Updated</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($personnel->listSoftCert as $softcert)
                                                                    <tr @if($softcert->is_active_cert === true) class="info" @endif>
                                                                        <td>{{$softcert->softcert_request_id}} {{$softcert->is_active_cert}}</td>
                                                                        <td>{{$softcert->softcert_provider}}</td>
                                                                        <td>{{$softcert->record_status}}
                                                                        @if($softcert->record_status == 1 && $softcert->softcert_provider == 'TG' && $personnel->p_is_softcert == 3) 
                                                                            <a target="_blank" href="https://www.msctrustgate.com/mytrustid/client/cdc_support?token=sign" 
                                                                                style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                                RESEND UPDATE SOFTCERT <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt;" title="Check on TG Portal if already issued! "></i>
                                                                            </a>
                                                                        @endif

                                                                        </td>
                                                                        <td>{{$softcert->created_date}}</td>
                                                                        <td>{{$softcert->is_free}}</td>
                                                                        <td>{{$softcert->type_apply}}</td>
                                                                        <td> @if($softcert->valid_to != null) @if($softcert->cert_issuer=='T') TrustGate @else Digicert @endif @endif</td>
                                                                        <td>{{$softcert->valid_from}}</td>
                                                                        <td>{{$softcert->valid_to}}</td>
                                                                        <td>{{$softcert->pdc_changed_date}}</td>
                                                                    </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!-- END Your Account Content -->

                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>
                                            @endif

                                        </div>

                                    </div>
                                    <!-- END Your Account Content -->
                                </div>

                                @endforeach
                                @else
                                <div class="notify-alert alert alert-warning alert-dismissable">
                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                    <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    <!-- END Most Viewed Courses Content -->
                </div>
                <!-- END Most Viewed Courses Block -->
            </div>

        </div>
        <!-- END Main Row -->
    </div>
</div>
@endforeach


@endif
<div id="modal-list-data-inquiries-reject" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-inquiries-reject">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-inquiries-reject" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    var APP_URL = {!!json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable-inquiries-reject').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });
</script>
@if(isset($supplierInfo['listApplHistoryDetails']) && count($supplierInfo['listApplHistoryDetails']) > 0 )
<script>
    $(document).ready(function() {
        $('.widget').on("click", '.modal-list-data-action', function() {
            $('.spinner-loading').show();
            $('#basic-datatable-inquiries-reject').html('').fadeIn();
            $('#modal-list-data-header-inquiries-reject').text($(this).attr('data-title'));

            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-inquiries-reject').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-inquiries-reject').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });
            $.ajax({
                url: APP_URL + "/find/supplier/application-inquiry/count/{{$appHistory->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-inquiry-{{$appHistory->appl_id}}').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + "/find/supplier/application-rejected/count/{{$appHistory->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-rejected-{{$appHistory->appl_id}}').hide().html($data).fadeIn();
                }
            });

        })
    });
</script>
@foreach ($supplierInfo['listApplHistoryDetails'] as $key => $appHistory)
<script>
    $(document).ready(function() {
        $.ajax({
            url: APP_URL + "/find/supplier/application-inquiry/count/{{$appHistory->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-inquiry-{{$appHistory->appl_id}}').hide().html($data).fadeIn();
            }
        });

        $.ajax({
            url: APP_URL + "/find/supplier/application-rejected/count/{{$appHistory->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-rejected-{{$appHistory->appl_id}}').hide().html($data).fadeIn();
            }
        });
    });
</script>
@endforeach
@endif
@endsection