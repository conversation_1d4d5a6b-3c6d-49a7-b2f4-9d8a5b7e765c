<?php

namespace App\Services\Helpdesk;

use DB;
use Carbon\Carbon;
use Log;
use Exception;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class HelpdeskService {

    public function getTicketDetail($ticketNumber) {
        $query = "select ticket.ticket_id, ticket.number, tdata.subject, tdata.reason,
                    ticket.created, ticket.updated,
                    tstatus.name as statusname, tstatus.state, tuser.name, dept.name as deptname, 
                    topic.topic as topic1,ptopic.topic as topic2,pptopic.topic as topic3,
                    thread.title, thread.body, d.id AS thread_id
                    from ost_ticket ticket
                    join ost_ticket__cdata tdata on tdata.ticket_id = ticket.ticket_id
                    JOIN ost_thread d ON d.object_id = ticket.ticket_id
                    join ost_ticket_status tstatus on ticket.status_id = tstatus.id
                    join ost_user tuser on tuser.id = ticket.user_id
                    join ost_department dept on dept.id = ticket.dept_id
                    join ost_help_topic topic on ticket.topic_id = topic.topic_id
                    left join ost_help_topic ptopic on ptopic.topic_id = topic.topic_pid 
                    left join ost_help_topic pptopic on pptopic.topic_id = ptopic.topic_pid
                    join ost_thread_entry thread on thread.thread_id = ticket.ticket_id
                    where ticket.number = '$ticketNumber'";

        return DB::connection('mysql_helpdesk')
                        ->select($query);
    }

    public function listStatus() {
        return $query = DB::connection('mysql_helpdesk')
                ->table('ost_ticket_status')
                ->orderBy('name', 'asc')
                ->get();
    }

    public function updateTicketDetail($ticketId, $updateStatusId) {
        $status = 'Failed';
        $query = DB::connection('mysql_helpdesk')
                ->table('ost_ticket')
                ->where('ticket_id', $ticketId)
                ->update([
            'status_id' => $updateStatusId
        ]);
        $dataStatus = '{"status":' . $updateStatusId . '}';
        $latestThread = self::getLatestThread($ticketId);
        $queryThread = DB::connection('mysql_helpdesk')
                ->table('ost_thread_event')
                ->where('thread_id', $ticketId)
                ->where('id', $latestThread->id)
                ->update([
            'data' => null
        ]);

        if ($queryThread = 1 && $query == 1) {
            $status = 'Success';
        }

        return $status;
    }

    public function updateTicketDetailSubject($ticketId, $updateSubject) {
        $status = 'Failed';
        $query = DB::connection('mysql_helpdesk')
                ->table('ost_ticket__cdata')
                ->where('ticket_id', $ticketId)
                ->update([
            'subject' => $updateSubject
        ]);

        if ($query == 1) {
            $status = 'Success';
        }

        return $status;
    }

    public function getLatestThread($ticketId) {
        return DB::connection('mysql_helpdesk')
                        ->table('ost_thread_event')
                        ->where('thread_id', $ticketId)
                        ->whereNotIn('username', ['SYSTEM'])
                        ->orderBy('timestamp', 'desc')
                        ->first();
    }

    public function getLatestThreadEntry($ticketId) {
        return DB::connection('mysql_helpdesk')
                        ->table('ost_thread_entry')
                        ->where('thread_id', $ticketId)
                        ->orderBy('created', 'desc')
                        ->first();
    }

    public function searchItTicket($ticketNumber) {
        $query = "SELECT DISTINCT a.ticket_id, a.number, a.status_id, a.dept_id, a.topic_id,
                    b.subject, b.reason, d.id AS entry_id,d.poster, d.title, d.body, d.created, c.id as thread_id,
                    d.staff_id 
                    FROM ost_ticket a
                    JOIN ost_ticket__cdata b ON a.ticket_id = b.ticket_id 
                    JOIN ost_thread c ON c.object_id = a.ticket_id
                    LEFT JOIN ost_thread_entry d ON c.id = d.thread_id
                    LEFT JOIN ost_thread_event e ON d.thread_id = e.thread_id
                    WHERE a.number IN ('$ticketNumber')
                    ORDER by d.created";
        return DB::connection('mysql_helpdesk')->select($query);
    }

    public function deleteItTicket($ticketNumber) {
        $query = "DELETE a,b,c,d,e
                    FROM ost_ticket a
                    LEFT JOIN ost_ticket__cdata b ON a.ticket_id = b.ticket_id 
                    LEFT JOIN ost_thread c ON a.ticket_id = c.object_id
                    LEFT JOIN ost_thread_entry d ON c.id = d.thread_id
                    LEFT JOIN ost_thread_event e ON d.thread_id = e.thread_id
                    WHERE a.number IN ('$ticketNumber')";
        return DB::connection('mysql_helpdesk')->select($query);
    }

    public function addNewThread($ticketId, $thread) {
        $latestThreadEntry = self::getLatestThreadEntry($ticketId);
        $userLogin = auth()->user()->id;

        $ostThread = DB::connection('mysql_helpdesk')
                        ->table('ost_thread')
                        ->where('object_id', $ticketId)
                        ->first();

        if(isset($ostThread)){
            $threadId = $ostThread->id;
        }
        $status = 'Failed';
        $crmUser = self::getCrmUser($userLogin);
        $pid = 0;
        $recipients = null;
        $staffId = 0;
        $userName = null;
        $userId = 0;
        $flag = 0;
        if (isset($latestThreadEntry)) {
            $recipients = $latestThreadEntry->recipients;
            $pid = $latestThreadEntry->pid; 
            $flag = $latestThreadEntry->flags;
        }
        if (isset($crmUser)) {
            Log::info($crmUser->user_name);
            Log::info($crmUser->email_address);
            $staff = self::getStaffDetail($crmUser->user_name);
            $user = self::getUserDetail($crmUser->email_address);
            if (isset($staff)) {
                $staffId = $staff->staff_id;
                Log::info('Staff Name : ' .$staff->firstname);
            }
            if (isset($user)) {
                $userId = $user->id;
                $userName = $user->name;
                Log::info('User Name : '. $userName);
            }
        }

        if ($userName != '' && $threadId != '') {
            $query = DB::connection('mysql_helpdesk')
                    ->insert('insert into ost_thread_entry  
                    (pid,thread_id,staff_id,user_id,type,poster,body,recipients,flags,created,updated) 
                    values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', [
                $pid,
                $threadId,
                $staffId,
                $userId,
                'R',
                $userName,
                htmlentities($thread),
                $recipients,
                $flag,
                Carbon::now(),
                Carbon::now()
            ]);
            if ($query == 1) {
                $status = 'Success';
            }
        } 

        return $status;
    }

    public function getCrmUser($id) {
        $query = DB::table('users');
        $query->join('email_addr_bean_rel', 'email_addr_bean_rel.bean_id', '=', 'users.id');
        $query->join('email_addresses', 'email_addresses.id', '=', 'email_addr_bean_rel.email_address_id');
        $query->where('email_addr_bean_rel.bean_module', 'Users');
        $query->where('email_addr_bean_rel.bean_id', $id);
        $data = $query->first();
        return $data;
    }

    public function getStaffDetail($name) {
        $query = DB::connection('mysql_helpdesk')
                ->table('ost_staff')
                ->where('username', 'like', '%' . $name . '%')
                ->orderBy('created', 'desc')
                ->first();
        return $query;
    }

    public function getUserDetail($email) {
        return DB::connection('mysql_helpdesk')
                        ->table('ost_user')
                        ->join('ost_user_email', 'ost_user_email.user_id', '=', 'ost_user.id')
                        ->where('ost_user_email.address', $email)
                        ->select('ost_user.id','ost_user.name','ost_user_email.address')
                        ->orderBy('created', 'desc')
                        ->first();
    }

    public function updateThreadEntry($entryId, $updateBody) {
        $status = 'Failed';
        $query = DB::connection('mysql_helpdesk')
                ->table('ost_thread_entry')
                ->where('id', $entryId)
                ->update([
            'body' => $updateBody
        ]);

        if ($query == 1) {
            $status = 'Success';
        }

        return $status;
    }

    public function getEventPerEntry($entryId, $threadId) {
        $query = "SELECT en.id AS entry_id, en.staff_id, en.poster, en.body, en.created,
                    ev.id AS event_id, ev.staff_id as event_staff_id, ev.username, ev.data, ev.timestamp 
                    FROM ost_thread_entry en
                    left join ost_thread_event ev ON en.thread_id = ev.thread_id
                    WHERE en.thread_id = $threadId
                    AND en.id = $entryId
                    AND en.staff_id = ev.staff_id
                    ORDER BY en.created";
        return DB::connection('mysql_helpdesk')->select($query);
    }

    public function getTeamDetail($teamId) {
        return DB::connection('mysql_helpdesk')
                        ->table('ost_team')
                        ->where('team_id', $teamId) 
                        ->first();
    }

    public function getStatusDetail($statusId) {
        return DB::connection('mysql_helpdesk')
                        ->table('ost_ticket_status')
                        ->where('id', $statusId) 
                        ->first();
    }

    public function deleteEntry($ticketId, $threadId, $entryId, $staffId) {
        $status = 'Failed';

        try{
            $query = "DELETE ost_thread_entry, ost_thread_event FROM ost_thread_entry
                        LEFT JOIN ost_thread_event ON ost_thread_entry.thread_id = ost_thread_event.thread_id
                        WHERE ost_thread_entry.thread_id = $threadId 
                        AND ost_thread_entry.id = $entryId 
                        AND ost_thread_entry.staff_id = $staffId AND ost_thread_event.staff_id = $staffId";
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > ' .$query);
            $result = DB::connection('mysql_helpdesk')->select($query);
            $status = 'Success';
        }catch(Exception $ex) {
            Log::info(__CLASS__ .' > ' .__METHOD__ .' > ' .$ex->getTraceAsString());
        } 
        return $status;
    }

    public function deleteEvent($ticketId, $entry, $eventId, $threadId, $staffId) {
        $status = 'Failed';
        try{
            $query = "DELETE FROM ost_thread_event 
                        WHERE ost_thread_event.thread_id = $threadId 
                        AND ost_thread_event.staff_id = $staffId";
            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > ' .$query);
            $result = DB::connection('mysql_helpdesk')->select($query);
            $status = 'Success';
        }catch(Exception $ex) {
            Log::info(__CLASS__ .' > ' .__METHOD__ .' > ' .$ex->getTraceAsString());
        } 
        return $status;
    }
}
