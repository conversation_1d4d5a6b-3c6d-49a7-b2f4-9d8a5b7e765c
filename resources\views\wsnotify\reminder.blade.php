@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
@if($connectionType === 'ep')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/ep') }}">Dashboard</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/ep') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/ep') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@elseif($connectionType === 'crm')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/crm') }}">Dashboard</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/crm') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/crm') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@endif
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="block">
    @if (session('message'))
    <div class="alert alert-success">
        {{ session('message') }}
    </div>
    @endif
    <div class="table-responsive" style="height: 500px; overflow-y: auto;">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <h3>REMINDERS</h3>
        <table id="basic-datatable" class="table table-vcenter table-striped table-bordered">
            <thead style="position: sticky; top: 0; z-index: 1;">
                <tr>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Title</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Description</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Receiver Group</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Schedule</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Last Remind Time</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Next Remind Time</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Final Remind Time</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Status</th>
                    <th class="text-center" style="position: sticky; top: 0; background: #fff; z-index: 1;">Action</th>
                </tr>
            </thead>
            @if($reminders != null)
            <tbody>
                @foreach($reminders as $reminder)
                <tr>
                    <td class="text-left">{{ $reminder->title }}</td>
                    <td class="text-left">{{ $reminder->description }}</td>
                    <td class="text-left">
                        @php
                        $receiverGroups = ($reminder->receiver_group);
                        @endphp
                        @foreach($receiverGroups as $receiverGroup)
                        <span class="badge" style="background-color: green;">{{ $receiverGroup }}</span>
                        @endforeach
                    </td>
                    <td class="text-left">{{ $reminder->schedule }}</td>
                    <td class="text-left">{{ $reminder->latest_remind_time }}</td>
                    <td class="text-left">{{ $reminder->next_remind_time }}</td>
                    <td class="text-left">{{ $reminder->final_remind_time }}</td>
                    <td class="text-left">{{ $reminder->status }}</td>
                    <td class="text-center action_reminder">
                        <div class="btn-group btn-group-xs">
                            <button id="update_reminder" class="btn btn-yellow update_reminder" data-toggle="modal" data-reminder="{{ json_encode($reminder) }}">Update</button>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
            @endif
        </table>
    </div>
    <button id="add_reminder" class="btn btn-primary">Add Reminder</button>
</div>
<div class="modal fade" id="modal_reminder" tabindex="-1" role="dialog" aria-labelledby="modal_reminder_label" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
                <h4 class="modal-title" id="modal_reminder_label">Add Reminder</h4>
            </div>
            <div class="modal-body" style="max-height: calc(100vh - 210px); overflow-y: auto;">
                <form id="reminder_form" action="{{ url()->current() }}" method="post">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <label for="id">ID</label>
                        <input type="text" class="form-control" id="id" name="id" readonly>
                    </div>
                    <div class="form-group">
                        <label for="title">Title</label>
                        <input type="text" class="form-control" id="title" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea class="form-control" id="description" name="description" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="create_new_receiver">Create new receiver</label>
                        <input type="checkbox" id="create_new_receiver" name="create_new_receiver">
                    </div>
                    <div class="form-group" style="display: none;">
                        <label for="phone_number">Phone Number</label>
                        <div id="phone_number_inputs">
                            <div class="input-group mb-3">
                                <input type="text" class="form-control" name="phone_number[]" placeholder="60">
                                <div class="input-group-append">
                                    <button class="btn btn-danger remove-phone-number" type="button">Remove</button>
                                </div>
                            </div>
                        </div>
                        <button type="button" id="add_phone_number" class="btn btn-primary">Add more</button>
                    </div>
                    <div class="form-group">
                        <label for="receiver_group">Receiver Group</label>
                        <select class="form-control select2" id="receiver_group" name="receiver_group[]" multiple required>
                            @foreach ($receivers_group as $receiver)
                            <option value="{{ $receiver->group_name }}">{{ $receiver->group_name }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="detail_notification" style="font-family: Arial, sans-serif; color: #333;">
                            <span class="iconify" style="font-size: 24px; color: #007BFF;" data-icon="info" data-toggle="tooltip" data-trigger="click" data-html="true" data-template='<div class="tooltip" role="tooltip"><div class="arrow"></div><div class="tooltip-inner" style="max-width: 350px; width: auto;"></div></div>' title="
                                <div style='display: flex; flex-wrap: wrap; flex-direction: row; overflow-y: auto; max-height:95vh; font-family: Arial, sans-serif; color: #333; background-color: #f8f9fa; border-radius: 5px; padding: 10px;'>
                                    <div style='width: 100%; text-align: center;'>
                                        <b>Whatsapp Message Formatting</b><br><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Italic</b><br>To italicize your message, place an underscore on both sides of the text: _text_<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Bold</b><br>To bold your message, place an asterisk on both sides of the text: *text*<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Strikethrough</b><br>To strikethrough your message, place a tilde on both sides of the text: ~text~<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Monospace</b><br>To monospace your message, place three backticks on both sides of the text: ```text```<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Bulleted list</b><br>To add a bulleted list to your message, place an asterisk or hyphen and a space before each word or sentence:<br>* text<br>* text<br>Or<br>- text<br>- text<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Numbered list</b><br>To add a numbered list to your message, place a number, period, and space before each line of text:<br>1. text<br>2. text<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Quote</b><br>To add a quote to your message, place an angle bracket and space before the text: > text<br><hr style='border-top: 1px solid #dee2e6;'><br>
                                    </div>
                                    <div style='margin-right: 10px;'>
                                        <b>Inline code</b><br>To add inline code to your message, place a backtick on both sides of the message: `text`
                                    </div>
                                </div>
                                    " data-placement="left">&#9432;</span>Detail Notification</label>
                        <textarea class="form-control" id="detail_notification" name="detail_notification" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="remind_type">Remind Type</label>
                        <select class="form-control" id="remind_type" name="remind_type" required>
                            @foreach ($remindTypes as $type)
                            <option value="{{ $type }}">{{ $type }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="remind_interval">Remind Interval</label>
                        <select class="form-control" id="remind_interval" name="remind_interval">
                            @foreach ($remindIntervals as $interval)
                            <option value="{{ $interval }}">{{ $interval }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="schedule">Schedule</label>
                        <input type="text" class="form-control" id="schedule" name="schedule" readonly>
                    </div>
                    <div class="form-group">
                        <label for="custom_interval">Custom Interval (Minutes)</label>
                        <input type="text" class="form-control" id="custom_interval" name="custom_interval">
                    </div>
                    <div class="form-group">
                        <label for="remind_minute">Remind Minute</label>
                        <input type="number" class="form-control" id="remind_minute" name="remind_minute" readonly>
                    </div>
                    <div class="form-group">
                        <label for="remind_hour">Remind Hour</label>
                        <input type="number" class="form-control" id="remind_hour" name="remind_hour" readonly>
                    </div>
                    <div class="form-group">
                        <label for="remind_day">Remind Day</label>
                        <input class="form-control" id="remind_day" name="remind_day" readonly>
                    </div>
                    <div class="form-group">
                        <label for="remind_month">Remind Month</label>
                        <input class="form-control" id="remind_month" name="remind_month" readonly>
                    </div>
                    <div class="form-group">
                        <label for="next_remind_time">Next Remind Time</label>
                        <input type="datetime-local" class="form-control" id="next_remind_time" name="next_remind_time" required>
                    </div>
                    <div class="form-group">
                        <label for="final_remind_time">Final Remind Time</label>
                        <input type="datetime-local" class="form-control" id="final_remind_time" name="final_remind_time" readonly>
                    </div>
                    <div class="form-group">
                        <label for="expired_at">Expired At</label>
                        <input type="datetime-local" class="form-control" id="expired_at" name="expired_at">
                    </div>
                    <div class="form-group">
                        <label for="status">Status</label>
                        <select class="form-control" id="status" name="status">
                            @foreach ($statuses as $status)
                            <option value="{{ $status }}">{{ ucfirst($status) }}</option>
                            @endforeach
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                <button type="submit" class="btn btn-primary" form="reminder_form">Save changes</button>
            </div>
        </div>
    </div>
</div>
@endif
@endsection

@section('jsprivate')
<script>
    $(document).ready(function() {
        App.sidebar('toggle-sidebar');

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })

        $('#modal_reminder').on('hide.bs.modal', function() {
            $('[data-toggle="tooltip"]').tooltip('hide');
        });
    });
</script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2({
            width: '100%',
            templateSelection: function(data, container) {
                $(container).css("background-color", "green");
                $(container).css("color", "white");
                return data.text;
            }
        }).on('change', function() {
            $(this).valid();
        });
    });
</script>
<script>
    $(document).ready(function() {
        function handleFormFieldsVisibility() {
            let remindType = $('#remind_type').val();
            let remindInterval = $('#remind_interval').val();

            if (remindType == 'once') {
                $('#remind_interval').closest('.form-group').hide();
                $('#custom_interval').closest('.form-group').hide();
                $('#remind_interval').val('');
                $('#custom_interval').val('');
            } else {
                $('#remind_interval').closest('.form-group').show();

                if (remindInterval == 'custom') {
                    $('#custom_interval').closest('.form-group').show();
                } else {
                    $('#custom_interval').closest('.form-group').hide();
                    $('#custom_interval').val('');
                }
            }
        }

        $('#remind_type').change(handleFormFieldsVisibility);
        $('#remind_interval').change(handleFormFieldsVisibility);

        $('#add_phone_number').click(function() {
            $('#phone_number_inputs').append('<div class="input-group mb-3"><input type="text" class="form-control" name="phone_number[]" placeholder="60"><div class="input-group-append"><button class="btn btn-danger remove-phone-number" type="button">Remove</button></div></div>');
        });

        $(document).on('click', '.remove-phone-number', function() {
            $(this).closest('.input-group').remove();
        });

        $('#create_new_receiver').change(function() {
            if ($(this).is(":checked")) {
                $('#add_phone_number').closest('.form-group').show();
            } else {
                $('#add_phone_number').closest('.form-group').hide();
            }
        });

        $('#add_reminder, #update_reminder').click(function(event) {
            event.preventDefault();

            $('#remind_day').parent().hide();
            $('#remind_month').parent().hide();
            $('#remind_minute').parent().hide();
            $('#remind_hour').parent().hide();
            $('#add_phone_number').closest('.form-group').hide();
            $('#phone_number_inputs').empty();

            $('#reminder_form')[0].reset();

            if ($(this).hasClass('update_reminder')) {
                $('#id').closest('.form-group').show();

                let reminder = $(this).data('reminder');
                let receiverGroup = (reminder.receiver_group);

                $('#id').val(reminder.id);
                $('#status').val(reminder.status);
                $('#title').val(reminder.title);
                $('#description').val(reminder.description);
                $('#receiver_group').val(receiverGroup).change();
                $('#detail_notification').val(reminder.detail_notification);
                $('#remind_type').val(reminder.remind_type);
                $('#remind_interval').val(reminder.remind_interval);
                $('#custom_interval').val(reminder.custom_interval);
                $('#remind_day').val(reminder.remind_day);
                $('#remind_month').val(reminder.remind_month);
                $('#next_remind_time').val(reminder.next_remind_time);
                $('#final_remind_time').val(reminder.final_remind_time);
                $('#expired_at').val(reminder.expired_at);
                $('#schedule').val(reminder.schedule);

                $('#reminder_form').append('<input type="hidden" name="_method" value="PUT">');
                $('#modal_reminder_label').text('Update Reminder');
            } else {
                $('#id').closest('.form-group').hide();
                $('#receiver_group').change();
                $('#create_new_receiver').closest('.form-group').show();

                $('#reminder_form').attr('method', 'POST');
                $('input[name="_method"]').remove();
                $('#modal_reminder_label').text('Add Reminder');

            }
            handleFormFieldsVisibility();
            $('#modal_reminder').modal('show');
        });
    });
</script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script>
    $(document).ready(function() {
        function generateSchedule(nextRemindTime, remindType, remindInterval, customInterval) {
            let schedule = '';
            if (remindType == 'once') {
                schedule = 'Once at ' + nextRemindTime.format('MMMM D, YYYY, h:mm A');
            } else {
                switch (remindInterval) {
                    case 'daily':
                        schedule = 'Daily';
                        break;
                    case 'weekly':
                        schedule = 'Every ' + nextRemindTime.format('dddd');
                        break;
                    case 'monthly':
                        schedule = 'Every month on the ' + nextRemindTime.format('D');
                        break;
                    case 'yearly':
                        schedule = 'Every year on ' + nextRemindTime.format('MMMM D');
                        break;
                    case 'custom':
                        schedule = 'Every ' + customInterval + ' minutes';
                        break;
                }
                schedule += ' at ' + nextRemindTime.format('h:mm A');
            }
            return schedule;
        }

        function calculateFinalRemindTime(nextRemindTime, remindType, remindInterval, customInterval, expiredAt) {
            if (!nextRemindTime || !remindType || (remindType == 'periodic' && !remindInterval) || !expiredAt || !nextRemindTime.isValid() || !expiredAt.isValid()) {
                return;
            }

            let finalRemindTime = nextRemindTime;
            if (remindType == 'once') {
                return finalRemindTime;
            } else {
                while (true) {
                    let nextRemindTime;
                    switch (remindInterval) {
                        case 'daily':
                            nextRemindTime = finalRemindTime.clone().add(1, 'days');
                            break;
                        case 'weekly':
                            nextRemindTime = finalRemindTime.clone().add(1, 'weeks');
                            break;
                        case 'monthly':
                            nextRemindTime = finalRemindTime.clone().add(1, 'months');
                            break;
                        case 'yearly':
                            nextRemindTime = finalRemindTime.clone().add(1, 'years');
                            break;
                        case 'custom':
                            nextRemindTime = finalRemindTime.clone().add(customInterval, 'minutes');
                            break;
                    }
                    if (nextRemindTime.isAfter(expiredAt)) {
                        break;
                    } else {
                        finalRemindTime = nextRemindTime;
                    }
                }
            }
            return finalRemindTime;
        }

        $('#next_remind_time, #remind_type, #remind_interval, #custom_interval, #expired_at').change(function() {
            let nextRemindTime = moment($('#next_remind_time').val());
            let remindType = $('#remind_type').val();
            let remindInterval = $('#remind_interval').val();
            let customInterval = parseInt($('#custom_interval').val());
            let expiredAt = null;

            if (nextRemindTime.isValid()) {
                switch (remindType) {
                    case 'once':
                        $('#remind_day').val(nextRemindTime.format('D'));
                        $('#remind_month').val(nextRemindTime.format('MMMM'));
                        $('#remind_minute').val(nextRemindTime.format('mm'));
                        $('#remind_hour').val(nextRemindTime.format('HH'));
                        $('#expired_at').val(nextRemindTime.format('YYYY-MM-DDTHH:mm'));

                        break;
                    case 'periodic':
                        switch (remindInterval) {
                            case 'daily':
                                $('#remind_day').val('');
                                $('#remind_month').val('');
                                $('#remind_minute').val(nextRemindTime.format('mm'));
                                $('#remind_hour').val(nextRemindTime.format('HH'));
                                break;
                            case 'weekly':
                                $('#remind_day').val(nextRemindTime.format('dddd'));
                                $('#remind_month').val('');
                                $('#remind_minute').val(nextRemindTime.format('mm'));
                                $('#remind_hour').val(nextRemindTime.format('HH'));
                                break;
                            case 'monthly':
                                $('#remind_day').val(nextRemindTime.format('D'));
                                $('#remind_month').val('');
                                $('#remind_minute').val(nextRemindTime.format('mm'));
                                $('#remind_hour').val(nextRemindTime.format('HH'));
                                break;
                            case 'yearly':
                                $('#remind_day').val(nextRemindTime.format('D'));
                                $('#remind_month').val(nextRemindTime.format('MMMM'));
                                $('#remind_minute').val(nextRemindTime.format('mm'));
                                $('#remind_hour').val(nextRemindTime.format('HH'));
                                break;
                            case 'custom':
                                $('#remind_day').val('');
                                $('#remind_month').val('');
                                $('#remind_minute').val('');
                                $('#remind_hour').val('');
                                break;
                        }
                        break;
                }
                $('#schedule').val(generateSchedule(nextRemindTime, remindType, remindInterval, customInterval));

                expiredAt = moment($('#expired_at').val());
                $('#final_remind_time').val(calculateFinalRemindTime(nextRemindTime, remindType, remindInterval, customInterval, expiredAt).format('YYYY-MM-DDTHH:mm'));
            }
        });
    });
</script>
@endsection