@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <!-- END Search Form -->
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
        <div class="row">
            <div class="col-lg-12">
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Quotation Tender <strong>Monitoring (Valid before 12PM)</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                
            </div>
            
            
        </div>
        <div class="row">
            <div class="col-lg-12">
                <div class="widget">
                   
                    <div class="widget-extra label-danger ">
                        
                        <h5 class="widget-content-light">
                             [RALAT] Quotation Tender <strong>Status as Close Not Match With Closing Date</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring_closing_too_early" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                
            </div>
            
            
        </div>
    
        <div class="row">
            <div class="col-lg-6">
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Quotation Tender <strong>Published</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring_published" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                
            </div>
            <div class="col-lg-6">
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Quotation Tender <strong>Closing</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring_closing" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                
            </div>
            
            
            
        </div>
    
        
    
        <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="table-responsive">
                                    <table id="basic-datatable" class="table table-striped table-vcenter">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>

    @endif
    
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->

    <script>
        var APP_URL = {!! json_encode(url('/')) !!}
        
        App.datatables();
        /* Initialize Datatables */
        var tableListData =     $('#basic-datatable').DataTable({
                columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });
            
            
        $(document).ready(function () {
            
            
            $('.widget').on("click",'.modal-list-data-action', function(){
                
                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();
                
                $('#modal-list-data-header').text($(this).attr('data-title'));

                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();
                
                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();
                        
                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });
                        
                        $('.spinner-loading').hide();
                    }
                });
                
            });
            
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoring',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoringPublished',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring_published').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoringClosing',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring_closing').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoringClosingTooEarly',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring_closing_too_early').hide().html($data).fadeIn();
                }
            });
            

            
            
            
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection
