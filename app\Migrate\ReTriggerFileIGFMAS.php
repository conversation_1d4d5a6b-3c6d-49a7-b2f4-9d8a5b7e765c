<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\SSHService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\EpSupportTaskService;
use Guzzle;
use GuzzleHttp\Client;
use DB;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Program to re-trigger files form IGFMAS to re-process.
 */
class ReTriggerFileIGFMAS
{
    use OSBService;
    use SSHService;
    use FulfilmentService;
    use OSBWebService;
    use EpSupportTaskService;



    public static function runAll_INFOLDER()
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        /**
         * "-rw-rw-r-- 1 <USER> <GROUP> 760 Oct 21 15:03 1000APOVE4000012019102100430.GPG"
         * Grep from SSH, result display such as "-rw-rw-r-- 1 <USER> <GROUP> 760 Oct 21 15:03 1000APOVE4000012019102100430.GPG"
         * program will get filename by from last char. Lenght file name must start '-' 
         */
        $arrayTypeFile = array(
            'AP511,GFM-140,-30',
            'APOVE,GFM-370,-32',
            'GLPRG,GFM-230,-30',
            'GLPRJ,GFM-240,-30',
            'GLGLC,GFM-260,-30',
            'GLDNA,GFM-220,-30',
            'GLVOT,GFM-210,-30',
            'GLPTJ,GFM-280,-30',
            'APERR,GFM-090,-32',
        );
        $listFileProcessed = collect();
        foreach ($arrayTypeFile as $fileTypeObj) {
            $fileTypeArr = explode(",", $fileTypeObj);
            $fileType = $fileTypeArr[0];
            $serviceCode = $fileTypeArr[1];
            $lengthFile = $fileTypeArr[2];
            MigrateUtils::logDump('Trigger for : ' . $fileTypeObj);
            $listFileName = self::getListFileName($fileType, $lengthFile);
            MigrateUtils::logDump('  Total found files: ' . count($listFileName));
            $counter = 0;
            foreach ($listFileName as $fileName) {
                MigrateUtils::logDump(' Retrigger : ' . $fileName . ' , ServiceCode: ' . $serviceCode);
                self::runIN($fileName, $serviceCode);
                $counter++;

                //every 5 files.. Just pause a while
                if ($counter % 5 === 0) {
                    MigrateUtils::logDump('sleep 20 sec...');
                    sleep(20);
                }
            }
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function runAll_OUTFOLDER_EP()
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        /**
         * "-rw-rw-r-- 1 <USER> <GROUP> 760 Oct 21 15:03 1000APOVE4000012019102100430.GPG"
         * Grep from SSH, result display such as "-rw-rw-r-- 1 <USER> <GROUP> 760 Oct 21 15:03 1000APOVE4000012019102100430.GPG"
         * program will get filename by from last char. Lenght file name must start '-' 
         */
        $arrayTypeFile = array(
            'AR502,GFM-380,-30,1GFMAS',
            'APIVE,GFM-010,-32,1GFMAS',
            'AP516,GFM-350,-30,1GFMAS',

            'syarikat,EPS-001,-23,eGPA',
            'bidang,EPS-002,-21,eGPA',

            'syarikat,GPI-010,-22,MyGPIS',
            'barang,GPI-020,-20,MyGPIS',
            'kumpulan_ptj,GPI-030,-25,MyGPIS',
            'ptj,GPI-040,-17,MyGPIS',
            'bidang,GPI-050,-22,MyGPIS',
            'iklan,GPI-060,-19,MyGPIS',
            'sst,GPI-070,-17,MyGPIS',
            'kontrak,GPI-080,-21,MyGPIS',
            'pemenuhan,GPI-090,-23,MyGPIS',
            'perancangan_perolehan,GPI-010,-35,MyGPIS',
            'perancangan_perbelanjaan,GPI-011,-38,MyGPIS',
            'prestasi,GPI-012,-22,MyGPIS',
        );

        foreach ($arrayTypeFile as $fileTypeObj) {
            $fileTypeArr = explode(",", $fileTypeObj);
            $prefixFileName = $fileTypeArr[0];
            $serviceCode = $fileTypeArr[1];
            $lengthFile = $fileTypeArr[2];
            $integrationName = $fileTypeArr[3];
            MigrateUtils::logDump('Trigger for : ' . $fileTypeObj);
            $listFileName = self::getListFileNameOutFolderEp($prefixFileName, $lengthFile,$integrationName);
            MigrateUtils::logDump('  Total found files: ' . count($listFileName));

            $counter = 0;
            foreach ($listFileName as $fileName) {
                MigrateUtils::logDump(' Retrigger : ' . $fileName . ' , ServiceCode: ' . $serviceCode);
                self::runOUT($fileName, $serviceCode);
                $counter++;

                //every 20 files.. Just pause a while
                if ($counter % 20 === 0) {
                    MigrateUtils::logDump('sleep 20 sec...');
                    sleep(5);
                }
            }
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function runAll_ServerGFMAS_OUT_FOLDER()
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        
        /**
         * AP511
         * APOVE
         */
        $listAP511FileName = self::getListAP511FileNameOUT_1GFMAS_SERVER();
        MigrateUtils::logDump('total found ap511 : ' . count($listAP511FileName));
        $counter = 0;
        foreach ($listAP511FileName as $fileName) {
            MigrateUtils::logDump(' Retrigger : ' . $fileName);
            $serviceCode = 'GFM-140';
            self::run_ServerGFMAS_OUT_FOLDER($fileName, $serviceCode);
            $counter++;
            if ($counter % 5 === 0) {
                MigrateUtils::logDump('sleep 10 sec...');
                sleep(10);
            }
        }

        $listApoveFileName = self::getListAPOVEFileNameOUT_1GFMAS_SERVER();
        MigrateUtils::logDump('total found APOVE : ' . count($listApoveFileName));
        $counter = 0;
        foreach ($listApoveFileName as $fileName) {
            MigrateUtils::logDump(' Retrigger : ' . $fileName);
            $serviceCode = 'GFM-370';
            self::run_ServerGFMAS_OUT_FOLDER($fileName, $serviceCode);
            $counter++;
            if ($counter % 5 === 0) {
                MigrateUtils::logDump('sleep 10 sec...');
                sleep(10);
            }
        }
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * SFTP to server IGFMAS to get all list files in OUT folder
     */
    public static function run_ServerGFMAS_OUT_FOLDER($filename, $serviceCode)
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        //$serviceCode = 'GFM-140';
        MigrateUtils::logDump('run_ServerGFMAS_OUT_FOLDER : ' . $filename);
        $result = self::triggerProcessFileOSB_OUTGFMAS($filename, $serviceCode);
        MigrateUtils::logDump(json_encode($result));
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }


    /**
     * location : batch/1GFMAS/IN
     * @param type $filename
     * @param type $serviceCode
     */
    public static function runIN($filename, $serviceCode)
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        //$serviceCode = 'GFM-140';
        MigrateUtils::logDump(' runIN : ' . $filename);
        $result = self::triggerProcessFileOSB($filename, $serviceCode);
        MigrateUtils::logDump(json_encode($result));
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        return $result;
    }

    /**
     * location : batch/1GFMAS/OUT
     * @param type $filename
     * @param type $serviceCode
     */
    public static function runOUT($filename, $serviceCode)
    {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        //$serviceCode = 'GFM-140';
        MigrateUtils::logDump(' runOUT : ' . $filename);
        $result = self::triggerProcessFileOutEp($filename, $serviceCode);
        MigrateUtils::logDump(json_encode($result));
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    /**
     * purposely only use for files in batch/1GFMAS/IN 
     * @param string $fileName
     * @param string $serviceCode
     * @return collection
     * 
     */
    protected static function triggerProcessFileOSB($fileName, $serviceCode)
    {
        $thisClass = new ReTriggerFileIGFMAS;
        $result = collect([]);
        $result->put("file_name", $fileName);
        $result->put("service_code", $serviceCode);
        //1st check filename should be not exist in DI_INTERFACE_LOG
        $interfaceLog = $thisClass->getDetailDiInterfaceLog($fileName);
        if ($interfaceLog == null) {
            // 2nd check filename is exist in BatchFile Log 
            // Disable 24/1/2022 Acoi, checking OSB_BATCH_FILE, some file not inserting in OSB_BATCH_FILE
            // $objFile = $thisClass->getBatchFileLog($fileName);
            // if ($objFile != null) {
                $totalFound = $thisClass->countFiles1GFMASFolderIN($fileName);
                if ($totalFound > 0) {
                    $listTransId = $thisClass->getTransIDInterfaceLog($serviceCode);
                    if (count($listTransId) > 0) {
                        $transInterfaceLog = $listTransId->random();
                        $result->put("trans_id", $transInterfaceLog->trans_id);
                        //$xmlContents = $thisClass->callCurlWSCallBackGfmasIN($interfaceLog->trans_id, $serviceCode, $fileName); 
                        $xmlContents = $thisClass->callWSCallBackGfmasIN($transInterfaceLog->trans_id, $serviceCode, $fileName);

                        sleep(15);
                        $result->put("success", "trigger process file in 'batch>>1GFMAS>>IN' ");
                        $result->put("xml", $xmlContents);
                        $result->put('data',  $thisClass->getDetailDiInterfaceLog($fileName)); // Get response after success in di_interface_log
                    } else {
                        $result->put("error", "Not trans_id available to pickup in di_interface_log ");
                    }
                } else {
                    $result->put("error", "Not found in folder 'batch/1GFMAS/IN' ");
                }
            //} else {
            //    $result->put("error", "Not found in osb_batch_file");
            //}
        } else {
            
            if($serviceCode == 'GFM-140' || 	$serviceCode == 'GFM-370'){
                $totalFound = $thisClass->countFiles1GFMASFolderIN($fileName);
                if ($totalFound > 0) {
                    $listTransId = $thisClass->getTransIDInterfaceLog($serviceCode);
                    if (count($listTransId) > 0) {
                        $transInterfaceLog = $listTransId->random();
                        $result->put("trans_id", $transInterfaceLog->trans_id);
                        $xmlContents = $thisClass->callWSCallBackGfmasIN($transInterfaceLog->trans_id, $serviceCode, $fileName);

                        sleep(15);
                        $result->put("success", "trigger process file in 'batch>>1GFMAS>>IN' ");
                        $result->put("xml", $xmlContents);
                        $result->put('data',  $thisClass->getDetailDiInterfaceLog($fileName)); // Get response after success in di_interface_log
                    } else {
                        $result->put("error", "Not trans_id available to pickup in di_interface_log ");
                    }
                } else {
                    $result->put("error", "Not found in folder 'batch/1GFMAS/IN' ");
                }
            }else{
                $result->put("error", "Data already exist in di_interface_log");
            }
        }

        return $result;
    }

    /**
     * purposely only use for files in server 1GFMAS 
     * @param string $fileName
     * @param string $serviceCode
     * @return collection
     * 
     */
    protected static function triggerProcessFileOSB_OUTGFMAS($fileName, $serviceCode)
    {
        $thisClass = new ReTriggerFileIGFMAS;
        $result = collect([]);
        $result->put("file_name", $fileName);
        $result->put("service_code", $serviceCode);
        //1st check filename should be not exist in DI_INTERFACE_LOG
        $interfaceLog = $thisClass->getDetailDiInterfaceLog($fileName);
        if ($interfaceLog == null) {
            $listTransId = $thisClass->getTransIDInterfaceLog($serviceCode);
            if (count($listTransId) > 0) {
                $transInterfaceLog = $listTransId->random();

                $result->put("trans_id", $transInterfaceLog->trans_id);
                $xmlContents = $thisClass->callWSClearFilesOUTFolder($transInterfaceLog->trans_id, $serviceCode, $fileName);

                sleep(15);
                $result->put("success", "trigger process file in 'Server 1GFMAS>>OUT' ");
                $result->put("xml", $xmlContents);
                $result->put('data',  $thisClass->getDetailDiInterfaceLog($fileName)); // Get response after success in di_interface_log

            } else {
                $result->put("error", "Not trans_id available to pickup in di_interface_log ");
            }
        } else {
            $result->put("error", "Data already exist in di_interface_log");
        }
        return $result;
    }

    /**
     * purposely only use for files in batch/1GFMAS/OUT
     * @param string $fileName
     * @param string $serviceCode
     * @return collection
     * 
     */
    protected static function triggerProcessFileOutEp($fileName, $serviceCode)
    {
        $thisClass = new ReTriggerFileIGFMAS;
        $uuid  = \Ramsey\Uuid\Uuid::uuid4();
        $result = collect([]);
        $result->put("uuid", $uuid->toString());
        $result->put("file_name", $fileName);
        $result->put("service_code", $serviceCode);
        //files generated by scheduler eP. should be found in di_Interface_log
        //$interfaceLog = $thisClass->getDetailDiInterfaceLog($fileName);
        //if ($interfaceLog != null) {
            $xmlContents = $thisClass->callWSPickupFileOutFolderEP($uuid, $serviceCode, $fileName);
            sleep(3);
            $result->put("success", "trigger process file in 'Server 1GFMAS>>OUT' ");
            $result->put("xml", $xmlContents);
            //$result->put('data', $interfaceLog);
        //} else {
        //    $result->put("error", "file not found : " . $fileName);
        //}
        return $result;
    }


    protected static function getListFileName($fileType, $lengthFile)
    {
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'ls -lrt *' . $fileType . '*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function ($line) use (&$filesFound, $lengthFile) {
            $data = $line . PHP_EOL;
            $arrayData  = (explode("\n", $data));
            foreach ($arrayData  as $str) {
                $pos = strpos($str, '.GPG');
                if ($pos !== false) {
                    $filename = trim(substr($str, $lengthFile));
                    $checkSpaceExist = strpos($filename, ' ');
                    if ($checkSpaceExist !== false) {
                        $arrayFilename =  explode(' ', $filename);
                        $filename = $arrayFilename[1];
                    }
                    if (strlen($filename) > 28) {
                        array_push($filesFound, trim($filename));
                    }
                }
            }
        });
        return $filesFound;
    }

    public static function getListFileNameOutFolderEp($fileName, $lengthFile,$integrateName=null)
    {
        if($integrateName == null || $integrateName == '1GFMAS'){
            $integrateName = '1GFMAS';
            $fileType = 'GPG';
        }elseif($integrateName == 'eGPA'){
            $fileType = 'gpg';
        }elseif($integrateName == 'MyGPIS'){
            $fileType = 'gpg';
        }
        $commands  = [
            'cd /batch/'.$integrateName.'/OUT',
            'ls -1 *' . $fileName . '*.'.$fileType,
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function ($line) use (&$filesFound, $lengthFile,$fileType) {
            $data = $line . PHP_EOL;
            $arrayData  = (explode("\n", $data));
            foreach ($arrayData  as $str) {
                
                $pos = strpos($str, '.'.$fileType);
                if ($pos !== false && str_contains($str,'cannot access') == false) {
                    /*
                    $filename = trim(substr($str, $lengthFile));
                    $checkSpaceExist = strpos($filename, ' ');
                    if ($checkSpaceExist !== false) {
                        $arrayFilename =  explode(' ', $filename);
                        $filename = $arrayFilename[1];
                    }
                    */
                    if(strlen($str) >= $lengthFile) {
                        array_push($filesFound, trim($str));
                    }
                }
            }
        });
        return $filesFound;
    }


    protected static function getListAP511FileNameOUT_1GFMAS_SERVER() {
        $data = array();
        // SIT sftp -oPort=2022 eperolehan@10.23.22.15:OUT 
        // Prod sftp -oPort=2022 eperolehan@10.38.206.73:OUT 

        $IGFMAS_SERVER =  env('SSH_IGFMAS_SERVER', '10.23.22.15');
        $IGFMAS_SERVER_USERNAME =  env('SSH_IGFMAS_SERVER_USERNAME', 'eperolehan@10');

        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 $IGFMAS_SERVER_USERNAME@$IGFMAS_SERVER:OUT",
            "exit",
        ];
        SSH::into('osb')->run($commands, function ($line) use (&$data) {
            $result = $line . PHP_EOL;
            $arrayData  = (explode("\n", $result));
            foreach ($arrayData  as $str) {
                $pos = strpos($str, 'AP511');
                if ($pos !== false) {
                    $filename = trim(substr($str, -33));
                    $checkSpaceExist = strpos($filename, ' ');
                    if ($checkSpaceExist !== false) {
                        $arrayFilename =  explode(' ', $filename);
                        $filename = $arrayFilename[1];
                    }
                    array_push($data, trim($filename));
                }
            }
        });
        return $data;
    }

    protected static function getListAPOVEFileNameOUT_1GFMAS_SERVER() {
        $data = array();
        // SIT sftp -oPort=2022 eperolehan@10.23.22.15:OUT 
        // Prod sftp -oPort=2022 eperolehan@10.38.206.73:OUT 

        $IGFMAS_SERVER =  env('SSH_IGFMAS_SERVER', '10.23.22.15');
        $IGFMAS_SERVER_USERNAME =  env('SSH_IGFMAS_SERVER_USERNAME', 'eperolehan@10');

        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 $IGFMAS_SERVER_USERNAME@$IGFMAS_SERVER:OUT",
            "exit",
        ];
        SSH::into('osb')->run($commands, function ($line) use (&$data) {
            $result = $line . PHP_EOL;
            $arrayData  = (explode("\n", $result));
            foreach ($arrayData  as $str) {
                $pos = strpos($str, 'APOVE');
                if ($pos !== false) {
                    $filename = trim(substr($str, -32));
                    $checkSpaceExist = strpos($filename, ' ');
                    if ($checkSpaceExist !== false) {
                        $arrayFilename =  explode(' ', $filename);
                        $filename = $arrayFilename[1];
                    }
                    array_push($data, trim($filename));
                }
            }
        });
        return $data;
    }

    /**Search filename. kindly remove .GPG first */
    public static function getListFileNameIGFMASArchivedIN($searchFileName, $lengthFile)
    {
        $commands  = [
            'cd /batch/1GFMAS/ARCHIVE/IN',
            'ls -lrt *' . $searchFileName . '*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function ($line) use (&$filesFound, $lengthFile) {
            $data = $line . PHP_EOL;
            $arrayData  = (explode("\n", $data));
            foreach ($arrayData  as $str) {
                $pos = strpos($str, '.GPG');
                if ($pos !== false) {
                    $filename = trim(substr($str, $lengthFile));
                    $checkSpaceExist = strpos($filename, ' ');
                    if ($checkSpaceExist !== false) {
                        $arrayFilename =  explode(' ', $filename);
                        $filename = $arrayFilename[1];
                    }
                    if (strlen($filename) > 28) {
                        array_push($filesFound, trim($filename));
                    }
                }
            }
        });
        return $filesFound;
    }

    public static function createDiInterfaceLogRecordByProcessId($serviceCode, $processId, $totalCreate)
    {
        // select count(*) from DI_INTERFACE_LOG where process_id = 'AP511' and file_name is null;
        $count = DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
            ->where('process_id', $processId)
            ->whereNull('file_name')
            ->count();
        MigrateUtils::logDump(self::class . ' > ' . __FUNCTION__ . ' > total check : ' . $count);
        if ($count < 100) {
            MigrateUtils::logDump(self::class . ' > ' . __FUNCTION__ . ' > will insert another  : ' . $totalCreate . ' records');
            try {
                //Sample Date FORMAT must be:  2019-01-28
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");
                //http://192.168.62.132:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

                $token = base64_encode(strtotime("now"));
                $url = $urlMiddleware . "/batch/diinterface/log/create";

                $options = [
                    'json' => [
                        'token' => $token,
                        'counter' => $totalCreate,
                        'serviceCode' => $serviceCode,
                        'processId' => $processId
                    ]
                ];

                $response = Guzzle::post($url, $options);

                $resultResp = json_decode($response->getBody(), true);


                return $resultResp;
            } catch (GuzzleException $ge) {
                MigrateUtils::logErrorDump(self::class . ' > ' . __FUNCTION__ . ' > ' . $ge->getMessage());
                MigrateUtils::logErrorDump(self::class . ' > ' . __FUNCTION__ . ' > ' . $ge->getTraceAsString());
            } catch (\GuzzleHttp\Exception\ConnectException $ex) {
                MigrateUtils::logErrorDump(self::class . ' > ' . __FUNCTION__ . ' > ' . $ex->getMessage());
                MigrateUtils::logErrorDump(self::class . ' > ' . __FUNCTION__ . ' > ' . $ex->getTraceAsString());
            }
        }
    }
}
