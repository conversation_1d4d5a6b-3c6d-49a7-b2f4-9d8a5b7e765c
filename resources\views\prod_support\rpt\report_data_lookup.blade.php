@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/prod-support/rpt/summary') }}"><i class="fa fa-archive"></i><PERSON><PERSON><PERSON></a>
                </li>
                <li class="active">
                    <a href="{{ url('/prod-support/rpt/data_lookup') }}"><i class="fa fa-tasks"></i>Data Lookup</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> DATA LOOKUP </strong></h1> 
        </div>   
        <div class="form-group">
            <button type="button" class="btn btn-sm btn-primary add-new-lookup"><i class="fa fa-save"></i> Add New Lookup</button>
        </div>
        <form class="form-horizontal form-bordered insert-lookup-form" id="insert-lookup-form" style="display:none" action="{{url('/prod-support/rpt/data_lookup')}}" method="post">
            {{ csrf_field() }}
            <div class="form-group panel-heading add-new-port">
                <input type="hidden" id="id" name="id" value="" class="form-control" style="width: 100px;">
                <label class="col-md-1 text-left" for="name">Name<span class="text-danger">*</span></label>
                <div class="col-md-2">
                    <input type="text" id="name" name="name" value="" required class="form-control" style="width: 700px;">
                </div>
                <label class="col-md-1 text-left" for="description">Description<span class="text-danger">*</span></label>
                <div class="col-md-2">
                    <input type="text" id="description" name="description" value="" required class="form-control" style="width: 700px;">
                </div>
                <label class="col-md-1 text-left" for="grouptype">Category<span class="text-danger">*</span></label>
                <div class="col-md-3 type">
                    <select id="grouptype" name = "grouptype" class="form-control" style="width: 700px;">
                        <option value="">Please Select</option>
                        <option value="Tindakan">Tindakan</option>
                        <option value="Status">Status</option>
                        <option value="Pengguna">Pengguna</option>
                    </select>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="pull-right">
                    <button type = "submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i> Save</button>
                    <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table id="datalookup-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">NO</th>
                        <th class="text-center">NAME</th>
                        <th class="text-center">DESCRIPTION</th>
                        <th class="text-center">CATEGORY</th>
                        <th class="text-center">CREATED BY</th>
                        <th class="text-center">CREATED DATE</th>
                        <th class="text-center">CHANGED BY</th>
                        <th class="text-center">CHANGED DATE</th>
                        <th class="text-center">ACTION</th>
                    </tr>                
                </thead>    
                <tbody>
                    @if($getLookupdate != null)
                    @foreach($getLookupdate as $rowData => $data)
                    <tr>
                        <td class="text-center">{{ ++$rowData }}</td>
                        <td class="text-center">{{ $data->name }}</td>
                        <td class="text-center">{{ $data->description }}</td>
                        <td class="text-center">{{ $data->category }}</td>
                        <td class="text-center">{{ $data->created_by }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                        <td class="text-center">{{ $data->changed_by }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                        <td class="text-center" action_table_task>
                            <div class="btn-group btn-group-xs">
                                <a idno ="{{$data->data_id}}" 
                                   name ="{{ $data->name }}"
                                   desc ="{{ $data->description }}"
                                   category ="{{ $data->category }}"
                                   data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                                <a idnom ="{{$data->data_id}}" 
                                   data-toggle="tooltip" title="Delete" class="btn btn-sm btn-danger delete_data"><i class="fa fa-times"></i></a>
                            </div>
                        </td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>

            <input type="hidden" id="delid" name="delid" value="" class="form-control" style="width: 100px;">
            <div id="modal_confirm_delete_data" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Delete Data Lookup? </label> &nbsp;&nbsp;&nbsp;
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DIV>
</div>
@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    $('#to-top').click();
        TablesDatatables.init();
    });
    App.datatables();
    
    $(".add-new-lookup").on("click", function () {
        $("#insert-lookup-form").show();
        $("#id").val("");
        $("#name").val("");
        $("#description").val("");
        $("#grouptype").val("");
    });
    
    $(".resetbutton").on("click", function () {
        $("#editid").val("");
        $("#editname").val("");
        $("#editdescription").val("");
        $("#editgrouptype").val("");
        $("#insert-lookup-form").hide();
    });
    
    $(".editbutton").on("click", function () {
        $('#to-top').click();
        $("#insert-lookup-form").show();
        let 
        id = $(this).attr('idno');
        let
        name = $(this).attr('name');
        let
        desc = $(this).attr('desc');
        let
        cateogry = $(this).attr('category');
        $('#id').val(id);
        $("#name").val(name);
        $("#description").val(desc);
        $("#grouptype").val(cateogry);
    });
    
    $(".delete_data").on("click", function () {
        $("#modal_confirm_delete_data").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
    });

    $('#submit_confirm_delete').on('click', function () {
        $('#modal_confirm_delete_data').modal('hide');
        $Id = $("#delid").val();
        $.ajax({
            type: "GET",
            url: "/prod-support/rpt/delete-data-lookup/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });
</script>
@endsection