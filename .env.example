APP_NAME="eP Support System"
APP_ENV=local
APP_KEY=base64:oNHmbWHyZ+6DXrrkDgtzhaQShYUVYOAPhscuTG+w0kA=
APP_DEBUG=true
APP_LOG_LEVEL=debug
APP_URL=http://localhost

DATA_PERIOD_BY_MINUTE=5
 
#JAVA_MIDDLEWARE_RESTFUL = http://**************:8080/ep-support-middleware
JAVA_MIDDLEWARE_RESTFUL = http://localhost:8080

#SPKI_MIDDLEWARE_RESTFUL = http://localhost:8385
SPKI_MIDDLEWARE_RESTFUL = http://**************:8080/ep-spki-middleware

CRM_INTEGRATION="localhost:8080"
CRM_INTEGRATION_API_KEY=6c7c9ecc-362f-4d77-bdbc-9218e28d8712

CRM_EP_HOSTNAME=http://localhost
CRM_EP_VRFKEY_DeleteNoteFiles=6A2Mq3bh~pbrU&P7Eu,*~IMayB/7IG

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=cdccrm
DB_USERNAME=root
DB_PASSWORD=password

DB_MYSQL_EP_SUPPORT_HOST=127.0.0.1
DB_MYSQL_EP_SUPPORT_PORT=3306
DB_MYSQL_EP_SUPPORT_DATABASE=ep_support
DB_MYSQL_EP_SUPPORT_USERNAME=root
DB_MYSQL_EP_SUPPORT_PASSWORD=password

### pointing prod - grant user (please do not use in workspace local station
#DB_MYSQL_EP_SUPPORT_HOST=***************
#DB_MYSQL_EP_SUPPORT_PORT=3306
#DB_MYSQL_EP_SUPPORT_DATABASE=ep_support
#DB_MYSQL_EP_SUPPORT_USERNAME=crm_user
#DB_MYSQL_EP_SUPPORT_PASSWORD=cDccRm@2017

### pointing prod - readonly user
#DB_HOST=***************
#DB_PORT=3306
#DB_DATABASE=cdccrm
#DB_USERNAME=suppcrm
#DB_PASSWORD=Suppcrm@2017

### pointing prod - grant user (please do not use in workspace local station
#DB_HOST=***************
#DB_PORT=3306
#DB_DATABASE=cdccrm
#DB_USERNAME=crm_user
#DB_PASSWORD=cDccRm@2017


#### THIS CONNECTION TO NEXTGEN  DB ####
#DB_NEXTGEN_RPT_HOST=racprd-cluster-scan.eperolehan.com.my
#DB_NEXTGEN_RPT_PORT=1521
#DB_NEXTGEN_RPT_DATABASE=ngepprd
#DB_NEXTGEN_RPT_USERNAME=NGEP_READ
#DB_NEXTGEN_RPT_PASSWORD=ng3p_r34d

DB_NEXTGEN_RPT_HOST=racrpt-cluster-scan.eperolehan.com.my
DB_NEXTGEN_RPT_PORT=1521
DB_NEXTGEN_RPT_DATABASE=ngeprpt
DB_NEXTGEN_RPT_USERNAME=NGEP_READ
DB_NEXTGEN_RPT_PASSWORD=ng3p_r34d

#DB_NEXTGEN_RPT_HOST=*************
#DB_NEXTGEN_RPT_PORT=1521
#DB_NEXTGEN_RPT_DATABASE=sitapp
#DB_NEXTGEN_RPT_USERNAME=ngep_sit
#DB_NEXTGEN_RPT_PASSWORD=ng3p_51t

DB_NEXTGEN_HOST=racrpt-cluster-scan.eperolehan.com.my
DB_NEXTGEN_PORT=1521
DB_NEXTGEN_DATABASE=ngeprpt
DB_NEXTGEN_USERNAME=ngep_crm
DB_NEXTGEN_PASSWORD=ng3p_crm54321

#### THIS CONNECTION TO NEXTGEN  DB (GRANT PRIVILEGES) ####
DB_NEXTGEN_FULLGRANT_HOST=rac-cluster-scan.eperolehan.com.my
DB_NEXTGEN_FULLGRANT_PORT=1521
DB_NEXTGEN_FULLGRANT_DATABASE=uatapp
DB_NEXTGEN_FULLGRANT_USERNAME=ngep_pds
DB_NEXTGEN_FULLGRANT_PASSWORD=ng3p_pd5


#### THIS CONNECTION TO NEXTGEN  BPM DB ####
DB_BPM_RPT_HOST=rac-cluster-scan.eperolehan.com.my
DB_BPM_RPT_PORT=1521
DB_BPM_RPT_DATABASE=uatsoa
DB_BPM_RPT_USERNAME=pdssoa_soainfra
DB_BPM_RPT_PASSWORD=pd5504

#### CONNECTION SSH OSB ####
##sit  stgbpmsoa01 - *************
SSH_OSB_HOST = *************
##production
#SSH_OSB_HOST = *************
SSH_OSB_USERNAME = support
SSH_OSB_PASSWORD = 5uPP0rt!@#

#### CONNECTION SSH EP PORTAL ####
##sit  stgportal01 - *************
SSH_EPPORTAL_HOST = *************
##Production
#SSH_EPPORTAL_HOST = *************
SSH_EPPORTAL_USERNAME = support
SSH_EPPORTAL_PASSWORD = 5uPP0rt!@#

BROADCAST_DRIVER=log
CACHE_DRIVER=file
SESSION_DRIVER=file
QUEUE_DRIVER=sync

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_DRIVER=smtp
MAIL_HOST=**************
MAIL_PORT=25
MAIL_USERNAME=mailuser
MAIL_PASSWORD=cdc2mail2014
MAIL_ENCRYPTION=null


#MAIL_HOST=smtp.office365.com
#MAIL_PORT=587
#MAIL_USERNAME=<EMAIL>
#MAIL_PASSWORD=Cdc@1234
#MAIL_ENCRYPTION=tls


PUSHER_APP_ID=
PUSHER_KEY=
PUSHER_SECRET=

KNOWAGE_RESOURCES_API=http://**************:5000