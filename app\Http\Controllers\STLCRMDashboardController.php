<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 * <AUTHOR>
 */

namespace App\Http\Controllers;

use App\Services\CRMService;
use App\Services\PomService;
use App\Services\CmsService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use DB;
use Log;
use App\EpSupportActionLog;

class STLCRMDashboardController extends Controller {

    public static function crmService() {
        return new CRMService;
    }

    public static function pomService() {
        return new PomService;
    }

    public static function cmsService() {
        return new CmsService;
    }

    public function __construct() {
        $this->middleware('auth');
    }

    public function main(Request $request) {

        $dateStart = null;
        $dateEnd = null;

        if (request()->isMethod('post')) {
            $dateStart = $request->dateStart;
            $dateEnd = $request->dateEnd;
        }
        $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
        $yesterday = Carbon::yesterday()->format('Y-m-d');
        $today = Carbon::now()->format('Y-m-d');

        $dateQueryStart = $startOfMonth;
        $dateQueryEnd = $yesterday;

        if ($dateStart !== null && $dateEnd !== null) {
            $dateQueryStart = Carbon::parse($dateStart)->format('Y-m-d');
            $dateQueryEnd = Carbon::parse($dateEnd)->format('Y-m-d');
        } else {
            if ($startOfMonth === $today) {
                $dateQueryStart = $today;
                $dateQueryEnd = $today;
            }
        }

        $totalCS = self::crmService()->getSlaCs('', $dateQueryStart, $dateQueryEnd);
        $totalCsActual = self::crmService()->getSlaCs('actual', $dateQueryStart, $dateQueryEnd);
//        $totalCsPom = self::pomService()->getSlaCsPoms();
        $totalCsCms = self::cmsService()->getSlaCsCms($dateQueryStart, $dateQueryEnd);

        $totalItCoord = self::crmService()->getSlaItCoord('', $dateQueryStart, $dateQueryEnd);
        $totalItCoordActual = self::crmService()->getSlaItCoord('actual', $dateQueryStart, $dateQueryEnd);
//        $totalItCoordPom = self::pomService()->getSlaItCoordPoms();
        $totalItCoordCms = self::cmsService()->getSlaItCoordCms($dateQueryStart, $dateQueryEnd);

        $totalItSpec = self::crmService()->getSlaItSpec('', $dateQueryStart, $dateQueryEnd);
        $totalItSpecActual = self::crmService()->getSlaItSpec('actual', $dateQueryStart, $dateQueryEnd);
//        $totalItSpecPom = self::pomService()->getSlaItSpecPoms();
        $totalItSpecCms = self::cmsService()->getSlaItSpecCms($dateQueryStart, $dateQueryEnd);

        $totalSeverity = self::crmService()->getSlaSeverity('', $dateQueryStart, $dateQueryEnd);
        $totalSeverityActual = self::crmService()->getSlaSeverity('actual', $dateQueryStart, $dateQueryEnd);
//        $totalSeverityPom = self::pomService()->getSlaSeverityPoms();
        $totalSeverityCms = self::cmsService()->getSlaSeverityCms($dateQueryStart, $dateQueryEnd);

        $totalS4 = self::crmService()->getSlaS4('', $dateQueryStart, $dateQueryEnd);
        $totalS4Actual = self::crmService()->getSlaS4('actual', $dateQueryStart, $dateQueryEnd);
//        $totalS4Pom = self::pomService()->getSlaS4Poms();
        $totalS4Cms = self::cmsService()->getSlaS4Cms($dateQueryStart, $dateQueryEnd);

        $array = '[{"name":"CS","total":"' . count($totalCS) . '","bugs":"' . count($totalCsActual) . '","poms":"' . $totalCsCms->total . '"},'
                . '{"name":"IT Coordinator","total":"' . count($totalItCoord) . '","bugs":"' . count($totalItCoordActual) . '","poms":"' . $totalItCoordCms->total . '"},'
                . '{"name":"IT Specialist (Support)","total":"' . count($totalItSpec) . '","bugs":"' . count($totalItSpecActual) . '","poms":"' . $totalItSpecCms->total . '"},'
                . '{"name":"IT Specialist (Severity)","total":"' . count($totalSeverity) . '","bugs":"' . count($totalSeverityActual) . '","poms":"' . $totalSeverityCms->total . '"},'
                . '{"name":"Approver","total":"' . count($totalS4) . '","bugs":"' . count($totalS4Actual) . '","poms":"' . $totalS4Cms->total . '"}]';

        return view('crm.main', [
            'data' => json_decode($array),
            'dateStart' => $dateQueryStart,
            'dateEnd' => $dateQueryEnd,
            'CS' => $totalCsActual,
            'ITCoord' => $totalItCoordActual,
            'ITSpec' => $totalItSpecActual,
            'ITSeverity' => $totalSeverityActual,
            'S4' => $totalS4Actual
        ]);
    }

    public function update(Request $request) {

        if ($request->isMethod("POST")) {

            $slaType = $request->slaType;
            switch ($slaType) {
                case "CS":
                    self::checkCS($request);
                    break;

                default:
                    break;
            }
        }
    }

    public function checkCS($request) {

        session()->flashInput(request()->input());

        $caseNumber = $request->caseNumber;
        $requestType = $request->requestType;
        $createdDate = $request->createdDate;
        $modifiedDate = $request->modifiedDate;
        $contactMode = $request->contactMode;

        if ($requestType === 'enquiry') {
            if ($createdDate === $modifiedDate) {
                if ($contactMode === 'Call-in') {
                    $slaDue = Carbon::parse($createdDate)->addMinute(15);
                    $query = DB::connection('mysql_crm')->table('cases')->where('case_number', $caseNumber)
                            ->update([
                        'cntc_mode_sla_startdate' => $createdDate,
                        'cntc_mode_sla_duedate' => $slaDue,
                        'cntc_mode_sla_startdate' => $createdDate,
                        'cntc_mode_executiondate' => $createdDate,
                    ]);

                    return $query;
                }
            }
        }
    }

    public function duplicateTask(Request $request) {

        $dateStart = Carbon::yesterday()->startOfMonth()->format('Y-m-d');
        $dateEnd = Carbon::now()->format('Y-m-d');

        if (request()->isMethod('post')) {
            $dateStart = $request->dateStart;
            $dateEnd = $request->dateEnd;
        }

        $result = self::crmService()->getDuplicateTask($dateStart, $dateEnd);

        return view('crm.duplicate_task', [
            'dateStart' => $dateStart,
            'dateEnd' => $dateEnd,
            'result' => $result
        ]);
    }

    public function taskDetail(Request $request) {
        $result = self::crmService()->getDetailCaseAndTaskAllCRM($request->caseNumber);

        return response()->json($result);
    }

    public function updateTaskDetail(Request $request) {
        session()->flashInput(request()->input());
        if (request()->isMethod('post')) {
            $taskId = $request->taskId;
            $before = DB::table('tasks')->join('tasks_cstm', 'tasks_cstm.id_c', 'tasks.id')
                    ->where('tasks.id', '=', $taskId)
                    ->get();
            $result = self::crmService()->updateTaskDeleted($taskId);
            $after = DB::table('tasks')->join('tasks_cstm', 'tasks_cstm.id_c', 'tasks.id')
                    ->where('tasks.parent_id', '=', $taskId)
                    ->get();
            EpSupportActionLog::saveActionLog('delete-task', 'CRM', $before, $after, "Success");

            if (isset($result)) {
                return $result;
            }
        }
    }

    public function caseSlaDetail(Request $req) {

        session()->flashInput(request()->input());

        $result = null;
        $listTaskStatus = self::crmService()->getDetailLookupCRMByType('cdc_task_status_list');
        $listCaseStatus = self::crmService()->getDetailLookupCRMByType('case_status_dom');
        if ($req->isMethod('post')) {

            $caseNumber = $req->caseNumber;
            $result = self::crmService()->getCaseTaskSlaDetail($caseNumber); 
        }
        return view('crm.sla_detail', [
            'result' => $result,
            'listTaskStatus' => $listTaskStatus,
            'listCaseStatus' => $listCaseStatus
        ]);
    }

    public function caseSlaUpdate(Request $req) {
        session()->flashInput(request()->input());
        if ($req->isMethod('post')) {
            $taskId = $req->taskId;
            $taskStatus = $req->newStatus;
            $taskDeleted = $req->newDelete;
            $caseId = $req->caseId;
            $caseStatus = $req->newCaseStatus;

            $actionStatus = 'Failed';
            if ($taskId != '') {
                //update task status
                if ($taskStatus != '') {
                    $query = DB::connection('mysql_crm')
                            ->table('tasks')->where('id', $taskId)
                            ->update([
                        'status' => $taskStatus
                    ]);
                    if ($query == 1) {
                        $actionStatus = 'Success';
                    }
                }
                //update task deleted
                if ($taskDeleted != '') {
                    $queryDel = DB::connection('mysql_crm')
                            ->table('tasks')->where('id', $taskId)
                            ->update([
                        'deleted' => $taskDeleted
                    ]);
                    if ($queryDel == 1) {
                        $actionStatus = 'Success';
                    }
                }
            }
            //update case status
            if ($caseId != '' && $caseStatus != '') { 
                $querycs = DB::connection('mysql_crm')
                        ->table('cases')->where('id', $caseId)
                        ->update([
                    'status' => $caseStatus
                ]);
                if ($querycs == 1) {
                    $actionStatus = 'Success';
                }
            }

            $data = collect([]);
            $data->put("status", $actionStatus);

            return $data;
        }
    }

    public function caseSlaUpdateResolution(Request $req){
        session()->flashInput(request()->input());
        if ($req->isMethod('post')) {
            $module = $req->module;
            $id = $req->id;
            $resolution = $req->newResolution; 

            $actionStatus = 'Failed';
            if ($id != '') {
                //update cases resolution
                if ($module == 'Cases') {
                    $query = DB::connection('mysql_crm')
                            ->table('cases')->where('id', $id)
                            ->update([
                        'resolution' => $resolution
                    ]);
                    if ($query == 1) {
                        $actionStatus = 'Success';
                    }
                }else if ($module == 'Tasks') {
                    $query = DB::connection('mysql_crm')
                            ->table('tasks_cstm')->where('id_c', $id)
                            ->update([
                        'resolution_c' => $resolution
                    ]);
                    Log::info($id);
                    Log::info($resolution);
                    Log::info($query);
                    if ($query == 1) {
                        $actionStatus = 'Success';
                    }
                }else{
                    $actionStatus = 'Failed';
                }
                
            } 

            $data = collect([]);
            $data->put("status", $actionStatus);

            return $data;
        }
    }

}
