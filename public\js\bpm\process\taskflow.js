/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */

//click on task flow
$('.workflowProcess').click(function () {

    $('tr').removeClass('hover');
    $(this).parent().addClass('hover');
    document.getElementById("failed_ul").style.display = "none";
    var id = this.id;
    var processId = $(this).attr('data-id');
    var status = $(this).attr('data-status');
    var cmpstId = $(this).attr('data-cmpstId');
    
    var csrf = $("input[name=_token]").val();
    var suspendBtnTask = document.getElementById("suspend_task");
    var resumeBtnTask = document.getElementById("resume_task");
    var withdrawBtnTask = document.getElementById("withdraw_task");
    var reassignBtnTask = document.getElementById("reassign_task");
    var executeBtnTask = document.getElementById("execute_task");
    var loadBtnTask = document.getElementById("load_task");
    var actionBtnTask = document.getElementById("action_task");
    var historyBtnTask = document.getElementById("history_task");

    document.getElementById("task-flow").style.display = "none";
    document.getElementById("bpmn-flow").style.display = "none";
    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/process/workflow/" + processId + "/" + status,
        data: {"_token": csrf, "process_id": processId, "status": status},
        error: function (xhr, status, error) {
            console.log('workflowprocess');
            document.getElementById("failed_ul").style.display = "block";
            console.log('url : /bpm/process/workflow/ ' + xhr.status + ' statusText ' + xhr.statusText);
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.statusAPI === null) {
            $('#failed').hide();
            //Allow to suspend, withdraw and reassign when status Assigned (1)  or Alerted (0)
            if (resp.status == 1 || resp.status == 0) {
                suspendBtnTask.disabled = false;
                resumeBtnTask.disabled = true;
                withdrawBtnTask.disabled = false;
                reassignBtnTask.disabled = false;
                executeBtnTask.disabled = false;
                actionBtnTask.disabled = false;

                //allow to resume when status Suspended (9)
            } else if (resp.status == 9) {
                suspendBtnTask.disabled = true;
                resumeBtnTask.disabled = false;
                withdrawBtnTask.disabled = true;
                reassignBtnTask.disabled = true;
                executeBtnTask.disabled = true;
                actionBtnTask.disabled = true;

            } else {
                suspendBtnTask.disabled = true;
                resumeBtnTask.disabled = true;
                withdrawBtnTask.disabled = true;
                reassignBtnTask.disabled = true;
                executeBtnTask.disabled = true;
                actionBtnTask.disabled = true;
            }

            var taskid = resp.listdata["taskId"];

            populateTable(taskid, resp.listdata, resp.payload, resp.statusListTask, resp.listaction, resp.history, resp.statusHistory, status, csrf, cmpstId);

        } else {
            document.getElementById("task-flow").style.display = "none";
            document.getElementById("failed_ul").style.display = "block";
            console.log('statusApi else than null ' + resp.statusAPI);
            $("#failed").html(resp.statusAPI);
        }

        $('#group_task').empty();
        var groupTask = document.getElementById('group_task');

        if (resp.statusTaskGroup == 'success') {
            if (resp.taskGroup.length > 1) {
                document.getElementById("group_task").style.display = "block";
                $('#statusApi').hide();
                for (const [listdata, value] of Object.entries(resp.taskGroup)) {
                    groupTask.add(new Option(value["key"], value["value"]));
                }

                //click on group task
                $("#group_task").change(function () {
                    var task_id = $(this).val();
                    console.log('group task id ' + task_id);
                    document.getElementById("task-flow").style.display = "none";
                    $('#modal_spinner').modal('show');
                    $.ajax({
                        type: "POST",
                        url: "/bpm/process/grouptask/" + task_id,
                        data: {"_token": csrf, "task_id": task_id},
                        error: function (xhr, status, error) {
                            document.getElementById("failed_ul").style.display = "block";
                            console.log('url : /bpm/process/grouptask/ ' + xhr.status + ' statusText ' + xhr.statusText);
                            $("#failed").html(xhr.status + ': ' + xhr.statusText);
                        }
                    }).done(function (resp) {
                        $('#modal_spinner').modal('hide');
                        populateTable(resp.taskid, resp.listdata, resp.payload, resp.statusListTask, resp.listaction, resp.history, resp.statusHistory, status, csrf);
                    });
                });
            } else {
                document.getElementById("group_task").style.display = "none";
            }

        } else {
            document.getElementById("group_task").style.display = "none";
        }
    });

});