@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

    @if($data == null)
    <div class="alert alert-success alert-dismissable">
        <h4><i class="fa fa-close"></i> Record not found </h4> Something wrong with data<a href="javascript:void(0)" class="alert-link"></a>
        <small class="text-primary">connect to :  {{env('DB_NEXTGEN_FULLGRANT_DATABASE')}}</small>
    </div>
    @endif
    
    @if(isset($data) && $data != null )
    
    <div class="block">
        <div class="block-title">
            <h2><strong>Kemaskini</strong>  Pembekal</h2>
            <small class="text-primary">connect to :  {{env('DB_NEXTGEN_FULLGRANT_DATABASE')}}</small>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="{{url('/support/report/log/patch-data-supplier')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                   data-title="List Action Patch Data Today ">View Today Action</a>
            </div>
        </div>
        
        @if(isset($result_status) && $result_status == 'success')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> {{$result_desc}}</a>
        </div>
        @elseif(isset($result_status) && $result_status == 'failed')
        <div class="alert alert-danger">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-exclamation-triangle"></i> Failed</h4> <span class="alert-link">{{$result_desc}}</span>
        </div>
        @endif
        <ul>
            <ol>
        <span class="text-primary"><strong>Segala aktiviti kemaskini yang dilakukan akan disimpan. Sila pastikan anda meletakkan 
            sebab data patching di ruangan 'Remarks'. Masukkan juga Case Number. </strong></span>
            </ol>
        </ul>    
        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
        <form id="form-search-mminf" action="{{url("/find/supplier")}}/{{ $data->appl_id  }}/{{ $data->supplier_id  }}" method="post" class="form-horizontal" onsubmit="return true;">
            {{ csrf_field() }}
            <input name="_method" id="_method" type="hidden" value="POST">
            <span id="selection_process_list" class="hide" >{{json_encode(App\Services\EPService::$SUPPLIER_PROCESS_PATCHING)}}</span>
            <div class="form-group">
                <label class="col-md-3 control-label" for="selected_process_id">Patching <span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <div class="input-group">
                        <select id="selected_process_id" name="selected_process_id" class="form-control">
                            <option value="">Please select</option>
                            @foreach(App\Services\EPService::$SUPPLIER_PROCESS_PATCHING as  $key => $obj)
                            <option value="{{$key}}" @if(old('selected_process_id_x') == $key) selected @endif>{{$obj['name']}}</option>
                            @endforeach
                        </select>
                        <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                    </div>
                    <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                </div>
            </div>
            <div class="form-group" id="panel_business_type_form" style="display:none;">
                <label class="col-md-3 control-label" for="selected_business_type">Business Type</label>
                <div class="col-md-6">
                    <div class="input-group">
                        <select id="selected_business_type" name="selected_business_type" class="form-control">
                            <option value="">Please select</option>
                            @foreach(App\Services\EPService::$BUSINESS_TYPE as  $key => $value)
                            <option value="{{$key}}" @if(old('selected_business_type_x') == $key) selected @endif>{{$key}} &raquo; {{$value}}</option>
                            @endforeach
                        </select>
                        <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group" id="panel_ssm_no_form"  style="display:none;">
                <label class="col-md-3 control-label" for="ssm_no">SSM No. / Registration No.<span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <input type="text" id="ssm_no" name="ssm_no" class="form-control" >
                </div>
            </div>
            <div class="form-group" id="panel_company_name_form"  style="display:none;">
                <label class="col-md-3 control-label" for="company_name">Company Name<span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <input type="text" id="company_name" name="company_name" class="form-control" >
                </div>
            </div>
            
            <div class="form-group" id="panel_appl_inprogress_form" style="display:none;">
                <label class="col-md-3 control-label" for="selected_inprogress_appl">Current Application (In Progress)</label>
                <div class="col-md-6">
                    <div class="input-group">
                        <select id="selected_inprogress_appl" name="selected_inprogress_appl" class="form-control">
                            <option value="">Please select</option>
                            @if(isset($appl_inprogress) && $appl_inprogress != null )
                            <option value="{{$appl_inprogress->appl_id}}" @if(old('selected_inprogress_appl') == $appl_inprogress->appl_id) selected @endif>{{$appl_inprogress->appl_id}} &raquo; {{$appl_inprogress->appl_no}}</option>
                            @endif
                        </select>
                        <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group" id="panel_appl_history_id_form"  style="display:none;">
                <label class="col-md-3 control-label" for="selected_history_appl">Appl History ID<span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <input type="number" id="selected_history_appl" name="selected_history_appl" class="form-control" >
                </div>
            </div>
            
            <div class="form-group">
                <label class="col-md-3 control-label" for="remarks">Remarks / Case Number <span class="text-danger">*</span></label>
                <div class="col-md-6">
                    <input type="text" id="remarks" name="remarks" class="form-control" >
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Kemaskini</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
    
    <div class="block block-alt-noborder full">
        <div class="block-title">
            <h2><strong>Maklumat Pengguna Pembekal</strong>  {{ $data->company_name }} &raquo;  <a href="{{url('find/epno/')}}/{{ $data->ep_no }}">{{ $data->ep_no }}</a></h2>
        </div>
        <div class="row">
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat Syarikat (SM_SUPPLIER) </span>
                        </h2>
                        
                    </div>
                    <address class="text-left">
                        <strong>&DoubleRightArrow; <span class="text-info">{{ $data->appl_no }}</strong> <span class="text-info" >Active</span> <br />
                        <strong>Company Name:  </strong> : {{ $data->company_name }}<br />
                        <strong>Supplier ID </strong> : {{ $data->supplier_id }}<br />
                        <strong>Appl ID </strong> : {{ $data->latest_appl_id }}<br />
                        <strong>Appl Type </strong> : {{ $data->appl_type }}<br />
                        <strong>Supplier Type </strong> : {{ $data->supplier_type }}<br />
                        <strong>Business Type </strong> : {{ $data->business_type }}  &raquo;  ({{ App\Services\EPService::$BUSINESS_TYPE[$data->business_type] }})<br />
                        <strong>SSM No </strong> : {{ $data->reg_no }}<br />
                        <strong>eP No </strong> : {{ $data->ep_no }}  <a target="_blank" href="{{ url('/find/gfmas/apive/') }}/{{ $data->ep_no }}" >Check Apive</a><br />
                        <strong>Establish Date </strong> : {{ $data->establish_date }} <br />
                        <strong>Record Status </strong> : <span class="bolder" @if($data->s_record_status == 9) style="color:red; font-weight:bolder" @endif>{{ $data->s_record_status }} </span><br />
                        <strong>Changed Date</strong> : {{ $data->s_changed_date }} <br />
                        <strong>Changed By</strong> : {{ $data->s_changed_by }} <br />
                    </address>
                </div>
            </div>

            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat Syarikat (SM_COMPANY_BASIC) </h2>
                    </div>
                    <address  class="text-left">
                        <address class="text-left">
                        <strong>&DoubleRightArrow; <span class="text-info">{{ $data->appl_no }}</strong> <span class="text-info" >Active</span> <br />    
                        <strong>Company Name:  </strong> : {{ $data->c_company_name }}<br />
                        <strong>Income Tax No </strong> : {{ $data->income_tax_no  }}<br />
                        <strong>Appl ID </strong> : {{ $data->appl_id }}<br />
                        <strong>Appl Type </strong> : {{ $data->appl_type }}<br />
                        <strong>Supplier Type </strong> : {{ $data->supplier_type }}<br />
                        <strong>Record Status </strong> : <span class="bolder" @if($data->c_record_status == 9) style="color:red; font-weight:bolder" @endif>{{ $data->c_record_status }} </span><br />
                        <strong>Phone No: </strong> : {{ $data->phone_country }}{{ $data->phone_area }}{{ $data->phone_no }} <br /> 
                        <strong>Fax No: </strong> : {{ $data->fax_country }}{{ $data->fax_area }}{{ $data->fax_no }} <br /> 
                        <strong>Federal? </strong> : {{ $data->is_with_federal  }}<br />
                        <strong>State? </strong> : {{ $data->is_with_state  }}<br />
                        <strong>Local Council? </strong> : {{ $data->is_with_statutory  }}<br />
                        <strong>GLC? </strong> : {{ $data->is_with_glc  }}<br />
                        <strong>Others? </strong> : {{ $data->is_with_others  }}<br />
                        <strong>Changed Date</strong> : {{ $data->c_changed_date }} <br />
                        <strong>Changed By</strong> : {{ $data->c_changed_by }} <br />
                    </address>
                    </address>
                </div>
            </div>
            
            @if(isset($data_inprogress) && $data_inprogress != null )
            <div class="col-sm-4">
                <div class="block">
                    <div class="block-title">
                        <h2>Maklumat Syarikat (SM_COMPANY_BASIC)</h2>
                    </div>
                    <address class="text-left">
                        <strong>&DoubleRightArrow; <span class="text-danger">{{ $data_inprogress->appl_no }}</strong> <span class="text-danger" >In Progress Application</span> <br />    
                        
                        <strong>Company Name:  </strong> : {{ $data_inprogress->c_company_name }}<br />
                        <strong>Income Tax No </strong> : {{ $data_inprogress->income_tax_no  }}<br />
                        <strong>Appl ID </strong> : {{ $data_inprogress->appl_id }}<br />
                        <strong>Appl Type </strong> : {{ $data_inprogress->appl_type }}<br />
                        <strong>Supplier Type </strong> : {{ $data_inprogress->supplier_type }}<br />
                        <strong>Record Status </strong> : <span class="bolder" @if($data_inprogress->c_record_status == 9) style="color:red; font-weight:bolder" @endif>{{ $data_inprogress->c_record_status }} </span><br />
                        <strong>Phone No: </strong> : {{ $data_inprogress->phone_country }}{{ $data_inprogress->phone_area }}{{ $data_inprogress->phone_no }} <br /> 
                        <strong>Fax No: </strong> : {{ $data_inprogress->fax_country }}{{ $data_inprogress->fax_area }}{{ $data_inprogress->fax_no }} <br /> 
                        <strong>Federal? </strong> : {{ $data_inprogress->is_with_federal  }}<br />
                        <strong>State? </strong> : {{ $data_inprogress->is_with_state  }}<br />
                        <strong>Local Council? </strong> : {{ $data_inprogress->is_with_statutory  }}<br />
                        <strong>GLC? </strong> : {{ $data_inprogress->is_with_glc  }}<br />
                        <strong>Others? </strong> : {{ $data_inprogress->is_with_others  }}<br />
                        <strong>Changed Date</strong> : {{ $data_inprogress->c_changed_date }} <br />
                        <strong>Changed By</strong> : {{ $data_inprogress->c_changed_by }} <br />
                    </address>
                </div>
            </div>
            @endif

        </div>
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                <tr>
                    <th class="text-center">UserID</th>
                    <th class="text-center">Name</th>
                    <th class="text-center">IC No</th>
                    <th class="text-center">Login ID</th>
                    <th class="text-center">Email</th>
                    <th class="text-center">Role</th>
                    <th class="text-center">PMUSER Status</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($listUsers as $user)
                    <tr>
                        <td class="text-center">{{ $user->user_id }}</td>
                        <td class="text-center">{{ $user->user_name }}</td>
                        <td class="text-center">{{ $user->identification_no }}</td>
                        <td class="text-center">{{ $user->login_id }}</td>
                        <td class="text-center">{{ $user->email }}</td>
                        <td class="text-center">{{ $user->role_code }}</td>
                        <td class="text-center">{{ $user->u_record_status }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
            
    </div>    
    @endif
    
            
    
    @include('_shared._modalListLogAction')
    
@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>

        $( "#selected_process_id" ).bind( "change", function() {
            var processList = JSON.parse($("#selection_process_list" ).text());
            //console.log(processList);
            var processId = $(this).find(":selected").val();
            console.log('processId',processId);
            if(processId.length > 0){
                var processObj = processList[processId];
                $('#selected_process_id_desc').text(processObj.description);
                //console.log(processObj);
            }
            
            // Reset First
            $('#panel_business_type_form').hide();
            $('#panel_ssm_no_form').hide();
            $('#panel_company_name_form').hide();
            $('#panel_appl_inprogress_form').hide();
            $('#panel_appl_history_id_form').hide();
            
            if(processId === 'supplier_business_type'){
                $('#panel_business_type_form').show();
            }else if(processId === 'supplier_ssm_no'){
                $('#panel_ssm_no_form').show();
            }else if(processId === 'supplier_company_name'){
                $('#panel_company_name_form').show();
            }else if(processId === 'cancel_appl_inprogress'){
                $('#panel_appl_inprogress_form').show();
            }else if(processId === 'setcancel_appl_history'){
                $('#panel_appl_history_id_form').show();
            }
            
            
            

        });

        
    
        
    </script>
@endsection