@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> List of Suppliers with Expired MOF but Still Active</strong></h1>
        </div>
        <div class="block">
            <!-- Log Content -->
            <div class="table-responsive">
                <p>
                    <code>
                                            Note: Suppliers whose MOF certification has expired for more than 24 months, but are still marked as active in the system and haven't logged in for more than 12 months.
                                            </code>
                </p>
            </div>
            <!-- END Log Content -->
        </div>

        <!-- SQL Button -->
        <div class="mb-3">
            <button type="button" class="btn btn-info" id="showSqlBtn">
                <i class="fa fa-database"></i> SQL
            </button>
        </div>

        <!-- SQL Modal -->
        <div class="modal fade" id="sqlModal" tabindex="-1" role="dialog" aria-labelledby="sqlModalLabel"
            aria-hidden="true">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="sqlModalLabel">SQL Query Source</h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <div id="sql-function-content">
                            <div class="text-center">
                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                <p>Loading SQL function...</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="table-responsive">
            <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Company Name</th>
                        <th class="text-center">EP No.</th>
                        <th class="text-center">MOF No.</th>
                        <th class="text-center">EP Role</th>
                        <th class="text-center">Last Login Date</th>
                        <th class="text-center">Days Not Logged In</th>
                        <th class="text-center">MOF Exp Date</th>
                        <th class="text-center">Days Expired</th>
                        <th class="text-center">ACTION</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($listdata))
                        @foreach ($listdata as $row)
                            <tr>
                                <td class="text-center">{{$row->company_name}}</td>
                                <td class="text-center">{{$row->ep_no}}</td>
                                <td class="text-center"><a href="{{url('/find/mofno/')}}/{{ $row->mof_no }}"
                                        target="_blank">{{ $row->mof_no }}</a></td>
                                <td class="text-center">{{$row->ep_role}}</td>
                                <td class="text-center">{{$row->login_date}}</td>
                                <td class="text-center">{{$row->days_not_login}}</td>
                                <td class="text-center">{{$row->exp_date}}</td>
                                <td class="text-center">{{$row->days_expired}}</td>
                                <td class="text-center"><a class='button' href="{{url('/find/supplier')}}/{{ $row->appl_id }}/{{ $row->supplier_id }}"
                                        target="_blank">FIX</a></td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </div>
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>

    <!-- Use a more distinct highlight.js theme for better visibility -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/styles/vs2015.min.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/highlight.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/11.9.0/languages/sql.min.js"></script>

    <script>
        $(function () {
            TablesDatatables.init();

            // Variable to track if content has been loaded
            var sqlContentLoaded = false;

            // Always highlight code when modal is shown
            $('#sqlModal').on('shown.bs.modal', function () {
                var codeBlock = document.querySelector('#sql-function-content code');
                if (codeBlock) {
                    hljs.highlightElement(codeBlock);
                }
            });

            // Handle SQL button click
            $('#showSqlBtn').on('click', function () {
                $('#sqlModal').modal('show');

                // Only fetch content if it hasn't been loaded before
                if (!sqlContentLoaded) {
                    // Fetch the function content dynamically
                    $.ajax({
                        url: '{{ url("/app-support/mof_active_to_deleted/sql") }}',
                        method: 'GET',
                        success: function (response) {
                            // Make sure code is visible with basic styling even before highlighting
                            var codeContent = response.function_content.replace(/</g, '&lt;').replace(/>/g, '&gt;');

                            $('#sql-function-content').html('<pre style="background-color: #1E1E1E; color: #DCDCDC; padding: 15px; border-radius: 5px;"><code class="sql">' + codeContent + '</code></pre>');

                            // Mark content as loaded
                            sqlContentLoaded = true;

                            // Ensure highlighting is applied
                            var codeBlock = document.querySelector('#sql-function-content code');
                            if (codeBlock) {
                                hljs.highlightElement(codeBlock);
                            }
                        },
                        error: function () {
                            $('#sql-function-content').html('<div class="alert alert-danger">Error loading SQL function</div>');
                        }
                    });
                }
            });

            App.datatables();
        });
    </script>
@endsection