@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Stuck Task  <br>
                <small>Stuck Task List</small> 
            </h1>
        </div>
    </div>

        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck Task List </strong>
                        <small>Issue task set as Completed. The task should set as ASSIGNED</small>
                    </h1>
                </div>
                <form action="{{url('/find/stuck-task-invalid-completed/')}}" method="get" class="form-horizontal form-bordered" >
                    
                    <div class="form-group">
                        <div class="input-group">
                            
                            <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                            <select id="composite_name" name="composite_name" class="select-chosen" 
                                data-placeholder="Choose Composite .." style="width: 200px;" required="required">
                                <option value="">Please select</option>
                                @foreach(App\Services\EPService::$BPM_COMPOSITE_MODULE as  $key => $value)
                                <option value="{{$key}}" @if($key == old('composite_name') ) selected @endif>{{$value}}</option>
                                @endforeach
                                
                            </select>
                            <span class="input-group-addon"><i class="fa fa-gears"></i>  Choose Composite Name <span class="text-danger">*</span></span>
                        </div>
                    </div>
                    <div class="form-group form-actions">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-sm btn-info pull-right">Submit</button>
                        </div>
                    </div>
                </form>
                @if($listdata != null && count($listdata) > 0)  
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">TASK CREATED</th>
                            <th class="text-center">FLOW ID</th>
                            <th class="text-center">COMPOSITE INSTANCE ID</th>
                            <th class="text-center">CUBE INSTANCE ID</th>
                            <th class="text-center">COMPOSITE NAME</th>
                            <th class="text-center">COMPOSITE VERSION</th>
                            <th class="text-center">COMPONENT NAME</th>
                            <th class="text-center">ACTIVITY NAME</th>
                            <th class="text-center">PROCESS NAME</th>
                            <th class="text-center">DOC NO</th>
                            <th class="text-center">STATE</th>
                            <th class="text-center">OUTCOME</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->composite_instance_id}}" target="_blank" >{{$data->composite_instance_id}}</a></td>
                                <td class="text-center">{{ $data->cube_instance_id }}</td>
                                <td class="text-center">{{ $data->composite_name }}</td>
                                <td class="text-center">{{ $data->composite_version }}</td>
                                <td class="text-center">{{ $data->component_name }}</td>
                                <td class="text-center">{{ $data->activity_name }}</td>
                                <td class="text-center">{{ $data->process_name }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('/find/trans/track/docno')}}/{{ $data->doc_no  }}" >{{ $data->doc_no }}</a></td>
                                <td class="text-center">{{ $data->state }}</td>
                                <td class="text-center">{{ $data->outcome }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
                @else
                <div class="row">
                    <div class="col-sm-6">
                        <p>No Stuck Tasks Available </p>
                    </div>
                </div>
                @endif
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
   


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



