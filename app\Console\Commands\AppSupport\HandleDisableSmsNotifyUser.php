<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Illuminate\Support\Facades\DB;
use Config;
use App\Migrate\MigrateUtils;

/**
 * Created on 25/8/2020
 * Ask by lilian to check and update frequently, should be there is no users set is_notify_by_sms as 1 to avoid sent notification by service DAPAT
 */
class HandleDisableSmsNotifyUser extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'set-notify-sms-disabled';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To update table PM_USER set is_notify_by_sms = 0. To make sure notify sms using DAPAT is OFF.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $listExcludeUsers = array(
                '647754'   // Case: 4239122  allow to get SMS
            );
            
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $countCheck = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER')
                    ->where('is_notify_by_sms',1)
                    ->count();
            
            MigrateUtils::logDump('Total is_notify_by_sms ACTIVE : '.$countCheck);
            
            DB::connection('oracle_nextgen_fullgrant')->transaction(function()  use ($listExcludeUsers) {   
                DB::connection('oracle_nextgen_fullgrant')->table('PM_USER')
                        ->where('is_notify_by_sms',1)
                        ->whereNotIn('user_id',$listExcludeUsers)
                        ->update([
                            'is_notify_by_sms' => 0
                        ]);
                MigrateUtils::logDump('Done! update pm_user set is_notify_by_sms = 0 ');
            });  
            
            MigrateUtils::logDump('Completed');
        
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error HandleDisableSmsNotifyUser'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
