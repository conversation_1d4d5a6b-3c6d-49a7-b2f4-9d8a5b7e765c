<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use DateTime;
use DateInterval;
use DatePeriod;
use Guzzle;
use GuzzleHttp\Client;

class CheckUserActivity {

    public static function run() {
        ini_set('memory_limit', '-1');
        $preLog = self::class . ' Completed ' . __FUNCTION__ . ' >> ' ;
        MigrateUtils::logDump($preLog.'Starting ... ');
        $dtStartTime = Carbon::now();
        $collectAll = collect([]);
        $listLoginId = self::getListLoginId();
        MigrateUtils::logDump($preLog.'Total LoginID to check: '.count($listLoginId));
        foreach($listLoginId as $loginId){
            
            $clInfo = self::checkByLoginId(loginId);
            $collectAll->push($clInfo);
            break;
        }
        dump($collectAll);

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function checkByLoginId($loginId){
        $preLog = self::class . ' Completed ' . __FUNCTION__ . ' >> ' ;
        //MigrateUtils::logDump($preLog.' Start check loginID : '.$loginId);
        $clInfo = collect([]);
        
        $statusUserLogin= self::wsStatusUserLogin($loginId);
        $clInfo->put("userStatus",$statusUserLogin);

        $userLoginInfo = self::getInfoUserLogin($loginId);
        $clInfo->put("userLoginInfo",$userLoginInfo);
        if($userLoginInfo){
            $userId = $userLoginInfo[0]->user_id;
            if($userLoginInfo[0]->org_type_id == 15){
                //MigrateUtils::logDump($preLog.' Detect as supplier : '.$loginId);
                $supplierInfo = self::getSupplierInfo($userId);
                $clInfo->put("supplierInfo",$supplierInfo);
            }else{
                //MigrateUtils::logDump($preLog.' Detect as Org Type ID : '.$userLoginInfo[0]->org_type_id);
                $govInfo = self::getGovInfo($userId);
                $clInfo->put("govinfo",$govInfo);
                
            }
            
            $userTrackingDiary = self::getTrackingDiary($userId);
            $clInfo->put("userTrackingDiary",$userTrackingDiary);

        }else{
            $clInfo->put("userTrackingDiary",null);
            MigrateUtils::logDump($preLog.' User not found : ');
        }

        $userLogActivityInfo = self::getLogActivityTrace($loginId);
        $clInfo->put("userLogActivityInfo",$userLogActivityInfo);

        $userCaseInfo = self::getCaseInfo($loginId);
        $clInfo->put("userCaseInfo",$userCaseInfo);

        return $clInfo;
    }

    protected static function getInfoUserLogin($loginId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT u.user_id,login_id,user_name,identification_no,
                email,h.LOGIN_DATE ,u.org_type_id, u.record_status 
                FROM pm_user u, pm_login_history  h WHERE u.user_id = h.user_id 
                AND LOWER(login_id) = lower(?)
            ",array($loginId));
        return $results;
    }

    protected static function getSupplierInfo($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT s.COMPANY_NAME ,s.EP_NO , 
                p.ep_role, p.name FROM sm_personnel p , sm_supplier s
            WHERE 
                p.appl_id =s.LATEST_APPL_ID 
            AND p.user_id = ?
            ",array($userId));
        return $results;
    }

    protected static function getGovInfo($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT distinct uo.org_profile_id,uo.RECORD_STATUS AS userorg_record_status, 
                ov.org_name, ov.org_code, ov.exp_date , ov.record_status AS org_record_status 
                FROM pm_user_org uo , 
                pm_org_validity ov 
                WHERE uo.org_profile_id = ov.org_profile_id 
                AND uo.USER_ID = ? 
                -- AND uo.record_status = 1
                AND ov.EXP_DATE = (SELECT max(f.EXP_DATE) FROM pm_org_validity f  WHERE f.org_profile_id = uo.org_profile_id)
            ",array($userId));
        return $results;
    }

    protected static function getLogActivityTrace($loginId) {
        $results = DB::connection('pgsql_dynatrace')->select(
            "SELECT
                log_dt,server_node, login_id,
                request_method,friendly_url, response_status
            from
                user_log ul
            where
                ul.login_id = ?
                and log_dt >= to_date('2021-07-01', 'YYYY-MM-DD');        
            ",array($loginId));
        return $results;
    }

    protected static function getTrackingDiary($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM PM_TRACKING_DIARY WHERE 
            actioned_by = ?
            AND to_char(actioned_date,'YYYY') = to_char(sysdate,'YYYY')
            AND rownum < 50
            ORDER BY actioned_date desc        
            ",array($userId));
        return $results;
    }

    protected static function wsStatusUserLogin($loginId) {

        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-pm/userlogin-status?loginId=" . $loginId;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect'+$ex->getMessage());
        }
    }

    protected static function getCaseInfo($loginId) {
        $results = DB::select(
            "SELECT 
			cases.`case_number`,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'request_type_list' AND value_code= cases_cstm.`request_type_c` LIMIT 0,1) AS case_request_type,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'category_list' AND value_code= cases_cstm.`category_c` LIMIT 0,1) AS case_category,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_sub_category_list' AND value_code= cases_cstm.`sub_category_c` LIMIT 0,1) AS case_sub_category,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_sub_category_2_list' AND value_code= cases_cstm.`sub_category_2_c` LIMIT 0,1) AS case_sub_sub_category,
			cases.`name` AS case_name,
			cases.`description` AS case_problem,
			cases.`doc_no` AS case_doc_no,
			cases.`resolution` AS case_resolution,
			u.`first_name` AS case_requested_by,
			(SELECT GROUP_CONCAT(email_address)   FROM email_addresses 
							INNER JOIN email_addr_bean_rel ON email_addresses.id = email_addr_bean_rel.email_address_id 
							WHERE email_addr_bean_rel.bean_module = 'Contacts' 
											AND email_addr_bean_rel.bean_id = cases_cstm.contact_id_c
											AND email_addr_bean_rel.deleted = 0 ) AS case_requested_by_email,
			YEAR(DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) ) AS case_year_created,		
			DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) AS case_created_date,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'case_status_dom' AND value_code= cases.`status` LIMIT 0,1) AS case_sub_status,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'case_state_dom' AND value_code= cases.`state` LIMIT 0,1) AS case_status,
			cases_cstm.`contact_mode_c` AS case_contact_mode_id,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_contact_mode_list' AND value_code= cases_cstm.`contact_mode_c` LIMIT 0,1) AS case_contact_mode
			FROM	cases 
			INNER JOIN cases_cstm ON cases.id = cases_cstm.id_c 
			INNER JOIN contacts u  ON u.id = cases_cstm.`contact_id_c`
			WHERE 
			YEAR(DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) )  = YEAR(NOW())
				AND u.login_id_nextgen = ?        
            ",array($loginId));
        return $results;
    }

    /**
     * To Check Personnel Activation No Valid
     * @return list
     */
    protected static function getListLoginId() {
        return ['771021145125',
                '35700044307', '35701019342', '35702139804',
                '35702139804', '35702178678', '35702191142',
                '357-00031399-5497', '357-01005306-5667',
                '357-02016686-5611', '357-02032385-5675',
                '357-02075091-5013', '357-02081499-5363',
                '357-02119407-5895', '357-02129300-5217',
                '357-02206547-5265', '357-02211882-5867',
                '357-02233980-5799', '357-02241426-5153',
                '357-02246787-6244', '357-02246787-6244',
                '357-02247393-5809', '357-02258800-5595',
                '357-02270337-5069', '357-02277086-5425',
                '357-20020393-5094', '600903025399', '601112085189',
                '710518105939', '740323055027', '740323055027',
                '740925025255', '741230015469', '760906146113',
                '770531115589', '770531115589', '770912055831',
                '771021145125', '810712045093', '810712045093',
                '830326025252', '830723015525', '840412085592',
                '840413135571', '841227115352', '850502145068',
                '860105115411', '860422525502', '860613335330',
                '860722387367', '860727525212', '870505295705',
                '870614065677', '870625025793', '890702145405',
                '920224015423', '921201025519', 'AAMMOVERS1', 'ACSLABS79',
                'ADMINSIX', 'AFIDENT2018', 'ALAMPESONA',
                'ALAMPESONAENT18', '<EMAIL>', 'ALFAROMEO',
                'ANPECAUTO8888', 'ASARUDDIN', 'AXIONETSB', 'BERA',
                'CONSTARSDNBHD', 'DINASURIA', 'EIRAZ1311', 'ELLYAF93',
                'ESNSB5001', 'EYZOLL5995', 'FAUZISANI', 'GEOHYDROCEAN777',
                'INFOINNERSPACE', 'IONETRADING200', 'IPOH',
                'ISHAKMAT6835', 'KBAIR2019', 'KENCANAJAYAMASSDNBHD',
                'KHATIJAH720310', 'KLHEARTCARE', 'KLUANG', 'MEE',
                'MINZWSVECK601MS', 'MULTITEK', 'NAVY', 'NAZRUL',
                'NIAGASRZ123', 'NILAIALAMDEV.SB', 'PARAAM13', 'PERAK',
                'REBUNG2018', 'ROADBASE', 'ROSDIFOTO1212',
                'SANCAHAYAMAJU', 'SCSB2018', 'SENTUHAN6543', 'SFS808PK',
                'SKESB6543', 'SMBC1234', 'SRZBANNER', 'STAGNOTECH',
                'STEELRECON', 'SUPERJAYABATERI', 'SURIA5998',
                'TALNOOR123', 'TALNOOR123', 'TECHPROSOLUTION', 'TEMPOYAK',
                'TISSOT', 'TMEH60285722', 'TMEH60285722',
                'TRANSRAPIDHOLIDAY', 'TRUESUPREME86', 'WMWENGINEERING',
                'WMWENGINEERINGRESOURCES', 'ZUFIQA2018'
        ];
    }



}
