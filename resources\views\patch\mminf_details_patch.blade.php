@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')



<div class="block">
    <div class="block">
        <div class="block-title">
            <h2><strong>Kemaskini</strong> Status rekod kod item</h2>
            <small class="text-primary">connect to :  {{env('DB_NEXTGEN_FULLGRANT_DATABASE')}}</small>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="{{url('/support/report/log/patch-data-mminf')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                   data-title="List Action Patch Data Today ">View Today Action</a>
            </div>
        </div>
        @if(isset($result_status) && $result_status == 'success')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> {{$result_desc}}</a>
        </div>
        @elseif(isset($result_status) && $result_status == 'failed')
        <div class="alert alert-danger">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-exclamation-triangle"></i> Failed</h4> <span class="alert-link">{{$result_desc}}</span>
        </div>
        @endif
        <ul>
            <ol>
                <span class="text-primary"><strong>Segala aktiviti kemaskini yang dilakukan akan disimpan. Sila pastikan anda meletakkan 
                        sebab data patching di ruangan 'Remarks'. Masukkan juga Case Number. </strong></span>
            </ol>
        </ul>    
        @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
        @endif
        <div class="tab-content">

            <form id="form-search-mminf" action="" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST">
                <span id="selection_process_list" class="hide" >{{json_encode(App\Services\EPService::$MMINF_PROCESS_PATCHING)}}</span>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="selected_process_id">Patching <span class="text-danger">*</span></label>
                    <div class="col-md-6">
                        <div class="input-group">
                            <select id="selected_process_id" name="selected_process_id" class="form-control">
                                <option value="">Please select</option>
                                @foreach(App\Services\EPService::$MMINF_PROCESS_PATCHING as  $key => $obj)
                                <option value="{{$key}}" @if(old('status_id') == $key) selected @endif>{{$obj['name']}}</option>
                                @endforeach
                            </select>
                            <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                        </div>
                        <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                    </div>
                </div>
                <div class="form-group" id="panel_material_desc_form"  style="display:none;">
                    <label class="col-md-3 control-label" for="material_desc">Material Desc<span class="text-danger">*</span></label>
                    <div class="col-md-6">
                        <input type="text" id="material_desc" name="material_desc" class="form-control" >
                    </div>
                </div>
                <div class="form-group" id="panel_material_add_desc_form"  style="display:none;">
                    <label class="col-md-3 control-label" for="material_add_desc">Material Add Desc<span class="text-danger">*</span></label>
                    <div class="col-md-6">
                        <input type="text" id="material_add_desc" name="material_add_desc" class="form-control" >
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="remarks">Remarks / Case Number <span class="text-danger">*</span></label>
                    <div class="col-md-6">
                        <input type="text" id="remarks" name="remarks" class="form-control" >
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Kemaskini</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div> 
            </form>


        </div>
    </div>
</div>

<div class="block">

    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">MMINF ID</th>
                    <th class="text-center">Material Code</th>
                    <th class="text-center">Code Type</th>
                    <th class="text-center">Action Code</th>
                    <th class="text-center">Material_Desc</th>
                    <th class="text-center">Material Add Desc</th>
                    <th class="text-center">Base UOM</th>
                    <th class="text-center">Alt UOM</th>
                    <th class="text-center">Is Sent</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Changed Date</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($listdata as $indexKey =>$data)
                <tr>                          
                    <td class="text-center">{{ $data->mminf_id }}</td>
                    <td class="text-center">{{ $data->material_code }}</td>
                    <td class="text-center">{{ $data->code_type }}</td>
                    <td class="text-center">{{ $data->action_code }}</td>
                    <td class="text-center">{{ $data->material_desc }}@if($data->notAsciiMaterialDesc == true)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="special character Non-ASCII detected!"></i>@endif</td>
                    <td class="text-center">{{ $data->material_add_desc }}@if($data->notAsciiMaterialAddDesc == true)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="special character Non-ASCII detected!"></i>@endif</td>
                    <td class="text-center">{{ $data->base_uom }}</td>
                    <td class="text-center">{{ $data->alt_uom }}</td>
                    <td class="text-center">{{ $data->is_sent }}</td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center td-{{ $data->mminf_id }}">{{ $data->changed_date }}</td>

                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
</div>

@if($listdata == 'notfound')
<div class="block block-alt-noborder full text-center label-primary">
    <span style="color: #FFF;">Tidak dijumpai!</span>
</div>
@endif



<div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div>
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>

@include('_shared._modalListLogAction')
<!-- END Content -->

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                    TablesDatatables.init();
                });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
                    ModalListActionLogDatatable.init();
                });</script>
<script>
    $("#selected_process_id").bind("change", function () {
        var processList = JSON.parse($("#selection_process_list").text());
        //console.log(processList);
        var processId = $(this).find(":selected").val();
        //console.log('processId',processId);
        if (processId.length > 0) {
            var processObj = processList[processId];
            $('#selected_process_id_desc').text(processObj.description);
            //console.log(processObj);
        }
        
        // Reset First
        $('#panel_material_desc_form').hide();
        $('#panel_material_add_desc_form').hide();

        if(processId === 'mminf_update_material_desc'){
            $('#panel_material_desc_form').show();
        }else if(processId === 'mminf_update_material_add_desc'){
            $('#panel_material_add_desc_form').show();
        }
            
    });
</script>

@endsection