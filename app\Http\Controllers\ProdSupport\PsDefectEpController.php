<?php

namespace App\Http\Controllers\ProdSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\ProdSupport\PsDefectEpService;
use Illuminate\Http\Request;
use DB;
use Carbon\Carbon;
use Auth;
use Excel;
use Exception;

class PsDefectEpController extends Controller {

    use PsDefectEpService;

    public function __construct() {
        $this->middleware('auth');
    }

    public function sirNumberFromRedmine(Request $request) {
        $collectResult = collect();
        $oriredmine_no = null;
        $orimodule = null;
        $oriraised = null;
        $oriassignee = null;
        $orideveloper = null;
        $oriedd = null;
        $oritypedefectid = null;
        $oriredmine_date = null;
        $oriSubject = null;
        $oritester = null;
        $oriteststatus = null;
        $oriremarks = null;
        $oristartdate = null;
        $orienddate = null;
        $orideploystatus = null;
        $oriredminestatus = null;
        $urgent_test = null;
        $dependency = null;
        $user = auth()->user()->first_name;
        $redmine_no = $request->redmine_no;
        $redmine_status = $request->status;
        $raised_by = $request->raised;
        $module = $request->moduletest;
        $assignee = $request->assigneeid;
        $developer = $request->developerid;
        $edd = $request->eddid;
        $typedefect = $request->typedefectid;
        $date = Carbon::parse($request->date1);
        $subject = $request->Subject;
        $deploy_status = $request->deploystatus;

        $listdata = $this->getDetailDefectTesting();
        foreach ($listdata as $list) {
            $list->testCount = $this->getCountTestingPerRedmine($list->defect_id);
        }

        $listdataWithClosedStatus = $this->getDetailDefectTestingWithClosedStatus();
        foreach ($listdataWithClosedStatus as $list) {
            $list->testCount = $this->getCountTestingPerRedmine($list->defect_id);
        }

        $listRedmineStatus = $this->listRedmineStatusDetails();



        if ($request->editid != null) {
            $editDefectId = $this->getDetailEditDefectTesting($request->editid);
            if (!$request->redmine_no) {
                $oriredmine_no = $editDefectId->redmine_no;
            } else
                $oriredmine_no = $request->redmine_no;
            if ($request->moduletest != "") {
                if (!$request->moduletest) {
                    $orimodule = $editDefectId->module;
                } else
                    $orimodule = $request->moduletest;
            } else {
                $orimodule = "";
            }
            if ($request->raised != "") {
                if (!$request->raised) {
                    $oriraised = $editDefectId->raised_by;
                } else
                    $oriraised = $request->raised;
            } else {
                $oriraised = "";
            }
            if ($request->assigneeid != "") {
                if (!$request->assigneeid) {
                    $oriassignee = $editDefectId->assignee;
                } else
                    $oriassignee = $request->assigneeid;
            } else {
                $oriassignee = "";
            }
            if ($request->developerid != null) {
                if (!$request->developerid) {
                    $orideveloper = $editDefectId->developer;
                } else
                    $orideveloper = $request->developerid;
            } else {
                $orideveloper = "";
            }
            if ($request->eddid != "") {
                if (!$request->eddid) {
                    $oriedd = $editDefectId->edd;
                } else
                    $oriedd = $request->eddid;
            } else {
                $oriedd = "";
            }
            if ($request->typedefectid != null) {
                if (!$request->typedefectid) {
                    $oritypedefectid = $editDefectId->type_defect;
                } else
                    $oritypedefectid = $request->typedefectid;
            } else {
                $oritypedefectid = "";
            }
            if (!$request->date1) {
                $oriredmine_date = $editDefectId->redmine_date;
            } else
                $oriredmine_date = $request->date1;
            if ($request->Subject != "") {
                if (!$request->Subject) {
                    $oriSubject = $editDefectId->subject;
                } else
                    $oriSubject = $request->Subject;
            } else {
                $oriSubject = "";
            }
            if ($request->tester != "") {
                if (!$request->tester) {
                    $oritester = $editDefectId->test_by;
                } else
                    $oritester = $request->tester;
            } else {
                $oritester = "";
            }
            if ($request->teststatus != "") {
                if (!$request->teststatus) {
                    $oriteststatus = $editDefectId->test_status;
                } else
                    $oriteststatus = $request->teststatus;
            } else {
                $oriteststatus = "";
            }
            if ($request->remarks != "") {
                if (!$request->remarks) {
                    $oriremarks = $editDefectId->test_remarks;
                } else
                    $oriremarks = $request->remarks;
            } else {
                $oriremarks = "";
            }
            if ($request->deploystatus != "") {
                if (!$request->deploystatus) {
                    $orideploystatus = $editDefectId->deploy_status;
                } else
                    $orideploystatus = $request->deploystatus;
            } else {
                $orideploystatus = "";
            }
            if ($request->status != "") {
                if (!$request->status) {
                    $oriredminestatus = $editDefectId->redmine_status;
                } else
                    $oriredminestatus = $request->status;
            } else {
                $oriredminestatus = "";
            }
            if (!$request->radio_urgent_testing) {
                $urgent_test = $editDefectId->urgent_testing;
            } else
                $urgent_test = $request->radio_urgent_testing;
            if (!$request->radio_dependency) {
                $dependency = $editDefectId->dependency;
            } else
                $dependency = $request->radio_dependency;

            if ($request->isMethod("POST")) {
                $updateData = [
                    'redmine_no' => $oriredmine_no,
                    'raised_by' => $oriraised,
                    'module' => $orimodule,
                    'assignee' => $oriassignee,
                    'developer' => $orideveloper,
                    'deploy_status' => $orideploystatus,
                    'redmine_status' => $oriredminestatus,
                    'edd' => $oriedd,
                    'type_defect' => $oritypedefectid,
                    'redmine_date' => $oriredmine_date,
                    'subject' => $oriSubject,
                    'urgent_testing' => $urgent_test,
                    'dependency' => $dependency,
                    'test_by' => $oritester,
                    'test_status' => $oriteststatus,
                    'test_remarks' => $oriremarks,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_defect_ep')
                        ->where('defect_id', $request->editid)
                        ->update($updateData);
            }
            return back();
        } else if ($redmine_no != null) {
            $checkingExisting = $this->checkingExistingRedmine($request->redmine_no);
            if ($checkingExisting == null) {
                $result = DB::connection('mysql_ep_prod_support')->table('ps_defect_ep')->insert([
                    ['redmine_no' => $redmine_no, 'redmine_status' => $redmine_status, 'raised_by' => $raised_by, 'deploy_status' => $deploy_status, 'test_status' => $request->teststatus, 'module' => $module, 'assignee' => $assignee, 'developer' => $developer, 'edd' => $edd, 'type_defect' => $typedefect, 'redmine_date' => $date, 'subject' => $subject, 'created_by' => $user, 'created_date' => Carbon::now(), 'urgent_testing' => $request->radio_urgent_testing, 'dependency' => $request->radio_dependency],
                ]);
                return back();
            } else {
                return $message = "Duplicate Redmine Number";
            }
        }

        return view('prod_support.defect_ep.testing_defect', [
            'listdata' => $listdata,
            'listRedmineStatus' => $listRedmineStatus,
            'listdataWithClosedStatus' => $listdataWithClosedStatus
        ]);
    }

    public function listDetailByFilterStatus($status, $carian, $deploy) {

        $listData = $this->getDetailsByStatus($status, $carian, $deploy);
        foreach ($listData as $list) {
            $list->testCount = $this->getCountTestingPerRedmine($list->defect_id);
        }

        return response()->json($listData, 200);
    }

    public function listDetailByFilterStatusClosed($carian) {

        $listData = $this->getDetailsByStatusClosed($carian);
        foreach ($listData as $list) {
            $list->testCount = $this->getCountTestingPerRedmine($list->defect_id);
        }

        return response()->json($listData, 200);
    }

    protected function listTestingDate($defectid) {
        $dateTesting = $this->getDetailDateAllTesting($defectid);
        if ($dateTesting != null) {
            $html = "<table  id='date_testing_datatable' class='table table-bordered table-vcenter'>
                    <thead>
                            <tr>
                            <th class='text-center'>Seq</th>
                            <th class='text-center'>Created By</th>
                            <th class='text-center'>Start Date</th>
                            <th class='text-center'>End Date</th>
                            <th class='text-center'>Remarks</th>
                            <th class='text-center'>Action</th>
                        </tr>
                    </thead>";
            $html = $html . "<tbody>";
            $counter = 0;
            foreach ($dateTesting as $rowData => $value) {
                $data = "
                    <tr>
                        <td class='text-center'>" . ($rowData + 1) . "</td>
                        <td class='text-center'>$value->created_date_by</td>
                        <td class='text-center'>$value->test_start_date</td>
                        <td class='text-center'>$value->test_end_date</td>
                        <td class='text-center'>$value->remarks</td>
                        <td class='text-center'><a id='trigger_edit_dt' dateid='{$value->defect_date_id}' seq='{$value->seq_id}' startdatetest ='{$value->test_start_date}' enddatetest='{$value->test_end_date}' remarkstest='{$value->remarks}' data-toggle='tooltip' title='Edit' class='btn btn-default'><i class='fa fa-edit'></i></a>
                            <a id='trigger_delete_dt' dateid='{$value->defect_date_id}' seq='{$value->seq_id}' defect_id='{$value->defect_id}' data-toggle='tooltip' title='Delete' class='btn btn-sm btn-danger'><i class='fa fa-times'></i></a></td>
                    </tr>";
                $html = $html . $data;
            }
            $html = $html . "<tbody></table>";
            return $html;
        }
    }

    public function addDateTesting(Request $request) {
        $startTestingDate = null;
        $endTestingDate = null;
        $user = auth()->user()->user_name;
        $defect_id_fk = $request->defectid;
        if ($request->startdate != null) {
            $startTestingDate = carbon::parse($request->startdate);
        }
        if ($request->enddate) {
            $endTestingDate = carbon::parse($request->enddate);
        } else
            $endTestingDate = null;
        $testRemarksPerDate = $request->remarks;

        $listDate = $this->getDetailDefectDateTesting();
        $getMaxSeqDate = $this->getDetailDefectDateTestingMax($defect_id_fk);
        $latestMaxId = $getMaxSeqDate[0]->seq;

//          create new date
        if ($request->defect_date_id == null && $latestMaxId == null && $startTestingDate != null) {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_defect_date')->insert([
                ['seq_id' => 1, 'defect_id' => $defect_id_fk, 'test_start_date' => $startTestingDate, 'test_end_date' => $endTestingDate, 'remarks' => $testRemarksPerDate, 'created_by' => $user, 'created_date' => Carbon::now()],
            ]);
        } else if ($request->defect_date_id == null && $latestMaxId != null && $startTestingDate != null) {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_defect_date')->insert([
                ['seq_id' => $latestMaxId + 1, 'defect_id' => $defect_id_fk, 'test_start_date' => $startTestingDate, 'test_end_date' => $endTestingDate, 'remarks' => $testRemarksPerDate, 'created_by' => $user, 'created_date' => Carbon::now()],
            ]);
        } else {
            'Add Date!';
        }
    }

    public function editDateTesting(Request $request) {
        $oristartdate = null;
        $orienddate = null;
        $oriremarks = null;
        $user = auth()->user()->first_name;
//        update date
        if ($request->defectdateid != null) {
            $defect_date = $this->getDetailDateTesting($request->defectdateid);
            if ($request->startdate != "") {
                if (!$request->startdate) {
                    $oristartdate = $defect_date->test_start_date;
                } else
                    $oristartdate = $request->startdate;
            } else {
                $oristartdate = "";
            }
            if ($request->enddate != "") {
                if (!$request->enddate) {
                    $orienddate = $defect_date->test_end_date;
                } else
                    $orienddate = $request->enddate;
            } else {
                $orienddate = "";
            }
            if ($request->remarks != "") {
                if (!$request->remarks) {
                    $oriremarks = $defect_date->remarks;
                } else
                    $oriremarks = $request->remarks;
            } else {
                $oriremarks = "";
            }
            if ($request->isMethod("POST")) {
                $updateData = [
                    'test_start_date' => carbon::parse($oristartdate),
                    'test_end_date' => carbon::parse($orienddate),
                    'remarks' => $oriremarks,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_defect_date')
                        ->where('defect_date_id', $request->defectdateid)
                        ->update($updateData);
            }
            return back();
        }
    }

    public function deleteDateTesting(Request $request) {
        if ($request->seqid != null) {
            DB::connection('mysql_ep_prod_support')->table('ps_defect_date')
                    ->where('defect_date_id', $request->editdateid)
                    ->delete();
            return back();
        }
    }

    public function testingFromRedmine($redmine_no) {

        try {
            $urlRedmine = env("URL_REDMINE", "https://192.168.120.102");
            $keyClientRedmine = env("KEY_CLIENT_REDMINE", "62d875bd246828ad033b54bf5b39a9a50c3aa1bb");

            $client = new \GuzzleHttp\Client([
                'base_uri' => $urlRedmine,
            ]);
            $data = [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'key' => $keyClientRedmine
                ],
                'verify' => false
            ];
            $response = $client->request('GET', '/issues/' . $redmine_no . '.json', $data);
            $resultResp = json_decode($response->getBody(), true);
            $app = $resultResp['issue']['custom_fields'];
            foreach ($app as $key => $try) {
                $try = $key;
                if ($app[$key]['name'] == 'Component') {
                    $defect_type = ($app[$key]['value']);
                }
            }
            $dev = $resultResp['issue']['custom_fields'];
            foreach ($dev as $key => $try) {
                $try = $key;
                if ($dev[$key]['name'] == 'Developer') {
                    $devlist = ($dev[$key]['value']);
                } else {
                    $devlist = null;
                }
            }
            return array(
                "status" => "Success",
                "status_desc" => 'Found Result',
                "result" => $resultResp, $defect_type, $devlist
            );
        } catch (Exception $ex) {
            return array(
                "status" => "Error",
                "status_desc" => 'Error getting result',
                "result" => $ex->getMessage()
            );
        }
    }

    public function downloadReport() {
        $listdata = $this->getDetailDefectTestingForDownload();
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Defect Testing';

            Excel::create($fileName, function($excel)use($collectlistReporting) {
                $excel->setTitle('Defect Testing');

                $excel->sheet('Defect Testing', function($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@',
                            'G' => '@',
                            'H' => '@',
                        ));

                        $sheet->row(1, array(
                            'Bil', 'Batch Date', 'Changed Date', 'Redmine No', 'Subject', 'Module', 'PIC', 'EDD', 'Status', 'Remarks', 'Type', 'Deployment Status' , 'Tester', 'Test Start Date', 'Test End Date', 'Days Count'
                        ));

                        $sheet->row(1, function($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:G1', function($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $obj) {
                            $sheet->row($count, array(
                                $obj->defect_id,
                                $obj->redmine_date,
                                $obj->changed_date,
                                $obj->redmine_no,
                                $obj->subject,
                                $obj->module,
                                $obj->raised_by,
                                $obj->edd,
                                $obj->test_status,
                                $obj->test_remarks,
                                $obj->type_defect,
                                $obj->deploy_status,
                                $obj->tester,
                                $obj->test_start_date,
                                $obj->test_end_date,
                                $obj->masa,
                                    )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

}
