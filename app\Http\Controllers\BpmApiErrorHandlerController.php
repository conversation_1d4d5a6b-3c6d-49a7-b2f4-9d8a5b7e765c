<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers;

use App\Services\Traits\BpmApiService;
use Log;
use Illuminate\Http\Request;

class BpmApiErrorHandlerController extends Controller {

    use BpmApiService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function listErrorHandler(Request $request) {

        $listdata = null;
        $totalList = null;
        $taskTitle = null;
        $statusApi = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            
            $this->validate($request, [
                'task_title' => 'required',
                'status_id' => 'required'
            ]);
            
            $taskTitle = $request->task_title;
            $statusId = $request->status_id;

            $list = $this->getErrorHandlerList($taskTitle,$statusId);

            if ($list["status"] == 'Success') {
                $listdata = $list["result"];
            }else{
                $statusApi = $list["result"];
            }
        }

        $data = array(
            "DP" => array(94008, 96004, 96702, 96705 ),
            "RN" => array(94004, 94007, 94010, 90001, 96704),
            "CT" => array(95001, 95003),
            "SM" => array(92001, 92002, 92003),
            "FL" => array(94001,94003, 94004, 94007, 94012, 94011, 94010),
            "QT" => array(96003,96004, 96006, 96013, 96007),
            "PM" => array(93001),
        );
        
        return view('bpm_api.list_error_handler', [
            'listdata' => $listdata,
            'totalList' => $totalList,
            'jsondata' => $data,
            'taskTitle' => $taskTitle,
            'statusApi' => $statusApi]);
    }

}
