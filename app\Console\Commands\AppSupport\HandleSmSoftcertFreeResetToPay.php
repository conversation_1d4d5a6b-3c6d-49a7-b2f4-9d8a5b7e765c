<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;

class HandleSmSoftcertFreeResetToPay extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSmSoftcertFreeResetToPay';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To update any supplier with same ssm no , already apply softcert free , then new creation account supplier, request softcert should be pay.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__ . ' starting ..');
        try {
            $listSupplierFreeSoftcert = $this->queryListSupplierFreeSoftcert();

            MigrateUtils::logDump(__METHOD__ . ' total suppliers found  : '.count($listSupplierFreeSoftcert ));
            // dd('OK');
            foreach ($listSupplierFreeSoftcert as $row){
                MigrateUtils::logDump(__METHOD__ .' data '.json_encode($row));
                if(strlen($row->ep_no) > 0){
                    DB::connection('oracle_nextgen_fullgrant')
                    ->table('SM_TMP_SOFTCERT_REQUEST')
                    ->insert(
                        [
                            'supplier_id' => $row->supplier_id,
                            'ep_no' =>  $row->ep_no,
                            'reg_no' => $row->reg_no,
                            'changed_by' => -1,
                            'changed_date' => Carbon::now()
                        ]
                        );
                }
                //dd('Done');
                
            }
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail($exc->getTraceAsString());
        }
        
        MigrateUtils::logDump(__METHOD__ . ' completed ');
    }

    protected function queryListSupplierFreeSoftcert(){
        MigrateUtils::logDump(__METHOD__ . ' entering..  ');
        $sql = "SELECT s.supplier_id, s.ep_no, s.company_name , s.reg_no, a.SUPPLIER_TYPE ,
        (SELECT count(*) FROM SM_SUPPLIER s2, SM_SOFTCERT_REQUEST sr WHERE s2.supplier_id = sr.supplier_id AND s2.reg_no = s.reg_no) AS total_softcert_request
        FROM sm_supplier s , sm_appl a
        WHERE s.latest_appl_id  = a.APPL_ID  
        AND a.supplier_type  NOT IN ('J')
        AND s.record_status IN (1,5) 
        AND s.ep_no is not null 
        AND NOT EXISTS (SELECT 1 FROM SM_SOFTCERT_REQUEST sr WHERE sr.supplier_id = s.supplier_id) 
        -- AND EXISTS (SELECT 1 FROM SM_SUPPLIER s2, SM_SOFTCERT_REQUEST sr WHERE s2.supplier_id = sr.supplier_id AND s2.reg_no = s.reg_no AND sr.IS_FREE = 1) 
        AND EXISTS (SELECT 1 FROM SM_SUPPLIER s2, SM_SOFTCERT_REQUEST sr WHERE s2.supplier_id = sr.supplier_id AND ( s2.reg_no = s.reg_no  OR s2.reg_no = replace(s.reg_no,'-','') ) AND sr.IS_FREE = 1) 
        ";
        return DB::connection('oracle_nextgen_fullgrant')->select($sql);
    }


    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => __METHOD__. ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . __METHOD__ . ' '.json_encode( ['Email' => $data["to"], 'ERROR' => $e->getMessage()]));
        }
    }
    
}
