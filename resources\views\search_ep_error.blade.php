@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianeperrmsg" action="{{url('/find/ep/error')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="errorCode" name="errorCode" value="{{ $errorCode }}" 
               min="4"
               class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... Kod Ke<PERSON>ahan eP">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
@if($result != null) 
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian eP Kod Mesej bagi Kod <PERSON>ahan @if($errorCode != null) {{ $errorCode }} @else <\?\> @endif <br/>
            <small>Carian ini untuk dapatkan mesej mengenai setiap kod kesalahan yang dipaparkan dalam eP.</small>
        </h1>
    </div>
</div>
<div class="block col-lg-6">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i><strong> KEPUTUSAN CARIAN </strong></h1>
    </div>
    <div class="row">
        @foreach ($result as $data)
            <table class="table table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-left" style="background-color: #15b357;color: white;">{{ $data['key'] }}</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-left">{{ $data['value'] }}</td>
                    </tr>
                </tbody>
            </table>
            <br/>
        @endforeach
    </div>
</div>
@endif

@endsection


@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                       TablesDatatables.init();
                   });</script>
@endsection
