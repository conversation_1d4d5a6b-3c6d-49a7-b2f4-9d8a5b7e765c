@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/dashboard/main/') }}">Main</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/batch/') }}">Batch File</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/osb/') }}">OSB</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/igfmas/') }}">IGFMAS</a>
            </li>
            <li class="active">
                <a href="{{ url('/dashboard/phis/') }}">PHIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/paymentreceipt/') }}">Payment AR502</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/gfmascrm/') }}">CRM</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/mygpis/') }}">MyGPIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/egpa/') }}">EGPA</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/spki/') }}">SPKI</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    Monitoring Web Service <strong>PHIS </strong>
                    <small>eP send web service to PHIS</small>
                </h5>
            </div>
            <div id="dash_monitoringWsFailedSent">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>

@endif

<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: true, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });


    $(document).ready(function () {

        //Monitoring MyGPIS
        $.ajax({
            url: APP_URL + '/dashboard/phis/checkMonitoringWsFailedSent',
            type: "GET",
            success: function (data) {
                tableListData.destroy();
                $data = $(data);
                $('#dash_monitoringWsFailedSent').hide().html($data).fadeIn();

                /* Re-Initialize Datatable */
                tableListData = $('#basic-datatable').DataTable({
                    columnDefs: [{orderable: true, targets: [0]}],
                    pageLength: 50,
                    lengthMenu: [[50, 100, 200, -1], [50, 100, 200, 'All']]
                });
            }
        });

    });
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
