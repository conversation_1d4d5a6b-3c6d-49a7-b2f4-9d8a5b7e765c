@extends('layouts.guest-dash')

<style>
    * {
        box-sizing: border-box;
    }

    /* Create two equal columns that floats next to each other */
    .column {
        float: left;
        width: 25%;
        padding: 10px;
    }

    /* Clear floats after the columns */
    .row:after {
        content: "";
        display: table;
        clear: both;
    }
</style>
<link rel="stylesheet" href="/css/jquery.treegrid.css">

@section('content')

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>COMPOSITE DETAILS <br>
            <small> (Production)  </small>
        </h1>
    </div>
</div> 


<div class="row">
    <div class="col-lg-12">
        <div class="block">
            <div class="block-title" >
                <h1><i class="fa fa-tasks"></i> <strong>COMPOSITE DETAILS</strong></h1>
            </div>

            <div class="block">
                {{ csrf_field() }}
                <form id="form-search-task" action="{{url("/bpm/instance/find")}}" method="POST" class="form-horizontal form-bordered">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <label class="col-md-3 col-md-offset-1 control-label" for="composite_module">Composite </label>
                        <div class="col-md-5">
                            <div class="input-group">
                                <select id="composite_module" name="composite_module" class="form-control">
                                    <option value="">Please select</option>
                                    @foreach($listdataComposite as  $key => $value)
                                    <option value="{{$key}}" @if($key == old('composite_module') ) selected @endif>{{$value}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 col-md-offset-1 control-label" for="composite_instance_id">Instance ID <span class="text-danger">*</span></label>
                        <div class="col-md-5">
                            <input id="composite_instance_id" name="composite_instance_id" class="form-control" value="{{$instanceId}}" placeholder="Instance ID.." type="number" 
                        </div>
                        <div class="col-md-9 col-md-offset-4">
                            <button type="submit" class="btn btn-sm btn-info"><i class="gi gi-search"></i> Search</button>
                        </div>
                    </div>
                </form>
            </div>

            <div>                 

                <?php

//               https://www.sitepoint.com/community/t/php-recursive-multidimensional-array-to-html-nested-code/256533

                function recursive($array, $level = 1) {

                    $sort = array();
                    foreach ($array as $k => $v) {
                        $sort['componentDateCreationTime'][$k] = $v['componentDateCreationTime'];
                    }
                    array_multisort($sort['componentDateCreationTime'], SORT_ASC, $array);

                    echo '<ul id="ul_name" style="list-style-type: none;">';
                    foreach ($array as $value) {
                        $date = date('d/m/y h:i:s A', strtotime($value["componentDateCreation"]));

                        if ($value["componentType"] === 'bpmn') {  //<a style="color:black;">{{ $data["componentInstanceId"] }} </a>
                            echo str_repeat("&emsp;", $level), '<li id="process_instance" value="' . $value["componentInstanceId"] . '">'
                            . '<div class="row">'
                            . '<div class="column text-center"> <i class="caret-icon fa fa-caret-down process"> <i class="fa fa-sitemap"> ' . $value["componentName"] . ' </i></i></div>'
                            . '<div class="column text-center" data-id="' . $value["componentInstanceId"] . '" data-status="' . $value["componentState"] . '"><a style="color:black;">' . $value["componentInstanceId"] . '</a></div>'
                            . '<div class="column text-center bpmnProcess" style="' . App\Services\BPMAPIService::$BPM_STATE_STYLE[$value["componentState"]] . '"> <i class="fa fa-' . App\Services\BPMAPIService::$BPM_STATE_ICON[$value["componentState"]] . '"></i> ' . App\Services\BPMAPIService::$BPM_STATE[$value["componentState"]] . '</div>'
                            . '<div class="column text-center">' . $date . '</div></div>';
                        } else {
                            echo str_repeat("&emsp;", $level), '<li id="process_instance" value="' . $value["componentInstanceId"] . '">'
                            . '<div class="row">'
                            . '<div class="column text-center"> <i class="caret-icon fa fa-caret-down process"> <i class="fa fa-user"> ' . $value["componentName"] . ' </i></i></div>'
                            . '<div class="column text-center" data-id="' . $value["componentInstanceId"] . '" data-status="' . $value["componentState"] . '"><a style="color:black;">' . $value["componentInstanceId"] . '</a></div>'
                            . '<div class="column text-center workflowProcess" style="' . App\Services\BPMAPIService::$BPM_STATE_STYLE[$value["componentState"]] . '"><i class="fa fa-' . App\Services\BPMAPIService::$BPM_STATE_ICON[$value["componentState"]] . '"></i> ' . App\Services\BPMAPIService::$BPM_STATE[$value["componentState"]] . '</div>'
                            . '<div class="column text-center">' . $date . '</div></div>';
                        }

                        if (isset($value['childs'])) {
//                            recursive($value['childs'], $level + 1);
                        }
                        echo '</li>';
                    }
                    echo '</ul>';
                }

                if (isset($listdata["childs"])) {
                    echo '<div class="row">'
                    . '<div class="column text-center"><strong> Name </strong></div>'
                    . '<div class="column text-center"><strong> Id </strong></div>'
                    . '<div class="column text-center"><strong> State </strong></div>'
                    . '<div class="column text-center"><strong> Created </strong></div>'
                    . '</div>';
                    recursive($listdata["childs"]);
                }
                ?>


            </div>


            @endsection
            @section('jsprivate')
            <!-- Load and execute javascript code used only in this page -->

            <script src="/js/vendor/jquery.treegrid.min.js"></script>
            <script src="/js/vendor/jquery.treegrid.bootstrap3.js"></script>

            <script>
            $('#page-container').removeAttr('class');
            </script>

            <script>

                $(document).ready(function () {
                    $('.tree').treegrid({
                        expanderExpandedClass: 'fa fa-angle-down',
                        expanderCollapsedClass: 'fa fa-angle-right'
                    });
                });

                var process = document.getElementsByClassName("process");

                for (i = 0; i < process.length; i++) {
                    process[i].addEventListener("click", function () {
                        $(this).closest("li").find("[id='process_instance']").slideToggle();
                        $(this).toggleClass('fa-caret-right fa-caret-down');
                    });
                }

                var inp=[{"primary":{"sub":{"ID":[1,2,3]}}},
                        {"primary":{"sub":{"Name":[4,5,6]}}},
                        {"primary":{"ID":[7,8,9]}},
                        {"primary":{"Name":[5,5,5]}}];
                
                var tree={};


                function dive(tree,obj){
                    for (var p in obj) {
                        if (!tree[p]) tree[p]=obj[p];
                        else     dive(tree[p],obj[p]);
                    }
                }

                    inp.forEach((el)=>dive(tree,el));

//                    console.log(tree);
            </script>
            @endsection