@extends('layouts.guest-dash')

@section('header')
    <div class="row">
        <div class="col-md-6">
            <form id="carianform"
                action="{{ url('/gpki/user-signing-list') . '?cari=' . request()->cari . '&year=' . request('year') }}"
                method="get" class="navbar-form-custom">
                <div class="form-group">
                    <input type="text" id="cari" name="cari" value="{{ request('cari') }}" class="form-control"
                        onfocus="this.select();" placeholder="Carian di sini ... ">
                    <input type="hidden" name="year" value="{{ request('year') }}">
                </div>
            </form>
        </div>
        <div class="col-md-6">
            <form id="yearform" action="{{ url('/gpki/user-signing-list') }}" method="get" class="navbar-form-custom">
                <div class="form-group">
                    <select name="year" class="form-control" onchange="this.form.submit()">
                        <option value="">Current Year</option>
                        @foreach($availableYears as $availableYear)
                            <option value="{{ $availableYear }}" {{ request('year') == $availableYear ? 'selected' : '' }}>
                                {{ $availableYear }}
                            </option>
                        @endforeach
                    </select>
                    <input type="hidden" name="cari" value="{{ request('cari') }}">
                </div>
            </form>
        </div>
    </div>
@endsection

@section('content')
    <div class="block full">
        <div class="block-title">
            <h2><strong>GPKI</strong> User Signing List {{ request('year') ? '- Year ' . request('year') : '' }}</h2>
            <div class="block-options pull-right">
                <form action="{{ url('/gpki/user-signing-list-csv') }}" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" name="cari" value="{{ request('cari') }}">
                    <input type="hidden" name="year" value="{{ request('year') }}">
                    <button type="submit" class="btn btn-sm btn-primary" data-toggle="tooltip" title="Export to CSV">
                        <i class="fa fa-file-excel-o"></i> &nbsp;Export
                    </button>
                </form>
            </div>
        </div>
        <div class="block-content">
            <div class="row">
                <div class="col-xs-12">
                    <div class="row text-center">
                        <div class="col-xs-3">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <h3><a href="{{ url('/gpki/user-signing-list') . '?year=' . request('year') }}"
                                            id="jumlah-all">
                                            <i class="fa fa-spinner fa-spin"></i>
                                        </a></h3>
                                </div>
                                <div class="panel-footer">
                                    <span class="label label-info">ALL</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <h3><a href="{{ url('/gpki/user-signing-list') . '?cari=exist&year=' . request('year') }}"
                                            id="jumlah-exist">
                                            <i class="fa fa-spinner fa-spin"></i>
                                        </a></h3>
                                </div>
                                <div class="panel-footer">
                                    <span class="label label-success">EXIST</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <h3><a href="{{ url('/gpki/user-signing-list') . '?cari=not_found&year=' . request('year') }}"
                                            id="jumlah-not-found">
                                            <i class="fa fa-spinner fa-spin"></i>
                                        </a></h3>
                                </div>
                                <div class="panel-footer">
                                    <span class="label label-warning">NOT FOUND</span>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="panel panel-default">
                                <div class="panel-body">
                                    <h3><a href="{{ url('/gpki/user-signing-list') . '?cari=error&year=' . request('year') }}"
                                            id="jumlah-error">
                                            <i class="fa fa-spinner fa-spin"></i>
                                        </a></h3>
                                </div>
                                <div class="panel-footer">
                                    <span class="label label-danger">ERROR</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="table-responsive">
                <table class="table table-striped table-bordered" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>IC No.</th>
                            @if(!request('year'))
                                <th>Created By</th>
                            @else
                                <th>User ID</th>
                                <th>Total GPKI</th>
                                <th>Batch</th>
                            @endif
                            <th>Status</th>
                            <th>Remark</th>
                            <th>Created Date</th>
                            <th>Updated Date</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($results as $result)
                                        <tr>
                                            <td>{{ $result->identification_no }}</td>
                                            @if(!request('year'))
                                                <td>{{ $result->created_by }}</td>
                                            @else
                                                <td>{{ $result->user_id }}</td>
                                                <td>{{ $result->total_gpki }}</td>
                                                <td>{{ $result->batch }}</td>
                                            @endif
                                            <td>
                                                <span class="label label-{{ isset($result->status) ?
                            ['exist' => 'success', 'not_found' => 'warning', 'error' => 'danger', 'pending' => 'default']
                            [strtolower($result->status)] ?? 'default' : 'default' }}">
                                                    {{ $result->status ?? 'N/A' }}
                                                </span>
                                            </td>
                                            <td>
                                                <div class="remark-container" style="max-height: 100px; overflow-y: auto;">
                                                    <code>{{ $result->remark }}</code>
                                                </div>
                                            </td>
                                            <td>{{ $result->created_at }}</td>
                                            <td>{{ $result->updated_at }}</td>
                                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <div class="row">
                <div class="col-sm-12">
                    <div class="pull-right">
                        {{ $results->appends(['cari' => request('cari'), 'year' => request('year')])->links() }}
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        $(document).ready(function () {
            // Status count AJAX call
            $.ajax({
                url: '{{ url('/gpki/user-status-count') }}',
                type: 'GET',
                data: {
                    year: '{{ request('year') }}'
                },
                dataType: 'json',
                success: function (data) {
                    console.log(data);
                    const getCount = (status) => {
                        const item = data.find(item => item.status === status);
                        return item ? item.jumlah : 0;
                    };

                    $('#jumlah-all').text(data.reduce((total, item) => total + parseInt(item.jumlah), 0));
                    $('#jumlah-exist').text(getCount('exist'));
                    $('#jumlah-not-found').text(getCount('not_found'));
                    $('#jumlah-error').text(getCount('error'));
                },
                error: function (xhr, status, error) {
                    console.error('Error fetching status counts:', error);
                    $('.panel-body h3 a').text('Error');
                }
            });

            // Initialize tooltips
            $('[data-toggle="tooltip"]').tooltip();
        });
    </script>

    <style>
        .remark-container {
            max-height: 100px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }

        .table>tbody>tr>td {
            vertical-align: middle;
        }

        .panel {
            margin-bottom: 20px;
            border-radius: 4px;
            box-shadow: 0 1px 1px rgba(0, 0, 0, .05);
        }

        .label {
            display: inline-block;
            padding: .3em .6em;
            font-size: 85%;
        }
    </style>
@endsection