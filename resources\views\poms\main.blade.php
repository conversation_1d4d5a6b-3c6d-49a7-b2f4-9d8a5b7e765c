@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
    @if (Auth::user())
        <div class="row">
            <div class="col-lg-12">
                <div class='widget'>
                    <div class="widget-extra themed-background-dark">
                        <div class="row">
                            <div class="col-md-6">
                                <h5 class='widget-content-light' style="margin-top: 10px; margin-bottom: 10px;">
                                    POMS - <strong>IT Incident Monitoring</strong>
                                </h5>
                            </div>
                            <div class="col-md-6 text-right">
                                <form method="GET" action="{{ route('poms.dashboard') }}" class="form-inline" style="margin-top: 5px;">
                                    <div class="form-group">
                                        <label for="year" class="control-label" style="color: #fff; margin-right: 10px; margin-top: 5px;">Year:</label>
                                        <select name="year" id="year" class="form-control input-sm" onchange="this.form.submit()">
                                            @foreach($years as $year)
                                                <option value="{{ $year }}" {{ $selectedYear == $year ? 'selected' : '' }}>{{ $year }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                    <div class="table-responsive">
                        <table id="basic-datatable" class="table table table-vcenter table-striped">
                            <thead>
                                <tr>
                                    <th>Year</th>
                                    <th>Month</th>
                                    <th>ACK Count</th>
                                    <th>RIT Count</th>
                                    <th>S123 Count</th>
                                    <th>ACK Duplicate</th>
                                    <th>RIT Duplicate</th>
                                    <th>S123 Duplicate</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($data as $row)
                                    <tr data-year="{{ $row['YEAR'] }}">
                                        <td>{{ $row['YEAR'] }}</td>
                                        <td>{{ $row['MONTH'] }}</td>
                                        <td>
                                            @if ($row['ACK_COUNT'] > 0)
                                                <a href="#" class="data-link" data-type="ACK_COUNT" data-value="{{ $row['ACK_COUNT'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    {{ $row['ACK_COUNT'] }}
                                                </a>
                                            @else
                                                {{ $row['ACK_COUNT'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['RIT_COUNT'] > 0)
                                                <a href="#" class="data-link" data-type="RIT_COUNT" data-value="{{ $row['RIT_COUNT'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    {{ $row['RIT_COUNT'] }}
                                                </a>
                                            @else
                                                {{ $row['RIT_COUNT'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['S123_COUNT'] > 0)
                                                <a href="#" class="data-link" data-type="S123_COUNT" data-value="{{ $row['S123_COUNT'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    {{ $row['S123_COUNT'] }}
                                                </a>
                                            @else
                                                {{ $row['S123_COUNT'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['ACK_DUPLICATE'] > 0)
                                                <a href="#" class="data-link" data-type="ACK_DUPLICATE" data-value="{{ $row['ACK_DUPLICATE'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    <strong>{{ $row['ACK_DUPLICATE'] }}</strong>
                                                </a>
                                            @else
                                                {{ $row['ACK_DUPLICATE'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['RIT_DUPLICATE'] > 0)
                                                <a href="#" class="data-link" data-type="RIT_DUPLICATE" data-value="{{ $row['RIT_DUPLICATE'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    <strong>{{ $row['RIT_DUPLICATE'] }}</strong>
                                                </a>
                                            @else
                                                {{ $row['RIT_DUPLICATE'] }}
                                            @endif
                                        </td>
                                        <td>
                                            @if ($row['S123_DUPLICATE'] > 0)
                                                <a href="#" class="data-link" data-type="S123_DUPLICATE" data-value="{{ $row['S123_DUPLICATE'] }}" data-year="{{ $row['YEAR'] }}" data-month="{{ $row['MONTH'] }}" style="color: red;">
                                                    <strong>{{ $row['S123_DUPLICATE'] }}</strong>
                                                </a>
                                            @else
                                                {{ $row['S123_DUPLICATE'] }}
                                            @endif
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modal -->
        <div class="modal fade" id="dataModal" tabindex="-1" aria-labelledby="dataModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="dataModalLabel">Data Details</h5>
                    </div>
                    <div class="modal-body">
                        <!-- Spinner -->
                        <div id="modal-spinner" class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>

                        <!-- DataTable -->
                        <table id="modal-datatable" class="table table-striped table-bordered" style="width:100%; display: none;">
                            <thead>
                                <tr>
                                    <th>Case Number</th>
                                    <th>Subject</th>
                                    <th>Task Number</th>
                                    <th>SLA Flag</th>
                                    <th>Actual Start Date</th>
                                    <th>Actual Completed Date</th>
                                    <th>Available Duration</th>
                                    <th>Actual Duration</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function () {
            // Initialize DataTables
            App.datatables();
            var table = $('#basic-datatable').DataTable({
                pageLength: 25,
                order: [[0, 'desc'], [1, 'desc']], // Sort by year (desc) then month (desc)
                columnDefs: [
                    { type: 'num', targets: [0, 1] }, // Ensure numeric sorting for year and month columns
                    { visible: false, targets: [0] } // Hide the year column as it's already filtered
                ]
            });
            
            // Show the year in the table header
            $('h2').first().after('<h4 class="text-muted">Year: {{ $selectedYear }}</h4>');
    
            // Initialize modal DataTable
            let modalTable = $('#modal-datatable').DataTable();
    
            // Handle modal popup for data links
            $('.data-link').on('click', function (e) {
                e.preventDefault();
    
                // Get data from clicked link
                var type = $(this).data('type');
                var value = $(this).data('value');
                var year = $(this).data('year');
                var month = $(this).data('month');
    
                // Update modal title
                $('#dataModalLabel').text(`Details for ${type} (${year}-${month})`);
    
                // Clear previous data
                modalTable.clear();
    
                // Show spinner and hide the table
                $('#modal-spinner').show();
                $('#modal-datatable_wrapper').hide();
    
                // Fetch data via AJAX
                $.ajax({
                    url: '/poms/crm/modal-data',
                    method: 'POST',
                    data: {
                        _token: '{{ csrf_token() }}',
                        type: type,
                        value: value,
                        year: year,
                        month: month,
                    },
                    success: function (response) {
                        // Hide spinner and show the table
                        $('#modal-spinner').hide();
                        $('#modal-datatable_wrapper').show();
                        $('#modal-datatable').show();
    
                        // Clear existing data
                        modalTable.clear().draw();
                        
                        // Check if we have data
                        if (response.success && response.data && response.data.length > 0) {
                            // Get the first item to detect column names
                            const firstItem = response.data[0];
                            const keys = Object.keys(firstItem);
                            
                            // Get column names based on available data
                            const columnDefs = [
                                {
                                    data: 'case_number',
                                    title: 'Case Number',
                                    render: function (data, type, row) {
                                        return `<a href="${location.origin}/support/crm/case?case_number=${data}" target="_blank">${data}</a>`;
                                    }
                                },
                                { data: keys.find(k => k.includes('_name')) || 'name', title: 'Subject' },
                                { data: keys.find(k => k.includes('_task_number')) || 'task_number', title: 'Task Number' },
                                { data: keys.find(k => k.includes('_sla_flag')) || 'sla_flag', title: 'SLA Flag' },
                                { data: keys.find(k => k.includes('_actual_start_datetime')) || 'actual_start_datetime', title: 'Actual Start Date' },
                                { data: keys.find(k => k.includes('_completed_datetime')) || 'completed_datetime', title: 'Actual Completed Date' },
                                { data: keys.find(k => k.includes('_available_duration')) || 'available_duration', title: 'Available Duration' },
                                { data: keys.find(k => k.includes('_actual_duration')) || 'actual_duration', title: 'Actual Duration' }
                            ];
                            
                            // Destroy existing DataTable
                            if ($.fn.DataTable.isDataTable('#modal-datatable')) {
                                modalTable.destroy();
                            }
                            
                            // Initialize new DataTable with columns
                            modalTable = $('#modal-datatable').DataTable({
                                data: response.data,
                                columns: columnDefs,
                                responsive: true,
                                pageLength: 10,
                                lengthMenu: [[10, 25, 50, -1], [10, 25, 50, 'All']]
                            });
                        } else {
                            // No data available
                            modalTable.clear().draw();
                            modalTable.row.add(['No data available']).draw();
                        }
                    },
                    error: function () {
                        // Hide spinner
                        $('#modal-spinner').hide();
                        alert('Failed to fetch data.');
                    },
                });
    
                // Show modal
                $('#dataModal').modal('show');
            });
        });
    </script>    
@endsection
