<?php

namespace App\Http\Controllers;

use App\Migrate\Nextgen\PMService;
use App\Migrate\Nextgen\SMService;
use App\Migrate\Nextgen\CRMService;
use App\Migrate\Nextgen\SyncGovernmentInfo;
use App\Services\Traits\SupplierService;
use Log;
use DB;
use Response;

/**
 * Kindly refer project cdccrm-integration. This code not as latest version. 
 * @deprecated
 */
class BatchController extends Controller {

    use SupplierService;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('guest');
    }

    /**
     * Manual request to Sync LoginID from Nextgen to CRM
     * @deprecated
     * @param  array  $data
     * @return User
     */
    public function syncUser($loginID) {
        $ObjSupplierUser = $this->syncSupplierUser($loginID);
        if($ObjSupplierUser == NULL){
            return $ObjOrganizationUser = $this->syncOrganizationUser($loginID);
        }
        return $ObjSupplierUser;
    }
    
    /**
     * Manual request to Migrate LoginID from Nextgen to CRM
     * @deprecated
     * @param  array  $data
     * @return User
     */
    public function syncSupplierUser($loginID) {
        $results = SMService::getSMSupplierUsersActiveByLoginID($loginID);

        if (count($results) > 0) {
            foreach ($results as $obj) {

                Log::info(' Check Company Name :- ' . $obj->company_name);
                Log::info('      eP No. :- ' . $obj->ep_no);
                $account = CRMService::getSupplierCrm($obj);

                if ($account == null) {
                    //Create Account
                    Log::info('     CREATE ACCOUNT :- ' . $obj->ep_no);
                    /* Need get Details */
                    $supplierObj = SMService::getSMSuppliersDetail($obj->supplier_id);
                    Log::info(json_encode($supplierObj));
                    $account = CRMService::saveAccountSupplierPrimary($supplierObj);
                    CRMService::saveAccountSupplierAddress($supplierObj, $account->id);
                }

                /** FIND account -> contact * */
                $query = DB::table('accounts as a')
                        ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                        ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                        ->where('ac.account_id', $account->id)
                        ->where('c.identity_no_nextgen', trim($obj->identification_no))
                        ->select('a.name as acc_name', 'a.id as acc_id')
                        ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                //var_dump($query->toSql());
                $contact = $query->first();
                if ($contact == null) {

                    if ($obj->p_record_status == '1') {
                        $contactObj = CRMService::createContactSupplier($account, $obj);
                        Log::info('        :: ->  success create ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                    } else {
                        Log::info('        :: ->  No need create this user . This user is InActive and not in record CRM');
                    }
                } else {
                    //update it
                    $contactObj = CRMService::saveContactSupplier($obj, $contact);
                    CRMService::saveEmailContact($obj->p_email, $contact->contact_id);
                    Log::info('        :: ->  success update ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                }
            }

            $contacts = DB::table('contacts')->where('login_id_nextgen', $loginID)->first();
            return Response::json($contacts);
        }
        return null;
    }

    /**
     * Manual request to Migrate LoginID from Nextgen to CRM
     * @deprecated
     * @param  array  $data
     * @return User
     */
    public function syncOrganizationUser($loginID) {
        $results = PMService::getPMOrgGovUsersDetailsByLoginID($loginID);
        if (count($results) > 0) {
            foreach ($results as $obj) {
                $orgcode = $obj->org_code;
                $orgTypeDesc = PMService::getOrgProfileType($obj->op_org_type_id);
                Log::info('     ' . $orgTypeDesc . ' -> ' . $orgcode);
                Log::info('     ' . json_encode($obj));
                Log::info('    Name :- ' . $obj->fullname);
                Log::info('     Identity No. :- ' . $obj->identification_no);
                Log::info('     Login ID :- ' . $obj->login_id);
                Log::info('     Status  :- ' . $obj->uo_record_status);
                $roles = PMService::getPMOrgGovUserRolesDetails($obj->user_org_id);
                $roles_name = null;
                foreach ($roles as $objRole) {
                    if ($roles_name == null) {
                        $roles_name = $objRole->role_code;
                    } else {
                        $roles_name = $roles_name . ', ' . $objRole->role_code;
                    }
                }

                //Check Data from CRM
                $account = CRMService::getGovProfileCrm($obj->org_code, $obj->op_org_type_id);
                if ($account == null) {
                    $account = SyncGovernmentInfo::prepareCreateAccount($obj);
                }

                if ($account != null) {
                    Log::info('     Checking Account ORG CODE  :- ' . $account->org_gov_code. ' ID : '.$account->id);
                    Log::info($account);
                    /** Let find and check in CRM * */
                    /** FIND account -> contact * */
                    $contact = DB::table('accounts as a')
                            ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                            ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                            ->where('a.id', $account->id)
                            ->where('c.identity_no_nextgen', trim($obj->identification_no))
                            ->select('a.name as acc_name', 'a.id as acc_id', 'a.org_gov_code', 'a.org_gov_type')
                            ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen')
                            ->first();
                    if ($contact == null) {
                        
                        if ($account != null) {
                            
                            //Checking old record user in CRM, try to sync it
                            $query = DB::table('accounts as a')
                                ->join('accounts_contacts as ac', 'a.id', '=', 'ac.account_id')
                                ->join('contacts as c', 'ac.contact_id', '=', 'c.id')
                                ->join('contacts_cstm as cc', 'c.id', '=', 'cc.id_c')
                                ->where('ac.account_id', $account->id)
                                ->where('cc.ic_no_c', trim($obj->identification_no))
                                ->select('a.name as acc_name', 'a.id as acc_id')
                                ->addSelect('c.first_name as contact_name', 'c.id as contact_id', 'c.identity_no_nextgen', 'c.user_id_nextgen');
                            $contactOldRecord = $query->first();
                            
                            if ($contactOldRecord == null) {
                                Log::info('     Checking No Contact Found as Existing');
                    
                                if ($obj->uo_record_status == '1') {
                                    $contactObj = CRMService::createContactGovernment($account, $obj, $roles_name);
                                    Log::info('          :: ->  success create ' . $contactObj->id . ' RECORD_STATUS: ' . $contactObj->record_status_nextgen);
                                } else {
                                    Log::info('          :: ->  No need create this user . This user is belong Organization InActive');
                                }
                            }else{
                                Log::info('     Checking Contact Found as Existing :: '.$contactOldRecord->contact_id);
                                $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contactOldRecord);
                                CRMService::saveEmailContact($obj->email, $contactOldRecord->contact_id);
                            }
                            
                        }
                    } else {
                        //update it
                        Log::info('     found contact  :: '.$contact->contact_id);
                        $contactObj = CRMService::saveContactGovernment($obj, $roles_name, $contact);
                        CRMService::saveEmailContact($obj->email, $contact->contact_id);
                    }
                }
            }
            $contacts = DB::table('contacts')->where('login_id_nextgen', $loginID)->first();
            if($contacts == null){
                $contacts = DB::table('contacts')->where('identity_no_nextgen', $loginID)->first();
            }
            return Response::json($contacts);
        }
        
        return null;
    }
    
    public function getListSmStuckTaskByJson(){
        $list =  collect($this->getFailedTaskApplNoInitiate());
        return $list->pluck('appl_no');
    }

}
