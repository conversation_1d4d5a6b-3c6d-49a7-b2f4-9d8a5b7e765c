@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianformparameter" action="{{url('/find/userlogin')}}" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="login_id" name="login_id" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')

@if (Auth::user())
@if($result != null && count($result) > 0 )
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Login ID<br>
            <small>Semakan Login ID pengguna</small>
        </h1>
    </div>
</div>    
<div class="block block-alt-noborder full">
    <div class="row">
        <div class="col-lg-6">
            
                <div class="col-lg-6">
                    <div class="block">
                            <div class="block-title">
                                <h2><strong>{{ $result['userdata']->user_name }}</strong></h2>
                            </div>
                            <address>
                                <strong>User ID : </strong>{{$result['userdata']->user_id}}<br/>
                                <strong>User Type ID : </strong>{{$result['userdata']->org_type_id}}<br/>
                                <strong>Login ID : </strong>{{$result['userdata']->login_id}}<br/>
                                <strong>Identification No. : </strong>{{$result['userdata']->identification_no}}<br/>
                                <strong>Email : </strong>{{$result['userdata']->email}}<br/>
                                <strong>Record Status : </strong>{{$result['userdata']->record_status}}<br/>
                                <strong>Mobile No. : </strong>{{$result['userdata']->mobile_country}}{{$result['userdata']->mobile_area}}{{$result['userdata']->mobile_no}}<br/>
                                <strong>Last Login Date : </strong>{{$result['userdata']->login_date}}<br/>
                                @if( Auth::user()->isAdvRolesEp() ) 
                                <strong>User Created Date : </strong>{{$result['userdata']->created_date}}<br/>
                                <strong>User Changed Date : </strong>{{$result['userdata']->changed_date}}<br/>
                                <strong>eAduan Impersonate: </strong><a target="_blank" href="{{$result['userdata']->eaduan_login}}" >Link eAduan</a><br/>
                                @endif
                            </address>
                    </div>
                </div>
                @if( Auth::user()->isAdvRolesEp() && $result['usersso'] != null ) 

                <div class="col-lg-6">
                    <div class="block">
                            <div class="block-title">
                                <h2><strong>SSO Details</strong></h2>
                            </div>
                            <address>
                            <strong>User key : </strong>{{$result['usersso']->usr_key}}<br/>
                            <strong>User login : </strong>{{$result['usersso']->usr_login}}<br/>
                            <strong>User email : </strong>{{$result['usersso']->usr_email}}<br/>
                            <strong>User first name : </strong>{{$result['usersso']->usr_first_name}}<br/>
                            <strong>User last name : </strong>{{$result['usersso']->usr_last_name}}<br/>
                            <strong>User status : </strong>{{$result['usersso']->usr_status}}<br/>
                            <strong>User pwd creation date : </strong>{{$result['usersso']->usr_pwd_creation_date}}<br/>
                            <strong>User pwd expire date : </strong>{{$result['usersso']->usr_pwd_expire_date}}<br/>
                            <strong>User pwd expired : </strong>{{$result['usersso']->usr_pwd_expired}}<br/>
                            <strong>User locked : </strong>{{$result['usersso']->usr_locked}}<br/>
                            <strong>User change pwd at next logon : </strong>{{$result['usersso']->usr_change_pwd_at_next_logon}}<br/>
                            <strong>User lockout duration : </strong>{{$result['usersso']->usr_lockout_duration}}<br/>
                            <strong>User Update : </strong>{{$result['usersso']->usr_update}}<br/>
                            <strong>User pwd warn date : </strong>{{$result['usersso']->usr_pwd_warn_date}}<br/>
                            <strong>User pwd warned : </strong>{{$result['usersso']->usr_pwd_warned}}<br/>
                            <strong>User pwd min age date : </strong>{{$result['usersso']->usr_pwd_min_age_date}}<br/>
                            </address>
                    </div>
                </div>
                @endif
            
        </div>
        <div class="col-lg-6">
            <div class="row">
                <div class="col-lg-12">
                    @if($organisasi != null)
                    <div class="block">
                        <!-- Block Title -->
                        <div class="block-title">
                            <h2><strong>{{ $organisasi->nama_organisasi }}</strong></h2>
                        </div>
                        <address>
                            <strong>Org. Profile ID: </strong>{{ $organisasi->org_profile_id }}<br/>
                            <strong>@if($result['userdata']->org_type_id == 15) SSM No. @else Organization Profile Id @endif</strong>{{ $organisasi->ssm_no_org_profile_id }}<br/>
                            <strong>@if($result['userdata']->org_type_id == 15) EP No. @else Organization Code @endif : </strong>{{ $organisasi->ep_no_org_code }}<br/>
                            <strong>Organization Type : </strong>{{ $result['userdata']->code_name }}<br/>
                            <strong>Record Status : </strong>{{ $organisasi->record_status }}<br/>
                            <strong>@if($result['userdata']->org_type_id == 15) Establish Date @else Effective Date @endif : </strong>{{ $organisasi->eff_establish_date }}<br/>
                        </address>
                        <!-- END Block Content -->
                    </div>
                    @endif
                </div>
                
             
                <div class="col-lg-12">
                    <div class="block">
                        <!-- Block Title -->
                        <div class="block-title">
                            <h2><strong>Checking GPKI User</strong>
                                <a href="javascript:void(0)" id="dash_GpkiSigning_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Checking Last Signing"><i class="fa fa-refresh"></i></a>
                            </h2>
                        </div>
                        <address>
                            <strong>User require GPKI ?  :  </strong>{{ App\Services\EPService::$YES_NO[$isGpkiValid]  }}<br/>
                            <div id="dash_GpkiSigning">
                                <strong>Last Successful Sign-in  :  </strong>Please click refresh button to check GPKI Sign-in<br/>
                            </div>
                        </address>
                        <!-- END Block Content -->

                    </div>
                </div class="col-lg-12">
                
            </div>
            
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block">
                <!-- Latest Title -->
                <div class="block-title">
                    <h2><strong>Status User Login</strong>
                    @if($result['userdata'] != null)
                        @if(Auth::user()->isPatcherRolesEp())
                        <a class="btn btn-warning btn-xs" href="{{url('/find/user-sync-role')}}/{{$result['userdata']->login_id}}" target="_blank">
                                Sync Role</a>
                        @endif
                    @endif
                    </h2>
                </div>
                <!-- END  Title -->
                @if(isset($result['userstatus']) && $result['userstatus']['status'] === 'Success' )
                <table class="table table-borderless table-striped table-vcenter table-bordered">
                    <tbody>
                        <tr>
                            <th>SSO Status</th>
                            <th>SSO Roles</th>
                            <th>Liferay Status</th>
                            <th>Liferay Roles</th>
                            <th>Ep Status</th>
                            <th>Ep Roles</th>
                        </tr>
                        <tr>
                            <td >{{$result['userstatus']['result']['OimStatus']}}</td>
                            <td >{{$result['userstatus']['result']['OimRole']}}</td>
                            <td >{{$result['userstatus']['result']['LiferayStatus']}}</td>
                            <td >{{$result['userstatus']['result']['LiferayRole']}}</td>
                            <td >{{$result['userstatus']['result']['PmStatus']}}</td>
                            <td >{{$result['userstatus']['result']['PmRole']}}</td>
                        </tr>

                    </tbody>
                </table>
                @endif
                <!-- END Latest  Content -->
            </div>
        </div>

        @if( Auth::user()->isAdvRolesEp() ) 
        @if(isset($result['userstatus']) && $result['userstatus']['status'] === 'Success' && 
            ( count($result['MissingRoleOim']) > 0 || $result['MissingRoleLiferay'] > 0 ) 
             )
        <div class="col-lg-12">
            <div class="block">
                <!-- Latest Title -->
                <div class="block-title">
                    <h2 class="text-danger"><strong>Roles Missing (This roles need to be fix) <i class="fa fa-exclamation-triangle"></i></strong>
                    </h2>
                </div>
                <!-- END  Title -->

                <table class="table table-borderless table-striped table-vcenter table-bordered">
                    <tbody>
                        <tr>
                        <th>Missing Roles In SSO</th>
                        <th>Missing Roles In Liferay</th>
                        </tr>
                        <tr>
                            <td >{{json_encode($result['MissingRoleOim'])}}</td>
                            <td >{{json_encode($result['MissingRoleLiferay'])}}</td>
                        </tr>

                    </tbody>
                </table>
                <!-- END Latest  Content -->
            </div>
        </div>
        @endif
        @endif
        <!-- Add this section after the Missing Roles section -->
        @if( Auth::user()->isAdvRolesEp() ) 
        @if(isset($result['ExtraRoleOim']) || isset($result['ExtraRoleLiferay'])
        && isset($result['userstatus']) && $result['userstatus']['status'] === 'Success' )
        <div class="col-lg-12">
            <div class="block">
                <!-- Extra Roles Title -->
                <div class="block-title">
                    <h2 class="text-info"><strong>Extra Roles (Additional roles in systems) <i class="fa fa-info-circle"></i></strong>
                    </h2>
                </div>
                <!-- END Title -->

                <table class="table table-borderless table-striped table-vcenter table-bordered">
                    <tbody>
                        <tr>
                        <th>Extra Roles In SSO</th>
                        <th>Extra Roles In Liferay</th>
                        </tr>
                        <tr>
                            <td>{{json_encode($result['ExtraRoleOim'] ?? [])}}</td>
                            <td>{{json_encode($result['ExtraRoleLiferay'] ?? [])}}</td>
                        </tr>
                    </tbody>
                </table>
                <!-- END Content -->
            </div>
        </div>
        @endif
        @endif
    </div>

    <div class="row">
        @if($history != null) 
        <div class="col-lg-12">
            <div class="block">
                <div class="block-title">
                    <h2><strong>Organization History</strong></h2>
                </div>

                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th>User Org Id</th>
                                <th>Organization Code</th>
                                <th>Organization Name</th>
                                <th>Org Status</th>
                                <th>Org Profile Record Status</th>
                                <th>Org Created Date</th>
                                <th>Org Changed Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($history as $history)
                            <tr>
                                <td >{{ $history->user_org_id }}</td>
                                <td >@if($result['userdata']->org_type_id == 15) <a target="_blank" href="{{url('find/epno/')}}/{{ $history->ep_no_org_code }}" >
                                        @else <a target="_blank" href="{{url('find/orgcode')}}/{{ $history->ep_no_org_code }}" > @endif{{ $history->ep_no_org_code }}</td>
                                <td >{{ $history->nama_organisasi }} </td>
                                <td >{{ $history->org_validity_record_status }}</td>
                                <td >{{ $history->record_status }}</td>
                                <td >{{ $history->created_date }}</td>
                                <td >{{ $history->changed_date }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table> 
                </div>                
            </div>
        </div>
        @endif
    </div>

</div>
@elseif($result != null && count($result) > 0  && isset($result['userstatus']) && $result['userstatus']['status'] === 'Error')
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Login ID<br>
            <small>{{$result['userstatus']['result']}}</small>
        </h1>
    </div>
</div>
@else
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Login ID<br>
            <small>Tidak dijumpai! Sila Masukkan Login ID pada carian diatas...</small>
        </h1>
    </div>
</div>
@endif
@endif
@endsection

@section('jsprivate')

<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>

@if($result != null && count($result) > 0  && isset($result['userstatus']) && $result['userstatus']['status'] === 'Success')
<script>
    var USER_ID = {!!json_encode($result['userdata']->user_id)!!}
</script>    
@endif
<script>
    $("form#carianformparameter").on("submit", function () {
        var gourl = $(this).attr('action') + '?login_id=' + $('#cari').val();
        $(this).attr('action', gourl);
    });
    var APP_URL = {!!json_encode(url('/'))!!}
    
    //Softcert Signing (Submission QT) Monitoring
    $('#dash_GpkiSigning_refresh').on("click", function() {
            
            $.ajax({
                url: APP_URL + '/find/userlogin/gpki-check?user_id='+USER_ID,
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_GpkiSigning').hide().html($data).fadeIn();
                }
            });
        });
</script>
@endsection

