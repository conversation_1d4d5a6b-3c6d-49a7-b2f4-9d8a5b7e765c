<?php

namespace App\Console\Commands\SPKI;

use App\Migrate\AppSupport\SmResendSoftcert;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use Log;
use Mail;
use Config;

class HandleFailedSpkiRequestCertificaticeByOSB extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleFailedSpkiRequestCertificaticeByOSB';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Trigger resend back service request certificate SPKI after failed by OSB';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(__METHOD__. ' starting ..', ['Date' => Carbon::now()]);
        try {
            SmResendSoftcert::triggerFailedOSBRequestCertificate();
            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__. '>> error happen!! ' . $exc->getTraceAsString());
            self::endErrorEmail(json_encode($exc->getTraceAsString()));
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected static function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'EPSS :: Server (' . env('APP_ENV') . ') - Error: HandleFailedSpkiRequestCertificaticeByOSB'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__.' Error >> '.json_encode( ['Email' => $data["to"], 'ERROR' => $e->getMessage()] ));
            return $e;
        }
    }
}
