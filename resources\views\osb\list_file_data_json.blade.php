<!-- List JSON Data -->
            
<div class="block-title">
    <h2><i class="fa fa-file-text-o"></i> <strong>List File Data In </strong> >> {{$objFile->file_name}}</h2>
    <div class="block-options pull-right">
        <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
            onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
    </div>
</div>
<div class="row" >
    <div class="col-sm-12">
        @if($listDataJson != null && count($listDataJson) > 0)
        <div class="table-responsive" style="height:600px">
            <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered" >
                @foreach ($listDataJson as $header)
                @if(@$loop->index == 0)
                <thead>
                    <tr style="background-color: #f2f2f2"><td colspan="{{ count($header->data) }}"><b>{{ $header->name }}</b></td></tr>
                    <tr>
                        @foreach ($header->data as $data)
                            <th class="text-center">{{ $data->key }}</th>
                        @endforeach
                    </tr>
                </thead>
                @endif
                <tbody style="border-bottom-style: outset;">
                    <tr>
                        @foreach ($header->data as $data)
                            <td class="text-center">{{ $data->value }}</td>
                        @endforeach
                    </tr>
                    <tr>
                    @if(isset($header->list))
                    <td colspan="{{ count($header->data) }}">@include('osb.manage_child',['childs' => $header->list])</td>
                    @endif
                    </tr>
                </tbody>
                @endforeach
            </table>
        </div>
        @endif
    </div>
</div>                   