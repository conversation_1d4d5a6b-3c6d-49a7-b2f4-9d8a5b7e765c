<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait FulfilmentService
{

    protected function getSupplierInfoByDocNo($docNo)
    {
        $type = substr($docNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');
        $query->join('SM_SUPPLIER as C', 'C.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as D', 'D.SUPPLIER_ID', '=', 'C.SUPPLIER_ID');
        $query->leftJoin('PM_USER as E', 'E.USER_ID', '=', 'A.CREATED_BY');
        $query->leftJoin('PM_USER as F', 'F.USER_ID', '=', 'A.CHANGED_BY');
        if ($type == 'PR' || $type == 'CR') {
            $query->where('A.DOC_NO', $docNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('B.DOC_NO', $docNo);
        }
        $query->select('A.DOC_NO AS FR_DOC_NO', 'A.DOC_TYPE AS FR_DOC_TYPE', 'A.AG_OFFICE_NAME', 'B.DOC_NO AS FO_DOC_NO', 'B.DOC_TYPE AS FO_DOC_TYPE', 'B.SAP_ORDER_NO');
        $query->addSelect('C.*');
        $query->addSelect('D.MOF_NO');
        $query->addSelect('E.USER_ID AS CREATED_USER_ID', 'E.LOGIN_ID AS CREATED_LOGIN_ID', 'F.USER_ID AS CHANGED_USER_ID', 'F.LOGIN_ID AS CHANGED_LOGIN_ID');
        return $query->first();
    }

    protected function getListTransactionPRCRBySupplierId($supplierId, $year)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->join('FL_SUPPLIER_DTL as D', 'D.FULFILMENT_REQ_ID', '=', 'A.FULFILMENT_REQ_ID');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');
        $query->join('SM_SUPPLIER as C', 'C.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->where('C.SUPPLIER_ID', $supplierId);
        $query->where('A.FINANCIAL_YEAR', $year);
        $query->select('A.DOC_NO AS FR_DOC_NO', 'A.DOC_TYPE AS FR_DOC_TYPE', 'B.DOC_NO AS FO_DOC_NO', 'B.DOC_TYPE AS FO_DOC_TYPE', 'B.SAP_ORDER_NO', 'B.FULFILMENT_ORDER_ID');
        $query->addSelect('A.*');
        $query->addSelect('D.MOF_NO', 'D.BRANCH_NAME', 'D.BRANCH_CODE');
        $query->addSelect(DB::RAW("( select w.status_id||' - '||d.status_name as status_prcr 
                    from  FL_WORKFLOW_STATUS w, PM_STATUS_DESC d
                    where w.status_id = d.status_id
                    and d.language_code = 'en'
                    and w.is_current = 1
                    and w.doc_type = A.DOC_TYPE
                    and w.doc_id =  A.FULFILMENT_REQ_ID ) as status_prcr"));
        $query->addSelect(DB::RAW("( select w.status_id||' - '||d.status_name as status_poco 
                    from  FL_WORKFLOW_STATUS w, PM_STATUS_DESC d
                    where w.status_id = d.status_id
                    and d.language_code = 'en'
                    and w.is_current = 1
                    and w.doc_type = B.DOC_TYPE
                    and w.doc_id =  B.FULFILMENT_ORDER_ID ) as status_poco"));
        $query->orderBy('A.CREATED_DATE', 'desc');
        $query->take(100);
        return $query->get();
    }

    protected function getPtjInfoByDocNo($docNo, $ptjField)
    {
        $type = substr($docNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as A');
        $query->leftJoin('FL_FULFILMENT_ORDER as B', 'A.FULFILMENT_REQ_ID', '=', 'B.FULFILMENT_REQ_ID');

        if ($ptjField == 'PREPARED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.PREPARED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'ISSUED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.ISSUED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'CREATED_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.CREATED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else if ($ptjField == 'CHARGE_PTJ') {
            $query->join('PM_ORG_PROFILE as C', 'A.CHARGE_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        } else {
            $query->join('PM_ORG_PROFILE as C', 'A.CREATED_ORG_PROFILE_ID', '=', 'C.ORG_PROFILE_ID');
        }

        $query->join('PM_ORG_VALIDITY as D', 'C.ORG_PROFILE_ID', '=', 'D.ORG_PROFILE_ID');

        $query->where('C.RECORD_STATUS', 1);
        $query->where('D.RECORD_STATUS', 1);
        if ($type == 'PR' || $type == 'CR') {
            $query->where('A.DOC_NO', $docNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('B.DOC_NO', $docNo);
        }
        $query->select('D.*');
        $query->addSelect('ORG_TYPE_ID', 'PARENT_ORG_PROFILE_ID');
        return $query->first();
    }

    /**
     * Get Document PR and PO or Document CR and CO.
     * 
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getDocNoPRPOorCRCO($docNo)
    {

        $type = substr($docNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')->table('FL_FULFILMENT_REQUEST as a');
        $query->leftJoin('FL_FULFILMENT_ORDER as b', 'a.FULFILMENT_REQ_ID', '=', 'b.FULFILMENT_REQ_ID');
        if ($type == 'PR' || $type == 'CR') {
            $query->where('a.DOC_NO', $docNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('b.DOC_NO', $docNo);
        }
        $query->select('A.DOC_NO AS FR_DOC_NO', 'A.DOC_TYPE AS FR_DOC_TYPE', 'B.DOC_NO AS FO_DOC_NO', 'B.DOC_TYPE AS FO_DOC_TYPE');
        return $query->first();
    }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getDocNoByPrCr($docNo)
    {
        $data = $this->getDocNoPRPOorCRCO($docNo);
        if ($data != null) {
            return $data->fo_doc_no;
        }
        return null;
    }

    protected function getListPurchaseInquiryByPiNoOther($piNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_PURCHASE_INQUIRY as a');
        $query->join('SC_WORKFLOW_STATUS as b', 'a.PURCHASE_INQUIRY_ID', '=', 'b.DOC_ID');
        $query->join('PM_STATUS as c', 'b.STATUS_ID', '=', 'c.STATUS_ID');
        $query->join('PM_STATUS_DESC as d', 'c.STATUS_ID', '=', 'd.STATUS_ID');
        $query->where('d.LANGUAGE_CODE', 'en');
        $query->where('b.DOC_TYPE', 'PI');
        $query->where('a.PURCHASE_INQUIRY_NO', $piNo);
        $query->select(DB::raw("TO_CHAR (b.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') as fws_date_created"));
        $query->addSelect('a.PURCHASE_INQUIRY_ID,', 'a.PURCHASE_INQUIRY_NO', 'a.DATE_TO_RESPONSE');
        $query->addSelect('b.DOC_ID,', 'd.STATUS_NAME', 'c.STATUS_ID');
        $query->addSelect('b.IS_CURRENT,', 'b.CREATED_BY', 'b.CHANGED_BY');
        $query->orderBy('b.CREATED_DATE', 'desc');
        return $query->get();
    }

    protected function getListPidByPdNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE , A.PID_NO AS DOC_NO,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY, 
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_PID  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PID_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?  
                    AND A.PID_NO  =  ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PD', $docNo)
        );
        return $results;
    }

    protected function getListPurchaseInquiryByPiNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE ,
                    A.PURCHASE_INQUIRY_NO AS DOC_NO,A.DATE_TO_RESPONSE, 
                    A.TITLE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_PURCHASE_INQUIRY  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PURCHASE_INQUIRY_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PURCHASE_INQUIRY_NO = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PI', $docNo)
        );
        return $results;
    }

    protected function getListSimpleQuoteBySqNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.QUOTE_NO as DOC_NO ,A.IS_PANEL,  A.END_DATE,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_QUOTE  A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.QUOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE =?
                    AND B.DOC_TYPE = ?
                    AND A.QUOTE_NO = ?
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'SQ', $docNo)
        );
        return $results;
    }

    protected function getListRequestNoteByRnNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.REQUEST_NOTE_NO AS DOC_NO,A.SOURCE_METHOD,A.APPROVER_ID,A.USER_GROUP_ID, A.CHANGED_BY RN_CHANGE,A.CREATED_BY RN_CREATED ,
                    D.STATUS_NAME ,C.STATUS_ID ,B.IS_CURRENT,B.CREATED_BY,B.CHANGED_BY ,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_REQUEST_NOTE A,SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.REQUEST_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ?
                    AND A.REQUEST_NOTE_NO = ?
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'RN', $docNo)
        );
        return $results;
    }

    protected function getListFulfilmenRequestByPrCr($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    TO_CHAR (B.CHANGED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CHANGED,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,(SELECT DOC_NO FROM FL_FULFILMENT_ORDER O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_CO,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.CREATED_BY, A.CHANGED_BY,A.APPROVER_ID, A.CONTRACT_ID , A.AG_APPROVED_DATE , A.CREATED_ORG_PROFILE_ID,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_REQUEST A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_REQ_ID = B.DOC_ID (+)
                    AND B.STATUS_ID = C.STATUS_ID (+)
                    AND C.STATUS_ID = D.STATUS_ID (+)
                    AND E.USER_ID (+) = A.CREATED_BY
                    AND F.USER_ID (+) = A.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('CR','PR')
                    AND A.DOC_NO = ?
                    -- AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListFulfilmenOrderByPoCoFc($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,A.DOC_NO,
                     (SELECT DOC_NO FROM FL_FULFILMENT_REQUEST O WHERE O.FULFILMENT_REQ_ID  = A.FULFILMENT_REQ_ID) AS PO_NO,A.FULFILMENT_REQ_ID ,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE IN ('PO','CO','FC')   
                    AND A.DOC_NO  = ?
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListDeliveryOrderByDoNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DELIVERY_ORDER_NO AS DOC_NO,A.SUPPLIER_DO_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_DELIVERY_ORDER A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.DELIVERY_ORDER_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ?    
                    AND A.DELIVERY_ORDER_NO  = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY   B.CREATED_DATE DESC",
            array('en', 'DO', $docNo)
        );
        return $results;
    }

    protected function getListFulfilmentNoteByFnNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.FULFILMENT_NOTE_NO AS DOC_NO, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_FULFILMENT_NOTE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.FULFILMENT_NOTE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.FULFILMENT_NOTE_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'FN', $docNo)
        );
        return $results;
    }

    protected function getListQuotationTenderByQtNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.QT_ID,A.IS_PANEL,
                    A.QT_NO AS DOC_NO, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SC_QT A, SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.QT_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE not in 'SQ' 
                    AND A.QT_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListLetterAcceptanceByLoaNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                B.DOC_ID,B.DOC_TYPE,
                A.LOA_ID,
                A.LOA_NO AS DOC_NO, 
                D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                FROM SC_LOA A, SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                WHERE A.LOA_ID = B.DOC_ID
                AND B.STATUS_ID = C.STATUS_ID
                AND C.STATUS_ID = D.STATUS_ID
                AND E.USER_ID (+) = B.CREATED_BY
                AND F.USER_ID (+) = B.CHANGED_BY
                AND D.LANGUAGE_CODE = ?
                AND B.DOC_TYPE in 'LA' 
                AND A.LOA_NO = ? 
                ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListSMByApplNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.APPL_ID,A.SUPPLIER_TYPE,A.APPL_TYPE,
                    A.APPL_NO AS DOC_NO,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM SM_APPL A, SM_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.APPL_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND A.APPL_NO = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListStopInstructionBySdNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR(B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.STOP_INSTR_NO as doc_no,A.FULFILMENT_REQ_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_STOP_INSTR A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.STOP_INSTR_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ?
                    AND B.DOC_TYPE = ? 
                    AND A.STOP_INSTR_NO = ?  
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'SD', $docNo)
        );
        return $results;
    }

    protected function getListApplicationNoByApNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT TO_CHAR(B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                B.DOC_ID,B.DOC_TYPE,
                A.APPL_NO as doc_no,A.ACCESS_APPL_ID,
                D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
                decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                FROM PM_ACCESS_APPL A, PM_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                WHERE A.ACCESS_APPL_ID = B.DOC_ID
                AND B.STATUS_ID = C.STATUS_ID
                AND C.STATUS_ID = D.STATUS_ID
                AND E.USER_ID (+) = B.CREATED_BY
                AND F.USER_ID (+) = B.CHANGED_BY
                AND D.LANGUAGE_CODE = ?
                AND B.DOC_TYPE IN ('MR','CP') 
                AND A.APPL_NO = ? 
                ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }


    protected function getListInvoiceByInvNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.INVOICE_NO AS DOC_NO,A.SUPPLIER_INVOICE_REF, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT, A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY  , A.*,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_INVOICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.INVOICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.INVOICE_NO = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'IN', $docNo)
        );
        return $results;
    }

    protected function getListAdjustmentByDocNo($docNo)
    {
        // C0251062601070003 B0381201011100015
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.DOC_NO,FULFILMENT_ORDER_ID, A.FULFILMENT_REQ_ID, 
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_ADJUSTMENT A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.ADJUSTMENT_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE IN ('CN','DN')
                    AND A.DOC_NO  = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', $docNo)
        );
        return $results;
    }

    protected function getListPaymentAdviseByPaNo($docNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
                    B.DOC_ID,B.DOC_TYPE,
                    A.PAYMENT_ADVICE_NO AS DOC_NO,
                    A.PAYMENT_REF_NO, A.FULFILMENT_REQ_ID,INVOICE_ID, A.FULFILMENT_ORDER_ID,
                    D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT , A.RECORD_STATUS,  B.CREATED_BY,B.CHANGED_BY,
                    decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
                    decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
                    decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
                    FROM FL_PAYMENT_ADVICE A, FL_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
                    WHERE A.PAYMENT_ADVICE_ID = B.DOC_ID
                    AND B.STATUS_ID = C.STATUS_ID
                    AND C.STATUS_ID = D.STATUS_ID
                    AND E.USER_ID (+) = B.CREATED_BY
                    AND F.USER_ID (+) = B.CHANGED_BY
                    AND D.LANGUAGE_CODE = ? 
                    AND B.DOC_TYPE = ? 
                    AND A.PAYMENT_ADVICE_NO = ? 
                    --AND TO_CHAR (A.CREATED_DATE, 'YYYY') = ? 
                    ORDER BY B.CREATED_DATE DESC",
            array('en', 'PA', $docNo)
        );
        return $results;
    }

    /**
     * Checking on SQ, RN, PR, PO
     * Return  List
     * Can be one RN have many PR
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentSQPOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "  SELECT DISTINCT q.QUOTE_ID, q.QUOTE_NO, 
                    rn.REQUEST_NOTE_ID, rn.REQUEST_NOTE_NO, rn.SOURCE_METHOD, 
                    rnd.PURCHASE_REQUEST_ID, rnd.FULFILMENT_TYPE_ID,
                    fr.FULFILMENT_REQ_ID, fr.DOC_TYPE as FR_DOC_TYPE, fr.DOC_NO as FR_DOC_NO,
                    fo.DOC_NO as FO_DOC_NO,fo.DOC_TYPE as FO_DOC_TYPE,fo.FULFILMENT_ORDER_ID 
                  FROM SC_QUOTE q, SC_REQUEST_NOTE rn, SC_REQUEST_NOTE_DTL rnd, FL_FULFILMENT_REQUEST fr, FL_FULFILMENT_ORDER fo  
                  WHERE 
                    q.QUOTE_ID = rn.QUOTE_ID (+)  
                    AND rn.REQUEST_NOTE_ID = rnd.REQUEST_NOTE_ID(+) 
                    AND rnd.PURCHASE_REQUEST_ID = fr.PURCHASE_REQUEST_ID(+)
                    AND fr.FULFILMENT_REQ_ID = fo.FULFILMENT_REQ_ID(+) 
                ";
        if ($type == 'SQ') {
            $query = $query . "
                    AND q.QUOTE_NO = ?
                    ";
        } else if ($type == 'RN') {
            $query = $query . "
                    AND rn.REQUEST_NOTE_NO = ?
                    ";
        } else if ($type == 'PR') {
            $query = $query . "
                    AND fr.DOC_NO = ?
                    ";
        } else if ($type == 'PO') {
            $query = $query . "
                    AND fo.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    /**
     * Checking on SQ, RN, LOA, CT, CR, CO
     * Return  List
     * Can be one RN have many PR
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentSQCOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "  SELECT DISTINCT Q.QUOTE_ID, Q.QUOTE_NO, 
                        RN.REQUEST_NOTE_ID, RN.REQUEST_NOTE_NO, RN.SOURCE_METHOD, 
                        RND.FULFILMENT_TYPE_ID,RND.PURCHASE_REQUEST_ID, 
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_QUOTE Q 
                                  LEFT JOIN SC_REQUEST_NOTE RN ON Q.QUOTE_ID = RN.QUOTE_ID
                                  LEFT JOIN SC_REQUEST_NOTE_DTL RND ON RN.REQUEST_NOTE_ID = RND.REQUEST_NOTE_ID
                                  LEFT JOIN SC_LOI_LOA LOI ON RND.PURCHASE_REQUEST_ID = LOI.DOC_ID
                                  LEFT JOIN SC_LOA LOA ON LOI.LOI_LOA_ID = LOA.LOI_LOA_ID
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID
                            WHERE 
                ";
        if ($type == 'SQ') {
            $query = $query . "
                     Q.QUOTE_NO = ?
                    ";
        } else if ($type == 'RN') {
            $query = $query . "
                     RN.REQUEST_NOTE_NO = ?
                    ";
        } else if ($type == 'LA') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'CT') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    /**
     * Checking on QT, LOA, CT, CR, CO
     * Return  List
     * 
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentQTCOTracking($docNo)
    {

        $type = substr($docNo, 0, 1);
        $type2 = substr($docNo, 0, 2);


        $query = " SELECT DISTINCT 
                        QT.QT_NO AS QT_DOC_NO ,  
                        LOI.DOC_TYPE AS LOA_DOC_TYPE, LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                    FROM SC_QT QT  
                    LEFT JOIN  SC_LOI_LOA LOI ON LOI.DOC_ID  = QT.QT_ID 
                    LEFT JOIN SC_LOA LOA ON  LOA.LOI_LOA_ID = LOI.LOI_LOA_ID 
                    LEFT JOIN CT_CONTRACT CT ON CT.LOA_ID = LOA.LOA_ID  
                    LEFT JOIN FL_FULFILMENT_REQUEST FR ON  FR.CONTRACT_ID = CT.CONTRACT_ID 
                    LEFT JOIN FL_FULFILMENT_ORDER FO ON FO.FULFILMENT_REQ_ID = FR.FULFILMENT_REQ_ID  
                    WHERE 
                ";
        if ($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'C' || $type == 'Z' || $type == 'M') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type2 == 'DL' || $type2 == 'QT' || $type2 == 'QM') {
            $query = $query . "
                     QT.QT_NO = ?
                    ";
        } else if ($type2 == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type2 == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    /**
     * Checking on PR,CR,PO,CO
     * Return  List
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentPRCRPOCOTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "   SELECT DISTINCT 
                            FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , 
                            FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                           FROM FL_FULFILMENT_REQUEST FR 
                      LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID 
                      WHERE 
                ";

        if ($type == 'CR' || $type == 'PR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type == 'PO' || $type == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    /**
     * Checking on LOA,CT,CR,CO
     * Return  List
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoFulfillmentLOACTCRCOTracking($docNo)
    {

        $type = substr($docNo, 0, 1);
        $type2 = substr($docNo, 0, 2);
        $query = "    SELECT DISTINCT 
                        LOA.LOA_NO AS LOA_DOC_NO, 
                        CT.CONTRACT_NO AS CT_DOC_NO,
                        FR.DOC_NO AS FR_DOC_NO, FR.DOC_TYPE AS FR_DOC_TYPE , FR.CREATED_DATE , 
                        FO.DOC_NO AS FO_DOC_NO, FO.DOC_TYPE AS FO_DOC_TYPE 
                             FROM SC_LOA LOA 
                                  LEFT JOIN CT_CONTRACT CT ON LOA.LOA_ID = CT.LOA_ID 
                                  LEFT JOIN FL_FULFILMENT_REQUEST FR ON CT.CONTRACT_ID = FR.CONTRACT_ID 
                                  LEFT JOIN FL_FULFILMENT_ORDER FO ON FR.FULFILMENT_REQ_ID = FO.FULFILMENT_REQ_ID  
                        WHERE 
                ";

        if ($type == 'N' || $type == 'L' || $type == 'W' || $type == 'A') {
            $query = $query . "
                     LOA.LOA_NO = ?
                    ";
        } else if ($type == 'C' || $type == 'Z' || $type == 'M') {
            $query = $query . "
                     CT.CONTRACT_NO = ?
                    ";
        } else if ($type2 == 'CR') {
            $query = $query . "
                     FR.DOC_NO = ?
                    ";
        } else if ($type2 == 'CO') {
            $query = $query . "
                     FO.DOC_NO = ?
                    ";
        } else {
            return array();
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        //dd($results);
        return $results;
    }

    /**
     * Checking on AP
     * Return  List
     * @param type $docNo
     * @return type
     */
    protected function getListDocNoMarketResearchTracking($docNo)
    {

        $type = substr($docNo, 0, 2);


        $query = "   SELECT DISTINCT 
                            A.APPL_NO AS AP_DOC_NO 
                           FROM PM_ACCESS_APPL A 
                      WHERE 
                       A.APPL_NO = ? 
                ";

        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    /**
     * Get Group ID by Doc No (PR)
     * @param type $docNo
     * @return type
     */
    protected function getGroupIdTrackingDiary($docNo)
    {
        $objResult = DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY')
            ->where('DOC_NO', $docNo)
            ->whereNotNull('GROUP_ID')
            ->first();
        return $objResult;
    }

    /**
     * Get Group ID by Doc No (PR)
     * @param type $data
     * @return type
     */
    protected function getListTrackingDiary($data)
    {

        $collectResult = collect();
        foreach ($data["list_doc_no"] as $docNo) {
            $listDataTrcDiary = $this->getListTrackingDiaryByDocNo($docNo);
            foreach ($listDataTrcDiary as $res) {
                $collectResult->push($res);
            }
        }

        foreach ($data["list_group_id"] as $groupId) {
            $listTrcDiary = $this->getListTrackingDiaryByGroupId($groupId);
            foreach ($listTrcDiary as $res) {
                $collectResult->push($res);
            }
        }

        return $collectResult->unique();
    }

    protected function getListTrackingDiaryByDocNo($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('td.DOC_NO', $docNo)
            ->select('td.*', 'sd.STATUS_NAME')
            ->get();
    }

    protected function getListTrackingDiaryByGroupId($groupId)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('td.GROUP_ID', $groupId)
            ->select('td.*', 'sd.STATUS_NAME')
            ->get();
    }

    protected function getLatestTrackingDiaryDetail($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('td.DOC_NO', $docNo)
            ->select('td.*', 'sd.STATUS_NAME')
            ->orderBy('tracking_diary_id', 'desc')
            ->first();
    }

    /** Maximum retrieve 1000 records only */
    protected function getListTrackingDiaryByUser($loginId, $dateFrom, $dateTo)
    {
        if (strlen($loginId) == 0 || strlen($dateFrom) == 0 || strlen($dateTo) == 0) {
            return array();
        }
        if ($dateFrom == $dateTo) {
            $dateToCrbn = Carbon::parse($dateTo);
            $dateTo = $dateToCrbn->addDay()->format('Y-m-d');
        }
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_USER pu', 'pu.user_id', '=', 'td.actioned_by')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->where('pu.login_id', $loginId)
            ->whereRaw("td.actioned_date BETWEEN to_date('$dateFrom','YYYY-MM-DD') AND to_date('$dateTo','YYYY-MM-DD')")
            ->select('td.*', 'sd.STATUS_NAME', 'pu.user_name', 'pu.login_id', 'pu.identification_no', 'pu.email')
            ->take(1000)
            ->get();
    }

    /**
     * Get Daily QT Published 
     * @param type $data
     * @return type
     */
    protected function getListQtPublished()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60007 
                           then qt.PUBLISH_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60007) and trunc(qt.publish_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    /**
     * Get List QT Published By DAY
     * @param type $data
     * @return type
     */
    protected function getListQtPublishedByDay()
    {
        $query = "  SELECT
                    TO_CHAR(qt.publish_date,'YYYY-MM-DD') as publish_date,
                    (SELECT status_name
                     FROM pm_status_desc
                     WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                    wf.status_id                                              AS QT_STATUS_ID,
                    count (* ) as total
                  FROM sc_qt qt
                    INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                  WHERE wf.STATUS_ID IN (60007) AND trunc(qt.publish_date) > sysdate
                  GROUP BY TO_CHAR(qt.publish_date,'YYYY-MM-DD'),wf.status_id
                  ORDER BY 1 asc
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    /**
     * Get List QT Published by Ministry 
     * @param type $data
     * @return type
     */
    protected function getListQtPublishedByMinistry($date)
    {
        $query = "  SELECT
                TO_CHAR(qt.publish_date, 'YYYY-MM-DD') as publish_date,
                (SELECT status_name
                 FROM pm_status_desc
                 WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                wf.status_id                                              AS QT_STATUS_ID,
                d.org_code                                                AS MINISTRY_CODE,
                d.org_name                                                AS MINISTRY_NAME,
                count(*)                                                  AS total
              FROM sc_qt qt, sc_workflow_status wf,
                pm_org_validity d,
                pm_org_profile w,
                pm_org_profile m,
                pm_org_profile j,
                pm_org_profile p
              WHERE p.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FPJ')              -- Ptj
                    AND j.org_profile_id = p.parent_org_profile_id
                    AND j.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FJA')          -- Jabatan
                    AND m.org_profile_id = j.parent_org_profile_id
                    AND w.org_profile_id = m.parent_org_profile_id
                    AND w.org_type_id IN (
                              SELECT parameter_id
                              FROM pm_parameter
                              WHERE parameter_type = 'OT'
                                    AND parameter_code = 'FMI')      -- Kementerian
                    AND d.org_profile_id = w.org_profile_id
                    AND qt.org_profile_id = p.org_profile_id
                    AND d.record_status = 1
                    AND qt.qt_id = wf.doc_id
                    AND wf.doc_type = 'QT' AND wf.is_current = 1
                    AND wf.STATUS_ID IN (60007) AND trunc(qt.publish_date) = TO_DATE(?, 'YYYY-MM-DD')
              GROUP BY TO_CHAR(qt.publish_date, 'YYYY-MM-DD'), wf.status_id, d.org_code, d.org_name
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($date));
    }

    /**
     * Get Daily QT Closing 
     * @param type $data
     * @return type
     */
    protected function getListQtClosing()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        , case when wf.status_id = 60009
                           then qt.CLOSING_DATE
                         end as QT_DATE
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60009) and trunc(qt.closing_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    /**
     * Get List QT Closing by DAY
     * @param type $data
     * @return type
     */
    protected function getListQtClosingByDay()
    {
        $query = "  SELECT
                    TO_CHAR(qt.closing_date,'YYYY-MM-DD')  as closing_date,
                    (SELECT status_name
                     FROM pm_status_desc
                     WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
                    wf.status_id                                              AS QT_STATUS_ID,
                    count (* ) as total
                  FROM sc_qt qt
                    INNER JOIN sc_workflow_status wf ON qt.qt_id = wf.doc_id AND wf.doc_type = 'QT' AND wf.is_current = 1
                  WHERE wf.STATUS_ID IN (60009) AND trunc(qt.closing_date) > sysdate
                  GROUP BY TO_CHAR(qt.closing_date,'YYYY-MM-DD'),wf.status_id
                  ORDER BY 1 asc
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    /**
     * Get List QT Closing by Ministry 
     * @param type $data
     * @return type
     */
    protected function getListQtClosingByMinistry($date)
    {
        $query = "  SELECT
            TO_CHAR(qt.closing_date, 'YYYY-MM-DD') as closing_date,
            (SELECT status_name
             FROM pm_status_desc
             WHERE status_id = wf.status_id AND language_code = 'en') AS QT_STATUS,
            wf.status_id                                              AS QT_STATUS_ID,
            d.org_code                                                AS MINISTRY_CODE,
            d.org_name                                                AS MINISTRY_NAME,
            count(*)                                                  AS total
          FROM sc_qt qt, sc_workflow_status wf,
                pm_org_validity d,
                pm_org_profile w,
                pm_org_profile m,
                pm_org_profile j,
                pm_org_profile p
              WHERE p.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FPJ')              -- Ptj
                    AND j.org_profile_id = p.parent_org_profile_id
                    AND j.org_type_id IN (
                SELECT parameter_id
                FROM pm_parameter
                WHERE parameter_type = 'OT'
                      AND parameter_code = 'FJA')          -- Jabatan
                    AND m.org_profile_id = j.parent_org_profile_id
                    AND w.org_profile_id = m.parent_org_profile_id
                    AND w.org_type_id IN (
                              SELECT parameter_id
                              FROM pm_parameter
                              WHERE parameter_type = 'OT'
                                    AND parameter_code = 'FMI')      -- Kementerian
                    AND d.org_profile_id = w.org_profile_id
                    AND qt.org_profile_id = p.org_profile_id
                    AND d.record_status = 1
                AND qt.qt_id = wf.doc_id
                AND wf.doc_type = 'QT' AND wf.is_current = 1
                AND wf.STATUS_ID IN (60009) AND trunc(qt.closing_date) = TO_DATE(?, 'YYYY-MM-DD')
          GROUP BY TO_CHAR(qt.closing_date, 'YYYY-MM-DD'), wf.status_id, d.org_code, d.org_name
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($date));
    }

    // 60035
    protected function getListQtPendingReschedulePublication()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60035) and trunc(qt.publish_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    // 60036
    protected function getListQtPendingRescheduleProposalClosingDate()
    {
        $query = "  select qt.qt_id,qt.qt_no
                        , (select status_name from pm_status_desc where status_id = wf.status_id and language_code = 'en') as QT_STATUS 
                        , wf.status_id as QT_STATUS_ID  
                        
                     from sc_qt qt
                   inner join sc_workflow_status wf on qt.qt_id = wf.doc_id and wf.doc_type = 'QT' and wf.is_current = 1
                   where wf.STATUS_ID IN (60036) and trunc(qt.closing_date) = trunc(sysdate)
                   order by 4, wf.STATUS_ID, qt.qt_no
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    /**
     * Get Information of Supplier Details and Contract
     * @param type $data
     * @return type
     */
    protected function getInfoSupplierAndContractByErrorGFM120($search)
    {
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT 
                    'Bank Pembekal tidak wujud di 1GFMAS' as ERROR_GFM120,
                    SYSDATE-1 as DATE_ERROR_HAPPEN,
                    A.DOC_TYPE as PRCR_DOC_TYPE, A.DOC_NO as PRCR_DOC_NO , 
                    B.DOC_TYPE as POCO_DOC_TYPE, B.DOC_NO as POCO_DOC_NO,
                    D.INVOICE_NO,
                    E.ORG_NAME as PTJ_NAME, E.ORG_CODE as PTJ_CODE, 
                    C.COMPANY_NAME,C.EP_NO ,
                    D.ACCOUNT_NO as ACCOUNT_NO_IN_INVOICE,D.PAYMENT_BANK_NAME AS BANK_NAME_IN_INVOICE ,
                    (select count(*) from OSB_LOGGING where service_code = 'GFM-010' AND REMARKS_3 = C.EP_NO  and ROWNUM < 2 )   as TOTAL_SEND_APIVE ,
                    (select CONCAT(a.ACCOUNT_NO, CONCAT( ' - ', b.BANK_CODE))  as BANK_PAYLOAD from SM_SUPPLIER_BANK a, PM_FINANCIAL_SVC b  
                    where  a.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID 
                    and a.appl_id = C.LATEST_APPL_ID  
                    and rev_no in (select max(rev_no) from SM_SUPPLIER_BANK  b where b.appl_id = a.appl_id) AND ROWNUM < 2 ) as LATEST_BANK_NO_IN_SUPPLIER
                    from FL_FULFILMENT_REQUEST A 
                  INNER JOIN FL_FULFILMENT_ORDER  B ON A.FULFILMENT_REQ_ID = B.FULFILMENT_REQ_ID 
                  INNER JOIN SM_SUPPLIER C ON A.SUPPLIER_ID = C.SUPPLIER_ID 
                  INNER JOIN PM_ORG_VALIDITY E ON E.ORG_PROFILE_ID = A.CREATED_ORG_PROFILE_ID AND E.RECORD_STATUS = 1 
                  INNER JOIN FL_INVOICE D ON D.FULFILMENT_REQ_ID = A.FULFILMENT_REQ_ID  AND D.FULFILMENT_ORDER_ID = B.FULFILMENT_ORDER_ID     AND D.RECORD_STATUS = 1 
                  WHERE B.DOC_NO = ? or A.DOC_NO = ?  ",
            array($search, $search)
        );

        return $dataList;
    }

    protected function getFailedTaskPRCRInitiate()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select doc_id,doc_type,doc_no, created_date , 'FailedInitiateTask' as type_error from (
                    SELECT DISTINCT fr.fulfilment_req_id as doc_id,fr.doc_no,fr.doc_type, fr.created_date
                      FROM fl_fulfilment_request fr, fl_workflow_status wf
                              WHERE fr.fulfilment_req_id = wf.doc_id(+)
                                --AND fr.DOC_TYPE='PR'
                                AND fr.record_status = 1
                                AND wf.DOC_ID is null
                                AND fr.financial_year = $year 
                                AND fr.created_date  < sysdate - interval '10' minute)  "
        );

        return $dataList;
    }

    protected function getFailedTaskPRCRPendingApproval()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "
                    SELECT fr.FULFILMENT_REQ_ID as doc_id, fr.DOC_TYPE,fr.DOC_NO,
                      ( select  rn.REQUEST_NOTE_NO from
                        sc_request_note_dtl rnd, sc_request_note rn where rnd.REQUEST_NOTE_ID = rn.REQUEST_NOTE_ID
                       and  rnd.PURCHASE_REQUEST_ID = fr.PURCHASE_REQUEST_ID
                     group by rn.REQUEST_NOTE_NO ) as REQUEST_NOTE_NO
                      ,s.STATUS_ID,  s.IS_CURRENT, fr.RECORD_STATUS , fr.PURCHASE_REQUEST_ID, s.created_date
                                        FROM fl_workflow_status s, FL_FULFILMENT_REQUEST fr
                                       WHERE
                                       fr.FULFILMENT_REQ_ID = s.doc_id
                                       and s.doc_type = fr.doc_type
                                       and fr.doc_type in ( 'PR' ,'CR')
                                       and s.IS_CURRENT = 1
                                       and s.STATUS_ID in (40000,40500)
                                       and fr.FINANCIAL_YEAR = $year
                                       and fr.financial_year =  to_char(sysdate, 'YYYY')
                                       and exists (
                                      SELECT tt.TRACKING_DIARY_ID
                                        FROM pm_tracking_diary tt
                                       WHERE tt.tracking_diary_id IN (SELECT MAX (t.tracking_diary_id)
                                                                        FROM pm_tracking_diary t
                                                                       WHERE t.doc_no = fr.DOC_NO and t.doc_type = fr.doc_type )
                                         AND tt.status_id in (40010,40510)
                                       )"
        );

        return $dataList;
    }

    protected function getPRStuckEpp13Y()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "
                select * from (
                    select mm, dd,remarks_1,poco.prcr, remarks_2,poco.status_name, status_id,poco.fws_date , TO_CHAR (Y.TRANS_DATE,'DD/MM/YYYY HH24:MI:SS') trans_date, 
                    TO_CHAR (trans_date, 'dd')hb, TO_CHAR (trans_date, 'mm')bulan,TO_CHAR (trans_date, 'yyyy')year 
                    from osb_logging Y , (select TO_CHAR (b.CREATED_DATE, 'mm') mm , TO_CHAR (b.CREATED_DATE, 'dd') dd, TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') fws_date, 
                    TO_CHAR (a.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') po_date_created, --WORKFLOW_STATUS_ID,
                    b.DOC_TYPE,e.doc_no prcr, a.DOC_NO pono,
                     (select FINANCIAL_YEAR from fl_fulfilment_request o where o.FULFILMENT_REQ_ID  = a.FULFILMENT_REQ_ID) as FINANCIAL_YEAR,d.status_name ,c.STATUS_ID,b.is_current ,
                     (select  nvl(sum (ffid.ORDERED_AMT ),0) from fl_fulfilment_item_addr ffid , fl_delivery_address fda where 
                    ffid.FULFILMENT_ADDR_ID = fda.DELIVERY_ADDRESS_ID and fda.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID  )as PRCR_AMOUNT,
                     (SELECT INVOICE_AMT_NEW  FROM fl_invoice inv where  inv.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID  and record_status = 1) INVOICE_AMT_NEW,a.fulfilment_req_id , 
                    a.FULFILMENT_ORDER_ID, a.SAP_ORDER_NO
                    from fl_fulfilment_order a,  fl_fulfilment_request e,   fl_workflow_status b, pm_status c, pm_status_desc d 
                    where e.fulfilment_req_id = b.doc_id
                    and b.status_id = c.status_id
                    and c.status_id = d.status_id
                    and d.language_code ='en'
                    and b.doc_type in ('PR','CR')   ---PO200000000050769  (PMQ)     -- data patch manual-31may2023
                    and b.is_current = 1  
                    and a.FULFILMENT_REQ_ID = e.FULFILMENT_REQ_ID
                    and b.status_id not in  (40800,40810,40900,40910,40300,40310,40400,40401,40430,40410)  --cari yg valid PRCR
                    --and ( ( b.status_id in (40810,40900,40910,40310,40400,40401,40430)  and b.is_current = 1 ) and e.FULFILMENT_REQ_ID not in 
                    --(SELECT ww.doc_ID FROM fl_workflow_status ww WHERE ww.DOC_TYPE in ('PR','CR') and is_current = 0 and status_id in (40800,40300) ) )--cari invalid PRCR tp maybe dh pnah approve tp timeout
                    and TO_CHAR (b.Created_DATE, 'yyyy') = $year  --Has Success ?    GTW16066925841567
                    --and a.doc_no  in ('PO220000000543468')  --CO180000000000269 --popo poco  -- coco   --timeout susah nk cari contoh crm     4713502
                    ) poco
                    where y.trans_id in (select distinct(trans_id) from osb_logging olg
                    where olg.service_code  = 'EPP-013' 
                    and  trans_date in  (select max(ol.trans_date) from osb_logging  ol
                    where ol.trans_id = olg.trans_id )) 
                    and status_code = '70022'  and trans_type = 'OBRes' and EXTRACT(year FROM trans_date)    = EXTRACT(year FROM sysdate)  and Y.remarks_1 = poco.pono and remarks_2 != 'NA' 
                    group by remarks_1, remarks_2,TO_CHAR (trans_date, 'dd'),TO_CHAR (trans_date, 'mm'),TO_CHAR (trans_date, 'yyyy') ,poco.status_name,mm, dd,poco.fws_date,Y.TRANS_DATE,status_id,poco.prcr
                    ) --where fws_date < trans_date  ---remarks_2 = sap order number
                    order by 2,1 asc"
        );

        return $dataList;
    }

    protected function getPRStuckMM501Del()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "
                select dd, mm,fws_date ,YEAR ,prcr ,pono ,status_name,osb.cancel_date,osb.status_desc
from (select mm, dd,remarks_1 remark1,poco.prcr, remarks_2,poco.status_name, status_id,poco.fws_date , TO_CHAR (Y.TRANS_DATE,'DD/MM/YYYY HH24:MI:SS') trans_date, 
TO_CHAR (trans_date, 'dd')hb, TO_CHAR (trans_date, 'mm')bulan,TO_CHAR (trans_date, 'yyyy')year ,pono
from osb_logging Y , (select TO_CHAR (b.CREATED_DATE, 'mm') mm , TO_CHAR (b.CREATED_DATE, 'dd') dd, TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') fws_date, 
TO_CHAR (a.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') po_date_created,b.DOC_TYPE,e.doc_no prcr, a.DOC_NO pono, a.fulfilment_req_id , a.FULFILMENT_ORDER_ID, a.SAP_ORDER_NO,
e.FINANCIAL_YEAR,d.status_name ,c.STATUS_ID,b.is_current 
from fl_fulfilment_order a,  fl_fulfilment_request e,   fl_workflow_status b, pm_status c, pm_status_desc d 
where e.fulfilment_req_id = b.doc_id and b.status_id = c.status_id and c.status_id = d.status_id and d.language_code ='en'
and b.doc_type in ('PR','CR') and b.is_current = 1 and a.FULFILMENT_REQ_ID = e.FULFILMENT_REQ_ID
--and b.status_id not in  (40800,40810,40900,40910,40300,40310,40400,40401,40430)  --cari yg valid PRCR
and ( ( b.status_id in (40810,40900,40910,40310,40400,40401,40430)  and b.is_current = 1 ) and e.FULFILMENT_REQ_ID not in 
(SELECT ww.doc_ID FROM fl_workflow_status ww WHERE ww.DOC_TYPE in ('PR','CR') and is_current = 0 and status_id in (40800,40300) ) )--cari invalid PRCR tp maybe dh pnah approve tp timeout
and TO_CHAR (b.Created_DATE, 'yyyy') = $year) poco
where y.trans_id in (select distinct(trans_id) from osb_logging olg where olg.service_code  = 'EPP-013' 
and  trans_date in  (select max(ol.trans_date) from osb_logging  ol where ol.trans_id = olg.trans_id )) 
and status_code = '70022'  and trans_type = 'OBRes' and EXTRACT(year FROM trans_date)    = EXTRACT(year FROM sysdate)  and Y.remarks_1 = poco.pono and remarks_2 != 'NA' 
group by remarks_1, remarks_2,TO_CHAR (trans_date, 'dd'),TO_CHAR (trans_date, 'mm'),TO_CHAR (trans_date, 'yyyy') ,poco.status_name,mm, dd,poco.fws_date,Y.TRANS_DATE,status_id,
poco.prcr,pono)  pocoz , 
(select Z.trans_id ,cncl.created_date cancel_date , cncl.status , cncl.status_code , cncl.status_desc,cncl.service_code , Z.REMARKS_1 , Z.REMARKS_2 , Z.REMARKS_3  
from osb_logging cncl , osb_logging Z where cncl.TRANS_ID = Z.TRANS_ID
and cncl.service_code  like 'GFM-100%' and (cncl.status_desc like '%Dokumen%berjaya dibatalkan%') 
and cncl.trans_date >= to_date('2022-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')  and cncl.TRANS_TYPE = 'IBRes' and Z.TRANS_TYPE = 'JmsIns'
--and Z.REMARKS_1 in ('CO230000000056053') 
) osb
where pocoz.pono = osb.remarks_1(+) 
and status_desc is null
order by 2,1 asc"
        );

        return $dataList;
    }

    protected function getFLPendingPaymentEpp017Y()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT mm, dd, remarks_2,  remarks_3,poco.status_name,wf_id, poco.fws_date_created ,TO_CHAR (trans_date, 'dd')hb, TO_CHAR (trans_date, 'mm')bulan,TO_CHAR (trans_date, 'yyyy')year 
            from osb_logging Y , (select TO_CHAR (b.CREATED_DATE, 'mm') mm , TO_CHAR (b.CREATED_DATE, 'dd') dd, TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') fws_date_created, 
            TO_CHAR (a.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') po_date_created,b.DOC_TYPE,a.doc_no pono, (select doc_no from fl_fulfilment_request o where o.FULFILMENT_REQ_ID  = a.FULFILMENT_REQ_ID) as pr_no,
            (select FINANCIAL_YEAR from fl_fulfilment_request o where o.FULFILMENT_REQ_ID  = a.FULFILMENT_REQ_ID) as FINANCIAL_YEAR,d.status_name ,c.STATUS_ID,b.is_current ,b.WORKFLOW_STATUS_ID wf_id,
            (select nvl(sum (ffid.ORDERED_AMT ),0) from fl_fulfilment_item_addr ffid , fl_delivery_address fda where ffid.FULFILMENT_ADDR_ID = fda.DELIVERY_ADDRESS_ID and fda.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID  )as PRCR_AMOUNT,
            (SELECT INVOICE_AMT_NEW  FROM fl_invoice inv where  inv.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID  and record_status = 1) INVOICE_AMT_NEW,a.fulfilment_req_id , a.FULFILMENT_ORDER_ID, a.SAP_ORDER_NO
            from fl_fulfilment_order a, fl_workflow_status b, pm_status c, pm_status_desc d 
            where a.fulfilment_order_id = b.doc_id and b.status_id = c.status_id and c.status_id = d.status_id and d.language_code ='en' and b.doc_type in ('PO','CO','FC')   ---PO200000000050769  (PMQ)     -- data patch manual-29may2023
            and b.is_current = 1  and b.status_id not in   (41530, 41535, 41030, 41035,49120,41932,41422)   --49120 | Acknowledged (49120)-FACTORING
            --and a.doc_no  in ('PO200000000537001')  --CO180000000000269 --popo poco  -- coco and TO_CHAR (b.Created_DATE, 'yyyy') in ('2022','2023')
            ) poco
            where y.trans_id in (select distinct(trans_id) from osb_logging 
            where service_code  = 'EPP-017' and trans_date >= to_date('2021-01-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS')) 
            --and remarks_2 in ('PO220000000010712')
            and remarks_3= 'Y' and trans_type = 'OBRes' and EXTRACT(year FROM trans_date)    = EXTRACT(year FROM sysdate) and Y.remarks_2 = poco.pono
            group by poco.fws_date_created,remarks_2, remarks_3,TO_CHAR (trans_date, 'dd'),TO_CHAR (trans_date, 'mm'),TO_CHAR (trans_date, 'yyyy'),poco.status_name,mm, dd,wf_id
            ORDER BY mm,dd asc"
        );

        return $dataList;
    }

    protected function getStuckYEPCF()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select distinct poco from (
SELECT aa.TRANS_ID, aa.remarks_1 poco, aa.remarks_2, 
(SELECT bb.STATUS_DESC
                      FROM osb_logging bb
                     WHERE bb.trans_id = aa.trans_id
                       AND bb.trans_type = 'IBRes' and rownum=1
                       AND bb.STATUS_DESC <> 'Timeout'
                        )  desc_status
            FROM osb_logging aa, osb_logging_dtl c
           WHERE aa.remarks_1 IN 
                   (
                   select yep.DOC_NO From fl_yep_tasklist yep where yep.PHASE = 3 and FINANCIAL_YEAR =2024 and yep.progress_status in (4)
                    )
             AND aa.service_code = 'GFM-100'
             AND aa.trans_type = 'IBReq'
             AND aa.logging_id = c.logging_id
             AND aa.TRANS_DATE  >= TO_DATE('01-01-2025 04:00' , 'dd-mm-yyyy hh24:mi')        
             AND EXISTS (
                    SELECT *
                      FROM osb_logging b
                     WHERE b.trans_id = aa.trans_id
                       AND b.trans_type = 'IBRes'
                       --AND b.status = 'F'
                       --AND b.STATUS_DESC like '%berjaya%'
                       AND b.status_code = '00'
                       )
        ORDER BY aa.remarks_1, trans_date)"
        );

        return $dataList;
    }

    protected function getStatusInfo($docNo)
    {
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT FYT.YEP_TASK_ID , FYT.PROGRESS_STATUS , FYT.FULFILMENT_ORDER_ID , psd.status_name
       FROM FL_YEP_TASKLIST fyt , FL_WORKFLOW_STATUS fws , PM_STATUS_DESC psd 
       WHERE fws.doc_id = fyt.FULFILMENT_ORDER_ID
       AND PSD.STATUS_ID = fws.status_id 
       AND PSD.LANGUAGE_CODE = 'en'
       AND order_no = ?
       AND FWS.DOC_TYPE IN ('PO','CO')
       AND FWS.IS_CURRENT = 1",
            array($docNo)
        );
        return $dataList;
    }

    protected function getFailedTaskSourcingDPSQCreateSimpleQuote()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "
                SELECT * FROM ( 
                    SELECT distinct
                        'FailedCreateSimpleQuote'                                 AS type_error,
                        q.quote_id                                                AS doc_id,
                        q.quote_no                                                AS doc_no,
                        ws.DOC_TYPE,
                        ws.STATUS_ID,
                        (SELECT status_name
                         FROM pm_status_desc
                         WHERE status_id = ws.status_id AND language_code = 'en') AS status_name,
                        --td.STATUS_ID,
                        (SELECT status_name
                         FROM pm_status_desc
                         WHERE status_id = td.status_id AND language_code = 'en') AS tracking_diary_status,
                        q.CREATED_DATE,
                        ws.CREATED_DATE                                           AS STATUS_DATE
                        -- ,td.ACTIONED_DATE
                      FROM sc_quote q
                        LEFT JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_TYPE = 'SQ' AND ws.DOC_ID = q.quote_id AND ws.IS_CURRENT = 1)
                        INNER JOIN pm_tracking_diary td ON td.doc_type = 'SQ' AND td.doc_id = q.quote_id
                      WHERE 
                      TO_CHAR(q.created_date,'YYYY') >=  ( TO_CHAR(sysdate,'YYYY') - 1) -- checking this year and last year only
                      AND (
                              ((ws.STATUS_ID IS NULL OR ws.STATUS_ID = 60850) AND td.Status_Id = 60851) OR
                              (ws.STATUS_ID = 60851 AND td.Status_Id = 60852)
                            )
                      AND q.created_date < sysdate - interval '10' MINUTE 
                     ) temp 
                WHERE 
                    NOT EXISTS (SELECT 1 FROM SC_WORKFLOW_STATUS WHERE DOC_TYPE = 'SQ' AND  DOC_ID =  temp.doc_id AND status_id = 60853  )      
                    AND NOT EXISTS (
                     SELECT 1 FROM sc_request_note rn ,
                      SC_WORKFLOW_STATUS ws 
                      WHERE rn.REQUEST_NOTE_ID  = ws.DOC_ID  AND ws.DOC_TYPE  = 'RN' 
                      AND ws.status_id IN (
                        60711, 		-- Rejected due to Simple Quote validity period is expired (60 days). (60711)
                        60704,     	-- Cancelled (60704))
                        60703 		-- 60703 - Completed
                        ) 
                      AND rn.quote_id = temp.doc_id 
                     )
                    AND NOT EXISTS (
                     SELECT 1 FROM sc_request_note rn ,
                      pm_tracking_diary td 
                      WHERE rn.REQUEST_NOTE_ID  = td.DOC_ID  AND td.DOC_TYPE  = 'RN' 
                      AND td.status_id IN (
                        60711, 		-- Rejected due to Simple Quote validity period is expired (60 days). (60711)
                        60704,     	-- Cancelled (60704))
                        60703 		-- 60703 - Completed
                        ) 
                      AND rn.quote_id = temp.doc_id 
                    )
                ORDER BY temp.CREATED_DATE DESC, temp.CREATED_DATE DESC "
        );

        return $dataList;
    }

    protected function getFailedTrackSQClose()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT DISTINCT * FROM (
        SELECT 
        'FailedTrackSQClose'  AS type_error,
        b.doc_id,
        a.QUOTE_NO AS doc_no,  
        b.doc_type,
        b.status_id,
        d.status_name,
        'Skip Check' AS tracking_diary_status,
        a.created_date,
        b.created_date AS status_date,
        to_char(a.END_DATE, 'YYYY-MM-DD HH24:MI:SS') end_date,to_date( to_char(sysdate , 'DD/MM/YYYY HH24') , 'DD/MM/YYYY HH24') date_compare,
           a.START_DATE  ,TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') date_created,a.QUOTE_ID,a.IS_PANEL
                   from sc_quote  a,SC_WORKFLOW_STATUS b, pm_status c, pm_status_desc d
                   where a.QUOTE_ID = b.doc_id
                   and b.status_id = c.status_id
                   and c.status_id = d.status_id
                   and B.STATUS_ID in (60851,60850)
                   and d.language_code ='en'
                   and b.doc_type = 'SQ'
                   and b.is_current = 1   
                   AND TO_CHAR (a.Created_DATE, 'yyyy') in ('$year')
                   and A.END_DATE <= to_date( to_char(sysdate , 'DD/MM/YYYY HH24') , 'DD/MM/YYYY HH24')
                   AND EXISTS (SELECT * FROM SC_QUOTE_SUPPLIER SUPP WHERE SUPP.QUOTE_ID = A.QUOTE_ID AND SUPP.IS_SUBMITTED = 1 AND RECORD_STATUS = 1) 
        UNION ALL 
        select 
        'FailedTrackSQClose'  AS type_error,
        b.doc_id,
        a.QUOTE_NO AS doc_no,  
        b.doc_type,
        b.status_id,
        d.status_name,
        'Skip Check' AS tracking_diary_status,
        a.created_date,
        b.created_date AS status_date,
        to_char(a.END_DATE, 'YYYY-MM-DD HH24:MI:SS') end_date,to_date( to_char(sysdate , 'DD/MM/YYYY HH24') , 'DD/MM/YYYY HH24') date_compare,
           a.START_DATE  ,TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') date_created,a.QUOTE_ID,a.IS_PANEL 
        from sc_quote  a,SC_WORKFLOW_STATUS b, pm_status c, pm_status_desc d
        where a.QUOTE_ID = b.doc_id
        and b.status_id = c.status_id
        and c.status_id = d.status_id
        --and B.STATUS_ID in (60851,60850)
        and B.STATUS_ID in (60851)
        and d.language_code ='en'
        and b.doc_type = 'SQ'
        and b.is_current = 1   
        AND TO_CHAR (a.Created_DATE, 'yyyy') in ('$year')
        and A.END_DATE <= to_date( to_char(sysdate , 'DD/MM/YYYY HH24') , 'DD/MM/YYYY HH24')
        ) k ORDER BY k.created_date asc 
        "
        );

        return $dataList;
    }

    protected function getFailedTaskSourcingDPRNPendingApproval()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select
                    'FailedPendingApproval'                                 AS type_error,
                    rn.REQUEST_NOTE_ID as doc_id, rn.REQUEST_NOTE_NO ,
                    (select quote_no from SC_QUOTE where quote_id=rn.quote_id) AS QUOTE_NO,
                    (select quote_no from SC_QUOTE where quote_id=rn.quote_id) || ' > ' || rn.REQUEST_NOTE_NO  as doc_no,
                    ws.DOC_TYPE,
                    --ws.STATUS_ID,
                    (select status_name from pm_status_desc where status_id = ws.status_id and language_code = 'en') as status_name,
                    --td.STATUS_ID,
                    (select status_name from pm_status_desc where status_id = td.status_id and language_code = 'en') as tracking_diary_status,
                    rn.CREATED_DATE,
                    ws.CREATED_DATE AS STATUS_DATE,
                    td.ACTIONED_DATE
                  from SC_REQUEST_NOTE rn
                  left join SC_WORKFLOW_STATUS ws on (ws.DOC_TYPE='RN' and ws.DOC_ID=rn.REQUEST_NOTE_ID and ws.IS_CURRENT = 1)
                  inner join pm_tracking_diary td on td.doc_type='RN' and td.doc_id=rn.REQUEST_NOTE_ID
                  where to_char(rn.created_date,'YYYY') = $year
                  and (
                    ((ws.STATUS_ID IS NULL or ws.STATUS_ID=60700) and rn.QUOTE_ID is null and td.Status_Id=60701) OR
                    (ws.STATUS_ID IS NULL and rn.QUOTE_ID is not null and td.Status_Id=60701)
                  )
                  -- and rn.REQUEST_NOTE_NO not in ('RN190000001624316')
                  AND rn.created_date < sysdate - interval '10' minute
                  order by td.ACTIONED_DATE desc, ws.CREATED_DATE desc, rn.CREATED_DATE desc "
        );

        return $dataList;
    }

    protected function getFailedTaskRNTriggerOrder()
    {
        $dataList = DB::connection('oracle_nextgen_rpt')->select("SELECT 
            DISTINCT 
            'FailedTriggerOrder' AS type_error,
            rn.created_date,
            rn.REQUEST_NOTE_ID as doc_id,
            rn.REQUEST_NOTE_NO AS doc_no,
            wfs.CREATED_DATE AS STATUS_DATE,
            sd.STATUS_NAME,
            wfs.doc_type,
            (SELECT doc_no FROM FL_FULFILMENT_REQUEST ffr WHERE ffr.purchase_request_id = rnd.PURCHASE_REQUEST_ID) PR_no,rnd.PURCHASE_REQUEST_ID ,
            rnd.RECORD_STATUS rnd_status,decode (rnd.FULFILMENT_TYPE_ID, '148' , 'One-Off', '149', 'Periodic(Schedule)', '150', 'Periodic(As n When)') AS typ_fl,
            (select user_name from pm_user where user_id = assign_to ) req,
            (select ur.RECORD_STATUS role_status from pm_user pu, pm_user_org puo , pm_user_role ur where pu.USER_ID = puo.USER_ID and puo.USER_ORG_ID = ur.USER_ORG_ID and  pu.user_id in  (assign_to) and puo.record_status =1 
                and ur.ROLE_CODE = 'REQUISITIONER') req_, 
            (select max(ORG_CODE) from pm_org_validity where org_profile_id = rn.org_profile_id) ptj_code, wfs.STATUS_ID
        FROM  sc_request_note rn,sc_workflow_status wfs, pm_status_desc sd, sc_request_note_dtl rnd 
        WHERE rnd.PURCHASE_REQUEST_ID in (
            select pre.PURCHASE_REQUEST_ID from sc_purchase_request pre where 
            not exists (select 1 from fl_fulfilment_request prx where prx.PURCHASE_REQUEST_ID = pre.PURCHASE_REQUEST_ID)
            and EXTRACT(year FROM pre.CREATED_DATE) = EXTRACT(year FROM sysdate) )  
        AND rn.REQUEST_NOTE_ID = rnd.REQUEST_NOTE_ID and rnd.FULFILMENT_TYPE_ID in (148) and sd.STATUS_ID = wfs.STATUS_ID and sd.LANGUAGE_CODE in ('en')
        AND wfs.doc_type = 'RN' and wfs.is_current = 1 and wfs.DOC_ID = rn.REQUEST_NOTE_ID and wfs.status_id in (60703 ) and rnd.RECORD_STATUS <> 2 
        AND rn.REQUEST_NOTE_NO NOT IN ('RN250000000266498')");

        return $dataList;
    }

    protected function getFailedTaskSourcingDPRNCodification()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select 
                    'FailedCodification'                                 AS type_error, b.doc_type,
                    a.CREATED_DATE, a.REQUEST_NOTE_ID as doc_id, a.REQUEST_NOTE_NO as doc_no, 
                    d.status_name ,c.STATUS_ID ,b.is_current,a.CREATED_BY rn_created, a.CHANGED_BY rn_change, b.created_date 
                   from sc_request_note a,SC_WORKFLOW_STATUS b, pm_status c, pm_status_desc d
                   where a.REQUEST_NOTE_ID = b.doc_id
                   and b.status_id = c.status_id
                   and c.status_id = d.status_id
                   and d.language_code ='en'
                   and b.doc_type = 'RN' 
                   and b.is_current = 1
                   and b.STATUS_ID in (60701) 
                   AND to_char(a.created_date,'YYYY') = $year
                   AND b.created_date < sysdate - interval '15' minute 
                   AND exists (
                   SELECT tt.TRACKING_DIARY_ID
                     FROM pm_tracking_diary tt
                    WHERE tt.tracking_diary_id IN (SELECT MAX (t.tracking_diary_id)
                                                     FROM pm_tracking_diary t
                                                    WHERE t.doc_no = a.REQUEST_NOTE_NO and t.doc_type = 'RN' )
                      AND tt.status_id in (60703,60708) 
                      AND tt.actioned_date < sysdate - interval '15' minute 
                    )"
        );

        return $dataList;
    }

    protected function getTrackingDiaryByPOCONo($groupId)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('PM_TRACKING_DIARY td')
            ->join('PM_STATUS_DESC sd', 'td.STATUS_ID', '=', 'sd.STATUS_ID')
            ->where('sd.LANGUAGE_CODE', 'en')
            ->whereIn('td.DOC_TYPE', ['PA', 'PO'])
            ->where('td.GROUP_ID', $groupId)
            ->orderBy('td.ACTIONED_DATE', 'desc')
            ->select('td.*', 'sd.STATUS_NAME')
            ->get();
    }

    protected function getDocDetails($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('FL_FULFILMENT_ORDER A')
            ->join('FL_WORKFLOW_STATUS B', 'A.FULFILMENT_ORDER_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->join('FL_PAYMENT_ADVICE PA', 'PA.FULFILMENT_ORDER_ID', '=', 'A.FULFILMENT_ORDER_ID')
            ->join('PM_USER PM', 'PM.USER_ID', '=', 'PA.CREATED_BY')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('PA.RECORD_STATUS', 1)
            ->whereIn('B.DOC_TYPE', ['PO', 'CO', 'FC'])
            ->whereIn('B.STATUS_ID', [41030, 41530])
            ->where('A.DOC_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('B.DOC_TYPE AS doctype', 'A.FULFILMENT_ORDER_ID AS docid', 'A.DOC_NO AS docno', 'PM.IDENTIFICATION_NO AS actionby', 'C.STATUS_ID AS statusid', 'B.DOC_TYPE AS groupdoctype', 'A.FULFILMENT_ORDER_ID AS groupdocid', 'A.DOC_NO AS tdiarydatalist1', 'PM.USER_NAME AS tdiarydatalist2')
            ->first();
    }

    protected function getPADetails($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select(
            "
        select b.DOC_TYPE as doctype, 
        a.PAYMENT_ADVICE_ID as docid ,
        a.PAYMENT_ADVICE_NO as docno,
        c.STATUS_ID as statusid,
        po.DOC_TYPE as groupdoctype,
        a.FULFILMENT_ORDER_ID as groupdocid, 
        a.PAYMENT_ADVICE_NO as tdiarydatalist1
                from fl_payment_advice a, fl_workflow_status b, pm_status c, pm_status_desc d , fl_fulfilment_order po
                where a.PAYMENT_ADVICE_ID = b.doc_id
                and b.status_id = c.status_id
                and c.status_id = d.status_id
                and a.FULFILMENT_ORDER_ID = po.FULFILMENT_ORDER_ID
                and d.language_code ='en'
                and b.doc_type in ('PA')
                and b.is_current = 1
                and a.RECORD_STATUS = 1 
                -- and a.FULFILMENT_order_ID = 13634548
                and a.FULFILMENT_REQ_ID in (select FULFILMENT_REQ_ID from fl_fulfilment_order 
                where doc_no = ?)
                -- AND TO_CHAR (a.Created_DATE, 'yyyy') = '2018'
                order by b.CREATED_DATE desc",
            array($docNo)
        );

        return $query;
    }

    /**
     * Return single object
     */
    protected function getCurrentWorkflowByPoCo($docNo)
    {

        return DB::connection('oracle_nextgen_rpt')
            ->table('FL_FULFILMENT_ORDER A')
            ->join('FL_WORKFLOW_STATUS B', 'A.FULFILMENT_ORDER_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('B.IS_CURRENT', 1)
            ->whereIn('B.DOC_TYPE', ['PO', 'CO', 'FC'])
            ->where('A.DOC_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('C.STATUS_ID', 'D.STATUS_NAME', 'C.CREATED_DATE as STATUS_CREATED_DATE')
            ->first();
    }

    protected function getListWorkFlowStatusDOFN($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select(
            "
        SELECT
            (SELECT doc_no
             FROM fl_fulfilment_order
             WHERE fulfilment_req_id = fdo.FULFILMENT_REQ_ID)               poco_no,
            fdo.DELIVERY_ORDER_NO,
            (SELECT do1.status_id
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND
                   do1.status_id = do2.status_id AND do1.is_current = 1 AND do2.language_code = 'en' AND
                   do1.doc_type IN ('DO'))                                  do_status,
            (SELECT do2.status_name
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND do1.status_id = do2.status_id AND do1.is_current = 1
                   AND do2.language_code = 'en' AND do1.doc_type IN ('DO')) do_status_name,
            nvl(frn.FULFILMENT_NOTE_NO, 'Tiada')                              FRN_NO,
            (SELECT frn1.status_id
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status,
            (SELECT frn2.status_name
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status_name,
            fdo.RECORD_STATUS                                               do_record_status,
            frn.RECORD_STATUS                                               frn_record_status,
            fdo.DELIVERY_order_ID,
            frn.FULFILMENT_NOTE_ID
          FROM fl_fulfilment_note frn, fl_fulfilment_request pr, fl_fulfilment_order po, fl_delivery_order fdo
          WHERE
            pr.FULFILMENT_REQ_ID = po.FULFILMENT_REQ_ID
            AND fdo.FULFILMENT_ORDER_ID = po.FULFILMENT_ORDER_ID
            AND fdo.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID (+)
            AND po.DOC_NO = ? ",
            array($docNo)
        );

        return $query;
    }

    protected function getFailedListInitiateTaskDO()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT TO_CHAR (do.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') created_date, do.delivery_order_no  as doc_no, do.record_status ,
                    do.delivery_order_id as doc_id,
                    (select doc_no from fl_fulfilment_order o where o.FULFILMENT_ORDER_ID  = do.FULFILMENT_ORDER_ID) as poco_no ,
                    (select b.status_name from pm_tracking_diary a , pm_status_desc b  where a.status_id = b.status_id and a.doc_no = do.delivery_order_no and a.doc_type = 'DO' and a.doc_id =  do.delivery_order_id
                      and b.language_code = 'en') as tracking_diary_status
                  FROM fl_delivery_order do
                  WHERE
                    to_char(do.created_date,'YYYY') = ? 
                    AND do.created_date  < sysdate - interval '30' minute
                    AND do.delivery_order_id NOT IN (
                      SELECT doc_id
                       FROM fl_workflow_status
                       WHERE
                         doc_id = delivery_order_id AND doc_type = 'DO')",
            array($year)
        );

        return $dataList;
    }

    protected function getFailedListIntegrationPRCRApprovalGFMASServiceCall()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select
                    TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') fws_date_created,--TO_CHAR (b.changed_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_changed,
                    a.CREATED_DATE,
                     a.doc_no,
                     (select doc_no from fl_fulfilment_order o where o.FULFILMENT_REQ_ID  = a.FULFILMENT_REQ_ID) as po_no,
                      a.fulfilment_req_id,b.doc_type , b.doc_id, d.status_name ,
                    c.STATUS_ID,b.is_current , a.SCHEDULE_NO,
                    (select max(ORG_CODE) from pm_org_validity ptj where ptj.ORG_PROFILE_ID = a.created_ORG_PROFILE_ID ) ptj_code,
                    USER_GROUP_ID, a.CREATED_BY peminta, a.CHANGED_BY,a.APPROVER_ID, a.CONTRACT_ID ,
                    a.AG_APPROVED_DATE , a.CREATED_ORG_PROFILE_ID,
                    a.* , start_date , end_date , start_date + 30,decode (a.PHIS_NO , '' , 'false', 'true') AS isPhis
                    from fl_fulfilment_request a, fl_workflow_status b, pm_status c, pm_status_desc d
                    where a.fulfilment_req_id = b.doc_id
                    and b.status_id = c.status_id
                    and c.status_id = d.status_id
                    and d.language_code ='en'
                    and b.doc_type in ('CR','PR')
                    AND b.is_current = 1
                    and b.STATUS_ID in (40600,40100) 
                    AND a.financial_year = $year  
                    AND  b.created_date  < sysdate - interval '30' minute
                    order by b.CREATED_DATE desc "
        );

        return $dataList;
    }

    protected function getFailedListIntegrationFRNApprovalGFMASServiceCall()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select * from (
                    SELECT
                      TO_CHAR(b.CREATED_DATE, 'YYYY/MM/DD HH24:MI:SS')     fws_date_created,
                      b.DOC_TYPE,
                      a.FULFILMENT_NOTE_NO                              AS doc_no,
                      a.FULFILMENT_REQ_ID,
                      b.doc_id,
                      d.status_name,
                      c.STATUS_ID,
                      b.is_current,
                      a.RECORD_STATUS,
                      a.FULFILMENT_NOTE_NO                              AS search_bpm,
                      b.CREATED_DATE,
                      (SELECT doc_no
                       FROM fl_fulfilment_order o
                       WHERE o.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID) AS po_no,
                      b.CREATED_BY
                    FROM fl_fulfilment_note a, fl_workflow_status b, pm_status c, pm_status_desc d
                    WHERE a.FULFILMENT_NOTE_ID = b.doc_id
                          AND b.status_id = c.status_id
                          AND c.status_id = d.status_id
                          AND d.language_code = 'en'
                          AND b.doc_type IN ('FN')
                          AND b.STATUS_ID IN (43100)
                          AND b.is_current = 1
                          AND to_char(a.created_date, 'YYYY') = to_char(sysdate, 'YYYY') 
                          AND b.created_date < sysdate - interval '30' minute



                  ) a where
                     not exists( select 1 from  FL_WORKFLOW_STATUS where doc_type = 'FN'
                             and doc_id = a.doc_id and status_id in (43010)
                     )
                      order by a.created_date desc, a.FULFILMENT_REQ_ID desc"
        );

        return $dataList;
    }

    protected function getFailedListStuckFRNOnSubmittion()
    {
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT
                TO_CHAR (td.actioned_date,'DD/MM/YYYY HH24:MI:SS') fws_date_created,
                TO_CHAR (td.actioned_date,'DD/MM/YYYY HH24:MI:SS') created_date,
                'FN' as doc_type,
                fn.fulfilment_note_no as doc_no,
                fo.FULFILMENT_REQ_ID,
                fn.FULFILMENT_NOTE_ID as doc_id,
                (select status_name from pm_status_desc where status_id = td.status_id and language_code='en' ) as status_name,
                td.status_id,
                fo.doc_no                              AS po_no,
                do.delivery_order_no as search_bpm
              FROM
                FL_FULFILMENT_NOTE fn, FL_DELIVERY_ORDER do,
                FL_FULFILMENT_ORDER fo, PM_TRACKING_DIARY td
              WHERE

                fn.delivery_order_id = do.delivery_order_id
                AND do.fulfilment_order_id = fo.fulfilment_order_id
                AND td.doc_no =  fn.fulfilment_note_no
                AND td.doc_type = 'FN'
                AND td.actioned_date in (select max(actioned_date)  from PM_TRACKING_DIARY WHERE
                   doc_type = 'FN'
                   AND doc_no = fn.fulfilment_note_no )
                AND NOT exists(SELECT 1
                               FROM FL_WORKFLOW_STATUS
                               WHERE doc_type = 'FN' AND doc_id = fn.fulfilment_note_id)
                AND to_char(fn.created_date, 'YYYY') = to_char(sysdate, 'YYYY')
                AND exists(select 1 from FL_WORKFLOW_STATUS where doc_id = fo.fulfilment_order_id
                            and is_current = 1 and doc_type = fo.doc_type
                            and status_id in (
                                41010,41005,41510,41505
                              )
                          )"
        );

        return $dataList;
    }

    protected function getFailedListIntegrationSDApprovalGFMASServiceCall()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created, b.DOC_TYPE ,b.doc_id,a.FULFILMENT_REQ_ID,a.STOP_INSTR_NO as doc_no, a.FULFILMENT_REQ_ID,
                    d.status_name ,c.STATUS_ID,b.is_current, a.RECORD_STATUS sd_record , a.*
                   from fl_stop_instr a, fl_workflow_status b, pm_status c, pm_status_desc d
                   where a.STOP_INSTR_ID = b.doc_id
                   and b.status_id = c.status_id
                   and c.status_id = d.status_id
                   and d.language_code ='en'
                   and b.doc_type in ('SD')
                   and b.is_current = 1 
                   AND to_char(a.created_date,'YYYY') = '$year' 
                   and b.status_id = 47100
                     AND  b.created_date  < sysdate - interval '30' minute
                   order by b.CREATED_DATE desc"
        );

        return $dataList;
    }

    protected function getFailedIntegrationDANApprovalGFMASServiceCall()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created, b.DOC_TYPE ,a.DOC_NO,a. 
                    FULFILMENT_ORDER_ID, a.FULFILMENT_REQ_ID, b.doc_id,
                    d.status_name ,c.STATUS_ID,b.is_current , a.RECORD_STATUS, a.*
                    from fl_ADJUSTMENT a, fl_workflow_status b, pm_status c, pm_status_desc d
                    where a.ADJUSTMENT_ID = b.doc_id
                    and b.status_id = c.status_id
                    and c.status_id = d.status_id
                    and d.language_code ='en'
                    and b.doc_type in ('CN','DN')
                    and b.is_current = 1 
                    AND to_char(a.created_date,'YYYY') = '$year' 
                    AND b.STATUS_ID in (45100)
                      AND  b.created_date  < sysdate - interval '30' minute
                    order by b.CREATED_DATE desc"
        );

        return $dataList;
    }

    protected function getFailedIntegrationPaymentAdviceApprovalGFMASServiceCall()
    {
        $year = Carbon::now()->year;
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM (
                    select TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created, 
                            a.created_date,
                            fo.DOC_TYPE ,a.FULFILMENT_order_ID as doc_id,a.PAYMENT_ADVICE_NO ,
                            fo.doc_no,
                            a.FULFILMENT_REQ_ID,INVOICE_ID, a.FULFILMENT_order_ID,
                            d.status_name ,c.STATUS_ID,b.is_current,DESCRIPTION
                     from fl_fulfilment_request r,
                          fl_fulfilment_order fo,
                          fl_payment_advice a,
                          fl_workflow_status b,
                          pm_status c,
                          pm_status_desc d
                                      where r.FULFILMENT_REQ_ID = a.FULFILMENT_REQ_ID
                                      and  r.FULFILMENT_REQ_ID  = fo.FULFILMENT_REQ_ID
                                      and  a.PAYMENT_ADVICE_ID = b.doc_id
                                      and b.status_id = c.status_id
                                      and c.status_id = d.status_id
                                      and d.language_code ='en'
                                      and b.doc_type in ('PA')
                                      and b.STATUS_ID   in (46100,46000)
                                      AND r.financial_year = to_char(sysdate, 'YYYY')
                                      and b.is_current = 1
                                      AND  b.created_date  < sysdate - interval '30' minute
                                      AND EXISTS(
                                            SELECT tt.tracking_diary_id
                                            FROM pm_tracking_diary tt
                                            WHERE tt.actioned_date IN (SELECT MAX(t.actioned_date)
                                                                           FROM pm_tracking_diary t
                                                                           WHERE t.doc_no = a.PAYMENT_ADVICE_NO and t.doc_type= 'PA')
                                             AND tt.status_id in (46100)
                                             AND tt.doc_no = a.PAYMENT_ADVICE_NO and tt.doc_type= 'PA'
                                            )
                  )  temp where
                     NOT EXISTS (SELECT * FROM fl_workflow_status wf where wf.doc_type in ('PO','CO') and wf.status_id in (41530,41535,41035) and wf.doc_id  = temp.FULFILMENT_ORDER_ID  )
            "
        );

        return $dataList;
    }

    protected function getFailedIntegrationPaymentAdviceApproval()
    {
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select
                    TO_CHAR (pa.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') created_date, 
                    pa.payment_advice_no , pa.record_status ,
                        o.doc_type,
                                        pa.PAYMENT_ADVICE_ID,
                                        o.fulfilment_order_id as doc_id ,
                                        o.doc_no,
                                        (select b.status_name from pm_tracking_diary a , pm_status_desc b
                                          where a.status_id = b.status_id and a.doc_no = pa.payment_advice_no and a.doc_type = 'PA' and a.doc_id =  pa.PAYMENT_ADVICE_ID
                                          and b.language_code = 'en' and rownum < 2) as status_name

                    from FL_PAYMENT_ADVICE pa , FL_FULFILMENT_REQUEST r, FL_FULFILMENT_ORDER o
                    where pa.fulfilment_req_id = r.fulfilment_req_id 
                      AND pa.fulfilment_order_id = o.fulfilment_order_id
                      AND r.financial_year = TO_CHAR(sysdate,'YYYY')
                      AND pa.record_status = 1 
                      AND  pa.created_date  < sysdate - interval '30' minute
                      AND o.DOC_NO not in ('CO200000000045954')
                      AND NOT EXISTS (
                        SELECT *
                                           FROM fl_workflow_status
                                           WHERE
                                             doc_id = pa.PAYMENT_ADVICE_ID AND doc_type = 'PA'
                        )
                      AND not exists(
                        SELECT *
                                               FROM fl_workflow_status
                                               WHERE
                                                 doc_id = o.fulfilment_order_id AND doc_type = o.doc_type
                                                and status_id IN (41030,41035,41535,41530,41030)
                      )  
            "
        );

        return $dataList;
    }

    protected function getDOSupplierList($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select(" 
                    SELECT  personnel.user_id as user_id,
                    (SELECT u.login_id FROM pm_user u WHERE u.user_id = personnel.user_id AND u.record_status = 1) as user_login_id,
                    personnel.NAME as username, 
                    personnel.DESIGNATION as designation,
                    personnel.RECORD_STATUS as record_status
                        FROM     sm_supplier supp, sm_personnel personnel, sm_mof_account mof
                        WHERE supp.supplier_id = mof.supplier_id(+)
                            AND supp.latest_appl_id = personnel.appl_id
                            AND is_authorized = 1
                            AND personnel.user_id is not null
                            AND supp.SUPPLIER_ID in (select supplier_id from fl_fulfilment_request pr where pr.FULFILMENT_REQ_ID  in 
                            ( select fulfilment_req_id from fl_fulfilment_order po
                            where po.DOC_NO in (?) ))
                            ORDER BY personnel.ep_role
                ", array($docNo));
        return $query;
    }

    protected function getDeliveryOrderDetail($docNo, $dono)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
                    select a.DELIVERY_order_ID as do_id, 
                        a.DELIVERY_ORDER_NO as do_doc_no,
                        a.SUPPLIER_DO_REF as supplier_do_no, 
                        TO_CHAR (a.CREATED_DATE,'yyyy-MM-dd') as supplier_do_date,
                        TO_CHAR (a.CREATED_DATE,'HH24:MI:ss.FF3') as supplier_do_time
                        from fl_delivery_order a
                        where a.FULFILMENT_ORDER_ID in (select FULFILMENT_order_ID from fl_fulfilment_order where doc_no in (?))
                        and a.DELIVERY_ORDER_NO  in (?)
                ", array($docNo, $dono));
        return $query;
    }

    protected function getReceivingOfficerList($docNo, $dono, $prio = 1)
    {
        $query = " select pu.USER_ID as user_id,
                    pu.LOGIN_ID as user_login_id, 
                    pu.USER_NAME as username, 
                    pu.DESIGNATION as designation
                        from pm_address_type pat , pm_address pa  , pm_user pu , pm_org_validity ptj  ,pm_user_org b, pm_user_role c
                        where pat.ADDRESS_ID = pa.ADDRESS_ID   
                        and pat.ORG_PROFILE_ID = ptj.ORG_PROFILE_ID
                        and b.USER_ORG_ID = c.USER_ORG_ID
                        and pu.USER_ID = b.USER_ID
                        and address_type in ('R') 
                        and pat.USER_ID = pu.USER_ID
                        and upper(c.ROLE_CODE) like  upper('%RECEIVING%')
                        and pat.RECORD_STATUS = 1 
                        and pa.RECORD_STATUS = 1
                        and ptj.RECORD_STATUS =1
                        and pu.RECORD_STATUS = 1
                        and c.RECORD_STATUS = 1
                        and b.RECORD_STATUS = 1 ";

        if ($prio == 2) {
            /** exactly get receiving officer by PO  * */
            $query = $query . " 
                        and pa.address_id in (
                        SELECT fda.PM_ADDRESS_ID
                          FROM 
                               fl_fulfilment_order po,
                               fl_delivery_order do,
                               fl_delivery_address fda 
                               where po.FULFILMENT_ORDER_ID = do.FULFILMENT_ORDER_ID 
                               and do.DELIVERY_ADDRESS_ID = fda.DELIVERY_ADDRESS_ID
                         and po.DOC_NO in (?) 
                         ) ";
            return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        } else if ($prio == 3) {
            /** exactly get receiving officer by PO created PTJ  * */
            $query = $query . " 
                         and ptj.org_profile_id in (
                             SELECT pr.created_org_profile_id 
                                FROM 
                                     fl_fulfilment_order po,
                                     fl_fulfilment_request pr
                               WHERE po.fulfilment_req_id = pr.fulfilment_req_id       
                               and po.DOC_NO in (?) 
                         
                         )
                         ";

            return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        } else {
            /** exactly get receiving officer by PO and DO * */
            $query = $query . " 
                        and pa.address_id in (
                        SELECT fda.PM_ADDRESS_ID
                          FROM 
                               fl_fulfilment_order po,
                               fl_delivery_order do,
                               fl_delivery_address fda 
                               where po.FULFILMENT_ORDER_ID = do.FULFILMENT_ORDER_ID 
                               and do.DELIVERY_ADDRESS_ID = fda.DELIVERY_ADDRESS_ID
                         and po.DOC_NO in (?) 
                         and do.DELIVERY_ORDER_NO in (?)
                         )";
            return DB::connection('oracle_nextgen_rpt')->select($query, array($docNo, $dono));
        }
    }

    protected function getDODetail($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
                    select a.fulfilment_order_id as order_id , 
                    a.fulfilment_req_id as request_id , 
                    pr.TITLE as order_name,  
                    a.DOC_NO as order_doc_no, 
                    a.SAP_ORDER_NO as sap_order_no,
                    a.DOC_TYPE as order_type ,
                    decode (pr.PHIS_NO , '' , 'false', 'true') as isphis,
                    ago.OFFICE_CODE as business_area
                        from fl_fulfilment_order a, fl_fulfilment_request pr ,fl_workflow_status b, 
                        pm_status c, pm_status_desc d, pm_ag_office e,
                        pm_ag_validity agv ,pm_ag_office ago
                        where a.fulfilment_order_id = b.doc_id
                        and a.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID
                        and b.status_id = c.status_id
                        and c.status_id = d.status_id
                        and pr.AG_OFFICE_ID = agv.AG_OFFICE_ID
                        and agv.AG_OFFICE_ID = ago.AG_OFFICE_ID
                        and d.language_code ='en'
                        and b.doc_type in ('PO','CO')
                        and b.is_current = 1  
                        and a.doc_no  in (?)
                        order by b.CREATED_DATE desc
                ", array($docNo));
        return $query;
    }

    protected function getRnNoByPR($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('sc_request_note a')
            ->join('sc_purchase_request b', 'a.REQUEST_NOTE_ID', '=', 'b.REQUEST_NOTE_ID')
            ->join('fl_fulfilment_request c', 'b.PURCHASE_REQUEST_ID', '=', 'c.PURCHASE_REQUEST_ID')
            ->whereIn('c.DOC_TYPE', ['PR'])
            ->where('c.DOC_NO', $docNo)
            ->select('a.REQUEST_NOTE_NO as rn_no', 'a.REQUEST_NOTE_ID as rn_no_id', 'a.RECORD_STATUS as rn_status', 'b.PURCHASE_REQUEST_ID as pr_id', 'b.RECORD_STATUS as pr_status', 'c.DOC_NO as pr_no', 'c.DOC_TYPE as pr_doc_type', 'c.FULFILMENT_REQ_ID')
            ->first();
    }

    protected function getFnNoByDoNo($docNo)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('fl_delivery_order a')
            ->leftJoin('fl_fulfilment_note b', 'a.DELIVERY_ORDER_ID', '=', 'b.DELIVERY_ORDER_ID')
            ->where('a.DELIVERY_ORDER_NO', $docNo)
            ->select('a.DELIVERY_ORDER_ID as do_id', 'a.DELIVERY_ORDER_NO as do_no', 'b.FULFILMENT_NOTE_NO as fn_no')
            ->first();
    }

    protected function getDPFromSQ($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT b.request_note_no
                        FROM sc_quote a, sc_request_note b
                        WHERE a.quote_id = b.quote_id 
                            and a.quote_no = ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getDPFromLOA($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT request_note_no
            FROM sc_loa loa, sc_loi_loa loi, SC_PURCHASE_REQUEST spr, SC_REQUEST_NOTE rn
            WHERE loa.LOI_LOA_ID  = loi.LOI_LOA_ID 
            AND loi.DOC_ID = spr.PURCHASE_REQUEST_ID 
            AND spr.REQUEST_NOTE_ID = rn.REQUEST_NOTE_ID 
            and loi.DOC_TYPE = 'PR'
            AND loa_no = ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getDPFromPR($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select request_note_no 
                        from fl_fulfilment_request flreq, sc_purchase_request screq, sc_request_note scnote
                        where  screq.purchase_request_id(+) = flreq.purchase_request_id
                        and screq.request_note_id = scnote.request_note_id
                        and flreq.doc_no = ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getDPFromPO($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select request_note_no , florder.fulfilment_order_id, flreq.doc_no
                        from fl_fulfilment_request flreq, sc_purchase_request screq, sc_request_note scnote, fl_fulfilment_order florder
                        where  screq.purchase_request_id(+) = flreq.purchase_request_id
                        and florder.FULFILMENT_REQ_ID(+) = flreq.FULFILMENT_REQ_ID
                        and screq.request_note_id = scnote.request_note_id
                        and florder.doc_no = ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getLocationForZonal($sq_id)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select  TSZD.zone_name,
                (SELECT state_name FROM pm_state ps WHERE ps.STATE_ID = tsl.state_id) AS state_name,
                (SELECT division_name FROM PM_DIVISION pd WHERE pd.division_id = tsl.division_id) AS division_name,
                (SELECT district_name FROM PM_DISTRICT pd1 WHERE pd1.district_id = tsl.district_id) AS district_name
                from sc_quote_doc sqd, tpl_spec_master tsm, tpl_spec_zone tsz, tpl_spec_zone_dtl tszd, tpl_spec_loc tsl
                where sqd.SPEC_MASTER_ID = tsm.SPEC_MASTER_ID
                AND tsz.SPEC_ZONE_ID  = tsm.SPEC_ZONE_ID 
                AND tsz.spec_zone_id = TSZD.spec_zone_id
                AND tsl.DOC_ID = tszd.spec_zone_dtl_id
                AND sqd.quote_id =  ?
         ",
            array($sq_id)
        );
        return $results;
    }

    protected function listWhenSqSubmitted($sq_id, $sq_id1, $sq_id2)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT CASE when sq.SUPPLIER_CATALOGUE_ID = 0 THEN 'no' WHEN sq.SUPPLIER_CATALOGUE_ID IS NULL THEN 'no' ELSE 'yes'END AS catalogu, 
tsq.spec_question_id AS SPEC_QUESTION_ID,sri.TEMP_ITEM_CODE , CL_ITEM, CL_UOM_FREQUENCY , CL_UOM_PER_DAY , CL_UOM_PER_MONTH , CL_DURATION_MONTH , sri.item_id, NULL AS item_clone, uom_code, uom_name,
            DECODE (tpm.ITEM_TYPE_ID , 199, 'Perkhidmatan', 198, 'Produk' ) AS item_type,
            DECODE (tsq.cl_fulfilment_type_id , 148, 'One-Off', 149, 'Bermasa (Berjadual)', 150, 'Bermasa (Bila Perlu)' ) AS jenis_pemenuhan,
            pmd.code_name, tsq.cl_quantity AS kuantiti,CL_SPEC_DETAIL 
            FROM sc_quote sq, sc_quote_doc sqd, tpl_spec_master tpm, TPL_SPEC_QUESTION tsq, pm_parameter_desc pmd  , pm_uom uom, SC_REQUEST_ITEM sri 
            WHERE sqd.SPEC_MASTER_ID = tpm.SPEC_MASTER_ID 
            AND sri.QUOTE_ID = sqd.quote_id
            AND uom.uom_id = tsq.cl_uom_id
            AND sq.quote_id = sqd.QUOTE_ID
            AND question_type = 'I'
            AND sqd.SPEC_MASTER_ID  = tsq.SPEC_MASTER_ID 
            AND pmd.PARAMETER_ID = tsq.CL_PRICE_TYPE_ID 
            AND pmd.LANGUAGE_CODE = 'en'
            AND sqd.quote_id = ?  
            AND cl_item NOT IN (SELECT 
            (SELECT tsq1.cl_item
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS item
            FROM TPL_SPEC_QUESTION tsq, sc_quote_doc sqd
            WHERE tsq.SPEC_MASTER_ID = sqd.SPEC_MASTER_ID  
            AND sqd.quote_id = ?  
            AND tsq.question_type = 'S')
            UNION
            SELECT CASE when sq.SUPPLIER_CATALOGUE_ID = 0 THEN 'no' WHEN sq.SUPPLIER_CATALOGUE_ID IS NULL THEN 'no' ELSE 'yes'END AS catalogu,
            (SELECT tsq5.SPEC_QUESTION_ID 
            FROM TPL_SPEC_QUESTION tsq5
            WHERE tsq5.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND tsq5.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq5.spec_question_id(+)) AS SPEC_QUESTION_ID,
            (SELECT DISTINCT sri3.temp_item_code 
            FROM TPL_SPEC_QUESTION tsq6, SC_REQUEST_ITEM sri3
            WHERE tsq6.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND sri3.QUOTE_ID = SQD.QUOTE_ID 
            AND sri3.SPEC_QUESTION_ID = tsq6.SPEC_QUESTION_ID 
            AND tsq6.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq6.spec_question_id(+)) AS TEMP_ITEM_CODE,
            (SELECT tsq1.cl_item
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS item, 
            (SELECT tsq1.CL_UOM_FREQUENCY
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS CL_UOM_FREQUENCY, 
            (SELECT tsq1.CL_UOM_PER_DAY
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS CL_UOM_PER_DAY, 
            (SELECT tsq1.CL_UOM_PER_MONTH
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS CL_UOM_PER_MONTH, 
            (SELECT tsq1.CL_DURATION_MONTH
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS CL_DURATION_MONTH, 
            (SELECT tsq1.item_id
            FROM TPL_SPEC_QUESTION tsq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS item_id,
            (SELECT sq1.quote_no
            FROM TPL_SPEC_QUESTION tsq1, TPL_SPEC_QUESTION tsq2 , SC_QUOTE_DOC sqd2 , sc_quote sq1
            WHERE tsq1.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
            AND tsq1.question_type = 'I'
            AND tsq1.PARENT_SPEC_QUESTION_ID = tsq2.spec_question_id(+)
            AND sqd2.spec_master_id = tsq2.SPEC_MASTER_ID 
            AND sqd2.QUOTE_ID = sq1.QUOTE_ID 
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq1.spec_question_id(+)) AS item_clone,
            (SELECT uom_code
            FROM TPL_SPEC_QUESTION tsq5, pm_uom uom
            WHERE tsq5.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND uom.uom_id = tsq5.cl_uom_id
            AND tsq5.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq5.spec_question_id(+)) AS uom_code,
            (SELECT uom_name
            FROM TPL_SPEC_QUESTION tsq5, pm_uom uom
            WHERE tsq5.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND uom.uom_id = tsq5.cl_uom_id
            AND tsq5.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq5.spec_question_id(+)) AS uom_name,
            (SELECT DECODE (tpm.ITEM_TYPE_ID , 199, 'Perkhidmatan', 198, 'Produk' )
            FROM tpl_spec_master tpm
            WHERE tpm.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID) AS item_type,
            (SELECT DECODE (tsq2.cl_fulfilment_type_id , 148, 'One-Off', 149, 'Bermasa (Berjadual)', 150, 'Bermasa (Bila Perlu)' )
            FROM TPL_SPEC_QUESTION tsq2
            WHERE tsq2.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND tsq2.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq2.spec_question_id(+)) AS jenis_pemenuhan,
            (SELECT pmd.code_name
            FROM TPL_SPEC_QUESTION tsq3, pm_parameter_desc pmd
            WHERE tsq3.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND tsq3.question_type = 'I'
            AND pmd.PARAMETER_ID = tsq3.CL_PRICE_TYPE_ID 
            AND pmd.LANGUAGE_CODE = 'en'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq3.spec_question_id(+)) AS code_name,
            (SELECT cl_quantity
            FROM TPL_SPEC_QUESTION tsq4
            WHERE tsq4.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID
            AND tsq4.question_type = 'I'
            AND tsq.PARENT_SPEC_QUESTION_ID = tsq4.spec_question_id(+)) AS kuantiti,
            tsq.CL_SPEC_DETAIL
            FROM TPL_SPEC_QUESTION tsq, sc_quote_doc sqd , sc_quote sq
            WHERE tsq.SPEC_MASTER_ID = sqd.SPEC_MASTER_ID 
            AND sqd.quote_id = ?  
            AND sqd.quote_id = sq.quote_id
            AND tsq.question_type = 'S'
         ",
            array($sq_id, $sq_id1, $sq_id2)
        );
        return $results;
    }

    protected function getRNno($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT a.quote_id,b.request_note_id,DECODE (a.is_multiple_quote, 0, 'Tawaran Tunggal', 1, 'Tawaran Berulang' ) AS jenis_pelawaan_tawaran_harga,DECODE (a.procurement_type_cat_id, 815, 'Bekalan', 816, 'Perkhidmatan' ) AS kategorijenisperolehan,DECODE (a.procurement_type_id, 225, 'Normal', 226, 'Segera',227, 'G2G', 228,'Special Item / PK7' ) AS jenisperolehan, DECODE (a.is_panel, 1, 'Yes', 0, 'No') AS ispanel, DECODE (a.is_zonal, 1, 'Yes', 0, 'No') as is_zonal1,a.is_zonal,pmstatus.status_name as status, a.quote_no,a.title,b.request_note_no, b.title, b.created_date, 
            to_char(a.START_DATE,'dd/mm/yyyy FMHH12:MI') AS START_DATE, CASE WHEN to_char(a.START_DATE,'HH24:MI:SS') >= '12:00:00' THEN 'PM' ELSE 'AM' END AS DATE1, to_char(a.END_DATE,'dd/mm/yyyy FMHH12:MI') as END_DATE,CASE WHEN to_char(a.END_DATE,'HH24:MI:SS') >= '12:00:00' THEN 'PM' ELSE 'AM' END AS DATE2, pmstatus1.status_name, pov.org_code, pov.org_name, pu.login_id, pu.user_name, pu.designation,pu1.login_id as login, pu1.user_name as approver, b.USER_GROUP_ID, pug.GROUP_NAME  
                       FROM sc_quote a, sc_request_note b,pm_status_desc pmstatus, SC_WORKFLOW_STATUS workf,  pm_status_desc pmstatus1 , SC_WORKFLOW_STATUS workf1, pm_org_validity pov, pm_user pu,pm_user pu1, PM_USER_GROUP pug
                       WHERE a.quote_id = b.quote_id 
                           and b.created_by = pu.user_id
                           and b.APPROVER_ID = pu1.USER_ID(+)
                           and b.org_profile_id = pov.org_profile_id
                           and pov.record_status = 1
                           and workf.status_id = pmstatus.status_id(+)
                           and workf.is_current(+) = 1
                           AND b.USER_GROUP_ID = pug.USER_GROUP_ID (+)
                           and pmstatus.language_code(+) = 'ms'
                           and a.quote_id = workf.doc_id(+)
                           and b.request_note_id = workf1.doc_id(+)
                           and workf1.status_id = pmstatus1.status_id(+)
                           and workf1.is_current(+) = 1
                           and pmstatus1.language_code(+) = 'ms'
                           and request_note_no = ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getActorList($RN_no, $RN_no1)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT sa.ROLE_CODE , pu.LOGIN_ID , pu.USER_NAME  
            FROM SC_ACTOR sa , SC_REQUEST_NOTE b, PM_USER pu
            WHERE b.REQUEST_NOTE_ID = sa.DOC_ID 
            AND pu.USER_ID = sa.USER_ID 
            AND sa.DOC_TYPE = 'RN'
            AND sa.ROLE_CODE = 'RN_APPROVER'
            AND b.request_note_no = ?
            union
            SELECT sa.ROLE_CODE , pu.LOGIN_ID , pu.USER_NAME  
            FROM SC_ACTOR sa , SC_REQUEST_NOTE b, PM_USER pu, SC_QUOTE sq 
            WHERE sq.QUOTE_ID = b.QUOTE_ID 
            AND sq.QUOTE_ID  = sa.DOC_ID 
            AND pu.USER_ID = sa.USER_ID 
            AND sa.DOC_TYPE = 'SQ'
            AND sa.ROLE_CODE = 'REQUISITIONER'
            AND b.request_note_no = ?
         ",
            array($RN_no, $RN_no1)
        );
        return $results;
    }

    protected function getRNno2($RN_no)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT NULL AS is_zonal, NULL AS quote_no, null as status, null as ispanel, null as is_zonal1, null as start_date, null as end_date,
            null as jenis_pelawaan_tawaran_harga, b.quote_id,  b.request_note_id,b.request_note_no, b.title, b.created_date,   pmstatus1.status_name, pov.org_code, pov.org_name, 
            pu.login_id, pu.user_name, pu.designation,pu1.login_id as login, pu1.user_name as approver, b.USER_GROUP_ID, pug.GROUP_NAME  ,
            DECODE (dtl.procurement_type_cat_id, 815, 'Bekalan', 816, 'Perkhidmatan' ) AS kategorijenisperolehan,DECODE (dtl.procurement_type_id, 225, 'Normal', 226, 'Segera',227, 'G2G', 228,'Special Item / PK7' ) AS jenisperolehan
                            FROM sc_request_note b,  pm_status_desc pmstatus1 , SC_WORKFLOW_STATUS workf1, pm_org_validity pov, pm_user pu,pm_user pu1, PM_USER_GROUP pug, SC_REQUEST_NOTE_DTL dtl
                            WHERE b.created_by = pu.user_id
                                and b.APPROVER_ID = pu1.USER_ID(+)
                                and b.org_profile_id = pov.org_profile_id
                                and pov.record_status = 1
                                AND b.USER_GROUP_ID = pug.USER_GROUP_ID (+)
                                and b.request_note_id = workf1.doc_id(+)
                                and workf1.status_id = pmstatus1.status_id(+)
                                and workf1.is_current(+) = 1
                                and pmstatus1.language_code(+) = 'ms'
                                AND dtl.request_note_id = b.REQUEST_NOTE_ID 
                                and request_note_no =  ?
         ",
            array($RN_no)
        );
        return $results;
    }

    protected function getTaskUser($sq, $rn, $loa, $RN_No)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "  SELECT sq.QUOTE_NO AS doc_no FROM SC_WORKFLOW_STATUS st, SC_QUOTE sq, SC_REQUEST_NOTE rn
            WHERE rn.QUOTE_ID  = sq.QUOTE_ID 
            AND sq.quote_id = st.DOC_ID 
            AND IS_current = 1 
            AND st.doc_type IN ('SQ')
            AND STATUS_ID IN (60850, 60852)
            AND rn.REQUEST_NOTE_NO = ?
            union
SELECT rn.REQUEST_NOTE_NO AS doc_no FROM SC_WORKFLOW_STATUS st, SC_REQUEST_NOTE rn
            WHERE rn.REQUEST_NOTE_ID  = st.DOC_ID
            AND IS_current = 1 
            AND st.doc_type IN ('RN')
            AND STATUS_ID IN (60700, 60701,60702, 60707, 60708)
            AND rn.REQUEST_NOTE_NO = ?
            UNION 
            SELECT loa.LOA_NO AS doc_no
            FROM sc_loa loa, sc_loi_loa loi, SC_PURCHASE_REQUEST sp, SC_REQUEST_NOTE rn, SC_WORKFLOW_STATUS st
            WHERE rn.REQUEST_NOTE_ID = sp.REQUEST_NOTE_ID 
            AND sp.PURCHASE_REQUEST_ID = loi.DOC_ID 
            AND loa.LOI_LOA_ID = loi.LOI_LOA_ID 
            AND loa.LOA_ID = st.DOC_ID
            AND st.doc_type IN ('LA')
            AND status_id IN (62501, 62502, 62504, 62505, 62508, 62520, 62521)
            AND rn.REQUEST_NOTE_NO = ?
            union
            SELECT rn.REQUEST_NOTE_NO AS doc_no
            FROM sc_loa loa, sc_loi_loa loi, SC_PURCHASE_REQUEST sp, SC_REQUEST_NOTE rn, SC_WORKFLOW_STATUS st
            WHERE rn.REQUEST_NOTE_ID = sp.REQUEST_NOTE_ID 
            AND sp.PURCHASE_REQUEST_ID = loi.DOC_ID 
            AND loa.LOI_LOA_ID = loi.LOI_LOA_ID 
            AND loa.LOA_ID = st.DOC_ID
            AND st.doc_type IN ('LA')
            AND status_id IN (62500)
            AND rn.REQUEST_NOTE_NO = ?
         ",
            array($sq, $rn, $loa, $RN_No)
        );
        return $results;
    }

    protected function findTaskuserOrder($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT rn.REQUEST_NOTE_NO AS doc_no
        FROM SC_PURCHASE_REQUEST sp, SC_REQUEST_NOTE rn, FL_FULFILMENT_REQUEST pr
        WHERE rn.REQUEST_NOTE_ID = sp.REQUEST_NOTE_ID
        AND pr.PURCHASE_REQUEST_ID = sp.PURCHASE_REQUEST_ID 
        AND rn.REQUEST_NOTE_NO = ? ", array($docNo));
        return $query;
    }

    protected function dpTrackingDiary($docNo, $rn_no, $la)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT DOC_TYPE , DOC_NO , ACTION_DESC , ACTIONED_DATE , STATUS_NAME 
FROM (
    SELECT pm.DOC_TYPE , pm.DOC_NO , pm.ACTION_DESC , pm.ACTIONED_DATE, des.STATUS_NAME 
    FROM PM_TRACKING_DIARY pm, pm_status_desc des
    WHERE pm.status_id = des.status_id
    AND des.language_code = 'en'
    AND doc_no = (
        SELECT quote_no 
        FROM sc_quote sq, SC_REQUEST_NOTE rn
        WHERE sq.QUOTE_ID(+) = rn.QUOTE_ID 
        AND rn.REQUEST_NOTE_NO = ?
    )
    UNION
    SELECT pm.DOC_TYPE , pm.DOC_NO , pm.ACTION_DESC , pm.ACTIONED_DATE , des.STATUS_NAME 
    FROM PM_TRACKING_DIARY  pm, pm_status_desc des
    WHERE pm.status_id = des.status_id
    AND des.language_code = 'en'
    and doc_no = ? 
    AND group_doc_type = 'SQ'
    UNION 
    SELECT pm.DOC_TYPE , pm.DOC_NO , pm.ACTION_DESC , pm.ACTIONED_DATE,des.STATUS_NAME 
    FROM PM_TRACKING_DIARY  pm, pm_status_desc des
    WHERE pm.status_id = des.status_id
    AND des.language_code = 'en'
    AND GROUP_ID  IN (SELECT doc_id FROM PM_TRACKING_DIARY WHERE doc_no = ?)
    AND DOC_TYPE = 'LA'
) CombinedResults
ORDER BY ACTIONED_DATE desc ", array($docNo, $rn_no, $la));
        return $query;
    }

    protected function dpTrackingDiary2($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pm.DOC_TYPE , pm.DOC_NO , pm.ACTION_DESC , to_char(pm.ACTIONED_DATE, 'dd/mm/YYYY HH24:MI:SS') as ACTIONED_DATE , des.STATUS_NAME 
                            FROM  PM_TRACKING_DIARY pm, pm_status_desc des
    						WHERE pm.status_id = des.status_id 
    						AND des.language_code = 'en'
    						AND  DOC_NO = ?
                            ORDER BY ACTIONED_DATE desc ", array($docNo));
        return $query;
    }

    protected function getDetailiklanDP($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct  b.request_note_no,reqitem.request_item_id, reqitem.item_name, reqitem.item_desc, reqitem.order_qty, reqitem.temp_item_code, ci.EXTENSION_CODE , uom.UOM_CODE, uom.UOM_NAME, CASE WHEN fulfilment_type_id = 149 THEN 'Bermasa (Berjadual)' WHEN fulfilment_type_id = 148  THEN 'One-Off' WHEN fulfilment_type_id = 150  THEN 'Bermasa (Bila Perlu)' END  jenis_pemenuhan
        FROM sc_quote a, sc_request_note b, sc_request_item reqitem,pm_uom uom, sc_request_note_dtl dtl, CM_ITEM ci 
        WHERE a.quote_id(+) = b.quote_id 
        and uom.uom_id(+) = reqitem.uom_id
        AND ci.ITEM_ID(+) = reqitem.ITEM_ID 
            and b.request_note_id = reqitem.request_note_id
            AND b.request_note_id = dtl.request_note_id
            AND reqitem.request_item_id = dtl.request_item_id
            AND b.request_note_no = ? 
            ORDER BY request_item_id asc", array($docNo));
        return $query;
    }

    protected function getSupplieriklanDP($itemid, $docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        select ranking,supplier_name, mof_no, item_name, b.request_item_id as tid, a.request_note_no, pri.UNIT_PRICE , c.REQUEST_SUPPLIER_ITEM_ID 
        from sc_request_supplier_item c, sc_request_item b , sc_request_note a, TPL_SPEC_QUESTION QUES , 
 TPL_SPEC_ANSWER ANS,TPL_SPEC_SUPP_ANSWER SUPP, SC_SUPPLIER_ITEM_PRICE pri
        where c.REQUEST_ITEM_ID = b.REQUEST_ITEM_ID
        and b.request_note_id = a.request_note_id
       AND b.SPEC_QUESTION_ID = QUES.SPEC_QUESTION_ID
        AND QUES.SPEC_QUESTION_ID = ANS.SPEC_QUESTION_ID
        AND ANS.SPEC_SUPP_ANSWER_ID = SUPP.SPEC_SUPP_ANSWER_ID
        AND supp.SUPPLIER_ID = c.supplier_id
        AND c.REQUEST_SUPPLIER_ITEM_ID = pri.REQUEST_SUPPLIER_ITEM_ID 
        and b.request_item_id = ?
        and a.request_note_no = ?  
        ", array($itemid, $docNo));
        return $query;
    }

    protected function getDetailiextension($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select to_char(scnote.created_date, 'YYYY') AS year, scnote.org_profile_id, scitem.item_name, c.item_name as group_name, substr(to_char(c.extension_code) , 1, 8) as extension_code
                        from sc_request_note scnote, sc_request_item scitem, cm_item c
                        where scitem.request_note_id = scnote.request_note_id
                            and c.item_id = scitem.item_id(+)
                            and scnote.request_note_no = ? ", array($docNo));
        return $query;
    }

    protected function getDetailSupplierDP($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT  DECODE (C.IS_SUBMITTED, 0, 'NO', 1, 'YES') SUBMITTED, c.supplier_name, c.mof_no, ss.EP_NO 
        FROM sc_quote a, sc_request_note b, SC_QUOTE_SUPPLIER c, SM_SUPPLIER ss 
        WHERE a.quote_id(+) = b.quote_id 
        AND ss.supplier_id = c.SUPPLIER_ID 
            and b.quote_id = c.QUOTE_ID(+)
            AND b.request_note_no = ? 
            order by SUBMITTED desc", array($docNo));
        return $query;
    }

    protected function getDetailPTJ($rn_no, $docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct '' AS ptj, pov.ORG_CODE , pov.ORG_NAME , b.request_note_no, pmgrp.org_profile_id, pmgrp.role_code,pmgrp.group_name,pmgrp.group_code, DECODE (pmgrp.record_status, 0, 'NOT ACTIVE', 1, 'ACTIVE') group_status 
        FROM sc_quote a, sc_request_note b, pm_user_group pmgrp, PM_ORG_VALIDITY pov 
        WHERE a.quote_id(+) = b.quote_id
            and pmgrp.ORG_PROFILE_ID = b.ORG_PROFILE_ID
            AND b.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
            AND pov.RECORD_STATUS = 1
            and pmgrp.role_code = 'RN_APPROVER'
            AND b.request_note_no = ?
            union
            SELECT 'NAIK TARAF' AS ptj, pov.ORG_CODE , pov.ORG_NAME , b.request_note_no, pmgrp.org_profile_id, pmgrp.role_code,pmgrp.group_name,pmgrp.group_code, DECODE (pmgrp.record_status, 0, 'NOT ACTIVE', 1, 'ACTIVE') group_status
            FROM sc_quote a, sc_request_note b, pm_user_group pmgrp, PM_PROFILE_GROUP ppg, PM_ORG_PROFILE pop, PM_ORG_VALIDITY pov 
            WHERE a.quote_id(+) = b.quote_id
            AND b.ORG_PROFILE_ID = ppg.AUTHORIZED_PROFILE_ID
            AND ppg.MAIN_PROFILE_ID = pop.ORG_PROFILE_ID
            AND pop.ORG_PROFILE_ID = PMGRP.ORG_PROFILE_ID 
            AND ppg.main_profile_id = pov.ORG_PROFILE_ID 
            AND pov.RECORD_STATUS = 1
            and pmgrp.record_status = 1
            and pmgrp.role_code = 'RN_APPROVER' 
            AND b.request_note_no =  ? ", array($rn_no, $docNo));
        return $query;
    }

    protected function getDetailRNapprover($grp, $docNo, $grp1, $docNo1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct  pmgrp.group_code, pm.login_id, pm.user_name, b.request_note_no
        FROM sc_request_note b, pm_user_group pmgrp, pm_user_group_user pmu, pm_user pm
        WHERE pmu.user_group_id = pmgrp.user_group_id
            and pmgrp.ORG_PROFILE_ID = b.ORG_PROFILE_ID
            and pm.user_id = pmu.user_id
            and pmgrp.group_code = ?
            and pmgrp.role_code = 'RN_APPROVER'
            and pmu.record_status = 1
            AND b.request_note_no =  ?
            union
             SELECT distinct  pmgrp.group_code, pm.login_id, pm.user_name, b.request_note_no
        FROM sc_request_note b, pm_user_group pmgrp, pm_user_group_user pmu, pm_user pm, PM_PROFILE_GROUP ppg, PM_ORG_PROFILE pop
        WHERE pmu.user_group_id = pmgrp.user_group_id
        AND b.ORG_PROFILE_ID = ppg.AUTHORIZED_PROFILE_ID 
            and pmgrp.ORG_PROFILE_ID = pop.ORG_PROFILE_ID
            AND ppg.MAIN_PROFILE_ID = pop.ORG_PROFILE_ID
            and pm.user_id = pmu.user_id
            and pmgrp.group_code = ?
            and pmgrp.role_code = 'RN_APPROVER'
            and pmu.record_status = 1
            AND b.request_note_no = ? ", array($grp, $docNo, $grp1, $docNo1));
        return $query;
    }

    protected function getDetailFLapprover($grp, $docNo, $grp1, $docNo1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct  pmgrp.group_code, pm.login_id, pm.user_name, b.DOC_NO , pur.APPROVAL_LIMIT 
        FROM FL_FULFILMENT_REQUEST b, pm_user_group pmgrp, pm_user_group_user pmu, pm_user pm, PM_USER_ORG puo , PM_USER_ROLE pur
        WHERE pmu.user_group_id = pmgrp.user_group_id
            and pmgrp.ORG_PROFILE_ID = b.CREATED_ORG_PROFILE_ID 
            and pm.user_id = pmu.user_id
            AND pm.USER_ID  = puo.user_id
            AND puo.user_org_id = pur.user_org_id
            and pmgrp.group_code = ?
            and pmgrp.role_code = 'FL_APPROVER'
            and pur.role_code = 'FL_APPROVER'
            and pmu.record_status = 1
            AND puo.RECORD_STATUS = 1
            AND b.DOC_NO =  ?
            union
             SELECT distinct  pmgrp.group_code, pm.login_id, pm.user_name, b.doc_no,pur.APPROVAL_LIMIT 
        FROM FL_FULFILMENT_REQUEST  b, pm_user_group pmgrp, pm_user_group_user pmu, pm_user pm, PM_PROFILE_GROUP ppg, PM_ORG_PROFILE pop, PM_USER_ORG puo , PM_USER_ROLE pur 
        WHERE pmu.user_group_id = pmgrp.user_group_id
        AND b.CREATED_ORG_PROFILE_ID = ppg.AUTHORIZED_PROFILE_ID 
            and pmgrp.ORG_PROFILE_ID = pop.ORG_PROFILE_ID
            AND ppg.MAIN_PROFILE_ID = pop.ORG_PROFILE_ID
            and pm.user_id = pmu.user_id
            AND pm.USER_ID  = puo.user_id
            AND puo.user_org_id = pur.user_org_id
            and pmgrp.group_code = ?
            and pmgrp.role_code = 'FL_APPROVER'
            and pur.role_code = 'FL_APPROVER'
            and pmu.record_status = 1
            AND puo.RECORD_STATUS = 1
            AND b.doc_no =  ? ", array($grp, $docNo, $grp1, $docNo1));
        return $query;
    }

    protected function getDetailROapprover($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select pat.created_date,pat.address_id, pat.changed_date,pat.org_profile_id,pu.login_id,pu.user_name,pm.address_name, pm.address_1,pm.address_2,pm.address_3,pm.postcode
                        from pm_address_type pat , pm_user pu, fl_fulfilment_request req, fl_delivery_address addr, pm_address pm 
                        where pat.USER_ID = pu.USER_ID
                            and req.fulfilment_req_id = addr.fulfilment_req_id
                            and pat.address_id = addr.pm_address_id 
                            and  address_type = 'R'
                            and pat.record_status = 1
                            and pu.record_status = 1
                            and pat.address_id = pm.address_id
                            and req.doc_no =  ? ", array($docNo));
        return $query;
    }

    protected function getDetailSupplierItems($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct reqdtl.request_note_dtl_id , LENGTH(PROCUREMENT_TITLE) AS len, reqdtl.procurement_title,reqdtl.request_item_id as reqdetailid,rsi.REQUEST_ITEM_ID as rsi_request_item , reqdtl.request_note_id, rsi.request_supplier_item_id ,flreq.doc_no, pmdesc.code_name ,pmdesc1.code_name as pcode, rsi.supplier_name, smupp.ep_no, reqitem.item_name, reqitem.order_qty, reqdtl.delivery_term, pmuser.user_name, case when reqdtl.REQUEST_ITEM_ID <> rsi.REQUEST_ITEM_ID then '!' end  as try,
                    (SELECT status_name FROM FL_WORKFLOW_STATUS flsta, pm_status_desc des WHERE flreq.fulfilment_req_id = flsta.doc_id AND des.STATUS_ID = flsta.STATUS_ID AND flsta.DOC_TYPE ='PR' AND flsta.IS_CURRENT= 1 AND des.LANGUAGE_CODE= 'en') status_pr,
                    (SELECT des.status_id FROM FL_WORKFLOW_STATUS flsta, pm_status_desc des WHERE flreq.fulfilment_req_id = flsta.doc_id AND des.STATUS_ID = flsta.STATUS_ID AND flsta.DOC_TYPE ='PR' AND flsta.IS_CURRENT= 1 AND des.LANGUAGE_CODE= 'en') status_id                   
                        from sc_request_note_dtl reqdtl,   fl_fulfilment_request flreq , pm_parameter_desc pmdesc, pm_parameter_desc pmdesc1, sc_quote scquote, sm_supplier smupp, sc_request_item reqitem, sc_request_note reqnote, sc_supplier_item_price supprice, pm_user pmuser, SC_REQUEST_SUPPLIER_ITEM rsi                 
                        where reqdtl.purchase_request_id = flreq.purchase_request_id(+)
                            and reqdtl.fulfilment_type_id = pmdesc.PARAMETER_ID(+)
                            and reqdtl.procurement_type_cat_id = pmdesc1.PARAMETER_ID(+)
                            and reqnote.quote_id = scquote.quote_id(+)
                            and reqnote.request_note_id = reqdtl.request_note_id
                            and reqitem.request_item_id = reqdtl.request_item_id
                            and reqitem.request_note_id = reqdtl.request_note_id
                            and reqdtl.REQUEST_SUPPLIER_ITEM_ID = rsi.REQUEST_SUPPLIER_ITEM_ID
                            and pmdesc.language_code(+) = 'ms'
                            and pmdesc1.language_code(+) = 'ms'
                            and flreq.supplier_id = smupp.supplier_id(+)
                            and reqitem.request_item_id = reqdtl.request_item_id
                            and reqdtl.request_supplier_item_id = supprice.request_supplier_item_id(+)
                            and pmuser.user_id = reqdtl.assign_to(+)
                            AND reqdtl.FULFILMENT_TYPE_ID = 148
                            and reqnote.request_note_no = ?
                        order by rsi.supplier_name", array($docNo));
        return $query;
    }

    protected function getDetailSupplierItemproblem($docNo, $supplierId)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("  
         select distinct rsii.supplier_id,rsii.request_supplier_item_id as requestsupplieritemid, rsii.request_item_id as req_item_id, req.request_note_id, reqdtl.request_note_dtl_id
                        from SC_REQUEST_SUPPLIER_ITEM rsii,sc_request_item scri, sc_request_note req, sc_request_note_dtl reqdtl
                        where req.request_note_id = scri.request_note_id  --1
                            and scri.request_note_id = reqdtl.request_note_id
                            and scri.request_item_id = reqdtl.request_item_id
                            and rsii.request_item_id = scri.request_item_id 
                            and rsii.request_item_id in (?) 
                            AND rsii.SUPPLIER_ID IN (?)
    ", array($docNo, $supplierId));
        return $query;
    }

    protected function getDetailSupplierItemproblemOut($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("   
                    select  distinct reqdtl.request_item_id as reqdetailid , rsi.SUPPLIER_ID             
                        from sc_request_note_dtl reqdtl,  fl_fulfilment_request flreq , pm_parameter_desc pmdesc, pm_parameter_desc pmdesc1, sc_quote scquote,   
                        sm_supplier smupp, sc_request_item reqitem, sc_request_note reqnote, sc_supplier_item_price supprice, pm_user pmuser, SC_REQUEST_SUPPLIER_ITEM rsi                 
                        where reqdtl.purchase_request_id = flreq.purchase_request_id(+)
                            and reqdtl.fulfilment_type_id = pmdesc.PARAMETER_ID(+)
                            and reqdtl.procurement_type_cat_id = pmdesc1.PARAMETER_ID(+)
                            and reqnote.quote_id = scquote.quote_id(+)
                            and reqnote.request_note_id = reqdtl.request_note_id
                            and reqitem.request_item_id = reqdtl.request_item_id
                            and reqitem.request_note_id = reqdtl.request_note_id
                            and reqdtl.REQUEST_SUPPLIER_ITEM_ID = rsi.REQUEST_SUPPLIER_ITEM_ID
                            and pmdesc.language_code(+) = 'ms'
                            and pmdesc1.language_code(+) = 'ms'
                            and flreq.supplier_id = smupp.supplier_id(+)
                            and reqitem.request_item_id = reqdtl.request_item_id
                            and reqdtl.request_supplier_item_id = supprice.request_supplier_item_id(+)
                            and pmuser.user_id = reqdtl.assign_to(+)
                            and reqdtl.request_item_id <> rsi.REQUEST_ITEM_ID 
                            and reqnote.request_note_no =  ? ", array($docNo));
        return $query;
    }

    protected function listFLAddrErrorPrice($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT name, fulfilment_item_addr_id, latest_supplier_item_price_id , supplier_item_price_id , unit_price, order_kuantiti,addr_request_item_id,total FROM (SELECT 
(select item_name from sc_request_item where request_item_id = addr.request_item_id) name, addr.fulfilment_item_addr_id , addr.request_item_id as addr_request_item_id, rsi.request_item_id, addr.supplier_item_price_id ,
(SELECT ssip1.supplier_item_price_id
FROM SC_REQUEST_SUPPLIER_ITEM sri1, sc_supplier_item_price ssip1 ,  SC_PURCHASE_REQUEST spr1, sc_request_note note1
WHERE sri1.REQUEST_SUPPLIER_ITEM_ID = ssip1.REQUEST_SUPPLIER_ITEM_ID
AND sri1.supplier_id = pr.SUPPLIER_ID 
AND spr1.PURCHASE_REQUEST_ID = pr.PURCHASE_REQUEST_ID 
AND note1.REQUEST_NOTE_ID = spr1.request_note_id
AND note1.request_note_no = reqnote.request_note_no
AND sri1.REQUEST_ITEM_ID = addr.request_item_id) AS latest_supplier_item_price_id,
(SELECT ssip1.unit_price
FROM SC_REQUEST_SUPPLIER_ITEM sri1, sc_supplier_item_price ssip1 ,  SC_PURCHASE_REQUEST spr1, sc_request_note note1
WHERE sri1.REQUEST_SUPPLIER_ITEM_ID = ssip1.REQUEST_SUPPLIER_ITEM_ID
AND sri1.supplier_id = pr.SUPPLIER_ID 
AND spr1.PURCHASE_REQUEST_ID = pr.PURCHASE_REQUEST_ID 
AND note1.REQUEST_NOTE_ID = spr1.request_note_id
AND note1.request_note_no = reqnote.request_note_no
AND sri1.REQUEST_ITEM_ID = addr.request_item_id ) AS unit_price,
(SELECT addr.ORDERED_QTY
FROM SC_REQUEST_SUPPLIER_ITEM sri1, sc_supplier_item_price ssip1 ,  SC_PURCHASE_REQUEST spr1, sc_request_note note1
WHERE sri1.REQUEST_SUPPLIER_ITEM_ID = ssip1.REQUEST_SUPPLIER_ITEM_ID
AND sri1.supplier_id = pr.SUPPLIER_ID 
AND spr1.PURCHASE_REQUEST_ID = pr.PURCHASE_REQUEST_ID 
AND note1.REQUEST_NOTE_ID = spr1.request_note_id
AND note1.request_note_no = reqnote.request_note_no
AND sri1.REQUEST_ITEM_ID = addr.request_item_id ) AS order_kuantiti,
(SELECT addr.ORDERED_QTY * ssip1.unit_price
FROM SC_REQUEST_SUPPLIER_ITEM sri1, sc_supplier_item_price ssip1 , SC_PURCHASE_REQUEST spr1, sc_request_note note1
WHERE sri1.REQUEST_SUPPLIER_ITEM_ID = ssip1.REQUEST_SUPPLIER_ITEM_ID
AND sri1.supplier_id = pr.SUPPLIER_ID 
AND spr1.PURCHASE_REQUEST_ID = pr.PURCHASE_REQUEST_ID 
AND note1.REQUEST_NOTE_ID = spr1.request_note_id
AND note1.request_note_no = reqnote.request_note_no
AND sri1.REQUEST_ITEM_ID = addr.request_item_id ) AS total
FROM FL_FULFILMENT_ITEM_ADDR addr, sc_supplier_item_price ssip, sc_request_item reqitem, sc_request_note reqnote, sc_request_note_dtl reqdtl, SC_REQUEST_SUPPLIER_ITEM rsi, SC_PURCHASE_REQUEST spr1,
FL_FULFILMENT_REQUEST pr, SC_REQUEST_SUPPLIER_ITEM srsi
WHERE addr.SUPPLIER_ITEM_PRICE_ID  = ssip.SUPPLIER_ITEM_PRICE_ID 
AND addr.REQUEST_ITEM_ID = REQITEM.REQUEST_ITEM_ID
AND spr1.PURCHASE_REQUEST_ID = pr.PURCHASE_REQUEST_ID 
AND srsi.SUPPLIER_ID = pr.SUPPLIER_ID 
AND srsi.REQUEST_ITEM_ID = rsi.REQUEST_ITEM_ID 
AND srsi.REQUEST_SUPPLIER_ITEM_ID = ssip.REQUEST_SUPPLIER_ITEM_ID 
AND reqnote.REQUEST_NOTE_ID = spr1.request_note_id
AND reqdtl.request_note_id = reqnote.request_note_id
and reqitem.request_item_id = reqdtl.request_item_id
AND reqitem.request_item_id <> rsi.request_item_id
AND ssip.REQUEST_SUPPLIER_ITEM_ID = rsi.REQUEST_SUPPLIER_ITEM_ID
and reqnote.request_note_no = ?)", array($docNo));
        return $query;
    }

    // protected function getDetailSuppliererror($docNo)
    // {

    //     $query = DB::connection('oracle_nextgen_rpt')->select("
    //                 select (select item_name from sc_request_item where request_item_id = addr.request_item_id) name,addr.FULFILMENT_ADDR_ID, addr.request_item_id, addr.supplier_item_price_id , supprice.SUPPLIER_ITEM_PRICE_ID Latest_supplier_item_price, supprice.unit_price,(supprice.unit_price * addr.ordered_qty) as total
    //                     from sc_request_note reqnote, sc_request_note_dtl reqdtl, fl_fulfilment_request flreq, fl_delivery_address fladdress,fl_fulfilment_item_addr addr , sc_supplier_item_price supprice, sc_request_item reqitem
    //                     where reqdtl.request_note_id = reqnote.request_note_id
    //                         and reqitem.request_item_id = reqdtl.request_item_id
    //                         and reqitem.request_note_id = reqdtl.request_note_id
    //                         and reqitem.request_item_id = reqdtl.request_item_id
    //                         and flreq.purchase_request_id = reqdtl.purchase_request_id
    //                         and fladdress.fulfilment_req_id = flreq.fulfilment_req_id 
    //                         and addr.FULFILMENT_ADDR_ID = fladdress.delivery_address_id 
    //                         and addr.request_item_id  = reqdtl.request_item_id  
    //                         and reqdtl.request_supplier_item_id = supprice.request_supplier_item_id(+)
    //                         and addr.supplier_item_price_id <> supprice.SUPPLIER_ITEM_PRICE_ID
    //                         and reqnote.request_note_no = ?", array($docNo));
    //     return $query;
    // }

    // SELECT status_name, supplier_name, file_no, ppd_f.code_name AS FULFILMENT_TYPE_ID, loa_no, ISSUE_DATE , ppd_c.code_name AS PROCUREMENT_TYPE_CAT_ID, ppd_m.code_name AS PROCUREMENT_MODE_ID, ppd_ct.code_name AS CONTRACT_TYPE_ID,
    // CONTRACT_DURATION , CONTRACT_EFF_DATE , CONTRACT_EXP_DATE , ACK_DUE_DATE , IS_AGREEMENT_REQ , IS_BOND_REQ
    // FROM (
    //     SELECT des.status_name, sll.supplier_name, sll.file_no, sll.FULFILMENT_TYPE_ID,  loa.loa_no, to_char(loa.ISSUE_DATE,'dd-mm-YYYY') as ISSUE_DATE , loa.PROCUREMENT_TYPE_CAT_ID, loa.PROCUREMENT_MODE_ID, loa.CONTRACT_TYPE_ID,
    //     loa.CONTRACT_DURATION , to_char(loa.CONTRACT_EFF_DATE,'dd-mm-YYYY') as CONTRACT_EFF_DATE , to_char(loa.CONTRACT_EXP_DATE,'dd-mm-YYYY') as CONTRACT_EXP_DATE, to_char(loa.ACK_DUE_DATE,'dd-mm-YYYY') as ACK_DUE_DATE , CASE WHEN loa.IS_AGREEMENT_REQ = 1 THEN 'YES' ELSE 'NO' END AS IS_AGREEMENT_REQ, CASE WHEN loa.IS_BOND_REQ = 1 THEN 'YES' ELSE 'NO' END AS IS_BOND_REQ
    //     FROM
    //         SC_REQUEST_NOTE srn
    //     JOIN
    //         SC_PURCHASE_REQUEST spr ON srn.REQUEST_NOTE_ID = spr.REQUEST_NOTE_ID
    //     JOIN
    //         SC_LOI_LOA sll ON spr.PURCHASE_REQUEST_ID = sll.DOC_ID
    //     JOIN
    //         sc_loa loa ON sll.LOI_LOA_ID = loa.LOI_LOA_ID
    //     INNER JOIN SC_WORKFLOW_STATUS sta ON sta.DOC_ID  = loa.LOA_ID
    //     AND sta.DOC_TYPE  = 'LA' AND sta.IS_current = 1
    //     INNER JOIN PM_STATUS_DESC des ON des.STATUS_ID = sta.STATUS_ID 
    //     AND des.LANGUAGE_CODE = 'ms'
    //     WHERE
    //         srn.REQUEST_NOTE_NO = ?
    // ) subquery
    // JOIN
    //     PM_PARAMETER_DESC ppd_f ON subquery.FULFILMENT_TYPE_ID = ppd_f.PARAMETER_ID AND ppd_f.LANGUAGE_CODE = 'ms'
    // JOIN
    //     PM_PARAMETER_DESC ppd_c ON subquery.PROCUREMENT_TYPE_CAT_ID = ppd_c.PARAMETER_ID AND ppd_c.LANGUAGE_CODE = 'ms'
    // JOIN
    //     PM_PARAMETER_DESC ppd_m ON subquery.PROCUREMENT_MODE_ID = ppd_m.PARAMETER_ID AND ppd_m.LANGUAGE_CODE = 'ms'
    // JOIN
    //     PM_PARAMETER_DESC ppd_ct ON subquery.CONTRACT_TYPE_ID = ppd_ct.PARAMETER_ID AND ppd_ct.LANGUAGE_CODE = 'ms'

    protected function getLoaDetails($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT request_note_no, procurement_title, status_name, supplier_name, file_no,  loa_no, ISSUE_DATE ,  
        CONTRACT_DURATION , CONTRACT_EFF_DATE , CONTRACT_EXP_DATE , ACK_DUE_DATE , IS_AGREEMENT_REQ , IS_BOND_REQ, supplier_sign_date, supplier_ack, witness_name, 
        ppd_f.code_name AS FULFILMENT_TYPE_ID , ppd_c.code_name AS PROCUREMENT_TYPE_CAT_ID, ppd_m.code_name AS PROCUREMENT_MODE_ID, ppd_ct.code_name AS CONTRACT_TYPE_ID, user_name, login_id
        FROM (
            SELECT srn.request_note_no, dtl.procurement_title, des.status_name, sll.supplier_name, sll.file_no, sll.FULFILMENT_TYPE_ID,  loa.loa_no, to_char(loa.ISSUE_DATE,'dd-mm-YYYY') as ISSUE_DATE , loa.PROCUREMENT_TYPE_CAT_ID, loa.PROCUREMENT_MODE_ID, loa.CONTRACT_TYPE_ID,
            loa.CONTRACT_DURATION , to_char(loa.CONTRACT_EFF_DATE,'dd-mm-YYYY') as CONTRACT_EFF_DATE , to_char(loa.CONTRACT_EXP_DATE,'dd-mm-YYYY') as CONTRACT_EXP_DATE, to_char(loa.ACK_DUE_DATE,'dd-mm-YYYY') as ACK_DUE_DATE , CASE WHEN loa.IS_AGREEMENT_REQ = 1 THEN 'YES' ELSE 'NO' END AS IS_AGREEMENT_REQ, CASE WHEN loa.IS_BOND_REQ = 1 THEN 'YES' ELSE 'NO' END AS IS_BOND_REQ
            , pu.user_name, pu.login_id, to_char(loa.SUPPLIER_SIGN_DATE,'dd-mm-YYYY') AS SUPPLIER_SIGN_DATE , pu1.user_name as supplier_ack, slw.WITNESS_NAME 
            FROM SC_REQUEST_NOTE srn, SC_PURCHASE_REQUEST spr , SC_LOI_LOA sll, sc_loa loa , SC_WORKFLOW_STATUS sta, PM_STATUS_DESC des , SC_REQUEST_NOTE_DTL dtl, PM_USER pu , PM_USER pu1 , SC_LOA_WITNESS slw
            WHERE srn.REQUEST_NOTE_ID = spr.REQUEST_NOTE_ID
            AND srn.REQUEST_NOTE_ID = dtl.REQUEST_NOTE_ID 
            AND dtl.PURCHASE_REQUEST_ID = spr.PURCHASE_REQUEST_ID 
            AND spr.PURCHASE_REQUEST_ID = sll.DOC_ID
            AND sll.LOI_LOA_ID = loa.LOI_LOA_ID(+)
            AND sta.DOC_ID(+)  = loa.LOA_ID
            AND des.STATUS_ID(+) = sta.STATUS_ID 
            AND sta.DOC_TYPE(+)  = 'LA' AND sta.IS_current(+) = 1
            AND des.LANGUAGE_CODE(+) = 'ms'
            AND dtl.assign_to = pu.user_id
            AND loa.ACK_BY = pu1.USER_ID(+) 
            AND loa.LOA_ID = slw.loa_id(+)
               AND  srn.REQUEST_NOTE_NO =  ?
        ) subquery, PM_PARAMETER_DESC ppd_f, PM_PARAMETER_DESC ppd_c, PM_PARAMETER_DESC ppd_m, PM_PARAMETER_DESC ppd_ct
        WHERE subquery.FULFILMENT_TYPE_ID = ppd_f.PARAMETER_ID(+) 
        AND ppd_f.LANGUAGE_CODE(+) = 'ms'
        AND subquery.PROCUREMENT_TYPE_CAT_ID = ppd_c.PARAMETER_ID(+) 
        AND ppd_c.LANGUAGE_CODE(+) = 'ms'
        AND subquery.PROCUREMENT_MODE_ID = ppd_m.PARAMETER_ID(+) 
        AND ppd_m.LANGUAGE_CODE(+) = 'ms'
        AND subquery.CONTRACT_TYPE_ID = ppd_ct.PARAMETER_ID(+) 
        AND ppd_ct.LANGUAGE_CODE(+) = 'ms'
            ", array($docNo));
        return $query;
    }

    protected function checkRNSyor($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
         SELECT rn1.request_note_no FROM sc_request_note rn , sc_request_note rn1
        WHERE rn.quote_id = rn1.quote_id
        AND rn.REQUEST_NOTE_NO = ? ", array($docNo));
        return $query;
    }

    protected function getdetailLOAitem($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT loa.loa_no, scitem.REQUEST_ITEM_ID, item_name, litem.qty, cl_uom_frequency, cl_uom_per_day, cl_uom_per_month, cl_duration_month
        FROM SC_REQUEST_NOTE srn , SC_PURCHASE_REQUEST spr, SC_LOI_LOA sll, sc_loa loa, sc_loi_loa_item litem , SC_REQUEST_ITEM scitem, TPL_SPEC_QUESTION ques
        WHERE srn.REQUEST_NOTE_ID = spr.REQUEST_NOTE_ID
        AND sll.DOC_ID = spr.PURCHASE_REQUEST_ID 
        AND loa.LOI_LOA_ID = sll.LOI_LOA_ID 
        AND scitem.REQUEST_NOTE_ID  = srn.REQUEST_NOTE_ID 
        AND litem.REQUEST_ITEM_ID = scitem.REQUEST_ITEM_ID 
        AND ques.SPEC_QUESTION_ID = SCITEM.SPEC_QUESTION_ID 
        AND litem.loa_id = loa.loa_id
        AND REQUEST_NOTE_NO =? ", array($docNo));
        return $query;
    }

    protected function getSupplierRespond($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT sm.COMPANY_NAME , RATE_PER_UOM , ANSWER_NUMERIC, ranking, sub_proposal_no, SPEC_ZONE_dtl_id, tsq.CL_DELIVERY_TERM 
FROM TPL_SPEC_ANSWER tsa, TPL_SPEC_SUPP_ANSWER tssa, TPL_SPEC_QUESTION tsq, SC_QUOTE_DOC sqd, sc_quote sq, SM_SUPPLIER sm
WHERE tsa.SPEC_QUESTION_ID = tsq.SPEC_QUESTION_ID 
AND sq.QUOTE_ID = sqd.QUOTE_ID 
AND tsa.SPEC_SUPP_ANSWER_ID = tssa.SPEC_SUPP_ANSWER_ID 
AND sm.SUPPLIER_ID = tssa.SUPPLIER_ID 
AND sqd.SPEC_MASTER_ID = tsq.SPEC_MASTER_ID 
AND tsq.QUESTION_TYPE = 'I'
AND sq.QUOTE_ID = ? ", array($docNo));
        return $query;
    }

    protected function getInfoPRCR($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT  pav.OFFICE_NAME , sm.latest_appl_id, fsd.BRANCH_NAME, fsd.BRANCH_CODE,fr.supplier_id,fr.DATE_ADJUSTMENT ,fr.fulfilment_req_id, doc_no, DECODE (fr.procurement_type_id, 225, 'Normal', 226, 'Segera',227, 'G2G', 228,'Special Item / PK7' ) AS jenisperolehan,
        title, financial_year, fr.delivery_term, to_char(fr.created_date,'dd-mm-YYYY') AS created_date, to_char(fr.START_DATE ,'dd-mm-YYYY') AS START_DATE, to_char(fr.END_DATE,'dd-mm-YYYY') AS END_DATE, fr.ag_office_name, fr.PREPARED_ORG_PROFILE_ID,
     DECODE (fr.procurement_type_cat_id, 815, 'Bekalan', 816, 'Perkhidmatan' ) AS kategorijenisperolehan, pmval.org_code ptjcode, pmval.ORG_NAME ptjname, pmvalgroupptj.ORG_NAME groupptjname, 
     pmvalpengawal.ORG_NAME pengawalname, pmvalministry.ORG_NAME ministryname, pu.user_name, pu.login_id, COMPANY_NAME, reg_no, ep_no, 
     (SELECT USER_NAME  FROM PM_USER pu WHERE pu.USER_ID = fr.APPROVER_ID) AS app_name, (SELECT group_code FROM PM_USER_GROUP pug WHERE pug.USER_GROUP_ID = fr.USER_GROUP_ID) AS group_code, 
     (SELECT LOGIN_ID FROM PM_USER pu2 WHERE pu2.USER_ID = fr.APPROVER_ID) AS app_login
        FROM fl_fulfilment_request fr , pm_org_validity pmval, PM_ORG_profile ptj, PM_ORG_profile groupptj , PM_ORG_VALIDITY pmvalgroupptj, 
        PM_ORG_profile pengawal, PM_ORG_VALIDITY pmvalpengawal, PM_ORG_profile ministry,  PM_ORG_VALIDITY pmvalministry, pm_user pu, sm_supplier sm, FL_SUPPLIER_DTL fsd , PM_AG_VALIDITY pav 
        WHERE pmval.org_profile_id = fr.PREPARED_ORG_PROFILE_ID
        AND ptj.org_profile_id = fr.PREPARED_ORG_PROFILE_ID 
        AND groupptj.org_profile_id = ptj.PARENT_ORG_PROFILE_ID 
        AND pmvalgroupptj.org_profile_id = groupptj.org_profile_id
        AND pengawal.org_profile_id = groupptj.PARENT_ORG_PROFILE_ID
        AND pmvalpengawal.org_profile_id = pengawal.org_profile_id
        AND ministry.org_profile_id = pengawal.PARENT_ORG_PROFILE_ID
        AND pmvalministry.org_profile_id = ministry.org_profile_id
        AND fsd.FULFILMENT_REQ_ID = fr.FULFILMENT_REQ_ID
        AND fr.AG_OFFICE_ID = pav.AG_VALIDITY_ID 
        and fr.created_by = pu.user_id
        AND sm.SUPPLIER_ID = fr.SUPPLIER_ID 
        and pmval.record_status = 1
        and pmvalgroupptj.record_status = 1
        AND pmvalpengawal.record_status = 1
        AND pmvalministry.record_status = 1
        AND fr.doc_no =  ?
              ", array($docNo));
        return $query;
    }

    protected function getActorListPr($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT fa.ROLE_CODE , pu.LOGIN_ID , pu.USER_NAME  
                           FROM FL_ACTOR fa , FL_FULFILMENT_REQUEST b, PM_USER pu
                           WHERE b.FULFILMENT_REQ_ID  = fa.DOC_ID 
                           AND pu.USER_ID = fa.USER_ID 
                           AND fa.ROLE_CODE IN ('REQUISITIONER','FL_APPROVER')
                           AND b.doc_no = ?
              ", array($docNo));
        return $query;
    }

    protected function getFulfilmentApprover($docNo, $do)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct '' AS ptj, pov.ORG_CODE , pov.ORG_NAME , b.DOC_NO , pmgrp.org_profile_id, pmgrp.role_code,pmgrp.group_name,pmgrp.group_code, DECODE (pmgrp.record_status, 0, 'NOT ACTIVE', 1, 'ACTIVE') group_status 
        FROM FL_FULFILMENT_REQUEST b, pm_user_group pmgrp, PM_ORG_VALIDITY pov 
        WHERE pmgrp.ORG_PROFILE_ID = b.CREATED_ORG_PROFILE_ID 
            AND b.CREATED_ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
            AND pov.RECORD_STATUS = 1
            and pmgrp.role_code = 'FL_APPROVER'
            AND b.DOC_NO = ?
            union
            SELECT 'NAIK TARAF' AS ptj, pov.ORG_CODE , pov.ORG_NAME , b.DOC_NO, pmgrp.org_profile_id, pmgrp.role_code,pmgrp.group_name,pmgrp.group_code, DECODE (pmgrp.record_status, 0, 'NOT ACTIVE', 1, 'ACTIVE') group_status
            FROM FL_FULFILMENT_REQUEST b, pm_user_group pmgrp, PM_PROFILE_GROUP ppg, PM_ORG_PROFILE pop, PM_ORG_VALIDITY pov 
            WHERE b.CREATED_ORG_PROFILE_ID = ppg.AUTHORIZED_PROFILE_ID
            AND ppg.MAIN_PROFILE_ID = pop.ORG_PROFILE_ID
            AND pop.ORG_PROFILE_ID = PMGRP.ORG_PROFILE_ID 
            AND ppg.main_profile_id = pov.ORG_PROFILE_ID 
            AND pov.RECORD_STATUS = 1
            AND pmgrp.record_status = 1
            and pmgrp.role_code = 'FL_APPROVER' 
            AND b.doc_no = ?   
            ", array($docNo, $do));
        return $query;
    }

    protected function getPMOuser($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pur.ROLE_CODE , user_name, login_id 
FROM PM_USER_ROLE pur, pm_user_org pug, FL_FULFILMENT_REQUEST pr, pm_user pu
WHERE pug.USER_ORG_ID = pur.USER_ORG_ID 
AND pr.PREPARED_ORG_PROFILE_ID = pug.ORG_PROFILE_ID 
AND pug.record_status = 1
AND pur.RECORD_STATUS = 1
AND pu.USER_ID = pug.user_id
AND pur.role_code = 'PAY_OFFICER'
AND pr.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function getListFlActor($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, ffr.DOC_NO AS REFERENCE_NO 
       FROM fl_actor fla
       JOIN FL_FULFILMENT_REQUEST ffr ON fla.doc_id = ffr.FULFILMENT_REQ_ID 
       WHERE fla.doc_type IN ('PR', 'CR')
       AND ffr.doc_no = :docNo
       UNION ALL
       SELECT CASE 
           WHEN fla.ROLE_CODE = 'RECEIVING_OFFICER' THEN 'DO'
           WHEN fla.ROLE_CODE = 'ACKNOWLEDGE_OFFICER' THEN 'FN'
       END AS doc_type , fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, 
              CASE 
                  WHEN fla.ROLE_CODE = 'RECEIVING_OFFICER' THEN fdo.DELIVERY_ORDER_NO 
                  WHEN fla.ROLE_CODE = 'ACKNOWLEDGE_OFFICER' THEN frn.FULFILMENT_NOTE_NO 
              END AS REFERENCE_NO
       FROM fl_actor fla
       JOIN fl_fulfilment_note frn ON fla.doc_id = frn.FULFILMENT_NOTE_ID
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = frn.FULFILMENT_REQ_ID
       LEFT JOIN FL_DELIVERY_ORDER fdo ON fdo.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID
       WHERE fla.DOC_TYPE = 'FN'
       AND ffr.doc_no = :docNo
       UNION ALL
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, fdo.DELIVERY_ORDER_NO
       FROM fl_actor fla
       JOIN FL_DELIVERY_ORDER fdo ON fla.DOC_ID = fdo.DELIVERY_ORDER_ID
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = fdo.FULFILMENT_REQ_ID
       WHERE fla.DOC_TYPE = 'DO'
       AND ffr.DOC_NO = :docNo
       UNION ALL
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, fsi.STOP_INSTR_NO
       FROM fl_actor fla
       JOIN FL_STOP_INSTR fsi ON fla.DOC_ID = fsi.STOP_INSTR_ID 
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = fsi.FULFILMENT_REQ_ID
       WHERE fla.DOC_TYPE = 'SD'
       AND ffr.DOC_NO = :docNo
       UNION ALL
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, fi.INVOICE_NO
       FROM fl_actor fla
       JOIN FL_INVOICE fi ON fi.INVOICE_ID = fla.DOC_ID 
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = fi.FULFILMENT_REQ_ID 
       WHERE fla.DOC_TYPE = 'IN'
       AND ffr.DOC_NO = :docNo
       UNION ALL
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, fa.DOC_NO
       FROM fl_actor fla
       JOIN FL_ADJUSTMENT fa ON fa.ADJUSTMENT_ID = fla.DOC_ID  
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = fa.FULFILMENT_REQ_ID
       WHERE fla.DOC_TYPE = 'DN'
       AND ffr.DOC_NO = :docNo
       UNION ALL
       SELECT fla.doc_type, fla.ROLE_CODE, fla.USER_ID, fla.USER_NAME, fla.ACTION_DATE, fpa.PAYMENT_ADVICE_NO 
       FROM fl_actor fla
       JOIN FL_PAYMENT_ADVICE fpa ON fpa.PAYMENT_ADVICE_ID = fla.DOC_ID  
       JOIN FL_FULFILMENT_REQUEST ffr ON ffr.FULFILMENT_REQ_ID = fpa.FULFILMENT_REQ_ID
       WHERE fla.DOC_TYPE = 'PA'
       AND ffr.DOC_NO = :docNo
       ORDER BY ACTION_DATE", ['docNo' => $docNo]);
        return $query;
    }

    protected function getAouser($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pur.ROLE_CODE , user_name, login_id 
FROM PM_USER_ROLE pur, pm_user_org pug, FL_FULFILMENT_REQUEST pr, pm_user pu
WHERE pug.USER_ORG_ID = pur.USER_ORG_ID 
AND pr.PREPARED_ORG_PROFILE_ID = pug.ORG_PROFILE_ID 
AND pug.record_status = 1
AND pur.RECORD_STATUS = 1
AND pu.USER_ID = pug.user_id
AND pur.role_code = 'ACKNOWLEDGE_OFFICER'
AND pr.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function findListItemCode($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT DISTINCT addr.ITEM_CODE
FROM fl_fulfilment_request pr , fl_delivery_address ad, fl_fulfilment_item_addr addr, 
 FL_FULFILMENT_ITEM flitem
WHERE pr.fulfilment_req_id = ad.fulfilment_req_id
AND addr.FULFILMENT_ADDR_ID = ad.DELIVERY_ADDRESS_ID   
AND flitem.fulfilment_item_id(+) = addr.fulfilment_item_id
AND pr.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function getOSBitemCode($docNo)
    {
        $collectResult = collect();
        foreach ($docNo["item_code"] as $itemCode) {
            $listDataTrcDiary = $this->itemCodeArchived($itemCode);
            foreach ($listDataTrcDiary as $res) {
                $collectResult->push($res);
            }
        }
        return $collectResult->unique();
    }

    protected function itemCodeArchived($itemCode)
    {
        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')
                    ->table('OSB_LOGGING as osb')
                    ->join('OSB_LOGGING_DTL as dtl', 'osb.LOGGING_ID', '=', 'dtl.LOGGING_ID')
                    ->select('osb.TRANS_date', 'dtl.PAYLOAD_BODY')
                    ->where('osb.TRANS_TYPE', '=', 'IBReq')
                    ->whereIn('osb.TRANS_ID', function ($query) use ($itemCode) { // Add the "use ($itemCode)" part
                        $query->select('osb1.trans_id')
                            ->from('OSB_LOGGING as osb1')
                            ->where('osb1.remarks_1', $itemCode)
                            ->distinct();
                    })
                    ->whereNotIn('osb.TRANS_ID', function ($query) {
                        $query->select('osb2.TRANS_ID')
                            ->from('OSB_LOGGING as osb2')
                            ->where('osb2.STATUS', '<>', 'S')
                            ->where('osb2.trans_type', '=', 'OBRes-FAULT');
                    });
                $resultBak = $queryBak->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $query = DB::connection('oracle_nextgen_rpt')
            ->table('OSB_LOGGING as osb')
            ->join('OSB_LOGGING_DTL as dtl', 'osb.LOGGING_ID', '=', 'dtl.LOGGING_ID')
            ->select('osb.TRANS_date', 'dtl.PAYLOAD_BODY')
            ->where('osb.TRANS_TYPE', '=', 'IBReq')
            ->whereIn('osb.TRANS_ID', function ($query) use ($itemCode) { // Add the "use ($itemCode)" part
                $query->select('osb1.trans_id')
                    ->from('OSB_LOGGING as osb1')
                    ->where('osb1.remarks_1', $itemCode)
                    ->distinct();
            })
            ->whereNotIn('osb.TRANS_ID', function ($query) {
                $query->select('osb2.TRANS_ID')
                    ->from('OSB_LOGGING as osb2')
                    ->where('osb2.STATUS', '<>', 'S')
                    ->where('osb2.trans_type', '=', 'OBRes-FAULT');
            });

        $result = $query->get();

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        return $result;
    }

    protected function getListItemBeforeApproved($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pu.UOM_NAME , rn.request_item_id, ffia.ORDERED_QTY, ffia.UNIT_PRICE ,cmi.extension_code, rn.item_name, rn.item_desc, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' ||state_name AS address
            FROM FL_FULFILMENT_REQUEST pr, FL_FULFILMENT_ITEM_ADDR ffia, sc_request_item rn, fl_delivery_address fda, pm_address pm, pm_state state, cm_item cmi, PM_CITY pc , PM_UOM pu 
            WHERE pr.fulfilment_req_id = fda.fulfilment_req_id
            AND ffia.FULFILMENT_ADDR_ID = fda.DELIVERY_ADDRESS_ID 
            AND rn.item_id = cmi.item_id
            AND state.state_id = pm.state_id
            AND pc.city_id = pm.city_id
            AND fda.pm_address_id = pm.address_id
            AND ffia.REQUEST_ITEM_ID = rn.REQUEST_ITEM_ID 
            AND ffia.UOM_ID = pu.UOM_ID(+) 
            AND pr.doc_no =   ?", array($docNo));
        return $query;
    }

    protected function getFNItemParcial($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT distinct est_delivery_date , procuct_type, charge_line_seq, do_no, do_status_name,frn_no,frn_status_name, item_name,ordered_qty,uom_code, uom_name,delivered_qty, unit_price, ordered_amt, delivered_amt,frn_received_qty, frn_received_amt FROM (
            SELECT to_char(do.est_delivery_date,'dd-mm-YYYY') as est_delivery_date , case when fli.item_type_id = 199 then 'service' when fli.item_type_id = 198 then 'product' end procuct_type, charge_line_seq,
            (SELECT item_name FROM sc_request_item WHERE REQUEST_ITEM_ID = fli.REQUEST_ITEM_ID ) item_name, do.delivery_order_no AS do_no,frn.FULFILMENT_NOTE_NO AS frn_no,
            addr.ordered_qty,deli.delivered_qty, addr.unit_price, addr.ordered_amt, deli.delivered_amt, uom.uom_code, uom.uom_name, item.received_qty as frn_received_qty, item.received_amt as frn_received_amt,
            (SELECT do2.status_name 
            FROM fl_workflow_status do1, pm_status_desc do2 WHERE do.delivery_order_id = do1.doc_id AND do1.status_id = do2.status_id AND do1.is_current = 1
            AND do2.language_code = 'en' AND do1.doc_type IN ('DO')) do_status_name,
            (SELECT frn2.status_name FROM fl_workflow_status frn1, pm_status_desc frn2 WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND 
            frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND frn1.doc_type IN ('FN')) frn_status_name
            FROM FL_FULFILMENT_ITEM fli, FL_FULFILMENT_ITEM_ADDR addr,fl_delivery_order do, FL_DEL_ORDER_ITEM deli, fl_fulfilment_note frn, FL_FULFILMENT_REQUEST pr, pm_uom uom, fl_fulfilment_note_item item
            WHERE addr.fulfilment_item_id = fli.fulfilment_item_id 
            AND deli.fulfilment_item_addr_id = addr.fulfilment_item_addr_id
            AND deli.fulfilment_item_id = fli.fulfilment_item_id
            AND do.fulfilment_req_id = fli.fulfilment_req_id
            AND deli.delivery_order_id = do.delivery_order_id
            and do.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID(+)
            AND fli.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID
            and item.DEL_ORDER_ITEM_ID(+) = deli.DEL_ORDER_ITEM_ID
            --and item.FULFILMENT_NOTE_ID(+) = frn.FULFILMENT_NOTE_ID
            AND fli.uom_id = uom.UOM_ID(+) 
            AND pr.doc_no = ? ) 
            order by 1,3 asc", array($docNo));
        return $query;
    }

    protected function getDODetails($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT 
    do.delivery_order_no AS do_no, 
    frn.FULFILMENT_NOTE_NO AS frn_no,
    SUM(addr.ordered_amt) AS ordered_amt, 
    SUM(deli.delivered_amt) AS delivered_amt,  
    SUM(item.received_amt) AS frn_received_amt,
    (SELECT do2.status_name 
            FROM fl_workflow_status do1, pm_status_desc do2 WHERE do.delivery_order_id = do1.doc_id AND do1.status_id = do2.status_id AND do1.is_current = 1
            AND do2.language_code = 'en' AND do1.doc_type IN ('DO')) do_status_name,
            (SELECT frn2.status_name FROM fl_workflow_status frn1, pm_status_desc frn2 WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND 
            frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND frn1.doc_type IN ('FN')) frn_status_name
FROM FL_FULFILMENT_ITEM fli
JOIN FL_FULFILMENT_ITEM_ADDR addr ON addr.fulfilment_item_id = fli.fulfilment_item_id
JOIN FL_DEL_ORDER_ITEM deli ON deli.fulfilment_item_addr_id = addr.fulfilment_item_addr_id
    AND deli.fulfilment_item_id = fli.fulfilment_item_id
JOIN fl_delivery_order do ON deli.delivery_order_id = do.delivery_order_id
LEFT JOIN fl_fulfilment_note frn ON do.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID
JOIN FL_FULFILMENT_REQUEST pr ON fli.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID
LEFT JOIN pm_uom uom ON fli.uom_id = uom.UOM_ID
LEFT JOIN fl_fulfilment_note_item item ON item.DEL_ORDER_ITEM_ID = deli.DEL_ORDER_ITEM_ID
WHERE pr.doc_no = ?
GROUP BY do.delivery_order_no, frn.FULFILMENT_NOTE_NO, do.delivery_order_id, frn.FULFILMENT_NOTE_ID
ORDER BY do.delivery_order_no", array($docNo));
        return $query;
    }

    protected function getListItemOrder($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT addr.ordered_amt, addr.unit_price,pss.PROJECT_CODE , pss.SETIA_CODE , pss.SUB_SETIA_CODE , vot_fund_code, prg_activity_code, gl_acc_code, addr.ITEM_CODE,addr.ORDERED_QTY , DECODE (item.ITEM_TYPE_ID , 199, 'Perkhidmatan', 198, 'Produk' ) AS item_type, 
item.item_name, item.item_desc, uom_code, uom_name, address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' ||state_name AS address, flitem.CHARGE_LINE_SEQ , addr.charge_org_profile_id, pov.org_name, pov.org_code,
(SELECT max(TRANS_DATE) FROM OSB_LOGGING osb WHERE REMARKS_1 = addr.ITEM_CODE AND trans_type = 'JmsIns') trans_date
FROM fl_fulfilment_request pr , fl_delivery_address ad, fl_fulfilment_item_addr addr, sc_request_item item,
PM_VOT_FUND vot, PM_PRG_ACTIVITY act, PM_GL_ACCOUNT acc, PM_SUB_SETIA pss ,  PM_UOM uom, PM_ADDRESS pa , pm_address_type pat, PM_STATE state, FL_FULFILMENT_ITEM flitem , PM_ORG_VALIDITY pov, pm_city city
WHERE pr.fulfilment_req_id = ad.fulfilment_req_id
AND addr.FULFILMENT_ADDR_ID = ad.DELIVERY_ADDRESS_ID  
AND addr.REQUEST_ITEM_ID = item.REQUEST_ITEM_ID 
AND addr.vot_fund_id = vot.VOT_FUND_ID (+) 
AND addr.prg_activity_id = act.prg_activity_id (+) 
AND addr.gl_account_id = acc.gl_account_id (+) 
AND addr.SUB_SETIA_ID  = pss.SUB_SETIA_ID (+)
AND item.uom_id = uom.uom_id
AND (ad.PM_ADDRESS_ID = pa.ADDRESS_ID or ad.RECEIVING_GRP_ADDR_ID  = pat.ADDRESS_TYPE_ID) 
AND pat.ADDRESS_ID = pa.ADDRESS_ID(+)
AND pa.STATE_ID = state.STATE_ID 
AND pa.city_id = city.city_id 
AND pat.ADDRESS_TYPe = 'D'
AND flitem.fulfilment_item_id(+) = addr.fulfilment_item_id
AND addr.CHARGE_ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
AND pov.RECORD_STATUS = 1
AND pr.doc_no = ?
ORDER BY CHARGE_LINE_SEQ", array($docNo));
        return $query;
    }

    protected function getListSDIItem($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT stop_instr_no, item_name , RECALLED_QTY , RECALLED_AMT , fws.STATUS_ID , psd.STATUS_NAME ,  psd.LANGUAGE_CODE
FROM FL_STOP_INSTR_ITEM sim , FL_STOP_INSTR stop, FL_FULFILMENT_ITEM item, NGEP_PRD.SC_REQUEST_ITEM sc, FL_FULFILMENT_REQUEST pr, FL_WORKFLOW_STATUS fws , PM_STATUS_DESC psd 
WHERE sim.STOP_INSTR_ID = stop.STOP_INSTR_ID
AND sim.FULFILMENT_ITEM_ID = item.FULFILMENT_ITEM_ID 
AND item.REQUEST_ITEM_ID = sc.REQUEST_ITEM_ID 
and pr.FULFILMENT_REQ_ID = stop.FULFILMENT_REQ_ID 
AND fws.DOC_ID = stop.STOP_INSTR_ID 
AND psd.STATUS_ID = fws.STATUS_ID 
AND psd.LANGUAGE_CODE = 'en'
AND fws.DOC_TYPE = 'SD'
AND fws.IS_CURRENT = 1
and stop.RECORD_STATUS = 1
AND  pr.doc_no =? ", array($docNo));
        return $query;
    }

    protected function getListDnItem($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT fa.doc_no , sta.STATUS_NAME , CHARGE_LINE_SEQ, item_name, i.ADJUSTED_QTY , i.ADJUSTED_AMT , i.DAN_COMMITMENT_NO 
FROM FL_ADJUSTMENT fa, FL_FULFILMENT_REQUEST pr, FL_WORKFLOW_STATUS fs, pm_status_desc sta, FL_ADJUSTMENT_ITEM i, FL_FULFILMENT_ITEM item, sc_request_item sc
WHERE fa.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID
AND fa.ADJUSTMENT_ID  = fs.DOC_ID 
AND sta.STATUS_ID = fs.STATUS_ID 
AND i.ADJUSTMENT_ID = fa.ADJUSTMENT_ID 
AND item.FULFILMENT_ITEM_ID = i.FULFILMENT_ITEM_ID 
AND sc.REQUEST_ITEM_ID = item.REQUEST_ITEM_ID 
AND fs.DOC_TYPE in ('DN','CN')
AND fs.IS_CURRENT = 1
AND sta.LANGUAGE_CODE = 'en'
AND pr.doc_no = ?
ORDER BY CHARGE_LINE_SEQ", array($docNo));
        return $query;
    }

    protected function getListPRCRStatus($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT st.status_id FROM FL_WORKFLOW_STATUS st, FL_FULFILMENT_REQUEST pr
                            WHERE pr.FULFILMENT_REQ_ID = st.DOC_ID 
                            AND IS_current = 1 
                            AND st.doc_type IN ('CR','PR')
                            AND STATUS_ID IN (40000,40010,40015,40020,40500,40510,40515,40520)
                            AND pr.DOC_NO = ? ", array($docNo));
        return $query;
    }

    protected function getSqrnno($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select  scr.REQUEST_NOTE_NO, sq.quote_no
                            from fl_fulfilment_request flreq, sc_purchase_request spr, sc_request_note scr, sc_quote sq 
                            where spr.purchase_request_id = flreq.purchase_request_id
                            and scr.request_note_id = spr.request_note_id
                            and sq.quote_id = scr.quote_id
                            and flreq.doc_no =  ? ", array($docNo));
        return $query;
    }

    protected function getCtInfoList($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pr.SUPPLIER_ID , TO_CHAR(pr.start_date, 'DD-MM-YYYY') AS start_delivery_date, to_char(val.eff_date,'dd-mm-YYYY') as eff_date, to_char(val.exp_date,'dd-mm-YYYY') as exp_date, code_name, pr.SCHEDULE_NO , cr.contract_id , contract_no, sett.SUPPLIER_DO_DAYS , TO_CHAR(pr.start_date - sett.SUPPLIER_DO_DAYS, 'DD-MM-YYYY') AS supplier_can_submit_do, TO_CHAR(val.exp_date - sett.LAST_CO_B4_EXP_DAYS, 'DD-MM-YYYY') AS cr_last_created, sett.LAST_CO_B4_EXP_DAYS
        FROM FL_FULFILMENT_request pr, ct_contract cr, ct_setting sett, CT_CONTRACT_VALUE val, PM_PARAMETER_DESC pm
        WHERE pr.CONTRACT_ID = cr.contract_id
        AND sett.CONTRACT_VER_ID = cr.LATEST_CONTRACT_VER_ID 
        AND val.CONTRACT_VER_ID = cr.LATEST_CONTRACT_VER_ID 
        AND pm.PARAMETER_ID = cr.FULFILMENT_TYPE_ID 
        AND pm.LANGUAGE_CODE = 'ms'
        AND pr.doc_no =  ? ", array($docNo));
        return $query;
    }

    protected function getCtInfoList2($docNo, $docNo1)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT 
  (SELECT COUNT(po.DOC_NO)
   FROM fl_fulfilment_order po ,  fl_fulfilment_request pr
   WHERE po.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID
   AND pr.CONTRACT_ID = (SELECT contract_id FROM FL_FULFILMENT_REQUEST WHERE doc_no = ?) 
   AND pr.record_status = 1) AS count_doc_no,
  main_query.harga_fl_itemsummary,
  main_query.ACK_DATE,
  main_query.CONTRACT_AMOUNT,
  main_query.agreement,main_query.eff_date, main_query.exp_date,
  TO_CHAR((((main_query.harga_fl_itemsummary) / main_query.CONTRACT_AMOUNT)*100), 'FM9999999990.00') AS percent
FROM (
  SELECT 
    SUM(fis.item_amt) AS harga_fl_itemsummary,
    ct.ACK_DATE,
    cta.CONTRACT_AMOUNT, cta.eff_date, cta.exp_date,
    CASE WHEN is_agreement = 1 THEN 'Yes' ELSE 'NO' END AS agreement 
  FROM 
     fl_item_summary fis, fl_fulfilment_request pr, ct_contract ct, ct_contract_amount cta
  WHERE pr.FULFILMENT_REQ_ID = fis.FULFILMENT_REQ_ID
  AND pr.CONTRACT_ID = ct.CONTRACT_ID 
  AND ct.LATEST_CONTRACT_VER_ID = cta.CONTRACT_ver_ID  
  AND  fis.CONTRACT_ID = (SELECT contract_id FROM FL_FULFILMENT_REQUEST WHERE doc_no = ?)
    AND fis.RECORD_STATUS = 1
    AND cta.exp_date >= SYSDATE
  GROUP BY 
   ct.ACK_DATE, cta.CONTRACT_AMOUNT, is_agreement, cta.eff_date, cta.exp_date
) main_query ", array($docNo, $docNo1));
        return $query;
    }

    protected function getRoOfficer($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct plh.LOGIN_DATE , req.doc_no, to_char(pat.created_date,'dd/mm/YYYY HH24:MI:SS') as created_date,CASE WHEN to_char(pat.created_date,'HH24:MI:SS') >= '12:00:00' THEN 'PM' ELSE 'AM' END AS DATE1, to_char(pat.changed_date,'dd/mm/YYYY HH24:MI:SS') as changed_date,CASE WHEN to_char(pat.changed_date,'HH24:MI:SS') >= '12:00:00' THEN 'PM' ELSE 'AM' END AS DATE2,pu.login_id,pu.user_name,pa.address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' || state_name AS address
                            from pm_address pa,pm_address_type pat , pm_user pu, fl_fulfilment_request req, fl_delivery_address addr  , pm_city city, pm_district dis, pm_state state, PM_LOGIN_HISTORY plh 
                            where pat.USER_ID = pu.USER_ID
                            and req.fulfilment_req_id = addr.fulfilment_req_id
                            and pat.address_id = addr.pm_address_id 
                            and  address_type = 'R'
                            and pat.record_status = 1
                            and pu.record_status = 1
                            and pa.address_id = pat.address_id
                            and pa.city_id = city.city_id
                            and pa.district_id = dis.district_id
                            and pa.state_id = state.state_id
                            AND plh.USER_ID(+) = pu.USER_ID 
                            and req.doc_no =  ? ", array($docNo));
        return $query;
    }

    protected function getRoOfficerManualAddress($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select distinct req.doc_no, pat.created_date,pat.changed_date,pu.login_id,pu.user_name,pa.address_name, pa.address_name ||', ' || pa.address_1 ||', ' || pa.address_2 ||', ' || pa.address_3 ||', ' ||  pa.postcode  ||', ' || city.city_name ||', ' || state.state_name AS address
                            from pm_address pa,pm_address_type pat , pm_address_type pat1 ,pm_user pu, fl_fulfilment_request req, fl_delivery_address addr  , pm_city city, pm_district dis, pm_state state
                            where pat.USER_ID = pu.USER_ID
                            and req.fulfilment_req_id = addr.fulfilment_req_id
                            and pat1.address_type_id = addr.receiving_grp_addr_id
                            and pat.address_id = pat1.address_id 
                            and pat.address_type = 'R'
                            and pat.record_status = 1
                            and pu.record_status = 1
                            and pa.address_id = pat.address_id
                            and pa.city_id = city.city_id
                            and pa.district_id = dis.district_id
                            and pa.state_id = state.state_id
                            and req.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function getPOCOnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select florder.fulfilment_order_id, flreq.doc_no
                            from fl_fulfilment_order florder, fl_fulfilment_request flreq
                            where florder.fulfilment_req_id = flreq.fulfilment_req_id(+)
                            and florder.Doc_no =? ", array($docNo));
        return $query;
    }

    protected function getFNnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select flreq.doc_no
                            from fl_fulfilment_note note, fl_fulfilment_request flreq 
                            where flreq.fulfilment_req_id = note.fulfilment_req_id
                            and fulfilment_note_no = ? ", array($docNo));
        return $query;
    }

    protected function getSDnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select pr.doc_no
                            from fl_stop_instr st ,fl_fulfilment_request pr
                            where pr.fulfilment_req_id = st.fulfilment_req_id
                            and stop_instr_no = ? ", array($docNo));
        return $query;
    }

    protected function getDOnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select flreq.doc_no
                            from fl_delivery_order delorder, fl_fulfilment_request flreq 
                            where delorder.fulfilment_req_id = flreq.fulfilment_req_id
                            and delivery_order_no = ? ", array($docNo));
        return $query;
    }

    protected function getDNnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select flreq.doc_no
                            from fl_fulfilment_request flreq, fl_adjustment fla
                            where flreq.fulfilment_req_id = fla.fulfilment_req_id
                            and fla.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function getPAnumber($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
        select flreq.doc_no
        from FL_PAYMENT_ADVICE fpa, fl_fulfilment_request flreq 
        where flreq.fulfilment_req_id = fpa.fulfilment_req_id
        and PAYMENT_ADVICE_NO = ? ", array($docNo));
        return $query;
    }

    protected function getROuser($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct pmuser.login_id, pmuser.user_name
                            from fl_delivery_order delorder, FL_DELIVERY_ADDRESS fladd, pm_address_type pmtype, pm_user pmuser, fl_fulfilment_request flreq
                            where delorder.delivery_address_id = fladd.delivery_address_id
                            and flreq.fulfilment_req_id = delorder.fulfilment_req_id
                            and pmtype.address_id = fladd.pm_address_id
                            and pmtype.record_status = 1 
                            and pmtype.address_type = 'R'
                            and pmuser.user_id = pmtype.user_id
                            and flreq.doc_no = ? ", array($docNo));
        return $query;
    }

    protected function getFNuser($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct pmuser.login_id , pmuser.user_name
                            from fl_fulfilment_request flreq, pm_user_group_user pmgroup, pm_user pmuser
                            where pmgroup.user_group_id = flreq.user_group_id
                            and pmgroup.record_status = 1
                            and pmuser.user_id = pmgroup.user_id
                            and pmuser.record_status = 1
                            and flreq.doc_no = ? ", array($docNo));
        return $query;
    }

    /** Return Single Object */
    protected function getLatestStatusPOCOByInvoiceNo($docNo)
    {
        $res = DB::connection('oracle_nextgen_rpt')->select("  
                Select distinct fi.invoice_no, flo.doc_no, sd.status_name, wf.created_date as status_created_date 
                from fl_invoice fi , fl_fulfilment_order flo, fl_workflow_status wf, PM_STATUS_DESC sd
                where fi.FULFILMENT_ORDER_ID = flo.FULFILMENT_ORDER_ID
                and flo.FULFILMENT_ORDER_ID = wf.doc_id
                and flo.doc_type = wf.doc_type
                AND wf.status_id = sd.status_id
                and wf.is_current = 1
                AND sd.language_code = 'en'
                and fi.invoice_no = ? ", array($docNo));
        if (count($res) > 0) {
            return $res[0];
        }
        return null;
    }

    protected function getPOCOstatus($docNo, $pr)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
         SELECT DOC_TYPE, DOC_NO, ACTION_DESC, ACTIONED_DATE, status_name, status_id 
FROM (
    SELECT pm.DOC_TYPE, pm.DOC_NO, pm.ACTION_DESC, pm.ACTIONED_DATE, des.status_name, des.status_id 
    FROM PM_TRACKING_DIARY pm
    JOIN pm_status_desc des ON des.status_id = pm.STATUS_ID 
    WHERE des.LANGUAGE_CODE = 'en'
    AND doc_no = ?
    AND group_id IS NULL
    UNION
    SELECT pm.DOC_TYPE, pm.DOC_NO, pm.ACTION_DESC, pm.ACTIONED_DATE, des.status_name, des.status_id
    FROM PM_TRACKING_DIARY pm
    JOIN pm_status_desc des ON des.status_id = pm.STATUS_ID 
    WHERE des.LANGUAGE_CODE = 'en'
    AND group_id = (
        SELECT group_id 
        FROM PM_TRACKING_DIARY 
        WHERE doc_no = ? 
        AND group_id IS NOT NULL 
        ORDER BY ACTIONED_DATE DESC
        FETCH FIRST 1 ROW ONLY  -- Use LIMIT 1 for MySQL
    ) 
    AND GROUP_DOC_TYPE IN ('PO', 'CO')
) 
ORDER BY ACTIONED_DATE DESC
                            ", array($docNo, $pr));
        return $query;
    }

    protected function getStatusWf($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    SELECT
            (SELECT doc_no
             FROM fl_fulfilment_order
             WHERE fulfilment_req_id = fdo.FULFILMENT_REQ_ID)               poco_no,
            fdo.delivery_order_no,
            (SELECT do1.status_id
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND
                   do1.status_id = do2.status_id AND do1.is_current = 1 AND do2.language_code = 'en' AND
                   do1.doc_type IN ('DO'))                                  do_status,
            (SELECT do2.status_name
             FROM fl_workflow_status do1, pm_status_desc do2
             WHERE fdo.delivery_order_id = do1.doc_id AND do1.status_id = do2.status_id AND do1.is_current = 1
                   AND do2.language_code = 'en' AND do1.doc_type IN ('DO')) do_status_name,
            nvl(frn.FULFILMENT_NOTE_NO, 'Tiada')                              frn_no,
            (SELECT frn1.status_id
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status,
            (SELECT frn2.status_name
             FROM fl_workflow_status frn1, pm_status_desc frn2
             WHERE frn.FULFILMENT_NOTE_ID = frn1.doc_id AND
                   frn1.status_id = frn2.status_id AND frn1.is_current = 1 AND frn2.language_code = 'en' AND
                   frn1.doc_type IN ('FN'))                                 frn_status_name,
            fdo.RECORD_STATUS                                               do_record_status,
            frn.RECORD_STATUS                                               frn_record_status,
            fdo.delivery_order_id,
            frn.fulfilment_note_id
          FROM fl_fulfilment_note frn, fl_fulfilment_request pr, fl_fulfilment_order po, fl_delivery_order fdo
          WHERE
            pr.FULFILMENT_REQ_ID = po.FULFILMENT_REQ_ID
            AND fdo.FULFILMENT_ORDER_ID = po.FULFILMENT_ORDER_ID
            AND fdo.DELIVERY_ORDER_ID = frn.DELIVERY_ORDER_ID (+)
            AND pr.DOC_NO = ?
                            ", array($docNo));
        return $query;
    }

    protected function getCrstatus($docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select   distinct pmd.doc_type as pmddoctype, pmd.doc_no as pmddocno, pmd.action_desc pmdesc,pmd.actioned_date pmdate, stat.status_name as statstas,pmd.status_id as statusidd
                            from fl_workflow_status status  , pm_tracking_diary pmd, pm_status_desc stat
                            where  pmd.doc_id = status.doc_id  
                            and status.status_id = stat.status_id
                            and pmd.status_id = stat.status_id
                            and stat.language_code = 'en'
                            and status.is_current = 1
                            and status.doc_id  in (?)
                            order by pmdate desc
                            ", array($docNo));
        return $query;
    }

    protected function getPmuser($ic)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        select pu.user_name,login_id,pu.record_status , SM.COMPANY_NAME 
from pm_user pu, SM_PERSONNEL sp , sm_supplier sm
where pu.RECORD_STATUS  =1 
AND SM.LATEST_APPL_ID(+) = sp.appl_id
AND pu.user_id = sp.user_id(+)
and login_id in (?)
ORDER BY SM.LATEST_APPL_ID asc
        ", array($ic));
        return $query;
    }

    protected function getPOCOWorkflowByDoNumber($doNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
            select A.DELIVERY_ORDER_NO as dono,A.DELIVERY_ORDER_ID as doid, E.DOC_NO as poco,C.STATUS_ID as statusid, D.STATUS_NAME as statusname from FL_DELIVERY_ORDER A, FL_FULFILMENT_ORDER E,
            FL_WORKFLOW_STATUS B, PM_STATUS C,PM_STATUS_DESC D
            where A.FULFILMENT_ORDER_ID = E.FULFILMENT_ORDER_ID 
            and A.FULFILMENT_ORDER_ID=B.DOC_ID
            and B.STATUS_ID = C.STATUS_ID
            and C.STATUS_ID = D.STATUS_ID
            and B.DOC_TYPE in ('PO', 'CO', 'FC')
            and D.LANGUAGE_CODE = 'en'
            and B.IS_CURRENT = 1
          --  and B.STATUS_ID in (41505, 41510)
            and A.DELIVERY_ORDER_NO = ?", array($doNo));

        return $query;
    }

    protected function getPOCOWorkflowByDoId($doId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
            select A.DELIVERY_ORDER_NO as dono,A.DELIVERY_ORDER_ID as doid, E.DOC_NO as poco,C.STATUS_ID as statusid, D.STATUS_NAME as statusname from FL_DELIVERY_ORDER A, FL_FULFILMENT_ORDER E,
            FL_WORKFLOW_STATUS B, PM_STATUS C,PM_STATUS_DESC D
            where A.FULFILMENT_ORDER_ID = E.FULFILMENT_ORDER_ID 
            and A.FULFILMENT_ORDER_ID=B.DOC_ID
            and B.STATUS_ID = C.STATUS_ID
            and C.STATUS_ID = D.STATUS_ID
            and B.DOC_TYPE in ('PO', 'CO', 'FC')
            and D.LANGUAGE_CODE = 'en'
            and B.IS_CURRENT = 1
          --  and B.STATUS_ID in (41505, 41510)
            and A.DELIVERY_ORDER_ID = ?", array($doId));

        return $query;
    }

    protected function getListRedundantSapOrderNoFromIgfmas($year, $month, $cb)
    {
        //        $year = Carbon::now()->year;
        //        $current_month = Carbon::now()->month;

        $query_osb = "";
        $query_osb_bak = "";
        $redundants = array();
        for ($mth = $month; $mth <= $month; $mth++) {
            $query_osb = "SELECT   remarks_1 as doc_no, MAX (remarks_2) AS sap_order_no, MIN(remarks_2) AS old_sap_order_no
                        FROM osb_logging
                       WHERE trans_type = 'IBReq-DEC'
                         AND service_code = 'EPP-013'
                         AND remarks_2 <> 'NA'
                         AND EXTRACT (YEAR FROM trans_date) = ?
                         AND EXTRACT (MONTH FROM trans_date) = ?
                    GROUP BY remarks_1
                      HAVING COUNT (DISTINCT remarks_2) > 1";

            $results = DB::connection('oracle_nextgen_rpt')->select($query_osb, array($year, $mth));
            $mth_datas = $this->pushRedundantSAPOrderNo($results);
            array_push($redundants, $mth_datas);
        }

        for ($mth = $month; $mth <= $month; $mth++) {
            $query_osb_bak = "SELECT   remarks_1 as doc_no, MAX (remarks_2) AS sap_order_no, MIN(remarks_2) AS old_sap_order_no
                        FROM OSB_LOGGING
                       WHERE trans_type = 'IBReq-DEC'
                         AND service_code = 'EPP-013'
                         AND remarks_2 <> 'NA'
                         AND EXTRACT (YEAR FROM trans_date) = ?
                         AND EXTRACT (MONTH FROM trans_date) = ?
                    GROUP BY remarks_1
                      HAVING COUNT (DISTINCT remarks_2) > 1";

            $results = DB::connection('oracle_nextgen_arc')->select($query_osb_bak, array($year, $mth));
            $mth_datas = $this->pushRedundantSAPOrderNo($results);
            array_push($redundants, $mth_datas);
        }

        $query_fl_order = "";
        $poco_redundants = array();

        if (count($redundants)) {
            foreach ($redundants as $recs) {
                foreach ($recs as $rec) {
                    // check if poco exist in fulfilment order and sap order no <> sap order no in fulfilment order, redundant found & not yet fix.
                    $query_fl_order = "SELECT count(1) as total
                                        FROM fl_fulfilment_order a, fl_workflow_status w
                                       WHERE a.fulfilment_order_id = w.doc_id
                                        AND w.doc_type IN ('PO', 'CO')
                                        AND w.is_current = 1
                                        AND w.status_id IN (41005, 41505)
                                        AND a.doc_no = ?
                                        AND a.sap_order_no <> ? ";

                    $result = DB::connection('oracle_nextgen_rpt')->select($query_fl_order, array($rec->doc_no, $rec->sap_order_no));

                    if ($result[0]->total == '1') {
                        $wf_sts = $this->getListWorkFlowStatusDOFN($rec->doc_no);
                        if (count($wf_sts)) {
                            $doc_no = $rec->doc_no;
                            if ($wf_sts[0]->delivery_order_no != null && $wf_sts[0]->delivery_order_no != 'Tiada')
                                $doc_no = $wf_sts[0]->delivery_order_no;
                            if ($wf_sts[0]->frn_no != null && $wf_sts[0]->frn_no != 'Tiada')
                                $doc_no = $wf_sts[0]->frn_no;
                            $task = $cb->getTaskDetailBpmByDocNo($doc_no);

                            if (null != $task) {
                                $task->poco_no = $rec->doc_no;
                                $rec->instance_id = $task->compositeinstanceid;
                            } else {
                                $rec->instance_id = 'NA';
                            }
                            array_push($poco_redundants, $rec);
                        }
                    }
                }
            }
        }

        return $poco_redundants;
    }

    protected function pushRedundantSAPOrderNo($list)
    {
        $push_redundant = array();
        if (count($list)) {
            foreach ($list as $row) {
                array_push($push_redundant, $row);
            }
        }

        return $push_redundant;
    }

    protected function trackingSQDetails($sqno, $rnno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select pmd.doc_id, pmd.doc_type as pmddoctype, pmd.doc_no as pmddocno, pmd.action_desc pmdesc,pmd.actioned_date pmdate, stat.status_name as statstas,pmd.status_id  
                            from PM_TRACKING_DIARY pmd,  pm_status_desc stat
                            where pmd.status_id = stat.status_id
                            and stat.language_code = 'en'
                            and pmd.doc_type in('SQ','RN','LA') 
                            and (pmd.doc_id = ? or pmd.doc_id = ?)
                            order by actioned_date desc", array($sqno, $rnno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function trackingDPWorkflowDetails($docno, $doc_type)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select status.doc_id, status.doc_type, status.created_date, stat.status_name,   status.is_current, status.created_by, pu.login_id, status.changed_by ,pu1.login_id as login
                        from pm_status_desc stat, pm_user pu, sc_workflow_status status
                        left join pm_user pu1 on pu1.user_id = status.changed_by
                        where stat.status_id = status.status_id
                        and stat.language_code = 'en'
                        and pu.USER_ID = status.created_by
                        and doc_id = ? and doc_type = ? 
                        order by status.created_date desc", array($docno, $doc_type));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function trackinFLPWorkflowDetails($docno, $doc_type)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select status.doc_id, status.doc_type, status.created_date, stat.status_name,   status.is_current, status.created_by, pu.login_id, status.changed_by ,pu1.login_id as login
                        from pm_status_desc stat, pm_user pu, fl_workflow_status status
                        left join pm_user pu1 on pu1.user_id = status.changed_by
                        where stat.status_id = status.status_id
                        and stat.language_code = 'en'
                        and pu.USER_ID = status.created_by
                        and doc_id = ? and doc_type = ? 
                        order by status.created_date desc", array($docno, $doc_type));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getOrderBPMlistFromRN($rnno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct rn.request_note_no
                            from sc_request_note rn, fl_fulfilment_request pr, sc_purchase_request scpr, fl_workflow_status status
                            where scpr.request_note_id = rn.request_note_id
                            and scpr.purchase_request_id = pr.purchase_request_id
                            and status.doc_id = pr.fulfilment_req_id
                            and status.doc_type= 'PR'
                            and status.status_id in (40000,40010,40300)
                            and status.is_current = 1
                            and rn.request_note_no = ?", array($rnno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getSQFromRN($sqno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select  sc.quote_no
                            from sc_request_note rn, sc_quote sc
                            where sc.quote_id = rn.quote_id
                            and request_note_no = ?", array($sqno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getSupplierErorItemList($itemid, $id)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select distinct rsii.request_item_id, rsii.supplier_name,rsii.supplier_id,rsii.request_supplier_item_id, rsii.request_item_id as req_item_id, req.request_note_id
                        from SC_REQUEST_SUPPLIER_ITEM rsii,sc_request_item scri, sc_request_note req, sc_request_note_dtl reqdtl, fl_fulfilment_request flreq
                        where req.request_note_id = scri.request_note_id  --1
                            and scri.request_note_id = reqdtl.request_note_id
                            and scri.request_item_id = reqdtl.request_item_id
                            and reqdtl.purchase_request_id = flreq.purchase_request_id(+)
                            and rsii.request_item_id = scri.request_item_id 
                            and rsii.request_item_id in (?) 
                            and rsii.supplier_id in (?)", array($itemid, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getSupplierErorItemListWhenPR($itemid)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select * from FL_FULFILMENT_ITEM_ADDR where REQUEST_ITEM_ID = ? ", array($itemid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListAddressAvailable($docno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pm.address_id, address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' ||state_name AS address
        FROM PM_ADDRESS_TYPE pat, PM_ADDRESS pm, FL_FULFILMENT_REQUEST pr , PM_STATE state, pm_city city
        WHERE pat.address_id = pm.address_id
        AND state.STATE_ID = pm.state_id
        and city.city_id = pm.city_id
        AND pr.prepared_org_profile_id = pat.org_profile_id 
        AND pat.record_status = 1 
        AND pat.ADDRESS_TYPE = 'D'
        AND pr.doc_no = ? ", array($docno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getlistAddress($docno, $docno1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
      SELECT COALESCE(psd.STATUS_NAME, 'No Status') AS STATUS_NAME, COALESCE(do.delivery_order_no, 'No Order') AS delivery_order_no, pm.address_id,
     pm.address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' ||state_name AS address, 'Auto' AS type,  deladd.start_date, deladd.end_date
FROM PM_ADDRESS pm
JOIN FL_DELIVERY_ADDRESS deladd ON deladd.pm_address_id = pm.address_id
JOIN PM_STATE state ON state.STATE_ID = pm.state_id
JOIN pm_city city on city.city_id = pm.city_id 
LEFT JOIN FL_DELIVERY_ORDER do ON do.delivery_address_id = deladd.DELIVERY_ADDRESS_ID
LEFT JOIN FL_WORKFLOW_STATUS fws ON do.DELIVERY_ORDER_ID = fws.DOC_ID AND fws.DOC_TYPE = 'DO' AND fws.IS_CURRENT = 1
LEFT JOIN  PM_STATUS_DESC psd ON fws.STATUS_ID = psd.STATUS_ID AND psd.LANGUAGE_CODE = 'en'
JOIN  FL_FULFILMENT_REQUEST pr ON pr.FULFILMENT_REQ_ID = deladd.FULFILMENT_REQ_ID
WHERE pr.doc_no = ?
UNION 
SELECT COALESCE(psd.STATUS_NAME, 'No Status') AS STATUS_NAME, COALESCE(do.delivery_order_no, 'No Order') AS delivery_order_no, pm.address_id, 
    pm.address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||city_name||', ' ||state_name AS address, 'Manual' AS type, deladd.start_date, deladd.end_date
FROM PM_ADDRESS pm
JOIN PM_ADDRESS_TYPE pat ON pat.ADDRESS_ID = pm.ADDRESS_ID
JOIN FL_DELIVERY_ADDRESS deladd ON deladd.RECEIVING_GRP_ADDR_ID = pat.address_type_id
JOIN PM_STATE state ON state.STATE_ID = pm.state_id
JOIN pm_city city on city.city_id = pm.city_id 
LEFT JOIN FL_DELIVERY_ORDER do ON do.delivery_address_id = deladd.DELIVERY_ADDRESS_ID
LEFT JOIN FL_WORKFLOW_STATUS fws ON do.DELIVERY_ORDER_ID = fws.DOC_ID AND fws.DOC_TYPE = 'DO' AND fws.IS_CURRENT = 1
LEFT JOIN PM_STATUS_DESC psd ON fws.STATUS_ID = psd.STATUS_ID AND psd.LANGUAGE_CODE = 'en'
JOIN FL_FULFILMENT_REQUEST pr ON pr.FULFILMENT_REQ_ID = deladd.FULFILMENT_REQ_ID
WHERE pr.doc_no = ? ", array($docno, $docno1));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getlistAddressManual($docno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT delivery_order_no, pm.address_id, address_name, address_name ||', ' ||address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||state_name AS address
        FROM PM_ADDRESS pm, FL_DELIVERY_ADDRESS deladd, FL_DELIVERY_ORDER do, FL_FULFILMENT_REQUEST pr , PM_STATE state, PM_ADDRESS_TYPE pat
        WHERE deladd.RECEIVING_GRP_ADDR_ID  = pat.ADDRESS_TYPE_ID
        AND pat.ADDRESS_ID = pm.ADDRESS_ID 
        AND state.STATE_ID = pm.state_id
        AND do.delivery_address_id = deladd.DELIVERY_ADDRESS_ID 
        AND pr.FULFILMENT_REQ_ID = deladd.FULFILMENT_REQ_ID 
        AND pr.doc_no =  ? ", array($docno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function newPriceOnPR($itemid)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
                     select addr.fulfilment_item_addr_id as rightid, sip.supplier_item_price_id, sip.unit_price ,(sip.unit_price * addr.ordered_qty) as total 
                            from sc_supplier_item_price sip, fl_fulfilment_item_addr addr, sc_request_supplier_item item
                            where sip.request_supplier_item_id = item.request_supplier_item_id
                            and item.REQUEST_ITEM_ID = addr.REQUEST_ITEM_ID 
                            and sip.request_supplier_item_id = ? ", array($itemid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    /**
     * PRCR TRACKING DH PENDING REVISION APPROVAL TP IN WORKFLOW STILL 40120 - PENDING QUERY VERIFICATION
     * STUCK TASK QUERY PRCR
     */
    protected function getFailedListStuckTaskQueryPRCR()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  S.CREATED_DATE,TO_CHAR (S.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created, S.DOC_TYPE ,S.DOC_ID,
            FR.FULFILMENT_REQ_ID, FR.DOC_NO, S.STATUS_ID,  S.IS_CURRENT, FR.RECORD_STATUS,d.STATUS_NAME 
              FROM FL_WORKFLOW_STATUS S, FL_FULFILMENT_REQUEST FR, PM_STATUS_DESC D
             WHERE S.DOC_TYPE IN ( 'PR' ,'CR') 
             AND FR.FINANCIAL_YEAR  = EXTRACT(YEAR FROM sysdate) 
             AND FR.FULFILMENT_REQ_ID = S.DOC_ID 
             AND S.STATUS_ID = D.STATUS_ID
             AND D.LANGUAGE_CODE ='en'
             AND S.IS_CURRENT = 1
             AND S.STATUS_ID IN (40120,40620)
             and exists  ( select td.doc_id from  pm_tracking_diary td where  td.DOC_no = FR.DOC_NO and status_id in (40010,40020,40510,40520)
and ACTIONED_DATE in  (select max(td2.ACTIONED_DATE) from pm_tracking_diary td2
where td2.DOC_NO = td.DOC_NO ) )
order by S.CREATED_DATE desc"
        );

        return $dataList;
    }

    /**
     * PRCR TRACKING DH AWAITING 1GFMAS RESPONSE (40100) TP IN WORKFLOW STILL 40010 - PENDING APPROVAL
     * STUCK TASK PRCR  AWAITING IGFMAS
     */
    protected function getFailedListStuckTaskAwaitingIgfmasPRCR()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT s.CREATED_DATE, TO_CHAR(s.CREATED_DATE, 'DD/MM/YYYY HH12:MI:SS') fws_date_created, s.DOC_TYPE, s.DOC_ID,
                        fr.FULFILMENT_REQ_ID, fr.DOC_NO, s.STATUS_ID, s.IS_CURRENT, fr.RECORD_STATUS, d.STATUS_NAME
                FROM fl_workflow_status s
                JOIN FL_FULFILMENT_request fr ON fr.FULFILMENT_req_ID = s.doc_id
                JOIN PM_STATUS_DESC d ON s.STATUS_ID = d.STATUS_ID
                WHERE s.doc_type IN ('PR', 'CR')
                    AND s.IS_CURRENT = 1
                    AND d.LANGUAGE_CODE = 'en'
                    AND s.STATUS_ID IN (40010, 40510)
                    AND fr.FINANCIAL_YEAR = EXTRACT(YEAR FROM sysdate) 
                    AND EXISTS (
                    SELECT doc_id
                        FROM pm_tracking_diary td
                    WHERE td.DOC_no = fr.DOC_NO
                        AND status_id IN (40100, 40600)
                        AND ACTIONED_DATE = (SELECT MAX(td2.ACTIONED_DATE)
                                            FROM pm_tracking_diary td2
                                            WHERE td2.DOC_NO = td.DOC_NO)
                    )"
        );

        return $dataList;
    }

    /**
     * PRCR TRACKING DH Pending Fulfilment TP IN WORKFLOW STILL Pending Supplier Acknowledgement
     * STUCK TASK POCO ORDER
     */
    protected function getFailedListStuckTaskOrderPOCO()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT 
                    S.CREATED_DATE,TO_CHAR (s.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,S.DOC_TYPE ,S.DOC_ID,
                    FR.FULFILMENT_REQ_ID, FR.DOC_NO, S.STATUS_ID,  S.IS_CURRENT, FR.RECORD_STATUS,
                    d.STATUS_NAME 
                  FROM FL_FULFILMENT_REQUEST rq, FL_WORKFLOW_STATUS S, FL_FULFILMENT_ORDER FR, PM_STATUS_DESC D
                 WHERE 
                  rq.FULFILMENT_REQ_ID  = fr.FULFILMENT_REQ_ID 
                 AND rq.FINANCIAL_YEAR = EXTRACT(YEAR FROM sysdate) 
                 AND S.DOC_TYPE IN ( 'PO' ,'CO')
                 AND FR.FULFILMENT_ORDER_ID = S.DOC_ID
                 AND S.STATUS_ID = D.STATUS_ID
                 AND D.LANGUAGE_CODE ='en'
                 AND S.IS_CURRENT = 1
                 AND S.STATUS_ID IN (41500,41000)
                 AND EXISTS (
                    SELECT TT.TRACKING_DIARY_ID
                      FROM PM_TRACKING_DIARY TT
                     WHERE TT.TRACKING_DIARY_ID IN (SELECT MAX (T.TRACKING_DIARY_ID)
                                                  FROM PM_TRACKING_DIARY T
                                                 WHERE T.DOC_NO = FR.DOC_NO )
                   AND TT.STATUS_ID IN (41505,41005)
                 )
                  ORDER BY s.CREATED_DATE DESC
                 "
        );

        return $dataList;
    }

    /**
     * PO TRACKING DH PENDING DEBIT NOTE APPROVAL TP IN WORKFLOW STILL PENDING INVOICE
     * STUCK TASK FL DEBIT
     */
    protected function getFailedListStuckTaskFLDebit()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  S.CREATED_DATE,TO_CHAR (s.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,S.DOC_TYPE ,S.DOC_ID,
                    FR.FULFILMENT_REQ_ID, FR.DOC_NO, S.STATUS_ID,  S.IS_CURRENT, FR.RECORD_STATUS,
                    d.STATUS_NAME 
                  FROM FL_FULFILMENT_REQUEST rq, FL_WORKFLOW_STATUS S, FL_FULFILMENT_ORDER FR, PM_STATUS_DESC D
                 WHERE 
                   rq.FULFILMENT_REQ_ID  = fr.FULFILMENT_REQ_ID 
                 AND rq.FINANCIAL_YEAR = EXTRACT(YEAR FROM sysdate) 
                 AND S.DOC_TYPE IN ( 'PO' ,'CO')
                 AND FR.FULFILMENT_ORDER_ID = S.DOC_ID
                 AND S.STATUS_ID = D.STATUS_ID
                 AND D.LANGUAGE_CODE ='en'
                 AND S.IS_CURRENT = 1
                 AND S.STATUS_ID IN (41515,41015)  --Pending Invoice
                 AND EXISTS (
                 select tt.TRACKING_DIARY_ID from pm_tracking_diary tt 
                 where  tt.ACTIONED_DATE in (select max(ttt.ACTIONED_DATE) from pm_tracking_diary ttt where fr.doc_no = ttt.doc_no ) 
                 and tt.status_id in (41520,41020) and tt.DOC_NO = fr.doc_no --Pending Debit Note Approval
                 )
                 ORDER BY s.CREATED_DATE DESC"
        );

        return $dataList;
    }

    /**
     * to monitor duplicate invoice for POCO
     * STUCK TASK FL DEBIT
     */
    protected function getDuplicateInvoice()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select count (*) ,inv.FULFILMENT_ORDER_id , po.DOC_NO  
from fl_fulfilment_order po , fl_invoice inv
where po.FULFILMENT_ORDER_ID = inv.FULFILMENT_ORDER_ID and inv.RECORD_STATUS = 1
and po.FULFILMENT_ORDER_ID in (select ws.DOC_ID from fl_workflow_status ws , pm_status_desc pd where ws.DOC_ID = po.FULFILMENT_ORDER_ID 
and ws.IS_CURRENT = 1 and ws.DOC_TYPE= po.DOC_TYPE and ws.STATUS_ID = pd.STATUS_ID and pd.LANGUAGE_CODE in ('en') 
and ws.STATUS_ID in (41015, 41515,41025,41525) ) --41015,41515 - Pending Invoice-PO , 41025,41525 - Pending Payment Match-PO
AND EXTRACT(YEAR FROM inv.created_date) = EXTRACT(YEAR FROM sysdate)
group by inv.FULFILMENT_ORDER_ID,po.DOC_NO 
having count (inv.FULFILMENT_ORDER_ID) > 1   
order by inv.FULFILMENT_ORDER_ID DESC"
        );

        return $dataList;
    }

    /**
     * --FRN tracking awaiting igfmas , workflow still pending approval 
     * STUCK TASK FRN Awaiting IGFMAS 
     */
    protected function getFailedListStuckTaskFrnAwaitingIgfmas()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT S.CREATED_DATE,TO_CHAR (S.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created, S.DOC_TYPE ,S.DOC_ID,
                frn.FULFILMENT_REQ_ID, frn.FULFILMENT_NOTE_NO AS doc_no, s.STATUS_ID,  s.IS_CURRENT, frn.RECORD_STATUS,d.STATUS_NAME
                  FROM fl_workflow_status s, fl_fulfilment_note frn, FL_FULFILMENT_REQUEST FR, PM_STATUS_DESC D
                 WHERE s.doc_type in ( 'FN')
                 and frn.FULFILMENT_NOTE_ID = s.doc_id 
                 AND frn.FULFILMENT_REQ_ID = fr.FULFILMENT_REQ_ID
                  AND FR.FINANCIAL_YEAR  = EXTRACT(YEAR FROM sysdate)
                  AND S.STATUS_ID = D.STATUS_ID
                 AND D.LANGUAGE_CODE ='en'
                 and s.IS_CURRENT = 1
                 and s.STATUS_ID in (43002 )
                 and exists  ( select td.doc_id from  pm_tracking_diary td where  td.DOC_no = frn.FULFILMENT_NOTE_NO and status_id in (43100)
                    and ACTIONED_DATE in  (select max(td2.ACTIONED_DATE) from pm_tracking_diary td2
                    where td2.DOC_NO = td.DOC_NO )
                    ) 
                 ORDER BY s.CREATED_DATE DESC"
        );

        return $dataList;
    }

    /**
     * PRCR tracking dh Pending Revision Approval tp in workflow still Pending Revision
     * STUCK TASK PENDING REVISION APPROVAL PRCR*
     */
    protected function getFailedListStuckTaskPendingRevisionApprovalPRCR()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT S.CREATED_DATE,TO_CHAR (S.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') FWS_DATE_CREATED, S.DOC_TYPE ,S.DOC_ID,
                FR.FULFILMENT_REQ_ID, FR.DOC_NO, S.STATUS_ID,  S.IS_CURRENT, FR.RECORD_STATUS,D.STATUS_NAME 
                  FROM FL_WORKFLOW_STATUS S, FL_FULFILMENT_REQUEST FR, PM_STATUS_DESC D
                 WHERE S.DOC_TYPE IN ( 'PR' ,'CR')
                  AND FR.FINANCIAL_YEAR  = EXTRACT(YEAR FROM SYSDATE) 
                 AND FR.FULFILMENT_REQ_ID = S.DOC_ID
                 AND S.IS_CURRENT = 1
                 AND S.STATUS_ID IN (40015,40515)
                  AND S.STATUS_ID = D.STATUS_ID
                 AND D.LANGUAGE_CODE ='en'
                 AND S.IS_CURRENT = 1
                 AND EXISTS (
                    SELECT TD.DOC_ID FROM  PM_TRACKING_DIARY TD WHERE  TD.DOC_NO = FR.DOC_NO AND STATUS_ID IN (40010,40020,40510,40520)
		                  AND ACTIONED_DATE IN  (SELECT MAX(TD2.ACTIONED_DATE) FROM PM_TRACKING_DIARY TD2
		              WHERE TD2.DOC_NO = TD.DOC_NO )
                 )
                 ORDER BY S.CREATED_DATE DESC "
        );

        return $dataList;
    }

    protected function getFailedListStuckTaskCancelPOCO()
    {

        $dataList = DB::connection('oracle_nextgen_rpt')->select(
            "select * from (
                SELECT s.created_date,
                (SELECT (select actioned_date from pm_user where user_id  = ACTIONED_BY) x FROM pm_tracking_diary tt WHERE tt.tracking_diary_id IN (SELECT MAX (t.tracking_diary_id) FROM pm_tracking_diary t WHERE t.doc_no = fr.DOC_NO )
                AND tt.status_id in (41600,41100))  actioned_cncl,(SELECT (select LOGIN_ID from pm_user er where user_id  = ACTIONED_BY) x FROM pm_tracking_diary tt WHERE tt.tracking_diary_id IN
                (SELECT MAX (t.tracking_diary_id) FROM pm_tracking_diary t WHERE t.doc_no = fr.DOC_NO )
                AND tt.status_id in (41600,41100)) cncl,fr.DOC_NO as doc_no ,(select doc_no from fl_fulfilment_request where fulfilment_req_id = fr.fulfilment_req_id) prcr,
                s2.STATUS_NAME,  s.STATUS_ID,  fr.DOC_TYPE dod, fr.IS_YEP, fr.FULFILMENT_order_ID, fr.FULFILMENT_req_ID, s.IS_CURRENT cur, fr.RECORD_STATUS 
                FROM fl_workflow_status s, FL_FULFILMENT_order fr , pm_status_desc s2 WHERE s.doc_type in ( 'PO' ,'CO') and fr.FULFILMENT_order_ID = s.doc_id and s2.STATUS_ID = s.STATUS_ID
                and s2.LANGUAGE_CODE in ('en') and s.IS_CURRENT = 1
                and s.STATUS_ID  not in (41530, 41535, 41030, 41035,41900,41430,41400,41910,41410,41421,41940,41431,41310,41440,40810,40910,41931,47900,49120,41130 ,41630 ) --cari transaction pending
                AND EXTRACT(YEAR FROM fr.Created_DATE) = EXTRACT(YEAR FROM sysdate)
                and exists ( SELECT tt.TRACKING_DIARY_ID FROM pm_tracking_diary tt WHERE tt.tracking_diary_id IN (SELECT MAX (t.tracking_diary_id) FROM pm_tracking_diary t WHERE t.doc_no = fr.DOC_NO )
                AND tt.status_id in (41600,41100) ) order by 2 desc) 
                where  STATUS_ID  not in (41530, 41535, 41030, 41035,41900,41430,41400,41910,41410,41421,41940,41431,41310,41440,40810,40910,41931,47900,49120)
                order by cncl, actioned_cncl asc
        "
        );

        return $dataList;
    }

    protected function getLatestDocPoco($pr, $po)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT doc_no
        FROM FL_FULFILMENT_REQUEST pr, fl_workflow_status sta
        WHERE sta.DOC_ID = pr.FULFILMENT_REQ_ID 
        AND sta.DOC_TYPE IN ('CR','PR')
        AND sta.STATUS_ID IN (40000, 40010, 40120, 40015, 40020,40500, 40510, 40515, 40520, 40620)
        AND sta.IS_CURRENT = 1
        AND doc_no = ?
        union
        SELECT po.doc_no
                         FROM FL_FULFILMENT_ORDER po, fl_workflow_status sta, FL_FULFILMENT_REQUEST pr
                         WHERE pr.FULFILMENT_REQ_ID = po.FULFILMENT_REQ_ID 
                         AND sta.DOC_ID = po.FULFILMENT_ORDER_ID  
                         AND sta.DOC_TYPE IN ('CO','PO')
                         AND sta.STATUS_ID IN (41000, 41002, 41500)
                         AND sta.IS_CURRENT = 1
                         AND pr.doc_no = ?", array($pr, $po));

        return $query;
    }



    protected function getLatestDoc($do, $do1, $fn, $sd, $dn, $in, $po)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT delivery_order_no AS doc_no, sta.status_id
FROM FL_FULFILMENT_REQUEST pr
JOIN FL_DELIVERY_ORDER do ON pr.FULFILMENT_REQ_ID = do.FULFILMENT_REQ_ID
JOIN fl_workflow_status sta ON sta.DOC_ID = do.DELIVERY_ORDER_ID
WHERE sta.DOC_TYPE IN ('DO')
AND sta.STATUS_ID IN (42000, 42010, 42015, 42030)
AND sta.IS_CURRENT = 1
AND doc_no = ?
AND do.delivery_order_id NOT in (
    SELECT do1.delivery_order_id
    FROM FL_FULFILMENT_REQUEST pr_sub
    JOIN FL_DELIVERY_ORDER do1 ON pr_sub.FULFILMENT_REQ_ID = do1.FULFILMENT_REQ_ID
    JOIN FL_FULFILMENT_NOTE fl ON do1.delivery_order_id = fl.delivery_order_id
    JOIN fl_workflow_status sta_sub ON sta_sub.DOC_ID = fl.FULFILMENT_NOTE_ID
    WHERE sta_sub.DOC_TYPE IN ('FN')
    AND sta_sub.STATUS_ID IN (43002, 43004, 43006, 43020, 43030)
    AND sta_sub.IS_CURRENT = 1
    AND doc_no = ?
)
        UNION ALL
        SELECT fulfilment_note_no AS doc_no, sta.status_id
        FROM FL_FULFILMENT_REQUEST pr, fl_workflow_status sta, FL_FULFILMENT_NOTE fl
        WHERE sta.DOC_ID = fl.FULFILMENT_NOTE_ID  
        AND pr.FULFILMENT_REQ_ID = fl.FULFILMENT_REQ_ID 
        AND sta.DOC_TYPE IN ('FN')
        AND sta.STATUS_ID IN (43002, 43004, 43006, 43020, 43030)
        AND sta.IS_CURRENT = 1
        AND doc_no = ?
        union
                 SELECT str.STOP_INSTR_NO  AS doc_no, sta.status_id
                FROM FL_FULFILMENT_REQUEST pr, fl_workflow_status sta, FL_STOP_INSTR str
                WHERE sta.DOC_ID = str.STOP_INSTR_ID  
                AND pr.FULFILMENT_REQ_ID = str.FULFILMENT_REQ_ID 
                AND sta.DOC_TYPE IN ('SD')
                AND sta.STATUS_ID IN (47000, 47005)
                AND sta.IS_CURRENT = 1
                AND doc_no = ?
                union
                 SELECT ad.DOC_NO  AS doc_no, sta.status_id
                FROM FL_FULFILMENT_REQUEST pr, fl_workflow_status sta, FL_ADJUSTMENT ad
                WHERE sta.DOC_ID = ad.ADJUSTMENT_ID 
                AND pr.FULFILMENT_REQ_ID = ad.FULFILMENT_REQ_ID 
                AND sta.DOC_TYPE IN ('DN')
                AND sta.STATUS_ID IN (45000)
                AND sta.IS_CURRENT = 1
                AND pr.doc_no = ?
                union
                SELECT po.DOC_NO  AS doc_no, sta.status_id
        FROM FL_FULFILMENT_REQUEST pr, fl_workflow_status sta, FL_INVOICE inv, fl_fulfilment_order po
        WHERE sta.DOC_ID = inv.INVOICE_ID 
        AND pr.FULFILMENT_REQ_ID = inv.FULFILMENT_REQ_ID 
        AND po.FULFILMENT_ORDER_ID = inv.FULFILMENT_ORDER_ID 
        AND sta.DOC_TYPE IN ('IN')
        AND sta.STATUS_ID IN (44000, 44010, 44015, 44017, 44130)
        AND sta.IS_CURRENT = 1
        AND pr.doc_no = ?
        union
         SELECT po.doc_no, sta.status_id
                         FROM FL_FULFILMENT_ORDER po, fl_workflow_status sta, FL_FULFILMENT_REQUEST pr
                         WHERE pr.FULFILMENT_REQ_ID = po.FULFILMENT_REQ_ID 
                         AND sta.DOC_ID = po.FULFILMENT_ORDER_ID  
                         AND sta.DOC_TYPE IN ('CO','PO')
                         AND sta.STATUS_ID IN (41015,  41025, 41130, 41017, 41515,  41525, 41630, 41517, 41027, 41527)
                         AND sta.IS_CURRENT = 1
                         AND pr.doc_no = ? ", array($do, $do1, $fn, $sd, $dn, $in, $po));

        return $query;
    }

    protected function getFactoring($pr)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT sap_order_no, case when IS_FACTORED= 1 then 'Factoring' else 'No Factoring' end as IS_FACTORED , FACTORING_ORG_ID , FACTORING_ORG_NAME , fl.ADDRESS_NAME , fl.ADDRESS_1 , fl.ADDRESS_2 , 
fl.ADDRESS_3 , fl.POSTCODE , (SELECT CITY_NAME  FROM PM_CITY pc WHERE pc.CITY_ID= fl.CITY_ID) AS city, (SELECT pd.DISTRICT_NAME  FROM PM_DISTRICT pd WHERE pd.DISTRICT_ID = fl.DISTRICT_ID) AS district,
(SELECT state_name FROM PM_STATE ps WHERE ps.STATE_ID= fl.STATE_ID) AS state, (SELECT pc1.country_name FROM PM_COUNTRY pc1 WHERE pc1.country_id = fl.COUNTRY_ID) AS country
FROM FL_FULFILMENT_ORDER ffo , FL_FULFILMENT_REQUEST pr, FL_ADDRESS fl
WHERE ffo.FULFILMENT_REQ_ID = pr.FULFILMENT_REQ_ID 
AND ffo.FACTORING_ADDRESS_ID  = fl.ADDRESS_ID(+) 
AND pr.doc_no = ?", array($pr));

        return $query;
    }

    protected function checkRnStatus($rn_no)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT sta.* 
        FROM SC_REQUEST_NOTE rn , PM_TRACKING_DIARY sta
        WHERE rn.REQUEST_NOTE_ID  = sta.DOC_ID 
        AND sta.DOC_TYPE = 'RN'
        AND rn.REQUEST_NOTE_NO = ?
        ORDER BY sta.actioned_date desc", array($rn_no));

        return $query;
    }

    protected function getListVot($docno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT CASE WHEN VOT_FUND_TYPE = 'B' THEN 'Bekalan' WHEN VOT_FUND_TYPE = 'P' THEN 'Pembangunan' 
        WHEN VOT_FUND_TYPE = 'T' THEN 'Tanggungan' WHEN VOT_FUND_TYPE = 'S' THEN 'Setia' WHEN VOT_FUND_TYPE = 'A' THEN 'Amanah' END AS VOT_FUND_TYPE_name,
        VOT_FUND_TYPE,  VOT_FUND_CODE , pvf.DESCRIPTION
        FROM PM_VOT_FUND pvf, FL_FULFILMENT_REQUEST pr, PM_ORG_PROFILE pop  
        WHERE PTJ_GROUP_ID = pop.PARENT_ORG_PROFILE_ID  
        AND pr.PREPARED_ORG_PROFILE_ID = pop.ORG_PROFILE_ID 
        AND pop.RECORD_STATUS = 1
        AND pvf.RECORD_STATUS  = 1
        AND pvf.EXP_DATE > sysdate
        AND doc_no = ? ", array($docno));

        return $query;
    }

    protected function bankInfo($code, $su_id, $supplier_id)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        select sa.SUPPLIER_ID, fo.FIN_ORG_NAME,b.appl_id,b.account_no,b.account_purpose,b.is_default_account,CASE WHEN b.IS_FOR_HQ = 1 THEN 'Yes' ELSE 'No' END AS IS_FOR_HQ,b.BANK_BRANCH,b.changed_date,
            sb.branch_name,sb.BRANCH_CODE,sb.APPL_ID, fo.record_status as bank_status,
            address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||district_name||', ' ||city_name||', ' ||state_name AS address
            from SM_SUPPLIER_BANK b
            inner join PM_FINANCIAL_ORG fo on fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
            left join SM_SUPPLIER_BRANCH_BANK bb on bb.SUPPLIER_BANK_ID = b.SUPPLIER_BANK_ID and bb.record_status = 1
            left join SM_SUPPLIER_BRANCH sb on sb.SUPPLIER_BRANCH_ID  = bb.SUPPLIER_BRANCH_ID  and  sb.record_status = 1
            INNER JOIN SM_APPL sa ON sa.APPL_ID = b.APPL_ID
            INNER JOIN SM_ADDRESS sa2 ON sb.ADDRESS_ID = sa2.ADDRESS_ID 
            INNER JOIN PM_DISTRICT pd ON sa2.DISTRICT_ID = pd.DISTRICT_ID
            INNER JOIN PM_CITY pc ON sa2.CITY_ID = pc.CITY_ID 
            INNER JOIN PM_STATE ps ON sa2.STATE_ID = ps.STATE_ID 
            INNER JOIN PM_COUNTRY pc2 ON sa2.COUNTRY_ID = pc2.COUNTRY_ID 
            WHERE sb.branch_code  = ?  
            and b.REV_NO = (select max(x.rev_no) from  SM_SUPPLIER_BANK x where x.appl_id = b.appl_id )
            AND sa.CHANGED_DATE = (SELECT MAX(sa.CHANGED_DATE) FROM SM_APPL sa, SM_SUPPLIER ss WHERE sa.APPL_ID = ss.LATEST_APPL_ID AND sa.SUPPLIER_ID = ?)
            AND sa.SUPPLIER_ID = ?
            ORDER BY b.APPL_ID DESC", array($code, $su_id, $supplier_id));

        return $query;
    }

    protected function bankInfoHq($code)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        select fo.FIN_ORG_NAME,b.appl_id,b.account_no,b.account_purpose,CASE WHEN b.IS_FOR_HQ = 1 THEN 'Yes' ELSE 'No' END AS IS_FOR_HQ,b.BANK_BRANCH,b.changed_date,
        sb.branch_name,sb.BRANCH_CODE,sb.APPL_ID, fo.record_status as bank_status ,
        address_1 ||', ' || address_2 ||', ' ||address_3 ||', ' ||postcode||', ' ||district_name||', ' ||city_name||', ' ||state_name AS address
        from SM_SUPPLIER_BANK b
        inner join PM_FINANCIAL_ORG fo on fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
        left join SM_SUPPLIER_BRANCH_BANK bb on bb.SUPPLIER_BANK_ID = b.SUPPLIER_BANK_ID and bb.record_status = 1
        left join SM_SUPPLIER_BRANCH sb on sb.SUPPLIER_BRANCH_ID  = bb.SUPPLIER_BRANCH_ID  and  sb.record_status = 1
        LEFT JOIN SM_ADDRESS sa2 ON sb.ADDRESS_ID = sa2.ADDRESS_ID
        LEFT JOIN PM_DISTRICT pd ON sa2.DISTRICT_ID = pd.DISTRICT_ID
        LEFT JOIN PM_CITY pc ON sa2.CITY_ID = pc.CITY_ID 
        LEFT JOIN PM_STATE ps ON sa2.STATE_ID = ps.STATE_ID 
        LEFT JOIN PM_COUNTRY pc2 ON sa2.COUNTRY_ID = pc2.COUNTRY_ID 
        WHERE b.appl_id  = ?
        and b.REV_NO = (select max(x.rev_no) from  SM_SUPPLIER_BANK x where x.appl_id = b.appl_id )", array($code));

        return $query;
    }


    protected function itemFromCatalogue($item_id)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT ITEM_NAME , EXTENSION_CODE FROM cm_item WHERE ITEM_ID  = ? ", array($item_id));

        return $query;
    }

    protected function getSQClone($sq_id, $sq_id1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        WITH RecursiveCTE(QUOTE_ID, QUOTE_NO, PARENT_QUOTE_ID) AS (
            SELECT QUOTE_ID, QUOTE_NO, PARENT_QUOTE_ID
            FROM SC_QUOTE
            WHERE QUOTE_ID = ?
            UNION ALL
            SELECT sq.QUOTE_ID, sq.QUOTE_NO, sq.PARENT_QUOTE_ID
            FROM SC_QUOTE sq
            INNER JOIN RecursiveCTE rc
            ON sq.QUOTE_ID = rc.PARENT_QUOTE_ID
          )
          SELECT *
          FROM RecursiveCTE
          WHERE quote_id NOT IN (?)
          ORDER BY QUOTE_ID ", array($sq_id, $sq_id1));

        return $query;
    }

    protected function getItemClone($spec_id, $spec_id1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        WITH RecursiveCTE(SPEC_QUESTION_ID, PARENT_SPEC_QUESTION_ID) AS (
            SELECT SPEC_QUESTION_ID, PARENT_SPEC_QUESTION_ID
            FROM TPL_SPEC_QUESTION
            WHERE SPEC_QUESTION_ID IN (?)
            UNION ALL
            SELECT sq.SPEC_QUESTION_ID, sq.PARENT_SPEC_QUESTION_ID
            FROM TPL_SPEC_QUESTION sq
            INNER JOIN RecursiveCTE rc
            ON sq.SPEC_QUESTION_ID = rc.PARENT_SPEC_QUESTION_ID
          )
          SELECT *
          FROM RecursiveCTE, SC_REQUEST_ITEM sri, SC_QUOTE sq
          WHERE RecursiveCTE.SPEC_QUESTION_ID NOT IN (?)
          AND sri.SPEC_QUESTION_ID = RecursiveCTE.SPEC_QUESTION_ID
          AND sq.QUOTE_ID = sri.QUOTE_ID", array($spec_id, $spec_id1));

        return $query;
    }

    protected function findMMNIFD($item_code, $uom_code)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        select * from DI_MMINF
            WHERE RECORD_STATUS = 1 AND MATERIAL_CODE = ? AND ALT_UOM = ? ", array($item_code, $uom_code));

        return $query;
    }

    protected function getListYepMenuTaskList($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        select * from fl_yep_tasklist
            WHERE doc_no = ? ", array($docNo));

        return $query;
    }

    protected function getSubmit_mm501($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
       SELECT ol.STATUS_DESC  , ol.trans_date, doc_no
    FROM OSB_LOGGING ol , ( SELECT TRANS_ID, doc_no
        FROM (
            SELECT TRANS_ID, po.doc_no
            FROM OSB_LOGGING ol , fl_fulfilment_request pr, fl_fulfilment_order po, fl_workflow_status fws
            WHERE ol.remarks_1 = po.doc_no
            AND pr.fulfilment_req_id = po.fulfilment_req_id
            AND fws.doc_id = pr.fulfilment_req_id
            AND fws.doc_type IN ('PR','CR')
            AND fws.is_current = 1 
            AND fws.status_id IN (40110,40610)
            AND pr.doc_no = ?
            AND ol.TRANS_TYPE = 'IBReq' 
            AND ol.SERVICE_CODE = 'GFM-100' 
            ORDER BY ol.TRANS_DATE DESC
        ) 
        WHERE ROWNUM = 1) pr_table
    WHERE ol.TRANS_TYPE IN ('IBRes')
    AND ol.SERVICE_CODE = 'GFM-100' 
    AND ol.TRANS_ID = pr_table.TRANS_ID", array($docNo));

        return $query;
    }

    protected function getCheck_mm501($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT SUBSTR(
           payload_body, 
           INSTR(payload_body, '<bizFailureDetails>'), 
           INSTR(payload_body, '</bizFailureDetails>') - INSTR(payload_body, '<lineItemDetails>') + LENGTH('</lineItemDetails>')
       ) AS extracted_data, old2.created_date
FROM OSB_LOGGING_DTL old2 
WHERE logging_id IN (
    SELECT ol.LOGGING_ID  
    FROM OSB_LOGGING ol 
    WHERE ol.TRANS_TYPE IN ('IBRes-DEC' )
    AND ol.SERVICE_CODE = 'GFM-100' 
    AND ol.TRANS_ID IN (
        SELECT TRANS_ID
        FROM (
            SELECT TRANS_ID
            FROM OSB_LOGGING ol , fl_fulfilment_request pr, fl_fulfilment_order po, fl_workflow_status fws
            WHERE ol.remarks_1 = po.doc_no
            AND pr.fulfilment_req_id = po.fulfilment_req_id
            AND fws.doc_id = pr.fulfilment_req_id
            AND fws.doc_type IN ('PR','CR')
            AND fws.is_current = 1 
            AND fws.status_id IN (40100,40020,40015,40510,40515,40520,40620 )
            AND pr.doc_no = ? 
            AND ol.TRANS_TYPE = 'IBReq' 
            AND ol.SERVICE_CODE = 'GFM-100' 
            ORDER BY ol.TRANS_DATE DESC
        ) 
        WHERE ROWNUM = 1
    )
) ", array($docNo));

        return $query;
    }

    protected function getEpp_013($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT SUBSTR(
           payload_body, 
           INSTR(payload_body, '<ns:NoPoCo>'), 
           INSTR(payload_body, '</ns:QueryDetailsArray>') - INSTR(payload_body, '<ns:NoPoCo>') + LENGTH('</ns:QueryDetailsArray>')
       ) AS extracted_data , old2.created_date
FROM OSB_LOGGING_DTL old2 
WHERE logging_id IN (
        SELECT logging_id
        FROM (
            SELECT logging_id
            FROM OSB_LOGGING ol , fl_fulfilment_request pr, fl_fulfilment_order po, fl_workflow_status fws
            WHERE ol.remarks_1 = po.doc_no
            AND pr.fulfilment_req_id = po.fulfilment_req_id
            AND fws.doc_id = pr.fulfilment_req_id
            AND fws.doc_type IN ('PR','CR')
            AND fws.is_current = 1 
            AND fws.status_id IN (40100,40020,40120,40015,40510,40515,40520,40620 )
            AND pr.doc_no = ?
            AND ol.TRANS_TYPE = 'IBReq-DEC' 
            AND ol.SERVICE_CODE = 'EPP-013' 
            ORDER BY ol.TRANS_DATE DESC
        ) 
        WHERE ROWNUM = 1
    ) ", array($docNo));

        return $query;
    }

    protected function getFrnMM504($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT ol.STATUS_DESC, ol.TRANS_DATE ,
       REPLACE(
           SUBSTR(
               old2.payload_body, 
               INSTR(old2.payload_body, '<lineItemDetails>'), 
               INSTR(old2.payload_body, '</lineItemDetails>') - INSTR(old2.payload_body, '<lineItemDetails>') + LENGTH('</lineItemDetails>')
           ), 
           '><', '>' || CHR(10) || '<'
             ) AS extracted_data
FROM OSB_LOGGING ol
LEFT JOIN OSB_LOGGING_DTL old2 ON old2.logging_id = ol.LOGGING_ID
WHERE ol.TRANS_TYPE IN ('IBRes-DEC')
AND ol.SERVICE_CODE = 'GFM-110'
AND ol.TRANS_ID = (
    SELECT TRANS_ID
    FROM (
        SELECT TRANS_ID
        FROM OSB_LOGGING ol 
        JOIN fl_fulfilment_order po ON ol.remarks_1 = po.doc_no
        JOIN fl_fulfilment_request pr ON pr.fulfilment_req_id = po.fulfilment_req_id
        JOIN fl_fulfilment_note frn ON pr.fulfilment_req_id = frn.fulfilment_req_id
        JOIN fl_workflow_status fws ON fws.doc_id = frn.fulfilment_note_id
        WHERE fws.doc_type = 'FN'
        AND fws.is_current = 1 
        AND fws.status_id IN (43002,43004 , 43006,43100)
        AND pr.doc_no = ?
        AND ol.TRANS_TYPE = 'IBReq'
        AND ol.SERVICE_CODE = 'GFM-110'
        ORDER BY ol.TRANS_DATE DESC
    ) WHERE ROWNUM = 1
) ", array($docNo));

        return $query;
    }

    protected function getMM506($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT status_desc, doc_no, USER_NAME, date_create, ol.TRANS_DATE
FROM OSB_LOGGING ol, (
    SELECT ol.TRANS_ID, ffo.doc_no, fa.USER_NAME, 
           TO_CHAR(fa.action_date, 'HH24:MI:SS DD-MM-YYYY') AS date_create 
    FROM OSB_LOGGING ol, FL_FULFILMENT_REQUEST ffr, FL_FULFILMENT_ORDER ffo, 
         FL_WORKFLOW_STATUS fws, FL_ACTOR fa, fl_payment_advice fp
    WHERE ffr.FULFILMENT_REQ_ID = ffo.FULFILMENT_REQ_ID
      AND ffo.DOC_NO = ol.REMARKS_2
      AND fws.DOC_ID = ffo.FULFILMENT_ORDER_ID
      AND fws.DOC_TYPE IN ('PO','CO')
      AND fa.doc_id = fp.PAYMENT_ADVICE_ID
      AND fa.doc_type = 'PA'
      AND fp.fulfilment_req_id = ffr.fulfilment_req_id
      AND fws.IS_CURRENT = 1
      AND fws.STATUS_ID IN (41026,41526)
      AND ol.service_code in ('GFM-120' , 'EPP-017')
AND ol.TRANS_TYPE IN ('IBReq','IBReq-DEC') 
      AND ffr.doc_no = ?
) table_poco
WHERE ol.TRANS_TYPE in ('IBRes-DEC','IBReq-DEC')
  AND ol.service_code in ('GFM-120' , 'EPP-017')
  AND ol.trans_id = table_poco.trans_id
ORDER BY ol.TRANS_DATE DESC
FETCH FIRST 1 ROW ONLY", array($docNo));

        return $query;
    }

    protected function getEpp_017ReturnN($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select(" 
        SELECT remarks_3, date_create , REPLACE(
           SUBSTR(
               dtl.payload_body, 
               INSTR(dtl.payload_body, '<ns:poCoNumber>'), 
               INSTR(dtl.payload_body, '</ns:queryDescription>') + LENGTH('</ns:queryDescription>') - 
               INSTR(dtl.payload_body, '<ns:poCoNumber>') 
           ), 
           '><', '>' || CHR(10) || '<'
       ) AS extracted_data  FROM osb_logging_dtl dtl, (SELECT ol.logging_id, ol.TRANS_ID, ffo.doc_no, fa.USER_NAME, remarks_3,
           TO_CHAR(fa.action_date, 'HH24:MI:SS DD-MM-YYYY') AS date_create 
    FROM OSB_LOGGING ol, FL_FULFILMENT_REQUEST ffr, FL_FULFILMENT_ORDER ffo, 
         FL_WORKFLOW_STATUS fws, FL_ACTOR fa, fl_payment_advice fp
    WHERE ffr.FULFILMENT_REQ_ID = ffo.FULFILMENT_REQ_ID
      AND ffo.DOC_NO = ol.REMARKS_2
      AND fws.DOC_ID = ffo.FULFILMENT_ORDER_ID
      AND fws.DOC_TYPE IN ('PO','CO')
      AND fa.doc_id = fp.PAYMENT_ADVICE_ID
      AND fa.doc_type = 'PA'
      AND fp.fulfilment_req_id = ffr.fulfilment_req_id
      AND fws.IS_CURRENT = 1
      AND fws.STATUS_ID IN (41027,41026,41527, 41526  )
      AND ol.service_code in ('EPP-017')
AND ol.TRANS_TYPE IN ('IBReq','IBReq-DEC') 
      AND ffr.doc_no = ?) table_a WHERE dtl.logging_id = table_a.logging_id", array($docNo));

        return $query;
    }
}
