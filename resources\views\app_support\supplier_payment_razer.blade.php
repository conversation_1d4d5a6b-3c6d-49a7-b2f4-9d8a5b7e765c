@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Supplier Payment Razer: Missmatch Card Type</strong></h1> 
    </div> 

    @if(isset($successUpdate) && $successUpdate === true)
        <div class="alert alert-success alert-dismissible" role="alert">{{ $message }}</div>
    @elseif(isset($successUpdate) && $successUpdate === false)
        <div class="alert alert-danger alert-dismissible" role="alert">{{ $message }}</div>
    @endif

    <div class="table-responsive">  
        <table id="razer-payment-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Order ID</th>
                    <th class="text-center">Billing Date</th>
                    <th class="text-center">Transaction ID</th>
                    <th class="text-center hide">Channel</th>
                    <th class="text-center">Amount</th>
                    <th class="text-center hide">Stat Code</th>
                    <th class="text-center">Card Type</th>
                    <th class="text-center">Card Scheme</th>
                    <th class="text-center">Card Bin</th>
                    <th class="text-center">BIN Bank</th>
                    <th class="text-center">BIN Card</th>
                    <th class="text-center">BIN Type</th>
                    <th class="text-center">BIN Level</th>
                    <th class="text-center">BIN Country</th>
                    <th class="text-center">Free BIN Verify</th>
                    <th class="text-center">Free BIN Type</th>
                    <th class="text-center">eP Channel Name</th>
                    <th class="text-center">eP Card Type</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $data)
                <tr>
                    <td class="text-center">{{ $data->order_id }}</td>
                    <td class="text-center">{{ $data->billing_date }}</td>
                    <td class="text-center">{{ $data->tran_id }}</td>
                    <td class="text-center hide">{{ $data->channel }}</td>
                    <td class="text-center">{{ $data->amount }}</td>
                    <td class="text-center hide">{{ $data->stat_code }}</td>
                    <td class="text-center">{{ $data->card_type }}</td>
                    <td class="text-center">{{ $data->card_scheme }}</td>
                    <td class="text-center">{{ $data->card_bin }}</td>
                    <td class="text-center">{{ $data->bin_bank }}</td>
                    <td class="text-center">{{ $data->bin_card }}</td>
                    <td class="text-center">{{ $data->bin_type }}</td>
                    <td class="text-center">{{ $data->bin_level }}</td>
                    <td class="text-center">{{ $data->bin_country }}</td>
                    <td class="text-center">{{ $data->free_bin_verify }}</td>
                    <td class="text-center">{{ $data->free_bin_type }}</td>
                    <td class="text-center">{{ $data->ep_channel_name }}</td>
                    <td class="text-center">{{ $data->ep_card_type }}</td>
                    <td class="text-center">
                        <div class="btn-group btn-group-xs" role="group" aria-label="...">
                            <a class="btn btn-info" data-toggle="tooltip" title="" data-original-title="BIN Checker"
                            href="https://api.freebinchecker.com/bin/{{ $data->card_bin }}" target="_blank"><i class="fa fa-check"></i></a>
                            <a href="#modal-update" class="modal-update-action btn btn-warning" data-toggle="modal" title="Update" data-orderid="{{ $data->order_id }}"
                                data-tranid="{{ $data->tran_id }}" data-bintype="{{ $data->bin_type }}"><i class="fa fa-pencil"></i></a>
                        </div>
                    </td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>

<div id="modal-update" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-edit"></i> <span id="modal-list-data-header">Update Razer Payment Info</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">

                <form id="form-search-task" action="{{url("/app-support/payment-razer")}}" method="post" class="form-horizontal form-bordered">
                    {{ csrf_field() }}
                    <input type="hidden" id="input_tranid" name="input_tranid" class="form-control">
                    <div class="form-group">
                        <div class="col-lg-12">
                            <fieldset>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="search_status_id">Order ID</label>
                                    <div class="col-md-9">
                                        <input type="text" id="input_orderid" name="input_orderid" class="form-control" readonly>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        {{-- <div class="col-lg-12">
                            <fieldset>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="search_status_id">BIN Verify?</label>
                                    <div class="col-md-9">
                                        <input type="checkbox" id="composite_cbx" name="composite_cbx" class="largerCheckbox">
                                    </div>
                                </div>
                            </fieldset>
                        </div> --}}
                        <div class="col-md-12">
                            <fieldset>
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="search_status_id">BIN Type</label>
                                    <div class="col-md-9">
                                        <div class="input-group">
                                            <select id="input_bintype" name="input_bintype" class="form-control">
                                                <option value="">Please select</option>                           
                                                <option value="CREDIT">CREDIT</option>
                                                <option value="DEBIT">DEBIT</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                    <div class="form-group form-actions">
                        <div class="col-md-7 col-md-offset-5">
                            <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="fa fa-times"></i> Cancel</button>
                            <button type="submit" class="btn btn-sm btn-primary"><i class="fa fa-save"></i> Submit</button>
                        </div>
                    </div>
                </form>
                
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            App.datatables();

            $('#razer-payment-datatable').dataTable({
                order: [[1, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
        });
        App.datatables();

$(document).ready(function () {

    $('#razer-payment-datatable').on("click", '.modal-update-action', function () {

        $('#input_orderid').val($(this).attr('data-orderid'));
        $('#input_tranid').val($(this).attr('data-tranid'));
        $('#input_bintype').val($(this).attr('data-bintype'));

    });
        
});    
</script>
@endsection



