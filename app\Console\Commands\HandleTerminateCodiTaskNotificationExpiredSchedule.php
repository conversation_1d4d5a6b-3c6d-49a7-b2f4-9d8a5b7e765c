<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use SSH;
use App\EpSupportActionLog;
use App\Migrate\BPMTaskServiceProgram;

class HandleTerminateCodiTaskNotificationExpiredSchedule extends Command {
    

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleTerminateCodiTaskNotificationExpiredSchedule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To terminate list task notification expired for codification modules. Showstopper for user to take action. Blank page.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $dtStartTime = Carbon::now();
        
        try {
            BPMTaskServiceProgram::terminateTasksNotificationCodification();
            
            $logsdata = self::class . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);

        } catch (\Exception $exc) {
            
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        
    }

}
