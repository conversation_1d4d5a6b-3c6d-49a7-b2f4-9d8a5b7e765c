@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>SM Stuck Task <br>
                <small>SM Stuck Task List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>SM Stuck Task List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>SM Stuck Task List </strong>
                        <small></small>
                    </h1>
                </div>
                <p class="text-danger bolder">
                    <h5>FailedInitiateTask</h5> 
                    Tracking Diary status as Pending Declaration but check in BPM, task not existed. 
                    Ask Middleware team to refire all tasks
                </p>
                <p class="text-danger bolder">
                    <h5>FailedTaskToPendingApplDeclaration</h5> 
                    Tracking Diary status as Pending Declaration but check in BPM, task already to Pending Processing Fee.
                    Ask Middleware team to fix stuck task. 
                </p>
                <p class="text-danger bolder">
                    <h5>FailedTaskToProcessingFee</h5> 
                    Tracking Diary menunjukkan terkini Bayaran Proses telah dibuat (Pending Payment Response) , Workflow masih menunjukkan 20101 - Pending Supporting Document Verification.
                    Ask Middleware team to fix stuck task. 
                </p>
                <p class="text-danger bolder">
                    <h5>StuckTaskToSDO</h5> 
                    Tracking Diary status and Workflow status not tally to status : 'Pending Supporting Document Verification'
                    Ask Middleware team to fix stuck task. 
                </p>
                <p class="text-danger bolder">
                    <h5>FailedTaskToPaymentRegistration</h5> 
                    Tracking Diary menunjukkan terkini Bayaran Proses telah dibuat (Pending Payment Response) , Workflow  menunjukkan 20199 - Registered.
                    Ask Middleware team to fix stuck task. 
                </p>
                <p class="text-danger bolder">
                    <h5>StuckPaymentRegistrationIsPaid</h5> 
                    Tracking Diary menunjukkan terkini Bayaran Proses telah dibuat (Pending Payment Response) , Workflow  menunjukkan 20251  -  Pending Payment Registration Fee.
                    Semakan juga menunjukkan Pembekal sudah membayar RM400. Resit bayaran sudah wujud.
                    Ask Middleware team untuk buat pembetulan. (Teknikal perlu semak, pastikan resit sudah wujud, jika Task BPM sudah rosak, perlu buat penciptaan semula untuk Task ke Tugasan 
                    Bayaran Registration Fee RM400. Kemudian Teknikal perlu untuk UPDATE table SM_PENDING_PROCESS untuk retrigger scheduler.
                </p>
                
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">TASK FAILED</th>
                            <th class="text-center">APPL ID</th>
                            <th class="text-center">APPL_NO</th>
                            <th class="text-center">CREATED_DATE</th>
                            <th class="text-center">BPM INSTANCE ID</th>
                            <th class="text-center">BPM COMPOSITE </th>
                            <th class="text-center">BPM FLOW ID </th>
                            <th class="text-center">ACTION REFIRE </th>
                            

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->type_error }}</td>
                                <td class="text-center">{{ $data->appl_id }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->appl_no  }}" >{{ $data->appl_no }}</a></td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></td>
                                <td class="text-center">{{ $data->composite_bpm }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                                <td class="text-center">@if ($data->type_error == 'FailedInitiateTask')
                                <a class="btn btn-primary" role="button" href="{{url('/bpm/sm/task/find')}}?appl_no={{$data->appl_no}}">Create task BPM for SM</a>
                                @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



