<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\EpSupportMonitoringQtEp;
use Mail;
use Log;
use Config;
use App\Services\Traits\FulfilmentService;

class MonitorQTSchedule extends Command {

    use FulfilmentService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-qt';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will add tracking monitoring QT daily';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $listQtPublished = $this->getListQtPublished();
            foreach ($listQtPublished as $obj) {
                EpSupportMonitoringQtEp::createMonitoringQtEp($obj, "QT_PUBLISHED");
            }
            
            $listQtClosing= $this->getListQtClosing();
            foreach ($listQtClosing as $obj) {
                EpSupportMonitoringQtEp::createMonitoringQtEp($obj, "QT_CLOSING");
            }
            
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            \Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error QT MONITORING'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . __METHOD__ . ' '.json_encode( ['Email' => $data["to"], 'ERROR' => $e->getMessage()]));
        }
    }
    
}
