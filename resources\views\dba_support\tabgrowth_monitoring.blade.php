@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
             <i class="gi gi-charts"></i>Report </br> <small>Monitoring of Table Growth</small>
        </h1>        
    </div>
@endsection
@section('content')
<div class="row">
    <form id="form-search-login" action="{{url("dba-support/report/TableGrowthMonitoringReport")}}" method="post" class="form-horizontal" onsubmit="return true;">
        {{ csrf_field() }}
        <div class="col-md-12">            
            <div class="widget" class="inset">
                <center><div class="block">                
                        <input id="searchType" name="searchType" type="hidden" value="search_date">                    
                        <strong> <label class="col-md-3 control-label" for="entry_date">Search by Date :</label></strong>
                        <div class="col-md-5">
                            <input class="form-control input-datepicker-close" id ="entry_date1" name="entry_date1" type="text" 
                                   data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                   value="{{$StartDate}}">
                            </br>
                            <input class="form-control input-datepicker-close" id ="entry_date2" name="entry_date2" type="text" 
                                   data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                   value="{{$EndDate}}">
                        </div>
                        
                        <div class="form-group form-actions form-actions-button text-center">                        
                            <div class="col-md-11">
                                </br>
                                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                            </div>
                        </div>

                    </div></center>
                <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
                <div id="chartTableGrowth" style="height: 370px; width: 100%; border-style: groove;"></div>
                </br>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <form id="form-search-login" action="" method="post" class="form-horizontal" onsubmit="return true;">
        {{ csrf_field() }}
        <div class="col-md-12">            
            <div class="widget" class="inset">
               
                <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
                <div id="chartTableBAQGrowth" style="height: 370px; width: 100%; border-style: groove;"></div>
                </br>
            </div>
        </div>
    </form>
</div> 
 
@endsection

@section('jsprivate')
<script type="text/javascript">   
    $('#page-container').removeAttr('class');
</script>
<script>
window.onload = function () {  
    
// TABLE GROWTH Chart
var chart = new CanvasJS.Chart("chartTableGrowth", {
   
	title: {
		text: "Table Growth Summary Report"
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
        axisX: {
		valueFormatString: "DD MMM,YY"
	},
	axisY: {
		title: "Total of Volume",
                interval : 8000000
	},
	legend: {
		cursor: "pointer",
                fontSize: 16,
		itemclick: toggleDataSeries
	},      
	data: [{			
			name: "Composite Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($compRows) !!}
		},{			
			name: "Cube Instance Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($cubeInstRows) !!}
		},{			
			name: "Cube Scope Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($cubeScopeRows) !!}
		},{			
			name: "BPM Cube AuditInstance Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($bpmcubeaudRows) !!}
		},{			
			name: "WFTask Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($wftRows) !!}
		},{			
			name: "SCA Flow Instance Rows",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($scaflowRows) !!}
		},{			
			name: "DLV Message Rows",
                        type: "line",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($dlvmsgRows) !!}
		}

	]
});
 
chart.render();
 
function toggleDataSeries(e){
	if (typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	}
	else{
		e.dataSeries.visible = true;
	}
	chart.render();
}

// TABLE BAQ GROWTH Chart
var chartBAQ = new CanvasJS.Chart("chartTableBAQGrowth", {
   
	title: {
		text: "Table BAQ Growth Summary Report"
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
        axisX: {
		valueFormatString: "DD MMM,YY"
	},
	axisY: {
		title: "Total of Volume",
                interval : 8000000
	},
	legend: {
		cursor: "pointer",
                fontSize: 16,
		itemclick: toggleDataSeries
	},      
	data: [{			
			name: "BAQ Rows",
                        type: "line",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($baqRows) !!}
		}

	]
});
 
chartBAQ.render();
 
function toggleDataSeries(e){
	if (typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	}
	else{
		e.dataSeries.visible = true;
	}
	chartBAQ.render();
}
}
</script>
@endsection