@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Stuck YEP CF<br>
                <small>Stuck YEP CF</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>Stuck YEP CF</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck YEP CF</strong>
                        <small></small>
                    </h1>
                </div>
             
                <div class="table-responsive">
                    <table id="fl_stuck_yep_cf" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Yep Task ID</th>
                            <th class="text-center">POCO (instance)</th>
                            <th class="text-center">Tracking Diary</th>
                            <th class="text-center">Progress Status</th>
                            <th class="text-center">POCO Status</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center"><a target="_blank" href="{{url('/find/patch-ep')}}" >{{ $data->yep_task_id  }}</a></td> 
                                <td class="text-center"><a target="_blank" href="{{url('/bpm/task/find?doc_no=')}}{{ $data->poco  }}{{'&module=YEP_Order&created_date='}}" >{{ $data->poco }}</a></td>  
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->poco  }}" >Link</a></td>   
                                <td class="text-center">{{ $data->progress_status }}</td>
                                <td class="text-center">{{ $data->status_name }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->\
<script>
    $(function() {
            TablesDatatables.init();
        });
        App.datatables();
    $('#fl_stuck_yep_cf').dataTable({
            order: [
                [0, "desc"]
            ],
            columnDefs: [],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, 50, -1],
                [10, 20, 30, 50, 'All']
            ]
        });
    
</script>

@endsection



