@extends('layouts.guest-dash')



@section('content')

    
        <div class="content-header">
            <div class="header-section">
                <ul class="nav-horizontal text-center">
                    <li class="active">
                        <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
                    </li>
                    <li>
                        <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
                    </li>
                    <li>
                        <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
                    </li>
                    <li>
                        <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
                    </li>
                    <li>
                        <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
                    </li>
                    <li>
                        <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
                    </li>
                    <li >
                        <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
                    </li>
                </ul>
            </div>
        </div> 
        

       

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
         
        <div class="block block-alt-noborder full">
            
 
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Task List BPM</strong></h1>
                </div>
                    @if($status_api != null)
                    <h5 class="alert alert-danger">{{$status_api}}</h5>
                    @endif
                    
                    <div class="block">
                        <form id="form-search-task" action="{{url("/bpm/task/find")}}" method="get" class="form-horizontal form-bordered">

                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="doc_no">Document Number <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <input type="text" id="doc_no" name="doc_no" required class="form-control" value="{{old('doc_no')}}">
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="module">Module <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="module" name="module" required class="form-control">
                                                    <option value="">Please select</option>
                                                    @foreach(App\Services\EPService::$BPM_COMPOSITE_MODULE as  $key => $value)
                                                    
                                                    <option value="{{$key}}" @if($key == old('module') ) selected @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                        
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="created_date">Date Created</label>
                                        <div class="col-md-9">
                                            <input type="text" id="created_date" name="created_date" class="form-control input-datepicker" 
                                                   data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" value="{{old('created_date')}}">
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-actions form-actions-button text-right " style="margin-right:30px;">
                                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
                            </div>
                        </form>
                        
                    </div>
                  
                    <div class="table-responsive">
                        <table id="basic-datatable" class="table table table-vcenter table-striped">
                            <thead>
                            <tr>
                                <th class="text-center">TaskID</th>
                                <th class="text-center">Task Number</th>
                                <th class="text-center">State</th>
                                <th class="text-center">Task Name</th>
                                <th class="text-center">Title</th>
                                <th class="text-center">Process</th>
                                <th class="text-center">Doc No</th>
                                <th class="text-center">Activity</th>
                                <th class="text-center">Instance ID</th>
                                <th class="text-center">Composite</th>
                                <th class="text-center">Created Date</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if(count($listdata) > 0)     
                                @foreach ($listdata as $data)
                                <?php
                                    $createDate = date("d/m/Y h:i:s A", substr($data["createDate"], 0, 10));
                                ?>
                                <tr>
                                    <td class="text-center">{{ $data['taskId'] }}</td>
                                    <!--<a target="_blank" href="{{ url('/bpm/detail/taskid') }}/{{ $data['taskId'] }}" >{{ $data['taskId'] }}</a><br />-->
                                    <td class="text-center">{{ $data['taskNumber'] }}</td>
                                    <td class="text-center">{{ $data['state'] }}</td>
                                    <td class="text-center">{{ $data['taskName'] }}</td>
                                    <td class="text-center">{{ $data['title'] }}</td>
                                    <td class="text-center">{{ $data['process'] }}</td>
                                    <td class="text-center">{{ $data['docNumber'] }}</td>
                                    <td class="text-center">{{ $data['activityName'] }}</td>
                                    <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_module={{$data['composite']}}&composite_instance_id={{$data['instanceId']}}" target="_blank" > {{ $data['instanceId'] }}</a></td>
                                    <td class="text-center">{{ $data['compositeDN'] }}</td>
                                    <td class="text-center">{{ $createDate }}</td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="12">No Records</td>
                                </tr>    
                            @endif
                            </tbody>
                        </table>
                
                    </div>
                
                
            </div>
            <!-- END Customer Addresses Block -->
        </div>
  


    
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    

</script>
@endsection



