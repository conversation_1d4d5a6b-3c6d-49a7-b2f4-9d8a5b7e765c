<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use Carbon\Carbon;

class IgfmasController extends Controller {

    use OSBService;
    use SSHService;

    public function getDashboardIgfmas() {
        return view('dashboard.igfmas', []);
    }

    public function checkMonitoringOutboundProcess() {
        $dateSearch = Carbon::now()->format('Y-m-d');
        $collect = collect();
        $collect->push($this->getListStatisticTotalFileCreationTransferredByScheduler($dateSearch,'GFM-380', 'AR502')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredByScheduler($dateSearch,'GFM-350', 'AP516')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredByScheduler($dateSearch,'GFM-010', 'APIVE')[0]);

        $html = "";
        $html .= "<div class='row'>
                    <div class='col-lg-12'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>OUTBOUND Files Created & Transferred</strong> ($dateSearch)</h2>
                            </div>
                                <table class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th>Process ID</th>
                                            <th>Service Code</th>
                                            <th>Service Name</th>
                                            <th>Date</th>
                                            <th>Total File Generated</th>
                                            <th>Total File Batch</th>
                                            <th>Total Sent</th>
                                        </tr>
                                    </thead>
                                <tbody>";

        foreach ($collect as $data) {
            $html .= "
                                    <tr>
                                        <td><strong>$data->process_id</strong></td>
                                        <td>$data->service_code</td>
                                        <td style='width: 35%;'><>$data->service_name</td>
                                        <td style='width:75px'>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total_di_interface_created</span></td>
                                        <td class='text-center'><span class='badge label-danger'>$data->total_batch_file_created</span></td> 
                                        <td class='text-center'><span class='badge label-danger'>$data->total_sent</span></td>    
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function checkMonitoringInboundProcess() {
        $dateSearch = Carbon::now()->format('Y-m-d');
        $collect = collect();
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-140', 'AP511')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-370', 'APOVE')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-090', 'APERR')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-390', 'AR902')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-190', 'GLBAC')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-200', 'GLSEG')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-210', 'GLVOT')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-220', 'GLDNA')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-230', 'GLPRG')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-240', 'GLPRJ')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-260', 'GLGLC')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-270', 'GLPCG')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-280', 'GLPTJ')[0]);
        $collect->push($this->getListStatisticTotalFileReceivedByScheduler($dateSearch,'GFM-360', 'CMBNK')[0]);
        
        

        $html = "";
        $html .= "<div class='row'>
                    <div class='col-lg-12'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>OUTBOUND Files Created & Transferred</strong> ($dateSearch)</h2>
                            </div>
                                <table class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th>Process ID</th>
                                            <th>Service Code</th>
                                            <th>Service Name</th>
                                            <th>Date</th>
                                            <th>Total Transferred</th>
                                            <th>Total Received</th>
                                            <th>Total Processed</th>
                                        </tr>
                                    </thead>
                                <tbody>";

        foreach ($collect as $data) {
            $html .= "
                                    <tr>
                                        <td><strong>$data->process_id</strong></td>
                                        <td>$data->service_code</td>
                                        <td style='width: 35%;'>$data->service_name</td>
                                        <td style='width:75px'>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total_transferred</span></td>
                                        <td class='text-center'><span class='badge label-danger'>$data->total_batch_file_created</span></td>  
                                        <td class='text-center'><span class='badge label-danger'>$data->total_di_interface_created</span></td>    
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function getDashboardBatchOutFolder() {
        $batchName = request()->batch_name;
        $lengthFileName = request()->length_filename;

        if ($batchName == null && $lengthFileName == null) {
            return "Invalid Request";
        }
        $outboundFiles = null; // $this->getListEpBatchFolderOUT($batchName, $lengthFileName);
        $totalOutboundFilesPending = 0; // count($outboundFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        $batchName Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound?batch_name=$batchName&length_filename=$lengthFileName'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP transfer files to Integration Server</small></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

}
