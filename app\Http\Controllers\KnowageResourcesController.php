<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Guzzle;
use Response;
use Carbon\Carbon;

class KnowageResourcesController extends Controller {
    /**
     * Create a new controller instance.
     *
     * @return void
     */

    private $razer_endpoint;

    private function UserData($user_info){ 
        return array(
            "username"      => $user_info->user_name,
            "first_name"    => $user_info->first_name,
            "last_name"     => $user_info->last_name,
        );
    }

    public function __construct(){
        $this->razer_endpoint = env("KNOWAGE_RESOURCES_API");
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(){
        return view('knowage_resources\index');
    }

    public function AjaxRazerTransactionDetail($razer_id){
        $uri = $this->razer_endpoint.'/SM/razerpay-transaction-detail/' . (int) $razer_id;
        $response = Guzzle::get($uri);
        return Response::make($response->getBody(), 200)->content();
    }

    public function AjaxRazerTransactionUpload(Request $request){
        $input_data = $request->all();
        $uri = $this->razer_endpoint.'/SM/razerpay-consolidation';
        $file = $request->file('razer_file');
        $path = $file->getRealPath();
        $response = Guzzle::post($uri,[
            'multipart' => [
                [
                    'name'     => 'razer_file',
                    'contents' => file_get_contents($path),
                    'filename' => $file->getClientOriginalName()
                ],
                [
                    'name'     => "user_profile",
                    'contents' => $input_data['user_profile']
                ]
            ],
        ]);
        return Response::make($response->getBody(), 200)->content();
    }

    public function AjaxRazerTransactionOverwrite(Request $request){
        $input_data = $request->all();
        $uri = $this->razer_endpoint.'/SM/razerpay-transaction-overwrite';
        $response = Guzzle::post($uri,["body"=> json_encode($input_data)]);
        return Response::make($response->getBody(), 200)->content();
    }

    public function RazerPayTransaction(){
        $uri = $this->razer_endpoint.'/SM/razerpay-upload-history';
        $upload_uri = $this->razer_endpoint.'/SM/razerpay-consolidation';
        $listHistory = Guzzle::get($uri);
        $listHistoryResponse = Response::make($listHistory->getBody(),200)->content();
        $cleanUploadHistory = $this->UploadTransasctionFileHistoryData(json_decode($listHistoryResponse, true));
        return view('knowage_resources.razerpay.new_transaction',[
            'list_upload_history' => $cleanUploadHistory,
            'upload_endpoint' => json_encode(array(
                'upload_uri' => $upload_uri,
                'user_profile' => $this->UserData(auth()->user()
            )))
        ]);
    }

    private function UploadTransasctionFileHistoryData($payloads){
        $result = [];
        foreach ($payloads as $p):
            $p['timestamp'] = Carbon::parse($p['timestamp'])->format('j M Y');
            $p['summary']['date'] = Carbon::parse($p['summary']['date'])->format('j M Y');
            $result[] = $p;
        endforeach;
        return $result;
    }


    
}
