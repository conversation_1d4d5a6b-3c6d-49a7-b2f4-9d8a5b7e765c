<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use App\User;
use DB;
use Carbon\Carbon;
use Log;
use Guzzle;
use Response;

class LoginController extends Controller
{
    /* 
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function login(Request $request)
    {
        
        $this->validateLogin($request);
        
        // If the class is using the ThrottlesLogins trait, we can automatically throttle
        // the login attempts for this application. We'll key this by the username and
        // the IP address of the client making these requests into this application.
        if ($this->hasTooManyLoginAttempts($request)) {
            $this->fireLockoutEvent($request);

            return $this->sendLockoutResponse($request);
        }

        
        if ($this->attemptLogin($request)) {
             return $this->sendLoginResponse($request);   
        }
        // If the login attempt was unsuccessful we will increment the number of attempts
        // to login and redirect the user back to the login form. Of course, when this
        // user surpasses their maximum number of attempts they will get locked out.
        $this->incrementLoginAttempts($request);

        return $this->sendFailedLoginResponse($request);
    }
    
    /**
     * Get the failed login response instance.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendFailedLoginResponse(Request $request)
    {
        $errors = [$this->username() => trans('auth.failed')];
        
        if ($request->expectsJson()) {
            return response()->json($errors, 422);
        }

        if(!$this->isAllowAccess($request)){
            $errorsNotAllowed = [$this->username() => 'You are not allowed! Please contact administrator to verify your account.'];

            return redirect()->back()
                ->withInput($request->only($this->username(), 'remember'))
                ->withErrors($errorsNotAllowed);  
        }
        
        return redirect()->back()
           ->withInput($request->only($this->username(), 'remember'))
            ->withErrors($errors);
    } 
    
    /**
     * Attempt to log the user into the application.
     * Overwrite using login to suiteCRM.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return bool
     */
    protected function attemptLogin(Request $request)
    {
        //$user = DB::table('users')->where('user_name',$request->username)->first();
        
        $user = User::where('user_name',$request->user_name)->first();
        if(!$user){
            return false;
        }
        if($user && $user->status != 'Active'){
            return false;
        }
        
        $user_hash = $user->user_hash;
        $password_md5 = md5($request->password);

        if($user_hash[0] != '$' && strlen($user_hash) == 32) {
            // Old way - just md5 password
            if(strtolower(md5($request->password)) == $user_hash){
                return $this->successLogin($request, $user);
            }
        }
        if(crypt(strtolower($password_md5), $user_hash) == $user_hash){
            
            return $this->successLogin($request, $user);
            
        }

        return false;
         
    }
    
    protected function logLastLogin($user){
        $dateToday = Carbon::now();
        $userLogin = DB::connection('mysql_ep_support')->table('ep_login_history')
                    ->where('user_id',$user->id)
                    ->whereDate('date',$dateToday->format('Y-m-d')) 
                    ->first();
        $groups = User::listRolesCRM($user);
        $email = User::getEmailUser($user->id);
        if($userLogin == null){
            DB::connection('mysql_ep_support')
                ->table('ep_login_history')
                ->insert(
                    [
                       'user_id' => $user->id, 
                       'username' => $user->user_name,
                       'fullname' => $user->first_name,
                       'email' => $email?$email->email_address:null,
                       'groups' => $groups->pluck('name'),
                       'date' => $dateToday->format('Y-m-d'),
                       'last_login' => Carbon::now()
                    ]);
        }else{
            DB::connection('mysql_ep_support')->table('ep_login_history')
                    ->where('user_id', $user->id)
                    ->whereDate('date',$dateToday->format('Y-m-d')) 
                    ->update([
                        'groups' => $groups->pluck('name'),
                        'fullname' => $user->first_name,
                        'email' => $email?$email->email_address:null,
                        'last_login' => Carbon::now()
                    ]);
        }
    }
    
    protected function successLogin($request,$user){
        $this->logLastLogin($user);
        if($this->isAllowAccess($request) == true){
            $this->guard()->loginUsingId($user->id);
            return true;
        }
        return false;
    }
    
    protected function isAllowAccess($request){
        if(User::isAllowAccessByGroupCRM($request) == false){
            //dd('Role not allow to access in EPSS');
            return false;
        }
        return true;
    }
    
    /**
     * Validate the user login request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    protected function validateLogin(Request $request)
    {
        $this->validate($request, [
            $this->username() => 'required', 'password' => 'required',
        ]);
    }
    
    
    /**
     * Get the login username to be used by the controller.
     *
     * @return string
     */
    public function username()
    {
        return 'user_name';
    }
    
    
    /**
     * Log the user out of the application.
     *
     * @param \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function logout(Request $request)
    {
        $this->guard()->logout();

        $request->session()->flush();

        $request->session()->regenerate();

        return redirect('/login');
    }

    public function responseCallback(Request $request){
        Log::info(__METHOD__.' >> received  : '.json_encode($request->all()));
        $input_data = $request->all();
        //$uri = 'http://192.168.120.161/cdccrm-dev/index.php?entryPoint=ProcessPaymentResult';
        $uri = 'http://192.168.68.159:2445/index.php?entryPoint=ProcessPaymentResult';
        $response = Guzzle::post($uri,["body"=> json_encode($input_data)]);
        return Response::make($response->getBody(), 200)->content();
    }
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
    }
}
