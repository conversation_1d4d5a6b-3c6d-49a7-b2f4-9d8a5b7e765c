@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>DP Request Note Stuck Task <br>
                <small>DP Request Note  Stuck Task List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>DP Request Note  Stuck Task List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>DP Request Note  Stuck Task List </strong>
                        <small></small>
                    </h1>
                </div>
                <p class="text-danger bolder">
                    <h5>FailedPendingApproval</h5> 
                    Issue cause of  Task Pending Approval for RN in BPM is not succesfully created. 
                    Ask Middleware team to refire all tasks
                </p>
             
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">TASK FAILED</th>
                            <th class="text-center">BPM INSTANCE ID</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">BPM FLOW ID</th>
                            <th class="text-center">DOC RN ID</th>
                            <th class="text-center">RN NO</th>
                            <th class="text-center">SQ NO</th>
                            <th class="text-center">DOC_TYPE</th>
                            <th class="text-center">TRACKING DIARY STATUS</th>
                            <th class="text-center">CREATED_DATE</th>
                            

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->type_error }}</td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></td>
                                <td class="text-center">{{ $data->composite_bpm }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/docno/insert_docno')}}/{{ $data->request_note_no  }}" >{{ $data->doc_id }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->request_note_no  }}" >{{ $data->request_note_no }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->quote_no  }}" >{{ $data->quote_no }}</a></td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center">{{ $data->tracking_diary_status }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



