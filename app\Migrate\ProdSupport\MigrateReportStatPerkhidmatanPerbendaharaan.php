<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\ProdSupport;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;



class MigrateReportStatPerkhidmatanPerbendaharaan {

    
    public static function run() {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        $collectData = self::getListDataFromExcel();
        if($collectData->count() > 0){
            self::migrateDataIntoTable($collectData);
        }
        MigrateUtils::logDump('Total : '.$collectData->count());
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function getListDataFromExcel() {
        $collectData = collect();
        $filename = '/app/Migrate/ProdSupport/data/StatistikPerkhidmatanPerbendaharaan2020BPK.csv';
        Excel::load($filename, function($reader) use (&$collectData) {
            $reader->each(function($row) use (&$collectData){
                //if($row->datetime != null){
                    $collectData->push($row);
                //}
            });
        });
        
        return $collectData;
        
    }
    
    protected static function migrateDataIntoTable($collectData) {
        foreach ($collectData as $data) {
            DB::connection('mysql_ep_prod_support')
                ->table('ps_fact_ep_service_rpt')
                ->insert(
                   [
                        "year" => $data->year, 
                        "month" => $data->month,
                        "bil_no" => $data->bil_no,
                        "service_name_bm" => $data->service_name_bm,
                        "service_name_bi" => $data->service_name_bi,
                        "online_offline" => $data->online_offline,
                        "total" => $data->total,
                        "month_no" => $data->month_no, 
                        "created_at" => Carbon::now(),
                        "created_by" => "Migrater",
                        "changed_at" => Carbon::now(),
                        "changed_by" => "Migrater"
                   ]     
                );        
        }
    }

    
}
