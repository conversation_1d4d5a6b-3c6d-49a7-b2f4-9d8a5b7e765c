@extends('layouts.guest-dash')

@section('content')

@if($mofHistorySupplierDetail != null)
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i> <strong>e<PERSON><PERSON><PERSON><PERSON> (Pembekal) : {{$mofHistorySupplierDetail[0]->company_name}}</strong></h1>
    </div>
    <div class="row">
        <div class="col-sm-3">
            <div class="block">
                <div class="block-title">
                    <h2>Maklumat Syarikat </h2>
                </div>
                <address>
                    <strong>Company Name</strong> : <strong> {{$mofHistorySupplierDetail[0]->company_name}} </strong><br />
                    <strong>SSM No </strong> : {{ $mofHistorySupplierDetail[0]->reg_no }}<br />
                    <strong>eP No </strong> : {{ $mofHistorySupplierDetail[0]->ep_no }} <a target="_blank" class="text-info" style="text-decoration: underline;" href="{{ url('/find/gfmas/apive/') }}/{{ $mofHistorySupplierDetail[0]->ep_no }}"><i class="fa fa-info-circle"></i> Check Apive</a><br />
                    <strong>Business Type </strong> : {{ $mofHistorySupplierDetail[0]->business_type }} &raquo; ({{ App\Services\EPService::$BUSINESS_TYPE[$mofHistorySupplierDetail[0]->business_type] }})<br />
                    <strong>Supplier Type </strong> : {{ $mofHistorySupplierDetail[0]->supplier_type }} <br />
                    <strong>Supplier ID </strong> :
                    @if(Auth::user()->isAdvRolesEp())
                    <a target="_blank" href="{{url('/find/supplier/')}}/{{ $mofHistorySupplierDetail[0]->appl_id }}/{{ $mofHistorySupplierDetail[0]->supplier_id }}">{{ $mofHistorySupplierDetail[0]->supplier_id }}</a>
                    @else
                    {{ $mofHistorySupplierDetail[0]->supplier_id }}
                    @endif
                    <br />
                    <strong>Appl ID </strong> : {{ $mofHistorySupplierDetail[0]->appl_id }} <br />
                    <strong>Appl No </strong> : {{ $mofHistorySupplierDetail[0]->appl_no }}<br />
                    <strong>Appl Type </strong> : {{ $mofHistorySupplierDetail[0]->appl_type }} &raquo; {{ App\Services\EPService::$APPL_TYPE[$mofHistorySupplierDetail[0]->appl_type] }} <br />
                    <strong>Establish Date </strong> : {{ $mofHistorySupplierDetail[0]->establish_date }} <br />
                    <strong>Last Date Changed </strong> : {{ $mofHistorySupplierDetail[0]->changed_date_sm }} <br />
                    <strong>Local Authority ID </strong> : {{ $mofHistorySupplierDetail[0]->local_authority_id }} <br />
                    <strong>Record Status </strong> : <span class="bolder">{{ $mofHistorySupplierDetail[0]->record_status_sm }} </span><br />
                    @if($basicCompInfo)
                    <strong>Address </strong> : <span @if($isData->is_address_hq_non_ascii) style="color:red; font-weight:bolder" @endif > ( Address ID: {{ $basicCompInfo->address_id }} )<br />
                        {{ $basicCompInfo->address_1 }}<br />
                        {{ $basicCompInfo->address_2 }} {{ $basicCompInfo->address_3 }}<br />
                        {{ $basicCompInfo->postcode }} {{ $basicCompInfo->city_name }}, {{ $basicCompInfo->district_name }}<br />
                        {{ $basicCompInfo->state_name }}, {{ $basicCompInfo->country_name }}
                    </span>
                    @if($isData->is_address_hq_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Address has special character Non-ASCII"></i>@endif
                    <br /><br />
                    <strong>Phone No: </strong> : {{ $basicCompInfo->phone_country }}{{ $basicCompInfo->phone_area }}{{ $basicCompInfo->phone_no }}
                    @if(strlen(trim($basicCompInfo->phone_no)) == 0)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Phone No. Company is Mandatory. Kindly do a patch data or Ask Supplier update profile"></i>@endif
                    <br />
                    @endif
                    <strong>Fax No: </strong> : {{ $basicCompInfo->fax_country }}{{ $basicCompInfo->fax_area }}{{ $basicCompInfo->fax_no }} <br /> <br />
                    @if($hqHistoryGstInfo)
                    <strong>GST Registration No. : </strong> {{ $hqHistoryGstInfo->gst_reg_no }}<br />
                    <strong>GST Effective Date : </strong> {{ $hqHistoryGstInfo->gst_eff_date }}<br /><br />
                    @else
                    <strong>GST Registration No. : </strong> Not Registered <br /><br />
                    @endif
                    @if(count($listinProgressSuppProcessAppl) > 0)
                    <strong>Supporting Document</strong> : {{ App\Services\EPService::$SUPPORTING_DOC_MODE[$listinProgressSuppProcessAppl[0]->supporting_doc_mode]  }}
                    <br />
                    @endif
                    <br />
                </address>
            </div>
        </div>
        <!--modal-->
        <div id="modal_list_supplier_items" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Supplier Items</span></h2>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="list-supplier-items-detail"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="modal-list-payment-history" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2><i class="fa fa-users"></i> <strong>Payment History : {{ count($listSuppPayment) }}</strong></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">

                                <div class="table-responsive">
                                    <table id="basic-datatable2" class="table table-striped table-vcenter">
                                        <thead>
                                            <tr>
                                                <th class="text-center">No.</th>
                                                <th class="text-center">Bill No.</th>
                                                <th class="text-center">Bill Type</th>
                                                <th class="text-center">Bill Date</th>
                                                <th class="text-center">Bill Amount</th>
                                                <th class="text-center">Order ID</th>
                                                <th class="text-center">Pending Process ID</th>
                                                <th class="text-center">Payment Date</th>
                                                <th class="text-center">Payment Mode</th>
                                                <th class="text-center">Status</th>
                                                <th class="text-center">Personnel</th>
                                                <th class="text-center">Receipt No.</th>
                                                <th class="text-center">Download Receipt</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($listSuppPayment as $indexKey => $data)
                                            <tr>
                                                <td class="text-center">{{ ++$indexKey }}</td>
                                                <td class="text-center">{{ $data->bill_no }}</td>
                                                <td class="text-center">{{ $data->bill_type }}</td>
                                                <td class="text-center">{{ $data->bill_date }}</td>
                                                <td class="text-center">{{ $data->payment_amt }}</td>
                                                <td class="text-center">{{ $data->payment_id }} - {{ $data->payment_gateway }}</td>
                                                <td class="text-center">{{ $data->pending_process_id }}
                                                    @if(Auth::user()->isAdvRolesEp() && $data->pending_process_id != null)
                                                    - <a target='_blank' href='{{url("/find/patch-ep")}}?patch=SM_PENDING_PROCESS&record={{ $data->pending_process_id }}'>Status: {{ $data->pending_process_status }} || Attempt: {{ $data->pending_process_attempt }} || ErrMsg: {{ $data->pending_process_err_msg }}</a>
                                                    @endif
                                                </td>

                                                <td class="text-center">{{ $data->payment_date }}</td>
                                                <td class="text-center">{{ $data->payment_mode }}</td>
                                                <td class="text-center">{{ $data->status }}</td>
                                                <td class="text-center">{{ $data->personnel }}</td>
                                                <td class="text-center">{{ $data->receipt_no }} </td>
                                                <td class="text-center">
                                                    @if($data->receipt_no && strlen($data->receipt_no) > 5)
                                                    @php $urlReport = env('EP_REPORT_URL','http://*************:5701/reports/rwservlet?ngepsit') @endphp
                                                    @if($data->bill_type == 'P' || $data->bill_type == 'R')
                                                    <a target="_blank" href="{{$urlReport}}&report=sm_payment_receipt.jsp&pmntid={{$data->payment_id}}&destype=cache&desformat=pdf"><i class="fa fa-download"></i></a>
                                                    @elseif($data->bill_type == 'S')
                                                    <a target="_blank" href="{{$urlReport}}&report=SM_TAX_INVOICE.jsp&pmntid={{$data->payment_id}}&destype=cache&desformat=pdf"><i class="fa fa-download"></i></a>
                                                    @endif
                                                    @endif
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
        <div id="modal-list-branch" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2><i class="fa fa-users"></i> <strong>List Branch: @if(count($listSupplierBranch) > 0){{ count($listSupplierBranch) }}@endif</strong></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">

                                <div class="table-responsive">
                                    <table id="basic-datatable3" class="table table-striped table-vcenter">
                                        <thead>
                                            <tr>
                                                <th class="text-center">Branch ID</th>
                                                <th class="text-center">Branch Name</th>
                                                <th class="text-center">Branch Code</th>
                                                <th class="text-center">SAP Vendor Code</th>
                                                <th class="text-center">Changed Date</th>
                                                <th class="text-left">GST</th>
                                                <th class="text-center">Address</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($listSupplierBranch as $indexKey => $data)
                                            <tr>
                                                <td class="text-center">{{ $data->supplier_branch_id }}</td>
                                                <td class="text-center" @if($data->is_branch_name_non_ascii) style="color:red; font-weight:bolder" @endif>{{ $data->branch_name }}</td>
                                                <td class="text-center">{{ $data->branch_code }}</td>
                                                <td class="text-center">{{ $data->sap_vendor_code }}</td>
                                                <td class="text-center">{{ $data->changed_date }}</td>
                                                <td class="text-left">
                                                    <strong>Registration No. :</strong> {{ $data->gst_reg_no }}<br />
                                                    <strong>Effective Date :</strong> {{ $data->gst_eff_date }}<br />
                                                </td>
                                                <td class="text-center" @if($data->is_non_ascii) style="color:red; font-weight:bolder" @endif>
                                                    (Address ID : {{ $data->address_id }}),<br />
                                                    {{ $data->address_1 }},
                                                    {{ $data->address_2 }},
                                                    {{ $data->address_3 }}<br />
                                                    {{ $data->postcode }} {{ $data->city_name }}, {{ $data->district_name }}<br />
                                                    {{ $data->state_name }}, {{ $data->country_name }}<br />
                                                    Phone : {{ $data->phone_country }} {{ $data->phone_area }}, {{ $data->phone_no }}
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>

                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
        <div id="modal-list-bank" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2><i class="fa fa-users"></i> <strong>List Bank: @if(count($listSupplierBank) > 0){{ count($listSupplierBank) }}@endif</strong></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="table-responsive">
                                    <table id="basic-datatable" class="table table-striped table-vcenter">
                                        <thead>
                                            <tr>
                                                <th class="text-center">No.</th>
                                                <th class="text-center">Bank Name</th>
                                                <th class="text-center">Acc. No.</th>
                                                <th class="text-center">Acc. Purpose</th>
                                                <th class="text-center">Is Default</th>
                                                <th class="text-center">Is HQ</th>
                                                <th class="text-center">Bank Branch</th>
                                                <th class="text-center">Changed Date</th>
                                                <th class="text-center">Supplier Branch Name</th>
                                                <th class="text-center">Supplier Branch Code</th>
                                                <th class="text-center">Supplier Bank Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($listSupplierBank as $indexKey => $data)
                                            <tr>
                                                <td class="text-center">{{ ++$indexKey }}</td>
                                                <td class="text-center">{{ $data->fin_org_name }} <span @if($data->bank_status == 9) class="text-danger" @endif>({{ App\Services\EPService::$RECORD_STATUS[$data->bank_status] }})</span></td>
                                                <td class="text-center">{{ $data->account_no }}</td>
                                                <td class="text-center">{{ $data->account_purpose }}</td>
                                                <td class="text-center">{{ $data->is_default_account }}</td>
                                                <td class="text-center">{{ $data->is_for_hq }}</td>
                                                <td class="text-center">{{ $data->changed_date }}</td>
                                                <td class="text-center">{{ $data->branch_name }}</td>
                                                <td class="text-center">{{ $data->branch_code }}</td>
                                                <td class="text-center">{{ $data->supplier_bank_status }} ({{ App\Services\EPService::$RECORD_STATUS[$data->supplier_bank_status] }})</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
        <div id="modal-category-code" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i> List Category Code</h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="table-responsive">
                                    <table id="datatable-category-code" class="table table-striped table-vcenter">
                                        <thead>
                                            <tr>
                                                <th class="text-center">No.</th>
                                                <th class="text-left">Category Code</th>
                                                <th class="text-left">Category</th>
                                                <th class="text-center">Category Type</th>
                                                <th class="text-left">Officer Decision</th>
                                                <th class="text-center" style="display: none;">Remarks</th>
                                                <th class="text-center">Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach ($listSupplierCategoryCode as $indexKey => $data)
                                            <tr>
                                                <td class="text-center">{{ ++$indexKey }}</td>
                                                <td class="text-center">{{ $data->category_code }}</td>
                                                <td class="text-left">
                                                    {{ $data->category_l1_code }} - {{ $data->category_l1_name }}<br />
                                                    {{ $data->category_l2_code }} - {{ $data->category_l2_name }}<br />
                                                    {{ $data->category_l3_code }} - {{ $data->category_l3_name }}<br />
                                                </td>
                                                <td class="text-center">{{ $data->is_special_category === 1 ? "Special" : "Normal" }}</td>
                                                <td class="text-left">
                                                    <strong>Approved Date :</strong> {{ $data->approved_date }}<br />
                                                    <strong>PO :</strong> {{ $data->is_approved_by_po }}<br />
                                                    <strong>Approver :</strong> {{ $data->is_approved_by_ap }}
                                                </td>
                                                <td class="text-left" style="display: none;">
                                                    <strong>PO :</strong> {{ $data->previous_po_remark }}<br />
                                                    <strong>Approver :</strong> {{ $data->previous_ap_remark }}
                                                </td>
                                                <td class="text-center">{{ $data->record_status }}</td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>

        <div class="col-sm-3">
            <div class="block">
                <div class="block-title">
                    <h2>Maklumat MOF</h2>
                </div>
                <address>
                    <strong>MOF NO </strong> : {{ $mofHistorySupplierDetail[0]->mof_no }}<br />
                    <strong>Effective Date </strong> : {{ $mofHistorySupplierDetail[0]->eff_date }}<br />
                    <strong>Expired Date </strong> : {{ $mofHistorySupplierDetail[0]->exp_date }} <br />
                    <strong>Record Status </strong> : {{ $mofHistorySupplierDetail[0]->record_status_mof }}<br />

                    @if($suppHistoryMofStatus)
                    <br />
                    <strong>Bumi Status </strong> : {{ $suppHistoryMofStatus->bumi_status }}<br />
                    <strong>Registration Type </strong> : {{ $suppHistoryMofStatus->type }}<br />
                    @endif
                    <br />
                    <strong>Cert Mof</strong> : <br />
                    @if($listSuppMofVirtCert && count($listSuppMofVirtCert) > 0)
                    @foreach ($listSuppMofVirtCert as $cerVirt)
                    {{ $cerVirt->cert_serial_no  }} ({{ $cerVirt->cert_type  }}) #{{ $cerVirt->appl_id  }}
                    <a href="{{url('/download/mofcert')}}/{{$cerVirt->mof_cert_id}}" target="_blank">Download</a>
                    <br />
                    @endforeach
                    @else
                    Tiada
                    @endif
                    <br />
                    <strong>List branch</strong> : @if(count($listSupplierBranch) > 0 )
                    <a href="#modal-list-branch" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierBranch)}}</a>
                    @if($isData->is_address_branch_non_ascii || $isData->is_branch_name_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Branch Name or Address has special character Non-ASCII"></i>@endif
                    @else Tiada @endif<br />
                    <br />
                    <strong>List Bank</strong> : @if($listSupplierBank)
                    <a href="#modal-list-bank" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierBank)}}</a>
                    @foreach($listSupplierBank as $bank) @if($bank->bank_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title="Bank is not active in 1GFMAS"></i> @break @endif @endforeach
                    @else Tiada @endif<br />
                    <br />
                    <strong>List Category Code</strong> : @if(count($listSupplierCategoryCode) > 0 )
                    <a href="#modal-category-code" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierCategoryCode)}}</a> @else Tiada @endif<br />
                    <br />
                </address>
            </div>
        </div>
        @if($basicCompInfo)
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Business Network</h2>
                </div>

                <address>
                    <strong>Federal? </strong> : {{ $basicCompInfo->is_with_federal  }}<br />
                    <strong>State? </strong> : {{ $basicCompInfo->is_with_state  }}<br />
                    <strong>Local Council? </strong> : {{ $basicCompInfo->is_with_statutory  }}<br />
                    <strong>GLC? </strong> : {{ $basicCompInfo->is_with_glc  }}<br />
                    <strong>Others? </strong> : {{ $basicCompInfo->is_with_others  }}<br />
                </address>
            </div>
        </div>
        @endif
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Application Status</h2>
                </div>
                @if(count($listHistoryWorkFlow)>0)
                <strong>Appl ID</strong> : {{ $listHistoryWorkFlow[0]->appl_id  }}<br />
                <strong>Appl No</strong> : {{ $listHistoryWorkFlow[0]->appl_no  }}<br />
                <strong>Created </strong> : {{ $listHistoryWorkFlow[0]->appl_created_date  }} <br />
                <strong>Changed </strong> : {{ $listHistoryWorkFlow[0]->appl_change_date  }} <br />
                <strong>Soal Selidik</strong> : {{ App\Services\EPService::$YES_NO[$listHistoryWorkFlow[0]->is_questionnaire]  }}<br />
                <strong>Supporting Documents</strong> : {{ App\Services\EPService::$SUPPORTING_DOC_MODE[$listHistoryWorkFlow[0]->supporting_doc_mode]  }}<br />
                <strong>Appl Reg Status ID</strong> : {{ $listHistoryWorkFlow[0]->reg_status_id  }}<br />
                <strong>Appl Category</strong> : {{ $listHistoryWorkFlow[0]->appl_category  }}<br />
                <strong>Appl Is Resubmit</strong> : {{ App\Services\EPService::$YES_NO[$listHistoryWorkFlow[0]->is_resubmit]  }}<br />
                <strong>Appl Original</strong> : {{ $listHistoryWorkFlow[0]->original_appl_id  }}<br />
                <strong>Appl Valid SSM</strong> : {{ App\Services\EPService::$YES_NO[$listHistoryWorkFlow[0]->is_appl_valid_with_ssm]  }}<br />
                <strong>Appl Record Status</strong> : {{ App\Services\EPService::$RECORD_STATUS[$listHistoryWorkFlow[0]->record_status]  }} &nbsp; <br /><br />
                @endif
                <div class="block">
                    @foreach ($listHistoryWorkFlow as $datawf)
                    <address>
                        <strong>Is Current</strong> : {{ $datawf->is_current  }} &nbsp; , &nbsp;
                        <strong>Status </strong> : {{ $datawf->wf_status  }}<br />
                        <strong>StatusID </strong> : {{ $datawf->wf_status_id  }}<br />
                        <strong>Created Date</strong> : {{ $datawf->wf_created_date  }}<br />
                    </address>
                    @endforeach
                </div>

                @if(isset($listApplSectionReview) && count($listApplSectionReview) > 0)
                <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                @foreach($listApplSectionReview as $key => $objRev )
                <strong>{{$key+1}} : Role Type: {{$objRev->role_type}}</strong> :
                <strong>Recommendation: {{$objRev->recommendation}}</strong> on {{$objRev->remark_date}} <br />
                <strong>Remark:</strong><span style="font-style: italic">{{ $objRev->remark_to_approver  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listApplRejectReason) && count($listApplRejectReason) > 0)
                <strong><span class="bolder">Reject Reason </span></strong><br />
                @foreach($listApplRejectReason as $key=>$objRej )
                <strong>{{$key+1}} : on {{$objRev->changed_date}} </strong><br />
                <strong>Remark:</strong><span style="font-style: italic"> {{ $objRej->reason_desc }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listRemarksCancelReject) && count($listRemarksCancelReject) > 0)
                <strong><span class="bolder">Remarks </span></strong><br />
                @foreach($listRemarksCancelReject as $objRemark)
                <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listAttachmentCancelReject) && count($listAttachmentCancelReject) > 0)
                <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                @foreach($listAttachmentCancelReject as $objAttReject)
                <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank">{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                @endforeach
                <br /><br />
                @endif
                @if($basicCompInfo && $basicCompInfo->appl_id)
                <div class='widget'>
                    <div id="count-application-inquiry-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div id="count-application-rejected-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    @if($mofHistorySupplierDetail != null)
    <div class="block">
        <div class="block-title epss-title-s2">
            <h2><i class="fa fa-users"></i> <strong>Jumlah Pengguna Pembekal : {{ count($mofHistorySupplierDetail) }}</strong></h2>
        </div>
        <div class="row">
            <div class="col-sm-12">

                <div class="table-responsive">
                    <table id="" class="table table-striped table-vcenter">
                        <thead>
                            <tr>
                                <th class="text-center">No.</th>
                                <th class="text-center">Name</th>
                                <th class="text-center">Identification No.</th>
                                <th class="text-center">Email</th>
                                <th class="text-center">Role</th>
                                <th class="text-center">Softcert Status</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach ($mofHistorySupplierDetail as $indexKey => $data)
                            <tr>
                                <td class="text-center">{{ ++$indexKey }}</td>
                                <td class="text-center">{{ $data->name }}</td>
                                <td class="text-center">
                                    @if(Auth::user()->isAdvRolesEp())
                                    <a target="_blank" href="{{url('/find/userpersonnel/')}}/{{ $data->appl_id }}/{{ $data->personnel_id }}">{{ $data->identification_no }}</a>
                                    @else
                                    {{ $data->identification_no }}
                                    @endif
                                </td>
                                <td class="text-center">{{ $data->email }}</td>
                                <td class="text-center">{{ $data->ep_role }}
                                    @if($data->ep_role != null && $data->user_id != null)
                                    @if(Auth::user()->isPatcherRolesEp())
                                    <a class="btn btn-warning btn-xs" href="{{url('/find/userpersonnel-sync-role')}}/{{ $data->personnel_id }}" target="_blank">
                                        Sync Role</a>
                                    @endif
                                    @endif
                                </td>
                                <td class="text-center">{{ $data->is_softcert }}</td>
                                <td class="text-center">

                                    <button class="btn btn-info btn-xs" data-toggle="collapse" data-target="#row_{{$data->personnel_id}}">
                                        Details</button>
                                </td>
                            </tr>
                            <tr id="row_{{$data->personnel_id}}" @if(strlen($data->ep_role) > 0)class="collapsed" @else class="collapse" @endif>
                                <td class="text-center" colspan="7">
                                    <!-- Customer Addresses Block -->
                                    <div class="block" @if($data->ep_role == 'MOF_SUPPLIER_ADMIN')style="background-color: inherit;"@endif>
                                        <div class="row">
                                            @if($mofHistorySupplierDetail != null)
                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat User Login </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                        <strong>Login ID </strong> : <a class="" href="{{url('find/userlogin')}}?login_id={{ $data->login_id  }}" target="_blank">{{ $data->login_id  }}</a><br />
                                                        <strong>Name </strong> : {{ $data->user_name  }}<br />
                                                        <strong>Email </strong> : {{ $data->user_email  }}<br />
                                                        <strong>ICNO </strong> : {{ $data->user_identification_no  }}<br />
                                                        <strong>Mobile </strong> : {{ $data->user_mobile }}<br />
                                                        <strong>Record Status </strong> : {{ $data->record_status_pu }}<br />
                                                        <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->changed_date_pu }} <br />

                                                        <br />
                                                        <strong>Peranan</strong> : <br />

                                                    </address>
                                                </div>
                                            </div>
                                            @endif

                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat Diri Staf Syarikat </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                                                        <strong>Name </strong> : {{ $data->name  }}<br />
                                                        <strong>Designation </strong> : {{ $data->designation  }}<br />
                                                        <strong>Email </strong> : {{ $data->email  }}<br />
                                                        <strong>Role </strong> : {{ $data->ep_role }}<br />
                                                        <strong>Mobile </strong> : {{ $data->mobile }}<br />
                                                        <strong>Race </strong> : {{ $data->race_name }}<br />
                                                        <strong>Identity_Resident Status </strong> : {{ $data->identity_resident_status }}<br />
                                                        <strong>SoftCert Status </strong> : {{ $data->is_softcert  }}<br />
                                                        <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                                                        <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                                                        <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                                                        <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Record Status </strong> : {{ $data->record_status_smp }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->changed_date_smp }} <br />
                                                        <strong>Rev. No. </strong> : {{ $data->rev_no }} <br />
                                                    </address>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
    @endif
</div>
@endif



<!-- END Customer Addresses Block -->

<!-- MODAL: Inquiries / Rejected Modal -->
<div id="modal-list-data-inquiries-reject" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-inquiries-reject">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-inquiries-reject" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>



@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    $(function() {
        TablesDatatables.init();
    });
</script>
<script>
    var APP_URL = {!!json_encode(url('/'))!!}

    /** initialize datatable **/
    App.datatables();
    $(document).ready(function() {
        $('.supplier-items-catalog').on("click", '.modal-list-data-action', function() {
            console.log("try");
            $('.spinner-loading').show();
            $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.list-supplier-items-detail').html($data).fadeIn();
                    $('#items-supplier-datatable').dataTable({
                        order: [
                            [2, "desc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

        });

    });
</script>
@if($mofHistorySupplierDetail != null)
@if($basicCompInfo && $basicCompInfo->appl_id)
<script>
    $(document).ready(function() {
        App.datatables();

        let tableListData = $('#basic-datatable-inquiries-reject').DataTable({
            columnDefs: [{
                orderable: false,
                targets: [0]
            }],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, -1],
                [10, 20, 30, 'All']
            ]
        });
        $('.widget').on("click", '.modal-list-data-action', function() {
            $('.spinner-loading').show();
            $('#basic-datatable-inquiries-reject').html('').fadeIn();
            $('#modal-list-data-header-inquiries-reject').text($(this).attr('data-title'));

            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-inquiries-reject').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-inquiries-reject').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });
            $.ajax({
                url: APP_URL + "/find/supplier/application-inquiry/count/{{$basicCompInfo->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-inquiry-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + "/find/supplier/application-rejected/count/{{$basicCompInfo->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-rejected-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
                }
            });

        })


        $.ajax({
            url: APP_URL + "/find/supplier/application-inquiry/count/{{$basicCompInfo->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-inquiry-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
            }
        });

        $.ajax({
            url: APP_URL + "/find/supplier/application-rejected/count/{{$basicCompInfo->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-rejected-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
            }
        });
    });
</script>
@endif
@endif
@endsection