<!DOCTYPE html>
<!--[if IE 8]>         <html class="no-js lt-ie9"> <![endif]-->
<!--[if IE 9]>         <html class="no-js lt-ie10"> <![endif]-->
<!--[if gt IE 9]><!-->
<html class="no-js"> <!--<![endif]-->

<head>
    <meta charset="utf-8">

    <title>eP Support System</title>

    <meta name="description" content="eP Supplier">
    <meta name="author" content="eP Supplier">
    <meta name="robots" content="noindex, nofollow">

    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">

    <!-- Icons -->
    <!-- The following icons can be replaced with your own, they are used by desktop and mobile browsers -->
    <link rel="shortcut icon" href="/img/favicon.png">
    <link rel="apple-touch-icon" href="/img/icon57.png" sizes="57x57">
    <link rel="apple-touch-icon" href="/img/icon72.png" sizes="72x72">
    <link rel="apple-touch-icon" href="/img/icon76.png" sizes="76x76">
    <link rel="apple-touch-icon" href="/img/icon114.png" sizes="114x114">
    <link rel="apple-touch-icon" href="/img/icon120.png" sizes="120x120">
    <link rel="apple-touch-icon" href="/img/icon144.png" sizes="144x144">
    <link rel="apple-touch-icon" href="/img/icon152.png" sizes="152x152">
    <link rel="apple-touch-icon" href="/img/icon180.png" sizes="180x180">
    <!-- END Icons -->

    <!-- Stylesheets -->
    <!-- Bootstrap is included in its original form, unaltered -->
    <link rel="stylesheet" href="/css/bootstrap.min.css">

    <!-- Related styles of various icon packs and plugins -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.10.16/css/jquery.dataTables.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/buttons/1.5.1/css/buttons.dataTables.min.css">

    <!-- Related styles of various icon packs and plugins -->
    <link rel="stylesheet" href="/css/plugins-backend.css">


    <!-- The main stylesheet of this template. All Bootstrap overwrites are defined in here -->
    <link rel="stylesheet" href="/css/main-backend.css">

    <!-- Include a specific file here from css/themes/ folder to alter the default theme of the template -->

    <!-- The themes stylesheet of this template (for using specific theme color in individual elements - must included last) -->
    <link rel="stylesheet" href="/css/themes-backend.css">

    <link rel="stylesheet" href="/css/themes/epss.css?v=2.0">
    <!-- END Stylesheets -->

    <!-- Load and execute css code used only in this page -->
    @yield('cssprivate')

    <!-- Modernizr (browser feature detection library) & Respond.js (enables responsive CSS code on browsers that don't support it, eg IE8) -->
    <script src="/js/vendor/modernizr-respond.min.js"></script>

    @stack('styleGPKIRoaming')
</head>

<body>
    <!-- Page Wrapper -->
    <!-- In the PHP version you can set the following options from inc/config file -->
    <!--
            Available classes:

            'page-loading'      enables page preloader
        -->
    <div id="page-wrapper">
        <!-- Preloader -->
        <!-- Preloader functionality (initialized in js/app.js) - pageLoading() -->
        <!-- Used only if page preloader is enabled from inc/config (PHP version) or the class 'page-loading' is added in #page-wrapper element (HTML version) -->
        <div class="preloader themed-background">
            <h1 class="push-top-bottom text-light text-center"><strong>Sila Tunggu Sebentar..</strong></h1>
            <div class="inner">
                <h3 class="text-light visible-lt-ie9 visible-lt-ie10"><strong>Loading..</strong></h3>
                <div class="preloader-spinner hidden-lt-ie9 hidden-lt-ie10"></div>
            </div>
        </div>
        <!-- END Preloader -->

        <!-- Page Container -->
        <!-- In the PHP version you can set the following options from inc/config file -->
        <!--
                Available #page-container classes:

                '' (None)                                       for a full main and alternative sidebar hidden by default (> 991px)

                'sidebar-visible-lg'                            for a full main sidebar visible by default (> 991px)
                'sidebar-partial'                               for a partial main sidebar which opens on mouse hover, hidden by default (> 991px)
                'sidebar-partial sidebar-visible-lg'            for a partial main sidebar which opens on mouse hover, visible by default (> 991px)
                'sidebar-mini sidebar-visible-lg-mini'          for a mini main sidebar with a flyout menu, enabled by default (> 991px + Best with static layout)
                'sidebar-mini sidebar-visible-lg'               for a mini main sidebar with a flyout menu, disabled by default (> 991px + Best with static layout)

                'sidebar-alt-visible-lg'                        for a full alternative sidebar visible by default (> 991px)
                'sidebar-alt-partial'                           for a partial alternative sidebar which opens on mouse hover, hidden by default (> 991px)
                'sidebar-alt-partial sidebar-alt-visible-lg'    for a partial alternative sidebar which opens on mouse hover, visible by default (> 991px)

                'sidebar-partial sidebar-alt-partial'           for both sidebars partial which open on mouse hover, hidden by default (> 991px)

                'sidebar-no-animations'                         add this as extra for disabling sidebar animations on large screens (> 991px) - Better performance with heavy pages!

                'style-alt'                                     for an alternative main style (without it: the default style)
                'footer-fixed'                                  for a fixed footer (without it: a static footer)

                'disable-menu-autoscroll'                       add this to disable the main menu auto scrolling when opening a submenu

                'header-fixed-top'                              has to be added only if the class 'navbar-fixed-top' was added on header.navbar
                'header-fixed-bottom'                           has to be added only if the class 'navbar-fixed-bottom' was added on header.navbar

                'enable-cookies'                                enables cookies for remembering active color theme when changed from the sidebar links
            -->
        @if (Auth::user())
            <div id="page-container" class="sidebar-no-animations sidebar-visible-lg">
        @else
            <div id="page-container" class="sidebar-no-animations">
        @endif

                @if (Auth::user())
                    <!-- Main Sidebar -->
                    <div id="sidebar">
                        <!-- Wrapper for scrolling functionality -->
                        <div id="sidebar-scroll">
                            <!-- Sidebar Content -->
                            <div class="sidebar-content">
                                <!-- Brand -->
                                <a href="/home" class="sidebar-brand">
                                    <img src="/img/logo.svg" width="26px">
                                    <span class="sidebar-nav-mini-hide" style="padding-left: 6px;">
                                        <strong>ePSS
                                            <small><span class="label" style="background-color: rgba(255, 255, 255, 0.2);">{{env('APP_ENV')}}</span></small>
                                        </strong>
                                    </span>
                                </a>
                                <!-- END Brand -->

                                <!-- User Info -->
                                <div class="sidebar-section sidebar-user clearfix sidebar-nav-mini-hide">

                                    <div class="sidebar-user-name"><strong>{{Auth::user()->first_name}}</strong></div>
                                    {{--@foreach(Auth::user()->roles() as $role)--}}
                                    {{--<small>{{$role}}</small><br />--}}
                                    {{--@endforeach--}}
                                    <ul class="sidebar-nav">
                                        <li class="sidebar-header"></span>
                                            <span class="sidebar-header-title">
                                                @if(Auth::user()->roles())
                                                    @foreach(Auth::user()->roles() as $role)
                                                        <small>{{$role}}</small><br />
                                                    @endforeach
                                                @endif
                                            </span>
                                        </li>
                                    </ul>
                                    <ul class="sidebar-nav">
                                        <li class="sidebar-header">
                                            <span class="sidebar-header-options clearfix"><a href="{{ url('/logout') }}"
                                                    onclick="event.preventDefault();
                        document.getElementById('logout-form').submit();" data-toggle="tooltip" data-placement="bottom"
                                                    title="Logout"><i class="gi gi-exit"></i></a></span>
                                            <span class="sidebar-header-title"></span>
                                            <form id="logout-form" action="{{ url('/logout') }}" method="POST"
                                                style="display: none;">
                                                {{ csrf_field() }}
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                                <!-- END User Info -->

                                @include('layouts.inc_menu_basic_user')                

                                @include('layouts.inc_menu_specialist_developer')

                                @include('layouts.inc_menu_it_support')

                                @include('layouts.inc_menu_unit_ep')

                                @include('layouts.inc_menu_specialist_operation')

                                @include('layouts.inc_menu_ep_notify')
                                <!-- END Sidebar Navigation -->

                                <br />
                                <br />
                                <div class="sidebar-header sidebar-nav-mini-hide">
                                    <span class="sidebar-header-title">Activity</span>
                                </div>
                                @if(Auth::user()->getLoginHistory())
                                    <div class="sidebar-section sidebar-nav-mini-hide">
                                        <div class="alert alert-success alert-alt">
                                            <small>{{Carbon\Carbon::now()->diffForHumans(Auth::user()->getLoginHistory()->last_login)}}</small><br>
                                            <i class="fa fa-thumbs-up fa-fw"></i> last login on
                                            {{Auth::user()->getLoginHistory()->last_login}}
                                        </div>
                                    </div>
                                @endif

                            </div>
                            <!-- END Sidebar Content -->
                        </div>
                        <!-- END Wrapper for scrolling functionality -->
                    </div>
                    <!-- END Main Sidebar -->
                @endif
                <!-- Main Container -->
                <div id="main-container">
                    <!-- Header -->
                    <!-- In the PHP version you can set the following options from inc/config file -->
                    <!--
                            Available header.navbar classes:
    
                            'navbar-default'            for the default light header
                            'navbar-inverse'            for an alternative dark header
    
                            'navbar-fixed-top'          for a top fixed header (fixed sidebars with scroll will be auto initialized, functionality can be found in js/app.js - handleSidebar())
                                'header-fixed-top'      has to be added on #page-container only if the class 'navbar-fixed-top' was added
    
                            'navbar-fixed-bottom'       for a bottom fixed header (fixed sidebars with scroll will be auto initialized, functionality can be found in js/app.js - handleSidebar()))
                                'header-fixed-bottom'   has to be added on #page-container only if the class 'navbar-fixed-bottom' was added
                        -->

                    @if (Auth::user())
                        <!-- END Header -->
                        <header class="navbar navbar-default">
                            <!-- Left Header Navigation -->
                            <ul class="nav navbar-nav-custom">
                                <!-- Main Sidebar Toggle Button -->
                                <li>
                                    <a href="javascript:void(0)" onclick="App.sidebar('toggle-sidebar');
                                                    this.blur();">
                                        <i class="fa fa-bars fa-fw"></i>
                                    </a>
                                </li>
                                <!-- END Main Sidebar Toggle Button -->
                            </ul>
                            <!-- END Left Header Navigation -->

                            <!-- Search Form -->
                            @yield('header')
                            <!-- END Search Form -->

                            <!-- Right Header Navigation -->
                            <ul class="nav navbar-nav-custom pull-right">
                            </ul>
                            <!-- END Right Header Navigation -->
                        </header>
                    @endif

                    <!-- Page content -->
                    <div id="page-content">
                        @yield('content')
                    </div>
                    <!-- END Page Content -->

                    <!-- Footer -->
                    <footer class="clearfix">
                        <div class="pull-right">
                            &nbsp;
                        </div>
                        <div class="pull-left">
                            <span id="year-copy"></span> &copy; <a href="#" target="_blank">Direkacipta oleh Commerce
                                Dot Com Sdn Bhd</a>
                        </div>
                    </footer>
                    <!-- END Footer -->


                    @if (env('APP_ENV') !== '')
                        <!-- Sticky Application Environment -->
                        <div id="app-info">
                            <span class="app-environment"
                                style="@if(env('APP_ENV') === 'SIT') background-color: #3c40c6; @elseif(env('APP_ENV') === 'DRC') background-color: #f53b57; @endif">
                                <strong>{{env('APP_ENV')}}</strong>
                            </span>
                        </div>
                        <!-- END Sticky Application Environment -->
                    @endif

                </div>
                <!-- END Main Container -->
            </div>
            <!-- END Page Container -->
        </div>
        <!-- END Page Wrapper -->

        <!-- Scroll to top link, initialized in js/app.js - scrollToTop() -->
        <a href="#" id="to-top"><i class="fa fa-angle-double-up"></i></a>

        @include('layouts.inc_dock_bottom')


        <!-- jQuery, Bootstrap.js, jQuery plugins and Custom JS code -->
        <script src="/js/vendor/jquery-1.12.0.min.js"></script>
        <script src="/js/vendor/bootstrap.min.js"></script>
        <script src="/js/vendor/defiant.min.js"></script>
        <script src="/js/plugins-backend.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/dataTables.buttons.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.flash.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/pdfmake.min.js"></script>
        <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.32/vfs_fonts.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.html5.min.js"></script>
        <script src="https://cdn.datatables.net/buttons/1.5.1/js/buttons.print.min.js"></script>

        <script src="/js/app-backend.js"></script>

        <!-- Add GPKI script here -->
        @if(request()->is('gpki/verify') || request()->is('gpki/sign'))
            <script src="/js/gpki/gpki-api-v2.js"></script>
        @endif
        @if(request()->is('gpki/verify'))
            <script src="/js/gpki/gpki-verify-v2.js"></script>
        @endif
        @if(request()->is('gpki/sign'))
            <script src="/js/gpki/gpki-sign-v2.js"></script>
        @endif

        <!-- Load and execute javascript code used only in this page -->
        @yield('jsprivate')

        <style>
            .app-environment {
                background-color: #57b846;
                color: white;
                text-transform: uppercase;
                font-size: 11pt;
                padding: 10px 20px;
                border-radius: 4px;
                border-color: #46b8da;
                -webkit-box-shadow: 0px 7px 20px -7px rgba(0, 0, 0, 0.54);
                -moz-box-shadow: 0px 7px 20px -7px rgba(0, 0, 0, 0.54);
                box-shadow: 0px 7px 20px -7px rgba(0, 0, 0, 0.54);
                transform: rotate(-90deg);
            }

            #app-info {
                position: fixed;
                top: 6px;
                right: 10px;
            }
        </style>

        @if(request()->is('gpki/verify') || request()->is('gpki/sign'))
            <link rel="stylesheet" href="{{ asset('css/GPKIRoaming.css') }}">
        @endif


</body>

</html>