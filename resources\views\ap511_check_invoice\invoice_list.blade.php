@extends('layouts.guest-dash')

@section('cssprivate')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/ap511-check-invoice/dashboard') }}"><i class="fa fa-dashboard"></i> Dashboard</a>
            </li>
            <li class="active">
                <a href="{{ url('/ap511-check-invoice/list') }}"><i class="fa fa-list"></i> Invoice List</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
@if (Auth::user())
    <div class="row">
        <div class="col-lg-12 text-right">
            <h5><strong>Requested on:</strong> {{ Carbon\Carbon::now()->format('d-M-Y H:i:s') }}</h5>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="block">
        <div class="block-header bg-primary-dark">
            <h3 class="block-title">
                <i class="si si-equalizer"></i> Filter Options
            </h3>
        </div>
        <div class="block-content">
            <div class="row items-push">
                <div class="col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label for="filterYear">Year:</label>
                        <select id="filterYear" name="year" class="form-control">
                            <option value="">All Years</option>
                            @foreach($availableYears as $availableYear)
                                <option value="{{ $availableYear }}" {{ $availableYear == $year ? 'selected' : '' }}>
                                    {{ $availableYear }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                </div>
                <div class="col-sm-6 col-lg-3">
                    <div class="form-group">
                        <label for="filterMonth">Month:</label>
                        <select id="filterMonth" name="month" class="form-control">
                            <option value="">All Months</option>
                            @for($i = 1; $i <= 12; $i++)
                                <option value="{{ $i }}" {{ $i == $month ? 'selected' : '' }}>
                                    {{ Carbon\Carbon::create()->month($i)->format('F') }}
                                </option>
                            @endfor
                        </select>
                    </div>
                </div>
                <div class="col-sm-12 col-lg-3">
                    <div class="form-group">
                        <label>&nbsp;</label>
                        <button type="button" id="applyFilter" class="btn btn-success btn-block">
                            <i class="fa fa-search"></i> Apply Filter
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Invoice List Table -->
    <div class="block">
        <div class="block-header bg-gray-darker">
            <h3 class="block-title">
                <i class="si si-list"></i> Invoice List
            </h3>
            <div class="block-options">
                <div class="btn-group">
                    <button type="button" class="btn btn-sm btn-secondary" data-toggle="tooltip" title="Refresh Data" onclick="$('#invoiceTable').DataTable().ajax.reload();">
                        <i class="si si-refresh"></i>
                    </button>
                </div>
            </div>
        </div>
        <div class="block-content block-content-full">
            <div class="table-responsive">
                <table id="invoiceTable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">POCO No</th>
                            <th class="text-center">PA No</th>
                            <th class="text-center">PA Exist</th>
                            <th class="text-center">Invoice No</th>
                            <th class="text-center">FL Order ID</th>
                            <th class="text-center">FL Req ID</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">AP511</th>
                            <th class="text-center">File Name</th>
                            <th class="text-center">Payment Ref</th>
                            <th class="text-center">Invoice Date</th>
                            <th class="text-center">Payment Date</th>
                            <th class="text-center">Payment Advice</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- Data will be populated via AJAX -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

@endif
@endsection

@section('jsprivate')
<script>
$(document).ready(function() {
    // Initialize DataTables
    App.datatables();

    console.log('Invoice List JavaScript loaded');
    console.log('jQuery version:', $.fn.jquery);

    // Check if elements exist
    console.log('Apply filter button exists:', $('#applyFilter').length);
    console.log('Filter year exists:', $('#filterYear').length);
    console.log('Filter month exists:', $('#filterMonth').length);
    console.log('Invoice table exists:', $('#invoiceTable').length);

    var table;
    
    // Initialize DataTable
    function initDataTable() {
        if (table) {
            table.destroy();
        }
        
        table = $('#invoiceTable').DataTable({
            processing: true,
            serverSide: true,
            ajax: {
                url: '{{ url("/ap511-check-invoice/data") }}',
                type: 'GET',
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                data: function(d) {
                    d.year = $('#filterYear').val();
                    d.month = $('#filterMonth').val();
                    console.log('DataTable AJAX request:', {
                        year: d.year,
                        month: d.month,
                        start: d.start,
                        length: d.length,
                        search: d.search
                    });
                },
                error: function(xhr, error, thrown) {
                    console.error('DataTable AJAX Error:', error);
                    console.error('Response:', xhr.responseText);
                },
                dataSrc: function(json) {
                    console.log('DataTable response:', json);
                    if (json.debug) {
                        console.log('Server debug info:', json.debug);
                    }
                    return json.data;
                }
            },
            columns: [
                {data: 'id', name: 'id', className: 'text-center'},
                {data: 'poco_no', name: 'poco_no', className: 'text-center'},
                {data: 'pa_no', name: 'pa_no', className: 'text-center'},
                {data: 'is_pa_no_exist_text', name: 'is_pa_no_exist', className: 'text-center', orderable: false},
                {data: 'inv_no', name: 'inv_no', className: 'text-center'},
                {data: 'fl_order_id', name: 'fl_order_id', className: 'text-center'},
                {data: 'fl_req_id', name: 'fl_req_id', className: 'text-center'},
                {data: 'status_name', name: 'status_name', className: 'text-center'},
                {data: 'is_ap511_text', name: 'is_ap511', className: 'text-center', orderable: false},
                {data: 'file_name', name: 'file_name', className: 'text-center'},
                {data: 'payment_reference_no', name: 'payment_reference_no', className: 'text-center'},
                {data: 'inv_date_created', name: 'inv_date_created', className: 'text-center'},
                {data: 'payment_date', name: 'payment_date', className: 'text-center'},
                {data: 'payment_advice_no', name: 'payment_advice_no', className: 'text-center'}
            ],
            order: [[0, 'desc']],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']],
            dom: '<"row"<"col-sm-6"l><"col-sm-6"f>>rt<"row"<"col-sm-6"i><"col-sm-6"p>>',
            language: {
                processing: '<i class="fa fa-spinner fa-spin"></i> Loading...',
                search: "Search:",
                lengthMenu: "Show _MENU_ entries",
                info: "Showing _START_ to _END_ of _TOTAL_ entries",
                infoEmpty: "Showing 0 to 0 of 0 entries",
                infoFiltered: "(filtered from _MAX_ total entries)",
                paginate: {
                    first: "First",
                    last: "Last",
                    next: "Next",
                    previous: "Previous"
                }
            },
            responsive: true,
            scrollX: true,
            autoWidth: false
        });
    }
    
    // Initialize table on page load
    initDataTable();
    
    // Apply filter button click event
    $('#applyFilter').click(function(e) {
        e.preventDefault();
        console.log('Apply filter button clicked in invoice list');
        if (table) {
            table.ajax.reload();
        } else {
            console.log('Table not initialized yet');
        }
    });
    
    // Auto-apply filter on dropdown change
    $('#filterYear, #filterMonth').change(function() {
        table.ajax.reload();
    });
});
</script>

<style>
/* Custom styles for better table appearance */
#invoiceTable_wrapper .dataTables_filter {
    text-align: right;
}

#invoiceTable_wrapper .dataTables_length {
    text-align: left;
}

#invoiceTable th {
    white-space: nowrap;
    font-weight: bold;
}

#invoiceTable td {
    font-size: 12px;
    white-space: nowrap;
}

.badge {
    font-size: 10px;
    padding: 3px 6px;
}

.badge-success {
    background-color: #28a745;
}

.badge-danger {
    background-color: #dc3545;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    #invoiceTable td {
        font-size: 11px;
    }

    .table-responsive {
        border: none;
    }
}
</style>
@endsection