CREATE TABLE ep_qt_accept_history (
    id                       INT AUTO_INCREMENT PRIMARY KEY,
    log_timestamp            TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    -- Task History Details
    createddate              <PERSON><PERSON><PERSON>IME NULL,
    updateddate              <PERSON><PERSON><PERSON><PERSON><PERSON> NULL,
    assigneddate             DATE NULL,
    assignees                TEXT,
    acquiredby               VARCHAR(400),
    approvers                TEXT,
    outcome                  VARCHAR(100),
    state                    VARCHAR(100),
    substate                 VARCHAR(200),
    taskid                   VARCHAR(64),
    tasknumber               INT,
    updatedby                VARCHAR(400),
    version                  INT,
    versionreason            TEXT,
    expirationdate           DATE NULL,
    
    -- Parent Process Information
    compositename            VARCHAR(200),
    compositeversion         VARCHAR(200),
    taskdefinitionname       VA<PERSON><PERSON><PERSON>(200),
    customattributenumber2   INT,
    componentname            VA<PERSON>HAR(500),
    customattributestring1   TEXT,
    customattributestring2   TEXT,
    flow_id                  BIGINT,
    compositeinstanceid      VARCHAR(200),
    compositedn              VARCHAR(500),
    processname              VARCHAR(400),
    enddate                  DATE NULL,
    ecid                     VARCHAR(200)
);