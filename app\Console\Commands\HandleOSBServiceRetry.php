<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;

class HandleOSBServiceRetry extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'osb-service-retry-fix';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To manage records in Batch Service Retry. To delete records in retry if already succeed transferred. ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $listFilesAlreadyProcess = DB::connection('oracle_nextgen_rpt')->select(
              " SELECT distinct remarks_1
                  FROM osb_logging
                  WHERE  trans_type = 'Status-BATCH'
                        -- AND service_code = 'GFM-010'
                        AND status = 'S'
                        AND remarks_1 IN (
                          SELECT file_name
                          FROM OSB_BATCH_RETRY_DTL
                        )
                  AND EXISTS (select * from di_interface_log where file_name  =  remarks_1 )
                ");
            
            MigrateUtils::logDump('Total Files already process : '.count($listFilesAlreadyProcess));
            
            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listFilesAlreadyProcess) {   
                $countBatchRetry = DB::connection('oracle_nextgen_fullgrant')->table('OSB_BATCH_RETRY_DTL')
                        ->whereIn('file_name',collect($listFilesAlreadyProcess)->pluck('remarks_1')->toArray())
                        ->count();
                MigrateUtils::logDump('Total in  OSB_BATCH_RETRY_DTL to be delete: '.$countBatchRetry);
                DB::connection('oracle_nextgen_fullgrant')->table('OSB_BATCH_RETRY_DTL')
                        ->whereIn('file_name',collect($listFilesAlreadyProcess)->pluck('remarks_1')->toArray())
                        ->delete();
                MigrateUtils::logDump('Done delete in OSB_BATCH_RETRY_DTL');
            });  
            
            MigrateUtils::logDump('Completed');
        
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . __METHOD__ . ' '.json_encode( ['Email' => $data["to"], 'ERROR' => $e->getMessage()]));
        }
    }
    
}
