<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\BpmApiService;
use SSH;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Log;
use DB;
use App\EpSupportActionLog;
use Auth;
use Carbon\Carbon;
use Validator;
use DOMDocument;
use DOMXPath;

class FulfilmentController extends Controller
{

    use SupplierService;

    use OSBService;

    use ProfileService;

    use FulfilmentService;

    use BpmApiService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findListWorkFlowStatusDOFN($docNo)
    {

        $list = $this->getListWorkFlowStatusDOFN($docNo);

        return view('include.list_trans_dofn', [
            'listDataDoFn' => $list
        ]);
    }

    public function findListYepMenuTasklistPoco($docNo)
    {

        $list = $this->getListYepMenuTaskList($docNo);

        return view('include.list_trans_yepmenu', [
            'listDataYepMenu' => $list
        ]);
    }


    public function findListWorkFlowStatusDocument($docNo)
    {
        $list = $this->searchTransactionDocNo($docNo);

        return view('include.list_trans_workflow', [
            'listDataWorkflow' => $list["listdata"]
        ]);
    }

    public function transactionDocNo()
    {
        return view('list_doc_transaction', [
            'listdata' => null,
            'listXml' => null,
            'carian' => ''
        ]);
    }

    public function findTransactionDocNo($search)
    {
        if ($search == 'insert_docno') {
            return view('list_doc_transaction', [
                'listdata' => array(),
                'carian' => $search
            ]);
        }
        return view('list_doc_transaction', $this->searchTransactionDocNo($search));
    }

    public function searchTransactionDocNo($search)
    {


        $typeDoc = substr($search, 0, 2);
        $list = array();
        if ($typeDoc == 'PD') {
            $list = $this->getListPidByPdNo($search);
        }
        if ($typeDoc == 'PI') {
            $list = $this->getListPurchaseInquiryByPiNo($search);
        }
        if ($typeDoc == 'SQ') {
            $list = $this->getListSimpleQuoteBySqNo($search);
        }
        if ($typeDoc == 'RN') {
            $list = $this->getListRequestNoteByRnNo($search);
        }
        if ($typeDoc == 'PR' || $typeDoc == 'CR') {
            $list = $this->getListFulfilmenRequestByPrCr($search);
        }
        if ($typeDoc == 'PO' || $typeDoc == 'CO' || $typeDoc == 'FC' || $typeDoc == 'L0') {
            $list = $this->getListFulfilmenOrderByPoCoFc($search);
        }

        if ($typeDoc == 'JR' || $typeDoc == 'JN' || $typeDoc == 'KR' || $typeDoc == 'KN' || $typeDoc == 'KU' || $typeDoc == 'KA' || $typeDoc == 'KB' || $typeDoc == 'KC') {
            $list = $this->getListSMByApplNo($search);
        }
        if ($typeDoc == 'DO' || $typeDoc == '60') {
            $list = $this->getListDeliveryOrderByDoNo($search);
        }
        if ($typeDoc == 'FN') {
            $list = $this->getListFulfilmentNoteByFnNo($search);
        }
        if ($typeDoc == 'QT') {
            $list = $this->getListQuotationTenderByQtNo($search);
        }
        if ($typeDoc == 'LA') {
            $list = $this->getListLetterAcceptanceByLoaNo($search);
        }
        if ($typeDoc == 'SD') {
            $list = $this->getListStopInstructionBySdNo($search);
        }
        if ($typeDoc == 'AP') {
            $list = $this->getListApplicationNoByApNo($search);
        }
        if ($typeDoc == '60') {
            $listInv = $this->getListInvoiceByInvNo($search);
            foreach ($listInv as $data) {
                array_push($list, $data);
            }
        }
        if ($typeDoc == 'C0' || $typeDoc == 'B0' || $typeDoc == 'CN' || $typeDoc == 'DN') {
            $list = $this->getListAdjustmentByDocNo($search);
        }
        if ($typeDoc == 'PA') {
            $list = $this->getListPaymentAdviseByPaNo($search);
        }

        $supplier = null;
        if ($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO') {
            $supplier = $this->getSupplierInfoByDocNo($search);
        }

        return [
            'listdata' => $list,
            'supplier' => $supplier,
            'carian' => $search
        ];
    }

    public function getListDocNoTrackingDefault()
    {
        return view('list_doc_tracking', [
            'listdata' => array(),
            'carian' => null
        ]);
    }

    public function getListDocNoTracking($docNo)
    {

        //Search Default SQ to PO
        $list = $this->getListDocNoFulfillmentSQPOTracking($docNo);
        $searchType = 1;

        //Search carian contractNo only.
        if (count($list) == 0) {
            $typeContractNo = substr($docNo, 0, 1);
            $typeContractNo2 = substr($docNo, 0, 2);
            if ($typeContractNo2 == 'CT' || $typeContractNo == 'Z' || $typeContractNo == 'M') {
                $list = collect(array(['contract_doc_no' => $docNo]));
            }
        }

        //Search Default SQ to CO
        if (count($list) == 0) {
            $list = $this->getListDocNoFulfillmentSQCOTracking($docNo);
            $searchType = 2;
        }

        //Searching for QT to CO only
        if (count($list) == 0) {
            $list = $this->getListDocNoFulfillmentQTCOTracking($docNo);
            $searchType = 3;
        }

        //Searching for PRCR POCO only
        if (count($list) == 0) {
            $list = $this->getListDocNoFulfillmentPRCRPOCOTracking($docNo);
            $searchType = 4;
        }

        //Searching for LOA CT POCO only
        if (count($list) == 0) {
            $list = $this->getListDocNoFulfillmentLOACTCRCOTracking($docNo);
            $searchType = 5;
        }

        //Searching for AP only
        if (count($list) == 0) {
            $list = $this->getListDocNoMarketResearchTracking($docNo);
            $searchType = 6;
        }

        //Filter > Do not proceed in tracking diary if result more than 10. If will suffer the processing query data.
        if (count($list) > 20) {
            return 'This Document No. skip to process. Try search other document no related.';
        }

        $listDocNo = array();
        $listGroupIdTracking = array();
        if (count($list) > 0) {

            if (isset($list[0]->ap_doc_no) && strlen($list[0]->ap_doc_no) > 0) {
                array_push($listDocNo, $list[0]->ap_doc_no);
            }

            if (isset($list[0]->fo_doc_no) && strlen($list[0]->fo_doc_no) > 0) {
                array_push($listDocNo, $list[0]->fo_doc_no);
            }

            if (isset($list[0]->quote_no) && strlen($list[0]->quote_no) > 0) {
                array_push($listDocNo, $list[0]->quote_no);
            }

            if (isset($list[0]->request_note_no) && strlen($list[0]->request_note_no) > 0) {
                array_push($listDocNo, $list[0]->request_note_no);
            }

            if (isset($list[0]->loa_doc_no) && strlen($list[0]->loa_doc_no) > 0) {
                array_push($listDocNo, $list[0]->loa_doc_no);
            }

            if (isset($list[0]->ct_doc_no) && strlen($list[0]->ct_doc_no) > 0) {
                array_push($listDocNo, $list[0]->ct_doc_no);
            }

            if (isset($list[0]->qt_doc_no) && strlen($list[0]->qt_doc_no) > 0) {
                array_push($listDocNo, $list[0]->qt_doc_no);
            }

            foreach ($list as $obj) {
                if (isset($obj->fr_doc_no) && $obj->fr_doc_no && strlen($obj->fr_doc_no) > 0) {
                    array_push($listDocNo, $obj->fr_doc_no);
                    $objResult = $this->getGroupIdTrackingDiary($obj->fr_doc_no);
                    if ($objResult != null && $objResult->group_id != null) {
                        array_push($listGroupIdTracking, $objResult->group_id);
                    }
                }
            }
        }

        /**
         * If not found step 1 search, So just search in Tracking Diary
         */
        if (count($listDocNo) == 0 && count($listGroupIdTracking) == 0) {
            array_push($listDocNo, $docNo);
            $objResult = $this->getGroupIdTrackingDiary($docNo);
            if ($objResult != null && $objResult->group_id != null) {
                array_push($listGroupIdTracking, $objResult->group_id);
            }
        }

        $collect = collect([]);

        $collect->put('list_doc_no', array_unique($listDocNo));
        $collect->put('list_group_id', array_unique($listGroupIdTracking));

        $listResult = $this->getListTrackingDiary($collect);


        $supplier = null;
        $smAppl = null;
        $preparedPtj = null;
        $issuedPtj = null;
        $createdPtj = null;
        $chargePtj = null;
        $typeDoc = substr($docNo, 0, 2);
        $sqInfo = null;
        if ($typeDoc == 'SQ') {
            $sqInfo = DB::connection('oracle_nextgen_rpt')->table('sc_quote')->where('quote_no', $docNo)->first();
        }
        if ($typeDoc == 'PR' || $typeDoc == 'CR' || $typeDoc == 'PO' || $typeDoc == 'CO') {
            $supplier = $this->getSupplierInfoByDocNo($docNo);

            $preparedPtj = $this->getPtjInfoByDocNo($docNo, 'PREPARED_PTJ');
            $issuedPtj = $this->getPtjInfoByDocNo($docNo, 'ISSUED_PTJ');
            $createdPtj = $this->getPtjInfoByDocNo($docNo, 'CREATED_PTJ');
            $chargePtj = $this->getPtjInfoByDocNo($docNo, 'CHARGE_PTJ');
        }

        if (Auth::user()->isCodiUsersEp()) {
            if (!Auth::user()->checkIfUserHasRole('GROUP GOVERNMENT MANAGEMENT(GM)')) {
                $smDocType = array('JR', 'JN', 'KR', 'KN', 'KU', 'KA', 'KB', 'KC');
                if (!in_array($typeDoc, $smDocType)) {
                    return 'You are not authorized to search this document.';
                }
            }
        }
        if (count($listResult) > 0) {

            $poco_no = null;
            foreach ($listResult as $rowData) {
                $typeDocTemp = substr($rowData->doc_no, 0, 2);
                if ($typeDocTemp == 'PO' || $typeDocTemp == 'CO') {
                    $poco_no = $rowData->doc_no;
                    break;
                }
            }

            $firstRow = $listResult->first();
            if ($firstRow->doc_type == 'SR') {
                //to get info SM_APPL
                $smAppl = DB::connection('oracle_nextgen_rpt')->table('SM_APPL ap')
                    ->join('PM_STATUS_DESC sd', 'ap.STATUS_ID', '=', 'sd.STATUS_ID')
                    ->where('sd.LANGUAGE_CODE', 'en')
                    ->where('appl_no', $firstRow->doc_no)
                    ->select('ap.*', 'sd.STATUS_NAME')
                    ->first();
                $supplier = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A')
                    ->join('SM_APPL as B', 'A.SUPPLIER_ID', '=', 'B.SUPPLIER_ID')
                    ->leftJoin('SM_MOF_ACCOUNT as D', 'D.SUPPLIER_ID', '=', 'A.SUPPLIER_ID')
                    ->where('B.APPL_NO', $firstRow->doc_no)
                    ->select('A.*')
                    ->addSelect('D.MOF_NO')
                    ->addSelect(DB::raw(" null  as sap_order_no"))
                    ->addSelect(DB::raw(" null  as fo_doc_no"))
                    ->first();
            }

            return view('list_doc_tracking', [
                'listdata' => $listResult,
                'supplier' => $supplier,
                'smAppl' => $smAppl,
                'preparedPtj' => $preparedPtj,
                'issuedPtj' => $issuedPtj,
                'createdPtj' => $createdPtj,
                'chargePtj' => $chargePtj,
                'poco_no' => $poco_no,
                'sqInfo' => $sqInfo,
                'carian' => $docNo
            ]);
        } else {
            return view('list_doc_tracking', [
                'listdata' => array(),
                'supplier' => $supplier,
                'smAppl' => null,
                'preparedPtj' => $preparedPtj,
                'issuedPtj' => $issuedPtj,
                'createdPtj' => $createdPtj,
                'chargePtj' => $chargePtj,
                'poco_no' => null,
                'sqInfo' => null,
                'carian' => $docNo
            ]);
        }
    }

    public function getListDocNoTrackingByUser()
    {
        $list = array();
        $userRoles = array();
        $user = array();
        if (request()->isMethod('post')) {
            $messages = [
                'login_id.required' => 'Sila masukkan Login ID pengguna',
                'date_from.required' => 'Sila pilih tarikh mula',
                'date_to.required' => 'Sila pilih tarikh akhir',
            ];
            Validator::make(request()->all(), [
                'login_id' => 'required',
                'date_from' => 'required',
                'date_to' => 'required'
            ], $messages)->validate();

            $list = $this->getListTrackingDiaryByUser(request()->login_id, request()->date_from, request()->date_to);
            $userRoles = $this->getListUserRoles(request()->login_id);
            $user = $this->getUser(request()->login_id);
            //dd($list);
        }

        session()->flashInput(request()->input());
        return view('pm.list_doc_tracking_by_user', [
            'listdata' => $list,
            'listRoles' => $userRoles,
            'user' => $user,
            'carian' => null
        ]);
    }

    public function getTrackingPayment()
    {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $docGroupId = '';
        $tracking = '';
        $workflow = '';
        $osblog = '';
        $trackingtrue = false;
        $flowtrue = false;
        $logtrue = false;
        $pendingPayment = '';
        $paymentcompleted = '';
        $osbtransid = '';
        if ($carianTemp != null && strlen($carianTemp) == 17) {

            // get group id for POCO
            $docGroupId = $this->getGroupIdTrackingDiary($carianTemp);

            if ($docGroupId != null && $docGroupId->group_id != null) {
                // result for tracking diary
                $tracking = $this->getTrackingDiaryByPOCONo($docGroupId->group_id);
            }

            // checking latest tracking
            // 41030 : Pending Payment
            // 41035 : Closed
            $latesttracking = $this->getLatestTrackingDiaryDetail($carianTemp);
            if ($latesttracking && ($latesttracking->status_id == 41030 || $latesttracking->status_id == 41035)) {
                $trackingtrue = true;
            } else {
                $trackingtrue = false;
            }

            // result for workflow/bpm
            $workflow = $this->getListFulfilmenOrderByPoCoFc($carianTemp);

            // checking latest workflow
            $currentflow = $this->getCurrentWorkflowByPoCo($carianTemp);
            if ($currentflow->status_id == 41030 && $currentflow->status_name == 'Pending Payment') {
                $flowtrue = true;
            } else {
                $flowtrue = false;
            }

            // result for osb log
            $osbtransid = $this->getTransIdOSBLoggingByDocNo($carianTemp);

            // checking current osb 
            if ($osbtransid !== '') {
                $osblog = $this->searchOSBLogDetailsByTransId($osbtransid->trans_id);

                // checking latest osb
                $ibres = $this->getIbResPayment($osbtransid->trans_id);
                if ($ibres && $ibres != '') {
                    $logtrue = true;
                } else {
                    $logtrue = false;
                }
            }

            // details for POCO pending payment
            $pendingPayment = $this->getDocDetails($carianTemp);

            // details for PA completed
            $paymentcompleted = $this->getPADetails($carianTemp);

            return view('list_doc_update_tracking', [
                'trackingdiary' => $tracking,
                'workflow' => $workflow,
                'osblog' => $osblog,
                'listdata' => $pendingPayment,
                'payment' => $paymentcompleted,
                'trackingtrue' => $trackingtrue,
                'flowtrue' => $flowtrue,
                'logtrue' => $logtrue,
                'carian' => $carianTemp
            ]);
        } else {
            return view('list_doc_update_tracking', [
                'trackingdiary' => '',
                'workflow' => '',
                'osblog' => '',
                'listdata' => '',
                'payment' => '',
                'trackingtrue' => '',
                'flowtrue' => '',
                'logtrue' => '',
                'carian' => $carianTemp
            ]);
        }
    }

    public function updateTracking(Request $request)
    {
        $doctype = $request->doc_type;
        $docid = $request->doc_id;
        $docno = $request->doc_no;
        $actionby = $request->action_by;
        $statusid = $request->status_id;
        $groupdoctype = $request->groupdoc_type;
        $groupdocid = $request->groupdoc_id;
        $tdiarylist1 = $request->tdiary_list1;
        $tdiarylist2 = $request->tdiary_list2;

        $doctypepa = $request->doc_type_pa;
        $docidpa = $request->doc_id_pa;
        $docnopa = $request->doc_no_pa;
        $statusidpa = $request->status_id_pa;
        $groupdoctypepa = $request->groupdoc_type_pa;
        $groupdocidpa = $request->groupdoc_id_pa;
        $tdiarylist1pa = $request->tdiary_datalist1_pa;

        $xmlPOCompleted = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' 
        xmlns:trac='http://xmlns.oracle.com/bpmn/bpmnProcess/TrackingDairyHandler' 
        xmlns:fl='http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Tracking_Dairy_List_Data' 
        xmlns:fl1='http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Tracking_Dairy_Data'>
        <soapenv:Header/>
        <soapenv:Body>
           <trac:start>
              <fl:FL_Tracking_Dairy_List_Data>
                 <!--1 or more repetitions:-->
                 <fl:trackingDairyInfo>
                 <fl1:trackingDairyId>62501</fl1:trackingDairyId>
                                     <fl1:docType>$doctype</fl1:docType>
                                     <fl1:docId>$docid</fl1:docId>
                                     <fl1:docNo>$docno</fl1:docNo>
                                     <fl1:actionBy>$actionby</fl1:actionBy>
                                     <fl1:roleCode/>
                                     <fl1:statusId>$statusid</fl1:statusId>
                                     <fl1:groupDocType>$groupdoctype</fl1:groupDocType>
                                     <fl1:groupDocId>$groupdocid</fl1:groupDocId>
                                     <fl1:tDairyData/>
                                     <fl1:tDairyDataList>$tdiarylist1</fl1:tDairyDataList> 
                                     <fl1:tDairyDataList>$tdiarylist2</fl1:tDairyDataList>               
                 </fl:trackingDairyInfo>
              </fl:FL_Tracking_Dairy_List_Data>
           </trac:start>
        </soapenv:Body>
     </soapenv:Envelope>
                 ";

        $xmlPACompleted = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' 
            xmlns:trac='http://xmlns.oracle.com/bpmn/bpmnProcess/TrackingDairyHandler' 
            xmlns:fl='http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Tracking_Dairy_List_Data' 
            xmlns:fl1='http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Tracking_Dairy_Data'>
            <soapenv:Header/>
            <soapenv:Body>
               <trac:start>
                  <fl:FL_Tracking_Dairy_List_Data>
                     <!--1 or more repetitions:-->
                     <fl:trackingDairyInfo>
                     <fl1:trackingDairyId>68501</fl1:trackingDairyId>
                                         <fl1:docType>$doctypepa</fl1:docType>
                                         <fl1:docId>$docidpa</fl1:docId>
                                         <fl1:docNo>$docnopa</fl1:docNo>
                                         <fl1:actionBy>0</fl1:actionBy>
                                         <fl1:roleCode/>
                                         <fl1:statusId>$statusidpa</fl1:statusId>
                                         <fl1:groupDocType>$groupdoctypepa</fl1:groupDocType>
                                         <fl1:groupDocId>$groupdocidpa</fl1:groupDocId>
                                         <fl1:tDairyData/>
                                         <fl1:tDairyDataList>$tdiarylist1pa</fl1:tDairyDataList>              
                     </fl:trackingDairyInfo>
                  </fl:FL_Tracking_Dairy_List_Data>
               </trac:start>
            </soapenv:Body>
         </soapenv:Envelope>";

        $client = new Client([
            'base_uri' => 'http://192.168.62.16:8001',
        ]);
        $payloadPOCompleted = $xmlPOCompleted;
        $responsePOCompleted = $client->post('/soa-infra/services/default/Order/TrackingDairyHandler.service', [
            //'debug' => TRUE,
            'body' => $payloadPOCompleted,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'updatePOCompleted',
            ]
        ]);

        $bodyPOCompleted = $responsePOCompleted->getBody();

        $payloadPACompleted = $xmlPACompleted;
        $responsePACompleted = $client->post('/soa-infra/services/default/Order/TrackingDairyHandler.service', [
            //'debug' => TRUE,
            'body' => $payloadPACompleted,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'updatePACompleted',
            ]
        ]);

        // save action in log table
        EpSupportActionLog::saveActionLog('Update Tracking Diary', 'Soap UI', $xmlPOCompleted . $xmlPACompleted, $docno, ('Completed'));

        $bodyPACompleted = $responsePACompleted->getBody();
        dump($bodyPOCompleted);
        dump($bodyPACompleted);
        return array('docno' => $docno, 'xmlPOCompleted' => $xmlPOCompleted, 'xmlPACompleted' => $xmlPACompleted);
    }

    protected function approverList($grp, $docNo)
    {
        $RNdetails = $this->getDetailRNapprover($grp, $docNo, $grp, $docNo);
        $html = "<table  id='rnapp_try' class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Approver Login</th>
                            <th class='text-center'>Approver Name</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($RNdetails as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->login_id</strong></td> 
                        <td class='text-center'><strong>$value->user_name</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }

    protected function flApproverList($grp, $docNo)
    {
        $FLdetails = $this->getDetailFLapprover($grp, $docNo, $grp, $docNo);
        $html = "<table  id='fl_approver_table' class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Approver Login</th>
                            <th class='text-center'>Approver Name</th>
                            <th class='text-center'>Approver Limit</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($FLdetails as $value) {
            $formattedLimit = 'RM' . number_format($value->approval_limit, 0, '', ',');
            $data = "
                <tr>
                    <td class='text-center'><strong>$value->login_id</strong></td> 
                    <td class='text-center'><strong>$value->user_name</strong></td>
                    <td class='text-center'><strong>$formattedLimit</strong></td>
                </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }

    protected function clickList($itemid, $RN_No)
    {
        $supplierreply = $this->getSupplieriklanDP($itemid, $RN_No);
        $html = "<table id='statusPOCO1-datatable' class='table table-bordered table-vcenter'><thead>
                        <tr>
                        <th class='text-center'>Supplier Ranking</th>
                        <th class='text-center'>Supplier Name</th>
                        <th class='text-center'>Mof No</th>
                        <th class='text-center'>Request Supplier Item</th>
                        <th class='text-center'>Request Item Id</th>";
        // Conditionally add Unit Price column header if the user is a specialist
        if (Auth::user()->isApproverTesting()) {
            $html .= "<th class='text-center'>Unit Price</th>";
        }
        $html .= "</tr></thead><tbody>";

        $counter = 0;
        foreach ($supplierreply as $value) {
            $data = "
                <tr>
                    <td class='text-center'><strong>$value->ranking</strong></td> 
                    <td class='text-center'><strong>$value->supplier_name</strong></td> 
                    <td class='text-center'><strong>$value->mof_no</strong></td>
                    <td class='text-center'><strong>$value->request_supplier_item_id</strong></td>
                    <td class='text-center'><strong>$value->tid</strong></td>";
            // Conditionally add Unit Price column data if the user is a specialist
            if (Auth::user()->isApproverTesting()) {
                $data .= "<td class='text-center'><strong>$value->unit_price</strong></td>";
            }
            $data .= "</tr>";
            $html .= $data;
        }
        $html .= "</tbody></table>";
        return $html;
    }

    public function getDPSummary()
    {
        $docNo = request()->doc_no;
        $RN_No = $getRNNo = $iklandetail = $detailproblem =  $listRnSyor = $differentRnSyorNumbers = $approverRN = $findFLAddrErrorPrices = $listSQClone = null;
        $asigneeNameApproverRN = $listDataResultPO = $getrealnameacc = $detailitemproblemIn = $tracking_diary = $listIsItemClone = null;
        $asigneeNameListSupplier = $listdataacquiredPO = $extensioncode = $listZonalLocation = $getItemWhenSqSubmitted = $itemSupplierRespond = null;
        $detailsupplieritem = $rnSyorNumbers = $loaRequestNo = $listDetailSupplier = $listRN = $app = $req = $loaDetails = $loaitemdetail = $ptjdetails = $itemFromCatalogue = null;

        $startTime = microtime(true);
        $endTime = microtime(true);
        $elapsedTime = $endTime - $startTime;

        if (strpos($docNo, 'SQ') !== false) {
            $getRNNo = $this->getDPFromSQ($docNo);
            if ($getRNNo)
                $RN_No = $getRNNo[0]->request_note_no;
        } else if (strpos($docNo, 'LA') !== false) {
            $getRNNo = $this->getDPFromLOA($docNo);
            if ($getRNNo)
                $RN_No = $getRNNo[0]->request_note_no;
        } else if (strpos($docNo, 'PR') !== false) {
            $getRNNo = $this->getDPFromPR($docNo);
            if ($getRNNo)
                $RN_No = $getRNNo[0]->request_note_no;
        } else if (strpos($docNo, 'PO') !== false) {
            $getRNNo = $this->getDPFromPO($docNo);
            if ($getRNNo)
                $RN_No = $getRNNo[0]->request_note_no;
        } else {
            $RN_No = $docNo;
        }

        if ($RN_No && $RN_No != null) {
            $detailitemproblemIn = array();
            $listRN = $this->getRNno($RN_No);
            if ($listRN == null) {
                $listRN = $this->getRNno2($RN_No);
            }
            $scActor = $this->getActorList($RN_No, $RN_No);
            foreach ($scActor as $actor) {
                if ($actor->role_code == 'REQUISITIONER') {
                    $req = $actor->login_id . ' | ' . $actor->user_name;
                }
                if ($actor->role_code == 'RN_APPROVER') {
                    $app = $actor->login_id . ' | ' . $actor->user_name;
                }
            }
            $listDetailSupplier = $this->getDetailSupplierDP($RN_No);
            $detailsupplieritem = $this->getDetailSupplierItems($RN_No);

            $loaDetails = $this->getLoaDetails($RN_No);
            $listRnSyor = $this->checkRNSyor($RN_No);
            if (!empty($loaDetails) && $loaDetails != null) {
                $loaRequestNo = $loaDetails[0]->request_note_no;
            }

            $rnSyorNumbers = array_map(fn($item) => $item->request_note_no, $listRnSyor);
            $differentRnSyorNumbers = array_values(array_filter($rnSyorNumbers, fn($rn) => $rn !== $loaRequestNo));

            if (!empty($differentRnSyorNumbers)) {
                $differentRnSyorNumbers;
            }

            $loaitemdetail = $this->getdetailLOAitem($RN_No);
            $ptjdetails = $this->getDetailPTJ($RN_No, $RN_No);
            $extensioncode = $this->getDetailiextension($RN_No);
            $iklandetail = $this->getDetailiklanDP($RN_No);
            $findTaskuser = $this->getTaskUser($RN_No, $RN_No, $RN_No, $RN_No);
            $findTaskuserOrder = $this->findTaskuserOrder($RN_No);
            $tracking_diary = $this->dpTrackingDiary($RN_No, $RN_No, $RN_No);
            if ($tracking_diary == null) {
                $tracking_diary = $this->dpTrackingDiary2($RN_No);
            }

            if ($listRN != null && $listRN[0]->is_zonal == 1) {
                $listZonalLocation = $this->getLocationForZonal($listRN[0]->quote_id);
            }

            if ($listRN != null) {
                $itemSupplierRespond = $this->getSupplierRespond($listRN[0]->quote_id);
                $listItemWhenSqSubmitted = $this->listWhenSqSubmitted($listRN[0]->quote_id, $listRN[0]->quote_id, $listRN[0]->quote_id);
                if ($listItemWhenSqSubmitted != null) {
                    $getItemWhenSqSubmitted = $listItemWhenSqSubmitted;
                    $itemFromCatalogue = $this->itemFromCatalogue($listItemWhenSqSubmitted[0]->item_id);
                    $listSQClone = $this->getSQClone($listRN[0]->quote_id, $listRN[0]->quote_id);
                    foreach ($listItemWhenSqSubmitted as $listItem) {
                        $listIsItemClone = $this->getItemClone($listItem->spec_question_id, $listItem->spec_question_id);
                    }
                }
            }

            $detailitemproblem = $this->getDetailSupplierItemproblemOut($RN_No);
            foreach ($detailitemproblem as $try) {
                array_push($detailitemproblemIn, $this->getDetailSupplierItemproblem($try->reqdetailid, $try->supplier_id));
                if (is_array($detailitemproblemIn) > 0 || is_array($detailitemproblemIn) != null) {
                    if ($detailitemproblemIn != null) {
                        $detailproblem = $detailitemproblemIn;
                    } else {
                        $detailproblem == null;
                    }
                }
            }
            $findFLAddrErrorPrice = $this->listFLAddrErrorPrice($RN_No);
            if (is_array($findFLAddrErrorPrice) > 0) {
                if ($findFLAddrErrorPrice == true) {
                    $findFLAddrErrorPrices = $findFLAddrErrorPrice;
                }
            }

            // $suppliererror = $this->getDetailSuppliererror($RN_No);
            if ($elapsedTime <= 30) {
                if ($findTaskuser != null && (strpos($findTaskuser[0]->doc_no, 'SQ') !== false || strpos($findTaskuser[0]->doc_no, 'RN') !== false || strpos($findTaskuser[0]->doc_no, 'LA') !== false)) {
                    $listDataResultPO = $this->findAPITaskBPMListDocAndModule($findTaskuser[0]->doc_no, 'SourcingDP');
                }
                if ($findTaskuserOrder != null && strpos($findTaskuserOrder[0]->doc_no, 'RN') !== false) {
                    $docNumbers = array();

                    foreach ($findTaskuserOrder as $list) {
                        $docNumbers[] = $list->doc_no;
                    }
                    $listDataResultPOArray = array();
                    foreach ($docNumbers as $docNumber) {
                        $listDataResultPOArray = $this->findAPITaskBPMListDocAndModule($docNumber, 'Order');
                        $listDataResultPO[$docNumber] = $listDataResultPOArray;
                    }
                }
            } else {
                $listDataResultPO = null;
            }
            if (is_array($listDataResultPO) && $listDataResultPO != null) {
                $try = 0;

                $asigneeNameApproverRN = array();

                if (isset($listDataResultPO['status'])) { // 1-dimensional, turn into 2-dimensional
                    $listDataResultPO = array($listDataResultPO);
                }

                foreach ($listDataResultPO as $listDataResultPO1) {
                    if ($listDataResultPO1["status"] != null && $listDataResultPO1["status"] === 'Success') {
                        $listdataPO = $listDataResultPO1["result"];
                        foreach ($listdataPO as $key => $row) {

                            if ($row['state'] === 'ASSIGNED') {
                                $listCompositePO = explode("*", $row['compositeDN']);
                                $assignees = array();
                                $listdataacquiredPO[] = array(
                                    'key' => $key,
                                    'acquiredBy' => $row['acquiredBy'],
                                );
                                $listPONumber[] = array(
                                    'key' => $key,
                                    'docNumber' => $row['docNumber'],
                                );
                                foreach ($row['assignees'] as $assignee) {
                                    $pmUser = $this->getPmuser($assignee);
                                    if (!empty($pmUser) && isset($pmUser[0])) {
                                        $getnamePO = $pmUser[0];
                                        if (isset($getnamePO) && $getnamePO != '') {
                                            $assignees[] = array(
                                                'userName' => $getnamePO->user_name,
                                                'loginId' => $getnamePO->login_id,
                                                'companyName' => $getnamePO->company_name,
                                            );
                                        }
                                    }
                                }
                                $asigneeNameApproverRN[$row['docNumber']][] = array(
                                    'assigneeDetails' => $assignees,
                                    'taskName' => $row['taskName'],
                                );
                            }
                        }
                    } else {
                        $statusAPI = $listDataResultPO1["result"];
                    }
                }

                for ($i = 0; $i < count($listDataResultPO1); $i++) {
                    if ($listdataacquiredPO != null && is_array($listdataacquiredPO)) {
                        $getrealnameaccPO = array();
                        $approverRN = array();

                        if (isset($listdataacquiredPO[$try])) {
                            $getacquireqPO = (array) $listdataacquiredPO[$try];
                            $doc_number = (array) $listPONumber[$try];

                            $getnameaccPO = $this->getPmuser($getacquireqPO['acquiredBy']);

                            if (is_array($getnameaccPO) && count($getnameaccPO) > 0) {
                                // Make sure the object structure matches your expectations
                                $userObject = $getnameaccPO[0];
                                if (property_exists($userObject, 'user_name') && property_exists($userObject, 'login_id')) {
                                    array_push($getrealnameaccPO, array($userObject->user_name, $userObject->login_id));
                                } else {
                                    $getrealnameaccPO = 'ERROR HANDLER';
                                }
                            }

                            array_push($approverRN, array($doc_number, $getrealnameaccPO));
                        }
                    }
                }
            }
        }

        return view('list_dp_summary', [
            'docno' => $docNo,
            'listed' => $listDetailSupplier,
            'getItemWhenSqSubmitted' => $getItemWhenSqSubmitted,
            'itemFromCatalogue' => $itemFromCatalogue,
            'items' => $detailsupplieritem,
            'Rniklan' => $iklandetail,
            'extension' => $extensioncode,
            'listRNdetail' => $listRN,
            'req' => $req,
            'app' => $app,
            'listZonalLocation' => $listZonalLocation,
            'approverRN' => $approverRN,
            'asigneeNameListSupplier' => $asigneeNameListSupplier,
            'asigneeNameApproverRN' => $asigneeNameApproverRN,
            'getrealnameacc' => $getrealnameacc,
            'itemproblem' => $detailproblem,
            'findFLAddrErrorPrices' => $findFLAddrErrorPrices,
            'loaitems' => $loaDetails,
            'loaitemarrays' => $loaitemdetail,
            'ptj' => $ptjdetails,
            'tracking_diary' => $tracking_diary,
            'listSQClone' => $listSQClone,
            'listIsItemClone' => $listIsItemClone,
            'respond' => $itemSupplierRespond,
            'differentRnSyorNumbers' => $differentRnSyorNumbers,
            'carian' => request()->doc_no
        ]);
    }

    protected function approverListRO($docNo)
    {
        $AOdetails = $this->getDetailROapprover($docNo);
        $html = "<thead>
                            <tr>
                            <th class='text-center'>Approver Login</th>
                            <th class='text-center'>Approver Name</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($AOdetails as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->login_id</strong></td> 
                        <td class='text-center'><strong>$value->user_name</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody>";
        return $html;
    }

    protected function findSummaryWFDOFN($docNo)
    {
        if (strpos($docNo, 'CO') !== false || strpos($docNo, 'PO') !== false) {
            $docDONo = $this->getPOCOnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else if (substr($docNo, 0, 2) == '60' && strpos($docNo, '60') !== false) {
            $docDONo = $this->getDOnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else if (strpos($docNo, 'FN') !== false) {
            $docDONo = $this->getFNnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else if (strpos($docNo, 'DN') !== false || strpos($docNo, 'CN') !== false) {
            $docDONo = $this->getDNnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else if (strpos($docNo, 'SD') !== false) {
            $docDONo = $this->getSDnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else if (strpos($docNo, 'PA') !== false) {
            $docDONo = $this->getPAnumber($docNo);
            $doc_no = $docDONo[0]->doc_no;
        } else {
            $doc_no = $docNo;
        }
        $getStatusWf = $this->getStatusWf($doc_no);
        $html = "<thead>
                            <tr>
                            <th class='text-center'>PO /CO Number</th>
                            <th class='text-center'>DO Number</th>
                            <th class='text-center'>DO Status</th>
                            <th class='text-center'>FRN Number</th>
                            <th class='text-center'>FRN Status</th>
                            <th class='text-center'>Delivery Order ID</th>
                            <th class='text-center'>Fulfilment Note ID</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($getStatusWf as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->poco_no</strong></td> 
                        <td class='text-center'><strong>$value->delivery_order_no</strong></td>
                        <td class='text-center'><strong>($value->do_status) $value->do_status_name</strong></td>
                        <td class='text-center'><strong>$value->frn_no</strong></td>
                        <td class='text-center'><strong>($value->frn_status) $value->frn_status_name</strong></td>
                        <td class='text-center'><strong>$value->delivery_order_id</strong></td>
                        <td class='text-center'><strong>$value->fulfilment_note_id</strong></td>    
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function getFNSummary()
    {
        $docNo = request()->doc_no;
        $itemparcial = $PRCRNo = $docDONo = $getRoOfficer = $listManualAddressUser = $POCOstatus = $listDnItem = $submit_mm501 = $check_mm501 = $epp_013 = $list_Epp_017ReturnN = null;
        $getPOCOcur = $getsqrnno = $getrealnameaccPO = $asigneeNameListPO = $Crdetails1 = $LatestDoc = $listAo = $getFactoringDetails = $frn_mm504 = $list_mm506 = null;
        $resultArray = $listDataResultPO = $listdataacquiredPO = $itemListOrderBeforeApprove = $listItemOrder = $listAddressAvailable = $getVotList = null;
        $listAddress = $getDODetails = $listSDiItem = $listPMO = $listFlActor = $listFulfilmentApprover = $app = $req = $getPRCRInfo = $getCtInfo = $getCtInfo2 = $bankInfo = null;
        $startTime = microtime(true);
        $endTime = microtime(true);
        $elapsedTime = $endTime - $startTime;

        if (strlen($docNo) == 17) {
            if (strpos($docNo, 'CO') !== false || strpos($docNo, 'PO') !== false) {
                $getPOCONo = $this->getPOCOnumber($docNo);
                if (!empty($getPOCONo)) {
                    $PRCRNo = $getPOCONo[0]->doc_no;
                } else {
                    $docNo =  "Document not exist";
                }
            } else if (strpos($docNo, 'PR') !== false || strpos($docNo, 'CR') !== false) {
                $PRCRNo = $docNo;
            } else if (strpos($docNo, 'FN') !== false) {
                $docDONo = $this->getFNnumber($docNo);
                if (!empty($docDONo)) {
                    $PRCRNo = $docDONo[0]->doc_no;
                } else {
                    $docNo =  "Document not exist";
                }
            } else if (substr($docNo, 0, 2) == '60' && strpos($docNo, '60') !== false) {
                $docDONo = $this->getDOnumber($docNo);
                if (!empty($docDONo)) {
                    $PRCRNo = $docDONo[0]->doc_no;
                } else {
                    $docNo =  "Document not exist";
                }
            } else if (strpos($docNo, 'DN') !== false || strpos($docNo, 'CN') !== false) {
                $getDNNo = $this->getDNnumber($docNo);
                if (!empty($getDNNo)) {
                    $PRCRNo = $getDNNo[0]->doc_no;
                } else {
                    $docNo = "Document not exist";
                }
            } else if (strpos($docNo, 'SD') !== false) {
                $getSDNo = $this->getSDnumber($docNo);
                if (!empty($getSDNo)) {
                    $PRCRNo = $getSDNo[0]->doc_no;
                } else {
                    $docNo = "Document not exist";
                }
            } else if (strpos($docNo, 'PA') !== false) {
                $getPANo = $this->getPAnumber($docNo);
                if (!empty($getPANo)) {
                    $PRCRNo = $getPANo[0]->doc_no;
                } else {
                    $docNo = "Document not exist";
                }
            } else {
                $docNo = 'Carian (PR, CR, PO, CO, DO, FN)... ';
            }
        } else if (strlen($docNo) >= 17) {
            $docNo = 'Lebih dari 17 digit';
        } else if (strlen($docNo) >= 1 && strlen($docNo) <= 17) {
            $docNo = 'Kurang dari 17 digit';
        } else {
            $docNo = null;
        }

        if ($PRCRNo && $PRCRNo != null) {
            $itemparcial = $this->getFNItemParcial($PRCRNo);
            $getDODetails = $this->getDODetails($PRCRNo);
            $itemListOrderBeforeApprove = $this->getListItemBeforeApproved($PRCRNo);
            $listItemOrder = $this->getListItemOrder($PRCRNo);
            $getPRCRStatus = $this->getListPRCRStatus($PRCRNo);
            $getVotList = $this->getListVot($PRCRNo);
            $listFulfilmentApprover = $this->getFulfilmentApprover($PRCRNo, $PRCRNo);
            $listPMO = $this->getPMOuser($PRCRNo);
            $listFlActor = $this->getListFlActor($PRCRNo);
            $listAo = $this->getAouser($PRCRNo);
            $listSDiItem = $this->getListSDIItem($PRCRNo);
            $listDnItem = $this->getListDnItem($PRCRNo);
            $getPRCRInfo = $this->getInfoPRCR($PRCRNo);
            $flActor = $this->getActorListPr($PRCRNo);

            //check igfmas resolution
            set_time_limit(30); // Set script timeout to 30 seconds
            $start_time = microtime(true);

            try {
                $submit_mm501 = $this->getSubmit_mm501($PRCRNo);
                if ((microtime(true) - $start_time) > 30) {
                    throw new Exception("Query timeout");
                }

                $check_mm501 = $this->getCheck_mm501($PRCRNo);
                // dd($check_mm501);
                if ((microtime(true) - $start_time) > 30) {
                    throw new Exception("Query timeout");
                }

                $epp_013 = $this->getEpp_013($PRCRNo);
                $frn_mm504 = $this->getFrnMM504($PRCRNo);
                $list_mm506 = $this->getMM506($PRCRNo);
                $list_Epp_017ReturnN = $this->getEpp_017ReturnN($PRCRNo);
            } catch (Exception $e) {
                error_log("Database timeout or failure: " . $e->getMessage());
                // Load an alternative part of the page
                include("alternative_part.php");
                exit;
            }
            foreach ($flActor as $actor) {
                if ($actor->role_code == 'REQUISITIONER') {
                    $req = $actor->login_id . ' | ' . $actor->user_name;
                }
                if ($actor->role_code == 'RN_APPROVER') {
                    $app = $actor->login_id . ' | ' . $actor->user_name;
                }
            }
            $listAddressAvailable = $this->getListAddressAvailable($PRCRNo);
            $listAddress = $this->getlistAddress($PRCRNo, $PRCRNo);
            $getsqrnno = $this->getSqrnno($PRCRNo);
            $getCtInfo = $this->getCtInfoList($PRCRNo);
            $getRoOfficer = $this->getRoOfficer($PRCRNo);
            $POCOstatus = $this->getPOCOstatus($PRCRNo, $PRCRNo);
            $listManualAddressUser = $this->getRoOfficerManualAddress($PRCRNo);
            $latestDocPoco = $this->getLatestDocPoco($PRCRNo, $PRCRNo);
            $LatestDoc = $this->getLatestDoc($PRCRNo, $PRCRNo, $PRCRNo, $PRCRNo, $PRCRNo, $PRCRNo, $PRCRNo);
            $getFactoringDetails = $this->getFactoring($PRCRNo);

            if (!is_null($getCtInfo) && isset($getCtInfo[0]->supplier_id) && $getCtInfo[0]->supplier_id != 37074) {
                $getCtInfo2 = $this->getCtInfoList2($PRCRNo, $PRCRNo);
            }

            if (isset($getPRCRInfo) && $getPRCRInfo != null) {
                if ($getPRCRInfo[0]->branch_code !== null) {
                    $bankInfo = $this->bankInfo($getPRCRInfo[0]->branch_code, $getPRCRInfo[0]->supplier_id, $getPRCRInfo[0]->supplier_id);
                } else {
                    $bankInfo = $this->bankInfoHq($getPRCRInfo[0]->latest_appl_id);
                }
            }

            if ($elapsedTime <= 30) {
                if ($LatestDoc != null && (strpos($LatestDoc[0]->doc_no, 'PR') !== false || strpos($LatestDoc[0]->doc_no, 'CR') !== false)) {
                    $listDataResultPO = $this->findAPITaskBPMListDocAndModule($LatestDoc[0]->doc_no, 'Order');
                } else if ($LatestDoc != null && (strpos($LatestDoc[0]->doc_no, 'PR') === false || strpos($LatestDoc[0]->doc_no, 'CR') === false)) {
                    if ($LatestDoc[0]->status_id == '42030' || $LatestDoc[0]->status_id == '42015') {
                        $listDataResultPO = $this->findAPITaskBPMListDocAndModule($LatestDoc[0]->doc_no, 'Fulfilment');
                    } else {
                        $listDataResultPO = []; // Initialize an array to store multiple results
                        foreach ($LatestDoc as $listPR) {
                            // Append the result to the array instead of overwriting
                            $listDataResultPO[] = $this->findAPITaskBPMListDocAndModule($listPR->doc_no, 'Fulfilment');
                        }
                    }
                } else if ($latestDocPoco != null) {
                    $listDataResultPO = $this->findAPITaskBPMListDocAndModule($latestDocPoco[0]->doc_no, 'Order');
                }
            } else {
                $listDataResultPO = null;
            }
            if (is_array($listDataResultPO) && $listDataResultPO != null) {
                $try = 0;
                $resultArray = array();

                if (isset($listDataResultPO['status'])) { // 1-dimensional, turn into 2-dimensional
                    $listDataResultPO = array($listDataResultPO);
                }

                foreach ($listDataResultPO as $listDataResultPO1) {
                    if ($listDataResultPO1["status"] != null && $listDataResultPO1["status"] === 'Success') {
                        $listdataPO = $listDataResultPO1["result"];
                        foreach ($listdataPO as $key => $row) {
                            if ($row['state'] === 'ASSIGNED') {
                                $try = $key;
                                $listCompositePO = explode("*", $row['compositeDN']);
                                $assignees = array();
                                $listdataacquiredPO[$key]['acquiredBy'] = $row['acquiredBy'];
                                $listPONumber[$key]['docNumber'] = $row['docNumber'];
                                foreach ($row['assignees'] as $assignee) {
                                    $getnamePOArray = $this->getPmuser($assignee);
                                    if (!empty($getnamePOArray)) {
                                        $getnamePO = $getnamePOArray[0];
                                        if ($getnamePO != '') {
                                            $assignees[] = array($getnamePO->user_name, $getnamePO->login_id);
                                        }
                                    }
                                }
                                $resultArray[$row['docNumber']][] = array(
                                    'assigneeDetails' => $assignees,
                                    'taskName' => $row['taskName']
                                );
                            }
                        }
                    } else {
                        $statusAPI = $listDataResultPO["result"];
                    }
                }

                for ($i = 0; $i < count($listDataResultPO1); $i++) {
                    if ($listdataacquiredPO != null && is_array($listdataacquiredPO)) {
                        $getrealnameaccPO = array();
                        $asigneeNameListPO = array();

                        if (isset($listdataacquiredPO[$try])) {
                            $getacquireqPO = (array) $listdataacquiredPO[$try];
                            $doc_number = (array) $listPONumber[$try];

                            $getnameaccPO = $this->getPmuser($getacquireqPO['acquiredBy']);

                            if (is_array($getnameaccPO) && count($getnameaccPO) > 0) {
                                // Make sure the object structure matches your expectations
                                $userObject = $getnameaccPO[0];
                                if (property_exists($userObject, 'user_name') && property_exists($userObject, 'login_id')) {
                                    array_push($getrealnameaccPO, array($userObject->user_name, $userObject->login_id));
                                }
                            }

                            array_push($asigneeNameListPO, array($doc_number, $getrealnameaccPO));
                        }
                    }
                }
            }

            if ($listItemOrder != null && $getPRCRStatus != null) {
                $findItemCode = $this->findListItemCode($PRCRNo);
                $listItem = array();
                foreach ($findItemCode as $list) {
                    array_push($listItem, $list->item_code);
                }
                $collect = collect([]);
                $collect->put('item_code', array_unique(array_map('strval', $listItem)));

                $getOsbDetail = $this->getOSBitemCode($collect);
                $listItemCode = array();
                $uomIgfmasCode = [];

                $masterDataMaterialInfoRq = []; // Move this line here, before the foreach loop

                foreach ($getOsbDetail as $getOsb) {
                    if (isset($getOsb) && ($elapsedTime <= 30)) {
                        $listpayload = $getOsb->payload_body;
                        $response = $listpayload;
                    } else {
                        $response = null;
                    }
                    if ($response != 'NA') {
                        $dom = new DOMDocument();
                        $dom->loadXML($response);
                        $xpath = new DOMXPath($dom);
                        $xpath->registerNamespace('ep', 'http://www.ep.gov.my/Schema/1-0/MasterDataMaterialInfo');
                        $uomDetailsArray = [];
                        foreach ($xpath->query('//ep:EPMFRq//ep:MasterDataMaterialInfoRq//ep:UomDetailsArray/*') as $uomDetail) {
                            $altUnitOfMeasureNode = $xpath->query('./*', $uomDetail)->item(0);
                            $altUnitOfMeasure = $altUnitOfMeasureNode ? $altUnitOfMeasureNode->nodeValue : null;
                            $uomDetailsArray[] = ['AltUnitOfMeasure' => $altUnitOfMeasure];


                            foreach ($xpath->query('//ep:EPMFRq//ep:MasterDataMaterialInfoRq/*') as $element) {
                                if ($element->nodeName === 'UomDetailsArray') {
                                    $masterDataMaterialInfoRq['UomDetailsArray'] = $uomDetailsArray;
                                } else {
                                    $masterDataMaterialInfoRq[$element->nodeName] = $element->nodeValue;
                                }
                            }
                        }
                        $materialCodeNode = $xpath->query('//ep:EPMFRq//ep:MasterDataMaterialInfoRq//ep:MaterialCode')->item(0);
                        $materialCode = $materialCodeNode ? $materialCodeNode->nodeValue : null;

                        array_push($listItemCode, $materialCode);

                        $json = json_encode(['EPMFRq' => ['MasterDataMaterialInfoRq' => $masterDataMaterialInfoRq]], JSON_PRETTY_PRINT);

                        $dataArray = json_decode($json, true);

                        if (isset($dataArray['EPMFRq']['MasterDataMaterialInfoRq']['UomDetailsArray'])) {
                            $altUnitOfMeasures = array_column($dataArray['EPMFRq']['MasterDataMaterialInfoRq']['UomDetailsArray'], 'AltUnitOfMeasure');
                        } else {
                            $altUnitOfMeasures = [];
                        }
                        array_push($uomIgfmasCode, $altUnitOfMeasures);
                    }
                }
                for ($i = 0; $i < sizeof($listItemOrder); $i++) {
                    $list_item_code = $listItemOrder[$i]->item_code;
                    $list_uom_code = $listItemOrder[$i]->uom_code;
                    if (is_array($listItemCode) && in_array($list_item_code, $listItemCode)) {
                        $found = false;
                        foreach ($uomIgfmasCode as $uomArray) {
                            if (is_array($uomArray) && in_array($list_uom_code, $uomArray)) {
                                $found = true;
                                break;
                            }
                        }
                        if ($found) {
                            $listItemOrder[$i]->uom_code_igfmas = $list_uom_code;
                        } else {
                            $listItemOrder[$i]->uom_code_igfmas = 'Need to trigger';
                        }
                    } else {
                        $listItemOrder[$i]->uom_code_igfmas = 'Need to trigger';
                    }
                }
                foreach ($listItemOrder as $findItemCode) {
                    $getMMNIFD = $this->findMMNIFD($findItemCode->item_code, $findItemCode->uom_code);
                    foreach ($getMMNIFD as $getItemCode) {
                        $findItemCode->mminf_id = $getItemCode->mminf_id;
                    }
                }
            }
        }

        return view('list_fn_summary', [
            'docno' => $docNo,
            'itemListOrderBeforeApprove' => $itemListOrderBeforeApprove,
            'fnparcialarrays' => $itemparcial,
            'listItemOrder' => $listItemOrder,
            'listSDiItem' => $listSDiItem,
            'sqrn1' => $getsqrnno,
            'POCOstatus' => $POCOstatus,
            'asigneeNameListPO1' => $resultArray,
            'popayment' => $asigneeNameListPO,
            'getRoOfficer' => $getRoOfficer,
            'listManualAddressUser' => $listManualAddressUser,
            'poname' => $getrealnameaccPO,
            'maklumatUmumPTJ' => $getPRCRInfo,
            'app' => $app,
            'req' => $req,
            'getPOCOcur' => $getPOCOcur,
            'Crdetails1' => $Crdetails1,
            'listAddress' => $listAddress,
            'listAddressAvailable' => $listAddressAvailable,
            'listFulfilmentApprover' => $listFulfilmentApprover,
            'listPMO' => $listPMO,
            'listFlActor' => $listFlActor,
            'listAo' => $listAo,
            'getCtInfo' => $getCtInfo,
            'getCtInfo2' => $getCtInfo2,
            'listDnItem' => $listDnItem,
            'getFactoringDetails' => $getFactoringDetails,
            'getVotList' => $getVotList,
            'bankInfo' => $bankInfo,
            'submit_mm501' => $submit_mm501,
            'check_mm501' => $check_mm501,
            'epp_013' => $epp_013,
            'frn_mm504' => $frn_mm504,
            'list_mm506' => $list_mm506,
            'list_Epp_017ReturnN' => $list_Epp_017ReturnN,
            'getDODetails' => $getDODetails,
            'carian' => request()->doc_no
        ]);
    }

    protected function dpWorkflowSummary($docid, $doctype)
    {
        $workflowDP = $this->trackingDPWorkflowDetails($docid, $doctype);
        $html = "<table  id='workflow_datatable' class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Doc Id</th>
                            <th class='text-center'>Doc Type</th>
                            <th class='text-center'>Created Date</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Is Current</th>
                            <th class='text-center'>Created By</th>
                            <th class='text-center'>Changed By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($workflowDP as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->doc_id</strong></td> 
                        <td class='text-center'><strong>$value->doc_type</strong></td>
                        <td class='text-center'><strong>$value->created_date</strong></td>
                        <td class='text-center'><strong>$value->status_name</strong></td>
                        <td class='text-center'><strong>$value->is_current</strong></td>
                        <td class='text-center'><strong>$value->created_by - $value->login_id</strong></td>
                            <td class='text-center'><strong>$value->changed_by - $value->login</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }

    protected function flWorkflowSummary($docid, $doctype)
    {
        $workflowFL = $this->trackinFLPWorkflowDetails($docid, $doctype);
        $html = "<table  id='workflow_fl_datatable' class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Doc Id</th>
                            <th class='text-center'>Doc Type</th>
                            <th class='text-center'>Created Date</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>Is Current</th>
                            <th class='text-center'>Created By</th>
                            <th class='text-center'>Changed By</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($workflowFL as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->doc_id</strong></td> 
                        <td class='text-center'><strong>$value->doc_type</strong></td>
                        <td class='text-center'><strong>$value->created_date</strong></td>
                        <td class='text-center'><strong>$value->status_name</strong></td>
                        <td class='text-center'><strong>$value->is_current</strong></td>
                        <td class='text-center'><strong>$value->created_by - $value->login_id</strong></td>
                            <td class='text-center'><strong>$value->changed_by - $value->login</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }
}
