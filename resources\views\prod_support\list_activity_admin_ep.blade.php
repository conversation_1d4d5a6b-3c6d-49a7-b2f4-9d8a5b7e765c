@extends('layouts.guest-dash')

@section('content')

<div class="block block-alt-noborder full">
    <form action="{{ url('/activity/admin/ep') }}" method="post">
        {{ csrf_field() }}
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Activity By Ep Admin</strong></h1> 
        </div>
        <div class="form-group">
            <label class="col-md-1 control-label" for="module">Start Date</label>
            <div class="input-group date text-right" data-provide="datepicker">
                <input id = 'startdate' name="startdate" type="text" class="form-control">
                <div style="display:none" class="input-group-addon">
                    <span class="glyphicon glyphicon-th"></span>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-1 control-label" for="module">End Date</label>
            <div class="input-group date text-right" data-provide="datepicker">
                <input id = 'enddate' name="enddate" type="text" class="form-control">
                <div style="display:none" class="input-group-addon">
                    <span class="glyphicon glyphicon-th"></span>
                </div>
            </div> 
        </div>
        <div class="form-actions form-actions-button text-left ">
            <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
        </div>
    </form>

    @if($logList != null && $listActivity == null)
    <div class="block block-alt-noborder full">
        <div class="block-title panel-heading epss-title-s3">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> Searching Log </strong></h1>
        </div>
        <div class="table-responsive">
            <table id = "list_log_action"  class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Start Date</th>
                        <th class="text-center">End Date</th>
                        <th class="text-center">Created Date</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($logList as $key=>$listed)
                    <tr>
                        <td class="text-center">{{ ++$key }}</td>
                        <td class="text-center">{{ $listed->log_user_name }}</td>
                        <td class="text-center">{{ $listed->log_date_from }}</td>
                        <td class="text-center">{{ $listed->log_date_to }}</td>
                        <td class="text-center">{{ $listed->log_created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif

    @if($listActivity != null)
    <div class="block block-alt-noborder full">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> List Of Activity </strong></h1> <h6>{{$start_date}} to {{$end_date}}</h6>
        </div>
        <div class="form-actions form-actions-button text-right ">
            <a href="/activity/admin/ep/download/{{$start_date}}/{{$end_date}}"><button type="submit" class="btn btn-sm btn-info"><i class="fa fa-download"></i> Download</button></a>
        </div>
        <div class="table-responsive">
            <table id = "list_activity_datatable"  class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Ep Admin</th>
                        <th class="text-center">Module/Document</th>
                        <th class="text-center">Document Number</th>
                        <th class="text-center">Action Desc</th>
                        <th class="text-center">Action By Id</th>
                        <th class="text-center">Action Date</th>
                        <th class="text-center">Document Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($listActivity as $key=>$listed)
                    <tr>
                        <td class="text-center">{{ ++$key }}</td>
                        <td class="text-center">{{ $listed->ep_admin }}</td>
                        <td class="text-center">{{ $listed->module_document }}</td>
                        <td class="text-center">{{ $listed->document_number }}</td>
                        <td class="text-center">{{ $listed->action_desc }}</td>
                        <td class="text-center">{{ $listed->actioned_by_id }}</td>
                        <td class="text-center">{{ $listed->action_date }}</td>
                        <td class="text-center">{{ $listed->document_status }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>
App.datatables();
$(document).ready(function () {
    $('#list_log_action').DataTable({
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]

    });
    $('#list_activity_datatable').DataTable({
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]

    });
});
    </script> 
@endsection

