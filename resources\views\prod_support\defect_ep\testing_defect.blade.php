@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="block block-alt-noborder full">

    <div class="block-title panel-heading">
        <h1><i class="fa fa-file-text"></i> <strong>Defect Testing</strong></h1>
    </div>

    <div class="block" style="display:none" id="formclassdefect">
        <form class="form-horizontal form-bordered " style="display:none" id ="defect_testing_form" action="{{url('prod-support/defect_testing')}}" method="post">
            {{ csrf_field() }}
            <div class="block-options pull-right">
                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm">Close</span>
            </div>
            <br />
            <div id="first" class="step">
                <!-- Step Info -->
                <input type="hidden" id="editid" name="editid" value="" class="form-control" style="width: 100px;">
                <div class="form-group">
                    <label class="col-md-4 text-right redmine" for="redmine_no">Redmine Number<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="number" id="redmine_no" name="redmine_no" value="" required class="form-control" style="width: 700px;">
                        <label class="col-md-12 text-left redmine" for="remind"><span class="text-danger">Please click out to refresh status in Redmine</span></label>
                    </div> 
                </div>  
                <div class="form-group">
                    <label class="col-md-2 text-right" for="status">Redmine Status</label>
                    <div class="col-md-3">
                        <input id="status" readonly="" name="status" type="text" class="form-control" style="width: 700px;">
                    </div>
                    <label class="col-md-2 text-right" for="moduletest">Module</label>
                    <div class="col-md-3">
                        <input id="moduletest" name="moduletest" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 text-right" for="edd">Estimated Deploy Date</label>
                    <div class="col-md-3">
                        <input id="eddid" name="eddid" type="text" class="form-control" style="width: 700px;">
                    </div>
                    <label class="col-md-2 text-right" for="batch_date">Batch Date<span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input id = 'date1' name="date1" type="date" required class="form-control">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 text-right" for="deploystatus">Deployment Status</label>
                    <div class="col-md-3">
                        <select id="deploystatus" name = "deploystatus" class="form-control" style="width: 700px;">
                            <option value="">Please Select</option>
                            <option value="Pending Fixing">Pending Fixing</option>
                            <option value="Pending Deployment">Pending Deployment</option>
                            <option value="Ready for Testing">Ready for Testing</option>
                        </select>
                    </div>
                    <label class="col-md-2 text-right"> Urgent Testing ?</label>
                    <div class="col-md-1">
                        <input type="radio" name="radio_urgent_testing" value="yes"> Yes
                        <input type="radio" name="radio_urgent_testing" value="no" checked="checked"> No
                    </div>
                    <label class="col-md-1 text-right"> Dependency</label>
                    <div class="col-md-1">
                        <input type="radio" name="radio_dependency" value="yes"> Yes
                        <input type="radio" name="radio_dependency" value="no" checked="checked"> No
                    </div> 
                </div>

                <div class="form-group">
                    <label class="col-md-2 text-right" for="Subject">Subject</label>
                    <div class="col-md-8">
                        <textarea readonly="" type="text" id="Subject" name="Subject" rows="2" class="form-control"></textarea>
                    </div>
                </div>
            </div>

            <div id="second" class="step">
                <!-- Step Info -->
                <div class="wizard-steps">

                </div>
                <div class="form-group">
                    <label class="col-md-2 text-right" for="raised">Raised By</label>
                    <div class="col-md-3">
                        <input id="raised" name="raised" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                    <label class="col-md-2 text-right" for="typedefect">Type</label>
                    <div class="col-md-3">
                        <input id="typedefectid" name="typedefectid" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-2 text-right" for="assignee">Assignee</label>
                    <div class="col-md-3">
                        <input id="assigneeid" name="assigneeid" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                    <label class="col-md-2 text-right" for="developer">Developer</label>
                    <div class="col-md-3">
                        <input id="developerid" name="developerid" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                </div>
                <div class="form-group remarksgroup" style="display:none">
                    <label class="col-md-2 text-right" for="tester">Test By</label>
                    <div class="col-md-3">
                        <select id="tester" name = "tester" class="form-control" style="width: 700px;">
                            <option value="">Please Select</option>
                            <option value="Shahril Anuar Ismail">Shahril Anuar Ismail</option>
                            <option value="Norhasfarine">Norhasfarine</option>
                            <option value="Safinah Salleh">Safinah Salleh</option>
                            {{-- <option value="Siti Norazreen Bt Abdullah">Siti Norazreen Bt Abdullah</option> --}}
                            <option value="MUHD AZWANDY BIN IJMAEN">MUHD AZWANDY BIN IJMAEN</option>
                            <option value="Aema Binti Ismail">Aema Binti Ismail</option>
                            <option value="AKMAL SAUFI BIN MASHAR">AKMAL SAUFI BIN MASHAR</option>
                            <option value="Putera Mohd Hafizi Helmi">Putera Mohd Hafizi Helmi</option>
                            <option value="Fauziah bt Mohd Mukhtar">Fauziah bt Mohd Mukhtar</option>
                            {{-- <option value="Mahadi Bin Jasman">Mahadi Bin Jasman</option> --}}
                            <option value="Nurun Nazifah">Nurun Nazifah</option>
                            <option value="Aminah Binti Abdul Muin">Aminah Binti Abdul Muin</option>
                            <option value="IQBAL FIKRI MOHAMED MISMAN">IQBAL FIKRI MOHAMED MISMAN</option>
                            <option value="MOHD YUSRI BIN ABD JABAR">MOHD YUSRI BIN ABD JABAR</option>
                            <option value="Mohd Shamsul Bin Amerudin">Mohd Shamsul Bin Amerudin</option>
                            <option value="Mior Muhamad Syairofi">Mior Muhamad Syairofi</option>
                            <option value="ASMIRA">ASMIRA BINTI SUPARDI</option>
                            <option value="Nurul Atiqah">Nurul Atiqah Binti Mohd Radzi</option>
                        </select>
                    </div>
                    <label class="col-md-2 text-right" for="teststatus">Test Status</label>
                    <div class="col-md-3">
                        <select id="teststatus" name = "teststatus" class="form-control" style="width: 700px;">
                            <option value="">Please Select</option>
                            <option value="Pass">Pass</option>
                            <option value="Reopen">Reopen</option>
                            <option value="Closed">Closed</option>
                            <option value="In Progress">In Progress</option>
                        </select>
                    </div>
                </div>
                <div class="form-group testgroup" style="display:none">
                    <label class="col-md-2 text-right" for="counttestDate">Count Test</label>
                    <div class="col-md-3">
                        <input id="counttestDate" name="counttestDate" readonly="" type="text" class="form-control" style="width: 700px;">
                    </div>
                    <div class="col-md-4">
                        @if($listdata != null)
                        <div class="addtestid">
                            <button type="button" id="addtest" class="btn btn btn-primary" style="float: right;">
                                <a href='#modal_list_testing_time'
                                   class='modal-list-data-action'
                                   data-toggle='modal'  
                                   data-url='/prod_support/defect_testing/{{ $listdata[0]->defect_id }}'
                                   data-title='List Testing Time'>
                                    <strong style="color:white;font-weight: bolder;">
                                        <i class="fa fa-users"></i> 
                                        Testing Date</strong><br />
                                </a>
                            </button>
                        </div>
                        @endif
                    </div>
                </div>
                <div class="form-group remarksgroup" style="display:none">
                    <label class="col-md-2 text-right" for="Subject">Test Remarks</label>
                    <div class="col-md-8">
                        <textarea type="text" id="remarks" name="remarks" rows="2" class="form-control"></textarea>
                    </div>
                </div>
            </div>

            <div class="form-group form-actions">
                <div class="col-md-12 col-md-offset-5">
                    <input type="reset" class="btn btn-sm btn-warning" id="back" value="Back">
                    <input type="submit" name="Submit" value="Submit" class="btn btn-sm btn-primary submitbutton" id="save-script" value="Next">


                </div>
            </div>

            <div id="modal_list_testing_time" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <input type="hidden" id="editidtime" name="editidtime" value="" class="form-control">
                        <input type="hidden" id="defect_date_id" name="defect_date_id" value="" class="form-control">
                        <input type="hidden" id="editdateid" name="editdateid" value="" class="form-control">
                        <div class="modal-header text-center">
                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Testing Date</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="form-group testgroup" style="display:none">
                                <label class="col-md-2 text-right" for="startdate" >Start Date<span class="text-danger">*</span></label>
                                <div class="col-md-3">
                                    <input id = 'startdate' name="startdate" type="date" required class="form-control" >
                                </div>
                                <label class="col-md-2 text-right" for="enddate" >End Date</label>
                                <div class="col-md-3">
                                    <input id = 'enddate' name="enddate" type="date" class="form-control" >
                                    <strong><label class="text-danger" id="reset_end_date">Click to RESET</label></strong>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 text-right" for="enddate">Remarks</label>
                                <div class="col-md-8">
                                    <input id="remarkstestseq" name="remarkstestseq" type="text" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="button" class="btn btn btn-primary editSave" style="display:none" style="float: right;">Edit Save</button>
                                <button type="button" class="btn btn btn-primary cancelEdit" style="display:none" style="float: right;">Cancel Edit</button>
                                <button type="button" class="btn btn btn-primary addtime" style="float: right;">Add Time</button>
                            </div>
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive"><table id="date_testing" class="table table-vcenter table-condensed table-bordered" ></table></div>
                        </div>
                    </div>
                </div>
            </div>

            <div id="modal_confirm_delete_details" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Delete Testing Date? </label> &nbsp;&nbsp;&nbsp;
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="button" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>

    <form action="{{ url('/prod-support/defect_testing/download') }}" method="post">
        {{ csrf_field() }}
        <div class="form-actions form-actions-button text-right ">
            <button type="submit" id="downloadfromdb" class="btn btn btn-primary" style="float: right;"><i class="fa fa-download"> Download</i></button>
        </div>
    </form>
    <button type="button" id="openAddTask" class="btn btn btn-primary" >Add Task</button>
    <div><br /></div>
    <div class="tab">
        <button class="tablinks" onclick="StatusControl(event, 'AllStatus')">All Status</button>
        <button class="tablinks" onclick="StatusControl(event, 'ClosedStatus')">Closed</button>
    </div>
    <br />

    <!-- Tab content -->
    <div id="AllStatus" class="tabcontent">
        @if($listdata != null)
        <div class="block">

            <div class="block-options">
                @if(isset($listRedmineStatus))
                <label class="col-md-2 text-right" for="findbystatus">Redmine Status</label>
                <div class="col-md-2 findstatus">
                    <select id="findbystatus" name="findbystatus" class="form-control" style="width: 300px;">
                        <option value="">All Status</option>
                        @foreach($listRedmineStatus as $key)
                        <option value="{{$key->redmine_status}}">{{$key->redmine_status}}</option>
                        @endforeach
                    </select>
                </div>
                @endif

                <label class="col-md-2 text-right" for="findteststatus">Test Status</label>
                <div class="col-md-2 findbytest">
                    <select id="findteststatus" name="findteststatus" class="form-control" style="width: 300px;">
                        <option value="">All Status</option>
                        <option value="Pass">Pass</option>
                        <option value="Reopen">Reopen</option>
                        <option value="Closed">Closed</option>
                        <option value="In Progress">In Progress</option>
                    </select>
                </div>

                <label class="col-md-2 text-right" for="finddeployment">Deployment Status</label>
                <div class="col-md-2 findbydeployment">
                    <select id="finddeployment" name="finddeployment" class="form-control" style="width: 300px;">
                        <option value="">Please Select</option>
                        <option value="Pending Fixing">Pending Fixing</option>
                        <option value="Pending Deployment">Pending Deployment</option>
                        <option value="Ready for Testing">Ready for Testing</option>
                    </select>
                </div>
            </div>

            <br />
            <div class="block-title block-options">
                <h1><i class="fa fa-tasks"></i> <strong>List Defects</strong></h1>
            </div>

            <div class="table-responsive">
                <table id="defect_testing-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Batch Date</th>
                            <th class="text-center">Redmine No</th>
                            <th class="text-center">Module</th>
                            <th class="text-center">Redmine Status</th>
                            <th class="text-center">Test Status</th>
                            <th class="text-center">Deployment Status</th>
                            <th class="text-center">Subject</th>
                            <th class="text-center">EDD</th>
                            <th class="text-center">Test By</th>
                            <th class="text-center">Test Days</th>
                            <th class="text-center">Action</th>
                            <th class="text-center">urgent testing</th>
                            <th class="text-center">Dependency</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($listdata as $rowData=> $data)
                        <tr>
                            <td class="text-center">{{ $data->redmine_date }}</td>
                            <td class="text-center">{{ $data->redmine_no }}</td>
                            <td class="text-center">{{ $data->module }}</td>
                            <td class="text-center">{{ $data->redmine_status }}</td>
                            <td class="text-center"><a href="javascript:void(0)" @if($data->test_status == "In Progress") class="label label-warning">{{ $data->test_status }} @elseif($data->test_status == "Pass") class="label label-success">{{ $data->test_status }} @elseif($data->test_status == "Closed") class="label label-info">{{ $data->test_status }} @elseif($data->test_status == "Reopen") class="label label-danger">{{ $data->test_status }} @endif</a></td>
                            <td class="text-center">{{ $data->deploy_status }}</td>
                            <td class="text-center">{{ $data->subject }}</td>
                            <td class="text-center">{{ $data->edd }}</td>
                            <td class="text-center">{{ $data->test_by }}</td>
                            <td class="text-center">@if($data->counttest != 0) @foreach($data->testCount as $test => $val) {{ $val->created_by.' ('.$val->Testing.')' }}<br/>  @endforeach @endif</td>
                            <td><a idno ="{{$data->defect_id}}" 
                                   date ="{{ $data->redmine_date }}"
                                   redmine_no ="{{ $data->redmine_no }}"
                                   subject ="{{ $data->subject }}"
                                   module ="{{ $data->module }}"
                                   raised_by ="{{ $data->raised_by }}"
                                   redmine_status ="{{ $data->redmine_status }}"
                                   deploy_status ="{{ $data->deploy_status }}"
                                   test_status ="{{ $data->test_status }}"
                                   type ="{{ $data->type_defect }}"
                                   test_by ="{{ $data->test_by }}"
                                   assignee ="{{ $data->assignee }}"
                                   developer ="{{ $data->developer }}"
                                   edd ="{{ $data->edd }}"
                                   remarks =" {{ $data->test_remarks}}"
                                   urgent_testing =" {{ $data->urgent_testing}}"
                                   dependency_ =" {{ $data->dependency}}"
                                   count_date = "@if($data->counttest != 0){{ $data->counttest}} @endif"
                                   data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a></td>
                            <td class="text-center">{{ $data->urgent_testing }}</td>
                            <td class="text-center">{{ $data->dependency }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>

    <div id="ClosedStatus" class="tabcontent" style="display:none">
        @if($listdataWithClosedStatus != null)
        <div class="block">

            <div class="block-options">
                <label class="col-md-2 text-right" for="findteststatusclosed">Test Status</label>
                <div class="col-md-3 findbytestclosed">
                    <select id="findteststatusclosed" name="findteststatusclosed" class="form-control" style="width: 300px;">
                        <option value="">All Status</option>
                        <option value="Pass">Pass</option>
                        <option value="Reopen">Reopen</option>
                        <option value="Closed">Closed</option>
                        <option value="In Progress">In Progress</option>
                    </select>
                </div>
            </div>

            <br />
            <div class="block-title block-options">
                <h1><i class="fa fa-tasks"></i> <strong>Closed List Defects</strong></h1>
            </div>

            <div class="table-responsive">
                <table id="defect_testing_closed-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Batch Date</th>
                            <th class="text-center">Redmine No</th>
                            <th class="text-center">Module</th>
                            <th class="text-center">Redmine Status</th>
                            <th class="text-center">Test Status</th>
                            <th class="text-center">Deployment Status</th>
                            <th class="text-center">Subject</th>
                            <th class="text-center">EDD</th>
                            <th class="text-center">Test By</th>
                            <th class="text-center">Test Days</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach($listdataWithClosedStatus as $rowData => $data)
                        <tr>
                            <td class="text-center">{{ $data->redmine_date }}</td>
                            <td class="text-center">{{ $data->redmine_no }}</td>
                            <td class="text-center">{{ $data->module }}</td>
                            <td class="text-center">{{ $data->redmine_status }}</td>
                            <td class="text-center"><a href="javascript:void(0)" @if($data->test_status == "In Progress") class="label label-warning">{{ $data->test_status }} @elseif($data->test_status == "Pass") class="label label-success">{{ $data->test_status }} @elseif($data->test_status == "Closed") class="label label-info">{{ $data->test_status }} @elseif($data->test_status == "Reopen") class="label label-danger">{{ $data->test_status }} @endif</a></td>
                            <td class="text-center">{{ $data->deploy_status }}</td>
                            <td class="text-center">{{ $data->subject }}</td>
                            <td class="text-center">{{ $data->edd }}</td>
                            <td class="text-center">{{ $data->test_by }}</td>
                            <td class="text-center">@if($data->counttest != 0) @foreach($data->testCount as $test => $val) {{ $val->created_by.' ('.$val->Testing .')' }}<br/>  @endforeach @endif</td>
                            <td><a idno ="{{$data->defect_id}}" 
                                   date ="{{ $data->redmine_date }}"
                                   redmine_no ="{{ $data->redmine_no }}"
                                   subject ="{{ $data->subject }}"
                                   module ="{{ $data->module }}"
                                   raised_by ="{{ $data->raised_by }}"
                                   redmine_status ="{{ $data->redmine_status }}"
                                   deploy_status ="{{ $data->deploy_status }}"
                                   test_status ="{{ $data->test_status }}"
                                   type ="{{ $data->type_defect }}"
                                   test_by ="{{ $data->test_by }}"
                                   assignee ="{{ $data->assignee }}"
                                   developer ="{{ $data->developer }}"
                                   edd ="{{ $data->edd }}"
                                   remarks =" {{ $data->test_remarks}}"
                                   count_date = "@if($data->counttest != 0){{ $data->counttest}} @endif"
                                   data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a></td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>



@endsection

@section('jsprivate')
<script src="/js/pages/psDefectEpWizardForm.js"></script>
<script>$(function () {
                FormsWizardDefectEp.init();
            });</script>
<script>$(function () {
        $('#defect_testing-datatable').DataTable({
            order: [[12, "asc"]],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']],
            "columnDefs": [
                {
                    "targets": [11, 12],
                    "visible": false,
                    "searchable": false
                }
            ],
            "fnRowCallback": function (nRow, aData) {
                if (aData[11] === 'yes') {
                    $('td', nRow).css('background-color', 'Orange').css('font-style', 'italic');
                    ;
                }
                if (aData[12] === 'yes') {
                    $('td', nRow).css('background-color', '#49FFE0').css('font-style', 'italic');
                    ;
                }
            }});
        $('#defect_testing_closed-datatable').DataTable({order: [[0, "asc"]],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]});
    });</script>
<script>
    App.datatables();
    window.parent.document.body.style.zoom = 0.9;

    var APP_URL = {!! json_encode(url('/')) !!}
    $("#redmine_no").on("focusout", function () {
        var redmineNo = $('#redmine_no').val();
        $("#status").trigger("reset");
        $("#raised").trigger("reset");
        $("#assigneeid").trigger("reset");
        $("#Subject").trigger("reset");
        $("#moduletest").trigger("reset");
        $("#developerid").trigger("reset");
        $("#typedefectid").trigger("reset");
        $("#eddid").trigger("reset");
        $.ajax({
            url: APP_URL + '/prod-support/defect_testing/redmine_no/' + redmineNo,
            dataType: 'json',
            type: "GET",
            success: function (data) {
                $('#redmine_no').val(redmineNo);
                if (data.status === 'Success') {
                    var objRedmine = data.result;
                    console.log(objRedmine)
                    $("#status").val(objRedmine.issue.status.name);
                    $("#raised").val(objRedmine.issue.author.name);
                    $("#assigneeid").val(objRedmine.issue.assigned_to.name);
                    $("#Subject").val(objRedmine.issue.subject);
                    $("#moduletest").val(objRedmine.issue.custom_fields[0].value);
                    $("#eddid").val(objRedmine.issue.custom_fields[16].value);
                    $("#developerid").val(data[1]);
                    var type_defect = [(data[0][0]), (data[0][1])];
                    if (!data[0][1]) {
                        $("#typedefectid").val(data[0][0]);
                    }
                    else if (data[0][1]) {
                        type_defect.forEach(myFunction);
                        function myFunction(item) {
                            var $typedefect = document.getElementById('typedefectid').innerText += item + ", ";
                            $("#typedefectid").val($typedefect);
                        }
                    }
                } else {
                    $('#defect_testing_form').trigger("reset");
                    alert('Result not found for searching redmine no : ' + redmineNo);
                    $('#redmine_no').val(redmineNo);
                }
            },
        })
    });

    $("#openAddTask").on("click", function () {
        $('#formclassdefect').show();
        $('#defect_testing_form').show();
        $('#openAddTask').hide();
        $('.testgroup').hide();
        $('.remarksgroup').hide();
        $('#editid').val("");
        $('#redmine_no').val("");
        $("#status").val("");
        $("#moduletest").val("");
        $("#raised").val("");
        $("#date1").val("");
        $("#developerid").val("");
        $("#assigneeid").val("");
        $("#eddid").val("");
        $("#typedefectid").val("");
        $("#Subject").val("");
        $("#deploystatus").val("");
        $("#teststatus").val("");
        $("#tester").val("");
        $("#remarks").val("");
        $("#back").trigger("click");
    });

    function StatusControl(evt, statusName) {
        // Declare all variables
        var i, tabcontent, tablinks;

        // Get all elements with class="tabcontent" and hide them
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }

        // Get all elements with class="tablinks" and remove the class "active"
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }

        // Show the current tab, and add an "active" class to the button that opened the tab
        document.getElementById(statusName).style.display = "block";
        evt.currentTarget.className += " active";
    }

    $('.addtestid').on("click", '.modal-list-data-action', function () {
        $('.spinner-loading').show();
        $('#date_testing_datatable').hide();
        $('#editid').val();
        $('#defect_date_id').val();
        $('#defect_date_id').val("");
        $('#startdate').val("");
        $('#enddate').val("");
        $('#remarkstestseq').val("");
        var idno = $('#editid').val();
        $('#editidtime').val(idno);
        var defect_id = $('#editidtime').val();
        $('#modal-list-data-header').text($(this).attr('data-title'));

        $.ajax({
            url: "/prod-support/defect_testing/add_date/" + defect_id,
            type: "GET",
            table: $('#modal_list_testing_time').serialize(),
            table2: $('#counttestDate').serialize(),
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#date_testing').html(data).fadeIn();
                $('#date_testing_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
                });
            }
        });


    });

    $('#reset_end_date').on("click", function () {
        $('#enddate').val("");
    });

    $('.addtime').click(function () {
        var defect_id = $('#editidtime').val();
        var startdate = $('#startdate').val();
        var enddate = $('#enddate').val();
        var remarks = $('#remarkstestseq').val();
        $.ajax({
            type: "POST",
            url: "/prod-support/defect_testing/add_date/" + defect_id,
            table: $('#modal_list_testing_time').serialize(),
            table2: $('#counttestDate').serialize(),
            data: {
                '_token': $('input[name=_token]').val(),
                'defectid': defect_id,
                'startdate': startdate,
                'enddate': enddate,
                'remarks': remarks
            },
        }).done(function (data) {
            console.log(data);
        });
        $.ajax({
            url: "/prod-support/defect_testing/add_date/" + defect_id,
            type: "GET",
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#date_testing').html(data).fadeIn();
                $('#date_testing_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
                });
            }
        });
        $('#startdate').val("");
        $('#enddate').val("");
        $('#remarkstestseq').val("");

    });

    $(document).ready(function () {
        $('#modal_list_testing_time').on("click", '#trigger_edit_dt', function () {
            $('.cancelEdit').show();
            $('.editSave').show();
            $('.addtime').hide();
            let
            date_id = $(this).attr('dateid');
            let
            seq_id = $(this).attr('seq');
            let
            startdate = $(this).attr('startdatetest');
            let
            enddate = $(this).attr('enddatetest');
            let
            remarks = $(this).attr('remarkstest');

            $('#editdateid').val(date_id);
            $('#defect_date_id').val(seq_id);
            $('#startdate').val(startdate);
            $('#enddate').val(enddate);
            $('#remarkstestseq').val(remarks);
        });
    });

    $(document).ready(function () {
        $('#modal_list_testing_time').on("click", '#trigger_delete_dt', function () {
            let
            date_id = $(this).attr('dateid');
            let
            seq_id = $(this).attr('seq');
            let
            defect_id = $(this).attr('defect_id');
            $('#editdateid').val(date_id);
            $('#defect_date_id').val(seq_id);
            $('#editidtime').val(defect_id);
            $("#modal_confirm_delete_details").modal('show');
        });
    });

    $('#submit_confirm_delete').on('click', function () {
        var editdateid = $('#editdateid').val();
        var seq_id = $('#defect_date_id').val();
        var defect_id = $('#editidtime').val();
        document.getElementById("startdate").required = false;
        $('#modal_confirm_delete_details').modal('hide');
        $.ajax({
            type: "POST",
            url: "/prod-support/defect_testing/delete_date/" + editdateid,
            table: $('#modal_list_testing_time').serialize(),
            table2: $('#counttestDate').serialize(),
            data: {
                '_token': $('input[name=_token]').val(),
                'editdateid': editdateid,
                'seqid': seq_id,
                'defect_id': defect_id,
            },
        }).done(function (resp) {
            console.log(resp);
        });

        $.ajax({
            url: "/prod-support/defect_testing/add_date/" + defect_id,
            type: "GET",
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#date_testing').html(data).fadeIn();
                $('#date_testing_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
                });
            }
        });

    });

    $('.editSave').click(function () {
        var defect_date_id = $('#editdateid').val();
        var defect_id = $('#editidtime').val();
        var startdate = $('#startdate').val();
        var enddate = $('#enddate').val();
        var remarks = $('#remarkstestseq').val();
        $.ajax({
            type: "POST",
            url: "/prod-support/defect_testing/edit_date/" + defect_date_id,
            table: $('#modal_list_testing_time').serialize(),
            table2: $('#counttestDate').serialize(),
            data: {
                '_token': $('input[name=_token]').val(),
                'defectdateid': defect_date_id,
                'startdate': startdate,
                'enddate': enddate,
                'remarks': remarks
            },
        }).done(function (data) {
            console.log(data);
        });
        $.ajax({
            url: "/prod-support/defect_testing/add_date/" + defect_id,
            type: "GET",
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#date_testing').html(data).fadeIn();
                $('#date_testing_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
                });
            }
        });
        $('#startdate').val("");
        $('#enddate').val("");
        $('#remarkstestseq').val("");
        $('.cancelEdit').hide();
        $('.editSave').hide();
        $('.addtime').show();

    });

    $(".cancelEdit").on("click", function () {
        $('.addtime').show();
        $('.editSave').hide();
        $('.cancelEdit').hide();
        $('#defect_date_id').val("");
        $('#startdate').val("");
        $('#enddate').val("");
        $('#remarkstestseq').val("");
    });

    $("#closeTaskForm").on("click", function () {
        $('#defect_testing_form').hide();
        $('#formclassdefect').hide();
        $('#openAddTask').show();
        $('.testgroup').hide();
        $('.remarksgroup').hide();
        $('#editid').val("");
        $('#redmine_no').val("");
        $("#status").val("");
        $("#moduletest").val("");
        $("#raised").val("");
        $("#date1").val("");
        $("#developerid").val("");
        $("#assigneeid").val("");
        $("#eddid").val("");
        $("#typedefectid").val("");
        $("#Subject").val("");
        $("#deploystatus").val("");
        $("#teststatus").val("");
        $("#tester").val("");
        $("#remarks").val("");
        $("#counttestDate").val("");
    });

    function editButtonTriggered(a) {
        $('#to-top').click();
        $('#formclassdefect').show();
        $('.testgroup').show();
        $('#openAddTask').show();
        $('.remarksgroup').show();
        $("#defect_testing_form").show();
        $("#back").trigger("click");
        let
        id = $(a).attr('idno');
        let
        date = $(a).attr('date');
        let
        redmineno = $(a).attr('redmine_no');
        let
        subject = $(a).attr('subject');
        let
        module = $(a).attr('module');
        let
        type_id = $(a).attr('type');
        let
        raised = $(a).attr('raised_by');
        let
        developer = $(a).attr('developer');
        let
        assignee = $(a).attr('assignee');
        let
        edd = $(a).attr('edd');
        let
        redmine_status = $(a).attr('redmine_status');
        let
        deploy_status = $(a).attr('deploy_status');
        let
        test_status = $(a).attr('test_status');
        let
        tester = $(a).attr('test_by');
        let
        remark = $(a).attr('remarks');
        let
        count_date = $(a).attr('count_date');
        let
        urgent_testing = $(a).attr('urgent_testing');
        var value = urgent_testing;
        if (value !== null) {
            $("input[name=radio_urgent_testing][value=" + value + "]").prop('checked', true);
        }
        let
        dependency = $(a).attr('dependency_');
        var value = dependency;
        if (value !== null) {
            $("input[name=radio_dependency][value=" + value + "]").prop('checked', true);
        }


        $('#editid').val(id);
        $('#redmine_no').val(redmineno);
        $("#status").val(redmine_status);
        $("#deploystatus").val(deploy_status);
        $("#teststatus").val(test_status);
        $("#moduletest").val(module);
        $("#raised").val(raised);
        $("#date1").val(date);
        $("#developerid").val(developer);
        $("#assigneeid").val(assignee);
        $("#eddid").val(edd);
        $("#typedefectid").val(type_id);
        $("#Subject").val(subject);
        $("#tester").val(tester);
        $("#remarks").val(remark);
        $("#counttestDate").val(count_date);
    }
    ;

    $(".editbutton").on("click", function () {
        editButtonTriggered(this);
    });

    $(function () {
        function dropdown() {
            var getstatus = $('#findbystatus').val();
            var getteststatus2 = $('#findteststatus').val();
            var getstatusdeploy = $('#finddeployment').val();
            var getteststatus;
            var getstatusdeploy2;
            if (getteststatus2 === "Reopen") {
                getteststatus = 'Reopen';
            } else if (getteststatus2 === "Closed") {
                getteststatus = 'Closed';
            } else if (getteststatus2 === "In Progress") {
                getteststatus = 'In Progress';
            } else if (getteststatus2 === "Pass") {
                getteststatus = 'Pass';
            } else
                getteststatus = "ALL";

            if (getstatusdeploy === 'Pending Fixing') {
                getstatusdeploy2 = 'Pending Fixing';
            } else if (getstatusdeploy === 'Pending Deployment') {
                getstatusdeploy2 = 'Pending Deployment';
            } else if (getstatusdeploy === 'Ready for Testing') {
                getstatusdeploy2 = 'Ready for Testing';
            } else
                getstatusdeploy2 = 'ALL';

            if (getstatus !== null) {
                if (getstatus === "") {
                    getstatus = 'ALL';
                }
                loadData(getstatus, getteststatus, getstatusdeploy2)
            }
        }
        ;
        $(".findstatus").on("change", '#findbystatus', function () {
            dropdown();
        });

        $(".findbytest").on("change", '#findteststatus', function () {
            dropdown();
        });

        $(".findbydeployment").on("change", '#finddeployment', function () {
            dropdown();
        });

        $(".findbytestclosed").on("change", '#findteststatusclosed', function () {

            var getteststatus2 = $('#findteststatusclosed').val();
            var getteststatus;
            if (getteststatus2 === "Reopen") {
                getteststatus = 'Reopen';
            }
            else if (getteststatus2 === "Closed") {
                getteststatus = 'Closed';
            }
            else if (getteststatus2 === "In Progress") {
                getteststatus = 'In Progress';
            }
            else if (getteststatus2 === "Pass") {
                getteststatus = 'Pass';
            }
            else
                getteststatus = "ALL";
            loadDataClosed(getteststatus)
        });

        function loadData(getstatus, getteststatus, getstatusdeploy2) {
            $.ajax({
                type: 'GET',
                url: APP_URL + '/prod-support/defect_testing/status/' + getstatus + '/' + getteststatus + '/' + getstatusdeploy2,
                contentType: "text/plain",
                dataType: 'json',
                success: function (data) {
                    myJsonData = data;
                    if (jQuery.isEmptyObject(myJsonData)) {
                        $("#defect_testing-datatable").DataTable().clear();
                        var redmine_date = null;
                        var redmine_no = null;
                        var module = null;
                        var redmine_status = null;
                        var b_test_status = null;
                        var deploy_status = null;
                        var subject = 'No macthing record found';
                        var edd = null;
                        var test_by = null;
                        var countDateTest = null;
                        var action = null;
                        var urgent_test = null;
                        var dependency = null;
                        $('#defect_testing-datatable').dataTable().fnAddData([
                            redmine_date,
                            redmine_no,
                            module,
                            redmine_status,
                            b_test_status,
                            deploy_status,
                            subject,
                            edd,
                            test_by,
                            countDateTest,
                            action,
                            urgent_test,
                            dependency
                        ]);
                    } else
                    {
                        populateDataTable(myJsonData);
                    }
                },
                error: function (e) {
                    console.log("There was an error with your request...");
                    console.log("error: " + JSON.stringify(e));
                }
            });
        }

        function loadDataClosed(getteststatus) {
            $.ajax({
                type: 'GET',
                url: APP_URL + '/prod-support/defect_testing/status_closed/' + getteststatus,
                contentType: "text/plain",
                dataType: 'json',
                success: function (data) {
                    myJsonData = data;
                    if (jQuery.isEmptyObject(myJsonData)) {
                        $("#defect_testing_closed-datatable").DataTable().clear();
                        var redmine_date = null;
                        var redmine_no = null;
                        var module = null;
                        var redmine_status = null;
                        var b_test_status = null;
                        var deploy_status = 'No macthing record found';
                        var subject = null;
                        var edd = null;
                        var test_by = null;
                        var countDateTest = null;
                        var action = null;
                        $('#defect_testing_closed-datatable').dataTable().fnAddData([
                            redmine_date,
                            redmine_no,
                            module,
                            redmine_status,
                            b_test_status,
                            deploy_status,
                            subject,
                            edd,
                            test_by,
                            countDateTest,
                            action
                        ]);
                    } else
                    {
                        populateDataTableClosed(myJsonData);
                    }
                },
                error: function (e) {
                    console.log("There was an error with your request...");
                    console.log("error: " + JSON.stringify(e));
                }
            });
        }

        // populate the data table with JSON data
        function populateDataTable(data) {
            // clear the table before populating it with more data
            $("#defect_testing-datatable").DataTable().clear();
            $("#defect_testing_closed-datatable").DataTable().clear();

            var length = Object.keys(data).length;
            var countDateTest = "";
            var countDateTest3 = "";
            for (var i = 0; i < length + 1; i++) {
                var defect_id = (data[i]['defect_id'] === null) ? "" : data[i]['defect_id'];
                var redmine_date2 = (data[i]['redmine_date'] === null) ? "" : data[i]['redmine_date'];
                var redmine_date = '<div class="text-center">' + redmine_date2 + '</div>'
                var redmine_no2 = (data[i]['redmine_no'] === null) ? "" : data[i]['redmine_no'];
                var redmine_no = '<div class="text-center">' + redmine_no2 + '</div>'
                var module2 = (data[i]['module'] === null) ? "" : data[i]['module'];
                var module = '<div class="text-center">' + module2 + '</div>'
                var redmine_status2 = (data[i]['redmine_status'] === null) ? "" : data[i]['redmine_status'];
                var redmine_status = '<div class="text-center">' + redmine_status2 + '</div>'
                var test_status2 = data[i]['test_status'];
                var b_test_status2;
                if (test_status2 === "Reopen") {
                    b_test_status2 = '<a class="label label-danger">Reopen</a>';
                }
                else if (test_status2 === "Closed") {
                    b_test_status2 = '<a class="label label-info">Closed</a>';
                }
                else if (test_status2 === "In Progress") {
                    b_test_status2 = '<a class="label label-warning">In Progress</a>';
                }
                else if (test_status2 === "Pass") {
                    b_test_status2 = '<a class="label label-success">Pass</a>';
                }
                else
                    b_test_status2 = '';
                var b_test_status = '<div class="text-center">' + b_test_status2 + '</div>'
                var test_status = (data[i]['test_status'] === null) ? "" : data[i]['test_status'];
                var deploy_status2 = (data[i]['deploy_status'] === null) ? "" : data[i]['deploy_status'];
                var deploy_status = '<div class="text-center">' + deploy_status2 + '</div>'
                var test_by2 = (data[i]['test_by'] === null) ? "" : data[i]['test_by'];
                var test_by = '<div class="text-center">' + test_by2 + '</div>'
                var raised_by = (data[i]['raised_by'] === null) ? "" : data[i]['raised_by'];
                var edd2 = (data[i]['edd'] === null) ? "" : data[i]['edd'];
                var edd = '<div class="text-center">' + edd2 + '</div>'
                var subject2 = (data[i]['subject'] === null) ? "" : data[i]['subject'];
                var subject = '<div class="text-center">' + subject2 + '</div>'
                var type_defect = (data[i]['type_defect'] === null) ? "" : data[i]['type_defect'];
                var assignee = (data[i]['assignee'] === null) ? "" : data[i]['assignee'];
                var developer = (data[i]['developer'] === null) ? "" : data[i]['developer'];
                var remarks = (data[i]['test_remarks'] === null) ? "" : data[i]['test_remarks'];
                var countDateTest1 = (data[i]['counttest'] !== 0) ? data[i]['counttest'] : "";
                var countDateTest2 = (data[i]['counttest'] !== 0) ? data[i]['testCount'] : "";
                
                $.each(countDateTest2, function (k, v) {
                    countDateTest3 += (data[i]['counttest'] !== 0) ? (v.created_by + ' (' + v.Testing + ')' + '<br>') : "";
                    
                });
                
                countDateTest = '<div class="text-center">' + countDateTest3 + '</div>'
                var urgent_test = (data[i]['urgent_testing'] === null) ? "" : data[i]['urgent_testing'];
                var dependency = (data[i]['dependency'] === null) ? "" : data[i]['dependency'];
                var action2 = '<a idno = "' + defect_id + '" date ="' + redmine_date2 + '" redmine_status="' + redmine_status2 + '" redmine_no="' + redmine_no2 + '" module = "' + module2 + '" test_status="' + test_status + '" \n\
                                    deploy_status = "' + deploy_status2 + '" raised_by = "' + raised_by + '" edd = "' + edd2 + '" subject="' + subject2 + '" type="' + type_defect + '" assignee="' + assignee + '" developer="' + developer + '" \n\
                                    test_by = "' + test_by2 + '" remarks="' + remarks + '" count_date="' + countDateTest1 + '" urgent_testing="' + urgent_test + '" dependency_="' + dependency + '" \n\
                                    data-toggle="tooltip" onClick= "editButtonTriggered(this);" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>'
                var action = '<div class="text-center">' + action2 + '</div>'
                // You could also use an ajax property on the data table initialization
                $('#defect_testing-datatable').dataTable().fnAddData([
                    redmine_date,
                    redmine_no,
                    module,
                    redmine_status,
                    b_test_status,
                    deploy_status,
                    subject,
                    edd,
                    test_by,
                    countDateTest,
                    action,
                    urgent_test,
                    dependency
                ]);
                
                countDateTest = "";
                countDateTest3 = "";
            }
        }

        function populateDataTableClosed(data) {
            // clear the table before populating it with more data
            $("#defect_testing_closed-datatable").DataTable().clear();

            var length = Object.keys(data).length;
            var countDateTest = "";
            var countDateTest3 = "";
            for (var i = 0; i < length + 1; i++) {
                var defect_id = (data[i]['defect_id'] === null) ? "" : data[i]['defect_id'];
                var redmine_date2 = (data[i]['redmine_date'] === null) ? "" : data[i]['redmine_date'];
                var redmine_date = '<div class="text-center">' + redmine_date2 + '</div>'
                var redmine_no2 = (data[i]['redmine_no'] === null) ? "" : data[i]['redmine_no'];
                var redmine_no = '<div class="text-center">' + redmine_no2 + '</div>'
                var module2 = (data[i]['module'] === null) ? "" : data[i]['module'];
                var module = '<div class="text-center">' + module2 + '</div>'
                var redmine_status2 = (data[i]['redmine_status'] === null) ? "" : data[i]['redmine_status'];
                var redmine_status = '<div class="text-center">' + redmine_status2 + '</div>'
                var test_status2 = data[i]['test_status'];
                var b_test_status2;
                if (test_status2 === "Reopen") {
                    b_test_status2 = '<a class="label label-danger">Reopen</a>';
                }
                else if (test_status2 === "Closed") {
                    b_test_status2 = '<a class="label label-info">Closed</a>';
                }
                else if (test_status2 === "In Progress") {
                    b_test_status2 = '<a class="label label-warning">In Progress</a>';
                }
                else if (test_status2 === "Pass") {
                    b_test_status2 = '<a class="label label-success">Pass</a>';
                }
                else
                    b_test_status2 = null;
                var b_test_status = '<div class="text-center">' + b_test_status2 + '</div>'
                var test_status = (data[i]['test_status'] === null) ? "" : data[i]['test_status'];
                var deploy_status2 = (data[i]['deploy_status'] === null) ? "" : data[i]['deploy_status'];
                var deploy_status = '<div class="text-center">' + deploy_status2 + '</div>'
                var test_by2 = (data[i]['test_by'] === null) ? "" : data[i]['test_by'];
                var test_by = '<div class="text-center">' + test_by2 + '</div>'
                var raised_by = (data[i]['raised_by'] === null) ? "" : data[i]['raised_by'];
                var edd2 = (data[i]['edd'] === null) ? "" : data[i]['edd'];
                var edd = '<div class="text-center">' + edd2 + '</div>'
                var subject2 = (data[i]['subject'] === null) ? "" : data[i]['subject'];
                var subject = '<div class="text-center">' + subject2 + '</div>'
                var type_defect = (data[i]['type_defect'] === null) ? "" : data[i]['type_defect'];
                var assignee = (data[i]['assignee'] === null) ? "" : data[i]['assignee'];
                var developer = (data[i]['developer'] === null) ? "" : data[i]['developer'];
                var remarks = (data[i]['test_remarks'] === null) ? "" : data[i]['test_remarks'];
                var countDateTest1 = (data[i]['counttest'] !== 0) ? data[i]['counttest'] : "";
                var countDateTest2 = (data[i]['counttest'] !== 0) ? data[i]['testCount'] : "";
                $.each(countDateTest2, function (k, v) {
                    countDateTest3 += (data[i]['counttest'] !== 0) ? (v.created_by + ' (' + v.Testing + ')' + '<br>') : "";
                    
                });
                
                countDateTest = '<div class="text-center">' + countDateTest3 + '</div>'
                var action2 = '<a idno = "' + defect_id + '" date ="' + redmine_date2 + '" redmine_status="' + redmine_status2 + '" redmine_no="' + redmine_no2 + '" module = "' + module2 + '" test_status="' + test_status + '" \n\
                                    deploy_status = "' + deploy_status2 + '" raised_by = "' + raised_by + '" edd = "' + edd2 + '" subject="' + subject2 + '" type="' + type_defect + '" assignee="' + assignee + '" developer="' + developer + '" \n\
                                    test_by = "' + test_by2 + '" remarks="' + remarks + '" count_date="' + countDateTest1 + '"\n\
                                    data-toggle="tooltip" onClick= "editButtonTriggered(this);" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>'
                var action = '<div class="text-center">' + action2 + '</div>'
                // You could also use an ajax property on the data table initialization
                $('#defect_testing_closed-datatable').dataTable().fnAddData([
                    redmine_date,
                    redmine_no,
                    module,
                    redmine_status,
                    b_test_status,
                    deploy_status,
                    subject,
                    edd,
                    test_by,
                    countDateTest,
                    action
                ]);
                
                countDateTest = "";
                countDateTest3 = "";
            }
        }
    });
</script>
@endsection        

