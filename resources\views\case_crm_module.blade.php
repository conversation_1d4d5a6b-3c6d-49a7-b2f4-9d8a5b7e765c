@extends('layouts.guest-dash')

@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }
    .table tbody > tr > td {
        font-size: 10px;
    }
</style>   

<div class="block block-alt-noborder full">
    <div class="block">     
        <div class="table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><strong>Total Cases Resolved By Module And User</strong>
                </h1>
            </div>  
            <table id="action-log-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Case Number</th>
                        <th class="text-center">Subject</th>
                        <th class="text-center">Module</th>
                        <th class="text-center">Resolved By</th>
                        <th class="text-center">Resolved Date</th> 
                    </tr>
                </thead>
                <tbody>
                    @foreach($list as $data)
                    <tr>
                        <td class="text-center">{{ $data->case_number }}</td>
                        <td class="text-left">{{ $data->name }}</td>
                        <td class="text-center">{{ $data->sub_category_desc_c }}</td>
                        <td class="text-center">{{ $data->first_name }}</td> 
                        <td class="text-center">{{ $data->task_date_entered }}</td> 
                    </tr>
                    @endforeach
                </tbody>
            </table>    
        </div>  

    </div>
</div>

@include('_shared._modalListLogAction')

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script>
    App.datatables();
</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
        ModalListActionLogDatatable.init();
    });</script> 


@endsection



