@extends('layouts.guest-dash')


@section('content')
<style>
    .dropzone {
        margin-bottom: 20px;
    }
    .dropzone .dz-message {
        margin : 5px 0;
    }
    .dropzone .dz-preview {
        display: block;
    }
    .dropzone .dz-preview .dz-image{
        display:none;
    }
</style>

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Upload MBB Report PDF <br>
            <small  class="text-info">Upload file for <span><strong>.PDF</strong></span> only. Please keep as original name file. </small>
            <br /><small  class="text-warning">Once file is uploaded. App-Converter will be schedule every minute to convert PDF into Excel. 
                App-Conveter will extract data excel to insert in DB-table. The transaction will be inserted if meet the requirement.   </small>
        </h1>
    </div>

</div>

<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title">
            <h2><strong>Upload File</strong></h2>
            <div class="block-options pull-right  action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " data-toggle="modal" 
                    data-url="{{url('support/report/log/upload-mbb-file')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                    data-title="List Action Upload File Today ">View Today Action</a>
            </div>

        </div>
        

        <form id="form-transfer-file" action="{{url("/app-support/file/upload-mbb")}}" method="post" class=" dropzone dz-clickable" >
            {{ csrf_field() }}
            <input name="_method" id="_method"  type="hidden" value="POST">
            <div class="dz-default dz-message"><span>Drop file here to upload</span></div>
        </form>

        <div class="row">
            <div class="col-lg-12">
                <div class="widget">
                    <div class="widget-extra themed-background-dark">
                        <h5 class="widget-content-light">
                             <strong>Pending Convertion PDF to EXCEL</strong>
                        </h5>
                    </div>
                    <div id="dash_monitoring" class="widget-extra-full">
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                                <thead> <tr>
                                    <th>Filename</th>
                                </tr></thead>
                               <tbody>
                                @foreach ($data as $filename)
                                <tr>
                                    <td>{{$filename}}</td>
                                </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="block" id="panel_file_detail" style="display:none;">
        <div class="block-title" >
            <h1><i class="fa fa-file-text"></i> <strong>File Results</strong></h1>
        </div>

        <h6 class="text-primary bold" id="file-upload-name" ></h6>

    </div>

    
    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    @include('_shared._modalListLogAction')
</div>
@if($data && count($data) > 0)
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    //App.datatables();
    /* Initialize Datatables */
    var tableListData =     $('#basic-datatable').DataTable({
            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
            pageLength: 10,
            lengthMenu: [[10, -1], [10, 'All']]
    });
    
    //form-transfer-file
    Dropzone.options.formTransferFile = {
        paramName: "uploadFile", // The name that will be used to transfer the file
        maxFilesize: 20, // MB
        maxFiles: 100,
        acceptedFiles: '.PDF',
        accept: function (file, done) {
            console.log('File Name: ', file.name);
            done();
        },
        success: function (file, response) {
            $('#panel_transfer_file').hide();
            $('#panel_file_detail').hide();
            if (response && response.status_upload === 'success') {
                tableListData.destroy();
                $('#panel_file_detail').show();
                $('#file-upload-name').append("<p>Succesfully uploaded "+response.file_name+"</p>");

                $('#basic-datatable').html(response.list_pending_html).fadeIn();
                        
                /* Re-Initialize Datatable */
                tableListData = $('#basic-datatable').DataTable({
                    columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                    pageLength: 10,
                    lengthMenu: [[10, -1], [10, 'All']]
                });

            }else{
                alert('Please make sure you upload a correct file.');
            }
        }
    };
    
    
</script>    


@endsection
