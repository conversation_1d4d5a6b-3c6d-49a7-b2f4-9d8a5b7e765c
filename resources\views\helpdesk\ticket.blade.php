@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<style>

    .tab {
        overflow: hidden;
        border: 1px solid #ccc;
        background-color: #f1f1f1;
    }

    /* Style the buttons that are used to open the tab content */
    .tab button {
        background-color: inherit;
        float: left;
        border: none;
        outline: none;
        cursor: pointer;
        padding: 14px 16px;
        transition: 0.3s;
    }

    /* Change background color of buttons on hover */
    .tab button:hover {
        background-color: #ddd;
    }

    /* Create an active/current tablink class */
    .tab button.active {
        background-color: #ccc;
    }

    /* Style the tab content */
    .tabcontent {
        display: none;
        padding: 6px 12px;
        border: 1px solid #ccc;
        border-top: none;
    }
</style>

<div class="block block-alt-noborder full">
    <div class="block">
        <form id="form-search-task" action="{{url("/helpdesk/ticket")}}" method="get" class="form-horizontal form-bordered">
            <div class="col-md-8">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-3 control-label" for="ticket_number">Ticket Number : <span class="text-danger">*</span></label>
                        <div class="col-md-9">
                            <input type="text" id="ticket_number" name="ticket_number" required class="form-control" value="{{old('ticket_number')}}">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="form-actions form-actions-button text-right" style="margin-right:30px;">
                <br/><button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
            </div>  <br/>
        </form>
    </div>
    <div id="Ticket">
        <div class="row">
            <div class="col-md-6"> 
                @if(isset($data) && count($data) > 0)
                <div class="block">
                <p><b> Ticket {{$data[0]->number}} | {{$data[0]->subject}} </b></p>
                    <div id="response" class="table-options clearfix display-none">
                        <div id="response-msg" class="text-center text-light" colspan="6"></div>
                    </div>
                    <div class="table-responsive">
                        <table id="basic-datatable" class="table table table-vcenter table-striped">
                            <tbody>
                                <tr>
                                    <th class="text-left">Ticket ID</th>
                                    <td class="text-left">{{$data[0]->ticket_id}}</td>
                                </tr> 
                                <tr>
                                    <th class="text-left">Status</th>
                                    <td class="action_td_status">
                                        <a class="action_status"  title="" data-original-title="Update Ticket"
                                               href="#myModal" data-toggle="modal" data-id="{{ $data[0]->ticket_id }}" 
                                               data-status="{{ $data[0]->statusname }}"
                                               data-number="{{ $data[0]->number }}">{{ $data[0]->statusname }}</a>
                                    </td>
                                </tr> 
                                <tr>
                                    <th class="text-left">User</th>
                                    <td class="text-left">{{$data[0]->name}}</td>
                                </tr>
                                <tr>
                                    <th class="text-left">Department</th>
                                    <td class="text-left">{{$data[0]->deptname}}</td>
                                </tr>
                                <tr>
                                    <th class="text-left">Create Date</th>
                                    <td class="text-left">{{$data[0]->created}}</td>
                                </tr>
                                <tr>
                                    <th class="text-left">Help Topic</th>                                        <td class="text-left">{{$data[0]->topic1}} / {{$data[0]->topic2}} / {{$data[0]->topic3}}</td>
                                </tr>
                                <tr>
                                    <th class="text-left">Subject</th>
                                    <td class="action_td_subject">
                                        <a class="action_subject"  title="" data-original-title="Update Ticket"
                                            href="#myModalSubject" data-toggle="modal" data-id="{{ $data[0]->ticket_id }}" 
                                            data-subject="{{ $data[0]->subject }}"
                                            data-number="{{ $data[0]->number }}">{{ $data[0]->subject }}</a>
                                    </td>
                                </tr>
                                <tr>
                                    <th class="text-left">Reason</th>
                                    <td class="text-left">{!! $data[0]->reason !!}</td>
                                </tr>
                                <tr>
                                    <th class="text-left">Close Date</th>
                                    <td class="text-left">{{$data[0]->updated}}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif 
            </div>
            <div class="col-md-6">  
            @if(isset($threadActivity) && count($threadActivity) > 0)
            <div class="block">
            <p><b> Thread Activities </b></p>
                <div id="responseThread" class="table-options clearfix display-none">
                    <div id="response-msg-thread" class="text-center text-light" colspan="6"></div>
                </div>
                <input type="hidden" id="tid" name="tid" value="{{$threadActivity[0]->ticket_id}}"/>
                <div>
                    @foreach ($threadActivity as $thread)
                    <div class="block thread-entry" id="{{$thread->entry_id}}" style="background-color: #E5CBB4">
                        <div>
                            <b> {{$thread->poster}} </b> posted on {{$thread->created}} 
                            <span class="pull-right">
                                <a class="delete_thread"  title="" data-original-title="Delete Thread Entry"
                                    href="#modalDeleteThread" data-toggle="modal" data-id="{{ $thread->ticket_id }}" 
                                    data-threadid="{{ $thread->thread_id }}"
                                    data-entryid="{{ $thread->entry_id }}"
                                    data-staffid="{{ $thread->staff_id }}"
                                    data-entrydata="{!! $thread->body !!}"><i class="fa fa-trash"></i>
                                </a>
                            </span>
                        </div>
                        <div>
                            <a class="action_tbody"  title="" data-original-title="Update Ticket"
                                href="#myModalTbody" data-toggle="modal" data-id="{{ $thread->entry_id }}" 
                                data-body="{{ $thread->body }}" data-ticketnumber="{{ $thread->number }}">{!! $thread->body !!}
                            </a> 
                        </div> 
                    </div>
                    <?php 
                    $helpdeskService = new App\Services\Helpdesk\HelpdeskService();
                    $event = $helpdeskService->getEventPerEntry($thread->entry_id, $thread->thread_id); 
                    foreach ($event as $ev) {
                        $data = $ev->data;
                        $dataBody = 'Create on ';
                        $dataValue = '';
                        if(isset($data)) {
                            $eventData = explode(':', str_replace('}', '', $data));
                            $eventDataVal = $eventData[1]; 
                            if((strpos($data, 'team') !== FALSE)){ 
                                $team = $helpdeskService->getTeamDetail($eventDataVal);
                                if(isset($team)) {
                                    $dataBody  = 'assigned this to ';
                                    $dataValue = $team->name;
                                } 
                            } else if((strpos($data, 'status') !== FALSE)) {
                                if(strpos($eventDataVal, '[') !== FALSE){
                                    $eventDataVals = explode(',', str_replace('[', '', $eventDataVal));
                                    $eventDataVal = str_replace("'", '', $eventDataVals[0]);
                                    $dataBody = 'Closed with status of ';
                                } else {
                                    $dataBody = ' changed the status to ';
                                }
                                $status = $helpdeskService->getStatusDetail($eventDataVal);
                                
                                if(isset($status)) {
                                    $dataValue = $status->name;
                                }
                            }
                        }
                        ?>
                        <div class="thread-event" id="{{$ev->event_id}}">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        @if($ev->username !== 'SYSTEM')    
                            <b> {{$ev->username}} </b> {{$dataBody}} 
                            <b> {{$dataValue}} </b> {{$ev->created}} 
                            <span>
                                <a class="delete_event"  title="" data-original-title="Delete Thread Event"
                                    href="#modalDeleteEvent" data-toggle="modal" data-ids="{{ $thread->ticket_id }}" 
                                    data-threadids="{{ $thread->thread_id }}"
                                    data-entryids="{{ $thread->entry_id }}"
                                    data-eventids="{{ $ev->event_id }}"
                                    data-staffids="{{ $ev->event_staff_id }}"
                                    data-eventdatas="{{$ev->username}} {{$dataBody}} {{$dataValue}} {{$ev->created}}"><i class="fa fa-trash"></i>
                                </a>
                            </span>
                        @endif
                        </div>  <br/>
                        
                        <?php   
                    }?>
                                    
                    @endforeach  
                    <!-- <div class="form-group form-actions">
                        <button type="button" class="btn btn-sm btn-primary add_thread"> ADD THREAD </button>
                    </div> -->
                </div>
            </div>
            @endif 
            </div>
        </div>
    </div> 
</div>
<div id="myModal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title" id="ticket_number_display"></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <fieldset>
                                    <label class="col-md-3 control-label">Ticket ID</label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="input_ticket_id" value="" />
                                        <p id="ticket_id_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <input type="hidden" id="input_ticket_status" value="" />
                                    <label class="col-md-3 control-label">Current Status</label>
                                    <div class="col-md-9">
                                        <p id="ticket_status_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Change Status <span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <select id="list_status" name="list_status" class="form-control select">
                                            @if(isset($list_status))
                                            <option value=""></option>
                                            @foreach($list_status as $value)
                                            <option value="{{$value->id}}" @if($value->id == old('list_status') ) selected @endif>{{$value->name}}</option>
                                            @endforeach
                                            @endif
                                        </select>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_status"><i class="gi gi-ok_2"></i> Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="myModalSubject" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title" id="sticket_number_display"></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <fieldset>
                                    <label class="col-md-3 control-label">Ticket ID</label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="input_ticket_id" value="" />
                                        <input type="hidden" id="input_ticket_number" value="" />
                                        <p id="sticket_id_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <input type="hidden" id="input_ticket_subject" value="" />
                                    <label class="col-md-3 control-label">Current Subject</label>
                                    <div class="col-md-9">
                                        <p id="ticket_subject_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Change Subject <span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <input class="form-control" id="update_subject" value="" required="true"/>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_subject"><i class="gi gi-ok_2"></i> Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modalThread" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <fieldset>
                                    <label class="col-md-3 control-label">Thread <span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <textarea id="add_thread" name="add_thread" style="width:95%;height:90%;"></textarea>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_add_thread"><i class="gi gi-ok_2"></i> Add</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="myModalTbody" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title">UPDATE THREAD ENTRY</h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <fieldset>
                                    <label class="col-md-3 control-label">Entry ID</label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="input_thread_id" value="" />
                                        <p id="entry_id_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <input type="hidden" id="input_current_tbody" value="" />
                                    <label class="col-md-3 control-label">Current Body</label>
                                    <div class="col-md-9">
                                        <p id="thread_body_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Change Body <span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <textarea id="update_thread_body" name="update_thread_body" style="width:95%;height:90%;" required="true"></textarea>
            
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_tbody"><i class="gi gi-ok_2"></i> Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modalDeleteThread" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title">DELETE THREAD ENTRY</h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }} 
                            <div class="form-group">
                            <center><h5 style="color:red"><b>THIS ACTION WILL PERMANENTLY DELETE DATA FROM DATABASE. PLEASE BACKUP FIRST!!</b></h5></center>
                                <fieldset>
                                    <label class="col-md-3 control-label">Ticket ID</label>
                                    <div class="col-md-9">
                                        <p id="delete_ticketid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Thread ID</label>
                                    <div class="col-md-9">
                                        <p id="delete_threadid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Entry ID</label>
                                    <div class="col-md-9">
                                        <p id="delete_entryid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Entry Data</label>
                                    <div class="col-md-9">
                                        <p id="delete_entrydata_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Staff</label>
                                    <div class="col-md-9">
                                        <p id="delete_staffid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_delete_entry"><i class="gi gi-ok_2"></i> Delete</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modalDeleteEvent" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title">DELETE THREAD EVENT</h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                            <center><h5 style="color:red"><b>THIS ACTION WILL PERMANENTLY DELETE DATA FROM DATABASE. PLEASE BACKUP FIRST!!</b></h5></center>
                                <fieldset>
                                    <label class="col-md-3 control-label">Ticket ID</label>
                                    <div class="col-md-9">
                                        <p id="event_ticketid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Thread ID</label>
                                    <div class="col-md-9">
                                        <p id="event_threadid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Entry ID</label>
                                    <div class="col-md-9">
                                        <p id="event_entryid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Event ID</label>
                                    <div class="col-md-9">
                                        <p id="event_eventid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Event Data</label>
                                    <div class="col-md-9">
                                        <p id="event_eventdata_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                                <fieldset>
                                    <label class="col-md-3 control-label">Staff ID</label>
                                    <div class="col-md-9">
                                        <p id="event_staffid_display" class="form-control-static"></p>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_delete_event"><i class="gi gi-ok_2"></i> Delete</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script type="text/javascript" language="JavaScript">
    function openTicket(evt, cityName) {
        // Declare all variables
        var i, tabcontent, tablinks;

        // Get all elements with class="tabcontent" and hide them
        tabcontent = document.getElementsByClassName("tabcontent");
        for (i = 0; i < tabcontent.length; i++) {
            tabcontent[i].style.display = "none";
        }

        // Get all elements with class="tablinks" and remove the class "active"
        tablinks = document.getElementsByClassName("tablinks");
        for (i = 0; i < tablinks.length; i++) {
            tablinks[i].className = tablinks[i].className.replace(" active", "");
        }

        // Show the current tab, and add an "active" class to the button that opened the tab
        document.getElementById(cityName).style.display = "block";
        evt.currentTarget.className += " active";

    }
    //default open tab
    document.getElementById("defaultOpen").click();
</script>
<script type="text/javascript" language="JavaScript">
    $('td.action_td_status').on("click", 'a.action_status', function () {
        var ticketId = $(this).attr('data-id');
        var ticketNumber = $(this).attr('data-number');
        var ticketStatus = $(this).attr('data-status');
        $("#ticket_number_display").text('#' + ticketNumber);
        $("#ticket_status_display").text(ticketStatus);
        $("#ticket_id_display").text(ticketId);
        $("#input_ticket_id").val(ticketId);
        $("#input_ticket_status").val(ticketStatus);
        $("#input_ticket_number").val(ticketNumber);
    });
    $('button.action_update_status').on("click", function () {

        var ticketId = $("#input_ticket_id").val();
        var currentTicketStatus = $("#input_ticket_status").val();
        var updateTicketStatusId = $('#list_status option:selected').val();
        var updateTicketStatusName = $('#list_status option:selected').text();
        var ticketNumber = $("#input_ticket_number").val();
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/helpdesk/updateticket/" + ticketId,
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "current_status": currentTicketStatus,
                "update_status_id": updateTicketStatusId, "update_status_name": updateTicketStatusName,
                "ticket_number": ticketNumber},
            context: document.body
        }).done(function (resp) {
            $('#myModal').modal('hide');
            if (resp.status === 'Success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Ticket Updated Successfully!");
                $("td.td-" + ticketId).addClass("text-success");
                $("td.td-" + ticketId).html(resp.value);
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Ticket Failed To Update!");
                $("td.td-" + ticketId).addClass("text-danger");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
    $('td.action_td_subject').on("click", 'a.action_subject', function () {
        var ticketId = $(this).attr('data-id');
        var ticketNumber = $(this).attr('data-number');
        var ticketSubject = $(this).attr('data-subject');
        console.log(ticketId);
        $("#sticket_number_display").text('#' + ticketNumber);
        $("#ticket_subject_display").text(ticketSubject);
        $("#sticket_id_display").text(ticketId);
        $("#input_ticket_id").val(ticketId);
        $("#input_ticket_subject").val(ticketSubject);
    });
    $('button.action_update_subject').on("click", function () {

        var ticketId = $("#input_ticket_id").val();
        var currentTicketSubject = $("#input_ticket_subject").val();
        var updateSubject = $("#update_subject").val();
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/helpdesk/updateticketsubject/" + ticketId,
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "current_subject": currentTicketSubject,
                "update_subject": updateSubject},
            context: document.body
        }).done(function (resp) {
            $('#myModalSubject').modal('hide');
            if (resp.status === 'Success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Ticket Updated Successfully!");
                $("td.td-" + ticketId).addClass("text-success");
                $("td.td-" + ticketId).html(resp.value);
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Ticket Failed To Update!");
                $("td.td-" + ticketId).addClass("text-danger");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
    $('button.add_thread').on("click", function () {
        $('#modalThread').modal('show');

        $('button.action_add_thread').on("click", function () {
            var ticketId = $("#tid").val();
            var thread = $("#add_thread").val();
            var csrf = $("input[name=_token]").val();

            $.ajax({
                url: "/helpdesk/addentry/" + ticketId,
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "thread": thread},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Success Add New Thread Activities");
                    $("td.td-" + ticketId).addClass("text-success");
                    $("td.td-" + ticketId).html(resp.value);
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("New Thread Entry Failed To Add!");
                    $("td.td-" + ticketId).addClass("text-danger");
                }
                setTimeout(location.reload.bind(location), 1000);
                document.getElementById("tactivities").click();
            });

            $('#modalThread').modal('hide');
        });


    });
    $('div.thread-entry').on("click", 'a.action_tbody', function () {
        var entryId = $(this).attr('data-id');
        var threadBody = $(this).attr('data-body');
        var ticketNumber = $(this).attr('data-ticketnumber');
        $("#entry_id_display").text(entryId);
        $("#thread_body_display").html(threadBody);
        $("#input_thread_id").val(entryId);
        $("#thread_tnumber_display").text(ticketNumber);
        $("#input_current_tbody").val(threadBody);
    }); 
    $('button.action_update_tbody').on("click", function () {

        var entryId = $("#input_thread_id").val();
        var currentBody = $("#input_current_tbody").val();
        var updateBody = $("#update_thread_body").val();
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/helpdesk/updatethreadentry/" + entryId,
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "currentBody": currentBody,
                "updateBody": updateBody},
            context: document.body
        }).done(function (resp) {
            $('#myModalTbody').modal('hide');
            if (resp.status === 'Success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Thread Entry Updated Successfully!");
                $("td.td-" + entryId).addClass("text-success");
                $("td.td-" + entryId).html(resp.value);
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Thread Entry Failed To Update!");
                $("td.td-" + entryId).addClass("text-danger");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    }); 
    $('div.thread-entry').on("click", 'a.delete_thread', function () {
        var ticketId = $(this).attr('data-id');
        var threadId = $(this).attr('data-threadid');
        var entryId = $(this).attr('data-entryid'); 
        var entryData = $(this).attr('data-entrydata'); 
        var staffId = $(this).attr('data-staffid'); 
        $("#delete_ticketid_display").text(ticketId);
        $("#delete_threadid_display").text(threadId);
        $("#delete_entryid_display").text(entryId); 
        $("#delete_entrydata_display").text(entryData);
        $("#delete_staffid_display").text(staffId);
    }); 
    $('button.action_delete_entry').on("click", function () {
        var ticketId = $("#delete_ticketid_display").text();
        var threadId = $("#delete_threadid_display").text();
        var entryId = $("#delete_entryid_display").text();
        var entryData = $("#delete_entrydata_display").text();
        var staffId = $("#delete_staffid_display").text();
        console.log('ticketId ' + ticketId);
        console.log('threadId ' + threadId);
        console.log('entryId ' + entryId);
        console.log('entryData ' + entryData);
        console.log('staffId ' + staffId);
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/helpdesk/deleteThreadEntry/",
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "ticketId": ticketId, "threadId": threadId, 
                "entryId": entryId, "entryData": entryData, "staffId": staffId},
         }).done(function (resp) {
            $('#modalDeleteThread').modal('hide');
            if (resp.status === 'Success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Thread Entry Deleted Successfully!"); 
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Thread Entry Failed To Delete!"); 
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
    $('div.thread-event').on("click", 'a.delete_event', function () {
        var ticketId = $(this).attr('data-ids');
        var entryId = $(this).attr('data-entryids'); 
        var eventId = $(this).attr('data-eventids'); 
        var eventData = $(this).attr('data-eventdatas');
        var threadId = $(this).attr('data-threadids'); 
        var staffId = $(this).attr('data-staffids'); 
        console.log('ticketId ' + ticketId); 
        $("#event_ticketid_display").text(ticketId);
        $("#event_entryid_display").text(entryId); 
        $("#event_eventid_display").text(eventId);
        $("#event_eventdata_display").text(eventData);
        $("#event_threadid_display").text(threadId);
        $("#event_staffid_display").text(staffId);
    }); 
    $('button.action_delete_event').on("click", function () {
        var ticketId = $("#event_ticketid_display").text();
        var entryid = $("#event_entryid_display").text();
        var eventId = $("#event_eventid_display").text();
        var eventData = $("#event_eventdata_display").text();
        var threadid = $("#event_threadid_display").text();
        var staffId = $("#event_staffid_display").text();
        console.log('ticketId ' + ticketId);
        console.log('threadid ' + threadid);
        console.log('entry ' + entryid);
        console.log('eventId ' + eventId);
        console.log('eventData ' + eventData);
        console.log('staffId ' + staffId);
        var csrf = $("input[name=_token]").val();
        $.ajax({
            url: "/helpdesk/deleteThreadEvent/",
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "ticketId": ticketId, "entryid": entryid, 
                "eventId": eventId, "threadid": threadid, "staffId": staffId},
         }).done(function (resp) {
            $('#modalDeleteEvent').modal('hide');
            if (resp.status === 'Success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Thread Event Deleted Successfully!"); 
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Thread Event Failed To Delete!"); 
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
</script>
@endsection
