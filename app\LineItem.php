<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class LineItem extends Model {
    protected $table = "aos_products_quotes";
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;
    
    public function lineItemCustom() {
        return $this->hasOne('App\LineItemCustom', 'id_c');
    }
    
    public function invoice()
    {
        return $this->hasMany('App\Invoices','id');
    }
}