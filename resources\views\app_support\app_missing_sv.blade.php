@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Application Missing SV</strong></h1> 
    </div> 
    <div class="block">

        <!-- Log Content -->
        <div class="table-responsive">
            <p>
                <code>
                
                Note:
                    Happen due to Softcert registration by supplier. Update sm_workflow_status is_current 1 from status_id 20199 to 20117
                </code>
            </p>
        </div>
        <!-- END Log Content -->
    </div>
    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Appl No</th>
                    <th class="text-center">Tracking Diary Id</th>
                    <th class="text-center">Tracking Status Id</th>
                    <th class="text-center">Tracking Status</th>
                    <th class="text-center">Workflow Id</th>
                    <th class="text-center">Workflow Status Id</th>
                    <th class="text-center">Workflow Status</th>
                    <th class="text-center">Appl Id</th>
                    <th class="text-center">Appl Status Id</th>
                    <th class="text-center">Appl Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Is Active Appl</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center"><a href="{{url('/find/trans/track/docno')}}/{{ $user->appl_no }}" target="_blank">{{ $user->appl_no }}</a></td>
                    <td class="text-center">{{$user->tracking_diary_id}}</td>
                    <td class="text-center">{{$user->tracking_status_id}}</td>
                    <td class="text-center">{{$user->tracking_status}}</td>
                    <td class="text-center">{{$user->workflow_id}}</td>
                    <td class="text-center">{{$user->workflow_status_id}}</td>
                    <td class="text-center">{{$user->workflow_status}}</td>
                    <td class="text-center">{{$user->appl_id }}</td>
                    <td class="text-center">{{$user->appl_status_id }}</td>
                    <td class="text-center">{{$user->appl_status }}</td>
                    <td class="text-center">{{$user->record_status}}</td>
                    <td class="text-center">{{$user->is_active_appl}}</td>
                    <td class="text-center"><a href="{{url('/app-support/fix/app_missing_sv/')}}/{{ $user->appl_no }}" class="btn btn-primary" target="_blank">UPDATE</a></td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



