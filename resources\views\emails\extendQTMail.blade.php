<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <title> QT Extend</title>
    <meta name="viewport" content="width=device-width"/>
    <style type="text/css">
        @media only screen and (max-width: 550px), screen and (max-device-width: 550px) {
            body[yahoo] .buttonwrapper {
                background-color: transparent !important;
            }

            body[yahoo] .button {
                padding: 0 !important;
            }

            body[yahoo] .button a {
                background-color: #fff;
                padding: 15px 25px !important;
            }
        }

        @media only screen and (min-device-width: 601px) {
            .content {
                width: 600px !important;
            }

            .col387 {
                width: 387px !important;
            }
        }
    </style>
</head>
<body bgcolor="#fff" style="margin: 0; padding: 0; font-family: Helvetica, Arial, sans-serif;" yahoo="fix">
<h3>QT Extend From {{ $date_start }} to {{ $date_end }}</h3>

<!--[if (gte mso 9)|(IE)]>
<table width="600" cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
<![endif]-->
<table border="1" cellpadding="0" cellspacing="0"
       style="border-collapse: collapse; width: 100%; max-width: 800px; font-family: Helvetica, Arial, sans-serif;"
       class="content">
    <thead style="background-color:#313a45; color: #ffffff;">
    <tr>
        <th align="left" width="250px" style="padding-left: 5px;padding-right: 5px;">
            QT DATE
        </th>
        <th align="left" width="250px" style="padding-left: 5px;padding-right: 5px;">
            EXTEND TO
        </th>
        <th align="left" width="250px" style="padding-left: 5px;padding-right: 5px;">
            TOTAL QT CLOSED ON EXTENDED DATE
        </th>
    </tr>
    </thead>
    @if($qtCount)
        <tr>
            <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                {{ $qtCount->qt_count_from }}
            </td>
            <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                {{ $qtCount->qt_count_to }}
            </td>
            <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                {{ $qtCount->qt_total }}
            </td>
        </tr>
    @endif
</table>
<!--[if (gte mso 9)|(IE)]>
</td>
</tr>
</table>
<![endif]-->

<br/>

<!--[if (gte mso 9)|(IE)]>
<table width="600" cellpadding="0" cellspacing="0" border="0">
    <tr>
        <td>
<![endif]-->
<table border="1" cellpadding="0" cellspacing="0"
       style="border-collapse: collapse; width: 100%; max-width: 800px; font-family: Helvetica, Arial, sans-serif;"
       class="content">
    <thead style="background-color:#313a45; color: #ffffff;">
    <tr>
        <th align="left" width="250px" style="padding-left: 5px;padding-right: 5px;">
            MINISTRY NAME
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            PTJ NAME
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            QT ID
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            QT NO
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            PUBLISH DATE
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            CLOSING DATE
        </th>
        <th align="center" style="padding-left: 5px;padding-right: 5px;">
            STATUS
        </th>
    </tr>
    </thead>
    @if($qtFromList)
        @foreach($qtFromList as $fromData)
            <tr>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->ministry_name }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->ptj_name }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->qt_id }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->qt_no }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->publish_date }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->closing_date }}
                </td>
                <td align="left" style="padding-left: 5px;padding-right: 5px; font-size:13px;">
                    {{ $fromData->status_name }}
                </td>
            </tr>
        @endforeach
    @endif
</table>
<!--[if (gte mso 9)|(IE)]>
</td>
</tr>
</table>
<![endif]-->
<br/>
<br/>

</body>
</html>
