<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\InvoiceService;
use App\Services\Traits\BpmApiService;
use Guzzle;
use GuzzleHttp\Client;

class StuckCreateInvoiceTask {
    use InvoiceService;
    use BpmApiService;

    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        $clsCreateInvoice = new StuckCreateInvoiceTask;
        
        /** USing list doc_no **/
        
        $listDocNo = array(
                  
            //'PO190000001020336',
            //'PO190000001000360'



            );
        dump("TOTAL FOUND: ".count($listDocNo));
        foreach ($listDocNo as $docNo){
            
            $typeDoc= substr($docNo, 0, 2);
            
            $stuckCreateInv = false;
            if($typeDoc == 'PO' && $clsCreateInvoice->isStuckCreateInvoicePOInEP($docNo) == true){
                dump('Check as Doc Type: PO');
                $stuckCreateInv = true;
            }
            if($typeDoc == 'CO' && $clsCreateInvoice->isStuckCreateInvoiceCOInEP($docNo) == true){
                dump('Check as Doc Type: CO');
                $stuckCreateInv = true;
            }
            
            //$stuckCreateInv = true;
            if($stuckCreateInv == true){
                $xmlData = self::getXMLCreateInvoice($docNo);
                if($xmlData != null){
                    dump('Start Create Invoice into BPM : '.$docNo);
                    //dump($xmlData);
                    self::triggerPOSTCreateInvoice($xmlData);  
                    sleep(3);
                    $dataTask = $clsCreateInvoice->findAPITaskBPMListDocAndModule($docNo, 'Fulfilment');
                    dump('Result :- ');
                    if(count($dataTask['result']) > 0){
                        dump($dataTask['result'][0]['taskId'] . ' -- instance_id : '.$dataTask['result'][0]['instanceId']. ' -- version : '.$dataTask['result'][0]['compositeDN']);
                    }else{
                        dump('##### need to check');
                        Log::debug(self::class . ' refire createinvoice not return success result ... ' . __FUNCTION__);
                        Log::debug($docNo);
                    }
                }
                
            }else{
                dump($docNo.' ->> Is not related in stuck task create invoice');
            }
        }
        
        
        /** Using list query data **/
        /*
        //$listData = $clsCreateInvoice->getListStuckCOCreateInvoice(100);
        //$listData = $clsCreateInvoice->getListStuckPOCreateInvoice(100);
        $listData = $clsCreateInvoice->getListStuckPOPartialCreateInvoice(50);
        dump("TOTAL FOUND: ".count($listData));
        foreach ($listData as $rowData){
            $documentNo =  $rowData->doc_no;
            $typeDoc= substr($documentNo, 0, 2);
            
            $stuckCreateInv = false;
            //if($typeDoc == 'PO' && $clsCreateInvoice->isStuckCreateInvoicePOInEP($documentNo) == true){
            if($typeDoc == 'PO' && $clsCreateInvoice->isStuckCreateInvoicePOPartialInEP($documentNo)== true){    
                $stuckCreateInv = true;
            }
            if($typeDoc == 'CO' && $clsCreateInvoice->isStuckCreateInvoiceCOInEP($documentNo) == true){
                $stuckCreateInv = true;
            }
            
            if($stuckCreateInv == true){
                $xmlData = self::getXMLCreateInvoice($documentNo);
                if($xmlData != null){
                    dump('Start Create Invoice into BPM : '.$documentNo);
                    self::triggerPOSTCreateInvoice($xmlData);  
                    sleep(1);
                }
                
            }
        }
        */ 
        
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }

    public static function terminateFire() {
        
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        $createInvoice = new StuckCreateInvoiceTask;
        
        $getList = self::getData();
        if (count($getList) > 0) {
            $compositeDn = null;

            dump('total ' . count($getList));
            foreach ($getList as $data) {
                if ($data->activityname === 'Create Invoice') {

                    $compositeDn = explode("*", $data->compositedn);
                    dump('Activity Name : ' . $data->activityname . ' Composite DN : ' . $compositeDn[0] . ' Instance ID : ' . $data->composite_id);
                    $dataTask = $createInvoice->submitTerminateInstance($data->composite, $compositeDn[0]);

                    if ($dataTask['status'] == 'Success') {
                        dump('Terminate Task ' . $dataTask['status']);

                        // after success terminate, refire task 
                        $xmlData = $createInvoice->getXMLCreateInvoice($data->doc_no);

                        if ($xmlData != null) {
                            dump('Start Create Invoice into BPM : ' . $data->doc_no);
                            //dump($xmlData);
                            $createInvoice->triggerPOSTCreateInvoice($xmlData);
                            sleep(3);
                            $dataTask = $createInvoice->findAPITaskBPMListDocAndModule($data->doc_no, $data->compositename);
                            dump('Result :- ');
                            if (count($dataTask['result']) > 0) {
                                dump($dataTask['result'][0]['taskId'] . ' -- instance_id : ' . $dataTask['result'][0]['instanceId'] . ' -- version : ' . $dataTask['result'][0]['compositeDN']);
                            } else {
                                dump('##### need to check');
                                Log::debug(self::class . ' refire createinvoice not return success result ... ' . __FUNCTION__);
                                Log::debug($data->doc_no);
                            }
                        }
                    } else {
                        dump($dataTask['status']);
                        dump('##### need to check ' . $data->composite_id);
                        Log::debug(self::class . ' error ' . __FUNCTION__);
                        Log::debug($data->composite_id);
                    }
                }
            }
        }
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
    
    protected static function getXMLCreateInvoice($docNo) {
        
        
        
        $classCreateInvoice = new StuckCreateInvoiceTask;
        
        $invoiceDetails = $classCreateInvoice->getInvoiceDetails($docNo);
        $approverDetails = $classCreateInvoice->getApproverDetails($docNo);
        $supplierDetails = $classCreateInvoice->getSupplierDetails($docNo);
        $supplierUsers = $classCreateInvoice->getListSupplierUsers($invoiceDetails->supplier_id);
        $orderDetails = $classCreateInvoice->getOrder($docNo);
        
        $orderDet = null;
        $isPHIS = 'false';
        $stopInst = 'false';
        if(count($orderDetails) > 0){
            $orderDet =   $orderDetails[0];
            if($orderDet->phis != ''){
                $isPHIS = 'true';
            }
            if($orderDet->stopinst != ''){
                $stopInst = 'true';
            }
            
            if($orderDet->orderno == '' || $orderDet->orderno == null){
                dump('ERROR: SAPOrderNo is NULL');
                return null;
            }
        }
       
        /** Skip if null **/
        if($invoiceDetails == null){
            dump('ERROR: Invoice Detail is NULL');
            return null;
        }
        if($approverDetails == null){
            dump('ERROR: Approver Detail is NULL');
            return null;
        }
        if($supplierDetails == null){
            dump('ERROR: Supplier Detail is NULL');
            return null;
        }
        if(count($supplierUsers) == 0){
            dump('ERROR: Supplier Users  is NULL');
            return null;
        }
        if(count($orderDetails)  == 0){
            dump('ERROR: Order Detail  is NULL');
            return null;
        }
        
        
        $ordername = str_replace("&","&amp;",$invoiceDetails->ordername);
        $approverDesignation = str_replace("&","&amp;",$approverDetails->designation);

        
        $xmlSupplierUsers = '';
        foreach ($supplierUsers as $suppUser) {
                $suppUserDesignation = str_replace("&","&amp;",$suppUser->designation);
                $strUserInfo = 
                    '<ns2:supplierList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">'.
                        '<ns4:userId>' . $suppUser->user_id . '</ns4:userId>'.
                        '<ns4:userLoginId>' . $suppUser->login_id . '</ns4:userLoginId>'.
                        '<ns4:userName>' . $suppUser->user_name . '</ns4:userName>'.
                        '<ns4:calendarName>PUTRAJAYA</ns4:calendarName>'.
                        '<ns4:designation>' . $suppUserDesignation . '</ns4:designation>'.
                    '</ns2:supplierList>';
                
                $xmlSupplierUsers = $xmlSupplierUsers.$strUserInfo;
        }
            
        $xmlCreateInvoice    = 
        '<x:Envelope xmlns:x="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://xmlns.oracle.com/bpmn/bpmnProcess/InvoiceAndPaymentCreation" xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/Order/FL_Order_Data" xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data" xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/GFMAS/SD/FL_Stop_Delivery_Req_Data" xmlns:ns5="http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Item_Data">'.        
            '<x:Header />'.
            '<x:Body>'.
                '<ns1:startInvoice>'.
                    '<ns2:FL_Order_Data xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/Order/FL_Order_Data">'.
                        '<ns2:requestId>'.$invoiceDetails->requestid.'</ns2:requestId>'.
                        '<ns2:orderId>'.$invoiceDetails->orderid.'</ns2:orderId>'.
                        '<ns2:orderName>'.$ordername.'</ns2:orderName>'.
                        '<ns2:orderType>'.$invoiceDetails->ordertype.'</ns2:orderType>'.
                        '<ns2:reqDocNo />'.
                        '<ns2:orderDocNumber>'.$invoiceDetails->orderdocnmber.'</ns2:orderDocNumber>'.
                        '<ns2:approverList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">'.
                            '<ns4:userId>' . $approverDetails->userid . '</ns4:userId>'.
                            '<ns4:userLoginId>' . $approverDetails->loginid . '</ns4:userLoginId>'.
                            '<ns4:userName>' . $approverDetails->username . '</ns4:userName>'.
                            '<ns4:calendarName>PUTRAJAYA</ns4:calendarName>'.
                            '<ns4:designation>' . $approverDesignation . '</ns4:designation>'.
                        '</ns2:approverList>'.
                        $xmlSupplierUsers.
                        '<ns2:sapOrderNo>'.$orderDet->orderno.'</ns2:sapOrderNo>'.
                        '<ns2:businessArea>'.$orderDet->businessarea.'</ns2:businessArea>'.
                    '</ns2:FL_Order_Data>'.
                    '<ns1:createdBy>'.$supplierDetails->loginid.'</ns1:createdBy>'.
                    '<ns1:isPhis>'.$isPHIS.'</ns1:isPhis>'.
                    '<ns1:hasStopFulfillment>'.$stopInst.'</ns1:hasStopFulfillment>'.
                    '<ns3:FL_Stop_Delivery_Req_Data xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/GFMAS/SD/FL_Stop_Delivery_Req_Data">'.
                        '<ns3:stopInstrutionReqId xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:orderNo />'.
                        '<ns3:stopInstructionNo />'.
                        '<ns3:postingDate xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:instructionItemList xmlns:ns5="http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Item_Data">'.
                            '<ns5:itemCode xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:lineItem xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:amt xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:quantity xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:unitOfMeasure xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                        '</ns3:instructionItemList>'.
                        '<ns3:stopInstructionDate xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:createdBy />'.
                        '<ns3:approvedBy />'.
                        '<ns3:sapPOCONo />'.
                        '<ns3:orderId xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:uuid />'.
                        '<ns3:businessArea />'.
                    '</ns3:FL_Stop_Delivery_Req_Data>'.
                    '<ns1:taxInvoice>true</ns1:taxInvoice>'.
                    '<ns1:taxRelief>false</ns1:taxRelief>'.
                    '<ns1:skipTax>true</ns1:skipTax>'.
                '</ns1:startInvoice>'.
            '</x:Body>'.
        '</x:Envelope>';
        
        //dump($xmlCreateInvoice);
        
        return $xmlCreateInvoice;
    }
    
    protected static  function triggerPOSTCreateInvoice($xmlData) {
        dump('Start triggerPOSTCreateInvoice...');
        //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePNo</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        //$xmlContents = '"'.$xmlData.'"';
        
        $client = new Client([
            'base_uri' => 'http://*************:9003',
          ]);
          $payload = $xmlData;
          $response = $client->post('http://*************:9003/soa-infra/services/default/Fulfilment/InvoiceAndPaymentCreation.service', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'startInvoice',
            ]
          ]);
          $body = $response->getStatusCode();
          dump($body);

    }
    
    public static function getData() {

        $query = " SELECT
                    a.CUSTOMATTRIBUTESTRING1 AS DOC_NO,
                    a.CUSTOMATTRIBUTESTRING2 AS DOC_TYPE,
                    a.STATE,
                    a.COMPOSITEINSTANCEID AS composite_id,
                    substr(a.TASKDEFINITIONID, 1, 33) AS composite_module,
                    a.TASKDEFINITIONID,
                    (a.COMPOSITEINSTANCEID || ',' || substr(a.TASKDEFINITIONID, 1, 26)) AS comp_id_module,
                    a.TASKNUMBER,
                    a.PROCESSID,
                    a.PROCESSNAME,
                    a.COMPOSITENAME,
                    a.ACTIVITYNAME,
                    a.CREATEDDATE,
                    a.UPDATEDDATE,
                    substr(a.TASKDEFINITIONID, 1, 26) as composite,
                    a.COMPOSITEDN
                  FROM wftask a, COMPOSITE_INSTANCE c
                  WHERE  a.COMPOSITEINSTANCEID = c.ID
                    AND a.state = 'ASSIGNED'
		    AND c.STATE in (16,17,18)
                   -- AND TO_CHAR(a.CREATEDDATE,'YYYY-MM-DD') = '2020-06-09'
                    order by a.UPDATEDDATE desc"; //oracle_bpm_rpt

        $result = DB::connection('oracle_bpm_rpt')->select($query);

        return $result;
    }

}