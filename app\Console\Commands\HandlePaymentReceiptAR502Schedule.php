<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use DB;
use App\EpSupportActionLog;
use Datetime;
use DateInterval;
use DatePeriod;

class HandlePaymentReceiptAR502Schedule extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandlePaymentReceiptAR502';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Extract content file AR502 to insert each receipt no in ep_supplier_ar502';

    protected $AR502_CODE = 'GFM-380';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        Log::info($clsInfo.'starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        
        /** Manual find contain by ssh */
        // $fileName = "1102AR50240000120220502001.GPG";
        // $contains = self::getContainFileBySSH($fileName);
        // self::extractFileData($fileName, $contains);
        // dd("DONE");


        //$this->extractAr502FileManualSsh("2023-11-10");
        
        self::extractAr502File();
        
        
        $logsdata = $clsInfo.'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
        MigrateUtils::logDump($logsdata);      
        
    }

    protected function extractAr502File(){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        /*Get latest created_date from ep_osb_batch_file*/
        $listDataMax = collect(DB::connection('mysql_ep_support')->select("SELECT MAX(DATE(created_date)) as latest_created_date FROM ep_supplier_ar502"));
        $dateStartFromLatestDB = $listDataMax->first()->latest_created_date;
     
        $dateStart = Carbon::parse($dateStartFromLatestDB)->format('Y-m-d');
        //$dateStart = '2022-01-01';
        $dateEnd = Carbon::now()->format('Y-m-d');
        
        MigrateUtils::logDump(__METHOD__.' dateStart :: '.$dateStart);
        MigrateUtils::logDump(__METHOD__.' dateEnd :: '.$dateEnd);


        $begin = new DateTime($dateStart);
        $end = new DateTime($dateEnd);

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end->add($interval));

        foreach ($period as $dt) {
            $date = $dt->format("Y-m-d");
            MigrateUtils::logDump(__METHOD__.' Query Date: '.$date);
            try { 
                $list = $this->getListFilesBatchFileByDate($this->AR502_CODE,$date);
                $listData = collect($list);

                $counter=0;
                foreach ($listData as $objFile){
                    $count = DB::connection('mysql_ep_support')->table('ep_supplier_ar502')
                            ->where('file_name',$objFile->file_name)
                            ->count();
                    if($count == 0){
                        MigrateUtils::logDump(__METHOD__.' Found  '.$objFile->file_name. ' to be inserted.');
                        self::extractFileData($objFile->file_name, $objFile->file_data);
                        
                    }else{
                        MigrateUtils::logDump($clsInfo.' Is existed! : '.$objFile->file_name);
                    }
                    $counter++;
                    if($counter == 5){
                        sleep(1);
                        $counter = 0;
                    }
                }

                // If not found check in OSB_BATCH_FILE, try search physical file in storage batch
                if($listData->count() == 0){
                   $this->extractAr502FileManualSsh($date);
                }

            } catch (\Exception $exc) {
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
                $this->sendErrorEmail($exc->getTraceAsString());
            }
        }
    }

    protected function extractAr502FileManualSsh($dateStart){
        // Get the filename AR502 created in DI_INTERFACE_LOG
        MigrateUtils::logDump(__METHOD__.' >> starting by checking date '.$dateStart );            
        $list = $this->getListGeneratedFileNameByDate($this->AR502_CODE,$dateStart);
        MigrateUtils::logDump(__METHOD__.' >> found file '.count($list) );
        foreach($list as $row){
            /** Manual find contain by ssh */
            if(strlen($row->file_name) > 10){
                $fileName = $row->file_name;
                MigrateUtils::logDump(__METHOD__.' >> find file '.$fileName );  
                $count = DB::connection('mysql_ep_support')->table('ep_supplier_ar502')
                            ->where('file_name',$fileName)
                            ->count();
                if($count == 0){
                    MigrateUtils::logDump(__METHOD__.' >>  '.$fileName.' starting to insert in table ep_supplier_ar502');
                    $contains = self::getContainFileBySSH($fileName);
                    self::extractFileData($fileName, $contains);
                }else{
                    MigrateUtils::logDump(__METHOD__.' >>  '.$fileName.' existed. Skip to insert');
                }   
            }
        }
        
        if(count($list) == 0){
            $dateTrim = str_replace("-","",$dateStart);
            $fileName = '1102AR502400001'.$dateTrim.'001.GPG';
            MigrateUtils::logDump(__METHOD__.' >> find file default by date '.$fileName );  
                $count = DB::connection('mysql_ep_support')->table('ep_supplier_ar502')
                            ->where('file_name',$fileName)
                            ->count();
                if($count == 0){
                    MigrateUtils::logDump(__METHOD__.' >>  '.$fileName.' starting to insert in table ep_supplier_ar502');
                    $contains = self::getContainFileBySSH($fileName);
                    self::extractFileData($fileName, $contains);
                }else{
                    MigrateUtils::logDump(__METHOD__.' >>  '.$fileName.' existed. Skip to insert');
                } 
        } 
    }

    protected function extractFileData($fileName, $file_content) {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $lineCount = substr_count($file_content, "\n");
        $count = 0; //For AR502, Skip 1st line cause show total receipt. start from 1
        $receiptNo = "";
        $paymentDate = "";
        $paymentType = "";
        $amount = '';
        MigrateUtils::logDump($clsInfo.'[' . $fileName . '] File content line count: ' . $lineCount );

        /* SPLIT DATA HERE */
        foreach (preg_split("/((\r?\n)|(\r\n?))/", $file_content) as $line) {
           
            //dump($count. ' >> '.$line);
          
            $chkLine = substr($line, 0, 4);
            if($chkLine == '1NRC') {
                //dump($count. ' >> '.$line);
                //$lineUpper = preg_split("/((\r?\n)|(\r\n?))/", $file_content)[$count-1];
                $paymentType = rtrim(substr($line, 141, 50));


                $receiptNo = trim(substr($line, 2, 20));
                //$paymentDate = trim(substr($line, 214, 8));
                $paymentDate = trim(substr($line, 22, 8));
                //dd($paymentDate);
                $amount =  ltrim(substr($line, 345, 13), '0');
                //dd($amount);
                if ($paymentDate && strlen($paymentDate) == 8) {
                    $paymentDate = Carbon::createFromFormat('dmY', $paymentDate);
                } else {
                    $paymentDate = null;
                }
                if( $amount > 0) {
                    $amount = ( $amount / 100);
                }
                $dataset = array(
                    [
                        'receipt_no' => $receiptNo,
                        'file_name' => $fileName,
                        'payment_type' => $paymentType,
                        'payment_date' => $paymentDate,
                        'amount' => $amount,
                        'created_date' => Carbon::now(),
                    ]
                );
                //dd($dataset);
        
                if(strpos($receiptNo, "RC") !== false){
                    
                    /* INSERTING */
                    DB::connection('mysql_ep_support')->table('ep_supplier_ar502')->insert($dataset);
                } 
            }
            
            $count++;
        }
        MigrateUtils::logDump($clsInfo.'Total success inserted : '.$count);
    }
    
    protected function getContainFileBySSH($filename){
        
        //$filename = "1102AR50240000120220321001.GPG";
        $remotePath =  "/batch/Temp/".$filename;
        return $contents = SSH::into('portal')->getString($remotePath);
    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: '.__CLASS__.' > '.$this->description
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($data["to"]). ' ERROR '. $e->getMessage());
        }
    }
    


}
