@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>FULFILMENT BPM TASK<br>
                <small>Refire Create Invoice, Submit Invoice & Payment Advice (PA)</small>
            </h1>
        </div>
    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="row" >
                <div class="col-sm-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>Search</strong> PO/CO Number</h2>
                        </div>                        
                        
                        @if($dataTask != null)

                        <div class="block">
                            <h5 class="alert alert-success">Success Refire Invoice Creation</h5>
                            <b>
                                <ul>
                                    <li>Composite : {{ $dataTask['result'][0]['compositeDN'] }}</li>
                                    <li>Instance Id : {{ $dataTask['result'][0]['instanceId'] }}</li>
                                </ul>
                            </b>
                        </div>
                        
                        @endif
                        
                        @if($status_api != null)

                        <div class="block">
                            <h5 class="alert alert-danger"> {{ $status_api }} </h5>
                        </div>

                        @endif
                        
                        <form action="{{url('/bpm/fl/task/createSubmitInvoicePA')}}" method="post" class="form-horizontal form-bordered" >
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="doc_no" name="doc_no" class="form-control" placeholder="PO/CO Number .. Fulfilment modules only" 
                                           required="required" value="{{old('doc_no')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Document Number <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="action_task_create_submit_invoice_pa" name="action_task_create_submit_invoice_pa" class="select-chosen" data-placeholder="Choose Type Action Task .." style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="create-invoice" @if(old('action_task_create_submit_invoice_pa') === "create-invoice") selected @endif>Create Invoice</option>
                                        <option value="submit-invoice" @if(old('action_task_create_submit_invoice_pa') === "submit-invoice") selected @endif>Submit Invoice</option>
                                        <option value="payment-advice" @if(old('action_task_create_submit_invoice_pa') === "payment-advice") selected @endif>Payment Advice</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-gears"></i>  Choose Type Action Task <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group" id="div-login-id" style="display:none">
                                <div class="input-group">
                                    <input type="text" id="login_id" name="login_id" class="form-control" placeholder="Login Payment Match .." 
                                           value="{{old('login_id')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Login Payment Match <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="is_trigger_bpm_create_submit_invoice_pa" name="is_trigger_bpm_create_submit_invoice_pa" class="select-chosen" data-placeholder="True (program will execute trigger to BPM) , False (program will not execute trigger to BPM)" style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="true" @if(old('is_trigger_bpm_create_submit_invoice_pa') === "true") selected @endif>True</option>
                                        <option value="false" @if(old('is_trigger_bpm_create_submit_invoice_pa') === "false") selected @endif>False</option>
                                    </select>
                                    <span class="input-group-addon"><i class="hi hi-transfer"></i>  Is Trigger BPM </span>
                                </div>
                            </div>
                            
                            <div class="form-group form-actions">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-sm btn-info pull-right">Submit</button>
                                </div>
                            </div>
                        </form>
                        @if($listdata != null && count($listdata) > 0)

                        @if(isset($listdata["xml_payload"]))
                        <div class="block">
                            <h4>Payload</h4>
                            <pre class="line-numbers">
                                    <code class="language-markup">{{ htmlentities($listdata["xml_payload"]) }}</code>
                            </pre>
                        </div>
                        @endif
                        
                        @if(isset($listdata["remarks"]))
                        <div class="block">
                            <h5 class="alert alert-danger">{{$listdata["remarks"]}}</h5>
                        </div>
                        @endif
                        </div>
                        
                        @endif
                    </div>

                </div>
            </div>

        </div>
    </div>
    



@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    $("#action_task_create_submit_invoice_pa").bind("change", function () {
        var action = $(this).find(":selected").val();
        console.log('action ' + action);
        if (action === 'payment-advice') {
            $('#div-login-id').show();
            $("#login_id").prop("required", true);
        } else {
            $('#div-login-id').hide();
            $("#login_id").prop("required", false);
        }
        
    });
</script>
@endsection



