@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/trans/track/docno/')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Tracking Diari<br>
                <small>Carian Dokumen No. &raquo;  Simple Quote (SQ),Quatation Tender (QT),  Request Note (RN) , LOA Number (LA), Contract No (CT), 
                    Purchase Request (PR), Contract Request (CR), Purchase Order (PO) and Contract Order (CO) only.</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Tracking Diari </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>
                
                
                
                {{-- INFO FOR TECHNICAL SPECILIST --}}
                @if(Auth::user()->isAdvRolesEp() || Auth::user()->isEpSpecialistUsers()) 
                <div class="row">
                    <div class="col-sm-4 text-info">
                        @if(isset($sqInfo) && $sqInfo != null)
                        <h5><strong>Details Info {{$sqInfo->quote_no}}</strong></h5>
                        <address>
                            <strong>Start Date </strong> : {{ $sqInfo->start_date }}<br />
                            <strong>End Date </strong> : {{ $sqInfo->end_date }}<br />
                        </address>    
                        @endif
                        @if(isset($supplier) && $supplier != null)
                        <address>
                            <strong>Supplier Name </strong> : {{ $supplier->company_name }}<br />
                            <strong>Business Type </strong> : {{ $supplier->business_type }}  &raquo;  ({{ App\Services\EPService::$BUSINESS_TYPE[$supplier->business_type] }})<br />
                            <strong>SSM No </strong> : {{ $supplier->reg_no }}<br />
                            <strong>eP No </strong> : <a target="_blank" href="{{ url('/find/mofno') }}/{{ $supplier->ep_no }}" class="text-info">{{ $supplier->ep_no }} </a> <a target="_blank" href="{{ url('/find/gfmas/apive/') }}/{{ $supplier->ep_no }}" >Check Apive</a><br />
                            <strong>MOF No </strong> : {{ $supplier->mof_no }}  <a target="_blank" href="{{ url('/find/mofno') }}/{{ $supplier->mof_no }}" >Details Supplier</a><br />
                            @if($supplier->sap_order_no != '')
                            <strong>SAP Order No </strong> : {{ $supplier->sap_order_no }} <br />
                            @endif
                        </address>                
                        @endif
                        
                        @if($poco_no != null)
                        <address>
                            <strong>Find OSB</strong> : <a target="_blank" href="{{ url('/find/osb/log') }}/{{ $poco_no }}" >OSB Log</a>
                        </address>     
                        @endif  
                    </div>

                    <div class="col-sm-8 text-info">
                        @if(isset($createdPtj) && $createdPtj != null)
                        <address>
                            <strong>PTJ Created </strong> : {{ $createdPtj->org_code }} -  
                                <a target="_blank" href="{{ url('/find/orgcode') }}/{{ $createdPtj->org_code }}" >{{$createdPtj->org_name}}</a><br />

                            @if(isset($preparedPtj) && $preparedPtj != null)
                                <strong>PTJ Prepared </strong> : {{ $preparedPtj->org_code }} -  
                                    <a target="_blank" href="{{ url('/find/orgcode') }}/{{ $preparedPtj->org_code }}" >{{$preparedPtj->org_name}}</a><br />              
                            @endif

                            @if(isset($issuedPtj) && $issuedPtj != null)
                                <strong>PTJ Issued </strong> : {{ $issuedPtj->org_code }} -  
                                    <a target="_blank" href="{{ url('/find/orgcode') }}/{{ $issuedPtj->org_code }}" >{{$issuedPtj->org_name}}</a><br />             
                            @endif
                            
                            @if(isset($chargePtj) && $chargePtj != null)
                                <strong>PTJ Charge </strong> : {{ $chargePtj->org_code }} -  
                                    <a target="_blank" href="{{ url('/find/orgcode') }}/{{ $chargePtj->org_code }}" >{{$chargePtj->org_name}}</a><br />             
                            @endif
                            
                            <strong>AO / AGO </strong> : {{ $supplier->ag_office_name }}<br />
                        </address>                
                        @endif
                        
                        @if(isset($smAppl) && $smAppl != null)
                        <address>
                            <strong>Appl ID </strong> : {{ $smAppl->appl_id }} , &nbsp;&nbsp; <strong>Appl No. </strong> : {{ $smAppl->appl_no }}<br />
                            <strong>Appl Type </strong> : {{ $smAppl->appl_type }} , &nbsp;&nbsp; <strong>Appl Record Status </strong> : {{ $smAppl->record_status }}<br />
                            <strong>Appl Created Date </strong> : {{ $smAppl->created_date }} , &nbsp;&nbsp; <strong>Appl Changed Date</strong> : {{ $smAppl->changed_date }}<br />
                            <strong>Appl Status </strong> : {{ $smAppl->status_id }} - {{$smAppl->status_name}}<br />
                            <strong>Is Active Appl </strong> : {{ $smAppl->is_active_appl }} , &nbsp;&nbsp; <strong>Is Resubmit </strong> : {{ $smAppl->is_resubmit }}<br />
                            
                        </address> 
                                     
                        @endif
                    </div>
                </div>
                
                
                {{-- END INFO FOR TECHNICAL SPECILIST --}}
                @else
                
                {{-- OPEN INFO FOR CS --}}
                <div class="row">
                    <div class="col-sm-4 text-info">
                        @if(isset($sqInfo) && $sqInfo != null)
                        <h5><strong>Details Info {{$sqInfo->quote_no}}</strong></h5>
                        <address>
                            <strong>Start Date </strong> : {{ $sqInfo->start_date }}<br />
                            <strong>End Date </strong> : {{ $sqInfo->end_date }}<br />
                        </address>    
                        @endif
                    </div>
                </div>
                @endif

                @if($poco_no != null)
                <div class="marginbtm10" >
                    <div class="list-trans-dofn" style="display: inline;">
                        <a href='#modal-list-trans-dofn'
                        class='modal-list-data-action btn  btn-default ' 
                        data-toggle='modal'  
                        data-url='/find/trans/track/docno/dofn/{{ $poco_no }}'
                        data-title='Status WorkFlow DO/FN Search By {{ $poco_no }}'>
                            <strong style="font-weight: bolder;">
                                <i class="fa fa-info-circle"></i> 
                            Checking Status WorkFlow DO/FN </strong>
                    </a>
                    </div>
                    <div class="list-trans-yepmenu" style="display: inline;">
                        <a href='#modal-list-trans-yepmenu'
                        class='modal-list-data-action btn  btn-default ' 
                        data-toggle='modal'  
                        data-url='/find/trans/track/docno/yepmenu/{{ $poco_no }}'
                        data-title='YEP Menu Tasklist Search By {{ $poco_no }}'>
                            <strong style="font-weight: bolder;">
                                <i class="fa fa-info-circle"></i> 
                            Checking YEP Menu </strong>
                        </a>
                    </div>
                </div>
                @endif
                
                
                <div class="table-responsive">
                    <table id="tracking-diary-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">ACTION DESCRIPTION</th>
                            <th class="text-center">ACTION DATE</th>
                            <th class="text-center">ROLE</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->tracking_diary_id }}</td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center list-trans-dofn">
                                <a href='#modal-list-trans-dofn'
                                    class='modal-list-data-action ' 
                                    data-toggle='modal'  
                                    data-url='/find/trans/docno/workflow/{{ $data->doc_no }}'
                                    data-title='Status WorkFlow Search By {{ $data->doc_no }}'>
                                     <strong style="font-weight: bolder;">
                                     {{ $data->doc_no }} </strong><br />
                                 </a>
                                
                                </td>
                                <td class="text-left">{{ $data->action_desc }}</td>
                                <td class="text-center">{{ $data->actioned_date }}</td>
                                <td class="text-center">{{ $data->role_code }}</td>
                                <td class="text-center">{{ $data->status_name }} ({{ $data->status_id }})</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif
    
    <!-- MODAL: TRANS DO/FN  -->
    <div id="modal-list-trans-dofn" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- MODAL: TRANS YEP MENU TASKLIST -->
    <div id="modal-list-trans-yepmenu" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header-yepmenu">List Title</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });
App.datatables();
$('#tracking-diary-datatable').dataTable({
                order: [[ 4, "desc" ],[ 0, "desc" ]],
                columnDefs: [  ],
                pageLength: 100,
                lengthMenu: [[10, 20, 30, 50, 100,  -1], [10, 20, 30, 50, 100, 'All']]
            });
            
var APP_URL = {!! json_encode(url('/')) !!}

$(document).ready(function () {

    $('.list-trans-dofn').on("click", '.modal-list-data-action', function () {

        $('.spinner-loading').show();
        $('.trans-div-detail').html('Please wait ...').fadeIn();

        $('#modal-list-data-header').text($(this).attr('data-title'));

        $.ajax({
            url: APP_URL + $(this).attr('data-url'),
            type: "GET",
            success: function (data) {
                $data = $(data)
                $('.spinner-loading').hide();
                $('.trans-div-detail').html($data).fadeIn();
            }
        });

    });
    $('.list-trans-yepmenu').on("click", '.modal-list-data-action', function () {

        $('.spinner-loading').show();
        $('.trans-div-detail').html('Please wait ...').fadeIn();

        $('#modal-list-data-header-yepmenu').text($(this).attr('data-title'));

        $.ajax({
            url: APP_URL + $(this).attr('data-url'),
            type: "GET",
            success: function (data) {
                $data = $(data)
                $('.spinner-loading').hide();
                $('.trans-div-detail').html($data).fadeIn();
            }
        });

    });
            
});            
</script>
@endsection



