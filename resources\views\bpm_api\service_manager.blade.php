@extends('layouts.guest-dash')

<style> 
    #table-scroll {
        height:50%;
        width:100%;
        overflow:auto;
    }
    .nested {
        display: none;
    }

    .active {
        display: block;
    }

</style>

@section('content')

<div class="content-header">
    <ul class="nav-horizontal text-center">
        <li>
            <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
        </li>
        <li>
            <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
        </li>
        <li>
            <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
        </li>
        <li>
            <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
        </li>
        <li class="active">
            <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
        </li>
        <li>
            <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
        </li>
        <li >
            <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
        </li>
    </ul>
</div>
@if($status_api != null)
    <h5 class="alert alert-danger">{{$status_api}}</h5>
@endif
<div id="result" class="block" style="display:none;">
        <h5 class="alert alert-warning" id="status" ></h5>
        <table id="results" class="table table-bordered alert-danger">
            
        </table>
</div>

<h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
<div class="col-lg-12">
    <div class="row">
        <div class="col-lg-6">
            @if(isset($listdata["data"]))
            <div class="block">
                <ul id="ul_name" style="list-style-type: none;">
                    @foreach($listdata["data"] as $url)
                    <li value="{{ $url["name"] }}" style="margin: 20px;font-size: 120%"><i class="caret-icon fa fa-caret-right fa-md process" style="width:15px;"></i><i class="fa fa-cubes" style="color: lightblue;width:10px;"></i> &nbsp;&nbsp; {{ $url["name"] }}
                        <ul id="ul_process" style="list-style-type: none;">
                            @foreach($url["processes"] as $process)
                            <li id="process_name" value="{{ $process["name"] }}" style="display:none;list-style-type: none;margin: 20px"><i class="caret-icon fa fa-caret-right fa-md trigger" style="width:15px;"></i><i class="fa fa-sitemap" style="color: lightblue;width:10px;"></i> &nbsp;&nbsp; {{ $process["name"] }} 
                                @foreach($process["triggers"] as $trigger)
                                <ul id="ul_trigger">
                                    <li id="trigger_name" value="|{{ $trigger["name"] }}-{{ $process["name"] }}?{{ $url["name"] }}>{{ json_encode($trigger) }}" style="display:none;list-style-type: none;cursor: pointer; margin: 20px"><i class="fa fa-arrow-right" style="color: green;width:15px;"></i> {{ $trigger["name"] }}</li>
                                </ul>

                                @endforeach
                            </li>
                            @endforeach
                        </ul>
                    </li>
                    @endforeach
                </ul>
            </div>
            @endif
        </div>
        <div class="col-lg-6">
            <div class="block" id="service_table" style="display: none">
                <table  class="table table-borderless" style="table-layout: fixed; width: 100%;">
                    <tbody style="font-size:80%;">
                        <tr>
                            <td class="text-right"><strong style="color:#003d7a;">URL : </strong></td>
                            <td style="word-wrap: break-word;" colspan="2" id="url">  </td>
                        </tr>
                        <tr>
                            <td class="text-right"><strong style="color:#003d7a;">Process : </strong></td>
                            <td id="process">  </td>
                            <td class="text-right"><strong style="color:#003d7a;">Trigger : </strong></td>
                            <td id="trigger">  </td>
                        </tr>
                    </tbody>
                </table>
                <table id="table_element" class="table table-borderless" style="table-layout: fixed; width: 100%;">

                </table>
            </div>
        </div>
    </div>
</div>
<div id="modal_spinner" class="modal fade">
    <div class="modal-dialog modal-sm" style="width: 10%; transform: translate(0, -50%); top: 50%; margin: 0 auto">
        <div class="modal-content">
            <div class="modal-body">
                <center>
                <i class="fa fa-spinner fa-3x fa-spin" style="color: red"></i><br/><br/>Loading..
            </center>
            </div>    
        </div>
    </div>
</div>
<div id="modal_detail" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-condensed table-bordered" id="detail"></table>
                </div>  
            </div> 
            <div class="modal-footer">
                <button type="button" id="confirm_trigger" class="btn btn-sm btn-info">Trigger</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('jsprivate')

<script>$('#page-container').removeAttr('class');</script>
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
        ModalListActionLogDatatable.init();
    });</script>

<script>
    $(document).ready(function () {

        var csrf = $("input[name=_token]").val();
        var process = document.getElementsByClassName("process");
        var trigger = document.getElementsByClassName("trigger");
        var i, j;

        for (i = 0; i < process.length; i++) {
            process[i].addEventListener("click", function () {
                $(this).closest("li").find("[id='process_name']").slideToggle();
                $(this).toggleClass('fa-caret-down fa-caret-right');
            });
        }

        for (j = 0; j < trigger.length; j++) {
            trigger[j].addEventListener("click", function () {
                $(this).closest("li").find("[id='trigger_name']").slideToggle();
                $(this).toggleClass('fa-caret-down fa-caret-right');
            });
        }

        $("#ul_name li").click(function () {

            $("#ul_process li").click(function () {

                $("#ul_trigger li").click(function () {

                    var name = $(this).attr('value');
                    var trigger = name.substring(
                            name.lastIndexOf("|") + 1,
                            name.lastIndexOf("-")
                            );

                    var process = name.substring(
                            name.lastIndexOf("-") + 1,
                            name.lastIndexOf("?")
                            );

                    var url = name.substring(
                            name.lastIndexOf("?") + 1,
                            name.lastIndexOf(">")
                            );

                    var element = name.split(">").pop();
                    $("#url").html("/soa-infra/services/default/" + url + "/" + process + ".service");
                    $("#process").html(process);
                    $("#trigger").html(trigger);

                    var html = '<tbody style="font-size:80%;width:100%;overflow-x:scroll;overflow-y:hidden">';

                    var elementArray = jQuery.parseJSON(element);
                    var count = (elementArray["elements"]).length;

                    for (i = 0; i < count; i++) {

                        var elementName = elementArray["elements"][i]["name"];
                        var elementType = elementArray["elements"][i]["type"];

                        if (elementType === 'string' || elementType === 'int' || elementType === 'long') {
                            html += "<tr><td><i class='fa fa-edit'></i> " + elementName + "<br/>";
                            html += "<input id='" + elementName + "' eltype='" + elementType +"' i='" + i +"' type='text' class='form-control'></td>";

                        } else if (elementType === 'boolean') {
                            html += "<tr><td><i class='fa fa-toggle-on'></i> " + elementName + "<br/>";
                            html += "<select id='" + elementName + "' eltype='" + elementType +"' i='" + i +"'required class='form-control'>";
                            html += "<option id='" + elementName + "' eltype='" + elementType +"' i='" + i +"'value='true'>True</option>";
                            html += "<option id='" + elementName + "' eltype='" + elementType +"' i='" + i +"'value='false'>False</option></select></td>";

                        } else {
                            html += "<tr><td><i class='fa fa-file-code-o'></i> " + elementName + "<br/>";
                            html += "<textarea id='" + elementName + "' eltype='" + elementType +"' i='" + i +"' style='width:100%;height:400px'></textarea></td>";
                        }

                    }
                    html += "</tr><tbody>";
                    html += "<tfoot><button id='create_service' class='btn btn-sm btn-primary pull-right' style='margin-right: 8px'><i class='fa fa-fire'></i> Execute</button></tfoot>";
                    document.getElementById("table_element").innerHTML = html;

                    document.getElementById("service_table").style.display = "block";

                    var elements = [];
                    var type = null;
                    var i = null;
                    var value = null;
                    var cid = null;
                    
                    $('#create_service').on('click', function () {

                        elements = [];
                        $('#table_element tr').find('input').each(function () {
                            type = $(this).attr('eltype');
                            i = $(this).attr('i');
                            value = this.value;
                            cid = this.id === 'cid' ? this.value : null;
                            elements.push({name: this.id, value: value, type: type, i: i});
                        });

                        $('#table_element tr').find('textarea').each(function () {
                            type = $(this).attr('eltype');
                            i = $(this).attr('i');
                            value = this.value;
                            elements.push({name: this.id, value: value, type: type, i: i});
                        });

                        $('#table_element tr').find('option:selected').each(function () {
                            type = $(this).attr('eltype');
                            i = $(this).attr('i');
                            value = this.value;
                            elements.push({name: this.id, value: value, type: type, i: i});
                        });                      
                             
                        elements.sort(function(a, b) {
                            return a['i'] - b['i'];
                        });
                       
                        elements.forEach(function(v){ delete v.i });
                        
                        if((process === 'QuotationTenderCreation' && trigger === 'publishQT') || 
                                ((process === 'Evaluation' && trigger === 'start'))) {
                            value = cid;
                            if(value !== ''){
                                $('#modal_spinner').modal('show');
                                $.ajax({
                                type: "GET",
                                url: "/bpm/service/manager/detail",
                                data: {"value": value}, 
                            }).done(function (resp) {
                                if(resp){
                                    $('#modal_spinner').modal('hide');
                                    $('#modal_detail').modal('show');
                                    var html = '<thead style="background-color:#f2f2f2;">\n\
                                    <tr><th>DOCUMENT NO</th>\n\
                                    <th>WORKFLOW STATUS</th>\n\
                                    <th>PUBLISH DATE</th>\n\
                                    <th>CLOSING DATE</th></tr></thead>';
                                    for (const [listdata, value] of Object.entries(resp)) {
                                        html += '<tbody style="font-size:80%;"><tr>';
                                        html += "<td>"+value["qt_no"]+"</td>";
                                        html += "<td>"+value["status_name"]+"</td>";
                                        html += "<td>"+value["publish_date"]+"</td>";
                                        html += "<td>"+value["closing_date"]+'<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Closed! "></i>\n\
                                    <span style="font-weight: bolder; color:red;"> CLOSED </span></td>';
                                    }
                                    html += "</tr><tbody>";
                                    document.getElementById("detail").innerHTML = html;
                                }
                            });
                            }else{
                                window.scrollTo(0, 0);
                                $('#result').show();
                                document.getElementById("status").innerHTML = "ERROR";
                                document.getElementById("results").innerHTML = "VALUE IS EMPTY!!";
                            }
                            
                        } else {
                        if(value !== ''){
                        triggerService(csrf,url,process,trigger,elements);
                            console.log('Trigger Service');
                            }
                            else{
                            $('#modal_spinner').modal('hide');
                            window.scrollTo(0, 0);
                                $('#result').show();
                                document.getElementById("status").innerHTML = "ERROR";
                                document.getElementById("results").innerHTML = "VALUE IS EMPTY!!";
                            } 
                        }
                        
                    });
                    $('#confirm_trigger').on('click', function () {
                        console.log('Button Confirm Trigger Service');
                        $('#modal_detail').modal('hide'); 
                        triggerService(csrf,url,process,trigger,elements);
                    });   
                });
            });
        });       
    });

function triggerService(csrf,url,process,trigger,elements){
    $('#modal_spinner').modal('show');                          
        $.ajax({
            type: "POST",
            url: "/bpm/service/manager/create/" + process,
            data: {"_token": csrf, "url": url, "process": process, "trigger": trigger, "elements": elements},
            error: function(xhr, status, error){
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText; 
            }
        }).done(function (resp) {
            window.scrollTo(0, 0);
                $('#result').show();
                if(resp){
                    $('#modal_spinner').modal('hide');
                    document.getElementById("status").innerHTML = resp["status"];
                    var html = '<thead><tr><th>Message</th><th>Status</th><th>Instance</th></tr></thead>';
                    
                    if(typeof resp["result"] === 'object'){
                        html += '<tbody style="font-size:80%;"><tr>\n\
                                    <td>'+resp["result"]["message"]+'</td>';
                        html += '<td>'+resp["result"]["status"]+'</td>';
                        
                        if(resp["result"].hasOwnProperty('instance')){
                            html += '<td>'+resp["result"]["instance"]+'</td></tr>';
                        }
                    } else {
                        for (const [listdata, value] of Object.entries(resp["result"]["message"])) {
                            html += '<tbody style="font-size:80%;"><tr>\n\
                                    <td>'+value["message"]+'</td>';
                            html += '<td>'+value["status"]+'</td>';
                            html += '<td>'+value["instance"]+'</td></tr>';

                        }
                    }
                    html += "<tbody>";
                    document.getElementById("results").innerHTML = html;
                }
        });
}
</script>


@endsection











