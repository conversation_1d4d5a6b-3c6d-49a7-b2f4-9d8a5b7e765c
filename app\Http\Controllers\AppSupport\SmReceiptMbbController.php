<?php

namespace App\Http\Controllers\AppSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\SupplierService;
use App\Migrate\AppSupport\SmRejectBumiStatusFixIssue;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierFullGrantService;
use App\EpSupportActionLog;
use Carbon\Carbon;
use Log;
use Illuminate\Http\Request;
use stdClass;
use Exception;
use DB;
use Auth;
use Validator;
use Storage;

class SmReceiptMbbController extends Controller {
    const PATH_DIR_UPLOAD_MBB = '/mbb_report/in_pdf';
    use SupplierService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

   
    public function initiateUploadMbbFile(Request $request) {

        if ($request->isMethod('post')) {
            dump($request->all());
        }
        $files = Storage::disk('local-storage')->files(self::PATH_DIR_UPLOAD_MBB);
        session()->flashInput($request->input());
        return view('app_support.upload_mbb_file', [
            'data' => $files,
        ]);
    }

    public function uploadMbbFile(Request $request) {
        $data = collect([]);
        if ($request->isMethod('post')) {

            Validator::make(request()->all(), [
                'uploadFile' => 'required',
                'uploadFile' => 'mimes:pdf'
            ])->validate();
            
            $file = $request->file('uploadFile') ;
            $fileName = $file->getClientOriginalName();
            $pathFolder = storage_path(self::PATH_DIR_UPLOAD_MBB);
            $file->move($pathFolder,$fileName);
            $data->put("status_upload", "success");
            $data->put("file_name", $fileName);

            $actionData = collect([]);
            $actionData->put("file_name", $fileName);
            $actionData->put("path_folder", $pathFolder);
            EpSupportActionLog::saveActionLog("UploadMbbReceiptFile", "UploadFile", $actionData, $data, "Completed");
            
            
            $files = Storage::disk('local-storage')->files(self::PATH_DIR_UPLOAD_MBB);
            $html = '<thead><tr><th>File Name</th></tr></thead><tbody>';
            foreach ($files as $file){
                $html = $html. "<tr><td>$file</td></tr>";
            }
            $html = $html.'</tbody>';
            $data->put("list_pending_html", $html);
        }
        return $data;
    }

}
