<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\EPService;
use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use SSH;
use App\EpSupportActionLog;
use DB;

class HandleMminfItemNotExistSchedule extends Command {
    
    use OSBService;
    use OSBWebService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleMminfItemNotExist';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Get all list item with uom from sc_request_item which is not exists in di_mminf';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
       
        try {
            $limit = 50;
            $list = $this->getListItemNotExistInMminf($limit);
            $this->fixMminfNotExist( $list);
            //$this->mminfTriggerByListID($list);
           
            $logsdata = self::class . '  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail($exc->getTraceAsString());
        }
        
    }
    
     /** This to avoid error in send to 1GFMAS, the program just remove special char. */
     public function getListItemNotExistInMminf($limit) {
        MigrateUtils::logDump(__METHOD__ . ' execute query  .. ');
        $listData = DB::connection('oracle_nextgen_rpt')
        ->select("SELECT distinct tmp.extension_code,tmp.uom FROM (
            SELECT 
                citem.EXTENSION_CODE ,
                ffia.REQUEST_ITEM_ID ,
                uom.UOM_CODE uom ,
                (SELECT count(*) FROM DI_MMINF  mm WHERE mm.MATERIAL_CODE = citem.EXTENSION_CODE   AND mm.ALT_UOM = uom.UOM_CODE ) AS mminf_check_exist ,
                (SELECT count(*) FROM DI_MMINF  mm WHERE mm.MATERIAL_CODE = citem.EXTENSION_CODE ) AS mminf_check 
            FROM fl_delivery_address fda, FL_FULFILMENT_ITEM_ADDR ffia   , cm_item citem ,  sc_request_item sri , pm_uom uom 
            WHERE  ffia.FULFILMENT_ADDR_ID  = fda.DELIVERY_ADDRESS_ID 
            AND sri.REQUEST_ITEM_ID  = ffia.REQUEST_ITEM_ID 
            AND ffia.ITEM_ID  = citem.ITEM_ID  
            AND sri.UOM_ID  = uom.UOM_ID 
            -- AND sri.CHANGED_DATE >= sysdate-30
            -- AND sri.CHANGED_DATE >= to_date('2023-01-01','YYYY-MM-DD')
            AND sri.CHANGED_DATE >= trunc(sysdate)
            AND sri.CHANGED_DATE < (sysdate - interval '10' minute)
        ) tmp 
        WHERE mminf_check_exist = 0 
        -- AND mminf_check = 0 
        AND ROWNUM < ? 
        ORDER BY 1 ASC ",array($limit));
        $listData = collect($listData);

        return $listData;
     }
    
    /**
     * Return OBJECT MMINF table DI_MMINF 
     **/ 
    protected function getMmminfDuplicate(){
        //Get list duplicate
        $listMminfDuplicate = DB::connection('oracle_nextgen_rpt')
        ->select("SELECT * FROM (
                                SELECT MATERIAL_CODE,ALT_UOM, count(*) FROM di_mminf 
                GROUP BY MATERIAL_CODE,ALT_UOM 
                HAVING count(*) > 1 ) tmp ");
        $listMminfDuplicate = collect($listMminfDuplicate);
        if($listMminfDuplicate->count() > 0){
            //get one record only from lisy duplicate
            $mminfObjDup = $listMminfDuplicate->first();
    
            //get detail obj
            $mminfObjDupDtl = DB::connection('oracle_nextgen_rpt')->table('DI_MMINF')
                                ->where('MATERIAL_CODE',$mminfObjDup->material_code)
                                ->where('ALT_UOM',$mminfObjDup->alt_uom)->first();
            return $mminfObjDupDtl;
        
        }else{
            // there is no duplicate mminf. We get oldest changed_date to pickup and overwrite 
            $listMminfOldest= DB::connection('oracle_nextgen_fullgrant')
            ->select("SELECT *
                        FROM di_mminf
                        WHERE changed_date = (
                            SELECT MIN(changed_date)
                            FROM di_mminf
                        ) AND rownum < 2");
            if(count($listMminfOldest) > 0){
                $objmminfOldest = $listMminfOldest[0];
                MigrateUtils::logDump(__METHOD__." >>  duplicate not exist anymore. Pickup oldest record by changed_date : ".json_encode($objmminfOldest));
                return $objmminfOldest;
            }
        }
        return null;
    }

    /** This to avoid error in send to 1GFMAS, the program just remove special char. */
    protected function fixMminfNotExist($list) {
        MigrateUtils::logDump(__METHOD__ . ' ... ');
        foreach ($list as $obj){
            MigrateUtils::logDump(__METHOD__." >>  ".json_encode($obj));

            // Get basic info code item from existing in DI_MMINF
            $mminfItem = DB::connection('oracle_nextgen_rpt')->table('DI_MMINF')->where('MATERIAL_CODE',$obj->extension_code)->first();
            if($mminfItem != null){
                //get detail obj to reused
                $mminfObjDupDtl = $this->getMmminfDuplicate();
                
                if($mminfObjDupDtl){
                    // update this with new info mminf
                    DB::connection('oracle_nextgen_fullgrant')->table('DI_MMINF')
                    ->where('mminf_id',$mminfObjDupDtl->mminf_id)
                    ->update(
                        [
                            'item_id'=>$mminfItem->item_id,
                            'action_code'=>'ADJ',
                            'material_code'=>$mminfItem->material_code,
                            'material_desc'=>$mminfItem->material_desc,
                            'eff_date'=>$mminfItem->eff_date,
                            'exp_date'=>$mminfItem->exp_date,
                            'commodity_name'=>$mminfItem->commodity_name,
                            'alt_uom'=>$obj->uom,  // This to be add in DI_MMINF
                            'material_type'=>$mminfItem->material_type,
                            'material_group'=>$mminfItem->material_group,
                            'changed_date'=>Carbon::now(),
                            'changed_by'=>1,
                            'is_sent'=>'0' 
                        ]
                    );
                    MigrateUtils::logDump(__METHOD__." >>  success updated $mminfItem->material_code with $obj->uom ");
                }
            }else{
                // NULL NOT FOUND
                //get detail obj to reused
                $mminfObjDupDtl = $this->getMmminfDuplicate();
                
                if($mminfObjDupDtl){
                    
                    // Get basic info code item from existing in cm_item and CM_UNSPSC 
                    $resultItem = DB::connection('oracle_nextgen_rpt')->select("SELECT i.item_id,
                        i.EXTENSION_CODE AS MATERIAL_CODE,
                        i.ITEM_NAME ,
                        SUBSTR(i.ITEM_NAME,1,39) AS  material_desc,
                        SUBSTR(i.ITEM_NAME,40,79) AS  material_add_desc,
                        TRUNC(sysdate) AS EFF_DATE ,
                        TO_DATE('9999-12-31', 'YYYY-MM-DD') AS EXP_DATE ,
                        u.UNSPSC_TITLE AS COMMODITY_NAME,
                        CONCAT(SUBSTR(i.EXTENSION_CODE,1,2),'00')  AS MATERIAL_TYPE,
                        CONCAT(SUBSTR(i.EXTENSION_CODE,1,4),'0000')  AS MATERIAL_GROUP 
                        FROM cm_item i, CM_UNSPSC u 
                        WHERE i.UNSPSC_ID  = u.UNSPSC_ID 
                        AND i.EXTENSION_CODE  = ?",array($obj->extension_code));

                    if( count($resultItem) > 0 ){
                        $itemDtlObj = $resultItem[0];
                        // update this with new info mminf
                        DB::connection('oracle_nextgen_fullgrant')->table('DI_MMINF')
                        ->where('mminf_id',$mminfObjDupDtl->mminf_id)
                        ->update(
                            [
                                'item_id'=>$itemDtlObj->item_id,
                                'action_code'=>'CRE',
                                'material_code'=>$itemDtlObj->material_code,
                                'material_desc'=>$itemDtlObj->material_desc,
                                'eff_date'=>$itemDtlObj->eff_date,
                                'exp_date'=>$itemDtlObj->exp_date,
                                'commodity_name'=>$itemDtlObj->commodity_name,
                                'alt_uom'=>$obj->uom,  // This to be add in DI_MMINF
                                'material_type'=>$itemDtlObj->material_type,
                                'material_group'=>$itemDtlObj->material_group,
                                'changed_date'=>Carbon::now(),
                                'changed_by'=>1,
                                'is_sent'=>'0' 
                            ]
                        );
                        MigrateUtils::logDump(__METHOD__." >>  success updated $itemDtlObj->material_code with $obj->uom ");
                    }
                }

            }
            // dd('EXIT');
        }
     }

    protected function mminfTriggerByListID($listReqItemId) {

        $ScRequestItemXML = "";
        foreach ($listReqItemId as $obj){
            MigrateUtils::logDump(__METHOD__." >>  ".json_encode($obj));
            $reqItemId = $obj->request_item_id;
            $ScRequestItemXML = $ScRequestItemXML.
                                "<trig:ScRequestItem>
                                    <trig:requestItemId>$reqItemId</trig:requestItemId>
                                 </trig:ScRequestItem>";
        }

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . $ScRequestItemXML
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        /*
        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        */

        $baseUrlOsbWs = $this->getBaseUrlWsOSB();
        $urlIdentity = "$baseUrlOsbWs/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";
        $commandCurl = "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents;
        MigrateUtils::logDump(__METHOD__." >>  $commandCurl");
        $commands  = [
            $commandCurl ,
        ];
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],json_encode($listReqItemId),'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){
            $line.PHP_EOL; 
        });
        
        sleep(3);
        
        EpSupportActionLog::updateActionLog($actionLog, 'Completed','SchedulerAdmin');
        MigrateUtils::logDump(__METHOD__.'        Completed');


    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleMminfItemNotExistSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
