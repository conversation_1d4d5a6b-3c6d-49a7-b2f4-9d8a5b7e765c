@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> List Of Summary</strong></h1>
        </div>
        <div class="table-responsive">
            <table id="search_list_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th>Campaign Type</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Error Message By Date (Today)</td>
                        <td>
                            <a href="{{ url('/ep/blast/email/error/message') }}"target='_blank'>
                                {{ $errorCount }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>List Of Campaign Completed</td>
                        <td>
                            <a href="{{ url('/ep/blast/email/list/campaign/completed') }}" target='_blank'>
                                {{ $completedCount }}
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <td>List Of Campaign Pending</td>
                        <td>
                            <a href="{{ url('/ep/blast/email/list/campaign/pending') }}" target='_blank'>
                                {{ $pendingCount }}
                            </a>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            TablesDatatables.init();
        });
        App.datatables();
        $('#search_list_datatable').dataTable({
            columnDefs: [],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, 50, -1],
                [10, 20, 30, 50, 'All']
            ]
        });
    </script>
@endsection
