@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Bumi Status Cancellation</strong></h1> 
    </div> 
    <div class="table-responsive">
        <p>
            <code>

                - Update is_bumi status (sm_supplier) 0 <br />
                - Update reg_status_id registration type (sm_appl) 3 <br />
                - Update is_bumi flag (sm_appl) 0 <br />
                - Reset cert mof, make sure only 1 latest mof cert is record_status active. Previous mof cert or bumi cert should be record_status inactive (sm_mof_cert) 0
            </code>
        </p>
    </div>
    <div class="table-responsive">  
        <table id="bumi_status_cancellation_datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">No</th>
                    <th class="text-center">Appl No</th>
                    <th class="text-center">Company Name</th>
                    <th class="text-center">Ep No</th>
                    <th class="text-center">Created Date</th>
                    @if(Auth::user()->isPatcherRolesEp())
                    <th class="text-center">Action</th>
                    @endif
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $rowData => $user)
                <tr>
                    <td class="text-center">{{ ++$rowData }}</td>
                    <td class="text-left content-link">
                        <a class="modal-list-data-action"
                           href="{{url('/find/mofno')}}/{{$user->appl_no }}" 
                           target='_blank' >
                            {{ $user->appl_no }} </a>
                    <td class="text-center">{{ $user->company_name }}</td>
                    <td class="text-center">{{ $user->ep_no }}</td>
                    <td class="text-center">{{$user->created_date}}</td>
                    @if(Auth::user()->isPatcherRolesEp())
                    <td class="text-center"><a target='_blank' href="{{url('/app-support/fix/bumi_status_cancellation')}}?applNo={{$user->appl_no}}" class="text-info">Click to fix</a></td>
                    @endif
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    TablesDatatables.init();
});
App.datatables();
$(document).ready(function () {
    $('#bumi_status_cancellation_datatable').DataTable();
});
</script>
@endsection



