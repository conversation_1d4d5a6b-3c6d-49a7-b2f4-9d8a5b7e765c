@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Softcert Users Not Sync With Personnel As Active</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Company Name</th>
                    <th class="text-center">Ep No.</th>
                    <th class="text-center">Name</th>
                    <th class="text-center">Identification No.</th>
                    <th class="text-center">Is Softcert</th>
                    <th class="text-center">Create Date</th>
                    <th class="text-center">Changed Date</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center">{{ $user->company_name }}</td>
                    <td class="text-center"><a href="{{url("/find/epno")}}/{{ $user->ep_no }}" target='_blank'>{{ $user->ep_no }}</td>
                    <td class="text-center">{{ $user->name }}</td>
                    <td class="text-center"><a href="{{url("/find/icno")}}/{{ $user->identification_no }}" target='_blank'>{{ $user->identification_no }}</td>
                    <td class="text-center">{{ App\Services\EPService::$SOFTCERT_STATUS[ $user->is_softcert ] }}</td>
                    <td class="text-center">{{ $user->created_date }}</td>
                    <td class="text-center">{{ $user->changed_date }}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



