@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianform" action="{{url('/find/gfmas')}}/{{$type}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Apive Fail </strong><small>Hasil Carian : {{$carian}}</small></h1>
            <a href="{{url('/trigger/gfmas/apive')}}/?ep_no={{$carian}}"  target="_blank" 
               class="btn btn btn-default pull-right"  style="margin:5px;">Trigger APIVE</a>
        </div>
        
        
        @if($result && count($result) > 0 )
        @foreach($result as $obj)
        <div class="block collapse panel-xml" id="row_{{$obj["key"]}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$obj["FileName"]}}</h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{$obj["contents"]}}</code>
            </pre>
        </div>
        @endforeach
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">FileName</th>
                        <th class="text-center">StatusSent</th>
                        <th class="text-center">Transaction Date</th>
                        <th class="text-center">Status Desc</th>
                        <th class="text-center">TransID</th>
                        <th class="text-center">Response 1GFMAS</th>
                        <th class="text-center">Details Link</th>
                    </tr>
                </thead>
                <tbody>
                @foreach($result as $obj)
                    <tr>
                        <td class="text-center text-success">
                            <strong>
                                <a class="text-danger" href="{{url('/find/osb/batch/file')}}/{{$obj["FileName"]}}" target="_blank" >{{$obj["FileName"]}}</a>
                            </strong>

                            <a class="hide" href="javascript:void(0)" data-toggle="collapse" data-target="#row_{{$obj["key"]}}" >{{$obj["FileName"]}}</a>
                            @if($obj["contentsHasSpecialChar"] === true)
                            <span class="text-danger bolder"><i class="fa fa-exclamation-triangle"></i> </span>
                            @endif
                            </td>
                        <td class="text-center">{{$obj["StatusSent"]}}</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null){{$obj["InfoDetails"]->trans_date}}@endif</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null){{$obj["InfoDetails"]->status_desc}}@endif</td>
                        <td class="text-center">@if($obj["InfoDetails"] != null)
                            <a class="text-success" href="{{url('find/osb/detail/log')}}/?cari={{$obj["InfoDetails"]->trans_id}}" target="_blank">{{$obj["InfoDetails"]->trans_id}}</a>@endif
                        </td>
                        <td>
                            @if($obj["ApoveFileName"] != null && $obj["ApoveFailedUpdatedInEp"] == false )
                            <span class="text-left text-success"><i class="gi gi-ok  text-success" ></i> {{$obj["StatusApove"]}}</span><br />
                            @elseif($obj["ApoveFileName"] != null && $obj["ApoveFailedUpdatedInEp"] == true )
                            <span class="text-left text-warning"><i class="gi gi-circle_exclamation_mark  text-warning" ></i> {{$obj["StatusApove"]}}</span><br />
                            @endif

                            @if($obj["AperrFileName"] != null)
                            <span class="text-left text-success"><i class="fa fa-close text-success" ></i> <strong>{{$obj["StatusAperr"]}}</strong></span>
                            <br />
                            @endif

                            @if($obj["contentsHasSpecialChar"] === true)
                            <span class="text-danger bolder"><i class="fa fa-exclamation-triangle"></i>  (Contents APIVE file has special character, Failed validation process in IGFMAS)</span>
                            @endif
                        </td>
                        <td class="text-center">
                            @if($obj["ApoveFileName"] != null && $obj["ApoveFailedUpdatedInEp"] == false )
                            <a class="text-success" href="{{url('/find/osb/batch/file')}}/{{$obj["ApoveFileName"]}}" target="_blank">{{$obj["ApoveFileName"]}}</a><br />
                            @elseif($obj["ApoveFileName"] != null && $obj["ApoveFailedUpdatedInEp"] == true )
                            <a class="text-warning" href="{{url('/find/osb/batch/file')}}/{{$obj["ApoveFileName"]}}" target="_blank">{{$obj["ApoveFileName"]}}</a><br />
                            @endif
                            
                            @if($obj["AperrFileName"] != null)
                            <strong>
                            <a class="text-danger" href="{{url('/find/osb/batch/file')}}/{{$obj["AperrFileName"]}}" target="_blank" >{{$obj["AperrFileName"]}}</a>
                            </strong>
                            @endif

                            
                        </td>
                            
                    </tr>
                @endforeach    
                </tbody>
            </table>
            <br />
        </div>
        @elseif($result != null)
            <div class="header-section">
                <h1>
                    <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                    <small>Tidak dijumpai!</small>
                </h1>
            </div>
        @else
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian Apive (MasterDataVendor)<br>
                    <small>Masukkan eP No. pada carian diatas...</small>
                </h1>
            </div>
        @endif
            
        

    </div>

                
    </div>
        
    
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection