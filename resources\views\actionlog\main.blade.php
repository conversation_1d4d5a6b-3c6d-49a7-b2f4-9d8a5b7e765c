@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
    <style>
        #loadingModal .modal-content {
            background-color: transparent;
            border: none;
            box-shadow: none;
        }

        #loadingModal .modal-body {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }

        #loadingModal .fa-spinner {
            color: #fff;
        }

        #loadingModal .modal-dialog {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
        }
    </style>
    <div class="row">
        <div class="col-md-12">
            <div class="block">
                <div class="block-title">
                    <h4>eP Support Action Log</h4>
                </div>
                <div class="block-body">
                    <form id="myForm" class="form-horizontal">
                        {{ csrf_field() }}
                        <div class="form-group">
                            <label class="col-md-2 control-label">Action Type</label>
                            <div class="col-md-4">
                                <select id="actionType" name="actionType" class="form-control">
                                    <option value="">Select Action Type</option>
                                </select>
                            </div>
                            <label class="col-md-2 control-label">Action Name</label>
                            <div class="col-md-4">
                                <select id="actionName" name="actionName" class="form-control">
                                    <option value="">Select Action Name</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">User</label>
                            <div class="col-md-4">
                                <select id="actionUser" name="actionUser" class="form-control">
                                    <option value="">Select User</option>
                                </select>
                            </div>
                            <label class="col-md-2 control-label">Action Date</label>
                            <div class="col-md-4">
                                <input type="text" class="form-control" id="datepicker" name="actionDate"
                                    placeholder="Select Action Date" data-date-format="dd/mm/yyyy">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-2 control-label">Action Data</label>
                            <div class="col-md-10">
                                <textarea id="actionData" name="actionData" class="form-control" placeholder="Enter Action"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-md-offset-2 col-md-10">
                                <button id="searchButton" type="submit" class="btn btn-primary">Search</button>
                                <button id="resetButton" type="reset" class="btn btn-info">Reset</button>
                            </div>
                        </div>
                    </form>
                    <!-- Loading Modal -->
                    <div class="modal fade" id="loadingModal" tabindex="-1" role="dialog"
                        aria-labelledby="loadingModalLabel" aria-hidden="true">
                        <div class="modal-dialog" role="document">
                            <div class="modal-content">
                                <div class="modal-body">
                                    <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div id="result" class="table-responsive">
        <table id="result-table" class="table table-striped table-vcenter">
        </table>
    </div>
    <div id="errorDiv" style="display: none;"></div>

    <div id="myModal" class="modal fade" role="dialog">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h4 class="modal-title">Action Data</h4>
                </div>
                <div class="modal-body">
                    <h5>Action Parameter</h5>
                    <pre>
                        <code id="actionParameter" style='float: left; color:#fff;'></code>
                    </pre>
                    <h5>Action Data</h5>
                    <pre>
                        <code id="actionData" style='float: left; color:#fff;'></code>
                    </pre>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        $(document).ready(function() {
            $("#datepicker").datepicker({
                dateFormat: "dd/mm/yy"
            });


            $(function() {
                TablesDatatables.init();
            });
            App.datatables();

            function fetchData() {
                console.log('Fetching data...');

                $('#loadingModal').modal('show');
                $('#searchButton').prop('disabled', true);

                $.ajax({
                    url: APP_URL + '/actionlog/fetch',
                    type: 'GET',
                    success: function(data) {

                        // Extract values
                        var actionTypeName = data.actionTypeName;
                        var userLists = data.userLists;

                        $('#actionType').empty();
                        $('#actionName').empty();
                        $('#actionUser').empty();

                        // Set default options
                        $('#actionType').append($('<option></option>').attr('value', '').text('Select Action Type'));
                        $('#actionName').append($('<option></option>').attr('value', '').text('Select Action Name'));
                        $('#actionUser').append($('<option></option>').attr('value', '').text('Select User'));

                        // Populate fields
                        $.each(actionTypeName, function(key, value) {
                            $('#actionType').append($('<option></option>').attr('value', key).text(key));
                        });

                        // Add event listener to actionType field
                        $('#actionType').change(function() {
                            var selectedActionType = $(this).val();

                            // Clear actionName field
                            $('#actionName').empty();

                            // Add default option
                            $('#actionName').append($('<option></option>').attr('value', '').text('Select Action Name'));

                            // Populate actionName field with values corresponding to selected actionType
                            $.each(actionTypeName[selectedActionType], function(index, value) {
                                $('#actionName').append($('<option></option>').attr('value', value).text(value));
                            });
                        });

                        $.each(userLists, function(key, value) {
                            $('#actionUser').append($('<option></option>').attr('value', value).text(value));
                        });
                    },
                    complete: function() {
                        $('#searchButton').prop('disabled', false);
                        $('#loadingModal').modal('hide');
                    },
                    error: function(error) {
                        // Handle error here
                        console.log(error);
                        $('#loadingModal').modal('hide');
                        $('#searchButton').prop('disabled', false);
                    }
                });
            }

            fetchData();

            $("#myForm").off('submit').on('submit', function(event) {
                event.preventDefault();

                $('#loadingModal').modal('show');
                $('#result').hide();

                // Disable the search button
                $('#searchButton').prop('disabled', true);

                $.ajax({
                    url: APP_URL + '/actionlog/search',
                    method: 'POST',
                    data: $(this).serialize(),
                    success: function(response) {
                        $('#result').show();

                        if ($.fn.DataTable.isDataTable('#result-table')) {
                            // console.log('DataTable instance already exists. Destroying it...')
                            try {
                                var table = $('#result-table').DataTable();
                                table.clear().destroy();
                            } catch (error) {
                                console.error('Error destroying DataTable instance:', error);
                            }
                        }
                        $('#result-table').DataTable({
                            data: response.resultLists,
                            columns: [
                                { title: "ID", data: "id" },
                                { title: "Action Name", data: "action_name" },
                                { title: "Action Type", data: "action_type" },
                                { title: "Action Status", data: "action_status" },
                                {
                                    title: "Action Data",
                                    data: "action_data",
                                    render: function(data, type, row) {
                                        return '<pre style="width: 100%; height: 108px; float: left; color:#fff;"><code>' + data + '</code></pre>';
                                    },
                                    width: '650px'
                                },
                                { title: "Created At", data: "created_at"  },
                                { title: "Created By", data: "created_by" },
                                {
                                    title: "Action",
                                    data: null,
                                    defaultContent: '<a class="btn btn-info btn-sm action_table_more_info" data-toggle="tooltip" title="" data-original-title="More Info..."><i class="fa fa-expand"></i></a>'
                                },
                            ],
                            order: [
                                [0, 'desc']
                            ] // Sort by the first column (ID) in descending order
                        });

                        $('#result-table').on('click', '.action_table_more_info', function() {
                            var data = $('#result-table').DataTable().row($(this).parents('tr')).data();
                            $('#myModal .modal-title').text(data.action_type + ' - ' + data.action_name);
                            $('#myModal #actionParameter').text(data.action_parameter);
                            $('#myModal #actionData').text(data.action_data);
                            $('#myModal').modal('show');
                        });

                        $(function() {
                            $('[data-toggle="tooltip"]').tooltip()
                        });
                    },
                    complete: function() {
                        $('#searchButton').prop('disabled', false);
                        $('#loadingModal').modal('hide');
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        $('#errorDiv').show().html('<div class="alert alert-danger">Error: ' + errorThrown + '</div>');
                        $('#loadingModal').modal('hide');
                        $('#searchButton').prop('disabled', false);
                    }
                });
            });
        });
    </script>
@endsection
