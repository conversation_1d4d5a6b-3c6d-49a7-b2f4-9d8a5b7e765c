
{{-- GROUP CUSTOMER SERVICE OR ANY USERS NOT IN TECHNICAL (APPROVAL LISTED) --}}
{{-- If user group as codification, this user cannot view  customer service menu features. Just for item features only--}}
@if(Auth::user()->isAllowAccessBasic() || Auth::user()->isCodiUsersEp() || Auth::user()->isUsersReport() )
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    @if(Auth::user()->isAllowAccessBasic())
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Support Customer Service"><i class="gi gi-global"></i></a></span>
        <span class="sidebar-header-title">Customer Service </span>
    </li>
    @if(Auth::user()->isCSUsersEp())
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Dashboard CS</span></a>
        <ul>
            <li class="{{ Request::is('dashboard/cs') ? 'active' : '' }}">
                <a href="{{url("/dashboard/cs")}}"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">CRM - CS Cases</span></a>
            </li>
            <li class="{{ Request::is('dashboard/crm/cs/main') ? 'active' : '' }}">
            <a href="{{url("/dashboard/crm/cs/main")}}"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - CS Pending Input</span></a>
            </li>
            <li class="hide {{ Request::is('/dashboard/crm/ageingcases') ? 'active' : '' }}">
                <a href="{{url("/dashboard/crm/ageingcases")}}"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">CRM - Total Summary Cases</span></a>
            </li>
            <li class="{{ Request::is('/dashboard/crm/cs/talkdesk') ? 'active' : '' }}">
                <a href="{{url("/dashboard/crm/cs/talkdesk")}}"><i class="fa fa-upload sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">POMS - Talkdesk</span></a>
            </li>                                
        </ul>  

        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">QT Module</span></a>
        <ul>
            <li class="{{ Request::is('qt/dashboard') ? 'active' : '' }}">
            <a href="{{url("/qt/dashboard")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">QT Dashboard</span></a>
            </li>
            <li class="{{ Request::is('find/qt/checkingBSV') ? 'active' : '' }}">
            <a href="{{url("/find/qt/checkingBSV")}}"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">QT Summary</span></a>
            </li>                                   
        </ul>  

        <li class="{{ Request::is('find/bpm/task/docno') ? 'active' : '' }}">
            <a href="{{url("/find/bpm/task/docno")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Tugasan</span></a>
        </li>  
    </li> 
    @endif

    {{-- Exclude ainnatiqah because allow login EPSS to view on reporting only , by refer email Fauzal:  9/8/2021 --}}
    @if(Auth::user()->user_name <> 'ainatiqah')
    <li class="{{ Request::is('find/identity') ? 'active' : '' }}">
        <a href="{{url("/find/identity")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Identity</span></a>
    </li>
    <li class="{{ Request::is('find/userlogin') ? 'active' : '' }}">
        <a href="{{url("/find/userlogin")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">User Login</span></a>
    </li>
    <li class="{{ Request::is('find/uom') ? 'active' : '' }}">
        <a href="{{url("/find/uom")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">UOM</span></a>
    </li>
    <li class="{{ Request::is('find/supplier') ? 'active' : '' }}">
        <a href="{{url("/find/supplier")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Supplier</span></a>
    </li>
    <li class="{{ Request::is('find/orgcode') ? 'active' : '' }}">
        <a href="{{url("/find/orgcode")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Organization</span></a>
    </li>
    <li class="{{ Request::is('find/trans/track/docno') ? 'active' : '' }}">
        <a href="{{url("/find/trans/track/docno")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Tracking Diary</span></a>
    </li>
    @endif

    @if(collect(Config::get('constant.users_cr_mgmt'))->contains(Auth()->user()->user_name) == 1)
    <li class="{{ Request::is('find/contract/') ? 'active' : '' }}">
        <a href="{{url("/find/contract")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Carian Kontrak</span></a>
    </li> 
    @endif
    
    @endif  {{-- END FOR customer service menu   Auth::user()->isAllowAccessBasic() --}}

    {{-- START FOR team codification menu--}}
    @if(Auth::user()->isCodiUsersEp())
    <li class="{{ Request::is('find/identity') ? 'active' : '' }}">
        <a href="{{url("/find/identity")}}"><i class="hi hi-search sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Identity</span></a>
    </li>
    <li class="{{ Request::is('find/item') ? 'active' : '' }}">
        <a href="{{url("/find/item")}}"><i class="hi hi-search sidebar-nav-icon"></i>Item</a>
    </li>
    <li class="{{ Request::is('find/items/unspsc') ? 'active' : '' }}">
        <a href="{{url("/find/items/unspsc")}}"><i class="hi hi-search sidebar-nav-icon"></i>UNSPSC Item</a>
    </li>
    <li class="{{ Request::is('find/bpm/task/docno') ? 'active' : '' }}">
        <a href="{{url("/find/items/supplier")}}"><i class="hi hi-search sidebar-nav-icon"></i>Item Supplier</a>
    </li>
    <li class="{{ Request::is('find/items/codi-task') ? 'active' : '' }}">
        <a href="{{url("/find/items/codi-task")}}"><i class="hi hi-search sidebar-nav-icon"></i>Item Task History</a>
    </li>
    <li class="{{ Request::is('find/codi/supplier') ? 'active' : '' }}">
        <a href="{{url("/find/codi/supplier")}}"><i class="hi hi-search sidebar-nav-icon"></i>Supplier</a>
    </li>
    <li class="{{ Request::is('find/byname') ? 'active' : '' }}">
        <a href="{{url("/find/byname")}}"><i class="hi hi-search sidebar-nav-icon"></i>Carian Nama Pembekal</a>
    </li>
    @endif
    {{-- END FOR team codification menu--}}

    @if(Auth::user()->isUsersReport() )
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix">
        <a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Revenue Report"><i class="gi gi-charts"></i></a></span>
        <span class="sidebar-header-title">REPORT </span>
    </li>
    <li class="{{ Request::is('report/revenue/daily-stat-transaction') ? 'active' : '' }}">
        <a href="{{url("/report/revenue/daily-stat-transaction")}}"></i>Today Transaction Revenue</a>
    </li>
    <li class="{{ Request::is('report/payment') ? 'active' : '' }}">
        <a href="{{url("/report/payment")}}"></i>Cumulative Revenue YTD&MTD</a>
    </li>
    <li class="{{ Request::is('report/revenue/daily') ? 'active' : '' }}">
        <a href="{{url("/report/revenue/daily")}}"></i>Daily Summary Revenue</a>
    </li>
    <li class="{{ Request::is('report/revenue/pending-transaction') ? 'active' : '' }}">
        <a href="{{url("/report/revenue/pending-transaction")}}"></i>Pending Transaction</a>
    </li>
    @endif
    
    

</ul>  
@endif 


