<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;

class HandleSmSoftcertFixRenewal extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSmSoftcertFixRenewal';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To update is_softcert field in SM_PERSONNEL change status 7 to 1. Softcert still not expired yet.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__ . ' starting ..');
        $this->updateIsSoftcertRenewalToIssue();
        $this->updateIsSoftcertExpiredToStatusRenewal();
        MigrateUtils::logDump(__METHOD__ . ' completed ');
    }


    /**
     * to set personnel softcert as 1 (issue) if still not expired yet. This issue bug on eP cause not allow Signing if status softcert as 7.
     */
    private function updateIsSoftcertRenewalToIssue(){
        MigrateUtils::logDump(__METHOD__ . ' starting... ');
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $script = " 
                    SELECT
                        p.personnel_id,
                        s.COMPANY_NAME ,
                        s.EP_NO ,
                        p.name,
                        p.identification_no,
                        p.ep_role,
                        p.is_softcert ,
                        p.created_date,
                        p.changed_date,
                        dc.valid_to
                    FROM
                        sm_supplier s,
                        sm_personnel p ,
                        sm_softcert_request sr ,
                        pm_digi_cert dc
                    WHERE
                        s.latest_appl_id = p.appl_id
                        AND p.user_id = sr.user_id
                        AND s.SUPPLIER_ID = sr.supplier_id
                        AND sr.softcert_request_id = dc.SOFTCERT_REQUEST_ID
                        AND p.is_softcert = 7
                        AND trunc(dc.valid_to) > trunc(sysdate + 1)
                        AND sr.record_status = 1 
                        AND NOT EXISTS (SELECT 1 FROM SM_WORKFLOW_STATUS w WHERE w.doc_id = s.LATEST_APPL_ID AND STATUS_ID = 20252 AND is_current = 1) 
                   
                ";
            $listRecords = DB::connection('oracle_nextgen_rpt')->select($script);
            
            MigrateUtils::logDump('Total records personnel changed to renewal status: '.count($listRecords));
            $listId = collect($listRecords)->pluck('personnel_id')->toArray();
            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listId) { 
                MigrateUtils::logDump(__METHOD__ . ' > list if personnel ID selected  : '.json_encode($listId));
                DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL')
                        ->whereIn('personnel_id',$listId)
                        ->update(['is_softcert'=>1,'changed_date'=>Carbon::now()]);
                MigrateUtils::logDump(__METHOD__ . ' > Done updated records in SM_PERSONNEL.');
            }); 
            
            $actionName = 'PatchData';
            $actionType = 'Script';
            
            $dataParam = collect();
            $dataParam->put("action","Update records in table SM_PERSONNEL");
            $dataParam->put("table","SM_PERSONNEL");
            $dataParam->put("list_personel_id",$listId);
            $dataParam->put("criteria","Softcert Issue not expired but softcert status changed to Renewal (7)");
            $dataParam->put("remark","List personnel record to be update softcert status 7 to 1");
            $dataParam->put("script_sql",$script);
            //dump($dataParam);
            $dataLog = collect();
            $dataLog->put("remark","List personnel record to be update softcert status 7 to 1");
            $dataLog->put("data",$listId);
            $dataLog->put("script_sql",$script);

            EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");

            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        MigrateUtils::logDump(__METHOD__ . ' completed ');
    }


    /**
     * To set is_softcert personnel as 7 (renewal) if already expired and last workflow to do pending payment softcert
     */
    private function updateIsSoftcertExpiredToStatusRenewal(){
        MigrateUtils::logDump(__METHOD__ . ' starting... ');
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $script = " 
                    SELECT
                    p.personnel_id,
                    s.COMPANY_NAME ,
                    s.EP_NO ,
                    p.name,
                    p.identification_no,
                    p.ep_role,
                    p.is_softcert ,
                    p.created_date,
                    p.changed_date,
                    dc.valid_to
                FROM
                    sm_supplier s,
                    sm_personnel p ,
                    sm_softcert_request sr ,
                    pm_digi_cert dc
                WHERE
                    s.latest_appl_id = p.appl_id
                    AND p.user_id = sr.user_id
                    AND s.SUPPLIER_ID = sr.supplier_id
                    AND sr.softcert_request_id = dc.SOFTCERT_REQUEST_ID
                    AND p.record_status = 1
                    AND p.is_softcert = 1
                    AND trunc(dc.valid_to) < trunc(sysdate)
                    AND sr.record_status = 1 
                    AND  EXISTS (SELECT 1 FROM SM_WORKFLOW_STATUS w WHERE w.doc_id = s.LATEST_APPL_ID AND STATUS_ID = 20252 AND is_current = 1)
                   
                ";
            $listRecords = DB::connection('oracle_nextgen_rpt')->select($script);
            
            MigrateUtils::logDump('Total records personnel changed to renewal status: '.count($listRecords));
            $listId = collect($listRecords)->pluck('personnel_id')->toArray();
            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listId) { 
                
                MigrateUtils::logDump(__METHOD__ . ' > list if personnel ID selected : '.json_encode($listId));
                DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL')
                        ->whereIn('personnel_id',$listId)
                        ->update(['is_softcert'=>7,'changed_date'=>Carbon::now()]);
                MigrateUtils::logDump(__METHOD__ . ' > Done updated records in SM_PERSONNEL.');
            }); 
            
            $actionName = 'PatchData';
            $actionType = 'Script';
            
            $dataParam = collect();
            $dataParam->put("action","Update records in table SM_PERSONNEL");
            $dataParam->put("table","SM_PERSONNEL");
            $dataParam->put("list_personel_id",$listId);
            $dataParam->put("criteria","Softcert expired but softcert status as (1). Then change to Renewal (7)");
            $dataParam->put("remark","List personnel record to be update softcert status 1 to 7");
            $dataParam->put("script_sql",$script);
            //dump($dataParam);
            $dataLog = collect();
            $dataLog->put("remark","List personnel record to be update softcert status 7 to 1");
            $dataLog->put("data",$listId);
            $dataLog->put("script_sql",$script);
            
            EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");
           
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        MigrateUtils::logDump(__METHOD__ . ' completed ');
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
