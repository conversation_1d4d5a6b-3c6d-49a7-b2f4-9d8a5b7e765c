.popover {
  max-width: 500px;
}

#page-content {
  background: #1b213b;
}

#chart_node,
#chart_top10_user,
#chart_top10_url {
  max-width: 500px;
  margin: 0px auto;
}

div#chart-box {
  background: #262d47;
  padding: 25px 25px;
  border-radius: 4px;
  max-width: 600px;
  margin: 10px auto;
  text-align: center;
  color: #fff;
}

div#chart-box-table {
  background: #262d47;
  padding: 25px 25px;
  border-radius: 4px;
  max-width: 800px;
  margin: 10px auto;
  text-align: center;
}

table.table,
table.table tbody tr {
  background: #262d47;
  color: #fff;
  border-style: none;
}

div.dataTables_length,
div.dataTables_filter {
  background: transparent;
}

div.dataTables_info,
div.dataTables_paginate {
  background: greenyellow;
  font-weight: bold;
  padding: 1px 3px 3px 3px;
  border-radius: 4px;
}

table thead {
  vertical-align: middle;
  background: greenyellow;
  color: #000;
}

div#log-bg-table {
  background: #fff;
  box-shadow: 0 0 15px 1px rgb(227, 235, 230);
  border-style: none;
  border-radius: 5px;
  padding: 3px 20px 3px 20px;
}

div#chart-box-table span.chart-title {
  font-size: 14px;
  font-weight: bold;
  color: #fff;
}

div.log-header {
  background: -webkit-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: -o-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: -moz-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: linear-gradient(-180deg, #8b5f82, #0e0f0f);
  display: inline-block;
  box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.35);
  overflow: hidden;
  border-radius: 5px;
  border-style: none;
  width: 100%;
  color: white;
  padding: 5px 10px;
}

div.log-header-title {
  padding: 5px 10px;
  text-align: left;
  left: 5px;
}

div.log-header-title span {
  font-size: 20px;
  font-weight: bold;
}

div.log-header-menu {
  padding: 20px 5px;
  text-align: right;
  right: 10px;
}

div.log-header-menu a {
  text-decoration: none;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

div.log-header-menu span.active {
  padding: 1px 3px 1px 3px;
  border-radius: 5px;
  background: greenyellow;
  color: black;
  box-shadow: 0 0 15px 1px rgb(234, 241, 237);
  font-weight: bold;
}

div.log-header-menu a:hover {
  padding: 2px 2px 2px 2px;
  color: greenyellow;
}

.log-trace-icon {
  border: 5px solid rgb(109, 110, 110);
  border-radius: 5px;
  padding: 5px 5px 5px 5px;
  box-shadow: 0 0 15px 1px rgb(6, 197, 85);
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  background: -webkit-linear-gradient(-90deg, #2de47f, #84e04f);
  background: -o-linear-gradient(-90deg, #2de47f, #84e04f);
  background: -moz-linear-gradient(-90deg, #2de47f, #84e04f);
  background: linear-gradient(-90deg, #2de47f, #84e04f);
}

.log-search-panel {
  background: #262d47;
  box-shadow: 0 0 15px 1px rgb(227, 235, 230);
  border-style: none;
  border-radius: 5px;
  color: #fff;
}

table#log_table tbody tr:hover {
  background: #3e3e3f;
}

div.modal-content,
div.modal-footer {
  background: #1b213b;
}

.modal-title {
  color: #fff;
}

div button.btn,
a.btn {
  background: greenyellow;
  color: #000;
}

div button.btn:hover,
a.btn:hover {
  background: black;
  color: #fff;
}

.log_word_break {
  width: 30%;
  word-break: break-all;
}
.log_text_center {
  text-align: center;
}

#chart-box-table div.patch-header {
  text-align: left;
  font-size: 14px;
  font-weight: bold;
  color: #fff;
  padding: 1px 15px 5px 15px;
}
