@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
@if($connectionType === 'ep')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/ep') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/receiver/ep') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/ep') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@elseif($connectionType === 'crm')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/wsnotify/dashboard/crm') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li class="active">
                <a href="{{ url('/wsnotify/receiver/crm') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/crm') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@endif
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="block">
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        @if($connectionType === 'ep')
            <h3>EP NOTIFICATION: LIST OF RECEIVERS</h3>
        @elseif($connectionType === 'crm')
            <h3>CRM JBAL NOTIFICATION: LIST OF RECEIVERS</h3>
        @endif
        <table id="basic-datatable" class="table table-vcenter table-striped table-bordered">
            <thead>
                <tr>
                    <th class="text-center">ID</th>
                    <th class="text-center">GROUP/PERSONAL</th>
                    <th class="text-center">NAME</th>
                    <th class="text-center">WHATAPP ID</th>
                    <th class="text-center">STATUS</th>
                    <th class="text-center">ACTION</th>
                </tr>
            </thead>
            @if($listReceiver != null)
            <tbody>
                @foreach($listReceiver as $obj)
                <tr>
                    <td class="text-left">{{ $obj->id }}
                    <td class="text-left">{{ $obj->is_group_personal }}</td>
                    <td class="text-left">{{ $obj->receiver_name }}</td>
                    <td class="text-left">{{ $obj->whatapp_id }} @if(strlen($obj->whatapp_id) == 0) Please ask middleware team to find and update Whatapp ID to ensure succesfully send notification whatapp. @endif</td>
                    <td class="text-left">{{ $obj->status }}</td>
                    <td class="text-center action_receiver">
                        <div class="btn-group btn-group-xs">
                            <a class="btn btn-yellow update_receiver"  title="" data-original-title="Update"
                               href="#modal_update_receiver" data-toggle="modal" data-id="{{ $obj->id }}" data-status="{{ $obj->status }}"
                               data-group="{{ $obj->is_group_personal }}" data-name="{{ $obj->receiver_name }}" 
                               data-connection="{{$connectionType}}" data-whatsapp-id="{{$obj->whatapp_id}}"> Update</a>
                        </div>
                    </td>
                </tr>
                @endforeach
            </tbody>
            @endif
        </table>
    </div>
    <button class="btn btn-sm btn-green pull-right add-receiver" style="margin-right: 10px">Add Receiver</button>
    <br/><br/>
</div>
<div id="modal_update_receiver" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> UPDATE RECEIVER </h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <input type="hidden" id="receiver_id" name="receiver_id"/>
                            <input type="hidden" id="connection" name="connection"/>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group/Personal </label>
                                <div class="input-group">
                                    <select id="update_group_receiver" name="update_group_receiver" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        <option value="group" >Group</option>
                                        <option value="personal" >Personal</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Receiver Name </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="update_name_receiver" name="update_group_receiver" value=""/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Status </label>
                                <div class="input-group">
                                    <select id="update_status_receiver" name="update_status_receiver" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        <option value="Active" >Active</option>
                                        <option value="In-Active" >In-Active</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Whatsapp Id </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="update_whatsapp_id" name="update_whatsapp_id" value=""/>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_receiver"><i class="gi gi-ok_2"></i> Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="modal_add_receiver" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> ADD NEW RECEIVER </h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <input type="hidden" id="receiver_id" name="receiver_id"/>
                            <input type="hidden" id="connections" name="connections" value="{{$connectionType}}"/>
                            <div class="form-group">
                                <label class="col-md-3 control-label">Group/Personal </label>
                                <div class="input-group">
                                    <select id="add_group_receiver" name="add_group_receiver" class="form-control" required="true">
                                        <option value="">Please select</option>
                                        <option value="group" >Group</option>
                                        <option value="personal" >Personal</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group" id="div_group" style="display: none">
                                <label class="col-md-3 control-label">Receiver Name </label>
                                <div class="input-group col-md-6">
                                    <input type="text" class="form-control" id="add_name_receiver_group" name="add_name_receiver_group" value=""/>
                                </div>
                            </div>
                            <div class="form-group" id="div_personal" style="display: none">
                                <label class="col-md-3 control-label">Receiver Name </label>
                                <div class="input-group col-md-6">
                                    <input type="number" class="form-control" id="add_name_receiver_personal" name="add_name_receiver_personal" value=""/>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_add_receiver"><i class="gi gi-ok_2"></i> Add</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
<!-- END Content -->
@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });</script>
<script>
    $('td.action_receiver').on("click", 'a.update_receiver', function () {
        var receiverId = $(this).attr('data-id');
        var groupPersonal = $(this).attr('data-group');
        var receiverName = $(this).attr('data-name');
        var receiverStatus = $(this).attr('data-status');
        var connection = $(this).attr('data-connection');
        var whatappId = $(this).attr('data-whatsapp-id');
        $("#receiver_id").val(receiverId);
        $("#update_group_receiver").val(groupPersonal);
        $("#update_name_receiver").val(receiverName);
        $("#update_status_receiver").val(receiverStatus);
        $("#connection").val(connection);
        $("#update_whatsapp_id").val(whatappId);
    });

    $('div.form-actions').on("click", 'button.action_update_receiver', function () {
        var receiverId = $("#receiver_id").val();
        var groupPersonal = $("#update_group_receiver option:selected").val();
        var receiverName = $("#update_name_receiver").val();
        var receiverStatus = $("#update_status_receiver").val();
        var csrf = $("input[name=_token]").val();
        var action = 'update';
        var connection = $("#connection").val();
        var whatappId = $("#update_whatsapp_id").val();

        $('#modal_update_receiver').modal('hide');

        $.ajax({
            url: "/wsnotify/receiver/"+connection,
            type: "POST",
            data: {"_token": csrf, "action": action, "receiverId": receiverId,
                "groupPersonal": groupPersonal, "receiverName": receiverName, "receiverStatus": receiverStatus, "whatappId" : whatappId},
        }).done(function (resp) {
            console.log(resp);
            if (resp.status === 'success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Successfully Updated!");
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Updated Failed! Please try again.");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
    $('button.add-receiver').on("click", function () {
        $('#modal_add_receiver').modal('show');

        $("#add_group_receiver").bind("change", function () {
            var typeGroup = $(this).find(":selected").val();

            if (typeGroup === 'personal') {
                $('#div_group').hide();
                $('#div_personal').show();

            } else {
                $('#div_personal').hide();
                $('#div_group').show();
            }
        });
    });
    $('div.form-actions').on("click", 'button.action_add_receiver', function () {
        var groupPersonal = $("#add_group_receiver option:selected").val();
        var receiverNameGroup = $("#add_name_receiver_group").val();
        var receiverNamePersonal = $("#add_name_receiver_personal").val();
        var csrf = $("input[name=_token]").val();
        var action = 'add';
        var connections = $("#connections").val();
        var receiverName = '';
        if(groupPersonal == 'personal'){
            receiverName = receiverNamePersonal;
        }else{
            receiverName = receiverNameGroup;
        }
        console.log('connections ' + connections);
        $('#modal_add_receiver').modal('hide');

        $.ajax({
            url: "/wsnotify/receiver/"+connections,
            type: "POST",
            data: {"_token": csrf, "action": action, 
                "groupPersonal": groupPersonal, "receiverName": receiverName},
        }).done(function (resp) {
            console.log(resp);
            if (resp.status === 'success') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Successfully Add New Receiver!");
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Add Failed! Please try again.");
            }
            setTimeout(location.reload.bind(location), 1000);
        });
    });
</script>
@endsection