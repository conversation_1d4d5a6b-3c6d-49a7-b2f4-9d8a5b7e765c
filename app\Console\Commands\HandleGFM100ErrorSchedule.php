<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\EPService;
use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\SupplierFullGrantService;
use SSH;
use App\EpSupportActionLog;

class HandleGFM100ErrorSchedule extends Command {
    
    use OSBService;
    use OSBWebService;
    use FulfilmentService;
    use SupplierService;
    use SupplierFullGrantService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleGFM100Error';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Any error response Kod barang tidak wujud di 1GFMAS or Unit ukuran tidak wujud di dalam 1GFMAS bagi kod barang, will auto trigger MMINF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d H:i:s');
        // $dateStart = $dtStartTime->subHour(1)->format('Y-m-d H:i:s');
        $dateStart = $dtStartTime->subMinutes(6);
        // $dateStart = '2022-11-06 23:00:00';
        // $dateEnd = '2022-11-07 10:15:00';

        MigrateUtils::logDump('Start in '.self::class .'Date Start: '.$dateStart.', Date End: '.$dateEnd);
       
        try {
            $list = $this->getListWsErrItemCodeInGFM100ByDateRange($dateStart, $dateEnd);
            $listData = collect($list);
           
            
            /* get list request item id in PR/CR based on error item code only  -> update MMINF is_send = 0 */
            $this->getAllRequestItemErrorOnlyByDocNoToUpdateMMINF($listData);

       
            /* get all list request item id in PR/CR  and trigger MMINF */
            $listRequestItemIdAll = $this->getAllRequestItemByDocNo($listData);
            
            
            MigrateUtils::logDump('Total requestItemId: '.count($listRequestItemIdAll));
            
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail($exc->getTraceAsString());
        }
        
    }
    
    /** From error cause of  Unit ukuran tidak wujud  OR Kod barang tidak wujud, 
     * program retrieve record in DI_MMINF to update is_sent = 0 to resend back.
    */
    protected function getAllRequestItemErrorOnlyByDocNoToUpdateMMINF($collectionData) {
        
        $collectionTransId = $collectionData->pluck('trans_id')->unique();
        $collectsDocAndItem = collect([]);
        foreach($collectionTransId as $transId){
            $collectionFiltered = $collectionData->where('trans_id', $transId);
            $docAndItem = collect([]);
            foreach($collectionFiltered as $objData){
                if($objData->trans_type == 'IBReq'){
                    $docAndItem->put('doc_no', $objData->remarks_1);
                }
                if($objData->trans_type == 'IBRes'){
                    // Unit ukuran PAC tidak wujud di dalam 1GFMAS bagi kod barang 42151624000000002R
                    // Kod barang [60105307000000003H] tidak wujud di 1GFMAS
                    $errorDesc = $objData->status_desc;
                    
                    $checkUnitUkuran = strpos($errorDesc, 'Unit ukuran');
                    if($checkUnitUkuran !== false){
                        $itemCode = trim(substr($errorDesc, -18)); 
                        $unitUkur = trim(substr($errorDesc,12, 3));
                        $docAndItem->put('error_reason', 'Unit ukuran tidak wujud');
                        $docAndItem->put('unit_ukuran', $unitUkur);
                        $docAndItem->put('item_code', $itemCode);
                    }
                    $checkKodBarang = strpos($errorDesc, 'Kod barang');
                    if($checkKodBarang !== false){
                        $itemCode = trim(substr($errorDesc, 12,18)); 
                        $docAndItem->put('error_reason', 'Kod barang tidak wujud');
                        $docAndItem->put('item_code', $itemCode);
                    }
                }
                $collectsDocAndItem->push($docAndItem);
            }
        }
        $collectionsDocAndItem = $collectsDocAndItem->unique();

        MigrateUtils::logDump(__METHOD__.' >> Total Doc PR (Got Error Item Code) : '.count($collectionsDocAndItem));
        //dd($collectsDocAndItem);

        $listRequestItemIdAll = collect([]);
        foreach ($collectionsDocAndItem as $objResult){
            $docNo = $objResult->get('doc_no');
            $itemCode = $objResult->get('item_code');
            $unitUkuran = $objResult->get('unit_ukuran');
            $errReason = $objResult->get('error_reason');
            
            MigrateUtils::logDump(__METHOD__.' >> to find and update is_sent  '.json_encode($objResult));
            
            /*
             * Just execute One record (same Kod Item) and unit ukuran  UPDATE DI_MMINF set IS_SENT =  0, So Scheduler will send MMINF to get all Unit Ukuran 
             * to send again
             */
            $mminfObj = $this->getRecordMminfDetailsByKodItem($itemCode,$unitUkuran);
            if($mminfObj != null){
                $parameters =  collect([]);            
                $parameters->put("remarks", "Error  GFM-100 ($errReason, $docNo, $itemCode, $unitUkuran)");
                $patchingProcess = EPService::$MMINF_PROCESS_PATCHING['mminf_isSent_status_0']['name'];
                $updateFields = ['is_sent' => 0,'changed_date' => Carbon::now(),'changed_by' => 1];
                $parameters->put("patching", $patchingProcess);
                $parameters->put("table", 'DI_MMINF');
                $parameters->put("mminf_id", $mminfObj->mminf_id);
                $logs = $this->updateMminfIDDetails($mminfObj->mminf_id, $updateFields);
                MigrateUtils::logDump(__METHOD__.' >> done updated  '.json_encode($parameters));
                $actionTypeLog = 'Script';
                $actionName = 'PatchDataMMINF';
                EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,'Completed','SchedulerAdmin');
      
            }
        }
        
        return $listRequestItemIdAll;
    }
    
    /**
     * Get all request item id to update field changed_date in sc_request_item to allow scheduler resend mminf.
     */
    protected function getAllRequestItemByDocNo($listData) {
        $listResult = $listData->pluck('remarks_1')->diff(["NA"])->unique();
            
        MigrateUtils::logDump(__METHOD__.' >> Total Doc PR (Got Error Item Code) : '.count($listResult));

        $listRequestItemIdAll = collect([]);
        foreach ($listResult as $docNo){
            $typeDoc   = substr($docNo, 0, 2);
            if($typeDoc == 'PO' || $typeDoc == 'CO'){
                $docObj = $this->getDocNoPRPOorCRCO($docNo);
                if($docObj){
                   $docNo =  $docObj->fr_doc_no;
                }
                $listReqItem = $this->getMminfReqItemId($docNo, null);
                $listRequestItemId = collect($listReqItem)->pluck('request_item_id')->unique();
                
                MigrateUtils::logDump(__METHOD__. '     DocNO '.$docNo. ' Total RequestItemID : '.count($listRequestItemId));
                
                /* Trigger Item All to changed date */
                $this->mminfTriggerByListID($listRequestItemId);
            
                /* populate to  $listRequestItemIdAll */
                $this->populateIntoListRequestItemIdAll($docNo, $listRequestItemId, $listRequestItemIdAll);
            }

        }
        
        return $listRequestItemIdAll;
    }
    
   protected function populateIntoListRequestItemIdAll($docNo,$listRequestItemId,&$listRequestItemIdAll) {
       $limitItem = 450;
        if(count($listRequestItemId) > $limitItem){
            $dataMoreItem = array(
                'Status' => 'Will not send trigger ChangeDate RequestItemID because more than 450 Items',
                'Doc No.' => $docNo,
                'Total' => count($listRequestItemId),
                'ListRequestItemID' => $listRequestItemId,
            );
            MigrateUtils::logDump(__METHOD__.'         send email -- item more than '.$limitItem);
            $this->sendErrorEmail(json_encode($dataMoreItem));
        }else{
            foreach ($listRequestItemId as $reqItemId){
                $listRequestItemIdAll->push($reqItemId);
                MigrateUtils::logDump(__METHOD__.'         RequestItemID : '.$reqItemId);
            }
        }
   }
    protected function mminfTrigger($reqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(10);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . "<trig:ScRequestItem><trig:requestItemId>$reqItemId</trig:requestItemId>"
                        . "<trig:changedDate>$now</trig:changedDate></trig:ScRequestItem>"
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $baseUrlOsbWs = $this->getBaseUrlWsOSB();
        $urlIdentity = "$baseUrlOsbWs/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";
        $commandCurl = "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents;
        MigrateUtils::logDump(__METHOD__." >>  $commandCurl");
        $commands  = [
            $commandCurl ,
        ];
        
        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],$reqItemId,'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){
            $line.PHP_EOL;
        });
        sleep(10);
        $resReqItems = $this->getMminfSearchDetail($reqItemId);

        $checkChangeDate = Carbon::parse($resReqItems[0]->changed_date);
        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
            EpSupportActionLog::updateActionLog($actionLog, 'Completed','SchedulerAdmin');
            
        } else {
            $status = 'Failed';
            EpSupportActionLog::updateActionLog($actionLog, 'Failed','SchedulerAdmin');
        }
        MigrateUtils::logDump(__METHOD__.'         '.$reqItemId. ': '.$status.' > '.$checkChangeDate->toString());

    }
    
    protected function mminfTriggerByListID($listReqItemId) {
        //$reqItemId = $request->request_item_id;
        //$curChangedDate = $request->current_changed_date;

        $now = Carbon::now()->addMinute(3);
        $now = $now->toDateString() . "T" . $now->toTimeString();

        $ScRequestItemXML = "";
        foreach ($listReqItemId as $reqItemId){
            $ScRequestItemXML = $ScRequestItemXML.
                                "<trig:ScRequestItem>
                                    <trig:requestItemId>$reqItemId</trig:requestItemId>
                                 </trig:ScRequestItem>";
        }

        $xmlContents = ""
                . "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                    . "<trig:ScRequestItemCollection xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/top/TriggerMMINF'>"
                        . $ScRequestItemXML
                    . "</trig:ScRequestItemCollection>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        /*
        $urlIdentity = "http://192.168.63.205:7011/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        */

        $baseUrlOsbWs = $this->getBaseUrlWsOSB();
        $urlIdentity = "$baseUrlOsbWs/TriggerMMINFID/v1.0";
        $urlHeader = "'Content-Type: application/xml'";
        $commandCurl = "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents;
        MigrateUtils::logDump(__METHOD__." >>  $commandCurl");
        $commands  = [
            $commandCurl ,
        ];


        $actionLog =  EpSupportActionLog::createActionLog('TriggerMMINFID','Web Service',$commands[0],json_encode($listReqItemId),'SchedulerAdmin');

        SSH::into('osb')->run($commands, function($line){$line.PHP_EOL; });
        
        sleep(10);
        
        $resReqItems = $this->getMminfSearchDetailByListReqItemId($listReqItemId);
        $reqItem = $resReqItems->first();
        $checkChangeDate = Carbon::parse($reqItem->changed_date);

        if($checkChangeDate->gt(Carbon::now())) {
            $status = 'Completed';
        }else{
            $status = 'Failed';
        }
        
        EpSupportActionLog::updateActionLog($actionLog, $status,'SchedulerAdmin');
     
        MigrateUtils::logDump(__METHOD__.'         '.$reqItem->request_item_id. ': '.$status.' > '.$checkChangeDate->toString());


    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM100ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
