@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Stuck Task PRCR  (EPP-013 = Y) <br>
                <small>Stuck Task PRCR  (EPP-013 = Y)</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>Stuck Task PRCR  (EPP-013 = Y) List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck Task PRCR  (EPP-013 = Y) List </strong>
                        <small></small>
                    </h1>
                </div>
             
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">MM</th>
                            <th class="text-center">DD</th>
                            <th class="text-center">POCO NO.</th>
                            <th class="text-center">PRCR NO.</th>
                            <th class="text-center">BPM INSTANCE ID</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">BPM FLOW ID</th>
                            <th class="text-center">REMARKS 2</th>
                            <th class="text-center">STATUS NAME</th>
                            <th class="text-center">STATUS ID</th>
                            <th class="text-center">FWS DATE</th>
                            <th class="text-center">TRANS DATE</th>
                            <th class="text-center">DAY</th>
                            <th class="text-center">MONTH</th>
                            <th class="text-center">YEAR</th>
                            

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->mm }}</td>
                                <td class="text-center">{{ $data->dd }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->remarks_1  }}" >{{ $data->remarks_1 }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->prcr  }}" >{{ $data->prcr }}</a></td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></td>
                                <td class="text-center">{{ $data->composite_bpm }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                                <td class="text-center">{{ $data->remarks_2 }}</td>
                                <td class="text-center">{{ $data->status_name }}</td>
                                <td class="text-center">{{ $data->status_id }}</td>
                                <td class="text-center">{{ $data->fws_date }}</td>
                                <td class="text-center">{{ $data->trans_date }}</td>
                                <td class="text-center">{{ $data->hb }}</td>
                                <td class="text-center">{{ $data->bulan }}</td>
                                <td class="text-center">{{ $data->year }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



