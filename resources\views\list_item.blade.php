@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/item')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Item<br>
                <small>Masukkan Item pada carian diatas...</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  @if(isset($error))
                  <p>Carian mesti lebih 3 aksara!</p>
                  @else
                  <p>Tidak dijumpai!</p>
                  @endif
              </div>
            </div>
        </div>
    </div>
    @endif
    
    
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Item </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">EXTENSION CODE</th>
                            <th class="text-center">ITEM NAME</th>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">UNSPSC CODE</th>
                            <th class="text-center">UNSPSC NAME</th>
                            <th class="text-center">UNSPSC LEVEL</th>
                            <th class="text-center">COLOR</th>
                            <th class="text-center">TYPE</th>
                            <th class="text-center">BRAND</th>
                            <th class="text-center">MEASUREMENT</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->item_id }}</td>
                                <td class="text-center">{{ $data->extension_code }}</td>
                                <td class="text-center">{{ $data->item_name }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-left">{{ $data->unspsc_code }}</td>
                                <td class="text-left">{{ $data->unspsc_title }}</td>
                                <td class="text-left">{{ $data->unspsc_level }} - {{ App\Services\EPService::$UNSPSC_LEVEL[$data->unspsc_level] }}</td>
                                <td class="text-left content-link">
                                    <a class="modal-list-data-action"
                                       href="javascript:void(0)" data-toggle='modal' 
                                       data-title='List All Items Color' 
                                       data-url='{{url('/find/item/unspsc/color')}}'
                                       data-target="#modal-item" >
                                       {{ $data->color_code }} - {{ $data->color_name }} </a>
                                </td>
                                <td class="text-left content-link">
                                    <a class="modal-list-data-action  "
                                       href="javascript:void(0)" data-toggle='modal' 
                                       data-title='Items Type in {{ $data->unspsc_code }}' 
                                       data-url='{{url('/find/item/unspsc/type')}}/{{$data->unspsc_id }}'
                                       data-target="#modal-item" >
                                       {{ $data->type_code }} - {{ $data->type_name }} </a>
                                </td>
                                <td class="text-left content-link">
                                    <a class="modal-list-data-action  "
                                       href="javascript:void(0)" data-toggle='modal'  
                                       data-title='Items Brands in {{ $data->unspsc_code }}' 
                                       data-url='{{url('/find/item/unspsc/brand')}}/{{$data->unspsc_id }}'
                                       data-target="#modal-item" >
                                       {{ $data->brand_code }} - {{ $data->brand_name }}</a>
                                </td>
                                <td class="text-left content-link">
                                    <a class="modal-list-data-action  "
                                       href="javascript:void(0)" data-toggle='modal' 
                                       data-title='Items Measurement in {{ $data->unspsc_code }}' 
                                       data-url='{{url('/find/item/unspsc/measurement')}}/{{$data->unspsc_id }}'
                                       data-target="#modal-item" >
                                       {{ $data->measurement_code }} - {{ $data->measurement_name }}</a>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
        <!-- MODAL: K -->
        <div id="modal-item" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i><span id="modal-list-data-header"> List Items </span></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="table-responsive">
                                    <table id="item-datatable" class="table table-striped table-vcenter">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    App.datatables();
    /* Initialize Datatables */
    var tableListData =     $('#item-datatable').DataTable({
            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });
        
        
    $(document).ready(function () {
        $('.content-link').on("click",'.modal-list-data-action', function(){
                
            $('.spinner-loading').show();
            $('#item-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url:  $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#item-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#item-datatable').DataTable({
                        order: [[ 1, "desc" ]],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
    });
</script>
@endsection



