<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Guzzle;
use GuzzleHttp\Client;
use Log;


trait BpmApiService {

    protected function findAPITaskBPMList($docNo,$module,$date){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task?doc_no=".$docNo."&module=".$module."&created_date=".$date;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function findAPITaskBPMListDocAndModule($docNo,$module){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task/find?doc_no=".$docNo."&module=".$module;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function findAPITaskBPMListAdvFilter($docNo,$module,$status,$activityName){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task/filterAdv?doc_no=".$docNo."&module=".$module."&task_state=".$status."&activity_name=".$activityName;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function findAPIProcessManagerBPMByInstance($instanceCompositeId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/processesByCompositeId?composite_instance_id=".$instanceCompositeId;
            
            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

    protected function findAPIProcessManagerBPMByInstanceModule($instanceCompositeId,$compositeModule){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/processesByComposite?composite_instance_id=".$instanceCompositeId."&composite_module=".$compositeModule;
            
            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
        
        

    }
    
    protected function findAPITaskIDBPMList($taskNo){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/taskByNo?task_id=".$taskNo;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
        
        

    }
    
    protected function findAPIBPMWorkflowByProcessId($processId){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/auditInstance/?process_id=".$processId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
        
        

    }
    
    
    protected function doAPIInitiateSMTaskApplication($applNo){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/ep/ejb-sm/initiate-task/?appl_no=".$applNo.'&token='.$token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }

    protected function doAPIgetPayloadSMTaskApplication($applNo,$isPayment){
        // http://**************:8080/ep-support-middleware/ep/ejb-sm/payload/appl-task?appl_no=***********-0025&is_payment=false
        try {

            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/ep/ejb-sm/payload/appl-task?appl_no=".$applNo.'&is_payment='.$isPayment;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }

    
    protected function doAPIRefireSMTaskApplication($applNo,$isPayment){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/ep/ejb-sm/refire-task/?appl_no=".$applNo.'&is_payment='.$isPayment.'&token='.$token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function doAPIIsValidToken(){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $token = base64_encode(strtotime("now"));
            
            $url = $urlMiddleware.'/ep/check-token/?token='.$token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function suspendResumeBpmn($id,$action){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            
            if($action == 'resume'){
                $url = $urlMiddleware."/bpm/process/doResume";  
            }else{
                $url = $urlMiddleware."/bpm/process/doSuspend"; 
            }           

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'process_id' => $id
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function suspendResumeTask($id,$action){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","localhost:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            
            if($action == 'resumeTask'){
                $url = $urlMiddleware."/bpm/task/resume";
            }else{
                $url = $urlMiddleware."/bpm/task/suspend";
            }            

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'task_id' => $id
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function withdrawTask($id){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","localhost:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            
            $url = $urlMiddleware."/bpm/task/withdraw";              

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'task_id' => $id
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function reassignTask($id, $assignee) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "localhost:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));

            $url = $urlMiddleware . "/bpm/task/reassign";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'task_id' => $id,
                        'assignees' => $assignee
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

    protected function findApiBPMGetListComposite() {
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/listComposites";

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPIInitiatePurchaseRequest($docNo,$actionTask,$triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            if($actionTask == 'initiate-task-pr') {
                $url = $urlMiddleware."/bpm/fl/initiatePurchaseRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }else if($actionTask == 'approve-task-pr'){
                $url = $urlMiddleware."/bpm/fl/approvePurchaseRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }else{
                $url = $urlMiddleware."/bpm/fl/initiateAndUpdatePurchaseRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPIInitiateContractRequest($docNo,$actionTask,$triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            if($actionTask == 'initiate-task-cr') {
                $url = $urlMiddleware."/bpm/fl/initiateContractRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }else if($actionTask == 'approve-task-cr'){
                $url = $urlMiddleware."/bpm/fl/approveContractRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }else{
                $url = $urlMiddleware."/bpm/fl/initiateAndUpdateContractRequest?doc_no=" .$docNo."&is_trigger_bpm=" .$triggerBpm;
            }

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPIInitiateDoFulfilment($docNo, $actionTask, $loginId, $triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            if ($actionTask == 'initiate-update-dofrn') {
                
                $url = $urlMiddleware . "/bpm/fl/initiateAndUpdateDOFRN?doc_no=" . $docNo . "&is_trigger_bpm=" . $triggerBpm;
                
            } else if ($actionTask == 'update-delivery-order') {
                
                $url = $urlMiddleware . "/bpm/fl/updateDeliveryOrder?delivery_order_id=" . $docNo . "&is_trigger_bpm=" . $triggerBpm;
                
            } elseif ($actionTask == 'acknowledge-receiving-note') {
                
                $url = $urlMiddleware . "/bpm/fl/acknowledgeReceivingNote?delivery_order_id=" . $docNo . "&is_trigger_bpm=" . $triggerBpm;
                
            } else if($actionTask == 'acknowledge-receiving-note-officer') {
                
                $url = $urlMiddleware . "/bpm/fl/acknowledgeReceivingNoteByOfficer?delivery_order_id=" . $docNo . "&acknowledge_officer_login_id=" . $loginId . "&is_trigger_bpm=" . $triggerBpm;
                
            }else{
                
                $url = $urlMiddleware . "/bpm/fl/updateApproveFRN?delivery_order_id=" . $docNo . "&is_trigger_bpm=" . $triggerBpm;
            }

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPICreateSubmitInvoicePA($fl_order_id, $actionTask, $loginId, $triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            if($actionTask == 'submit-invoice'){
                $url = $urlMiddleware . "/bpm/fl/constructSubmitInvoice?fl_order_id=" . $fl_order_id . "&is_trigger_bpm=" . $triggerBpm;
            }else{
                $url = $urlMiddleware . "/bpm/fl/constructPaymentAdvice?fl_order_id=" . $fl_order_id . "&login_payment_match=" .$loginId . "&is_trigger_bpm=" . $triggerBpm;
            }
           
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPIModifyCancelInvoice($invoice_id, $actionTask, $triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            if($actionTask == 'modify-invoice'){
                $url = $urlMiddleware . "/bpm/fl/constructModifyInvoice?invoice_id=" . $invoice_id . "&is_trigger_bpm=" . $triggerBpm;
            }else{
                $url = $urlMiddleware . "/bpm/fl/constructCancelInvoice?invoice_id=" . $invoice_id . "&is_trigger_bpm=" . $triggerBpm;
            }
            
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

    protected function doAPIModifyCancelDO($do_id, $actionTask, $triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            if($actionTask == 'modify-do'){
                $url = $urlMiddleware . "/bpm/fl/constructModifyDO?delivery_order_id=" . $do_id . "&is_trigger_bpm=" . $triggerBpm;
            }else{
                $url = $urlMiddleware . "/bpm/fl/constructCancelDO?delivery_order_id=" . $do_id . "&is_trigger_bpm=" . $triggerBpm;
            }
            
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function doAPIStopInstr($sd_id, $supp_login_id, $triggerBpm) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

                $url = $urlMiddleware . "/bpm/fl/constructStopInstr?stop_intruction_id=" . $sd_id . "&supplier_login_id=" .$supp_login_id . "&is_trigger_bpm=" . $triggerBpm;
            
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function alterflowBpmnProcessId($processId){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/get/alterlist?process_id=".$processId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function variableBpmnProcessId($processId){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/get/variables?process_id=".$processId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function submitAlterflowBpmnProcessId($processId,$source,$target){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/alterflow";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'process_id' => $processId,
                        'source' => $source,
                        'target' => $target
                    ]
                ];
                $response = $client->post($url, $options);
                Log::info(__METHOD__." execute >>  $url :".json_encode($options));
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function submitVariableBpmnProcessId($processId,$key,$value){
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/update/variable";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'process_id' => $processId,
                        'key' => $key,
                        'value' => $value
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        } 
    }
    
    protected function findAPIListTaskAction($taskId,$user){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task/list/action?user=".$user."&task_id=" .$taskId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function updateExecuteActionAPI($taskId, $user, $payload, $executeAction, $param){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $token = base64_encode(strtotime("now"));

            $url = $urlMiddleware."/bpm/task/update";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'user' => $user,
                        'task_id' => $taskId,
                        'action' => $executeAction,
                        'payload' => $payload,
                        'custom' => $param
                    ]
                ];
                Log::info(__METHOD__." execute >>  $url :".json_encode($options));
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function taskHistory($taskId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/bpm/task/history/list?task_id=" . $taskId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function submitTerminateInstance($module,$instanceId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/terminate/instance";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'comp_mod' => $module,
                        'comp_instance_id' => $instanceId
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function getErrorHandlerList($taskTitle,$statusId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/err/list/id?user=cdcadmin&task_title=" . $taskTitle . "&status_id=" . $statusId . "&offset=0&limit=50";

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;  
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                "status" => "Error",
                "result" => "No Response");
            }  
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => "Failed to connect");
        }
    }
    
    protected function findApiWorklist($userId, $state, $assignment, $offset, $limit){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task/list/criteria?user=" . $userId . "&task_title=&state=" . $state . "&assignment=" . $assignment ."&offset=" .$offset . "&limit=" .$limit;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => "Failed to connect");
        }
    }
    
    protected function findApiWorklistTaskDetail($taskId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/taskById?task_id=" . $taskId;
            
            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => "Failed to connect");
        }
    }
    
    protected function delegateTaskAPI($taskId,$userId,$assignee){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $token = base64_encode(strtotime("now"));

            $url = $urlMiddleware."/bpm/task/delegate";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'user' => $userId,
                        'task_id' => $taskId,
                        'assignees' => $assignee
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function actionWorklistTaskAPI($action,$taskId,$userId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $token = base64_encode(strtotime("now"));
            $url = '';
            
            if($action === 'Claim-Task'){
                $url = $urlMiddleware."/bpm/task/claim";
            }else if($action === 'Delete-Task'){
                $url = $urlMiddleware."/bpm/task/delete";
            }else if($action === 'Release-Task'){
                $url = $urlMiddleware."/bpm/task/release";
            }

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'user' => $userId,
                        'task_id' => $taskId
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function worklistTaskInitiable($userId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/task/list/initiable?user=" . $userId;
            
            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => "Failed to connect");
        }
        
    }
    
    protected function initiateProcessAPI($processId, $userId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $token = base64_encode(strtotime("now"));

            $url = $urlMiddleware."/bpm/task/proc/initiate";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        'token' => $token,
                        'user' => $userId,
                        'process_id' => $processId
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function findApiBPMGetInstanceQuery($composite, $dateFrom, $dateTo, $timeFrom, $timeTo, $states, $offset, $limit) {
        
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/bpm/instance/query";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        "user" => null,
                        "token" => null,
                        "compositeDN" => $composite,
                        "createdDtFrom" => $dateFrom,
                        "createdDtTo" => $dateTo,
                        "createdTsFrom" => $timeFrom,
                        "createdTsTo" => $timeTo,
                        "state" => $states,
                        "offset" => $offset,
                        "limit" => $limit
                    ]
                ];
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

    protected function findApiBPMGetListServiceManager(){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $url = $urlMiddleware."/bpm/service/manager/list";

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
    
    protected function findApiBPMCreateServiceManager($name,$process,$trigger,$elements){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $url = $urlMiddleware."/bpm/service/manager/create";

            try {
                $client = new \GuzzleHttp\Client(["base_uri" => $url]);
                $options = [
                    'json' => [
                        "name" => $name,
                        "process" => $process,
                        "trigger" => $trigger,
                        "elements" => $elements
                    ]
                ];
                Log::info(__METHOD__." trigger >>  $url :".json_encode($options));
                $response = $client->post($url, $options);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            Log::info($ex);
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
        
    }
    
    protected function findApiBPMGetTaskGroupByTaskId($taskId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  
            
            $url = $urlMiddleware."/bpm/task/group/list?task_id=" .$taskId;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }
}
