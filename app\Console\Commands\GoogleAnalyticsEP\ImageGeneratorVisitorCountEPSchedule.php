<?php

namespace App\Console\Commands\GoogleAnalyticsEP;

use App\Migrate\ImageGeneratorVisitorCountEP;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use Illuminate\Support\Facades\Config as Config;
use Illuminate\Support\Facades\Log as Log;
use Illuminate\Support\Facades\Mail as Mail;

class ImageGeneratorVisitorCountEPSchedule extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep-visitor-count';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To generate image visitor counter of eP - from google analytics';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' starting ..', ['Date' => Carbon::now()]);
        try {
            ImageGeneratorVisitorCountEP::run();
            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error: ImageGeneratorVisitorCountEPSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' Error >> ' . json_encode(['Email' => $data["to"], 'ERROR' => $e->getMessage()]));
            return $e;
        }
    }
}
