<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Log;
use App\Services\Traits\BpmApiService;
use App\EpSupportActionLog;

class BpmApiWorklistController extends Controller {

    use BpmApiService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function searchWorklist(Request $request) {

        $userId = null;
        $firstData = null;
        $statusAPI = null;
        $offset = 0;
        $limit = 20;
        
        $assignmentData = array('ASSIGNMENT_MY' => 'MY',
            'ASSIGNMENT_GROUP' => 'GROUP',
            'ASSIGNMENT_ALL' => 'ALL');

        $stateData = array('TASK_STATE_ASSIGNED' => 'ASSIGNED',
            'TASK_STATE_COMPLETED' => 'COMPLETED',
            'TASK_STATE_EXPIRED' => 'EXPIRED',
            'TASK_STATE_SUSPENDED' => 'SUSPENDED',
            'TASK_STATE_WITHDRAWN' => 'WITHDRAWN');

        session()->flashInput(request()->input());

        if ($request->isMethod("POST")) {
            $this->validate(request(), [
                'user_id' => 'required',
            ]);

            
            $userId = $request->user_id;
            $assigment = $request->assignment;
            $state = $request->state;
            
            if($assigment === null && $state === null){
                $listDataResult = $this->findApiWorklist($userId, 'ASSIGNED', 'MY', $offset, $limit);
            }else{
                $listDataResult = $this->findApiWorklist($userId, $state, $assigment, $offset, $limit);
            }        

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $firstData = $listDataResult["result"];

            } else {
                $statusAPI = $listDataResult["result"];
            }
            
        }

        return view('bpm_api.worklist', [
            'userId' => $userId,
            'assignmentData' => $assignmentData,
            'stateData' => $stateData,
            'firstData' => $firstData,
            'status_api' => $statusAPI,
            'offset' => $offset,
            'limit' => $limit
        ]);
    }

    public function searchWorklistAssignment($userId, Request $request) {

        $listdata = null;
        $data = collect([]);
        $assignment = $request->assignment;
        $state = $request->state;
        $statusApi = null;
        $offset = $request->offset;
        $limit = $request->limit;

        if ($userId !== '' && $state !== '' && $assignment !== '') {
            $listDataResult = $this->findApiWorklist($userId, $state, $assignment, $offset, $limit);

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];

                $data->put('status', 'success');
                $data->put('listdata', $listdata);
            } else {
                $statusApi = $listDataResult["result"];
                
                $data->put('status', 'failed');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusApi);
                
            }
        }

        return $data;
    }

    public function searchWorklistTaskDetail($taskId) {

        $listdata = null;
        $data = collect([]);
        $payload = null;
        $history = null;
        $listAction = null;
        $assignees = null;
        $totalAssignee = 0;
        $statusApi = null;
        
        if ($taskId !== '') {
            $listDataResult = $this->findApiWorklistTaskDetail($taskId);

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];
                $assignees = str_replace("\n ", ",", $listdata["assigneesString"]);

                if ($listdata["payload"]) {
                    foreach ($listdata["payload"] as $value) {
                        $payload = preg_replace('!^[^>]+>!', '', $value);
                    }
                }

                $data->put('statusApiTaskDetail', 'success');
                $data->put('listdata', $listdata);
                $data->put('payload', $payload);
            } else {
                $statusApiTaskDetail = $listDataResult["result"];
                $data->put('listdata', $listdata);
                $data->put('payload', $payload);
                $data->put('statusApiTaskDetail', $statusApiTaskDetail);
            }

            //get list of action
            $cdcadmin = array('Contract_Management.ProcessOwner',
                'Fulfilment.ProcessOwner',
                'Order.ProcessOwner',
                'SourcingDP.ProcessOwner',
                'SourcingQT.ProcessOwner',
                'Supplier_Management.ProcessOwner',
                'Profile_Management.ProcessOwner',
                'SCBusinessRule.ProcessOwner',
                'Codification.ProcessOwner');


            if ($listdata["taskId"] !== null && $assignees !== null) {
                $taskId = $listdata["taskId"];

                $getList = null;
                if (in_array($assignees, $cdcadmin)) {
                    $getList = $this->findAPIListTaskAction($taskId, 'cdcadmin');
                } else {
                    $arr = explode(",", $assignees, 2);
                    $first = $arr[0];
                    $getList = $this->findAPIListTaskAction($taskId, $first);
                }
            }

            if (isset($getList)) {
                if ($getList['status'] == 'Success') {
                    $data->put('statusListTask', 'success');
                    $listAction = $getList["result"];
                }else{
                    $statusListTask = $getList["result"];
                    $data->put('statusListTask', $statusListTask);
                }
            }

            //get task history
            $getHistory = $this->taskHistory($listdata["taskId"]);
            if ($getHistory['status'] == 'Success') {
                $history = $getHistory["result"];
                $data->put('statusHistory', 'success');
            } else {
                $statusHistory = $getHistory["result"];
                $data->put('statusHistory', $statusHistory);
            }
            
            $totalAssignee = count($listdata["assignees"]);
        }
        $data->put('listaction', $listAction);
        $data->put('history', $history);
        $data->put('totalassignee', $totalAssignee);
        $data->put('statusApi', $statusApi);

        return $data;
    }

    public function delegateTaskAction($taskId, Request $request) {
        $listdata = null;
        $data = collect([]);
        $status = null;
        $statusApi = null;

        if ($taskId !== '') {

            $userId = $request->userId;
            $assignee = $request->assignee;
            $actionTask = $request->action;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Worklist-' . $actionTask;

            $parameters = collect([]);
            $parameters->put("task_id", $taskId);
            $parameters->put("user_id", $userId);
            $parameters->put("assignee", $assignee);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

            $listDataResult = $this->delegateTaskAPI($taskId, $userId, $assignee);

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];
                $status = 'success';
            } else {
                $status = 'failed';
                $statusApi = $listDataResult["result"];
            }
            $data->put('status', $status);
            $data->put('statusApi', $statusApi);
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
        }

        return $data;
    }

    public function worklistTaskAction($taskId, Request $request) {
        $listdata = null;
        $data = collect([]);
        $status = null;
        $statusApi = null;

        if ($taskId !== '') {

            $userId = $request->userId;
            $actionTask = $request->action;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Worklist' . $actionTask;

            $parameters = collect([]);
            $parameters->put("task_id", $taskId);
            $parameters->put("user_id", $userId);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

            $listDataResult = $this->actionWorklistTaskAPI($actionTask, $taskId, $userId);

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];
                $status = 'success';
            } else {
                $status = 'failed';
                $statusApi = $listDataResult["result"];
            }

            $data->put('status', $status);
            $data->put('statusApi', $statusApi);
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
        }

        return $data;
    }

    public function executeTaskAction($taskId, Request $request) {
        $listdata = null;
        $data = collect([]);
        $status = null;
        $statusApi = null;

        if ($taskId !== '') {
            $userId = $request->userid;
            $action = $request->action;
            $actionTask = $request->taskaction;
            $payload = $request->updatedpayload;
            $param = $request->param;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Worklist-' . $action;

            $parameters = collect([]);
            $parameters->put("task_id", $taskId);
            $parameters->put("user_id", $userId);
            $parameters->put("payload", $payload);
            $parameters->put("param", $param);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

            $listdata = $this->updateExecuteActionAPI($taskId, $userId, $payload, $actionTask, $param);

            if ($listdata['status'] == 'Success') {
                $status = 'Success';
            } else {
                $status = 'Failed';
                $statusApi = $listdata["result"];
                $parameters->put("server_response", $listdata);
            }
            $data->put('status', $status);
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
        }

        return $data;
    }

    public function listInitiate($userId) {

        $data = collect([]);
        $initiableResult = null;
        $newInitiable = null;
        $status = null;

        if ($userId != '') {
            $initiable = $this->worklistTaskInitiable($userId);

            if ($initiable["status"] != null && $initiable["status"] === 'Success') {

                $initiableResult = $initiable["result"];
                if (isset($initiableResult)) {

                    foreach ($initiableResult as $value) {
                        $newInitiable[$value["compositeName"]][$value["version"]][$value["processName"]] = ["id" => $value["id"],
                            "compositeName" => $value["compositeName"],
                            "processName" => $value["processName"],
                            "compositeDN" => $value["compositeDN"],
                            "version" => $value["version"],
                            "default" => $value["default"]];
                    }

                    $status = 'success';
                    
                }else{
                    $status = 'No Record';
                }
            }else{
                $status = $initiable["result"];
            }
        }

        $data->put('status',$status);
        $data->put('initiable',$newInitiable);
        
        return $data;
    }

    public function initiateProcessAction($userId, Request $request) {
        $listdata = null;
        $data = collect([]);
        $status = null;
        $statusApi = null;
        
        if ($userId !== '') {

            $processId = $request->processId;
            $action = $request->action;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Worklist-' . $action;

            $parameters = collect([]);
            $parameters->put("process_id", $processId);
            $parameters->put("user_id", $userId);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

            $listdata = $this->initiateProcessAPI($processId, $userId);

            if ($listdata['status'] == 'Success') {
                $status = 'success';
            } else {
                $status = 'failed';
                $statusApi = $listdata['result'];
            }
            $data->put('status', $status);
            $data->put('statusApi', $statusApi);
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
        }

        return $data;
    }

}
