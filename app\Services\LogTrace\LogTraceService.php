<?php

namespace App\Services\LogTrace;

use Carbon\Carbon;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Services\Traits\LogTrace\TraceLogService;
use Exception;
use SSH;
use Illuminate\Support\Facades\Log;

/**
 * Log Trace Service
 *
 * <AUTHOR>
 */
class LogTraceService
{
    use TraceLogService;

    public function deleteEmptyLog()
    {
        $deleted_files = $this->getEmptyLogFile();

        if ($deleted_files) {
            foreach ($deleted_files as $delfile) {
                try {
                    $commands = [
                        "cd /opt/ep-trace/log/",
                        'rm -rf ' . $delfile,
                        'exit',
                    ];
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        Log::info($data);
                    });
                } catch (Exception $e) {
                    Log::info($e->getMessage());
                }
            }
        }
    }

    public function getTotalStuckLog()
    {
        $stuckList = $this->monitorTmpLogFileFailToProcess();
        $total = count($stuckList);
        return $total;

    }
    
    public function clearStuckLog()
    {
        $stuckList = $this->monitorTmpLogFileFailToProcess();

        if ($stuckList) {
            $total = count($stuckList);
            $counter = 1;
            Log::info(__METHOD__." > total files need to recover : $total");
            foreach ($stuckList as $stuck) {
                Log::info(__METHOD__." $counter/$total) > file name: ".$stuck);
                $counter++;
                try {
                    $commands = ['cd /opt/ep-trace', 'python3 patch_single.py ' . $stuck, 'exit',];
                    
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        Log::info('Result from python > '.$data);
                    });
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        }
    }

    public function clearStuckLogByLimit($totalLimit)
    {
        $dateStart = Carbon::now();
        $dateStartFmt = $dateStart->format('Y-m-d H:i:s');
        $stuckList = $this->monitorTmpLogFileFailToProcess();
        Log::info(__METHOD__." fix stuck log file ".$totalLimit. ' only');
        if ($stuckList) {
            $total = count($stuckList);
            $counter = 1;
            Log::info(__METHOD__." > total files need to recover : $total");
            foreach ($stuckList as $stuck) {
                if($counter > $totalLimit){
                    return ;
                }
                Log::info(__METHOD__." trigger: $dateStartFmt  $counter/$totalLimit) > file name: ".$stuck);
                $counter++;
                try {
                    $commands = ['cd /opt/ep-trace', 'python3 patch_single.py ' . $stuck, 'exit',];
                    
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        Log::info('Result from python > '.$data);
                    });
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        }
    }

    /**
     * @$dateSearch must be string as format yyyymmdd
     */
    public function clearStuckLogByUserAndDate($loginId,$dateSearch)
    {
        $stuckList = $this->monitorTmpLogFileFailToProcessByUserAndDate($loginId,$dateSearch);
        Log::info(__METHOD__." fix stuck by search loginID: $loginId and date log file: $dateSearch ");
        if ($stuckList) {
            $total = count($stuckList);
            $counter = 1;
            Log::info(__METHOD__." > total files need to recover : $total");
            foreach ($stuckList as $stuck) {
                Log::info(__METHOD__." ($loginId <> $dateSearch) : $counter/$total  file name: ".$stuck);
                $counter++;
                try {
                    $commands = ['cd /opt/ep-trace', 'python3 patch_single.py ' . $stuck, 'exit',];
                    
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        //Log::info('Result from python > '.$data);
                    });
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        }
    }

    /**
     * @$dateSearch must be string as format yyyymmdd
     */
    public function clearStuckLogByDate($dateSearch)
    {
        $stuckList = $this->monitorTmpLogFileFailToProcessByDate($dateSearch);
        Log::info(__METHOD__." fix stuck by search date log file: $dateSearch : ".count($stuckList));
        if ($stuckList) {
            $total = count($stuckList);
            $counter = 1;
            Log::info(__METHOD__." > total files need to recover : $total");
            foreach ($stuckList as $stuck) {
                Log::info(__METHOD__." ($dateSearch : $counter/$total)  file name: ".$stuck);
                $counter++;
                try {
                    $commands = ['cd /opt/ep-trace', 'python3 patch_single.py ' . $stuck, 'exit',];
                    
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        //Log::info('Result from python > '.$data);
                    });
                } catch (Exception $e) {
                    Log::error($e->getMessage());
                }
            }
        }
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    public function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "bcc" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Log Trace Stuck Log'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__METHOD__ . ' >>> ERROR :- '.$e->getMessage(). ' DETAILS LOG : '.$e->getTraceAsString());
        }
    }
}
