    <table class="table table-vcenter table-condensed table-bordered">
        @foreach($childs as $child)
            @if(@$loop->index == 0)
            <thead>
                @if(isset($child->name))
                <tr style="background-color: #f2f2f2"><td colspan="{{ count($child->data) }}"><b>{{ $child->name }}</b></td></tr>
                @endif
                @if(isset($child->data))
                <tr>
                    @foreach ($child->data as $data)
                        <th class="text-center">{{ $data->key }}</th>
                    @endforeach
                </tr>
                @endif
            </thead>
            @endif
            <tbody style=" border-bottom-style: outset;"">
                @if(isset($child->data))
                <tr>
                    @foreach ($child->data as $data)
                        <td class="text-center">{{ $data->value }}</td>
                    @endforeach
                </tr>
                @endif
                <tr>
                    @if(isset($child->list))
                    <td colspan="{{ count($child->data) }}">@include('osb.manage_child',['childs' => $child->list])</td>
                    @endif
                </tr>
            </tbody>
        @endforeach
    </table>