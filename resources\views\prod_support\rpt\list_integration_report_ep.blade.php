@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-users"></i> LAPORAN INTEGRASI</h6>
        </div>

        <!-- Search Form -->
        <div class="block block-alt-noborder full">
            <form id="reportForm" action="{{ url('/report/ep/integration') }}" method="post">
                {{ csrf_field() }}
                <div class="form-group row align-items-center">
                    <label for="by_info" class="col-md-2 col-form-label text-left">Info <span
                            class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <select id="by_info" name="by_info" required class="form-control">
                            <option value="">Please Select</option>
                            <option value="ssm" {{ $filterBy == 'ssm' ? 'selected' : '' }}>SSM</option>
                            <option value="myidentity" {{ $filterBy == 'myidentity' ? 'selected' : '' }}>myIDENTITY</option>
                        </select>
                    </div>

                    <label for="by_year" class="col-md-2 col-form-label text-left">Year <span
                            class="text-danger">*</span></label>
                    <div class="col-md-2">
                        <select id="by_year" name="by_year" required class="form-control">
                            <option value="">Select Year</option>
                            @foreach (range(date('Y'), 2024) as $year)
                                <option value="{{ $year }}"
                                    {{ request('by_year', date('Y')) == $year ? 'selected' : '' }}>{{ $year }}
                                </option>
                            @endforeach
                        </select>
                    </div>

                    <div class="col-md-3">
                        <button id="submitButton" type="submit" class="btn btn-sm btn-primary">
                            <i id="submitIcon" class="fa fa-search"></i> Search
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <br>

        <!-- Reports Section -->
        @foreach (['SSM' => $listSSM, 'myIDENTITY' => $listmyIdentity] as $reportType => $reportData)
            @if ($reportData)
                <div class="block block-alt-noborder full">
                    <div class="block-title panel-heading epss-title-s3">
                        <h1><i class="fa fa-building-o"></i></h1>
                        <h1><strong>LAPORAN {{ $reportType }}</strong></h1>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped table-hover">
                            <thead class="thead-dark"
                                style="position: sticky; top: 0; background-color: #004085; color: white;">
                                <tr>
                                    <th class="text-center">YEAR-MONTH Request</th>
                                    <th class="text-center">TOTAL {{ $reportType == 'SSM' ? 'SSM NO' : 'IC NO' }}</th>
                                    <th class="text-center">TOTAL REQUEST</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($reportData as $item)
                                    <tr>
                                        <td class="text-center">{{ $item->year_month_request }}</td>
                                        <td class="text-center">
                                            {{ $reportType == 'SSM' ? $item->total_ssm_no : $item->total_ic_no }}</td>
                                        <td class="text-center">{{ $item->total_request }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
        @endforeach
    </div>
@endsection

@section('jsprivate')
    <script>
        document.getElementById('reportForm').addEventListener('submit', function() {
            const button = document.getElementById('submitButton');
            const icon = document.getElementById('submitIcon');

            // Disable button during processing
            button.disabled = true;
            button.setAttribute('aria-disabled', 'true');

            // Update visual feedback
            icon.classList.replace('fa-search', 'fa-spinner');
            icon.classList.add('fa-spin');
        });
    </script>
@endsection
