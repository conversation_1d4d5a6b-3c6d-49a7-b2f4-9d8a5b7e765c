<?php

namespace App\Services\LogTrace;

use DB;
use Carbon\Carbon;
use Mail;
use Config;
use App\Migrate\MigrateUtils;
use App\Model\LogTrace\LogTraceUserRequest;
use App\Model\LogTrace\LogTraceCubeModel;

/**
 * Log Trace Service
 *
 * <AUTHOR>
 */
class ExportLogService
{

    public function export_user_requests($dt)
    {
        $y = $dt->year;
        $y = substr($y, 2, 2);
        $m = $dt->month;
        $m = strlen($m) < 2 ? '0' . $m : $m;
        $d = $dt->day;
        $d = strlen($d) < 2 ? '0' . $d : $d;

        $log_dt = '_py' . $y . 'm' . $m . 'd' . $d;
        MigrateUtils::logDump('Fetch User Access Request Today => ' . $log_dt);

        $sql = "SELECT COALESCE(NULLIF(login_id ,''),'Anonymous') as user, TO_DATE(log_dt, 'yyyy-mm-dd') AS log_dt,  count(1) AS total FROM (
                SELECT DISTINCT TO_CHAR(log_dt, 'yyyy-mm-dd HH24:MI') AS log_dt, login_id, session_id, request_method, request_url, friendly_url, portlet_id, portlet_page, user_agent, server_name, server_node
                FROM userlog" . $log_dt . " WHERE to_char(log_dt, 'yyyy-mm-dd') = ? ) a 
                GROUP BY  login_id, TO_DATE(log_dt, 'yyyy-mm-dd')";

        MigrateUtils::logDump('Filter Date => ' . $dt->format('Y-m-d'));
        $users = DB::connection('pgsql_dynatrace')->select($sql, array($dt->format('Y-m-d')));

        LogTraceUserRequest::whereDate('created_at', $dt->format('Y-m-d'))->delete();

        foreach ($users as $user) {
            $user_request = new LogTraceUserRequest();
            $user_request->login_id = $user->user;
            $user_request->created_date = $user->log_dt;
            $user_request->total = $user->total;
            $user_request->created_at = $dt;
            $user_request->save();
        }
    }

    public function export_cube_requests($dt)
    {
        $y = $dt->year;
        $y = substr($y, 2, 2);
        $m = $dt->month;
        $m = strlen($m) < 2 ? '0' . $m : $m;
        $d = $dt->day;
        $d = strlen($d) < 2 ? '0' . $d : $d;

        $log_dt = '_py' . $y . 'm' . $m . 'd' . $d;
        MigrateUtils::logDump(__METHOD__.' > Fetch URL Access Request Today => ' . $log_dt);

        $sql = "SELECT TO_DATE(a.log_dt, 'yyyy-mm-dd') AS log_dt, a.server_name, a.server_node, a.friendly_url,  count(1) AS total FROM (
                SELECT DISTINCT TO_CHAR(log_dt, 'yyyy-mm-dd HH24:MI') AS log_dt, login_id, session_id, request_method, request_url, friendly_url, portlet_id, portlet_page, user_agent, server_name, server_node
                FROM userlog" . $log_dt . " WHERE to_char(log_dt, 'yyyy-mm-dd') = ? ) a 
                GROUP BY  TO_DATE(log_dt, 'yyyy-mm-dd'), server_name, server_node, friendly_url";

        MigrateUtils::logDump(__METHOD__.' > Filter Date => ' . $dt->format('Y-m-d'));
        $frndly_urls = DB::connection('pgsql_dynatrace')->select($sql, array($dt->format('Y-m-d')));

        LogTraceCubeModel::whereDate('created_at', $dt->format('Y-m-d'))->delete();
        foreach ($frndly_urls as $fu) {
            $fu_request = new LogTraceCubeModel();
            $fu_request->friendly_url = $fu->friendly_url;
            $fu_request->log_dt = $fu->log_dt;
            $fu_request->server_name = $fu->server_name;
            $fu_request->server_node = $fu->server_node;
            $fu_request->total = $fu->total;
            $fu_request->created_at = $dt;
            $fu_request->save();
        }
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    public function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error LogTraceCubeJob'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
}
