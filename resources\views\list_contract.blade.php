@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianforms" action="{{url('/find/contract')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
    <style>
        .table thead > tr > th {
            font-size: 11px;
        }
        .table tbody > tr > td {
            font-size: 10px;
        }
    </style>    
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian <PERSON><br>
                <small>Masukkan <span class="text-info" >Contract Physical No, LOA Physical No , Contract No </span> pada carian diatas...</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  @if(isset($error))
                  <p>Carian mesti lebih 3 aksara!</p>
                  @else
                  <p>Tidak dijumpai!</p>
                  @endif
              </div>
            </div>
        </div>
    </div>
    @endif
    
    
    @if($listdataEkontrak != null)
        <div class="block block-alt-noborder full">
          
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Kontrak </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>
                @if($contract != null)
                <div class="table-responsive">
                    <table class="table table-bordered table-vcenter" style="width:100%;">
                        <tbody>
                            <tr>
                                <td width="10%">Contract No.</td>
                                
                                <td><strong><a target="_blank" href="{{url('/find/contractInfo/')}}/{{ $contract->contract_no }}/{{ $listdata[0]->contract_ver_id }}" >{{ $contract->contract_no }}</a></strong></td>
                                
                            </tr>
                            <tr>
                                <td width="10%">Contract name</td>
                                <td><strong>{{$contract->contract_title}}</strong></td>
                            </tr>
                            <tr>
                                <td width="10%">Contract Status</td>
                                <td><strong>{{$contract->contract_status}}</strong></td>
                            </tr>
                            <tr>
                                <td width="10%">Contract Physical No.</td>
                                <td><strong>{{$contract->physical_contract_no}}</strong></td>
                            </tr>
                            <tr>
                                <td width="10%">File Ref No.</td>
                                <td><strong>{{$contract->file_ref_no}}</strong></td>
                            </tr>
                            <tr>
                                <td width="10%">LOA No.</td>
                                <td><strong>{{$contract->loa_no}}</strong></td>
                            </tr>
                            <tr>
                                <td width="10%">Latest Contract Ver ID</td>
                                <td><strong>{{$contract->latest_contract_ver_id}}</strong></td>
                            </tr>
                            <tr>
                                <td>Current Contract Ver ID</td>
                                <td><strong>{{$contract->current_contract_ver_id}}</strong></td>
                            </tr>
                            <tr>
                                <td>Org Gov</td>
                                <td class="text-success"><strong> {{$contract->ptj_name}} - ({{$contract->ptj_code}})</strong> &raquo;
                                    <a href="{{url('find/orgcode/')}}/{{$contract->ptj_code}}" target="_blank"> <i class="fa fa-info-circle"></i> View Detail</a>
                                </td>
                            </tr>
                            <tr>
                                <td>Supplier</td>
                                <td class="text-info"><strong>{{$contract->company_name}} - ({{$contract->ep_no}})</strong> &raquo;
                                    <a href="{{url('find/epno/')}}/{{$contract->ep_no}}" target="_blank"> <i class="fa fa-info-circle"></i> View Detail</a>
                                </td>
                            </tr>
                            <tr>
                                <td>Effective Date</td>
                                <td><strong>{{$contract->eff_date}}</strong></td>
                            </tr>
                            <tr>
                                <td>Expired Date</td>
                                <td><strong>{{$contract->exp_date}}</strong></td>
                            </tr>
                            <tr>
                                <td>Current Status </td>
                                <td><strong>{{$contract->current_status}}</strong></td>
                            </tr>
                            <tr>
                                <td>Fulfilment Detail Status </td>
                                <td><strong>{{$contract->fl_details_status}}</strong></td>
                            </tr>
                            <tr>
                                <td>Agreement Status </td>
                                <td><strong>{{$contract->agreement_status}}</strong></td>
                            </tr>
                            <tr>
                                <td>Doc Contract Status </td>
                                <td><strong>{{$contract->doc_contract_status}}</strong></td>
                            </tr>
                        </tbody>
                    </table>  
                </div>  
                @endif
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">CT CREATED</th>
                            <th class="text-center">CONTRACT VER</th>
                            <th class="text-center">CREATED VER</th>
                            <th class="text-center">WF DATE CREATED </th>
                            <th class="text-center">WF STATUS </th>
                            <th class="text-center">IS CURRENT</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-left">{{ $data->created_date }}</td>
                                <td class="text-left">{{ $data->contract_ver_id }}</td>
                                <td class="text-left">{{ $data->created_date_ver }}</td>
                                <td class="text-left">{{ $data->wf_created_date }}</td>
                                <td class="text-left">{{ $data->status_id }} - {{ $data->status_name }}</td>
                                <td class="text-left">{{ $data->is_current }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="block">
                <div class="block-title">
                    <h1><i class="fa fa-building-o"></i> <strong>List Contract Agreement </strong>
                    </h1>
                </div>
                
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">AGREEMENT DATE CREATED</th>
                            <th class="text-center">AGREEMENT ID</th>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">WF DATE CREATED</th>
                            <th class="text-center">WF STATUS </th>
                            <th class="text-center">IS CURRENT</th>
                           
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listAgreement as $data)
                            <tr>
                                
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->agreement_id }}</td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-left">{{ $data->wf_created_date }}</td>
                                <td class="text-left">{{ $data->status_id }} - {{ $data->status_name }}</td>
                                <td class="text-left">{{ $data->is_current }}</td>
                                
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>    
            <div class="block">
                <div class="block-title">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai eKontrak </strong>
                    </h1>
                </div>
                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">CT PHYSICAL NO</th>
                            <th class="text-center">LOA PHYSICAL NO</th>
                            <th class="text-center">TEMPLATE</th>
                            <th class="text-center">COMPLETED AT</th>
                            <th class="text-center">STATUS</th>
                            <th class="text-center">SUCCESS MIGRATED AT</th>
                            <th class="text-center">MORE</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdataEkontrak as $dataEkontrak)
                            <tr>
                                <td class="text-center">
                                    @if($dataEkontrak->template === 'TEMPLATE_A')
                                    <a href="https://ekontrak.eperolehan.gov.my/main?cid={{$dataEkontrak->contract_id}}&action=view" 
                                       target="_blank" class="text-info">{{ $dataEkontrak->contract_physical_no }}</a>
                                    @elseif($dataEkontrak->template === 'TEMPLATE_C')
                                    <a href="https://ekontrak.eperolehan.gov.my/item-main?c_no={{$dataEkontrak->contract_no}}&cid={{$dataEkontrak->contract_id}}&action=view" 
                                       target="_blank" class="text-info">{{ $dataEkontrak->contract_physical_no }}</a>
                                    @else 
                                    {{ $dataEkontrak->contract_physical_no }}
                                    @endif
                                </td>
                                <td class="text-left">{{ $dataEkontrak->loa_physical_no }}</td>
                                <td class="text-left">{{ $dataEkontrak->template }}</td>
                                <td class="text-center">{{ $dataEkontrak->updated_at }}</td>
                                <td class="text-center">{{ App\Services\EPService::$EKONTRAK_STATUS[$dataEkontrak->status] }}</td>
                                <td class="text-center">{{ $dataEkontrak->migrated_at }}</td>
                                <td class="text-center">
                                    <button type="button" class="action-view-detail btn btn-primary" data-toggle="modal" data-id="{{$dataEkontrak->contract_id}}" data-target="#modal-ekontrak-detail">
                                        Detail
                                    </button>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
         
        </div>
    
        <!-- MODAL: K -->
        <div id="modal-item" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i><span id="modal-list-data-header"> List Items </span></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="table-responsive">
                                    <table id="item-datatable" class="table table-striped table-vcenter">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>

        <!-- MODAL: show ekontrak detail -->
        <div id="modal-ekontrak-detail" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i><span id="modal-list-data-header"> <strong>eKontrak</strong> Info </span></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <span id="contract_id"></span>
                                </div>
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="ekontrak-container">
                                    masuk
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    let APP_URL = {!! json_encode(url('/')) !!}
    
    $('#page-container').removeAttr('class');

    /** WHEN CLICK FOR DELETE ITEM  (APPEAR MODAL CONFIRMATION BOX) **/
    $(document).on("click",'.action-view-detail', function(){
        var contractId = $(this).attr('data-id');

        $("#contract_id").text(contractId);

        var urlGetItem = APP_URL+'/find/contract/detail/'+contractId;

        $.ajax({
            url: urlGetItem,
            type: "get",
            success: function (data) {
                $('.spinner-loading').hide();
                $('.ekontrak-container').html(data.html);
            },
        });

    });
</script>
@endsection



