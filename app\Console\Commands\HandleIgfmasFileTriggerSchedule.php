<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use SSH;
use App\EpSupportActionLog;
use App\Migrate\ReTriggerFileIGFMAS;
use DB;

class HandleIgfmasFileTriggerSchedule extends Command {
    

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'igfmas-file-trigger';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Re-Trigger all files AP511,APOVE (batch/1GFMAS/IN) to re-process  ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        try {
            
            /**Delete first stuck in batch retry*/
            DB::connection('oracle_nextgen_fullgrant')->table('osb_batch_retry_dtl')->whereIn('service_code',['GFM-370','GFM-140','GFM-010'])->delete();
            sleep(3);

            ReTriggerFileIGFMAS::runAll_OUTFOLDER_EP();  // to manual pickup all files in batch/1GFMAS/OUT
            ReTriggerFileIGFMAS::runAll_ServerGFMAS_OUT_FOLDER(); // to manual pickup all files in Server 1GFMAS folder: OUT
            ReTriggerFileIGFMAS::runAll_INFOLDER(); // to manual pickup all files in batch/1GFMAS/IN
            
            // Just delete in retry table. not working trigger by retry.
           

            $logsdata = self::class . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
            dump($logsdata);

        } catch (\Exception $exc) {
            
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }

    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleAP511TriggerSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
