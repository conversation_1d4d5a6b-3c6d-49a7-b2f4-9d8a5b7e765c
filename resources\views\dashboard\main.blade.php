@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/dashboard/main/') }}">Main</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/batch/') }}">Batch File</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/osb/') }}">OSB</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/igfmas/') }}">IGFMAS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/phis/') }}">PHIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/paymentreceipt/') }}">Payment AR502</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/gfmascrm/') }}">CRM</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/mygpis/') }}">MyGPIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/egpa/') }}">EGPA</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/spki/') }}">SPKI</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')

<!-- Content -->
@if (Auth::user())
    <div class="row">
        <div class="col-lg-12 text-right">
            <h5><strong>Requested on :</strong> {{Carbon\Carbon::now()->format('d-M-Y H:i:s')}}</h5>   
        </div>
    </div>
    <div class="row">
        <div class="col-lg-4">
            <div class="widget">
                <div class="widget-extra themed-background-dark">
                    <h5 class="widget-content-light">
                        Quotation Tender <strong>Monitoring</strong>
                    </h5>
                </div>
                <div id="dash_qtmonitoring" class="widget-extra-full">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Quotation Tender <strong>Service Retry</strong>
                    </h5>
                </div>
                <div id="dash_qtServiceRetry">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Simple Quote (SQ) <strong> Today Closed Time</strong>
                    </h5>
                </div>
                <div id="dash_SqCloseTimeDaily">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_softcertMonitoring_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        Softcert Signing (Submission QT) <strong>Monitoring</strong>
                    </h5>
                </div>
                <div id="dash_softcertMonitoring">
                    <div class="text-center" style="padding: 20px;">Click refresh button</div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_gpkiSigningMonitoring_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        GPKI Signing <strong>Monitoring</strong>
                    </h5>
                </div>
                <div id="dash_gpkiSigningMonitoring">
                    <div class="text-center" style="padding: 20px;">Click refresh button</div>
                </div>
            </div>

            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_qtSubmissionMonitoring_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        Submitted QT Today <strong>Monitoring</strong>
                    </h5>
                </div>
                <div id="dash_qtSubmissionMonitoring">
                    <div class="text-center" style="padding: 20px;">Click refresh button</div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        List MMINF on pending send to 1GFMAS - <strong> BACKLOG MMINF</strong>
                    </h5>
                </div>
                <div id="dash_BackLogMMINF">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        User Creation in SSO & Liferay <strong>Monitoring </strong>
                    </h5>
                </div>
                <div id="dash_UserCreation">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Process Payment <strong>Monitoring </strong>
                    </h5>
                </div>
                <div id="dash_ProcessPayment">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Process Remove Task <strong>Monitoring </strong>
                    </h5>
                </div>
                <div id="dash_ProcessRemoveTask">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_EmailNotifications_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        Email Notifications <strong>Monitoring </strong>
                    </h5>
                </div>
                <div id="dash_EmailNotifications">
                    <div class="text-center" style="padding: 20px;">Do not click Refresh (Button) except to check backlog notification, Kindly click on after office hour.
                        Do not click more than one time. Execute on query take a long time to finish.</div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Pending Notification <strong>Monitoring </strong>
                    </h5>
                </div>
                <div id="dash_pending_notification">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        OSB <strong>Service Retry
                            <span class="pull-right badge label-danger" id="totalcounts_osbServiceRetry" style="margin-right: 32px"></span>
                            <span>
                                @if(Auth::user()->isAdvRolesEp())
                                <a href="{{url('/list/osb/batch/retry/trigger/service')}}" target="_blank" accesskey="" class="btn btn-xs btn-red" style="margin-left: 135px">Trigger</a>
                                @endif
                            </span>
                        </strong>
                    </h5>
                </div>
                <div id="dash_osbretry">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        OSB <strong>Notification Retry</strong>
                    </h5>
                </div>
                <div id="dash_osbNotifyRetry">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        OSB <strong>Batch Retry
                            <span>
                                @if(Auth::user()->isAdvRolesEp())
                                <a href="{{url('/list/osb/batch/retry/trigger/batch')}}" target="_blank" accesskey="" class="btn btn-xs btn-red" style="margin-left: 145px">Trigger</a>
                                @endif
                            </span>
                            <span class="pull-right badge label-danger" id="totalcounts_osbBatchRetry" style="margin-right: 30px"></span></strong>
                    </h5>
                </div>
                <div id="dash_osbBatchRetry">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Monitoring Error in eP Scheduler
                    </h5>
                </div>
                <div id="dash_monitorErrorEPScheduler" style="padding: 20px;">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                    <div class="pull-right">
                        <a href="{{ url("/list/1gfmas/folder") }}" target="_blank" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger Batch</a>
                    </div>
                </div>
                <div id="trigger_btn" class="row" style="padding: 0 15px 15px 0; display: none;" disabled>
                    <div class="pull-right">
                        <a id="trigger_url"></a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!!json_encode(url('/'))!!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });


        $(document).ready(function () {
            $('.widget').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();
                $('#basic-datatable').hide();

                $('#modal-list-data-header').text($(this).attr('data-title'));
                if ($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound') {
                    $('#fetch_btn').show();
                    $('#trigger_btn').hide();
                    console.log('fetch');
                } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllService') {
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/service') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title=''\n\
                            data-original-title='Trigger All Service'\n\
                            > Trigger All Service</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger service');
                } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllBatch') {
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/batch') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title='' \n\
                            data-original-title='Trigger All Batch'\n\
                            > Trigger All Batch</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger batch');
                } else if ($(this).attr('data-url') === '/dashboard/listRemoveTask/8') {
                    $('#trigger_btn').hide();
                    $('#fetch_btn').hide();
                } else if( $(this).attr('data-url') === '/dashboard/listRemoveTask/1'){
                    $('#trigger_btn').hide();
                    $('#fetch_btn').hide();
                } else {
                    $('#trigger_btn').hide();
                    $('#fetch_btn').hide();
                    console.log('hide all');
                }

                /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();

                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [{
                                orderable: false,
                                targets: [0]
                            }],
                            pageLength: 10,
                            lengthMenu: [
                                [10, 20, 30, -1],
                                [10, 20, 30, 'All']
                            ]
                        });

                        $('.spinner-loading').hide();
                    }
                });

            });
        //Quotation Tender Monitoring
        $.ajax({
            url: APP_URL + '/dashboard/checkQtMonitoring',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_qtmonitoring').hide().html($data).fadeIn();
            }
        });
        //Quotation Tender Service Retry
        $.ajax({
            url: APP_URL + '/dashboard/qtServiceRetry',
            success: function(data) {
                $data = $(data);
                $('#dash_qtServiceRetry').hide().html($data).fadeIn();
            }
        });
        //Simple Quote (SQ) Today Closed Time
        $.ajax({
            url: APP_URL + '/dashboard/sqCloseTimeDaily',
            success: function(data) {
                $data = $(data);
                $('#dash_SqCloseTimeDaily').hide().html($data).fadeIn();
            }
        });
        //Softcert Signing (Submission QT) Monitoring
        $('#dash_softcertMonitoring_refresh').on("click", function() {
            $('#dash_softcertMonitoring').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();

            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringSoftcert',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_softcertMonitoring').hide().html($data).fadeIn();
                }
            });
        });

        //Refresh call GPKI Latest Date Signing
        $('#dash_gpkiSigningMonitoring_refresh').on("click", function() {
            $('#dash_gpkiSigningMonitoring').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();

            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringSigningGpki',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_gpkiSigningMonitoring').hide().html($data).fadeIn();
                }
            });
        });

        // (submission QT) Monitoring
        $('#dash_qtSubmissionMonitoring_refresh').on("click", function() {
            $('#dash_qtSubmissionMonitoring').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();

            $.ajax({
                url: APP_URL + '/dashboard/checkQtSubmissionMonitoring',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_qtSubmissionMonitoring').hide().html($data).fadeIn();
                }
            });
        });

        //OSB Service Retry
        $.ajax({
            url: APP_URL + '/dashboard/osbretry',
            type: "GET",
            success: function(resp) {
                $data = $(resp.data);
                $('#dash_osbretry').hide().html($data).fadeIn();

                $totalcounts = 0;
                if (resp.totalcounts[0]["totalcounts"] != null) {
                    $totalcounts = resp.totalcounts[0]["totalcounts"];
                }

                $html = "<a href='#modal-list-data' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/list/osb/batch/retry/AllService' data-title='Details of OSB Service Retry' >" + $totalcounts + "</a>";

                $("#totalcounts_osbServiceRetry").html($html);
            }
        });
        setInterval(function() {
            $.ajax({
                url: APP_URL + '/dashboard/osbretry',
                success: function(resp) {
                    $data = $(resp.data);
                    $('#dash_osbretry').hide().html($data).fadeIn();
                }
            });
        }, 300000);
        //OSB Notification Retry
        $.ajax({
            url: APP_URL + '/dashboard/osbnotifyretry',
            success: function(data) {
                $data = $(data);
                $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
            }
        });
        setInterval(function() {
            $.ajax({
                url: APP_URL + '/dashboard/osbnotifyretry',
                success: function(data) {
                    $data = $(data);
                    $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
                }
            });
        }, 300000);
        //OSB Batch Retry
        $.ajax({
            url: APP_URL + '/dashboard/osbbatchretry',
            success: function(resp) {
                $data = $(resp.data);
                $('#dash_osbBatchRetry').hide().html($data).fadeIn();

                $totalcounts = 0;
                if (resp.totalcounts[0]["totalcounts"] != null) {
                    $totalcounts = resp.totalcounts[0]["totalcounts"];
                }

                $html = "<a href='#modal-list-data' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/list/osb/batch/retry/AllBatch' data-title='Details of OSB Batch Retry' >" + $totalcounts + "</a>";

                $("#totalcounts_osbBatchRetry").html($html);
            }
        });
        setInterval(function() {
            $.ajax({
                url: APP_URL + '/dashboard/osbbatchretry',
                success: function(resp) {
                    $data = $(resp.data);
                    $('#dash_osbBatchRetry').hide().html($data).fadeIn();
                }
            });
        }, 300000);
        //Monitor Error in eP Scheduler
        $.ajax({
            url: APP_URL + '/dashboard/monitorErrorEPScheduler',
            success: function(data) {
                $data = $(data);
                $('#dash_monitorErrorEPScheduler').hide().html($data).fadeIn();
            }
        });
        setInterval(function() {
            $.ajax({
                url: APP_URL + '/dashboard/monitorErrorEPScheduler',
                success: function(data) {
                    $data = $(data);
                    $('#dash_monitorErrorEPScheduler').hide().html($data).fadeIn();
                }
            });
        }, 300000);
        //List MMINF on pending send to 1GFMAS - BACKLOG MMINF
        $.ajax({
            url: APP_URL + '/dashboard/backlogmminf',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_BackLogMMINF').hide().html($data).fadeIn();
            }
        });
        setInterval(function() {
            $.ajax({
                url: APP_URL + '/dashboard/backlogmminf',
                success: function(data) {
                    $data = $(data);
                    $('#dash_BackLogMMINF').hide().html($data).fadeIn();
                }
            });
        }, 300000);
        //User Creation in SSO & Liferay Monitoring
        $.ajax({
            url: APP_URL + '/dashboard/userCreation',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_UserCreation').hide().html($data).fadeIn();
            }
        });
        //Process Payment Monitoring
        $.ajax({
            url: APP_URL + '/dashboard/processPayment',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_ProcessPayment').hide().html($data).fadeIn();
            }
        });
        //Process Remove Task Monitoring
        $.ajax({
            url: APP_URL + '/dashboard/processRemoveTask',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_ProcessRemoveTask').hide().html($data).fadeIn();
            }
        });
        //Email Notifications Monitoring
        $('#dash_EmailNotifications_refresh').on("click", function() {
            $('#dash_EmailNotifications').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();

            $.ajax({
                url: APP_URL + '/dashboard/emailNotificationsMonitoring',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_EmailNotifications').hide().html($data).fadeIn();
                }
            });
        });
        //Pending Notifications Monitoring
        $.ajax({
            url: APP_URL + '/dashboard/pendingNotification',
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#dash_pending_notification').hide().html($data).fadeIn();
            }
        });
        
    });
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection