<?php

namespace App\Http\Controllers\AppSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\ProfileService;
use App\Services\Traits\EpWebService;


class PmRoleIssueController extends Controller
{
    use ProfileService;
    use EpWebService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function displayPmDashboardView()
    {
        return view('app_support.dashboard.pm');
    }

    public function getDuplicateRoleError()
    {
        $result = $this->getDuplicateRoleErrorService();

        $html = "
        <style>
            .clickable-row {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }
            .clickable-row:hover {
                background-color: green !important;
            }
        </style>
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>User ID</th>
                        <th>Login ID</th>
                        <th>Org Type ID</th>
                        <th>Role Code</th>
                        <th>Count</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
                    <tr class='clickable-row'>
                        <td style='width: 20%;'><strong>" . htmlspecialchars($data->user_id) . "</strong></td>
                        <td class='text-center'>" . htmlspecialchars($data->login_id) . "</td>
                        <td class='text-center'>" . htmlspecialchars($data->org_type_id) . "</td>
                        <td class='text-center'>" . htmlspecialchars($data->role_code) . "</td>
                        <td class='text-center'>" . htmlspecialchars($data->{'count(*)'}) . "</td>
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function getDuplicateRoleErrorDetail($userId, $roleCode)
    {
        $result = $this->getDuplicateRoleErrorDetailService($userId, $roleCode);

        if (empty($result)) {
            return "<div>No records found for User ID: {$userId} and Role Code: {$roleCode}</div>";
        }

        $html = "
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>User Role ID</th>
                        <th>User ID</th>
                        <th>Login ID</th>
                        <th>Org Type ID</th>
                        <th>Role Code</th>
                        <th>Changed Date</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
                    <tr>
                        <td style='width: 20%;'><strong>{$data->user_role_id}</strong></td>
                        <td class='text-center'>{$data->user_id}</td>
                        <td class='text-center'>{$data->login_id}</td>
                        <td class='text-center'>{$data->org_type_id}</td>
                        <td class='text-center'>{$data->role_code}</td>
                        <td class='text-center'>{$data->changed_date}</td>
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function getUsersWithRoleIssues()
    {
        try {
            $result = $this->getUsersWithRoleIssuesService();
        } catch (\Exception $e) {
            $errorMessage = $e->getMessage();
            return "
            <div style='overflow-x:auto;' class='alert alert-danger'>
                <strong>Error:</strong> {$errorMessage}
            </div>";
        }

        $html = "
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Last Login Date</th>
                        <th>User ID</th>
                        <th>Login ID</th>
                        <th>User Name</th>
                        <th>Email</th>
                        <th>Identification No</th>
                        <th>Org Type ID</th>
                        <th>Org Profile ID</th>
                        <th>Total Role EP</th>
                        <th>Total Role Liferay</th>
                        <th>Diff Role</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td><strong>{$data->last_login_date}</strong></td>
                <td class='text-center'>{$data->user_id}</td>
                <td class='text-center'>{$data->login_id}</td>
                <td class='text-center'><strong>{$data->user_name}</strong></td>
                <td class='text-center'>{$data->email}</td>
                <td class='text-center'>{$data->identification_no}</td>
                <td class='text-center'>{$data->org_type_id}</td>
                <td class='text-center'>{$data->org_profile_id}</td>
                <td class='text-center'>{$data->total_role_ep}</td>
                <td class='text-center'>{$data->total_role_liferay}</td>
                <td class='text-center'><strong>{$data->diff_role}</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }
}