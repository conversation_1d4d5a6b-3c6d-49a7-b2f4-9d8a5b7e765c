from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (
    DateRange,
    Dimension,
    Metric,
    RunReportRequest,
    Filter,
    FilterExpression
)

import os
import json
import imgkit
import sys
from contextlib import redirect_stdout

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = r"service-account-file.json"

def get_pageview(property_id):
    """Runs a report on Google Analytics 4 property for page views by page title and total."""
    client = BetaAnalyticsDataClient()

    try:
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name="pageTitle")],
            metrics=[Metric(name="screenPageViews")],
            date_ranges=[DateRange(start_date="2023-01-01", end_date="today")],
            dimension_filter=FilterExpression(
                filter=Filter(
                    field_name="pageTitle",
                    string_filter=Filter.StringFilter(
                        value="Selamat Datang ke Portal Rasmi ePerolehan - ePerolehan"
                    )
                )
            )
        )

        response = client.run_report(request)

        result = {
            "status": "success",
            "message": "Data retrieved successfully",
            "data": {
                "page_views": 0,
                "image": {
                    "status": "pending",
                    "message": "Image generation is pending",
                    "file_name": "",
                    "file_size": ""
                }
            }
        }

        page_views = 0  # Initialize page_views

        for row in response.rows:
            page_title = row.dimension_values[0].value
            page_views += int(row.metric_values[0].value)

        result["data"]["page_views"] = page_views
        result["data"]["image"]["status"] = "success"
        result["data"]["image"]["message"] = "Image generated successfully"

        # Update HTML content with individual span values
        update_html(page_views)

        # Generate image and update result
        file_name, file_size = generate_image()
        result["data"]["image"]["file_name"] = file_name
        result["data"]["image"]["file_size"] = file_size

        print(json.dumps(result))

    except Exception as e:
        result = {
            "status": "error",
            "message": f"An error occurred: {str(e)}",
            "data": {
                "page_views": 0,
                "image": {
                    "status": "failed",
                    "message": f"Image generation failed: {str(e)}",
                    "file_name": "",
                    "file_size": ""
                }
            }
        }
        print(json.dumps(result))

def update_html(page_views):
    """Update HTML content with individual span values and comma separator."""
    # Format page_views with commas
    page_views_str = "{:,}".format(page_views)

    html_content = """
    <!DOCTYPE html>
    <html>

    <head>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Lato&display=swap" rel="stylesheet">
        <style>
            body {
                background: #292C2F;
                color: #fff;
                font-size: 12pt;
                font-family: 'Lato', sans-serif;
                font-weight: bold;
                /* width: 220px; */
            }

            span {
                display: inline-block;
                padding: 6px;
                border-radius: 3px;
                background: #808285;
            }

            .container {
                text-align: left;
                margin: 5px auto;
            }
        </style>
    </head>

    <body>

        <div class="container">
    """

    # Update HTML content with individual span values and comma separator
    for char in page_views_str:
        if char == ',':
            html_content += f'        ,\n'
        else:
            html_content += f'        <span>{char}</span>\n'

    html_content += """
        </div>

    </body>

    </html>
    """

    # Save the updated HTML content to a file
    with open('updated_visitor_counter.html', 'w') as html_file:
        html_file.write(html_content)

def generate_image():
    """Generate image and return file name and size."""
    # Specify the path to wkhtmltoimage for RHEL
    config = imgkit.config(wkhtmltoimage='/usr/local/bin/wkhtmltoimage')
    # Specify the path to wkhtmltoimage for Windows
    # config = imgkit.config(wkhtmltoimage=r'C:\Program Files\wkhtmltopdf\bin\wkhtmltoimage.exe')

    # Configure wkhtmltoimage options
    options = {
        'format': 'png',
        'quality': 100,
        'crop-w': '235'
    }

    # The first argument is the script name, so we get the second
    output_dir = sys.argv[1]

    # Define the input HTML file and output image file names
    html_file = 'updated_visitor_counter.html'
    output_file = f"{output_dir}/visitor-counter.png"
    
    # Redirect standard output to suppress imgkit console logs
    with open(os.devnull, 'w') as fnull:
        with redirect_stdout(fnull):
            imgkit.from_file(html_file, output_file, options=options, config=config)

    # Get image file size
    file_size = os.path.getsize(output_file)
    file_size_str = f"{file_size / (1024 * 1024):.2f} MB"  # Convert to MB with two decimal places

    return output_file, file_size_str

# Specify the Google Analytics property ID
property_id = "349994770"
get_pageview(property_id)
