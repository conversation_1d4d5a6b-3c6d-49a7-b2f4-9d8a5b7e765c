@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
@if (Auth::user())
<div class="container-fluid d-flex flex-column">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h3><strong>POMS</strong> Incident Tasks Fix</h3>
        <div class="form-group">
            <select id="db_connection_selector" class="form-control">
                <option value="mysql_crm_archive" {{ request('db_connection', 'mysql_crm_archive') == 'mysql_crm_archive' ? 'selected' : '' }}>CRM ARCHIVE</option>
                <option value="mysql_crm" {{ request('db_connection') == 'mysql_crm' ? 'selected' : '' }}>CRM PROD</option>
            </select>
        </div>
    </div>

    <!-- Case Number Input Form -->
    <div class="p-4" style="background-color: {{ request('db_connection') == 'mysql_crm' ? '#FFD1CC' : '#c9d5eb' }}; padding: 20px;">
        <form method="GET" action="{{ route('incident.tasks') }}" class="form-inline mb-4">
            <input type="hidden" name="db_connection" id="db_connection_hidden" value="{{ request('db_connection', 'mysql_crm_archive') }}">

            <div class="form-group">
                <input type="text" name="case_number" id="case_number_input" class="form-control" placeholder="Enter Case Number" value="{{ old('case_number', $caseNumber) }}">
            </div>
            <div class="form-group ml-2">
                <div class="checkbox">
                    <label>
                        <input type="checkbox" name="show_deleted" id="showDeleted" value="1" {{ request('show_deleted') ? 'checked' : '' }}> Show Deleted
                    </label>
                </div>
            </div>
            <button type="submit" class="btn btn-primary ml-2"><i class="fa fa-search"></i> Search</button>
        </form>
    </div>

    <div class=block>
    @if($caseNumber && $tasks->isEmpty())
    <div class="alert alert-warning">No tasks found for case number: {{ $caseNumber }}</div>
    @endif

    @if(!$tasks->isEmpty())
    <div class="table-responsive" style="max-height: 500px; overflow-y: auto; background-color: #f8f9fa">
        <table class="table table-bordered table-striped">
            <thead class="table-dark">
                <tr>
                    <th>Task Number</th>
                    <th>Name</th>
                    <th>Created By</th>
                    <th>Modified By</th>
                    <th>SLA Task Flag</th>
                    <th>Task Created</th>
                    <th>Task Modified</th>
                    <th>Start Date</th>
                    <th>Due Date</th>
                    <th>Acknowledge Time</th>
                    <th>SLA 15 Min Start</th>
                    <th>SLA 15 Min Stop</th>
                    <th>SLA 4 Hr Start</th>
                    <th>SLA 4 Hr Stop</th>
                    <th>SLA Approver Start</th>
                    <th>SLA Approver Stop</th>
                    <th>Execution Time</th>
                    <th>Deleted</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($tasks as $task)
                <tr class="{{ $task->deleted == 1 ? 'table-danger' : '' }}">
                    <td>{{ $task->task_number_c }}</td>
                    <td>{{ $task->name }}</td>

                    <!-- Created By (Dropdown) -->
                    <td>
                        <select class="form-control form-control-sm update-task" data-id="{{ $task->id }}" data-field="created_by">
                            @foreach ($users as $user)
                            <option value="{{ $user->id }}" {{ $task->created_by == $user->id ? 'selected' : '' }}>{{ $user->user_name }}</option>
                            @endforeach
                        </select>
                    </td>

                    <!-- Modified By (Dropdown) -->
                    <td>
                        <select class="form-control form-control-sm update-task" data-id="{{ $task->id }}" data-field="modified_user_id">
                            @foreach ($users as $user)
                            <option value="{{ $user->id }}" {{ $task->modified_user_id == $user->id ? 'selected' : '' }}>{{ $user->user_name }}</option>
                            @endforeach
                        </select>
                    </td>

                    <!-- SLA Task Flag (Static, Non-Editable) -->
                    <td>{{ $task->sla_task_flag_c }}</td>

                    <!-- Editable Date Fields -->
                    @foreach ([
                    'date_entered', 'date_modified', 'date_start', 'date_due',
                    'acknowledge_time_c', 'sla_start_15min_c', 'sla_stop_15min_c', 'sla_start_4hr_c', 'sla_stop_4hr_c',
                    'sla_start_approver_c', 'sla_stop_approver_c', 'date_execution_time_c'
                    ] as $field)
                    <td>
                        <input type="text" class="form-control form-control-sm update-task"
                            data-id="{{ $task->id }}" data-field="{{ $field }}"
                            value="{{ $task->$field ? $task->$field : '' }}" style="width: 145px;">
                    </td>
                    @endforeach

                    <!-- Deleted -->
                    <td>
                        <select class="form-control form-control-sm update-task" data-id="{{ $task->id }}" data-field="deleted">
                            <option value="0" {{ $task->deleted == 0 ? 'selected' : '' }}>No</option>
                            <option value="1" {{ $task->deleted == 1 ? 'selected' : '' }}>Yes</option>
                        </select>
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
    </div>

    <div class="block" style="margin-top: 20px;">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-clock-o"></i><strong> POMS SLA Summary </strong></h1>
        </div>
        @if($caseNumber)
            <!-- Spinner -->
            <div id="poms-spinner" class="text-center" style="padding: 20px;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>

            <!-- Container to inject POMS table -->
            <div id="poms-section"></div>
        @endif
    </div>

</div>
@endif
<!-- <div class="container-fluid h-100 d-flex flex-column" style="height: 100vh;"></div> -->
@endsection

@section('cssprivate')
<style>
    .update-task[data-field="created_by"],
    .update-task[data-field="modified_user_id"] {
        width: 110px;
    }

    .update-task[data-field="deleted"] {
        width: 60px;
    }
    
    .table-danger {
        background-color: #f8d7da !important;
    }
    
    .is-valid {
        border-color: #28a745 !important;
        background-color: rgba(40, 167, 69, 0.1) !important;
    }
    
    .is-invalid {
        border-color: #dc3545 !important;
        background-color: rgba(220, 53, 69, 0.1) !important;
    }
</style>
@endsection

@section('jsprivate')
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        // Handle database connection change
        $('#db_connection_selector').change(function() {
            // Update the hidden field value
            $('#db_connection_hidden').val($(this).val());
            
            // Submit the form
            $('form').submit();
        });

        // Store original values when page loads
        $('.update-task').each(function() {
            $(this).data('original-value', $(this).val());
        });

        // Ensure event is bound only once
        $(document).off('change', '.update-task').on('change', '.update-task', function() {
            let taskId = $(this).data('id');
            let field = $(this).data('field');
            let newValue = $(this).val();
            let originalValue = $(this).data('original-value');
            let $input = $(this);
            
            // Skip if value didn't change
            if (newValue === originalValue) {
                return;
            }

            // Show confirmation dialog
            let confirmed = confirm(
                `Confirm Update:\n\n` +
                `Field: ${field}\n` +
                `From: ${originalValue || '(empty)'}\n` +
                `To: ${newValue || '(empty)'}\n\n` +
                `Are you sure you want to make this change?`
            );

            if (!confirmed) {
                $(this).val(originalValue); // Revert to original value
                return;
            }

            fetch('{{ route("incident.tasks.update") }}', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': '{{ csrf_token() }}',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: taskId,
                        field: field,
                        value: newValue,
                        db_connection: $('#db_connection_hidden').val()
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Show success feedback
                        $input.addClass('is-valid');
                        setTimeout(() => $input.removeClass('is-valid'), 2000);
                        
                        // Update the original value
                        $input.data('original-value', newValue);
                        
                        if (field === 'deleted') {
                            if (newValue == '1') {
                                $input.closest('tr').addClass('table-danger');
                            } else {
                                $input.closest('tr').removeClass('table-danger');
                            }
                        }
                    } else {
                        // Show error feedback and revert
                        $input.addClass('is-invalid');
                        $input.val(originalValue);
                        alert(data.message || 'Failed to update');
                    }
                })
                .catch(error => {
                    $input.addClass('is-invalid');
                    $input.val(originalValue);
                    console.error('Error:', error);
                    alert('An error occurred while updating');
                });
        });

        // Toggle deleted rows visibility
        $('#showDeleted').change(function() {
            $('form').submit();
        });

        // Store original values for SLA summary fields
        $('.update-sla-summary').each(function() {
            $(this).data('original-value', $(this).val());
        });

        // Function to update calculated duration for an SLA row
        function updateCalculatedDuration($row) {
            const actualStartStr = $row.find('.actual-start-datetime-input').val();
            const completedStr = $row.find('.completed-datetime-input').val();
            const $calculatedDurationCell = $row.find('.calculated-duration');
            const $actualDurationInput = $row.find('.actual-duration-input');
            const actualDuration = parseInt($actualDurationInput.val()) || 0;

            if (!actualStartStr || !completedStr) {
                $calculatedDurationCell.html('<span></span>');
                return;
            }

            const actualStart = new Date(actualStartStr);
            const completed = new Date(completedStr);

            if (isNaN(actualStart.getTime()) || isNaN(completed.getTime())) {
                $calculatedDurationCell.html('<span></span>');
                return;
            }

            const duration = (completed - actualStart) / 1000; // Convert to seconds
            
            // Create span with appropriate styling
            const $span = $('<span>').text(duration);
            if (duration !== actualDuration) {
                $span.addClass('text-danger font-weight-bold');
            }
            
            $calculatedDurationCell.html($span);
        }
        
        // Function to update styling for actual_duration input
        function updateActualDurationStyle($input) {
            const $row = $input.closest('tr');
            const actualDuration = parseFloat($input.val());
            // Assuming available_duration is in the 9th td (index 8)
            const availableDurationText = $row.find('td:eq(8)').text(); 
            const availableDuration = parseFloat(availableDurationText);

            if (!isNaN(actualDuration) && !isNaN(availableDuration) && actualDuration > availableDuration) {
                $input.css({'color': 'red', 'border-color': 'red'});
            } else {
                $input.css({'color': '', 'border-color': ''});
            }
        }


        // Event listener for SLA summary actual_start_datetime or completed_datetime changes
        $(document).on('change', '.update-sla-summary.actual-start-datetime-input, .update-sla-summary.completed-datetime-input', function() {
            const $currentRow = $(this).closest('tr');
            updateCalculatedDuration($currentRow);
        });
        
        // Event listener for SLA summary actual_duration changes (for styling)
        $(document).on('input', '.update-sla-summary.actual-duration-input', function() {
            updateActualDurationStyle($(this));
        });


        // Handle SLA Summary Updates (delegated event)
        $(document).on('change', '.update-sla-summary', function() {
            console.log('Triggered SLA summary change handler'); // Debug
            let $input = $(this);
    
            // Prevent multiple rapid triggers
            if ($input.data('is-processing')) {
                console.log('Preventing duplicate update.');
                return;
            }

            $input.data('is-processing', true); // Lock it

            setTimeout(() => {
                $input.removeData('is-processing'); // Unlock after 1 sec
            }, 1000);

            let $row = $input.closest('tr');
            let caseNumber = $row.data('case-number');
            let taskNumber = $row.data('task-number'); // Might be empty string
            let slaType = $row.data('sla-type');
            let field = $input.data('field');
            let newValue = $input.val();
            let originalValue = $input.data('original-value');

            if (newValue === originalValue) {
                return; // No change
            }

            // Confirmation dialog
            let confirmed = confirm(
                `Confirm SLA Update for Case ${caseNumber} (Type: ${slaType}):\n\n` +
                `Field: ${field}\n` +
                `From: ${originalValue || '(empty)'}\n` +
                `To: ${newValue || '(empty)'}\n\n` +
                `Are you sure?`
            );

            if (!confirmed) {
                $input.val(originalValue); // Revert
                // If it was a date field that affected calculation, re-trigger calculation with original value
                if (field === 'actual_start_datetime' || field === 'completed_datetime') {
                    setTimeout(() => updateCalculatedDuration($row) ,0); // Allow DOM to update val first
                }
                if (field === 'actual_duration') {
                    updateActualDurationStyle($input);
                }
                return;
            }

            fetch('{{ route("sla.summary.update") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    case_number: caseNumber,
                    task_number: taskNumber,
                    sla_type: slaType,
                    field: field,
                    value: newValue
                    // db_connection is not needed here as POMS updates go to 'mysql_poms' by default in controller
                })
            })
            .then(response => response.json())
            .then(data => {
                $input.removeClass('is-valid is-invalid'); // Clear previous states
                if (data.success) {
                    $input.addClass('is-valid');
                    setTimeout(() => $input.removeClass('is-valid'), 2000);
                    $input.data('original-value', newValue); // Update original value

                    if (field === 'deleted') {
                        if (newValue == '1') {
                            $row.addClass('table-danger');
                        } else {
                            $row.removeClass('table-danger');
                        }
                        // Optionally reload: location.reload(); or just update UI
                    }
                    // If date fields were updated, ensure calculation is correct
                    if (field === 'actual_start_datetime' || field === 'completed_datetime') {
                        updateCalculatedDuration($row);
                    }
                    if (field === 'actual_duration') {
                        updateActualDurationStyle($input);
                    }

                } else {
                    $input.addClass('is-invalid');
                    $input.val(originalValue); // Revert on failure
                    if (field === 'actual_start_datetime' || field === 'completed_datetime') {
                        setTimeout(() => updateCalculatedDuration($row) ,0);
                    }
                    if (field === 'actual_duration') {
                        updateActualDurationStyle($input); // Revert style too
                    }
                    alert(data.message || 'Failed to update SLA summary.');
                }
            })
            .catch(error => {
                console.error('Error updating SLA summary:', error);
                $input.removeClass('is-valid').addClass('is-invalid');
                $input.val(originalValue); // Revert on error
                if (field === 'actual_start_datetime' || field === 'completed_datetime') {
                    setTimeout(() => updateCalculatedDuration($row) ,0);
                }
                if (field === 'actual_duration') {
                    updateActualDurationStyle($input);
                }
                alert('An error occurred while updating SLA summary.');
            });
        });

        let caseNumber = $('#case_number_input').val();

        if (caseNumber && !window.pomsFetched) {
            window.pomsFetched = true;

            fetch('/poms/fix/fetch-sla-summary', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ case_number: caseNumber })
            })
            .then(response => response.text())
            .then(html => {
                $('#poms-spinner').hide();
                $('#poms-section').hide().html(html).fadeIn();
            })
            .catch(error => {
                console.error('Failed to load SLA Summary:', error);
                $('#poms-spinner').html('<div class="text-danger">Error loading SLA Summary.</div>');
            });
        }

    }); // End of document.ready
</script>
@endsection