<?php

namespace App\Services;

use DB;
use Carbon\Carbon;
use Log;

/**
 * POMS Service
 *
 * <AUTHOR>
 */
class CmsService {

    public function getSlaCsCms($dateStart, $dateEnd) {

        $results = DB::connection('mysql_cms')
                ->table('poms_cs_sla')
                ->whereBetween('case_created_date', [
                    $dateStart,
                    $dateEnd])
                ->select(DB::raw('CASE WHEN SUM(total_case) IS NULL THEN 0
                                    ELSE SUM(total_case) END AS total'))
                ->first();

        return $results;
    }

    public function getSlaItCoordCms($dateStart, $dateEnd) {

        $results = DB::connection('mysql_cms')
                ->table('poms_itcoord_sla')
                ->whereBetween('task_created_date', [
                    $dateStart,
                    $dateEnd])
                ->select(DB::raw('CASE WHEN SUM(total_task) IS NULL THEN 0
                                    ELSE SUM(total_task) END AS total'))
                ->first();

        return $results;
    }

    public function getSlaItSpecCms($dateStart, $dateEnd) {

        $results = DB::connection('mysql_cms')
                ->table('poms_itspec_sla')
                ->whereBetween('task_datetime', [
                    $dateStart,
                    $dateEnd])
                ->select(DB::raw('CASE WHEN SUM(total_task) IS NULL THEN 0
                                    ELSE SUM(total_task) END AS total'))
                ->first();

        return $results;
    }

    public function getSlaSeverityCms($dateStart, $dateEnd) {

        $results = DB::connection('mysql_cms')
                ->table('poms_it_severity_sla')
                ->whereBetween('task_datetime', [
                    $dateStart,
                    $dateEnd])
                ->select(DB::raw('CASE WHEN SUM(total_task) IS NULL THEN 0
                                    ELSE SUM(total_task) END AS total'))
                ->first();

        return $results;
    }

    public function getSlaS4Cms($dateStart, $dateEnd) {

        $results = DB::connection('mysql_cms')
                ->table('poms_approver_sla')
                ->whereBetween('task_datetime', [
                    $dateStart,
                    $dateEnd])
                ->select(DB::raw('CASE WHEN SUM(total_task) IS NULL THEN 0
                                    ELSE SUM(total_task) END AS total'))
                ->first();

        return $results;
    }

}
