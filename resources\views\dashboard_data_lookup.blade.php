@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/ePTable-v1.0.css') }}" rel="stylesheet" />
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
        <div class= "block block-alt-noborder full">
            <div class="block log-header">
                <h1>
                    <i class=""></i>ePerolehan Table
                </h1>
            </div>
            <div class="center-container">
                <div class="input-group">
                    <select id="table_search" name="table_search" class="form-control">
                        <option value="">Please Select</option>
                        <option value="pm_city">PM_CITY</option>
                        <option value="pm_country">PM_COUNTRY </option>
                        <option value="pm_district">PM_DISTRICT</option>
                        <option value="pm_division">PM_DIVISION</option>
                        <option value="pm_salutation">PM_SALUTATION</option>
                        <option value="pm_state">PM_STATE</option>
                        <option value="pm_uom">PM_UOM</option>
                    </select>
                    <button type="button" class="btn btn-primary" id="searching_history" name="searching_history">
                        <div>Search</div>
                    </button>
                </div>
            </div>

            <div class="button-container">
                <a id="download_button" href="{{ url('/eperolehan/download/data/lookup') }}" target="_blank" class="btn btn-sm btn-primary">
                    <i class="fa fa-download"></i> Download
                </a>
            </div>
            <style>
                .button-container {
                    display: flex;
                    justify-content: flex-end;
                }
            </style>

            <div id="table-container" class="table-container table-responsive">
            </div>
        </div>
    @endif

    <!-- END Content -->
@endsection

@section('jsprivate')
    <script>
        document.getElementById('page-container').classList = [];
        App.datatables();


        $(document).ready(function() {
            $('#searching_history').on('click', function() {
                var selectedTable = $('#table_search').val();
                if (selectedTable) {
                    let url = '/search/table/' + selectedTable;

                    $.ajax({
                        url: url,
                        type: 'GET',
                        success: function(response) {

                            var table = $('<table></table>').addClass('dynamic-table');
                            var thead = $('<thead></thead>');
                            var tbody = $('<tbody></tbody>');
                            if (response.headers && response.headers.length > 0) {
                                var headerRow = $('<tr></tr>');
                                response.headers.forEach(function(header) {
                                    var th = $('<th></th>').text(
                                        header); 
                                    headerRow.append(th);
                                });
                                thead.append(headerRow);
                            } else {
                                console.warn("No headers found in response.");
                            }
                            if (response.data && response.data.length > 0) {
                                response.data.forEach(function(row) {
                                    var tr = $('<tr></tr>');
                                    Object.values(row).forEach(function(cellData) {
                                        var td = $('<td></td>').text(cellData);
                                        tr.append(td);
                                    });
                                    tbody.append(tr);
                                });
                            } else {
                                tbody.append(
                                    '<tr><td colspan="100%">No data available</td></tr>');
                            }

                            table.append(thead).append(tbody);
                            $('#table-container').html(table);

                            table.DataTable({
                                order: [
                                    [0, "asc"]
                                ],
                                pageLength: 20,
                                lengthMenu: [
                                    [20, 30, 50, -1],
                                    [20, 30, 50, 'All']
                                ]
                            });
                        },
                        error: function(xhr) {
                            console.error("An error occurred while fetching data: ", xhr
                                .responseText);
                        }
                    });
                } else {
                    alert('Please select a table from the dropdown!');
                }
            });
        });
    </script>

<script>
    document.getElementById('table_search').addEventListener('change', function() {
        const selectedValue = this.value;
        const downloadButton = document.getElementById('download_button');
        downloadButton.href = `{{ url('/eperolehan/download/data/lookup') }}/${selectedValue}`;
    });
</script>

</script>
@endsection
