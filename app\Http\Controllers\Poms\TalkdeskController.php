<?php

namespace App\Http\Controllers\Poms;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\Traits\Poms\PomsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\DB as DB;
use Illuminate\Support\Facades\Storage;

class TalkdeskController extends Controller
{

    use PomsService;

    public function main(Request $request)
    {
        // dd($request->all());
        $type = $request->input('type', 'daily'); // default to 'daily' if 'type' is not provided

        if ($type === 'monthly') {
            $data = $this->getTalkdeskMonthlyData();
        } else {
            $data = $this->getTalkdeskData();
        }

        return view('crmdashboard.poms_talkdesk', ['data' => $data, 'byType' => $type]);
    }


    public function uploadData(Request $request)
    {
        $user = auth()->user()->user_name;
        $status_upload = 'success';
        $error_message = '';

        if ($request->isMethod('post')) {
            $type = $request->input('type');
            $file = $request->file('uploadFile');

            // Store the uploaded file in the storage/poms/talkdesk directory
            $path = Storage::disk('local')->putFileAs(
                'poms/talkdesk',
                $file,
                $file->getClientOriginalName()
            );

            // Open the file for reading
            $handle = fopen(Storage::disk('local')->path($path), 'r');

            // Read the first row (header)
            $header = fgetcsv($handle);

            // Read the remaining rows (data)
            while ($row = fgetcsv($handle)) {
                try {
                    // Check if row has enough columns
                    if (count($row) < 10) {
                        $status_upload = 'failed';
                        $error_message .= "Row does not have enough columns. ";
                        continue;
                    }

                    // Handle different date formats and convert to YYYY-MM-DD format
                    $dateCall = $row[1];
                    $formattedDate = null;
                    
                    // Check if date is already in YYYY-MM-DD format
                    if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateCall)) {
                        // Date is already in MySQL format, validate it
                        try {
                            $parsedDate = Carbon::createFromFormat('Y-m-d', $dateCall);
                            $formattedDate = $parsedDate->format('Y-m-d');
                        } catch (Exception $dateException) {
                            $status_upload = 'failed';
                            $error_message .= "Invalid date value: {$dateCall}. ";
                            continue;
                        }
                    } else {
                        // Try to parse DAY/MONTH/YEAR formats
                        try {
                            // Parse the date in DAY/MONTH/YEAR format and convert to MySQL format
                            $parsedDate = Carbon::createFromFormat('j/n/Y', $dateCall);
                            $formattedDate = $parsedDate->format('Y-m-d');
                        } catch (Exception $dateException) {
                            // If the first format fails, try DD/MM/YYYY format
                            try {
                                $parsedDate = Carbon::createFromFormat('d/m/Y', $dateCall);
                                $formattedDate = $parsedDate->format('Y-m-d');
                            } catch (Exception $dateException2) {
                                // If both formats fail, try zero-padded day with single digit month
                                try {
                                    $parsedDate = Carbon::createFromFormat('d/n/Y', $dateCall);
                                    $formattedDate = $parsedDate->format('Y-m-d');
                                } catch (Exception $dateException3) {
                                    $status_upload = 'failed';
                                    $error_message .= "Invalid date format for date: {$dateCall}. ";
                                    continue;
                                }
                            }
                        }
                    }

                    // Check if date_call already exists in the database
                    $slaTalkdeskTable = $type === 'daily' ? 'sla_mitel' : 'sla_talkdesk_monthly';
                    $date_call_exists = DB::connection('mysql_poms')
                        ->table($slaTalkdeskTable)
                        ->where('date_call', $formattedDate)
                        ->exists();

                    if ($date_call_exists) {
                        // Add error message if date_call already exists
                        $status_upload = 'failed';
                        $error_message .= "Row with Call Date {$formattedDate} already exists and was skipped. ";
                        continue;
                    }

                    // Map the data to the table fields using correct index numbers
                    $insertData = [
                        'date_call' => $formattedDate,            // Use the formatted date
                        'acd_call_offer' => $row[2],              // Total Inbound Contacts
                        'acd_call_handle' => $row[5],             // Actual Answered
                        'call_WI_lvl' => $row[6],                 // Actual Answered within 10 secs
                        'call_abandon_short' => $row[4],          // Total Short Abandoned Contacts
                        'call_abandon_long' => $row[3],           // Total Abandoned Contacts
                        'abandon_percentage' => number_format((float)str_replace('%', '', $row[7]), 2),  // Actual % Abandoned Contacts
                        'answer_percentage' => number_format((float)str_replace('%', '', $row[8]), 2),   // Actual % Answered Contacts
                        'service_level' => number_format((float)str_replace('%', '', $row[9]), 2),       // Actual % SLA
                        'mitel_insert_data_datetime' => Carbon::now(),
                        'filename' => $file->getClientOriginalName(),
                        'periodicity' => $type,
                        'created_by' => $user
                    ];

                    // Insert the data into the database
                    DB::connection('mysql_poms')->table($slaTalkdeskTable)->insert($insertData);
                } catch (Exception $e) {
                    // Add error message if an exception is caught
                    $status_upload = 'failed';
                    $error_message .= "An error occurred: {$e->getMessage()}. ";
                }
            }

            // Close the file
            fclose($handle);
        } else {
            $status_upload = 'failed';
            $error_message = 'Invalid request method';
        }

        return response()->json([
            'status_upload' => $status_upload,
            'error_message' => rtrim($error_message)
        ]);
    }
}
