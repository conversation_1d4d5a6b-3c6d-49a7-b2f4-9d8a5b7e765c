@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

<div class="block">
    @if($type != '')
    <div class="block-title">
        @if($type === 'service')
        <h2><strong>OSB Service Retry</strong> Trigger</h2>
        @else 
        <h2><strong>OSB Batch Retry</strong> Trigger</h2>
        @endif
    </div>
    @if($masterStatus ==='Running' && isset($process))
    <div class="block">
        <div class="alert alert-warning">
            <h4><i class="fa fa-warning"></i> Trigger OSB Retry Is Currently Running... Please Wait Until Finish </h4>
            <h5><strong>Total Record : {{ $process[0]->message }}  
                    <br/> Status : {{ $process[0]->status }}
                    <br/> Date Trigger : {{ $process[0]->created_at }}
                    <br> Trigger By : {{ $process[0]->created_by }}
                    </h5></strong>
        </div>
    </div>
    @endif

    <form id="my-form" action="{{url("/list/osb/batch/retry/trigger")}}/{{$type}}" method="post" class="form-horizontal" onsubmit="return true;">

        @if($status && $status === 'Success')
        <div class="block">
            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> {{ $status }}! </h4>
                @if(isset($triggerResult))
                <p> {{ $triggerResult }}</p>
                @endif
            </div>
        </div>
        @elseif($status && $status === 'Error')
        <div class="alert alert-danger alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> {{ $status }}! </h4> 
            @if(isset($triggerResult))
            <p> {{ $triggerResult }}</p>
            @endif
        </div>
        @endif

        {{ csrf_field() }}
        <div class="block">
            @if($datalist && $datalist !== '')
            <input type="hidden" id="type" name="type" value="{{$type}}"/>
            <div class="form-group">
                <label class="col-md-3 control-label" for="total_service">Total Record </label>
                <div class="input-group">
                    <input type="text" class="form-control" id="total_service" name="total_service" value="{{ $total }}" readonly="true"/>
                </div>
            </div>
            <input type="hidden" id="totaltarget" name="totaltarget" value=""/>
            <div class="form-group">
                <label class="col-md-3 control-label" for="target_system">Target System </label>
                <div class="input-group">
                    <select id="target_system" name="target_system" class="form-control targetSystem"/>
                    <option value="All">All</option>   
                    @foreach($listTargetWithService as $data)
                    @foreach($data as $val=>$key)
                    <option value="{{$val}}" @if($val == old('target_system') ) selected @endif>{{$val}}</option>
                    @endforeach
                    @endforeach
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label" for="service_name">Service Name </label>
                <div class="input-group">
                    <select id="service_name" name="service_name" class="form-control serviceName"/>
                    <option value="All">All</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label class="col-md-3 control-label" for="limit_service">Limit </label>
                <div class="input-group">
                    <input type="number" class="form-control" id="limit_service" name="limit_service" value=""/>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    @if(($status && $status !== 'null')|| ($masterStatus === 'Running'))
                    <button type="submit" class="btn btn-sm btn-info" disabled="true"> Trigger </button>
                    @else 
                    @if(count($datalist) > 0)
                    <button type="submit" class="btn btn-sm btn-info submitBtn"> Trigger </button>
                    @endif
                    @endif
                </div>
            </div>
        </div>
        @endif
    </form>
    @endif
</div>
</div>
<div id="modal_spinner" class="modal fade">
    <div class="modal-dialog modal-sm" style="width: 10%; transform: translate(0, -50%); top: 50%; margin: 0 auto">
        <div class="modal-content">
            <div class="modal-body">
                <center>
                    <i class="fa fa-spinner fa-3x fa-spin" style="color: red"></i><br/><br/>Loading..
                </center>
            </div>    
        </div>
    </div>
</div>
@include('_shared._modalListLogAction')

@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
            ModalListActionLogDatatable.init();
        });</script>

<script>
    var status = "<?php
if (isset($status)) {
    echo $status;
}
?>";

    if (status && status !== '') {
        //window.location.reload(true);
    }
</script>
<script>
    $(document).ready(function () {
        $("#my-form").submit(function () {
            $(".submitBtn").attr("disabled", true);
            return true;
        });
    });
</script>
<script>
    var oldValue = $("input[name=total_service]").val();
    $('.targetSystem').change(function () {
        
        var options = '<option value="All">All</option>';
        var task = $('#target_system option:selected').text();
        var jsondata = <?php echo json_encode($listTargetWithService); ?>;
        for (const [listdata, value] of Object.entries(jsondata)) {
            for (const [key, val] of Object.entries(value)) {
                if (key === task) {
                    for (const [data, vals] of Object.entries(val)) {
                        options += '<option value = "' + vals["service_name"] + '">' + vals["service_name"] + '</option>';
                    }
                }

            }
        }
        $('select[name="service_name"]').html(options);
        var targetName = $(this).find(":selected").val();
        var csrf = $("input[name=_token]").val();
        var serviceType = $("input[name=type]").val();
        var serviceName = $("#service_name option:selected").attr("value");
        
        if(targetName !== 'All'){
            $('#modal_spinner').modal('show');
            $.ajax({
            type: "POST",
            url: "/find/osb/totalbytarget/" + serviceType,
            data: {"_token": csrf, "serviceType": serviceType, "serviceName": serviceName, "targetName": targetName}
        }).done(function (resp) {
            $("input[name=total_service]").val(resp);
            $("input[name=totaltarget]").val(resp);
            $('#modal_spinner').modal('hide');
        });
        }else{
            $("input[name=total_service]").val(oldValue);
        }
        
    });
    
    $('.serviceName').change(function () {
        var serviceName = $(this).find(":selected").val();
        var csrf = $("input[name=_token]").val();
        var serviceType = $("input[name=type]").val();
        var targetName = $("#target_name option:selected").attr("value");
        var totaltarget = $("input[name=totaltarget]").val();
        
        if(serviceName !== 'All'){
            $('#modal_spinner').modal('show');
            $.ajax({
            type: "POST",
            url: "/find/osb/totalbyservice/" + serviceType,
            data: {"_token": csrf, "serviceType": serviceType, "serviceName": serviceName, "targetName": targetName}
        }).done(function (resp) {
            console.log(resp);

            $("input[name=total_service]").val(resp);
            $('#modal_spinner').modal('hide');
        });
        }else{
            $("input[name=total_service]").val(totaltarget);
        }
    });
</script>
@endsection