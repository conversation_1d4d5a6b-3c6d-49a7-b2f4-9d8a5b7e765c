<?php

namespace App\Services\Traits;

use DB;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait ItemService {

    /**
     * Get Detail UNSPSC Item
     * @param type $type
     * @param type $value
     * @return List Item UNSPSC
     */
    protected function getItemDetail($type, $value){

        $query  = "  SELECT  CI.ITEM_ID,CI.EXTENSION_CODE,CI.ITEM_NAME,CI.CREATED_DATE ,
                        CU.UNSPSC_ID,  CU.UNSPSC_CODE,CU.UNSPSC_TITLE,CU.UNSPSC_LEVEL,
                        CT.TYPE_CODE,  CT.TYPE_NAME,
                        CB.BRAND_CODE, CB.BRAND_NAME,
                        CC.COLOR_NAME, CC.COLOR_CODE, 
                        CM.MEASUREMENT_CODE, CM.MEASUREMENT_NAME 
                      FROM CM_ITEM CI,CM_UNSPSC CU, CM_TYPE CT,CM_BRAND CB,CM_MEASUREMENT CM , CM_COLOR CC 
                      WHERE CI.UNSPSC_ID = CU.UNSPSC_ID 
                      AND CI.TYPE_ID = CT.TYPE_ID 
                      AND CI.BRAND_ID = CB.BRAND_ID 
                      AND CI.MEASUREMENT_ID = CM.MEASUREMENT_ID 
                      AND CI.COLOR_ID = CC.COLOR_ID  
                      AND CU.RECORD_STATUS = 1 
                      AND CT.RECORD_STATUS = 1 
                      AND CB.RECORD_STATUS = 1 
                      AND CM.RECORD_STATUS = 1 
                      
                ";
        if($type == 'UNSPSC_ID'){
            $query = $query. "
                     AND CU.UNSPSC_ID  = ?
                    ";
        }
        else if($type == 'UNSPSC_CODE'){
            $query = $query. "
                     AND CU.UNSPSC_CODE = ?
                    ";
        }
        else if($type == 'UNSPSC_TITLE'){
            $query = $query. "
                     AND CU.UNSPSC_TITLE  = ?
                    ";
        }
        else if($type == 'EXTENSION_CODE'){
            $query = $query. "
                     AND CI.EXTENSION_CODE  = ?
                    ";
        }
        else if($type == 'ITEM_NAME'){
            $query = $query. "
                     AND CI.ITEM_NAME  = ?
                    ";
        }
        else if($type == 'SEARCH'){
            $query = $query. "
                     AND ( CU.UNSPSC_TITLE  like ? or CI.ITEM_NAME  like ? )
                    ";
           
            return  DB::connection('oracle_nextgen_rpt')->select($query, array($value,$value));
        }
        else{
            $query = $query. "
                     AND CI.CREATED_DATE >= TO_DATE(TO_CHAR(SYSDATE, 'YYYY-MM-DD') || '00:00:00','YYYY-MM-DD HH24:MI:SS')  
                    ";
            return  DB::connection('oracle_nextgen_rpt')->select($query);
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value));
        return $results;
    }
    
    /**
     * Get Detail UNSPSC Item
     * @param type $type
     * @param type $value
     * @return List Item UNSPSC
     */
    protected function getUNSPSCItemDetail($type, $value){

        $query  = "  SELECT  DISTINCT 
                            CU.UNSPSC_ID, CU.UNSPSC_CODE,CU.UNSPSC_TITLE,CU.UNSPSC_LEVEL,CU.CREATED_DATE 
                      FROM CM_UNSPSC CU 
                      WHERE CU.RECORD_STATUS = 1  
                      
                ";
        if($type == 'UNSPSC_ID'){
            $query = $query. "
                     AND CU.UNSPSC_ID  = ?
                    ";
        }
        else if($type == 'UNSPSC_CODE'){
            $query = $query. "
                     AND CU.UNSPSC_CODE = ?
                    ";
        }
        else if($type == 'UNSPSC_TITLE'){
            $query = $query. "
                     AND CU.UNSPSC_TITLE  like ?
                    ";
        }else{
            $query = $query. "
                     AND CU.CREATED_DATE > TO_DATE(?,'YYYY-MM-DD') 
                    ";
            $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value));
            return $results;
            
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value));
        return $results;
    }

    /**
     * getListItemTypeByUNSPSCID
     * @param type $unspscID
     * @return type
     */
    protected function getListItemTypeByUNSPSCID($unspscID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT TYPE_ID as ID, TYPE_CODE as CODE, TYPE_NAME as NAME , CREATED_DATE  FROM CM_TYPE WHERE UNSPSC_ID = ? ORDER BY TYPE_CODE DESC ", array($unspscID));
        return $results; 
    }
    
    /**
     * getListItemBrandByUNSPSCID
     * @param type $unspscID
     * @return type
     */
    protected function getListItemBrandByUNSPSCID($unspscID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT BRAND_ID as ID, BRAND_CODE as CODE, BRAND_NAME as NAME , CREATED_DATE  FROM CM_BRAND WHERE UNSPSC_ID = ? ORDER BY BRAND_CODE DESC ", array($unspscID));
        return $results; 
    }
    
    /**
     * getListItemMeasurementByUNSPSCID
     * @param type $unspscID
     * @return type
     */
    protected function getListItemMeasurementByUNSPSCID($unspscID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT MEASUREMENT_ID as ID, MEASUREMENT_CODE as CODE, MEASUREMENT_NAME as NAME , CREATED_DATE  FROM CM_MEASUREMENT WHERE UNSPSC_ID = ? ORDER BY MEASUREMENT_CODE DESC ", array($unspscID));
        return $results; 
    }
    
    /**
     * getListItemMeasurementByUNSPSCID
     * @param type $unspscID
     * @return type
     */
    protected function getListItemColor(){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COLOR_ID as ID, COLOR_CODE as CODE, COLOR_NAME as NAME , CREATED_DATE FROM CM_COLOR ORDER BY COLOR_CODE DESC ");
        return $results; 
    }
    
    
    /**
     * getListProductSupplierPendingCodification
     * @param type $unspscID
     * @return type
     */
    protected function getListProductSupplierPendingCodification($type, $value){

        $query  = " SELECT CRC.CHANGED_DATE,(SELECT USER_NAME FROM PM_USER WHERE USER_ID = CRC.CHANGED_BY) AS CHANGED_BY,TO_CHAR (CWS.CREATED_DATE,'DD/MM/YYYY HH24:MI:SS') CODE_REQUEST_DATE , CRC.DOC_NO,CWS.DOC_TYPE,
                    DECODE (CWS.DOC_TYPE,  'CU' , 'Unspsc_Doc', 'CE', 'Extension_Doc') AS DOC_TYPE,
                    REPLACE (REPLACE (REPLACE (NVL(TEMP_TITLE,ITEM_NAME) , CHR (9), ' '),
                                               CHR (10),
                                               ' '
                                              ),
                                      CHR (13),
                                      ' '
                                     ) AS PRODUCT_NAME, 
                    (SELECT D.STATUS_NAME FROM PM_STATUS_DESC D WHERE D.STATUS_ID = CWS.STATUS_ID AND D.LANGUAGE_CODE = 'en' ) STATUS_NAME,
                    SS.SUPPLIER_ID  ,SS.EP_NO, SS.COMPANY_NAME ,  NVL(MA.MOF_NO, 'N/A') MOF_NO
                    FROM CM_REQUEST_CODE CRC , CM_WORKFLOW_STATUS CWS, SM_SUPPLIER SS , SM_MOF_ACCOUNT MA, PM_USER U 
                    WHERE CRC.SUPPLIER_ID = SS.SUPPLIER_ID
                    AND CRC.REQUEST_ID  = CWS.DOC_ID
                    AND CRC.SUPPLIER_ID = MA.SUPPLIER_ID (+)
                    AND CRC.CHANGED_BY = U.USER_ID (+) 
                    AND CWS.IS_CURRENT = 1
                    AND CRC.RECORD_STATUS = 1 
                    ";
        if($type == 'LIKE_SEARCH'){
            $query = $query. "
                     AND ( 
                        SS.COMPANY_NAME LIKE ? 
                        OR CRC.ITEM_NAME LIKE ? 
                        OR U.IDENTIFICATION_NO LIKE ? 
                        )
                    ";
            $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value,$value,$value));
            return $results;
        }
        else if($type == 'EP_NO'){
            $query = $query. "
                     AND SS.EP_NO  = ? 
                    ";
        }
        else if($type == 'MOF_NO'){
            $query = $query. "
                     AND MA.MOF_NO  = ? 
                    ";
        }
        else if($type == 'DOC_NO'){
            $query = $query. "
                     AND CRC.DOC_NO  = ? 
                    ";
        }
        else{
            return array();
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value));
        return $results;
    }

    
    /**
     * Show history task by each team codification
     * getListProductSupplierCodificationTask
     * @param type $unspscID
     * @return type
     */
    protected function getListProductSupplierCodificationTask($type, $value,$date = null){
        // U.ORG_TYPE_ID IN ('9','6')  CDC & BPK 
        $query  = " SELECT CWS.CREATED_DATE AS CREATED_DATE_STATUS,U.IDENTIFICATION_NO,U.USER_NAME as NAME,
                CRC.DOC_NO,REPLACE (REPLACE (REPLACE (NVL(TEMP_TITLE,ITEM_NAME) , CHR (9), ' '),
                                               CHR (10),
                                               ' '
                                              ),
                                      CHR (13),
                                      ' '
                                     ) AS ITEM_NAME,D.STATUS_NAME, 
                SS.COMPANY_NAME, SS.EP_NO, SS.REG_NO 
                FROM CM_REQUEST_CODE CRC , CM_WORKFLOW_STATUS CWS, PM_STATUS_DESC D , PM_USER U , SM_SUPPLIER SS 
                WHERE CRC.REQUEST_ID = CWS.DOC_ID 
                AND CRC.SUPPLIER_ID = SS.SUPPLIER_ID 
                AND D.STATUS_ID = CWS.STATUS_ID AND D.LANGUAGE_CODE = 'en' 
                AND CWS.CREATED_BY = U.USER_ID 
                AND U.ORG_TYPE_ID IN ('9','6') 
                AND CRC.RECORD_STATUS = 1 
                    ";
        if($date != null){
            $query = $query. "
                     AND TO_CHAR(CWS.CREATED_DATE,'YYYY-MM-DD')  = '$date'  
                    ";
        }
        if($type == 'LIKE_SEARCH'){
            $query = $query. "
                     AND 
                       ( 
                        CRC.TEMP_TITLE LIKE ? 
                        OR CRC.ITEM_NAME LIKE ? 
                        OR U.IDENTIFICATION_NO LIKE ? 
                        )
                        
                    ";
            $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value,$value,$value));
            return $results;
        }
        else if($type == 'DOC_NO'){
            $query = $query. "
                     AND CRC.DOC_NO  = ? 
                    ";
        }
       
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($value));
        return $results;
    }
}
