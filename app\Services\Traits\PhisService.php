<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;


/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait PhisService {

    /**
     * show CR details
     * @return list
     */    
       
    protected function getPhisNo($phisNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
           "select ax.PHIS_NO as order_reference_no, 
            bx.changed_date as created_date,
            ax.doc_no as cr_no, ex.DOC_NO as co_no,
            dx.status_name as status_ep , v.ORG_CODE as ptj_code
            from fl_fulfilment_request ax
            left join fl_workflow_status bx on ax.fulfilment_req_id = bx.doc_id and bx.doc_type in ('CR')
            left join pm_status cx on bx.status_id = cx.status_id
            left join pm_status_desc dx on cx.status_id = dx.status_id and dx.language_code ='en'
            left join FL_FULFILMENT_ORDER ex on ex.FULFILMENT_REQ_ID=ax.FULFILMENT_REQ_ID
            left join pm_org_profile pm on ax.CREATED_ORG_PROFILE_ID = pm.ORG_PROFILE_ID and pm.record_status = 1
            left join pm_org_validity v on pm.org_profile_id = v.org_profile_id and v.record_status = 1
            left join pm_parameter a on a.parameter_id = pm.org_type_id
            left join pm_parameter_desc b on a.parameter_id = b.parameter_id and b.language_code = 'en'
            left join pm_org_profile pm1 on pm1.org_profile_id = pm.parent_org_profile_id and pm1.record_status = 1
            left join pm_org_validity v1 on pm1.org_profile_id = v1.org_profile_id and v1.record_status = 1
            left join pm_parameter a1 on a1.parameter_id = pm1.org_type_id
            left join pm_parameter_desc b1 on a1.parameter_id = b1.parameter_id and b1.language_code = 'en'
            left join pm_org_profile pm2 on pm2.org_profile_id = pm1.parent_org_profile_id and pm2.record_status = 1
            left join pm_org_validity v2 on pm2.org_profile_id = v2.org_profile_id and v2.record_status = 1
            left join pm_parameter a2 on a2.parameter_id = pm2.org_type_id
            left join pm_parameter_desc b2 on a2.parameter_id = b2.parameter_id and b2.language_code = 'en'
            left join pm_org_profile pm3 on pm3.org_profile_id = pm2.parent_org_profile_id and pm3.record_status = 1
            left join pm_org_validity v3 on pm3.org_profile_id = v3.org_profile_id and v3.record_status = 1
            left join pm_parameter a3 on a3.parameter_id = pm3.org_type_id
            left join pm_parameter_desc b3 on a3.parameter_id = b3.parameter_id and b3.language_code = 'en'
            where ax.phis_no = ? 
            ORDER BY bx.changed_date DESC", array($phisNo));        

        return $results;
    }
    
    /**
     * show CR/CO details
     * @return list
     */   
    
    protected function getDocNoCRCO($docNo){
        $parameters = array();
        $type   = substr($docNo, 0, 2); 
        $query = "select ax.PHIS_NO as order_reference_no, 
            bx.changed_date as created_date,
            ax.doc_no as cr_no, ex.DOC_NO as co_no,
            dx.status_name as status_ep , v.ORG_CODE as ptj_code
            from fl_fulfilment_request ax
            left join fl_workflow_status bx on ax.fulfilment_req_id = bx.doc_id and bx.doc_type in ('CR')
            left join pm_status cx on bx.status_id = cx.status_id
            left join pm_status_desc dx on cx.status_id = dx.status_id and dx.language_code ='en'
            left join FL_FULFILMENT_ORDER ex on ex.FULFILMENT_REQ_ID=ax.FULFILMENT_REQ_ID
            left join pm_org_profile pm on ax.CREATED_ORG_PROFILE_ID = pm.ORG_PROFILE_ID and pm.record_status = 1
            left join pm_org_validity v on pm.org_profile_id = v.org_profile_id and v.record_status = 1
            left join pm_parameter a on a.parameter_id = pm.org_type_id
            left join pm_parameter_desc b on a.parameter_id = b.parameter_id and b.language_code = 'en'
            left join pm_org_profile pm1 on pm1.org_profile_id = pm.parent_org_profile_id and pm1.record_status = 1
            left join pm_org_validity v1 on pm1.org_profile_id = v1.org_profile_id and v1.record_status = 1
            left join pm_parameter a1 on a1.parameter_id = pm1.org_type_id
            left join pm_parameter_desc b1 on a1.parameter_id = b1.parameter_id and b1.language_code = 'en'
            left join pm_org_profile pm2 on pm2.org_profile_id = pm1.parent_org_profile_id and pm2.record_status = 1
            left join pm_org_validity v2 on pm2.org_profile_id = v2.org_profile_id and v2.record_status = 1
            left join pm_parameter a2 on a2.parameter_id = pm2.org_type_id
            left join pm_parameter_desc b2 on a2.parameter_id = b2.parameter_id and b2.language_code = 'en'
            left join pm_org_profile pm3 on pm3.org_profile_id = pm2.parent_org_profile_id and pm3.record_status = 1
            left join pm_org_validity v3 on pm3.org_profile_id = v3.org_profile_id and v3.record_status = 1
            left join pm_parameter a3 on a3.parameter_id = pm3.org_type_id
            left join pm_parameter_desc b3 on a3.parameter_id = b3.parameter_id and b3.language_code = 'en'";        
        if($type == 'CR'){            
            $query = $query."WHERE ax.doc_no = ?  ORDER BY bx.changed_date DESC";
            array_push($parameters, $docNo);
        }
        if($type == 'CO'){
            $query = $query."WHERE ex.doc_no = ? ORDER BY bx.changed_date DESC";
            array_push($parameters, $docNo);
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);        
        return $results;

    }  
    
    protected function checkContract($docNo) {

         return DB::connection('oracle_nextgen_rpt')
                    ->table('ct_contract c')
                    ->join('ct_contract_value vl', 'c.latest_contract_ver_id', '=', 'vl.contract_ver_id')
                    ->join('ct_contract_ver v', 'v.contract_ver_id', '=', 'c.latest_contract_ver_id')
                    ->where('c.contract_no',$docNo)
                    ->select('c.contract_no as contractno','v.contract_physical_no as physicalno','vl.eff_date as effdate','vl.exp_date as expdate','c.record_status as contractstatus','c.is_zonal_pricing as iszonal')
                    ->first();
    }
    
    protected function checkZonal($docNo) {
   
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as zonalcount
                    FROM ct_contract_zone z
                    WHERE z.contract_ver_id IN (SELECT latest_contract_ver_id FROM ct_contract
                    WHERE contract_no = ?)", array($docNo));

        return $query;
    }
    
    protected function checkItems($docNo) {
        
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as itemcount
                    FROM ct_contract_item c
                    WHERE c.contract_ver_id IN (SELECT latest_contract_ver_id FROM ct_contract
                    WHERE contract_no =  ?)", array($docNo));

        return $query;
    }
    
    protected function getPhisItems($docNo, $skuNo) {
        $parameters = array();
        $query = "SELECT
                        distinct t.extension_code as extensioncode, 
                        i.sku as sku,
                        REPLACE (REPLACE (r.item_name, CHR (13), ''), CHR (10), '') as itemname,
                        u.UOM_CODE as uom,
                        r.min_qty as minqty, 
                        m.max_quantity as maxqty,
                        i.RECORD_STATUS as itemsuspend,
                        i.eff_date as suspendfrom,
                        i.exp_date as suspendto,
                        DECODE (i.record_status, 1, 'Active', 'Inactive') as statusitem
                            FROM ct_contract_item i,
                                ct_contract_ver v,
                                sc_request_item r,
                                ct_item_price p,
                                ct_item_max_qty m,
                                cm_item t,
                                pm_uom u,
                                ct_contract_zone z,
                                ct_contract c,
                                ct_contract_value cl
                                WHERE i.contract_ver_id = v.contract_ver_id 
                                    AND p.contract_item_id = i.contract_item_id 
                                    AND i.request_item_id = r.request_item_id 
                                    AND r.item_id = t.item_id -- done
                                    AND m.contract_item_id = i.contract_item_id 
                                    and m.CONTRACT_ZONE_ID = z.CONTRACT_ZONE_ID
                                    AND u.uom_id = r.uom_id 
                                    AND p.contract_zone_id = z.contract_zone_id 
                                    AND z.contract_ver_id = c.latest_contract_ver_id 
                                    AND v.contract_ver_id = c.latest_contract_ver_id 
                                    AND c.latest_contract_ver_id = cl.contract_ver_id 
                                    ";
        if($docNo != null && strlen($docNo) > 0){
            $query = $query." AND c.contract_no = ? ";
            array_push($parameters, $docNo);
        }
        if($skuNo != null && strlen($skuNo) > 0){
            $query = $query." AND i.sku IN (?) ";
            array_push($parameters, $skuNo);
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        return $results;
    }
    
    protected function getZonal($docNo,$skuNo,$uom,$itemstatus) {
        
        $query = DB::connection('oracle_nextgen_rpt')->select(DB::raw("SELECT
                        t.extension_code as extensioncode, 
                        i.sku as sku,
                        u.UOM_CODE as uom,
                        r.min_qty as minqty, 
                        m.max_quantity as maxqty, 
                        REPLACE (REPLACE (r.item_name, CHR (13), ''), CHR (10), '') as itemname,
                        REPLACE (REPLACE (r.item_desc, CHR (13), ''), CHR (10), '') as itemdesc, 
                        z.zone_name as zonename, 
                        p.unit_price as unitprice,
                        i.RECORD_STATUS as itemsuspend
                            FROM ct_contract_item i,
                                ct_contract_ver v,
                                sc_request_item r,
                                ct_item_price p,
                                ct_item_max_qty m,
                                cm_item t,
                                pm_uom u,
                                ct_contract_zone z,
                                ct_contract c,
                                ct_contract_value cl
                                WHERE i.contract_ver_id = v.contract_ver_id 
                                    AND p.contract_item_id = i.contract_item_id 
                                    AND i.request_item_id = r.request_item_id 
                                    AND r.item_id = t.item_id -- done
                                    AND m.contract_item_id = i.contract_item_id 
                                    AND m.CONTRACT_ZONE_ID = z.CONTRACT_ZONE_ID
                                    AND u.uom_id = r.uom_id 
                                    AND p.contract_zone_id = z.contract_zone_id 
                                    AND z.contract_ver_id = c.latest_contract_ver_id 
                                    AND v.contract_ver_id = c.latest_contract_ver_id 
                                    AND c.latest_contract_ver_id = cl.contract_ver_id 
                                    AND c.contract_no = ?
                                    AND i.sku IN (?)
                                    AND u.UOM_CODE = ?
                                    AND i.RECORD_STATUS = ?
                                    "),array($docNo,$skuNo,$uom,$itemstatus));
        
        return $query;
        
    }
    
    protected function getSkuNo($docNo) {
        
        $query = DB::connection('oracle_nextgen_rpt')->select(DB::raw("SELECT 
                        i.sku as sku,
                        u.UOM_CODE as uom,
                        r.min_qty as minqty, 
                        m.max_quantity as maxqty, 
                        REPLACE (REPLACE (r.item_name, CHR (13), ''), CHR (10), '') as itemname,
                        REPLACE (REPLACE (r.item_desc, CHR (13), ''), CHR (10), '') as itemdesc, 
                        z.zone_name as zonename, 
                        p.unit_price as unitprice,
                        i.RECORD_STATUS as itemsuspend
                            FROM ct_contract_item i,
                                ct_contract_ver v,
                                sc_request_item r,
                                ct_item_price p,
                                ct_item_max_qty m,
                                cm_item t,
                                pm_uom u,
                                ct_contract_zone z,
                                ct_contract c,
                                ct_contract_value cl
                                WHERE i.contract_ver_id = v.contract_ver_id 
                                    AND p.contract_item_id = i.contract_item_id 
                                    AND i.request_item_id = r.request_item_id 
                                    AND r.item_id = t.item_id -- done
                                    AND m.contract_item_id = i.contract_item_id 
                                    AND m.CONTRACT_ZONE_ID = z.CONTRACT_ZONE_ID
                                    AND u.uom_id = r.uom_id 
                                    AND p.contract_zone_id = z.contract_zone_id 
                                    AND z.contract_ver_id = c.latest_contract_ver_id 
                                    AND v.contract_ver_id = c.latest_contract_ver_id 
                                    AND c.latest_contract_ver_id = cl.contract_ver_id 
                                    AND c.contract_no = ?
                                    "),array($docNo));
        
        return $query;
    }
    
   protected function getContractInfoPhis160($docNo) {
        
       $type   = substr($docNo, 0, 2);
       
       $query = "
            select fr.fulfilment_req_id,
            fr.DOC_NO as contractreqno, florder.DOC_NO as contractorderno, fr.PHIS_NO as phisno,
            (select CONTRACT_NO from ct_contract con where con.CONTRACT_ID = fr.CONTRACT_ID ) as contractphysicalno,
            (select ORG_CODE from pm_org_validity ptj where ptj.ORG_PROFILE_ID = fr.CREATED_ORG_PROFILE_ID and record_status = 1 ) as orgcode,
            fr.AG_APPROVED_DATE as actioneddate,
            nvl (sum (item.ORDERED_AMT ),0) as coamount
            from fl_fulfilment_request fr
            left join FL_FULFILMENT_ORDER florder on florder.FULFILMENT_REQ_ID = fr.FULFILMENT_REQ_ID
            left join FL_DELIVERY_ADDRESS addr on addr.FULFILMENT_REQ_ID = florder.FULFILMENT_REQ_ID
            left join FL_FULFILMENT_ITEM_ADDR item on item.FULFILMENT_ADDR_ID = addr.DELIVERY_ADDRESS_ID ";
       
       if($type == 'CO'){
            $query = $query. "
                    where florder.doc_no = ?
                    ";
        }
        
        else if($type == 'CR'){
            $query = $query. "
                    where fr.DOC_NO = ?
                    ";
        } else {
            $query = $query. "
                    where fr.PHIS_NO = ?
                    ";
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query."group by fr.fulfilment_req_id,fr.DOC_NO,florder.DOC_NO,fr.PHIS_NO,fr.CONTRACT_ID,fr.CREATED_ORG_PROFILE_ID,fr.AG_APPROVED_DATE", array($docNo));
        return $results;

   }
   
   protected function getItemPhis160($docNo) {
       $type   = substr($docNo, 0, 2);
       
       
       
       
       $query = "
            select 
                (select CHARGE_LINE_SEQ from fl_fulfilment_item fi where fi.FULFILMENT_ITEM_ID = item.FULFILMENT_ITEM_ID  )sequence_item,
                addr.PM_ADDRESS_ID as deliveryaddrid,
                (select substr(ORG_CODE,3,6) from pm_org_validity ptj where ptj.ORG_PROFILE_ID = fr.CREATED_ORG_PROFILE_ID and record_status = 1 ) as ptjdipertanggung,
                (select VOT_FUND_CODE from pm_vot_fund vf where vf.VOT_FUND_ID = item.VOT_FUND_ID)||(select substr(ORG_CODE,1,2) from pm_org_validity ptj where ptj.ORG_PROFILE_ID = fr.CREATED_ORG_PROFILE_ID and record_status = 1 ) as vot,
                (select VOT_FUND_CODE from pm_vot_fund vf where vf.VOT_FUND_ID = item.VOT_FUND_ID)|| (select pac.PRG_ACTIVITY_CODE from pm_prg_activity pac where pac.PRG_ACTIVITY_id = item.PRG_ACTIVITY_ID ) as programactivity,
                (select se.PROJECT_CODE from pm_sub_setia se where se.SUB_SETIA_ID = item.SUB_SETIA_ID ) as project,
                (select se.SETIA_CODE from pm_sub_setia se where se.SUB_SETIA_ID = item.SUB_SETIA_ID ) as setia,
                (select se.SUB_SETIA_CODE from pm_sub_setia se where se.SUB_SETIA_ID = item.SUB_SETIA_ID ) as sub_setia,
                (select GL_ACC_CODE from pm_gl_account ga where ga.GL_ACCOUNT_ID = item.GL_ACCOUNT_ID ) as glaccount,
                (select PARAMETER_CODE from pm_parameter param where param.PARAMETER_ID =  item.ASSET_IND ) as indicator,
                item.ITEM_CODE as itemcode, item.ORDERED_QTY as quantity,item.UNIT_PRICE as unitprice , 
                uom.uom_code as uom
                    from fl_fulfilment_request fr
                    left join FL_FULFILMENT_ORDER florder on florder.FULFILMENT_REQ_ID = fr.FULFILMENT_REQ_ID
                    left join FL_DELIVERY_ADDRESS addr on addr.FULFILMENT_REQ_ID = florder.FULFILMENT_REQ_ID
                    left join FL_FULFILMENT_ITEM_ADDR item on item.FULFILMENT_ADDR_ID = addr.DELIVERY_ADDRESS_ID
                    left join sc_request_item scitem on scitem.request_item_id = item.request_item_id
                    left join pm_uom uom on uom.uom_id = scitem.uom_id
                    left join cm_item cm on cm.item_id = scitem.item_id ";
       
       if($type == 'CO'){
            $query = $query. "
                    where florder.doc_no = ?
                    ";
        }
        
        else if($type == 'CR'){
            $query = $query. "
                    where fr.DOC_NO = ?
                    ";
        } else {
            $query = $query. "
                    where fr.PHIS_NO = ?
                    ";
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query.
                "group by fr.DOC_NO,florder.DOC_NO,fr.PHIS_NO,fr.CONTRACT_ID,fr.CREATED_ORG_PROFILE_ID,item.FULFILMENT_ITEM_ID,addr.PM_ADDRESS_ID,
                item.VOT_FUND_ID,item.PRG_ACTIVITY_ID ,item.GL_ACCOUNT_ID,item.SUB_SETIA_ID ,item.SUB_SETIA_ID,item.SUB_SETIA_ID ,
                item.ASSET_IND,item.ITEM_CODE,item.ORDERED_QTY,item.UNIT_PRICE ,item.UOM_ID,uom.uom_code
                order by 1 asc", array($docNo));
        return $results;

   }
   
   protected function getTotalAmountCr($id) {


        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT NVL (SUM (ffid.ordered_amt), 0) as coamount
                    FROM fl_fulfilment_item_addr ffid,
                    fl_delivery_address fda
                        WHERE ffid.fulfilment_addr_id = fda.delivery_address_id
                        AND fda.fulfilment_req_id = ?", array($id));
        return $results;
    }

}
