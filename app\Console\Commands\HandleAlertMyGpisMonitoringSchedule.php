<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\BiMigrateMyGPIS;
use Mail;
use Log;
use Config;

class HandleAlertMyGpisMonitoringSchedule extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleAlertMyGpisMonitoring {sessionMyGpis} {hourCompare}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This daily check to monitor Cronjob MyGpis running smoothly. If not will notify alert whatapp to support operation';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(__METHOD__ . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $sessionMyGpis = $this->argument('sessionMyGpis');
            $hourCompare = $this->argument('hourCompare');
            BiMigrateMyGPIS::checkMonitoringMyGpisDailyIntegration($sessionMyGpis,$hourCompare);
        } catch (\Exception $exc) {

            Log::error(__METHOD__. '>> error happen!! ' . $exc->getMessage());
            Log::error(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' >> '. $e->getMessage());
            return $e;
        }
    }
    
}
