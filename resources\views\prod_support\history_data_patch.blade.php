@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li class="active">
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div>
<div class="block block-alt-noborder full">
    <form action="{{ url('/prod-support/history-data-patch') }}" method="post">
        {{ csrf_field() }}
        <fieldset>
        <div class="form-group">

            <label class="col-md-1 text-left crmno1" for="crmno">CRM Number</label>
            <div class="col-md-2 carianform">
                <input type="number" id="crmno1" name="crmno1" value="{{old('crmno1')}}" class="form-control">
            </div>
            <label class="col-md-1 text-left" for="modulename">Module</label>
            <div class="col-md-2">
                <select id="modulename" name = "modulename" class="form-control">
                    <option value="">Please Select</option>
                    @foreach($getModule as  $key)
                    <option value="{{$key->id}}" @if($key->id == old('modulename') ) selected @endif>{{$key->name}}</option>
                    @endforeach
                </select>
            </div>
            <label class="col-md-1 text-left company_name1" for="company_name">Company Name</label>
            <div class="col-md-2">
                <input type="text" id="company_name" name="company_name" value="{{old('company_name')}}" class="form-control">
            </div>
            
            <label class="col-md-1 text-right" for="date" >Date</label>
                                <div class="col-md-2 date">
                                    <input id = 'date1' name="date1" type="date" value="{{old('date1')}}" class="form-control" >
                                </div> 
            <div class="form-actions form-actions-button text-right ">
                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
            </div>
        </div>
    </fieldset>
    </form>
</div>

@if(($listdata != null) || ($listdatasix != null) || ($listdataurgent != null))
<div class="block block-alt-noborder full">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> Patching History</strong></h1>
    </div>
    @foreach($listdata as $list)  
    @if($list[0]->status !== 'Open')
    <div class="table-responsive">
        <h5><strong> {{$list[0]->datetime_porting}} : {{$list[0]->name_port}}</strong></h5> <h5><strong>Status : @if($list[0]->status == 'Closed') <span style="color: red;"> {{$list[0]->status}} </span> @elseif ($list[0]->status  == 'Cancelled') {{$list[0]->status}} <a function="byPort" crmid = "{{ $list[0]->datafixid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatchAll"><i class="fa fa-repeat"></i></a>@endif&nbsp;&nbsp;&nbsp;&nbsp; @if($list[0]->status == 'Closed') Closed By : {{$list[0]->closed_by}}@endif</strong></h5>
        
        <table id = ""  class="table table-vcenter table-condensed table-bordered history-datatable">
            <thead>
                <tr>
                    <th class="text-center">CRM No</th>
                    <th class="text-center">Redmine No</th>
                    <th class="text-center">Module</th>
                    <th class="text-center">Problem Description</th>
                    <th class="text-center">Requester Type</th>
                    <th class="text-center">Requester Name</th>
                    <th class="text-center">Problem Type</th>
                    <th class="text-center">Endorcement By</th>
                    <th class="text-center">Created By</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Change By</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($list as $listed)
                <tr>
                    <td class="text-center">{{ $listed->crm_no }}</td>
                    <td class="text-center">{{ $listed->redmineno }}</td>
                    <td class="text-center">{{ $listed->module }}</td>
                    <td class="text-center">{{ $listed->Problem_Description }}</td>
                    <td class="text-center">{{ $listed->Requester_Type }}</td>
                    <td class="text-center">{{ $listed->requester_name }}</td>
                    <td class="text-center">{{ $listed->Problem_Type }}</td>
                    <td class="text-center">{{ $listed->endorsement_by }}</td>
                    <td class="text-center">{{ $listed->created_by }}</td>
                    <td class="text-center">{{ $listed->created_date }}</td>
                    <td class="text-center">{{ $listed->changed_by }}</td>
                    <td class="text-center">{{ $listed->changed_date }}</td>
                    <td class="text-center">@if(($listed->patchstatus) == 'Rejected'){{$listed->patchstatus}} @else @if(($listed->CRstatus) == null){{$listed->patchstatus}} @elseif(($listed->CRstatus) != null && ($listed->scriptstatus) == null) {{$listed->CRstatus}} @else{{$listed->scriptstatus}} @endif @endif</td>
                    <td>@if($listed->total_valid_script > 0 || $listed->total_valid_cr > 0)
                        <a href="/prod-support/history-data-patching/download/{{ $listed->aid }}"data-toggle="tooltip" title="Download" style="alignment-adjust: middle" class="btn btn-xs btn-info"><i class="fa fa-download"></i></a>@endif
                        <a href="/prod-support/history-data-patching/view/{{ $listed->aid }}"data-toggle="tooltip" title="View" style="alignment-adjust: middle" class="btn btn-xs btn-success viewbutton"><i class="fa fa-file-text"></i></a>
                        @if(($listed->patchstatus) == 'Rejected' || $listed->status == 'Cancelled')<a function="byCRM" crmid = "{{ $listed->aid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatch"><i class="fa fa-repeat"></i></a>@endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
    @endforeach

    @if($listdatasix != null) 
    @foreach($listdatasix as $listSix)
    @if($listSix[0]->status !== 'Open')
    <div class="table-responsive">
        <h5><strong> {{$listSix[0]->datetime_porting}} : {{$listSix[0]->name_port}}</strong></h5><h5><strong>Status : @if($listSix[0]->status == 'Closed') <span style="color: red;"> {{$listSix[0]->status}} </span> @elseif ($listSix[0]->status  == 'Cancelled') {{$listSix[0]->status}} <a function="byPort" crmid = "{{ $listSix[0]->datafixid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatchAll"><i class="fa fa-repeat"></i></a>@endif&nbsp;&nbsp;&nbsp;&nbsp; @if($listSix[0]->status == 'Closed') Closed By : {{$listSix[0]->closed_by}}@endif</strong></h5>
        <table id = ""  class="table table-vcenter table-condensed table-bordered historysix-datatable">
            <thead>
                <tr>
                    <th class="text-center">CRM No</th>
                    <th class="text-center">Redmine No</th>
                    <th class="text-center">Module</th>
                    <th class="text-center">Problem Description</th>
                    <th class="text-center">Requester Type</th>
                    <th class="text-center">Requester Name</th>
                    <th class="text-center">Problem Type</th>
                    <th class="text-center">Endorcement By</th>
                    <th class="text-center">Created By</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Change By</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($listSix as $list)
                <tr>
                    <td class="text-center">{{ $list->crm_no }}</td>
                    <td class="text-center">{{ $list->redmineno }}</td>
                    <td class="text-center">{{ $list->module }}</td>
                    <td class="text-center">{{ $list->Problem_Description }}</td>
                    <td class="text-center">{{ $list->Requester_Type }}</td>
                    <td class="text-center">{{ $list->requester_name }}</td>
                    <td class="text-center">{{ $list->Problem_Type }}</td>
                    <td class="text-center">{{ $list->endorsement_by }}</td>
                    <td class="text-center">{{ $list->created_by }}</td>
                    <td class="text-center">{{ $list->created_date }}</td>
                    <td class="text-center">{{ $list->changed_by }}</td>
                    <td class="text-center">{{ $list->changed_date }}</td>
                    <td class="text-center">@if(($list->patchstatus) == 'Rejected'){{$list->patchstatus}} @else @if(($list->CRstatus) == null){{$list->patchstatus}} @elseif(($list->CRstatus) != null && ($list->scriptstatus) == null) {{$list->CRstatus}} @else{{$list->scriptstatus}} @endif @endif</td>
                    <td>@if($list->total_valid_script > 0 || $list->total_valid_cr > 0)
                        <a href="/prod-support/history-data-patching/download/{{ $list->aid }}"data-toggle="tooltip" title="Download" style="alignment-adjust: middle" class="btn btn-xs btn-info"><i class="fa fa-download"></i></a>@endif
                        <a href="/prod-support/history-data-patching/view/{{ $list->aid }}"data-toggle="tooltip" title="View" style="alignment-adjust: middle" class="btn btn-xs btn-success viewbutton"><i class="fa fa-file-text"></i></a>
                        @if(($list->patchstatus) == 'Rejected' || $list->status == 'Cancelled')<a function="byCRM" crmid = "{{ $list->aid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatch"><i class="fa fa-repeat"></i></a>@endif
                    </td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
    @endforeach
    @endif
    @if(Auth::user()->isPatcherRolesEp() && $listdataurgent != null) 
    @foreach($listdataurgent as $listUrgent)
    @if($listUrgent[0]->status !== 'Open')
    <div class="table-responsive">
        <h5><strong> {{$listUrgent[0]->name}}</strong></h5><h5><strong>Status : @if($listUrgent[0]->status == 'Closed') <span style="color: red;"> {{$listUrgent[0]->status}} </span> @elseif ($listUrgent[0]->status  == 'Cancelled') {{$listUrgent[0]->status}} <a function="byPort" crmid = "{{ $listUrgent[0]->datafixid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatchAll"><i class="fa fa-repeat"></i></a>@endif&nbsp;&nbsp;&nbsp;&nbsp; @if($listUrgent[0]->status == 'Closed') Closed By : {{$listUrgent[0]->closed_by}}@endif</strong></h5>
        <table id = ""  class="table table-vcenter table-condensed table-bordered historyurgent-datatable">
            <thead>
                <tr>
                    <th class="text-center">CRM No</th>
                    <th class="text-center">Redmine No</th>
                    <th class="text-center">Module</th>
                    <th class="text-center">Problem Description</th>
                    <th class="text-center">Requester Type</th>
                    <th class="text-center">Requester Name</th>
                    <th class="text-center">Problem Type</th>
                    <th class="text-center">Endorcement By</th>
                    <th class="text-center">Created By</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Change By</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Status</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @foreach($listUrgent as $list)
                <tr>
                    <td class="text-center">{{ $list->crm_no }}</td>
                    <td class="text-center">{{ $list->redmineno }}</td>
                    <td class="text-center">{{ $list->module }}</td>
                    <td class="text-center">{{ $list->Problem_Description }}</td>
                    <td class="text-center">{{ $list->Requester_Type }}</td>
                    <td class="text-center">{{ $list->requester_name }}</td>
                    <td class="text-center">{{ $list->Problem_Type }}</td>
                    <td class="text-center">{{ $list->endorsement_by }}</td>
                    <td class="text-center">{{ $list->created_by }}</td>
                    <td class="text-center">{{ $list->created_date }}</td>
                    <td class="text-center">{{ $list->changed_by }}</td>
                    <td class="text-center">{{ $list->changed_date }}</td>
                    <td class="text-center">@if(($list->patchstatus) == 'Rejected'){{$list->patchstatus}} @else @if(($list->CRstatus) == null){{$list->patchstatus}} @elseif(($list->CRstatus) != null && ($list->scriptstatus) == null) {{$list->CRstatus}} @else{{$list->scriptstatus}} @endif @endif</td>
                    <td>@if($list->total_valid_script > 0 || $list->total_valid_cr > 0)
                        <a href="/prod-support/history-data-patching/download/{{ $list->aid }}"data-toggle="tooltip" title="Download" style="alignment-adjust: middle" class="btn btn-xs btn-info"><i class="fa fa-download"></i></a>@endif
                        <a href="/prod-support/history-data-patching/view/{{ $list->aid }}"data-toggle="tooltip" title="View" style="alignment-adjust: middle" class="btn btn-xs btn-success viewbutton"><i class="fa fa-file-text"></i></a>
                        @if(($list->patchstatus) == 'Rejected' || $list->status == 'Cancelled')<a function="byCRM" crmid = "{{ $list->aid }}" data-toggle="tooltip" title="Repatch" style="alignment-adjust: middle" class="btn btn-xs btn-danger repatch"><i class="fa fa-repeat"></i></a>@endif
                    </td>    
                </tr>
                @endforeach
            </tbody>
        </table> 
    </div>
    @endif
    @endforeach
    @endif
</div>
@endif

<input type="hidden" id="id" name="id" value="" class="form-control" style="width: 100px;">
<input type="hidden" id="func" name="func" value="" class="form-control" style="width: 100px;">
<div id="modal_confirm_repatch_by_crm" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <label>Please choose Porting Type</label> &nbsp;&nbsp;&nbsp;
                <select id="status_porting" name="status_porting" value="" required class="form-control">
                    <option value="">Please Select</option>
                    <option value="S">Schedule Porting</option>
                    <option value="U">Urgent Porting</option>
                </select>
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="submit" id="submit_confirm_repatch_by_crm" name="submit_confirm_repatch_by_crm" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_repatch_by_crm" name="cancel_submit_repatch_by_crm"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>

@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
        ModalListActionLogDatatable.init();
    });</script>

<script>$(function () {
        TablesDatatables.init();
    });
    var APP_URL = {!! json_encode(url('/')) !!}

    $(".repatchAll").on("click", function () {
        $("#modal_confirm_repatch_by_crm").modal('show');
        let
        id = $(this).attr('crmid');
        let
        f = $(this).attr('function');
        $('#id').val(id);
        $('#func').val(f);
    });

    $(".repatch").on("click", function () {
        $("#modal_confirm_repatch_by_crm").modal('show');
        let
        id = $(this).attr('crmid');
        let
        f = $(this).attr('function');
        $('#id').val(id);
        $('#func').val(f);
    });
    $('#submit_confirm_repatch_by_crm').on('click', function () {
        $('#modal_confirm_repatch_by_crm').modal('hide');
        var status_porting = $("#status_porting").val();
        $Id = $("#id").val();
        $fun = $('#func').val();
        
        $.ajax({
            type: "GET",
            url: "/prod-support/repatch/crmid/" + $fun + "/" + $Id + "/" + status_porting,
        }).done(function (resp) {
            if(status_porting === 'S'){
                window.open(APP_URL + '/prod-support/data-patching', '_blank');
            }
            if(status_porting === 'U'){
                window.open(APP_URL + '/prod-support/urgent-patching', '_blank');
            }
        });

    });

    App.datatables();
    var objSelect = document.getElementById("Mobility");
    $('.history-datatable').DataTable({
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
    
    $('.historysix-datatable').DataTable({
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
    
    $('.historyurgent-datatable').DataTable({
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
//Set selected
    setSelectedValue(objSelect, "10");

    function setSelectedValue(selectObj, valueToSet) {
        for (var i = 0; i < selectObj.options.length; i++) {
            if (selectObj.options[i].text == valueToSet) {
                selectObj.options[i].selected = true;
                return;
            }
        }
    }
</script>  
@endsection

