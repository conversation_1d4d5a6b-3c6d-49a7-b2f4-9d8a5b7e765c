@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Application Status Not Sync</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="application_not_sync-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Appl No</th>
                    <th class="text-center">Tracking Diary Id</th>
                    <th class="text-center">Tracking Status Id</th>
                    <th class="text-center">Tracking Status</th>
                    <th class="text-center">Workflow Id</th>
                    <th class="text-center">Workflow Status Id</th>
                    <th class="text-center">Workflow Status</th>
                    <th class="text-center">Appl Id</th>
                    <th class="text-center">Appl Status Id</th>
                    <th class="text-center">Appl Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Is Active Appl</th>
                    <th class="text-center">Instance Id</th>
                    <th class="text-center">Activity</th>
                </tr>
            </thead>
            <thead class="filters">
                <tr>
                    <td class="text-center">Appl No</td>
                    <td class="text-center">Tracking Diary Id</td>
                    <td class="text-center">Tracking Status Id</td>
                    <td class="text-center">Tracking Status</td>
                    <td class="text-center">Workflow Id</td>
                    <td class="text-center">Workflow Status Id</td>
                    <td class="text-center">Workflow Status</td>
                    <td class="text-center">Appl Id</td>
                    <td class="text-center">Appl Status Id</td>
                    <td class="text-center">Appl Status</td>
                    <td class="text-center">Record Status</td>
                    <td class="text-center">Is Active Appl</td>
                    <td class="text-center"></td>
                    <td class="text-center"></td>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center">{{ $user->appl_no }}</td>
                    <td class="text-center">{{ $user->tracking_diary_id }}</td>
                    <td class="text-center">{{ $user->tracking_status_id }}</td>
                    <td class="text-center">{{ $user->tracking_status }}</td>
                    <td class="text-center">{{ $user->workflow_id }}</td>
                    <td class="text-center">{{ $user->workflow_status_id }}</td>
                    <td class="text-center">{{ $user->workflow_status }}</td>
                    <td class="text-center">{{ $user->appl_id }}</td>
                    <td class="text-center">{{ $user->appl_status_id }}</td>
                    <td class="text-center">{{ $user->appl_status }}</td>
                    <td class="text-center">{{ $user->record_status }}</td>
                    <td class="text-center">{{ $user->is_active_appl }}</td>
                    <th class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$user->instance_bpm}}" target="_blank" > {{ $user->instance_bpm }}</a></th>
                    <td class="text-center">{{ $user->activity_name }}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>
App.datatables();
$(document).ready(function () {
    $('#application_not_sync-datatable').DataTable({
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]

    });
});

$(document).ready(function () {
    // Setup - add a text input to each footer cell
    $('#application_not_sync-datatable .filters td').each(function () {
        var title = $('#application_not_sync-datatable thead th').eq($(this).index()).text();
        $(this).html('<input type="text" placeholder="Search ' + title + '" />');
    });

    // DataTable
    var table = $('#application_not_sync-datatable').DataTable();

    // Apply the search
    table.columns().eq(0).each(function (colIdx) {
        $('input', $('.filters td')[colIdx]).on('keyup change', function () {
            table
                    .column(colIdx)
                    .search(this.value)
                    .draw();
        });
    });
});
</script>
@endsection



