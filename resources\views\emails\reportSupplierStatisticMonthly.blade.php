<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title>Statistik syarikat/perunding yg berdaftar di semenanjung</title>
        <meta name="viewport" content="width=device-width" />
        <style type="text/css">
            /* Reset styles */
        body {
            margin: 0;
            padding: 0;
            min-width: 100%;
            width: 100% !important;
            height: 100% !important;
        }

        body, table, td, div, p, a {
            -webkit-font-smoothing: antialiased;
            text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            -webkit-text-size-adjust: 100%;
            line-height: 100%;
        }

        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
            border-collapse: collapse !important;
            border-spacing: 0;
        }

        img {
            border: 0;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }

        #outlook a {
            padding: 0;
        }

        .ReadMsgBody {
            width: 100%;
        }

        .ExternalClass {
            width: 100%;
        }

        .ExternalClass, .ExternalClass p, .ExternalClass span, .ExternalClass font, .ExternalClass td, .ExternalClass div {
            line-height: 100%;
        }

        /* Rounded corners for advanced mail clients only */
        @media all and (min-width: 560px) {
            .container {
                border-radius: 8px;
                -webkit-border-radius: 8px;
                -moz-border-radius: 8px;
                -khtml-border-radius: 8px;
            }
        }

        /* Set color for auto links (addresses, dates, etc.) */
        a, a:hover {
            color: #127DB3;
        }

        .footer a, .footer a:hover {
            color: #999999;
        }
        </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: Tahoma,sans-serif; background-color:#fff" yahoo="fix">
        <label class="col-md-1 text-left">Tuan / Puan,</label>
        <br /><br />
        
        <label class="col-md-1 text-left">Statistik syarikat/perunding yg berdaftar di semenanjung sehingga {{$data->get('dateasof')}}</label>
        <br /><br />
       
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 400px;" class="content">
            <thead style="background-color:#D9D9D9;">
                <tr>
                    <th align="center" style="width:10%; padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Bil.
                    </th>
                    <th align="center" style="width:70%;padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Perkara
                    </th>
                    <th align="center" style="width:20%;padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                       Jumlah
                    </th>
                </tr>
            </thead>
            @if(count($data->get('statData')) > 0)
            @foreach($data->get('statData') as $row)
            <tr>
                <td align="center"  style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$row->bil}} 
                </td>
                <td align="center" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$row->perkara}} 
                </td>
                <td align="center" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$row->jumlah}} 
            </tr>
            @endforeach
            @else
            <tr>
                <td colspan=9>Tiada Rekod</td>
            @endif
        </table>
        <br /><br />
        <label class="col-md-1 text-left">Terima Kasih</label>
        <br />
    </body>


</html>
