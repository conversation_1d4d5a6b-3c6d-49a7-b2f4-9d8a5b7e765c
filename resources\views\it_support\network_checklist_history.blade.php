@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            @if ($section === 'network_kvdc')
                <li>
                    <a href="{{ url('/it_support/network/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/network/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/network/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            @endif
            @if ($section === 'network_per')
                <li>
                    <a href="{{ url('/it_support/network/performance/checklist') }}"><i class="fa fa-tasks"></i>Check
                        List</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/network/performance/checklist/history') }}"><i
                            class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/network/performance/data_lookup') }}"><i class="fa fa-cubes"></i>Data
                        Lookup</a>
                </li>
            @endif
            @if ($section === 'network_am')
                <li>
                    <a href="{{ url('/it_support/network/amtek/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/network/amtek/checklist/history') }}"><i
                            class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/network/amtek/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            @endif
        </ul>
    </div>
    <div class="block">
        <form id="network_checklist_history"
            @if ($section == 'network_kvdc') action="{{ url('/it_support/network/checklist/history/{date}') }}" @endif
            @if ($section == 'network_am') action="{{ url('/it_support/network/amtek/checklist/history/{date}') }}" @endif
            @if ($section == 'network_per') action="{{ url('/it_support/network/performance/checklist/history/{date}') }}" @endif
            method="post">
            {{ csrf_field() }}
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            @if ($message1 !== null)
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ $message1 }}
                </div>
            @endif

            <div class="col-md-2 date hide">
                <input id="date_search" name="date_search" type="date" value="{{ isset($dateNote) ? $dateNote : '' }}"
                    class="form-control">
            </div>

            @if (in_array($section, ['network_kvdc', 'network_am', 'network_per']))
                <div id="customDiv">
                    <label class="col-md-2 text-center">Date : @if (isset($dateNote)) {{$dateNote}} @endif</label>
                    <div class="col-md-11">
                        @if ($section == 'network_kvdc')
                            <input type="radio" name="radio_reqcategory" value="K" style="width: 100px; " checked
                                onchange="handleRadioChange(this)">KVDC
                        @elseif ($section == 'network_am')
                            <input type="radio" name="radio_reqcategory" value="W" style="width: 100px;" checked
                                onchange="handleRadioChange(this)">WISMA CDC
                        @elseif ($section == 'network_per')
                            <input type="radio" name="radio_reqcategory" value="S" style="width: 100px;" checked
                                onchange="handleRadioChange(this)">CDC CYBERJAYA
                        @endif
                    </div>
                    <label class="col-md-2 text-center"></label>
                </div>

                <div class="table-options clearfix" id="network_status_list">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($groupName))
                            @foreach ($groupName as $key => $list)
                                <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                    title="{{ $section == 'network_kvdc' ? $list->net_data_group : ($section == 'network_am' ? $list->am_data_group : $list->netper_data_group) }}"
                                    group="{{ $section == 'network_kvdc' ? $list->net_data_group : ($section == 'network_am' ? $list->am_data_group : $list->netper_data_group) }}">
                                    <input type="radio" name="menu"
                                        value="{{ $section == 'network_kvdc' ? $list->net_data_group : ($section == 'network_am' ? $list->am_data_group : $list->netper_data_group) }}">
                                    {{ $section == 'network_kvdc' ? $list->net_data_group : ($section == 'network_am' ? $list->am_data_group : $list->netper_data_group) }}
                                </label>
                            @endforeach
                        @endif
                    </div>
                </div>
            @endif

            <div class="text-center spinner-loading" style="padding: 20px;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>

            <div class="table-responsive">
                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
            </div>
            @if (isset($listNote) == false ||
                    $listNote[0] == null ||
                    (isset($listNote) &&
                        $listNote[0] &&
                        ($listNote[0]->ack_status != 'Pending Endorsement' && $listNote[0]->ack_status != 'Completed')))
                <div class="text-center">
                    <button type="submit" style="background-color: #414770; color: white"
                        class="btn btn btn-primary editSave">Update</button>
                </div>
            @endif
        </form>
    </div>

    @if (in_array($section, ['network_kvdc', 'network_am', 'network_per']))
        @include('it_support.page_status', [
            'page' =>
                $section == 'network_kvdc'
                    ? 'network'
                    : ($section == 'network_per'
                        ? 'network_per'
                        : 'network_am'),
            'location' => old(
                'radio_reqcategory',
                $section == 'network_kvdc' ? 'K' : ($section == 'network_per' ? 'S' : 'W')),
        ])
    @endif
@endsection
@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        var customDiv = document.getElementById('customDiv');
        customDiv.style.width = '93vw'; // Set the width
        customDiv.style.height = '50px'; // Set the height
        customDiv.style.backgroundColor = '#414770'; // Set the background color
        customDiv.style.color = 'white';
        customDiv.style.textAlign = 'center';
        customDiv.style.fontSize = '16px';
        customDiv.style.display = 'flex';
        customDiv.style.alignItems = 'center';
    </script>
    <script>
        function selectAll(ele) {
            console.log('sinituuuuu')
            var checkboxes = document.querySelectorAll('#radio_for_status_okay');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        var dateNote = "{{ $dateNote }}";
        $('#selected_date').val(dateNote);

        function buttonSearch() {
            $('#network_list_panel').show();
            $("#menu_button").trigger("change");
            $('#menu_button').click();
            $('#menu_button').addClass("active");

            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
                $('#menu_button').click();
                $('#menu_button').addClass("active");
            }
            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();

            var today = new Date();
            var formattedDate = today.getFullYear() + '-' + ('0' + (today.getMonth() +
                1)).slice(-2) + '-' + ('0' + today.getDate()).slice(-2);
            $('#check_date').val(formattedDate);

            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("");
            }
        }

        $(document).ready(function() {
            $('#menu_button').click();
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("");
            }
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("{{ $listNote[0]->ack_remarks ?? '' }}");
            }
            var ackStatus = "{{ $listNote[0]->ack_status ?? '' }}";
            if (ackStatus === 'Pending Endorsement' || ackStatus === 'Completed') {
                $('.editSave').hide();
            } else {
                $('.editSave').show();
            }

        });

        var Tabledatatable;

        function handleRadioChange(radio) {
            var locationInput = document.getElementById('location_type');
            locationInput.value = radio.value;
            resetDataTable();
            $("#menu_button").trigger("change");

        }

        $(".btn-group").on("change", "#menu_button", function(e) {
            changeMenu(this)
        });

        function resetDataTable() {
            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                Tabledatatable.destroy();
            }
            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();
        }

        function changeMenu(a) {
            var dateSelection = $('#date_search').val();
            let group = $(a).attr('group');

            var radioButtons = document.getElementsByName("radio_reqcategory");
            var selectedLocation;
            for (var i = 0; i < radioButtons.length; i++) {
                if (radioButtons[i].checked) {
                    selectedLocation = radioButtons[i].value;
                    break;
                }
            }

            resetDataTable();

            if(selectedLocation === 'K'){
                var sectionModule = 'network';
            }  
            else if(selectedLocation === 'S'){
                var sectionModule = 'network_per';
            }else if(selectedLocation === 'W'){
                var sectionModule = 'network_am';
            }

            $('.spinner-loading').show();

            $.ajax({
                type: "GET",
                url: "/it_support/network/status/find_by/" + selectedLocation + "/" + group + "/" + dateSelection,
            }).done(function(data) {
                $('.spinner-loading').hide();
                $('#list_datatable').html(data).fadeIn();
                Tabledatatable = $('#list_datatable').DataTable({
                    ordering: false,
                    lengthMenu: [
                        [10, 20, 30, -1],
                        [10, 20, 30, 'All']
                    ]
                });
            });

            $.ajax({
                
                url: "/it_support/network/checklist/notes/history/" + dateSelection + '/' + sectionModule,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    console.log(data);
                    var today = new Date();
                    var formattedDate = today.getFullYear() + '-' + ('0' + (today.getMonth() +
                        1)).slice(-2) + '-' + ('0' + today.getDate()).slice(-2);
                    if ($.isEmptyObject(data)) {
                        $('.notes_form').show();
                        $('.editSave').show();
                    }
                    if (data.listdata && data.listdata.length > 0) {
                        data.listdata.forEach(function(item) {
                            var checkRemarks = data.listdata[0].ack_remarks;
                            var checkBy = data.listdata[0].ack_check_by;
                            var checkDate = data.listdata[0].ack_check_date;
                            var ackDate = data.listdata[0].ack_endorsement_date;
                            var ackBy = data.listdata[0].ack_endorsement_by;

                            $('#ack_status').text(item.ack_status);
                            if (item.ack_status === 'Pending Endorsement') {
                                $('.button_update').hide();
                                $('.save_button').hide();
                                $('.endorse_button').show();
                                $('#remarks').val(checkRemarks);
                                $('#check_by').val(checkBy);
                                $('#check_date').val(checkDate);
                                $('#ack_date').val(formattedDate);
                                $('#ack_by').val(data.user);
                            }
                            if ($('#ack_status').text() === 'Completed') {
                                var remarksInput = document.getElementById('remarks');
                                remarksInput.readOnly = true;
                                $('.button_update').hide();
                                $('.save_button').hide();
                                $('.endorse_button').hide();
                                $('#remarks').prop('readonly', true);
                                $('#remarks').val(checkRemarks);
                                $('#check_by').val(checkBy);
                                $('#check_date').val(checkDate);
                                $('#ack_date').val(ackDate);
                                $('#ack_by').val(ackBy);
                            }
                        });

                    } else {
                        $('.button_update').show();
                        $('.save_button').show();
                        $('.endorse_button').hide();
                        $('#remarks').val("");
                        $('#check_by').val(data.user);
                        $('#remarks').prop('readonly', false);
                        $('#check_date').val(formattedDate);
                        $('#ack_by').val("");
                        $('#ack_date').val("");
                        $('#ack_status').text("")
                    }
                }
            });
        }
    </script>
@endsection
