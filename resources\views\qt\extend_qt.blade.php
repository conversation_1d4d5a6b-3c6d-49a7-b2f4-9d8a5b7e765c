@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
    <div id="response" class="table-options clearfix display-none">
        <div id="response-msg" class="alert" colspan="6">TEST</div>
    </div>

    <div class="block">
        <div class="block-title">
            <h2><strong>Extend</strong> QT</h2>
            <div class="block-options pull-right action-today">
            </div>
        </div>

        <div class="block-alt-noborder">
            <form id="form-search-mminf" action="{{url("/trigger/gfmas/mminf/search")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method"  type="hidden" value="POST">
                <input id="searchType" name="searchType" type="hidden" value="search_docno">
                <div class="col-md-12">
                    <div class=" col-md-6 form-group">
                        <label class="col-md-2 control-label" for="doc_no">QT Date<span class="text-danger">*</span></label>
                        <div class="col-md-10">
                            <input type="text" id="qt-from-date" name="qt-from-date" class="form-control input-datepicker" value="{{ date("d-m-Y") }}" data-date-format="dd-mm-yyyy" placeholder="dd-mm-yyyy">
                            <span class="help-block"></span>
                        </div>

                    </div>
                    <div class="col-md-6 form-group">
                        <label class="col-md-2 control-label" for="doc_no">Extend To<span class="text-danger">*</span></label>
                        <div class="col-md-10">
                            <input type="text" id="qt-to-date" name=qt-to-date" class="form-control input-datepicker" value="{{ date('d-m-Y', strtotime(date("d-m-Y") . ' +1 day')) }}" data-date-format="dd-mm-yyyy" placeholder="dd-mm-yyyy">
                            <span class="help-block"></span>
                        </div>
                    </div>
                </div>

                <div class="form-group form-actions">
                    <div class="col-md-2 col-md-offset-10">
                        <button type="submit" class="btn btn-sm btn-primary"><i class="fa fa-repeat"></i> Reset</button>
                        {{--<button type="reset" class="btn btn-sm btn-warning"><i class="fa fa-warning"></i><strong> Extend</strong></button>--}}
                        <a class="btn btn-sm btn-warning action-modal-extend" title="" data-original-title="Extend"
                           data-url="{{url("/trigger/gfmas/mminf/update")}}"
                           href="#modal_confirm_extend" data-toggle="modal" data-id=""><i class="fa fa-warning"></i> Extend</a>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-4 col-lg-4">
            <a href="javascript:void(0)" class="widget widget-hover-effect2 text-center">
                <div class="widget-extra themed-background">
                    <h5 class="widget-content-light"><strong>QT Date</strong> <span id="show-date-from"></span></h5>
                </div>
                <div class="widget-extra-full"><span class="h2 animation-expandOpen qt-from-count">0</span></div>
            </a>
        </div>
        <div class="col-sm-4 col-lg-4">
            <a href="javascript:void(0)" class="widget widget-hover-effect2 text-center">
                <div class="widget-extra themed-background">
                    <h5 class="widget-content-light"><strong>Extend To</strong> <span id="show-date-to"></span></h5>
                </div>
                <div class="widget-extra-full"><span class="h2 animation-expandOpen qt-to-count">0</span></div>
            </a>
        </div>
        <div class="col-sm-4 col-lg-4">
            <a href="javascript:void(0)" class="widget widget-hover-effect2 text-center">
                <div class="widget-extra themed-background-danger">
                    <h5 class="widget-content-light"><strong>Total QT Closed on Extended Date</strong> <span id="show-date-total"></span></h5>
                </div>
                <div class="widget-extra-full"><span class="h2 animation-expandOpen qt-total-count">0</span></div>
            </a>
        </div>
    </div>

    <div id="modal_confirm_extend" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title" id="modal-confirm-title"> Are you sure to extend this QT?</h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                {{ csrf_field() }}
                                <input type="hidden" id="extend_from_date" value="" />
                                <input type="hidden" id="extend_to_date" value="" />
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Extend From Date</label>
                                    <div class="col-md-9">
                                        <label class="col-md-3 form-control-static text-danger" id="modal-extend-from">-</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Extend To Date</label>
                                    <div class="col-md-9">
                                        <label class="col-md-3 form-control-static text-danger" id="modal-extend-to">-</label>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-md-3 control-label">Reason/Remarks<span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <textarea class="col-md-3 form-control text-danger" id="modal-remarks" required></textarea>
                                        <span id="remark-alert" class="text-danger display-none">Please fill in remark. (at least 5 characters)</span>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_extend"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="wait-modal" class="modal fade loading-modal" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-hidden="true" role="dialog">
        <div class="modal-dialog modal-sm text-center">
            <div class="modal-content">
                <span class="fa fa-spinner fa-spin fa-3x text-light"></span>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->

    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();
        /* Initialize Datatables */
        var tableListData =     $('#basic-datatable').DataTable({
            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });

        let date = new Date();
        date.setDate(date.getDate()-1);
        /* ONLOAD */
        $('#qt-from-date input').datepicker({
            autoclose: true,
            startDate: new Date(),
            endDate: new Date(new Date().setDate(new Date().getDate() + 5))
        });


        $(document).ready(function () {

            let qtFromDate = $('#qt-from-date').val();
            let qtToDate = $('#qt-to-date').val();

            countDashboard();

            /* END ONLOAD */

            $('#qt-from-date').datepicker().on('changeDate', function (ev) {
                let qtDate = this.value;

                $.ajax({
                    url: APP_URL + '/qt/extend/showQTCountByDate/' + qtDate,
                    type: "GET",
                    success: function (data) {
                        let $data = $(data);
                        $('.qt-from-count').text($data[0].total).fadeIn();
                        $('#show-date-from').text('['+qtDate+']').fadeIn();
                        calcTotalQtCount();
                    }
                });
            });

            $('#qt-to-date').datepicker().on('changeDate', function (ev) {
                let qtDate = this.value;

                $.ajax({
                    url: APP_URL + '/qt/extend/showQTCountByDate/' + qtDate,
                    type: "GET",
                    success: function (data) {
                        let $data = $(data);
                        $('.qt-to-count').text($data[0].total).fadeIn();
                        $('#show-date-to').text('['+qtDate+']').fadeIn();
                        calcTotalQtCount();
                    }
                });
            });

            function countDashboard() {
                $.ajax({
                    url: APP_URL + '/qt/extend/showQTCountByDate/' + qtFromDate,
                    type: "GET",
                    success: function (data) {
                        let $data = $(data);
                        $('.qt-from-count').text($data[0].total).fadeIn();
                        $('#show-date-from').text('[' + qtFromDate + ']').fadeIn();
                    }
                });

                $.ajax({
                    url: APP_URL + '/qt/extend/showQTCountByDate/' + qtToDate,
                    type: "GET",
                    success: function (data) {
                        let $data = $(data);
                        $('.qt-to-count').text($data[0].total).fadeIn();
                        $('#show-date-to').text('[' + qtToDate + ']').fadeIn();
                        calcTotalQtCount();
                    }
                });
            }

            function calcTotalQtCount() {
                let qtFrom = parseInt($('.qt-from-count').text());
                let qtTo = parseInt($('.qt-to-count').text());
                let qtTotal = qtFrom + qtTo;

                $('.qt-total-count').text(qtTotal).fadeIn();
                $('#show-date-total').text($('#show-date-to').text()).fadeIn();
            }

            function triggerQtCountAjax(qtDate) {
                $.ajax({
                    url: APP_URL + '/qt/extend/showQTCountByDate/' + qtDate,
                    type: "GET",
                    success: function (data) {
                        let $data = $(data);
                        // console.log($data[0].total);
                        $('.qt-from-count').text($data[0].total).fadeIn();
                        $('#show-date-from').text('['+qtDate+']').fadeIn();
                        calcTotalQtCount();
                    }
                });
            }

            // CLICK EXTEND BUTTON > MODAL POPUP
            $('div.form-actions').on("click",'a.action-modal-extend', function(){
                // console.log('click extend');
                let qtFrom = parseInt($('.qt-from-count').text());

                if(qtFrom > 0) {
                    $("#modal-confirm-title").text(' Are you sure to extend this QT?');
                    let qtFromDate = $('#qt-from-date').val();
                    let qtToDate = $('#qt-to-date').val();
                    $("#extend_from_date").val(qtFromDate);
                    $("#extend_to_date").val(qtToDate);
                    $("#modal-extend-from").text(qtFromDate);
                    $("#modal-extend-to").text(qtToDate);
                    $(".action_confirm_extend").attr("disabled", false);
                } else {
                    $("#modal-confirm-title").text(' Please make sure QT on selected date is not closed yet.');
                    $("#extend_from_date").val(qtFromDate);
                    $("#extend_to_date").val(qtToDate);
                    $("#modal-extend-from").text(qtFromDate);
                    $("#modal-extend-to").text(qtToDate);
                    $(".action_confirm_extend").attr("disabled", true);
                }


            });

            $('div.form-actions').on("click",'button.action_confirm_extend', function(){

                let extendFromDate =$("#extend_from_date").val();
                let extendToDate =$("#extend_to_date").val();
                let extendRemark =$("#modal-remarks").val();
                let csrf = $("input[name=_token]").val();

                if(extendRemark.length >= 5) {
                    $('#modal_confirm_extend').modal('hide');
                    $('#wait-modal').modal('toggle');

                    $.ajax({
                        url: APP_URL + '/qt/extend/confirm',
                        method : "POST",
                        dataType : "json",
                        data : {"_token":csrf,"extend_from_date":extendFromDate,"extend_to_date":extendToDate,"extend_remarks":extendRemark},
                        context: document.body,
                        success: function (resp) {
                            if(resp.status === 'success'){
                                $("#response").show();
                                $("#response").addClass("alert alert-success");
                                $("#response-msg").html("Successfully Updated!");

                                if(resp.hasOwnProperty('updated_row')){
                                    $("#response-msg").html("Successfully Update <strong>"+resp.updated_row+" QT Closing Date.</strong>");
                                }
                                $('#wait-modal').modal('hide');
                                countDashboard();
                            } else {
                                $("#response").show();
                                $("#response").addClass("alert alert-danger");
                                $("#response-msg").html("Update Failed!");

                                if (resp.hasOwnProperty('err_msg')) {
                                    $("#response-msg").html("<strong> Update Failed!</strong> Please try again. <br />ERR: " + resp.err_msg);
                                }

                                $('#wait-modal').modal('hide');
                                countDashboard();
                            }
                        },
                        error: function (xhr, ajaxOptions, thrownError) {
                            alert(xhr.status);
                            alert(xhr.responseText);
                        }
                    });
                } else {
                    $("#remark-alert").show();
                }

            });





        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection