<?php

namespace App\Migrate;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Log;

class MigrateUtils {

    public static function getTakenTime($dtStartTime) {
        return array(
            'TakenTime' => Carbon::now()->diffForHumans($dtStartTime),
            'TakenTimePerMinutes' => Carbon::now()->diffInMinutes($dtStartTime),
            'TakenTimePerSeconds' => Carbon::now()->diffInSeconds($dtStartTime)
        );
    }
    
    public static function getDateStartByPeriod($periodMinute){
       $startCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateStart = Carbon::now()->sub($startCarbonInterval);
       $cDateStart->subSecond($cDateStart->second); //Set second clear 00
       return $cDateStart;
    }
    
    public static function getDateEndByPeriod(Carbon $cDateStart , $periodMinute){
       $endCarbonInterval = CarbonInterval::create(0, 0, 0, 0, 0, $periodMinute, 0); 
       $cDateEnd = new Carbon($cDateStart->format('Y-m-d H.i.s'));
       $cDateEnd->add($endCarbonInterval);
       return $cDateEnd;
    }

    public static function logDump($content){
        dump($content);
        Log::info($content);
    }
    
    public static function logErrorDump($content){
        dump($content);
        Log::error($content);
    }
}
