@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

<div class="content-header">
    <div class="header-section">
        <h1><i class="gi gi-search"></i>REFIRE NEW INSTANCE AND SUBMIT TASKS (TEC OR FEC ONLY)<br></h1>
    </div>
</div>

<div class="block block-alt-noborder full">
    <div class="row" >
        <div class="col-sm-12">
            <div class="block">
                <form action="{{url('/bpm/sourcing/task/refireEvaluation')}}" method="post" class="form-horizontal form-bordered" >
                    {{ csrf_field() }}
                    <div class="row">
                        <div class="col-lg-5">
                            <div class="form-group" id="div_search_query" style="display:block;">
                                <label class="col-md-3 control-label">QT Number</label>
                                <div class="input-group">
                                    <input id="qt_no" name="qt_no" class="form-control" required value="{{old('qt_no')}}" placeholder="qt no" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-5">
                            <div class="form-group" style="display:block;">
                                <label class="col-md-3 control-label" for="process_name">Process Name</label>
                                <div class="col-md-9">
                                    <div class="input-group">
                                        <select id="process_name" name="process_name" class="form-control" required>
                                            <option value=@if($process_name == old('process_name') ) selected @endif>Please select</option>
                                            @foreach(App\Services\BPMAPIService::$BPM_PROCESS_EVALUATION as $key => $value)
                                            <option value="{{$key}}" @if($key == $process_name ) selected @endif>{{$value}}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>  
                        <div class="col-lg-2">
                            <div class="form-group form-actions form-actions-button text-right">
                                <button type="submit" class="btn btn-sm btn-info">Search</button>
                            </div>
                        </div> 
                    </div> 
                </form>
                <div class="row">
                @if($qtDetails !== null)
                @if(count($runningInstances) > 0)
                <?php $listInstance = implode(',',$runningInstances);?>
                <div class="col-sm-12">
                    <div class="block">
                        <p style="color: red"><strong> Instance {{$listInstance}}  Is Running. Please Terminate First Before Proceed!! </strong></p>
                    </div>
                </div>
                @else
                <div class="col-sm-12">
                <input id="doc_id" name="doc_id" value="{{$doc_id}}" type="hidden"> 
                <h5 id="comp_instance"></h5>
                    <div class="table-responsive">  
                        <table class="table table-vcenter table-striped table-bordered ">
                            <thead style="background-color:#f2f2f2;">
                                <tr>    
                                    <th class="text-center">PROCESS NAME</th>
                                    <th class="text-center">ACTIVITY NAME</th>
                                    <th class="text-center">STATUS</th>
                                </tr>
                            </thead>
                            <tbody id="table_evaluation"></tbody>
                        </table>  
                    </div>
                </div> 
                @endif
                @endif
                </div>  
            </div> 
        </div>
    </div>
</div>

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    TablesDatatables.init();
});</script>
<script>
var APP_URL = {!! json_encode(url('/')) !!}
    $(document).ready(function () {
        var proceedEvaluation = 'no';
        proceedEvaluation = "<?php
            if($qtDetails !== null && $status === 'Success' && count($runningInstances) == 0){
                echo 'yes';
            }?>";
        console.log('proceedEvaluation '+ proceedEvaluation); 
        var totalOcMember = 0;
        totalOcMember = "<?php
            if($totalOcMember !== null){
                echo $totalOcMember;
            }?>";
        console.log('totalOcMember '+ totalOcMember); 
        var totalTecMember = 0;
        totalTecMember = "<?php
            if($totalTecMember !== null){
                echo $totalTecMember;
            }?>";
        console.log('totalTecMember '+ totalTecMember); 
        var totalFecMember = 0;
        totalFecMember = "<?php
            if($totalFecMember !== null){
                echo $totalFecMember;
            }?>";
        console.log('totalFecMember '+ totalFecMember); 
        var docNo = $("input[name=qt_no]").val();
        var docId = $("input[name=doc_id]").val();
        var selectedProcess = $("#process_name option:selected").attr("value");

        var process = 'Evaluation'; 
        var groupTask = '';
        var totalGroup = ''; 
        var time = 1000;
        var increaseTime = 30000;
        var html = '';
        var htmlStatusBefore  = '';

        const evArray = ['prestart','start',''];
        var processArray = ['OC','TEC'];
        if(selectedProcess === 'tec_evaluation') {
            processArray = ['OC'];
        } 
        var activityArray = ['Coordinate','Finalize','Endorse','SignOff'];
        
        setTimeout(function () { 
            for (var e of evArray) {
                time += increaseTime;
                if(e === '') {
                    checkNewInstanceFunction(docNo,time);
                }else {
                    html += '<tr id="'+e+'">';
                    html += '<td id="'+e+'_process">'+process+'</td>';
                    html += '<td id="'+e+'_activity">'+e+'</td>'; 
                    html += '<td id="'+e+'_status" class="'+e+'_spinner widget"><i class="fa fa-spinner fa-2x fa-spin"></td>';
                    html += '</tr>';
                    document.getElementById("table_evaluation").innerHTML = html;
                    checkStatus(proceedEvaluation,process,e,docNo,e,htmlStatusBefore,docId,time);
                    htmlStatusBefore = 'prestart_status';
                } 
            }
            htmlStatusBefore = 'start_status';
            for (var p of processArray) {  
                for (var a of activityArray) {
                    var divHtml = p.toLowerCase() + '_' + a.toLowerCase();   
                    if(a === 'Endorse') { 
                        if(p === 'OC') {
                            for (let i = 1; i <= totalOcMember; i++) { 
                                time = time + increaseTime;
                                divEndorse = divHtml + '_' + i;
                                html += '<tr id="'+p+'_'+i+'">';
                                html += '<td id="'+divEndorse+'_process">'+p+'</td>';
                                html += '<td id="'+divEndorse+'_activity">'+a+'</td>'; 
                                html += '<td id="'+divEndorse+'_status" class="'+divEndorse+'_spinner widget"><i class="fa fa-spinner fa-2x fa-spin"></td>';
                                html += '</tr>';
                                document.getElementById("table_evaluation").innerHTML = html;
                                checkStatus(proceedEvaluation,p,a,docNo,divEndorse,htmlStatusBefore,docId,time,i);
                                htmlStatusBefore = divEndorse + '_status'; 
                                
                            }  
                        } else if(p === 'TEC') {
                            for (let i = 1; i <= totalTecMember; i++) { 
                                time = time + increaseTime;
                                divEndorse = divHtml + '_' + i;
                                html += '<tr id="'+p+'_'+i+'">';
                                html += '<td id="'+divEndorse+'_process">'+p+'</td>';
                                html += '<td id="'+divEndorse+'_activity">'+a+'</td>'; 
                                html += '<td id="'+divEndorse+'_status" class="'+divEndorse+'_spinner widget"><i class="fa fa-spinner fa-2x fa-spin"></td>';
                                html += '</tr>';
                                document.getElementById("table_evaluation").innerHTML = html;
                                checkStatus(proceedEvaluation,p,a,docNo,divEndorse,htmlStatusBefore,docId,time,i);
                                htmlStatusBefore = divEndorse + '_status'; 
                            }  
                        } else if(p === 'FEC') {
                            for (let i = 1; i <= totalFecMember; i++) { 
                                time = time + increaseTime;
                                divEndorse = divHtml + '_' + i;
                                html += '<tr id="'+p+'_'+i+'">';
                                html += '<td id="'+divEndorse+'_process">'+p+'</td>';
                                html += '<td id="'+divEndorse+'_activity">'+a+'</td>'; 
                                html += '<td id="'+divEndorse+'_status" class="'+divEndorse+'_spinner widget"><i class="fa fa-spinner fa-2x fa-spin"></td>';
                                html += '</tr>';
                                document.getElementById("table_evaluation").innerHTML = html;
                                checkStatus(proceedEvaluation,p,a,docNo,divEndorse,htmlStatusBefore,docId,time,i);
                                htmlStatusBefore = divEndorse + '_status'; 
                            }  
                        }                          
                    } else {
                        time = time + increaseTime;
                        html += '<tr id="'+p+'">';
                        html += '<td id="'+divHtml+'_process">'+p+'</td>';
                        html += '<td id="'+divHtml+'_activity">'+a+'</td>'; 
                        html += '<td id="'+divHtml+'_status" class="'+divHtml+'_spinner widget"><i class="fa fa-spinner fa-2x fa-spin"></td>';
                        html += '</tr>';
                        document.getElementById("table_evaluation").innerHTML = html;
                        checkStatus(proceedEvaluation,p,a,docNo,divHtml,htmlStatusBefore,docId,time);
                        htmlStatusBefore = divHtml + '_status';
                    }
                  }
            }
        }, time); 
    });   

    function checkStatus(proceedEvaluation,process,activity,docNo,htmldiv,status,docId,time,groupTask) {
        setTimeout(function () { 
            var statusEval = $('#'+status+'').text(); 
            console.log('status ' + status); 
            console.log('statusEval ' + statusEval); 
            if(activity === 'prestart') {
                callAjaxFunction(proceedEvaluation,process,activity,docNo,htmldiv,docId,groupTask);
            } else {
                if(statusEval === 'Success') { 
                    callAjaxFunction(proceedEvaluation,process,activity,docNo,htmldiv,docId,groupTask);
                }
            } 
        }, time);   
    }

    function callAjaxFunction(proceedEvaluation,process,activity,docNo,htmldiv,docId,groupTask) {
        $processName = process;
        $activityName = activity + ' Evaluation';
        if(process !== 'Evaluation') {
            $processName = process + 'Evaluation';
        }
        if(activity === 'Coordinate') {
            $activityName = activity + ' Evaluation Meeting';
        }else if(activity === 'prestart' || activity === 'start') {
            $activityName = activity;
        }
        if(groupTask !== '') {
            groupTask = 'T'+groupTask;
        }
        $('#'+htmldiv+'').show();
        $('#'+htmldiv+'_process').html($processName);
        $('#'+htmldiv+'_activity').html($activityName);
        
        if(proceedEvaluation == 'yes'){  
            $.ajax({
                url: APP_URL + '/bpm/sourcing/task/ajaxActionEvaluation',
                type: "GET",
                data: {"docNo": docNo, "process": process, "activity": activity, "docId": docId, "groupTask" : groupTask}, 
            }).done(function (resp) {
                console.log('resp ' + resp);
                if(resp === 'Success') {
                    $('#'+htmldiv+'_status').html(resp);
                }else {
                    $('#'+htmldiv+'_status').html('Failed');
                }
                
                $('#'+htmldiv+'_spinner').hide(); 
             }); 
             
        } else {
            console.log('error evaluation');
            $('#'+htmldiv+'_status').html('Response More Than 30 seconds');
            $('#'+htmldiv+'_spinner').show();  
        }
         
    }

    function checkNewInstanceFunction(docNo,time) {
        setTimeout(function () { 
            $.ajax({
                url: APP_URL + '/bpm/sourcing/task/ajaxCheckInstanceID',
                type: "GET",
                data: {"docNo": docNo}, 
            }).done(function (resp) {
                console.log('comp instance ' + resp);
                $('#comp_instance').show();  
                $('#comp_instance').html('<strong>INSTANCE ID: ' + resp +'</strong>'); 
             });
        },time);
    }
</script>
@endsection



