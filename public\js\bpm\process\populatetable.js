/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
function populateTable(taskid, listdata, payload, statusListTask, listaction, history, statusHistory, status, csrf, cmpstId) {

    var suspendBtnTask = document.getElementById("suspend_task");
    var resumeBtnTask = document.getElementById("resume_task");
    var withdrawBtnTask = document.getElementById("withdraw_task");
    var reassignBtnTask = document.getElementById("reassign_task");
    var executeBtnTask = document.getElementById("execute_task");
    var historyBtnTask = document.getElementById("history_task");

    //taskId
    $("#task_id").html(taskid);
    //assuignee
    if (listdata["assignees"] !== '') {
        var link = '';
        var arrLength = listdata["assignees"].length;
        var i = 0;
        listdata["assignees"].forEach(function (entry) {
            i++;
            link += '<a href="/find/userlogin?login_id=';
            link += entry;
            link += '" target="_blanks">';
            if (arrLength === 1) {
                link += entry;
            } else if (i == arrLength) {
                link += entry;
            } else {
                link += entry + ",";
            }
            link += '</a>';
        });
        document.getElementById("assignee").innerHTML = link;
    } else {
        document.getElementById("assignee").style.color = "grey";
        $("#assignee").html('null');
    }
    //acquired
    if (listdata["acquiredBy"] !== '') {
        if (listdata["acquiredBy"] == null) {
            document.getElementById("acquired").style.color = "grey";
            $("#acquired").html('null');
        } else {
            var link = '<a href="/find/userlogin?login_id=';
            link += listdata["acquiredBy"];
            link += '" target="_blanks">';
            link += listdata["acquiredBy"];
            link += '</a>';
            document.getElementById("acquired").style.color = "black";
            document.getElementById("acquired").innerHTML = link;
        }
    } else {
        document.getElementById("acquired").style.color = "grey";
        $("#acquired").html('null');
    }
    //composite
    $("#composite").html(listdata["compositeName"] + '!' + listdata["instanceVersion"]);
    //status
    $("#status").html(listdata["state"]);
    //process
    $("#process").html(listdata["process"]);
    //outcome
    if (listdata["outcome"] !== '') {
        document.getElementById("outcome").style.color = "black";
        $("#outcome").html(listdata["outcome"]);
    } else {
        document.getElementById("outcome").style.color = "grey";
        $("#outcome").html('null');
    }
    //activity
    $("#activity").html(listdata["taskName"]);
    //created
    $("#created").html(listdata["createDateString"]);
    //expiry
    if (listdata["expirationDateString"] !== '') {
        document.getElementById("expiry").style.color = "black";
        $("#expiry").html(listdata["expirationDateString"]);
    } else {
        document.getElementById("expiry").style.color = "grey";
        $("#expiry").html('null');
    }
    //string 1
    if (listdata["docNumber"] !== '') {
        if (listdata["docNumber"] == null) {
            document.getElementById("string_1").style.color = "grey";
            $("#string_1").html('null');
        } else {
            var link = '<a href="/find/trans/track/docno/';
            link += listdata["docNumber"];
            link += '" target="_blanks">';
            link += listdata["docNumber"];
            link += '</a>';
            document.getElementById("string_1").style.color = "black";
            document.getElementById("string_1").innerHTML = link;
        }
    } else {
        document.getElementById("string_1").style.color = "grey";
        $("#string_1").html('null');
    }
    //number 1
    if (listdata["docId"] !== '') {
        document.getElementById("number_1").style.color = "black";
        $("#number_1").html(listdata["docId"]);
    } else {
        document.getElementById("number_1").style.color = "grey";
        $("#number_1").html('null');
    }
    //string 2
    if (listdata["docType"] !== '') {
        if (listdata["docType"] == null) {
            document.getElementById("string_2").style.color = "grey";
            $("#string_2").html('null');
        } else {
            document.getElementById("string_2").style.color = "black";
            $("#string_2").html(listdata["docType"]);
        }
    } else {
        document.getElementById("string_2").style.color = "grey";
        $("#string_2").html('null');
    }
    //number 2
    if (listdata["docStatus"] !== '') {
        document.getElementById("number_2").style.color = "black";
        $("#number_2").html(listdata["docStatus"]);
    } else {
        document.getElementById("number_2").style.color = "grey";
        $("#number_2").html('null');
    }

    if(listdata["taskName"] == 'Handle Error'){
        var searchRQId = payload.search("<rqUID>");
        if(searchRQId != '-1'){
            var rqUid = getSubstring(payload, '<rqUID>', '</rqUID>');
            var link = '<a href="/find/osb/detail-rquid/log?cari=';
            link += rqUid;
            link += '" target="_blanks">';
            link += rqUid;
            link += '</a>';
            document.getElementById("rq_uid_cls").style.display = "block"; 
            $("#rq_uid").html(link);
        }
    }
    //payload
    var newPayload = String(payload).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
    $("#payload").html(newPayload);
    

    //list action
    $('#action_task').empty();
    var listAction = document.getElementById('action_task');

    if (statusListTask == 'success') {
        $('#statusApi').hide();
        listAction.add(new Option('SAVE'));
        for (const [listdata, value] of Object.entries(listaction['action'])) {
            listAction.add(new Option(value));
        }
    } else {
        if (statusListTask == '' || statusListTask == null) {
            listAction.add(new Option('SAVE'));
            $('#failed').hide();
        } else {
            console.log('list task failed ' + statusListTask);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(statusListTask);
        }

    }

    document.getElementById("task-flow").style.display = "block";

    suspendBtnTask.value = 'suspendTask';
    suspendBtnTask.onclick = function () {
        suspendResumeAction(suspendBtnTask.value, taskid, status, csrf, cmpstId)
    };
    resumeBtnTask.value = 'resumeTask';
    resumeBtnTask.onclick = function () {
        suspendResumeAction(resumeBtnTask.value, taskid, status, csrf, cmpstId)
    };
    withdrawBtnTask.value = 'withdrawTask';
    withdrawBtnTask.onclick = function () {
        withdrawAction(withdrawBtnTask.value, taskid, status, csrf, cmpstId)
    };
    reassignBtnTask.value = 'reassignTask';
    reassignBtnTask.onclick = function () {
        reassignAction(reassignBtnTask.value, taskid, status, csrf, cmpstId)
    };
    executeBtnTask.value = 'executeTask';
    executeBtnTask.onclick = function () {
        document.getElementById("failed_ul").style.display = "none";
        var updatedPayload = $('textarea#payload').val();
        var assignee = document.getElementById("assignee").textContent;
        var user = '';
        var totalAssignee = listdata["assignees"].length;
        var string1 = document.getElementById("string_1").textContent;
        ;
        var string2 = document.getElementById("string_2").textContent;
        ;
        var number1 = document.getElementById("number_1").textContent;
        ;
        var number2 = document.getElementById("number_2").textContent;
        ;

        var cdcadmin = ["Contract_Management.ProcessOwner", "Fulfilment.ProcessOwner", "Order.ProcessOwner",
            "SourcingDP.ProcessOwner", "SourcingQT.ProcessOwner", "Supplier_Management.ProcessOwner", "Profile_Management.ProcessOwner",
            "SCBusinessRule.ProcessOwner", "Codification.ProcessOwner"];

        var param = [string1, string2, number1, number2];

        var totalAssignee = (listdata["assignees"]).length;

        var taskaction = $('#action_task option:selected').text();

        if (cdcadmin.includes(assignee)) {
            user = 'cdcadmin';
            executeAction(executeBtnTask.value, taskid, user, taskaction, updatedPayload, param, status, csrf, cmpstId);
        } else {
            if (totalAssignee > 1) {
                $('#modal_submit_user').modal('show');

                var submitTaskBtn = document.getElementById("submit_user_btn");
                submitTaskBtn.onclick = function () {

                    var submitUser = document.getElementById('submit_user');
                    user = submitUser.value;
                    executeAction(executeBtnTask.value, taskid, user, taskaction, updatedPayload, param, status, csrf, cmpstId);
                    $('#modal_submit_user').modal('hide');
                }
            } else {
                user = assignee;
                executeAction(executeBtnTask.value, taskid, user, taskaction, updatedPayload, param, status, csrf, cmpstId);
            }

        }
    };
    historyBtnTask.value = 'historyTask';
    historyBtnTask.onclick = function () {
        document.getElementById("failed_ul").style.display = "none";
        $("#history_task_id").html(listdata["taskId"]);
        if (statusHistory == 'success') {
            $('#modal_history').modal('show');
            $('#failed').hide();
            var html = '<tbody style="font-size:80%;">';

            for (const [hist, value] of Object.entries(history)) {
                var d = new Date(value["updateDate"]);
                let formatted_date = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear() + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
                var outcome = '';
                if (value["outcome"] !== null) {
                    outcome = " : " + value["outcome"];
                }
                html += "<tr><td>" + formatted_date + "</td>";
                html += "<td><ul><li style='color:blue'>" + value["state"] + "<span style='color:black'>" + outcome + "</span></li><li>" + value["taskId"] + "</li><li>";

                var assignee = '';
                var arrLength = value["assignees"].length;
                var i = 0;
                for (const [val, data] of Object.entries(value["assignees"])) {
                    i++;
                    if (arrLength === 1) {
                        assignee += data;
                    } else if (i == arrLength) {
                        assignee += data;
                    } else {
                        assignee += data + ",";
                    }
                }
                html += assignee + "</li></ul></td>";
            }
            html += "</tr><tbody>";
            document.getElementById("table_history").innerHTML = html;
        } else {
            $('#modal_history').modal('hide');
            console.log('history failed ' + statusHistory);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(statusHistory);
        }
    };
}

function getSubstring(string, char1, char2) {
    return string.slice(
      string.indexOf(char1) + 7,
      string.lastIndexOf(char2),
    );
  }