@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Application With No Category</strong></h1> 
    </div> 
    <div class="block">
        <!-- Log Title -->
        <div class="block-title">
            <h2><i class="fa fa-file-text-o"></i> <strong>Solution</strong> Patch Data</h2>
        </div>
        <!-- END Log Title -->

        <!-- Log Content -->
        <div class="table-responsive">
            <p>
                <code>
                
                UPDATE sm_supplier_category
                   SET record_status = 8,
                       approved_date = NULL
                 WHERE appl_id = ?;
                <br />
                UPDATE sm_appl
                   SET status_id = 20108
                 WHERE appl_id = ?;
                **20108 follow workflow status
                </code>
            </p>
        </div>
        <!-- END Log Content -->
    </div>
    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Appl Id</th>
                    <th class="text-center">Appl No</th>
                    <th class="text-center">Appl Status Id</th>
                    <th class="text-center">Appl Status</th>
                    <th class="text-center">Tracking Diary Id</th>
                    <th class="text-center">Tracking Status Id</th>
                    <th class="text-center">Tracking Status</th>
                    <th class="text-center">Workflow Id</th>
                    <th class="text-center">Workflow Status Id</th>
                    <th class="text-center">Workflow Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Is Active Appl</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center">{{ $user->appl_id }}</td>
                    <td class="text-center">{{ $user->appl_no }}</td>
                    <td class="text-center">{{ $user->appl_status_id }}</td>
                    <td class="text-center">{{ $user->appl_status }}</td>
                    <td class="text-center">{{$user->tracking_diary_id}}</td>
                    <td class="text-center">{{$user->tracking_status_id}}</td>
                    <td class="text-center">{{$user->tracking_status}}</td>
                    <td class="text-center">{{$user->workflow_id}}</td>
                    <td class="text-center">{{$user->workflow_status_id}}</td>
                    <td class="text-center">{{$user->workflow_status}}</td>
                    <td class="text-center">{{$user->record_status}}</td>
                    <td class="text-center">{{$user->is_active_appl}}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



