<?php

namespace App\Services\Traits\Poms;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Log;

trait PomsService
{
    public function getTalkdeskData() {
        $results = DB::connection('mysql_poms')
        ->table('sla_mitel')
        ->orderBy('date_call', 'desc')
        ->get();
        return $results;
    }

    public function getTalkdeskMonthlyData() {
        $results = DB::connection('mysql_poms')
        ->table('sla_talkdesk_monthly')
        ->orderBy('date_call', 'desc')
        ->get();
        return $results;
    }

    public function findLoginHistory($loginName, $yearSearch = 2019) {
        // All records start created on 2019 POMS Data
        $yearStart = 2019;
        $yearNow = Carbon::now()->year;

        // If using search by Year
        if($yearSearch && strlen($yearSearch) == 4 ){
            $yearStart =  $yearSearch;
            $yearNow = $yearSearch;
        }


        $resultCollect = collect([]);
        do{
            // append year for table performance_proxies only applicable for old year
            $tableName = 'performance_proxies_'.$yearStart;

            // Design table performance_proxies, if current year.. table default as performance_proxies 
            if($yearStart == Carbon::now()->year){
                $tableName = 'performance_proxies'; 
            }
            try {
                $query = DB::connection('mysql_poms')
                    ->table($tableName)
                    ->where('user_details' ,'like',$loginName.'%');
                    //->orWhere('user_details' ,$loginName);
                    //->orderBy('date_time', 'desc')
                    //->take(2)
                Log::info(__METHOD__.' >> '.$query->toSql(). ' loginName : '.$loginName);
                $results = $query->get();
            
                    if ($resultCollect->count() > 0) {
                        foreach ($results as $row) {
                            $resultCollect->push($row);
                        }
                    }else{
                        $resultCollect = collect($results);
                    }
            } catch (\Throwable $th) {
                Log::info('Error! => '.$th->getMessage());
            }
            

            $yearStart++;
        }while ($yearStart <= $yearNow);
        return $resultCollect;
    }
    
}