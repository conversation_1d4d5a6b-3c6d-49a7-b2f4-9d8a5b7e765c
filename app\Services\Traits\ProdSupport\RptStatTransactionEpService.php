<?php

namespace App\Services\Traits\ProdSupport;

use Log;
use DB;
use Carbon\Carbon;

/**
 * report TRANSAKSI Ep 
 */
trait RptStatTransactionEpService {

    /**
     * 01 - 08 : Sistem ePerolehan : Permohonan baru pendaftaran syarikat-
     * Application Type
     *  //--N-New 
     *  //--R-renew
     *  //--U-kemaskini profile
     *  //--A-add bidang
     *  //--B-Bumistatus
     *
     * Report for 01 until 05 (sequence)
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     * @return Collection 
     */
    protected function getTotalStatTS01SupplierApplication($billNo, $dateStart, $dateEnd, $applType) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT COUNT (*) as total
            FROM sm_supplier sup, sm_appl appl, sm_workflow_status wf
            WHERE sup.supplier_id = appl.supplier_id
             AND appl.appl_id = wf.doc_id
             AND wf.is_current = 1
             -- AND appl.appl_type NOT IN 'C'
             AND TRUNC (appl.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
             and appl.appl_type = ?
             AND appl.created_date = (SELECT MAX (appl1.created_date)
                                        FROM sm_appl appl1
                                       WHERE appl1.supplier_id = appl.supplier_id)   
            -- GROUP BY appl.appl_type
         ";
        $parameters = array($dateStartFmt, $dateEndFmt, $applType);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 06 : Perolehan barangan/Perkhidmatan melalui kaedah Pembelian Terus
     * Report for 06 : Perolehan barangan/Perkhidmatan melalui kaedah Pembelian Terus
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS06DirectPurchase($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT count(*) as total
            FROM fl_fulfilment_request a,
                 fl_workflow_status b,
                 pm_status c,
                 pm_status_desc d
            WHERE a.fulfilment_req_id = b.doc_id
             AND b.status_id = c.status_id
             AND c.status_id = d.status_id
             AND d.language_code = 'en'
             AND b.doc_type IN ('PR')
             AND d.STATUS_ID not in (40430,40400,40310)
             AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
            and b.is_current = 1
         ";

        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 07 - 08: Perolehan barangan/Perkhidmatan melalui kaedah Tender
     * PROCUREMENT_MODE_ID : 185 & 186
     *   185,Quotation
     *   186,Tender
     * Report for 07 - 08 : Perolehan barangan/Perkhidmatan melalui kaedah Tender
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     * @param type $procurementModeId         as 185 & 186
     */
    protected function getTotalStatTS07QuotationTenderByProcurementMode($billNo, $dateStart, $dateEnd, $procurementModeId) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT   count(*) as total 
                FROM fl_fulfilment_request a,
                     ct_contract ct,
                     ct_contract_ver ver,
                     fl_workflow_status b,
                     pm_status c,
                     pm_status_desc d,
                     pm_parameter_desc pa
               WHERE a.fulfilment_req_id = b.doc_id
                AND a.CONTRACT_ID  = ct.CONTRACT_ID
                AND ct.LATEST_CONTRACT_VER_ID = ver.CONTRACT_VER_ID
                AND ct.PROCUREMENT_MODE_ID = pa.PARAMETER_ID
                 AND pa.CODE_NAME not in ('Direct Purchase','Direct Nego')
                 AND b.status_id = c.status_id
                 AND c.status_id = d.status_id
                 AND d.language_code = 'en'
                 AND b.doc_type IN ('CR')
                 AND d.STATUS_ID not in (40810,40900)
                 AND TRUNC (a.created_date)  BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
                 AND ct.PROCUREMENT_MODE_ID = ? 
            and b.is_current = 1
            and pa.LANGUAGE_CODE = 'en'
         ";

        $parameters = array($dateStartFmt, $dateEndFmt, $procurementModeId);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 09 : Perolehan barangan/Perkhidmatan dibelanjakan melalui kaedah Kontrak Kementerian
     * Report for 09 : Perolehan barangan/Perkhidmatan dibelanjakan melalui kaedah Kontrak Kementerian
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS09FulfilmentContract($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT count(*) as total
            FROM fl_fulfilment_request a,
                 ct_contract ct,
                 ct_contract_ver ver,
                 fl_workflow_status b,
                 pm_status c,
                 pm_status_desc d,
                 pm_parameter_desc pa
                 --sc_request_item h 
            WHERE a.fulfilment_req_id = b.doc_id
            AND a.CONTRACT_ID  = ct.CONTRACT_ID
            AND ct.LATEST_CONTRACT_VER_ID = ver.CONTRACT_VER_ID
            AND ct.PROCUREMENT_MODE_ID = pa.PARAMETER_ID
             AND b.status_id = c.status_id
             AND c.status_id = d.status_id
             AND d.language_code = 'en'
             AND b.doc_type IN ('CR')
             AND d.STATUS_ID not in (40810,40900)
             AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
            and b.is_current = 1
            and pa.LANGUAGE_CODE = 'en'
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 10 : Perolehan barangan/Perkhidmatan melalui kaedah eBiddig 10
     * Report for 10 : Perolehan barangan/Perkhidmatan melalui kaedah eBiddig 10
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS10QuotationTenderByEbidding($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT COUNT (*) as total
            FROM fl_fulfilment_request a,
                 ct_contract ct,
                 sc_bid bid,
                 sc_qt qt,
                 sc_loa loa,
                 sc_loi_loa loi,
                 ct_contract_ver ver,
                 fl_workflow_status b,
                 pm_status c,
                 pm_status_desc d,
                 pm_parameter_desc pa
          WHERE  a.fulfilment_req_id = b.doc_id
             AND qt.qt_id = bid.doc_id
             AND qt.qt_id = loi.doc_id
             AND loi.loi_loa_id = loa.loi_loa_id
             AND ct.loa_id = loa.loa_id
             AND a.contract_id = ct.contract_id
             AND ct.latest_contract_ver_id = ver.contract_ver_id
             AND ct.procurement_mode_id = pa.parameter_id
             AND b.status_id = c.status_id
             AND c.status_id = d.status_id
             AND d.language_code = 'en'
             AND b.doc_type IN ('CR')
             AND d.status_id NOT IN (40810, 40900)
             AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
             AND b.is_current = 1
             AND pa.language_code = 'en'
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 11 : Permohonan Medium Akses eP (untuk pembekal) 
     * Report for 11 : Permohonan Medium Akses eP (untuk pembekal) 
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS11MediumAccessEpBySupplier($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT COUNT (*) as total 
                FROM pm_user a, sm_softcert_request b, pm_digi_cert c
                WHERE  a.user_id = b.user_id
                AND b.softcert_request_id = c.softcert_request_id
                AND a.record_status = 1
                AND b.record_status = 1
                AND TRUNC (c.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY') 
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 12 : Perkhidmatan memaparkan/mengemaskini katalog 
     * Report for 12 : Perkhidmatan memaparkan/mengemaskini katalog
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS12SupplierCatalog($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT distinct count (*) as total
                   FROM cm_supplier_item csi,
                        sm_supplier ss,
                        cm_item ci,
                        cm_price cp,
                        sm_appl sa
                  WHERE ss.supplier_id = csi.supplier_id
                    AND cp.supp_item_id(+) = csi.supp_item_id
                    AND ci.item_id = csi.item_id
                    AND ss.supplier_id = sa.supplier_id
                    AND price_type = 01
                    AND csi.record_status = 1
                    AND csi.item_status = 'U'
                    AND sa.supplier_type IN ('K', 'J')
                    AND sa.appl_id = ss.latest_appl_id
                    AND TRUNC (ci.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY') 
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 13 : Permohonan ID dan Kata Laluan untuk akses profil syarikat  
     * Report for 13 : Permohonan ID dan Kata Laluan untuk akses profil syarikat 
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS13ApplAccessCompanyProfil($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT count (*) as total
            FROM pm_access_appl ap,
                 pm_user usr,
                 pm_user_org org,
                 pm_user_role rl,
                 pm_role_desc rd
           WHERE ap.identification_no = usr.identification_no
             AND usr.user_id = org.user_id
             AND rd.language_code = 'en'
             AND usr.record_status = 1
             AND ap.is_company_profile = 1
             AND rl.role_code = 'COMPANY_PROFILE'
             AND TRUNC (usr.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
             AND rl.user_org_id = org.user_org_id
             AND rl.role_code = rd.role_code 
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 14 : Perkhidmatan kajian kepuasan pelanggan dalam pendaftaran syarikat 
     * Report for 14 : Perkhidmatan kajian kepuasan pelanggan dalam pendaftaran syarikat
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS14SurveySatisfactionCompReg($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT DISTINCT COUNT (*) as total
                FROM sm_appl
               WHERE TRUNC (created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
                 AND is_questionnaire = 1
                 AND record_status = 1
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 15 : Permohonan ID dan Kata Laluan untuk capaian modul kajian pasaran 
     * Report for 15 : Permohonan ID dan Kata Laluan untuk capaian modul kajian pasaran
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS15ApplAccessMarketResearch($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT count(*) as total
            FROM pm_access_appl ap,
                 pm_user usr,
                 pm_user_org org,
                 pm_user_role rl,
                 pm_role_desc rd
           WHERE ap.identification_no = usr.identification_no
             AND usr.user_id = org.user_id
             AND rd.language_code = 'en'
             AND usr.record_status = 1
             AND ap.is_company_profile = 1
             --AND ap.IS_MARKET_RESEARCH = 1
             AND rl.role_code = 'COMPANY_PROFILE'
             AND TRUNC (usr.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
             AND rl.user_org_id = org.user_org_id
             AND rl.role_code = rd.role_code
           ORDER BY ap.identification_no
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 16 : Perkhidmatan Kajian Pasaran  
     * Report for 16 : Perkhidmatan Kajian Pasaran 
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS16MarketResearch($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
                SELECT count(*) as total
              FROM pm_access_appl ap,
                   pm_user usr,
                   pm_user_org org,
                   pm_user_role rl,
                   pm_login_history his,
                   pm_role_desc rd
              WHERE ap.identification_no = usr.identification_no
               AND usr.user_id = org.user_id
               AND his.USER_ID = usr.USER_ID
               AND rd.LANGUAGE_CODE = 'en'
               AND ap.IS_MARKET_RESEARCH = 1 
               AND rl.ROLE_CODE = 'MARKET_RESEARCH'
               AND TRUNC (his.login_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
               AND rl.user_org_id = org.user_org_id
               AND rl.role_code = rd.role_code 
         ";
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 17 : Perkhidmatan pemberitahuan berhubung pembatalan eBidding  
     * Report for 17 : Perkhidmatan pemberitahuan berhubung pembatalan sebutharga /tender/eBidding 
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS17NotificationQuotationTenderCancellation($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('d-F-Y');
        $dateEndFmt = $dateEnd->format('d-F-Y');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            select sum(total) as total from(
 SELECT COUNT (*) as total
    FROM sc_qt sq,
       sc_workflow_status s,
       sc_bid bid,
       pm_parameter a,
       pm_parameter_desc b,
       pm_status_desc pm
    WHERE sq.qt_id = s.doc_id
    AND pm.status_id = s.status_id
    AND bid.DOC_ID = sq.QT_ID
    AND TRUNC (sq.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
    AND b.language_code = 'en'
    AND s.doc_type = 'QT'
    AND s.created_date IN (SELECT MAX (x.created_date)
                              FROM sc_workflow_status x
                             WHERE sq.qt_id = x.doc_id)
    AND sq.procurement_mode_id = b.parameter_id
    AND b.parameter_id = a.parameter_id
    AND pm.status_name like '%Cancelled%'
    UNION
    SELECT distinct COUNT (*) as total
      FROM sc_qt sq,
           sc_workflow_status s,
           pm_parameter a,
           pm_parameter_desc b,
           pm_status_desc pm
     WHERE
           sq.qt_id = s.doc_id
       AND pm.status_id = s.status_id
       AND SUBSTR (sq.qt_no, 0, 2) = 'QT'
       AND TRUNC (sq.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
       AND b.language_code = 'en'
       AND s.created_date IN (SELECT MAX (x.created_date)
                                  FROM sc_workflow_status x
                                 WHERE sq.qt_id = x.doc_id)
       AND sq.procurement_mode_id = b.parameter_id
       AND b.parameter_id = a.parameter_id
       AND pm.status_name = 'Cancelled')       
         ";
        $parameters = array($dateStartFmt, $dateEndFmt, $dateStartFmt, $dateEndFmt);
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }

    /**
     * 18 : Sistem pembelajaran kepada pengguna kerajaan dan pembekal (ePOL)  
     * Report for 18 : Sistem pembelajaran kepada pengguna kerajaan dan pembekal (ePOL)
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
    protected function getTotalStatTS18EpolByUser($billNo, $dateStart, $dateEnd) {
        $dateStartFmt = $dateStart->format('Y-m-d');
        $dateEndFmt = $dateEnd->format('Y-m-d');
        $year = $dateStart->year;
        $monthFull = $dateStart->format('F');
        $monthNo = $dateStart->month;

        $startDateExec = Carbon::now();
        $query = "
            SELECT COUNT(DISTINCT user_login_id ) AS total FROM epol_training 
            WHERE  DATE(`training_dt_enrolled`) BETWEEN  ? AND  ? 
            AND training_status IN ('E','C','N') 
         ";
        //$parameters = array($year, $monthNo);
        $parameters = array($dateStartFmt, $dateEndFmt);
        $results = DB::connection('mysql_cms')->select($query, $parameters);
        $endDateExec = Carbon::now();
        $duration = $endDateExec->diffInSeconds($startDateExec);
        $collectData = collect();
        $collectData->put("script", ['query' => $query, 'parameter' => $parameters]);
        $collectData->put("duration", $duration);
        $collectData->put("bil_no", $billNo);
        $collectData->put("data_year", $year);
        $collectData->put("data_month_no", $monthNo);
        $collectData->put("data_month_full", $monthFull);
        $collectData->put("start_execute", $startDateExec->format('Y-m-d H:i:s'));
        $collectData->put("end_execute", $endDateExec->format('Y-m-d H:i:s'));
        if (count($results) > 0) {
            $collectData->put('total', $results[0]->total);
            return $collectData;
        }

        $collectData->put('total', 0);
        return $collectData;
    }
    
    /**
     * Check exist statistic from MySQL statistic fact 
     * @param type $year
     * @param type $month
     * @return type
     */
    protected function checkTotalStatFactTransaction($year, $month) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_fact_ep_service_rpt')
                        ->where('year', $year)
                        ->where('month_no', $month)
                        ->count();
    }

    /**
     * 
     * @param type $year
     * @param type $month
     * @return type
     */
    protected function checkTotalETLTransactionReportValid($year, $month, $dateExecution) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_ep_service_log_rpt')
                        ->where('year', $year)
                        ->where('month_no', $month)
                        ->where('execution_datetime', $dateExecution)
                        ->count();
    }

    /**
     * Check exist statistic from MySQL statistic fact 
     * @param type $month
     * @param type $year
     * @return type
     */
    protected function copyStatisticETLLogToFactTable($year, $month, $dateExecution) {
        $query = "
                    INSERT INTO ep_prod_support.ps_fact_ep_service_rpt (
                      year,
                      month,
                      bil_no,
                      service_name_bm,
                      service_name_bi,
                      online_offline,
                      total,
                      created_at,
                      changed_at,
                      month_no,
                      created_by,
                      changed_by,
                      ep_service_log_rpt_id
                    ) 
                    SELECT 
                        e.year,
                        e.month,
                        e.bil_no,
                        e.service_name_bm,
                        e.service_name_bi,
                        e.online_offline,
                        e.total,
                        NOW(),
                        NOW(),
                        e.month_no,
                        e.created_by,
                        e.created_by,
                        e.id
                      FROM ps_ep_service_log_rpt e 
                      WHERE e.year = ? 
                      AND e.month_no = ? 
                      AND e.execution_datetime = ? 
        ";

        DB::connection('mysql_ep_prod_support')
                ->select($query, array($year, $month, $dateExecution));
    }

    /**
     * stat001 - stat017 (read real live - prod data)
     * untuk checking manual (/prod_support/rpt/report_001_bymonth)
     * @param type $dateStart       as first day month
     * @param type $dateEnd         as end day month
     */
//    protected function stat001($dateStart, $dateEnd, $applType) {
//        $query = "
//    SELECT COUNT (*) as t1
//    FROM sm_supplier sup, sm_appl appl, sm_workflow_status wf
//    WHERE sup.supplier_id = appl.supplier_id
//     AND appl.appl_id = wf.doc_id
//     AND wf.is_current = 1
//     AND appl.appl_type NOT IN 'C'
//      AND TRUNC (appl.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//     and appl.appl_type = ?
//     AND appl.created_date = (SELECT MAX (appl1.created_date)
//                                FROM sm_appl appl1
//                               WHERE appl1.supplier_id = appl.supplier_id)   
//    GROUP BY appl.appl_type
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd, $applType));
//    }

    //06 : Perolehan barangan/Perkhidmatan melalui kaedah Pembelian Terus
//    protected function stat006($dateStart, $dateEnd) {
//        $query = "
//    SELECT count(*) as t6
//    FROM fl_fulfilment_request a,
//         fl_workflow_status b,
//         pm_status c,
//         pm_status_desc d
//    WHERE a.fulfilment_req_id = b.doc_id
//     AND b.status_id = c.status_id
//     AND c.status_id = d.status_id
//     AND d.language_code = 'en'
//     AND b.doc_type IN ('PR')
//     AND d.STATUS_ID not in (40430,40400,40310)
//     AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//    and b.is_current = 1
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //07 : Perolehan barangan/Perkhidmatan melalui kaedah Tender 7-8
//    protected function stat007($dateStart, $dateEnd) {
//        $query = "
//SELECT   count(*)as t7
//    FROM fl_fulfilment_request a,
//         ct_contract ct,
//         ct_contract_ver ver,
//         fl_workflow_status b,
//         pm_status c,
//         pm_status_desc d,
//         pm_parameter_desc pa
//   WHERE a.fulfilment_req_id = b.doc_id
//    AND a.CONTRACT_ID  = ct.CONTRACT_ID
//    AND ct.LATEST_CONTRACT_VER_ID = ver.CONTRACT_VER_ID
//    AND ct.PROCUREMENT_MODE_ID = pa.PARAMETER_ID
//     AND pa.CODE_NAME not in ('Direct Purchase','Direct Nego')
//     AND b.status_id = c.status_id
//     AND c.status_id = d.status_id
//     AND d.language_code = 'en'
//     AND ct.PROCUREMENT_MODE_ID = '186'
//     AND b.doc_type IN ('CR')
//     AND d.STATUS_ID not in (40810,40900)
//     AND TRUNC (a.created_date)  BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//and b.is_current = 1
//and pa.LANGUAGE_CODE = 'en'
//group by pa.CODE_NAME
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //08 : Perolehan barangan/Perkhidmatan melalui kaedah Tender 7-8
//    protected function stat008($dateStart, $dateEnd) {
//        $query = "
// SELECT   count(*) as t8
//    FROM fl_fulfilment_request a,
//         ct_contract ct,
//         ct_contract_ver ver,
//         fl_workflow_status b,
//         pm_status c,
//         pm_status_desc d,
//         pm_parameter_desc pa
//   WHERE a.fulfilment_req_id = b.doc_id
//    AND a.CONTRACT_ID  = ct.CONTRACT_ID
//    AND ct.LATEST_CONTRACT_VER_ID = ver.CONTRACT_VER_ID
//    AND ct.PROCUREMENT_MODE_ID = pa.PARAMETER_ID
//     AND pa.CODE_NAME not in ('Direct Purchase','Direct Nego')
//     AND b.status_id = c.status_id
//     AND c.status_id = d.status_id
//     AND d.language_code = 'en'
//     AND ct.PROCUREMENT_MODE_ID = '185'
//     AND b.doc_type IN ('CR')
//     AND d.STATUS_ID not in (40810,40900)
//     AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//and b.is_current = 1
//and pa.LANGUAGE_CODE = 'en'
//group by pa.CODE_NAME
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //09 : Perolehan barangan/Perkhidmatan dibelanjakan melalui kaedah Kontrak Kementerian
//    protected function stat009($dateStart, $dateEnd) {
//        $query = "
//    SELECT count(*) as t9
//    FROM fl_fulfilment_request a,
//         ct_contract ct,
//         ct_contract_ver ver,
//         fl_workflow_status b,
//         pm_status c,
//         pm_status_desc d,
//         pm_parameter_desc pa
//         --sc_request_item h 
//    WHERE a.fulfilment_req_id = b.doc_id
//    AND a.CONTRACT_ID  = ct.CONTRACT_ID
//    AND ct.LATEST_CONTRACT_VER_ID = ver.CONTRACT_VER_ID
//    AND ct.PROCUREMENT_MODE_ID = pa.PARAMETER_ID
//     AND b.status_id = c.status_id
//     AND c.status_id = d.status_id
//     AND d.language_code = 'en'
//     AND b.doc_type IN ('CR')
//     AND d.STATUS_ID not in (40810,40900)
//     AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//    and b.is_current = 1
//    and pa.LANGUAGE_CODE = 'en'
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //10 : Perolehan barangan/Perkhidmatan melalui kaedah eBiddig 10 & 11
//    protected function stat010($dateStart, $dateEnd) {
//        $query = "
//  SELECT COUNT (*) as t10
//  FROM fl_fulfilment_request a,
//       ct_contract ct,
//       sc_bid bid,
//       sc_qt qt,
//       sc_loa loa,
//       sc_loi_loa loi,
//       ct_contract_ver ver,
//       fl_workflow_status b,
//       pm_status c,
//       pm_status_desc d,
//       pm_parameter_desc pa
//WHERE  a.fulfilment_req_id = b.doc_id
//   AND qt.qt_id = bid.doc_id
//   AND qt.qt_id = loi.doc_id
//   AND loi.loi_loa_id = loa.loi_loa_id
//   AND ct.loa_id = loa.loa_id
//   AND a.contract_id = ct.contract_id
//   AND ct.latest_contract_ver_id = ver.contract_ver_id
//   AND ct.procurement_mode_id = pa.parameter_id
//   AND b.status_id = c.status_id
//   AND c.status_id = d.status_id
//   AND d.language_code = 'en'
//   AND b.doc_type IN ('CR')
//   AND d.status_id NOT IN (40810, 40900)
//   AND TRUNC (a.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//   AND b.is_current = 1
//   AND pa.language_code = 'en'
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //11 : Permohonan Medium Akses eP (untuk pembekal) 
//    protected function stat011($dateStart, $dateEnd) {
//        $query = "
//    SELECT COUNT (*) as t11
//   FROM pm_user a, sm_softcert_request b, pm_digi_cert c
//   WHERE  a.user_id = b.user_id
//   AND b.softcert_request_id = c.softcert_request_id
//   AND a.record_status = 1
//   AND b.record_status = 1
//   AND TRUNC (c.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //12 : Perkhidmatan memaparkan/mengemaskini katalog
//    protected function stat012($dateStart, $dateEnd) {
//        $query = "
//    SELECT distinct count (*) as t12
//           FROM cm_supplier_item csi,
//                sm_supplier ss,
//                cm_item ci,
//                cm_price cp,
//                sm_appl sa
//          WHERE ss.supplier_id = csi.supplier_id
//            AND cp.supp_item_id(+) = csi.supp_item_id
//            AND ci.item_id = csi.item_id
//            AND ss.supplier_id = sa.supplier_id
//            AND price_type = 01
//            AND csi.record_status = 1
//            AND csi.item_status = 'U'
//            AND sa.supplier_type IN ('K', 'J')
//            AND sa.appl_id = ss.latest_appl_id
//            AND TRUNC (ci.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//       ORDER BY ci.extension_code ASC
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //13 : Permohonan ID dan Kata Laluan untuk akses profil syarikat 
//    protected function stat013($dateStart, $dateEnd) {
//        $query = "
//    SELECT count (*)as t13
//    FROM pm_access_appl ap,
//         pm_user usr,
//         pm_user_org org,
//         pm_user_role rl,
//         pm_role_desc rd
//   WHERE ap.identification_no = usr.identification_no
//     AND usr.user_id = org.user_id
//     AND rd.language_code = 'en'
//     AND usr.record_status = 1
//     AND ap.is_company_profile = 1
//     AND rl.role_code = 'COMPANY_PROFILE'
//     AND TRUNC (usr.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//     AND rl.user_org_id = org.user_org_id
//     AND rl.role_code = rd.role_code
//   ORDER BY ap.identification_no
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //14 : Perkhidmatan kajian kepuasan pelanggan dalam pendaftaran syarikat
//    protected function stat014($dateStart, $dateEnd) {
//        $query = "
//    SELECT DISTINCT COUNT (*) as t14
//           FROM sm_appl
//          WHERE TRUNC (created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//            AND is_questionnaire = 1
//            AND record_status = 1
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //15 : Permohonan ID dan Kata Laluan untuk capaian modul kajian pasaran
//    protected function stat015($dateStart, $dateEnd) {
//        $query = "
//    SELECT count(*) as t15
//    FROM pm_access_appl ap,
//         pm_user usr,
//         pm_user_org org,
//         pm_user_role rl,
//         pm_role_desc rd
//   WHERE ap.identification_no = usr.identification_no
//     AND usr.user_id = org.user_id
//     AND rd.language_code = 'en'
//     AND usr.record_status = 1
//     AND ap.is_company_profile = 1
//     --AND ap.IS_MARKET_RESEARCH = 1
//     AND rl.role_code = 'COMPANY_PROFILE'
//     AND TRUNC (usr.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//     AND rl.user_org_id = org.user_org_id
//     AND rl.role_code = rd.role_code
//   ORDER BY ap.identification_no
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //16 : Perkhidmatan Kajian Pasaran 
//    protected function stat016($dateStart, $dateEnd) {
//        $query = "
//    SELECT count(*) as t16
//  FROM pm_access_appl ap,
//       pm_user usr,
//       pm_user_org org,
//       pm_user_role rl,
//       pm_login_history his,
//       pm_role_desc rd
//  WHERE ap.identification_no = usr.identification_no
//   AND usr.user_id = org.user_id
//   AND his.USER_ID = usr.USER_ID
//   AND rd.LANGUAGE_CODE = 'en'
//   AND ap.IS_MARKET_RESEARCH = 1 
//   AND rl.ROLE_CODE = 'MARKET_RESEARCH'
//   AND TRUNC (his.login_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//   AND rl.user_org_id = org.user_org_id
//   AND rl.role_code = rd.role_code
//   order by ap.IDENTIFICATION_NO
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }

    //17 : Perkhidmatan pemberitahuan berhubung pembatalan eBidding 
//    protected function stat017_bd($dateStart, $dateEnd) {
//        $query = "
//    select sum(total) as total from(
// SELECT COUNT (*) as total
//    FROM sc_qt sq,
//       sc_workflow_status s,
//       sc_bid bid,
//       pm_parameter a,
//       pm_parameter_desc b,
//       pm_status_desc pm
//    WHERE sq.qt_id = s.doc_id
//    AND pm.status_id = s.status_id
//    AND bid.DOC_ID = sq.QT_ID
//    AND TRUNC (sq.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//    AND b.language_code = 'en'
//    AND s.doc_type = 'QT'
//    AND s.created_date IN (SELECT MAX (x.created_date)
//                              FROM sc_workflow_status x
//                             WHERE sq.qt_id = x.doc_id)
//    AND sq.procurement_mode_id = b.parameter_id
//    AND b.parameter_id = a.parameter_id
//    AND pm.status_name like '%Cancelled%'
//    UNION
//    SELECT distinct COUNT (*) as total
//      FROM sc_qt sq,
//           sc_workflow_status s,
//           pm_parameter a,
//           pm_parameter_desc b,
//           pm_status_desc pm
//     WHERE
//           sq.qt_id = s.doc_id
//       AND pm.status_id = s.status_id
//       AND SUBSTR (sq.qt_no, 0, 2) = 'QT'
//       AND TRUNC (sq.created_date) BETWEEN to_date(?,'DD-MONTH-YYYY') AND to_date(?,'DD-MONTH-YYYY')
//       AND b.language_code = 'en'
//       AND s.created_date IN (SELECT MAX (x.created_date)
//                                  FROM sc_workflow_status x
//                                 WHERE sq.qt_id = x.doc_id)
//       AND sq.procurement_mode_id = b.parameter_id
//       AND b.parameter_id = a.parameter_id
//       AND pm.status_name = 'Cancelled') 
//         ";
//        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($billNo, $dateStart, $dateEnd));
//    }
    /**
     * Read4year1 - Read4year18 (read local db to display @ reportbyYear)
     * untuk display (/prod_support/rpt/report_001_byyear)
     * @param type $getyear       as get year of report
     */
    //read local db to display @ reportbyYear&Month

//    protected function Read4case1($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 1
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case2($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 2
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case3($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 3
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case4($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 4
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case5($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 5
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case6($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 6
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case7($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 7
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case8($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//    and a.bil_no = 8
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case9($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 9
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case10($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 10
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case11($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 11
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case12($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 12
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case13($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 13
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case14($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 14
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case15($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 15
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case16($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 16
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case17($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 17
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//
//    protected function Read4case18($getyear, $getmonth) {
//        $query = "
//    select total from ep_prod_support.ps_fact_ep_service_rpt as a
//    where a.year = ?
//    and month_no = ?
//    and a.online_offline ='online'
//     and a.bil_no = 18
//         ";
//        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
//    }
//    protected function CheckfacTbl() {
//        $query = DB::connection('mysql_ep_prod_support')->select("
//            select * from ep_prod_support.ps_fact_ep_service_rpt
//            where year = YEAR(current_date())
//            and month = MONTHNAME(current_date())
//            and service_name_bm ='Perolehan barangan/Perkhidmatan melalui kaedah Tender'
//            and online_offline ='online'
//            and total <> '0' ;
//        ", array());
//        return $query[0];
//    }

    /**
     * Read4year1 - Read4year18 (read local db to display @ reportbyYear)
     * untuk display (/prod_support/rpt/report_001_byyear)
     * @param type $getyear       as get year of report
     */
    protected function listForYear($year) {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select bil_no, service_name_bm,year, sum(case month_no when 1 then  total end)  as Jan , sum(case month_no when 2 then  total end)  as Feb
           , sum(case month_no when '3' then  total end)  as March, sum(case month_no when '4' then  total end)  as April, sum(case month_no when '5' then  total end)  as May
           , sum(case month_no when '6' then  total end)  as June, sum(case month_no when '7' then  total end)  as July, sum(case month_no when '8' then  total end)  as Aug
           , sum(case month_no when '9' then  total end)  as Sep, sum(case month_no when '10' then  total end)  as October
           , sum(case month_no when '11' then  total end)  as Nov, sum(case month_no when '12' then  total end) as December
           from ps_fact_ep_service_rpt where year = ? and online_offline = 'online' group by bil_no,service_name_bm, year
                ", array($year));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function findYear() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select distinct year from ps_fact_ep_service_rpt", array());
        return $query;
    }
    
    protected function findServiceName() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select distinct bil_no, service_name_bm from ps_fact_ep_service_rpt order by service_name_bm", array());
        return $query;
    }
    
    protected function getNameService($name) {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select distinct service_name_bm from ps_fact_ep_service_rpt where bil_no = ?", array($name));
        return $query;
    }

    protected function findMonth() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select distinct month, month_no from ps_fact_ep_service_rpt", array());
        return $query;
    }
    
    protected function listTindakan() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_datalookup where category = 'Tindakan' ", array());
        return $query;
    }
    
    protected function listPengguna() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_datalookup where category = 'Pengguna' ", array());
        return $query;
    }
    
    protected function listStatus() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_datalookup where category = 'Status' ", array());
        return $query;
    }
    
    protected function listReportSummary() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_summary where status <> 'Deleted' or status is null", array());
        return $query;
    }
    
    protected function listReportSummaryInDetails($id) {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_summary where (status <> 'Deleted' or status is null) and id = ? ", array($id));
        return $query;
    }
    
    protected function listDataLookup() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_datalookup", array());
        return $query;
    }
    
    protected function listSummary($Id) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_report_summary')
                        ->where('id', $Id)
                        ->first();
    }
    
    protected function listDataLook($Id) {
        return DB::connection('mysql_ep_prod_support')
                        ->table('ps_report_datalookup')
                        ->where('data_id', $Id)
                        ->first();
    }
    
    protected function listStatistic($year, $bil_no) {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select sum(case month_no when 1 then  total end)  as January , sum(case month_no when 2 then  total end)  as Feb
           , sum(case month_no when '3' then  total end)  as March, sum(case month_no when '4' then  total end)  as April, sum(case month_no when '5' then  total end)  as May
           , sum(case month_no when '6' then  total end)  as June, sum(case month_no when '7' then  total end)  as July, sum(case month_no when '8' then  total end)  as Aug
           , sum(case month_no when '9' then  total end)  as Sep, sum(case month_no when '10' then  total end)  as October
           , sum(case month_no when '11' then  total end)  as Nov, sum(case month_no when '12' then  total end) as December
           from ps_fact_ep_service_rpt where year = ? and bil_no = ? and online_offline = 'online'
                ", array($year, $bil_no));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
    
     protected function listScriptByReport($reportid) {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_report_script where report_id = ?
        ", array($reportid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
    
    protected function getFileForDownload($scriptid) {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_report_script where script_id = ?
        ", array($scriptid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function Readfact_yearmonth_online($getyear, $getmonth) {
        $query = "
     select * from ep_prod_support.ps_fact_ep_service_rpt as a
    where a.year = ?
    and month_no = ?
    and a.online_offline ='online'
    order by bil_no
         ";
        return $results = DB::connection('mysql_ep_prod_support')->select($query, array($getyear, $getmonth));
    }
    
    protected function getDetailsByDateApply($date) {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_summary where (status <> 'Deleted' or status is null) and received_date_apply = ? ", array($date));
        return $query;
    }
    
    protected function getDetailsByDateApplyFull() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select * from ps_report_summary where (status <> 'Deleted' or status is null)", array());
        return $query;
    }
    
    protected function downloadDetailsCurrentYear() {
        $query = DB::connection('mysql_ep_prod_support')
                ->select("select perkara, remarks,pemohon,received_date_apply,data_required_date,status from ps_report_summary where (status <> 'Deleted' or status is null)", array());
        return $query;
    }
    
    protected function listActivityByAdminEp($start_date, $end_date) {
        $query = DB::connection('oracle_nextgen_rpt')
        ->select("SELECT epa.user_name as ep_admin,
DECODE (ptd.doc_type,
       'SR', 'Permohonan Pembekal',
       'RN', 'Nota Minta',
       'FN', 'Nota Penerimaan Bekalan/Perkhidmatan',
       'UP', 'Profil Pengguna',
       'PR', 'Permintaan Pembelian',
       'DOC', 'Dokumen Sokongan & Senarai Semak',
       'UOM', 'Unit Ukuran',
       'OP', 'Profil Organisasi',
       'FA', 'Pindaan dengan Kontrak Tambahan',
       'MR', 'Akses Kajian Pasaran',
       'FIN', 'Organisasi Kewangan',
       'QT', 'Sebut Harga/Tender',
       'USG', 'Kumpulan Pengguna',
       'QTV', 'Nilai Sebut harga dengan tender'
      ) as module_document,
ptd.doc_no as document_number, ptd.action_desc as action_desc,
ptd.actioned_by as actioned_by_id,
TO_CHAR (ptd.actioned_date, 'dd/Mon/yyyy hh:mi:ss PM') as action_date,
psd.status_name as document_status
FROM pm_tracking_diary ptd,
pm_user epa,
pm_user_org puo,
pm_user_role pur,
pm_status_desc psd
WHERE epa.user_id = puo.user_id
AND ptd.actioned_by = epa.user_id
AND puo.user_org_id = pur.user_org_id
AND ptd.status_id = psd.status_id
AND psd.language_code = 'ms'
AND psd.record_status = 1
AND org_profile_id = 1
AND epa.record_status = 1
AND puo.record_status = 1
AND pur.record_status = 1
AND TRUNC (ptd.actioned_date) >= ? AND TRUNC (ptd.actioned_date) <= ?
AND pur.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2')
AND ptd.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2') and ROWNUM <=1000",array($start_date, $end_date));  
        return $query;
    }

    protected function downloadListActivityByAdminEp($start_date, $end_date) {
        $query = DB::connection('oracle_nextgen_rpt')
        ->select("SELECT epa.user_name as ep_admin,
DECODE (ptd.doc_type,
       'SR', 'Permohonan Pembekal',
       'RN', 'Nota Minta',
       'FN', 'Nota Penerimaan Bekalan/Perkhidmatan',
       'UP', 'Profil Pengguna',
       'PR', 'Permintaan Pembelian',
       'DOC', 'Dokumen Sokongan & Senarai Semak',
       'UOM', 'Unit Ukuran',
       'OP', 'Profil Organisasi',
       'FA', 'Pindaan dengan Kontrak Tambahan',
       'MR', 'Akses Kajian Pasaran',
       'FIN', 'Organisasi Kewangan',
       'QT', 'Sebut Harga/Tender',
       'USG', 'Kumpulan Pengguna',
       'QTV', 'Nilai Sebut harga dengan tender'
      ) as module_document,
ptd.doc_no as document_number, ptd.action_desc as action_desc,
ptd.actioned_by as actioned_by_id,
TO_CHAR (ptd.actioned_date, 'dd/Mon/yyyy hh:mi:ss PM') as action_date,
psd.status_name as document_status
FROM pm_tracking_diary ptd,
pm_user epa,
pm_user_org puo,
pm_user_role pur,
pm_status_desc psd
WHERE epa.user_id = puo.user_id
AND ptd.actioned_by = epa.user_id
AND puo.user_org_id = pur.user_org_id
AND ptd.status_id = psd.status_id
AND psd.language_code = 'ms'
AND psd.record_status = 1
AND org_profile_id = 1
AND epa.record_status = 1
AND puo.record_status = 1
AND pur.record_status = 1
AND TRUNC (ptd.actioned_date) >= ? AND TRUNC (ptd.actioned_date) <= ?
AND pur.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2')
AND ptd.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2')",array($start_date, $end_date));  
        return $query;
    }

    protected function listLogAction() {
        $query = DB::connection('mysql_ep_support')
                ->select("select * from ep_report_log_action where log_type = 'E' ", array());
        return $query;
    }

    protected function findIntegrationSSM($year) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT to_char(trans_date,'YYYY-MM') AS YEAR_MONTH_REQUEST, count(DISTINCT remarks_1) TOTAL_SSM_NO, count(*) TOTAL_REQUEST  
 FROM osb_logging WHERE service_code IN ('SSM-101')
                    AND trans_type = 'OBRes' 
                    AND status_code = '70060' 
                    AND to_char(trans_date,'YYYY') = ?
                GROUP BY  to_char(trans_date,'YYYY-MM')
                order by 1", array($year));
        return $query;
    }

    protected function findIntegrationmyIdentity($year) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->select(" SELECT to_char(trans_date,'YYYY-MM') AS YEAR_MONTH_REQUEST, count(DISTINCT remarks_1) TOTAL_IC_NO, count(*) TOTAL_REQUEST  
 FROM osb_logging WHERE service_code IN ('JPN-101','JPN-102')
                    AND trans_type = 'OBRes' 
                    AND status_code = '70018' 
                    AND to_char(trans_date,'YYYY') = ?
                GROUP BY  to_char(trans_date,'YYYY-MM')
                order by 1", array($year));
        return $query;
    }

}
