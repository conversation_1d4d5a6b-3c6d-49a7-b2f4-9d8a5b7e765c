@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>SOURCING QT BPM TASK<br>
                <small>Refire QuotationTenderCreation > Start</small>
        </h1>
    </div>
</div>

<div class="block block-alt-noborder full">
    <div class="row" >
        <div class="col-sm-12">
            <div class="block">
                <div class="block-title">
                    <h2><strong>Search</strong> QT Number</h2>
                </div>

                <form action="{{url('/bpm/sourcing/task/qtcreation')}}" method="post" class="form-horizontal form-bordered" >
                    {{ csrf_field() }}
                    <div class="form-group">
                        <div class="input-group">
                            <input type="text" id="qt_no" name="qt_no" class="form-control" placeholder="Quotation Number .." 
                                   required="required" value="{{old('qt_no')}}">
                                <span class="input-group-addon"><i class="fa fa-file-text"></i>  Document Number <span class="text-danger">*</span></span>
                        </div>
                    </div>
                    <h5 class="alert alert-success" id="success" style="display:none;"></h5>
                    <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
                    @if(isset($elements))
                    <div class="block">
                        <table id="table_element" class="table table-borderless" style="table-layout: fixed; width: 100%;">
                            <tbody>
                                @foreach($elements as $element)
                                <tr id="{{ $element["name"] }}">
                                    @if($element["name"] === 'SC_QuotationTender_Data')
                                    <td style="height:450px;width:100%;overflow-x:scroll;overflow-y:hidden;">
                                        <label>{{ $element["name"] }}</label> <br/>
                                        <textarea id="{{ $element["name"] }}" style="width: 100%;height:96%;resize:none;"><SC_QuotationTender_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_QuotationTender_Data">
                                            {{ htmlentities($payload)}}
                                        </textarea>
                                    </td>
                                    @elseif($element["name"] === 'document_number')
                                    <td>
                                        <label>{{ $element["name"] }}</label> <br/>
                                        <input id="{{ $element["name"] }}" type="text" class="form-control" value="{{ $quotationTenderPayload[0]->document_number }}">
                                    </td>

                                    @elseif($element["name"] === 'task_performer')
                                    <td>
                                        <label>{{ $element["name"] }}</label> <br/>
                                        <input id="{{ $element["name"] }}" type="text" class="form-control" value="{{ $quotationTenderPayload[0]->task_performer }}">
                                    </td>

                                    @else
                                    <td>
                                        <label>{{ $element["name"] }}</label> <br/>
                                        <select id="{{ $element["name"] }}" class="form-control">
                                            <option id="{{ $element["name"] }}" value="false">True</option>
                                            <option id="{{ $element["name"] }}" value="false">False</option>
                                        </select>
                                    </td>

                                    @endif
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    @endif
                    <div class="form-group form-actions">
                        <div class="col-md-12">
                            <button type="submit" class="btn btn-sm btn-info fa fa-search pull-right"> Search </button>
                            @if(isset($elements))
                            <button id="execute_service" name="execute_service" class="btn btn-sm btn-primary fa fa-fire pull-right"> Execute </button>
                            @endif
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    TablesDatatables.init();
});</script>
<script>

    $(document).ready(function () {
        var params = [];
        var elements = [];
        $('#execute_service').on('click', function () {
            params = [];
            elements = [];
            
            $('#table_element tr').find('textarea').each(function () {
                elements.push({name: this.id, value: this.value, type:'object'});
            });
            
            $('#table_element tr').find('input').each(function () {
                elements.push({name: this.id, value: this.value, type:'string'});
            });

            $('#table_element tr').find('option:selected').each(function () {
                elements.push({name: this.id, value: this.value, type:'boolean'});
            });

            var process = 'QuotationTenderCreation';
            var trigger = 'start';
            var url = 'SourcingQT';
            var csrf = $("input[name=_token]").val();

            $.ajax({
                type: "POST",
                url: "/bpm/service/manager/create/" + process,
                data: {"_token": csrf, "url": url, "process": process, "trigger": trigger, "params": params, "elements": elements}
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#success').show();
                    document.getElementById("success").innerHTML = resp.status;
                } else {
                    $('#failed').show();
                    document.getElementById("failed").innerHTML = resp.status;
                }
            });
        });
    });


</script>
@endsection



