@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carianforms" action="{{url('/find/qt/scquotationtender')}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection


@section('content')
    <style>
        .table thead > tr > th {
            font-size: 11px;
        }

        .table tbody > tr > td {
            font-size: 10px;
        }
    </style>
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Payload QT (SC Quotation Tender Data)<br>
                <small>Masukkan <span class="text-info">No. QT </span> pada carian diatas...</small>
            </h1>
        </div>
    </div>

    @if($quotationTenderPayload == null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-list-alt"></i>
                        <strong>Payload bagi Carian: </strong>{{$carian}}
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">

                    </div>
                </div>
            </div>

        </div>
        </div>
    @endif


    @if($quotationTenderPayload != null)
        <div class="block block-alt-noborder full">

            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-list-alt"></i>
                        <strong>Payload bagi Carian: </strong>{{$carian}}
                    </h1>
                </div>

                <!--SC_Direct_Purchase Block-->
                <div class="block">

                     <div class="block-title">
                        <h2>Steps on Ninja</h2>
                    </div>

                    <p>&nbsp;&nbsp;&nbsp;&nbsp;Service Manager > SourcingQT > QuotationTenderCreation [Process Service] > start [Trigger]</p>
                    
                    <div class="block">
                        <div class="block-title">
                            <h2>SCQuotationTenderCreation</h2>
                        </div>

                        <?php
                        $xmlHeader = '<SC_QuotationTender_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_QuotationTender_Data">';
                        $xml = $quotationTenderPayload[0]->qt_tender;
                        ?>
                        <pre class="line-numbers">
                    <code class="language-markup">{{ $xmlHeader }}</code>
                    <code class="language-markup">{{ $xml }}</code>
                        </pre><br/><br/>

                    </div>

                    <div class="block">
                        <div class="block-title">
                            <h2>document_number</h2>
                        </div>
                        {{ $quotationTenderPayload[0]->document_number }}
                        <br/><br/>
                    </div>

                    <div class="block">
                        <div class="block-title">
                            <h2>task_performer</h2>
                        </div>
                        {{ $quotationTenderPayload[0]->task_performer }}
                        <br/><br/>
                    </div>

                    <div class="block">
                        <div class="block-title">
                            <h2>requote</h2>
                        </div>
                        false
                        <br/><br/>
                    </div>
                </div>
            </div>

    @endif


@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function () {
            TablesDatatables.init();
        });</script>
    <script>

        $('#page-container').removeAttr('class');
    </script>
@endsection



