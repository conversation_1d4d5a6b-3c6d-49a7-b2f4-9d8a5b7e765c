<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class GPKIController extends Controller
{

    public function __construct()
    {
        $this->middleware('auth');
    }

    private function getTableName($year = null)
    {
        return $year ? "ep_gpki_user_signing_list_{$year}" : "ep_gpki_user_signing_list";
    }

    public function test()
    {
        return view('gpki.test');
    }

    public function sign()
    {
        return view('gpki.sign');
    }

    public function verify()
    {
        return view('gpki.verify');
    }

    public function userSigningList(Request $request)
    {
        $carian = $request->cari;
        $year = $request->year;
        $tableName = $this->getTableName($year);

        $query = DB::connection('mysql_ep_support')->table($tableName);

        if ($carian) {
            $query->where(function ($q) use ($carian, $year) {
                if ($year == '2024') {
                    // For 2024 table - using varchar(100) identification_no
                    $q->where('identification_no', 'like', '%' . $carian . '%')
                        ->orWhere('status', 'like', '%' . $carian . '%')
                        ->orWhere('remark', 'like', '%' . $carian . '%')
                        ->orWhere('user_id', 'like', '%' . $carian . '%')
                        ->orWhere('batch', 'like', '%' . $carian . '%');
                } else {
                    // For default table - using bigint(50) identification_no and created_by
                    $q->where('identification_no', 'like', '%' . $carian . '%')
                        ->orWhere('created_by', 'like', '%' . $carian . '%')
                        ->orWhere('status', 'like', '%' . $carian . '%')
                        ->orWhere('remark', 'like', '%' . $carian . '%');
                }
            });
        }

        $results = $query->paginate(25);

        // Get available years for dropdown
        $availableYears = ['2024']; // Add more years as needed

        return view('gpki.userSigningList', compact('results', 'year', 'availableYears'));
    }

    public function userStatusCount(Request $request)
    {
        $year = $request->year;
        $tableName = $this->getTableName($year);

        $results = DB::connection('mysql_ep_support')->table($tableName)
            ->selectRaw('COALESCE(status, \'pending\') as status, count(*) as jumlah')
            ->groupBy('status')
            ->get();

        return response()->json($results);
    }

    public function userSigningListCSV(Request $request)
    {
        $carian = $request->cari;
        $year = $request->year;
        $tableName = $this->getTableName($year);

        $query = DB::connection('mysql_ep_support')->table($tableName);

        if ($carian) {
            $query->where(function ($q) use ($carian, $year) {
                if ($year == '2024') {
                    // For 2024 table
                    $q->where('identification_no', 'like', '%' . $carian . '%')
                        ->orWhere('status', 'like', '%' . $carian . '%')
                        ->orWhere('remark', 'like', '%' . $carian . '%')
                        ->orWhere('user_id', 'like', '%' . $carian . '%')
                        ->orWhere('batch', 'like', '%' . $carian . '%');
                } else {
                    // For default table
                    $q->where('identification_no', 'like', '%' . $carian . '%')
                        ->orWhere('created_by', 'like', '%' . $carian . '%')
                        ->orWhere('status', 'like', '%' . $carian . '%')
                        ->orWhere('remark', 'like', '%' . $carian . '%');
                }
            });
        }

        $results = $query->get();

        // Define headers based on table structure
        $headers = $year == '2024'
            ? ['user_id', 'total_gpki', 'batch', 'identification_no', 'status', 'remark']
            : ['created_by', 'identification_no', 'status', 'remark'];

        $csvContent = implode(",", $headers) . "\n";

        foreach ($results as $row) {
            $remark = $row->remark;

            // Extract NRIC
            preg_match('/nric:\s*([0-9]+)/', $remark, $nricMatch);
            $nric = $nricMatch[1] ?? 'N/A';

            // Extract all mediumType values
            preg_match_all('/"mediumType"\s*:\s*"([^"]+)"/', $remark, $mediumTypeMatches);
            $mediumTypes = !empty($mediumTypeMatches[1]) ? implode(" + ", $mediumTypeMatches[1]) : 'N/A';

            // Extract Message
            preg_match('/message:\s*(.+)/', $remark, $messageMatch);
            $message = $messageMatch[1] ?? 'N/A';

            // Format the final remark
            $formattedRemark = "nric: $nric - mediumType: $mediumTypes - message: $message";

            if ($year == '2024') {
                $rowData = [
                    $row->user_id ?? '',
                    $row->total_gpki ?? '',
                    $row->batch ?? '',
                    $row->identification_no ?? '',
                    $row->status ?? '',
                    $formattedRemark
                ];
            } else {
                $rowData = [
                    $row->created_by ?? '',
                    $row->identification_no ?? '',
                    $row->status ?? '',
                    $formattedRemark
                ];
            }

            $csvContent .= implode(",", array_map(function ($field) {
                return '"' . str_replace('"', '""', str_replace("\n", ' ', $field)) . '"';
            }, $rowData)) . "\n";
        }

        $response = response($csvContent, 200);
        $response->header('Content-Type', 'text/csv');
        $response->header('Content-Disposition', 'attachment; filename="gpki_user_signing_list_' . ($year ?? 'current') . '_' . date('YmdHis') . '.csv"');

        return $response;
    }
}