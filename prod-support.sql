/*
SQLyog Ultimate v12.09 (64 bit)
MySQL - 5.7.24-log : Database - ep_prod_support
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`ep_prod_support` /*!40100 DEFAULT CHARACTER SET utf8mb4 */;

USE `ep_prod_support`;

/*Table structure for table `ps_change_request` */

DROP TABLE IF EXISTS `ps_change_request`;

CREATE TABLE `ps_change_request` (
  `changed_request_id` int(11) NOT NULL AUTO_INCREMENT,
  `data_fix_dtl_id` int(11) DEFAULT NULL,
  `request_type` varchar(100) DEFAULT NULL,
  `request_category` varchar(100) DEFAULT NULL,
  `description` text,
  `reason` text,
  `requester_name` varchar(100) DEFAULT NULL,
  `requester_contact_no` varchar(100) DEFAULT NULL,
  `request_date` datetime DEFAULT NULL,
  `recommender_name` varchar(100) DEFAULT NULL,
  `recommender_contact_no` varchar(100) DEFAULT NULL,
  `recommender_date` datetime DEFAULT NULL,
  `approver_name` varchar(100) DEFAULT NULL,
  `approver_contact_no` varchar(100) DEFAULT NULL,
  `approver_date` datetime DEFAULT NULL,
  `remarks` text,
  `system_affected` varchar(100) DEFAULT NULL,
  `impact_assessment` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  `activity_plan` varchar(100) DEFAULT NULL,
  `expected_complete_date` datetime DEFAULT NULL,
  `impact_category` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`changed_request_id`),
  KEY `ps_change_request_fk` (`data_fix_dtl_id`),
  CONSTRAINT `ps_change_request_fk` FOREIGN KEY (`data_fix_dtl_id`) REFERENCES `ps_data_fix_dtl` (`data_fix_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_change_request` */

/*Table structure for table `ps_data_fix` */

DROP TABLE IF EXISTS `ps_data_fix`;

CREATE TABLE `ps_data_fix` (
  `data_fix_id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) DEFAULT NULL,
  `description` text,
  `status` varchar(100) DEFAULT NULL,
  `datetime_porting` datetime DEFAULT NULL,
  `porting_seq` int(11) DEFAULT NULL,
  `closed_by` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`data_fix_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_data_fix` */

/*Table structure for table `ps_data_fix_case` */

DROP TABLE IF EXISTS `ps_data_fix_case`;

CREATE TABLE `ps_data_fix_case` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `data_fix_dtl_id` int(11) DEFAULT NULL,
  `case_no` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ps_data_fix_case_fk` (`data_fix_dtl_id`),
  CONSTRAINT `ps_data_fix_case_fk` FOREIGN KEY (`data_fix_dtl_id`) REFERENCES `ps_data_fix_dtl` (`data_fix_dtl_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_data_fix_case` */

/*Table structure for table `ps_data_fix_dtl` */

DROP TABLE IF EXISTS `ps_data_fix_dtl`;

CREATE TABLE `ps_data_fix_dtl` (
  `data_fix_dtl_id` int(11) NOT NULL AUTO_INCREMENT,
  `data_fix_id` int(11) DEFAULT NULL,
  `bill_seq` int(6) DEFAULT NULL,
  `datetime_porting` datetime DEFAULT NULL,
  `crm_no` varchar(100) DEFAULT NULL,
  `module` varchar(100) DEFAULT NULL,
  `problem_description` varchar(100) DEFAULT NULL,
  `requester_type` varchar(100) DEFAULT NULL,
  `requester_name` varchar(100) DEFAULT NULL,
  `requester_code` varchar(100) DEFAULT NULL,
  `problem_type` varchar(100) DEFAULT NULL,
  `endorsement_by` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` varchar(100) DEFAULT NULL,
  `remarks` text,
  `status` varchar(50) DEFAULT NULL,
  PRIMARY KEY (`data_fix_dtl_id`),
  KEY `ps_data_fix_dtl_fk` (`data_fix_id`),
  CONSTRAINT `ps_data_fix_dtl_fk` FOREIGN KEY (`data_fix_id`) REFERENCES `ps_data_fix` (`data_fix_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_data_fix_dtl` */

/*Table structure for table `ps_ep_service_log_rpt` */

DROP TABLE IF EXISTS `ps_ep_service_log_rpt`;

CREATE TABLE `ps_ep_service_log_rpt` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `execution_datetime` datetime DEFAULT NULL,
  `execution_duration` bigint(12) DEFAULT NULL,
  `year` int(4) DEFAULT NULL,
  `month` varchar(30) DEFAULT NULL,
  `bil_no` int(11) DEFAULT NULL,
  `service_name_bm` varchar(200) DEFAULT NULL,
  `service_name_bi` varchar(200) DEFAULT NULL,
  `online_offline` varchar(15) DEFAULT NULL,
  `total` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(50) DEFAULT NULL,
  `script` text,
  `month_no` int(2) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `comp_unique_key` (`execution_datetime`,`year`,`month`,`bil_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_ep_service_log_rpt` */

/*Table structure for table `ps_fact_ep_service_rpt` */

DROP TABLE IF EXISTS `ps_fact_ep_service_rpt`;

CREATE TABLE `ps_fact_ep_service_rpt` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `year` int(11) DEFAULT NULL,
  `month` varchar(30) DEFAULT NULL,
  `bil_no` int(11) DEFAULT NULL,
  `service_name_bm` varchar(200) DEFAULT NULL,
  `service_name_bi` varchar(200) DEFAULT NULL,
  `online_offline` varchar(30) DEFAULT NULL,
  `total` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `changed_at` datetime DEFAULT NULL,
  `month_no` int(2) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `ep_service_log_rpt_id` varchar(40) DEFAULT NULL COMMENT 'Run by program to get from log ETL',
  PRIMARY KEY (`id`),
  UNIQUE KEY `composite_key` (`year`,`month`,`bil_no`,`online_offline`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_fact_ep_service_rpt` */

/*Table structure for table `ps_lookup` */

DROP TABLE IF EXISTS `ps_lookup`;

CREATE TABLE `ps_lookup` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `code` varchar(100) DEFAULT NULL,
  `name` varchar(200) DEFAULT NULL,
  `description` varchar(100) DEFAULT NULL,
  `group_type` varchar(100) DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_lookup` */

insert  into `ps_lookup`(`id`,`code`,`name`,`description`,`group_type`,`created_by`,`created_date`,`changed_by`,`changed_date`) values (1,'QT','QT - To Deactivate QT/LOA/CT','QT - To Deactivate QT/LOA/CT','Problem Description','','2020-07-08 10:58:18','',NULL),(2,'QT','QT - To Delete Duplicate Data','QT - To Delete Duplicate Data','Problem Description','','2020-07-08 10:58:18','',NULL),(3,'QT','QT - To Delete Invalid Workflow','QT - To Delete Invalid Workflow','Problem Description','','2020-07-08 10:58:18','',NULL),(4,'QT','QT - To Delete Unsuccesful Proposal from Report','QT - To Delete Unsuccesful Proposal from Report','Problem Description','','2020-07-08 10:58:18','',NULL),(5,'QT','QT - To Enabled Upload Function','QT - To Enabled Upload Function','Problem Description','','2020-07-08 10:58:18','',NULL),(6,'QT','QT - To Fix Blank Screen','QT - To Fix Blank Screen','Problem Description','','2020-07-08 10:58:18','',NULL),(7,'QT','QT - To Rollback Status','QT - To Rollback Status','Problem Description','','2020-07-08 10:58:18','',NULL),(8,'QT','QT - To Update BSV Disqualified Status / Invitation','QT - To Update BSV Disqualified Status / Invitation','Problem Description','','2020-07-08 10:58:18','',NULL),(9,'QT','QT - To Update BSV Status','QT - To Update BSV Status','Problem Description','','2020-07-08 10:58:18','',NULL),(10,'QT','QT - To Update Category Code ID','QT - To Update Category Code ID','Problem Description','','2020-07-08 10:58:18','',NULL),(11,'QT','QT - To Update Checklist Details','QT - To Update Checklist Details','Problem Description','','2020-07-08 10:58:18','',NULL),(12,'QT','QT - To Update Closing Date','QT - To Update Closing Date','Problem Description','','2020-07-08 10:58:18','',NULL),(13,'QT','QT - To Update Committee Activity','QT - To Update Committee Activity','Problem Description','','2020-07-08 10:58:18','',NULL),(14,'QT','QT - To Update Committee Status/Details','QT - To Update Committee Status/Details','Problem Description','','2020-07-08 10:58:18','',NULL),(15,'QT','QT - To Update Committee Type','QT - To Update Committee Type','Problem Description','','2020-07-08 10:58:18','',NULL),(16,'QT','QT - To Update Committee User Details','QT - To Update Committee User Details','Problem Description','','2020-07-08 10:58:18','',NULL),(17,'QT','QT - To Update Conflict Data','QT - To Update Conflict Data','Problem Description','','2020-07-08 10:58:18','',NULL),(18,'QT','QT - To Update Contract Duration','QT - To Update Contract Duration','Problem Description','','2020-07-08 10:58:18','',NULL),(19,'QT','QT - To update Document Details','QT - To update Document Details','Problem Description','','2020-07-08 10:58:18','',NULL),(20,'QT','QT - To Update Item Details','QT - To Update Item Details','Problem Description','','2020-07-08 10:58:18','',NULL),(21,'QT','QT - To Update LOA Details','QT - To Update LOA Details','Problem Description','','2020-07-08 10:58:18','',NULL),(22,'QT','QT - To Update Missing Data','QT - To Update Missing Data','Problem Description','','2020-07-08 10:58:18','',NULL),(23,'QT','QT - To Update Missing Procument Type','QT - To Update Missing Procument Type','Problem Description','','2020-07-08 10:58:18','',NULL),(24,'QT','QT - To Update Missing Proposal Details','QT - To Update Missing Proposal Details','Problem Description','','2020-07-08 10:58:18','',NULL),(25,'QT','QT - To Update Missing Proposal Evaluatian Details','QT - To Update Missing Proposal Evaluatian Details','Problem Description','','2020-07-08 10:58:18','',NULL),(26,'QT','QT - To Update Null Data','QT - To Update Null Data','Problem Description','','2020-07-08 10:58:18','',NULL),(27,'QT','QT - To Update QT Details','QT - To Update QT Details','Problem Description','','2020-07-08 10:58:18','',NULL),(28,'QT','QT - To Update QT Duration','QT - To Update QT Duration','Problem Description','','2020-07-08 10:58:18','',NULL),(29,'QT','QT - To Update QT Fulfilment Type','QT - To Update QT Fulfilment Type','Problem Description','','2020-07-08 10:58:18','',NULL),(30,'QT','QT - To Update QT Schedule Date/Time','QT - To Update QT Schedule Date/Time','Problem Description','','2020-07-08 10:58:18','',NULL),(31,'QT','QT - To Update QT Upload Checklist','QT - To Update QT Upload Checklist','Problem Description','','2020-07-08 10:58:18','',NULL),(32,'QT','QT - To Update Remark','QT - To Update Remark','Problem Description','','2020-07-08 10:58:18','',NULL),(33,'QT','QT - To Update Report Data','QT - To Update Report Data','Problem Description','','2020-07-08 10:58:18','',NULL),(34,'QT','QT - To Update Specification Details','QT - To Update Specification Details','Problem Description','','2020-07-08 10:58:18','',NULL),(35,'QT','QT - To Update Supplier Profile Details','QT - To Update Supplier Profile Details','Problem Description','','2020-07-08 10:58:18','',NULL),(36,'QT','QT - To Update User Profile','QT - To Update User Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(37,'QT','QT - To Update Workflow Status','QT - To Update Workflow Status','Problem Description','','2020-07-08 10:58:18','',NULL),(38,'QT','QT - To Update/Delete Null Data','QT - To Update/Delete Null Data','Problem Description','','2020-07-08 10:58:18','',NULL),(39,'QT','QT - To Update/Delete Invalid Data','QT - To Update/Delete Invalid Data','Problem Description','','2020-07-08 10:58:18','',NULL),(40,'QT','QT - To Cancel QT/LOI/LOA','QT - To Cancel QT/LOI/LOA','Problem Description','','2020-07-08 10:58:18','',NULL),(41,'QT','QT - To Update Zonal Info','QT - To Update Zonal Info','Problem Description','','2020-07-08 10:58:18','',NULL),(42,'QT','QT - To Update Approver','QT - To Update Approver','Problem Description','','2020-07-08 10:58:18','',NULL),(43,'QT','QT - To Update Proposal Evaluation Details','QT - To Update Proposal Evaluation Details','Problem Description','','2020-07-08 10:58:18','',NULL),(44,'CT','CT - To Change PTJ Contract Owner With PTJ of Contract Admin','CT - To Change PTJ Contract Owner With PTJ of Contract Admin','Problem Description','','2020-07-08 10:58:18','',NULL),(45,'CT','CT - To Update  Item Price','CT - To Update  Item Price','Problem Description','','2020-07-08 10:58:18','',NULL),(46,'CT','CT - To Update Factoring Information','CT - To Update Factoring Information','Problem Description','','2020-07-08 10:58:18','',NULL),(47,'CT','CT - To Update Contract Agency Information','CT - To Update Contract Agency Information','Problem Description','','2020-07-08 10:58:18','',NULL),(48,'CT','CT - To Update Contract Amount','CT - To Update Contract Amount','Problem Description','','2020-07-08 10:58:18','',NULL),(49,'CT','CT - To Update Contract Bond Amount','CT - To Update Contract Bond Amount','Problem Description','','2020-07-08 10:58:18','',NULL),(50,'CT','CT - To Update Contract Bond Information','CT - To Update Contract Bond Information','Problem Description','','2020-07-08 10:58:18','',NULL),(51,'CT','CT - To Update Contract Effective/Expiry Date','CT - To Update Contract Effective/Expiry Date','Problem Description','','2020-07-08 10:58:18','',NULL),(52,'CT','CT - To Update Contract GST Amount','CT - To Update Contract GST Amount','Problem Description','','2020-07-08 10:58:18','',NULL),(53,'CT','CT - To Update Contract Information','CT - To Update Contract Information','Problem Description','','2020-07-08 10:58:18','',NULL),(54,'CT','CT - To Update Contract Information (Lampiran A1)','CT - To Update Contract Information (Lampiran A1)','Problem Description','','2020-07-08 10:58:18','',NULL),(55,'CT','CT - To Update Contract Item Amount','CT - To Update Contract Item Amount','Problem Description','','2020-07-08 10:58:18','',NULL),(56,'CT','CT - To Update Contract Item Information','CT - To Update Contract Item Information','Problem Description','','2020-07-08 10:58:18','',NULL),(57,'CT','CT - To Update Contract Item Max Quantity','CT - To Update Contract Item Max Quantity','Problem Description','','2020-07-08 10:58:18','',NULL),(58,'CT','CT - To Update Contract Zonal Information','CT - To Update Contract Zonal Information','Problem Description','','2020-07-08 10:58:18','',NULL),(59,'CT','CT - To Update Performance Bond Amount','CT - To Update Performance Bond Amount','Problem Description','','2020-07-08 10:58:18','',NULL),(60,'CT','CT - To Update Workflow Status','CT - To Update Workflow Status','Problem Description','','2020-07-08 10:58:18','',NULL),(61,'FL','FL - Closed old Fulfillment Transaction','FL - Closed old Fulfillment Transaction','Problem Description','','2020-07-08 10:58:18','',NULL),(62,'FL','FL - To Close Transaction in Old eP','FL - To Close Transaction in Old eP','Problem Description','','2020-07-08 10:58:18','',NULL),(63,'FL','FL - To update document status','FL - To update document status','Problem Description','','2020-07-08 10:58:18','',NULL),(64,'FL','FL - To update FL workflow','FL - To update FL workflow','Problem Description','','2020-07-08 10:58:18','',NULL),(65,'FL','FL - To update item details','FL - To update item details','Problem Description','','2020-07-08 10:58:18','',NULL),(66,'FL','FL - To update item summary','FL - To update item summary','Problem Description','','2020-07-08 10:58:18','',NULL),(67,'FL','FL - To Update PA Information','FL - To Update PA Information','Problem Description','','2020-07-08 10:58:18','',NULL),(68,'FL','FL - To Update PO/CO Information','FL - To Update PO/CO Information','Problem Description','','2020-07-08 10:58:18','',NULL),(69,'FL','FL - To Update PO/CO Status to Closed','FL - To Update PO/CO Status to Closed','Problem Description','','2020-07-08 10:58:18','',NULL),(70,'FL','FL - To Update PR Information','FL - To Update PR Information','Problem Description','','2020-07-08 10:58:18','',NULL),(71,'FL','FL - To update supplier item Information','FL - To update supplier item Information','Problem Description','','2020-07-08 10:58:18','',NULL),(72,'FL','FL - To Update Workflow Status','FL - To Update Workflow Status','Problem Description','','2020-07-08 10:58:18','',NULL),(73,'FL','FL - Update File No','FL - Update File No','Problem Description','','2020-07-08 10:58:18','',NULL),(74,'FL','FL - To Update PO status','FL - To Update PO status','Problem Description','','2020-07-08 10:58:18','',NULL),(75,'DP','DP - To Update Direct Purchase Information','DP - To Update Direct Purchase Information','Problem Description','','2020-07-08 10:58:18','',NULL),(76,'DP','DP - To update document Information','DP - To update document Information','Problem Description','','2020-07-08 10:58:18','',NULL),(77,'DP','DP - To Update Inactive Requisitiioner','DP - To Update Inactive Requisitiioner','Problem Description','','2020-07-08 10:58:18','',NULL),(78,'DP','DP - To Update LOA Information','DP - To Update LOA Information','Problem Description','','2020-07-08 10:58:18','',NULL),(79,'DP','DP - To Update Mismatch Data','DP - To Update Mismatch Data','Problem Description','','2020-07-08 10:58:18','',NULL),(80,'DP','DP - To Update Purchase Request Information','DP - To Update Purchase Request Information','Problem Description','','2020-07-08 10:58:18','',NULL),(81,'DP','DP - To update RN detail','DP - To update RN detail','Problem Description','','2020-07-08 10:58:18','',NULL),(82,'DP','DP - To update RN Information','DP - To update RN Information','Problem Description','','2020-07-08 10:58:18','',NULL),(83,'DP','DP - To update SQ Information','DP - To update SQ Information','Problem Description','','2020-07-08 10:58:18','',NULL),(84,'DP','DP - To Updated Data','DP - To Updated Data','Problem Description','','2020-07-08 10:58:18','',NULL),(85,'DP','DP - To Update fulfillment type Information','DP - To Update fulfillment type Information','Problem Description','','2020-07-08 10:58:18','',NULL),(86,'DP','DP - To updated mismatch Data','DP - To updated mismatch Data','Problem Description','','2020-07-08 10:58:18','',NULL),(87,'PM','PM - To Update user Information','PM - To Update user Information','Problem Description','','2020-07-08 10:58:18','',NULL),(88,'PM','PM - To Update User Profile Status','PM - To Update User Profile Status','Problem Description','','2020-07-08 10:58:18','',NULL),(89,'PM','PM - To Create User Profile','PM - To Create User Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(90,'PM','PM - To Deactivate Officer Profile','PM - To Deactivate Officer Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(91,'PM','PM - To Deactivate the Officer Profile','PM - To Deactivate the Officer Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(92,'PM','PM - To Inactive User Profile','PM - To Inactive User Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(93,'PM','PM - To Insert Role to Status','PM - To Insert Role to Status','Problem Description','','2020-07-08 10:58:18','',NULL),(94,'PM','PM - To remove duplicate Null Address','PM - To remove duplicate Null Address','Problem Description','','2020-07-08 10:58:18','',NULL),(95,'PM','PM - To Update Approver Group Information','PM - To Update Approver Group Information','Problem Description','','2020-07-08 10:58:18','',NULL),(96,'PM','PM - To update approver group user status','PM - To update approver group user status','Problem Description','','2020-07-08 10:58:18','',NULL),(97,'PM','PM - To Update Document Status','PM - To Update Document Status','Problem Description','','2020-07-08 10:58:18','',NULL),(98,'PM','PM - To update profile Information','PM - To update profile Information','Problem Description','','2020-07-08 10:58:18','',NULL),(99,'PM','PM - To update PTJ org Information','PM - To update PTJ org Information','Problem Description','','2020-07-08 10:58:18','',NULL),(100,'PM','PM - To Update PTJ user Information','PM - To Update PTJ user Information','Problem Description','','2020-07-08 10:58:18','',NULL),(101,'PM','PM - To Update QT Checklist','PM - To Update QT Checklist','Problem Description','','2020-07-08 10:58:18','',NULL),(102,'PM','PM - To Update Task Reassignment','PM - To Update Task Reassignment','Problem Description','','2020-07-08 10:58:18','',NULL),(103,'PM','PM - To Update User Group','PM - To Update User Group','Problem Description','','2020-07-08 10:58:18','',NULL),(104,'PM','PM - To Update User Group Status','PM - To Update User Group Status','Problem Description','','2020-07-08 10:58:18','',NULL),(105,'PM','PM - To Update User Information','PM - To Update User Information','Problem Description','','2020-07-08 10:58:18','',NULL),(106,'PM','PM - To Update Workflow Status','PM - To Update Workflow Status','Problem Description','','2020-07-08 10:58:18','',NULL),(107,'PM','PM - Update PTJ Information','PM - Update PTJ Information','Problem Description','','2020-07-08 10:58:18','',NULL),(108,'PM','PM - Update User Profile','PM - Update User Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(109,'PM','PM - Update User Profile Org','PM - Update User Profile Org','Problem Description','','2020-07-08 10:58:18','',NULL),(110,'PM','PM - Update User Profile Status','PM - Update User Profile Status','Problem Description','','2020-07-08 10:58:18','',NULL),(111,'PM','PM - Update User Profile Type','PM - Update User Profile Type','Problem Description','','2020-07-08 10:58:18','',NULL),(112,'PM','PM - To Update Address Status','PM - To Update Address Status','Problem Description','','2020-07-08 10:58:18','',NULL),(113,'SM','SM - To Add Application Information','SM - To Add Application Information','Problem Description','','2020-07-08 10:58:18','',NULL),(114,'SM','SM - To Alter Category Column','SM - To Alter Category Column','Problem Description','','2020-07-08 10:58:18','',NULL),(115,'SM','SM - To clone new committee','SM - To clone new committee','Problem Description','','2020-07-08 10:58:18','',NULL),(116,'SM','SM - To Deactivate Supplier Information','SM - To Deactivate Supplier Information','Problem Description','','2020-07-08 10:58:18','',NULL),(117,'SM','SM - To Deactivate Supplier User','SM - To Deactivate Supplier User','Problem Description','','2020-07-08 10:58:18','',NULL),(118,'SM','SM - To Remove Inactive Certificate Information','SM - To Remove Inactive Certificate Information','Problem Description','','2020-07-08 10:58:18','',NULL),(119,'SM','SM - To Reset Self Activation','SM - To Reset Self Activation','Problem Description','','2020-07-08 10:58:18','',NULL),(120,'SM','SM - To Reset SSO Activation','SM - To Reset SSO Activation','Problem Description','','2020-07-08 10:58:18','',NULL),(121,'SM','SM - To Reset User Profile Activation','SM - To Reset User Profile Activation','Problem Description','','2020-07-08 10:58:18','',NULL),(122,'SM','SM - To Update Application Information','SM - To Update Application Information','Problem Description','','2020-07-08 10:58:18','',NULL),(123,'SM','SM - To Update Application Status','SM - To Update Application Status','Problem Description','','2020-07-08 10:58:18','',NULL),(124,'SM','SM - To Update Category Information','SM - To Update Category Information','Problem Description','','2020-07-08 10:58:18','',NULL),(125,'SM','SM - To Update Category Status','SM - To Update Category Status','Problem Description','','2020-07-08 10:58:18','',NULL),(126,'SM','SM - To Update Certificate Information','SM - To Update Certificate Information','Problem Description','','2020-07-08 10:58:18','',NULL),(127,'SM','SM - To Update Company Division ID','SM - To Update Company Division ID','Problem Description','','2020-07-08 10:58:18','',NULL),(128,'SM','SM - To Update Company Information','SM - To Update Company Information','Problem Description','','2020-07-08 10:58:18','',NULL),(129,'SM','SM - To Update Company Name','SM - To Update Company Name','Problem Description','','2020-07-08 10:58:18','',NULL),(130,'SM','SM - To Update Company Status','SM - To Update Company Status','Problem Description','','2020-07-08 10:58:18','',NULL),(131,'SM','SM - To Update Facility Information','SM - To Update Facility Information','Problem Description','','2020-07-08 10:58:18','',NULL),(132,'SM','SM - To Update Factoring Information','SM - To Update Factoring Information','Problem Description','','2020-07-08 10:58:18','',NULL),(133,'SM','SM - To Update Payment Information','SM - To Update Payment Information','Problem Description','','2020-07-08 10:58:18','',NULL),(134,'SM','SM - To Update Personnel Information','SM - To Update Personnel Information','Problem Description','','2020-07-08 10:58:18','',NULL),(135,'SM','SM - To Update Personnel Role','SM - To Update Personnel Role','Problem Description','','2020-07-08 10:58:18','',NULL),(136,'SM','SM - To Update Site Visit Information','SM - To Update Site Visit Information','Problem Description','','2020-07-08 10:58:18','',NULL),(137,'SM','SM - To Update SM Information','SM - To Update SM Information','Problem Description','','2020-07-08 10:58:18','',NULL),(138,'SM','SM - To Update Softcert Information','SM - To Update Softcert Information','Problem Description','','2020-07-08 10:58:18','',NULL),(139,'SM','SM - To Update Softcert Status','SM - To Update Softcert Status','Problem Description','','2020-07-08 10:58:18','',NULL),(140,'SM','SM - To Update Supplier Address Division ID','SM - To Update Supplier Address Division ID','Problem Description','','2020-07-08 10:58:18','',NULL),(141,'SM','SM - To Update Supplier Information','SM - To Update Supplier Information','Problem Description','','2020-07-08 10:58:18','',NULL),(142,'SM','SM - To Update Tracking Diary','SM - To Update Tracking Diary','Problem Description','','2020-07-08 10:58:18','',NULL),(143,'SM','SM - To Update User Information','SM - To Update User Information','Problem Description','','2020-07-08 10:58:18','',NULL),(144,'SM','SM - To Update User Profile','SM - To Update User Profile','Problem Description','','2020-07-08 10:58:18','',NULL),(145,'SM','SM - To Update User Role','SM - To Update User Role','Problem Description','','2020-07-08 10:58:18','',NULL),(146,'SM','SM - To Update Virtual Certificate','SM - To Update Virtual Certificate','Problem Description','','2020-07-08 10:58:18','',NULL),(147,'SM','SM - Update Status Information','SM - Update Status Information','Problem Description','','2020-07-08 10:58:18','',NULL),(148,'SM','SM - To Update profile Information','SM - To Update profile Information','Problem Description','','2020-07-08 10:58:18','',NULL),(149,'CM','CM - Update item Information','CM - Update item Information','Problem Description','','2020-07-08 10:58:18','',NULL),(150,'PP','PP - Update PP Information','PP - Update PP Information','Problem Description','','2020-07-08 10:58:18','',NULL),(151,'BPM','BPM - To Update Instance State Status','BPM - To Update Instance State Status','Problem Description','','2020-07-08 10:58:18','',NULL),(152,'Others','Others - To update SDI Information','Others - To update SDI Information','Problem Description','','2020-07-08 10:58:18','',NULL),(153,'PP','Procument Plan','Procument Plan','Module','','2020-07-08 11:06:10','',NULL),(154,'DP','Direct Purchase','Direct Purchase','Module','','2020-07-08 11:07:33','',NULL),(155,'CT','Contract','Contract','Module','','2020-07-08 11:07:37','',NULL),(156,'QT','Quotation Tender','Quotation Tender','Module','','2020-07-08 11:07:40','',NULL),(157,'FL','Fulfilment','Fulfilment','Module','','2020-07-08 11:07:42','',NULL),(158,'SM','Supplier Management','Supplier Management','Module','','2020-07-08 11:07:45','',NULL),(159,'PM','Personal Management','Personal Management','Module','','2020-07-08 11:07:47','',NULL),(160,'BPM','BPM','BPM','Module','','2020-07-08 11:07:49','',NULL),(161,'Other','Other','Other','Module','','2020-07-08 11:07:51','',NULL),(162,'BPK','BPK Request','BPK Request','Problem Type','','2020-07-08 11:10:44','',NULL),(163,'MP','Migration Problem','Migration Problem','Problem Type','','2020-07-08 11:10:47','',NULL),(164,'MR','Ministry Restructuring','Ministry Restructuring','Problem Type','','2020-07-08 11:10:49','',NULL),(165,'SP','System Problem','System Problem','Problem Type','','2020-07-08 11:10:51','',NULL),(166,'UPS','UPS Request','UPS Request','Problem Type','','2020-07-08 11:10:53','',NULL),(167,'UR','User Request','User Request','Problem Type','','2020-07-08 11:10:55','',NULL),(168,'S','Supplier','Supplier','Requester Type','','2020-07-08 16:24:24','',NULL),(169,'P','PTJ','PTJ','Requester Type','','2020-07-08 16:24:26','',NULL),(170,'C','CDC','CDC','Requester Type','','2020-07-08 16:24:28','',NULL),(171,'B','BPK','BPK','Requester Type','','2020-07-08 16:24:30','',NULL),(172,'Deploy','Deployment System/Server','Deployment System/Server','Request Type','','2020-07-08 17:12:05','',NULL),(173,'DB','Database','Database','Request Type','','2020-07-08 17:12:07','',NULL),(174,'N','Network','Network','Request Type','','2020-07-08 17:12:09','',NULL),(175,'App','Application','Application','Request Type','','2020-07-08 17:12:11','',NULL),(176,'Others','Others','Others','Request Type','','2020-07-08 17:12:12','',NULL),(177,'F','Data Fix','Data Fix','Request Type','','2020-07-08 17:12:14','',NULL),(178,'D','Document/Content','Document/Content','Request Type','','2020-07-08 17:12:17','',NULL),(179,'SC','Scheduled Change','Scheduled Change','Request Category','','2020-07-08 17:13:24','',NULL),(180,'UC','Urgent Change','Urgent Change','Request Category','','2020-07-08 17:13:26','',NULL),(181,'MJ','Major','Major','Impact Category','','2020-07-08 17:14:34','',NULL),(182,'MN','Minor','Minor','Impact Category','','2020-07-08 17:14:35','',NULL);

/*Table structure for table `ps_patch_script` */

DROP TABLE IF EXISTS `ps_patch_script`;

CREATE TABLE `ps_patch_script` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `data_fix_dtl_id` int(11) DEFAULT NULL,
  `changed_request_id` int(11) DEFAULT NULL,
  `sql` longtext,
  `description` text,
  `created_by` varchar(100) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `changed_by` varchar(100) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `ps_patch_script_fk` (`data_fix_dtl_id`),
  KEY `ps_patch_script_fk_1` (`changed_request_id`),
  CONSTRAINT `ps_patch_script_fk` FOREIGN KEY (`data_fix_dtl_id`) REFERENCES `ps_data_fix_dtl` (`data_fix_dtl_id`),
  CONSTRAINT `ps_patch_script_fk_1` FOREIGN KEY (`changed_request_id`) REFERENCES `ps_change_request` (`changed_request_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

/*Data for the table `ps_patch_script` */

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
