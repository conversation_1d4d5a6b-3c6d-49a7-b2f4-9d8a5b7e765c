<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\OSBWebService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB as DB;

class SpkiController extends Controller
{

    use OSBService;
    use SSHService;
    use OSBWebService;

    public function getDashboardSpki()
    {
        return view('dashboard.spki');
    }

    public function getSigningMonitor()
    {
        $queries = array();
        if(isset($_SERVER['QUERY_STRING'])) {
            parse_str($_SERVER['QUERY_STRING'], $queries);
        }

        if(isset($queries['selectedDate'])) {
            $today = $queries['selectedDate'];
        } else {
            $today = Carbon::today();
        }
        

        $spkiLogList = DB::connection('mysql_ep_support')->table('ep_log_spki')
            ->select(
                'provider',
                'service',
                'status',
                DB::raw('COUNT(*) as count'),
                DB::raw('MIN(created_at) as min_start'),
                DB::raw('MAX(created_at) as max_start'),
                DB::raw('"' . $today . '" as query_date')
            )
            ->whereDate('created_at', $today)
            ->groupBy('provider', 'service', 'status')
            ->get();

        // dd($spkiLogList);

        $html = "";
        $html .= "
        <div>
            <div class='row'>
                <div class='col-md-3 col-md-offset-9'>
                    <div class='input-group date' data-provide='datepicker'>
                        <input type='text' id='created_date' name='created_date' class='form-control input-datepicker'>
                        <div class='input-group-addon'>
                            <span class='gi gi-calendar'></span>
                        </div>
                    </div>
                </div>
            </div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Provider</th>
                        <th>Service</th>
                        <th>Status</th>
                        <th>Start Check At</th>
                        <th>Latest Check At</th>
                        <th class='text-center'>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($spkiLogList as $data) {
            $provider = strtoupper($data->provider);
            $textClass = $data->status == 'success' ? 'text-success' : 'text-danger';
            $minStart = Carbon::parse($data->min_start)->format('d/m/Y h:i:s A');
            $maxStart = Carbon::parse($data->max_start)->format('d/m/Y h:i:s A');
            $html .= "
                    <tr>
                        <td style='width: 30%;'><strong>$provider</strong></td>
                        <td><strong>$data->service</strong></td>    
                        <td><strong class='$textClass'>$data->status</strong></td>  
                        <td><strong>$minStart</strong></td>  
                        <td><strong>$maxStart</strong></td>  
                        <td class='text-center'>
                            <strong>
                                <a href='#modal-list-data' 
                                class='modal-list-data-action label label-danger' 
                                data-toggle='modal' data-url='/dashboard/spki/displayRecordSpki?provider=$data->provider&service=$data->service&status=$data->status&date=$data->query_date'
                                data-title='$provider - $data->service ($data->status)' >
                                {$data->count}
                                </a>
                            </strong>
                        </td>
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayRecordSpki()
    {
        $queries = array();
        parse_str($_SERVER['QUERY_STRING'], $queries);
        // dd($queries);
        $provider = $queries['provider'];
        $service = $queries['service'];
        $status = $queries['status'];
        $query_date = $queries['date'];
        // $result = $this->getListRecordsOSBErrorEmptyResponse($code);

        // $today = Carbon::today();

        $spkiLogList = DB::connection('mysql_ep_support')->table('ep_log_spki')
            ->select('request_data', 'response_data', 'created_at')
            ->where('provider', $provider)
            ->where('service', $service)
            ->where('STATUS', $status)
            ->whereDate('created_at', $query_date)
            ->orderBy('created_at', 'desc')
            ->get();

        // dd($spkiLogList);

        //Render the data
        $html = "";

        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>Request Data</th>
                        <th class='text-center'>Response Data</th>
                        <th class='text-center'>Created At</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($spkiLogList as $data) {
            $html .= "
            <tr>
                <td class='text-left'>$data->request_data</td>
                <td class='text-left'>$data->response_data</td>
                <td class='text-center'>$data->created_at</td>
            </tr>";
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function displayNewSoftcert()
    {
        $result = DB::connection('oracle_nextgen_rpt')->table('PM_CONFIG_RULE_VALUE')
            ->select('rule_type', 'rule_value', 'eff_date', 'exp_date', 'created_date')
            ->where('rule_type', 'SMSCP')
            ->where('record_status', 1)
            ->whereDate('eff_date', '<=', Carbon::now())
            ->whereDate('exp_date', '>=', Carbon::now())
            ->get();

        //Render the data
        $html = "";

        $html .= "
            <div>
                <table class='table table-borderless table-striped table-vcenter'>
                    <thead>
                        <tr>
                            <th class='text-left'>Rule Type</th>
                            <th class='text-left'>Rule Value</th>
                            <th class='text-center'>Effective Date</th>
                            <th class='text-center'>Expire Date</th>
                            <th class='text-center'>Created Date</th>
                        </tr>
                    </thead>
                    <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td class='text-left'>$data->rule_type</td>
                <td class='text-left'>$data->rule_value</td>
                <td class='text-center'>$data->eff_date</td>
                <td class='text-center'>$data->exp_date</td>
                <td class='text-center'>$data->created_date</td>
            </tr>";
        }

        $html .= "
            </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayMonitoringRequest()
    {
        $results = DB::connection('oracle_nextgen_rpt')->table('osb_logging')
            ->select(DB::raw('MIN(trans_date) as min_datetime, MAX(trans_date) as max_datetime, CONSUMER_CODE, TARGET_SYSTEM, status_code, status_desc, COUNT(*) as total'))
            ->where('trans_type', '=', 'OBRes')
            ->where('SERVICE_FLOW', 'NOT LIKE', 'JAVA')
            ->whereIn('service_code', ['SPK-010', 'SPK-020', 'SPK-030', 'SPK-040', 'SPK-050', 'SPK-060'])
            ->whereDate('trans_date', Carbon::today()->toDateString())
            ->groupBy('CONSUMER_CODE', 'TARGET_SYSTEM', 'status_code', 'status_desc')
            ->get();

        //Render the data
        $html = "";

        $html .= "
            <div>
                <table class='table table-borderless table-striped table-vcenter'>
                    <thead>
                        <tr>
                            <th class='text-left'>Min Datetime</th>
                            <th class='text-left'>Max Datetime</th>
                            <th class='text-center'>Consumer Code</th>
                            <th class='text-center'>Target System</th>
                            <th class='text-center'>Status Code</th>
                            <th class='text-left'>Status Desc</th>
                            <th class='text-center'>Total</th>
                        </tr>
                    </thead>
                    <tbody>";

        foreach ($results as $data) {
            $html .= "
            <tr>
                <td class='text-left'>$data->min_datetime</td>
                <td class='text-left'>$data->max_datetime</td>
                <td class='text-center'>$data->consumer_code</td>
                <td class='text-center'>$data->target_system</td>
                <td class='text-center'>$data->status_code</td>
                <td class='text-left'>$data->status_desc</td>
                <td class='text-center'><span class='modal-list-data-action label label-danger'><strong>$data->total</strong></span></td>
            </tr>";
        }

        $html .= "
            </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayMonitoringRSigningSPKI()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT MIN(trans_date) as min_datetime, 
MAX(trans_date) as max_datetime,
SERVICE_CODE,
CASE 
	WHEN SERVICE_CODE = 'SPK-090' THEN 'GetChallengeQuestions'
	WHEN SERVICE_CODE = 'SPK-080' THEN 'SignData'
	WHEN SERVICE_CODE = 'SPK-100' THEN 'GenerateSecurityToken'
END AS SERVICE_NAME,
status_code,
CASE 
    WHEN SERVICE_CODE = 'SPK-100' AND REMARKS_3 = 'TG' AND status_code = '1' THEN 'Operation success'
	WHEN SERVICE_CODE = 'SPK-090' AND REMARKS_3 = 'DG' AND status_code = '1' THEN 'Operation success'
	WHEN SERVICE_CODE = 'SPK-090' AND REMARKS_3 = 'TG' AND status_code = '1' THEN 'Operation success'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'DG' AND status_code = '99' THEN 'Signing API return error'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'DG' AND status_code = '50' THEN 'User PIN is not valid'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'DG' AND status_code = '51' THEN 'User PIN is blocked'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'DG' AND status_code = '0' THEN 'Wrong Answer. >> Error code given by service was 40'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'DG' AND status_code = '1' THEN 'Operation success'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '99' THEN 'Signing fatal error'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '50' THEN 'Wrong PIN '
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '51' THEN 'PIN blocked'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '0' THEN 'Invalid Challenge Response information'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '1' THEN 'Operation success'
	WHEN SERVICE_CODE = 'SPK-080' AND REMARKS_3 = 'TG' AND status_code = '000000' THEN 'Client received SOAP Fault from server: java.lang.NullPointerException'
	ELSE 'N/A'
END AS STATUS_DESCRIPTION ,
REMARKS_3 AS spki_provider, COUNT(*) as total
FROM NGEP_OSB.OSB_LOGGING ol
            where trans_type = 'IBRes'
            and service_code in ('SPK-080', 'SPK-090', 'SPK-100')
and trunc(TRANS_DATE) = trunc(sysdate)
group by REMARKS_3 ,SERVICE_CODE,status_code
ORDER BY  REMARKS_3,SERVICE_CODE ,  MAX(trans_date) desc ");

        //Render the data
        $html = "";

        $html .= "
            <div>
                <table class='table table-borderless table-striped table-vcenter'>
                    <thead>
                        <tr>
                            <th class='text-left'>Min Datetime</th>
                            <th class='text-left'>Max Datetime</th>
                            <th class='text-center'>Service Code</th>
                            <th class='text-center'>Service Name</th>
                            <th class='text-center'>Provider</th>
                            <th class='text-left'>Status Code</th>
                            <th class='text-left'>Status Desc</th>
                            <th class='text-center'>Total</th>
                        </tr>
                    </thead>
                    <tbody>";

        foreach ($results as $data) {
            $html .= "
            <tr>
                <td class='text-left'>$data->min_datetime</td>
                <td class='text-left'>$data->max_datetime</td>
                <td class='text-center'>$data->service_code</td>
                <td class='text-center'>$data->service_name</td>
                <td class='text-center'>$data->spki_provider</td>
                <td class='text-left'>$data->status_code</td>
                <td class='text-left'>$data->status_description</td>
                <td class='text-center'><span class='modal-list-data-action label label-danger'><strong>$data->total</strong></span></td>
            </tr>";
        }

        $html .= "
            </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayMonitoringValidity() {
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT  trunc(CERT_CREATED) CERT_CREATED, PROVIDER, count(*) AS TOTAL FROM (
	
            SELECT DISTINCT 
                CASE WHEN  c.cert_issuer = 'T' THEN 'TRUSTGATE' ELSE 'DIGICERT' END AS PROVIDER,
                r.softcert_request_id AS REQUEST_ID,
                r.created_date AS REQUEST_DATE,
                c.orn AS EP_NO,
                c.prn AS IDENTIFICATION_NO,
                u.user_name AS NAME,
                c.valid_from AS VALID_FROM,
                c.valid_to AS VALID_TO,
                c.cert_serial_no AS CERT_SERIAL,
                c.created_date AS CERT_CREATED,
                 CASE 
                    WHEN TRUNC(c.valid_to) < TRUNC(SYSDATE) THEN 'EXPIRED'
                    ELSE 'ACTIVE'
                END AS STATUS_CERT
                FROM 
                SM_SOFTCERT_REQUEST r,
                PM_DIGI_CERT c,
                PM_USER u ,
                (SELECT c.orn,c.prn,c.valid_to,c.valid_from, count(*) 
                    FROM PM_DIGI_CERT c
                    WHERE to_char(c.created_date,'YYYY') > 2020
                    GROUP BY c.orn,c.prn,c.valid_to,c.valid_from 
                    HAVING count(*) > 1) dupp
                WHERE 
                r.softcert_request_id = c.softcert_request_id 
                AND r.user_id = u.user_id 
                AND c.orn = dupp.orn
                AND c.prn= dupp.prn 
                AND c.valid_from =dupp.valid_from
                AND c.valid_to =dupp.valid_to
                AND to_char(r.created_date,'YYYY') >= (to_char(sysdate,'YYYY')-1)
                AND to_char(c.valid_to,'YYYY') <= (to_char(sysdate,'YYYY')+1)
                AND NOT EXISTS (SELECT 1 FROM PM_DIGI_CERT dc WHERE dc.orn=c.orn AND  dc.prn = c.prn AND TO_CHAR(dc.valid_to,'YYYY') > TO_CHAR(sysdate,'YYYY') +1)
                ORDER BY c.created_date DESC
        ) g GROUP BY   trunc(CERT_CREATED), PROVIDER 
        ORDER BY 1 asc");

        //Render the data
        $html = "";

        $html .= "
            <div>
                <table class='table table-borderless table-striped table-vcenter'>
                    <thead>
                        <tr>
                            <th class='text-left'>Cert Created</th>
                            <th class='text-left'>Cert Provider</th>
                            <th class='text-center'>Total</th>
                        </tr>
                    </thead>
                    <tbody>";

        foreach ($results as $data) {
            if ($data->total > 0) {
                $data->total = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/spki/monitoringValidity/details/$data->cert_created/$data->provider'
                            data-title='List Validity Issue Cert Invalid' >{$data->total}</a>";
            }
            $html .= "
            <tr>
                <td class='text-left'>$data->cert_created</td>
                <td class='text-left'>$data->provider</td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
            </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayMonitoringValidityDetails($date,$provider) {
        $certDate = Carbon::parse($date)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT  * FROM (
	
            SELECT DISTINCT 
                CASE WHEN  c.cert_issuer = 'T' THEN 'TRUSTGATE' ELSE 'DIGICERT' END AS PROVIDER,
                r.softcert_request_id AS REQUEST_ID,
                r.created_date AS REQUEST_DATE,
                c.orn AS EP_NO,
                c.prn AS IDENTIFICATION_NO,
                u.user_name AS NAME,
                c.valid_from AS VALID_FROM,
                c.valid_to AS VALID_TO,
                c.cert_serial_no AS CERT_SERIAL,
                c.created_date AS CERT_CREATED,
                 CASE 
                    WHEN TRUNC(c.valid_to) < TRUNC(SYSDATE) THEN 'EXPIRED'
                    ELSE 'ACTIVE'
                END AS STATUS_CERT
                FROM 
                SM_SOFTCERT_REQUEST r,
                PM_DIGI_CERT c,
                PM_USER u ,
                (SELECT c.orn,c.prn,c.valid_to,c.valid_from, count(*) 
                    FROM PM_DIGI_CERT c
                    WHERE to_char(c.created_date,'YYYY') > 2020
                    GROUP BY c.orn,c.prn,c.valid_to,c.valid_from 
                    HAVING count(*) > 1) dupp
                WHERE 
                r.softcert_request_id = c.softcert_request_id 
                AND r.user_id = u.user_id 
                AND c.orn = dupp.orn
                AND c.prn= dupp.prn 
                AND c.valid_from =dupp.valid_from
                AND c.valid_to =dupp.valid_to
                AND to_char(r.created_date,'YYYY') >= (to_char(sysdate,'YYYY')-1)
                AND to_char(c.valid_to,'YYYY') <= (to_char(sysdate,'YYYY')+1)
        
                ORDER BY c.created_date DESC
        ) g where provider = '$provider' AND to_char(cert_created,'YYYY-MM-DD') = '$certDate'");

        //Render the data
        $html = "";

        $html .= "
                    <thead>
                        <tr>
                            <th class='text-left'>Provider</th>
                            <th class='text-left'>Request ID</th>
                            <th class='text-left'>Request Date</th>
                            <th class='text-left'>EP No</th>
                            <th class='text-left'>Identification No</th>
                            <th class='text-left'>Name</th>
                            <th class='text-left'>Valid From</th>
                            <th class='text-left'>Valid To</th>
                            <th class='text-left'>Cert Serial</th>
                            <th class='text-left'>Cert Created</th>
                            <th class='text-left'>Status Cert</th>
                        </tr>
                    </thead>
                    <tbody>"; 
        foreach ($results as $data) { 
            $html .= "
            <tr>
                <td class='text-left'>$data->provider</td>
                <td class='text-left'>$data->request_id</td>
                <td class='text-left'>$data->request_date</td>
                <td class='text-left'>$data->ep_no</td>
                <td class='text-left'>$data->identification_no</td>
                <td class='text-left'>$data->name</td>
                <td class='text-left'>$data->valid_from</td>
                <td class='text-left'>$data->valid_to</td>
                <td class='text-left'>$data->cert_serial</td>
                <td class='text-left'>$data->cert_created</td>
                <td class='text-left'>$data->status_cert</td>
            </tr>";
        }

        $html .= "
            </tbody>";

        return $html;
    }
}
