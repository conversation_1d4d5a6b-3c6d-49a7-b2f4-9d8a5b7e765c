<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\ProdSupport;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;

class MigrateDataPatchingExcel {

    public static function run() {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        $collectData = self::getListDataFromExcel();
//        dd($collectData);
        if ($collectData->count() > 0) {
            self::migrateDataIntoDataPatchTable($collectData);
        }
        MigrateUtils::logDump('Total : ' . $collectData->count());
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    protected static function getListDataFromExcel() {
        $collectData = collect();
        $filename = '/app/Migrate/ProdSupport/data/data_patching_2020.csv';
        Excel::load($filename, function($reader) use (&$collectData) {
            $reader->each(function($row) use (&$collectData) {
                if ($row->datetime != null) {
                    $collectData->push($row);
                }
            });
        });

        return $collectData;
    }

    protected static function migrateDataIntoDataPatchTable($collectData) {
        foreach ($collectData as $data) {
            $dateTimePortingStr = trim($data->datetime);
//            dd($dateTimePortingStr);
            $dateCreatedStr = trim($data->created_date);
            $dateChangeddStr = trim($data->created_date);
            //dump(Carbon::now()->format('d/m/Y  H:i:s'));
//            dump($dateTimePortingStr);
            //Data in excel: 02/01/2020  13:00:00
            $dateTimePorting = Carbon::createFromFormat('Y-m-d  H:i:s', $dateTimePortingStr);
//            dd($dateTimePorting);
            $dateCreatedat = Carbon::createFromFormat('Y-m-d  H:i:s', $dateCreatedStr);
            $dateChangedat = Carbon::createFromFormat('Y-m-d  H:i:s', $dateChangeddStr);

            //get Parent Data Or insert if not found
            $dataFixPort = self::getDetailDataFixByDateTimePorting($dateTimePorting);
            $seq = self::getSeqDataFixByDateTimePorting($dateTimePorting);

            if ($dataFixPort) {
                $dataFixId = $dataFixPort->data_fix_id;
                $dataFixDetail = self::getDetailDataFixDetail($dataFixId, $data->bill);
                if ($dataFixDetail == null) {
                    //Insert
                    $dataFixPort = DB::connection('mysql_ep_prod_support')
                            ->table('ps_data_fix_dtl')
                            ->insert(
                            [
                                "data_fix_id" => $dataFixId,
                                "bill_seq" => $data->bill,
                                "datetime_porting" => $dateTimePorting->format('Y-m-d  H:i:s'),
                                "crm_no" => $data->crm_no,
                                "module" => $data->module,
                                "problem_description" => $data->problem,
                                "requester_type" => $data->req_type,
                                "requester_name" => $data->requester,
                                "requester_code" => $data->requester,
                                "problem_type" => $data->problem_type,
                                "endorsement_by" => $data->endorsement,
                                "created_by" => $data->created_by,
                                "created_date" => $dateCreatedat->format('Y-m-d  H:i:s'),
                                "changed_by" => $data->changed_by,
                                "changed_date" => $dateChangedat->format('Y-m-d  H:i:s')
                            ]
                    );
                } 
                else {
                    dump($data->bill . ' - ' . $data->crm_no . ' sudah wujud!');
                }
            } else {

                //Insert
                $dataFixPort = DB::connection('mysql_ep_prod_support')
                        ->table('ps_data_fix')
                        ->insert(
                        [
                            "name" => $data->name,
                            "description" => $data->description,
                            "status" => "Closed",
                            "datetime_porting" => $dateTimePorting->format('Y-m-d  H:i:s'),
                            "porting_seq" => $data->seq,
                            "created_date" => $dateCreatedat->format('Y-m-d  H:i:s'),
                            "closed_by" => "Migrater",
                            "created_by" => "Migrater",
                            "changed_by" => "Migrater",
                            "changed_date" => $dateChangedat->format('Y-m-d  H:i:s')
                        ]
                );
//                dd($dataFixPort);
            }
        }
    }

    /**
     * 
     * @param type Carbon $dateTimePorting
     * @return type
     */
    protected static function getDetailDataFixByDateTimePorting(Carbon $dateTimePorting) {
        $dataFixPort = DB::connection('mysql_ep_prod_support')
                ->table('ps_data_fix')
                ->where('datetime_porting', $dateTimePorting->format('Y-m-d  H:i:s'))
                ->first();

        return $dataFixPort;
    }

    /**
     * 
     * @param type Carbon $dateTimePorting
     * @return type
     */
    protected static function getSeqDataFixByDateTimePorting(Carbon $datePorting) {
        $dataFixPortSeq = DB::connection('mysql_ep_prod_support')
                ->table('ps_data_fix')
                ->whereDate('datetime_porting', $datePorting->format('Y-m-d'))
                ->orderBy('porting_seq', 'desc')
                ->first();
        $seq = 1;
        if ($dataFixPortSeq != null) {
            $seq = $dataFixPortSeq->porting_seq;
        }

        return $seq;
    }

    /**
     * 
     * @param type Carbon $dateTimePorting
     * @return type
     */
    protected static function getDetailDataFixDetail($dataFixId, $billNo) {
        $dataFixDetail = DB::connection('mysql_ep_prod_support')
                ->table('ps_data_fix_dtl')
                ->where('data_fix_id', $dataFixId)
                ->where('bill_seq', $billNo)
                ->first();

        return $dataFixDetail;
    }

}
