.popover {
  max-width: 500px;
}

#page-content {
  background: #1b213b;
}


div.log-header {
  background: -webkit-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: -o-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: -moz-linear-gradient(-180deg, #8b5f82, #0e0f0f);
  background: linear-gradient(-180deg, #8b5f82, #0e0f0f);
  display: inline-block;
  box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.35);
  overflow: hidden;
  border-radius: 5px;
  border-style: none;
  width: 100%;
  color: white;
  padding: 5px 10px;
}

div.log-header-title {
  padding: 5px 10px;
  text-align: left;
  left: 5px;
}

div.log-header-title span {
  font-size: 20px;
  font-weight: bold;
}

div.log-header-menu {
  padding: 20px 5px;
  text-align: right;
  right: 10px;
}

div.log-header-menu a {
  text-decoration: none;
  color: white;
  font-size: 12px;
  font-weight: bold;
}

table.table,
table.table tbody tr {
  background: #262d47;
  color: #fff;
  border-style: none;
}

table.table,
table.table tbody tr:hover {
  background: rgb(2, 42, 73);
  color: #fff;
  border-style: none;
}

div.dataTables_length,
div.dataTables_filter {
  background: transparent;
  color:#262d47;
}

div.dataTables_info,
div.dataTables_paginate {
  color:#fff;
  font-weight: bold;
  padding: 1px 3px 3px 3px;
  border-radius: 4px;
}

table thead {
  vertical-align: middle;
  background: rgb(71, 63, 161);
  color: #fff;
}

div#log-bg-table {
  background: #3448a2;
  box-shadow: 0 0 15px 1px rgb(227, 235, 230);
  border-style: none;
  border-radius: 5px;
  padding: 3px 20px 3px 20px;
}

.log-trace-icon {
  border: 5px solid rgb(109, 110, 110);
  border-radius: 5px;
  padding: 5px 5px 5px 5px;
  box-shadow: 0 0 15px 1px rgb(6, 197, 85);
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  background: -webkit-linear-gradient(-90deg, #2de47f, #84e04f);
  background: -o-linear-gradient(-90deg, #2de47f, #84e04f);
  background: -moz-linear-gradient(-90deg, #2de47f, #84e04f);
  background: linear-gradient(-90deg, #2de47f, #84e04f);
}

.log-search-panel {
  background: #262d47;
  box-shadow: 0 0 15px 1px rgb(227, 235, 230);
  border-style: none;
  border-radius: 5px;
  color: #fff;
}

table#log_table tbody tr:hover {
  background: #3e3e3f;
}

div.modal-content,
div.modal-footer {
  background: #1b213b;
}

.modal-title {
  color: #fff;
}

div button.btn,
a.btn {
  background: greenyellow;
  color: #000;
}

div button.btn:hover,
a.btn:hover {
  background: black;
  color: #fff;
}

.log_word_break {
  width: 30%;
  word-break: break-all;
}
.log_text_center {
  text-align: center;
}

div.modal-contents {
  background: #3448a2;
  color: #fff;
}

/* div.modal-contents:hover {
  text-decoration: none;
  background-color: rgb(90, 169, 230);
  color: rgb(22, 13, 13);
} */

div.modal-contents a {
  text-decoration: none;
  color: rgb(22, 13, 13);
  font-size: 14px;
  font-weight: bold;
}

div.modal-contents a:hover {
  color: rgb(237, 8, 8);
}

form {
  background: #1b213b;
  color: #333;
  padding: 20px;
  border-radius: 8px;
}

form input {
  padding: 10px;
  margin-bottom: 10px;
  width: 10%;
  box-sizing: border-box;
}

form label {
  display: block;
  margin-bottom: 5px;
}

form button {
  background: #3448a2;
  color: #fff; 
  padding: 10px 20px;
  border: none;
  border-radius: 5px; 
  cursor: pointer;
}

form button:hover {
  background: #1a2d5a;
}

.panel-title {
  margin-bottom: 5px;
}


