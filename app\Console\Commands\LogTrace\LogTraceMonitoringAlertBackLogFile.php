<?php

namespace App\Console\Commands\LogTrace;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\LogTraceService;
use App\Model\Notify\NotifyModel;

class LogTraceMonitoringAlertBackLogFile extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'LogTraceMonitoringAlertBackLogFile';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To monitor and send alert by whatapp notification if has backlog files log';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        MigrateUtils::logDump(__METHOD__ . ' starting ..', ['Date' => Carbon::now()]);
        $logtrace = new LogTraceService();
        try {
            $dateExecution = Carbon::now()->format('Y-m-d H:i:s');
            $total = $logtrace->getTotalStuckLog(); 

            if($total > 50){
                $msgAlertSummary = "
*[ALERT] Monitoring Logtrace Backlog File*
*As checking on $dateExecution*
  Total files pending to process : $total

Kindly do manual checking";
                MigrateUtils::logDump(__METHOD__.' '.$msgAlertSummary);
                $this->saveNotify('LOGTRACE',$msgAlertSummary);
            }

            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            $logtrace->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }

    
    protected function saveNotify($receiver,$msg){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $totNotify = NotifyModel::where('message',$msg)->count();
        if($totNotify > 0){
            MigrateUtils::logDump($clsInfo.'Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring daily Receipt eP';
            $nty->save();
        }
        
    }
}
