@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <!-- END Search Form -->
@endsection

@section('content')
    <style>
        .dropzone {
            margin-bottom: 20px;
        }

        .dropzone .dz-message {
            margin: 5px 0;
        }

        .dropzone .dz-preview {
            display: block;
        }

        .dropzone .dz-preview .dz-image {
            display: none;
        }
    </style>

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i><strong>POMS Talkdesk</strong> Data Upload <br>
                <small class="text-info">Please only upload a <span><strong>.CSV</strong></span> file, and kindly ensure that
                    you retain its original file name.</small>
            </h1>
        </div>

    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="block-title">
                <h2>Upload <strong>Talkdesk</strong> Call Data File</h2>

            </div>
            <div class="form-group clearfix">
                <label class="col-md-1 control-label" for="type">Call Data </label>
                <div class="col-md-2">
                    <select id="type" name="type" class="form-control">
                        <option value="daily" {{ $byType == 'daily' ? 'selected' : '' }}>Daily</option>
                        <option value="monthly" {{ $byType == 'monthly' ? 'selected' : '' }}>Monthly</option>
                    </select>
                </div>
            </div>
            <form id="form-transfer-file" class="dropzone dz-clickable">
                {{-- {{ csrf_field() }} --}}
                <input name="_method" id="_method" type="hidden" value="POST">
                <div class="dz-default dz-message"><span>Drop file or click here to upload</span></div>
            </form>



            <div class="row">
                <div class="col-lg-12">
                    <div class="widget">
                        <div class="widget-extra themed-background-dark">
                            <h5 class="widget-content-light">
                                <strong>Talkdesk POMS</strong> - Uploaded Data
                            </h5>
                        </div>
                        <div id="dash_monitoring" class="widget-extra-full">
                            <div class="table-responsive">
                                <table id="basic-datatable"
                                    class='table table-borderless table-vcenter table-striped table-bordered'>
                                    <thead>
                                        <tr>
                                            <th>Date Call</th>
                                            <th>ACD Call Offer</th>
                                            <th>ACD Call Handle</th>
                                            <th>Call WI Lvl</th>
                                            <th>Call Abandon Short</th>
                                            <th>Call Abandon Long</th>
                                            <th>Abandon Percentage</th>
                                            <th>Answer Percentage</th>
                                            <th>Service Level</th>
                                            <th>Insert Datetime</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($data as $row)
                                            <tr>
                                                <td>{{ date('d/m/Y', strtotime($row->date_call)) }}</td>
                                                <td>{{ $row->acd_call_offer }}</td>
                                                <td>{{ $row->acd_call_handle }}</td>
                                                <td>{{ $row->call_WI_lvl }}</td>
                                                <td>{{ $row->call_abandon_short }}</td>
                                                <td>{{ $row->call_abandon_long }}</td>
                                                <td>{{ $row->abandon_percentage }}</td>
                                                <td>{{ $row->answer_percentage }}</td>
                                                <td>{{ $row->service_level }}</td>
                                                <td>{{ date('d/m/Y h:i A', strtotime($row->mitel_insert_data_datetime)) }}
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog"
            aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div>
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="messageModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
            aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="exampleModalLabel">
                            <i class="fa fa-info-circle"></i> Talkdesk - Upload Status
                        </h5>
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                    <div class="modal-body" id="messageModalBody">
                        <!-- Message will be inserted here -->
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>

        @include('_shared._modalListLogAction')

        <!-- END Content -->
    @endsection

    @section('jsprivate')
        <!-- Load and execute javascript code used only in this page -->
        <script src="/js/pages/modalListActionLogDatatable.js"></script>
        <script>
            $(function() {
                ModalListActionLogDatatable.init();
            });
        </script>

        <script>
            var APP_URL = {!! json_encode(url('/')) !!}

            App.datatables();
            /* Initialize Datatables */
            var tableListData = $('#basic-datatable').DataTable({
                columnDefs: [{
                    orderable: false, // Set orderable to false for all columns
                    targets: '_all' // '_all' means all columns are targeted
                }],
                order: [], // Disable initial sorting on all columns
                pageLength: 10,
                lengthMenu: [
                    [10, 20, 30, -1],
                    [10, 20, 30, 'All']
                ]
            });

            $('#type').change(function(e) {
                e.preventDefault();
                $('<form>', {
                    method: 'POST',
                    action: "{{ url('/dashboard/crm/cs/talkdesk') }}"
                }).append(
                    $('<input>', {
                        type: 'hidden',
                        name: '_token',
                        value: "{{ csrf_token() }}"
                    }),
                    $('<input>', {
                        type: 'hidden',
                        name: 'type',
                        value: $('#type').val()
                    }),
                    $('<button>', {
                        type: 'submit',
                        class: 'btn btn-primary',
                        text: 'Submit'
                    })
                ).appendTo('body').submit();
            });

            function showMessageModal(message, isSuccess, callback) {
                $('#messageModalBody').text(message);
                if (isSuccess) {
                    $('#messageModalBody').removeClass('text-danger').addClass('text-success');
                } else {
                    $('#messageModalBody').removeClass('text-success').addClass('text-danger');
                }
                $('#messageModal').modal('show');
                $('#messageModal').on('hidden.bs.modal', function(e) {
                    if (callback) callback();
                });
            }

            Dropzone.options.formTransferFile = {
                url: "{{ url('/dashboard/crm/cs/talkdesk/upload') }}",
                paramName: "uploadFile", // The name that will be used to transfer the file
                maxFilesize: 20, // MB
                maxFiles: 100,
                acceptedFiles: '.csv',
                accept: function(file, done) {
                    console.log('File Name: ', file.name);
                    done();
                },
                headers: {
                    'X-CSRF-TOKEN': "{{ csrf_token() }}"
                },
                // ...
                sending: function(file, xhr, formData) {
                    var type = $('#type').val();
                    formData.append('type', type);
                },
                success: function(file, response) {
                    if (response && response.status_upload === 'success') {
                        showMessageModal('Call Data successfully uploaded.', true, function() {
                            location.reload();
                        });
                    } else {
                        showMessageModal(response.error_message || 'Please make sure you upload a correct file.', false,
                            function() {
                                location.reload();
                            });
                    }
                }
            };
        </script>
        <script>
            // $('#page-container').removeAttr('class');
        </script>
        <!-- Load and execute javascript code used only in this page -->
    @endsection
