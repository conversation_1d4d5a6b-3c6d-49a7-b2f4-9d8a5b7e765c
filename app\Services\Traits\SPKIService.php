<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Guzzle;
use GuzzleHttp\Client;
use Log;

trait SPKIService {

    protected function trustgateService($service, $icNumber, $epNumber, $selectedQuestion, $email, $pin = null, $question = null, $answer = null, $data = null) {

        try {

            $url = env("SPKI_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-spki-middleware");

            Log::info(__CLASS__ . "::" . __FUNCTION__ . " > START");
            $startTime = microtime(true);

            try {

                if ($service === 'ChallengeQuestion') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/challengequestion?ic_number="
                                    . $icNumber . "&ep_number=$epNumber&selected_question=$selectedQuestion");
                } else if ($service === 'ResetPIN') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/resetpin?icNumber=$icNumber&epNumber=$epNumber&email=$email");
                } else if ($service === 'ChangePin') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/changepin?icNumber=$icNumber&epNumber=$epNumber");
                } else if ($service === 'RevokeCert') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/revoke?icNumber=$icNumber&epNumber=$epNumber");
                } else if ($service === 'UpdateChallengeQuestion') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/updateChallengeQuestion?icNumber=$icNumber&epNumber=$epNumber");
                } else if ($service === 'SignIn') {
                    $response = Guzzle::get($url . "/spki/signing/trustgate/signin?ic_number=$icNumber&ep_number=$epNumber&pin=$pin&question=$question&answer=$answer&data=$data");
                }

                $resultResp = json_decode($response->getBody(), true);

                $endTime = microtime(true);
                $duration = $endTime - $startTime;
                Log::info(__CLASS__ . "::" . __FUNCTION__ . " > COMPLETE executed in " . $duration . " seconds");

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                Log::info(__CLASS__ . "::" . __FUNCTION__ . " >  caught an error: " . $ex->getMessage());
                return array(
                    "status" => "Failed",
                    "result" => $ex->getMessage());
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            Log::info(__CLASS__ . "::" . __FUNCTION__ . " >  caught an error: " . $ex->getMessage());
            return array(
                "status" => "Failed",
                "result" => 'Failed to connect ' . $ex->getMessage());
        }
    }

    protected function digicertService($service, $icNumber, $epNumber = null, $email = null, $pin = null, $question = null, $answer = null, $data = null) {
        try {

            $url = env("SPKI_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-spki-middleware");

            Log::info(__CLASS__ . "::" . __FUNCTION__ . " > START");
            $startTime = microtime(true);

            try {

                if ($service === 'GetChallengeQuestion') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/getchallengequestion?icNumber=$icNumber");
                } else if ($service === 'UpdateChallengeQuestion') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/updateChallengeQuestion?icNumber=$icNumber&epNumber=$epNumber");
                } else if ($service === 'ChangePin') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/changePin?icNumber=$icNumber&epNumber=$epNumber");
                } else if ($service === 'ResetPin') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/resetPin?icNumber=$icNumber&epNumber=$epNumber&email=$email");
                } else if ($service === 'SignIn') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/signin?ic_number=$icNumber&ep_number=$epNumber&pin=$pin&question=$question&answer=$answer&data=$data");
                } else if ($service === 'RevokeCert') {
                    $response = Guzzle::get($url . "/spki/signing/digicert/revoke?icNumber=$icNumber&epNumber=$epNumber");
                }

                $resultResp = json_decode($response->getBody(), true);

                $endTime = microtime(true);
                $duration = $endTime - $startTime;
                Log::info(__CLASS__ . "::" . __FUNCTION__ . " > COMPLETE executed in " . $duration . " seconds");

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                Log::info(__CLASS__ . "::" . __FUNCTION__ . " >  caught an error: " . $ex->getMessage());
                return array(
                    "status" => "Failed",
                    "result" => $ex->getMessage());
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            Log::info(__CLASS__ . "::" . __FUNCTION__ . " >  caught an error: " . $ex->getMessage());
            return array(
                "status" => "Failed",
                "result" => 'Failed to connect ' . $ex->getMessage());
        }
    }

}
