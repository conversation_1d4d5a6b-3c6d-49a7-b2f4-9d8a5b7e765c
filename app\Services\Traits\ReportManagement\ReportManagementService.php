<?php

namespace App\Services\Traits\ReportManagement;

use Carbon\Carbon;
use DB;

trait ReportManagementService
{

    protected function getListReport()
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_request_report')
            ->get();
    }

    protected function getReportById($id)
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_request_report')
            ->where('report_id', $id)
            ->first();
    }

    protected function getScriptById($id)
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_report_script')
            ->where('report_id', $id)
            ->get();
    }

    protected function getScriptByScriptId($id)
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_report_script')
            ->where('script_id', $id)
            ->get();
    }

    protected function getResultById($id)
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_report_result')
            ->where('report_id', $id)
            ->get();
    }

    protected function getResultByResultId($id)
    {
        return DB::connection('mysql_ep_report')
            ->table('ep_report_result')
            ->where('result_id', $id)
            ->get();
    }



    protected function getLatestTestEp()
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select login_date, login_id 
                            from pm_login_history ph, pm_user pu
                            where pu.user_id = ph.user_id
                            and login_id = '357-02006140-1'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleSM($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("Select cps.company_name , business_type_desc ,cps.reg_no , mof_no,origin_country_ssm, A.SUPPLIER_ID , l1.category_l1_code|| l2.category_l2_code|| l3.category_l3_code kod_bidang,
            C.appl_id , decode(a.RECORD_STATUS ,'9', 'Deleted','0', 'Inactive', '1', 'Active','5','In-Progress') AS RECORD_STATUS 
            --l1.category_l1_code l1_code, l1.category_name l1_name, l2.category_l2_code l2_code, l2.category_name l2_name,l3.category_l3_code l3_code, 
            --l3.category_name l3_name,l1.record_status l1_stat, l2.record_status l2_stat,l3.record_status l3_stat
            from sm_supplier_category C, sm_supplier A, sm_appl B,pm_category_l1 L1, pm_category_l2 L2, pm_category_l3 L3 , cptpp_supplier cps
            where C.appl_id = B.appl_id
            and B.SUPPLIER_ID = A.SUPPLIER_ID
            and A.LATEST_APPL_ID = C.APPL_ID
            and C.RECORD_STATUS = 1
            and C.REV_NO in (select max(rev_no) from SM_supplier_category where appl_id = C.APPL_ID and CATEGORY_L1_ID = C.CATEGORY_L1_ID
                            and CATEGORY_L2_ID = C.CATEGORY_L2_ID and CATEGORY_L3_ID = C.CATEGORY_L3_ID )
            and C.CATEGORY_L1_ID = L1.CATEGORY_L1_ID
            and C.CATEGORY_L2_ID = L2.CATEGORY_L2_ID
            and L2.CATEGORY_L1_ID = L1.CATEGORY_L1_ID
            and C.CATEGORY_L3_ID = L3.CATEGORY_L3_ID
            and L3.CATEGORY_L2_ID = L2.CATEGORY_L2_ID
            and C.appl_id is not null
            and cps.SUPPLIER_ID = A.SUPPLIER_ID
            and exists (select * from cptpp_bidang cpb where cpb.KOD_BIDANG = l1.category_l1_code|| l2.category_l2_code|| l3.category_l3_code )
            AND to_char(a.CHANGED_DATE,'yyyy-mm-dd') >= ? AND to_char(a.CHANGED_DATE,'yyyy-mm-dd') <= ? 
            --and M.mof_no = '357-02095942' -- 357-02146171
            --and upper(L1.category_name) like  upper('%Perabot%') 
            --and M.SUPPLIER_ID = 108765
            --and L1.CATEGORY_L1_CODE||L2.CATEGORY_L2_CODE||L3.CATEGORY_L3_CODE = '120199'
            order by 3 ASC", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModulePP($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT min_code, min_name, ptj_code, ptj_name, plan_year, plan_no, status_name,tajuk_pelan, perihal_plan, kategori_jenis_perolehan, amaun_anggaran, pp_changed_date
                FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
                     PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
                     PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
                     PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4, pp_item
                WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
                  AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
                  AND ptj.parent_org_profile_id = ptj2.org_profile_id
                  AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
                  AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
                  AND ptj2.parent_org_profile_id = ptj3.org_profile_id
                  AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
                  AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
                  AND ptj3.parent_org_profile_id = ptj4.org_profile_id
                  AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
                  AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
                  AND ptjv.RECORD_STATUS = 1
                  AND ptjv2.RECORD_STATUS = 1
                  AND ptjv3.RECORD_STATUS = 1
                  AND ptjv4.RECORD_STATUS = 1
                  AND pp_item.ptj_code = ptjv.ORG_CODE
                  AND ((pp_item.PROCUREMENT_CAT_ID = 707 AND pp_item.amaun_anggaran > 8850000) 
                       OR (pp_item.PROCUREMENT_CAT_ID IN (709,708) AND pp_item.amaun_anggaran > 11800000)
                       OR (pp_item.PROCUREMENT_CAT_ID = 710 AND pp_item.amaun_anggaran > 371700000))
                --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
                --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
                --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
                AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,
                955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
                AND pp_item.ptj_code NOT in ('10010000') -- ptj istana negara
                AND (UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%khazanah%')  
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%seni%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%warisan%')
                --AND UPPER(pp_item.tajuk_pelan) LIKE UPPER ('%agama%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%penyelidikan%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%kebangsaan%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%kabotaj%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%cpc%')
                AND UPPER(pp_item.tajuk_pelan) NOT LIKE UPPER ('%plasma%'))
                AND to_char(pp_changed_date,'yyyy-mm-dd') >= ? AND to_char(pp_changed_date) <= ? ", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleQT($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT kod_kementerian, kementerian, kod_ptj, ptj,qt_no, tajuk_qt, jenis_perolehan, status, stage, amount, qt_changed_date, action_date, closing_date 
            FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
                 PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
                 PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
                 PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4, qt_cptpp
            WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
              AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
              AND ptj.parent_org_profile_id = ptj2.org_profile_id
              AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
              AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
              AND ptj2.parent_org_profile_id = ptj3.org_profile_id
              AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
              AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
              AND ptj3.parent_org_profile_id = ptj4.org_profile_id
              AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
              AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
              AND ptjv.RECORD_STATUS = 1
              AND ptjv2.RECORD_STATUS = 1
              AND ptjv3.RECORD_STATUS = 1
              AND ptjv4.RECORD_STATUS = 1
              AND qt_cptpp.kod_ptj = ptjv.ORG_CODE
              AND UPPER (qt_cptpp.tajuk_qt) NOT LIKE '%FTA%CPTPP%' 
            --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
            --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
            --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
            AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,
            955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
            AND qt_cptpp.kod_ptj NOT in ('10010000') -- ptj istana negara
            AND (UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%khazanah%')  
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%seni%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%warisan%')
            --AND UPPER(pp_item.tajuk_pelan) LIKE UPPER ('%agama%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%penyelidikan%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%kebangsaan%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%kabotaj%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%cpc%')
            AND UPPER(qt_cptpp.tajuk_qt) NOT LIKE UPPER ('%plasma%'))
            AND to_char(qt_cptpp.qt_changed_date,'yyyy-mm-dd') >= ? AND to_char(qt_cptpp.qt_changed_date,'yyyy-mm-dd') <= ? ", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleQtIklan($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT distinct
            ptj_org,ptj_name, qt.qt_id, qt.QT_NO,  qt.PUBLISH_PERIOD , qt.QT_TITLE,
            CASE WHEN psd.STATUS_NAME IS NULL THEN 'Belum Diserah' ELSE psd.STATUS_NAME END AS STATUS_NAME
        FROM (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
              ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
               FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
               PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
               PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
               PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
               WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
               AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
               AND ptj.parent_org_profile_id = ptj2.org_profile_id
               AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
               AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
               AND ptj2.parent_org_profile_id = ptj3.org_profile_id
               AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
               AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
               AND ptj3.parent_org_profile_id = ptj4.org_profile_id
               AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
               AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
               AND ptjv.RECORD_STATUS = 1
               AND ptjv2.RECORD_STATUS = 1
               AND ptjv3.RECORD_STATUS = 1
               AND ptjv4.RECORD_STATUS = 1
               AND ptjv.org_code not in ('10010000')
               --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
               --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
               --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
               AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
               ) byptj, SC_QT qt
        LEFT JOIN 
            SC_WORKFLOW_STATUS sws ON sws.DOC_ID = qt.QT_ID AND sws.IS_CURRENT = 1 AND sws.DOC_TYPE = 'QT'
        LEFT JOIN 
            PM_STATUS_DESC psd ON sws.STATUS_ID = psd.STATUS_ID AND psd.LANGUAGE_CODE = 'ms'
        WHERE qt.OWNER_ORG_PROFILE_ID = byptj.ptj_id
        AND UPPER(qt.QT_TITLE) LIKE '%CPTPP%'
        AND (UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%khazanah%')  
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%seni%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%warisan%')
        --AND UPPER(qt.QT_TITLE) LIKE UPPER ('%agama%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%penyelidikan%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%kebangsaan%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%kabotaj%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%cpc%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%plasma%'))
        AND to_char(qt.changed_date,'yyyy-mm-dd') >= ? AND to_char(qt.changed_date,'yyyy-mm-dd') <= ?
        AND qt.PUBLISH_PERIOD < 40
        ORDER BY qt.QT_NO desc", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleQtKodbidang($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT distinct
            ptj_org,ptj_name, qt.QT_NO , pcl.CATEGORY_L1_CODE || pcl2.CATEGORY_L2_CODE || pcl3.CATEGORY_L3_CODE AS code  ,  qt.QT_TITLE, 
            CASE WHEN psd.STATUS_NAME IS NULL THEN 'Belum Diserah' ELSE psd.STATUS_NAME END AS STATUS_NAME
        FROM 
            SC_QT_CATEGORY sqc, PM_CATEGORY_L1 pcl, PM_CATEGORY_L2 pcl2, PM_CATEGORY_L3 pcl3, (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
              ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
               FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
               PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
               PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
               PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
               WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
               AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
               AND ptj.parent_org_profile_id = ptj2.org_profile_id
               AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
               AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
               AND ptj2.parent_org_profile_id = ptj3.org_profile_id
               AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
               AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
               AND ptj3.parent_org_profile_id = ptj4.org_profile_id
               AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
               AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
               AND ptjv.RECORD_STATUS = 1
               AND ptjv2.RECORD_STATUS = 1
               AND ptjv3.RECORD_STATUS = 1
               AND ptjv4.RECORD_STATUS = 1
               AND ptjv.ORG_CODE NOT in ('10010000') 
               --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
               --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
               --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
               --AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
               ) byptj, SC_QT qt
        LEFT JOIN 
            SC_WORKFLOW_STATUS sws ON sws.DOC_ID = qt.QT_ID AND sws.IS_CURRENT = 1 AND sws.DOC_TYPE = 'QT'
        LEFT JOIN 
            PM_STATUS_DESC psd ON sws.STATUS_ID = psd.STATUS_ID AND psd.LANGUAGE_CODE = 'ms'
        WHERE pcl.CATEGORY_L1_ID = sqc.CATEGORY_L1_ID
        AND pcl2.CATEGORY_L2_ID = sqc.CATEGORY_L2_ID
        AND pcl3.CATEGORY_L3_ID = sqc.CATEGORY_L3_ID
        AND qt.QT_ID = sqc.QT_ID
        AND qt.OWNER_ORG_PROFILE_ID = byptj.ptj_id
        AND qt.QT_TITLE LIKE '%CPTPP%'
        AND (UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%khazanah%')  
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%seni%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%warisan%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%penyelidikan%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%kebangsaan%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%kabotaj%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%cpc%')
        AND UPPER(qt.QT_TITLE) NOT LIKE UPPER ('%plasma%'))
        AND to_char(qt.changed_date,'yyyy-mm-dd') >= ? AND to_char(qt.changed_date,'yyyy-mm-dd') <= ?
        --AND qt.qt_no ='QT230000000024321'
            AND (pcl.CATEGORY_L1_CODE || pcl2.CATEGORY_L2_CODE || pcl3.CATEGORY_L3_CODE) IN (SELECT kod_bidang FROM cptpp_bidang)
        ORDER BY qt.QT_NO desc
        ", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleQtLoa($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT ptj_code, ptj_name, qt_no, qt_title, day_difference, publish_date, closing_date
            FROM (
                SELECT 
                    sc.qt_no, 
                    CASE
                        WHEN COUNT(DISTINCT sws.CREATED_DATE) = 1 THEN 1111
                        ELSE EXTRACT(DAY FROM (MAX(CAST(sws.CREATED_DATE AS TIMESTAMP)) - MIN(CAST(sws.CREATED_DATE AS TIMESTAMP))))
                    END AS day_difference, 
                    min_name, 
                    ptj_id, 
                    ptj_code, 
                    ptj_name, 
                    ptj_org,
                    DECODE (sc.procurement_mode_id,
                        185, 'Quotation',
                        186, 'Tender'
                    ) as Jenis_Perolehan,  
                    sc.qt_title, 
                    sc.publish_date, 
                    sc.closing_date
                FROM
                    (
                        SELECT 
                            ptjv.ORG_PROFILE_ID ptj_id , 
                            ptjd.CODE_NAME ptj_code, 
                            ptjv.ORG_NAME ptj_name,
                            ptjv.ORG_CODE ptj_org,  
                            ptjv2.ORG_PROFILE_ID, 
                            ptjd2.CODE_NAME , 
                            ptjv2.ORG_CODE , 
                            ptjv2.ORG_NAME, 
                            ptjv3.ORG_PROFILE_ID, 
                            ptjd3.CODE_NAME , 
                            ptjv3.ORG_CODE , 
                            ptjv3.ORG_NAME,
                            ptjv4.ORG_PROFILE_ID, 
                            ptjd4.CODE_NAME , 
                            ptjv4.ORG_CODE , 
                            ptjv4.ORG_NAME min_name
                        FROM 
                            PM_ORG_PROFILE ptj, 
                            PM_ORG_VALIDITY ptjv, 
                            PM_PARAMETER_DESC ptjd,
                            PM_ORG_PROFILE ptj2, 
                            PM_ORG_VALIDITY ptjv2, 
                            PM_PARAMETER_DESC ptjd2, 
                            PM_ORG_PROFILE ptj3, 
                            PM_ORG_VALIDITY ptjv3, 
                            PM_PARAMETER_DESC ptjd3, 
                            PM_ORG_PROFILE ptj4, 
                            PM_ORG_VALIDITY ptjv4, 
                            PM_PARAMETER_DESC ptjd4
                        WHERE 
                            ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
                            AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
                            AND ptj.parent_org_profile_id = ptj2.org_profile_id
                            AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
                            AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
                            AND ptj2.parent_org_profile_id = ptj3.org_profile_id
                            AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
                            AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
                            AND ptj3.parent_org_profile_id = ptj4.org_profile_id
                            AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
                            AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
                            AND ptjv.RECORD_STATUS = 1
                            AND ptjv2.RECORD_STATUS = 1
                            AND ptjv3.RECORD_STATUS = 1
                            AND ptjv4.RECORD_STATUS = 1
                            AND ptjv.org_code NOT IN ('10010000')
                            AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035, 955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
                    ) byptj, 
                    sc_qt sc ,
                    SC_WORKFLOW_STATUS sws
                WHERE 
                    sc.QT_ID = sws.DOC_ID
                    AND sc.OWNER_ORG_PROFILE_ID = byptj.ptj_id
                    AND sc.qt_no LIKE ('QT%')
                    AND UPPER (sc.qt_title) LIKE '%FTA%CPTPP%'
                    AND (
                        UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%khazanah%')  
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%seni%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%warisan%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%penyelidikan%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%kebangsaan%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%kabotaj%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%cpc%')
                        AND UPPER(sc.QT_TITLE) NOT LIKE UPPER ('%plasma%')
                    )
                    AND sws.STATUS_ID IN (62213, 62201)
                    AND to_char(sc.CHANGED_DATE,'yyyy-mm-dd') >= ? AND to_char(sc.changed_date,'yyyy-mm-dd') <= ?
                GROUP BY
                    sc.qt_no,
                    sc.procurement_mode_id,
                    sc.qt_title,
                    sc.publish_date,
                    sc.closing_date, 
                    ptj_id, 
                    ptj_code, 
                    ptj_name, 
                    ptj_org, 
                    min_name
            ) filtered_results
            WHERE day_difference < 10", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleQtTempasal($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT  MAX (d.org_code) AS Kod_PTJ,  MAX (d.org_name) AS PTJ,sq.qt_no, sq.qt_title ,pm.status_name,  DECODE (pm.status_id,
            60022, '3. Closed',
            60023, '3. Closed',
            60010, '3. Closed',
            60011, '3. Closed',
            60026, '1. Before Publish',
            60004, '1. Before Publish',
            60013, '3. Closed',
            60003, '1. Before Publish',
            60014, '4. Completed',
            60009, '2. Published',
            60028, '1. Before Publish',
            60033, '1. Before Publish',
            60024, '1. Before Publish',
            60008, '2. Published',
            60016, '3. Closed',
            60005, '1. Before Publish',
            60025, '1. Before Publish',
            60012, '3. Closed'
           ) AS Stage, s.CHANGED_DATE AS Action_Date, sq.publish_date, sq.closing_date
--DECODE (sq.procurement_mode_id,185, 'Quotation', 186, 'Tender') AS Jenis_Perolehan,
    --MAX (w.org_code) AS Kod_Kementerian,  MAX (w.org_name) AS Kementerian,
    --MAX (e.org_code) AS Kod_Kumpulan_PTJ, MAX (e.org_name) AS Kumpulan_PTJ, pm.status_id
FROM (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
 ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
  FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
  PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
  PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
  PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
  WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
  AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
  AND ptj.parent_org_profile_id = ptj2.org_profile_id
  AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
  AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
  AND ptj2.parent_org_profile_id = ptj3.org_profile_id
  AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
  AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
  AND ptj3.parent_org_profile_id = ptj4.org_profile_id
  AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
  AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
  AND ptjv.RECORD_STATUS = 1
  AND ptjv2.RECORD_STATUS = 1
  AND ptjv3.RECORD_STATUS = 1
  AND ptjv4.RECORD_STATUS = 1
  --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
  --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
  --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
  AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,
955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
AND ptjv.ORG_CODE NOT in ('10010000') ) by_ptj, sc_qt sq,
    sc_workflow_status s,
    pm_status_desc pm,
    pm_parameter mtr,
    pm_org_validity d,
    pm_org_profile pr,
    pm_org_validity e,                                              --jab
    pm_org_profile pr2,
    pm_org_validity y,
    pm_org_profile m,
    pm_org_validity w,
    pm_org_profile mi, 
    (SELECT *
FROM sc_qt qt, PM_ORG_VALIDITY pov
WHERE qt.OWNER_ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
AND pov.RECORD_STATUS = 1
AND NOT EXISTS (
SELECT *
FROM sc_qt_checklist list
WHERE  (list.DOCUMENT_ID = 1277 AND  qt.QT_ID = list.qt_id) OR (LOWER (list.title)  like '%tempasal%' AND qt.QT_ID = list.qt_id)
)) tempasal
WHERE w.org_profile_id = mi.org_profile_id
AND d.org_profile_id = pr.org_profile_id
AND e.org_profile_id = pr.parent_org_profile_id
AND pr.org_type_id = mtr.parameter_id
AND pr2.org_profile_id = pr.parent_org_profile_id
AND m.org_profile_id = pr2.parent_org_profile_id
AND y.org_profile_id = m.org_profile_id
AND mi.org_profile_id = m.parent_org_profile_id
AND d.record_status = 1
AND w.record_status = 1
AND sq.qt_id = s.doc_id
AND sq.org_profile_id = d.org_profile_id
AND pm.status_id = s.status_id
AND sq.procurement_mode_id IN ('185', '186')
AND pm.language_code = 'en'
AND s.is_current = 1
AND d.ORG_CODE = by_ptj.ptj_org
--AND pm.status_id IN (60014)
/*AND pm.status_id NOT IN
       (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
        60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
        60046)*/ --- uncomment to exclude qt cancel 
AND s.doc_type = 'QT'
AND sq.qt_no LIKE ('QT%')
AND sq.created_date BETWEEN TO_DATE('01/11/2022', 'dd/mm/yyyy') AND SYSDATE
AND to_char(sq.CHANGED_DATE,'yyyy-mm-dd') >= ? AND to_char(sq.changed_date,'yyyy-mm-dd') <= ?
AND UPPER (sq.qt_title) LIKE '%FTA%CPTPP%'
AND (UPPER(sq.qt_title) NOT LIKE UPPER ('%khazanah%')  
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%seni%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%warisan%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%penyelidikan%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%kebangsaan%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%kabotaj%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%cpc%')
AND UPPER(sq.qt_title) NOT LIKE UPPER ('%plasma%'))
AND tempasal.qt_no = sq.qt_no
--AND sq.publish_period < 40
GROUP BY sq.qt_title,
    sq.created_date,
    sq.qt_no,
    sq.procurement_mode_id,
    sq.procurement_type_cat_id,
    sq.closing_date,
    pm.status_name,
    pm.status_id,
    s.changed_date,
    sq.publish_date, 
    sq.closing_date
ORDER BY sq.qt_no", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleCT($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT DISTINCT ct.CONTRACT_NO , decode(ct.RECORD_STATUS,'0','Terminate','1','Active','3','Expired') AS record_status, ss.COMPANY_NAME , ptj_org, ptj_name, min_name, ct.CONTRACT_NAME ,NVL(TO_CHAR(IS_CONTRACT_EXTEND), 'N/A') IS_CONTRACT_EXTEND , nvl(TO_CHAR(IS_QUANTITY_CHANGE) ,'N/A') IS_QUANTITY_CHANGE , nvl(TO_CHAR(IS_VALUE_CHANGE) ,'N/A') IS_VALUE_CHANGE --, scb.SSM_COMPANY_COUNTRY
            FROM CT_CONTRACT ct, CT_CONTRACT_VER ccv , CT_AMENDMENT ca, SM_SUPPLIER ss , --SM_COMPANY_BASIC scb , 
            (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
          ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
           FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
           PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
           PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
           PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
           WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
           AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
           AND ptj.parent_org_profile_id = ptj2.org_profile_id
           AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
           AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
           AND ptj2.parent_org_profile_id = ptj3.org_profile_id
           AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
           AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
           AND ptj3.parent_org_profile_id = ptj4.org_profile_id
           AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
           AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
           AND ptjv.RECORD_STATUS = 1
           AND ptjv2.RECORD_STATUS = 1
           AND ptjv3.RECORD_STATUS = 1
           AND ptjv4.RECORD_STATUS = 1
           --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
           --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
           --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
           --AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
           ) byptj
            WHERE ct.CONTRACT_NAME LIKE '%CPTPP%'
            AND ct.CONTRACT_ID = ccv.CONTRACT_ID 
            AND ct.SUPPLIER_ID = ss.SUPPLIER_ID 
            --AND ss.LATEST_APPL_ID = scb.APPL_ID 
            AND ccv.CONTRACT_VER_ID = ca.CONTRACT_VER_ID(+)
            AND ct.OWNER_ORG_PROFILE_ID = byptj.ptj_id
            AND (ca.IS_CONTRACT_EXTEND IS NOT null OR ca.IS_QUANTITY_CHANGE IS NOT null OR ca.IS_VALUE_CHANGE IS NOT null)
            AND to_char(ca.CHANGED_DATE,'yyyy-mm-dd') >= ? AND to_char(ca.changed_date,'yyyy-mm-dd') <= ?
            --AND ccv.DOC_TYPE ='FA'
            ORDER BY ct.CONTRACT_NO DESC", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listByModuleFL($startDate, $toDate)
    {
        $query = DB::connection('oracle_nextgen_drc')
            ->select("SELECT DISTINCT byptj_ct.min_name, byptj_ct.ptj_org, byptj_ct.ptj_name, ct.CONTRACT_NAME , ct.CONTRACT_NO , cr.doc_no, cr.title, byptj.ptj_org as ptj, byptj.ptj_name as ptj_name2,
            ss.COMPANY_NAME ,   decode(cr.RECORD_STATUS,'0','Inactive','1','Active','2','Cancelled') AS RECORD_STATUS   
            FROM CT_CONTRACT ct, CT_CONTRACT_VER ccv , CT_AMENDMENT ca, SM_SUPPLIER ss , SM_COMPANY_BASIC scb , FL_FULFILMENT_REQUEST cr, 
            (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
          ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
           FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
           PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
           PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
           PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
           WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
           AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
           AND ptj.parent_org_profile_id = ptj2.org_profile_id
           AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
           AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
           AND ptj2.parent_org_profile_id = ptj3.org_profile_id
           AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
           AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
           AND ptj3.parent_org_profile_id = ptj4.org_profile_id
           AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
           AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
           AND ptjv.RECORD_STATUS = 1
           AND ptjv2.RECORD_STATUS = 1
           AND ptjv3.RECORD_STATUS = 1
           AND ptjv4.RECORD_STATUS = 1
           --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
           --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
           --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
           AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,
    955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)) byptj_ct, (SELECT ptjv.ORG_PROFILE_ID ptj_id , ptjd.CODE_NAME ptj_code, ptjv.ORG_NAME ptj_name,ptjv.ORG_CODE ptj_org,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE , ptjv2.ORG_NAME, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME , ptjv3.ORG_CODE , ptjv3.ORG_NAME,
          ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE , ptjv4.ORG_NAME min_name
           FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
           PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
           PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
           PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4
           WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
           AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
           AND ptj.parent_org_profile_id = ptj2.org_profile_id
           AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
           AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
           AND ptj2.parent_org_profile_id = ptj3.org_profile_id
           AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
           AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
           AND ptj3.parent_org_profile_id = ptj4.org_profile_id
           AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
           AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
           AND ptjv.RECORD_STATUS = 1
           AND ptjv2.RECORD_STATUS = 1
           AND ptjv3.RECORD_STATUS = 1
           AND ptjv4.RECORD_STATUS = 1
           --AND ptj.ORG_PROFILE_ID IN (982591,982593,982595,982602,982604) --ptj
           --AND ptj2.ORG_PROFILE_ID = 955160 --kumpulan ptj
           --AND ptj3.ORG_PROFILE_ID = 955072   -- Pegawai Pengawal
           AND ptj4.ORG_PROFILE_ID IN (955025, 955027, 955038, 955032, 955026, 955031, 1089308, 955034, 955020, 955019, 955021, 955018, 955039, 955022, 955024, 955035,
    955036, 955015, 955040, 955028, 955023, 955017, 955016, 955030, 955029, 1031733)
    AND ptjv.org_code not in ('10010000')) byptj
            WHERE UPPER(ct.CONTRACT_NAME) LIKE '%CPTPP%'
            AND (UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%khazanah%')  
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%seni%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%warisan%')
    --AND UPPER(ct.CONTRACT_NAME) LIKE UPPER ('%agama%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%penyelidikan%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%kebangsaan%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%kabotaj%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%cpc%')
    AND UPPER(ct.CONTRACT_NAME) NOT LIKE UPPER ('%plasma%'))
    --AND TO_DATE(TO_CHAR(cr.CHANGED_DATE, 'YYYY-MM-DD HH24:MI:SS'), 'YYYY-MM-DD HH24:MI:SS') < TO_DATE('2024-02-29', 'YYYY-MM-DD')
            --AND cr.title NOT LIKE '%CPTPP%'
            AND ct.CONTRACT_ID = ccv.CONTRACT_ID 
            AND ct.SUPPLIER_ID = ss.SUPPLIER_ID 
            AND ss.LATEST_APPL_ID = scb.APPL_ID 
            AND ccv.CONTRACT_VER_ID = ca.CONTRACT_VER_ID(+)
            AND ct.OWNER_ORG_PROFILE_ID = byptj_ct.ptj_id
            AND cr.contract_id = ct.CONTRACT_ID 
            AND cr.PREPARED_ORG_PROFILE_ID = byptj.ptj_id
            AND to_char(cr.CHANGED_DATE,'yyyy-mm-dd') >= ? AND to_char(cr.CHANGED_DATE,'yyyy-mm-dd') <= ?
            --AND ccv.DOC_TYPE ='FA'
            ORDER BY ct.CONTRACT_NO DESC", array($startDate, $toDate));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getSpkiMatrixMonthly($year, $month)
    {
        $query = DB::connection('mysql_ep_support')
            ->select("SELECT tmp2.*, ROUND((tmp2.total_failed / tmp2.total_attempt * 100), 2) AS 'SLA%' FROM (
                SELECT (
                    SELECT COUNT(*) 
                    FROM ep_support.ep_log_spki e 
                    WHERE e.provider = tmp.provider 
                    AND e.service IN ('SignIn') 
                    AND YEAR(e.created_at) = tmp.year 
                    AND MONTH(e.created_at) = tmp.month
                ) AS total_attempt, tmp.* 
                FROM (
                    SELECT 
                        UPPER(s.provider) AS provider, 
                        YEAR(s.created_at) AS YEAR, 
                        MONTH(s.created_at) AS MONTH, 
                        COUNT(*) AS total_failed 
                    FROM ep_support.ep_log_spki s 
                    WHERE s.status IN ('failed', 'error') 
                    AND s.service IN ('SignIn') 
                    AND s.response_data NOT LIKE '%192.168.62.132%' 
                    AND s.response_data NOT LIKE '%192.168.63.232%' 
                    AND s.response_data NOT LIKE '%cURL%' 
                    AND s.request_data IS NOT NULL 
                    AND YEAR(s.created_at) = ? 
                    AND MONTH(s.created_at) = ? 
                    GROUP BY 
                        UPPER(s.provider), 
                        YEAR(s.created_at), 
                        MONTH(s.created_at)
                ) tmp
            ) tmp2", array(
                $year,
                $month,
            ));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }



    public function getAp511MatrixMonthly($year, $month)
    {
        $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth()->toDateString();
        $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth()->toDateString();

        $query = DB::connection('oracle_nextgen_rpt')
            ->select("
            SELECT 
                tmp2.year_created, 
                tmp2.month_created, 
                COUNT(tmp2.created_date) AS TOTAL_CREATED, 
                COUNT(tmp2.process_date) AS TOTAL_PROCESSED, 
                COUNT(CASE WHEN diff_in_hour > 24 THEN 1 END) AS TOTAL_EXCEED_24HOUR 
            FROM (
                SELECT 
                    tmp.year_created,
                    tmp.month_created,
                    tmp.service_code,
                    tmp.service_name, 
                    tmp.file_name,
                    tmp.CREATED_DATE,
                    tmp.process_date,
                    NVL(ROUND((CAST(tmp.process_date AS DATE) - CAST(tmp.CREATED_DATE AS DATE)) * 24, 2), 0) AS diff_in_hour 
                FROM (
                    SELECT 
                        to_char(obf.created_date, 'YYYY') AS year_created, 
                        to_char(obf.created_date, 'MM') AS month_created, 
                        obf.service_code, 
                        s.service_name, 
                        obf.file_name, 
                        obf.CREATED_DATE, 
                        (SELECT MIN(changed_date) 
                        FROM DI_INTERFACE_LOG dil 
                        WHERE dil.service_code = obf.service_code 
                        AND dil.FILE_NAME = obf.FILE_NAME) AS process_date 
                    FROM OSB_BATCH_FILE obf 
                    INNER JOIN osb_service s ON s.service_code = obf.service_code 
                    LEFT JOIN DI_INTERFACE_LOG dil ON dil.service_code = obf.service_code 
                    AND dil.FILE_NAME = obf.FILE_NAME 
                    WHERE obf.service_code IN ('GFM-140') 
                    AND obf.created_date BETWEEN TO_DATE(:start_date, 'YYYY-MM-DD') 
                    AND TO_DATE(:end_date, 'YYYY-MM-DD')
                ) tmp
            ) tmp2 
            GROUP BY tmp2.year_created, tmp2.month_created 
            ORDER BY 1, 2
        ", ['start_date' => $startDate, 'end_date' => $endDate]);

        if (count($query) > 0) {
            return $query;
        }
        return null;
    }


    public function getApiveMatrixMonthly($year, $month)
    {
        $startDate = Carbon::createFromDate($year, $month, 1)->startOfMonth()->toDateString();
        $endDate = Carbon::createFromDate($year, $month, 1)->endOfMonth()->toDateString();

        $query = DB::connection('oracle_nextgen_rpt')->select("
            SELECT    
                tmp2.year_created,  
                tmp2.month_created,  
                COUNT(tmp2.created_date) AS TOTAL_CREATED,  
                COUNT(tmp2.process_date) AS TOTAL_PROCESSED,  
                COUNT(CASE WHEN diff_in_hour > 24 THEN 1 END) AS TOTAL_EXCEED_24HOUR  
            FROM ( 
                SELECT 
                    tmp.year_created,tmp.month_created, tmp.service_code,tmp.service_name,  
                    tmp.file_name,tmp.created_date,  
                    CASE WHEN tmp.process_date IS NULL THEN tmp.changed_date ELSE tmp.process_date END AS process_date,  
                    NVL(ROUND((CAST(tmp.process_date AS DATE) - CAST(tmp.CREATED_DATE AS DATE)) * 24, 2), 0) AS diff_in_hour 
                FROM 
                    (SELECT 
                        to_char(dil.created_date, 'YYYY') AS year_created, 
                        to_char(dil.created_date, 'MM') AS month_created, 
                        dil.service_code, 
                        s.service_name, 
                        dil.file_name, 
                        dil.CREATED_DATE , 
                        dil.CHANGED_DATE, 
                        MIN(obf.created_Date) AS process_date, 
                        COUNT(*) AS total 
                    FROM DI_INTERFACE_LOG dil  
                    INNER JOIN osb_service s ON  s.service_code = dil.service_code 
                    LEFT JOIN OSB_BATCH_FILE obf ON obf.service_code = dil.service_code AND obf.FILE_NAME = dil.FILE_NAME 
                    WHERE dil.service_code IN ('GFM-010') 
                    AND dil.created_date BETWEEN to_date(:start_date, 'YYYY-MM-DD') 
                    AND to_date(:end_date, 'YYYY-MM-DD') 
                    AND dil.file_name IS NOT NULL 
                    AND dil.file_name NOT IN ('DNF') 
                GROUP BY to_char(dil.created_date, 'YYYY'), 
                    to_char(dil.created_date, 'MM'), 
                    dil.service_code, 
                    s.service_name, 
                    dil.file_name, 
                    dil.CREATED_DATE , 
                    dil.CHANGED_DATE 
                ) tmp  
            ) tmp2  
            GROUP BY tmp2.year_created,  
                    tmp2.month_created  
            ORDER BY 1, 2
        ", ['start_date' => $startDate, 'end_date' => $endDate]);
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
}
