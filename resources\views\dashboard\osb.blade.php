@extends('layouts.guest-dash')

@section('header')
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/dashboard/main/') }}">Main</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/batch/') }}">Batch File</a>
                </li>
                <li class="active">
                    <a href="{{ url('/dashboard/osb/') }}">OSB</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/igfmas/') }}">IGFMAS</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/phis/') }}">PHIS</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/paymentreceipt/') }}">Payment AR502</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/gfmascrm/') }}">CRM</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/mygpis/') }}">MyGPIS</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/egpa/') }}">EGPA</a>
                </li>
                <li>
                    <a href="{{ url('/dashboard/spki/') }}">SPKI</a>
                </li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
        <div class="row">
            <div class="col-lg-4">
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Monitoring Error Response in OSB
                        </h5>
                    </div>
                    <div id="dash_monitorErrorResponseOSB" style="padding: 20px;">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Inbound Error File Monitoring <strong> (Return Error File)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkFileErrorInbound" style="padding: 20px;">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            IGFMAS Integration <strong>Today Monitoring Check Charging & PMQ</strong>
                        </h5>
                    </div>
                    <div id="dash_gfmasCheckChargingIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            SSM Integration <strong>Today Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_ssmIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service<strong> SPKI Trustgate</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_trustgate" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service<strong> SPKI Digicert</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_digicert" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>

            </div>
            <div class="col-lg-4">
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Web Service Integration <strong>Connection Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_all_integration_webservice">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>

                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            myIDENTITY <strong>Today Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_jpnIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>

                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service <strong>IGFMAS</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_igfmas" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service <strong>PHIS</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_phis" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>

            </div>
            <div class="col-lg-4">
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Batch Service Integration <strong>Connection Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_all_integration_batchservice">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service <strong>SSM</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_ssm" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget hide'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Connection Service <strong>JPN</strong>
                        </h5>
                    </div>
                    <div id="dash_connection_service_jpn" class="widget">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>

                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            <strong>IGFMAS Integration</strong> Today Monitoring eP call IGFMAS Service
                        </h5>
                    </div>
                    <div id="igfmasIntegrationMonitoring" style="padding: 20px; display: flex; justify-content: center;">
                        <button type="button" class="btn btn-danger">Click to refresh</button>
                    </div>
                </div>

                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            <strong>IGFMAS Integration</strong> Monitoring Item Code
                        </h5>
                    </div>
                    <div class="text-center spinner-loading" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="date_picker_container"></div>
                    {{-- <div id="item_code_datatable_container"></div> --}}

                    <div id="igfmasIntegrationMonitoringItemCode">
                        <div class="table-responsive">
                            <table id="item_code_datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    @endif

    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List
                            Title</span>
                    </h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                        <div class="pull-right">
                            <a href="{{ url('/list/1gfmas/folder') }}" target="_blank"
                                class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title=""
                                data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger
                                Batch</a>
                        </div>
                    </div>
                    <div id="trigger_btn" class="row" style="padding: 0 15px 15px 0; display: none;" disabled>
                        <div class="pull-right">
                            <a id="trigger_url"></a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>

    <div id="modal-igfmas-integration" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-igfmas-integration-header">IGFMAS
                            Integration Data</span>
                    </h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="igfmas-datatable" class="table table-striped table-vcenter">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    <!-- END Content -->
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();
        /* Initialize Datatables */
        var tableListData = $('#basic-datatable').DataTable({
            columnDefs: [{
                orderable: false,
                targets: [0]
            }],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, -1],
                [10, 20, 30, 'All']
            ]
        });

        var tableItemCode = $('#item_code_datatable').DataTable({
            columnDefs: [{
                orderable: false,
                targets: [0]
            }],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, -1],
                [10, 20, 30, 'All']
            ]
        });


        $(document).ready(function() {

            $('.widget').on("click", '.modal-list-data-action', function() {

                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));
                if ($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound') {
                    $('#fetch_btn').show();
                    $('#trigger_btn').hide();
                    console.log('fetch');
                } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllService') {
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/service') }}' \n\
                                                                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title=''\n\
                                                                            data-original-title='Trigger All Service'\n\
                                                                            > Trigger All Service</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger service');
                } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllBatch') {
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/batch') }}' \n\
                                                                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title='' \n\
                                                                            data-original-title='Trigger All Batch'\n\
                                                                            > Trigger All Batch</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger batch');
                } else {
                    $('#trigger_btn').hide();
                    $('#fetch_btn').hide();
                    console.log('hide all');
                }

                /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();

                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [{
                                orderable: false,
                                targets: [0]
                            }],
                            pageLength: 10,
                            lengthMenu: [
                                [10, 20, 30, -1],
                                [10, 20, 30, 'All']
                            ]
                        });

                        $('.spinner-loading').hide();
                    }
                });

            });

            $('#igfmasIntegrationMonitoring button').on('click', function() {
                var button = $(this);

                // Change the button to a spinner
                button.html('<i class="fa fa-spinner fa-spin"></i>');

                $.ajax({
                    url: APP_URL + '/dashboard/igfmasIntegrationMonitoring',
                    type: "GET",
                    success: function(data) {
                        $data = $(data);
                        $('#igfmasIntegrationMonitoring').hide().html($data).fadeIn();

                        $(document).on('click', '.total-req-button', function() {
                            var statusDesc = $(this).data('statusdesc');
                            fetchDetailedListIGFMASIntegration(statusDesc);
                        });

                        function fetchDetailedListIGFMASIntegration(statusDesc) {
                            $('#igfmas-datatable').hide();
                            // Show the loading spinner
                            $('.spinner-loading').show();

                            // Make an AJAX request to your server-side method
                            $.ajax({
                                url: APP_URL +
                                    '/dashboard/igfmasIntegrationMonitoringListDetails',
                                type: 'GET',
                                data: {
                                    statusDesc: statusDesc
                                },
                                success: function(data) {
                                    // Hide the loading spinner
                                    $('.spinner-loading').hide();

                                    $('#igfmas-datatable').hide().html(data)
                                        .fadeIn();
                                }
                            });
                        }
                    }
                });
            });


            //Monitor Error Response in OSB
            $.ajax({
                url: APP_URL + '/dashboard/monitorErrorResponseOSB',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_monitorErrorResponseOSB').hide().html($data).fadeIn();
                }
            });

            //Inbound Error File Monitoring (Return Error File)
            $.ajax({
                url: APP_URL + '/dashboard/checkFileErrorInbound',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_checkFileErrorInbound').hide().html($data).fadeIn();
                }
            });

            //Connection All Web Service Integration
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-all-ws',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_all_integration_webservice').hide().html($data).fadeIn();
                }
            });

            //Connection All Web Service Integration
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-all-batch-server',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_all_integration_batchservice').hide().html($data).fadeIn();
                }
            });

            //IGFMAS Integration Today Monitoring Check Charging & PMQ
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringErrorCheckChargingReceived',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_gfmasCheckChargingIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });
            //SSM Integration Today Monitoring
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringSSMIntegration',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_ssmIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringJPNIntegration',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_jpnIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });


            /*** START CHECK CONNECTIVITY */
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=JPN',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_jpn').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=SSM',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_ssm').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=IGFMAS',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_igfmas').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=PHIS',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_phis').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=TRUSTGATE',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_trustgate').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/check-connection-service?service=DIGICERT',
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#dash_connection_service_digicert').hide().html($data).fadeIn();
                }
            });
        });



        //code item monitoring
        $.ajax({
            url: APP_URL + '/dashboard/igfmas/item/code',
            type: "GET",
            beforeSend: function() {
                $('#item_code_datatable').hide();
                $('.spinner-loading').show();
            },
            success: function(response) {
                $('.spinner-loading').hide();
                $('#item_code_datatable').html(response.table).fadeIn();
                tableItemCode.clear().destroy();
                $('#date_picker_container').html(response.datePicker);
                $('#item_code_datatable').html(response.table);

                const todayDate = new Date($('#created_date').val());
                $('#created_date').datepicker({
                    format: 'yyyy-mm-dd',
                    autoclose: true,
                    todayHighlight: true,
                    startDate: '0',
                }).datepicker("setDate", todayDate);

                tableItemCode = $('#item_code_datatable').DataTable({
                    columnDefs: [{
                        orderable: false,
                        targets: [0]
                    }],
                    pageLength: 10,
                    lengthMenu: [
                        [10, 20, 30, -1],
                        [10, 20, 30, 'All']
                    ]
                });
                $('#item_code_datatable').fadeIn();

                selectDate();
            },
            error: function() {
                $('.spinner-loading').hide();
                $('#item_code_datatable').fadeIn();
            }
        });

        function selectDate() {
            $('#created_date').on('change', function(e) {
                var selectedDate = $(this).val();
                var formattedDate = moment(selectedDate, 'MM/DD/YYYY').format('YYYY-MM-DD');
                $('.spinner-loading').show();
                $.ajax({
                    url: APP_URL + '/dashboard/igfmas/item/code?selectedDate=' + formattedDate,
                    type: "GET",
                    beforeSend: function() {
                        $('#item_code_datatable').hide();
                        $('.spinner-loading').show();
                    },
                    success: function(response) {
                        $('.spinner-loading').hide();
                        $('#item_code_datatable').html(response.table).fadeIn();
                        tableItemCode.clear().destroy();
                        $('#date_picker_container').html(response.datePicker);
                        $('#item_code_datatable').html(response.table);

                        const todayDate = new Date($('#created_date').val());
                        $('#created_date').datepicker({
                            format: 'yyyy-mm-dd',
                            autoclose: true,
                            todayHighlight: true,
                            startDate: '0',
                        }).datepicker("setDate", todayDate);

                        tableItemCode = $('#item_code_datatable').DataTable({
                            columnDefs: [{
                                orderable: false,
                                targets: [0]
                            }],
                            pageLength: 10,
                            lengthMenu: [
                                [10, 20, 30, -1],
                                [10, 20, 30, 'All']
                            ]
                        });
                        $('#item_code_datatable').fadeIn();

                        $('#created_date').val(selectedDate);

                        selectDate();

                    },
                    error: function() {
                        $('.spinner-loading').hide();
                        $('#item_code_datatable').fadeIn();
                    }
                });

            });
        }
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection
