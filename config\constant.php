<?php

return [

    /*
    |--------------------------------------------------------------------------
    | DEFAULT
    |--------------------------------------------------------------------------
    */
    'default_password_self_portal'        => 'P@ssword1234',

    
    /*
    |--------------------------------------------------------------------------
    | EMAIL TEMPLATE
    |--------------------------------------------------------------------------
    */
    'email_sender'                => '<EMAIL>',
    'email_sender_name'           => 'Pentadbir EPSS',
    'email_sender_datafix'        => '<EMAIL>',
    'email_sender_ep-auto-notify' => '<EMAIL>',
    'email_sender_bantuan_ptj'        => '<EMAIL>',
    
    
    
    //By default. any group in CRM to allow access EPSS, must register in this array.
    'group_access' => [
        'Group IT Coordinator',
        'Group Middleware',
        'Group IT Specialist(Production Support)',
        'Group IT Specialist(Database Admin)',
        'Group IT Specialist(Database Management)',
        'Group IT Specialist(Network Admin)',
        'Group IT Specialist(Security)',
        'Group IT Specialist(Server Admin)',
        'Group IT Specialist(Developer)',
        'Group Revenue Management',
        //'Group Change & Release Mgmt',
        //'Group ePC',
        //'Group Supplier Management',
        //'Group Operation Management(OM)',
        //'Group Business Coordinator',
        'Group Customer Service',
        //'Group Akaun Ep',
        //'eP Operation Specialist',
        //'IT Helpdesk',
        'Group EPSS',
        'Approver', 
        'eP Admin 2',
        //'Group EPSS – Codi',
        'Group Codification',
        'Group PMO',
        'Group ePA2',
        'Group ePA3', // Request helpdesk 13/3/2025 to allow ePA3 access EPSS
        //'Group Government Management(GM)'
    ],
    
    /**
     * Group EPSS : selection users CDC from group Supplier Management, ePC, Operation Management, Government Management or any non technical group.
     */
    
    
    /*
    | Specific Roles to access advance information eP 
    | Check roles in .env file. IF not setup, set by default access roles. 
    */
    
    //Specific for Team CS or SM or ePC to access EPSS get eP information.
    'roles_basic_access'  =>  [
        'Group ePC',
        'Group Customer Service',
        'Group EPSS',
        ],
    
    //Please take note. Arahan Kak Lily.. Team Codification cannot access view features roles_basic_access (CS Menu). Team codi has Item features in EPSS
    'roles_codi_ep'  =>  ['Group Codification'],
    
    //This roles applicable for team specialist IOIM
    'roles_adv_ep'  =>  [
        'Group Middleware',
        'Group IT Specialist(Production Support)',
        'Group IT Specialist(Database Admin)',
        'Group IT Specialist(Database Management)',
        'Group IT Specialist(Network Admin)',
        'Group IT Specialist(Security)',
        'Group IT Specialist(Server Admin)'
        ],
    
    //To give access for Team eP Operation . Puan Adura,Puan Tim
    // Request helpdesk 13/3/2025 to allow ePA3 access EPSS
    'roles_ep_operation' => ['Approver','Group ePA2','Group ePA3'],
    
    'roles_it_specialist'  =>  [
        'Group Middleware',
        'Group IT Specialist(Production Support)',
        'Group IT Specialist(Database Admin)',
        'Group IT Specialist(Database Management)',
        'Group IT Specialist(Network Admin)',
        'Group IT Specialist(Security)',
        'Group IT Specialist(Server Admin)'
    ],
    
    // This users must be in group (Specialist Technical IOIM)
    'users_dev_ep'  =>  ['iqbalfikri','mohdshamsul','shahril','hazman','lilian2','lilian','amirrudin','aminah','luqmanulhakim'],
    
    'users_report'  =>  ['fauzal','halim','sharmilakaur','wani','salanizah','norlita','izuan','muhammadfarhan'],
    
    
    
    /** Enable temporary for not IOAM Team **/
    'users_cr_mgmt'  =>  [
        'moriana' //Enable on 5/11/2020 for testing MyGPIS until 8/11/2020, Open view detail PO/CO
        ],
    
    // This users must be in group (Specialist Technical IOIM to do data patching only )
    'roles_patch_ep'  =>  ['Group Middleware','Group IT Specialist(Production Support)'],
    
    //Purposely for Team CS only.
    'roles_cs_ep'  =>  ['Group Business Coordinator', 'Group Customer Service','Group Middleware'],
    'users_adv_crm'  =>  ['noorhayati','shaza','hidayahm','nadiahhani'],
    
    'roles_access_smmodule' => [
        'Group Business Coordinator', 
        'Group Customer Service',
        //'Group ePC', 
        //'Group Supplier Management',
        //'Group Operation Management(OM)',
        'Group Akaun Ep',
        'eP Operation Specialist',
        'IT Helpdesk',
        'Group EPSS',
        //'Group IT Coordinator', 
        'Group Middleware', 
        'Group IT Specialist(Production Support)', 
        //'Group Change & Release Mgmt',
        'Approver', 
        //'Group Archisoft Build Team', 
        'Group IT Specialist(Database Admin)', 
        'Group IT Specialist', 
        'Group IT Specialist(Network Admin)',
    ],
    
    
    //Please do not update any roles. this features restricted for prod support team technical only.
    'roles_specialist'  =>  ['Group Middleware','Group IT Specialist(Production Support)'],
    
    //Access some menu for IT Support team (Not as specialist technical) 
    'roles_it_support' => ['Group IT Coordinator'],
    
    'roles_ep_specialist' => ['eP Operation Specialist'],
    
    'roles_it_developer' => ['Group IT Specialist(Developer)'],
    
    'roles_stl_crm' => ['aminah','iqbalfikri','mohdshamsul','mohdyusri','nazifah','shahril','luqmanulhakim','aizat'],
    
    //Access menu Purging Monitoring
    'roles_dba_users' => ['Group IT Specialist(Database Admin)','Group Middleware', 'azwandy'],
    
    // Access menu helpdesk
    'users_helpdesk'  =>  ['aminah','mohdshamsul','nazifah','shahril','iqbalfikri','hafizahmad','hazman','mohamadanas','umarzuki','zaiful','amirrudin','amnah','azman','nurasmaa','zati'],

    'roles_middleware' => ['Group Middleware'],

    'roles_server_admin' => ['hafizahmad', 'umarzuki','zaiful', 'mohamadanas', 'azwandy'],

    'roles_SM_testing' => ['fauziahm','aema','azwandy','miormuhamad'], 
    
    'roles_approver_testing' => ['azwandy'],//for testing purpose only

    'roles_network_approver' => ['edi', 'azwandy'],
    
    //Access menu POMS
    'users_poms' => ['iqbalfikri'],
];
