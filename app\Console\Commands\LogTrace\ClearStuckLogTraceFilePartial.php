<?php

namespace App\Console\Commands\LogTrace;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\LogTraceService;

class ClearStuckLogTraceFilePartial extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ClearStuckLogTraceFilePartial';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To clear stuck log trace file every minute max 30 files Only';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $logtrace = new LogTraceService();
        try {
            $logtrace->clearStuckLogByLimit(30); 

            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            $logtrace->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }
}
