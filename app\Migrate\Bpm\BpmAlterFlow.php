<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\BPMService;
use App\Services\Traits\FulfilmentService;
use Illuminate\Support\Arr;

class BpmAlterFlow {

    use BpmApiService;
    use BPMService;
    use FulfilmentService;

    public static function run($composite) {
        ini_set('memory_limit', '-1');
        Log::debug(__METHOD__ . ' Starting ... ');
        $alterFlow = new BpmAlterFlow;

        $parentComponent = "";
        $childComponent = "";
        $activityComponent = "";
        switch ($composite) {
            case "Order":
                $parentComponent = array("PurchaseRequestInitiation", "ContractRequestInitiation", "PHISContractRequestInitiation");
                $childComponent = array("PurchaseRequestCreation", "ContractRequestCreation");
                $activityComponent = array("ReviewPR", "ReviewCR");
                break;
            case "Fulfilment":
                $parentComponent = array("DeliveryOrderListInitiation");
                $childComponent = array("DeliveryOrderFulfilment");
                $activityComponent = array("CreateFRN");
                break;
        }

        $list = $alterFlow->findListStuckInvalidCompletedByComposite($composite);
        $total = count($list);
        dump($total);
        foreach ($list as $data) {
            if (in_array($data->component_name, $activityComponent)) {
                $compositeId = $data->composite_instance_id;
                dump('COMPOSITE ID:' . $compositeId);
                Log::info('COMPOSITE ID:' . $compositeId);
                $listTask = $alterFlow->findAPIProcessManagerBPMByInstance($compositeId);
                if ($listTask["status"] != null && $listTask["status"] === 'Success') {
                    $listdata = $listTask["result"];
                    if (isset($listdata["childs"])) {
                        foreach ($listdata["childs"] as $child) {
                            if ($child["componentType"] == 'bpmn' && in_array($child["componentName"], $parentComponent)) {
                                if (isset($child["childs"])) {
                                    foreach ($child["childs"] as $ch) {
                                        if ($ch["componentType"] == 'bpmn' && in_array($ch["componentName"], $childComponent) && $ch["componentState"] == 1) {
                                            $processId = $ch["componentInstanceId"];
                                            if (isset($ch["childs"])) {
                                                foreach ($ch["childs"] as $data) {
                                                    if (in_array($data["componentName"], $activityComponent) && $data["componentStatus"] == 'COMPLETED') {
                                                        $workflowTask = $alterFlow->findAPITaskIDBPMList($data["componentInstanceId"]);
                                                        if ($workflowTask["status"] != null && $workflowTask["status"] === 'Success') {
                                                            $listActivity = $alterFlow->findAPIBPMWorkflowByProcessId($processId);
                                                            if ($listActivity["status"] != null && $listActivity["status"] === 'Success') {
                                                                if ($listActivity["status"] != null && $listActivity["status"] === 'Success') {
                                                                    $listResult = $listActivity["result"];
                                                                    if (isset($listResult["audits"])) {
                                                                        $lastActivity = end($listResult["audits"]);
                                                                        $lastActivityId = $lastActivity["activity_id"];
                                                                        dump('LAST ACTIVITY ID: ' . $lastActivityId);
                                                                        dump('LAST ACTIVITY NAME: ' . $lastActivity["name"]);
                                                                        Log::info('LAST ACTIVITY ID: ' . $lastActivityId);
                                                                        Log::info('LAST ACTIVITY NAME: ' . $lastActivity["name"]);
                                                                        $listAlterFlow = $alterFlow->alterflowBpmnProcessId($processId);
                                                                        if ($listAlterFlow["status"] != null && $listAlterFlow["status"] === 'Success') {
                                                                            $list = $listAlterFlow["result"];
                                                                            dump($processId);
                                                                            $submit = $alterFlow->submitAlterflowBpmnProcessId($processId, $lastActivityId, $lastActivityId);
                                                                            if ($submit['status'] == 'Success') {
                                                                                dump($submit);
                                                                                Log::info($submit);
                                                                            }
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }


    public static function runAlterFlowDpCancelItemCodification() {
        ini_set('memory_limit', '-1');
        Log::debug(__METHOD__ . ' Starting ... ');
        $alterFlow = new BpmAlterFlow;

        $list =  DB::connection('oracle_bpm_rpt')->select( "SELECT 
        CIKEY,c.COMPONENT_NAME ,CMPST_ID ,COMPOSITE_NAME , COMPOSITE_REVISION,FLOW_ID,TITLE ,c.STATE,MODIFY_DATE ,
        (SELECT CUSTOMATTRIBUTESTRING1 FROM wftask w WHERE w.COMPOSITENAME = 'SourcingDP'  AND w.STATE = 'WITHDRAWN' AND w.flow_id  = c.flow_id AND w.activityname= 'Codify Purchase Request') AS doc_no
        FROM cube_instance c WHERE COMPOSITE_NAME = 'SourcingDP' 
        AND c.STATE = 1 
        AND c.FLOW_ID not in (-1) 
        AND c.COMPONENT_NAME = 'SupplierCodification'
        AND component_name NOT IN ('ErrorHandler')
        AND EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME = 'SourcingDP'  AND w.STATE = 'WITHDRAWN' AND w.flow_id  = c.flow_id AND w.activityname= 'Codify Purchase Request')
        ");
        $total = count($list);
        MigrateUtils::logDump(__METHOD__. ' >> result found : '.$total);
        foreach ($list as $data) {
            MigrateUtils::logDump(__METHOD__ . ' objeck '.json_encode($data));
            $processId = $data->cikey;
            $submit = $alterFlow->submitAlterflowBpmnProcessId($processId, 'ACT18838803733822', 'EVT18838766512067');
            if ($submit['status'] == 'Success') {
                MigrateUtils::logDump(__METHOD__. ' >> result '.json_encode($submit));
            }
            sleep(2);
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

    public static function runTerminateDpRNCodificationIsInvalid() {
        ini_set('memory_limit', '-1');
        Log::debug(__METHOD__ . ' Starting ... ');
        $alterFlow = new BpmAlterFlow;

        $list =  $alterFlow->findListInstanceSourcingDpRunningStl(); 
        $total = count($list);
        MigrateUtils::logDump(__METHOD__. ' >> result found : '.$total);
        foreach ($list as $data) {
            // find RN docno already created PR

           
            foreach($data->doc_no_list as $rowDoc){
                $listRn = $alterFlow->getListDocNoFulfillmentSQPOTracking($rowDoc->doc_no_other);
                foreach($listRn as $pr){
                    if($pr->fr_doc_no != null){
                        MigrateUtils::logDump(__METHOD__ . ' invalid '. $data->cmpst_id. ' >> '. $pr->fr_doc_no);
                        MigrateUtils::logDump(__METHOD__ . ' invalid terminate >>> '. 'default/'.$data->composite_name.'!'.$data->composite_revision . '   ' .$data->cmpst_id);
                        $res = $alterFlow->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                        MigrateUtils::logDump($res);
                        break;
                    }
                }
            }
            //MigrateUtils::logDump(__METHOD__ . ' objeck '.json_encode($data));
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

}
