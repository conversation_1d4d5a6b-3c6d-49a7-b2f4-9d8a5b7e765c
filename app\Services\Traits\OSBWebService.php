<?php

namespace App\Services\Traits;

use DB;
use App\Migrate\MigrateUtils;
use SSH;
use GuzzleHttp\Client;
use Guzzle;
use Log;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait OSBWebService {

    /** Get Base URL for OSB Webservice Batch Integration */
    public function getBaseUrlWsBatchOSB(){
        return env("BASE_URL_WS_BATCH_OSB","http://*************:8011");  //default pointing to SIT
    }
    /** Get Base URL for OSB Webservice eP */
    public function getBaseUrlWsOSB(){
        return env("BASE_URL_WS_OSB","http://*************:8011");  //default pointing to SIT
    }

    public function callWSProcessFileTransfer($processId,$stuctAt){
        MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        MigrateUtils::logDump('      processId >> '.$processId. '  >> stuctAt >> '.$stuctAt);
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'>"
                . "<soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header>"
                . "<soapenv:Body>"
                . "<trig:TriggerBatchInput xmlns:trig='http://www.ep.gov.my/Schema/1-0/TriggerBatch'>"
                . "<trig:ProcessId>$processId</trig:ProcessId>"
                . "<trig:StuckAt>$stuctAt</trig:StuckAt>"
                . "</trig:TriggerBatchInput>"
                . "</soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"'.$xmlContents.'"';

        $url = $this->getBaseUrlWsBatchOSB()."/TriggerBatch/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl ".$url." --header  ".$urlHeader."  -d ".$xmlContents,
        ];

        $msg = '';
        SSH::into('osb')->run($commands, function ($line) use (&$msg) {
            $data = $line . PHP_EOL;
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);
            foreach($vals as $val){
                if($val["tag"] == 'TRIG:STATUS') {
                    if(array_key_exists('value', $val)) $msg = $val["value"];
                }
            }
        });

        if($msg == 'No file to be processed'){
            $state = 'false';
        } else {
            $state = 'true';
        }

        return array('msg'=>$msg,'state'=>$state);
    }
    
    
    
    public  function callCurlWSCallBackGfmasIN($transID,$serviceCode,$fileName) {
        MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        MigrateUtils::logDump('      transID >> '.$transID. '  >> serviceCode >> '.$serviceCode.'  >> fileName >> '.$fileName);
        if(strlen($transID) == 0 || strlen($serviceCode) == 0 || strlen($fileName) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        };
        
        //dump('$transID : '.$transID. ' ,$serviceCode : '.$serviceCode. ' ,$fileName : '.$fileName);
    
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
            . "<x:Header/>"
            . "<x:Body>"
                . "<bat:EPMFRqArray>"
                    . "<bat:EPMFRq>"
                        . "<epm:RqHeader>"
                            . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                            . "<epm:UID>"
                                . "<epm:RqUID>$transID</epm:RqUID>"
                            . "</epm:UID>"
                        . "</epm:RqHeader>"
                        . "<bat:BatchInboundRq>"
                            . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                            . "<bat:Filename>$fileName</bat:Filename>"
                       . " </bat:BatchInboundRq>"
                    . "</bat:EPMFRq>"
                . "</bat:EPMFRqArray>"
            . "</x:Body>"
        . "</x:Envelope>";
        $xmlContents = '"'.$xmlData.'"';

        $url = $this->getBaseUrlWsBatchOSB() ."/Batch/eP/Callback/v1.4";
        $urlHeader = "'Content-Type: application/xml'";

        $commands  = [
            "curl ".$url." --header  ".$urlHeader."  -d ".$xmlContents,
        ];
        MigrateUtils::logDump("Command Curl : ".json_encode($commands));
        SSH::into('osb')->run($commands, function ($line) use (&$msg) {
            $data = $line . PHP_EOL;
        });
        
        return $xmlData;

    }
    
    
    public  function callWSCallBackGfmasIN($transID,$serviceCode,$fileName) {
        MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        MigrateUtils::logDump('      transID >> '.$transID. '  >> serviceCode >> '.$serviceCode.'  >> fileName >> '.$fileName);
        if(strlen($transID) == 0 || strlen($serviceCode) == 0 || strlen($fileName) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
            . "<x:Header/>"
            . "<x:Body>"
                . "<bat:EPMFRqArray>"
                    . "<bat:EPMFRq>"
                        . "<epm:RqHeader>"
                            . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                            . "<epm:UID>"
                                . "<epm:RqUID>$transID</epm:RqUID>"
                            . "</epm:UID>"
                        . "</epm:RqHeader>"
                        . "<bat:BatchInboundRq>"
                            . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                            . "<bat:Filename>$fileName</bat:Filename>"
                       . " </bat:BatchInboundRq>"
                    . "</bat:EPMFRq>"
                . "</bat:EPMFRqArray>"
            . "</x:Body>"
        . "</x:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsBatchOSB(),
          ]);
        $fullURL =  $this->getBaseUrlWsBatchOSB().'/Batch/eP/Callback/v1.4';
        MigrateUtils::logDump('URL WS TRIGGER : '.$fullURL); 
        $payload = $xmlData;
        $response = $client->post('/Batch/eP/Callback/v1.4', [
          //'debug' => TRUE,
          'body' => $payload,
          'headers' => [
              'Content-Type' => 'text/xml; charset=utf-8',
              'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/Batch/invoke',
          ]
        ]);
        
        return $xmlData;

    }
    
    /**
     * Pick all files in 1GFMAS Server (OUT Folder)
     * @param type $transID
     * @param type $serviceCode
     * @param type $fileName
     * @return string
     */
    public  function callWSClearFilesOUTFolder($transID,$serviceCode,$fileName) {
        MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        MigrateUtils::logDump('      transID >> '.$transID. '  >> serviceCode >> '.$serviceCode.'  >> fileName >> '.$fileName);
        if(strlen($transID) == 0 || strlen($serviceCode) == 0 || strlen($fileName) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
            . "<x:Header/>"
            . "<x:Body>"
                . "<bat:EPMFRq>"
                    . "<epm:RqHeader>"
                        . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                        . "<epm:UID>"
                            . "<epm:RqUID>$transID</epm:RqUID>"
                        . "</epm:UID>"
                    . "</epm:RqHeader>"
                    . "<bat:BatchInboundRq>"
                        . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                        . "<bat:Filename>$fileName</bat:Filename>"
                    . "</bat:BatchInboundRq>"
                . "</bat:EPMFRq>"
            . "</x:Body>"
        . "</x:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsBatchOSB(),
          ]);
        $fullUrl =   $this->getBaseUrlWsBatchOSB().'/Batch/Generic/Inbound/v1.4';
        MigrateUtils::logDump("URL Trigger: ".$fullUrl);
        $payload = $xmlData;
        $response = $client->post('/Batch/Generic/Inbound/v1.4', [
          //'debug' => TRUE,
          'body' => $payload,
          'headers' => [
              'Content-Type' => 'text/xml; charset=utf-8',
              'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/Batch/invoke',
          ]
        ]);
        //dump($client->getConfig()['base_uri']);
        //dump($response->getBody());
        //dump($response->getStatusCode());
        return $xmlData;

    }
    
    
     /**
     * Pick all files in eP (OUT) Folder
     * @param type $uuid
     * @param type $serviceCode
     * @param type $fileName
     * @return string
     */
    public  function callWSPickupFileOutFolderEP($uuid,$serviceCode,$fileName) {
        MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        MigrateUtils::logDump('      uuid >> '.$uuid. '  >> serviceCode >> '.$serviceCode.'  >> fileName >> '.$fileName);

        if(strlen($uuid) == 0 || strlen($serviceCode) == 0 || strlen($fileName) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        
        
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
            . "<x:Header/>"
            . "<x:Body>"
                . "<bat:EPMFRq>"
                    . "<epm:RqHeader>"
                        . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                        . "<epm:UID>"
                            . "<epm:RqUID>$uuid</epm:RqUID>"
                        . "</epm:UID>"
                    . "</epm:RqHeader>"
                    . "<bat:BatchOutboundRq>"
                        . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                        . "<bat:Filename>$fileName</bat:Filename>"
                    . "</bat:BatchOutboundRq>"
                . "</bat:EPMFRq>"
            . "</x:Body>"
        . "</x:Envelope>";
        
        $client = new Client([
            'base_uri' => $this->getBaseUrlWsBatchOSB()
          ]);
        $pathUrlTrigger =  '/Batch/Generic/Outbound/v1.4'; 
        $fullUrl =   $this->getBaseUrlWsBatchOSB().$pathUrlTrigger;
        MigrateUtils::logDump("URL Trigger: ".$fullUrl);  
        $payload = $xmlData;
        $resp = $client->post($pathUrlTrigger, [
          //'debug' => TRUE,
          'body' => $payload,
          'headers' => [
              'Content-Type' => 'text/xml; charset=utf-8',
              'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/Batch/invoke',
          ]
        ]);

        return collect([
                'status_sent' =>$resp->getStatusCode(),
                'xml'=> $xmlData]);

    }
    
    
    /**
     * Using OSB service to integrate search SSM
     * Guzzle do not support RESPONSE for WSDL. SO this service not working get result!
    */
    public  function callWSFindSSMCompany($uuid,$ssmNo,$ssmType) {
        Log::info(' entering >> '.__FUNCTION__);
        Log::info('      uuid >> '.$uuid. '  >> serviceCode >> '.$ssmNo.'  >> fileName >> '.$ssmType);

        if(strlen($uuid) == 0 || strlen($ssmNo) == 0 || strlen($ssmType) == 0){
            return array('msg'=>'Invalid Parameter','status'=>'Failed');
        }
        
        
        
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:ret='http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
        ."<x:Header/>"
        ."<x:Body>"
            ."<ret:EPMFRq>"
                ."<epm:RqHeader>"
                    ."<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                    ."<epm:UID>"
                        ."<epm:RqUID>$uuid</epm:RqUID>"
                    ."</epm:UID>"
                ."</epm:RqHeader>"
                ."<ret:RetrieveSSMInfoRq>"
                    ."<ret:BusinessRegistrationNo>$ssmNo</ret:BusinessRegistrationNo>"
                    ."<ret:Type>$ssmType</ret:Type>"
                ."</ret:RetrieveSSMInfoRq>"
            ."</ret:EPMFRq>"
        ."</x:Body>"
        ."</x:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsOSB(),
          ]);
        
        $pathUrlTrigger =  '/RetrieveSSMInfo/v1.0'; 
        $fullUrl =   $this->getBaseUrlWsOSB().$pathUrlTrigger;
        MigrateUtils::logDump("URL Trigger: ".$fullUrl);  

        $payload = $xmlData;
        $resp = Guzzle::post($fullUrl, [
                    //'debug' => TRUE,
                    //'future' => true,
                    'body' => $payload,
                    'headers' => [
                        'Content-Type' => 'text/xml; charset=utf-8',
                        'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/RetrieveSSMInfo/inquire',
                    ]
                  ]);
        MigrateUtils::logDump(json_encode($resp));
        return collect([
                'status_sent' =>$resp->getStatusCode(),
                'xml_sent'=> $xmlData,
                'result'=> $resp,
                'body'=> simplexml_load_string($resp->getBody(),'SimpleXMLElement',LIBXML_NOCDATA)]);

    }


    /**
     * Sample 
     * URL : /ContractFulfillmentReceivedNote/v1.1
     * SOAPACTION: http://www.ep.gov.my/Service/1-0/ContractFulfillmentReceivedNote/inquire
     * PAYLOAD : Must start from EPMFRq
     * <EPMFRq>...</EPMFRq>
     * 
     */
    public  function sendWebServicePayload($urlWs,$soapAction,$payloadEPMFRq) {
        //MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        //MigrateUtils::logDump('      URL >> '.$urlWs. '  >> soapAction >> '.$soapAction);
        //MigrateUtils::logDump('      payload >> '.$payloadEPMFRq);
        if(strlen($urlWs) == 0 || strlen($soapAction) == 0 || strlen($payloadEPMFRq) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        $payloadEPMFRq = str_replace('"',"'",$payloadEPMFRq);
        //dd($payloadEPMFRq);
        $xmlData    = 
        "<env:Envelope xmlns:env='http://schemas.xmlsoap.org/soap/envelope/' >"
            . "<env:Header/>"
                .$payloadEPMFRq
        . "</env:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsOSB(),
          ]);
        $fullURL =  $this->getBaseUrlWsOSB().$urlWs;
        //MigrateUtils::logDump('URL WS TRIGGER : '.$fullURL); 
        $payload = $xmlData;

        $msg = 'Success';
        $state = true;
        $response = null;
        try {
            $response = $client->post($urlWs, [
                //'debug' => TRUE,
                'body' => $payload,
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction'   => $soapAction,
                ]
              ]);
        } catch (\Exception $th) {
            $msg = 'Failed';
            $state = false;
            $response = $th->getMessage();
        }
        
        
        return array(
            'msg' => $msg,
            'state' => $state,
            'response' => $response, 
            'url' => $fullURL,
            'payload' =>$xmlData
        );
    }


    /**
     * Sample 
     * URL : /PaymentInstructionsQuery/v1.0
     * SOAPACTION: urn:PaymentInstructionsQuery/inquire
     * PAYLOAD : Must start from Body
     * <SOAP-ENV:Body ... </SOAP-ENV:Body>
     * 
     */
    public  function sendWebServicePayloadBody($urlWs,$soapAction,$payloadEPMFRq) {
        //MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        //MigrateUtils::logDump('      URL >> '.$urlWs. '  >> soapAction >> '.$soapAction);
        //MigrateUtils::logDump('      payload >> '.$payloadEPMFRq);
        if(strlen($urlWs) == 0 || strlen($soapAction) == 0 || strlen($payloadEPMFRq) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        $payloadEPMFRq = str_replace('"',"'",$payloadEPMFRq);
        //dd($payloadEPMFRq);
        $xmlData    = 
        "<SOAP-ENV:Envelope   xmlns:SOAP-ENV='http://schemas.xmlsoap.org/soap/envelope/'>"
            .$payloadEPMFRq
        ."</SOAP-ENV:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsOSB(),
          ]);
        $fullURL =  $this->getBaseUrlWsOSB().$urlWs;
        //MigrateUtils::logDump('URL WS TRIGGER : '.$fullURL); 
        $payload = $xmlData;

        $msg = 'Success';
        $state = true;
        $response = null;
        try {
            $response = $client->post($urlWs, [
                //'debug' => TRUE,
                'body' => $payload,
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction'   => $soapAction,
                ]
              ]);
        } catch (\Exception $th) {
            $msg = 'Failed';
            $state = false;
            $response = $th->getMessage();
        }
        
        
        return array(
            'msg' => $msg,
            'state' => $state,
            'response' => $response, 
            'url' => $fullURL,
            'payload' =>$xmlData
        );
    }

    /**
     * Sample 
     * URL : /FulfillmentReceivingNote/v1.4
     * SOAPACTION: http://www.ep.gov.my/Service/1-0/FulfillmentReceivingNote/inquire
     * PAYLOAD : Must start from Body
     * <env:Body ... </env:Body>
     * 
     */
    public  function sendWebServicePayloadBodyTemplate2($urlWs,$soapAction,$payloadEPMFRq) {
        //MigrateUtils::logDump(' entering >> '.__FUNCTION__);
        //MigrateUtils::logDump('      URL >> '.$urlWs. '  >> soapAction >> '.$soapAction);
        //MigrateUtils::logDump('      payload >> '.$payloadEPMFRq);
        if(strlen($urlWs) == 0 || strlen($soapAction) == 0 || strlen($payloadEPMFRq) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        }
        
        $payloadEPMFRq = str_replace('"',"'",$payloadEPMFRq);
        //dd($payloadEPMFRq);
        $xmlData    = 
        "<env:Envelope   xmlns:env='http://schemas.xmlsoap.org/soap/envelope/'>"
            .$payloadEPMFRq
        ."</env:Envelope>";

        $client = new Client([
            'base_uri' => $this->getBaseUrlWsOSB(),
          ]);
        $fullURL =  $this->getBaseUrlWsOSB().$urlWs;
        //MigrateUtils::logDump('URL WS TRIGGER : '.$fullURL); 
        $payload = $xmlData;

        $msg = 'Success';
        $state = true;
        $response = null;
        try {
            $response = $client->post($urlWs, [
                //'debug' => TRUE,
                'body' => $payload,
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction'   => $soapAction,
                ]
              ]);
        } catch (\Exception $th) {
            $msg = 'Failed';
            $state = false;
            $response = $th->getMessage();
        }
        
        
        return array(
            'msg' => $msg,
            'state' => $state,
            'response' => $response, 
            'url' => $fullURL,
            'payload' =>$xmlData
        );
    }
}
