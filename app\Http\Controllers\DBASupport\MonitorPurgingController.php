<?php

namespace App\Http\Controllers\DBASupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\DBMonitoringService;
use Carbon\Carbon;
use Log;
use Illuminate\Http\Request;
use Excel;

class MonitorPurgingController extends Controller {  
    
     use DBMonitoringService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }   
    
    //DBA Monitoring - 3/5/2021 asmaa, update 1/7/2021
    public function showCompositeInstanceMonitoringReport(Request $request) {
                
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {            
           $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                    // Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                  //  Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }  
        }

        //get total Composite Instance based on duration of date
          $displayCompositeInstanceByDate = $this->getStatisticCompositeInstanceByDate($StartDate, $EndDate);
          
        //get total Composite Backlog based on duration of date
          $displayCompositeBacklogByDate = $this->getStatisticBackLogCompositeByDate($StartDate, $EndDate);
          
        //get total Composite New Instance based on duration of date
          $displayCompositeNewInstByDate = $this->getStatisticNewInstCompositeByDate($StartDate, $EndDate);              
               
       //Display data for line chart for New Composite Instance
        $NewCompositeInstance = array();
        foreach ($displayCompositeNewInstByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($NewCompositeInstance, ['label' =>$days, 'y' =>intval($pInfo->newcompinst)]);
        }  
        
        //Display data for line chart for BackLog Composite
        $BcklogCompositeInstance = array();
        foreach ($displayCompositeBacklogByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($BcklogCompositeInstance, ['label' =>$days, 'y' =>intval($pInfo->bcklogcompinst)]);
        } 
        
        //Display data for line chart for Purged Composite
        $PurgedCompositeInstance = array();
        foreach ($displayCompositeInstanceByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($PurgedCompositeInstance, ['label' =>$days, 'y' =>intval($pInfo->purgecompinst)]);
        } 
        
        return view('dba_support.composite_instance_monitoring', [
           'NewCompositeInstance' => $NewCompositeInstance,
           'BcklogCompositeInstance' => $BcklogCompositeInstance,
           'PurgedCompositeInstance' => $PurgedCompositeInstance,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
    }
    
    public function showCubeInstanceMonitoringReport(Request $request) {
                
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {            
            $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                   //  Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                  //  Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }  
        }

        //get total Cube Instance based on duration of date
          $displayCubeInstanceByDate = $this->getStatisticCubeInstanceByDate($StartDate, $EndDate);
          
        //get total Cube Instance based on duration of date
          $displayBackLogCubeInstanceByDate = $this->getStatisticBackLogCubeInstanceByDate($StartDate, $EndDate);
          
        //get total Cube Instance based on duration of date
          $displayPurgeCubeInstanceByDate = $this->getStatisticPurgedCubeInstanceByDate($StartDate, $EndDate);        
               
       //Display data for line chart for New Cube Instance
        $NewCubeInstance = array();
        foreach ($displayCubeInstanceByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($NewCubeInstance, ['label' =>$days, 'y' =>intval($pInfo->newcubeinst)]);
        }  
        
        //Display data for line chart for BackLog Cube
        $BcklogCubeInstance = array();
        foreach ($displayBackLogCubeInstanceByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($BcklogCubeInstance, ['label' =>$days, 'y' =>intval($pInfo->bcklogcubeinst)]);
        } 
        
        //Display data for line chart for Purged Cube
        $PurgedCubeInstance = array();
        foreach ($displayPurgeCubeInstanceByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($PurgedCubeInstance, ['label' =>$days, 'y' =>intval($pInfo->purgecubepinst)]);
        } 
        
        return view('dba_support.cube_instance_monitoring', [
           'NewCubeInstance' => $NewCubeInstance,
           'BcklogCubeInstance' => $BcklogCubeInstance,
           'PurgedCubeInstance' => $PurgedCubeInstance,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
    }
    
    public function showWFTaskMonitoringReport(Request $request) {
        
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {
                 $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                    // Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                  //  Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }                                             
        }

        //get total WFTask based on duration of date
          $displayWFTaskByDate = $this->getStatisticWFTaskByDate($StartDate, $EndDate);
        
          //get total WFTask based on duration of date
          $displayBackLogWFTaskByDate = $this->getStatisticBackLogWFTaskByDate($StartDate, $EndDate);
          
          //get total WFTask based on duration of date
          $displayPurgeWFTaskByDate = $this->getStatisticPurgedWFTaskByDate($StartDate, $EndDate);
               
       //Display data for line chart for New WFTask
        $NewWFTask = array();
        foreach ($displayWFTaskByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($NewWFTask, ['label' =>$days, 'y' =>intval($pInfo->newwft)]);
        }  
        
        //Display data for line chart for BackLog WFTask
        $BcklogWFTask = array();
        foreach ($displayBackLogWFTaskByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($BcklogWFTask, ['label' =>$days, 'y' =>intval($pInfo->bcklogwft)]);
        } 
        
      //  dump($BcklogCubeInstance);
        
        //Display data for line chart for Purged WFTask
        $PurgedWFTask = array();
        foreach ($displayPurgeWFTaskByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($PurgedWFTask, ['label' =>$days, 'y' =>intval($pInfo->purgewft)]);
        } 
        
        return view('dba_support.wftask_monitoring', [           
           'BcklogWFTask' => $BcklogWFTask,
           'PurgedWFTask' => $PurgedWFTask,
            'NewWFTask' => $NewWFTask,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
        
    }
    
    public function showSCAFlowInstMonitoringReport(Request $request) {
        
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {
                 $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                    // Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                  //  Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }                                             
        }

        //get total SCA based on duration of date
          $displaySCAByDate = $this->getStatisticSCAFlowInstByDate($StartDate, $EndDate);
        
          //get total SCA based on duration of date
          $displayBackLogWSCAByDate = $this->getStatisticBackLogSCAFlowInstByDate($StartDate, $EndDate);
          
          //get total SCA based on duration of date
          $displayPurgeSCAByDate = $this->getStatisticPurgedSCAFlowInstByDate($StartDate, $EndDate);
               
       //Display data for line chart for New SCA
        $Newsca = array();
        foreach ($displaySCAByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($Newsca, ['label' =>$days, 'y' =>intval($pInfo->newsca)]);
        }  
        
        //Display data for line chart for BackLog SCA
        $Bcklogsca = array();
        foreach ($displayBackLogWSCAByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($Bcklogsca, ['label' =>$days, 'y' =>intval($pInfo->bcklogsca)]);
        } 
        
      //  dump($BcklogCubeInstance);
        
        //Display data for line chart for Purged SCA
        $purgesca = array();
        foreach ($displayPurgeSCAByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($purgesca, ['label' =>$days, 'y' =>intval($pInfo->purgesca)]);
        } 
        
        return view('dba_support.scaflowinst_monitoring', [           
           'Bcklogsca' => $Bcklogsca,
           'purgesca' => $purgesca,
            'Newsca' => $Newsca,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
        
    }
    
    public function showDLVMsgMonitoringReport(Request $request) {
        
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {
                 $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                    // Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                  //  Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }                                             
        }

        //get total DLVMSG based on duration of date
          $displayDLVByDate = $this->getStatisticDLVMsgByDate($StartDate, $EndDate);
        
          //get total DLVMSG based on duration of date
          $displayBackLogDLVByDate = $this->getStatisticBackLogDLVMsgByDate($StartDate, $EndDate);
          
          //get total DLVMSG based on duration of date
          $displayPurgeDLVByDate = $this->getStatisticPurgedDLVMsgByDate($StartDate, $EndDate);
               
       //Display data for line chart for New DLVMSG
        $Newdlv = array();
        foreach ($displayDLVByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($Newdlv, ['label' =>$days, 'y' =>intval($pInfo->newdlv)]);
        }  
        
        //Display data for line chart for BackLog DLVMSG
        $Bcklogdlv = array();
        foreach ($displayBackLogDLVByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($Bcklogdlv, ['label' =>$days, 'y' =>intval($pInfo->bcklogdlv)]);
        } 
        
      //  dump($BcklogCubeInstance);
        
        //Display data for line chart for Purged DLVMSG
        $purgedlv = array();
        foreach ($displayPurgeDLVByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($purgedlv, ['label' =>$days, 'y' =>intval($pInfo->purgedlv)]);
        } 
        
        return view('dba_support.dlvmsg_monitoring', [           
           'Bcklogdlv' => $Bcklogdlv,
           'purgedlv' => $purgedlv,
            'Newdlv' => $Newdlv,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
        
    }
    
    public function showTableGrowthMonitoringReport(Request $request) {
        
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {            
            $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                  //   Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                   // Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }  
        }

        //get total Table Growth based on duration of date
          $displayTableGrowthByDate = $this->getStatisticTableGrowthByDate($StartDate, $EndDate);
               
       //Display data for line chart for Table Growth - BAQ_ROWS
        $baqRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($baqRows, ['label' =>$days, 'y' =>intval($pInfo->baqrows)]);
        }  
        
        //Display data for line chart for Table Growth - COMPOSITE_ROWS
        $compRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($compRows, ['label' =>$days, 'y' =>intval($pInfo->comprows)]);
        } 
        
        //Display data for line chart for Table Growth - CUBE_INSTANCE_ROWS
        $cubeInstRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($cubeInstRows, ['label' =>$days, 'y' =>intval($pInfo->cubeinstrows)]);
        } 
        
        //Display data for line chart for Table Growth - CUBE_SCOPE_ROWS
        $cubeScopeRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($cubeScopeRows, ['label' =>$days, 'y' =>intval($pInfo->cubescoperows)]);
        }
        
        //Display data for line chart for Table Growth - WFTask ROWS
        $wftRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($wftRows, ['label' =>$days, 'y' =>intval($pInfo->wftaskrows)]);
        }
        
        //Display data for line chart for Table Growth - BPM_CUBE_AUDITINSTANCE_ROWS
        $bpmcubeaudRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($bpmcubeaudRows, ['label' =>$days, 'y' =>intval($pInfo->bpmcubeaudrows)]);
        }      
        //Display data for line chart for Table Growth - SCA_FLOW_INSTANCE_ROWS
        $scaflowRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($scaflowRows, ['label' =>$days, 'y' =>intval($pInfo->scaflowrows)]);
        }
        //Display data for line chart for Table Growth - DLV_MSG_ROWS
        $dlvmsgRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($dlvmsgRows, ['label' =>$days, 'y' =>intval($pInfo->dlvmsgrows)]);
        }
        
        
               
        return view('dba_support.tabgrowth_monitoring', [           
           'baqRows' => $baqRows,
           'compRows' => $compRows,
           'cubeInstRows' => $cubeInstRows,
           'cubeScopeRows' => $cubeScopeRows,
           'wftRows' => $wftRows,
           'bpmcubeaudRows' => $bpmcubeaudRows,
           'scaflowRows' => $scaflowRows,
           'dlvmsgRows' => $dlvmsgRows,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
        
    }  
    
    public function showAppDataGrowthMonitoringReport(Request $request) {
        
        $carbonYesterday = Carbon::yesterday()->toDateString(); 
        $start = Carbon::now(); //returns current day
        $yestDay = Carbon::yesterday();
        $searchingDate1 = $request->entry_date1;
        $searchingDate2 = $request->entry_date2;
        
        // for Date
        if ($searchingDate1 != null && $searchingDate2 != null) {
            $StartDate = Carbon::parse($searchingDate1)->format('Y-m-d');
            $EndDate = Carbon::parse($searchingDate2)->format('Y-m-d');
        } else {            
            $firstDay = $start->firstOfMonth()->format('Y-m-d');
                 $lastDay = $start->subMonth()->endOfMonth()->format('Y-m-d');
                 $firstDayOfLastMonth = $yestDay->firstOfMonth()->format('Y-m-d');
                if($firstDay && $lastDay){
                  //   Log::info('Test X');
                     $StartDate = Carbon::parse($firstDayOfLastMonth)->format('Y-m-d');
                     $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                    
                }  else{
                   // Log::info('Test Y');
                    $StartDate = Carbon::parse($firstDay)->format('Y-m-d');
                    $EndDate = Carbon::parse($carbonYesterday)->format('Y-m-d');
                }  
        }

        //get total Data Growth based on duration of date
          $displayTableGrowthByDate = $this->getStatisticAppDataGrowthByDate($StartDate, $EndDate);
               
       //Display data for line chart for Data Growth - SC_QT_ENCRYPT_DATA_ROWS
        $scqtenRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($scqtenRows, ['label' =>$days, 'y' =>intval($pInfo->scqtenrows)]);
        }  
        
        //Display data for line chart for Data Growth - SC_QT_SUPPLIER_ROWS
        $scqtsuppRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($scqtsuppRows, ['label' =>$days, 'y' =>intval($pInfo->scqtsupprows)]);
        } 
        
        //Display data for line chart for Data Growth - TPL_SPEC_ANSWER_ROWS
        $tplspecRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($tplspecRows, ['label' =>$days, 'y' =>intval($pInfo->tplspecrows)]);
        } 
        
        //Display data for line chart for Data Growth - OSB_LOGGING_ROWS
        $osblogRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($osblogRows, ['label' =>$days, 'y' =>intval($pInfo->osblogrows)]);
        }
        
        //Display data for line chart for Data Growth - OSB_LOGGING_DTL_ROWS
        $osblogdtlRows = array();
        foreach ($displayTableGrowthByDate as $pInfo) { 
             $days = Carbon::parse($pInfo->datecreated)->format('d-m-Y');
                array_push($osblogdtlRows, ['label' =>$days, 'y' =>intval($pInfo->osblogdtlrows)]);
        }
               
        return view('dba_support.datagrowth_monitoring', [           
           'scqtenRows' => $scqtenRows,
           'scqtsuppRows' => $scqtsuppRows,
           'tplspecRows' => $tplspecRows,
           'osblogRows' => $osblogRows,
           'osblogdtlRows' => $osblogdtlRows,
           'StartDate' => $StartDate,
           'EndDate' => $EndDate
       ]);
        
    }  

}
