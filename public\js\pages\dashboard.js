/*
 *  Document   : dashboard.js
 *  Author     : CDC - Team
 *  Description: Custom javascript code used in Dashboard
 */

 var CompCalendar = function() {
     var calendarEvents  = $('.calendar-events');
     
     var reFormValueTaklimat = function (data) {
            $('#form-taklimat').attr('action', '/taklimats/'+data.id);
            $('#form-taklimat_method').val('PATCH');
            $('#form-taklimat-submit').html('Kemaskini');
            $('#title_taklimat_label').html('Kemaskini Taklimat');
            $('#form-taklimat-submit').attr('data-type','update');
            $('#status_field').show();
            $('#form-taklimat-reset').hide();


            $('#title').val(data.title);
            $('#info_ringkas').val(data.info_ringkas);
            $('#lokasi').val(data.lokasi);
            $('#max_peserta').val(data.max_peserta);
            $('#type').val(data.type);
            $('#modul').val(data.modul);
            $('#tarikh_mula').val(data.tarikh_mula);
            $('#tarikh_akhir').val(data.tarikh_akhir);
            $('#masa_mula').val(data.masa_mula);
            $('#masa_akhir').val(data.masa_akhir);
            $('#status').val(data.status);
            $('#state').val(data.state);
            $('#alamat').val(data.alamat);
            $('#bandar').val(data.bandar);
            $('#poskod').val(data.poskod);
            $('#state').trigger("chosen:updated"); //this cause State using Select plugin Chosen (need to update)

            $('html, body').animate({scrollTop: 200}, 500);
                            
     };
     
     return {
         init: function() {

            /* Initialize Bootstrap Datatables Integration */
            App.datatables();

            
            var taklimat_list_datatable = $('#taklimat-list-datatable').DataTable( {
                    "processing": true,
                    "serverSide": true,
                    "order": [[ 4, "desc" ]],
                    "ajax": {
                        "url": "taklimats_dt",
                        "data":  {
                            "_token"  : $('input[name=_token]').val()
                        },
                        "type": "POST"
                    },
                    initComplete: function () {
                      $('#taklimat-list-datatable_filter input').unbind();
                      $('#taklimat-list-datatable_filter input').bind('keyup', function (e) {
                          if (e.keyCode === 13) {
                              taklimat_list_datatable.search( this.value ).draw();
                          }
                      });
                    },
                  lengthMenu: [[ 10, 20, 30, -1], [ 10, 20, 30, 'All']],
                  "aoColumnDefs": [
                      { "visible": false,"searchable": false ,"aTargets": [ 0 ] },
                      {  "width": "20%", "aTargets": [ 2 ]},
                      {  "width": "20%", "aTargets": [ 3 ]},
                      { "sClass": "text-center", "width": "5%", "aTargets": [ 4,5 ]},
                      { "sClass": "text-center", "width": "10%", "aTargets": [ 6 ]}
                  ],
                  "columns": [
                      { "data": "id" },
                      { "data": "title" },
                      { "data": "state" },
                      { "data": "lokasi" },
                      { "data": "tarikh_mula" },
                      { "data": "status" },
                      {
                          data: null,
                          defaultContent: '<div class="btn-group btn-group-xs">'+
                                            '<a href="javascript:void(0)" data-toggle="tooltip" title="Edit" data-placement="left" data-original-title="Edit"   class="edit_row_dt btn btn-default"><i class="fa fa-pencil"></i></a>'+
                                            '<a href="javascript:void(0)" data-toggle="tooltip" title="Senarai Syarikat / Peserta Yang Hadir" data-original-title="Senarai Syarikat / Peserta Yang Hadir"   class="pemohon_row_dt btn btn-default"><i class="gi gi-group"></i></a>'+
                                            '<a href="javascript:void(0)" data-toggle="tooltip" title="Catat / Semak Kehadiran" data-original-title="Catat / Semak Kehadiran"   class="pemohon_check_row_dt btn btn-default"><i class="hi hi-check"></i></a>'+
                                            '<a href="javascript:void(0)" data-toggle="tooltip" title="Delete" data-original-title="Delete" class="delete_row_dt btn btn-danger"><i class="fa fa-times"></i></a>'+
                                           '</div>',
                          orderable: false
                      }
                  ]
              } );
              
 
            

              $('#taklimat-list-datatable').on( 'click', '.delete_row_dt', function (e) {
                  e.preventDefault();
                  App.hideAlert();
                  var tr = $(this).parents('tr');
                  var index = $( "#taklimat-list-datatable tbody tr" ).index( tr );

                  var obj = taklimat_list_datatable.rows().data()[index];
                  $("#taklimat-panel").slideUp();  //Set tutupkan panel
                  var taklimat_id = obj.id;

                  $('#form-hapus-taklimat').attr('action', '/taklimats/'+taklimat_id);
                  //console.log('id delete : '+id);
                  $('#deleteModalTaklimat').data('record', taklimat_id).modal('show');
              });

              $('#taklimat-list-datatable').on( 'click', '.edit_row_dt', function (e) {
                e.preventDefault();
                App.hideAlert();
                var tr = $(this).parents('tr');
                var index = $( "#taklimat-list-datatable tbody tr" ).index( tr );

                var obj = taklimat_list_datatable.rows().data()[index];
                $("#taklimat-panel").slideUp();  //Set tutupkan panel
                var taklimat_id = obj.id;

                //Open Panel -> calling ajax
                $('#taklimat-panel').show(function(){
                   App.hideAlert();
                   $.ajax({
                       method: "GET",
                       url: "/taklimats/"+taklimat_id
                   })
                   .done(function(data) {
                        /*This will return to Update Form  */
                        reFormValueTaklimat(data);

                   });
                  });




              } );  //End click by #taklimat-list-datatable

              $('#taklimat-list-datatable').on( 'click', '.pemohon_row_dt', function (e) {
                App.hideAlert();
                var tr = $(this).parents('tr');
                var index = $( "#taklimat-list-datatable tbody tr" ).index( tr );

                var obj = taklimat_list_datatable.rows().data()[index];
                $("#taklimat-panel").slideUp();  //Set tutupkan panel
                var taklimat_id = obj.id;
                var url = "taklimat-details/"+taklimat_id;
                $(this).attr('href',url);

              } );  //End click by #taklimat-list-datatable

              $('#taklimat-list-datatable').on( 'click', '.pemohon_check_row_dt', function (e) {
                App.hideAlert();
                var tr = $(this).parents('tr');
                var index = $( "#taklimat-list-datatable tbody tr" ).index( tr );

                var obj = taklimat_list_datatable.rows().data()[index];
                $("#taklimat-panel").slideUp();  //Set tutupkan panel
                var taklimat_id = obj.id;
                var url = "taklimat-kedatangan/"+taklimat_id;
                $(this).attr('href',url);

              } );  //End click by #taklimat-list-datatable



             /* Add placeholder attribute to the search input */
             //$('.dataTables_filter input').attr('placeholder', 'Search');

             $('#calendar').fullCalendar({
                 header: {
                     left: 'prev,next',
                     center: 'title',
                     right: 'month,agendaWeek,agendaDay'
                 },
                 lang: 'en',
                 firstDay: 1,
                 editable: false,
                 droppable: false,
                 eventClick: function(event) {
                  
                    /* This to clean back colorbackground each taklimat */
                    App.clearCalendarSelection();
                    $(this).addClass('event-calendar-active');
                  
                    App.hideAlert();
                    $("#taklimat-panel").slideUp();
                    $('#taklimat-panel').show(function(){
                        $.ajax({
                            method: "GET",
                            url: "/taklimats/"+event.id
                        })
                        .done(function(data) {
                            /*This will return to Update Form  */
                            reFormValueTaklimat(data);

                        });
                     });

           			  return false;
           			 },

                 timeFormat: 'h(:mm)a', // like '7am', for all other views
                 events: '/taklimats',
                 eventRender: function(event, element) {
                    var fcTime = element.find('.fc-time')[0];
                    var textFcTime = $(fcTime).text();
                    $(fcTime).html('<b>Masa : </b>'+textFcTime);

                    var fcTitle = element.find('.fc-title')[0];
                    var textFcTitle = $(fcTitle).text();
                    $(fcTitle).html('<b>Tajuk : </b>'+textFcTitle);

                    //element.find('.fc-title').append("<br/>" + event.lokasi).append(", " + event.state).append("<br/>Kekosongan " + event.bil_peserta_daftar).append("/").append(event.max_peserta);
                    element.find('.fc-title').append("<br/><b>Negeri : </b>" + event.state).append("<br/><b>Kekosongan : </b> " + event.bil_peserta_daftar).append("/").append(event.max_peserta);;
                }

             });
             
             var listElement = $('#page-content').find('.fc-month-button');
             $.each( listElement, function( key, input_obj ) {
                    $(input_obj).text('Bulan');
             });
             
             var listElement = $('#page-content').find('.fc-agendaWeek-button');
             $.each( listElement, function( key, input_obj ) {
                    $(input_obj).text('Minggu');
             });
             
             var listElement = $('#page-content').find('.fc-agendaDay-button');
             $.each( listElement, function( key, input_obj ) {
                    $(input_obj).text('Hari');
             });
         }
     };
 }();


var Dashboard = function() {
    return {
      init: function() {

          $("#tutup_maklumat_form" ).on( "click", function() {
               App.hideAlert();
               $('#taklimat-panel').hide();
          });
          $("#add-taklimat-btn" ).on( "click", function() {
               App.hideAlert();

               $("#taklimat-panel").slideUp(function(){
                 $(this).show(function(){
                   $('#form-taklimat').attr('action', '/taklimat');
                   $('#form-taklimat_method').val('POST');
                   $('#form-taklimat-submit').html('Serah');
                   $('#form-taklimat-submit').attr('data-type','add');
                   $('#title_taklimat_label').html('Tambah Taklimat Baru');
                   $('#status_field-panel').hide();
                   $('#form-taklimat-reset').show();
                   $('#form-taklimat').trigger('reset');
                   $('#state').trigger("chosen:updated"); //this cause State using Select plugin Chosen (need to update)


                 });
               });



          });
          $('#form-taklimat-submit').on( "click", function(e) {
              
                if($('#form-taklimat').valid()){
                    var checkupdate = $('#form-taklimat-submit').attr('data-type');
                    console.log(checkupdate);
                    if(checkupdate === 'update'){
                      $('#confirmUpdateTaklimat').modal('show');  
                    }else{
                        $('#form-taklimat').submit();
                    }
                }
              
              
          });
          
          $(".table-row-edit-taklimat" ).on( "click", function(e) {
              e.preventDefault();
              App.hideAlert();

              $("#taklimat-panel").slideUp();
               var taklimat_id = $(this).attr('data-record');
               console.log(taklimat_id);
               $('#taklimat-panel').show(function(){
                 $.ajax({
                     method: "GET",
                     url: "/taklimats/"+taklimat_id
                 })
                 .done(function(data) {
                   //  alert( "success" );
                   /*This will return to Update Form  */
                   $('#form-taklimat').attr('action', '/taklimats/'+data.id);
                   $('#form-taklimat_method').val('PATCH');
                   $('#form-taklimat-submit').html('Kemaskini');
                   
                   $('#title_taklimat_label').html('Kemaskini Taklimat');
                   $('#status_field').show();
                   $('#form-taklimat-reset').hide();


                   $('#title').val(data.title);
                   $('#info_ringkas').val(data.info_ringkas);
                   $('#lokasi').val(data.lokasi);
                   $('#max_peserta').val(data.max_peserta);
                   $('#type').val(data.type);
                   $('#modul').val(data.modul);
                   $('#tarikh_mula').val(data.tarikh_mula);
                   $('#tarikh_akhir').val(data.tarikh_akhir);
                   $('#masa_mula').val(data.masa_mula);
                   $('#masa_akhir').val(data.masa_akhir);
                   $('#status').val(data.status);
                   $('#state').val(data.state);

                 });
               });

          });

          
          $('#btnYesConfirmUpdateTaklimat').on('click', function() {
              $('#form-taklimat').submit();
          });
          
          

          $('.table-row-delete-taklimat').on('click', function(e) {
              e.preventDefault();
              App.hideAlert();
              var id = $(this).data('record');

              $('#form-hapus-taklimat').attr('action', '/taklimats/'+id);
              //console.log('id delete : '+id);
              $('#deleteModalTaklimat').data('record', id).modal('show');

          });

          $('#btnYesDeleteTaklimat').click(function() {
              // handle deletion here
            	var id = $('#deleteModalTaklimat').data('record');
              //console.log('Siap sedia hapus : '+id);

            	$('#deleteModalTaklimat').modal('hide');
          });




          /*
           *  Jquery Validation, Check out more examples and documentation at https://github.com/jzaefferer/jquery-validation
           */


          /* Initialize Form Validation */
          $('#form-taklimat').validate({
              errorClass: 'help-block animation-slideDown', // You can change the animation class for a different entrance animation - check animations page
              errorElement: 'div',
              errorPlacement: function(error, e) {
                  e.parents('.form-group > div').append(error);
              },
              highlight: function(e) {
                  $(e).closest('.form-group').removeClass('has-success has-error').addClass('has-error');
                  $(e).closest('.help-block').remove();
              },
              success: function(e) {
                  // You can use the following if you would like to highlight with green color the input after successful validation!
                  e.closest('.form-group').removeClass('has-success has-error'); // e.closest('.form-group').removeClass('has-success has-error').addClass('has-success');
                  e.closest('.help-block').remove();
              },
              rules: {
                  title: {
                      required: true,
                      minlength: 3
                  },
                  info_ringkas: {
                      required: true,
                      minlength: 3
                  },
                  type: {
                      required: true
                  },
                  tarikh_mula: {
                      required: true
                  },
                  tarikh_akhir: {
                      required: true
                  },
                  masa_mula: {
                      required: true
                  },
                  masa_akhir: {
                      required: true
                  },
                  lokasi: {
                      required: true,
                      minlength: 3
                  },
                  max_peserta: {
                      required: true,
                      number: true
                  },
                  alamat: {
                      required: true
                  },
                  bandar: {
                      required: true
                  },
                  poskod: {
                      required: true,
                      minlength: 5,
                      maxlength: 5
                  },
                  state: {
                      required: true
                  }
              },
              messages: {
                  title: {
                      required: 'Sila masukkan Tajuk / Subjek Taklimat',
                      minlength: 'Sila masukkan tidak kurang dari 3 aksara'
                  },
                  info_ringkas: {
                      required: 'Sila masukkan penerangan ringkas mengenai taklimat ini',
                      minlength: 'Sila masukkan tidak kurang dari 3 aksara'
                  },
                  lokasi: {
                      required: 'Sila masukkan Nama Dewan',
                      minlength: 'Sila masukkan tidak kurang dari 3 aksara'
                  },
                  max_peserta: {
                      required: 'Sila masukkan jumlah peserta yang boleh hadir ke taklimat ini ',
                      number: 'Sila masukkan nombor sahaja'
                  },
                  type: 'Sila pilih',
                  tarikh_mula: 'Sila masukkan Tarikh Mula ',
                  tarikh_akhir: 'Sila masukkan Tarikh Tamat ',
                  masa_mula: 'Sila masukkan Masa Mula ',
                  masa_akhir: 'Sila masukkan Masa Tamat ',
                  alamat: {
                      required: 'Sila masukkan Alamat Dewan'
                  },
                  bandar: {
                      required: 'Sila masukkan Daerah / Bandar '
                  },
                  poskod: {
                      required: 'Sila masukkan Poskod ',
                      minlength: 'Sila masukkan tidak kurang dari 5 aksara',
                      maxlength: 'Sila masukkan tidak lebih dari 5 aksara'
                  },
                  state: 'Sila pilih Negeri '
              }
          });

      }

    };
}();
