<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use DateTime;
use Config;
use Mail;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use App\Model\Notify\NotifyModel;

class HandleSeverityCases extends Command {

    public static function crmService() {
        return new CRMService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSeverityCases';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To send alert for cases with S3';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__ . ' > ' . __FUNCTION__ . ' >> ';
        MigrateUtils::logDump($clsInfo . 'starting ..');
        $groupId = array('97e7c38b-650a-778b-4ec4-58997d0e97fc','5dac7b92-18d0-4beb-b600-413957aa4c26'); //approver/pmo
        $casesSeverityS3 = self::crmService()->pendingCaseSeverity3List($groupId, 's3');
        $listArray = array();
        MigrateUtils::logDump($clsInfo . ' found total : '.count($casesSeverityS3));
        $current = Carbon::now();
        foreach ($casesSeverityS3 as $data) {
            $collect = collect();

            if ($data->taskStatus == 'Pending Acknowledgement') {
                $datestart = Carbon::parse($data->datestart)->addHour(8)->format("Y-m-d H:i:s");
                $datedue = Carbon::parse($data->datedue)->addHour(8)->format("Y-m-d H:i:s");
            } else {
                $datestart = Carbon::parse($data->actualstart)->addHour(8)->format("Y-m-d H:i:s");
                $datedue = Carbon::parse($data->actualstart)->addDay(5)->addHour(8)->format("Y-m-d H:i:s");
            }

            $dateDiff = $current->diff(new DateTime($datedue));
            $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

            if ($datedue > Carbon::now()) {
                //within SLA
                if ($dateDiff->days < 1) {
                    $msg = "
*[ALERT] Cases With Severity 3 Is About To Burst.* 
*Case Number :* $data->caseNumber 
*Case Subject :* $data->caseName 
*Sub Category :* $data->subCategory 
*Sub Category 2 :* $data->subCategory2 
*Assigned Group :* $data->taskname 
*Sla Due :* $datedue 
*Time Remaining :* $timeRemaining ";

                    $collect->put('msg', $msg);
                    $this->saveNotify('CASES_SEVERITY', $collect);
                }
            } else {
                //burst SLA
                $msg = "
*[ALERT] Cases With Severity 3 Is Already Burst.* 
*Case Number :* $data->caseNumber 
*Case Subject :* $data->caseName 
*Sub Category :* $data->subCategory 
*Sub Category 2 :* $data->subCategory2 
*Assigned Group :* $data->taskname 
*Sla Due :* $datedue 
*Time Exceed :* $timeRemaining ";

                $collect->put('msg', $msg);
                MigrateUtils::logErrorDump($clsInfo . $msg);
                $this->saveNotify('CASES_SEVERITY', $collect);
            }
        }
        MigrateUtils::logDump($clsInfo . ' completed');
    }

    public function saveNotify($receiver, $collect) {
        MigrateUtils::logDump(__METHOD__ . 'starting ..');
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group'; // 'notify group , 'notify personal
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring cases with severity 3';
        $nty->save();

        $this->sendNotifyEmail($nty->message);
    }

    protected function sendNotifyEmail($dataContents) {
        MigrateUtils::logDump(__METHOD__ . 'starting ..');
        // Process content to replace line breaks with <br> tags
        $dataContents = nl2br($dataContents);

        // Process content to replace asterisks with <strong> tags
        $dataContents = str_replace(".*", "</strong>", $dataContents);
        $dataContents = str_replace(":*", "</strong>", $dataContents);
        $dataContents = str_replace("*", "<strong>", $dataContents);

        $dataSend = array(
            "to" => [
                    '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'
                ],
            "subject" => '['.env('APP_ENV').'] - CRM eP [ALERT] Cases With Severity 3 Is About To Burst.'
        );
        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mailer
                ->send('emails.notifyCaseIsAboutToBurstSLA', ['data' => $dataContents], function ($m) use ($dataSend) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($dataSend["to"]);
                    $m->subject($dataSend["subject"]);
                });
            Log::info(__CLASS__.' >>  '.__FUNCTION__. 'success send email : '.json_encode($dataSend["to"]). ' info:>>  '. $dataContents);
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }

         
    }

}
