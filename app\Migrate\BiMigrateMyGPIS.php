<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use App\Services\Traits\OSBService;
use App\Model\Notify\NotifyModel;

class BiMigrateMyGPIS {
    use OSBService;
    public static function run() {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(__METHOD__.' Starting ... ' );
        $dtStartTime = Carbon::now();
        //self::migrateMyGpisDataStatistic();
        //self::checkMonitoringMyGpisDailyIntegration('SESSION_MORNING',2);
        MigrateUtils::logDump(__METHOD__. ' Completed  --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function migrateMyGpisDataStatistic() {
        MigrateUtils::logDump(__METHOD__.' Starting ... ' );
        $result = self::getListStatisticTotalETLAllModuleMyGPIS();

        if ($result) {
            foreach ($result as $mygpis_mod) {
                $check = DB::connection('mysql_ep_support')->table('ep_integration_statistic')
                    ->where('module',$mygpis_mod->modul)
                    ->where('group','MYGPIS')
                    ->where('service_code',$mygpis_mod->service_code)
                    ->where('created_date',$mygpis_mod->created_date)
                    ->count();
                if($check == 0){
                    MigrateUtils::logDump(__METHOD__.' No record found in statistic ... data will be insert. ' );
                    DB::connection('mysql_ep_support')->table('ep_integration_statistic')
                        ->insert(
                                [
                                    'group' => 'MYGPIS',
                                    'module' => $mygpis_mod->modul,
                                    'service_code' => $mygpis_mod->service_code,
                                    'created_date' => $mygpis_mod->created_date,
                                    'total' => $mygpis_mod->total,
                                    'last_updated' => Carbon::now()
                                ]);
                }else{
                    MigrateUtils::logDump(__METHOD__.' data already exists  in statistic ... '.json_encode($mygpis_mod) );
                }
            }
        }
    }

    /**
     * @sessionMyGpis   value should be 'SESSION_MORNING' or 'SESSION_EVENING'
     * @hourCompare value should be hour based.  0 ... 23
     */
    public static function checkMonitoringMyGpisDailyIntegration($sessionMyGpis,$hourCompare) {
        MigrateUtils::logDump(__METHOD__.' Starting ... ' );

        $dateToday = Carbon::now();
        $dateTodayFormatted = $dateToday->format('Y-m-d');
        $timeHourNow = Carbon::now()->format('H');
        $dayNow = Carbon::now()->format('l');
        $thsCls = new BiMigrateMyGPIS;

        $maxDateCreation = null;
        if($sessionMyGpis == 'SESSION_MORNING'){
            MigrateUtils::logDump(__METHOD__.' Day today : '.$dateToday->format('l'));

            $result = collect($thsCls->getListStatisticTotalETLMyGPIS());
            $maxDateCreation = $result->whereNotIn('modul',['MYGPIS_IKLAN'])->max('created_date');
            MigrateUtils::logDump(__METHOD__.' maxDateCreation ... '.$maxDateCreation);
            $maxDate = Carbon::parse($maxDateCreation);

            // Checking at least one file must sent to MyGPIS, if 0 then need to alert
            $totalFiles = $thsCls->getTotalStatisticTotalFileMyGPISGeneratedToday($dateTodayFormatted,$sessionMyGpis);
            MigrateUtils::logDump(__METHOD__.' total files generated : '.$totalFiles);
            if( $totalFiles == 0 && $timeHourNow >= 3 && $dayNow != 'Saturday'){
                $modules = 'All modules except IKLAN';
                $thsCls->alertMyGPISFailedGeneratedFile($dateTodayFormatted,$modules);
            }elseif( $totalFiles == 0 && $timeHourNow >= 10 && $dayNow == 'Saturday'){
                $modules = 'All modules except IKLAN';
                $thsCls->alertMyGPISFailedGeneratedFile($dateTodayFormatted,$modules);
            }
        }

        if($sessionMyGpis == 'SESSION_EVENING'){
            $result = collect($thsCls->getListStatisticTotalETLMyGPIS());
            $maxDateCreation = $result->whereIn('modul',['MYGPIS_IKLAN'])->max('created_date');
            MigrateUtils::logDump(__METHOD__.' maxDateCreation ... '.$maxDateCreation);
            $maxDate = Carbon::parse($maxDateCreation);

            // Checking at least one file must sent to MyGPIS, if 0 then need to alert
            $totalFiles = $thsCls->getTotalStatisticTotalFileMyGPISGeneratedToday($dateTodayFormatted,$sessionMyGpis);
            if( $totalFiles == 0  && $timeHourNow > 18){
                $modules = 'IKLAN';
                $thsCls->alertMyGPISFailedGeneratedFile($dateTodayFormatted,$modules);
            }
            
        }
        
        if($maxDateCreation != null){
            // Checking if not as today -> mean MyGPIS not running store procedure at 12am everyday except (saturday)
            if($maxDate->format("Y-m-d") != Carbon::now()->format('Y-m-d')){
                $msg = '*[ALERT]* *MyGPIS Store Procedure Script* Checking SP MyGPIS ['.$sessionMyGpis.'], last generate data on '.$maxDateCreation.'. No data generated by SP on today! Kindly check.' ;
                MigrateUtils::logErrorDump(__METHOD__.$msg);
                $thsCls->saveNotify('MYGPIS',$msg);
            }else{
                $hourTime = $maxDate->hour;
                MigrateUtils::logDump(__METHOD__.' Max Hour time : '.$hourTime.' to compare time hour : '.$hourCompare);
                if($hourTime >= $hourCompare ){
                    $msg = '*[ALERT]* *MyGPIS Store Procedure Script* execute SP MyGPIS ['.$sessionMyGpis.'] still not complete after checking time at : '.$hourCompare.'AM. Kindly check scheduler file to retrigger manual' ;
                    MigrateUtils::logErrorDump(__METHOD__.$msg);
                    $thsCls->saveNotify('MYGPIS',$msg);
                }
            }
        }else{
            MigrateUtils::logDump(__METHOD__.' ERROR! Could not get max date from MyGPIS data');
        }
    }

    protected static function alertMyGPISFailedGeneratedFile($dateProcess,$modules) {
        $msg = "*[ALERT] MyGPIS BATCH FILE*
        *DATE SCHEDULER*
          $dateProcess
        *Modules*
          $modules
        *ISSUE*
          Please check, as the scheduler did not generate any creation file.";
        MigrateUtils::logErrorDump(__METHOD__.$msg);
        self::saveNotify('MYGPIS',$msg);
    }

    /**
     * Get list statistic MyGPIS on ETL data completed extrac
     * @return list
     */
    protected static function getListStatisticTotalETLAllModuleMyGPIS() {
        $results = DB::connection('oracle_nextgen_rpt')->select("
        (SELECT   'MYGPIS_SYARIKAT' AS modul, 'GPI-010' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_syarikat
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_SYARIKAT' AS modul, 'GPI-010' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT  1
                                              FROM mygpis_syarikat
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_BARANG' AS modul, 'GPI-020' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_barang
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_BARANG' AS modul, 'GPI-020' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_barang
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_KUMP_PTJ' AS modul, 'GPI-030' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_kump_ptj
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_KUMP_PTJ' AS modul, 'GPI-030' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_kump_ptj
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_PTJ' AS modul, 'GPI-040' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_ptj
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_PTJ' AS modul, 'GPI-040' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_ptj
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_BIDANG' AS modul, 'GPI-050' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_bidang
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_BIDANG' AS modul, 'GPI-050' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_bidang
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_IKLAN' AS modul, 'GPI-060' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_iklan
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_IKLAN' AS modul, 'GPI-060' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_iklan
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_SST' AS modul, 'GPI-070' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_sst
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_SST' AS modul, 'GPI-070' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_sst
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_KONTRAK' AS modul, 'GPI-080' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_kontrak
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_KONTRAK' AS modul, 'GPI-080' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_kontrak
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_PEMENUHAN' AS modul, 'GPI-090' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_pemenuhan
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_PEMENUHAN' AS modul, 'GPI-090' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_pemenuhan
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_PPEROLEHAN' AS modul, 'GPI-100' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_pperolehan
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_PPEROLEHAN' AS modul, 'GPI-100' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_pperolehan
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_PPERBELANJAAN' AS modul, 'GPI-110' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_pperbelanjaan
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_PPERBELANJAAN' AS modul, 'GPI-110' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_pperbelanjaan
                                          GROUP BY 1,2,3))
                      UNION
                      (SELECT   'MYGPIS_PRESTASI' AS modul, 'GPI-120' AS service_code,
                                TO_CHAR (max(created_date), 'YYYY-MM-DD HH24:MI') AS created_date,
                                COUNT (*) AS total
                           FROM mygpis_prestasi
                       GROUP BY 1,2,3
                       UNION
                       SELECT 'MYGPIS_PRESTASI' AS modul, 'GPI-120' AS service_code,
                              TO_CHAR(sysdate, 'YYYY-MM-DD')||' 00:00' AS created_date, 0 AS total
                         FROM DUAL
                        WHERE NOT EXISTS (SELECT   1
                                              FROM mygpis_prestasi
                                          GROUP BY 1,2,3))
                      ORDER BY 2
                    
                ");

        return $results;
    }

    /**
     * Check get total file generated toda
     * @return list
     */
    protected static function getTotalStatisticTotalFileMyGPISGeneratedToday($date,$sessionMyGpis ) {
        if($sessionMyGpis == 'SESSION_EVENING'){
            $whereClause = "AND created_date >= to_date('$date 18:00','YYYY-MM-DD HH24:MI')";
        }else{
            $whereClause = "AND created_date < to_date('$date 18:00','YYYY-MM-DD HH24:MI')";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT count(*) AS total 
            FROM OSB_BATCH_FILE 
            WHERE created_date >=  to_date('$date','YYYY-MM-DD')
            $whereClause 
            AND service_code IN (
                'GPI-010',
                'GPI-020',
                'GPI-030',
                'GPI-040',
                'GPI-050',
                'GPI-070',
                'GPI-080',
                'GPI-090',
                'GPI-100',
                'GPI-110',
                'GPI-120')");
        return $results[0]->total;
            
    }
    protected static function saveNotify($receiver,$msg){
        MigrateUtils::logDump(__METHOD__.' entering.. '); 
        $totNotify = NotifyModel::where('message',$msg)->count();
        MigrateUtils::logDump(__METHOD__.' total similar.. '.$totNotify); 
        if($totNotify > 0){
            MigrateUtils::logDump(__METHOD__.' Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring daily MyGPIS';
            $nty->save();
        }
        
    }

}
