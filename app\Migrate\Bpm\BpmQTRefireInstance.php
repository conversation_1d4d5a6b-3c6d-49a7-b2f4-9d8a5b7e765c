<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\BPMService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\PayloadGeneratorService;

class BpmQTRefireInstance {

    use BpmApiService;
    use BPMService;
    use FulfilmentService;
    use PayloadGeneratorService;

    /**
     * 60201 - Pending Committee Formation Approval
     */
    public static function runRefireQTExistingInstance() {
        ini_set('memory_limit', '-1');
        Log::debug(__METHOD__ . ' Starting ... ');
        $cls = new BpmQTRefireInstance;

        
        $compositeName = "SourcingQT";
        $processName = "Committee Approval";
        $activityName = "Approve Committee";
        $status_id = '60201'; // Pending Committee Formation Approval
        $status_id_revise = '60204'; //  Pending Committee Formation Revision Approval
        $listTasksAssigned = $cls->findTaskAssigned12cMigrateInstanceDetail($compositeName,$processName,$activityName);
        dump('Total : '.count($listTasksAssigned));
        $counterRefire = 0;
        $counterInvalid = 0;
        $listTasksAssigned = collect($listTasksAssigned)->take(70);
        //dd($listTasksAssigned);
        if($listTasksAssigned != null && count($listTasksAssigned) > 0){
            $counter = 0;
            foreach($listTasksAssigned as $task){
                $counter++;
                dump("");
                dump("######################### $counter) Checking... QT No. : ".$task->doc_no);
                if($task->doc_status_id == $status_id || $task->doc_status_id == $status_id_revise){
                    // Checking status in eP same status 
                    $listQtStatus = collect($cls->getListQuotationTenderByQtNo($task->doc_no));
                    $qtFlowCMT = $listQtStatus->where('is_current',1)->where('doc_type','CMT')->first();
                    $qtFlow = $listQtStatus->where('is_current',1)->where('doc_type','QT')->first();
                  
                    if($qtFlowCMT != null && ($qtFlowCMT->status_id == $status_id || $qtFlowCMT->status_id == $status_id_revise)){
                        
                        //dump($task);
                        //dump($qtFlowCMT);
                        
                        if($qtFlow ->status_id == '60024'){
                            $counterRefire++;
                            dump("# Valid QT No. to terminate and refire: ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid. ' ,statusID eP :'.$qtFlowCMT->status_id. 'statusID eP :'.$task->doc_status_id);
                            //dd($qtFlowCMT);
                            $res = $cls->submitTerminateInstance('default/'.$task->compositename.'!'.$task->compositeversion, $task->compositeinstanceid);
                            MigrateUtils::logDump($res);
                            if($res != null){
                                dump('success terminate instance : '.$task->compositeinstanceid);

                                $quotationTenderPayload = $cls->getQtQuotationTenderCreation($task->doc_no);
                                $payload = str_replace("&", "&amp;", $quotationTenderPayload[0]->qt_tender);
                                $payload = '<SC_QuotationTender_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_QuotationTender_Data">'.$payload ;
                                
                                $url = 'SourcingQT'; 
                                $process = 'QuotationTenderCreation';
                                $trigger = 'start';
                                $elements = [
                                    ['name'=>'SC_QuotationTender_Data','value'=>$payload],
                                    ['name'=>'document_number','value'=>$task->doc_no],
                                    ['name'=>'task_performer','value'=>$quotationTenderPayload[0]->task_performer],
                                    ['name'=>'requote','value'=>false],
                                ];
                                $createService = $cls->findApiBPMCreateServiceManager($url, $process, $trigger, $elements);
                                MigrateUtils::logDump('Result refire '.json_encode($createService));
                                // dump('Start refire  new instance : ');
                                sleep(4);
                                $listNewTaskAssigned = collect($cls->getTaskBpmByDocNo($task->doc_no));
                                $newTask = $listNewTaskAssigned ->where('state','ASSIGNED')->first();
                                if($newTask->compositeinstanceid != $task->compositeinstanceid){
                                    dump("New Instance Success Created : ".json_encode($newTask));
                                }else{
                                    dd(">> New Instance failed creatd!");
                                }
                                

                                // dd('Find new instance : ');
                            }else{
                                $counterInvalid++;
                                dd("####Failed Terminate! ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid); 
                            }
                        }else{
                            $counterInvalid++;
                            dump(" Status DOC QT not valid to refire");
                            dump("# >> QT No. not valid to terminate and refire: ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid. ', statusID eP :'.$qtFlowCMT->status_id. 'statusID eP :'.$task->doc_status_id.' ,statusID QT Common :'.$qtFlow->status_id.'-'.$qtFlow->status_name);
                        }
                        
                    }elseif($qtFlowCMT != null){
                        $counterInvalid++;
                        dump("####Skip!.. Status ID in eP App  not same for QT No. : ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid.
                        ' ,QT status ID BPM : '.$task->doc_status_id. 
                        ' ,QT Status in eP App: '.$qtFlowCMT->status_id.' - '.$qtFlowCMT->status_name);
                        dump("  QT Common Status in eP App: ".$qtFlow->status_id.' - '.$qtFlow->status_name);

                        if($qtFlow->status_id == '60015' || $qtFlow->status_id == '60014' || $qtFlow->status_id == '62216' || $qtFlow->status_id == '60045'){
                            dump(' Valid to terminate this instance.'.$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid);
                            $resT = $cls->submitTerminateInstance('default/'.$task->compositename.'!'.$task->compositeversion, $task->compositeinstanceid);
                            MigrateUtils::logDump($resT);
                        }
                    }else{
                        $counterInvalid++;
                        dump("####Skip!.. Not found in eP : ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid); 
                    }
                }else{
                    $counterInvalid++;
                    dump("####Skip!.. Status ID in BPM task not same for QT No. : ".$task->doc_no. ' ,compositeinstanceid: '.$task->compositeinstanceid);
                }
            }
        }
        dump("");
        dump('  counterRefire : '. $counterRefire);
        dump('  counterInvalid : '. $counterInvalid);
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

}
