<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use SSH;
use App\Migrate\MigrateUtils;
use Guzzle;
use GuzzleHttp\Client;
use DB;
use GuzzleHttp\Exception\GuzzleException;
use App\Services\Traits\SSHService;
use App\Services\Traits\OSBWebService;

/**
 * Program to re-trigger files form IGFMAS to re-process.
 */
class ResendFileBatchOUT
{

    use SSHService;
    use OSBWebService;

    /**
     * Path : /batch/MyGPIS/OUT/*
     * All pending files failed to sent. 
     */
    public static function triggerStuckFileMyGpis() {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' entering..');
        $batchName = 'MyGPIS'; 
        $lengthFileName = 7;
        $thCls = new ResendFileBatchOUT;
        $dataList = $thCls->getListEpBatchFolderOUT($batchName, $lengthFileName); 
        MigrateUtils::logDump(self::class . ' >> ' . __FUNCTION__. ' total found file '.count($dataList));
        foreach ($dataList as $fileName){
            self::reSendFileOutbound($fileName,$batchName);
        }
    }

    /**
     * Path : /batch/PHIS/OUT/*
     * All pending files failed to sent. 
     */
    public static function triggerStuckFilePHIS() {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' entering..');
        $batchName = 'PHIS'; 
        $lengthFileName = 10;
        $thCls = new ResendFileBatchOUT;
        $dataList = $thCls->getListEpBatchFolderOUT($batchName, $lengthFileName); 
        MigrateUtils::logDump(self::class . ' >> ' . __FUNCTION__. ' total found file '.count($dataList));
        foreach ($dataList as $fileName){
            self::reSendFileOutbound($fileName );
        }
    }

    /**
     * Path : /batch/LMS/OUT/*
     * All pending files failed to sent. 
     */
    public static function triggerStuckFileLMS() {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' entering..');
        $batchName = 'LMS'; 
        $lengthFileName = 10;
        $thCls = new ResendFileBatchOUT;
        $dataList = $thCls->getListEpBatchFolderOUT($batchName, $lengthFileName); 
        MigrateUtils::logDump(self::class . ' >> ' . __FUNCTION__. ' total found file '.count($dataList));
        foreach ($dataList as $fileName){
            self::reSendFileOutbound($fileName);
        }
    }

    public static function reSendFileOutbound($fileName,$batchName = null) {
        MigrateUtils::logDump(self::class . ' >> ' . __FUNCTION__. ' FileName '.$fileName);
        $thCls = new ResendFileBatchOUT;
        if(strlen($fileName) > 6){
            MigrateUtils::logDump(self::class . ' ' . __FUNCTION__. ' find file in OSB_BATCH_FILE : '.$fileName);
            $objFile = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')->where('file_name',$fileName)
                ->select('file_name','service_code')
                ->first();
            if($objFile != null){
                $uuid  = \Ramsey\Uuid\Uuid::uuid4();
                $resp = $thCls->callWSPickupFileOutFolderEP($uuid, $objFile->service_code, $objFile->file_name);
                dump($resp);
            }else{
                $filenameNew = $fileName;
                if($batchName != null && $batchName == 'MyGPIS'){
                    $filenameNew = str_replace('.gpg','',$fileName);
                }
                
                MigrateUtils::logDump(self::class . ' ' . __FUNCTION__. ' find file in DI_INTERFACE_LOG : '.$filenameNew);
                //Try to get  service_code by filename. In di_interface_log , record save filename not include .gpg
                $objFileIntLog = DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')->where('file_name',$filenameNew)
                ->select('file_name','service_code')
                ->first();
                if($objFileIntLog != null){
                    //dd($objFileIntLog);
                    // to make sure sent filename include .gpg
                    $uuid  = \Ramsey\Uuid\Uuid::uuid4();
                    $resp = $thCls->callWSPickupFileOutFolderEP($uuid, $objFileIntLog->service_code, $fileName);
                    dump($resp);
                }
            }
        }
    }    


    public static function sendByFileNames($listFileNameObj) {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. 'total : '.$listFileNameObj->count());
        $dtStartTime = Carbon::now();
        $thCls = new ResendFileBatchOUT;
        foreach ($listFileNameObj as $row) {
            $serviceCode = $row->service_code;
            $fileName = $row->file_name;
            $uuid  = \Ramsey\Uuid\Uuid::uuid4();
            $thCls->callWSPickupFileOutFolderEP($uuid, $serviceCode, $fileName);
            
        }
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function sendByFileNamesServiceCode($arrList) {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        $thCls = new ResendFileBatchOUT;
        foreach ($arrList as $row) {
            $fileTypeArr = explode(",", $row);
            $serviceCode = $fileTypeArr[0];
            $fileName = $fileTypeArr[1];
            $uuid  = \Ramsey\Uuid\Uuid::uuid4();
            $thCls->callWSPickupFileOutFolderEP($uuid, $serviceCode, $fileName);
        }
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
   
}
