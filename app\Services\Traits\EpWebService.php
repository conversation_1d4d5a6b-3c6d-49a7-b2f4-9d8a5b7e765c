<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Guzzle;
use GuzzleHttp\Client;

trait EpWebService {

    protected function wsStatusUserLogin($loginId) {

        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-pm/userlogin-status?loginId=" . $loginId;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect'+$ex->getMessage());
        }
    }

    protected function wsListMonitoringServiceRunningEjb($host, $port) {

        try {

            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-monitor?host=" . $host . "&port=" . $port;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "host" => $host,
                "port" => $port,
                "result" => 'Failed to connect '+$ex->getMessage());
        }
    }

    protected function wsDetailParameterServiceRunningEjb($host, $port, $key) {

        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-monitor/detail?host=" . $host . "&port=" . $port . "&key=" . $key;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }

    protected function wsStartUpServiceRunningEjb($host, $port) {
        $key = 'ejb-start';
        // **************:8080/ep-support-middleware/ep/ejb-monitor/start?host=*************&port=4447&action=ejb-start
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-monitor/start?host=" . $host . "&port=" . $port . "&action=" . $key;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }

    protected function wsUpdatePaymentAP511($fileName) {

        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/batch/au/payment/filename/?filename=" . $fileName . '&token=' . $token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);
            //dump('File AP511: ' . $fileName);
            //dump($resultResp);
            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }
    
    protected function wsUpdatePaymentAP511ByInvoices($filename,$invoicenos) {
	// $invoicenos can beli ke : 60000302761900034,60000302761900036,60000302761900055

        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/batch/au/paymentadhoc/?filename=" . $filename . '&invoicenos=' . $invoicenos.'&token=' . $token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);
            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }
    
    protected function wsSyncSmRolePersonnelUser($personnelId) {
	// $invoicenos can beli ke : 60000302761900034,60000302761900036,60000302761900055

        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-sm/sync-role?personnel_id=$personnelId&token=$token&";
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }
    
    protected function wsSyncRoleUserEp($userId,$loginId,$orgTypeId) {
	// $invoicenos can beli ke : 60000302761900034,60000302761900036,60000302761900055

        try {
           //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

            //$token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-pm/sso_liferay_role/sync";

            $isSupplierUser = false;
            // if($orgTypeId == 15){
            //     $isSupplierUser = true;
            // }
            $options = [
                    'json' => [
                        'userId' => $userId,
                        'loginId' => $loginId,
                        'isSupplierUser' => $isSupplierUser,
                    ]
                ];
            $response = Guzzle::post($url, $options);

            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }
    
    protected function wsSendRequestSoftcertToProvider($personnelId, $softcertRequestId,$softcertProvider) {
	
        //http://**************:8080/ep-support-middleware/ep/ejb-sm/request-certificate?personnel_id=9356315&softcert_request_id=139484&softcert_provider=TG
            
        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-sm/request-certificate?personnel_id=$personnelId&softcert_request_id=$softcertRequestId&softcert_provider=$softcertProvider&token=$token";
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }

    protected function wsRetriggerApiveSupplier($supplierId) {
	  
        //POST http://192.168.63.232:6060/ep-support-middleware/ep/ejb-sm/retrigger-apive?supplier_id=45327

        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-sm/retrigger-apive?supplier_id=$supplierId";
            $response = Guzzle::post($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '+$ex->getMessage());
        }
    }
    
    protected function getEpErrorMessageByCode($code){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");  

            $url = $urlMiddleware."/ep/ejb/error?errorCode=".$code;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

}
