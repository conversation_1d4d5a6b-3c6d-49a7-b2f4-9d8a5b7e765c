<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use DB;
use App\EpSupportActionLog;

class HandleGFM020ErrorSchedule extends Command {
    
    use OSBService;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleGFM020Error';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Retrigger MMINF for error in MMINF (GFM-020)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d').' 00:00:00';
        $dateStart = $dtStartTime->subDay(1)->format('Y-m-d').' 00:00:00';

        //$dateStart = '2019-05-13 00:00:00';
        //$dateEnd = '2019-05-13 18:15:00';

        dump('Date Start: '.$dateStart);
        dump('Date End: '.$dateEnd);
        Log::info('Start in '.self::class .'Date Start: '.$dateStart.', Date End: '.$dateEnd);
        try {
            $list = $this->getListWsErrGFM020ByDateRange($dateStart, $dateEnd);
            $listData = collect($list);
            
            dump('Check Count: '.count($listData));
            $counter=0;
            foreach ($listData as $objData){
                if($objData->remarks_1 != null && strlen($objData->remarks_1)== 18){
                    $kodItem = $objData->remarks_1;
                    $mminfObj = DB::connection('oracle_nextgen_fullgrant')->table('di_mminf')
                            ->where('material_code',$kodItem)
                            ->orderBy('changed_date','desc')
                            ->first();
                    if($mminfObj){
                        DB::connection('oracle_nextgen_fullgrant')
                            ->table('di_mminf')
                            ->where('mminf_id', $mminfObj->mminf_id)
                            ->update([
                                'is_sent' => 0 
                                ]);
                    
                    dump('Update MMINF ID: '.$mminfObj->mminf_id.' , KodItem: '.$mminfObj->material_code.' on ChangedDate: '.$mminfObj->changed_date);
                    $counter ++;
                    }
                }
            }
            
            dump('Total Update MMINF Item: '.$counter);

            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , '
                    . 'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            Log::info($logsdata);
            dump($logsdata);

        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM020ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
