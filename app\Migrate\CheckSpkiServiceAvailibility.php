<?php

namespace App\Migrate;

use Log;
use DB;

use App\Services\Traits\SPKIService;
use App\SpkiLog;
use Carbon\Carbon;
use Exception;
use App\Model\Notify\NotifyModel;

class CheckSpkiServiceAvailibility
{
    use SPKIService;

    public static function run()
    {
        $preLog = self::class . ' Start ' . __FUNCTION__ . ' >> ';
        MigrateUtils::logDump($preLog . 'Starting ... ');
        $dtStartTime = Carbon::now();

        self::checkServiceAvailability();
        self::checkSigningSuccess();

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    protected static function checkServiceAvailability()
    {
        //TRUSTGATE
        $service = 'ChallengeQuestion';
        $provider = 'trustgate';

        $trustgateUserData = self::getTrustgateUser()[0];

        if ($trustgateUserData) {
            self::getProviderResponse($trustgateUserData, $service, $provider);
        } else {
            self::invalidRequestData($provider, $service);
        }

        //DIGICERT
        $service = 'GetChallengeQuestion';
        $provider = 'digicert';

        $digiCertUserData = self::getDigiCertUser()[0];

        if ($digiCertUserData) {
            self::getProviderResponse($digiCertUserData, $service, $provider);
        } else {
            self::invalidRequestData($provider, $service);
        }
    }

    protected static function checkSigningSuccess()
    {
        //TRUSTGATE
        $service = 'SignIn';
        $provider = 'trustgate';

        $trustgateUserData = (object)[
            'prn' => '741203065567',
            'orn' => 'eP-1008I03E7',
            'pin' => 'cDc12345',
            'question' => 'Which is your favourite food?',
            'answer' => 'cdcep',
            'data' => 'test'
        ];

        if ($trustgateUserData) {
            self::getProviderResponse($trustgateUserData, $service, $provider);
        } else {
            self::invalidRequestData($provider, $service);
        }

        //TRUSTGATE
        $service = 'SignIn';
        $provider = 'digicert';

        $digiCertUserData = (object)[
            'prn' => '770907025320',
            'orn' => 'eP-1008I03E7',
            'pin' => 'cDc12345',
            'question' => 'What was your childhood nickname?',
            'answer' => 'cdcep',
            'data' => 'test'
        ];

        if ($digiCertUserData) {
            self::getProviderResponse($digiCertUserData, $service, $provider);
        } else {
            self::invalidRequestData($provider, $service);
        }
    }

    protected static function getProviderResponse($userData, $service, $provider)
    {
        try {
            $icNumber = $userData->prn;
            $epNumber = $userData->orn;
            $pin = null;
            $question = null;
            $answer = null;
            $data = null;

            if ($service === 'SignIn') {
                $pin = $userData->pin;
                $question = $userData->question;
                $answer = $userData->answer;
                $data = $userData->data;
            }

            $result = null;

            if ($provider === 'trustgate') {
                $selectedQuestion = 10; //default

                $th = new CheckSpkiServiceAvailibility;
                $result = $th->trustgateService($service, $icNumber, $epNumber, $selectedQuestion, null, $pin, $question, $answer, $data);
            } else if ($provider === 'digicert') {
                $th = new CheckSpkiServiceAvailibility;
                $result = $th->digicertService($service, $icNumber, $epNumber, null, $pin, $question, $answer, $data);
            }

            if ($result) {
                if ($result['status'] == '') {
                    $result = ['status' => 'failed', 'result' => "no response data"];
                }
                if (empty($result['result'])) {
                    $result = ['status' => 'failed', 'result' => $result['result']];
                }
                if (!empty($result['result']) && $service === 'SignIn') {
                    $resultData = $result['result'];
                    if (is_array($resultData) && isset($resultData['code']) && $resultData['code'] != 1) {
                        $result = ['status' => 'failed', 'result' => implode(', ', $result['result'])];
                    }
                }
                if ($result['status'] === 'failed') {
                    $msg = self::message($provider, $service, $result);
                    self::sendNotifyCheck($msg, $provider, $service);
                }
                self::storeServiceLog($provider, $service, $userData, $result);
            }

            MigrateUtils::logDump($provider . ' > ' . $result['status']);
        } catch (Exception $e) {
            $result = [
                'status' => 'failed',
                'result' => $e->getMessage(),
            ];
            $msg = self::message($provider, $service, $result);
            self::sendNotifyCheck($msg, $provider, $service);
            self::storeServiceLog($provider, $service, $userData, $result);
        }
    }

    protected static function storeServiceLog($provider, $service, $request, $response)
    {

        $spkiLog = new SpkiLog;
        $spkiLog->provider = $provider;
        $spkiLog->service = $service;
        $spkiLog->status = strtolower($response['status']);
        $spkiLog->request_data = json_encode($request);
        $spkiLog->response_data = json_encode($response['result']);

        $spkiLog->save();
    }

    protected static function getTrustgateUser()
    {
        $query = "SELECT d.DIGI_CERT_ID,r.SOFTCERT_REQUEST_ID,d.valid_to,d.orn,d.prn 
        FROM pm_digi_cert d, SM_SOFTCERT_REQUEST r, PM_DIGI_SIGN s
        WHERE 
        d.SOFTCERT_REQUEST_ID  = r.SOFTCERT_REQUEST_ID
        AND valid_to > sysdate 
        AND r.user_id = s.created_by 
        AND r.record_status = 1 
        AND r.softcert_provider = 'TG'
        AND rownum < 2";

        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    protected static function getDigiCertUser()
    {
        $query = "SELECT d.DIGI_CERT_ID,r.SOFTCERT_REQUEST_ID,d.valid_to,d.orn,d.prn 
        FROM pm_digi_cert d, SM_SOFTCERT_REQUEST r, PM_DIGI_SIGN s
        WHERE 
        d.SOFTCERT_REQUEST_ID  = r.SOFTCERT_REQUEST_ID
        AND valid_to > sysdate 
        AND r.user_id = s.created_by 
        AND r.record_status = 1 
        AND r.softcert_provider = 'DG'
        AND rownum < 2";

        return DB::connection('oracle_nextgen_rpt')->select($query);
    }

    protected static function invalidRequestData($provider, $service)
    {
        $userData = null;
        $result = ['status' => 'failed', 'result' => 'invalid request data'];
        self::storeServiceLog($provider, $service, $userData, $result);
    }

    protected static function message($provider, $service, $result)
    {
        $status = $result['status'];
        $responseData = $result['result'];

        $msg = "*ALERT: SPKI MONITORING*\n\n*" . strtoupper($provider) . " | $service - " . strtoupper($status) . "!*\n\n*Response Data:*\n$responseData at " . Carbon::now()->format('Y-m-d H:i:s');

        return $msg;
    }

    protected static function sendNotifyCheck($message, $provider, $service)
    {
        $currentTime = Carbon::now();
        $startTime = Carbon::createFromTime(7, 0, 0);
        $endTime = Carbon::createFromTime(23, 0, 0);

        // Set values based on time window
        if ($currentTime->between($startTime, $endTime)) {
            $consecutiveFailures = 2;
            $minuteFailures = 10;
        } else {
            $consecutiveFailures = 6;
            $minuteFailures = 30;
        }

        $spkiLogLimit = $consecutiveFailures;

        $logs = SpkiLog::where('provider', $provider)
            ->where('service', $service)
            ->orderBy('created_at', 'desc')
            ->limit($spkiLogLimit)
            ->get();

        // Count consecutive failures
        $failedLogsCount = 0;
        foreach ($logs as $log) {
            if ($log->status == 'failed') {
                $failedLogsCount++;
                if ($failedLogsCount >= $consecutiveFailures) {
                    break; // No need to continue counting once the required consecutive failures are found
                }
            } else {
                $failedLogsCount = 0; // Reset count if a success log is encountered
            }
        }

        $lastNotifyTime = NotifyModel::where('source_remark', 'Monitoring SPKI Service Availibility')
            ->where('message', 'LIKE', "%$provider | $service%")
            ->orderBy('date_entered', 'desc')
            ->first();

        $lastSuccess = SpkiLog::where('provider', $provider)
            ->where('service', $service)
            ->where('status', 'success')
            ->orderBy('created_at', 'desc')
            ->first();

        $msgSuccess = '';
        $msgSuccess = ($service === 'SignIn') ? 'Signing' : (($service === 'GetChallengeQuestion' || $service === 'ChallengeQuestion') ? 'Getting Challenge Question' : '');
        $message .= "\n\n*Last Successfully $msgSuccess:*\n" . $lastSuccess->created_at . " (" . Carbon::parse($lastSuccess->created_at)->diffForHumans() . ")";

        if ($failedLogsCount >= $consecutiveFailures && (!$lastNotifyTime || Carbon::parse($lastNotifyTime->date_entered)->diffInMinutes() >= $minuteFailures)) {
            $receivers = [];

            switch ($provider) {
                case 'trustgate':
                    $receivers = ['SPKI-TRUSTGATE'];
                    break;
                case 'digicert':
                    $receivers = ['SPKI-DIGICERT'];
                    break;
            }

            // Send notify
            self::sendWhatsappNotify($message, $receivers);
        }
    }

    private static function sendWhatsappNotify($message, $receivers)
    {
        foreach ($receivers as $receiver) {
            $notify = new NotifyModel;
            $notify->message = $message;
            $notify->receiver_group = $receiver;
            $notify->process = 'notify group'; // 'notify group , 'notify personal
            $notify->status = 0;
            $notify->sent = 0;
            $notify->date_entered = Carbon::now();
            $notify->retry = 0;
            $notify->source_app = 'EPSS';
            $notify->source_class = __CLASS__;
            $notify->source_remark = 'Monitoring SPKI Service Availibility';
            $notify->save();
        }
    }
}
