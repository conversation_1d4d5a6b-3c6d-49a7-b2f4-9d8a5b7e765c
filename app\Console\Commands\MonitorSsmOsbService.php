<?php

namespace App\Console\Commands;

use App\Migrate\MigrateUtils;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Services\Traits\OSBService;
use App\Model\Notify\NotifyModel;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class MonitorSsmOsbService extends Command
{

    use OSBService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-ssm-osb-service';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will monitor SSM Osb Service with error code 70005';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()
        ]);
        try {
            $getSsmOsbLogService = $this->getStatisticSSMErrorLog();
            $maxErrorDatetime = null;
            $maxSuccessDatetime = null;
            $current = Carbon::now();
            $last10Minutes = Carbon::now()->subMinute(10);

            $errorStatusCodes = [70004, 70005];

            foreach ($getSsmOsbLogService as $obj) {
                if (in_array($obj->status_code, $errorStatusCodes)) {
                    $maxErrorDatetime = Carbon::parse($obj->max_datetime);
                }
                if ($obj->status_code == 70060) {
                    $maxSuccessDatetime = Carbon::parse($obj->max_datetime);
                }

                if (in_array((int)$obj->status_code, $errorStatusCodes) && $maxErrorDatetime < $current && $maxErrorDatetime >= $last10Minutes) {
                    Log::info('Current Time : ' . $current);
                    Log::info('Time 10 Minute Ago : ' . $last10Minutes);
                    Log::info('Trans Date : ' . $maxErrorDatetime);

                    if ($maxErrorDatetime > $maxSuccessDatetime) {
                        $this->setAlertError($obj->status_desc, $maxErrorDatetime, $maxSuccessDatetime);
                        $this->setAlertLastSuccess($maxSuccessDatetime);
                    }
                }
            }
        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }

    private function setAlertError($errorDesc, $maxErrorDatetime, $maxSuccessDatetime)
    {
        $msg = "
*[ALERT] ePerolehan System failed to connect with SSM Service* 
*Error Description:* $errorDesc 
*Last Error Datetime:* $maxErrorDatetime
*Last Success Datetime:* $maxSuccessDatetime
Please rectify this issue. Thank you.";

        // Save notify to table
        $this->saveNotify('SSM_ERROR_CODE_70005', collect(['msg' => $msg]));
    }

    private function setAlertLastSuccess($maxSuccessDatetime)
    {
        $maxSuccessDatetime = Carbon::parse($maxSuccessDatetime)->format('j F Y g:i A');

        $msg = "
*[‼MAKLUMAN] Gangguan Perkhidmatan Integrasi SSM [MAKLUMAN‼]* 
- Adalah dimaklumkan pada masa sekarang telah berlaku gangguan perkhidmatan integrasi SSM.
- Gangguan perkhidmatan ini telah memberi impak kepada penciptaan permohonan Modul Pengurusan Pembekal (SM) Sistem ePerolehan.
- Pihak Teknikal akan berhubung dengan pihak SSM bagi mendapatkan penjelasan lanjut dan memaklumkan semula jika isu telah berjaya diselesaikan. Sebarang kesulitan amatlah dikesali. Terima kasih.
*Kali terakhir integrasi berjaya: $maxSuccessDatetime*";

        $this->saveNotify('SSM_ALERT_EPEROLEHAN', collect(['msg' => $msg]));
    }

    public function saveNotify($receiver, $collect)
    {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group'; // 'notify group , 'notify personal
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring ssm error code 70005';
        $nty->save();
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error SSM ERROR CODE 70005'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    //->cc($data["cc"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . __METHOD__ . ' ' . json_encode(['Email' => $data["to"], 'ERROR' => $e->getMessage()]));
        }
    }
}
