<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class Ap511InvoiceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Display the AP511 Invoice Check dashboard
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function dashboard(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month'); // Default to null (All Months)

        // Get summary statistics
        $statistics = $this->getSummaryStatistics($year, $month);

        // Get available years for filter dropdown
        $availableYears = $this->getAvailableYears();

        return view('ap511_check_invoice.dashboard', compact('statistics', 'availableYears', 'year', 'month'));
    }

    /**
     * Display the invoice list with DataTable
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function invoiceList(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month'); // Default to null (All Months)

        // Get available years for filter dropdown
        $availableYears = $this->getAvailableYears();

        return view('ap511_check_invoice.invoice_list', compact('availableYears', 'year', 'month'));
    }

    /**
     * Get summary statistics for dashboard
     *
     * @param int $year
     * @param int|null $month
     * @return array
     */
    private function getSummaryStatistics($year, $month = null)
    {
        $query = "SELECT 
            YEAR(inv_date_created) AS 'YEAR',
            MONTH(inv_date_created) AS 'MONTH',
            is_ap511 AS 'IS_AP511',
            COUNT(id) AS 'TOTAL'
        FROM ep_support.ep_invoice_check 
        WHERE YEAR(inv_date_created) = ?";
        
        $params = [$year];
        
        if ($month) {
            $query .= " AND MONTH(inv_date_created) = ?";
            $params[] = $month;
        }
        
        $query .= " GROUP BY 1, 2, 3 ORDER BY 1, 2, 3";

        $results = DB::connection('mysql_ep_support')->select($query, $params);
        
        // Process results into a more usable format
        $processed = [
            'total_invoices' => 0,
            'ap511_invoices' => 0,
            'non_ap511_invoices' => 0,
            'monthly_breakdown' => []
        ];

        foreach ($results as $row) {
            $monthKey = $row->MONTH;
            $isAp511 = $row->IS_AP511;
            $total = $row->TOTAL;

            if (!isset($processed['monthly_breakdown'][$monthKey])) {
                $processed['monthly_breakdown'][$monthKey] = [
                    'month' => $monthKey,
                    'month_name' => Carbon::create()->month($monthKey)->format('F'),
                    'total' => 0,
                    'ap511' => 0,
                    'non_ap511' => 0
                ];
            }

            $processed['monthly_breakdown'][$monthKey]['total'] += $total;
            $processed['total_invoices'] += $total;

            if ($isAp511 == 1) {
                $processed['monthly_breakdown'][$monthKey]['ap511'] += $total;
                $processed['ap511_invoices'] += $total;
            } else {
                $processed['monthly_breakdown'][$monthKey]['non_ap511'] += $total;
                $processed['non_ap511_invoices'] += $total;
            }
        }

        return $processed;
    }

    /**
     * Get available years from the database
     *
     * @return array
     */
    private function getAvailableYears()
    {
        $query = "SELECT DISTINCT YEAR(inv_date_created) as year 
                  FROM ep_support.ep_invoice_check 
                  WHERE inv_date_created IS NOT NULL 
                  ORDER BY year DESC";
        
        $results = DB::connection('mysql_ep_support')->select($query);
        
        return array_column($results, 'year');
    }

    /**
     * AJAX endpoint for DataTables
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getInvoiceData(Request $request)
    {
        // Clean and validate input
        $year = $request->get('year');
        $month = $request->get('month');
        $start = intval($request->get('start', 0));
        $length = intval($request->get('length', 10));
        $searchValue = '';
        
        // Handle search parameter safely
        $search = $request->get('search');
        if (is_array($search) && isset($search['value'])) {
            $searchValue = $search['value'];
        }
        
        // Clean year and month parameters (handle array or HTML entities)
        if (is_array($year)) {
            $year = $year[0] ?? null;
        }
        if (is_array($month)) {
            $month = $month[0] ?? null;
        }
        
        // Decode HTML entities and clean
        $year = html_entity_decode($year);
        $month = html_entity_decode($month);
        
        // Remove any quotes or brackets
        $year = trim($year, '"[]');
        $month = trim($month, '"[]');
        
        // Validate year and month
        if ($year && (!is_numeric($year) || $year < 2000 || $year > 2050)) {
            $year = null;
        }
        if ($month && (!is_numeric($month) || $month < 1 || $month > 12)) {
            $month = null;
        }

        // Base query for counting total records
        $baseQuery = DB::connection('mysql_ep_support')->table('ep_invoice_check');
        
        // Get total count (all records)
        $totalRecords = $baseQuery->count();

        // Build filtered query
        $query = DB::connection('mysql_ep_support')
            ->table('ep_invoice_check')
            ->select([
                'id',
                'poco_no',
                'pa_no',
                'is_pa_no_exist',
                'inv_no',
                'fl_order_id',
                'fl_req_id',
                'status_name',
                'status_id',
                'is_ap511',
                'file_name',
                'payment_reference_no',
                'inv_date_created',
                'payment_date',
                'cancelation_date',
                'payment_advice_no',
                'created_at'
            ]);

        // Apply year filter
        if ($year) {
            $query->whereYear('inv_date_created', $year);
        }

        // Apply month filter
        if ($month) {
            $query->whereMonth('inv_date_created', $month);
        }

        // Apply search filter
        if ($searchValue) {
            $query->where(function($q) use ($searchValue) {
                $q->where('poco_no', 'like', "%{$searchValue}%")
                  ->orWhere('pa_no', 'like', "%{$searchValue}%")
                  ->orWhere('inv_no', 'like', "%{$searchValue}%")
                  ->orWhere('file_name', 'like', "%{$searchValue}%")
                  ->orWhere('payment_reference_no', 'like', "%{$searchValue}%")
                  ->orWhere('status_name', 'like', "%{$searchValue}%");
            });
        }

        // Get filtered count (before pagination)
        $filteredRecords = $query->count();

        // Apply pagination
        $results = $query->orderBy('id', 'desc')
            ->skip($start)
            ->take($length)
            ->get();

        // Format the data
        $data = [];
        foreach ($results as $row) {
            // Safe date formatting function
            $formatDate = function($date) {
                if (!$date || $date === '0000-00-00 00:00:00') {
                    return '-';
                }
                try {
                    return Carbon::parse($date)->format('d-M-Y H:i:s');
                } catch (\Exception $e) {
                    return '-';
                }
            };

            $data[] = [
                'id' => $row->id,
                'poco_no' => $row->poco_no ?: '-',
                'pa_no' => $row->pa_no ?: '-',
                'is_pa_no_exist_text' => $row->is_pa_no_exist == 1 ? 
                    '<span class="badge badge-success">Yes</span>' : 
                    '<span class="badge badge-danger">No</span>',
                'inv_no' => $row->inv_no ?: '-',
                'fl_order_id' => $row->fl_order_id ?: '-',
                'fl_req_id' => $row->fl_req_id ?: '-',
                'status_name' => $row->status_name ?: '-',
                'is_ap511_text' => $row->is_ap511 == 1 ? 
                    '<span class="badge badge-success">Yes</span>' : 
                    '<span class="badge badge-danger">No</span>',
                'file_name' => $row->file_name ?: '-',
                'payment_reference_no' => $row->payment_reference_no ?: '-',
                'inv_date_created' => $formatDate($row->inv_date_created),
                'payment_date' => $formatDate($row->payment_date),
                'payment_advice_no' => $row->payment_advice_no ?: '-'
            ];
        }

        return response()->json([
            'draw' => intval($request->get('draw')),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $filteredRecords,
            'data' => $data,
            'debug' => [
                'year' => $year,
                'month' => $month,
                'search' => $searchValue,
                'start' => $start,
                'length' => $length
            ]
        ]);
    }

    /**
     * AJAX endpoint for dashboard statistics
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getDashboardData(Request $request)
    {
        $year = $request->get('year', Carbon::now()->year);
        $month = $request->get('month');

        $statistics = $this->getSummaryStatistics($year, $month);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }
}