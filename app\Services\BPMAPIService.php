<?php

namespace App\Services;

class BPMAPIService {

    /*
     *  0       STATE_INITIATED
        1	STATE_OPEN_RUNNING
        2	STATE_OPEN_SUSPENDED
        3	STATE_OPEN_FAULTED
        4	STATE_CLOSED_PENDING_CANCEL
        5	STATE_CLOSED_COMPLETED
        6	STATE_CLOSED_FAULTED
        7	STATE_CLOSED_CANCELLED
        8	STATE_CLOSED_ABORTED
        9	STATE_CLOSED_STALE
        10	STATE_CLOSED_ROLLED_BACK
     */
public static $BPM_STATE = array(
        1 => 'Running',
        2 => 'Suspended',
        3 => 'Faulted',
        4 => 'Cancel',
        5 => 'Completed',
        6 => 'Faulted',
        7 => 'Cancelled',
        8 => 'Terminated',
        9 => 'Stale',
        10 => 'Faulted'
    );
    
    public static $WORKFLOW_STATE = array(
        0 => 'Alerted',
        1 => 'Assigned',
        2 => 'Completed',
        3 => 'Faulted',
        4 => 'ERRORED',
        5 => 'Expired',
        8 => 'Stale',
        9 => 'Suspended',
        10 => 'Withdrawn'
    );

    public static $BPM_STATE_ICON = array(
        
        1 => " ",
        2 => "pause-circle",
        3 => "times-circle",
        4 => "ban-circle",
        5 => "check-circle",
        6 => "check-circle",
        7 => "ban-circle",
        8 => "minus-circle",
        9 => "minus-circle",
        10 => "times-circle"
        
    );
    
    public static $WORKFLOW_STATE_ICON = array(
        0 => 'exclamation',
        1 => " ",
        2 => "check-circle",
        3 => "times-circle",
        4 => "question-circle",
        5 => "clock-o",
        8 => "trash",
        9 => "pause-circle",
        10 => "trash"
        
    );
    
    public static $BPM_STATE_STYLE = array(        
        1 => "font-weight:bold; font-style:italic",
        2 => "color:blue;",
        3 => "color:red;",
        4 => "color:red;",
        5 => "color:green;",
        6 => "color:red;",
        7 => 'color:red;',
        8 => "color:grey;",
        9 => "color:grey;",
        10 => "color:red;"
        
    );
    
    public static $WORKFLOW_STATE_STYLE = array(
        0 => 'color:red',
        1 => 'font-weight:bold; font-style:italic',
        2 => "color:green;",
        3 => "color:red;",
        4 => "font-weight:bold; color:grey;",
        5 => "color:grey;",
        8 => "color:grey;",
        9 => "color:blue;",
        10 => "color:grey;"
    );
    
    public static $BPM_STATUS = array(        
        0 => 'Running',
        1 => 'Running',
        2 => 'Completed',
        3 => 'Running',
        4 => 'Terminated',
        5 => 'Suspended',
        6 => 'Stale'
    );
    
    public static $BPM_STATUS_ICON = array(
        0 => 'play-circle',
        1 => 'play-circle',
        2 => 'check-circle',
        3 => 'play-circle',
        4 => 'stop-circle',
        5 => 'stop-circle',
        6 => 'stop-circle'
    );
    
    public static $BPM_STATUS_STYLE = array(
        0 => 'color:blue;',
        1 => 'color:blue;',
        2 => 'color:green;',
        3 => 'color:blue;',
        4 => 'color:grey',
        5 => 'color:grey',
        6 => 'color:grey'
    );
    
    public static $BPM_STATUS_INSTANCE_QUERY = array(
        'FAULTED' => 'Faulted',
        'SUSPENDED' => 'Suspended',
        'RUNNING' => 'Running',
        'COMPLETED' => 'Completed',
        'TERMINATED' => 'Terminated',
        'RECOVERY' => 'Recovery'
    );
    
    public static $BPM_ICON_STATUS_INSTANCE_QUERY_ = array(
        'FAULTED' => 'times-circle',
        'RUNNING' => '',
        'SUSPENDED' => '',
        'COMPLETED' => 'check-circle',
        'TERMINATED' => 'minus-circle',
        'RECOVERY' => 'exclamation'
    );
    
    public static $BPM_STYLE_STATUS_INSTANCE_QUERY_ = array(
        'FAULTED' => 'color:red;',
        'RUNNING' => '',
        'SUSPENDED' => '',
        'COMPLETED' => 'color:green;',
        'TERMINATED' => 'color:grey;',
        'RECOVERY' => 'color:red;'
    );

    public static $BPM_PROCESS_EVALUATION = array(
        'tec_evaluation' => 'TEC Evaluation',
        'fec_evaluation' => 'FEC Evaluation'  
    );
}