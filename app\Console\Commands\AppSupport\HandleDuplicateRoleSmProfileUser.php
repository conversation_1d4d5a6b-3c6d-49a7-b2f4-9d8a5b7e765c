<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;
Use App\Migrate\AppSupport\SmFixIssueSyncMofSupplierAdminRole;

class HandleDuplicateRoleSmProfileUser extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'handle-duplicate-role-sm-user';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To find duplicate role on sm profile user. One user_org_id should hava 1 user per role only. Delete record based on record_status.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
        public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            SmFixIssueSyncMofSupplierAdminRole::fixIssueToDeleteDuplicateRole();
            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
//            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
