<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;

class HandleSmSiteVisitTaskDuplicateData extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sm-remove-duplicate-site-visit-task';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To remove duplicate record on site visit task by appl ID';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $script = " 
                    SELECT a.SV_TASK_ID,sa.appl_id,a.created_date
                      FROM sm_sv_task a,sm_appl sa
                     WHERE 
                     a.appl_id = sa.appl_id 
                     AND a.appl_id 
                     IN (
                              SELECT   sst.appl_id
                                  FROM sm_sv_task sst, sm_appl sa
                                 WHERE sst.appl_id = sa.appl_id
                                   AND appl_no LIKE ('KB-%202%-%')
                                   AND sa.record_status = 1
                                HAVING COUNT (sst.sv_task_id) > 1
                              GROUP BY sst.appl_id)
                     AND NOT EXISTS (SELECT 1 FROM sm_sv_address WHERE sv_task_id = a.sv_task_id)   
                      AND a.sv_task_id = (SELECT MIN (b.sv_task_id)
                                             FROM sm_sv_task b
                                            WHERE b.appl_id = a.appl_id)    
                   
                ";
            $listRecords = DB::connection('oracle_nextgen_rpt')->select($script);
            
            MigrateUtils::logDump('Total records site visit task DUPLICATE: '.count($listRecords));

            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listRecords) { 
                $listSvTaskId = collect($listRecords)->pluck('sv_task_id')->toArray();
                DB::connection('oracle_nextgen_fullgrant')->table('SM_SV_TASK')
                        ->whereIn('sv_task_id',$listSvTaskId)
                        ->delete();
                MigrateUtils::logDump('Done delete records in SM_SV_TASK.');
            }); 
            
            $actionName = 'DeleteData';
            $actionType = 'Script';
            
            $dataParam = collect();
            $dataParam->put("action","Delete records in table SM_SV_TASK");
            $dataParam->put("table","SM_SV_TASK");
            $dataParam->put("criteria","Duplicate record in SM_SV_TASK by APPL_ID");
            $dataParam->put("remark","List duplicate record to be delete.");
            $dataParam->put("script_sql",$script);
            
            $dataLog = collect();
            $dataLog->put("remark","List duplicate record to be delete.");
            $dataLog->put("data",$listRecords);
            $dataLog->put("script_sql",$script);
            
            EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");
            MigrateUtils::logDump('Completed');
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
