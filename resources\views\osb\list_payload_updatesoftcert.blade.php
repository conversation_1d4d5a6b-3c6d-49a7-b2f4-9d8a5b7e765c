@extends('layouts.guest-dash')

@section('header')



<!-- END Search Form -->
@endsection

@section('content')

<style>
    code[class^="language-"], code[class*=" language-"], pre[class^="language-"], pre[class*=" language-"] {
        white-space: pre-line;
    }
</style>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> </h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              
              <p>Tiada rekod!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title">
            <h1><i class="fa fa-building-o"></i> <strong>Transaction Logs</strong></h1>
        </div>
        
        @if($listdata && count($listdata) > 0 )
        @foreach($listdata as $xml)
        <div class="row">
        <div class="col-lg-6">
        <div class="block">
            <!-- Customer Info Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-o"></i> <strong>Retry</strong> Detail</h2>
            </div>
            <div class="block-section text-center">
                
                <h3>
                    <strong>{{$xml->service_name}}</strong><br><small></small>
                </h3>
            </div>
            <table class="table table-borderless table-striped table-vcenter">
                <tbody>
                    <tr>
                        <td class="text-right" style="width: 50%;"><strong>Retry Detail ID</strong></td>
                        <td>{{$xml->retry_dtl_id}}</td>
                    </tr>
                    <tr>
                        <td class="text-right"><strong>Target System</strong></td>
                        <td>{{$xml->target_system}}</td>
                    </tr>
                    <tr>
                        <td class="text-right"><strong>Retry No.</strong></td>
                        <td>{{$xml->retry_count}}</td>
                    </tr>
                    <tr>
                        <td class="text-right"><strong>Created Date</strong></td>
                        <td>{{$xml->created_date}}</td>
                    </tr>
                    <tr>
                        <td class="text-right"><strong>Trans ID</strong></td>
                        <td><a href="{{url('find/osb/detail/log')}}?cari={{$xml->trans_id}}">{{$xml->trans_id}}</a></td>
                    </tr>
                </tbody>
            </table>
        </div>
        </div>
        </div>
        
        <div class="block panel-xml" id="{{$xml->trans_id}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >><a href="{{url('find/osb/detail/log')}}?cari={{$xml->trans_id}}">{{$xml->trans_id}}</a></h2>                
            </div>
            <pre class="language-markup">
                <code  style="float: left; color:#fff;" >{{$xml->payload}}</code>
            </pre>
        </div>
        @endforeach
        @endif
     
        
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection


