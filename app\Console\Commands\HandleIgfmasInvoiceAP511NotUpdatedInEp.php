<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use SSH;
use DB;
use Guzzle;
use App\EpSupportActionLog;
use App\Migrate\ReTriggerFileIGFMAS;

class HandleIgfmasInvoiceAP511NotUpdatedInEp extends Command {
    

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'igfmas-ap511-update-payment-closed';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Program will check file AP511 already processed but missing invoice no still not updated in eP as Closed';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting .. '.$this->description);
        $dtStartTime = Carbon::now();
        
        try {
          
            $listInvoice =  DB::connection('mysql_ep_support')->table('ep_invoice_check')
                ->where('is_ap511','YES')->where('is_pa_no_exist','YES')
                ->take(100)->get();
            foreach ($listInvoice  as $invoiceObj){
                MigrateUtils::logDump(self::class . ' Found ap511 file still not process '.$invoiceObj->file_name);
                $fileName = json_decode($invoiceObj->file_name)[0];
                $fileNameWithoutGPG  = str_replace(".GPG","",$fileName);
                MigrateUtils::logDump(self::class . ' Searching file to make sure already in Archived : '.$fileName);
                $listfiles =  ReTriggerFileIGFMAS::getListFileNameIGFMASArchivedIN($fileNameWithoutGPG, "-30");
                //We will proceed if this file exist in Archived/IN
                if(count($listfiles) > 0){
                    MigrateUtils::logDump(self::class . ' found this files already in archived .. ');
                    $this->triggerAP511($fileName);
                    break;
                }else{
                    MigrateUtils::logDump('Checking this file is not in folder  /batch/1GFMAS/ARCHIVE/IN  ');
                }

            }

            /** Detect list AP511 files not process yet */
            $listFileAp511 = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM (
                SELECT tmp.*,
                 24 * ( trunc(process_date) - trunc(CREATED_DATE)) as diff_hours
                FROM (
                    SELECT to_char(created_date,'YYYY-MM') AS year_month, service_code,
                    (SELECT s.service_name FROM osb_service s WHERE s.service_code = obf.service_code ) AS service_name,
                    file_name,
                    CREATED_DATE ,
                    (SELECT min(changed_date) FROM DI_INTERFACE_LOG dil WHERE dil.service_code = obf.service_code AND dil.FILE_NAME = obf.FILE_NAME)  AS process_date
                    FROM OSB_BATCH_FILE obf WHERE service_code IN ('GFM-140')
                    AND  to_char(created_date,'YYYY') = to_char(sysdate,'YYYY')
                )  tmp 
                ) tt 
                WHERE process_date IS NULL 
                ORDER BY diff_hours DESC");
            MigrateUtils::logDump(__METHOD__ . ' found files not process :  '.count($listFileAp511 ));
            foreach($listFileAp511 as $objFile){
                $this->triggerAP511($objFile->file_name);
                //dd('Completed');
            }

            $logsdata = __METHOD__ . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);

        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__METHOD__ . ' >> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            MigrateUtils::logErrorDump(__METHOD__ . ' >> error happen!! ' . json_encode($err));
            MigrateUtils::logErrorDump(__METHOD__ . ' >> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
        }
        
    }

    protected function triggerAP511($fileName) {
        try {
            MigrateUtils::logDump(__METHOD__. ' trigger service :  '.$fileName);
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
            $token = base64_encode(strtotime("now"));
            MigrateUtils::logDump(__METHOD__ . ' trigger service :  '.$fileName.' -> update closed');
            $url = $urlMiddleware."/batch/au/payment/filename/?filename=".$fileName.'&token='.$token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            $actionName = 'Integration-IGFMAS-AP511-Reprocess';
            $actionTypeLog = 'Web Service';
            $parameters =  collect([]);
            $parameters->put("file_name", $fileName);
            $parameters->put("action", "Request to reprocess file AP511 by batch file to update in eP");
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

            $actionData =  collect([]);
            $actionData->put("file_name", $fileName);
            $actionData->put("response", $resultResp);

            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed","Batch Program");

            if($resultResp != null && $resultResp['status'] == "Success"){
                $ids = DB::connection('mysql_ep_support')->select("select id from ep_invoice_check where inv_no in (SELECT  invoice_no FROM ep_invoice_detail WHERE  file_name = ?) AND is_ap511 = 'YES'", array($fileName));
                $collect =  collect($ids); 
                $listId = $collect->pluck('id');
                DB::connection('mysql_ep_support')->table('ep_invoice_check')->whereIn('id',$listId)->delete();
                MigrateUtils::logDump(__METHOD__. ' Total Invoice Deleted = '.count($listId));
            }
            MigrateUtils::logDump(__METHOD__ . ' File AP511: '.$fileName);
        } catch (\Exception $ex) {
            MigrateUtils::logErrorDump(__METHOD__.' FileName: '.$fileName.' >> '.$ex->getMessage());
            MigrateUtils::logErrorDump(__METHOD__.' '.$ex->getTraceAsString());
        }
    }    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleAP511TriggerSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
