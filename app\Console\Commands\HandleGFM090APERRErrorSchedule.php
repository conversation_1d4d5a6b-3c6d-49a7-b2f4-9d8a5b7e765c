<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\SupplierFullGrantService;
use SSH;
use DB;
use Response;
use Guzzle;
use App\EpSupportActionLog;

class HandleGFM090APERRErrorSchedule extends Command {
    
    use OSBService;
    use SupplierFullGrantService;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleGFM090APERRError';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Read APERR file (GFM-090) to find eP No. If exist eP No, resend back APIVE by update changed date SM_SUPPLIER ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__. ' starting .. '.$this->description, [ 'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        $dtStartTime = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        $dateEnd = $dtStartTime->format('Y-m-d H').':00:00';
        $dateStart = $dtStartTime->subHour(1)->format('Y-m-d H').':00:00';

        //$dateStart = '2018-06-25 00:00:00';
        //$dateEnd = '2018-06-28 00:00:00';

        MigrateUtils::logDump(__METHOD__. ' Date Start: '.$dateStart.', Date End: '.$dateEnd);
        try {
            $list = $this->getListOSBBatchFile('GFM-090',$dateStart, $dateEnd);
            $listData = collect($list);
            
            MigrateUtils::logDump(__METHOD__. ' Check Count: '.count($listData));
            $counter=0;
            foreach ($listData as $objData){
                MigrateUtils::logDump(__METHOD__. ' FileName : '.$objData->file_name);
                $fileName = $objData->file_name;
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
        
                $responseUrl = Guzzle::get($urlMiddleware."/decrypt-file?filename=$fileName");
                $response = Response::make($responseUrl->getBody(), 200);
                $content = $response->content();
                
                
                
                $checkExist = DB::connection('mysql_ep_support')->table('ep_osb_batch_file_aperr')
                            ->where('batch_file_id',$objData->batch_file_id)
                            ->count();
                if($checkExist == 0){
                    $ePNo = '';
                    $arrayContent = explode("\n", $content);
                    if(count($arrayContent) > 1){
                        $contentLine1 = $arrayContent[1];
                        $contentLine1   = str_replace("EP",'eP',$contentLine1); 
                        $ePNo   = substr($contentLine1, 1, 12);
                        $ePNo   = str_replace("EP",'eP',$ePNo);   
                    }        
                    DB::connection('mysql_ep_support')
                        ->table('ep_osb_batch_file_aperr')
                        ->insert([
                            'batch_file_id' => $objData->batch_file_id,
                            'trans_id' => $objData->trans_id,
                            'service_code' => $objData->service_code,
                            'file_name' => $objData->file_name,
                            'ep_no' => $ePNo,
                            'created_date' => $objData->created_date,
                            'file_data' => $content
                        ]);
                    MigrateUtils::logDump('  inserted : '.$objData->file_name);
                }else{
                    MigrateUtils::logDump('  already exist : '.$objData->file_name);
                }

                //dump('Content',$content);
                if($content != null && strlen($content) > 4){
                    $detectApiveEpNo = "eP-";   // We want to find Error because of send APIVE 
                    $detectBlocked = "currently blocked";

                    if (strpos($content, $detectApiveEpNo) !== false && strpos($content, $detectBlocked) !== false) {  // This mean , we detect content has "eP-" 
                        
                       
                        MigrateUtils::logDump(__METHOD__. " Checking... : This APERR file refer to APIVE");
                        $arrayContent = explode("\n", $content);
                        if(count($arrayContent) > 1){
                            $contentLine1 = $arrayContent[1];
                            $ePNo  = substr($contentLine1, 1, 12);

                            MigrateUtils::logDump(__METHOD__. ' eP No: '. $ePNo);
                            
                            $supplierObj = $this->getSMSupplierDetailByEpNo($ePNo);
                            if($supplierObj && $ePNo != 'eP-1001I0242'){
                                // skip resend if epno: eP-1001I0242
                                
                                $countEpNo = DB::connection('mysql_ep_support')->table('ep_action_log')->where('action_name','PatchDataSupplier')->where('action_parameter->ep_no',$ePNo)->whereDate('created_at',Carbon::now()->format('Y-m-d'))->count();

                                if($countEpNo <= 5){
                                    $updateFields = ['changed_date' => Carbon::now()->addMinutes(4),'changed_by' => 1]; 
                                    $logs = collect([]);
                                    $logQuery = $this->updateSMSupplier($supplierObj->supplier_id,$updateFields);
                                    $logs->put('action_sm_supplier',$logQuery);

                                    $parameters =  collect([]);            
                                    $parameters->put("remarks", $fileName);
                                    $parameters->put("patching", "supplier_changed_date");
                                    $parameters->put("ep_no", $ePNo);
                                    $parameters->put("error_file", $contentLine1);

                                    EpSupportActionLog::saveActionLog("PatchDataSupplier", "Script", $logs, $parameters, "Completed",'SchedulerAdmin');

                                    MigrateUtils::logDump(__METHOD__. ' '.json_encode($logs));
                                    $counter ++; 
                                }else{
                                    MigrateUtils::logDump(__METHOD__. " Checking already sent 5 times. Skip sent APIVE");
                                }
                            }
                        }
                    } else {
                        MigrateUtils::logDump(__METHOD__. " Content >> This APERR file not belong to APIVE");
                    }
                }
            }
            
            MigrateUtils::logDump(__METHOD__. ' Total Send back : '.$counter);
            
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , '
                    . 'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            MigrateUtils::logDump(__METHOD__. ' '.json_encode($logsdata));

        } catch (\Exception $exc) {
            MigrateUtils::logDump(__METHOD__. ' >> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            MigrateUtils::logErrorDump(__METHOD__. ' >> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
        }
        
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM090(APERR)ErrorSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
