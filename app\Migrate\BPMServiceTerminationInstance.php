<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\BPMService;
use Guzzle;
use GuzzleHttp\Client;

class BPMServiceTerminationInstance {

    use PayloadGeneratorService;
    use BPMService;
    use BpmApiService;
    use FulfilmentService;
    

    public static function mergeBpmInstanceOldVersion(){
        MigrateUtils::logDump(__METHOD__ .' Starting ... ');

        /** Get list Old version */
        $listCompositeVer = DB::connection('oracle_bpm_rpt')
                ->select("SELECT  SUBSTR(c.COMPOSITE_DN, 1, Instr(c.COMPOSITE_DN, '*', -1, 1) -1) AS composite_version   , c.COMPOSITE_DN,
                min(created_time),max(created_time),
                                count(c.id) AS TOTAL_COMPOSITE_INSTANCE
                                FROM COMPOSITE_INSTANCE c 
                                WHERE 
                                SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1)   IN (
                                    'default/Contract_Management!1.0.0',
                                    'default/Contract_Management!1.0.1',
                                    'default/Contract_Management!1.0.2',
                                    'default/Contract_Management!1.0.3',
                                    'default/Contract_Management!1.0.4',
                                    'default/Contract_Management!1.0.5',
                                    'default/Contract_Management!1.0.6',
                                    'default/Contract_Management!1.0.7',
                                    'default/Contract_Management!1.0.7.1',
                                    'default/Contract_Management!1.0.8',
                                    'default/Fulfilment!1.0.4',
                                    'default/Fulfilment!1.0.5',
                                    'default/Fulfilment!1.0.6',
                                    'default/Fulfilment!1.0.6.1',
                                    'default/Fulfilment!1.0.6.2',
                                    'default/Fulfilment!1.0.7.0',
                                    'default/Fulfilment!1.0.7.1',
                                    'default/Fulfilment!1.0.7.2',
                                    'default/Fulfilment!*******',
                                    'default/Fulfilment!1.0.7.4',
                                    'default/Fulfilment!1.0.7.5',
                                    'default/Fulfilment!1.0.7.6',
                                    'default/Order!1.0.4',
                                    'default/Order!1.0.6',
                                    'default/Order!1.0.7',
                                    'default/Order!1.0.7.1',
                                    'default/Order!1.0.7.2',
                                    'default/Order!*******',
                                    'default/Order!1.0.7.4',
                                    'default/Order!1.0.8',
                                    'default/Order!1.0.9'
                                ) 
                                GROUP BY SUBSTR(c.COMPOSITE_DN, 1, Instr(c.COMPOSITE_DN, '*', -1, 1) -1) , c.COMPOSITE_DN 
                                ORDER BY 1");

        MigrateUtils::logDump(__METHOD__ .' Total result listCompositeVer ... '.count($listCompositeVer));
        foreach ($listCompositeVer as $compVer ){
            MigrateUtils::logDump(__METHOD__ .' composite version : '.json_encode($compVer));

            MigrateUtils::logDump(__METHOD__ ." $compVer->composite_version >> get query details instance ... ");
            // Get list instance with tasks by each version instance
            $queryListInstance = "SELECT
                                        SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1) AS  COMPOSITE_VERSION, 
                                        c.*
                                    FROM COMPOSITE_INSTANCE c 
                                    WHERE c.COMPOSITE_DN = '$compVer->composite_dn'  ";
            MigrateUtils::logDump(__METHOD__ ." QUERY => $queryListInstance");

            $listInstance = DB::connection('oracle_bpm_rpt')->select($queryListInstance);

            MigrateUtils::logDump(__METHOD__ ." $compVer->composite_version >> Total result : ".count($listInstance));
            foreach($listInstance  as $instObj){

                $checkExist = DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_instance_old_ver')
                    ->where('composite_instance_id',$instObj->id)
                    ->count();
                if($checkExist == 0 ){
                    DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_composite_instance')
                    ->insert(
                        [
                            'composite_instance_id' =>  $instObj->id,
                            'composite_ecid' =>  $instObj->ecid,
                            'composite_created_date' =>  $instObj->created_time,
                            'composite_name' =>  $instObj->composite_version,
                            'composite_dn' =>  $instObj->composite_dn,
                            'composite_conversation_id' =>  $instObj->conversation_id,
                            'composite_source_name' =>  $instObj->source_name,
                            'composite_state' =>  $instObj->state,
                            'is_terminated' =>  null,
                            'remark_action' =>  null,
                            'status' =>  'PENDING',
                            'created_at' => Carbon::now(),
                            'changed_at' =>  null
                        ]
                          
                    );
                }
                
            }
        } 

        MigrateUtils::logDump(__METHOD__ .' Completed');
    }


    public static function terminateTasksBySelectionComposite(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_composite_instance as d')
            ->whereNull('is_terminated') 
            ->where('status','PENDING') 
            ->whereNotIn('composite_state',[16,1]) 
            ->whereNotIn('composite_instance_id',['20869237','24828898'])
            
            /* ->whereIn('composite_name',[
                'default/Order!1.0.9'
                ])  */
            //->take(1)
            ->get(); 
        $totalRec=count($listInstances);     
        MigrateUtils::logDump(__METHOD__.' > total found  '.$totalRec );
        sleep(2);
        //dd('done');
        $bpmServiceTerminationInstance = new BPMServiceTerminationInstance();
        $counter  = 1;
        foreach ($listInstances as $inst){
            
            MigrateUtils::logDump(__METHOD__." $counter/$totalRec) >> Start terminate: Created : ".$inst->composite_created_date.' ,State : '.$inst->composite_state.  ', instanceID : '.$inst->composite_instance_id. ' module: '.$inst->composite_name);
            
            $res = $bpmServiceTerminationInstance->submitTerminateInstance($inst->composite_name, $inst->composite_instance_id);
            MigrateUtils::logDump($res);
            if($res != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_composite_instance')
                ->where('composite_instance_id',$inst->composite_instance_id)
                ->update(
                    [
                        'status' =>  'COMPLETED',
                        'is_terminated' => 1,
                        'changed_at' => Carbon::now()
                    ]
                );
                if($counter === 1000){
                    MigrateUtils::logDump(__METHOD__.'Done Completed 1000 terminated!');
                    //return;
                }
                $counter ++;
            }else{
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_composite_instance')
                ->where('composite_instance_id',$inst->composite_instance_id)
                ->update(
                    [
                        'status' =>  'ERROR',
                        'changed_at' => Carbon::now()
                    ]
                );
            }
        }
        MigrateUtils::logDump(__METHOD__.' > Completed terminated:  '.count($listInstances) );
       
    }

    public static function terminateTaskByMrYepOrphanSelection($typeTerminate,$docType,$year,$monthNo,$limit,$terminateFlag,$componentName){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );
        $dateFormat = "2024";


        $listInstances = DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan');
        if(strlen($docType) > 0){
            $listInstances->where('doc_type',$docType) ; // can be DO or SQ
        }
        if(strlen($terminateFlag) > 0){
            $listInstances->where('is_terminate_instance',$terminateFlag);  // 0: available to terminate , 1: success terminate , 2: failed terminate
        }else{
            $listInstances->where('is_terminate_instance',0) ;
        }

        if(strlen($typeTerminate) > 0){
            $listInstances->where('user_comment',$typeTerminate) ; // MR-2024-1 , YEP-2024-2
        }
        
        if(strlen($year) > 0){
            $listInstances->whereRaw(" YEAR(component_creation_date) = '$year'");
        }

        if(strlen($monthNo) > 0){
            $listInstances->whereRaw("MONTH(component_creation_date) = '$monthNo'");
        }

        if(strlen($componentName) > 0){
            $listInstances->where('component_name',$componentName) ; // MR-2024-1 , YEP-2024-2
        }
          
        //->whereRaw("DATE_FORMAT(component_creation_date,'%Y-%m') = '$dateFormat '")
        //->where('composite_module','default/Order!*******')
        //->whereIn('component_name',['DeliveryOrderFulfilment','DOCancellation','DOModificationRequestSubmition','InvoiceAndPaymentCreation','ReSubmitOrderCancellation'])
        //->whereIn('component_name',['ContractRequestInitiation','DeliveryOrderFulfilment','InvoiceAndPaymentCreation','PHISContractRequestInitiation','PurchaseRequestCreation'])
        //->take(5) // set total record to get
        //->skip(400)
        $listInstances->select('component_creation_date','composite_id','composite_module','component_name','doc_no');
        $listInstances->take($limit);
        $listInstances = $listInstances->get();

        MigrateUtils::logDump(__METHOD__.'Total record found : '.count($listInstances));
        $successTerminate = 0;
        $failedTerminate = 0;

        $totalRec=count($listInstances);     
        MigrateUtils::logDump(__METHOD__.' > total found  '.$totalRec );
        sleep(2);
        $bpmServiceTerminationInstance = new BPMServiceTerminationInstance();
        $counter  = 1;
        foreach ($listInstances as $task){
            
            MigrateUtils::logDump(__METHOD__." $counter/$totalRec) >> Start terminate: Created : ".$task->component_creation_date.  ', instanceID : '.$task->composite_id. ' module: '.$task->composite_module);
            $counter++;
            $dtStartTimeLog = Carbon::now();
            $res = $bpmServiceTerminationInstance->submitTerminateInstance($task->composite_module, $task->composite_id);
            MigrateUtils::logDump($res);
            MigrateUtils::logDump(__METHOD__.' Completed --- Taken Time : '.  json_encode(MigrateUtils::getTakenTime($dtStartTimeLog)));
            if($res != null){
                DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')
                                ->where('composite_id',$task->composite_id)
                                ->where('composite_module',$task->composite_module)
                                ->where('component_name',$task->component_name)
                                ->where('doc_no',$task->doc_no)
                                ->update([
                                    'is_terminate_instance' => 1,
                                    'terminate_at' => Carbon::now(),
                                    'terminate_response' => json_encode($res)
                                        ]);
                MigrateUtils::logDump(__METHOD__.'RESULT >>> SUCCESS TERMINATE INSTANCE : '.$task->composite_id);
                $successTerminate++;

            }else{
                DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')
                                    ->where('composite_id',$task->composite_id)
                                    ->where('composite_module',$task->composite_module)
                                    ->where('component_name',$task->component_name)
                                    ->where('doc_no',$task->doc_no)
                                    ->update([
                                        'is_terminate_instance' => 2,
                                        'terminate_at' => Carbon::now(),
                                        'terminate_response' => json_encode($res)
                                            ]); 
                MigrateUtils::logDump(__METHOD__.'RESULT >>> FAILED! TERMINATE INSTANCE : '.$task->composite_id);
                $failedTerminate++;
            }
        }
        MigrateUtils::logDump(__METHOD__.' > Completed terminated:  '.count($listInstances) );
        MigrateUtils::logDump(__METHOD__.' >Success Terminate : '.$successTerminate);
        MigrateUtils::logDump(__METHOD__.' >Failed Terminate : '.$failedTerminate);
       
    }


    /**
     * Do not use this method unless you know what u doing. This will terminate all instances in BPM by selected year
     */
    public static function terminateBPMInstanceCreatedByYear($year) {
        MigrateUtils::logDump(__METHOD__.' entering... Year: '.$year);
        $listResult = self::getQueryComposite($year);

        MigrateUtils::logDump(__METHOD__.' result total  : '. $listResult->count());
       
        $bpmServiceTerminationInstance = new BPMServiceTerminationInstance();
        $counter = 0;
        foreach ($listResult as $data){
            $counter++;
            MigrateUtils::logDump(__METHOD__.' >> '.$counter.") >> createdDate=".$data->created_time.", componentInstanceID=".$data->id." ,compositeModule=".$data->composite_dn." ,state=".$data->state);
           
            $response = $bpmServiceTerminationInstance->submitTerminateInstance($data->composite_dn, $data->id);
            MigrateUtils::logDump(__METHOD__.' >> '.json_encode($response));
            
        }
        

        
    }

    protected static function getQueryComposite($year) {
        MigrateUtils::logDump(__METHOD__.' entering... Year: '.$year);

        $sql = DB::connection('oracle_bpm_rpt')->table('COMPOSITE_INSTANCE')
                ->whereRaw("to_char(CREATED_TIME,'YYYY') = $year")
                ->whereNotIn('state',[16])
                ->select('id','COMPOSITE_DN' ,'CREATED_TIME','state');
        $result = $sql->get();
        return $result;
    }


     /**
     * terminate list instance ID
     */
    public static function terminateBPMInstanceList() {
        MigrateUtils::logDump(__METHOD__.' entering... ');
        $collectInstances = collect([
            collect(['composite_instance_id' => '1723431', 'composite_version' => 'default/Codification!1.0.0' ])
        ]);
        MigrateUtils::logDump(__METHOD__.' result total  : '. $collectInstances->count());
       
        $bpmServiceTerminationInstance = new BPMServiceTerminationInstance();
        $counter = 0;
        foreach ($collectInstances as $data){
            $counter++;
            MigrateUtils::logDump(__METHOD__.' >> '.$counter.")  componentInstanceID=".$data->get('composite_instance_id')." ,compositeModule=".$data->get('composite_version'));
           
            $response = $bpmServiceTerminationInstance->submitTerminateInstance($data->get('composite_version'), $data->get('composite_instance_id'));
            MigrateUtils::logDump(__METHOD__.' >> '.json_encode($response));
            
        }
        

        
    }


}
