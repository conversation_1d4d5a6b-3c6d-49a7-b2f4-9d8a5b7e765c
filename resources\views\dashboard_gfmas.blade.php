@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <!-- END Search Form -->
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
        <div class="row">
            <div class="col-lg-4">
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Quotation Tender <strong>Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_qtmonitoring" class="widget-extra-full">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Quotation Tender <strong>Service Retry</strong>
                        </h5>
                    </div>
                    <div id="dash_qtServiceRetry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Simple Quote (SQ) <strong> Today Closed Time</strong>
                        </h5>
                    </div>
                    <div id="dash_SqCloseTimeDaily">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                 <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            List MMINF on pending send to 1GFMAS - <strong> BACKLOG MMINF</strong>
                        </h5>
                    </div>
                    <div id="dash_BackLogMMINF">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            User Creation in SSO & Liferay <strong>Monitoring </strong>
                        </h5>
                    </div>
                    <div id="dash_UserCreation">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Process Payment <strong>Monitoring </strong>
                        </h5>
                    </div>
                    <div id="dash_ProcessPayment">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Monitoring <strong>MyGPIS</strong>
                        </h5>
                    </div>
                    <div id="dash_monitoringMyGPIS">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
                <div id="dash_outbound" class="widget">
                    <div class="text-center" style="display:none; padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_inbound" class="widget">
                    <div class="text-center" style="display:none; padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                
                <!--div id="dash_quartz" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div -->
            </div>
            <div class="col-lg-4">
                <div id="dash_outbound_1gfmas" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_inbound_1gfmas" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_outbound_eperunding" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_outbound_mygpis" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_outbound_phis" class="widget">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_outbound_spa" class="widget"   style="display:none;">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div id="dash_outbound_lms" class="widget"   style="display:none;">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Scheduler Batch File Pending in Processing to eP - <strong>1GFMAS Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_checkBatchFilePending" style="padding: 20px;">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <div class="widget-options">
                            <div class="btn-group btn-group-xs">
                                <a href="javascript:void(0)" id="dash_EmailNotifications_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                            </div>
                        </div>
                        <h5 class='widget-content-light'>
                            Email Notifications <strong>Monitoring </strong>
                        </h5>
                    </div>
                    <div id="dash_EmailNotifications">
                        <div class="text-center" style="padding: 20px;">Do not click Refresh (Button) except to check backlog notification, Kindly click on after office hour.
                Do not click more than one time. Execute on query take a long time to finish.</div>
                    </div>
                </div>
                <div id="dash_interface" class="widget"  style="display:none;">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div class='widget' style="display:none;">
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB to EJB <strong>Monitor</strong>
                        </h5>
                    </div>
                    <div id="dash_ejbosb">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div class='widget' style="display:none;">
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Item Code Error Monitoring <strong> (GFM-100)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkWsItemCodeErrorInGFM100" style="padding: 20px;">
                        <div class="text-center" style="display:none"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget' style="display:none;">
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Item Code Error Monitoring <strong> (MMINF / GFM-020)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkWsItemCodeErrorInMMINF" style="padding: 20px;">
                        <div class="text-center"  style="display:none"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget' style="display:none;">
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Web Service Monitoring <strong> (Validation Exception)</strong>
                        </h5>
                    </div>
                    <div id="dash_ws_validation_exception">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin hide"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Telnet - <strong>Connection 1GFMAS Batch Server</strong>
                        </h5>
                    </div>
                    <div id="dash_checkConnection" style="padding: 20px;">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
            </div>
            <div class="col-lg-4">

                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Service Retry 
                                <span class="pull-right badge label-danger" id="totalcounts_osbServiceRetry" style="margin-right: 32px"></span> 
                                <span>
                                    <a href="{{url('/list/osb/batch/retry/trigger/service')}}"  target="_blank" 
                                       accesskey="" class="btn btn-xs btn-red" style="margin-left: 135px">Trigger</a>
                                </span>
                            </strong>
                        </h5>
                    </div>
                    <div id="dash_osbretry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Notification Retry</strong>
                        </h5>
                    </div>
                    <div id="dash_osbNotifyRetry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            OSB <strong>Batch Retry 
                                <span>
                                    <a href="{{url('/list/osb/batch/retry/trigger/batch')}}"  target="_blank" 
                                       accesskey="" class="btn btn-xs btn-red" style="margin-left: 145px">Trigger</a>
                                </span>
                                <span class="pull-right badge label-danger" id="totalcounts_osbBatchRetry" style="margin-right: 30px"></span></strong>
                        </h5>
                    </div>
                    <div id="dash_osbBatchRetry">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            Inbound Error File Monitoring <strong> (Return Error File)</strong>
                        </h5>
                    </div>
                    <div id="dash_checkFileErrorInbound" style="padding: 20px;">
                        <div class="text-center" ><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            IGFMAS Integration <strong>Today Monitoring Check Charging & PMQ</strong>
                        </h5>
                    </div>
                    <div id="dash_gfmasCheckChargingIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            SSM Integration <strong>Today Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_ssmIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <div class="widget-options">
                            <div class="btn-group btn-group-xs">
                                <a href="javascript:void(0)" id="dash_softcertMonitoring_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                            </div>
                        </div>
                        <h5 class='widget-content-light'>
                            Softcert Signing (Submition QT) <strong>Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_softcertMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <div class="widget-options">
                            <div class="btn-group btn-group-xs">
                                <a href="javascript:void(0)" id="dash_gpkiSigningMonitoring_refresh" class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
                            </div>
                        </div>
                        <h5 class='widget-content-light'>
                            GPKI Signing <strong>Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_gpkiSigningMonitoring">
                        <div class="text-center" style="padding: 20px;">Click refresh button</div>
                    </div>
                </div>
                <div class='widget'>
                    <div class='widget-extra themed-background-dark'>
                        <h5 class='widget-content-light'>
                            CRM eP Integration <strong>Monitoring</strong>
                        </h5>
                    </div>
                    <div id="dash_crmIntegrationMonitoring">
                        <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                
            </div>
        </div>

    @endif
    
    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                        <div class="pull-right">
                            <a href="{{ url("/list/1gfmas/folder") }}" target="_blank" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger Batch</a>
                        </div>
                    </div>
                    <div id="trigger_btn" class="row" style="padding: 0 15px 15px 0; display: none;" disabled>
                        <div class="pull-right">
                            <a id="trigger_url"></a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    <!-- END Content -->
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script>
    $('#page-container').removeAttr('class');
    </script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}
        
        App.datatables();
        /* Initialize Datatables */
        var tableListData =     $('#basic-datatable').DataTable({
                columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });
            
            
        $(document).ready(function () {
            
            $('#dash_softcertMonitoring_refresh').on("click", function(){
                $('#dash_softcertMonitoring').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
                
                $.ajax({
                    url: APP_URL + '/dashboard/checkMonitoringSoftcert',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_softcertMonitoring').hide().html($data).fadeIn();
                    }
                });
            });

            $('#dash_gpkiSigningMonitoring_refresh').on("click", function(){
                $('#dash_gpkiSigningMonitoring').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
                
                $.ajax({
                    url: APP_URL + '/dashboard/checkMonitoringSigningGpki',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_gpkiSigningMonitoring').hide().html($data).fadeIn();
                    }
                });
            });
            //
            
            $('#dash_EmailNotifications_refresh').on("click", function(){
                $('#dash_EmailNotifications').hide().html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
                
                $.ajax({
                    url: APP_URL + '/dashboard/emailNotificationsMonitoring',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_EmailNotifications').hide().html($data).fadeIn();
                    }
                });
            });
            //
            
            
            $('.widget').on("click",'.modal-list-data-action', function(){
                
                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();
                
                $('#modal-list-data-header').text($(this).attr('data-title'));
                if($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound'){
                    $('#fetch_btn').show();
                    $('#trigger_btn').hide();
                    console.log('fetch');
                }
                else if($(this).attr('data-url') === '/list/osb/batch/retry/AllService'){
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/service') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title=''\n\
                            data-original-title='Trigger All Service'\n\
                            > Trigger All Service</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger service');
                }else if($(this).attr('data-url') === '/list/osb/batch/retry/AllBatch'){
                    $('#trigger_btn').show();
                    $('#fetch_btn').hide();
                    $html = "<a href='{{ url('/list/osb/batch/retry/trigger/batch') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title='' \n\
                            data-original-title='Trigger All Batch'\n\
                            > Trigger All Batch</a>";
                    $("#trigger_url").html($html);
                    console.log('trigger batch');
                }else {
                    $('#trigger_btn').hide();
                    $('#fetch_btn').hide();
                    console.log('hide all');
                }
                
                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();
                
                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();
                        
                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });
                        
                        $('.spinner-loading').hide();
                    }
                });
                
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkQtMonitoring',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtmonitoring').hide().html($data).fadeIn();
                }
            });
            
            //onload apive outbound widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/apive/outbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound').hide().html($data).fadeIn();
                    $(".pie-chart").easyPieChart();
                }
            });
            */
           
            //onload quartz widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/quartz',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_quartz').hide().html($data).fadeIn();
                }
            });
            */
           
           
            $.ajax({
                url: APP_URL + '/dashboard/qtServiceRetry',
                success: function (data) {
                    $data = $(data);
                    $('#dash_qtServiceRetry').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/sqCloseTimeDaily',
                success: function (data) {
                    $data = $(data);
                    $('#dash_SqCloseTimeDaily').hide().html($data).fadeIn();
                }
            });
            
            
            
            //onload check ejbosb
            $.ajax({
                url: APP_URL + '/dashboard/osbretry',
                type: "GET",
                success: function (resp) {
                    $data = $(resp.data);
                    $('#dash_osbretry').hide().html($data).fadeIn();
                    
                    $totalcounts = resp.totalcounts[0]["totalcounts"];
                    $html = "<a href='#modal-list-data' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/list/osb/batch/retry/AllService' data-title='Details of OSB Service Retry' >" + $totalcounts + "</a>";
                    
                    $("#totalcounts_osbServiceRetry").html($html);
                }
            });
            
            $.ajax({
                    url: APP_URL + '/dashboard/checkMonitoringSoftcert',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_softcertMonitoring').hide().html($data).fadeIn();
                    }
                });
                
            //onload check BackLogMMINF 
            $.ajax({
                url: APP_URL + '/dashboard/backlogmminf',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_BackLogMMINF').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/osbnotifyretry',
                success: function (data) {
                    $data = $(data);
                    $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/osbbatchretry',
                success: function (resp) {
                    $data = $(resp.data);
                    $('#dash_osbBatchRetry').hide().html($data).fadeIn();
                    
                    $totalcounts = resp.totalcounts[0]["totalcounts"];
                    $html = "<a href='#modal-list-data' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/list/osb/batch/retry/AllBatch' data-title='Details of OSB Batch Retry' >" + $totalcounts + "</a>";
                    
                    $("#totalcounts_osbBatchRetry").html($html);
                }
            });
            
            
            /*    
            $.ajax({
                url: APP_URL + '/dashboard/emailNotificationsMonitoring',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_EmailNotifications').hide().html($data).fadeIn();
                }
            });
            */
           
            //onload apive outbound widget
            /*
            $.ajax({
                url: APP_URL + '/dashboard/apove/inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_inbound').hide().html($data).fadeIn();
                }
            });
            */
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringMyGPIS',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_monitoringMyGPIS').hide().html($data).fadeIn();
                }
            });
           
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/outbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_1gfmas').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/batch/outbound?batch_name=PHIS&length_filename=30',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_phis').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/batch/outbound?batch_name=ePerunding&length_filename=7',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_eperunding').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/batch/outbound?batch_name=MyGPIS&length_filename=5',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_mygpis').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/batch/outbound?batch_name=LMS&length_filename=20',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_lms').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/batch/outbound?batch_name=SPA&length_filename=20',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_spa').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/userCreation',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_UserCreation').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/processPayment',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ProcessPayment').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_inbound_1gfmas').hide().html($data).fadeIn();
                }
            });
           
            //onload apive diinterfacelog widget
            /* $.ajax({
                url: APP_URL + '/dashboard/apive/diinterfacelog',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_interface').hide().html($data).fadeIn();
                }
            });
            */
            //onload Scheduler Batch File Pending in Processing to eP
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/checkBatchFilePending',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkBatchFilePending').hide().html($data).fadeIn();
                }
            });            
            //onload check connection
            $.ajax({
                url: APP_URL + '/dashboard/checkConnection',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkConnection').hide().html($data).fadeIn();
                }
            });

            
            
            $.ajax({
                url: APP_URL + '/dashboard/checkFileErrorInbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkFileErrorInbound').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringSSMIntegration',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ssmIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringErrorCheckChargingReceived',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_gfmasCheckChargingIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkMonitoringIntegrationCRMeP',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_crmIntegrationMonitoring').hide().html($data).fadeIn();
                }
            });
            
            /*
            $.ajax({
                url: APP_URL + '/dashboard/checkWsItemCodeErrorInGFM100',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkWsItemCodeErrorInGFM100').hide().html($data).fadeIn();
                }
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/checkWsItemCodeErrorInMMINF',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_checkWsItemCodeErrorInMMINF').hide().html($data).fadeIn();
                }
            });
            */
            
            /*
             * Load too long. disable first 
            $.ajax({
                url: APP_URL + '/dashboard/checkwsvalidation',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ws_validation_exception').hide().html($data).fadeIn();
                }
            });
            */
          
            //onload check ejbosb
            /* Load too long. disable first  
            $.ajax({
                url: APP_URL + '/dashboard/checkejbosb',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_ejbosb').hide().html($data).fadeIn();
                }
            });
            */
            
            /*
            //interval: apive outbound widget
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apive/outbound',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_outbound').html($data);
                        $(".pie-chart").easyPieChart();
                    }
                });
            }, 300000);
            */
           
            //interval: quartz widget
            /*
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/quartz',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_quartz').html($data);
                    }
                });
            }, 30000);
            */
           
           /*
            //interval: apove inbound widget
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apove/inbound',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_inbound').hide().html($data).fadeIn();
                    }
                });
            }, 300000);
            */
           
           
            //interval: apive diinterfacelog widget
            /*
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/apive/diinterfacelog',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_interface').html($data);
                    }
                });
            }, 300000);
            */
            //interval: check connection
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkConnection',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkConnection').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //
            //interval: Scheduler Batch File Pending in Processing to eP
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/1gfmas/checkBatchFilePending',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkBatchFilePending').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //
            /*
            //interval: check ejb osb
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkejbosb',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_ejbosb').hide().html($data).fadeIn();
                    }
                });
            }, 840000); //14min
            */
           
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbretry',
                    success: function (resp) {
                        $data = $(resp.data);
                        $('#dash_osbretry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/backlogmminf',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_BackLogMMINF').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
           
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbnotifyretry',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_osbNotifyRetry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/osbbatchretry',
                    success: function (resp) {
                        $data = $(resp.data);
                        $('#dash_osbBatchRetry').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            /* 
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkwsvalidation',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_ws_validation_exception').hide().html($data).fadeIn();
                    }
                });
            }, 840000); //14min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkFileErrorInbound',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkFileErrorInbound').hide().html($data).fadeIn();
                    }
                });
            }, 300000); //5min
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkWsItemCodeErrorInGFM100',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkWsItemCodeErrorInGFM100').hide().html($data).fadeIn();
                    }
                });
            }, 1020000); //17min
            
            
            setInterval(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkWsItemCodeErrorInMMINF',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkWsItemCodeErrorInMMINF').hide().html($data).fadeIn();
                    }
                });
            }, 1020000); //17min
            
            */
            
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection
