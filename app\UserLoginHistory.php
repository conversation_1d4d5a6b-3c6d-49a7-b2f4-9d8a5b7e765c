<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class UserLoginHistory extends Model {
    protected $connection= 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_login_history";
    public $timestamps = false;
   
    public function user()
    {
        // return $this->hasOne('App\User', 'foreign_key', 'local_key');
        return $this->hasOne('App\User', 'id', 'user_id');
    }
}


