@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-notes_2"></i>GPKI Roaming Signing<br>
                <small>To Run Signing testing with GPKI Roaming</small>
            </h1>
        </div>
    </div>
    <div class="block-alt-noborder full block">
        <header class="w3-container w3-theme w3-padding" id="myHeader">
            <div class="w3-center" style="text-align: center;">
                <img src="{{ asset('img/GPKI.png') }}" alt="GPKI AGENT">
                <div class="w3-padding-30">
                    <button class="w3-btn w3-large w3-dark-grey w3-hover-light-grey">SIGN API</button>
                    <br><br>
                </div>
            </div>
        </header>
        <br>
        <div class="wrapper">
            <form align="center" autocomplete="off">

                <!-- allmedium -->
                <div id="reqNric">
                    Nama Aplikasi:
                    <input type="text" id="applicationCode" name="applicationCode" value="ePerolehan"><br><br>

                    <div id="btnSubmit">
                        ID Sijil Digital:
                        <input type="text" id="nric" name="nric" value="" maxlength="12"><br><br>

                        <input type="checkbox" id="mode" name="mode" value="Training">Mode Latihan<br><br>
                        <input type="button" id="requestMedium" value="Submit">
                    </div>

                </div>

                <!-- allmedium -->
                <div id="listMedium">
                    ID Sijil Digital:
                    <input type="text" id="nric2" name="nric2" readonly type="text"><br><br>
                    Medium:
                    <select id="mediumId" name="mediumId"></select>
                    <!-- <div id="optbatch"> -->
                    <input type="checkbox" id="batchsign" name="batchsign" value="batch">Batch Sign<br><br>
                    <!-- </div> -->
                    <input type="button" id="nextStep" value="Submit">
                </div>

                <!-- roaming otp only -->
                <div id="reqOtp">
                    ID Pengguna: (Berdaftar Mobile)<br>
                    <input type="text" id="userId" name="userId" maxlength="12"><br>
                    ID Sijil Digital:<br>
                    <input type="text" id="nric3" name="nric3" maxlength="12"><br>

                    Agency ID: <br>
                    <input type="text" id="agencyId" name="agencyId" value="GOVICT_484829"><br>

                    Tempoh Tamat OTP: (Saat) <br>
                    <input type="text" id="otpExp" name="otpExp" value="120" pattern="[0-9]+"><br>

                    Saluran Penerimaan OTP<br>
                    <input type="radio" id="1" name="saluran" value="mobil" checked>
                    <label for="mobil">Mobile</label>
                    <input type="radio" id="2" name="saluran" value="email">
                    <label for="email">E-mel</label><br><br>

                    <div id="reqEmail">
                        E-mel:<br>
                        <input type="text" id="emailuser" name="emailuser" value=""><br><br>
                    </div>

                    <input type="hidden" id="mediumId2" name="mediumId2">
                    <input type="button" id="requestOtp" value="Generate OTP">
                </div>

                <!-- for signing roaming otp only -->
                <div id="reqSign">

                    Id Transaksi : <br>
                    <input type="text" id="nonce" name="nonce" value=""><br>
                    Kod OTP: <br>
                    <input type="text" id="otpCode" name="otpCode" value=""><br>
                    Pin:<br>
                    <input type="password" id="pin" value="" maxlength="16"><br>
                    <br>
                    <input type="radio" id="1" name="signingType" value="data" checked>
                    <label for="data">Data Signing</label>
                    <input type="radio" id="2" name="signingType" value="hash">
                    <label for="hash">Hash Signing</label><br>
                    <br>Data To Sign: <br>
                    <textarea id="data" rows="10" cols="100">data</textarea> <br>
                    <input type="hidden" id="userId2" name="userId2">
                    <input type="hidden" id="nric4" name="nric4">
                    <br>
                    <input type="button" id="requestSign" value="Sign">
                </div>


                <!-- for signing token, softcert -->
                <div id="reqSignSoftcertToken">
                    ID:<br>
                    <input name="certID" value="" type="text" maxlength="12" id="certID"><br>
                    Pin:<br>
                    <input name="pin2" type="password" id="pin2" maxlength="16"><br>
                    <br>
                    <input type="radio" id="1" name="signingTypeSTR" value="data" checked>
                    <label for="data">Data Signing</label>
                    <input type="radio" id="2" name="signingTypeSTR" value="hash">
                    <label for="hash">Hash Signing</label><br>
                    <br>Data To Sign: <br>
                    <textarea name="textToSign" id="textToSign">data</textarea><br>
                    <input type="hidden" id="mediumId3" name="mediumId3">
                    <input type="button" name="signButton" id="signButton" value="Sign"><br>
                </div>

                <!-- for batch signing softcert,token, &roaming(if cert is active at agent)-->
                <div id="reqBatchSignSoftcertToken">
                    ID:<br>
                    <input name="idbatch" value="" type="text" maxlength="12" id="idbatch"><br>
                    Pin:<br>
                    <input name="pass" type="password" id="pass" maxlength="16"><br>
                    <br>
                    <input type="radio" id="1" name="batchType" value="data" checked>
                    <label for="data">Data Signing</label>
                    <input type="radio" id="2" name="batchType" value="hash">
                    <label for="hash">Hash Signing</label><br>
                    <br>Data To Sign: <br>
                    <textarea name="testdata" id="testdata">data</textarea> <br>
                    <textarea name="testdata" id="testdata1">data</textarea> <br>
                    <textarea name="testdata" id="testdata2">data</textarea> <br>
                    <input type="hidden" id="mediumId3" name="mediumId3">
                    <input type="button" name="signBatchButton" id="signBatchButton" value="Sign Batch"><br>
                </div>
                <div>
                    <div id="gpki-data" class="w3-container w3-small"></div>
                </div>

                <!-- for medium roaming only -->
                <div id="security_q">
                    <div id="image_sec"></div>
                    <label>ID: </label><br>
                    <input id="certID2" name="certID" type="text"><br><br>
                    <label>Pin:</label><br>
                    <input id="pin3" name="pin3" type="password" maxlength="16"> <br>
                    <br><input type="radio" id="1" name="signingTypeRoaming" value="data" checked>
                    <label for="data">Data Signing</label>
                    <input type="radio" id="2" name="signingTypeRoaming" value="hash">
                    <label for="hash">Hash Signing</label><br>
                    <br>Data To Sign: <br>
                    <textarea name="textToSign2" id="textToSign2">data</textarea><br>
                    <div id="roamingBatch">
                        <textarea name="roamingdata" id="roamingdata1">data</textarea><br>
                        <textarea name="roamingdata" id="roamingdata2">data</textarea><br>

                    </div>
                    <div id="securityconfirm">
                        <label>Security Question:</label><br>
                        <select id="question" name="question"></select><br><br>
                        <label>Security Answer:</label><br>
                        <input id="answer" name="answer" type="text" value=""><br><br>
                    </div>
                    <div id="roamingSingle">
                        <input id="n_proceed" type="button" name="Sign" value="Sign"> <br>
                    </div>

                    <div id="roamingBatch2">
                        <input id="n_proceed2" type="button" name="Sign Batch" value="Sign Batch"> <br>
                    </div>
                    <div></div>
                </div>

            </form>

            <!-- for roaming otp only -->
            <form id="batch" align="center" autocomplete="off">

                <div id="nextSign">
                    Next Signing <br>
                    Session Id : <br>
                    <input type="text" id="sessionId" name="sessionId" value="" disabled><br>
                    Data To Sign: <br>
                    <textarea name="datatosign" id="datatosign">data</textarea><br>
                    Batch Flag: <br>
                    <small>Batch Flag Indicator : 1 - Next data,2 - Last Data</small> <br>
                    <input type="text" id="dataSeq" name="dataSeq" value="1"><br><br>
                    <button type="submit" id="requestSignNext">Sign</button>
                </div><br>
                <div id="signResult"></div><br>
                <div id="signResultNext"></div><br>

            </form>
        </div>
        <br>

        <div class="wrapper">
            <button onClick="window.location.reload();">Refresh</button>
        </div>
    </div>
@endsection


@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
@endsection