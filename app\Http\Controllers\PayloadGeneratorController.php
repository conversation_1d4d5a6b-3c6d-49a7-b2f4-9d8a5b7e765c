<?php

namespace App\Http\Controllers;

use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\InvoiceService;
use App\Services\Traits\FulfilmentService;
use Illuminate\Http\Request;
use App\Services\Traits\BpmApiService;
use Log;
use DB;
use App\Services\Traits\ProfileService;

class PayloadGeneratorController extends Controller {

    use PayloadGeneratorService;
    use SupplierService;
    use InvoiceService;
    use FulfilmentService;
    use BpmApiService;
    use ProfileService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function findSimpleQuote() {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $simpleQuoteDetails = null;
        $orgDetails = null;
        $taskPerformerDetails = null;
        if ($carianTemp != null) {
            $simpleQuoteDetails = $this->getSimpleQuoteDetails($carianTemp);

            if ($simpleQuoteDetails) {
                $orgDetails = $this->getOrgProfileDetails($simpleQuoteDetails->org_profile_id);
                $taskPerformerDetails = $this->getTaskPerformerdetails($simpleQuoteDetails->created_by);
                $simpleQuoteDetails->start_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->start_date));
                $simpleQuoteDetails->end_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->end_date));
            }
        }

        if (isset($simpleQuoteDetails)) {
            return view('payload.payload_sq_creation', [
                'simpleQuoteDetails' => $simpleQuoteDetails,
                'orgDetails' => $orgDetails,
                'taskPerformerDetails' => $taskPerformerDetails,
                'carian' => $carianTemp]);
        } else {
            return view('payload.payload_sq_creation', [
                'simpleQuoteDetails' => $simpleQuoteDetails,
                'orgDetails' => $orgDetails,
                'taskPerformerDetails' => $taskPerformerDetails,
                'carian' => $carianTemp]);
        }
    }

    public function findInvoice() {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $invoiceDetails = null;
        $approverDetails = null;
        $supplierDetails = null;
        $orderDetails = null;
        $phis = null;
        $stopinst = null;
        $listSupplier = null;

        if ($carianTemp != null && strlen($carianTemp) == 17) {

            /* Checking workflow.. DO status must be 'Acknowledged' and FN status must be 'Approved' before proceed to Invoice Creation. */
            $listWorkflow = $this->getListWorkFlowStatusDOFN($carianTemp);

            foreach ($listWorkflow as $list) {

                // 42005 - Acknowledged  (DO)
                // 43010 - Approved  (FRN)
                if ($list->do_status == 42005 && $list->frn_status == 43010) {
                    $invoiceDetails = $this->getInvoiceDetails($carianTemp);
                    $approverDetails = $this->getApproverDetails($carianTemp);
                    $supplierDetails = $this->getSupplierDetails($carianTemp);
                    if ($invoiceDetails) {
                        $listSupplier = $this->getListSupplierUsers($invoiceDetails->supplier_id);
                    }


                    $orderDetails = $this->getOrder($carianTemp);
                    $activeSupplier = null;

                    //check supplier status active
                    if ($supplierDetails && $supplierDetails->recordstatus == 0) {
                        $activeSupplier = $this->getActiveSupplier($invoiceDetails->supplier_id);
                    } else {
                        $activeSupplier = $supplierDetails;
                    }

                    foreach ($orderDetails as $result) {
                        if ($result->phis != '') {
                            $phis = 'true';
                        } else {
                            $phis = 'false';
                        }

                        if ($result->stopinst != '') {
                            $stopinst = 'true';
                        } else {
                            $stopinst = 'false';
                        }
                    }

                    $resultSupplier = array();
                    if (count($listSupplier) > 0) {
                        foreach ($listSupplier as $result) {
                            $resultSupplier[] = $result->login_id;
                        }
                    }
                }
            }
        }

        if ($approverDetails != null) {
            return view('payload.payload_invoice_creation', [
                'invoice' => $invoiceDetails,
                'approver' => $approverDetails,
                'supplier' => $activeSupplier,
                'listsupplier' => $resultSupplier,
                'order' => $orderDetails,
                'phis' => $phis,
                'stopinst' => $stopinst,
                'carian' => $carianTemp]);
        } else {
            return view('payload.payload_invoice_creation', [
                'invoice' => $invoiceDetails,
                'approver' => $approverDetails,
                'supplier' => $supplierDetails,
                'order' => $orderDetails,
                'phis' => $phis,
                'stopinst' => $stopinst,
                'carian' => $carianTemp]);
        }
    }

    public function findDO(Request $request) {
        $array = array();

        if (!isset($request->poco_no) && !isset($request->do_no)) {
            $array = array(
                'poco_no' => '',
                'do_no' => ''
            );
        } else {
            if (strlen($request->poco_no) == 17 && strlen($request->do_no)) {

                $array = array(
                    'poco_no' => $request->poco_no,
                    'do_no' => $request->do_no
                );
            }
        }

        return view('payload.payload_do_creation', [
            'result' => null,
            'formSearch' => $array
        ]);
    }

    public function findDODetail(Request $request) {
        $poco_no = $request->poco_no;
        $do_no = $request->do_no;
        $prio = $request->priority;
        

        $listSupplierDetail = '';
        $listDeliveryOrderDetail = '';
        $listReceivingOfficerDetail = '';
        $listDODetail = '';

        $result = null;
        if (strlen($request->poco_no) == 17) {
            $listSupplierDetail = $this->getDOSupplierList($poco_no);
            $listDeliveryOrderDetail = $this->getDeliveryOrderDetail($poco_no, $do_no);
            $listReceivingOfficerDetail = $this->getReceivingOfficerList($poco_no, $do_no, $prio);
            $listDODetail = $this->getDODetail($poco_no);
            $result = $listDeliveryOrderDetail;
        }

        return view('payload.payload_do_creation', [
            'result' => 'found',
            'supplier' => $listSupplierDetail,
            'deliveryorder' => $listDeliveryOrderDetail,
            'receivingofficer' => $listReceivingOfficerDetail,
            'do' => $listDODetail,
            'formSearch' => $request->all()
        ]);
    }
    
    public function findRequestNote() {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $requestNotePayload = null;
        
        if ($carianTemp != null) {
            $requestNotePayload = $this->generateRequestNotePayload($carianTemp);
        }
        
        if (isset($requestNotePayload) && count($requestNotePayload) > 0) {
            
            return view('payload.payload_request_note', [
                'requestNotePayload' => $requestNotePayload,
                'carian' => $carianTemp]);
        } else {
            return view('payload.payload_request_note', [
                'requestNotePayload' => $requestNotePayload,
                'carian' => $carianTemp]);
        }
    }
    
    public function findQTScEvaluation() {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $scEvaluationPayload = null;
        
        if ($carianTemp != null) {
            $scEvaluationPayload = $this->getQtScEvaluation($carianTemp);
        }
        
        if (count($scEvaluationPayload) > 0) {
            
            return view('payload.payload_qt_sc_evaluation', [
                'scEvaluationPayload' => $scEvaluationPayload,
                'carian' => $carianTemp]);
        } else {
            return view('payload.payload_qt_sc_evaluation', [
                'scEvaluationPayload' => $scEvaluationPayload,
                'carian' => $carianTemp]);
        }
    }
    
    public function findQTQuotationTender() {
        $carian = request()->cari;
        $carianTemp = trim($carian);
        $quotationTenderPayload = null;
        
        if ($carianTemp != null) {
            $quotationTenderPayload = $this->getQtQuotationTenderCreation($carianTemp);
        }
        
        if (count($quotationTenderPayload) > 0) {
            
            return view('payload.payload_qt_quotation_tender', [
                'quotationTenderPayload' => $quotationTenderPayload,
                'carian' => $carianTemp]);
        } else {
            return view('payload.payload_qt_quotation_tender', [
                'quotationTenderPayload' => $quotationTenderPayload,
                'carian' => $carianTemp]);
        }
    }
    
    public function findDAN() {
        $dncn_no = request()->dncn_no;
        $dncn_no_temp = trim($dncn_no);
        $data = null;
        $payload = null;
        if ($dncn_no_temp != null) {
            $data = $this->getDAN($dncn_no_temp);
        }

        if (isset($data) && $data['result'] > 0) {
            foreach($data['result'] as $pyload){
                $payload = $pyload;
                break;
            }
            return view('payload.payload_dan_can_creation', [
                'data' => $payload,
                'formSearch' => ['dncn_no' => $dncn_no_temp]]);
        } else {
            return view('payload.payload_dan_can_creation', [
                'data' => $payload,
                'formSearch' => ['dncn_no' => $dncn_no_temp]]);
        }
    }

    public function submitInvoice() {

        session()->flashInput(request()->input());
        
        $listdata = collect([]);
        $listDataResult = null;
        
        $doc_no = request()->doc_no;

        $getDocDetails = DB::connection('oracle_nextgen_rpt')
                ->table('fl_fulfilment_order a')
                ->where('a.doc_no', $doc_no)
                ->select('a.fulfilment_order_id as fl_order_id', 'a.doc_no as doc_no')
                ->first();

        if (isset($getDocDetails)) {
            $listDataResult = $this->doAPICreateSubmitInvoicePA($getDocDetails->fl_order_id, 'submit-invoice', null, 'false');
            if ($listDataResult['status'] == 'Success') {
                $listdata = $listDataResult["result"];
            }
        }
        return view('payload.payload_submit_invoice', [
            'listdata' => $listdata
        ]);
    }

    public function findFactoringCompany() {
        session()->flashInput(request()->input());
        
        $factoringData = null; 
        $factoringUser = null;
        $doc_no = request()->doc_no;
        $orgDetails = null; 
        if ($doc_no != null) {
            $factoringData = $this->getFactoringData($doc_no);
            if(isset($factoringData)){
                $factoringUser = $this->getFactoringUser($factoringData[0]->financial_org_id);
            }
            $orgDetails = $this->getOrgProfileDetails($factoringData[0]->owner_org_profile_id);
        }
        
        return view('payload.payload_factoring_company', [
            'factoringData' => $factoringData,
            'factoringUser' => $factoringUser,
            'orgDetails' => $orgDetails
        ]);
    }
    
    public function contractAgreement(){
        session()->flashInput(request()->input());
        
        $agreementData = null;
        $minContractAdmin = null;
        $ctApprover = null; 
        $doc_no = request()->doc_no;
        $remarks = null;
        if ($doc_no != null) {
            $agreementData = $this->getAgreementData($doc_no);
            if(count($agreementData) > 0){
                $minContractAdmin = $this->getRoleForCtAgreementPayload('MIN_CONTRACT_ADMIN', $doc_no);
                $ctApprover = $this->getRoleForCtAgreementPayload('CT_APPROVER',$doc_no);
                
                if(count($minContractAdmin) == 0){
                    $minContractAdmin = $this->getRoleForCtAgreementPayload($agreementData[0]->owner_org_profile_id,'MIN_CONTRACT_ADMIN');
                }
                if(count($ctApprover) == 0){
                    $ctApprover = $this->getRoleForCtAgreementPayload($agreementData[0]->owner_org_profile_id,'CT_APPROVER');
                }
                
            }else{
                $remarks = 'Agreement Data Not Exist';
            }
        }
        
        return view('payload.payload_contract_agreement', [
            'agreementData' => $agreementData,
            'minContractAdmin' => $minContractAdmin,
            'ctApprover' => $ctApprover,
            'remarks' => $remarks
        ]);
    }

    public function contractAgreementDraft(){
        session()->flashInput(request()->input());
        
        $agreementData = null;
        $minContractAdmin = null;
        $ctApprover = null; 
        $ctAgreementObj = null;
        $doc_no = request()->doc_no;
        $doc_type = request()->doc_type;
        $remarks = null;
        if ($doc_no != null) {
            $agreementData = $this->getAgreementData($doc_no);
            if(count($agreementData) > 0){
                $ctAgreementObj = collect($agreementData)->where('doc_type',$doc_type)->first();
                //dd($ctAgreementObj );
                if($ctAgreementObj != null){
                    $ctAgreementObj->agreement_type = null;
                    if($doc_type == 'AC'){
                        $ctAgreementObj->agreement_type = 'agreement';
                    }elseif($doc_type == 'SA'){
                        $ctAgreementObj->agreement_type = 'supplemental';
                    }
                    
                    $minContractAdmin = $this->getRoleAgreementACSA('MIN_CONTRACT_ADMIN', $doc_no);
               
                    $ctApprover = $this->getRoleForCtAgreementPayload('CT_APPROVER',$doc_no);
                    
                    if(count($minContractAdmin) == 0){
                        $minContractAdmin = $this->getRoleForCtAgreementPayload($ctAgreementObj->owner_org_profile_id,'MIN_CONTRACT_ADMIN');
                    }
                    if(count($ctApprover) == 0){
                        $ctApprover = $this->getRoleForCtAgreementPayload($ctAgreementObj->owner_org_profile_id,'CT_APPROVER');
                    }
                }else{
                    $remarks = "Agreement $doc_no for $doc_type is not found!";
                }
                
                
            }else{
                $remarks = "Agreement $doc_no  not found!";
            }
        }
        
        return view('payload.payload_contract_agreement_draft', [
            'agreementData' => $ctAgreementObj,
            'minContractAdmin' => $minContractAdmin,
            'ctApprover' => $ctApprover,
            'remarks' => $remarks
        ]);
    }
}
