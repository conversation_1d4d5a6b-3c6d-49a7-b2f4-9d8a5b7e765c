<?php

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Auth;
use Validator;
use Excel;
use stdClass;
use Log;
use DateTime;
use App\EpSupportActionLog;
use Illuminate\Http\Request;

class CRMController extends Controller
{

    public static function crmService()
    {
        return new CRMService;
    }

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function detailCaseCRM()
    {

        $caseNumber = request()->case_number;
        $deleteNoteStatus = request()->deleteNote ? request()->deleteNote : null;

        if (strlen($caseNumber) == 0) {
            return view('case_crm', [
                'case' => null,
                'task' => null,
                'caseTask' => null,
                'status_action' => null,
                'carian' => $caseNumber
            ]);
        }

        $specialistUsers = null;
        $specialistName = null;

        //get case and task
        $caseTask = self::crmService()->getDetailCaseAndTaskLatestCRM($caseNumber);
        $case = self::crmService()->getDetailCase($caseNumber);
        $task = null;
        $combineCategoryList = null;
        $resolutionCategoryList = null;
        $slaSeverityList = null;
        $taskFactorList = null;
        $taskJustificationList = null;
        $taskSlaStart = null;
        $taskSlaDue = null;
        $taskDuration = null;
        $statusEkList = null;
        $reservedBy = null;
        $groups = null;
        $userMd = null;

        if ($case != null) {

            $account_code = null;
            $account_name = null;
            $account_url = "javascript:void(0)";
            $account = self::crmService()->getDetailAccountCRM($case->account_id);
            if ($account != null) {
                $account_name = $account->name;
                $account_url = url("/find/mofno")."/$account_code";
                if ($account->account_type == 'SUPPLIER') {
                    $account_code = $account->mof_no;
                    if (strlen(trim($account_code)) == 0 ) {
                        $account_code = $account->ep_no;
                        $account_url = url("/find/epno")."/$account_code";
                    }else{
                        // (strlen(trim($account_code)) == 0) {
                        $account_code = $account->registration_no;
                        $account_url = url("/find/byname")."?nama_pembekal=$account_code";
                    }
                    
                } elseif ($account->account_type == 'GOVERNMENT') {
                    $account_code = $account->org_gov_code;
                    $app_env = env('APP_ENV', 'PRD'); // Default to PRD if not set
                    
                    if ($app_env === 'SIT') {
                        $account_url = url("/find/orgcode")."/$account_code";
                    } else {
                        // Default to PRD behavior for any other value
                        $account_url = "https://epss.eperolehan.gov.my/find/orgcode/$account_code";
                    }  
                }
            }
            $case->account_code = $account_code;
            $case->account_name = $account_name;
            $case->account_url = $account_url;
            $case->record_status = ($case->deleted == 0) ? 'Active' : 'Inactive';
            $case->case_status = self::crmService()->getValueLookupCRM('case_status_dom', $case->status);
            $case->sub_category = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $case->sub_category_c);
            $case->sub_sub_category = self::crmService()->getValueLookupCRM('cdc_sub_category_2_list', $case->sub_category_2_c);
            $case->request_type = self::crmService()->getValueLookupCRM('request_type_list', $case->request_type_c);
            $case->created_date = Carbon::parse($case->date_entered)->addHour(8)->format("Y-m-d H:i:s A");
            $case->incident_service_type = self::crmService()->getValueLookupCRM('incident_service_type_list', $case->incident_service_type_c);
            $case->portal_category_value = self::crmService()->getValueLookupCRM('portal_category_list', $case->portal_category);
            $combineCategoryList = self::crmService()->getDetailLookupCRMByType('combine_subcategory2_list');
            $resolutionCategoryList = self::crmService()->getDetailLookupCRMByType('task_resolution_category_list');
            $slaSeverityList = self::crmService()->getDetailLookupCRMByType('task_severity_list');
            $taskFactorList = self::crmService()->getDetailLookupCRMByType('task_factor_list');
            $taskJustificationList = self::crmService()->getDetailLookupCRMByType('task_justification');
            $statusEkList = self::crmService()->getDetailLookupCRMByType('task_econtract_status');

            //get task detail
            $task = self::crmService()->getDetailTaskLatestCRM($case->id);
            if ($task != null) {
                $task->created_date = Carbon::parse($task->date_entered)->addHour(8)->format("Y-m-d H:i:s A");
                $task->changed_date = Carbon::parse($task->date_modified)->addHour(8)->format("Y-m-d H:i:s A");
                $task->created_user = self::crmService()->getDetailUserCRM($task->created_by);
                $task->changed_user = self::crmService()->getDetailUserCRM($task->modified_user_id);
                $task->task_status = self::crmService()->getValueLookupCRM('cdc_task_status_list', $task->status);
                $task->task_resolution_category = self::crmService()->getValueLookupCRM('task_resolution_category_list', $task->resolution_category);
                $task->task_category_factor = self::crmService()->getValueLookupCRM('task_factor_list', $task->category_factor_c);
                $task->task_task_justification = self::crmService()->getValueLookupCRM('task_justification', $task->task_justification);
                $task->task_incident_factor = self::crmService()->getValueLookupCRM('combine_subcategory2_list', $task->tasks_combine_c);
                $task->assigned_user_id = $task->assigned_user_id;
                $task->task_econtract_status = self::crmService()->getValueLookupCRM('task_econtract_status', $task->econtract_status);
                if (isset($task->reserve_task_userid)) {
                    $reservedBy = self::crmService()->getNameUserCRM($task->reserve_task_userid);
                    $userGroupCrm = self::crmService()->getListDetailGroupCRM($task->reserve_task_userid);
                    $groups = json_decode($userGroupCrm->pluck('name'));
                }

                $groupMd = self::crmService()->getListDetailGroupMiddleware();
                $userMd = json_decode($groupMd->pluck('user_id'));

                $slaArrayTask = array('3', 's1', 's2', 's3');
                $taskSlaFlag = $task->sla_task_flag_c;
                if ($taskSlaFlag == 2) {
                    $taskSlaStart = Carbon::parse($task->date_start)->addHour(8)->format("Y-m-d H:i");
                    $taskSlaDue = Carbon::parse($task->date_due)->addHour(8)->format("Y-m-d H:i");
                } else if ($taskSlaFlag == '' && $task->status == 'Pending Acknowledgement') {
                    if ($task->date_due != '') {
                        $taskSlaDue = Carbon::parse($task->date_due)->addHour(8)->format("Y-m-d H:i");
                    } else {
                        $taskSlaDue = '';
                    }
                    $taskSlaStart = Carbon::parse($task->date_start)->addHour(8)->format("Y-m-d H:i");
                } else if (in_array($taskSlaFlag, $slaArrayTask)) {
                    $taskSlaStart = Carbon::parse($task->sla_start_4hr_c)->addHour(8)->format("Y-m-d H:i");

                    if ($taskSlaFlag == 3) {
                        $taskSlaDue = Carbon::parse($task->sla_start_4hr_c)->addHour(12)->format("Y-m-d H:i");
                    } else if ($taskSlaFlag == 's1') {
                        $taskSlaDue = Carbon::parse($task->sla_start_4hr_c)->addDay(1)->addHour(8)->format("Y-m-d H:i");
                    } else if ($taskSlaFlag == 's2') {
                        $taskSlaDue = Carbon::parse($task->sla_start_4hr_c)->addDay(3)->addHour(8)->format("Y-m-d H:i");
                    } else if ($taskSlaFlag == 's3') {
                        $taskSlaDue = Carbon::parse($task->sla_start_4hr_c)->addDay(5)->addHour(8)->format("Y-m-d H:i");
                    }
                }

                if ($taskSlaStart != '' && $taskSlaDue != '') {
                    $dateStart = Carbon::parse($taskSlaStart);
                    $dateDue = Carbon::parse($taskSlaDue);
                    $diff = $dateDue->diffInSeconds($dateStart);

                    $minutes = $diff / 60;
                    $hour = $diff / (60 * 60);
                    $days = $diff / (60 * 60 * 24);

                    if ($minutes < 60) {
                        $taskDuration = $minutes . ' minutes';
                    } else if ($minutes < 1440) {
                        $taskDuration = $hour . ' hours';
                    } else {
                        $taskDuration = $days . ' days';
                    }
                }


                $specialistName = self::crmService()->getDetailUserCRM($task->task_pic);
                $task->data_change_status = self::crmService()->getValueLookupCRM('data_change_status_list', $task->data_change_status);
            }

            $case->filecases = self::crmService()->listNotesFile('Cases', [$case->id]);
            //dump($case->filecases);

            $listCaseUpdated = DB::table('aop_case_updates')->where('case_id', $case->id)->get();
            $listCaseUpdatedId = $listCaseUpdated->pluck('id');
            $case->filecaseupdated = self::crmService()->listNotesFile('aop_case_updates', $listCaseUpdatedId);
            //dump($case->filecaseupdated);

            $specialistUsers = self::crmService()->getSpecialistUsers();
        }

        // dd($case,$task, $caseTask, $reservedBy);

        return view('case_crm', [
            'case' => $case,
            'task' => $task,
            'deleteNoteStatus' => $deleteNoteStatus,
            'caseTask' => $caseTask,
            'status_action' => null,
            'carian' => $caseNumber,
            'combineCategoryList' => $combineCategoryList,
            'resolutionCategoryList' => $resolutionCategoryList,
            'slaSeverityList' => $slaSeverityList,
            'taskFactorList' => $taskFactorList,
            'taskJustificationList' => $taskJustificationList,
            'taskSlaStart' => $taskSlaStart,
            'taskSlaDue' => $taskSlaDue,
            'taskDuration' => $taskDuration,
            'specialistUsers' => $specialistUsers,
            'specialistName' => $specialistName,
            'statusEkList' => $statusEkList,
            'reservedBy' => $reservedBy,
            'groupCRM' => $userMd
        ]);
    }

    public function updateCaseCRM($caseId, Request $request)
    {

        $userId = auth()->user()->id;
        $taskId = $request->taskId;
        $caseNo = $request->caseNo;
        $submitBtn = $request->submitBtnValue;
        $task = self::crmService()->getDetailTaskLatestCRM($caseId);
        $caseTask = self::crmService()->getDetailCaseAndTaskLatestCRM($caseNo);

        $initialTask = 'INITIAL';
        $prodSupport = '4HOUR';
        $taskType = null;
        $flagSeverity = '';
        $actionTypeLog = 'CRM';

        $data = collect([]);
        $actionParameter = collect([]);
        $status_action = "failed";

        $actionData = collect([]);
        $actionData->put("case_number", $caseNo);
        $actionData->put("data_before", $caseTask);

        if ($submitBtn == 'own') {

            $groupMd = self::crmService()->getListDetailGroupMiddleware();
            $userMd = json_decode($groupMd->pluck('user_id'));

            if (($task->reserve_task_userid === '' || $task->reserve_task_userid === null) ||
                ($task->assign_group_c === 'Group Middleware' && in_array(auth()->user()->id, $userMd))
            ) {

                $result = self::crmService()->updateTaskOwn($caseTask, $userId);
                if ($result === 1) {
                    //                Log::info('success');
                    $status_action = "success";
                }
            } else {
                $status_action = "failed";
            }

            //acknowledge task
        } else if ($submitBtn == 'reset') {
            $result = self::crmService()->updateTaskOwn($caseTask, null);
            if ($result === 1) {
                //                Log::info('success');
                $status_action = "success";
            }
        } else if ($submitBtn == 'acknowledge') {
            //            Log::info('acknowledge ' .$task->reserve_task_userid);
            $taskReserve = DB::table('tasks')
                ->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c')
                ->where('tasks.id', '=', $task->id)
                ->first();

            if (isset($taskReserve)) {
                if ($taskReserve->reserve_task_userid === $userId) {
                    if (strpos($task->name, 'Initial Task') !== false) {
                        $result = self::crmService()->updateTaskAcknowledge($caseTask, $userId, $initialTask, '', $caseTask->incidentservicetype);

                        if ($result === '111') {
                            $status_action = "success";
                        }
                        $taskType = $initialTask;
                    } else {
                        if ($caseTask->task_severity !== null) {
                            $result = self::crmService()->updateTaskAcknowledge($caseTask, $userId, $prodSupport, $caseTask->task_severity, $caseTask->incidentservicetype);
                            if ($result === '111') {
                                $status_action = "success";
                            }
                            $flagSeverity = $caseTask->task_severity;
                        } else {
                            if (strpos($caseTask->name, 'Assigned to Group Archisoft Build Team') !== false) {
                                $result = self::crmService()->updateTaskAcknowledge($caseTask, $userId, $prodSupport, '', $caseTask->incidentservicetype);
                                if ($result === '111') {
                                    $status_action = "success";
                                }
                            } else {
                                if ($caseTask->task_severity !== null) {
                                    $result = self::crmService()->updateTaskAcknowledge($caseTask, $userId, $prodSupport, $caseTask->task_severity, $caseTask->incidentservicetype);
                                    if ($result === '111') {
                                        $status_action = "success";
                                    }
                                    $flagSeverity = $caseTask->task_severity;
                                } else {
                                    $result = self::crmService()->updateTaskAcknowledge($caseTask, $userId, $prodSupport, 3, $caseTask->incidentservicetype);
                                    if ($result === '111') {
                                        $status_action = "success";
                                    }
                                    $flagSeverity = 3;
                                }
                            }
                        }
                        $taskType = $prodSupport;
                    }
                } else {
                    $status_action = "failed";
                }
            }
        } else {

            $userGroup = $request->usergroup;
            $resolutionCategory = $request->resolutionCategory;
            $categoryFactor = $request->categoryFactor;
            $incidentFactor = $request->incidentFactor;
            $taskJustification = $request->taskJustification;
            $severity = $request->severity;
            $newResolution = $request->resolution;
            $redmineNo = $request->redmineNo;
            $statusEk = $request->statusEk;
            $incidentClassification = $request->incidentClassification;

            //            /**
            //             * 10/04/2020
            //             * Status eK
            //             * Request Type : Service 
            //             * Incident/Serive Type : IT Service
            //             * Sub Category : 10713_15506_16641/10713_15506_16643
            //             * Task at Production Support/Middleware Only
            //             */
            // resolve case > assign to case owner
            $taskReserve = DB::table('tasks')
                ->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c')
                ->where('tasks.id', '=', $task->id)
                ->first();

            if ($taskReserve->reserve_task_userid === $userId) {
                if ($userGroup == 'Group Business Coordinator') {
                    if (strpos($caseTask->name, 'Initial Task') !== false) {
                        $result = self::crmService()->updateTaskCaseToResolve($caseTask, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $initialTask, $userId, 2, $caseTask->incidentservicetype, $statusEk, $incidentClassification);
                        //                    Log::info($result);
                        if ($result === '11') {
                            $status_action = "success";
                        }
                        $flagSeverity = 2;
                        $taskType = $initialTask;
                    } else {
                        $result = self::crmService()->updateTaskCaseToResolve($caseTask, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $prodSupport, $userId, $caseTask->sla_task_flag_c, $caseTask->incidentservicetype, $statusEk, $incidentClassification);
                        //                    Log::info($result);
                        if ($result === '11') {
                            $status_action = "success";
                        }
                        $flagSeverity = $caseTask->sla_task_flag_c;
                        $taskType = $prodSupport;
                    }
                }
                //reassigned to other group
                else {
                    if ($userGroup == 'Group ePP1') {
                        $userGroupId = CRMService::$CRM_GROUP_EPP1[$userGroup]['user_group_id'];
                        $taskNameGroup = CRMService::$CRM_GROUP_EPP1[$userGroup]['name_task'];
                        $assignGroupName = CRMService::$CRM_GROUP_EPP1[$userGroup]['assign_group'];
                    }else{
                        $userGroupId = CRMService::$CRM_GROUP_USER[$userGroup]['user_group_id'];
                        $taskNameGroup = CRMService::$CRM_GROUP_USER[$userGroup]['name_task'];
                        $assignGroupName = CRMService::$CRM_GROUP_USER[$userGroup]['assign_group'];
                    }
                     
                    if (strpos($caseTask->name, 'Initial Task') !== false) {
                        $result = self::crmService()->updateTaskToAssignAnotherGroup(
                            $caseTask,
                            $newResolution,
                            $resolutionCategory,
                            $categoryFactor,
                            $taskJustification,
                            $incidentFactor,
                            $userGroupId,
                            $assignGroupName,
                            $taskNameGroup,
                            $initialTask,
                            $userId,
                            $redmineNo,
                            2,
                            $caseTask->incidentservicetype,
                            null,
                            $incidentClassification
                        );
                        //                    Log::info($result);
                        if ($result === '111') {
                            $status_action = "success";
                        }
                        $flagSeverity = 2;
                        $taskType = $initialTask;
                    } else {
                        if ($severity !== null) {
                            $result = self::crmService()->updateTaskToAssignAnotherGroupSeverity(
                                $caseTask,
                                $newResolution,
                                $resolutionCategory,
                                $categoryFactor,
                                $taskJustification,
                                $incidentFactor,
                                $userGroupId,
                                $assignGroupName,
                                $taskNameGroup,
                                $userId,
                                $redmineNo,
                                $caseTask->sla_task_flag_c,
                                $severity,
                                $statusEk,
                                $incidentClassification
                            );
                            //                        Log::info($result);
                            if ($result === '111') {
                                $status_action = "success";
                            }
                            $flagSeverity = $caseTask->sla_task_flag_c . '/' . $severity;
                            $taskType = 'severity';
                        } else {
                            $result = self::crmService()->updateTaskToAssignAnotherGroup($caseTask, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $userGroupId, $assignGroupName, $taskNameGroup, $prodSupport, $userId, $redmineNo, $caseTask->sla_task_flag_c, $caseTask->incidentservicetype, $statusEk, $incidentClassification);
                            //                        Log::info($result);
                            if ($result === '111') {
                                $status_action = "success";
                            }
                            $flagSeverity = $caseTask->sla_task_flag_c;
                            $taskType = $prodSupport;
                        }
                        $status_action = "success";
                    }
                }
            } else {
                $status_action = "failed";
            }
        }

        $actionParameter->put("task_id", $taskId);
        $actionParameter->put("user_id", $userId);
        $actionParameter->put("task_type", $taskType);
        $actionParameter->put("flag/severity", $flagSeverity);
        EpSupportActionLog::saveActionLog($submitBtn, $actionTypeLog, $actionData, $actionParameter, $status_action);

        $data->put('status', $status_action);
        return $data;
    }

    public function updateJobQue()
    {
        if (request()->isMethod('post')) {

            Validator::make(request()->all(), [
                'job_id' => 'required'
            ])->validate();

            $jobId = request()->job_id;

            /* UPDATE JOB QUEUE */
            if ($jobId) {
                try {
                    $update = DB::table('job_queue')
                        ->where('id', $jobId)
                        ->update([
                            'status' => 'done',
                            'resolution' => 'failed',
                            'message' => 'Update via EPSS by ' . auth()->user()->user_name
                        ]);

                    if ($update > 0) {
                        return 'update-success';
                    }
                } catch (\Illuminate\Database\QueryException $e) {
                    return 'update-failed';
                }
            }
        }
    }

    public function assignPicForTask($userId, Request $request)
    {
        $data = collect([]);
        $status = null;
        $userLogin = auth()->user()->id;

        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {
            $taskId = $request->taskid;
            $firstName = $request->firstname;

            $updateTaskPic = self::crmService()->updateTaskPicSpecialist($taskId, $userId, $userLogin);
            $updateReservedBy = self::crmService()->updateTaskOwnByTaskId($taskId, $userId);
            $parameters =  collect([]);
            $parameters->put("task_id", $taskId);
            $parameters->put("first_name", $firstName);
            $parameters->put("user_id", $userId);
            $parameters->put("user_login", $userLogin);

            if ($updateTaskPic == 1 && $updateReservedBy == 1) {
                $status = 'Success';
            } else {
                $status = 'Failed';
            }
            EpSupportActionLog::saveActionLog('Assign-Pic-Specialist', 'CRM', $parameters, $parameters, $status);

            $data->put('status', $status);

            return $data;
        }
    }

    public function updateResolution($caseId, Request $request)
    {
        $data = collect([]);
        $status = null;
        $userLogin = auth()->user()->id;

        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            Validator::make(request()->all(), [
                'newResolution' => 'required',
            ])->validate();

            $taskId = $request->taskId;
            $oldResolution = $request->oldResolution;
            $newResolution = $request->newResolution;

            //            Log::info('userId ' .$userLogin .' caseId ' .$caseId .' taskId ' .$taskId .' old ' .$oldResolution .' new ' .$newResolution);
            $updateResolution = self::crmService()->updateCaseResolution($caseId, $taskId, $newResolution, $userLogin);

            $parameters =  collect([]);
            $parameters->put("case_id", $caseId);
            $parameters->put("task_id", $taskId);
            $parameters->put("old_resolution", $oldResolution);
            $parameters->put("new_resolution", $newResolution);
            $parameters->put("user_login", $userLogin);

            if ($updateResolution === '1 1 1') {
                $status = 'Success';
            } else {
                $status = 'Failed';
            }
            EpSupportActionLog::saveActionLog('Update-Case-Resolution', 'CRM', $parameters, $parameters, $status);

            $data->put('status', $status);

            return $data;
        }
    }

    public function deleteNoteFiles(Request $request, $caseId)
    {
        $userLogin = auth()->user()->id;
        $fileId = $request->input('fileId');

        $result = self::crmService()->updateDeleteNoteFiles($fileId, $userLogin);
        $statusCode = (json_decode($result->content()))->status_code;
        $statusMessage = (json_decode($result->content()))->status_message;

        $parameters =  collect([]);
        $parameters->put("case_id", $caseId);
        $parameters->put("file_id", $fileId);
        $parameters->put("user_login", $userLogin);
        $parameters->put("status_code", $statusCode);
        $parameters->put("status_message", $statusMessage);
        EpSupportActionLog::saveActionLog('Delete-Note-Files', 'CRM', $parameters, $parameters, $statusCode == 200 ? 'Success' : 'Failed');
        if ($statusCode != 200) {
            return redirect((back()->getTargetUrl()) . '&deleteNote=fail');
        }
        return redirect((back()->getTargetUrl()) . '&deleteNote=success');
    }
}
