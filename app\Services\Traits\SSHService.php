<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;
use SSH;
use Exception;
use App\Services\EPService;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SSHService {

    /**
     * Direct call OSB instead of JPN API.
     *
     */
    protected function getMyIdentityInfo($icNo){
        $icObj = array();
        $dataResp = array();
        $icno = trim(str_replace("-", "", $icNo));
        $uuid = \Ramsey\Uuid\Uuid::uuid4();
        $rqUid = $uuid.' eps'; 
        $url = "http://**************:7011/GetMyIdentityInfo/v1.0?wsdl";

        $headers = array(
            "Content-type: text/xml; charset=utf-8",
            "SOAPAction: http://www.ep.gov.my/Service/1-0/GetMyIdentityInfo/inquire", 
        );
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:get='http://www.ep.gov.my/Schema/1-0/GetMyIdentityInfo'>    <soapenv:Header/>    <soapenv:Body xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><EPMFRq xmlns='http://www.ep.gov.my/Schema/1-0/GetMyIdentityInfo'><ns1:RqHeader xmlns:ns1='http://www.ep.gov.my/Schema/1-0/epmf'><ns1:ConsumerID>EPP-001</ns1:ConsumerID><ns1:UID><ns1:RqUID>$rqUid</ns1:RqUID></ns1:UID></ns1:RqHeader><GetMyIdentityInfoRq><Type>Basic</Type><ICNumber>$icno</ICNumber></GetMyIdentityInfoRq></EPMFRq></soapenv:Body></soapenv:Envelope>";

        try{

            $soap_do = curl_init();

            curl_setopt($soap_do, CURLOPT_URL, $url );
            curl_setopt($soap_do, CURLOPT_SSL_VERIFYPEER, FALSE);
            curl_setopt($soap_do, CURLOPT_RETURNTRANSFER, true );
            curl_setopt($soap_do, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($soap_do, CURLOPT_POST,           true );
            curl_setopt($soap_do, CURLOPT_POSTFIELDS,     $xmlContents);


            $output = curl_exec($soap_do);
            
            $p = xml_parser_create();
            xml_parse_into_struct($p, $output, $vals, $index);
            xml_parser_free($p);
            foreach ($vals as $val) {
                if ($val["tag"] == 'EPMF:STATUSCODE')
                    $icObj["messagecode"] = $val["value"];
                if ($val["tag"] == 'EPMF:SEVERITY')
                    $icObj["severity"] = $val["value"];
                if ($val["tag"] == 'EPMF:STATUSDESC')
                    $icObj["message"] = $val["value"];
                if ($val["tag"] == 'GET:ICNUMBER')
                    $icObj["icno"] = $val["value"];
                if ($val["tag"] == 'GET:NAME')
                    $icObj["name"] = $val["value"];
                if ($val["tag"] == 'GET:DATEOFBIRTH')
                    $icObj["date_of_birth"] = $val["value"];
                if ($val["tag"] == 'GET:GENDER')
                    $icObj["gender"] = $val["value"] . " - " . EPService::$JPN_GENDER[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RACE')
                    $icObj["race"] = $val["value"] . " - " . EPService::$JPN_RACE[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RELIGION')
                    $icObj["religion"] = $val["value"] . " - " . EPService::$JPN_RELIGION[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:PERMANENTADDR1')
                    $icObj["addr1"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDR2')
                    $icObj["addr2"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDR3')
                    $icObj["addr3"] = array_key_exists('value', $val) ? $val["value"] : '';
                if ($val["tag"] == 'GET:PERMANENTADDRPOSTCODE')
                    $icObj["postcode"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRCITYCODE')
                    $icObj["city_code"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRCITYDESC')
                    $icObj["city"] = $val["value"];
                if ($val["tag"] == 'GET:PERMANENTADDRSTATECODE')
                    $icObj["statecode"] = $val["value"] . " - " . EPService::$JPN_STATE[$val["value"]];
                if ($val["tag"] == 'GET:RESIDENTSTATUS')
                    $icObj["residential_status"] = $val["value"] . " - " . EPService::$JPN_RESIDENTIAL[$this->getValueJpn($val)];
                if ($val["tag"] == 'GET:RECORDSTATUS')
                    $icObj["record_status"] = $val["value"] . " - " . EPService::$JPN_RECORD_STATUS[$this->getValueJpn($val)];
            }
            $dataResp['result'] = $icObj;
            $dataResp['status'] = 'success';
            $dataResp['statusDesc'] = 'Completed';
        } catch (Exception $ex) {
            $status = 'error';
            $statusDesc = $ex->getMessage();
            
            $dataResp['result'] = $icObj;
            $dataResp['status'] = $status;
            $dataResp['statusDesc'] = $statusDesc;
        }
        return $dataResp;
    }

    /**
     * Direct Call JPN Identity 
     * @param type $icNo
     * @return array
     */
    protected function getIdentityInfoJPN($icNo){
        $icno = trim(str_replace("-", "", $icNo));
        
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>103007</AgencyCode><BranchCode>ePSupplier</BranchCode><UserId>$icno</UserId><TransactionCode>T7</TransactionCode><RequestDateTime>2021-05-09T08:44:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
        $xmlContents = '"' . $xmlContents . '"';

        $urlIdentity = "https://esb.myidentity.gov.my:81/crsservice";
        $urlHeader = "'Content-Type: application/xml'";

        $commands = [
            "curl -k " . $urlIdentity . " --header  " . $urlHeader . "  -d " . $xmlContents,
        ];

        $dataResp = array();
        $icObj = array();
        try {
            SSH::into('osb')->run($commands, function($line) use ($icno, &$icObj) {
                $data = $line . PHP_EOL;
                if (strpos($data, $icno) !== false) {
                    $p = xml_parser_create();
                    xml_parse_into_struct($p, $data, $vals, $index);
                    xml_parser_free($p);
                    //dump($vals);
                    //string(16) "SOAPENV:ENVELOPE" string(12) "SOAPENV:BODY" string(27) "NS1:RETRIEVECITIZENSDATARES" string(10) "AGENCYCODE" string(10) "BRANCHCODE" string(6) "USERID" string(15) "TRANSACTIONCODE" string(13) "REPLYDATETIME" string(8) "ICNUMBER" string(14) "REPLYINDICATOR" string(11) "MESSAGECODE" string(4) "NAME" string(11) "DATEOFBIRTH" string(6) "GENDER" string(4) "RACE" string(8) "RELIGION" string(17) "PERMANENTADDRESS1" string(17) "PERMANENTADDRESS2" string(17) "PERMANENTADDRESS3" string(24) "PERMANENTADDRESSPOSTCODE" string(24) "PERMANENTADDRESSCITYCODE" string(25) "PERMANENTADDRESSSTATECODE" string(22) "CORRESPONDENCEADDRESS1" string(22) "CORRESPONDENCEADDRESS2" string(22) "CORRESPONDENCEADDRESS3" string(22) "CORRESPONDENCEADDRESS4" string(22) "CORRESPONDENCEADDRESS5" string(29) "CORRESPONDENCEADDRESSPOSTCODE" string(29) "CORRESPONDENCEADDRESSCITYCODE" string(36) "CORRESPONDENCEADDRESSCITYDESCRIPTION" string(30) "CORRESPONDENCEADDRESSSTATECODE" string(32) "CORRESPONDENCEADDRESSCOUNTRYCODE" string(13) "ADDRESSSTATUS" string(12) "RECORDSTATUS" string(12) "VERIFYSTATUS" string(31) "CORRESPONDENCEADDRESSUPDATEDATE" string(29) "CORRESPONDENCEADDRESSUPDATEBY" string(11) "DATEOFDEATH" string(11) "OLDICNUMBER" string(17) "RESIDENTIALSTATUS" string(17) "CITIZENSHIPSTATUS" string(12) "EMAILADDRESS" string(17) "MOBILEPHONENUMBER" string(11) "NEWICNUMBER" string(24) "PERMANENTADDRESSCITYDESC" string(27) "NS1:RETRIEVECITIZENSDATARES" string(12) "SOAPENV:BODY" string(16) "SOAPENV:ENVELOPE"
                    foreach ($vals as $val) {
                        if ($val["tag"] == 'MESSAGECODE')
                            $icObj["messagecode"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'MESSAGE')
                            $icObj["message"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'MessageCode')
                            $icObj["messagecode"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'Message')
                            $icObj["message"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'ICNUMBER')
                            $icObj["icno"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'NAME')
                            $icObj["name"] = $this->getValueJpn($val);
                        if ($val["tag"] == 'GENDER')
                            $icObj["gender"] = EPService::$JPN_GENDER[$this->getValueJpn($val)];
                        if ($val["tag"] == 'PERMANENTADDRESS1') {
                            if (array_key_exists('value', $val))
                                $icObj["addr1"] = $val["value"];
                            
                                else $icObj["addr1"] = '';
                        }
                        if ($val["tag"] == 'PERMANENTADDRESS2') {
                            if (array_key_exists('value', $val))
                                $icObj["addr2"] = $val["value"];
                            
                                else $icObj["addr2"] = '';
                        }
                        if ($val["tag"] == 'PERMANENTADDRESS3') {
                            if (array_key_exists('value', $val))
                                $icObj["addr3"] = $val["value"];
                            
                                else $icObj["addr3"] = '';
                        }
                        if ($val["tag"] == 'PERMANENTADDRESSPOSTCODE') {
                            if (array_key_exists('value', $val))
                                $icObj["postcode"] = $val["value"];
                            
                                else $icObj["postcode"] = '';
                        }
                        if ($val["tag"] == 'PERMANENTADDRESSCITYDESC') {
                            if (array_key_exists('value', $val))
                                $icObj["city"] = $val["value"];
                            
                                else $icObj["city"] = '';
                        }
                        if ($val["tag"] == 'PERMANENTADDRESSSTATECODE') {
                            if (array_key_exists('value', $val))
                                $icObj["statecode"] = $val["value"] . " - " . EPService::$JPN_STATE[$val["value"]];
                            
                                else $icObj["statecode"] = '';
                        }
                        if ($val["tag"] == 'EMAILADDRESS') {
                            if (array_key_exists('value', $val))
                                $icObj["email"] = $val["value"];
                            
                                else $icObj["email"] = '';
                        }
                        if ($val["tag"] == 'MOBILEPHONENUMBER') {
                            if (array_key_exists('value', $val))
                                $icObj["mobile"] = $val["value"];
                            
                                else $icObj["mobile"] = '';
                        }
                        if ($val["tag"] == 'RECORDSTATUS') {
                            if (array_key_exists('value', $val))
                                $icObj["record_status"] = $val["value"];
                            
                                else $icObj["record_status"] = '';
                        }
                        if ($val["tag"] == 'VERIFYSTATUS') {
                            if (array_key_exists('value', $val))
                                $icObj["verify_status"] = $val["value"];
                            
                                else $icObj["verify_status"] = '';
                        }
                        if ($val["tag"] == 'RESIDENTIALSTATUS') {
                            if (array_key_exists('value', $val))
                                $icObj["residential_status"] = $val["value"];
                            
                                else $icObj["residential_status"] = '';
                        }
                        if ($val["tag"] == 'CITIZENSHIPSTATUS') {
                            if (array_key_exists('value', $val))
                                $icObj["citizenship_status"] = $val["value"];
                            
                                else $icObj["citizenship_status"] = '';
                        }
                        if ($val["tag"] == 'RACE') {
                            if (array_key_exists('value', $val))
                                $icObj["race"] = $val["value"];
                            
                                else $icObj["race"] = '';
                        }
                        if ($val["tag"] == 'RELIGION') {
                            if (array_key_exists('value', $val))
                                $icObj["religion"] = $val["value"];
                            
                                else $icObj["religion"] = '';
                        }
                        
                        
                    }
                    //dd($icObj );
                }
            });
            $dataResp['result'] = $icObj;
            $dataResp['status'] = 'success';
            $dataResp['statusDesc'] = 'Completed';
        } catch (Exception $ex) {
            //dd($ex->getMessage());
            $status = 'error';
            $statusDesc = $ex->getMessage();
            
            $dataResp['result'] = $icObj;
            $dataResp['status'] = $status;
            $dataResp['statusDesc'] = $statusDesc;
        }
        return $dataResp;
    }
    
    private function getValueJpn($val){
        if (array_key_exists('value', $val)){
            return  $val["value"];
        }
        return '';
    }
    protected function getListAPIVEOutFolder(){
        $commands  = [
            'cd /batch/1GFMAS/OUT',
            'find 1000APIVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APIVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }
    
    protected function getListAPOVEInFolder(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'find 1000APOVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APOVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasFolderOUT(){
        $commands  = [
            'cd /batch/1GFMAS/OUT',
            'ls -lrt *.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $checkingFileGPG = substr($str, -3);
                    if($checkingFileGPG && $checkingFileGPG == 'GPG'){
                        $filename = trim(substr($str, -33)); 
                        $checkSpaceExist = strpos($filename, ' ');
                        if($checkSpaceExist !== false){
                           $arrayFilename =  explode(' ', $filename);
                           $filename = $arrayFilename[1];
                        }
                        array_push($filesFound,trim($filename)); 
                    }
                }
            }
        });
        
        return $filesFound;
    }
    
    /**
     * 
     * @param type $batchIntegration Put as 1GFMAS, PHIS, ePerunding, LMS, SPA 
     * @param type $lengthCheck   filter on filename length same or more with $lengthCheck
     * @return array
     */
    protected function getListEpBatchFolderOUT($batchIntegration,$lengthCheck){
        
        $commandGetList = 'ls -1 /batch/'.$batchIntegration.'/OUT';
        $commands  = [
            $commandGetList,
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound,$lengthCheck)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                //$filename = substr($str, 0, 32); 
                if(strlen($str) >= $lengthCheck ){
                    array_push($filesFound,trim($str));   
                }
            }
        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasFolderIN(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'ls -lrt *.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    if(strlen($filename) > 20){
                        array_push($filesFound,trim($filename)); 
                    }     
                     
                }
            }
        });
        
        return $filesFound;
    }
    
    protected function getList1GfmasServerFolderIN(){
       
        $data = array();
        
        $IGFMAS_SERVER =  env('SSH_IGFMAS_SERVER', '10.23.22.15');
        $IGFMAS_SERVER_USERNAME =  env('SSH_IGFMAS_SERVER_USERNAME', 'eperolehan@10');
        
        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 $IGFMAS_SERVER_USERNAME@$IGFMAS_SERVER:IN",
            "exit",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            $arrayData  = (explode("\n",$result));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    array_push($data,trim($filename)); 
                 }
            }
        });
        return $data;
    }
    
    protected function getList1GfmasServerFolderOUT(){
       
        $data = array();
        
        $IGFMAS_SERVER =  env('SSH_IGFMAS_SERVER', '10.23.22.15');
        $IGFMAS_SERVER_USERNAME =  env('SSH_IGFMAS_SERVER_USERNAME', 'eperolehan@10');
        
        $commands  = [
            "echo 'ls -lr' | sftp -oPort=2022 $IGFMAS_SERVER_USERNAME@$IGFMAS_SERVER:OUT",
            "exit",
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            $arrayData  = (explode("\n",$result));
            foreach($arrayData  as $str){
                $pos = strpos($str, '.GPG');
                if($pos !== false){
                    $filename = trim(substr($str, -33)); 
                    $checkSpaceExist = strpos($filename, ' ');
                    if($checkSpaceExist !== false){
                       $arrayFilename =  explode(' ', $filename);
                       $filename = $arrayFilename[1];
                    }
                    array_push($data,trim($filename)); 
                 }
            }
        });
        return $data;
    }
    
    protected function countFiles1GFMASFolderIN($searchFileName){
        //$searchFileName = '1007AP51140000120190716001.GPG';
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'find '.$searchFileName,
            'exit',
        ];
        $filesFound = collect([]);
        SSH::into('portal')->run($commands, function($line) use (&$filesFound,$searchFileName)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                if($str == $searchFileName){
                    $filesFound->push(trim($str));
                }
            }
        });
        return $filesFound->count();
    }
    
    protected function getListFiles1GFMASFolderIN(){
        $commands  = [
            'cd /batch/1GFMAS/IN',
            'find 1000APOVE40000*.GPG',
            'exit',
        ];
        $filesFound = array();
        SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
            $data = $line.PHP_EOL;
            $arrayData  = (explode("\n",$data));
            foreach($arrayData  as $str){
                $filename = trim(substr($str, -33)); 
                $checkSpaceExist = strpos($filename, ' ');
                if($checkSpaceExist !== false){
                   $arrayFilename =  explode(' ', $filename);
                   $filename = $arrayFilename[1];
                }
                $pos = strpos($filename, '1000APOVE40000120');
                if($pos !== false){
                    if(strlen($filename) > 25 ){
                        array_push($filesFound,trim($filename));   
                    }
                }

            }

        });
        
        return $filesFound;
    }

    /**
     * List Health Node BPM and Thread Count
     * @return Collection 
     */
    protected function getListHealthBPM(){
        
        $collect = collect();
        $remotePath = "/home/<USER>/script/data/data.txt";
        $contents = SSH::into('bpm')->getString($remotePath);
        $listHealthBPM = explode("\n", trim($contents));
        //var_dump($listHealthBPM);
        $collect->put('health_bpm',$listHealthBPM);

        $remotePath2 = "/home/<USER>/script/data/threadcount.txt";
        $contents2 = SSH::into('bpm')->getString($remotePath2);
        $listThreadCountBPM = explode("\n", trim($contents2));
        //var_dump($listThreadCountBPM);
        $collect->put('thread_bpm',$listThreadCountBPM);
        
        return $collect;
    }

    protected function checkPing($domain){
        $data = array();
        $commands  = [
            'ping -c 1 '.$domain,
            'exit',
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            //var_dump($result);
            array_push($data,trim($result));
        });
        return $data;
    }

    protected function checkConnectionService($urlService){
        // wget --server-response http://integrasi.ssm.com.my/InfoService/1 2>&1 |grep Connecting
        $data = array();
        $commands  = [
            'wget --timeout=10 --server-response '.$urlService.'  2>&1 |grep Connecting',
            'exit',
        ];
        SSH::into('osb')->run($commands, function($line) use (&$data)  {
            $result = $line.PHP_EOL;
            array_push($data,trim($result));
        });
        return $data;
    }
}
