<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\BpmApiService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmRollbackSiteVisitToApproval {

    use PayloadGeneratorService;
    use BpmApiService;

    public static function importFileApplsRollback(){
        
        //$filename = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20200504.xlsx';
        //$filename = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20201101.xlsx';
        //$filename = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20201117.xlsx';
        //$filename   = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20210114.xlsx';
        //$filename   = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20210129.xlsx';
        //$filename   = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20210308.xlsx';
        //$filename   = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20210520.xlsx';
        $filename   = '/app/Migrate/data/SM_Appl_RevertStatusSiteVisitMCO-20210624.xlsx';
		Excel::load($filename, function($reader){
            $reader->each(function($row) {
                DB::connection('mysql_ep_support')
                            ->table('ep_sm_rollback_appl')->updateOrInsert(
                            [
                               'appl_no' =>$row->appl_no ,
                            ],         
                            [
                                'appl_id' => $row->appl_id,
                                'appl_status_id' => $row->appl_status_id,
                                'appl_status_name' => $row->appl_status,
                                'wf_status_id' => $row->workflow_status_id,
                                'wf_status_name' => $row->workflow_status,
                                'diary_status_id' => $row->tracking_status_id,
                                'diary_status_name' => $row->tracking_status,
                                'process_status' => 0,
                                'created_date' => Carbon::now(),
                            ]
                );
            });
        });
        dd('DONE import by excel');
    }
    
    public static function runRollbackSiteVisitToApprover($applNo,$isSkipTerminateTask = false) {

        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        $thisClass = new SmRollbackSiteVisitToApproval();

        //$taskDetailPayload = $thisClass->findApiWorklistTaskDetail("b6c0b217-54c4-440e-8dab-4cdc20f8bacc");
        //dd($taskDetailPayload);
        
        $rowAppl = DB::connection('mysql_ep_support')->table('ep_sm_rollback_appl')
                        ->where('appl_no',$applNo)
                        ->first();
        
        if($rowAppl === null){
            $objApplCheck = $thisClass->getCurrentStatus($applNo);
            if($objApplCheck){
                DB::connection('mysql_ep_support')
                            ->table('ep_sm_rollback_appl')->updateOrInsert(
                            [
                               'appl_no' =>$objApplCheck->appl_no ,
                            ],         
                            [
                                'appl_id' => $objApplCheck->appl_id,
                                'appl_status_id' => $objApplCheck->appl_status_id,
                                'appl_status_name' => $objApplCheck->appl_status,
                                'wf_status_id' => $objApplCheck->workflow_status_id,
                                'wf_status_name' => $objApplCheck->workflow_status,
                                'diary_status_id' => $objApplCheck->tracking_status_id,
                                'diary_status_name' => $objApplCheck->tracking_status,
                                'process_status' => 0,
                                'created_date' => Carbon::now(),
                            ]
                );
                dump("Inserted table ep_sm_rollback_appl - revert to site visit.");
                $rowAppl = DB::connection('mysql_ep_support')->table('ep_sm_rollback_appl')
                            ->where('appl_no',$applNo)
                            ->first();
            }
        }

        //Skip for checking status SiteVisit.
        if($rowAppl && $rowAppl->process_status=== 0 || $rowAppl->process_status=== 2  ) {
            $collectDataStep = $thisClass->rollbackSiteVisitToApprover($applNo,true, $isSkipTerminateTask);
            $resultAppl = $thisClass->getCurrentStatusWithoutCheckMaxDate($applNo);
            $collectDataStep->put("status_doc", $resultAppl);
            dump($collectDataStep);
             
            if($rowAppl != null){
                DB::connection('mysql_ep_support')->table('ep_sm_rollback_appl')
                    ->where('appl_no',$applNo)
                    ->update([
                        'process_status' => $collectDataStep->get('status_process'),
                        'retry_count' => ($rowAppl->retry_count + 1),
                        'result' => $collectDataStep,
                        'changed_date' => Carbon::now()
                    ]);
            }
        }else{
            "Invalid Appl No!";
        }

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }
    
    public static function runRollbackSiteVisitToApproverByList($param) {

        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        $isSkipTerminateTask = $param->get('isSkipTerminateTask');
        $thisClass = new SmRollbackSiteVisitToApproval();

        $listAppls = DB::connection('mysql_ep_support')->table('ep_sm_rollback_appl')
                        ->where('process_status',$param->get('process_status'))
                        ->take($param->get('total_appl'))
                        ->get();
        
        foreach ($listAppls as $rowAppl){
            
            $isSkipCheckSiteVisit = false;
            if($rowAppl->process_status == 2){
                $isSkipCheckSiteVisit = true;
            }
            $collectDataStep = $thisClass->rollbackSiteVisitToApprover($rowAppl->appl_no,$isSkipCheckSiteVisit,$isSkipTerminateTask);
            sleep(3);
            $resultAppl = $thisClass->getCurrentStatusWithoutCheckMaxDate($rowAppl->appl_no);
            $collectDataStep->put("status_doc", $resultAppl);
            dump($collectDataStep);
            
            DB::connection('mysql_ep_support')->table('ep_sm_rollback_appl')
                ->where('appl_no',$rowAppl->appl_no)
                ->update([
                    'process_status' => $collectDataStep->get('status_process'),
                    'retry_count' => ($rowAppl->retry_count + 1),
                    'result' => $collectDataStep,
                    'changed_date' => Carbon::now()
                ]);
                        
        }
        
        
        
        

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    
    protected function rollbackSiteVisitToApprover($applNo,$isSkipCheckSiteVisit = false,$isSkipTerminateTask = false){
        dump(__FUNCTION__.' > Find applNo: ' . $applNo);
        
        $collectDataStep = collect();
        $collectDataStep->put('status_process', 0);
        
        $applObj = $this->getCurrentStatus($applNo);
        
        if($applObj->workflow_status_id == 20108){
           $collectDataStep->put('status_process', 3);
           $collectDataStep->push(' Stop here!! Skip cause status go to : '.$applObj->workflow_status. ' - '.$applObj->workflow_status_id);
           return $collectDataStep;
        }
         
        
        
        $collectDataStep->push(__FUNCTION__.' > Find applNo: ' . $applNo);
        
        $collectDataStep->push('Checking Appl No.. is it valid for rollback? ');
        if($isSkipCheckSiteVisit == true){
            $collectDataStep->push('Checking Appl No.. is it valid for rollback? --> SKIP ');
        }else{
            $isSiteVisitFlow = $this->isCurrentSiteVisitTask($applNo);
            if($isSiteVisitFlow == false){ $collectDataStep->push('This Appl No not valid for rollback!');  $collectDataStep->put('status_process', 9); return $collectDataStep;}
        }
      
        $collectDataStep->push('Stage 1 - Check task exist. IF exist. will terminate if running instance not MOFSupplierRegistration');

        //Find Task 
        $taskDetail = $this->getDetailTaskAssigned($applNo); 
        if($taskDetail != null && $taskDetail['processId'] == 'MOFSupplierSiteVisit' ){
            //Terminate Task
            $instanceId = $taskDetail['instanceId'];
            $module = $taskDetail['composite'];
            $collectDataStep->push('Stage 1.1 - Checking and found task MOFSupplierSiteVisit exist as ASSIGNED. Need to terminate this instance.. , instanceID : '.$instanceId. ' ProcessID: '.$taskDetail['processId']);
            $res = $this->submitTerminateInstance($module, $instanceId);
            //dump($res);
            $collectDataStep->push('Stage 1.1 - Status Terminate : '.$res['status']);
            
        }
        
        $taskDetail = $this->getDetailTaskAssigned($applNo); 
        if($taskDetail != null 
            && ( $taskDetail['taskName'] == 'Verify Supporting Documents' || $taskDetail['taskName'] == 'Review Application' )){
            $collectDataStep->push('Check! Task already exist. No need to refire! => '.$taskDetail['taskId'].' , TaskName: '.$taskDetail['taskName']);
        }else{
            //Refire new instance to SDO
            $collectDataStep->push('Stage 2 - Refire new task - application SM');
            $this->refireSMTaskApplication($applNo, "refire-task", false);
            sleep(5);
        }
        
        //Find Task After Refire
        $collectDataStep->push('Stage 3 Completed Refire Task. Check Task for Pending SDO');
        sleep(3);
        $taskDetailNew = $this->getDetailTaskAssigned($applNo);
        
        
        if($taskDetailNew == null && $applObj->workflow_status_id == 20101){
           $collectDataStep->put('status_process', 2);
           $collectDataStep->push(' Stop here!! do not found Task Assigned for => Verify Supporting Documents');
           return $collectDataStep;
        }
        
        if($taskDetailNew['taskName'] == 'Verify Supporting Documents'){
            $collectDataStep->push('Task assigned to SDO. TaskID : '.$taskDetailNew['taskId'].' , TaskName: '.$taskDetailNew['taskName']);
            
            //Get SDO User
            // 20101 - Pending Supporting Document Verification 
            $sdoUser = $this->getUserLoginIDTakenTask($applNo, '20101');

            if($sdoUser != null){
                $collectDataStep->push('Found SDO user to execute VERIFY task');
                $collectDataStep->push('Stage 4 Verify Task by SDO user : '.$sdoUser->login_id);
                $taskDetailPayload = $this->findApiWorklistTaskDetail($taskDetailNew['taskId']);
                $payloadTemp = $taskDetailPayload['result']['payload'][0];
                $payload = preg_replace('!^[^>]+>!', '', $payloadTemp);
                //dump($payload);
                $result = $this->executeTaskAction($taskDetailPayload['result']['taskId'], $sdoUser->login_id, "VERIFY", $payload, "executeTask");
                if($result && $result->get('status') == 'Failed'){
                    $collectDataStep->push('execute VERIFY task FAILED by this user '.$sdoUser->login_id);
                    $randomUserLoginId = $taskDetailPayload['result']['assignees'][0];
                    $collectDataStep->push('execute VERIFY task using random Assignee User  '.$randomUserLoginId);
                    $result = $this->executeTaskAction($taskDetailPayload['result']['taskId'], $randomUserLoginId, "VERIFY", $payload, "executeTask");
                    $collectDataStep->push('Stage 5 Completed Verify by SDO. Check Task for Approval');
                }else{
                    $collectDataStep->push('Stage 5 Completed Verify by SDO. Check Task for Approval');
                }
            }else{
                $collectDataStep->put('status_process', 2);
                $collectDataStep->push(' Stop here!! not found SDO User!');
                return $collectDataStep;
            } 
        }else{
            $collectDataStep->push('Stage 4 Check Task for Pending SDO (Verify Supporting Documents)  is not found!');
        }

        sleep(5);
        
        //Find Task After Refire
        $collectDataStep->push('Stage 5 Check Task for Approval');
        
        $taskDetailApproval = $this->getDetailTaskAssigned($applNo);
        if($taskDetailApproval == null || $taskDetailApproval['taskName'] != 'Review Application' ){
           $collectDataStep->push(' Stop here!! do not found Task Assigned for Pending Review Application!');
           $collectDataStep->put('status_process', 2);
           return $collectDataStep;
        }
        if($taskDetailApproval['taskName'] == 'Review Application'){
            $collectDataStep->push('Task assigned to PO. TaskID : '.$taskDetailApproval['taskId'].' , TaskName: '.$taskDetailApproval['taskName']);
            
            //Get PO User
            // 20104 - Pending Review Application
            $POUser = $this->getUserLoginIDTakenTask($applNo, '20104');
            if($POUser != null){
                $collectDataStep->push('Found PO user execute verify this appl no. ');
                $collectDataStep->push('Stage 6 PO User  : '.$POUser->login_id.' -> Task to be re-Assign to this user');
                $this->reassignTaskAction($taskDetailApproval['taskId'], $POUser->login_id);
                $collectDataStep->push('Completed Re-Assigned Task to '.$POUser->login_id);
                
                
                try {
                    //Update SM_APPL roolback to status Pending Application Verification (20102)
                    $collectDataStep->push('Stage 7 rollback status to Application Verification (20102) in sm_appl');
                    $this->updateSMApplReviewApplication($applNo); 
                } catch (Exception $ex) {
                    $collectDataStep->put('status_process', 2);
                    $collectDataStep->push(' Stop here!! Stage 7 Failed update SM APPL - Status ID');
                }
                
                
                try {
                    //Delete record in tracking diary. for status 	Pending Site Visit Assignment - Before Approval (20110)
                    //$collectDataStep->push('Stage 8 delete record tracking diary Pending Site Visit Assignment - Before Approval (20110)');
                    //$this->deleteTrackingDiarySiteVisit($applNo); 
                } catch (Exception $ex) {
                    $collectDataStep->put('status_process', 2);
                    $collectDataStep->push(' Stop here!! Stage 8 Failed DELETE Tracking Diary - Site Visit');
                }
                

                $collectDataStep->push('Completed rollback for this appl no : '.$applNo);
                $collectDataStep->put('status_process', 1); // SUCCESS
                sleep(3);
        
            }else{
                $collectDataStep->put('status_process', 2);
                $collectDataStep->push(' Stop here!! not found PO User!');
                return $collectDataStep;
            }
        }
        
        return $collectDataStep;
    }
    
    protected function getDetailTaskAssigned($applNo) {
        $listDataResult = $this->findAPITaskBPMListDocAndModule($applNo, "Supplier_Management");
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
            //dump('   getDetailTaskAssigned '.$applNo.' >> found tasks : ' . count($listdata));

            $collectAssignedTask = collect();
            foreach ($listdata as $key => $row) {
                $listComposite = explode("*", $row['compositeDN']);
                //$listdata[$key]['composite'] = $listComposite[0];
                $row['composite'] = $listComposite[0];
                if($row['state'] == 'ASSIGNED'){
                    $collectAssignedTask->push($row);
                    break;
                }
            }
            //dump($collectAssignedTask);
            // Must be only one instance active with  assigned task.
            if($collectAssignedTask->count() ==  1){
                $taskDetail = $collectAssignedTask->first();
                return $taskDetail;
                
            }else{
                dump('   >> Task Assigned Is Not Found : '.$collectAssignedTask->count());
                Log::info(__FUNCTION__.' >> Task Assigned Is Not Found : '.$collectAssignedTask->count());
                return null;
            }
        }else{
            dump(' ########## not found!');
            Log::info(__FUNCTION__.' >> ########## not found!');
        } 
        return null;
    }
    
    /**
     * 
     * @param type $applNo
     * @param type $actionTask  initiate-task |  refire-task
     * @param type $isPayment  true | false
     * @return type
     */
    protected function refireSMTaskApplication($applNo,$actionTask,$isPayment) {

        $listdata = collect([]);
        $statusAPI = null;
        


        $actionTypeLog = 'Web Service';
        $actionName = 'BPM-SM-CreateTask';

        $parameters =  collect([]);            
        $parameters->put("appl_no", $applNo);
        $parameters->put("action_task", $actionTask);
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
        $parameters->put("module", 'Supplier_Management');


        $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$parameters,$parameters,'Processing');


        if($actionTask === 'initiate-task'){
           $listDataResult = $this->doAPIInitiateSMTaskApplication($applNo); 
           $listdata = $listDataResult;
           if($listDataResult['status']=='Success'){
              EpSupportActionLog::updateActionLog($actionLog, 'Completed'); 
           }else{
              EpSupportActionLog::updateActionLog($actionLog, 'Failed');  
           }
        }else if($actionTask === 'refire-task'){
           dump(' refireSMTaskApplication > do refire appl No. : '.$applNo);
           $listDataResult = $this->doAPIRefireSMTaskApplication($applNo, $isPayment);
           $listdata = $listDataResult;
           if($listDataResult['status']=='Success'){
              EpSupportActionLog::updateActionLog($actionLog, 'Completed'); 
           }else{
              EpSupportActionLog::updateActionLog($actionLog, 'Failed');  
           }
        }else{
            EpSupportActionLog::updateActionLog($actionLog, 'Failed'); 
        }

        return $listdata;
        
    }
    
    protected function executeTaskAction($taskid, $loginID, $executeAction,$payload,$actionTask){
        $data = collect([]);
        $actionTypeLog = 'Web Service';

        $parameters =  collect([]);            
        $parameters->put("taskid", $taskid);
        $parameters->put("user", $loginID);
        $parameters->put("execute_action", $executeAction);
        $parameters->put("payload", $payload);
        $parameters->put("action_task", $actionTask);
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
        $actionName = 'BPM-Execute-Task-Flow';

        $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');

        $status = null;

        if ($actionTask == 'executeTask') {
            
            $executeData = $this->updateExecuteActionAPI($taskid, $loginID, $payload, $executeAction, null);
            if ($executeData['status'] == 'Success') {
                 $status = 'Success';
                $data->put('status', $status);
            } else {
                $status = 'Failed';
                $data->put('status', status);
                $data->put('remarks', $executeData["result"]);
            }
            EpSupportActionLog::updateActionLog($actionLog, $status); 
            return $data;
        }
        
        return $data;
        
    }
    
    protected function getUserLoginIDTakenTask($applNo, $statusId){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT u.*
                    FROM sm_appl a, SM_WORKFLOW_STATUS w,
                      pm_user u
                    WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                          AND w.changed_by = u.user_id 
                          AND a.appl_no = ? 
                          AND w.status_id = ? 
                           ", array($applNo,$statusId));
        if($dataList != null && count($dataList) > 0){
            return $dataList[0];
        }
        return null;
    }
    
    protected function isCurrentSiteVisitTask($applNo){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT count(*) as total
                    FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                    WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                          AND a.status_id = d.status_id
                          AND a.appl_id = d.doc_id
                          AND a.appl_no = ?
                          AND a.status_id = '20110'  -- Pending Site Visit Assignment - Before Approval (20110)
                          AND w.status_id IN (20105,20110 )  -- 20105 Pending Site Visit , 20110 Pending Site Visit Assignment - Before Approval
                          AND w.is_current = 1
                          AND d.actioned_date = (SELECT max(actioned_date)
                                                 FROM PM_TRACKING_DIARY
                                                 WHERE doc_id = d.doc_id AND doc_type = w.doc_type) 
                           ", array($applNo));
        if($dataList != null && count($dataList) > 0 && $dataList[0]->total == 1){
            return true;
        }
        return false;
    }
    
    protected function getCurrentStatus($applNo){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "
                SELECT
                  a.appl_no,
                  d.tracking_diary_id,
                  d.status_id                                              AS tracking_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = d.status_id AND language_code = 'ms') AS tracking_status,
                  w.workflow_status_id,
                  w.status_id                                              AS workflow_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = w.status_id AND language_code = 'ms') AS workflow_status,
                  a.appl_id,
                  a.status_id                                              AS appl_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = a.status_id AND language_code = 'ms') AS appl_status,
                  a.record_status,
                  a.is_active_appl

                FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                      AND a.status_id = d.status_id
                      AND a.appl_id = d.doc_id
                      AND a.appl_no = ? 
                      AND w.is_current = 1
                      AND d.actioned_date = (SELECT max(actioned_date)
                                             FROM PM_TRACKING_DIARY
                                             WHERE doc_id = d.doc_id AND doc_type = w.doc_type)
                           ", array($applNo));
        if($dataList != null && count($dataList) > 0 ){
            return $dataList[0];
        }
        return null;
    }

    protected function getCurrentStatusWithoutCheckMaxDate($applNo){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "
                SELECT
                  a.appl_no,
                  d.tracking_diary_id,
                  d.status_id                                              AS tracking_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = d.status_id AND language_code = 'ms') AS tracking_status,
                  w.workflow_status_id,
                  w.status_id                                              AS workflow_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = w.status_id AND language_code = 'ms') AS workflow_status,
                  a.appl_id,
                  a.status_id                                              AS appl_status_id,
                  (SELECT status_name
                   FROM pm_status_desc
                   WHERE status_id = a.status_id AND language_code = 'ms') AS appl_status,
                  a.record_status,
                  a.is_active_appl

                FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                      AND a.status_id = d.status_id
                      AND a.appl_id = d.doc_id
                      AND a.appl_no = ? 
                      AND w.is_current = 1
                           ", array($applNo));
        if($dataList != null && count($dataList) > 0 ){
            return $dataList[0];
        }
        return null;
    }
    
    
    protected function findApiWorklistTaskDetail($taskId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/taskById?task_id=" . $taskId;
            
            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);
                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
            
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => "Failed to connect");
        }
    }
    
    
    protected function reassignTaskAction($taskId, $assignee){
        $data = collect([]);
        
        

        $reassignTask = $this->reassignTask($taskId, $assignee);

        if ($reassignTask['status'] == 'Success') {
             $status = 'Success';
            $data->put('status', $status);
        } else {
            $status = 'Failed';
            $data->put('status', $reassignTask["result"]);
        }


        return $data;
        
        
    }
    
    protected function updateSMApplReviewApplication($applNo){
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_APPL')
                ->where('appl_no', $applNo);
        dump('Before update '.__FUNCTION__);
        $check =  $query->select('appl_id','appl_no','status_id','changed_date')->first();
        dump($check);
        if($check != null){
            $queryUpd =  DB::connection('oracle_nextgen_fullgrant')
               ->table('SM_APPL')
               ->where('appl_no', $applNo);
            $queryUpd->update(['status_id'=> 20102,'changed_date'=>Carbon::now()]); 
        }
        dump('After update '.__FUNCTION__);
        dump($query->select('appl_id','appl_no','status_id','changed_date')->first());
        
    }
    
    protected function deleteTrackingDiarySiteVisit($applNo){
        $QueryData =  DB::connection('oracle_nextgen_fullgrant')
                ->table('PM_TRACKING_DIARY')
                ->where('doc_no', $applNo)
                ->where('doc_type', 'SR')
                ->whereIn('status_id',['20110']);
                
        dump('Before delete '.__FUNCTION__);   
        $checkData = $QueryData->count();
        dump('Check Tracking SiteVisit >  Total :'.$checkData);
        if($checkData > 0 && $checkData < 3){
            DB::connection('oracle_nextgen_fullgrant')
                ->table('PM_TRACKING_DIARY')
                ->where('doc_no', $applNo)
                ->where('doc_type', 'SR')
                ->whereIn('status_id',['20110'])
                ->delete();
        }
        dump('After delete '.__FUNCTION__);    
        dump($QueryData->count());
        
    }
    
    
}
