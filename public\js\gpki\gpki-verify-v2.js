var start = new Date().getTime();
var modeUsed = "Production"; //Training/Production

$(document).ready(function () {
  $("#listMedium").hide();
  $("#reqOtp").hide();
  $("#reqVerify").hide();
  $("#reqVerifySoftToken").hide();
  $("#reqEmail").hide();

  const emailRadio = document.getElementById("2");

  emailRadio.addEventListener("change", function () {
    if (emailRadio.checked || modeUsed == "Training") {
      $("#reqEmail").show();
    }
  });

  const mobilRadio = document.getElementById("1");

  mobilRadio.addEventListener("change", function () {
    if (mobilRadio.checked & (modeUsed == "Production")) {
      $("#reqEmail").hide();
    }
  });

  $(document).ready(function () {
    $("#verifyButton").click(function () {
      var originalText = encodeURIComponent(
        Gpki.hash($("#originalText").val())
      );
      var signedText = encodeURIComponent($("#signedText").val());
      verify(originalText, signedText);
    });
  });

  $("#requestMedium").click(function () {
    if ($("#nric").val() == "" || $("#applicationCode").val() == "") {
      Gpki.alert("Sila masukkan ID Sijil Digital");
    } else {
      if ($("#mode").is(":checked")) {
        modeUsed = "Training";
      } else {
        modeUsed = "Production";
      }
      var nric = $("#nric").val();
      var applicationCode = $("#applicationCode").val();
      request_mediumList(nric, applicationCode, modeUsed);
    }
  });

  $("#nextStep").click(function () {
    if ($("#nric2").val() == "" || $("#mediumId").val() == "") {
      Gpki.alert("Pilih medium");
    } else {
      var nric2 = $("#nric2").val();
      var mediumId = $("#mediumId").val();

      $("#listMedium").hide();
      $("#reqNric").hide();
      $("#reqOtp").show();
      $("#userId").val(nric2);
      $("#nric3").val(nric2);
      // $("#nric3").val(nric2);
      $("#mediumId2").val(mediumId);

      if (mediumId == 5) {
        if (modeUsed == "Training") {
          $("#reqEmail").show();
        } else {
          $("#reqEmail").hide();
        }

        $("#reqOtp").show();
        $("#reqVerifySoftToken").hide();
      } else {
        $("#reqVerifySoftToken").show();
        $("#reqOtp").hide();
      }
    }
  });

  $("#requestOtp").click(function () {
    var radioButtons = document.getElementsByName("saluran");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        var saluran = radioButtons[i].value;
        break;
      }
    }

    if (
      $("#nric3").val() == "" ||
      $("#agencyId").val() == "" ||
      $("#userId").val() == "" ||
      $("#otpExp").val() == ""
    ) {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      var nric3 = $("#nric3").val();
      var agencyId = $("#agencyId").val();
      var userId = $("#userId").val();
      var otpExp = $("#otpExp").val();

      var emailuser = "";
      console.log("email user - ", $("#emailuser").val());
      if ($("#emailuser").val() !== null) {
        emailuser = $("#emailuser").val();
      }

      request_otp(
        nric3,
        agencyId,
        userId,
        modeUsed,
        otpExp,
        saluran,
        emailuser
      );
    }
  });

  $("#requestVerify").click(function () {
    if (
      $("#nric2").val() == "" ||
      $("#nonce").val() == "" ||
      $("#otpCode").val() == "" ||
      $("#plaintext").val() == "" ||
      $("#agencyId").val() == "" ||
      $("#signature").val() == ""
    ) {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      var nric = $("#nric2").val();
      var nonce = $("#nonce").val(); //id transaksi
      var otpCode = $("#otpCode").val();
      var plaintext = btoa(Gpki.hash($("#plaintext").val()));
      var signature = $("#signature").val();
      var agencyId = $("#agencyId").val();

      start = new Date().getTime();
      verify_roaming(nric, nonce, otpCode, plaintext, agencyId, signature);
    }
  });
});

function parseTimeout(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    //var statusCode = obj.status_code;

    var statusMsg = obj.status_message;
    //$("#gpki-data").html(statusMsg);

    var extMsg = obj.status_ext_message;
    Gpki.alert(extMsg);
  });
}

/* put your logic to handle the result here */
function parseResultMedium(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;

    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      $("#listMedium").show();
      $("#reqNric").hide();
      var resp = jQuery.parseJSON(obj.response);
      var nric = jQuery.parseJSON(obj.nric);

      $("#nric2").val(nric);
      const selectElement = document.querySelector("#mediumId");

      const options = resp;

      options.forEach((option) => {
        const newOption = document.createElement("option"); // create a new <option> element
        newOption.value = option.id; // set the value of the option to the id of the object in the array
        newOption.text = option.mediumType; // set the text of the option to the mediumType property of the object in the array
        selectElement.add(newOption); // add the new option to the select element
      });

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result here */
function parseTxResultRequestOTP(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;

    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      var resp = jQuery.parseJSON(obj.response);

      var nonce = resp.nonce; //id transaksi

      var startDate = resp.start_date;
      var endDate = resp.end_date;
      var subjectDN = resp.subject_dn;
      var serialNo = resp.serial_no;

      var nric = $("#nric3").val();
      var userId = $("#userId").val();
      $("#nric4").val(nric);
      $("#userId2").val(userId);
      $("#nonce").val(nonce); //id transaksi

      $("#reqNric").hide();
      $("#listMedium").hide();
      $("#reqOtp").hide();
      $("#reqVerify").show();

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result here */
function parseTxResultVerifyRoaming(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      var resp = jQuery.parseJSON(obj.response);
      var nonce = resp.nonce; //Id Transaksi

      $("#reqNric").hide();
      $("#listMedium").hide();
      $("#reqOtp").hide();
      $("#reqVerify").show();
      console.log("obj -- ", obj);

      var serialno = resp.serial_no;
      var validity = resp.cert_validity;
      var subjectDN = resp.subject_dn;
      var signedPayload = resp.signed_payload;

      msg =
        "This signature is VALID<br>" +
        "serialno:" +
        serialno +
        "\n<br>" +
        "validity:" +
        validity +
        "\n<br>" +
        "subjectDN:" +
        subjectDN;
      console.log("msg - ", msg);
      $("#gpki-data").html(msg);
      Gpki.alert(statusMsg);
    } else {
      console.log("obj ", obj);
      Gpki.alert(statusMsg);
      //handle here for otp still valid. must use nonce value return by requestOtp
      if (obj.code == "25") {
        $("#requestVerify").show();
        $("#requestOtp").hide();
      } else if (statusMsg == "Pengesahan Tandatangan Digital Tidak Sah") {
        console.log("else if");
        $("#gpki-data").html("This signature is NOT VALID");
      } else {
        console.log("else");
        $("#gpki-data").html(statusMsg);
      }
    }

    // console.log("time >>",time)
    time /= 1000;

    // get seconds
    var seconds = Math.round(time);
    // console.log(seconds + " seconds");
  });
}

function parseVerifyResult(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var statusCode = obj.status_code;
    var statusMsg = obj.status_message;

    console.log("obj - ", obj);
    console.log("obj - ", statusMsg);

    if (statusCode == "0") {
      var serialno = obj.serial_no;
      var validity = obj.cert_validity;
      var subjectDN = obj.subject_dn;
      var signedPayload = obj.signed_payload;

      msg =
        "This signature is VALID<br>" +
        "serialno:" +
        serialno +
        "\n<br>" +
        "validity:" +
        validity +
        "\n<br>" +
        "subjectDN:" +
        subjectDN;
      console.log("msg - ", msg);
      $("#gpki-data").html(msg);

      Gpki.alert("Pengesahan Tandatangan Digital Sah");
    } else {
      $("#gpki-data").html("This signature is NOT VALID");
      Gpki.alert("Pengesahan Tandatangan Digital Tidak Sah");
    }
  });
}
