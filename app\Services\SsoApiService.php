<?php

namespace App\Services;

use DB;
use App\Migrate\MigrateUtils;
use SSH;
use GuzzleHttp\Client;
use Guzzle;
use Log;

/**
 * SSO eP Web Services 
 *
 * <AUTHOR>
 */
class SsoApiService {

    /** Get Base URL for SSO Webservice*/
    private static function getBaseUrlWsSso(){
        return env("BASE_URL_WS_SSO","http://192.168.0.1:14000");  //default pointing
    }

    /** Get Base SSO Webservice : User Management*/
    private static function getBaseServiceUserMgmt(){
        return '/OIMWS-context-root/UserManagementPort';
    }


    public static function callUserChangePassword($loginId,$password,$isSendNotification){
        $isSendNotification = 'false';
        if($isSendNotification == 1){
            $isSendNotification = 'true';
        }
        Log::info(__METHOD__.' entering.. ');
        Log::info(__METHOD__."      loginId >> $loginId  , password >> $password , isSendNotification >> $isSendNotification");
        $xmlData    = 
        "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:use='http://user.client.oim/'>"
            . "<x:Header/>"
            . "<x:Body>"
                . "<use:userChangePassword>"
                    . "<arg0>"
                        . "<localeString />"
                        . "<oimUser>"
                            . "<userID>$loginId</userID>"
                            . "<password>$password</password>"
                        . "</oimUser>"
                        . "<sendNotification>$isSendNotification</sendNotification>"
                        . "<setPasswordResetFlag>false</setPasswordResetFlag>"
                        . "<userId>$loginId</userId>"
                    . "</arg0>"
                . "</use:userChangePassword>"
            . "</x:Body>"
        . "</x:Envelope>";


        $client = new Client([
            'base_uri' =>  self::getBaseUrlWsSso()
          ]);
        $fullUrl =   self::getBaseUrlWsSso().self::getBaseServiceUserMgmt();
        Log::info(__METHOD__." URL Trigger: ".$fullUrl);  

        try {
            $resp = $client->post(self::getBaseServiceUserMgmt(), [
            //'debug' => TRUE,
            'body' => $xmlData,
            'headers' => [
                'SOAPAction'   => '{N/A}',
                'Content-Type' => 'text/xml; charset=utf-8',
            ]
            ]);
            $res = collect([
                'status_sent' =>'Success',
                'status_code' =>$resp->getStatusCode(),
                'xml'=> $xmlData]);
            Log::info(__METHOD__.' Result: '.json_encode($res));
        } catch (\Exception $th) {
            $res = collect([
                'status_sent' =>'Failed',
                'status_code' =>'99',
                'status_description' =>$th->getMessage(),
                'xml'=> $xmlData]);
            Log::info(__METHOD__.' Result: '.json_encode($res));
        }
        return $res;
    }
    
}
