from google.analytics.data_v1beta import BetaAnalyticsDataClient
from google.analytics.data_v1beta.types import (
    DateRange,
    Dimension,
    Metric,
    RunReportRequest,
    Filter,
    FilterExpression
)

import os
import json

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = r"service-account-file.json"

def get_pageview(property_id):
    """Runs a report on Google Analytics 4 property for page views by page title and total."""
    client = BetaAnalyticsDataClient()

    try:
        request = RunReportRequest(
            property=f"properties/{property_id}",
            dimensions=[Dimension(name="pageTitle")],
            metrics=[Metric(name="screenPageViews")],
            date_ranges=[DateRange(start_date="2023-01-01", end_date="today")],
            dimension_filter=FilterExpression(
                filter=Filter(
                    field_name="pageTitle",
                    string_filter=Filter.StringFilter(
                        value="Selamat Datang ke Portal Rasmi ePerolehan - eP<PERSON>lehan"
                    )
                )
            )
        )

        response = client.run_report(request)

        result = {
            "status": "success",  # You can change this based on the actual status of the operation
            "message": "Data retrieved successfully",
            "data": {}
        }

        for row in response.rows:
            page_title = row.dimension_values[0].value
            page_views = int(row.metric_values[0].value)
            result["data"] = {
                "page_title": page_title,
                "page_views": page_views
            }

        print(json.dumps(result))

    except Exception as e:
        result = {
            "status": "error",
            "message": f"An error occurred: {str(e)}",
            "data": {}
        }
        print(json.dumps(result))

property_id = "349994770"
get_pageview(property_id)
