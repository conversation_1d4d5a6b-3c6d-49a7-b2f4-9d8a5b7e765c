<?php

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use Carbon\Carbon;
use SSH;
use stdClass;
use Exception;
use Guzzle;
use Log;
use Illuminate\Http\Request;

class EpSupplierController extends Controller
{

    use SupplierService;
    use OSBService;
    use ProfileService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findSupplier()
    {

        $carian = request()->carian;
        $collectionData = collect();
        if ($carian && strlen($carian) > 0) {
            $typeSearch = $this->searchFilterSupplier($carian);
            $collectionData =  $this->getDetailsSupplierForCSViewer($carian, $typeSearch);

            //Added to auto sync into CRM Database.
            try {
                if (count($collectionData) > 0) {
                    foreach ($collectionData as $dataObj) {
                        $dataUser = collect($dataObj['listPersonnel']);
                        $logins = $dataUser->pluck('login_id');
                        if ($logins->count() > 0 && strlen($logins->first()) > 0) {
                            $urlIntegrateCrm = env("CRM_INTEGRATION", "http://192.168.62.153") . "/rest/user/" . $logins->first();
                            $resultResp = json_decode(Guzzle::get($urlIntegrateCrm, ['timeout' => 15])->getBody(), true);
                            Log::info("Integrate CRM $urlIntegrateCrm : " . json_encode($resultResp));
                        }
                    }
                }
            } catch (\GuzzleHttp\Exception\ConnectException $ex) {
                Log::info($ex->getMessage());
                Log::info($ex->getTraceAsString());
            }
        }
        //dump($collectionData);
        // dd($collectionData);
        return view('sm.supplier_info_cs', [
            'carian' => $carian,
            'data' => $collectionData
        ]);
    }

    public function getCountApplicationInquiries() {
        $appId = request()->appId;
        $count = count($this->getListApplicationInquiriesService($appId));
        //Return the html to display count
        if($count > 0) {
        $html = "
        <strong>
            <span class='bolder'>Inquiries</span>
            <a href='#modal-list-data-inquiries-reject' 
            class='modal-list-data-action label label-danger' 
            data-toggle='modal' data-url='/find/supplier/application-inquiry/list/$appId'
            data-title='Inquiries' >
            $count
            </a>
        </strong>
        ";
        } else {
            $html = "";
        }
        return $html;
    }

    public function getListApplicationInquiries() {
        $appId = request()->appId;
        $result = $this->getListApplicationInquiriesService($appId);
        //Return the html to display result
        $html = "";

        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>INQUIRY DATE</th>
                        <th class='text-center'>RESPONSE DATE</th>
                        <th class='text-center'>INQUIRY TEXT</th>
                        <th class='text-center'>INQUIRY SUBJECT</th>
                        <th class='text-center'>RESPONSE TEXT</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td class='text-center'><strong>$data->inquiry_date</strong></td>
                <td class='text-center'><strong>$data->response_date</strong></td>
                <td class='text-center'><strong>$data->inquiry_text</strong></td>
                <td class='text-center'><strong>$data->inquiry_subject</strong></td>
                <td class='text-center'><strong>$data->response_text</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>";
                
        return $html;

    }

    public function getCountApplicationRejected() {
        $appId = request()->appId;
        $count = count($this->getListApplicationRejectedService($appId));
        //Return the html to display count
        if($count > 0) {
        $html = "
        <strong>
            <span class='bolder'>Rejected</span>
            <a href='#modal-list-data-inquiries-reject' 
            class='modal-list-data-action label label-danger' 
            data-toggle='modal' data-url='/find/supplier/application-rejected/list/$appId'
            data-title='Rejected' >
            $count
            </a>
        </strong>
        ";
        } else {
            $html = "";
        }
        return $html;
    }

    public function getListApplicationRejected() {
        $appId = request()->appId;
        $result = $this->getListApplicationRejectedService($appId);
        //Return the html to display result
        $html = "";

        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>PO REMARK</th>
                        <th class='text-center'>AP REMARK</th>
                        <th class='text-center'>APPROVED BY PO?</th>
                        <th class='text-center'>APPROVED BY AP?</th>
                        <th class='text-center'>CAT SUP STATUS</th>
                        <th class='text-center'>CATEGORY CODE</th>
                        <th class='text-center'>CATEGORY L1 NAME</th>
                        <th class='text-center'>CATEGORY L2 NAME</th>
                        <th class='text-center'>CATEGORY L3 NAME</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td class='text-center'><strong>$data->po_remark</strong></td>
                <td class='text-center'><strong>$data->ap_remark</strong></td>
                <td class='text-center'><strong>$data->is_approved_by_po</strong></td>
                <td class='text-center'><strong>$data->is_approved_by_ap</strong></td>
                <td class='text-center'><strong>$data->cat_sup_status</strong></td>
                <td class='text-center'><strong>$data->category_code</strong></td>
                <td class='text-center'><strong>$data->category_l1_name</strong></td>
                <td class='text-center'><strong>$data->category_l2_name</strong></td>
                <td class='text-center'><strong>$data->category_l3_name</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>";
                
        return $html;

    }


    public function getListTotalAddress($personnelId)
    {
        $result = $this->getListTotalAddressService($personnelId);
        $rowHeader = [
            'Address Type',
            'Address Id',
            'Address 1',
            'Address 2',
            'Address 3',
            'Postcode',
            'Country Id',
            'State Id',
            'Division Id',
            'District Id',
            'City Id',
            'SSM Data'
        ];
        $html = "<thead>" . self::generateHeaderRow($rowHeader) . "</thead><tbody>";
        foreach ($result as $rowData) {
            $html .= self::generateTableRow($rowData);
        }
        return $html;
    }

    /**
     * Return Collection Data Suppliers & Personnels
     * @param type $carian
     * @param type $type
     * @return collection
     */
    protected function getDetailsSupplierForCSViewer($carian, $type)
    {
        $collectionData = collect();
        if ($type === 'IcNo') {
            $listSupplier = $this->getSMSupplierUsersByIcNo($carian);
        } else {
            // can be type: MOF, ePNo, SUPPLIER_NAME
            $listSupplier = $this->getSMSupplierInfo($carian, $type);
        }
        //dump('Total:'.count($listSupplier));
        if ($listSupplier != null && count($listSupplier) > 0) {

            // Can be more than one company
            foreach ($listSupplier as $supp) {

                $supplier = $supp;
                if ($type === 'IcNo') {
                    $supplier =  $this->getSMSupplierInfo($supp->supplier_id, "ID")->first();
                }

                $mofDetail = $this->getSMSupplierMofDetail($supp->supplier_id);

                $listPersonnel = collect($this->getSMSupplierUsersActiveBySupplierID($supp->supplier_id));
                if ($type === 'IcNo') {
                    $listPersonnel = $listPersonnel->where('p_identification_no', $carian)->all();
                } else {
                    //$listPersonnel = $listPersonnel->whereIn('p_ep_role', ['MOF_SUPPLIER_ADMIN', 'BASIC_SUPPLIER_ADMIN', 'G2G_ADMIN', 'GOVT_SELLER', 'SUPPLIER_TEMP'])->all();
                    $listPersonnel = $listPersonnel->whereIn('p_ep_role', ['MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER', 'BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER', 'FL_USER', 'G2G_ADMIN', 'GOVT_SELLER', 'SUPPLIER_TEMP'])->all();
                    
                }

                //Repopulate ListPersonnel
                foreach ($listPersonnel as $data) {
                    $data = $this->populatePersonnelUserData($data);
                }


                $applDetail = $this->getSmApplDetail($supp->latest_appl_id);
                //$listWorkFlow = $this->getWorkFlowSupplierProcess($supp->supplier_id, $supp->latest_appl_id);
                $listSuppTrackDiary = null != $applDetail ? $this->getTrackingDiarySupplierByDocNo($applDetail->appl_no) : null;
                $listAttachmentCancelReject = $this->getListAttachmentRejectOrCancel($supp->latest_appl_id);
                $listRemarksCancelReject = $this->getListRemarksRejectOrCancel($supp->latest_appl_id);
                $totalItems = $this->getTotalItemsSupplier($supp->supplier_id);
                $sapVendorCode = $this->getMainSapVendorCode($supp->ep_no);

                $suppMofStatus = $this->getSupplierMofStatus($supp->supplier_id);
                //$listSuppMofVirtCert = $this->getSupplierMofVirtCert($supp->supplier_id);
                //$listSuppPayment = $this->getPaymentSuppliers($supp->supplier_id);
                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($supp->supplier_id);


                $listInProgressSuppTrackDiary = null;
                if ($applDetail && count($listinProgressSuppProcessAppl) > 0) {
                    $listInProgressSuppTrackDiary = $this->getTrackingDiarySupplierByDocNo($applDetail->appl_no);
                }


                $basicCompInfo = $this->getBasicSupplierInfo($supp->latest_appl_id);

                $listApplRejectReason = $this->getListApplRejectReason($supp->latest_appl_id);
                $listApplSectionReview = $this->getListApplSectionReview($supp->latest_appl_id);
                if ($basicCompInfo && $basicCompInfo != null) {

                    $basicCompInfo->is_with_federal = EPService::$YES_NO[$basicCompInfo->is_with_federal];
                    $basicCompInfo->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
                    $basicCompInfo->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
                    $basicCompInfo->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
                    $basicCompInfo->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];


                    $basicCompInfo->is_name_hq_non_ascii = false;
                    if (mb_check_encoding($basicCompInfo->company_name, 'ASCII') == false) {
                        $basicCompInfo->is_name_hq_non_ascii = true;
                    }


                    $basicCompInfo->nonAsciiHqDetected = false;
                    if (mb_check_encoding($basicCompInfo->address_1, 'ASCII') == false) {
                        $basicCompInfo->nonAsciiHqDetected = true;
                    }
                    if (mb_check_encoding($basicCompInfo->address_2, 'ASCII') == false) {
                        $basicCompInfo->nonAsciiHqDetected = true;
                    }
                    if (mb_check_encoding($basicCompInfo->address_3, 'ASCII') == false) {
                        $basicCompInfo->nonAsciiHqDetected = true;
                    }
                }

                /*
                $listSupplierBranch = $this->getListSupplierBranch($supp->latest_appl_id);
                $checkNonAscii = false;
                $checkBranchNameNonAscii = false;
                if (count($listSupplierBranch) > 0) {
                    foreach ($listSupplierBranch as $branch) {

                        $branchNameNonAscii = false;
                        if (mb_check_encoding($branch->branch_name, 'ASCII') == false) {
                            $branchNameNonAscii = true;
                            $checkBranchNameNonAscii = true; //SetParent
                        }
                        $branch->is_branch_name_non_ascii = $branchNameNonAscii;

                        $nonAsciiDetected = false;
                        if (mb_check_encoding($branch->address_1, 'ASCII') == false) {
                            $nonAsciiDetected = true;
                        }
                        if (mb_check_encoding($branch->address_2, 'ASCII') == false) {
                            $nonAsciiDetected = true;
                        }
                        if (mb_check_encoding($branch->address_3, 'ASCII') == false) {
                            $nonAsciiDetected = true;
                        }
                        $branch->is_non_ascii = $nonAsciiDetected;
                        if ($branch->is_non_ascii == true) {
                            $checkNonAscii = true;
                        }

                        $branch->sap_vendor_code = '-';
                        $branchSapVendorCode = $this->getMainSapVendorCodeByBranchCode($supp->ep_no, $branch->branch_code);
                        if ($branchSapVendorCode) {
                            $branch->sap_vendor_code = $branchSapVendorCode->sap_vendor_code;
                        }
                    }
                }
                */

                //$listSupplierBank = $this->getListSupplierBank($supp->latest_appl_id);
                //$hqGstInfo = $this->getHqGstInfo($supp->supplier_id);

                /*
                $listSupplierCategoryCode = $this->getListSupplierCategoryCode($supp->latest_appl_id);
                if (count($listSupplierCategoryCode) > 0) {
                    foreach ($listSupplierCategoryCode as $cat) {
                        $cat->is_approved_by_po = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_po];
                        $cat->is_approved_by_ap = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_ap];
                        $cat->record_status = EPService::$RECORD_STATUS[$cat->record_status];
                    }
                }
                */

                $listApplHistoryDetails = $this->getHistoryApplCsView($supp->supplier_id);
                $listApplHistoryDetails = collect($listApplHistoryDetails)->take(5);
                foreach ($listApplHistoryDetails as $row) {
                    $listRemarksCancelReject = $this->getListRemarksRejectOrCancel($row->appl_id);
                    $row->listRemarksCancelReject = $listRemarksCancelReject;
                    $listAttachmentCancelReject = $this->getListAttachmentRejectOrCancel($row->appl_id);
                    $row->listAttachmentCancelReject = $listAttachmentCancelReject;
                    $listApplRejectReason = $this->getListApplRejectReason($row->appl_id);
                    $row->listApplRejectReason = $listApplRejectReason;
                    $listApplSectionReview = $this->getListApplSectionReview($row->appl_id);
                    $row->listApplSectionReview = $listApplSectionReview;
                }
                //dd($listApplHistoryDetails);

                $dataDetailSupp = [
                    'supplier' => $supplier,
                    'listPersonnel' => $listPersonnel,
                    'applDetail' => $applDetail,
                    'totalItems' => $totalItems,
                    'sapVendorCode' => $sapVendorCode,
                    'basicCompInfo' => $basicCompInfo,
                    'mofDetail' => $mofDetail,
                    'listSuppTrackDiary' => $listSuppTrackDiary,
                    'suppMofStatus' => $suppMofStatus,
                    //'listSuppPayment' => $listSuppPayment,
                    //'listSuppMofVirtCert' => $listSuppMofVirtCert,
                    //'listSupplierBranch' => $listSupplierBranch,
                    //'listSupplierBank' => $listSupplierBank,
                    //'listSupplierCategoryCode' => $listSupplierCategoryCode,
                    //'hqGstInfo' => $hqGstInfo,
                    'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                    'listInProgressSuppTrackDiary' => $listInProgressSuppTrackDiary,
                    //'listWorkFlow' => $listWorkFlow,
                    'listAttachmentCancelReject' => $listAttachmentCancelReject,
                    'listRemarksCancelReject' => $listRemarksCancelReject,
                    'listApplRejectReason' => $listApplRejectReason,
                    'listApplSectionReview' => $listApplSectionReview,
                    'listApplHistoryDetails' => $listApplHistoryDetails
                ];

                $collectionData->push($dataDetailSupp);
            }
        }

        return $collectionData;
    }

    /**
     * Switch Carian - Easy Seacrh on UI. Auto Switch
     * Result : MOF, ePNo, IcNo, SUPPLIER_NAME
     * @param type $search
     * @param type $type
     * @return boolean
     */
    protected function searchFilterSupplier($search)
    {
        $checkMof = substr($search, 0, 4);
        $checkEpNo = substr($search, 0, 3);
        $checkIcno = strlen(trim($search));
        $checkApplNo = substr($search, 0, 2);

        if ($checkMof === '357-' || $checkMof === '465-') {
            return "MOF";
        }
        if ($checkEpNo === 'eP-') {
            return "ePNo";
        }
        if ($checkIcno === 12) {
            return "IcNo";
        }
        if (strlen(intval($search)) === 9) {
            return "IcNo";
        }
        if ($checkApplNo == 'JR' || $checkApplNo == 'JN' || $checkApplNo == 'KR' || $checkApplNo == 'KN' || $checkApplNo == 'KU' || $checkApplNo == 'KA' || $checkApplNo == 'KB' || $checkApplNo == 'KC') {
            return "ApplNo";
        }
        return "SUPPLIER_NAME";;
    }

    public function findSupplierCodi()
    {

        $carian = request()->carian;
        $collectionData = collect();
        if ($carian && strlen($carian) > 0) {
            $typeSearch = $this->searchFilterSupplier($carian);
            $collectionData =  $this->getDetailsSupplierForCSViewer($carian, $typeSearch);
        }
        //dump($collectionData);
        return view('codi.supplier_info', [
            'carian' => $carian,
            'data' => $collectionData
        ]);
    }

    private function generateHeaderRow($rowHeader)
    {
        return "<tr><th>" . implode("</th><th>", $rowHeader) . "</th></tr>";
    }

    private function generateTableRow($data)
    {
        $rowData = [
            $data->address_type,
            $data->address_id,
            $data->address_1,
            $data->address_2,
            $data->address_3,
            $data->postcode,
            $data->country_id,
            $data->state_id,
            $data->division_id,
            $data->district_id,
            $data->city_id,
            $data->ssm_data,
        ];

        return "<tr><td>" . implode("</td><td>", $rowData) . "</td></tr>";
    }
}
