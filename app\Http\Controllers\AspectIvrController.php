<?php

namespace App\Http\Controllers;

use App\IvrLog;
use App\Services\EPService;
use App\Services\Traits\SupplierService;
use Exception;
use Illuminate\Http\Request;
use DateTime;
use Carbon\Carbon;

class AspectIvrController extends Controller
{
    use SupplierService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // $this->middleware('auth');
    }

    public function getApplicationStatusByMof(Request $request)
    {
        $result = array();
        try {
            if (request()->isMethod("POST")) {
                $this->validate($request, [
                    'token' => 'required',
                    'mof_no' => 'required|numeric',
                    'appl_no' => 'required|numeric'
                ]);

                // validate token
                $isRequestValid = $this->validateToken($request);
                // $isRequestValid['StatusToken'] = 'Valid';

                if ($isRequestValid['StatusToken'] === 'Valid') {

                    $mof_no = '';
                    $appl_no = '';
                    if ($request->mof_no) {
                        $mof_no = substr_replace($request->mof_no, '-', 3, 0); // add '-' to MOF Number
                    }
                    if ($request->appl_no) {
                        // $appl_no = substr_replace($request->appl_no, '-', 4, 0); // add '-' to application number
                        $appl_no = $request->appl_no;
                    }

                    //find mof no
                    $mof = $this->getIvrMofNo($mof_no);
                    if ($mof) {
                        $dataList = $this->getIvrApplNo($mof[0]->supplier_id, '%' . $appl_no);
                        if ($dataList) {
                            $languages = [];
                            foreach ($dataList as $data) {
                                $languages[] = [
                                    'language_code' => $data->language_code,
                                    'status_name' => $data->status_name,
                                ];
                            }
                            $result = array(
                                'response' => $this->responseHandler(00),
                                'appl_no' => $dataList[0]->appl_no,
                                'status_id' => $dataList[0]->status_id,
                                'status_desc' => $languages
                            );
                        } else {
                            $result = ['response' => $this->responseHandler(01)];
                        }
                    } else {
                        $result = ['response' => $this->responseHandler(01)];
                    }
                } else {
                    $result = ['response' => $this->responseHandler(04)];
                }
            }

            $logData = [
                'request_type' => 'APPLICATION_STATUS',
                'request_data' => $request->all(),
                'response' => $result,
                'status' => 'success'
            ];

            $this->insertLog($logData, $request);

            return response()->json($result, 200);
        } catch (Exception $e) {

            $result = response()->json(['response' => $this->responseHandler(03), 'error' => $e->getMessage()], 500);

            $logData = [
                'request_type' => 'APPLICATION_STATUS',
                'request_data' => $request->all(),
                'response' => $result,
                'status' => 'error'
            ];
            $this->insertLog($logData, $request);

            return $result;
        }
    }

    public function getSoftcertStatusByMof(Request $request)
    {
        $result = array();
        try {
            if (request()->isMethod("POST")) {
                $this->validate($request, [
                    'token' => 'required',
                    'mof_no' => 'required|numeric',
                    'ic_no' => 'required|numeric'
                ]);

                // validate token
                $isRequestValid = $this->validateToken($request);
                // $isRequestValid['StatusToken'] = 'Valid';

                if ($isRequestValid['StatusToken'] === 'Valid') {

                    $mof_no = '';
                    $ic_no = '';
                    if ($request->mof_no) {
                        $mof_no = substr_replace($request->mof_no, '-', 3, 0); // add '-' to MOF Number
                    }
                    if ($request->ic_no) {
                        // $appl_no = substr_replace($request->appl_no, '-', 4, 0); // add '-' to application number
                        $ic_no = $request->ic_no;
                    }

                    //find mof no
                    $mof = $this->getIvrMofNo($mof_no);
                    if ($mof) {
                        $data = $this->getIvrSoftcertStatus($mof[0]->supplier_id, '%' . $ic_no);
                        if ($data) {
                            $result = array(
                                'response' => $this->responseHandler(00),
                                'status_code' => $data[0]->is_softcert,
                                'status_desc' => EPService::$SOFTCERT_STATUS[$data[0]->is_softcert]
                            );
                        } else {
                            $result = ['response' => $this->responseHandler(01)];
                        }
                    } else {
                        $result = ['response' => $this->responseHandler(01)];
                    }
                } else {
                    $result = ['response' => $this->responseHandler(04)];
                }
            }

            $logData = [
                'request_type' => 'SOFTCERT_STATUS',
                'request_data' => $request->all(),
                'response' => $result,
                'status' => 'success'
            ];

            $this->insertLog($logData, $request);

            return response()->json($result, 200);
        } catch (Exception $e) {

            $result = response()->json(['response' => $this->responseHandler(03), 'error' => $e->getMessage()], 500);

            $logData = [
                'request_type' => 'SOFTCERT_STATUS',
                'request_data' => $request->all(),
                'response' => $result,
                'status' => 'error'
            ];
            $this->insertLog($logData, $request);
            
            return $result;
        }
    }

    public function responseHandler($code)
    {
        return ['code' => str_pad($code, 2, "0", STR_PAD_LEFT), 'desc' => $this::$RESPONSE_CODE[$code]];
    }

    public static $RESPONSE_CODE = array(
        00 => 'SUCCESS',
        01 => 'RECORD NOT FOUND',
        02 => 'BUSINESS ERROR',
        03 => 'TECHNICAL ERROR',
        04 => 'VALIDATION ERROR',
        05 => 'UNAUTHORIZED',
    );

    public function mockRequestToken($token)
    {
        //todo mock requetst token
    }

    /**
     * Handle a login request to the application.
     *
     * @param  \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function validateToken(Request $request)
    {
        // return $token === 'token-ivr123'; //test
        
        $responseData = collect();
        
        $CODE_PIN = '463432563464';
        $responseData->put("CodePin", $CODE_PIN);
        
        $token = base64_decode($request->token);
        $responseData->put("Token", $token);
        
        if(str_contains($token, $CODE_PIN) ){
            
            $time = str_after($token, $CODE_PIN);
            $responseData->put("RetrieveTime", $time);

            $dateCheck =Carbon::createFromTimestamp($time);
            $responseData->put("ConvertTimeToDate", $dateCheck);

            $diffSec = Carbon::now()->diffInSeconds($dateCheck);
            $responseData->put("DurationCheckPerSecond", $diffSec);

            $DURATION_SECOND_ALLOW = 5;
            $responseData->put("DurationAllowPerSecond", $DURATION_SECOND_ALLOW);
            if($diffSec <= $DURATION_SECOND_ALLOW){
                $responseData->put("StatusToken", "Valid");
                $responseData->put("StatusTokenDesc", "Successfully");
            }else{
                $responseData->put("StatusToken", "Invalid");
                $responseData->put("StatusTokenDesc", "Duration more than ".$DURATION_SECOND_ALLOW." seconds");
            }
        }

        return $responseData;
    }
    
    /*
     * Purposely for test. Must off when in production
     */
    public function getToken($mockErr = false) {   
        $CODE_PIN = '463432563464';
        
        if($mockErr){
           $CODE_PIN = '24test'; 
        }
        
        $date = Carbon::now();
        $timeLong = $date->getTimestamp();
        
        $token = $CODE_PIN.$timeLong;
        
        $tokenBase64 = base64_encode($token);

        return array(
            'Code' => $CODE_PIN,
            'Date' => $date,
            'DateLong' => $timeLong,
            'FormulaToken' => "APPEND Code+DateLong",
            'TokenBeforeBase64' => $token,
            'Base64Token' => $tokenBase64,
        );
    }
    
    /*
     * Purposely for test. Must off when in production
     */
    public function testToken(Request $request) {   
        $delay = $request->delay;
        $error = $request->error;
        
        if($delay == null){
            $delay = 0;
        }
        
        if($error){
            $dataToken = $this->getToken(true);
        }else{
            $dataToken = $this->getToken();
        }
        
        $responseData = collect();

        $CODE_PIN = '463432563464';
        $responseData->put("CodePin", $CODE_PIN);
        sleep($delay);
        
        $token = base64_decode($dataToken["Base64Token"]);
        $responseData->put("Token", $token);
        
        if(str_contains($token, $CODE_PIN) ){
            
            $time = str_after($token, $CODE_PIN);
            $responseData->put("RetrieveTime", $time);

            $dateCheck =Carbon::createFromTimestamp($time);
            $responseData->put("ConvertTimeToDate", $dateCheck);

            $diffSec = Carbon::now()->diffInSeconds($dateCheck);
            $responseData->put("DurationCheckPerSecond", $diffSec);

            $DURATION_SECOND_ALLOW = 5;
            $responseData->put("DurationAllowPerSecond", $DURATION_SECOND_ALLOW);
            if($diffSec <= 5){
                $responseData->put("StatusToken", "Valid");
                $responseData->put("StatusTokenDesc", "Successfully");
            }else{
                $responseData->put("StatusToken", "Invalid");
                $responseData->put("StatusTokenDesc", "Duration more than ".$DURATION_SECOND_ALLOW." seconds");
            }
        }

        return $responseData;
    }

    /*
     * Save request log
     */
    public function insertLog($data, $request) {  

        $requestClient = ['ip' => $request->ip(), 'agent' => $request->header('User-Agent')];
        
        $ivrLog = new IvrLog;
        $ivrLog->request_type = $data['request_type'];
        $ivrLog->request_data = json_encode($data['request_data']);
        $ivrLog->response = json_encode($data['response']);
        $ivrLog->request_client = json_encode($requestClient);
        $ivrLog->status = $data['status'];

        $ivrLog->save();
    }
}
