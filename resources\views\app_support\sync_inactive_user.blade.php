@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> List of Users Inactive SSO but Not Fully Sync With eP App</strong></h1>
        </div>
        <span class="alert alert-success" id="status_succ" style="display: none; color: rgb(6, 231, 81);"><b><i
                    class="fa fa-check-circle"></i>&nbsp;<span id="msg_succ"></span></b></span>
        <span class="alert alert-danger" id="status_fail" style="display: none; color: red;"><b><i
                    class="fa fa-times-circle"></i>&nbsp;Oops! <span id="msg_fail"></span></b></span>
        <div class="table-responsive">
            <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">User ID</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Login ID</th>
                        <th class="text-center">Identification No</th>
                        <th class="text-center">Org Type ID</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if (isset($listdata))
                        @foreach ($listdata as $user)
                            <tr>
                                <td class="text-center">{{ $user->user_id }}</td>
                                <td class="text-left">{{ $user->user_name }}</td>
                                <td class="text-center"><a
                                        href="{{ url('find/userlogin') }}/?login_id={{ $user->login_id }}"
                                        target="_blank">{{ $user->login_id }}</a></td>
                                <td class="text-center">{{ $user->identification_no }}</td>
                                <td class="text-center">{{ $user->org_type_id }}</td>
                                <td style="text-align: center;">
                                    <div class="btn-group">
                                        <div class='btn-group btn-group-xs' style="cursor: pointer;"><a
                                                data-loginid="{{ $user->login_id }}" data-userid="{{ $user->user_id }}" 
                                                data-user_org_id="{{ $user->user_org_id }}"
                                                data-user_name="{{ $user->user_name }}" id="validate_user"
                                                title="Validate Inactive User"><i
                                                    class="fa fa-check-square-o fa-lg"></i></a>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </DIV>
    </div>
    <div class="modal fade" id="user-status" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header text-left">
                    <h2 class="modal-title" style="color: #0fc74d;"><i class="fa fa-info-circle"></i> Validation Result
                    </h2>
                </div>
                <div class="modal-body">
                    <span id="user-msg"></span>
                </div>
                <form>
                    {{ csrf_field() }}
                    <input name="userid" id="userid" type="hidden" value="" />
                    <input name="uname" id="uname" type="hidden" value="" />
                    <input name="user_org_id" id="user_org_id" type="hidden" value="" />
                    <div class="modal-footer col-md-12" style="display: inline-block;">
                        <div class="row">
                            <div class="col-md-8"></div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-primary"
                                    onclick="deactivate_user()">Deactivate</button>
                            </div>
                            <div class="col-md-2">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!};

        function showHideAlertMessage(id, element, msg) {
            $('#' + id).css('display', 'block');
            document.getElementById(element).innerHTML = msg;
            setTimeout(function() {
                $('#' + id).css('display', 'none');
            }, 5000);
        }

        function deactivate_user() {
            let csrf = $("input[name=_token]").val();
            let userid = $('#userid').val();
            let user_org_id = $('#user_org_id').val();
            let name = $('#uname').val();
            $('#user-status').modal('hide');

            $.ajax({
                url: APP_URL + '/app-support/pm/user/deactivate',
                type: "POST",
                data: {
                    "_token": csrf,
                    userid,
                    user_org_id,
                    name
                },
                success: function(response) {
                    if (response.code === 0) {
                        showHideAlertMessage('status_succ', 'msg_succ', response.message);
                    } else {
                        showHideAlertMessage('status_fail', 'msg_fail', response.message);
                    }
                },
                error: function(error) {
                    showHideAlertMessage('status_fail', 'msg_fail', error);
                }
            });
        }

        $(function() {
            $('#supplier-datatable').dataTable({
                destroy: true,
                order: [
                    [1, "asc"]
                ]
            });

            $('#supplier-datatable tbody').on('click', 'td a#validate_user', function() {
                let loginid = $(this).attr('data-loginid');
                let userid = $(this).attr('data-userid');
                let user_org_id = $(this).attr('data-user_org_id');
                let name = $(this).attr('data-user_name');

                $.ajax({
                    url: APP_URL + '/app-support/pm/user/validate',
                    type: "GET",
                    data: {
                        loginid,
                        'status': 'I'
                    },
                    success: function(response) {
                        if (response.code === 0) {
                            let is_inactive = response.message;
                            if (is_inactive) {
                                document.getElementById('user-msg').innerHTML =
                                    `Both SSO and Liferay Status Are Inactive. <br/>Proceed To Deactivate User ${userid} - ${name}`;
                                $('#userid').val(userid);
                                $('#uname').val(name);
                                $('#user_org_id').val(user_org_id);
                                $('.btn-primary').css('display', 'block');

                            } else {
                                document.getElementById('user-msg').innerHTML =
                                    `User ${userid} - ${name} Is Active. <br/>No Action Required.`;
                                $('.btn-primary').css('display', 'none');
                            }
                        } else {
                            document.getElementById('user-msg').innerHTML =
                                `${userid} - ${name} <br/> Error: ${response.message}`;
                            $('.btn-primary').css('display', 'none');
                        }

                        $('#user-status').modal('show');
                    },
                    error: function(error) {
                        document.getElementById('user-msg').innerHTML =
                            `${userid} - ${name} <br/> Validation Error, ${error}`;
                        $('.btn-primary').css('display', 'none');
                        $('#user-status').modal('show');
                    }
                });
            });
        });
    </script>
@endsection
