@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Status PO/CO Pending Payment Instruction QUERY Verification or Pending Payment Instruction QUERY FROM 1GFMAS <br>
                <small>Query check created date latest status  more than 2 weeks </small>
            </h1>
        </div>
    </div>

    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Pending PR/CR Review from IGFMAS  </strong>
                        <small></small> 
                    </h1>
                </div>
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                {{ csrf_field() }}
                <div class="table-responsive">
                    <table id="stuck-bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">AGO</th>
                            <th class="text-center">DOC NO</th>
                            <th class="text-center">PA DOC NO</th>
                            <th class="text-center">PA STATUS ID</th>
                            <th class="text-center">PA STATUS</th>
                            <th class="text-center">IS CURRENT</th>
                            <th class="text-center">OSB LINK</th>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">AGEING (Days)</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                        <?php
                        $to = \Carbon\Carbon::now();
                        $from = \Carbon\Carbon::parse($data->status_created_date);
                        $diffInDays = $to->diffInDays($from);
                        ?>
                            <tr>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center">{{ $data->ag_office_name }}</td>
                                <td class="text-center">
                                    <a href="{{url('/find/trans/track/docno')}}/{{ $data->doc_no }}" target="_blank" title="Find Tracking Diary"> {{ $data->doc_no }}</a> 
                                    
                                </td>
                                <td class="text-center">
                                    <a href="{{url('/find/trans/track/docno')}}/{{ $data->payment_advice_no }}" target="_blank" title="Find Tracking Diary"> {{ $data->payment_advice_no }}</a> 
                                    
                                </td>
                                <td class="text-center">{{ $data->status_id }}</a></td>
                                <td class="text-center">{{ $data->status_name }}</a></td>
                                <td class="text-center">{{ $data->is_current }}</a></td>
                                <td class="text-center">
                                <a href="{{url('/find/osb/log')}}/{{ $data->doc_no }}" target="_blank" title="Find OSB Log"> Check OSB </a>
                                </td>
                                <td class="text-center">{{ $data->status_created_date }}</td>
                                <td class="text-center">{{ $diffInDays }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    App.datatables();
    
    $('#stuck-bpm-datatable').dataTable({
                order: [5, "asc"],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
</script>
@endsection



