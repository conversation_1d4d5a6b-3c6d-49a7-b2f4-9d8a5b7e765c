<?php

namespace App\Http\Controllers\ItSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\ItSupport\ItSupportService;
use Illuminate\Http\Request;
use Carbon\Carbon;
use DateTime;
use DB;
use Auth;

class ItChecklistEpController extends Controller
{

    use ItSupportService;

    public function data_lookup()
    {
        $listdata = $this->listAllDataLook();
        return view('it_support.data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function action_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $name = $request->name;
        $group = $request->group;
        $sub_group = $request->sub_group;
        $status = $request->status;
        $seq = $request->sequence;
        $total_h = $request->total_hour;

        if ($request->id != null) {
            $listdata = $this->listDataLook($request->id);

            if ($request->name != "") {
                if (!$request->name) {
                    $oriname = $listdata->esc_name;
                } else {
                    $oriname = $request->name;
                }
            } else {
                $oriname = null;
            }

            if ($request->group != "") {
                if (!$request->group) {
                    $ori_group = $listdata->esc_group;
                } else {
                    $ori_group = $request->group;
                }
            } else {
                $ori_group = null;
            }

            if ($request->sub_group != "") {
                if (!$request->sub_group) {
                    $ori_sub = $listdata->esc_group_sub;
                } else {
                    $ori_sub = $request->sub_group;
                }
            } else {
                $ori_sub = null;
            }

            if ($request->status != "") {
                if (!$request->status) {
                    $ori_status = $listdata->esc_status;
                } else {
                    $ori_status = $request->status;
                }
            } else {
                $ori_status = null;
            }

            if ($request->sequence != "") {
                if (!$request->sequence) {
                    $orisequence = $listdata->esc_seq;
                } else {
                    $orisequence = $request->sequence;
                }
            } else {
                $orisequence = null;
            }

            if ($request->total_hour != "") {
                if (!$request->total_hour) {
                    $oritotal_h = $listdata->total_hour_checking;
                } else {
                    $oritotal_h = $request->total_hour;
                }
            } else {
                $oritotal_h = null;
            }

            if ($request->isMethod("POST")) {
                $updateData = [
                    'esc_name' => $oriname,
                    'esc_group' => $ori_group,
                    'esc_group_sub' => $ori_sub,
                    'esc_status' => $ori_status,
                    'esc_seq' => $orisequence,
                    'total_hour_checking' => $oritotal_h,
                    'esc_changed_by' => $user,
                    'esc_changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_it_support')->table('ep_status_checklist')
                    ->where('esc_id', $request->id)
                    ->update($updateData);
            }
            return back();
        }

        if ($request->id == null) {
            DB::connection('mysql_ep_it_support')->table('ep_status_checklist')->insert([
                ['esc_name' => $name, 'esc_group' => $group, 'esc_group_sub' => $sub_group, 'esc_status' => $status, 'esc_seq' => $seq, 'total_hour_checking' => $total_h, 'esc_created_by' => $user, 'esc_created_date' => carbon::now()],
            ]);
            return back();
        }

        return view('it_support.data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function delete_data_lookup(Request $request)
    {
        if ($request->id != null) {
            DB::connection('mysql_ep_it_support')->table('ep_status_checklist')
                ->where('esc_id', $request->id)
                ->delete();
            return back();
        }
    }

    public function statusChecklist()
    {
        $user = auth()->user()->first_name;
        $menu = $this->getMenu();
        $hour = (intval(Carbon::now()->format('H')));
        $currentDate = Carbon::now();
        $dateNote = $currentDate->format("Y-m-d");
        $listNote = $this->getListNotes($dateNote, 'status');
        $day = date('l');
        if ($hour >= 8 && $hour < 19) {
            $shift = 'M';
        } else {
            $shift = 'E';
        }

        return view('it_support.status_checklist', [
            'menu' => $menu,
            'shift' => $shift,
            'date' => $dateNote,
            'day' => $day,
            'listing' => null,
            'location' => null,
            'listNote' => $listNote,
            'user' => $user,
            'dateNote' => $dateNote,
        ]);
    }

    public function updateChecklist(Request $request)
    {
        $user = auth()->user()->first_name;
        $checkLatestEp = $this->getLatestTestEp();
        $currentDate = Carbon::now();
        $date = $currentDate->format("m/d/Y");
        $yesterdayDate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        if ($request->input('id') != null) {
            foreach ($request->input('id') as $list) {
                $check_type = $this->listTaskJob($list);
                $group_task = $this->getGroupTask($list);
                if (is_object($check_type) && isset($check_type->esc_id)) {
                    $getHeaderGroup = $this->listDataLook($check_type->esc_id);
                } else {
                    $getHeaderGroup = null;
                }
                $hour = intval(Carbon::now()->format('H'));

                // Time Conversion
                if (is_object($check_type)) {
                    $newTime = ($check_type->time_format == 'PM')
                        ? ($check_type->task_time == 12 ? 12 : $check_type->task_time + 12)
                        : ($check_type->task_time == 12 ? 0 : $check_type->task_time);
                } else {
                    $newTime = null;
                }

                // Default checkType
                $checkType = ($yesterdayDate != $date) ? 'L' : 'T';

                // Group Task Condition Checks
                if ($request->date_search != null && $request->date_search != $date) {
                    $checkType = 'L';
                } else if ($newTime < $hour) {
                    $checkType = 'L';
                } else if ($newTime == $hour) {
                    $checkType = 'T';
                }
                if (!empty($group_task) && isset($group_task[0]->esc_group)) {
                    if (in_array($group_task[0]->esc_group, ['Batch Check', 'SCT Status', 'Backup Status', 'CCTV Review'])) {
                        $checkType = ($request->date_search != null && $request->date_search != $date)
                            ? 'L'
                            : (($newTime == $hour || $newTime + 1 == $hour) ? 'T' : 'L');
                    } elseif ($group_task[0]->esc_group == 'DRC') {
                        $checkType = ($request->date_search != null && $request->date_search != $date)
                            ? 'L'
                            : ($hour < 21 ? 'T' : 'L');
                    }
                } elseif (!empty($group_task) && isset($group_task[0]->esc_group) && $newTime < $hour || ($request->date_search != null && $request->date_search != $date)) {
                    $checkType = 'L';
                } else {
                    $checkType = 'L';
                }

                // Header Group Login Check
                if (is_object($getHeaderGroup) && isset($getHeaderGroup->esc_group)) {
                    $loginId = ($getHeaderGroup->esc_group == 'Login test') ? $checkLatestEp[0]->login_id : null;
                    $loginDate = ($getHeaderGroup->esc_group == 'Login test') ? $checkLatestEp[0]->login_date : null;
                } else {
                    $loginId = null;
                    $loginDate = null;
                }

                // Update Logic
                if ($request->isMethod("POST")) {
                    DB::connection('mysql_ep_it_support')->table('esc_job_task')
                        ->where('task_id', $list)
                        ->update([
                            'check_type' => $checkType,
                            'task_done' => 'done',
                            'task_login_id' => $loginId,
                            'task_last_login_date' => $loginDate,
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now(),
                            'task_remarks' => $request->remarks,
                            'incident_detail' => $request->incident_detail,
                            'incident_number' => $request->incident_number,
                            'crm_log_no' => $request->crm_log_no
                        ]);
                }
            }
        } else if ($request->input('teststatus') != null) {
            $editRemarks = $this->getRemarksDetails($request->teststatus);
            if ($request->remarks_sct != "") {
                if (!$request->remarks_sct) {
                    $oriremarks = $editRemarks->task_remarks;
                } else
                    $oriremarks = $request->remarks_sct;
            } else {
                $oriremarks = "";
            }
            if ($request->isMethod("POST")) {
                $updateData = [
                    'task_remarks' => $oriremarks
                ];
                DB::connection('mysql_ep_it_support')->table('esc_job_task')
                    ->where('task_id', $request->teststatus)
                    ->update($updateData);
            }
        } else if ($request->input('cctv_name') != null) {
            $editRemarks = $this->getRemarksDetails($request->cctv_name);
            if ($request->incident_detail != "") {
                if (!$request->incident_detail) {
                    $oriincident_detail = $editRemarks->incident_detail;
                } else
                    $oriincident_detail = $request->incident_detail;
            } else {
                $oriincident_detail = "";
            }

            if ($request->incident_number != "") {
                if (!$request->incident_number) {
                    $oriincident_number = $editRemarks->incident_number;
                } else
                    $oriincident_number = $request->incident_number;
            } else {
                $oriincident_number = "";
            }

            if ($request->crm_log_no != "") {
                if (!$request->crm_log_no) {
                    $oricrm_log_no = $editRemarks->crm_log_no;
                } else
                    $oricrm_log_no = $request->crm_log_no;
            } else {
                $oricrm_log_no = "";
            }

            if ($request->isMethod("POST")) {
                $updateData = [
                    'incident_detail' => $oriincident_detail,
                    'incident_number' => $oriincident_number,
                    'crm_log_no' => $oricrm_log_no
                ];
                DB::connection('mysql_ep_it_support')->table('esc_job_task')
                    ->where('task_id', $request->cctv_name)
                    ->update($updateData);
            }
        }
        return back();
    }

    public function remarksStatus($group, $data, $shift_interval)
    {
        $currentDate = Carbon::now();
        if ($data != 'null') {
            $date = $data;
        } else {
            $date = $currentDate->format("Y-m-d");
        }
        $helangStatus = null;
        $helangStatus = $this->getDocStatus($date, $group, $shift_interval);
        return response()->json($helangStatus, 200);
    }

    public function remarksStatusDetails($id)
    {
        $editRemarks = $this->getRemarksDetails($id);
        return response()->json($editRemarks, 200);
    }

    public function searchByGroupChecklist($group, $data, $shift_interval)
    {
        $currentDate = Carbon::now();
        if ($data != 'null') {
            $date = $data;
        } else {
            $date = $currentDate->format("Y-m-d");
        }

        $hour = $currentDate->format("H");
        $dateCurrent = $currentDate->format("Y-m-d");
        $yesterdayDate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        $tomorrowDate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
        // $helangStatus = null;
        // $helangStatus = $this->getDocStatus($date, $group, $shift_interval);
        $getHourTaskInterval = $this->getHour($date, $shift_interval);
        $listLoginTestGroupSub = $this->getGroupSubLoginTest($group);
        $html = '';
        if ($getHourTaskInterval != null) {
            foreach ($getHourTaskInterval as $basedOnHourInterval) {
                if ($shift_interval == 'M') {
                    $listingHeaderHour = $this->getChecklistHourHeader($group, $date, $shift_interval, $basedOnHourInterval->time_interval);
                }

                if ($shift_interval == 'E') {
                    $listingHeaderHour = null;
                    if ($dateCurrent == $date && $hour >= '08' && $hour <= '23') {
                        $listingHeaderHour = $this->getChecklistHourHeaderAfter8PMandBefore12AM($group, $date, $shift_interval, $basedOnHourInterval->time_interval);
                    }
                    if ($dateCurrent == $date && $hour >= '00' && $hour <= '07') {
                        $listingHeaderHour = $this->getChecklistHourHeaderAfter12AMandBefore8AM($group, $yesterdayDate, $basedOnHourInterval->time_interval, $group, $date, $basedOnHourInterval->time_interval);
                    } else {
                        $listingHeaderHour = $this->getChecklistHourHeaderAfter12AMandBefore8AM($group, $date, $basedOnHourInterval->time_interval, $group, $tomorrowDate, $basedOnHourInterval->time_interval);
                    }
                }
                if ($listingHeaderHour != null) {
                    $html .= "<table id='list_datatable' class='cell-border hover'>
                    <thead>
                    <tr>
                    <th style='width: 30px;' class='text-center'><input type='checkbox' id='selectall' onchange='selectAll(this)'></th>
                    <th style='width: 30px;' class='text-center'>No.</th>
                    <th class='text-center'>Name</th>";
                    foreach ($listingHeaderHour as $list) {
                        $html .= "<th class='text-center'>$list->task_date $list->task_time $list->time_format</th>";
                    }
                    $html .= "</tr>
                    </thead>";
                    $html = $html . "<tbody>";

                    if (!empty($listLoginTestGroupSub[0]->esc_group_sub)) {
                        foreach ($listLoginTestGroupSub as $listGroupSub) {
                            $countStep = ($shift_interval == 'M') ? 13 : null;
                            $listingNaming = ($shift_interval == 'M')
                                ? $this->getChecklist($group, $date, $shift_interval, $basedOnHourInterval->time_interval)
                                : null;
                            if ($shift_interval == 'E') {
                                if ($dateCurrent == $date) {
                                    if ($hour >= '08' && $hour <= '23') {
                                        $listingNaming = $this->getChecklistAfter8PMandBefore12AM($group, $date, $shift_interval, $basedOnHourInterval->time_interval);
                                        $countStep = 5;
                                    } elseif ($hour >= '00' && $hour <= '07') {
                                        $listingNaming = $this->getChecklistAfter12AMandBefore8AM($group, $yesterdayDate, $basedOnHourInterval->time_interval, $group, $date, $basedOnHourInterval->time_interval);
                                        $countStep = 13;
                                    }
                                } else {
                                    $listingNaming = $this->getChecklistAfter12AMandBefore8AM($group, $date, $basedOnHourInterval->time_interval, $group, $tomorrowDate, $basedOnHourInterval->time_interval);
                                    $countStep = 13;
                                }
                            }

                            // Group Header Switch
                            switch ($listGroupSub->esc_group_sub) {
                                case 'Streamyx':
                                    $groupHeader = 'A';
                                    break;
                                case 'eGnet':
                                    $groupHeader = 'B';
                                    break;
                                case 'P':
                                case 'W':
                                    $groupHeader = 'Interface Name (Nagios)';
                                    break;
                                default:
                                    $groupHeader = '';
                            }

                            // HTML Generation
                            if ($groupHeader) {
                                if ($groupHeader == 'Interface Name (Nagios)') {
                                    $html .= "<th colspan='3' class='text-center'>$groupHeader</th>";
                                } else {
                                    $html .= "<th colspan='1' class='text-center'>$groupHeader</th>";
                                }

                                $html .= "<th colspan='14' class='text-center'>$listGroupSub->esc_group_sub</th>";
                                for ($i = 0; $i < $countStep; $i++) {
                                    $html .= "<th style='display:none'></th>";
                                }

                                foreach ($listingNaming as $value) {
                                    if ($value->esc_group_sub == $listGroupSub->esc_group_sub) {
                                        $html .= self::returnTableCheckBox($date, $shift_interval, $value, $html, $listingHeaderHour, $currentDate);
                                    }
                                }
                            }
                            $html .= "</tr><br />";
                        }
                    } else {
                        $listingNaming = $this->getChecklist($group, $date, $shift_interval, $basedOnHourInterval->time_interval);
                        foreach ($listingNaming as $value) {
                            $html .= self::returnTableCheckBox($date, $shift_interval, $value, $html, $listingHeaderHour, $currentDate);
                        }
                    }
                    $html = $html . "</tbody></table>";
                }
            }
        } else {
            $html .= "Please Choose A Correct Date";
        }
        return $html;
    }

    private function returnTableCheckBox($date, $shift_interval, $value, $html, $listingHeaderHour, $currentDate)
    {
        $html = self::headerTable($value, $html);
        $yesterdayDate = date('Y-m-d', strtotime('-1 day', strtotime($date)));
        $semalam = date('Y-m-d', strtotime("-1 days"));
        $esokDate = date('Y-m-d', strtotime('+1 day', strtotime($date)));
        $hour = $currentDate->format("H");
        $sekarangDate = $currentDate->format('Y-m-d');
        foreach ($listingHeaderHour as $listing) {

            if ($shift_interval == 'M') {
                $getTaskId = $this->getTaskId($date, $shift_interval, $listing->task_time, $value->esc_id);
            }

            if ($shift_interval == 'E') {
                $getTaskId = null;

                if ($date == $sekarangDate && $hour >= '08' && $hour < '24') {
                    $getTaskId = $this->getTaskId($date, $shift_interval, $listing->task_time, $value->esc_id);
                }
                if ($date == $sekarangDate && $hour >= '00' && $hour < '08') {
                    $getTaskId = $this->getTaskIdForYesterday($yesterdayDate, $listing->task_time, $value->esc_id, $date, $listing->task_time, $value->esc_id);
                }
                if ($date == $semalam && $hour >= '00' && $hour < '08') {
                    $getTaskId = $this->getTaskIdForYesterday($semalam, $listing->task_time, $value->esc_id, $date, $listing->task_time, $value->esc_id);
                }
                if ($date != $sekarangDate) {
                    $getTaskId = $this->getTaskIdForYesterday($date, $listing->task_time, $value->esc_id, $esokDate, $listing->task_time, $value->esc_id);
                }
            }


            // $getId = $getTaskId[0]->task_id;
            if (!empty($getTaskId) && isset($getTaskId[0]->task_id)) {
                $getId = $getTaskId[0]->task_id;
            } else {
                $getId = null;
            }
            $getChecked = $this->getCheckedList($getId);
            $dateCurrent = $currentDate->format("Y-m-d");
            $timebeforecurrent = $currentDate->format("H");
            if ($listing->time_format == 'PM' && $listing->task_time == 12) {
                $newTime = 12;
            } else if ($listing->time_format == 'PM') {
                $newTime = $listing->task_time + 12;
            }
            if ($listing->time_format == 'AM' && $listing->task_time == 12) {
                $newTime = 00;
            } else if ($listing->time_format == 'AM') {
                $newTime = $listing->task_time;
            }
            $html .= self::checkBoxFunction($dateCurrent, $date, $html, $getId, $newTime, $timebeforecurrent, $getChecked, $semalam);
        }
        return $html;
    }

    private function headerTable($value, $html)
    {
        $html = "
                    <tr>
                    <td style='width: 30px;' class='text-center'></td>
                    <td style='width: 30px;' class='text-center'>$value->esc_seq</td>
                    <td class='text-center'>$value->task_name</td>";

        return $html;
    }

    private function checkBoxFunction($dateCurrent, $date, $html, $getId, $newTime, $timebeforecurrent, $getChecked, $semalam)
    {
        $findDate = $this->listTaskJob($getId);

        if (is_object($findDate) && isset($findDate->task_date) && $findDate->task_date != $date) {
            $html = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId'></td>";
        } else {
            $html = "<td style='width: 30px;' class='text-center'>No task date available</td>";
        }
        if ($dateCurrent == $date && $findDate->task_date == $date) {
            $html = self::timeDiasbleAndGotChecked($html, $getId, $newTime, $timebeforecurrent, $getChecked);
        } else {
            $html = self::iSGetChecked($html, $getId, $getChecked);
        }
        return $html;
    }

    private function timeDiasbleAndGotChecked($html, $getId, $newTime, $timebeforecurrent, $getChecked)
    {
        if ($newTime > $timebeforecurrent) {
            $html = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId' disabled></td>";
        } else {
            $html = self::iSGetChecked($html, $getId, $getChecked);
        }

        return $html;
    }

    //    checking
    private function iSGetChecked($html, $getId, $getChecked)
    {
        if ($getChecked != null) {
            if ($getChecked[0]->check_type === 'L') {
                $html = self::checkBoxIfLate($html, $getId, $getChecked);
            }
            if ($getChecked[0]->check_type === 'T') {
                $html = self::checkBoxIfOnTime($html, $getId, $getChecked);
            }
            if ($getChecked[0]->esc_group == 'Login test' && $getChecked[0]->check_type === 'L') {
                $html = self::checkBoxIfLateLoginTest($html, $getId, $getChecked);
            }
            if ($getChecked[0]->esc_group == 'Login test' && $getChecked[0]->check_type === 'T') {
                $html = self::checkBoxIfOnTimeLoginTest($html, $getId, $getChecked);
            }
        } else {
            $html = self::checkBoxIfNoAction($html, $getId);
        }

        return $html;
    }

    private function checkBoxIfLate($html, $getId, $getChecked)
    {
        $html = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId' checked='checked' disabled> "
            . "<a id='trigger_view_details' data-toggle='tooltip' title='Changed By : {$getChecked[0]->task_changed_by} \nChanged Date : {$getChecked[0]->task_changed_date}'><i class='fa fa-exclamation'></i></a></td>";

        return $html;
    }

    private function checkBoxIfLateLoginTest($html, $getId, $getChecked)
    {
        $html = "<td style='width: 30px;' data-toggle='tooltip' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId' checked='checked' disabled> "
            . "<a id='trigger_view_details' title='Changed By : {$getChecked[0]->task_changed_by} \nChanged Date : {$getChecked[0]->task_changed_date} \nLogin Id : {$getChecked[0]->task_login_id} \nLogin Date : {$getChecked[0]->task_last_login_date}'><i class='fa fa-exclamation'></i></a></td>";

        return $html;
    }

    private function checkBoxIfOnTime($html, $getId, $getChecked)
    {
        $html = "<td style='width: 30px;' data-toggle='tooltip' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId' checked='checked' disabled> "
            . "<a id='trigger_view_details' title='Changed By : {$getChecked[0]->task_changed_by} \nChanged Date : {$getChecked[0]->task_changed_date}'><i class='fa fa-location-arrow' style='color : blue'></i></a></td>";

        return $html;
    }

    private function checkBoxIfOnTimeLoginTest($html, $getId, $getChecked)
    {
        $html = "<td style='width: 30px;' data-toggle='tooltip' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId' checked='checked' disabled> "
            . "<a id='trigger_view_details' title='Changed By : {$getChecked[0]->task_changed_by} \nChanged Date : {$getChecked[0]->task_changed_date} \nLogin Id : {$getChecked[0]->task_login_id} \nLogin Date : {$getChecked[0]->task_last_login_date}'><i class='fa fa-location-arrow' style='color : blue'></i></a></td>";

        return $html;
    }

    private function checkBoxIfNoAction($html, $getId)
    {
        $html = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='id[$getId]' name='id[$getId]' value='$getId'></td>";

        return $html;
    }

    public function statusChecklistHistory()
    {
        $year = $this->getServerYear();
        return view('it_support.server_checklist_searching_history', [
            'year' => $year,
            'section' => 'status_check',
        ]);
    }

    public function summaryDetails()
    {
        $listData = $this->listSummaryJobDetails();
        return view('it_support.summary_details', [
            'listData' => $listData
        ]);
    }

    public function actionSummaryDetails(Request $request)
    {
        $user = auth()->user()->first_name;
        $jobDate = $request->date_created_task;
        $shift = $request->shift;
        $remarks = $request->remarks;
        $respond = $request->respond;
        // $currentDate = Carbon::now();
        $listdata = null;
        if ($request->id != null) {
            $listdata = $this->getSummaryJobDetails($request->id);

            if ($request->date_created_task != "") {
                if (!$request->date_created_task) {
                    $oridate = $listdata->job_date;
                } else
                    $oridate = $request->date_created_task;
            } else {
                $oridate = null;
            }

            if ($request->shift != "") {
                if (!$request->shift) {
                    $ori_shift = $listdata->job_shift;
                } else
                    $ori_shift = $request->shift;
            } else {
                $ori_shift = null;
            }

            if ($request->remarks != "") {
                if (!$request->remarks) {
                    $ori_remarks = $listdata->job_remarks;
                } else
                    $ori_remarks = $request->remarks;
            } else {
                $ori_remarks = null;
            }

            if ($request->respond != "") {
                if (!$request->respond) {
                    $ori_respond = $listdata->job_respond;
                } else
                    $ori_respond = $request->respond;
            } else {
                $ori_respond = null;
            }

            if ($request->isMethod("POST")) {
                $updateData = [
                    'job_date' => $oridate,
                    'job_shift' => $ori_shift,
                    'job_remarks' => $ori_remarks,
                    'job_respond' => $ori_respond,
                    'job_changed_by' => $user,
                    'job_changed_date' => Carbon::now(),
                    'job_respond_by' => $user,
                    'job_respond_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_it_support')->table('esc_job_day_summary')
                    ->where('job_day_id', $request->id)
                    ->update($updateData);
            }
            return back();
        }

        if ($request->id == null) {
            $result = DB::connection('mysql_ep_it_support')->table('esc_job_day_summary')->insert([
                ['job_date' => $jobDate, 'job_shift' => $shift, 'job_remarks' => $remarks, 'job_respond' => $respond, 'job_created_date' => Carbon::now(), 'job_created_by' => $user, 'job_respond_date' => Carbon::now(), 'job_respond_by' => $user],
            ]);
            return back();
        }
        return view('it_support.summary_details', [
            'listdata' => $listdata,
        ]);
    }

    public function statusServerChecklist()
    {
        $menu = $this->getMenuServer();
        $menuHelang = $this->getMenuServerHelang();
        $menuKvdc = $this->getMenuServerKvdcActive();
        $hour = (intval(Carbon::now()->format('H')));
        $currentDate = Carbon::now();
        $date = $currentDate->format("d/m/Y");
        $dateNote = $currentDate->format("Y-m-d");
        $listNote = $this->getListNotes($dateNote, 'server');
        $user = auth()->user()->first_name;
        $day = date('l');
        if ($hour >= 8 && $hour < 19) {
            $shift = 'M';
        } else {
            $shift = 'E';
        }
        return view('it_support.server_checklist', [
            'menu' => $menu,
            'menuHelang' => $menuHelang,
            'menuKvdc' => $menuKvdc,
            'shift' => $shift,
            'dateNote' => $date,
            'day' => $day,
            'listing' => null,
            'listNote' => $listNote,
            'user' => $user,
            'dateNote' => $dateNote,
            'location' => null,
        ]);
    }

    public function server_data_lookUp()
    {
        $listdata = $this->listAllServerDataLookup();
        return view('it_support.server_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function server_action_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $name = $request->name;
        $group = $request->group;
        $subGroup = $request->subgroup;
        $location = $request->location;
        $status = $request->status;
        $seq = $request->sequence;

        if ($request->id != null) {
            $listdata = $this->listServerDataLookUp($request->id);

            if ($request->name != "") {
                if (!$request->name) {
                    $oriname = $listdata->esc_name;
                } else {
                    $oriname = $request->name;
                }
            } else {
                $oriname = null;
            }

            if ($request->group != "") {
                if (!$request->group) {
                    $ori_group = $listdata->esc_group;
                } else {
                    $ori_group = $request->group;
                }
            } else {
                $ori_group = null;
            }

            if ($request->subgroup != "") {
                if (!$request->subgroup) {
                    $ori_sub_group = $listdata->esc_sub_group;
                } else {
                    $ori_sub_group = $request->subgroup;
                }
            } else {
                $ori_sub_group = null;
            }

            if ($request->location != "") {
                if (!$request->location) {
                    $ori_location = $listdata->esc_location;
                } else {
                    $ori_location = $request->location;
                }
            } else {
                $ori_location = null;
            }

            if ($request->status != "") {
                if (!$request->status) {
                    $ori_status = $listdata->esc_status;
                } else {
                    $ori_status = $request->status;
                }
            } else {
                $ori_status = null;
            }

            if ($request->sequence != "") {
                if (!$request->sequence) {
                    $orisequence = $listdata->esc_seq;
                } else {
                    $orisequence = $request->sequence;
                }
            } else {
                $orisequence = null;
            }

            if ($request->isMethod("POST")) {
                $updateData = [
                    'esc_name' => $oriname,
                    'esc_group' => $ori_group,
                    'esc_sub_group' => $ori_sub_group,
                    'esc_location' => $ori_location,
                    'esc_status' => $ori_status,
                    'esc_seq' => $orisequence,
                    'esc_changed_by' => $user,
                    'esc_changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_it_support')->table('ep_server_checklist')
                    ->where('esc_id', $request->id)
                    ->update($updateData);
            }
            return back();
        }

        if ($request->id == null) {
            DB::connection('mysql_ep_it_support')->table('ep_server_checklist')->insert([
                ['esc_name' => $name, 'esc_group' => $group, 'esc_sub_group' => $subGroup, 'esc_location' => $location, 'esc_status' => $status, 'esc_seq' => $seq, 'esc_created_by' => $user, 'esc_created_date' => carbon::now()],
            ]);
            return back();
        }

        return view('it_support.server_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function searchByServerGroup($group, $data, $shift, $location)
    {
        $currentDate = Carbon::now();
        if ($data != 'null') {
            $date = $data;
        } else {
            $date = $currentDate->format("Y-m-d");
        }
        $listSearchByGroup = $this->listBySearchingGroup($group, $date, $shift, $location);
        $listSearchBySubGroup = $this->getGroupSubServer($group);
        $html = "<thead>
                            <tr>
                            <th class='text-center'>No</th>
                            <th class='text-center'>Server Name</th>
                            <th class='text-center'>Check <input type='checkbox' id='selectall' onchange='selectAll(this)'></th>
                            <th class='text-center'>if (Amber Light)</th>
                            <th class='text-center'>Remarks</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";

        if (!is_null($listSearchByGroup) && is_array($listSearchByGroup)) {
            if (!empty($listSearchBySubGroup)) {
                foreach ($listSearchBySubGroup as $listing) {
                    $variable = $listing->esc_sub_group;
                    $html = self::subGroup($listing, $listSearchByGroup, $group, $date, $shift, $html, $variable);
                }
            }
        }

        $html = $html . "</tbody></table>";
        return $html;
    }

    private function subGroup($listing, $listSearchByGroup, $group, $date, $shift, $html, $variable)
    {
        $count = 1;
        $countStep = 4;

        if (!empty($listSearchByGroup)) {
            if (!is_null($listing->esc_sub_group)) {
                for ($i = 0; $i < $count; $i++) {
                    $html .= "<th colspan='5' class='text-center'>$listing->esc_sub_group</th>";
                    for ($i = 0; $i < $countStep; $i++) {
                        $html .= "<th style='display:none'></th>";
                    }
                }
            }
        }
        if ($listSearchByGroup !== null) {
            foreach ($listSearchByGroup as $value) {
                $listingNaming = $this->getServerChecklist($group, $date, $value->esc_id, $shift);
                if (!empty($listingNaming) && (is_array($listingNaming) || is_object($listingNaming))) {
                    foreach ($listingNaming as $list) {
                        if ($list->esc_sub_group == $variable) {
                            $html .= self::returnColumn($date, $shift, $value, $html, $list);
                        }
                    }
                }
            }
        }
        return $html;
    }

    private function returnColumn($date, $shift, $value, $html, $list)
    {
        $html = self::fixColumnData($value, $html);

        // Check if $getTaskId is not empty
        $getTaskId = $this->getServerTaskId($date, $list->esc_id, $shift);
        if (!empty($getTaskId)) {
            $getId = $getTaskId[0]->task_id;

            // Perform actions only if $getId is not empty
            if (!empty($getId)) {
                $html = self::actionForChecking($getTaskId, $getId, $html, $date);
                $html = self::actionForAmberLight($getTaskId, $getId, $html, $date);
                $html = self::actionForRemarks($getTaskId, $getId, $html, $date);
            } else {
                // Handle case where $getId is empty
                // For example, set default values or display a message
            }
        } else {
            // Handle case where $getTaskId is empty
            // For example, set default values or display a message
        }

        return $html;
    }

    private function fixColumnData($value, $html)
    {
        $html = "<tr>
                    <td style='width: 30px;' class='text-center'>$value->esc_seq</td>
                    <td class='text-center'>$value->esc_name</td>";

        return $html;
    }

    private function actionForChecking($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'server');
        if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed') && $getTaskId[0]->checking != null) {
            $data = "<td style='width: 30px;' class='text-center checkBoxClass'><input type='checkbox' id='checkbox_for_check' name='check[$getId]' checked='checked' disabled></td>";
        } else if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed') && $getTaskId[0]->checking == null) {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox_for_check' name='check[$getId]' disabled></td>";
        } else if (isset($getNoted) && $getNoted[0]->ack_status != 'Pending Endorsement' && $getTaskId[0]->checking != null) {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox_for_check' name='check[$getId]' checked='checked' disabled></td>";
        } else if ($getNoted == null && $getTaskId[0]->checking != null) {
            $data = "<td style='width: 30px;' class='text-center checkBoxClass'><input type='checkbox' id='checkbox_for_check' name='check[$getId]' checked='checked'></td>";
        } else {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox_for_check' name='check[$getId]'></td>";
        }
        $html = $html . $data;
        return $html;
    }

    private function actionForAmberLight($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'server');
        if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed') && $getTaskId[0]->if_amber_light != null) {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox' name='amber[$getId]' checked='checked' disabled></td>";
        } else if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed') && $getTaskId[0]->if_amber_light == null) {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox' name='amber[$getId]' disabled></td>";
        } else if (isset($getNoted) && $getNoted[0]->ack_status != 'Pending Endorsement' && $getTaskId[0]->if_amber_light != null) {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox' name='amber[$getId]' checked='checked' disabled></td>";
        } else if ($getNoted == null && $getTaskId[0]->if_amber_light != null) {
            $data = "<td style='width: 30px;' class='text-center checkBoxClass'><input type='checkbox' id='checkbox' name='amber[$getId]' checked='checked'></td>";
        } else {
            $data = "<td style='width: 30px;' class='text-center'><input type='checkbox' id='checkbox' name='amber[$getId]'></td>";
        }
        $html = $html . $data;
        return $html;
    }

    private function actionForRemarks($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'server');
        $remarks = $getTaskId[0]->task_remarks;
        $valueAttr = $remarks !== '' ? "value='$remarks'" : '';

        if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed')) {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;' disabled>
          </td></tr>";
        } else if (isset($getNoted) && $getNoted[0]->ack_status != 'Pending Endorsement') {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;' disabled>
          </td></tr>";
        } else {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;'>
          </td></tr>";
        }
        $html .= $data;
        return $html;
    }

    public function updateServerChecklist(Request $request)
    {
        $remarks = $request->remarks;
        $user = auth()->user()->first_name;
        if ($request->input("check") != null) {
            foreach ($request->input('check') as $key => $list) {
                $getDetailsChecklistTask = $this->listServerJobTask($key);
                if ($request->isMethod("POST")) {
                    if ($getDetailsChecklistTask->task_remarks != null) {
                        $updateData = [
                            'checking' => 'Y',
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now()
                        ];
                    } else {
                        $updateData = [
                            'checking' => 'Y',
                            'task_remarks' => $remarks[$key],
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now()
                        ];
                    }
                    DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                        ->where('task_id', $key)
                        ->update($updateData);
                }
            }
        }

        if ($request->input("amber") != null) {
            foreach ($request->input('amber') as $key => $list) {
                $getDetailsChecklistTask = $this->listServerJobTask($key);
                if ($request->isMethod("POST")) {
                    if ($getDetailsChecklistTask->task_remarks != null) {
                        $updateData = [
                            'checking' => 'Y',
                            'if_amber_light' => 'Y',
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now()
                        ];
                    } else {
                        $updateData = [
                            'checking' => 'Y',
                            'if_amber_light' => 'Y',
                            'task_remarks' => $remarks[$key],
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now()
                        ];
                    }
                    DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                        ->where('task_id', $key)
                        ->update($updateData);
                }
            }
        }

        if ($request->input("remarks") != null) {
            foreach ($request->input('remarks') as $key => $list) {
                if ($request->isMethod("POST")) {
                    $updateData = [
                        'task_remarks' => $remarks[$key],
                        'task_changed_by' => $user,
                        'task_changed_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                        ->where('task_id', $key)
                        ->update($updateData);
                }
            }
        }
        return back();
    }

    public function statusChecklistServerHistory()
    {
        $year = $this->getServerYear();
        return view('it_support.server_checklist_searching_history', [
            'year' => $year,
            'section' => 'server_check',
        ]);
    }

    public function statusChecklistServerHistoryDate(Request $request)
    {
        $date = $request->dateSelection;
        $currentDate = Carbon::now();
        $dateNote = $currentDate->format("Y-m-d");
        $remarks = $request->remarks ?? [];
        $menu = null;
        $menuHelang = null;
        $menuKvdc = null;
        $listNote = null;
        $user = auth()->user()->first_name;
        $listNote = $this->getListNotes($date, 'server');

        if (!empty($date)) {
            if ($request->input("check") != null) {
                foreach ($request->input('check') as $key => $value) {
                    $taskId = intval(preg_replace('/[^0-9]+/', '', $key));
                    // $getDetailsChecklistTask = $this->listServerJobTask($taskId);
                    if ($request->isMethod("POST")) {
                        $taskRemark = isset($remarks[$taskId]) ? $remarks[$taskId] : null; // Get the remark for the current task ID

                        $updateData = [
                            'checking' => 'Y',
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now(),
                            'task_remarks' => $taskRemark, // Use the remark obtained from $remarks array
                        ];
                        DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                            ->where('task_id', $taskId)
                            ->update($updateData);
                    }
                }
            }
            if ($request->input("amber") != null) {
                foreach ($request->input('amber') as $key => $value) {
                    $taskId = intval(preg_replace('/[^0-9]+/', '', $key));
                    // $getDetailsChecklistTask = $this->listServerJobTask($taskId);
                    if ($request->isMethod("POST")) {
                        $taskRemark = isset($remarks[$taskId]) ? $remarks[$taskId] : null; // Get the remark for the current task ID

                        $updateData = [
                            'checking' => 'Y',
                            'if_amber_light' => 'Y',
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now(),
                            'task_remarks' => $taskRemark, // Use the remark obtained from $remarks array
                        ];
                        DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                            ->where('task_id', $taskId)
                            ->update($updateData);
                    }
                }
            }
            if ($request->input("remarks") != null) {
                foreach ($request->input('remarks') as $key => $list) {
                    $taskId = intval(preg_replace('/[^0-9]+/', '', $key));

                    if ($request->isMethod("POST")) {
                        $updateData = [
                            'task_remarks' => $remarks[$key], // Use the extracted task ID here
                            'task_changed_by' => $user,
                            'task_changed_date' => Carbon::now()
                        ];

                        DB::connection('mysql_ep_it_support')->table('esc_server_job_task')
                            ->where('task_id', $taskId)
                            ->update($updateData);
                    }
                }
            }
            $timestamp = strtotime($date);
            if ($timestamp !== false) {
                $formattedDate = date('Y-m-d', $timestamp);
                $menu = $this->getMenuServerHistory($formattedDate, $formattedDate);
                $menuHelang = $this->getMenuServerHelangHistory($formattedDate, $formattedDate);
                $menuKvdc = $this->getMenuServerKvdcHistory($formattedDate, $formattedDate);
            } else {
                return response()->json(['error' => 'Failed to parse date'], 400);
            }
        } else {
            return response()->json(['error' => 'Date is empty'], 400);
        }

        return response()->json([
            'menu' => $menu ?? null,
            'menuHelang' => $menuHelang ?? null,
            'menuKvdc' => $menuKvdc ?? null,
            'listNote' => $listNote ?? null,
            'user' => $user ?? null,
            'dateNote' => $dateNote ?? null,
            'location' => null,
        ]);
    }

    public function byDateServerChecklist($date, $section)
    {
        if ($section === 'server_check') {
            $user = auth()->user()->first_name;
            $groupName = $this->getServerGroup();
            $subGroupName = $this->getServerSubGroup();
            $listdata = $this->getListNotes($date, 'server');
        }

        return view('it_support.server_checklist_history', [
            'dateNote' => $date,
            'message1' => null,
            'groupName' => $groupName,
            'subGroupName' => $subGroupName,
            'listNote' => $listdata,
            'user' => $user,
            'section' => $section
        ]);
    }

    public function byDateStatusChecklist($date, $section)
    {
        if ($section === 'status_check') {
            $user = auth()->user()->first_name;
            $currentDate = Carbon::now();
            $dateNote = $currentDate->format("Y-m-d");
            $menu = $this->getMenu();
            $listdata = $this->getListNotes($date, 'status');
        }

        return view('it_support.status_checklist_history', [
            'date' => $date,
            'dateNote' => $dateNote,
            'message1' => null,
            'menu' => $menu,
            'listNote' => $listdata,
            'user' => $user,
            'section' => $section
        ]);
    }

    public function byDateDbaChecklist($date, $section)
    {
        if ($section === 'dba_check') {
            $groupName = $this->getCategoryName();
            $listdata = $this->getListNotes($date, 'dba');
        }

        return view('it_support.dba_morning_checklist_history', [
            'dateNote' => $date,
            'message1' => null,
            'menu' => $groupName,
            'listNote' => $listdata,
            'user' => null,
            'section' => $section
        ]);
    }

    public function dba_morning_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $id = $request->id;
        $name = $request->name;
        $categoryName = $request->category_name;

        if ($id !== null) {
            $listdata = $this->dbaListDataLook($id);

            $oriname = $name ?: $listdata->dba_data_lookup_name;
            $oricategoryname = $categoryName ?: $listdata->dba_data_lookup_category_name;

            if ($request->isMethod("POST")) {
                $updateData = [
                    'dba_data_lookup_name' => $oriname,
                    'dba_data_lookup_category_name' => $oricategoryname,
                    'dba_data_lookup_changed_by' => $user,
                    'dba_data_lookup_changed_date' => Carbon::now()
                ];

                DB::connection('mysql_ep_it_support')->table('dba_data_lookup')
                    ->where('dba_data_lookup_id', $id)
                    ->update($updateData);
            }

            return back();
        }

        if ($id === null) {
            DB::connection('mysql_ep_it_support')->table('dba_data_lookup')->insert([
                ['dba_data_lookup_name' => $name, 'dba_data_lookup_category_name' => $categoryName, 'dba_data_lookup_created_by' => $user, 'dba_data_lookup_created_date' => Carbon::now()],
            ]);
            return back();
        }

        return $this->dbaMorningDataLookup();
    }

    public function dbaMorningChecklist()
    {

        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        $categoryName = $this->getCategoryName();
        $listdata = $this->getListNotes($date, 'dba');
        return view('it_support.dba_morning_checklist', [
            'listNote' => $listdata,
            'categoryName' => $categoryName,
            'dateNote' => $date,
            'user' => $user,
            'message' => null,
            'location' => null,
        ]);
    }

    public function dbaMorningChecklistHistory()
    {
        $year = $this->getServerYear();
        return view('it_support.dba_checklist_searching_history', [
            'year' => $year,
            'section' => 'dba_check',
        ]);
    }

    public function dbaMorningChecklistNotesHistory($date)
    {
        $listdata = $this->getListNotes($date, 'dba');
        $user = auth()->user()->first_name;

        $responseData = [
            'listdata' => $listdata,
            'user' => $user
        ];
        return response()->json($responseData, 200);
    }

    public function dbaMorningDataLookup()
    {
        $listdata = $this->listAllDBAMorningDataLookUp();
        return view('it_support.dba_morning_checklist_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function searchDbaMorningByGroupChecklist($group, $date)
    {
        $currentDate = Carbon::now();
        if ($date != 'null') {
            $date = $date;
        } else {
            $date = $currentDate->format("Y-m-d");
        }
        $listByCategory = $this->getListByCategory($group);
        $html = '';
        $html = "<thead>
                            <tr>
                            <th class='text-center'>No</th>
                            <th class='text-center'>Task</th>
                            <th class='text-center'>Ok <input type='checkbox' id='selectall' onchange='selectAll(this)'></th>
                            <th class='text-center'>Failed</th>
                            <th class='text-center'>Remarks</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        if ($listByCategory != null) {
            $listByCategoryAndDate = $this->getListByCategoryAndDate($group, $date);
            $index = 1;
            foreach ($listByCategoryAndDate as $listByCategoryAndDate) {
                $html .= self::returnTableStructure($date, $html, $listByCategoryAndDate, $index);
                $index++;
            }
        }
        $html = $html . "</tbody></table>";
        return $html;
    }

    private function returnTableStructure($date, $html, $listByCategoryAndDate, $index)
    {
        $html = self::fixData($listByCategoryAndDate, $html, $index);
        $getTaskId = $this->getDbaMorningTaskId($date, $listByCategoryAndDate->dba_morning_checklist_task_id);
        $getId = $getTaskId[0]->dba_morning_checklist_id;
        $html = self::actionForStatusOK($getTaskId, $getId, $html, $date);
        $html = self::actionForStatusFailed($getTaskId, $getId, $html, $date);
        $html = self::actionForRemarksDbaChecklist($getTaskId, $getId, $html, $date);

        return $html;
    }

    private function fixData($listByCategoryAndDate, $html, $index)
    {
        $html = "<tr>
                    <td style='width: 30px;' class='text-center'>$index</td>
                    <td class='text-center'>$listByCategoryAndDate->dba_morning_checklist_task_name</td>";

        return $html;
    }

    private function actionForStatusOK($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'dba');
        $statusOk = $getTaskId[0]->dba_morning_checklist_status_ok;
        $ackStatus = isset($getNoted[0]) ? $getNoted[0]->ack_status : null;

        $checked = $statusOk ? "checked='checked'" : "";
        $disabled = ($ackStatus == 'Pending Endorsement' || $ackStatus == 'Completed') ? "disabled" : "";

        $data = "<td style='width: 30px;' class='text-center'>
                <input type='radio' id='radio_for_status_okay' name='status[$getId]' value='ok' $checked $disabled>
             </td>";

        return $html . $data;
    }

    private function actionForStatusFailed($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'dba');
        $statusFailed = $getTaskId[0]->dba_morning_checklist_status_failed;
        $ackStatus = isset($getNoted[0]) ? $getNoted[0]->ack_status : null;

        $checked = $statusFailed ? "checked='checked'" : "";
        $disabled = ($ackStatus == 'Pending Endorsement' || $ackStatus == 'Completed') ? "disabled" : "";

        $data = "<td style='width: 30px;' class='text-center'>
                <input type='radio' id='checkbox_for_status_failed' name='status[$getId]' value='failed' $checked $disabled>
             </td>";

        return $html . $data;
    }

    private function actionForRemarksDbaChecklist($getTaskId, $getId, $html, $date)
    {
        $getNoted = $this->getListNotes($date, 'dba');
        $remarks = $getTaskId[0]->dba_morning_checklist_remarks;
        $valueAttr = $remarks !== '' ? "value='$remarks'" : '';

        if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed')) {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;' disabled>
          </td></tr>";
        } else {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;'>
          </td></tr>";
        }



        $html .= $data;

        return $html;
    }

    public function updateDbaMorningChecklist(Request $request)
    {
        // $previousDate = $request->date_search;
        $remarks = $request->remarks;
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        // $categoryName = $this->getCategoryName();
        // $listdata = $this->getListNotes($date, 'dba');

        if (is_array($request->input('status'))) {
            foreach ($request->input('status') as $key => $status) {
                $updateData = [
                    'dba_morning_checklist_changed_by' => $user,
                    'dba_morning_checklist_changed_date' => $currentDate,
                    'dba_morning_checklist_status_ok' => $status === 'ok' ? 'Y' : null,
                    'dba_morning_checklist_status_failed' => $status === 'failed' ? 'Y' : null,
                    'dba_morning_checklist_remarks' => $remarks[$key] ?? null,
                ];

                DB::connection('mysql_ep_it_support')
                    ->table('dba_morning_checklist')
                    ->where('dba_morning_checklist_id', $key)
                    ->update($updateData);
            }
        }
        if ($request->input('status') == null && $request->input('remarks') !== null) {
            foreach ($request->input('remarks') as $key => $list) {
                $getDetailsChecklistTask = $this->updateDbaMorningTask($key);
                if ($getDetailsChecklistTask !== null && !$getDetailsChecklistTask->dba_morning_checklist_remarks) {
                    $updateData = [
                        'dba_morning_checklist_remarks' => $list ?? null,
                        'dba_morning_checklist_changed_by' => $user,
                        'dba_morning_checklist_changed_date' => $currentDate,
                    ];
                    DB::connection('mysql_ep_it_support')
                        ->table('dba_morning_checklist')
                        ->where('dba_morning_checklist_id', $key)
                        ->update($updateData);
                }
            }
        }

        $message = "The checklist tasks have been successfully saved in the system!";
        return back()->with('success', $message);
    }

    public function dbaMorningCreateNote(Request $request)
    {
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $request->selected_date;
        if ($date != null) {
            $date1 = $request->selected_date;
        } else {
            $date1 = $currentDate->format("Y-m-d");
        }
        $listdata = $getCheckBoxChecking = null;
        $checkboxChecking = $this->getServerTaskNo($date1);
        $maxQuantityRow = count($checkboxChecking ?? []);
        $checkingCount = 0;

        if ($checkboxChecking !== null) {
            foreach ($checkboxChecking as $row) {
                if ($row->checking === 'Y') {
                    $checkingCount++;
                }
            }
        }
        if ($request->page_type == 'dba') {
            $listdata = $this->getDbaMorningTaskNo($date1);
            $getCheckBoxChecking = $this->getCountCheckBox($date1);
        } else if ($request->page_type == 'server') {
            $listdata = $this->getServerTaskNo($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxServer($date1);
        } else if ($request->page_type == 'status') {
            $listdata = $this->getStatusChecklistTaskNo($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxStatus($date1);
        } else if ($request->page_type == 'network') {
            $countTask = $this->getNetworkStatusTaskNo($date1);
            $listdata = $this->getNetworkStatus($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxNetworkStatus($date1, $request->location_type);
        } else if ($request->page_type == 'network_per') {
            $countTask = $this->getNetworkPerformStatusTaskNo($date1);
            $listdata = $this->getNetworkPerformStatus($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxNetworkPerformStatus($date1, $request->location_type);
        } else if ($request->page_type == 'network_am') {
            $countTask = $this->getNetworkAmtekStatusTaskNo($date1);
            $listdata = $this->getNetworkAmtekStatus($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxNetworkAmtekStatus($date1, $request->location_type);
        } else if ($request->page_type == 'backup_c') {
            $listdata = $this->getNetworkBackupTotalTask($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxNetworkBackup($date1);
        } else if ($request->page_type == 'backup_w') {
            $listdata = $this->getNetworkBackupWismaTotalTask($date1);
            $getCheckBoxChecking = $this->getCountCheckBoxNetworkBackupWisma($date1);
        }
        $getNoted = $this->getListNotes($date1, $request->page_type);

        if (isset($getCheckBoxChecking) && count($getCheckBoxChecking) > 0 && $getCheckBoxChecking != null) {
            $totalCheck = count($getCheckBoxChecking);
            if ($request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am') {
                $totalCount = $countTask;
            } else {
                $totalCount = count($listdata);
            }
            $totalList = count($listdata);
            $user = auth()->user()->first_name;

            // dump($totalCheck);
            // dump($totalCount);
            // dd($totalList);
            if ($totalCheck == $totalCount && $totalCount == $totalList) {
                //create note
                if ($getNoted == null || (count($getNoted) > 0 && $getNoted[0]->ack_status != 'Pending Endorsement')) {
                    $updateData = [
                        'ack_date' => $date1,
                        'ack_remarks' => $request->remarks ?? ($getNoted ? $getNoted->ack_remarks : null),
                        'ack_endorsement_by' => $request->ack_by ?? ($getNoted ? $getNoted[0]->ack_endorsement_by : null),
                        'ack_endorsement_date' => $request->ack_date ?? ($getNoted && is_object($getNoted) ? $getNoted->ack_endorsement_date : null),
                        'ack_status' => 'Pending Endorsement' ?? ($getNoted ? 'Pending Endorsement' : null)
                    ];
                    if ($getNoted == null || ($getNoted && is_object($getNoted) && $getNoted->ack_check_by == null)) {
                        $updateData['ack_check_by'] = $user;
                        $updateData['ack_check_date'] = Carbon::now();
                        $updateData['ack_module'] = $request->page_type;
                    }
                    if ($getNoted == null) {
                        $task_no = $listdata[0]->ack_task_no;
                        $updateData['ack_task_no'] = $task_no;
                        DB::connection('mysql_ep_it_support')
                            ->table('ep_acknowledge_status')
                            ->insert($updateData);
                    }

                    if (is_array($getNoted) && count($getNoted) > 0 && $getNoted != null) {
                        $task_no = $listdata[0]->ack_task_no;
                        if ($request->isMethod("POST")) {
                            DB::connection('mysql_ep_it_support')
                                ->table('ep_acknowledge_status')
                                ->where('ack_task_no', $task_no)
                                ->update($updateData);
                        }
                    }
                    if ($date1 == $currentDate->format("Y-m-d")) {
                        $getNoted = $this->getListNotes($date1, $request->page_type);
                        if ($request->page_type == 'dba') {
                            return back()->with('success', "The daily task 'DBA monitoring check' has been saved successfully!");
                        } elseif ($request->page_type == 'server') {
                            return back()->with('success', "The daily task 'Server Checklist' has been saved successfully!");
                        } elseif ($request->page_type == 'status') {
                            return back()->with('success', "The daily task 'Status Checklist' has been saved successfully!");
                        } elseif ($request->page_type == 'network') {
                            return back()->with('success', "The daily task 'Network KVDC List' has been submit successfully!");
                        } elseif ($request->page_type == 'network_per') {
                            return back()->with('success', "The daily task 'Network CDC Cyberjaya List' has been submit successfully!");
                        } elseif ($request->page_type == 'network_am') {
                            return back()->with('success', "The daily task 'Network Wisma CDC List' has been submit successfully!");
                        } elseif ($request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                            return back()->with('success', "The daily task 'Network Backup Checklist' has been submit successfully!");
                        }
                    } else {
                        if ($request->page_type == 'dba') {
                            return back()->with('success', "The daily task 'DBA monitoring check' has been saved successfully!");
                        } elseif ($request->page_type == 'server') {
                            return back()->with('success', "The daily task 'Server Checklist' has been saved successfully!");
                        } elseif ($request->page_type == 'status') {
                            return back()->with('success', "The daily task 'Status Checklist' has been saved successfully!");
                        } elseif ($request->page_type == 'network') {
                            return back()->with('success', "The daily task 'Network KVDC List' has been submit successfully!");
                        } elseif ($request->page_type == 'network_per') {
                            return back()->with('success', "The daily task 'Network CDC Cyberjaya List' has been submit successfully!");
                        } elseif ($request->page_type == 'network_am') {
                            return back()->with('success', "The daily task 'Network Wisma CDC List' has been submit successfully!");
                        } elseif ($request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                            return back()->with('success', "The daily task 'Network Backup Checklist' has been submit successfully!");
                        }
                    }
                } //task for approver 
                elseif (Auth::user()->isGroupServerAdmin() || Auth::user()->isDbaUsersEpss() || Auth::user()->isNetworkApprover()) {
                    $user = auth()->user()->first_name;
                    if (is_array($getNoted) && count($getNoted) > 0 && $getNoted != null && isset($getNoted[0]->ack_status) && !is_null($getNoted[0]->ack_status) && $getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed') {
                        $updateData = [
                            'ack_remarks' => $request->remarks ?? ($getNoted ? $getNoted->ack_remarks : null),
                            'ack_endorsement_by' => $user ?? ($getNoted ? $getNoted->ack_endorsement_by : null),
                            'ack_endorsement_date' => $request->ack_date ?? ($getNoted ? $getNoted->ack_endorsement_date : null),
                            'ack_status' => 'Completed' ?? ($getNoted ? $getNoted->ack_status : null)
                        ];

                        if ($getNoted != null) {
                            $task_no = $listdata[0]->ack_task_no;
                            if ($request->isMethod("POST")) {
                                DB::connection('mysql_ep_it_support')
                                    ->table('ep_acknowledge_status')
                                    ->where('ack_task_no', $task_no)
                                    ->update($updateData);
                            }
                        }
                        // history for approver to approve
                        if ($date1 != $currentDate->format("Y-m-d")) {
                            if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                                return back()->with('success', "Task has been completed");
                            } elseif (Auth::user()->isGroupServerAdmin() && $request->page_type == 'server' || $request->page_type == 'status') {
                                return back()->with('success', "Task has been completed");
                            } elseif (Auth::user()->isNetworkApprover() && $request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                                return back()->with('success', "Task has been completed");
                            }
                        }
                        // date today for approver to approve
                        else {
                            if (Auth::user()->isDbaUsersEpss() || Auth::user()->isGroupServerAdmin() || Auth::user()->isNetworkApprover() && $date1 == $currentDate->format("Y-m-d")) {
                                $getNoted = $this->getListNotes($date1, $request->page_type);
                                if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                                    return back()->with('success', "Task has been completed");
                                } elseif (Auth::user()->isNetworkApprover() && $request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                                    return back()->with('success', "Task has been completed");
                                } elseif (Auth::user()->isGroupServerAdmin() && $request->page_type == 'server' || $request->page_type == 'status') {
                                    return back()->with('success', "Task has been completed");
                                }
                            }
                        }
                    } //if skip ke approver
                    else {
                        // for history -approver page
                        if (Auth::user()->isDbaUsersEpss() && $date1 != $currentDate->format("Y-m-d")) {
                            if ($request->page_type == 'dba') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            } elseif (Auth::user()->isGroupServerAdmin() && $request->page_type == 'server' || $request->page_type == 'status') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            } elseif (Auth::user()->isNetworkApprover() && $request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            }
                        } else {
                            // for today - approver page
                            if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            } elseif (Auth::user()->isNetworkApprover() && $request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            } elseif (Auth::user()->isGroupServerAdmin() && $request->page_type == 'server' || $request->page_type == 'status') {
                                return back()->with('failed', "A note is required. Kindly input a note");
                            }
                        }
                    }
                } else {
                    if ($request->page_type == 'dba' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'network' || $request->page_type == 'server' || $request->page_type == 'status') {
                        return back()->with('failed', "Invalid Role");
                    }
                }
            }
            // kalau ada check box yang tak di check
            else {
                //today punya
                if ($date1 == $currentDate->format("Y-m-d")) {
                    if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Failed' in that location.");
                    } elseif ($request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Not Ok' in that location.");
                    } elseif ($request->page_type == 'server') {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'Check' or 'If(Amber Light)'.");
                    } elseif ($request->page_type == 'status') {
                        return back()->with('failed', "Please ensure that you review each task in every row.");
                    }
                } else {
                    //history
                    if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Failed' in that location.");
                    } elseif ($request->page_type == 'server' && $maxQuantityRow != $checkingCount) {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'Check' or 'If(Amber Light)'.");
                    } elseif ($request->page_type == 'status' && $totalCheck != $totalList) {
                        return back()->with('failed', "Please ensure that you review each task in every row.");
                    } elseif ($request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                        return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Not Ok' in that location.");
                    }
                }
            }
        }
        // kalau tak tick semua checkbox 
        else {
            //today page
            if ($date1 == $currentDate->format("Y-m-d")) {
                if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Failed' in that location.");
                } elseif ($request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Not Ok' in that location.");
                } elseif ($request->page_type == 'server') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'Check' or 'If(Amber Light)'.");
                } elseif ($request->page_type == 'status') {
                    return back()->with('failed', "Please ensure that you review each task in every row.");
                }
            } else { //history page
                if (Auth::user()->isDbaUsersEpss() && $request->page_type == 'dba') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Failed' in that location.");
                } elseif ($request->page_type == 'server') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'Check' or 'If(Amber Light)'.");
                } elseif ($request->page_type == 'status') {
                    return back()->with('failed', "Please ensure that you review each task in every row.");
                } elseif ($request->page_type == 'network' || $request->page_type == 'network_per' || $request->page_type == 'network_am' || $request->page_type == 'backup_c' || $request->page_type == 'backup_w') {
                    return back()->with('failed', "Please ensure that you review each task in every row and mark it as either 'OK' or 'Not Ok' in that location.");
                }
            }
        }
    }

    public function dba_morning_delete_data_lookup(Request $request)
    {
        if ($request->id != null) {
            DB::connection('mysql_ep_it_support')->table('dba_data_lookup')
                ->where('dba_data_lookup_id', $request->id)
                ->delete();
            return back();
        }
    }

    public function networkDataLookup()
    {
        $listdata = $this->listAllNetworkDataLookUp();
        return view('it_support.network_checklist_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function add_network_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $id = $request->task_id;
        $name = $request->task_name;
        $group = $request->task_group;
        $subgroup = $request->task_subgroup;
        $taskStatus = $request->task_status;

        if ($id !== null) {
            $listdata = $this->networkListDataLook($id);

            $oriName = $name ?: $listdata->net_data_name;
            $oriGrup = $group ?: $listdata->net_data_group;
            $oriSubGrup = $subgroup ?: $listdata->net_data_subgroup;
            $oriStatus = $taskStatus ?: $listdata->net_data_status;

            if ($request->isMethod("POST")) {
                $updateData = [
                    'net_data_name' => $oriName,
                    'net_data_group' => $oriGrup,
                    'net_data_subgroup' => $oriSubGrup,
                    'net_data_status' => $oriStatus,
                    'net_changed_by' => $user,
                    'net_changed_date' => Carbon::now()
                ];

                DB::connection('mysql_ep_it_support')->table('network_data_lookup')
                    ->where('net_data_id', $id)
                    ->update($updateData);
            }

            return back();
        }

        if ($id === null) {
            DB::connection('mysql_ep_it_support')->table('network_data_lookup')->insert([
                ['net_data_name' => $name, 'net_data_group' => $group, 'net_data_subgroup' => $subgroup, 'net_data_status' => $taskStatus, 'net_created_by' => $user, 'net_created_date' => Carbon::now()],
            ]);
            return back();
        }

        return $this->networkDataLookup();
    }

    public function networkChecklist()
    {
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        $groupName = $this->getNetworkGroup();
        $listdata = $this->getListNotes($date, 'network');
        return view('it_support.network_checklist', [
            'listNote' => $listdata,
            'groupName' => $groupName,
            'dateNote' => $date,
            'user' => $user,
            'message1' => null,
            'section' => 'network_kvdc'
        ]);
    }

    public function networkChecklistHistory()
    {
        $year = $this->getNetworkYear();
        return view('it_support.network_checklist_searching_history', [
            'year' => $year,
            'section' => 'network'
        ]);
    }

    public function getMonthList($year, $section)
    {
        if ($section == 'network') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(netlist_task_date) AS month from ep_it_support.network_checklist where YEAR(netlist_task_date) = ?
            order by month asc", [$year]);
        }

        if ($section == 'network_per') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(netperlist_task_date) AS month from ep_it_support.network_perform_checklist where YEAR(netperlist_task_date) = ?
        order by month asc ", [$year]);
        }

        if ($section == 'network_am') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(amlist_task_date) AS month from ep_it_support.amtek_checklist where YEAR(amlist_task_date) = ?
        order by month asc ", [$year]);
        }

        if ($section == 'backup_c' || $section == 'backup_w') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(nbld_task_date) AS month from ep_it_support.network_backup_list_day where YEAR(nbld_task_date) = ?
        order by month asc ", [$year]);
        }

        if ($section == 'server_check') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(task_date) AS month from ep_it_support.esc_server_job_task where year(task_date) = ?
        order by month asc ", [$year]);
        }

        if ($section == 'status_check') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(task_date) AS month from ep_it_support.esc_job_task where year(task_date) = ?
        order by month asc ", [$year]);
        }

        if ($section == 'dba_check') {
            $months = DB::connection('mysql_ep_it_support')->select("
        select distinct MONTH(dba_morning_checklist_date) AS month from ep_it_support.dba_morning_checklist where year(dba_morning_checklist_date) = ?
        order by month asc ", [$year]);
        }

        return response()->json($months);
    }

    public function getListAllData($year, $month, $section)
    {
        if ($section == 'network') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT nc.netlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.network_checklist nc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = nc.netlist_task_date
AND eas.ack_module = 'network'
WHERE YEAR(nc.netlist_task_date) = ?
AND MONTH(nc.netlist_task_date) = ?
GROUP BY nc.netlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY nc.netlist_task_date ASC", [$year, $month]);
        }

        if ($section == 'network_per') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT nc.netperlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.network_perform_checklist nc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = nc.netperlist_task_date
AND eas.ack_module = 'network_per'
WHERE YEAR(nc.netperlist_task_date) = ?
AND MONTH(nc.netperlist_task_date) = ?
GROUP BY nc.netperlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY nc.netperlist_task_date ASC", [$year, $month]);
        }

        if ($section == 'network_am') {
            $listing = DB::connection('mysql_ep_it_support')->select("
            SELECT DISTINCT nc.amlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
            FROM ep_it_support.amtek_checklist nc
            LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = nc.amlist_task_date
            AND eas.ack_module = 'network_am'
            WHERE YEAR(nc.amlist_task_date) = ?
            AND MONTH(nc.amlist_task_date) = ?
            GROUP BY nc.amlist_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
            ORDER BY nc.amlist_task_date ASC", [$year, $month]);
        }

        if ($section == 'server_check') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT nc.task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.esc_server_job_task nc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = nc.task_date
AND eas.ack_module = 'server'
WHERE YEAR(nc.task_date) = ?
AND MONTH(nc.task_date) = ?
GROUP BY nc.task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY nc.task_date ASC", [$year, $month]);
        }

        if ($section == 'status_check') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT nc.task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.esc_job_task nc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = nc.task_date
AND eas.ack_module = 'status'
WHERE YEAR(nc.task_date) = ?
AND MONTH(nc.task_date) = ?
GROUP BY nc.task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY nc.task_date ASC", [$year, $month]);
        }

        if ($section == 'dba_check') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT dmc.dba_morning_checklist_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.dba_morning_checklist dmc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = dmc.dba_morning_checklist_date
AND eas.ack_module = 'dba'
WHERE YEAR(dmc.dba_morning_checklist_date) = ?
AND MONTH(dmc.dba_morning_checklist_date) = ?
GROUP BY dmc.dba_morning_checklist_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY dmc.dba_morning_checklist_date ASC", [$year, $month]);
        }
        
        if ($section == 'backup_c') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT dmc.nbld_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.network_backup_list_day dmc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = dmc.nbld_task_date
AND eas.ack_module = 'backup_c'
WHERE YEAR(dmc.nbld_task_date) = ?
AND MONTH(dmc.nbld_task_date) = ?
and dmc.nbl_location <> 'WISMA CDC'
GROUP BY dmc.nbld_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY dmc.nbld_task_date asc ", [$year, $month]);
        }

        if ($section == 'backup_w') {
            $listing = DB::connection('mysql_ep_it_support')->select("
        SELECT DISTINCT dmc.nbld_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted') AS ack_status
FROM ep_it_support.network_backup_list_day dmc
LEFT OUTER JOIN ep_it_support.ep_acknowledge_status eas ON eas.ack_date = dmc.nbld_task_date
AND eas.ack_module = 'backup_w'
WHERE YEAR(dmc.nbld_task_date) = ?
AND MONTH(dmc.nbld_task_date) = ?
and dmc.nbl_location = 'WISMA CDC'
GROUP BY dmc.nbld_task_date, eas.ack_date, COALESCE(eas.ack_status, 'Not submitted')
ORDER BY dmc.nbld_task_date asc ", [$year, $month]);
        }

        return response()->json($listing);
    }

    public function networkChecklistNotesHistory($date, $section)
    {

        $listdata = $this->getListNotes($date, $section);
        $user = auth()->user()->first_name;

        $responseData = [
            'listdata' => $listdata,
            'user' => $user
        ];
        return response()->json($responseData, 200);
    }

    public function searchNetworkGroupChecklist($location, $group, $data)
    {
        $currentDate = Carbon::now();
        if ($data != 'null') {
            $date = $data;
        } else {
            $date = $currentDate->format("Y-m-d");
        }

        switch ($location) {
            case 'K':
                $listByCategory = $this->getListByNetworkCategoryAndDate($group, $date);
                $listSearchBySubGroup = $this->getListBySubCategoryNetwork($group);
                break;
            case 'S':
                $listByCategory = $this->getListByNetworkPerformCategoryAndDate($group, $date);
                $listSearchBySubGroup = null;
                break;
            case 'W':
                $listByCategory = $this->getListByNetworkAmtekCategoryAndDate($group, $date);
                $listSearchBySubGroup = $this->getListBySubCategoryNetworkAmtek($group);
                break;
            default:
                $listByCategory = null;
                $listSearchBySubGroup = null;
                break;
        }

        $html = "<thead>
                            <tr>
                            <th class='text-center'>No</th>
                            <th class='text-center'>Network Device Name</th>
                            <th class='text-center'>Ok <input type='checkbox' id='selectall' onchange='selectAll(this)'></th>
                            <th class='text-center'>Not Ok</th>
                            <th class='text-center'>Remarks</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";

        if (!is_null($listByCategory) && is_array($listByCategory)) {
            if (!empty($listSearchBySubGroup) && in_array($location, ['K', 'W'])) {
                foreach ($listSearchBySubGroup as $listing) {
                    $variable = ($location == 'K') ? $listing->netlist_task_subgroup : $listing->amlist_task_subgroup;
                    $html = self::subGroupNetwork($listing, $listByCategory, $date, $html, $variable, $location);
                }
            } elseif ($location == 'S') {
                foreach ($listByCategory as $value) {
                    static $index = 1;
                    $html .= self::returnTableStructureNetwork($date, $html, $value, $location, $index);
                    $index++;
                }
            }
        }

        $html = $html . "</tbody></table>";
        return $html;
    }

    private function subGroupNetwork($listing, $listByCategory, $date, $html, $variable, $location)
    {
        $count = 1;
        $countStep = 4;
        if ($location == 'K') {
            if (!empty($listByCategory)) {
                if (!is_null($listing->netlist_task_subgroup)) {
                    for ($i = 0; $i < $count; $i++) {
                        $html .= "<th colspan='5' class='text-center'>$listing->netlist_task_subgroup</th>";
                        for ($i = 0; $i < $countStep; $i++) {
                            $html .= "<th style='display:none'></th>";
                        }
                    }
                }
            }
            if ($listByCategory !== null) {
                foreach ($listByCategory as $value) {
                    $listingNaming = $this->getNetworkTaskId($date, $value->netlist_task_id);
                    if (!empty($listingNaming) && (is_array($listingNaming) || is_object($listingNaming))) {
                        foreach ($listingNaming as $list) {
                            if ($list->netlist_task_subgroup == $variable) {
                                static $index = 1;
                                $html .= self::returnTableStructureNetwork($date, $html, $value, $location, $index);
                                $index++;
                            }
                        }
                    }
                }
            }
        }

        if ($location == 'W') {
            if (!empty($listByCategory)) {
                if (!is_null($listing->amlist_task_subgroup)) {
                    for ($i = 0; $i < $count; $i++) {
                        $html .= "<th colspan='5' class='text-center'>$listing->amlist_task_subgroup</th>";
                        for ($i = 0; $i < $countStep; $i++) {
                            $html .= "<th style='display:none'></th>";
                        }
                    }
                }
            }
            if ($listByCategory !== null) {
                foreach ($listByCategory as $value) {
                    $listingNaming = $this->getNetworkAmtekTaskId($date, $value->amlist_task_id);
                    if (!empty($listingNaming) && (is_array($listingNaming) || is_object($listingNaming))) {
                        foreach ($listingNaming as $list) {
                            if ($list->amlist_task_subgroup == $variable) {
                                static $index = 1;
                                $html .= self::returnTableStructureNetwork($date, $html, $value, $location, $index);
                                $index++;
                            }
                        }
                    }
                }
            }
        }
        return $html;
    }

    private function returnTableStructureNetwork($date, $html, $value, $location, $index)
    {
        $html = self::fixDataNetwork($value, $html, $location, $index);

        $taskIdFieldMapping = [
            'K' => 'netlist_task_id',
            'S' => 'netperlist_task_id',
            'W' => 'amlist_task_id'
        ];

        $taskIdMethodMapping = [
            'K' => 'getNetworkTaskId',
            'S' => 'getNetworkTaskIdPerform',
            'W' => 'getNetworkAmtekTaskId'
        ];

        if (array_key_exists($location, $taskIdFieldMapping) && array_key_exists($location, $taskIdMethodMapping)) {
            $taskIdField = $taskIdFieldMapping[$location];
            $taskIdMethod = $taskIdMethodMapping[$location];

            $getTaskId = $this->$taskIdMethod($date, $value->$taskIdField);
            $getId = $getTaskId[0]->$taskIdField;
        }

        $html = self::actionForStatusOKNetwork($getTaskId, $getId, $html, $date, $location);
        $html = self::actionForStatusFailedNetwork($getTaskId, $getId, $html, $date, $location);
        $html = self::actionForRemarksDbaChecklistNetwork($getTaskId, $getId, $html, $date, $location, $value);

        return $html;
    }

    private function fixDataNetwork($value, $html, $location, $index)
    {
        $taskNameFieldMapping = [
            'K' => 'netlist_task_name',
            'S' => 'netperlist_task_name',
            'W' => 'amlist_task_name'
        ];

        if (array_key_exists($location, $taskNameFieldMapping)) {
            $taskNameField = $taskNameFieldMapping[$location];
            $html = "<tr>
                     <td style='width: 30px;' class='text-center'>$index</td>
                     <td class='text-center'>{$value->$taskNameField}</td>";
        }
        return $html;
    }

    private function actionForStatusOKNetwork($getTaskId, $getId, $html, $date, $location)
    {
        $noteTypeMapping = [
            'K' => 'network',
            'S' => 'network_per',
            'W' => 'network_am'
        ];

        $checkedFieldMapping = [
            'K' => 'netlist_checked_kvdc',
            'S' => 'netperlist_checked_star',
            'H' => 'netlist_checked_heitech',
            'W' => 'amlist_checked_wisma'
        ];

        if (array_key_exists($location, $noteTypeMapping)) {
            $getNoted = $this->getListNotes($date, $noteTypeMapping[$location]);
        }

        $checkedValue = $checkedFieldMapping[$location] ?? null;

        if ($checkedValue) {
            $checkedValue = $getTaskId[0]->$checkedValue ?? null;
        }

        $disabledAttribute = "";

        $value = 'P';
        $checkedAttribute = ($checkedValue === 'P') ? "checked='checked'" : '';
        if ($getNoted == null || ($getNoted[0]->ack_status != 'Pending Endorsement' && $getNoted[0]->ack_status != 'Completed')) {
            $disabledAttribute = "";
        } else {
            $disabledAttribute = "disabled";
        }

        $data = "<td style='width: 30px;' class='text-center'><input type='radio' id='radio_for_status_okay' name='status[$getId]' value='$value' $checkedAttribute $disabledAttribute></td>";

        $html .= $data;
        return $html;
    }

    private function actionForStatusFailedNetwork($getTaskId, $getId, $html, $date, $location)
    {
        $noteTypeMapping = [
            'K' => 'network',
            'S' => 'network_per',
            'W' => 'network_am'
        ];

        $checkedFieldMapping = [
            'K' => 'netlist_checked_failed_kvdc',
            'S' => 'netperlist_checked_failed_star',
            'H' => 'netlist_checked_failed_heitech',
            'W' => 'amlist_checked_failed_wisma'
        ];

        $getNoted = isset($noteTypeMapping[$location]) ? $this->getListNotes($date, $noteTypeMapping[$location]) : null;

        $checkedValueField = $checkedFieldMapping[$location] ?? null;
        $checkedValue = $checkedValueField ? $getTaskId[0]->$checkedValueField ?? null : null;

        $disabledAttribute = "";

        if ($checkedValue === 'F') {
            $value = 'F';
            $checkedAttribute = "checked='checked'";
        } else {
            $value = 'F';
            $checkedAttribute = '';
        }

        if ($getNoted == null || ($getNoted[0]->ack_status != 'Pending Endorsement' && $getNoted[0]->ack_status != 'Completed')) {
            $disabledAttribute = "";
        } else {
            $disabledAttribute = "disabled";
        }

        $data = "<td style='width: 30px;' class='text-center'><input type='radio' id='radio_for_status_failed' name='status[$getId]' value='$value' $checkedAttribute $disabledAttribute></td>";

        $html .= $data;
        return $html;
    }

    private function actionForRemarksDbaChecklistNetwork($getTaskId, $getId, $html, $date, $location, $value)
    {
        $remarks = null;
        $noteTypeMapping = [
            'K' => 'network',
            'S' => 'network_per',
            'W' => 'network_am'
        ];

        $remarksFieldMapping = [
            'K' => 'netlist_remarks_kvdc',
            'S' => 'netperlist_remarks_star',
            'W' => 'amlist_remarks_wisma'
        ];

        $getNoted = isset($noteTypeMapping[$location]) ? $this->getListNotes($date, $noteTypeMapping[$location]) : null;
        $remarksField = $remarksFieldMapping[$location] ?? null;
        $remarks = $remarksField ? $getTaskId[0]->$remarksField ?? null : null;

        $valueAttr = $remarks !== '' ? "value='$remarks'" : '';

        if (isset($getNoted) && ($getNoted[0]->ack_status == 'Pending Endorsement' || $getNoted[0]->ack_status == 'Completed')) {
            $data = "<td style='width: 500px;'>
            <input  type='text' name='remarks[$getId]' $valueAttr  style='width: 500px;' disabled>
          </td></tr>";
        } else {
            if ($location === 'S' && $value->netperlist_remarks_star === null) {
                if ($value->netperlist_task_name === "CDC CYBERJAYA FIREWALL") {
                    $valueAttr = "value='Conn : '";
                }
                if ($value->netperlist_task_name === "CDC CYBERJAYA USER TO INTERNET" || $value->netperlist_task_name === 'CDC CYBERJAYA TO 192.168.68.118 (KVDC)30MB' || $value->netperlist_task_name === "WISMA CDC TO 192.168.68.118 (KVDC)20MB" || $value->netperlist_task_name === "KVDC TO 192.168.43.43 (DRC) 30MB") {
                    $valueAttr = "value='DL :  Mbps   UL :  Mbps'";
                }
            }
            $data = "<td style='width: 500px;'>
                <input type='text' name='remarks[$getId]' $valueAttr style='width: 500px;'>
            </td></tr>";
        }

        $html .= $data;

        return $html;
    }

    public function byDateNetworkChecklist($date, $section)
    {
        if ($section === 'network_kvdc') {
            $groupName = $this->getNetworkGroup();
            $listdata = $this->getListNotes($date, 'network');
        }

        if ($section === 'network_per') {
            $groupName = $this->getNetworkPerformGroup();
            $listdata = $this->getListNotes($date, 'network_per');
        }

        if ($section === 'network_am') {
            $groupName = $this->getNetworkAmtekGroup();
            $listdata = $this->getListNotes($date, 'network_am');
        }

        if ($section === 'backup_c') {
            $user = auth()->user()->first_name;
            $listTask = $this->getListTask($date);
            $listdata = $this->getListNotes($date, 'backup_c');
            return view('it_support.network_backup_list', [
                'listNote' => $listdata,
                'dateNote' => $date,
                'user' => $user,
                'listTask' => $listTask,
                'message1' => null,
                'location' => 'backup_c'
            ]);
        }

        if ($section === 'backup_w') {
            $user = auth()->user()->first_name;
            $listTask = $this->getListTaskWisma($date);
            $listdata = $this->getListNotes($date, 'backup_w');
            return view('it_support.network_backup_list_wisma', [
                'listNote' => $listdata,
                'dateNote' => $date,
                'user' => $user,
                'listTask' => $listTask,
                'message1' => null,
                'location' => 'backup_w'
            ]);
        }

        return view('it_support.network_checklist_history', [
            'dateNote' => $date,
            'message1' => null,
            'groupName' => $groupName,
            'listNote' => $listdata,
            'user' => null,
            'section' => $section
        ]);
    }

    public function updateNetworkChecklist(Request $request)
    {
        $location = $request->radio_reqcategory;
        $previousDate = $request->date_search;
        $remarks = $request->remarks;
        $group = $request->menu;
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        if (is_array($request->input('status')) === true) {
            foreach ($request->input('status') as $key => $list) {
                if ($list === 'P') {
                    switch ($location) {
                        case 'K':
                            $table = 'network_checklist';
                            $changeByField = 'netlist_changed_by_kvdc';
                            $changeDateField = 'netlist_changed_date_kvdc';
                            $checkedField = 'netlist_checked_kvdc';
                            $failedField = 'netlist_checked_failed_kvdc';
                            $remarksField = 'netlist_remarks_kvdc';
                            break;
                        case 'S':
                            $table = 'network_perform_checklist';
                            $changeByField = 'netperlist_changed_by_star';
                            $changeDateField = 'netperlist_changed_date_star';
                            $checkedField = 'netperlist_checked_star';
                            $failedField = 'netperlist_checked_failed_star';
                            $remarksField = 'netperlist_remarks_star';
                            break;
                        case 'W':
                            $table = 'amtek_checklist';
                            $changeByField = 'amlist_changed_by_wisma';
                            $changeDateField = 'amlist_changed_date_wisma';
                            $checkedField = 'amlist_checked_wisma';
                            $failedField = 'amlist_checked_failed_wisma';
                            $remarksField = 'amlist_remarks_wisma';
                            break;
                        default:
                            break;
                    }

                    $updateData = [
                        $changeByField => $user,
                        $changeDateField => $currentDate,
                        $checkedField => 'P',
                        $failedField => null,
                        $remarksField => $remarks[$key] ?? null,
                    ];

                    DB::connection('mysql_ep_it_support')
                        ->table($table)
                        ->where($table === 'network_checklist' ? 'netlist_task_id' : ($table === 'network_perform_checklist' ? 'netperlist_task_id' : 'amlist_task_id'), $key)
                        ->update($updateData);
                }
                if ($list === 'F') {
                    switch ($location) {
                        case 'K':
                            $table = 'network_checklist';
                            $changeByField = 'netlist_changed_by_kvdc';
                            $changeDateField = 'netlist_changed_date_kvdc';
                            $checkedField = 'netlist_checked_kvdc';
                            $failedField = 'netlist_checked_failed_kvdc';
                            $remarksField = 'netlist_remarks_kvdc';
                            break;
                        case 'S':
                            $table = 'network_perform_checklist';
                            $changeByField = 'netperlist_changed_by_star';
                            $changeDateField = 'netperlist_changed_date_star';
                            $checkedField = 'netperlist_checked_star';
                            $failedField = 'netperlist_checked_failed_star';
                            $remarksField = 'netperlist_remarks_star';
                            break;
                        case 'W':
                            $table = 'amtek_checklist';
                            $changeByField = 'amlist_changed_by_wisma';
                            $changeDateField = 'amlist_changed_date_wisma';
                            $checkedField = 'amlist_checked_wisma';
                            $failedField = 'amlist_checked_failed_wisma';
                            $remarksField = 'amlist_remarks_wisma';
                            break;
                        default:
                            break;
                    }

                    $updateData = [
                        $changeByField => $user,
                        $changeDateField => $currentDate,
                        $checkedField => null,
                        $failedField => 'F',
                        $remarksField => $remarks[$key] ?? null,
                    ];

                    DB::connection('mysql_ep_it_support')
                        ->table($table)
                        ->where($table === 'network_checklist' ? 'netlist_task_id' : ($table === 'network_perform_checklist' ? 'netperlist_task_id' : 'amlist_task_id'), $key)
                        ->update($updateData);
                }
            }
        } else {
            $columnMap = [
                'K' => [
                    'changed_by' => 'kvdc',
                    'checked' => null,
                    'checked_failed' => null,
                    'remarks' => 'kvdc',
                ],
                'S' => [
                    'changed_by' => 'star',
                    'checked' => null,
                    'checked_failed' => null,
                    'remarks' => 'star',
                ],
                'H' => [
                    'changed_by' => 'heitech',
                    'checked' => null,
                    'checked_failed' => null,
                    'remarks' => 'heitech',
                ],
                'W' => [
                    'changed_by' => 'wisma',
                    'checked' => null,
                    'checked_failed' => null,
                    'remarks' => 'wisma',
                ],
            ];

            if (isset($columnMap[$location])) {
                $updateData = [
                    'netlist_changed_by_' . $columnMap[$location]['changed_by'] => $user,
                    'netlist_changed_date_' . $columnMap[$location]['changed_by'] => $currentDate,
                    'netlist_checked_' . $columnMap[$location]['changed_by'] => $columnMap[$location]['checked'],
                    'netlist_checked_failed_' . $columnMap[$location]['changed_by'] => $columnMap[$location]['checked_failed'],
                    'netlist_remarks_' . $columnMap[$location]['changed_by'] => null,
                ];
                DB::connection('mysql_ep_it_support')
                    ->table('network_checklist')
                    ->where('netlist_task_date', $date)
                    ->where('netlist_task_group', $group)
                    ->update($updateData);
            }
            return back();
        }


        if ($request->input('status') !== null) {
            if ($previousDate == null || $previousDate == $date) {
                return back()->with('success', "The checklist tasks have been successfully saved in the system!");
            } else {
                return back()->with('success', "The checklist tasks have been successfully saved in the system!");
            }
        }
    }

    public function networkPerformanceChecklist()
    {
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        $groupName = $this->getNetworkPerformGroup();
        $listdata = $this->getListNotes($date, 'network_per');
        return view('it_support.network_checklist', [
            'listNote' => $listdata,
            'groupName' => $groupName,
            'dateNote' => $date,
            'user' => $user,
            'message1' => null,
            'section' => 'network_per'
        ]);
    }

    public function networkPerformanceDataLookup()
    {
        $listdata = $this->listAllNetworkPerformDataLookUp();
        return view('it_support.network_performance_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function addNetworkPerformanceDataLookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $id = $request->task_id;
        $name = $request->task_name;
        $group = $request->task_group;
        $taskStatus = $request->task_status;

        if ($id !== null) {
            $listdata = $this->networkPerformListDataLook($id);

            $oriName = $name ?: $listdata->net_data_name;
            $oriGrup = $group ?: $listdata->net_data_group;
            $oriStatus = $taskStatus ?: $listdata->net_data_status;

            if ($request->isMethod("POST")) {
                $updateData = [
                    'netper_data_name' => $oriName,
                    'netper_data_group' => $oriGrup,
                    'netper_data_status' => $oriStatus,
                    'netper_changed_by' => $user,
                    'netper_changed_date' => Carbon::now()
                ];

                DB::connection('mysql_ep_it_support')->table('network_perform_data_lookup')
                    ->where('netper_data_id', $id)
                    ->update($updateData);
            }

            return back();
        }

        if ($id === null) {
            DB::connection('mysql_ep_it_support')->table('network_perform_data_lookup')->insert([
                ['netper_data_name' => $name, 'netper_data_group' => $group, 'netper_data_status' => $taskStatus, 'netper_created_by' => $user, 'netper_created_date' => Carbon::now()],
            ]);
            return back();
        }

        return $this->networkPerformanceDataLookup();
    }

    public function networkPerformanceChecklistHistory()
    {
        $year = $this->getPerformanceYear();
        return view('it_support.network_checklist_searching_history', [
            'year' => $year,
            'section' => 'network_per'
        ]);
    }

    public function networkAmtekDataLookup()
    {
        $listdata = $this->listAllAmtekDataLookUp();
        return view('it_support.network_amtek_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function add_networkAmtek_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $id = $request->task_id;
        $seqId = $request->seq_id;
        $name = $request->task_name;
        $group = $request->task_group;
        $subgroup = $request->task_subgroup;
        $taskStatus = $request->task_status;

        if ($id !== null) {
            $listdata = $this->networkWismaListDataLook($id);

            // Ensure $listdata is an object before accessing its properties
            if ($listdata) {
                $oriSeqId = $seqId ?: $listdata->am_seq_id;
                $oriName = $name ?: $listdata->am_data_name;
                $oriGrup = $group ?: $listdata->am_data_group;
                $oriSubGrup = $subgroup ?: $listdata->am_data_subgroup;
                $oriStatus = $taskStatus ?: $listdata->am_data_status;

                if ($request->isMethod("POST")) {
                    $updateData = [
                        'am_seq_id' => $oriSeqId,
                        'am_data_name' => $oriName,
                        'am_data_group' => $oriGrup,
                        'am_data_subgroup' => $oriSubGrup,
                        'am_data_status' => $oriStatus,
                        'am_changed_by' => $user,
                        'am_changed_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_it_support')->table('amtek_data_lookup')
                        ->where('am_data_id', $id)
                        ->update($updateData);
                }
            } else {
                // Handle the case where $listdata is null
                return back()->withErrors('Data not found');
            }

            return back();
        }

        if ($id === null) {
            DB::connection('mysql_ep_it_support')->table('amtek_data_lookup')->insert([
                ['am_seq_id' => $seqId, 'am_data_name' => $name, 'am_data_group' => $group, 'am_data_subgroup' => $subgroup, 'am_data_status' => $taskStatus, 'am_created_by' => $user, 'am_created_date' => Carbon::now()],
            ]);
            return back();
        }

        return $this->networkAmtekDataLookup();
    }

    public function networkAmtekChecklist()
    {
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        $groupName = $this->getNetworkAmtekGroup();
        $listdata = $this->getListNotes($date, 'network_am');
        return view('it_support.network_checklist', [
            'listNote' => $listdata,
            'groupName' => $groupName,
            'dateNote' => $date,
            'user' => $user,
            'message1' => null,
            'section' => 'network_am'
        ]);
    }

    public function networkAmtekChecklistHistory()
    {
        $year = $this->getNetworAmtekkYear();
        return view('it_support.network_checklist_searching_history', [
            'year' => $year,
            'section' => 'network_am'
        ]);
    }

    public function networkBackupLookup()
    {
        $listdata = $this->listNetworkBackUpLookUp();
        return view('it_support.network_backup_data_lookup', [
            'getLookupdate' => $listdata,
        ]);
    }

    public function add_networkBackup_data_lookup(Request $request)
    {
        $user = auth()->user()->first_name;
        $id = $request->task_id;
        $deviceName = $request->device_name;
        $deviceDesc = $request->device_desc;
        $deviceHost = $request->hostname;
        $deviceIp = $request->ip_address;
        $deviceLoc = $request->device_location;
        $deviceStatus = $request->task_status;

        if ($id !== null) {
            $listdata = $this->networkListDataLook($id);

            $oriDevice = $deviceName ?: $listdata->nbl_device;
            $oriDesc = $deviceDesc ?: $listdata->nbl_description;
            $oriHost = $deviceHost ?: $listdata->nbl_hostname;
            $oriIp = $deviceIp ?: $listdata->nbl_ip_address;
            $oriLoc = $deviceLoc ?: $listdata->nbl_location;
            $oriStatus = $deviceStatus ?: $listdata->nbl_status;

            if ($request->isMethod("POST")) {
                $updateData = [
                    'nbl_device' => $oriDevice,
                    'nbl_description' => $oriDesc,
                    'nbl_hostname' => $oriHost,
                    'nbl_ip_address' => $oriIp,
                    'nbl_location' => $oriLoc,
                    'nbl_status' => $oriStatus,
                    'nbl_changed_by' => $user,
                    'nbl_changed_date' => Carbon::now()
                ];

                DB::connection('mysql_ep_it_support')->table('network_backup_lookup')
                    ->where('nbl_data_id', $id)
                    ->update($updateData);
            }

            return back();
        }

        if ($id === null) {
            DB::connection('mysql_ep_it_support')->table('network_backup_lookup')->insert([
                ['nbl_device' => $deviceName, 'nbl_description' => $deviceDesc, 'nbl_ip_address' => $deviceIp, 'nbl_hostname' => $deviceHost, 'nbl_location' => $deviceLoc, 'nbl_status' => $deviceStatus, 'nbl_created_by' => $user, 'nbl_created_date' => Carbon::now()],
            ]);
            return back();
        }

        return $this->networkDataLookup();
    }

    public function networkBackupList($page_location)
    {
        $user = auth()->user()->first_name;
        $currentDate = Carbon::now();
        $date = $currentDate->format("Y-m-d");
        if($page_location === 'wisma'){
            $listdata = $this->getListNotes($date, 'backup_w');
            $listTask = $this->getListTaskWisma($date);
            return view('it_support.network_backup_list_wisma', [
                'listNote' => $listdata,
                'dateNote' => $date,
                'user' => $user,
                'listTask' => $listTask,
                'message1' => null,
                'location' => 'backup_w'
            ]);
        }

        if($page_location === 'cyber'){
            $listdata = $this->getListNotes($date, 'backup_c');
            $listTask = $this->getListTask($date);
            return view('it_support.network_backup_list', [
                'listNote' => $listdata,
                'dateNote' => $date,
                'user' => $user,
                'listTask' => $listTask,
                'message1' => null,
                'location' => 'backup_c'
            ]);
        }
        
        
        
    }

    public function updateNetworkBackupList(Request $request)
    {
        $data = $request->all();
        $user = auth()->user()->first_name;
        foreach ($data as $key => $value) {
            if (strpos($key, 'remarks_') === 0) {
                $nbld_list_id = substr($key, 8);
                $remarks = $value;

                $updateData = [
                    'nbld_remarks' => $remarks,
                    'nbld_changed_by' => $user,
                    'nbld_changed_date' => Carbon::now(),
                ];
                if ($request->has($nbld_list_id)) {
                    $status = $request->input($nbld_list_id);
                    if ($status === 'Y') {
                        $updateData['nbld_checked_ok'] = 'Y';
                        $updateData['nbld_checked_not_ok'] = null;
                    } elseif ($status === 'N') {
                        $updateData['nbld_checked_not_ok'] = 'N';
                        $updateData['nbld_checked_ok'] = null;
                    }
                }
                DB::connection('mysql_ep_it_support')->table('network_backup_list_day')
                    ->where('nbld_list_id', $nbld_list_id)
                    ->update($updateData);
            }
        }

        return back()->with('success', "The daily task 'Network Backup Checklist' has been submit successfully!");
    }

    public function networkBackupChecklistHistory()
    {
        $year = $this->getNetworkBackupYear();
        return view('it_support.network_checklist_searching_history', [
            'year' => $year,
            'section' => 'backup_c'
        ]);
    }

    public function networkBackupWismaChecklistHistory()
    {
        $year = $this->getNetworkBackupYear();
        return view('it_support.network_checklist_searching_history', [
            'year' => $year,
            'section' => 'backup_w'
        ]);
    }
}
