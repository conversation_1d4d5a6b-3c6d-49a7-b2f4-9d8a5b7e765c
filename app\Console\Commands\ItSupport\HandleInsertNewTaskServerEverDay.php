<?php

namespace App\Console\Commands\ItSupport;

use Illuminate\Console\Command;
use App\Services\Traits\ItSupport\ItSupportService;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;

class HandleInsertNewTaskServerEverDay extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ItServer-insert-new-task {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will insert new server task every day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */

     public function handle()
     {
        $date = $this->argument('date') ?? Carbon::now()->toDateString();
        $this->processTasks($date);
     }

    public function processTasks($date) {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $listdata = $this->listActiveTask();
        MigrateUtils::logDump(' Total task to be insert. ' . count($listdata));
        try {
            $checkDataExist = $this->listDataExist();
            if($checkDataExist == null){
                $seq = 1;
            }
            else {
                $seq = $checkDataExist[0]->ack_task_no + 1;
            }
            foreach ($listdata as $list) {

                DB::connection('mysql_ep_it_support')->table('esc_server_job_task')->insert([
                    ['task_date' => $date,
                        'shift_interval' => 'M',
                        'esc_id' => $list->esc_id,
                        'task_name' => $list->esc_name,
                        'task_seq' => $list->esc_seq,
                        'task_status' => 'active',
                        'ack_task_no' => $seq,
                        'task_created_by' => 'AutoSystem',
                        'task_created_date' => Carbon::now(),
                    ],
                ]);
            }
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
        MigrateUtils::logDump(' Done! Completed');
    }

    protected function listActiveTask() {
        $query = "select * from ep_it_support.ep_server_checklist where esc_status = 'active' ";
        $result = DB::connection('mysql_ep_prod_support')->select($query);
        return $result;
    }

    protected function listDataExist() {
        $query = "select ack_task_no from ep_it_support.esc_server_job_task order by ack_task_no desc";
        $result = DB::connection('mysql_ep_prod_support')->select($query);
        return $result;
    }

}
