<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\InvoiceService;
use Guzzle;
use GuzzleHttp\Client;

class UpdateSAPOrderNo {


    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        
        $listData = self::getListEmptySapOrderNoFL();
       
        dump('Total list: '.count($listData));
        foreach($listData as $row){
            dump('DocNo: '.$row->doc_no);
            $flOrderId=$row->fulfilment_order_id;
            $sapOrderNo=$row->sap_order_no;
            $xmlData = self::getXMLUpdateSAPOrderNo($flOrderId,$sapOrderNo);
            dump($xmlData);
            self::triggerPOSTUpdateSAPOrderNo($xmlData);
        }
        
        
        /** By Manual sent **/
        /*$flOrderId = '13398480';
        $sapOrderNo = '1000351539';
        $xmlData = self::getXMLUpdateSAPOrderNo($flOrderId,$sapOrderNo);
        self::triggerPOSTUpdateSAPOrderNo($xmlData);*/
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }

    
    protected static function getXMLUpdateSAPOrderNo($flOrderId,$sapOrderNo) {
        
        /** Skip if null **/
        if($flOrderId == null && strlen($flOrderId)>= 8){
            dump('ERROR: FlOrderId is NULL or not valid :: '.$flOrderId);
            return null;
        }
        if($sapOrderNo == null && strlen($sapOrderNo)>= 10){
            dump('ERROR: SapOrderNo Detail is NULL or not valid :: '.$sapOrderNo);
            return null;
        } 
        $xmlCreateInvoice    = 
        '<soapenv:Envelope '.
                'xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" '. 
                'xmlns:fls="http://www.ep.gov.my/Schema/1-0/FLService" '.
                'xmlns:epmf="http://www.ep.gov.my/Schema/1-0/epmf">'.
                '<soapenv:Header/>'.
                '<soapenv:Body>'.
                        '<fls:EPMFUpdateSAPOrderNoRq>'.
                                '<epmf:RqHeader>'.
                                        '<epmf:ConsumerID>EPP-002</epmf:ConsumerID>'.
                                        '<epmf:UID>'.
                                                '<epmf:RqUID>00000000000000000000000000000000</epmf:RqUID>'.
                                        '</epmf:UID>'.
                                '</epmf:RqHeader>'.
                                '<fls:UpdateSAPOrderNoRq>'.
                                        '<fls:FlOrderId>'.$flOrderId.'</fls:FlOrderId>'.
                                        '<fls:SapOrderNo>'.$sapOrderNo.'</fls:SapOrderNo>'.
                                '</fls:UpdateSAPOrderNoRq>'.
                        '</fls:EPMFUpdateSAPOrderNoRq>'.
                '</soapenv:Body>'.
        '</soapenv:Envelope>';
        
        
        return $xmlCreateInvoice;
    }

    protected static  function triggerPOSTUpdateSAPOrderNo($xmlData) {
        dump('Start triggerPOSTCreateInvoice...');

        $client = new Client([
            'base_uri' => 'http://192.168.63.205:7011',
          ]);
          $payload = $xmlData;
          $response = $client->post('/InternalService/FL/FLService/v1.6', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/FLService/UpdateSAPOrderNo',
            ]
          ]);
          $body = $response->getBody();
          dump($body);
          sleep(1);

    }

    
    protected static function getListEmptySapOrderNoFL(){
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
                    a.fulfilment_order_id,
                    a.doc_no    AS doc_no,
                    o.REMARKS_2 AS sap_order_no
                  FROM fl_fulfilment_order a, fl_workflow_status b, osb_logging o
                  WHERE a.fulfilment_order_id = b.doc_id
                        AND a.DOC_NO = o.REMARKS_1
                        AND b.doc_type IN ('PO', 'CO')
                        AND b.is_current = 1
                        AND a.SAP_ORDER_NO IS NULL
                        AND a.CREATED_DATE >= '01-jan-2018'
                        AND o.service_code = 'EPP-013'
                        AND o.TRANS_TYPE = 'OBRes'
                        AND o.STATUS_CODE = 70022
                        AND o.REMARKS_2 <> 'NA'
                  GROUP BY a.fulfilment_order_id, a.doc_no, o.REMARKS_2");
                
        return $query;
    }
}