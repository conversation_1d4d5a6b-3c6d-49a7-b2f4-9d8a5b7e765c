@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/uom')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    @if ($listdata == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                        <i class="gi gi-search"></i>Carian UOM<br>
                        <small>Masukkan UOM pada carian diatas... (Contoh: gram, unit..dll)</small>
                    @endif
                </h1>
            </div>
        </div>
    @endif

    @if($listdata != null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Carian UOM<br>
                    <small>Masukkan UOM pada carian diatas... (Contoh: gram, unit..dll)</small>
                </h1>
            </div>
        </div>
        <div class="block block-alt-noborder full">
            <div class="text-right">
                <button type="submit" class="btn btn-sm btn-info"><a href="{{ url('/uom/download/all') }}/{{$carian}}"
                    target="_blank"><i class="fa fa-download"></i> Download All UOM</a></button>
            </div>
            <!-- Customer Addresses Block -->
            <div class="block">
                
                <div class="block-title panel-heading epss-title-s1 form-group">
                    <div class="col-md-9">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai UOM </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                    </div>
                    <div class="text-right">
                        <button type="submit" class="btn btn-sm btn-info"><a href="{{ url('/uom/download/searching') }}/{{$carian}}"
                        target="_blank"><i class="fa fa-download"></i> Download</a></button>
                    </div>
                 
                </div>

                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">CODE</th>
                            <th class="text-center">NAME</th>
                            <th class="text-center">DECIMAL SCALE</th>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">CHANGED DATE</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->uom_id }}</td>
                                <td class="text-center">{{ $data->uom_code }}</td>
                                <td class="text-center">{{ $data->uom_name }}</td>
                                <td class="text-center">{{ $data->decimal_scale }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->changed_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection



