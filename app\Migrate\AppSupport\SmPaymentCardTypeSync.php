<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PaymentReceiptService;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmPaymentCardTypeSync {
 
    use PaymentReceiptService;
    use SupplierService;

    /** 
     * Issue on <PERSON><PERSON> , wrongly give the info card type (DEBIT/CREDIT) which is different from MBB Info.  
     * Data will patching on table py_payment and py_payment_order to change card type info
     * THIS PATCHING MUST DO PAYMENT ON SAME DAY ONLY.
    */
    public static function syncPatchingFixCardType($dateFind){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmPaymentCardTypeSync();
        $thisCls->fixIssueCardTypeDebitFromRazer($dateFind);
        $thisCls->fixIssueCardTypeCreditFromRazer($dateFind);
        $thisCls->fixIssueCardTypeEmptyFromRazer($dateFind);
        
        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    /**
     * Issue detect in razer set as DEBIT, but actually is CREDIT. Need to do patching set as CREDIT
     */
    protected function fixIssueCardTypeDebitFromRazer($dateFind) {
        $listCardRazer = $this->listReceiptRazerCardBinNotSameAPIBinCard($dateFind);

        MigrateUtils::logDump(__METHOD__. ' total found : '.count($listCardRazer). '  for date find : '.$dateFind);
        foreach ($listCardRazer as $obj){
            
            $pymntObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)->first();
            
            $pymntOrderObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)->first();
            
            if($pymntObj != null && $pymntOrderObj != null && $pymntOrderObj->channel_name == 'debit'){
                MigrateUtils::logDump(__METHOD__. ' >> data razer : '.json_encode($obj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT : '.json_encode($pymntObj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT_ORDER : '.json_encode($pymntOrderObj));
                MigrateUtils::logDump(__METHOD__. ' >> #### valid to update channel_name to credit');
                
                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)
                    ->update(['service_charge_gst_rate' => 0.9,'card_type' => 5125 ]);

                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)
                    ->update(['channel_name'=>'credit']);

                $pymntObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)->first();
                $pymntOrderObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)->first();
                sleep(3);

                if($pymntObjUpt->card_type == 5125 && $pymntOrderObjUpt->channel_name == 'credit'){
                    DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')->where('order_id',$obj->order_id)
                        ->update(['is_ep_patching'=>1,'is_card_resolved'=>1,'patching_date'=>Carbon::now()]);
                    MigrateUtils::logDump(__METHOD__. ' >> done successfully update '.$obj->order_id.' to as channel_name as CREDIT >> cardtype: '.$pymntObjUpt->card_type . ' channel:'.$pymntOrderObjUpt->channel_name);
                }else{
                    MigrateUtils::logErrorDump(__METHOD__. ' >> failed to update channel_name as CREDIT');
                }
                
                MigrateUtils::logDump(__METHOD__. '  ');
            }else{
                MigrateUtils::logDump(__METHOD__. ' >> #### already updated channel_name to credit');
            }
        }
    }

    /**
     * Issue detect in razer set as CREDIT, but actually is DEBIT. Need to do patching set as DEBIT
     */
    protected function fixIssueCardTypeCreditFromRazer($dateFind) {
        $listCardRazer = $this->listReceiptRazerCardBinNotSameMaybankBinCard($dateFind);

        MigrateUtils::logDump(__METHOD__. ' total found : '.count($listCardRazer). '  for date find : '.$dateFind);
        foreach ($listCardRazer as $obj){
            
            $pymntObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)->first();
            
            $pymntOrderObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)->first();
            
            // Do patching set as DEBIT , after confirm check on bin_mbb_type=='DEBIT' 
            if($pymntObj != null && $pymntOrderObj != null && $pymntOrderObj->channel_name == 'credit' && $obj->bin_mbb_type=='DEBIT'){
                MigrateUtils::logDump(__METHOD__. ' >> data razer : '.json_encode($obj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT : '.json_encode($pymntObj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT_ORDER : '.json_encode($pymntOrderObj));
                MigrateUtils::logDump(__METHOD__. ' >> #### valid to update channel_name to debit');
                
                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)
                    ->update(['service_charge_gst_rate' => 0.5,'card_type' => 5126 ]);

                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)
                    ->update(['channel_name'=>'debit']);

                $pymntObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)->first();
                $pymntOrderObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)->first();
                sleep(3);

                if($pymntObjUpt->card_type == 5126 && $pymntOrderObjUpt->channel_name == 'debit'){
                    DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')->where('order_id',$obj->order_id)
                        ->update(['is_ep_patching'=>1,'is_card_resolved'=>1,'patching_date'=>Carbon::now()]);
                    MigrateUtils::logDump(__METHOD__. ' >> done successfully update '.$obj->order_id.' to as channel_name as DEBIT >> cardtype: '.$pymntObjUpt->card_type . ' channel:'.$pymntOrderObjUpt->channel_name);
                }else{
                    MigrateUtils::logErrorDump(__METHOD__. ' >> failed to update channel_name as DEBIT');
                }
                
                MigrateUtils::logDump(__METHOD__. '  ');
            }else{
                MigrateUtils::logDump(__METHOD__. ' >> #### already updated channel_name to debit');
            }
        }
    }

    /**
     * Issue detect in razer set empty CardType, We need to define from master table ep_bin_info to get  Card Type is DEBIT or CREDIT
     */
    protected function fixIssueCardTypeEmptyFromRazer($dateFind) {
        $listCardRazer = $this->listReceiptRazerCardTypeEmpty($dateFind);

        MigrateUtils::logDump(__METHOD__. ' total found : '.count($listCardRazer). '  for date find : '.$dateFind);
        foreach ($listCardRazer as $obj){
            
            $pymntObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)->first();
            
            $pymntOrderObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)->first();
            
            // found  MBB check already as  DEBIT, check in eP as CREDIT , then we need patch to DEBIT
            if($pymntObj != null && $pymntOrderObj != null && $pymntOrderObj->channel_name == 'credit' && $obj->bin_mbb_type=='DEBIT'){
                MigrateUtils::logDump(__METHOD__. ' >> data razer : '.json_encode($obj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT : '.json_encode($pymntObj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT_ORDER : '.json_encode($pymntOrderObj));
                MigrateUtils::logDump(__METHOD__. ' >> #### valid to update channel_name to debit');
                
                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)
                    ->update(['service_charge_gst_rate' => 0.5,'card_type' => 5126 ]);

                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)
                    ->update(['channel_name'=>'debit']);
                sleep(1);

                $pymntObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)->first();
                $pymntOrderObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)->first();
                

                if($pymntObjUpt->card_type == 5126 && $pymntOrderObjUpt->channel_name == 'debit'){
                    DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')->where('order_id',$obj->order_id)
                        ->update(['is_ep_patching'=>1,'is_card_resolved'=>1,'patching_date'=>Carbon::now()]);
                    MigrateUtils::logDump(__METHOD__. ' >> done successfully update '.$obj->order_id.' to as channel_name as DEBIT >> cardtype: '.$pymntObjUpt->card_type . ' channel:'.$pymntOrderObjUpt->channel_name);
                }else{
                    MigrateUtils::logErrorDump(__METHOD__. ' >> failed to update channel_name as DEBIT');
                }
                
                MigrateUtils::logDump(__METHOD__. '  ');
            }
            // found  MBB check already as  CREDIT, check in eP as DEBIT , then we need  patch to CREDIT
            elseif($pymntObj != null && $pymntOrderObj != null && $pymntOrderObj->channel_name == 'debit' && $obj->bin_mbb_type=='CREDIT'){
                MigrateUtils::logDump(__METHOD__. ' >> data razer : '.json_encode($obj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT : '.json_encode($pymntObj));
                MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT_ORDER : '.json_encode($pymntOrderObj));
                MigrateUtils::logDump(__METHOD__. ' >> #### valid to update channel_name to credit');
                
                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)
                    ->update(['service_charge_gst_rate' => 0.9,'card_type' => 5125 ]);

                DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)
                    ->update(['channel_name'=>'credit']);
                sleep(1);
                $pymntObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')
                    ->where('payment_id',$obj->order_id)->first();
                $pymntOrderObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')
                    ->where('payment_id',$obj->order_id)->first();

                if($pymntObjUpt->card_type == 5125 && $pymntOrderObjUpt->channel_name == 'credit'){
                    DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')->where('order_id',$obj->order_id)
                        ->update(['is_ep_patching'=>1,'is_card_resolved'=>1,'patching_date'=>Carbon::now()]);
                    MigrateUtils::logDump(__METHOD__. ' >> done successfully update '.$obj->order_id.' to as channel_name as CREDIT >> cardtype: '.$pymntObjUpt->card_type . ' channel:'.$pymntOrderObjUpt->channel_name);
                }else{
                    MigrateUtils::logErrorDump(__METHOD__. ' >> failed to update channel_name as CREDIT');
                }
                
                MigrateUtils::logDump(__METHOD__. '  ');
            }
            else{
                MigrateUtils::logDump(__METHOD__. ' >> #### invalid to check');
            }
        }
    }


    /** 
     * Issue on Razer ,card type set as PREPAID.  
     * Data will patching on table py_payment and py_payment_order to change card type as DEBIT. Default will set as CREDIT
    */
    public static function checkAndPatchingCardTypePrepaid($dateFind){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmPaymentCardTypeSync();
        
        $listCardRazer = $thisCls->listReceiptRazerCardBinAsPrepaidType($dateFind);

        MigrateUtils::logDump(__METHOD__. ' total found : '.count($listCardRazer). '  for date find : '.$dateFind);
        foreach ($listCardRazer as $obj){

            // Checking.. If razer set as PREPAID but we check in Binlist API.. this bin number as DEBIT.. then need to changed if eP set as Credit
            // Checking issue on PREPAID and result from BIN LIST is CREDIT but come from UNITED STATE.. then change to DEBIT
            if(
                ($obj->card_type == 'PREPAID' && $obj->bin_type == 'DEBIT') 
                 || ($obj->card_type == 'PREPAID' && $obj->bin_type == 'CREDIT' && $obj->bin_country_code = 'US')  
                ){
                MigrateUtils::logDump(__METHOD__. " card_type = $obj->card_type and bin_type = $obj->bin_type and country_code = $obj->bin_country_code ");
                $thisCls->changeToDebit($obj);
            }elseif($obj->card_type == 'PREPAID'){
                // start on oct 2024  - set force PREPAID as DEBIT
                MigrateUtils::logDump(__METHOD__. " check card_type = $obj->card_type  >>> set to DEBIT as FORCE action");
                $thisCls->changeToDebit($obj);
            }

            
        }
       

        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    private function changeToDebit($obj){
        $pymntObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)->first();
        $pymntOrderObj =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)->first();
        
        if($pymntObj != null && $pymntOrderObj != null && $pymntOrderObj->channel_name == 'credit'){
            MigrateUtils::logDump(__METHOD__. ' >> data razer : '.json_encode($obj));
            MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT : '.json_encode($pymntObj));
            MigrateUtils::logDump(__METHOD__. ' >> data PY_PAYMENT_ORDER : '.json_encode($pymntOrderObj));
            MigrateUtils::logDump(__METHOD__. ' >> #### valid to update channel_name to debit');
            
            DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)
                ->update(['service_charge_gst_rate' => 0.5,'card_type' => 5126 ]);

            DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)
                ->update(['channel_name'=>'debit']);

            $pymntObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT')->where('payment_id',$obj->order_id)->first();
            $pymntOrderObjUpt =   DB::connection('oracle_nextgen_fullgrant')->table('PY_PAYMENT_ORDER')->where('payment_id',$obj->order_id)->first();
            sleep(3);

            if($pymntObjUpt->card_type == 5126 && $pymntOrderObjUpt->channel_name == 'debit'){
                DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')->where('order_id',$obj->order_id)->update(['is_ep_patching'=>1,'patching_date'=>Carbon::now()]);
                MigrateUtils::logDump(__METHOD__. ' >> done successfully update '.$obj->order_id.' to as channel_name as DEBIT >> cardtype: '.$pymntObjUpt->card_type . ' channel:'.$pymntOrderObjUpt->channel_name);
            }else{
                MigrateUtils::logErrorDump(__METHOD__. ' >> failed to update channel_name as DEBIT');
            }
            
            MigrateUtils::logDump(__METHOD__. '  ');
        }else{
            MigrateUtils::logDump(__METHOD__. ' >> #### already updated channel_name to debit');
        }
    }

    public static function syncCardTypeBin(){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmPaymentCardTypeSync();
        $listCardRazer = $thisCls->listPaymentCardRazerNotIntegrationBinCheck();
        foreach ($listCardRazer as $obj){
            //dd($obj);
            
            //$binInfo = $thisCls->getIntegrationBinCheckerCardInfo($obj->card_bin);
            //$thisCls->saveCardInfo($binInfo ,$obj);
            $cardBinNo =$obj->card_bin;
            if(strlen($obj->card_bin) > 6){
                $cardBinNo  = substr($cardBinNo ,0,6);
            }
            $binInfo = $thisCls->getBinCodesInfoInternal( $cardBinNo);
            if($binInfo  == null){
                $binInfoInt = $thisCls->getIntegrationBinCodesCheckerCardInfo( $cardBinNo);
                $thisCls->saveCardInfoFromBinCodes( $cardBinNo,$binInfoInt);
                $binInfo = $thisCls->getBinCodesInfoInternal( $cardBinNo);
            }
            
            $thisCls->saveCardInfoFromBinInternal($binInfo,$obj );
      
        }
       

        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    /** 
     * Source from : sample :https://lookup.binlist.net/537085
     */
    public static function syncCardBinNoFromBinNet(){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmPaymentCardTypeSync();
        $listBinNo = DB::connection('mysql_ep_support')->table('ep_bin_info')->whereNull('bin_net_scheme')->get();
        MigrateUtils::logDump(__METHOD__. ' Total list bin no not updated yet : '.count($listBinNo));
        foreach ($listBinNo as $obj){
            
            $binInfo = $thisCls->getIntegrationBinlistNet($obj->bin_no);
            if($binInfo != null && $binInfo['status'] == 200){
                $thisCls->saveCardInfoFromBinNet($obj->bin_no,$binInfo);
            }
    
        }
       

        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }


    

    /**
     * save-update MBB card type > debit & credit
     */
    public static function syncUpdateMBBCardType() {
        // Checking in 4/7/2025, confirmed by ePP4 that FOREIGNAMEX as CREDIT. not as DEBIT
        $q = "SELECT DISTINCT SUBSTRING(credit_card_number,1,6) AS bin_no ,credit_fee,card_type, 
                IF(credit_fee > 0, 'CREDIT',IF (card_type IN ('FOREIGNAMEX'),'CREDIT','DEBIT'  )) AS card_type
                FROM ep_supplier_payment_maybank WHERE  credit_card_number IS NOT NULL ";
        $result = DB::connection('mysql_ep_support')->select($q);
        foreach($result as $row){
            $dataCardBinMbb = [
                'bin_mbb_type' =>  strtoupper($row->card_type),
                'bin_mbb_fee' =>  $row->credit_fee,
                'changed_at' =>  Carbon::now(),
            ];

            $binObj = DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->where('bin_no',$row->bin_no)->first();
            if($binObj != null){
                if(strlen($binObj->bin_type) > 0 && $binObj->bin_type != strtoupper($row->card_type) ){
                    $dataCardBinMbb['remarks_fix'] = "Detected bin_type ($binObj->bin_type) diff with MBB Carc Type ($row->card_type). Then Updated bin_type as $row->card_type " ;
                    $dataCardBinMbb['bin_type']  = strtoupper($row->card_type) ;
                }
                DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->where('bin_no',$row->bin_no)
                        ->update($dataCardBinMbb);
                
            }else{
                $dataCardBinMbb['bin_no'] = $row->bin_no;
                $dataCardBinMbb['created_at'] = Carbon::now();
                DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->insert($dataCardBinMbb);
            }
            
        }
    }

    /**
     * using API - data apilayer.. not used 
     */
    protected function saveCardInfo($binInfo,$obj) {
        $dataResp = $binInfo->get('data');
        $dataCardBin = [
            'api_card_type' =>  strtoupper($dataResp->get('type')),
            'api_card_scheme' =>  strtoupper($dataResp->get('scheme')),
            'api_card_bank_name' =>  $dataResp->get('bank_name'),
            'api_card_country' =>  $dataResp->get('country'),
            'api_card_url' =>  $dataResp->get('url'),
            'api_last_updated' => Carbon::now(),
            'api_remark' => json_encode($binInfo) 
        ];
        MigrateUtils::logDump(__METHOD__. ' >> '.json_encode($dataCardBin));
        DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')
                        ->where('order_id',$obj->order_id)->where('tran_id',$obj->tran_id)
                        ->update($dataCardBin);
    }

    protected function saveCardInfoFromBinCodes($binNo,$binInfo) {
        $dataResp = $binInfo->get('data');
        $dataCardBin = [
            'bin_no' =>  $binNo,
            'bin_bank' =>  strtoupper($dataResp->get('bank')),
            'bin_card' =>  strtoupper($dataResp->get('card')),
            'bin_type' =>  $dataResp->get('type'),
            'bin_level' =>  $dataResp->get('level'),
            'bin_country' =>  $dataResp->get('country'),
            'bin_country_code' =>  $dataResp->get('countrycode'),
            'bin_website' =>  $dataResp->get('website'),
            'bin_phone' =>  $dataResp->get('phone'),
            'bin_valid' =>  $dataResp->get('valid'),
            'bin_remark' => json_encode($binInfo), 
            'source_bin' => 'www.bincodes.com',
            'created_at' => Carbon::now()
        ];
        MigrateUtils::logDump(__METHOD__. ' >> '.json_encode($dataCardBin));
        DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->insert($dataCardBin);
    }

    protected function saveCardInfoFromBinNet($binNo,$binInfo) {
        $dataResp = $binInfo->get('data');
        $dataCardBin = [
            'bin_net_scheme' =>  strtoupper($dataResp->get('scheme')),
            'bin_net_type' =>  strtoupper($dataResp->get('type')),
            'bin_net_brand' =>   strtoupper($dataResp->get('brand')),
            'bin_net_change_at' => Carbon::now()
        ];
        MigrateUtils::logDump(__METHOD__. ' >> '.json_encode($dataCardBin));
        DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->where('bin_no',$binNo)
                        ->update($dataCardBin);
    }

    protected function saveCardInfoFromBinInternal($binInfo,$obj) {
        $dataCardBin = [
            'bin_bank' =>  $binInfo->bin_bank,
            'bin_card' =>  $binInfo->bin_card,
            'bin_type' =>  $binInfo->bin_type,
            'bin_level' =>  $binInfo->bin_level,
            'bin_country' =>  $binInfo->bin_country,
            'bin_country_code' =>  $binInfo->bin_country_code,
            'bin_website' =>  $binInfo->bin_website,
            'bin_phone' =>  $binInfo->bin_phone,
            'bin_valid' =>  $binInfo->bin_valid,
            'bin_last_updated' => Carbon::now(),
            'bin_remark' => $binInfo->bin_remark
        ];
        MigrateUtils::logDump(__METHOD__. ' >> '.json_encode($dataCardBin));
        DB::connection('mysql_ep_support')->table('ep_supplier_payment_razer')
                        ->where('order_id',$obj->order_id)->where('tran_id',$obj->tran_id)
                        ->update($dataCardBin);
    }

    /**
     * https://api.apilayer.com
     */
    protected function getIntegrationBinCheckerCardInfo($bin) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');

        $collect = collect([]);
		$apiKey = '2tmDOTp95aaxTAxRAc4jaY2aiPCtBhbW';
        $client = new Client([
            'base_uri' => 'https://api.apilayer.com',
          ]);

          $response = $client->get('bincheck/'.$bin, [
            //'debug' => TRUE,
            'headers' => [
                'apikey' => $apiKey ,
            ]
          ]);
          $resultResp = json_decode($response->getBody(), true);
          $collect->put('status',$response->getStatusCode());
          $collect->put('data',collect($resultResp));
          
          MigrateUtils::logDump(__METHOD__. ' result  >>  '.json_encode($collect));
          dump($collect);
          return $collect;
    }

    /**
     * get our database to get info bin card
     */
    protected function getBinCodesInfoInternal($bin) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
		$result = DB::connection('mysql_ep_support')->table('ep_bin_info')
                        ->where('bin_no',$bin)->first();
        return $result;
    }


    /**
     * https://api.bincodes.com
     */
    protected function getIntegrationBinCodesCheckerCardInfo($bin) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');

        $collect = collect([]);
		$apiKey = '0b0546ead26f98f6aeb38c29db6a5217';
        $client = new Client([
            'base_uri' => 'https://api.bincodes.com',
          ]);

          $response = $client->get("/bin/json/$apiKey/$bin/");
          $resultResp = json_decode($response->getBody(), true);
          $collect->put('status',$response->getStatusCode());
          $collect->put('data',collect($resultResp));
          
          MigrateUtils::logDump(__METHOD__. ' result  >>  '.json_encode($collect));
          //dump($collect);
          return $collect;
    }

    /**
     * https://lookup.binlist.net/537085
     */
    protected function getIntegrationBinlistNet($binNo) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');

        $collect = collect([]);
        $client = new Client([
            'base_uri' => 'https://lookup.binlist.net',
          ]);

        $response = $client->get("/$binNo");
        $resultResp = json_decode($response->getBody(), true);
        $collect->put('status',$response->getStatusCode());
        $collect->put('data',collect($resultResp));
        
        MigrateUtils::logDump(__METHOD__. ' result  >>  '.json_encode($collect));
        //dump($collect);
        sleep(10);
        return $collect;
    }

    protected function updatePaymentStuck($listStuckPayment){
        $actionName = 'UpdatePaymentProcess';
        $actionTypeLog = 'Update Table Ep';
        foreach($listStuckPayment as $obj){
            dump($obj);
            if($obj->proceed == 1 ){
                MigrateUtils::logDump($obj->appl_no .' checking');
                $pendingProcessId = $obj->pending_process_id;
                $applId = $obj->appl_id;
                $paymentId = $obj->payment_id;
                if($pendingProcessId && $applId && $paymentId){
                    $parameters =  collect([]);            
                    $parameters->put("pending_process_id", $pendingProcessId);
                    $parameters->put("appl_id", $applId);
                    $parameters->put("payment_id", $paymentId);
                    $parameters->put("action_task", 'Set record_status to 1 at sm_pending_process'); 
                    MigrateUtils::logDump('Pending Process Id : '.$pendingProcessId. ', Appl Id : '. $applId .', Payment Id : '. $paymentId);
                    $update =  DB::connection('oracle_nextgen_fullgrant')
                        ->table('SM_PENDING_PROCESS')->where('pending_process_id',$pendingProcessId)
                            ->where('appl_id',$applId)->where('payment_id',$paymentId)
                        ->update(
                            [
                                'record_status' => 1
                            ] 
                        );
                    MigrateUtils::logDump('Success Update ? '.$update);
                    EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed');
                }
                
            }else{
                MigrateUtils::logDump($obj->appl_no .' >> Please do manual checking... ');
            }     
        }
    }

    
    /**
     * get list Receipt Razer Card BIN with not same API CardBin
     */
    protected function listReceiptRazerCardBinNotSameAPIBinCard($date) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $q = "SELECT
                order_id,
                billing_date,
                tran_id,
                channel,
                amount,
                stat_code,
                card_type,
                card_scheme,
                card_bin,
                bin_bank,
                bin_card,
                bin_type,
                bin_level,
                bin_country,
                free_bin_verify,
                free_bin_type
            FROM
                ep_supplier_payment_razer
            WHERE card_bin IS NOT NULL
                AND bin_card IS NOT NULL
                AND card_type <> bin_type
                AND card_type = 'DEBIT'  
                AND bin_type in ( 'CREDIT' ,'CHARGE CARD')
                AND bin_country NOT IN ('UNITED STATES') -- Agree with shahril 23/6/2012 to exclude USA if razer detected as debit. most previous record always debit.
                AND date(billing_date) > '2022-11-06' -- please do not changed this.. this start auto reconcile card bin start after 6/11/2022
                AND date(billing_date) = ? ";

        $result = DB::connection('mysql_ep_support')->select($q,array($date));

        return $result;
    }

    /**
     * get list Receipt Razer Card as Type Prepaid
     */
    protected function listReceiptRazerCardBinAsPrepaidType($date) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $q = "SELECT
                order_id,
                billing_date,
                tran_id,
                channel,
                amount,
                stat_code,
                card_type,
                card_scheme,
                card_bin,
                bin_bank,
                bin_card,
                bin_type,
                bin_level,
                bin_country,
                free_bin_verify,
                free_bin_type,
                bin_country_code
            FROM
                ep_supplier_payment_razer
            WHERE card_bin IS NOT NULL
                AND card_type = 'PREPAID'  
                AND date(billing_date) >= '2022-12-06' -- please do not changed this.. this start auto reconcile card bin start after 6/12/2022
                AND stat_code ='00'
                AND date(billing_date) = ? ";

        $result = DB::connection('mysql_ep_support')->select($q,array($date));

        return $result;
    }

    /**
     * get list Receipt Razer Card BIN with not same MAYBANK Bin No.
     * This query to find on bin_type as CREDIT only
     */
    protected function listReceiptRazerCardBinNotSameMaybankBinCard($date) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $q = "SELECT
                        i.bin_mbb_type,
                        r.order_id,
                        r.billing_date,
                        r.tran_id,
                        r.channel,
                        r.amount,
                        r.stat_code,
                        r.card_type,
                        r.card_scheme,
                        r.card_bin,
                        r.bin_bank,
                        r.bin_card,
                        r.bin_type,
                        r.bin_level,
                        r.bin_country,
                        r.free_bin_verify,
                        r.free_bin_type
                    FROM
                        ep_supplier_payment_razer r, ep_bin_info i 
                    WHERE  
                    SUBSTRING(r.card_bin,1,6) = i.`bin_no`
                    AND card_type = 'CREDIT' 
                    AND r.`card_type` <> i.bin_mbb_type 
                    AND DATE(billing_date) =  ? ";

        $result = DB::connection('mysql_ep_support')->select($q,array($date));

        return $result;
    }

    /**
     * get list Receipt Razer Card BIN with not same API CardBin
     */
    protected function listReceiptRazerCardTypeEmpty($date) {
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $q = "SELECT
                        i.bin_mbb_type,
                        i.`bin_no`,
                        r.order_id,
                        r.billing_date,
                        r.tran_id,
                        r.channel,
                        r.amount,
                        r.stat_code,
                        r.card_type,
                        r.card_scheme,
                        r.card_bin,
                        r.bin_bank,
                        r.bin_card,
                        r.bin_type,
                        r.bin_level,
                        r.bin_country,
                        r.free_bin_verify,
                        r.free_bin_type
                    FROM
                        ep_supplier_payment_razer r, ep_bin_info i 
                    WHERE  
                    SUBSTRING(r.card_bin,1,6) = i.`bin_no`
		    AND r.card_bin IS NOT NULL
		    AND response_code = '00' 
		    AND card_bin IS NOT NULL
		    AND card_type = '' 
            AND DATE(billing_date) =  ? ";

        $result = DB::connection('mysql_ep_support')->select($q,array($date));

        return $result;
    }

    
    /**
     * Fee RM50
     * Pending Supporting Document Verification (20101).
     */
    protected function getListPaymentStuckFeeProcessing(){
        $dataList = DB::connection('oracle_nextgen_fullgrant')->select(
                "select smp.pending_process_id, smp.payment_id, smp.appl_id, smp.attempt, smp.record_status as process_record_status, smp.created_date, smp.changed_date, smp.err_msg,
                supp.supplier_id, supp.company_name, supp.reg_no, supp.ep_no, supp.record_status,appl.appl_no, supp.company_name,
                pymt.changed_date, pymt.payment_date, pymt.payment_amt,
                (SELECT count(*) as total FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                AND a.status_id = d.status_id
                AND a.appl_id = d.doc_id
                AND a.appl_no = appl.appl_no
                and d.status_id = 20101
                and appl.status_id = 20101
                and w.status_id = 20101 and w.is_current = 1) as proceed
                from SM_PENDING_PROCESS smp, SM_APPL appl, SM_SUPPLIER supp, PY_PAYMENT pymt,PY_BILL bill
                where appl.appl_id=SMP.appl_id
                and supp.supplier_id=appl.supplier_id
                and smp.payment_id=pymt.payment_id 
                AND pymt.BILL_ID = bill.BILL_ID 
                and smp.record_status=8
                and bill.BILL_TYPE  = 'P'
                and SMP.attempt >=3");
        return $dataList;
    }

    /**
     * Fee RM400
     * Registered
     */
    protected function getListPaymentStuckRegistration(){
        $dataList = DB::connection('oracle_nextgen_fullgrant')
                ->select("select smp.pending_process_id, smp.payment_id, smp.appl_id, smp.attempt, smp.record_status as process_record_status, smp.created_date, smp.changed_date, smp.err_msg,
                supp.supplier_id, supp.company_name, supp.reg_no, supp.ep_no, supp.record_status,appl.appl_no, supp.company_name,
                pymt.changed_date, pymt.payment_date, pymt.payment_amt,
                (
                    SELECT count(*) as total 
                    FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                    WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                    AND a.status_id = d.status_id
                    AND a.appl_id = d.doc_id
                    AND a.appl_no = appl.appl_no
                    and d.status_id = 20199
                    and appl.status_id = 20199
                    and w.status_id = 20199 and w.is_current = 1) as proceed
                from SM_PENDING_PROCESS smp, SM_APPL appl, SM_SUPPLIER supp, PY_PAYMENT pymt, PY_BILL bill
                where appl.appl_id=SMP.appl_id
                and supp.supplier_id=appl.supplier_id
                and smp.payment_id=pymt.payment_id
                AND pymt.BILL_ID = bill.BILL_ID 
                and smp.record_status=8
                and bill.BILL_TYPE  = 'R'
                and SMP.attempt >=3");
        return $dataList;
    }

    /**
     * Softcert Payment RM120
     * Get list of invalid stuck on payment softcert. 
     */
    protected function getListPaymentStuckSoftcert(){
        $dataList = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT
                        smp.pending_process_id,	smp.payment_id,	smp.appl_id,smp.attempt,smp.record_status AS process_record_status,smp.created_date,
                        smp.changed_date,smp.err_msg,
                        supp.supplier_id,supp.company_name,supp.reg_no,supp.ep_no,supp.record_status,
                        appl.appl_no,
                        pymt.changed_date,pymt.payment_date,pymt.payment_amt,
                        p.identification_no,p.name,p.user_id,
                        sr.softcert_request_id,sr.softcert_provider, 
                        1 as proceed
                    FROM
                        sm_pending_process smp,
                        sm_appl appl,
                        sm_supplier supp,
                        py_payment pymt,
                        py_bill bill,
                        py_bill_dtl pbd ,
                        sm_personnel p ,
                        sm_softcert_request sr ,
                        py_payment_dtl ppd
                    WHERE
                        appl.appl_id = smp.appl_id
                        AND supp.supplier_id = appl.supplier_id
                        AND smp.payment_id = pymt.payment_id
                        AND pymt.bill_id = bill.bill_id
                        AND bill.bill_id = pbd.bill_id
                        AND pbd.bill_dtl_ref_id = p.personnel_id
                        AND pymt.payment_id = ppd.payment_id
                        AND p.user_id = sr.user_id
                        AND supp.ep_no = sr.ep_no
                        AND ppd.payment_dtl_id = sr.payment_dtl_id
                        AND smp.record_status = 8
                        AND bill.bill_type = 'S'
                        AND smp.attempt >= 3");
        return $dataList;
    }


    /**
     * 22/3/2024 Import list Bin from MBB 
     */
    public static function importFileBinListFromMBB(){
        $filename   = '/app/Migrate/data/CARDLINK_MBPF_20240320.xlsx';
		Excel::load($filename, function($reader){
            $counter = 0;
            $reader->each(function($row) use (&$counter ) {
                $objBin = DB::connection('mysql_ep_support') ->table('ep_bin_info')->where('bin_no',$row->issuer_bin)->first();
                if($objBin == null ){
                    $counter++;
                    MigrateUtils::logDump(__METHOD__. "$counter) ## Not Found : $row->issuer_bin  >> ". json_encode($row));
                }           
            });
        });
        dd('DONE import by excel');
    }
    
}
