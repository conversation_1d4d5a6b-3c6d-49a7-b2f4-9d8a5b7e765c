<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use App\Services\Traits\ProdSupport\PsDefectEpService;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;

class HandleRefreshDefectEpTesting extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ps-refresh-defect-ep-redmine';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will refresh per five minute';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $listdata = $this->listRedmineNumber();
        MigrateUtils::logDump(' Total redmine to be update. ' . count($listdata));
        try {
            $urlRedmine = env("URL_REDMINE", "https://***************");
            $keyClientRedmine = env("KEY_CLIENT_REDMINE", "62d875bd246828ad033b54bf5b39a9a50c3aa1bb");
            
            $client = new \GuzzleHttp\Client([
                'base_uri' => $urlRedmine,
            ]);
            $data = [
                'headers' => [
                    'Content-Type' => 'application/json'
                ],
                'json' => [
                    'key' => $keyClientRedmine
                ],
                'verify' => false
            ];
            if ($listdata) {
                $type = null;
                foreach ($listdata as $obj) {
                    $pathSpecificApi = '/issues/' . $obj->redmine_no . '.json';
                    $urlRedmineApi = $urlRedmine.$pathSpecificApi;
                    MigrateUtils::logDump(' URL Redmine API ' . $urlRedmineApi);
                    $response = $client->request('GET', $pathSpecificApi, $data);
                    $resultResp = json_decode($response->getBody(), true);
                    try {
                        $id = ($resultResp['issue']['id']);
                        $status = ($resultResp['issue']['status']['name']);
                        $author = ($resultResp['issue']['author']['name']);
                        $assigned_to = ($resultResp['issue']['assigned_to']['name']);
                        $module = ($resultResp['issue']['custom_fields'][0]['value']);
                        $edd = ($resultResp['issue']['custom_fields'][16]['value']);
                        $subject = ($resultResp['issue']['subject']);
                        $app = $resultResp['issue']['custom_fields'];
                        $type = null;
                        foreach ($app as $key => $try) {
                            $try = $key;
                            if ($app[$key]['name'] == 'Component') {
                                $defect_type = ($app[$key]['value']);
                                if (count($defect_type) > 1) {
                                    $type = $defect_type[0] . ',' . $defect_type[1];
                                } else {
                                    if(isset($defect_type[0])){
                                        $type = $defect_type[0];
                                    }
                                }
                            }
                        }
                        $dev = $resultResp['issue']['custom_fields'];
                        $devlist = null;
                        foreach ($dev as $key => $try) {
                            $try = $key;
                            if ($dev[$key]['name'] == 'Developer') {
                                $devlist = ($dev[$key]['value']);
                            }
                        }
                        if ($status == '09 - Closed in SIT') {
                            $updateData = [
                                'redmine_status' => $status,
                                'raised_by' => $author,
                                'assignee' => $assigned_to,
                                'module' => $module,
                                'edd' => $edd,
                                'developer' => $devlist,
                                'type_defect' => $type,
                                'subject' => $subject,
                                'deploy_status' => null,
                            ];
                            DB::connection('mysql_ep_prod_support')->table('ps_defect_ep')
                                    ->where('redmine_no', $id)
                                    ->update($updateData);
                        } else if ($status == '12 - Closed') {
                            $updateData = [
                                'redmine_status' => $status,
                                'raised_by' => $author,
                                'assignee' => $assigned_to,
                                'module' => $module,
                                'edd' => $edd,
                                'developer' => $devlist,
                                'type_defect' => $type,
                                'subject' => $subject,
                                'test_status' => 'Closed',
                            ];
                            DB::connection('mysql_ep_prod_support')->table('ps_defect_ep')
                                    ->where('redmine_no', $id)
                                    ->update($updateData);
                        } else {
                            $updateData = [
                                'redmine_status' => $status,
                                'raised_by' => $author,
                                'assignee' => $assigned_to,
                                'module' => $module,
                                'edd' => $edd,
                                'developer' => $devlist,
                                'type_defect' => $type,
                                'subject' => $subject,
                            ];
                            DB::connection('mysql_ep_prod_support')->table('ps_defect_ep')
                                    ->where('redmine_no', $id)
                                    ->update($updateData);
                        }
                    } catch (\Exception $exc) {
                        MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
                        MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
                        MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
                    }
                }
            }
        } catch (\Exception $ex) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $ex->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($ex->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $ex->getTraceAsString());
        }
        MigrateUtils::logDump(' Done! Completed');
    }

    protected function listRedmineNumber() {
        $query = "select redmine_no from ps_defect_ep where redmine_status != '12 - Closed' ";
        $result = DB::connection('mysql_ep_prod_support')->select($query);
        if (count($result) > 0) {
            return $result;
        }
        return null;
    }

}
