@extends('layouts.guest-dash')

@section('cssprivate')
<meta name="csrf-token" content="{{ csrf_token() }}">
@endsection

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/ap511-check-invoice/dashboard') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/ap511-check-invoice/list') }}">Invoice List</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
@if (Auth::user())
    <div class="row">
        <div class="col-lg-12 text-right">
            <h5><strong>Requested on :</strong> {{Carbon\Carbon::now()->format('d-M-Y H:i:s')}}</h5>   
        </div>
    </div>

    <!-- Filter Section -->
    <div class="row push-20-t">
        <div class="col-sm-12">
            <div class="block block-themed">
                <div class="block-header bg-primary-dark">
                    <h3 class="block-title">
                        <i class="si si-equalizer"></i> Filter Options
                    </h3>
                </div>
                <div class="block-content">
                    <div class="row items-push">
                        <div class="col-sm-6 col-lg-3">
                            <label for="filterYear">Year:</label>
                            <select id="filterYear" name="year" class="form-control">
                                @foreach($availableYears as $availableYear)
                                    <option value="{{ $availableYear }}" {{ $availableYear == $year ? 'selected' : '' }}>
                                        {{ $availableYear }}
                                    </option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-sm-6 col-lg-3">
                            <label for="filterMonth">Month:</label>
                            <select id="filterMonth" name="month" class="form-control">
                                <option value="">All Months</option>
                                @for($i = 1; $i <= 12; $i++)
                                    <option value="{{ $i }}" {{ $i == $month ? 'selected' : '' }}>
                                        {{ Carbon\Carbon::create()->month($i)->format('F') }}
                                    </option>
                                @endfor
                            </select>
                        </div>
                        <div class="col-sm-12 col-lg-3">
                            <label>&nbsp;</label>
                            <button type="button" id="applyFilter" class="btn btn-success btn-block">
                                <i class="fa fa-search"></i> Apply Filter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row items-push">
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-primary">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="gi gi-file_txt fa-3x text-white-op"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="totalInvoices" class="h2">{{ number_format($statistics['total_invoices']) }}</span>
                        </div>
                        <div class="text-white-op">Total Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-success">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="si si-check fa-3x text-white-op"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="ap511Invoices" class="h2">{{ number_format($statistics['ap511_invoices']) }}</span>
                        </div>
                        <div class="text-white-op">AP511 Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-danger">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="si si-close fa-3x text-white-op"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="nonAp511Invoices" class="h2">{{ number_format($statistics['non_ap511_invoices']) }}</span>
                        </div>
                        <div class="text-white-op">Non-AP511 Invoices</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-6 col-lg-3">
            <div class="block block-themed block-transparent bg-info">
                <div class="block-content">
                    <div class="py-20 text-center">
                        <div class="fa fa-percent fa-3x text-white-op"></div>
                        <div class="font-w600 text-white push-15-t push-15">
                            <span id="ap511Percentage" class="h2">
                                {{ $statistics['total_invoices'] > 0 ? number_format(($statistics['ap511_invoices'] / $statistics['total_invoices']) * 100, 1) : 0 }}%
                            </span>
                        </div>
                        <div class="text-white-op">AP511 Percentage</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Breakdown -->
    <div class="row">
        <div class="col-sm-12">
            <div class="block block-themed">
                <div class="block-header bg-gray-darker">
                    <h3 class="block-title">
                        <i class="si si-bar-chart"></i> Monthly Breakdown
                    </h3>
                </div>
                <div class="block-content">
                    <div class="table-responsive">
                        <table class="table table-striped table-vcenter">
                            <thead>
                                <tr class="active">
                                    <th class="text-center font-w600">Month</th>
                                    <th class="text-center font-w600">Total Invoices</th>
                                    <th class="text-center font-w600">AP511 Invoices</th>
                                    <th class="text-center font-w600">Non-AP511 Invoices</th>
                                    <th class="text-center font-w600">AP511 %</th>
                                </tr>
                            </thead>
                            <tbody id="monthlyBreakdownTable">
                                @foreach($statistics['monthly_breakdown'] as $monthData)
                                <tr>
                                    <td class="text-center font-w600 text-primary">{{ $monthData['month_name'] }}</td>
                                    <td class="text-center">
                                        <span class="badge badge-primary">{{ number_format($monthData['total']) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-success">{{ number_format($monthData['ap511']) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge badge-danger">{{ number_format($monthData['non_ap511']) }}</span>
                                    </td>
                                    <td class="text-center">
                                        <span class="font-w600 text-{{ $monthData['total'] > 0 && ($monthData['ap511'] / $monthData['total']) * 100 > 50 ? 'success' : 'warning' }}">
                                            {{ $monthData['total'] > 0 ? number_format(($monthData['ap511'] / $monthData['total']) * 100, 1) : 0 }}%
                                        </span>
                                    </td>
                                </tr>
                                @endforeach
                                @if(count($statistics['monthly_breakdown']) == 0)
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-20">
                                        <i class="fa fa-info-circle"></i> No data available for the selected period
                                    </td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endif
@endsection

@section('jsprivate')
<script>
$(document).ready(function() {
    console.log('Dashboard JavaScript loaded');
    
    // Test if jQuery is working
    console.log('jQuery version:', $.fn.jquery);
    
    // Check if elements exist
    console.log('Apply filter button exists:', $('#applyFilter').length);
    console.log('Filter year exists:', $('#filterYear').length);
    console.log('Filter month exists:', $('#filterMonth').length);
    
    // Apply filter button click event
    $('#applyFilter').click(function(e) {
        e.preventDefault();
        console.log('Apply filter button clicked');
        var year = $('#filterYear').val();
        var month = $('#filterMonth').val();
        console.log('Year:', year, 'Month:', month);
        
        // Show loading
        $('body').append('<div id="loading" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999;"><div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 18px;"><i class="fa fa-spinner fa-spin"></i> Loading...</div></div>');
        
        // Make AJAX request to get updated statistics
        $.ajax({
            url: '{{ url("/ap511-check-invoice/dashboard-data") }}',
            type: 'GET',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            data: {
                year: year,
                month: month
            },
            success: function(response) {
                if (response.success) {
                    var data = response.data;
                    
                    // Update summary cards
                    $('#totalInvoices').text(numberWithCommas(data.total_invoices));
                    $('#ap511Invoices').text(numberWithCommas(data.ap511_invoices));
                    $('#nonAp511Invoices').text(numberWithCommas(data.non_ap511_invoices));
                    
                    var percentage = data.total_invoices > 0 ? (data.ap511_invoices / data.total_invoices * 100) : 0;
                    $('#ap511Percentage').text(percentage.toFixed(1) + '%');
                    
                    // Update monthly breakdown table
                    var tableBody = $('#monthlyBreakdownTable');
                    tableBody.empty();
                    
                    if (Object.keys(data.monthly_breakdown).length === 0) {
                        var emptyRow = '<tr>' +
                            '<td colspan="5" class="text-center text-muted py-20">' +
                            '<i class="fa fa-info-circle"></i> No data available for the selected period' +
                            '</td>' +
                            '</tr>';
                        tableBody.append(emptyRow);
                    } else {
                        $.each(data.monthly_breakdown, function(index, monthData) {
                            var monthPercentage = monthData.total > 0 ? (monthData.ap511 / monthData.total * 100) : 0;
                            var percentageClass = monthPercentage > 50 ? 'success' : 'warning';
                            var row = '<tr>' +
                                '<td class="text-center font-w600 text-primary">' + monthData.month_name + '</td>' +
                                '<td class="text-center"><span class="badge badge-primary">' + numberWithCommas(monthData.total) + '</span></td>' +
                                '<td class="text-center"><span class="badge badge-success">' + numberWithCommas(monthData.ap511) + '</span></td>' +
                                '<td class="text-center"><span class="badge badge-danger">' + numberWithCommas(monthData.non_ap511) + '</span></td>' +
                                '<td class="text-center"><span class="font-w600 text-' + percentageClass + '">' + monthPercentage.toFixed(1) + '%</span></td>' +
                                '</tr>';
                            tableBody.append(row);
                        });
                    }
                }
            },
            error: function(xhr, status, error) {
                console.log('AJAX Error:', error);
                console.log('Status:', status);
                console.log('Response:', xhr.responseText);
                alert('Error loading data. Please check console for details.');
            },
            complete: function() {
                $('#loading').remove();
            }
        });
    });
    
    // Helper function to format numbers with commas
    function numberWithCommas(x) {
        return x.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
    }
});
</script>
@endsection