<?php

namespace App\Http\Middleware;

// First copy this file into your middleware directoy
use Closure;
use Log;
use Auth;
use DB;
use Carbon\Carbon;

class ActivityAccess {

    static $ignorePath = [
        'login',
        'home',
        'support/task',
        'crm/',
        'find/trans/docno/insert_docno',
        'find/identity/111111002222',
        'find/gfmas/apive/insert_epno',
    ];
    
    public function handle($request, Closure $next) {
        if (Auth::user()) {
            $this->saveLogActivityAccess($request);
        }
        
        return $next($request);
    }

    private function saveLogActivityAccess($request) {

        $path = $request->path();
        foreach ($this::$ignorePath as $value){
            if (strpos($path, $value) !== false) {
                return;
            }
        }
        
        //Log::info(self::class . ' ' . __FUNCTION__ . 'url :  ' . $request->path());
        //Log::info(self::class . ' ' . __FUNCTION__ . '#fullUrl :  '.$request->fullUrl());
        try {
            DB::connection('mysql_ep_support')
                ->insert('insert into ep_activity_access  
                    (user_id,username,access_url,access_detail,created_at) 
                    values (?, ?, ?, ?, ?)', 
                    [   
                        Auth::user()->id, 
                        Auth::user()->user_name,
                        $request->path(),
                        $request->fullUrl(),
                        Carbon::now()  
                    ]);
        } catch (\Exception $e) {
            Log::error(__METHOD__.' >>   : '.$e->getMessage());
            Log::error(__METHOD__.' >> path '.$request->fullUrl().'   : '.json_encode($request->all()));
        }
        
    }

}
