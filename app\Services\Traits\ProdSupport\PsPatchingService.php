<?php

namespace App\Services\Traits\ProdSupport;

use Log;
use DB;
use Carbon\Carbon;
use Hash;

trait PsPatchingService
{

    protected function getDetailDataFixPorting($dataFixId)
    {
        return DB::connection('mysql_ep_prod_support')
            ->table('ps_data_fix')
            ->where('data_fix_id', $dataFixId)
            ->first();
    }

    protected function getListDetailsDataPatch($dataFixId)
    {
        return DB::connection('mysql_ep_prod_support')
            ->table('ps_data_fix_dtl')
            ->where('data_fix_id', $dataFixId)
            ->get();
    }

    protected function getDetailCRCreationForRepatch($dataFixDetailId)
    {
        return DB::connection('mysql_ep_prod_support')
            ->table('ps_change_request')
            ->where('data_fix_dtl_id', $dataFixDetailId)
            ->first();
    }

    protected function getDetailScriptForRepatch($dataFixDetailId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
        select * from ps_patch_script pps where data_fix_dtl_id = ?
        ", array($dataFixDetailId));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getDetailsDataPatch($dataFixDetailId)
    {
        $data = DB::connection('mysql_ep_prod_support')
            ->table('ps_data_fix_dtl')
            ->where('data_fix_dtl_id', $dataFixDetailId)
            ->first();
        if ($data) {
            $data->module_name = null != $data->module ? $this->getDetailLookup($data->module)->name : null;
            $data->module_code = null != $data->module ? $this->getDetailLookup($data->module)->code : null;
            $data->problem_description_name = null != $data->problem_description ? $this->getDetailLookup($data->problem_description)->name : null;
            $data->requester_type_name = null != $data->requester_type ? $this->getDetailLookup($data->requester_type)->name : null;
            $data->problem_type_name = null != $data->problem_type ? $this->getDetailLookup($data->problem_type)->name : null;
        }
        return $data;
    }

    protected function getDetailsChangeRequest($dataFixDetailId)
    {
        return DB::connection('mysql_ep_prod_support')
            ->table('ps_change_request')
            ->where('data_fix_dtl_id', $dataFixDetailId)
            ->first();
    }

    protected function getListDetailsPatchScript($dataFixDetailId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_patch_script where data_fix_dtl_id = ?
        ", array($dataFixDetailId));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getIdForDeleteDetails($dataFixDtlId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select distinct pdfd.data_fix_dtl_id, pcr.data_fix_dtl_id as pcrid,pps.data_fix_dtl_id as ppsid
         from ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           where pdfd.data_fix_dtl_id = ?
        ", array($dataFixDtlId));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getIdForDeletePorting($data)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select pdfd.data_fix_dtl_id, pcr.data_fix_dtl_id as pcrid,pps.data_fix_dtl_id as ppsid
         from ps_data_fix pdf
          left join ps_data_fix_dtl pdfd on pdfd.data_fix_id = pdf.data_fix_id
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           where pdf.data_fix_id = ?
        ", array($data));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getDetailLookup($lookupId)
    {
        return DB::connection('mysql_ep_prod_support')
            ->table('ps_lookup')
            ->where('id', $lookupId)
            ->first();
    }

    protected function getListLookupGroupTypeById($groupType, $lookupId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           SELECT ps.id, ps.name 
            FROM ps_lookup ps, ps_lookup ps1
            WHERE ps.group_type = ?  
            AND ps1.code = ps.code
            AND ps1.id = ? 
        ", array($groupType, $lookupId));
        return $query;
    }

    /**
     * 
     * @param Collection $dataFixPorting
     * @return type
     */
    protected function getListAllData($byDate, $byCRM, $byModule, $byCompany)
    {


        $query = "
         SELECT DISTINCT 
                pdfd.data_fix_dtl_id as aid, pdf.porting_seq , pdf.name ,pdfd.datetime_porting ,pdfd.crm_no ,pdfd.redmineno, pl.name as module , pl2.name as Problem_Description,
                pl4.name as Requester_Type,pdfd.requester_name , pl3.name as Problem_Type, pdfd.endorsement_by,pdfd.created_by, pdfd.created_date,
                pdfd.changed_by,pdfd.changed_date, pdf.name as name_port, pdf.status, pdfd.status AS patchstatus, pcr.status AS CRstatus, pps.status AS scriptstatus, pdf.closed_by , pdf.data_fix_id as datafixid, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
           WHERE pdfd.data_fix_id = pdf.data_fix_id 
           AND (pdfd.module = pl.id)
           AND (pdfd.problem_description = pl2.id)
           AND (pdfd.requester_type = pl3.id)
           AND (pdfd.problem_type = pl4.id)
           and (pdf.name like 'Porting at 13 PM' or pdf.name like 'Data Fix @ 1 PM %') ";
           $params_date = array();
           if($byDate != null){
            $query = $query. " and date_format(pdfd.datetime_porting,'%Y-%m-%d') = '".$byDate."'" ;
            array_push($params_date,$byDate);
           }
           $params_crm = array();
           if($byCRM != null){
            $query = $query. " and pdfd.crm_no = ". $byCRM ;
            array_push($params_crm,$byCRM);
           }
           $params_module = array();
           if($byModule != null){
            $query = $query. " and pl.id = ".$byModule ;
            array_push($params_module,$byModule);
           }
           $params_name = array();
           if($byCompany != null){
            $query = $query. " and pdfd.requester_name like '". $byCompany ."'" ;
            array_push($params_name,$byCompany);
           }
           $query = $query." Order by pdf.data_fix_id desc";
        $res = DB::connection('mysql_ep_prod_support')->select( $query, $params_date,$params_crm,$params_module,$params_name);
        return $res;
        
    }

    protected function getListAllDatasixpm($byDate, $byCRM, $byModule, $byCompany)
    {
        $query = "
        SELECT DISTINCT 
                pdfd.data_fix_dtl_id as aid, pdf.porting_seq , pdf.name ,pdfd.datetime_porting ,pdfd.crm_no ,pdfd.redmineno, pl.name as module , pl2.name as Problem_Description,
                pl4.name as Requester_Type,pdfd.requester_name , pl3.name as Problem_Type, pdfd.endorsement_by,pdfd.created_by, pdfd.created_date,
                pdfd.changed_by,pdfd.changed_date, pdf.name as name_port, pdf.status, pdfd.status AS patchstatus, pcr.status AS CRstatus, pps.status AS scriptstatus, pdf.closed_by , pdf.data_fix_id as datafixid, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
           WHERE pdfd.data_fix_id = pdf.data_fix_id 
           AND (pdfd.module = pl.id)
           AND (pdfd.problem_description = pl2.id)
           AND (pdfd.requester_type = pl3.id)
           AND (pdfd.problem_type = pl4.id)
           and (pdf.name like 'Porting at 13 PM' or pdf.name like 'Data Fix @ 6 PM %')";
           $params_date = array();
           if($byDate != null){
            $query = $query. " and date_format(pdfd.datetime_porting,'%Y-%m-%d') = '".$byDate."'" ;
            array_push($params_date,$byDate);
           }
           $params_crm = array();
           if($byCRM != null){
            $query = $query. " and pdfd.crm_no = ". $byCRM ;
            array_push($params_crm,$byCRM);
           }
           $params_module = array();
           if($byModule != null){
            $query = $query. " and pl.id = ".$byModule ;
            array_push($params_module,$byModule);
           }
           $params_name = array();
           if($byCompany != null){
            $query = $query. " and pdfd.requester_name like '". $byCompany ."'" ;
            array_push($params_name,$byCompany);
           }

           $query = $query. " Order by pdf.data_fix_id desc";
        
       $res= DB::connection('mysql_ep_prod_support')->select( $query, $params_date,$params_crm,$params_module,$params_name);
        return $res;
    }

    protected function getListAllDataurgent($byDate, $byCRM, $byModule, $byCompany)
    {
        $query ="
         SELECT DISTINCT 
                pdfd.data_fix_dtl_id as aid, pdf.porting_seq , pdf.name ,pdfd.datetime_porting ,pdfd.crm_no ,pdfd.redmineno, pl.name as module , pl2.name as Problem_Description,
                pl4.name as Requester_Type,pdfd.requester_name , pl3.name as Problem_Type, pdfd.endorsement_by,pdfd.created_by, pdfd.created_date,
                pdfd.changed_by,pdfd.changed_date, pdf.name as name_port, pdf.status, pdfd.status AS patchstatus, pcr.status AS CRstatus, pps.status AS scriptstatus, pdf.closed_by , pdf.data_fix_id as datafixid, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
           WHERE pdfd.data_fix_id = pdf.data_fix_id 
           AND (pdfd.module = pl.id)
           AND (pdfd.problem_description = pl2.id)
           AND (pdfd.requester_type = pl3.id)
           AND (pdfd.problem_type = pl4.id)
           and pdf.name like '%Urgent%'";
           $params_date = array();
           if($byDate != null){
            $query = $query. " and date_format(pdfd.datetime_porting,'%Y-%m-%d') = '".$byDate."'" ;
            array_push($params_date,$byDate);
           }
           $params_crm = array();
           if($byCRM != null){
            $query = $query. " and pdfd.crm_no = ". $byCRM ;
            array_push($params_crm,$byCRM);
           }
           $params_module = array();
           if($byModule != null){
            $query = $query. " and pl.id = ".$byModule ;
            array_push($params_module,$byModule);
           }
           $params_name = array();
           if($byCompany != null){
            $query = $query. " and pdfd.requester_name like '". $byCompany ."'" ;
            array_push($params_name,$byCompany);
           }
           $query = $query. " Order by pdf.data_fix_id desc";
      $res= DB::connection('mysql_ep_prod_support')->select( $query, $params_date,$params_crm,$params_module,$params_name);
        return $res;
    }

    protected function getDataLookup()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_lookup where status = 'Created' or status = 'Updated'
           
        ", array());
        return $query;
    }

    protected function getApproverUserList()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select data_fix_user_id,full_name, email, record_status, is_manager_datafix,is_ep_endorsed ,is_group_cc,is_ep_group_cc,is_mo_group,
         case when is_manager_datafix = 1  then 'Manager DataFix' end as manager,
         case when is_ep_endorsed = 1 then 'Endorsement Officer' end as ep_endorser,
         case when is_group_cc = 1 then 'Email CC Group' end as email_cc_group,
         case when is_ep_group_cc = 1 then 'Email eP CC Group' end as email_eP_cc_group,
         case when is_mo_group = 1 then 'Email eP MO Group' end as email_mo_group,
         case when record_status = 1 then 'Active' else 'Inactive' end as record
         from ps_data_fix_user
           
        ", array());
        return $query;
    }

    protected function getManagerDataFix()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select full_name from ps_data_fix_user where is_manager_datafix = 1;
           
        ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getEditedApprover($id)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_data_fix_user where data_fix_user_id = ?
           
        ", array($id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDistinctGroupType()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select distinct group_type from ps_lookup
           
        ", array());
        return $query;
    }

    protected function getEditedDataLookup($id)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select * from ps_lookup where id = ?
           
        ", array($id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDataModuleFromCRM($modulename)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select id from ps_lookup where group_type = 'Module' and name = ?
        ", array($modulename));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDataModule()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select id, name, code from ps_lookup where group_type = 'Module' and (status = 'Created' or  status = 'Updated') order by name
        ", array());
        return $query;
    }

    protected function getProblemType()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select id, name from ps_lookup where group_type = 'Problem Type' and status = 'Created' order by name
        ", array());
        return $query;
    }

    protected function getRequesterType()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select id,name from ps_lookup where group_type = 'Requester Type'  and status = 'Created' order by name
        ", array());
        return $query;
    }

    protected function getProblemdescription($portmodule)
    {
        if($portmodule == 230){
            $query = DB::connection('mysql_ep_prod_support')->select("
            select ps.id, ps.name, 'PPD' as code 
            from ps_lookup ps
            where ps.group_type = 'Problem Description'  
            order by name
           ", array($portmodule));
        } 
        else {$query = DB::connection('mysql_ep_prod_support')->select("
            select ps.id, ps.name, ps.code 
            from ps_lookup ps, ps_lookup ps1
            where ps.group_type = 'Problem Description'  
            and ps1.group_type = 'Module'
            and ps1.code = ps.code
            and ps1.id = ?
            order by name
           ", array($portmodule));}
        
        return $query;
    }

    protected function getProblemdescForChange($probid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select ps.name 
         from ps_lookup ps
         where ps.group_type = 'Problem Description'
         and ps.id = ?
         order by name
        ", array($probid));
        return $query;
    }

    protected function getOpenPatchDetail()
    {
        $query = DB::connection('mysql_ep_prod_support')->select(
            "select * from ps_data_fix 
            where date(datetime_porting) = date(NOW()) 
            and type_porting = 'U'
            and status = 'Open'
            order by data_fix_id desc",
            array()
        );
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getClosedPatchDetail()
    {
        $query = DB::connection('mysql_ep_prod_support')->select(
            "select * from ps_data_fix 
            where date(datetime_porting) = date(NOW()) 
            and type_porting = 'U'
            and status = 'Closed'
            order by data_fix_id desc",
            array()
        );
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getPatchDetailScheduledNormalByToday()
    {
        $query = DB::connection('mysql_ep_prod_support')->select(
            "select * from ep_prod_support.ps_data_fix 
            where 
            type_porting = 'S'
            and date(datetime_porting) = date(now()) 
            order by data_fix_id desc ",
            array()
        );
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getOpenPatchDetailScheduledByToday()
    {
        $query = DB::connection('mysql_ep_prod_support')->select(
            "select * from ep_prod_support.ps_data_fix 
            where 
            type_porting = 'S'
            and status in ('Open','Submit') 
            and date(datetime_porting) = date(now()) 
            order by datetime_porting desc ",
            array()
        );
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getOpenPatchDetailScheduledforSixPm()
    {
        $query = DB::connection('mysql_ep_prod_support')->select(
            "select * from ep_prod_support.ps_data_fix 
            where (TIME(datetime_porting) > '13:00:00' and TIME(datetime_porting) <= '18:00:00')
            and (status = 'Open' or status = 'Submit')
            and type_porting = 'S'
            and date(datetime_porting) = curdate()   ",
            array()
        );
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMaxSeq()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select max(porting_seq) as seq from ep_prod_support.ps_data_fix where date(datetime_porting) = curdate()  
        ", array());
        $seqObj = $query[0];
        if ($seqObj->seq != null) {
            return $seqObj->seq;
        }
        return 0;
    }

    protected function getMaxSeqFixId()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select max(data_fix_id) as port from ep_prod_support.ps_data_fix where date(datetime_porting) = date(NOW()) and type_porting = 'S'
        ", array());
        return $query[0];
    }

    protected function getMaxSeqFixIdUrgent()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select max(data_fix_id) as port from ep_prod_support.ps_data_fix where date(datetime_porting) = date(NOW()) and type_porting = 'U'
        ", array());
        return $query[0];
    }

    protected function getMaxBillSeq()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select max(bill_seq) as bill 
            from ep_prod_support.ps_data_fix_dtl pdfd,  ep_prod_support.ps_data_fix pdf 
            where pdf.data_fix_id = pdfd.data_fix_id 
            and date(pdf.datetime_porting) = date(NOW())
            and pdf.type_porting = 'S'
            and pdf.status ='Open'
        ", array());
        return $query[0];
    }

    protected function getMaxBillSeqUrgent()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select max(bill_seq) as bill 
            from ep_prod_support.ps_data_fix_dtl pdfd,  ep_prod_support.ps_data_fix pdf 
            where pdf.data_fix_id = pdfd.data_fix_id 
            and date(pdf.datetime_porting) = date(NOW())
            and pdf.status ='Open'
            and pdf.type_porting = 'U'
        ", array());
        return $query[0];
    }

    protected function getMaxDataFixDetailId()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
        select max(data_fix_dtl_id) as data_fix_dtl_id 
        from ep_prod_support.ps_data_fix_dtl pdfd,  ep_prod_support.ps_data_fix pdf 
        where pdf.data_fix_id = pdfd.data_fix_id 
        and date(pdf.datetime_porting) = date(NOW())
        and pdf.status ='Open'
        ", array());
        return $query[0];
    }

    protected function listDataOpenPatchforUrgent()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            SELECT DISTINCT 
                pdf.data_fix_id,pdf.datetime_porting AS dateport,pdf.porting_seq,pdf.status,
                pdf.is_sent, pdfd.data_fix_dtl_id,pdfd.sent_helpdesk,
                case when pdf.urgent_type = 'external' then 'External' 
                when pdf.urgent_type = 'internal' then 'Internal' end as urgent_type,
                pdf.created_by,pdf.created_date,pdf.changed_date,pdf.closed_by,
                pdfd.status AS patchstatus,pdfd.bill_seq,pdfd.data_fix_dtl_id,  
                pdfd.crm_no,pdfd.redmineno,pdfd.created_date AS createdate, pdfd.endorsement_by, pdfd.endorsement_date,
                pdfd.created_date AS dtl_created_date,pdfd.created_by AS dtl_created_by,
                pcr.status AS CRstatus, 
                pps.status AS scriptstatus,pdf.name, 
                pl.name AS module,pl2.name AS prob_desc ,
                pl3.name AS requester,pdfd.requester_name, 
                pl4.name AS probtype, pdfd.created_by AS createdby, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
            where pdfd.data_fix_id = pdf.data_fix_id 
            and (pdfd.module = pl.id)
            and (pdfd.problem_description = pl2.id)
            and (pdfd.requester_type = pl3.id)
            and (pdfd.problem_type = pl4.id)
            and pdf.type_porting = 'U'
            and (pdf.status = 'Open') 
            and pdfd.status <> 'Cancelled'
            and date(pdf.datetime_porting) = date(NOW()) 
            ORDER BY pdfd.created_date desc
        ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataPatchNormalByToday()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         SELECT DISTINCT 
                pdf.data_fix_id,pdf.datetime_porting AS dateport,pdf.porting_seq,pdf.status,
                pdf.is_sent,
                pdf.created_by,pdf.created_date,
                pdf.changed_date,pdf.closed_by,
                pdfd.status AS patchstatus,pdfd.bill_seq,pdfd.data_fix_dtl_id,  
                pdfd.crm_no,pdfd.redmineno,pdfd.created_date AS createdate, pdfd.endorsement_by, pdfd.endorsement_date,
                pdfd.created_date AS dtl_created_date,pdfd.created_by AS dtl_created_by,
                pdfd.sent_helpdesk,
                pdfd.sent_helpdesk_date,
                pdfd.remark_helpdesk,
                pcr.status AS CRstatus, 
                pps.status AS scriptstatus,pdf.name, 
                pl.name AS module,pl2.name AS prob_desc ,
                pl3.name AS requester,pdfd.requester_name, 
                pl4.name AS probtype, pdfd.created_by AS createdby, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
           WHERE pdfd.data_fix_id = pdf.data_fix_id 
           AND (pdfd.module = pl.id)
           AND (pdfd.problem_description = pl2.id)
           AND (pdfd.requester_type = pl3.id)
           AND (pdfd.problem_type = pl4.id)
           AND pdf.type_porting <> 'U'
           AND DATE(pdf.datetime_porting) = DATE(NOW())  
           ORDER BY pdfd.created_date desc
        ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataPatchUrgentByToday()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         SELECT DISTINCT 
                pdf.data_fix_id,pdf.datetime_porting AS dateport,pdf.porting_seq,pdf.status,
                pdf.is_sent,
                pdf.created_by,pdf.created_date,
                pdf.changed_date,pdf.closed_by,
                case when pdf.urgent_type = 'external' then 'External' 
                when pdf.urgent_type = 'internal' then 'Internal' end as urgent_type,
                pdfd.status AS patchstatus,pdfd.bill_seq,pdfd.data_fix_dtl_id,  
                pdfd.crm_no,pdfd.redmineno,pdfd.created_date AS createdate, pdfd.endorsement_by, pdfd.endorsement_date,
                pdfd.created_date AS dtl_created_date,pdfd.created_by AS dtl_created_by,
                pdfd.sent_helpdesk,
                pdfd.sent_helpdesk_date,
                pdfd.remark_helpdesk,
                pcr.status AS CRstatus, 
                pps.status AS scriptstatus,pdf.name, 
                pl.name AS module,pl2.name AS prob_desc ,
                pl3.name AS requester,pdfd.requester_name, 
                pl4.name AS probtype, pdfd.created_by AS createdby, 
                (SELECT COUNT(*) 
                       FROM ps_change_request 
                       WHERE data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(description) > 3 
                       AND LENGTH(reason) > 3) AS total_valid_cr, 
                (SELECT COUNT(*) 
                       FROM ps_patch_script s
                       WHERE s.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                       AND LENGTH(s.sql) > 3 ) AS total_valid_script 	
           FROM ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
           LEFT JOIN ps_change_request pcr ON  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
           LEFT JOIN ps_patch_script pps ON pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
           , ps_lookup pl3 , ps_lookup pl4 
           WHERE pdfd.data_fix_id = pdf.data_fix_id 
           AND (pdfd.module = pl.id)
           AND (pdfd.problem_description = pl2.id)
           AND (pdfd.requester_type = pl3.id)
           AND (pdfd.problem_type = pl4.id)
           AND pdf.type_porting <> 'S'
           AND DATE(pdf.datetime_porting) = DATE(NOW())  
           ORDER BY pdfd.created_date desc
        ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataOpenPatch()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select distinct pdf.data_fix_id, pdfd.status as patchstatus,pcr.status as CRstatus, pps.status as scriptstatus,pdf.name, pdf.datetime_porting as dateport,pdf.porting_seq,pdfd.bill_seq,pdfd.data_fix_dtl_id,  pdfd.crm_no,pdfd.redmineno, pl.name as module,pl2.name as prob_desc ,pl3.name as requester,pdfd.requester_name, 
         pl4.name as probtype, pdfd.created_by as createdby, pdfd.created_date as createdate, pdfd.endorsement_by, pdfd.endorsement_date
            from ps_data_fix pdf, ps_lookup pl, ps_lookup pl2, ps_data_fix_dtl pdfd
				left join ps_change_request pcr on  pdfd.data_fix_dtl_id = pcr.data_fix_dtl_id
				left join ps_patch_script pps on pps.data_fix_dtl_id = pdfd.data_fix_dtl_id
            , ps_lookup pl3 , ps_lookup pl4 
            where pdfd.data_fix_id = pdf.data_fix_id 
            and (pdfd.module = pl.id)
            and (pdfd.problem_description = pl2.id)
            and (pdfd.requester_type = pl3.id)
            and (pdfd.problem_type = pl4.id)
            and pdf.type_porting <> 'U'
            and (pdf.status = 'Submit' or pdf.status = 'Open')
            and pdfd.status <> 'Cancelled'
            and date(pdf.datetime_porting) = date(NOW()) 
        ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataPatchDetailById($redmine)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select pdfd.data_fix_dtl_id, pdfd.remarks, pl.code, pdfd.created_by, pdfd.created_date,pdfd.changed_by, pdfd.changed_date,  pdf.datetime_porting as dateport,pdf.porting_seq,pdfd.data_fix_dtl_id, pdfd.bill_seq, pdfd.crm_no, pdfd.redmineno,
         pl.name as module,pl.id as moduleid, pl2.name as prob_desc,pl2.id as prob_desc_id, pl3.name as requester,pl3.id as requesterid, pdfd.requester_name, 
         pl4.name as probtype, pl4.id as probtypeid, pdfd.created_by as createdby, pdfd.created_date as createdate, pdfd.endorsement_by
            from ps_data_fix_dtl pdfd, ps_data_fix pdf, ps_lookup pl, ps_lookup pl2
            , ps_lookup pl3 , ps_lookup pl4 
            where pdfd.data_fix_id = pdf.data_fix_id 
            and (pdfd.module = pl.id)
            and (pdfd.problem_description = pl2.id)
            and (pdfd.requester_type = pl3.id)
            and (pdfd.problem_type = pl4.id)
            and pdf.status = 'Open'
            and date(pdf.datetime_porting) = date(NOW())
            and pdfd.redmineno = ? 
        ", array($redmine));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataOpenPatchbyCRMNumber($datafixdtlid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select pdfd.data_fix_dtl_id, pdfd.remarks, pl.code, pdfd.created_by, pdfd.created_date,pdfd.changed_by, pdfd.changed_date,  pdf.datetime_porting as dateport,pdf.porting_seq,pdfd.data_fix_dtl_id, pdfd.bill_seq, pdfd.crm_no, 
         pl.name as module,pl.id as moduleid, pl2.name as prob_desc,pl2.id as prob_desc_id, pl3.name as requester,pl3.id as requesterid, pdfd.requester_name, 
         pl4.name as probtype, pl4.id as probtypeid, pdfd.created_by as createdby, pdfd.created_date as createdate, pdfd.endorsement_by,pdfd.endorsement_date, pdfd.crm_tag_no , pdfd.redmineno 
            from ps_data_fix_dtl pdfd, ps_data_fix pdf, ps_lookup pl, ps_lookup pl2
            , ps_lookup pl3 , ps_lookup pl4 
            where pdfd.data_fix_id = pdf.data_fix_id 
            and (pdfd.module = pl.id)
            and (pdfd.problem_description = pl2.id)
            and (pdfd.requester_type = pl3.id)
            and (pdfd.problem_type = pl4.id)
            and pdf.status = 'Open'
            and date(pdf.datetime_porting) = date(NOW())
            and pdfd.data_fix_dtl_id = ? 
        ", array($datafixdtlid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListDataOpenPatchbyUrgent($crmno)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
         select pdfd.data_fix_dtl_id, pdfd.remarks, pl.code, pdfd.created_by, pdfd.created_date,pdfd.changed_by, pdfd.changed_date,  pdf.datetime_porting as dateport,pdf.porting_seq,pdfd.data_fix_dtl_id, pdfd.bill_seq, pdfd.crm_no, pdfd.redmineno,
         pl.name as module,pl.id as moduleid, pl2.name as prob_desc,pl2.id as prob_desc_id, pl3.name as requester,pl3.id as requesterid, pdfd.requester_name, 
         pl4.name as probtype, pl4.id as probtypeid, pdfd.created_by as createdby, pdfd.created_date as createdate, pdfd.endorsement_by, pdfd.crm_tag_no 
            from ps_data_fix_dtl pdfd, ps_data_fix pdf, ps_lookup pl, ps_lookup pl2
            , ps_lookup pl3 , ps_lookup pl4 
            where pdfd.data_fix_id = pdf.data_fix_id 
            and (pdfd.module = pl.id)
            and (pdfd.problem_description = pl2.id)
            and (pdfd.requester_type = pl3.id)
            and (pdfd.problem_type = pl4.id)
            and pdf.status = 'Open'
            and date(pdf.datetime_porting) = date(NOW())
            and pdfd.crm_no = ? 
        ", array($crmno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getSupplierNameDetail($mofno, $epno)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select company_name as name
                from sm_supplier sm, SM_MOF_ACCOUNT ma
                where sm.supplier_id = ma.supplier_id
                and (ma.mof_no = ? or sm.ep_no = ?) 
        ", array($mofno, $epno));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNameKumpulanKementerian($orgcode)
    {
        if ($orgcode == 'SYS') {
            $query = DB::connection('oracle_nextgen_rpt')->select("
           select 'KEMENTERIAN KEWANGAN MALAYSIA' as name, 169 as orgname from pm_org_validity where org_code = ? 
        ", array($orgcode));
        } else {
            $query = DB::connection('oracle_nextgen_rpt')->select("
           select distinct v2.org_name as name, 169 as orgname
                from pm_org_validity v1, pm_org_profile p1, pm_org_profile p2, pm_org_profile p3, pm_org_validity v2
                where p1.ORG_PROFILE_ID = v1.ORG_PROFILE_ID
                and p1.parent_org_profile_id = p2.ORG_PROFILE_ID
                and p2.parent_org_profile_id = p3.ORG_PROFILE_ID
                and p3.parent_org_profile_id = v2.ORG_PROFILE_ID
                and v1.record_status = 1
                and v2.record_status = 1
                and v1.org_code =  ? 
        ", array($orgcode));
        }
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getNameKumpulanKementerianAlone($orgcode)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
           select distinct org_name as name, '169' as orgname
                from pm_org_validity 
                where org_code =  ? 
        ", array($orgcode));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getNameFromMofNumber($mofno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
           select company_name as name, 168 as orgname
                from sm_supplier s, sm_mof_account a
                where s.supplier_id = a.supplier_id
                and mof_no = ? 
        ", array($mofno));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getNameFromepNo($epno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
           select company_name as name, 168 as orgname from sm_supplier where ep_no = ?
        ", array($epno));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getNameFromeregNo($regno)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
           select company_name as name, 168 as orgname from sm_supplier where reg_no = ?
        ", array($regno));
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function getTimeOpenPatch()
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select timestamp(datetime_porting) as date, data_fix_id, porting_seq from ep_prod_support.ps_data_fix where date(datetime_porting) = curdate() and status = 'Open' and type_porting = 'S'
        ", array());
        if (count($query) > 0) {
            return $query[0];
        }
        return null;
    }

    protected function checkIfScriptNotExist($datafixdtlid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select pps.sql as editsql,pps.name,  pps.id as scriptingid, pps.data_fix_dtl_id as datafixdtlid
           from ps_patch_script pps,  ps_data_fix_dtl pdfd, ps_data_fix pdf 
            where pdfd.data_fix_dtl_id = pps.data_fix_dtl_id 
            and pdf.data_fix_id = pdfd.data_fix_id 
            and pdf.status = 'Open'
            and pdfd.data_fix_dtl_id = ?
        ", array($datafixdtlid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function checkIfScriptNotExistforRedmine($redmine)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select pps.sql as editsql,  pps.id as scriptingid
           from ps_patch_script pps, ps_change_request pcr, ps_data_fix_dtl pdfd, ps_data_fix pdf 
            where pps.changed_request_id = pcr.changed_request_id 
            and pdfd.data_fix_dtl_id = pps.data_fix_dtl_id 
            and pdf.data_fix_id = pdfd.data_fix_id 
            and pdf.status = 'Open'
            and pdfd.redmineno = ?
        ", array($redmine));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function returnValueCRafterEdit($datafixdtlid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select pcr.* 
            from ps_change_request pcr, ps_data_fix_dtl pdfd, ps_data_fix pdf 
            where pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id 
            and pdf.data_fix_id = pdfd.data_fix_id 
            and pdf.status = 'Open'
            and pdfd.data_fix_dtl_id = ?
        ", array($datafixdtlid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function returnValueCRafterEditRedmine($redmine)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select pcr.* 
            from ps_change_request pcr, ps_data_fix_dtl pdfd, ps_data_fix pdf 
            where pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id 
            and pdf.data_fix_id = pdfd.data_fix_id 
            and pdf.status = 'Open'
            and pdfd.redmineno = ?
        ", array($redmine));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listCompletedDataFix($datafixid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select distinct pdfd.data_fix_dtl_id as data_fix_id_detail, pdf.datetime_porting, pdf.name as port, pl2.code as modulecode, pdf.data_fix_id,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name,pps.name as namescript,pps.data_fix_dtl_id as ppsdatafixdtlid, pps.id as ppsid,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pl.name as problem ,pcr.request_type,pcr.request_category, pcr.description,pcr.reason,pcr.requester_name,pcr.requester_date,
                pcr.recommender_name, pcr.recommender_date, pcr.approver_name, pcr.approver_date, pcr.activity_plan, pcr.expected_complete_date,pcr.impact_category, pcr.remarks, pcr.system_affected,
                pcr.impact_assessment, pdf.type_porting,
                pps.sql as querysql, date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport,
                pps.name as file_name_script,pl4.name as probtype,
                case when pdfd.endorsement_by = '' then '-' 
                when pdfd.endorsement_by is null then '-' 
                else pdfd.endorsement_by end as endorsement_by, pl3.name as requester 
                from ps_data_fix pdf , ps_data_fix_dtl pdfd , ps_change_request pcr , ps_patch_script pps , ps_lookup pl , ps_lookup pl2, ps_lookup pl3 , ps_lookup pl4 
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pl.id = pdfd.problem_description 
                and pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                and pps.data_fix_dtl_id  = pdfd.data_fix_dtl_id 
                and pl2.id = pdfd.module
                and pl3.id = pdfd.requester_type 
                and pl4.id = pdfd.problem_type 
                and pdf.data_fix_id = ? 
                and pps.status = 'Completed' 
                and pdfd.status = 'Created'
               
                ", array($datafixid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listEmailDataFix($datafixid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select distinct pdf.datetime_porting, pdf.name as port, pl2.code as modulecode, pdf.data_fix_id,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name,pps.data_fix_dtl_id as ppsdatafixdtlid, 
                pdf.token_access,
                CASE 
                        WHEN pdfd.redmineno IS NOT NULL THEN CONCAT('REDMINE#',pdfd.redmineno,' - ',pdf.name) 
                        ELSE  CONCAT('CASE#',pdfd.crm_no,' - ',pdf.name) 
                END AS title_request,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pl.name as problem , pl.id ,
                pdfd.token_access as dtl_token_access,
                pdfd.data_fix_dtl_id,
                pcr.*, 
                date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport,
                pl4.name as probtype, pdf.type_porting ,
                case when pdfd.endorsement_by = '' then '-' 
                when pdfd.endorsement_by is null then '-' 
                else pdfd.endorsement_by end as endorsement_by,
                case when pdfd.endorsement_date = '' then '-' 
                when pdfd.endorsement_date is null then '-' 
                else pdfd.endorsement_date end as endorsement_date,pl3.name as requester 
                from ps_data_fix pdf , ps_data_fix_dtl pdfd , ps_change_request pcr , ps_patch_script pps , ps_lookup pl , ps_lookup pl2, ps_lookup pl3 , ps_lookup pl4 
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pl.id = pdfd.problem_description 
                and pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                and pps.data_fix_dtl_id  = pdfd.data_fix_dtl_id 
                and pl2.id = pdfd.module
                and pl3.id = pdfd.requester_type 
                and pl4.id = pdfd.problem_type 
                and pdf.data_fix_id = ? 
                and pps.status = 'Completed' 
                and pdfd.status = 'Created'
               
                ", array($datafixid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function historyDataPatchDetail($dataFixdtlId, $scriptid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select distinct pdf.datetime_porting, pdf.name as port, pl2.code as modulecode, pdf.data_fix_id,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name,folder_name,pdf.folder_path,pps.name as namescript,pps.data_fix_dtl_id as ppsdatafixdtlid, pps.id as ppsid,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pl.name as problem ,pcr.*, pdf.type_porting,
                pps.sql as querysql, date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport,
                pps.name as file_name_script,pl4.name as probtype,
                case when pdfd.endorsement_by = '' then '-' 
                when pdfd.endorsement_by is null then '-' 
                else pdfd.endorsement_by end as endorsement_by, pl3.name as requester 
                from ps_data_fix pdf ,  ps_lookup pl , ps_lookup pl2, ps_lookup pl3 , ps_lookup pl4, ps_data_fix_dtl pdfd
                left join ps_change_request pcr on pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id
                left join ps_patch_script pps on pps.data_fix_dtl_id  = pdfd.data_fix_dtl_id
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pl.id = pdfd.problem_description 
                and pl2.id = pdfd.module
                and pl3.id = pdfd.requester_type
                and pl4.id = pdfd.problem_type
                and pdfd.data_fix_dtl_id = ?
                and pps.id = ?
                ", array($dataFixdtlId, $scriptid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function historyDataPatchDetailAll($dataFixdtlId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
           select distinct pdf.datetime_porting, pdf.name as port, pl2.code as modulecode, pdf.data_fix_id,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name,folder_name,pdf.folder_path,pps.name as namescript,pps.data_fix_dtl_id as ppsdatafixdtlid, pps.id as ppsid,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pl.name as problem ,pcr.*, pdf.type_porting,
                pps.sql as querysql, date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport,
                pps.name as file_name_script,pl4.name as probtype,
                case when pdfd.endorsement_by = '' then '-' 
                when pdfd.endorsement_by is null then '-' 
                else pdfd.endorsement_by end as endorsement_by, pl3.name as requester 
                from ps_data_fix pdf ,  ps_lookup pl , ps_lookup pl2, ps_lookup pl3 , ps_lookup pl4, ps_data_fix_dtl pdfd
                left join ps_change_request pcr on pcr.data_fix_dtl_id = pdfd.data_fix_dtl_id
                left join ps_patch_script pps on pps.data_fix_dtl_id  = pdfd.data_fix_dtl_id
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pl.id = pdfd.problem_description 
                and pl2.id = pdfd.module
                and pl3.id = pdfd.requester_type
                and pl4.id = pdfd.problem_type
                and pdfd.data_fix_dtl_id = ?
                ", array($dataFixdtlId));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    /**
     * Any Porting Date must be close on same day.
     * If any data porting still Open not as today, should be set as Cancelled
     * @param Collection $dataFixPorting
     * @return type
     */
    protected function setCancelledFixPortingInvalid()
    {
        DB::connection('mysql_ep_prod_support')
            ->select("UPDATE  ps_data_fix  
                            SET STATUS = 'Cancelled' ,
                            changed_date = NOW(),
                            changed_by = 'SystemAutoCancelled'
                            WHERE STATUS = 'Open' 
                            AND DATE(datetime_porting) < DATE(NOW())");
    }

    protected function findYear()
    {
        $query = DB::connection('mysql_ep_prod_support')
            ->select("select distinct date_format(datetime_porting,'%Y') as year from ps_data_fix", array());
        return $query;
    }

    protected function listReportByYear($type, $year)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select  pdf.urgent_type, pdf.data_fix_id, pdfd.datetime_porting , pdfd.crm_no, pdfd.redmineno, pl.code as module, pl2.name as problem_description, pdfd.requester_name, pl3.name as problem_type, pdfd.endorsement_by, date_format(pdf.datetime_porting, '%Y') as year, pdf.type_porting
           from ps_data_fix_dtl pdfd, ps_lookup pl, ps_lookup pl2, ps_lookup pl3 , ps_data_fix pdf 
           where pl.id = pdfd.module
           and pl2.id = pdfd.problem_description
           and pl3.id = pdfd.problem_type 
           and pdf.data_fix_id = pdfd.data_fix_id 
           and pdf.status = 'Closed'
           and pdfd.status <> 'Rejected'
           and type_porting = ?
           and date_format(pdf.datetime_porting, '%Y') = ?
                ", array($type, $year));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listReportByMonth($type, $year, $month)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select  pdf.urgent_type, pdf.data_fix_id, pdfd.datetime_porting , pdfd.crm_no, pdfd.redmineno, pl.code as module, pl2.name as problem_description, pdfd.requester_name, pl3.name as problem_type, pdfd.endorsement_by, date_format(pdf.datetime_porting, '%Y') as year, date_format(pdf.datetime_porting, '%m') as month
           from ps_data_fix_dtl pdfd, ps_lookup pl, ps_lookup pl2, ps_lookup pl3 , ps_data_fix pdf 
           where pl.id = pdfd.module
           and pl2.id = pdfd.problem_description
           and pl3.id = pdfd.problem_type 
           and pdf.data_fix_id = pdfd.data_fix_id 
           and pdf.status = 'Closed'
           and pdfd.status <> 'Rejected'
           and type_porting = ?
           and date_format(pdf.datetime_porting, '%Y') = ?
           and date_format(pdf.datetime_porting, '%m') = ?
                ", array($type, $year, $month));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listImpactAttached($datafixdtlid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select  * from ps_impact_attachment where data_fix_dtl_id = ? 
                ", array($datafixdtlid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listImpactAll($datafixdtlid)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select distinct pia.impact_attachment_id, pdf.datetime_porting, pdf.name as port, pdf.data_fix_id,pia.file_name ,pia.folder_path,pl2.code as modulecode,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name, pdfd.data_fix_dtl_id, pdf.folder_path as folder,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pdf.type_porting,
                date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport
                from ps_data_fix pdf , ps_data_fix_dtl pdfd, ps_impact_attachment pia, ps_lookup pl2
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pia.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                and pl2.id = pdfd.module 
                and pia.data_fix_dtl_id = ?
                ", array($datafixdtlid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listImpact($datafixdtlid, $attachId)
    {
        $query = DB::connection('mysql_ep_prod_support')->select("
            select distinct pia.impact_attachment_id, pdf.datetime_porting, pdf.name as port, pdf.data_fix_id,pia.file_name ,pia.folder_path,pl2.code as modulecode,
                pdf.is_sent, pdf.folder_zip_path,folder_zip_name, pdfd.data_fix_dtl_id, pdf.folder_path as folder,
                pdfd.crm_no,pdfd.redmineno,pdfd.requester_name as req, pdf.type_porting,
                date_format(pdf.datetime_porting,'%d/%m/%Y') as dateport
                from ps_data_fix pdf , ps_data_fix_dtl pdfd, ps_impact_attachment pia, ps_lookup pl2
                where pdf.data_fix_id = pdfd.data_fix_id 
                and pia.data_fix_dtl_id = pdfd.data_fix_dtl_id 
                and pl2.id = pdfd.module 
                and pia.data_fix_dtl_id = ?
                and pia.impact_attachment_id = ?
                ", array($datafixdtlid, $attachId));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
}
