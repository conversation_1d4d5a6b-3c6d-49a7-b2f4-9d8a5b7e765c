@extends('layouts.guest-dash')

@section('content')
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/it_support/dba_morning/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li class="active">
                <a href="{{ url('/it_support/dba_morning/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                    History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/dba_morning/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>
    <div class="block">
        <form id="it_support_checklist" action="{{ url('/it_support/dba_morning/checklist/history/{date}') }}" method="post">
            {{ csrf_field() }}
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            <div id="customDiv">
                <label class="col-md-12 text-center">Date : @if (isset($dateNote)) {{$dateNote}} @endif</label>  
                </div> 

            <input readonly="" id="date_search" name="date_search" type="text" value="{{ $dateNote }}"
                class="form-control" style="width: 700px; display:none">
            <div id="dba_morning_list_panel">
                <div class="table-options clearfix" id="dba_morning_list">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($menu))
                            @foreach ($menu as $key => $list)
                                <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                    title="{{ $list->dba_data_lookup_category_name }}"
                                    group="{{ $list->dba_data_lookup_category_name }}">
                                    <input type="radio" name="menu" value="">
                                    {{ $list->dba_data_lookup_category_name }}
                                </label>
                            @endforeach
                        @endif
                    </div>
                </div>
                <div class="text-center spinner-loading" style="padding: 20px;">
                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                </div>

                <div class="table-responsive">
                    <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
                </div>

                <div class="text-center button_update">
                    <button type="submit" style="background-color: #414770; color: white"
                        class="btn btn btn-primary">Update</button>
                </div>
            </div>
        </form>
    </div>

    <div class="block panel-heading notes_form" style="display:none">@include('it_support.page_status', ['page' => 'dba', 'location' => 'D'])</div>

@endsection
@section('jsprivate')
    <script>
        App.datatables();
    </script>
    <script>
        var customDiv = document.getElementById('customDiv');
        customDiv.style.width = '85vw'; // Set the width
        customDiv.style.height = '50px'; // Set the height
        customDiv.style.backgroundColor = '#414770'; // Set the background color
        customDiv.style.color = 'white';
        customDiv.style.textAlign = 'center';
        customDiv.style.fontSize = '16px';
        customDiv.style.display = 'flex';
        customDiv.style.alignItems = 'center';
    </script>

    <script>
        function selectAll(ele) {
            console.log('siniiitttutuuu')
            var checkboxes = document.querySelectorAll('#radio_for_status_okay');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }
        $('.editSave').hide();
        // $('.button_save').hide();
        // $('.button_endorse').hide();
        // $('.record_status').hide();

        $(document).ready(function() {
            if ($('#date_search').val() !== null && $('#date_search').val() !== "") {
                buttonSearch();
            }
            $('#seraching_history').on('click', function() {
                buttonSearch();
            });

        });

        function buttonSearch() {
            $('#dba_morning_list_panel').show();
            $('.notes_form').show();
            $('#menu_button').click();
            $('#menu_button').addClass("active");

            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
                $('#menu_button').click();
                $('#menu_button').addClass("active");

            }
            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();


            var dateSelected = $('#date_search').val();
            $('#selected_date').val(dateSelected);

            var today = new Date();
            var formattedDate = today.getFullYear() + '-' + ('0' + (today.getMonth() +
                1)).slice(-2) + '-' + ('0' + today.getDate()).slice(-2);
            $('#check_date').val(formattedDate);

            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("");
            }


        }

        function changeMenu(a) {

            var dateSelection = $('#date_search').val();
            let group = $(a).attr('group');

            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
            }

            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();

            $('.spinner-loading').show();

            $.ajax({
                type: "GET",
                url: "/it_support/dba_morning/find_by/" + group + "/" + dateSelection,
            }).done(function(data) {
                $('.spinner-loading').hide();
                $('#list_datatable').html(data).fadeIn();
                Tabledatatable = $('#list_datatable').DataTable({
                    ordering: false,
                    lengthMenu: [
                        [10, 20, 30, -1],
                        [10, 20, 30, 'All']
                    ]
                });

            })
            $.ajax({
                url: "/it_support/dba_morning/checklist/notes/history/" + dateSelection,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    var today = new Date();
                    var formattedDate = today.getFullYear() + '-' + ('0' + (today.getMonth() +
                        1)).slice(-2) + '-' + ('0' + today.getDate()).slice(-2);

                    if ($.isEmptyObject(data)) {
                        $('.notes_form').show();
                        $('.editSave').show();
                    }
                    if (data.listdata && data.listdata.length > 0) {
                        data.listdata.forEach(function(item) {
                            var checkRemarks = data.listdata[0].ack_remarks;
                            var checkBy = data.listdata[0].ack_check_by;
                            var checkDate = data.listdata[0].ack_check_date;
                            var ackDate = data.listdata[0].ack_endorsement_date;
                            var ackBy = data.listdata[0].ack_endorsement_by;
                            if (item.ack_status === 'Pending Endorsement') {
                                $('.button_update').hide();
                                $('.save_button').hide();
                                $('.endorse_button').show();
                                $('#remarks').val(checkRemarks);
                                $('#check_by').val(checkBy);
                                $('#check_date').val(checkDate);
                                $('#ack_date').val(formattedDate);
                                $('#ack_by').val(data.user);
                            }
                            if (item.ack_status === 'Completed') {
                                var remarksInput = document.getElementById('remarks');
                                remarksInput.readOnly = true;
                                $('.button_update').hide();
                                $('.save_button').hide();
                                $('.endorse_button').hide();
                                $('#remarks').prop('readonly', true);
                                $('#remarks').val(checkRemarks);
                                $('#check_by').val(checkBy);
                                $('#check_date').val(checkDate);
                                $('#ack_date').val(ackDate);
                                $('#ack_by').val(ackBy);
                            }
                        });

                    } else {
                        $('.button_update').show();
                        $('.save_button').show();
                        $('.endorse_button').hide();
                        $('#remarks').val("");
                        $('#check_by').val(data.user);
                        $('#remarks').prop('readonly', false);
                        $('#check_date').val(formattedDate);
                        $('#ack_by').val("");
                        $('#ack_date').val("");
                        $('#ack_status').text("")
                    }
                }
            });
        }
        $(".btn-group").on("click", "#menu_button", function(e) {
            changeMenu(this)
        });
    </script>
@endsection
