<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;

use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class PmOrgMasterDataFixDuplicate {

    public static function fixIssueDuplicateRecordStatusOrgProfile(){
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $dtStartTime = Carbon::now();

        $listOrg =   DB::connection('oracle_nextgen_fullgrant')
                        ->select('SELECT org_code, org_profile_id, count(*)  AS total
                                FROM pm_org_validity 
                                WHERE record_status  = 1 
                                GROUP BY org_code, org_profile_id
                                HAVING count(*) > 1');
        MigrateUtils::logDump(__METHOD__. ' Total '.count($listOrg ));
        foreach ($listOrg as $org){
            $listOrgDtl = DB::connection('oracle_nextgen_fullgrant')->table('pm_org_validity')
                ->where('org_code',$org->org_code)
                ->where('org_profile_id',$org->org_profile_id)
                ->orderBy('org_validity_id','desc')
                ->get();
            foreach ($listOrgDtl as $obj){
                if(Carbon::now()->gt(Carbon::parse($obj->exp_date)) && $obj->record_status == 1){
                    MigrateUtils::logDump(__METHOD__. ' Update as inactive... ');
                    DB::connection('oracle_nextgen_fullgrant')->table('pm_org_validity')
                        ->where('org_validity_id',$obj->org_validity_id)
                        ->update(
                            [
                                'record_status' => 0
                            ]
                        );
                    MigrateUtils::logDump(__METHOD__. ' Done as updated inactive... org_validity_id: '.$obj->org_validity_id);
                }

            }
            
            
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

}
