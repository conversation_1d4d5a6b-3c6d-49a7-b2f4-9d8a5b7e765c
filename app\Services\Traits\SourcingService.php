<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;
use Auth;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SourcingService {

    protected function getQtAcceptHistoryListService($docNo) {
        $query = DB::connection('mysql_ep_support')
            ->table('ep_qt_accept_history');
        
        if (!empty($docNo)) {
            $query->where('customattributestring1', $docNo);
        } else {
            $query->limit(200);
        }
        
        return $query->orderBy('updateddate', 'desc')->get();
    }

    protected function getSupplierData($updatedByValues) {
        if (empty($updatedByValues)) {
            return [];
        }
        
        $loginId = str_repeat('?,', count($updatedByValues) - 1) . '?';
        $supplierQuery = "
            SELECT
                PMU.login_id,
                SUPP.company_name
            FROM PM_USER PMU
            LEFT JOIN PM_USER_ORG PMUO ON PMUO.user_id = PMU.user_id
            LEFT JOIN SM_SUPPLIER SUPP ON SUPP.supplier_id = PMUO.org_profile_id
            AND PMU.org_type_id = 15
            WHERE PMU.login_id IN ($loginId)
            AND PMU.record_status = 1
            AND (PMU.org_type_id != 15 OR (PMU.org_type_id = 15 AND PMUO.record_status = 1 AND SUPP.record_status = 1))
        ";
        
        return DB::connection('oracle_nextgen_rpt')
            ->select($supplierQuery, $updatedByValues);
    }

        private function getQtAcceptHistoryData($docNo)
    {
        $sql = "
            SELECT
                mofacct.mof_no,
                supp.company_name AS supplier_name,
                (compbasic.phone_country || compbasic.phone_area || compbasic.phone_no) AS tel_no,
                personnel.NAME,
                personnel.designation,
                personnel.email
            FROM
                sm_mof_account mofacct,
                sm_supplier supp,
                sm_appl appl,
                sm_company_basic compbasic,
                sm_company_address compaddress,
                sm_address address,
                sc_qt a,
                sc_qt_supplier b,
                sc_qt_proposal c,
                sm_personnel personnel
            WHERE
                supp.supplier_id = mofacct.supplier_id
                AND supp.latest_appl_id = appl.appl_id
                AND compbasic.appl_id = appl.appl_id
                AND compbasic.rev_no IN (
                    SELECT MAX(cbasic.rev_no)
                    FROM sm_company_basic cbasic
                    WHERE compbasic.appl_id = cbasic.appl_id
                )
                AND compaddress.company_basic_id = compbasic.company_basic_id
                AND compaddress.address_id = address.address_id
                AND a.qt_id = b.qt_id
                AND b.qt_supplier_id = c.qt_supplier_id
                AND mofacct.mof_no = c.mof_no
                AND supp.latest_appl_id = personnel.appl_id
                AND personnel.ep_role = 'MOF_SUPPLIER_ADMIN'
                AND a.qt_no = :qt_no
                AND c.is_submitted = 1
                AND c.IS_AGREE_QT_EXTEND_PERIOD = 0
        ";
        
        return DB::connection('oracle_nextgen_rpt')->select($sql, ['qt_no' => $docNo]);
    }

    protected function getListAllCommitteeMembersQT($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT D.QT_NO, A.COMMITTEE_ID, B.COMMITTEE_TYPE_ID,
                        decode ((B.STATUS_ID), '60200', 'Pending Submission', '60201', 'Pending Approval','60202', 'Completed', '60203', 'Pending Revision','60204', 'Pending Revision Approval' ) as statuscmt,
                        P1.PARAMETER_CODE MEMBER_CODE,
                        P2.CODE_NAME MEMBER_DESC, C.USER_ID, C.MEMBER_NAME, C.IC_PASSPORT,
                        PP1.PARAMETER_CODE ROLE_CODE, PP2.CODE_NAME ROLE_DESC
                   FROM SC_QT_COMMITTEE A,
                        SC_COMMITTEE B,
                        PM_PARAMETER P1,
                        PM_PARAMETER_DESC P2,
                        PM_PARAMETER_TYPE P3,
                        SC_COMMITTEE_MEMBER C,
                        PM_PARAMETER PP1,
                        PM_PARAMETER_DESC PP2,
                        PM_PARAMETER_TYPE PP3,
                        SC_QT D
                  WHERE A.COMMITTEE_ID = B.COMMITTEE_ID
                    AND B.COMMITTEE_TYPE_ID = P1.PARAMETER_ID
                    AND P1.PARAMETER_TYPE = P3.PARAMETER_TYPE
                    AND P1.PARAMETER_ID = P2.PARAMETER_ID
                    AND P2.LANGUAGE_CODE = 'en'
                    AND B.COMMITTEE_ID = C.COMMITTEE_ID
                    AND C.MEMBER_ROLE_ID = PP1.PARAMETER_ID
                    AND PP1.PARAMETER_TYPE = PP3.PARAMETER_TYPE
                    AND PP1.PARAMETER_ID = PP2.PARAMETER_ID
                    AND PP2.LANGUAGE_CODE = 'en'
                    AND D.QT_ID = A.QT_ID
                    AND D.QT_NO = ?
                ORDER BY P2.CODE_NAME, PP1.PARAMETER_CODE ", array($docNo));

        return $results;
    }

    protected function getListProposalSupplierByQuotationTender($docNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " SELECT A.QT_NO, C.PROPOSAL_NO, C.PROPOSAL_SUBMIT_DATE, C.MOF_NO,
                        C.SUPPLIER_NAME, DECODE (C.IS_SUBMITTED, 0, 'NO', 1, 'YES') SUBMITTED, C.CHANGED_DATE
                   FROM SC_QT A, SC_QT_SUPPLIER B, SC_QT_PROPOSAL C
                 WHERE A.QT_ID = B.QT_ID
                    AND B.QT_SUPPLIER_ID = C.QT_SUPPLIER_ID
                    AND A.QT_NO = ?  ", array($docNo));
        return $results;
    }

    protected function getTotalSqCloseTimeDaily() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " select trunc(a.end_date),  count(*) as total
                    from sc_quote a
                    inner join sc_workflow_status b on a.quote_id = b.doc_id and b.doc_type = 'SQ'
                    where b.status_id = 60851
                    and trunc(a.end_date) = trunc(sysdate)
                    group by trunc(a.end_date)");
        return $results;
    }

    protected function getListSqCloseTimeDaily() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                " select a.end_date time_closed , row_number() OVER (ORDER BY a.end_date) as counter,  count(a.quote_id) as total
                    from sc_quote a
                    inner join sc_workflow_status b on a.quote_id = b.doc_id and b.doc_type = 'SQ'
                    where b.status_id = 60851
                    and trunc(a.end_date) = trunc(sysdate)
                    group by a.end_date
                    order by a.END_DATE");
        return $results;
    }

    protected function getListTaklimatOrLawatanTapakByQuotationTender($docNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
//                " SELECT   a.qt_no ,s.company_name,e.mof_no, g.disqualified_stage,
//                        DECODE (c.bsv_type, 'B', 'Briefing', 'S', 'Site Visit', 'A', 'B and SV') as BSV_TYPE, d.is_attended,
//                        d.is_post_registered, d.qt_approval_request_id,f.approver_action_id
//                   FROM sc_qt a,
//                        sc_qt_bsv b,
//                        sc_qt_bsv_dtl c,
//                        sc_qt_bsv_registration d,
//                        sm_mof_account e,
//                        sm_supplier s,
//                        sc_qt_supplier g,
//                        sc_qt_approval_request f
//
//                  WHERE a.qt_id = b.qt_id
//                    AND a.qt_id = g.qt_id
//                    AND b.qt_bsv_id = c.qt_bsv_id
//                    AND c.qt_bsv_dtl_id = d.qt_bsv_dtl_id
//                    AND d.supplier_id = e.supplier_id
//                    AND g.supplier_id = e.supplier_id
//                    AND g.supplier_id = s.supplier_id
//                    AND d.qt_approval_request_id = f.qt_approval_request_id(+)
//                    AND a.qt_no = ?  ", array($docNo));

                "SELECT   a.qt_no ,s.company_name,e.mof_no, g.disqualified_stage,
                        DECODE (c.bsv_type, 'B', 'Briefing', 'S', 'Site Visit', 'A', 'B and SV') as BSV_TYPE,
                        decode ((h.zone_name), 'SEMENANJUNG', 'Semenanjung', 'SABAH & SARAWAK', 'Sabah & Sarawak', 'N/A')as zonename,
                        d.is_attended,c.bsv_desc,
                        d.is_post_registered, d.qt_approval_request_id,f.approver_action_id
                   FROM sc_qt a,
                        sc_qt_bsv b,
                        sc_qt_bsv_dtl c,
                        sc_qt_bsv_registration d,
                        sm_mof_account e,
                        sm_supplier s,
                        sc_qt_supplier g,
                        sc_qt_approval_request f,
                        tpl_spec_zone_dtl h
                  WHERE a.qt_id = b.qt_id
                    AND a.qt_id = g.qt_id
                    AND b.qt_bsv_id = c.qt_bsv_id
                    AND c.qt_bsv_dtl_id = d.qt_bsv_dtl_id
                    AND d.supplier_id = e.supplier_id
                    AND g.supplier_id = e.supplier_id
                    AND g.supplier_id = s.supplier_id
                    AND b.spec_zone_dtl_id = h.spec_zone_dtl_id(+)
                    AND d.qt_approval_request_id = f.qt_approval_request_id(+)
                    AND a.qt_no =? ", array($docNo));
        return $results;
    }

    protected function getListStuctTaskQt() {
        $query = "
                  select q.qt_id, q.QT_NO, ws.DOC_TYPE,
                (select status_name from pm_status_desc where status_id = ws.status_id and language_code = 'en') as status_name,
                (select status_name from pm_status_desc where status_id = td.status_id and language_code = 'en') as tracking_diary_status_name,
                q.CREATED_DATE,
                td.ACTIONED_DATE,
                (SELECT actioned_date from PM_TRACKING_DIARY where DOC_NO = q.QT_NO and status_id = '60024' and rownum < 2) as date_pending_qt_approval
              from SC_WORKFLOW_STATUS ws
              inner join SC_QT q on q.QT_ID=ws.DOC_ID
              inner join (SELECT * from PM_TRACKING_DIARY td2 where ACTIONED_DATE=(SELECT MAX(ACTIONED_DATE) from PM_TRACKING_DIARY where DOC_NO=td2.DOC_NO and DOC_TYPE=td2.DOC_TYPE group by DOC_NO,DOC_TYPE)) td on (td.DOC_NO=q.QT_NO and td.DOC_TYPE='QT')
              where ws.IS_CURRENT = 1
              and (
                (ws.STATUS_ID IN (60003, 60027) and td.STATUS_ID=60025) OR
                (ws.STATUS_ID=60025 and td.STATUS_ID=60026) OR
                (ws.STATUS_ID=60026 and td.STATUS_ID IN (60028, 60037, 60004)) OR
                (ws.STATUS_ID=60004 and td.STATUS_ID IN (60250, 60150, 60005)) OR
                (ws.STATUS_ID=60025 and td.STATUS_ID=60027) OR
                (ws.STATUS_ID=62050 and td.STATUS_ID=62051) OR
                (ws.STATUS_ID=62051 and td.STATUS_ID=62053)  OR
                (ws.STATUS_ID=62053 and td.STATUS_ID=62054)  OR
                (ws.STATUS_ID=62054 and td.STATUS_ID=60023)  OR
                (ws.STATUS_ID=62001 and td.STATUS_ID=62002)  OR
                (ws.STATUS_ID=62002 and td.STATUS_ID=62003)  OR
                (ws.STATUS_ID=62003 and td.STATUS_ID IN (60200, 62100))  OR
                (ws.STATUS_ID=62100 and td.STATUS_ID=62101)  OR
                (ws.STATUS_ID=62101 and td.STATUS_ID=62103)  OR
                (ws.STATUS_ID=62103 and td.STATUS_ID=62104)  OR
                (ws.STATUS_ID=62104 and td.STATUS_ID IN (60200, 62150))  OR
                (ws.STATUS_ID=62151 and td.STATUS_ID=62152)  OR
                (ws.STATUS_ID=62152 and td.STATUS_ID=62153)  OR
                (ws.STATUS_ID=62201 and td.STATUS_ID=62210) OR
                (ws.STATUS_ID=62210 and td.STATUS_ID=62211) OR
                (ws.STATUS_ID=63551 and td.STATUS_ID=63552) OR
                (ws.STATUS_ID=63551 and td.STATUS_ID=63553) OR
                (ws.STATUS_ID=60008 and td.STATUS_ID=60009) OR
                (ws.STATUS_ID=60022 and td.STATUS_ID IN (62050,62000)) OR
                (ws.STATUS_ID=60002 and td.STATUS_ID=60024) OR
               -- (ws.STATUS_ID=60024 and td.STATUS_ID=60003) OR
                (ws.STATUS_ID=62208 and td.STATUS_ID=62210) OR
                (ws.STATUS_ID=60005 and td.STATUS_ID=60007) OR
                (ws.STATUS_ID=60006 and td.STATUS_ID=60038) OR
                (ws.STATUS_ID=60023 and td.STATUS_ID IN (60205,60206,62200)) OR
                (ws.STATUS_ID=60206 and td.STATUS_ID=62200) OR
                (ws.STATUS_ID=62200 and td.STATUS_ID=62201) --OR
              )
              and ws.DOC_TYPE IN ('QT','SE','EC','OC','TEC','FEC','SF')
              and not exists (select * from SC_WORKFLOW_STATUS where DOC_TYPE IN ('CMT','QT') and DOC_ID=q.QT_ID and IS_CURRENT=1 and STATUS_ID IN (60014,60015,60032,60201))
              order by td.ACTIONED_DATE desc
                ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    protected function getListStuckTaskChargingStatus() {
        $year = Carbon::now()->year;
        $query = "select ws.status_id,ws.created_date,fr.doc_no as doc_no_request,fo.doc_no as doc_no_order from fl_workflow_status ws, fl_fulfilment_request fr,  FL_FULFILMENT_ORDER fo
                    where fr.fulfilment_req_id = ws.doc_id
                    and fr.fulfilment_req_id = fo.fulfilment_req_id
                    and ws.doc_type in ('PR','CR')
                    and ws.IS_CURRENT = 1
                    and fr.financial_year = ?
                    and ws.status_id in ('40610','40110')";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($year));
    }
    
    protected function getListStuckTaskChargingStatusPHIS() {
        $year = Carbon::now()->year;
        $query = "select ws.status_id,ws.created_date,fr.doc_no as doc_no_request,
                    fo.doc_no as doc_no_order , fr.phis_no 
                    from fl_workflow_status ws, fl_fulfilment_request fr,  FL_FULFILMENT_ORDER fo
                    where fr.fulfilment_req_id = ws.doc_id
                    and fr.fulfilment_req_id = fo.fulfilment_req_id
                    and fr.financial_year = ? 
                    and fr.phis_no IS NOT NULL
                    and ws.doc_type in ('PR','CR')
                    and ws.status_id in ('40800','40300')
                    and ws.IS_CURRENT = 1
                    and trunc(ws.created_date) >=  trunc(sysdate-4)
                    and not exists( select td.tracking_diary_id from PM_TRACKING_DIARY td
                                 where td.doc_type = fr.doc_type and td.doc_id= fr.fulfilment_req_id and td.status_id in (40800,40300)
                    )";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($year));
    }

    protected function getDetailQuatationTenderInfo($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "SELECT   sq.qt_no, sq.publish_date, sq.closing_date,
          sq.qt_Id, sq.evaluation_type,
                        sq.proposal_validity_end_date,
                        (SELECT pd.code_name
                           FROM pm_parameter_desc pd
                          WHERE sq.procurement_mode_id = pd.parameter_id
                            AND pd.language_code = 'en') AS procurement,
                        (SELECT pd.code_name
                            FROM pm_parameter_desc pd
                           WHERE sq.PROCUREMENT_CATEGORY_ID = pd.parameter_id
                             AND pd.language_code = 'en') AS procurement_category,
                        (SELECT pd.code_name
                            FROM pm_parameter_desc pd
                           WHERE sq.PROCUREMENT_TYPE_CAT_ID = pd.parameter_id
                             AND pd.language_code = 'en') AS procurement_type_category,
                        DECODE (sq.is_bsv_req, 0, 'NO', 1, 'YES') as bsv,
                        vm.org_code AS ministry_code,
                        vm.org_name AS ministry_name,
                        vpp.org_code AS pp_code,
                        vpp.org_name AS pp_name,
                        vj.org_code AS kptj_code,
                        vj.org_name AS kptj_name,
                        vp.org_code AS ptj_code,
                        vp.org_name AS ptj_name,
                        sd.status_id, sd.status_name,
                        sq.qt_title,
                        (SELECT NVL (MAX (s.state_name), 'N/A')
                           FROM pm_state s
                          WHERE s.state_id IN (
                                   SELECT adr.state_id
                                     FROM pm_address adr
                                    WHERE adr.address_id IN (
                                             SELECT pat.address_id
                                               FROM pm_address_type pat
                                              WHERE pat.address_type = 'B'
                                                AND record_status = 1
                                                AND pat.org_profile_id = sq.org_profile_id)))
                                                                               AS state_name,
                        pu.login_id,pu.user_name
                   FROM sc_qt sq,
                        pm_org_validity vp,
                        pm_org_profile pp,
                        pm_org_validity vj,
                        pm_org_profile pj,
                        pm_org_validity vpp,
                        pm_org_profile ppp,
                        pm_org_validity vm,
                        pm_org_profile pm,
                        pm_parameter mtr,
                        sc_workflow_status s,
                        pm_status_desc sd,
                        pm_user pu
                  WHERE sq.OWNER_ORG_PROFILE_ID = vp.org_profile_id
                    AND vp.org_profile_id = pp.org_profile_id
                    AND pp.parent_org_profile_id = vj.org_profile_id
                    AND vj.org_profile_id = pj.org_profile_id
                    AND pj.parent_org_profile_id = vpp.org_profile_id
                    AND vpp.org_profile_id = ppp.org_profile_id
                    AND ppp.parent_org_profile_id = vm.org_profile_id
                    AND vm.org_profile_id = pm.org_profile_id
                    AND pp.org_type_id = mtr.parameter_id
                    AND sq.created_by = pu.user_id
                    AND vp.record_status = 1
                    AND vj.record_status = 1
                    AND vpp.record_status = 1
                    AND vm.record_status = 1
                    AND vp.exp_date > sysdate
                    AND sq.qt_id = s.doc_id
                    AND s.status_id = sd.status_id
                    AND sd.language_code = 'en'
                    AND s.created_date IN (SELECT MAX (x.created_date)
                                             FROM sc_workflow_status x
                                            WHERE sq.qt_id = x.doc_id
                                            AND x.doc_type = 'QT')
                    AND sq.qt_no = ?
               GROUP BY sq.qt_title,
                        sq.publish_date,
                        sq.qt_no,
                        sq.qt_Id,
                        sq.evaluation_type,
                        sq.is_bsv_req,
                        sd.status_name,
                        sd.status_id,
                        sq.closing_date,
                        sq.qt_id,
                        sq.org_profile_id,
                        sq.proposal_validity_end_date,
                        sq.procurement_mode_id,
                        sq.PROCUREMENT_CATEGORY_ID,
                        sq.PROCUREMENT_TYPE_CAT_ID,
                        vp.org_name,
                        vp.org_code,
                        vj.org_name,
                        vj.org_code,
                        vpp.org_name,
                        vpp.org_code,
                        vm.org_name,
                        vm.org_code,
                        pu.login_id,pu.user_name ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    protected function getQtCountByDate($qtDate) {
        // Log::info(__METHOD__." >> ".$qtDate);
        $query = "SELECT count(*) as total
        FROM SC_QT q
        INNER JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_ID = q.QT_ID AND ws.DOC_TYPE = 'QT' AND ws.IS_CURRENT = 1)
        WHERE qt_no LIKE 'QT%' AND trunc(q.Closing_Date) = to_date(?,'DD-MM-YY') AND ws.STATUS_ID IN (60009)";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtDate));
    }

    protected function getQtListByDate($qtDate) {
        // Log::info(__METHOD__." >> ".$qtDate);
        $query = "SELECT (SELECT ministry.ORG_NAME
                FROM PM_ORG_VALIDITY ministry
                WHERE ministry.ORG_PROFILE_ID IN (SELECT PARENT_ORG_PROFILE_ID
                                                  FROM PM_ORG_PROFILE
                                                  WHERE ORG_PROFILE_ID IN (SELECT PARENT_ORG_PROFILE_ID
                                                                           FROM PM_ORG_PROFILE
                                                                           WHERE ORG_PROFILE_ID IN (SELECT PARENT_ORG_PROFILE_ID
                                                                                                    FROM PM_ORG_PROFILE
                                                                                                    WHERE ORG_PROFILE_ID = q.ORG_PROFILE_ID))) AND
                        ministry.RECORD_STATUS = 1) AS MINISTRY_NAME,
               (SELECT ministry.ORG_NAME
                FROM PM_ORG_VALIDITY ministry
                WHERE ministry.ORG_PROFILE_ID = q.ORG_PROFILE_ID AND ministry.RECORD_STATUS = 1) AS PTJ_NAME,
               q.QT_ID, q.QT_NO, q.PUBLISH_DATE, q.CLOSING_DATE,
               (SELECT status_name FROM pm_status_desc WHERE status_id = ws.status_id AND language_code = 'en') AS STATUS_NAME
        FROM SC_QT q
               INNER JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_ID = q.QT_ID AND ws.DOC_TYPE = 'QT' AND ws.IS_CURRENT = 1)
        WHERE qt_no LIKE 'QT%' AND trunc(q.Closing_Date) = to_date(?,'DD-MM-YY')  AND ws.STATUS_ID IN (60009)
        ORDER BY MINISTRY_NAME, PTJ_NAME, q.QT_NO";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtDate));
    }

    // <editor-fold defaultstate="collapsed" desc="REGION SUMMARY QT">
    /*     * Akmal add for summary--> */
    protected function getDetailQuatationTenderSummary($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
        SELECT sq.file_no, sq.is_panel,sq.parent_qt_id,
        decode ((sq.is_panel), '0', 'NO  ', '1', 'Panel Only  ') as panel,
        sq.is_panel as checkpanel,
        decode ((sq.fulfilment_type_id), '148', 'One Off', '149', 'Periodic Schedule','150', 'Periodic As and When Basic') as ff_type,
        decode ((sq.evaluate_by), 'B', 'Pakej & Item', 'I', 'Item','P', 'Pakej') as item_or_pakej,
        decode ((sq.qt_type), 'O', 'Open', 'R', 'Restricted','I', 'International Tender')as jenis_qt,      
        decode ((sq.evaluation_type), '1', '1 Tier', '2', '2 Tier')as evaluation_type2,
        decode ((sq.is_zonal), '1', 'YES ', '0', 'NO ')as is_zonal,
        decode ((sq.open_for), 'A', 'All', 'B', 'Bumiputra')as openfor2,
        decode ((sq.IS_BSV_REQ), '1', 'YES', '0', 'NO')as brefingsitevisit,
        decode ((sq.record_status), '1', 'Active', '0', 'Inactive')as status_supplier,
        sq.qt_no, sq.publish_date,sq.qt_validity_period, sq.publish_period,
        sq.closing_date,
         CASE
             WHEN sq.closing_date <= SYSDATE
                THEN 'iklanexpired'
             ELSE ''
          END AS checkvalidityiklan,
        sq.qt_id, sq.contract_duration,
        sq.evaluation_type,
        sq.proposal_validity_end_date,
        CASE
             WHEN sq.proposal_validity_end_date <= SYSDATE
                THEN 'qtexpired'
             ELSE ''
          END AS checkvalidityqt,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_mode_id = pd.parameter_id AND pd.language_code = 'en') AS procurement,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_category_id = pd.parameter_id AND pd.language_code = 'en') AS TYPE,
        (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.PROCUREMENT_TYPE_CAT_ID = pd.parameter_id AND pd.language_code = 'en') AS category,
        vm.org_code AS ministry_code, vm.org_name AS ministry_name,
        vpp.org_code AS pp_code, vpp.org_name AS pp_name,
        vj.org_code AS kptj_code, vj.org_name AS kptj_name,
        vp.org_code AS ptj_code, vp.org_name AS ptj_name, 
        vp1.ORG_CODE as for_ptj_code, vp1.ORG_NAME as for_ptj_name,
        sd.status_id,
        sd.status_name, sq.qt_title,
        (SELECT NVL (MAX (s.state_name), 'N/A')
         FROM pm_state s WHERE s.state_id IN (SELECT adr.state_id FROM pm_address adr WHERE adr.address_id IN (
             SELECT pat.address_id FROM pm_address_type pat WHERE pat.address_type = 'B'
             AND record_status = 1 AND pat.org_profile_id = sq.org_profile_id))) AS state_name,
        pu.login_id, pu.user_name
 FROM   sc_qt sq, pm_org_validity vp, pm_org_profile pp, pm_org_validity vj,
        pm_org_profile pj, pm_org_validity vpp, pm_org_profile ppp, pm_org_validity vm,
        pm_org_profile pm, pm_parameter mtr, sc_workflow_status s,   pm_status_desc sd,pm_user pu,
        pm_org_validity vp1       
 WHERE  sq.owner_org_profile_id = vp.org_profile_id
        AND vp.org_profile_id = pp.org_profile_id
        AND sq.ORG_PROFILE_ID = vp1.ORG_PROFILE_ID
        AND pp.parent_org_profile_id = vj.org_profile_id
        AND vj.org_profile_id = pj.org_profile_id
        AND pj.parent_org_profile_id = vpp.org_profile_id
        AND vpp.org_profile_id = ppp.org_profile_id
        AND ppp.parent_org_profile_id = vm.org_profile_id
        AND vm.org_profile_id = pm.org_profile_id
        AND pp.org_type_id = mtr.parameter_id
        AND sq.created_by = pu.user_id
        AND vp.record_status = 1
        AND vp1.record_status = 1
        AND vj.record_status = 1
        AND vpp.record_status = 1
        AND vm.record_status = 1
        AND vp.exp_date > SYSDATE
        AND sq.qt_id = s.doc_id
        AND s.status_id = sd.status_id
        AND sd.language_code = 'en'
        AND s.created_date IN (SELECT MAX (x.created_date) FROM sc_workflow_status x
           WHERE sq.qt_id = x.doc_id AND x.doc_type in ('QT','CMT','TEC','OC','FEC','EC','BD'))
       AND sq.qt_no = ?
 GROUP BY sq.file_no, sq.is_panel,sq.parent_qt_id, sq.fulfilment_type_id,sq.qt_title, sq.publish_date, sq.is_zonal,
          sq.qt_validity_period, sq.publish_period,sq.qt_no,sq.qt_id,sq.contract_duration,
          sq.evaluation_type, sq.is_bsv_req, sd.status_name, sd.status_id,sq.closing_date, sq.qt_id, sq.org_profile_id,
          sq.proposal_validity_end_date, sq.procurement_mode_id, sq.procurement_category_id, sq.PROCUREMENT_TYPE_CAT_ID,
          vp.org_name, vp.org_code, vj.org_name, vj.org_code, vpp.org_name, vpp.org_code, vm.org_name, vm.org_code,
          pu.login_id, pu.user_name,sq.EVALUATE_BY, sq.QT_TYPE,sq.EVALUATION_TYPE,sq.IS_BSV_REQ,sq.RECORD_STATUS, 
          sq.org_profile_id, vp1.ORG_CODE, vp1.ORG_NAME, sq.open_for
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    /*     * Akmal add for summary-->add zonal details */
    protected function getDetailZonalQTSummary($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
        SELECT distinct szd.zone_name,
        loc.state_id || ' - ' || st.STATE_NAME state,
        loc.district_id || ' - ' || ds.DISTRICT_NAME district,
        loc.division_id || ' - ' || div.division_name division
   FROM sc_qt qt,
        sc_qt_supplier_criteria qtc,
        tpl_spec_zone sz,
        tpl_spec_zone_dtl szd,
        tpl_spec_loc loc,
        pm_state st,
        pm_division div,
        pm_district ds
  WHERE qt.qt_id = qtc.qt_id
    AND qtc.qt_supplier_criteria_id = sz.doc_id
    AND sz.spec_zone_id = szd.spec_zone_id
    AND szd.spec_zone_dtl_id = loc.doc_id
    AND loc.division_id = div.division_id(+)
    and loc.STATE_ID = st.STATE_ID (+)
    and loc.DISTRICT_ID= ds.DISTRICT_ID(+)
    AND loc.doc_type = 'ZON'
    AND sz.doc_type = 'QTZ'
    and qt.qt_no=?
ORDER BY  zone_name
    ";
    return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
}

    protected function getDetailQuatationTenderDL($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
    SELECT distinct sq.file_no, sq.is_panel,sq.parent_qt_id,
       decode ((sq.is_panel), '0', 'Normal  ', '1', 'Panel Only  ') as panel,
       sq.is_panel as checkpanel,
       decode ((sq.fulfilment_type_id), '148', 'One Off', '149', 'Periodic Schedule','150', 'Periodic As and When Basic') as ff_type,
       decode ((sq.evaluate_by), 'B', 'Pakej & Item', 'I', 'Item','P', 'Pakej') as item_or_pakej,
       decode ((sq.qt_type), 'O', 'Open/Terbuka', 'R', 'Restricted','I', 'International Tender')as jenis_qt,
       decode ((sq.evaluation_type), '1', '1 Tier', '2', '2 Tier')as evaluation_type2,
       decode ((sq.open_for), 'A', 'All', 'B', 'Bumiputra')as openfor2,
       decode ((sq.IS_BSV_REQ), '1', 'YES', '0', 'NO')as brefingsitevisit,
       decode ((sq.record_status), '1', 'Active', '0', 'Inactive')as status_supplier,
       sq.qt_no, sq.publish_date,
       sq.closing_date,
        CASE
            WHEN sq.closing_date <= SYSDATE
               THEN 'iklanexpired'
            ELSE ''
         END AS checkvalidityiklan,
       sq.qt_id,sq.contract_duration, sq.evaluation_type,
       sq.proposal_validity_end_date,
       CASE
            WHEN sq.proposal_validity_end_date <= SYSDATE
               THEN 'qtexpired'
            ELSE ''
         END AS checkvalidityqt,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_mode_id = pd.parameter_id AND pd.language_code = 'en') AS procurement,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.procurement_category_id = pd.parameter_id AND pd.language_code = 'en') AS TYPE,
       (SELECT pd.code_name FROM pm_parameter_desc pd WHERE sq.PROCUREMENT_TYPE_CAT_ID = pd.parameter_id AND pd.language_code = 'en') AS category,
       vm.org_code AS ministry_code, vm.org_name AS ministry_name,
       vpp.org_code AS pp_code, vpp.org_name AS pp_name,
       vj.org_code AS kptj_code, vj.org_name AS kptj_name,
       vp.org_code AS ptj_code, vp.org_name AS ptj_name, 
       vp1.ORG_CODE as for_ptj_code, vp1.ORG_NAME as for_ptj_name,
       sd.status_id,sd.status_name,
       sq.qt_title,
      (SELECT NVL (MAX (s.state_name), 'N/A')
       FROM pm_state s WHERE s.state_id IN (SELECT adr.state_id FROM pm_address adr WHERE adr.address_id IN (
            SELECT pat.address_id FROM pm_address_type pat WHERE pat.address_type = 'B'
           AND record_status = 1 AND pat.org_profile_id = sq.org_profile_id))) AS state_name,
       pu.login_id, pu.user_name
    FROM 
       sc_qt sq, pm_org_validity vp, pm_org_profile pp, pm_org_validity vj,
       pm_org_profile pj, pm_org_validity vpp, pm_org_profile ppp, pm_org_validity vm,
       pm_org_profile pm, pm_parameter mtr, sc_workflow_status s,  pm_status_desc sd,pm_user pu,
       pm_org_validity vp1   
    WHERE  sq.owner_org_profile_id = vp.org_profile_id(+)
       AND vp.org_profile_id = pp.org_profile_id(+)
       AND sq.ORG_PROFILE_ID = vp1.ORG_PROFILE_ID
       AND pp.parent_org_profile_id = vj.org_profile_id(+)
       AND vj.org_profile_id = pj.org_profile_id(+)
       AND pj.parent_org_profile_id = vpp.org_profile_id(+)
       AND vpp.org_profile_id = ppp.org_profile_id(+)
       AND ppp.parent_org_profile_id = vm.org_profile_id(+)
       AND vm.org_profile_id = pm.org_profile_id(+)
       AND pp.org_type_id = mtr.parameter_id(+)
       AND sq.created_by = pu.user_id(+)
       AND vp.record_status = 1
       AND vp1.record_status = 1
       AND vj.record_status = 1
       AND vpp.record_status = 1
       AND vm.record_status = 1
       AND vp.exp_date > SYSDATE
       AND sq.qt_id = s.doc_id(+)
       AND s.status_id = sd.status_id(+)
       AND sd.language_code = 'en'
      -- AND s.created_date IN (SELECT MAX (x.created_date) FROM sc_workflow_status x
      -- WHERE sq.qt_id = x.doc_id AND x.doc_type in ('QT','CMT','TEC','OC','FEC','EC','BD'))
      AND sq.qt_no = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
    }

    protected function getNewQTNo($qtno) {
        // Log::info(__METHOD__." >> ".$qtno);
        $query = "
         select qt_id from sc_qt where qt_no = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtno));
    }

    protected function CheckNewGenerateQTNo($qtid) {
        // Log::info(__METHOD__." >> qtid :: ".$qtid);
        $query = "
         select qt_no as qtno2, qt_id  from sc_qt where parent_qt_id = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtid));
    }

    protected function CheckPreviousQTno($qtno) {
        // Log::info(__METHOD__." >> ".$qtno);
        $query = "
         select qt_id as pqtid, qt_no as pqtno from sc_qt where qt_id in (select parent_qt_id from sc_qt where qt_id = ?)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtno));
    }

    protected function getDetailKodBidang($NoQTb) {
        // Log::info(__METHOD__." >> ".$NoQTb);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    SELECT    a.qt_no,
       l1.category_l1_code
       || l2.category_l2_code
       || l3.category_l3_code AS categorycode, b.category_l1_name bidang, b.category_l2_name subbidang, b.category_l3_name pecahansubbidang
    FROM sc_qt a,
       sc_qt_category b,
       pm_category_l1 l1,
       pm_category_l2 l2,
       pm_category_l3 l3
    WHERE a.qt_id = b.qt_id
       AND l1.category_l1_id = b.category_l1_id
       AND l2.category_l2_id = b.category_l2_id
       AND l3.category_l3_id = b.category_l3_id
       AND a.qt_no = ?
         ", array($NoQTb));
        return $results;
    }

    protected function getDetailQuatationTenderBSV($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    SELECT
        s.company_name, e.mof_no,
        decode ((g.invitation_type), 'I', 'Invite', 'S', 'Schedular', 'N/A')as jenis_invitation,
        decode ((g.disqualified_stage), 'B', 'Failed To Attend', 'C', 'Not Fulfill The Criteria')as disqualified_stage,
        decode ((c.bsv_type), 'S', 'Site Visit', 'B', 'Briefing','A', 'Site Visit & Briefing','N/A')as bsv_type2,
        h.zone_name zonename, --UTARA, TENGAH, SELATAN, TIMUR
        --decode ((h.zone_name), 'SEMENANJUNG', 'Semenanjung', 'SABAH & SARAWAK', 'Sabah&Sarawak', 'N/A')as zonename,
        c.bsv_desc,
        decode ((d.is_attended), '1', 'Yes', '0', 'No','')as is_attended,
        c.bsv_date,
        decode ((d.is_post_registered), '1', 'Yes', '0', 'No','')as ispost_registered,
        d.qt_approval_request_id,
        decode ((f.approver_action_id), '479', 'Approved', '480', 'Rejected')as approver_action_id,
        case when g.disqualified_stage = 'B'
        and d.is_post_registered = '1'
        and d.qt_approval_request_id is not null
        and f.approver_action_id ='479'
        then '!'
        when g.disqualified_stage = 'C'
        and d.is_post_registered = '1'
        and d.qt_approval_request_id is not null
        and f.approver_action_id ='479'
        then '!' end as np,
        k.user_name,
        f.CREATED_DATE as Approver_createdate,
        f.CHANGED_DATE as Approver_changedate
    FROM sc_qt a,
         sc_qt_bsv b,
         sc_qt_bsv_dtl c,
         sc_qt_bsv_registration d,
         sm_mof_account e,
         sc_qt_supplier g,
         sc_qt_approval_request f,
         tpl_spec_zone_dtl h,
         pm_user k,
         sm_supplier s
    WHERE a.qt_id = b.qt_id
        AND a.qt_id = g.qt_id
        AND b.qt_bsv_id = c.qt_bsv_id
        AND c.qt_bsv_dtl_id = d.qt_bsv_dtl_id
        AND d.supplier_id = e.supplier_id
        AND g.supplier_id = e.supplier_id
        AND g.supplier_id = s.supplier_id
        AND f.approver_id = k.user_id (+)
        AND d.qt_approval_request_id = f.qt_approval_request_id(+)
        AND b.SPEC_ZONE_DTL_ID = h.SPEC_ZONE_DTL_ID (+)
        AND a.qt_no = ?
    ORDER BY disqualified_stage desc, e.mof_no
             ", array($docNo));
        return $results;
    }


    protected function getDetailQuatationTenderCommittePTJ($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    SELECT z.login_id, x.role_code AS ROLE, x.user_name AS NAME
    FROM sc_actor x, sc_qt y, pm_user z
    WHERE
            y.qt_id = x.doc_id
            AND x.user_id = z.user_id
            AND x.doc_type IN ('QT')
            AND y.qt_no = ?
            AND z.login_id NOT IN (
                    SELECT c.ic_passport
                        FROM sc_qt_committee a,
                        sc_committee b,
                        pm_parameter p1,
                        pm_parameter_desc p2,
                        pm_parameter_type p3,
                        sc_committee_member c,
                        pm_parameter pp1,
                        pm_parameter_desc pp2,
                        pm_parameter_type pp3,
                        sc_qt d
                    WHERE
                        a.committee_id = b.committee_id
                        AND b.committee_type_id = p1.parameter_id
                        AND p1.parameter_type = p3.parameter_type
                        AND p1.parameter_id = p2.parameter_id
                        AND p2.language_code = 'en'
                        AND b.committee_id = c.committee_id
                        AND c.member_role_id = pp1.parameter_id
                        AND pp1.parameter_type = pp3.parameter_type
                        AND pp1.parameter_id = pp2.parameter_id
                        AND pp2.language_code = 'en'
                        AND d.qt_id = a.qt_id
                        AND x.role_code <> 'DESK_OFFICER'
                        AND x.role_code <> 'COMMITTEE_APPROVER'
                        AND x.role_code <> 'PUBLICATION_APPROVER'
                        AND d.qt_no = ?
                        )
             ", array($docNo, $docNo));
        return $results;
    }

    /*     * Akmal add for get QT from LOA--> */

    protected function getQTNoFromLOA($LoaNo) {
        // Log::info(__METHOD__." >> ".$LoaNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
        select c.qt_no
        from sc_loa a,sc_loi_loa b, sc_qt c
        where a.loi_loa_id = b.loi_loa_id
        and b.doc_id = c.qt_id
        and a.loa_no = ?
         ", array($LoaNo));
        return $results;
    }

    /*     * Akmal add for get QT from LOI--> */

    protected function getQTNoFromLOI($LoiNo) {
        // Log::info(__METHOD__." >> ".$LoiNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select c.qt_no
        from sc_loi a,sc_loi_loa b, sc_qt c
        where a.loi_loa_id = b.loi_loa_id
        and b.doc_id = c.qt_id
        and a.loi_no = ?
         ", array($LoiNo));
        return $results;
    }

    protected function getQTNoFromBIDDING($BidNo) {
        // Log::info(__METHOD__." >> ".$BidNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select a.doc_no
        from SC_BID a, SC_BID_SCHEDULE b, sc_qt c
        where a.bid_id = b.bid_id
        and c.qt_no = a.doc_no
        and b.bid_no = ?
         ", array($BidNo));
        return $results;
    }

    protected function checkDaftarKodBidangbyToday($BidangNo) {
        // Log::info(__METHOD__." >> ".$BidangNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    SELECT   qt_no, mof.mof_no, supp.company_name, appl.appl_no,
            l1.category_l1_code
         || l2.category_l2_code
         || l3.category_l3_code AS categorycode,
         scat.record_status, appl.created_date
    FROM sc_qt_category scat,
         sm_appl appl,
         sm_supplier supp,
         sm_mof_account mof,
         pm_category_l1 l1,
         pm_category_l2 l2,
         pm_category_l3 l3,
         sc_qt_supplier qts,
         sc_qt qt
    WHERE appl.appl_id = supp.latest_appl_id
        AND supp.supplier_id = appl.supplier_id
        AND l1.category_l1_id = scat.category_l1_id
        AND l2.category_l2_id = scat.category_l2_id
        AND l3.category_l3_id = scat.category_l3_id
        AND supp.supplier_id = mof.supplier_id
        AND supp.supplier_id = qts.supplier_id
        AND scat.qt_id = qts.qt_id
        AND scat.qt_id = qt.qt_id
        AND scat.record_status = 1
        AND SUBSTR (appl.appl_no, 0, 2) = 'KA'
        AND (appl.created_date > SYSDATE - 2 AND appl.created_date < SYSDATE + 2 )
     AND qt.qt_no = ?
    ORDER BY appl.created_date DESC, 7
         ", array($BidangNo));
        return $results;
    }

    /*     * Akmal check supplier respond qt validity--> */

    protected function getDetailSuppRespond($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT rownum,qt.qt_no, p.qt_proposal_id, p.is_submitted, qt.qt_id,s.supplier_id, sp.company_name, mof.mof_no, p.proposal_no,p.is_agree_qt_extend_period, p.created_date,p.changed_date
                FROM sc_qt qt, sc_qt_supplier s, sm_mof_account mof, sc_qt_proposal p, sm_supplier sp
                WHERE qt.qt_id = s.qt_id
                    AND sp.SUPPLIER_ID = mof.supplier_id
                    AND s.supplier_id = mof.supplier_id
                    AND mof.record_status = 1
                    AND s.QT_SUPPLIER_ID=p.QT_SUPPLIER_ID(+)
                    AND qt.QT_NO = ?
                    AND p.IS_SUBMITTED=1  
                order by p.IS_AGREE_QT_EXTEND_PERIOD
         ", array($docNo));
        return $results;
//         and p.is_agree_qt_extend_period is not null
    }

    /*     * Akmal check supplier experience--> */

    protected function getDetailSuppExp1($mofNo) {
        // Log::info(__METHOD__." >> ".$mofNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT distinct t2.company_name, t2.supplier_id, mof.mof_no, 
                    t0.company_experience_id, t0.client_name,
                    decode ((t0.client_type), 'F', 'Federal', 'O', 'Others') as client_type, 
                    t0.contract_value,t0.procurement_date, t0.procurement_mode_id, 
                    decode ((t0.progress_status), 'P', 'Progress', 'S', 'Success', 'F', 'Failed', 'K', 'KIV', 'R', 'Repeal') as progress_status, 
                    decode ((t0.record_status), '1', 'Active', '0', 'Inactive') as record_status, 
                    t0.title
                FROM pm_category_l3 t6, sc_qt t5, sc_qt_category t4,
                    sm_company_experience_cat t3,
                    sm_supplier t2, sm_mof_account mof, sm_appl t1, sm_company_experience t0
                WHERE t1.appl_id = t2.latest_appl_id
                    AND t0.appl_id = t1.appl_id
                    AND t3.company_experience_id = t0.company_experience_id
                    AND t6.category_l3_id = t4.category_l3_id
                    AND t3.category_l3_id = t6.category_l3_id
                    AND t5.qt_id = t4.qt_id
                    AND t2.supplier_id = mof.supplier_id
                    AND t0.client_type ='F'
                    AND mof.mof_no =?
                    ORDER BY t0.procurement_date desc
         ", array($mofNo));
        return $results;
    }

    protected function getDetailSuppExp2($mofNo) {
        // Log::info(__METHOD__." >> ".$mofNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT distinct t2.company_name, t2.supplier_id, mof.mof_no, 
                    t0.company_experience_id, t0.client_name,
                    decode ((t0.client_type), 'F', 'Federal', 'O', 'Others') as client_type, 
                    t0.contract_value,t0.procurement_date, t0.procurement_mode_id, 
                    decode ((t0.progress_status), 'P', 'Progress', 'S', 'Success', 'F', 'Failed', 'K', 'KIV', 'R', 'Repeal') as progress_status, 
                    decode ((t0.record_status), '1', 'Active', '0', 'Inactive') as record_status, 
                    t0.title
                FROM pm_category_l3 t6, sc_qt t5, sc_qt_category t4,
                    sm_company_experience_cat t3,
                    sm_supplier t2, sm_mof_account mof, sm_appl t1, sm_company_experience t0
                WHERE t1.appl_id = t2.latest_appl_id
                    AND t0.appl_id = t1.appl_id
                    AND t3.company_experience_id = t0.company_experience_id
                    AND t6.category_l3_id = t4.category_l3_id
                    AND t3.category_l3_id = t6.category_l3_id
                    AND t5.qt_id = t4.qt_id
                    AND t2.supplier_id = mof.supplier_id
                    AND t0.client_type ='O'
                    AND mof.mof_no =?
                    ORDER BY t0.procurement_date desc
         ", array($mofNo));
        return $results;
    }

    protected function getDetailSuppExp3($mofNo) {
        // Log::info(__METHOD__." >> ".$mofNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT  distinct   
                    t2.company_name,t2.supplier_id, mof.mof_no
                FROM 
                    sm_supplier t2,
                    sm_mof_account mof
                WHERE 
                    t2.supplier_id = mof.supplier_id
                    AND mof.mof_no =?
         ", array($mofNo));
        return $results;
    }

    protected function getDetailWHOreject($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT qt.qt_no, mof.MOF_NO, sup.COMPANY_NAME, r.remark, u.login_id, u.user_name, r.created_date
    FROM sc_qt qt, sc_qt_approval a, sc_remark r, pm_user u, sm_personnel p, sm_appl app, sm_supplier sup, sm_mof_account mof
    WHERE 
        qt.QT_ID=a.QT_ID 
        and a.qt_approval_id = r.doc_id
        and a.APPROVAL_CATEGORY_ID=337
        and r.doc_type = 'QTA'
        and r.created_by = u.user_id
        and u.ORG_TYPE_ID=15
        and qt.QT_NO = ?
        and u.USER_ID = p.USER_ID
        and p.APPL_ID = app.APPL_ID
        and app.APPL_ID = sup.LATEST_APPL_ID
        and sup.SUPPLIER_ID = mof.SUPPLIER_ID
    ORDER BY r.created_date DESC
         ", array($docNo));
        return $results;
    }

    protected function getOutputExtendQT($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = DB::connection('oracle_bpm_rpt')->select("SELECT soaa.customattributestring1 doc_no,
         soaa.customattributestring2 doc_type,
         soaa.customattributenumber2 status_id, soaa.activityname,
         soab.assignee login_id,  soaa.createddate,
         soaa.compositeinstanceid,
         soaa.state,soaa.outcome, 
         COALESCE(soaa.state,soaa.outcome) AS testing
         FROM     wftask soaa, wfassignee soab
         WHERE soaa.taskid = soab.taskid
            AND soaa.customattributestring1 IN ('QT190000000046882')
            AND soaa.swimlanerole = 'Supplier'
         ORDER BY soaa.createddate DESC
          ", array($docNo));
        return $query;
    }

    // </editor-fold>
    // <editor-fold defaultstate="collapsed" desc="REGION USER STUCK">
    /*     * Akmal add for stuck user info--> */
    protected function getDetailSummaryStuckInfo($icNo) {
        // Log::info(__METHOD__." >> ".$icNo);
        $query = "
    SELECT
        A.USER_ID, A.LOGIN_ID, A.USER_NAME, A.EMAIL, b.org_profile_id as orgprofileid, b.user_org_id
    FROM
        PM_USER A, pm_user_org b, pm_org_validity d,  pm_org_profile prof
    WHERE
        a.record_status = 1
        and d.record_status = 1
        and b.record_status = 1
        and a.user_id(+) = b.user_id
        and b.org_profile_id = d.org_profile_id(+)
        and prof.org_profile_id = b.org_profile_id
        and a.identification_no = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($icNo));
    }

    /*     * Akmal add for stuck user role--> */

    protected function getDetailSummaryStuckRole($icNo) {
        // Log::info(__METHOD__." >> ".$icNo);
        $query = "
    SELECT
        d.org_code,d.org_name,par.code_name,e.role_name, a.created_date, a.CHANGED_DATE
    FROM
        pm_user a, pm_user_org b, pm_user_role c, pm_org_validity d, pm_parameter_desc par, pm_org_profile prof, pm_role_desc e
    WHERE
        a.user_id(+) = b.user_id
        and c.user_org_id(+) = b.user_org_id
        and b.org_profile_id = d.org_profile_id(+)
        and c.role_code = e.role_code
        and par.parameter_id = prof.org_type_id
        and prof.org_profile_id = b.org_profile_id
        and a.record_status = 1
        and b.record_status = 1
        and c.record_status = 1
        and d.record_status = 1
        and e.language_code = 'ms'
        and a.identification_no = ?
        and par.language_code = 'en'
        ORDER BY b.USER_ORG_ID
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($icNo));
    }

    /*     * Akmal add forinfo stuck task--> */ 
    protected function getDetailSummaryStuckTask($icNo) {
        // Log::info(__METHOD__." >> ".$icNo);
        $query = "
        SELECT 
            kk.qtno, 
            LISTAGG(kk.jawatankuasa, ', ') WITHIN GROUP (ORDER BY kk.qtno) AS jawatankuasa,
            kk.status_qt,
            kk.deskofficer,
            kk.kementerian,
            kk.kumpulanptj,
            kk.milikptj,
            kk.untukptj,
            kk.state_name,
            kk.validity,
            kk.qt_title
        FROM (
            SELECT DISTINCT
                (sq.qt_no || ' | ' || DECODE(sq.procurement_mode_id, 185, 'Quotation', 186, 'Tender')) AS qtno,
                sq.proposal_validity_end_date as validity,
                (PP2.CODE_NAME || ' - ' || P2.CODE_NAME) AS jawatankuasa,
                (sd.status_id || ' | ' || sd.status_name) AS status_qt,
                (SELECT (a.login_id || ' | ' || b.USER_NAME)
                FROM pm_user a, sc_actor b
                WHERE a.USER_ID = b.USER_ID
                AND b.doc_ID = sq.QT_ID
                AND b.ROLE_CODE = 'DESK_OFFICER'
                AND b.DOC_TYPE = 'QT') AS deskofficer,
                vm.org_name AS kementerian,
                vj.org_name AS kumpulanptj,
                (vp.org_code || ' | ' || vp.org_name) AS milikptj,
                (vp1.org_code || ' | ' || vp1.ORG_NAME) AS untukptj,
                (SELECT NVL(MAX(s.state_name), 'N/A')
                FROM pm_state s
                WHERE s.state_id IN (
                    SELECT adr.state_id
                    FROM pm_address adr
                    WHERE adr.address_id IN (
                        SELECT pat.address_id
                        FROM pm_address_type pat
                        WHERE pat.address_type = 'B'
                        AND pat.record_status = 1
                        AND pat.org_profile_id = sq.org_profile_id
                    )
                )) AS state_name,
                sq.qt_title
            FROM
                sc_qt sq
                JOIN pm_org_validity vp ON sq.owner_org_profile_id = vp.org_profile_id
                JOIN pm_org_profile pp ON vp.org_profile_id = pp.org_profile_id
                JOIN pm_org_validity vj ON pp.parent_org_profile_id = vj.org_profile_id
                JOIN pm_org_profile pj ON vj.org_profile_id = pj.org_profile_id
                JOIN pm_org_validity vpp ON pj.parent_org_profile_id = vpp.org_profile_id
                JOIN pm_org_profile ppp ON vpp.org_profile_id = ppp.org_profile_id
                JOIN pm_org_validity vm ON ppp.parent_org_profile_id = vm.org_profile_id
                JOIN pm_org_profile pm ON vm.org_profile_id = pm.org_profile_id
                JOIN sc_workflow_status s ON sq.qt_id = s.doc_id
                JOIN pm_status_desc sd ON s.status_id = sd.status_id
                JOIN pm_org_validity vp1 ON sq.ORG_PROFILE_ID = vp1.ORG_PROFILE_ID
                JOIN pm_user pu ON sq.created_by = pu.user_id
                JOIN sc_actor x ON x.user_id = pu.user_id AND x.doc_type = 'QT' AND x.role_code = 'DESK_OFFICER'
                JOIN pm_parameter p1 ON pp.org_type_id = p1.parameter_id
                JOIN SC_QT_COMMITTEE A ON sq.qt_id = A.QT_ID
                JOIN SC_COMMITTEE B ON A.COMMITTEE_ID = B.COMMITTEE_ID
                JOIN PM_PARAMETER P10 ON B.COMMITTEE_TYPE_ID = P10.PARAMETER_ID
                JOIN PM_PARAMETER_DESC P2 ON P10.PARAMETER_ID = P2.PARAMETER_ID
                JOIN SC_COMMITTEE_MEMBER C ON B.COMMITTEE_ID = C.COMMITTEE_ID
                JOIN PM_PARAMETER PP1 ON C.MEMBER_ROLE_ID = PP1.PARAMETER_ID
                JOIN PM_PARAMETER_DESC PP2 ON PP1.PARAMETER_ID = PP2.PARAMETER_ID
                JOIN PM_PARAMETER_TYPE PP3 ON PP1.PARAMETER_TYPE = PP3.PARAMETER_TYPE
            WHERE
                vp.record_status = 1
                AND vp1.record_status = 1
                AND vj.record_status = 1
                AND vpp.record_status = 1
            AND vm.record_status = 1
                AND vp.exp_date > SYSDATE
            AND sd.language_code = 'en'
            AND P2.LANGUAGE_CODE = 'en'
                AND PP2.LANGUAGE_CODE = 'en'
            AND C.IC_PASSPORT = ?
            AND s.created_date IN (
                    SELECT MAX(x.created_date)
                    FROM sc_workflow_status x
                    WHERE sq.qt_id = x.doc_id
                    AND x.doc_type IN ('QT', 'CMT', 'TEC', 'OC', 'FEC', 'EC', 'BD')
                )
                AND
                sq.qt_no IN (
                            SELECT t2.qt_no
        FROM sc_qt t2
        JOIN sc_qt_committee t1 ON t1.qt_id = t2.qt_id
        JOIN sc_committee t0 ON t0.committee_id = t1.committee_id
        JOIN sc_committee_member t3 ON t3.committee_id = t0.committee_id
        JOIN sc_workflow_status t4 ON t4.doc_id = t2.qt_id
        JOIN pm_parameter t5 ON t5.parameter_id = t0.committee_type_id
        WHERE t4.doc_type = 'QT'
        AND t3.ic_passport = ?
        AND t4.is_current = 1
        AND (t0.status_id IN (60202, 60200))
        AND (
            (t5.parameter_code = 'SC' AND t4.status_id IN (60003, 60025, 60026, 60028, 60037, 60027, 60034, 60004, 60029, 60030, 60039, 60031, 60005, 60038, 60006, 60007, 60040, 60035, 60051))
            OR (t5.parameter_code IN ('EC', 'OC', 'TEC', 'FEC') AND t4.status_id IN (60029, 60030, 60039, 60031, 60005, 60038, 60006, 60007, 60040, 60035, 60008, 60009, 60036, 60022, 60010, 60051))
            OR (t5.parameter_code IN ('QCA', 'QCB', 'PBA', 'PBB', 'PPB', 'FSB', 'FSW') AND t4.status_id IN (60011, 60016, 60012, 60017, 60013))
        )   	
                )
            GROUP BY
                sq.file_no,
                sq.qt_title,
                sq.qt_no,
                pu.login_id,
                x.user_name,
                sq.proposal_validity_end_date,
                sd.status_name,
                sd.status_id,
                sq.qt_id,
                sq.org_profile_id,
                sq.procurement_mode_id,
                vp.org_name,
                vp.org_code,
                vj.org_name,
                vj.org_code,
                vpp.org_name,
                vpp.org_code,
                vm.org_name,
                vm.org_code,
                pu.login_id,
                pu.user_name,
                sq.org_profile_id,
                vp1.ORG_CODE,
                vp1.ORG_NAME,
                PP2.CODE_NAME,
                P2.CODE_NAME
        ) kk
        GROUP BY 
            kk.qtno, 
            kk.status_qt, 
            kk.deskofficer, 
            kk.kementerian, 
            kk.kumpulanptj, 
            kk.milikptj, 
            kk.untukptj, 
            kk.state_name, 
            kk.validity,
            kk.qt_title
     ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($icNo,$icNo));
    }

    protected function getListQTsubmitproposal_mofsupplier($mofNo) {
        // Log::info(__METHOD__." >> ".$mofNo);
        $query = "
            SELECT c.mof_no, c.supplier_name, a.qt_no, c.proposal_no, c.proposal_submit_date,
            DECODE (c.is_submitted, 0, 'NO', 1, 'YES') as submitted,
            (SELECT s.status_name
                FROM pm_status_desc s
                WHERE wsla.status_id = s.status_id
                  AND language_code = 'en') as qtstatus
            FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sc_workflow_status wsla
            WHERE a.qt_id = b.qt_id
              and b.qt_supplier_id = c.qt_supplier_id
              and a.QT_ID = wsla.DOC_ID
              and wsla.DOC_TYPE = 'QT'
              and wsla.IS_CURRENT = 1
              and c.is_submitted = 1
              and c.mof_no = :mof_no
              order by 1 desc
        ";
    
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, ['mof_no' => $mofNo]);
    }

    protected function getListQTnotsubmitproposal_mofsupplier($mofNo) {
        // Log::info(__METHOD__." >> ".$mofNo);
        $query = "
            SELECT c.mof_no, c.supplier_name, a.qt_no, c.proposal_no, c.proposal_submit_date,
            DECODE (c.is_submitted, 0, 'NO', 1, 'YES') as submitted,
            (SELECT s.status_name
                FROM pm_status_desc s
                WHERE wsla.status_id = s.status_id
                  AND language_code = 'en') as qtstatus
            FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sc_workflow_status wsla
            WHERE a.qt_id = b.qt_id
              and b.qt_supplier_id = c.qt_supplier_id
              and a.QT_ID = wsla.DOC_ID
              and wsla.DOC_TYPE = 'QT'
              and wsla.IS_CURRENT = 1
              and c.is_submitted = 0
              and c.mof_no = :mof_no
              order by 1 desc
        ";
    
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, ['mof_no' => $mofNo]);
    }

    protected function getDetailVerifySupplierLulus($qtNo, $qtKey) {
        // Log::info(__METHOD__." >> ".$qtNo. " >> qtKey : "+$qtKey);
        $query = "
        SELECT distinct a.qt_no, b.supplier_id, c.supplier_name, c.mof_no, c.supplier_no,
        e.is_qualified, c.proposal_no, c.is_submitted, c.proposal_submit_date,
        d.eval_stage, e.total_score, e.eval_ranking, h.item_name,
        x.NAME AS mof_supplier_admin,
        scb.phone_country || scb.phone_area || scb.phone_no nophone, 
        sad.address_1, sad.address_2, sad.address_3, sad.postcode, 
        pms.state_name,
             (SELECT city.city_name
            FROM pm_city city
           WHERE city.city_id = sad.city_id AND city.record_status = 1) city_name,
         (SELECT district.district_name
            FROM pm_district district
           WHERE district.district_id = sad.district_id
             AND district.record_status = 1) district_name, 
          (SELECT division.division_name
            FROM pm_division division
           WHERE division.division_id = sad.division_id
             AND division.record_status = 1) division_name
 FROM   sc_qt a JOIN sc_qt_supplier b ON a.qt_id = b.qt_id
        LEFT JOIN sc_qt_proposal c ON b.qt_supplier_id = c.qt_supplier_id
        LEFT JOIN sc_qt_eval_result d ON c.qt_proposal_id = d.qt_proposal_id
        LEFT JOIN sc_qt_eval_result_dtl e ON d.qt_eval_result_id = e.qt_eval_result_id
        LEFT JOIN sc_request_item h ON e.request_item_id = h.request_item_id
        JOIN sm_supplier i ON b.supplier_id = i.supplier_id
        JOIN sm_appl sa ON i.latest_appl_id = sa.appl_id
        JOIN sm_company_basic scb ON sa.appl_id = scb.appl_id
        JOIN sm_company_address sca ON scb.company_basic_id = sca.company_basic_id
        JOIN sm_address sad ON sca.address_id = sad.address_id
        JOIN sm_personnel x ON x.appl_id = i.latest_appl_id
        JOIN pm_state pms ON pms.state_id = sad.state_id
  WHERE c.is_submitted = 1
    AND e.is_qualified = 1
    AND x.ep_role = 'MOF_SUPPLIER_ADMIN'
    AND a.qt_no = ?
    AND d.eval_stage = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo, $qtKey));
    }

    protected function getDetailVerifySupplierGagal($qtNo, $qtKey) {
        // Log::info(__METHOD__." >> ".$qtNo. " >> qtKey : "+$qtKey);
        $query = "
        SELECT distinct a.qt_no, b.supplier_id, c.supplier_name, c.mof_no, c.supplier_no,
        e.is_qualified, c.proposal_no, c.is_submitted, c.proposal_submit_date,
        d.eval_stage, e.total_score, e.eval_ranking, h.item_name,
        x.NAME AS mof_supplier_admin,
        scb.phone_country || scb.phone_area || scb.phone_no nophone, 
        sad.address_1, sad.address_2, sad.address_3, sad.postcode, 
        pms.state_name,
             (SELECT city.city_name
            FROM pm_city city
           WHERE city.city_id = sad.city_id AND city.record_status = 1) city_name,
         (SELECT district.district_name
            FROM pm_district district
           WHERE district.district_id = sad.district_id
             AND district.record_status = 1) district_name, 
          (SELECT division.division_name
            FROM pm_division division
           WHERE division.division_id = sad.division_id
             AND division.record_status = 1) division_name
 FROM   sc_qt a JOIN sc_qt_supplier b ON a.qt_id = b.qt_id
        LEFT JOIN sc_qt_proposal c ON b.qt_supplier_id = c.qt_supplier_id
        LEFT JOIN sc_qt_eval_result d ON c.qt_proposal_id = d.qt_proposal_id
        LEFT JOIN sc_qt_eval_result_dtl e ON d.qt_eval_result_id = e.qt_eval_result_id
        LEFT JOIN sc_request_item h ON e.request_item_id = h.request_item_id
        JOIN sm_supplier i ON b.supplier_id = i.supplier_id
        JOIN sm_appl sa ON i.latest_appl_id = sa.appl_id
        JOIN sm_company_basic scb ON sa.appl_id = scb.appl_id
        JOIN sm_company_address sca ON scb.company_basic_id = sca.company_basic_id
        JOIN sm_address sad ON sca.address_id = sad.address_id
        JOIN sm_personnel x ON x.appl_id = i.latest_appl_id
        JOIN pm_state pms ON pms.state_id = sad.state_id
  WHERE c.is_submitted = 1
    AND e.is_qualified = 0
    AND x.ep_role = 'MOF_SUPPLIER_ADMIN'
    AND a.qt_no = ?
    AND d.eval_stage = ?

         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo, $qtKey));
    }

    //check supplier submit proposal
    protected function getListSubmitProposal($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
            d.latest_appl_id, a.qt_no, c.proposal_no,  c.mof_no,
            DECODE (c.is_submitted, 0, 'NO', 1, 'YES') submitted,
            c.supplier_name, c.changed_date, c.proposal_submit_date
        FROM
           sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sm_supplier d
        WHERE
            a.qt_id = b.qt_id
            and b.qt_supplier_id = c.qt_supplier_id
            and b.supplier_id = d.supplier_id
            and c.is_submitted =1
            and a.qt_no = ?
        order by c.proposal_submit_date desc
        ", array($docNo));
        return $results;
    }

    //check supplier tak submit proposal
    protected function getListNotSubmitProposal($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
        SELECT
            d.latest_appl_id, a.qt_no, c.proposal_no,  c.mof_no,
            DECODE (c.is_submitted, 0, 'NO', 1, 'YES') submitted,
            c.supplier_name, c.changed_date, c.proposal_submit_date
        FROM
           sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sm_supplier d
        WHERE
            a.qt_id = b.qt_id
            and b.qt_supplier_id = c.qt_supplier_id
            and b.supplier_id = d.supplier_id
            and c.is_submitted =0
            and a.qt_no = ?
        order by c.changed_date desc
        ", array($docNo));
        return $results;
    }

    //check supplier RESPOND PROPOSAL
    protected function ListRespondProposal($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    select count(*) as total
    FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sm_supplier d
    WHERE a.qt_id = b.qt_id
        and b.qt_supplier_id = c.qt_supplier_id
        and b.supplier_id = d.supplier_id
        and a.qt_no = ?
        ", array($docNo));
        return $results;
    }

    //check supplier SUBMIT PROPOSAL
    protected function ListSubmitProposal($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    select count(*) as total
    FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sm_supplier d
    WHERE a.qt_id = b.qt_id
        and b.qt_supplier_id = c.qt_supplier_id
        and b.supplier_id = d.supplier_id
        and c.is_submitted = 1
        and a.qt_no = ?
        ", array($docNo));
        return $results;
    }

    //check supplier TAK SUBMIT PROPOSAL
    protected function ListNotSubmitProposal($docNo) {
        // Log::info(__METHOD__." >> ".$docNo);
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "
    select count(*) as total
    FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, sm_supplier d
    WHERE a.qt_id = b.qt_id
        and b.qt_supplier_id = c.qt_supplier_id
        and b.supplier_id = d.supplier_id
        and c.is_submitted = 0
        and a.qt_no = ?
        ", array($docNo));
        return $results;
    }

    //check pengalaman supplier
    protected function getPengalaman($applID) {
        // Log::info(__METHOD__." >> ".$applID);
        $query = "
    SELECT a.appl_id, l1.category_l1_code
       || l2.category_l2_code
       || l3.category_l3_code AS categorycode,
       a.title,a.client_name,a.contract_value,a.progress_status
       --a.*, b.*
    FROM sm_company_experience a,
       sm_company_experience_cat b,
       pm_category_l1 l1,
       pm_category_l2 l2,
       pm_category_l3 l3
    WHERE a.company_experience_id = b.company_experience_id
       and l1.category_l1_id = b.category_l1_id
       and l2.category_l2_id = b.category_l2_id
       and l3.category_l3_id = b.category_l3_id
       and appl_id = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($applID));
    }

    protected function QTCheckInvitation($docno) {
        // Log::info(__METHOD__." >> ".$docNo);
        $query = "
    select count(*) as totalinv
    from sc_qt qt, sc_qt_supplier qts, SC_WORKFLOW_STATUS ws, PM_STATUS_DESC s
    where qt.QT_ID = qts.QT_ID(+)
    and qt.QT_ID = ws.DOC_ID
    and ws.STATUS_ID = s.STATUS_ID
    and s.LANGUAGE_CODE = 'en'
    and ws.IS_CURRENT = 1
    and ws.created_date IN (SELECT MAX (x.created_date)
                          FROM sc_workflow_status x
                            WHERE qt.qt_id = x.doc_id
                            --AND s.IS_CURRENT = 0
                            AND x.doc_type = 'QT')
    and qt.QT_NO = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docno));
    }

    //check success LOA from QT
    protected function getSuccessLOA($qtNo) {
        $query = "
   select d.qt_no, a.qt_id,g.loa_no,h.supplier_name, e.item_name, b.record_status, c.is_cancelled,c.order_qty, 
        b.qt_proposal_item_id, b.is_loi_awarded, b.is_loa_awarded
    from 
        sc_qt_finalization a, sc_qt_recommend_dtl b, sc_qt_recommend c, sc_qt 
        d, sc_request_item e, sc_loi_loa_item f, sc_loa g, sc_loi_loa h
    where d.qt_id = a.qt_id
        and h.loi_loa_id = g.loi_loa_id
        and c.qt_finalization_id = a.qt_finalization_id
        and c.qt_recommend_id = b.qt_recommend_id
        and c.request_item_id = f.request_item_id
        and f.request_item_id = e.request_item_id
        and f.loa_id = g.loa_id
        and d.qt_no = ?
        and b.record_status = 1
        and c.is_cancelled = 0
        and b.is_loa_awarded = 1
        order by LOA_NO desc
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo));
    }

     protected function getPendingLOA($qtNo) {
        $query = "
    select d.qt_no, a.qt_id,g.loa_no,e.item_name,h.supplier_name, b.record_status,  c.is_cancelled,c.order_qty, 
        b.qt_proposal_item_id, b.is_loi_awarded, b.is_loa_awarded
    from 
        sc_qt_finalization a, sc_qt_recommend_dtl b, sc_qt_recommend c, sc_qt 
        d, sc_request_item e, sc_loi_loa_item f, sc_loa g, sc_loi_loa h
    where d.qt_id = a.qt_id
        and h.loi_loa_id = g.loi_loa_id
        and c.qt_finalization_id = a.qt_finalization_id
        and c.qt_recommend_id = b.qt_recommend_id
        and c.request_item_id = f.request_item_id
        and f.request_item_id = e.request_item_id
        and f.loa_id = g.loa_id
        and d.qt_no = ?
        and b.record_status = 1 
        and c.is_cancelled = 0 
        and b.is_loa_awarded is null
        order by LOA_NO desc
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo));
    }
    
    //check rejcted by supplier & cancel item by PTJ - LOA from QT
    protected function getRejectCancelLOA($qtNo) {
        $query = "
    select d.qt_no, a.qt_id,g.loa_no,e.item_name,b.record_status,  c.is_cancelled,c.order_qty, 
        b.qt_proposal_item_id, b.is_loi_awarded, b.is_loa_awarded
    from 
        sc_qt_finalization a, sc_qt_recommend_dtl b, sc_qt_recommend c, sc_qt d, sc_request_item e, sc_loi_loa_item f, sc_loa g
    where d.qt_id = a.qt_id
        and c.qt_finalization_id = a.qt_finalization_id
        and c.qt_recommend_id = b.qt_recommend_id
        and c.request_item_id = f.request_item_id
        and f.request_item_id = e.request_item_id
        and f.loa_id = g.loa_id
        and d.qt_no = ?
        and b.record_status = 4
        and c.is_cancelled = 1
        and b.is_loa_awarded = 0
    order by LOA_NO desc
         ";

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo));
    }

    //check rejcted by supplier & can continue to New LOA from QT (refinalization)
    protected function getRejectContinueLOA($qtNo) {
        $query = "
    select d.qt_no, a.qt_id,g.loa_no,e.item_name,b.record_status,  c.is_cancelled,c.order_qty, 
        b.qt_proposal_item_id, b.is_loi_awarded, b.is_loa_awarded
    from 
        sc_qt_finalization a, sc_qt_recommend_dtl b, sc_qt_recommend c, sc_qt d, sc_request_item e, sc_loi_loa_item f, sc_loa g
    where d.qt_id = a.qt_id
        and c.qt_finalization_id = a.qt_finalization_id
        and c.qt_recommend_id = b.qt_recommend_id
        and c.request_item_id = f.request_item_id
        and f.request_item_id = e.request_item_id
        and f.loa_id = g.loa_id
        and d.qt_no = ?
        and b.record_status = 4
        and c.is_cancelled = 0
    order by LOA_NO desc
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtNo));
    }

    // </editor-fold>
    // Changed it as old version on 29/6/2019 .
    protected function getSQNotClosingInBpmOldVersion() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select a.quote_id, a.quote_no, b.status_id, a.end_date,
            a.CREATED_DATE from sc_quote a
          left join sc_workflow_status b on a.quote_id = b.doc_id and b.doc_type = 'SQ'
          where end_date < sysdate
          and b.status_id = 60851 and b.is_current = 1
          and a.CREATED_DATE < (SELECT EFF_DATE FROM PM_BPM_VERSION where MODULE_COMPOSITE='default/SourcingDP' and REL_VERSION='1.0.7')
          order by end_date
         ");
        return $results;
    }

    // Changed it new version on 29/6/2019
    protected function getSQNotClosingInBpmNewVersion() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "select a.quote_id, a.quote_no, b.status_id, a.end_date,
            a.CREATED_DATE from sc_quote a
          left join sc_workflow_status b on a.quote_id = b.doc_id and b.doc_type = 'SQ'
          where end_date < sysdate
          and b.status_id = 60851 and b.is_current = 1
          and a.CREATED_DATE >= (SELECT EFF_DATE FROM PM_BPM_VERSION where MODULE_COMPOSITE='default/SourcingDP' and REL_VERSION='1.0.7')
          order by end_date
         ");
        return $results;
    }

    /**
     * Get list for dashboard Quotation Service Retry
     * @return list
     */
    protected function getDashboardQtServiceRetry() {
        /*
         SELECT
                sd.status_name,
                (select (a.parameter_code ||' - '|| b.code_name) as code_name from PM_PARAMETER a, PM_PARAMETER_DESC b
                    where a.parameter_id = b.parameter_id and a.parameter_type = 'QDS' and a.parameter_code =  dl.disruption_stage and b.language_code = 'en' ) as disruption_stage,
                count(*) as counts
              FROM SC_DISRUPTION_LOG dl
                INNER JOIN SC_QT q ON q.QT_ID = dl.doc_id AND doc_type = 'QT'
                INNER JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_TYPE = 'QT' AND ws.DOC_ID = q.QT_ID AND ws.IS_CURRENT = 1)
                INNER JOIN PM_STATUS_DESC sd ON (sd.status_id = ws.status_id  AND sd.language_code = 'en')
              WHERE TRUNC(dl.CREATED_DATE) = TRUNC(sysdate)
                    AND dl.IS_SUCCESS = 0
                    -- AND dl.DOC_TYPE = 'QT'
              GROUP BY sd.status_name,dl.disruption_stage
         */
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
                sd.status_name,
                ws.status_id,
                TRUNC(dl.CREATED_DATE) as disruption_date,
                dl.disruption_stage,
                (select (a.parameter_code ||' - '|| b.code_name) as code_name from PM_PARAMETER a, PM_PARAMETER_DESC b
                    where a.parameter_id = b.parameter_id and a.parameter_type = 'QDS' and a.parameter_code =  dl.disruption_stage and b.language_code = 'en' ) as disruption_stage_desc,
                count(*) as counts
              FROM SC_DISRUPTION_LOG dl
                INNER JOIN SC_QT q ON q.QT_ID = dl.doc_id AND doc_type = 'QT'
                INNER JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_TYPE = 'QT' AND ws.DOC_ID = q.QT_ID AND ws.IS_CURRENT = 1)
                INNER JOIN PM_STATUS_DESC sd ON (sd.status_id = ws.status_id  AND sd.language_code = 'en')
              WHERE dl.IS_SUCCESS = 0
              GROUP BY sd.status_name,ws.status_id,TRUNC(dl.CREATED_DATE),dl.disruption_stage 
              ORDER BY TRUNC(dl.CREATED_DATE) desc");
        return $results;
    }
    
    /**
     * Get list for dashboard Quotation Service Retry
     * @return list
     */
    protected function getListDashboardQtServiceRetryDetailsByStage($disruptionStage,$disruptionDate) {
        if($disruptionStage == null || strlen($disruptionStage)==0){
            $disruptionStage = 1;
        }
        
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT 
                q.qt_id,q.qt_no,ws.status_id,
                trunc(dl.created_date) as disruption_date,
                              sd.status_name as wf_qt_status,
                  dl.disruption_stage,
                              (select (a.parameter_code ||' - '|| b.code_name) as code_name from PM_PARAMETER a, PM_PARAMETER_DESC b
                                  where a.parameter_id = b.parameter_id and a.parameter_type = 'QDS' and a.parameter_code =  dl.disruption_stage and b.language_code = 'en' ) as disruption_stage,
                dl.error_msg                  
                            FROM SC_DISRUPTION_LOG dl
                              INNER JOIN SC_QT q ON q.QT_ID = dl.doc_id 
                              INNER JOIN SC_WORKFLOW_STATUS ws ON (ws.DOC_TYPE = 'QT' AND ws.DOC_ID = q.QT_ID AND ws.IS_CURRENT = 1)
                              INNER JOIN PM_STATUS_DESC sd ON (sd.status_id = ws.status_id  AND sd.language_code = 'en')
                            WHERE
                              dl.IS_SUCCESS = 0
                              AND dl.doc_type = 'QT'
                              AND dl.disruption_stage = ? 
                              AND to_char(dl.created_date,'YYYY-MM-DD') = ? 
                              order by dl.created_date desc", array($disruptionStage,$disruptionDate));
        return $results;
    }

    /**
     * Get list for dashboard Quotation Tender as Status Closed but Date Closing still not yet.
     * @return list
     */
    protected function getDashboardQtListStatusClosedTooEarly() {

        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT   a.qt_id, a.qt_no, b.status_id, c.status_name, b.created_date,
                    a.publish_date, a.closing_date, b.created_by
               FROM sc_qt a, sc_workflow_status b, pm_status_desc c
              WHERE a.qt_id = b.doc_id
                AND b.status_id = c.status_id
                AND b.is_current = 1
                AND c.language_code = 'ms'
                AND b.status_id > (60009)
                AND b.status_id NOT IN (60015)
                AND b.doc_type = 'QT'
                AND TRUNC (a.closing_date) = TRUNC (SYSDATE)
                AND (b.created_date > SYSDATE - 1 AND b.created_date < SYSDATE + 1)
                AND TO_CHAR (b.created_date, 'hh24:mm') < '12:00'
           ORDER BY b.created_date");

        return $results;
    }

    protected function getListStuckTaskPendingPRCRReviewFromIgfmas() {
        $year = Carbon::now()->year;
        $query = "select r.doc_type,r.doc_no,fo.DOC_NO as poco,w.status_id,w.is_current,w.created_date
                    from FL_FULFILMENT_REQUEST r, FL_WORKFLOW_STATUS w, FL_FULFILMENT_ORDER fo
                      where
                        r.fulfilment_req_id = fo.fulfilment_req_id
                      AND r.FULFILMENT_REQ_ID = w.doc_id
                      AND r.FINANCIAL_YEAR = ? 
                      AND w.STATUS_ID in (40610,40110)
                      AND w.doc_type in ('PR','CR')
                      and IS_CURRENT = 1
                      and trunc(w.CREATED_DATE) < trunc(sysdate - 7)
                      and fo.sap_order_no is null
                      order by w.CREATED_DATE asc";

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($year));
    }
    
    /**
     * Waiting service_code:EPP-017 from osb_logging
     */
    protected function getListStuckTaskPaymentQueryFromIgfmas() {
        $year = Carbon::now()->year;
        $query = "
                SELECT distinct pr.FULFILMENT_REQ_ID,po.fulfilment_order_id,
                  to_char (po.created_date, 'YYYY-MM-DD') order_created_date,
                  to_char (pr.created_date, 'YYYY-MM-DD') request_created_date,
                  pr.doc_no as request_doc_no,
                  pr.doc_type as request_doc_type,
                  s.status_name,
                  w.is_current,
                  w.status_id,
                  w.created_date as status_created_date,
                  to_char(w.created_date, 'YYYY-MM') as status_mm_created_date,
                  pr.ag_office_name,
                  pr.ag_office_id,
                  decode (pr.doc_type, 'PR', 'Purchase Request', 'CR', 'Contract Request') AS request_module,
                  po.doc_no,
                  pr.financial_year,
                  po.doc_type,
                  pa.payment_advice_no,
                  decode (po.doc_type, 'PO', 'Purchase Order', 'CO', 'Contract Order') AS MODULE
                FROM fl_fulfilment_request pr,
                fl_fulfilment_order po,
                FL_PAYMENT_ADVICE pa ,
                FL_WORKFLOW_STATUS w ,
                PM_STATUS_DESC s
                WHERE pr.fulfilment_req_id = po.fulfilment_req_id
                AND pa.fulfilment_order_id = po.fulfilment_order_id
                AND pa.payment_advice_id = w.doc_id
                AND pr.financial_year = ? 
                AND w.doc_type in ('PA')
                AND w.status_id = s.status_id
                AND s.language_code = 'en'
                AND w.status_id in (
                41527, -- Pending Payment Instruction QUERY Verification
                41526, -- Pending Payment Instruction QUERY FROM 1GFMAS
                41027, -- Pending Payment Instruction QUERY Verification
                41026 -- Pending Payment Instruction QUERY FROM 1GFMAS
                )
                AND w.is_current = 1
                AND trunc(w.created_date) <= trunc(sysdate-14)
                order by w.created_date asc ";

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($year));
    }

    protected function qtSiteVisitDetail($qtId) {
        $query = "
                select qt.qt_id, qt.qt_no, qt.qt_title, qt.created_date, 
                qt.publish_date, qt.publish_period, qt.closing_date,
                wf.status_id, wf.is_current, sd.status_name,
                trunc(sysdate) from sc_qt qt, sc_workflow_status wf, pm_status_desc sd
                where qt.qt_id = wf.doc_id
                and sd.status_id = wf.status_id
              --  and wf.status_id = 60008
                and wf.is_current = 1
                and qt.record_status = 1
                and wf.doc_type = 'QT'
                and sd.language_code = 'en' 
                and qt.qt_id = ?";

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($qtId));
    }

    protected function getDashboardSubmitQtTodayStatistic() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT    count(*) AS total_submit,max(c.proposal_submit_date) AS latest_submit_date
                        FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c
                    WHERE a.qt_id = b.qt_id
                        AND b.qt_supplier_id = c.qt_supplier_id
                        AND c.is_submitted = 1
                        AND TRUNC (c.proposal_submit_date) = trunc(sysdate)
                    ORDER BY c.proposal_submit_date DESC ");

        return $results;
    }


    /**
     * return list 
     */
    protected function getCurrentWorkflowSourcingQt($docNo,$docType)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT A');
        $result =  $query->join('SC_WORKFLOW_STATUS B', 'A.QT_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('B.IS_CURRENT', 1)
            ->where('B.DOC_TYPE', $docType)
            ->where('A.QT_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('B.DOC_TYPE','B.DOC_ID','A.QT_NO','C.STATUS_ID', 'D.STATUS_NAME')
            ->get();
        return $result;

    }

     /**
     * return list 
     */
    protected function listQtCancelledByYear($yearSearch,$monthSearch)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select("
        SELECT B.DOC_TYPE,B.DOC_ID,A.QT_NO,C.STATUS_ID, D.STATUS_NAME,B.CREATED_DATE 
        FROM SC_QT A 
        INNER JOIN SC_WORKFLOW_STATUS B ON A.QT_ID  = B.DOC_ID 
        INNER JOIN PM_STATUS C ON B.STATUS_ID = C.STATUS_ID
        INNER JOIN PM_STATUS_DESC D  ON C.STATUS_ID = D.STATUS_ID 
        WHERE D.LANGUAGE_CODE  = 'en' 
        AND B.IS_CURRENT  = 1 
        AND B.DOC_TYPE = 'QT' 
        AND TO_CHAR(B.CREATED_DATE,'YYYY') = '$yearSearch'
        -- AND TO_CHAR(B.CREATED_DATE,'MM') = '$monthSearch'
        AND B.STATUS_ID IN (
            '60015', -- Cancelled
            '60045', -- Quotation Has Been Cancelled Due To Proposal Validity Expired. Mof Approval Is Required For Further Action (If Any)	
            '60042', -- Proceed Manual Due To Ministry Restructuring	
            '60043', -- Cancelled due to PTJ Code Changed
            '60041', -- Cancel Due To Ministry Restructuring	
            '60046', -- QT Cancellation Request From User Approved By BPK,MoF
            '60047'  -- Quotation/Tender Closed by Agency Due To Validity Period Expired/Special Approval
            )
        ");
        return $result;

    }



    /**
     * return list 
     */
    protected function getCurrentWorkflowSourcingLoa($docNo,$docType)
    {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_LOA A');

        $result =  $query->join('SC_WORKFLOW_STATUS B', 'A.LOA_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('B.IS_CURRENT', 1)
            ->where('B.DOC_TYPE', $docType)
            ->where('A.LOA_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('B.DOC_TYPE','B.DOC_ID','A.LOA_NO','C.STATUS_ID', 'D.STATUS_NAME')
            ->get();
        return $result;

    }

    

    /**
     * return list 
     */
    protected function getCurrentWorkflowRequestNote($docNo,$docType)
    {
        return DB::connection('oracle_nextgen_rpt')
            ->table('SC_REQUEST_NOTE A')
            ->join('SC_WORKFLOW_STATUS B', 'A.REQUEST_NOTE_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('B.IS_CURRENT', 1)
            ->where('B.DOC_TYPE', $docType)
            ->where('A.REQUEST_NOTE_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('C.STATUS_ID', 'D.STATUS_NAME')
            ->get();
    }
}
