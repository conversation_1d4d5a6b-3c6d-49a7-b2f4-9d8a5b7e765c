<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use DB;
use App\Migrate\MigrateUtils;
use Mail;
use Config;


class ReportSupplierDisciplinaryAction {


    public static function run() {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        $reportStat = new ReportSupplierDisciplinaryAction;
        $reportStat->executeSupplierDisciplinaryAction();

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :'.  json_encode(MigrateUtils::getTakenTime($dtStartTime)));

    }

    /**
     * Execution parameter for last month.(Start Date to End Date)
     * @return type
     */
    protected function executeSupplierDisciplinaryAction() {

        MigrateUtils::logDump('executeSupplierDisciplinaryAction : entering ..');

        try {
            $listApplSuppliers = $this->queryVerifyApplicationSupplier();
            MigrateUtils::logDump('Total Result > queryVerifyApplicationSupplier : '.count($listApplSuppliers));

            $listQtProposalSuppliers = $this->queryVerifyQtProposalSupplier();
            MigrateUtils::logDump('Total Result > queryVerifyQtProposalSupplier : '.count($listQtProposalSuppliers));

            $this->sendEmailToEpApprover($listApplSuppliers,$listQtProposalSuppliers);
            
        } catch (Exception $ex) {
            MigrateUtils::logErrorDump($ex->getMessage());
            MigrateUtils::logErrorDump($ex->getTraceAsString());
            MigrateUtils::logErrorDump(json_encode($ex->getTrace()));
        }
    }

    protected function queryVerifyApplicationSupplier() {
        $query = "SELECT
                ss.COMPANY_NAME AS Nama_Syarikat,
                ss.REG_NO AS Nombor_Pendaftaran_SSM,
                sda.mof_no AS Nombor_Pendaftaran_MOF,
                sda.START_DATE AS Tarikh_Mula_DA,
                sda.END_DATE AS Tarikh_Tamat_DA,
                sda.REMARK AS Catatan,
                sa.appl_no AS Nombor_Permohonan,
                (
                SELECT
                    status_name
                FROM
                    pm_status_desc
                WHERE
                    status_id = sa.STATUS_ID
                    AND LANGUAGE_CODE = 'ms') AS Status_Permohonan
            FROM
                SM_DISCIPLINARY_ACTION sda,
                sm_supplier ss,
                sm_appl sa
            WHERE
                sda.SUPPLIER_ID = ss.SUPPLIER_ID
                --AND supplier_id = 103440
                AND ss.supplier_id = sa.SUPPLIER_ID
                AND sa.is_active_appl = 1
                AND sa.status_id IN (20102, 20108)
                AND trunc(sa.CHANGED_DATE) = trunc(SYSDATE)
                AND sda.END_DATE > sysdate
            ORDER BY
                sda.END_DATE";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);
        return $results;
    }

    protected function queryVerifyQtProposalSupplier() {

        $query = "SELECT
        DISTINCT 
        ss.COMPANY_NAME AS Nama_Syarikat,
        ss.REG_NO AS Nombor_Pendaftaran_SSM,
        sda.mof_no AS Nombor_Pendaftaran_MOF,
        qp.PROPOSAL_NO AS Nombor_Cadangan,
        qt.QT_NO AS Nombor_Dokumen_QT,
        vm.org_code AS Kod_Kementerian,
        vp.org_code AS Kod_PTJ,
        vp.org_name AS Nama_PTJ,
        (
        SELECT
            pd.code_name
        FROM
            pm_parameter_desc pd
        WHERE
            qt.procurement_mode_id = pd.parameter_id
            AND pd.language_code = 'ms') AS Jenis,
        
        s.STATUS_NAME AS Status_QT,
        qt.CLOSING_DATE AS Tarikh_Tutup_QT,
        qt.PROPOSAL_VALIDITY_END_DATE AS Tarikh_Tamat_Tempoh_Sahlaku,
        pu.USER_NAME AS Urusetia,
        pu.EMAIL AS Emel,
        pu.MOBILE_COUNTRY||pu.MOBILE_AREA||pu.MOBILE_NO AS Telefon_Bimbit,
        pu.phone_COUNTRY||pu.phone_AREA||pu.phone_NO AS Telefon,
        sda.START_DATE AS Tarikh_Mula_DA,
		sda.END_DATE AS Tarikh_Tamat_DA,
		sda.REMARK AS Catatan
    FROM
        sc_qt qt,
        SC_QT_SUPPLIER qs,
        SC_QT_PROPOSAL qp,
        SC_WORKFLOW_STATUS ws,
        PM_STATUS_DESC s,
        pm_org_validity vp,
        pm_org_profile pp,
        pm_org_validity vj,
        pm_org_profile pj,
        pm_org_validity vpp,
        pm_org_profile ppp,
        pm_org_validity vm,
        pm_org_profile pm,
        SM_DISCIPLINARY_ACTION sda,
        pm_user pu,
        sm_supplier ss
    WHERE
        qt.QT_ID = qs.QT_ID
        AND sda.SUPPLIER_ID = ss.SUPPLIER_ID 
        AND qt.CREATED_BY = pu.user_id
        AND qp.MOF_NO = sda.MOF_NO
        AND sda.END_DATE > sysdate
        AND qs.QT_SUPPLIER_ID = qp.QT_SUPPLIER_ID
        AND qt.QT_ID = ws.DOC_ID
        AND ws.DOC_TYPE = 'QT'
        --AND qp.MOF_NO IN ( '357-02080518', '357-02185785', '357-02183553', '357-02273318', '357-02048052', '357-02232842', '357-02136426', '357-02257045', '357-00051844', '357-02247307', '357-02237257', '357-02270612', '357-02234296', '357-02155044', '357-02202788', '357-02169796', '357-02265803', '357-02221517', '357-02238427', '357-02259029', '357-02259028', '357-02130814', '357-02232347', '357-02226137', '357-02065765' )
        AND ws.IS_CURRENT = 1
        AND ws.STATUS_ID NOT IN (60014, 60015, 60045, 60041, 60042)
        AND ws.STATUS_ID = s.STATUS_ID
        AND s.LANGUAGE_CODE = 'en'
        AND qt.OWNER_ORG_PROFILE_ID = vp.ORG_PROFILE_ID
        AND vp.ORG_PROFILE_ID = pp.ORG_PROFILE_ID
        AND pp.parent_org_profile_id = vj.org_profile_id
        AND vj.org_profile_id = pj.org_profile_id
        AND pj.parent_org_profile_id = vpp.org_profile_id
        AND vpp.org_profile_id = ppp.org_profile_id
        AND ppp.parent_org_profile_id = vm.org_profile_id
        AND vm.org_profile_id = pm.org_profile_id
        --and ws.CREATED_DATE in (select max (x.CREATED_DATE) from sc_workflow_status x where qt.qt_id = x.doc_id and x.doc_type = 'QT')
        AND vp.record_status = 1
        AND vj.record_status = 1
        AND vpp.record_status = 1
        AND vm.record_status = 1
        AND sda.DISCIPLINARY_ACTION_ID = (
            SELECT
                max(DISCIPLINARY_ACTION_ID)
            FROM
                sm_DISCIPLINARY_ACTION
            WHERE
                supplier_id = sda.SUPPLIER_ID
        )
        AND trunc(qt.CLOSING_DATE) = trunc(sysdate)
        AND qp.IS_SUBMITTED = 1 ORDER BY qt.CLOSING_DATE ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);
        return $results;
    }

    protected function sendEmailToEpApprover($listApplSuppliers,$listQtProposalSuppliers) {
        $dateToday  = Carbon::now()->format('m/d/Y');
        // remove email '<EMAIL>'  -> pindah 24/11/2023
        // Request add email for LAPORAN SEMAKAN PADA PEMBEKAL TINDAKAN TATATERTIB #0000010544 -> 31/01/2025
        // Request add email for LAPORAN SEMAKAN PADA PEMBEKAL TINDAKAN TATATERTIB #0000010576 -> 04/02/2025 (<EMAIL>)
        $data = array(
            "to" => [
                    '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>',
                    '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'],
            "subject" => 'LAPORAN SEMAKAN PADA PEMBEKAL TINDAKAN TATATERTIB PADA '.$dateToday
        );

        $result = [
            'subject' => $data["subject"],
            'date' => Carbon::now()->toDateString(),
            'listApplSuppliers' => $listApplSuppliers,
            'listQtProposalSuppliers' => $listQtProposalSuppliers
        ];

        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mail = $mailer
                ->send('emails.reportSupplierDisciplinaryAction', $result, function ($m) use ($data) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($data["to"]);
                    $m->subject($data["subject"]);
                });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__METHOD__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }
    }
}
