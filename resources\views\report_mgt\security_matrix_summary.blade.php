@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
            <i class="gi gi-charts"></i>Security Matrix Summary<br><small>(Production Middleware Support)</small>
        </h1>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <!-- Filter Section -->
            <div class="panel panel-default">
                <div class="panel-body">
                    <form method="GET" action="#" class="form-inline mb-3">
                        <div class="form-group mr-2">
                            <label for="year" class="mr-2">Year</label>
                            <select name="year" id="year" class="form-control"></select>
                        </div>
                        <div class="form-group mr-2" style="padding: 0 10px;">
                            <label for="month" class="mr-2">Month</label>
                            <select name="month" id="month" class="form-control"></select>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Row 1: AP511 and APIVE Tables -->
            <div class="row">
                <div class="col-md-6">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>ePerolehan iGFMAS Integration Close Payment (AP511)</strong></h2>
                        </div>
                        <div class="table-responsive">
                            <div class="spinner-container" id="ap511_spinner" style="padding: 20px;"><i
                                    class="fa fa-spinner fa-4x fa-spin"></i></div>
                            <table class="table table-bordered fixed-table" id="ap511_table">
                                <tbody>
                                    <tr>
                                        <td>Month</td>
                                        <td data-field="month"></td>
                                    </tr>
                                    <tr>
                                        <td>(A) Total number of AP511 received</td>
                                        <td data-field="total_received"></td>
                                    </tr>
                                    <tr>
                                        <td>(B) Total number of AP511 processed</td>
                                        <td data-field="total_processed"></td>
                                    </tr>
                                    <tr>
                                        <td>Total files processed delay in 24 hours</td>
                                        <td data-field="total_exceed"></td>
                                    </tr>
                                    <tr>
                                        <td>Actual % (A/B x 100)</td>
                                        <td data-field="percentage"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>ePerolehan iGFMAS Integration Master Data Vendor (APIVE)</strong></h2>
                        </div>
                        <div class="table-responsive">
                            <div class="spinner-container" id="apive_spinner" style="padding: 20px;"><i
                                    class="fa fa-spinner fa-4x fa-spin"></i></div>
                            <table class="table table-bordered fixed-table" id="apive_table">
                                <tbody>
                                    <tr>
                                        <td>Month</td>
                                        <td data-field="month"></td>
                                    </tr>
                                    <tr>
                                        <td>(A) Total number of APIVE generated</td>
                                        <td data-field="total_received"></td>
                                    </tr>
                                    <tr>
                                        <td>(B) Total number of APIVE sent to iGFMAS</td>
                                        <td data-field="total_processed"></td>
                                    </tr>
                                    <tr>
                                        <td>Total files processed delay in 24 hours</td>
                                        <td data-field="total_exceed"></td>
                                    </tr>
                                    <tr>
                                        <td>Actual % (A/B x 100)</td>
                                        <td data-field="percentage"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Row 2: Trustgate and Digicert Tables -->
            <div class="row">
                <div class="col-md-6">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>ePerolehan SPKI Trustgate (Signing Service)</strong></h2>
                        </div>
                        <div class="table-responsive">
                            <div class="spinner-container" id="trustgate_spinner" style="padding: 20px;"><i
                                    class="fa fa-spinner fa-4x fa-spin"></i></div>
                            <table class="table table-bordered fixed-table" id="trustgate_table">
                                <tbody>
                                    <tr>
                                        <td>Month</td>
                                        <td data-field="month"></td>
                                    </tr>
                                    <tr>
                                        <td>(A) Total Attempt</td>
                                        <td data-field="total_attempt"></td>
                                    </tr>
                                    <tr>
                                        <td>(B) Total Failed</td>
                                        <td data-field="total_failed"></td>
                                    </tr>
                                    <tr>
                                        <td>Actual Success % (A/B x 100)</td>
                                        <td data-field="success_percentage"></td>
                                    </tr>
                                    <tr>
                                        <td>SLA < 10%</td>
                                        <td data-field="sla_percentage"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>ePerolehan SPKI Digicert (Signing Service)</strong></h2>
                        </div>
                        <div class="table-responsive">
                            <div class="spinner-container" id="digicert_spinner" style="padding: 20px;"><i
                                    class="fa fa-spinner fa-4x fa-spin"></i></div>
                            <table class="table table-bordered fixed-table" id="digicert_table">
                                <tbody>
                                    <tr>
                                        <td>Month</td>
                                        <td data-field="month"></td>
                                    </tr>
                                    <tr>
                                        <td>(A) Total Attempt</td>
                                        <td data-field="total_attempt"></td>
                                    </tr>
                                    <tr>
                                        <td>(B) Total Failed</td>
                                        <td data-field="total_failed"></td>
                                    </tr>
                                    <tr>
                                        <td>Actual Success % (A/B x 100)</td>
                                        <td data-field="success_percentage"></td>
                                    </tr>
                                    <tr>
                                        <td>SLA < 10%</td>
                                        <td data-field="sla_percentage"></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('cssprivate')
    <style>
        .spinner-container {
            text-align: center;
            padding: 10px;
            font-size: 1.2rem;
            display: none;
            /* Initially hidden */
        }

        /* Fixed Table Width */
        .fixed-table td:first-child {
            width: 70%;
            /* Description column width */
            white-space: nowrap;
        }

        .fixed-table td:last-child {
            width: 30%;
            /* Value column width */
            white-space: nowrap;
        }

        .fixed-table tbody tr:first-child td {
            background-color: #f5f5f5;
            font-weight: bold;
        }

        .block-title {
            background-color: #fec010;
        }

        .panel-body {
            /* background-color: #f5f5f5; */
        }
    </style>
@endsection


@section('jsprivate')
    <script>
        // CSRF Token Setup
        window.Laravel = <?php echo json_encode(['csrfToken' => csrf_token()]); ?>;

        $(document).ready(function() {
            // Flag to prevent double initialization
            let isInitialized = false;

            // CSRF token setup for all AJAX requests
            $.ajaxSetup({
                headers: {
                    'X-CSRF-TOKEN': Laravel.csrfToken
                }
            });

            const monthNames = [
                'January', 'February', 'March', 'April', 'May', 'June',
                'July', 'August', 'September', 'October', 'November', 'December'
            ];

            // Utility to show spinner and hide table
            function toggleSpinnerVisibility(spinnerId, tableId, show = true) {
                $(spinnerId).toggle(show);
                $(tableId).toggle(!show);
            }

            // Reusable function to fetch data with a promise
            function fetchDataWithPromise(url, requestData, spinnerIds, tableIds) {
                spinnerIds.forEach(id => toggleSpinnerVisibility(id, tableIds[spinnerIds.indexOf(id)], true));

                return $.ajax({
                    url: url,
                    type: "POST",
                    data: requestData,
                }).then(
                    response => {
                        spinnerIds.forEach(id => toggleSpinnerVisibility(id, tableIds[spinnerIds.indexOf(id)], false));

                        if (response.status === 'Success') {
                            // console.log(`Success: ${url}`, response);
                            return response.data;
                        } else {
                            console.error(`Error in ${url}:`, response.message);
                            return Promise.reject(response.message);
                        }
                    },
                    xhr => {
                        spinnerIds.forEach(id => toggleSpinnerVisibility(id, tableIds[spinnerIds.indexOf(id)], false));

                        if (xhr.status === 419) {
                            location.reload(); // Refresh CSRF token
                        } else {
                            console.error(`Error in ${url}:`, xhr.statusText);
                        }
                        return Promise.reject(xhr.statusText);
                    }
                );
            }


            // Populate dropdowns
            function populateDropdowns() {
                const currentYear = new Date().getFullYear();
                const currentMonth = new Date().getMonth() + 1; // Months are 0-based

                const yearDropdown = $('#year');
                const monthDropdown = $('#month');

                yearDropdown.empty();
                monthDropdown.empty();

                // Year Dropdown
                for (let year = currentYear; year >= 2024; year--) {
                    yearDropdown.append(
                        `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`
                    );
                }

                // Month Dropdown
                function updateMonthDropdown(selectedYear) {
                    monthDropdown.empty();
                    monthNames.forEach((month, index) => {
                        const monthValue = index + 1;
                        // If selected year is the current year, only show months up to the current month
                        if (selectedYear == currentYear && monthValue > currentMonth) return;

                        monthDropdown.append(
                            `<option value="${monthValue}">${month}</option>`
                        );
                    });
                }

                // Set initial month dropdown based on default selected year
                updateMonthDropdown(currentYear);

                // Update months when year is changed
                yearDropdown.on('change', function() {
                    updateMonthDropdown($(this).val());
                });
            }

            // Update SPKI table
            function updateSPKITable(data) {
                data.forEach(item => {
                    const table = item.provider === "DIGICERT" ? '#digicert_table' : '#trustgate_table';
                    const tableElement = $(table);

                    const month = monthNames[item.MONTH - 1];
                    tableElement.find('[data-field="month"]').text(`${month} ${item.YEAR}`);
                    tableElement.find('[data-field="total_attempt"]').text(item.total_attempt);
                    tableElement.find('[data-field="total_failed"]').text(item.total_failed);
                    tableElement.find('[data-field="success_percentage"]').text(
                        (100 - parseFloat(item['SLA%'])).toFixed(2) + '%'
                    );
                    tableElement.find('[data-field="sla_percentage"]').text(item['SLA%'] + '%');
                });
            }

            // Update GFMAS tables (APIVE/AP511)
            function updateGfmasTable(data, tableId) {
                const tableElement = $(tableId);

                const month = monthNames[data.month_created - 1];
                tableElement.find('[data-field="month"]').text(`${month} ${data.year_created}`);
                tableElement.find('[data-field="total_received"]').text(data.total_created || '-');
                tableElement.find('[data-field="total_processed"]').text(data.total_processed || '-');
                tableElement.find('[data-field="total_exceed"]').text(data.total_exceed_24hour || '-');
                tableElement.find('[data-field="percentage"]').text(
                    parseFloat(data.percentage) === 100 ? '100%' : (data.percentage || '-') + '%'
                );
            }

            // Fetch all data concurrently
            function fetchAllData(requestData) {
                const spkiURL = "{{ url('report/matrix/spki-data') }}";
                const ap511URL = "{{ url('report/matrix/ap511-data') }}";
                const apiveURL = "{{ url('report/matrix/apive-data') }}";

                const spkiPromise = fetchDataWithPromise(spkiURL, requestData, ['#digicert_spinner', '#trustgate_spinner'],
                        ['#digicert_table', '#trustgate_table'])
                    .then(updateSPKITable);
                const ap511Promise = fetchDataWithPromise(ap511URL, requestData, ['#ap511_spinner'], ['#ap511_table'])
                    .then(data => updateGfmasTable(data, '#ap511_table'));
                const apivePromise = fetchDataWithPromise(apiveURL, requestData, ['#apive_spinner'], ['#apive_table'])
                    .then(data => updateGfmasTable(data, '#apive_table'));

                Promise.all([spkiPromise, ap511Promise, apivePromise])
                    .then(() => {
                        // console.log("All data loaded successfully.");
                    })
                    .catch(error => {
                        console.error("Error loading one or more data sets:", error);
                    });
            }

            // Handle dropdown changes
            function handleDropdownChange() {
                if (!isInitialized) return; // Prevent execution during initialization

                const selectedYear = $('#year').val();
                const selectedMonth = $('#month').val();

                const requestData = {
                    year: selectedYear,
                    month: selectedMonth
                };

                fetchAllData(requestData);
            }

            // Initialization
            function initialize() {
                populateDropdowns();

                // Add event listener after initialization
                $('#year, #month').on('change', handleDropdownChange);

                // Set initialization flag and trigger the first fetch
                isInitialized = true;
                handleDropdownChange();
            }

            // Start initialization
            initialize();
        });
    </script>
@endsection
