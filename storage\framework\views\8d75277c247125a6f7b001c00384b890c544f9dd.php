
<?php if( Auth::user()->isAdvRolesEp() ): ?> 
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    <li class="<?php echo e(Request::is('support/task') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/support/task")); ?>"><i class="fa fa-tasks sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Tasks</span></a>
    </li>
    <li class="<?php echo e(Request::is('support/task-missing') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/support/task-missing")); ?>"><i class="fa fa-tasks sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Stuck Task List</span></a>
    </li>
    <li class="<?php echo e(Request::is('support-spki/task') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/support-spki/task")); ?>"><i class="fa fa-tasks sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">SPKI Tasks</span></a>
    </li>

    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Modules"><i class="gi gi-notes_2"></i></a></span>
        <span class="sidebar-header-title">eP </span>
    </li>

    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-building sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Pembekal</span></a>
        <ul>
            <li>
                <a href="<?php echo e(url("/find/mofno")); ?>">Carian Pembekal</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/icno")); ?>">Carian IC No.</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/byname")); ?>">Carian Nama Pembekal</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/ssmno")); ?>">Carian SSM No.</a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="hi hi-user sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Carian Pengguna</span></a>
        <ul>
            <li>
                <a href="<?php echo e(url("/find/identity")); ?>">Carian Identity</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/userlogin")); ?>">Carian User Login</a>
            </li>
        </ul>
    </li>


    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-building sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Organisasi</span></a>
        <ul>
            <li>
                <a href="<?php echo e(url("/find/orgcode")); ?>">Carian Org Code</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/org/icno")); ?>">Carian IC No.</a>
            </li>
        </ul>
    </li>
    <li class="<?php echo e(Request::is('dashboard/data/lookup') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/dashboard/data/lookup")); ?>"><i class="fa fa-table sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Data Lookup</span></a>
    </li>
    <li class="<?php echo e(Request::is('ep/blast/email/front') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/ep/blast/email/front")); ?>"><i class="fa fa-envelope sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Blast Email</span></a>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-list sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Item</span></a>
        <ul>
            <li>
                <a href="<?php echo e(url("/find/uom")); ?>">Carian UOM</a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/item")); ?>">Carian Items </a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/items/unspsc")); ?>">Carian UNSPSC Items </a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/items/supplier")); ?>">Carian Item Pembekal </a>
            </li>
            <li>
                <a href="<?php echo e(url("/find/items/codi-task")); ?>">Item Task History </a>
            </li>
        </ul>
    </li>


    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Sourcing & Fulfillment</span></a>
        <ul>
            <li class="<?php echo e(Request::is('find/trans/docno/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/trans/docno/insert_docno")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Carian Status Doc No.</span></a>
            </li>
            <li class="<?php echo e(Request::is('find/trans/track/docno/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/trans/track/docno")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Tracking Diary Document</span></a>
            </li>
            <li class="<?php echo e(Request::is('find/trans/track/user/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/trans/track/user")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Tracking Diary User</span></a>
            </li>

            <li class="<?php echo e(Request::is('find/dp-summary/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/dp-summary/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">DP Summary</span></a>
            </li>
            <li class="<?php echo e(Request::is('find/fn-summary/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/fn-summary/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL Summary</span></a>
            </li>

        </ul>
    </li>

    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-file-text sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Quotation Tender</span></a>
        <ul>
            <li class="">
                <a href="<?php echo e(url("/qt/dashboard")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT Dashboard</span></a>
            </li>
            <?php if(Auth::user()->isPatcherRolesEp()): ?>
            <li class="">
                <a href="<?php echo e(url("/qt/extend")); ?>"><i class="hi hi-time sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT Extension</span></a>
            </li>
            <?php endif; ?>                                           

            <li class="">
                <a href="<?php echo e(url("/find/qt/summary")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Summary QT</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/accept-history")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT Accept History</span>
                </a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/loastatus")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Check LOA Status</span></a>
            </li>

            <li class="">
                <a href="<?php echo e(url("/find/qt/suppresp")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Check Extend QT </span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/verifysupp")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Ranking Supplier</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/suppexp")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Check Experience</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/stucksummary")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT User Involvement</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/qt/findqtbysupp")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Get List QT by MOF No</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/qt/doc/view")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT Document Checklist</span></a>
            </li>
        </ul>
    </li>


    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-file-text sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Contract</span></a>
        <ul>

            <li class="<?php echo e(Request::is('find/contract/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/find/contract")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Carian Kontrak</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/contract/committee")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Ahli Jawatan Kuasa</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/ep/ct/stat-ct-cancel-batch-no")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">WIP Agreement Cancel</span></a>
            </li>

        </ul>
    </li>
    
    <?php if(Auth::user()->isPatcherRolesEp()): ?>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">App Support</span></a>
        <ul>
            <li class="<?php echo e(Request::is('app-support/dashboard*') ? 'active' : ''); ?>">
                <a href="<?php echo e(url('/app-support/dashboard/pm')); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Dashboard</span></a>
            </li>

            <li class="<?php echo e(Request::is('app-support/mof_deleted_to_active') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/mof_deleted_to_active")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Mof Deleted To Active</span></a>
            </li>

            <li class="<?php echo e(Request::is('app-support/mof_active_to_deleted') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/mof_active_to_deleted")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Mof Active To Deleted</span></a>
            </li>

            <li class="<?php echo e(Request::is('app-support/softcert-user-not-sync') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/softcert-user-not-sync")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Softcert Users Not Sync</span></a>
            </li>

            <li class="<?php echo e(Request::is('app-support/personnel_is_bumi_null') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/personnel_is_bumi_null")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Personnel Is Bumi Null</span></a>
            </li>

            <li class="<?php echo e(Request::is('app-support/application_with_no_category') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/application_with_no_category")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">App With No Category</span></a>
            </li>
            
            <li class="<?php echo e(Request::is('app-support/app_missing_sv') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/app_missing_sv")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">App Missing SV</span></a>
            </li>

            
            <li class="<?php echo e(Request::is('app-support/supplier_disciplinary_action_app') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/supplier_disciplinary_action_app")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Supplier Disciplinary</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/bumi_status_cancellation') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/bumi_status_cancellation")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Bumi Status Cancellation</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/site_visit_rollback') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/site_visit_rollback")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Site Visit Rollback</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/application_status_not_sync') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/application_status_not_sync")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">App Status Not Sync</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/users_inactive_not_sync') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/users_inactive_not_sync")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Users Inactive Not Sync</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/ptj_org_validaty_rollback') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/ptj_org_validaty_rollback")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">PTJ Org Validity Rollback</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/report/all/module') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/report/all/module")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Report All Module</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/reporting') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/reporting")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Reporting</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/file/upload-mbb') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/file/upload-mbb")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Upload MBB Report</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/mof_cert_exceed_3_years') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/mof_cert_exceed_3_years")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">MOF Cert Exceed 3 Years</span></a>
            </li>
            <li class="<?php echo e(Request::is('app-support/payment-razer') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/payment-razer")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Razer Update Card Type</span></a>
            </li>
            <li class="<?php echo e(Request::is('s4-crm-case-monitoring') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/app-support/s4-crm-case-monitoring")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">S4 CRM Case Monitoring</span></a>
            </li>
            </ul>
    </li>
    <?php endif; ?>
    <?php if(Auth::user()->isPatcherRolesEp()): ?>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Prod Support</span></a>
        <ul>
            <?php if(Auth::user()->isPatcherRolesEp()): ?>
            <li class="<?php echo e(Request::is('prod-support/data-patching') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/prod-support/data-patching")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Data Patch</span></a>
            </li>
            <li class="<?php echo e(Request::is('prod-support/defect_testing') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/prod-support/defect_testing")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Defect Testing</span></a>
            </li>
            <?php endif; ?>
        </ul>
    </li>
    <?php endif; ?>

    <?php if(Auth::user()->isPatcherRolesEp()): ?>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Unit eP</span></a>
        <ul>
            <li class="<?php echo e(Request::is('find/prod-support/report_001_byyear') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/prod-support/rpt/report_001_byyear")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Laporan Perbendaharaan</span></a>
            </li>
            <li class="<?php echo e(Request::is('prod-support/rpt/summary') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/prod-support/rpt/summary")); ?>"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Laporan Permohonan</span></a>
            </li>
            <li class="<?php echo e(Request::is('report/loginSummaryReport') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/report/loginSummaryReport")); ?>">
                    <i class="gi gi-charts sidebar-nav-icon"></i>
                    Laporan eP Login</a>
            </li>
            <li class="<?php echo e(Request::is('activity/admin/ep') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/activity/admin/ep")); ?>">
                    <i class="gi gi-notes_2 sidebar-nav-icon"></i>
                    Activity Admin eP</a>
            </li>
        </ul>
    </li>
    <?php endif; ?>
    
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-table sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">It Support</span></a>
        <ul>
            <li class="<?php echo e(Request::is('/it_support/dba_morning/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/dba_morning/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">DBA Morning Checklist</span></a>
            </li> 
            <li class="<?php echo e(Request::is('/it_support/server/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/server/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Server Checklist</span></a>
            </li>
            <li class="<?php echo e(Request::is('/it_support/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Status Checklist</span></a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-table sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Network Checklist</span></a>
        <ul>
            <li class="<?php echo e(Request::is('/it_support/network/backup/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/network/backup/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">BACKUP CYBER</span></a>
            </li>
            <li class="<?php echo e(Request::is('/it_support/network/backup/checklist/wisma') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/network/backup/checklist/wisma")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">BACKUP WISMA CDC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/it_support/network/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/network/performance/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">CDC CYBERJAYA</span></a>
            </li>
            <li class="<?php echo e(Request::is('/it_support/network/checklist') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/network/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">KVDC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/it_support/network/amtek/checklist/') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/it_support/network/amtek/checklist")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">WISMA CDC</span></a>
            </li>
        </ul>
    </li>

    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Tech Refresh Issue"><i class="gi gi-notes_2"></i></a></span>
        <span class="sidebar-header-title">Tech Refresh Issue</span>
    </li>
    <li>
        <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Monitoring List</a>
        <ul>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-create-sst') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-create-sst")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Create SST</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-closed') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-closed")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Closed</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-spec') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-spec")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task Spec</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-ec') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-ec")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task EC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-oc') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-oc")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task OC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-tec') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-tec")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task TEC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-fec') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-fec")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task FEC</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=qt-stuck-award') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=qt-stuck-award")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task Awarded</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=fl-invoice-cancellation') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=fl-invoice-cancellation")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - Invoice Cancellation</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=fl-poco-cancellation') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=fl-poco-cancellation")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - POCO Cancellation</span></a>
            </li>
            <li class="<?php echo e(Request::is('/techrefresh/monitoring/?page=fl-po-expired') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/techrefresh/monitoring/?page=fl-po-expired")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - PO Expired</span></a>
            </li>
        </ul>
    </li>

    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Integration BPM"><i class="gi gi-nameplate"></i></a></span>
        <span class="sidebar-header-title">BPM</span>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM Support</span></a>
        <ul>

            <li class="">
                <a href="<?php echo e(url("/find/bpm/task/docno")); ?>">Carian Tugasan</a>
            </li>

            <li class="">
                <a href="<?php echo e(url("/find/bpm/instanceid")); ?>">Find Composite Instance</a>
            </li>

            <li class="">
                <a href="<?php echo e(url("/find/updatetracking/")); ?>">Update Tracking PA</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/phis150/")); ?>">Trigger PHIS 150</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/phis160/")); ?>">Trigger PHIS 160</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/invoice")); ?>">Payload Invoice Creation</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/fulfilment/do/")); ?>">Payload DO Initiation</span></a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/simplequote")); ?>">Payload SQ Creation</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/requestnote")); ?>">Payload RN Completed</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/fulfilment/submitinv")); ?>">Payload Submit Invoice</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/fulfilment/dan")); ?>">Payload DAN</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/contract/factoring")); ?>">Payload Factoring Company</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/contract/agreement")); ?>">Recreate Contract Agreement</a>
            </li>
            <li class="">
                <a href="<?php echo e(url("/find/contract/agreement/draft")); ?>">Payload CT Agreement Draft AC/SA</a>
            </li>
            

</ul>
</li>



<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM STL</span></a>
    <ul>
        <li class="">
            <a href="<?php echo e(url("/find/sm/stuck-task/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck SM</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/qt/stuck/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck QT</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/dp/stuck-task/sq")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck DP SQ</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/dp/stuck-task/rn")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck DP RN</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/dp/stuck-task/codification")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck DP Codify</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/fl/stuck-task/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck FL Initiate PRCR</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/fl/stuck-task-pending-approval/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck FL Approver PRCR</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/fl/stuck-task-initiate-do/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck FL Initiate DO</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/fl/stuck-task-pending-invoice/")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck FL Pending Invoice</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/gfmas/stuck-charging-status-bpm")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration EPP-013</span></a>
        </li>

        <li class="">
            <a href="<?php echo e(url("find/fl/stuck-task/integration/")); ?>?type=PRCR"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration PRCR</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("find/fl/stuck-task/integration/")); ?>?type=FRN"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration FRN</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("find/fl/stuck-task/integration/")); ?>?type=DAN"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration DAN</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("find/fl/stuck-task/integration/")); ?>?type=SD"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration SD</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("find/fl/stuck-task/integration/")); ?>?type=PA"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Integration PA</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/stuck-task-invalid-completed/")); ?>?composite_name=SourcingDP"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Stuck Task Invalid Completed</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/gfmas/stuck-pending-prcr-review")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Pending PR/CR Review</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/gfmas/stuck-pending-payment-query")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Pending Payment Query IGFMAS</span></a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/find/gfmas/redundant-saporderno/2020/1")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Redundant SAP Order No</span></a>
        </li>
    </ul>
</li>


<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM API</span></a>
    <ul>
        <li class="">
            <a href="<?php echo e(url("/bpm/task/find")); ?>">Task By DocNo</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/bpm/instance/query")); ?>">Instance Query</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/bpm/instance/find")); ?>">Process Manager</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/bpm/worklist/find")); ?>">Worklist Manager</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/bpm/service/manager")); ?>">Service Manager</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/bpm/errhandler/find")); ?>">Error Handler List</a>
        </li>
    </ul>
</li>

<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM - Refire Task</span></a>
    <ul>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Supplier Management</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sm/task/find")); ?>">MOF Supplier Application</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>SourcingDP</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sourcing/task/sqcreation")); ?>">Simple Quote Creation</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>SourcingQT</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sourcing/task/qtcreation")); ?>">QuotationTenderCreation</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sourcing/task/evaluation")); ?>">Evaluation</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sourcing/task/finalization")); ?>">Supplier Finalization</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/sourcing/task/refireEvaluation")); ?>">Evaluation (TEC/FEC)</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Order</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/order/task/PurchaseRequest")); ?>">Purchase Request</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/order/task/ContractRequest")); ?>">Contract Request</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Delivery Order</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/fl/task/doFulfilment")); ?>">Delivery Order Fulfilment</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/fl/task/modifyCancelDO")); ?>">Modify / Cancel DO</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Invoice And Payment</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/bpm/fl/task/createSubmitInvoicePA")); ?>">Create / Submit Invoice, PA</a>
                </li>                                             
                <li class="">
                    <a href="<?php echo e(url("/bpm/fl/task/modifyCancelInvoice")); ?>">Modify / Cancel Invoice</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/bpm/fl/task/stopInstruction")); ?>">Stop Instruction</a>
                </li>
            </ul>
        </li>
    </ul>
</li>



<li class="sidebar-header">
    <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Integration"><i class="fa fa-wrench"></i></a></span>
    <span class="sidebar-header-title">eP Integration</span>
</li>
<li class="<?php echo e(Request::is('dashboard/main') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/dashboard/main")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard</span></a>
</li>
<li class="<?php echo e(Request::is('dashboard/ejb') ? 'active' : ''); ?> hide">
    <a href="<?php echo e(url("/dashboard/ejb")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard EJB</span></a>
</li>

<li class="<?php echo e(Request::is('dashboard/statistic') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/dashboard/statistic")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard Statistic</span></a>
</li>

<li class="<?php echo e(Request::is('stl') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/stl")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard STL</span></a>
</li>
<li class="<?php echo e(Request::is('dashboard/bpm/stl/Profile_Management') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/dashboard/bpm/stl/Profile_Management")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard BPM STL</span></a>
</li>

<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">GPKI</span></a>
    <ul>
        <li>
            <a href="<?php echo e(url("gpki/test")); ?>">Test</a>
        </li>
        <li>
            <a href="<?php echo e(url("gpki/sign")); ?>">Sign</a>
        </li>
        <li>
            <a href="<?php echo e(url("gpki/verify")); ?>">Verify</a>
        </li>
        <li>
            <a href="<?php echo e(url("gpki/user-signing-list")); ?>">User Signing List</a>
        </li>
    </ul>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-settings sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">SPKI</span></a>
    <ul>

        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Trustgate</a>
            <ul>
                <li>
                    <a href="<?php echo e(url("spki/trustgate/challengequestion")); ?>">Challenge Question</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/trustgate/updatechallengequestion")); ?>">Update Challenge Question</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/trustgate")); ?>">Signing</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/trustgate/resetpin")); ?>">Reset PIN</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/trustgate/changepin")); ?>">Change PIN</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/trustgate/revoke")); ?>">Revoke Cert</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Digicert</a>
            <ul>
                <li>
                    <a href="<?php echo e(url("spki/digicert/challengequestion")); ?>">Challenge Question</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/digicert/updatechallengequestion")); ?>">Update Challenge Question</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/digicert/changepin")); ?>">Change PIN</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/digicert/resetpin")); ?>">Reset PIN</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/digicert/signing")); ?>">Sign In</a>
                </li>
                <li>
                    <a href="<?php echo e(url("spki/digicert/revoke")); ?>">Revoke Cert</a>
                </li>
            </ul>
        </li>

    </ul>
</li> 

<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">OSB</span></a>
    <ul>

        <li>
            <a href="<?php echo e(url("/find/osb/log")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">OSB Log</span></a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/osb/batch/file")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">OSB Batch File Log</span></a>
        </li>
        <li>
            <a href="<?php echo e(url("/osb/file/content/search")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Find Content File</span></a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/osb/detail/log")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">OSB Log Details</span></a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/osb/detail-rquid/log")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">OSB Log RQ-UID</span></a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/osb/error")); ?>"><i class="gi gi-table sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">OSB Log Error</span></a>
        </li> 
    </ul>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-settings sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Components</span></a>
    <ul>

        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Trigger</a>
            <ul>
                <li>
                    <a href="<?php echo e(url("/trigger/gfmas/apive/")); ?>">APIVE Trigger</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/trigger/gfmas/mminf/")); ?>">MMINF Trigger</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/list/1gfmas/folder")); ?>">Batch Trigger</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/osb/file/upload")); ?>">Upload & Transfer File</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>1GFMAS</a>
            <ul>
                <li>
                    <a href="<?php echo e(url("/find/1gfmas/ws")); ?>">Carian Web Service Log</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/find/gfmas/materialCode")); ?>">Carian MMINF Code</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/find/gfmas/apive")); ?>/insert_epno">Carian 1GFMAS APIVE File</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>PHIS</a>
            <ul>
                <li class="">
                    <a href="<?php echo e(url("/find/phis/ws")); ?>">Carian Web Service Log</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("/find/phis/view/")); ?>">Carian Order Details</a>
                </li>
            </ul>
        </li>
        <li>
            <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Automation</a>
            <ul>
                <li>
                    <a href="<?php echo e(url("/automation/ep-portal")); ?>">Selenium Runner</a>
                </li>
                <li>
                    <a href="<?php echo e(url("/knowage/razerpay/transaction")); ?>">RazerPay Transactions</a>
                </li>
            </ul>
        </li>
    </ul>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">eP Scheduler</span></a>
    <ul>
        <li>
            <a href="<?php echo e(url("/find/app-scheduler/SIT")); ?>">SIT</a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/app-scheduler/PDS")); ?>">PDS</a>
        </li>
        <li>
            <a href="<?php echo e(url("/find/app-scheduler/Prod")); ?>">Production</a>
        </li>
    </ul>    
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Master Data</span></a>
    <ul>
        <li class="<?php echo e(Request::is('find/masterdata/ptj') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/find/masterdata/ptj")); ?>"><i class="gi gi-package sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Master Data</span></a>
        </li>
        <li class="<?php echo e(Request::is('find/masterdata/ep') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/find/masterdata/ep")); ?>"><i class="gi gi-package sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">EP Data</span></a>
        </li>
    </ul>    
</li> 

<!--CRM START-->
<li class="sidebar-header">
    <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Data"><i class="fa fa-figter-jet"></i></a></span>
    <span class="sidebar-header-title">CRM</span>
</li>
<?php if(Auth::user()->isSpecialistUsers()): ?>  
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Report CRM</span></a>
        <ul>
            <li class="<?php echo e(Request::is('report/cptpp') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/report/cptpp")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Pemantauan Cptpp</span></a>
            </li>
            <li class="<?php echo e(Request::is('report/pemantauan_cptpp_qt') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/report/pemantauan_cptpp_qt")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Pemantauan Cptpp - QT</span></a>
            </li>
            <li class="<?php echo e(Request::is('report/perubahandata') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/report/perubahandata")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Pemantauan Perubahan Data</span></a>
            </li>
        </ul>    
    </li> 
<?php endif; ?>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Dashboard CRM</span></a>
    <ul>
        <li class="<?php echo e(Request::is('dashboard/crm') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/dashboard/crm")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - Incident</span></a>
        </li>
        <li class="<?php echo e(Request::is('dashboard/crm/other') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/dashboard/crm/other")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - Others</span></a>
        </li>
        <li class="<?php echo e(Request::is('dashboard/crm/pmo') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/dashboard/crm/pmo")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - PMO</span></a>
        </li>
        <li class="<?php echo e(Request::is('dashboard/crm/topcases') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/dashboard/crm/topcases")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - Top 10 Cases</span></a>
        </li>
        <li class="<?php echo e(Request::is('dashboard/crm/cs/main') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/dashboard/crm/cs/main")); ?>"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - CS Pending Input</span></a>
        </li>
    </ul>    
</li>
<li class="<?php echo e(Request::is('dashboard/module') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/dashboard/module")); ?>"><i class="gi gi-stopwatch sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard Module</span></a>
</li>

<?php if(Auth::user()->isStlCrmUser()): ?>
<li class="<?php echo e(Request::is('stl') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/stl/crm")); ?>"><i class="gi gi-sampler sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Dashboard STL CRM</span></a>
</li>
<?php endif; ?>
<li class="<?php echo e(Request::is('crmmanagement/main') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/crmmanagement/main")); ?>"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">CRM Management</span></a>
</li>
<!--CRM END-->
<?php if(Auth::user()->isPatcherRolesEp()): ?>
<li class="sidebar-header">
    <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Data"><i class="fa fa-figter-jet"></i></a></span>
    <span class="sidebar-header-title">Data</span>
</li>


<li class="<?php echo e(Request::is('find/patch-ep') ? 'active' : ''); ?>">
    <a href="<?php echo e(url("/find/patch-ep")); ?>"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Fix</span></a>
</li>
<?php endif; ?>
<li>
    <a href="<?php echo e(url("/find/ep/error")); ?>"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">Carian eP Error Message</span></a>
</li>
<li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">eP Log Trace</span></a>
    <ul>
        <li class="<?php echo e(Request::is('/log/dashboard') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/log/dashboard")); ?>"><i class="hi hi-search sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">Dashboard</span></a>
        </li>
    </ul>
</li>
<li>
    <a href="<?php echo e(url("/actionlog/main")); ?>"><i class="gi gi-notes_2 sidebar-nav-icon"></i>
        <span class="sidebar-nav-mini-hide">eP Support Action Log</span></a>
</li>


<li class="sidebar-header">
    <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Integration"><i class="gi gi-charts"></i></a></span>
    <span class="sidebar-header-title">Report</span>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Technical Support Report</span></a>
    <ul>
        <?php if(Auth::user()->isSpecialistUsers()): ?>
        <li class="">
            <a href="<?php echo e(url("/report/management")); ?>">Report Management</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/report/ketidakpatuhan/cptpp")); ?>">Ketidakpatuhan CPTPP</a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/report/matrix")); ?>">Security Matrix Summary</a>
        </li>
        <?php endif; ?>
        <?php if(Auth::user()->isDevUsersEp() ): ?>
        <li class="">
            <a href="<?php echo e(url("/report/loginSummaryReport")); ?>">eP Login Summary Report</a>
        </li>
        <?php endif; ?>
    </ul>
</li>
<?php if(Auth::user()->isDevUsersEp() ): ?>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Revenue Report</span></a>
    <ul>
        
        <li class="">
            <a href="<?php echo e(url("/report/revenue/daily-stat-transaction")); ?>"> Today Transaction Revenue </a>
        </li>
        <li class="">
            <a href="<?php echo e(url("/report/payment")); ?>"> Revenue Accumulative - YTD & MTD </a>
        </li>
        <li class="<?php echo e(Request::is('/report/revenue/daily') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/report/revenue/daily")); ?>"><i class="gi gi-charts sidebar-nav-icon"></i>Daily Summary Revenue</a>
        </li>
        <li class="<?php echo e(Request::is('/report/revenue/pending-transaction') ? 'active' : ''); ?>">
            <a href="<?php echo e(url("/report/revenue/pending-transaction")); ?>"><i class="gi gi-charts sidebar-nav-icon"></i>Pending Transaction</a>
        </li>
    </ul>
</li>
<?php endif; ?>


<?php if( Auth::user()->isDbaUsersEpss()): ?>
<li class="sidebar-header">
    <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Integration"><i class="gi gi-charts"></i></a></span>
    <span class="sidebar-header-title">DBA Monitoring Report</span>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Purge Report</span></a>
    <ul>
        <li>
         
           
                <?php if(Auth::user()->isDbaUsersEpss() ): ?>
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/CompositeInstanceMonitoringReport")); ?>">Composite Instance Report</a>
                </li> 
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/CubeInstMonitoringReport")); ?>">Cube Instance Report</a>
                </li> 
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/WFTaskMonitoringReport")); ?>">WFTask Report</a>
                </li> 
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/SCAFlowInstMonitoringReport")); ?>">SCA Flow Instance Report</a>
                </li>  
                 <li class="">
                    <a href="<?php echo e(url("dba-support/report/DLVMsgMonitoringReport")); ?>">DLV Message Report</a>
                </li>  
                <?php endif; ?>
         
        </li>

    </ul>
</li>
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-charts sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Table Growth Report</span></a>
    <ul>
        <li>            
                <?php if(Auth::user()->isDbaUsersEpss() ): ?>
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/AppDataGrowthMonitoringReport")); ?>">NGEPDBS Growth Report</a>
                </li>
                <li class="">
                    <a href="<?php echo e(url("dba-support/report/TableGrowthMonitoringReport")); ?>">NGEPSOA Growth Report</a>
                </li>
                <?php endif; ?>
        </li>
    </ul>
</li>

<?php endif; ?>
</ul>



<?php if(Auth::user()->isAllowHelpdesk()): ?>
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix">
            <a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="eP Support Helpdesk">
            <i class="fa fa-ticket"></i></a>
        </span>
        <span class="sidebar-header-title">Helpdesk </span>
    </li>

    <li class="<?php echo e(Request::is('helpdesk/ticket') ? 'active' : ''); ?>">
        <a href="<?php echo e(url("/helpdesk/ticket")); ?>"><i class="fa fa-ticket sidebar-nav-icon"></i>
            <span class="sidebar-nav-mini-hide">Ticket</span>
        </a>
    </li> 
</ul>  
<?php endif; ?> 


<?php if(Auth::user()->isPomsUser()): ?>
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="POMS"><i class="gi gi-notes_2"></i></a></span>
        <span class="sidebar-header-title">POMS</span>
    </li>

    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-check sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">SLA Checker</span></a>
        <ul>
            <li><a href="<?php echo e(url("/poms/incident")); ?>">IT Incident Monitoring </a></li>
            <li>
                <a href="<?php echo e(url("/poms/check/system-availability")); ?>">SLA System Availability</a>
            </li> 
        </ul>    
    </li> 
    <li>
        <a href="<?php echo e(url("/poms/fix/incident-tasks")); ?>"><i class="gi gi-search sidebar-nav-icon"></i>FIX Incident Tasks</a>
    </li>
    
</ul>
<?php endif; ?>


<?php if(Auth::user()->isGroupMiddleware()): ?>
<!-- Sidebar Navigation -->
<ul class="sidebar-nav">
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Middleware"><i class="gi gi-notes_2"></i></a></span>
        <span class="sidebar-header-title">MONTHLY JOBS</span>
    </li>

    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-list sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">AP511</span></a>
        <ul>
            <li class="<?php echo e(Request::is('ap511-check-invoice/dashboard') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/ap511-check-invoice/dashboard")); ?>">AP511 Check Invoice Dashboard</a>
            </li>
            <li class="<?php echo e(Request::is('ap511-check-invoice/list') ? 'active' : ''); ?>">
                <a href="<?php echo e(url("/ap511-check-invoice/list")); ?>">AP511 Invoice List</a>
            </li>
            <li><a href="<?php echo e(url("/middleware/job/ap511-update-payment")); ?>">AP511 Update Payment</a></li>
        </ul>    
    </li>
</ul>
<?php endif; ?>

<?php endif; ?>    
    

