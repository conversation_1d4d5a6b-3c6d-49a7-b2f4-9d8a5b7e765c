<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\OrganizationService;
use App\Services\Traits\SourcingService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\GFMASService;
use Carbon\Carbon;
use SSH;
use DB;

class DashboardController extends Controller
{

    use OSBService;
    use SSHService;
    use OrganizationService;
    use SourcingService;
    use SupplierService;
    use GFMASService;

    public function getDashboardMain()
    {
        return view('dashboard.main', []);
    }

    public function getDashboardBatchOutFolder()
    {
        $batchName = request()->batch_name;
        $lengthFileName = request()->length_filename;

        if ($batchName == null && $lengthFileName == null) {
            return "Invalid Request";
        }
        //$outboundFiles = null; // 
        $outboundFiles = $this->getListEpBatchFolderOUT($batchName, $lengthFileName);
        //$totalOutboundFilesPending = 0; // 
        $totalOutboundFilesPending = count($outboundFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        $batchName Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound?batch_name=$batchName&length_filename=$lengthFileName'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP transfer files to Integration Server</small></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

    public function getDashboardUserCreation()
    {

        $pendingUserCreation = $this->getPendingUserCreation();
        $totalPendingUserCreation = count($pendingUserCreation);
        $stuckUserCreation = $this->getStuckUserCreation();
        $totalStuckUserCreation = count($stuckUserCreation);

        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
              
                <tbody>";

        $html .= "
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Pending User Creation </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listPendingUserCreation'
                                   data-title='List Total Pending User Creation' >{$totalPendingUserCreation}</a></strong></td>
                   </tr> 
                   <tr>
                               <td class='text-left' style='width: 70%;'><strong>Stuck User Creation </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listStuckUserCreation'
                                   data-title='List Total Stuck User Creation' >{$totalStuckUserCreation}</a></strong></td>
                   </tr>";

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function checkQtServiceRetry()
    {
        $list = $this->getDashboardQtServiceRetry();
        //$list =  array();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>QT Status</th>
                        <th>Disruption Date</th>
                        <th>Disruption Stage</th>
                        <th>Total (QT)</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $disruptionDate = Carbon::parse($data->disruption_date)->format('Y-m-d');
            $html .= "
            <tr>
                <td style='width: 40%;'><strong>$data->status_name</strong></td>
                <td>$disruptionDate</td>    
                <td>$data->disruption_stage_desc</td>
                <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action badge label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listQtServiceRetry?disruption_stage=$data->disruption_stage&disruption_date=$disruptionDate'
                                   data-title='List Total QT Service Retry ($data->disruption_stage_desc)' >$data->counts</a></strong></td>    
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function listQtServiceRetry()
    {
        $disruptionStage = request()->disruption_stage;
        $disruptionDate = request()->disruption_date;
        $list = $this->getListDashboardQtServiceRetryDetailsByStage($disruptionStage, $disruptionDate);
        $html = "";

        $html .= "
        <thead>
                    <tr> 
                        <th class='text-center'>QT No</th>
                        <th class='text-center'>QT Status</th>
                        <th class='text-center'>Error Msg</th>
                    </tr>
                </thead>
                <tbody>";

        if (count($list) > 0) {
            foreach ($list as $data) {
                $html .= "
                <tr>
                    <td class='text-center'><a href='" . url('/find/qt/summary') . "?doc_no=" . $data->qt_no . "' target='_blank'>" . $data->qt_no . "</a></strong></td>
                    <td class='text-center'>$data->wf_qt_status</td>
                    <td class='text-left'>$data->error_msg</td>
                </tr>";
            }
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function totalSqCloseTimeDaily()
    {


        $list = $this->getTotalSqCloseTimeDaily();
        $total = $list[0]->total;
        $dataTotal = '0';
        if ($total > 0) {
            $dataTotal = "<a href='#modal-list-data' 
                        class='modal-list-data-action label label-danger' 
                        data-toggle='modal' data-url='/dashboard/listSqCloseTimeDaily'
                        data-title='List Total Simple Quote (SQ) : Today Closed Time' >$total</a>";
        }

        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th class='text-left'>Total Today Closed SQ</th>
                        <th class='text-center'>$dataTotal</th>
                    </tr>
                </thead>
                <tbody>";
        $html .= "
                </tbody>
            </table>
        </div>";



        return $html;
    }

    public function checkOsbRetry()
    {
        $collect = collect([]);
        $list = $this->getDashboardOsbRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if ($data->counts > 0) {
                $data->counts = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/osb/batch/retry/$data->service_name'
                            data-title='Details of OSB Service Retry' >{$data->counts}</a>";
            }
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_name</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        $collect->put('data', $html);

        $totalcounts = $this->getTotalDashboardOsbRetry();
        $collect->put('totalcounts', $totalcounts);

        return $collect;
    }

    public function checkOsbNotifyRetry()
    {
        $list = $this->getDashboardOsbNotifyRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_name</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkOsbBatchRetry()
    {
        $collect = collect([]);
        $list = $this->getDashboardOsbBatchRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Code</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_code</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        $collect->put('data', $html);

        $totalcounts = $this->getTotalDashboardOsbBatchRetry();
        $collect->put('totalcounts', $totalcounts);

        return $collect;
    }

    public function monitorErrorEPScheduler()
    {
        $result = $this->getDashboardErrorEPScheduler();
        $html = "";

        $html .= "
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Date Finish</th>
                        <th>Job Name</th>
                        <th>Job Group</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->date_finish</strong></td>
                <td class='text-center'>$data->job_name</td>
                <td class='text-center'>$data->job_group</td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function backlogmminf()
    {
        $list = $this->getListBackLogMMINF();
        $carbonNow = Carbon::now();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
              
                <tbody>";

        foreach ($list as $data) {
            if ($data->total > 0) {

                $html .= "
             <tr>
                        <td class='text-left' style='width: 70%;'><strong>Total (Pending send to IGFMAS) </strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/support/report/log/di-mminf/$carbonNow'
                            data-title='List MMINF on pending send to 1GFMAS' >{$data->total}</a></strong></td>
            </tr>";
            }
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function getDashboardProcessPayment()
    {

        $pendingProcessPayment = $this->getPendingProcessPayment();
        $totalPendingProcessPayment = count($pendingProcessPayment);
        $stuckProcessPayment = $this->getStuckProcessPayment();
        $totalStuckProcessPayment = count($stuckProcessPayment);

        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <tbody>";

        $html .= "
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Pending Process Payment </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listPendingProcessPayment'
                                   data-title='List Total Pending Process Payment' >{$totalPendingProcessPayment}</a></strong></td>
                   </tr> 

                   <tr>
                               <td class='text-left' style='width: 70%;'><strong>Stuck Process Payment </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listStuckProcessPayment'
                                   data-title='List Total Stuck Process Payment' >{$totalStuckProcessPayment}</a></strong></td>
                   </tr>";

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function getDashboardProcessRemoveTask()
    {
        // Fetch pending and in-progress remove tasks using the new methods
        $pendingTasks = $this->getSummaryOfPendingAndInProgressRemoveTasks();
    
        $pendingRemoveTask = 0;
        $inProgressRemoveTask = 0;
        
        // Calculate pending and in-progress remove tasks count
        foreach ($pendingTasks as $task) {
            if ($task->status === 'Pending Process Remove Task') {
                $pendingRemoveTask = $task->total;
            } elseif ($task->status === 'In Progress Remove Task') {
                $inProgressRemoveTask = $task->total;
            }
        }
    
        $html = "";
        $html .= "
            <div>
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>";
    
        // Pending Process Remove Task row
        $html .= "
                        <tr>
                            <td class='text-left' style='width: 70%;'><strong>Pending Process Remove Task</strong></td>
                            <td><strong><a href='#modal-list-data' 
                                class='modal-list-data-action label label-danger' 
                                data-toggle='modal' data-url='/dashboard/listRemoveTask/8'
                                data-title='List Pending Process Remove Task'>{$pendingRemoveTask}</a></strong></td>
                        </tr>";
    
        // In Progress Remove Task row
        $html .= "
                        <tr>
                            <td class='text-left' style='width: 70%;'><strong>In Progress Remove Task</strong></td>
                            <td><strong><a href='#modal-list-data' 
                                class='modal-list-data-action label label-warning' 
                                data-toggle='modal' data-url='/dashboard/listRemoveTask/1'
                                data-title='List In Progress Remove Task'>{$inProgressRemoveTask}</a></strong></td>
                        </tr>";
    
        $html .= "
                    </tbody>
                </table>
            </div>";
    
        return $html;
    }

    public function getDetailedRemoveTask($record_status)
    {
        // Fetch the detailed remove tasks based on the record status
        $detailedTasks = $this->getDetailedPendingRemoveTasks($record_status);

        $html = "";
        $html .= "<thead>
                    <tr>
                        <th>ID</th>
                        <th>Task</th>
                        <th>Status</th>
                        <th>Module</th>
                        <th>User</th>
                        <th>Created Date</th>
                        <th>Changed Date</th>
                        <th>Total Deleted</th>
                        <th>Ageing (days)</th>
                    </tr>
                  </thead>
                  <tbody>";

        foreach ($detailedTasks as $task) {
            $html .= "<tr>
                        <td><a href='".url('/find/patch-ep')."?patch=PM_PENDING_REMOVE_TASK&record=".$task->pm_pending_remove_task_id."' target='_blank'>$task->pm_pending_remove_task_id</td>
                        <td>{$task->param_task}</td>
                        <td>{$task->record_status}</td>
                        <td>{$task->module}</td>
                        <td>{$task->user_id}</td>
                        <td>{$task->created_date}</td>
                        <td>{$task->changed_date}</td>
                        <td>{$task->total_task_deleted}</td>
                        <td>{$task->ageing_day}</td>
                      </tr>";
        }

        $html .= "</tbody>";

        return $html;
    }
    
    public function checkMonitoringSoftcert()
    {
        //$latestSigningDigicert = $this->getDashboardLatestDigiCertSuccessSigning();
        //$latestSigningTrustgate = $this->getDashboardLatestTrustgateSuccessSigning();
        //$todaylistDashboardSoftcert = $this->getDashboardTotalCertificationAuthority();

        //$latestSigningDigicertDate = $latestSigningDigicert[0]->latest_signing;
        //$latestSigningTrustgateDate = $latestSigningTrustgate[0]->latest_signing;
        $date = request()->dateSearch;
        if ($date == null) {
            $date = Carbon::now()->format('Y-m-d');
        }
        $resultData = $this->getDashboardSpkiSigningSubmitionQtByDate($date);
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Certification Authority (CA)</th>
                        <th>Latest Signing Date</th>
                        <th>Total (Today)</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($resultData as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->softcert_provider</strong></td>
                <td style='width: 50%;'><strong>$data->latest_date</strong></td>
                <td class='text-center'><span class='badge label-danger'>$data->total</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkQtSubmissionMonitoring()
    {

        $resultData = $this->getDashboardSubmitQtTodayStatistic();
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th class='text-center'>LAST SUBMIT DATE</th>
                        <th class='text-center'>TOTAL SUBMIT QT</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($resultData as $data) {
            $html .= " 
            <tr>
                <td style='width: 50%;' class='text-center'><strong>$data->latest_submit_date</strong></td>
                <td class='text-center'><span class='badge label-info'>$data->total_submit</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function getDashboardBpm()
    {
        return view('dashboard.bpm', []);
    }

    public function checkHealthBPM()
    {

        $resultData = $this->getListHealthBPM();
        $listHealthBpm = $resultData->get('health_bpm');
        $listThreadBpm = $resultData->get('thread_bpm');
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>BPM Health</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($listHealthBpm as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        $html .= "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>BPM Thread</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($listThreadBpm as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function srvCheckPing()
    {
        $resultData = $this->checkPing(request()->domain);

        $collection = collect($resultData);

        $filtered = $collection->filter(function ($value, $key) {
            return $value != 'stdin: is not a tty';
        });

        $result = $filtered->all();

        $html = "";
        $html .= "
        <div class='block'>
        <div class='block-title'>
            <h2><strong>" . request()->name . "</strong></h2>
        </div>
        <address>";

        foreach ($result as $data) {
            $html .= "<strong> {{$data}}<br/>";
        }

        $html .= "</address></div>";

        return $html;
    }

    /**
     * Check connection response from Web Service Integration
     */
    public function srvCheckConnectionService()
    {
        $services = collect();
        $services->put("IGFMAS", "https://************:6030/ws/ag.eai.common.ws.pub.provider.wsProcessCommonReqSvc/ag_eai_common_ws_pub_provider_wsProcessCommonReqSvc_Port?wsdl");
        $services->put("PHIS", "http://************:8087/ContractInformation?wsdl");
        //$services->put("SSM", "https://integrasi.ssm.com.my/InfoService/1");
        $services->put("SSM", "http://integrasiapi.ssm4u.com.my/ssm/ssm4u/infoservice");
        $services->put("JPN", "https://esb.myidentity.gov.my:81/crsservice");
        $services->put("TRUSTGATE", "https://digitalid.msctrustgate.com");
        $services->put("DIGICERT", "https://ep.posdigicert.com.my/ap");


        $serviceUrl = $services->get(request()->service);
        $resultData = $this->checkConnectionService($serviceUrl);

        $collection = collect($resultData);

        $filtered = $collection->filter(function ($value, $key) {
            return $value != 'stdin: is not a tty';
        });

        $result = $filtered->all();
        $html = "";
        $html .= "
        <div class='block'>
        <div class='block-title hide'>
            <h2><strong>" . $serviceUrl . "</strong></h2>
        </div>
        <address>";



        foreach ($result as $data) {
            if (str_contains($data, 'connected')) {
                $html .= "<strong><p class='text-success'> <i class='gi gi-circle_ok'></i>  &nbsp;$data</p></strong>";
            } else {
                $html .= "<strong><p class='text-danger'> <i class='gi gi-circle_exclamation_mark'></i>  &nbsp;$data</p></strong>";
            }
        }

        $html .= "</address></div>";

        return $html;
    }

    public function srvCheckConnectionIntegrationAllWebService()
    {
        $services = collect();
        $services->push(['integ_name' => "IGFMAS", 'integ_url' => "https://************:6030/ws/ag.eai.common.ws.pub.provider.wsProcessCommonReqSvc/ag_eai_common_ws_pub_provider_wsProcessCommonReqSvc_Port?wsdl"]);
        $services->push(['integ_name' => "PHIS", 'integ_url' => "http://************:8087/ContractInformation?wsdl"]);
        //$services->push(['integ_name' => "SSM", 'integ_url' => "https://integrasi.ssm.com.my/InfoService/1"]);
        $services->push(['integ_name' => "SSM", 'integ_url' => "http://integrasiapi.ssm4u.com.my/ssm/ssm4u/infoservice"]);
        $services->push(['integ_name' => "JPN", 'integ_url' => "https://esb.myidentity.gov.my:81/crsservice"]);
        $services->push(['integ_name' => "TRUSTGATE", 'integ_url' => "https://digitalid.msctrustgate.com"]);
        $services->push(['integ_name' => "DIGICERT", 'integ_url' => "https://ep.posdigicert.com.my/ap"]);


        $html = "";
        $html .= "
        <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Integration Name</th>
                        <th>Status</th>
                        <th class='text-center'>&nbsp;</th>
                        
                    </tr>
                </thead>
                <tbody>";

        foreach ($services as $serv) {
            $serviceName = $serv['integ_name'];
            $serviceUrl = $serv['integ_url'];
            $resultData = $this->checkConnectionService($serviceUrl);

            $collection = collect($resultData);

            $filtered = $collection->filter(function ($value, $key) {
                return $value != 'stdin: is not a tty';
            });

            $result = $filtered->all();

            foreach ($result as $data) {
                if (str_contains($data, 'connected')) {
                    $html .= "<tr>
                        <td style='width: 30%;'><strong>WS Service $serviceName</strong></td>
                        <td class='text-left text-success'>$data</td>
                        <td class='text-center'><i class='gi gi-circle_ok'></i></td></tr>";
                } else {
                    $html .= "<tr>
                        <td style='width: 30%;'><strong>WS Service $serviceName</strong></td>
                        <td class='text-left text-danger'>$data</td>
                        <td class='text-center'><i class='gi gi-circle_exclamation_mark'></i></td></tr> ";
                }
            }
        }

        $html .= "</tbody></table>";

        return $html;
    }

    public function srvCheckConnectionIntegrationAllBatchService()
    {
        $services = collect();
        $services->push([
            'integ_name' => "IGFMAS",
            'integ_host' => "************",
            'integ_port' => "2022",
            'integ_command' => "echo 'pwd exit' | sftp -oPort=2022 eperolehan@************"
        ]);
        $services->push([
            'integ_name' => "PHIS",
            'integ_host' => "************",
            'integ_port' => "22",
            //'integ_command' => "echo 'pwd exit' | sftp Phismc@************"
            'integ_command' => "echo -e '\x1dclose\x0d' | telnet ************ 22"
        ]);
        $services->push([
            'integ_name' => "MyGPIS",
            'integ_host' => "************",
            'integ_port' => "22",
            'integ_command' => "echo -e '\x1dclose\x0d' | telnet ************ 22"
            //'integ_command' => "echo 'pwd exit'|sshpass -p 'k3r3t44p1\$4tu' sftp -oPort=22 mygpis_ep@************"
        ]);
        /*
        $services->push([
            'integ_name' => "ePerunding",
            'integ_host' => "***********", 'integ_port' => "22",
            'integ_command' => "echo 'pwd exit'|sshpass -p 'eperunding2013' sftp <EMAIL>"
        ]);
        */
        $services->push([
            'integ_name' => "LMS",
            'integ_host' => "*************",
            'integ_port' => "21",
            'integ_command' => "echo -e '\x1dclose\x0d' | telnet ************* 21"
        ]);
        /*
        $services->push([
            'integ_name' => "SPA",
            'integ_host' => "************", 'integ_port' => "22",
            'integ_command' => "echo -e '\x1dclose\x0d' | telnet ************ 22"
        ]);
        */


        $html = "";
        $html .= "
        <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Integration Name</th>
                        <th>Status</th>
                        <th class='text-center'>&nbsp;</th>
                        
                    </tr>
                </thead>
                <tbody>";

        foreach ($services as $serv) {
            $serviceName = $serv['integ_name'];
            $serviceHost = $serv['integ_host'];
            $servicePort = $serv['integ_port'];
            $serviceCommand = $serv['integ_command'];

            $data = array();
            $commands = [
                $serviceCommand,
            ];

            SSH::into('osb')->run($commands, function ($line) use (&$data) {
                $result = $line . PHP_EOL;
                //var_dump($result);
                array_push($data, trim($result));
            });

            $collection = collect($data);
            $data = $collection->filter(function ($value, $key) {
                return ($value != 'stdin: is not a tty' && $value != 'sftp> pwd exit');
            });

            $telnet = "";
            foreach ($data as $d) {
                $telnet .= $d . " ";
            }
            // Remote working directory

            if (str_contains($data, 'Remote working directory') || str_contains($data, 'Connected')) {
                $html .= "<tr>
                    <td style='width: 30%;'><strong>Batch Service $serviceName ($serviceHost:$servicePort)</strong></td>
                    <td class='text-left text-success'>$telnet</td>
                    <td class='text-center'><i class='gi gi-circle_ok'></i></td></tr>";
            } else {
                $html .= "<tr>
                    <td style='width: 30%;'><strong>Batch Service $serviceName ($serviceHost:$servicePort)</strong></td>
                    <td class='text-left text-danger'>$data</td>
                    <td class='text-center'><i class='gi gi-circle_exclamation_mark'></i></td></tr> ";
            }
        }

        $html .= "</tbody></table>";

        return $html;
    }


    public function checkMonitoringSigningGpki()
    {

        $list = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT max(CREATED_DATE) as latest_gpki_date FROM pm_digi_sign WHERE  GPKI_SIGNED_DATA  IS NOT null"
        );
        $rowData = $list[0];

        $dateTimeGpki = $rowData->latest_gpki_date;
        $html = "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Certification Authority (CA)</th>
                        <th>Latest Signing Date</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style='width: 50%;'><strong>GPKI</strong></td>
                        <td style='width: 50%;'><strong>$dateTimeGpki</strong></td>
                    </tr>
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function getDashboardBatchAperr() {
        $queries = array();
        if(isset($_SERVER['QUERY_STRING'])) {
            parse_str($_SERVER['QUERY_STRING'], $queries);
        }

        if(isset($queries['selectedDate'])) {
            $today = $queries['selectedDate'];
        } else {
            $today = Carbon::today();
        }

        $queryMonitoring = "SELECT date_created,error_message,COUNT(*) AS total 
        FROM (
            SELECT 
                file_name, ep_no,
                DATE(created_date) AS date_created,
                CASE 
                WHEN SUBSTRING_INDEX(file_data, 'Error:', -1) LIKE '%Kod Pembekal masih mempunyai transaksi%' THEN 'Kod Pembekal masih mempunyai transaksi'
                ELSE TRIM(REPLACE(SUBSTRING_INDEX(file_data, 'Error:', -1), '\n', ''))
                END AS error_message  
            FROM 
                ep_osb_batch_file_aperr
            WHERE 
                file_data LIKE '%Error:%' 
                AND file_data NOT LIKE '%Error:Account%' 
                AND DATE(created_date) = '$today'
        ) tmp  
        GROUP BY date_created,error_message";
        $resultMonitoring = DB::connection('mysql_ep_support')->select($queryMonitoring);

        $html = "";
        $html .= "
        <div>
            <div class='row'>
                <div class='col-md-5 col-md-offset-7'>
                    <div class='input-group date' data-provide='datepicker' >
                        <input type='text' id='created_date' name='created_date' class='form-control input-datepicker' style='min-width:100px !important' >
                        <div class='input-group-addon'>
                            <span class='gi gi-calendar'></span>
                        </div>
                    </div>
                </div>
            </div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Created Date</th>
                        <th>Error Message</th> 
                        <th class='text-center'>Total</th>
                    </tr>
                </thead>
                <tbody>";


        foreach ($resultMonitoring as $data) {
            $errorMessage = $data->error_message;  
            $dateCreated = Carbon::parse($data->date_created)->format('d/m/Y');
            $html .= "
                    <tr>
                        <td style='width: 30%;'><strong>$dateCreated</strong></td>
                        <td><strong>$errorMessage</strong></td>   
                        <td class='text-center'>
                            <strong>
                                <a href='#modal-list-data' 
                                class='modal-list-data-action label label-danger' 
                                data-toggle='modal' data-url='/dashboard/batch/aperr/list?errorMessage=$errorMessage&date_created=$data->date_created'
                                data-title='$errorMessage' >
                                {$data->total}
                                </a>
                            </strong>
                        </td>
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function getDashboardBatchAperrList()
    {
        $queries = array();
        parse_str($_SERVER['QUERY_STRING'], $queries); 
        $errorMessage = $queries['errorMessage']; 
        $date_created = $queries['date_created']; 

        $queryList = "SELECT * FROM (SELECT 
                file_name, ep_no,
                created_date,
                CASE 
                WHEN SUBSTRING_INDEX(file_data, 'Error:', -1) LIKE '%Kod Pembekal masih mempunyai transaksi%' THEN 'Kod Pembekal masih mempunyai transaksi'
                ELSE TRIM(REPLACE(SUBSTRING_INDEX(file_data, 'Error:', -1), '\n', ''))
                END AS error_message
            FROM 
                ep_osb_batch_file_aperr
            WHERE 
                file_data LIKE '%Error:%' 
                AND file_data NOT LIKE '%Error:Account%' 
                AND DATE(created_date) = '$date_created'
            ORDER BY created_date ASC) a WHERE error_message = '$errorMessage';";
        $resultList = DB::connection('mysql_ep_support')->select($queryList);
        $html = "";

        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>File Name</th>
                        <th class='text-center'>eP Number</th>
                        <th class='text-center'>Created Date</th>
                        <th class='text-center'>Error Message</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($resultList as $data) {
            $html .= "
            <tr>
                <td class='text-left'><a href='".url('/find/osb/batch/file')."/".$data->file_name."' target='_blank'>".$data->file_name."</a></td>
                <td class='text-left'><a href='".url('/find/epno/')."/".$data->ep_no."' target='_blank'>".$data->ep_no."</a></td>
                <td class='text-center'>$data->created_date</td>
                <td class='text-center'>$data->error_message</td>
            </tr>";
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function getPendingNotification() {

        $resultList =DB::connection('oracle_nextgen_rpt')->select(
            "SELECT t.NOTIFICATION_SUBJECT ,m.notification_id, 
            min(m.created_date) AS start_created_date,
            count(*) AS total_pending
            FROM pm_notify_message m, pm_notification_text t
            WHERE 
            m.notification_id = t.notification_id
            AND m.is_sent = 0 AND m.remark IS  NULL 
            AND t.language_code = 'en' 
            AND t.record_status = 1
            GROUP BY  t.NOTIFICATION_SUBJECT,m.notification_id
            ORDER BY 1 asc"
        );  
        $html = "<div> <table class='table table-borderless table-striped table-vcenter'>";

        $html .= "<thead>
                    <tr>
                        <th class='text-left'>Subject</th>
                        <th class='text-left'>ID</th>
                        <th class='text-left'>Created Date</th>
                        <th class='text-left'>Total Pending</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($resultList as $data) {
            $html .= "
            <tr>
                <td class='text-left'>$data->notification_subject</td>
                <td class='text-left'>$data->notification_id</a></td>
                <td class='text-left'>$data->start_created_date</td>
                <td class='text-center'>$data->total_pending</td>
            </tr>";
        }

        $html .= "
                </tbody></table>";

        return $html;
    }
}
