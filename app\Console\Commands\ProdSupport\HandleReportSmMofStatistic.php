<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use DateTime;
use Config;
use Mail;
use DB;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use App\Model\Notify\NotifyModel;

class HandleReportSmMofStatistic extends Command {


    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleReportSmMofStatistic';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To send email report for statistic supplier MOF';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__ . ' > ' . __FUNCTION__ . ' >> ';
        MigrateUtils::logDump($clsInfo . 'starting ..');
        $data = collect([]);
       
        $dateAsOf = Carbon::yesterday()->format('d M Y');
        $data->put('dateasof', $dateAsOf );

        $listStatResult = $this->getStatisticSupplier();
        MigrateUtils::logDump($clsInfo . ' found : '.json_encode($listStatResult));

        $data->put('statData', $listStatResult );
        
        $this->sendNotifyEmail($data);
        MigrateUtils::logDump($clsInfo . ' completed');
    }

    protected function getStatisticSupplier(){
        return DB::connection('mysql_cms')->select("SELECT '1' as 'bil', 'PEMBEKAL' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s 
               WHERE DATE(s.mof_exp_date) >= DATE(NOW()) 
               AND s.supplier_type = 'K' AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN') ) AS 'jumlah'
       FROM DUAL 
       UNION ALL 
       SELECT '2' as 'bil', 'PERUNDING' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s 
               WHERE DATE(s.mof_exp_date) >= DATE(NOW()) 
               AND s.supplier_type = 'J' AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN')) AS 'jumlah'		
       FROM DUAL 
       UNION ALL 
       SELECT '3' as 'bil', 'PEMBEKAL BERTARAF DAFTAR' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s 
               WHERE DATE(s.mof_exp_date) >= DATE(NOW()) 
               AND s.supplier_type = 'K' AND s.is_bumi != 1 AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN')) AS 'jumlah'	
       FROM DUAL
       UNION ALL 
       SELECT '4' as 'bil', 'PEMBEKAL BERTARAF BUMIPUTERA' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s 
               WHERE DATE(s.mof_exp_date) >= DATE(NOW()) 
               AND s.supplier_type = 'K' AND s.is_bumi = 1 AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN')) AS 'jumlah'
       FROM DUAL 
       UNION ALL 
       SELECT '5' as 'bil', 'PEMBEKAL DALAM BIDANG BIASA' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s , ep_supplier_category c 
               WHERE s.latest_appl_id = c.latest_appl_id 
               AND NOT EXISTS (SELECT 1 FROM ep_supplier_category d WHERE d.latest_appl_id = c.latest_appl_id AND d.is_special_category = 1 )
               AND DATE(s.mof_exp_date) >= DATE(NOW())
               AND s.supplier_type = 'K' AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN') ) AS 'jumlah'		
       FROM DUAL
       UNION ALL 
       SELECT '6' as 'bil', 'PEMBEKAL DALAM BIDANG KHAS' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s , ep_supplier_category c 
               WHERE s.latest_appl_id = c.latest_appl_id 
               AND c.is_special_category = 1
               AND DATE(s.mof_exp_date) >= DATE(NOW())
               AND s.supplier_type = 'K' AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN') ) AS 'jumlah'	
       FROM DUAL
       UNION ALL 
       SELECT '7' as 'bil', 'PEMBEKAL DALAM BIDANG PEMBUAT' AS 'perkara', 
        (SELECT COUNT(DISTINCT s.supplier_id)  FROM ep_supplier s , ep_supplier_category c 
               WHERE s.latest_appl_id = c.latest_appl_id 
               AND EXISTS (SELECT 1 FROM ep_supplier_category d WHERE d.latest_appl_id = c.latest_appl_id AND d.category_lvl_three_code = '99' )
               AND DATE(s.mof_exp_date) >= DATE(NOW())
               AND s.supplier_type = 'K'  AND s.state NOT IN ('SABAH','SARAWAK','WILAYAH PERSEKUTUAN LABUAN')) AS 'jumlah' 		
       FROM DUAL ");
    }

    protected function sendNotifyEmail($data) {
        MigrateUtils::logDump(__METHOD__ . ' starting ..');
        // #0000010576 -> 04/02/2025 (<EMAIL>) 
        // Ticket #0000010594 | Request to update email receiver eP report by schedule : <EMAIL>
        $dataSend = array(
            "to" => [
                    '<EMAIL>','<EMAIL>','<EMAIL>'
                ],
            "cc" => [
                    '<EMAIL>','<EMAIL>',
                    '<EMAIL>','<EMAIL>','<EMAIL>',
                    '<EMAIL>','<EMAIL>'
                ],
            "bcc" => ['<EMAIL>' ],
            "subject" => 'Statistik syarikat/perunding yg berdaftar di semenanjung sehingga '.$data->get('dateasof')
        );
        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mailer
                ->send('emails.reportSupplierStatisticMonthly', ['data' => $data], function ($m) use ($dataSend) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($dataSend["to"]);
                    $m->cc($dataSend["cc"]);
                    $m->bcc($dataSend["bcc"]);
                    $m->subject($dataSend["subject"]);
                });
            Log::info(__CLASS__.' >>  '.__FUNCTION__. 'success send email : '.json_encode($dataSend["to"]). ' info:>>  '. $data);
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }

         
    }

}
