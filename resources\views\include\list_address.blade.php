@if(isset($addresses) && count($addresses) > 0 )

<thead>
    <tr>
        <th class="text-center">Type</th>
        <th class="text-center">Name</th>
        <th class="text-center">Address 1</th>
        <th class="text-center">Address 2</th>
        <th class="text-center">Address 3</th>
        <th class="text-center">Postcode</th>
        <th class="text-center">District</th>
        <th class="text-center">Division</th>
        <th class="text-center">City</th>
        <th class="text-center">Phone</th>
        <th class="text-center">Changed Date</th>
    </tr>
</thead>
<tbody>
    @foreach ($addresses as  $indexKey => $addr)   
    <tr>
        <td class="text-center">{{ $addr->address_type }}</td>
        <td class="text-center">{{ $addr->address_name }}</td>
        <td class="text-center">{{ $addr->address_1 }}</td>
        <td class="text-center">{{ $addr->address_2 }}</td>
        <td class="text-center">{{ $addr->address_3 }}</td>
        <td class="text-center">{{ $addr->postcode }}</td>
        <td class="text-center">{{ $addr->district_id }}</td>
        <td class="text-center">{{ $addr->division_id }}</td>
        <td class="text-center">{{ $addr->city_id }}</td>
        <td class="text-center">{{ $addr->phone_country }} {{ $addr->phone_area }} {{ $addr->phone_no }}</td>
        <td class="text-center">{{ $addr->changed_date }}</td>

    </tr>
    @endforeach
</tbody>

@else
<thead>
    <tr>
        <th class="text-center">Type</th>
    </tr>
</thead>
@endif