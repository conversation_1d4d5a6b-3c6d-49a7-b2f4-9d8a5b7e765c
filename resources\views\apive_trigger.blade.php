@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

    <div class="block">
        <div class="block-title">
            <h2><strong>APIVE</strong> Trigger</h2>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="{{url('/support/report/log/task-apive')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                   data-title="List Action Trigger APIVE Today ">View Today Action</a>
            </div>
        </div>

        <form id="form-search-mminf" action="{{url("/trigger/gfmas/apive/search")}}" method="post" class="form-horizontal" onsubmit="return true;">
            {{ csrf_field() }}
            <input name="_method" id="_method" type="hidden" value="POST">
            <div class="form-group">
                <label class="col-md-3 control-label" for="ep_no">eP No. <span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input id="ep_no" name="ep_no" class="form-control" placeholder="eP No.." type="text" required
                           @if(isset($formSearch)) value="{{ $formSearch["ep_no"] }}" @endif>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
        </form>
    </div>
    @if($result && $result != 'notfound')
        <div class="block">
            <div class="table-responsive">
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">eP No.</th>
                        <th class="text-center">Supplier ID</th>
                        <th class="text-center">Company Name</th>
                        <th class="text-center">MOF No.</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center"></th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach ($result as $data)
                        <tr>
                            <td class="text-center"><a href="{{url("/find/gfmas/apive")}}/{{ $data->ep_no }}" target="_blank" data-toggle="tooltip" title="Check APIVE">{{ $data->ep_no }}</a></td>
                            <td class="text-center">{{ $data->supplier_id }}</td>
                            <td class="text-center"><a href="{{url("/find/epno")}}/{{ $data->ep_no }}" target="_blank" data-toggle="tooltip" title="Company Information">{{ $data->company_name }} ({{ $data->reg_no }}) </a></td>
                            <td class="text-center">{{ $data->mof_no }}</td>
                            <td class="text-center">{{ $data->record_status }}</td>
                            <td class="text-center">{{ $data->created_date }}</td>
                            <td class="text-center td-{{ $data->supplier_id }}">{{ $data->changed_date }}</td>
                            <td class="text-center action_table">
                                <input name="supplier_id" id="supplier_id"  type="hidden" value="{{ $data->supplier_id }}">
                                <input name="current_changed_date" id="current_changed_date"  type="hidden" value="{{ $data->changed_date }}">
                                <input name="ep_no" id="ep_no"  type="hidden" @if(isset($formSearch, $formSearch["ep_no"])) value="{{ $formSearch["ep_no"] }}" @endif>
                                <div class="btn-group btn-group-xs">
                                    <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                                       href="#modal_confirm_trigger" data-toggle="modal" data-id="{{ $data->ep_no }}"><i class="hi hi-transfer"></i> Trigger</a>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
    @if($result == 'notfound')
        <div class="block block-alt-noborder full text-center label-primary">
              <span style="color: #FFF;">Tidak dijumpai!</span>
        </div>
    @endif

    <div id="modal_confirm_trigger" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Are you sure want to update this supplier to be triggered? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                {{ csrf_field() }}
                                <div class="form-group">
                                    <label class="col-md-3 control-label">eP No. </label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="trigger_epno" value="" />
                                        <p id="trigger_epno_display" class="form-control-static"></p>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_trigger"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    @include('_shared._modalListLogAction')
    
@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>

        //Confirm Trigger Dialogue
        $('td.action_table').on("click",'a.action_trigger', function(){
            var ePno = $(this).attr('data-id');
            var changedDate = $("#current_changed_date").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);
            $("#trigger_epno").val(ePno);
            $("#trigger_epno_display").text(ePno);
        });
        $('div.form-actions').on("click",'button.action_confirm_trigger', function(){
            var ePno = $("#trigger_epno").val();
            var changedDate = $("#current_changed_date").val();
            var supplierId = $("#supplier_id").val();
            var csrf = $("input[name=_token]").val();
            console.log("eP No.: "+ePno);
            console.log("changedDate: "+changedDate);

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: "/trigger/gfmas/apive/update",
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"ep_no":ePno,"changed_date":changedDate},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Triggered!");
                    $("td.td-"+supplierId).addClass("text-success");
                    $("td.td-"+supplierId).html(resp.value);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Trigger Failed! Please try again.");
                    $("td.td-"+supplierId).addClass("text-danger");
                    $('#wait-modal').modal('hide');
                }
            });
        });
    </script>
@endsection