@extends('layouts.guest-dash')
@section('content')
<!--menu-->
<div class="block block-alt-noborder full">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/prod-support/rpt/summary') }}"><i class="fa fa-archive"></i><PERSON><PERSON><PERSON></a>

            <li>
                <a href="{{ url('/prod-support/rpt/data_lookup') }}"><i class="fa fa-tasks"></i>Data Lookup</a>
            </li>
        </ul>
    </div>

    <div style="display:none" id="formclassreport">
        <form class="form-horizontal form-bordered" style="display:none" id ="report_form" action="{{url('/prod-support/rpt/summary')}}" method="post">
            {{ csrf_field() }}

            <div class="block-options pull-right">
                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm">Tutup</span>
            </div>
            <br /><br /><br />
            <div id="first" class="step">
                <div class="block">
                    <input type="hidden" id="editid" name="editid" value="" class="form-control" style="width: 100px;">
                    <div class="form-group">
                        <label class="col-md-2 text-left" for="subject">Perkara</label>
                        <div class="col-md-10">
                            <textarea type="text" id="subject" name="subject" rows="2" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 text-left" for="remarks">Catatan</label>
                        <div class="col-md-10">
                            <textarea type="text" id="remarks" name="remarks" rows="2" class="form-control"></textarea>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 text-left" for="pemohon">Pemohon</label>
                        <div class="col-md-10">
                            <input id="pemohon" name="pemohon" type="text" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-2 text-left" for="apply_type">Jenis Permohonan</label>
                        <div class="col-md-4">
                            <select id="apply_type" name = "apply_type" class="form-control" style="width: 700px;">
                                <option value="">Please Select</option>
                                <option value="Urgent">Urgent</option>
                                <option value="Normal">Normal</option>
                            </select>
                        </div> 
                        <label class="col-md-2 text-left" for="statusterkini">Status Permohonan</label>
                        <div class="col-md-4">
                            <select id="statusterkini" name = "statusterkini" class="form-control" style="width: 700px;">
                                <option value="">Please Select</option>
                                @if(isset($listStatus))
                                @foreach($listStatus as  $list)
                                <option value="{{$list->name}}">{{$list->name}}</option>
                                @endforeach
                                @endif
                            </select>
                        </div>
                    </div>
                </div>
                <div class="block">
                    <div class="form-group">
                        <label class="col-md-3 text-left" for="received_date_apply">Tarikh Terima Permohonan</label>
                        <div class="col-md-3">
                            <input type="date" id="received_date_apply" name="received_date_apply" class="form-control">
                            <strong><label class="text-danger" id="reset_received_apply">Click to RESET</label></strong>
                        </div> 
                        <label class="col-md-3 text-left" for="data_date_req">Tarikh Data Diperlukan</label>
                        <div class="col-md-3">
                            <input type="date" id="data_date_req" name="data_date_req" class="form-control">
                            <strong><label class="text-danger" id="reset_date_req">Click to RESET</label></strong>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 text-left" for="submit_d5_date">Tarikh Serah Permohonan (D5)</label>
                        <div class="col-md-3">
                            <input type="date" id="submit_d5_date" name="submit_d5_date" class="form-control">
                            <strong><label class="text-danger" id="reset_submit_d5">Click to RESET</label></strong>
                        </div>
                        <label class="col-md-3 text-left" for="result_d5">Keputusan D5</label>
                        <div class="col-md-3">
                            <select id="result_d5" name = "result_d5" class="form-control" style="width: 700px;">
                                <option value="">Please Select</option>
                                <option value="Terima">Terima</option>
                                <option value="Tolak">Tolak</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 text-left" for="return_d5_date">Tarikh Serah Data (D5)</label>
                        <div class="col-md-3">
                            <input type="date" id="return_d5_date" name="return_d5_date" class="form-control">
                            <strong><label class="text-danger" id="reset_return_d5">Click to RESET</label></strong>
                        </div>
                    </div>
                </div>
                <div class="block">
                    <h5><strong><span class="text-danger">*Action By CDC</span></strong></h5>
                    <div class="form-group">
                        <label class="col-md-3 text-left" for="submit_apply_date">Tarikh Serah Permohonan (CDC)</label>
                        <div class="col-md-3">
                            <input type="date" id="submit_apply_date" name="submit_apply_date" class="form-control">
                            <strong><label class="text-danger" id="reset_apply_date">Click to RESET</label></strong>
                        </div>
                        <label class="col-md-3 text-left" for="received_date">Tarikh Terima Data</label>
                        <div class="col-md-3">
                            <input type="date" id="received_date" name="received_date" class="form-control">
                            <strong><label class="text-danger" id="reset_received_date">Click to RESET</label></strong>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-3 text-left" for="prepared_by">Disediakan Oleh (CDC)</label>
                        <div class="col-md-3">
                            <select id="prepared_by" name = "prepared_by" class="form-control" style="width: 700px;">
                                <option value="">Please Select</option>
                                @if(isset($listPengguna))
                                @foreach($listPengguna as  $list)
                                <option value="{{$list->name}}">{{$list->name}}</option>
                                @endforeach
                                @endif
                            </select>
                        </div>
                        <label class="col-md-3 text-left" for="tindakan">Tindakan</label>
                        <div class="col-md-3">
                            <select id="tindakan" name = "tindakan" class="form-control" style="width: 700px;">
                                <option value="">Please Select</option>
                                @if(isset($listTindakan))
                                @foreach($listTindakan as  $list)
                                <option value="{{$list->name}}">{{$list->name}}</option>
                                @endforeach
                                @endif
                            </select>
                        </div> 
                    </div>
                </div>
            </div>
            <div id="second" class="step">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-1 text-right" for="copy_subject">Perkara</label>
                        <div class="col-md-10">
                            <textarea type="text" id="copy_subject" name="copy_subject" rows="2" class="form-control" readonly=""></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-6">
                        <label class="col-md-1 text-left" for="copy_pemohon">Pemohon</label>
                        <div class="col-md-5">
                            <input id="copy_pemohon" name="copy_pemohon" type="text" class="form-control" readonly="">
                        </div>
                        <label class="col-md-3 text-right" for="copy_received_date_apply">Tarikh Terima Permohonan</label>
                        <div class="col-md-3">
                            <input type="text" id="copy_received_date_apply" name="copy_received_date_apply" class="form-control"readonly="">
                        </div>
                    </div>
                </div>
                <div class="col-sm-4"></div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label class="col-md-4 text-left" for="typeupload">Upload Document</label><br />
                        <select id="typeupload" name = "typeupload" class="form-control" style="width: 700px;">
                            <option value="">Please Select</option>
                            <option value="scripting">SQL File</option>
                            <option value="excel">Supporting Document</option>
                        </select>
                        <div class="ux-a-file-upload" id ="upload_script" style="display:none">
                            <input id="choosefile" type='file'  onchange='onChooseFile(event, onFileLoad.bind(this, "contents"))' accept=".sql"/>
                            <p id="contents" style="display:none" spellcheck="false"></p> 
                            <button type="button" class="btn btn-sm right btn-primary savescript"><i class="fa fa-save"></i> Simpan</button>
                        </div>
                        <div class="ux-a-file-upload" id ="upload_doc" style="display:none">
                            <input type="file" id="file-input-excel" name="file-input-excel" accept=".xlsx,.xlsm,.xlsb,.xltx,.xltm,.csv,.xls,.xlt,.xml,.xlam,.xlw,.xlc,.xlr,.doc,.docm,.docx,.pdf"/>
                        </div>
                        <button type="button" class="btn btn-sm right btn-primary submitexcel" style="display:none"><i class="fa fa-save"></i> Simpan</button>
                    </div>

                </div>
                <div id="table_for_serialize" class="col-md-12">
                    <input type="hidden" id="deldocid" name="deldocid" value="" class="form-control" style="width: 100px;">
                    <div class="table-responsive">
                        <table id="report_document" class="table table-vcenter table-condensed table-bordered" ></table>
                    </div>
                </div>
                <div id="modal_confirm_delete_details" class="modal fade">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h5> CONFIRMATION</h5>
                            </div>
                            <div class="modal-body text-center">
                                <input id="file_name" name="file_name" readonly="" style="font-family: Helvetica; font-family: 8.5; border: 0"/>
                                <label>Adakah anda ingin menghapus dokumen ini?</label> 
                            </div> 
                            <br/><br/>
                            <div class="modal-footer">
                                <button type="button" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YA</button>
                                <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">TIDAK</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-12 col-md-offset-5">
                    <input type="reset" class="btn btn-sm btn-warning" id="back" value="Back">
                    <input type="submit" name="Submit" value="Submit" class="btn btn-sm btn-primary" id="submitbutton" value="Next">
                </div>
            </div>
        </form>
    </div>
    <br />
    <button type="button" id="openAddTask" class="btn btn btn-primary" >Tambah</button>
    <br />
    @if($listReport != null)
    <div class="block">
        <div class="block-options">
            <label class="col-md-2 text-left" for="carian_by_date">Carian Tarikh Terima Permohonan</label>
            <div class="col-md-2">
                <input type="date" id="carian_by_date" name="carian_by_date" class="form-control">
                <strong><label class="text-danger" id="reset_carian_by_date">Click to RESET</label></strong>
            </div>
            <form action="{{ url('/prod-support/reporting/download') }}" method="post">
                {{ csrf_field() }}
                <div class="form-actions form-actions-button text-right ">
                    <button type="submit" id="downloadfromdb" class="btn btn btn-info" style="float: right;"><i class="fa fa-download"> Download</i></button>
                </div>
            </form>
        </div>
        <br />
        <div class="block-title block-options">
            <h1><i class="fa fa-tasks"></i> <strong>Senarai Status Permohonan Data ePerolehan</strong></h1>
        </div>

        <div class="table-responsive">
            <table id="senarai_summary_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Perkara</th>
                        <th class="text-center">Catatan</th>
                        <th class="text-center">Pemohon</th>
                        <th class="text-center">Tarikh Terima Permohonan</th>
                        <th class="text-center">Tarikh Data Diperlukan</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Action</th>
                        <th class="text-center">Bilangan hari process</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($listReport as $row=>$data)
                    <tr>
                        <td class="text-center">{{ ++$row }}</td>
                        <td class="text-left" width="20%" >
                            <textarea rows="5" class="form-control" style="width: 100%" readonly>{{ $data->perkara }}</textarea>
                        </td>
                        <td class="text-left" width="20%" >
                            <textarea rows="5" class="form-control" style="width: 100%" readonly>{{ $data->remarks }}</textarea>
                        </td>
                        <td class="text-center">{{ $data->pemohon }}</td>
                        <td class="text-center">{{ $data->received_date_apply }}</td>
                        <td class="text-center">{{ $data->data_required_date }}</td>
                        <td class="text-center">{{ $data->status }}</td>
                        <td class="text-center">
                            <a idno ="{{$data->id}}" 
                               perkara ="{{ $data->perkara }}"
                               remarks ="{{ $data->remarks }}"
                               pemohon ="{{ $data->pemohon }}"
                               received_date_apply ="{{ $data->received_date_apply }}"
                               received_date ="{{ $data->received_date }}"
                               data_required_date ="{{ $data->data_required_date }}"
                               tindakan ="{{ $data->tindakan }}"
                               prepared_by ="{{ $data->completed_by}}"
                               status ="{{ $data->status }}"
                               submit_d5 ="{{ $data->submit_d5_date}}"
                               return_d5 ="{{ $data->return_d5_date}}"
                               submit_cdc ="{{ $data->submit_cdc_date}}"
                               d5_result ="{{ $data->d5_result}}"
                               apply_type ="{{ $data->apply_type}}"
                               data-toggle="tooltip" title="Edit" class="btn btn-xs btn-info editbutton"><i class="fa fa-edit"></i></a>
                            <a href='#modal-list-trans-detail_table' 
                               data-toggle='modal' 
                               idno ="{{$data->id}}" title="View" class="btn btn-xs btn-primary viewlevel2summary"><i class="fa fa-tasks"></i></a>
                            <a idno ="{{$data->id}}" 
                               data-toggle="tooltip" title="Delete" class="btn btn-xs btn-danger deletereport"><i class="fa fa-times"></i></a>
                        </td>
                        <td class="text-center">{{ $data->bilangan_hari_process }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
            <div id="modal-list-trans-detail_table" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">More Info</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                                    </div>
                                    <div class="details_table table table-vcenter table-condensed table-bordered"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div id="modal_confirm_delete_report" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <input type="hidden" id="deldocid" name="deldocid" value="" class="form-control" style="width: 100px;">
                            <label>Adakah anda ingin menghapus dokumen ini?</label> 
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="button" id="submit_confirm_delete_report" name="submit_confirm_delete_report" class="btn btn-sm btn-info pull-left">YA</button>
                            <button type="button" id="cancel_submit_delete_report" name="cancel_submit_delete_report"  data-dismiss="modal" class="btn btn-sm btn-default">TIDAK</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>
@endsection

@section('jsprivate')
<script src="/js/pages/psReportEpWizardForm.js"></script>
<script>$(function () {
        $('#senarai_summary_datatable').dataTable({
            order: [[0, "asc"]],
            pageLength: 20,
            lengthMenu: [[20, 30, -1], [20, 30, 'All']],
            "columnDefs": [
                {
                    "targets": [8],
                    "visible": false,
                    "searchable": false
                }
                ],
                    "fnRowCallback": function (nRow, aData) {
                        if (aData[8] > 7) {
                            $('td', nRow).css('background-color', '#FC5937');
                                }
                        }
                    });
});</script>
<script>$(function () {
        FormsWizardReportEp.init();
    });</script>
<script>
    App.datatables();
    window.parent.document.body.style.zoom = 0.9;

    submit_apply_date.max = new Date().toISOString().split("T")[0];
    $('#submitbutton').on("click", function () {
        $('#to-top').click();
    });

    $("#openAddTask").on("click", function () {
        $('#formclassreport').show();
        $('#report_form').show();
        $('#openAddTask').hide();
        $('#upload_script').hide();
        $('#upload_doc').hide();
        $('.submitexcel').hide();
        $('#choosefile').val("");
        $('#editid').val("");
        $('#file-input-excel').val("");
        $('#file-input-word').val("");
        $('#typeupload').val("");
        $('#report_document').hide("");
        $('#subject').val("");
        $('#remarks').val("");
        $('#pemohon').val("");
        $('#prepared_by').val("");
        $('#statusterkini').val("");
        $('#tindakan').val("");
        $('#received_date').val("");
        $('#received_date_apply').val("");
        $('#data_date_req').val("");
        $('#submit_d5_date').val("");
        $('#return_d5_date').val("");
        $('#submit_apply_date').val("");
        $('#result_d5').val("");
        $('#apply_type').val("");
        $('#copy_subject').val("");
        $('#copy_pemohon').val("");
        $('#copy_received_date_apply').val("");
        $("#back").trigger("click");
    });

    $("#closeTaskForm").on("click", function () {
        $('#report_form').hide();
        $('#formclassreport').hide();
        $('#openAddTask').show();
        $('#upload_script').hide();
        $('#upload_doc').hide();
        $('.submitexcel').hide();
        $('#choosefile').val("");
        $('#file-input-excel').val("");
        $('#file-input-excel').val("");
        $('#typeupload').val("");
        $('#editid').val("");
        $('#subject').val("");
        $('#remarks').val("");
        $('#pemohon').val("");
        $('#prepared_by').val("");
        $('#statusterkini').val("");
        $('#tindakan').val("");
        $('#received_date').val("");
        $('#received_date_apply').val("");
        $('#data_date_req').val("");
        $('#submit_d5_date').val("");
        $('#return_d5_date').val("");
        $('#submit_apply_date').val("");
        $('#result_d5').val("");
        $('#apply_type').val("");
        $('#copy_subject').val("");
        $('#copy_pemohon').val("");
        $('#copy_received_date_apply').val("");
        $("#back").trigger("click");
    });

    $('#subject').bind('keyup keypress blur', function () {
        var input1 = $('#subject').val();
        $('#copy_subject').val(input1);
    });

    $('#pemohon').bind('keyup keypress blur', function () {
        var input1 = $('#pemohon').val();
        $('#copy_pemohon').val(input1);
    });

    $('#received_date_apply').bind('keyup keypress blur', function () {
        var input1 = $('#received_date_apply').val();
        $('#copy_received_date_apply').val(input1);
    });

    $("#typeupload").change(function () {
        if ($(this).val() === "scripting") {
            $('#upload_script').show();
            $('#upload_doc').hide();
            $('#file-input-excel').val("");
            $('#file-input-word').val("");
            $('.submitexcel').hide();
        } else if ($(this).val() === "excel") {
            $('#upload_doc').show();
            $('.submitexcel').show();
            $('#upload_script').hide();
            $('#choosefile').val("");
            $('#file-input-word').val("");
        }
        else {
            $('#upload_doc').hide();
            $('#upload_script').hide();
            $('#file-input-excel').val("");
            $('#choosefile').val("");
            $('.submitexcel').hide();
            $('#file-input-word').val("");
        }
    });

    function onFileLoad(elementId, event) {
        document.getElementById(elementId).innerText = event.target.result;
        contentScript = event.target.result;
    }

    function onChooseFile(event, onLoadFileHandler) {
        $('.savescript').show();
        if (typeof window.FileReader !== 'function')
            throw ("The file API isn't supported on this browser.");
        let
        input = event.target;
        if (!input)
            throw ("The browser does not properly implement the event object");
        if (!input.files)
            throw ("This browser does not support the `files` property of the file input.");
        if (!input.files[0])
            return undefined;
        let
        file = input.files[0];
        let
        fr = new FileReader();
        fr.onload = onLoadFileHandler;
        fileNameScript = file.name;
        fr.readAsText(file);
    }

    $('.savescript').click(function () {
        var reportid = $('#editid').val();
        $('.savescript').hide();
        var script = contentScript;
        var encodedString = btoa(script);
        $('#contents').html("");
        $.ajax({
            type: "POST",
            url: "/prod-support/reporting_applied/createscript/" + reportid,
            table: $('#table_for_serialize').serialize(),
            data: {
                '_token': $('input[name=_token]').val(),
                'script': encodedString,
                'scriptName': fileNameScript,
                'reportid': reportid
            },
        }).done(function (data) {
            console.log(data);
        });
        $.ajax({
            url: "/prod-support/report_document/carian/" + reportid,
            type: "GET",
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#report_document').html(data).fadeIn();
                $('#report_document_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
                });
            }
        });
    });

    $('#file-input-excel').click(function () {
        $('.submitexcel').show();
    });
    $('#file-input-word').click(function () {
        $('.submitexcel').show();
    });

    $('.submitexcel').click(function () {
        var type_format = $('#typeupload').val();
        var report_id = $('#editid').val();
        var documentData = new FormData();

        if (type_format === 'excel') {
            documentData.append('upload_excel_file', $('input#file-input-excel')[0].files[0]);
            documentData.append("_token", "{{ csrf_token() }}")
            documentData.append("type_format", type_format)
            documentData.append("reportid", report_id)
        } else if (type_format === 'word') {
            documentData.append('upload_excel_file', $('input#file-input-word')[0].files[0]);
            documentData.append("_token", "{{ csrf_token() }}")
            documentData.append("type_format", type_format)
        }
        var reportid = $('#editid').val();
        $('.submitexcel').hide();

        $.ajax({
            type: "POST",
            url: "/prod-support/reporting_applied/upload_excel/" + reportid,
            data: documentData,
            cache: false,
            contentType: false,
            processData: false,
            table: $('#table_for_serialize').serialize(),
        }).done(function (data) {
            console.log('done first');
            console.log(data);
            $.ajax({
                url: "/prod-support/report_document/carian/" + reportid,
                type: "GET",
                success: function (data) {
                    data = $(data)
                    console.log('masok sini tak table');
                    $('.spinner-loading').hide();
                    $('#report_document').html(data).fadeIn();
                    $('#report_document_datatable').dataTable({
                        order: [[0, "asc"]],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
                    });
                }
            });
        });
        console.log('teus masuk sini');
    });
    $('.viewlevel2summary').on("click", function () {
        $('.spinner-loading').show();
        $('#details_table').hide();
        let
        doc_id = $(this).attr('idno');
        $.ajax({
            type: "GET",
            url: "/prod-support/report/view_more_summary/" + doc_id,
        }).done(function (resp) {
            data = resp
            $('.spinner-loading').hide();
            $('.details_table').html(data).fadeIn();
            $('#details_table').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
            });
        });
    });

    function viewSecondLevelSummary(a) {
        $("#modal-list-trans-detail_table").modal('show');
        $('.spinner-loading').show();
        $('#details_table').hide();
        let
        doc_id = $(a).attr('idno');
        $.ajax({
            type: "GET",
            url: "/prod-support/report/view_more_summary/" + doc_id,
        }).done(function (resp) {
            data = resp
            $('.spinner-loading').hide();
            $('.details_table').html(data).fadeIn();
            $('#details_table').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
            });
        });
    }
    ;

    $(document).ready(function () {
        $('#table_for_serialize').on("click", '#deletebutton', function () {
            $("#modal_confirm_delete_details").modal('show');
            let
            doc_id = $(this).attr('docid');
            let
            file_name = $(this).attr('file_name');
            $('#deldocid').val(doc_id);
            $('#file_name').val(file_name);
        });
    });

    $('#submit_confirm_delete').on('click', function () {
        var deldocid = $('#deldocid').val();
        var reportid = $('#editid').val()

        $('#modal_confirm_delete_details').modal('hide');
        $.ajax({
            type: "POST",
            url: "/prod-support/report/delete_document/" + deldocid,
            table: $('#table_for_serialize').serialize(),
            data: {
                '_token': $('input[name=_token]').val(),
                'deleteid': deldocid,
            },
        }).done(function (resp) {
            console.log(resp);
        });

        $.ajax({
            url: "/prod-support/report_document/carian/" + reportid,
            type: "GET",
            success: function (data) {
                data = $(data)
                $('.spinner-loading').hide();
                $('#report_document').html(data).fadeIn();
                $('#report_document_datatable').dataTable({
                    order: [[0, "asc"]],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
                });
            }
        });

    });

    $('#reset_date_req').on("click", function () {
        $('#data_date_req').val("");
    });
    $('#reset_received_apply').on("click", function () {
        $('#received_date_apply').val("");
    });
    $('#reset_submit_d5').on("click", function () {
        $('#submit_d5_date').val("");
    });
    $('#reset_return_d5').on("click", function () {
        $('#return_d5_date').val("");
    });
    $('#reset_apply_date').on("click", function () {
        $('#submit_apply_date').val("");
    });
    $('#reset_received_date').on("click", function () {
        $('#received_date').val("");
    });
    $('#reset_carian_by_date').on("click", function () {
        $('#carian_by_date').val("");
    });

    $('.deletereport').on("click", function () {
        $("#modal_confirm_delete_report").modal('show');
        let
        idno = $(this).attr('idno');
        $('#deldocid').val(idno);
    });

    function deleteReport(a) {
        $("#modal_confirm_delete_report").modal('show');
        let
        idno = $(a).attr('idno');
        $('#deldocid').val(idno);
    }
    ;

    $('#submit_confirm_delete_report').on('click', function () {
        var idno = $('#deldocid').val()
        $('#modal_confirm_delete_details').modal('hide');
        $.ajax({
            type: "GET",
            url: "/prod-support/report/delete/" + idno,
        }).done(function (resp) {
            location.reload();
        });

    });

    function editButtonTriggered(a) {
        $('#to-top').click();
        $('#formclassreport').show();
        $('#openAddTask').show();
        $("#report_form").show();
        $('#upload_script').hide();
        $('#upload_doc').hide();
        $('#file-input-word').val("");
        $('.submitexcel').hide();
        $('#choosefile').val("");
        $('#file-input-excel').val("");
        $('#typeupload').val("");
        $("#back").trigger("click");
        let
        id = $(a).attr('idno');
        let
        perkara = $(a).attr('perkara');
        let
        remarks = $(a).attr('remarks');
        let
        received_date_apply = $(a).attr('received_date_apply');
        let
        received_date = $(a).attr('received_date');
        let
        data_required_date = $(a).attr('data_required_date');
        let
        tindakan = $(a).attr('tindakan');
        let
        prepared = $(a).attr('prepared_by');
        let
        status = $(a).attr('status');
        let
        pemohon = $(a).attr('pemohon');
        let
        submit_d5 = $(a).attr('submit_d5');
        let
        return_d5 = $(a).attr('return_d5');
        let
        submit_cdc = $(a).attr('submit_cdc');
        let
        d5_result = $(a).attr('d5_result');
        let
        apply_type = $(a).attr('apply_type');
        $('#editid').val(id);
        $('#subject').val(perkara);
        $('#copy_subject').val(perkara);
        $("#remarks").val(remarks);
        $("#received_date_apply").val(received_date_apply);
        $("#copy_received_date_apply").val(received_date_apply);
        $("#received_date").val(received_date);
        $("#data_date_req").val(data_required_date);
        $("#tindakan").val(tindakan);
        $("#prepared_by").val(prepared);
        $("#statusterkini").val(status);
        $("#pemohon").val(pemohon);
        $("#copy_pemohon").val(pemohon);
        $("#submit_d5_date").val(submit_d5);
        $("#return_d5_date").val(return_d5);
        $("#submit_apply_date").val(submit_cdc);
        $("#result_d5").val(d5_result);
        $("#apply_type").val(apply_type);
        $(document).ready(function () {
            $.ajax({
                url: "/prod-support/report_document/carian/" + id,
                type: "GET",
                success: function (data) {
                    data = $(data)
                    $('.spinner-loading').hide();
                    $('#report_document').html(data).fadeIn();
                    $('#report_document_datatable').dataTable({
                        order: [[0, "asc"]],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [[5, 10, 20, 30, 50, -1], [5, 10, 20, 30, 50, 'All']]
                    });
                }
            });
        });
    }
    ;
    $(".editbutton").on("click", function () {
        editButtonTriggered(this);
    });

    $(function () {
        $("#carian_by_date").on("change", function () {
            var getDate = $('#carian_by_date').val();
            console.log(getDate);
            findListData(getDate)
        });
        $('#reset_carian_by_date').on("click", function () {
            $('#carian_by_date').val("");
            var getDate = null;
            findListData(getDate)
        });

        function findListData(getDate) {
            $.ajax({
                type: 'GET',
                url: '/prod-support/reporting/find_by_date/' + getDate,
                contentType: "text/plain",
                dataType: 'json',
                success: function (data) {
                    myJsonData = data;
                    if (jQuery.isEmptyObject(myJsonData)) {
                        $("#senarai_summary_datatable").DataTable().clear();
                        var noid = null;
                        var perkara = null;
                        var remarks = null;
                        var pemohon = 'No macthing record found';
                        var received_date_apply = null;
                        var data_required_date = null;
                        var status = null;
                        var action = null;
                        var bilangan_hari_process = null;
                        $('#senarai_summary_datatable').dataTable().fnAddData([
                            noid,
                            perkara,
                            remarks,
                            pemohon,
                            received_date_apply,
                            data_required_date,
                            status,
                            action,
                            bilangan_hari_process,
                        ]);
                    } else
                    {
                        populateDataTable(myJsonData);
                    }
                },
                error: function (e) {
                    console.log("There was an error with your request...");
                    console.log("error: " + JSON.stringify(e));
                }
            });
        }
        // populate the data table with JSON data
        function populateDataTable(data) {
            // clear the table before populating it with more data
            $("#senarai_summary_datatable").DataTable().clear();
            var length = Object.keys(data).length;
            for (var i = 0; i < length + 1; i++) {
                var id = (data[i]['id'] === null) ? "" : data[i]['id'];
                var noid2 = i + 1;
                var noid = '<div class="text-center">' + noid2 + '</div>'
                var perkara2 = (data[i]['perkara'] === null) ? "" : data[i]['perkara'];
                var perkara = '<textarea rows="5" class="form-control" style="width: 100%" readonly="">' + perkara2 + '</textarea>'
                var remarks2 = (data[i]['remarks'] === null) ? "" : data[i]['remarks'];
                var remarks = '<textarea rows="5" class="form-control" style="width: 100%" readonly="">' + remarks2 + '</textarea>'
                var pemohon2 = (data[i]['pemohon'] === null) ? "" : data[i]['pemohon'];
                var pemohon = '<div class="text-center">' + pemohon2 + '</div>'
                var tindakan = (data[i]['tindakan'] === null) ? "" : data[i]['tindakan'];
                var received_date = (data[i]['received_date'] === null) ? "" : data[i]['received_date'];
                var received_date_apply2 = (data[i]['received_date_apply'] === null) ? "" : data[i]['received_date_apply'];
                var received_date_apply = '<div class="text-center">' + received_date_apply2 + '</div>'
                var data_required_date2 = (data[i]['data_required_date'] === null) ? "" : data[i]['data_required_date'];
                var data_required_date = '<div class="text-center">' + data_required_date2 + '</div>'
//                var data_required_description = (data[i]['data_required_description'] === null) ? "" : data[i]['data_required_description'];
                var status2 = (data[i]['status'] === null) ? "" : data[i]['status'];
                var status = '<div class="text-center">' + status2 + '</div>'
                var completed_by = (data[i]['completed_by'] === null) ? "" : data[i]['completed_by'];
//                var completed_date = (data[i]['completed_date'] === null) ? "" : data[i]['completed_date'];
                var submit_d5_date = (data[i]['submit_d5_date'] === null) ? "" : data[i]['submit_d5_date'];
                var return_d5_date = (data[i]['return_d5_date'] === null) ? "" : data[i]['return_d5_date'];
                var submit_cdc_date = (data[i]['submit_cdc_date'] === null) ? "" : data[i]['submit_cdc_date'];
                var d5_result = (data[i]['d5_result'] === null) ? "" : data[i]['d5_result'];
                var apply_type = (data[i]['apply_type'] === null) ? data[i]['apply_type'] : "";
                var bilangan_hari_process = data[i]['bilangan_hari_process'];
                var action2 = '<a idno = "' + id + '" perkara ="' + perkara2 + '" remarks="' + remarks2 + '" received_date_apply="' + received_date_apply2 + '" received_date = "' + received_date + '" data_required_date="' + data_required_date2 + '" \n\
                                    tindakan = "' + tindakan + '" prepared_by = "' + completed_by + '" status = "' + status2 + '" pemohon="' + pemohon2 + '" submit_d5="' + submit_d5_date + '" return_d5="' + return_d5_date + '" submit_cdc="' + submit_cdc_date + '" d5_result="' + d5_result + '" \n\
                                    apply_type = "' + apply_type + '"\n\
                                    data-toggle="tooltip" onClick= "editButtonTriggered(this);" title="Edit" class="btn btn-xs btn-info editbutton"><i class="fa fa-edit"></i></a>\n\
                                <a idno = "' + id + '"\n\
                                    data-toggle="modal" onClick= "viewSecondLevelSummary(this);" title="View" class="btn btn-xs btn-primary viewlevel2summary"><i class="fa fa-tasks"></i></a>\n\
                                <a idno = "' + id + '"\n\
                                    data-toggle="tooltip" onClick= "deleteReport(this);" title="Delete" class="btn btn-xs btn-danger deletereport"><i class="fa fa-times"></i></a>'
                var action = '<div class="text-center">' + action2 + '</div>'
                // You could also use an ajax property on the data table initialization
                $('#senarai_summary_datatable').dataTable().fnAddData([
                    noid,
                    perkara,
                    remarks,
                    pemohon,
                    received_date_apply,
                    data_required_date,
                    status,
                    action,
                    bilangan_hari_process,
                ]);
            }
        }
    })


</script>
@endsection




