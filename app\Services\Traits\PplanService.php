<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;


trait PplanService
{

    protected function getPPCreation($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pp.PLAN_ID , PLAN_NO, decode(pp.PLAN_TYPE_ID,'255','<PERSON><PERSON><PERSON>', '256','<PERSON><PERSON><PERSON><PERSON><PERSON>') AS type_pp, plan_year, ptjv.ORG_PROFILE_ID , ptjd.CODE_NAME, ptjv.ORG_NAME ,ptjv.ORG_CODE,  ptjv2.ORG_PROFILE_ID, ptjd2.CODE_NAME , ptjv2.ORG_CODE kptjcode, ptjv2.ORG_NAME kptjname, ptjv3.ORG_PROFILE_ID, ptjd3.CODE_NAME, ptjv3.ORG_CODE pptjcode, ptjv3.ORG_NAME pptjname,
      ptjv4.ORG_PROFILE_ID, ptjd4.CODE_NAME , ptjv4.ORG_CODE mptjcode, ptjv4.ORG_NAME mptjname
       FROM PM_ORG_PROFILE ptj, PM_ORG_VALIDITY ptjv, PM_PARAMETER_DESC ptjd,
       PM_ORG_PROFILE ptj2, PM_ORG_VALIDITY ptjv2, PM_PARAMETER_DESC ptjd2, 
       PM_ORG_PROFILE ptj3, PM_ORG_VALIDITY ptjv3, PM_PARAMETER_DESC ptjd3, 
       PM_ORG_PROFILE ptj4, PM_ORG_VALIDITY ptjv4, PM_PARAMETER_DESC ptjd4,
       PP_PLAN pp--, PP_ACTOR pa 
       WHERE ptj.ORG_PROFILE_ID = ptjv.ORG_PROFILE_ID 
       AND ptj.ORG_TYPE_ID = ptjd.PARAMETER_DESC_ID
       AND ptj.parent_org_profile_id = ptj2.org_profile_id
       AND ptj2.ORG_TYPE_ID = ptjd2.PARAMETER_DESC_ID
       AND ptj2.ORG_PROFILE_ID = ptjv2.ORG_PROFILE_ID
       AND ptj2.parent_org_profile_id = ptj3.org_profile_id
       AND ptj3.ORG_TYPE_ID = ptjd3.PARAMETER_DESC_ID
       AND ptj3.ORG_PROFILE_ID = ptjv3.ORG_PROFILE_ID
       AND ptj3.parent_org_profile_id = ptj4.org_profile_id
       AND ptj4.ORG_TYPE_ID = ptjd4.PARAMETER_DESC_ID
       AND ptj4.ORG_PROFILE_ID = ptjv4.ORG_PROFILE_ID
       AND ptjv.RECORD_STATUS = 1
       AND ptjv2.RECORD_STATUS = 1
       AND ptjv3.RECORD_STATUS = 1
       AND ptjv4.RECORD_STATUS = 1
       AND ptj.ORG_PROFILE_ID = pp.ORG_PROFILE_ID 
       AND PLAN_NO = ?", array($docNo));
        return $query;
    }

    protected function getItemPE($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT  pei.vot_fund_type_id, ppd.CODE_NAME ,
    pei.EXPENDITURE_ITEM_ID, 
    pga.GL_ACC_CODE, 
    pga.DESCRIPTION, 
    pei.BUDGET_AMT, 
    pei.PLAN_AMT, 
    pei.NON_EP_AMT, 
    pr.REMARK, 
    DECODE(pei.RECORD_STATUS, '1', 'Aktif (1)', '2','deleted (2)', '9', 'Tidak Aktif (9)') AS STATUS, 
    pei.CHANGED_DATE as tarikh_akhir_kemaskini
FROM 
   PM_PARAMETER_DESC ppd,  PP_EXPENDITURE_ITEM pei
JOIN 
    PM_GL_ACCOUNT pga ON pei.GL_ACCOUNT_ID = pga.GL_ACCOUNT_ID
JOIN 
    PP_PLAN pp ON pei.PLAN_ID = pp.PLAN_ID
LEFT JOIN 
    PP_REMARK pr ON pr.DOC_ID = pei.EXPENDITURE_ITEM_ID 
    AND pr.REMARK_TYPE = 2 
    AND pr.DOC_TYPE = 'PE'
WHERE ppd.PARAMETER_ID = pei.VOT_FUND_TYPE_ID 
AND ppd.LANGUAGE_CODE = 'ms'
AND pp.PLAN_NO = ?", array($docNo));
        return $query;
    }

    protected function getItemPP($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ppi.PROCUREMENT_TITLE tajuk_pelan, ppi.PROCUREMENT_DESC Perihal_Pelan, ppd.CODE_NAME Kategori_Jenis_Perolehan, ppd1.CODE_NAME Kaedah_Perolehan, to_char(ppi.INVITATION_DATE , 'DD/MM/YYYY') Tarikh_Jangkaan_Pelawaan, decode(ppi.RECORD_STATUS,'1','Aktif','2','deleted (2)','0','Tak Aktif') Status, ppi.CHANGED_DATE Tarikh_Akhir_Kemaskini ,ppi.ESTIMATED_AMT 
FROM PP_PROCUREMENT_ITEM ppi , PM_PARAMETER_DESC ppd, PM_PARAMETER_DESC ppd1,  PP_PLAN pp
WHERE pp.PLAN_ID = ppi.PLAN_ID 
AND ppi.PROCUREMENT_CAT_ID = ppd.PARAMETER_ID 
AND ppi.PROCUREMENT_TYPE_ID = ppd1.PARAMETER_ID 
AND ppd.LANGUAGE_CODE = 'ms'
AND ppd1.LANGUAGE_CODE = 'ms'
AND plan_no = ?", array($docNo));
        return $query;
    }

    protected function getListPpActor($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT PLAN_NO, pu.LOGIN_ID , role_code, pu.user_id,pa.user_name, pa.ACTION_DATE FROM PP_ACTOR pa , PP_PLAN pp, PM_USER pu 
WHERE DOC_ID = plan_id
AND pu.USER_ID = pa.USER_ID 
AND PLAN_NO =?
order by pa.ACTION_DATE desc", array($docNo));
        return $query;
    }

    protected function getListVerGroup($docNo,$docNo2)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
          SELECT PLAN_NO, pu.LOGIN_ID , role_code, pu.user_id, pu.user_name, pa.record_status FROM PP_PLAN_ACTOR pa , PP_PLAN pp, PM_USER pu 
WHERE pp.plan_id = pa.plan_id
AND pu.USER_ID = pa.USER_ID 
and role_code = 'PLAN_VERF_OFFICER'
and pa.record_status = 1
AND PLAN_NO = ?
AND pu.LOGIN_ID IN (SELECT pu.LOGIN_ID 
FROM PM_USER_ORG puo , PP_PLAN pp, PM_USER_ROLE pur, PM_USER pu 
WHERE pp.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
AND puo.USER_ORG_ID = pur.USER_ORG_ID 
AND puo.USER_ID = pu.USER_ID 
AND role_code = 'PLAN_VERF_OFFICER'
AND puo.RECORD_STATUS = 1
AND pur.RECORD_STATUS = 1
AND pp.PLAN_NO = ?)", array($docNo,$docNo2));
        return $query;
    }

    protected function getListAppGroup($docNo, $docNo2, $docNo3)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT PLAN_NO, pu.LOGIN_ID , role_code, pu.user_id, pu.user_name, pa.record_status FROM PP_PLAN_ACTOR pa , PP_PLAN pp, PM_USER pu 
WHERE pp.plan_id = pa.plan_id
AND pu.USER_ID = pa.USER_ID 
and role_code = 'PLAN_APPROVER'
and pa.record_status = 1
AND PLAN_NO = ?
AND pu.LOGIN_ID IN (SELECT pu.LOGIN_ID 
       FROM PM_ORG_PROFILE pop , PM_USER_ORG puo, PM_USER_ROLE pur, PM_USER pu, PM_ORG_VALIDITY pov 
       WHERE pop.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
       AND pop.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
       AND pur.USER_ORG_ID = puo.USER_ORG_ID 
       AND pu.USER_ID = puo.USER_ID 
       AND pur.RECORD_STATUS = 1
       AND pov.RECORD_STATUS = 1
       AND puo.RECORD_STATUS = 1
       AND role_code = 'PLAN_APPROVER'
       AND pop.GROUP_ORG_TYPE IN ('M','K')
       AND pop.PARENT_ORG_PROFILE_ID = (SELECT ptj.PARENT_ORG_PROFILE_ID
       FROM PM_ORG_PROFILE ptj, PM_ORG_PROFILE pt , PP_PLAN pp
       WHERE pt.ORG_PROFILE_ID = ptj.ORG_PROFILE_ID
       AND pt.ORG_PROFILE_ID = pp.ORG_PROFILE_ID  
       AND PLAN_NO = ?) 
       union
       SELECT pu.LOGIN_ID
FROM PM_USER_ORG puo , PP_PLAN pp, PM_USER_ROLE pur, PM_USER pu , PM_ORG_VALIDITY pov , PM_ORG_PROFILE pop 
WHERE pp.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
AND puo.USER_ORG_ID = pur.USER_ORG_ID 
AND puo.ORG_PROFILE_ID = pop.ORG_PROFILE_ID 
AND pop.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
AND puo.USER_ID = pu.USER_ID 
AND role_code = 'PLAN_APPROVER'
AND puo.RECORD_STATUS = 1
AND pov.RECORD_STATUS = 1
and pur.RECORD_STATUS = 1
AND pop.GROUP_ORG_TYPE IS null
AND pp.PLAN_NO = ?)", array($docNo, $docNo2, $docNo3));
        return $query;
    }

    protected function getListPpOff($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT plan_no, pu.LOGIN_ID , pu.USER_NAME 
FROM PM_USER_ORG puo , PP_PLAN pp, PM_USER_ROLE pur, PM_USER pu 
WHERE pp.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
AND puo.USER_ORG_ID = pur.USER_ORG_ID 
AND puo.USER_ID = pu.USER_ID 
AND role_code = 'PLAN_OFFICER'
AND puo.RECORD_STATUS = 1
AND pur.RECORD_STATUS = 1
AND pp.PLAN_NO = ?", array($docNo));
        return $query;
    }

    protected function getListPpVeriOff($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT plan_no, pu.LOGIN_ID , pu.USER_NAME 
FROM PM_USER_ORG puo , PP_PLAN pp, PM_USER_ROLE pur, PM_USER pu 
WHERE pp.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
AND puo.USER_ORG_ID = pur.USER_ORG_ID 
AND puo.USER_ID = pu.USER_ID 
AND role_code = 'PLAN_VERF_OFFICER'
AND puo.RECORD_STATUS = 1
AND pur.RECORD_STATUS = 1
AND pp.PLAN_NO = ?", array($docNo));
        return $query;
    }

    protected function getListPpApp($docNo, $docNo1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pu.LOGIN_ID , pu.USER_NAME , pov.ORG_CODE , pov.ORG_NAME 
       FROM PM_ORG_PROFILE pop , PM_USER_ORG puo, PM_USER_ROLE pur, PM_USER pu, PM_ORG_VALIDITY pov 
       WHERE pop.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
       AND pop.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
       AND pur.USER_ORG_ID = puo.USER_ORG_ID 
       AND pu.USER_ID = puo.USER_ID 
       AND pur.RECORD_STATUS = 1
       AND pov.RECORD_STATUS = 1
       AND puo.RECORD_STATUS = 1
       AND role_code = 'PLAN_APPROVER'
       AND pop.GROUP_ORG_TYPE IN ('M','K')
       AND pop.PARENT_ORG_PROFILE_ID = (SELECT ptj.PARENT_ORG_PROFILE_ID
       FROM PM_ORG_PROFILE ptj, PM_ORG_PROFILE pt , PP_PLAN pp
       WHERE pt.ORG_PROFILE_ID = ptj.ORG_PROFILE_ID
       AND pt.ORG_PROFILE_ID = pp.ORG_PROFILE_ID  
       AND PLAN_NO = ?) 
       union
       SELECT pu.LOGIN_ID , pu.USER_NAME , pov.ORG_CODE , pov.ORG_NAME 
FROM PM_USER_ORG puo , PP_PLAN pp, PM_USER_ROLE pur, PM_USER pu , PM_ORG_VALIDITY pov , PM_ORG_PROFILE pop 
WHERE pp.ORG_PROFILE_ID = puo.ORG_PROFILE_ID 
AND puo.USER_ORG_ID = pur.USER_ORG_ID 
AND puo.ORG_PROFILE_ID = pop.ORG_PROFILE_ID 
AND pop.ORG_PROFILE_ID = pov.ORG_PROFILE_ID 
AND puo.USER_ID = pu.USER_ID 
AND role_code = 'PLAN_APPROVER'
AND puo.RECORD_STATUS = 1
AND pov.RECORD_STATUS = 1
and pur.RECORD_STATUS = 1
AND pop.GROUP_ORG_TYPE IS null
AND pp.PLAN_NO = ?
ORDER BY 2", array($docNo, $docNo1));
        return $query;
    }

    protected function getListTracking($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT ptd.TRACKING_DIARY_ID, ptd.DOC_TYPE, ptd.DOC_NO, ptd.ACTION_DESC, ptd.ACTIONED_DATE, ptd.ROLE_CODE, ptd.STATUS_ID, psd.STATUS_NAME 
FROM PM_TRACKING_DIARY ptd , PM_STATUS_DESC psd 
WHERE ptd.STATUS_ID = psd.STATUS_ID 
AND psd.LANGUAGE_CODE = 'en'
AND DOC_NO = ?
ORDER BY ptd.ACTIONED_DATE desc", array($docNo));
        return $query;
    }

    protected function searchTransactionPp($docNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
        SELECT pp.PLAN_NO AS doc_no , pws.DOC_TYPE , pws.DOC_ID , pws.WORKFLOW_STATUS_ID ,TO_CHAR (pws.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED,
pws.CHANGED_DATE, pp.PLAN_YEAR , DECODE(pp.PLAN_TYPE_ID,'255','Perolehan','256','Perbelanjaan') pp_type , pws.IS_CURRENT , psd.STATUS_ID ,psd.STATUS_NAME ,
decode (pu.USER_ID , '' , 'null', pu.USER_ID) AS CREATED_USER_ID,
                    decode (pu.LOGIN_ID , '' , 'null', pu.LOGIN_ID) AS CREATED_LOGIN_ID,
                    decode (pu1.USER_ID , '' , 'null', pu1.USER_ID) AS CHANGED_USER_ID,
                    decode (pu1.USER_ID , '' , 'null', pu1.LOGIN_ID) AS CHANGED_LOGIN_ID
FROM PP_WORKFLOW_STATUS pws, PP_PLAN pp , pm_status pm, PM_STATUS_DESC psd, pm_user pu, pm_user pu1
WHERE pws.DOC_ID = pp.PLAN_ID 
AND pws.STATUS_ID = pm.STATUS_ID 
AND psd.STATUS_ID = pm.STATUS_ID 
AND pu.USER_ID = pws.CREATED_BY 
AND pu1.USER_ID(+) = pws.CHANGED_BY 
AND pm.MODULE_CODE = 'PP'
AND psd.LANGUAGE_CODE = 'ms'
AND pp.PLAN_NO = ?
ORDER BY pws.CHANGED_DATE DESC", array($docNo));
        return $query;
    }
}
