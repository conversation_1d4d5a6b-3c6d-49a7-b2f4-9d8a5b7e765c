/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
function populateTable(csrf, userId, assignment, state, offset, limit) {

    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/worklist/userid/" + userId,
        data: {"_token": csrf, "userId": userId, "assignment": assignment, "state": state, "offset": offset, "limit": limit},
        error: function (xhr, status, error) {
            $('#failed').show();
            document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
        }
    }).done(function (resp) {
        $('#first_table').hide();
        $('#modal_spinner').modal('hide');

        if (resp.status === 'success') {

            var pageNo = 0;
            if (offset < 0) {
                pageNo = 1;
            } else {
                pageNo = +offset + 1;
            }
            var html = '<thead style="background-color:#f2f2f2;"><tr><th class="text-center">Instance</th><th class="text-center">Title</th><th class="text-center">Activity</th><th class="text-center">Created</th></tr></thead>';

            for (const [listdata, value] of Object.entries(resp.listdata["worklistItem"])) {
                html += '<tbody style="font-size:80%;"><tr>';
                var a = value["compositeDN"].split('*');
                var link = '<a href="/bpm/instance/find/?composite_module=';
                link += a[0];
                link += '&composite_instance_id=';
                link += value["instanceId"];
                link += '" target="_blanks">';
                link += value["instanceId"];
                link += '</a>';

                var date = new Date(value["createDate"]);
                let formatted_date = date.getDate() + "/" + (date.getMonth() + 1) + "/" + date.getFullYear() + " " + date.getHours() + ":" + (date.getMinutes() < 10 ? '0' : '') + date.getMinutes() + ":" + (date.getSeconds() < 10 ? '0' : '') + date.getSeconds();
                var suffix = "AM";
                if (date.getHours() >= 12) {
                    suffix = "PM";
                }

                html += "<td>" + link + "</td>";
                html += '<td data-id="' + value["taskId"] + '" class="worklistProcess" data-target="worklist_flow"><a style="color:black;">' + value["title"] + '</a></td>';
                html += "<td>" + value["taskName"] + "</td>";
                html += "<td>" + formatted_date + " " + suffix + "</td>";
            }
            html += "</tr><tbody>";
            html += '<tfoot><tr><td colspan="4" style="font-size:90%;text-align: right;"><i>Page ' + pageNo + '</i></td></tr></tfoot>';
            document.getElementById("worklist_table").innerHTML = html;
        } else {
            $('#statusApi').show();
            $("#statusApi").html(resp.statusApi);
        }

        showWorklistTask();
    });
}