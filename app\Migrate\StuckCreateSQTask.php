<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\BpmApiService;
use Guzzle;
use GuzzleHttp\Client;

class StuckCreateSQTask {
    use PayloadGeneratorService;
    use BpmApiService;

    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
       
        
        /** USing list doc_no **/
        
        $listDocNo = array(
            //'SQ200000000965858'

            );
        dump("TOTAL FOUND: ".count($listDocNo));
        $counter = 0;
        foreach ($listDocNo as $docNo){
            dump($counter++);
           self::initiateTaskSQ($docNo);
        }
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    public static function initiateTaskSQ($docNo) {
        $clsCreateSQ = new StuckCreateSQTask;
        
        $isRefire = false;
        
        $checkTask = $clsCreateSQ->findAPITaskBPMListDocAndModule($docNo, 'SourcingDP');
        if(count($checkTask['result']) > 0){
            $dataTasks = collect($checkTask['result']);
            $listAssignedTask = $dataTasks->where('state','ASSIGNED')->all();
            //dd(collect($listAssignedTask)->first());
            if(collect($listAssignedTask)->count() == 0){
                $isRefire = true;
            }else{
                $tk = collect($listAssignedTask)->first();
                dump('Checking '.$docNo.' already has task created. TASK >> STATE: '.$tk['state'] .' TaskID: '.$tk['taskId'] . ' -- instance_id : '.$tk['instanceId']. ' -- version : '.$tk['compositeDN']);
            }
        }
        
        if(count($checkTask['result']) == 0){
            $isRefire = true;
        }
        
        if($isRefire === true){
            $xmlData = self::getXMLCreateSQ($docNo);
            if($xmlData != null){
                dump('##### Start Create SQ into BPM : '.$docNo);
                //dump($xmlData);
                self::triggerPOSTCreateSQ($xmlData);  
                sleep(1);
                $dataTaskCheck = $clsCreateSQ->findAPITaskBPMListDocAndModule($docNo, 'SourcingDP');
                dump('Result :- ');
                if(count($dataTaskCheck['result']) > 0 && $dataTaskCheck['result'][0] != null){
                    dump(' TASK >> STATE: '.$dataTaskCheck['result'][0]['state'] .' TaskID: '.$dataTaskCheck['result'][0]['taskId'] . ' -- instance_id : '.$dataTaskCheck['result'][0]['instanceId']. ' -- version : '.$dataTaskCheck['result'][0]['compositeDN']);
                }else{
                    dump('  > not found new instance.  try check.. ');
                    dump('');
                }
            }
        }
    }
    
    protected static function getXMLCreateSQ($docNo) {
        $classCreateSQ= new StuckCreateSQTask;
        $simpleQuoteDetails = $classCreateSQ->getSimpleQuoteDetails($docNo);

        if ($simpleQuoteDetails != null) {
            $orgDetails = $classCreateSQ->getOrgProfileDetails($simpleQuoteDetails->org_profile_id);
            $taskPerformerDetails = $classCreateSQ->getTaskPerformerdetails($simpleQuoteDetails->created_by);
            $simpleQuoteDetails->start_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->start_date));
            $simpleQuoteDetails->end_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->end_date));
            
            $title = str_replace("&", "&amp;", $simpleQuoteDetails->title);
            $ptjName = str_replace("&", "&amp;", $orgDetails[0]->ptjname);
            $ptjAddress = str_replace("&", "&amp;", $orgDetails[0]->ptjaddress);
            
            $xmlPayload = ""
                    . "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' "
                    . "xmlns:sim='http://xmlns.oracle.com/bpmn/bpmnProcess/SimpleQuoteCreation' "
                    . "xmlns:scd='http://xmlns.oracle.com/bpm/bpmobject/Data/SC_DirectPurchase_Data'>
                            <x:Header/>
                            <x:Body>
                                <sim:start>
                                    <scd:SC_DirectPurchase_Data>
                                        <scd:title>".$title."</scd:title>
                                        <scd:dp_id>".$simpleQuoteDetails->dp_id."</scd:dp_id>
                                        <scd:rn_id>". $simpleQuoteDetails->rn_id ."</scd:rn_id>
                                        <scd:pid_id />
                                        <scd:dp_mode>SQ</scd:dp_mode>
                                        <scd:url_link />
                                        <scd:ptj_name>" . $ptjName . "</scd:ptj_name>
                                        <scd:ptj_address>". $ptjAddress ."</scd:ptj_address>
                                        <scd:recommend>user011</scd:recommend>
                                        <scd:approver>?</scd:approver>
                                        <scd:contract_id />
                                        <scd:contract_type />
                                        <scd:frequency />?
                                        <scd:loi_loa_creator />
                                        <scd:start_date>" . $simpleQuoteDetails->start_date . "+08:00</scd:start_date>
                                        <scd:end_date>" . $simpleQuoteDetails->end_date . "+08:00</scd:end_date>
                                        <scd:rn_document_number>". $simpleQuoteDetails->request_note_no . "</scd:rn_document_number>
                                        <scd:contract_number />
                                        <scd:contract_request />
                                        <scd:sq_request />
                                        <scd:pid_expiry_date />
                                        <scd:pid_expiry_duration />
                                        <scd:pid_approver />
                                        <scd:kpi_value />
                                        <scd:sq_document_number />
                                        <scd:pid_document_number />
                                        <scd:desk_officer />
                                        <scd:sq_expiry_date />
                                        <scd:sq_expiry_duration />
                                    </scd:SC_DirectPurchase_Data>
                                    <sim:document_number>".$simpleQuoteDetails->quote_no."</sim:document_number>
                                    <sim:task_performer>".$taskPerformerDetails->login_id."</sim:task_performer>
                                </sim:start>
                            </x:Body>
                        </x:Envelope>";
            return $xmlPayload;
        }
        
        return null;
        
        
    }
    
    protected static  function triggerPOSTCreateSQ($xmlData) {
        dump('Start triggerPOSTCreateSQ...');
        //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePNo</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        //$xmlContents = '"'.$xmlData.'"';
        
        $client = new Client([
            'base_uri' => 'http://192.168.63.16:9003',
          ]);
          $payload = $xmlData;
          $response = $client->post('http://192.168.63.16:9003/soa-infra/services/default/SourcingDP/SimpleQuoteCreation.service', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'start',
            ]
          ]);
          $body = $response->getStatusCode();
          dump('    status: '.$body);

    }

}