<?php

namespace App\Console\Commands\LogTrace;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\ExportLogService;

class LogTraceCubeJob extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'LogTraceCubeJob';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To retrieve log trace as cube model';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $expsvc = new ExportLogService();
        try {
            $expsvc->export_cube_requests(Carbon::now());

            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $expsvc->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }
}
