@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/bpm/task/docno')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')


    
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Tugasan BPM<br>
                <small>Data carian tugasan adalah lewat sehari dari data production. 
                Carian tidak dapat dilakukan setiap hari pada 7PM sehingga 1AM.</small>
            </h1>
        </div>
    </div>
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Tugasan </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>
                <p class="text-info bolder">Data carian tugasan adalah lewat sehari dari data production.
                <br />
                Data yang ditanda warna <span class="bolder" style="color:chartreuse; "> light green </span> bermaksud tugasan terkini yang ada pada Pengguna.
                </p>
                <p class="text-danger bolder">
                    Jika ada 'TFP' pada ruangan column USER COMMENT . Sila jangan kemaskinikan Tugasan tersebut as 'ASSIGNED'
                </p>
                <div class="table-responsive">
                    <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">CREATED DATE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">TASK</th>
                            <th class="text-center">USER COMMENT</th>
                            <th class="text-center">COMPOSITE INSTANCE ID</th>
                            <th class="text-center">COMPOSITE</th>
                            <th class="text-center">ACQUIRED BY</th>
                            <th class="text-center">ASSIGNEES</th>
                            <th class="text-center">STATE</th>
                            <th class="text-center">CREATOR</th>
                            <th class="text-center">VERSION REASON</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr @if($data->state =='ASSIGNED') style = "background-color:chartreuse;" @endif>
                                <td class="text-center">{{ $data->createddate }}</td>
                                <td class="text-center">{{ $data->customattributestring1 }}</td>
                                <td class="text-center">{{ $data->activityname }}</td>
                                <td class="text-center">{{ $data->usercomment }}</td>
                                <td class="text-center">{{ $data->compositeinstanceid }}</td>
                                <td class="text-center">{{ $data->taskdefinitionid }}</td>
                                <td class="text-center">{{ $data->acquiredby }}</td>
                                <td class="text-left">{{ $data->assignees }}</td>
                                <td class="text-center">{{ $data->state }}</td>
                                <td class="text-center">{{ $data->creator }}</td>
                                <td class="text-center">{{ $data->versionreason }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection



