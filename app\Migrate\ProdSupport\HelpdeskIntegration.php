<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\ProdSupport;

use Carbon\Carbon;
use DB;
use App\Migrate\MigrateUtils;
use Guzzle;
use GuzzleHttp\Client;
use Log;
use App\Services\Traits\ProdSupport\PsPatchingService;

class HelpdeskIntegration {
    
    use PsPatchingService;

    /**
     * This for HELPDESK call API EPSS to request send request by Type 39.
     */
    public function retrieveAndSendHelpDeskByEachFixDtl($token) {
        Log::info(__METHOD__ .' entering.. ');
        $objDataFixDtl = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
        ->where('token_access',$token)->first();
        if($objDataFixDtl){
            $dataFixId = $objDataFixDtl->data_fix_id;
            $listDataFixDtl = $this->listEmailDataFix($dataFixId);
            if($listDataFixDtl && count($listDataFixDtl) > 0){

                /** SEND TO HELPDESK DEVELOPMENT */
                $isDevHelpdeskSent = env("IS_HELPDESK_DEV_INTEGRATE", false);
                if($isDevHelpdeskSent == true){
                    try {
                        $arrHelpdeskConfigDev = array();
                        $arrHelpdeskConfigDev['URL_HELPDESK_INTEGRATE'] = "https://support.commercedc.com.my";
                        $arrHelpdeskConfigDev['KEY_CLIENT_HELPDESK_INTEGRATE'] = "75C7195694045066488CCAEA2E35E777";
                        $arrHelpdeskConfigDev['FULL_URL_HELPDESK_INTEGRATE'] =  "/api/http.php/tickets.json";
                        $isSaveLogDev = false;
    
                        $collectDataFixDtl = collect($listDataFixDtl);
                        $objData = $collectDataFixDtl->where('data_fix_dtl_id',$objDataFixDtl->data_fix_dtl_id)->first();
                        $typeRequestItRequestEp= 39;  // 39
                        $subjectIR = "IT-".$objData->title_request;
                        $this->sendHelpDeskByEachFixDtl($objData,$typeRequestItRequestEp,$subjectIR,$arrHelpdeskConfigDev,$isSaveLogDev);  
                    } catch (\Exception $e) {
                        Log::error("Exception error : ".$e->getMessage());
                    }
                }
                
                
                
                
                /** SEND TO HELPDESK PRODUCTION */
                $arrHelpdeskConfig = array();
                $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] = env("URL_HELPDESK_INTEGRATE", "https://support.commercedc.com.my");
                $arrHelpdeskConfig['KEY_CLIENT_HELPDESK_INTEGRATE'] = env("KEY_CLIENT_HELPDESK_INTEGRATE", "75C7195694045066488CCAEA2E35E777");
                $arrHelpdeskConfig['FULL_URL_HELPDESK_INTEGRATE'] = $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] . "/api/http.php/tickets.json";
                $isSaveLog = true;

                $collectDataFixDtl = collect($listDataFixDtl);
                $objData = $collectDataFixDtl->where('data_fix_dtl_id',$objDataFixDtl->data_fix_dtl_id)->first();
                $typeRequestItRequestEp= 39;  // 39
                $subjectIR = "IT-".$objData->title_request;
                return $this->sendHelpDeskByEachFixDtl($objData,$typeRequestItRequestEp,$subjectIR,$arrHelpdeskConfig,$isSaveLog);    
            }

        }

        return "Invalid";
    }


     /*** 
        SEND HELPDESK BY ONE REQUEST AS ONE FIX DETAIL PATCHING
        SEND HELPDESK 2 API Request.
        1) For IT Helpdesk Request
        2) For IT Request Form (eP Form)
    */
    public function sendHelpDeskDataPatchRequestByEachPatchFixDetail($dataFixId) {
        Log::info(__METHOD__ .' entering.. ');
        $result = collect();
        $listDataFixDtl = $this->listEmailDataFix($dataFixId);
        if ($listDataFixDtl && count($listDataFixDtl) > 0) {
            foreach ($listDataFixDtl as $row) {

                /** SEND TO HELPDESK DEVELOPMENT */
                $isDevHelpdeskSent = env("IS_HELPDESK_DEV_INTEGRATE", false);
                if($isDevHelpdeskSent == true){
                    try {
                        $arrHelpdeskConfigDev = array();
                        $arrHelpdeskConfigDev['URL_HELPDESK_INTEGRATE'] = "https://support.commercedc.com.my";
                        $arrHelpdeskConfigDev['KEY_CLIENT_HELPDESK_INTEGRATE'] = "75C7195694045066488CCAEA2E35E777";
                        $arrHelpdeskConfigDev['FULL_URL_HELPDESK_INTEGRATE'] = "/api/http.php/tickets.json";
                        $isSaveLogDev = false;

                        $typeRequestItHelpdesk = 63;  // 63
                        $subjectIT = "CR-".$row->title_request;
                        $this->sendHelpDeskByEachFixDtl($row,$typeRequestItHelpdesk,$subjectIT,$arrHelpdeskConfigDev,$isSaveLogDev);
                    } catch (\Exception $e) {
                        Log::error("Exception error : ".$e->getMessage());
                    }
                }
                
                

                /** SEND TO HELPDESK PRODUCTION */
                $arrHelpdeskConfig = array();
                $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] = env("URL_HELPDESK_INTEGRATE", "https://support.commercedc.com.my");
                $arrHelpdeskConfig['KEY_CLIENT_HELPDESK_INTEGRATE'] = env("KEY_CLIENT_HELPDESK_INTEGRATE", "75C7195694045066488CCAEA2E35E777");
                $arrHelpdeskConfig['FULL_URL_HELPDESK_INTEGRATE'] = $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] . "/api/http.php/tickets.json";
                $isSaveLog = true;

                $typeRequestItHelpdesk = 63;  // 63
                $subjectIT = "CR-".$row->title_request;
                $resultTemp = $this->sendHelpDeskByEachFixDtl($row,$typeRequestItHelpdesk,$subjectIT,$arrHelpdeskConfig,$isSaveLog);
                $result->push($resultTemp);

            }
        }

        return $result;

    }

    /**
     * SEND only detail patch ID
     */
    public function sendHelpDeskDataPatchRequestByFixPatchDetail($dataFixId,$dataFixDetailId) {
        Log::info(__METHOD__ .' entering.. ');
        $result = collect();
        $listDataFixDtl = $this->listEmailDataFix($dataFixId);
        if ($listDataFixDtl && count($listDataFixDtl) > 0) {
            foreach ($listDataFixDtl as $row) {
                //dump('data_fix_dtl_id:  '.$row->data_fix_dtl_id);
                //dump('try to sent '.$dataFixDetailId);
               
                if($row->data_fix_dtl_id == $dataFixDetailId ){
                    //dump('now to sent '.$dataFixDetailId);
                    
                     /** SEND TO HELPDESK DEVELOPMENT */
                     $isDevHelpdeskSent = env("IS_HELPDESK_DEV_INTEGRATE", false);
                    if($isDevHelpdeskSent == true){ 
                        try {
                            $arrHelpdeskConfigDev = array();
                            $arrHelpdeskConfigDev['URL_HELPDESK_INTEGRATE'] = "https://support.commercedc.com.my";
                            $arrHelpdeskConfigDev['KEY_CLIENT_HELPDESK_INTEGRATE'] = "75C7195694045066488CCAEA2E35E777";
                            $arrHelpdeskConfigDev['FULL_URL_HELPDESK_INTEGRATE'] = "/api/http.php/tickets.json";
                            $isSaveLogDev = false;

                            $typeRequestItHelpdesk = 63;  // 63
                            $subjectIT = "CR-".$row->title_request;
                            $this->sendHelpDeskByEachFixDtl($row,$typeRequestItHelpdesk,$subjectIT,$arrHelpdeskConfigDev,$isSaveLogDev);
                        } catch (\Exception $e) {
                            Log::error("Exception error : ".$e->getMessage());
                        }
                    }
                    
                    

                    /** SEND TO HELPDESK PRODUCTION */
                    $arrHelpdeskConfig = array();
                    $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] = env("URL_HELPDESK_INTEGRATE", "https://support.commercedc.com.my");
                    $arrHelpdeskConfig['KEY_CLIENT_HELPDESK_INTEGRATE'] = env("KEY_CLIENT_HELPDESK_INTEGRATE", "75C7195694045066488CCAEA2E35E777");
                    $arrHelpdeskConfig['FULL_URL_HELPDESK_INTEGRATE'] = $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] . "/api/http.php/tickets.json";
                    $isSaveLog = true;

                    $typeRequestItHelpdesk = 63;  // 63
                    $subjectIT = "CR-".$row->title_request;
                    $resultTemp = $this->sendHelpDeskByEachFixDtl($row,$typeRequestItHelpdesk,$subjectIT,$arrHelpdeskConfig,$isSaveLog);
                    $result->push($resultTemp);
                }
            }
        }

        return $result;

    }


    /***
        $typeRequestHelpdesk can be 39,63
    */
    public  function sendHelpDeskByEachFixDtl($objData,$typeRequestHelpdesk,$subject , $arrHelpdeskConfig, $isSaveLog = true) {
        Log::info(__METHOD__ .' entering.. ');
        try {
            $urlHelpDeskBase =  $arrHelpdeskConfig['URL_HELPDESK_INTEGRATE'] ;
            $keyClientHelpDeskBase = $arrHelpdeskConfig['KEY_CLIENT_HELPDESK_INTEGRATE'];
            $fullUrl = $arrHelpdeskConfig['FULL_URL_HELPDESK_INTEGRATE'];

            $user = auth()->user();

            $typePortingRequest = 'Schedule Change';
            if($objData->type_porting === 'U'){
                $typePortingRequest = 'Urgent Change';
            }
            $data = null;
            try {

                $userCreatedReq = DB::connection('mysql_ep_support')->table('ep_login_history')
                                ->where('fullname', $objData->created_by)
                                ->first();
                $client = new \GuzzleHttp\Client([
                    'base_uri' => $urlHelpDeskBase,
                ]);
                $data = [
                    'headers' => [
                        'X-API-Key' => $keyClientHelpDeskBase,
                        'Content-Type' => 'application/json'
                    ],
                    'json' => [
                        'alert' => true,
                        'autorespond' => true,
                        'source' => "API",
                        'topicId' => $typeRequestHelpdesk,  
                        'name' => $userCreatedReq?$userCreatedReq->fullname:auth()->user()->user_name,
                        'email' => $userCreatedReq? $userCreatedReq->email:$user->getEmailUser($user->id)->email_address,
                        'phone' => '019-12345515',
                        'api_payload' => $objData->dtl_token_access,
                        'subject' => $subject,
                        'reason' => $subject,
                        'message' => $this->htmlListDataFixDtlHelpDeskByDtlPatchRecord($objData),
                        'dummy_body' => $typePortingRequest
                    ],
                    'verify' => false
                ];
                //dump($data);
                $response = $client->request('POST', $fullUrl, $data);

                $resultResp = json_decode($response->getBody(), true);

                $dataResult = array(
                    "status" => "Success",
                    "status_desc" => "Successfully sent to " . $fullUrl,
                    "payload" => json_encode($data),
                    "result" => $resultResp);
                
                if($isSaveLog === true){
                    $updateData = [
                        'sent_helpdesk' => 1,
                        'remark_helpdesk' => json_encode($dataResult),
                        'sent_helpdesk_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                            ->where('data_fix_dtl_id', $objData->data_fix_dtl_id)
                            ->update($updateData);
                }
                return $dataResult;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                //dump($ex->getMessage());
                $dataResult = array(
                    "status" => "Error",
                    "status_desc" => $ex->getMessage(),
                    "payload" => json_encode($data),
                    "result" => "No Response");
                if($isSaveLog === true){
                    $updateData = [
                        'sent_helpdesk' => 0,
                        'remark_helpdesk' => json_encode($dataResult),
                        'sent_helpdesk_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                            ->where('data_fix_dtl_id', $objData->data_fix_dtl_id)
                            ->update($updateData);
                }
                return $dataResult;
            }

        } catch (Exception $ex) {
            $dataResult = array(
                "status" => "Error",
                "status_desc" => $ex->getMessage(),
                "result" => 'Failed to connect');
            if($isSaveLog === true){    
                $updateData = [
                    'sent_helpdesk' => 0,
                    'remark_helpdesk' => json_encode($dataResult),
                    'sent_helpdesk_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                        ->where('data_fix_dtl_id', $objData->data_fix_dtl_id)
                        ->update($updateData);
            }
            return $dataResult;
        }
    }

    protected function htmlListDataFixDtlHelpDeskByDtlPatchRecord($obj) {
        Log::info(__METHOD__ .' entering.. ');
        if ($obj != null) {
            $urlDownload = url('/prod-support/download/data-patch-detail/' . $obj->dtl_token_access);
            $html = "data:text/html,<h3>Attached above is the sql to be " . $obj->port . ". Kindly be informed</h3>
                    <br />
                    <table>
                    <tr>
                            <th>Bil</th>
                            <th>Date/Time</th>
                            <th>CRM No. /Redmine No.</th>
                            <th>Module</th>
                            <th>Problem Description (from CRM)</th>
                            <th>Requester (supplier or PTJ, CDC, BPK)</th>
                            <th>Problem Type</th> 
                            <th>Endorsement By</th>
                    </tr>";
            $counter = 1;
            
            //priority display to redmine no
            $crmRedmine = $obj->redmineno != null ? $obj->redmineno : $obj->crm_no;
            $html = $html .
                    "<tr>
                    <td>$counter</td>
                    <td>$obj->datetime_porting</td>
                    <td>$crmRedmine</td>
                    <td>$obj->modulecode</td>
                    <td>$obj->problem</td>
                    <td>$obj->req</td>
                    <td>$obj->probtype</td>
                    <td>$obj->endorsement_by</td>
                    </tr>";
            
            $html = $html . "</table>";
            $html = $html . "<br /><p>Attachment Download <a href='$urlDownload' target='_blank' ><strong>Click here</strong></a></p>";
            return $html;
        }

        return '';
    }

    /*** 
    SEND HELPDESK BY ONE PACKAGE
    */
    public function sendHelpDeskDataPatchRequest($dataFixId) {
        Log::info(__METHOD__ .' entering.. ');
        try {
            $urlHelpDeskBase = env("URL_HELPDESK_INTEGRATE", "https://support.commercedc.com.my");
            $keyClientHelpDeskBase = env("KEY_CLIENT_HELPDESK_INTEGRATE", "75C7195694045066488CCAEA2E35E777");

            $fullUrl = $urlHelpDeskBase . "/api/http.php/tickets.json";

            $objDataFix = $this->getDetailDataFixPorting($dataFixId);
            if ($objDataFix) {
                $user = auth()->user();
                $typePortingRequest = 'Schedule Change';
                if($objDataFix->type_porting === 'U'){
                    $typePortingRequest = 'Urgent Change';
                }
                try {

                    $client = new \GuzzleHttp\Client([
                        'base_uri' => $urlHelpDeskBase,
                    ]);
                    $data = [
                        'headers' => [
                            'X-API-Key' => $keyClientHelpDeskBase,
                            'Content-Type' => 'application/json'
                        ],
                        'json' => [
                            'alert' => true,
                            'autorespond' => true,
                            'source' => "API",
                            'topicId' => 39, 
                            'name' => auth()->user()->user_name,
                            'email' => $user->getEmailUser($user->id)->email_address,
                            'phone' => '019-12345515',
                            'subject' => $objDataFix->name,
                            'reason' => $objDataFix->name,
                            'message' => $this->htmlListDataFixDtlHelpDesk($dataFixId),
                            'dummy_body' => $typePortingRequest
                        ],
                        'verify' => false
                    ];
                    $response = $client->request('POST', $fullUrl, $data);

                    $resultResp = json_decode($response->getBody(), true);

                    $dataResult = array(
                        "status" => "Success",
                        "status_desc" => "Successfully sent to " . $fullUrl,
                        "result" => $resultResp);

                    $updateData = [
                        'sent_helpdesk' => 1,
                        'remark_helpdesk' => json_encode($dataResult),
                        'sent_helpdesk_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                            ->where('data_fix_id', $dataFixId)
                            ->update($updateData);

                    $dataResult = array(
                        "status" => "Success",
                        "status_desc" => "Successfully sent to " . $fullUrl,
                        "result" => $resultResp);
                } catch (\GuzzleHttp\Exception\ClientException $ex) {
                    $dataResult = array(
                        "status" => "Error",
                        "status_desc" => $ex->getMessage(),
                        "result" => "No Response");
                    $updateData = [
                        'sent_helpdesk' => 0,
                        'remark_helpdesk' => json_encode($dataResult),
                        'sent_helpdesk_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                            ->where('data_fix_id', $dataFixId)
                            ->update($updateData);
                }
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            $dataResult = array(
                "status" => "Error",
                "status_desc" => $ex->getMessage(),
                "result" => 'Failed to connect');
            $updateData = [
                'sent_helpdesk' => 0,
                'remark_helpdesk' => json_encode($dataResult),
                'sent_helpdesk_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                    ->where('data_fix_id', $dataFixId)
                    ->update($updateData);
            return $dataResult;
        }
    }

    protected function htmlListDataFixDtlHelpDesk($dataFixId) {
        Log::info(__METHOD__ .' entering.. ');
        $listDataFixDtl = $this->listEmailDataFix($dataFixId);
        if ($listDataFixDtl != null) {
            $urlDownload = url('/prod-support/download/data-patch/' . $listDataFixDtl[0]->token_access);
            $html = "data:text/html,<h3>Attached above is the sql to be " . $listDataFixDtl[0]->port . ". Kindly be informed</h3>
                    <br />
                    <table>
                    <tr>
                            <th>Bil</th>
                            <th>Date/Time</th>
                            <th>CRM No</th>
                            <th>Module</th>
                            <th>Problem Description (from CRM)</th>
                            <th>Requester (supplier or PTJ, CDC, BPK)</th>
                            <th>Problem Type</th> 
                            <th>Endorsement By</th>
                    </tr>";
            $counter = 1;
            foreach ($listDataFixDtl as $key => $row) {
                $counter = $key + 1;
                //priority display to redmine no
                $crmRedmine = $row->redmineno != null ? $row->redmineno : $row->crm_no;
                $html = $html .
                        "<tr>
                        <td>$counter</td>
                        <td>$row->datetime_porting</td>
                        <td>$crmRedmine</td>
                        <td>$row->modulecode</td>
                        <td>$row->problem</td>
                        <td>$row->req</td>
                        <td>$row->probtype</td>
                        <td>$row->endorsement_by</td>
                        </tr>";
            }
            $html = $html . "</table>";
            $html = $html . "<br /><p>Attachment Download <a href='$urlDownload' target='_blank' ><strong>Click here</strong></a></p>";
            return $html;
        }

        return '';
    }
}
