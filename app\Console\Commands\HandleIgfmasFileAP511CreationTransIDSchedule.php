<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use SSH;
use App\EpSupportActionLog;
use App\Migrate\ReTriggerFileIGFMAS;

class HandleIgfmasFileAP511CreationTransIDSchedule extends Command {
    

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'igfmas-ap511-trigger-trans-id';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'insert available trans id for AP511 ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting .. '.$this->description. ' > Date '.Carbon::now());
        $dtStartTime = Carbon::now();
        
        try {
            
            //This to retrigger by process ID to create available record in DiInterfaceLog
            $resultRecords = ReTriggerFileIGFMAS::createDiInterfaceLogRecordByProcessId('GFM-140', 'AP511', 5);
            MigrateUtils::logDump($resultRecords);
            
            $logsdata = self::class . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);

        } catch (\Exception $exc) {
            
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }

    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleAP511TriggerSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
