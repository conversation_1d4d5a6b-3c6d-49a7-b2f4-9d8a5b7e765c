<?php

namespace App\Http\Controllers;

use App\Services\Traits\PplanService;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Excel;

class PplanController extends Controller
{

    use PplanService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findPPlan()
    {
        $getPpOff = $getPpApp = $getPpVeriOff = $getPpActor = $getItemListPE = $getItemListPP = $getTrackingDiary = 
        $votBItems = $votTItems = $votPItems = $votSItems = $danaItems = null;
        $docNo = request()->doc_no;
        $getPpInfo = $this->getPPCreation($docNo);
        $getPpActor = $this->getListPpActor($docNo);
        $getVeriGroup = $this->getListVerGroup($docNo,$docNo);
        $getAppGroup = $this->getListAppGroup($docNo,$docNo,$docNo);
        $getPpOff = $this->getListPpOff($docNo);
        $getPpVeriOff = $this->getListPpVeriOff($docNo);
        $getPpApp = $this->getListPpApp($docNo, $docNo);
        $getTrackingDiary = $this->getListTracking($docNo);
        $carianTemp = trim($docNo);

        if (strpos($docNo, 'PE') !== false) {
            $getItemListPE = $this->getItemPE($docNo);
            $votBItems = [];
            $votTItems = [];
            $votPItems = [];
            $votSItems = [];
            $danaItems = [];

            foreach ($getItemListPE as $itemList) {
                if ($itemList->vot_fund_type_id === "251") { 
                    $votBItems[] = $itemList;
                } elseif ($itemList->vot_fund_type_id === "253") { 
                    $votTItems[] = $itemList;
                } elseif ($itemList->vot_fund_type_id === "250") { 
                    $votPItems[] = $itemList;
                } elseif ($itemList->vot_fund_type_id === "252") { 
                    $votSItems[] = $itemList;
                } elseif ($itemList->vot_fund_type_id === "254") { 
                    $danaItems[] = $itemList;
                }
            }
        } else if (strpos($docNo, 'PP') !== false) {
            $getItemListPP = $this->getItemPP($docNo);
        } else {
            $docNo =  "Document not exist";
        }

        return view('list_pplan_summary', [
            'carian' => $carianTemp,
            'getPpInfo' => $getPpInfo,
            'docNo' => $docNo,
            'getPpActor' => $getPpActor,
            'getVeriGroup' => $getVeriGroup,
            'getAppGroup' => $getAppGroup,
            'getPpOff' => $getPpOff,
            'getPpVeriOff' => $getPpVeriOff,
            'getPpApp' => $getPpApp,
            'getTrackingDiary' => $getTrackingDiary,
            'getItemListPE' => $getItemListPE,
            'getItemListPP' => $getItemListPP,
            'votBItems' => $votBItems,
            'votTItems' => $votTItems,
            'votPItems' => $votPItems,
            'votSItems' => $votSItems,
            'danaItems' => $danaItems,
        ]);
    }

    public function findPPlanListWorkFlowStatusDocument($docNo)
    {
        $list = $this->searchTransactionDocPp($docNo);

        return view('include.list_trans_workflow', [
            'listDataWorkflow' => $list["listdata"]
        ]);
    }

    private function searchTransactionDocPp($search){
        $list = $this->searchTransactionPp($search);
        return [
            'listdata' => $list,
            'carian' => $search
        ];
    }
}
