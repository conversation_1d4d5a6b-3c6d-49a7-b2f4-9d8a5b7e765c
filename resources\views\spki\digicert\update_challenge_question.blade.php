@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <ul class="nav-horizontal text-center">
        <li>
            <a href="{{ url('/spki/digicert/challengequestion') }}"><i class="fa fa-tasks"></i> Challenge Question</a>
        </li>
        <li class="active">
            <a href="{{ url('/spki/digicert/updatechallengequestion') }}"><i class="fa fa-pencil-square-o"></i> Update Challenge Question</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/changepin') }}"><i class="fa fa-pencil"></i> Change PIN</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/resetpin') }}"><i class="fa fa-refresh"></i> Reset PIN</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/signing') }}"><i class="fa fa-sign-in"></i> Sign In</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/revoke') }}"><i class="fa fa-ban"></i> Revoke Cert</a>
        </li>
    </ul>
</div>
<div class="block block-alt-noborder full">
    <div class="row">
        <div class="col-lg-6">
            <div class="block">
                <div class="block-title">
                    <h2>DIGICERT : UPDATE CHALLENGE QUESTION</h2>
                    <h2 class="pull-right"><span><a style="color:darkblue;font-weight: bolder;text-decoration: underline;" href="{{ url('/spki/trustgate/challengequestion') }}"> SWITCH TRUSTGATE  </a></span></h2>
                </div>
                @if($status && $status !== 'Success')
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4> {{ $status }} </h4> 
                    <p>  {{ $result }} </p>
                </div>
                @endif
                <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
                <div class="block">
                    <form class="form-horizontal form-bordered" action="{{url('/spki/digicert/updatechallengequestion')}}" method="post" onsubmit="return false;">  
                        {{ csrf_field() }}  
                        <div class="form-group">
                            <label class="col-lg-3 control-label" for="ic_no">IC Number</label>
                            <div class="col-lg-9">
                                <input type="text" id="ic_no" name="ic_no" class="form-control" required="true" placeholder="5050505050506" value="{{ old('ic_no') }}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-3 control-label" for="ep_no">EP Number</label>
                            <div class="col-lg-9">
                                <input type="text" id="ep_no" name="ep_no" class="form-control" required="true" placeholder="gombak2026" value="{{ old('ep_no') }}"/>
                            </div>
                        </div>
                        <div class="form-group form-actions">
                            <center>
                                <div class="input-group">
                                    <button type="submit" class="btn btn-sm btn-info action_submit"></i> Submit </button>
                                </div>
                            </center>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    @endsection


    @section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script>
        $('div.form-actions').on("click", 'button.action_submit', function () {

            var icNo = $("#ic_no").val();
            var epNo = $("#ep_no").val();
            var csrf = $("input[name=_token]").val();

            $.ajax({
                url: "/spki/digicert/updatechallengequestion",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "ic_no": icNo, "ep_no": epNo},
                context: document.body
            }).done(function (resp) {
                if (resp["result"] !== '') {
                    window.open(resp["result"], '_blank');
                }
            });
        });

    </script>
    @endsection
