<?php

namespace App\Http\Controllers\AppSupport;

use App\Http\Controllers\Controller;
use App\Migrate\AppSupport\SmRejectBumiStatusFixIssue;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierFullGrantService;
use App\EpSupportActionLog;
use App\Services\Traits\TechRefreshIssueService;
use Carbon\Carbon;
use Log;
use Illuminate\Http\Request;
use stdClass;
use Exception;
use DB;
use Auth;
use Validator;

class TechRefreshIssueController extends Controller
{

    use TechRefreshIssueService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Retrieve and render a list of QT - Create SST
     *
     * @return \Illuminate\View\View
     */
    public function getMonitoringList(Request $request)
    {

        $page = $request->page;
        $list = null;
        $tableName = '';

        switch ($page) {
            case 'qt-create-sst':
                $tableName = 'QT Create SST';
                $list = $this->getQtCreateSST();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-closed':
                $tableName = 'QT Closed';
                $list = $this->getQtClosed();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-spec':
                $tableName = 'QT - Stuck Task Spec';
                $list = $this->getQtSpecStuck();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-ec':
                $tableName = 'QT - Stuck Task EC';
                $list = $this->getQtSpecStuckEC();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-oc':
                $tableName = 'QT - Stuck Task OC';
                $list = $this->getQtSpecStuckOC();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-tec':
                $tableName = 'QT - Stuck Task TEC';
                $list = $this->getQtSpecStuckTEC();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-fec':
                $tableName = 'QT - Stuck Task FEC';
                $list = $this->getQtSpecStuckFEC();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'qt-stuck-award':
                $tableName = 'QT - Stuck Task Awarded';
                $list = $this->getQtSpecStuckAwarded();
                if ($list) {
                    $this->populateBpmInstance($list, 'SourcingQT', 'qt_no');
                }
                break;
            case 'fl-invoice-cancellation':
                $tableName = 'FL - Invoice Cancellation';
                $list = $this->getFlInvoiceCancellation();
                if ($list) {
                    $this->populateBpmInstance($list, 'Fulfilment', 'poco_no');
                }
                break;
            case 'fl-poco-cancellation':
                $tableName = 'FL - POCO Cancellation';
                $list = $this->getFlPocoCancellation();
                if ($list) {
                    $this->populateBpmInstance($list, 'Order', 'doc_no');
                }
                break;
            case 'fl-po-expired':
                $tableName = 'FL - PO Expired';
                $list = $this->getFlPoExpired();
                if ($list) {
                    $this->populateBpmInstance($list, 'Order', 'doc_no');
                }
                break;
            default:
                $list = [];
        }

        $headers = [];
        $body = [];

        $hideField = ['composite_name', 'composite_version', 'is_instance_found'];
        $docType = ['qt_no', 'doc_no', 'poco_no'];
        if ($list) {
            foreach ($list[0] as $key => $value) {
                if (!in_array($key, $hideField)) {
                    $formattedKey = strtoupper(str_replace("_", " ", $key));
                    $headers[] = $formattedKey;
                    $body[] = $key;
                }
            }
        }

        $listdata = [
            "table_name" => $tableName,
            "header" => $headers,
            "body" => $body,
            "doc_type" => $docType,
            "data" => $list
        ];

        return view('app_support.techrefresh_monitoring', ['listdata' => $listdata]);
    }

    private function populateBpmInstance($list, $module, $field)
    {
        foreach ($list as $data) {
            $instance = $this->getBpmInstance($data->{$field}, $module);
            if (empty($instance)) {
                $data->is_instance_found = false;
            } else {
                $data->is_instance_found = true;
                $data->instance_id = $instance[0]->compositeinstanceid;
                $data->instance_created_date = $instance[0]->instance_created_date;
                $data->composite_name = $instance[0]->compositename;
                $data->composite_version = $instance[0]->compositeversion;
            }
        }
    }
}
