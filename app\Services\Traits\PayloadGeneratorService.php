<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use App\Services\Traits\SupplierService;
use Guzzle;

trait PayloadGeneratorService {
    
    protected function getDAN($dnCnNo) {
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");  

            $url = $urlMiddleware."/bpm/fl/dan?doc_no=".$dnCnNo;

            try {
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                return $resultResp;
            } catch (\GuzzleHttp\Exception\ClientException $ex) {
                return array(
                    "status" => "Error",
                    "result" => "No Response");
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect');
        }
    }

    protected function getSimpleQuoteDetails($documentNo) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->table('sc_request_note as a');
        $query->join('sc_quote as q', 'a.QUOTE_ID', '=', 'q.QUOTE_ID');
        $query->where('q.quote_no', '=', $documentNo);
        $query->select('q.title', 'a.quote_id AS dp_id', 'a.request_note_id AS rn_id', 'a.REQUEST_NOTE_NO', 'q.QUOTE_NO', 'q.START_DATE', 'q.END_DATE', 'a.CREATED_BY', 'a.ORG_PROFILE_ID');
//        $query->orderBy('b.CREATED_DATE', 'desc');

        return $query->first();
    }

    protected function getOrgProfileDetails($orgProfileId) {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT DISTINCT v3.org_code mincode, v3.org_name AS minname, v2.ORG_CODE AS PP_CODE,v2.ORG_PROFILE_ID pp_org_id,
                v1.org_code AS jabcode, v1.org_profile_id AS jabid,h.ADDRESS_TYPE,
                v.org_code AS ptjcode, v.org_profile_id AS ptjid, V.RECORD_STATUS,V2.RECORD_STATUS,
                v.org_name ptjname, d.ADDRESS_1||','||d.ADDRESS_2||','||d.ADDRESS_3||','||d.POSTCODE||','||c.CITY_NAME||','||ds.DISTRICT_NAME||','||s.STATE_NAME as ptjaddress
           FROM pm_org_profile pm,
                pm_org_validity v,
                pm_org_profile pm1,
                pm_org_validity v1,
                pm_org_profile pm2,
                pm_org_validity v2,
                pm_org_profile pm3,
                pm_org_validity v3,
                pm_address d,
                pm_address_type h,
                pm_city c,
                pm_district ds,
                pm_state s
          WHERE pm.org_profile_id = v.org_profile_id
            AND pm1.org_profile_id = v1.org_profile_id
            AND pm.parent_org_profile_id = pm1.org_profile_id
            AND pm2.org_profile_id = v2.org_profile_id
            AND pm1.parent_org_profile_id = pm2.org_profile_id
            AND pm3.org_profile_id = v3.org_profile_id
            AND pm2.parent_org_profile_id = pm3.org_profile_id
            AND v.record_status = 1
            AND v1.record_status = 1
            AND v2.record_status = 1
            AND v3.record_status = 1
            AND h.record_status = 1
            AND pm.org_type_id IN (
                        SELECT parameter_id
                          FROM pm_parameter
                         WHERE parameter_type = 'OT'
                               AND parameter_code = 'FPJ')              -- PTJ
            AND pm1.org_type_id IN (
                        SELECT parameter_id
                          FROM pm_parameter
                         WHERE parameter_type = 'OT'
                               AND parameter_code = 'FJA')          -- Jabatan
            AND pm3.org_type_id IN (
                   SELECT parameter_id
                     FROM pm_parameter
                    WHERE parameter_type = 'OT'                         -- Min
                      AND parameter_code = 'FMI')
            --AND us.IDENTIFICATION_NO = '790318065218'
            --AND v.org_code IN ('34205011')
            AND v.org_profile_id IN (?)
            and d.ADDRESS_ID = h.ADDRESS_ID
            and h.ORG_PROFILE_ID = v.ORG_PROFILE_ID
            and h.ADDRESS_TYPE = 'B'
            and d.CITY_ID = c.CITY_ID
            and d.DISTRICT_ID = ds.DISTRICT_ID
            and d.STATE_ID = s.STATE_ID
        -- AND rl.role_code LIKE '%FL_APPROVER%'
        ORDER BY 1", array($orgProfileId));

        return $query;
    }

    protected function getTaskPerformerdetails($userId) {
        $query = DB::connection('oracle_nextgen_rpt')
            ->table('PM_USER as a');
        $query->where('a.USER_ID', '=', $userId);
        $query->select('a.login_id', 'a.user_name', 'a.identification_no');

        return $query->first();
    }
    
    protected function generateRequestNotePayload($docNo) {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select 
                    rn.TITLE as title,
                    rn.REQUEST_NOTE_ID as dp_id,
                    rn.REQUEST_NOTE_ID as rn_id,
                    '   <suppliers>' || (select listagg(rnsi.SUPPLIER_ID, '</suppliers><suppliers>') within group (order by rnsi.PURCHASE_REQUEST_ID) from SC_PURCHASE_REQUEST pr inner join (select DISTINCT rnd.PURCHASE_REQUEST_ID, rsi.SUPPLIER_ID from SC_REQUEST_NOTE_DTL rnd inner join SC_REQUEST_ITEM ri on (ri.REQUEST_NOTE_ID=rnd.REQUEST_NOTE_ID and ri.REQUEST_ITEM_ID=rnd.REQUEST_ITEM_ID) inner join SC_REQUEST_SUPPLIER_ITEM rsi on rsi.REQUEST_SUPPLIER_ITEM_ID=rnd.REQUEST_SUPPLIER_ITEM_ID where ri.ITEM_ID is null and rnd.fulfilment_type_id = (select parameter_id from pm_parameter where parameter_type = 'FLT' and parameter_code = 'OF')) rnsi on (rnsi.PURCHASE_REQUEST_ID=pr.PURCHASE_REQUEST_ID) where pr.REQUEST_NOTE_ID=rn.REQUEST_NOTE_ID) || '</suppliers>' as supplier,
                    '  <approver>' || (SELECT LOGIN_ID from PM_USER where user_id=rn.APPROVER_ID) || '</approver>' || chr(10) || '    <approver>' || (SELECT listagg(u.LOGIN_ID, '</approver>' || chr(10) || '    <approver>') within group (order by u.USER_NAME desc) from PM_USER_GROUP_USER ugu inner join PM_USER_GROUP ug on ug.USER_GROUP_ID=ugu.USER_GROUP_ID inner join PM_USER u on u.USER_ID=ugu.USER_ID where ugu.USER_GROUP_ID=rn.USER_GROUP_ID and ug.ROLE_CODE='RN_APPROVER' and u.RECORD_STATUS=1 and ugu.RECORD_STATUS=1 and ug.ORG_PROFILE_ID=rn.ORG_PROFILE_ID and ugu.USER_ID <> rn.APPROVER_ID AND ROWNUM <= 20) || '</approver>' as approver,
                    (select listagg(rnsi.Login_Id) within group (order by rnsi.PURCHASE_REQUEST_ID) from SC_PURCHASE_REQUEST pr inner join (select DISTINCT rnd.PURCHASE_REQUEST_ID, assu.Login_Id from SC_REQUEST_NOTE_DTL rnd inner join SC_REQUEST_ITEM ri on (ri.REQUEST_NOTE_ID=rnd.REQUEST_NOTE_ID and ri.REQUEST_ITEM_ID=rnd.REQUEST_ITEM_ID) inner join Pm_User assu on assu.User_Id=Rnd.Assign_To where rnd.fulfilment_type_id <> (select parameter_id from pm_parameter where parameter_type = 'FLT' and parameter_code = 'OF')) rnsi on (rnsi.PURCHASE_REQUEST_ID=pr.PURCHASE_REQUEST_ID) where pr.REQUEST_NOTE_ID=rn.REQUEST_NOTE_ID) as do_list,
                    (select listagg(pr.PURCHASE_REQUEST_ID) within group (order by pr.PURCHASE_REQUEST_ID) from SC_PURCHASE_REQUEST pr where pr.REQUEST_NOTE_ID=rn.REQUEST_NOTE_ID and NOT EXISTS(select 1 from fl_fulfilment_request fr where fr.PURCHASE_REQUEST_ID=pr.PURCHASE_REQUEST_ID) and NOT EXISTS(select 1 from SC_REQUEST_NOTE_DTL rnd inner join SC_REQUEST_ITEM ri on (ri.REQUEST_NOTE_ID=rnd.REQUEST_NOTE_ID and ri.REQUEST_ITEM_ID=rnd.REQUEST_ITEM_ID) where rnd.PURCHASE_REQUEST_ID=pr.PURCHASE_REQUEST_ID and ri.ITEM_ID is null)) as pr_list,
                    rn.REQUEST_NOTE_NO as doc_no,
                    u.LOGIN_ID as task_performer

                    from SC_REQUEST_NOTE rn
                    inner join SC_ACTOR act on act.DOC_TYPE='RN' and act.DOC_ID=rn.REQUEST_NOTE_ID and act.ROLE_CODE='REQUISITIONER'
                    inner join PM_USER u on u.USER_ID=act.USER_ID
                    where rn.REQUEST_NOTE_NO IN (?)
                    order by u.LOGIN_ID,rn.REQUEST_NOTE_NO", array($docNo));

        return $query;
    }
    
    protected function getQtScEvaluation($docNo) {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT    '<qt_id>'|| q.qt_id || '</qt_id>'||chr(10)
       || CASE q.evaluation_type
             WHEN 1
                THEN    '<oc_secretary/>'||chr(10)||'<oc_chairperson/>'||chr(10)||'<oc_member/>'||chr(10)
                     || '<ec_secretary>'||(select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  
                     inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='C')
                     ||'</ec_secretary>'||chr(10)
                     ||'<ec_chairperson>'||(select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  
                     inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='S')
                     ||'</ec_chairperson>'||chr(10)
                     ||'<ec_member>'||(select listagg(u.LOGIN_ID, ',') within group (order by cmm.COMMITTEE_ID desc) 
                     from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID 
                     where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='M')
                     ||'</ec_member>'||chr(10)
             ELSE '<oc_secretary>'||(select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  
                     inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='C')
                     ||'</oc_secretary>'||chr(10)
                     ||'<oc_chairperson>'||(select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  
                     inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='S')
                     ||'</oc_chairperson>'||chr(10)
                     ||'<oc_member>'||(select listagg(u.LOGIN_ID, ',') within group (order by cmm.COMMITTEE_ID desc) 
                     from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID 
                     where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='M')
                     ||'</oc_member>'||chr(10)
                     ||'<ec_secretary/>'||chr(10)||'<ec_chairperson/>'||chr(10)||'<ec_member/>'||chr(10)
          END 
       || CASE
             WHEN tec_com.cnt > 0
                THEN    '<tec_secretary>'
                     || (SELECT u1.login_id
                     FROM sc_qt_committee qc1 
                     INNER JOIN sc_committee cm1 ON cm1.committee_id = qc1.committee_id
                     INNER JOIN pm_parameter pct1 ON pct1.parameter_id = cm1.committee_type_id
                     INNER JOIN sc_committee_member cmm1 ON cm1.committee_id = cmm1.committee_id
                     INNER JOIN pm_user u1 ON u1.user_id = cmm1.user_id
                     INNER JOIN pm_parameter pcu1 ON pcu1.parameter_id = cmm1.member_role_id
                     WHERE qc1.qt_id = q.qt_id AND pct1.parameter_code = 'TEC' AND pcu1.parameter_code = 'S')
                     || '</tec_secretary>'||CHR (10)
                     || '<tec_chairperson>'
                     || (SELECT u1.login_id
                     FROM sc_qt_committee qc1 
                     INNER JOIN sc_committee cm1 ON cm1.committee_id = qc1.committee_id
                     INNER JOIN pm_parameter pct1 ON pct1.parameter_id = cm1.committee_type_id
                     INNER JOIN sc_committee_member cmm1 ON cm1.committee_id = cmm1.committee_id
                     INNER JOIN pm_user u1 ON u1.user_id = cmm1.user_id
                     INNER JOIN pm_parameter pcu1 ON pcu1.parameter_id = cmm1.member_role_id
                     WHERE qc1.qt_id = q.qt_id AND pct1.parameter_code = 'TEC' AND pcu1.parameter_code = 'C')
                     || '</tec_chairperson>'||CHR (10)
                     || '<tec_member>'
                     || (SELECT listagg(u1.LOGIN_ID, ',') within group (order by cmm1.COMMITTEE_ID desc)
                     FROM sc_qt_committee qc1 
                     INNER JOIN sc_committee cm1 ON cm1.committee_id = qc1.committee_id
                     INNER JOIN pm_parameter pct1 ON pct1.parameter_id = cm1.committee_type_id
                     INNER JOIN sc_committee_member cmm1 ON cm1.committee_id = cmm1.committee_id
                     INNER JOIN pm_user u1 ON u1.user_id = cmm1.user_id
                     INNER JOIN pm_parameter pcu1 ON pcu1.parameter_id = cmm1.member_role_id
                     WHERE qc1.qt_id = q.qt_id AND pct1.parameter_code = 'TEC' AND pcu1.parameter_code = 'M')
                     || '</tec_member>'
             ELSE    '<tec_secretary/>'
               || CHR (10)
               || '<tec_chairperson/>'
               || CHR (10)
               || '<tec_member/>'
          END ||CHR (10)
        || CASE
            WHEN fec_com.cnt > 0
                THEN    '<fec_secretary>'
                  || (SELECT u1.login_id
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = 'FEC'
                         AND pcu1.parameter_code = 'S')
                  || '</fec_secretary>'||CHR (10)
                  || '<fec_chairperson>'||
                  (SELECT u1.login_id
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = 'FEC'
                         AND pcu1.parameter_code = 'C')
                  || '</fec_chairperson>'||CHR (10)
                  ||'<fec_member>'||
                  (SELECT listagg(u1.LOGIN_ID, ',') within group (order by cmm1.COMMITTEE_ID desc)
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = 'FEC'
                         AND pcu1.parameter_code = 'M')||
                  '</fec_member>'
          ELSE    '<fec_secretary/>'
               || CHR (10)
               || '<fec_chairperson/>'
               || CHR (10)
               || '<fec_member/>'
        END||chr(10) 
    ||'<higher_fc_secretary/>'||chr(10)
    ||'<fc_secretary/>'||chr(10)
    ||'<fc_chairperson/>'||chr(10)
    ||'<fc_member/>'||chr(10)
    ||'<fc_decision/>'||chr(10)
    ||'<new_committee/>'||chr(10)
    ||'<higher_committee_type/>'||chr(10)
    ||'<committee_type>'||pct.PARAMETER_CODE||'</committee_type>'||chr(10)
    ||'<evaluation_stages>'||q.evaluation_type||'</evaluation_stages>'||chr(10)
    ||'<title>' || q.QT_TITLE || '</title>' ||chr(10)
    ||'<committee_approver/>'||chr(10)
    ||'<groupkey_all/>'||chr(10)
    ||'<groupkey_da/>'||chr(10)
    ||'<groupkey_ma/>'||chr(10)
    ||'<groupkey_ba/>'||chr(10)
    ||'<letter_creator/>'||chr(10)
    ||'<bid_escalate/>'||chr(10)
    ||'<bid_url/>'||chr(10)
    ||'<bid_reaward/>'||chr(10)
    ||'<escalate_reason/>'||chr(10)
    ||'<bid_suppliers>0</bid_suppliers>'||chr(10)
    ||'<bid_hashcodes/>'||chr(10)
    ||'<reevaluate_committee/>'||chr(10)
    ||'<award_type/>'||chr(10)
    ||'<xflag_committee/>'||chr(10)
    ||'<isQuotation/>'||chr(10)
    ||'<pendingLOA/>'||chr(10)
    ||'<sc_chairperson/>'||chr(10)
    ||'<bpk_approvers/>'||chr(10)
    ||'</SC_Evaluation_Data>' as evaluation_payload,
       q.evaluation_type, cm.COMMITTEE_ID, pct.PARAMETER_CODE,
       q.qt_no as document_number,
       (SELECT usr.login_id
          FROM pm_user usr
         WHERE usr.user_id = q.created_by) as task_performer 
  FROM sc_qt q INNER JOIN sc_qt_committee qc ON qc.qt_id = q.qt_id
       INNER JOIN sc_committee cm ON cm.committee_id = qc.committee_id
       INNER JOIN pm_parameter pct ON pct.parameter_id = cm.committee_type_id
              LEFT JOIN
       (SELECT   qc1.qt_id, COUNT (*) cnt
            FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                 ON cm1.committee_id = qc1.committee_id
                 INNER JOIN pm_parameter pct1
                 ON pct1.parameter_id = cm1.committee_type_id
                 INNER JOIN sc_committee_member cmm1
                 ON cm1.committee_id = cmm1.committee_id
                 INNER JOIN pm_user u1 ON u1.user_id = cmm1.user_id
                 INNER JOIN pm_parameter pcu1
                 ON pcu1.parameter_id = cmm1.member_role_id
           WHERE pct1.parameter_code = 'TEC' AND cm1.status_id = 60202
        GROUP BY qc1.qt_id) tec_com ON tec_com.qt_id = q.qt_id
       LEFT JOIN
       (SELECT   qc2.qt_id, COUNT (*) cnt
            FROM sc_qt_committee qc2 INNER JOIN sc_committee cm2
                 ON cm2.committee_id = qc2.committee_id
                 INNER JOIN pm_parameter pct2
                 ON pct2.parameter_id = cm2.committee_type_id
                 INNER JOIN sc_committee_member cmm2
                 ON cm2.committee_id = cmm2.committee_id
                 INNER JOIN pm_user u2 ON u2.user_id = cmm2.user_id
                 INNER JOIN pm_parameter pcu2
                 ON pcu2.parameter_id = cmm2.member_role_id
           WHERE pct2.parameter_code = 'FEC' AND cm2.status_id = 60202
        GROUP BY qc2.qt_id) fec_com ON fec_com.qt_id = q.qt_id
 WHERE qt_no IN (?) 
 and pct.PARAMETER_CODE in ('EC','OC')", array($docNo));

        return $query;
    }
    
    protected function getQtQuotationTenderCreation($docNo) {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select 
  '<title>' || q.QT_TITLE || '</title>' ||chr(10)||
  '<qt_id>' || q.QT_ID || '</qt_id>' ||chr(10)||
  '<qt_type>' || (select PARAMETER_CODE from PM_PARAMETER where parameter_id=q.PROCUREMENT_MODE_ID) || '</qt_type>' ||chr(10)||
  '<biz_chair/>'||chr(10)||'<biz_owner/>'||chr(10)||'<msc/>' ||chr(10)||
  '<sc_chairperson>' || (select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='C') || '</sc_chairperson>' ||chr(10)||
  '<sc_secretary>' || (select u.LOGIN_ID from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='S') || '</sc_secretary>' ||chr(10)||
  '<sc_member>' || (select listagg(u.LOGIN_ID, ',') within group (order by cmm.COMMITTEE_ID desc) from SC_COMMITTEE_MEMBER cmm inner join PM_USER u on u.USER_ID=cmm.USER_ID  inner join PM_PARAMETER pcu on pcu.PARAMETER_ID=cmm.MEMBER_ROLE_ID where cmm.COMMITTEE_ID=cm.COMMITTEE_ID and pcu.PARAMETER_CODE='M') || '</sc_member>' ||chr(10)||
  '<budget_approver/>' ||chr(10)||
  '<committee_approver>' || (SELECT listagg(u.LOGIN_ID, ',') within group (order by au.USER_SEQ desc) from SC_QT_APPROVAL qa inner join SC_APPROVAL_USER au on au.DOC_TYPE='QTA' and au.DOC_ID=qa.QT_APPROVAL_ID inner join PM_USER u on u.USER_ID=au.USER_ID inner join PM_PARAMETER pac on pac.PARAMETER_ID=qa.APPROVAL_CATEGORY_ID where qa.QT_ID=q.QT_ID and pac.PARAMETER_CODE='CAA') || '</committee_approver>' ||chr(10)||
  '<bpk_approval/>'||chr(10)||'<bpk_revise/>'||chr(10)||'<pcb_secretary/>' ||chr(10)||
  '<evaluation_stage>' || q.EVALUATION_TYPE || '</evaluation_stage>' ||chr(10)||
  '<estimated_price/>' ||chr(10)||
  '<publication_approver>' || (SELECT listagg(u.LOGIN_ID, ',') within group (order by au.USER_SEQ desc) from SC_QT_APPROVAL qa inner join SC_APPROVAL_USER au on au.DOC_TYPE='QTA' and au.DOC_ID=qa.QT_APPROVAL_ID inner join PM_USER u on u.USER_ID=au.USER_ID inner join PM_PARAMETER pac on pac.PARAMETER_ID=qa.APPROVAL_CATEGORY_ID where qa.QT_ID=q.QT_ID and pac.PARAMETER_CODE='PTJ') || '</publication_approver>' ||chr(10)||
  '<publish_date>' || to_char(q.PUBLISH_DATE, 'YYYY-MM-DD') || 'T' || to_char(q.PUBLISH_DATE, 'HH24:MI') || ':00+08:00' || '</publish_date>' ||chr(10)||
  '<is_published>' || CASE WHEN EXISTS (SELECT 1 FROM SC_WORKFLOW_STATUS where DOC_TYPE='QT' and DOC_ID=q.QT_ID and STATUS_ID IN (60008, 60009)) THEN 'true' ELSE 'false' END || '</is_published>' ||chr(10)||
  '<site_visit>' || CASE WHEN q.IS_BSV_REQ=1 THEN 'true' ELSE 'false' END || '</site_visit>' ||chr(10)||
  '<doa>' || (SELECT listagg(u.LOGIN_ID, ',') within group (order by cp.QT_CONTACT_PERSON_ID desc) from SC_QT_CONTACT_PERSON cp inner join PM_USER u on u.USER_ID=cp.USER_ID where cp.QT_ID=q.QT_ID and OFFICER_TYPE='A') || '</doa>' ||chr(10)||
  '<pcb_approval/>'||chr(10)||'<circular/>' ||chr(10)||
  '<start_proposal_date>' || to_char(q.PROPOSAL_START_DATE, 'YYYY-MM-DD') || 'T' || to_char(q.PROPOSAL_START_DATE, 'HH24:MI') || ':00+08:00' || '</start_proposal_date>' ||chr(10)||
  '<close_proposal_date>' || to_char(q.CLOSING_DATE, 'YYYY-MM-DD') || 'T' || to_char(q.CLOSING_DATE, 'HH24:MI') || ':00+08:00' || '</close_proposal_date>' ||chr(10)||
  '<requote_scoring/>'||chr(10)||'<new_sc/>'||chr(10)||'<xflag_committee/>'||chr(10)||'<xflag_bpk/>'||chr(10)||'<xflag_publication/>'||chr(10)||'<xflag_finalization/>'||chr(10)||'<isQuotation/>'||chr(10)||'<supplier_users/>'||chr(10)||'<kpi_value/>'||chr(10)||'<request_id/>'||chr(10)||'<is_opentender/>'||chr(10)||'</SC_QuotationTender_Data>' as qt_tender,
  q.QT_NO,
  q.qt_no as document_number,
       (SELECT usr.login_id
          FROM pm_user usr
         WHERE usr.user_id = q.created_by) as task_performer,
  (select u.LOGIN_ID from SC_ACTOR act inner join PM_USER u on u.USER_ID=act.USER_ID where act.DOC_TYPE='QT' and act.DOC_ID=q.QT_ID and act.ROLE_CODE='DESK_OFFICER') As DESK_OFFICER 
from SC_QT q
inner join SC_QT_COMMITTEE qc on qc.QT_ID=q.QT_ID
inner join SC_COMMITTEE cm on cm.COMMITTEE_ID=qc.COMMITTEE_ID
inner join PM_PARAMETER pct on pct.PARAMETER_ID=cm.COMMITTEE_TYPE_ID
where q.QT_NO IN (?)
and pct.PARAMETER_CODE='SC'
order by DESK_OFFICER,q.QT_NO", array($docNo));

        return $query;
    }
    
    protected function getFactoringData($docNo) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->select("select cf.factoring_id, cc.contract_id, cc.contract_name, pu.user_id supplier_userid, pu.login_id supplier_loginid,
                            pu.user_name supplier_username, sl.loa_no, cf.financial_org_name, cf.remove_file_name, pmu.user_id ptj_userid,
                            pmu.login_id ptj_loginid, pmu.user_name ptj_username, cc.contract_no, cc.owner_org_profile_id, cf.financial_org_id,
                            cc.loa_physical_no, ccv.contract_physical_no 
                            from ct_contract cc, ct_factoring cf, sm_supplier ss,pm_user pu, sc_loa sl, pm_user pmu, ct_contract_ver ccv
                            where cc.contract_id = cf.contract_id
                            and cc.supplier_id = ss.supplier_id 
                            and cf.created_by = pu.user_id 
                            and cc.loa_id = sl.loa_id
                            and cc.created_by = pmu.user_id
                            and cc.latest_contract_ver_id = ccv.contract_ver_id
                            and cc.record_status = 1 and cf.record_status = 1
                            and ss.record_status = 1 and pu.record_status = 1
                            and sl.record_status = 1 and pmu.record_status = 1
                            and ccv.record_status = 1
                            and cc.contract_no = (?)", array($docNo));

        return $query;
    }
    
    protected function getFactoringUser($financialOrgId) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT  pm.user_id,pm.user_name, pm.identification_no, org.fin_org_name, des.role_code
                            FROM pm_user pm,
                                 pm_financial_org org,
                                 pm_user_role ROLE,
                                 pm_user_org uorg,
                                 pm_org_profile prof,
                                 pm_role_desc des
                           WHERE pm.user_id = uorg.user_id
                             AND ROLE.user_org_id = uorg.user_org_id
                             AND prof.factoring_org_id = org.financial_org_id
                             AND prof.org_profile_id = uorg.org_profile_id
                             AND des.role_code = ROLE.role_code
                             AND uorg.record_status = 1
                             AND org.record_status = 1
                             AND des.language_code = 'en'
                             AND pm.org_type_id = 7
                             and org.FINANCIAL_ORG_ID = (?)", array($financialOrgId));

        return $query;
    }
    
    protected function getAgreementData($docNo){
        $query = DB::connection('oracle_nextgen_rpt')
                ->select("select TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,a.CONTRACT_ID,
                            cc.contract_no, cc.contract_name, b.doc_type, cc.loa_physical_no, 
                            a.AGREEMENT_ID, b.doc_id, cc.loa_id, sl.loa_no, cc.owner_org_profile_id,
                            d.status_name ,c.STATUS_ID,b.is_current , a.CREATED_BY , a.CHANGED_BY
                            from ct_contract cc,ct_agreement a, ct_workflow_status b, pm_status c, pm_status_desc d, sc_loa sl
                            where a.AGREEMENT_ID = b.doc_id
                            and b.status_id = c.status_id    
                            and c.status_id = d.status_id
                            and cc.contract_id = a.contract_id
                            and sl.loa_id = cc.loa_id
                            and d.language_code ='en'
                            and b.doc_type in ('AC','SA') 
                            and cc.contract_no = ?
                            order by  a.contract_id, b.CREATED_DATE desc", array($docNo));

        return $query;
    }
    
    public function getRoleForCtAgreementPayload($roleCode, $docNo){
        $query = DB::connection('oracle_nextgen_rpt')
                ->select("select cc.contract_id, cc.contract_no, cws.changed_by, cws.doc_id, cws.doc_type,
                ps.status_id,psd.status_name, a.user_name, a.login_id, a.user_id, c.role_code
                from CT_CONTRACT cc, CT_CONTRACT_VER ccv, CT_WORKFLOW_STATUS cws, 
                PM_STATUS ps, PM_STATUS_DESC psd, pm_user a, pm_user_org b, pm_user_role c
                where cc.contract_id = ccv.contract_id 
                and cws.doc_id = ccv.contract_ver_id
                and ps.status_id = cws.status_id 
                and psd.status_id = ps.status_id
                and cws.changed_by = a.user_id
                and a.user_id = b.user_id
                and c.user_org_id = b.user_org_id
                and a.record_status = 1
                and b.record_status = 1
                and c.record_status= 1
                and psd.language_code = 'en'
                and cc.record_status = 1
                and ps.doc_type IN ('FA','FD','FW')
                and c.role_code = ?
                and cc.contract_no = ?", array($roleCode,$docNo));

        return $query;
    }

    public function getRoleAgreementACSA($roleCode, $docNo){
           
            $query = "SELECT u.user_id,u.login_id , u.user_name,'$roleCode' as role_name,
                ur.role_code as role_code,
                (SELECT ur.ROLE_CODE  FROM pm_user u1, pm_user_org uo, pm_user_role ur 
                WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                AND ur.ROLE_CODE = 'MIN_CONTRACT_ADMIN' 
                AND u1.RECORD_STATUS  = 1
                AND uo.RECORD_STATUS  = 1
                AND ur.RECORD_STATUS  = 1 
                AND u1.USER_ID = u.USER_ID) AS role_name_active,
                (SELECT u1.login_id   FROM pm_user u1, pm_user_org uo, pm_user_role ur , PM_LOGIN_HISTORY lh
                WHERE u1.USER_ID = uo.USER_ID AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                AND u1.USER_ID = lh.USER_ID 
                AND ur.ROLE_CODE = '$roleCode' 
                AND u1.RECORD_STATUS  = 1
                AND uo.RECORD_STATUS  = 1
                AND ur.RECORD_STATUS  = 1    
                AND uo.org_profile_id = cc.owner_org_profile_id 
                AND rownum < 2) AS pick_random_active_ctadmin,
            TO_CHAR (b.CREATED_DATE,'DD/MM/YYYY HH12:MI:SS') fws_date_created,a.contract_id,
            cc.contract_no, b.doc_type, 
            a.AGREEMENT_ID, b.doc_id, cc.loa_id, sl.loa_no, cc.owner_org_profile_id,
            d.status_name ,c.status_id,b.is_current , b.created_by , b.changed_by
                from ct_contract cc,ct_agreement a, ct_workflow_status b, pm_status c, pm_status_desc d, sc_loa sl, pm_user u, pm_user_org uo, pm_user_role ur 
                where a.AGREEMENT_ID = b.doc_id
                and b.status_id = c.status_id    
                and c.status_id = d.status_id
                and cc.contract_id = a.contract_id
                and sl.loa_id = cc.loa_id 
                and u.user_id = uo.user_id
                and uo.user_org_id = ur.user_org_id(+)
                AND b.CREATED_BY = u.user_id
                and d.language_code ='en'
                -- AND b.STATUS_ID in (57000,56000)
                and b.doc_type in  ('AC','SA')
                and cc.contract_no = '$docNo'
                and ur.role_code = '$roleCode'
            ORDER BY  a.contract_id, b.CREATED_DATE DESC";
            
            $res = DB::connection('oracle_nextgen_rpt')->select($query);
    return $res;
    }
    
    protected function getQtSuppFinalization($docNo) {
        $link = "http://xmlns.oracle.com/bpm/bpmobject/Data/SC_Evaluation_Data";
        $query = DB::connection('oracle_nextgen_rpt')
                ->select
("SELECT  '<qt_id>'|| q.qt_id || '</qt_id>'||chr(10)
    ||'<oc_secretary/>'||chr(10)
    ||'<oc_chairperson/>'||chr(10)
    ||'<oc_member/>'||chr(10)
    ||'<ec_secretary/>'||chr(10)
    ||'<ec_chairperson/>'||chr(10)
    ||'<ec_member/>'||chr(10)
    ||'<tec_secretary/>'||chr(10)
    ||'<tec_chairperson/>'||chr(10)
    ||'<tec_member/>'||chr(10) 
    ||'<fec_secretary/>'||chr(10)
    ||'<fec_chairperson/>'||chr(10)
    ||'<fec_member/>' ||chr(10)
    ||'<higher_fc_secretary/>'||chr(10)
    ||'<fc_secretary>'
    || (SELECT u1.login_id
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = pct.PARAMETER_CODE
                         AND pcu1.parameter_code = 'S')||
    '</fc_secretary>'||chr(10)
    ||'<fc_chairperson>'||
                      (SELECT u1.login_id
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = pct.PARAMETER_CODE
                         AND pcu1.parameter_code = 'C')
    ||'</fc_chairperson>'||chr(10)
    ||'<fc_member>'||
                  (SELECT listagg(u1.LOGIN_ID, ',') within group (order by cmm1.COMMITTEE_ID desc)
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = pct.PARAMETER_CODE
                         AND pcu1.parameter_code = 'M')
    ||'</fc_member>'||chr(10)||
      case qtf.DECISION_ID when null then
        '<fc_decision/>'
      else
        '<fc_decision>'|| (select parameter_code from pm_parameter where parameter_id= qtf.DECISION_ID) ||'</fc_decision>' 
      end
    ||chr(10)
    ||'<new_committee/>'||chr(10)
    ||'<higher_committee_type/>'||chr(10)
    ||'<committee_type>'||pct.PARAMETER_CODE||'</committee_type>'||chr(10)
    ||'<evaluation_stages>'||q.evaluation_type||'</evaluation_stages>'||chr(10)
    ||'<title>' || q.QT_TITLE || '</title>' ||chr(10)
    ||'<committee_approver/>'||chr(10)||
    case qtf.FINALIZATION_METHOD_ID when 368 then
        --'<groupkey_da/>'||chr(10) ||      
        (select listagg('<groupkey_ma>'||qtrd.QT_RECOMMEND_DTL_ID||'</groupkey_ma>',chr(10)) within group (order by qtrd.QT_RECOMMEND_DTL_ID)  
        from sc_qt_recommend qtr, sc_qt_recommend_dtl qtrd
        where qtr.QT_FINALIZATION_ID =qtf.QT_FINALIZATION_ID and qtr.IS_CANCELLED=0 and qtrd.RECORD_STATUS=1 and  qtr.QT_RECOMMEND_ID=qtrd.QT_RECOMMEND_ID)||chr(10)
        --||'<groupkey_ba/>'||chr(10)
    when 367 then
        --'<groupkey_da/>'||chr(10)
        --||'<groupkey_ma/>'||chr(10)||
        (select listagg('<groupkey_ba>'||qtrd.QT_RECOMMEND_DTL_ID||'</groupkey_ba>',chr(10)) within group (order by qtrd.QT_RECOMMEND_DTL_ID)  
        from sc_qt_recommend qtr, sc_qt_recommend_dtl qtrd
        where qtr.QT_FINALIZATION_ID =qtf.QT_FINALIZATION_ID and qtr.IS_CANCELLED=0 and qtrd.RECORD_STATUS=1 and  qtr.QT_RECOMMEND_ID=qtrd.QT_RECOMMEND_ID)||chr(10)
    else
        (select listagg('<groupkey_da>'||qtrd.QT_RECOMMEND_DTL_ID||'</groupkey_da>',chr(10)) within group (order by qtrd.QT_RECOMMEND_DTL_ID)  
        from sc_qt_recommend qtr, sc_qt_recommend_dtl qtrd
        where qtr.QT_FINALIZATION_ID =qtf.QT_FINALIZATION_ID and qtr.IS_CANCELLED=0 and qtrd.RECORD_STATUS=1 and  qtr.QT_RECOMMEND_ID=qtrd.QT_RECOMMEND_ID)||chr(10)
        --||'<groupkey_ma/>'||chr(10)
       --||'<groupkey_ba/>'||chr(10) 
    end 
    ||
    case qtf.LOI_LOA_BY when null then
        '<letter_creator/>'||chr(10)
    else        
        '<letter_creator>'||qtf.LOI_LOA_BY ||'</letter_creator>'||chr(10)
    end
    ||'<bid_escalate/>'||chr(10)
    ||'<bid_url/>'||chr(10)
    ||'<bid_reaward/>'||chr(10)
    ||'<escalate_reason/>'||chr(10)
    ||'<bid_suppliers/>'||chr(10)
    ||'<bid_hashcodes/>'||chr(10)
    ||'<reevaluate_committee/>'||chr(10)||
    case qtf.FINALIZATION_METHOD_ID when null then
        '<award_type/>'||chr(10)
    else
        '<award_type>'
        ||(select listagg(code_name,' / ') within group (order by LANGUAGE_CODE desc)  from pm_parameter_desc b where PARAMETER_ID=364)
        ||'</award_type>'||chr(10)
    end
    ||'<xflag_committee/>'||chr(10)
    ||'<isQuotation/>'||chr(10)
    ||'<pendingLOA/>'||chr(10)
    ||'<sc_chairperson/>'||chr(10)
    ||'<bpk_approvers/>'||chr(10)
    ||'</SC_Evaluation_Data>' finalization_payload,
       q.evaluation_type, cm.COMMITTEE_ID, pct.PARAMETER_CODE, q.QT_ID,qt_no,
	   (SELECT u1.login_id
                        FROM sc_qt_committee qc1 INNER JOIN sc_committee cm1
                             ON cm1.committee_id = qc1.committee_id
                             INNER JOIN pm_parameter pct1
                             ON pct1.parameter_id = cm1.committee_type_id
                             INNER JOIN sc_committee_member cmm1
                             ON cm1.committee_id = cmm1.committee_id
                             INNER JOIN pm_user u1 ON u1.user_id =
                                                                  cmm1.user_id
                             INNER JOIN pm_parameter pcu1
                             ON pcu1.parameter_id = cmm1.member_role_id
                       WHERE qc1.qt_id = q.qt_id
                         AND pct1.parameter_code = pct.PARAMETER_CODE
                         AND pcu1.parameter_code = 'S') as task_performer
  FROM sc_qt q 
       join sc_qt_finalization qtf on q.QT_ID=qtf.QT_ID
       INNER JOIN sc_qt_committee qc ON qc.qt_id = q.qt_id
       INNER JOIN sc_committee cm ON cm.committee_id = qc.committee_id
       INNER JOIN pm_parameter pct ON pct.parameter_id = cm.committee_type_id
 WHERE qt_no IN (?) 
 and pct.PARAMETER_CODE not in ('SC','EC','OC','TEC','FEC')", array($docNo));

        return $query;
    }

}
