@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/it_support/network/amtek/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/network/amtek/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/network/amtek/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> DATA LOOKUP </strong></h1>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-sm btn-primary add_new_lookup"><i class="fa fa-save"></i> Add New
                Lookup</button>
        </div>
        <form class="form-horizontal form-bordered insert_lookup_form" id="insert_lookup_form" style="display:none"
            action="{{ url('/it_support/network/amtek/add_data_lookup/create') }}" method="post">
            {{ csrf_field() }}
            <div class="form-group">
                <input type="hidden" id="task_id" name="task_id" value="" class="form-control"
                    style="width: 100px;">
                    <label class="col-md-1 text-right" for="name">Sequence<span class="text-danger">*</span></label>
                    <div class="col-md-1">
                        <input type="text" id="seq_id" name="seq_id" value="" required class="form-control">
                    </div>
                <label class="col-md-1 text-right" for="name">Name<span class="text-danger">*</span></label>
                <div class="col-md-3">
                    <input type="text" id="task_name" name="task_name" value="" required class="form-control">
                </div>
                <label class="col-md-1 text-right" for="name">Group<span class="text-danger">*</span></label>
                <div class="col-md-1">
                    <input type="text" id="task_group" name="task_group" value="" required class="form-control">
                </div>
                <label class="col-md-1 text-right" for="name">SubGroup</label>
                <div class="col-md-1">
                    <input type="text" id="task_subgroup" name="task_subgroup" value="" class="form-control">
                </div>
                <label class="col-md-1 text-right" for="status">Status<span class="text-danger">*</span></label>
                <div class="col-md-1">
                    <select id="task_status" name = "task_status" class="form-control" style="width: 700px;">
                        <option value="active">Active</option>
                        <option value="inactive">In Active</option>
                    </select>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="pull-right">
                    <button type="submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i>
                        Save</button>
                    <button type="reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i
                            class="fa fa-repeat"></i> Reset</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table id="datalookup_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Sequence Id</th>
                        <th class="text-center">Group</th>
                        <th class="text-center">SubGroup</th>
                        <th class="text-center">Created By</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed By</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Task Status</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($getLookupdate != null)
                        @foreach ($getLookupdate as $rowData => $data)
                            <tr>
                                <td class="text-center">{{ ++$rowData }}</td>
                                <td class="text-center">{{ $data->am_data_name }}</td>
                                <td class="text-center">{{ $data->am_seq_id }}</td>
                                <td class="text-center">{{ $data->am_data_group }}</td>
                                <td class="text-center">{{ $data->am_data_subgroup }}</td>
                                <td class="text-center">{{ $data->am_created_by }}</td>
                                <td class="text-center">{{ $data->am_created_date }}</td>
                                <td class="text-center">{{ $data->am_changed_by }}</td>
                                <td class="text-center">{{ $data->am_changed_date }}</td>
                                <td class="text-center">{{ $data->am_data_status }}</td>
                                <td class="text-center" action_table_task>
                                    <div class="btn-group btn-group-xs">
                                        <a idno="{{ $data->am_data_id }}" seqId="{{ $data->am_seq_id }}" taskName="{{ $data->am_data_name }}"
                                            taskGroup="{{ $data->am_data_group }}"
                                            tasksubGroup="{{ $data->am_data_subgroup }}"
                                            statusName="{{ $data->am_data_status }}" data-toggle="tooltip"
                                            title="Edit" class="btn btn-default editbutton"><i
                                                class="fa fa-edit"></i></a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </div>
    </DIV>

@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
        $(document).ready(function() {
            $('#datalookup_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
        });
    </script>
    <script>
        $(function() {
            $('#to-top').click();
        });


        $(".add_new_lookup").on("click", function() {
            $("#insert_lookup_form").show();
            resetFormFields();
        });

        $(".resetbutton").on("click", resetFormFields);

        function resetFormFields() {
            $("#task_id, #seq_id, #task_name, #task_group, #task_subgroup").val("");
        }

        $(".editbutton").on("click", function() {
            $('#to-top').click();
            $("#insert_lookup_form").show();
            let idno = $(this).attr('idno');
            let seqId = $(this).attr('seqId');
            let taskName = $(this).attr('taskName');
            let taskGroup = $(this).attr('taskGroup');
            let tasksubGroup = $(this).attr('tasksubGroup');
            let statusName = $(this).attr('statusName');

            $("#task_id").val(idno);
            $("#seq_id").val(seqId);
            $("#task_name").val(taskName);
            $("#task_group").val(taskGroup);
            $("#task_subgroup").val(tasksubGroup);
            $("#status_name").val(statusName);
        });
    </script>
@endsection
