<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use SSH;
use DB;
use App\EpSupportActionLog;

class HandlePaymentClosedAP511Schedule extends Command {
    
    use OSBService;
    use FulfilmentService;
    use SupplierService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandlePaymentClosedAP511';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Decrypt file contents for service GFM-140 AP511 to put temporary table';

    
    protected $failed_decrypt = 'Decrypt data is failed!';

    protected $AP511_CODE = 'GFM-140';
    
    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        $dtStartTime = Carbon::now();
        
        /*Manual Date Pick*/
        //$dateStart = Carbon::parse('2018-10-24')->format('Y-m-d');
        //$dateEnd = Carbon::parse('2018-10-24')->format('Y-m-d');
        

        /*Do retrigger decrypt failed 1st*/
        $this->reTriggerDecryptFailed();
        
        /*Get latest created_date from ep_osb_batch_file*/
        $listDataMax = collect(DB::connection('mysql_ep_support')->select("SELECT MAX(DATE(created_date)) as latest_created_date FROM ep_osb_batch_file"));
        $dateStartFromLatestDB = $listDataMax->first()->latest_created_date;
     
        $dateStart = Carbon::parse($dateStartFromLatestDB)->format('Y-m-d');
        $dateEnd = Carbon::now()->format('Y-m-d');
        
        MigrateUtils::logDump('$dateStart :: '.$dateStart);
        MigrateUtils::logDump('$dateEnd :: '.$dateEnd);
        
        while (strtotime($dateStart) <= strtotime($dateEnd)) {
            MigrateUtils::logDump('Start query get File AP511 by date: '.$dateStart);
           
            MigrateUtils::logDump('Start in '.self::class .'Date : '.$dateStart);
            try {
                $list = $this->getListFilesBatchFileByDate($this->AP511_CODE,$dateStart);
                $listData = collect($list);

                $counter=0;
                foreach ($listData as $objFile){
                    $count = DB::connection('mysql_ep_support')->table('ep_osb_batch_file')
                            ->where('batch_file_id',$objFile->batch_file_id)
                            ->count();
                    if($count == 0){
                        $content = $this->wsDecryptFileContentIGFMAS($objFile->file_name);
                        DB::connection('mysql_ep_support')
                        ->insert('insert into ep_osb_batch_file 
                            (batch_file_id,trans_id,service_code,file_name,created_date,file_data) 
                            values (?, ?, ?, ?, ?, ?)', 
                            [   
                                $objFile->batch_file_id, 
                                $objFile->trans_id,
                                $objFile->service_code,
                                $objFile->file_name,
                                $objFile->created_date,
                                $content
                                ]);
                        MigrateUtils::logDump('  inserted : '.$objFile->file_name);

                        if($content !== $this->failed_decrypt) {
                            self::extractFileData($objFile, $content);
                        }
                    }else{
                        MigrateUtils::logDump('Is existed! : '.$objFile->file_name);
                    }
                    $counter++;
                    if($counter == 5){
                        sleep(1);
                        $counter = 0;
                    }
                }

            } catch (\Exception $exc) {

                \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
                $err = $exc->getTrace();
                \Log::error(self::class . '>> error happen!! ' . json_encode($err));
                \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
                $this->sendErrorEmail(json_encode($err));
                echo $exc->getTraceAsString();
            }
           $dateStart = date ("Y-m-d", strtotime("+1 day", strtotime($dateStart)));
        }
        
        $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , '
                        . 'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
        MigrateUtils::logDump($logsdata);      
        
    }

    protected function extractFileData($file, $file_content) {
        if($this->checkIfInvoiceExist($file->batch_file_id,$file->file_name) > 0){
            Log::info('This filename: '.$file->file_name.' , Already exist in table: ep_invoice_detail');
            dump('This filename: '.$file->file_name.' , Already exist in table: ep_invoice_detail');
            return;
        }
        
        $lineCount = substr_count($file_content, "\n");
        $count = 0;
        $ptjCode = "";
        $sapVendorCode = "";
        $bankName = "";
        $supplierName = "";
        $gfmasPaymentId = "";
        $paymentRefNo = "";
        $totalPaymentAmt = "";
        $vendorIdCode = "";
        $entityCode = "";
        $paymentNo = "";
        $cancellationDate = "";
        $paymentAdviceNo = "";
        $paymentAdviceDate = "";
        $paymentDate = "";

        dump('[' . $file->file_name . '] File content line count: ' . $lineCount );

        /* SPLIT DATA HERE */
        foreach (preg_split("/((\r?\n)|(\r\n?))/", $file_content) as $line) {

            if ($count % 2 == 0) { // 1st line data
                $ptjCode = substr($line, 314, 8);
                $sapVendorCode = ltrim(substr($line, 115, 10));
                $bankName = rtrim(substr($line, 51, 40));
                $supplierName = rtrim(substr($line, 133, 100));
                $totalPaymentAmt = ltrim(substr($line, 276, 13), '0');
                $paymentRefNo = trim(substr($line, 33, 18));
                $gfmasPaymentId = trim(substr($line, 20, 13));
                $vendorIdCode = trim(substr($line, 91, 20));
                $entityCode = trim(substr($line, 111, 4));
                $paymentNo = trim(substr($line, 254, 10));
                $cancellationDate = trim(substr($line, 264, 8));
                $paymentAdviceNo = trim(substr($line, 289, 17));
                $paymentAdviceDate = trim(substr($line, 306, 8));
                $paymentDate = trim(substr($line, 11, 8));

                if ($cancellationDate == "********" || strlen($cancellationDate) < 8) {
                    $cancellationDate = null;
                } else {
                    $cancellationDate = Carbon::createFromFormat('dmY', $cancellationDate);
                }

                if ($paymentAdviceDate && strlen($paymentAdviceDate) == 8) {
                    $paymentAdviceDate = Carbon::createFromFormat('dmY', $paymentAdviceDate);
                } else {
                    $paymentAdviceDate = null;
                }

                if ($paymentDate && strlen($paymentDate) == 8) {
                    $paymentDate = Carbon::createFromFormat('dmY', $paymentDate);
                } else {
                    $paymentDate = null;
                }

            } else { //2nd line data
                $invoiceNo = rtrim(substr($line, 9, 17));
                $paymentAmt = ltrim(substr($line, 26, 13), '0');
                $invoiceDate = trim(substr($line, 1, 8));

                if ($invoiceDate && strlen($invoiceDate) == 8) {
                    $invoiceDate = Carbon::createFromFormat('dmY', $invoiceDate);
                } else {
                    $invoiceDate = null;
                }

                $dataset = array(
                    [
                        'batch_file_id' => $file->batch_file_id,
                        'file_name' => $file->file_name,
                        'invoice_no' => $invoiceNo,
                        'payment_amount' => $paymentAmt,
                        'total_payment_amount' => $totalPaymentAmt,
                        'sap_vendor_code' => $sapVendorCode,
                        'supplier_name' => $supplierName,
                        'ptj_code' => $ptjCode,
                        'bank_name' => $bankName,
                        '1gfmas_payment_id' => $gfmasPaymentId,
                        'payment_reference_no' => $paymentRefNo,
                        'vendor_id_code' => $vendorIdCode,
                        'entity_code' => $entityCode,
                        'payment_no' => $paymentNo,
                        'cancelation_date' => $cancellationDate,
                        'payment_advice_no' => $paymentAdviceNo,
                        'payment_advice_date' => $paymentAdviceDate,
                        'invoice_date' => $invoiceDate,
                        'payment_date' => $paymentDate,
                        'created_date' => $file->created_date,
                    ]
                );

                //Log::info("-------------------- INSERT -------------------- > invoice_no: ".$dataset[0]['invoice_no']);
                // Log::info(\GuzzleHttp\json_encode($dataset));

                /* INSERTING */
                DB::connection('mysql_ep_support')->table('ep_invoice_detail')->insert($dataset);
                //Log::info("-------------------- SUCCESS --------------------");
                //Log::info("");
                //Log::info("");
            }
            $count++;
        }
        Log::info(self::class . ' > Total success inserted : '.$count);
    }
    
    protected function reTriggerDecryptFailed(){
        MigrateUtils::logDump(self::class.' >> '.__FUNCTION__.' >>> entering..');
        $list = DB::connection('mysql_ep_support')->table('ep_osb_batch_file')
                    ->where('service_code',$this->AP511_CODE) 
                    ->where('file_data',$this->failed_decrypt)
                    ->get();
        MigrateUtils::logDump(self::class.' >> '.__FUNCTION__.' >>> found total decrypt failed :- '.$list->count());
        foreach($list as $objFile){
            MigrateUtils::logDump(self::class.' >> '.__FUNCTION__.' >>> try decrypt filename: '.$objFile->file_name);
            $content = $this->wsDecryptFileContentIGFMAS($objFile->file_name);
            if($content != null && $content !== $this->failed_decrypt) {
                DB::connection('mysql_ep_support')
                    ->table('ep_osb_batch_file')    
                    ->where('batch_file_id',$objFile->batch_file_id)     
                    ->update([
                        'file_data' => $content
                    ]);   
                self::extractFileData($objFile, $content);
            }
        }
    }
    
    protected  function checkIfInvoiceExist($batchFileId,$fileName)
    {
        $total = DB::connection('mysql_ep_support')->table('ep_invoice_detail')
            ->where('batch_file_id', '=', $batchFileId)    
            ->where('file_name', '=', $fileName)
            ->count();

        return $total;
    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleGFM140ErrorSchedule (Decrypt file contents for service GFM-140 AP511 to put temporary table)'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
