<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use Illuminate\Http\Request;
use Carbon\Carbon;

class OsbController extends Controller
{

    use OSBService;

    public function getDashboardOsb()
    {
        return view('dashboard.osb', []);
    }

    public function igfmasIntegrationMonitoring()
    {
        $results = $this->getListStatisticIGFMASIntegration();

        if (empty($results)) {
            return "<p>No logs available.</p>";
        }

        $html = "
        <div style='overflow-x:auto; max-height: 500px; overflow-y: auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Code</th>
                        <th>Service Name</th>
                        <th>Status Description</th>
                        <th>Start Date</th>
                        <th>Latest Date</th>
                        <th>Total Request</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($results as $data) {
            $html .= "
            <tr>
                <td class='text-center'>{$data->service_code}</td>
                <td class='text-center'>{$data->service_name}</td>
                <td class='text-center'>{$data->status_desc}</td>
                <td class='text-center'><strong>{$data->start_date}</strong></td>
                <td class='text-center'><strong>{$data->latest_date}</strong></td>
                <td class='text-center'>
                    <button class='btn btn-sm btn-primary total-req-button' data-toggle='modal' data-target='#modal-igfmas-integration' data-statusDesc='{$data->status_desc}'>
                        <strong>{$data->total_req}</strong>
                    </button>
                </td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function igfmasIntegrationMonitoringListDetails(Request $request)
    {
        $statusDesc = $request->get('statusDesc');

        $results = $this->getListStatisticIGFMASIntegrationDetails($statusDesc);
        if (empty($results)) {
            return "<p>No logs available.</p>";
        }

        $html = "
    <div style='overflow-x:auto; max-height: 500px; overflow-y: auto;'>
        <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Transaction ID</th>
                        <th>Service Code</th>
                        <th>Remarks 1</th>
                        <th>Remarks 2</th>
                        <th>Remarks 3</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($results as $data) {
            $html .= "
        <tr>
            <td class='text-center'><strong>{$data->trans_date}</strong></td>
            <td class='text-center'><a href='/find/osb/detail/log?cari={$data->trans_id}' target='_blank'><strong>{$data->trans_id}</strong></a></td>            <td class='text-center'><strong>{$data->service_code}</strong></td>
            <td class='text-center'><strong>{$data->remarks_1}</strong></td>
            <td class='text-center'><strong>{$data->remarks_2}</strong></td>
            <td class='text-center'><strong>{$data->remarks_3}</strong></td>
        </tr>";
        }

        $html .= "
            </tbody>
        </table>
    </div>";

        return $html;
    }

    public function monitorErrorResponseOSB()
    {
        $result = $this->getListErrorEmptyResponseOSB();

        // return $result;

        $html = "";

        $html .= "
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Request Date</th>
                        <th>Service Code</th>
                        <th>Service Name</th>
                        <th>Start Date</th>
                        <th>Last Date</th>
                        <th>Total Request</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->req_date</strong></td>
                <td class='text-center'>$data->service_code</td>
                <td class='text-center'>$data->service_name</td>
                <td class='text-center'><strong>$data->start_date</strong></td>
                <td class='text-center'><strong>$data->last_date</strong></td>
                <td>
                    <strong>
                        <a href='#modal-list-data' 
                        class='modal-list-data-action label label-danger' 
                        data-toggle='modal' data-url='/dashboard/displayRecordsOSBError?code=$data->service_code'
                        data-title='$data->service_name' >
                        {$data->total_request}
                        </a>
                    </strong>
                </td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function displayRecordsOSBError()
    {
        $queries = array();
        parse_str($_SERVER['QUERY_STRING'], $queries);
        $code = $queries['code'];
        $result = $this->getListRecordsOSBErrorEmptyResponse($code);

        //Render the data
        $html = "";

        $html .= "
                <thead>
                    <tr>
                        <th class='text-center'>Trans ID</th>
                        <th class='text-center'>Trans Type</th>
                        <th class='text-center'>Service Code</th>
                        <th class='text-center'>Trans Date</th>
                        <th class='text-center'>Status Code</th>
                        <th class='text-center'>Status</th>
                        <th class='text-center'>Status Description</th>
                        <th class='text-center'>Remarks 1</th>
                        <th class='text-center'>Remarks 2</th>
                        <th class='text-center'>Remarks 3</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr>
                <td class='text-center' style='width: 30%;'><strong><a href='/find/osb/detail/log?cari=$data->trans_id'>$data->trans_id</a></strong></td>
                <td class='text-center'>$data->trans_type</td>
                <td class='text-center'>$data->service_code</td>
                <td class='text-center'><strong>$data->trans_date</strong></td>
                <td class='text-center'><strong>$data->status_code</strong></td>
                <td class='text-center'><strong>$data->status</strong></td>
                <td class='text-center'><strong>$data->status_desc</strong></td>
                <td class='text-center'><strong>$data->remarks_1</strong></td>
                <td class='text-center'><strong>$data->remarks_2</strong></td>
                <td class='text-center'><strong>$data->remarks_3</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function checkFileErrorInbound()
    {
        $list = $this->getListStatisticFileErrProcessing();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Trans Date</th>
                        <th>Service Code</th>
                        <th>Service Name</th>
                        <th>Total Files</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if ($data->total > 0) {
                $data->total = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/batch/ep-file-error-inbound/$data->service_code/$data->trans_date'
                            data-title='List Pending Files (OUT 1GFMAS FOLDER)' >{$data->total}</a>";
            }
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->service_code</td>
                <td class='text-center'>$data->service_name</td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function checkMonitoringErrorCheckChargingReceived()
    {
        $collect = collect();
        $res1 = $this->getStatisticGFMASIntegrationErrorLogToday("EJB Callback Error", "70004", "EPP-013", "Check Charging");
        if ($res1 != null) {
            $collect->push($res1);
        }

        $res2 = $this->getStatisticGFMASIntegrationErrorLogToday("EJB Callback Error", "70004", "EPP-017", "PMQ");
        if ($res2 != null) {
            $collect->push($res2);
        }


        $html = "";
        $html .= "
        <div class='block'>
            <table class='table table-borderless table-striped table-vcenter '>
                <thead>
                    <tr>
                        <th>Error</th>
                        <th>Service</th>
                        <th>Start Datetime</th>
                        <th>Last Datetime</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($collect as $data) {
            if ($data->total > 0) {
                $data->total = "<span  
                                    class='modal-list-data-action label label-danger' 
                                     >{$data->total}</span>";
            }
            $html .= "
                    <tr>
                        <td>$data->error</td>
                        <td>$data->service_code | $data->service_name</td>    
                        <td>$data->start_date</td>
                        <td>$data->last_date</td>    
                        <td><strong>$data->total</strong></td>      
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function checkMonitoringSSMIntegration()
    {
        //        $collect = collect();
        //        $res1 = $this->getStatisticSSMErrorLogToday(70005);
        //        if ($res1 != null) {
        //            $collect->push($res1);
        //        }
        //
        //        $res2 = $this->getStatisticSSMErrorLogToday(70006);
        //        if ($res2 != null) {
        //            $collect->push($res2);
        //        }

        $res1 = $this->getStatisticSSMErrorLog();
        //        if ($res1 != null) {
        //            $collect->push($res1);
        //        }
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Min Date</th>
                        <th>Max Date</th>
                        <th>Status Code</th>
                        <th>Status Desc</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($res1 as $data) {
            if ($data->total > 0) {
                $labelColor = ($data->status_code == '70060') ? 'label-success' : 'label-danger';
                $data->total = "<span class='modal-list-data-action label " . $labelColor . "'>{$data->total}</span>";
            }
            $html .= "
                    <tr>
                        <td>$data->min_datetime</td>
                        <td>$data->max_datetime</td>
                        <td>$data->status_code</td>
                        <td>$data->status_desc</td>    
                        <td><strong>$data->total</strong></td>      
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkMonitoringJPNIntegration()
    {

        $res1 = $this->getStatisticMyIdentityLogToday();

        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Start Date</th>
                        <th>Latest Date</th>
                        <th>Status Code</th>
                        <th>Status Desc</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($res1 as $data) {
            if ($data->total > 0) {
                $label = ($data->status_code == '70018') ? 'label-success' : 'label-danger';
                $data->total = "<span class='modal-list-data-action label " . $label . "'>{$data->total}</span>";
            }
            $html .= "
                    <tr>
                        <td>$data->min_trans_date</td>
                        <td>$data->max_trans_date</td>
                        <td>$data->status_code</td>
                        <td>$data->status_desc</td>    
                        <td><strong>$data->total</strong></td>      
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function getItemCodeCheckingIgfmas()
    {

        $queries = array();
        if (isset($_SERVER['QUERY_STRING'])) {
            parse_str($_SERVER['QUERY_STRING'], $queries);
        }

        if (isset($queries['selectedDate'])) {
            $today = Carbon::createFromFormat("Y-m-d", $queries['selectedDate'])->format("d/m/Y");
        } else {
            $today = Carbon::today()->format("d/m/Y");
        }
        $itemCodeList = $this->getListItemCodeIGFMASIntegration($today, $today);

        $datePickerHtml = "
    <div class='row'>
        <div class='col-md-4 col-md-offset-8'>
            <div class='input-group date' data-provide='datepicker'>
                <input type='text' id='created_date' name='created_date' class='form-control input-datepicker' value='$today'>
                <div class='input-group-addon'>
                    <span class='gi gi-calendar'></span>
                </div>
            </div>
        </div>
    </div>";

        // HTML for the table
        $tableHtml = "
    <div>
        <table id='item_code_datatable' class='table table-borderless table-striped table-vcenter'>
            <thead>
                <tr>
                    <th>Trans Date</th>
                    <th>Trans Id</th>
                    <th>Status</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>";

        if (is_array($itemCodeList)) {
            foreach ($itemCodeList as $data) {
                $trans_date = $data->transaction_date;
                $trans_id = $data->trans_id;
                $status_desc = $data->status_desc;
                $total = $data->total;
                $tableHtml .= "
            <tr>
                <td style='width: 10%;'><strong>$trans_date</strong></td>  
                <td><a href='/find/osb/detail/log/?cari=$trans_id' target='_blank'>$trans_id</a></td> 
                <td><strong>$status_desc</strong></td>  
                <td><strong>$total</strong></td>  
            </tr>";
            }
        } else {
            // Handle the case where $itemCodeList is not iterable
            $tableHtml .= "<tr><td colspan='4'>No data available</td></tr>";
        }

        $tableHtml .= "
            </tbody>
        </table>
    </div>";

        // Return both HTML parts
        return response()->json(['datePicker' => $datePickerHtml, 'table' => $tableHtml]);
    }
}
