<?php

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SSHService;
use App\Services\Traits\PendingTransactionService;
use App\Services\Traits\EpWebService;
use App\Services\Traits\EpBlastEmailService;
use App\Migrate\CheckUserActivity;
use Carbon\Carbon;
use SSH;
use stdClass;
use Exception;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use DOMDocument;
use Log;
use Excel;
use Auth;

class EpController extends Controller
{

    use SupplierService;
    use OSBService;
    use ProfileService;
    use PendingTransactionService;
    use FulfilmentService;
    use SSHService;
    use EpWebService;
    use EpBlastEmailService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function getUserActivityInfo()
    {
        $loginId = request()->loginId;
        if ($loginId && strlen($loginId) > 1) {
            return CheckUserActivity::checkByLoginId($loginId);
        }
        return 'No data';
    }

    public function getIdentity()
    {

        $icNo = request()->ic_no;
        if (strlen($icNo) == 0) {
            return view('user.identity', [
                'carian' => $icNo,
                'result' => array(),
                'status' => 'success'
            ]);
        }

        //$data = $this->getIdentityInfoJPN($icNo);
        $data = $this->getMyIdentityInfo($icNo);

        return view('user.identity', [
            'carian' => $icNo,
            'result' => $data['result'],
            'status' => $data['status'],
            'statusDesc' => $data['statusDesc']
        ]);
    }

    public function detailsUserByEpNo()
    {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'epno',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function getDetailsUserByEpNo($epno)
    {
        $epNo = trim($epno);
        $result = $this->searchFilterSupplier($epNo, "EPNO");
        if ($result["status"] == true) {
            return redirect($result["redirect"]);
        }
        $checkEpNo = substr($epno, 0, 3);
        if ($checkEpNo != 'eP-') {
            // This carian come from Company Name o SSM No.
            $resultEpNo = $this->getEpNoSmSupplier($epno);
            if ($resultEpNo != null) {
                $epNo = $resultEpNo;
            } else {
                $resultEpNo2 = $this->getEpNoSmSupplierByApplNo($epno);
                if ($resultEpNo2 != null) {
                    $epNo = $resultEpNo2;
                }
            }
        }
        $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
        return $this->getDetailsUsersSupplier($list, $epNo, 'epno');
    }

    public function detailsUserByMof()
    {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mofno',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function getDetailsUserByMof($mofno)
    {

        $mofNo = trim($mofno);
        $result = $this->searchFilterSupplier($mofNo, "MOFNO");
        if ($result["status"] == true) {
            return redirect($result["redirect"]);
        }

        $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo($mofNo, null);
        return $this->getDetailsUsersSupplier($list, $mofNo, 'mofno');
    }

    public function detailsUserBySapVendorCode()
    {
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'sap',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function getDetailsUserBySapVendorCode($sapVendorCode)
    {
        $epNo = $this->getEpNoBySapVendorCode($sapVendorCode);
        if ($epNo && strlen($epNo) > 0) {
            $list = $this->getSMSupplierUsersDetailsByMofNOorEpNo(null, $epNo);
            return $this->getDetailsUsersSupplier($list, $sapVendorCode, 'sap');
        }
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'sap',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function listUom()
    {
        return view('list_uom', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'uom',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function getListUom($uom)
    {
        $uomTemp = strtolower($uom);
        $list = $this->getListUomByUomName($uomTemp);
        if (count($list) > 0) {
            return view('list_uom', [
                'listdata' => $list,
                'carian' => $uom
            ]);
        }
        //return "MAAF! CARIAN TIDAK DIJUMPAI.";
        return view('list_uom', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'uom',
            'result' => 'notfound',
            'carian' => $uom
        ]);
    }

    public function historyApplId($suppid)
{
    $Appldetails = $this->getHistoryApplId($suppid);
    $isSMTesting = Auth::user()->isSMTesting(); 

    $html = "<div class='table-responsive'>
                <table id='summary-appl-history-datatable' class='table table-bordered table-vcenter'>
                    <thead>
                        <tr>
                            <th class='text-center'>Appl Id</th>
                            <th class='text-center'>Appl Type</th>
                            <th class='text-center'>Appl Number</th>
                            <th class='text-center'>Appl Submit Date</th>
                            <th class='text-center'>Appl Changed Date</th>
                            <th class='text-center'>Status Name</th>
                            <th class='text-center'>Supporting Documents</th>
                            <th class='text-center'>Appl Category</th>
                            <th class='text-center'>Appl Is Resubmit</th>
                            <th class='text-center'>Appl Original</th>
                            <th class='text-center'>Appl Valid SSM</th>";
    if ($isSMTesting) {
        $html .= "<th class='text-center'>Paid Up</th>";
    }

    $html .= "</tr></thead><tbody>";

    foreach ($Appldetails as $value) {
        $html .= "<tr>
                    <td class='text-center'><a href='/find/appl-id/$value->appl_id' target='_blank'><strong>$value->appl_id</strong></a></td> 
                    <td class='text-center'><strong>$value->appl_type</strong></td>
                    <td class='text-center'><strong>$value->appl_no</strong></td>
                    <td class='text-center'><strong>$value->appl_submit_date</strong></td>
                    <td class='text-center'><strong>$value->changed_date</strong></td>
                    <td class='text-center'><strong>$value->status_name</strong></td>
                    <td class='text-center'><strong>$value->supporting_doc_mode</strong></td>
                    <td class='text-center'><strong>$value->appl_category</strong></td>
                    <td class='text-center'><strong>$value->is_resubmit</strong></td>
                    <td class='text-center'><strong>$value->original_appl_id</strong></td>
                    <td class='text-center'><strong>$value->is_appl_valid_with_ssm</strong></td>";
        if ($isSMTesting) {
            $html .= "<td class='text-center'><strong>$value->paid_up_capital</strong></td>";
        }

        $html .= "</tr>";
    }

    $html .= "</tbody></table></div>"; 
    return $html;
}

    public function getHistoryAppl($applId)
    {
        $supplier = $this->getSupplierIdFromApplId($applId);
        //dd($supplier);
        $isData = app()->make('stdClass');
        if ($supplier == null) {
            dd('Invalid. No Supplier found by appl ID!');
        }
        $listHistoryWorkFlow = $this->getWorkFlowSupplierProcess($supplier->supplier_id, $supplier->appl_id);
        $mofHistorySupplierDetail = $this->getHistorySupplierDetailFromApplId($supplier->supplier_id, $supplier->appl_id);
        $suppHistoryMofStatus = $this->getHistorySupplierMofStatus($supplier->appl_id, $supplier->supplier_id);
        $basicCompInfo = $this->getBasicSupplierInfo($supplier->appl_id);
        $hqHistoryGstInfo = $this->getHqGstInfo($supplier->supplier_id);
        $totalItems = $this->getTotalItemsSupplier($supplier->supplier_id);
        $sapVendorCode = $this->getMainSapVendorCode($supplier->ep_no);
        $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($supplier->supplier_id, $supplier->appl_id);
        $listSuppPayment = $this->getPaymentSuppliers($supplier->supplier_id);
        $listSuppMofVirtCert = $this->getSupplierMofVirtCertByApplId($supplier->appl_id);
        $listSupplierBank = $this->getListSupplierBank($supplier->appl_id);
        $listSupplierBranch = $this->getListSupplierBranch($supplier->appl_id);
        $listRemarksCancelReject = $this->getListRemarksRejectOrCancel($supplier->appl_id);
        $listAttachmentCancelReject = $this->getListAttachmentRejectOrCancel($supplier->appl_id);
        $listApplRejectReason = $this->getListApplRejectReason($supplier->appl_id);
        $listApplSectionReview = $this->getListApplSectionReview($supplier->appl_id);
        $checkNonAscii = false;
        $checkBranchNameNonAscii = false;
        if (count($listSupplierBranch) > 0) {
            foreach ($listSupplierBranch as $branch) {

                $branchNameNonAscii = false;
                if (mb_check_encoding($branch->branch_name, 'ASCII') == false) {
                    $branchNameNonAscii = true;
                    $checkBranchNameNonAscii = true; //SetParent
                }
                $branch->is_branch_name_non_ascii = $branchNameNonAscii;

                $nonAsciiDetected = false;
                if (mb_check_encoding($branch->address_1, 'ASCII') == false) {
                    $nonAsciiDetected = true;
                }
                if (mb_check_encoding($branch->address_2, 'ASCII') == false) {
                    $nonAsciiDetected = true;
                }
                if (mb_check_encoding($branch->address_3, 'ASCII') == false) {
                    $nonAsciiDetected = true;
                }
                $branch->is_non_ascii = $nonAsciiDetected;
                if ($branch->is_non_ascii == true) {
                    $checkNonAscii = true;
                }

                $branch->sap_vendor_code = '-';
                $branchSapVendorCode = $this->getMainSapVendorCodeByBranchCode($mofHistorySupplierDetail[0]->ep_no, $branch->branch_code);
                if ($branchSapVendorCode) {
                    $branch->sap_vendor_code = $branchSapVendorCode->sap_vendor_code;
                }
            }
        }
        $isData->is_address_branch_non_ascii = $checkNonAscii;
        $isData->is_branch_name_non_ascii = $checkBranchNameNonAscii;
        if ($basicCompInfo && $basicCompInfo != null) {
            $basicCompInfo->is_with_federal = EPService::$YES_NO[$basicCompInfo->is_with_federal];
            $basicCompInfo->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
            $basicCompInfo->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
            $basicCompInfo->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
            $basicCompInfo->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];

            $isData->is_name_hq_non_ascii = false;
            if (mb_check_encoding($basicCompInfo->company_name, 'ASCII') == false) {
                $isData->is_name_hq_non_ascii = true;
            }


            $nonAsciiHqDetected = false;
            if (mb_check_encoding($basicCompInfo->address_1, 'ASCII') == false) {
                $nonAsciiHqDetected = true;
            }
            if (mb_check_encoding($basicCompInfo->address_2, 'ASCII') == false) {
                $nonAsciiHqDetected = true;
            }
            if (mb_check_encoding($basicCompInfo->address_3, 'ASCII') == false) {
                $nonAsciiHqDetected = true;
            }

            $isData->is_address_hq_non_ascii = $nonAsciiHqDetected;
        }
        $listSupplierCategoryCode = $this->getListSupplierCategoryCode($supplier->appl_id);
        if (count($listSupplierCategoryCode) > 0) {
            foreach ($listSupplierCategoryCode as $cat) {
                $cat->is_approved_by_po = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_po];
                $cat->is_approved_by_ap = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_ap];
                $cat->record_status = EPService::$RECORD_STATUS[$cat->record_status];
            }
        }

        return view('supplier_history_appl_id', [
            'listHistoryWorkFlow' => $listHistoryWorkFlow,
            'mofHistorySupplierDetail' => $mofHistorySupplierDetail,
            'suppHistoryMofStatus' => $suppHistoryMofStatus,
            'isData' => $isData,
            'totalItems' => $totalItems,
            'hqHistoryGstInfo' => $hqHistoryGstInfo,
            'basicCompInfo' => $basicCompInfo,
            'sapVendorCode' => $sapVendorCode,
            'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
            'listSuppPayment' => $listSuppPayment,
            'listSuppMofVirtCert' => $listSuppMofVirtCert,
            'listSupplierBranch' => $listSupplierBranch,
            'listSupplierBank' => $listSupplierBank,
            'listSupplierCategoryCode' => $listSupplierCategoryCode,
            'listRemarksCancelReject' => $listRemarksCancelReject,
            'listAttachmentCancelReject' => $listAttachmentCancelReject,
            'listApplRejectReason' => $listApplRejectReason,
            'listApplSectionReview' => $listApplSectionReview,
        ]);
    }

    public function getDetailsUsersSupplier($list, $carian, $type)
    {
        $isNotAppointedAdmin = true;
        if (count($list) > 0) {

            $isData = app()->make('stdClass');

            /** Repopulate Data to bind others data * */
            foreach ($list as $data) {

                //populate put to $data
                $this->populatePersonnelUserData($data);

                if (strlen($data->p_ep_role) > 0) {
                    $isNotAppointedAdmin = false;
                }
            }

            $latestApplObj = $this->getSmApplDetail($list[0]->latest_appl_id);
            /* Checking is MOF Expired Sync With Latest APPL */
            $isData->is_mof_error_activate = false;
            if ($latestApplObj && $latestApplObj->status_id == '20199' && ($latestApplObj->appl_type == 'R' || $latestApplObj->appl_type == 'N')) {
                $listCheckSupplierNotActivateMOF = $this->checkSupplierMofExpiredNotActivate($list[0]->supplier_id);
                if (count($listCheckSupplierNotActivateMOF) > 0) {
                    $isData->is_mof_error_activate = true;
                }
            }
            $supplierDisciplinearyAction = $this->getDetailSupplierDisciplinaryAction($list[0]->supplier_id);
            $listWorkFlow = $this->getWorkFlowSupplierProcess($list[0]->supplier_id, $list[0]->latest_appl_id);
            $listSuppTrackDiary = $this->getTrackingDiarySupplierByDocNo($list[0]->appl_no);
            $listAttachmentCancelReject = $this->getListAttachmentRejectOrCancel($list[0]->latest_appl_id);
            $listRemarksCancelReject = $this->getListRemarksRejectOrCancel($list[0]->latest_appl_id);
            $totalItems = $this->getTotalItemsSupplier($list[0]->supplier_id);
            $sapVendorCode = $this->getMainSapVendorCode($list[0]->ep_no);
            $suppMofStatus = $this->getSupplierMofStatus($list[0]->supplier_id);
            $listSuppMofVirtCert = $this->getSupplierMofVirtCert($list[0]->supplier_id);
            $listSuppPayment = $this->getPaymentSuppliers($list[0]->supplier_id);
            $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($list[0]->supplier_id);
            $listInProgressSuppTrackDiary = null;
            $facilityInfoInProgress = null;
            if (count($listinProgressSuppProcessAppl) > 0) {
                $listInProgressSuppTrackDiary = $this->getTrackingDiarySupplierByDocNo($listinProgressSuppProcessAppl[0]->appl_no);
                $facilityInfoInProgress = $this->getFacilityInfo($listinProgressSuppProcessAppl[0]->appl_id);
            }
            $basicCompInfo = $this->getBasicSupplierInfo($data->latest_appl_id);
            $listApplRejectReason = $this->getListApplRejectReason($list[0]->latest_appl_id);
            $listApplSectionReview = $this->getListApplSectionReview($list[0]->latest_appl_id);
            $facilityInfo = $this->getFacilityInfo($list[0]->latest_appl_id);
            if ($basicCompInfo && $basicCompInfo != null) {
                $basicCompInfo->is_with_federal = EPService::$YES_NO[$basicCompInfo->is_with_federal];
                $basicCompInfo->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
                $basicCompInfo->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
                $basicCompInfo->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
                $basicCompInfo->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];


                $isData->is_name_hq_non_ascii = false;
                if (mb_check_encoding($basicCompInfo->company_name, 'ASCII') == false) {
                    $isData->is_name_hq_non_ascii = true;
                }


                $nonAsciiHqDetected = false;
                if (mb_check_encoding($basicCompInfo->address_1, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }
                if (mb_check_encoding($basicCompInfo->address_2, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }
                if (mb_check_encoding($basicCompInfo->address_3, 'ASCII') == false) {
                    $nonAsciiHqDetected = true;
                }

                $isData->is_address_hq_non_ascii = $nonAsciiHqDetected;
            }

            /** Check MOF expiry * */
            $list[0]->is_mof_expired = false;
            if ($list[0]->ma_exp_date) {
                $mofExpDate = Carbon::parse($list[0]->ma_exp_date);
                $now = Carbon::now();

                $list[0]->is_mof_expired = $mofExpDate < $now;
            }

            $listSupplierBranch = $this->getListSupplierBranch($list[0]->latest_appl_id);
            $checkNonAscii = false;
            $checkBranchNameNonAscii = false;
            if (count($listSupplierBranch) > 0) {
                foreach ($listSupplierBranch as $branch) {

                    $branchNameNonAscii = false;
                    if (mb_check_encoding($branch->branch_name, 'ASCII') == false) {
                        $branchNameNonAscii = true;
                        $checkBranchNameNonAscii = true; //SetParent
                    }
                    $branch->is_branch_name_non_ascii = $branchNameNonAscii;

                    $nonAsciiDetected = false;
                    if (mb_check_encoding($branch->address_1, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    if (mb_check_encoding($branch->address_2, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    if (mb_check_encoding($branch->address_3, 'ASCII') == false) {
                        $nonAsciiDetected = true;
                    }
                    $branch->is_non_ascii = $nonAsciiDetected;
                    if ($branch->is_non_ascii == true) {
                        $checkNonAscii = true;
                    }

                    $branch->sap_vendor_code = '-';
                    $branchSapVendorCode = $this->getMainSapVendorCodeByBranchCode($list[0]->ep_no, $branch->branch_code);
                    if ($branchSapVendorCode) {
                        $branch->sap_vendor_code = $branchSapVendorCode->sap_vendor_code;
                    }
                }
            }
            $isData->is_address_branch_non_ascii = $checkNonAscii;
            $isData->is_branch_name_non_ascii = $checkBranchNameNonAscii;

            $listSupplierBank = $this->getListSupplierBank($list[0]->latest_appl_id);
            $hqGstInfo = $this->getHqGstInfo($data->supplier_id);
            $listSupplierCategoryCode = $this->getListSupplierCategoryCode($data->latest_appl_id);
            if (count($listSupplierCategoryCode) > 0) {
                foreach ($listSupplierCategoryCode as $cat) {
                    $cat->is_approved_by_po = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_po];
                    $cat->is_approved_by_ap = EPService::$CATEGORY_IS_APPROVED[$cat->is_approved_by_ap];
                    $cat->record_status = EPService::$RECORD_STATUS[$cat->record_status];
                }
            }

            $listPendingTransaction = array();
            // $listPendingTransaction = $this->getListPendingTransaction($list[0]->supplier_id);
            $isPendingTransaction = false;

            /** Condition to show flag danger * */
            foreach ($listPendingTransaction as $data) {
                // dump($data);
                if ($data->get('total') > 0) {
                    $isPendingTransaction = true;
                    break;
                }
            }

            //cast to Object from array
            //$isData = (object)$isDataCheck;

            return view('user_details_mof', [
                'listdata' => $list,
                'isNotAppointedAdmin' => $isNotAppointedAdmin,
                'totalItems' => $totalItems,
                'sapVendorCode' => $sapVendorCode,
                'basicCompInfo' => $basicCompInfo,
                'suppMofStatus' => $suppMofStatus,
                'listSuppTrackDiary' => $listSuppTrackDiary,
                'listSuppPayment' => $listSuppPayment,
                'listSuppMofVirtCert' => $listSuppMofVirtCert,
                'listSupplierBranch' => $listSupplierBranch,
                'listSupplierBank' => $listSupplierBank,
                'listSupplierCategoryCode' => $listSupplierCategoryCode,
                'listPendingTransaction' => $listPendingTransaction,
                'isPendingTransaction' => $isPendingTransaction,
                'hqGstInfo' => $hqGstInfo,
                'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                'listInProgressSuppTrackDiary' => $listInProgressSuppTrackDiary,
                'listWorkFlow' => $listWorkFlow,
                'listAttachmentCancelReject' => $listAttachmentCancelReject,
                'listRemarksCancelReject' => $listRemarksCancelReject,
                'listApplRejectReason' => $listApplRejectReason,
                'listApplSectionReview' => $listApplSectionReview,
                'isData' => $isData,
                'carian' => $carian,
                'type' => $type,
                'facilityInfo' => $facilityInfo,
                'facilityInfoInProgress' => $facilityInfoInProgress,
                'supplierDisciplinearyAction' => $supplierDisciplinearyAction
            ]);
        }
        //return "MAAF! CARIAN TIDAK DIJUMPAI.";
        return view('user_details_mof', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'mofno',
            'result' => 'notfound',
            'carian' => $carian
        ]);
    }

    public function getDetailsPendingTransaction($supplierID)
    {

        $xml = $this->getListPendingTransaction($supplierID);

        $isPendingTransaction = false;

        /** Condition to show flag danger * */
        foreach ($xml as $data) {
            if ($data->get('total') > 0) {
                $isPendingTransaction = true;
                break;
            }
        }

        // dump($xml);
        return view('include.pending_transaction_detail', [
            'xml' => $xml,
            'isPendingTransaction' => $isPendingTransaction
        ]);
    }

    public function getListTransactionSupplier($supplierId, $year)
    {

        $listData = $this->getListTransactionPRCRBySupplierId($supplierId, $year);

        return view('include.list_transactions_supplier', [
            'listTransSupplier' => $listData,
        ]);
    }

    public function findDetailsSupplierItems($supplierID)
    {

        $list = $this->getItemsSupplier($supplierID);

        return view('include.list_items_supplier', [
            'listSupplierItems' => $list
        ]);
    }

    public function getLatestSuccessSigning($userId, $type)
    {
        $data = $this->getLastSigningSPKIorGPKI($userId, $type);

        return view('include.signing_success', [
            'dataSign' => $data
        ]);
    }

    public function getSoftcertLogDetail($softReqID, $serviceCode)
    {
        //Digicert : SPK-020  (NEW & RENEW)
        //Trustgate : SPK-050 (NEW) , SPK-060 (RENEW)
        //UpdateSoftcert SPK-010
        $dateLatest = null;
        $xml = $this->getDetailCertSPKI($softReqID, "SPK-020");
        if ($xml) {
            $dateLatest = Carbon::parse($xml->trans_date);
        }

        $xml2 = $this->getDetailCertSPKI($softReqID, "SPK-050");
        if ($xml2) {
            if (Carbon::parse(Carbon::parse($xml2->trans_date))->gt($dateLatest) > 0) {
                $dateLatest = Carbon::parse($xml2->trans_date);
                $xml = $xml2;
            }
        }

        $xml3 = $this->getDetailCertSPKI($softReqID, "SPK-060");
        if ($xml3) {
            if (Carbon::parse(Carbon::parse($xml3->trans_date))->gt($dateLatest) > 0) {
                $dateLatest = Carbon::parse($xml3->trans_date);
                $xml = $xml3;
            }
        }

        $xml4 = $this->getDetailCertSPKI($softReqID, "SPK-010");
        if ($xml4) {
            if (Carbon::parse(Carbon::parse($xml4->trans_date))->gt($dateLatest) > 0) {
                $dateLatest = Carbon::parse($xml4->trans_date);
                $xml = $xml4;
            }
        }

        return view('include.osb_logging_detail', [
            'xml' => $xml
        ]);
    }

    public function detailsUser()
    {
        return view('user_details', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'icno',
            'result' => null,
            'carian' => ''
        ]);
    }

    public function getDetailsUser($icNo)
    {
        $icno = trim($icNo);

        $result = $this->searchFilterSupplier($icno, "ICNO");
        if ($result["status"] == true) {
            return redirect($result["redirect"]);
        }

        $list = $this->getSMSupplierUsersActiveByICNO($icno);

        if (count($list) > 0) {
            foreach ($list as $data) {
                //populate put to $data
                $this->populatePersonnelUserData($data);


                $data->is_business_network = false;

                $listWorkFlow = $this->getWorkFlowSupplierProcess($data->supplier_id, $data->latest_appl_id);
                $totalItems = $this->getTotalItemsSupplier($data->supplier_id);
                $sapVendorCode = $this->getMainSapVendorCode($data->ep_no);
                $suppMofStatus = $this->getSupplierMofStatus($data->supplier_id);
                $listSuppMofVirtCert = $this->getSupplierMofVirtCert($data->supplier_id);
                $listSuppPayment = $this->getPaymentSuppliers($data->supplier_id);
                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcess($data->supplier_id);
                $basicCompInfo = $this->getBasicSupplierInfo($data->latest_appl_id);
                if ($basicCompInfo && $basicCompInfo != null) {
                    $data->is_business_network = true;
                    $data->company_basic_id = $basicCompInfo->company_basic_id;
                    $data->is_with_federal = EPService::$YES_NO[$basicCompInfo->is_with_federal];
                    $data->is_with_state = EPService::$YES_NO[$basicCompInfo->is_with_state];
                    $data->is_with_statutory = EPService::$YES_NO[$basicCompInfo->is_with_statutory];
                    $data->is_with_glc = EPService::$YES_NO[$basicCompInfo->is_with_glc];
                    $data->is_with_others = EPService::$YES_NO[$basicCompInfo->is_with_others];
                }

                $listSupplierBranch = $this->getListSupplierBranch($data->latest_appl_id);
                $listSupplierBank = $this->getListSupplierBank($data->latest_appl_id);
                $hqGstInfo = $this->getHqGstInfo($data->supplier_id);
            }
            return view(
                'user_details',
                [
                    'listdata' => $list,
                    'totalItems' => $totalItems,
                    'sapVendorCode' => $sapVendorCode,
                    'suppMofStatus' => $suppMofStatus,
                    'listSuppPayment' => $listSuppPayment,
                    'listSuppMofVirtCert' => $listSuppMofVirtCert,
                    'listSupplierBranch' => $listSupplierBranch,
                    'listSupplierBank' => $listSupplierBank,
                    'hqGstInfo' => $hqGstInfo,
                    'listinProgressSuppProcessAppl' => $listinProgressSuppProcessAppl,
                    'listWorkFlow' => $listWorkFlow,
                    'carian' => $icno
                ]
            );
        }
        return view('user_details', [
            'listdata' => null,
            'listXml' => null,
            'type' => 'icno',
            'result' => 'notfound',
            'carian' => $icno
        ]);
    }

    protected function searchSupplier($search)
    {
        $checkMof = substr($search, 0, 4);
        $checkEpNo = substr($search, 0, 2);
        if ($checkMof == '357-' || $checkMof == '465-') {
            return redirect('find/mofno/' . $search);
        } else if ($checkEpNo == 'eP-') {
            return redirect('find/epno/' . $search);
        } else {
            return redirect('find/icno/' . $search);
        }
    }

    /**
     * Switch Carian - Easy Seacrh on UI. Auto Switch
     * @param type $search
     * @param type $type
     * @return boolean
     */
    protected function searchFilterSupplier($search, $type)
    {
        $result = array('status' => false); //to set no auto switch. 

        $checkMof = substr($search, 0, 4);
        $checkEpNo = substr($search, 0, 3);
        $checkIcno = strlen(trim($search));

        if ($type == 'ICNO') {
            if ($checkMof == '357-' || $checkMof == '465-') {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/mofno/' . $search
                );
            } else if ($checkEpNo == 'eP-') {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/epno/' . $search
                );
            } else if ($checkIcno == 12) {
                $result = array('status' => false);
            } else {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/epno/' . $search
                );
            }
        }
        if ($type == 'MOFNO') {
            if ($checkMof == '357-' || $checkMof == '465-') {
                $result = array('status' => false);
            } else if ($checkEpNo == 'eP-') {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/epno/' . $search
                );
            } else if ($checkIcno == 12) {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/icno/' . $search
                );
            } else {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/epno/' . $search
                );
            }
        }
        if ($type == 'EPNO') {
            if ($checkMof == '357-' || $checkMof == '465-') {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/mofno/' . $search
                );
            } else if ($checkEpNo == 'eP-') {
                $result = array('status' => false);
            } else if ($checkIcno == 12) {
                $result = array(
                    'status' => true,
                    'redirect' => 'find/icno/' . $search
                );
            } else {
                $result = array('status' => false);
            }
        }

        return $result;
    }

    /**
     * Return result by Company Name or SSM No.
     * @return type
     */
    public function getDetailsUserByName()
    {
        $nama_pembekal = request()->nama_pembekal;
        //dd($nama_pembekal);

        $detailPembekal = null;

        if ($nama_pembekal == null) {

            return view('search_by_name', [
                'listdata' => null,
                'carianPembekal' => $nama_pembekal,
                'result' => ''
            ]);
        } else {
            if (strlen($nama_pembekal) < 4) {
                return view('search_by_name', [
                    'listdata' => null,
                    'carianPembekal' => $nama_pembekal,
                    'result' => 'Minimum carian adalah 4 aksara.'
                ]);
            } else {
                $detailPembekal = $this->getSupplierByCompanyName($nama_pembekal);
                if (count($detailPembekal) == 0) {
                    $detailPembekal = $this->getSupplierByRegistrationNo($nama_pembekal);
                }

                return view('search_by_name', [
                    'listdata' => $detailPembekal,
                    'carianPembekal' => $nama_pembekal,
                    'result' => ''
                ]);
            }
        }
    }

    //Akmal add checking for delete mof
    public function CheckingDeleteMOF()
    {
        $NosppID = request()->supp_id;
        $detailSM = null;
        $check001 = null;
        $check002 = null;
        $check003 = null;
        $check004 = null;
        $check0042 = null;
        $check005 = null;
        $check0052 = null;
        $check006 = null;
        $check007 = null;
        $check008 = null;
        $check009 = null;
        $infosupplier = null;
        $result001 = null;
        $result002 = null;
        $result003 = null;
        $result006 = null;
        $result007 = null;
        $result008 = null;
        $result009 = null;
        $result004 = null;
        $result0042 = null;
        $result005 = null;
        $result0052 = null;
        $check001z = null;
        $check002z = null;
        $check003z = null;
        $check004z = null;
        $check0042z = null;
        $check005z = null;
        $check0052z = null;
        $check006z = null;
        $check007z = null;
        $check008z = null;
        $check009z = null;
        $infosupplierz = null;
        $result001z = null;
        $result002z = null;
        $result003z = null;
        $result006z = null;
        $result007z = null;
        $result008z = null;
        $result009z = null;
        $result004z = null;
        $result0042z = null;
        $result005z = null;
        $result0052z = null;

        if ($NosppID && $NosppID != null) {
            $infosupplier = $this->getSupplierInfoDEL($NosppID);
            //get supplier id
            if ($infosupplier) {
                $getSupplierID = $infosupplier[0]->supplier_id;
                $getSupplierID2 = $getSupplierID;
            } else {
                $infosupplier2 = $this->getSupplierInfobySupplierID($NosppID);
                $getSupplierID = $NosppID;
                $getSupplierID2 = $NosppID;
                $infosupplier = $infosupplier2;
            }
            $check001 = $this->check001($getSupplierID);
            $check002 = $this->check002($getSupplierID, $getSupplierID2);
            $check003 = $this->check003($getSupplierID);
            $check006 = $this->check006($getSupplierID);
            $check007 = $this->check007($getSupplierID);
            $check008 = $this->check008($getSupplierID);
            $check009 = $this->check009($getSupplierID);
            $check004 = $this->check004_1($getSupplierID, $getSupplierID2);
            $check0042 = $this->check004_2($getSupplierID, $getSupplierID2);
            $check005 = $this->check005_1($getSupplierID);
            $check0052 = $this->check005_2($getSupplierID);

            $check001z = $this->check001z($getSupplierID);
            $check002z = $this->check002z($getSupplierID, $getSupplierID2);
            $check003z = $this->check003z($getSupplierID);
            $check006z = $this->check006z($getSupplierID);
            $check007z = $this->check007z($getSupplierID);
            $check008z = $this->check008z($getSupplierID);
            $check009z = $this->check009z($getSupplierID);
            $check004z = $this->check004_1z($getSupplierID, $getSupplierID2);
            $check0042z = $this->check004_2z($getSupplierID, $getSupplierID2);
            $check005z = $this->check005_1z($getSupplierID);
            $check0052z = $this->check005_2z($getSupplierID);

            if (count($infosupplier) > 0) {
                $detailSM = $infosupplier[0];
            }
            if (count($check001) > 0) {
                $result001 = $check001[0];
            }
            if (count($check002) > 0) {
                $result002 = $check002[0];
            }
            if (count($check003) > 0) {
                $result003 = $check003[0];
            }
            if (count($check006) > 0) {
                $result006 = $check006[0];
            }
            if (count($check007) > 0) {
                $result007 = $check007[0];
            }
            if (count($check008) > 0) {
                $result008 = $check008[0];
            }
            if (count($check009) > 0) {
                $result009 = $check009[0];
            }
            if (count($check004) > 0) {
                $result004 = $check004[0];
            }
            if (count($check0042) > 0) {
                $result0042 = $check0042[0];
            }
            if (count($check005) > 0) {
                $result005 = $check005[0];
            }
            if (count($check0052) > 0) {
                $result0052 = $check0052[0];
            }
            //            test
            if (count($check001z) > 0) {
                $result001z = $check001z;
            }
            if (count($check002z) > 0) {
                $result002z = $check002z;
            }
            if (count($check003z) > 0) {
                $result003z = $check003z;
            }
            if (count($check006z) > 0) {
                $result006z = $check006z;
            }
            if (count($check007z) > 0) {
                $result007z = $check007z;
            }
            if (count($check008z) > 0) {
                $result008z = $check008z;
            }
            if (count($check009z) > 0) {
                $result009z = $check009z;
            }
            if (count($check004z) > 0) {
                $result004z = $check004z;
            }
            if (count($check0042z) > 0) {
                $result0042z = $check0042z;
            }
            if (count($check005z) > 0) {
                $result005z = $check005z;
            }
            if (count($check0052z) > 0) {
                $result0052z = $check0052z;
            }
        }

        return view('sm.sm_verify_delete_mof', [
            'infosm' => $detailSM,
            'cr001' => $result001,
            'cr002' => $result002,
            'cr003' => $result003,
            'cr006' => $result006,
            'cr007' => $result007,
            'cr008' => $result008,
            'cr009' => $result009,
            'cr004' => $result004,
            'cr0042' => $result0042,
            'cr005' => $result005,
            'cr0052' => $result0052,
            'vr001' => $result001z,
            'vr002' => $result002z,
            'vr003' => $result003z,
            'vr006' => $result006z,
            'vr007' => $result007z,
            'vr008' => $result008z,
            'vr009' => $result009z,
            'vr004' => $result004z,
            'vr0042' => $result0042z,
            'vr005' => $result005z,
            'vr0052' => $result0052z,
            'carian' => request()->mof_no
        ]);
    }

    public function getEpErrorMessage()
    {
        $code = request()->errorCode;

        $response = $this->getEpErrorMessageByCode($code);

        $list = array();
        foreach ($response['result'] as $key => $value) {
            foreach ($value as $k => $v) {
                if ($k === 'en_code') {
                    array_push($list, array('key' => 'ENGLISH', 'value' => $v));
                } else {
                    array_push($list, array('key' => 'BAHASA MELAYU', 'value' => $v));
                }
            }
        }

        return view('search_ep_error', ['errorCode' => $code, 'result' => $list]);
    }

    public function detailsUserBySsm(Request $request, $reqSsmNo = null, $reqBusinessType = null)
    {
        session()->flashInput(request()->input());

        $statusCode = null;
        $statusDesc = null;
        $capitalEquityOwner = null;
        $CapitalEquityInformation = null;
        $companyAddress = null;
        $companyRegAddress = null;
        $personnelInfo = null;
        $particularOfAuditors = null;
        $totalPersonnel = 0;
        $totalNatureBusiness = 0;
        $totalAnnualRevenue = 0;
        $totalParticularOfAuditors = 0;
        $data = null;
        $xmlData = null;
        $xmlDataVer2 = null;
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'ssm_no' => 'required',
                'business_type' => 'required'
            ]);
        }
        $ssmNo = $request->ssm_no;
        $businessType = $request->business_type;

        if ($reqSsmNo != null && $reqBusinessType != null) {
            $ssmNo = $reqSsmNo;
            $businessType = substr($reqBusinessType, 0, 3);
        }
        if (str_contains($ssmNo, '-')) {
            $ssmNo = substr($ssmNo, 0, strpos($ssmNo, "-"));
        }

        if ($ssmNo != null) {
            $uuid  = \Ramsey\Uuid\Uuid::uuid4();
            $wsdlBody = '
            <x:Envelope
            xmlns:x="http://schemas.xmlsoap.org/soap/envelope/"
            xmlns:ret="http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo">
            <x:Header/>
            <x:Body>
                <ret:EPMFRq>
                    <ns1:RqHeader xmlns:ns1="http://www.ep.gov.my/Schema/1-0/epmf">
                             <ns1:ConsumerID>EPP-001</ns1:ConsumerID>
                             <ns1:UID>
                                     <ns1:RqUID>' . $uuid . '</ns1:RqUID>
                             </ns1:UID>
                    </ns1:RqHeader>
                    <ret:RetrieveSSMInfoRq>
                        <ret:BusinessRegistrationNo>' . $ssmNo . '</ret:BusinessRegistrationNo>
                        <ret:Type>' . $businessType . '</ret:Type>
                    </ret:RetrieveSSMInfoRq>
                </ret:EPMFRq>
            </x:Body>
        </x:Envelope>';

            $urlWsOsb = env("BASE_URL_WS_OSB", "http://*************:8011");
            $client = new Client([
                'base_uri' => $urlWsOsb,
            ]);
            $serviceVersionVer1 = '/RetrieveSSMInfo/v1.0'; //using SSM 1.0
            $payloadCompleted = $wsdlBody;
            $result  = $this->wsOsbRetrieveSSMInfo($serviceVersionVer1, $payloadCompleted);
            $data = new DOMDocument('1.0', 'utf-8');
            $data->loadXML($result);

            $data->preserveWhiteSpace = false;
            $data->formatOutput = true;
            $xmlData = $data->saveXML();
            $statusCode = $data->getElementsByTagName("StatusCode")->item(0)->nodeValue;
            $statusDesc = $data->getElementsByTagName("StatusDesc")->item(0)->nodeValue;
            if (isset($data->getElementsByTagName("AnnualRevenue")->item(0)->nodeValue)) {
                $totalAnnualRevenue = $data->getElementsByTagName("AnnualRevenue")->length;
            }
            if (isset($data->getElementsByTagName("NatureOfBusiness")->item(0)->nodeValue)) {
                $totalNatureBusiness = $data->getElementsByTagName("NatureOfBusiness")->length;
            }
            if (isset($data->getElementsByTagName("CompanyAddress")[0])) {
                $companyAddress = $data->getElementsByTagName("CompanyAddress")[0];
            }
            if (isset($data->getElementsByTagName("CompanyRegAddress")[0])) {
                $companyRegAddress = $data->getElementsByTagName("CompanyRegAddress")[0];
            }
            if (isset($data->getElementsByTagName("CapitalEquityOwner")[0]->nodeValue)) {
                // $capitalEquityOwner = $data->getElementsByTagName("CapitalEquityOwner")[0];
                $capitalEquityOwner = $data->getElementsByTagName("CapitalEquityOwner")->length;
            }

            if (isset($data->getElementsByTagName("CapitalEquityInformation")[0])) {
                $CapitalEquityInformation = $data->getElementsByTagName("CapitalEquityInformation")[0];
            }
            if (isset($data->getElementsByTagName("PersonnelInformation")->item(0)->nodeValue)) {
                $personnelInfo = $data->getElementsByTagName("PersonnelInformation");
                $totalPersonnel = $data->getElementsByTagName("PersonnelInformation")->length;
            }
            if (isset($data->getElementsByTagName("ParticularsOfAuditors")->item(0)->nodeValue)) {
                $totalParticularOfAuditors = $data->getElementsByTagName("ParticularsOfAuditors")->length;
                $particularOfAuditors = $data->getElementsByTagName("ParticularsOfAuditors");
            }


            // call ssm version 2
            $uuid  = \Ramsey\Uuid\Uuid::uuid4();
            $wsdlBodyVer2 = '
            <x:Envelope
                xmlns:x="http://schemas.xmlsoap.org/soap/envelope/"
                xmlns:ret="http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo">
                <x:Header/>
                <x:Body>
                    <ret:EPMFRq>
                        <ns1:RqHeader xmlns:ns1="http://www.ep.gov.my/Schema/1-0/epmf">
                                <ns1:ConsumerID>EPP-001</ns1:ConsumerID>
                                <ns1:UID>
                                        <ns1:RqUID>' . $uuid . '</ns1:RqUID>
                                </ns1:UID>
                        </ns1:RqHeader>
                        <ret:RetrieveSSMInfoRq>
                            <ret:BusinessRegistrationNo>' . $ssmNo . '</ret:BusinessRegistrationNo>
                            <ret:Type>' . $businessType . '</ret:Type>
                        </ret:RetrieveSSMInfoRq>
                    </ret:EPMFRq>
                </x:Body>
            </x:Envelope>';
            $serviceVersionVer2 = '/RetrieveSSMInfo/v2.0'; //using SSM 2.0
            $resultVer2  = $this->wsOsbRetrieveSSMInfo($serviceVersionVer2, $wsdlBodyVer2);
            $dataVer2 = new DOMDocument('1.0', 'utf-8');
            $dataVer2->loadXML($resultVer2);

            $dataVer2->preserveWhiteSpace = false;
            $dataVer2->formatOutput = true;
            $xmlDataVer2 = $dataVer2->saveXML();
        }



        return view('user_details_ssm', [
            'type' => 'ssmno',
            'ssmNo' => $ssmNo,
            'xmlData' => $xmlData,
            'xmlDataVer2' => $xmlDataVer2,
            'businessType' => $businessType,
            'statusCode' => $statusCode,
            'statusDesc' => $statusDesc,
            'data' => $data,
            'totalAnnualRevenue' => $totalAnnualRevenue,
            'totalNatureBusiness' => $totalNatureBusiness,
            'companyAddress' => $companyAddress,
            'companyRegAddress' => $companyRegAddress,
            'capitalEquityOwner' => $capitalEquityOwner,
            'CapitalEquityInformation' => $CapitalEquityInformation,
            'totalPersonnel' => $totalPersonnel,
            'personnelInfo' => $personnelInfo,
            'totalParticularOfAuditors' => $totalParticularOfAuditors,
            'particularOfAuditors' => $particularOfAuditors
        ]);
    }

    /**
     * SSM 1.0
     */
    public function wsOsbRetrieveSSMInfo($serviceVersion, $wsdlBody)
    {
        try {
            $urlWsOsb = env("BASE_URL_WS_OSB", "http://*************:8011");
            $client = new Client([
                'base_uri' => $urlWsOsb,
            ]);
            //$serviceVersion = '/RetrieveSSMInfo/v1.0'; //using SSM 1.0
            //$serviceVersion = '/RetrieveSSMInfo/v2.0'; //using SSM 2.0

            $payloadCompleted = $wsdlBody;
            $response = $client->post($serviceVersion, [
                //'debug' => TRUE,
                'body' => $payloadCompleted,
                'headers' => [
                    'Content-Type' => 'text/xml; charset=utf-8',
                    'SOAPAction' => 'RetrieveSSMInfo',
                ]
            ]);
            $result = $response->getBody();
            return $result;
        } catch (\Exception $exc) {
            $error = $exc->getMessage();
            return '<?xml version="1.0" encoding="UTF-8"?>
            <x:Envelope xmlns:x="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ret="http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo">
               <x:Header />
               <x:Body>
                  <ret:EPMFRq>
                     <ret:Result>
                        <ret:url><![CDATA[' . $urlWsOsb . ']]></ret:url>
                        <ret:service>' . $serviceVersion . '></ret:service>
                        <ret:desc><![CDATA[' . $error . ']]></ret:desc>
                     </ret:Result>
                  </ret:EPMFRq>
               </x:Body>
            </x:Envelope>';
        }
    }

    public function getPaymentHistory(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $paymentHistory = $this->getPaymentHistoryByPaymentId($paymentId);
        return response()->json($paymentHistory);
    }

    public function downloadSearchingUom($uom_name)
    {
        $listdata = $this->getListUomByUomName($uom_name);
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Searching By UOM';

            Excel::create($fileName, function ($excel) use ($collectlistReporting) {
                $excel->setTitle('Searching By UOM');
                $tryje = 1;
                $excel->sheet('Searching By UOM' . $tryje, function ($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@'
                        ));

                        $sheet->row(1, array(
                            'Id',
                            'Code',
                            'Name',
                            'Decimal Scale',
                            'Created Date',
                            'Changed Date'
                        ));

                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:F1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $obj) {
                            $sheet->row(
                                $count,
                                array(
                                    $obj->uom_id,
                                    $obj->uom_code,
                                    $obj->uom_name,
                                    $obj->decimal_scale,
                                    $obj->created_date,
                                    $obj->changed_date
                                )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/Uom/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/Uom/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function downloadAllUom()
    {
        $listdata = $this->getListAllUom();
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'All UOM';

            Excel::create($fileName, function ($excel) use ($collectlistReporting) {
                $excel->setTitle('All UOM');
                $tryje = 1;
                $excel->sheet('All UOM' . $tryje, function ($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@'
                        ));

                        $sheet->row(1, array(
                            'Id',
                            'Code',
                            'Name',
                            'Decimal Scale',
                            'Created Date',
                            'Changed Date'
                        ));

                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:F1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $obj) {
                            $sheet->row(
                                $count,
                                array(
                                    $obj->uom_id,
                                    $obj->uom_code,
                                    $obj->uom_name,
                                    $obj->decimal_scale,
                                    $obj->created_date,
                                    $obj->changed_date
                                )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/Uom/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/Uom/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function ePBlastSummary()
    {
        $completedCount = collect($this->getListCampaignCompleted())->count();
        $pendingCount = collect($this->getListCampaignPending())->count();
        $date = $request->searching_date ?? Carbon::now()->format('Y-m-d');
        $errorCount = collect($this->getErrorMessageByDate($date))->count();
    
        return view('ep_blast_email.blastEmailBlastSummary', [
            'completedCount' => $completedCount,
            'pendingCount' => $pendingCount,
            'errorCount' => $errorCount,
            'selectedDate' => $date,
        ]);
    }


    public function ePBlastErrorMessageByDate(Request $request)
    {

        if ($request->searching_date == null) {
            $date = Carbon::now()->format('Y-m-d');
        } else {
            $date = $request->searching_date;
        }
        $getListErrorMessage = null;
        $getListErrorMessage = $this->getErrorMessageByDate($date);

        return view('ep_blast_email.blastEmailErrorMessage', [
            'getListErrorMessage' => $getListErrorMessage,
            'date1' => $date,
        ]);
    }

    public function ePBlastListCampaignCompleted()
    {
        $getListCampaignCompleted = null;

        $getListCampaignCompleted = $this->getListCampaignCompleted();

        return view('ep_blast_email.blastEmailListCampaignCompleted', [
            'getListCampaignCompleted' => $getListCampaignCompleted,
        ]);
    }

    public function ePBlastListCampaignPending()
    {
        $getListCampaignPending = null;

        $getListCampaignPending = $this->getListCampaignPending();

        return view('ep_blast_email.blastEmailListCampaignPending', [
            'getListCampaignPending' => $getListCampaignPending,
        ]);
    }
}
