<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BpmApiService;


class BPMUpgrade12c {
    use BpmApiService;
    public static function syncErrorBpmWithStatusInEp() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
            $list = DB::connection('oracle_bpm_rpt')->table('UPGRADE_ERROR_LOG')->get(); 

            foreach($list as $row){
                $ecid = $row->info;
                $data = [];
                $data['err_created_date'] = Carbon::parse($row->created_time);
                $data['err_thread'] = $row->thread;
                $data['err_msg'] = $row->error_msg;


                //1ST : Default Info if exist in main data :  COMPOSITE INSTANCE
                $isFoundType = 'COMPOSITE_INSTANCE';
                $data['found_by'] = $isFoundType;
                $compObj = DB::connection('oracle_bpm_rpt')->table('COMPOSITE_INSTANCE')->where('ecid', $ecid)
                            ->selectRaw("SUBSTR(COMPOSITE_DN, 1, Instr(COMPOSITE_DN, '*', -1, 1) -1) as composite_name_cust")
                            ->addSelect('id','composite_dn','state','source_name','created_time','flow_id')
                            ->first(); 
                if( $compObj  != null){
                    $data['found_by'] = $isFoundType;
                    $data = self::populateCompositeInstance($compObj,$data);
                }

                //2ND : Get detail info TASK ASSIGNED WFTASK or CUBE_INSTANCE
                // WFTASK_WITH_DOCNO_ASSIGNED
                $obj = self::findWftaskWithDocNoAssigned($ecid,$data);
               
                if($obj == null){
                    // WFTASK_WITH_DOCNO_NOT_ASSIGNED
                    $obj = self::findWftaskWithDocNoNotAssigned($ecid,$data);
                }
                if($obj == null){
                    // WFTASK_WITH_DOCNO_NOTEXISTED
                    $obj = self::findWftaskWithDocNoNotExist($ecid,$data);
                }
                if($obj == null){
                    // WFTASK_WITH_DOCNO_NOTEXISTED
                    $obj = self::findCubeInstance($ecid,$data);
                }


                if($obj != null &&  $compObj != null  ){
                    MigrateUtils::logDump(__METHOD__ . " Found by  ".$data['found_by'] );
                    if(isset($data['bpm_doc_no']) && strlen($data['bpm_doc_no']) > 0 ){
                        $docNo = $data['bpm_doc_no'];
                        // Find eP DocNo Status
                        $docType = substr($docNo , 0, 2);
                        if($docType === 'LA'){
                            self::populateDataLoaInfo($docNo,$data);
                        }
                    }
                    dump(json_encode($data));
                }else{
                    MigrateUtils::logDump(__METHOD__ . " Last check by  ".$data['found_by']." still not found  by ecid :  $ecid" );
                }
                
                self::updateInsertBpm12cError( $ecid,$row->type,$data);
            }
        MigrateUtils::logDump(__METHOD__ . ' Completed ' );
    }

    /**
     * For LA , to get confirmation data LA already created until CT or not
     */
    private static function populateDataLoaInfo($docNo,&$data){
        $docLaObj = self::getDocLaStatus($docNo);
        if( $docLaObj != null){
            $data['ep_doc_status_id'] = $docLaObj->status_id;  
            $data['ep_doc_status_name'] = $docLaObj->status_name;  
            $infoLoaObj = self::getInfoLoaWithCt($docNo);
            if($infoLoaObj != null && strlen($infoLoaObj->contract_no) > 0){
                $ctNo = $infoLoaObj->contract_no;
                $data['ep_doc_other'] = $ctNo ;  
                $docCtObj = self::getDocCtStatus($ctNo);
                if($docCtObj != null){
                    $data['ep_doc_other_status_id'] = $docCtObj->status_id;  
                    $data['ep_doc_other_status_name'] = $docCtObj->status_name;  
                }
            }
        }
    }

    private static function findWftaskWithDocNoAssigned($ecid,&$data){
        $isFoundType = 'WFTASK_WITH_DOCNO_ASSIGNED';
        $obj = DB::connection('oracle_bpm_rpt')->table('WFTASK')->where('ecid',$ecid)->where('state','ASSIGNED')
                ->select('taskid','assignees','activityname','processid','processname','createddate','state','taskdefinitionname','componentname','compositeinstanceid','compositename','compositeversion','customattributestring1 as doc_no','customattributestring2 as doc_type','customattributenumber2 as status_id')
                ->first(); 
        if($obj != null ){
            $data['found_by'] = $isFoundType;
            $data = self::populateWftask($obj,$data);
        }

        return $obj ;
    }

    private static function findWftaskWithDocNoNotAssigned($ecid,&$data){
        $isFoundType = 'WFTASK_WITH_DOCNO_NOT_ASSIGNED';
        $obj = DB::connection('oracle_bpm_rpt')->table('WFTASK')->where('ecid', $ecid)
            ->whereNotNull('CUSTOMATTRIBUTESTRING1')
            ->select('taskid','assignees','activityname','processid','processname','createddate','state','taskdefinitionname','componentname','compositeinstanceid','compositename','compositeversion','customattributestring1 as doc_no','customattributestring2 as doc_type','customattributenumber2 as status_id')
            ->orderBy('CREATEDDATE','desc')->first();
        if($obj != null ){
            $data['found_by'] = $isFoundType;
            $data = self::populateWftask($obj,$data);
        }
        return $obj ;
    }

    private static function findWftaskWithDocNoNotExist($ecid,&$data){
        $isFoundType = 'WFTASK_WITH_DOCNO_NOTEXISTED';
        $obj = DB::connection('oracle_bpm_rpt')->table('WFTASK')->where('ecid', $ecid)
            ->whereNotIn('componentname',array('WorkflowStatusUpdate','WorkflowNotificationService'))
            ->select('taskid','assignees','activityname','processid','processname','createddate','state','taskdefinitionname','componentname','compositeinstanceid','compositename','compositeversion','customattributestring1 as doc_no','customattributestring2 as doc_type','customattributenumber2 as status_id')
            ->orderBy('CREATEDDATE','desc')->first();
        if($obj != null ){
            $data['found_by'] = $isFoundType;
            $data = self::populateWftask($obj,$data);
        }
        return $obj ;
    }

    private static function findCubeInstance($ecid,&$data){
        $isFoundType = 'CUBE_INSTANCE';
        $obj = DB::connection('oracle_bpm_rpt')->table('CUBE_INSTANCE')->where('ecid', $ecid)
            ->whereNotIn('component_name',array('WorkflowStatusUpdate','WorkflowNotificationService'))
            ->select('componenttype','composite_name','component_name','composite_revision','state','cikey','cmpst_id','creation_date')
            ->orderBy('state','asc')->first();
        if($obj != null ){
            $data['found_by'] = $isFoundType;
            $data = self::populateCubeInstance($obj,$data);
        }
        return $obj ;
    }
    
    
    private static function populateCompositeInstance($compObj,$data){
        $scmp = str_replace("default/", "", $compObj->composite_name_cust );
        $arrComposites = explode("!",$scmp);

        $data['composite_instance_id'] = $compObj->id;
        $data['composite_name'] = $arrComposites[0];  
        $data['composite_version'] = $arrComposites[1];  
        $data['composite_source_name'] = $compObj->source_name;  
        $data['composite_state'] = $compObj->state;
        $data['bpm_created_date'] = Carbon::parse($compObj->created_time);   
        return  $data;
    }

    private static function populateWftask($obj,$data){
       
        if(isset($data['composite_instance_id']) === false && strlen($data['composite_instance_id']) == 0){
            $data['composite_instance_id'] = $obj->compositeinstanceid;
        }
        if(isset($data['composite_name']) === false && strlen($data['composite_name']) == 0){
            $data['composite_name'] = $obj->compositename;
        }
        if(isset($data['composite_version']) === false && strlen($data['composite_version']) == 0){
            $data['composite_version'] = $obj->compositeversion;
        }
        if(isset($data['bpm_created_date']) === false && strlen($data['bpm_created_date']) == 0){
            $data['bpm_created_date'] = Carbon::parse($obj->createddate);
        }
 
        $data['bpm_doc_no'] = $obj->doc_no;
        $data['bpm_doc_type'] = $obj->doc_type;
        $data['bpm_doc_status_id'] = $obj->status_id;
        $data['component_name'] = $obj->componentname;
        $data['state'] = $obj->state;
        $data['process_id'] = $obj->processid;
        $data['process_name'] = $obj->processname;
        $data['activity_name'] = $obj->activityname;
        $data['assignees'] = $obj->assignees;
        $data['task_id'] = $obj->taskid;

        return  $data;
    }
    private static function populateCubeInstance($obj,$data){

        if(isset($data['composite_instance_id']) === false && strlen($data['composite_instance_id']) == 0){
            $data['composite_instance_id'] = $obj->cmpst_id;
        }
        if(isset($data['composite_name']) === false && strlen($data['composite_name']) == 0){
            $data['composite_name'] = $obj->composite_name;
        }
        if(isset($data['composite_version']) === false && strlen($data['composite_version']) == 0){
            $data['composite_version'] = $obj->composite_revision;
        }
        if(isset($data['bpm_created_date']) === false && strlen($data['bpm_created_date']) == 0){
            $data['bpm_created_date'] = Carbon::parse($obj->creation_date);
        }
 

        $data['component_name'] = $obj->component_name;
        $data['state'] = $obj->state;

        return  $data;
    }
    private static function updateInsertBpm12cError($ecid,$type,$data){

        $check = DB::connection('mysql_ep_support')
        ->table('ep_bpm_support.bpm_12_error')
        ->whereRaw("ecid='$ecid'")
        ->whereRaw("err_type='$type'")
        ->count();

        if($check > 0){
            $data['last_updated'] = Carbon::now();
            DB::connection('mysql_ep_support')
                ->table('ep_bpm_support.bpm_12_error')
                ->whereRaw("ecid='$ecid'")
                ->whereRaw("err_type='$type'")
                ->update($data);
        }else{
            $data['ecid'] = $ecid;
            $data['err_type'] = $type;
            DB::connection('mysql_ep_support')
                ->table('ep_bpm_support.bpm_12_error')
                ->insert($data);
        }

    }

    /**
     * Get Object LA Latest Status
     */
    private static function getDocLaStatus($docNo){
        $list = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
            B.DOC_ID,B.DOC_TYPE,
            A.LOA_ID,
            A.LOA_NO AS DOC_NO, 
            D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
            decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
            decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
            decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
            decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
            FROM SC_LOA A, SC_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
            WHERE A.LOA_ID = B.DOC_ID
            AND B.STATUS_ID = C.STATUS_ID
            AND C.STATUS_ID = D.STATUS_ID
            AND E.USER_ID (+) = B.CREATED_BY
            AND F.USER_ID (+) = B.CHANGED_BY
            AND D.LANGUAGE_CODE = 'en'
            AND B.DOC_TYPE in 'LA' 
            AND A.LOA_NO = ? 
            AND B.IS_CURRENT = 1
            ORDER BY B.CREATED_DATE DESC", array( $docNo));
        if(count($list) > 0){
            return $list[0];
        }
        return null;
    }

    /**
     * Get Object CT Latest Status
     */
    private static function getDocCtStatus($docNo){
        $list = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT TO_CHAR (B.CREATED_DATE,'YYYY-MM-DD HH12:MI:SS AM') FWS_DATE_CREATED, B.WORKFLOW_STATUS_ID,
            B.DOC_ID,B.DOC_TYPE,
            A.CONTRACT_ID,
            A.CONTRACT_NO AS DOC_NO, 
            D.STATUS_NAME ,C.STATUS_ID,B.IS_CURRENT,A.RECORD_STATUS, B.CREATED_BY,B.CHANGED_BY,
            decode (E.USER_ID , '' , 'null', E.USER_ID) AS CREATED_USER_ID,
            decode (E.LOGIN_ID , '' , 'null', E.LOGIN_ID) AS CREATED_LOGIN_ID,
            decode (F.USER_ID , '' , 'null', F.USER_ID) AS CHANGED_USER_ID,
            decode (F.USER_ID , '' , 'null', F.LOGIN_ID) AS CHANGED_LOGIN_ID
            FROM CT_CONTRACT A, CT_WORKFLOW_STATUS B, PM_STATUS C, PM_STATUS_DESC D, PM_USER E, PM_USER F
            WHERE A.CURRENT_CONTRACT_VER_ID = B.DOC_ID
            AND B.STATUS_ID = C.STATUS_ID
            AND C.STATUS_ID = D.STATUS_ID
            AND E.USER_ID (+) = B.CREATED_BY
            AND F.USER_ID (+) = B.CHANGED_BY
            AND D.LANGUAGE_CODE = 'en'
            AND A.CONTRACT_NO = ?  
            AND B.IS_CURRENT = 1
            ORDER BY B.CREATED_DATE DESC", array( $docNo));
        if(count($list) > 0){
            return $list[0];
        }
        return null;
    }

    /**
     * Get Info LA With CT 
     */
    private static function getInfoLoaWithCt($docNoLoa){
        $list = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT LOA.LOA_ID, LOA.LOI_LOA_ID  ,LOA.LOA_NO, LOA.CONTRACT_EFF_DATE,LOA.CONTRACT_DURATION,LOA.CONTRACT_EXP_DATE,LOA.SUPPLIER_SIGN_DATE,
                LOI.DOC_TYPE, LOI.DOC_ID,LOI.ORG_PROFILE_ID,LOI.SUPPLIER_ID,
                CT.CONTRACT_NO,CT.RECORD_STATUS AS CT_RECORD_STATUS 
                FROM SC_LOA  LOA     
                INNER JOIN SC_LOI_LOA LOI ON LOI.LOI_LOA_ID  = LOA.LOI_LOA_ID 
                LEFT JOIN CT_CONTRACT CT ON CT.LOA_ID = LOA.LOA_ID  
                WHERE 
                LOA.LOA_NO = ?
            ", array( $docNoLoa));
        if(count($list) > 0){
            return $list[0];
        }
        return null;
    }


    public static function findPayloadCT(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_12_error as d')
            ->whereNull('payload')
            ->where('state','ASSIGNED')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        foreach($listInstances as $inst){
            $compositeInstanceId = $inst->composite_instance_id ;
            $bpmTaskId = $inst->task_id ;
            $bpmUpgrade12c = new BPMUpgrade12c();
            $res = $bpmUpgrade12c->findApiWorklistTaskDetail($bpmTaskId);
            if($res != null && $res['status'] === 'Success' && isset($res['result']['payload']) && count($res['result']['payload']) > 0){

                $payload = $res['result']['payload'][0];
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_12_error')
                    ->where('composite_instance_id',$compositeInstanceId )
                    ->where('task_id',$bpmTaskId)
                    ->update([
                            'payload' =>  $payload
                        ]);
                MigrateUtils::logDump('Updated payload :: '.$res['result']['taskId']);
            }else{
                MigrateUtils::logDump("Nor succesfully found payload by compositeInstanceID $compositeInstanceId , task ID: $bpmTaskId  >> ".json_encode($res));
            }
        }
    }

    public static function reupdateTaskStatus(){

        MigrateUtils::logDump(__METHOD__.' > Entering..' );

        $listInstances  =  DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_12_error as d')
            ->where('state','ASSIGNED')
            ->get(); 
        MigrateUtils::logDump(__METHOD__.' > total found  '.count($listInstances) );
        sleep(2);
        //dd('done');
        foreach($listInstances as $inst){
            $compositeInstanceId = $inst->composite_instance_id ;
            $bpmTaskId = $inst->task_id ;

            $taskObj = DB::connection('oracle_bpm_rpt')->table('WFTASK')->where('taskid', $bpmTaskId)
            ->whereNull('state')
            ->select('taskid','assignees','activityname','processid','processname','createddate','state','taskdefinitionname','componentname','compositeinstanceid','compositename','compositeversion','customattributestring1 as doc_no','customattributestring2 as doc_type','customattributenumber2 as status_id')
            ->first();

            if( $taskObj){
                DB::connection('mysql_ep_support')->table('ep_bpm_support.bpm_12_error')
                ->where('composite_instance_id',$compositeInstanceId )
                ->where('task_id',$bpmTaskId)
                ->update([
                        'state' =>  $taskObj->state,
                        'last_updated' => Carbon::now(),
                        'remarks' => 'Task already completed'
                    ]);
            }
            
        }
    }
    
}
