<?php

namespace App\Services\Traits\Qt;

use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Facades\Log;
use Guzzle;

/**
 * QT Service
 *
 * <AUTHOR>
 */
trait QuotationDocumentService
{
    public function middleware_endpoint()
    {
        return env("JAVA_MIDDLEWARE_RESTFUL", "http://localhost:8080");
    }

    public function getQtDocumentChecklist($req)
    {
        $params = array($req->qtNo);
        $sql = 'SELECT   pr.proposal_no,qt.QT_NO, qc.checklist_action_id,qc.doc_category,att.doc_type, att.file_name,  att.file_path, att.FILE_ARC_FLAG
                    FROM sc_qt qt,
                        sc_qt_supplier qts,
                        sc_qt_proposal pr,
                        sc_qt_proposal_checklist qpc,
                        sc_attachment att,
                        sc_qt_checklist qc
                WHERE qt.qt_id = qts.qt_id
                    AND qts.qt_supplier_id = pr.qt_supplier_id
                    AND pr.qt_proposal_id = qpc.qt_proposal_id
                    AND qc.qt_checklist_id = qpc.qt_checklist_id
                    AND qpc.qt_proposal_checklist_id = att.doc_id(+)
                   AND qc.checklist_action_id IN (319, 322)
                    AND qc.doc_category in (1,2)
                    AND att.file_path IS NOT NULL 
                    AND pr.is_submitted = 1
                    AND qt.qt_no = ? ';

        if (isset($req->pmNo) && $req->pmNo != null) {
            $sql .= 'AND pr.proposal_no = ? ';
            array_push($params, $req->pmNo);
        }

        $qtFiles = DB::connection('oracle_nextgen_rpt')->select($sql, $params);

        if ($qtFiles) {
            return $qtFiles;
        }

        return [];
    }

    public function getDecryptKey($qtNo)
    {
        $params = array($qtNo);
        $sql = "SELECT qte.decrypt_key
                    FROM sc_qt qt,
                        (SELECT   enc.qt_id,
                                decrypt (MAX (enc.encryption_key),
                                            'commerceDotComMY'
                                        ) decrypt_key
                            FROM sc_qt_encryption enc
                        GROUP BY enc.qt_id) qte
                WHERE qt.qt_no = ? AND qt.qt_id = qte.qt_id ";

        $keys = DB::connection('oracle_nextgen_rpt')->select($sql, $params);

        if ($keys) {
            return $keys[0]->decrypt_key;
        }

        return null;
    }

    public function decryptDocument($filename, $dirname, $qtNo, $isarchive)
    {
        try {
            $token = base64_encode(strtotime("now"));
            $url = $this->middleware_endpoint() . "/ep/ejb/doc/decrypt";
            $dkey = $this->getDecryptKey($qtNo);
 
            $requestapi = [
                'token' => $token,
                'filename' => $filename,
                'dirname' => $dirname,
                'dkey' => $dkey,
                'isarchive' => $isarchive,
                'qtNo' => $qtNo
            ];

            $response = Guzzle::post($url, ["json" => $requestapi]);
            $resultResp = json_decode($response->getBody(), true);
            Log::info($resultResp);

            return true;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            Log::error("Oops! qt_decrypt_doc error => " . $ex->getMessage());
            return false;
        }

        return false;
    }

    public function deleteQtEDocument($filename)
    {
        try {
            $localPath = env('SSH_PORTAL_TMP_QT_ENCRYPT_PATH', '/tmp/qt/encrypt');
            $deletedfile = $localPath . '/' . $filename;
            if (file_exists($deletedfile)) {
                unlink($deletedfile);
            }
            Log::info('done delete encrypted file');
        } catch (Exception $e) {
            Log::error('delete encrypted file error => ' . $e->getMessage());
        }
    }

    public function deleteQtDDcoument($filename)
    {
        try {
            $localPath = env('SSH_PORTAL_TMP_QT_DECRYPT_PATH', '/tmp/qt/decrypt');
            $deletedfile = $localPath . '/' . $filename;
            if (file_exists($deletedfile)) {
                unlink($deletedfile);
            }
        } catch (Exception $e) {
            Log::error('delete decrypted file error => ' . $e->getMessage());
        }
    }

    public function checkDocumentExist($filename)
    {
        try {
            $localPath = env('SSH_PORTAL_TMP_QT_DECRYPT_PATH', '/tmp/qt/decrypt');
            $path_parts = pathinfo($filename);
            $ext = pathinfo($filename, PATHINFO_EXTENSION);
            $localFile = $localPath . '/' . $path_parts['filename'] . '.' . $ext;

            if (file_exists($localFile)) {
                return 1;
            }

            return 0;
        } catch (Exception $e) {
            Log::error("Oops! validate_qt_doc Error => " . $filename . ' - ' . $e->getMessage());
            return 0;
        }
    }

    public function transferAndDecryptDocument($filename, $qtno, $isarchive)
    {
        try {
            $path_parts = pathinfo($filename);
            $basename = $path_parts['basename'];
            $dirname = $path_parts['dirname'];


            $is_decrypted = $this->decryptDocument($basename, $dirname, $qtno, $isarchive);
            Log::info('$is_decrypted => ' . ($is_decrypted ? 'true' : 'false'));

            if ($is_decrypted) {
                return $basename;
            }

            return 0;
        } catch (Exception $e) {
            Log::error('decrypt error => ' . $e->getMessage());
            return 0;
        }
    }

    public function getListProposalNosByQtNo($qtNo)
    {
        $params = array($qtNo);
        $sql = "SELECT qt_no,proposal_no,pr.PROPOSAL_SUBMIT_DATE ,pr.MOF_NO 
                    FROM sc_qt qt,
                        sc_qt_supplier qts,
                        sc_qt_proposal pr
                WHERE qt.qt_id = qts.qt_id
                    AND qts.qt_supplier_id = pr.qt_supplier_id
                    and pr.IS_SUBMITTED = 1
                    AND qt.qt_no= ? ";

        $proposals = DB::connection('oracle_nextgen_rpt')->select($sql, $params);

        if ($proposals) {
            return $proposals;
        }

        return [];
    }

    public function doZipMultipleDocuments($request)
    {
        try {
            $dt_crt_fmt = Carbon::now()->format('YmdHi');
            $token = base64_encode(strtotime("now"));
            $docs = json_decode($request->filenames, true);
            $zipName = $request->qtno . "_" . $dt_crt_fmt . '.zip';
            $docList = array();

            foreach ($docs as $doc) {
                $part_paths = pathinfo($doc);
                array_push($docList, $part_paths['basename']);
            }

            $requestapi = [
                "filenames" => $docList,
                "qtNo" => $request->qtno . "_" . $dt_crt_fmt,
                "token" => $token,
            ];

            Log::info(__METHOD__ . json_encode($request));
            $response = Guzzle::post($this->middleware_endpoint() . "/ep/ejb/doc/zip", ["json" => $requestapi]);
            $content = $response->getBody()->getContents();

            return ['zipname' => $zipName, 'content' => $content];
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return ['zipname' => $request->qtno, 'content' => $e->getMessage()];
        }
    }
}
