<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use DateTime;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use App\Model\Notify\NotifyModel;
use Exception;

class HandleSlaPmoAssessment extends Command {

    public static function crmService() {
        return new CRMService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSlaPmoAssessment';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To send alert for cases with S4 severity (PMO) for assessment';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__ . ' > ' . __FUNCTION__ . ' >> ';
        MigrateUtils::logDump($clsInfo . 'starting ..');
        $pmoTasks = self::crmService()->getDashboardCRMPMOAgeing('before');
        if (count($pmoTasks) > 0) {
            foreach ($pmoTasks as $tasks) {
                $collect = collect();
                $current = Carbon::now();
                $estimateDue = date('Y-m-d H:i:s', strtotime($tasks->pmo_start_datetime . "+ $tasks->taskduration"));
                $estimateDueDate = Carbon::parse($estimateDue)->addHour(8)->format("Y-m-d H:i:s");
                $dateDiff = $current->diff(new DateTime($estimateDueDate));
                $timeRemaining = $dateDiff->days;
                $statusRedmine = null;
//                $module = $tasks->module;
                if ($tasks->ageing_day_pmo > 1 && $tasks->ageing_day_pmo <= 8) {
                    if (isset($tasks->redmine_number)) {
                        try {
                            $urlRedmine = env("URL_REDMINE", "https://192.168.120.102");
                            $keyClientRedmine = env("KEY_CLIENT_REDMINE", "62d875bd246828ad033b54bf5b39a9a50c3aa1bb");
                            $client = new \GuzzleHttp\Client([
                                'base_uri' => $urlRedmine,
                            ]);
                            $data = [
                                'headers' => [
                                    'Content-Type' => 'application/json'
                                ],
                                'json' => [
                                    'key' => $keyClientRedmine
                                ],
                                'verify' => false
                            ];
                            $pathSpecificApi = '/issues/' . $tasks->redmine_number . '.json';
                            $urlRedmineApi = $urlRedmine . $pathSpecificApi;
                            MigrateUtils::logDump(' URL Redmine API ' . $urlRedmineApi);
                            $response = $client->request('GET', $pathSpecificApi, $data);
                            $resultResp = json_decode($response->getBody(), true);
                            try {
                                $statusRedmine = ($resultResp['issue']['status']['name']);
                                $module = ($resultResp['issue']['custom_fields'][0]['value']);
                                Log::info('redmine status ' . $statusRedmine .', module ' .$module);
                            } catch (Exception $ex) {
                                $msg = $ex->getMessage();
                            }
                        } catch (Exception $ex) {
                            $msg = $ex->getMessage();
                        }
                    }
                    
                    $receiverGrp = '';
                    $grpDefectSm = 'SLA_PMO_SM';
                    $grpDefectDp = 'SLA_PMO_DP';
                    $grpDefectFl = 'SLA_PMO_FL';
                    $grpDefectPm = 'SLA_PMO_PM';
                    $grpDefectCt = 'SLA_PMO_CT';
                    $grpDefectQt = 'SLA_PMO_QT';
                    
                    if($module == 'Supplier Management' || $module == 'Report-Supplier Management' || $module == 'SSM-External') {
                        $receiverGrp = $grpDefectSm;
                    }else if($module == 'Direct Purchase') {
                        $receiverGrp = $grpDefectDp;
                    }else if($module == 'Fulfilment') {
                        $receiverGrp = $grpDefectFl;
                    }else if($module == 'Profile Management' || $module == 'PPlan' || $module == 'Catalogue/Codification Management') {
                        $receiverGrp = $grpDefectPm;
                    }else if($module == 'Contract Management') {
                        $receiverGrp = $grpDefectCt;
                    }else if($module == 'Quotation/Tender') {
                        $receiverGrp = $grpDefectQt;
                    }
                    
                    $msg = "
*[ALERT] ASSESSMENT SLA ALERT.* 
*Case Number* 
  $tasks->case_number 
*Module* 
  $module 
*Redmine Number* 
  $tasks->redmine_number 
*Redmine Status* 
  $statusRedmine 
*Ageing Day* 
  $tasks->ageing_day_pmo Days";

                    MigrateUtils::logErrorDump($clsInfo . $msg);
                    $collect->put('msg', $msg);
                    if($receiverGrp != ''){
                        $this->saveNotify($collect,$receiverGrp);
                    }
                    
                }
            }
        }
    }

    public function saveNotify($collect,$receiverGrp) {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiverGrp;
        $nty->process = 'notify group'; // 'notify group , 'notify personal
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring sla pmo with severity 4';
        $nty->save();
    }

}
