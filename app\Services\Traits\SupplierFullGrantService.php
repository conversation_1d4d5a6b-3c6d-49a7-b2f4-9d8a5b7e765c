<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * This service will connect user has fully grant CREATE/UPDATE/DELETE FUNCTION.
 * Please be careful using DB::connection('oracle_nextgen_fullgrant').
 * Any action function  CREATE/UPDATE/DELETE and query will impact performance and data integrity.
 *
 * <AUTHOR>
 */
trait SupplierFullGrantService {


    public function createLogQueryTable($objBeforeUpdate,$queryObj,$fields) {
        //Extract to get Old Data
        $oldObj = collect([]);
        if($objBeforeUpdate != null){
            foreach ($fields as $key=>$value){
                //Get old value
                $oldObj->put($key,$objBeforeUpdate->{$key});
            }
        }
        
        
        $logQuery = collect([]);
        $logQuery->put('data_before',$oldObj);   
        $logQuery->put('table',$queryObj->from);      
        $logQuery->put('where',$queryObj->wheres);
        $logQuery->put('update',$fields);
        if($oldObj->count() > 0){
            $logQuery->put('status','Success update the record.');
        }else{
            $logQuery->put('status','Failed update the record. Get the record is not found');
        }
        return $logQuery;
    }
    
    public function createLogDeleteRecordTable($objBeforeUpdate,$queryObj) {
        $logQuery = collect([]);
        $logQuery->put('status','success');  
        $logQuery->put('data_before',$objBeforeUpdate);   
        $logQuery->put('table',$queryObj->from);      
        $logQuery->put('where',$queryObj->wheres);
        $logQuery->put('delete','Permenantly Delete');
        return $logQuery;
    }
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $supplierId
     * @return type
     */
    public function getSMSupplierDetails($applId,$supplierId) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER as S');
        $query->join('SM_APPL as A', 'S.SUPPLIER_ID', '=', 'A.SUPPLIER_ID');
        $query->join('SM_COMPANY_BASIC as C', 'A.APPL_ID', '=', 'C.APPL_ID');
        $query->where('C.REV_NO', DB::raw("(select max(per.rev_no) from SM_COMPANY_BASIC per WHERE per.APPL_ID=C.APPL_ID)"));
        $query->where('S.SUPPLIER_ID', $supplierId);
        $query->where('A.APPL_ID', $applId);
        
        $query->select('S.*');
        $query->addSelect('S.RECORD_STATUS AS S_RECORD_STATUS','S.CREATED_DATE AS S_CREATED_DATE','S.CHANGED_DATE AS S_CHANGED_DATE','S.CREATED_BY AS S_CREATED_BY','S.CHANGED_BY AS S_CHANGED_BY');
        
        $query->addSelect('A.APPL_ID','A.APPL_NO','A.SUPPLIER_TYPE','A.APPL_TYPE','A.IS_RESUBMIT','A.IS_ACTIVE_APPL');
        $query->addSelect('A.RECORD_STATUS AS A_RECORD_STATUS','A.CREATED_DATE AS A_CREATED_DATE','A.CHANGED_DATE AS A_CHANGED_DATE','A.CREATED_BY AS A_CREATED_BY','A.CHANGED_BY AS A_CHANGED_BY');
        
        $query->addSelect('C.COMPANY_BASIC_ID','C.COMPANY_NAME AS C_COMPANY_NAME','C.IS_WITH_FEDERAL','IS_WITH_STATE','IS_WITH_STATUTORY','IS_WITH_GLC','IS_WITH_OTHERS');
        $query->addSelect('C.PHONE_COUNTRY','C.PHONE_AREA','C.PHONE_NO','C.FAX_COUNTRY','C.FAX_AREA','C.FAX_NO','C.INCOME_TAX_NO');
        $query->addSelect('C.RECORD_STATUS AS C_RECORD_STATUS','C.CREATED_DATE AS C_CREATED_DATE','C.CHANGED_DATE AS C_CHANGED_DATE','C.CREATED_BY AS C_CREATED_BY','C.CHANGED_BY AS C_CHANGED_BY');
        
        return $query->first();
    }
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getListSMSupplierPMUser($supplierId) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER as S');
        $query->join('PM_USER_ORG as UO', 'S.SUPPLIER_ID', '=', 'UO.ORG_PROFILE_ID');
        $query->join('PM_USER_ROLE as UR', 'UO.USER_ORG_ID', '=', 'UR.USER_ORG_ID');
        $query->join('PM_USER as U', 'UO.USER_ID', '=', 'U.USER_ID');
        $query->where('S.SUPPLIER_ID', $supplierId);
        $query->where('UO.RECORD_STATUS', 1);
        $query->where('UR.RECORD_STATUS', 1);
        $query->select('S.*');
        $query->addSelect('S.RECORD_STATUS AS S_RECORD_STATUS','S.CREATED_DATE AS S_CREATED_DATE','S.CHANGED_DATE AS S_CHANGED_DATE','S.CREATED_BY AS S_CREATED_BY','S.CHANGED_BY AS S_CHANGED_BY');
        
        $query->addSelect('U.USER_ID','U.USER_NAME','U.IDENTIFICATION_NO','U.LOGIN_ID','U.ORG_TYPE_ID','U.EMAIL');
        $query->addSelect('U.RECORD_STATUS AS U_RECORD_STATUS','U.CREATED_DATE AS U_CREATED_DATE','U.CHANGED_DATE AS U_CHANGED_DATE','U.CREATED_BY AS U_CREATED_BY','U.CHANGED_BY AS U_CHANGED_BY');

        $query->addSelect('UO.ORG_PROFILE_ID','UR.USER_ORG_ID');
        $query->addSelect('UO.RECORD_STATUS AS UO_RECORD_STATUS','UO.CREATED_DATE AS UO_CREATED_DATE','UO.CHANGED_DATE AS UO_CHANGED_DATE','UO.CREATED_BY AS UO_CREATED_BY','UO.CHANGED_BY AS UO_CHANGED_BY');
        
        $query->addSelect('UR.ROLE_CODE','UR.USER_ROLE_ID');
        $query->addSelect('UR.RECORD_STATUS AS UR_RECORD_STATUS','UR.CREATED_DATE AS UR_CREATED_DATE','UR.CHANGED_DATE AS UR_CHANGED_DATE','UR.CREATED_BY AS UR_CREATED_BY','UR.CHANGED_BY AS UR_CHANGED_BY');
        
        
        return $query->get();
    }
    
    
    /**
     * Get one record details MOF Details
     * @param type $supplierId
     * @param type $supplierType
     * @return type
     */
    public function getMOFDetails($supplierId,$supplierType) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_MOF_ACCOUNT as S');
        $query->where('S.SUPPLIER_ID', $supplierId);
        $query->where('S.SUPPLIER_TYPE', $supplierType);
        $query->select('S.*');
        $query->orderBy('S.created_date','desc');
        return $query->first();
    }
    
    
    /**
     * Get list info for process current application for supplier and also application latest approved
     * @param type $supplierID
     * @return type
     */
    public function getListPersonnelIcBySupplierId($supplierID) {

        $results = DB::connection('oracle_nextgen_fullgrant')->select(
                " SELECT distinct
                    personnel_id,
                    appl_id,
                    identification_no,
                    name,
                    identity_resident_status,
                    identity_response_code
                  FROM SM_PERSONNEL
                  WHERE appl_id = (
                    SELECT appl_id
                    FROM sm_appl
                    WHERE supplier_id = ?
                    AND is_active_appl = 1
                  )
                  OR appl_id = (
                    select latest_appl_id from sm_supplier where supplier_id = ?
                  ) ", array($supplierID,$supplierID));

        return $results;
    }
    
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getListSMSupplierUserDetailsBySpecificUser($supplierId,$userId) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER as U');
        $query->join('PM_USER_ORG as UO', 'U.USER_ID', '=', 'UO.USER_ID');
        $query->join('PM_USER_ROLE as UR', 'UO.USER_ORG_ID', '=', 'UR.USER_ORG_ID');
        $query->where('UO.ORG_PROFILE_ID', $supplierId);
        $query->where('U.USER_ID', $userId);
        $query->select('U.*');
        $query->addSelect('U.RECORD_STATUS AS U_RECORD_STATUS','U.CREATED_DATE AS U_CREATED_DATE','U.CHANGED_DATE AS U_CHANGED_DATE','U.CREATED_BY AS U_CREATED_BY','U.CHANGED_BY AS U_CHANGED_BY');

        $query->addSelect('UO.ORG_PROFILE_ID','UR.USER_ORG_ID');
        $query->addSelect('UO.RECORD_STATUS AS UO_RECORD_STATUS','UO.CREATED_DATE AS UO_CREATED_DATE','UO.CHANGED_DATE AS UO_CHANGED_DATE','UO.CREATED_BY AS UO_CREATED_BY','UO.CHANGED_BY AS UO_CHANGED_BY');
        
        $query->addSelect('UR.ROLE_CODE','UR.USER_ROLE_ID');
        $query->addSelect('UR.RECORD_STATUS AS UR_RECORD_STATUS','UR.CREATED_DATE AS UR_CREATED_DATE','UR.CHANGED_DATE AS UR_CHANGED_DATE','UR.CREATED_BY AS UR_CREATED_BY','UR.CHANGED_BY AS UR_CHANGED_BY');

        return $query->get();
    }
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getSMSupplierUsersDetailsByPersonnel($applId, $personnelId) {
        
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');

        if ($applId != null) {
            $query->where('P.APPL_ID', $applId);
        }
        if ($personnelId != null) {
            $query->where('P.PERSONNEL_ID', $personnelId);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(per.rev_no) from SM_PERSONNEL per WHERE per.APPL_ID=P.APPL_ID and per.IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID','P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT','P.EP_ROLE',  'P.EP_ROLE as P_EP_ROLE',  'P.USER_ID as P_USER_ID');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI as P_IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->first();
    }
    
    public function getListSMSoftcertRequestBySoftcertProvider($supplierId,$userId,$softcertProvider) {
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_SOFTCERT_REQUEST as R')
                ->where('supplier_id', $supplierId)
                ->where('user_id', $userId)
                ->where('softcert_provider', $softcertProvider);
        $query->whereRaw('not exists (select * from PM_DIGI_CERT a where a.softcert_request_id = R.softcert_request_id )');
        return $query->get();        
    }
    
    public function checkSoftcertRequestTG($softcertRequestId) {
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_SOFTCERT_REQUEST as R')
                ->where('softcert_request_id', $softcertRequestId)
                ->where('softcert_provider', 'TG');
        return $query->count();        
    }
    
    /**
     * Get Query for  Supplier Users Info.
     * @param type $applId
     * @param type $personnelId
     * @return type
     */
    public function getSMSupplierUsersDetailsInProgressApplByPersonnel($applId, $personnelId) {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL as P');
        $query->join('SM_APPL as A', 'P.APPL_ID', '=', 'A.APPL_ID');
        $query->join('SM_SUPPLIER as S', 'A.SUPPLIER_ID', '=', 'S.SUPPLIER_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');

        if ($applId != null) {
            $query->where('P.APPL_ID', $applId);
        }
        if ($personnelId != null) {
            $query->where('P.PERSONNEL_ID', $personnelId);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(per.rev_no) from SM_PERSONNEL per WHERE per.APPL_ID=P.APPL_ID and per.IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        
        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID','P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI as P_IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('A.APPL_NO');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );
        //dump($data);
        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->first();
    }
    
    /**
     * Get list info for process current application for supplier 
     * @param type $supplierID
     * @return type
     */
    public function getInProgressWorkFlowSupplierProcessUsingFullGrantBySupplierId($supplierID) {

        $results = DB::connection('oracle_nextgen_fullgrant')->select(
                " SELECT   wf.is_current, sup.supplier_id, sup.company_name, 
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where 
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'  
                    and ppd.RECORD_STATUS = 1 
                    and pp.RECORD_STATUS = 1 
                    and pp.PARAMETER_TYPE = 'AT' 
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type , 
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id 
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ?  
                AND appl.IS_ACTIVE_APPL = 1 
           ORDER BY appl.appl_id DESC,wf.created_date DESC, wf.is_current DESC", array($supplierID));

        return $results;
    }
    
    public function getRecordMminfDetails($mminfID) {

        $results = DB::connection('oracle_nextgen_fullgrant')->select(
                " SELECT * FROM DI_MMINF where mminf_id = ?", array($mminfID));

        return $results;
    }
    
    /**
     * 
     * @param type $kodItem  -- Refer to 18 Digit Extension Code
     * @return type
     */
    public function getRecordMminfDetailsByKodItem($kodItem,$unitUkuran = null) {

        $query = DB::connection('oracle_nextgen_fullgrant')->table('DI_MMINF')
                ->where('material_code',$kodItem);
        if($unitUkuran != null){
            $query->where('alt_uom',$unitUkuran);
        }
        $query->orderBy('created_date','desc');
        $result =  $query->first();
        return $result;
    }
    
    /**
     * Get list info for process current application for supplier 
     * @param type $supplierID
     * @return type
     */
    public function getInProgressWorkFlowSupplierProcessUsingFullGrant($supplierID,$identification_no) {

        $results = DB::connection('oracle_nextgen_fullgrant')->select(
                " SELECT   wf.is_current, sup.supplier_id, sup.company_name, 
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where 
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'  
                    and ppd.RECORD_STATUS = 1 
                    and pp.RECORD_STATUS = 1 
                    and pp.PARAMETER_TYPE = 'AT' 
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type , 
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status,
                    sp.personnel_id 
               FROM sm_supplier sup,
                    sm_personnel sp,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND sp.appl_id = appl.appl_id 
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ? 
                AND sp.identification_no = ? 
                AND appl.IS_ACTIVE_APPL = 1 
           ORDER BY appl.appl_id DESC,wf.created_date DESC, wf.is_current DESC ", array($supplierID,$identification_no));

        return $results;
    }
    
    /**
     * UPDATE SM PERSONNEL
     * @param type $personnelId
     * @param type $fields
     */
    public function updateSMPersonnel($personnelId,$fields) {
        if($personnelId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_PERSONNEL')
                ->where('personnel_id', $personnelId);
        $dataBefore =  $query->first();
        $query->update($fields);      
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }

    /**
     * UPDATE SM WORKFLOW STATUS
     * @param type $personnelId
     * @param type $fields
     */
    public function updateSMWorkflowStatus($workflowStatusId,$fields) {
        if($workflowStatusId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_WORKFLOW_STATUS')
                ->where('workflow_status_id', $workflowStatusId);
        $dataBefore =  $query->first();
        $query->update($fields);      
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    
    public function getListSMMofCert($supplierId) {
        $query =  DB::connection('oracle_nextgen_fullgrant')->table('SM_MOF_CERT as C');
        $query->join('SM_MOF_ACCOUNT as A', 'C.MOF_ACCOUNT_ID', '=', 'A.MOF_ACCOUNT_ID');
        $query->where('supplier_id', $supplierId);
        $query->where('A.record_status', 1);
        $query->where('C.record_status', 1);
        $query->select('C.*');
        return $query->get();        
    }
    
    public function getListSMMofCertActiveByMofAccountId($mofAccountId) {
        $query =  DB::connection('oracle_nextgen_fullgrant')->table('SM_MOF_CERT as C');
        $query->where('C.mof_account_id', $mofAccountId);
        $query->where('C.record_status', 1);
        return $query->get();        
    }
    
    /**
     * UPDATE SM_MOF_CERT
     * @param type $mofCertId
     * @param type $fields
     */
    public function updateSMMofCert($mofCertId,$fields) {
        if($mofCertId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_MOF_CERT')
                ->where('mof_cert_id', $mofCertId);
        $dataBefore =  $query->first();
        $query->update($fields);      
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    public function getSMSoftcertRequestWihoutCert($supplierId,$userId) {
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_SOFTCERT_REQUEST as R')
                ->where('supplier_id', $supplierId)
                ->where('user_id', $userId)
                ->where('record_status', 1)
                ->whereNotExists(function ($query) {
                    $query->select(DB::raw(1))
                          ->from('PM_DIGI_CERT as A')
                          ->whereRaw('A.softcert_request_id = R.softcert_request_id');
                });
        return $query->first();        
    }
    
    public function updateSMSoftcertRequest($softcertRequestId,$fields) {
        if($softcertRequestId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_SOFTCERT_REQUEST')
                ->where('softcert_request_id', $softcertRequestId);
        $dataBefore =  $query->first();
        
        $query->update($fields);      
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE DI_MMINF
     * @param type $mminfId,$fields
     * @param type $fields
     */
    public function updateMminfIDDetails($mminfId,$fields) {
        if($mminfId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('DI_MMINF')
                ->where('mminf_id', $mminfId);
        $dataBefore =  $query->first();
        $query->update($fields); // field untuk is_sent = 0
              
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    public function deleteDIMminf($mminfId) {
        if($mminfId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('DI_MMINF')
                ->where('mminf_id', $mminfId);
        $dataBefore =  $query->first();
        $query->delete(); 
              
        return $this->createLogDeleteRecordTable($dataBefore, $query);
    }
    
    /**
     * UPDATE SM_SUPPLIER
     * @param type $supplierId
     * @param type $fields
     */
    public function updateSMSupplier($supplierId,$fields) {
        if($supplierId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_SUPPLIER')
                ->where('supplier_id', $supplierId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE SM_MOF_ACCOUNT
     * @param type $mofAccountId
     * @param type $fields
     */
    public function updateSMMofAccount($mofAccountId,$fields) {
        if($mofAccountId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_MOF_ACCOUNT')
                ->where('mof_account_id', $mofAccountId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE SM_COMPANY_BASIC
     * @param type $companyBasicId
     * @param type $fields
     */
    public function updateSMCompanyBasic($companyBasicId,$fields) {
        if($companyBasicId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_COMPANY_BASIC')
                ->where('company_basic_id', $companyBasicId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE PM_USER
     * @param type $userId
     * @param type $fields
     */
    public function updatePMUser($userId,$fields) {
        if($userId == null ) { return null;}
        
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('PM_USER')
                ->where('user_id', $userId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE PM_USER_ORG
     * @param type $userOrgId
     * @param type $fields
     */
    public function updatePMUserOrg($userOrgId,$fields) {
        if($userOrgId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('PM_USER_ORG')
                ->where('user_org_id', $userOrgId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE SM_APPL
     * @param type $applId
     * @param type $fields
     */
    public function updateSMAppl($applId,$fields) {
        if($applId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_APPL')
                ->where('appl_id', $applId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }
    
    /**
     * UPDATE PM_USER_ROLE
     * @param type $userRoleId
     * @param type $fields
     */
    public function updatePMUserRole($userRoleId,$fields) {
        if($userRoleId == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('PM_USER_ROLE')
                ->where('user_role_id', $userRoleId);
        $dataBefore =  $query->first();
        $query->update($fields);
        
        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }

    /*
     * Return single object
     * Parameters personnel_id,identification_no,appl_id
     */
    public function getSMPersonnelDetailsOnly($parameters) {
        
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_PERSONNEL');
        if($parameters->has('personnel_id')){
            $query->where('personnel_id', $parameters->get('personnel_id'));
        }
        if($parameters->has('identification_no')){
            //dump('identification_no: '.$parameters->get('identification_no'));
            $query->where('identification_no', $parameters->get('identification_no'));
        }
        if($parameters->has('appl_id')){
            //dump('appl_id: '.$parameters->get('appl_id'));
            $query->where('appl_id', $parameters->get('appl_id'));
        }
        $query->orderBy('rev_no','desc');
        //dump($query->toSql());
        //dump($query->wheres);
        $query->first();
    }
    
       public function getRecordByItemDetails($mminfId, $materialCode) {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('DI_MMINF as A');
        $query->where('A.MMINF_ID', $mminfId);
        $query->where('A.MATERIAL_CODE', $materialCode);
        $query->where('A.RECORD_STATUS', 1);
        $query->select('A.MMINF_ID','A.MATERIAL_CODE','A.CODE_TYPE','A.ACTION_CODE','A.MATERIAL_DESC');   
        $query->addSelect('A.MATERIAL_ADD_DESC','A.BASE_UOM','A.ALT_UOM','A.IS_SENT');
        return $query->first();
    }
    
    public function getRecordByItemDetailsUpdated($mminfId, $materialCode) {
        
        $results = DB::connection('oracle_nextgen_fullgrant')->select(
            "select * from DI_MMINF
            WHERE RECORD_STATUS = 1 
            AND MMINF_ID = ? AND MATERIAL_CODE = ? ", array($mminfId,$materialCode));       
        return $results;  
        
    }
    
    public function isSapVendorCode($epNo){
        $obj = $this->getSAPVendorCodeSupplierHq($epNo);
        if($obj != null){
            return true;
        }else{
            return false;
        }
    }
    public function getSAPVendorCodeSupplierHq($epNo) {
        // select * from SM_SAP_VENDOR_CODE where ep_no = 'eP-1005C001D' and branch_code is null;
        $obj = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE')   
                ->whereNull('BRANCH_CODE')
                ->where('EP_NO',$epNo)
                ->where('RECORD_STATUS',1)
                ->first();
        return $obj;  
        
    }
    public function isExistSupplierSSM($ssmNo,$supplierType){
        $obj = $this->getSMSupplierDetailByRegNoAndSupplierType($ssmNo,$supplierType);
        if($obj != null){
            return true;
        }else{
            return false;
        }
    }
    
    public function getSMSupplierDetailByRegNoAndSupplierType($ssmNo,$supplierType) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER as S') ;
        $query->join('SM_APPL as A', 'S.LATEST_APPL_ID', '=', 'A.APPL_ID');
        $query->where('S.REG_NO',$ssmNo);
        $query->where('A.SUPPLIER_TYPE',$supplierType);
        $query->where('S.RECORD_STATUS',1);
        return $query->first();
    }
    
    public function getSMSupplierDetailByEpNo($epNo) {
        $query = DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER as S') ;
        $query->join('SM_APPL as A', 'S.LATEST_APPL_ID', '=', 'A.APPL_ID');
        $query->where('S.EP_NO',$epNo);
        return $query->first();
    }
    
    public function totalAuthorizedUserSupplier($applId) {
        return DB::connection('oracle_nextgen_fullgrant')->table('sm_personnel')
                        ->where('appl_id', $applId)
                        ->where('is_authorized', 1)
                        ->count();
    }

    public function totalContractSignerUserSupplier($applId) {
        return DB::connection('oracle_nextgen_fullgrant')->table('sm_personnel')
                        ->where('appl_id', $applId)
                        ->where('is_contract_signer', 1)
                        ->count();
    }
    
    public function totalIdentificationNoSmPersonnel($applId,$identificationNo) {
        return DB::connection('oracle_nextgen_fullgrant')->table('sm_personnel')
                        ->where('appl_id', $applId)
                        ->where('identification_no', $identificationNo)
                        ->where('record_status', '1')
                        ->count();
    }
    
    /**
     * 
     * @param type $tableName
     * @param type $field
     * @param type $record_id
     * @return One Record Object
     */
    public function getDetailsByTable($databaseConnection,$tableName,$field,$record_id) {
        try {
            return DB::connection($databaseConnection)->table($tableName)
                        ->where($field, $record_id)
                        ->take(20)  // Set max record view 20 only.
                        ->get();  
        } catch (\Illuminate\Database\QueryException $ex) {
            return null;
        }
        
    }
    
    
    /**
     * UPDATE field value based on TABLE
     * @param type $applId
     * @param type $fields
     */
    public function updateRecordByTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,$addWheres = null) {
        if($tableName == null || strlen(trim($tableName)) == 0 
                || $primaryField == null || strlen(trim($primaryField)) == 0 
                || $primaryValue == null || strlen(trim($primaryValue)) == 0 
                || count($updateScriptFields) == 0) { 
                    $logQuery = collect([]);
                    $logQuery->put('status','Query UPDATE is not execute. Not match with requirement!');  
                    return $logQuery;
                }
                       
        $query =  DB::connection($databaseConnection)
                ->table($tableName)
                ->where($primaryField, $primaryValue);
        if($addWheres != null && count($addWheres) > 0){
            foreach ($addWheres as $key => $value) {
                $query->where($key,$value);
            }
        }
        $dataBefore =  $query->first();

        if($dataBefore  != null){
            $query->update($updateScriptFields);
        }
        
        return $this->createLogQueryTable($dataBefore, $query, $updateScriptFields);
    }

    /**
     * This delete permenantly table. This function to make sure will delete only one record.
     * @param type $tableName
     * @param type $field
     * @param type $record_id
     * @return One Record Object
     */
    public function deleteDetailsByTable($databaseConnection,$tableName,$primaryField,$primaryValue) {
                       
        $query =  DB::connection($databaseConnection)
                ->table($tableName)
                ->where($primaryField, $primaryValue);
        $dataCount =  $query->count();
        if($dataCount == 1){
            // will proceed delete
            $dataBefore =  $query->first();
            $query->delete();
            return $this->createLogDeleteRecordTable($dataBefore, $query);
        }
        $logQuery = collect([]);
        $logQuery->put('status','failed');  
        $logQuery->put('description','Record not found or record not return as one record.');  
        return $logQuery;
    }

    /**
     * UPDATE SC_QT
     * @param type $qtIds
     * @param type $fields
     */
    public function updateScQt($qtIds,$fields) {
        if($qtIds == null ) { return null;}
        $query =  DB::connection('oracle_nextgen_fullgrant')
            ->table('SC_QT')
            ->whereIn('QT_ID', $qtIds);
        $dataBefore =  $query->first();
        $query->update($fields);

        return $this->createLogQueryTable($dataBefore, $query, $fields);
    }

}
