@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of MOF Suppliers Inactive/Deleted but actually is Active</strong></h1> 
    </div> 
    <div class="block">

        <!-- Log Content -->
        <div class="table-responsive">
            <p>
                <code>
                Note:Supplier is valid cert MOF but checking on status supplier is inactive/deleted.
                </code>
            </p>
        </div>
        <!-- END Log Content -->
    </div>
    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Company Name</th>
                    <th class="text-center">MOF No.</th>
                    <th class="text-center">Supplier Status</th>
                    <th class="text-center">Supplier Changed Date</th>
                    <th class="text-center">Appl Changed Date</th>
                    <th class="text-center">MOF Exp Date</th>
                    <th class="text-center">Action</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $row)
                <tr>
                    <td class="text-center">{{$row->company_name}}</td>    
                    <td class="text-center"><a href="{{url('/find/mofno/')}}/{{ $row->mof_no }}" target="_blank">{{ $row->mof_no }}</a></td>
                    <td class="text-center">{{$row->record_status}}</td>
                    <td class="text-center">{{$row->supplier_changed_date}}</td>
                    <td class="text-center">{{$row->appl_changed_date}}</td>
                    <td class="text-center">{{$row->exp_date}}</td>
                    <td class="text-center"><a href="{{url('find/supplier')}}/{{$row->latest_appl_id }}/{{ $row->supplier_id }}" class="btn btn-primary" 
                        target="_blank">Fix</a></td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



