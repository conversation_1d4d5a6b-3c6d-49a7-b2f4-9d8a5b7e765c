@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/it_support/server/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li>
                <a href="{{ url('/it_support/server/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/server/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>

    <div class="block">
        <div class="block-options">
            <label class="col-md-2 text-right" for="day">{{ $day }}</label>
            <label class="col-md-2 text-right" id ="date" for="date"> {{ $dateNote }} </label>
        </div>

        <div class="text-center" style="padding: 5px;">
            <ul class="nav nav-tabs" data-toggle="tabs">
                <li id="kvdcId_M" name="kvdcId_M"><a href="#kvdc">KVDC</a></li>
            </ul>
        </div>

        <form id ="it_server_checklist" action="{{ url('/it_support/server/checklist') }}" method="post">
            {{ csrf_field() }}
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            <div class="table-options" id="clearfix_M">
                <div class="btn-group btn-group-sm pull-left kvdc_M" data-toggle="buttons" style="display:none">
                    @if (isset($menuKvdc))
                        @foreach ($menuKvdc as $key => $list)
                            <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                title="{{ $list->esc_group }}" group ="{{ $list->esc_group }}" shift="M"
                                location="kvdc">
                                <input type="radio" name="menu" value=""> {{ $list->esc_group }}
                            </label>
                        @endforeach
                    @endif
                </div>
            </div>

            <div class="text-center spinner-loading" style="padding: 20px;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>

            <div class="">
                <table id="server_datatable" class="table table-vcenter table-condensed table-bordered table-responsive"></table>
            </div>

            @if ($listNote == null || $listNote[0]->ack_status === null)
                <div class="text-center">
                    <button type="submit" style="background-color: #414770; color: white"
                        class="btn btn btn-primary editSave">Update</button>
                </div>
            @endif

        </form>
    </div>

    <div class="block panel-heading">@include('it_support.page_status', ['page' => 'server'])</div>

@endsection
@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        function selectAll(ele) {
            var checkboxes = document.querySelectorAll('#checkbox_for_check');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'checkbox') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'checkbox') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        $(document).ready(function() {
            $('#kvdcId_M').click();
            $('.kvdc_M').show();
            $('#kvdcId_M').css({
                'color': 'white',
                'background-color': '#414770',
                'font-size': '150%'
            });
            $('.kvdc_M').click();
            $('.kvdc_M').addClass("active");
            $('#menu_button').click();
            $('#menu_button').addClass("active");
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {
            } else {
                $('#remarks').val("{{ $listNote[0]->ack_remarks ?? '' }}");
            }
            var ackStatus = "{{ $listNote[0]->ack_status ?? '' }}";
            if (ackStatus === 'Pending Endorsement' || ackStatus === 'Completed') {
                $('.editSave').hide();
            } else {
                $('.editSave').show();
            }
        });

        function changeMenu(a) {
            $('.editSave').show();
            let
                group = $(a).attr('group');
            let
                shift = $(a).attr('shift');
            let
                location = $(a).attr('location');
            var dateSelection = null;

            if ($.fn.DataTable.isDataTable('#server_datatable')) {
                $('#server_datatable').DataTable().destroy();
            }

            $('#server_datatable thead').empty();
            $('#server_datatable tbody').empty();

            $('.spinner-loading').show();

            $.ajax({
                type: "GET",
                url: "/it_support/server/find_by/" + group + "/" + dateSelection + "/" + shift + "/" + location,
            }).done(function(data) {
                $('.spinner-loading').hide();
                $('#server_datatable').html(data).fadeIn();
                Tabledatatable = $('#server_datatable').DataTable({
                    ordering: false,
                    lengthMenu: [
                        [20, 30, 50, -1],
                        [20, 30, 50, 'All']
                    ]
                });

            })
        }
        $(".btn-group").on("change", "#menu_button", function(e) {
            changeMenu(this)
        });
    </script>
@endsection
