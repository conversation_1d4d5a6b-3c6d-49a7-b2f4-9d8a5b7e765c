<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmManagement;

use App\Http\Controllers\Controller;
use App\Services\EpNotify\EpNotifyService;
use Illuminate\Http\Request;
use GuzzleHttp\Client;
use Log;
use DB;
use Exception;
use App\EpSupportActionLog;

class CrmManagementController extends Controller
{

    public static $db_connection_crm_ep = 'mysql_crm';
    public static $db_connection_crm_ssm = 'mysql_crm_ssm';
    public static $db_connection_crm_casb = 'mysql_crm_casb';
    public static $db_connection_crm_jbal = 'mysql_crm_jbal';
    public static $action_name = 'Update-Case';
    public static $action_type = 'CRM';
    public static $crm_ep = 'CRM eP';
    public static $crm_casb = 'CRM Mytv/Altel';
    public static $crm_ssm = 'CRM SSM';
    public static $crm_jbal = 'CRM JBAL';

    public static function EpNotifyService()
    {
        return new EpNotifyService;
    }

    public function __construct()
    {
        $this->middleware('auth');
    }

    public function main(Request $request)
    {

        $type_crm = $request->type_crm;
        $change_type = $request->change_type;
        $value = $request->value; //can be case number or user email address
        $module = $request->modules;
        $user_category = $request->user_category;

        $arrayData = array(
            self::$crm_ep,
            self::$crm_jbal,
            self::$crm_casb,
            self::$crm_ssm
        );

        $arrayModule = array(
            'Cases',
            'Users'
        );

        $arrayUserCategory = array(
            'Username',
            'Name',
            'Email'
        );
        $selectedUserCategory = $user_category ? $user_category : 'Email';

        $result = null;
        $dbConnection = null;
        $listChannel = null;
        $listState = null;
        $listStatus = null;
        $listType = null;
        $listIncidentType = null;
        $listCategory1 = null;
        $listCategory2 = null;
        $listCategory3 = null;
        $listCategory4 = null;
        $listCategory5 = null;
        $listCategory6 = null;
        $users = null;

        if (($module == 'Users' && $value != '') || ($type_crm != '' && $value != '')) {
            if (in_array($module, $arrayModule)) {
                switch ($module) {
                    case 'Cases':
                        switch ($type_crm) {
                            case self::$crm_ep:
                                $dbConnection = self::$db_connection_crm_ep;
                                $result = self::getDetailCase($type_crm, $value, $dbConnection);
                                $listChannel = self::getDetailLookupCRMByType('cdc_contact_mode_list', $dbConnection);
                                $listState = self::getDetailLookupCRMByType('case_state_dom', $dbConnection);
                                $listStatus = self::getDetailLookupCRMByType('case_status_dom', $dbConnection);
                                $listType = self::getDetailLookupCRMByType('request_type_list', $dbConnection);
                                $listIncidentType = self::getDetailLookupCRMByType('incident_service_type_list', $dbConnection);
                                $listCategory1 = self::getDetailLookupCRMByType('category_list', $dbConnection);
                                $listCategory2 = self::getDetailLookupCRMByType('cdc_sub_category_list', $dbConnection);
                                $listCategory3 = self::getDetailLookupCRMByType('cdc_sub_category_2_list', $dbConnection);
                                break;
                            case self::$crm_ssm:
                                $dbConnection = self::$db_connection_crm_ssm;
                                $result = self::getDetailCase($type_crm, $value, $dbConnection);
                                $listChannel = self::getDetailLookupCRMByType('channel_list', $dbConnection);
                                $listState = self::getDetailLookupCRMByType('case_state_dom', $dbConnection);
                                $listStatus = self::getDetailLookupCRMByType('case_status_dom', $dbConnection);
                                $listType = self::getDetailLookupCRMByType('case_type_dom', $dbConnection);
                                $listCategory1 = self::getDetailLookupCRMByType('category_list', $dbConnection);
                                $listCategory2 = self::getDetailLookupCRMByType('category_2_list', $dbConnection);
                                $listCategory3 = self::getDetailLookupCRMByType('category_3_list', $dbConnection);
                                $listCategory4 = self::getDetailLookupCRMByType('category_4_list', $dbConnection);
                                $listCategory5 = self::getDetailLookupCRMByType('category_5_list', $dbConnection);
                                $listCategory6 = self::getDetailLookupCRMByType('category_6_list', $dbConnection);
                                break;
                            case self::$crm_casb:
                                $result = self::getDetailCase($type_crm, $value, self::$db_connection_crm_casb);
                                $listChannel = self::getDetailLookupCRMByType('contact_mode_list', self::$db_connection_crm_casb);
                                $listState = self::getDetailLookupCRMByType('case_state_dom', self::$db_connection_crm_casb);
                                $listStatus = self::getDetailLookupCRMByType('case_status_dom', self::$db_connection_crm_casb);
                                break;
                            case self::$crm_jbal:
                                $result = self::getDetailCase($type_crm, $value, self::$db_connection_crm_jbal);
                                $listChannel = self::getDetailLookupCRMByType('channel_list', self::$db_connection_crm_jbal);
                                break;
                        }
                        break;
                    case 'Users':
                        $client = new Client([
                            'headers' => [
                                'Authorization' => 'Bearer ' . env('CRM_INTEGRATION_API_KEY'),
                            ],
                        ]);

                        switch ($user_category) {
                            case 'Username':
                                $apiUrl = env('CRM_INTEGRATION') . "/api/crm/user/username/$value";
                                break;
                            case 'Name':
                                $apiUrl = env('CRM_INTEGRATION') . "/api/crm/user/name/$value";
                                break;
                            case 'Email':
                                $apiUrl = env('CRM_INTEGRATION') . "/api/crm/user/email/$value";
                                break;
                            default:
                                $value = 'Invalid user category';
                                break;
                        }

                        if (isset($apiUrl)) {
                            $response = $client->get($apiUrl);
                            $users = json_decode($response->getBody(), true);
                        }
                        break;
                }
            }
        }
        return view('crm.crm_management', [
            'crm_type' => $type_crm,
            'change_type' => $change_type,
            'array_data' => $arrayData,
            'arrayUserCategory' => $arrayUserCategory,
            'selectedUserCategory' => $selectedUserCategory,
            'value' => $value,
            'result' => $result,
            'listState' => $listState,
            'listStatus' => $listStatus,
            'listChannel' => $listChannel,
            'listType' => $listType,
            'listIncidentType' => $listIncidentType,
            'listCategory1' => $listCategory1,
            'listCategory2' => $listCategory2,
            'listCategory3' => $listCategory3,
            'listCategory4' => $listCategory4,
            'listCategory5' => $listCategory5,
            'listCategory6' => $listCategory6,
            'module' => $module,
            'arrayModule' => $arrayModule,
            'users' => $users
        ]);
    }

    public function updateCrm(Request $request)
    {
        $data = collect([]);
        $dbConnection = null;
        $typeCrm = $request->typeCrm;
        $caseNumber = $request->caseNumber;
        $updateCase = null;
        $actionStatus = 'Failed';

        if ($typeCrm != '' && $caseNumber != '') {
            switch ($typeCrm) {
                case self::$crm_ep:
                    $dbConnection = self::$db_connection_crm_ep;
                    break;
                case self::$crm_ssm:
                    $dbConnection = self::$db_connection_crm_ssm;
                    break;
                case self::$crm_casb:
                    $dbConnection = self::$db_connection_crm_casb;
                    break;
                case self::$crm_jbal:
                    $dbConnection = self::$db_connection_crm_jbal;
                    break;
                default:
            }
        }
        if ($dbConnection != null) {
            $dataBefore = self::getDetailCase($typeCrm, $caseNumber, $dbConnection);
            $updateCase = self::updateCase($typeCrm, $caseNumber, $dbConnection, $request);
            $dataAfter = self::getDetailCase($typeCrm, $caseNumber, $dbConnection);

            if ($updateCase != null) {
                if ($updateCase == 1) {
                    $actionStatus = 'Success';
                } else {
                    $data->put("msg", $updateCase);
                }
            }
            $actionData = collect([]);
            $actionData->put('data_before', $dataBefore);
            $actionData->put('data_after', $dataAfter);
            $data->put("status", $actionStatus);
            EpSupportActionLog::saveActionLog(self::$action_name, self::$action_type, $actionData, $actionData, $actionStatus);
        }

        return $data;
    }

    public function getDetailLookupCRMByType($type, $dbConnection)
    {
        $query = DB::connection($dbConnection)->table('cstm_list_app');
        $query->where('type_code', $type);
        $query->where('deleted', 0);
        $query->where('status', 1);
        $query->whereNotIn('value_code', ['']);
        $query->select('value_code', 'value_name');
        $query->orderBy('value_name', 'asc');
        $data = $query->get();
        return $data;
    }

    public function getDetailCase($type_crm, $caseNumber, $dbConnection)
    {
        if ($dbConnection != null) {
            $query = DB::connection($dbConnection)->table('cases');
            if ($type_crm == self::$crm_ep) {
                $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
                $query->where('case_number', $caseNumber);
                $query->select('cases.case_number', 'cases_cstm.contact_mode_c as channel', 'cases.state', 'cases.status', 'cases.redmine_number');
                $query->addSelect('cases_cstm.request_type_c', 'cases_cstm.incident_service_type_c', 'cases_cstm.category_c', 'cases_cstm.sub_category_c', 'cases_cstm.sub_category_2_c', 'cases.cptpp_flag');
            } else if ($type_crm == self::$crm_jbal) {
                $query->where('case_number', $caseNumber);
                $query->select('cases.case_number', 'cases.channel as channel', 'cases.state', 'cases.status');
                $query->addSelect('cases.type', 'cases.category', 'cases.category_2');
            } else if ($type_crm == self::$crm_casb) {
                $query->where('case_number', $caseNumber);
                $query->select('cases.case_number', 'cases.contact_mode as channel', 'cases.state', 'cases.status');
                $query->addSelect('cases.category', 'cases.sub_category', 'cases.sub_sub_category', 'cases.sub_sub_sub_category');
            } else if ($type_crm == self::$crm_ssm) {
                $query->where('case_number', $caseNumber);
                $query->select('cases.case_number', 'cases.channel as channel', 'cases.state', 'cases.status');
                $query->addSelect('cases.type', 'cases.category', 'cases.category_2', 'cases.category_3', 'cases.category_4', 'cases.category_5', 'cases.category_6');
            }
            $query->where('case_number', $caseNumber);
            $query->addSelect(DB::raw("CONVERT_TZ(cases.date_entered,'+00:00','+08:00') as case_created"));
            $query->addSelect(DB::raw("CONVERT_TZ(cases.date_modified,'+00:00','+08:00') as case_modified"));
            $result = $query->first();
            return $result;
        }
    }

    public function updateCase($typeCrm, $caseNumber, $dbConnection, $request)
    {
        $state = $request->state;
        $status = $request->status;
        $channel = $request->channel;
        $type = $request->type;
        $incidentType = $request->incidentType;
        $category1 = $request->category1;
        $category2 = $request->category2;
        $category3 = $request->category3;
        $category4 = $request->category4;
        $category5 = $request->category5;
        $category6 = $request->category6;
        $redmineNumber = $request->redmineNumber;
        $cptpp = $request->cptpp;
        try {
            $query = DB::connection($dbConnection)->table('cases');
            if ($typeCrm == self::$crm_ep) {
                $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
                $query->where('cases.case_number', $caseNumber);
                $query->update([
                    'state' => $state,
                    'status' => $status,
                    'contact_mode_c' => $channel,
                    'request_type_c' => $type,
                    'incident_service_type_c' => $incidentType,
                    'category_c' => $category1,
                    'sub_category_c' => $category2,
                    'sub_category_2_c' => $category3,
                    'redmine_number' => $redmineNumber,
                    'cptpp_flag' => $cptpp
                ]);
                if ($redmineNumber != '') {
                    $queryTask = DB::connection($dbConnection)->table('tasks');
                    $queryTask->join('cases', 'cases.id', '=', 'tasks.parent_id');
                    $queryTask->where('cases.case_number', $caseNumber);
                    $queryTask->update([
                        'case_redmine_number' => $redmineNumber,
                    ]);
                }
            } else if ($typeCrm == self::$crm_jbal) {
                $query->where('cases.case_number', $caseNumber);
                $query->update([
                    'state' => $state,
                    'status' => $status,
                    'channel' => $channel
                ]);
            } else if ($typeCrm == self::$crm_casb) {
                $query->where('cases.case_number', $caseNumber);
                $query->update([
                    'state' => $state,
                    'status' => $status,
                    'contact_mode' => $channel
                ]);
            } else if ($typeCrm == self::$crm_ssm) {
                $query->where('cases.case_number', $caseNumber);
                $query->update([
                    'state' => $state,
                    'status' => $status,
                    'channel' => $channel,
                    'type' => $type,
                    'category' => $category1,
                    'category_2' => $category2,
                    'category_3' => $category3,
                    'category_4' => $category4,
                    'category_5' => $category5,
                    'category_6' => $category6
                ]);
            }



            if ($status == 'Open_New') {
                //check task.if exist, delete
                $task = DB::connection($dbConnection)->table('tasks')
                    ->join('cases', 'cases.id', '=', 'tasks.parent_id')
                    ->where('cases.case_number', $caseNumber)
                    ->select('tasks.id as taskid')
                    ->get();

                if (count($task) > 0) {
                    foreach ($task as $row) {
                        DB::connection($dbConnection)->table('tasks')
                            ->where('id', $row->taskid)
                            ->update([
                                'deleted' => 1
                            ]);
                    }
                }
            }
            $result = 1;
        } catch (Exception $ex) {
            Log::info($ex->getMessage());
            $result = $ex->getMessage();
        }
        return $result;
    }

    public function deactivateUser(Request $request)
    {
        Log::info('deactivateUser' . print_r($request->all(), true));

        $actionLog = new EpSupportActionLog;
        $actionLog->action_name = 'Deactivate-User';
        $actionLog->action_type = 'CRM';
        $actionLog->action_data = json_encode($request->all());
        $actionLog->created_by = auth()->user()->user_name;

        $client = new Client([
            'headers' => [
                'Authorization' => 'Bearer ' . env('CRM_INTEGRATION_API_KEY'),
            ],
        ]);
        Log::info('deactivateUser' . print_r($client, true));

        $apiUrl = env('CRM_INTEGRATION') . "/api/crm/user/deactivate-user";

        // Get the request data, excluding the _token
        $requestData = $request->except('_token');
        
        // Include the authenticated user's name in the request data
        $requestData['authUserName'] = auth()->user()->user_name;

        // Send the request data as a body to the apiUrl
        $response = $client->post($apiUrl, [
            'json' => $requestData
        ]);

        // Get the response body and decode it as an array
        $responseBody = json_decode($response->getBody(), true);

        Log::info('deactivateUser Response' . print_r($responseBody, true));

        // Check the success value in the response
        if (isset($responseBody['success']) && $responseBody['success']) {
            // The request was successful
            // Redirect back or do something else
            $actionLog->action_status = 'Success';
            $actionLog->save();

            session()->flash('deactivateUserResult', true);
            return redirect()->back();
        } else {
            // The request failed
            // Handle the failure
            $actionLog->action_status = 'Failed';
            $actionLog->save();
            return redirect()->back()->withErrors(['msg' => 'Deactivation failed']);
        }
    }
}
