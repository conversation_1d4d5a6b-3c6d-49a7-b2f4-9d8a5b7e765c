<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmResendSoftcert {

    use SupplierService;

    public static function triggerFailedOSBRequestCertificate(){
        MigrateUtils::logDump(__METHOD__.' entering..');
        $listSoftcert = DB::connection('oracle_nextgen_rpt')->select("SELECT  DISTINCT service_code,remarks_1 AS softcert_request_id, remarks_3 AS ic_no FROM ngep_osb.osb_logging o WHERE service_code in ('SPK-020', 'SPK-030', 'SPK-040', 'SPK-050', 'SPK-060') 
                AND trans_type = 'JmsIns'
                -- AND NOT EXISTS (SELECT 1 FROM osb_logging f WHERE f.TRANS_ID = o.TRANS_ID  and f.TRANS_TYPE = 'OBRes' and service_flow = 'JMS')
                AND NOT EXISTS (
                    SELECT 1 FROM ngep_osb.osb_logging f WHERE f.service_code in ('SPK-020', 'SPK-030', 'SPK-040', 'SPK-050', 'SPK-060')  AND f.remarks_3  = o.REMARKS_3 AND trans_type = 'JmsIns'
                    AND EXISTS (SELECT 1 FROM osb_logging x WHERE x.TRANS_ID = f.TRANS_ID  and x.TRANS_TYPE = 'OBRes' and service_flow = 'JMS' AND status = 'S')
                )
                AND EXISTS (SELECT 1 FROM ngep_osb.osb_logging x WHERE x.TRANS_ID = o.TRANS_ID  and x.TRANS_TYPE = 'OBRes' and service_flow = 'JMS' AND status_code in ('111','110') )
                -- add this to find issue ion TG -- AND EXISTS (SELECT 1 FROM ngep_osb.osb_logging x WHERE x.TRANS_ID = o.TRANS_ID  and x.TRANS_TYPE = 'OBRes' and service_flow = 'JMS' AND status_desc like '%connect to remote MySQL server for client connected to %') 
                ");
        $clct = collect($listSoftcert);
        MigrateUtils::logDump(__METHOD__.' total list : '.$clct->count());
        foreach($clct as $obj){
            MigrateUtils::logDump(__METHOD__.' Find ICNO: '.$obj->ic_no. ' RequestID: '.$obj->softcert_request_id);
            self::triggerSendSoftcertRequest($obj->ic_no,$obj->softcert_request_id);
        }
    }

    public static function triggerSendSoftcertRequest($icNo,$softcertRequestId){
        MigrateUtils::logDump(__METHOD__ .' entering..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmResendSoftcert();
        $pers = $thisCls->getUserPersonnelBySoftcertRequest($icNo,$softcertRequestId);
        MigrateUtils::logDump(' Info Result '.json_encode($pers));
        
        if($pers != null){
            $personnelId = $pers->personnel_id;
            $softcertRequestId  = $pers->softcert_request_id;
            $softcertProvider = $pers->softcert_provider;
            
            $res = $thisCls->sendOsbSoftcertRequest($personnelId ,$softcertRequestId,$softcertProvider);
            MigrateUtils::logDump(' Trigger Result '.json_encode($res));

        }else{
            MigrateUtils::logDump(__METHOD__ .' Record not found based on parameter given..');
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    public static function sendSwitchTgToDgSoftcertReq($icNo,$softcertRequestId){
        MigrateUtils::logDump(__METHOD__ .' entering..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmResendSoftcert();
        $pers = $thisCls->getUserPersonnelBySoftcertRequest($icNo,$softcertRequestId);
        //MigrateUtils::logDump(' Info Result '.json_encode($pers));
        
        if($pers != null && $pers->softcert_provider == 'TG' ){

            // Checking record issue only has 1 ep_no only
            /*
            $listCheckEpno = DB::connection('oracle_nextgen_rpt')->select("SELECT count (*) AS total_ep_no FROM (
                SELECT orn,prn, count(*) FROM PM_DIGI_CERT  WHERE prn = ? 
                GROUP BY orn,prn
                )",array($icNo)); 
            $checkHasEpno = $listCheckEpno[0]->total_ep_no;
            if($checkHasEpno <= 1){
            */
                DB::connection('oracle_nextgen_fullgrant')->table('SM_SOFTCERT_REQUEST')
                    ->where('SOFTCERT_REQUEST_ID', $softcertRequestId)
                    ->update([ 'softcert_provider' => 'DG']);
                sleep(4);

                $perD = $thisCls->getUserPersonnelBySoftcertRequest($icNo,$softcertRequestId);
                if($perD->softcert_provider == 'DG'){
                    $personnelId = $perD->personnel_id;
                    $softcertRequestId  = $perD->softcert_request_id;
                    $softcertProvider = $perD->softcert_provider;
                    
                    $res = $thisCls->sendOsbSoftcertRequest($personnelId ,$softcertRequestId,$softcertProvider);
                    MigrateUtils::logDump(' Trigger Result '.json_encode($res));
                    
                }
            /*
            }else{
                MigrateUtils::logDump(__METHOD__ .' check this user belong to company (issued softcert) more than one: '.$checkHasEpno. ' >> '.json_encode($listCheckEpno));
            }
            */
        }else{
            MigrateUtils::logDump(__METHOD__ .' Record not found based on parameter given ' );
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    protected function sendOsbSoftcertRequest($personnelId ,$softcertRequestId,$softcertProvider){
        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-sm/request-certificate?personnel_id=$personnelId&softcert_request_id=$softcertRequestId&softcert_provider=$softcertProvider&token=$token";
            MigrateUtils::logDump(__METHOD__ .' url send : '.$url);
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect > '.$ex->getMessage());
        }

    }

    public static function triggerUpdateFailedTGToDG(){
        $listFailed =  DB::connection('oracle_nextgen_rpt')->select("SELECT distinct r.trans_id,r.service_name,r.target_system ,
        o.REMARKS_1 AS request_id, o.REMARKS_3 AS ic_no
        FROM ngep_osb.OSB_RETRY_DTL r , ngep_osb.osb_logging o 
        WHERE r.TRANS_ID  = o.TRANS_ID AND o.trans_type  = 'JmsIns'  ");
        foreach($listFailed as $obj){
            $reqObj =  DB::connection('oracle_nextgen_fullgrant')->table('SM_SOFTCERT_REQUEST')->where('SOFTCERT_REQUEST_ID', $obj->request_id)->first();
            if($reqObj != null && $reqObj->softcert_provider == "TG"){
                MigrateUtils::logDump(__METHOD__ .' need to update DG');
                MigrateUtils::logDump(__METHOD__ .' '.json_encode($obj));
                DB::connection('oracle_nextgen_fullgrant')->table('SM_SOFTCERT_REQUEST')->where('SOFTCERT_REQUEST_ID', $obj->request_id)->update([ 'softcert_provider' => 'DG']);
                sleep(10);
                self::triggerSendSoftcertRequest($obj->ic_no,$obj->request_id);
                DB::connection('oracle_nextgen_fullgrant')->table('osb_retry_dtl')->where('trans_id',$obj->trans_id)->delete();
                MigrateUtils::logDump(__METHOD__ .' completed set DG and resed to DG provider.');
          
            }else{
                MigrateUtils::logDump(__METHOD__ .' just delete retry data. Data already set to DG');
                DB::connection('oracle_nextgen_fullgrant')->table('osb_retry_dtl')->where('trans_id',$obj->trans_id)->delete();
            }
            
        }

       
    }
    
}
