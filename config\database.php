<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Database Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the database connections below you wish
    | to use as your default connection for all database work. Of course
    | you may use many connections at once using the Database library.
    |
    */

    'default' => env('DB_CONNECTION', 'mysql'),

    /*
    |--------------------------------------------------------------------------
    | Database Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the database connections setup for your application.
    | Of course, examples of configuring each database platform that is
    | supported by Laravel is shown below to make development simple.
    |
    |
    | All database work in Laravel is done through the PHP PDO facilities
    | so make sure you have the driver for your particular database of
    | choice installed on your machine before you begin development.
    |
    */

    'connections' => [

        'sqlite' => [
            'driver' => 'sqlite',
            'database' => env('DB_DATABASE', database_path('database.sqlite')),
            'prefix' => '',
        ],

        'mysql' => [
            'driver' => 'mysql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '3306'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'unix_socket' => env('DB_SOCKET', ''),
            'charset' => 'utf8mb4',
            'collation' => 'utf8mb4_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_prod_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_PROD_SUPPORT_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_PROD_SUPPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_PROD_SUPPORT_DATABASE', 'ep_prod_support'),
            'username' => env('DB_MYSQL_EP_PROD_SUPPORT_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_PROD_SUPPORT_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_SUPPORT_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_SUPPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_SUPPORT_DATABASE', 'ep_support'),
            'username' => env('DB_MYSQL_EP_SUPPORT_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_SUPPORT_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_logtrace' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_LOGTRACE_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_LOGTRACE_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_LOGTRACE_DATABASE', 'ep_logtrace'),
            'username' => env('DB_MYSQL_EP_LOGTRACE_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_LOGTRACE_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_notify' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_NOTIFY_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_NOTIFY_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_NOTIFY_DATABASE', 'ep_logtrace'),
            'username' => env('DB_MYSQL_EP_NOTIFY_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_NOTIFY_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],


        'mysql_ep_it_support' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_IT_SUPPORT_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_IT_SUPPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_IT_SUPPORT_DATABASE', 'ep_it_support'),
            'username' => env('DB_MYSQL_EP_IT_SUPPORT_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_IT_SUPPORT_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_report' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_REPORT_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_REPORT_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_REPORT_DATABASE', 'ep_report'),
            'username' => env('DB_MYSQL_EP_REPORT_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_REPORT_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_cdc' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_CDC_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_CDC_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_CDC_DATABASE', 'ep_cdc'),
            'username' => env('DB_MYSQL_EP_CDC_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_CDC_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_crm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_HOST', '***************'),
            'port' => env('DB_MYSQL_CRM_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_DATABASE', 'cdccrm'),
            'username' => env('DB_MYSQL_CRM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_PASSWORD', 'cDccRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_poms' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_POMS_HOST', '*************'),
            'port' => env('DB_MYSQL_POMS_PORT', '3306'),
            'database' => env('DB_MYSQL_POMS_DATABASE', 'cdc_poms'),
            'username' => env('DB_MYSQL_POMS_USERNAME', 'poms_user'),
            'password' => env('DB_MYSQL_POMS_PASSWORD', 'cDcPoms@2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_ep_kontrak' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_EP_KONTRAK_HOST', 'localhost'),
            'port' => env('DB_MYSQL_EP_KONTRAK_PORT', '3306'),
            'database' => env('DB_MYSQL_EP_KONTRAK_DATABASE', 'contract'),
            'username' => env('DB_MYSQL_EP_KONTRAK_USERNAME', 'root'),
            'password' => env('DB_MYSQL_EP_KONTRAK_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'mysql_cms' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CMS_HOST', '*************'),
            'port' => env('DB_MYSQL_CMS_PORT', '3306'),
            'database' => env('DB_MYSQL_CMS_DATABASE', 'cdccms'),
            'username' => env('DB_MYSQL_CMS_USERNAME', 'epcm@suser'),
            'password' => env('DB_MYSQL_CMS_PASSWORD', 'cDc-2019'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'STRICT_ALL_TABLES',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
        
        'mysql_crm_ssm' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_SSM_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_SSM_PORT', '3306'),
            'database' => env('DB_MYSQL_SSM_CRM_DATABASE', 'crm_ssm'),
            'username' => env('DB_MYSQL_SSM_CRM_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_SSM_CRM_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
        
        'mysql_crm_casb' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_CASB_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_CASB_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_CASB_DATABASE', 'casbcrm'),
            'username' => env('DB_MYSQL_CRM_CASB_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_CASB_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
        
        'mysql_crm_jbal' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_JBAL_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_JBAL_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_JBAL_DATABASE', 'casbcrm_jbal'),
            'username' => env('DB_MYSQL_CRM_JBAL_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_JBAL_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
        
        'mysql_crm_notify' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_NOTIFY_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_NOTIFY_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_NOTIFY_DATABASE', 'crm_notify'),
            'username' => env('DB_MYSQL_CRM_NOTIFY_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_NOTIFY_PASSWORD', 'CasbCrm@2022'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],

        'pgsql' => [
            'driver' => 'pgsql',
            'host' => env('DB_HOST', '127.0.0.1'),
            'port' => env('DB_PORT', '5432'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'schema' => 'public',
            'sslmode' => 'prefer',
        ],

        'pgsql_dynatrace' => [
            'driver' => 'pgsql',
            'host' => env('DB_DYNATRACE_HOST', '127.0.0.1'),
            'port' => env('DB_DYNATRACE_PORT', '5432'),
            'database' => env('DB_DYNATRACE_DATABASE', 'forge'),
            'username' => env('DB_DYNATRACE_USERNAME', 'forge'),
            'password' => env('DB_DYNATRACE_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
            'schema' => 'public',
            'sslmode' => 'prefer',
            'default' => 'postgres'
        ],

        'oracle_nextgen' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_TNS', ''),
            'host'          => env('DB_NEXTGEN_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_DATABASE', 'uatapp'),
            'service_name'  => env('DB_NEXTGEN_DATABASE', 'uatapp'),
            'username'      => env('DB_NEXTGEN_USERNAME', 'ngep_uat'),
            'password'      => env('DB_NEXTGEN_PASSWORD', 'ng3p_u4t'),
            'charset'       => env('DB_NEXTGEN_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_PREFIX', ''),

        ],

        'oracle_nextgen_rpt' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_RPT_TNS', ''),
            'host'          => env('DB_NEXTGEN_RPT_HOST', 'rac-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_RPT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'service_name'  => env('DB_NEXTGEN_RPT_DATABASE', 'uatapp'),
            'username'      => env('DB_NEXTGEN_RPT_USERNAME', 'ngep_uat'),
            'password'      => env('DB_NEXTGEN_RPT_PASSWORD', 'ng3p_u4t'),
            'charset'       => env('DB_NEXTGEN_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_RPT_PREFIX', ''),

        ],

        'oracle_nextgen_fullgrant' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_FULLGRANT_TNS', ''),
            'host'          => env('DB_NEXTGEN_FULLGRANT_HOST', 'racrpt-cluster-scan.eperolehan.com.my'), // stg
            'port'          => env('DB_NEXTGEN_FULLGRANT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_FULLGRANT_DATABASE', 'pdsapp'),
            'service_name'  => env('DB_NEXTGEN_FULLGRANT_DATABASE', 'pdsapp'),
            'username'      => env('DB_NEXTGEN_FULLGRANT_USERNAME', 'ngep_pds'),
            'password'      => env('DB_NEXTGEN_FULLGRANT_PASSWORD', 'ng3p_pd5'),
            'charset'       => env('DB_NEXTGEN_FULLGRANT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_FULLGRANT_PREFIX', ''),

        ],

        //'tns' => '(DESCRIPTION = (ADDRESS_LIST = (ADDRESS = (PROTOCOL = TCP)(HOST = ***************)(PORT = 1521))) (CONNECT_DATA = (SERVICE_NAME = orcl)))',
        'oracle_nextgen_soa_fullgrant' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_SOA_FULLGRANT_TNS', ''),
            'host'          => env('DB_NEXTGEN_SOA_FULLGRANT_HOST', '*************'), // stg
            'port'          => env('DB_NEXTGEN_SOA_FULLGRANT_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_SOA_FULLGRANT_DATABASE', 'sitsoa'),
            'service_name'  => env('DB_NEXTGEN_SOA_FULLGRANT_DATABASE', 'sitsoa'), 
            'username'      => env('DB_NEXTGEN_SOA_FULLGRANT_USERNAME', 'sit01_soainfra'),
            'password'      => env('DB_NEXTGEN_SOA_FULLGRANT_PASSWORD', 'welcome1'),
            'charset'       => env('DB_NEXTGEN_SOA_FULLGRANT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_SOA_FULLGRANT_PREFIX', ''),

        ],

        'oracle_nextgen_arc' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_ARCHIVE_TNS', ''),
            'host'          => env('DB_NEXTGEN_ARCHIVE_HOST', '*************'), // eP Archive
            'port'          => env('DB_NEXTGEN_ARCHIVE_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_ARCHIVE_DATABASE', 'ngepdms1'),
            'service_name'  => env('DB_NEXTGEN_ARCHIVE_DATABASE', 'ngepdms1'),
            'username'      => env('DB_NEXTGEN_ARCHIVE_USERNAME', 'NGEP_READ'),
            'password'      => env('DB_NEXTGEN_ARCHIVE_PASSWORD', 'ng3p_r34d'),
            'charset'       => env('DB_NEXTGEN_ARCHIVE_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_ARCHIVE_PREFIX', ''),

        ],

        'oracle_nextgen_drc' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_DRC_TNS', ''),
            'host'          => env('DB_NEXTGEN_DRC_HOST', '**************'), // eP Archive
            'port'          => env('DB_NEXTGEN_DRC_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_DRC_DATABASE', 'ngepdbsstb'),
            'service_name'  => env('DB_NEXTGEN_DRC_DATABASE', 'ngepdbsstb'),
            'username'      => env('DB_NEXTGEN_DRC_USERNAME', 'ngep_prd'),
            'password'      => env('DB_NEXTGEN_DRC_PASSWORD', 'ng3p_prd'),
            'charset'       => env('DB_NEXTGEN_DRC_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_DRC_PREFIX', ''),

        ],

        'oracle_bpm_rpt' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_BPM_RPT_TNS', ''),
            'host'          => env('DB_BPM_RPT_HOST', 'racrpt-cluster-scan.eperolehan.com.my'), // pds
            'port'          => env('DB_BPM_RPT_PORT', '1521'),
            'database'      => env('DB_BPM_RPT_DATABASE', 'pdssoa'),
            'service_name'  => env('DB_BPM_RPT_DATABASE', 'pdssoa'),
            'username'      => env('DB_BPM_RPT_USERNAME', 'pdssoa_soainfra'),
            'password'      => env('DB_BPM_RPT_PASSWORD', 'pd5504'),
            'charset'       => env('DB_BPM_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_BPM_RPT_PREFIX', ''),

        ],


        // oam readonly
        'oracle_oam' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_OAM_TNS', ''),
            'host'          => env('DB_NEXTGEN_OAM_HOST', '**************'), // ************** //racprd-cluster-scan.eperolehan.com.my
            'port'          => env('DB_NEXTGEN_OAM_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_OAM_DATABASE', 'ngepdb'),
            'service_name'  => env('DB_NEXTGEN_OAM_DATABASE', 'ngepdb'),
            'username'      => env('DB_NEXTGEN_OAM_USERNAME', 'epss'),
            'password'      => env('DB_NEXTGEN_OAM_PASSWORD', 'ep55oam'),
            'charset'       => env('DB_NEXTGEN_OAM_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_OAM_PREFIX', ''),

        ],

        // sso readonly
        'oracle_sso' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_NEXTGEN_SSO_TNS', ''),
            'host'          => env('DB_NEXTGEN_SSO_HOST', '**************'), 
            'port'          => env('DB_NEXTGEN_SSO_PORT', '1521'),
            'database'      => env('DB_NEXTGEN_SSO_DATABASE', 'ngepdbstb'),
            'service_name'  => env('DB_NEXTGEN_SSO_DATABASE', 'ngepdbstb'),
            'username'      => env('DB_NEXTGEN_SSO_USERNAME', 'ngep_read'),
            'password'      => env('DB_NEXTGEN_SSO_PASSWORD', 'ng3p_r34d'),
            'charset'       => env('DB_NEXTGEN_SSO_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_NEXTGEN_SSO_PREFIX', ''),

        ],

        'oracle_ngepsoa_read' => [
            'driver'        => 'oracle',
            'tns'           => env('DB_SOAN_RPT_TNS', ''),
            'host'          => env('DB_SOA_RPT_HOST', '**************'), //racprd-cluster-scan.eperolehan.com.my
            'port'          => env('DB_SOA_RPT_PORT', '1521'),
            'database'      => env('DB_SOA_RPT_DATABASE', 'ngepsoa'),
            'service_name'  => env('DB_SOA_RPT_DATABASE', 'ngepsoa'),
            'username'      => env('DB_SOA_RPT_USERNAME', 'ngep_read'),
            'password'      => env('DB_SOA_RPT_PASSWORD', 'ng3p_r34d'),
            'charset'       => env('DB_SOA_RPT_CHARSET', 'AL32UTF8'),
            'prefix'        => env('DB_SOA_RPT_PREFIX', ''),

        ],
        
        // https://www.microsoft.com/en-us/sql-server/developer-get-started/php/windows?rtc=1
        'sqlsrv_epol' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_SQLSERV_EPOL_HOST', 'localhost'),
            'port' => env('DB_SQLSERV_EPOL_PORT', '1433'),
            'database' => env('DB_SQLSERV_EPOL_DATABASE', 'forge'),
            'username' => env('DB_SQLSERV_EPOL_USERNAME', 'forge'),
            'password' => env('DB_SQLSERV_EPOL_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
            'prefix_indexes' => true,
        ],

        'sqlsrv' => [
            'driver' => 'sqlsrv',
            'host' => env('DB_HOST', 'localhost'),
            'port' => env('DB_PORT', '1433'),
            'database' => env('DB_DATABASE', 'forge'),
            'username' => env('DB_USERNAME', 'forge'),
            'password' => env('DB_PASSWORD', ''),
            'charset' => 'utf8',
            'prefix' => '',
        ],

        'mysql_helpdesk' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_HELPDESK_HOST', '**************'),
            'port' => env('DB_MYSQL_HELPDESK_PORT', '3306'),
            'database' => env('DB_MYSQL_HELPDESK_DATABASE', 'helpdesk_production'),
            'username' => env('DB_MYSQL_HELPDESK_USERNAME', 'osticket'),
            'password' => env('DB_MYSQL_HELPDESK_PASSWORD', 'cDc@2020'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
        
        'mysql_notify' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_NOTIFY_HOST', 'localhost'),
            'port' => env('DB_MYSQL_NOTIFY_PORT', '3306'),
            'database' => env('DB_MYSQL_NOTIFY_DATABASE', 'ep_notify'),
            'username' => env('DB_MYSQL_NOTIFY_USERNAME', 'root'),
            'password' => env('DB_MYSQL_NOTIFY_PASSWORD', 'password'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ], 

        'mysql_crm_archive' => [
            'driver' => 'mysql',
            'host' => env('DB_MYSQL_CRM_ARCHIVE_HOST', '**************'),
            'port' => env('DB_MYSQL_CRM_ARCHIVE_PORT', '3306'),
            'database' => env('DB_MYSQL_CRM_ARCHIVE_DATABASE', 'cdccrm'),
            'username' => env('DB_MYSQL_CRM_ARCHIVE_USERNAME', 'crm_user'),
            'password' => env('DB_MYSQL_CRM_ARCHIVE_PASSWORD', 'cDccRm@2017'),
            'charset' => 'utf8',
            'collation' => 'utf8_unicode_ci',
            'prefix' => '',
            'strict' => true,
            'engine' => null,
            'modes'  => [
                'ONLY_FULL_GROUP_BY',
                'STRICT_TRANS_TABLES',
                'NO_ZERO_IN_DATE',
                'NO_ZERO_DATE',
                'ERROR_FOR_DIVISION_BY_ZERO',
                'NO_ENGINE_SUBSTITUTION',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Migration Repository Table
    |--------------------------------------------------------------------------
    |
    | This table keeps track of all the migrations that have already run for
    | your application. Using this information, we can determine which of
    | the migrations on disk haven't actually been run in the database.
    |
    */

    'migrations' => 'migrations',

    /*
    |--------------------------------------------------------------------------
    | Redis Databases
    |--------------------------------------------------------------------------
    |
    | Redis is an open source, fast, and advanced key-value store that also
    | provides a richer set of commands than a typical key-value systems
    | such as APC or Memcached. Laravel makes it easy to dig right in.
    |
    */

    'redis' => [

        'client' => 'predis',

        'default' => [
            'host' => env('REDIS_HOST', '127.0.0.1'),
            'password' => env('REDIS_PASSWORD', null),
            'port' => env('REDIS_PORT', 6379),
            'database' => 0,
        ],

    ],

];
