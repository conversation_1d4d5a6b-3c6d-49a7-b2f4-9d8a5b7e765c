<?php

namespace App\Http\Controllers;

use Validator;
use DB;
use Illuminate\Http\Request;
use App\Services\Traits\EpWebService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\SupplierService;
use App\EpSupportActionLog;
use App\Migrate\AppSupport\SmResendSoftcert;
use App\User;
use Log;
use App\Migrate\BPMTaskServiceProgram;

class WebServiceEpController extends Controller {

    use EpWebService;
    use OSBWebService;
    use SupplierService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    // the value string by commane = "[aaa,bbb,ccc,dddd]
    public function convertStringToArray($valueByComma)
    {

        // Extract the string from the collection (assuming it's the first item)
        $rolesString = collect($valueByComma)->first();

        // Remove square brackets and whitespace from the string
        $rolesString = str_replace(['[', ']', ' '], '', $rolesString);

        // Explode the string into an array using commas as the delimiter
        $rolesArray = explode(',', $rolesString);

        // Trim whitespace from each role name in the array
        $rolesArray = array_map('trim', $rolesArray);

        // Return the converted array (you can use this array as needed)
        return $rolesArray;
    }

    public function getUserLogin() {
        $userData = collect([]);
        $loginId = request()->login_id;
        if (strlen($loginId) == 0) {
            return view('user.user_login', [
                'type' => 'identity',
                'carian' => $loginId,
                'result' => $userData
            ]);
        }

        $user = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU')
                ->join('PM_PARAMETER_DESC as PMDESC', 'PMDESC.PARAMETER_ID', 'PMU.ORG_TYPE_ID')
                ->leftJoin('PM_LOGIN_HISTORY as LH','LH.USER_ID', 'PMU.USER_ID')
                ->where('PMDESC.LANGUAGE_CODE', '=', 'en')
                ->where('login_id', $loginId)
                ->select('PMU.*')
                ->addSelect('PMDESC.code_name')
                ->addSelect('LH.login_date')
                ->first();

        $userSso = null;
        try {
            $userSso = DB::connection('oracle_sso')->table('USR')
                ->whereRaw("lower(usr_login)  = lower('$loginId')")
                ->first();
        } catch (\Exception $exc) {
            Log::error("ERROR Connection DB SSO  get Loging ID $loginId >>> " .$exc->getMessage());
            Log::error($exc->getTraceAsString());
        }
        $userData->put('usersso', $userSso);
        

        $history = '';
        $organisasi = '';

        if ($user != null) {

            $user->eaduan_login = User::getTokenEaduan($loginId);
            $userData->put('userdata', $user);
            $objResult = $this->wsStatusUserLogin($loginId);
            
            $isGpkiValid = false;

            if( $objResult && isset($objResult['result']) ){
                // Extract role lists from the decoded JSON data
                $pmRoleList = $this->convertStringToArray($objResult['result']['PmRole']);
                $liferayRoleList = $this->convertStringToArray($objResult['result']['LiferayRole']);
                $oimRoleList = $this->convertStringToArray($objResult['result']['OimRole']);

                // Convert role lists to arrays
                $pmRoles = collect($pmRoleList);
                
                $userData->put('userstatus', $objResult);

                $liferayRoles = collect($liferayRoleList);
                $oimRoles = collect($oimRoleList);

                // Find roles missing in LiferayRole compared to PmRole
                $rolesMissingInLiferay = $pmRoles->diff($liferayRoles)->all();
                // Find roles missing in OimRole compared to PmRole
                $rolesMissingInOim = $pmRoles->diff($oimRoles)->all();

                // Find extra roles in Liferay compared to PmRole
                $extraRolesInLiferay = $liferayRoles->diff($pmRoles)->all();
                // Find extra roles in OimRole compared to PmRole
                $extraRolesInOim = $oimRoles->diff($pmRoles)->all();

                // Add to userData
                $userData->put('ExtraRoleLiferay', $extraRolesInLiferay);
                $userData->put('ExtraRoleOim', $extraRolesInOim);

                // $userData->put('role_compare', collect(['MissingRoleLiferay'=>$rolesMissingInLiferay,'MissingRoleOim'=>$rolesMissingInOim ]));
                $userData->put('MissingRoleLiferay', $rolesMissingInLiferay);
                $userData->put('MissingRoleOim', $rolesMissingInOim);

                // gpki roles 
                $gpkiRole = collect(['CT_APPROVER','BPK_CC_APPROVER','RN_APPROVER','FL_APPROVER','ACKNOWLEDGE_OFFICER','PAY_OFFICER','BPK_CQ_APPROVER','BPK_PQ_APPROVER','COMMITTEE_APPROVER','PUBLICATION_APPROVER','SC_CHAIRPERSON','EC_CHAIRPERSON','OC_CHAIRPERSON','TEC_CHAIRPERSON','FEC_CHAIRPERSON','QCA_CHAIRPERSON','QCB_CHAIRPERSON','PPB_CHAIRPERSON','FPB_CHAIRPERSON','FPB_CHAIRPERSON']);
            

                $isGpkiValid = $pmRoles->contains(function ($value, $key)  use ( $gpkiRole) {
                    return $gpkiRole->contains($value);
                });
            }
            
            
            $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER_ORG as PMUO');
            $query->where('PMUO.USER_ID', $user->user_id);
            $query->select('PMUO.org_profile_id');

            if ($user->org_type_id == 15) {

                $query->join('SM_SUPPLIER as SUPP', 'SUPP.SUPPLIER_ID', 'PMUO.ORG_PROFILE_ID');
                $query->select('SUPP.SUPPLIER_ID AS ORG_PROFILE_ID','SUPP.COMPANY_NAME AS NAMA_ORGANISASI', 'SUPP.REG_NO as SSM_NO_ORG_PROFILE_ID', 'SUPP.EP_NO as EP_NO_ORG_CODE', 'SUPP.RECORD_STATUS as RECORD_STATUS', 'SUPP.ESTABLISH_DATE as EFF_ESTABLISH_DATE');
                $query->addSelect('SUPP.CREATED_DATE as CREATED_DATE', 'SUPP.CHANGED_DATE as CHANGED_DATE','PMUO.USER_ORG_ID','SUPP.record_status as org_validity_record_status');

                $history = $query->get();
                $organisasi = $query->where('SUPP.RECORD_STATUS', 1)->first();
            } else {

                $query->join('PM_ORG_VALIDITY as PMOV', 'PMOV.ORG_PROFILE_ID', 'PMUO.ORG_PROFILE_ID');
                //$query->where('PMOV.RECORD_STATUS',1);
                $query->whereRaw("DECODE(to_number(to_char(PMOV.exp_date, 'j')),NULL,0,to_number(to_char(PMOV.exp_date, 'j'))) = (SELECT max(DECODE(to_number(to_char(exp_date, 'j')),NULL,0,to_number(to_char(exp_date, 'j')))) as exp_date_long FROM pm_org_validity  WHERE org_code = PMOV.org_code ) ");
                $query->select('PMOV.ORG_PROFILE_ID','PMOV.ORG_NAME as NAMA_ORGANISASI', 'PMOV.ORG_PROFILE_ID as SSM_NO_ORG_PROFILE_ID', 'PMOV.ORG_CODE as EP_NO_ORG_CODE', 'PMOV.RECORD_STATUS as PMOV_RECORD_STATUS', 'PMOV.EFF_DATE as EFF_ESTABLISH_DATE');
                $query->addSelect('PMOV.CREATED_DATE as CREATED_DATE', 'PMOV.CHANGED_DATE as CHANGED_DATE', 'PMUO.record_status as record_status','PMUO.USER_ORG_ID','PMOV.record_status as org_validity_record_status');

                $history = $query->get();
                $organisasi = $organisasi = $query->where('PMUO.RECORD_STATUS', 1)->where('PMOV.RECORD_STATUS', 1)->first();
            }
        }

        return view('user.user_login', [
            'carian' => $loginId,
            'result' => $userData,
            'history' => $history,
            'organisasi' => $organisasi,
            'isGpkiValid' => $isGpkiValid
        ]);
    }

    public function checkGpkiLastSigning()
    {
        //$latestSigningDigicert = $this->getDashboardLatestDigiCertSuccessSigning();
        //$latestSigningTrustgate = $this->getDashboardLatestTrustgateSuccessSigning();
        //$todaylistDashboardSoftcert = $this->getDashboardTotalCertificationAuthority();

        //$latestSigningDigicertDate = $latestSigningDigicert[0]->latest_signing;
        //$latestSigningTrustgateDate = $latestSigningTrustgate[0]->latest_signing;
        $user_id = request()->user_id;
        $resultData = $this->getLatestGpkiSuccessSigning($user_id);
        $html = "<strong>Last Successful Sign-in  :  </strong>No record found<br/>";
        if(count($resultData) > 0){
            $dateSigning = $resultData[0]->latest_signing;
            if(strlen($dateSigning) > 0){
                $html = "<strong>Last Successful Sign-in  :  </strong>$dateSigning <br/>";
            }
            
        }
        return $html;
    }

    
    public function findSsmCompany() {
        $ssmNo = request()->ssm_no;
        $ssmType = request()->ssm_type;
        if(strlen($ssmNo) > 5 && strlen($ssmType) > 2 ){
            $uuid  = \Ramsey\Uuid\Uuid::uuid4();
            return $this->callWSFindSSMCompany($uuid, $ssmNo, $ssmType); 
        }
        return 'Invalid'; 
    }
    
    /**
     * Please take node.. To add new Server EJB Request in Monitoring page,
     * Please ensure connection Prod Server ************** (EPSS Server) allow to connect EJB server. If connection failed!, Ask Team Network Admin to allow EPSS server connect EJB server
     * On 16/4/2019 already allowed by Team NetworkAdmin : 
     *      Production:- 
      prdportal03a - **************:4447
      prdportal03b - **************:4447
      prdportal03c - **************:4447
      prdportal04a - **************:4447
      prdportal04b - **************:4447
      prdportal04c - **************:4447

      On 29/6/2019 change IP address
      **************  prdejb01a-clu.eperolehan.com.my prdejb01a-clu
      **************  prdejb01b-clu.eperolehan.com.my prdejb01b-clu
      **************  prdejb01c-clu.eperolehan.com.my prdejb01c-clu
      **************  prdejb02a-clu.eperolehan.com.my prdejb02a-clu
      **************  prdejb02b-clu.eperolehan.com.my prdejb02b-clu
      **************  prdejb02c-clu.eperolehan.com.my prdejb02c-clu


      SIT:-
      *************:4447

     */
    public function listMonitoringServiceRunningEjb() {

        $listdata = collect();

        $listEjbNode1 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");
        $listEjbNode2 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");
        $listEjbNode3 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");
        $listEjbNode4 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");
        $listEjbNode5 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");
        $listEjbNode6 = $this->wsListMonitoringServiceRunningEjb("**************", "4447");

        $listdata->push($listEjbNode1);
        $listdata->push($listEjbNode2);
        $listdata->push($listEjbNode3);
        $listdata->push($listEjbNode4);
        $listdata->push($listEjbNode5);
        $listdata->push($listEjbNode6);

        return view('dashboard_ejb', ['listdata' => $listdata]);
    }

    public function getDetailParameterServiceRunningEjb() {
        $host = request()->host;
        $port = request()->port;
        $key = request()->key;
        if (strlen($host) == 0 || strlen($port) == 0 || strlen($key) == 0) {
            $html = "Invalid Parameter! ";
        }
        $listdata = $this->wsDetailParameterServiceRunningEjb($host, $port, $key);

        return $listdata;
    }

    public function startServiceRunningEjb() {
        $phaseSecurity = request()->security_phrase;

        if ($phaseSecurity === 'OK') {
            $this->wsStartUpServiceRunningEjb("**************", "4447");
            $this->wsStartUpServiceRunningEjb("**************", "4447");
            $this->wsStartUpServiceRunningEjb("**************", "4447");
            $this->wsStartUpServiceRunningEjb("**************", "4447");
            $this->wsStartUpServiceRunningEjb("**************", "4447");
            $this->wsStartUpServiceRunningEjb("**************", "4447");

            return array(
                "status" => "Success",
                "result" => 'Done');
        }
        return array(
            "status" => "Error",
            "result" => 'Failed');
    }

    public function syncRoleUserEp($loginId) {

        $user = DB::connection('oracle_nextgen_rpt')->table('PM_USER')->where('login_id',$loginId)
            ->where('record_status',1)->first();
        
        if($user == null){
            return array(
            "status" => "Error",
            "result" => 'No record found for login ID  with valid criteria: '.$loginId);
        }
        
        //If Supplier
        // if($user->org_type_id == 15){
        //     $personnel = DB::connection('oracle_nextgen_rpt')
        //             ->table('SM_PERSONNEL as PERS')
        //             ->join('SM_SUPPLIER as SUPP', 'SUPP.LATEST_APPL_ID', 'PERS.APPL_ID')
        //             ->where('PERS.USER_ID',$user->user_id)
        //             ->where('PERS.RECORD_STATUS',1)
        //             ->whereNotNull('PERS.EP_ROLE')
        //             ->first();
        //     return $this->syncRoleSMPersonnelUser($personnel->personnel_id);
        // }
        
        $result = $this->wsSyncRoleUserEp($user->user_id, $user->login_id, $user->org_type_id);

        $actionName = 'Sync Role User eP';
        $actionTypeLog = 'Web Service';
        $parameters =  collect([]);            
        $parameters->put("user_id", $user->user_id);
        $parameters->put("login_id", $user->login_id);
        $parameters->put("org_type_id", $user->org_type_id);
        $parameters->put("action", "Request to re-sync roles to SSO and liferay");
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

        $actionData =  collect([]);            
        $actionData->put("user_id", $user->user_id);
        $actionData->put("login_id", $user->login_id);
        $actionData->put("org_type_id", $user->org_type_id);
        $actionData->put("response", $result);

        EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed");

        return $result;
            
        

    }
    
    public function resendSoftcertRequest(){
        $icNo = request()->ic_no;
        $softcertRequestId = request()->softcert_request_id;
    }

    public function resendSoftcertRequestCertificate(){
        $resp = collect([]);
        $personnelId = request()->personnel_id;
        $listData = $this->getPersonnelInfoDtlSoftcertPaid($personnelId);
        if($listData != null && count($listData) > 0){
            $obj = $listData[0];
            $objData = collect([]);
            $objData->put('login_id',$obj->login_id);
            $objData->put('appl_id',$obj->appl_id);
            $objData->put('user_id',$obj->user_id);
            $objData->put('pending_process_id',$obj->pending_process_id);
            $objData->put('personnel_id',$obj->personnel_id);
            $objData->put('doc_no',$obj->appl_no);
            $resp->put('data',$objData);
            $res = BPMTaskServiceProgram::refirePaidSoftcertTaskSmApplication($objData);
            $resp->put('result',$res);
        }else{
            $resp->put('result','Not Valid Data');
        }
        return $resp;
    }

    public function resendSoftcertRequestTgtoDg(){
        $icNo = request()->ic_no;
        $softcertRequestId = request()->softcert_request_id;
        SmResendSoftcert::sendSwitchTgToDgSoftcertReq($icNo,$softcertRequestId);
    }

    public function syncRoleSMPersonnelUser($personnelId) {

        $personnel = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL')->where('personnel_id',$personnelId)
            ->where('record_status',1)->whereNotNull('user_id')->first();
        
        if($personnel == null){
            return array(
            "status" => "Error",
            "result" => 'No record found for personnel ID  with valid criteria: '.$personnelId);
        }
        
        $result = $this->wsSyncSmRolePersonnelUser($personnel->personnel_id);

        $actionName = 'Sync Role SM UserPersonnel';
        $actionTypeLog = 'Web Service';
        $parameters =  collect([]);            
        $parameters->put("personnel_id", $personnelId);
        $parameters->put("action", "Request to re-sync roles to SSO and liferay");
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

        $actionData =  collect([]);            
        $actionData->put("personnel_id", $personnelId);
        $actionData->put("response", $result);

        EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed");

        return $result;
            
        

    }
    
    public function reProcessFileAP511() {
        $fileName = request()->filename;

        if ($fileName != '') {

            $result = $this->wsUpdatePaymentAP511($fileName);
            
            $actionName = 'Integration-IGFMAS-AP511-Reprocess';
            $actionTypeLog = 'Web Service';
            $parameters =  collect([]);            
            $parameters->put("file_name", $fileName);
            $parameters->put("action", "Request to reprocess file AP511 to update in eP");
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            $actionData =  collect([]);            
            $actionData->put("file_name", $fileName);
            $actionData->put("response", $result);
            
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed");
            return $result;
        }
        return array(
            "status" => "Error",
            "result" => 'Invalid Parameter');
    }
    
    public function reProcessFileAP511ByInvoices() {
        $filename = request()->filename;
        $invoices = request()->invoices;

        if ($filename != '' && $invoices != '') {
            
            $result = $this->wsUpdatePaymentAP511ByInvoices($filename,$invoices);
            
            $actionName = 'Integration-IGFMAS-AP511-Reprocess-BySpecificInvoice';
            $actionTypeLog = 'Web Service';
            $parameters =  collect([]);            
            $parameters->put("file_name", $filename);
            $parameters->put("invoices", $invoices);
            $parameters->put("action", "Request to reprocess file AP511 by specific list invoices no to update in eP");
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            $actionData =  collect([]);            
            $actionData->put("file_name", $filename);
            $actionData->put("response", $result);
            
            EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed");
            return $result;
        }
        return array(
            "status" => "Error",
            "result" => 'Invalid Parameter');
    }

}
