@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>List POCO Having Redundant SAP Order No From IGFMAS<br>
                <small>Check POCO Redundant SAP Order No from IGFMAS. Query check as of date </small>
            </h1>
        </div>
    </div>

    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Start Redundant SAP Order No Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Redundant SAP Order No from IGFMAS  </strong>
                        <small></small> 
                    </h1>
                </div>
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                {{ csrf_field() }}
                <div class="table-responsive">
                    <table id="stuck-bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC NO</th>
                            <th class="text-center">OLD SAP ORDER NO</th>
                            <th class="text-center">NEW SAP ORDER NO</th>
                            <th class="text-center">INSTANCE ID</th>
                            <th class="text-center">PATCH</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center"><a href="{{url('/find/trans/track/docno')}}/{{ $data->doc_no }}" target="_blank" title="Find Tracking Diary"> {{ $data->doc_no }}</a></td>
                                <td class="text-center">{{ $data->old_sap_order_no }}</td>
                                <td class="text-center">{{ $data->sap_order_no }}</td>
                                @if($data->instance_id != 'NA')
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_id}}" target="_blank" > {{ $data->instance_id }}</a></td>
                                @else
                                <td class="text-center">{{$data->instance_id}}</td>
                                @endif
                                <td class="text-left"><code>update fl_fulfilment_order set sap_order_no ='{{ $data->sap_order_no }}' where doc_no='{{ $data->doc_no }}'</code></td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Redundant SAP Order No Block -->
        </div>
    
    @endif

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
    <script>
        App.datatables();

        $('#stuck-bpm-datatable').dataTable({
                order: [[ 4, "desc" ],[ 0, "desc" ]],
                columnDefs: [  ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
                });
    </script>
@endsection



