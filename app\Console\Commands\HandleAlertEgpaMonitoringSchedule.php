<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Model\Notify\NotifyModel;
use Mail;
use Log;
use Config;
use DB;

class HandleAlertEgpaMonitoringSchedule extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleAlertEgpaMonitoring';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This daily check to check is it schedule eGPA execute successfully ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(__METHOD__ . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            
            $timeHourNow = Carbon::now()->format('H');

            // to make sure.. on saturday will trigger  after 7am.. cause scheduler start on 7am.
            // except saturday, scheduler start on 1am and 1:35am.. so alert will checking after 1:45am
            if( 
                ( Carbon::now()->isSaturday() && $timeHourNow > 7 ) 
                || 
                ( !Carbon::now()->isSaturday() && $timeHourNow >= 1) 
            ){
                $serviceCodeSyarikat = 'EPS-001';
                $processNameSyarikatTbl = "EGPA_SYARIKAT";
                $this->checkingAlertEgpaScheduler($serviceCodeSyarikat,$processNameSyarikatTbl);

                $serviceCodeBidang = 'EPS-002';
                $processNameBidangTbl = "EGPA_BIDANG";
                $this->checkingAlertEgpaScheduler($serviceCodeBidang,$processNameBidangTbl);
            }
            

        } catch (\Exception $exc) {

            Log::error(__METHOD__. '>> error happen!! ' . $exc->getMessage());
            Log::error(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());

        }
        
    }

    protected function checkingAlertEgpaScheduler($serviceCode,$processNameTbl) {
        $dateNow = Carbon::now()->format('Y-m-d H:i:s');
        $checkSuccTrigger = $this->isSuccessExtractData($serviceCode);
        MigrateUtils::logDump(__METHOD__." >> $serviceCode,$processNameTbl , checking no trigger log record execute in osb_logging :- $checkSuccTrigger->total record");
        if($checkSuccTrigger->total == 0){
            $countFileSent = $this->isSendFileSuccess($serviceCode);
            if($countFileSent->total == 0){
                $statObj = $this->getStatEgpaProcessName($processNameTbl);
                $msg = $this->msgNotificationAlert($dateNow,$processNameTbl,$statObj);
                MigrateUtils::logErrorDump(__METHOD__.$msg);
                $this->saveNotify('EGPA',$msg);
            }
            else { 
                MigrateUtils::logDump(__METHOD__." >> checking is any file is sent ? $serviceCode,$processNameTbl Already sent file $countFileSent->total");
                MigrateUtils::logDump(__METHOD__." >> no need to put as alert.");
            }
        }
    }

    protected function msgNotificationAlert($dateProcess,$processName,$statObj) {
        $totalDesc = "$statObj->total_record created at $statObj->created_date";
        if($statObj->total_record == 0){
            $totalDesc = "$statObj->total_record";
        }
       //ALERT : This format contains to send in whatapp.
       $msg = "
*[ALERT] eGPA SCHEDULER*
*DATE CHECKING TIME*
  $dateProcess
*PROCESS NAME*
  $processName
*TOTAL RECORDS*
  $totalDesc
*ISSUE*
  Please check, as the scheduler did not generate any creation file.";
        return $msg;
    }
    
    protected function isSuccessExtractData($serviceCode) {
        $res = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT count(*) AS total FROM OSB_LOGGING WHERE SERVICE_CODE IN ('EPP-018') 
            AND TRANS_TYPE = 'IBReq' AND TRUNC(trans_date) = TRUNC(sysdate) 
            AND remarks_1 = ? ",array($serviceCode));
        return  $res[0];
    }

    /**
     * Just checking log, to make sure at least 1 file success sent 
     */
    protected function isSendFileSuccess($serviceCode) {
        $res = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  count(*) AS total   FROM OSB_LOGGING WHERE SERVICE_CODE = ? 
            AND TRANS_TYPE = 'Status-BATCH'
            AND TRUNC(trans_date) = TRUNC(sysdate)  ",array($serviceCode));
        return  $res[0];
    }

    protected function getStatEgpaProcessName($processNameTable) {
        $res = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT count(*) AS total_record, max(created_date) AS created_date FROM $processNameTable");
        return  $res[0];
    }

    protected function saveNotify($receiver,$msg){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $totNotify = NotifyModel::where('message',$msg)->count();
        if($totNotify > 0){
            MigrateUtils::logDump($clsInfo.'Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring daily eGPA';
            $nty->save();
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__ . ' >> '. $e->getMessage());
            return $e;
        }
    }
    
}
