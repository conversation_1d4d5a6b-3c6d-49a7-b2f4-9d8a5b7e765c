@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/support-spki/task/list')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...  (Case No, Company Name, Description)">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    @if ($listdata == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                        <i class="gi gi-search"></i><PERSON><PERSON><br>
                        <small>Masukkan carian diatas...</small>
                    @endif
                </h1>
            </div>
        </div>
    @endif

    @if($listdata != null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>SPKI Tasks<br>
                    <small>For eP Support</small>
                </h1>
            </div>
        </div> 
        
        @if(count($reportStatService) > 0)
        <div class="row">
            <div class="block">
                <div class="block-title">
                    <h2><i class="fa fa-line-chart"></i><strong>SPKI Service </strong>Statistic Today</h2>
                </div>
                
                <div class="row text-center">
                    
                    <div class="widget col-sm-6 col-lg-6">
                        @foreach($reportStatService as $report)
                        @if($report->provider === 'digicert')
                        <h5><strong>{{ strtoupper($report->provider) }}</strong></h5>
                        <div style="margin-top: 2px">
                            <div class="themed-background-muted-light" style="float: left; display: block; width: 70%; padding: 8px; margin-top: 3px;"><strong>{{ strtoupper($report->status) }}</strong></div>
                            <div class="@if($report->status == 'success') themed-background-success @else themed-background-danger @endif" style="float: left; display: block; width: 30%; padding: 8px; color: #fff; margin-top: 3px;" data-toggle="tooltip" title="Total {{ $report->status }} service check count as of today." data-original-title="{{ $report->status }}">
                                <span><strong>{{ $report->total }}<strong></span>
                            </div>
                        </div>
                        @endif
                        @endforeach 
                    </div>

                    <div class="widget col-sm-6 col-lg-6">
                        @foreach($reportStatService as $report)
                        @if($report->provider === 'trustgate')
                        <h5><strong>{{ strtoupper($report->provider) }}</strong></h5>
                        <div style="margin-top: 2px">
                            <div class="themed-background-muted-light" style="float: left; display: block; width: 70%; padding: 8px; margin-top: 3px;"><strong>{{ strtoupper($report->status) }}</strong></div>
                            <div class="@if($report->status == 'success') themed-background-success @else themed-background-danger @endif" style="float: left; display: block; width: 30%; padding: 8px; color: #fff; margin-top: 3px;" data-toggle="tooltip" title="Total {{ $report->status }} service check count as of today." data-original-title="{{ $report->status }}">
                                <span><strong>{{ $report->total }}<strong></span>
                            </div>
                        </div>
                        @endif
                        @endforeach 
                    </div>

            </div>
        </div>
        @endif

        <div class="row">
            <div class="col-md-6">
                <div class="widget">
                       
                    <div class="widget-extra themed-background-danger">
                        
                        <h5 class="widget-content-light">
                            Pending <strong>Tasks</strong>
                        </h5>
                    </div>
                    <div class="widget-extra-full">
                        <div class="row text-center">
                            @if(count($reportStatPending) > 0)
                            @foreach($reportStatPending as $report)   
                            <div class="col-xs-6 col-lg-2">
                                <h4 style="margin:0px;padding:0px;">
                                    <strong>{{$report->total}}</strong><br>
                                    <small>{{App\Services\EPService::$SPKI_PROVIDER[$report->provider_id]}}</small>
                                </h4>
                            </div>
                            @endforeach 
                            @else
                            <div class="col-md-12">
                                <h4 style="margin:0px;padding:0px;">
                                    <strong>&nbsp;</strong><br>
                                    <small>No Pending Task.</small>
                                </h4>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="widget">
                           
                    <div class="widget-extra themed-background-success">
                        
                        <h5 class="widget-content-light">
                            Completed <strong>Tasks</strong>
                        </h5>
                    </div>
                    <div class="widget-extra-full">
                        <div class="row text-center">
                            @if(count($reportStatCompleted) > 0)
                            @foreach($reportStatCompleted as $report)   
                            <div class="col-xs-6 col-lg-2">
                                <h4 style="margin:0px;padding:0px;">
                                    <strong>{{$report->total}}</strong><br>
                                    <small>{{App\Services\EPService::$SPKI_PROVIDER[$report->provider_id]}}</small>
                                </h4>
                            </div>
                            @endforeach
                            @else
                            <div class="col-md-12">
                                <h4 style="margin:0px;padding:0px;">
                                    <strong>&nbsp;</strong><br>
                                    <small>No Completed Task.</small>
                                </h4>
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        
        @if($success && $success == 'success')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> Task <a href="javascript:void(0)" class="alert-link">saved</a>!
        </div>
        @elseif($success && $success == 'failed')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Failed</h4> Task <a href="javascript:void(0)" class="alert-link">failed</a>!
        </div>
        @endif


        <div class="block block-alt-noborder full">
            <span style="display:none" id="loadingTaskForm"> Repopulate Data. Please Wait... <i class="fa fa-spinner fa-spin"></i> </span>
            <div class="row" id="panelFormTask" 
                @if ($errors->any()) style="display:block" @else style="display:none" @endif>
                <div class="col-md-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong><span id="titleTask">Add SPKI Task</span></strong></h2>
                            <div class="block-options pull-right">
                                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                                    onclick="$('#panelFormTask').hide();    $('#taskViewOther').hide();" >Close</span>
                            </div>
                        </div>

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form id="form-task" action="{{url("/support-spki/task")}}" method="post" class="form-horizontal form-bordered">
                            {{ csrf_field() }}
                            <input name="_method" id="_method"  type="hidden" value="POST">
                            <input type="hidden" name="task_id"  id="task_id" value="" />
                            <input type="hidden" name="btn_action"  id="btn_action" value="" />
                            {{-- <span id="categories" class="hide" >{{$categories}}</span> --}}
                            <div class="col-md-6">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="provider_id">Provider <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="provider_id" name="provider_id" class="form-control">
                                                    <option value="provider_id">Please select</option>
                                                    @foreach(App\Services\EPService::$SPKI_PROVIDER as  $key => $value)
                                                    
                                                    <option value="{{$key}}" @if(old('provider_id') == $key) selected @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="case_no">Case No. <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="case_no" name="case_no" class="form-control" placeholder="Case No CRM.. / Or for UPS put 1111"
                                                    value="{{old('case_no')}}">
                                                <input type="hidden" name="case_type"  id="case_type" value="" />
                                                <span class="input-group-addon"><i class="gi gi-qrcode"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="entity_name">Company Name <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="entity_name" name="entity_name" class="form-control" placeholder="Company Name.."
                                                value="{{old('entity_name')}}">
                                                <span class="input-group-addon"><i class="gi gi-nameplate"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="description">Description<span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <textarea id="description" name="description" rows="6" class="form-control" placeholder="Details Information..">{{old('description')}}</textarea>
                                        </div>
                                    </div>

                                    <div class="form-group form-group-resolution" style="display:none;">
                                        <label class="col-md-3 control-label" for="resolution">Resolution<span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <textarea id="resolution" name="resolution" rows="3" class="form-control" placeholder="Resolution Information.."></textarea>
                                        </div>
                                    </div>
                                    
                                </fieldset>
                            </div>
                            <div class="col-md-6 hide" id="taskViewOther">
                                <fieldset>
                                    <div class="form-group form-group-status">
                                        <label class="col-md-3 control-label" for="status_id">Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="status_id" name="status_id" class="form-control">
                                                    <option value="status_id">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_SPKI_STATUS as  $key => $value)
                                                    
                                                    <option value="{{$key}}" @if(old('status_id') == $key) selected @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-group-view-created">
                                        <label class="col-md-3 control-label">Created</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <p class="form-control-static" id="viewCreated"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-group-created-datetime">
                                        <label class="col-md-3 control-label" for="created_date">Created DateTime <span class="text-danger">*</span></label>
                                        <div class="col-md-8">
                                            <div style="float: left; padding-right: 5px;">
                                                <input type="text" id="created_date" name="created_date" class="form-control input-datepicker" data-date-format="dd/mm/yyyy" placeholder="dd/mm/yyyy" value="{{Carbon\Carbon::now()->format('d/m/Y')}}">
                                            </div>
                                            <div class="input-group bootstrap-timepicker"><div class="bootstrap-timepicker-widget dropdown-menu"><table><tbody><tr><td><a href="#" data-action="incrementHour"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementMinute"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementSecond"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td class="meridian-column"><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-up"></i></a></td></tr><tr><td><input type="text" class="form-control bootstrap-timepicker-hour" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-minute" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-second" maxlength="2"></td><td class="separator">&nbsp;</td><td><input type="text" class="form-control bootstrap-timepicker-meridian" maxlength="2"></td></tr><tr><td><a href="#" data-action="decrementHour"><i class="fa fa-chevron-down"></i></a></td><td class="separator"></td><td><a href="#" data-action="decrementMinute"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="decrementSecond"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-down"></i></a></td></tr></tbody></table></div>
                                                <input type="text" id="created_time" name="created_time" class="form-control input-timepicker">
                                                <span class="input-group-btn">
                                                    <a href="javascript:void(0)" class="btn btn-primary"><i class="fa fa-clock-o"></i></a>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    {{-- <div class="form-group form-group-view-created-datetime">
                                        <label class="col-md-3 control-label">Created DateTime</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <p class="form-control-static" id="viewCreatedDatetime"></p>
                                            </div>
                                        </div>
                                    </div> --}}
                                    <div class="form-group form-group-response-datetime">
                                        <label class="col-md-3 control-label" for="response_date">Response DateTime <span class="text-danger">*</span></label>
                                        <div class="col-md-8">
                                            <div style="float: left; padding-right: 5px;">
                                                <input type="text" id="response_date" name="response_date" class="form-control input-datepicker" data-date-format="dd/mm/yyyy" placeholder="dd/mm/yyyy" value="{{Carbon\Carbon::now()->format('d/m/Y')}}">
                                            </div>
                                            <div class="input-group bootstrap-timepicker"><div class="bootstrap-timepicker-widget dropdown-menu"><table><tbody><tr><td><a href="#" data-action="incrementHour"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementMinute"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementSecond"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td class="meridian-column"><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-up"></i></a></td></tr><tr><td><input type="text" class="form-control bootstrap-timepicker-hour" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-minute" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-second" maxlength="2"></td><td class="separator">&nbsp;</td><td><input type="text" class="form-control bootstrap-timepicker-meridian" maxlength="2"></td></tr><tr><td><a href="#" data-action="decrementHour"><i class="fa fa-chevron-down"></i></a></td><td class="separator"></td><td><a href="#" data-action="decrementMinute"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="decrementSecond"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-down"></i></a></td></tr></tbody></table></div>
                                                <input type="text" id="response_time" name="response_time" class="form-control input-timepicker">
                                                <span class="input-group-btn">
                                                    <a href="javascript:void(0)" class="btn btn-primary"><i class="fa fa-clock-o"></i></a>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-group-resolved-datetime">
                                        <label class="col-md-3 control-label" for="resolved_date">Resolved DateTime <span class="text-danger">*</span></label>
                                        <div class="col-md-8">
                                            <div style="float: left; padding-right: 5px;">
                                                <input type="text" id="resolved_date" name="resolved_date" class="form-control input-datepicker" data-date-format="dd/mm/yyyy" placeholder="dd/mm/yyyy" value="{{Carbon\Carbon::now()->format('d/m/Y')}}">
                                            </div>
                                            <div class="input-group bootstrap-timepicker"><div class="bootstrap-timepicker-widget dropdown-menu"><table><tbody><tr><td><a href="#" data-action="incrementHour"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementMinute"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="incrementSecond"><i class="fa fa-chevron-up"></i></a></td><td class="separator">&nbsp;</td><td class="meridian-column"><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-up"></i></a></td></tr><tr><td><input type="text" class="form-control bootstrap-timepicker-hour" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-minute" maxlength="2"></td> <td class="separator">:</td><td><input type="text" class="form-control bootstrap-timepicker-second" maxlength="2"></td><td class="separator">&nbsp;</td><td><input type="text" class="form-control bootstrap-timepicker-meridian" maxlength="2"></td></tr><tr><td><a href="#" data-action="decrementHour"><i class="fa fa-chevron-down"></i></a></td><td class="separator"></td><td><a href="#" data-action="decrementMinute"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="decrementSecond"><i class="fa fa-chevron-down"></i></a></td><td class="separator">&nbsp;</td><td><a href="#" data-action="toggleMeridian"><i class="fa fa-chevron-down"></i></a></td></tr></tbody></table></div>
                                                <input type="text" id="resolved_time" name="resolved_time" class="form-control input-timepicker">
                                                <span class="input-group-btn">
                                                    <a href="javascript:void(0)" class="btn btn-primary"><i class="fa fa-clock-o"></i></a>
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-group-view-modified">
                                        <label class="col-md-3 control-label">Modified</label>
                                        <div class="col-md-9">
                                            <div>
                                                <p class="form-control-static" id="viewModified"></p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group form-group-view-completed">
                                        <label class="col-md-3 control-label">Completed</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <p class="form-control-static" id="viewCompleted"></p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </fieldset>
                            </div>
                            <div class="form-group form-actions form-actions-button">
                                <div class="col-md-8 col-md-offset-4">
                                    <button type="submit" class="btn btn-sm btn-primary"><i class="fa fa-arrow-right"></i><span id="btn_save_span"> Save</span></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Customer Addresses Block -->
            <div class="block" id="panelListTask">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>List Tasks</strong>
                    </h1>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="{{url("/support/report/log/task-spki")}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                           data-title="List Action Tasks by Today ">View Today Action</a>
                    </div>
                </div>
                <div class="col-md-12" >
                    <div class="block">
                        <form id="form-search-task" action="{{url("/support-spki/task/list")}}" method="post" class="form-horizontal form-bordered">
                            {{ csrf_field() }}
                            <input name="_method" id="_method"  type="hidden" value="POST">
                            <div class="col-md-6">
                                
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_provider_id">Provider <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_provider_id" name="search_provider_id" class="form-control">
                                                    <option value="provider_id">Please select</option>
                                                    @foreach(App\Services\EPService::$SPKI_PROVIDER as  $key => $value)
                                                    
                                                    {{-- <option value="{{$key}}" @if(old('provider_id') == $key) selected @endif>{{$value}}</option> --}}
                                                    <option value="{{$key}}"
                                                            @if(isset($formSearch))
                                                                @if($key == $formSearch["search_provider_id"] ) selected @endif
                                                            @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-6">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_status_id">Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_status_id" name="search_status_id" class="form-control">
                                                    <option value="status_id">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_SPKI_STATUS as  $key => $value)
                                                    
                                                    <option value="{{$key}}"
                                                            @if(isset($formSearch))
                                                                @if($key == $formSearch["search_status_id"] ) selected @endif
                                                            @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions form-actions-button text-right">
                                
                                    <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                                    <a href="{{url('/support-spki/task')}}" class="btn btn-sm btn-warning"><i class="fa fa-repeat"></i> Reset</a>
                               
                            </div>
                        </form>
                        
                    </div>
                </div>
                <button type="button" id="openAddTask" class="btn btn btn-info" >Add Task</button>
                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Provider</th>
                            <th class="text-center">Company</th>
                            <th class="text-center">Description</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Case No</th>
                            <th class="text-center">Created</th>
                            <th class="text-center">Response</th>
                            <th class="text-center">Resolved</th>
                            <th class="text-center">Completed</th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ App\Services\EPService::$SPKI_PROVIDER[$data->provider_id] }}</td>
                                <td class="text-center">{{ $data->entity_name }}</td>
                                <td class="text-left" width="30%" >
                                <textarea rows="5" class="form-control" style="width: 100%" readonly>{{ $data->description }}</textarea>
                                </td>
                                <td class="text-center">
                                    @if($data->status == 0)
                                    <a href="javascript:void(0)" class="label label-info">{{ App\Services\EPService::$TASK_SPKI_STATUS[$data->status] }}</a>
                                    @endif
                                    @if($data->status == 1)
                                    <a href="javascript:void(0)" class="label label-warning">{{ App\Services\EPService::$TASK_SPKI_STATUS[$data->status] }}</a>
                                    @endif
                                    @if($data->status == 2)
                                    <a href="javascript:void(0)" class="label label-success">{{ App\Services\EPService::$TASK_SPKI_STATUS[$data->status] }}</a>
                                    @endif
                               </td>
                               <td class="text-center">{{ $data->case_no }}  {{ $data->case_type }}</td>
                                <td class="text-center">{{ $data->created_datetime }} &nbsp; {{ $data->created_by }}</td>
                                <td class="text-center">{{ $data->response_datetime }}</td>
                                <td class="text-center">{{ $data->resolved_datetime }}</td>
                                <td class="text-center">{{ $data->completed_at }} &nbsp; {{ $data->completed_by }}</td>
                                <td class="text-center action_table_task">
                                    <div class="btn-group btn-group-xs">
                                        
                                        @if($data->status == 2)
                                        <a class="btn btn-primary action_table_view_task" data-toggle="tooltip" title="" data-original-title="Show Details"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="hi hi-eye-open"></i></a>
                                        @else
                                        <a class="btn btn-primary action_table_edit_task" data-toggle="tooltip" title="" data-original-title="Update Task"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="fa fa-arrow-up"></i></a>
                                        {{-- <a class="btn btn-primary action_table_complete_task" data-toggle="tooltip" title="" data-original-title="Set to complete"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="hi hi-ok"></i></a> --}}
                                        @endif
                                        
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
        

        @include('_shared._modalListLogAction')

    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    function getCurrentFormatedDate(){
        return moment(new Date()).format("DD/MM/YYYY");
    }

    function getCurrentFormatedTime(){
        return moment(new Date()).format("hh:mm:ss A");
    }

    function openFormTask(typeForm){
        $('#to-top').click(); //move scroll up to top page
        
        $('#panelFormTask').removeClass('hide'); 
        $('#panelFormTask').show(); 
            
        if(typeForm === 'add'){
            // $('#taskViewOther').hide();
            $('#taskViewOther').removeClass('hide'); 
            $('.form-actions-button').show();
            $('.form-group-resolution').hide();
            $('.form-group-resolved-datetime').hide();
            $("#titleTask").text("Add SPKI Task");
            $("#btn_save_span").text(" Save");
            $("#_method").val("POST");
            $('.form-group-created-datetime').show();
            $(".form-group-view-created").hide();
            $(".form-group-response-datetime").hide();
            $(".form-group-view-modified").hide();
            $(".form-group-view-completed").hide();
            $(".form-group-status").hide();

            $("#created_date").val(getCurrentFormatedDate());
            $("#created_time").val(getCurrentFormatedTime());

            $("#resolved_date").val(null);
            $("#resolved_time").val(null);
            $("#provider_id").removeAttr("readonly");
            $("#status_id").removeAttr("readonly");
            $("#case_no").removeAttr("readonly");
            $("#entity_name").removeAttr("readonly");
            $("#description").removeAttr("readonly");
            $("#created_date").removeAttr("readonly");
            $("#created_time").removeAttr("readonly");
            $("#created_date").removeAttr("disabled");
            $("#created_time").removeAttr("disabled");
        }else if(typeForm === 'edit'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').hide();
            $('.form-group-resolved-datetime').hide();
            $("#titleTask").text("Update Task");
            // $("#btn_save_span").text(" Save");
            $("#_method").val("POST");

            // $('.form-group-created-datetime').hide();
            $(".form-group-view-created").hide();
            $(".form-group-response-datetime").show();
            // $(".form-group-view-modified").hide();
            // $(".form-group-view-completed").hide();
            $(".form-group-status").show();

            $("#provider_id").removeAttr("readonly");
            // $("#status_id").removeAttr("readonly");
            // $("#status_id").removeAttr("disabled");
            $("#status_id").attr("readonly","readonly");
            $("#status_id").attr("disabled","true");
            $("#case_no").removeAttr("readonly");
            $("#entity_name").removeAttr("readonly");
            $("#description").removeAttr("readonly");
            $("#created_date").attr("disabled","true");
            $("#created_time").attr("disabled","true");
        }else if(typeForm === 'complete'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').show();
            $('.form-group-resolved-datetime').show();
            $("#titleTask").text("To Complete Task");
            $("#btn_save_span").text(" Set Completed");
            $("#_method").val("PUT");

            $(".form-group-view-created").hide();
            $('.form-group-created-datetime').show();
            $(".form-group-response-datetime").show();
            $(".form-group-view-modified").hide();
            $(".form-group-view-completed").hide();
            $(".form-group-status").show();
            
            $("#provider_id").attr("readonly","readonly");
            $("#status_id").attr("readonly","readonly");
            $("#status_id").attr("disabled","true");
            $("#case_no").attr("readonly","readonly");
            $("#entity_name").attr("readonly","readonly");
            $("#description").attr("readonly","readonly");
            $("#resolution").removeAttr("readonly");
            $("#created_date").attr("disabled","true");
            $("#created_time").attr("disabled","true");
            $("#resolved_date").removeAttr("readonly");
            $("#resolved_time").removeAttr("readonly");
        }else if(typeForm === 'view'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $('.form-group-resolution').show();
            $('.form-group-resolved-datetime').show();
            $("#titleTask").text("View Detail Task");
            $("#btn_save_span").parent().hide();
            $("#_method").val("GET");
            
            $("#provider_id").attr("readonly","readonly");
            $("#status_id").attr("readonly","readonly");
            $("#case_no").attr("readonly","readonly");
            $("#entity_name").attr("readonly","readonly");
            $("#description").attr("readonly","readonly");
            $("#resolution").attr("readonly","readonly");
            $("#created_date").attr("readonly","readonly");
            $("#created_time").attr("readonly","readonly");
            $("#response_date").attr("readonly","readonly");
            $("#response_time").attr("readonly","readonly");
            $("#resolved_date").attr("readonly","readonly");
            $("#resolved_time").attr("readonly","readonly");
        }
        
        $("#provider_id").val("");
        $("#status_id").val("");
        $("#task_id").val("");
        $("#case_no").val("");
        $("#description").text("");
        $("#entity_name").val("");
        $("#resolution").text("");
        $("#resolved_date").text("");
        // $("#resolved_time").text("");
    }
    // $( "#provider_id" ).bind( "click change", function() {
    //     var providers = JSON.parse($("#providers" ).text());
    //     var catId = $(this).val();
    //     if(parseInt(catId) > 0){
    //         var cats = JSON.search( providers, '//*[provider_id="'+catId+'"]' );
    //         var catDesc = cats[0].category_desc;
    //         //console.log(catDesc);
    //         $('#description').text(catDesc);
    //     }
    // });
    
    $( "#case_no" ).bind( "change focusout", function() {
        var case_no = $(this).val();
        // console.log('caseno: '+case_no);
        $('#case_type').val('');
        $('#entity_name').val('');
        $.ajax({
            url: "/crm/case/"+case_no,
            context: document.body
        }).done(function(resp) {
            var obj = JSON.parse(resp);
            if(obj !== null){
                if(obj.hasOwnProperty('account_name')){
                    $('#entity_name').val(obj.account_name);
                }
                if(obj.hasOwnProperty('request_type_c')){
                    $('#case_type').val(obj.request_type_c);
                }
                //$('#description').val(obj.description);
            }
        });
    });

    $('div#panelListTask').on("click",'#openAddTask', function(){
        openFormTask("add");
    });

    $('div#taskViewOther').on("change",'#status_id', function(){
        // console.log($(this).val());
        if($(this).val() == 1) {
            editAction($("#task_id").val(), $(this).val());
            $('.form-group-resolved-datetime').show();
        } else if($(this).val() == 2) {
            completeAction($("#task_id").val());
        } else {
            editAction($("#task_id").val(), $(this).val());
            $('.form-group-resolved-datetime').hide();
        }
    });

    function editAction(taskId, status) {
        var taskID = taskId;
        // console.log("Task ID: "+taskID);
        openFormTask("edit");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support-spki/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);

            $("#task_id").val(obj.task_id);
            $("#provider_id").val(obj.provider_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            // $("#resolution").text(obj.resolution);

            if(status == null) {
                $("#status_id").val(obj.status);
                if(obj.status > 0) {
                    $('.form-group-resolved-datetime').show();
                } else {
                    $('.form-group-resolved-datetime').hide();
                }
            } else {
                $("#status_id").val(status);
            }

            //set form
            switch (obj.status) {
                case null: //do nothing
                case 0:
                    $("#btn_save_span").text(" Set Response");
                    $("#btn_action").val("SET_RESPONSE");
                    break;
                case 1:
                    $("#btn_save_span").text(" Set Resolution");
                    $("#btn_action").val("SET_RESOLUTION");
                    $('.form-group-resolution').show();
                    $("#resolution").removeAttr("readonly");
                    break;
            }
           
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
                $(".form-group-view-modified").show();
            } else {
                $(".form-group-view-modified").hide();
            }

            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
                $(".form-group-view-completed").show();
            } else {
                $(".form-group-view-completed").hide();
            }

            // if(obj.created_datetime !== null){
            //     $("#viewCreatedDatetime").text(obj.created_date + ' ' + obj.created_time);
            // }

            if(obj.created_date !== null){
                $("#created_date").val(obj.created_date);
            } else {
                $("#created_date").val(getCurrentFormatedDate());
            }

            if(obj.created_time !== null){
                $("#created_time").val(obj.created_time);
            } else {
                $("#created_time").val(getCurrentFormatedTime());
            }

            if(obj.response_date !== null){
                $("#response_date").val(obj.response_date);
            } else {
                $("#response_date").val(getCurrentFormatedDate());
            }

            if(obj.response_time !== null){
                $("#response_time").val(obj.response_time);
            } else {
                $("#response_time").val(getCurrentFormatedTime());
            }

            if(obj.resolved_date !== null){
                $("#resolved_date").val(obj.resolved_date);
            } else {
                $("#resolved_date").val(getCurrentFormatedDate());
            }

            if(obj.resolved_time !== null){
                $("#resolved_time").val(obj.resolved_time);
            } else {
                $("#resolved_time").val(getCurrentFormatedTime());
            }
        });
    }

    $('td.action_table_task').on("click",'a.action_table_edit_task', function(){
        editAction($(this).attr('data-id'), null);
    });

    function completeAction(taskId) {
        $('#to-top').click();
        var taskID = taskId;
        // console.log("Task ID: "+taskID);
        openFormTask("complete");
        

        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support-spki/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);
            
            $("#task_id").val(obj.task_id);
            $("#provider_id").val(obj.provider_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            if(obj.resolution !== null){
                $("#resolution").text(obj.resolution);
            }
            $("#status_id").val(2);
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
                $(".form-group-view-modified").show();
            } else {
                $(".form-group-view-modified").hide();
            }

            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
                $(".form-group-view-completed").show();
            } else {
                $(".form-group-view-completed").hide();
            }

            if(obj.created_date !== null){
                $("#created_date").val(obj.created_date);
            } else {
                $("#created_date").val(getCurrentFormatedDate());
            }
            if(obj.created_time !== null){
                $("#created_time").val(obj.created_time);
            } else {
                $("#created_time").val(getCurrentFormatedTime());
            }

            if(obj.response_date !== null){
                $("#response_date").val(obj.response_date);
            } else {
                $("#response_date").val(getCurrentFormatedDate());
            }
            if(obj.response_time !== null){
                $("#response_time").val(obj.response_time);
            } else {
                $("#response_time").val(getCurrentFormatedTime());
            }

            if(obj.resolved_date !== null){
                $("#resolved_date").val(obj.resolved_date);
            } else {
                $("#resolved_date").val(getCurrentFormatedDate());
            }

            if(obj.resolved_time !== null){
                $("#resolved_time").val(obj.resolved_time);
            } else {
                $("#resolved_time").val(getCurrentFormatedTime());
            }
        });
    }

    $('td.action_table_task').on("click",'a.action_table_complete_task', function(){
        completeAction($(this).attr('data-id'));
    });

    $('td.action_table_task').on("click",'a.action_table_view_task', function(){
        $('#to-top').click();
        var taskID = $(this).attr('data-id');
        // console.log("Task ID: "+taskID);
        openFormTask("view");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support-spki/task/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);

            $("#task_id").val(obj.task_id);
            $("#provider_id").val(obj.provider_id);
            $("#case_no").val(obj.case_no);
            $("#entity_name").val(obj.entity_name);
            $("#description").text(obj.description);
            $("#resolution").text(obj.resolution);
            $("#status_id").val(obj.status);
            $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
            if(obj.updated_by !== null){
                $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
            }
            if(obj.completed_by !== null){
                $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
            }
            $("#created_date").val(obj.created_date);
            $("#created_time").val(obj.created_time);
            $("#response_date").val(obj.response_date);
            $("#response_time").val(obj.response_time);
            $("#resolved_date").val(obj.resolved_date);
            $("#resolved_time").val(obj.resolved_time);
        });
    });

</script>
@endsection



