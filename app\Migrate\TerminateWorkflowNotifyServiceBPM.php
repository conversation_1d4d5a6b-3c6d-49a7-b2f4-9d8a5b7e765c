<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use DateTime;
use DateInterval;
use DatePeriod;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use Guzzle;
use GuzzleHttp\Client;


class TerminateWorkflowNotifyServiceBPM {

    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        self::terminateBPMTask();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function terminateBPMTask() {
  
        $begin = new DateTime('2018-01-01');
        $end = new DateTime('2018-12-31');

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);
        $counter = 0;
        foreach ($period as $dt) {
            $date = $dt->format("Y-m-d");
            dump('Query Date: '.$date);
            $listResult = self::getQuery($date);
            dump('Found Total: '.count($listResult));
            foreach ($listResult as $data){
                $counter++;
                dump($counter.") >> createdDate=".$data->createddate.", instanceId=".$data->compositeinstanceid." ,compositeModule=".$data->compositedn." ,componentInstanceID=".$data->instanceid);
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");  
                $response = Guzzle::get($urlMiddleware."/bpm/terminateSuspendWfNotifyService?instanceId=".$data->compositeinstanceid."&compositeModule=".$data->compositedn."&componentInstanceID=".$data->instanceid);
                dump(json_decode($response->getBody(), true));
            }
        }

        
    }
    
 
    protected static function getQuery($date) {
        dump("TerminateWorkflowNotifyServiceBPM > getQuery by : ".$date);
        $sql = DB::connection('oracle_bpm_rpt')->table('wftask')
                ->where('componentname', 'WorkflowNotificationService')
                ->where('state', 'SUSPENDED')
                ->where('createddate', '>=', DB::raw("TO_DATE('$date','YYYY-MM-DD')"))
                 ->where('createddate', '<=', DB::raw("TO_DATE('$date','YYYY-MM-DD')+1"));
        $result = $sql->get();
        return $result;
    }


}
