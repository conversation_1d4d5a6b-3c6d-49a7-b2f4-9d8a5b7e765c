<?php

namespace App\Services\Traits;

use Log;
use DB;
use Carbon\Carbon;


/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait NotificationService {

    /**
     * Get list of record batch no with has more than 30k records of users
     * @param type $recordsNo
     * @return List
     */  
    protected function getQueryListNotificationBulkMessageHugeRecords($recordsNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
           " select notify_message_id,m.batch_no, m.batch_record_no ,m.subject_param, m.remark , m.CREATED_DATE, m.changed_date,m.retry_count, m.is_sent, m.NOTIFICATION_ID from pm_notify_message m
            where m.notify_mode = 'B' and m.record_status = 1  and m.is_sent = 0
              and m.retry_count = 0 
              AND substr(subject_param,0,2) = 'QT'
              and m.batch_record_no >= ?  ", array($recordsNo));        

        return $results;
    }

    
    
    /**
     * Get backlog list of record batch no with has more than 30k records of users that manually SKIP to be RUN. 
     * @param type $recordsNo
     * @return List
     */  
    protected function getQueryBackLogSkipListNotificationBulkMessageHugeRecords($recordsNo) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
           " select notify_message_id,m.batch_no, m.batch_record_no ,m.subject_param, m.remark , m.CREATED_DATE, m.changed_date,m.retry_count, m.is_sent, m.NOTIFICATION_ID from pm_notify_message m
                where m.notify_mode = 'B' and m.record_status = 1  and m.is_sent = 1
                  and m.retry_count = 0
                  and trunc(m.CREATED_DATE) > to_date('2019-10-01','YYYY-MM-DD')   -- Skip backlog previous data.. 
                  and m.batch_record_no >= ? 
                  AND substr(subject_param,0,2) = 'QT' 
                  order by m.batch_no asc", array($recordsNo));        

        return $results;
    }

    protected function getCountBulkMessageNotQT() {
        $results = DB::connection('oracle_nextgen_rpt')->select(
           "SELECT
           count(*) AS total
           FROM PM_NOTIFY_MESSAGE m
           WHERE  
           notify_mode = 'B' 
           AND is_sent = 0 
           AND ( substr(subject_param,0,2) <> 'QT' OR subject_param IS NULL) ");        
    
        return $results[0]->total;
    }

    
    
    

}
