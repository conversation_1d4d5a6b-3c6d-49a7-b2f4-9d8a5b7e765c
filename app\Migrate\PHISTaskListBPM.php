<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService;


class PHISTaskListBPM {
    use BPMService;
    
    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        //self::insertFromExcel();
        self::updateBPMTask();
        //self::getUpdateBPMTask();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function updateBPMTask() {
        $list = DB::connection('mysql_ep_support')->table('ep_phis_task')
                ->whereIn('status_name',['Pending Supplier Acknowledgement','Pending Invoice'])
                ->get();
        foreach ($list as $obj) {
            $th = new PHISTaskListBPM;
            //Checking status already changed and not stuck
            $resList = $th->getTaskBpmByDocNo($obj->co_po_number);
            if ($resList && count($resList) > 0) {
                
                if($obj->status_name == 'Pending Supplier Acknowledgement'){
                    $objBpm = $resList->whereIn('activityname', ['Acknowledge CO','Acknowledge PO'])->first();
                    if($objBpm){
                        $assigneeListTemp = $objBpm->assignees;
                        $assigneeListTemp2 = str_replace(":", "", $assigneeListTemp);
                        $assigneeList = str_replace("user", "", $assigneeListTemp2);

                        DB::connection('mysql_ep_support')
                                ->table('ep_phis_task')
                                ->where('id', $obj->id)
                                ->update([
                                    'task_to' => $assigneeList,
                                    'claim_by' => $objBpm->acquiredby,
                                    'composite_id' => $objBpm->compositeinstanceid,
                                    'composite_ver' => $objBpm->taskdefinitionid
                        ]);
                        dump('Updated Acknowledge POCO: '.$obj->co_po_number. ' , claim_by : '.$objBpm->acquiredby.'  ####: Assignees :::-> '.$assigneeList);  

                    }
                }
                if($obj->status_name == 'Pending Invoice'){
                    $objBpmInvoice = $resList->where('activityname', 'Create Invoice')->first();
                    if($objBpmInvoice){
                        $assigneeInvListTemp = $objBpmInvoice->assignees;
                        $assigneeInvListTemp2 = str_replace(":", "", $assigneeInvListTemp);
                        $assigneeInvList = str_replace("user", "", $assigneeInvListTemp2);

                        DB::connection('mysql_ep_support')
                                ->table('ep_phis_task')
                                ->where('id', $obj->id)
                                ->update([
                                    'task_to' => $assigneeInvList,
                                    'claim_by' => $objBpmInvoice->acquiredby,
                                    'composite_id' => $objBpmInvoice->compositeinstanceid,
                                    'composite_ver' => $objBpmInvoice->taskdefinitionid
                        ]);
                        dump('Updated Create Invoice : '.$obj->co_po_number. ' , claim_by : '.$objBpmInvoice->acquiredby.'  ####: Assignees :::-> '.$assigneeInvList);  

                    }
                }
                dump('');
            }    
        }
    }
    
    protected static function getBPMTask() {

        $list = DB::connection('mysql_ep_support')->table('ep_phis_stuck')->get();
        foreach ($list as $obj) {
            $th = new PHISStuckList;
            
            $resListCr = $th->getTaskBpmByDocNo($obj->doc_cr);
            if ($resListCr && count($resListCr) > 0) {
                $objBpm2 = $resListCr->where('activityname', 'Review CR')->first();
                if($objBpm2){
                    dump('Get Instance only : '.$obj->doc_co);
                    DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'bpm_instance' =>  $objBpm2->compositeinstanceid,
                                'bpm_composite' => $objBpm2->taskdefinitionid
                    ]);
                }
            }
                
            $resList = $th->getTaskBpmByDocNo($obj->doc_co);
            if ($resList && count($resList) > 0) {
                $objBpm = $resList->where('activityname', 'Acknowledge CO')->first();
                if($objBpm){
                    dump('Fixed Done : '.$obj->doc_co);
                    DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'bpm_instance' => $objBpm->compositeinstanceid,
                                'status' => '1'
                    ]);
                }
            } 
        }
    }

    protected static function insertFromExcel() {
        $filename = '/app/Migrate/data/pharmaniaga-status.xls';
        Excel::load($filename, function($reader){
            
            $reader->each(function($row) {
                //dump($row);
                //$requestDateObj = Carbon::createFromFormat('d/m/Y H:i:s', $row->request_date);
                //dump($requestDateObj->format('Y-m-d H:i:s'));
                
                DB::connection('mysql_ep_support')
                        ->table('ep_phis_task')->insert(
                        [
                            
                            //'request_date' => $requestDateObj,
                            'co_po_number' => trim($row->co_po_number),
                            'co_po_type' => trim($row->co_po_type),
                            'cr_pr_number' => trim($row->cr_pr_number),
                            'cr_pr_type' => trim($row->cr_pr_type),
                            'total_amount' => trim($row->total_amount),
                            'status_name' => trim($row->status_name),
                            'ptj_code' => trim($row->ptj_code),
                            'ptj_name' => trim($row->ptj_name),
                            'state_name' => trim($row->state_name)
                        ]
                );
                dump('  Success Insert : '.$row->co_po_number);
            });
        });
        
    }


}
