@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/logtrace-v1.0.css') }}" rel="stylesheet" />
@endsection

@section('content')
    <div class="block log-header">
        <div class="row">
            <div class="col-md-1">
                <div class="log-trace-icon">eP</div>
            </div>
            <div class="col-md-4 log-header-title">
                <span>Log Trace Dashboard<br /></span>
                <small>To View Log Trace Metric</small>
            </div>
            <div class="col-md-6 log-header-menu">
                <a href="/home"><i class="fa fa-home"></i></a> |
                <a href="/log/dashboard">Dashboard</a> |
                <a href="/log">User Activity</a> |
                <a href="/log/login-history">Login History</a> |
                @if (Auth::user()->isDevUsersEp()) <a href="/log/patch">Patch
                        Management</a> | <span class="active">Internal Dashboard</span> @endif
            </div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="row">
        <span class="pull-right">
            <div class="form-control form-inline"
                style="background: #1b213b; border-style:none; color:#fff; padding-right:30px;">
                <label>Filter Date: </label>
                <input type="text" id="filter-dt" class="form-control input-datepicker" name="filter-dt"
                    data-date-format="yyyy-mm-dd" style="padding: 5px; text-align: center;" placeholder="yyyy-mm-dd"
                    value="{{ Carbon\Carbon::now()->format('Y-m-d') }}">
                <a href="javascript:void(0)" id="refresh-log" class="btn btn-sm" data-toggle="tooltip" title=""
                    data-original-title="Refresh"><i class="fa fa-refresh"></i></a>
            </div>
        </span>
    </div>
    <div class="row">
        <div class="col-md-4">
            <div class="card" id="chart-box">
                <div class="card-body">
                    <div id="chart_node"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card" id="chart-box">
                <div class="card-body">
                    <div id="chart_top10_user"></div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card" id="chart-box">
                <div class="card-body">
                    <div id="chart_top10_url"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <span class="chart-title">Daily Request By User</span>
                </div>
                <div class="card-body" id="log-bg-table">
                    <table id="daily_access_by_user" class="table table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align: center;">User</th>
                                <th style="text-align: center;">Total</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <span class="chart-title">Daily Request By URL</span>
                </div>
                <div class="card-body" id="log-bg-table">
                    <table id="daily_req_by_furl" class="table table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th style="text-align: center;">URL</th>
                                <th style="text-align: center;">Total</th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script src="{{ asset('js/vendor/apexcharts.js') }}"></script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!};

        function construct_top10_user_chart(users) {
            let values = [];
            let labels = [];
            if (users.length > 0) {
                users.forEach(function(user) {
                    values.push(user.total);
                    labels.push(user.login_id);
                });
            }

            construct_bar_chart(values, labels, '#chart_top10_user', 'Top 10 Request By User');
        }

        function get_daily_request_by_user(filter_dt) {
            $('#daily_access_by_user').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $('#chart_top10_user').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $.ajax({
                url: APP_URL + '/log/daily/by/user',
                type: "GET",
                data: {
                    filter_dt
                },
                success: function(users) {
                    let top10user = [];
                    var content = `<thead><tr>
                                    <th style="text-align: center;">User</th>
                                    <th style="text-align: center;">Total</th>
                                </tr></thead>`;
                    content += `<tbody>`;
                    if (users.length > 0) {
                        users.forEach(function(stat) {
                            content += `<tr><td style="text-align: center;">${stat.login_id}</td>`;
                            content += `<td style="text-align: center;">${stat.total}</td></tr>`;
                        });

                        top10user = users.sort((a, b) => b.total - a.total).slice(0, 10);
                    }

                    construct_top10_user_chart(top10user);

                    content += `</tbody>`;
                    $('#daily_access_by_user').hide().html(content).fadeIn();

                    $('#daily_access_by_user').dataTable({
                        destroy: true,
                        order: [
                            [1, "desc"]
                        ]
                    });
                },
                error: function(error) {
                    $('#daily_access_by_user').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function construct_bar_chart(values, labels, id, chart_title) {
            window.Apex = {
                chart: {
                    foreColor: "#fff",
                    toolbar: {
                        show: false
                    }
                },
                colors: ["#FCCF31", "#17ead9", "#f02fc2"],
                stroke: {
                    width: 3
                },
                dataLabels: {
                    enabled: false
                },
                grid: {
                    borderColor: "#40475D"
                },
                xaxis: {
                    axisTicks: {
                        color: "#333"
                    },
                    axisBorder: {
                        color: "#333"
                    }
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        gradientToColors: ["#F55555", "#6078ea", "#6094ea"]
                    }
                },
                tooltip: {
                    theme: "dark",
                },
                yaxis: {
                    decimalsInFloat: 2,
                    opposite: true,
                    labels: {
                        offsetX: -10
                    }
                }
            };

            var options = {
                chart: {
                    width: "100%",
                    height: 380,
                    type: "bar"
                },
                plotOptions: {
                    bar: {
                        horizontal: true,
                    }
                },
                dataLabels: {
                    enabled: true
                },
                stroke: {
                    width: 0,
                },
                series: [{
                    name: "Total",
                    data: values
                }],
                title: {
                    text: chart_title,
                    align: "left",
                    style: {
                        fontSize: "12px"
                    }
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "dark",
                        type: "horizontal",
                        shadeIntensity: 0.5,
                        inverseColors: false,
                        opacityFrom: 1,
                        opacityTo: 0.8,
                        stops: [0, 100]
                    }
                },
                xaxis: {
                    categories: labels,
                    labels: {
                        show: false
                    }
                },
                legend: {
                    show: true,
                    position: "right",
                    verticalAlign: "top"
                },
                responsive: [{
                    breakpoint: 1000,
                    options: {
                        plotOptions: {
                            bar: {
                                horizontal: false
                            }
                        },
                        legend: {
                            position: "bottom"
                        }
                    }
                }]
            };

            $(id).hide().html('').fadeIn();
            var chart = new ApexCharts(document.querySelector(id), options);
            chart.render();
        }

        function get_daily_request_by_node(filter_dt) {
            $('#chart_node').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $.ajax({
                url: APP_URL + '/log/daily/by/node',
                type: "GET",
                data: {
                    filter_dt
                },
                success: function(data) {
                    let nodes = [];
                    let node_labels = [];
                    if (data.length > 0) {
                        data.forEach(function(stat) {
                            if (stat.server_node != null) {
                                nodes.push(stat.total);
                                node_labels.push(stat.server_node);
                            }
                        });
                    }

                    construct_bar_chart(nodes, node_labels, '#chart_node', 'Total Request By Node');
                },
                error: function(error) {
                    $('#chart_node').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function construct_top10_url_chart(urls) {
            let values = [];
            let labels = [];
            if (urls.length > 0) {
                urls.forEach(function(url) {
                    values.push(url.total);
                    labels.push(url.friendly_url);
                });
            }

            construct_bar_chart(values, labels, '#chart_top10_url', 'Top 10 Request By URL');
        }

        function get_daily_request_by_url(filter_dt) {
            $('#daily_req_by_furl').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $('#chart_top10_url').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>"
                )
                .fadeIn();
            $.ajax({
                url: APP_URL + '/log/daily/by/furl',
                type: "GET",
                data: {
                    filter_dt
                },
                success: function(urls) {
                    let top10url = [];
                    var content = `<thead><tr>
                                    <th style="text-align: center;">URL</th>
                                    <th style="text-align: center;">Total</th>
                                </tr></thead>`;
                    content += `<tbody>`;
                    if (urls.length > 0) {
                        urls.forEach(function(stat) {
                            content += `<tr><td style="text-align: center;">${stat.friendly_url}</td>`;
                            content +=
                                `<td style="text-align: center;">${stat.total}</td></tr>`;
                        });

                        top10url = urls.sort((a, b) => b.total - a.total).slice(0, 10);
                    }

                    construct_top10_url_chart(top10url);

                    content += `</tbody>`;
                    $('#daily_req_by_furl').hide().html(content).fadeIn();
                    $('#daily_req_by_furl').dataTable({
                        destroy: true,
                        order: [
                            [1, "desc"]
                        ]
                    });
                },
                error: function(error) {
                    $('#daily_req_by_furl').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        $('#filter-dt').on('change', function() {
            let dt = $('#filter-dt').val();
            get_daily_request_by_user(dt);
            get_daily_request_by_node(dt);
            get_daily_request_by_url(dt);
        })

        $('#refresh-log').on('click', function() {
            let dt = $('#filter-dt').val();
            get_daily_request_by_user(dt);
            get_daily_request_by_node(dt);
            get_daily_request_by_url(dt);
        })

        $(document).ready(function() {
            $('#page-container').removeAttr('class');

            get_daily_request_by_user();
            get_daily_request_by_node();
            get_daily_request_by_url();
        });
    </script>
@endsection
