@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/dashboard/main/') }}">Main</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/batch/') }}">Batch File</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/osb/') }}">OSB</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/igfmas/') }}">IGFMAS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/phis/') }}">PHIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/paymentreceipt/') }}">Payment AR502</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/gfmascrm/') }}">CRM</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/mygpis/') }}">MyGPIS</a>
            </li>
            <li class="active">
                <a href="{{ url('/dashboard/egpa/') }}">EGPA</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/spki/') }}">SPKI</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    Monitoring <strong>eGPA</strong>
                </h5>
            </div>
            <div id="dash_monitoringEgpa">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
<div class="row"> 
    <div class="col-lg-6">
        <div id="dash_outbound_egpa" class="widget">
            <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
        </div>
    </div>
</div>
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                    <div class="pull-right">
                        <a href="{{ url("/list/1gfmas/folder") }}" target="_blank" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger Batch</a>
                    </div>
                </div>
                <div id="trigger_btn" class="row" style="padding: 0 15px 15px 0; display: none;" disabled>
                    <div class="pull-right">
                        <a id="trigger_url"></a>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });


    $(document).ready(function () {

        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));
            if ($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound') {
                $('#fetch_btn').show();
                $('#trigger_btn').hide();
                console.log('fetch');
            } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllService') {
                $('#trigger_btn').show();
                $('#fetch_btn').hide();
                $html = "<a href='{{ url('/list/osb/batch/retry/trigger/service') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title=''\n\
                            data-original-title='Trigger All Service'\n\
                            > Trigger All Service</a>";
                $("#trigger_url").html($html);
                console.log('trigger service');
            } else if ($(this).attr('data-url') === '/list/osb/batch/retry/AllBatch') {
                $('#trigger_btn').show();
                $('#fetch_btn').hide();
                $html = "<a href='{{ url('/list/osb/batch/retry/trigger/batch') }}' \n\
                            target='_blank' class='btn btn-sm btn-primary toggle-bordered enable-tooltip' title='' \n\
                            data-original-title='Trigger All Batch'\n\
                            > Trigger All Batch</a>";
                $("#trigger_url").html($html);
                console.log('trigger batch');
            } else {
                $('#trigger_btn').hide();
                $('#fetch_btn').hide();
                console.log('hide all');
            }

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        //Monitoring eGPA
        $.ajax({
            url: APP_URL + '/dashboard/checkMonitoringEgpa',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_monitoringEgpa').hide().html($data).fadeIn();
            }
        });
        //eGPA Monitoring (OUTBOUND)
        $.ajax({
            url: APP_URL + '/dashboard/batch/outbound?batch_name=eGPA&length_filename=5',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_outbound_egpa').hide().html($data).fadeIn();
            }
        });
    });
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
