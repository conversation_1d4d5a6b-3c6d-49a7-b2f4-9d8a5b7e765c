@extends('layouts.guest-dash')
@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-5 log-header-title">
            <span>Transaction Logs (IGFMAS)<br /></span>
            <small>To View Log OSB. Search by PR,CR,PO,CO,DN,FN,SD, Invoice No (17 Digits Start with 600), IC No (12 Digit Only), eP No, Kod item (18 Digit Only)</small>
        </div>
        <div class="col-md-6 log-header-menu">
            <a href="{{url('find/osb/log')}}"><span class="{{ Request::is('find/osb/log') ? 'active' : '' }}">Log</span></a> |
            <a href="{{url('find/osb/detail/log')}}"><span class="{{ Request::is('find/osb/detail/log') ? 'active' : '' }}">Log Detail</span></a> |
            <a href="{{url('find/osb/detail-rquid/log')}}"><span class="{{ Request::is('find/osb/detail-rquid/log') ? 'active' : '' }}">Log RQ-UID</span></a> | 
            <a href="{{url('find/osb/batch/file')}}"><span class="{{ Request::is('find/osb/batch/file') ? 'active' : '' }}">Batch File Log</span></a> | 
            <a href="{{url('osb/file/content/search')}}"><span class="{{ Request::is('osb/file/content/search') ? 'active' : '' }}">Find Content File</span></a> |
            <a href="{{url('find/osb/error')}}"><span class="{{ Request::is('find/osb/error') ? 'active' : '' }}">Error Log</span></a> | 
            <a href="{{url('find/1gfmas/ws')}}"><span class="{{ Request::is('find/1gfmas/ws') ? 'active' : '' }}">Log (IGFMAS)</span></a> | 
            <a href="{{url('find/phis/ws')}}"><span class="{{ Request::is('find/phis/ws') ? 'active' : '' }}">Log (PHIS)</span></a> 
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="block">
    <form class="form-horizontal" id="carianform" action="{{url('/find/1gfmas/ws')}}/" method="get" >
        <div class="row">
            <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cari">Carian / Search </label>
                        <div class="col-md-6">
                            <input type="text" id="cari" name="cari" class="form-control" value="{{$carian}}"  onfocus="this.select();" placeholder="Klik carian di sini ... ">
                        </div>
                    </div>
            </div>
        </div>
    </form>
</div>

<div class="content-header">
    
    <!-- Log Block -->
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Log</strong> 1GFMAS Info</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td>GFM-020</td>
                            <td class="text-center">MMINF</td>
                            <td class="text-success"><strong>MasterDataMaterial</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-080</td>
                            <td class="text-center">MM503</td>
                            <td class="text-info"><strong>DebitAdviceNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-100</td>
                            <td class="text-center">MM501</td>
                            <td class="text-success"><strong>POContractForGoodsAndServices</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-110</td>
                            <td class="text-center">MM504</td>
                            <td class="text-danger"><strong>FulfillmentReceivingNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-120</td>
                            <td class="text-center">MM506</td>
                            <td><strong>PaymentInstruction</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-130  ||  EPP-013</td>
                            <td class="text-center">MM507</td>
                            <td><strong>ChargingCheckingStatus</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-170</td>
                            <td class="text-center">MM513</td>
                            <td><strong>StopInstruction</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- END Log Content -->
        </div>
        <!-- END Log Block -->

</div>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              <p>Tidak dijumpai!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        
    
        <ul class="text-info">
            <li>IBReq : Refering to request</li>
            <li>IBRes : Refering to response  </li>
            <li>Click link in column service code to view each payload for transaction type.  </li>
        </ul>
        <div class="table-responsive">
            <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">STATUS CODE</th>
                        <th class="text-center">STATUS DESC</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                        <th class="text-center">PAYLOAD</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center"><a href="{{url('/find/osb/detail/log')}}?cari={{$data->trans_id }}" target="_blank" >{{$data->trans_id }}</a></td>
                        <td class="text-center">{{ $data->trans_type }}</td>
                        <td class="text-center">{{$data->service_code }}</td>
                        <td class="text-center">{{ $data->trans_date }}</td>
                        <td class="text-left">{{ App\Services\EPService::$OSB_STATUS[$data->status] }}</td>
                        <td class="text-left">{{ $data->status_code }}</td>
                        <td class="text-left">
                            @include('_shared._infoDetailErrorDescription')
                        </td>
                        <td class="text-center">{{ $data->remarks_1 }}</td>
                        <td class="text-center">{{ $data->remarks_2 }}</td>
                        <td class="text-center">{{ $data->remarks_3 }}</td>
                        <td class="text-center">
                            <a href="#modal-list-data-detail" class="modal-list-data-action " 
                               data-toggle="modal" data-url="{{url('/support/report/log-detail/osb-payload')}}/" 
                               data-carian="@if(isset($carian)){{$data->logging_id}}@endif" 
                               data-title="Payload Data">Click Here</a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->

</div>
@endif

@include('_shared._modalDetailInfo')

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


@endsection
