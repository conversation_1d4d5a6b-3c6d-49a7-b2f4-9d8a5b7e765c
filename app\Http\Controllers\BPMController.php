<?php

namespace App\Http\Controllers;

use App\Services\Traits\BPMService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\InvoiceService;
use Validator;
use DB;
use Carbon\Carbon;

class BPMController extends Controller {

    use BPMService;
    use SupplierService;
    use FulfilmentService;
    use InvoiceService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function getListTaskBpm(){
        return view('list_task_bpm', [
            'listdata' => null,
            'carian' => '']);
    }
    public function getListTaskBpmByDocNo($docNo){
        $list = $this->getTaskBpmByDocNo($docNo);
        return view('list_task_bpm', [
            'listdata' => $list,
            'carian' => $docNo]);
    }
    public function getListCompositeInstanceId(){
        
        if(count(request()->all()) == 0){
            return view('bpm.list_instance_bpm', [
            'listdata' => null]);
        }
        
        Validator::make(request()->all(), [
                'doc_id' => 'required'
            ])->validate();
 
        $docId = request()->doc_id;
        $list = $this->getInstanceIdBPMbyDocId($docId);
        
        session()->flashInput(request()->input());
        return view('bpm.list_instance_bpm', [
            'listdata' => $list]);
    }
    

    public function getListStuckTaskInvalidCompleted(){
        
        if(count(request()->all()) == 0){
            return view('bpm.list_stuck_task_bpm', [
            'listdata' => null]);
        }
        
        Validator::make(request()->all(), [
                'composite_name' => 'required'
            ])->validate();
        $compositeName = request()->composite_name;
        $list = $this->findListStuckInvalidCompletedByComposite($compositeName);
        
        session()->flashInput(request()->input());
        return view('bpm.list_stuck_task_bpm', [
            'listdata' => $list]);
    }


    public function getListSmStuckTask(){
        $list = collect([]);
        
        $listOne = $this->getFailedTaskApplNoInitiate();
        foreach($listOne as $obj1){
           $list->push($obj1) ;
        }
        
        $listTwo = $this->getFailedApplNoTaskProcessingFeeAdv();
        foreach($listTwo as $obj2){
           $list->push($obj2) ;
        }

        $listThree = $this->getFailedApplNoTaskToSDO();
        foreach($listThree as $obj3){
           $list->push($obj3) ;
        }
        
        $listFour = $this->getFailedApplNoTaskToProcessingFee();
        foreach($listFour as $obj4){
           $list->push($obj4) ;
        }
        
        $listFive = $this->getFailedApplNoTaskToPaymentRegistration();
        foreach($listFive as $obj5){
           $list->push($obj5) ;
        }
        
        $listSix = $this->getFailedApplNoTaskToPaymentRegistrationOther();
        foreach($listSix as $obj6){
           $list->push($obj6) ;
        }
        
        $listSeven = $this->getFailedApplNoTaskToPaymentRegistrationOther2();
        foreach($listSeven as $obj7){
           $list->push($obj7) ;
        }
        
        $listEight = $this->getFailedApplNoTaskToProcessingFeeOther();
        foreach($listEight as $obj8){
           $list->push($obj8) ;
        }
        
        
        $listNine = $this->getStuckPaymentRegistrationIsPaid();
        foreach($listNine as $obj9){
           $list->push($obj9) ;
        }
        
        $listTen = $this->getStuckPendingReviewApplication();
        foreach($listTen as $obj10){
           $list->push($obj10) ;
        }

        if($list != null){
            foreach ($list as $obj){
                $obj->instance_bpm = '';
                $obj->composite_bpm = '';
                $obj->flow_id = '';
                $hourNow = Carbon::now()->hour;
                //if($hourNow > 6 && $hourNow < 20){
                    
                    $searchDocno = $obj->appl_no;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }
                    $task = $this->getTaskDetailBpmByDocNo($searchDocno);
                    //$task = null;
                    if ($task != null) {
                        $obj->instance_bpm = $task->compositeinstanceid;
                        $obj->composite_bpm = $task->taskdefinitionid;
                        $obj->flow_id =  $task->flow_id;
                    }
                //}
            }
        }
        
        return view('sm.list_sm_stuck_task', ['listdata' => $list]);
    }
    
    public function getListFlStuckTask(){

        $list = $this->getFailedTaskPRCRInitiate();
        foreach ($list as $obj){

            $tracDiary = $this->getLatestTrackingDiaryDetail($obj->doc_no);
            if($tracDiary){
                $obj->tracking_diary_status = $tracDiary->status_name;
            }else{
                $obj->tracking_diary_status = '';
            }
        }
        return view('fl.list_fl_stuck_task', ['listdata' => $list]);
    }
    
    public function getListFlStuckTaskPendingApproval(){

        $list = $this->getFailedTaskPRCRPendingApproval();
       
        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
           
            if($obj->doc_type=='CR'){
                $task = $this->getTaskDetailBpmByDocNo($obj->doc_no );
            }else{
                $task = $this->getTaskDetailBpmByDocNo($obj->request_note_no );
            }
            //$task = null;
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
            
        }
        return view('fl.list_fl_pending_approval_stuck_task', ['listdata' => $list]);
    }

    public function getListPrStuckEpp013Y(){

        $list = $this->getPRStuckEpp13Y();

        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $searchDocno = $obj->prcr;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }

            $task = $this->getTaskDetailBpmByDocNo($searchDocno);
            
            //$task = null;
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
            
        }
        
       
        return view('fl.list_fl_stuck_epp_13_y', ['listdata' => $list]);
    }

    public function getListCancelPOCO(){

        $list = $this->getFailedListStuckTaskCancelPOCO();
        foreach ($list as $obj){
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            //if($date->hour < 19 && $date->hour > 6){
                $task = $this->getTaskDetailBpmByDocNo($obj->doc_no);
                if ($task != null) {
                    $obj->instance_bpm = $task->compositeinstanceid;
                    $obj->composite_bpm = $task->taskdefinitionid;
                    $obj->flow_id =  $task->flow_id;
                }
            //}
        }
        
       
        return view('fl.list_fl_stuck_cancel_poco', ['listdata' => $list]);
    }

    public function getListPrStuckMM501Del(){

        $list = $this->getPRStuckMM501Del();
        
       
        return view('fl.list_fl_stuck_mm_501_del', ['listdata' => $list]);
    }
    
    public function getListFLStuckTaskInitiateDO(){

        $list = $this->getFailedListInitiateTaskDO();
        return view('fl.list_fl_do_stuck_task', ['listdata' => $list]);
    }
    
    public function getListFLStuckTaskPendingInvoice() {
        $list = $this->getStuckPendingInvoice();
        return view('fl.list_fl_pending_invoice_stuck_task', ['listdata' => $list]);
    }
    
    public function getListFLPendingPaymentEpp017Y(){

        $list = $this->getFLPendingPaymentEpp017Y();

        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $searchDocno = $obj->remarks_2;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }

            $task = $this->getTaskDetailBpmByDocNo($searchDocno);
            
            //$task = null;
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
            
        }
        
       
        return view('fl.list_fl_stuck_fl_pending_payment_epp_017_y', ['listdata' => $list]);
    }

    public function getListStuckYEPCF(){

        $list = $this->getStuckYEPCF();

        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $searchDocno = $obj->poco;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }

            // $task = $this->getTaskDetailBpmByDocNo($searchDocno);
            $status = $this->getStatusInfo($searchDocno);
            
            //$task = null;
            if ($status != null) {
                $obj->yep_task_id = $status[0]->yep_task_id;
                $obj->progress_status = $status[0]->progress_status;
                $obj->status_name =  $status[0]->status_name;
            }
            
        }
        
       
        return view('fl.list_fl_stuck_yep_cf', ['listdata' => $list]);
    }

    public function getListDuplicateInvoice(){

        $list = $this->getDuplicateInvoice();
        return view('fl.list_fl_duplicate_invoice', ['listdata' => $list]);
    }

    public function getListFLStuckTaskPendingPaymentMatch() {
        $list = $this->getStuckPendingPaymentMatch();
        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $hourNow = Carbon::now()->hour;
            //if($hourNow > 6 && $hourNow < 20){

                $searchDocno = $obj->doc_no;
                if(isset($obj->search_bpm)){
                    $searchDocno = $obj->search_bpm;
                }
                $task = $this->getTaskDetailBpmByDocNo($searchDocno);
                //$task = null;
                if ($task != null) {
                    $obj->instance_bpm = $task->compositeinstanceid;
                    $obj->composite_bpm = $task->taskdefinitionid;
                    $obj->flow_id =  $task->flow_id;
                }
            //} 
        }
        
        return view('fl.list_fl_pending_payment_match_stuck_task', ['listdata' => $list]);
    }
    
    
    public function getListDpSQStuckTask(){
        $date = Carbon::now();
        $list = collect($this->getFailedTaskSourcingDPSQCreateSimpleQuote());
        $listTrackSqClose = $this->getFailedTrackSQClose();
        foreach($listTrackSqClose as $obj1){
            $list->push($obj1) ;
        }
        foreach ($list as $obj){
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            //if($date->hour < 19 && $date->hour > 6){
                $task = $this->getTaskDetailBpmByDocNo($obj->doc_no);
                if ($task != null) {
                    $obj->instance_bpm = $task->compositeinstanceid;
                    $obj->composite_bpm = $task->taskdefinitionid;
                    $obj->flow_id =  $task->flow_id;
                }
            //}
        }
        return view('dp.list_dp_sq_stuck_task', ['listdata' => $list]);
    }
    
    public function getListDpRNStuckTask(){

        $list = $this->getFailedTaskSourcingDPRNPendingApproval();
        foreach ($list as $obj){
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            if($obj->quote_no != ''){
                $task = $this->getTaskDetailBpmByDocNo($obj->quote_no);
            }else{
                $task = $this->getTaskDetailBpmByDocNo($obj->request_note_no); 
            }
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
        }
        return view('dp.list_dp_rn_stuck_task', ['listdata' => $list]);
    }

    public function getListRNStuckTaskTriggerOrder(){

        $list = $this->getFailedTaskRNTriggerOrder();
        foreach ($list as $obj){
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $compositeName = 'SourcingDP';
            if($obj->doc_no != ''){
                $task = $this->getTaskDetailBpmByDocNo($obj->doc_no,$compositeName);
            }
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
        }
        return view('dp.list_rn_trigger_order_stuck_task', ['listdata' => $list]);
    }
    
    public function getListDpCodificationStuckTask(){

        $list = $this->getFailedTaskSourcingDPRNCodification();
        $arrayFilterOut = array();
        foreach ($list as $obj){
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $hourNow = Carbon::now()->hour;
            //if($hourNow > 6 && $hourNow < 20){
                    
                $task = $this->getTaskDetailBpmByDocNo($obj->doc_no); 

                if ($task != null) { 

                    $obj->instance_bpm = $task->compositeinstanceid;
                    $obj->composite_bpm = $task->taskdefinitionid;
                    $obj->flow_id =  $task->flow_id;

                    //When Task BPM Show -->> PrepareLetter , Actually still in Normal Flow. 
                    if(str_contains($obj->composite_bpm,'PrepareLetter') == true){
                        array_push($arrayFilterOut,$obj->doc_no);
                    }
                }
            //}
        }
        
        $listFilter = collect($list);
        $listFilterData = $listFilter->whereNotIn('doc_no', $arrayFilterOut);
        
        return view('dp.list_dp_codification_stuck_task', ['listdata' => $listFilterData]);
    }
    
    public function getListIntegrationStuckTask(){
        $list = null;
        if(request()->type == 'PRCR'){
            $list = $this->getFailedListIntegrationPRCRApprovalGFMASServiceCall();
        }
        if(request()->type == 'FRN'){
            $list = collect([]);
            $listOne = $this->getFailedListIntegrationFRNApprovalGFMASServiceCall();
            foreach($listOne as $obj1){
               $list->push($obj1) ;
            }
            $listTwo = $this->getFailedListStuckFRNOnSubmittion();
            foreach($listTwo as $obj2){
               $list->push($obj2) ;
            }
        }
        if(request()->type == 'DAN'){
            $list = $this->getFailedIntegrationDANApprovalGFMASServiceCall();
        }
        if(request()->type == 'SD'){
            $list = $this->getFailedListIntegrationSDApprovalGFMASServiceCall();
        }
        if(request()->type == 'PA'){
            $list = collect([]);
            $listOne = $this->getFailedIntegrationPaymentAdviceApproval();
            foreach($listOne as $obj1){
               $list->push($obj1) ;
            }
            $listTwo = $this->getFailedIntegrationPaymentAdviceApprovalGFMASServiceCall();
            foreach($listTwo as $obj2){
               $list->push($obj2) ;
            }

        }    
        if($list != null){
            foreach ($list as $obj){
                $obj->instance_bpm = '';
                $obj->composite_bpm = '';
                $obj->flow_id = '';
                $hourNow = Carbon::now()->hour;
                //if($hourNow > 6 && $hourNow < 20){
                    
                    $searchDocno = $obj->doc_no;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }
                    $task = $this->getTaskDetailBpmByDocNo($searchDocno);
                    //$task = null;
                    if ($task != null) {
                        $obj->instance_bpm = $task->compositeinstanceid;
                        $obj->composite_bpm = $task->taskdefinitionid;
                        $obj->flow_id =  $task->flow_id;
                    }
                //}
            }
        }
        return view('fl.list_stuck_awaiting_gfmas', 
                ['listdata' => $list,
                 'request' => request()->type
                ]);
    }
    

    public function getListStuckTask(){
        $list = null;
        if(request()->type == 'QUERY-PRCR'){
            $list = $this->getFailedListStuckTaskQueryPRCR();
        }
        if(request()->type == 'PEND-REVISION-APRV-PRCR'){
            $list = $this->getFailedListStuckTaskPendingRevisionApprovalPRCR();
        }
        if(request()->type == 'IGFMAS-PRCR'){
            $list = $this->getFailedListStuckTaskAwaitingIgfmasPRCR();
        }
        if(request()->type == 'ORDER-POCO'){
            $list = $this->getFailedListStuckTaskOrderPOCO();
        }
        if(request()->type == 'FL-DEBIT'){
            $list = $this->getFailedListStuckTaskFLDebit();
        } 
        if(request()->type == 'FL-IGFMAS-FRN'){
            $list = $this->getFailedListStuckTaskFrnAwaitingIgfmas();
        }    
        if($list != null){
            foreach ($list as $obj){
                $obj->instance_bpm = '';
                $obj->composite_bpm = '';
                $obj->flow_id = '';
                $hourNow = Carbon::now()->hour;
                //if($hourNow > 6 && $hourNow < 20){
                    
                    $searchDocno = $obj->doc_no;
                    if(isset($obj->search_bpm)){
                        $searchDocno = $obj->search_bpm;
                    }
                    $task = $this->getTaskDetailBpmByDocNo($searchDocno);
                    //$task = null;
                    if ($task != null) {
                        $obj->instance_bpm = $task->compositeinstanceid;
                        $obj->composite_bpm = $task->taskdefinitionid;
                        $obj->flow_id =  $task->flow_id;
                    }
                //}
            }
        }
        return view('fl.list_stuck_task_bpm', 
                ['listdata' => $list,
                 'request' => request()->type
                ]);
    }
    
    

}
