<?php

namespace App\Console\Commands\LogTrace;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\ExportLogService;

class HandleLogTraceDailyRequestByUser extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleLogTraceDailyRequestByUser';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To retrieve log for statistic daily request by user.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' Starting ..', ['Date' => Carbon::now()]);
        $expsvc = new ExportLogService();
        try {
            $expsvc->export_user_requests(Carbon::now());

            MigrateUtils::logDump(self::class . 'Completed', ['Date' => Carbon::now()]);
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $expsvc->sendErrorEmail("ERROR: $exc->getMessage()  => ".json_encode($exc->getTrace()));
        }
    }
}
