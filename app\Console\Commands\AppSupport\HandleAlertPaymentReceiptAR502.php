<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use App\Services\Traits\ProdSupport\PsDefectEpService;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use Mail;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;
use App\Services\Traits\PaymentReceiptService;
use App\Migrate\ReTriggerFileIGFMAS;
use App\Model\Notify\NotifyModel;
use App\Migrate\ClientRazerApi;

class HandleAlertPaymentReceiptAR502 extends Command {

    use PaymentReceiptService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleAlertPaymentReceiptAR502';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To detect pattern any failed process sync from razer integration to ar502. To notify alert failed or success sync AR502 file';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $paymentDate = Carbon::yesterday()->format('Y-m-d');
        $dateToday = Carbon::now()->format('Y-m-d');

        //$paymentDate = '2025-05-05';
        //$dateToday = '2025-05-06';

        $res1 = false;
        $res2 = false;
        $res3 = false;
        $res4 = false;
        
        //Alert 12:06AM : Not sync Razer vs eP
        $res1 = $this->alertReceiptEpNotSyncWithReceiptRazer($paymentDate);

        $hour =  Carbon::now()->hour;
        if($hour >= 6 ) {
            //Alert Step 1 : File not created AR502
            $res2 = $this->alertFileAR502NotCreated($dateToday);

            //Alert Step 2 : Total receipt yesterday not same with File AR502
            $res3 = $this->alertTotalReceiptAR502NotSameYesterday($paymentDate);

            //Alert Step 3 : File failed to send
            $res4 = $this->alertFileAR502FailedToSend($dateToday);

        }
        
        
        //Alert  Successfully send AR502 with info statistic receipt
        if($res1 === false && $res2 === false && $res3 === false && $res4 === false ){
            $this->alertSuccessNotificationAR502($paymentDate,$dateToday);
        }
        
        

        MigrateUtils::logDump($clsInfo.'Done! Completed');
    }

    protected function alertReceiptEpNotSyncWithReceiptRazer($paymentDate){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $res = false;
        try {
            $listPaymentEp = $this->listPaymentIdEpByDate($paymentDate);
            $listPaymentIdArr = $listPaymentEp->pluck('payment_id')->toArray();
    
            $listPaymentReceiptRazer  = collect($this->listPaymentRazerNotInWithReceiptEp($paymentDate,$listPaymentIdArr));
            if($listPaymentReceiptRazer->count() > 0 ){

                //*** Auto sent to eP using MolpayCallbackURL */
                $this->doReconciliationRazerToEp($listPaymentReceiptRazer);

                $listPaymentReceiptRazerRecheck  = collect($this->listPaymentRazerNotInWithReceiptEp($paymentDate,$listPaymentIdArr));
                if($listPaymentReceiptRazerRecheck->count() > 0 ){
                    $msg ='*[ALERT]* Reconciliation Issue for Payment Date: '.$paymentDate.' - Receipt data mismatch between eP and Razer. '.$listPaymentReceiptRazer->count().' receipts found in Razer are not updated in eP.';
                    
                    MigrateUtils::logErrorDump($clsInfo.$msg);

                    $this->saveNotify('ALERT_AR502',$msg);
                    
                    $res = true;
                }
            }else{
                $msg = "*[INFO]* Successfully sync eP Receipt with Razer Receipt for payment date on ".$paymentDate;
                MigrateUtils::logDump($clsInfo.$msg);
                $this->saveNotify('RECEIPT_AR502',$msg);
            }
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        return $res;
    }

    protected function doReconciliationRazerToEp($listPaymentRezersNotSync){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        foreach ($listPaymentRezersNotSync as $obj){
            $collect = collect();
            $collect->put('amount',$obj->amount);
            $collect->put('orderid',$obj->order_id);
            $collect->put('tranID',$obj->bank_transaction_id);
            $collect->put('status',$obj->stat_code);
            $collect->put('appcode',$obj->approval_code);
            $collect->put('currency','MYR');
            
            $channel = 'fpx';
            if(strlen($obj->approval_code) > 0){
                $cardBin = substr($obj->card_bin,0,6);

                // to get card type from master.
                $binObj = DB::connection('mysql_ep_support')->table('ep_bin_info')
                    ->where('bin_no',$cardBin )->first();
                $channel = 'credit'; 
                if($binObj != null){
                    if(strlen($binObj->bin_mbb_type) > 0){
                        $channel = strtolower($binObj->bin_mbb_type);
                    }
                }
            }

            $collect->put('channel',$channel );
            $collect->put('paydate',$obj->billing_date);
            $collect->put('extraP','{"ccbrand":"'.$obj->card_scheme.'","cctype":"'.$obj->card_type.'","cclast4":"0000","MOLPay_tranID":"'.$obj->tran_id.'","bank_datetime":"'.$obj->bank_date_time.'"}');
            //$collect->put('extraP','{"MOLPay_tranID":"'.$obj->tran_id.'","bank_datetime":"'.$obj->bank_date_time.'"}');
            $collect->put('domain',$obj->merchant_id);
            $collect->put('vkey',ClientRazerApi::getMerchantEpVkey());
            $collect->put('urlCallback',ClientRazerApi::getCalbackUrlEp());
            ClientRazerApi::sendCallback($collect);
            sleep(3);
        }
    }

    
    protected function alertFileAR502NotCreated($date){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $res = false;    
        try {
            $results = collect($this->getFileAR502CreatedByDate($date));
            if($results->count() == 0 ){
                $msg = '*[ALERT]* File AR502 failed to generate today ('.$date.'). ';
                MigrateUtils::logErrorDump($clsInfo.$msg);
                $this->saveNotify('ALERT_AR502',$msg);
                $res = true; 
            }else{
                MigrateUtils::logDump($clsInfo.'[OK] File AR502 successfully generated today');
            }
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        return $res;
    }

    protected function alertTotalReceiptAR502NotSameYesterday($paymentDate){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $res = false;
        try {
            $listPaymentReceiptAr502  = $this->listReceiptNoAr502ByDate($paymentDate);
            $listReceiptNoArr = $listPaymentReceiptAr502->pluck('receipt_no')->toArray();
            
            $listPaymentEp = collect($this->listPaymentEpNotInWithReceiptAr502($paymentDate,$listReceiptNoArr));
            if($listPaymentEp->count() > 0 ){
                $msg = '*[ALERT]* Receipt eP <> Receipt AR502 not sync. Found '.$listPaymentEp->count().' still not send by AR502 file';
                MigrateUtils::logErrorDump($clsInfo.$msg);
                $this->saveNotify('ALERT_AR502',$msg);
                $res = true;
            }else{
                MigrateUtils::logDump($clsInfo.'[OK] Receipt eP <> Receipt AR502 synced');
            }
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        return $res;
    }

    protected function alertFileAR502FailedToSend($date){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $res = false;
        try {
            $fileType = 'AR502';
            $lengthFile = '-30';
            $listFiles = ReTriggerFileIGFMAS::getListFileNameOutFolderEp($fileType, $lengthFile);
            if(count($listFiles) > 0 ){
                $msg = '*[ALERT]* File AR502 ('.$date.') failed send to IGFMAS. List failes : '.json_encode($listFiles);
                MigrateUtils::logErrorDump($clsInfo.$msg);
                $this->saveNotify('ALERT_AR502',$msg);
                $res = true;
            }else{
                MigrateUtils::logDump($clsInfo.'[OK] There is no stuck file AR502 in /batch/1GFMAS/OUT.');
            }

        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        return $res;
    }

    protected function alertSuccessNotificationAR502($paymentDate,$dateToday){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        
        try {
            
            $result = collect($this->getFileAR502CreatedByDate($dateToday));
            $objInterfaceFile = $result->first();
            if($objInterfaceFile){
                $fileName = $objInterfaceFile->file_name; 
                MigrateUtils::logDump($clsInfo.'FileName Created : '.$fileName);

                $listPaymentReceiptAr502  = collect($this->listReceiptNoAr502ByFileName($fileName));
                $totalReceipt = $listPaymentReceiptAr502->count();
                MigrateUtils::logDump($clsInfo.'FileName Total Receipts : '.$totalReceipt);

                $objSuccessSent = $this->getDetailOsbLoggingSuccessSent($fileName);
                if($objSuccessSent){
                    $dateSend = Carbon::parse($objSuccessSent->trans_date)->format('Y-m-d H:i:s');
                }else{
                    $dateSend = Carbon::parse($objInterfaceFile->changed_date)->format('Y-m-d H:i:s');
                }
                MigrateUtils::logDump($clsInfo.'File successfully sent at: '. $dateSend);
                
                $listPaymentEp = $this->summaryReceiptEpByDate($paymentDate);
                $paymentChannelFpx = $listPaymentEp->where('channel_name','fpx')->first();
                $paymentChannelCredit = $listPaymentEp->where('channel_name','credit')->first();
                $paymentChannelDebit = $listPaymentEp->where('channel_name','debit')->first();
        
                $fpxTotalPayment = $paymentChannelFpx  ? $paymentChannelFpx->total_payment : 0;
                $fpxAmountPayment = $paymentChannelFpx  ? $paymentChannelFpx->amount_payment : 0;
                $creditTotalPayment = $paymentChannelCredit  ? $paymentChannelCredit->total_payment : 0;
                $creditAmountPayment = $paymentChannelCredit  ? $paymentChannelCredit->amount_payment : 0;
                $debitTotalPayment = $paymentChannelDebit  ? $paymentChannelDebit->total_payment : 0;
                $debitAmountPayment = $paymentChannelDebit ? $paymentChannelDebit->amount_payment : 0;
                
                $data = array(
                    "title" => "Fail AR502 telah berjaya dihantar ke iGFMAS.",
                    "file_name" => $fileName,
                    "total_receipts" => $totalReceipt,
                    "date_send" => $dateSend,
                    'datePayment' => $paymentDate,
                    "paymentChannelFpx" => $paymentChannelFpx,
                    "paymentChannelCredit" => $paymentChannelCredit,
                    'fpxTotalPayment'=> $fpxTotalPayment,
                    'fpxAmountPayment'=> $fpxAmountPayment,
                    'creditTotalPayment'=> $creditTotalPayment,
                    'creditAmountPayment'=> $creditAmountPayment,
                    'debitTotalPayment'=> $debitTotalPayment,
                    'debitAmountPayment'=> $debitAmountPayment,

                );

                $this->sendNotifyEmail($data);

                $msg = "*[INFO]* File $fileName succesfully sent to IGFMAS at $dateSend with $totalReceipt receipts";
                MigrateUtils::logDump($clsInfo.$msg);
                $this->saveNotify('RECEIPT_AR502',$msg);

                // Don't change space or tab in this contents unless u know get the result in whatapp notification.
                $msgInfoSummary = "
*[INFO] SUMMARY PAYMENT eP ($paymentDate)*
*Credit Card*
  No. of Transaction: $creditTotalPayment
  Amount(RM): $creditAmountPayment
*Debit Card*
  No. of Transaction: $debitTotalPayment
  Amount(RM): $debitAmountPayment
*Fpx*
  No. of Transaction: $fpxTotalPayment
  Amount(RM): $fpxAmountPayment
*TOTAL AMOUNT*
  No. of Transaction: ".($fpxTotalPayment+$creditTotalPayment+$debitTotalPayment)."
  Amount(RM): ".($fpxAmountPayment+$creditAmountPayment+$debitAmountPayment);
                MigrateUtils::logDump($clsInfo.$msgInfoSummary);
                $this->saveNotify('RECEIPT_AR502',$msgInfoSummary);
                
            }


        }catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }

    }

    protected function getDetailOsbLoggingSuccessSent($fileName){
        $query = "SELECT *  
                FROM OSB_LOGGING o
                WHERE o.remarks_1 = ?
                    AND o.service_code = 'GFM-380' 
                    AND o.trans_type = 'Status-BATCH'
                    AND o.status = 'S'
            ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($fileName));
        if(count( $results ) > 0){
            return $results[0];
        }
        return null;
    }

    protected function getFileAR502CreatedByDate($date){
        $query = "SELECT i.interface_log_id ,i.service_code , i.process_id, i.created_date, i.file_name , i.changed_date 
                FROM di_interface_log i
                WHERE i.PROCESS_ID = 'AR502' 
                    AND i.FILE_NAME IS NOT NULL 
                    AND i.TRANS_ID  IS NOT NULL
                    AND trunc(i.CREATED_DATE) = to_date(?,'YYYY-MM-DD')
            ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($date));
        return $results;
    }
    
    protected function saveNotify($receiver,$msg){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $totNotify = NotifyModel::where('message',$msg)->count();
        if($totNotify > 0){
            MigrateUtils::logDump($clsInfo.'Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring daily Receipt eP';
            $nty->save();
        }
        
    }

    protected function sendNotifyEmail($dataContents) {
        $dataSend = array(
            /***
            ePA3
            HAFSAH BINTI JAAFAR  <<EMAIL>>
            RAHA BINTI OTHMAN <<EMAIL>>

            ePO1
            ASMAH BINTI ADAM <<EMAIL>>
            FATIMAH HARYATI BINTI KAMALUDIN <<EMAIL>>

            ePP4
            JULIYANA BINTI ABD. RAHIM <<EMAIL>>
            ISMALIZA BINTI IMAN <<EMAIL>> 
            NOORAFIZAH BINTI SAJULI <<EMAIL>> remove on 18/9/2024
            SALASIAH BINTI HABLI <<EMAIL>>

            MOHD ROSLAN BIN ISMAIL (MOF) <<EMAIL>> added 7/11/2023 ,  remove on 17/9/2024

            Marlia Binti <NAME_EMAIL> add 18/9/2024
            Nurulhuda <NAME_EMAIL> add 18/9/2024

             ***/
            "to" => [
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>','<EMAIL>',
                    '<EMAIL>','<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>'
                ],
            //"to" => ['<EMAIL>'],
            "subject" => '['.env('APP_ENV').'] - Notis Penghantaran Fail '.$dataContents['file_name'].' Telah Berjaya  dihantar ke iGFMAS'
        );
        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mail = $mailer
                ->send('emails.notifySuccessPaymentReceiptAR502', ['data' => $dataContents], function ($m) use ($dataSend) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($dataSend["to"]);
                    $m->subject($dataSend["subject"]);
                });
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }

         
    }
}
