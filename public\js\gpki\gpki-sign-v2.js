var start = new Date().getTime();
var modeUsed = "Production"; //Training/Production
var sess = ""; //sessionID used for batch signing softcert/roaming cert agent
var batchFlag = 0; //batchFlag used for batch signing agent
var testdata = "#testdata";
var k = 1;
var x = 2;
var result = new Array();
var medium = "";
var id;
var pin;
var signingType;
var responseSign = new Array();
var batchAgent = false; //true : sign batch; false : sign single
var detachMode = false; //signing attach/detach
var roamingst = false;

$(document).ready(function () {
  $("#listMedium").hide();
  $("#reqOtp").hide();
  $("#reqSign").hide();
  $("#reqSignSoftcertToken").hide();
  $("#security_q").hide();
  $("#reqEmail").hide();
  $("#nextSign").hide();
  $("#reqBatchSignSoftcertToken").hide();
  $("#nextBatchSign").hide();
  $("#roamingBatch").hide();
  $("#roamingSingle").hide();
  $("#roamingBatch2").hide();

  const emailRadio = document.getElementById("2");

  emailRadio.addEventListener("change", function () {
    if (emailRadio.checked || modeUsed == "Training") {
      $("#reqEmail").show();
    }
  });

  const mobilRadio = document.getElementById("1");

  mobilRadio.addEventListener("change", function () {
    if (mobilRadio.checked & (modeUsed == "Production")) {
      $("#reqEmail").hide();
    }
  });

  // 1. request_mediumList, return result in function parseResultMedium
  $("#requestMedium").click(function () {
    if ($("#nric").val() == "" || $("#applicationCode").val() == "") {
      Gpki.alert("Sila masukkan ID Sijil Digital dan Kod Aplikasi");
    } else {
      if ($("#mode").is(":checked")) {
        modeUsed = "Training";
      } else {
        modeUsed = "Production";
      }
      var nric = $("#nric").val();
      var applicationCode = $("#applicationCode").val();

      request_mediumList(nric, applicationCode, modeUsed);
    }
  });

  // 2. choose medium (id 2:softcert 3:roaming 4:token 5:roaming otp)
  $("#nextStep").click(function () {
    if ($("#nric2").val() == "" || $("#mediumId").val() == "") {
      Gpki.alert("Pilih medium");
    } else {
      var nric2 = $("#nric2").val();
      var mediumId = $("#mediumId").val();

      $("#listMedium").hide();
      $("#reqNric").hide();
      $("#reqOtp").show();
      $("#mediumId3").val(mediumId);

      if ($("#batchsign").is(":checked")) {
        batchAgent = true;
      }
      if (mediumId == 5) {
        if (modeUsed == "Training") {
          $("#reqEmail").show();
        } else {
          $("#reqEmail").hide();
        }
        $("#userId").val(nric2);
        $("#nric3").val(nric2);
        $("#reqOtp").show();
        $("#reqSignSoftcertToken").hide();
      } else if (mediumId == 2 || mediumId == 4) {
        if (batchAgent) {
          $("#idbatch").val(nric2);
          $("#reqBatchSignSoftcertToken").show();
          $("#reqSignSoftcertToken").hide();
          $("#reqOtp").hide();
        } else {
          $("#certID").val(nric2);
          $("#reqSignSoftcertToken").show();
          $("#reqOtp").hide();
        }
      } else {
        $("#reqSignSoftcertToken").hide();
        $("#reqOtp").hide();

        verify_id("roaming", nric2); // return result in funtion callback_verify_id
      }
    }
  });

  // roaming otp - request otp return result in function parseTxResultRequestOTP
  $("#requestOtp").click(function () {
    var radioButtons = document.getElementsByName("saluran");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        var saluran = radioButtons[i].value;
        break;
      }
    }

    if (
      $("#nric3").val() == "" ||
      $("#agencyId").val() == "" ||
      $("#userId").val() == "" ||
      $("#otpExp").val() == ""
    ) {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      var nric3 = $("#nric3").val();
      var agencyId = $("#agencyId").val();
      var userId = $("#userId").val();
      var otpExp = $("#otpExp").val();
      var emailuser = "";
      console.log("email user - ", $("#emailuser").val());
      if ($("#emailuser").val() !== null) {
        emailuser = $("#emailuser").val();
      }

      request_otp(
        nric3,
        agencyId,
        userId,
        modeUsed,
        otpExp,
        saluran,
        emailuser
      );
    }
  });

  // roaming otp - sigining return result in function parseTxResultSigningRoaming
  $("#requestSign").click(function () {
    var radioButtons = document.getElementsByName("signingType");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        var signingType = radioButtons[i].value;
        break;
      }
    }

    if (
      $("#nric2").val() == "" ||
      $("#nonce").val() == "" ||
      $("#otpCode").val() == "" ||
      $("#data").val() == "" ||
      $("#agencyId").val() == "" ||
      $("#pin").val() == "" ||
      $("#pin").val() == ""
    ) {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      var nric = $("#nric3").val();
      var nonce = $("#nonce").val(); //id transaksi
      var otpCode = $("#otpCode").val();
      var data = btoa(Gpki.hash($("#data").val()));
      var pin = $("#pin").val();
      var agencyId = $("#agencyId").val();
      console.log("hash data ", data);
      start = new Date().getTime();
      sign_roaming(
        nric,
        nonce,
        otpCode,
        data,
        pin,
        agencyId,
        modeUsed,
        signingType,
        "0"
      );
    }
  });

  // Listen for form submission event
  document.getElementById("batch").addEventListener("submit", function (event) {
    event.preventDefault(); // Prevent default form submission behavior

    // Get input values
    var sessionId = document.getElementById("sessionId").value;
    var dataToSign = document.getElementById("datatosign").value;
    var data = btoa(Gpki.hash(dataToSign));
    var dataSeq = document.getElementById("dataSeq").value;

    console.log("sessionId - ", sessionId);
    console.log("dataToSign - ", data);
    console.log("dataSeq - ", dataSeq);
    sign_roaming_next(sessionId, dataSeq, data);
  });

  // signing softcert, token
  $("#signButton").click(function () {
    var radioButtons = document.getElementsByName("signingTypeSTR");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        var signingTypeSTR = radioButtons[i].value;
        break;
      }
    }
    console.log("signingTypeSTR ", signingTypeSTR);

    var plainText = encodeURIComponent(Gpki.hash($("#textToSign").val()));
    var pin = $("#pin2").val();
    var id = $("#certID").val();
    var mediumId3 = $("#mediumId3").val();

    if (plainText == "" || pin == "" || id == "") {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      if (mediumId3 == "2") {
        sign("softcert", id, pin, plainText, true, signingTypeSTR);
      } else if (mediumId3 == "3") {
        sign("roaming", id, pin, plainText, true, signingTypeSTR);
      } else if (mediumId3 == "4") {
        sign("token", id, pin, plainText, true, signingTypeSTR);
      }
    }
  });

  // batch signing softcert, token
  $("#signBatchButton").click(function () {
    var radioButtons = document.getElementsByName("batchType");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        signingType = radioButtons[i].value;
        break;
      }
    }

    var plainText = encodeURIComponent(Gpki.hash($("#testdata").val()));
    pin = $("#pass").val();
    id = $("#idbatch").val();
    medium = $("#mediumId3").val();

    if (plainText == "" || pin == "" || id == "") {
      Gpki.alert("Sila masukkan maklumat yang sah");
    } else {
      if (medium == "2") {
        signbatchCert(
          "softcert",
          id,
          pin,
          plainText,
          true,
          signingType,
          batchFlag,
          sess
        );
      } else if (medium == "3") {
        signbatchCert(
          "roaming",
          id,
          pin,
          plainText,
          true,
          signingType,
          batchFlag,
          sess
        );
      } else if (medium == "4") {
        signbatch(id, pin, plainText, detachMode, batchFlag, signingType);
      }
    }
  });

  //  roaming only
  $("#n_proceed").click(function () {
    var errorMsg = "";
    pin = $("#pin3").val();
    type = "roaming";
    medium = "3";

    var radioButtons = document.getElementsByName("signingTypeRoaming");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        signingType = radioButtons[i].value;
        break;
      }
    }
    console.log("signingTypeRoaming ", signingType);

    if ($("#answer").val() == "" || $("#pin3").val() == "") {
      errorMsg += "Please enter your security answer and pin";
      $("#msg").html(errorMsg);
    }

    if (!existed) {
      if ($("#answer").val() == "" || $("#pin3").val() == "") {
        Gpki.alert("Sila masukkan maklumat yang sah");
      } else {
        var answer = $("#answer").val();
        var question = $("#question").val();
        verify_question(type, certID, pin, question, answer, 10);
      }
    } else {
      var plainText = encodeURIComponent(Gpki.hash($("#textToSign2").val()));

      if ($("#pin3").val() == "") {
        Gpki.alert("Sila masukkan maklumat yang sah");
      } else {
        if (batchAgent) {
          signbatchCert(
            type,
            id,
            pin,
            plainText,
            true,
            signingType,
            batchFlag,
            sess
          );
        } else {
          sign(type, certID, pin, plainText, false, signingType);
        }
      }
    }
  });

  // batch roaming only
  $("#n_proceed2").click(function () {
    var errorMsg = "";
    pin = $("#pin3").val();
    type = "roaming";
    medium = "3";

    var radioButtons = document.getElementsByName("signingTypeRoaming");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        signingType = radioButtons[i].value;
        break;
      }
    }
    console.log("signingTypeRoaming ", signingType);

    if ($("#answer").val() == "" || $("#pin3").val() == "") {
      errorMsg += "Please enter your security answer and pin";
      $("#msg").html(errorMsg);
    }

    if (!existed) {
      if ($("#answer").val() == "" || $("#pin3").val() == "") {
        Gpki.alert("Sila masukkan maklumat yang sah");
      } else {
        var answer = $("#answer").val();
        var question = $("#question").val();
        verify_question(type, certID, pin, question, answer, 10);
      }
    } else {
      var plainText = encodeURIComponent(Gpki.hash($("#textToSign2").val()));

      if ($("#pin3").val() == "") {
        Gpki.alert("Sila masukkan maklumat yang sah");
      } else {
        if (batchAgent) {
          signbatchCert(
            type,
            id,
            pin,
            plainText,
            true,
            signingType,
            batchFlag,
            sess
          );
        } else {
          sign(type, certID, pin, plainText, false, signingType);
        }
      }
    }
  });
});

// handles timeout
function parseTimeout(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    //var statusCode = obj.status_code;

    var statusMsg = obj.status_message;
    //$("#gpki-data").html(statusMsg);

    var extMsg = obj.status_ext_message;
    Gpki.alert(extMsg);
  });
}

/* put your logic to handle the result verify_question roaming */
function processVerifyQuestionResult(msg) {
  var obj = jQuery.parseJSON(msg);
  var status_code = obj.status_code;
  var status_message = obj.status_message;
  if (status_code == "0") {
    $("#msg").html("ID and PIN accepted.");
    /* put your logic to handle the result here */

    var plainText = encodeURIComponent(Gpki.hash($("#textToSign2").val()));
    console.log("value ", $("#textToSign2").val());
    var sessionID = $("#sessionID").val();
    type = "roaming";
    id = $("#certID2").val();
    pin = $("#pin3").val();
    medium = "3";

    var radioButtons = document.getElementsByName("signingTypeRoaming");

    for (var i = 0; i < radioButtons.length; i++) {
      if (radioButtons[i].checked) {
        signingType = radioButtons[i].value;
        break;
      }
    }

    console.log("signingTypeRoaming - ", signingType);

    if (batchAgent) {
      signbatchCert(
        type,
        id,
        pin,
        plainText,
        true,
        signingType,
        batchFlag,
        sess
      );
    } else {
      sign(type, id, pin, plainText, false, signingType);
    }

    /* Handle Exeptions */
  } else if (status_code == "10") {
    Gpki.alert("ID is not found", "Error");
  } else {
    Gpki.alert(status_message);
  }
}

/* put your logic to handle the result verify_id roaming */
function callback_verify_id(msg) {
  var obj = jQuery.parseJSON(msg);
  var status_code = obj.status_code;
  var image = obj.image;
  question = obj.question;
  certID = obj.cert_id;
  var status_message = obj.status_message;

  if (status_code == "0") {
    existed = false;
    /* put your logic to handle the result here */
    $("#security_q").show();
    var image_url =
      '<img style="width: 10%;" border="0" src="http://127.0.0.1:8084/resource?get=' +
      image +
      '" >';
    $("#image_sec").html(image_url);
    $("#certID2").val(certID);
    $("#question").find("option").remove();
    $("#securityconfirm").show();
    $("#question").append(question).show();

    if (batchAgent) {
      roamingst = true;
      console.log("hide ", $("#roamingSingle").hide());
      $("#roamingBatch").show();
      $("#roamingBatch2").show();
      $("#roamingSingle").hide();
    } else {
      console.log("show ", $("#roamingSingle").hide());
      $("#roamingBatch").hide();
      $("#roamingBatch2").hide();
      $("#roamingSingle").show();
    }
  } else if (status_code == "200") {
    existed = true;
    $("#image_sec").html("");
    $("#securityconfirm").hide();
    $("#certID2").val(certID);
    if (batchAgent) {
      $("#idbatch").val(certID);
      $("#reqBatchSignSoftcertToken").show();
    } else {
      $("#roamingSingle").show();
      $("#security_q").show();
    }
  } else if (status_code == "404") {
    Gpki.alert(status_message);
    $("#image_sec").html("");
  } else {
    Gpki.alert(status_message);
  }
}

/* put your logic to handle the result signing for token,softcert & roaming */
function parseSignResult(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var statusCode = obj.status_code;
    var statusMsg = obj.status_message;

    if (statusCode == "0") {
      var signedData = obj.signed_data;
      var startDate = obj.start_date;
      var endDate = obj.end_date;
      var subjectDN = obj.subject_dn;
      var serialNo = obj.serial_no;

      signedData = encodeURIComponent(signedData);

      $("#security_q").hide();

      if (batchAgent) {
        if (obj.session_id) {
          sess = obj.session_id;
        }
        if (medium == "3" && roamingst) {
          testdata = "#roamingdata";
        }

        if ($(testdata + k).val()) {
          plainText = encodeURIComponent(Gpki.hash($(testdata + k).val()));

          if (!$(testdata + x).val()) {
            batchFlag = 2;
          } else {
            batchFlag = 1;
          }

          if (medium == "2") {
            signbatchCert(
              "softcert",
              id,
              pin,
              plainText,
              true,
              signingType,
              batchFlag,
              sess
            );
          } else if (medium == "3") {
            signbatchCert(
              "roaming",
              id,
              pin,
              plainText,
              true,
              signingType,
              batchFlag,
              sess
            );
          } else if (medium == "4") {
            signbatch(id, pin, plainText, detachMode, batchFlag, signingType);
          }
          x++;
          k++;
        }
      }
      result.push(obj);

      var certinfo =
        "<br><br><h2 class='title'>Certificate Data<h2><ul class='listitems'><li>Start Date: " +
        startDate +
        "</li><li>End Date: " +
        endDate +
        "</li><li>Certificate's Subject DN: " +
        subjectDN +
        "</li><li>Serial No: " +
        serialNo +
        "</li></ul>";
      responseSign.push(
        '<textarea name="signedData" cols="100" rows="15">' +
          signedData +
          "</textarea> <br>"
      );

      $("#gpki-data").html(certinfo + responseSign);

      Gpki.alert("Tandatangan Digital Berjaya");
    } else if (statusCode == "12") {
      Gpki.alert("ID not found");
    } else if (statusCode == "14") {
      Gpki.alert("Nombor PIN tidak sah. Sila masukkan nombor PIN yang tepat.");
    } else {
      console.log("obj -- ", obj);
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result list medium */
function parseResultMedium(msg) {
  $(document).ready(function () {
    console.log(msg);
    var obj = jQuery.parseJSON(msg);
    console.log("obj  - ", obj);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;

    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      $("#listMedium").show();
      $("#btnSubmit").hide();
      // $("#reqNric").hide();
      var resp = jQuery.parseJSON(obj.response);
      var nric = jQuery.parseJSON(obj.nric);

      $("#nric2").val(nric);
      const selectElement = document.querySelector("#mediumId");

      const options = resp;

      options.forEach((option) => {
        const newOption = document.createElement("option"); // create a new <option> element
        newOption.value = option.id; // set the value of the option to the id of the object in the array
        newOption.text = option.mediumType; // set the text of the option to the mediumType property of the object in the array
        selectElement.add(newOption); // add the new option to the select element
      });

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result request otp */
function parseTxResultRequestOTP(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;

    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      var resp = jQuery.parseJSON(obj.response);
      var nonce = resp.nonce; //id transaksi

      var startDate = resp.start_date;
      var endDate = resp.end_date;
      var subjectDN = resp.subject_dn;
      var serialNo = resp.serial_no;

      var nric = $("#nric3").val();
      var userId = $("#userId").val();
      $("#nric4").val(nric);
      $("#userId2").val(userId);
      $("#nonce").val(nonce); //id transaksi

      $("#reqNric").hide();
      $("#listMedium").hide();
      $("#reqOtp").hide();
      $("#reqSign").show();

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result signing roaming otp */
function parseTxResultSigningRoaming(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;
    var sessionId = obj.sessionId;
    console.log("obj - - ", obj);
    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      if (batchAgent) {
        $("#nextSign").show();
      }
      $("#reqNric").hide();
      $("#listMedium").hide();
      $("#reqOtp").hide();
      $("#reqSign").show();

      var resp = jQuery.parseJSON(obj.response);

      var nonce = resp.nonce; //id transaksi

      $("#sessionId").val(sessionId);

      var startDate = resp.start_date;
      var endDate = resp.end_date;
      var subjectDN = resp.subject_dn;
      var serialNo = resp.serial_no;

      var nric = $("#nric3").val();
      var userId = $("#userId").val();
      $("#nric").val(nric);
      $("#userId").val(userId);
      $("#nonce").val(nonce); //id transaksi
      $("#reqSign").hide();
      var certinfo =
        "<br><br><h2 class='title'>Certificate Data<h2><ul class='listitems'><li>Start Date: " +
        startDate +
        "</li><li>End Date: " +
        endDate +
        "</li><li>Certificate's Subject DN: " +
        subjectDN +
        "</li><li>Serial No: " +
        serialNo +
        "</li></ul>";
      if (startDate != null) {
        var signedData = resp.signed_data.replace("\\", "");

        $("#signResult").html(
          certinfo +
            '<textarea name="signedData" cols="110" rows="15">' +
            signedData +
            "</textarea>"
        );
      }

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}

/* put your logic to handle the result batch signing roaming otp */
function parseTxResultSigningRoamingNext(msg) {
  $(document).ready(function () {
    var obj = jQuery.parseJSON(msg);
    var status = obj.status;
    var statusMsg = obj.message;
    var response = obj.response;
    var sessionId = obj.sessionId;
    console.log("obj - - ", obj);
    var end = new Date().getTime();
    var time = end - start;

    if (status == true) {
      $("#reqNric").hide();
      $("#listMedium").hide();
      $("#reqOtp").hide();
      $("#reqSign").show();
      $("#nextSign").show();
      $("#signResult").hide();
      var resp = jQuery.parseJSON(obj.response);

      $("#sessionId").val(sessionId);

      var startDate = resp.start_date;
      var endDate = resp.end_date;
      var subjectDN = resp.subject_dn;
      var serialNo = resp.serial_no;

      $("#reqSign").hide();
      var certinfo =
        "<br><br><h2 class='title'>Certificate Data<h2><ul class='listitems'><li>Start Date: " +
        startDate +
        "</li><li>End Date: " +
        endDate +
        "</li><li>Certificate's Subject DN: " +
        subjectDN +
        "</li><li>Serial No: " +
        serialNo +
        "</li></ul>";
      if (startDate != null) {
        var signedData = resp.signed_data.replace("\\", "");

        console.log("signedData - - ", signedData);

        $("#signResultNext").html(
          certinfo +
            '<textarea name="signedData" cols="110" rows="15">' +
            signedData +
            "</textarea>"
        );
      }

      Gpki.alert(statusMsg);
    } else {
      Gpki.alert(statusMsg);
    }
  });
}
