<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use DateTime;
use DateInterval;
use DatePeriod;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService;


class CheckInvoiceAP511 {
    use BPMService;
    
    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::checkInvoiceNoPendingPayment();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function checkInvoiceNoPendingPayment() {
  
        $begin = new DateTime('2018-01-01');
        $end = new DateTime('2019-01-01');

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end);

        dump('$dateStart :: ' . $begin->format("Y-m-d"));
        dump('$dateEnd :: ' . $end->format("Y-m-d"));
        dump('Start to extract invoice detail: ' . $begin->format("Y-m-d") . ' to ' . $end->format("Y-m-d"));

        foreach ($period as $dt) {
            $date = $dt->format("Y-m-d");
            dump('Query Date: '.$date);
            $listResult = self::getQuery($date);
            dump('Found Total: '.count($listResult));
            foreach ($listResult as $data){
                dump('Checking...: '.$data->doc_no . ' InvNo: '.$data->invoice_no);
                $checkCount = DB::connection('mysql_ep_support')->table('ep_invoice_check')
                        ->where('poco_no',$data->doc_no)->where('inv_no',$data->invoice_no)
                        ->count();
                
                if($checkCount == 0){
                    dump('  Check not found: '.$data->doc_no . ' InvNo: '.$data->invoice_no);
                    $isExistAp511 = 'NO';
                    $fileName = '';
                    $paymentRefNo = '';
                    $paymentDate = '';
                    $cancelationDate = '';
                    $paymentAdviceNo = '';
                    $listApFile = self::findInvoiceNoInOSBFile($data->invoice_no);
                    $paInfoCheck = self::getInfoCheckPaymentAdviceEp($data->doc_no,$data->invoice_no);
                    if(count($listApFile) > 0){
                        $isExistAp511 = 'YES';
                        $fileNameList = $listApFile->pluck('file_name');
                        $refNoList = $listApFile->pluck('payment_reference_no');
                        $paymentDateList = $listApFile->pluck('payment_date');
                        $cancelationDateList = $listApFile->pluck('cancelation_date');
                        $paymentAdviceNoList = $listApFile->pluck('payment_advice_no');
                        $fileName = $fileNameList->toJson();
                        $paymentRefNo = $refNoList->toJson();
                        $paymentDate = $paymentDateList->toJson();
                        $cancelationDate = $cancelationDateList->toJson();
                        $paymentAdviceNo = $paymentAdviceNoList->toJson();
                    }
                    DB::connection('mysql_ep_support')
                          ->table('ep_invoice_check')->insert(
                          [
                             
                              'poco_no' => $data->doc_no,
                              'pa_no' => $paInfoCheck['pa_no'],
                              'is_pa_no_exist' => $paInfoCheck['is_pa_no_exist'],
                              'inv_no' => $data->invoice_no,
                              'fl_order_id' => $data->fulfilment_order_id,
                              'fl_req_id' => $data->fulfilment_req_id,
                              'status_name' => $data->status_name,
                              'status_id' => $data->status_id,
                              'file_name' => $fileName,
                              'payment_reference_no' => $paymentRefNo,
                              'is_ap511' => $isExistAp511,
                              'inv_date_created' => $data->created_date,
                              'payment_date' => $paymentDate,
                              'cancelation_date' => $cancelationDate,
                              'payment_advice_no' => $paymentAdviceNo,
                              'created_at' => Carbon::now(),
                          ]
                  );  
                }
                
            }
        }

        
    }
    
  
    protected static function getInfoCheckPaymentAdviceEp($pocoNo,$invoiceNo) {
        dump(__FUNCTION__.' find ... poco: ' . $pocoNo. ' , invoiceno: '.$invoiceNo);
        $data = array(
            'pa_no' => null,
            'is_pa_no_exist' => null
        );
        $listPaymentAdvice = DB::connection('oracle_nextgen_rpt')->select("
           SELECT
                payment_advice_id,
                fulfilment_order_id,
                invoice_id,
                payment_advice_no,
                record_status
              FROM fl_payment_advice
              WHERE
                fulfilment_order_id IN (SELECT fulfilment_order_id
                                        FROM fl_fulfilment_order
                                        WHERE doc_no = ?)
                AND invoice_id IN (SELECT invoice_id
                                   FROM fl_invoice
                                   WHERE invoice_no = ?)
        ", array($pocoNo,$invoiceNo));
        if (count($listPaymentAdvice) > 0) {
            $objPa = $listPaymentAdvice[0];
            $data = array(
                'pa_no' => $objPa->payment_advice_no,
                'is_pa_no_exist' => 'YES'
            );
        }else{
            $listPa = DB::connection('oracle_nextgen_rpt')->select("
                select distinct a.DOC_NO as poco_no, b.DOC_NO as pa_no from pm_tracking_diary a, pm_tracking_diary b
                where a.GROUP_ID = b.GROUP_ID
                and b.STATUS_ID = 46005
                and b.DOC_TYPE = 'PA'
                and b.GROUP_DOC_TYPE in ('PO','CO')
                and a.doc_no = ? 
            ", array($pocoNo));
            if (count($listPa) > 0) {
                $trackPa = $listPa[0];
                $paNo = $trackPa->pa_no;
                dump($trackPa);

                $checkPa = DB::connection('oracle_nextgen_rpt')->table('fl_payment_advice')
                                ->where('payment_advice_no', $paNo)->count();
                $isExistPa = 'NO';
                if ($checkPa > 0) {
                    $isExistPa = 'YES';
                }
                dump('  check is exist in PA table :- ' . $isExistPa);

                $data = array(
                    'pa_no' => $paNo,
                    'is_pa_no_exist' => $isExistPa
                );
            }
        }
        return $data;
    }

    protected static function getQuery($date) {

        //$createdDateStart = '2018-01-01';
        $start = microtime(true);

        $result = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT fo.fulfilment_order_id, fo.fulfilment_req_id, fo.DOC_NO,
                psd.status_name, psd.status_id, ws.is_current, ws.created_date,
                (SELECT i.invoice_no
                FROM FL_INVOICE i
                WHERE i.FULFILMENT_ORDER_ID = fo.FULFILMENT_ORDER_ID AND i.RECORD_STATUS = 1) AS invoice_no
                FROM fl_fulfilment_order fo, fl_workflow_status ws, pm_status_desc psd
                WHERE ws.DOC_ID = fo.FULFILMENT_ORDER_ID AND ws.DOC_TYPE = fo.DOC_TYPE AND ws.STATUS_ID = psd.STATUS_ID AND
                psd.LANGUAGE_CODE = 'en' AND ws.IS_CURRENT = 1 AND ws.STATUS_ID IN(41030, 41530) AND
                trunc(ws.created_date) = to_date(?, 'YYYY-MM-DD')   ", array($date));
        //$sql->skip($nextSkip)->take($take);

        $time_elapsed_secs = microtime(true) - $start;
        dump('query execution time: ' . $time_elapsed_secs);
        
        return $result;
    }
    
    protected static function findInvoiceNoInOSBFile($docNo){
        
        return DB::connection('mysql_ep_support')->table('ep_invoice_detail as a')
                    ->where('a.invoice_no','=',$docNo)
                    ->get();
    }

}
