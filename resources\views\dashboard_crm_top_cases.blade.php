@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'  style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Top 10 for <strong>Incident / Service : {{Carbon\Carbon::now()->format('d-m-Y')}}</strong>
                </h5>
            </div>
            <div id="dash_top_incserv">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
@endif


<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $.ajax({
            url: APP_URL + '/dashboard/cs/topEnqSubCategory',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_top_enq').hide().html($data).fadeIn();
            }
        });
        $.ajax({
            url: APP_URL + '/dashboard/cs/topIncServSubCategory',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_top_incserv').hide().html($data).fadeIn();
            }
        });

        /* CONFIRM RESUME JOB QUE */
        $('.widget').on("click", '.modal-confirm-action', function () {

            let job_id = $(this).attr('data-id');
            let url = APP_URL + '/dashboard/cs/update-jobque';

            $('#confirm-modal').on("click", '#modal-btn-yes', function () {
                $('#confirm-modal').modal('hide');
                $('.spinner-loading').show();
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {
                        '_token': $('input[name=_token]').val(),
                        'job_id': job_id,
                    },
                    success: function (data) {
                        let $data = $(data);

                        if ($data.selector === 'update-success') {
                            alert('Successfully Updated.');
                        } else {
                            alert('Failed.');
                        }

                        $('.spinner-loading').hide();
                    }
                });


            });

            $('#confirm-modal').on("click", '#modal-btn-no', function () {
                $('#confirm-modal').modal('hide');
            });

        });
        
    });

</script>
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
