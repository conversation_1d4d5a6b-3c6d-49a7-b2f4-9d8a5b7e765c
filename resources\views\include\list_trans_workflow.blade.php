
@if(count($listDataWorkflow) > 0)
<div class="row">
    <div class="col-sm-12">
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                <tr>
                    <th class="text-center">DOC ID</th>
                    <th class="text-center">DOC TYPE</th>
                    <th class="text-center">WORKFLOW ID</th>
                    <th class="text-center">DOC NO.</th>
                    <th class="text-center">CREATED DATE</th>
                    <th class="text-center">STATUS</th>
                    <th class="text-center">IS CURRENT</th>
                    <th class="text-center">CREATED BY</th>
                    <th class="text-center">CHANGED BY</th>
                </tr>
                </thead>
                <tbody>
                @foreach ($listDataWorkflow as $data)
                    <tr>
                        <td class="text-center">{{ $data->doc_id }}</td>
                        <td class="text-center">{{ $data->doc_type }}</td>
                        <td class="text-center">{{ $data->workflow_status_id }}</td>
                        <td class="text-center">{{ $data->doc_no }} @if(isset($data->po_co)) &nbsp;>>&nbsp; {{ $data->po_co }}@endif</td>
                        <td class="text-center">{{ $data->fws_date_created }}</td>
                        <td class="text-left">{{ $data->status_id }} - {{ $data->status_name }}</td>
                        <td class="text-center">{{ $data->is_current }}</td>
                        <td class="text-center">{{ $data->created_user_id }} - <a target="_blank" href="{{ url('/find/userlogin?login_id=') }}{{ $data->created_login_id }}" >{{$data->created_login_id}}</a><br /></td>
                        <td class="text-center">{{ $data->changed_user_id }} - <a target="_blank" href="{{ url('/find/userlogin?login_id=') }}{{ $data->changed_login_id }}" >{{$data->changed_login_id}}</a><br /></td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>   
    </div>
</div>
@else
<div class="">
    <div class="block-title">
        <h2><i class="fa fa-ban"></i> <strong>No transactions. </strong></h2>
    </div>
</div>
@endif