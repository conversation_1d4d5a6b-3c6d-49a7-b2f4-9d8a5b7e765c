@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/it_support/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li class="">
                <a href="{{ url('/it_support/summary_details') }}"><i class="fa fa-list-alt"></i>Summary Details</a>
            </li>
            <li>
                <a href="{{ url('/it_support/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>


    <div class="block">
        @if (session()->has('failed'))
            <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                {{ session('failed') }}
            </div>
        @endif

        @if (session()->has('success'))
            <div class="alert alert-info alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-info-circle"></i> Success! </h4>
                {{ session('success') }}
            </div>
        @endif
        <div class="block-options">
            <label class="col-md-2 text-right" for="day">{{ $day }}</label>
            <label class="col-md-2 text-right" id ="date" for="date"> {{ $date }} </label>
            <input style="display:none" class="col-md-2 text-right" id ="shift_inter" value="{{ $shift }}"
                for="shift_inter" />
        </div>

        <div class="text-center" style="padding: 5px;">
            <ul class="nav nav-tabs" data-toggle="tabs">
                <li id="button_submit_M" name="button_submit_M"><a href="#Morning">Morning Shift</a></li>
                <li id="button_submit_E" name="button_submit_E"><a href="#Evening">Night Shift</a></li>
            </ul>
        </div>
        <form id ="it_support_checklist" action="{{ url('/it_support/checklist') }}" method="post">
            {{ csrf_field() }}
            <div id="Morning">
                <div class="table-options clearfix" id="clearfix_M">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($menu))
                            @foreach ($menu as $key => $list)
                                <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                    title="{{ $list->esc_group }}" group ="{{ $list->esc_group }}" shift="M">
                                    <input type="radio" name="menu" value=""> {{ $list->esc_group }}
                                </label>
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>

            <div id="Evening">
                <div class="table-options clearfix" id="clearfix_E">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($menu))
                            @foreach ($menu as $key => $list)
                                @if (in_array($list->esc_group, [
                                        'Common',
                                        'Login test',
                                        'Integration',
                                        'Batch Check',
                                        'SCT Status',
                                        'Backup Status',
                                        'CCTV Review',
                                    ]))
                                    <label id="menu_button1" class="btn btn-primary" data-toggle="tooltip"
                                        title="{{ $list->esc_group }}" group ="{{ $list->esc_group }}" shift="E">
                                        <input type="radio" name="menu" value=""> {{ $list->esc_group }}
                                    </label>
                                @endif
                            @endforeach
                        @endif
                    </div>
                </div>
            </div>
            <div class="text-center spinner-loading" style="padding: 20px;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>

            <div class="table-responsive">
                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
                <div id='helang_dc' style="display:none">
                    <label class="col-md-1 text-right" for="name">Name</label>
                    <div class="col-md-2 helang_remarks">
                        <select id="teststatus" name = "teststatus" class="form-control" style="width: 700px;">
                        </select>
                    </div>
                    <div class="col-md-8" style="display:none">
                        <textarea type="text" id="trytest" name="trytest" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Remarks</label>
                    <div class="col-md-8">
                        <textarea type="text" id="remarks_sct" name="remarks_sct" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-4 text-left notes_aircond" style="display: none">
                        Aircond <br />
                        Temperature : Below 25 Celcius <br />
                        Humidity : Below 80% <br />
                    </label>
                    <label class="col-md-4 text-left notes_fm200" style="display: none">
                        FM 200: <br />
                        Pressure : 27psi - 30psi <br />
                        Note : <br />
                        Below 27psi - Gas has been discharged <br />
                        Above 30psi - Room temperature is high
                    </label>
                </div>

                <div id='cctv_review' style="display:none">
                    <label class="col-md-1 text-right" for="name">Name</label>
                    <div class="col-md-2 cctv_remarks">
                        <select id="cctv_name" name = "cctv_name" class="form-control" style="width: 700px;">
                        </select>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Incident Details</label>
                    <div class="col-md-2">
                        <textarea type="text" id="incident_detail" name="incident_detail" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Incident Number</label>
                    <div class="col-md-2">
                        <textarea type="text" id="incident_number" name="incident_number" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">CRM Log No</label>
                    <div class="col-md-2">
                        <textarea type="text" id="crm_log_no" name="crm_log_no" rows="2" class="form-control"></textarea>
                    </div>
                </div>
            </div>

            <div class="text-center">
                <button type="submit" style="background-color: #414770; color: white"
                    class="btn btn btn-primary editSave">Update</button>
            </div>
        </form>
    </div>

    <div class="block panel-heading">@include('it_support.page_status', ['page' => 'status'])</div>

@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>

    <script>
        function selectAll(ele) {
            // Use a more general selector for checkboxes
            var checkboxes = document.querySelectorAll('input[type="checkbox"][name^="id["]');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].checked = true;
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].checked = false;
                }
            }
        }

        $(document).ready(function() {
            if ($('#shift_inter').val() === "M") {
                $('#button_submit_M').click();
                $('#button_submit_M').addClass("active");
                $('#menu_button').click();
                $('#menu_button').addClass("active");
            } else if ($('#shift_inter').val() === "E") {
                $('#button_submit_E').click();
                $('#button_submit_E').addClass("active");
                $('#menu_button1').click();
                $('#menu_button1').addClass("active");
            }
        });


        $('#button_submit_M').click(function() {
            $('#clearfix_M').show();
            $('#clearfix_E').hide();
            $('#button_submit_M').css({
                'color': 'white',
                'background-color': '#57B846',
                'font-size': '150%'
            });
            $('#button_submit_E').css({
                'color': 'white',
                'background-color': '#414770',
                'font-size': '150%'
            });
            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
                $(document).ready(function() {
                    $('#menu_button').click();
                    $('#menu_button').addClass("active");
                });
            }
            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();

        });

        $('#button_submit_E').click(function() {
            $('#clearfix_E').show();
            $('#clearfix_M').hide();
            $('#button_submit_E').css({
                'color': 'white',
                'background-color': '#57B846',
                'font-size': '150%'
            });
            $('#button_submit_M').css({
                'color': 'white',
                'background-color': '#414770',
                'font-size': '150%'
            });
            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
                $(document).ready(function() {
                    $('#menu_button1').click();
                    $('#menu_button1').addClass("active");
                });
            }
            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();
        });

        function changeMenu(a) {
            const group = $(a).attr('group');
            const shift = $(a).attr('shift');
            const dateSelection = null;

            $('#cctv_review, #helang_dc, .notes_aircond, .notes_fm200').hide();
            $('.editSave').show();
            $('#teststatus, #cctv_name').empty();
            $('#remarks, #incident_detail, #incident_number, #crm_log_no').val("");

            if (group === 'SCT Status' || group === 'Backup Status' || group === 'CCTV Review') {
                const target = group === 'CCTV Review' ? '#cctv_review' : '#helang_dc';
                $(target).show();
                const url = `/it_support/checklist/remarks/helangdcstatus/${group}/${dateSelection}/${shift}`;

                $.get(url, function(data) {
                    const items = "<option value='' disabled selected>Please select</option>" +
                        $.map(data, item => `<option value='${item.task_id}'>${item.esc_name}</option>`).join('');
                    const targetSelect = group === 'CCTV Review' ? "#cctv_name" : "#teststatus";
                    $(targetSelect).html(items).trigger("change");
                });
            }

            if ($.fn.DataTable.isDataTable('#list_datatable')) $('#list_datatable').DataTable().destroy();
            $('#list_datatable thead, #list_datatable tbody').empty();
            $('.spinner-loading').show();

            $.get(`/it_support/find_by/${group}/${dateSelection}/${shift}`, function(data) {
                $('.spinner-loading').hide();
                $('#list_datatable').html(data).fadeIn();
                $('#list_datatable').DataTable({
                    ordering: false,
                    lengthMenu: [
                        [20, 30, 50, -1],
                        [20, 30, 50, 'All']
                    ]
                });
            });
        }

        $(".btn-group").on("change", "#menu_button", function(e) {
            changeMenu(this)
        });
        $(".btn-group").on("click", "#menu_button1", function(e) {
            changeMenu(this)
        });

        $('.helang_remarks').on("change", '#teststatus', function() {
            $("#remarks_sct").val("");
            $task_id = $('#teststatus').val();
            var selectedTexted = $('#teststatus option:selected').text();
            if (selectedTexted === 'Aircon') {
                $('.notes_aircond').show();
                $('.notes_fm200').hide();
            }
            if (selectedTexted === 'FM200 Gas Pressure') {
                $('.notes_fm200').show();
                $('.notes_aircond').hide();
            }

            $.ajax({
                url: "/it_support/checklist/remarks/status/" + $task_id,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $("#remarks_sct").val($data[0].task_remarks);
                    $task_name = $("#trytest").val($data[0].task_name);
                    $task_name = ($("#trytest").val())
                    if ($task_name === 'Air Con') {
                        console.log('1')
                        $('.notes_aircond').show();
                        $('.notes_fm200').hide();
                    }
                    if ($task_name === 'FM200 Gas Pressure') {
                        $('.notes_fm200').show();
                        $('.notes_aircond').hide();
                    }
                }
            });
        });

        $('.cctv_remarks').on("change", '#cctv_name', function() {
            $("#incident_detail").val("");
            $("#incident_number").val("");
            $("#crm_log_no").val("");
            $task_id = $('#cctv_name').val();
            $.ajax({
                url: "/it_support/checklist/remarks/status/" + $task_id,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    console.log($data);
                    $("#incident_detail").val($data[0].incident_detail);
                    $("#incident_number").val($data[0].incident_number);
                    $("#crm_log_no").val($data[0].crm_log_no);
                }
            });
        });
    </script>
@endsection
