@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="cariandpform" action="{{ url('/find/fn-summary/') }}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{ $carian }}" class="form-control"
                onfocus="this.select();" placeholder="Klik carian di sini (PR, CR, PO, CO, DO, FN)... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if ($maklumatUmumPTJ == null && $Crdetails1 == null)
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{ $carian }} </strong>
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>{{ $docno }}</p>
                    </div>
                </div>
            </div>
        </div>
    @endif



    <!--DETAILS INFO-->
    @if ($maklumatUmumPTJ != null)
        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> Doc Number : <font color="yellow">{{ $docno }}</font></strong></h1>
                <h1><strong> PRCR Id : <font color="yellow">{{ $maklumatUmumPTJ[0]->fulfilment_req_id }}</font></strong>
                </h1>
                @if ($sqrn1 != null)
                    @foreach ($sqrn1 as $sq)
                        <h1><strong> SQ No : <font color="yellow">{{ $sq->quote_no }}</font>
                        </h1>
                        <h1><strong> RN No : <font color="yellow">{{ $sq->request_note_no }}</font>
                        </h1>
                    @endforeach
                @endif
                @if ($getCtInfo != null || $getCtInfo2 != null)
                    <a href='#modal-list-trans-ctinfo' class='modal-list-data-action ' data-toggle='modal'
                        data-url='/find/fn-summary/'>
                        <strong style="font-weight: bolder;">CT INFO
                            <i style="font-size: 10pt; padding-left:10px;" title="CT INFO"></i>
                        </strong>
                        <br />
                    </a>
                    <div id="modal-list-trans-ctinfo" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                        style="display: none;">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header text-center">
                                    <h2 class="modal-title" style="color: black;"><i class="fa fa-info-circle"></i> <span
                                            id="modal-list-data-header">CT INFO</span></h2>
                                </div>
                                <div class="modal-body">
                                    <div class="col-cl-12">
                                        <address>
                                            <font color="black"><strong>CT Number</strong> :
                                                {{ $getCtInfo[0]->contract_no }} ({{ $getCtInfo[0]->contract_id }})
                                            </font><br />
                                            <font color="black"><strong>Fulfilment Type</strong> :
                                                {{ $getCtInfo[0]->code_name }}</font><br />
                                            <font color="black"><strong>Schedule No</strong> :
                                                {{ $getCtInfo[0]->schedule_no }}
                                            </font><br />
                                            <font color="black"><strong>Start Delivery Date</strong> :
                                                {{ $getCtInfo[0]->start_delivery_date }}</font><br />
                                            <font color="black"><strong>Effective Date</strong> :
                                                {{ $getCtInfo[0]->eff_date }}</font><br />
                                            <font color="black"><strong>Expiry Date</strong> :
                                                {{ $getCtInfo[0]->exp_date }}</font><br />
                                            <font color="black"><strong>CR Createed Before Expired</strong> :
                                                {{ $getCtInfo[0]->cr_last_created }} |
                                                ({{ $getCtInfo[0]->last_co_b4_exp_days }} hari sebelum tarikh tamat
                                                kontrak)</font><br />
                                            <font color="black"><strong>Start Supplier Submit DO</strong> :
                                                {{ $getCtInfo[0]->supplier_can_submit_do }} |
                                                ({{ $getCtInfo[0]->supplier_do_days }} hari sebelum tarikh mula bekalan dan
                                                perkhidmatan)</font>
                                        </address>
                                    </div>
                                    <div class="col-cl-12">
                                        <address>
                                            <font color="black"><strong>Agreement</strong> :
                                                @if (isset($getCtInfo2) && count($getCtInfo2) > 0)
                                                    {{ $getCtInfo2[0]->agreement }}
                                                @else
                                                @endif
                                            </font><br />
                                            <font color="black"><strong>Count Doc</strong> :
                                                @if (isset($getCtInfo2) && count($getCtInfo2) > 0)
                                                    {{ $getCtInfo2[0]->count_doc_no }}
                                                @else
                                                @endif
                                            </font><br />
                                            <font color="black"><strong>Loa Ack Date</strong> :
                                                @if (isset($getCtInfo2) && count($getCtInfo2) > 0)
                                                    {{ $getCtInfo2[0]->ack_date }}
                                                @else
                                                @endif
                                            </font><br />
                                            <font color="black"><strong>Accumate Amount</strong> :
                                                @if (isset($getCtInfo2) && count($getCtInfo2) > 0)
                                                    {{ $getCtInfo2[0]->percent }}%
                                                @else
                                                @endif
                                            </font><br />
                                        </address>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>

            <div class="row">
                <div class="col-md-15">
                    <h6><strong>
                            <font color="black">{{ $maklumatUmumPTJ[0]->title }}</font>
                        </strong></h6><br />
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>PRCR Number</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->doc_no }}</font><br />
                        <strong>Created By</strong> : <font color="black">
                            @if ($req !== null)
                                {{ $req }}
                            @else
                                {{ $maklumatUmumPTJ[0]->login_id }} | {{ $maklumatUmumPTJ[0]->user_name }}

                            @endif
                        </font><br />
                        <strong>Approved By</strong> : <font color="black">
                            @if ($app !== null)
                                {{ $app }}
                            @else
                                {{ $maklumatUmumPTJ[0]->app_login }} | {{ $maklumatUmumPTJ[0]->app_name }}

                            @endif
                            ({{ $maklumatUmumPTJ[0]->group_code }})
                        </font><br />
                        <strong>Jenis Perolehan</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->jenisperolehan }}
                        </font><br />
                        <strong>Kategori Jenis Perolehan</strong> : <font color="black">
                            {{ $maklumatUmumPTJ[0]->kategorijenisperolehan }}</font><br />
                        <strong>Tahun Kewangan</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->financial_year }}
                        </font><br />
                        <strong>Tarikh Disediakan</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->created_date }}
                        </font><br />
                        <strong>Tempoh Penghantaran /Perkhidmatan (Hari)</strong> : <font color="black">
                            {{ $maklumatUmumPTJ[0]->delivery_term }}</font><br />
                        <strong>Tarikh Mula Penghantaran</strong> : <font color="black">
                            {{ $maklumatUmumPTJ[0]->start_date }}</font>
                        @if ($maklumatUmumPTJ[0]->date_adjustment == 1)
                            <a href='#modal-list-trans-date-adjustment' class='modal-list-data-action' data-toggle='modal'
                                data-req_item_id=''>
                                <strong style="font-weight: bolder;">
                                    <i class="gi gi-circle_exclamation_mark  text-danger"
                                        style="font-size: 10pt; padding-left:10px;"></i>
                                </strong>
                            </a>
                        @endif
                        <br />
                        <strong>Tarikh Akhir Penghantaran</strong> : <font color="black">
                            {{ $maklumatUmumPTJ[0]->end_date }}</font><br />
                    </address>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>Prepared For PTJ</strong> : <font><a class="modal-list-data-action"
                                href="{{ url('/find/orgcode/') }}/{{ $maklumatUmumPTJ[0]->ptjcode }}" target='_blank'>
                                {{ $maklumatUmumPTJ[0]->ptjcode }}</a>
                        </font>
                        <font color="black">{{ $maklumatUmumPTJ[0]->ptjname }}</font>
                        <br />
                        <strong>PTJ Group</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->groupptjname }}</font>
                        <br />
                        <strong>Pegawai Pengawal</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->pengawalname }}
                        </font><br />
                        <strong>Kementerian</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->ministryname }}</font>
                        <br />
                        <strong>Pejabat Perakaunan</strong> : <font color="black">
                            {{ $maklumatUmumPTJ[0]->office_name }}
                        </font><br />
                        <a href='#modal-list-trans-dofn-test' class='modal-list-data-action' data-toggle='modal'>
                            <strong style="font-weight: bolder;">GROUP RO NAME LIST
                                <i style="font-size: 10pt; padding-left:10px;"></i>
                            </strong>
                        </a><br />
                        @if (is_array($listAddressAvailable) >= 1)
                            <a href='#modal-list-trans-listAddress' class='modal-list-data-action ' data-toggle='modal'
                                data-url='/find/fn-summary/'>
                                <strong style="font-weight: bolder;">LIST OF ADDRESS
                                    <i style="font-size: 10pt; padding-left:10px;" title="ADDRESS INFO"></i>
                                </strong>
                                <br />
                            </a>
                            <div id="modal-list-trans-listAddress" class="modal fade" tabindex="-1" role="dialog"
                                aria-hidden="true" style="display: none;">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header text-center">
                                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                    id="modal-list-data-header">LIST OF ADDRESS</span></h2>
                                        </div>
                                        <div class="modal-body table-responsive">
                                            <table id="list_address_datatable"
                                                class="table table-vcenter table-condensed table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Address Id</th>
                                                        <th class="text-center">Address Name</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if ($listAddressAvailable != null)
                                                        @foreach ($listAddressAvailable as $listAddressDo)
                                                            <tr>
                                                                <td class="text-center">{{ $listAddressDo->address_id }}
                                                                </td>
                                                                <td class="text-center">{{ $listAddressDo->address }}</td>
                                                            </tr>
                                                        @endforeach
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif


                        @if (is_array($listAddress) >= 1)
                            <a href='#modal-list-trans-dofn2' class='modal-list-data-action ' data-toggle='modal'
                                data-url='/find/fn-summary/'>
                                <strong style="font-weight: bolder;">ADDRESS DETAILS
                                    <i style="font-size: 10pt; padding-left:10px;" title="ADDRESS INFO"></i>
                                </strong>
                                <br />
                            </a>
                            <div id="modal-list-trans-dofn2" class="modal fade" tabindex="-1" role="dialog"
                                aria-hidden="true" style="display: none;">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header text-center">
                                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                    id="modal-list-data-header">ADDRESS INFO</span></h2>
                                        </div>
                                        <div class="modal-body table-responsive">
                                            <table id="itemparcial-datatable"
                                                class="table table-vcenter table-condensed table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Do Number</th>
                                                        <th class="text-center">Do Status</th>
                                                        <th class="text-center">Address Id</th>
                                                        <th class="text-center">Address Name</th>
                                                        <th class="text-center">Address Type</th>
                                                        <th class="text-center">Start Date</th>
                                                        <th class="text-center">End Date</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if ($listAddress != null)
                                                        @foreach ($listAddress as $listAddressDo)
                                                            <tr>
                                                                <td class="text-center">
                                                                    {{ $listAddressDo->delivery_order_no }}</td>
                                                                <td class="text-center">{{ $listAddressDo->status_name }}
                                                                </td>
                                                                <td class="text-center">{{ $listAddressDo->address_id }}
                                                                </td>
                                                                <td class="text-center">{{ $listAddressDo->address }}</td>
                                                                <td class="text-center">{{ $listAddressDo->type }}</td>
                                                                <td class="text-center">{{ $listAddressDo->start_date }}
                                                                </td>
                                                                <td class="text-center">{{ $listAddressDo->end_date }}
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </address>
                </div>

                <div class="col-md-4">
                    <address>
                        <strong>Company Name</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->company_name }}
                            (<a href='#modal-list-trans-bank' class='modal-list-data-action ' data-toggle='modal'
                                data-url='/find/fn-summary/'>
                                <strong style="font-weight: bolder;">
                                    @if ($maklumatUmumPTJ[0]->branch_code !== null)
                                        {{ $maklumatUmumPTJ[0]->branch_name }}
                                    @else
                                        HQ
                                    @endif
                                </strong>
                            </a>
                            @if ($maklumatUmumPTJ[0]->branch_code !== null)
                                | {{ $maklumatUmumPTJ[0]->branch_code }}
                            @endif)
                            <div id="modal-list-trans-bank" class="modal fade" tabindex="-1" role="dialog"
                                aria-hidden="true" style="display: none;">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header text-center">
                                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                    id="modal-list-data-header">BANK INFO</span></h2>
                                        </div>
                                        <div class="modal-body table-responsive">
                                            <table id="bank-info-datatable"
                                                class="table table-vcenter table-condensed table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Branch Name</th>
                                                        <th class="text-center">Branch Address</th>
                                                        <th class="text-center">Bank Name</th>
                                                        <th class="text-center">Acc No</th>
                                                        <th class="text-center">Acc Purpose</th>
                                                        <th class="text-center">Is Hq</th>
                                                        <th class="text-center">Bank Branch</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @if ($bankInfo != null)
                                                        @foreach ($bankInfo as $list)
                                                            <tr>
                                                                <td class="text-center">
                                                                    {{ $list->branch_name }}</td>
                                                                <td class="text-center">
                                                                    @if ($list->address === null)
                                                                        HQ
                                                                        @else{{ $list->address }}
                                                                    @endif
                                                                </td>
                                                                <td class="text-center">{{ $list->fin_org_name }}</td>
                                                                <td class="text-center">{{ $list->account_no }}</td>
                                                                <td class="text-center">{{ $list->account_purpose }}</td>
                                                                <td class="text-center">{{ $list->is_for_hq }}</td>
                                                                <td class="text-center">{{ $list->bank_branch }}</td>
                                                            </tr>
                                                        @endforeach
                                                    @endif
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </font>
                        <br />
                        <strong>SSM No</strong> : <font color="black">{{ $maklumatUmumPTJ[0]->reg_no }}</font><br />
                        <strong>EP No</strong> : <font color="black">
                            <a class="modal-list-data-action"
                                href="{{ url('/find/epno/') }}/{{ $maklumatUmumPTJ[0]->ep_no }}" target='_blank'>
                                {{ $maklumatUmumPTJ[0]->ep_no }}</a>
                        </font><br />
                        @if ($getFactoringDetails != null)
                            <strong>SAP Order No</strong> : <font color="black">
                                {{ $getFactoringDetails[0]->sap_order_no }}</font><br />
                            <strong>Is Factoring</strong> : <font color="black">
                                {{ $getFactoringDetails[0]->is_factored }}</font>
                            <br />
                            <strong>Factoring Org Name</strong> : <font color="black">
                                {{ $getFactoringDetails[0]->factoring_org_name }}</font>
                            <br />
                            <strong>Address</strong> : <font color="black">
                                {{ $getFactoringDetails[0]->address_name }}{{ $getFactoringDetails[0]->address_1 }}{{ $getFactoringDetails[0]->address_2 }}{{ $getFactoringDetails[0]->address_3 }}
                                {{ $getFactoringDetails[0]->postcode }}{{ $getFactoringDetails[0]->city }}{{ $getFactoringDetails[0]->district }}{{ $getFactoringDetails[0]->state }}{{ $getFactoringDetails[0]->country }}
                            </font>
                            <br />
                        @endif
                    </address>
                </div>

                <div id="modal-list-trans-dofn-test" class="modal fade" tabindex="-1" role="dialog"
                    aria-hidden="true" style="display: none;">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h2 class="modal-title"><i class="fa fa-list"></i> <span
                                        id="modal-list-data-header">GROUP RO NAME LIST</span></h2>

                            </div>
                            <div class="modal-body table-responsive">
                                <div class="row table-responsive">
                                    @if ($getRoOfficer != null)
                                        <div class="col-sm-12">
                                            <strong>PTJ Address : {{ $getRoOfficer[0]->address_name }} ||
                                                {{ $getRoOfficer[0]->address }}</strong>
                                            <table id="ptjgroup-datatable"
                                                class="table table-vcenter table-condensed table-bordered">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Login Id</th>
                                                        <th class="text-center">Name</th>
                                                        <th class="text-center">Last Login Date</th>
                                                        <th class="text-center">Changed Date</th>
                                                        <th class="text-center">Address</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($getRoOfficer as $getRoOfficer)
                                                        <tr>
                                                            <td class="text-center">{{ $getRoOfficer->login_id }}</td>
                                                            <td class="text-center">{{ $getRoOfficer->user_name }}</td>
                                                            <td class="text-center">{{ $getRoOfficer->login_date }}
                                                            </td>
                                                            <td class="text-center">{{ $getRoOfficer->changed_date }}
                                                                {{ $getRoOfficer->date2 }}
                                                            </td>
                                                            <td class="text-center">{{ $getRoOfficer->address }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @endif
                                    @if ($listManualAddressUser != null)
                                        <div class="col-sm-12">
                                            <strong>PTJ Address (Manual): {{ $listManualAddressUser[0]->address_name }} ||
                                                {{ $listManualAddressUser[0]->address }}</strong>
                                            <table id="ptjgroupmanual-datatable"
                                                class="table table-vcenter table-condensed table-bordered table-responsive">
                                                <thead>
                                                    <tr>
                                                        <th class="text-center">Login Id</th>
                                                        <th class="text-center">Name</th>
                                                        <th class="text-center">Created Date</th>
                                                        <th class="text-center">Changed Date</th>
                                                        <th class="text-center">Address</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach ($listManualAddressUser as $getRoOfficer)
                                                        <tr>
                                                            <td class="text-center">{{ $getRoOfficer->login_id }}</td>
                                                            <td class="text-center">{{ $getRoOfficer->user_name }}</td>
                                                            <td class="text-center">{{ $getRoOfficer->created_date }}
                                                            </td>
                                                            <td class="text-center">{{ $getRoOfficer->changed_date }}
                                                            </td>
                                                            <td class="text-center">{{ $getRoOfficer->address }}</td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    @endif
    @if ($Crdetails1 != null || $maklumatUmumPTJ != null)
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> CURRENT USER </strong></h1>

            </div>
            <!--user accuired-->
            @if ($poname != null)
                @foreach ($popayment as $key => $row)
                    <h6> <Strong> {{ $row[0]['docNumber'] }} berada ditugasan
                            @foreach ($poname as $data2)
                                <a class="modal-list-data-action"
                                    href="{{ url('/find/userlogin') }}?login_id={{ $row[1][0][1] }}"
                                    target='_blank'>{{ $row[1][0][0] }}</a> untuk tindakan.
                            @endforeach
                        </strong></h6>
                @endforeach
            @endif

            @if (!empty($asigneeNameListPO1))
                @foreach ($asigneeNameListPO1 as $docNumber => $data)
                    <a class='modal-list-data-action1'>
                        <strong style="font-weight: bolder; color: black">
                            <h5><strong>{{ $docNumber }} : {{ $data[0]['taskName'] }}</strong></h5>
                            <i style="font-size: 10pt; padding-left:10px;"></i>
                        </strong>
                    </a>
                    @foreach ($data[0]['assigneeDetails'] as $assignee)
                        <a class="modal-list-data-action"
                            href="{{ url('/find/userlogin') }}?login_id={{ $assignee[1] }}" target='_blank'>
                            {{ $assignee[0] }}</a>
                        {{ $loop->last ? '' : ', ' }}
                    @endforeach
                @endforeach
            @endif
        </div>

        @if (
            (Auth::user()->isApproverTesting() && $frn_mm504 != null) ||
                $epp_013 != null ||
                $check_mm501 != null ||
                $submit_mm501 != null ||
                $list_mm506 != null ||
                $list_Epp_017ReturnN != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> Resolution </strong></h1>
                </div>
                @if ($submit_mm501 != null)
                    <h5><strong>
                            Trans_time : {{ $submit_mm501[0]->trans_date }} <br /><br />

                            Untuk makluman, tiada tindakan dapat diambil di pihak ePerolehan bagi isu ini. Memandangkan
                            sistem igfmas masih tidak membalas data intergrasi yang dihantar dari eP ke IGFMAS setelah
                            menghantar info MM501 dan eP masih tidak menerima maklum balas dr igfmas untuk info MM507
                            (ChargingCheckingStatus) menunjukkan POCO ini telah berjaya di igfmas. <br /><br />

                            eP hanya menerima maklumbalas '{!! nl2br(e($submit_mm501[0]->status_desc)) !!}' bagi {{ $submit_mm501[0]->doc_no }}
                            <br /><br />

                            Mohon pengguna log masalah ini di dalam iGFMAS helpdesk - iGFMAS Service Desk (SOLMAN) untuk
                            tindakan selanjutnya. Untuk makluman, tiada tindakan dapat diambil di pihak ePerolehan bagi isu
                            ini. Sebarang kesulitan amat dikesali. terima kasih
                    </h5></strong>
                @endif

                @if ($check_mm501 != null)
                    <h5><strong>
                            Trans_time : {{ $check_mm501[0]->created_date }} <br /><br />

                            {!! nl2br(e($check_mm501[0]->extracted_data)) !!} <br /><br />

                            Mohon untuk rujuk kuiri tersebut di igfmas atau log kes tersebut di iGFMAS helpdesk - iGFMAS
                            Service Desk (SOLMAN). terima kasih
                    </h5></strong>
                @endif

                @if ($epp_013 != null)
                    <h5><strong>
                            Trans_time : {{ $epp_013[0]->created_date }} <br /><br />

                            {!! nl2br(e($epp_013[0]->extracted_data)) !!} <br /><br />

                            Mohon untuk rujuk kuiri tersebut di igfmas atau log kes tersebut di iGFMAS helpdesk - iGFMAS
                            Service Desk (SOLMAN). terima kasih
                    </h5></strong>
                @endif

                @if ($frn_mm504 != null)
                    <h5><strong>
                            Trans_time : {{ $frn_mm504[0]->trans_date }} <br /><br />
                            {{ $frn_mm504[0]->status_desc }} <br /><br />
                            {!! nl2br(e($frn_mm504[0]->extracted_data)) !!} <br /><br />

                            Mohon untuk rujuk kuiri tersebut di igfmas atau log kes tersebut di iGFMAS helpdesk - iGFMAS
                            Service Desk (SOLMAN). terima kasih
                    </h5></strong>
                @endif

                @if ($list_mm506 != null && $list_mm506[0]->status_desc === 'Invois telah berjaya diwujudkan')
                    <h5><strong>
                            Trans_time : {{ $list_mm506[0]->date_create }} <br /><br />

                            {{ $list_mm506[0]->doc_no }} telah dipadankan oleh {{ $list_mm506[0]->user_name }} pada
                            {{ $list_mm506[0]->date_create }} dan telah mendapat maklum balas dari igfmas sebagai "Invois
                            telah berjaya diwujudkan" & status dokumen bertukar menjadi Menunggu Semakan Kuiri Arahan
                            Pembayaran dari 1GFMAS. <br /><br />

                            Namun, invoice ini masih belum disemak oleh AO kewangan di igfmas. <br /><br />

                            Oleh itu, mohon berhubung dengan AO di Bahagian Kewangan PTJ untuk membuat semakan ke atas
                            invois bagi nombor Pesanan Kerajaan di atas. <br /><br />

                            Jika dokumen tidak wujud di igfmas , log masalah ini di dalam iGFMAS helpdesk - iGFMAS Service
                            Desk (SOLMAN) untuk tindakan selanjutnya di igfmas. terima kasih
                    </h5></strong>
                @endif
                @if ($list_mm506 != null && $list_mm506[0]->status_desc === 'Invois pernah berjaya diwujudkan')
                    <h5><strong>
                            Trans_time : {{ $list_mm506[0]->date_create }} <br /><br />

                            As already check, {{ $list_mm506[0]->doc_no }} facing timeout from igfmas upon payment match
                            stage ( MM506 ), since ePerolehan received return as "Invois pernah berjaya diwujudkan" from
                            igfmas system. <br /><br />

                            Please advise users to log this issue in iGFMAS helpdesk - iGFMAS Service Desk (SOLMAN) for
                            further action. For information, no action can be taken on ePerolehan side. Any difficulties are
                            greatly regretted.tq

                    </h5></strong>
                @endif
                @if ($list_Epp_017ReturnN != null && $list_Epp_017ReturnN[0]->remarks_3 === 'N')
                    <h5><strong>
                            Trans_time : {{ $list_Epp_017ReturnN[0]->date_create }} <br /><br />

                            {!! nl2br(e($list_Epp_017ReturnN[0]->extracted_data)) !!} <br /><br />

                            Mohon untuk rujuk kuiri tersebut di igfmas atau log kes tersebut di iGFMAS helpdesk - iGFMAS
                            Service Desk (SOLMAN). terima kasih

                    </h5></strong>
                @endif
            </div>
        @endif

        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> PTJ USER LIST</strong></h1>
            </div>

            <div class="row">

                <div class="col-md-3 table-responsive">
                    <h5><strong> FL APPROVER </strong></h5>
                    <table id="fl_approver-datatable" class="table table-vcenter table-condensed table-bordered">
                        @if ($listFulfilmentApprover != null)
                            <thead>
                                <tr>
                                    <th class="text-center">PTJ Group</th>
                                    <th class="text-center">Role Code</th>
                                    <th class="text-center">Group Name</th>
                                    <th class="text-center">Group Status</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($listFulfilmentApprover as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->ptj }}<br />
                                            @if ($list->ptj !== null)
                                                <a class="modal-list-data-action"
                                                    href="{{ url('/find/orgcode/') }}/{{ $list->org_code }}"
                                                    target='_blank'>
                                                    {{ $list->org_code }}</a>
                                            @endif
                                        </td>
                                        <td class="text-left">
                                            <div class="modal-list-data-rnapprover">
                                                <a href='#modal-list-trans-dofn3' class='modal-list-data-action3'
                                                    data-toggle='modal'
                                                    data-url='/find/fl-summary/fl/approver/{{ $list->group_code }}/{{ $list->doc_no }}'
                                                    data-id='{{ $list->group_code }}'>
                                                    <strong style="font-weight: bolder;">FL Approver
                                                        <i style="font-size: 10pt; padding-left:10px;"
                                                            title="FL Approver"></i>
                                                    </strong>
                                                    <br />
                                                </a>
                                            </div>
                                        </td>
                                        <td class="text-left">{{ $list->group_name }} ({{ $list->group_code }})</td>
                                        <td class="text-left">{{ $list->group_status }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        @endif
                    </table>
                </div>
                <div id="modal-list-trans-dofn3" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                    style="display: none;">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                        id="modal-list-data-header">FL Approver</span></h2>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-sm-12">
                                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                                        </div>
                                        <div
                                            class="fl_approver_table table table-vcenter table-condensed table-bordered table-responsive">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <h5><strong> Acknowledge Officer </strong></h5>
                    <table id="ao-datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                        @if ($listAo != null)
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($listAo as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        @endif
                    </table>
                </div>
                @if ($listPMO != null)
                    <div class="col-md-3">
                        <h5><strong> Payment Match Officer </strong></h5>
                        <table id="pmo-datatable"
                            class="table table-vcenter table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($listPMO as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>
                @endif
                @if ($listFlActor != null)
                    <div class="col-md-3">
                        <h5><strong> FL Actor </strong></h5>
                        <table id="fl_actor_datatable"
                            class="table table-vcenter table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="text-center">Doc No</th>
                                    <th class="text-center">Login ID</th>
                                    <th class="text-center">Action Date</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($listFlActor as $list)
                                    <tr>
                                        <td class="text-left">({{ $list->doc_type }}) {{ $list->reference_no }}</td>
                                        <td class="text-left">{{ $list->role_code }} ({{ $list->user_id }})
                                            <br>{{ $list->user_name }}
                                        </td>
                                        <td class="text-left">{{ $list->action_date }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
        </div>
    @endif


    <!--STATUS INFO-->
    @if ($POCOstatus != null)
        <!--check workflow-->
        @if ($docno != null)
            <div class="list-trans-dofn marginbtm10">
                <a href='#modal-list-trans-dofn' class='modal-list-data-actiondo btn  btn-default ' data-toggle='modal'
                    data-url='/find/fn-summary/dofn/{{ $docno }}'
                    data-title='Status WorkFlow DO/FN Search By {{ $docno }}'>
                    <strong style="font-weight: bolder;">
                        <i class="fa fa-info-circle"></i>
                        Checking Status WorkFlow DO/FN</strong><br />
                </a>
            </div>
        @endif

        <div id="modal-list-trans-dofn" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
            style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List
                                DO/FRN</span></h2>
                    </div>
                    <div class="modal-body table-responsive">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <table id="listDOFRN-datatable"
                                    class="table table-vcenter table-condensed table-bordered table-responsive">

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="block table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> STATUS INFO </strong></h1>
            </div>
            <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                <thead>
                    <tr>
                        <th class="text-center">Doc Type</th>
                        <th class="text-center">Doc No</th>
                        <th class="text-center">Action Description</th>
                        <th class="text-center">Actioned Date</th>
                        <th class="text-center">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($POCOstatus != null)
                        @foreach ($POCOstatus as $status)
                            <tr>
                                <td class="text-center">{{ $status->doc_type }}</td>
                                <td class="text-center list-trans-dofn-wf">
                                    <a href='#modal-list-trans-dofn-wf' class='modal-list-data-action_wf'
                                        data-toggle='modal' data-url='/find/trans/docno/workflow/{{ $status->doc_no }}'
                                        data-title='Status WorkFlow Search By {{ $status->doc_no }}'>
                                        <strong style="font-weight: bolder;">
                                            {{ $status->doc_no }} </strong><br />
                                    </a>

                                </td>
                                <td class="text-center">{{ $status->action_desc }}</td>
                                <td class="text-center">{{ $status->actioned_date }}</td>
                                <td class="text-center">{{ $status->status_name }} ({{ $status->status_id }})</td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>
        </DIV>
    @endif

    <div id="modal-list-trans-dofn-wf" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Workflow
                            Status</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!--ITEM INFO-->
    @if ($listItemOrder == null && $itemListOrderBeforeApprove != null)
        <div class="block table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> ITEM LIST ORDER BEFORE APPROVE</strong></h1>
                <a href='#modal-list-trans-votlist' class='modal-list-data-action ' data-toggle='modal'
                    data-url='/find/fn-summary/'>
                    <strong style="font-weight: bolder;">VOT LIST
                        <i style="font-size: 10pt; padding-left:10px;"></i>
                    </strong>
                    <br />
                </a>
                <div id="modal-list-trans-votlist" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                    style="display: none;">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h2 class="modal-title" style="color: black;"><i class="fa fa-info-circle"></i> <span
                                        id="modal-list-data-header">VOT LIST</span></h2>

                                <div class="modal-body ">
                                    @if ($getVotList != null)
                                        <table id="vot_list_datatable"
                                            class="table table-vcenter table-condensed table-bordered table-responsive">
                                            <thead>
                                                <tr>
                                                    <th class="text-center" style="color: black;">Vot Type Fund</th>
                                                    <th class="text-center" style="color: black;">Vot Fund Code</th>
                                                    <th class="text-center" style="color: black;">Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($getVotList as $list)
                                                    <tr>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->vot_fund_type }} ({{ $list->vot_fund_type_name }})
                                                        </td>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->vot_fund_code }}</td>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->description }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <table id="item_order-datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                <thead>
                    <tr>
                        <th class="text-center">Item Code</th>
                        <th class="text-center">Item Name</th>
                        <th class="text-center">Item Description</th>
                        <th class="text-center">UOM</th>
                        <th class="text-center">Quantity Order</th>
                        @if (Auth::user()->isApproverTesting())
                            <th class="text-center">Unit Price</th>
                        @endif
                        <th class="text-center">Address</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach ($itemListOrderBeforeApprove as $listItemOrder1)
                        <tr>
                            <td class="text-center">{{ $listItemOrder1->extension_code }}</td>
                            <td class="text-center">{{ $listItemOrder1->item_name }}</td>
                            <td class="text-center">{{ $listItemOrder1->item_desc }}</td>
                            <td class="text-center">{{ $listItemOrder1->uom_name }}</td>
                            <td class="text-center">{{ $listItemOrder1->ordered_qty }}</td>
                            @if (Auth::user()->isApproverTesting())
                                <td class="text-center">{{ $listItemOrder1->unit_price }}</td>
                            @endif
                            <td class="text-center">{{ $listItemOrder1->address }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    @endif
    @if ($listItemOrder != null)
        <div class="block table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> ITEM LIST ORDER</strong></h1>
                <a href='#modal-list-trans-votlist' class='modal-list-data-action ' data-toggle='modal'
                    data-url='/find/fn-summary/'>
                    <strong style="font-weight: bolder;">VOT LIST
                        <i style="font-size: 10pt; padding-left:10px;"></i>
                    </strong>
                    <br />
                </a>
                <div id="modal-list-trans-votlist" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                    style="display: none;">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h2 class="modal-title" style="color: black;"><i class="fa fa-info-circle"></i>
                                    <span id="modal-list-data-header">VOT LIST</span>
                                </h2>

                                <div class="modal-body ">
                                    @if ($getVotList != null)
                                        <table id="vot_list_datatable"
                                            class="table table-vcenter table-condensed table-bordered table-responsive">
                                            <thead>
                                                <tr>
                                                    <th class="text-center" style="color: black;">Vot Type Fund
                                                    </th>
                                                    <th class="text-center" style="color: black;">Vot Fund Code
                                                    </th>
                                                    <th class="text-center" style="color: black;">Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($getVotList as $list)
                                                    <tr>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->vot_fund_type }}
                                                            ({{ $list->vot_fund_type_name }})
                                                        </td>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->vot_fund_code }}</td>
                                                        <td class="text-center" style="color: black;">
                                                            {{ $list->description }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <table id="item_order-datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                <thead>
                    <tr>
                        <th class="text-center">Charge Line</th>
                        <th class="text-center">Type</th>
                        <th class="text-center">PTJ Tanggung</th>
                        <th class="text-center">VOT</th>
                        <th class="text-center">Program/ Aktiviti</th>
                        <th class="text-center">Kod Akaun</th>
                        <th class="text-center">Item Code</th>
                        <th class="text-center">Code Exist</th>
                        <th class="text-center">Item Name</th>
                        <th class="text-center">UOM</th>
                        @if (Auth::user()->isApproverTesting())
                            <th class="text-center">Unit Price</th>
                            <th class="text-center">Ordered Amt</th>
                        @endif
                        <th class="text-center">Order Quantity</th>
                        <th class="text-center">Address</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach ($listItemOrder as $listItemOrder1)
                        <tr>
                            <td class="text-center">{{ $listItemOrder1->charge_line_seq }}</td>
                            <td class="text-center">{{ $listItemOrder1->item_type }}</td>
                            <td class="text-center"><a class="modal-list-data-action"
                                    href="{{ url('/find/orgcode/') }}/{{ $listItemOrder1->org_code }}"
                                    target='_blank'>{{ $listItemOrder1->org_code }}</a><br />
                                {{ $listItemOrder1->org_name }}</td>
                            <td class="text-center">{{ $listItemOrder1->vot_fund_code }}</td>
                            <td class="text-center">{{ $listItemOrder1->prg_activity_code }}</td>
                            <td class="text-center">{{ $listItemOrder1->gl_acc_code }}</td>
                            <td class="text-center">{{ $listItemOrder1->item_code }}</td>
                            <td class="text-center">
                                @isset($listItemOrder1->uom_code_igfmas)
                                    @if ($listItemOrder1->uom_code_igfmas === 'Need to trigger')
                                        <a class="modal-list-data-action"
                                            @isset($listItemOrder1->mminf_id) href="{{ url('/find/gfmas/mminfid/') }}/{{ $listItemOrder1->mminf_id }}/{{ $listItemOrder1->item_code }}" @endif
                                            target="_blank"> {{ $listItemOrder1->uom_code_igfmas }}
                                        </a>
                                @else
                                {{ $listItemOrder1->uom_code_igfmas }}
                                @endif
                                @endisset
                                            </td>
                                <td class="text-center">{{ $listItemOrder1->item_name }}</td>
                                <td class="text-center">{{ $listItemOrder1->uom_code }} |
                                    {{ $listItemOrder1->uom_name }}
                                </td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $listItemOrder1->unit_price }}</td>
                                    <td class="text-center">{{ $listItemOrder1->ordered_amt }}</td>
                                @endif
                                <td class="text-center">{{ $listItemOrder1->ordered_qty }}</td>
                                <td class="text-center">{{ $listItemOrder1->address }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        @if ($fnparcialarrays != null)
            <div class="block  table-responsive">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> ITEM LIST DELIVERY ORDER</strong></h1>
                </div>
                <table id="item_do-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">CHARGE LINE SEQ</th>
                            <th class="text-center">DO Number</th>
                            <th class="text-center">DO Status</th>
                            <th class="text-center">FN Number</th>
                            <th class="text-center">FN Status</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">Unit Price</th>
                            @endif
                            <th class="text-center">Order Quantity</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">Order Amt</th>
                            @endif
                            <th class="text-center">Delivered Quantity</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">Delivered Amt</th>
                            @endif
                            <th class="text-center">AO Received</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">AO Amt</th>
                            @endif
                            <th class="text-center">Item Name</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($fnparcialarrays as $itemparcial)
                            <tr>
                                <td class="text-center">{{ $itemparcial->charge_line_seq }}</td>
                                <td class="text-center">{{ $itemparcial->do_no }} <br/> EST : {{$itemparcial->est_delivery_date}}</td>
                                <td class="text-center">{{ $itemparcial->do_status_name }}</td>
                                <td class="text-center">{{ $itemparcial->frn_no }}</td>
                                <td class="text-center">{{ $itemparcial->frn_status_name }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $itemparcial->unit_price }}</td>
                                @endif
                                <td class="text-center">{{ $itemparcial->ordered_qty }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $itemparcial->ordered_amt }}</td>
                                @endif
                                <td class="text-center">{{ $itemparcial->delivered_qty }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $itemparcial->delivered_amt }}</td>
                                @endif
                                <td class="text-center">{{ $itemparcial->frn_received_qty }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $itemparcial->frn_received_amt }}</td>
                                @endif
                                <td class="text-center">{{ $itemparcial->item_name }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>

                @if (Auth::user()->isApproverTesting())
                    <table id="details_do-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">DO Number</th>
                                <th class="text-center">DO Status</th>
                                <th class="text-center">FN Number</th>
                                <th class="text-center">FN Status</th>
                                <th class="text-center">Ordered Amt</th>
                                <th class="text-center">Delivery Amt</th>
                                <th class="text-center">FRN Amt</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($getDODetails as $itemparcial)
                                <tr>
                                    <td class="text-center">{{ $itemparcial->do_no }}</td>
                                    <td class="text-center">{{ $itemparcial->do_status_name }}</td>
                                    <td class="text-center">{{ $itemparcial->frn_no }}</td>
                                    <td class="text-center">{{ $itemparcial->frn_status_name }}</td>
                                    <td class="text-center">{{ $itemparcial->ordered_amt }}</td>
                                    <td class="text-center">{{ $itemparcial->delivered_amt }}</td>
                                    <td class="text-center">{{ $itemparcial->frn_received_amt }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @endif
            </div>
        @endif

        @if ($listDnItem != null)
            <div class="block  table-responsive">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> DAN/CAN ITEM</strong></h1>
                </div>
                <table id="item_dan-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Doc No</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Chargeline No</th>
                            <th class="text-center">Item Name</th>
                            <th class="text-center">Qty</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">Amt</th>
                            @endif
                            <th class="text-center">Commitment No <h6><small> *Reservation No from iGFMAS*</small></h6>
                            </th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($listDnItem as $listDnItem)
                            <tr>
                                <td class="text-center">{{ $listDnItem->doc_no }}</td>
                                <td class="text-center">{{ $listDnItem->status_name }}</td>
                                <td class="text-center">{{ $listDnItem->charge_line_seq }}</td>
                                <td class="text-center">{{ $listDnItem->item_name }}</td>
                                <td class="text-center">{{ $listDnItem->adjusted_qty }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $listDnItem->adjusted_amt }}</td>
                                @endif
                                <td class="text-center">{{ $listDnItem->dan_commitment_no }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        @if ($listSDiItem != null)
            <div class="block  table-responsive">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> SDI ITEM</strong></h1>
                </div>
                <table id="item_sdi-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">SDI Number</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Recalled Quantity</th>
                            @if (Auth::user()->isApproverTesting())
                                <th class="text-center">Amt</th>
                            @endif
                            <th class="text-center">Item Name</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($listSDiItem as $listSDiItems)
                            <tr>
                                <td class="text-center">{{ $listSDiItems->stop_instr_no }}</td>
                                <td class="text-center">{{ $listSDiItems->status_name }}
                                    ({{ $listSDiItems->status_id }})
                                </td>
                                <td class="text-center">{{ $listSDiItems->recalled_qty }}</td>
                                @if (Auth::user()->isApproverTesting())
                                    <td class="text-center">{{ $listSDiItems->recalled_amt }}</td>
                                @endif
                                <td class="text-center">{{ $listSDiItems->item_name }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @endif

        <div id="modal-list-trans-date-adjustment" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
            style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5 class="modal-title"><i class="fa fa-info-circle"></i>
                            <span id="modal-list-data-header">Tanda jika ingin mengekalkan Tarikh Mula Penghantaran dan
                                Tarikh
                                Akhir Penghantaran seperti yang dikunci masuk oleh pengguna untuk dipaparkan di dalam
                                Pesanan
                                Kerajaan</span>
                        </h5>
                    </div>
                    <div class="modal-body">
                        <div class="row">
                            Tanda √ pada checkbox bagi mengekalkan Tarikh Mula Penghantaran dan Tarikh Akhir
                            Penghantaran yang
                            dikunci masuk oleh pengguna.<br />
                            Sekiranya checkbox tidak ditandakan, Tarikh Mula Penghantaran akan mengikut tarikh JANM
                            meluluskan
                            Pesanan Kerajaan.<br />
                            Tarikh Akhir Penghantaran pula akan dikira berdasarkan tempoh penghantaran yang telah
                            ditetapkan.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endsection

    @section('jsprivate')
        <!-- Load and execute javascript code used only in this page -->
        <!-- Load and execute javascript code used only in this page -->
        <script src="/js/pages/tablesDatatables.js"></script>
        <script>
            $(function() {
                TablesDatatables.init();
            });
            App.datatables();
            $('#list_address_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
            $('#tracking-diary-datatable').dataTable({
                order: [
                    [4, "desc"],
                    [0, "desc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
            $('#fl_actor_datatable').dataTable({
                order: [
                    [2, "desc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });

            function initializeDataTable(elementId) {
                $(elementId).dataTable({
                    order: [
                        [0, "asc"]
                    ],
                    columnDefs: [],
                    pageLength: 5,
                    lengthMenu: [
                        [5, 10, 15, 20, -1],
                        [5, 10, 15, 20, 'All']
                    ]
                });
            }

            initializeDataTable('#bank-info-datatable');
            initializeDataTable('#item_order-datatable');
            initializeDataTable('#item_do-datatable');
            initializeDataTable('#details_do-datatable');
            initializeDataTable('#item_sdi-datatable');
            initializeDataTable('#item_dan-datatable');
            initializeDataTable('#fl_approver-datatable');
            initializeDataTable('#ptjgroupmanual-datatable');
            initializeDataTable('#ao-datatable');
            initializeDataTable('#vot_list1_datatable');
            initializeDataTable('#vot_list_datatable');
            initializeDataTable('#pmo-datatable');


            var APP_URL = {!! json_encode(url('/')) !!}

            $(document).on("click", '.modal-list-data-actiondo', function() {
                $('#modal-list-data-header').text($(this).attr('data-title'));
                $('.spinner-loading').show();
                $('#listDOFRN-datatable').hide();

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data);
                        $('.spinner-loading').hide();
                        $('#listDOFRN-datatable').html($data).fadeIn();
                        if ($.fn.dataTable.isDataTable('#listDOFRN-datatable')) {
                            $('#listDOFRN-datatable').DataTable().destroy();
                        }
                        $('#listDOFRN-datatable').DataTable({
                            order: [
                                [0, "asc"]
                            ],
                            pageLength: 20,
                            lengthMenu: [
                                [20, 30, 50, -1],
                                [20, 30, 50, 'All']
                            ]
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error("An error occurred: " + error);
                        $('.spinner-loading').hide();
                    }
                });
            });

            $(document).ready(function() {
                $(document).on("click", ".modal-list-data-action_wf", function() {

                    $('.spinner-loading').show();
                    $('.trans-div-detail').html('Please wait ...').fadeIn();

                    $('#modal-list-data-header').text($(this).attr('data-title'));

                    $.ajax({
                        url: APP_URL + $(this).attr('data-url'),
                        type: "GET",
                        success: function(data) {
                            $data = $(data)
                            $('.spinner-loading').hide();
                            $('.trans-div-detail').html($data).fadeIn();
                        }
                    });

                });

            });

            $(document).on("click", '.modal-list-data-action3', function() {
                $('#fl_approver_table').hide();
                $('.spinner-loading').show();
                $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('.fl_approver_table').html($data).fadeIn();
                        $('#fl_approver_table').dataTable({
                            order: [
                                [0, "desc"]
                            ],
                            columnDefs: [],
                            pageLength: 5,
                            lengthMenu: [
                                [5, 10, 20, 30, 50, -1],
                                [10, 20, 30, 50, 'All']
                            ]
                        });
                    }
                });

            });
        </script>
    @endsection
