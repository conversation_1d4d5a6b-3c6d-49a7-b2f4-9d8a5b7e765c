@extends('layouts.guest-dash')

@section('header')
<style type="text/css"> 
    .highlight {
        background: yellow;
    }

</style>
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/stl/crm') }}"> SLA CRM/POMS</a>
            </li>
            <li class="active">
                <a href="{{ url('/stl/crm/duplicatetask') }}"> DUPLICATE TASKS</a>
            </li>
            <li>
                <a href="{{ url('/stl/crm/sla') }}"> TASK FLOW</a>
            </li>
        </ul>
    </div>
</div>
@if (Auth::user())
<div class="row">
    <form action=" {{ url('/stl/crm/duplicatetask') }}" method="post" class="form-horizontal form-bordered">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-6 control-label" for="dateStart">Date Start</label>
                        <div class="col-md-6">
                            <input type="date" id="dateStart" name="dateStart" class="form-control" value="{{ $dateStart }}">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-6 control-label" for="dateEnd">Date End</label>
                        <div class="col-md-6">
                            <input type="date" id="dateEnd" name="dateEnd" class="form-control" value="{{ $dateEnd }}">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-sm btn-primary pull-right"><i class="fa fa-search"></i> Search </button>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </form>  
</div>
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class="widget-extra themed-background-dark">
                <h5 class='widget-content-light'>
                    DUPLICATE TASK - <strong>IT Incident</strong>
                </h5>
            </div>
            <div class="table-responsive">
                <table id="basic-datatable" class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-left">CASE NUMBER</th>
                            <th class="text-left">REQUEST TYPE</th>
                            <th class="text-left">DATE CREATED</th>
                            <th class="text-left">DATE MODIFIED</th>
                            <th class="text-left">STATE</th>
                            <th class="text-left">STATUS</th>
                            <th class="text-center">TOTAL TASK</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($result))
                        @foreach($result as $val)
                        <tr>
                            <td class="text-left"> {{ $val->case_number }} </td>
                            <td class="text-left"> {{ $val->request_type_c }} <br/> {{ $val->incident_service_type_c }} </td>
                            <td class="text-left"> {{ $val->date_entered }} </td>
                            <td class="text-left"> {{ $val->date_modified }} </td>
                            <td class="text-left"> {{ $val->casestate }} </td>
                            <td class="text-left"> {{ $val->casestatus }} </td>
                            <td class="text-center detail" data-id="{{ $val->case_number }}" data-value="{{ $val->id }}" 
                                data-target="task-detail"><strong><a style="color: red">{{ $val->total  }} </a></strong></td> 
                        </tr>
                        @endforeach
                        @endif 
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="row" id="task-row" style="display: none">
    <div class="col-lg-12" id="case_detail_div">
        <div class="widget">
            <div class="widget-extra themed-background-coral">
                <h5 class='widget-content-light'>
                    CASE : <strong><span id="caseNumber" name="caseNumber" value=""></span> </strong>
                </h5>
            </div>
            <input type="hidden" id="case_no" name="case_no"/>
            <div class="table-responsive">
                <table class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">Task No.</th>
                            <th class="text-center">Task Name</th>
                            <th class="text-center">Task Created</th>
                            <th class="text-center">Task Modified</th>
                            <th class="text-center">Task Status</th>
                            <th class="text-center">Task Flag</th>
                            <th class="text-center">Created By</th>
                            <th class="text-center">Task Deleted</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="task-body">

                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-md">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title text-center">DELETE TASK</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <input type="hidden" id="case_id_display" name="case_id_display"/>
                            <div class="form-group">
                                <div class="col-md-9">
                                    <table class="table table-vcenter" id="task-table">
                                        <tbody>
                                            <tr style="display: none">
                                                <th>Task Id </th>  
                                                <td id='taskid_table'><input type="hidden" id='taskid_display' name="taskid_display"></td>
                                            </tr>
                                            <tr>
                                                <th>Task Number </th>  
                                                <td id='taskno_display'></td>
                                            </tr>
                                            <tr>
                                                <th>Task Name </th> 
                                                <td id='taskname_display'></td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_confirm_delete"><i class="gi gi-ok_2"></i> Yes</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
</div>
@endif
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
</script>
<script>

    $('.detail').click(function () {

        $('#task-row').hide();
        var caseNumber = $(this).attr('data-id');
        var caseId = $(this).attr('data-value');

        $.ajax({
            type: "GET",
            url: "/stl/crm/taskdetail",
            data: {"caseNumber": caseNumber}
        }).done(function (resp) {

            var html = '';
            $.each(resp, function (index, value) {

                html += '<tbody><tr><td class="text-center">' + value["task_number_c"] + '</td>';
                html += '<td class="text-left">' + value["taskname"] + '</td>';
                html += '<td class="text-center">' + value["task_date_created"] + '</td>';
                html += '<td class="text-center">' + value["task_date_modified"] + '</td>';
                html += '<td class="text-left">' + value["taskstatus"] + '</td>';
                html += '<td class="text-center">' + value["sla_task_flag_c"] + '</td>';
                html += '<td class="text-center">' + value["createdby"] + '</td>';
                html += '<td class="text-center">' + value["task_deleted"] + '</td>';
                if (value["taskstatus"] !== 'Completed' || value["taskstatus"] !== 'Approved') {
                    html += '<td class="text-center taskdetail" data-id="' + value["taskid"] + '" data-value="' + value["task_number_c"] + '" data-name="' + value["taskname"] + '">\n\
                        <button id="update_task" class="btn btn-sm btn-primary"> Update</button></td>';
                } else {
                    html += '<td></td>';
                }
                html += '</tr></tbody>';
            });
            $("input[name=case_no]").val(caseNumber);
            $("input[name=case_id]").val(caseId);
            document.getElementById("caseNumber").innerHTML = caseNumber;
            document.getElementById("task-body").innerHTML = html;
            $('#task-row').show();

            $('.taskdetail').click(function () {
                $('#myModal').modal('hide');
                var taskId = $(this).attr('data-id');
                var taskNumber = $(this).attr('data-value');
                var taskName = $(this).attr('data-name');
                $('#myModal').modal('show');
                $("#taskid_display").text(taskId);
                $("#taskno_display").text(taskNumber);
                $("#taskname_display").text(taskName);
                console.log(taskId);
            });
        });

    });

    $('div.form-actions').on("click", 'button.action_confirm_delete', function () {
        var task_id = $("#task-table #taskid_table").text();
        var case_number = $("input[name=case_no]").val();

        var csrf = $("input[name=_token]").val();
        $.ajax({
            type: "POST",
            url: "/stl/crm/duplicatetask/delete",
            data: {"_token": csrf, "taskId": task_id, "caseNumber": case_number}
        }).done(function (resp) {
            $('#myModal').modal('hide');
            setTimeout(location.reload.bind(location), 2000);
        });
    });
</script>

@endsection
