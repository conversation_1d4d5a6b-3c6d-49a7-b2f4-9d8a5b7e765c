@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="cariandpform" action="{{ url('/find/ct-summary') }}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{ $carian }}" class="form-control"
                onfocus="this.select();" placeholder="Klik carian di sini (Kontrak Sahaja)... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    @if ($contractDetails == null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{ $carian }} {{ $docno }}</strong>
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>Tidak dijumpai!</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if ($contractDetails != null)
        <div class="block block-alt-noborder full">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> Doc Number : <font color="yellow">{{ $docno }} </font>
                        ({{ $contractDetails[0]->contract_id }}) | Contract Ver :
                        {{ $ver }}</strong></h1>
                @if ($contractDetails[0]->jenis_pemenuhan === 'Bermasa (Berjadual)')
                    <a href='#modal-list-schedule-list' class='modal-list-data-action' data-toggle='modal'>
                        <strong style="font-weight: bolder;">Schedule No
                            <i style="font-size: 10pt; padding-left:10px;"></i>
                        </strong>
                    </a>
                @endif
                @if ($getContractVer != null)
                    <a href='#modal-list-contract_ver-list' class='modal-list-data-action' data-toggle='modal'>
                        <strong style="font-weight: bolder;">Loa Physical No
                            <i style="font-size: 10pt; padding-left:10px;"></i>
                        </strong>
                    </a>
                @endif
            </div>

            <div class="row">
                <div class="col-md-15">
                    <h6><strong>
                            <font color="black">{{ $contractDetails[0]->contract_name }}</font>
                        </strong></h6><br />
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>Created By</strong> : <font color="black">
                            <a class="modal-list-data-action"
                                href="{{ url('/find/userlogin') }}?login_id={{ $contractDetails[0]->login_id }}"
                                target='_blank'>{{ $contractDetails[0]->user_name }}</a>
                        </font><br />
                        <strong>Prepared For PTJ</strong> : <font><a class="modal-list-data-action"
                                href="{{ url('/find/orgcode/') }}/{{ $contractDetails[0]->org_code }}" target='_blank'>
                                {{ $contractDetails[0]->org_code }}</a>
                        </font>
                        <font color="black">{{ $contractDetails[0]->org_name }}</font>
                        <br />
                        <strong>Contract Type</strong> : <font color="black">{{ $contractDetails[0]->jenis_kontrak }}
                        </font><br />
                        <strong>Fulfilment Type</strong> : <font color="black">{{ $contractDetails[0]->jenis_pemenuhan }}
                        </font><br />
                        <strong>Cateogry Type</strong> : <font color="black">{{ $contractDetails[0]->jenis_category_perolehan }}
                        </font><br />
                        <strong>kaedah perolehan</strong> : <font color="black">
                            {{ $contractDetails[0]->kaedah_perolehan }}</font><br />
                        <strong>Effective Date</strong> : <font color="black">{{ $contractDetails[0]->eff_date }}</font>
                        <br />
                        <strong>Expire Date</strong> : <font color="black">{{ $contractDetails[0]->exp_date }}</font>
                        <br />
                    </address>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>Loa No</strong> : <font color="black">{{ $contractDetails[0]->loa_no }}</font><br />
                        <strong>Loa Physical No</strong> : <font color="black">{{ $contractDetails[0]->loa_physical_no }}
                        </font><br />
                        <strong>QT No</strong> : <font color="black">{{ $contractDetails[0]->qt_no }}</font><br />
                        @if (isset($getKodBidangQt) && count($getKodBidangQt) > 0)
                            <a href='#modal-list-kod-bidang-list' class='modal-list-data-action' data-toggle='modal'>
                                <strong style="font-weight: bolder;">Kod Bidang
                                    <i style="font-size: 10pt; padding-left:10px;"></i>
                                </strong>
                            </a><br />
                        @endif
                        <strong>Publish Date</strong> : <font color="black">{{ $contractDetails[0]->publish_date }}</font>
                        <br />
                        <strong>Ack Loa Date</strong> : <font color="black">
                            {{ $contractDetails[0]->tarikh_pengesahan_loa }}</font><br />
                        <strong>Delivery Term</strong> : <font color="black">{{ $contractDetails[0]->delivery_term }} 
                            @if($contractDetailsChanges !== null && $contractDetailsChanges[0]->delivery_term !== $contractDetails[0]->delivery_term) <strong style="font-weight: bolder;">
                                <i class="gi gi-circle_exclamation_mark  text-danger"
                                style="font-size: 10pt; padding-left:10px;"
                                title="have changes!"></i>
                            </strong>{{ $contractDetailsChanges[0]->delivery_term }}  @endif
                        </font><br />
                    </address>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>Concession</strong> : <font color="black">{{ $contractDetails[0]->is_concession }}
                            @if($contractDetailsChanges !== null && $contractDetailsChanges[0]->is_concession !== $contractDetails[0]->is_concession) <strong style="font-weight: bolder;">
                                <i class="gi gi-circle_exclamation_mark  text-danger"
                                style="font-size: 10pt; padding-left:10px;"
                                title="have changes!"></i>
                            </strong>{{ $contractDetailsChanges[0]->is_concession }}  @endif
                        </font>
                        <br />
                        <strong>Agreement</strong> : <font color="black">{{ $contractDetails[0]->is_agreement }}</font>
                        <br />
                        <strong>Agreement LOA</strong> : <font color="black">{{ $contractDetails[0]->is_agreement_loa }}</font>
                        <br />
                        @if ($contractDetails[0]->is_agreement === 'Yes')
                            <strong>Agreement Status</strong> : <font color="black">
                                @if(isset($getAgreementStatus) && !empty($getAgreementStatus) && $getAgreementStatus[0] !== null )  {{ $getAgreementStatus[0]->status_name }}@else NULL @endif</font>
                            <br />
                        @endif
                        <strong>Fulfilment Status</strong> : <font color="black">
                            {{ $getFulfilmentStatus[0]->status_name }}</font>
                        <br />
                        <strong>Bond Req LOA</strong> : <font color="black">{{ $contractDetails[0]->is_bond_req_loa }}</font>
                        <br />
                        <strong>Bond Req</strong> : <font color="black">{{ $contractDetails[0]->is_bond_req }}</font>
                        <br />
                        <strong>Bond Exempted</strong> : <font color="black">{{ $contractDetails[0]->is_bond_exempted }}
                        </font><br />
                        <strong>Bond Waived</strong> : <font color="black">{{ $contractDetails[0]->is_bond_waived }}
                        </font><br />
                    </address>
                </div>
            </div>
        </div>

        @if ($getSupplierDetails != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> SUPPLIER INFO</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <address>
                            <strong>Company Name</strong> : <font color="black">{{ $getSupplierDetails[0]->company_name }}
                            </font><br />
                            <strong>Reg No</strong> : <font color="black">{{ $getSupplierDetails[0]->reg_no }}
                            </font><br />
                            <strong>Mof No</strong> : <font color="black">{{ $getSupplierDetails[0]->mof_no }}</font>
                            <br />
                            <strong>Ep No</strong> : <font color="black">
                                <a class="modal-list-data-action"
                                    href="{{ url('/find/epno/') }}/{{ $getSupplierDetails[0]->ep_no }}" target='_blank'>
                                    {{ $getSupplierDetails[0]->ep_no }}</a>
                            </font><br />
                            <strong>Address</strong> : <font color="black">{{ $getSupplierDetails[0]->address_1 }}
                                {{ $getSupplierDetails[0]->address_2 }} {{ $getSupplierDetails[0]->postcode }},
                                {{ $getSupplierDetails[0]->city }}, {{ $getSupplierDetails[0]->district }},
                                {{ $getSupplierDetails[0]->state }}, {{ $getSupplierDetails[0]->country }} </font>
                            <br />
                        </address>
                    </div>
                    <div class="col-md-6">
                        <address>
                            <strong>Bank Name</strong> : <font color="black">{{ $getSupplierDetails[0]->fin_org_name }}
                            </font><br />
                            <strong>Account No</strong> : <font color="black">{{ $getSupplierDetails[0]->account_no }}
                            </font><br />
                            <strong>Branch Name</strong> : <font color="black">{{ $getSupplierDetails[0]->bank_branch }}
                            </font><br />
                            <strong>Bank Address</strong> : <font color="black">
                                {{ $getSupplierDetails[0]->address1_bank }} {{ $getSupplierDetails[0]->address2_bank }}
                                {{ $getSupplierDetails[0]->postcode_bank }}, {{ $getSupplierDetails[0]->city_bank }},
                                {{ $getSupplierDetails[0]->district_bank }}, {{ $getSupplierDetails[0]->state_bank }},
                                {{ $getSupplierDetails[0]->country_bank }} </font><br />
                                <strong>Pegawai untuk Dihubungi</strong> : <font color="black">{{ $getSupplierDetails[0]->contact_person }}
                                </font><br />
                                <strong>Email</strong> : <font color="black">{{ $getSupplierDetails[0]->contact_email }}
                                </font><br />
                        </address>
                    </div>
                </div>
            </div>
        @endif
        @if ($getContractAdmin != null || $getContractApprover != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> PTJ USER LIST</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-4">
                        <h5><strong> Contract Admin </strong></h5>
                        <table id="ca_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractAdmin as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>

                    <div class="col-md-4">
                        <h5><strong> Contract Approver Group</strong></h5>
                        <table id="approver_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractApprover as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <div class="col-md-4">
                        <h5><strong> Contract Approver (Selected)</strong></h5>
                        <table id="approver_select_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractApproverSelected as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @if($getContractApproverSelectedChanges !== null && $getContractApproverSelectedChanges[0]->login_id !==  $getContractApproverSelected[0]->login_id) 
                        <h5><strong> Contract Approver (Selected)  <strong style="font-weight: bolder;">
                            <i class="gi gi-circle_exclamation_mark  text-danger"
                                style="font-size: 10pt; padding-left:10px;"
                                title="have changes!"></i>
                        </strong></strong></h5>
                        <table id="approver_select_change_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Login ID</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractApproverSelectedChanges as $list)
                                    <tr>
                                        <td class="text-left">{{ $list->user_name }}</td>
                                        <td class="text-left">{{ $list->login_id }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                        @endif
                    </div>
                </div>
            </div>
        @endif
        @if ($getContractAgencies != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> AGENCY LIST</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <table id="agency_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Id</th>
                                    <th class="text-center">Minitry</th>
                                    <th class="text-center">PTJ Group</th>
                                    <th class="text-center">PTJ</th>
                                    <th class="text-center">Ceiling Amount</th>
                                    <th class="text-center">Effective Date</th>
                                    <th class="text-center">Expiry Date</th>
                                    <th class="text-center">Record Status</th>
                                    <th class="text-center">Address</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractAgencies as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->agency_id }}</td>
                                        <td class="text-center">{{ $list->kementerian }}</td>
                                        <td class="text-center">{{ $list->kumpulan_ptj }}</td>
                                        <td class="text-center">(<a class="modal-list-data-action"
                                                href="{{ url('/find/orgcode/') }}/{{ $list->org_code }}"
                                                target='_blank'>{{ $list->org_code }}</a>) {{ $list->ptj }}</td>
                                        <td class="text-center">{{ $list->ceiling_amount }}</td>
                                        <td class="text-center">{{ $list->eff_date }}</td>
                                        <td class="text-center">{{ $list->exp_date }}</td>
                                        <td class="text-center">{{ $list->record_status }}</td>
                                        <td class="text-center agency_address"><a href='#modal-list-address-list'
                                                class='modal-list-data-action'
                                                data-url='/find/address/agency/{{ $list->agency_id }}/{{ $ver }}'
                                                data-toggle='modal'>
                                                <strong style="font-weight: bolder;">Address List
                                                    <i style="font-size: 10pt; padding-left:10px;"></i>
                                                </strong>
                                            </a></td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($agencyNotSame != null)
                    <div class="row">
                        @if ($getVerId != null)
                            <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type :
                                {{ $getVerId[0]->doc_type }}</label>
                        @endif
                        <div class="col-md-12">
                            <table id="agency_list_current_datatable"
                                class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Minitry</th>
                                        <th class="text-center">PTJ Group</th>
                                        <th class="text-center">PTJ</th>
                                        <th class="text-center">Ceiling Amount</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Address</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($agencyNotSame as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->agency_id }}</td>
                                            <td class="text-center">{{ $list->kementerian }}</td>
                                            <td class="text-center">{{ $list->kumpulan_ptj }}</td>
                                            <td class="text-center">(<a class="modal-list-data-action"
                                                    href="{{ url('/find/orgcode/') }}/{{ $list->org_code }}"
                                                    target='_blank'>{{ $list->org_code }}</a>) {{ $list->ptj }}</td>
                                            <td class="text-center">{{ $list->ceiling_amount }}</td>        
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->record_status }}</td>
                                            <td class="text-center agency_address"><a href='#modal-list-address-list'
                                                    class='modal-list-data-action'
                                                    data-url='/find/address/agency/{{ $list->agency_id }}/{{ $ver }}'
                                                    data-toggle='modal'>
                                                    <strong style="font-weight: bolder;">Address List
                                                        <i style="font-size: 10pt; padding-left:10px;"></i>
                                                    </strong>
                                                </a></td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        @if ($getContractItem != null || $getItemZone != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> ITEM LIST</strong></h1> 
                    <a href="{{ url('/ct-summary/download/item') }}/{{ $ver }}/{{ $docno }}"
                        target="_blank" class="btn btn-sm btn-primary"><i
                            class="fa fa-download"></i> Download</a>
                </div>
                @if ($getItemZone != null)
                    <div class="row">
                        <div class="col-md-12">
                            <table id="item_list_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Zone Name</th>
                                        <th class="text-center">State Name</th>
                                        
                                            @if ($getItemZone[0]->city_name != null)
                                            <th class="text-center">City Name</th>
                                            @elseif($getItemZone[0]->division_name != null)
                                            <th class="text-center">Division Name</th>
                                            @elseif($getItemZone[0]->district_name != null)
                                            <th class="text-center">District Name</th>
                                            @endif
                                        
                                        <th class="text-center">Delivery Term</th>
                                        <th class="text-center">Nama Item</th>
                                        <th class="text-center">Jenis Item</th>
                                        <th class="text-center">Jenis Barang</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Min</th>
                                        <th class="text-center">Max</th>
                                        <th class="text-center">Used</th>
                                        <th class="text-center">balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($getItemZone as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->zone_name }}</td>
                                            <td class="text-center">{{ $list->state_name }}</td>
                                            @if ($getItemZone[0]->city_name != null)
                                            <td class="text-center">{{ $list->city_name }}</td>
                                                @elseif($getItemZone[0]->division_name != null)
                                                <td class="text-center">{{ $list->division_name }}</td>
                                                @elseif($getItemZone[0]->district_name != null)
                                                <td class="text-center">{{ $list->district_name }}</td>
                                                @endif
                                            <td class="text-center">{{ $list->delivery_term }}</td>
                                            <td class="text-center">{{ $list->item_name }} <h5>
                                                    <small>{{ $list->extension_code }}</small>
                                                </h5>
                                            </td>
                                            <td class="text-center">{{ $list->jenis_item }}</td>
                                            <td class="text-center">{{ $list->jenis_barang }}</td>
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->status }}</td>
                                            <td class="text-center">{{ $list->minimum_order_qty }}</td>
                                            <td class="text-center">{{ $list->max_quantity }}</td>
                                            <td class="text-center">{{ $list->qty }}</td>
                                            <td class="text-center">{{ $list->balance }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @elseif ($getContractItem != null)
                    <div class="row">
                        <div class="col-md-12">
                            <table id="item_list_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Nama Item</th>
                                        <th class="text-center">Jenis Item</th>
                                        <th class="text-center">Jenis Barang</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Min</th>
                                        <th class="text-center">Max</th>
                                        <th class="text-center">Used</th>
                                        <th class="text-center">Balance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($getContractItem as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->item_name }} <h5>
                                                    <small>{{ $list->extension_code }}</small>
                                                </h5>
                                            </td>
                                            <td class="text-center">{{ $list->jenis_item }}</td>
                                            <td class="text-center">{{ $list->jenis_barang }}</td>
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->status }}</td>
                                            <td class="text-center">{{ $list->minimum_order_qty }}</td>
                                            <td class="text-center">{{ $list->max_quantity }}</td>
                                            <td class="text-center">{{ $list->qty }}</td>
                                            <td class="text-center">{{ $list->balance }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif

                @if ($itemZoneNotSame != null)
                    <div class="row">
                        @if ($getVerId != null)
                            <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type
                                :
                                {{ $getVerId[0]->doc_type }}</label>
                        @endif
                        <div class="col-md-12">
                            <table id="item_list_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Zone Name</th>
                                        <th class="text-center">State Name</th>
                                        <th class="text-center">
                                            @if ($getItemZone[0]->city_name != null)
                                                City Name
                                            @elseif($getItemZone[0]->division_name != null)
                                                Division Name
                                            @elseif($getItemZone[0]->district_name != null)
                                                District Name
                                            @endif
                                        </th>
                                        <th class="text-center">Delivery Term</th>
                                        <th class="text-center">Nama Item</th>
                                        <th class="text-center">Jenis Item</th>
                                        <th class="text-center">Jenis Barang</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Min</th>
                                        <th class="text-center">Max</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($getItemZone as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->zone_name }}</td>
                                            <td class="text-center">{{ $list->state_name }}</td>
                                            <td class="text-center">
                                                @if ($getItemZone[0]->city_name != null)
                                                    {{ $list->city_name }}
                                                @elseif($getItemZone[0]->division_name != null)
                                                    {{ $list->division_name }}
                                                @elseif($getItemZone[0]->district_name != null)
                                                    {{ $list->district_name }}
                                                @endif
                                            </td>
                                            <td class="text-center">{{ $list->delivery_term }}</td>
                                            <td class="text-center">{{ $list->item_name }} <h5>
                                                    <small>{{ $list->extension_code }}</small>
                                                </h5>
                                            </td>
                                            <td class="text-center">{{ $list->jenis_item }}</td>
                                            <td class="text-center">{{ $list->jenis_barang }}</td>
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->status }}</td>
                                            <td class="text-center">{{ $list->minimum_order_qty }}</td>
                                            <td class="text-center">{{ $list->max_quantity }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @elseif($itemNotSame != null)
                    <div class="row">
                        @if ($getVerId != null)
                            <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type
                                :
                                {{ $getVerId[0]->doc_type }}</label>
                        @endif
                        <div class="col-md-12">
                            <table id="item_list_current_datatable"
                                class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Nama Item</th>
                                        <th class="text-center">Jenis Item</th>
                                        <th class="text-center">Jenis Barang</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Minimum Kuantiti Pesanan</th>
                                        <th class="text-center">Maksimum Kuantiti</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($itemNotSame as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->item_name }} <h5>
                                                    <small>{{ $list->extension_code }}</small>
                                                </h5>
                                            </td>
                                            <td class="text-center">{{ $list->jenis_item }}</td>
                                            <td class="text-center">{{ $list->jenis_barang }}</td>
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->status }}</td>
                                            <td class="text-center">{{ $list->minimum_order_qty }}</td>
                                            <td class="text-center">{{ $list->max_quantity }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif

                @if ($unitPriceNotSame != null)
                    <div class="row">
                        @if ($getVerId != null)
                            <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type
                                :
                                {{ $getVerId[0]->doc_type }}</label>
                        @endif
                        <div class="col-md-12">
                            <table id="item_price_list_current_datatable"
                                class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Nama Item</th>
                                        <th class="text-center">Jenis Item</th>
                                        <th class="text-center">Jenis Barang</th>
                                        <th class="text-center">Effective Date</th>
                                        <th class="text-center">Expiry Date</th>
                                        <th class="text-center">Record Status</th>
                                        <th class="text-center">Minimum Kuantiti Pesanan</th>
                                        <th class="text-center">Price Change</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($unitPriceNotSame as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->item_name }} <h5>
                                                    <small>{{ $list->extension_code }}</small>
                                                </h5>
                                            </td>
                                            <td class="text-center">{{ $list->jenis_item }}</td>
                                            <td class="text-center">{{ $list->jenis_barang }}</td>
                                            <td class="text-center">{{ $list->eff_date }}</td>
                                            <td class="text-center">{{ $list->exp_date }}</td>
                                            <td class="text-center">{{ $list->status }}</td>
                                            <td class="text-center">{{ $list->minimum_order_qty }}</td>
                                            <td class="text-center">Yes</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        @if ($getContractDeduction != null || $DeductNotSame != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> DEDUCTION LIST</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <table id="deduction_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Name</th>
                                    <th class="text-center">Description</th>
                                    <th class="text-center">Clause Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractDeduction as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->code_name }}</td>
                                        <td class="text-center">{{ $list->deduction_desc }}</td>
                                        <td class="text-center">{{ $list->clause_description }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
                @if ($DeductNotSame != null)
                    <div class="row">
                        @if ($getVerId != null)
                            <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type
                                :
                                {{ $getVerId[0]->doc_type }}</label>
                        @endif
                        <div class="col-md-12">
                            <table id="deduction_list_current_datatable"
                                class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Name</th>
                                        <th class="text-center">Description</th>
                                        <th class="text-center">Clause Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($DeductNotSame as $list)
                                        <tr>
                                            <td class="text-center">{{ $list->code_name }}</td>
                                            <td class="text-center">{{ $list->deduction_desc }}</td>
                                            <td class="text-center">{{ $list->clause_description }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> BOND LIST</strong></h1>
            </div>
            <div class="row">
                @if ($contractDetails[0]->is_bond_waived === 'Yes')
                    <label class="col-md-12 text-left">Maka dengan ini kami, tertakluk kepada syarat-syarat yang telah
                        ditetapkan,di atas kontrak tersebut, telah meluluskan permohonan bertulis penepian yang diberikan
                        oleh GLOBAL ELECTROMECH (M) SDN. BHD., bagi bekalan barang yang perlu dibuat dengan segera atau
                        serta-merta apabila kelewatan itu akan memudaratkan dan menjejaskan perkhidmatan dan kepentingan
                        awam.
                    </label>
                @endif
                @if ($getContractBond != null)
                    <div class="col-md-12">
                        <table id="bond_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Ref No</th>
                                    <th class="text-center">Financial Org Name</th>
                                    <th class="text-center">Eff Date</th>
                                    <th class="text-center">Exp Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractBond as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->ref_no }}</td>
                                        <td class="text-center">{{ $list->financial_org_name }}</td>
                                        <td class="text-center">{{ $list->eff_date }}</td>
                                        <td class="text-center">{{ $list->exp_date }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
            @if ($BondNotSame != null)
                <div class="row">
                    @if ($getVerId != null)
                        <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type :
                            {{ $getVerId[0]->doc_type }}</label>
                    @endif
                    <div class="col-md-12">
                        <table id="bond_list_current_datatable"
                            class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Ref No</th>
                                    <th class="text-center">Financial Org Name</th>
                                    <th class="text-center">Eff Date</th>
                                    <th class="text-center">Exp Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($BondNotSame as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->ref_no }}</td>
                                        <td class="text-center">{{ $list->financial_org_name }}</td>
                                        <td class="text-center">{{ $list->eff_date }}</td>
                                        <td class="text-center">{{ $list->exp_date }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            @endif
        </div>

        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> NOTIFICATION LIST</strong></h1>
            </div>
            @if ($getContractNotification != null)
                <div class="row">
                    <div class="col-md-12">
                        <address>
                            <strong>Notifikasi Kontrak</strong> : <font color="red">
                                {{ $getContractNotification[0]->ct_notify_days }}
                            </font> hari sebelum kontrak tamat<br />
                            <strong>Notifikasi Perjanjian</strong> : <font color="red">
                                {{ $getContractNotification[0]->ag_notify_days_b4_ag }}
                            </font> hari sebelum perjanjian formal perlu dimuktamadkan sepenuhnya (4 bulan daripada tarikh
                            SST
                            disetuju terima)<br />
                            <strong>Notifikasi Bon Pelaksanaan</strong> : <font color="red">
                                {{ $getContractNotification[0]->bond_notify_days }}</font> hari sebelum tarikh tamat bon
                            pelaksanaan
                            <br />
                            <strong>Notifikasi Remedi</strong> : <font color="red">
                                {{ $getContractNotification[0]->termination_notify_days }}</font> hari sebelum tarikh
                            akhir
                            remedi
                            <br />
                            <strong>Penyediaan Pesanan Penghantaran oleh Pembekal</strong> : <font color="red">
                                {{ $getContractNotification[0]->supplier_do_days }}</font> hari sebelum tarikh mula
                            bekalan dan
                            perkhidmatan
                            <br />
                            <strong>Pesanan Kontrak Terakhir</strong> : <font color="red">
                                {{ $getContractNotification[0]->last_co_b4_exp_days }}</font> hari sebelum tarikh tamat
                            kontrak
                            <br />
                        </address>
                    </div>
                </div>
            @endif
            @if ($NotiNotSame != null)
                <div class="row">
                    @if ($getVerId != null)
                        <label class="col-md-12 text-left">contract ver : {{ $getVerId[0]->contract_ver_id }} | type :
                            {{ $getVerId[0]->doc_type }}</label>
                    @endif
                    <div class="col-md-12">
                        <address>
                            <strong>Notifikasi Kontrak</strong> : <font color="red">
                                {{ $NotiNotSame[0]->ct_notify_days }}
                            </font> hari sebelum kontrak tamat<br />
                            <strong>Notifikasi Perjanjian</strong> : <font color="red">
                                {{ $NotiNotSame[0]->ag_notify_days_b4_ag }}
                            </font> hari sebelum perjanjian formal perlu dimuktamadkan sepenuhnya (4 bulan daripada tarikh
                            SST
                            disetuju terima)<br />
                            <strong>Notifikasi Bon Pelaksanaan</strong> : <font color="red">
                                {{ $NotiNotSame[0]->bond_notify_days }}</font> hari sebelum tarikh tamat bon
                            pelaksanaan
                            <br />
                            <strong>Notifikasi Remedi</strong> : <font color="red">
                                {{ $NotiNotSame[0]->termination_notify_days }}</font> hari sebelum tarikh akhir
                            remedi
                            <br />
                            <strong>Penyediaan Pesanan Penghantaran oleh Pembekal</strong> : <font color="red">
                                {{ $NotiNotSame[0]->supplier_do_days }}</font> hari sebelum tarikh mula bekalan dan
                            perkhidmatan
                            <br />
                            <strong>Pesanan Kontrak Terakhir</strong> : <font color="red">
                                {{ $NotiNotSame[0]->last_co_b4_exp_days }}</font> hari sebelum tarikh tamat kontrak
                            <br />
                        </address>
                    </div>
                </div>
            @endif
        </div>

        @if ($getAmendmentWithSupp != null || $getAmendmentWithSuppCurrentVer != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> AMENDMENT WITH SUPPLEMENTAL</strong></h1>
                </div>
                @if ($getAmendmentWithSupp != null)
                    <div class="row">
                        <div class="col-md-4">
                            <address>
                                <strong>Extend Contract</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_contract_extend }} Month(s)
                                </font> <br />
                                <strong>Extend Period</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->extend_period }}
                                </font> <br />
                                <strong>Start Extend Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->extend_start_date }}
                                </font> <br />
                                <strong>Start End Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->extend_end_date }}
                                </font> <br />
                            </address>
                        </div>
                        <div class="col-md-4">
                            <address>
                                <strong>Item Price Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_item_price_change }} (Effected Item)
                                </font><br />
                                <strong>Item Quantity Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_quantity_change }}</font>
                                <br />
                                <strong>Value Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_value_change }}</font>
                                <br />
                                <strong>Clause Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_clause_change }}</font>
                                <br />
                                <strong>Other Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_other_change }} (Effected Agency or Item or Deduction)
                                </font>
                                <br />
                            </address>
                        </div>
                        <div class="col-md-4">
                            <address>
                                <strong>File Ref No</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->file_ref_no }}
                                </font> <br />
                                <strong>Mof Apporver</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_mof_authority_req }}
                                </font> <br />
                                <strong>PBCQ Approver</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_pbqc_authority_req }}
                                </font> <br />
                                <strong>Contract Approver</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->is_approver_authority_req }}
                                </font> <br />
                                <strong>Desk Officer</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->user_name }}
                                </font> <br />
                            </address>
                        </div>
                        <div class="col-md-12">
                            <address>
                                <strong>Amendment Effective Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->amendment_eff_date }}
                                </font> <br />
                                <strong>Title</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->title }}
                                </font> <br />
                                <strong>Description</strong> : <font color="black">
                                    {{ $getAmendmentWithSupp[0]->description }}
                                </font> <br />
                            </address>
                        </div>

                    </div>
                @endif
                @if ($getAmendmentWithSuppCurrentVer != null)
                    <div class="row">
                        <div class="col-md-4">
                            <address>
                                <strong>Extend Contract</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_contract_extend }}
                                </font> <br />
                                <strong>Extend Period</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->extend_period }} Month(s)
                                </font> <br />
                                <strong>Start Extend Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->extend_start_date }}
                                </font> <br />
                                <strong>Start End Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->extend_end_date }}
                                </font> <br />
                            </address>
                        </div>
                        <div class="col-md-4">
                            <address>
                                <strong>Item Price Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_item_price_change }} (Effected Item)
                                </font><br />
                                <strong>Item Quantity Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_quantity_change }}</font>
                                <br />
                                <strong>Value Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_value_change }}</font>
                                <br />
                                <strong>Clause Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_clause_change }}</font>
                                <br />
                                <strong>Other Change</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_other_change }} (Effected Agency or Item or
                                    Deduction)</font>
                                <br />
                            </address>
                        </div>
                        <div class="col-md-4">
                            <address>
                                <strong>File Ref No</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->file_ref_no }}
                                </font> <br />
                                <strong>Mof Apporver</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_mof_authority_req }}
                                </font> <br />
                                <strong>PBCQ Approver</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_pbqc_authority_req }}
                                </font> <br />
                                <strong>Contract Approver</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->is_approver_authority_req }}
                                </font> <br />
                                <strong>Desk Officer</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->user_name }}
                                </font> <br />
                            </address>
                        </div>
                        <div class="col-md-12">
                            <address>
                                <strong>Amendment Effective Date</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->amendment_eff_date }}
                                </font> <br />
                                <strong>Title</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->title }}
                                </font> <br />
                                <strong>Description</strong> : <font color="black">
                                    {{ $getAmendmentWithSuppCurrentVer[0]->description }}
                                </font> <br />
                            </address>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        @if ($getFactoringList != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i></h1>
                    <h1><strong> FACTORING LIST</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <table id="factoring_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Syarikat Pemfaktoran</th>
                                    <th class="text-center">No. Akaun / Nama Bank</th>
                                    <th class="text-center">No. Rujukan Surat</th>
                                    <th class="text-center">Tarikh Kuat Kuasa</th>
                                    <th class="text-center">Tarikh Tamat</th>
                                    <th class="text-center">Tarikh Tandatangan</th>
                                    <th class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getFactoringList as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->financial_org_name }}</td>
                                        <td class="text-center">{{ $list->bank_account_no }} / {{ $list->bank_name }}
                                        </td>
                                        <td class="text-center">{{ $list->auth_ref_no }}</td>
                                        <td class="text-center">{{ $list->eff_date }}</td>
                                        <td class="text-center">{{ $list->exp_date }}</td>
                                        <td class="text-center">{{ $list->agreement_date }}</td>
                                        <td class="text-center">{{ $list->record_status }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        
        @if ($getContractTerminate != null)
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> CONTRACT TERMINATION</strong></h1>
            </div>
            <div class="row">
                    <div class="col-md-12">
                        <table id="terminate_list_datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Keputusan Penamatan</th>
                                    <th class="text-center">File Ref No</th>
                                    <th class="text-center">Decision Date</th>
                                    <th class="text-center">Remedy Start Date</th>
                                    <th class="text-center">Remedy End Date</th>
                                    <th class="text-center">Clause</th>
                                    <th class="text-center">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($getContractTerminate as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->keputusan_penamatan }}</td>
                                        <td class="text-center">{{ $list->file_ref_no }}</td>
                                        <td class="text-center">{{ $list->decision_date }}</td>
                                        <td class="text-center">{{ $list->remedy_start_date }}</td>
                                        <td class="text-center">{{ $list->remedy_end_date }}</td>
                                        <td class="text-center">{{ $list->clause_no }}</td>
                                        <td class="text-center">{{ $list->description }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div> 
            </div>
        </div>
        @endif

    @endif

    


    <div id="modal-list-schedule-list" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">SCHEDULE NO
                            LIST</span></h2>

                </div>
                <div class="modal-body">
                    <div class="row">
                        @if ($getSchedule != null)
                            <div class="col-sm-12">
                                <table id="schedule_list_datatable"
                                    class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Schedule No</th>
                                            <th class="text-center">Doc No</th>
                                            <th class="text-center">From Date</th>
                                            <th class="text-center">To Date</th>
                                            <th class="text-center">Schedule Type</th>
                                            <th class="text-center">Record Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($getSchedule as $list)
                                            <tr>
                                                <td class="text-center">{{ $list->schedule_no }}</td>
                                                <td class="text-center">{{ $list->doc_no }}</td>
                                                <td class="text-center">{{ $list->from_date }}</td>
                                                <td class="text-center">{{ $list->to_date }}</td>
                                                <td class="text-center">{{ $list->schedule_type_id }}</td>
                                                <td class="text-center">{{ $list->record_status }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modal-list-kod-bidang-list" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">KOD BIDANG</span></h2>

                </div>
                <div class="modal-body">
                    <div class="row">
                        @if ($getKodBidangQt != null)
                            <div class="col-sm-12">
                                <table id="kod_bidang_list_datatable"
                                    class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">No</th>
                                            <th class="text-left">Category</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($getKodBidangQt as $key=>$list)
                                            <tr>
                                                <td class="text-center">{{ ++$key }}</td>
                                                <td class="text-left">Bidang : {{ $list->c1 }} - {{ $list->a1 }} <br />
                                                    Sub Bidang : {{ $list->c2 }} - {{ $list->a2 }} <br />
                                                    Pecahan Sub Bidang : {{ $list->c3 }} - {{ $list->a3 }} 
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modal-list-contract_ver-list" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">LOA PHYSICAL
                            LIST</span></h2>

                </div>
                <div class="modal-body">
                    <div class="row">
                        @if ($getContractVer != null)
                            <div class="col-sm-12">
                                <table id="contract_ver_list_datatable"
                                    class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Created Date</th>
                                            <th class="text-center">Contract Ver</th>
                                            <th class="text-center">LOA No</th>
                                            <th class="text-center">Loa Physical File</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($getContractVer as $list)
                                            <tr>
                                                <td class="text-center">{{ $list->created_date }}</td>
                                                <td class="text-center"><a class="modal-list-data-action"
                                                        href="{{ url('/find/contract/ver') }}/{{ $list->contract_ver_id }}"
                                                        target='_blank'>{{ $list->contract_ver_id }}</a></td>
                                                <td class="text-center">{{ $list->loa_no }}</td>
                                                <td class="text-center">{{ $list->loa_physical_no }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="modal-list-address-list" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Address
                            List
                        </span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <table class="agency_datatable table table-vcenter table-condensed table-bordered">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        App.datatables();
        var APP_URL = {!! json_encode(url('/')) !!}

        function initializeDataTable(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });
        }
        initializeDataTable('#schedule_list_datatable');
        initializeDataTable('#ca_list_datatable');
        initializeDataTable('#approver_datatable');
        initializeDataTable('#approver_select_datatable');
        initializeDataTable('#approver_select_change_datatable');
        initializeDataTable('#agency_list_datatable');
        initializeDataTable('#agency_list_current_datatable');
        initializeDataTable('#item_list_datatable');
        initializeDataTable('#item_list_current_datatable');
        initializeDataTable('#item_price_list_current_datatable');
        initializeDataTable('#deduction_list_datatable');
        initializeDataTable('#deduction_list_current_datatable');
        initializeDataTable('#bond_list_datatable');
        initializeDataTable('#bond_list_current_datatable');
        initializeDataTable('#factoring_list_datatable');
        initializeDataTable('#kod_bidang_list_datatable');
        initializeDataTable('#terminate_list_datatable');

        function initializeDataTable1(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "desc"]
                ],
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });
        }
        initializeDataTable1('#contract_ver_list_datatable');

        $(document).ready(function() {
            var dataTableInitialized = false;
            $('.agency_address').on("click", '.modal-list-data-action', function() {
                $('.spinner-loading').show();
                $('.agency_datatable').hide();
                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data);
                        $('.spinner-loading').hide();
                        if (dataTableInitialized) {
                            $('.agency_datatable').DataTable().destroy();
                        }
                        $('.agency_datatable').html(data).fadeIn();
                        initializeDataTable('.agency_datatable');

                        dataTableInitialized = true;
                    }
                });
            });
        });
    </script>
@endsection
