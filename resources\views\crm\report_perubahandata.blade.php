@extends('layouts.guest-dash')

@section('header')
<style type="text/css">  
    .dt-buttons {
        display: none;
    }
</style>
@endsection

@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">  
        <div class="block">
            <div class="block-title">
                <h2><strong>PEMANTAUAN PERUBAHAN DATA</strong></h2>  
            </div>
            <div class="row">
                <form action=" {{ url('/report/perubahandata') }}" method="post" class="form-horizontal form-bordered">
                    {{ csrf_field() }}
                    <div class="col-lg-12">
                        <div class="col-md-4">
                            <fieldset>
                                <div class="form-group">
                                    <label class="col-md-6 control-label" for="dateStart">Date Created From</label>
                                    <div class="col-md-6">
                                        <input type="date" id="dateStart" name="dateStart" class="form-control" value="{{ $dateStart }}">
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="col-md-4">
                            <fieldset>
                                <div class="form-group">
                                    <label class="col-md-6 control-label" for="dateEnd">Date Created To</label>
                                    <div class="col-md-6">
                                        <input type="date" id="dateEnd" name="dateEnd" class="form-control" value="{{ $dateEnd }}">
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                        <div class="col-md-4">
                            <fieldset>
                                <div class="form-group">
                                    <div class="col-md-6">
                                        <button type="submit" class="btn btn-sm btn-primary pull-right"> Search </button>
                                    </div>
                                </div>
                            </fieldset>
                        </div>
                    </div>
                </form>  
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="block">
                        <div class="card"> 
                            <div class="card-body">
                                <p>Pelaporan jumlah aduan berkenaan Perubahan Data</p>
                                <a href="#">
                                    <input type="button" style="background-color: black; color: white; padding: 5px" id="download" name="download" value="DOWNLOAD" />
                                </a>
                                <div class="table-responsive">
                                    <table class="table" id="cptpptable">
                                        <thead class="text-darken">
                                            <tr>	 
                                                <th class="text-left">Case No</th> 
                                                <th class="text-left">Redmine No</th>
                                                <th class="text-left">Kementerian</th>
                                                <th class="text-left" style="display: none;">Kumpulan PTJ / Jabatan</th>
                                                <th class="text-left" style="display: none;">PTJ / Organisasi</th>
                                                <th class="text-left" style="display: none;">PTJ Code</th>
                                                <th class="text-left" style="display: none;">Organisation Type</th>
                                                <th class="text-left" style="display: none;">Company Name</th>
                                                <th class="text-left" style="display: none;">MOF No</th> 
                                                <th class="text-left" style="display: none;">Leads Name</th> 
                                                <th class="text-left" style="display: none;">SSM No</th> 
                                                <th class="text-left">Customer Type</th>
                                                <th class="text-left" style="display: none;">Request Type</th>
                                                <th class="text-left">Incident / Service Type</th>
                                                <th class="text-left">Portal Category</th>
                                                <th class="text-left">Category</th>
                                                <th class="text-left">Sub-Category</th>
                                                <th class="text-left">Sub-Category2</th>
                                                <th class="text-left">Subject</th> 
                                                <th class="text-left" style="width: 40%;">Problem Details</th> 
                                                <th class="text-left" style="display: none;">Document Number</th> 
                                                <th class="text-left" style="display: none;">Resolution</th>
                                                <th class="text-left" style="display: none;">Priority</th>
                                                <th class="text-left">Created Date</th>
                                                <th class="text-left" style="display: none;">Requested By</th>
                                                <th class="text-left" style="display: none;">Owner Name</th>
                                                <th class="text-left" style="display: none;">Assigned To</th>
                                                <th class="text-left">Status</th> 
                                                <th class="text-left">Sub Status</th>  
                                                <th class="text-left" style="display: none;">Information</th>
                                                <th class="text-left">Date Modified</th>
                                                <th class="text-left" style="display: none;">Modified By</th>
                                                <th class="text-left">Contact Mode</th>
                                                <th class="text-left" style="display: none;">Ageing (Day)</th> 
                                                <th class="text-left" style="display: none;">Duration to Close (Day)</th>
                                                <th class="text-left" style="display: none;">e-mail</th>
                                                <th class="text-left" style="display: none;">State</th>
                                                <th class="text-left">Task Number</th>
                                                <th class="text-left">Task Status</th>
                                                <th class="text-left" >Task Assign Group</th>
                                                <th class="text-left" style="display: none;">GM Site</th>
                                                <th class="text-left">Cptpp Enquiry</th>
                                                <th class="text-left">Data Change Status</th>
                                                <th class="text-left" style="display: none;">Task Assigned To ePP1 (From Support)</th>
                                                <th class="text-left" style="display: none;">Task Completed By ePP1</th>
                                                <th class="text-left" style="display: none;">Task Assigned From ePP1 to ePA</th>
                                                <th class="text-left" style="display: none;">Task Name (Group ePA)</th>
                                                <th class="text-left" style="display: none;">Task Completed By ePA</th>
                                                <th class="text-left" style="display: none;">Task Completed By Support</th>
                                                <th class="text-left" style="display: none;">Ageing By ePP1 (days)</th>
                                                <th class="text-left" style="display: none;">Duration To Close By ePP1 (days)</th>
                                            </tr> 
                                        </thead>
                                        <tbody>
                                            @if($data != null)
                                            @foreach ($data as $indexKey => $row)
                                            @php
                                            $groups = App\Http\Controllers\CrmDashboard\ReportCrmController::getListDetailGroupCRM($row->user_id);
                                            $listGroupName = $groups->pluck('groupname');
                                            $assignGrp = implode(",",$listGroupName->toArray());
                                            $durationDayToClose = '';
                                            $ageingDay ='';
                                            $assignGrpTask = '';
                                            $epp1Duration = '';
                                            $epp1Ageing = '';
                                            
                                            if($row->task_assigned_to_epp1 !='' && $row->task_closed_by_epp1 !=''){
                                                $taskCreated = Carbon\Carbon::parse($row->task_assigned_to_epp1);
                                                $taskModified = Carbon\Carbon::parse($row->task_closed_by_epp1);
                                                $epp1Duration = $taskCreated->diffInDays($taskModified);
                                            }
                                            if($row->task_assigned_to_epp1 !='' && $row->task_closed_by_epp1 ==''){
                                                $taskCreated = Carbon\Carbon::parse($row->task_assigned_to_epp1);
                                                $epp1Ageing = $taskCreated->diffInDays(Carbon\Carbon::now());  
                                            }
                                            
                                            if ($row->status_code == 'Open_New' || $row->status_code == 'Open_Assigned' || $row->status_code == 'In_Progress' || $row->status_code == 'Open_Pending Input') {
                                            $dateCreated = Carbon\Carbon::parse($row->date_created);
                                            $ageingDay = $dateCreated->diffInDays(Carbon\Carbon::now()); 
                                            }
                                            if ($row->status_code == 'Closed_Closed' || $row->status_code == 'Closed_Rejected' || $row->status_code == 'Closed_Rejected_Eaduan' || $row->status_code == 'Closed_Verified_Eaduan' || $row->status_code == 'Closed_Duplicate' ||
                                            $row->status_code == 'Pending_User_Verification' || $row->status_code == 'Open_Resolved' || $row->status_code == 'Closed_Cancelled_Eaduan') {
                                            $dateCreated = Carbon\Carbon::parse($row->date_created);
                                            $dateModified = Carbon\Carbon::parse($row->date_modified);
                                            $durationDayToClose = $dateCreated->diffInDays($dateModified);
                                            }

                                            $tasks = App\Http\Controllers\CrmDashboard\ReportCrmController::getDetailTaskLatestCRM($row->id);
                                            if($tasks && $tasks->assigned_user_id !=''){
                                            $groupTask = App\Http\Controllers\CrmDashboard\ReportCrmController::getListDetailGroupCRM($tasks->assigned_user_id);
                                            $listTask = $groupTask->pluck('groupname');
                                            $assignGrpTask = implode(",",$listTask->toArray());
                                            }
                                            @endphp
                                            <tr> 
                                                <td class="text-left"> {{ $row->case_number }} </td>
                                                <td class="text-left"> {{ $row->redmine_number }} </td>
                                                <td class="text-left"> {{ $row->kementerian_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->jabatan_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->ptj_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->ptj_code }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->org_type }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->account_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->mof_no }} </td> 
                                                <td class="text-left" style="display: none;"> {{ $row->lead_name }}</td>
                                                <td class="text-left" style="display: none;"> {{ $row->ssm_no_c }} </td>
                                                <td class="text-left"> {{ $row->acc_type_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->req_type }} </td>
                                                <td class="text-left"> {{ $row->inc_type }} </td>
                                                <td class="text-left"> {{ $row->portal_cat }} </td>
                                                <td class="text-left"> {{ $row->cat_name }} </td>
                                                <td class="text-left"> {{ $row->subcat_name }} </td>
                                                <td class="text-left"> {{ $row->subcat2_name }} </td> 
                                                <td class="text-left"> {{ $row->csubject }} </td>
                                                <td class="text-left"> {{ $row->problem }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->doc_no }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->resolution }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->priority }} </td>
                                                <td class="text-left"> {{ $row->date_created }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->ctc_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->owner_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $assignGrp }}</td>
                                                <td class="text-left"> {{ $row->state_name }} </td>
                                                <td class="text-left"> {{ $row->status_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->case_info }} </td>
                                                <td class="text-left"> {{ $row->date_modified }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->modified_by }} </td>
                                                <td class="text-left"> {{ $row->ctcmode_name }} </td>
                                                <td class="text-left" style="display: none;"> {{ $ageingDay }} </td> 
                                                <td class="text-left" style="display: none;"> {{ $durationDayToClose }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->email }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->addr_state }} </td>
                                                <td class="text-left"> @if($tasks){{ $tasks->task_number_c }} @endif </td>
                                                <td class="text-left"> @if($tasks){{ $tasks->status }} @endif </td> 
                                                <td class="text-left"> @if($tasks){{ $assignGrpTask }} @endif </td> 
                                                <td class="text-left" style="display: none;"> @if($tasks){{ $tasks->gm_site_c }} @endif</td>
                                                <td class="text-left"> {{ $row->cptpp }} </td>
                                                <td class="text-left"> {{ $row->data_approve }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_assigned_to_epp1 }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_closed_by_epp1 }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_epp1_to_epa }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_epa }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_closed_by_epa }} </td>
                                                <td class="text-left" style="display: none;"> {{ $row->task_closed_by_support }} </td>
                                                <td class="text-left" style="display: none;"> {{ $epp1Ageing }} </td>
                                                <td class="text-left" style="display: none;"> {{ $epp1Duration }} </td>
                                            </tr>
                                            @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div> 
                </div>
            </div>
        </div> 
    </div> 
</div> 
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page --> 

<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();</script>  
<script>

    $('#page-container').removeAttr('class');
    $('#cptpptable').DataTable({
        dom: "Blfrtip",
        buttons: [
            {
                text: 'csv',
                extend: 'csvHtml5',
                exportOptions: {
                    //columns: ':visible:not(.not-export-col)'
                    columns: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20,
                        21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40,
                        41, 42, 43, 44, 45, 46, 47, 48, 49, 50]
                }
            },
            {
                text: 'excel',
                extend: 'excelHtml5',
                exportOptions: {
                    columns: ':visible:not(.not-export-col)'
                }
            }
        ],
        order: [[0, "asc"]],
        columnDefs: [],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']],
    });

    $('#download').on('click', function () {
        var table = $('#cptpptable').DataTable();
        table.button('.buttons-csv').trigger();
    });
</script>

@endsection