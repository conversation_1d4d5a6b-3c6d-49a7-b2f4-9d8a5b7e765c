<?php

namespace App\Http\Controllers\LogTrace;

use App\Http\Controllers\Controller;
use App\Services\Traits\LogTrace\TraceLogService;
use App\Services\Traits\Poms\PomsService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class TraceLogController extends Controller {

    use TraceLogService;
    use PomsService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        // $this->middleware('auth');
    }

    public function openDashboardMonitoring() {
        return view('logtrace.monitor', []);
    }

    public function openiDashboardMonitoring() {
        return view('logtrace.internal_monitor', []);
    }

    public function openPatchDashboard() {
        return view('logtrace.patch', []);
    }

    public function findDefaultLog() {
        $dt = Carbon::now();
        $defaultDt = $dt->format('Y-m-d');
        $dt2 = Carbon::create($dt->year, $dt->month, $dt->day, 0, 0, 1);
        $defaultTs = $dt2->format('H-i-s');
        return view('logtrace.activity', [
            'dt_from' => $defaultDt,
            'dt_to' => $defaultDt,
            'user_id' => '',
            'session_id' => '',
            'ts_from' => $defaultTs,
            'ts_to' => $dt->format('H-i-s'),
            'logs' => array()
        ]);
    }

    public function findLogByCriteria(Request $request) {
        return $this->getLogByCriteria($request);
    }

    public function findDefaultLoginHistory() {
        return view('logtrace.login_history', [
            'user_id' => '',
            'year_search' => '',
            'logs' => array()
        ]);
    }

    public function findLoginHistoryByCriteria(Request $request) {
        $res = $this->findLoginHistory($request->user_id,$request->year_search);
        $response = array(
            "recordsTotal" => $res->count(),
            "recordsFiltered" => $res->count(),
            "data" => $res
        );
        return json_encode($response);
    }

    public function findLogById(Request $request) {
        $log_id = request()->log_id;
        return $this->getLogById($log_id);
    }

    public function getTmpLogFileFailToProcess(Request $request) {
        return $this->monitorTmpLogFileFailToProcess();
    }

    public function grepTmpLogErrorMessage(Request $request) {
        return $this->getTmpLogErrorMessages();
    }

    public function emptyTmpLogErrorContent() {
        return $this->emptyLogErrorContent();
    }
    

    public function fixTmpLogFileFail(Request $request) {
        return $this->patchTmpLogFileFail($request->patch_dt, $request->patch_ts_fr, $request->patch_ts_to);
    }

    public function clearLogTrace(Request $request) {
        return $this->emptyLogTrace();
    }

    public function dailyAccessByUser(Request $request) {
        return $this->getDailyAccessByUser($request->filter_dt);
    }

    public function dailyRequestByEnvAndServerNode(Request $request){
        return $this->getDailyRequestByEnvAndServerNode($request->filter_dt);
    }

    public function dailyLogTraceGrpByFriendlyUrl(Request $request){
        return $this->getLogTraceGrpByFriendlyUrl($request->filter_dt);
    }

    public function shipLogToTmpLogFile(Request $request) {
        return $this->grepBacklogBySpecificDateTime($request);
    }

    public function runJob(Request $request){
        return $this->runJobByCriteria($request);
    }
}

