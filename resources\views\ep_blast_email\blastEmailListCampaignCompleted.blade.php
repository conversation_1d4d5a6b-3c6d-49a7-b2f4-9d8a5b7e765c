@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> List Campaign Completed</strong></h1>
        </div>
        <div class="table-responsive">
            <table id="search_list_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Scheduled Datetime</th>
                        <th class="text-center">Campaign Name</th>
                        <th class="text-center">Is Completed</th>
                        <th class="text-center">Total Email</th>
                    </tr>
                </thead>
                @if ($getListCampaignCompleted != null)
                    <tbody>
                        @foreach ($getListCampaignCompleted as $list)
                            <tr>
                                <td class="text-center">{{ $list->schedule_datetime }}</td>
                                <td class="text-center">{{ $list->campaign_name }}</td>
                                <td class="text-center">{{ $list->is_completed }}</td>
                                <td class="text-center">{{ $list->total_email }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                @endif
            </table>
        </div>

    </div>
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
                TablesDatatables.init();
            });
            App.datatables();
            $('#search_list_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
    </script>
@endsection
