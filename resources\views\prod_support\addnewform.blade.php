<div class="block-title panel-heading epss-title-s1 labelport">
    <h1><i class="fa fa-building-o"></i> <strong>Add New Porting</strong></h1>
</div>

<form class="form-horizontal form-bordered patch-form" id="patch-form" action="{{url('/prod-support/data-patching/create')}}" method="post">
    {{ csrf_field() }}
    <div class="form-group">
        <label class="col-md-1 text-left crmno1" for="crmno">CRM Number<span class="text-danger">*</span></label>
        <div class="col-md-3 carianform">
            <input type="number" id="crmno" name="crmno" value="" required class="form-control" style="width: 700px;">
        </div>
        <label class="col-md-1 text-left" for="dateport">Date Porting</label>
        @if($getOpenTimePatch)
        <div class="col-md-3">
            <input readonly="" id="dateport" name="dateport" type="text" class="form-control" value="{{($getOpenTimePatch->date)}}" style="width: 700px;">
        </div>
        @endif
        @if($getOpenTimePatch)
        <label class="col-md-1 text-left" for="seq">Porting Sequence</label>
        <div class="col-md-3">
            <input readonly="" id="seq" name="seq" type="text" value="{{$getOpenTimePatch->porting_seq}}" class="form-control" style="width: 700px;"><br />
        </div>
        @endif
    </div>

    <div class="form-group panel-heading add-new-port">
        <label class="col-md-1 text-left" for="portmodule">Module<span class="text-danger">*</span></label>
        <div class="col-md-3 type">
            <select id="portmodule" name = "portmodule" required class="form-control" style="width: 700px;">
                @foreach($getModule as  $key)
                <option value="{{$key->id}}">{{$key->name}}</option>
                @endforeach
            </select>
        </div>
        <label class="col-md-1 text-left" for="prodesc">Problem Description<span class="text-danger">*</span></label>
        <div class="col-md-3">
            <select id="prodesc" name="prodesc" required class="form-control" style="width: 700px;">
            </select>
        </div>
        <label class="col-md-1 text-left" for="probtype">Problem Type<span class="text-danger">*</span></label>
        <div class="col-md-3">
            <select id="probtype" name="probtype" required class="form-control" style="width: 700px;">
                @foreach($getProblemtype as $key)
                <option value="{{$key->id}}">{{$key->name}}</option>
                @endforeach
            </select>
        </div>
    </div>

    <div class="form-group">
        <label class="col-md-1 text-left" for="grouptype">Group Type<span class="text-danger">*</span></label>
        <div class="col-md-3 prob">
            <select id="grouptype" name ="grouptype" required class="form-control" style="width: 700px;">
                @foreach($getRequesterType as  $key)
                <option value="{{$key->id}}">{{$key->name}}</option>
                @endforeach
            </select>
        </div>

        <label class="col-md-1 text-left" for="orgname">Name<span class="text-danger">*</span></label>
        <div class="col-md-3">
            <input id="orgname" name="orgname" type="text" required class="form-control" style="width: 700px;">
        </div>

        <label class="col-md-1 text-left endorse1" for="endorse">Endorsed By<span class="text-danger">*</span></label>
        <div class="col-md-3">
            <select id="endorse" name="endorse" required class="form-control" style="width: 700px;" style="display:none"> 
                @foreach($usersEndorsed as $key)
                <option value="{{$key->full_name}}">{{$key->full_name}}</option>
                @endforeach
            </select>
        </div>
    </div>
    <div class="form-group">  
        <label class="col-md-1 text-left" ></label>
        <div class="col-md-3"></div>
        <label class="col-md-1 text-left" ></label>
        <div class="col-md-3"></div>
        <label class="col-md-1 text-left endorse1" for="endorse">Endorsement Date<span class="text-danger">*</span></label>
        <div class="col-md-3" id="date_endorse1">
            <input id = 'date_endorse' name="date_endorse" type="date" class="form-control" >
            <strong><label class="text-danger" id="reset_date">Click to RESET</label></strong>
        </div>
    </div>
    <div class="form-group">
        <div class="col-md-4">
            <label for="type-checkbox1"> 
                <input type="checkbox" id="checkbox" name="checkbox" value=""> Redmine
            </label>
        </div>
        <div class="col-md-4">
            <label for="type-checkbox2"> 
                <input type="checkbox" id="checkboxcase" name="checkboxcase" value=""> If many same case
            </label>
        </div>
    </div>
    <div class="form-group">
        <label class="col-md-1 redmine"  style="display:none" for="redmine">Redmine Number<span class="text-danger">*</span></label>
        <div class="col-md-3">
            <input type="number" id="redmineid" name="redmineid" value="" required class="form-control" style="width: 700px;">
        </div>
        <fieldset id="exampletags2" style="display:none">
            <label class="col-md-1 text-left exampletags1" for="grouptype">Case Number<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <input type="text" id="exampletags" name="exampletags" class="input-tags" value=""  class="form-control" style="width: 700px;">
            </div>
        </fieldset>
    </div>

    <div class="form-group">
        <label class="col-md-1 text-left" for="remarks">Remarks</label>
        <div class="col-md-12">
            <textarea type="text" id="remarks" name="remarks" rows="5" class="form-control" placeholder="Please insert remarks here if any"></textarea>
        </div>
    </div>

    <div class="form-group form-actions">
        <div class="pull-right">
            <button type = "submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i> Save</button>
            <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
        </div>
    </div>

</form>


