@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('/find/qt/suppexp')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="mof_no" name="mof_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Masukkan No MOF Pembekal & <PERSON><PERSON><PERSON> ...">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>SEMAKAN PENGALAMAN PEMBEKAL
        </h1>
    </div>
</div> 

@if($suppexp1 == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Maaf, Tiada Sebarang Rekod Pengalaman dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif

<!--Akmal Region SEMAKAN SUPPLIER experience  -->
@if($suppexp1)
<div class="block block-alt-noborder full">
    <div class="block">   
        <div class="block-title panel-heading epss-title-s1">
            <h5><i class="fa fa-building-o"></i><strong> PENGALAMAN DENGAN PERSEKUTUAN (KEMENTERIAN) :<font color="yellow"></font></strong></h5>
        </div>
        <div class="row">    
            
            <div class="col-md-12">
                 @if($suppexp3 != null)
                 <h6><i class="fa fa-building-o"></i><strong> NAMA SYARIKAT : <font color="red"> {{ $suppexp3->company_name}} </font> | <i class="fa fa-building-o"></i> SUPPLIER ID : <font color="red"> {{ $suppexp3->supplier_id}}</font>  |  <i class="fa fa-building-o"></i> MOF NO :<font color="red">  {{ $suppexp3->mof_no}} </font> </strong></h6>
                @endif
                
                
                <table id="summary-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center" class="col-md-4">FIRMA/PELANGGAN</th>
                            <th class="text-center">NILAI (RM)</th>
                            <th class="text-center">TARIKH</th>
                            <th class="text-center">STATUS</th> 
                            <th class="text-center">TAJUK</th>   
                        </tr>
                    </thead>
                    @if($suppexp1 != null)
                    @foreach ($suppexp1 as $key=>$spp)
                    <tr>
                        <td class="text-left">{{ $spp->client_name}}</td> 
                        <td class="text-right">{{ $spp->contract_value }}</td>
                        <td class="text-center">{{ strtoupper($spp->procurement_date) }}</td>
                        <td class="text-center">{{ strtoupper($spp->progress_status) }}</td>
                        <td class="text-left">
                            <span class="less{{ $key }}">{{ str_limit($spp->title, $limit = 22, $end = '...') }}</span>
                            <span class="details{{ $key }}" style="display:none">{{ $spp->title }}</span>
                            <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                    });">See More</a>
                        </td>
                    </tr>
                    @endforeach  
                    @endif
                </table>
            </div>
        </div>
    </div>
</div>
@endif

@if($suppexp2)
<div class="block block-alt-noborder full">
    <div class="block">   
        <div class="block-title panel-heading epss-title-s1">
            <h5><i class="fa fa-building-o"></i><strong> PENGALAMAN DENGAN BADAN BERKANUN (LAIN-LAIN):<font color="yellow"></font></strong></h5>
        </div>
        <div class="row">
            <div class="col-md-12">
                <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center" class="col-md-4">FIRMA/PELANGGAN</th>
                            <th class="text-center">NILAI (RM)</th>
                            <th class="text-center">TARIKH</th>
                            <th class="text-center">STATUS</th> 
                            <th class="text-center">TAJUK</th>   
                        </tr>
                    </thead>
                    @if($suppexp2 != null)
                    @foreach ($suppexp2 as $key=>$spp)
                    <tr>
                        <td class="text-left">{{ $spp->client_name}}</td> 
                        <td class="text-right">{{ $spp->contract_value }}</td>
                        <td class="text-center">{{ strtoupper($spp->procurement_date) }}</td>
                        <td class="text-center">{{ strtoupper($spp->progress_status) }}</td>
                        <td class="text-left">
                            <span class="less{{ $key }}">{{ str_limit($spp->title, $limit = 22, $end = '...') }}</span>
                            <span class="details{{ $key }}" style="display:none">{{ $spp->title }}</span>
                            <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                        $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                                    });">See More</a>
                        </td>
                    </tr>
                    @endforeach  
                    @endif
                </table>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                                                                                    TablesDatatables.init();
                                                                                    });</script>

@endsection