<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use DateTime;
use DateInterval;
use DatePeriod;
use Guzzle;
use GuzzleHttp\Client;
use stdClass;
use Excel;

class CheckUserActivityRep {

    public static function run() {
        ini_set('memory_limit', '-1');
        $preLog = self::class . ' Completed ' . __FUNCTION__ . ' >> ' ;
        MigrateUtils::logDump($preLog.'Starting ... ');
        $dtStartTime = Carbon::now();
        $collectAll = collect([]);
        $listLoginId = self::getListLoginId();
        $cl = collect($listLoginId);
        MigrateUtils::logDump($preLog.'Total LoginID to check: '.count($listLoginId));
        foreach($listLoginId as $loginId){
            $clInfo = self::checkByLoginId($loginId);
            $collectAll->push($clInfo);
            break;
        }

        //dump($collectAll);

        $collectObj = collect();
        foreach($collectAll as $obj){
            
            if($obj->get('userLoginInfo') && count($obj->get('userLoginInfo')) > 0 ){
                $objInfo  = new stdClass;
                    //dump('Login ID :'.$obj->get('userLoginInfo')[0]->login_id);
                $objInfo->login_id = $obj->get('userLoginInfo')[0]->login_id;
                //dump('Name :'.$obj->get('userLoginInfo')[0]->user_name);
                $objInfo->name = $obj->get('userLoginInfo')[0]->user_name;
                //dump('lastLoginDate :'.$obj->get('userLoginInfo')[0]->login_date);
                $objInfo->lastLoginDate = $obj->get('userLoginInfo')[0]->login_date;
                //dump('Role: '.$obj->get('userStatus')['result']['PmRole']);
                $objInfo->roles = $obj->get('userStatus')['result']['PmRole'];
                if($obj->get('userLoginInfo')[0]->org_type_id == 15){
                    //dump('Org Type : Supplier');
                    $objInfo->org_type = 'Supplier';
                    //dump('Org Name : '.$obj->get('supplierInfo')[0]->company_name);
                    $objInfo->org_name = $obj->get('supplierInfo')[0]->company_name;
                    //dump('Org Code : '.$obj->get('supplierInfo')[0]->ep_no);
                    $objInfo->org_code = $obj->get('supplierInfo')[0]->ep_no;
                    $objInfo->mof_no = $obj->get('supplierInfo')[0]->mof_no;
                }else{
                    //dump('Org Type : Government');
                    $objInfo->org_type = 'Government';
                    //dump('Org Name : '.$obj->get('govInfo')[0]->orgName);
                    //dump('Org Code : '.$obj->get('govInfo')[0]->orgCode);
                    if($obj->get('govInfo') && count($obj->get('govInfo')) > 0){
                        $objInfo->org_name = $obj->get('govInfo')[0]->org_name;
                        $objInfo->org_code = $obj->get('govInfo')[0]->org_code;
                        $objInfo->mof_no = null;
                    }else{
                        $objInfo->org_name = null;
                        $objInfo->org_code = null;
                        $objInfo->mof_no = null;
                    }
                    
                }
                if($obj->get('userTrackingDiary') && count($obj->get('userTrackingDiary')) > 0 ){
                    //dump('TD DocNo : '.$obj->get('userTrackingDiary')[0]->doc_no);
                    //dump('TD ActionDate : '.$obj->get('userTrackingDiary')[0]->actioned_date);
                    //dump('TD action_desc : '.$obj->get('userTrackingDiary')[0]->action_desc);
                    //dump('TD status_name : '.$obj->get('userTrackingDiary')[0]->status_name);
                    $objInfo->td_doc_no = $obj->get('userTrackingDiary')[0]->doc_no;
                    $objInfo->td_actioned_date = $obj->get('userTrackingDiary')[0]->actioned_date;
                    $objInfo->td_action_desc = $obj->get('userTrackingDiary')[0]->action_desc;
                    $objInfo->td_status_name = $obj->get('userTrackingDiary')[0]->status_name;
                }else{
                    $objInfo->td_doc_no = null;
                    $objInfo->td_actioned_date = null;
                    $objInfo->td_action_desc = null;
                    $objInfo->td_status_name = null;
                }
                if($obj->get('userLogActivityInfo') && count($obj->get('userLogActivityInfo')) > 0 ){
                    $listTrace = $obj->get('userLogActivityInfo');
                    foreach($listTrace as $trace){
                        $objInfoTemp  = new stdClass;
                        $objInfoTemp->login_id = $objInfo->login_id;
                        $objInfoTemp->name = $objInfo->name;
                        $objInfoTemp->lastLoginDate = $objInfo->lastLoginDate;
                        $objInfoTemp->roles = $objInfo->roles;
                        $objInfoTemp->org_type = $objInfo->org_type;
                        $objInfoTemp->org_name = $objInfo->org_name;
                        $objInfoTemp->org_code = $objInfo->org_code;
                        $objInfoTemp->mof_no = $objInfo->mof_no;
                        $objInfoTemp->td_doc_no = $objInfo->td_doc_no;
                        $objInfoTemp->td_actioned_date = $objInfo->td_actioned_date;
                        $objInfoTemp->td_action_desc = $objInfo->td_action_desc;
                        $objInfoTemp->td_status_name = $objInfo->td_status_name;
                
                        //dump('Trace Date : '.$trace->log_dt);
                        //dump('Trace Node : '.$trace->server_node);
                        //dump('Trace Method : '.$trace->request_method);
                        //dump('Trace Url : '.$trace->friendly_url);
                        $objInfoTemp->trc_log_date = $trace->log_dt;
                        $objInfoTemp->trc_node = $trace->server_node;
                        $objInfoTemp->trc_method = $trace->request_method;
                        $objInfoTemp->trc_url = $trace->friendly_url;
                        $collectObj->push($objInfoTemp);
                    }

                }else{
                    $objInfo->trc_log_date = null;
                    $objInfo->trc_node = null;
                    $objInfo->trc_method = null;
                    $objInfo->trc_url = null;
                    $collectObj->push($objInfo);
                }
            }else{
               dump('Tak jumpa');
               dump($obj); 
            }
        }
        dump(count($collectObj));
        self::generateExcel($collectObj);

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    

    public static function checkByLoginId($loginId){
        $preLog = self::class . ' Completed ' . __FUNCTION__ . ' >> ' ;
        //MigrateUtils::logDump($preLog.' Start check loginID : '.$loginId);
        $clInfo = collect([]);
        
        $userLoginInfo = self::getInfoUserLogin($loginId);
        $clInfo->put("userLoginInfo",$userLoginInfo);
        if($userLoginInfo){
            $statusUserLogin= self::wsStatusUserLogin($loginId);
            $clInfo->put("userStatus",$statusUserLogin);

            $userId = $userLoginInfo[0]->user_id;
            if($userLoginInfo[0]->org_type_id == 15){
                //MigrateUtils::logDump($preLog.' Detect as supplier : '.$loginId);
                $supplierInfo = self::getSupplierInfo($userId);
                $clInfo->put("supplierInfo",$supplierInfo);
            }else{
                //MigrateUtils::logDump($preLog.' Detect as Org Type ID : '.$userLoginInfo[0]->org_type_id);
                $govInfo = self::getGovInfo($userId);
                $clInfo->put("govInfo",$govInfo);
                
            }
            
            $userTrackingDiary = self::getTrackingDiary($userId);
            $clInfo->put("userTrackingDiary",$userTrackingDiary);

        }else{
            $clInfo->put("userStatus",null);
            $clInfo->put("userTrackingDiary",null);
            MigrateUtils::logDump($preLog.' User not found : '. $loginId);
        }

        $userLogActivityInfo = self::getLogActivityTrace($loginId);
        $clInfo->put("userLogActivityInfo",$userLogActivityInfo);

        //$userCaseInfo = self::getCaseInfo($loginId);
        //$clInfo->put("userCaseInfo",$userCaseInfo);

        return $clInfo;
    }

    protected static function getInfoUserLogin($loginId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT u.user_id,login_id,user_name,identification_no,
                email,h.LOGIN_DATE ,u.org_type_id, u.record_status 
                FROM pm_user u, pm_login_history  h WHERE u.user_id = h.user_id(+) 
                AND LOWER(login_id) = lower(?)
            ",array($loginId));
        return $results;
    }

    protected static function getSupplierInfo($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT s.COMPANY_NAME ,s.EP_NO , 
                p.ep_role, p.name,
                (select mof_no from sm_mof_account m where m.supplier_id = s.supplier_id and rownum<2) as mof_no 
                 FROM sm_personnel p , sm_supplier s
            WHERE 
                p.appl_id =s.LATEST_APPL_ID 
            AND p.user_id = ?
            ",array($userId));
        return $results;
    }

    protected static function getGovInfo($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT distinct uo.org_profile_id,uo.RECORD_STATUS AS userorg_record_status, 
                ov.org_name, ov.org_code, ov.exp_date , ov.record_status AS org_record_status 
                FROM pm_user_org uo , 
                pm_org_validity ov 
                WHERE uo.org_profile_id = ov.org_profile_id 
                AND uo.USER_ID = ? 
                -- AND uo.record_status = 1
                AND ov.EXP_DATE = (SELECT max(f.EXP_DATE) FROM pm_org_validity f  WHERE f.org_profile_id = uo.org_profile_id)
            ",array($userId));
        return $results;
    }

    protected static function getLogActivityTrace($loginId) {
        $results = DB::connection('pgsql_dynatrace')->select(
            "SELECT distinct
                to_char(log_dt, 'YYYY-MM-DD HH24:MI') as log_dt ,server_node, login_id,
                request_method,friendly_url, response_status
            from
                user_log ul
            where
                lower(ul.login_id) = lower(?)
                and log_dt >= to_date('2021-07-06', 'YYYY-MM-DD');        
            ",array($loginId));
        return $results;
    }

    protected static function getTrackingDiary($userId) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * 
            from (SELECT d.* , (select status_name from pm_status_desc  psd where psd.status_id = d.status_id and language_code = 'en' ) as status_name  
            FROM PM_TRACKING_DIARY d WHERE 
            actioned_by = ?
            AND to_char(actioned_date,'YYYY') = to_char(sysdate,'YYYY')
            ORDER BY actioned_date desc) tmp where rownum < 2        
            ",array($userId));
        return $results;
    }

    protected static function wsStatusUserLogin($loginId) {

        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

            $url = $urlMiddleware . "/ep/ejb-pm/userlogin-status?loginId=" . $loginId;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            return $resultResp;
        } catch (\GuzzleHttp\Exception\ClientException $ex) {
            return array(
                "status" => "Error",
                "result" => 'Failed to connect'+$ex->getMessage());
        }
    }

    protected static function getCaseInfo($loginId) {
        $results = DB::select(
            "SELECT 
			cases.`case_number`,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'request_type_list' AND value_code= cases_cstm.`request_type_c` LIMIT 0,1) AS case_request_type,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'category_list' AND value_code= cases_cstm.`category_c` LIMIT 0,1) AS case_category,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_sub_category_list' AND value_code= cases_cstm.`sub_category_c` LIMIT 0,1) AS case_sub_category,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_sub_category_2_list' AND value_code= cases_cstm.`sub_category_2_c` LIMIT 0,1) AS case_sub_sub_category,
			cases.`name` AS case_name,
			cases.`description` AS case_problem,
			cases.`doc_no` AS case_doc_no,
			cases.`resolution` AS case_resolution,
			u.`first_name` AS case_requested_by,
			(SELECT GROUP_CONCAT(email_address)   FROM email_addresses 
							INNER JOIN email_addr_bean_rel ON email_addresses.id = email_addr_bean_rel.email_address_id 
							WHERE email_addr_bean_rel.bean_module = 'Contacts' 
											AND email_addr_bean_rel.bean_id = cases_cstm.contact_id_c
											AND email_addr_bean_rel.deleted = 0 ) AS case_requested_by_email,
			YEAR(DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) ) AS case_year_created,		
			DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) AS case_created_date,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'case_status_dom' AND value_code= cases.`status` LIMIT 0,1) AS case_sub_status,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'case_state_dom' AND value_code= cases.`state` LIMIT 0,1) AS case_status,
			cases_cstm.`contact_mode_c` AS case_contact_mode_id,
			(SELECT value_name FROM cstm_list_app WHERE type_code = 'cdc_contact_mode_list' AND value_code= cases_cstm.`contact_mode_c` LIMIT 0,1) AS case_contact_mode
			FROM	cases 
			INNER JOIN cases_cstm ON cases.id = cases_cstm.id_c 
			INNER JOIN contacts u  ON u.id = cases_cstm.`contact_id_c`
			WHERE 
			YEAR(DATE(CONVERT_TZ(cases.date_entered,'+00:00','+08:00')) )  = YEAR(NOW())
				AND u.login_id_nextgen = ?        
            ",array($loginId));
        return $results;
    }

    protected static function generateExcel($list){
        $fileName = 'ReportUsersCheck';
        Excel::create($fileName, function($excel)use($list) {
            $excel->setTitle('List Users - Check Activity');

            $excel->sheet('List Users', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));

                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => 'yyyy-mm-dd h:mm',
                    'D' => '@',
                    'E' => '@',
                    'F' => '@',
                    'G' => '@',
                    'H' => '@',
                    'I' => '@',
                    'J' => '@',
                    'K' => '@',
                    'L' => '@',
                    'M' => '@',
                    'N' => 'yyyy-mm-dd h:mm',
                    'O' => '@',
                    'P' => '@',
                ));

                $sheet->row(1, array(
                    'LOGIN ID', 'NAME', 'LAST LOGIN DATE',
                    'ROLES', 'ORG TYPE' ,'ORG CODE','MOF NO','ORG NAME',
                    'TD DOC NO','TD ACTIONED DATE','TD STATUS NAME','TD ACTION DESC',
                    'TRC LOG DATE','TRC NODE','TRC METHOD','TRC URL',
                ));    
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:P1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });

                $count = 2;

                foreach ($list as $obj) {
                    $sheet->row($count, array(
                            $obj->login_id,
                            $obj->name,
                            $obj->lastLoginDate,
                            $obj->roles,
                            $obj->org_type,
                            $obj->org_code,
                            $obj->mof_no,
                            $obj->org_name,
                            $obj->td_doc_no,
                            $obj->td_actioned_date,
                            $obj->td_status_name,
                            $obj->td_action_desc,
                            $obj->trc_log_date,
                            $obj->trc_node,
                            $obj->trc_method,
                            $obj->trc_url,
                        )
                    );
                    $count++;
                }
            });         

        })->store('xlsx', storage_path('app/exports/check-users'));

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/exports/check-users/'.$fileName.'.xlsx');
    }

    /**
     * To Check Personnel Activation No Valid
     * @return list
     */
    protected static function getListLoginId() {
        return [
            'TMEH60285722',
            '921201025519',
            'KENCANAJAYAMASSDNBHD',
            '35701019342',
            'DINASURIA',
            '357-02211882-5867',
            'STAGNOTECH',
            'KLHEARTCARE',
            'talnoor123',
            'AXIONETSB',
            'transrapidholiday',
            'ZUFIQA2018',
            'EIRAZ1311',
            '357-02119407-5895',
            '860727525212',
            'TRUESUPREME86',
            'minzwsveck601ms',
            '840412085592',
            '830326025252',
            '740925025255',
            '357-02241426-5153',
            'asaruddin',
            '357-01005306-5667',
            'EYZOLL5995',
            'CONSTARSDNBHD',
            'kbair2019',
            '600903025399',
            'STEELRECON',
            'ELLYAF93',
            '357-02032385-5675',
            'afident2018',
            '357-00031399-5497',
            'SANCAHAYAMAJU',
            '357-02233980-5799',
            'Multitek',
            'geohydrocean777',
            '741230015469',
            '357-02246787-6244',
            'SKESB6543',
            'SENTUHAN6543',
            '357-02246787-6244',
            '870625025793',
            '35702191142',
            '357-02247393-5809',
            '860722387367',
            '760906146113',
            '710518105939',
            '870614065677',
            '890702145405',
            '841227115352',
            'SFS808PK',
            '357-02206547-5265',
            'Suria5998',
            'REBUNG2018',
            '920224015423',
            '770912055831',
            'infoinnerspace',
            'aammovers1',
            '35700044307',
            '357-02270337-5069',
            'superjayabateri',
            '357-02277086-5425',
            'niagasrz123',
            '770531115589'            
        ];
    }



}
