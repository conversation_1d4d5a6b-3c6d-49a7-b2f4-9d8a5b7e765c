<?php

namespace App\Services\Traits;

use DB;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use SSH;
use Guzzle;
use Response;
use Log;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait OSBService
{

    /*
     * Get Doc Po || Doc CO 
     */
    public function getDocNoPoCo($docPrCr)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT b.DOC_NO
                      FROM fl_fulfilment_request a, fl_fulfilment_order b
                     WHERE a.fulfilment_req_id = b.fulfilment_req_id AND a.doc_no = ? ",
            array($docPrCr)
        );
        return $results;
    }

    /**
     * 
     * @param type $docPrCr
     * @return type
     */
    public function getPoCoNo($docPrCr)
    {
        $poCoNoList = $this->getDocNoPoCo($docPrCr);
        if ($poCoNoList && count($poCoNoList) > 0) {
            return $poCoNo = $poCoNoList[0]->doc_no;
        }
        return null;
    }

    /**
     * Check status WS, receive Key Cert SPKI to eP
     * @param type $softcertRequestID
     * @param type $icNo
     * @return type
     */
    public function checkSuccessReceiveCertSPKI($softcertRequestID, $icNo)
    {
        $service_code = "SPK-010";
        $total = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_2', $icNo)
            ->where('service_code', $service_code)
            ->count();
        $total2 = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_2', $icNo)
            ->where('service_code', $service_code)
            ->count();
        return $total + $total2;
    }

    /**
     * Check status WS , successfull sent to SPKI
     * @param type $softcertRequestID
     * @param type $icNo
     * @return total
     */
    public function checkSuccessSentSPKI($softcertRequestID, $icNo)
    {
        $service_code = ['SPK-020', 'SPK-050', 'SPK-060', 'SPK-030'];
        $total = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $softcertRequestID)
            ->where('remarks_3', $icNo)
            ->whereIN('service_code', $service_code)
            ->count();

        $isNgepOSB = env("IS_NGEP_OSB", true);
        if ($isNgepOSB) {
            try {
                $total2 = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING')
                    ->where('remarks_1', $softcertRequestID)
                    ->where('remarks_3', $icNo)
                    ->whereIN('service_code', $service_code)
                    ->count();
                return $total + $total2;
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }

        return $total;
        
    }


    /**
     * get detial, sent/received Key Cert SPKI to eP / eP to SPKI
     * @param type $softcertRequestID
     * @param type $serviceCode
     * @return type
     */
    public function getDetailCertSPKI($softcertRequestID, $serviceCode)
    {
        $trans_type_OBRes = 'OBRes';
        $trans_type_IBReq = 'IBReq';

        /* GET TRANS ID */
        $res = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->where('a.remarks_1', $softcertRequestID)
            ->where('a.service_code', $serviceCode)
            ->select('a.trans_id')
            ->orderBy('a.trans_date', 'desc')
            ->first();
        if($res  == null){

            $isNgepOSB = env("IS_NGEP_OSB", true);
            if ($isNgepOSB) {
                try {
                    // Find in Schema NGEP_OSB
                    $res = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                    ->where('a.remarks_1', $softcertRequestID)
                    ->where('a.service_code', $serviceCode)
                    ->select('a.trans_id')
                    ->orderBy('a.trans_date', 'desc')
                    ->first();
                } catch (\Exception $e) {
                    Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
                }
            }
        }

        if ($res) {
            $objOBRes = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
                ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                ->where('a.trans_id', $res->trans_id)
                ->where('a.service_code', $serviceCode)
                ->where('a.trans_type', $trans_type_OBRes)
                ->select(
                    'a.trans_id',
                    'a.trans_type',
                    'a.service_code',
                    'a.trans_date',
                    'a.status_code',
                    'a.status',
                    'a.status_desc',
                    'a.remarks_1',
                    'a.remarks_2',
                    'a.remarks_3',
                    'b.payload_body'
                )
                ->orderBy('a.trans_date', 'desc')
                ->first();
            if($objOBRes == null){

                $isNgepOSB = env("IS_NGEP_OSB", true);
                if ($isNgepOSB) {
                    try {
                        // Find in Schema NGEP_OSB
                        $objOBRes = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                        ->join('NGEP_OSB.OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                        ->where('a.trans_id', $res->trans_id)
                        ->where('a.service_code', $serviceCode)
                        ->where('a.trans_type', $trans_type_OBRes)
                        ->select(
                            'a.trans_id',
                            'a.trans_type',
                            'a.service_code',
                            'a.trans_date',
                            'a.status_code',
                            'a.status',
                            'a.status_desc',
                            'a.remarks_1',
                            'a.remarks_2',
                            'a.remarks_3',
                            'b.payload_body'
                        )
                        ->orderBy('a.trans_date', 'desc')
                        ->first();
                    } catch (\Exception $e) {
                        Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
                    }
                }
                
            }

            $objIBReq = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
                ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                ->where('a.trans_id', $res->trans_id)
                ->where('a.trans_type', $trans_type_IBReq)
                ->where('a.service_code', $serviceCode)
                ->select(
                    'a.trans_id',
                    'a.trans_type',
                    'a.service_code',
                    'a.trans_date',
                    'a.status_code',
                    'a.status',
                    'a.status_desc',
                    'a.remarks_1',
                    'a.remarks_2',
                    'a.remarks_3',
                    'b.payload_body'
                )
                ->orderBy('a.trans_date', 'desc')
                ->first();
            if( $objIBReq == null ){

                $isNgepOSB = env("IS_NGEP_OSB", true);
                if ($isNgepOSB) {
                    try {
                        // Find in Schema NGEP_OSB
                        $objIBReq = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                        ->join('NGEP_OSB.OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
                        ->where('a.trans_id', $res->trans_id)
                        ->where('a.trans_type', $trans_type_IBReq)
                        ->where('a.service_code', $serviceCode)
                        ->select(
                            'a.trans_id',
                            'a.trans_type',
                            'a.service_code',
                            'a.trans_date',
                            'a.status_code',
                            'a.status',
                            'a.status_desc',
                            'a.remarks_1',
                            'a.remarks_2',
                            'a.remarks_3',
                            'b.payload_body'
                        )
                        ->orderBy('a.trans_date', 'desc')
                        ->first();
                    } catch (\Exception $e) {
                        Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
                    }
                }
                
            }
            if ($objOBRes) {
                $objOBRes->ibreq = $objIBReq;
                return $objOBRes;
            }
        }
        return null;
    }

    /**
     * Get response OSB , successfull sent file to 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getApive1GFMASResponse($filename)
    {
        $trans_type = 'Status-BATCH';
        $service_code = "GFM-010";
        //        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
        //                ->where('remarks_1',$filename)
        //                ->where('trans_type',$trans_type)
        //                ->where('service_code',$service_code)
        //                ->first();

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $resultBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING')
                    ->where('remarks_1', $filename)
                    ->where('trans_type', $trans_type)
                    ->where('service_code', $service_code)
                    ->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_1', $filename)
            ->where('trans_type', $trans_type)
            ->where('service_code', $service_code)
            ->get();

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        return $result;
    }

    /**
     * Get response OSB , successfull sent file to 1GFMAS
     * @param type $epNo
     * @return Object List Result 
     */
    public function getApive1GFMASResponseByEpNo($epNo)
    {
        $trans_type = 'Status-BATCH';
        $service_code = "GFM-010";
        //        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
        //                ->where('remarks_3',$epNo)
        //                ->where('trans_type',$trans_type)
        //                ->where('service_code',$service_code)
        //                ->get();

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $resultBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING')
                    ->where('remarks_3', $epNo)
                    ->where('trans_type', $trans_type)
                    ->where('service_code', $service_code)
                    ->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }
        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->where('remarks_3', $epNo)
            ->where('trans_type', $trans_type)
            ->where('service_code', $service_code)
            ->get();

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        return $result;
    }

    /**
     * Get contents APIVE 
     * @param type $transID
     * @return Object List Result 
     */
    public function getApiveContents1GFMAS($transID)
    {
        return DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
            ->where('trans_id', $transID)
            ->first();
    }

    /**
     * Get response 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getApove1GFMASResponseByApivefile($filename)
    {
        $service_code = "GFM-370";
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
            ->where('file_name', $filename)
            ->where('service_code', $service_code)
            ->first();
    }

    /**
     * Get response 1GFMAS
     * @param type $filename
     * @return Object Single Result 
     */
    public function getAperr1GFMASResponseByApivefile($filename)
    {
        $service_code = "GFM-090";
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
            ->where('file_name', $filename)
            ->where('service_code', $service_code)
            ->first();
    }

    /** Just replace name  APIVE to APOVE*/
    protected function getApoveFilename($filename)
    {
        return str_replace("APIVE", "APOVE", $this->getAPIVEFilenameOUTExtra($filename));
    }

    /** Just replace name  APIVE to APERR*/
    protected function getAperrFilename($filename)
    {
        return str_replace("APIVE", "APERR", $this->getAPIVEFilenameOUTExtra($filename));
    }
    protected function getAPIVEFilenameOUTExtra($filename)
    {


        $date = substr($filename, 15, 8);

        $tarikhExtraFile = Carbon::createFromFormat('Ymd', '20180415')->startOfDay();
        $tarikh = Carbon::createFromFormat('Ymd', $date)->startOfDay();
        $isGreater = $tarikh->gt($tarikhExtraFile);

        if ($isGreater == false) {
            $fileRemoveGPG = str_replace(".GPG", "", $filename);
            $seq = substr($fileRemoveGPG, 23);
            $year = substr($filename, 15, 4);
            $monthday = substr($filename, 19, 4);
            if ($seq > 999 && $seq <= 1998) {
                $seqNew = $seq - 999;
                $filename = '1000APIVE400001' . ($year - 8) . $monthday . $seqNew . '.GPG';
            }
            if ($seq > 1998) {
                $seqNew = $seq - 1998;
                $filename = '1000APIVE400001' . ($year - 16) . $monthday . $seqNew . '.GPG';
            }
        }
        return $filename;
    }

    /**
     * Search WebService eP to PHIS 
     */
    public function searchPHISWebService($search, $serviceCode)
    {
        //$trans_type = ['IBReq','IBRes'];
        $service_code = ['PHS-080', 'PHS-150', 'PHS-160', 'PHS-170', 'PHS-180', 'PHS-190', 'PHS-210', 'PHS-220'];

        if ($serviceCode != null) {
            $service_code = [$serviceCode];
        }

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $resultBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING')
                    ->whereIn('service_code', $service_code)
                    ->where(function ($query) use ($search) {
                        $query->orWhere('remarks_1', $search)
                            ->orWhere('remarks_2', $search)
                            ->orWhere('remarks_3', $search);
                    })
                    ->select('trans_id', 'trans_date')
                    ->distinct()
                    ->take(50)
                    ->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }
        $result = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING')
            ->whereIn('service_code', $service_code)
            ->where(function ($query) use ($search) {
                $query->orWhere('remarks_1', $search)
                    ->orWhere('remarks_2', $search)
                    ->orWhere('remarks_3', $search);
            })
            ->orderBy('trans_date', 'desc')
            ->select('trans_id', 'trans_date')
            ->distinct()
            ->take(50)
            ->get();

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        if (count($result) > 0) {
            return $result->pluck('trans_id')->unique();
        } else {
            return null;
        }
    }



    /**
     * get TransID by remarks on OSB_LOGGING 
     */
    public function getTransIdOSBLoggingByRemarks($search)
    {

        $checkSubStrTwo = substr($search, 0, 2);
        if ($checkSubStrTwo == 'PO' || $checkSubStrTwo == 'CO') {
            $resultAll = collect([]);
            $result1 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_1", "GFM-100", $search);
            foreach ($result1 as $obj) {
                $resultAll->push($obj);
            }
            $result2 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_1", "GFM-110", $search);
            foreach ($result2 as $obj) {
                $resultAll->push($obj);
            }
            $result3 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_1", "EPP-013", $search);
            foreach ($result3 as $obj) {
                $resultAll->push($obj);
            }
            $result4 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "GFM-080", $search);
            foreach ($result4 as $obj) {
                $resultAll->push($obj);
            }
            $result5 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "GFM-120", $search);
            foreach ($result5 as $obj) {
                $resultAll->push($obj);
            }
            $result6 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "GFM-170", $search);
            foreach ($result6 as $obj) {
                $resultAll->push($obj);
            }
            $result7 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "EPP-017", $search);
            foreach ($result7 as $obj) {
                $resultAll->push($obj);
            }
            return $resultAll;
        }

        if ($checkSubStrTwo == 'SD') {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_1", "GFM-170", $search);
        }

        if ($checkSubStrTwo == 'DN') {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "GFM-080", $search);
        }

        if ($checkSubStrTwo == 'FN') {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "GFM-110", $search);
        }

        //Search DO no.
        $checkSubStrThree = substr($search, 0, 3);
        if ($checkSubStrThree === '600' && strlen(trim($search)) == 17) {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "GFM-110", $search);
        }

        //If eP No. on Softcert
        if ($checkSubStrThree === 'eP-' && strlen(trim($search)) == 12) {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "SPK-010", $search);
        }

        //Search MMINF
        if (strlen(trim($search)) == 18) {
            return $result = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_1", "GFM-020", $search);
        }

        //If Ic No. on Softcert
        if (strlen(trim(intval($search))) == 12) {
            $resultAll = collect([]);
            $result1 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_2", "SPK-010", $search);
            foreach ($result1 as $obj) {
                $resultAll->push($obj);
            }
            $result2 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "SPK-020", $search);
            foreach ($result2 as $obj) {
                $resultAll->push($obj);
            }
            $result3 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "SPK-050", $search);
            foreach ($result3 as $obj) {
                $resultAll->push($obj);
            }
            $result4 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "SPK-060", $search);
            foreach ($result4 as $obj) {
                $resultAll->push($obj);
            }
            $result5 = $this->getTransIdOSBLoggingByServiceCodeAndRemarks("remarks_3", "SPK-030", $search);
            foreach ($result5 as $obj) {
                $resultAll->push($obj);
            }
            return $resultAll;
        }

        return array();
    }

    /**
     * Search in OSB_LOGGING & OSB_LOGGING
     * @param type $fieldRemark
     * @param type $serviceCode
     * @param type $search
     * @return type
     */
    public function getTransIdOSBLoggingByServiceCodeAndRemarks($fieldRemark, $serviceCode, $search)
    {
        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING');
                $queryBak->where($fieldRemark, $search);
                $queryBak->where('service_code', $serviceCode);
                $queryBak->select('trans_id', 'trans_date');
                $queryBak->distinct();
                $queryBak->orderByDesc('trans_date');
                $resultBak = $queryBak->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }


        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING');
        $query->where($fieldRemark, $search);
        $query->where('service_code', $serviceCode);
        $query->select('trans_id', 'trans_date');
        $query->distinct();
        $query->orderByDesc('trans_date');
        $result = $query->get();

        $isNgepOSB = env("IS_NGEP_OSB", true);
        $resultOsb = collect([]);
        if ($isNgepOSB) {
            try {
                // Find in Schema NGEP_OSB
                $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING');
                $queryOsb->where($fieldRemark, $search);
                $queryOsb->where('service_code', $serviceCode);
                $queryOsb->select('trans_id', 'trans_date');
                $queryOsb->distinct();
                $queryOsb->orderByDesc('trans_date');
                $resultOsb = $queryOsb->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }
        
        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }
        if ($resultOsb->count() > 0) {
            foreach ($resultOsb as $row) {
                $result->push($row);
            }
        }

        if (count($result) > 0) {
            return $result->pluck('trans_id')->unique()->take(1000);
        } else {
            return array();
        }
    }

    /**
     * Search in OSB_LOGGING & OSB_LOGGING ARCHIVE
     * @param type $fieldRemark
     * @param type $serviceCode
     * @param type $search
     * @return type
     */
    public function getFirstOsbLogDetail($fieldRemark, $serviceCode, $transType, $docNo)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING');
        $query->where('service_code', $serviceCode);
        $query->where($fieldRemark, $docNo);
        $query->where('trans_type', $transType);
        $query->orderBy('trans_date', 'desc');
        $objResult = $query->first();

        if (!$objResult) {

            $isNgepOSB = env("IS_NGEP_OSB", true);
            $objResultOsb = null;
            if ($isNgepOSB) {
                try {
                    // Find in Schema NGEP_OSB
                    $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING');
                    $queryOsb->where('service_code', $serviceCode);
                    $queryOsb->where($fieldRemark, $docNo);
                    $queryOsb->where('trans_type', $transType);
                    $quequeryOsbry->orderBy('trans_date', 'desc');
                    $objResultOsb = $queryOsb->first();
                } catch (\Exception $e) {
                    Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
                }
            }

            if (!$objResultOsb) {
                $isEpArchived = env("IS_EP_ARCHIVED", true);
                if ($isEpArchived) {
                    try {
                        $query = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING');
                        $query->where('service_code', $serviceCode);
                        $query->where($fieldRemark, $docNo);
                        $query->where('trans_type', $transType);
                        $query->orderBy('trans_date', 'desc');
                        return $query->first();
                    } catch (\Exception $e) {
                        Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
                    }
                }
            }else{
                return $objResultOsb; 
            }
        }
        return $objResult;
    }

    /**
     * Search in OSB_LOGGING_DTL & OSB_LOGGING_DTL ARCHIVE
     * @param type $osgLoggingId
     * @return object
     */
    public function getOsbLoggingPayload($osgLoggingId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING_DTL a')
                ->where('a.logging_id', $osgLoggingId);
        $objResult = $query->first();
        if($objResult )
            return $objResult ;

        $isNgepOSB = env("IS_NGEP_OSB", true);
        if ($isNgepOSB) {
            try {
                // Find in Schema NGEP_OSB
                $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING_DTL a')
                    ->where('a.logging_id', $osgLoggingId);
                $objResultOsb = $queryOsb->first();
                if($objResultOsb )
                    return $objResultOsb ;  
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }
          


        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $objResult = null;
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING_DTL q')
                    ->where('q.logging_id', $osgLoggingId);
                $objResult = $queryBak->first();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }
        if ($objResult == null) {
            $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING_DTL a')
                ->where('a.logging_id', $osgLoggingId);
            $objResult = $query->first();
        }

        return $objResult;
    }

    /**
     * get File Details by FileName
     */
    public function getBatchFileLog($fileName)
    {
        $obj = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
            ->where('FILE_NAME', $fileName)
            ->first();
        return $obj;
    }

     /**
     * get File Details by FileName
     */
    public function getBatchFileLogDetail($fileName,$serviceCode)
    {
        $obj = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
            ->where('SERVICE_CODE',$serviceCode)
            ->where('FILE_NAME', $fileName)
            ->first();
        return $obj;
    }

    /**
     * get File Details by FileName
     */
    public function getBatchFileById($batchFileId)
    {
        $obj = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
            ->where('batch_file_id', $batchFileId)
            ->first();
        return $obj;
    }

    /**
     * Get File is processed. 
     * @param type $filename
     * @return Object Single Result 
     */
    public function getDetailDiInterfaceLog($filename)
    {
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
            ->where('file_name', $filename)
            ->first();
    }


    public function getTransIDInterfaceLog($serviceCode)
    {
        return DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')
            ->where('service_code', $serviceCode)
            ->whereNull('file_name')
            ->take(100)
            ->get();
    }


    /**
     * Return Decrypt Content
     * @param type $content
     * @return type
     */
    public function wsDecryptContentIGFMAS($content)
    {
        $data = collect([]);
        // Check in BEGIN PGP MESSAGE
        if ($content == null or strlen(trim($content)) == 0) {
            $data->put("status", "failed");
            $data->put("content", "Content is EMPTY!");
            return $data;
        }
        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

        try {
            $responseUrl = Guzzle::request('POST', $urlMiddleware . "/decrypt-content", [
                'form_params' => [
                    'content' => $content
                ]
            ]);
            $response = Response::make($responseUrl->getBody(), 200);
            $data->put("status", "success");
            $data->put("content", $response->content());
            return $data;
        } catch (Exception $ex) {
            $data->put("status", "failed");
            $data->put("content", $ex->getMessage());
            return $data;
        }
    }

    /**
     * Return Decrypt Content
     * @param type $fileName
     * @return string
     */
    public function wsDecryptFileContentIGFMAS($fileName)
    {
        if ($fileName == null or strlen(trim($fileName)) == 0) {
            return null;
        }

        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

        $responseUrl = Guzzle::get($urlMiddleware . "/decrypt-file?filename=$fileName");

        $response = Response::make($responseUrl->getBody(), 200);
        $response->header('Content-Type', 'text/plain');
        return $response->content();
    }



    /**
     * Call WebService to decrypt content file from 1GFMAS
     */
    public function wsDecryptFile1GFMAS($objFile)
    {
        $target = '1GFMAS'; //PHIS //EP
        $result = collect([]);
        $dataFile = trim($objFile->file_data);
        $xmlContents = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:pgp1='http://www.ep.gov.my/Schema/1-0/PGP' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>
            <x:Header/>
            <x:Body>
                <pgp1:EPMFRq>
                    <epm:RqHeader>
                    </epm:RqHeader>
                    <pgp1:PGPRq>
                        <pgp1:Mode>D</pgp1:Mode>
                        <pgp1:Target>$target</pgp1:Target>
                        <pgp1:Input>$dataFile</pgp1:Input>
                    </pgp1:PGPRq>
                </pgp1:EPMFRq>
            </x:Body>
            </x:Envelope>";
        $xmlContents = '"' . $xmlContents . '"';
        $url = "http://192.168.63.205:7011/Common/Utilities/PGPSO";
        $urlHeader = "'Content-Type: application/xml'";
        $commands = [
            "curl -k " . $url . " --header  " . $urlHeader . "  -d " . $xmlContents,
        ];

        //dump($commands);
        SSH::into('osb')->run($commands, function ($line) use (&$result) {
            $data = $line . PHP_EOL;
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);

            if (count($vals) > 0) {
                foreach ($vals as $val) {
                    if ($val["tag"] == 'X:BODY') {
                        //dump($val["value"]);
                        $result->push($val["value"]);
                    }
                }
            }
        });
        if (count($result) == 0) {
            $result->push('Failed to decrypt data  ' . $objFile->file_name);
        }
        return $result;
    }


    public function getListOSB1GFMASWebServiceDetails($search)
    {
        $listTransId = $this->getTransIdOSBLoggingByRemarks($search);
        $list = array();
        if ($listTransId != null) {
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return $list;
    }

    public function getListOSBPHISWebServiceDetails($search, $serviceCode = null)
    {
        $listTransId = $this->searchPHISWebService($search, $serviceCode);
        $list = array();
        if ($listTransId != null) {
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return $list;
    }

    /**
     * Get list of details OSB Logging for Type Request  and Type Response 
     * @param type $listTransId
     * @return type
     */
    public function searchOSBLogDetailsAllByTransId($listTransId)
    {

        return DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
            ->whereIn('a.trans_id', $listTransId)
            ->select(
                'a.trans_id',
                'a.trans_type',
                'a.service_code',
                'a.trans_date',
                'a.status_code',
                'a.status',
                'a.status_desc',
                'a.remarks_1',
                'a.remarks_2',
                'a.remarks_3',
                'b.payload_body'
            )
            ->orderBy('a.trans_date', 'desc')
            ->get();
    }


    /**
     * Get list of details OSB Logging for Type Request  and Type Response 
     * Query OSB_LOGGING & OSB_LOGGING
     * @param type $listTransId
     * @return type
     */
    public function searchOSBLogDetailsReqResByTransId($listTransId)
    {
        //$trans_type = ['IBRes','OBRes','OBReq','OBReq-DEC','OBRes-ENC','IBReq','IBReq-ENC','IBReq-DEC','OBRes-QUEUE'];
        $trans_type = ['IBRes', 'OBRes', 'IBReq', 'IBReq-DEC', 'IBRes-DEC', 'OBRes-FAULT'];
        $trans_id = $listTransId;

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING q')
                    ->whereIn('q.trans_id', $trans_id)
                    ->whereIn('q.trans_type', $trans_type)
                    ->select(
                        'q.logging_id',
                        'q.trans_id',
                        'q.trans_type',
                        'q.service_code',
                        'q.trans_date',
                        'q.status_code',
                        'q.status',
                        'q.status_desc',
                        'q.remarks_1',
                        'q.remarks_2',
                        'q.remarks_3'
                    );
                $resultBak = $queryBak->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->whereIn('a.trans_id', $trans_id)
            ->whereIn('a.trans_type', $trans_type)
            ->select(
                'a.logging_id',
                'a.trans_id',
                'a.trans_type',
                'a.service_code',
                'a.trans_date',
                'a.status_code',
                'a.status',
                'a.status_desc',
                'a.remarks_1',
                'a.remarks_2',
                'a.remarks_3'
            );
        $result = $query->get();

        $isNgepOSB = env("IS_NGEP_OSB", true);
        $resultOsb = collect([]);
        if ($isNgepOSB) {
            try {
                // Find in Schema NGEP_OSB
                $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                    ->whereIn('a.trans_id', $trans_id)
                    ->whereIn('a.trans_type', $trans_type)
                    ->select(
                        'a.logging_id',
                        'a.trans_id',
                        'a.trans_type',
                        'a.service_code',
                        'a.trans_date',
                        'a.status_code',
                        'a.status',
                        'a.status_desc',
                        'a.remarks_1',
                        'a.remarks_2',
                        'a.remarks_3'
                    );
                $resultOsb = $queryOsb->get(); 
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }
        if ($resultOsb->count() > 0) {
            foreach ($resultOsb as $row) {
                $result->push($row);
            }
        }

        return $result;
    }

    //left join OSB_SERVICE c on a.service_code = c.service_code
    public function searchOSBLogDetailsByTransId($transId)
    {

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING x')
                    ->where('x.trans_id', $transId)
                    ->select(
                        'x.logging_id',
                        'x.trans_id',
                        'x.trans_type',
                        'x.service_code',
                        'x.trans_type',
                        'x.service_flow',
                        'x.trans_date',
                        'x.status_code',
                        'x.status',
                        'x.status_desc',
                        'x.rq_uid',
                        'x.async_rq_uid',
                        'x.remarks_1',
                        'x.remarks_2',
                        'x.remarks_3'
                    );
                $resultBak = $queryBak->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->where('a.trans_id', $transId)
            ->select(
                'a.logging_id',
                'a.trans_id',
                'a.trans_type',
                'a.service_code',
                'a.trans_type',
                'a.service_flow',
                'a.trans_date',
                'a.status_code',
                'a.status',
                'a.status_desc',
                'a.rq_uid',
                'a.async_rq_uid',
                'a.remarks_1',
                'a.remarks_2',
                'a.remarks_3'
            );
        $result = $query->get();

        $isNgepOSB = env("IS_NGEP_OSB", true);
        $resultOsb = collect([]);
        if ($isNgepOSB) {
            try {
                // Find in Schema NGEP_OSB
                $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                    ->where('a.trans_id', $transId)
                    ->select(
                        'a.logging_id',
                        'a.trans_id',
                        'a.trans_type',
                        'a.service_code',
                        'a.trans_type',
                        'a.service_flow',
                        'a.trans_date',
                        'a.status_code',
                        'a.status',
                        'a.status_desc',
                        'a.rq_uid',
                        'a.async_rq_uid',
                        'a.remarks_1',
                        'a.remarks_2',
                        'a.remarks_3'
                    );
                $resultOsb = $queryOsb->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        if ($resultOsb->count() > 0) {
            foreach ($resultOsb as $row) {
                $result->push($row);
            }
        }

        return $result;
    }

    public function searchOSBLogDetailsByRqUid($rqUId)
    {
        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultBak = collect([]);
        if ($isEpArchived) {
            try {
                $queryBak = DB::connection('oracle_nextgen_arc')->table('OSB_LOGGING x')
                    ->where('x.rq_uid', $rqUId)
                    ->select(
                        'x.logging_id',
                        'x.trans_id',
                        'x.trans_type',
                        'x.service_code',
                        'x.trans_type',
                        'x.service_flow',
                        'x.trans_date',
                        'x.status_code',
                        'x.status',
                        'x.status_desc',
                        'x.rq_uid',
                        'x.async_rq_uid',
                        'x.remarks_1',
                        'x.remarks_2',
                        'x.remarks_3'
                    );
                $resultBak = $queryBak->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING a')
            ->where('a.rq_uid', $rqUId)
            ->select(
                'a.logging_id',
                'a.trans_id',
                'a.trans_type',
                'a.service_code',
                'a.trans_type',
                'a.service_flow',
                'a.trans_date',
                'a.status_code',
                'a.status',
                'a.status_desc',
                'a.rq_uid',
                'a.async_rq_uid',
                'a.remarks_1',
                'a.remarks_2',
                'a.remarks_3'
            );
        $result = $query->get();

        $isNgepOSB = env("IS_NGEP_OSB", true);
        $resultOsb = collect([]);
        if ($isNgepOSB) {
            try {
                // Find in Schema NGEP_OSB
                $queryOsb = DB::connection('oracle_nextgen_rpt')->table('NGEP_OSB.OSB_LOGGING a')
                    ->where('a.rq_uid', $rqUId)
                    ->select(
                        'a.logging_id',
                        'a.trans_id',
                        'a.trans_type',
                        'a.service_code',
                        'a.trans_type',
                        'a.service_flow',
                        'a.trans_date',
                        'a.status_code',
                        'a.status',
                        'a.status_desc',
                        'a.rq_uid',
                        'a.async_rq_uid',
                        'a.remarks_1',
                        'a.remarks_2',
                        'a.remarks_3'
                    );
                $resultOsb = $queryOsb->get();
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : schema  NGEP_OSB >> ' . $e->getMessage());
            }
        }

        if ($resultBak->count() > 0) {
            foreach ($resultBak as $row) {
                $result->push($row);
            }
        }

        if ($resultOsb->count() > 0) {
            foreach ($resultOsb as $row) {
                $result->push($row);
            }
        }
        return $result;
    }



    /**
     * Get list for dashboard (Error Validation Exception Transaction Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsValidationException()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,COUNT(*) AS TOTAL FROM OSB_LOGGING WHERE 
                STATUS_DESC LIKE 'Consumer%'  
                AND STATUS='F' 
                AND  TRANS_DATE >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS') 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD')"
        );

        return $results;
    }

    /**
     * Get list for dashboard (Error Item Code GFM-100 Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsItemCodeInGFM100()
    {
        $tarikh = Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,
                    SUBSTR(A.STATUS_DESC, 0, 11) as ERROR_TYPE, 
                    COUNT(*) as TOTAL FROM OSB_LOGGING A 
                WHERE 
                  A.TRANS_ID IN (
                    SELECT DISTINCT TRANS_ID FROM ( 
                      SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                          FROM OSB_LOGGING
                          WHERE TRANS_TYPE = 'IBReq'
                          AND TRANS_ID IN (
                             SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                             WHERE 
                             SERVICE_CODE = 'GFM-100' 
                             AND 
                             ( STATUS_DESC LIKE 'Kod barang%' OR   STATUS_DESC LIKE 'Unit ukuran%' )
                             AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                          )
                        ) WHERE RANK = 1
                  )
                  AND  A.TRANS_TYPE = 'IBRes' 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD'),SUBSTR(A.STATUS_DESC, 0, 11) 
                ORDER BY TRANS_DATE DESC "
        );

        return $results;
    }

    /**
     * Get list for dashboard (Error Item Code GFM-020 MMINF Web Service) -- Last 3 days
     * @return list
     */
    protected function getDashboardStatisticWsItemCodeInMMINF()
    {
        $tarikh = Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  TO_CHAR(TRANS_DATE,'YYYY-MM-DD') AS TRANS_DATE,
                    SUBSTR(A.STATUS_DESC, 0, 8) as ERROR_TYPE, 
                    COUNT(*) as TOTAL FROM OSB_LOGGING A 
                WHERE 
                  A.TRANS_ID IN (
                    SELECT DISTINCT TRANS_ID FROM ( 
                      SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                          FROM OSB_LOGGING
                          WHERE TRANS_TYPE = 'IBReq'
                          AND TRANS_ID IN (
                             SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                             WHERE 
                             SERVICE_CODE = 'GFM-020' 
                             AND 
                             ( STATUS_DESC LIKE '%wujud di 1GFMAS%' OR   STATUS_DESC LIKE '%MOHON CUBA SEBENTAR LAGI SEHINGGA BERJAYA'  )
                             AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS')
                          )
                        ) WHERE RANK = 1
                  )
                  AND  A.TRANS_TYPE = 'IBRes' 
                GROUP BY TO_CHAR(TRANS_DATE,'YYYY-MM-DD'),SUBSTR(A.STATUS_DESC, 0, 8) 
                ORDER BY TRANS_DATE DESC "
        );

        return $results;
    }

    /**
     * Get list transactions (Error Validation Exception Transaction Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsValidationException()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A WHERE 
                  A.STATUS_DESC LIKE 'Consumer%'  
                  AND A.STATUS='F' 
                  AND  TRANS_DATE >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')) 
                  ORDER BY  A.TRANS_ID DESC "
        );

        return $results;
    }

    /**
     * Get list transactions (Error No EJB Receiver in Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsErrNoEJBReceiver()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (
                    SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A,OSB_LOGGING_DTL B  WHERE 
                    A.LOGGING_ID =B.LOGGING_ID 
                    AND A.STATUS_DESC = 'Service Not Found: OSB Service Callout action received SOAP Fault response' 
                    AND extractvalue(xmltype.createxml(B.PAYLOAD_BODY),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%EJBCLIENT000025%' 
                    AND A.TRANS_DATE  >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
                    
                )
                ORDER BY A.TRANS_ID DESC 
                "
        );

        return $results;
    }

    /**
     * Get list transactions (Error  EJB Time Out in Web Service) -- Last 3 days
     * @return list
     */
    protected function getListWsErrEJBTimeOut()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY FROM OSB_LOGGING A,OSB_LOGGING_DTL B WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND A.TRANS_ID IN (
                    SELECT DISTINCT A.TRANS_ID FROM OSB_LOGGING A,OSB_LOGGING_DTL B  WHERE 
                    A.LOGGING_ID =B.LOGGING_ID 
                    AND A.STATUS_DESC = 'Service Not Found: OSB Service Callout action received SOAP Fault response' 
                    AND extractvalue(xmltype.createxml(B.PAYLOAD_BODY),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%java.util.concurrent.TimeoutException: No invocation response received%' 
                    AND A.TRANS_DATE  >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
                    
                )
                ORDER BY A.TRANS_ID DESC 
                "
        );

        return $results;
    }

    /**
     * Get list transactions (Error  Item Code in Web Service GFM-100) -- Last 3 days
     * @return list
     */
    protected function getListWsErrItemCodeInGFM100()
    {
        $tarikh = Carbon::now()->subDays(1)->format('Y-m-d');
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                WHERE 
                A.LOGGING_ID =B.LOGGING_ID  
                AND
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-100' 
                           AND 
                           ( STATUS_DESC LIKE 'Kod barang%%' OR   STATUS_DESC LIKE 'Unit ukuran%'  )
                           AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 

                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE
                "
        );

        return $results;
    }

    /**
     * Get list Files GFM-140)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListFilesGFM140ByDateRange($dateStart, $dateEnd)
    {
        // sample date : 2018-04-19 00:00:00 

        $dtStartTime = Carbon::now();

        $query = "SELECT  * 
                    FROM OSB_BATCH_FILE A 
                    WHERE A.SERVICE_CODE = 'GFM-140' 
                    AND A.CREATED_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                    AND A.CREATED_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                ";
        dump($query);

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        $logsdata = self::class . ' Query Date Start : ' . $dateStart . ' , Query Date End : ' . $dateEnd . ' , Completed --- ' .
            ' , Total: ' . count($results) . ' Taken Time : '
            . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump($logsdata);
        return $results;
    }

    /**
     * Get list Files GFM-140 or etc
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListFilesBatchFileByDate($serviceCode, $date)
    {
        // sample date : 2018-04-19 00:00:00 
        $query = "SELECT  * 
                    FROM OSB_BATCH_FILE A 
                    WHERE A.SERVICE_CODE = ?  
                    AND TO_CHAR(A.CREATED_DATE,'YYYY-MM-DD') = ? 
                ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($serviceCode, $date));

        return $results;
    }

    /**
     * Get list Files generated 
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListGeneratedFileNameByDate($serviceCode, $date)
    {
        // sample date : 2018-04-19
        $query = "SELECT  * 
                    FROM DI_INTERFACE_LOG A 
                    WHERE A.SERVICE_CODE = ?  
                    AND TO_CHAR(A.CREATED_DATE,'YYYY-MM-DD') = ? 
                ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($serviceCode, $date));

        return $results;
    }

    /**
     * Searching DocNo LA in GFM-350 (AP516)
     * @param type $docNo
     * @return Object LIST
     */
    protected function findDocNoInAP516File($docNo)
    {
        $searchData = "utl_raw.cast_to_raw ('$docNo')";
        $query = "select * from osb_batch_file where service_code = 'GFM-350' and
                    dbms_lob.instr (
                            file_data, -- the blob
                            " . $searchData . ", -- the search string cast to raw
                            1, -- where to start. i.e. offset
                            1 -- Which occurrance i.e. 1=first
                        ) > 0 
                ";
        return DB::connection('oracle_nextgen_rpt')->select($query);
    }


    /**
     * Get list transactions (Error  Item Code in Web Service GFM-100)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListWsErrItemCodeInGFM100ByDateRange($dateStart, $dateEnd)
    {
        // sample date : 2018-04-19 00:00:00 

        $dtStartTime = Carbon::now();

        $query = "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3
                FROM OSB_LOGGING A
                WHERE 
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-100' 
                           AND 
                           ( STATUS_DESC LIKE 'Kod barang%%' OR   STATUS_DESC LIKE 'Unit ukuran%'  )
                           AND TRANS_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                           AND TRANS_DATE <= TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE
                ";
        dump($query);

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        $logsdata = self::class . ' Query Date Start : ' . $dateStart . ' , Query Date End : ' . $dateEnd . ' , Completed --- ' .
            ' , Total: ' . count($results) . ' Taken Time : '
            . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump($logsdata);
        return $results;
    }

    /**
     * Get list transactions (Error  Bank Pembekal tidak wujud di 1GFMAS in Web Service GFM-120)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListWsErrBankPembekalTidakWujudInGFM120ByDateRange($dateStart, $dateEnd)
    {
        // sample date : 2018-04-19 00:00:00 

        $dtStartTime = Carbon::now();

        $query = "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3
                FROM OSB_LOGGING A
                WHERE 
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-120' 
                           AND 
                            STATUS_DESC ='Bank Pembekal tidak wujud di 1GFMAS' 
                           AND TRANS_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                           AND TRANS_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE 
                ";
        //dump($query);

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        $logsdata = self::class . ' Query Date Start : ' . $dateStart . ' , Query Date End : ' . $dateEnd . ' , Completed --- ' .
            ' , Total: ' . count($results) . ' Taken Time : '
            . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        //dump($logsdata);
        return $results;
    }


    /**
     * Get list transactions (Error  Invalid Agency - Process Configuration in Web Service GFM-020)
     * sample date : 2018-04-19 00:00:00 
     * @return list
     */
    protected function getListWsErrGFM020ByDateRange($dateStart, $dateEnd)
    {
        // sample date : 2018-04-19 00:00:00 

        $dtStartTime = Carbon::now();

        $query = "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3
                FROM OSB_LOGGING A
                WHERE 
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-020' 
                           AND
                           ( 
                                STATUS_DESC ='Invalid Agency - Process Configuration' OR
                                STATUS_DESC = 'Generic Error - Service Provider Is Unable To Process Your Request: OSB Service Callout action received SOAP Fault response' OR 
                                STATUS_DESC ='Required parameters missing when calling up module T001k_SINGLE_READ' OR 
                                STATUS_DESC ='Generic Error - Service Provider Is Unable To Process Your Request: Error accessing the JNDI context for the EJB call: javax.security.auth.login.FailedLoginException: [Security:090304]Authentication F' 
                           )
                           AND TRANS_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS') 
                           AND TRANS_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS') 
                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              ORDER BY A.TRANS_ID , A.TRANS_TYPE 
                ";
        dump($query);

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        $logsdata = self::class . ' Query Date Start : ' . $dateStart . ' , Query Date End : ' . $dateEnd . ' , Completed --- ' .
            ' , Total: ' . count($results) . ' Taken Time : '
            . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump($logsdata);
        return $results;
    }

    /**
     * Get list transactions (Error  Item Code in Web Service GFM-020 MMINF) -- Last 3 days
     * @return list
     */
    protected function getListWsErrItemCodeInMMINF()
    {
        $tarikh = Carbon::now()->subDays(1)->format('Y-m-d');

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                    FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                    WHERE 
                    A.LOGGING_ID =B.LOGGING_ID  
                    AND
                    A.TRANS_ID IN (
                      SELECT DISTINCT TRANS_ID FROM ( 
                        SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                            FROM OSB_LOGGING
                            WHERE TRANS_TYPE = 'IBReq'
                            AND TRANS_ID IN (
                               SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                               WHERE 
                               SERVICE_CODE = 'GFM-020' 
                               AND 
                               ( STATUS_DESC LIKE '%wujud di 1GFMAS%' OR   STATUS_DESC LIKE '%MOHON CUBA SEBENTAR LAGI SEHINGGA BERJAYA'  )
                               AND TRANS_DATE >= TO_DATE('$tarikh 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 
                                   
                            )
                          ) WHERE RANK = 1
                    )
                    AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
                  ORDER BY A.TRANS_ID , A.TRANS_TYPE 
                "
        );


        return $results;
    }

    protected function getListStatisticIGFMASIntegration()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select("
        SELECT TRANS_TYPE,SERVICE_CODE ,
        (SELECT service_name FROM osb_service s WHERE s.SERVICE_CODE = o.SERVICE_CODE) AS service_name,
        STATUS_DESC,
        min(trans_date) AS start_date, max(trans_date) AS latest_date, 
        count(*) AS total_req
        FROM OSB_LOGGING o WHERE service_code IN ('GFM-080','GFM-100','GFM-110','GFM-120','GFM-170') 
            AND status = 'F'
            AND trans_type = 'IBRes-DEC' 
            AND trans_date BETWEEN trunc(sysdate) AND trunc(sysdate+1) 
        GROUP BY TRANS_TYPE,SERVICE_CODE,STATUS_DESC 
        ORDER BY total_req desc
    ");
        return $results;
    }

    protected function getListStatisticIGFMASIntegrationDetails($statusDesc)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select("
        SELECT TRANS_DATE,TRANS_ID ,TRANS_TYPE,SERVICE_CODE,REMARKS_1,REMARKS_2,REMARKS_3 
        FROM OSB_LOGGING
        WHERE 
        trans_id IN (
                SELECT TRANS_ID   FROM OSB_LOGGING WHERE service_code IN ('GFM-100','GFM-110','GFM-120') 
             AND status = 'F'
             AND trans_type = 'IBRes-DEC' 
             AND trans_date BETWEEN trunc(sysdate) AND trunc(sysdate+1) 
             AND STATUS_DESC = :statusDesc
         ) 
        AND trans_type = 'IBReq' 
        ORDER BY trans_date ", ['statusDesc' => $statusDesc]);

        return $result;
    }

    protected function getListErrorEmptyResponseOSB()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT trunc(trans_date) AS req_date,service_code,
            (SELECT service_name FROM osb_service s WHERE s.service_code = o.service_code ) AS service_name,
            min(trans_date) AS start_date, 
            max(trans_date) AS last_date,
            count(DISTINCT trans_id) AS total_request, 
            status_desc
            FROM osb_logging o 
            WHERE trunc(trans_date) = trunc(sysdate) 
            AND trans_type = 'OBRes-FAULT'
            AND STATUS_CODE = '70003'
            GROUP BY trunc(trans_date), service_code,status_desc"
        );

        return $results;
    }

    protected function getListRecordsOSBErrorEmptyResponse($code)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT trans_id, trans_type, service_code, trans_date, status_code, status, status_desc, remarks_1, remarks_2, remarks_3 FROM osb_logging o
            WHERE trunc(trans_date) = trunc(sysdate)
            AND trans_type = 'OBRes-FAULT'
            AND STATUS_CODE = '70003'
            AND SERVICE_CODE = '$code'"
        );

        return $result;
    }



    /**
     * Get list Statistic (Error  FIle Processing) -- Last 3 days
     * APERR, AR902, 1000GL
     * @return list
     */
    protected function getListStatisticFileErrProcessing()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')  AS TRANS_DATE ,A.SERVICE_CODE,B.SERVICE_NAME , COUNT(*) AS TOTAL
                from OSB_BATCH_FILE A, OSB_SERVICE B where A.SERVICE_CODE = B.SERVICE_CODE(+)
                AND A.CREATED_DATE >= TO_DATE(TO_CHAR(SYSDATE-2, 'YYYY-MM-DD') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
                AND (
                    FILE_NAME LIKE '%AR902%'
                    OR FILE_NAME LIKE '%GLMDE%'
                    OR FILE_NAME LIKE '%APERR%'
                  )
                 GROUP BY TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD'),A.SERVICE_CODE,B.SERVICE_NAME
                 ORDER BY TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD') DESC 
                "
        );

        return $results;
    }

    /**
     * Get list transactions (Error  FIle Processing) -- Last 3 days
     * APERR, AR902, 1000GL
     * @return list
     */
    protected function getListFileErrProcessing($serviceCode, $transDate)
    {
        /*
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT A.TRANS_DATE , A.SERVICE_CODE,B.SERVICE_NAME,A.TRANS_TYPE, A.REMARKS_1  AS FILE_NAME  
                FROM OSB_LOGGING A, OSB_SERVICE B 
                WHERE 
                  A.SERVICE_CODE = B.SERVICE_CODE  (+) 
                  AND A.SERVICE_CODE = ? 
                  AND TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') = ? 
                  AND A.TRANS_TYPE = 'Status-BATCH'
                AND (
                  REMARKS_1 LIKE '%AR902%' 
                  OR REMARKS_1 LIKE '1000GLMDE%' 
                  OR REMARKS_1 LIKE '1000APERR400001%' 
                )
                ORDER BY TO_CHAR(A.TRANS_DATE, 'YYYY-MM-DD') DESC
                ", array($serviceCode,$transDate));
        */
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select * from OSB_BATCH_FILE where SERVICE_CODE = ?  
                    AND TO_CHAR(CREATED_DATE, 'YYYY-MM-DD') = ?  
                    AND (
                        FILE_NAME LIKE '%AR902%'
                        OR FILE_NAME LIKE '%GLMDE%'
                        OR FILE_NAME LIKE '%APERR%'
                      )
                     ORDER BY TO_CHAR(CREATED_DATE, 'YYYY-MM-DD') DESC ",
            array($serviceCode, $transDate)
        );
        return $results;
    }



    /**
     * Get list transactions Return Error 
     * @param type $dataSearch ( service_code,date_from,date_to)
     * @return type
     */
    protected function getListWsTransactionErrorBySearch($dataSearch)
    {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1,A.REMARKS_2,A.REMARKS_3, B.PAYLOAD_BODY 
                    FROM OSB_LOGGING A,OSB_LOGGING_DTL B 
                    WHERE 
                    A.LOGGING_ID =B.LOGGING_ID  
                    AND
                    A.TRANS_ID IN (
                      SELECT DISTINCT TRANS_ID FROM ( 
                        SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                            FROM OSB_LOGGING
                            WHERE TRANS_TYPE = 'IBReq'
                            AND TRANS_ID IN (
                               SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING A 
                               WHERE 
                                A.SERVICE_CODE = ?
                                AND A.TRANS_DATE >= TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS') 
                                AND A.TRANS_DATE < TO_DATE(?, 'YYYY-MM-DD HH24:MI:SS') 
                                AND A.STATUS =  'F' 
                                AND ( A.TRANS_TYPE = 'IBRes' or  A.TRANS_TYPE = 'OBRes-QUEUE' )
                            ) 
                          ) WHERE RANK = 1
                    )
                    AND  A.TRANS_TYPE IN ('IBReq','IBRes','IBReq-DEC','IBRes-DEC','OBRes-FAULT','OBRes-QUEUE') 
                ORDER BY A.TRANS_DATE desc,A.TRANS_ID
                ",
            array(
                $dataSearch->get('service_code'),
                $dataSearch->get('date_start'),
                $dataSearch->get('date_end')
            )
        );

        return $results;
    }


    /**
     * Get list transactions (Error  FIle Processing) -- Last 3 days
     * APERR, AR902, 1000GL
     * @return list
     */
    protected function getListOSBBatchFile($serviceCode, $dateStart, $dateEnd)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   SELECT *
                FROM OSB_BATCH_FILE
                WHERE CREATED_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS')
                      AND CREATED_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS')
                      AND SERVICE_CODE = ?  
                ",
            array($serviceCode)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLSEG
     * @return list
     */
    protected function getListFilesPendingGLSEG($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_PP a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLPTJ
     * @return list
     */
    protected function getListFilesPendingGLPTJ($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_PTJ a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : CMBNK
     * @return list
     */
    protected function getListFilesPendingCMBNK($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_BANK a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLPRJ
     * @return list
     */
    protected function getListFilesPendingGLPRJ($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_PROJECT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLVOT
     * @return list
     */
    protected function getListFilesPendingGLVOT($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_VOT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLPRG
     * @return list
     */
    protected function getListFilesPendingGLPRG($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_PRG_ACT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLGLC
     * @return list
     */
    protected function getListFilesPendingGLGLC($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_GL_ACCOUNT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLPCG
     * @return list
     */
    protected function getListFilesPendingGLPCG($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_KPTJ a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLBAC
     * @return list
     */
    protected function getListFilesPendingGLBAC($processID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_AG_OFFICE a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.PROCESS_ID = ?
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                ",
            array($processID)
        );

        return $results;
    }

    /**
     * Get list transactions (FIle Processing) -- process di : GLDNA
     * @return list
     */
    protected function getListFilesPendingGLDNA()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, count(*) as total_data 
                    from DI_IM_DANA a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    group by b.file_name,trunc(b.created_date), b.PROCESS_ID
                    order by trunc(b.created_date) asc 
                "
        );

        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLSEG
     * @return list
     */
    protected function getListDataPendingGLSEG($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_PP a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLPTJ
     * @return list
     */
    protected function getListDataPendingGLPTJ($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_PTJ a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : CMBNK
     * @return list
     */
    protected function getListDataPendingCMBNK($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_BANK a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLPRJ
     * @return list
     */
    protected function getListDataPendingGLPRJ($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_PROJECT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLVOT
     * @return list
     */
    protected function getListDataPendingGLVOT($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_VOT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLPRG
     * @return list
     */
    protected function getListDataPendingGLPRG($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_PRG_ACT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLGLC
     * @return list
     */
    protected function getListDataPendingGLGLC($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_GL_ACCOUNT a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLPCG
     * @return list
     */
    protected function getListDataPendingGLPCG($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_KPTJ a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLBAC
     * @return list
     */
    protected function getListDataPendingGLBAC($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_AG_OFFICE a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    /**
     * Get list transactions (Data Processing) -- process di : GLDNA
     * @return list
     */
    protected function getListDataPendingGLDNA($fileName)
    {

        $modifiedFilename = substr($fileName, 0, 26) . '%';
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select b.file_name, b.PROCESS_ID,trunc(b.created_date) as created_date, a.* 
                    from DI_IM_DANA a, di_interface_log b
                    where a.interface_log_id = b.interface_log_id
                    and a.process_status = 0
                    and b.FILE_NAME like '$modifiedFilename'
                    order by trunc(b.created_date) asc"
        );
        return $results;
    }

    protected function getTransIdOSBLoggingByDocNo($docNo)
    {

        return DB::connection('oracle_nextgen_rpt')
            ->table('OSB_LOGGING')
            ->where('REMARKS_2', $docNo)
            ->orWhere('REMARKS_1', $docNo)
            ->select('TRANS_ID as trans_id')
            ->orderBy('TRANS_DATE', 'desc')
            ->first();
    }

    protected function getIbResPayment($transid)
    {

        return DB::connection('oracle_nextgen_rpt')
            ->table('OSB_LOGGING as a')
            ->join('OSB_LOGGING_DTL as b', 'b.logging_id', '=', 'a.logging_id')
            ->where('a.TRANS_TYPE', 'IBRes-DEC')
            ->where('a.SERVICE_CODE', 'GFM-120')
            ->where('a.STATUS', 'S')
            ->where('a.TRANS_ID', $transid)
            ->select('a.TRANS_TYPE')
            ->orderBy('a.TRANS_DATE', 'desc')
            ->first();
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : UpdateSoftCert
     * @return list
     */
    protected function getDetailsOfUpdateSoftCert($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id,
                    a.*,b.* 
                    from  osb_retry_dtl a, osb_logging b  
                    where a.trans_id = b.trans_id 
                    and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'SPK-010' 
                    and b.service_code not in 'EPP-600'  
                    order by b.trans_date desc 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : requestCertificate
     * @return list
     */
    protected function getDetailsOfRequestCertificate($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select 
                    a.retry_dtl_id as id, 
                    a.trans_id,a.service_name,a.target_system,a.retry_count,b.service_code,a.created_date,
                    b.trans_type, b.trans_date, b.remarks_1, b.remarks_2, b.remarks_3
                    from  osb_retry_dtl a, osb_logging b  
                    where a.trans_id = b.trans_id 
                    and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'JmsIns'
                    and b.service_code in ('SPK-020','SPK-050','SPK-060','SPK-030') 
                    order by b.trans_date desc 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $transID : UpdateSoftCert
     * @return list
     */
    protected function searchOSBLogPayloadByTransId($transID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "  select *
                    from  OSB_RETRY_DTL  
                    where TRANS_ID like ?
                ",
            array($transID)
        );

        return $results;
    }

    protected function getDetailsOSBLogByRetryLog($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id,a.trans_id,a.service_name,a.target_system,a.retry_count,b.service_code,a.created_date,
                    b.trans_type, b.trans_date, b.remarks_1, b.remarks_2, b.remarks_3 
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                     and b.TRANS_TYPE = 'JmsIns' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : ContractStopInstruction
     * @return list
     */
    protected function getDetailsOfContractStopInstruction($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.* 
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'PHS-180' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : PHISMasterDataMaterialInformation
     * @return list
     */
    protected function getDetailsOfPHISMasterDataMaterialInformation($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.*  
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'PHS-220' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : ContractOrderInformation
     * @return list
     */
    protected function getDetailsOfContractOrderInformation($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.*  
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'PHS-160' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : ContractPaymentInstruction
     * @return list
     */
    protected function getDetailsOfContractPaymentInstruction($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.*  
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'GFM-120' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : ContractFulfillmentReceivedNote
     * @return list
     */
    protected function getDetailsOfContractFulfillmentReceivedNote($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.*  
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'PHS-170' 
                ",
            array($serviceName)
        );

        return $results;
    }

    /**
     * Get list transactions (OSB Service Retry) -- $serviceName : ContractFulfillmentReceivedNote
     * @return list
     */
    protected function getDetailsOfPHISMasterDataDeliveryAddress($serviceName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select a.retry_dtl_id as id, a.*,b.*  
                    from  osb_retry_dtl a, osb_logging b
                    where a.trans_id = b.trans_id 
                     and a.SERVICE_NAME = ?
                    and b.TRANS_TYPE = 'IBReq' 
                    and b.service_code = 'PHS-080' 
                ",
            array($serviceName)
        );

        return $results;
    }


    /**
     * Get list statistic MyGPIS on ETL data completed extrac
     * @return list
     */
    protected function getListStatisticTotalETLMyGPIS()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select 'MYGPIS_SYARIKAT' as MODUL , 'GPI-010' as service_code, TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_SYARIKAT
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_BARANG' as MODUL , 'GPI-020' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_BARANG
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_KUMP_PTJ' as MODUL , 'GPI-030' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_KUMP_PTJ
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_PTJ' as MODUL , 'GPI-040' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_PTJ
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_BIDANG' as MODUL , 'GPI-050' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_BIDANG
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_IKLAN' as MODUL , 'GPI-060' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_IKLAN
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_SST' as MODUL , 'GPI-070' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_SST
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_KONTRAK' as MODUL , 'GPI-080' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_KONTRAK
                GROUP BY  1,2,3
              UNION
              select 'MYGPIS_PEMENUHAN' as MODUL , 'GPI-090' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_PEMENUHAN
                GROUP BY  1,2,3
              UNION
              select 'MYGPIS_PPEROLEHAN' as MODUL , 'GPI-100' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_PPEROLEHAN
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_PPERBELANJAAN' as MODUL , 'GPI-110' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_PPERBELANJAAN
                GROUP BY 1,2,3
              UNION
              select 'MYGPIS_PRESTASI' as MODUL , 'GPI-120' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM MYGPIS_PRESTASI
                GROUP BY 1,2,3
                    
                "
        );

        return $results;
    }

    /**
     * Get list statistic MyGPIS on creation file today
     * @return list
     */
    protected function getListStatisticTotalFileCreationBySchedulerMyGPISToday()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   /** Checking generate file **/
                        SELECT
                          TO_CHAR(d.created_date, 'YYYY-MM-DD') as created_date,
                          d.service_code,
                          DECODE(d.service_code, 'GPI-010', 'MYGPIS_SYARIKAT',
                                 'GPI-020', 'MYGPIS_BARANG',
                                 'GPI-030', 'MYGPIS_KUMP_PTJ',
                                 'GPI-040', 'MYGPIS_PTJ',
                                 'GPI-050', 'MYGPIS_BIDANG',
                                 'GPI-060', 'MYGPIS_IKLAN',
                                 'GPI-070', 'MYGPIS_SST',
                                 'GPI-080', 'MYGPIS_KONTRAK',
                                 'GPI-090', 'MYGPIS_PEMENUHAN',
                                 'GPI-100', 'MYGPIS_PPEROLEHAN',
                                 'GPI-110', 'MYGPIS_PPERBELANJAAN',
                                 'GPI-120', 'MYGPIS_PRESTASI',
                                 'N/A')                                    AS modul,
                          count(*)                                         AS total_created,
                          (SELECT count(*)
                           FROM osb_logging
                           WHERE service_code = d.service_code
                                 AND trans_type = 'Status-BATCH'
                                 AND SERVICE_FLOW = 'JMS' AND status = 'S'
                                 AND trunc(created_date) = trunc(sysdate)) AS total_transferred
                        FROM DI_INTERFACE_LOG d
                        WHERE d.service_code IN (
                          'GPI-010', 'GPI-020', 'GPI-030', 'GPI-040',
                          'GPI-050', 'GPI-060', 'GPI-070', 'GPI-080',
                          'GPI-090', 'GPI-100','GPI-110','GPI-120'
                        )
                              AND trunc(d.created_date) = trunc(sysdate)
                        GROUP BY TO_CHAR(d.created_date, 'YYYY-MM-DD'), d.service_code
                        ORDER BY service_code
                "
        );

        return $results;
    }

    /**
     * Get list statistic MyGPIS on file succesfully transferred to MyGPIS
     * @return list
     */
    protected function getListStatisticTotalFileMyGPISTodaySuccessfullyTransferred()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "  select TO_CHAR(created_date,'YYYY-MM-DD')  as created_date,
                    service_code ,
                    DECODE (service_code, 'GPI-010', 'MYGPIS_SYARIKAT',
                                          'GPI-020', 'MYGPIS_BARANG',
                                          'GPI-030', 'MYGPIS_KUMP_PTJ',
                                          'GPI-040', 'MYGPIS_PTJ',
                                          'GPI-050', 'MYGPIS_BIDANG',
                                          'GPI-060', 'MYGPIS_IKLAN',
                                          'GPI-070', 'MYGPIS_SST',
                                          'GPI-080', 'MYGPIS_KONTRAK',
                                          'GPI-090', 'MYGPIS_PEMENUHAN',
                                          'GPI-100', 'MYGPIS_PPEROLEHAN',
                                          'GPI-110', 'MYGPIS_PPERBELANJAAN',
                                          'GPI-120', 'MYGPIS_PRESTASI',
                                                  'N/A') as modul,
                    count(*) as total  from osb_logging where service_code in (
                    'GPI-010','GPI-020','GPI-030','GPI-040',
                    'GPI-050','GPI-060','GPI-070','GPI-080',
                    'GPI-090','GPI-100','GPI-110','GPI-120'
                  )
                  AND trans_type = 'Status-BATCH'
                  AND SERVICE_FLOW = 'JMS'   and status = 'S'
                  AND trunc(created_date) = trunc(sysdate)
                  group by TO_CHAR(created_date,'YYYY-MM-DD'),service_code
                  ORDER BY created_date,service_code
                "
        );

        return $results;
    }


    /**
     * Get list statistic eGPA on ETL data completed extrac
     * @return list
     */
    protected function getListStatisticTotalETLEgpa()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   select 'EGPA_SYARIKAT' as MODUL , 'EPS-001' as service_code, TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM EGPA_SYARIKAT
                GROUP BY 1,2,3
              UNION
              select 'EGPA_BIDANG' as MODUL , 'EPS-002' as service_code,TO_CHAR(max(created_date),'YYYY-MM-DD HH24:MI') as created_date ,count(*) as total
                FROM EGPA_BIDANG
                GROUP BY 1,2,3 
                "
        );

        return $results;
    }

    /**
     * Get list statistic eGPA on creation file today
     * @return list
     */
    protected function getListStatisticTotalFileCreationBySchedulerEgpaToday()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "   /** Checking generate file **/
                        SELECT
                          TO_CHAR(d.created_date, 'YYYY-MM-DD') as created_date,
                          d.service_code,
                          DECODE(d.service_code, 'GPI-010', 'MYGPIS_SYARIKAT',
                                 'EPS-001', 'EGPA_SYARIKAT',
                                 'EPS-002', 'EGPA_BIDANG',
                                 'N/A')                                    AS modul,
                          count(*)                                         AS total_created,
                          (SELECT count(*)
                           FROM osb_logging
                           WHERE service_code = d.service_code
                                 AND trans_type = 'Status-BATCH'
                                 AND SERVICE_FLOW = 'JMS' AND status = 'S'
                                 AND trunc(created_date) = trunc(sysdate)) AS total_transferred
                        FROM DI_INTERFACE_LOG d
                        WHERE d.service_code IN (
                          'EPS-001', 'EPS-002'
                        )
                              AND trunc(d.created_date) = trunc(sysdate)
                        GROUP BY TO_CHAR(d.created_date, 'YYYY-MM-DD'), d.service_code
                        ORDER BY service_code
                "
        );

        return $results;
    }

    /**
     * Get list statistic MyGPIS on file succesfully transferred to MyGPIS
     * @return list
     */
    protected function getListStatisticTotalFileEgpaTodaySuccessfullyTransferred()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "  select TO_CHAR(created_date,'YYYY-MM-DD')  as created_date,
                    service_code ,
                    DECODE (service_code, 'EPS-001', 'MYGPIS_SYARIKAT',
                                          'EPS-001', 'MYGPIS_BARANG',
                                                  'N/A') as modul,
                    count(*) as total  from osb_logging where service_code in (
                    'GPI-010','GPI-020','GPI-030','GPI-040',
                    'GPI-050','GPI-060','GPI-070','GPI-080',
                    'GPI-090','GPI-100','GPI-110','GPI-120'
                  )
                  AND trans_type = 'Status-BATCH'
                  AND SERVICE_FLOW = 'JMS'   and status = 'S'
                  AND trunc(created_date) = trunc(sysdate)
                  group by TO_CHAR(created_date,'YYYY-MM-DD'),service_code
                  ORDER BY created_date,service_code
                "
        );

        return $results;
    }

    protected function getListStatisticTotalFileCreationTransferredByScheduler($dateSearch, $serviceCode, $processId)
    {

        $query = "select '$dateSearch' as created_date ,
                    '$serviceCode' as service_code,
                    '$processId' as process_id,
                    (SELECT service_name FROM osb_service WHERE service_code = '$serviceCode' ) as service_name,
                    (SELECT count(*)
                         FROM DI_INTERFACE_LOG
                         WHERE service_code = '$serviceCode'
                               AND trunc(created_date) = to_date('$dateSearch','YYYY-MM-DD')
                               AND file_name IS NOT null 
                               AND file_name NOT IN ('DNF')
                    ) as total_di_interface_created,
                    (SELECT count(*)
                         FROM osb_batch_file
                         WHERE service_code = '$serviceCode'
                               AND trunc(created_date) = to_date('$dateSearch','YYYY-MM-DD') ) AS total_batch_file_created,
                    (SELECT count(*)
                         FROM osb_logging
                         WHERE service_code = '$serviceCode'
                               AND trans_type = 'Status-BATCH'
                               AND SERVICE_FLOW = 'JMS' AND status = 'S'
                               AND trunc(created_date) = to_date('$dateSearch','YYYY-MM-DD') ) AS total_sent
                    FROM DUAL ";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getListStatisticTotalFileReceivedByScheduler($dateSearch, $serviceCode, $processId)
    {

        $query = "select '$dateSearch' as created_date ,
                    '$serviceCode' as service_code,
                    '$processId' as process_id,
                    (SELECT service_name FROM osb_service WHERE service_code = '$serviceCode' ) as service_name,
                    (SELECT count(*)
                         FROM osb_batch_file
                         WHERE service_code = '$serviceCode'
                               AND trunc(created_date) = to_date('$dateSearch','YYYY-MM-DD') ) AS total_batch_file_created,
                    (SELECT count(*)
                               FROM DI_INTERFACE_LOG
                               WHERE service_code = '$serviceCode'
                                     AND trunc(changed_date) = to_date('$dateSearch','YYYY-MM-DD') 
                                     AND file_name is not null 
                                     AND file_name NOT IN ('DNF')
                          ) as total_di_interface_created,
                    (SELECT count(*)
                          FROM osb_logging
                          WHERE service_code = '$serviceCode'
                                AND trans_type = 'Status-BATCH'
                                AND SERVICE_FLOW = 'JMS' AND status = 'S'
                                AND trunc(created_date) = to_date('$dateSearch','YYYY-MM-DD') ) AS total_transferred
                    FROM DUAL ";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    /**
     * Get list statistic MyGPIS on creation file today
     * @return list
     */
    protected function getListStatisticTotalFileCreationTransferredBySchedulerToday($serviceCode, $module)
    {

        $query = "select TO_CHAR(sysdate, 'YYYY-MM-DD') as created_date ,
                    '$serviceCode' as service_code,
                    '$module' as modul,
                    (SELECT count(*)
                         FROM osb_logging
                         WHERE service_code = '$serviceCode'
                               AND trans_type = 'IBReq-BATCH'
                               AND trunc(created_date) = trunc(sysdate) ) AS total_created,
                    (SELECT count(*)
                         FROM osb_logging
                         WHERE service_code = '$serviceCode'
                               AND trans_type = 'Status-BATCH'
                               AND SERVICE_FLOW = 'JMS' AND status = 'S'
                               AND trunc(created_date) = trunc(sysdate) ) AS total_transferred
                    FROM DUAL ";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }



    /**
     * Get list statistic MyGPIS on creation file today
     * @return Object
     */
    protected function getStatisticSSMErrorLogToday($errorCode = 70006)
    {
        $typeError = 'Host Not Found';
        if ($errorCode == 70006) {
            $typeError = 'Timeout';
        }
        $query = "SELECT '$typeError' as error, min(trans_date) as start_date,max(trans_date) as last_date ,
                    count(*) as total 
                    FROM osb_logging a
                    WHERE trunc(a.trans_date) = trunc(sysdate)
                          AND a.trans_type = 'OBRes' AND a.service_code = 'SSM-101' AND status = 'F'
                          AND status_code IN ('$errorCode') 
                    ORDER BY last_date desc ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    protected function getStatisticSSMErrorLog()
    {

        $query = "SELECT 
                    max(trans_date) as max_datetime, min(trans_date) as min_datetime,
                    status_code,status_desc , count(*) as total
                    FROM osb_logging WHERE service_code = 'SSM-101' 
                    AND trunc(trans_date) = trunc(sysdate)
                    AND trans_type = 'OBRes' 
                    -- AND status = 'F'
                    GROUP BY  status_code,status_desc 
                    ORDER BY max_datetime desc";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getStatisticMyIdentityLogToday()
    {

        $query = "SELECT min(trans_date) as min_trans_date , max(trans_date) as max_trans_date , status_code,status_desc , count(*) as total
                    FROM osb_logging WHERE service_code IN ('JPN-101','JPN-102')
                    AND trunc(trans_date) = trunc(sysdate)
                    AND trans_type = 'OBRes' 
                    -- AND status = 'F'
                    GROUP BY  status_code,status_desc  
                    ORDER BY 2 desc";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    /**
     * Get list statistic MyGPIS on creation file today
     * @return Object
     */
    protected function getStatisticGFMASIntegrationErrorLogToday($typeError, $errorCode, $service_code, $serviceName)
    {

        $query = "SELECT '$typeError' as error,'$service_code' as service_code, '$serviceName' as service_name, min(trans_date) as start_date,max(trans_date) as last_date ,
                    count(*) as total 
                    FROM osb_logging a
                    WHERE trunc(a.trans_date) = trunc(sysdate)
                          AND a.trans_type = 'OBRes' AND a.service_code = '$service_code' AND status = 'F'
                          AND status_code IN ('$errorCode')";
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    protected function getTotalListBatchRetry($target = null, $serviceName = null, $trans_id = null)
    {

        if ($target !== null) {
            if ($serviceName !== null) {
                $query = "SELECT BATCH_RETRY_DTL_ID as ID, TRANS_ID, TARGET_SYSTEM, SERVICE_CODE, SERVICE_NAME, FILE_NAME, STATUS_DESC "
                    . "FROM OSB_BATCH_RETRY_DTL WHERE TARGET_SYSTEM = '$target' AND SERVICE_NAME = '$serviceName'";
            } else {
                $query = "SELECT BATCH_RETRY_DTL_ID as ID, TRANS_ID, TARGET_SYSTEM, SERVICE_CODE, SERVICE_NAME, FILE_NAME, STATUS_DESC "
                    . "FROM OSB_BATCH_RETRY_DTL WHERE TARGET_SYSTEM = '$target'";
            }
        } else if ($trans_id !== null) {
            $query = "SELECT BATCH_RETRY_DTL_ID as ID, TRANS_ID, BATCH_RETRY_DTL_ID, TARGET_SYSTEM, SERVICE_CODE, SERVICE_NAME, FILE_NAME, STATUS_DESC FROM OSB_BATCH_RETRY_DTL WHERE TRANS_ID = '$trans_id'";
        } else {
            $query = "SELECT BATCH_RETRY_DTL_ID as ID, TRANS_ID, TARGET_SYSTEM, SERVICE_CODE, SERVICE_NAME, FILE_NAME, STATUS_DESC FROM OSB_BATCH_RETRY_DTL";
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getTotalListBatchRetryByServiceName()
    {

        $query = "SELECT TARGET_SYSTEM,count(*) as total FROM OSB_BATCH_RETRY_DTL group by TARGET_SYSTEM";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getServiceByTarget($type, $target)
    {

        if ($type === 'service') {
            $query = "select distinct service_name from OSB_RETRY_DTL where target_system = '$target'";
        } else {
            $query = "select distinct service_name from osb_batch_retry_dtl where target_system = '$target'";
        }

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getTotalTarget($type, $targetName)
    {

        $query = "";
        if ($type === 'service') {
            $query = "select target_system, count(*) as total from osb_retry_dtl where target_system = '$targetName' group by target_system";
        } else {
            $query = "select target_system, count(*) as total from osb_batch_retry_dtl where target_system = '$targetName' group by target_system";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getTotalService($type, $serviceName)
    {

        $query = "";
        if ($type === 'service') {
            $query = "select service_name, count(*) as total from osb_retry_dtl where service_name = '$serviceName' group by service_name";
        } else {
            $query = "select service_name, count(*) as total from osb_batch_retry_dtl where service_name = '$serviceName' group by service_name";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getTotalListServiceRetry($target = null, $serviceName = null, $trans_id = null)
    {

        if ($target !== null) {
            if ($serviceName !== null) {
                $query = "SELECT RETRY_DTL_ID as ID, TRANS_ID, RETRY_DTL_ID, SERVICE_NAME, TARGET_SYSTEM "
                    . "FROM OSB_RETRY_DTL WHERE TARGET_SYSTEM = '$target' AND SERVICE_NAME = '$serviceName'";
            } else {
                $query = "SELECT RETRY_DTL_ID as ID, TRANS_ID, RETRY_DTL_ID, SERVICE_NAME, TARGET_SYSTEM "
                    . "FROM OSB_RETRY_DTL WHERE TARGET_SYSTEM = '$target'";
            }
        } else if ($trans_id !== null) {
            $query = "SELECT RETRY_DTL_ID as ID, TRANS_ID, RETRY_DTL_ID, PAYLOAD, SERVICE_NAME, TARGET_SYSTEM FROM OSB_RETRY_DTL WHERE TRANS_ID = '$trans_id'";
        } else {
            $query = "SELECT RETRY_DTL_ID as ID, TRANS_ID, RETRY_DTL_ID, SERVICE_NAME, TARGET_SYSTEM FROM OSB_RETRY_DTL";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function getTotalListServiceRetryByServiceName()
    {

        $query = "SELECT TARGET_SYSTEM,count(*) as total FROM OSB_RETRY_DTL group by TARGET_SYSTEM";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);

        return $results;
    }

    protected function checkingOsbTigger($transId, $date)
    {

        $query = "SELECT * FROM OSB_LOGGING WHERE TRANS_ID = '$transId'
                    AND trans_type = 'OBRes'
                    AND service_code = 'EPP-600'
                    AND to_char(trans_date,'YYYY-MM-DD HH24:MI') = '$date'";

        $results = DB::connection('oracle_nextgen_fullgrant')->select($query);

        return $results;
    }

    protected function checkingOsbBatchTrigger($rquid, $date)
    {

        $query = "select * FROM OSB_LOGGING WHERE RQ_UID = '$rquid' 
                    AND TRANS_TYPE = 'OBRes-BATCH' 
                    AND to_char(trans_date,'YYYY-MM-DD HH24:MI') = '$date'";

        $results = DB::connection('oracle_nextgen_fullgrant')->select($query);

        return $results;
    }

    protected function deleteServiceRetry($dtlId, $transId)
    {

        $query = DB::connection('oracle_nextgen_fullgrant')
            ->table('OSB_RETRY_DTL')
            ->where('retry_dtl_id', $dtlId)
            ->where('trans_id', $transId);

        $query->delete();

        return $query;
    }

    protected function deleteBatchRetry($dtlId, $transId)
    {

        $query = DB::connection('oracle_nextgen_fullgrant')
            ->table('OSB_BATCH_RETRY_DTL')
            ->where('batch_retry_dtl_id', $dtlId)
            ->where('trans_id', $transId);

        $query->delete();

        return $query;
    }

    protected function masterData($type, $param1, $param2, $param3 = null, $param4 = null)
    {

        switch ($type) {
            case "PROJECT":
                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_PROJECT as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.PROJECT_CODE', 'like', '%' . strtoupper($param1) . '%');
                }
                if ($param2 !== null) {
                    $query->where('A.PROJECT_DESC', 'like', '%' . strtoupper($param2) . '%');
                }
                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.INTERFACE_LOG_ID', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME', 'B.TRANS_ID');
                $data = $query->get();

                return $data;

                break;

            case "INTERFACE_LOG":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_INTERFACE_LOG as A');

                if ($param1 !== null) {
                    $query->where('A.SERVICE_CODE', '=', strtoupper($param1));
                }
                if ($param2 !== null) {
                    $query->where('A.PROCESS_ID', '=', $param2);
                }

                if ($param3 !== null) {
                    $query->where('A.FILE_NAME', 'like', '%' . $param3 . '%');
                }
                if ($param4 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param4);
                }

                /** $param3: fileName
                 *  $param4: Date
                 *  To avoid take all data
                 * **/
                if ($param3 == null && $param4 == null) {
                    $query->take(2000);
                    $query->orderBy('A.CREATED_DATE', 'desc');
                }

                return $query->get();

            case "PTJ":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_PTJ as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.PTJ_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.PTJ_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME', 'B.TRANS_ID');

                return $query->get();

            case "KUMP_PTJ":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_KPTJ as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.KPTJ_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.KPTJ_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME', 'B.TRANS_ID');

                return $query->get();

            case "PEG_PENGAWAL":
                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_PP as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.PP_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.PP_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME', 'B.TRANS_ID');

                return $query->get();

            case "VOT":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_VOT as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.VOT_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.VOT_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME', 'B.TRANS_ID');

                return $query->get();

            case "GL_ACCOUNT":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_GL_ACCOUNT as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.GL_ACCOUNT_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.GL_ACCOUNT_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where('A.KPTJ_CODE', 'like', '%' . $param3 . '%');
                }

                if ($param4 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param4);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME');

                return $query->get();

            case "PROGRAM_ACTIVITY":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('DI_IM_PRG_ACT as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.PRG_ACT_CODE', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.DI_IM_PRG_DESC', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME');

                return $query->get();

            case "AG_OFFICE":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('di_im_ag_office as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.office_code', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.office_desc', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME');

                return $query->get();

            case "DANA":

                $query = DB::connection('oracle_nextgen_rpt')
                    ->table('di_im_dana as A')
                    ->leftJoin('DI_INTERFACE_LOG as B', 'B.INTERFACE_LOG_ID', '=', 'A.INTERFACE_LOG_ID');

                if ($param1 !== null) {
                    $query->where('A.dana_code', 'like', '%' . strtoupper($param1) . '%');
                }

                if ($param2 !== null) {
                    $query->where('A.dana_desc', 'like', '%' . strtoupper($param2) . '%');
                }

                if ($param3 !== null) {
                    $query->where(DB::raw("TO_CHAR(A.CREATED_DATE, 'YYYY-MM-DD')"), '=', $param3);
                }

                $query->select('A.*', 'B.SERVICE_CODE', 'B.PROCESS_ID', 'B.FILE_NAME');

                return $query->get();

            default:
                break;
        }
    }

    protected function epMasterData($type, $code)
    {

        $results = array();
        switch ($type) {
            case "vot":
                $results = DB::connection('oracle_nextgen_rpt')
                    ->table('pm_vot_fund as a');

                $votFundType = substr($code, 0, 1);
                //project/prg activity
                if ($votFundType === 'B' || $votFundType === 'T') {
                    if (strlen($code) === 1) {
                        $results->where('a.vot_fund_type', '=', substr($code, 0, 1))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_prg_activity where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if (strlen($code) === 3) {
                        $results->where('a.vot_fund_code', '=', substr($code, 0, 3))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_prg_activity where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if (strlen($code) === 4) {
                        $results->where('a.vot_fund_code', '=', substr($code, 0, 4))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_prg_activity where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if (strlen($code) > 4) {
                        $length = strlen($code);
                        $projectCode = substr($code, 3, $length);
                        $results->join('pm_prg_activity as b', 'a.vot_fund_id', '=', 'b.vot_fund_id')
                            ->where(DB::raw('concat(a.vot_fund_code,b.prg_activity_code)'), 'like', '%' . $code . '%')
                            ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw("(select count(*) from pm_prg_activity where vot_fund_id = a.vot_fund_id and record_status = 1 and prg_activity_code like '%" . $projectCode . "%') as total"))
                            ->groupBy('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id');
                    } else {
                        $results->where('a.vot_fund_code', '=', $code)
                            ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw("'0' as total"));
                    }
                } else if ($votFundType === 'P' || $votFundType === 'S') {
                    $length = strlen($code);

                    if ($length === 1) {
                        $results->where('a.vot_fund_type', '=', substr($code, 0, 1))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_sub_setia where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if ($length === 3) {
                        $results->where('a.vot_fund_code', '=', substr($code, 0, 3))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_sub_setia where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if ($length === 4) {
                        $results->where('a.vot_fund_code', '=', substr($code, 0, 4))
                            ->select('a.*')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw('(select count(*) from pm_sub_setia where vot_fund_id = a.vot_fund_id and record_status = 1) as total'));
                    } else if ($length > 4 && $length < 9) {
                        $projectCode = substr($code, 3, $length);
                        $results->join('pm_sub_setia as b', 'a.vot_fund_id', '=', 'b.vot_fund_id')
                            ->where(DB::raw('concat(a.vot_fund_code,b.project_code)'), 'like', '%' . $code . '%')
                            ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw("(select count(*) from pm_sub_setia where vot_fund_id = a.vot_fund_id and record_status = 1 and project_code like '%" . $projectCode . "%') as total"))
                            ->groupBy('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id');
                    } else if ($length > 8) {
                        $projectCode = substr($code, 3, 5);
                        $setiaCode = substr($code, 8, 3);

                        $results->join('pm_sub_setia as b', 'a.vot_fund_id', '=', 'b.vot_fund_id')
                            ->where('a.vot_fund_code', '=', substr($code, 0, 3))
                            ->where(DB::raw('concat(a.vot_fund_code,b.project_code)'), 'like', '%' . $projectCode . '%')
                            ->where(DB::raw('concat(b.project_code,b.setia_code)'), 'like', '%' . $setiaCode . '%')
                            ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw("(select count(*) from pm_sub_setia where vot_fund_id = a.vot_fund_id and record_status = 1 and project_code like '%" . $projectCode . "%' and setia_code like '%" . $setiaCode . "%') as total"))
                            ->groupBy('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id');
                    } else {
                        $results->where('a.vot_fund_code', '=', $code)
                            ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                            ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                            ->addSelect(DB::raw("'0' as total"));
                    }
                } else {
                    $results->where('a.vot_fund_code', '=', $code)
                        ->select('a.vot_fund_id', 'a.vot_fund_type', 'a.vot_fund_code', 'a.description', 'a.record_status', 'a.created_date', 'a.changed_date', 'a.ptj_group_id')
                        ->addSelect(DB::raw("(select org_code from pm_org_profile op,pm_org_validity ov where op.org_profile_id = ov.org_profile_id 
                                    and op.org_profile_id = a.ptj_group_id and op.record_status =1 and ov.record_status = 1) as org_code"))
                        ->addSelect(DB::raw("'0' as total"));
                }

                return $results->get();
                break;

            case "ptj":

                $query = "select org2.*,
                    (select org_type_id from pm_org_profile b where b.org_profile_id = org2.parentc)as parenttypeidc,
                    (select case when org_type_id = 5 then 'PTJ'
                            when org_type_id = 4 then 'KPTJ'
                            when org_type_id = 3 then 'PP'
                            when org_type_id = 2 then 'MIN' end from pm_org_profile b where b.org_profile_id = org2.parentc)as parenttypec
                    from ( select org.*,
                    (select parent_org_profile_id from pm_org_profile where org_profile_id = org.parentb) as parentc,
                    (select org_type_id from pm_org_profile b where b.org_profile_id = org.parentb)as parenttypeidb,
                    (select case when org_type_id = 5 then 'PTJ'
                            when org_type_id = 4 then 'KPTJ'
                            when org_type_id = 3 then 'PP'
                            when org_type_id = 2 then 'MIN' end from pm_org_profile b where b.org_profile_id = org.parentb)as parenttypeb
                    from(
                    select op.*,
                    case when op.org_type_id = 5 then 'PTJ'
                            when op.org_type_id = 4 then 'KPTJ'
                            when op.org_type_id = 3 then 'PP'
                            when op.org_type_id = 2 then 'MIN' end as type,
                    ov.org_validity_id,ov.org_code,ov.org_name,ov.created_date as ov_created_date,
                    ov.changed_date as ov_changed_date,ov.record_status as ov_record_status,ov.ag_office_id,
                    (select org_profile_id from pm_org_profile a where a.org_profile_id = op.parent_org_profile_id) as parenta,
                    (select org_type_id from pm_org_profile a where a.org_profile_id = op.parent_org_profile_id) as parenttypeida,
                    (select case when org_type_id = 5 then 'PTJ'
                            when org_type_id = 4 then 'KPTJ'
                            when org_type_id = 3 then 'PP'
                            when org_type_id = 2 then 'MIN' end from pm_org_profile b where b.org_profile_id = op.parent_org_profile_id)as parenttypea,
                    (select parent_org_profile_id from pm_org_profile b where b.org_profile_id = op.parent_org_profile_id) as parentb
                    from pm_org_profile op, pm_org_validity ov
                    where op.org_profile_id = ov.org_profile_id
                and ov.org_code = '$code') org) org2";

                $results = DB::connection('oracle_nextgen_rpt')->select($query);

                return $results;
                break;

            case "ag":
                $results = DB::connection('oracle_nextgen_rpt')
                    ->table('pm_ag_office as a')
                    ->where('a.office_code', $code);

                return $results->get();
                break;
        }
    }

    protected function epMasterDataProject($id, $type)
    {

        if ($type === 'B' || $type === 'T') {
            $query = DB::connection('oracle_nextgen_rpt')->table('pm_prg_activity as a')
                ->where('a.vot_fund_id', '=', $id);
        } else {
            $query = DB::connection('oracle_nextgen_rpt')->table('pm_sub_setia as a')
                ->where('a.vot_fund_id', '=', $id);
        }

        return $query->get();
    }

    protected function getListTransWsPhisFailedSend()
    {

        $query = "SELECT 
                (SELECT f.remarks_1 FROM osb_logging f WHERE f.trans_id = o.trans_id AND f.trans_type = 'IBReq' AND f.remarks_1 NOT IN ('NA') ) AS doc_no,
                -- (SELECT service_name FROM osb_service s WHERE s.service_code = o.service_code ) AS service_name,
                'N/A' AS service_name,
                o.trans_id, o.service_code,o.trans_date,o.status_code,o.status_desc 
                FROM OSB_LOGGING o WHERE 
                o.SERVICE_CODE IN ('PHS-080','PHS-150','PHS-160','PHS-170','PHS-180','PHS-190','PHS-200','PHS-170') 
                AND trans_type = 'OBRes-FAULT' 
                AND o.STATUS_CODE = '70003'
                AND o.status_desc NOT IN ('Generic Error - Service Provider Is Unable To Process Your Request: OSB Service Callout action received an error response')
                /*AND  NOT EXISTS (
                    SELECT 1 FROM osb_logging x WHERE x.SERVICE_CODE = o.SERVICE_CODE AND x.trans_type = 'OBRes-FAULT' 
                    AND x.status_desc  IN ('Generic Error - Service Provider Is Unable To Process Your Request: OSB Service Callout action received an error response')
                    AND x.trans_id in (
                        SELECT DISTINCT trans_id FROM osb_logging g WHERE g.SERVICE_CODE = o.SERVICE_CODE AND g.trans_type = 'IBReq' 
                        AND g.REMARKS_1 IN (SELECT remarks_1 FROM osb_logging f WHERE f.TRANS_ID  = o.TRANS_ID  AND f.trans_type = 'IBReq') 
                        )
                )*/
                AND  trunc(trans_date) >= to_date('2023-01-01','YYYY-MM-DD') 
                AND  NOT EXISTS (
                    SELECT 1 FROM osb_logging x WHERE x.SERVICE_CODE = o.SERVICE_CODE AND x.trans_type = 'IBRes' 
                    AND x.trans_id in (
                        SELECT DISTINCT trans_id FROM osb_logging g WHERE g.SERVICE_CODE = o.SERVICE_CODE AND g.trans_type = 'IBReq' 
                        AND g.REMARKS_1 IN (SELECT remarks_1 FROM osb_logging f WHERE f.TRANS_ID  = o.TRANS_ID  AND f.trans_type = 'IBReq') 
                        )
                )
                ORDER BY remarks_1";

        $isEpArchived = env("IS_EP_ARCHIVED", true);
        $resultArc = collect([]);
        if ($isEpArchived) {
            try {
                $resultArc = collect(DB::connection('oracle_nextgen_arc')->select($query));
            } catch (\Exception $e) {
                Log::error(__METHOD__ . ' Failed connection : oracle_nextgen_arc >> ' . $e->getMessage());
            }
        }

        $result = collect(DB::connection('oracle_nextgen_rpt')->select($query));

        if ($resultArc->count() > 0) {
            foreach ($resultArc as $row) {
                $checkSucc = DB::connection('oracle_nextgen_rpt')->select("SELECT count(*) AS total_rec FROM osb_logging x WHERE x.SERVICE_CODE =  '$row->service_code' AND x.trans_type = 'IBRes' 
                AND x.trans_id in (
                    SELECT DISTINCT trans_id FROM osb_logging g WHERE g.SERVICE_CODE = '$row->service_code' AND g.trans_type = 'IBReq' 
                    AND g.REMARKS_1 = '$row->doc_no'
                    )");
                /*
                $checkNoResp = DB::connection('oracle_nextgen_rpt')->select("SELECT count(*) AS total_rec FROM osb_logging x WHERE x.SERVICE_CODE =  '$row->service_code' AND x.trans_type = 'OBRes-FAULT' 
                AND x.status_desc  IN ('Generic Error - Service Provider Is Unable To Process Your Request: OSB Service Callout action received an error response')
                AND x.trans_id in (
                    SELECT DISTINCT trans_id FROM osb_logging g WHERE g.SERVICE_CODE = '$row->service_code' AND g.trans_type = 'IBReq' 
                    AND g.REMARKS_1 = '$row->doc_no'
                    )");
                */
                //if($checkSucc[0]->total_rec == 0 && $checkNoResp[0]->total_rec == 0){
                if ($checkSucc[0]->total_rec == 0) {
                    $result->push($row);
                }
            }
        }

        return $result;
    }


    /**
     * dateFormatSearch : YYYY-MM-DD
     */
    protected function getListTransWsIgfmasGfm110FrnErrorKodItemCubaSemula($dateFormatSearch)
    {
        $query = "SELECT a.trans_id,tmpF.* ,d.PAYLOAD_BODY  FROM 
        (
            SELECT  g.REMARKS_1,g.REMARKS_2,g.REMARKS_3 ,g.SERVICE_CODE ,max(g.TRANS_DATE) AS LOGDATE,
                (
                    SELECT count(*) FROM FL_FULFILMENT_NOTE  fn , FL_WORKFLOW_STATUS  ws 
                    WHERE fn.FULFILMENT_NOTE_ID  = ws.DOC_ID 
                    AND ws.DOC_TYPE = 'FN' 
                    AND ws.STATUS_ID = '43010'
                    AND fn.FULFILMENT_NOTE_NO  = g.remarks_2
                ) is_approved_frn
            
               FROM OSB_LOGGING g, OSB_LOGGING_DTL d 
               WHERE 
               g.LOGGING_ID = d.LOGGING_ID 
               AND trans_id IN (
                       SELECT k.TRANS_ID   FROM OSB_LOGGING k WHERE k.service_code IN ('GFM-100','GFM-110','GFM-120') 
                    AND k.status = 'F'
                    AND k.trans_type = 'IBRes-DEC' 
                    AND k.trans_date BETWEEN to_date('$dateFormatSearch','YYYY-MM-DD') AND to_date('$dateFormatSearch 23:59:59','YYYY-MM-DD HH24:MI:SS') 
                    AND k.STATUS_DESC = 'KOD ITEM 901016030000000000 SEDANG DIPROSES. MOHON CUBA SEBENTAR LAGI SEHINGGA BERJAYA.'
                ) 
                AND trans_type = 'IBReq' 
                AND service_code IN ('GFM-100','GFM-110','GFM-120') 
            GROUP BY g.REMARKS_1,g.REMARKS_2,g.REMARKS_3 ,g.SERVICE_CODE 
        ) tmpF  ,
        osb_logging a , 
        osb_logging_dtl d 
        WHERE  
         a.remarks_2 = tmpF.remarks_2 
        AND a.service_code = tmpf.service_code
        AND a.trans_date = tmpf.logdate 
        AND a.logging_id = d.LOGGING_ID  
        AND a.trans_type = 'IBReq'
        AND tmpf.is_approved_frn = 0";

        return collect(DB::connection('oracle_nextgen_rpt')->select($query));

    }

protected function getListItemCodeIGFMASIntegration($date, $date1)
    {
        $query = DB::connection('oracle_nextgen_rpt')->select("
         SELECT to_char(TRANS_DATE,'DD/MM/YYYY') AS TRANSACTION_DATE, TRANS_ID,status_desc, count(*) AS TOTAL 
 FROM (
	SELECT  A.TRANS_ID,A.SERVICE_CODE,A.TRANS_TYPE,A.TRANS_DATE,A.STATUS_CODE,A.STATUS,A.STATUS_DESC,A.REMARKS_1 AS DOC_NO,A.REMARKS_2,A.REMARKS_3
                FROM OSB_LOGGING A
                WHERE 
                A.TRANS_ID IN (
                  SELECT DISTINCT TRANS_ID FROM ( 
                    SELECT TRANS_ID, REMARKS_1, TRANS_DATE, RANK() OVER (PARTITION BY REMARKS_1 ORDER BY TRANS_DATE DESC) RANK
                        FROM OSB_LOGGING
                        WHERE TRANS_TYPE = 'IBReq'
                        AND TRANS_ID IN (
                           SELECT DISTINCT(TRANS_ID) FROM OSB_LOGGING 
                           WHERE 
                           SERVICE_CODE = 'GFM-100' 
                           AND 
                           ( STATUS_DESC LIKE 'Kod barang%%' OR   STATUS_DESC LIKE 'Unit ukuran%'  )
                            AND to_char(trans_date,'DD/MM/YYYY') IN ?
                            -- AND TRANS_DATE >= TO_DATE('2024-10-01 00:00:00', 'YYYY-MM-DD HH24:MI:SS') 
                        )
                      ) WHERE RANK = 1
                )
                AND  A.TRANS_TYPE IN ('IBReq','IBRes') 
              -- ORDER BY A.TRANS_ID , A.TRANS_TYPE
) tmp 
WHERE tmp.status_desc NOT IN ('NA')
AND to_char(tmp.trans_date,'DD/MM/YYYY') IN ?
GROUP BY 
to_char(TRANS_DATE,'DD/MM/YYYY') ,
tmp.TRANS_ID,tmp.status_desc
ORDER BY 1 ASC
        ", array($date, $date1));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
}