/*
 *  Document   : tablesDatatables.js
 *  Author     : pixelcave
 *  Description: Custom javascript code used in Tables Datatables page
 */

var TablesDatatables = function () {

    return {
        init: function () {
            /* Initialize Bootstrap Datatables Integration */
            App.datatables();

            /* Initialize Datatables */
            $('#basic-datatable').dataTable({
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            $('#basic-datatable-2').dataTable({
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            $('#basic-datatable-3').dataTable({
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, -1], [5, 10, 20, 30, 'All']]
            });
            $('#basic-datatable-4').dataTable({
                columnDefs: [],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, -1], [5, 10, 20, 30, 'All']]
            });
            $('#basic-datatable-5').dataTable({
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            $('#basic-datatable-6').dataTable({
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            $('#table-contract-datatable').dataTable({
                order: [[1, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            $('#table-contractedit-datatable').dataTable({
                order: [[2, "desc"]],
                columnDefs: [],
                pageLength: -1,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
            });
            /* Initialize Datatables */
            $('#tracking-datatable').dataTable({
                order: [[3, "desc"], [0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#ws-datatable').dataTable({
                order: [[3, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#bpm-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            /* Initialize Datatables */
            $('#datatable-category-code').dataTable({
                columnDefs: [{orderable: false, targets: [1, 5]}],
                pageLength: 5,
                lengthMenu: [[5, 10, 20, 30, -1], [5, 10, 20, 30, 'All']]
            });

            $('#item-codification-datatable').DataTable({
                dom: 'Bfrtip',
                buttons: [
                    'excel', 'pdf'
                ],
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#summary-appl-history-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#summary-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#payload-datatable').dataTable({
                order: [[0, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#bsv-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#iklanRN-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#supplier-datatable').dataTable({
                order: [[1, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#suppliers-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#ptjgroup-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            
            $('#ptjhistory-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#statuswork-datatable').dataTable({
                order: [[3, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#statusPOCO-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#item-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#ROapprover-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#status-datatable').dataTable({
                order: [[1, 5, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#bpmapi-datatable').dataTable({
                order: [[3, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#itemparcial-datatable').dataTable({
                order: [],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#cancel-datatable').dataTable({
                order: [[0, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#rejected-datatable').dataTable({
                order: [[0, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#smdel01-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel02-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel03-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel04-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel05-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel06-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel07-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel08-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel09-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#smdel10-datatable').dataTable({
                order: [[0, "desc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#data-listport-datatable').dataTable({
                order: [[1, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            $('#datalistport-datatable').dataTable({
                order: [[1, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            $('#datalookup-datatable').dataTable({
                order: [[0, "asc"]],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });

            /* Add placeholder attribute to the search input */
            $('.dataTables_filter input').attr('placeholder', 'Search');
        }
    };
}();