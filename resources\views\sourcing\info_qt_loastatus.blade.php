@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('find/qt/loastatus')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="qt_no" name="qt_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Masukkan No Sebutharga/Tender & Mulakan Carian ...">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
</div>
@if($success == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif

<!--Akmal Region QT INFO & BRIEFING SITE VISIT-->
@if($success)
<div class="block block-alt-noborder full">
    <!--AKMAL RSTUCK INFO-->
    <h4><strong> CHECK ALL LOA STATUS </strong></h4>
    <div class="block">   
        <div class="row">
            <div class="col-md-12">
                <h5><strong> LIST OF SUCCESSFULL LOA </strong></h5>
                <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT NO</th>
                            <th class="text-center">LOA NO</th>
                            <th class="text-center">SUPPLIER NAME</th>
                            <th class="text-center">ITEM NAME</th>      
                            <th class="text-center">RECORD STATUS</th>    
                            <th class="text-center">CANCEL ITEM</th>    
                            <th class="text-center">LOA STATUS</th>  
                        </tr>
                    </thead>
                    @foreach ($success as $key=>$msuccess)
                    <tr>
                        <td class="text-center">{{ $msuccess->qt_no }}</td>
                        <td class="text-center">{{ $msuccess->loa_no }}</td>
                        <td class="text-center">{{ $msuccess->supplier_name }}</td>
                        <td class="text-left">{{ $msuccess->item_name }}</td>
                        <td class="text-center">{{ $msuccess->record_status }}</td>
                        <td class="text-center">{{ $msuccess->is_cancelled }}</td>
                        <td class="text-center">{{ $msuccess->is_loa_awarded }}</td>
                    </tr>
                    @endforeach                        
                </table>
                
                <h5><strong> LIST OF PENDING LOA </strong></h5>
                <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT NO</th>
                            <th class="text-center">LOA NO</th>
                            <th class="text-center">SUPPLIER NAME</th>
                            <th class="text-center">ITEM NAME</th>      
                            <th class="text-center">RECORD STATUS</th>    
                            <th class="text-center">CANCEL ITEM</th>    
                            <th class="text-center">LOA STATUS</th>  
                        </tr>
                    </thead>
                    <tbody>
                        @if($pendingloa)
                        @foreach ($pendingloa as $key=>$mpending)
                        <tr>
                            <td class="text-center">{{ $mpending->qt_no }}</td>
                        <td class="text-center">{{ $mpending->loa_no }}</td>
                        <td class="text-center">{{ $mpending->supplier_name }}</td>
                        <td class="text-left">{{ $mpending->item_name }}</td>
                        <td class="text-center">{{ $mpending->record_status }}</td>
                        <td class="text-center">{{ $mpending->is_cancelled }}</td>
                        <td class="text-center">{{ $mpending->is_loa_awarded }}</td>
                        </tr>
                        @endforeach
                        @endif
                    </tbody>
                </table>
                
                <h5><strong> REJECTED BY SUPPLIER & CONTINUE REFINALIZATION </strong></h5>
                <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT NO</th>
                            <th class="text-center">LOA NO</th>
                            <th class="text-center">ITEM NAME</th>         
                            <th class="text-center">RECORD STATUS</th>    
                            <th class="text-center">CANCEL ITEM</th>    
                            <th class="text-center">LOA STATUS</th>  
                        </tr>
                    </thead>
                    <tbody>
                        @if($rejectcontinue)
                        @foreach ($rejectcontinue as $key=>$mrcon)
                        <tr>
                            <td class="text-center">{{ $mrcon->qt_no }}</td>
                            <td class="text-center">{{ $mrcon->loa_no }}</td>
                              <td class="text-left">{{ $mrcon->item_name }}</td>
<!--                            <td class="text-left">
                          <span class="less{{ $key }}">{{ str_limit($mrcon->item_name, $limit = 35, $end = '...') }}</span>
                          <span class="details{{ $key }}" style="display:none">{{ $mrcon->item_name }}</span>
                          <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                              $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                  });">See More</a>
                      </td>-->
                            <td class="text-center">{{ $mrcon->record_status }}</td>
                            <td class="text-center">{{ $mrcon->is_cancelled }}</td>
                            <td class="text-center">{{ $mrcon->is_loa_awarded }}</td>
                        </tr>
                        @endforeach
                        @endif
                    </tbody>
                </table>
                
                <h5><strong> REJECTED BY SUPPLIER & CANCELLED ITEM BY PTJ </strong></h5>
                <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT NO</th>
                            <th class="text-center">LOA NO</th>
                            <th class="text-center">ITEM NAME</th>        
                            <th class="text-center">RECORD STATUS</th>    
                            <th class="text-center">CANCEL ITEM</th>    
                            <th class="text-center">LOA STATUS</th>  
                        </tr>
                    </thead>
                    <tbody>
                        @if($rejectcancel)
                        @foreach ($rejectcancel as $key=>$mrc)
                        <tr>
                            <td class="text-center">{{ $mrc->qt_no }}</td>
                            <td class="text-center">{{ $mrc->loa_no }}</td>
                             <td class="text-left">{{ $mrc->item_name }}</td>
<!--                            <td class="text-left">
                                <span class="less{{ $key }}">{{ str_limit($mrc->item_name, $limit = 35, $end = '...') }}</span>
                                <span class="details{{ $key }}" style="display:none">{{ $mrc->item_name }}</span>
                                <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                    });">See More</a>
                            </td>-->
                            <td class="text-center">{{ $mrc->record_status }}</td>
                            <td class="text-center">{{ $mrc->is_cancelled }}</td>
                            <td class="text-center">{{ $mrc->is_loa_awarded }}</td>
                        </tr>
                        @endforeach
                        @endif
                    </tbody>
                </table>


            </div>
        </div>
    </div>
</div>
@endif
@endsection
@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                                                                    TablesDatatables.init();
                                                                    });</script>

@endsection