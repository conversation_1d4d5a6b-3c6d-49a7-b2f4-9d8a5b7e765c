<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\BPMService;
use Carbon\Carbon;
use SSH;
use DB;

class DashboardBpmController extends Controller
{

    use BPMService;

    public function getDashboardBpmStlRunning($composite)
    {
        
        $list = array();
        switch ($composite) {
            case "Profile_Management":
                $list = $this->findListInstanceProfileMgmtRunningStl();
              break;
            case "Supplier_Management":
                $list = $this->findListInstanceSupplierMgmtRunningStl();
              break;
            case "Codification":
                $list = $this->findListInstanceCodificationRunningStl();
              break;
            case "Procurement_Plan":
                $list = $this->findListInstanceProcurementPlanRunningStl();
              break;
            case "Contract_Management":
                $list = $this->findListInstanceContractMgmtRunningStl();
              break;
            case "SourcingDP":
                $list = $this->findListInstanceSourcingDpRunningStl();
              break;
            case "SourcingQT":
                $list = $this->findListInstanceSourcingQtRunningStl();
              break;
            case "Order":
                $list = $this->findListInstanceOrderRunningStl();
              break;
            case "YEP_Order":
                $list = $this->findListInstanceYEPOrderRunningStl();
              break;
            case "Fulfilment":
                $list = $this->findListInstanceFulfilmentRunningStl();
              break;
            case "YEP_Fulfilment":
                $list = $this->findListInstanceYEPFulfilmentRunningStl();
              break;
            default:
                $list = $this->findListInstanceCodificationRunningStl();
        }
        return view('bpm.dashboard_stl_running', ['listResult' => $list]);
    }


}
