<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmFixPaymentProcess {

    use SupplierService;

    public static function fixPaymentProcess(){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmFixPaymentProcess();
        $listStuckPayment = $thisCls->getListPaymentStuckFeeProcessing();
        MigrateUtils::logDump(__METHOD__. ' total found Fee Processing Payment: '.count($listStuckPayment));
        $thisCls->updatePaymentStuck($listStuckPayment );

        $listStuckPayment2 = $thisCls->getListPaymentStuckRegistration();
        MigrateUtils::logDump(__METHOD__. ' total found Registration Payment: '.count($listStuckPayment2));
        $thisCls->updatePaymentStuck($listStuckPayment2);


        $listStuckPayment3 = $thisCls->getListPaymentStuckSoftcert();
        MigrateUtils::logDump(__METHOD__. ' total found Softcert Payment: '.count($listStuckPayment3));
        $thisCls->updatePaymentStuck($listStuckPayment3);

        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    protected function updatePaymentStuck($listStuckPayment){
        $actionName = 'UpdatePaymentProcess';
        $actionTypeLog = 'Update Table Ep';
        foreach($listStuckPayment as $obj){
            dump($obj);
            if($obj->proceed == 1 ){
                MigrateUtils::logDump($obj->appl_no .' checking');
                $pendingProcessId = $obj->pending_process_id;
                $applId = $obj->appl_id;
                $paymentId = $obj->payment_id;
                if($pendingProcessId && $applId && $paymentId){
                    $parameters =  collect([]);            
                    $parameters->put("pending_process_id", $pendingProcessId);
                    $parameters->put("appl_id", $applId);
                    $parameters->put("payment_id", $paymentId);
                    $parameters->put("action_task", 'Set record_status to 1 at sm_pending_process'); 
                    MigrateUtils::logDump('Pending Process Id : '.$pendingProcessId. ', Appl Id : '. $applId .', Payment Id : '. $paymentId);
                    $update =  DB::connection('oracle_nextgen_fullgrant')
                        ->table('SM_PENDING_PROCESS')->where('pending_process_id',$pendingProcessId)
                            ->where('appl_id',$applId)->where('payment_id',$paymentId)
                        ->update(
                            [
                                'record_status' => 1
                            ] 
                        );
                    MigrateUtils::logDump('Success Update ? '.$update);
                    EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed');
                }
                
            }else{
                MigrateUtils::logDump($obj->appl_no .' >> Please do manual checking... ');
            }     
        }
    }

    
    /**
     * Fee RM50
     * Pending Supporting Document Verification (20101).
     */
    protected function getListPaymentStuckFeeProcessing(){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "select smp.pending_process_id, smp.payment_id, smp.appl_id, smp.attempt, smp.record_status as process_record_status, smp.created_date, smp.changed_date, smp.err_msg,
                supp.supplier_id, supp.company_name, supp.reg_no, supp.ep_no, supp.record_status,appl.appl_no, supp.company_name,
                pymt.changed_date, pymt.payment_date, pymt.payment_amt,
                (SELECT count(*) as total 
                    FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                    WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                    AND a.appl_id = d.doc_id
                    AND a.appl_no = appl.appl_no
                    and d.status_id = 20101
                    and w.status_id = 20101 ) as proceed
                from SM_PENDING_PROCESS smp, SM_APPL appl, SM_SUPPLIER supp, PY_PAYMENT pymt,PY_BILL bill
                where appl.appl_id=SMP.appl_id
                and supp.supplier_id=appl.supplier_id
                and smp.payment_id=pymt.payment_id 
                AND pymt.BILL_ID = bill.BILL_ID 
                and smp.record_status=8
                and bill.BILL_TYPE  = 'P'
                and SMP.attempt >=3");
        return $dataList;
    }

    /**
     * Fee RM400
     * Registered
     */
    protected function getListPaymentStuckRegistration(){
        $dataList = DB::connection('oracle_nextgen_rpt')
                ->select("select smp.pending_process_id, smp.payment_id, smp.appl_id, smp.attempt, smp.record_status as process_record_status, smp.created_date, smp.changed_date, smp.err_msg,
                supp.supplier_id, supp.company_name, supp.reg_no, supp.ep_no, supp.record_status,appl.appl_no, supp.company_name,
                pymt.changed_date, pymt.payment_date, pymt.payment_amt,
                (
                    SELECT count(*) as total 
                    FROM sm_appl a, SM_WORKFLOW_STATUS w, PM_TRACKING_DIARY d
                    WHERE a.appl_id = w.doc_id AND w.doc_type = 'SR'
                    AND a.status_id = d.status_id
                    AND a.appl_id = d.doc_id
                    AND a.appl_no = appl.appl_no
                    and d.status_id = 20199
                    and appl.status_id = 20199
                    and w.status_id = 20199 and w.is_current = 1) as proceed
                from SM_PENDING_PROCESS smp, SM_APPL appl, SM_SUPPLIER supp, PY_PAYMENT pymt, PY_BILL bill
                where appl.appl_id=SMP.appl_id
                and supp.supplier_id=appl.supplier_id
                and smp.payment_id=pymt.payment_id
                AND pymt.BILL_ID = bill.BILL_ID 
                and smp.record_status=8
                and bill.BILL_TYPE  = 'R'
                and SMP.attempt >=3");
        return $dataList;
    }

    /**
     * Softcert Payment RM120
     * Get list of invalid stuck on payment softcert. 
     */
    protected function getListPaymentStuckSoftcert(){
        $dataList = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT
                        smp.pending_process_id,	smp.payment_id,	smp.appl_id,smp.attempt,smp.record_status AS process_record_status,smp.created_date,
                        smp.changed_date,smp.err_msg,
                        supp.supplier_id,supp.company_name,supp.reg_no,supp.ep_no,supp.record_status,
                        appl.appl_no,
                        pymt.changed_date,pymt.payment_date,pymt.payment_amt,
                        p.identification_no,p.name,p.user_id,
                        sr.softcert_request_id,sr.softcert_provider, 
                        1 as proceed
                    FROM
                        sm_pending_process smp,
                        sm_appl appl,
                        sm_supplier supp,
                        py_payment pymt,
                        py_bill bill,
                        py_bill_dtl pbd ,
                        sm_personnel p ,
                        sm_softcert_request sr ,
                        py_payment_dtl ppd
                    WHERE
                        appl.appl_id = smp.appl_id
                        AND supp.supplier_id = appl.supplier_id
                        AND smp.payment_id = pymt.payment_id
                        AND pymt.bill_id = bill.bill_id
                        AND bill.bill_id = pbd.bill_id
                        AND pbd.bill_dtl_ref_id = p.personnel_id
                        AND pymt.payment_id = ppd.payment_id
                        AND p.user_id = sr.user_id
                        AND supp.ep_no = sr.ep_no
                        AND ppd.payment_dtl_id = sr.payment_dtl_id
                        AND smp.record_status = 8
                        AND bill.bill_type = 'S'
                        AND smp.attempt >= 3");
        return $dataList;
    }
}
