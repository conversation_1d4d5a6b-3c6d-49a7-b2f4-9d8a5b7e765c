@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <!--region menu-->
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li class="active">
                    <a href="{{ url('/prod-support/rpt/report_001_byyear') }}"><i class="fa fa-list-alt"></i><PERSON><PERSON><PERSON></a>
                </li>
                <li>
                    <a href="{{ url('/prod-support/rpt/report_001_updatedform') }}"><i class="fa fa-cubes"></i>Kemaskini Data</a>
                </li>
                <li>
                    <a href="{{ url('/prod-support/rpt/report_001_statistic') }}"><i class="fa fa-area-chart"></i>Statistik</a>
                </li>
            </ul>
        </div>
    </div>  
    <div class="block-title epss-title-s1">
        <h6><i class="fa fa-users"></i> LAPORAN PERKHIDMATAN PERBENDAHARAAN DARIPADA BULAN JANUARI HINGGA DISEMBER (DALAM TALIAN)</h6>
    </div> 
    <form action="{{ url('/prod-support/rpt/report_001_byyear') }}" method="post">
        {{ csrf_field() }}

        <label class="col-md-1 text-left by_year_label" for="by_year">Year<span class="text-danger">*</span></label>
        <div class="col-md-2">
            <select id="by_year" name = "by_year" required class="form-control" style="width: 200px;">
                <option value="">Please Select</option>
                @foreach($listYear as  $list)
                <option value="{{$list->year}}">{{$list->year}}</option>
                @endforeach
            </select>
        </div>
        <div class="form-actions form-actions-button text-right ">
            <button type="submit" id="searchbutton" name="searchbutton" class="btn btn btn-primary" style="float: left;"><i class="fa fa-search"> Search</i></button>
        </div>
        <div class="form-actions form-actions-button text-right ">
            <button type="submit" id="downloadfromdb" name="downloadfromdb" class="btn btn btn-primary" style="float: right;"><i class="fa fa-download"> Download</i></button>
        </div>
    </form>
    <br>
    <table class="table table-vcenter table-condensed table-bordered">
        <thead>
            <tr style="background-color:#B4EBFF">
                <td class="text-center"><strong>BIL</strong></td>
                <td class="text-left"><strong>KATEGORI DALAM TALIAN</strong></td>
                <td class="text-center" ><strong>JAN</strong></td>
                <td class="text-center" ><strong>FEB</strong></td>
                <td class="text-center" ><strong>MAC</strong></td>
                <td class="text-center" ><strong>APR</strong></td>
                <td class="text-center" ><strong>MAY</strong></td>
                <td class="text-center" ><strong>JUN</strong></td>
                <td class="text-center" ><strong>JUL</strong></td>
                <td class="text-center" ><strong>AUG</strong></td>
                <td class="text-center" ><strong>SEP</strong></td>
                <td class="text-center" ><strong>OCT</strong></td>
                <td class="text-center" ><strong>NOV</strong></td>
                <td class="text-center" ><strong>DEC</strong></td>
            </tr>
        </thead>
        <tbody>
            @if($listDataForYear != null)
            @foreach($listDataForYear as $listdata)
            <tr>
                <td class="text-center">{{ $listdata->bil_no }}</td>
                <td class="text-center">{{ $listdata->service_name_bm }}</td>
                <td class="text-center">{{ $listdata->Jan }}</td>
                <td class="text-center">{{ $listdata->Feb }}</td>
                <td class="text-center">{{ $listdata->March }}</td>
                <td class="text-center">{{ $listdata->April }}</td>
                <td class="text-center">{{ $listdata->May }}</td>
                <td class="text-center">{{ $listdata->June }}</td>
                <td class="text-center">{{ $listdata->July }}</td>
                <td class="text-center">{{ $listdata->Aug }}</td>
                <td class="text-center">{{ $listdata->Sep }}</td>
                <td class="text-center">{{ $listdata->October }}</td>
                <td class="text-center">{{ $listdata->Nov }}</td>
                <td class="text-center">{{ $listdata->December }}</td>
            </tr>
            @endforeach
            @endif
        </tbody>
    </table>
</div>

@endsection
