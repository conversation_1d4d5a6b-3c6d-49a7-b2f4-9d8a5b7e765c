<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\InvoiceService;
use Guzzle;
use GuzzleHttp\Client;

class CallEpProcessFileIN {

    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        
        $callEpProcessFileIN = new CallEpProcessFileIN;
        
        /** List File Names **/
        
        $SERVICE_CODE = 'GFM-370';  // APOVE
        //$SERVICE_CODE = 'GFM-140';  // AP511
        
        $listFileNames = array(
            '1000APOVE4000012022010400075.GPG',
            );
        dump("TOTAL FOUND: ".count($listFileNames));
        $listTransId = DB::connection('oracle_nextgen_rpt')->table('di_interface_log')
                ->where('service_code',$SERVICE_CODE)
                ->whereNull('file_name')
                ->select('trans_id')
                ->take(20)
                ->get(); 
        dump(count($listTransId));
        $counter = 0;
        foreach ($listFileNames as $fileName){
            $serviceCode = $SERVICE_CODE;
            $transID = $listTransId[$counter]->trans_id;
            $counter++;
            dump($counter.')  >>> $transID : '.$transID. ' ,$serviceCode : '.$serviceCode. ' ,$fileName : '.$fileName);
            self::callWSCallBackEPProcessFileInFolder($transID, $serviceCode, $fileName);
            sleep(1);
        }

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }

     protected  static function callWSCallBackEPProcessFileInFolder($transID,$serviceCode,$fileName) {

        if(strlen($transID) == 0 || strlen($serviceCode) == 0 || strlen($fileName) == 0){
            return array('msg'=>'Invalid Parameter','state'=>false);
        };
        
        $baseUrl = env("BASE_URL_WS_BATCH_OSB","http://192.168.63.205:7012"); 
        //dump('$transID : '.$transID. ' ,$serviceCode : '.$serviceCode. ' ,$fileName : '.$fileName);
        
        $payload = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:bat='http://www.ep.gov.my/Schema/1-0/Batch' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>"
                    . "<x:Header/>"
                    . "<x:Body>"
                        . "<bat:EPMFRqArray>"
                            . "<bat:EPMFRq>"
                                . "<epm:RqHeader>"
                                    . "<epm:ConsumerID>EPP-001</epm:ConsumerID>"
                                    . "<epm:UID>"
                                        . "<epm:RqUID>$transID</epm:RqUID>"
                                    . "</epm:UID>"
                                . "</epm:RqHeader>"
                                . "<bat:BatchInboundRq>"
                                    . "<bat:ServiceCode>$serviceCode</bat:ServiceCode>"
                                    . "<bat:Filename>$fileName</bat:Filename>"
                               . " </bat:BatchInboundRq>"
                            . "</bat:EPMFRq>"
                        . "</bat:EPMFRqArray>"
                    . "</x:Body>"
                . "</x:Envelope>";
        dump($payload);
        $client = new Client([
            'base_uri' => $baseUrl,
        ]);

        $response = $client->post('/Batch/eP/Callback/v1.4', [
          //'debug' => TRUE,
          'body' => $payload,
          'headers' => [
              'Content-Type' => 'text/xml; charset=utf-8',
              'SOAPAction'   => 'http://www.ep.gov.my/Service/1-0/Batch/invoke',
          ]
        ]);
        
        $body = $response->getStatusCode();
        dump($body);

    }


}