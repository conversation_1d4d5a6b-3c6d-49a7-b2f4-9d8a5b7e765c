@extends('layouts.guest-dash')
@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-4 log-header-title">
            <span>List CT Agreement Cancellation<br /></span>
            <small>To View By Batch No</small>
        </div>
        <div class="col-md-8 log-header-menu">
            <a href="{{url('ep/ct/stat-ct-cancel-batch-no')}}"><span class="{{ Request::is('ep/ct/stat-ct-cancel-batch-no') ? 'active' : '' }}">Statistic By Batch No</span></a> |
            <a href="{{url('ep/ct/list-ct-cancel-agreement')}}"><span class="{{ Request::is('ep/ct/list-ct-cancel-agreement') ? 'active' : '' }}">List CT Agreement Cancellation</span></a> |
            <a href="{{url('ep/ct/list-ct-agreement-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-null') ? 'active' : '' }}">WIP List CT Agreement Null</span></a> |
            <br/><a href="{{url('ep/ct/list-ct-pending-amendment')}}"><span class="{{ Request::is('ep/ct/list-ct-pending-amendment') ? 'active' : '' }}">WIP List CT Pending Amendment</span></a> |   
            <a href="{{url('ep/ct/list-ct-agreement-not-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-not-null') ? 'active' : '' }}">WIP List CT Agreement Not Null</span></a> | 
            <a href="{{url('ep/ct/list-ct-agreement-missing')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-missing') ? 'active' : '' }}">WIP List CT Agreement Missing</span></a>   
        </div>
    </div>
</div>
@endsection
@section('content')
<div class="block">
    <form class="form-horizontal" id="carian-form" action="{{url('/ep/ct/stat-ct-cancel-batch-no')}}" method="get" >
        <div class="row">
            <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cari">Carian / Search (Batch No.) </label>
                        <div class="col-md-6">
                            <input type="text" id="cari" name="cari" class="form-control" value="{{$carian}}"  onfocus="this.select();" placeholder="Klik carian batch no. di sini ... ">
                        </div>
                    </div>
            </div>
        </div>
    </form>
</div>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> </h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              
              <p>Tiada rekod!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title">
            <h1><i class="fa fa-building-o"></i> <strong>Statistic CT Cancellation Agreement</strong></h1>
        </div>

        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">PROGRESS STATUS</th>
                        <th class="text-center">BATCH NO.</th>
                        <th class="text-center">DATE START</th>
                        <th class="text-center">DATE END</th>
                        <th class="text-center">TOTAL</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">{{ $data->progress_status }} - {{ App\Services\EPService::$CT_CANCELLATION_AGREEMENT_PROGRESS[$data->progress_status]}}</td>
                            <td class="text-center">
                            <a href="{{ url("/ep/ct/list-ct-cancel-agreement") }}?cari={{$data->batch_no}}" target="_blank" >
                            {{$data->batch_no }} </a>
                        </td>
                        <td class="text-center">{{$data->min_date }}</td>
                        <td class="text-center">{{ $data->max_date }}</td>
                        <td class="text-center">{{ $data->total }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


@endsection
