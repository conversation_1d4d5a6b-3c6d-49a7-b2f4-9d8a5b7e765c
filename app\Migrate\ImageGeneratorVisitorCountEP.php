<?php

namespace App\Migrate;

use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class ImageGeneratorVisitorCountEP
{
    public static function run()
    {
        try {
            $startTime = Carbon::now();
            $logMessage = 'Start ' . __FUNCTION__ . ' >> ';
            Log::info(__CLASS__ . ' ' . $logMessage . 'Starting ...');

            $data = self::getGoogleAnalyticsData();
            Log::info(__CLASS__ . ' ' . __FUNCTION__ . ' Google Analytics data:', ['data' => $data]);

            $takenTime = MigrateUtils::getTakenTime($startTime);
            Log::info(__CLASS__ . ' ' . __FUNCTION__ . ' Completed --- Taken Time :', ['Time' => $takenTime]);
        } catch (\Exception $e) {
            Log::error(__CLASS__ . ' ' . __FUNCTION__ . ' Error: ' . $e->getMessage());
        }
    }

    public static function getGoogleAnalyticsData()
    {
        try {
            chdir(base_path('app/Scripts'));

            $pythonScriptPath = base_path('app/Scripts/GAePVisitorCountImgGenerator.py');

            if (!file_exists($pythonScriptPath)) {
                throw new \Exception('Python script file not found.');
            }

            $publicPath = public_path('img');
            $command = escapeshellcmd("python3 {$pythonScriptPath} {$publicPath} 2>&1");

            $output = shell_exec($command);

            if ($output === null) {
                throw new \Exception('Failed to execute the Python script.');
            }

            $data = json_decode($output, true);

            if ($data === null && json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Error decoding JSON data.');
            }

            return $data;
        } catch (\Exception $e) {
            Log::error(__CLASS__ . ' ' . __FUNCTION__ . ' Error: ' . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }
}
