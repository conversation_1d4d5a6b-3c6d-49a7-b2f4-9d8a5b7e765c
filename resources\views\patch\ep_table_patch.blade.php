@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<style>
    code[class^="language-"], code[class*=" language-"], pre[class^="language-"], pre[class*=" language-"] {
        white-space: pre-line;
    }
</style>
<div class="row">
    <div class="col-md-6">
        <div class="block">
            <div class="block-title">
                <h2><strong>Patch Data</strong> eP Table (Allowed)</h2>
                <small class="text-primary">connect to :  {{env('DB_NEXTGEN_FULLGRANT_DATABASE')}} / {{env('DB_NEXTGEN_SOA_FULLGRANT_DATABASE')}}</small>
                <div class="block-options pull-right action-today">
                    <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                       data-toggle="modal" data-url="{{url('/support/report/log/patch-table-ep')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                       data-title="List Action Patch Data Today ">View Today Action</a>
                </div>
            </div>

            @if(isset($result_status) && $result_status == 'success')
            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> Success</h4> {{$result_desc}}</a>
            </div>
            @elseif(isset($result_status) && $result_status == 'failed')
            <div class="alert alert-danger">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-exclamation-triangle"></i> Failed</h4> <span class="alert-link">{{$result_desc}}</span>
            </div>
            @endif
            <ul>
                <ol>
                    <span class="text-primary"><strong>Segala aktiviti kemaskini yang dilakukan akan disimpan. Sila pastikan anda meletakkan 
                            sebab data patching di ruangan 'Remarks'. Masukkan juga Case Number. </strong></span>
                </ol>
            </ul>    
            @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
            @endif
            <form id="form-search" action="{{url("/find/patch-ep")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST">
                <span id="list_table_ep" class="hide" >{{json_encode(App\Services\EPTableService::$LIST_TABLE)}}</span>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="selected_process_table">Patching <span class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <div class="input-group">
                            <select id="selected_process_table" name="selected_process_table" class="form-control">
                                <option value="">Please select</option>
                                @foreach(App\Services\EPTableService::$LIST_TABLE as  $key => $obj)
                                <option value="{{$key}}" @if(old('selected_process_table') == $key || $selected_process_table == $key ) selected @endif>{{$obj['table_name']}}</option>
                                @endforeach
                            </select>
                            <span class="input-group-addon"><i class="gi gi-sampler"></i></span>
                        </div>

                    </div>
                </div>
                <div class="block" id="panel_selected_process_table_json_desc" style="display:none;">
                    <div class="block-title">
                        <h2><strong>Update Fields</strong> <small>Description</small></h2>
                        <div class="block-options pull-right action-today">
                            <a href="#pre_json_desc" data-toggle="collapse" class="btn btn-alt btn-default"><i class="fa fa-angle-down"></i></a>
                        </div>
                    </div>

                    <pre id="pre_json_desc" class="" class="collapse show">
                         <code style="float: left; color:#fff;" id="selected_process_table_json_desc"></code>
                    </pre>
                </div>
                <div class="form-group">
                    <label class="col-md-2 control-label" for="record_id">Record ID<span class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <div class="input-group">
                            <input type="text" id="record_id" name="record_id" class="form-control" value="{{$record_id}}" >
                            <span id="view_record_button"  class="input-group-addon" style="cursor:pointer;"><i class="fa fa-search"></i> Search</span>
                        </div>
                        
                    </div>
                </div>
                <div class="form-group" id="panel_selected_field"  style="display:none;">
                    <label class="col-md-2 control-label" for="selected_field">Choose Field <span class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <div class="input-group">
                            <select id="selected_field" name="selected_field" class="form-control">
                                <option value="">Please select</option>
                            </select>
                            <span class="input-group-addon"><i class="fa fa-bars"></i></span>
                        </div>
                    </div>
                </div>
                <div class="form-group" id="panel_field_value"  style="display:none;">
                    <label class="col-md-2 control-label" for="field_value">Value Field<span class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <input type="text" id="field_value" name="field_value" class="form-control" >
                    </div>
                </div>
                <div class="form-group" id="panel_remark" style="display:none;">
                    <label class="col-md-2 control-label" for="remarks">Remarks / Case Number <span class="text-danger">*</span></label>
                    <div class="col-md-9">
                        <input type="text" id="remarks" name="remarks" class="form-control" >
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-6 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default" style="display:none" id="submit_record"><i class="fa fa-pencil-square-o"></i> Patch Update Record</button>
                  
                    </div>
                </div>
                
            </form>
        </div>


    </div>
    <div class="col-md-6">
        <div id="response" class="table-options clearfix display-none">
            <h4 class='text-center text-light' id="response-msg-h4"></h4>
            <pre>
                <code style="float: left; color:#fff;" id="response-msg"></code> 
            </pre>
        </div>
        <div class="block block-alt-noborder full">
            <div class="block-title">
                <h2><strong>Details Record </a></h2>
                <div class="block-options pull-right trigger-delete-div">
                    <a href="#modal_confirm_trigger" data-toggle="modal"
                     class="btn btn-sm btn-danger" style="display:none;" 
                     id='div-allow-delete' ><i class="fa fa-trash-o fa-fw"></i> Delete  Record</a>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-12">
                    <div class="block">
                        <table  class="table table-borderless table-striped table-vcenter">
                            <tbody id="info-table-record">
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div> 
    </div>
</div>    

<div id="modal_confirm_trigger" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> Are you sure want to delete this record? </h2>
                <span>This record will permenantly delete. It will not recover. </span>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form  action="{{url("/find/ep/table/del")}}" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="table_name_del">Table Name</label>
                                <div class="col-md-9">
                                    <input type="text" id="table_name_del" name="table_name_del" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-md-2 control-label" for="record_id_del">Record ID</label>
                                <div class="col-md-9">
                                    <input type="text" id="record_id_del" name="record_id_del" class="form-control" readonly>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_confirm_delete"><i class="gi gi-ok_2"></i> Yes</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div>
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
@include('_shared._modalListLogAction')

@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
                    ModalListActionLogDatatable.init();
                });</script>
<script>
    var APP_URL = {!! json_encode(url('/')) !!}
    var LIST_TABLE_EP = JSON.parse($("#list_table_ep").text());
    $('#modal_confirm_trigger').modal('hide');
    $('#wait-modal').hide();

    $("#selected_process_table").bind("change", function () {
        $('#panel_selected_process_table_json_desc').hide();
        var processId = $(this).find(":selected").val();
        //console.log('processId',processId);
        if (processId.length > 0) {
            var processObj = LIST_TABLE_EP[processId];
            var data = JSON.stringify(processObj.field_allow, null, 4);
            
            
            $('#selected_process_table_json_desc').text(data);
            $('#panel_selected_process_table_json_desc').show();
            
            
        }

        if (processId === 'user_set_active') {
            $('#panel_user_role_form').show();
        } else {
            $('#panel_user_role_form').hide();
        }
        
        $("#panel_selected_field").hide();
        $("#selected_field").find('option').not(':first').remove();
        
        $("#panel_field_value").hide();
        $("#record_id").val('');
        
        $("#panel_remark").hide();
        $("#remarks").val('');
        
        
        
    });

    $('div.form-group').on("click", '#view_record_button', function () {
        //console.log('trigger get data');
        $('#div-allow-delete').hide();
        $("#submit_record").hide();
        $("#panel_selected_field").hide();
        $("#panel_field_value").hide();
                
        $("#info-table-record").empty();
        var loading = "<div class=text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>";
        $("#info-table-record").append(loading);

        var table_name = $("#selected_process_table").val();
        var record_id = $("#record_id").val();

        var processObj = LIST_TABLE_EP[table_name];
        var record_field = processObj.primary_field;
        var csrf = $("input[name=_token]").val();

        var isAllowDelete = processObj.is_allow_delete;
        
        $.ajax({
            url: APP_URL + "/find/ep/table",
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "table_name": table_name, "record_id": record_id, "record_field": record_field},
            context: document.body
        }).done(function (resp) {
            //console.log(resp);
            $("#info-table-record").empty();
            
            if ($.isEmptyObject(resp) === true) {
                var fieldData = "<tr><td class='text-center'><strong>No records found</strong></td></tr>";
                $("#info-table-record").append(fieldData);
            }else{
                console.log("is_allow_delete : "+isAllowDelete+" for table "+table_name+" with id "+record_id);
                if(isAllowDelete === true){
                    $('#div-allow-delete').show();
                }

                $.each(resp, function (key, value) {
                    //console.log('Key: ' + key + " , value: " + value);
                    if ($.isPlainObject(value) === true) {
                        $counter = key + 1;
                        var recordData = "<tr><td colspan='2' class='text-right' style='width: 50%; background-color:#3426b5; color:#fff;'><strong>SHOWING RECORD : "+$counter+" </td></tr>";
                        $("#info-table-record").append(recordData);
                        $.each(value, function (key2, value2) {
                            var fieldData2 = "<tr><td class='text-right' style='width: 50%;'><strong>" + key2 + " </strong> : </td><td>" + value2 + "</td></tr>";
                            $("#info-table-record").append(fieldData2);
                        });
                    }else{
                        var fieldData = "<tr><td class='text-right' style='width: 50%;'><strong>" + key + " </strong> : </td><td>" + value + "</td></tr>";
                        $("#info-table-record").append(fieldData);
                    }
                });
                    
                
                $("#submit_record").show();
                
                
                var processId = $("#selected_process_table").find(":selected").val();

                if (processId.length > 0) {
                    var processObj = LIST_TABLE_EP[processId];
                    var fieldsAllowed = processObj.field_allow;
                    //console.log(fieldsAllowed);
                    
                    $("#selected_field").find('option').not(':first').remove();
                    $.each( fieldsAllowed, function( key, obj ) {
                        console.log( key );
                        
                        $descriptionOpt = "FIELD : "+key+ " >>>  "+obj.validation_type+ "("+obj.validation_length+") ";
                        var o = new Option($descriptionOpt, key);
                        $("#selected_field").append(o);
                        
                      

                    });

                }
        
        
                $("#panel_selected_field").show();
                $("#panel_field_value").show();
                $("#panel_remark").show();
            }
            
        });
    });

    $('div.trigger-delete-div').on("click", '#div-allow-delete', function () {
        var table_name = $("#selected_process_table").val();
        var record_id = $("#record_id").val();
        $("#table_name_del").val(table_name);
        $("#record_id_del").val(record_id);
    });

    $('div.form-actions').on("click",'button.action_confirm_delete', function(){
            var table_name = $("#table_name_del").val();
            var record_id = $("#record_id_del").val();
            var csrf = $("input[name=_token]").val();   

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: "/find/ep/table/del",
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"table_name":table_name,"record_id":record_id},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg-h4").html("Successfully Deleted!");
                    $("#response-msg").html(resp.data);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg-h4").html("Failed Delete!");
                    $("#response-msg").html(resp.data);
                    $('#wait-modal').modal('hide');
                }
            });
        });
</script>
@endsection