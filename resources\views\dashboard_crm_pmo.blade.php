@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
    <div class="content-header">
        <div class='text-center' style="padding: 8px;">  
            <div class='text-center' style="padding: 8px;">  
        <div class='text-center' style="padding: 8px;">  
            <button type="submit" id="button_submit_before" name="button_submit_before" class="btn btn-sm btn-info text-center active" value="before">
                <div class="h5 mb-0" style="font-weight: 800">BEFORE</div>
                    <span>PMO Tasks - Before Approval</span>
            </button>
            <button type="submit" id="button_submit_after" name="button_submit_after" class="btn btn-sm btn-info text-center" value="after"> 
                <div class="h5 mb-0" style="font-weight: 800">AFTER</div>
                    <span>PMO Tasks - After Approval</span>
            </button>
            </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class='widget'>
                <div class='widget-extra themed-background-dark center-block'>
                    <h5 class='widget-content-light'>
                        Incident Management - <strong>PMO Ageing Pending Cases</strong>  | <font id='request_type'>Before Approval</font>    
                    </h5>
                </div>
                <div class="text-center spinner-loading" style="padding: 20px;">
                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                </div>
                <div class="table-responsive">
                    <div id="ageing-stats-filter"></div>
                    {{-- <button data-id="" class="btn-module btn btn-sm btn-secondary"><strong>All</strong></button>
                    <button data-id="Direct Purchase" class="btn-module btn btn-sm btn-secondary"><strong>Direct Purchase</strong></button> --}}
                    <table id="basic-datatable-ageing" class="table table-striped table-vcenter"></table>
                </div>
            </div>
        </div>
    </div>
     
    
@endif

<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>
    var APP_URL = {!! json_encode(url('/')) !!};
    var tableListAgeingData; // Declare here, initialize in getData

    App.datatables();
    /* Initialize Datatables */
    tableListAgeingData = $('#basic-datatable-ageing').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    function getData(reqType) {
        $('.spinner-loading').show();
        $('.table-responsive').hide(); // Hide the container of the table

        // Destroy existing DataTable instance if it exists
        if ($.fn.DataTable.isDataTable('#basic-datatable-ageing')) {
            $('#basic-datatable-ageing').DataTable().destroy();
        }
        
        $('#basic-datatable-ageing').empty(); 

        $.ajax({
            url: APP_URL + '/dashboard/crm/incident/pmo/ageing',
            type: "GET",
            data: {"type": reqType},
            success: function (response) { // Changed 'data' to 'response' for clarity
                // 'response' is already a JavaScript object, no need for $(response)
                if (response && response.ageing_table && response.filter) {
                    $('#basic-datatable-ageing').html(response.ageing_table); // Inject new table HTML
                    $('#ageing-stats-filter').html(response.filter).fadeIn();
                    
                    // Initialize DataTables on the new HTML
                    tableListAgeingData = $('#basic-datatable-ageing').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}], // Assuming 0 is your first column (e.g., checkbox)
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']],
                    });
                    
                    $('#select-module').off('change').on('change', function() { // Use .off('change') to prevent multiple bindings
                        tableListAgeingData.columns(6).search(this.value).draw();
                    });

                    $('#select-severity').off('change').on('change', function() { // Use .off('change')
                        tableListAgeingData.columns(7).search(this.value).draw();
                    });

                } else {
                    console.error("AJAX success but data is not in the expected format:", response);
                    $('#basic-datatable-ageing').html('<tr><td colspan="8" class="text-center"><strong>Error: Unable to retrieve data. Please try again later.</strong></td></tr>');
                }

                $('.spinner-loading').hide();
                $('.table-responsive').show(); // Show the container now that the table is ready
            },
            error: function (jqXHR, textStatus, errorThrown) {
                console.error("AJAX Error:", textStatus, errorThrown);
                console.error("Response Text:", jqXHR.responseText);
                $('.spinner-loading').hide();
                $('.table-responsive').show(); // Or show an error message container
                $('#basic-datatable-ageing').html('<tr><td colspan="8" class="text-center"><strong>Unable to retrieve data. Please try again later.</strong></td></tr>');
            }
        });
    }

    $(document).ready(function () {  
        // Initial data load
        getData('before'); 
         
        $('#button_submit_before').click(function() {
            var reqType = $(this).val();
            getData(reqType); // getData now handles destroy and re-init
            $('#request_type').text('Before Approval');
            $('#button_submit_after').removeClass('active');
            $('#button_submit_before').addClass('active');
        });

        $('#button_submit_after').click(function() {
            var reqType = $(this).val();
            getData(reqType); // getData now handles destroy and re-init
            $('#request_type').text('After Approval');
            $('#button_submit_before').removeClass('active');
            $('#button_submit_after').addClass('active');
        });
    });

</script>
<script>
    $('#page-container').removeAttr('class'); 
</script>
@endsection
