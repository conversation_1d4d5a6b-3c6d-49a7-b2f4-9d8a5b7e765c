.popover {
  max-width: 500px;
}

#page-content {
  background: #ecedec;
}

.block-alt-noborder {
  background: #a8e0d1;
}


div.log-header {
  background: -webkit-linear-gradient(-180deg, #057d6d, #0e0f0f);
  background: -o-linear-gradient(-180deg, #057d6d, #0e0f0f);
  background: -moz-linear-gradient(-180deg, #057d6d, #0e0f0f);
  background: linear-gradient(-180deg, #057d6d, #0e0f0f);
  display: inline-block;
  box-shadow: 0 0 15px 1px rgba(0, 0, 0, 0.35);
  overflow: hidden;
  border-radius: 5px;
  border-style: none;
  width: 100%;
  color: white;
  padding: 5px 10px;
}

.center-container {
  display: flex;
  justify-content: center;
  align-items: center;
  
}

.input-group {
  display: flex;
  width: 300px; 
}

.form-control {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  flex: 1; 
}

.btn-primary {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  margin-left: -1px; 
}

div button.btn,
a.btn {
  background: rgb(13, 151, 114);
  color: #f7f5f5;
}

div button.btn:hover,
a.btn:hover {
  background: rgb(1, 85, 67);
  color: #fff;
}

table.dataTable thead {
  background-color: #2d442d; 
  color: white;  
}

table.dataTable tbody tr {
  background-color: #c0eccf;  
  font-size: 16px;
  font-family: 'Poppins', sans-serif;
}

table.dataTable tbody tr:hover {
  background-color: #ddd; 
}

table.dataTable {
  border: 2px solid #080808; 
  border-collapse: collapse;   
}

table.dataTable th {
  border: 1px solid #080808;  
  padding: 8px;               
  text-align: left;        
}

table.dataTable td {
  border: 1px solid #080808;  
  padding: 8px;             
  text-align: left;         
}

table.dataTable tbody td {
  color: #333;  
}




