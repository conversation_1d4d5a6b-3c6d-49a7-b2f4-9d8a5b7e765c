<?php

/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/20/2018
 * Time: 10:05 AM
 */

namespace App\Http\Controllers;

use App\Services\EPService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\SourcingService;
use App\Services\Traits\BPMService;
use App\Services\Traits\BpmApiService;
use Carbon\Carbon;
use Config;
use Log;
use Mail;
use DB;
use App\Services\Traits\SupplierFullGrantService;
use App\Services\Traits\Qt\QuotationDocumentService;
use App\EpSupportActionLog;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Validator;
use Guzzle;
use Excel;

class QtController extends Controller
{

    use SupplierService;

    use SourcingService;

    use BPMService;

    use SupplierFullGrantService;

    use QuotationDocumentService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function dashboard()
    {
        return view('qt.dashboard_qt');
    }

    public function extendQt()
    {
        return view('qt.extend_qt');
    }

    public function searchPage()
    {
        return view('list_qt_qtno', [
            'qtinfo' => null,
            'listdata' => null,
            'carian' => '',
            'result' => '',
            'type' => 'qtno'
        ]);
    }

    public function getQuatationTenderInfo()
    {
        $docNo = request()->doc_no;
        $detailQt = null;
        if ($docNo && $docNo != null) {
            $listDetailQt = $this->getDetailQuatationTenderInfo($docNo);
            if (count($listDetailQt) > 0) {
                $detailQt = $listDetailQt[0];
            }
        }
        return view('sourcing.info_qt', [
            'qtinfo' => $detailQt,
            'carian' => request()->doc_no
        ]);
    }

    public function getSupplierAttendanceByQtNo($qtNo)
    {
        $qtInfo = $this->getScQt($qtNo);
        if ($qtInfo) {
            $list = $this->checkQTSupplierAttendance($qtNo);
            $qtInfo->bsv_date = '-';
            if (count($list) > 0) {
                $qtInfo->bsv_date = $list[0]->bsv_date;
                foreach ($list as $data) {
                    $data->is_authorized = EPService::$IS_CHECKED[$data->is_authorized];
                    $data->is_pre_registered = EPService::$IS_CHECKED[$data->is_pre_registered];
                    $data->is_attended = EPService::$IS_CHECKED[$data->is_attended];
                    $data->is_post_registered = EPService::$IS_CHECKED[$data->is_post_registered];
                    $data->record_status = EPService::$QT_BSV_REGISTRATION_STATUS[$data->record_status];

                    $data->approval_status = '-';
                    if ($data->qt_approval_request_id != null) {
                        //dd($data->qt_approval_request_id);
                        $approval = $this->checkQTApproveRequestDetail($data->qt_approval_request_id);
                        $status = $this->getParameterDesc($approval->approver_action_id);
                        $data->approval_status = $status->code_desc;
                    }

                    $qualify = $this->checkDisqualifiedStage($qtNo, $data->supplier_id);
                    if ($qualify) {
                        $qualify->disqualified_stage = EPService::$QT_QUALIFY[$qualify->disqualified_stage];
                        $data->qualify = $qualify;
                    }
                }
            }
            return view('list_qt_qtno', [
                'qtinfo' => $qtInfo,
                'listdata' => $list,
                'carian' => $qtNo,
                'type' => 'qtno'
            ]);
        }
        return view('list_qt_qtno', [
            'qtinfo' => null,
            'listdata' => null,
            'carian' => $qtNo,
            'result' => 'notfound',
            'type' => 'qtno'
        ]);
    }

    public function getQtDetails($supplierId, $qtNo, $qtBsvAttendanceId)
    {
        $qtDetail = $this->checkQTApproveRequest($supplierId, $qtNo);
        $qtDetail->approval_status = '-';
        $qtDetail->approval_request_type_desc = '-';
        $qtDetail->approval_request_type_id = null;
        $qtAppReqId = $qtDetail->qt_approval_request_id;
        if ($qtAppReqId) {
            $qtApprovalReq = $this->checkQTApproveRequestDetail($qtAppReqId);
            if ($qtApprovalReq) {
                $qtDetail->approvalReqDetail = $qtApprovalReq;
                $qtDetail->approval_status = $qtApprovalReq->approval_status;
                $param = $this->getParameterDesc($qtApprovalReq->approval_request_type_id);
                if ($param) {
                    $qtDetail->approval_request_type_id = $qtApprovalReq->approval_request_type_id;
                    $qtDetail->approval_request_type_desc = $param->code_desc;
                }
            }
        }

        $qtBsvAttendance = $this->getQtBsvAttendance($qtBsvAttendanceId);
        if ($qtBsvAttendance) {
            $qtDetail->qt_bsv_attendance = $qtBsvAttendance;
        }

        $supplier = $this->getSupplierDetail($supplierId);
        if ($supplier) {
            $qtDetail->supplier = $supplier;
        }

        return view('include.qt_more_info', [
            'qtDetail' => $qtDetail
        ]);
    }

    public function searchTaklimatOrLawatanTapakByQuotationTender($carian = null)
    {
        $carianTemp = trim(strtoupper($carian));
        $qtInfo = null;
        $list = null;
        if ($carianTemp != null) {
            $qtInfo = $this->getScQt($carian);
            if ($qtInfo) {
                $list = $this->getListTaklimatOrLawatanTapakByQuotationTender($carian);
            }
        }

        return view('list_qt_lawatan', [
            'qtinfo' => $qtInfo,
            'listdata' => $list,
            'carian' => $carian
        ]);
    }

    public function searchProposalSupplierByQuotationTender($carian = null)
    {
        $carianTemp = trim(strtoupper($carian));
        if ($carianTemp == null) {
            $list = null;
        } else {
            $list = $this->getListProposalSupplierByQuotationTender($carian);
        }

        return view('list_qt_proposal', [
            'listdata' => $list,
            'carian' => $carianTemp
        ]);
    }

    public function searchQTCommitteMembersByQuotationTender($carian = null)
    {
        $carianTemp = trim(strtoupper($carian));
        if ($carianTemp == null) {
            $list = null;
        } else {
            $list = $this->getListAllCommitteeMembersQT($carian);
        }

        return view('list_qt_committees', [
            'listdata' => $list,
            'carian' => $carianTemp
        ]);
    }

    public function searchListStuctTaskQt()
    {
        $list = $this->getListStuctTaskQt();
        foreach ($list as $obj) {
            $qtNo = $obj->qt_no;
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $task = $this->getTaskDetailBpmByDocNo($qtNo);
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id =  $task->flow_id;
            }
        }
        return view('list_qt_stuck', ['listdata' => $list]);
    }

    public function showQTCountByDate($qtDate)
    {
        if ($qtDate && $qtDate != null) {
            $qtCount = $this->getQtCountByDate($qtDate);
            if ($qtCount > 0) {
                return $qtCount;
            }
        }
    }

    public function showQTListByDate($qtDate)
    {
        if ($qtDate && $qtDate != null) {
            $listDetailQt = $this->getQtListByDate($qtDate);
            return $listDetailQt;
        }
    }

    public function actionExtend()
    {
        $isSuccessSave = false;
        $actionTypeLog = 'Script';
        $actionName = 'ExtendQTClosingDate';
        $logs = collect([]);
        $parameters = collect([]);

        Validator::make(request()->all(), [
            'extend_from_date' => 'required',
            'extend_to_date' => 'required',
            "extend_remarks" => 'required',
        ])->validate();

        $qtIds = array();
        $extendFromDate = Carbon::parse(request()->extend_from_date);
        $extendToDate = Carbon::parse(request()->extend_to_date);

        $parameters->put("remarks", request()->extend_remarks);

        $listDetailQt = $this->getQtListByDate($extendFromDate->format('d-m-Y'));

        foreach ($listDetailQt as $qt) {
            array_push($qtIds, $qt->qt_id);
        }

        if ($extendFromDate && sizeof($qtIds) > 0) {
            try {
                $patchingProcess = "Extend QT Closing Date From " . $extendFromDate->format('d-m-Y') . " To " . $extendToDate;

                $updateFields = ['closing_date' => $extendToDate, 'changed_date' => Carbon::now(), 'changed_by' => 1];
                $parameters->put("patching", $patchingProcess);

                //If failed, it will rollback
                DB::connection('oracle_nextgen_fullgrant')->transaction(function () use ($updateFields, $qtIds, &$logs, &$isSuccessSave) {

                    $this->saveTransactionScQt($updateFields, $qtIds, $logs);
                    $isSuccessSave = true;
                });
                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $logs, $parameters, ($isSuccessSave == true) ? 'Completed' : 'Failed');

                if ($isSuccessSave) {
                    $qtFromList = $this->showQTListByDate($extendFromDate->format('d-m-Y'));
                    $qtToList = $this->showQTListByDate($extendToDate->format('d-m-Y'));

                    $qtCounts = (object) array();
                    $qtCounts->qt_count_from = count($qtFromList);
                    $qtCounts->qt_count_to = count($qtToList);
                    $qtCounts->qt_total = count($qtFromList) + count($qtToList);

                    $dataEmail = [
                        "date_start" => $extendFromDate->format('d/m/Y'),
                        "date_end" => $extendToDate->format('d/m/Y'),
                        "qtFromList" => $qtFromList,
                        "qtToList" => $qtToList,
                        "qtCount" => $qtCounts,
                        "email_subject" => 'QT Successfully Extended From Date ' . $extendFromDate->format('d/m/Y') . ' to ' . $extendToDate->format('d/m/Y'),
                    ];

                    //SEND EMAIL
                    $this->sendEmail($dataEmail);

                    return array(
                        'status' => 'success',
                        //'updated_row' => $updateQt
                    );
                } else {
                    return array(
                        'status' => 'failed',
                        'err_msg' => 'No record updated.'
                    );
                }
            } catch (\Illuminate\Database\QueryException $ex) {
                Log::error(self::class . ' Error to update -> ' . $ex->getMessage());

                return array(
                    'status' => 'failed',
                    'err_msg' => $ex->getMessage()
                );
            }
        } else {
            return array(
                'status' => 'failed',
                'err_msg' => 'No record updated.'
            );
        }
    }

    public function saveTransactionScQt($updateFields, $qtIds, &$logs)
    {
        $logQuery = $this->updateScQt($qtIds, $updateFields);
        $logs->put('action_sc_qt', $logQuery);
    }

    /**
     * Send email once qt successfully extended
     */
    function sendEmail($dataEmail)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > ' . $dataEmail['email_subject']
        );
        try {
            Mail::send('emails.extendQTMail', $dataEmail, function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    public function getQtAcceptHistoryList(Request $request) {
        $carian = $request->get('doc_no', '');
        $rawQtInfo = $this->getQtAcceptHistoryListService($carian);
        $qtinfo = (function($result) {
            if ($result->isEmpty()) {
                return $result;
            }
            // Get unique first assignee values for supplier lookup
            $assigneeValues = $result->map(function ($item) {
                if (!empty($item->assignees)) {
                    $assigneeArray = explode(',', $item->assignees);
                    return trim($assigneeArray[0]);
                }
                return null;
            })->unique()->filter()->values()->toArray();
            // Get unique updatedby values for fallback supplier lookup
            $updatedByValues = $result->pluck('updatedby')->unique()->filter()->values()->toArray();
            // Get supplier data for both assignees and updatedby values
            $assigneeSupplierData = [];
            $updatedBySupplierData = [];
            if (!empty($assigneeValues)) {
                $assigneeSupplierResults = $this->getSupplierData($assigneeValues);
                foreach ($assigneeSupplierResults as $row) {
                    $assigneeSupplierData[$row->login_id] = $row->company_name;
                }
            }
            if (!empty($updatedByValues)) {
                $updatedBySupplierResults = $this->getSupplierData($updatedByValues);
                foreach ($updatedBySupplierResults as $row) {
                    $updatedBySupplierData[$row->login_id] = $row->company_name;
                }
            }
            // Process assignees using anonymous function
            $processAssignees = function($assigneesString) {
                if (empty($assigneesString)) {
                    return [];
                }
                $assigneesList = array_map('trim', explode(',', $assigneesString));
                $processedAssignees = [];
                foreach($assigneesList as $assignee) {
                    // Skip if value is exactly "user"
                    if (strtolower($assignee) === 'user') {
                        continue;
                    }
                    // Remove "user:" prefix if present
                    $cleanAssignee = $assignee;
                    if (stripos($assignee, 'user:') === 0) {
                        $cleanAssignee = substr($assignee, 5); // Remove "user:" (5 characters)
                    }
                    // Only add if not empty after processing
                    if (!empty(trim($cleanAssignee))) {
                        $processedAssignees[] = trim($cleanAssignee);
                    }
                }
                return $processedAssignees;
            };
            // Merge supplier data with main results, prioritizing assignee data
            return $result->map(function ($item) use ($assigneeSupplierData, $updatedBySupplierData, $processAssignees) {
                $companyName = null;
                // First priority: try to get company name from first assignee
                if (!empty($item->assignees)) {
                    $assigneeArray = explode(',', $item->assignees);
                    $firstAssignee = trim($assigneeArray[0]);
                    $companyName = $assigneeSupplierData[$firstAssignee] ?? null;
                }
                // Fallback: if no company name from assignee, try updatedby
                if ($companyName === null && !empty($item->updatedby)) {
                    $companyName = $updatedBySupplierData[$item->updatedby] ?? null;
                }
                $item->company_name = $companyName;
                // Process assignees for display
                $item->processed_assignees = $processAssignees($item->assignees);
                return $item;
            });
        })($rawQtInfo);

        // Create summary data grouped by composite instance id
        $summaryData = [];
        if (!empty($carian) && $qtinfo->isNotEmpty()) {
            $grouped = $qtinfo->groupBy('compositeinstanceid');
            foreach ($grouped as $compositeId => $records) {
                $taskOutcomes = [];
                $distinctTaskNumbers = $records->pluck('tasknumber')->unique();
                
                foreach ($distinctTaskNumbers as $taskNumber) {
                    // Get all records for this task number sorted by version (latest first)
                    $taskRecords = $records->where('tasknumber', $taskNumber)
                                        ->sortByDesc('version');
                    
                    $outcome = null;
                    // Iterate through versions to find first non-empty outcome
                    foreach ($taskRecords as $taskRecord) {
                        if (!empty($taskRecord->outcome)) {
                            $outcome = $taskRecord->outcome;
                            break;
                        }
                    }
                    
                    $taskOutcomes[$taskNumber] = $outcome;
                }
                
                $summaryData[] = [
                    'composite_instance_id' => $compositeId,
                    'task_outcomes' => $taskOutcomes,
                    'doc_no' => $records->first()->customattributestring1,
                    'expiry_date' => $records->first()->expirationdate,
                    'total_tasks' => count($taskOutcomes)
                ];
            }
        };

        // Calculate supplier statistics for each composite instance
        $compositeInstanceStats = [];
        if (!empty($carian) && $qtinfo->isNotEmpty()) {
            $groupedByComposite = $qtinfo->groupBy('compositeinstanceid');
            
            foreach ($groupedByComposite as $compositeId => $records) {
                $acceptedCount = 0;
                $rejectedCount = 0;
                $notRespondedCount = 0;
                
                // Group by supplier (company_name) within this composite instance
                $supplierGroups = $records->groupBy('company_name');
                
                foreach ($supplierGroups as $companyName => $supplierTasks) {
                    // Skip if company name is null or empty
                    if (empty($companyName) || $companyName === '-') {
                        continue;
                    }
                    
                    // Check supplier's response status
                    $hasAccept = $supplierTasks->contains(function ($task) {
                        return !empty($task->outcome) && 
                            strtoupper($task->outcome) === 'ACCEPT';
                    });
                    
                    $hasReject = $supplierTasks->contains(function ($task) {
                        return !empty($task->outcome) && 
                            strtoupper($task->outcome) === 'REJECT';
                    });
                    
                    // Prioritize ACCEPT over REJECT if both exist
                    if ($hasAccept) {
                        $acceptedCount++;
                    } elseif ($hasReject) {
                        $rejectedCount++;
                    } else {
                        $notRespondedCount++;
                    }
                }
                
                // Total unique suppliers (excluding empty company names)
                $uniqueSuppliersCount = $supplierGroups->keys()->filter(function($companyName) {
                    return !empty($companyName) && $companyName !== '-';
                })->count();
                
                $compositeInstanceStats[] = [
                    'composite_instance_id' => $compositeId,
                    'doc_no' => $records->first()->customattributestring1,
                    'unique_suppliers_count' => $uniqueSuppliersCount,
                    'suppliers_accepted_count' => $acceptedCount,
                    'suppliers_rejected_count' => $rejectedCount,
                    'suppliers_not_responded_count' => $notRespondedCount,
                    'expiry_date' => $records->first()->expirationdate
                ];
            }
        }

        $viewData = (function($qtinfo, $carian) {
            return [
                'carian' => $carian,
                'qtinfo' => $qtinfo,
            ];
        })($qtinfo, $carian);

        // Add summary data and composite instance stats to view data
        $viewData['summary_data'] = $summaryData;
        $viewData['composite_instance_stats'] = $compositeInstanceStats;

        return view('sourcing.info_qt_accept_history', $viewData);
    }

    public function exportAcceptHistory(Request $request)
    {
        $docNo = $request->get('doc_no');
        
        // Check if QT No is provided
        if (empty($docNo)) {
            return response()->json([
                'error' => 'QT Number is required for export'
            ], 400);
        }
        
        try {
            $results = $this->getQtAcceptHistoryData($docNo);
            
            // Check if any results found
            if (empty($results)) {
                return response()->json([
                    'error' => 'No data found for QT Number: ' . $docNo
                ], 404);
            }
            
            // Convert to CSV
            $csvData = $this->convertToCSV($results);
            
            // Generate filename with QT number
            $filename = 'QT_Accept_History_' . $docNo . '_' . date('Y-m-d_H-i-s') . '.csv';
            
            return response($csvData)
                ->header('Content-Type', 'text/csv')
                ->header('Content-Disposition', 'attachment; filename="' . $filename . '"');
                
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Error exporting data: ' . $e->getMessage()
            ], 500);
        }
    }

    private function convertToCSV($data)
    {
        if (empty($data)) {
            return "No data available\n";
        }
        
        $csvData = '';
        
        // Add headers
        $headers = [
            'MOF No',
            'Supplier Name', 
            'Tel No',
            'Personnel Name',
            'Designation',
            'Email'
        ];
        $csvData .= implode(',', array_map([$this, 'escapeCsvField'], $headers)) . "\n";
        
        // Add data rows
        foreach ($data as $row) {
            $rowData = [
                $row->mof_no ?? '',
                $row->supplier_name ?? '',
                $row->tel_no ?? '',
                $row->name ?? '',
                $row->designation ?? '',
                $row->email ?? ''
            ];
            $csvData .= implode(',', array_map([$this, 'escapeCsvField'], $rowData)) . "\n";
        }
        
        return $csvData;
    }

    private function escapeCsvField($field)
    {
        // Handle null values
        if ($field === null) {
            return '""';
        }
        
        // Convert to string
        $field = (string) $field;
        
        // If field contains comma, quote, or newline, wrap in quotes and escape quotes
        if (strpos($field, ',') !== false || strpos($field, '"') !== false || strpos($field, "\n") !== false) {
            $field = '"' . str_replace('"', '""', $field) . '"';
        }
        
        return $field;
    }
    public function getQuatationTenderSummary()
    {
        $docNo = request()->doc_no;
        $detailQt = null;
        $detailKodB = null;
        $detailZonal = null;
        $detailQt2 = null;
        $detailQt3 = null;
        $detailsemakansubmit = null;
        $detailsemakanxsubmit = null;
        $detailbidang = null;
        $detailinvitation = null;
        $detailrespond = null;
        $detailsubmit = null;
        $detailXsubmit = null;
        $listDetailQt22 = null;
        $listDetailQt33 = null;
        $listDetailQt4 = null;
        $listsemakan = null;
        $getQtNo = null;
        $getqt = null;
        $getqt1 = null;
        $getqt2 = null;
        $getqt11 = null;
        $getqt22 = null;
        $getbsv1 = null;
        $getbsv2 = null;
        $getbsvfinal = null;
        $getqtfinal = null;
        $getprevious = null;
        $qtNo = null;

        if (strpos($docNo, 'LA') !== false) {
            //LA daripada QT
            $getQtNo = $this->getQTNoFromLOA($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'LI') !== false) {
            $getQtNo = $this->getQTNoFromLOI($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'BD') !== false) {
            $getQtNo = $this->getQTNoFromBIDDING($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->doc_no;
        } else {
            $qtNo = $docNo;
        }

        if ($qtNo && $qtNo != null) {
            if (strpos($qtNo, 'QT') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'QM') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'X') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'DL') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderDL($qtNo);
            }
            $listDetailKodBidang = $this->getDetailKodBidang($qtNo);
            $listDetailListZonal = $this->getDetailZonalQTSummary($qtNo);
            $listDetailQt22 = $this->getDetailQuatationTenderBSV($qtNo);
            $listDetailQt33 = $this->getListAllCommitteeMembersQT($qtNo);
            $listDetailQt4 = $this->getDetailQuatationTenderCommittePTJ($qtNo);
            $listsemakansubmit = $this->getListSubmitProposal($qtNo);
            $listsemakanxsubmit = $this->getListNotSubmitProposal($qtNo);
            $listdaftarbidang = $this->checkDaftarKodBidangbyToday($qtNo);
            $listinvitation2 = $this->QTCheckInvitation($qtNo);
            $listRespondPro = $this->ListRespondProposal($qtNo);
            $listSubmitPro = $this->ListSubmitProposal($qtNo);
            $listXSubmitPro = $this->ListNotSubmitProposal($qtNo);
            $getqt = (array) $this->getNewQTNo($docNo);

            if (count($listDetailQt) > 0) {
                $detailQt = $listDetailQt[0];
            }
            if (count($listDetailKodBidang) > 0) {
                $detailKodB = $listDetailKodBidang;
            }
            //akmal add details zonal
            if (count($listDetailListZonal) > 0) {
                $detailZonal = $listDetailListZonal;
                //dd($detailZonal);
            }
            if (count($listDetailQt22) > 0) {
                $detailQt2 = $listDetailQt22[0];
            }
            if (count($listDetailQt33) > 0) {
                $detailQt3 = $listDetailQt33;
            }
            if (count($listDetailQt4) > 0) {
                $detailQt4 = $listDetailQt4;
            }
            if (count($listsemakansubmit) > 0) {
                $detailsemakansubmit = $listsemakansubmit;
            }
            if (count($listsemakanxsubmit) > 0) {
                $detailsemakanxsubmit = $listsemakanxsubmit;
            }
            if (count($listdaftarbidang) > 0) {
                $detailbidang = $listdaftarbidang;
            }
            if (count($listinvitation2) > 0) {
                $detailinvitation = $listinvitation2;
            }

            if (count($listDetailQt22) > 0) {
                $getbsv1 = (array) $listDetailQt22[0];
                $getbsvfinal = $getbsv1['bsv_date'];
            }
            if (count($listRespondPro) > 0) {
                $detailrespond = $listRespondPro;
            }
            if (count($listSubmitPro) > 0) {
                $detailsubmit = $listSubmitPro;
            }
            if (count($listXSubmitPro) > 0) {
                $detailXsubmit = $listXSubmitPro;
            }
            if (count($getqt) > 0) {
                $getqt1 = (array) $getqt[0];
                $getqt2 = $this->CheckNewGenerateQTNo($getqt1['qt_id']);
                if (count($getqt2) > 0) {
                    $getqtfinal = $getqt2[0];
                }
            }
            if (count($getqt) > 0) {
                $getqt11 = (array) $getqt[0];
                $getqt22 = $this->CheckPreviousQTno($getqt11['qt_id']);
                if (count($getqt22) > 0) {
                    $getprevious = $getqt22[0];
                }
            }
        }
        return view('sourcing.info_qt_summary', [
            'newqt' => $getqtfinal,
            'getbsv' => $getbsvfinal,
            'previousqt' => $getprevious,
            'qtinfo' => $detailQt,
            'kodbinfo' => $detailKodB,
            'kodzonal' => $detailZonal,
            'listdata22' => $listDetailQt22,
            'listdata' => $listDetailQt33,
            'listcommittee' => $listDetailQt4,
            'listdatasemakan' => $detailsemakansubmit,
            'listdatasemakanx' => $detailsemakanxsubmit,
            'listdetailbidang' => $detailbidang,
            'listinv' => $detailinvitation,
            'listRP' => $detailrespond,
            'listSP' => $detailsubmit,
            'listXSP' => $detailXsubmit,
            'carian' => request()->doc_no
        ]);
    }

    public function getBSVdetails()
    {
        $docNo = request()->doc_no;
        $detailQt = null;
        $detailZonal = null;
        $detailQt2 = null;
        $detailQt3 = null;
        $listDetailQt22 = null;
        $listsemakan = null;
        $getQtNo = null;
        $getqt = null;
        $getqt1 = null;
        $getqt2 = null;
        $getqt11 = null;
        $getqtprevious = null;
        $getbsv1 = null;
        $getbsv2 = null;
        $getbsvfinal = null;
        $getqtfinal = null;
        $getprevious = null;
        $qtNo = null;

        if (strpos($docNo, 'LA') !== false) {
            //LA daripada QT
            $getQtNo = $this->getQTNoFromLOA($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'LI') !== false) {
            $getQtNo = $this->getQTNoFromLOI($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->qt_no;
        } else if (strpos($docNo, 'BD') !== false) {
            $getQtNo = $this->getQTNoFromBIDDING($docNo);
            if ($getQtNo)
                $qtNo = $getQtNo[0]->doc_no;
        } else {
            $qtNo = $docNo;
        }

        if ($qtNo && $qtNo != null) {
            if (strpos($qtNo, 'QT') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'QM') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'X') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderSummary($qtNo);
            } else if (strpos($qtNo, 'DL') !== false) {
                $listDetailQt = $this->getDetailQuatationTenderDL($qtNo);
            }
            $listDetailListZonal = $this->getDetailZonalQTSummary($qtNo);
            $listDetailQt22 = $this->getDetailQuatationTenderBSV($qtNo);
            $getqt = (array) $this->getNewQTNo($docNo);

            if (count($listDetailQt) > 0) {
                $detailQt = $listDetailQt[0];
            }
            //akmal add details zonal
            if (count($listDetailListZonal) > 0) {
                $detailZonal = $listDetailListZonal;
                //dd($detailZonal);
            }
            if (count($listDetailQt22) > 0) {
                $detailQt2 = $listDetailQt22[0];
            }
            if (count($listDetailQt22) > 0) {
                $getbsv1 = (array) $listDetailQt22[0];
                $getbsvfinal = $getbsv1['bsv_date'];
            }
            if (count($getqt) > 0) {
                $getqt1 = (array) $getqt[0];
                $getqt2 = $this->CheckNewGenerateQTNo($getqt1['qt_id']);
                if (count($getqt2) > 0) {
                    $getqtfinal = $getqt2[0];
                }
            }
            if (count($getqt) > 0) {
                $getqt11 = (array) $getqt[0];
                $getqtprevious = $this->CheckPreviousQTno($getqt11['qt_id']);
                if (count($getqtprevious) > 0) {
                    $getprevious = $getqtprevious[0];
                }
            }
        }
        return view('sourcing.info_qt_checkingBSV', [
            'newqt' => $getqtfinal,
            'getbsv' => $getbsvfinal,
            'previousqt' => $getprevious,
            'qtinfo' => $detailQt,
            'kodzonal' => $detailZonal,
            'listdata22' => $listDetailQt22,
            'carian' => request()->doc_no
        ]);
    }

    public function getQuatationTenderBSV()
    {
        $docNo = request()->doc_no;
        $detailQt = null;
        if ($docNo && $docNo != null) {
            $listDetailQt = $this->getDetailQuatationTenderBSV($docNo);

            if (count($listDetailQt) > 0) {
                $detailQt = $listDetailQt[0];
            }
        }
        return view('sourcing.info_qt_summary', [
            'qtinfo' => $detailQt,
            'carian' => request()->doc_no
        ]);
    }

    public function getQuotationTenderStuckSummary()
    {
        $icNo = request()->ic_no;
        $icNo2 = $icNo;
        //        $icNo3 = $icNo;
        $detailstuck1 = null;
        $detailstuck2 = null;
        $detailstuck3 = null;
        $listDetailStuck2 = null;
        $listDetailStuck3 = null;

        if ($icNo && $icNo != null) {
            $listDetailQt = $this->getDetailSummaryStuckInfo($icNo);
            $listDetailStuck2 = $this->getDetailSummaryStuckRole($icNo);
            $listDetailStuck3 = $this->getDetailSummaryStuckTask($icNo);
            //            dd($listDetailStuck3);

            if (count($listDetailQt) > 0) {
                $detailstuck1 = $listDetailQt[0];
                //var_dump($listDetailQt);
            }
            if (count($listDetailStuck2) > 0) {
                $detailstuck2 = $listDetailStuck2[0];
                //var_dump($listDetailStuck2);
            }
            if (count($listDetailStuck3) > 0) {
                $detailstuck3 = $listDetailStuck3[0];
                //var_dump($listDetailStuck3);
            }
        }

        return view('sourcing.info_qt_stucksummary', [
            'stuckinfo' => $detailstuck1,
            'stuckrole' => $listDetailStuck2,
            'stucktask' => $listDetailStuck3,
            'carian' => request()->$icNo
        ]);
    }

    public function getQuotationTenderVerifySupp()
    {
        $qtNo = request()->doc_no;
        $qtKey = request()->kategori;
        $detailverifyL = null;
        $detailverifyG = null;
        $verifyLulus = null;
        $verifyGagal = null;

        if ($qtNo && $qtKey != null) {
            $verifyLulus = $this->getDetailVerifySupplierLulus($qtNo, $qtKey);
            $verifyGagal = $this->getDetailVerifySupplierGagal($qtNo, $qtKey);
            
        }
        return view('sourcing.qt_spl_verification', [
            'verifylulus' => $verifyLulus,
            'verifyGagal' => $verifyGagal,
            'cariandocno' => request()->doc_no,
            'cariankategori' => request()->kategori
        ]);
    }
   
    public function getQTsubmitproposalbyMOF()
    {
        $mofNo = request()->mof_no;
        $verifySubmit = null;
        $verifyNotSubmit = null;

        if ($mofNo != null) {
            $verifySubmit = $this->getListQTsubmitproposal_mofsupplier($mofNo);
            $verifyNotSubmit = $this->getListQTnotsubmitproposal_mofsupplier($mofNo); 
        }
        return view('sourcing.qt_searching_qtby_suppmof', [
            'verifysubmit' => $verifySubmit,
            'verifynotsubmit' => $verifyNotSubmit,
            'carianmofno' => request()->mof_no,
        ]);
    }

    public function downloadReportSubmit(Request $request) {
        $MOFNo = $request->mof_no;
        $listdata = $this->getListQTsubmitproposal_mofsupplier($MOFNo);  
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Maklumat Pembekal';

            Excel::create($fileName, function($excel)use($collectlistReporting) {
                $excel->setTitle('Pembekal Submit');

                $excel->sheet('Pembekal Submit', function($sheet) use ($collectlistReporting) {
                   $this->getDetailsExcelsubmitproposal($sheet,$collectlistReporting);
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    private function getDetailsExcelsubmitproposal($sheet,$collectlistReporting){
        $sheet->setOrientation('landscape');
        foreach ($collectlistReporting as $list) {
            $sheet->setStyle(array(
                'font' => array(
                    'name' => 'Calibri',
                    'size' => 10,
                    'bold' => false
                )
            ));

            $sheet->setColumnFormat(array(
                'A' => '@',
                'B' => '@',
                'C' => '@',
                'D' => '@',
                'E' => '@',
                'F' => '@',
                'G' => '@',
                'H' => '@',
            ));

            $sheet->row(1, array(
                'Supplier Name', 'MOF No', 'QT No', 'Proposal No', 'Submitted', 'Submit Date', 'QT Status'
            ));

            $sheet->row(1, function($row) {
                // call cell manipulation methods
                $row->setBackground('#D9D9D9');
            });

            $sheet->setAutoSize(true);

            $sheet->cells('A1:G1', function($cells) {
                // manipulate the range of cells
                // Set with font color
                $cells->setFontColor('##070606');

                // Set font family
                $cells->setFontFamily('Calibri');

                // Set font size
                $cells->setFontSize(9);

                // Set font weight to bold
                $cells->setFontWeight('bold');

                // Set all borders (top, right, bottom, left)
                $cells->setBorder('solid', 'solid', 'solid', 'solid');
            });

            $count = 2;
            foreach ($list as $obj) {
                $sheet->row($count, array(
                    $obj->supplier_name,
                    $obj->mof_no,
                    $obj->qt_no,
                    $obj->proposal_no,
                    $obj->submitted,
                    $obj->proposal_submit_date,
                    $obj->qtstatus,
                        )
                );
                $count++;
            }
        }
    }



    public function downloadReportLulus(Request $request) {
        $qtNo = $request->doc_no;
        $qtKey = $request->kategori;
        $listdata = $this->getDetailVerifySupplierLulus($qtNo, $qtKey);
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Status Pembekal';

            Excel::create($fileName, function($excel)use($collectlistReporting) {
                $excel->setTitle('Pembekal Lulus');

                $excel->sheet('Pembekal Lulus', function($sheet) use ($collectlistReporting) {
                   $this->getDetailsExcel($sheet,$collectlistReporting);
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    public function downloadReportGagal(Request $request) {
        $qtNo = $request->doc_no;
        $qtKey = $request->kategori;
        $listdata = $this->getDetailVerifySupplierGagal($qtNo, $qtKey);
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Status Pembekal';

            Excel::create($fileName, function($excel)use($collectlistReporting) {
                $excel->setTitle('Pembekal Gagal');

                $excel->sheet('Pembekal Gagal', function($sheet) use ($collectlistReporting) {
                   $this->getDetailsExcel($sheet,$collectlistReporting);
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }

    private function getDetailsExcel($sheet,$collectlistReporting){
        $sheet->setOrientation('landscape');
        foreach ($collectlistReporting as $list) {
            $sheet->setStyle(array(
                'font' => array(
                    'name' => 'Calibri',
                    'size' => 9,
                    'bold' => false
                )
            ));

            $sheet->setColumnFormat(array(
                'A' => '@',
                'B' => '@',
                'C' => '@',
                'D' => '@',
                'E' => '@',
                'F' => '@',
                'G' => '@',
                'H' => '@',
            ));

            $sheet->row(1, array(
                'QT No', 'Supplier Id', 'Supplier Name', 'MOF No','Supplier Code','Qualified','Proposal No',
                'Submit Poposal', 'Submit Date','Evaluation Stage', 'Score', 'Ranking', 'Item Name',
                'MOF Admin','No Phone','Address 1', 'Address 2', 'Address 3','Poscode',
               'City Nama', 'District Name','Division Name','State_name'
            ));

            $sheet->row(1, function($row) {
                // call cell manipulation methods
                $row->setBackground('#D9D9D9');
            });

            $sheet->setAutoSize(true);

            $sheet->cells('A1:G1', function($cells) {
                // manipulate the range of cells
                // Set with font color
                $cells->setFontColor('##070606');

                // Set font family
                $cells->setFontFamily('Calibri');

                // Set font size
                $cells->setFontSize(9);

                // Set font weight to bold
                $cells->setFontWeight('bold');

                // Set all borders (top, right, bottom, left)
                $cells->setBorder('solid', 'solid', 'solid', 'solid');
            });

            $count = 2;
            foreach ($list as $obj) {
                $sheet->row($count, array(
                    $obj->qt_no,
                    $obj->supplier_id,
                    $obj->supplier_name,
                    $obj->mof_no,
                    $obj->supplier_no,
                    $obj->is_qualified,
                    $obj->proposal_no,
                    $obj->is_submitted,
                    $obj->proposal_submit_date,
                    $obj->eval_stage,
                    $obj->total_score,
                    $obj->eval_ranking,
                    $obj->item_name,
                    $obj->mof_supplier_admin,
                    $obj->nophone,
                    $obj->address_1,
                    $obj->address_2,
                    $obj->address_3,
                    $obj->postcode,
                    $obj->city_name,
                    $obj->district_name,
                    $obj->division_name,
                    $obj->state_name
                        )
                );
                $count++;
            }
        }
    }

    use BpmApiService;

    //    akmal was here 12082020
    public function getSupplierRespond()
    {

        $docNo = request()->doc_no;
        $module = 'SourcingQT';
        $semaksurpress = null;
        $semaksurpress2 = null;
        $listDataResult2 = null;
        $listpembekal = null;
        $listpembekal2 = null;
        $listsupplier = array();

        if ($docNo && $docNo != null) {
            $listDetailQt = $this->getDetailSuppRespond($docNo);
            //            $listDetailQt2 = $this->getDetailWHOreject($docNo);
            //getBPM
            if (count($listDetailQt) > 0) {
                $listpembekal = $this->findAPITaskBPMListDocAndModule($docNo, $module);

                if ($listpembekal["status"] != null && $listpembekal["status"] === 'Success') {
                    $listpembekal2 = $listpembekal["result"];
                    if ($listpembekal2) {
                        foreach ($listpembekal2 as $data) {
                            if ($data['componentName'] == 'AcceptQTChange') {
                                array_push($listsupplier, $data);
                            }
                        }
                    }
                } else {
                    $statusAPI = $listCompositeSupplier["result"];
                }
            }
            if (count($listDetailQt) > 0) {
                $semaksurpress = $listDetailQt;
            }
            //            if (count($listDetailQt2) > 0) {
            //                $semaksurpress2 = $listDetailQt2;
            //            }
        }
        return view('sourcing.verify_sup_respond', [
            'suppres' => $semaksurpress,
            //            'suppres2' => $semaksurpress2,
            'listResult2' => $listsupplier,
            'carian' => request()->$docNo
        ]);
    }

    //    akmal test bpmapi 12082020 rujukan
    public function searchListTaskBpmtempoh(Request $request)
    {

        $listdata = collect();
        $statusAPI = null;
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'doc_no' => 'required',
                'module' => 'required'
            ]);
        }

        if ($request->created_date != null) {
            $listDataResult = $this->findAPITaskBPMList($request->doc_no, $request->module, $request->created_date);
        } else {
            $listDataResult = $this->findAPITaskBPMListDocAndModule($request->doc_no, $request->module);
        }
        //dump($listDataResult);
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
            foreach ($listdata as $key => $row) {
                $listComposite = explode("*", $row['compositeDN']);
                $listdata[$key]['composite'] = $listComposite[0];
            }
        } else {
            $statusAPI = $listDataResult["result"];
        }

        return view('sourcing.extend_tempohsahlaku', [
            'status_api' => $statusAPI,
            'listdata' => $listdata
        ]);
    }

    public function getSupplierExp()
    {
        $mofNo = request()->mof_no;
        $semakexp = null;
        $semakexp2 = null;
        $semakexp3 = null;

        if ($mofNo && $mofNo != null) {
            $listDetailQt1 = $this->getDetailSuppExp1($mofNo);
            $listDetailQt2 = $this->getDetailSuppExp2($mofNo);
            $listDetailQt3 = $this->getDetailSuppExp3($mofNo);

            if (count($listDetailQt1) > 0) {
                $semakexp = $listDetailQt1;
            }
            if (count($listDetailQt2) > 0) {
                $semakexp2 = $listDetailQt2;
            }
            if (count($listDetailQt3) > 0) {
                $semakexp3 = $listDetailQt3[0];
            }
        }
        return view('sourcing.verify_sup_experience', [
            'suppexp1' => $semakexp,
            'suppexp2' => $semakexp2,
            'suppexp3' => $semakexp3,
            'carian' => request()->$mofNo
        ]);
    }

    public function getLOALOIStatusforQT()
    {
        $qtNo = request()->qt_no;
        $listSuccess = null;
        $listPending = null;
        $listRejectCancel = null;
        $listRejectContinue = null;
        $listSuccess1 = null;
        $listPending1 = null;
        $listRejectCancel1 = null;
        $listRejectContinue1 = null;

        if ($qtNo != null) {
            $listSuccess = $this->getSuccessLOA($qtNo);          
            $listPending = $this->getPendingLOA($qtNo);
            $listRejectContinue = $this->getRejectContinueLOA($qtNo);
            $listRejectCancel = $this->getRejectCancelLOA($qtNo);

            if (count($listSuccess) > 0) {
                $listSuccess1 = $listSuccess;
            }
            if (count($listPending) > 0) {
                $listPending1 = $listPending;
            }
            if (count($listRejectContinue) > 0) {
                $listRejectContinue1 = $listRejectContinue;
            }
            if (count($listRejectCancel) > 0) {
                $listRejectCancel1 = $listRejectCancel;
            }
        }

        return view('sourcing.info_qt_loastatus', [
            'success' => $listSuccess1,
            'pendingloa' => $listPending1,
            'rejectcontinue' => $listRejectContinue1,
            'rejectcancel' => $listRejectCancel1,
            'carian' => request()->$qtNo
        ]);
    }

    public function viewQtDocumentChecklist(Request $request)
    {
        $method = $request->method();
        $docs = [];
        if ($method === 'POST') {
            $docs = $this->getQtDocumentChecklist($request);
        }

        return view('qt.qtdocument', ['documents' => $docs, 'qt_no' => $request->qtNo, 'pmNo' => $request->pmNo]);
    }

    public function validateDocumentExist(Request $request)
    {
        return $this->checkDocumentExist($request->filename);
    }

    public function getQtDocuments(Request $request)
    {
        return $this->getQtDocumentChecklist($request);
    }

    public function extractDocument(Request $request)
    {
        return $this->transferAndDecryptDocument($request->filename, $request->qtno, $request->isarchive);
    }

    public function zipMultipleDocuments(Request $request)
    {
        return $this->doZipMultipleDocuments($request);
    }

    public function downloadSingleQtDocument(Request $request)
    {
        $filenames = array();
        array_push($filenames, $request->filename);
        $request->filenames = json_encode($filenames);
        $response = $this->doZipMultipleDocuments($request);
        $zipName = $response['zipname'];
        $content = $response['content'];

        $parameters =  collect([]);
        $parameters->put("filename", $request->filename);
        $parameters->put("user_id", Auth::user()->id);
        $parameters->put("path", $request->path());
        $parameters->put("full_url", $request->fullUrl());

        $actionTypeLog = 'Download';
        $actionName = 'Qt Download Single Document';
        EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed', Auth::user()->user_name);

        try {
            $headers = [
                'Content-type'        => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipName . '"',
            ];

            $response = response()->make($content, 200, $headers);
            return $response;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public function downloadMultipleQtDocument(Request $request)
    {
        $response = $this->doZipMultipleDocuments($request);
        $zipName = $response['zipname'];
        $content = $response['content'];

        $parameters =  collect([]);
        $parameters->put("filename", $zipName);
        $parameters->put("qt_no", $request->qtno);
        $parameters->put("user_id", Auth::user()->id);
        $parameters->put("path", $request->path());
        $parameters->put("full_url", $request->fullUrl());

        $actionTypeLog = 'Download';
        $actionName = 'Qt Download Multiple Document';
        EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed', Auth::user()->user_name);

        // $mdw_api = env("JAVA_MIDDLEWARE_RESTFUL", "http://localhost:8080");
        // $fileurl = $mdw_api . '/ep/zip-download' . '?filename=' . $zipName;
        // $response = Guzzle::get($fileurl);

        try {
            $headers = [
                'Content-type'        => 'application/zip',
                'Content-Disposition' => 'attachment; filename="' . $zipName . '"',
            ];

            $response = response()->make($content, 200, $headers);
            return $response;
        } catch (Exception $e) {
            return $e->getMessage();
        }
    }

    public function getProposalNosByQtNo(Request $request)
    {
        return $this->getListProposalNosByQtNo($request->qtno);
    }
}
