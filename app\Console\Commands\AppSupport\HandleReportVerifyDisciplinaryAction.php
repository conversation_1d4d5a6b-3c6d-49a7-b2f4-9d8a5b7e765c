<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\AppSupport\ReportSupplierDisciplinaryAction;
use App\Migrate\MigrateUtils;

/**
 * Request by <PERSON><PERSON> on 16/4/2021 , to avoid Users eP to allow process supplier on application SM or proposal QT if supplier under disciplinary action.
 */
class HandleReportVerifyDisciplinaryAction extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'report-mail-disciplinary-action-supplier';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To send report by email list of disciplinary action supplier on activity application SM and proposal QT';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            ReportSupplierDisciplinaryAction::run();
            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error HandleReportVerifyDisciplinaryAction'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__METHOD__.' >  sendErrorEmail '.json_encode($data["to"]) .' >> ' .$e->getMessage());
        }
    }
    
}
