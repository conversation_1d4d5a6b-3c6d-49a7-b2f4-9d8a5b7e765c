@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Site Visit Rollback</strong></h1> 
    </div> 
    <div class="table-responsive">
        <p>
            <code>

                - Notes: Initiate MOFSupplierSiteVisit from Service Manager. Replace with values below. 
            </code>
        </p>
    </div>
    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">appl_no</th>
                    <th class="text-center">supplier_id</th>
                    <th class="text-center">document_id</th>
                    <th class="text-center">supplier_list</th>
                    <th class="text-center">svoTaskIdList</th>
                    <th class="text-center">svoUserList</th>
                    <th class="text-center">svoTaskId</th>
                    <th class="text-center">svoUserListStr</th>
                    <th class="text-center">companyName</th>
                    <th class="text-center">businessRegNo</th>
                    <th class="text-center">poUsers</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $rowData)
                <tr>
                    <td class="text-center">{{$rowData->appl_no}}</td>
                    <td class="text-center">{{$rowData->supplier_id}}</td>
                    <td class="text-center">{{$rowData->document_id}}</td>
                    <td class="text-center">{{$rowData->supplier_list}}</td>
                    <td class="text-center">{{$rowData->svotaskidlist}}</td>
                    <td class="text-center">{{$rowData->svouserlist}}</td>
                    <td class="text-center">{{$rowData->svotaskid}}</td>
                    <td class="text-center">{{$rowData->svouserliststr}}</td>
                    <td class="text-center">{{$rowData->companyname}}</td>
                    <td class="text-center">{{$rowData->businessregno}}</td>
                    <td class="text-center">{{$rowData->pousers}}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



