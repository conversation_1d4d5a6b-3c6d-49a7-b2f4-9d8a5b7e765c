<?php

namespace App\Console\Commands\Baileys;

use Illuminate\Console\Command;
use App\Services\EpNotify\EpNotifyService;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Config;

class HandleBaileysStatusMonitoring extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'whatsapp:monitor';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor WhatsApp connection status for both EP and CRM and send email notifications if there are issues';

    private $epNotifyService;
    private $connections = [
        'EP' => 'mysql_notify',
        'CRM JBAL' => 'mysql_crm_notify'
    ];

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
        $this->epNotifyService = new EpNotifyService();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        try {
            $this->info('Starting WhatsApp connection monitoring...');
            $hasErrors = false;
            $errorData = [];

            foreach ($this->connections as $name => $connection) {
                $this->info("Checking {$name} WhatsApp connection status...");

                $status = $this->epNotifyService->getWhatsAppStatus($connection);

                if (!$status) {
                    $this->error("No WhatsApp status found in logs for {$name}");
                    $hasErrors = true;
                    $errorData[$name] = [
                        'status' => 'No Status',
                        'message' => 'No WhatsApp status found in logs',
                        'last_update' => 'N/A'
                    ];
                    continue;
                }

                if ($status->status !== 'Completed' && $status->status !== 'Loading') {
                    $hasErrors = true;

                    $statusMessage = '';
                    if ($status->status === 'Failed') {
                        $statusMessage = "{$name} WhatsApp connection failed: " . $status->description;
                        $this->error($statusMessage);
                    } elseif ($status->status === 'QR') {
                        $statusMessage = "{$name} WhatsApp connection needs QR scan: " . $status->description;
                        $this->error($statusMessage);
                    } else {
                        $statusMessage = "{$name} WhatsApp connection is not completed (Status: {$status->status}): " . $status->description;
                        $this->error($statusMessage);
                    }

                    $errorData[$name] = [
                        'status' => $status->status,
                        'message' => $statusMessage,
                        'last_update' => $status->date_entered
                    ];
                } elseif ($status->status === 'Loading') {
                    $this->info("{$name} WhatsApp connection is loading: " . $status->description);
                } else {
                    $this->info("{$name} WhatsApp connection is healthy (Status: " . $status->status . ")");
                }
            }

            if ($hasErrors) {
                $this->sendAlertEmail($errorData);
            }

            return $hasErrors ? 1 : 0;

        } catch (\Exception $e) {
            Log::error('WhatsApp monitoring error: ' . $e->getMessage());
            $this->error('Error running WhatsApp monitor: ' . $e->getMessage());
            return 1;
        }
    }

    /**
     * Send alert email to admin
     */
    private function sendAlertEmail(array $errorData)
    {
        $currentDateTime = Carbon::now()->format('D, d M Y H:i:s');

        $dataEmail = [
            'email_subject' => "WhatsApp Connection Status Alert ({$currentDateTime})",
            'connections' => $errorData
        ];

        $data = [
            "to" => ['<EMAIL>'],
            "cc" => ['<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > ' . $dataEmail['email_subject']
        ];

        try {
            Mail::send('emails.whatsapp-alert', $dataEmail, function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->cc($data["cc"]) // Add CC recipients
                    ->subject($data["subject"]);
            });

            Log::info(self::class . ' > ' . __FUNCTION__ . ' :: WhatsApp alert email sent', $errorData);

        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', [
                'Email' => $data["to"],
                'ERROR' => $e->getMessage()
            ]);
            return $e;
        }
    }
}