@extends('layouts.guest-dash')

@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-5 log-header-title">
            <span>OSB Log (PHIS)<br /></span>
            <small>Search By:  ContractRequestNo, FulfillmentReceivedNote, ContractOrderNo, ProductCode, 
                ItemDescription, StopInstructionNo, EPInvoiceNo, DeliveryOrderNo, PTJCode, PhysicalContactNo</small>
        </div>
        <div class="col-md-6 log-header-menu">
            <a href="{{url('find/osb/log')}}"><span class="{{ Request::is('find/osb/log') ? 'active' : '' }}">Log</span></a> |
            <a href="{{url('find/osb/detail/log')}}"><span class="{{ Request::is('find/osb/detail/log') ? 'active' : '' }}">Log Detail</span></a> |
            <a href="{{url('find/osb/detail-rquid/log')}}"><span class="{{ Request::is('find/osb/detail-rquid/log') ? 'active' : '' }}">Log RQ-UID</span></a> | 
            <a href="{{url('find/osb/batch/file')}}"><span class="{{ Request::is('find/osb/batch/file') ? 'active' : '' }}">Batch File Log</span></a> | 
            <a href="{{url('osb/file/content/search')}}"><span class="{{ Request::is('osb/file/content/search') ? 'active' : '' }}">Find Content File</span></a> |
            <a href="{{url('find/osb/error')}}"><span class="{{ Request::is('find/osb/error') ? 'active' : '' }}">Error Log</span></a> | 
            <a href="{{url('find/1gfmas/ws')}}"><span class="{{ Request::is('find/1gfmas/ws') ? 'active' : '' }}">Log (IGFMAS)</span></a> | 
            <a href="{{url('find/phis/ws')}}"><span class="{{ Request::is('find/phis/ws') ? 'active' : '' }}">Log (PHIS)</span></a> 
        </div>
    </div>
</div>
@endsection


@section('content')

<div class="content-header">
    
    <!-- Log Block -->
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Log</strong> PHIS Info</h2>
            </div>
            
                <div class="block">
                    <form id="form-search-mminf" action="{{url("/find/phis/ws/search")}}" method="get" class="form-horizontal" onsubmit="return true;">
                       
                        
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="doc_no">Document No </label>
                            <div class="col-md-5">
                                <input id="doc" name="doc_no" class="form-control" placeholder="Document No. (CR/CO).." type="text" required="true"
                                       value="{{ $carian }}">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="service_code">Service Code </label>
                            <div class="col-md-5">
                                <div class="input-group">
                                    <select id="service_code" name="service_code" class="form-control">
                                        <option value="">All</option>
                                        
                                        @foreach(App\Services\EPService::$SERVICE_CODE_PHIS as  $key => $value)
                                            <option value="{{$key}}" @if(old('service_code') == $key) selected @endif>{{$value}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="form-group form-actions">
                            <div class="col-md-9 col-md-offset-3">
                                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                            </div>
                        </div>
                    </form>
                </div>
            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td>PHS-080</td>
                            <td class="text-success"><strong>PHISMasterDataDeliveryAddress</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-150</td>
                            <td class="text-success"><strong>ContractInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-160</td>
                            <td class="text-info"><strong>ContractOrderInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-170</td>
                            <td class="text-info"><strong>ContractFulfilmentReceivedNote</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-180</td>
                            <td class="text-success"><strong>ContractStopInstruction</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-190</td>
                            <td class="text-danger"><strong>ContractPaymentInstruction</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-200</td>
                            <td><strong>ContractPaymentEffectNotification</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                        <tr>
                            <td>PHS-210</td>
                            <td><strong>ContractRequestCreation</strong></td>
                            <td>PHIS sent to eP</td>
                        </tr>
                        <tr>
                            <td>PHS-220</td>
                            <td><strong>PHISMasterDataMaterialInformation</strong></td>
                            <td>eP sent to PHIS</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- END Log Content -->
            </div>
            <!-- END Log Title -->          
        </div>
        <!-- END Log Block -->

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              <p>Tidak dijumpai!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        
    
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                        <th class="text-center">PAYLOAD</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center"><a href="{{url('/find/osb/detail/log')}}?cari={{$data->trans_id }}" target="_blank" >{{$data->trans_id }}</a></td>
                        <td class="text-center">{{ $data->trans_type }}</td>
                        <td class="text-center">{{$data->service_code }}</td>
                        <td class="text-center">{{ $data->trans_date }}</td>
                        <td class="text-left">{{ $data->status_desc }}</td>
                        <td class="text-center">{{ $data->remarks_1 }}</td>
                        <td class="text-center">{{ $data->remarks_2 }}</td>
                        <td class="text-center">{{ $data->remarks_3 }}</td>
                        <td class="text-center">
                            <a href="#modal-list-data-detail" class="modal-list-data-action " 
                               data-toggle="modal" data-url="{{url('/support/report/log-detail/osb-payload')}}/" 
                               data-carian="@if(isset($carian)){{$data->logging_id}}@endif" 
                               data-title="Payload Data">Click Here</a>
                        </td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>

@include('_shared._modalDetailInfo')

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>
@endif

@endsection
