<div id="task-flow" style="display:none;">
    <div class="block">
        <button disabled="true" id="suspend_task" name="suspend_task" class="btn btn-sm btn-primary fa fa-pause"> Suspend</button>
        <button disabled="true" id="resume_task" name="resume_task" class="btn btn-sm btn-primary fa fa-play"> Resume</button>
        <button disabled="true" id = "reassign_task" name="reassign_task" class="btn btn-sm btn-primary fa fa-hand-grab-o pull-right"> Reassign</button>
        <button disabled="true" id = "withdraw_task" name="withdraw_task" class="btn btn-sm btn-primary fa fa-trash pull-right"> Withdraw</button>
        <br/><br/>
        <table  class="table table-borderless" style="table-layout: fixed; width: 100%;background-color: #f2f2f2">
            <tbody style="font-size:80%;">
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Task ID : </strong></td>
                    <td colspan="2" id="task_id"></td>
                    <td><select id="group_task" name="group_task" class="form-control" style="background-color: #f7f7f7;width:70px;display: none;"></select></td>
                    <td><button class="btn btn-sm btn-primary fa fa-file-text" id="history_task"></button></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Assignees : </strong></td>
                    <td style="word-wrap: break-word;" colspan="2" id="assignee"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Acquired : </strong></td>
                    <td id="acquired"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Composite : </strong></td>
                    <td id="composite"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Status : </strong></td>
                    <td id="status"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Process : </strong></td>
                    <td id="process"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Outcome : </strong></td>
                    <td id="outcome"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Activity : </strong></td>
                    <td id="activity"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">Created : </strong></td>
                    <td id="created"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Expiry : </strong></td>
                    <td id="expiry"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">String 1 : </strong></td>
                    <td id="string_1"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Number 1 : </strong></td>
                    <td id="number_1"></td>
                </tr>
                <tr>
                    <td class="text-right"><strong style="color:#003d7a;">String 2 : </strong></td>
                    <td id="string_2"></td>
                    <td class="text-right"><strong style="color:#003d7a;">Number 2 : </strong></td>
                    <td id="number_2"></td>
                </tr>
                <tr> 
                    <td id="rq_uid_cls" style="display:none" class="text-right"><strong style="color:#003d7a;">RQ-UID : </strong></td>
                    <td id="rq_uid" style="word-wrap: break-word;" colspan="3"></td>
                </tr>
            </tbody>
        </table> 

        <strong><p style="color:#003d7a;font-size:120%;">&nbsp;&nbsp;Payload </p></strong>
        <textarea rows="20" id="payload" style="width: 100%"> </textarea> <br/><br/>

        <fieldset>
            <div class="input-group pull-left">
                <input type="text" disabled="true" class="form-control" id="reference_task" name="reference_task" placeholder="- reference task id/number -" style="width:230px">
            </div>
            <div class="input-group pull-left">
                &nbsp;&nbsp;&nbsp;<button disabled="true" id="load_task" name="load_task" class="btn btn-sm btn-primary fa fa-download"> Load</button>
            </div>
            <div class="input-group pull-right">
                &nbsp;&nbsp;&nbsp;<button id="execute_task" name="execute_task" class="btn btn-sm btn-primary fa fa-fire"> Execute</button>
            </div>
            <div class="input-group pull-right">
                <select id="action_task" name="action_task" class="form-control pull-right" style="background-color: #f7f7f7;width:200px"></select>
            </div>
        </fieldset>
    </div>
</div>

<div id="modal_reassign" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h4> Enter user to reassign task to </h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="assign_user">User : <span class="text-danger">*</span></label>
                    <div class="col-md-10">
                        <input type="text" id="assign_user" name="assign_user" class="form-control">
                    </div>
                </div>
            </div>
            <br/><br/>
            <div class="modal-footer">
                <button type="button" id="submit_reassign" class="btn btn-sm btn-info">Ok</button>
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Cancel</button>
            </div>
        </div>
    </div>
</div>

<!--modal task history-->
<div id="modal_history" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h5> Log history for task </h5>
                <h5 id="history_task_id"></h5>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-condensed table-borderless" id="table_history"></table>
                </div> 
            </div>
            <br/><br/>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Close</button>
            </div>
        </div>
    </div>
</div>

<!--modal task execute user input-->
<div id="modal_submit_user" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h4> Enter user to submit task </h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="submit_user">User : <span class="text-danger">*</span></label>
                    <div class="col-md-10">
                        <input type="text" id="submit_user" name="submit_user" class="form-control">
                    </div>
                </div>
            </div>
            <br/><br/>
            <div class="modal-footer">
                <button type="button" id="submit_user_btn" class="btn btn-sm btn-info">Ok</button>
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Cancel</button>
            </div>
        </div>
    </div>
</div>