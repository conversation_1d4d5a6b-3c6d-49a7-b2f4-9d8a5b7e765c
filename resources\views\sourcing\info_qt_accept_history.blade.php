@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carian_qt_form" action="{{url()->current()}}" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{$carian ?? ''}}" class="form-control"
                onfocus="this.select();" placeholder="Sila Masukkan No QT">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    <div class="content-header"></div>

    @if($qtinfo == null || $qtinfo->isEmpty())
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> 
                        <strong>
                            @if(isset($carian) && !empty($carian))
                                Carian : {{$carian}}
                            @else
                                QT Accept History
                            @endif
                        </strong>
                    </h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>Rekod Tidak dijumpai!</p>
                    </div>
                </div>
            </div>
        </div>
    @else
        <!-- Supplier Statistics Table - Show one table with composite instances as rows -->
        @if(isset($carian) && !empty($carian) && isset($composite_instance_stats) && !empty($composite_instance_stats))
            <div class="block block-alt-noborder full">
                <div class="block">
                    <div class="block-title panel-heading epss-title-s1">
                        <h3><i class="fa fa-pie-chart"></i> 
                            <strong>Supplier Response Statistics by Composite Instance</strong>
                        </h3>
                    </div>
                    <div class="table-responsive" style="zoom: 1.2;">
                        <table class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Composite Instance ID</th>
                                    <th class="text-center">Total Suppliers Involved</th>
                                    <th class="text-center">Suppliers Accepted</th>
                                    <th class="text-center">Suppliers Rejected</th>
                                    <th class="text-center">Suppliers No Response</th>
                                    <th class="text-center">Response Rate</th>
                                    <th class="text-center">Expiry Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($composite_instance_stats as $stats)
                                    <tr>
                                        <td class="text-center">
                                            <a target="_blank" href="/bpm/instance/find?composite_instance_id={{urlencode($stats['composite_instance_id'])}}">
                                                {{$stats['composite_instance_id']}}
                                            </a>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge" style="background-color: blue;">{{ $stats['unique_suppliers_count'] }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge" style="background-color: green;">{{ $stats['suppliers_accepted_count'] }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge" style="background-color: red;">{{ $stats['suppliers_rejected_count'] }}</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge" style="background-color: grey;">{{ $stats['suppliers_not_responded_count'] }}</span>
                                        </td>
                                        <td class="text-center">
                                            @php
                                                $responseRate = $stats['unique_suppliers_count'] > 0 
                                                    ? round((($stats['suppliers_accepted_count'] + $stats['suppliers_rejected_count']) / $stats['unique_suppliers_count']) * 100, 1)
                                                    : 0;
                                            @endphp
                                            <span class="badge" style="background-color: green;">{{ $responseRate }}%</span>
                                        </td>
                                        <td class="text-center">
                                            {{$stats['expiry_date'] ? date('d/m/Y', strtotime($stats['expiry_date'])) : '-'}}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        <!-- END Supplier Statistics Table -->

        <!-- Summary Table - Only show when QT No is provided -->
        @if(isset($carian) && !empty($carian) && isset($summary_data) && !empty($summary_data))
            <div class="block block-alt-noborder full">
                <div class="block">
                    <div class="block-title panel-heading epss-title-s1">
                        <h3><i class="fa fa-bar-chart"></i> 
                            <strong>QT Summary by Composite Instance</strong>
                        </h3>
                    </div>
                    <div class="table-responsive" style="zoom: 1.2;">
                        <table class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">Composite Instance ID</th>
                                    <th class="text-center">Doc No</th>
                                    <th class="text-center">Total Tasks</th>
                                    <th class="text-center">Task Outcomes</th>
                                    <th class="text-center">Expiry Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($summary_data as $summary)
                                    <tr>
                                        <td class="text-center">
                                            <a target="_blank" href="/bpm/instance/find?composite_instance_id={{urlencode($summary['composite_instance_id'])}}">
                                                {{$summary['composite_instance_id']}}
                                            </a>
                                        </td>
                                        <td class="text-center">{{$summary['doc_no']}}</td>
                                        <td class="text-center">
                                            <span class="badge">{{$summary['total_tasks']}}</span>
                                        </td>
                                        <td class="text-left">
                                            <div style="max-width: 300px;">
                                                @php
                                                    $taskOutcomes = $summary['task_outcomes'];
                                                    $totalTasks = count($taskOutcomes);
                                                    $uniqueId = 'summary-' . $summary['composite_instance_id'];
                                                    $index = 0;
                                                @endphp
                                                @foreach($taskOutcomes as $taskNumber => $outcome)
                                                    <div class="@if($index >= 5)task-outcome-hidden-{{$uniqueId}}@endif" 
                                                         style="margin-bottom: 3px;@if($index >= 5) display: none;@endif">
                                                        <strong>Task {{$taskNumber}}:</strong> 
                                                        <span class="badge">
                                                            {{$outcome ?: 'N/A'}}
                                                        </span>
                                                    </div>
                                                    @php $index++; @endphp
                                                @endforeach
                                                
                                                @if($totalTasks > 5)
                                                    <a href="javascript:void(0)" 
                                                       onclick="toggleTaskOutcomes('{{$uniqueId}}')" 
                                                       id="toggle-outcomes-{{$uniqueId}}" 
                                                       style="font-size: 12px; color: #007bff;">
                                                        show more ({{$totalTasks - 5}} more)
                                                    </a>
                                                @endif
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            {{$summary['expiry_date'] ? date('d/m/Y', strtotime($summary['expiry_date'])) : '-'}}
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        @endif
        <!-- END Summary Table -->

        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> 
                        <strong>
                            @if(isset($carian) && !empty($carian))
                                QT Accept History : {{$carian}}
                            @else
                                QT Accept History (Latest Records)
                            @endif
                        </strong>
                    </h1>
                    
                    <!-- Add Export Button - Only show when QT No is provided -->
                    @if(isset($carian) && !empty($carian))
                        <button class="btn btn-success pull-right">
                            <a href="/find/qt/accept-history/export?doc_no={{$carian}}">
                                <i class="fa fa-download"></i> Export CSV
                            </a>
                        </button>
                    @endif
                </div>
                <div class="table-responsive">
                    <table id="qt-accept-history-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Task ID</th>
                                <th class="text-center">Task Number</th>
                                <th class="text-center">Composite Instance ID</th>
                                <th class="text-center">Doc No</th>
                                <th class="text-center">State</th>
                                <th class="text-center">Outcome</th>
                                <th class="text-center">Assignees</th>
                                <th class="text-center">Supplier</th>
                                <th class="text-center">Updated By</th>
                                <th class="text-center">Created Date</th>
                                <th class="text-center">Updated Date</th>
                                <th class="text-center">Expiry Date</th>
                                <th class="text-center">Version</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($qtinfo as $record)
                                <tr>
                                    <td class="text-center">{{$record->taskid}}</td>
                                    <td class="text-center">{{$record->tasknumber}}</td>
                                    <td class="text-center">
                                        <a target="_blank" href="/bpm/instance/find?composite_instance_id={{urlencode($record->compositeinstanceid)}}">
                                            {{$record->compositeinstanceid}}
                                        </a>
                                    </td>
                                    <td class="text-center">{{$record->customattributestring1}}</td>
                                    <td class="text-center">
                                        <span class="badge">
                                            {{$record->state}}
                                        </span>
                                    </td>
                                    <td class="text-center">{{$record->outcome}}</td>
                                    <td class="text-left">
                                        @if(count($record->processed_assignees) > 0)
                                            @php
                                                $assigneesCount = count($record->processed_assignees);
                                                $uniqueId = $record->taskid . '-' . $record->version;
                                            @endphp
                                            
                                            <ul style="margin: 0; padding-left: 15px; list-style-type: disc;">
                                                @foreach($record->processed_assignees as $index => $assignee)
                                                    <li class="@if($index >= 2)assignee-hidden-{{$uniqueId}}@endif" 
                                                        style="margin-bottom: 2px;@if($index >= 2) display: none;@endif">
                                                        <a target="_blank" href="/find/userlogin?login_id={{urlencode($assignee)}}">
                                                            {{ $assignee }}
                                                        </a>
                                                    </li>
                                                @endforeach
                                            </ul>
                                            
                                            @if($assigneesCount > 2)
                                                <a href="javascript:void(0)" 
                                                onclick="toggleAssignees('{{$uniqueId}}')" 
                                                id="toggle-link-{{$uniqueId}}" 
                                                style="font-size: 12px; color: #007bff;">
                                                    show more ({{$assigneesCount - 2}} more)
                                                </a>
                                            @endif
                                        @else
                                            -
                                        @endif
                                    </td>
                                    <td class="text-center">
                                        <a target="_blank" href="/find/byname/?nama_pembekal={{urlencode($record->company_name ?? '')}}">
                                            {{$record->company_name ?? '-'}}
                                        </a>
                                    </td>
                                    <td class="text-center">
                                        <a target="_blank" href="/find/userlogin?login_id={{$record->updatedby}}">{{$record->updatedby}}</a>
                                    </td>
                                    <td class="text-center">
                                        {{$record->createddate ? date('d/m/Y H:i:s', strtotime($record->createddate)) : '-'}}</td>
                                    <td class="text-center">
                                        {{$record->updateddate ? date('d/m/Y H:i:s', strtotime($record->updateddate)) : '-'}}</td>
                                    <td class="text-center">{{$record->expirationdate ? date('d/m/Y', strtotime($record->expirationdate)) : '-'}}</td>
                                    <td class="text-center">{{$record->version}}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    @endif

@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function () { TablesDatatables.init(); });</script>

    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();

        /* Initialize Datatables */
        var tableQtAcceptHistory = $('#qt-accept-history-datatable').DataTable({
            columnDefs: [{ orderable: false, targets: [] }],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']],
            order: [[0, 'asc'], [11, 'desc']] // Sort by Task ID (index 0) then Version (index 10) in ascending order
        });
    </script>

    <script>
        function toggleAssignees(uniqueId) {
            const hiddenItems = document.querySelectorAll('.assignee-hidden-' + uniqueId);
            const toggleLink = document.getElementById('toggle-link-' + uniqueId);
            
            if (hiddenItems[0].style.display === 'none') {
                // Show hidden items
                hiddenItems.forEach(item => item.style.display = 'list-item');
                toggleLink.textContent = 'show less';
            } else {
                // Hide items
                hiddenItems.forEach(item => item.style.display = 'none');
                const totalCount = document.querySelectorAll('.assignee-hidden-' + uniqueId).length;
                toggleLink.textContent = 'show more (' + totalCount + ' more)';
            }
        }

        function toggleTaskOutcomes(uniqueId) {
            const hiddenItems = document.querySelectorAll('.task-outcome-hidden-' + uniqueId);
            const toggleLink = document.getElementById('toggle-outcomes-' + uniqueId);
            
            if (hiddenItems[0].style.display === 'none') {
                // Show hidden items
                hiddenItems.forEach(item => item.style.display = 'block');
                toggleLink.textContent = 'show less';
            } else {
                // Hide items
                hiddenItems.forEach(item => item.style.display = 'none');
                const totalCount = document.querySelectorAll('.task-outcome-hidden-' + uniqueId).length;
                toggleLink.textContent = 'show more (' + totalCount + ' more)';
            }
        }
    </script>

@endsection