<?php

namespace App\Services\Traits\LogTrace;

use App\Model\LogTrace\LogTraceCubeModel;
use App\Model\LogTrace\LogTraceUserRequest;
use Carbon\Carbon;
use DateInterval;
use DatePeriod;
use DateTime;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use SSH;

/**
 * Description of Dynamic Tracing Log
 *
 * <AUTHOR>
 */
trait TraceLogService
{

    /**
     * Get user activity log by criteria
     * @param type Object
     * @return log list
     */
    public function getLogByCriteria($request)
    {
        try {
            $search_dt = Carbon::createFromFormat('Y-m-d', $request->dt_from);
            $search_y = $search_dt->year;
            $search_y2 = substr($search_y, 2, 2);
            $search_m = $search_dt->month;
            $search_m2 = strlen($search_m) < 2 ? '0' . $search_m : $search_m;
            $search_d = $search_dt->day;
            $search_d2 = strlen($search_d) < 2 ? '0' . $search_d : $search_d;

            $target_partition_table = 'userlog_py' . $search_y2 . 'm' . $search_m2 . 'd' . $search_d2;

            $draw = $request->get('draw');
            $start = $request->get("start");
            $rowperpage = $request->get("length"); // Rows display per page 

            $columnIndex_arr = $request->get('order');
            $columnName_arr = $request->get('columns');
            $order_arr = $request->get('order');
            $search_arr = $request->get('search');

            $columnIndex = $columnIndex_arr[0]['column']; // Column index
            $columnName = $columnName_arr[$columnIndex]['data']; // Column name
            $columnSortOrder = $order_arr[0]['dir']; // asc or desc
            $searchValue = $search_arr['value']; // Search value

            $query = DB::connection('pgsql_dynatrace')->table($target_partition_table);
            if (isset($request->user_id) && $request->user_id != null) {
                $query->where('login_id', $request->user_id);
            }

            if (isset($request->session_id) && $request->session_id != null) {
                $query->where('session_id', 'like', $request->session_id . '%');
            }

            $query->where('log_dt', '>=', $request->dt_from . ' ' . $request->ts_from);
            $query->where('log_dt', '<=', $request->dt_from . ' ' . $request->ts_to);
            $query->where(function ($query) use ($searchValue) {
                $query->orwhere('request_url', 'like', '%' . $searchValue . '%')
                    ->orwhere('friendly_url', 'like', '%' . $searchValue . '%')
                    ->orwhere('request_method', 'like', '%' . $searchValue . '%')
                    ->orwhere('portlet_id', 'like', '%' . $searchValue . '%')
                    ->orwhere('server_node', 'like', '%' . $searchValue . '%');
            });
            $query->distinct();
            $query->selectRaw("to_timestamp(to_char(log_dt,'YYYY-MM-DD HH24:MI'),'YYYY-MM-DD HH24:MI') as log_dt");
            $query->addselect(
                'login_id',
                'session_id',
                'portlet_id',
                'portlet_page',
                'request_method',
                'request_url',
                'friendly_url',
                'user_agent',
                'response_status',
                'server_node',
                'server_name',
                'environment'
            );

            $totalRecords = count($query->get());

            $log_sql = DB::connection('pgsql_dynatrace')->table($target_partition_table);
            if (isset($request->user_id) && $request->user_id != null) {
                $log_sql->where('login_id', $request->user_id);
            }

            if (isset($request->session_id) && $request->session_id != null) {
                $log_sql->where('session_id', 'like', $request->session_id . '%');
            }

            $log_sql->where('log_dt', '>=', $request->dt_from . ' ' . $request->ts_from);
            $log_sql->where('log_dt', '<=', $request->dt_from . ' ' . $request->ts_to);
            $log_sql->distinct();
            $log_sql->selectRaw("to_timestamp(to_char(log_dt,'YYYY-MM-DD HH24:MI'),'YYYY-MM-DD HH24:MI') as log_dt");
            $log_sql->addselect(
                'login_id',
                'session_id',
                'portlet_id',
                'portlet_page',
                'request_method',
                'request_url',
                'friendly_url',
                'user_agent',
                'response_status',
                'server_node',
                'server_name',
                'environment'
            );

            $log_sql->orderBy($columnName, $columnSortOrder);
            $log_sql->where(function ($log_sql) use ($searchValue) {
                $log_sql->orwhere('request_url', 'like', '%' . $searchValue . '%')
                    ->orwhere('friendly_url', 'like', '%' . $searchValue . '%')
                    ->orwhere('request_method', 'like', '%' . $searchValue . '%')
                    ->orwhere('portlet_id', 'like', '%' . $searchValue . '%')
                    ->orwhere('server_node', 'like', '%' . $searchValue . '%');
            });
            $log_sql->skip($start);
            $log_sql->take($rowperpage);
            $records = $log_sql->get();


            $response = array(
                "draw" => intval($draw),
                "recordsTotal" => $totalRecords,
                "recordsFiltered" => $totalRecords,
                "data" => $records
            );

            return json_encode($response);
        } catch (Exception $e) {
            Log::error("message => " . $e->getMessage());

            $response = array(
                "draw" => intval($draw),
                "recordsTotal" => 0,
                "recordsFiltered" => 0,
                "data" => []
            );

            return json_encode($response);
        }
    }

    public function getLogById($log_id)
    {
        $result = DB::connection('pgsql_dynatrace')->table('userlog')->where('id', $log_id)->first();

        return json_encode($result);
    }

    public function monitorTmpLogFileFailToProcess()
    {
        $commands = [
            "find /opt/ep-trace/log/ -type f ! -size 0 | sort",
            'exit',
        ];
        $file_proc_fails = $this->sshGetListFileNameTrc($commands);

        return $file_proc_fails;
    }

    /**
     * @$dateSearch must be string as format yyyymmdd
     */
    public function monitorTmpLogFileFailToProcessByUserAndDate($loginId,$dateSearch)
    {
        $commands = [
            "grep  -l $loginId /opt/ep-trace/log/prdportal*.prod.node*.$dateSearch-* | sort",
            'exit',
        ];
        $file_proc_fails =  $this->sshGetListFileNameTrc($commands);

        return $file_proc_fails;
    }

     /**
     * @$dateSearch must be string as format yyyymmdd
     */
    public function monitorTmpLogFileFailToProcessByDate($dateSearch)
    {
        $commands = [
            "find /opt/ep-trace/log/prdportal*.prod.node*.$dateSearch*  -type f ! -size 0 | sort",
            'exit',
        ];
        $file_proc_fails =  $this->sshGetListFileNameTrc($commands);

        return $file_proc_fails;
    }

    public function sshGetListFileNameTrc( $commands )
    {
        $file_proc_fails = array();
        //  prdportal04.prod.node1.20221125-14:32.trc
        SSH::into('log-trace')->run($commands, function ($line) use (&$file_proc_fails) {
            $data = $line . PHP_EOL;
            $data_arr = preg_split('/\n/', $data);
            $today = Carbon::now()->format('Ymd');
            foreach ($data_arr as $data_str) {
                $ts_file_fmt = substr($data_str, -9);
                $split_ts_fmt = explode(".", $ts_file_fmt);
                $dt_file_created = substr($data_str, -18, 8);
                if (count($split_ts_fmt) > 1 && $split_ts_fmt[1] === 'trc') {
                    $log_ts = strtotime($split_ts_fmt[0]);
                    $current_ts = strtotime(Carbon::now()->format('H:i'));
                    $final = explode("/", trim($data_str));
                    if (is_numeric($dt_file_created)) {
                        if ($dt_file_created === $today) {
                            if (($current_ts - $log_ts) / 60 > 10) {
                                $final = explode("/", trim($data_str));
                                if (count($final) == 5)
                                    array_push($file_proc_fails, $final[4]);
                            }
                        } else {
                            if (count($final) == 5)
                                array_push($file_proc_fails, $final[4]);
                        }
                    }
                }
            }
        });

        return $file_proc_fails;
    }

    public function getTmpLogErrorMessages()
    {
        $commands = [
            'cd /tmp',
            'grep "[0-9]\{6\}-[0-9]\{2\}:[0-9]\{2\} \[ERROR]".* trace.log',
            'exit',
        ];
        $file_errors = array();
        SSH::into('log-trace')->run($commands, function ($line) use (&$file_errors) {
            $data = $line . PHP_EOL;
            $errors = preg_split('/\n/', $data);
            foreach ($errors as $err) {
                array_push($file_errors, $err);
            }
        });

        return $file_errors;
    }

    public function emptyLogErrorContent()
    {
        $commands = [
            'truncate -s0 /tmp/trace.log'
        ];
        SSH::into('log-trace')->run($commands, function ($line) {
            $data = $line . PHP_EOL;
            Log::info($data);
        });

        return "Completed";
    }

    public function patchTmpLogFileFail($patch_dt, $patch_ts_fr, $patch_ts_to)
    {
        $patch_sts = array();
        $time_from = new DateTime($patch_dt . ' ' . $patch_ts_fr);
        $time_to = new DateTime($patch_dt . ' ' . $patch_ts_to);

        $minutes_to_add = 1;
        $time_to->add(new DateInterval('PT' . $minutes_to_add . 'M'));

        $interval = DateInterval::createFromDateString('+1 minutes');
        $period = new DatePeriod($time_from, $interval, $time_to);


        foreach ($period as $dt) {
            try {
                $commands = ['cd /opt/ep-trace', 'python3 patch.py ' . $dt->format("Ymd H:i"), 'exit',];

                Log::info($commands);
                SSH::into('log-trace')->run($commands, function ($line) use (&$patch_sts) {
                    $data = $line . PHP_EOL;
                    array_push($patch_sts, $data);
                });
            } catch (Exception $e) {
                array_push($patch_sts, $e->getMessage());
            }
        }

        return $patch_sts;
    }

    public function getEmptyLogFile()
    {
        $commands = [
            "find /opt/ep-trace/log/ -type f -size 0",
            'exit',
        ];
        $deleted_files = array();
        SSH::into('log-trace')->run($commands, function ($line) use (&$deleted_files) {
            $data = $line . PHP_EOL;
            $data_arr = preg_split('/\n/', $data);
            $today = Carbon::now()->format('Ymd');
            foreach ($data_arr as $data_str) {
                $ts_file_fmt = substr($data_str, -9);
                $split_ts_fmt = explode(".", $ts_file_fmt);
                $dt_file_created = substr($data_str, -18, 8);
                if (count($split_ts_fmt) > 1 && $split_ts_fmt[1] === 'trc') {
                    $log_ts = strtotime($split_ts_fmt[0]);
                    $current_ts = strtotime(Carbon::now()->format('H:i'));
                    $final = explode("/", trim($data_str));
                    if (is_numeric($dt_file_created)) {
                        if ($dt_file_created === $today) {
                            if (($current_ts - $log_ts) / 60 > 10) {
                                $final = explode("/", trim($data_str));
                                if (count($final) == 5)
                                    array_push($deleted_files, $final[4]);
                            }
                        } else {
                            if (count($final) == 5)
                                array_push($deleted_files, $final[4]);
                        }
                    }
                }
            }
        });

        return $deleted_files;
    }

    public function emptyLogTrace()
    {
        $clear_trace_log_commands = ['cd /tmp', '> trace.log', 'exit'];
        $clear_sts = array();

        try {
            SSH::into('log-trace')->run($clear_trace_log_commands, function ($line) use (&$clear_sts) {
                $data = $line . PHP_EOL;
                array_push($clear_sts, $data);
            });
        } catch (Exception $e) {
            array_push($clear_sts, $e->getMessage());
        }

        $deletedEmptyLogFiles = $this->getEmptyLogFile();
        if ($deletedEmptyLogFiles) {
            foreach ($deletedEmptyLogFiles as $delfile) {
                try {
                    $commands = [
                        "cd /opt/ep-trace/log/",
                        'rm -rf ' . $delfile,
                        'exit',
                    ];
                    SSH::into('log-trace')->run($commands, function ($line) {
                        $data = $line . PHP_EOL;
                        Log::info($data);
                    });
                } catch (Exception $e) {
                    Log::info($e->getMessage());
                }
            }
        }

        return $clear_sts;
    }

    public function getDailyAccessByUser($filter_dt)
    {
        if ($filter_dt == null || $filter_dt === "") {
            $filter_dt = Carbon::now()->format('Y-m-d');
        }

        return LogTraceUserRequest::whereDate('created_date', $filter_dt)->get();
    }

    public function getDailyRequestByEnvAndServerNode($filter_dt)
    {
        if ($filter_dt == null || $filter_dt === "") {
            $filter_dt = Carbon::now()->format('Y-m-d');
        }

        return LogTraceCubeModel::select('server_node', DB::raw('sum(total) as total'))
            ->whereDate('log_dt', $filter_dt)
            ->groupBy('server_node')
            ->get();
    }

    public function getLogTraceGrpByFriendlyUrl($filter_dt)
    {
        if ($filter_dt == null || $filter_dt === "") {
            $filter_dt = Carbon::now()->format('Y-m-d');
        }

        $result = LogTraceCubeModel::select('friendly_url', DB::raw('sum(total) as total'))
            ->whereDate('log_dt', $filter_dt)
            ->whereNotNull('friendly_url')
            ->groupBy('friendly_url')
            ->get();

        return $result;
    }

    public function grepBacklogBySpecificDateTime($request)
    {
        $name = $request->name;
        $node = $request->node;
        $portal_arr = array();

        if ($name === 'all') {
            array_push($portal_arr, 'prdportal01');
            array_push($portal_arr, 'prdportal02');
            array_push($portal_arr, 'prdportal05');
            array_push($portal_arr, 'prdportal06');
        } else {
            array_push($portal_arr, $name);
        }

        $grep_date = $request->grep_dt;
        $grep_from = $request->grep_ts_fr;
        $grep_to = $request->grep_ts_to;
        $today = new Carbon;

        $grep_arr = array();
        $time_from = new DateTime($grep_date . ' ' . $grep_from);
        $time_to = new DateTime($grep_date . ' ' . $grep_to);

        $minutes_to_add = 1;
        $time_to->add(new DateInterval('PT' . $minutes_to_add . 'M'));

        $interval = DateInterval::createFromDateString('+1 minutes');
        $period = new DatePeriod($time_from, $interval, $time_to);

        foreach ($portal_arr as $portal) {
            $source = '/data/rsyslog/log/prd/' . $portal . "/" . $node . ".log";
            $target = "/opt/ep-trace/log/" . $portal . ".prod." . $node . ".";

            try {
                foreach ($period as $dt) {
                    $file = $target . $dt->format("Ymd") . "-" . $dt->format("H:i") . ".trc";
                    $grep = " | grep -a ^" . $dt->format("Y-m-d") . "T" . $dt->format("H:i") . ".* | sort | uniq > " . $file;
                    if ($today > $dt) {
                        date_add($dt, DateInterval::createFromDateString('+1 days'));
                        $source .= "-" . $dt->format("Ymd");
                    }

                    $commands = [
                        "cat " . $source . $grep,
                        "exit"
                    ];

                    Log::info($commands);

                    SSH::into('log-trace')->run([
                        "rm -rf " . $file,
                        "exit"
                    ], function ($line) use (&$grep_arr) {
                        $data = $line . PHP_EOL;
                        array_push($grep_arr, $data);
                    });

                    SSH::into('log-trace')->run($commands, function ($line) use (&$grep_arr) {
                        $data = $line . PHP_EOL;
                        array_push($grep_arr, $data);
                    });

                    $source = '/data/rsyslog/log/prd/' . $portal . "/" . $node . ".log";
                }
            } catch (Exception $e) {
                array_push($grep_arr, $e->getMessage());
            }
        }

        return $grep_arr;
    }

    public function runJobByCriteria($request)
    {
        $job_name = $request->job_name;
        $log_date = $request->log_date;
        Artisan::call($job_name . " " . $log_date);

        return ['status' => 'Ok', 'message' => 'your request successfully sent to artisan call.'];
    }
}
