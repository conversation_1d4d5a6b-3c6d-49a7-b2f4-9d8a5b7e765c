<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use SSH;
use DB;
use App\EpSupportActionLog;

class HandleMminfSpecialCharSchedule extends Command {

    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleMminfSpecialChar';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Issue cause of special character in DI_MMINF and CM_ITEM';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__ . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);

        try {
            $listData = DB::connection('oracle_nextgen_fullgrant')
            ->select("SELECT ASCIISTR(MATERIAL_DESC) AS ascii_material_desc ,ASCIISTR(MATERIAL_ADD_DESC) AS ascii_material_add_desc, m.* 
            FROM DI_MMINF m 
            WHERE ASCIISTR(MATERIAL_DESC) LIKE '%\%' 
            OR 
            ASCIISTR(MATERIAL_ADD_DESC) LIKE '%\%' 
            ORDER BY created_date desc ");
            $listData = collect($listData);
            MigrateUtils::logDump('Check Count: '.$listData->count());
            $listData = collect($listData->take(30)->all());
            MigrateUtils::logDump('get 30 records to run: '.$listData->count());

            foreach ($listData as $objData){
                $updateDate = array(
                    'is_sent' => 0
                );
                MigrateUtils::logDump('material_desc: '.$objData->material_desc. ' ,material_add_desc: '.$objData->material_add_desc);
                $material = $this->fixSpecialChar($objData->material_desc);
                $updateDate['material_desc'] = $material; 

                $materialAdd = $this->fixSpecialChar($objData->material_add_desc);
                $updateDate['material_add_desc'] = $materialAdd; 

                MigrateUtils::logDump(json_encode($updateDate));
                DB::connection('oracle_nextgen_fullgrant')
                    ->table('DI_MMINF')
                    ->where('mminf_id',$objData->mminf_id)
                    ->update($updateDate);

            }

        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__ . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail($exc->getTraceAsString());
        }
        
    }
    
    protected function fixSpecialChar($material){

        $chars = str_split($material);
        foreach ($chars  as $char) {
            if(mb_check_encoding($char, 'ASCII') === false){
                $material  = str_replace($char,"",$material );
            }
        }

        return $material;
    }
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleMminfSpecialCharSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
