<?php 

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Guzzle;
use Response;
use Carbon\Carbon;
use Symfony\Component\HttpFoundation\StreamedResponse;

class AutomationController extends Controller{

    private $refcode;
    private $automation_endpoint;

    

    public function __construct(){
        $this->middleware('auth');
        $this->automation_endpoint = env('MIDDLEWARE_AUTOMATION_API','http://**************'); //http://localhost:8001 //http://**************/automation       
        // $this->automation_endpoint = env('MIDDLEWARE_AUTOMATION_API','http://localhost:8088'); //http://localhost:8001 //http://**************/automation       
    }

    public function index(){
        $this->refcode = strtolower(auth()->user()->user_name) . "-" . $this->RandomNumber();
        return view('automation.index',[
            'refcode' => $this->refcode
        ]);
    }

    public function ClearCookies($refcode){
        $uri = "$this->automation_endpoint/selenium-runner/ep-portal/clear-cookies/$refcode";
        Guzzle::get($uri);
    }

    public function Sse(Request $request, $refcode){
        $response = new StreamedResponse();
        $response_x = Guzzle::get("$this->automation_endpoint/sse/event-message/$refcode");
        $response_x_raw = json_decode($response_x->getBody());
        $data = (!empty($response_x_raw)) ? json_encode($response_x_raw) : false;
        $response->setCallback(function () use ($data){
            echo 'data: ' . $data . "\n\n";
            ob_flush();
            flush();
            usleep(200000);
        });

        $response->headers->set('Content-Type', 'text/event-stream');
        $response->headers->set('X-Accel-Buffering', 'no');
        $response->headers->set('Cach-Control', 'no-cache');
        return $response;
    }

    private function RandomNumber(){
        return str_pad(mt_rand(0, 999999), 6, '0', STR_PAD_LEFT);
    }

}