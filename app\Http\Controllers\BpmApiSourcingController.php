<?php

namespace App\Http\Controllers;

use App\Services\Traits\PayloadGeneratorService;
use App\Services\Traits\BpmApiService;
use Illuminate\Http\Request;
use Log;
use App\EpSupportActionLog;
use Carbon\Carbon;
use App\Services\Traits\BPMService;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SourcingService; 

class BpmApiSourcingController extends Controller {

    use PayloadGeneratorService;
    use BpmApiService;
    use BPMService;
    use SourcingService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function searchTaskSourcingQTCreation(Request $request) {

        $docNo = null;
        $listdataService = null;
        $elements = null;
        $quotationTenderPayload = null;
        $payload = null;

        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'qt_no' => 'required'
            ]);

            $docNo = trim($request->qt_no);

            if ($docNo != null) {
                $quotationTenderPayload = $this->getQtQuotationTenderCreation($docNo);
                $payload = str_replace("&", "&amp;", $quotationTenderPayload[0]->qt_tender);
            }

            $listService = $this->findApiBPMGetListServiceManager();
            if ($listService["status"] != null && $listService["status"] === 'Success') {
                $listdataService = $listService["result"];
            }

            foreach ($listdataService["data"] as $name) {
                if ($name["name"] === 'SourcingQT') {
                    foreach ($name["processes"] as $process) {
                        if ($process["name"] === 'QuotationTenderCreation') {
                            foreach ($process["triggers"] as $trigger) {
                                if ($trigger["name"] === 'start') {
                                    $elements = $trigger["elements"];
                                }
                            }
                        }
                    }
                }
            }
        }

        return view('bpm_api.sourcing.qtcreation', [
            'elements' => $elements,
            'quotationTenderPayload' => $quotationTenderPayload,
            'payload' => $payload
        ]);
    }

    public function searchTaskSourcingQTEvaluation(Request $request) {
        $docNo = null;
        $listdataService = null;
        $elements = null;
        $scEvaluationPayload = null;
        $payload = null;

        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'qt_no' => 'required'
            ]);

            $docNo = trim($request->qt_no);

            if ($docNo != null) {
                $scEvaluationPayload = $this->getQtScEvaluation($docNo);
                $payload = str_replace("&", "&amp;", $scEvaluationPayload[0]->evaluation_payload);
            }

            $listService = $this->findApiBPMGetListServiceManager();
            if ($listService["status"] != null && $listService["status"] === 'Success') {
                $listdataService = $listService["result"];
            }

            foreach ($listdataService["data"] as $name) {
                if ($name["name"] === 'SourcingQT') {
                    foreach ($name["processes"] as $process) {
                        if ($process["name"] === 'Evaluation') {
                            foreach ($process["triggers"] as $trigger) {
                                if ($trigger["name"] === 'prestart') {
                                    $elements = $trigger["elements"];
                                }
                            }
                        }
                    }
                }
            }
        }

        return view('bpm_api.sourcing.qtevaluation', [
            'elements' => $elements,
            'scEvaluationPayload' => $scEvaluationPayload,
            'payload' => $payload
        ]);
    }

    public function supplierFinalization(Request $request) {
        $docNo = null;
        $elements = null;
        $finalizationPayload = null;
        $payload = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'qt_no' => 'required'
            ]);

            $docNo = trim($request->qt_no);

            if ($docNo != null) {
                $finalizationPayload = $this->getQtSuppFinalization($docNo);
                $payload = str_replace("&", "&amp;", $finalizationPayload[0]->finalization_payload);
            }

            $listService = $this->findApiBPMGetListServiceManager();
            if ($listService["status"] != null && $listService["status"] === 'Success') {
                $listdataService = $listService["result"];
            }

            foreach ($listdataService["data"] as $name) {
                if ($name["name"] === 'SourcingQT') {
                    foreach ($name["processes"] as $process) {
                        if ($process["name"] === 'SupplierFinalization') {
                            foreach ($process["triggers"] as $trigger) {
                                if ($trigger["name"] === 'start') {
                                    $elements = $trigger["elements"];
                                }
                            }
                        }
                    }
                }
            }
            
        }
        return view('bpm_api.sourcing.supplierfinalization', [
            'elements' => $elements,
            'finalizationPayload' => $finalizationPayload,
            'payload' => $payload
        ]);
    } 

    public function refireEvaluation(Request $request) {        
        $docNo = null;
        $listdataService = null;
        $elements = array();
        $scEvaluationPayload = null;
        $payload = null;
        $qtDetails = null;
        $runningInstances = array(); 
        $docIdSearch = null;
        $reqProcess = null;
        $listCommittee = null;
        $totalOcMember = null;
        $totalTecMember = null;
        $totalFecMember = null;
        $status = 'Failed';

        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'qt_no' => 'required',
                'process_name' => 'required'
            ]);

            $docNo = trim($request->qt_no);
            $reqProcess = $request->process_name;
            $module = 'SourcingQT';
            $process = 'Evaluation';
            $trigger = 'prestart';
            $count = 0;
             
            $qtDetails = $this->getDetailQuatationTenderInfo($docNo);
            $listCommittee = $this->getListAllCommitteeMembersQT($docNo);
            if($qtDetails) {
                $docIdSearch = $qtDetails[0]->qt_id;
                $listInstances = $this->getInstanceIdBPMbyDocIdModule($docIdSearch,$module); 
                foreach($listInstances as $comp) {
                    //check status composite instance
                    $instanceID = $comp->composite_instance_id;
                    $checkStatusInstance = $this->findAPIProcessManagerBPMByInstance($instanceID);
                    if($checkStatusInstance["status"] === 'Success') {
                        $compositeInstanceDetail = $checkStatusInstance['result'];
                        $compositeInstanceState = $compositeInstanceDetail['compositeState'];
                        if($compositeInstanceState == 0) {
                            array_push($runningInstances, $instanceID);
                            $count++;
                            Log::info(__METHOD__ . "> Instance: $instanceID State Is Running. Please Terminate Before Proceed."); 
                        }
                        $status = 'Success';
                    } else { 
                        Log::info(__METHOD__ . "> Failed To Find Instance: $instanceID Details.");
                    }
                }
            }  
            $collectCommittee = collect($listCommittee);
            $totalOcMember = $collectCommittee->where('member_code','OC')->where('role_code','M')->count();
            $totalTecMember = $collectCommittee->where('member_code','TEC')->where('role_code','M')->count();
            $totalFecMember = $collectCommittee->where('member_code','FEC')->where('role_code','M')->count();
        } 
        return view('bpm_api.sourcing.evaluationTecFec', [ 
            'qtDetails' => $qtDetails,
            'runningInstances' => $runningInstances,
            'process_name' => $reqProcess,
            'doc_id' => $docIdSearch,
            'totalOcMember' => $totalOcMember,
            'totalTecMember' => $totalTecMember,
            'totalFecMember' => $totalFecMember,
            'status' => $status
        ]);
    }

    public function ajaxCheckInstanceID(Request $request) {
        $docNo = $request->docNo;
        $compInstance = '';
        $objCompositeInstance = $this->getTaskBpmByDocNo($docNo);
        if ($objCompositeInstance && count($objCompositeInstance) > 0) {
            foreach($objCompositeInstance as $obj){  
                if($obj->state == 'ASSIGNED') {
                    $compInstance = $obj->compositeinstanceid; 
                } 
            }
        } else {
            Log::info(__METHOD__ . "> Failed Get Bpm Composite Instance For QT Number: $docNo"); 
        }
        return $compInstance;
    }

    public function ajaxActionEvaluation(Request $request) {
        //ajax request 
        $docNo = $request->docNo;
        $process = $request->process; 
        $activity = $request->activity;
        $groupTask = $request->groupTask;
        $docId = $request->docId;
        $stopProcess = $request->stopProcess;
        $module = 'SourcingQT';
        $result = 'Failed'; 
        $elements = array();

        if($process == 'Evaluation') {
            if($activity == 'prestart') {
                $scEvaluationPayload = $this->getQtScEvaluation($docNo);
                if($scEvaluationPayload) {
                    $payload = str_replace("&", "&amp;", $scEvaluationPayload[0]->evaluation_payload);
                    $fullPayload = '<SC_Evaluation_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_Evaluation_Data">'.$payload;
                    $taskPerformer = $scEvaluationPayload[0]->task_performer; 
                    array_push($elements,
                        ["name"=>"SC_Evaluation_Data","value"=>$fullPayload,"type"=>"object"],
                        ["name"=>"document_number","value"=>$docNo,"type"=>"string"],
                        ["name"=>"task_performer","value"=>$taskPerformer,"type"=>"string"],
                        ["name"=>"reevaluate","value"=>"false","type"=>"boolean"]); 
                    $triggerServicepreStart = $this->findApiBPMCreateServiceManager($module,$process,$activity,$elements);
                    if($triggerServicepreStart && $triggerServicepreStart['status'] === 'Success') {
                        $result = 'Success';
                    }
                }
            } else if($activity == 'start') {
                array_push($elements,
                ["name"=>"cid","value"=>$docId,"type"=>"long"]); 
                sleep(10);
                $triggerServiceStart = $this->findApiBPMCreateServiceManager($module,$process,'start',$elements);
                if($triggerServiceStart && $triggerServiceStart['status'] === 'Success') {
                    $result = 'Success';
                }
            }
        } else {
            $resultEvaluation = $this->evaluationProcess($docNo,$process,$activity,$groupTask,$stopProcess);
            $result = $resultEvaluation;
        } 

        $parameters = collect([]);
        $parameters->put("name", $request->url);
        $parameters->put("process", $process);
        $parameters->put("trigger", $activity);
        $parameters->put("elements", $elements);
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
        $actionName = 'BPM-Refire-QT-Evaluation';

        EpSupportActionLog::saveActionLog($actionName, 'Web Service', $parameters, $parameters, $result);
         
        return $result;
    }

    public function evaluationProcess($docNo,$process,$activity,$groupTask,$stopProcess) {
        $processName = $process.'Evaluation';
        $meetingName = "Coordinate $process Meeting";
        $finalizeName ="Finalize $process Evaluation";
        $endorseName = "Endorse $process Evaluation";
        $signoffName = "SignOff $process Evaluation"; 
        $result = 'Failed'; 

        if($activity == 'Coordinate') {
            $meetingBpmTask = $this->findBpmDetailTask($docNo,$processName,$meetingName);
            Log::info($meetingBpmTask);
            if($meetingBpmTask) {
                Log::info(__METHOD__ . "> Success Find Coordinate Process Name: $processName > Activity Name: $meetingName For QT Number: $docNo. Proceed To Submit Task"); 
                $taskNumber = $meetingBpmTask['bpm_tasknumber'];
                $compositeInstanceId = $meetingBpmTask['bpm_compositeinstanceid'];
                sleep(10);
                if($stopProcess == 'yes' && $taskNumber !== ''){
                    $result = 'Success';
                } else {
                    // Coordinate Meeting > SUBMIT
                    $executeMeetingBpmTask = $this->executeBpmDetailTask($taskNumber,$processName,$meetingName,'SUBMIT','');
                    $result = $executeMeetingBpmTask;
                } 
            }
        } else if($activity == 'Finalize') {
            $finalizeBpmTask = $this->findBpmDetailTask($docNo,$processName,$finalizeName);
            if($finalizeBpmTask) {
                Log::info(__METHOD__ . "> Success Find Finalize Process Name: $processName > Activity Name: $finalizeName For QT Number: $docNo. Proceed To Submit Task"); 
                $taskNumber = $finalizeBpmTask['bpm_tasknumber'];
                sleep(10);
                // Finalize Evaluation > SUBMIT (secretary)
                $executeFinalizeBpmTask = $this->executeBpmDetailTask($taskNumber,$processName,$finalizeName,'SUBMIT','');
                $result = $executeFinalizeBpmTask;
            } 
        } else if($activity == 'Endorse') {
            $endorseBpmTask1 = $this->findBpmDetailTask($docNo,$processName,$endorseName);
            if($endorseBpmTask1) {
                Log::info(__METHOD__ . "> Success Find Endorse Process Name: $processName > Activity Name: $endorseName For Group List $groupTask For QT Number: $docNo. Proceed To Endorse Task"); 
                $taskNumber = $endorseBpmTask1['bpm_tasknumber'];
                sleep(10);
                // Endorse Evaluation > ENDORSE
                $executeEndorseBpmTask1 = $this->executeBpmDetailTask($taskNumber,$processName,$endorseName,'ENDORSE',$groupTask);
                $result = $executeEndorseBpmTask1;
            }
            
        } else if($activity == 'SignOff') {
            $signoffBpmTask = $this->findBpmDetailTask($docNo,$processName,$signoffName);
            if($signoffBpmTask) {
                Log::info(__METHOD__ . "> Success Find SignOff Process Name: $processName > Activity Name: $signoffName For QT Number: $docNo. Proceed To Signoff Task"); 
                $taskNumber = $signoffBpmTask['bpm_tasknumber'];
                sleep(10);
                // Signoff Evaluation > SIGNOFF (chairperson)
                $executeSignoffBpmTask = $this->executeBpmDetailTask($taskNumber,$processName,$signoffName,'SIGNOFF','');
                $result = $executeSignoffBpmTask;
            }
        }
        return $result;
    }

    public function findBpmDetailTask($docNo,$processName,$activityName) {
        $result = collect();
        $objCompositeInstance = $this->getTaskBpmByDocNo($docNo);
        if ($objCompositeInstance && count($objCompositeInstance) > 0) {
            foreach($objCompositeInstance as $obj){  
                if($obj->processname == $processName && $obj->activityname == $activityName && $obj->state == 'ASSIGNED') {
                    $result->put("bpm_tasknumber", $obj->tasknumber); 
                    $result->put("bpm_compositeinstanceid", $obj->compositeinstanceid); 
                } 
            }
        } else {
            Log::info(__METHOD__ . "> Failed Get Bpm Task By Doc Number For QT Number: $docNo. Please Check!"); 
        }
        return $result;
    }

    public function executeBpmDetailTask($taskNumber,$processName,$activityName,$action,$groupTaskKey) {
        $result = 'Failed';
        $detailBpmTask = $this->findAPITaskIDBPMList($taskNumber);     
        if($detailBpmTask && $detailBpmTask["status"] === 'Success' &&  $detailBpmTask["status"] != null){
            Log::info(__METHOD__ . "> BPM Task detail Found For Task Number: $taskNumber. Proceed To Execute $processName > $activityName Task");  
            $resultDetailBpmTask = $detailBpmTask["result"];

            //check group tasks if exist
            $taskId = $resultDetailBpmTask['taskId'];
            if($groupTaskKey !== '') {
                $getTaskGroup = $this->findApiBPMGetTaskGroupByTaskId($taskId);
                if ($getTaskGroup != null && $getTaskGroup['status'] == 'Success') {
                    $taskGroup = $getTaskGroup["result"];
                    foreach($taskGroup as $grp) {
                        if($grp['key'] === $groupTaskKey) {
                            Log::info(__METHOD__ . ' > BPM Group Task detail found. Proceed Find Task Detail By Task ID');  
                            $detailGrpBpmTask = $this->findApiWorklistTaskDetail($grp['value']); 
                            if($detailGrpBpmTask && $detailGrpBpmTask["status"] === 'Success' &&  $detailGrpBpmTask["status"] != null){
                                Log::info(__METHOD__ . "> BPM Task detail Found For Task ID: $taskId. Proceed To Execute $processName > $activityName Task");  
                                $resultDetailBpmTask = $detailGrpBpmTask["result"];
                            } else {
                                Log::info(__METHOD__ . "> BPM Task detail Not Found For Task ID: $taskId. Please Check!");  
                            }
                        }
                    }
                }else {
                    Log::info(__METHOD__ . "> Group Task Detail Not Found. Please check!");  
                }
            }
            
            if($resultDetailBpmTask !== '') {
                $payloadTaskBpm = $resultDetailBpmTask['payload'][0];
                $updatePayloadTaskBpm = str_replace('<?xml version = \'1.0\' encoding = \'UTF-8\'?>',"",$payloadTaskBpm);
                
                $docNumberTaskBpm = $resultDetailBpmTask['docNumber']; 
                $docTypeTaskBpm = $resultDetailBpmTask['docType'];
                $docIdTaskBpm = (string)$resultDetailBpmTask['docId'];
                $docStatus = (string)$resultDetailBpmTask['docStatus']; 
                $docTaskId = $resultDetailBpmTask['taskId']; 
                $docAssignee = $resultDetailBpmTask['assignees'][0];
    
                $param = array();
                array_push($param,$docNumberTaskBpm,$docTypeTaskBpm,$docIdTaskBpm,$docStatus);  
                
                //submit task
                sleep(10); 
                $submitTask = $this->updateExecuteActionAPI($docTaskId,$docAssignee,$updatePayloadTaskBpm,$action,$param);
                if($submitTask && $submitTask['status'] === 'Success') {
                    $result = $submitTask["status"];
                } 
            } else {
                Log::info(__METHOD__ . "> BPM Task detail Not Found To Execute Task. Please Check!");  
            }
         } else {
            Log::info(__METHOD__ . "> BPM Task detail Not Found For Task Number: $taskNumber. Please Check!");  
         }
         return $result;
    }
}
