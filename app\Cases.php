<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Cases extends Model {
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;

    
    public function caseCustom() {
        return $this->hasOne('App\caseCustom', 'id_c');
    }

    public function tasks() {
        return $this->belongsToMany('App\Task', 'tasks', 'parent_id')
                ->withPivot('id');
    }

}
