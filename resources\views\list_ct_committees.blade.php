@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianforms" action="{{url('/find/contract/committee')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Ahli Jawatan Kuasa Kontrak <br>
                <small>Senarai ahli jawatan kuasa bagi Kontrak.</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian Ahli Jawatan Kuasa Kontrak </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Contract No.</th>
                            <th class="text-center">Contract Version</th>
                            <th class="text-center">IC No.</th>
                            <th class="text-center">Name</th>
                            <th class="text-center">Role Code</th>
                            <th class="text-center">Role Desc</th>
                            <th class="text-center">Role Name</th>
                            <th class="text-center">CommitteeMemberId</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center"><a href="{{url('/find/contract/')}}/?cari={{$data->contract_no}}" target="_blank" > {{ $data->contract_no }}</a></td>
                                 <td class="text-center">{{ $data->contract_ver }}</td>
                                <td class="text-center">{{ $data->ic_passport }}</td>
                                <td class="text-left">{{ $data->member_name }}</td>
                                <td class="text-left">{{ $data->role_code }}</td>
                                <td class="text-left">{{ $data->role_desc }}</td>
                                <td class="text-left">{{ $data->role_name }}</td>
                                <td class="text-left">{{ $data->committee_member_id }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



