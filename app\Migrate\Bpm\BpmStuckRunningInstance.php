<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\SourcingService;
use Illuminate\Support\Arr;

class BpmStuckRunningInstance {

    use BPMService;
    use FulfilmentService;
    use BpmApiService;
    use SourcingService;

    public static function runFulfilmentStuckRunning() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $thisCls = new BpmStuckRunningInstance;

        $list = $thisCls->findListInstanceFulfilmentRunningStl();
        $total = count($list);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);
        foreach ($list as $data) {
            if(
                ( $data->component_name== 'DeliveryOrderFulfilment' 
                    || $data->component_name== 'OrderCancellation' 
                )
                
                && count($data->doc_no_list) == 0){

                MigrateUtils::logDump(__METHOD__ . ' valid to terminate. '.json_encode($data));
                MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                
            }
            /***
            foreach($data->doc_no_list as $rowDoc){
                if($rowDoc->doc_type == 'PO' || $rowDoc->doc_type == 'CO'){
                    $objPoco = $thisCls->getCurrentWorkflowByPoCo($rowDoc->doc_no);
                    if($objPoco->status_name == 'Closed'){
                        MigrateUtils::logDump(__METHOD__ . ' valid to terminate. PO is closed. '.json_encode($data). ' - '.json_encode($objPoco));
                        MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                        $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                        MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                        //dd('Completed');
                    }
                }
            }
            **/
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

    public static function runOrderStuckRunning() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $thisCls = new BpmStuckRunningInstance;

        $list = $thisCls->findListInstanceOrderRunningStl();
        $total = count($list);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);

        foreach ($list as $data) {
            //dump($data);
            if(
                ( $data->component_name== 'ContractRequestCreation' 
                    || $data->component_name== 'PurchaseRequestInitiation' 
                    || $data->component_name== 'ContractRequestInitiation' 
                )
                
                && count($data->doc_no_list) == 0){

                MigrateUtils::logDump(__METHOD__ . ' valid to terminate. '.json_encode($data));
                MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                
            }
            
        }
        
            /*** 
            foreach($data->doc_no_list as $rowDoc){
                if($rowDoc->doc_type == 'PO' || $rowDoc->doc_type == 'CO'){
                    $objPoco = $thisCls->getCurrentWorkflowByPoCo($rowDoc->doc_no);
                    if($objPoco->status_name == 'Closed'){
                        MigrateUtils::logDump(__METHOD__ . ' valid to terminate. PO is closed. '.json_encode($data). ' - '.json_encode($objPoco));
                        MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                        $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                        MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                        //dd('Completed');
                    }
                }
            }
            **/
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }


    public static function runSupplierManagementStuckRunning() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $thisCls = new BpmStuckRunningInstance;

        $list = $thisCls->findListInstanceSupplierMgmtRunningStl();
        $total = count($list);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);
        foreach ($list as $data) {
            $dateCube = Carbon::parse($data->modify_date);
            if($dateCube->year <= 2022 && $data->component_name == 'MOFSupplierRegistration'){
                MigrateUtils::logDump(__METHOD__ . ' data to terminate >> '.json_encode ($data));
                $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                //dd('cmpleted');
            }
            
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }


    public static function runSourcingQtStuckRunning() {
        $componentName = 'WorkflowStatusUpdate';
        // ,'QuotationTenderMaintenance','QuotationTenderCreation'
        //$componentName  = null;
        MigrateUtils::logDump(__METHOD__ . ' Starting ... componentName: '.$componentName );
        $thisCls = new BpmStuckRunningInstance;
        
        $list = $thisCls->findListInstanceSourcingQtRunningStl($componentName);
        $total = count($list);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);
        $counter = 0;
        foreach ($list as $data) {
            $dateModify = Carbon::parse($data->modify_date);
            $counter++;
            if($dateModify->year <= 2023){
                
                if(
                    $data->component_name== 'WorkflowStatusUpdate' 
                    && $data->state == 10 
                    && count($data->doc_no_list) == 0){
    
                    MigrateUtils::logDump(__METHOD__ . ' valid to terminate. '.json_encode($data));
                    MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                    $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                    MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
        
                }

                /*** 
                foreach($data->doc_no_list as $rowDoc){
                    $docNo = $rowDoc->doc_no;
                    if(strlen($docNo) == 0){
                        $docNo = $rowDoc->doc_no_other;
                    }
                    $docType= substr($docNo, 0, 2);
                    MigrateUtils::logDump(__METHOD__ . " >> $counter/$total) DocNo: ".$docNo. ' ,DocType: '.$docType);
                    if($docType == 'QT'|| $docType == 'LA' ){

                        if ($docType == 'LA'){
                            $listObjWf = collect($thisCls->getCurrentWorkflowSourcingLoa($docNo,$docType));
                        }else{
                            $listObjWf = collect($thisCls->getCurrentWorkflowSourcingQt($docNo,$docType));
                        }

                        
                        //QT
                        //"Cancelled"	"60015"
                        //"Quotation Has Been Cancelled Due To Proposal Validity Expired. Mof Approval Is Required For Further Action (If Any)"	"60045"
                        //"Proceed Manual Due To Ministry Restructuring"	"60042"
                        //"Cancelled due to PTJ Code Changed"	"60043"
                        //"Cancel Due To Ministry Restructuring"	"60041"
                        //"QT Cancellation Request From User Approved By BPK,MoF"	"60046"
                        //"Quotation/Tender Closed by Agency Due To Validity Period Expired/Special Approval"	"60047"
                        //"Awarded"	"60014"

                        //LA
                        //Proceed Manual Due To Ministry Restructuring (62511)
                        //Proceed Manual Due To PTJ Code Changed (62512)
                        //Pending Verification of Acknowledgement Receipt's Letter Of Acceptance (62505)
                        
                        if($docType == 'QT'){
                            $listObjWf = $listObjWf->whereIn('status_id',[60014,60015,60045,60042,60043,60041,60046,60047]);
                        }elseif ($docType == 'LA'){
                            $listObjWf = $listObjWf->whereIn('status_id',[62511,62512,62505]);
                        }
                       
                        if($listObjWf->count() > 0){
                            $objWf = $listObjWf->first();
                            if($objWf){
                                MigrateUtils::logDump(__METHOD__ . ' valid to terminate. QT Instance Info '.json_encode($data));
                                MigrateUtils::logDump(__METHOD__ . ' valid to terminate. QT Status Info'.json_encode($objWf));
                                MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                                $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                                MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                                MigrateUtils::logDump('');
                                //dd('Completed');
                            }
                        }else{
                            MigrateUtils::logDump(__METHOD__ . ' SKIP! QT still pending status');
                        }
                    }
                }

                **/
            }
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

    public static function runSourcingQtCancelledToTerminateTask() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $thisCls = new BpmStuckRunningInstance;
        $yearFilter = Carbon::now()->year;
        $yearFilter = 2018;
        $monthFilter = '12';
        $listQtCancelled = $thisCls->listQtCancelledByYear($yearFilter,$monthFilter);
        $total = count($listQtCancelled);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);

        $counter = 0;
        foreach($listQtCancelled as $obj){
            
            $listTask = collect($thisCls->getTaskBpmByDocNo($obj->qt_no));
            if($listTask->count() > 0){
                MigrateUtils::logDump(__METHOD__ . ' find task Qt No : '.$obj->qt_no. ' with latest status : '.$obj->status_name);
                $listCompositeInstanceId = $listTask->pluck('compositeinstanceid')->unique();
                MigrateUtils::logDump(__METHOD__ . ' ready to terminate '.json_encode($listCompositeInstanceId) );
                foreach($listCompositeInstanceId as $compositeInstanceId){
                    $objTask = $listTask->where('compositeinstanceid',$compositeInstanceId)->first();
                    $counter++;
                    MigrateUtils::logDump(__METHOD__ . ' valid to terminate QT '.$objTask->doc_no);
                    MigrateUtils::logDump(__METHOD__ . ' '.$counter.') Start terminate : composite_name: '.$objTask ->compositename.'!'.$objTask->compositeversion . ' ,composite_ID : '. $objTask->compositeinstanceid);
                    $res = $thisCls->submitTerminateInstance('default/'.$objTask->compositename.'!'.$objTask->compositeversion, $objTask->compositeinstanceid);
                    MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                    MigrateUtils::logDump('');
                    //dd('OK');
                }
                
            }
        }

    }
    public static function runDirectPurchaseStuckRunning() {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $thisCls = new BpmStuckRunningInstance;
        $componentName = 'SupplierCodification';
        $list = $thisCls->findListInstanceSourcingDpRunningStl($componentName);
        $total = count($list);
        MigrateUtils::logDump(__METHOD__ . ' total: '.$total);
        $counter = 0;
        foreach ($list as $data) {
            $dateModify = Carbon::parse($data->modify_date);
            //dump($data);
            if($dateModify->year <= 2021){
                $counter++;

                if(
                    $data->component_name== 'SupplierCodification' 
                    && count($data->doc_no_list) == 0){
    
                    MigrateUtils::logDump(__METHOD__ . " $counter/$total)  valid to terminate. ".json_encode($data));
                    MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                    $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                    MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                    
                }

                /*** 
                foreach($data->doc_no_list as $rowDoc){
                    $docNo = $rowDoc->doc_no;
                    if(strlen($docNo) == 0){
                        $docNo = $rowDoc->doc_no_other;
                    } 
                    $docType= substr($docNo, 0, 2);
                    MigrateUtils::logDump(__METHOD__ . "$counter/$total) DocNo: ".$docNo. ' ,DocType: '.$docType);
                    if($docType == 'RN' ){
                        $listObjWf = collect($thisCls->getCurrentWorkflowRequestNote($docNo,$docType));
                        dd($listObjWf);
                        $listObjWf->whereIn('status_id',[600150000])->all();
                        $objWf = $listObjWf->first();
                        dd($objWf);

                        if($objWf){
                            MigrateUtils::logDump(__METHOD__ . ' valid to terminate. QT is invalid! '.json_encode($data). ' - '.json_encode($objWf));
                            MigrateUtils::logDump(__METHOD__ . ' Start terminate : composite_name: '.$data->composite_name.'!'.$data->composite_revision . ' ,composite_ID : '. $data->cmpst_id);
                            $res = $thisCls->submitTerminateInstance('default/'.$data->composite_name.'!'.$data->composite_revision, $data->cmpst_id);
                            MigrateUtils::logDump(__METHOD__ . ' status terminate: '.json_encode($res));
                            MigrateUtils::logDump('');
                            //dd('Completed');
                        }
                    }
                }
                **/
            }
        }
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }

}
