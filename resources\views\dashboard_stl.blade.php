@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <!-- END Search Form -->
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
    <div class="row">
        <div class="col-lg-12">
            <div class="widget">
                <div class="widget-extra themed-background-dark"  class="text-center">
                    <h3 class="widget-content-light">
                        Stuck Task List <strong>Monitoring</strong>
                        <small class="pull-right"><strong>Request on </strong> {{Carbon\Carbon::now()->format('d-M-Y H:i:s')}}</small>
                    </h3>
                </div>                
            </div>            
        </div>
    </div> 
    <div class="row">
        <div class="col-lg-2">
            <div class="widget">                        
                <div id="dash_stlmonitoringSM">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
                <div class='row text-center'><h3><small>Stuck Task SM</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringQT">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task QT</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringDPSQ">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task DP SQ</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringDPRN">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task DP RN</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringDPCodify">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task DP Codify</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringRNTriggerOrder">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task RN Trigger Order</small></h3></div>
            </div>
        </div>  
    </div> 
    <div class="row">
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringPRCRInitiate">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task PR CR Initiate</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringDO">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL Initiate DO</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringIntegrationPRCR">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Integration PRCR</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLQueryPRCR">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Query PRCR</small></h3></div>
            </div>
        </div>         
         <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLAwaitingIgfmasPRCR">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Awaiting IGFMAS PRCR</small></h3></div>
            </div>
        </div> 
          <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLOrderPOCO">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Order POCO</small></h3></div>
            </div>
        </div>  
    </div> 
    <div class="row">
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLFLDebit">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL Debit</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringIntegrationFRN">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Integration FRN</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringIntegrationDAN">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Integration DAN</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringIntegrationSD">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Integration SD</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringIntegrationPA">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Integration PA</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlmonitoringFLApproverPRCR">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL Approver PRCR</small></h3></div>
            </div>
        </div> 
    </div>
    <div class="row">
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlpendingInvoice">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL Pending Invoice</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_stlpendingPaymentMatch">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL Pending Payment Match</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLFrnAwaitingIgfmas">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task FL FRN Awaiting IGFMAS</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLPendingRevisionApprovalPRCR">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Pending Revision Approval PRCR</small></h3></div>
            </div>
        </div> 
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLCancelPOCO">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Cancel POCO</small></h3></div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkSTLPRCREPP13Y">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task PRCR  (EPP-013 = Y)</small></h3></div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkStuckTaskPRCRMM501DEL">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task PRCR (MM501-DEL)</small></h3></div>
            </div>
        </div>
        <!--        <div class="col-lg-3">
            <div class='widget'>                  
                <div id="dash_stlpendingPrCrReview">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck Task Pending PR/CR Review</small></h3></div>
            </div>
        </div>-->

        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkStuckFLPendingPaymentEpp017Y">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>FL Pending Payment (EPP-017 = Y)</small></h3></div>
            </div>
        </div>

        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkStuckYEPCF">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Stuck YEP CF</small></h3></div>
            </div>
        </div>
        <div class="col-lg-2">
            <div class='widget'>                  
                <div id="dash_checkDuplicateInvoice">
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div >
                <div class='row text-center'><h3><small>Duplicate Invoice</small></h3></div>
            </div>
        </div>
    </div>
    @endif
    
  
    <!-- END Content -->
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script>
    $('#page-container').removeAttr('class');
    </script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}
        
            
        $(document).ready(function () {

            /*
            $.ajax({
                url: APP_URL + '/dashboard/checkSTLMonitoring',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_stlmonitoring').hide().html($data).fadeIn();
                }
            });
            */          

           
            //interval: quartz widget
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringSM',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringSM').html($data);
                    }
                });
            }, 1000); 
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringQT',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringQT').html($data);
                    }
                });
            }, 2000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringDPSQ',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringDPSQ').html($data);
                    }
                });
            }, 3000);
            
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringDPRN',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringDPRN').html($data);
                    }
                });
            }, 4000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringDPCodify',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringDPCodify').html($data);
                    }
                });
            }, 5000);

            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringRNTriggerOrder',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringRNTriggerOrder').html($data);
                    }
                });
            }, 5500);
            
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringPRCRInitiate',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringPRCRInitiate').html($data);
                    }
                });
            }, 6000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitorinDO',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringDO').html($data);
                    }
                });
            }, 7000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/checkSTLMonitorinIntegrationPRCR',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringIntegrationPRCR').html($data);
                    }
                });
            }, 8000);            
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/checkSTLMonitorinIntegrationFRN',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringIntegrationFRN').html($data);
                    }
                });
            }, 9000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/checkSTLMonitorinIntegrationDAN',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringIntegrationDAN').html($data);
                    }
                });
            }, 10000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/checkSTLMonitorinIntegrationSD',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringIntegrationSD').html($data);
                    }
                });
            }, 11000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/checkSTLMonitorinIntegrationPA',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringIntegrationPA').html($data);
                    }
                });
            }, 12000);
            
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLMonitoringFLApproverPRCR',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlmonitoringFLApproverPRCR').html($data);
                    }
                });
            }, 13000); 
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLPendingInvoiceCreation',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlpendingInvoice').html($data);
                    }
                });
            }, 14000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLPendingPaymentMatch',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlpendingPaymentMatch').html($data);
                    }
                });
            }, 15000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLPendingPrCrReview',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_stlpendingPrCrReview').html($data);
                    }
                });
            }, 16000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLQueryPRCR',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLQueryPRCR').html($data);
                    }
                });
            }, 17000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLAwaitingIgfmasPRCR',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLAwaitingIgfmasPRCR').html($data);
                    }
                });
            }, 18000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLOrderPOCO',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLOrderPOCO').html($data);
                    }
                });
            }, 19000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLFLDebit',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLFLDebit').html($data);
                    }
                });
            }, 20000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLFrnAwaitingIgfmas',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLFrnAwaitingIgfmas').html($data);
                    }
                });
            }, 21000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLPendingRevisionApprovalPRCR',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLPendingRevisionApprovalPRCR').html($data);
                    }
                });
            }, 22000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLCancelPOCO',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLCancelPOCO').html($data);
                    }
                });
            }, 22000);
            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkSTLPRCREpp13',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkSTLPRCREPP13Y').html($data);
                    }
                });
            }, 22000);

            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkStuckTaskPRCRMM501DEL',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkStuckTaskPRCRMM501DEL').html($data);
                    }
                });
            }, 22000);

            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkStuckFLPendingPaymentEpp017Y',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkStuckFLPendingPaymentEpp017Y').html($data);
                    }
                });
            }, 22000);

            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkStuckYEPCF',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkStuckYEPCF').html($data);
                    }
                });
            }, 22000);

            setTimeout(function () {
                $.ajax({
                    url: APP_URL + '/dashboard/checkDuplicateInvoice',
                    success: function (data) {
                        $data = $(data);
                        $('#dash_checkDuplicateInvoice').html($data);
                    }
                });
            }, 20000);
            
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection
