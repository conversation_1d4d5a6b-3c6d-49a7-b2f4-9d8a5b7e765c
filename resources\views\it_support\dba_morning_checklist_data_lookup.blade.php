@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/it_support/dba_morning/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/dba_morning/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/dba_morning/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            </ul>
        </div>
    </div>

    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> DATA LOOKUP </strong></h1>
        </div>
        <div class="form-group">
            <button type="button" class="btn btn-sm btn-primary add_new_lookup"><i class="fa fa-save"></i> Add New
                Lookup</button>
        </div>
        <form class="form-horizontal form-bordered insert_lookup_form" id="insert_lookup_form" style="display:none"
            action="{{ url('/it_support/dba_morning/add_data_lookup/create') }}" method="post">
            {{ csrf_field() }}
            <div class="form-group">
                <input type="hidden" id="id" name="id" value="" class="form-control"
                    style="width: 100px;">
                <label class="col-md-1 text-left" for="name">Name<span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input type="text" id="name" name="name" value="" required class="form-control">
                </div>
                <label class="col-md-1 text-left" for="name">Category Name<span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <div class="col-md-5">
                        <select id="category_name" name="category_name" class="form-control" style="width: 700px;">
                            <option value="PRODUCTION">PRODUCTION</option>
                            <option value="TESTING & DEVELOPMENT">TESTING & DEVELOPMENT</option>
                        </select>
                    </div>
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="pull-right">
                    <button type="submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i>
                        Save</button>
                    <button type="reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i
                            class="fa fa-repeat"></i> Reset</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table id="datalookup_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Category Name</th>
                        <th class="text-center">Created By</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed By</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    @if ($getLookupdate != null)
                        @foreach ($getLookupdate as $rowData => $data)
                            <tr>
                                <td class="text-center">{{ ++$rowData }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_name }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_category_name }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_created_by }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_created_date }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_changed_by }}</td>
                                <td class="text-center">{{ $data->dba_data_lookup_changed_date }}</td>
                                <td class="text-center" action_table_task>
                                    <div class="btn-group btn-group-xs">
                                            <a data-idno="{{ $data->dba_data_lookup_id }}"
                                                data-name="{{ $data->dba_data_lookup_name }}"
                                                data-categoryname="{{ $data->dba_data_lookup_category_name }}"
                                                data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i
                                                    class="fa fa-edit"></i></a>
                                        <a idnom="{{ $data->dba_data_lookup_id }}" data-toggle="tooltip" title="Delete"
                                            class="btn btn-sm btn-danger delete_data"><i class="fa fa-times"></i></a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    @endif
                </tbody>
            </table>

            <div id="modal_confirm_delete_data" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <input style="display:none" type="" id="delid" name="delid" value="" class="form-control"
                                style="width: 100px;">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Delete Data Lookup? </label> &nbsp;&nbsp;&nbsp;
                        </div>
                        <br /><br />
                        <div class="modal-footer">
                            <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete"
                                class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"
                                data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </DIV>

@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
        $(document).ready(function() {
            $('#datalookup_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
        });
    </script>
    <script>
        $(function() {
            $('#to-top').click();
        });


        $(".add_new_lookup").on("click", function() {
            $("#insert_lookup_form").show();
            resetFormFields();
        });

        $(".resetbutton").on("click", resetFormFields);

        function resetFormFields() {
            $("#id, #name, #category_name").val("");
        }

        $(".editbutton").on("click", function() {
            console.log("Edit button clicked");
            $('#to-top').click();
            $("#insert_lookup_form").show();
            let attrs = $(this).data(); // Retrieve all data attributes as an object

            console.log(attrs); // Debug: Check data attributes in the console

            $("#id").val(attrs.idno);
            $("#name").val(attrs.name);
            $("#category_name").val(attrs.categoryname);
        });

        $(".delete_data").on("click", function() {
            $("#modal_confirm_delete_data").modal('show');
            let
                delid = $(this).attr('idnom');
            $('#delid').val(delid);
        });

        $('#submit_confirm_delete').on('click', function() {
            $('#modal_confirm_delete_data').modal('hide');
           var Id = $("#delid").val();
            $.ajax({
                type: "GET",
                url: "/it_support/dba_morning/delete-data-lookup/" + Id,
            }).done(function(resp) {
                console.log(resp);
                location.reload();
            });

        });
    </script>
@endsection
