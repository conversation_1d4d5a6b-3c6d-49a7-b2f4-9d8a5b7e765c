<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::middleware('auth:api')->get('/user', function (Request $request) {
    return $request->user();
});

Route::post('/ivr/getAppStatusByMof', ['uses' => 'AspectIvrController@getApplicationStatusByMof']);
Route::post('/ivr/getSoftcertStatus', ['uses' => 'AspectIvrController@getSoftcertStatusByMof']);

Route::get('/ep-page-views', 'api\EPGoogleAnalyticController@getGoogleAnalyticsData');

/** Test Purpose **/
// Route::get('/ivr/getToken', ['uses' => 'AspectIvrController@getToken']);
// Route::get('/ivr/testToken', ['uses' => 'AspectIvrController@testToken']);
