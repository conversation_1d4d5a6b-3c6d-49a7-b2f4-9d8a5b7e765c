<?php

namespace App\Http\Controllers;

use App\Services\CRMService;
use Carbon\Carbon;
use DB;
use Auth;
use App\Services\Traits\SSHService;
use SSH;
use Illuminate\Http\Request;
use DateTime;
use Log;

class ModuleDashboardController extends Controller {

    static $url = 'https://epss.eperolehan.gov.my/support/crm/case?case_number';
    
    public static function crmService() {
        return new CRMService;
    }

    use SSHService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function getDashboardModule() {
        return $this->getDashboardDetailModule();
    }
    
   
    public function getDashboardDetailModule() {
        
         $displaySpecialistByModuleChart = self::crmService()->getSpecialistByModuleChart(); 
        
        $dataDeveloperbyModuleStat = array();   
        foreach ($displaySpecialistByModuleChart as $ModuleData) { 
              $moduleName = $ModuleData->ModuleName;
              $totalITCoord = $ModuleData->totalCaseAssignedtoDeveloper;
              array_push($dataDeveloperbyModuleStat, ['label' => $moduleName,'y' => $totalITCoord]);
        }
        
        $dataProdSupportbyModuleStat = array();   
        foreach ($displaySpecialistByModuleChart as $ModuleData) { 
              $moduleName = $ModuleData->ModuleName;
              $totalProdSupport = $ModuleData->totalCaseAssignedtoGroupProdSupport;
              array_push($dataProdSupportbyModuleStat, ['label' => $moduleName,'y' => $totalProdSupport]);
        }
        
        $dataMiddlewaredbyModuleStat = array();   
        foreach ($displaySpecialistByModuleChart as $ModuleData) { 
              $moduleName = $ModuleData->ModuleName;
              $totalMiddleware = $ModuleData->totalCaseAssignedtoMiddleware;
              array_push($dataMiddlewaredbyModuleStat, ['label' => $moduleName,'y' => $totalMiddleware]);
        }
       // dd($dataDeveloperbyModuleStat);
        
        return view('dashboard_module', [
            'dataDeveloperbyModuleStat' => $dataDeveloperbyModuleStat,
            'dataProdSupportbyModuleStat' => $dataProdSupportbyModuleStat,
            'dataMiddlewaredbyModuleStat' => $dataMiddlewaredbyModuleStat
                ]);
        
        
        
    }
    
    public function dashboardCRMIncidentITSpecModule(Request $request){
        $countItSpecSeverityIncidentPendAck_S1 = 0;
        $countItSpecSeverityIncidentPendAck_S2 = 0;
        $countItSpecSeverityIncidentPendAck_S3 = 0;
        $countItSpecSeverityIncidentAck_S1 = 0;
        $countItSpecSeverityIncidentAck_S2 = 0;
        $countItSpecSeverityIncidentAck_S3 = 0;
        $countItSpecSeverityIncidentPendAck_Others = 0;
        $countItSpecSeverityIncidentAck_Others = 0;
        
        $countItSpecSeverityIncidentPendAckExceed_S1 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S2 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S3 = 0;
        $countItSpecSeverityIncidentAckExceed_S1 = 0;
        $countItSpecSeverityIncidentAckExceed_S2 = 0;
        $countItSpecSeverityIncidentAckExceed_S3 = 0;
        $countItSpecSeverityIncidentPendAckExceed_Others = 0;
        $countItSpecSeverityIncidentAckExceed_Others = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();
        $SubCatID = $request->subcatid;
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Day of Severity</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecModule($SubCatID);
      //  dump($listTasks);
      if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {
                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

                if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '') {
                    if ($tasks->taskSeverity == 's1') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S1++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S1++;
                        }
                    }
                    if ($tasks->taskSeverity == 's2') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S2++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S2++;
                        }
                    }
                    if ($tasks->taskSeverity == 's3') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_S3++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_S3++;
                        }
                    }
                    if ($tasks->taskSeverity == null && $tasks->incidentType != 'incident_it') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_Others++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_Others++;
                        }
                    }
                }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '') {
                    if ($tasks->taskSeverity == 's1') {
                        if ($acknowledgetime <= $datedue) {
                            $countItSpecSeverityIncidentAck_S1++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S1++;
                        }
                    }
                    if ($tasks->taskSeverity == 's2') {
                        if ($acknowledgetime <= $datedue) {
                            $countItSpecSeverityIncidentAck_S2++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S2++;
                        }
                    }
                    if ($tasks->taskSeverity == 's3') {
                        if ($acknowledgetime <= $datedue) {
                            $countItSpecSeverityIncidentAck_S3++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_S3++;
                        }
                    }
                    if ($tasks->taskSeverity == null && $tasks->incidentType != 'incident_it') {
                        if ($acknowledgetime <= $datedue) {
                            $countItSpecSeverityIncidentAck_Others++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_Others++;
                        }
                    }
                }
            }
        }

        $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>1 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckS1/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 1 Day)' >{$countItSpecSeverityIncidentPendAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckExceedS1/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 1 Day)' >{$countItSpecSeverityIncidentPendAckExceed_S1}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeS1/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Within 1 Day)' >{$countItSpecSeverityIncidentAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeExceedS1/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Exceed 1 Day)' >{$countItSpecSeverityIncidentAckExceed_S1}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>3 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckS2/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 3 Days)' >{$countItSpecSeverityIncidentPendAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckExceedS2/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 3 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S2}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeS2/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Within 3 Days)' >{$countItSpecSeverityIncidentAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeExceedS2/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Exceed 3 Days)' >{$countItSpecSeverityIncidentAckExceed_S2}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>5 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckS3/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 5 Days)' >{$countItSpecSeverityIncidentPendAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncPendAckExceedS3/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 5 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S3}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeS3/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Within 5 Days)' >{$countItSpecSeverityIncidentAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itModuleIncAcknowledgeExceedS3/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Exceed 5 Days)' >{$countItSpecSeverityIncidentAckExceed_S3}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Other than IT Incident</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_Others)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleSelectedOtherThanITIncidentPendAck/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 5 Days)' >{$countItSpecSeverityIncidentPendAck_Others}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_Others)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itModuleSelectedOtherThanITIncidentPendAckExceed/$SubCatID'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 5 Days)' >{$countItSpecSeverityIncidentPendAckExceed_Others}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_Others)}' 
                            data-toggle='modal' data-url='/list/crm/itModuleSelectedOtherThanITIncidentAcknowledge/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Within 5 Days)' >{$countItSpecSeverityIncidentAck_Others}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_Others)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itModuleSelectedOtherThanITIncidentAcknowledgeExceed/$SubCatID'
                            data-title='List IT Specialist Acknowledge (Exceed 5 Days)' >{$countItSpecSeverityIncidentAckExceed_Others}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }
    
    public function listTaskItModuleIncPendAckS1($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';       
        
        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID); 

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's1' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItModuleIncPendAckExceedS1($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's1' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }    
    
    public function listTaskItModuleIncAcknowledgeS1($SubCatID) {
        
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's1' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>  
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItModuleIncAcknowledgeExceedS1($SubCatID) {
        
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's1' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>  
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItModuleIncPendAckS2($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's2' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItModuleIncPendAckExceedS2($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's2' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }    
    
    public function listTaskItModuleIncAcknowledgeS2($SubCatID) {
        
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's2' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItModuleIncAcknowledgeExceedS2($SubCatID) {
        
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's2' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItModuleIncPendAckS3($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's3' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItModuleIncPendAckExceedS3($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's3' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }    
    
    public function listTaskItModuleIncAcknowledgeS3($SubCatID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's3' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskItModuleIncAcknowledgeExceedS3($SubCatID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's3' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }  
    
    public function listTaskSelectedModuleOtherThanITIncidentPendAck($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->incidentType != 'incident_it') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskSelectedModuleOtherThanITIncidentPendAckExceed($SubCatID) {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->incidentType != 'incident_it') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }    
    
    public function listTaskSelectedModuleOtherThanITIncidentAcknowledge($SubCatID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->incidentType != 'incident_it') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }

    public function listTaskSelectedModuleOtherThanITIncidentAcknowledgeExceed($SubCatID) {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModule($SubCatID);

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->incidentType != 'incident_it') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function dashboardCRMIncidentITSpecModuleOthers(){
        $countItSpecSeverityIncidentPendAck_S1 = 0;
        $countItSpecSeverityIncidentPendAck_S2 = 0;
        $countItSpecSeverityIncidentPendAck_S3 = 0;
        $countItSpecSeverityIncidentAck_S1 = 0;
        $countItSpecSeverityIncidentAck_S2 = 0;
        $countItSpecSeverityIncidentAck_S3 = 0;
        $countItSpecSeverityIncidentPendAck_Others = 0;
        $countItSpecSeverityIncidentAck_Others = 0;
        
        $countItSpecSeverityIncidentPendAckExceed_S1 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S2 = 0;
        $countItSpecSeverityIncidentPendAckExceed_S3 = 0;
        $countItSpecSeverityIncidentAckExceed_S1 = 0;
        $countItSpecSeverityIncidentAckExceed_S2 = 0;
        $countItSpecSeverityIncidentAckExceed_S3 = 0;
        $countItSpecSeverityIncidentPendAckExceed_Others = 0;
        $countItSpecSeverityIncidentAckExceed_Others = 0;

        $PENDING_ACK = 'Pending Acknowledgement';
        $ACKNOWLEDGE = 'Acknowledge';
        $current = Carbon::now();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-vcenter table-striped table-bordered'>
                <thead>
                    <tr class=border='1'>
                        <th>Day of Severity</th>
                        <th>Status</th>
                        <th>Not Exceed</th>
                        <th>Exceed</th>
                    </tr>
                </thead>
                <tbody>";

        $listTasks = self::crmService()->getDashboardCRMITSpecModuleOthers();
       if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {
                
                        $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                        $acknowledgetime = Carbon::parse($tasks->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
               
                   if ($tasks->taskStatus == $PENDING_ACK && $tasks->acknowledgetime == '' // && $tasks->taskSeverity != null && $tasks->taskFlag == ''
                           ) {
                        if($tasks->taskSeverity == 's1'){
                            if ($datedue > $current) {
                                $countItSpecSeverityIncidentPendAck_S1++;
                            } else {
                                $countItSpecSeverityIncidentPendAckExceed_S1++;
                            }                            
                        }  
                        if($tasks->taskSeverity == 's2'){
                            if ($datedue > $current) {
                                $countItSpecSeverityIncidentPendAck_S2++;
                            } else {
                                $countItSpecSeverityIncidentPendAckExceed_S2++;
                            }                            
                        } 
                        if($tasks->taskSeverity == 's3'){
                            if ($datedue > $current) {
                                $countItSpecSeverityIncidentPendAck_S3++;
                            } else {
                                $countItSpecSeverityIncidentPendAckExceed_S3++;
                            }                            
                        } 
                        if ($tasks->taskSeverity == null && $tasks->incidentType != 'incident_it') {
                        if ($datedue > $current) {
                            $countItSpecSeverityIncidentPendAck_Others++;
                        } else {
                            $countItSpecSeverityIncidentPendAckExceed_Others++;
                        }
                    }
                   }if ($tasks->taskStatus == $ACKNOWLEDGE && $tasks->acknowledgetime != '' // && $tasks->taskSeverity != null && $tasks->taskFlag != null
                           ) {
                       if($tasks->taskSeverity == 's1'){
                           if ($acknowledgetime <= $datedue) {
                           $countItSpecSeverityIncidentAck_S1++;
                            } else {
                                $countItSpecSeverityIncidentAckExceed_S1++;
                            }
                       }
                       if($tasks->taskSeverity == 's2'){
                           if ($acknowledgetime <= $datedue) {
                           $countItSpecSeverityIncidentAck_S2++;
                            } else {
                                $countItSpecSeverityIncidentAckExceed_S2++;
                            }
                       }
                       if($tasks->taskSeverity == 's3'){
                           if ($acknowledgetime <= $datedue) {
                           $countItSpecSeverityIncidentAck_S3++;
                            } else {
                                $countItSpecSeverityIncidentAckExceed_S3++;
                            }
                       }
                      if ($tasks->taskSeverity == null && $tasks->incidentType != 'incident_it') {
                        if ($acknowledgetime <= $datedue) {
                            $countItSpecSeverityIncidentAck_Others++;
                        } else {
                            $countItSpecSeverityIncidentAckExceed_Others++;
                        }
                    } 
                       
                   }
           
               }
        }

         $html .= "
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>1 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckS1'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 1 Day)' >{$countItSpecSeverityIncidentPendAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckExceedS1'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 1 Day)' >{$countItSpecSeverityIncidentPendAckExceed_S1}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeS1'
                            data-title='List IT Specialist Acknowledge (Within 1 Day)' >{$countItSpecSeverityIncidentAck_S1}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S1)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeExceedS1'
                            data-title='List IT Specialist Acknowledge (Exceed 1 Day)' >{$countItSpecSeverityIncidentAckExceed_S1}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>3 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckS2'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 3 Days)' >{$countItSpecSeverityIncidentPendAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckExceedS2'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 3 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S2}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeS2'
                            data-title='List IT Specialist Acknowledge (Within 3 Days)' >{$countItSpecSeverityIncidentAck_S2}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S2)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeExceedS2'
                            data-title='List IT Specialist Acknowledge (Exceed 3 Days)' >{$countItSpecSeverityIncidentAckExceed_S2}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>5 Day</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckS3'
                            data-title='List Pending Acknowledgement for IT Specialist (Within 5 Days)' >{$countItSpecSeverityIncidentPendAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncPendAckExceedS3'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed 5 Days)' >{$countItSpecSeverityIncidentPendAckExceed_S3}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_S3)}' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeS3'
                            data-title='List IT Specialist Acknowledge (Within 5 Days)' >{$countItSpecSeverityIncidentAck_S3}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_S3)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itOtherModuleIncAcknowledgeExceedS3'
                            data-title='List IT Specialist Acknowledge (Exceed 5 Days)' >{$countItSpecSeverityIncidentAckExceed_S3}</a></strong></td>
            </tr>
            <tr>
                <td rowspan='2' style='width: 20%;'><strong>Other than IT Incident</strong></td>
                <td style='width: 20%;'><strong>{$PENDING_ACK}</strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentPendAck_Others)}' 
                            data-toggle='modal' data-url='/list/crm/itOthersPendAck'
                            data-title='List Pending Acknowledgement for IT Specialist (Other than IT Incident)' >{$countItSpecSeverityIncidentPendAck_Others}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentPendAckExceed_Others)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itOthersPendAckExceed'
                            data-title='List Pending Acknowledgement for IT Incident (Exceed - Other than IT Incident)' >{$countItSpecSeverityIncidentPendAckExceed_Others}</a></strong></td>
            </tr>
             <tr>
                <td style='width: 20%;'><strong>{$ACKNOWLEDGE}</strong></td>                
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                             
                            class='modal-list-data-action btn btn-sm font-weight-bold shadow {$this->setDisplayBtnWarningClass($countItSpecSeverityIncidentAck_Others)}' 
                            data-toggle='modal' data-url='/list/crm/itOthersAcknowledge'
                            data-title='List IT Specialist Acknowledge (Other than IT Incident)' >{$countItSpecSeverityIncidentAck_Others}</a></strong></td>
                <td style='width: 30%;'><strong><a href='#modal-list-data' 
                            class='modal-list-data-action btn {$this->setDisplayBtnClass($countItSpecSeverityIncidentAckExceed_Others)} btn-sm font-weight-bold shadow' 
                            data-toggle='modal' data-url='/list/crm/itOthersAcknowledgeExceed'
                            data-title='List IT Specialist Acknowledge (Exceed - Other than IT Incident)' >{$countItSpecSeverityIncidentAckExceed_Others}</a></strong></td>
            </tr>
                </tbody>
            </table>
        </div>";

        return $html;
    }
    
    public function listTaskItOtherModuleIncPendAckS1() {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's1' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncPendAckExceedS1() {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's1' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOtherModuleIncAcknowledgeS1() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's1' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncAcknowledgeExceedS1() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's1' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOtherModuleIncPendAckS2() {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's2' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncPendAckExceedS2() {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's2' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOtherModuleIncAcknowledgeS2() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's2' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncAcknowledgeExceedS2() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's2' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOtherModuleIncPendAckS3() {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's3' && $value->taskFlag == '') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncPendAckExceedS3() {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->taskSeverity == 's3' && $value->taskFlag == '') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOtherModuleIncAcknowledgeS3() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's3' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOtherModuleIncAcknowledgeExceedS3() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->taskSeverity == 's3' && $value->taskFlag != null) {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOthersModulePendAck() {
        
        $PENDING_ACK = 'Pending Acknowledgement';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Remain</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $current = Carbon::now();

            if ($datedue > $current && $value->acknowledgetime == '' && $value->taskStatus == $PENDING_ACK && $value->incidentType != 'incident_it') {
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$timeRemaining</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOthersModulePendAckExceed() {
        
        $PENDING_ACK = 'Pending Acknowledgement';
        $current = Carbon::now();

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Time Exceed</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime == '' && $datedue <= $current && $value->taskStatus == $PENDING_ACK && $value->incidentType != 'incident_it') {

                $current = Carbon::now();
                $dateDiff = $current->diff(new DateTime($datedue));
                $timeExceeding = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$timeExceeding</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    } 
    
    public function listTaskItOthersModuleAcknowledge() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($list as $value) {

            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");
            if ($acknowledgeTime <= $datedue && $value->acknowledgetime != '' && $value->taskStatus == $ACKNOWLEDGE && $value->incidentType != 'incident_it') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td>
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='green'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
    
    public function listTaskItOthersModuleAcknowledgeExceed() {
        $ACKNOWLEDGE = 'Acknowledge';

        $list = self::crmService()->getDashboardCRMITSpecModuleOthers();

        $html = "<thead>
                        <tr>
                            <th class='text-center'>&nbsp;</th>
                            <th class='text-center'>Case Number</th>
                            <th class='text-center'>Group Assigned</th>
                            <th class='text-center'>Redmine No.</th>
                            <th class='text-center'>Sub Category</th>
                            <th class='text-center'>Case Subject</th>
                            <th class='text-center'>Status</th>
                            <th class='text-center'>SLA Start</th>
                            <th class='text-center'>SLA End</th>
                            <th class='text-center'>Acknowledge Time</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;

        foreach ($list as $value) {
            $datestart = Carbon::parse($value->datestart)->addHour(8)->format("Y-m-d H:i:s");
            $datedue = Carbon::parse($value->datedue)->addHour(8)->format("Y-m-d H:i:s");
            $acknowledgeTime = Carbon::parse($value->acknowledgetime)->addHour(8)->format("Y-m-d H:i:s");

            if ($value->acknowledgetime != '' && $acknowledgeTime > $datedue && $value->taskStatus == $ACKNOWLEDGE && $value->incidentType != 'incident_it') {
                $data = "
                    <tr>
                        <td class='text-center'><strong>" . ++$counter . "</strong></td>
                        <td class='text-left'><strong><a target='_blank' href='".self::$url."=$value->caseNumber' title='View Detail Case in CRM'>$value->caseNumber</a></strong></td>
                        <td class='text-left'><strong>$value->assignedGroupSpec</strong></td> 
                        <td class='text-left'><strong>$value->redmine</strong></td>
                        <td class='text-left'><strong>$value->subCategory</strong></td>
                        <td class='text-left'><strong>$value->caseName</strong></td>    
                        <td class='text-left'><strong>$value->taskStatus</strong></td>
                        <td class='text-left'><strong>$datestart</strong></td>
                        <td class='text-left'><strong>$datedue</strong></td>
                        <td class='text-left'><font color='red'><strong>$acknowledgeTime</strong></td>
                    </tr>";
                $html = $html . $data;
            }
        }
        $html = $html . "<tbody>";
        return $html;
    }
           
    public function setDisplayBtnClass($count) {

        if($count > 0) {
            return 'btn-red';
        } 

        return 'btn-secondary';

    }
    
    public function setDisplayBtnWarningClass($count) {

        if($count > 0) {
            return 'btn-yellow';
        } 

        return 'btn-secondary';

    }

}
