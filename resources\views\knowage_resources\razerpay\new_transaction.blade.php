@extends('layouts.guest-dash')

@section('content')
    @if (Auth::user())
            @if(Auth::user()->isAllowAccessSMModule() ) 
            <div class="content-header">
            <div class="header-section">
                <h1>RAZER PAY TRANSACTION</h1>
            </div>
            </div> 

            <div class="row">
                <div class="col-sm-12 col-md-12 col-lg-4">
                    <div class="block">
                        <div class="block-title">
                            <h1>
                                <i class="fa fa-tasks"></i>
                                <strong>New Transactions File</strong>
                            </h1>
                        </div>

                        <div class="block-content">
                        
                            <div id="e1_alert_box" class="alert" style='display:none'></div>
                            <form id="upload_razer_transaction" class='form' action="">
                            <div class="form-group">
                                <label for="razer_file">Daily RAZER file Transaction</label>
                                <input type="file" id="xls_file">
                                <p class="help-block">*.xlsx file with first tab only, other tabs will be trashed!! </p>
                            </div>
                            <div class="form-group">
                                <button id='e1_btn_text' type="submit" class="btn btn-primary btn-sm">Upload </button>
                            </div>
                            </form>
                        </div>
                        
                    </div>


                    <div id="upload-response" class="block" style='display:none;'>
                    
                        <div class="block-title">
                            <h1>
                                <i class="fa fa-tasks"></i> 
                                <strong>Upload Response Summary</strong>
                            </h1>
                        </div>

                        <div class="block-content">
                            <div id='upload-on-progress' class="text-center spinner-loading" style="padding: 20px; display:block">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>

                            <div id='upload-done' class="row" style="display:none">
                                <div class='col-xs-12'>
                                    <strong>Transaction Date :</strong> <span id="transaction_date"></span>
                                    <br>
                                    <br>
                                </div>
                                <div class="col-md-12">
                                    <table class='table table-bordered'>
                                        <thead>
                                            <tr>
                                                <th class="text-center">Status</th>
                                                <th class="text-center">Registered</th>
                                                <th class="text-center">Renewed</th>
                                                <th class="text-center">Quantity</th>
                                                <th class="text-right">Total Collection</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Captured</td>
                                                <td class="text-center" id="captured_registered"></td>
                                                <td class="text-center" id="captured_renewed"></td>
                                                <td class="text-center" id="captured_quantity"></td>
                                                <td class="text-right" id="captured_total_collection"></td>
                                            </tr>
                                            <tr>
                                                <td>Failed</td>
                                                <td class="text-center" id="failed_registered"></td>
                                                <td class="text-center" id="failed_renewed"></td>
                                                <td class="text-center" id="failed_quantity"></td>
                                                <td class="text-right" id="failed_total_collection"></td>
                                            </tr>
                                            <tr>
                                                <td>Blocked</td>
                                                <td class="text-center" id="blocked_registered"></td>
                                                <td class="text-center" id="blocked_renewed"></td>
                                                <td class="text-center" id="blocked_quantity"></td>
                                                <td class="text-right" id="blocked_total_collection"></td>
                                            </tr>
                                        </tbody>
                                    </table>

                                    <table class="table table-bordered">
                                        <thead>
                                            <tr>
                                                <th>Status</th>
                                                <th class="text-center">Captured</th>
                                                <th class="text-center">No Receipt</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>Payment Received</td>
                                                <td class="text-center" id="captured_payment_match"></td>
                                                <td class="text-center" id="captured_no_receipt"></td>
                                            </tr>
                                        </tbody>

                                    </table>

                                    <div id='overwrite-buttons' class='text-right' style="margin-bottom:10px; display:none">
                                        <div class="btn btn-default btn-sm">Cancel</div>
                                        <div 
                                            class="btn btn-warning btn-sm" 
                                            id="overwrite-transaction" 
                                             data-filename="">Overwrite Old Data</div>
                                    </div>

                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-lg-8">
                    <div class='block'>
                        <div class="block-title">
                        <h1>
                                <i class="fa fa-tasks"></i>
                                <strong>Payment Transaction Summary</strong>
                            </h1>
                        </div>

                        <div class="block-content">
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                    <th>PAYMENT ID</th>
                                    <th>PAYMENT AMOUNT</th>
                                    <th>BILL NO</th>
                                    <th>BILL TYPE</th>
                                    <th>COMPANY NAME</th>
                                    <th>EP_NO</th>
                                    </tr>
                                </thead>
                                <tbody  id="payment_with_no_receipt">
                                    <tr>
                                        <td colspan=6>Please upload transaction file first</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="block hide">
                        <div class="block-title">
                            <h1>
                                <i class="fa fa-tasks"></i>
                                <strong>List of Uploaded Files</strong>
                            </h1>
                        </div>

                        <div class="block-content">
                        <table id="basic-datatable" class="table table-vcenter table-striped table-bordered">
                            <thead>
                            <tr>
                                <th class="text-center">Upload Date Time</th>
                                <th class="text-center">Transaction Date</th>
                                <th class="text-center">FileName</th>
                                <th class="text-center">Uploaded By</th>
                                <th class="text-center">Action</th>
                            </tr>
                            </thead>
                            <tbody>
                            @if(count($list_upload_history) > 0)     
                                @foreach ($list_upload_history as $data)
                                <tr>
                                    <td class="text-center">{{ $data['timestamp'] }}</td>
                                    <td class="text-center">{{ $data['summary']['date'] }}</td>
                                    <td class="">{{ $data['filename'] }}</td>
                                    <td class="text-center">{{ json_decode($data['user_profile'])->username }}</td>
                                    <td class="text-center">
                                        <div class="razer-summary btn btn-default btn-xs" data-razerid="{{ $data['id'] }}">View</div>
                                    </td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="5">No Records</td>
                                </tr>    
                            @endif
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>

            @endif
    @endif
@endsection

@section('jsprivate')

<script>
    var user_profile = {}
    var alert_box = $('#e1_alert_box')
    var upload_on_progress = $('#upload-on-progress')
    var upload_done = $('#upload-done')
    var overwrite_buttons = $('#overwrite-buttons')
    var submit_btn = $('#e1_btn_text')

    function NoReceiptPayment(payloads){
        html = "<tr><td colspan='6'>All payment complete with Receipt No</td></tr>"
        console.log(payloads.length)
        if (payloads.length > 0) {
            html = '';
            payloads.map((value,key)=>{
                console.log(value)
                html += '<tr>'
                html += `<td>${value.PAYMENT_ID}</td>`
                html += `<td>${value.PAYMENT_AMT}</td>`
                html += `<td>${value.BILL_NO}</td>`
                html += `<td>${value.BILL_TYPE}</td>`
                html += `<td>${value.COMPANY_NAME}</td>`
                html += `<td>${value.EP_NO}</td>`
                html += '</tr>'
            })
        }
        $("#payment_with_no_receipt").html(html)
    }

    function MappedResponse(payloads, filename){
        $('#blocked_registered').text(parseFloat(payloads['blocked_register']).toLocaleString())
        $('#blocked_renewed').text(parseFloat(payloads['blocked_renew']).toLocaleString())
        $('#blocked_quantity').text(parseFloat(payloads['blocked_count']).toLocaleString())
        $('#blocked_total_collection').text("RM " + parseFloat(payloads['blocked_total']).toLocaleString())
        $('#failed_registered').text(parseFloat(payloads['failed_register']).toLocaleString())
        $('#failed_renewed').text(parseFloat(payloads['failed_renew']).toLocaleString())
        $('#failed_quantity').text(parseFloat(payloads['failed_count']).toLocaleString())
        $('#failed_total_collection').text("RM " + parseFloat(payloads['failed_total']).toLocaleString())
        $('#captured_registered').text(parseFloat(payloads['captured_register']).toLocaleString())
        $('#captured_renewed').text(parseFloat(payloads['captured_renew']).toLocaleString())
        $('#captured_quantity').text(parseFloat(payloads['captured_count']).toLocaleString())
        $('#captured_total_collection').text("RM " + parseFloat(payloads['captured_total']).toLocaleString())
        $('#transaction_date').text(payloads['date'])
        $('#captured_payment_match').text(payloads['razerpay_payment_match']['payment_no_receipt']['payment_count'])
        $('#captured_no_receipt').text(payloads['razerpay_payment_match']['payment_no_receipt']['total_no_receipt'])
        NoReceiptPayment(payloads['razerpay_payment_match']['payment_no_receipt']['without_receipt_no'])
        $('#overwrite-transaction').data('filename', filename)
        
    }

    $('.razer-summary').click(function(){
        $('.razer-summary').text('View')
        var razer_id = $(this).data('razerid') 
        let obj = $(this)
        obj.text('loading...')
        $('#upload-response').show()
        $.ajax({
            url: './transaction-view/' + razer_id,
            type: 'GET',
            success : function(response){
                var responseObject = JSON.parse(response)
                MappedResponse(responseObject['summary'],"")
                obj.text('----')
                upload_on_progress.hide()
                upload_done.show()
            }
        })
    })

    $('#overwrite-transaction').click(function(){
        var filename = $(this).data('filename')
        upload_on_progress.show()
        overwrite_buttons.hide()
        upload_done.hide()
        $.ajax({
            type: 'POST',
            url: './transaction-overwrite',
            data: {
                "_token": "{{ csrf_token() }}",
                "filename" : filename,
                "user_profile" : user_profile
            },
            success: function(response){
                var obj = JSON.parse(response)
                overwrite_buttons.hide()
                MappedResponse(obj, "")
                upload_on_progress.hide()
                upload_done.show()
                alert_box
                    .show()
                    .removeClass('alert-danger')
                    .addClass('alert-success')
                    .html("<strong>Success!!</strong> " + "Transaction has been overwrited.")
                $('#upload_razer_transaction')[0].reset()
                submit_btn
                    .text('Upload')
                    .removeClass('btn-danger, btn-warning')
                    .addClass('btn-primary')
                    .show()
            }
        })
    })

    $('#upload_razer_transaction').submit(function(e){
        e.preventDefault()
        var documentData = new FormData();
        
        submit_btn.html('Uploading... Please wait!')
        var endpoint = {!! $upload_endpoint !!} ;
        documentData.append('razer_file', $('input#xls_file')[0].files[0]);
        documentData.append('user_profile', JSON.stringify(endpoint['user_profile']))
        documentData.append("_token","{{ csrf_token() }}")
        $('#upload-response').show()

        $.ajax({
            url: './transaction-upload',
            type: 'POST',
            data: documentData,
            cache: false,
            contentType: false,
            processData: false,
            success: function (response) {
                var obj = JSON.parse(response)
                if(obj.status == 800){
                    submit_btn
                        .html('Upload Completed')
                        .removeClass('btn-primary')
                        .addClass('btn-default')
                        .show()
                    alert_box
                        .show()
                        .removeClass('alert-danger')
                        .addClass('alert-success')
                        .html("<strong>Success!!</strong> " + "File has been uploaded.")
                        upload_on_progress.hide()
                        upload_done.show()
                        MappedResponse(obj, "")
                        $('#upload_razer_transaction')[0].reset()
                } 
                if (obj.status == 809){
                    submit_btn
                        .html('Progess halt!!!')
                        .removeClass('btn-primary')
                        .addClass('btn-warning')
                        .hide()
                    alert_box
                        .show()
                        .removeClass('alert-danger')
                        .addClass('alert-warning')
                        .html("<strong>Warning!!</strong> " + "Transaction date already existed.")
                    MappedResponse(obj.existed_data, obj.filename)
                    upload_on_progress.hide()
                    upload_done.show()
                    overwrite_buttons.show()
                    user_profile = obj.user_profile
                }
                
            },
            error: function(response){
                submit_btn
                    .html('Upload Failed')
                    .removeClass('btn-primary')
                    .addClass('btn-danger')
                alert_box
                    .show()
                    .addClass('alert-danger')
                    .html('<strong>Error!!</strong> ' + response.responseJSON.errors.razer_file)
            }

        });
    })
</script>
@endsection
