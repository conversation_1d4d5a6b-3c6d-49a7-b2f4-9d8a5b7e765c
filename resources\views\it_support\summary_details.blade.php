@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/it_support/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li  class="active">
                <a href="{{ url('/it_support/summary_details') }}"><i class="fa fa-list-alt"></i>Summary Details</a>
            </li>
            <li>
                <a href="{{ url('/it_support/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>
</div>

<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> Summary Details </strong></h1> 
    </div>  
    <form class="form-horizontal form-bordered" style="display:none" id="summary_form" action="{{url('/it_support/summary_details/create')}}" method="post">
        <div class="block-options pull-right">
            <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm" style="background-color : red ">Close</span>
        </div>
        {{ csrf_field() }}
        <div class="form-group">
            <input type="hidden" id="id" name="id" value="" class="form-control" style="width: 100px;">
            <label class="col-md-1 text-left" for="date_created_task">Date<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <input id = 'date_created_task' name="date_created_task" type="date" required class="form-control">
            </div>
            <label class="col-md-1 text-left" for="shift">Job Shift<span class="text-danger">*</span></label>
            <div class="col-md-3">
                <select id="shift" name = "shift" required class="form-control" style="width: 700px;">
                    <option value="">Please Select</option>
                    <option value="M">Morning</option>
                    <option value="E">Evening</option>
                </select>
            </div>
        </div>
        <div class="form-group">
            <div class="form-group">
                <label class="col-md-1 text-left" for="remarks">Remarks<span class="text-danger">*</span></label>
                <div class="col-md-12">
                    <textarea type="text" id="remarks" name="remarks" rows="5" required class="form-control" placeholder="Please insert remarks here if any"></textarea>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-1 text-left" for="changedby">Changed By</label>
            <div class="col-md-3">
                <input readonly="" id="changedby" name="changedby" type="text" value="" class="form-control" style="width: 700px;">
            </div>

            <label class="col-md-1 text-left" for="changeddate">Changed Date</label>
            <div class="col-md-3">
                <input readonly="" id="changeddate" name="changeddate" type="text" value="" class="form-control" style="width: 700px;">
            </div>
        </div> 
        <div class="form-group">
            <div class="form-group">
                <label class="col-md-3 text-left" for="respond">Manager Respond</label>
                <div class="col-md-12">
                    <textarea type="text" id="respond" name="respond" rows="5" class="form-control" placeholder="Please insert respond here if any"></textarea>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-1 text-left" for="respondby">Respond By</label>
            <div class="col-md-3">
                <input readonly="" id="respondby" name="respondby" type="text" value="" class="form-control" style="width: 700px;">
            </div>

            <label class="col-md-1 text-left" for="responddate">Respond Date</label>
            <div class="col-md-3">
                <input readonly="" id="responddate" name="responddate" type="text" value="" class="form-control" style="width: 700px;">
            </div>
        </div> 
        <div class="form-group form-actions">
            <div class="pull-right">
                <button type = "submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i> Save</button>
                <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
            </div>
        </div>
    </form>
    <button type="button" id="openNewTask" class="btn btn btn-primary" >Add New</button>
    <br />

    <table class="table table-borderless table-striped table-vcenter">
        @if(isset($listData))
        @foreach($listData as $list)
        <tbody>
            <tr>
                <td class="text-center" style="width: 60px;">
                    <span class="text-success">
                        <i class="fa fa-exclamation-circle fa-2x" data-toggle="tooltip"></i>
                    </span>
                </td>
                <td>
                    <strong>{{$list->job_remarks}}</strong>
                    <div class="text-muted">Created at {{$list->job_created_date}} by <span class="label label-success">{{$list->job_created_by}}</span></div>
                </td>
                <td class="text-center" style="width: 60px;">
                    <a idno ="{{$list->job_day_id}}" 
                       shift ="{{$list->job_shift}}"
                       date ="{{$list->job_date}}"  
                       remarks = "{{$list->job_remarks}}" 
                       respond = "{{$list->job_respond}}" 
                       created_by = "{{$list->job_created_by}}" 
                       created_date="{{$list->job_created_date}}" 
                       changed_by ="{{$list->job_changed_by}}" 
                       changed_date= "{{$list->job_changed_date}}" 
                       respond_by = "{{$list->job_respond_by}}"  
                       respond_date="{{$list->job_respond_date}}"  class="btn btn-default editbutton">
                        <i class="fa fa-comments"></i> Open
                    </a>
                </td>
            </tr>
        </tbody>
        @endforeach
        @endif
    </table>
</div>

@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<script>$(function () {
        $('#to-top').click();

        $("#openNewTask").on("click", function () {
            $('#summary_form').show();
            $('#openNewTask').hide();
            $('#closeTaskForm').show();
        });

        $("#closeTaskForm").on("click", function () {
            $('#summary_form').hide();
            $('#openNewTask').show();
            $('#closeTaskForm').hide();
            $('#date_created_task').val('');
            $("#id").val('');
            $("#shift").val('');
            $("#remarks").val('');
            $("#respond").val('');
            $("#changedby").val('');
            $("#changeddate").val('');
            $("#changedby").val('');
            $("#changeddate").val('');
            $("#respondby").val('');
            $("#responddate").val('');
        });

        $("#closeTaskForm").on("click", function () {
            $('#date_created_task').val('');
            $("#id").val('');
            $("#shift").val('');
            $("#remarks").val('');
            $("#respond").val('');
            $("#changedby").val('');
            $("#changeddate").val('');
            $("#changedby").val('');
            $("#changeddate").val('');
            $("#respondby").val('');
            $("#responddate").val('');
        });

        $(".editbutton").on("click", function () {
            $('#closeTaskForm').show();
            $('#to-top').click();
            $("#summary_form").show();
            let id = $(this).attr('idno');
            let
            date = $(this).attr('date');
            let
            shift = $(this).attr('shift');
            let
            remarks = $(this).attr('remarks');
            let
            respond = $(this).attr('respond');
            let
            createdby = $(this).attr('created_by');
            let
            createdDate = $(this).attr('created_date');
            let
            changeBy = $(this).attr('changed_by');
            let
            changeDate = $(this).attr('changed_date');
            let
            respondBy = $(this).attr('respond_by');
            let
            respondDate = $(this).attr('respond_date');
            var value = changeBy
            var resValue = respond
            $("#id").val(id);
            $("#date_created_task").val(date);
            $("#shift").val(shift);
            $("#remarks").val(remarks);
            $("#respond").val(respond);
            if (value === '') {
                $("#changedby").val(createdby);
                $("#changeddate").val(createdDate);
            }
            else {
                $("#changedby").val(changeBy);
                $("#changeddate").val(changeDate);
            }
            if (resValue !== '') {
                $("#respondby").val(respondBy);
                $("#responddate").val(respondDate);
            }
        });


    });
</script>
@endsection        

