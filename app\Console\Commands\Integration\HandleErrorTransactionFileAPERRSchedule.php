<?php

namespace App\Console\Commands\Integration;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Services\Traits\OSBService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\SupplierFullGrantService;
use SSH;
use DB;
use Response;
use Guzzle;
use App\EpSupportActionLog;

class HandleErrorTransactionFileAPERRSchedule extends Command {
    
    use OSBService;
    use SupplierFullGrantService;
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleErrorTransactionFileAPERR';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Read APERR file (GFM-090) to find Error:Kod Pembekal masih mempunyai transaksi di iGFMAS';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__. ' starting .. '.$this->description, [ 'Date' => Carbon::now()]);
        $dtStartTimeLog = Carbon::now();
        $dtNow = Carbon::now();
        
        //sample 2018-04-19 00:00:00
        
        $dateNow = $dtNow->format('Y-m-d ').'00:00:00';
        $dateEnd = $dtNow->format('Y-m-d H:i:s');

        //$dateNow = '2024-10-01 00:00:00';
        //$dateEnd = '2018-06-28 00:00:00';
        $serviceCodeAperr='GFM-090';

        MigrateUtils::logDump(__METHOD__. ' Date Start: '.$dateNow.', Date End: '.$dateEnd);
        try {
            $list = $this->getListOSBBatchFile($serviceCodeAperr,$dateNow, $dateEnd);
            $listData = collect($list);
            
            
            MigrateUtils::logDump(__METHOD__. ' Check Count: '.count($listData));
            $counter=0;
            foreach ($listData as $objData){
                MigrateUtils::logDump(__METHOD__. ' FileName : '.$objData->file_name);
                $fileName = $objData->file_name;
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
        
                $responseUrl = Guzzle::get($urlMiddleware."/decrypt-file?filename=$fileName");
                $response = Response::make($responseUrl->getBody(), 200);
                $content = $response->content();
                

                //dump('Content',$content);
                if($content != null && strlen($content) > 4){
                    $detectApiveEpNo = "EP-";   // We want to find Error because of send APIVE 
                    $detectHasTransaction = "Kod Pembekal masih mempunyai transaksi di iGFMAS";

                    if (strpos($content, $detectApiveEpNo) !== false 
                        && strpos($content, $detectHasTransaction) !== false ){  
                        MigrateUtils::logDump(__METHOD__. " Contain APERR... : ".$content);
                        $arrayContent = explode("\n", $content);
                        if(count($arrayContent) > 1){
                            $contentLine1 = $arrayContent[1];
                            $contentLine1   = str_replace("EP",'eP',$contentLine1); 
                            $ePNo   = substr($contentLine1, 1, 12);
                            $ePNo   = str_replace("EP",'eP',$ePNo);   
                            MigrateUtils::logDump(__METHOD__. ' eP No: '. $ePNo);
                           
                            $supplierObj = $this->getSMSupplierDetailByEpNo($ePNo);
                            if($supplierObj){
                                $countEpNo = DB::connection('mysql_ep_support')->table('ep_action_log')->where('action_name','PatchDataSupplier')
                                    ->where('action_parameter->ep_no',$ePNo)
                                    ->where('action_parameter->issue','Kod Pembekal masih mempunyai transaksi di iGFMAS')
                                    ->whereDate('created_at',Carbon::now()->format('Y-m-d')
                                    )->count();
                               if($countEpNo == 0){
                                    $updateFields = ['changed_date' => Carbon::now()->addMinutes(4),'changed_by' => 1]; 
                                    $logs = collect([]);
                                    $logQuery = $this->updateSMSupplier($supplierObj->supplier_id,$updateFields);
                                    $logs->put('action_sm_supplier',$logQuery);

                                    $parameters =  collect([]);            
                                    $parameters->put("remarks", $fileName);
                                    $parameters->put("patching", "supplier_changed_date");
                                    $parameters->put("ep_no", $ePNo);
                                    $parameters->put("issue", 'Kod Pembekal masih mempunyai transaksi di iGFMAS');
                                    $parameters->put("error_file", $contentLine1);

                                    EpSupportActionLog::saveActionLog("PatchDataSupplier", "Script", $logs, $parameters, "Completed",'SchedulerAdmin');

                                    MigrateUtils::logDump(__METHOD__. ' '.json_encode($logs));
                                    $counter ++; 
                                }else{
                                    MigrateUtils::logDump(__METHOD__. " Checking already update and send APIVE cause of issue : Kod Pembekal masih mempunyai transaksi di iGFMAS");
                                }
                            }
                        }
                    } else {
                        MigrateUtils::logDump(__METHOD__. " Content >> This APERR file not issue cause of Kod Pembekal masih mempunyai transaksi di iGFMAS");
                    }
                }
            }
            
            MigrateUtils::logDump(__METHOD__. ' Total Send back : '.$counter);
            
            $logsdata = self::class . ' Query Date Start : '.$dateNow.' , Query Date End : '.$dateEnd.' , '
                    . 'Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTimeLog)]);
            MigrateUtils::logDump(__METHOD__. ' '.json_encode($logsdata));

        } catch (\Exception $exc) {
            MigrateUtils::logDump(__METHOD__. ' >> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            MigrateUtils::logErrorDump(__METHOD__. ' >> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
        }
        
    }
    
    
    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error: HandleErrorTransactionFileAPERRSchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
