<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Osb;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\OSBService;

class OsbResendMM504ErrorKodItemFrn {
    use OSBWebService;
    use OSBService;

    public static function resendFRNFailedCauseKodItemCubaSemula( $dateSearch) {
        MigrateUtils::logDump(__METHOD__ . ' Starting ... '. $dateSearch);
        $cls = new OsbResendMM504ErrorKodItemFrn;
       
        $listOsbFrnFailed = $cls->getListTransWsIgfmasGfm110FrnErrorKodItemCubaSemula( $dateSearch);
        MigrateUtils::logDump(__METHOD__ . ' total records list found  :  '.$listOsbFrnFailed->count() );
        foreach($listOsbFrnFailed as $obj){
            self:: resendGFM110MM504($obj->payload_body) ;
            dd('DONE');
        }
        MigrateUtils::logDump(__METHOD__ . ' completed');
        
    }

    public static function resendGFM110MM504($payload) {
        MigrateUtils::logDump(__METHOD__ . ' entering.. ');
        self::sendWebServiceFulfillmentReceivingNoteMm504($payload);       
    }  
    
    public static function sendWebServiceFulfillmentReceivingNoteMm504($payload) {
        MigrateUtils::logDump(__METHOD__ . ' start sending.... ');
         // resend web service http://192.168.63.205:7011/PaymentInstructionsQuery/v1.0?wsdl
         $cls = new OsbResendMM504ErrorKodItemFrn;
         $urlWebService = '/FulfillmentReceivingNote/v1.4';
         $soapAction = 'http://www.ep.gov.my/Service/1-0/FulfillmentReceivingNote/inquire';
         $res = $cls->sendWebServicePayloadBodyTemplate2($urlWebService,$soapAction,$payload);
         MigrateUtils::logDump(__METHOD__ . ' result response  :  '.json_encode($res));
    }  

}
