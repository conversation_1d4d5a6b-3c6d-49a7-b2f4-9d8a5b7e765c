<?php

namespace App\Http\Controllers\AppSupport;

use App\EpSupportActionLog;
use App\Http\Controllers\Controller;
use App\Services\Traits\ProfileService;
use App\Services\Traits\EpWebService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PmUserIssueController extends Controller
{

    use ProfileService;
    use EpWebService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findListUsersNotSyncAsInactive()
    {

        $listUsersNotSyncAsInactive = $this->getListUsersNotSyncAsInactive();

        return view('app_support.sync_inactive_user', ['listdata' => $listUsersNotSyncAsInactive]);
    }

    public function validateUserByStatus(Request $request)
    {
        try {
            $response = $this->wsStatusUserLogin($request->loginid);

            if ($response['status'] === 'Error') {
                return ['code' => 1, 'message' => $response['result']];
            }

            $user_status = $response['result'];
            Log::info($user_status);

            switch ($request->status) {
                case 'I': {
                        if ($user_status['OimStatus'] === 'YES-INACTIVE' && $user_status['LiferayStatus'] === 'YES-INACTIVE') {
                            // if (Str::contains($user_status['OimRole'], 'COMMON_USER') || Str::contains($user_status['LiferayRole'], 'COMMON_USER')) {
                            //     return ['code' => 2, 'message' => false];
                            // }
                            return ['code' => 0, 'message' => true];
                        }

                        break;
                    }
                case 'A':
                    break; //TODO
                default:
            }


            return ['code' => 0, 'message' => false];
        } catch (Exception $e) {
            Log::error(__FUNCTION__ . ' - ' . $e->getMessage());
            return ['code' => 1, 'status' => false];
        }
    }

    public function deactivateUser(Request $request)
    {
        try {
            $user_id = $request->userid;
            $name = $request->name;
            $user_org_id = $request->user_org_id;

            if ($user_org_id != null || $user_org_id !== '') {
                $fields = [
                    'record_status' => 0,
                    'changed_by' => 1,
                    'changed_date' => Carbon::now(),
                ];

                DB::connection('oracle_nextgen_fullgrant')->table('PM_USER_ORG')->where('user_org_id', $user_org_id)->update($fields);
                DB::connection('oracle_nextgen_fullgrant')->table('PM_USER_ROLE')->where('user_org_id', $user_org_id)->update($fields);

                $actionName = 'update';
                $actionType = 'oracle sql';

                $dataParam = collect();
                $dataParam->put("user_id", $user_id);
                $dataParam->put("user_org_id", $user_org_id);

                $dataLog = collect();
                $dataLog->put("remark", "Deactivate User If Both SSO and Liferay Are Inactive");
                $dataLog->put("script_sql", "");

                EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");

                return ['code' => 0, 'message' => 'User ' . $user_id . '-' . $name . ' Successfully Deactivated'];
            }

            return ['code' => 1, 'message' => 'PM User Org Not Found for User ID ' . $user_id];
        } catch (Exception $e) {
            Log::error(__FUNCTION__ . ' - ' . $e->getMessage());
            return ['code' => 1, 'message' => 'Deactivation User Has Error for User ID ' . $user_id . ',' . $e->getMessage()];
        }
    }
}
