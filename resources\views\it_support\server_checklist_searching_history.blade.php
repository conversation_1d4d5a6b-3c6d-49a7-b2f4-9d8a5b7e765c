@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            @if ($section === 'server_check')
                <li>
                    <a href="{{ url('/it_support/server/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/server/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                        History</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/server/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            @endif
            @if ($section === 'status_check')
                <li>
                    <a href="{{ url('/it_support/checklist') }}"><i class="fa fa-tasks"></i>CheckList</a>
                </li>
                <li class="">
                    <a href="{{ url('/it_support/summary_details') }}"><i class="fa fa-list-alt"></i>Summary Details</a>
                </li>
                <li class="active">
                    <a href="{{ url('/it_support/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
                </li>
                <li>
                    <a href="{{ url('/it_support/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
                </li>
            @endif
        </ul>
    </div>
    <div class="block">
        <form id="checklist_history"
            @if ($section == 'server_check') action="{{ url('/it_support/server/checklist/history') }}" @endif
            @if ($section == 'status_check') action="{{ url('/it_support/checklist/history') }}" @endif method="post">
            {{ csrf_field() }}

            <input type="text" id="section_name" name="section_name" value="{{ $section }}"
                class="form-control hide">

            <div id="error_message" style="color: red;"></div>

            <div class="form-group">
                <div class="block-options">
                    <label class="col-md-1 text-right" for="date">Year<span class="text-danger">*</span></label>
                    <div class="col-md-2">
                        @if ($year !== null)
                            <select id="year_search" name="year_search" class="form-control" style="width: 200px;">
                                <option value="">Please Select</option>
                                @foreach ($year as $yr)
                                    <option value="{{ $yr->year }}">{{ $yr->year }}</option>
                                @endforeach
                            </select>
                        @endif
                    </div>
                    <label class="col-md-1 text-right" for="month">Month<span class="text-danger">*</span></label>
                    <div class="col-md-2">
                        <select id="month_search" name="month_search" class="form-control" style="width: 200px;">
                            <option value="">Please Select</option>
                        </select>
                    </div>
                </div>
            </div>

            <div class='text-center' style="background: #ffffff; padding: 5px;">
                <button type="button" id="searching_history" name="searching_history"
                    class="btn btn-sm btn-info text-center">
                    <div class="h5 mb-0" style="font-weight: 800">Search</div>
                </button>
            </div>

            <div id="network_listing" style="display:none">
                <div class="table-responsive">
                    <table id="network_listing_datatable" class="table table-vcenter table-condensed table-bordered">
                    </table>
                </div>
            </div>
        </form>
    </div>

@endsection
@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        $(document).ready(function() {
            const monthNames = {
                1: 'January',
                2: 'February',
                3: 'March',
                4: 'April',
                5: 'May',
                6: 'June',
                7: 'July',
                8: 'August',
                9: 'September',
                10: 'October',
                11: 'November',
                12: 'December'
            };

            $('#year_search').change(function() {
                const selectedYear = $(this).val();
                const monthSelect = $('#month_search');
                var sectionModule = $('#section_name').val();
                monthSelect.empty();
                monthSelect.append('<option value="">Please Select</option>');

                if (selectedYear) {
                    let url = ''; // Initialize URL

                    // Define the URL based on the sectionModule value
                    if (sectionModule === 'server_check') {
                        url = '/it_support/server/checklist/history/getmonths/' + selectedYear + '/' +
                            sectionModule;
                    } else if (sectionModule === 'status_check') {
                        url = '/it_support/status/checklist/history/getmonths/' + selectedYear + '/' +
                            sectionModule;
                    }

                    // Ensure URL is defined before making the AJAX request
                    if (url) {
                        $.ajax({
                            url: url,
                            type: 'GET',
                            success: function(response) {
                                response.forEach(function(month) {
                                    const monthName = monthNames[month
                                        .month]; // Assuming monthNames is defined
                                    monthSelect.append(
                                        `<option value="${month.month}">${monthName}</option>`
                                    );
                                });
                            },
                            error: function(xhr) {
                                console.error("An error occurred while fetching months: ", xhr
                                    .responseText);
                            }
                        });
                    } else {
                        console.error("Invalid sectionModule value.");
                    }
                }
            });
        });

        $(document).ready(function() {
            const errorMessage = $('#error_message');
            $('#searching_history').on('click', function() {
                const selectedYear = $('#year_search').val();
                const monthSelect = $('#month_search').val();
                var sectionModule = $('#section_name').val();
                if (selectedYear && monthSelect) {
                    let url = ''; // Initialize URL

                    // Define the URL based on the sectionModule value
                    if (sectionModule === 'server_check') {
                        url = '/it_support/server/checklist/history/lists/' + selectedYear + '/' +
                            monthSelect + '/server_check';
                    } else if (sectionModule === 'status_check') {
                        url = '/it_support/status/checklist/history/lists/' + selectedYear + '/' +
                            monthSelect + '/status_check';
                    }
                    if (url) {
                        $.ajax({
                            url: url,
                            type: 'GET',
                            success: function(response) {
                                $('#network_listing').show();
                                $('#network_listing').empty();

                                var table = $('<table>').addClass('table').attr('id',
                                    'network_listing_datatable');

                                var headerRow = $('<thead>').append(
                                    $('<tr>').append(
                                        $('<th>').text('Date Task'),
                                        $('<th>').text('Status'),
                                        $('<th>').text('Action')
                                    )
                                );
                                table.append(headerRow);

                                var tbody = $('<tbody>');
                                response.forEach(function(item) {
                                    if (sectionModule === 'server_check') {
                                        var editLink = $('<a>').attr('href',
                                                '/it_support/server/checklist/history/' +
                                                item.task_date + '/' +
                                                sectionModule)
                                            .attr('date_no', item.task_date)
                                            .attr('data-toggle', 'tooltip')
                                            .attr('title', 'Edit')
                                            .attr('target',
                                                '_blank')
                                            .addClass('btn btn-default editbutton')
                                            .append($('<i>').addClass('fa fa-edit'));

                                        var row = $('<tr>').append(
                                            $('<td>').text(item.task_date),
                                            $('<td>').text(item.ack_status),
                                            $('<td>').append(
                                                editLink
                                            )
                                        );
                                    }

                                    if (sectionModule === 'status_check') {
                                        var editLink = $('<a>').attr('href',
                                                '/it_support/status/checklist/history/' +
                                                item.task_date + '/' +
                                                sectionModule)
                                            .attr('date_no', item.task_date)
                                            .attr('data-toggle', 'tooltip')
                                            .attr('title', 'Edit')
                                            .attr('target',
                                                '_blank')
                                            .addClass('btn btn-default editbutton')
                                            .append($('<i>').addClass('fa fa-edit'));

                                        var row = $('<tr>').append(
                                            $('<td>').text(item
                                                .task_date),
                                            $('<td>').text(item.ack_status),
                                            $('<td>').append(
                                                editLink
                                            )
                                        );
                                    }

                                    tbody.append(row);
                                });
                                table.append(tbody);

                                $('#network_listing').append(table);

                                $('#network_listing_datatable').DataTable({
                                    ordering: false,
                                    lengthMenu: [
                                        [10, 20, 30, -1],
                                        [10, 20, 30, 'All']
                                    ]
                                });
                            },
                            error: function(xhr) {
                                console.error("An error occurred while fetching months: ", xhr
                                    .responseText);
                            }
                        });
                    } else {
                        errorMessage.text('Please select a month!');
                    }
                }
            });
        });
    </script>
@endsection
