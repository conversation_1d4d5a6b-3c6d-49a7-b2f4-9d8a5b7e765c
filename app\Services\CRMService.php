<?php

namespace App\Services;

use DB;
use Carbon\Carbon;
use App\Tasks;
use App\TaskCustom;
use App\Cases;
use Ramsey\Uuid\Uuid;
use Log;
use GuzzleHttp\Client;
use Response;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
class CRMService
{

    public static $CRM_GROUP_USER = array(
        'Group Business Coordinator' =>
        array(
            'name' => 'Group Business Coordinator (Case Owner)',
            'name_task' => 'Assigned to Case Owner',
            'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
            'assign_group' => 'Case Owner'  // Dont Changed It
        ),
        'Group Production Support' =>
        array(
            'name' => 'Group Production Support',
            'name_task' => 'Assigned to Group IT Specialist(Production Support)',
            'user_group_id' => 'd3bf216c-122b-4ce5-9410-899317762b60',
            'assign_group' => 'Group IT Specialist(Production Support)'  // Dont Changed It
        ),
        //            'Group Archisoft Build Team' =>  
        //                array(
        //                    'name' => 'Group Archisoft Build Team',
        //                    'name_task' => 'Assigned to Group Archisoft Build Team',
        //                    'user_group_id' => '15c7bd74-12f3-311b-9e8f-5a810702a1a5',
        //                    'assign_group' => 'Archissoft Build Team'  // Dont Changed It
        //                ),
        'Group Middleware' =>
        array(
            'name' => 'Group Middleware',
            'name_task' => 'Assigned to Group Middleware',
            'user_group_id' => '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e',
            'assign_group' => 'Group Middleware'  // Dont Changed It
        ),
        'Group IT Network Admin' =>
        array(
            'name' => 'Group IT Network Admin',
            'name_task' => 'Assigned to Group IT Specialist(Network Admin)',
            'user_group_id' => 'bb59c521-4a4d-a001-a8aa-58d09f694ae7',
            'assign_group' => 'Group IT Specialist(Network Admin)'  // Dont Changed It
        ),
        'Group IT Database Admin' =>
        array(
            'name' => 'Group IT Database Admin',
            'name_task' => 'Assigned to Group IT Specialist(Database Admin)',
            'user_group_id' => '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad',
            'assign_group' => 'Group IT Specialist(Database Admin)'  // Dont Changed It
        ),
        'Group IT Database Management' =>
        array(
            'name' => 'Group IT Database Management',
            'name_task' => 'Assigned to Group IT Specialist(Database Management)',
            'user_group_id' => '3d74f2af-f673-4263-afc0-d3f89aadf0ef',
            'assign_group' => 'Group IT Specialist(Database Management)'  // Dont Changed It
        ),
        'Group PMO' =>
        array(
            'name' => 'Group PMO', //'Group IT Developer',
            'name_task' => 'Assigned to Group PMO',
            'user_group_id' => '5dac7b92-18d0-4beb-b600-413957aa4c26',
            'assign_group' => 'Group PMO'  // Dont Changed It
        ),
        'Group IT Security' =>
        array(
            'name' => 'Group IT Security',
            'name_task' => 'Assigned to Group IT Specialist(Security)',
            'user_group_id' => '86dde498-da2d-4208-a7dd-786980e6827a',
            'assign_group' => 'Group IT Specialist(Security)'  // Dont Changed It
        ),
        'Group IT Server Admin' =>
        array(
            'name' => 'Group IT Server Admin',
            'name_task' => 'Assigned to Group IT Specialist(Server Admin)',
            'user_group_id' => '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4',
            'assign_group' => 'Group IT Specialist(Server Admin)'  // Dont Changed It
        )
    );
    public static $CRM_GROUP_USER_SUPPORT = array(
        'Group Business Coordinator' =>
        array(
            'name' => 'Group Business Coordinator (Case Owner)',
            'name_task' => 'Assigned to Case Owner',
            'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
            'assign_group' => 'Case Owner'  // Dont Changed It
        ),
        'Group Production Support' =>
        array(
            'name' => 'Group Production Support',
            'name_task' => 'Assigned to Production Support',
            'user_group_id' => 'd3bf216c-122b-4ce5-9410-899317762b60',
            'assign_group' => 'Group IT Specialist(Production Support)'  // Dont Changed It
        )
    );
    public static $CRM_GROUP_CASE_OWNER = array(
        'Group Business Coordinator' =>
        array(
            'name' => 'Group Business Coordinator (Case Owner)',
            'name_task' => 'Assigned to Case Owner',
            'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
            'assign_group' => 'Case Owner'  // Dont Changed It
        )
    );
    public static $CRM_GROUP_CASE_OWNER_PMO = array(
        'Group Business Coordinator' =>
        array(
            'name' => 'Group Business Coordinator (Case Owner)',
            'name_task' => 'Assigned to Case Owner',
            'user_group_id' => '2732c16c-1fff-9e6c-bb4e-58997edd3340',
            'assign_group' => 'Case Owner'  // Dont Changed It
        ),
        'Group PMO' =>
        array(
            'name' => 'Group PMO', //'Group IT Developer',
            'name_task' => 'Assigned to Group PMO',
            'user_group_id' => '5dac7b92-18d0-4beb-b600-413957aa4c26',
            'assign_group' => 'Group PMO'  // Dont Changed It
        )
    );
    public static $CRM_GROUP_SPECIALIST = array(
        'Group Production Support' =>
        array(
            'name' => 'Group Production Support',
            'name_task' => 'Assigned to Group IT Specialist(Production Support)',
            'user_group_id' => 'd3bf216c-122b-4ce5-9410-899317762b60',
            'assign_group' => 'Group IT Specialist(Production Support)'  // Dont Changed It
        ),
        //            'Group Archisoft Build Team' =>  
        //                array(
        //                    'name' => 'Group Archisoft Build Team',
        //                    'name_task' => 'Assigned to Group Archisoft Build Team',
        //                    'user_group_id' => '15c7bd74-12f3-311b-9e8f-5a810702a1a5',
        //                    'assign_group' => 'Archissoft Build Team'  // Dont Changed It
        //                ),
        'Group Middleware' =>
        array(
            'name' => 'Group Middleware',
            'name_task' => 'Assigned to Group Middleware',
            'user_group_id' => '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e',
            'assign_group' => 'Group Middleware'  // Dont Changed It
        ),
        'Group IT Network Admin' =>
        array(
            'name' => 'Group IT Network Admin',
            'name_task' => 'Assigned to Group IT Specialist(Network Admin)',
            'user_group_id' => 'bb59c521-4a4d-a001-a8aa-58d09f694ae7',
            'assign_group' => 'Group IT Specialist(Network Admin)'  // Dont Changed It
        ),
        'Group IT Database Admin' =>
        array(
            'name' => 'Group IT Database Admin',
            'name_task' => 'Assigned to Group IT Specialist(Database Admin)',
            'user_group_id' => '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad',
            'assign_group' => 'Group IT Specialist(Database Admin)'  // Dont Changed It
        ),
        'Group IT Database Management' =>
        array(
            'name' => 'Group IT Database Management',
            'name_task' => 'Assigned to Group IT Specialist(Database Management)',
            'user_group_id' => '3d74f2af-f673-4263-afc0-d3f89aadf0ef',
            'assign_group' => 'Group IT Specialist(Database Management)'  // Dont Changed It
        ),
        'Group PMO' =>
        array(
            'name' => 'Group PMO', //'Group IT Developer',
            'name_task' => 'Assigned to Group PMO',
            'user_group_id' => '5dac7b92-18d0-4beb-b600-413957aa4c26',
            'assign_group' => 'Group PMO'  // Dont Changed It
        ),
        'Group IT Security' =>
        array(
            'name' => 'Group IT Security',
            'name_task' => 'Assigned to Group IT Specialist(Security)',
            'user_group_id' => '86dde498-da2d-4208-a7dd-786980e6827a',
            'assign_group' => 'Group IT Specialist(Security)'  // Dont Changed It
        ),
        'Group IT Server Admin' =>
        array(
            'name' => 'Group IT Server Admin',
            'name_task' => 'Assigned to Group IT Specialist(Server Admin)',
            'user_group_id' => '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4',
            'assign_group' => 'Group IT Specialist(Server Admin)'  // Dont Changed It
        )
    );
    public static $CRM_GROUP_EPP1 = array(
        'Group ePP1' =>
        array(
            'name' => 'Group ePP1',
            'name_task' => 'Assigned to Group ePP1',
            'user_group_id' => 'c131af4a-a19e-3394-06b0-638ff2a99564',
            'assign_group' => 'Group ePP1'  // Dont Changed It
        )
    );
    public static $IT_COORDINATOR_ID = 'bd305f97-902e-e186-f506-58997eeecc12';
    public static $PRODUCTION_SUPPORT = 'd3bf216c-122b-4ce5-9410-899317762b60';
    public static $MIDDLEWARE = '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e';
    public static $PMO = '5dac7b92-18d0-4beb-b600-413957aa4c26';
    public static $APPROVER = '97e7c38b-650a-778b-4ec4-58997d0e97fc';
    public static $EPP1 = 'c131af4a-a19e-3394-06b0-638ff2a99564';

    public function getDetailLookupCRM($type, $value)
    {
        $query = DB::table('cstm_list_app');
        $query->where('type_code', $type);
        $query->where('value_code', $value);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRM($id)
    {
        $query = DB::table('users');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailUserCRMByUsername($username)
    {
        $query = DB::table('users');
        $query->where('user_name', $username);
        $data = $query->first();
        return $data;
    }

    public function getDetailContactCRM($id)
    {
        $query = DB::table('contacts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountCRM($id)
    {
        if ($id == null) {
            return null;
        }

        $query = DB::table('accounts');
        $query->where('id', $id);
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPtjToKementerianCRM($id)
    {
        $query = DB::table('accounts as ptj');
        $query->join('accounts as kumpulanptj', 'ptj.parent_id', '=', 'kumpulanptj.id');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('ptj.id', $id);
        $query->where('ptj.deleted', 0);
        $query->select('ptj.name as ptj_name', 'kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountKumpulanPtjToKementerianCRM($id)
    {
        $query = DB::table('accounts as kumpulanptj');
        $query->join('accounts as pegawaipengawal', 'kumpulanptj.parent_id', '=', 'pegawaipengawal.id');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('kumpulanptj.id', $id);
        $query->where('kumpulanptj.deleted', 0);
        $query->select('kumpulanptj.name as kumpulanptj_name', 'pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailAccountPegawaiPengawalToKementerianCRM($id)
    {
        $query = DB::table('accounts as pegawaipengawal');
        $query->join('accounts as kementerian', 'pegawaipengawal.parent_id', '=', 'kementerian.id');
        $query->where('pegawaipengawal.id', $id);
        $query->where('pegawaipengawal.deleted', 0);
        $query->select('pegawaipengawal.name as pegawaipengawal_name', 'kementerian.name as kementerian_name');
        $data = $query->first();
        return $data;
    }

    public function getDetailCase($caseNumber)
    {
        $query = DB::table('cases');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('cases.case_number', $caseNumber);
        $data = $query->first();
        return $data;
    }

    public function getDetailTaskLatestCRM($caseId)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->select('tasks.*', 'tasks_cstm.*', 'tasks.resolution_category_c as resolution_category');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailCaseAndTaskLatestCRM($caseNumber)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->where('cases.case_number', $caseNumber);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->where('cases.deleted', 0);
        $query->select('tasks.*');
        $query->addSelect('tasks_cstm.*', 'tasks.id as task_id');
        $query->addSelect('cases.id as case_id', 'cases.name as case_name', 'cases.description as case_description', 'cases.case_number', 'cases.status as case_status', 'cases.state as case_state');
        $query->addSelect('cases_cstm.incident_service_type_c as incidentservicetype');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

    public function getDetailCaseAndTaskAllCRM($caseNumber)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases.id', '=', 'cases_cstm.id_c');
        $query->join('users', 'users.id', '=', 'tasks.created_by');
        $query->where('cases.case_number', $caseNumber);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->where('cases.deleted', 0);
        $query->select('cases.*', 'cases_cstm.*', 'tasks.*', 'tasks_cstm.*');
        $query->addSelect('cases.id as case_id', 'tasks.name as taskname', 'tasks.id as taskid', 'tasks.assigned_user_id as task_assigned', 'tasks.status as taskstatus');
        $query->addSelect('tasks.deleted as task_deleted');
        $query->addSelect(DB::raw("CONVERT_TZ(tasks.date_entered,'+00:00','+08:00') as task_date_created"));
        $query->addSelect(DB::raw("CONVERT_TZ(tasks.date_modified,'+00:00','+08:00') as task_date_modified"));
        $query->addSelect('users.user_name as createdby');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getListDetailGroupCRM($userId)
    {
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups_users.user_id', $userId);
        $query->where('securitygroups_users.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public function getListDetailGroupMiddleware()
    {
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups.id', '2d03a7e9-b033-4573-804d-5c4f8f64fa0c');
        $query->where('securitygroups_users.deleted', 0);
        $data = $query->get();
        return $data;
    }

    public function getEmailDetail($module, $moduleId)
    {
        $query = DB::table('email_addresses');
        $query->join('email_addr_bean_rel', 'email_addresses.id', '=', 'email_addr_bean_rel.email_address_id');
        $query->where('email_addr_bean_rel.bean_module', $module);
        $query->where('email_addr_bean_rel.bean_id', $moduleId);
        $data = $query->first();
        return $data;
    }

    public function getDetailLookupCRMByType($type)
    {
        $query = DB::table('cstm_list_app');
        $query->where('type_code', $type);
        $query->where('deleted', 0);
        $query->where('status', 1);
        $query->whereNotIn('value_code', ['']);
        $query->select('value_code', 'value_name');
        $query->orderBy('value_name', 'asc');
        $data = $query->get();
        return $data;
    }

    /*
      SELECT * FROM notes WHERE parent_type='AOP_Case_Updates' AND parent_id IN (SELECT id FROM aop_case_updates WHERE case_id = '5ce1020f-b092-7c14-d706-5b67ad64ca41')
      AND filename IS NOT NULL;

      SELECT * FROM notes WHERE parent_type='Cases' AND parent_id = '5ce1020f-b092-7c14-d706-5b67ad64ca41'
      AND filename IS NOT NULL;
     */

    /**
     * 
     * @param type $parentType
     * @param type $listParentId  array ['aaa','bbb']
     * @return list
     */
    public function listNotesFile($parentType, $listParentId)
    {
        $query = DB::table('notes');
        $query->where('parent_type', $parentType);
        $query->whereIn('parent_id', $listParentId);
        $query->where('deleted', 0);
        //$query->whereNotNull('filename');
        $data = $query->get();
        return $data;
    }

    public function updateDeleteNoteFiles($fileId, $userId)
    {
        $url = env('CRM_EP_HOSTNAME', null);
        $verifyKey = env('CRM_EP_VRFKEY_DeleteNoteFiles', null);
        if (!$url || !$verifyKey) {
            return response()->json([
                'status_code' => 400,
                'status_message' => 'ENV Variable NULL'
            ], 400);
        }
        $url = $url . '/index.php?entryPoint=DeleteNoteFiles';
        $time = Carbon::now()->format('Y-m-d H:i:s');

        $jsonData = json_encode(
            array(
                'keyAgent' => md5($userId . $time . $fileId . $verifyKey),
                'time' => $time,
                'userId' => $userId,
                'fileId' => $fileId
            )
        );

        try {
            $client = new Client();
            $response = $client->post($url, [
                'headers' => ['Content-Type' => 'application/json'],
                'body' => $jsonData
            ]);

            $responseContent = json_decode($response->getBody(), true);
            return response()->json($responseContent, $response->getStatusCode());
        } catch (\Exception $e) {
            return response()->json([
                'status_code' => 500,
                'status_message' => 'API Request Error, cannot process'
            ], 500);
        }
    }

    public function getDetailLeadCRM($id)
    {
        $query = DB::table('leads');
        $query->join('leads_cases_1_c', 'leads.id', '=', 'leads_cases_1_c.leads_cases_1cases_idb');
        $query->join('leads_cstm', 'leads.id', '=', 'leads_cstm.id_c');
        $query->where('leads_cases_1_c.leads_cases_1leads_ida', $id);
        $data = $query->first();
        return $data;
    }

    public function getValueLookupCRM($type, $value)
    {
        $data = $this->getDetailLookupCRM($type, $value);
        if ($data) {
            return $data->value_name;
        }
    }

    public function getNameUserCRM($id)
    {
        $data = $this->getDetailUserCRM($id);
        if ($data) {
            return $data->first_name;
        }
    }

    public function getNameContactCRM($id)
    {
        $data = $this->getDetailContactCRM($id);
        if ($data) {
            return $data->first_name;
        }
    }

    public function getEmailCRM($module, $moduleId)
    {
        $data = $this->getEmailDetail($module, $moduleId);
        if ($data) {
            return $data->email_address;
        }
    }

    /**
     * Request on email to resolve task Pharmaniaga as Resolve based on list case given.
     */
    public static function resolveTaskByListCaseNo()
    {

        $listCaseNo = array(
            //'2967556',
            // '2963812',
        );
        $newResolution = 'Hasil dari semakan yang dibuat.
Masalah yang dihadapi pada dokumen ini sudah diselesaikan.';
        foreach ($listCaseNo as $caseNo) {
            $crmServ = new CRMService();
            $caseTask = $crmServ->getDetailCaseAndTaskLatestCRM($caseNo);
            if ($caseTask && ($caseTask->case_status == 'In_Progress' || $caseTask->case_status == 'Open_Assigned')) {

                //RESOLVED CASE
                if (strpos($caseTask->name, 'Initial Task') !== false) {
                    $crmServ->updateTaskCaseToResolve($caseTask, $newResolution, "INITIAL", null);
                    dump('Resolved Initial Task to Case Owner : ' . $caseNo);
                } else {
                    $crmServ->updateTaskCaseToResolve($caseTask, $newResolution, '4HOUR', null);
                    dump('Resolved Specialist Task to Case Owner : ' . $caseNo);
                }
            } else {
                dump('This case number: ' . $caseNo . ' already as status: ' . $caseTask->case_status);
            }
        }
    }

    public static $RESOLUTION_CATEGORY = array(
        'data_fix' => 'Data Fix - User Request',
        'data_fix_system_problem' => 'Data Fix - System Problem',
        'program_fix' => 'Program Fix',
        'data_program_fix' => 'Data Fix & Program Fix',
        'user_familiarity' => 'User Familiarity',
    );
    public static $CRM_SLA_SEVERITY = array(
        's1' => 'S1',
        's2' => 'S2',
        's3' => 'S3'
    );
    public static $CRM_CATEGORY_FACTOR = array(
        'external_factor' => 'External Factor',
        'internal_factor' => 'Internal Factor'
    );
    public static $CRM_TASK_JUSTIFICATION = array(
        'technical' => 'Technical',
        'human' => 'Human',
        'technical_trusgate' => 'Technical Trusgate',
        'technical_digicert' => 'Technical Digicert'
    );
    public static $CRM_INCIDENT_FACTOR = array(
        'internal_factor_12110' => 'SERVICE DOWN - CORE MODULE',
        'internal_factor_12111' => 'SERVICE DOWN - SUPPORTING MODULES',
        'internal_factor_12112' => 'SERVICE DOWN - CUSTOMER SUPPORT MANAGEMENT (CONTACT CENTRE)',
        'internal_factor_12113' => 'DATA INTEGRITY ISSUE',
        'internal_factor_12114' => 'SYSTEM SECURITY ISSUE',
        'internal_factor_12115' => 'IT SERVICE REQUEST / CHANGE REQUEST',
        'internal_factor_12116' => 'NORMAL INCIDENT'
    );

    public function getDashboardCRM()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseno',
            'tasks.status as status',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as flag',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.sla_start_15min_c as actualstart',
            'tasks_cstm.sla_stop_15min_c as actualstop',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration'
        );
        $query->orderBy('tasks.date_start', 'asc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMSpecialist()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->join('poms_itspec', 'poms_itspec.case_number', '=', 'cases.case_number');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->where('poms_itspec.itspec_sla_flag', 3);
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseno',
            'tasks.status as status',
            'tasks.name as taskname',
            'poms_itspec.itspec_sla_flag as flag',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.sla_start_4hr_c as actualstart',
            'tasks_cstm.sla_stop_4hr_c as actualstop',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration'
        );
        $query->orderBy('tasks.date_start', 'asc');
        $data = $query->get();
        return $data;
    }

    public function getLatestJobQue($name)
    {
        $query = DB::table('job_queue');
        $query->where('name', $name);
        $query->orderBy('execute_time', 'desc');
        $data = $query->first();
        return $data;
    }

    public static function updateTaskAcknowledge($caseObj, $actionBy, $typeTask = null, $slaFlag, $incidentservicetype)
    {

        if ($actionBy == null) {
            $actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';
        }
        $flaFlagValue = '';
        if ($incidentservicetype == 'incident_it') {
            $flaFlagValue = $slaFlag;
        }

        if ($typeTask == 'INITIAL') {

            $checkTask = DB::table('tasks')->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c')
                ->where('tasks.id', $caseObj->task_id)->first();

            if ($checkTask && $checkTask->status == 'Acknowledge' && $checkTask->acknowledge_time_c !== null && $checkTask->sla_stop_15min_c !== null) {
                Log::info(self::class . ' ' . __FUNCTION__ . ' Task ' . $typeTask . ' already acknowledged! Do not override.. ' . $caseObj->task_id);
                $queryTaskCstm = DB::table('tasks_cstm')
                    ->where('id_c', $caseObj->task_id)
                    ->update([
                        'acknowledge_by_userid_c' => $actionBy
                    ]);
            } else {
                Log::info(self::class . ' ' . __FUNCTION__ . ' Task ' . $typeTask . ' still not acknowledged.. ' . $caseObj->task_id);
                $queryTaskCstm = DB::table('tasks_cstm')
                    ->where('id_c', $caseObj->task_id)
                    ->update([
                        'acknowledge_time_c' => Carbon::now()->subHour(8),
                        'sla_stop_15min_c' => Carbon::now()->subHour(8),
                        'acknowledge_by_userid_c' => $actionBy
                    ]);
            }
        } else {
            Log::info(self::class . ' ' . __FUNCTION__ . ' Task ' . $typeTask . ' ' . $caseObj->task_id);
            $queryTaskCstm = DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'acknowledge_time_c' => Carbon::now()->subHour(8),
                    'sla_start_4hr_c' => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c' => $actionBy,
                    'sla_task_flag_c' => $flaFlagValue
                ]);
        }

        $queryCase = DB::table('cases')
            ->where('id', $caseObj->case_id)
            ->update([
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $actionBy
            ]);

        $queryTask = DB::table('tasks')
            ->where('id', $caseObj->task_id)
            ->update([
                'status' => 'Acknowledge',
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $actionBy
            ]);

        $data = $queryTaskCstm . $queryCase . $queryTask;

        return $data;
    }

    /**
     * 
     * @param type $caseObj
     * @param type $newResolution
     * @param type $typeTask   
     * @param string $actionBy
     */
    public static function updateTaskCaseToResolve($caseObj, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $typeTask = null, $actionBy = null, $slaFlag, $incidentservicetype, $statusEk, $incidentClassification)
    {

        if ($actionBy == null) {
            $actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';
        }  // default (must be not null)

        $flaFlagValue = '';
        if ($incidentservicetype == 'incident_it') {
            $flaFlagValue = $slaFlag;
        }
        // update case Completed
        if ($typeTask == '4HOUR') {
            $queryTask = DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status' => 'Completed',
                    'task_justification' => 'technical',
                    'date_modified' => Carbon::now()->subHour(8),
                    'modified_user_id' => $actionBy,
                    'task_justification' => $taskJustification,
                    'resolution_category_c' => $resolutionCategory,
                    'econtract_status' => $statusEk,
                    'incident_classification' => $incidentClassification
                ]);
            $queryTaskCstm = DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c' => $newResolution,
                    'sla_stop_4hr_c' => Carbon::now()->subHour(8),
                    'date_execution_time_c' => Carbon::now()->subHour(8),
                    'sla_task_flag_c' => $flaFlagValue,
                    'category_factor_c' => $categoryFactor,
                    'tasks_combine_c' => $incidentFactor,
                    'reserve_task_userid' => $caseObj->reserve_task_userid
                ]);
        } else {
            //This refer Initial Task
            $queryTask = DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status' => 'Completed',
                    'date_modified' => Carbon::now()->subHour(8),
                    'modified_user_id' => $actionBy,
                    'task_justification' => $taskJustification,
                    'resolution_category_c' => $resolutionCategory,
                    'econtract_status' => $statusEk,
                    'incident_classification' => $incidentClassification
                ]);
            $queryTaskCstm = DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c' => $newResolution,
                    'date_execution_time_c' => Carbon::now()->subHour(8),
                    'category_factor_c' => $categoryFactor,
                    'tasks_combine_c' => $incidentFactor,
                    'reserve_task_userid' => $caseObj->reserve_task_userid
                ]);
        }
        $crmServ = new CRMService();
        $checkLatestTask = $crmServ->getDetailCaseAndTaskLatestCRM($caseObj->case_number);
        Log::info($caseObj->case_number . ' > Latest Task : ' . $checkLatestTask->name);
        //check task already created or not, to avoid duplicate record.
        if ($checkLatestTask && $checkLatestTask->name !== 'Assigned to Case Owner') {
            $newTask = new Tasks;
            $newTask->id = Uuid::uuid4()->toString();
            $newTask->name = 'Assigned to Case Owner';
            $newTask->description = $caseObj->case_description;
            $newTask->date_entered = Carbon::now()->subHour(8);
            $newTask->date_modified = Carbon::now()->subHour(8);
            $newTask->modified_user_id = $actionBy;
            $newTask->created_by = $actionBy;
            $newTask->deleted = 0;
            $newTask->assigned_user_id = '2732c16c-1fff-9e6c-bb4e-58997edd3340';  //Group Business Coordinator
            $newTask->status = 'Pending Acknowledgement';
            $newTask->date_due_flag = 0;
            $newTask->date_start = Carbon::now()->subHour(8);
            $newTask->date_due = Carbon::now()->subHour(8)->addMinutes(15); // NOW() + 15 minutes
            $newTask->date_start_flag = 0;
            $newTask->parent_type = 'Cases';
            $newTask->parent_id = $caseObj->case_id;
            $newTask->contact_id = $caseObj->contact_id;
            $newTask->priority = 'Low';
            $newTask->task_justification = $taskJustification;
            $newTask->resolution_category_c = $resolutionCategory;
            $newTask->incident_classification = $incidentClassification;
            if ($caseObj->task_pic != null) {
                $newTask->task_pic = $caseObj->task_pic;
            }
            $newTask->econtract_status = $statusEk;

            $newTask->save();

            $newTaskCustom = new TaskCustom;
            $newTaskCustom->id_c = $newTask->id;
            $newTaskCustom->assign_group_c = 'Case Owner';
            $newTaskCustom->resolution_c = $newResolution;
            $newTaskCustom->sla_flag_c = 0;
            $newTaskCustom->checkbox_add_day_c = 0;
            $newTaskCustom->category_factor_c = $categoryFactor;
            $newTaskCustom->tasks_combine_c = $incidentFactor;
            $newTaskCustom->reserve_task_userid = $caseObj->reserve_task_userid;
            $newTaskCustom->save();
        } else {
            Log::info('Task Already Created. Skip!! ' . $caseObj->case_number);
        }

        $caseModel = Cases::find($caseObj->case_id);
        $caseModel->date_modified = Carbon::now()->subHour(8);
        $caseModel->modified_user_id = $actionBy;
        $caseModel->status = 'Open_Resolved';
        $caseModel->resolution = $newResolution;
        $caseModel->sla_flag = $flaFlagValue;
        $caseModel->case_factor = $categoryFactor;
        $caseModel->case_justification = $taskJustification;
        $caseModel->save();

        $data = $queryTaskCstm . $queryTask;

        return $data;
    }

    public static function updateTaskToAssignAnotherGroup($caseObj, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $userGroupId, $assignGroupName, $taskNameGroup, $typeTask = null, $actionBy = null, $redmineNo = null, $slaFlag, $incidentservicetype, $statusEk = null, $incidentClassification)
    {

        if ($actionBy == null) {
            $actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';
        }  // default (must be not null)

        $flaFlagValue = '';
        if ($incidentservicetype == 'incident_it') {
            $flaFlagValue = $slaFlag;
        }

        // update case Completed
        if ($typeTask == '4HOUR') {
            $queryTask = DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'status' => 'Completed',
                    'task_justification' => $taskJustification,
                    'date_modified' => Carbon::now()->subHour(8),
                    'modified_user_id' => $actionBy,
                    'resolution_category_c' => $resolutionCategory,
                    'econtract_status' => $statusEk,
                    'incident_classification' => $incidentClassification
                ]);
            $queryTaskCstm = DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c' => $newResolution,
                    'sla_stop_4hr_c' => Carbon::now()->subHour(8),
                    'date_execution_time_c' => Carbon::now()->subHour(8),
                    'sla_task_flag_c' => $flaFlagValue,
                    'acknowledge_time_c' => Carbon::now()->subHour(8),
                    'acknowledge_by_userid_c' => $actionBy,
                    'category_factor_c' => $categoryFactor,
                    'tasks_combine_c' => $incidentFactor,
                    'reserve_task_userid' => $caseObj->reserve_task_userid
                ]);
        } else {
            //This refer Initial Task
            $queryTask = DB::table('tasks')
                ->where('id', $caseObj->task_id)
                ->update([
                    'date_modified' => Carbon::now()->subHour(8),
                    'modified_user_id' => $actionBy,
                    'status' => 'Completed',
                    'task_justification' => $taskJustification,
                    'resolution_category_c' => $resolutionCategory,
                    'incident_classification' => $incidentClassification
                ]);
            $queryTaskCstm = DB::table('tasks_cstm')
                ->where('id_c', $caseObj->task_id)
                ->update([
                    'resolution_c' => $newResolution,
                    'date_execution_time_c' => Carbon::now()->subHour(8),
                    'category_factor_c' => $categoryFactor,
                    'tasks_combine_c' => $incidentFactor,
                    'reserve_task_userid' => $caseObj->reserve_task_userid
                ]);
        }

        $queryCase = DB::table('cases')
            ->where('id', $caseObj->case_id)
            ->update([
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $actionBy,
                'resolution' => $newResolution,
                'sla_flag' => $flaFlagValue,
                'case_factor' => $categoryFactor,
                'case_justification' => $taskJustification,
                'incident_classification' => $incidentClassification
            ]);

        $newTask = new Tasks;
        $newTask->id = Uuid::uuid4()->toString();
        $newTask->name = $taskNameGroup;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = $actionBy;
        $newTask->created_by = $actionBy;
        $newTask->description = $caseObj->case_description;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = $userGroupId;   // Group User ID
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = Carbon::now()->subHour(8)->addHour(4); // NOW() + 4 hour
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->contact_id = $caseObj->contact_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = $taskJustification;
        $newTask->resolution_category_c = $resolutionCategory;
        $newTask->incident_classification = $incidentClassification;
        if ($redmineNo != null) {
            $newTask->case_redmine_number = $redmineNo;
        }
        if ($caseObj->task_pic != null) {
            $newTask->task_pic = $caseObj->task_pic;
        }
        if ($statusEk != null) {
            $newTask->econtract_status = $statusEk;
        }
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = $assignGroupName;
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = $categoryFactor;
        $newTaskCustom->tasks_combine_c = $incidentFactor;
        $newTaskCustom->reserve_task_userid = $caseObj->reserve_task_userid;
        $newTaskCustom->save();

        if ($redmineNo != null) {
            DB::table('cases')
                ->where('id', $caseObj->case_id)
                ->update([
                    'redmine_number' => $redmineNo
                ]);
        }

        return $queryTask . $queryTaskCstm . $queryCase;
    }

    public static function updateTaskToAssignAnotherGroupSeverity($caseObj, $newResolution, $resolutionCategory, $categoryFactor, $taskJustification, $incidentFactor, $userGroupId, $assignGroupName, $taskNameGroup, $actionBy = null, $redmineNo = null, $slaFlag, $severity, $statusEk = null, $incidentClassification)
    {

        if ($actionBy == null) {
            $actionBy = '61795c8d-9bbf-46eb-9187-dc59f49101bf';
        }  // default (must be not null)
        // update case Completed

        $queryTask = DB::table('tasks')
            ->where('id', $caseObj->task_id)
            ->update([
                'status' => 'Completed',
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $actionBy,
                'task_justification' => $taskJustification,
                'econtract_status' => $statusEk,
                'incident_classification' => $incidentClassification
            ]);
        $queryTaskCstm = DB::table('tasks_cstm')
            ->where('id_c', $caseObj->task_id)
            ->update([
                'resolution_c' => $newResolution,
                'sla_stop_4hr_c' => Carbon::now()->subHour(8),
                'date_execution_time_c' => Carbon::now()->subHour(8),
                'category_factor_c' => $categoryFactor,
                'tasks_combine_c' => $incidentFactor,
                'reserve_task_userid' => $caseObj->reserve_task_userid
            ]);

        $queryCase = DB::table('cases')
            ->where('id', $caseObj->case_id)
            ->update([
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $actionBy,
                'resolution' => $newResolution,
                'sla_flag' => $slaFlag,
                'incident_classification' => $incidentClassification
            ]);

        $dateDue = Carbon::now()->subHour(8);
        if ($severity == 's1' || $severity == 's5') {
            $dateDue = Carbon::now()->subHour(8)->addDay(1);
        }
        if ($severity == 's2') {
            $dateDue = Carbon::now()->subHour(8)->addDay(3);
        }
        if ($severity == 's3') {
            $dateDue = Carbon::now()->subHour(8)->addDay(5);
        }
        $newTask = new Tasks;
        $newTask->id = Uuid::uuid4()->toString();
        $newTask->name = $taskNameGroup;
        $newTask->date_entered = Carbon::now()->subHour(8);
        $newTask->date_modified = Carbon::now()->subHour(8);
        $newTask->modified_user_id = $actionBy;
        $newTask->created_by = $actionBy;
        $newTask->description = $caseObj->case_description;
        $newTask->deleted = 0;
        $newTask->assigned_user_id = $userGroupId;   // Group User ID
        $newTask->status = 'Pending Acknowledgement';
        $newTask->date_due_flag = 0;
        $newTask->date_start = Carbon::now()->subHour(8);
        $newTask->date_due = $dateDue; // NOW() + severity
        $newTask->date_start_flag = 0;
        $newTask->parent_type = 'Cases';
        $newTask->parent_id = $caseObj->case_id;
        $newTask->contact_id = $caseObj->contact_id;
        $newTask->priority = 'Low';
        $newTask->task_justification = $taskJustification;
        $newTask->resolution_category_c = $resolutionCategory;
        $newTask->task_severity = $severity;
        $newTask->incident_classification = $incidentClassification;
        if ($redmineNo != null) {
            $newTask->case_redmine_number = $redmineNo;
        }
        if ($caseObj->task_pic != null) {
            $newTask->task_pic = $caseObj->task_pic;
        }
        if ($statusEk != null) {
            $newTask->econtract_status = $statusEk;
        }
        $newTask->save();

        $newTaskCustom = new TaskCustom;
        $newTaskCustom->id_c = $newTask->id;
        $newTaskCustom->assign_group_c = $assignGroupName;
        $newTaskCustom->resolution_c = $newResolution;
        $newTaskCustom->sla_flag_c = 0;
        $newTaskCustom->checkbox_add_day_c = 0;
        $newTaskCustom->category_factor_c = $categoryFactor;
        $newTaskCustom->tasks_combine_c = $incidentFactor;
        $newTaskCustom->reserve_task_userid = $caseObj->reserve_task_userid;
        $newTaskCustom->save();

        if ($redmineNo != null) {
            DB::table('cases')
                ->where('id', $caseObj->case_id)
                ->update([
                    'redmine_number' => $redmineNo
                ]);
        }

        return $queryTask . $queryTaskCstm . $queryCase;
    }

    public static function updateTaskOwn($caseObj, $actionBy)
    {

        $queryTaskCstm = DB::table('tasks_cstm')
            ->where('id_c', $caseObj->task_id)
            ->update([
                'reserve_task_userid' => $actionBy,
            ]);

        return $queryTaskCstm;
    }

    public static function updateTaskOwnByTaskId($taskId, $actionBy)
    {

        $queryTaskCstm = DB::table('tasks_cstm')
            ->where('id_c', $taskId)
            ->update([
                'reserve_task_userid' => $actionBy,
            ]);

        return $queryTaskCstm;
    }

    public function getDashboardCS($contactMode = null, $caseInfo = null, $date = null)
    {

        $query = DB::table('cases as c');
        $query->join('cases_cstm as cc', 'cc.id_c', '=', 'c.id');
        $query->leftJoin('users as u', 'c.pickupby_id', '=', 'u.id');
        $query->leftJoin('users as us', 'c.modified_user_id', '=', 'us.id');
        $query->where('c.deleted', 0);
        $query->whereIn('c.created_by', ['1be60b16-974a-11c0-cb30-591eb0147a82', '96fa954b-8288-4bef-ac39-5338a5b5c9c0']);
        if ($contactMode !== null) {
            $query->where('cc.contact_mode_c', $contactMode);
        } else {
            $query->whereIn('cc.contact_mode_c', ["Open Portal", "Email"]);
        }

        if ($caseInfo === 'info') {
            $query->where('cc.case_info_c', '=', 'info_notcompleted');
        }

        if ($date !== null) { //use in view dashboard_cs
            $query->where(DB::raw("STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d')"), $date);
        } else { // use in view crmdashboard/dashboard_cs -> show for all dates 
            $query->where('c.state', '=', 'Open');
            $query->where('c.status', '=', 'Open_Pending Input');
        }
        $query->select(
            'c.case_number AS case_number',
            'c.name AS case_name',
            'u.first_name AS csName',
            'us.first_name AS csNama',
            'c.pickup_datetime AS case_pickupdate',
            'c.date_entered AS created_date',
            'c.date_modified AS modified_date',
            'cc.request_type_c AS request_type',
            'cc.incident_service_type_c AS type_of_incident',
            'c.state AS case_state',
            'c.status AS case_status',
            'c.sla_flag AS cs_sla_flag',
            'cc.contact_mode_c AS contact_mode',
            'cc.case_info_c AS case_info',
            'c.id AS case_id'
        );
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') as cs_start_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00') as cs_due_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') as cs_actual_start_datetime"));
        $query->addSelect(DB::raw("CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00') as cs_completed_datetime"));
        $query->addSelect(DB::raw("CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')) END AS cs_available_duration"));
        $query->addSelect(DB::raw("CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00')) END AS cs_actual_duration"));

        $data = $query->get();
        return $data;
    }

    public function getDashboardPendingResponse()
    {

        $processDate = Carbon::yesterday()->toDateString();
        // Log::info(self::class . ' Hari ini : ' . $processDate);

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.id     AS case_id,
                 c.case_number     AS case_number,
                 c.name            AS case_name,
                 u.first_name     AS csName,
                 us.first_name AS csNama,
                 c.pickup_datetime AS case_pickupdate,
                 CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS created_date,
                 cc.request_type_c                            AS request_type,
                 cc.incident_service_type_c                   AS type_of_incident,
                 c.state                                     AS case_state,
                 c.status                                    AS case_status,
                 c.sla_flag                                  AS cs_sla_flag,
                 cmode.value_name                             AS contact_mode,
                 cc.case_info_c                              AS case_info,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_start_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')   AS cs_due_datetime,
                 CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00') AS cs_actual_start_datetime,
                 CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00') AS cs_completed_datetime,
                 NOW(),
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_sla_duedate,'+00:00','+08:00')) END)   AS cs_available_duration,
                 (CASE WHEN (c.cntc_mode_sla_startdate IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(c.cntc_mode_sla_startdate,'+00:00','+08:00'),CONVERT_TZ(c.cntc_mode_executiondate,'+00:00','+08:00')) END) AS cs_actual_duration 
                FROM (cases c
                JOIN cases_cstm cc ON (c.id = cc.id_c)
                LEFT JOIN cstm_list_app cmode ON (cc.contact_mode_c = cmode.value_code)
                LEFT JOIN users u ON (u.`id`= c.`pickupby_id`)
                LEFT JOIN users us ON (us.`id`= c.`modified_user_id`))
                WHERE( (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') <= ?)
                AND ( cmode.`value_name` IN ('Open Portal','Email'))
                AND (c.`created_by` IN ('1be60b16-974a-11c0-cb30-591eb0147a82','96fa954b-8288-4bef-ac39-5338a5b5c9c0')) 
                AND (c.cntc_mode_executiondate IS NOT NULL)    
                AND (c.`status` = 'Open_Pending Input') 
                AND (cc.`request_type_c` = '')
                AND (cc.`case_info_c` = '')                  
                AND (`c`.`deleted` = 0))
                ORDER BY cs_completed_datetime ASC",
            array($processDate)
        );

        return $results;
    }

    public function getDashboardCRMITCoord($status = null)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('tasks_cstm.sla_task_flag_c', 2);
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->where('tasks.assigned_user_id', 'bd305f97-902e-e186-f506-58997eeecc12');
        if ($status !== null) {
            $query->where('tasks.status', $status);
        } else {
            $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        }
        $query->whereIn('tasks.name', ['Initial Task', 'Initial Task - IT Incident', 'Initial Task : IT Incident']);
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as flag',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks_cstm.assign_group_c as assignedGroup',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases.name as caseName',
            'cases.status as caseStatus',
            'cases.date_entered as caseEntered',
            'cases.date_modified as caseModified'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITServiceCoord()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'service_it');
        $query->where('tasks.assigned_user_id', 'bd305f97-902e-e186-f506-58997eeecc12');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->whereIn('tasks.name', ['Initial Task', 'Initial Task : IT Request']);
        $query->select('tasks_cstm.task_number_c as taskno', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as flag', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks_cstm.assign_group_c as assignedGroup', 'cases_cstm.sub_category_desc_c as subCategory', 'cases.name as caseName');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMOthersAssignedToITSpec($GroupID)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->leftJoin('users', 'users.id', '=', 'tasks.task_pic');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', '<>', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->where('tasks.assigned_user_id', $GroupID);
        $query->select('tasks_cstm.task_number_c as taskno', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as taskFlag', 'users.first_name as IndividualAssigned', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks.task_severity as taskSeverity', 'tasks_cstm.assign_group_c as assignedGroupSpec', 'tasks.case_redmine_number as redmine', 'cases_cstm.sub_category_desc_c as subCategory', 'cases.name as caseName', 'tasks.assigned_user_id as group_id');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpec()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('tasks_cstm.sla_task_flag_c', 3);
        $query->where('tasks.assigned_user_id', 'd3bf216c-122b-4ce5-9410-899317762b60');
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as taskFlag',
            'tasks.date_start as datestart',
            'tasks_cstm.sla_start_4hr_c as actualstart',
            'tasks_cstm.sla_stop_4hr_c as actualstop',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks.task_severity as taskSeverity',
            'tasks_cstm.assign_group_c as assignedGroupSpec',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases_cstm.sub_category_2_desc_c as subCategory2',
            'cases.name as caseName'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpecSeverity($GroupID = null, $status = null, $flag = null)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->leftJoin('users', 'users.id', '=', 'tasks.task_pic');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->where('tasks.assigned_user_id', $GroupID);
        if ($status !== null) {
            $query->where('tasks.status', $status);
        } else {
            $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        }
        if ($flag !== null) {
            $query->where('tasks.task_severity', $flag);
        } else {
            $query->whereIn('tasks.task_severity', ['s1', 's2', 's3']);
        }
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as taskFlag',
            'users.first_name as IndividualAssigned',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.sla_start_4hr_c as actualstart',
            'tasks_cstm.sla_stop_4hr_c as actualstop',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks.task_severity as taskSeverity',
            'tasks_cstm.assign_group_c as assignedGroupSpec',
            'tasks.case_redmine_number as redmine',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases.name as caseName',
            'tasks.assigned_user_id as group_id',
            'cases.status as caseStatus',
            'cases.date_entered as caseEntered',
            'cases.date_modified as caseModified'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpecRedmine()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->whereIn('tasks.assigned_user_id', ['d3bf216c-122b-4ce5-9410-899317762b60', '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e', '86dde498-da2d-4208-a7dd-786980e6827a', '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4', 'bb59c521-4a4d-a001-a8aa-58d09f694ae7', '5dac7b92-18d0-4beb-b600-413957aa4c26', '3d74f2af-f673-4263-afc0-d3f89aadf0ef', '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad']);
        $query->select('tasks_cstm.task_number_c as taskno', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as taskFlag', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks.task_severity as taskSeverity', 'tasks_cstm.assign_group_c as assignedGroupSpec', 'tasks.case_redmine_number as redmine', 'cases_cstm.sub_category_desc_c as subCategory', 'cases.name as caseName');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpecApproval()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2020-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->where('tasks.name', '<>', 'Assigned to Case Owner');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement', 'Reassigned']);
        $query->whereIn('tasks.assigned_user_id', ['d3bf216c-122b-4ce5-9410-899317762b60', '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e', '86dde498-da2d-4208-a7dd-786980e6827a', '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4', 'bb59c521-4a4d-a001-a8aa-58d09f694ae7', '5dac7b92-18d0-4beb-b600-413957aa4c26', '3d74f2af-f673-4263-afc0-d3f89aadf0ef', '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad']);
        $query->select('tasks_cstm.task_duration_c AS itapprover_duration', 'tasks_cstm.task_number_c as taskno', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as taskFlag', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks.task_severity as taskSeverity', 'tasks_cstm.assign_group_c as assignedGroupSpec', 'tasks.case_redmine_number as redmine', 'cases_cstm.sub_category_desc_c as subCategory', 'cases.name as caseName', 'tasks_cstm.sla_start_approver_c as sla_start');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpecModule($SubCatID)
    {
        $FLModule = array("10713_15505", "10712_15005");
        $QTModule = array("10712_15004", "10713_15504");
        $SMModule = array("10712_15002", "10713_15502");
        $DPModule = array("10712_15003", "10713_15503");
        $CTModule = array("10712_15006", "10713_15506");
        $PPModule = array("10712_15010", "10713_15510");
        $PMModule = array("10712_15008");

        if ($SubCatID == '10712_15005') {
            $resultSubCatID = $FLModule;
        }
        if ($SubCatID == '10712_15004') {
            $resultSubCatID = $QTModule;
        }
        if ($SubCatID == '10712_15002') {
            $resultSubCatID = $SMModule;
        }
        if ($SubCatID == '10712_15003') {
            $resultSubCatID = $DPModule;
        }
        if ($SubCatID == '10712_15006') {
            $resultSubCatID = $CTModule;
        }
        if ($SubCatID == '10712_15008') {
            $resultSubCatID = $PMModule;
        }
        if ($SubCatID == '10712_15010') {
            $resultSubCatID = $PPModule;
        }
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->join('users', 'users.id', '=', 'tasks.assigned_user_id');
        $query->join('cstm_list_app', 'cstm_list_app.value_code', '=', 'cases_cstm.sub_category_c');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->whereIn('tasks.assigned_user_id', ['5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e', 'd3bf216c-122b-4ce5-9410-899317762b60', '5dac7b92-18d0-4beb-b600-413957aa4c26', 'bb59c521-4a4d-a001-a8aa-58d09f694ae7', '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4', '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad', '3d74f2af-f673-4263-afc0-d3f89aadf0ef', '86dde498-da2d-4208-a7dd-786980e6827a']);
        $query->whereIn('cases_cstm.sub_category_c', $resultSubCatID);
        $query->select('cases_cstm.incident_service_type_c as incidentType', 'tasks_cstm.task_number_c as taskno', 'cases_cstm.sub_category_c as subCatID', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as taskFlag', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks.task_severity as taskSeverity', 'tasks_cstm.assign_group_c as assignedGroupSpec', 'tasks.case_redmine_number as redmine', 'cases_cstm.sub_category_desc_c as subCategory', 'cases.name as caseName', 'tasks.assigned_user_id as group_id');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }

    public function getDashboardCRMITSpecModuleOthers()
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->join('cstm_list_app', 'cstm_list_app.value_code', '=', 'cases_cstm.sub_category_c');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->whereIn('tasks.assigned_user_id', ['5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e', 'd3bf216c-122b-4ce5-9410-899317762b60', '5dac7b92-18d0-4beb-b600-413957aa4c26', 'bb59c521-4a4d-a001-a8aa-58d09f694ae7', '825b06f5-d0b6-e6da-0d56-58d0a0bca1e4', '2d567f7a-1cf1-9892-ac97-58d0a0eac2ad', '3d74f2af-f673-4263-afc0-d3f89aadf0ef', '86dde498-da2d-4208-a7dd-786980e6827a']);
        $query->whereIn('cases_cstm.sub_category_c', [
            '10712_15000', '10713_15500',
            '10712_15001', '10713_15501',
            '10712_15007', '10713_15507',
            '10712_15009', '10713_15509',
            '10712_15011', '10713_15511',
            '10712_15012', '10713_15512',
            '10712_15013', '10713_15513',
            '10712_15014', '10713_15514',
            '10712_15015', '10713_15515',
            '10712_15016', '10713_15516',
            '10712_15017', '10713_15517',
            '10712_15018', '10713_15518',
            '10712_15019', '10713_15519',
            '10712_15020', '10713_15520',
            '10712_15021', '10713_15521',
            '10712_15022', '10713_15522',
            '10712_15023', '10713_15523',
            '10712_15025', '10713_15525',
            '10712_15026', '10713_15526',
            '10712_15027', '10713_15527',
            '10712_15028', '10713_15528',
            '10712_15029', '10713_15529',
            '10712_15030', '10713_15530',
            '10712_15031', '10713_15531',
            '10712_15032', '10713_15532',
            '10712_15033', '10713_15533',
            '10712_15034', '10713_15534',
            '10712_15035', '10713_15535',
            '10712_15036', '10713_15536',
            '10712_15037', '10713_15537',
            '10712_15038', '10713_15538',
            '10712_15039', '10713_15539',
            '10712_15826', '10713_15827',
            '10712_15828', '10713_15829'
        ]);
        $query->select('cases_cstm.incident_service_type_c as incidentType', 'tasks_cstm.task_number_c as taskno', 'cases.case_number as caseNumber', 'tasks.status as taskStatus', 'tasks.name as taskname', 'tasks_cstm.sla_task_flag_c as taskFlag', 'cstm_list_app.value_name as subCategory', 'tasks.date_start as datestart', 'tasks.date_due as datedue', 'tasks_cstm.acknowledge_time_c as acknowledgetime', 'tasks_cstm.task_duration_c as taskduration', 'tasks.task_severity as taskSeverity', 'tasks_cstm.assign_group_c as assignedGroupSpec', 'tasks.case_redmine_number as redmine', 'cases_cstm.sub_category_desc_c as subCategori', 'cases.name as caseName', 'tasks.assigned_user_id as group_id');
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        // dd($data);
        return $data;
    }

    public function getDashboardTopEnqSubCategory()
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  clist.value_name AS subCategory ,clistt.value_name AS subCategory2,clist.value_code as subCatCode ,clistt.value_code as subCatCode2,COUNT(c.case_number) AS totalCases
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c = 'enquiry'
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                GROUP BY clistt.value_name,clist.value_name,clist.value_code,clistt.value_code
                ORDER BY COUNT(clistt.value_name) DESC , clist.value_name ASC
                LIMIT 10",
            array($processDate)
        );

        return $results;
    }

    public function getDetailsTopEnqSubCategory($subCat, $subCat2)
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.`case_number` as caseNum,c.`date_entered` AS caseCreated,cc.`contact_mode_c` as contactMode,cc.`request_type_c` as requestType,clist.value_name AS subCategory ,clistt.value_name AS subCategory2
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c = 'enquiry'
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                AND clist.`value_code` = ?
                AND clistt.`value_code` =?
                ORDER BY c.`date_entered` DESC",
            array($processDate, $subCat, $subCat2)
        );

        return $results;
    }

    public function getDashboardTopIncServSubCategory()
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  clist.value_name AS subCategory ,clistt.value_name AS subCategory2,clist.value_code as subCatCode ,clistt.value_code as subCatCode2 ,COUNT(c.case_number) AS totalCases
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c IN ('service', 'incident')
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                GROUP BY clistt.value_name,clist.value_name,clist.value_code,clistt.value_code
                ORDER BY COUNT(clistt.value_name) DESC , clist.value_name ASC
                LIMIT 10",
            array($processDate)
        );

        return $results;
    }

    public function getDetailsTopIncServSubCategory($subCat, $subCat2)
    {
        $processDate = Carbon::now()->toDateString();

        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT  c.`case_number` as caseNum,c.`date_entered` AS caseCreated,cc.`contact_mode_c` as contactMode,cc.`request_type_c` as requestType,clist.value_name AS subCategory ,clistt.value_name AS subCategory2
                FROM cases c, cases_cstm cc, cstm_list_app clist, cstm_list_app clistt
                WHERE c.id=cc.id_c
                AND clist.value_code=cc.sub_category_c
                AND ((clistt.value_code=cc.sub_category_2_c)
                AND (clistt.type_code = 'cdc_sub_category_2_list'))
                AND c.status <> 'Open_Pending Input'
                AND cc.request_type_c IN ('service', 'incident')
                AND cc.category_c NOT IN (10715,10719,10720, 10722, 10721)
                AND c.deleted = 0
                AND STR_TO_DATE(CONVERT_TZ(c.date_entered,'+00:00','+08:00'),'%Y-%m-%d') = ?
                AND clist.`value_code` = ?
                AND clistt.`value_code` =?
                ORDER BY c.`date_entered` DESC",
            array($processDate, $subCat, $subCat2)
        );

        return $results;
    }

    public function getDashboardStatisticCaseAssignedbyModule($GroupID)
    {
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups_users.securitygroup_id', '=', 'securitygroups.id');
        $query->join('users', 'users.id', '=', 'securitygroups_users.user_id');
        $query->where('users.deleted', 0);
        $query->where('users.status', '=', 'Active');
        $query->where('securitygroups_users.deleted', 0);
        $query->where('users.id', $GroupID);
        $query->select('securitygroups_users.securitygroup_id as securityID');
        $data = $query->get();
        return $data;
    }

    public function getDashboardTotalStatisticCaseAssignedbyModule($GroupID)
    {
        $UsersMiddleware = array("aminah", "iqbalfikri", "mohdshamsul", "mohdyusri", "nazifah", "luqmanulhakim");
        $UsersProdSupport = array("aema", "akmalsaufi", "azwandy", "fauziahm", "hafizi", "norhasfarine", "safinah", "shahril", "miormuhamad","asmira","nurulatiqah");

        $UsersMiddlewareStr = implode("', '", $UsersMiddleware);
        $UsersProdSupportStr = implode("', '", $UsersProdSupport);
        if ($GroupID == '2d03a7e9-b033-4573-804d-5c4f8f64fa0c') { // security group ID middleware
            $userResultStr = $UsersMiddlewareStr;
        }
        if ($GroupID == 'c6f9904e-4351-4b88-91e9-86453ae3b158') { // security group ID prod Support
            $userResultStr = $UsersProdSupportStr;
        }

        $results = DB::connection('mysql_crm')->select(
            "SELECT
                u.first_name AS fullname,u.id as userId,
                COUNT(CASE WHEN (DAY(t.date_entered) = DAY(NOW())) AND (MONTH(t.date_entered) = MONTH(NOW())) AND (YEAR(t.date_entered) = YEAR(NOW())) THEN 1 ELSE NULL END) AS totalCaseResolvedDaily,
                COUNT(CASE WHEN (DAY(t.date_entered) = DAY(NOW() - INTERVAL 1 DAY)) AND (MONTH(t.date_entered) = MONTH(NOW() - INTERVAL 1 DAY)) AND (YEAR(t.date_entered) = YEAR(NOW())) THEN 1 ELSE NULL END) AS yesterday,
		COUNT(CASE WHEN (MONTH(t.date_entered) = MONTH(NOW())) and (YEAR(t.date_entered) = YEAR(NOW()))  THEN 1 ELSE NULL END) AS totalCaseResolvedMonthly,
		COUNT(CASE WHEN (YEAR(t.date_entered) = YEAR(NOW()))  THEN 1 ELSE NULL END) AS totalCaseResolvedYearly,
                COUNT(CASE WHEN (YEAR(t.date_entered) = YEAR(NOW() - INTERVAL 1 YEAR))  THEN 1 ELSE NULL END) AS totalCaseResolvedLastYear
                FROM
                cases c,
                cases_cstm cc,
                tasks t,
                tasks_cstm tc,
                users u 
                WHERE c.id = cc.id_c 
                AND t.parent_id = c.id 
                AND t.parent_type = 'Cases' 
                AND t.id = tc.id_c 
                AND t.created_by = u.id 
                AND t.name  IN ('Assigned to Case Owner','Task Rejected') 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		WHERE   tm.value_code = cc.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		WHERE   tm.value_code = cc.sub_category_2_c )
                AND YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) >= YEAR(NOW() - INTERVAL 1 YEAR)
                AND u.user_name IN ('$userResultStr')
                GROUP BY u.first_name,u.id
                ORDER BY totalCaseResolvedYearly DESC"
        );
        return $results;
    }

    public function getDetailsStatisticModuleResolved($userId)
    {

        $results = DB::connection('mysql_crm')->select(
            "SELECT
                u.first_name AS fullname, cc.`sub_category_desc_c` as moduleName, 
                COUNT(CASE WHEN (DAY(t.date_entered) = DAY(NOW())) AND (MONTH(t.date_entered) = MONTH(NOW())) AND (YEAR(t.date_entered) = YEAR(NOW())) THEN 1 ELSE NULL END) AS totalCaseResolvedDaily,
                COUNT(CASE WHEN (DAY(t.date_entered) = DAY(NOW() - INTERVAL 1 DAY)) AND (MONTH(t.date_entered) = MONTH(NOW() - INTERVAL 1 DAY)) AND (YEAR(t.date_entered) = YEAR(NOW())) THEN 1 ELSE NULL END) AS yesterday,
		COUNT(CASE WHEN (MONTH(t.date_entered) = MONTH(NOW())) and (YEAR(t.date_entered) = YEAR(NOW()))  THEN 1 ELSE NULL END) AS totalCaseResolvedMonthly,
		COUNT(CASE WHEN (YEAR(t.date_entered) = YEAR(NOW()))  THEN 1 ELSE NULL END) AS totalCaseResolvedYearly,
                COUNT(CASE WHEN (YEAR(t.date_entered) = YEAR(NOW() - INTERVAL 1 YEAR))  THEN 1 ELSE NULL END) AS totalCaseResolvedLastYear
                FROM
                cases c,
                cases_cstm cc,
                tasks t,
                tasks_cstm tc,
                users u 
                WHERE c.id = cc.id_c 
                AND t.parent_id = c.id 
                AND t.parent_type = 'Cases' 
                AND t.id = tc.id_c 
                AND t.created_by = u.id 
                AND t.name  IN ('Assigned to Case Owner','Task Rejected') 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		WHERE   tm.value_code = cc.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		WHERE   tm.value_code = cc.sub_category_2_c )
                AND YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) >= YEAR(NOW() - INTERVAL 1 YEAR)
                AND u.id = '$userId'
                GROUP BY cc.`sub_category_desc_c`"
        );
        return $results;
    }

    public function getDetailsStatisticModuleResolvedByDate($userId, $module, $date)
    {

        $results = DB::connection('mysql_crm')->select(
            " SELECT
                c.case_number, c.name, u.first_name,
                CONVERT_TZ(t.date_entered, '+00:00','+08:00') task_date_entered, cc.sub_category_desc_c,cc.sub_category_c
                FROM
                cases c,
                cases_cstm cc,
                tasks t,
                tasks_cstm tc,
                users u 
                WHERE c.id = cc.id_c 
                AND t.parent_id = c.id 
                AND t.parent_type = 'Cases' 
                AND t.id = tc.id_c 
                AND t.created_by = u.id 
                AND t.name  IN ('Assigned to Case Owner','Task Rejected') 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('PTJ Onsite')) AS tm 
		WHERE   tm.value_code = cc.sub_category_c ) 
                AND  NOT EXISTS (SELECT  NULL FROM    (SELECT value_code,value_name FROM cstm_list_app WHERE value_name  IN ('Onsite Support')) AS tm 
		WHERE   tm.value_code = cc.sub_category_2_c )
                AND YEAR(DATE(CONVERT_TZ(t.date_entered, '+00:00','+08:00'))) >= YEAR(NOW() - INTERVAL 1 YEAR)
                AND u.id = '$userId' 
                and cc.sub_category_desc_c = '$module'
                and ($date) "
        );
        return $results;
    }

    public function getStatisticCRMITSpecModule()
    {
        //Group IT Specialist(Production Support)
        //Group IT Coordinator
        //Group Middleware
        $results = DB::connection('mysql_crm')->select(
            "SELECT cstm_list_app.`value_name` AS ModuleName,
                    COUNT(CASE WHEN (users.`first_name` = 'Group IT Specialist(Developer)') THEN 1 ELSE NULL END) AS totalCaseAssignedtoDeveloper,
                    COUNT(CASE WHEN (users.`first_name` = 'Group IT Specialist(Production Support)') THEN 1 ELSE NULL END) AS totalCaseAssignedtoGroupProdSupport,
                    COUNT(CASE WHEN (users.`first_name` = 'Group Middleware') THEN 1 ELSE NULL END) AS totalCaseAssignedtoMiddleware
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN cstm_list_app ON cstm_list_app.value_code=cases_cstm.sub_category_c 
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`    
                    JOIN users ON users.`id`=tasks.`assigned_user_id`
                    WHERE tasks.`status` IN ('Pending Acknowledgement', 'Acknowledge')
                    AND tasks.`assigned_user_id` IN ('d3bf216c-122b-4ce5-9410-899317762b60', '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e', '5dac7b92-18d0-4beb-b600-413957aa4c26')
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    GROUP BY cstm_list_app.`value_name`"
        );
        return $results;
    }

    public function getSpecialistByModuleChart()
    {

        $results = DB::connection('mysql_crm')->select(
            "SELECT cstm_list_app.`value_name` AS ModuleName,
                    COUNT(CASE WHEN (users.`first_name` = 'Group IT Specialist(Developer)') THEN 1 ELSE NULL END) AS totalCaseAssignedtoDeveloper,
                    COUNT(CASE WHEN (users.`first_name` = 'Group IT Specialist(Production Support)') THEN 1 ELSE NULL END) AS totalCaseAssignedtoGroupProdSupport,
                    COUNT(CASE WHEN (users.`first_name` = 'Group Middleware') THEN 1 ELSE NULL END) AS totalCaseAssignedtoMiddleware
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN cstm_list_app ON cstm_list_app.value_code=cases_cstm.sub_category_c 
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`    
                    JOIN users ON users.`id`=tasks.`assigned_user_id`
                    WHERE tasks.`status` IN ('Pending Acknowledgement', 'Acknowledge')
                    AND tasks.`assigned_user_id` IN ('d3bf216c-122b-4ce5-9410-899317762b60', '5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e','5dac7b92-18d0-4beb-b600-413957aa4c26')
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    GROUP BY cstm_list_app.`value_name`"
        );
        return $results;
    }

    public function getSpecialistUsers()
    {
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT a.`first_name` as firstname, a.`id` as userid
                    FROM users a, securitygroups_users b, securitygroups c
                    WHERE a.`id` = b.`user_id` 
                    AND b.`securitygroup_id` = c.`id`
                    AND c.`id` IN ('c6f9904e-4351-4b88-91e9-86453ae3b158','2d03a7e9-b033-4573-804d-5c4f8f64fa0c')
                    AND a.`first_name` not in ('Group IT Specialist(Developer)','Group IT Specialist(Production Support)','Group Middleware')
                    AND a.deleted = 0
                    AND b.`deleted` = 0
                    AND c.`deleted` = 0
                    order by firstname asc"
        );
        return $results;
    }

    public static function updateTaskPicSpecialist($taskId, $userId, $userLogin)
    {
        $query = DB::table('tasks')
            ->where('id', $taskId)
            ->update([
                'task_pic' => $userId,
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $userLogin,
            ]);

        return $query;
    }

    public function getMiddlewareUsers()
    {
        $results = DB::connection('mysql_crm')->select(
            "SELECT distinct c.`id` AS userid,c.`first_name` AS fullname FROM securitygroups a,
                securitygroups_users b,
                users c
                WHERE a.`id` = b.`securitygroup_id`
                AND b.`user_id` = c.`id`
                AND a.`id` = '2d03a7e9-b033-4573-804d-5c4f8f64fa0c'
                AND c.`deleted` = 0 and c.status = 'Active'
               -- AND b.deleted = 0
                AND c.`id` NOT IN ('5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e')
                AND c.department = 'Middleware'
                ORDER BY fullname"
        );
        return $results;
    }

    public static function updateCaseResolution($caseId, $taskId, $newResolution, $userLogin)
    {
        $cases = DB::table('cases')
            ->where('id', $caseId)
            ->update([
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $userLogin,
                'resolution' => $newResolution
            ]);

        $tasks = DB::table('tasks')
            ->where('id', $taskId)
            ->update([
                'date_modified' => Carbon::now()->subHour(8),
                'modified_user_id' => $userLogin
            ]);

        $tasksCstm = DB::table('tasks_cstm')
            ->where('id_c', $taskId)
            ->update([
                'resolution_c' => $newResolution
            ]);

        return $cases . ' ' . $tasks . ' ' . $tasksCstm;
    }

    public function getDashboardCRMITIncidentNormalPendingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status = 'Open_Assigned'
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentApproverPendingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status = 'Open_Assigned'
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    and tasks_cstm.`sla_task_flag_c` = 4
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );

        return $results;
    }

    public function getDashboardCRMITIncidentNormalResolvedSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.sla_flag AS caseFlag,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status IN ('Pending_User_Verification','Open_Resolved')
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    AND tasks.`status` <> 'Completed'
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentApproverResolvedSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.sla_flag AS caseFlag,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status IN ('Pending_User_Verification','Open_Resolved')
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND tasks.`status` <> 'Completed'
                    AND tasks_cstm.`sla_task_flag_c` = 4 
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentNormalClosedSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.sla_flag AS caseFlag,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Closed'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentApproverClosedSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.sla_flag AS caseFlag,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Closed'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND tasks_cstm.`sla_task_flag_c` = 4 
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentNormalPendingAgeingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.`date_entered`,cases.`date_modified`,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
		, TIMESTAMPDIFF(DAY,STR_TO_DATE(CONVERT_TZ(`cases`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d'),CURDATE()) as ageing
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status = 'Open_Assigned'
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentApproverPendingAgeingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.`date_entered`,cases.`date_modified`,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
		, TIMESTAMPDIFF(DAY,STR_TO_DATE(CONVERT_TZ(`cases`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d'),CURDATE()) as ageing
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status = 'Open_Assigned'
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    AND tasks_cstm.`sla_task_flag_c` = 4
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentNormalResolveAgeingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.`date_entered`,cases.`date_modified`,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
		, TIMESTAMPDIFF(DAY,STR_TO_DATE(CONVERT_TZ(`cases`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d'),CURDATE()) as ageing
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status IN ('Pending_User_Verification','Open_Resolved')
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getDashboardCRMITIncidentApproverResolveAgeingSummary()
    {
        $year = Carbon::now()->format('Y');
        $month = Carbon::now()->format('m');
        $results = DB::connection('mysql_crm')->select(
            "SELECT DISTINCT cases.`date_entered`,cases.`date_modified`,cases.state AS caseState, cases.status AS caseStatus,a.`account_type` AS accType,cases.`case_number` AS caseNum, tasks.name AS taskName,tasks.`assigned_user_id` AS assignGroupID,tasks.`status` AS taskStatus,tasks_cstm.`acknowledge_time_c` AS acknowledgetime, 
		    tasks.`task_severity` AS taskSeverity, tasks_cstm.`sla_task_flag_c` AS tasksFlag, cases.`status` AS casesStatus, tasks.date_entered AS tasksCreated, tasks_cstm.task_number_c AS tasksNum
		, TIMESTAMPDIFF(DAY,STR_TO_DATE(CONVERT_TZ(`cases`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d'),CURDATE()) as ageing
                    FROM cases 
                    JOIN cases_cstm ON cases.id=cases_cstm.id_c
                    JOIN tasks ON tasks.`parent_id`=cases.`id`
                    JOIN tasks_cstm ON tasks_cstm.`id_c`=tasks.`id`  
                    JOIN accounts a ON a.`id` = cases.`account_id`
                    JOIN contacts ctc ON ctc.`id` = cases_cstm.`contact_id_c`
                    WHERE cases.`state` = 'Open'
                    AND cases.status IN ('Pending_User_Verification','Open_Resolved')
                    AND cases_cstm.`request_type_c` = 'incident'
                    AND cases_cstm.`incident_service_type_c` = 'incident_it'
                    AND YEAR(cases.`date_entered`) = $year
                    AND MONTH(cases.`date_entered`) = $month
                    and tasks.`status` <> 'Completed'
                    AND tasks_cstm.`sla_task_flag_c` = 4
                    AND cases.`deleted` = 0
                    AND tasks.`deleted` = 0
                    ORDER BY tasksCreated DESC"
        );
        return $results;
    }

    public function getSlaCs($actual = null, $dateStart = null, $dateEnd = null)
    {

        $query = "SELECT DISTINCT c.`is_sent`,`c`.`case_number` AS `case_number`,
                        CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `created_date`,
                        CONVERT_TZ(`c`.`date_modified`,'+00:00','+08:00') AS `date_modified`,
                        cc.`request_type_c` AS request_type,
                        `cc`.`incident_service_type_c` AS `type_of_incident`,
                        `c`.`status` AS `case_status`,
                        `c`.`sla_flag` AS `cs_sla_flag`,
                        `cc`.`contact_mode_c` AS `contact_mode`,
                        us.`first_name` AS created_by,
                        CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00') AS `cs_start_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_sla_duedate`,'+00:00','+08:00')   AS `cs_due_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00') AS `cs_actual_start_datetime`,
                        CONVERT_TZ(`c`.`cntc_mode_executiondate`,'+00:00','+08:00') AS `cs_completed_datetime`,
                        (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_sla_duedate`,'+00:00','+08:00')) END)   AS `cs_available_duration`,
                        (CASE WHEN (`c`.`cntc_mode_sla_startdate` IS NOT NULL) THEN TIMESTAMPDIFF(SECOND,CONVERT_TZ(`c`.`cntc_mode_sla_startdate`,'+00:00','+08:00'),CONVERT_TZ(`c`.`cntc_mode_executiondate`,'+00:00','+08:00')) END) AS `cs_actual_duration`,
                        `c`.`is_sent` AS `is_sent`,
                        'CS' AS title
                        FROM ((`cases` `c`
                                JOIN `cases_cstm` `cc`
                                  ON ((`c`.`id` = `cc`.`id_c`)))
                               LEFT JOIN `cstm_list_app` `cmode`
                                 ON (((`cc`.`contact_mode_c` = `cmode`.`value_code`)
                                      AND (TRIM(`cmode`.`value_code`) <> '')
                                      AND (`cmode`.`type_code` = 'cdc_contact_mode_list')))
                               LEFT JOIN users us
				 ON (us.`id`=c.`created_by`))
                            WHERE(
                            (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN '$dateStart' AND '$dateEnd')
                            AND  (`cc`.`category_c` <> 10715)
                           AND c.`is_sent` = 1
                            AND (`c`.`deleted` = 0))";

        if ($actual != null) {
            $query = $query . "
                    AND cntc_mode_executiondate IS NULL";
        }

        $results = DB::connection('mysql_crm')->select($query);

        return $results;
    }

    public function getSlaItCoord($actual = null, $dateStart = null, $dateEnd = null)
    {

        $query = "
            SELECT 
                DISTINCT `c`.`case_number` AS `case_number`,
                CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itcoord_case_created`,
                `c`.`name` AS `itcoord_name`,
                `itcoord_tc`.`task_number_c` AS `itcoord_task_number`,
                `itcoord_tc`.`sla_task_flag_c` AS `itcoord_sla_flag`,
                CONVERT_TZ(`itcoord_t`.`date_start`,'+00:00','+08:00') AS `itcoord_start_datetime`,
                CONVERT_TZ(`itcoord_t`.`date_due`,'+00:00','+08:00') AS `itcoord_due_datetime`,
                CONVERT_TZ(`itcoord_tc`.`sla_start_15min_c`,'+00:00','+08:00') AS `itcoord_actual_start_datetime`,
                CONVERT_TZ(`itcoord_tc`.`sla_stop_15min_c`,'+00:00','+08:00') AS `itcoord_completed_datetime`,
                TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itcoord_t`.`date_start`,'+00:00','+08:00'),CONVERT_TZ(`itcoord_t`.`date_due`,'+00:00','+08:00')) AS `itcoord_available_duration`,
                TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itcoord_tc`.`sla_start_15min_c`,'+00:00','+08:00'),CONVERT_TZ(`itcoord_tc`.`sla_stop_15min_c`,'+00:00','+08:00')) AS `itcoord_actual_duration`,
                `itcoord_tc`.`is_sent` AS `is_sent`,
                'IT Coordinator' AS title
            FROM ((((`cases` `c` JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
            LEFT JOIN `tasks` `itcoord_t` ON((`c`.`id` = `itcoord_t`.`parent_id`))) 
            LEFT JOIN `tasks_cstm` `itcoord_tc` ON(((`itcoord_t`.`id` = `itcoord_tc`.`id_c`) 
                AND (`itcoord_tc`.`sla_task_flag_c` = '2')))) 
            LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                AND (TRIM(`subcat`.`value_code`) <> '') 
                AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
            WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
            AND (`cc`.`request_type_c` = 'incident') 
            AND (`c`.`deleted` = 0) 
            AND (`itcoord_t`.`deleted` = 0)  
            AND (`itcoord_tc`.`task_number_c` IS NOT NULL) 
            AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN '$dateStart' AND '$dateEnd')
            AND (`itcoord_tc`.`is_sent` = 1)
            AND (TRIM(`subcat`.`value_code`) <> ''))";

        if ($actual != null) {
            $query = $query . "
            AND sla_stop_15min_c IS NULL ";
        }

        $results = DB::connection('mysql_crm')->select($query);

        return $results;
    }

    public function getSlaItSpec($actual = null, $dateStart = null, $dateEnd = null)
    {

        $query = "
            SELECT DISTINCT c.`state`, c.`status`,`c`.`case_number` AS `case_number`,
                CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itspec_case_created`,
                `c`.`name` AS `itspec_name`,
                `itspec_tc`.`task_number_c` AS `itspec_task_number`,
                `itspec_tc`.`sla_task_flag_c` AS `itspec_sla_flag`,
                CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00') AS `itspec_start_datetime`,
                CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00') AS `itspec_due_datetime`,
                CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00') AS `itspec_actual_start_datetime`,
                CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00') AS `itspec_completed_datetime`,
                TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00'),CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00')) AS `itspec_available_duration`,
                TIMESTAMPDIFF(SECOND,CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00'),CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00')) AS `itspec_actual_duration`, 
                itspec_tc.is_sent AS is_sent
            FROM ((((`cases` `c` 
            JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
            LEFT JOIN `tasks` `itspec_t` ON((`c`.`id` = `itspec_t`.`parent_id`))) 
            LEFT JOIN `tasks_cstm` `itspec_tc` ON(((`itspec_t`.`id` = `itspec_tc`.`id_c`) 
                AND (`itspec_tc`.`sla_task_flag_c` = '3')))) 
            LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                AND (TRIM(`subcat`.`value_code`) <> '') 
                AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
            WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
            AND (`cc`.`request_type_c` = 'incident')
            AND (itspec_t.`name` IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)'))
            AND (`c`.`deleted` = 0)
            AND (`itspec_t`.`deleted` = 0) 
            AND (`c`.`state` = 'Closed') 
            AND (`itspec_t`.`status` = 'Completed') 
            AND (`c`.`date_entered` >= '2019-09-01')
            AND ( itspec_t.`assigned_user_id` <> '15c7bd74-12f3-311b-9e8f-5a810702a1a5')
            AND (`itspec_tc`.`task_number_c` IS NOT NULL) 
            AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN '$dateStart' AND '$dateEnd')
            AND (itspec_tc.is_sent = 1)  
            AND (TRIM(`subcat`.`value_code`) <> ''))";

        if ($actual != null) {
            $query = $query . "
                AND sla_stop_4hr_c IS NULL ";
        }

        $results = DB::connection('mysql_crm')->select($query);

        return $results;
    }

    public function getSlaSeverity($actual = null, $dateStart = null, $dateEnd = null)
    {

        $query = "
            SELECT 
                DISTINCT `c`.`case_number` AS `case_number`,
                CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00') AS `itseverity_case_created`,
                `c`.`name` AS `itseverity_name`,
                itspec_t.`name` AS subjectTaskName,
                `itspec_tc`.`task_number_c` AS `itseverity_task_number`,
                `itspec_tc`.`sla_task_flag_c` AS `itseverity_sla_flag`,
                CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00') AS `itseverity_start_datetime`,
                CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00') AS `itseverity_due_datetime`,
                CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00') AS `itseverity_actual_start_datetime`,
                CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00') AS `itseverity_completed_datetime`,
                DATEDIFF(STR_TO_DATE(CONVERT_TZ(`itspec_t`.`date_due`,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(`itspec_t`.`date_start`,'+00:00','+08:00'),'%Y-%m-%d')) AS `itseverity_available_duration`,
                DATEDIFF(STR_TO_DATE(CONVERT_TZ(`itspec_tc`.`sla_stop_4hr_c`,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(`itspec_tc`.`sla_start_4hr_c`,'+00:00','+08:00'),'%Y-%m-%d')) AS `itseverity_actual_duration`,
                itspec_tc.`is_sent` AS is_sent
            FROM ((((`cases` `c` 
            JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
            LEFT JOIN `tasks` `itspec_t` ON((`c`.`id` = `itspec_t`.`parent_id`))) 
            LEFT JOIN `tasks_cstm` `itspec_tc` ON(((`itspec_t`.`id` = `itspec_tc`.`id_c`) AND (`itspec_tc`.`sla_task_flag_c` IN ('s1','s2','s3'))))) 
            LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                AND (TRIM(`subcat`.`value_code`) <> '') 
                AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))) 
            WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
            AND (`cc`.`request_type_c` = 'incident') AND (`c`.`deleted` = 0)
            AND (`itspec_t`.`deleted` = 0) AND (`c`.`state` = 'Closed') 
            AND (`itspec_t`.`status` = 'Completed') 
            AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') >= '2020-01-01')
            AND ( itspec_t.`assigned_user_id` NOT IN ('15c7bd74-12f3-311b-9e8f-5a810702a1a5','5dac7b92-18d0-4beb-b600-413957aa4c26'))
            AND (`itspec_tc`.`task_number_c` IS NOT NULL) 
            AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN '$dateStart' AND '$dateEnd')
            AND itspec_tc.`is_sent` = 1  
            AND (TRIM(`subcat`.`value_code`) <> ''))";

        if ($actual != null) {
            $query = $query . "
            AND sla_stop_4hr_c IS NULL ";
        }

        $results = DB::connection('mysql_crm')->select($query);
        return $results;
    }

    public function getSlaS4($actual = null, $dateStart = null, $dateEnd = null)
    {

        $query = "
            SELECT 
                DISTINCT approver_t.`name`,approver_t.`id`,`c`.`case_number` AS `case_number`,
                CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS itapprover_case_created,
                c.name AS itapprover_name,
                `approver_tc`.`task_number_c` AS `itapprover_task_number`,
                `approver_tc`.`sla_task_flag_c` AS `itapprover_sla_flag`,
                CONVERT_TZ(`approver_t`.`date_start`,'+00:00','+08:00') AS `itapprover_start_datetime`,
                CONVERT_TZ(`approver_t`.`date_due`,'+00:00','+08:00') AS `itapprover_due_datetime`,
                CONVERT_TZ(`approver_tc`.`sla_start_approver_c`,'+00:00','+08:00') AS `itapprover_actual_start_datetime`,
                CONVERT_TZ(`approver_tc`.`sla_stop_approver_c`,'+00:00','+08:00') AS `itapprover_completed_datetime`,
                approver_tc.task_duration_c AS itapprover_duration,
                DATEDIFF(STR_TO_DATE(CONVERT_TZ(`approver_t`.`date_due`,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(`approver_t`.`date_start`,'+00:00','+08:00'),'%Y-%m-%d')) AS `itseverity_available_duration`,
                DATEDIFF(STR_TO_DATE(CONVERT_TZ(`approver_tc`.`sla_stop_approver_c`,'+00:00','+08:00'),'%Y-%m-%d'),STR_TO_DATE(CONVERT_TZ(`approver_tc`.`sla_start_approver_c`,'+00:00','+08:00'),'%Y-%m-%d')) AS `itseverity_actual_duration`,
                approver_tc.`is_sent` AS is_sent
            FROM ((((`cases` `c` 
            JOIN `cases_cstm` `cc` ON((`c`.`id` = `cc`.`id_c`))) 
            LEFT JOIN `tasks` `approver_t` ON((`c`.`id` = `approver_t`.`parent_id`))) 
            LEFT JOIN `tasks_cstm` `approver_tc` ON(((`approver_t`.`id` = `approver_tc`.`id_c`) AND (`approver_tc`.`sla_task_flag_c` = '4')))) 
            LEFT JOIN `cstm_list_app` `subcat` ON(((`cc`.`sub_category_c` = `subcat`.`value_code`) 
                AND (`subcat`.`type_code` = 'cdc_sub_category_list') 
                AND (TRIM(`subcat`.`value_code`) <> '') 
                AND (`subcat`.`value_code` NOT IN ('10712_15034','10714_15842','10713_15534'))))
            JOIN users us ON us.`id`=approver_t.`created_by`) 
            WHERE ((`cc`.`incident_service_type_c` = 'incident_it') 
            AND (`c`.`state` = 'Closed') 
            AND (`approver_t`.`status` = 'Completed') 
            AND (`cc`.`request_type_c` = 'incident')
            AND (c.`deleted` = 0)
            AND (approver_t.`deleted` = 0)
            AND (`approver_tc`.`task_number_c` IS NOT NULL) 
            AND (TRIM(`subcat`.`value_code`) <> '')) 
            AND (STR_TO_DATE(CONVERT_TZ(`c`.`date_entered`,'+00:00','+08:00'),'%Y-%m-%d') BETWEEN '$dateStart' AND '$dateEnd')
            AND (approver_tc.`is_sent` = 1) ";

        if ($actual != null) {
            $query = $query . "
            AND sla_stop_approver_c IS NULL ";
        }

        $results = DB::connection('mysql_crm')->select($query);
        return $results;
    }

    public function getDuplicateTask($dateStart, $dateEnd)
    {

        $query = "SELECT a.`id`, a.`case_number`, b.`request_type_c`, b.`incident_service_type_c`, 
                    CONVERT_TZ(a.`date_entered`,'+00:00','+08:00') AS date_entered,
                    CONVERT_TZ(a.`date_modified`,'+00:00','+08:00') AS date_modified,
                    a.`state` AS casestate, a.`status` AS casestatus,
                    COUNT(c.name) AS total
                    FROM cases a
                    LEFT JOIN `cases_cstm` b ON a.id=b.`id_c`
                    LEFT JOIN tasks c ON c.`parent_id`=a.`id`
                    LEFT JOIN tasks_cstm d ON d.`id_c`=c.`id`
                    WHERE (c.`name` LIKE '%support%' OR c.`name` LIKE '%owner%')
                    AND c.`status` NOT IN ('Completed','Approved')
                    AND (d.`sla_task_flag_c` IS NULL OR d.`sla_task_flag_c` = '')
                    AND a.`deleted` =0 AND c.`deleted` = 0
                    AND (STR_TO_DATE(CONVERT_TZ(a.date_entered,'+00:00','+08:00'),'%Y-%m-%d') 
                    BETWEEN '$dateStart' AND '$dateEnd')
                    GROUP BY id,case_number,date_entered,date_modified,casestate,casestatus
                    HAVING total > 1

                    UNION

                    SELECT a.`id`, a.`case_number`, b.`request_type_c`, b.`incident_service_type_c`, 
                    CONVERT_TZ(a.`date_entered`,'+00:00','+08:00') AS date_entered,
                    CONVERT_TZ(a.`date_modified`,'+00:00','+08:00') AS date_modified,
                    a.`state` AS casestate, a.`status` AS casestatus,
                    COUNT(c.name) AS total
                    FROM cases a
                    LEFT JOIN `cases_cstm` b ON a.id=b.`id_c`
                    LEFT JOIN tasks c ON c.`parent_id`=a.`id`
                    LEFT JOIN tasks_cstm d ON d.`id_c`=c.`id`
                    WHERE (c.`name` LIKE '%initial%')
                    AND c.`status` NOT IN ('Completed','Approved') 
                    AND a.`deleted` =0 AND c.`deleted` = 0
                    AND (STR_TO_DATE(CONVERT_TZ(a.date_entered,'+00:00','+08:00'),'%Y-%m-%d') 
                    BETWEEN '$dateStart' AND '$dateEnd')
                    GROUP BY id,case_number,date_entered,date_modified,casestate,casestatus
                    HAVING total > 1";

        $results = DB::connection('mysql_crm')->select($query);
        return $results;
    }

    public function updateTaskDeleted($taskId)
    {

        $query = "UPDATE tasks, tasks_cstm
                SET tasks.`deleted` = 1
                WHERE tasks.`id` = tasks_cstm.`id_c`
                and tasks.`id` = '$taskId'";

        $results = DB::connection('mysql_crm')->select($query);
        return $results;
    }

    public function getCaseTaskSlaDetail($caseNumber)
    {

        $query = "select t.id as task_id,
                    c.id, c.`case_number`, c.`state`, 
                    (SELECT value_name FROM cstm_list_app WHERE value_code = c.state AND type_code = 'case_state_dom' AND STATUS = 1) AS case_state, 
                    (SELECT value_name FROM cstm_list_app WHERE value_code = c.status AND type_code = 'case_status_dom' AND STATUS = 1) AS case_status,
                    c.`sla_flag`, cc.`request_type_c`, cc.`incident_service_type_c`, cc.`contact_mode_c`,c.resolution,
                    CONVERT_TZ(t.`date_entered`,'+00:00','+08:00') as task_date_entered, t.deleted,
                    CONVERT_TZ(t.`date_modified`,'+00:00','+08:00') as task_date_modified, t.`name`, tc.task_number_c, t.`status`, -- tc.`sla_task_flag_c`, 
                    CASE WHEN t.status = 'Pending Acknowledgement' AND t.task_severity IS NOT NULL THEN t.task_severity
			 ELSE tc.`sla_task_flag_c` END AS sla_task_flag_c,
                    CONVERT_TZ(t.date_start,'+00:00','+08:00') as sla_available_start, 
                    CONVERT_TZ(t.date_due,'+00:00','+08:00') as sla_available_due,
                    case 	when tc.`sla_task_flag_c` = 2 then CONVERT_TZ(tc.sla_start_15min_c,'+00:00','+08:00')
                            when tc.`sla_task_flag_c` in (3,'s1','s2','s3') then CONVERT_TZ(tc.sla_start_4hr_c,'+00:00','+08:00')
                            when tc.`sla_task_flag_c` = 4 then CONVERT_TZ(tc.sla_start_approver_c,'+00:00','+08:00')
                            else CONVERT_TZ(tc.sla_start_15min_c,'+00:00','+08:00') end as sla_actual_start,
                    CASE 	WHEN tc.`sla_task_flag_c` = 2 THEN CONVERT_TZ(tc.sla_stop_15min_c,'+00:00','+08:00')
                            WHEN tc.`sla_task_flag_c` IN (3,'s1','s2','s3') THEN CONVERT_TZ(tc.sla_stop_4hr_c,'+00:00','+08:00')
                            WHEN tc.`sla_task_flag_c` = 4 THEN CONVERT_TZ(tc.sla_stop_approver_c,'+00:00','+08:00')
                            ELSE CONVERT_TZ(tc.sla_start_15min_c,'+00:00','+08:00') END AS sla_stop_15min_c,
                    CASE 	WHEN tc.`sla_task_flag_c` = 2 THEN 
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(tc.`sla_start_15min_c`,'+00:00','+08:00'),CONVERT_TZ(tc.`sla_stop_15min_c`,'+00:00','+08:00'))
                            WHEN tc.`sla_task_flag_c` IN (3,'s1','s2','s3') THEN 
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(tc.`sla_start_4hr_c`,'+00:00','+08:00'),CONVERT_TZ(tc.`sla_stop_4hr_c`,'+00:00','+08:00'))
                            WHEN tc.`sla_task_flag_c` = 4  then
                            TIMESTAMPDIFF(SECOND,CONVERT_TZ(tc.`sla_start_approver_c`,'+00:00','+08:00'),CONVERT_TZ(tc.`sla_stop_approver_c`,'+00:00','+08:00'))
                            ELSE TIMESTAMPDIFF(SECOND,CONVERT_TZ(tc.`sla_start_15min_c`,'+00:00','+08:00'),CONVERT_TZ(tc.`sla_stop_15min_c`,'+00:00','+08:00'))
                            END AS actual_duration,
                    TIMESTAMPDIFF(SECOND,CONVERT_TZ(t.`date_start`,'+00:00','+08:00'),CONVERT_TZ(t.`date_due`,'+00:00','+08:00')) AS available_duration,
                    tc.task_duration_c, CONVERT_TZ(t.`reassign_time_c`,'+00:00','+08:00') as reassign_time_c, tc.is_sent,tc.resolution_c
                    from cases c
                    join cases_cstm cc on c.`id` = cc.`id_c` 
                    left join tasks t on t.`parent_id` = c.`id` and t.deleted = 0
                    left join tasks_cstm tc on tc.`id_c` = t.`id`
                    where c.deleted = 0 
                    and c.`case_number` = $caseNumber
                    order by t.date_entered";

        $results = DB::connection('mysql_crm')->select($query);
        return $results;
    }

    public static $RESETRESERVEUSER = array(
        'b8593d4f-41f9-399b-fe21-5d006a33362c', //azwandy
        'e3e4012d-620f-457d-8efb-a4f30a221f8d', //norhasfarine  
        'bb92b17c-43e7-4df8-b29a-d2c96d782e4e', //safinah 
        '8e1aee12-eb19-4209-b9c2-c327d1cc436f', //shahril  
    );

    public function getDashboardCRMITSpecAgeing()
    {
        $query = "SELECT 
            c.`case_number`,
            c.`redmine_number`,
            CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_created,
            c.status AS case_status,
            cc.`incident_service_type_c` AS incident_service_type,
            cc.`request_type_c` AS request_type,
            (SELECT m.value_name 
                FROM cstm_list_app m  
			    WHERE m.name = 'cdc_sub_category_list' AND m.value_code =  cc.sub_category_c ) AS module,  
            ( SELECT CASE 
							-- WHEN (pp.sla_task_flag_c IS NULL OR pp.sla_task_flag_c = '') THEN p.task_severity 
							WHEN p.case_redmine_number IS NOT NULL THEN 's4' 
							ELSE pp.sla_task_flag_c 
							END AS a  
			    FROM tasks p , tasks_cstm pp 
			    WHERE  p.id = pp.id_c AND p.parent_id = c.id 
			    AND p.date_entered IN (SELECT MAX(date_entered) FROM tasks r , tasks_cstm rr WHERE  r.id = rr.id_c AND r.parent_id = p.parent_id  ) 
			) AS severity,
            -- t.`id`, tt.`task_number_c`,
            -- t.name, 
            -- tt.sla_task_flag_c,
            -- CONVERT_TZ(t.`date_entered`,'+00:00','+08:00') as task_created, 
            CONVERT_TZ(t.date_start,'+00:00','+08:00') AS itspec_start_datetime ,
            DATEDIFF(NOW(),CONVERT_TZ(c.date_entered,'+00:00','+08:00')) AS ageing_day_case  ,
            DATEDIFF(NOW(),CONVERT_TZ(t.date_start,'+00:00','+08:00')) AS ageing_day_specialist 
            FROM cases c ,cases_cstm cc,
            tasks t , tasks_cstm tt
            WHERE 
            c.id = cc.id_c 
            AND c.id = t.`parent_id` 
            AND t.id = tt.id_c 
            AND c.`status` = 'Open_Assigned' 
            AND cc.incident_service_type_c = 'incident_it'
            AND cc.request_type_c = 'incident' 
            AND t.name IN ('Assigned to Production Support','Assigned to Group IT Specialist(Production Support)')
            AND tt.sla_task_flag_c = '3'   -- Confirmation task as under RIT
            /** to exclude task not category as FINANCE **/
            AND  NOT EXISTS (SELECT 1 FROM cstm_list_app WHERE value_code   = cc.sub_category_c AND value_code  IN ('10712_15034','10714_15842','10713_15534') )
            AND c.`deleted` = 0 
            AND t.`deleted` = 0";

        $result = DB::select($query);
        return $result;
    }

    public function getDashboardCRMPMOAgeing($type)
    {
        $query = "SELECT 
        c.case_number, t.name, tt.task_number_c, tt.sla_task_flag_c,
        c.redmine_number, t.implementation_issue,
        CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_created,
        c.status AS case_status,
        cc.incident_service_type_c AS incident_service_type,
        cc.request_type_c AS request_type,
        (SELECT m.value_name 
        FROM cstm_list_app m  
                WHERE m.name = 'cdc_sub_category_list' AND m.value_code =  cc.sub_category_c ) AS module,  
        ( SELECT CASE 
                            -- WHEN (pp.sla_task_flag_c IS NULL OR pp.sla_task_flag_c = '') THEN p.task_severity 
                            WHEN p.case_redmine_number IS NOT NULL THEN 's4' 
                            ELSE pp.sla_task_flag_c 
                            END AS a  
                FROM tasks p , tasks_cstm pp 
                WHERE  p.id = pp.id_c AND p.parent_id = c.id 
                AND p.date_entered IN (SELECT MAX(date_entered) FROM tasks r , tasks_cstm rr WHERE  r.id = rr.id_c AND r.parent_id = p.parent_id  ) 
        ) AS severity,
        CONVERT_TZ(t.date_start,'+00:00','+08:00') AS pmo_start_datetime ,
        DATEDIFF(NOW(),CONVERT_TZ(c.date_entered,'+00:00','+08:00')) AS ageing_day_case  ,
        DATEDIFF(NOW(),CONVERT_TZ(t.date_start,'+00:00','+08:00')) AS ageing_day_pmo, tt.task_duration_c AS taskduration
        FROM cases c, cases_cstm cc, tasks t, tasks_cstm tt
        WHERE c.id = cc.id_c 
        AND c.id = t.parent_id 
        AND t.id = tt.id_c 
        AND c.status = 'Open_Assigned' 
        AND cc.incident_service_type_c = 'incident_it'
        AND cc.request_type_c = 'incident' 
        AND t.name IN ('Assigned to Group PMO')";

        if ($type == 'before') {
            $query .= "AND (tt.sla_task_flag_c IS NULL OR tt.sla_task_flag_c = 's5' OR tt.sla_task_flag_c = '')";
        } else {
            $query .= "AND (tt.sla_task_flag_c = '4')";
        }

        $query .= "
        /* to exclude task not category as FINANCE */
        AND  NOT EXISTS (SELECT 1 FROM cstm_list_app WHERE value_code = cc.sub_category_c AND value_code IN ('10712_15034','10714_15842','10713_15534'))
        AND tt.task_number_c = (SELECT zc.task_number_c FROM tasks z, tasks_cstm zc
            WHERE z.id = zc.id_c
            AND z.parent_id = c.id
            AND z.deleted = 0
            ORDER BY z.date_entered DESC LIMIT 1)
        AND c.deleted = 0 
        AND t.deleted = 0";

        $result = DB::select($query);
        return $result;
    }

    public function pendingCaseSeverity3List($GroupID, $flag)
    {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id');
        $query->join('cases', 'cases.id', '=', 'tasks.parent_id');
        $query->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id');
        $query->leftJoin('users', 'users.id', '=', 'tasks.task_pic');
        $query->where('cases.deleted', 0);
        $query->where('tasks.deleted', 0);
        $query->where('cases.date_entered', '>=', '2019-01-01');
        $query->where('cases.status', 'Open_Assigned');
        $query->where('cases_cstm.request_type_c', 'incident');
        $query->where('cases_cstm.incident_service_type_c', 'incident_it');
        $query->whereNotIn('tasks.assigned_user_id', $GroupID);
        $query->where('tasks.task_severity', $flag);
        $query->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement']);
        $query->select(
            'tasks_cstm.task_number_c as taskno',
            'cases.case_number as caseNumber',
            'tasks.status as taskStatus',
            'tasks.name as taskname',
            'tasks_cstm.sla_task_flag_c as taskFlag',
            'users.first_name as IndividualAssigned',
            'tasks.date_start as datestart',
            'tasks.date_due as datedue',
            'tasks_cstm.acknowledge_time_c as acknowledgetime',
            'tasks_cstm.sla_start_4hr_c as actualstart',
            'tasks_cstm.sla_stop_4hr_c as actualstop',
            'tasks_cstm.task_duration_c as taskduration',
            'tasks.task_severity as taskSeverity',
            'tasks_cstm.assign_group_c as assignedGroupSpec',
            'tasks.case_redmine_number as redmine',
            'cases_cstm.sub_category_desc_c as subCategory',
            'cases_cstm.sub_category_2_desc_c as subCategory2',
            'cases.name as caseName',
            'tasks.assigned_user_id as group_id',
            'cases.status as caseStatus',
            'cases.date_entered as caseEntered',
            'cases.date_modified as caseModified'
        );
        $query->orderBy('tasks.date_start', 'desc');
        $data = $query->get();
        return $data;
    }
}
