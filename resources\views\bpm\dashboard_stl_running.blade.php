@extends('layouts.guest-dash')

@section('header')
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li class="{{ Request::is('dashboard/bpm/stl/Profile_Management') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Profile_Management">Profile Management</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Supplier_Management') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Supplier_Management">Supplier Management</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Codification') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Codification">Codification</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Procurement_Plan') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Procurement_Plan">Procurement Plan</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Contract_Management') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Contract_Management">Contract Management</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/SourcingDP') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/SourcingDP">Sourcing DP</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/SourcingQT') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/SourcingQT">Sourcing QT</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Order') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Order">Order</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/Fulfilment') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/Fulfilment">Fulfilment</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/YEP_Fulfilment') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/YEP_Fulfilment">YEP Fulfilment</a>
                </li>
                <li class="{{ Request::is('dashboard/bpm/stl/YEP_Order') ? 'active' : '' }}">
                    <a href="{{ url('/dashboard/bpm/stl') }}/YEP_Order">YEP Order</a>
                </li>
            </ul>
        </div>
    </div>
@endsection

@section('content')
    <!-- Content -->
    @if (Auth::user())
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> The list indicates that BPM instances are marked as "running," but they are actually stalled at
                        the subsequent activity/process. </strong></h1>
            </div>

            <div class="table-responsive">
                <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Cube Instance ID</th>
                            <th class="text-center">Component Name</th>
                            <th class="text-center">Composite ID</th>
                            <th class="text-center">Flow ID</th>
                            <th class="text-center">Composite Name</th>
                            <th class="text-center">Composite Version</th>
                            <th class="text-center">Cube Title</th>
                            <th class="text-center">Cube State ID</th>
                            <th class="text-center">Cube Date Modified</th>
                            <th class="text-center">Doc No.</th>
                            @if (!empty($listResult) && isset($listResult[0]) && $listResult[0]->composite_name === 'SourcingDP')
                                <th class="text-center">RN Status</th>
                                <th class="text-center">PR Status</th>
                                <th class="text-center">PR Date</th>
                                <th class="text-center">Current PR</th>
                                <th class="text-center">Total PR</th>
                            @elseif (!empty($listResult) && isset($listResult[0]) && $listResult[0]->composite_name === 'SourcingQT')
                                <th class="text-center">Status ID</th>
                                <th class="text-center">Status Name</th>
                            @endif
                        </tr>
                    </thead>
                    <tbody>
                        @if (isset($listResult))
                            @foreach ($listResult as $row)
                                <tr>
                                    <td class="text-center">{{ $row->cikey }}</td>
                                    <td class="text-center">{{ $row->component_name }}</td>
                                    <td class="text-center">
                                        <a href="{{ url('/bpm/instance/find') }}?composite_instance_id={{ $row->cmpst_id }}"
                                            target="_blank">
                                            {{ $row->cmpst_id }}</a>
                                    </td>
                                    <td class="text-center">{{ $row->flow_id }}</td>
                                    <td class="text-center">{{ $row->composite_name }}</td>
                                    <td class="text-center">{{ $row->composite_revision }}</td>
                                    <td class="text-center">{{ $row->title }}</td>
                                    <td class="text-center">{{ $row->state }}</td>
                                    <td class="text-center">{{ $row->modify_date }}</td>
                                    <td class="text-left">
                                        @if ($row->doc_no_list != null)
                                            @foreach ($row->doc_no_list as $rowDoc)
                                                <a href="{{ url('/find/trans/track/docno') }}/{{ $rowDoc->doc_no }}" target="_blank">
                                                    {{ $rowDoc->doc_no }}</a> &nbsp;&nbsp;
                                                @if (strlen($rowDoc->doc_no_other) > 0)
                                                    <a href="{{ url('/find/trans/track/docno') }}/{{ $rowDoc->doc_no_other }}" target="_blank">
                                                        {{ $rowDoc->doc_no_other }}</a> &nbsp;&nbsp;
                                                @endif
                                            @endforeach
                                        @endif
                                    </td>
                                    @if ($row->composite_name === 'SourcingDP' && !empty($row->rn_status_list))
                                        <td class="text-center">{{ $row->rn_status_list[0]->rn_status }}</td>
                                        <td class="text-center">{{ $row->rn_status_list[0]->pr_status }}</td>
                                        <td class="text-center">{{ $row->rn_status_list[0]->pr_date }}</td>
                                        <td class="text-center">{{ $row->rn_status_list[0]->current_pr }}</td>
                                        <td class="text-center">{{ $row->rn_status_list[0]->total_pr }}</td>
                                    @elseif ($row->composite_name === 'SourcingQT')
                                        <td class="text-center">
                                            {{ !empty($row->qt_status_list[0]) ? $row->qt_status_list[0]->status_id : '-' }}</td>
                                        <td class="text-center">
                                    {{ !empty($row->qt_status_list[0]) ? $row->qt_status_list[0]->status_name : '-' }}</td> @endif
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </DIV>
        </div>
    @endif


    <!-- END Content -->
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();
        /* Initialize Datatables */
        var tableListData = $('#basic-datatable').DataTable({
            columnDefs: [{
                orderable: false,
                targets: [0]
            }],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, -1],
                [10, 20, 30, 'All']
            ]
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection