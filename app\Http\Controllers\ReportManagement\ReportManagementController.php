<?php

namespace App\Http\Controllers\ReportManagement;

use App\Http\Controllers\Controller;
use App\Services\Traits\ReportManagement\ReportManagementService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Session;
use Carbon\Carbon;
use DB;
use Auth;
use Illuminate\Support\Facades\Cache;

class ReportManagementController extends Controller
{

    use ReportManagementService;

    public function reportManagement(Request $request)
    {
        $user = auth()->user()->first_name;
        $report_id = $request->editid;
        $report_name = $request->report_name;
        $helpdesk_no = $request->helpdesk_no;
        $report_requester = $request->requester;
        $report_description = $request->report_description;
        $list_report = $this->getListReport();
        if ($request->isMethod("POST")) {
            if ($report_id == null) {
                DB::connection('mysql_ep_report')->table('ep_request_report')->insert([
                    ['report_name' => $report_name, 'helpdesk_no' => $helpdesk_no, 'report_description' => $report_description, 'report_requester' => $report_requester, 'created_by' => $user, 'created_date' => Carbon::now()],
                ]);
                return back();
            } else {
                $updateData = [
                    'report_name' => $report_name,
                    'helpdesk_no' => $helpdesk_no,
                    'report_requester' => $report_requester,
                    'report_description' => $report_description,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_report')->table('ep_request_report')
                    ->where('report_id', $report_id)
                    ->update($updateData);
            }
        }
        return view('report_mgt.report_management', [
            'list_report' => $list_report,
        ]);
    }

    public function editReport($id)
    {
        $get_report = $this->getReportById($id);
        $get_script = $this->getScriptById($id);
        $get_attachment = $this->getResultById($id);
        return view('report_mgt.edit_report', [
            'get_report' => $get_report,
            'get_script' => $get_script,
            'get_attachment' => $get_attachment,
        ]);
    }

    protected function htmlListScriptData($report_id)
    {
        $listScript = DB::connection('mysql_ep_report')->table('ep_report_script')
            ->where('report_id', $report_id)
            ->orderBy('created_date', 'asc')
            ->get();

        $html = "";
        $counter = 1;
        foreach ($listScript as $key => $row) {
            $counter = $key + 1;
            $html = $html .
                "<div class='panel panel-default' style='margin-bottom: 5px;'>" .
                "<div class='panel-heading'>" .
                "<div class='form-group'>" .
                "<div class='col-md-9'>" .
                "<h4 class='panel-title'>$counter <i class='fa fa-angle-right'></i>" .
                "<a class='accordion-toggle' data-toggle='collapse' data-parent='#script_$row->script_id' href='#script_$row->script_id'> $row->script_name</a>" .
                "<a class='accordion-toggle' style='margin-left: 30px;'> (Created By : $row->created_by | $row->created_date)</a>" .
                "</h4>" .
                "</div>" .
                "<div class='col-md-2'> " .
                "<a script_id = '$row->script_id' 
                 db_env = '$row->env_database'
                 sc_env = '$row->env_schema'";
            if ($row->remarks != null) {
                $html .= " sc_remark='$row->remarks'";
            }
            $html .= "data-toggle='tooltip' class='btn btn-sm btn-primary editbutton'><i class='fa fa-edit'> Edit</i></a>" .
                "<a href='" . url('/report/edit-report/script/download', ['id' => $row->script_id]) . "' target='_blank' class='btn btn-sm btn-primary'><i class='fa fa-download'></i> Download</a>" .
                "</div>" .
                "<div class='pull-right'>
                           <a href='javascript:void(0)' data-toggle='tooltip'
                           onclick='onClickDeleteScript(this)' 
                           data-script-id='$row->script_id'
                           title='Delete this script' data-original-title=''><i class='fa fa-times'></i></a>  
                        </div>" .

                "</div>" .
                "<div id='script_$row->script_id' class='panel-collapse collapse'>" .
                "<div class='panel-body'>" .
                "<pre>" .
                "<code style='float: left; color:#faf7f7;' id='selected_script_table_json_desc'>$row->script_text</code>" .
                "</pre>" .
                "</div>" .
                "</div>" .
                "</div>" .
                "</div>";
        }

        return $html;
    }

    public function createScriptReport()
    {
        $user = auth()->user()->first_name;
        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $script_id = request()->script_id;
        $report_id = request()->report_id;
        $schema_env = request()->schema_env;
        $database_env = request()->database_env;
        $remarks = request()->script_remarks;
        $scriptName = rtrim(request()->scriptName, '.sql');
        $script = request()->script;

        if ($script_id == null) {
            $encodescript = base64_decode($script);
            DB::connection('mysql_ep_report')->table('ep_report_script')->insert([
                [
                    'report_id' => $report_id,
                    'script_name' => $scriptName . '.sql',
                    'script_text' => $encodescript,
                    'env_database' => $database_env,
                    'env_schema' => $schema_env,
                    'remarks' => $remarks,
                    'created_by' => $user,
                    'created_date' => Carbon::Now()
                ],
            ]);
        } else {
            $updateData = [
                'env_database' => $database_env,
                'env_schema' => $schema_env,
                'remarks' => $remarks,
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_report')->table('ep_report_script')
                ->where('script_id', $script_id)
                ->update($updateData);
        }

        $result = array(
            'status' => 'Success',
            'dataHtml' => $this->htmlListScriptData($report_id)
        );

        return response()->json($result, 200);
    }

    public function deleteScriptReport()
    {
        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $report_id = request()->report_id;
        $scriptId = request()->script_Id;

        if (strlen($scriptId) == 0 || strlen($report_id) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'scriptId and report_id is required'
            );
        } else {

            DB::connection('mysql_ep_report')->table('ep_report_script')
                ->where('script_id', $scriptId)
                ->delete();
            $result = array(
                'status' => 'Success',
                'description' => 'Delete',
                'dataHtml' => $this->htmlListScriptData($report_id)
            );
        }
        return response()->json($result, 200);
    }

    protected function htmlListUploadAttachment($report_id)
    {
        $listAttachment = DB::connection('mysql_ep_report')->table('ep_report_result')
            ->where('report_id', $report_id)
            ->orderBy('created_date', 'asc')
            ->get();
        $html = "";
        $counter = 1;
        foreach ($listAttachment as $key => $row) {
            $counter = $key + 1;
            $html = $html .
                "<div class='panel panel-default' style='margin-bottom: 5px;'>" .
                "<div class='panel-heading'>" .
                "<div class='form-group'>" .
                "<h4 class='panel-title'>" .
                "<div class='col-md-9'>$counter <i class='fa fa-angle-right'></i>" .
                "<a class='accordion-toggle' data-parent='#uploadAttached_$row->result_id' href='#uploadAttached_$row->result_id'> $row->file_name</a>" .
                "<a class='accordion-toggle' style='margin-left: 30px;'> (Created By : $row->created_by | $row->created_date)</a>" .
                "</div>" .
                "<div class='col-md-2'>" .
                "<a result_id = '$row->result_id'";
            if ($row->result_description != null) {
                $html .= " description='$row->result_description'";
            }
            $html .= "data-toggle='tooltip' class='btn btn-sm btn-primary editattach'><i class='fa fa-edit'> Edit</i></a> " .
                "<a href='" . url('/report/edit-report/attachment/download', ['id' => $row->result_id]) . "' target='_blank' class='btn btn-sm btn-primary'><i class='fa fa-download'></i> Download</a>" .
                "</div>" .
                "<div class='pull-right'>
                           <a href='javascript:void(0)' data-toggle='tooltip'
                           onclick='onClickDeleteAttachment(this)' 
                           data-result-id='$row->result_id'
                           title='Delete this attachment' data-original-title=''><i class='fa fa-times'></i></a>  
                        </div>" .
                "</h4>" .
                "</div>" .
                "</div>" .
                "</div>" .
                "</div>";
        }

        return $html;
    }

    public function createAttachmentReport(Request $request)
    {
        $report_id = $request->report_id;
        $remarks = $request->remarks;

        // $path_name = $file->getRealPath();

        // $file_type = substr($file_name, strrpos($file_name, '.') + 1);
        $attachId = $request->attach_id;
        $user = $user = auth()->user()->first_name;

        if ($attachId == null) {
            $file = $request->file('upload_attachment');
            $file_name = $file->getClientOriginalName();
            $folder_path = storage_path('Report/report_management/' . $report_id);
            Storage::makeDirectory($folder_path);
            $path = Storage::putFileAs('Report/report_management/' . $report_id, $file, 'attachment - ' . $file_name);
            DB::connection('mysql_ep_report')->table('ep_report_result')->insert([
                [
                    'report_id' => $report_id,
                    'result_description' => $remarks,
                    'file_name' => 'attachment - ' . $file_name,
                    'folder_path' => 'D:\Project\PHP_laravel\ep-support\storage\app/Report/report_management/' . $report_id,
                    'created_by' => $user,
                    'created_date' => Carbon::Now()
                ],
            ]);
        } else {
            $updateData = [
                'result_description' => $remarks,
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_report')->table('ep_report_result')
                ->where('result_id', $attachId)
                ->update($updateData);
        }
        $result = array(
            'status' => 'Success',
            'data_upload' => $this->htmlListUploadAttachment($report_id)
        );

        return response()->json($result, 200);
    }

    public function downloadScriptReport($script_id)
    {

        $getscriptDownload = $this->getScriptByScriptId($script_id);

        $sqlContent = $getscriptDownload[0]->script_text;
        $filename = $getscriptDownload[0]->script_name . '.sql';

        $headers = [
            'Content-Type' => 'application/octet-stream',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        return Response::make($sqlContent, 200, $headers);
    }

    public function downloadAttachmentReport($result_id)
    {
        $getDownloadAttachment = $this->getResultByResultId($result_id);

        $filePath = storage_path('app/Report/report_management/' . $getDownloadAttachment[0]->report_id . '/' . $getDownloadAttachment[0]->file_name);
        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        $headers = [
            'Content-Type' => 'application/octet-stream',
            'Content-Disposition' => 'attachment; filename="' . $getDownloadAttachment[0]->file_name . '"',
        ];

        return response()->download($filePath, $getDownloadAttachment[0]->file_name, $headers);
    }

    public function deleteAttacmentReport()
    {
        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $result_id = request()->attachId;
        $report_id = request()->reportId;

        if (strlen($result_id) == 0 || strlen($result_id) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'result_id is required'
            );
        } else {
            DB::connection('mysql_ep_report')->table('ep_report_result')
                ->where('result_id', $result_id)
                ->delete();
            $result = array(
                'status' => 'Success',
                'description' => 'Delete',
                'dataUpload' => $this->htmlListUploadAttachment($report_id)
            );
        }
        return response()->json($result, 200);
    }

    public function nonComplianceCptpp()
    {
        return view('report_mgt.non_compliance_cptpp', [
            'module' => null,
            'startDate' => null,
            'toDate' => null,
        ]);
    }

    public function nonComplianceCptppForModule($startDate, $toDate, $module)
    {
        $getListByModuleSM = $getListByModulePP = $getListByModuleCT = $getListByModuleFL = $getListByModuleQT = $getListByQtIklan = $getListByQtKodBidang = $getListByQtLoa = $getListByQtTempasal = null;

        if ($module == 'sm') {
            $getListByModuleSM = $this->listByModuleSM($startDate, $toDate);
        }

        if ($module == 'pp') {
            $getListByModulePP = $this->listByModulePP($startDate, $toDate);
        }

        if ($module == 'qt') {
            $getListByModuleQT = $this->listByModuleQT($startDate, $toDate);
            $getListByQtIklan = $this->listByModuleQtIklan($startDate, $toDate);
            $getListByQtKodBidang = $this->listByModuleQtKodbidang($startDate, $toDate);
            $getListByQtLoa = $this->listByModuleQtLoa($startDate, $toDate);
            $getListByQtTempasal = $this->listByModuleQtTempasal($startDate, $toDate);
        }

        if ($module == 'ct') {
            $getListByModuleCT = $this->listByModuleCT($startDate, $toDate);
        }

        if ($module == 'fl') {
            $getListByModuleFL = $this->listByModuleFL($startDate, $toDate);
        }

        $responseData = [
            'getListByModuleSM' => $getListByModuleSM,
            'getListByModulePP' => $getListByModulePP,
            'getListByModuleQT' => $getListByModuleQT,
            'getListByQtIklan' => $getListByQtIklan,
            'getListByQtKodBidang' => $getListByQtKodBidang,
            'getListByQtLoa' => $getListByQtLoa,
            'getListByQtTempasal' => $getListByQtTempasal,
            'getListByModuleCT' => $getListByModuleCT,
            'getListByModuleFL' => $getListByModuleFL,
        ];
        return response()->json($responseData, 200);
    }

    public function securityMatrixSummaryView()
    {
        return view('report_mgt.security_matrix_summary');
    }

    protected function getMonthlyData(array $data)
    {
        try {
            $collection = collect($data);

            $responseData = [
                'status' => 'Success',
                'data' => $collection,
            ];
        } catch (\Exception $e) {
            $responseData = [
                'status' => 'Error',
                'message' => $e->getMessage(),
            ];
        }

        return response()->json($responseData, 200);
    }

    public function securityMatrixSummarySpkiData(Request $request)
    {
        $currentDate = Carbon::now();
        $year = (int)($request->year ?? $currentDate->year);
        $month = (int)($request->month ?? $currentDate->month);

        if ($month == $currentDate->month && $currentDate->day == 1) {
            $month = $currentDate->subMonth()->month;
        }

        $cacheKey = "spki_matrix_{$year}_{$month}";

        $data = Cache::remember($cacheKey, Carbon::now()->addDay(), function () use ($year, $month) {
            return $this->getSpkiMatrixMonthly($year, $month);
        });

        return $this->getMonthlyData($data);
    }

    public function securityMatrixSummaryAp511Data(Request $request)
    {
        $currentDate = Carbon::now();
        $year = (int)($request->year ?? $currentDate->year);
        $month = (int)($request->month ?? $currentDate->month);

        if ($month == $currentDate->month && $currentDate->day == 1) {
            $month = $currentDate->subMonth()->month;
        }

        $cacheKey = "ap511_matrix_{$year}_{$month}";

        $data = Cache::remember($cacheKey, Carbon::now()->addDay(), function () use ($year, $month) {
            return collect($this->getAp511MatrixMonthly($year, $month))
                ->mapWithKeys(function ($item) {
                    return [
                        'year_created' => $item->year_created,
                        'month_created' => $item->month_created,
                        'total_created' => $item->total_created,
                        'total_processed' => $item->total_processed,
                        'total_exceed_24hour' => $item->total_exceed_24hour,
                        'percentage' => number_format($item->total_processed / $item->total_created * 100, 2),
                    ];
                })
                ->toArray();
        });

        return $this->getMonthlyData($data);
    }

    public function securityMatrixSummaryApiveData(Request $request)
    {
        $currentDate = Carbon::now();
        $year = (int)($request->year ?? $currentDate->year);
        $month = (int)($request->month ?? $currentDate->month);

        if ($month == $currentDate->month && $currentDate->day == 1) {
            $month = $currentDate->subMonth()->month;
        }

        $cacheKey = "apive_matrix_{$year}_{$month}";

        $data = Cache::remember($cacheKey, Carbon::now()->addDay(), function () use ($year, $month) {
            return collect($this->getApiveMatrixMonthly($year, $month))
                ->mapWithKeys(function ($item) {
                    return [
                        'year_created' => $item->year_created,
                        'month_created' => $item->month_created,
                        'total_created' => $item->total_created,
                        'total_processed' => $item->total_processed,
                        'total_exceed_24hour' => $item->total_exceed_24hour,
                        'percentage' => number_format($item->total_processed / $item->total_created * 100, 2),
                    ];
                })
                ->toArray();
        });

        return $this->getMonthlyData($data);
    }
}
