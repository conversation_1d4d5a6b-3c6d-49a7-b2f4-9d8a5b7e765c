@extends('layouts.guest-dash')
@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-4 log-header-title">
            <span>List CT Agreement Cancellation<br /></span>
            <small>To View List Detail</small>
        </div>
        <div class="col-md-8 log-header-menu">
            <a href="{{url('ep/ct/stat-ct-cancel-batch-no')}}"><span class="{{ Request::is('ep/ct/stat-ct-cancel-batch-no') ? 'active' : '' }}">Statistic By Batch No</span></a> |
            <a href="{{url('ep/ct/list-ct-cancel-agreement')}}"><span class="{{ Request::is('ep/ct/list-ct-cancel-agreement') ? 'active' : '' }}">List CT Agreement Cancellation</span></a> |
            <a href="{{url('ep/ct/list-ct-agreement-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-null') ? 'active' : '' }}">WIP List CT Agreement Null</span></a> |
            <br/><a href="{{url('ep/ct/list-ct-pending-amendment')}}"><span class="{{ Request::is('ep/ct/list-ct-pending-amendment') ? 'active' : '' }}">WIP List CT Pending Amendment</span></a> | 
            <a href="{{url('ep/ct/list-ct-agreement-not-null')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-not-null') ? 'active' : '' }}">WIP List CT Agreement Not Null</span></a> | 
            <a href="{{url('ep/ct/list-ct-agreement-missing')}}"><span class="{{ Request::is('ep/ct/list-ct-agreement-missing') ? 'active' : '' }}">WIP List CT Agreement Missing</span></a>     
        </div>
    </div>
</div>
@endsection
@section('content')
<div class="block">
    <form class="form-horizontal" id="carian-form" action="{{url('/ep/ct/list-ct-cancel-agreement')}}/" method="get" >
        <div class="row">
            <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cari">Carian / Search (Batch No. | Contract No. | Loa No.) </label>
                        <div class="col-md-6">
                            <input type="text" id="cari" name="cari" class="form-control" value="{{$carian}}"  onfocus="this.select();" placeholder="Klik carian batch no. | contract no. | Loa No. di sini ... ">
                        </div>
                    </div>
            </div>
        </div>
    </form>
</div>

@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> </h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              
              <p>Tiada rekod!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title">
            <h1><i class="fa fa-building-o"></i> <strong>Statistic CT Cancellation Agreement</strong></h1>
        </div>

        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        
                        <th class="text-center">CONTRACT NO</th>
                        <th class="text-center">LOA NO</th>
                        <th class="text-center">INSTANCE ID</th>
                        <th class="text-center">BATCH NO.</th>
                        <th class="text-center">PROGRESSSTATUS</th>
                        <th class="text-center">WF DATE CREATED</th>
                        <th class="text-center">LATEST STATUS</th>
                        <th class="text-center">STATUS ID</th>
                        <th class="text-center">CONTRACT ID</th>
                        <th class="text-center">AGREEMENT ID</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">
                        <a href="{{ url("/find/contract/") }}?cari={{$data->contract_no}}" target="_blank" >
                            {{$data->contract_no }} </a>
                        </td>
                        <td class="text-center">
                        {{$data->loa_no }} 
                        </td>
                        <td class="text-center">
                        <a href="{{ url("/bpm/instance/find") }}?composite_instance_id={{$data->instance_id}}" target="_blank" >
                        {{$data->instance_id }} </a>
                        </td>
                        <td class="text-center">{{$data->batch_no }}</td>
                        <td class="text-center">{{ $data->progress_status }} - {{ App\Services\EPService::$CT_CANCELLATION_AGREEMENT_PROGRESS[$data->progress_status]}}</td>
                        <td class="text-center">{{$data->fws_date_created }}</td>
                        <td class="text-center">{{$data->latest_status }}</td>
                        <td class="text-center">{{$data->status_id }}</td>
                        <td class="text-center">{{$data->contract_id }}</td>
                        <td class="text-center">{{$data->agreement_id }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


@endsection
