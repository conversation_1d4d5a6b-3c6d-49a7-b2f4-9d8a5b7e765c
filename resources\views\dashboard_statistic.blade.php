@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
 <div class="row">
     <div class="col-lg-12">
         <div class='widget'>
             <div class='widget-extra themed-background-dark center-block'>
                 <h5 class='widget-content-light'>
                     Case-Statistics Resolved : <strong>By Group Specialist </strong>      
                 </h5>
             </div>
             <div class='text-center' style="background: #ffffff; padding: 5px;">  
                 <button type="submit" id="button_submit_PS" name="button_submit_PS" class="btn btn-sm btn-info text-center" value="d3bf216c-122b-4ce5-9410-899317762b60"> 
                     <div class="h5 mb-0" style="font-weight: 800">PS</div>
                     <span>Production Support</span>
                 </button>
                 <button type="submit" id="button_submit_M" name="button_submit_M" class="btn btn-sm btn-info text-center" value="5f919eef-f9cc-4278-b7d1-bbc1aa87ff6e">
                     <div class="h5 mb-0" style="font-weight: 800">M</div>
                     <span>Middleware</span>
                 </button>
                 <button type="submit" id="button_submit_M_STL" name="button_submit_M_STL" class="btn btn-sm btn-info text-center">
                     <div class="h5 mb-0" style="font-weight: 800">M</div>
                     <span>Middleware (STL)</span>
                 </button>
             </div>
             <h5 class="alert alert-success" id="success" style="display:none;"></h5>
             <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
             <div id="dash_statistic_assignedgroup">
                 <div class="text-center"><i class=""></i></div>
             </div>
         </div>
     </div>     
     </div>
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<div id="modal-list-statistic" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-list-statistic-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="form-group" id="edit-statistic">
                            
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
        $(function () {

            $("#date_resolve").datepicker();
        });
</script>
<script>
    var APP_URL = {!! json_encode(url('/')) !!};

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $('.widget').on("click", '.modal-list-statistic-action', function () {

            $('.spinner-loading').show();
            $('#edit-statistic').html('').fadeIn();

            $('#modal-list-statistic-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#edit-statistic').html($data).fadeIn();

                    $('.spinner-loading').hide();
                    $('#btn_update_stats').click(function() {
                        var userId = $("input[name=userid]").val();
                        var fullName = $("input[name=fullname]").val();
                        var totalDocNo = $("input[name=totaldocno]").val();
                        var category = $('#category option:selected').text();
                        var module = $('#module option:selected').text();
                        var case_no = $("input[name=case_no]").val();
                        var doc_no = $("input[name=doc_no]").val();
                        var dateResolve = $("input[name=date_resolve]").val();
                        var remarks = $("input[name=remarks]").val();
                        var csrf = $("input[name=_token]").val(); 
                        
                        if(category == 'Please Select' || module == 'Please Select'){
                            $('#errorMsg').show();
                            document.getElementById("errorMsg").innerHTML ='Please Select Category';
                        }else{
                            $('#errorMsg').hide();
                            if(category == 'ASSIST'){
                                
                                if(doc_no == ''){
                                    $('#requiredDocNo').show();
                                    $('#requiredTotal').hide();
                                    $('#requiredDate').hide();
                                }else if(totalDocNo == ''){
                                    $('#requiredDocNo').hide();
                                    $('#requiredTotal').show();
                                    $('#requiredDate').hide();
                                }else if(dateResolve == ''){
                                    $('#requiredDocNo').hide();
                                    $('#requiredTotal').hide();
                                    $('#requiredDate').show();
                                }else{
                                    $('#errorMsg').hide();
                                    $('#requiredDate').hide();
                                    $('#requiredTotal').hide();
                                    $('#requiredDocNo').hide();
                                    
                                    updateStatistic(csrf,userId,fullName,totalDocNo,category,module,case_no,doc_no,dateResolve,remarks);
                                }
                            }else{

                                $('#requiredDocNo').hide();
                                if(totalDocNo == ''){
                                    $('#requiredTotal').show();
                                    $('#requiredDate').hide();
                                }else if(dateResolve == ''){
                                    $('#requiredTotal').hide();
                                    $('#requiredDate').show();
                                }else{
                                    $('#errorMsg').hide();
                                    $('#requiredDate').hide();
                                    $('#requiredTotal').hide();
                                    
                                    updateStatistic(csrf,userId,fullName,totalDocNo,category,module,case_no,doc_no,dateResolve,remarks);
                                }
                            }
                        }
                    });
                }
            });
        });
        
        function updateStatistic(csrf,userId,fullName,totalDocNo,category,module,case_no,doc_no,dateResolve,remarks){
            $.ajax({
                type: 'POST',
                url: APP_URL + '/list/individualmodule/updateStatisticValue',
                data: {"_token": csrf,"user_id": userId,"full_name": fullName,"total": totalDocNo, "category": category, "module": module,
                        "case_no": case_no, "doc_no": doc_no, "date_resolve": dateResolve, "remarks": remarks},
                success: function(resp) {
                        $('#modal-list-statistic').modal('hide');
                        if(resp == 'Success'){
                            $('#success').show();
                            document.getElementById("success").innerHTML = resp;
                            setTimeout(location.reload.bind(location), 3000);
                        }else{
                            $('#errorMsg').show();
                            document.getElementById("errorMsg").innerHTML = resp;
                        }
                }
            });
        }
        $('#button_submit_PS').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/statistic/moduleAssigned',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_statistic_assignedgroup').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_M').click(function() {
        var groupID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/statistic/moduleAssigned',
            data: {"groupid": groupID},
            success: function(data) {
               $data = $(data);
               $('#dash_statistic_assignedgroup').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_M_STL').click(function() {
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/statistic/moduleAssignedSTL',
            success: function(data) {
               $data = $(data);
               $('#dash_statistic_assignedgroup').hide().html($data).fadeIn();
            }
            });
        });
    });

</script>
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection