<?php

namespace App\Console\Commands\LogTrace;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\ExportLogService;

class ExtractBacklogTraceToFactTable extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ExtractBacklogTraceToFactTable';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To extract backlog data into epss fact table';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $extract = new ExportLogService();
        try {
            $extract->export_user_requests(Carbon::yesterday());
            $extract->export_cube_requests(Carbon::yesterday());

            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $extract->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }
}
