@extends('layouts.guest-dash')

@section('content')

<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
            </li>
            <li  class="active">
                <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
            </li>
            <li >
                <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
            </li>
        </ul>
    </div>
</div> 

<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title " >
            <h1><i class="fa fa-tasks"></i> <strong>Error Handler List</strong></h1>                           
        </div>
        
        @if($statusApi != null)
            <h5 class="alert alert-danger">{{$statusApi}}</h5>
        @endif
    
        <div class="block">
            <form id="form-search-task" action="{{url("/bpm/errhandler/find")}}" method="post" class="form-horizontal form-bordered">
                {{ csrf_field() }}
                <div class="col-md-4">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="task_title">Task Title <span class="text-danger">*</span></label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="task_title" name="task_title" required class="form-control taskTitle">
                                        <option value="" >Please Select</option>
                                        @foreach($jsondata as $key => $value)
                                        <option value="{{$key}}" @if($key == old('task_title') ) selected @endif>{{$key}}</option>
                                        @endforeach
                                    </select>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-4">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="status_id">Status Id <span class="text-danger">*</span></label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="status_id" name="status_id" required class="form-control statusId">
                                        <?php
                                        foreach ($jsondata as $key => $value) {
                                            if ($key === $taskTitle) {
                                                foreach ($value as $datas) {
                                                    ?>
                                                    <option value="{{$datas}}" @if($datas == old('status_id') ) selected @endif>{{$datas}}</option>
                                                    <?php
                                                }
                                            }
                                        }
                                        ?>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-4">
                    <fieldset>
                        <div class="form-group">
                            <div class="col-md-9">
                                <div class="form-actions form-actions-button text-right " style="margin-right:30px;">
                                    <button type="submit" class="btn btn-sm btn-info pull-right"><i class="fa fa-search"> Search </i></button>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
            </form><br/><br/><br/><br/>
        </div>

        @if(isset($listdata))
        <div class="block">
            <p> Total Records : {{ $listdata["total"] }}</p>
            <p> This table will show the first 50 records only</p>
            <div class="table-responsive">
                <table id="bpmapi-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Instance</th>
                            <th class="text-center">Title</th>
                            <th class="text-center">Activity</th>
                            <th class="text-center">Created</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($listdata["worklistItem"] as $data)
                        <?php
                        $createDate = date("d/m/Y h:i:s A", substr($data["createDate"], 0, 10));
                        $module = explode("*", $data['compositeDN']);
                        ?>
                        <tr>
                            <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_module={{ $module[0] }}&composite_instance_id={{$data['instanceId']}}" target="_blank" > {{ $data['instanceId'] }}</a></td>
                            <td class="text-center">{{ $data['title'] }}</td>
                            <td class="text-center">{{ $data['taskName'] }}</td>
                            <td class="text-center">{{ $createDate }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        @endif
    </div>
</div>


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    TablesDatatables.init();
});</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
    ModalListActionLogDatatable.init();
});</script>

<script>

    $('.taskTitle').click(function () {
        var task = $('#task_title option:selected').text();
        var jsondata = <?php echo json_encode($jsondata); ?>;
        if (jsondata[task]) {
            var options = '';
            for (i in jsondata[task]) {
                var value = jsondata[task][i];
                options += '<option value = "' + value + '">' + value + '</option>';
            }
            $('select[name="status_id"]').html(options);
        }

    });
    


</script>
@endsection