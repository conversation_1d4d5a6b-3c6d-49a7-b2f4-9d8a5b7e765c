@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>FULFILMENT BPM TASK<br>
                <small>Refire Stop Instruction</small>
            </h1>
        </div>
    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="row" >
                <div class="col-sm-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>Search</strong> SD Number</h2>
                        </div>

                        @if($status_api != null)
                            <h5 class="alert alert-danger">{{$status_api}}</h5>
                        @endif
                        
                        @if($task_assigned != null)
                        <div class="block">
                            <h5 class="alert alert-danger">Running Instance Detected.Do Not Refire</h5>
                        <ul>
                            <li>{{ $task_assigned[0]->composite_id }}</li>
                            <li>{{ $task_assigned[0]->composite_module }}</li>
                            <li>{{ $task_assigned[0]->composite_revision }}</li>
                        </ul>
                        </div>
                        @endif
                        
                        
                        <form action="{{url('/bpm/fl/task/stopInstruction')}}" method="post" class="form-horizontal form-bordered" >
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="sd_no" name="sd_no" class="form-control" placeholder="SD Number .. Fulfilment modules only" 
                                           required="required" value="{{old('sd_no')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Document Number <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="supplier_login_id" name="supplier_login_id" class="form-control" placeholder="Supplier Login Id.." 
                                           required="required" value="{{old('supplier_login_id')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Supplier Login Id <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="action_task_stop_instr" name="action_task_stop_instr" class="select-chosen" data-placeholder="Choose Type Action Task .." style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="stop-instr" @if(old('action_task_stop_instr') === "stop-instr") selected @endif>Stop Instruction</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-gears"></i>  Choose Type Action Task <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="is_trigger_stop_instr" name="is_trigger_stop_instr" class="select-chosen" data-placeholder="True (program will execute trigger to BPM) , False (program will not execute trigger to BPM)" style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="true" @if(old('is_trigger_stop_instr') === "true") selected @endif>True</option>
                                        <option value="false"   @if(old('is_trigger_stop_instr') === "false") selected @endif>False</option>
                                    </select>
                                    <span class="input-group-addon"><i class="hi hi-transfer"></i>  Is Trigger BPM </span>
                                </div>
                            </div>
                            
                            <div class="form-group form-actions">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-sm btn-info pull-right">Submit</button>
                                </div>
                            </div>
                        </form>
                        
                        @if($listdata != null && count($listdata) > 0)

                        <div class="block">

                            @if(isset($listdata["result"]["is_trigger_bpm"]) && isset($listdata["result"]["bpm_version"]) && isset($listdata["result"]["bpm_instance_id"]) && isset($listdata["result"]["doc_no"]))
                                @if($listdata["result"]["is_trigger_bpm"] == 'true')
                                <div class="alert alert-success">
                                    <b><h5>Success refire task..</h5>
                                        <ul>
                                            <li>Composite : {{$listdata["result"]["bpm_version"]}}</li>
                                            <li>Instance Id : {{$listdata["result"]["bpm_instance_id"]}} </li>
                                            <li>PR Number : {{$listdata["result"]["doc_no"]}} </li>
                                    </b>
                                    </ul>
                                </div>
                                @endif
                            @endif

                            <h4>Payload</h4>
                                @if(isset($listdata["result"]["xml_payload"]))
                                <pre class="line-numbers">
                                        <code class="language-markup">{{ htmlentities($listdata["result"]["xml_payload"]) }}</code>
                                </pre>
                                @else
                            <h5 class="alert alert-danger">{{$listdata["result"]["remarks"]}}</h5>
                            @endif
                        </div>

                        @endif

                    </div>

                </div>
            </div>

        </div>
    </div>
    



@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



