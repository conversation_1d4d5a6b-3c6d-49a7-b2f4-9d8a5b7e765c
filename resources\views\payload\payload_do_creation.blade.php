@extends('layouts.guest-dash')

@section('header')
@endsection


@section('content')
<div class="block">

    <form id="form-search" action="{{url("/find/fulfilment/do/search")}}/" method="get" class="form-horizontal" onsubmit="return true;">
        <div class="form-group">
            <label class="col-md-3 control-label" for="ep_no">PO/CO No. <span class="text-danger">*</span></label>
            <div class="col-md-5">
                <input id="poco_no" name="poco_no" class="form-control" placeholder="PO/CO No.." type="text" required
                       @if(isset($formSearch)) value="{{ $formSearch["poco_no"] }}" @endif>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-3 control-label" for="do_no">DO No. <span class="text-danger">*</span></label>
            <div class="col-md-5">
                <input id="do_no" name="do_no" class="form-control" placeholder="DO No.." type="text" required
                       @if(isset($formSearch)) value="{{ $formSearch["do_no"] }}" @endif>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="col-md-9 col-md-offset-3">
                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
            </div>
        </div>
    </form>
</div>

@if($result == 'found' && ($supplier == null || $deliveryorder == null || $receivingofficer == null || $do == null))
@if($supplier == null)
<div class="block block-alt-noborder full text-center label-warning">
    <span style="color: #FFF;font-size: 15px"><strong>Supplier Record Not Found!</strong></span>
</div>
@elseif($deliveryorder == null || $do == null) 
<div class="block block-alt-noborder full text-center label-warning">
    <span style="color: #FFF;font-size: 15px"><strong>Delivery Order Record Not Found!</strong></span>
</div>
@elseif($receivingofficer == null) 
<div class="block block-alt-noborder full text-center label-warning">
    <span style="color: #FFF;font-size: 15px"><strong>Receiving Officer Record Not Found!</strong></span>
</div>
@else
<div class="block block-alt-noborder full text-center label-warning">
    <span style="color: #FFF;font-size: 15px"><strong>Tidak Dijumpai!</strong></span>
</div>
@endif
@endif
@if($result != null && $supplier != null && $deliveryorder != null && $receivingofficer != null && $do != null )
<div class="block block-alt-noborder full">
    <div class="block">  
        <a href="{{url('/crm/guideline/stuck_gfmas/PENDING_DO-STL_GL15082019.docx')}}" style="color: #3498db; text-decoration: underline;">Guideline Refire Task Pending DO Acknowledgement </a><br/><br/>
        <div class="block">

            <div class="block-title">
                <h2>DeliveryOrderListInitiation  </h2>
            </div>
            <?php
            $xmlSupplier = '';
            foreach ($supplier as $supplierdata) {
                $designationSupplier = str_replace("&", "&amp;", $supplierdata->designation);
                $xmlDOFull = '<FL_Delivery_Order_List_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/DoFulfillment/FL_Delivery_Order_List_Data" xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/DoFulfillment/FL_DeliveryOrder_Data" xmlns:user="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">';

                $xmlReceivingOfficer = '';

                $xmlSupplier = $xmlSupplier . '
    <supplierList>
        <user:userId>' . $supplierdata->user_id . '</user:userId>
        <user:userLoginId>' . $supplierdata->user_login_id . '</user:userLoginId>
        <user:userName>' . $supplierdata->username . '</user:userName>
        <user:calendarName>PUTRAJAYA</user:calendarName>
        <user:designation>' . $designationSupplier . '</user:designation>
    </supplierList>';
            }
            foreach ($deliveryorder as $deliveryorderdata) {
                $date = $deliveryorderdata->supplier_do_date;
                $xmlDeliveryOrder = '
        <ns3:doId>' . $deliveryorderdata->do_id . '</ns3:doId>
        <ns3:doDocType ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
        <ns3:doDocNo>' . $deliveryorderdata->do_doc_no . '</ns3:doDocNo>
        <ns3:supplierDoNo>' . $deliveryorderdata->supplier_do_no . '</ns3:supplierDoNo>
        <ns3:supplierDoDate>' . $deliveryorderdata->supplier_do_date . 'T' . $deliveryorderdata->supplier_do_time . '+08:00' . '</ns3:supplierDoDate>';

                foreach ($receivingofficer as $receivingofficerdata) {
                    $designation = str_replace("&", "&amp;", $receivingofficerdata->designation);
                    $xmlReceivingOfficer = $xmlReceivingOfficer . '
    <ns3:receivingOfficerList>
        <user:userId>' . $receivingofficerdata->user_id . '</user:userId>
        <user:userLoginId>' . $receivingofficerdata->user_login_id . '</user:userLoginId>
        <user:userName>' . $receivingofficerdata->username . '</user:userName>
        <user:calendarName>PUTRAJAYA</user:calendarName>
        <user:designation>' . $designation . '</user:designation>
    </ns3:receivingOfficerList>';
                }

                foreach ($do as $dodata) {
                    $ordername = str_replace("&", "&amp;", $dodata->order_name);
                    $xmlDO = '
    <ns3:status ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
    <ns3:orderId>' . $dodata->order_id . '</ns3:orderId>
    <ns3:requestId>' . $dodata->request_id . '</ns3:requestId>
    <ns3:orderName>' . $ordername . '</ns3:orderName>
    <ns3:orderDocNo>' . $dodata->order_doc_no . '</ns3:orderDocNo>
    <ns3:sapOrderNo>' . $dodata->sap_order_no . '</ns3:sapOrderNo>
    <ns3:orderType>' . $dodata->order_type . '</ns3:orderType>
    <ns3:supplier ns4:nil="true" xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance"/>
    <ns3:isPhis>' . $dodata->isphis . '</ns3:isPhis>
    <ns3:businessArea>' . $dodata->business_area . '</ns3:businessArea>';
                }
            }
            ?>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($xmlDOFull . $xmlSupplier) }}</code>
                <code class="language-markup">{{ htmlentities('    <deliveryOrderList>' .$xmlDeliveryOrder .$xmlReceivingOfficer .$xmlDO ) }}</code>
                <code class="language-markup">{{ htmlentities('    </deliveryOrderList>') }}</code>
                <code class="language-markup">{{ htmlentities('</FL_Delivery_Order_List_Data>') }}</code>
            </pre>
            <br/><br/>
        </div>
        <div class="block">
            <div class="block-title">
                <h2>createBy  </h2>
            </div>
            <div class="block">
                <?php
                foreach ($supplier as $supplierdata) {

                    $createBy = $supplierdata->user_login_id;
                }
                ?>
                {{$createBy}} <br/><br/>
            </div>
        </div>
    </div>
</div>
@endif
@include('_shared._modalListLogAction')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });</script>
<script>

    $('#page-container').removeAttr('class');
</script>
@endsection



