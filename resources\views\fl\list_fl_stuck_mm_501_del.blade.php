@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Stuck Task PRCR (MM501-DEL) <br>
                <small>Stuck Task PRCR (MM501-DEL)</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>Stuck Task PRCR (MM501-DEL) List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck Task PRCR (MM501-DEL) List </strong>
                        <small></small>
                    </h1>
                </div>
             
                <div class="table-responsive">
                    <table id="prcr_mm501_del-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DD</th>
                            <th class="text-center">MM</th>
                            <th class="text-center">FWS DATE</th>
                            <th class="text-center">YEAR</th>
                            <th class="text-center">PRCR NO.</th>
                            <th class="text-center">POCO NO.</th>
                            <th class="text-center">STATUS NAME</th>
                            <th class="text-center">CANCEL DATE</th>
                            <th class="text-center">STATUS DESC</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->dd }}</td>
                                <td class="text-center">{{ $data->mm }}</td>
                                <td class="text-center">{{ $data->fws_date }}</td>
                                <td class="text-center">{{ $data->year }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->prcr  }}" >{{ $data->prcr }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->pono  }}" >{{ $data->pono }}</a></td>
                                <td class="text-center">{{ $data->status_name }}</td>
                                <td class="text-center">{{ $data->cancel_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script>
    $(function() {
            TablesDatatables.init();
        });
        App.datatables();
    $('#prcr_mm501_del-datatable').dataTable({
            order: [
                [4, "desc"],
                [0, "desc"]
            ],
            columnDefs: [],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, 50, -1],
                [10, 20, 30, 50, 'All']
            ]
        });
    
</script>
@endsection



