<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Report\EpApp;

use Carbon\Carbon;
use DB;
use Log;
use Excel;
use Mail;
use Config;
use App\Migrate\MigrateUtils;


class ActionEpAdminReport {
    
    public static function run() {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::generateReportExcel();
        //self::export();

        //$pathExcel = storage_path('app/exports/epapp/ep-admin').'/LaporanTindakanEpAdmin-02052024.xlsx';
        //self::sendEmail($pathExcel,2024);
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }
 
    public static function getFirstDayOfYear() {
        // Get yesterday's date
        $yesterday = Carbon::yesterday();

        // Set the date to yesterday and change the year to the current year
        return $yesterday->startOfYear()->format('Y-m-d');
    }

    protected static function generateReportExcel() {
        $yearReport = Carbon::yesterday()->year;
        $fileName = 'LaporanTindakanEpAdmin-'.Carbon::yesterday()->format('dmY');
        MigrateUtils::logDump('filename : '.$fileName);
        Excel::create($fileName, function($excel) use ($fileName) {
            $excel->setTitle($fileName);
            
            $sheetName = 'TindakanEpAdmin-'.self::getFirstDayOfYear();
            $excel->sheet($sheetName, function($sheet) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Tw Cen MT',
                        'size' => 12,
                        'bold' => false
                    )
                ));
                
                // https://laravel-excel.maatwebsite.nl/docs/2.1/export/autosize
                $sheet->setColumnFormat(array(
                    'A' => '@','B' => '@','C' => '@',
                    'D' => '@','E' => '@','F' => 'd/m/yyyy h:mm',
                    'G' => '@',
                ));
                  
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
//                    $row->setBackground('#684E49');
                });
                
                // Disable auto size for columns
                $sheet->setAutoSize(array(
                    'D'
                ));

                $sheet->cells('A1:G1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
//                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Tw Cen MT');

                    // Set font size
                    $cells->setFontSize(12);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });
                //$sheet->setSize('A' . $intRowNumber, 25, 18);
                
                $sheet->row(1, array(
                    'eP Admin',	
                    'Module / Document',
                    'Document Number',	
                    'Action Desc',	
                    'Actioned By ID',	
                    'Action Date',	
                    'Document Status'
                ));
                
                $count = 2;
                $dtStartTimeOP = Carbon::now();
                $firstResults = self::queryReport(self::getFirstDayOfYear());

                foreach ($firstResults as $obj) {
    
                    $sheet->row($count, array(
                            $obj->ep_admin,
                            $obj->module,
                            $obj->document_number,
                            $obj->action_desc,
                            $obj->actioned_by_id,
                            $obj->action_date,
                            $obj->document_status
                        )
                    );
                    $count++;
                }
                
                $takentime = array(
                    'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
                    'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
                );
                MigrateUtils::logDump(__METHOD__.'    :: Timetaken >> Time   :   '.json_encode($takentime));

            });         
            
         })->store('xlsx', storage_path('app/exports/epapp/ep-admin'));
         
        $dataReport = collect([]);
        $dataReport->put("date_from", self::getFirstDayOfYear());
        $dataReport->put("report_name",'Laporan Tindakan Ep Admin');
        $dataReport->put("report_path",storage_path('app/exports/epapp/ep-admin').'/'.$fileName.'.xlsx');
        self::sendEmail($dataReport->get('report_path'),$yearReport);
           
    }

    /**
     * 4/4/2025 add new user Pn Natrah , Pn Zariana
     */
    protected static function queryReport($date){   
        $result = DB::connection('oracle_nextgen_rpt')->select("SELECT epa.user_name as ep_admin,
                DECODE (ptd.doc_type,
                        'SR', 'Permohonan Pembekal',
                        'RN', 'Nota Minta',
                        'FN', 'Nota Penerimaan Bekalan/Perkhidmatan',
                        'UP', 'Profil Pengguna',
                        'PR', 'Permintaan Pembelian',
                        'DOC', 'Dokumen Sokongan & Senarai Semak',
                        'UOM', 'Unit Ukuran',
                        'OP', 'Profil Organisasi',
                        'FA', 'Pindaan dengan Kontrak Tambahan',
                        'MR', 'Akses Kajian Pasaran',
                        'FIN', 'Organisasi Kewangan',
                        'QT', 'Sebut Harga/Tender',
                        'USG', 'Kumpulan Pengguna',
                        'QTV', 'Nilai Sebut harga dengan tender'
                    ) as module,
                ptd.doc_no as document_number, 
                ptd.action_desc as action_desc,
                ptd.actioned_by as actioned_by_id,
                TO_CHAR (ptd.actioned_date, 'dd/mm/yyyy hh24:mi:ss') as action_date,
                -- ptd.actioned_date as action_date,
                psd.status_name as document_status
        FROM pm_tracking_diary ptd,
                pm_user epa,
                pm_user_org puo,
                pm_user_role pur,
                pm_status_desc psd
        WHERE epa.user_id = puo.user_id
            AND ptd.actioned_by = epa.user_id
            AND puo.user_org_id = pur.user_org_id
            --AND pu.created_by = 419544
            AND ptd.status_id = psd.status_id
            AND psd.language_code = 'ms'
            AND psd.record_status = 1
            AND org_profile_id = 1
            AND epa.record_status = 1
            AND puo.record_status = 1
            AND pur.record_status = 1
            -- AND ptd.ACTIONED_BY IN (1, 419544, 724281, 451702, 420264, 54299, 420126, 683330, 631501)
            AND ptd.ACTIONED_BY IN (1, 419544, 724281, 420264, 683330, 631501, 534223)
            AND ptd.actioned_date >= TO_DATE(?,'YYYY-MM-DD')
            AND pur.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2')
            AND ptd.role_code IN ('EP_ADMIN', 'EP_ADMIN_1', 'EP_ADMIN_2') 
            ",
            array($date));

        return $result ;
    }

    protected static function sendEmail($pathAttachedReport,$year) {
        MigrateUtils::logDump(__METHOD__ . ' starting ..');

        $dataSend = array(
            "to" => [
                    // '<EMAIL>',
                    '<EMAIL>',
                    '<EMAIL>','<EMAIL>'
                    //'<EMAIL>','<EMAIL>'
                ],
            "subject" => 'Laporan Tindakan eP Admin '.$year
        );
        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mailer
                ->send('emails.reportActionEpAdmin', ['data' => $dataSend, 'year' => $year], function ($m) use ($dataSend,$pathAttachedReport) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($dataSend["to"]);
                    $m->attach($pathAttachedReport);
                    $m->subject($dataSend["subject"]);
                });
            Log::info(__CLASS__.' >>  '.__FUNCTION__. 'success send email : '.json_encode($dataSend["to"]));
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }

         
    }

}
