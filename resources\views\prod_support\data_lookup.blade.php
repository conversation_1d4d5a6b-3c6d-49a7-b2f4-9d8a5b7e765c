@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li>
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li  class="active  @if(!Auth::user()->isPatcherRolesEp()) hide @endif ">
                <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div>

<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> DATA LOOKUP </strong></h1> 
    </div>   
    <div class="form-group">
        <button type="button" class="btn btn-sm btn-primary add-new-lookup"><i class="fa fa-save"></i> Add New Lookup</button>
    </div>
    <form class="form-horizontal form-bordered insert-lookup-form" id="insert-lookup-form" style="display:none" action="{{url('/prod-support/add-data-lookup/create')}}" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading add-new-port">
            <input type="hidden" id="id" name="id" value="" class="form-control" style="width: 100px;">
            <label class="col-md-1 text-left" for="code">CODE<span class="text-danger">*</span></label>
            <div class="col-md-1 type">
                <input type="text" id="code" name="code" value="" required class="form-control" style="width: 100px;">
            </div>
            <label class="col-md-1 text-left" for="name">Name<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="name" name="name" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="description">Description<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="description" name="description" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="grouptype">Group Type<span class="text-danger">*</span></label>
            <div class="col-md-3 type">
                <select id="grouptype" name = "grouptype" class="form-control" style="width: 700px;">
                    @foreach($getDistinctGroupType  as $key)
                    <option value="{{ $key->group_type }}">{{ $key->group_type }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="pull-right">
                <button type = "submit" class="btn btn-sm btn-primary save-patch"><i class="fa fa-save"></i> Save</button>
                <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
            </div>
        </div>
    </form>
    <form class="form-horizontal form-bordered edit-lookup-form" id="edit-lookup-form" style="display:none" action="/prod-support/edit-data-lookup" method="post">
        {{ csrf_field() }}
        <div class="form-group panel-heading add-new-port">
            <input type="hidden" id="editid" name="editid" value="" class="form-control" style="width: 100px;">
            <label class="col-md-1 text-left" for="code">CODE<span class="text-danger">*</span></label>
            <div class="col-md-1 type">
                <input type="text" id="editcode" name="editcode" value="" required class="form-control" style="width: 100px;">
            </div>
            <label class="col-md-1 text-left" for="name">Name<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="editname" name="editname" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="description">Description<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <input type="text" id="editdescription" name="editdescription" value="" required class="form-control" style="width: 700px;">
            </div>
            <label class="col-md-1 text-left" for="grouptype">Group Type<span class="text-danger">*</span></label>
            <div class="col-md-3 type">
                <select id="editgrouptype" name = "editgrouptype" class="form-control" style="width: 700px;">
                    @foreach($getDistinctGroupType  as $key)
                    <option value="{{ $key->group_type }}">{{ $key->group_type }}</option>
                    @endforeach
                </select>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="pull-right">
                <button type = "submit" class="btn btn-sm btn-primary save-edit"><i class="fa fa-save"></i> Save</button>
                <button type = "reset" class="btn btn-sm btn-warning resetbutton" value="reset"><i class="fa fa-repeat"></i> Reset</button>
            </div>
        </div>
    </form>
    <div class="table-responsive">
        <table id="datalookup-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">NO</th>
                    <th class="text-center">CODE</th>
                    <th class="text-center">NAME</th>
                    <th class="text-center">DESCRIPTION</th>
                    <th class="text-center">GROUP TYPE</th>
                    <th class="text-center">CREATED BY</th>
                    <th class="text-center">CREATED DATE</th>
                    <th class="text-center">CHANGED BY</th>
                    <th class="text-center">CHANGED DATE</th>
                    <th class="text-center">ACTION</th>
                </tr>                
            </thead>    
            <tbody>
                @if($getLookupdate != null)
                @foreach($getLookupdate as $rowData => $data)
                <tr>
                    <td class="text-center">{{ ++$rowData }}</td>
                    <td class="text-center">{{ $data->code }}</td>
                    <td class="text-center">{{ $data->name }}</td>
                    <td class="text-center">{{ $data->description }}</td>
                    <td class="text-center">{{ $data->group_type }}</td>
                    <td class="text-center">{{ $data->created_by }}</td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center">{{ $data->changed_by }}</td>
                    <td class="text-center">{{ $data->changed_date }}</td>
                    <td class="text-center" action_table_task>
                        <div class="btn-group btn-group-xs">
                            <a idno ="{{$data->id}}" 
                               co ="{{ $data->code }}"
                               na ="{{ $data->name }}"
                               de ="{{ $data->description }}"
                               gr ="{{ $data->group_type }}"
                               data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                            <a idnom ="{{$data->id}}" 
                               data-toggle="tooltip" title="Delete" class="btn btn-sm btn-danger delete_data"><i class="fa fa-times"></i></a>
                        </div>
                    </td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>

        <input type="hidden" id="delid" name="delid" value="" class="form-control" style="width: 100px;">
        <div id="modal_confirm_delete_data" class="modal fade">
            <div class="modal-dialog modal-sm">
                <div class="modal-content">
                    <div class="modal-header text-center">
                        <h5> CONFIRMATION</h5>
                    </div>
                    <div class="modal-body text-center">
                        <label>Are You Sure To Delete Data Lookup? </label> &nbsp;&nbsp;&nbsp;
                    </div> 
                    <br/><br/>
                    <div class="modal-footer">
                        <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                        <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</DIV> 

@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    $('#to-top').click();
        TablesDatatables.init();
    });
    App.datatables();
    

    $(".add-new-lookup").on("click", function () {
        $("#insert-lookup-form").show();
        $("#id").val("");
        $("#code").val("");
        $("#name").val("");
        $("#description").val("");
        $("#grouptype").val("");
        $("#edit-lookup-form").hide();
    });

    $(".resetbutton").on("click", function () {
        $("#editid").val("");
        $("#editcode").val("");
        $("#editname").val("");
        $("#editdescription").val("");
        $("#editgrouptype").val("");
        $("#edit-lookup-form").hide();
        $("#insert-lookup-form").hide();
    });

    $(".editbutton").on("click", function () {
        $('#to-top').click();
        console.log('try');
        $("#insert-lookup-form").hide();
        $("#edit-lookup-form").show();
        let id = $(this).attr('idno');
        let
        cod = $(this).attr('co');
        let
        nam = $(this).attr('na');
        let
        des = $(this).attr('de');
        let
        grop = $(this).attr('gr');
        $('#editid').val(id);
        $('#editcode').val(cod);
        $("#editname").val(nam);
        $("#editdescription").val(des);
        $("#editgrouptype").val(grop);
        console.log(id);
    });
    
    $(".delete_data").on("click", function () {
        $("#modal_confirm_delete_data").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
        console.log(delid);
    });

    $('#submit_confirm_delete').on('click', function () {
        $('#modal_confirm_delete_data').modal('hide');
        $Id = $("#delid").val();
        console.log($Id);
        $.ajax({
            type: "GET",
            url: "/prod-support/delete-data-lookup/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });
</script>
@endsection        

