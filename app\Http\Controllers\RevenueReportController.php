<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\RevenueService;
use DB;
use Carbon\Carbon;
use Auth;


class RevenueReportController extends Controller {
    use RevenueService;
    use SupplierService;
    

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function showRevenueDailySummary(){
        $listData = null;
        return view('report.revenue.revenue_daily_summary_stat', [
            'dataResult' => $listData
                ]);
    }
    public function getDataHtmlRevenueDailySummary(){
        $group = request()->group;
        $type = request()->type;
        $dateSearch = request()->date_search;
        
        $title = '';
        if(strlen($group)!= 2 || strlen($type) < 2 || strlen($dateSearch) != 10){
            return "Invalid Request";
        }
        $listPvDaily = array(); 
        if($group == 'GM' ){
            if($type == 'PV'){
                $title = 'PROCUREMENT VALUE (PV)';
                $listPvDaily = $this->cmsListSummaryPVDailyByDate($dateSearch);
            }else if($type == 'TR'){
                $title = 'TRANSACTION REVENUE (TR)';
                $listPvDaily = $this->cmsListSummaryTRDailyByDate($dateSearch);
            }    
        }else if($group == 'SM' ){
            if($type == 'SR'){
                $title = 'SUPPLIER REVENUE COLLECTION (SR)';
                $listPvDaily = $this->cmsListSummarySmSRDailyByDate($dateSearch);
            }else if($type == 'REGISTRATION'){
                $title = 'REGISTRATION COLLECTION (MOF)';
                $listPvDaily = $this->cmsListSummarySmRegistrationDailyByDate($dateSearch);
            }else if($type == 'TRAINING'){
                $title = 'TRAINING - eP TRAINING';
                $listPvDaily = $this->cmsListSummarySmTrainingDailyByDate($dateSearch);
            }else if($type == 'SOFTCERT'){
                $title = 'SOFTCERT COLLECTION';
                $listPvDaily = $this->cmsListSummarySmSoftcertDailyByDate($dateSearch);
            }else if($type == 'MEMBERSHIP'){
                $title = 'TRAINING - ELITE MEMBERSHIP';
                $listPvDaily = $this->cmsListSummarySmMembershipDailyByDate($dateSearch);
            }    
        }
        
        
        $html = '<ul class="nav nav-pills nav-stacked">'
                .   '<li class="active">
                        <a href="javascript:void(0)">'.$title.'</a>
                    </li>
                    <li>
                        <hr>
                    </li>';
        
        foreach ($listPvDaily as $row){
            $labelCss = "label-info";
            $total = $row->total;
            if($group == 'SM' ){
                $total = number_format($row->total,2);
                if($total < 0){
                    $labelCss = "label-danger";
                }
            }else if($group == 'GM' ){
                //GM query return result as String. short form number.
                if(str_contains($total, '-')){
                   $labelCss = "label-danger"; 
                }
            }   
            $html =  $html.''
                   .'<li>
                       <a href="javascript:void(0)">'.$row->name.''
                   . ' <span class="'.$labelCss.' badge pull-right" style="font-weight:bolder !important;font-size: 100% !important;">'.$total.'</span></a>
                    </li>';
        }
        $html = $html.'</ul>';
        
        return $html;
    }
    
    function isHighest($array){
        if(is_array($array)){
            foreach($array as $key => $value){
                $array[$key] = $this->isHighest($value);
            }
            return max($array);

        }else{
            return $array;
        }
    }

    function isHighestMonth($arr, $val){
        foreach($arr as $item){
            if ($item[1] == $val)
                return $item[0];
        }
        return "-";
    }

    public function showPaymentStatRoute(){
        // set default to current year.
        $nowYear = Carbon::now()->year;
        return $this->showPaymentStat($nowYear);
    }

    public function showPaymentStat($year){
        $dataList = collect([]);
        $summaryPvTr = collect([]);
        
        $paymentPercentage = $this->getPaymentStatPercentage($year);
        $pvPercentage = $this->getAmountStatPV($year);
        $paymentStatInfo = $this->getPaymentStatInfo($year);

        $dataSumPvAmount = array();
        $countPv = 1;
        $sumPvAll = 0;
        $currentPvEarn = 0;
        $highestPvEarnMth = '';
        $highestPvEarnAmount = '';
        if($pvPercentage) {
            foreach ($pvPercentage as $data) {
                $dataList->push($data) ;
                array_push($dataSumPvAmount, [$countPv, $data->sum_amount]);
    
                $sumPvAll += $data->sum_amount;
                $currentPvEarn = $data->sum_amount;
                
                if($currentPvEarn > $highestPvEarnAmount){
                    $highestPvEarnAmount = $currentPvEarn;
                    $highestPvEarnMth = $data->month;
                }
                
                $countPv++;
            }
            $highestPvEarnAmt = number_format($highestPvEarnAmount,2);

            $summaryPvTr->put('summ_pv_all',number_format($sumPvAll,2) );
            $summaryPvTr->put('current_pv_earn',number_format($currentPvEarn,2) );
            $summaryPvTr->put('highest_pv_earn_amount', $highestPvEarnAmt);
            $summaryPvTr->put('highest_pv_earn_month',$highestPvEarnMth );
        }
        
        
        $dataSumAmount = array();
        $count = 1;
        $sumAll = 0;
        $currentEarn = 0;
        $highestEarnMth = '';
        $highestEarnAmount = '';
        if($paymentPercentage) {
            foreach ($paymentPercentage as $data) {
                $dataList->push($data) ;
                array_push($dataSumAmount, [$count, $data->sum_amount]);
    
                $sumAll += $data->sum_amount;
                $currentEarn = $data->sum_amount;
                
                if($currentEarn > $highestEarnAmount){
                    $highestEarnAmount = $currentEarn;
                    $highestEarnMth = $data->month;
                }
                
                $count++;
            }
            $highestEarnAmt = number_format($highestEarnAmount,2);
            $summaryPvTr->put('summ_all',number_format($sumAll,2) );
            $summaryPvTr->put('current_earn',number_format($currentEarn,2) );
            $summaryPvTr->put('highest_earn_amount',$highestEarnAmt );
            $summaryPvTr->put('highest_earn_month',$highestEarnMth );
        }
        
        

        $countInfo = 0;
        $statProcessFeeAmt = array();
        $statRegisterFeeAmt = array();
        $statSoftcertFeeAmt = array();

        $currentMonth = date('m');
        $currentMonthAmt = 0;
        $statSumAll = 0;
        $statProcessTotal = 0;
        $statRegisterTotal = 0;
        $statSoftcertTotal = 0;
        $stathighestMth = '';
        $stathighestAmount = '';
        
        foreach ($paymentStatInfo as $pInfo) {
            if($pInfo->bill_type === 'Processing Fee') {
                $statProcessTotal += $pInfo->total_amt;
                array_push($statProcessFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            } elseif($pInfo->bill_type === 'Registration Fee') {
                $statRegisterTotal += $pInfo->total_amt;
                array_push($statRegisterFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            } elseif($pInfo->bill_type === 'Softcert Fee') {
                $statSoftcertTotal += $pInfo->total_amt;
                array_push($statSoftcertFeeAmt, [$pInfo->month, $pInfo->total_amt]);
            }

            $statSumAll += $pInfo->total_amt;

            if($pInfo->month == $currentMonth){
                $currentMonthAmt += $pInfo->total_amt;
            }

            $countInfo++;
        }

        $paymentStatInfo[0]->current_month_total = $currentMonthAmt;
        $paymentStatInfo[0]->sum_process = $statProcessTotal;
        $paymentStatInfo[0]->sum_register = $statRegisterTotal;
        $paymentStatInfo[0]->sum_softcert = $statSoftcertTotal;
        $paymentStatInfo[0]->sum_all = $statSumAll;

        // dd($paymentStatInfo);

        $dataResult = [
            'summaryPvTr' => $summaryPvTr,
            'dataList' => $dataList,
            'dataStats' => $paymentStatInfo,
            'dataSumPvAmount' => $dataSumPvAmount,
            'dataSumAmount' => $dataSumAmount,
            'statProcessFeeAmt' => $statProcessFeeAmt,
            'statRegisterFeeAmt' => $statRegisterFeeAmt,
            'statSoftcertFeeAmt' => $statSoftcertFeeAmt,
            'dataYear' => $year
        ];
        //dump($dataResult);
        return view('report.revenue.revenue_accumulative_stat',$dataResult );
    }

    /**
     * @deprecated since not use since 2019
     */
    public function listPendingPaymentSupplier(){
        
        $listData = $this->getListPendingPaymentSuppliers();
        
        //$list = $listData->whereIn('order_id',$listOrderIdFailed)->all();
        return view('list_pending_payment', [
            'listdata' => $listData,
            'listXml' => null,
            'carian' => '']);
    }
    
    /**
     * @deprecated since not use since 2019
     */
    public function searchPendingPaymentSupplier($search){
        $list = $this->getListPendingPaymentSuppliers();
        return view('list_pending_payment', [
            'listdata' => $list,
            'carian' => $search]);
    }
    
    /**
     * @deprecated since not use since 2019
     */
    public function removePendingPaymentSupplier($orderID){
        $data = $this->getPendingPaymentSuppliersByOrderID($orderID); 
        if($data && $data->order_id != null){
            $check = DB::connection('mysql_ep_support')->table('ep_payment_failed')
                    ->where('order_id',$orderID)->count();
            if($check == 0){
                DB::connection('mysql_ep_support')
                    ->insert('insert into ep_payment_failed 
                        (   supplier_id,company_name,ep_no,
                            order_id,payment_amt,bill_no,bill_type,bill_date,
                            bill_ref_id,payment_due_date,payment_created,
                            payment_gateway,created_at,created_by) 
                        values (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)', 
                        [   
                            $data->supplier_id,
                            $data->company_name,
                            $data->ep_no,
                            $data->order_id,
                            $data->payment_amt,
                            $data->bill_no,
                            $data->bill_type,
                            Carbon::parse($data->bill_date),
                            $data->bill_ref_id,
                            Carbon::parse($data->payment_due_date),
                            Carbon::parse($data->created_date),
                            $data->payment_gateway,
                            Carbon::now(),
                            Auth::user()->user_name  ]);
                return array('status'=>'00','desc'=>'success inserted');
            }else{
                return array('status'=>'99','desc'=>'record existed');
            } 
        }
        return array('status'=>'99','desc'=>'failed inserted');
    }

    public function showPendingTransactionStatistic(){
        $listData = $this->cmsListPendingTransactionStatistic(Carbon::yesterday()->year);
        return view('report.revenue.revenue_pending_transaction_stat', [
            'listDataResults' => $listData
                ]);
    }

    public function showStatPVTRDailyToday(){
        $dateRequest = Carbon::now();
        $listData = $this->getStatPVTRDailyToday();
        return view('report.revenue.daily_transaction_stat', [
            'dataResult' => $listData,
            'dateRequest' =>$dateRequest
                ]);
    }
    
}
