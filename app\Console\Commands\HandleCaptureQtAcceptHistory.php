<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Config;
use Carbon\Carbon;

class HandleCaptureQtAcceptHistory extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qt:capture-accept-history {--today-only : Import only today\'s records}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Capture QT Accept History (all records by default) and insert/update into MySQL';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $startTime = microtime(true);
        $isTodayOnly = $this->option('today-only');

        Log::info('QT Accept History command started', [
            'command' => 'qt:capture-accept-history',
            'today_only' => $isTodayOnly
        ]);

        if ($isTodayOnly) {
            $this->info('Fetching QT Accept History for today only...');
        } else {
            $this->info('Fetching all QT Accept History records...');
        }

        try {
            Log::info('Building SQL query for QT Accept History');

            // Build the SQL query conditionally
            $sql = "
                SELECT
                /* info history detail and task */
                h.createddate, h.updateddate, h.assigneddate, h.assignees, h.acquiredby, h.approvers,
                h.outcome, h.state, h.substate, h.taskid, h.tasknumber, h.updatedby, h.version, h.versionreason, h.expirationdate,
                /* info parent */
                h.compositename, h.compositeversion, h.taskdefinitionname, h.customattributenumber2, h.componentname,
                h.customattributestring1, h.customattributestring2, h.flow_id, h.compositeinstanceid, h.compositedn,
                h.processname, h.enddate, h.ecid
                FROM wftaskhistory h
                WHERE h.compositename = 'SourcingQT'
                AND h.componentname = 'AcceptQTChange'
                AND h.state != 'OUTCOME_UPDATED'";

            if ($isTodayOnly) {
                $sql .= " AND trunc(h.updateddate) = trunc(sysdate)";
            }

            $sql .= " ORDER BY h.taskid, h.version";

            Log::info('Executing Oracle query for QT Accept History');
            $results = DB::connection('oracle_bpm_rpt')->select($sql);
            Log::info('Oracle query executed successfully', ['record_count' => count($results)]);

            if (empty($results)) {
                if ($isTodayOnly) {
                    $this->info('No records found for today.');
                    Log::info('No records found for today');
                } else {
                    $this->info('No records found.');
                    Log::info('No records found');
                }

                // Send success email even if no records found
                $this->sendSuccessEmail(0, 0, $startTime);
                return 0;
            }

            $this->info('Found ' . count($results) . ' records. Processing insert/update into MySQL...');
            Log::info('Starting MySQL insert/update process', ['record_count' => count($results)]);

            $processedCount = 0;
            $mysqlConnection = DB::connection('mysql_ep_support');

            foreach ($results as $row) {
                try {
                    $mysqlConnection->table('ep_qt_accept_history')->updateOrInsert(
                        [
                            'taskid' => $row->taskid,
                            'version' => $row->version,
                        ],
                        [
                            'createddate' => $row->createddate,
                            'updateddate' => $row->updateddate,
                            'assigneddate' => $row->assigneddate,
                            'assignees' => $row->assignees,
                            'acquiredby' => $row->acquiredby,
                            'approvers' => $row->approvers,
                            'outcome' => $row->outcome,
                            'state' => $row->state,
                            'substate' => $row->substate,
                            'tasknumber' => $row->tasknumber,
                            'updatedby' => $row->updatedby,
                            'versionreason' => $row->versionreason,
                            'expirationdate' => $row->expirationdate,
                            'compositename' => $row->compositename,
                            'compositeversion' => $row->compositeversion,
                            'taskdefinitionname' => $row->taskdefinitionname,
                            'customattributenumber2' => $row->customattributenumber2,
                            'componentname' => $row->componentname,
                            'customattributestring1' => $row->customattributestring1,
                            'customattributestring2' => $row->customattributestring2,
                            'flow_id' => $row->flow_id,
                            'compositeinstanceid' => $row->compositeinstanceid,
                            'compositedn' => $row->compositedn,
                            'processname' => $row->processname,
                            'enddate' => $row->enddate,
                            'ecid' => $row->ecid,
                            'log_timestamp' => Carbon::now(),
                        ]
                    );

                    $processedCount++;

                } catch (\Exception $insertError) {
                    $this->error("Failed to process record with taskid {$row->taskid}: " . $insertError->getMessage());
                    Log::error('Failed to process QT Accept History record', [
                        'taskid' => $row->taskid,
                        'error' => $insertError->getMessage()
                    ]);
                    continue;
                }
            }

            $this->info("Successfully processed {$processedCount} out of " . count($results) . " records.");
            Log::info('MySQL insert/update process completed', [
                'processed_count' => $processedCount,
                'total_records' => count($results)
            ]);

            // Send success email
            $this->sendSuccessEmail(count($results), $processedCount, $startTime);

        } catch (\Exception $e) {
            $this->error('Error executing command: ' . $e->getMessage());
            Log::error('QT Accept History command failed', [
                'error' => $e->getMessage(),
                'command' => 'qt:capture-accept-history'
            ]);

            // Send failure email
            $this->sendFailureEmail($e->getMessage());
            return 1;
        }

        $this->info('Command completed successfully.');
        Log::info('QT Accept History command completed successfully');
        return 0;
    }

    /**
     * Send email notification
     */
    private function sendEmail($dataEmail)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            // "cc" => ['<EMAIL>'],
            "subject" => 'Server (' . env('APP_ENV') . ') > ' . $dataEmail['email_subject']
        );

        try {
            Mail::send('emails.qtAcceptHistoryMail', $dataEmail, function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                // ->cc($data["cc"])
                ->subject($data["subject"]);
            });

            Log::info('QT Accept History email sent successfully', ['to' => $data["to"]]);
        } catch (\Exception $e) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

    /**
     * Send success email notification
     */
    private function sendSuccessEmail($totalRecords, $processedCount, $startTime)
    {
        $executionTime = round(microtime(true) - $startTime, 2) . ' seconds';

        $dataEmail = [
            'email_subject' => 'QT Accept History Import - Success',
            'is_success' => true,
            'total_records' => $totalRecords,
            'processed_count' => $processedCount,
            'execution_time' => $executionTime,
            'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
            'server_env' => env('APP_ENV'),
            'success_rate' => $totalRecords > 0 ? round(($processedCount / $totalRecords) * 100, 2) : 100
        ];

        $this->sendEmail($dataEmail);
    }

    /**
     * Send failure email notification
     */
    private function sendFailureEmail($errorMessage)
    {
        $dataEmail = [
            'email_subject' => 'QT Accept History Import - Failed',
            'is_success' => false,
            'error_message' => $errorMessage,
            'command' => 'qt:capture-accept-history',
            'timestamp' => Carbon::now()->format('Y-m-d H:i:s'),
            'server_env' => env('APP_ENV')
        ];

        $this->sendEmail($dataEmail);
    }
}