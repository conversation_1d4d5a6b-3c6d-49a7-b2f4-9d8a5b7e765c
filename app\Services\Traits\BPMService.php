<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Exception;

trait BPMService {


  /** Just get first records only.  */
  protected function getTaskBpmByInstanceId($instanceID){
    $query = DB::connection('oracle_bpm_rpt')->table('WFTASK');
    $query->where('COMPOSITEINSTANCEID', $instanceID);
    $query->orderBy('CREATEDDATE','DESC');
    $data = $query->first();
    return $data;
  }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskBpmByDocNo($docNo){
        
        $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK'));
        
//        if(Auth::user()->user_name == 'mohdshamsul' 
//                || Auth::user()->user_name || 'shahril' 
//                || Auth::user()->user_name || 'moriana'){
//           $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK@PRDSOA')); 
//        }

        $query->where('CUSTOMATTRIBUTESTRING1', $docNo);
        $query->select('CREATEDDATE', 'CUSTOMATTRIBUTESTRING1',
                        'CUSTOMATTRIBUTESTRING1 as DOC_NO','CUSTOMATTRIBUTESTRING2 as DOC_TYPE',
                        'CUSTOMATTRIBUTENUMBER1 AS DOC_ID','CUSTOMATTRIBUTENUMBER2 AS DOC_STATUS_ID',
                        'USERCOMMENT','ACTIVITYNAME','PROCESSNAME','FLOW_ID',
                        'ACQUIREDBY','ASSIGNEES','CREATOR','INSTANCEID','COMPOSITEINSTANCEID','COMPOSITENAME','COMPOSITEVERSION',
                        'STATE','VERSIONREASON','TASKDEFINITIONID','TASKNUMBER');
        $query->orderBy('CREATEDDATE','DESC');
        $data = $query->get();
        return $data;
    }
    
    protected function getTaskDetailBpmByDocNo($docNo,$compositeName=null){
        try {
            $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK'));
            $query->where('CUSTOMATTRIBUTESTRING1', $docNo);
            if($compositeName != null){
              $query->where('COMPOSITENAME', $compositeName);
            }
            $query->select('CREATEDDATE', 'CUSTOMATTRIBUTESTRING1','USERCOMMENT','ACTIVITYNAME','ACQUIREDBY','ASSIGNEES','CREATOR','INSTANCEID','COMPOSITEINSTANCEID','COMPOSITENAME','STATE','VERSIONREASON','TASKDEFINITIONID','FLOW_ID');
            $query->orderBy('CREATEDDATE','DESC');
            return $query->first(); 
        } catch (Exception $ex) {
            return null;
        }
    }
    
    protected function getSQTaskSubmitSimpleQuote(){
        
        $query = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/SourcingDP!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/SourcingDP!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
            and c.composite_dn like 'default/SourcingDP!1.0.%'
          and i.state = 1
          and i.component_name in ('StartSimpleQuoteCreation')
          and w.state = 'ASSIGNED'
          and w.activityname = 'Submit Simple Quote'
          order by i.creation_date");
        return $query->get();
    }
    
    protected function getInstanceIdBPMbyDocId($docId){
        $docIdSearch = '%'.$docId.'%';
        $result = DB::connection('oracle_bpm_rpt')->select(""
                . "select distinct
                            (select distinct CMPST_ID from CUBE_INSTANCE ci where ci.cikey = ds.cikey) as composite_instance_id,
                                     ds.domain_name||'/' || ds.Composite_Name || '!' || ds.composite_revision as composite,
                   ds.cikey,ds.composite_name,to_char(ds.SUBSCRIPTION_DATE,'YYYY-MM-DD HH24:MI:SS') as subscription_date  , ds.state  , ds.component_name, ds.domain_name
                 from Dlv_Subscription ds
                  where
                                            ds.properties like ? 
                                            -- and ds.Composite_Name = 'Fulfilment'
                    --and ds.state in (0,2,3) 
        ", array($docIdSearch));
        return $result;
    }

    protected function getInstanceIdBPMbyDocIdModule($docId,$module){
      $docIdSearch = '%'.$docId.'%';
      $result = DB::connection('oracle_bpm_rpt')->select(""
              . "select distinct
                          (select distinct CMPST_ID from CUBE_INSTANCE ci where ci.cikey = ds.cikey) as composite_instance_id,
                          ds.domain_name||'/' || ds.Composite_Name || '!' || ds.composite_revision as composite
               from Dlv_Subscription ds where ds.properties like ? and ds.Composite_Name = '$module'
                  --and ds.state in (0,2,3) 
      ", array($docIdSearch));
      return $result;
  }

    /**
     * return list
     */
    protected function findDlvSubcscriptionCorrelation($compositeInstanceId){
      $result = DB::connection('oracle_bpm_rpt')->select("SELECT 
      d.CIKEY ,d.PARTNER_LINK ,d.OPERATION_NAME ,d.COMPOSITE_NAME ,d.COMPONENT_NAME ,d.COMPOSITE_REVISION ,d.STATE,
      SUBSTR(properties, 
        INSTR(properties, '{http://xmlns.oracle.com/bpmn/bpmnProcess/') + LENGTH('{http://xmlns.oracle.com/bpmn/bpmnProcess/'), 
        INSTR(properties, '=') - INSTR(properties, '{http://xmlns.oracle.com/bpmn/bpmnProcess/') - LENGTH('{http://xmlns.oracle.com/bpmn/bpmnProcess/') 
        ) AS module_doc_type,
      CASE  
        WHEN SUBSTR(REGEXP_SUBSTR(properties, 'id=[a-zA-Z0-9]+'), 1) IS NOT NULL THEN SUBSTR(REGEXP_SUBSTR(properties, 'id=[a-zA-Z0-9]+'), 1)
        WHEN SUBSTR(REGEXP_SUBSTR(properties, 'Id=[a-zA-Z0-9]+'), 1)  IS NOT NULL THEN SUBSTR(REGEXP_SUBSTR(properties, 'Id=[a-zA-Z0-9]+'), 1) 
        WHEN SUBSTR(REGEXP_SUBSTR(properties, 'ID=[a-zA-Z0-9]+'), 1)  IS NOT NULL THEN SUBSTR(REGEXP_SUBSTR(properties, 'ID=[a-zA-Z0-9]+'), 1) 
        ELSE  'NOT FOUND'
      END AS DOC_ID,
      properties
      FROM DLV_SUBSCRIPTION d 
      WHERE 
      d.PROPERTIES  LIKE '%correlation%'
      AND  cikey IN (SELECT cikey FROM cube_instance WHERE CMPST_ID  = ? )", array($compositeInstanceId));
      return $result;
  }



    
    protected function getTaskAssignedPR($docNo) {
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Order!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Order!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Order!1.0.%'
          and i.state = 1
          and i.component_name = 'PurchaseRequestInitiation'
          and w.activityname = 'Submit PR'
          and w.state = 'ASSIGNED'
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date
        ", array($docNo));
        return $result;
    }

    protected function getTaskAssignedCR($docNo) {
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Order!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Order!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Order!1.0.%'
          and i.state = 1
          and i.component_name IN ('PHISContractRequestInitiation','ContractRequestInitiation')
          and w.state = 'ASSIGNED'
          and w.activityname IN ('Initiate CR','Initiate Phis CR')
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date
        ", array($docNo));
        return $result;
    }
    
    protected function getTaskAssignedDOFN($docNo) {
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Fulfilment!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Fulfilment!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Fulfilment!1.0.%'
          and i.state = 1
          and w.state = 'ASSIGNED'
          and i.component_name = 'DeliveryOrderFulfilment'
          and w.activityname = 'Create FRN'
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date
        ", array($docNo));
        return $result;
    }
    
    protected function getTaskAssignedSubmitInvoice($docNo) {
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Fulfilment!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Fulfilment!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Fulfilment!1.0.%'
          and i.state = 1
          and w.state = 'ASSIGNED'
          and i.component_name = 'InvoiceAndPaymentCreation'
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date
        ", array($docNo));
        return $result;
    }
    
    protected function getTaskAssignedModifyCancelInvoice($docNo){
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Fulfilment!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Fulfilment!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Fulfilment!1.0.%'
           and i.state = 1
           and w.state = 'ASSIGNED'
          and i.component_name = 'InvoiceAndPaymentCreation'
          and w.CUSTOMATTRIBUTESTRING2 = 'IN'
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date",array($docNo));
        return $result;
    }
    
    protected function getTaskAssignedModifyCancelDO($docNo){
        $result = DB::connection('oracle_bpm_rpt')->select("select c.id as composite_id,w.CUSTOMATTRIBUTESTRING1 as doc_no,
            concat('default/Fulfilment!',i.composite_revision) as composite_module,
            concat(concat(c.id,','),concat('default/Fulfilment!',i.composite_revision) ) as comp_id_module,
            c.composite_dn, c.source_name,c.state as composite_state,
            i.cikey as component_instance_id, i.state as component_instant_state,i.componenttype,i.composite_name,i.component_name,i.composite_revision,
            i.creation_date as component_creation_date,
            w.CUSTOMATTRIBUTESTRING2 as doc_type,w.ACTIVITYNAME as activity_name, w.STATE
            
          from
            composite_instance c, cube_instance i , wftask w where
            c.id = i.cmpst_id
            and i.cikey = w.INSTANCEID
           and c.composite_dn like 'default/Fulfilment!1.0.%'
           and i.state = 1
           and w.state = 'ASSIGNED'
          and i.component_name like 'DeliveryOrder%'
          and w.CUSTOMATTRIBUTESTRING2 = 'DO'
          and w.CUSTOMATTRIBUTESTRING1 = ?
          order by i.creation_date",array($docNo));
        return $result;
    }
 
    public function getDocNoByInstanceId($instanceId){
        $result = DB::connection('oracle_bpm_rpt')->select(" select distinct 
            wf.protectedtextattribute1,wf.customattributestring1,wf.componenttype
            FROM wftask wf
            WHERE  wf.compositeinstanceid = ? 
            AND ( CUSTOMATTRIBUTESTRING1 IS NOT NULL OR PROTECTEDTEXTATTRIBUTE1 IS NOT NULL)  ",array($instanceId));
        
        return $result;

    }

    protected function listTaskDetailBpmByInstanceId($instanceId){
      try {
          $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK'));
          $query->where('compositeinstanceid', $instanceId);
          $query->select('TASKID','CREATEDDATE', 'CUSTOMATTRIBUTESTRING1 AS DOC_NO','USERCOMMENT','ACTIVITYNAME','ACTIVITYID','ACQUIREDBY','ASSIGNEES','CREATOR','INSTANCEID','COMPOSITEINSTANCEID','STATE','VERSIONREASON','TASKDEFINITIONID','PROCESSID' ,'PROCESSNAME');
          $query->orderBy('CREATEDDATE','DESC');
          return $query->get(); 
      } catch (Exception $ex) {
          return null;
      }
    }

    protected function listTaskDetailBpmByDocStateActivity($docNo,$state,$activityName){
      try {
          $query = DB::connection('oracle_bpm_rpt')->table(DB::raw('WFTASK'));
          $query->where('CUSTOMATTRIBUTESTRING1', $docNo);
          $query->where('STATE', $state);
          $query->where('ACTIVITYNAME', $activityName);
          $query->select('TASKID','CREATEDDATE', 'ECID','CREATEDDATE','CUSTOMATTRIBUTESTRING1 AS DOC_NO','TASKDEFINITIONNAME',
                        'USERCOMMENT','ACTIVITYNAME','ACTIVITYID','ACQUIREDBY','ASSIGNEES','CREATOR','INSTANCEID','COMPOSITENAME',
                        'COMPOSITEINSTANCEID','STATE','VERSIONREASON','TASKDEFINITIONID','PROCESSID' ,'PROCESSNAME','COMPONENTNAME');
          return $query->get(); 
      } catch (Exception $ex) {
          return null;
      }
    }

    protected function findListStuckInvalidCompletedByComposite($compositeName){
      try {
          $query = DB::connection('oracle_bpm_rpt')->table('WFTASK as wf');
          $query->where('compositename', $compositeName);
          $query->whereNull('state');
          $query->whereNull('outcome');
          $query->whereDate('createddate','>=','2022-01-24');
          $query->whereRaw(" NOT EXISTS (select 1 from wftask s where s.compositeinstanceid = wf.compositeinstanceid and s.state='ASSIGNED' ) ");
          $query->whereRaw(" NOT EXISTS (select 1 from wftask s where s.compositeinstanceid = wf.compositeinstanceid and s.activityname = wf.activityname and s.processname =wf.processname and s.outcome is not null) ");
          $query->whereRaw(" NOT EXISTS (SELECT 1 FROM sca_flow_instance f WHERE f.flow_id = wf.flow_id AND f.ACTIVE_COMPONENT_INSTANCES = 0 ) ");
          $query->whereNotIn('compositeinstanceid',['84716560','84664914']);
          $query->whereNotIn('activityname',['Publish Result Notification','Recommend Result Notification']);
          $query->select('wf.createddate AS created_date',
                  'wf.FLOW_ID',
                  'wf.compositeinstanceid AS composite_instance_id',
                  'wf.tasknumber  AS cube_instance_id',
                  'wf.compositename AS composite_name',
                  'wf.compositeversion AS composite_version',
                  'wf.componentname  AS component_name',
                  'wf.ACTIVITYNAME AS activity_name',
                  'wf.PROCESSNAME AS process_name',
                  'wf.CUSTOMATTRIBUTESTRING1 AS doc_no' ,
                  'wf.state',
                  'wf.outcome');
          return $query->get(); 
      } catch (Exception $ex) {
          return null;
      }
    }
    
    protected function findBpmFlowId($compositeId) {
        try {
            $result = DB::connection('oracle_bpm_rpt')->select(""
                    . "SELECT DISTINCT flow_id FROM cube_instance WHERE CMPST_ID = '$compositeId'");
            return $result;
        } catch (Exception $ex) {
            return null;
        }
    }


  protected function findTaskAssigned12cMigrateInstanceDetail($compositeName,$processName,$activityName) {
      try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
             a.FLOW_ID ,
             a.TASKID ,
             a.COMPOSITEINSTANCEID ,
             a.COMPOSITEVERSION,
             a.ASSIGNEES,                
             a.CUSTOMATTRIBUTESTRING1 AS DOC_NO,
             a.CUSTOMATTRIBUTESTRING2 AS DOC_TYPE,
             a.CUSTOMATTRIBUTENUMBER1 AS DOC_ID,
             a.CUSTOMATTRIBUTENUMBER2 AS DOC_STATUS_ID,
             a.STATE,
             a.COMPOSITEINSTANCEID,
             a.PROCESSID,
             a.PROCESSNAME,
             a.COMPOSITENAME,
             a.ACTIVITYNAME,
             a.CREATEDDATE
            FROM wftask a
            WHERE 
               a.state = 'ASSIGNED'
              AND a.compositename = '$compositeName'
              AND a.PROCESSNAME = '$processName'
              AND a.activityname = '$activityName' 
              AND EXISTS (SELECT 1 FROM sca_flow_instance s WHERE s.FLOW_ID = a.FLOW_ID AND trunc(s.CREATED_TIME) < to_date('2023-01-24','YYYY-MM-DD') )
            ");
          return $result;
      } catch (Exception $ex) {
          return null;
      }
  }

  protected function findListInstanceProfileMgmtRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
                c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
                FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
                c.FLOW_ID = s.FLOW_ID 
                AND s.ACTIVE_COMPONENT_INSTANCES <> 0
                AND COMPOSITE_NAME = 'Profile_Management' 
                AND c.STATE = 1 
                AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME   = c.COMPOSITE_NAME   AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
            ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceCodificationRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0 
            AND c.COMPOSITE_NAME = 'Codification' 
            AND c.STATE NOT in (5,8) 
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME   = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceSupplierMgmtRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
                        c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
                        FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
                        c.FLOW_ID = s.FLOW_ID 
                        AND s.ACTIVE_COMPONENT_INSTANCES <> 0
                        AND COMPOSITE_NAME = 'Supplier_Management' 
                            AND c.STATE NOT in (5,8) 
                            AND c.component_name NOT IN ('ErrorHandler')
                            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
                        
                            ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceProcurementPlanRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0
            AND c.COMPOSITE_NAME = 'Procurement_Plan' 
            AND c.STATE NOT in (5,8) 
            AND c.component_name NOT IN ('ErrorHandler')
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME     = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceContractMgmtRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0
            AND c.COMPOSITE_NAME = 'Contract_Management' 
            AND c.STATE NOT in (5,8) 
            AND c.component_name NOT IN ('ExceptionHandler')
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceSourcingDpRunningStl($componentName = null) {
    try {
          $whereComponentName = "AND c.COMPONENT_NAME = 'SupplierCodification' ";
          if(strlen($componentName) > 0){
            $whereComponentName = " AND c.COMPONENT_NAME = '$componentName' ";
          }
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0 
            AND c.COMPOSITE_NAME  = 'SourcingDP'
            AND c.STATE NOT in (5,8) 
            AND c.FLOW_ID not in (-1) 
            $whereComponentName  
            AND component_name NOT IN ('ErrorHandler')
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.COMPOSITEINSTANCEID  = c.CMPST_ID   )
            AND NOT EXISTS (
            	SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') 
            		AND w.CUSTOMATTRIBUTESTRING1  IN (SELECT   DISTINCT  x.CUSTOMATTRIBUTESTRING1   FROM wftask x WHERE x.flow_id = c.FLOW_ID AND 
           				x.CUSTOMATTRIBUTESTRING2 = 'RN')   )
            ");
          
          foreach($result as $row){
            $row->doc_no_list = $this->findDocNoTaskByFlowId($row->flow_id);

            $rnNumbers = [];
            foreach ($row->doc_no_list as $doc) {
                if (!empty($doc->doc_no) && (strpos($doc->doc_no, 'RN') === 0)) {
                    $rnNumbers[] = $doc->doc_no;
                }
                if (!empty($doc->doc_no_other) && (strpos($doc->doc_no_other, 'RN') === 0)) {
                    $rnNumbers[] = $doc->doc_no_other;
                }
            }

            $rnNumbers = array_unique($rnNumbers);
            
            $statuses = [];
            foreach ($rnNumbers as $rnNumber) {
                $statuses[] = $this->getStatusByRequestNoteNo($rnNumber);
            }
            
            $row->rn_status_list = $statuses;
          }
          return $result;

          
    } catch (Exception $ex) {
          return null;
    }
  }

  private function getStatusByRequestNoteNo($requestNoteNo) {
    $sql = "
        SELECT 
    MAX(CASE WHEN t.no = 1 THEN t.STATUS_NAME END) AS rn_status,
    LISTAGG(CASE WHEN t.no = 2 THEN t.STATUS_NAME END, ', ') 
        WITHIN GROUP (ORDER BY t.changed_date) AS pr_status,
    LISTAGG(CASE WHEN t.no = 2 THEN TO_CHAR(t.changed_date, 'DD-MON-YY HH12:MI:SS AM') END, ', ') 
        WITHIN GROUP (ORDER BY t.changed_date) AS pr_date,
    COUNT(CASE WHEN t.no = 2 AND t.changed_date IS NOT NULL THEN 1 END) AS current_pr,
    COUNT(t.purchase_request_id) AS total_pr 
FROM (
    -- Get RN Status
    SELECT 
        CAST(NULL AS NUMBER) AS purchase_request_id, 
        srn.request_note_id,
        psd.STATUS_NAME, 
        sws.changed_date, 
        1 AS no, 
        srn.REQUEST_NOTE_NO  
    FROM SC_REQUEST_NOTE srn
    JOIN SC_WORKFLOW_STATUS sws ON srn.REQUEST_NOTE_ID = sws.DOC_ID
    JOIN PM_STATUS_DESC psd ON sws.STATUS_ID = psd.STATUS_ID
    WHERE sws.IS_CURRENT = 1
    AND sws.DOC_TYPE = 'RN'
    AND psd.LANGUAGE_CODE = 'en'
    AND srn.REQUEST_NOTE_NO IN :requestNoteNo
    UNION ALL
    SELECT 
    spr.PURCHASE_REQUEST_ID,
    spr.REQUEST_NOTE_ID,
    psd.STATUS_NAME,
    fws.CREATED_DATE AS changed_date,
    2 AS no,
    srn.REQUEST_NOTE_NO
FROM SC_REQUEST_NOTE srn
JOIN SC_PURCHASE_REQUEST spr ON srn.REQUEST_NOTE_ID = spr.REQUEST_NOTE_ID
LEFT JOIN FL_FULFILMENT_REQUEST ffr ON spr.PURCHASE_REQUEST_ID = ffr.PURCHASE_REQUEST_ID
LEFT JOIN FL_WORKFLOW_STATUS fws 
    ON ffr.FULFILMENT_REQ_ID = fws.DOC_ID 
    AND fws.DOC_TYPE = 'PR'
    AND fws.IS_CURRENT = 1
LEFT JOIN PM_STATUS_DESC psd 
    ON fws.STATUS_ID = psd.STATUS_ID 
    AND psd.LANGUAGE_CODE = 'en'
WHERE srn.REQUEST_NOTE_NO IN :requestNoteNo
) t
GROUP BY t.REQUEST_NOTE_NO
    ";

    // Execute query with parameter binding
    return DB::connection('oracle_nextgen_rpt')->selectOne($sql, [':requestNoteNo' => $requestNoteNo]);
}

  protected function findListInstanceSourcingQtRunningStl($componentName = null)
  {
    try {
      $whereComponentName = '';
      if (strlen($componentName) > 0) {
        $whereComponentName = " AND c.COMPONENT_NAME = '$componentName' ";
      }
      $query1 = "SELECT * FROM (
          SELECT distinct
          c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE,
          CASE WHEN COMPONENT_NAME = 'QuotationTenderCreation'
          THEN (SELECT count(*)  FROM wftask WHERE flow_id = c.FLOW_ID AND PROCESSNAME = 'QuotationTenderCreation' AND OUTCOME = 'APPROVE'
          AND ACTIVITYNAME= 'Approve Publication')
          ELSE 0
          END AS IS_APPROVE_PUBLICATION
          FROM cube_instance c, SCA_FLOW_INSTANCE s
          WHERE
          c.FLOW_ID = s.FLOW_ID
          AND s.ACTIVE_COMPONENT_INSTANCES <> 0
          AND COMPOSITE_NAME = 'SourcingQT'
          AND c.STATE IN (5)
          --  AND c.FLOW_ID not in (-1,-2)
          -- AND EXTRACT( YEAR FROM c.MODIFY_DATE ) <= 2022
          -- AND trunc(c.MODIFY_DATE) <= TO_DATE('2022-01-01','YYYY-MM-DD)
          AND c.COMPONENT_NAME  IN ('SupplierFinalization')
          -- AND c.flow_id = '711324'
          AND  EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED') AND w.COMPOSITEINSTANCEID  = c.CMPST_ID
          AND w.processid = 'DirectAward' )
          ) tmp
          WHERE tmp.IS_APPROVE_PUBLICATION = 0  -- 0 mean this RECORD IS valid AS stuck INSTANCE for issue running component  QuotationTenderCreation
      ";

      $query2 = "SELECT * FROM (
          SELECT distinct
          c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE,
          CASE WHEN COMPONENT_NAME = 'QuotationTenderCreation'
          THEN (SELECT count(*)  FROM wftask WHERE flow_id = c.FLOW_ID AND PROCESSNAME = 'QuotationTenderCreation' AND OUTCOME = 'APPROVE'
          AND ACTIVITYNAME= 'Approve Publication')
          ELSE 0
          END AS IS_APPROVE_PUBLICATION
          FROM cube_instance c, SCA_FLOW_INSTANCE s
          WHERE
          c.FLOW_ID = s.FLOW_ID
          AND s.ACTIVE_COMPONENT_INSTANCES <> 0
          AND COMPOSITE_NAME = 'SourcingQT'
          AND c.STATE NOT IN (5,8)
          AND c.FLOW_ID not in (-1,-2)
          -- AND EXTRACT( YEAR FROM c.MODIFY_DATE ) <= 2022
          -- AND trunc(c.MODIFY_DATE) <= TO_DATE('2022-01-01','YYYY-MM-DD)
          AND c.COMPONENT_NAME NOT IN ('Evaluation','SiteVisitAndProposal')
          $whereComponentName
          AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME    = c.COMPOSITE_NAME  AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.COMPOSITEINSTANCEID  = c.CMPST_ID  )
          ) tmp
          WHERE tmp.IS_APPROVE_PUBLICATION = 0  -- 0 mean this RECORD IS valid AS stuck INSTANCE for issue running component  QuotationTenderCreation
      ";

      $result = DB::connection('oracle_bpm_rpt')
        ->select($query2);

      $newList = collect([]);
      foreach ($result as $row) {
        $row->doc_no_list = collect($this->findDocNoTaskByFlowId($row->flow_id));
        $isSkip = false;

        if ($row->doc_no_list->count() > 0) {
          $docNoObj = $row->doc_no_list->first();
          $docNo = $docNoObj->doc_no;
          if ($docNoObj->doc_no == null) {
            $docNo = $docNoObj->doc_no_other;
          }

          // Get QT status info
          $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT A');
          $result = $query->join('SC_WORKFLOW_STATUS B', 'A.QT_ID', '=', 'B.DOC_ID')
            ->join('PM_STATUS C', 'B.STATUS_ID', '=', 'C.STATUS_ID')
            ->join('PM_STATUS_DESC D', 'C.STATUS_ID', '=', 'D.STATUS_ID')
            ->where('D.LANGUAGE_CODE', 'en')
            ->where('B.IS_CURRENT', 1)
            ->where('B.DOC_TYPE', 'QT')
            ->where('A.QT_NO', $docNo)
            ->orderBy('B.CREATED_DATE', 'desc')
            ->select('B.DOC_TYPE', 'B.DOC_ID', 'A.QT_NO', 'C.STATUS_ID', 'D.STATUS_NAME')
            ->get();

          if ($result->count() > 0) {
            // Store status info for display in the view
            $row->qt_status_list = $result;

            // Skip logic for QuotationTenderMaintenance
            if ($row->component_name == 'QuotationTenderMaintenance') {
              $statusObj = $result->first();
              // Only show if status is 60014 or 60015, skip all others
              if ($statusObj->status_id != '60014' && $statusObj->status_id != '60015') {
                $isSkip = true;
              }
              // check status committee, if complete then exclude from list
            } else if($row->component_name == 'CommitteeApproval') {
              $queryCmt = "SELECT D.QT_NO, A.COMMITTEE_ID, B.COMMITTEE_TYPE_ID,
                            decode ((B.STATUS_ID), '60200', 'Pending Submission', '60201', 'Pending Approval','60202', 'Completed', '60203', 'Pending Revision','60204', 'Pending Revision Approval' ) as statuscmt,
                            P1.PARAMETER_CODE MEMBER_CODE,
                            P2.CODE_NAME MEMBER_DESC, C.USER_ID, C.MEMBER_NAME, C.IC_PASSPORT,
                            PP1.PARAMETER_CODE ROLE_CODE, PP2.CODE_NAME ROLE_DESC
                            FROM SC_QT_COMMITTEE A,
                                  SC_COMMITTEE B,
                                  PM_PARAMETER P1,
                                  PM_PARAMETER_DESC P2,
                                  PM_PARAMETER_TYPE P3,
                                  SC_COMMITTEE_MEMBER C,
                                  PM_PARAMETER PP1,
                                  PM_PARAMETER_DESC PP2,
                                  PM_PARAMETER_TYPE PP3,
                                  SC_QT D
                            WHERE A.COMMITTEE_ID = B.COMMITTEE_ID
                              AND B.COMMITTEE_TYPE_ID = P1.PARAMETER_ID
                              AND P1.PARAMETER_TYPE = P3.PARAMETER_TYPE
                              AND P1.PARAMETER_ID = P2.PARAMETER_ID
                              AND P2.LANGUAGE_CODE = 'en'
                              AND B.COMMITTEE_ID = C.COMMITTEE_ID
                              AND C.MEMBER_ROLE_ID = PP1.PARAMETER_ID
                              AND PP1.PARAMETER_TYPE = PP3.PARAMETER_TYPE
                              AND PP1.PARAMETER_ID = PP2.PARAMETER_ID
                              AND PP2.LANGUAGE_CODE = 'en'
                              AND D.QT_ID = A.QT_ID
                              AND D.QT_NO = '$docNo'
                              AND B.STATUS_ID <> 60202
                          ORDER BY P2.CODE_NAME, PP1.PARAMETER_CODE";
              $resultCmt = DB::connection('oracle_nextgen_rpt')->select($queryCmt);
              if(count($resultCmt) == 0) {
                  $isSkip = true;
              }
            }
          } else {
            $row->qt_status_list = collect([]);
          }
        } else {
          $row->qt_status_list = collect([]);
        }

        if (!$isSkip) {
          $newList->push($row);
        }
      }
      return $newList;
    } catch (Exception $ex) {
      return collect([]);
    }
  }

  protected function findListInstanceFulfilmentRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT * FROM (
              SELECT 
                  c.cikey,c.COMPONENT_NAME ,c.cmpst_id ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.title ,c.state,c.modify_Date,
                  decode(COMPONENT_NAME,'InvoiceCancellation',
                  (SELECT 1 FROM wftask w WHERE COMPOSITE_NAME  = 'Fulfilment' 
                    AND w.flow_id  = c.flow_id  AND w.state IS NULL AND w.PROCESSNAME = 'InvoiceCancellation' ),
                  null
              ) AS is_valid_inv_cancel
                  FROM cube_instance c, SCA_FLOW_INSTANCE s 
                  WHERE 
                  c.FLOW_ID = s.FLOW_ID 
                  AND s.ACTIVE_COMPONENT_INSTANCES <> 0 
                  AND COMPOSITE_NAME = 'Fulfilment' 
                  AND state NOT IN (5,8)
                  AND c.flow_id NOT in (-1,-2)
                  AND component_name NOT IN ('UpdateWorkflowStatus','WorkflowNotification','WorkFlowStatusUpdate','GFMASServiceCall','WorkflowNotificationService','ErrorHandler','TrackingDairyHandler')
                  AND NOT EXISTS (SELECT 1 FROM wftask w WHERE COMPOSITE_NAME = c.COMPOSITE_NAME 
                          AND state in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
                  AND NOT EXISTS (SELECT 1 FROM cube_instance ci WHERE ci.flow_id  = c.flow_id AND  ci.component_name IN ('GFMASServiceCall') AND ci.state = 1  ) 
              
              )  tmp 
              WHERE 
              tmp.is_valid_inv_cancel  IS null
              -- is_valid_inv_cancel set is null mean, for component InvoiceCancellation running still in valid status..
            ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceYEPFulfilmentRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0
            AND COMPOSITE_NAME = 'YEP_Fulfilment' 
            AND c.STATE NOT IN (5,8) 
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME   = c.COMPOSITE_NAME   AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
               ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceYEPOrderRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0
            AND COMPOSITE_NAME = 'YEP_Order' 
            AND c.STATE NOT IN (5,8) 
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME   = c.COMPOSITE_NAME   AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
               ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListInstanceOrderRunningStl() {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT 
            c.CIKEY,c.COMPONENT_NAME ,c.CMPST_ID ,c.COMPOSITE_NAME , c.COMPOSITE_REVISION,c.FLOW_ID,c.TITLE ,c.STATE,c.MODIFY_DATE
            FROM cube_instance c, SCA_FLOW_INSTANCE s WHERE 
            c.FLOW_ID = s.FLOW_ID 
            AND s.ACTIVE_COMPONENT_INSTANCES <> 0
            AND COMPOSITE_NAME = 'Order' 
            AND c.STATE NOT IN (5,8) 
            AND component_name NOT IN ('UpdateWorkflowStatus','WorkflowNotification','WorkFlowStatusUpdate','GFMASServiceCall','WorkflowNotificationService','GFMASQueueCallback')
            AND NOT EXISTS (SELECT 1 FROM wftask w WHERE w.COMPOSITENAME  = c.COMPOSITE_NAME 
              AND w.STATE in ('ASSIGNED','SUSPENDED') AND w.flow_id  = c.flow_id  )
            AND NOT EXISTS (SELECT 1 FROM cube_instance ci WHERE ci.flow_id  = c.flow_id AND  ci.component_name IN ('GFMASServiceCall') AND ci.STATE = 1   )
             ");
          
          foreach($result as $row){
            $row->doc_no_list =  $this->findDocNoTaskByFlowId($row->flow_id);
          }
          return $result;
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findDocNoTaskByFlowId($flowId) {
    try {
          $result = DB::connection('oracle_bpm_rpt')
            ->select("SELECT DISTINCT CUSTOMATTRIBUTESTRING1 as doc_no , CUSTOMATTRIBUTESTRING2 as doc_type , PROTECTEDTEXTATTRIBUTE1 AS doc_no_other  
              FROM wftask 
              WHERE flow_id = ?
              AND ( CUSTOMATTRIBUTESTRING1 IS NOT NULL OR PROTECTEDTEXTATTRIBUTE1 IS NOT NULL) 
            ",array($flowId));
          return $result ;
          
    } catch (Exception $ex) {
          return null;
    }
  }

  protected function findListStuckVerifyLoa() {
    try {
      $result = DB::connection('oracle_bpm_rpt')
        ->select("SELECT w.ASSIGNEES, count(*) 
                    FROM WFTASK  w 
                    WHERE w.ACTIVITYNAME = 'Verify LOA' 
                          AND w.PROCESSNAME = 'LOA Acceptance'
                          AND w.COMPOSITENAME = 'SourcingQT'
                          AND w.COMPONENTNAME = 'VerifyLetter'
                          AND w.STATE = 'ASSIGNED' 
                          AND w.flow_id is null 
                  GROUP BY w.ASSIGNEES 
                  ORDER BY 2 desc"); 
      return $result;
} catch (Exception $ex) {
  dump($ex);
      return null;
}
  }

}
