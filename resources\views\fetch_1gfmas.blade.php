@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

        <div class="block full">
            <!-- Inline Form Title -->
            <div class="block-title">
                <h2><strong>1GFMAS Server > Out 1GFMAS </strong> Folder</h2>
            </div>
            <!-- END Inline Form Title -->

            <div class="row">
                <!-- Inline Form Content -->
                <form action="" method="post" class="form-inline" onsubmit="return false;">
                    <div class="form-group col-md-3">
                        <label class="control-label" for="process_id">Process</label>
                        <select id="process_id" name="process_id" class="form-control" size="1">
                            <option value="APOVE">APOVE</option>
                            <option value="AP511" selected>AP511</option>
                            <option value="APERR">APERR</option>
                            <option value="AR902">AR902</option>
                            <option value="CMBNK">CMBNK</option>
                            <option value="GLBAC">GLBAC</option>
                            <option value="GLDNA">GLDNA</option>
                            <option value="GLPRG">GLPRG</option>
                            <option value="GLPRJ">GLPRJ</option>
                            <option value="GLPRO">GLPRO</option>
                            <option value="GLSEG">GLSEG</option>
                            <option value="GLGLC">GLGLC</option>
                            <option value="GLPTJ">GLPTJ</option>
                            <option value="GLPCG">GLPCG</option>
                            <option value="GLVOT">GLVOT</option>
                        </select>
                    </div>
                    <div class="form-group col-md-3 form-actions pull-right">
                        <button id="fetch_btn" type="submit" class="btn btn-sm btn-primary action_fetch enable-tooltip" 
                                data-original-title="Trigger Batch Process">
                            <i class="fa fa-arrow-down"></i> Fetch
                        </button>
                        <button class="btn btn-sm btn-default action_refresh" data-url='/list/1gfmas/fetch'>
                            <i class="fa fa-refresh"></i> Refresh
                        </button>
                    </div>
                </form>
            </div>

            <!-- END Inline Form Content -->
        </div>

        <div class="block full">
            <!-- Inline Form Title -->
            <div class="block-title">
                <h2><strong>eP Server > IN/OUT 1GFMAS </strong> Folder</h2>
            </div>
            <!-- END Inline Form Title -->

            <div class="row">
                <!-- Inline Form Content -->
                <form action="" method="post" class="form-inline" onsubmit="return false;">
                    <div class="form-group col-md-3">
                        <label class="control-label" for="process_id_inout">Process</label>
                        <select id="process_id_inout" name="process_id_inout" class="form-control" size="1">
                            <option value="APOVE">APOVE</option>
                            <option value="APIVE">APIVE</option>
                            <option value="AP511" selected>AP511</option>
                            <option value="AP516">AP516</option>
                            <option value="APERR">APERR</option>
                            <option value="AR502">AR502</option>
                            <option value="AR902">AR902</option>
                            <option value="CMBNK">CMBNK</option>
                            <option value="GLBAC">GLBAC</option>
                            <option value="GLDNA">GLDNA</option>
                            <option value="GLPRG">GLPRG</option>
                            <option value="GLPRJ">GLPRJ</option>
                            <option value="GLPRO">GLPRO</option>
                            <option value="GLSEG">GLSEG</option>
                            <option value="GLGLC">GLGLC</option>
                            <option value="GLPTJ">GLPTJ</option>
                            <option value="GLPCG">GLPCG</option>
                            <option value="GLVOT">GLVOT</option>
                            
                        </select>
                    </div>
                    <div class="form-group col-md-3 form-actions pull-right">
                        <button id="process_inout_btn" type="submit" class="btn btn-sm btn-primary action_fetch_inout enable-tooltip" 
                                data-original-title="Trigger Batch Process">
                            <i class="fa fa-arrow-down"></i> Fetch
                        </button>
                        <button class="btn btn-sm btn-default action_refresh" data-url='/list/1gfmas/fetch'>
                            <i class="fa fa-refresh"></i> Refresh
                        </button>
                    </div>
                </form>
            </div>

            <!-- END Inline Form Content -->
        </div>

        <div class="block remove-padding">
         
            <div id="dash_outbound_1gfmas" class="widget">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
            <div id="dash_inbound_1gfmas" class="widget">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
        

    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div id="fetch_btn" class="row" style="padding: 0 15px 15px 0; display: none;">
                        <div class="pull-right">
                            <a href="{{ url("/list/1gfmas/folder") }}" target="_blank" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" data-original-title="Fetch from 1GFMAS Out Folder"><i class="gi gi-disk_save"></i> Trigger Batch</a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>

        var APP_URL = {!! json_encode(url('/')) !!}

        App.datatables();
        /* Initialize Datatables */
        var tableListData =     $('#basic-datatable').DataTable({
                columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                pageLength: 10,
                lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
        });
        
        $(document).ready(function () {

            
            $('.widget').on("click",'.modal-list-data-action', function(){
                
                $('.spinner-loading').show();
                $('#basic-datatable').html('').fadeIn();
                
                $('#modal-list-data-header').text($(this).attr('data-title'));
                if($(this).attr('data-url') === '/list/1gfmas/pending/batch/1gfmas-outbound'){
                    $('#fetch_btn').show();
                }
                
                 /* Destroy ID Datatable, To make sure reRun again ID */
                tableListData.destroy();
                
                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#basic-datatable').html($data).fadeIn();
                        
                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });
                        
                        $('.spinner-loading').hide();
                    }
                });
                
            });
            
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/outbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_outbound_1gfmas').hide().html($data).fadeIn();
                }
            });
            $.ajax({
                url: APP_URL + '/dashboard/1gfmas/inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_inbound_1gfmas').hide().html($data).fadeIn();
                }
            });
            
            
            
            function loadingPanel(){
                $('#dash_inbound_1gfmas').hide().html('<div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>').fadeIn();
                $('#dash_outbound_1gfmas').hide().html('<div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>').fadeIn();
            }

            function refreshDataTable() {
                loadingPanel();
                $.ajax({
                    url: APP_URL + '/dashboard/1gfmas/outbound',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_outbound_1gfmas').hide().html($data).fadeIn();
                    }
                });
                $.ajax({
                    url: APP_URL + '/dashboard/1gfmas/inbound',
                    type: "GET",
                    success: function (data) {
                        $data = $(data);
                        $('#dash_inbound_1gfmas').hide().html($data).fadeIn();
                    }
                });
            }

            //REFRESH BUTTON
            $('div.form-actions').on("click", 'button.action_refresh', function () {
                refreshDataTable();
            });


            //FETCH BUTTON
            $('div.form-actions').on("click", 'button.action_fetch', function () {
                var processId = $("#process_id").val();
                var csrf = $("input[name=_token]").val();

                loadingPanel();

                $.ajax({
                    url: "/fetch/1gfmas/1gfmas-out",
                    method: "POST",
                    dataType: "json",
                    data: {
                        "_token": csrf,
                        "process_id": processId
                    },
                    context: document.body,
                    error: function(jqXHR, textStatus, errorThrown)
                    {
                        alert(errorThrown);
                    }
                }).done(function (resp) {
                    if (resp.state === 'true') {
                        alert(resp.msg);
                        refreshDataTable();
                    } else {
                        alert(resp.msg);
                        refreshDataTable();
                    }
                });
            });
            
            
            //PROCES INOUT BUTTON
            $('div.form-actions').on("click", 'button.action_fetch_inout', function () {
                var processIdInOut = $("#process_id_inout").val();
                var csrf = $("input[name=_token]").val();

                loadingPanel();

                $.ajax({
                    url: "/fetch/1gfmas/process/in-out-file",
                    method: "POST",
                    dataType: "json",
                    data: {
                        "_token": csrf,
                        "process_id": processIdInOut
                    },
                    context: document.body,
                    error: function(jqXHR, textStatus, errorThrown)
                    {
                        alert(errorThrown);
                    }
                }).done(function (resp) {
                    if (resp.state === 'true') {
                        alert(resp.msg);
                        refreshDataTable();
                    } else {
                        alert(resp.msg);
                        refreshDataTable();
                    }
                });
            });

            //ON PROCESS CHANGE
            $('#process_id').change(function() {
                $('#fetch_btn').attr('data-original-title', 'Trigger ' + $('#process_id').val() + ' Batch Process');
            });
            
            $('#process_id_inout').change(function() {
                $('#process_inout_btn').attr('data-original-title', 'Trigger ' + $('#process_id_inout').val() + ' Batch Process');
            });
            

        });



    </script>
@endsection