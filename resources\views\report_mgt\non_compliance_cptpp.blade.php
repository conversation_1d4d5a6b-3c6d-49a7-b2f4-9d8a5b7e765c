@extends('layouts.guest-dash')
@section('content')
    <div class="block block-alt-noborder full">
        <div class="block-title panel-heading epss-title-s1">
            <h1>
                <i class=""></i>Ketidakpatuhan CPTPP
            </h1>
        </div>

        @if (session()->has('failed'))
            <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-info-circle"></i> Error! </h4>
                {{ session('failed') }}
            </div>
            <?php session()->forget('failed'); ?>
        @endif
        <div class="d-flex justify-content-center align-items-center">
            <label class="col-md-3 text-right" for="date">Date From<span class="text-danger">*</span></label>
            <div class="col-md-2 date">
                <input id="date_from" name="date_from" type="date" value="{{ $startDate }}" class="form-control">
            </div>
            <label class="col-md-2 text-right" for="date">Date To<span class="text-danger">*</span></label>
            <div class="col-md-2 date">
                <input id="date_to" name="date_to" type="date" value="{{ $toDate }}" class="form-control">
            </div>
        </div>
        <div id="error_message" class="text-center col-md-8" style="color: red; display: none;">Please choose date_from
        </div>


        <ul id="dynamicNav" class="nav-horizontal text-center" style="margin-top: 100px;"></ul>

        <div id="report_sm" class="panel-group" style="display: none">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="kodBidangToggleSM" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#kodBidangContentSM" href="#">Kod Bidang</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="kodBidangContentSM" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingSM" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_SM" class="text-center" style="color: red;"></div>
                    <div class="panel-body firstSM" style="display:none">
                        <div class ="table-responsive">
                            <table id="cptppSM_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Company Name</th>
                                        <th class="text-center">Business Type</th>
                                        <th class="text-center">Reg No</th>
                                        <th class="text-center">MOF No</th>
                                        <th class="text-center">Origin Country SSM</th>
                                        <th class="text-center">Supplier ID</th>
                                        <th class="text-center">Kod Bidang</th>
                                        <th class="text-center">Appl Id</th>
                                        <th class="text-center">Record Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="report_pp" class="panel-group" style="display: none">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="kodBidangTogglePP" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#kodBidangContentPP" href="#">Penggunaan tajuk CPTPP</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="kodBidangContentPP" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingPP" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_PP" class="text-center" style="color: red;"></div>
                    <div class="panel-body firstPP" style="display:none">
                        <div class ="table-responsive">
                            <table id="cptppPP_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Ministry Code</th>
                                        <th class="text-center">Ministry Name</th>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">Plan Year</th>
                                        <th class="text-center">Plan No</th>
                                        <th class="text-center">Status Name</th>
                                        <th class="text-center">Plan Title</th>
                                        <th class="text-center">Plan Description</th>
                                        <th class="text-center">Plan Type</th>
                                        <th class="text-center">Plan Amount</th>
                                        <th class="text-center">Changed Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="report_qt" class="panel-group" style="display: none">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="tajukToggleQT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#tajukContentQT" href="#">Penggunaan tajuk CPTPP</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="tajukContentQT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingQT" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_QT" class="text-center" style="color: red;"></div>
                    <div class="panel-body firstQT" style="display:none">
                        <div class ="table-responsive">
                            <table id="cptppQT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Ministry Code</th>
                                        <th class="text-center">Ministry Name</th>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">QT No</th>
                                        <th class="text-center">QT Title</th>
                                        <th class="text-center">QT Type</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-center">Stage</th>
                                        <th class="text-center">QT Amount</th>
                                        <th class="text-center">QT Changed Date</th>
                                        <th class="text-center">QT Action Date</th>
                                        <th class="text-center">QT Closing Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="iklanToggleQT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#iklanContentQT" href="#">Tempoh Iklan</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="iklanContentQT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingQT2" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_QT2" class="text-center" style="color: red;"></div>
                    <div class="panel-body secondQT" style="display:none">
                        <div class ="table-responsive">
                            <table id="iklanQT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">QT Id</th>
                                        <th class="text-center">QT No</th>
                                        <th class="text-center">Publish Period</th>
                                        <th class="text-center">QT Title</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="kodBidangToggleQT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#kodBidangContentQT" href="#">Kod Bidang</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="kodBidangContentQT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingQT3" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_QT3" class="text-center" style="color: red;"></div>
                    <div class="panel-body thirdQT" style="display:none">
                        <div class ="table-responsive">
                            <table id="kodQT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">QT No</th>
                                        <th class="text-center">Kod Bidang</th>
                                        <th class="text-center">QT Title</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="loaToggleQT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#loaContentQT" href="#">Prosuder DRP Sistem eP</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="loaContentQT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingQT4" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_QT4" class="text-center" style="color: red;"></div>
                    <div class="panel-body forthQT" style="display:none">
                        <div class ="table-responsive">
                            <table id="loaQT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">QT No</th>
                                        <th class="text-center">QT Title</th>
                                        <th class="text-center">Day Difference</th>
                                        <th class="text-center">Publish Date</th>
                                        <th class="text-center">Closing Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="tempasalToggleQT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#tempasalContentQT" href="#">Senarai Semak Kewangan (Tempasal)</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="tempasalContentQT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingQT5" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_QT5" class="text-center" style="color: red;"></div>
                    <div class="panel-body fifthQT" style="display:none">
                        <div class ="table-responsive">
                            <table id="tempasalQT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">QT No</th>
                                        <th class="text-center">QT Title</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-center">Stage</th>
                                        <th class="text-center">Action Date</th>
                                        <th class="text-center">Publish Date</th>
                                        <th class="text-center">Closing Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="report_ct" class="panel-group" style="display: none">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="kodBidangToggleCT" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#kodBidangContentCT" href="#">Pindaan Kontrak</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="kodBidangContentCT" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingCT" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_CT" class="text-center" style="color: red;"></div>
                    <div class="panel-body firstCT" style="display:none">
                        <div class ="table-responsive">
                            <table id="cptppCT_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Ministry Name</th>
                                        <th class="text-center">PTJ Code</th>
                                        <th class="text-center">PTJ Name</th>
                                        <th class="text-center">Company Name</th>
                                        <th class="text-center">Contract No</th>
                                        <th class="text-center">Contract Name</th>
                                        <th class="text-center">Is Contract Extend</th>
                                        <th class="text-center">Is Quantity Change</th>
                                        <th class="text-center">Is Value Change</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="report_fl" class="panel-group" style="display: none">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <div class="form-group">
                        <div class="col-md-9">
                            <h4 class="panel-title" style="margin-bottom: 5px;">
                                <a id="kodBidangToggleFL" class="accordion-toggle" data-toggle="collapse"
                                    data-target="#kodBidangContentFL" href="#">Penggunaan tajuk CPTPP</a>
                            </h4>
                        </div>
                        <div class="col-md-2">
                        </div>
                    </div>
                </div>

                <div id="kodBidangContentFL" class="panel-collapse collapse">
                    <div class="text-center spinnerLoadingFL" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                    <div id="error_message_FL" class="text-center" style="color: red;"></div>
                    <div class="panel-body firstFL" style="display:none">
                        <div class ="table-responsive">
                            <table id="cptppFL_datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">Ministry Name</th>
                                        <th class="text-center">PTJ Code CT</th>
                                        <th class="text-center">PTJ Name CT</th>
                                        <th class="text-center">Contract Name</th>
                                        <th class="text-center">Contract No</th>
                                        <th class="text-center">CR No</th>
                                        <th class="text-center">CR Title</th>
                                        <th class="text-center">PTJ Code CR</th>
                                        <th class="text-center">PTJ Name CR</th>
                                        <th class="text-center">Supplier Name</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $('#page-container').removeAttr('class');
        $(function() {
            TablesDatatables.init();
        });
        App.datatables();
        var APP_URL = {!! json_encode(url('/')) !!}
    </script>

    <script>
        $(document).ready(function() {
            function getPathValue() {
                const path = window.location.pathname;
                const parts = path.split('/');
                const index = parts.indexOf('cptpp');
                if (index !== -1 && index < parts.length - 1) {
                    return parts[index + 3];
                } else {
                    return null;
                }
            }

            var initialModule = "{{ $module }}";
            $('#date_from').on('click', function(event) {
                $('#date_from').val("");
                $('#date_to').val("");
                $('#date_to').prop('disabled', false);
                $('#report_sm').hide();
                $('#report_pp').hide();
                $('#report_qt').hide();
                $('#report_ct').hide();
                $('#report_fl').hide();
            });

            $('#date_to').on('click', function(event) {
                $('#report_sm').hide();
                $('#report_pp').hide();
                $('#report_qt').hide();
                $('#report_ct').hide();
                $('#report_fl').hide();
                if ($('#date_from').val() === "") {
                    $('#error_message').show();
                    $('#date_to').prop('disabled', true);
                } else {
                    $('#error_message').hide();
                    $('#cptppSM_datatable').hide();
                    $('.panel-collapse').collapse('hide');
                }
            });


            renderNav(initialModule);

            $('#dynamicNav').on('click', 'a', function(event) {
                var startDate = $('#date_from').val();
                var toDate = $('#date_to').val();
                if (!toDate) {
                    $('#date_to').val(getTodayDate());
                    toDate = $('#date_to').val();
                }

                if (!startDate) {
                    $('#error_message').show();
                    return;
                } else {
                    $('#error_message').hide();
                    if (startDate && toDate) {
                        renderNav(currentModule);
                    }
                }
                var currentModule = $(this).data('module');

                if (currentModule === 'sm') {

                    $('#report_sm').show();
                    $('#report_pp').hide();
                    $('#report_qt').hide();
                    $('#report_ct').hide();
                    $('#report_fl').hide();
                }

                if (currentModule === 'pp') {
                    $('#report_pp').show();
                    $('#report_sm').hide();
                    $('#report_qt').hide();
                    $('#report_ct').hide();
                    $('#report_fl').hide();
                }

                if (currentModule === 'qt') {
                    $('#report_qt').show();
                    $('#report_sm').hide();
                    $('#report_pp').hide();
                    $('#report_ct').hide();
                    $('#report_fl').hide();
                }

                if (currentModule === 'ct') {
                    $('#report_ct').show();
                    $('#report_sm').hide();
                    $('#report_pp').hide();
                    $('#report_qt').hide();
                    $('#report_fl').hide();
                }

                if (currentModule === 'fl') {
                    $('#report_fl').show();
                    $('#report_sm').hide();
                    $('#report_pp').hide();
                    $('#report_qt').hide();
                    $('#report_ct').hide();
                }

                if (startDate && toDate) {
                    renderNav(currentModule);
                } else {
                    return true;
                }
                $('.firstSM').hide();
                $('.firstPP').hide();
                $('.firstCT').hide();
                $('.firstFL').hide();
                $('.firstQT').hide();
                $('.secondQT').hide();
                $('.thirdQT').hide();
                $('.forthQT').hide();
                $('.fifthQT').hide();
                $('.panel-collapse').collapse('hide');

                event.preventDefault();
            });
        });

        function getTodayDate() {
            var today = new Date();
            var dd = String(today.getDate()).padStart(2, '0');
            var mm = String(today.getMonth() + 1).padStart(2, '0'); //January is 0!
            var yyyy = today.getFullYear();

            return yyyy + '-' + mm + '-' + dd;
        }

        var modules = {
            'sm': 'Supplier Management',
            'pp': 'P Plan',
            'qt': 'Quotation Tender',
            'ct': 'Contract',
            'fl': 'Fulfilment'
        };

        function createNavItem(module, moduleName, isActive) {
            var listItem = document.createElement('li');
            listItem.className = isActive ? 'active' : '';

            var link = document.createElement('a');
            link.dataset.module = module;

            var icon = document.createElement('i');
            var iconClass = '';

            switch (module) {
                case 'sm':
                    iconClass = 'gi gi-group';
                    break;
                case 'pp':
                    iconClass = 'fa fa-list-alt';
                    break;
                case 'qt':
                    iconClass = 'gi gi-package';
                    break;
                case 'ct':
                    iconClass = 'fa fa-pencil-square-o';
                    break;
                case 'fl':
                    iconClass = 'fa fa-hourglass-2';
                    break;
            }

            icon.className = iconClass;
            link.appendChild(icon);
            link.appendChild(document.createTextNode(moduleName));
            listItem.appendChild(link);

            return listItem;
        }

        function renderNav(currentModule) {
            var navContainer = document.getElementById('dynamicNav');

            navContainer.innerHTML = '';

            for (var module in modules) {
                var isActive = module === currentModule;
                var listItem = createNavItem(module, modules[module], isActive);
                navContainer.appendChild(listItem);
            }
            renderContentForModule(currentModule);
        }

        function renderContentForModule(module) {
            if (module === 'sm') {
                setupAjax('#kodBidangToggleSM', 'sm');
            }
            if (module === 'pp') {
                setupAjax('#kodBidangTogglePP', 'pp');
            }
            if (module === 'qt') {
                setupAjax('#tajukToggleQT, #iklanToggleQT, #kodBidangToggleQT, #loaToggleQT, #tempasalToggleQT', 'qt');
            }
            if (module === 'ct') {
                setupAjax('#kodBidangToggleCT', 'ct');
            }
            if (module === 'fl') {
                setupAjax('#kodBidangToggleFL', 'fl');
            }

            function setupAjax(toggleIds, module) {
                var startDate_1 = $('#date_from').val();
                var toDate_1 = $('#date_to').val();

                if (!toDate_1) {
                    $('#date_to').val(getTodayDate());
                    var toDate_1 = $('#date_to').val();
                }

                var toggleState = {};

                $(toggleIds).each(function() {
                    var toggleId = this.id;
                    toggleState[toggleId] = false;
                    $(this).off('click').on('click', function() {
                        toggleState[toggleId] = !toggleState[toggleId];

                        var contentId, tableId;
                        if (module === 'sm') {
                            $('.spinnerLoadingSM').show();
                            $('#error_message_SM').hide();
                            contentId = '#kodBidangContentSM';
                            tableId = '#cptppSM_datatable';
                        } else if (module === 'pp') {
                            $('.spinnerLoadingPP').show();
                            $('#error_message_PP').hide();
                            contentId = '#kodBidangContentPP';
                            tableId = '#cptppPP_datatable';
                        } else if (module === 'qt') {
                            var contentMap = {
                                'tajukToggleQT': '#tajukContentQT',
                                'iklanToggleQT': '#iklanContentQT',
                                'kodBidangToggleQT': '#kodBidangContentQT',
                                'loaToggleQT': '#loaContentQT',
                                'tempasalToggleQT': '#tempasalContentQT'
                            };
                            var tableMap = {
                                'tajukToggleQT': '#cptppQT_datatable',
                                'iklanToggleQT': '#iklanQT_datatable',
                                'kodBidangToggleQT': '#kodQT_datatable',
                                'loaToggleQT': '#loaQT_datatable',
                                'tempasalToggleQT': '#tempasalQT_datatable'
                            };
                            contentId = contentMap[toggleId];
                            tableId = tableMap[toggleId];
                            if (contentId === '#tajukContentQT') {
                                $('.spinnerLoadingQT').show();
                                $('#error_message_QT').hide();
                            }
                            if (contentId === '#iklanContentQT') {
                                $('.spinnerLoadingQT2').show();
                                $('#error_message_QT2').hide();
                            }
                            if (contentId === '#kodBidangContentQT') {
                                $('.spinnerLoadingQT3').show();
                                $('#error_message_QT3').hide();
                            }
                            if (contentId === '#loaContentQT') {
                                $('.spinnerLoadingQT4').show();
                                $('#error_message_QT4').hide();
                            }
                            if (contentId === '#tempasalContentQT') {
                                $('.spinnerLoadingQT5').show();
                                $('#error_message_QT5').hide();
                            }
                        } else if (module === 'ct') {
                            $('.spinnerLoadingCT').show();
                            $('#error_message_CT').hide();
                            contentId = '#kodBidangContentCT';
                            tableId = '#cptppCT_datatable';
                        } else if (module === 'fl') {
                            $('.spinnerLoadingFL').show();
                            $('#error_message_FL').hide();
                            contentId = '#kodBidangContentFL';
                            tableId = '#cptppFL_datatable';
                        }
                        $(tableId).hide();
                        if (toggleState[toggleId]) {
                            if ($.fn.DataTable.isDataTable(tableId)) {
                                $(tableId).DataTable().clear().destroy();
                            }
                            var linkModule =
                                `/report/ketidakpatuhan/cptpp/${startDate_1}/${toDate_1}/${module}`;
                            $.ajax({
                                url: linkModule,
                                method: 'GET',
                                dataType: 'json',
                                success: function(response) {
                                    var dataExists = false;
                                    if (module === 'sm') {
                                        if (response.getListByModuleSM !== null) {
                                            dataExists = true;
                                            $('.firstSM').show();
                                            $('.spinnerLoadingSM').hide();
                                            $('#error_message_SM').hide();
                                        }
                                    } else if (module === 'pp') {
                                        if (response.getListByModulePP !== null) {
                                            dataExists = true;
                                            $('.firstPP').show();
                                            $('.spinnerLoadingPP').hide();
                                            $('#error_message_PP').hide();
                                        }
                                    } else if (module === 'ct') {
                                        if (response.getListByModuleCT !== null) {
                                            dataExists = true;
                                            $('.firstCT').show();
                                            $('.spinnerLoadingCT').hide();
                                            $('#error_message_CT').hide();
                                        }
                                    } else if (module === 'fl') {
                                        if (response.getListByModuleFL !== null) {
                                            dataExists = true;
                                            $('.firstFL').show();
                                            $('.spinnerLoadingFL').hide();
                                            $('#error_message_FL').hide();
                                        }
                                    } else if (module === 'qt') {
                                        var contentExists = {
                                            '#tajukContentQT': response.getListByModuleQT,
                                            '#iklanContentQT': response.getListByQtIklan,
                                            '#kodBidangContentQT': response
                                                .getListByQtKodBidang,
                                            '#loaContentQT': response.getListByQtLoa,
                                            '#tempasalContentQT': response
                                                .getListByQtTempasal
                                        } [contentId];
                                        if (contentExists !== null) {
                                            dataExists = true;
                                            if (contentId === '#tajukContentQT') {
                                                $('.firstQT').show();
                                                $('.spinnerLoadingQT').hide();
                                                $('#error_message_QT').hide();
                                            }
                                            if (contentId === '#iklanContentQT') {
                                                $('.secondQT').show();
                                                $('.spinnerLoadingQT2').hide();
                                                $('#error_message_QT2').hide();
                                            }
                                            if (contentId === '#kodBidangContentQT') {
                                                $('.thirdQT').show();
                                                $('.spinnerLoadingQT3').hide();
                                                $('#error_message_QT3').hide();
                                            }
                                            if (contentId === '#loaContentQT') {
                                                $('.forthQT').show();
                                                $('.spinnerLoadingQT4').hide();
                                                $('#error_message_QT4').hide();
                                            }
                                            if (contentId === '#tempasalContentQT') {
                                                $('.fifthQT').show();
                                                $('.spinnerLoadingQT5').hide();
                                                $('#error_message_QT5').hide();
                                            }
                                        }
                                    }

                                    if (dataExists) {
                                        handleResponse(response, module, toggleId, tableId,
                                            contentId);
                                    } else {
                                        handleNoData(module, toggleId, tableId);
                                    }
                                },
                                error: function(xhr, status, error) {}
                            });
                        } else {
                            $(contentId).collapse('hide');
                        }
                    });
                });
            }

            function handleResponse(response, module, toggleId, tableId, contentId) {
                var data;
                if (module === 'sm') {
                    data = response.getListByModuleSM;
                } else if (module === 'pp') {
                    data = response.getListByModulePP;
                } else if (module === 'qt') {
                    var keyMap = {
                        'tajukToggleQT': 'getListByModuleQT',
                        'iklanToggleQT': 'getListByQtIklan',
                        'kodBidangToggleQT': 'getListByQtKodBidang',
                        'loaToggleQT': 'getListByQtLoa',
                        'tempasalToggleQT': 'getListByQtTempasal'
                    };
                    var key = keyMap[toggleId];
                    data = response[key];
                } else if (module === 'ct') {
                    data = response.getListByModuleCT;
                } else if (module === 'fl') {
                    data = response.getListByModuleFL;
                }

                if (data !== null && data !== undefined) {
                    displayDataInTable(data, tableId);
                }
            }

            function handleNoData(module, toggleId, tableId) {
                var errorMessage;
                if (module === 'sm') {
                    $('#cptppSM_datatable').hide();
                    $('.spinnerLoadingSM').hide();
                    errorMessage = $('#error_message_SM');
                    errorMessage.text('No record Found!').show();
                } else if (module === 'pp') {
                    $('#cptppPP_datatable').hide();
                    $('.spinnerLoadingPP').hide();
                    errorMessage = $('#error_message_PP');
                    errorMessage.text('No record Found!').show();
                } else if (module === 'qt') {
                    if (toggleId === 'tajukToggleQT') {
                        $('#cptppQT_datatable').hide();
                        $('.spinnerLoadingQT').hide();
                        errorMessage = $('#error_message_QT');
                    }
                    if (toggleId === 'iklanToggleQT') {
                        $('#iklanQT_datatable').hide();
                        $('.spinnerLoadingQT2').hide();
                        errorMessage = $('#error_message_QT2');
                    }
                    if (toggleId === 'kodBidangToggleQT') {
                        $('#kodQT_datatable').hide();
                        $('.spinnerLoadingQT3').hide();
                        errorMessage = $('#error_message_QT3');
                    }
                    if (toggleId === 'loaToggleQT') {
                        $('#loaQT_datatable').hide();
                        $('.spinnerLoadingQT4').hide();
                        errorMessage = $('#error_message_QT4');
                    }
                    if (toggleId === 'tempasalToggleQT') {
                        $('#tempasalQT_datatable').hide();
                        $('.spinnerLoadingQT5').hide();
                        errorMessage = $('#error_message_QT5');
                    }
                    errorMessage.text('No record Found!').show();
                } else if (module === 'ct') {
                    $('#cptppCT_datatable').hide();
                    $('.spinnerLoadingCT').hide();
                    errorMessage = $('#error_message_CT');
                    errorMessage.text('No record Found!').show();
                } else if (module === 'fl') {
                    $('#cptppFL_datatable').hide();
                    $('.spinnerLoadingFL').hide();
                    errorMessage = $('#error_message_FL');
                    errorMessage.text('No record Found!').show();
                }
            }

            function displayDataInTable(data, tableId) {
                var tableBody = $(`${tableId} tbody`);
                tableBody.empty();
                data.forEach(function(item) {
                    var row = '<tr>' + Object.values(item).map(value => `<td>${value}</td>`).join('') + '</tr>';
                    tableBody.append(row);
                });

                if ($.fn.dataTable.isDataTable(tableId)) {
                    $(tableId).DataTable().clear();
                    $(tableId).DataTable().rows.add($(tableId + ' tbody tr'));
                    $(tableId).DataTable().draw();
                } else {
                    $(tableId).show();
                    $(tableId).DataTable();
                }
            }
        }
    </script>
@endsection
