@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Stuck Task Cancel POCO <br>
                <small>Stuck Task Cancel POCO</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>Stuck Task Cancel POCO List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck Task Cancel POCO List </strong>
                        <small></small>
                    </h1>
                </div>
             
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Created Date</th>
                            <th class="text-center">Action Cancelled</th>
                            <th class="text-center">Cancelled By</th>
                            <th class="text-center">Instance Id</th>
                            <th class="text-center">POCO NO.</th>
                            <th class="text-center">PRCR NO.</th>
                            <th class="text-center">STATUS NAME</th>
                            <th class="text-center">STATUS ID</th>
                            <th class="text-center">DOD</th>
                            <th class="text-center">IS YEP</th>
                            <th class="text-center">FULFILMENT ORDER ID</th>
                            <th class="text-center">FULFILMENT REQ ID</th>
                            <th class="text-center">CUR</th>
                            <th class="text-center">RECORD_STATUS</th>
                            

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->actioned_cncl }}</td>
                                <td class="text-center">{{ $data->cncl }}</td>
                                <th class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></th>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->doc_no  }}" >{{ $data->doc_no }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->prcr  }}" >{{ $data->prcr }}</a></td>
                                <td class="text-center">{{ $data->status_name }}</td>
                                <td class="text-center">{{ $data->status_id }}</td>
                                <td class="text-center">{{ $data->dod }}</td>
                                <td class="text-center">{{ $data->is_yep }}</td>
                                <td class="text-center">{{ $data->fulfilment_order_id }}</td>
                                <td class="text-center">{{ $data->fulfilment_req_id }}</td>
                                <td class="text-center">{{ $data->cur }}</td>
                                <td class="text-center">{{ $data->record_status }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



