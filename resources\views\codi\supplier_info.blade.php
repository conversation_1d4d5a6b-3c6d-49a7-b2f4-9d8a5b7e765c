@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form action="{{url('/find/codi/supplier')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="carian" name="carian" value="@if(isset($carian)){{$carian}}@endif" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

<!-- Courses Header -->
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="fa fa-globe"></i>Search <strong>Supplier</strong><br><small>MOF NO. or eP No. or IC No.</small>
        </h1>
    </div>
</div>

@if(isset($data))
<!-- Success Alert Content -->
<div class="alert alert-success alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-check-circle"></i> Found {{$data->count()}} records supplier</h4>
</div>
@foreach ($data as $key => $supplierInfo)
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <div class="block-options pull-right">
            <a href="javascript:void(0)" class="btn btn-alt btn-sm btn-primary" data-toggle="block-toggle-content"><i class="fa fa-arrows-v"></i></a>
        </div>
        <h1><i class="fa fa-building-o"></i> <strong>{{$key+1}}) ePerolehan (Pembekal) : {{$supplierInfo['supplier']->company_name}}</strong></h1>
    </div>
    <!-- Main Row -->
    <div class="row">
        <div class="col-md-12">
            <!-- Your Account Block -->
            <div class="block">
                <!-- Your Account Title -->
                <div class="block-title">
                    <h2><strong>Supplier</strong> Info</h2>
                </div>
                <!-- END Your Account Title -->

                <!-- Your Account Content -->
                <div class="block-section">
                    <table class="table table-borderless table-striped table-vcenter">
                        <tbody>
                            <tr>
                                <td class="text-right">Company Name</td>
                                <td>
                                    <strong>{{$supplierInfo['supplier']->company_name}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-right">eP No.</td>
                                <td>
                                    <strong>{{$supplierInfo['supplier']->ep_no}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-right">MOF No.</td>
                                <td>
                                    <strong>{{$supplierInfo['supplier']->mof_no}}</strong>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-right">Reg No. / SSM No.</td>
                                <td>
                                    <strong>{{$supplierInfo['supplier']->reg_no}}</strong>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!-- END Your Account Content -->
            </div>
            <!-- END Your Account Block -->
        </div>
        <div class="col-md-12">
            <!-- Most Viewed Courses Block -->
            <div class="block">
                <!-- Most Viewed Courses Title -->
                <div class="block-title text-info">
                    <h2><strong>Users </strong>  Info </h2>  
                </div>
                <!-- END Most Viewed Courses Title -->
                <div class="block-section">
                    <div class="row">
                        <div class="col-md-12">
                            @if(isset($supplierInfo['listPersonnel']) && $supplierInfo['listPersonnel'] != null )
                            @foreach ($supplierInfo['listPersonnel'] as $key => $personnel)
                            <div class="block">
                                <!-- Your Account Title -->
                                <div class="block-title">
                                    <h2><strong>{{$key+1}}) {{$personnel->p_name}}</h2>
                                    <span>{{$personnel->p_ep_role}}</span>
                                </div>
                                <!-- END Your Account Title -->

                                <!-- Your Account Content -->
                                <div class="block-section">
                                    <div class="row">
                                        <div class="col-md-12">
                                            <!-- Your Account Block -->
                                            <div class="block">
                                                <!-- Your Account Title -->
                                                <div class="block-title">
                                                    <h2><strong>Personnel</strong> Info</h2>
                                                </div>
                                                <!-- END Your Account Title -->

                                                <!-- Your Account Content -->
                                                <div class="block-section">
                                                    <table class="table table-borderless table-striped table-vcenter">
                                                        <tbody>
                                                            <tr>
                                                                <td class="text-right">Name</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_name}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">Email</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_email}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">IC No.</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_identification_no}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">Mobile</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_mobile_country}}{{$personnel->p_mobile_area}}{{$personnel->p_mobile_no}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">Designation</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_designation}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">Record Status</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_record_status}}</strong>
                                                                </td>
                                                            </tr>
                                                            <tr>
                                                                <td class="text-right">Softcert Status</td>
                                                                <td>
                                                                    <strong>{{$personnel->p_is_softcert}}</strong>
                                                                </td>
                                                            </tr>
                                                        </tbody>
                                                    </table>
                                                </div>
                                                <!-- END Your Account Content -->
                                            </div>
                                            <!-- END Your Account Block -->
                                        </div>
                                        @if(isset($personnel->listSoftCert) && $personnel->listSoftCert != null )
                                            <div class="col-md-12">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Softcert</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <div class="table-responsive">
                                                            <table class="table  table-condensed table-bordered table-vcenter">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Request ID</th>
                                                                        <th>Provider</th>
                                                                        <th>Record Status</th>
                                                                        <th>Created</th>
                                                                        <th>Is Free</th>
                                                                        <th>Apply Document</th>
                                                                        <th>Issuer</th>
                                                                        <th>Valid From</th>
                                                                        <th>Valid To</th>
                                                                        <th>Cert Updated</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    @foreach($personnel->listSoftCert as $softcert)
                                                                    <tr @if($softcert->is_active_cert === true) class="info" @endif>
                                                                        <td>{{$softcert->softcert_request_id}} {{$softcert->is_active_cert}}</td>
                                                                        <td>{{$softcert->softcert_provider}}</td>
                                                                        <td>{{$softcert->record_status}}
                                                                        @if($softcert->record_status == 1 && $softcert->softcert_provider == 'TG' && $personnel->p_is_softcert == 3) 
                                                                            <a target="_blank" href="https://www.msctrustgate.com/mytrustid/client/cdc_support?token=sign" 
                                                                                style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                                RESEND UPDATE SOFTCERT <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt;" title="Check on TG Portal if already issued! "></i>
                                                                            </a>
                                                                        @endif

                                                                        </td>
                                                                        <td>{{$softcert->created_date}}</td>
                                                                        <td>{{$softcert->is_free}}</td>
                                                                        <td>{{$softcert->type_apply}}</td>
                                                                        <td> @if($softcert->valid_to != null) @if($softcert->cert_issuer=='T') TrustGate @else Digicert @endif @endif</td>
                                                                        <td>{{$softcert->valid_from}}</td>
                                                                        <td>{{$softcert->valid_to}}</td>
                                                                        <td>{{$softcert->pdc_changed_date}}</td>
                                                                    </tr>
                                                                    @endforeach
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!-- END Your Account Content -->

                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>
                                            @endif
                                    </div>

                                </div>
                                <!-- END Your Account Content -->
                            </div>

                            @endforeach
                            @else
                            <div class="notify-alert alert alert-warning alert-dismissable">
                                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4> 
                            </div>
                            @endif
                        </div>
                    </div>
                </div>
                <!-- END Most Viewed Courses Content -->
            </div>
            <!-- END Most Viewed Courses Block -->
        </div>
    </div>

</div>
</div> 
@endforeach
@endif
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>
<script>


</script>
@endsection



