@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/gfmas/materialCode/')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... " @if(isset($formSearch, $formSearch["cari"])) value="{{ $formSearch["cari"] }}" @endif>
       
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Senarai Kod Item yang dihantar ke 1GFMAS (Service MMINF)<br>
                <small>Masukka<PERSON> Kod Item pada carian diatas...</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai MMINF Code </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">MMINF ID</th>
                            <th class="text-center">Material Code</th>
                            <th class="text-center">Code Type</th>
                            <th class="text-center">Action Code</th>
                            <th class="text-center">Material_Desc</th>
                            <th class="text-center">Material Add Desc</th>
                            <th class="text-center">Base UOM</th>
                            <th class="text-center">Alt UOM</th>
                            <th class="text-center">Is Sent</th>
                            <th class="text-center">Created Date</th>
                            <th class="text-center">Changed Date</th>
<!--                            <th class="text-center">Action</th>-->
                            
                            
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                               
                                <td class="text-center">                                              
                                 <a href="{{url('/find/gfmas/mminfid/')}}/{{ $data->mminf_id }}/{{ $data->material_code }}" target="_blank">{{ $data->mminf_id }}</a>                                            
                                </td>
                                <td class="text-center">{{ $data->material_code }}</td>
                                <td class="text-center">{{ $data->code_type }}</td>
                                <td class="text-center">{{ $data->action_code }}</td>
                                <td class="text-center">{{ $data->material_desc }}@if($data->notAsciiMaterialDesc == true)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="special character Non-ASCII detected!"></i>@endif</td>
                                <td class="text-center">{{ $data->material_add_desc }}@if($data->notAsciiMaterialAddDesc == true)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="special character Non-ASCII detected!"></i>@endif</td>
                                <td class="text-center">{{ $data->base_uom }}</td>
                                <td class="text-center">{{ $data->alt_uom }}</td>
                                <td class="text-center">{{ $data->is_sent }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center td-{{ $data->mminf_id }}">{{ $data->changed_date }}</td>
<!--                            <td class="text-center action_table">
                                <input name="current_changed_date" id="current_changed_date"  type="hidden" value="{{ $data->changed_date }}">    
                                <input name="material_code" id="material_code"  type="hidden" value="{{ $data->material_code }}">
                                <input name="mminfId" id="mminfId"  type="hidden" value="{{ $data->mminf_id }}"> 
                                <div class="btn-group btn-group-xs">
                                    <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                                       data-url="" 
                                       href="#modal_confirm_trigger" data-toggle="modal" data-id="{{ $data->material_code }}" data-id2="{{ $data->mminf_id }}"><i class="gi gi-search"></i> Kemaskini</a>
                                </div>
                            </td>-->
                                
                                
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    <div id="modal_confirm_trigger" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title" id="modal-confirm-title"> Are you sure want to update is Sent to 0? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                {{ csrf_field() }}

                                <div class="form-group">
                                     <input type="hidden" id="trigger_materialCode" value="" />
                                        <input type="hidden" id="trigger_rqurl" value="" />
                                    <div class="col-md-9">
                                        <label class="col-md-3 control-label" id="label-request-item-id">Material Code :</label>                                       
                                        <p id="trigger_materialCode_display" class="form-control-static"></p>
                                    </div>
                                    <div class="col-md-9">                                        
                                        <label class="col-md-3 control-label" id="label-request-item-id">MMINF Id :</label>
                                        <p id="trigger_mminfid_display" class="form-control-static"></p>
                                    </div>                                    
                                    <div class="col-md-9">
                                        <label class="col-md-3 control-label" for="remarks">Case Number <span class="text-danger">*</span> :</label>
                                        <input type="text" id="remarks" name="remarks" class="form-control" >
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_trigger"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
        <!-- MODAL: K -->
        <div id="modal-item" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <!-- Modal Header -->
                    <div class="modal-header text-center">
                        <h2 class="modal-title"><i class="fa fa-list"></i><span id="modal-list-data-header"> List Items </span></h2>
                    </div>
                    <!-- END Modal Header -->

                    <!-- Modal Body -->
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-sm-12">
                                <div class="text-center spinner-loading" style="padding: 20px;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div class="table-responsive">
                                    <table id="item-datatable" class="table table-striped table-vcenter">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- END Modal Body -->
                </div>
            </div>
        </div>
    @endif


@endsection

@section('jsprivate')

<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    
    App.datatables();
    /* Initialize Datatables */
    var tableListData =     $('#item-datatable').DataTable({
            columnDefs: [ { orderable: false, targets: [ 0 ] } ],
            pageLength: 10,
            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });
        
        
    $(document).ready(function () {
        $('.content-link').on("click",'.modal-list-data-action', function(){
                
            $('.spinner-loading').show();
            $('#item-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url:  $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#item-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#item-datatable').DataTable({
                        order: [[ 1, "desc" ]],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
    });
    
    //Confirm Trigger Dialogue
        $('td.action_table').on("click",'a.action_trigger', function(){
            $("#modal-confirm-title").text(' Are you sure want to update is Sent to 0? ');
            $("#label-request-item-id").text('Material Code :');

            var materialCode = $(this).attr('data-id');
            var urlMminf = $(this).attr('data-url');
            var changedDate = $("#current_changed_date").val();
            var mminfId = $(this).attr('data-id2');
            console.log("MaterialCode: "+materialCode);
            console.log("changedDate: "+changedDate);
            console.log("urlMminf: "+urlMminf);
            console.log("MminfCode: "+mminfId);
            $("#trigger_rqurl").val(urlMminf);
            $("#trigger_materialCode").val(mminfId);
            $("#trigger_mminfid").val(materialCode);
            $("#trigger_materialCode_display").text(materialCode);
            $("#trigger_mminfid_display").text(mminfId);
        });
        
        $('div.form-actions').on("click",'button.action_confirm_trigger', function(){
            
            var materialCode =$("#trigger_materialCode").val();
            var mminfId =$("#trigger_mminfid").val();
            var changedDate = $("#current_changed_date").val();
            var trigger_rqurl = $("#trigger_rqurl").val();
            var csrf = $("input[name=_token]").val();

            $('#modal_confirm_trigger').modal('hide');
            $('#wait-modal').modal('toggle');

            $.ajax({
                url: trigger_rqurl,
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"mminf_id":mminfId,"changed_date":changedDate,"material_code":materialCode,"mminfId":mminfId},
                context: document.body
            }).done(function(resp) {
                if(resp.status === 'success'){
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Triggered!");
  
                    if(resp.hasOwnProperty('total_items')){
                       $("#response-msg").html("Successfully "+resp.total_items+" Items Triggered! Please refresh search.");
                    }    
                    $("td.td-"+materialCode).addClass("text-success");
                    $("td.td-"+materialCode).html(resp.value);
                    $('#wait-modal').modal('hide');
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Trigger Failed!.");
                    
                    if(resp.hasOwnProperty('status_error')){
                       $("#response-msg").html("Trigger Failed! Please try again."+resp.status_error);
                    } 
                    
                    $("td.td-"+materialCode).addClass("text-danger");
                    $('#wait-modal').modal('hide');
                }
            });
        });
</script>
@endsection



