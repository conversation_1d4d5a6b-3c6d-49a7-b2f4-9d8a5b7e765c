@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('/find/qt/suppresp')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="doc_no" name="doc_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Masukkan No QT & Mulakan Carian ...">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>SEMAKAN MAKLUM BALAS PEMBEKAL (LANJUTAN TEMPOH SAH LAKU QT)
        </h1>
    </div>
</div> 

@if($suppres == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Rekod Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif

<!--Akmal Region SEMAKAN SUPPLIER RESPOND  -->
@if($suppres)
<div class="block block-alt-noborder full">
    <div class="block">   
        <div class="block-title panel-heading epss-title-s1">
            <h5><i class="fa fa-building-o"></i><strong> SEMAKAN SENARAI PEMBEKAL (DATABASE)<font color="yellow"></font></strong></h5>
        </div>
        <div class="row">
            <div class="col-md-12">
                <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT No</th>
                            <th class="text-center">Supplier ID</th>
                            <th class="text-left">Supplier Name</th>
                            <th class="text-left">MOF No</th>
                            <th class="text-center">Respond</th>       
                        </tr>
                    </thead>
                    @if($suppres != null)
                    @foreach ($suppres as $key=>$spp)
                    <tr>
                        <td class="text-center">{{ $spp->qt_no}}</td> 
                        <td class="text-center">{{ $spp->supplier_id }}</td>
                        <td class="text-left">{{ strtoupper($spp->company_name) }}</td>
                        <td class="text-center"><a href="{{url('/find/epno')}}/{{ $spp->mof_no }}" target='_blank' >
                                {{ $spp->mof_no }}</a></td>  
                        @if($spp->is_agree_qt_extend_period == null)
                        <td class="text-center">RESET<i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title="Reset! "></td> 
                        @elseif($spp->is_agree_qt_extend_period != null && $spp->is_agree_qt_extend_period == '0')
                        <td class="text-center">NOT YET<i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title="Not Yet! "></td> 
                        @elseif($spp->is_agree_qt_extend_period != null && $spp->is_agree_qt_extend_period == '1')
                        <td class="text-center">DONE <i class="gi gi-circle_exclamation_mark text-success" style="font-size: 10pt;" title="Done! "></td> 
                        @endif
                    </tr>
                    @endforeach  
                    @endif
                </table>
            </div>
        </div>
    </div>
</div>

<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-tasks"></i> <strong></strong></h1>
            <h5><i class="fa fa-building-o"></i><strong>  SEMAKAN SENARAI TINDAKAN (BPM SOA - Supplier Accept QT Change)<font color="yellow"></font></strong></h5><br>
        </div>

        <div class="table-responsive">
            <table id="basic-datatable" class="table table table-vcenter table-striped">
                <thead>
                    <tr>
                        <th class="text-center">Doc No</th>
                        <th class="text-center">Instance ID</th> 
                        <th class="text-center">Outcome</th>
                        <th class="text-center">Create Date</th>
                        <th class="text-center">Expiry Date</th>
                        <th class="text-center">Assignees (Click for Supplier Info)</th>
                    </tr>
                </thead>              
                <tbody>
                    @if(count($listResult2) > 0)     
                    @foreach ($listResult2 as $data)
                    <tr>
                        <td class="text-center">{{ $data['docNumber'] }}</td>
                        <td class="text-center">{{ $data['instanceId'] }}</td>

                        <td class="text-center"><strong><font color="blue">{{$data['outcome']}}<strong></td> 

                        <td class="text-center">{{ $data['createDateString'] }}</td>
                        <td class="text-center">{{ $data['expirationDateString'] }}</td>
                        <td class="text-center content-link">
                            @foreach ($data['assignees'] as $dt)
                            <a class="modal-list-data-action"
                               href="{{url('/find/userlogin?')}}login_id={{$dt}}"
                               target='_blank' >
                                {{$dt}} | </a>
                            @endforeach
                        </td>
                    </tr>
                    @endforeach
                    @else
                    <tr>
                        <td class="text-center" colspan="12">No Records</td>
                    </tr>    
                    @endif
                </tbody>
            </table>

        </div>


    </div>
    <!-- END Customer Addresses Block -->
</div>








@endif

@endsection
@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>

@endsection