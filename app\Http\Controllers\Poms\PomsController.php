<?php

namespace App\Http\Controllers\Poms;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PomsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function checkSystemAvailability()
    {
        return view('poms.checkSystemAvailability');
    }

    public function calculateSlaApi(Request $request)
    {
        try {
            $year = (int) $request->input('year');
            $month = (int) $request->input('month');
            $nagiosInterval = (int) $request->input('nagios_interval');

            $result = $this->calculateSla($year, $month, $nagiosInterval);

            return response()->json($result, 200);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    private function getNumOfSaDown($year, $month)
    {
        $query = "
        SELECT COUNT(DISTINCT downtime) AS num_of_sa_down
        FROM (
            SELECT a.*, 
            CASE
                WHEN a.host_group = 'portal' AND a.counter >= 18 THEN 'YES'
                WHEN a.host_group = 'bpm' AND a.counter >= 20 THEN 'YES'
                WHEN a.host_group = 'database' AND a.counter >= 7 THEN 'YES'
                WHEN a.host_group = 'web' AND a.counter >= 10 THEN 'YES'
                WHEN a.host_group = 'network' AND a.counter >= 1 THEN 'YES'
                WHEN a.host_group = 'sso' AND a.counter >= 14 THEN 'YES'
                WHEN a.host_group = 'solr' AND a.counter >= 2 THEN 'YES'
                ELSE 'NO'
            END AS all_down_flag
            FROM (
                SELECT host_group, 
                DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i') AS downtime,
                COUNT(1) AS counter
                FROM sla_nagios
                WHERE host_group IN ('sso', 'portal', 'database', 'bpm', 'solr', 'web', 'network')
                AND is_exclude = 0
                AND service_status <> '2'
                AND MONTH(updated_at) = ?
                AND YEAR(updated_at) = ?
                AND (
                    DAYOFWEEK(updated_at) NOT IN (6, 7)
                    OR (DAYOFWEEK(updated_at) = 6 AND TIME(updated_at) NOT BETWEEN '22:00:00' AND '23:59:00')
                    OR (DAYOFWEEK(updated_at) = 7 AND TIME(updated_at) NOT BETWEEN '00:00:00' AND '06:00:00')
                )
                GROUP BY host_group, DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i')
            ) a
        ) b
        WHERE all_down_flag = 'YES'
        ORDER BY downtime DESC
        ";

        $result = DB::connection('mysql_poms')->select($query, [$month, $year]);

        return $result ? $result[0]->num_of_sa_down : 0;
    }

    private function getScheduleDowntime($year, $month)
    {
        $fridays = [];
        $totalMinutes = 0;
        $firstDay = Carbon::create($year, $month, 1);

        if ($firstDay->dayOfWeek == 6) {
            $totalMinutes += 360; // 6 hours (Midnight to 6 AM)
            $fridays[] = $firstDay->format('Y-m-d');
        }

        for ($day = 1; $day <= $firstDay->daysInMonth; $day++) {
            $currentDate = Carbon::create($year, $month, $day);
            if ($currentDate->dayOfWeek == 5) {
                $totalMinutes += ($day == $firstDay->daysInMonth) ? 120 : 480;
                $fridays[] = $currentDate->format('Y-m-d');
            }
        }

        return [
            'fridays' => $fridays,
            'total_minutes' => $totalMinutes
        ];
    }

    private function getMinutesFromPercentage($availableMinutes, $percentage)
    {
        return ($percentage / 100) * $availableMinutes;
    }

    private function calculateSla($year, $month, $nagiosInterval)
    {
        $numOfSaDown = $this->getNumOfSaDown($year, $month);
        $totalDays = Carbon::create($year, $month, 1)->daysInMonth;
        $totalMinutes = $totalDays * 24 * 60;
        $scheduledDowntime = $this->getScheduleDowntime($year, $month);
        $availableMinutes = $totalMinutes - $scheduledDowntime['total_minutes'];

        $saDowntime = $numOfSaDown * $nagiosInterval;
        $sla = round((($availableMinutes - $saDowntime) / $availableMinutes) * 100, 2);
        $slaNonCompliance = max(99.6 - $sla, 0);
        $nonComplyMinutes = $this->getMinutesFromPercentage($availableMinutes, $slaNonCompliance);
        $overallNonCompliance = 100 - $sla;
        $penaltyAmount = $nonComplyMinutes * 10;

        return [
            "year" => $year,
            "month" => $month,
            "fridays" => $scheduledDowntime['fridays'],
            "total_minutes" => $totalMinutes,
            "total_days" => $totalDays,
            "scheduled_downtime" => $scheduledDowntime['total_minutes'],
            "available_minutes" => $availableMinutes,
            "total_downtime" => $saDowntime,
            "num_of_sa_down" => $numOfSaDown,
            "sla_percentage" => $sla,
            "overall_non_compliance" => round($overallNonCompliance, 2),
            "sla_non_compliance" => round($slaNonCompliance, 2),
            "non_compliance_minutes" => round($nonComplyMinutes, 3),
            "penalty_amount" => round($penaltyAmount, 2)
        ];
    }

    public function incidentTasks(Request $request)
    {
        $caseNumber = $request->input('case_number');
        $showDeleted = $request->input('show_deleted');
        $dbConnection = $request->input('db_connection', 'mysql_crm_archive');
        $tasks = collect();
        $users = collect();

        if ($caseNumber) {
            $query = "
            SELECT
            c1.case_number,
            t2.task_number_c,
            t1.name,
            t2.sla_task_flag_c,
            u_created.user_name AS created_by,
            u_modified.user_name AS modified_by,
            CONVERT_TZ(t1.date_entered, '+00:00', '+08:00') AS date_entered,
            CONVERT_TZ(t1.date_modified, '+00:00', '+08:00') AS date_modified,
            t1.deleted,
            CONVERT_TZ(t1.date_start, '+00:00', '+08:00') AS date_start,
            CONVERT_TZ(t1.date_due, '+00:00', '+08:00') AS date_due,
            t1.created_by, 
            t1.modified_user_id, 
            CONVERT_TZ(t2.sla_start_15min_c, '+00:00', '+08:00') AS sla_start_15min_c,
            CONVERT_TZ(t2.sla_stop_15min_c, '+00:00', '+08:00') AS sla_stop_15min_c,
            CONVERT_TZ(t2.sla_start_4hr_c, '+00:00', '+08:00') AS sla_start_4hr_c,
            CONVERT_TZ(t2.sla_stop_4hr_c, '+00:00', '+08:00') AS sla_stop_4hr_c,
            CONVERT_TZ(t2.sla_start_approver_c, '+00:00', '+08:00') AS sla_start_approver_c,
            CONVERT_TZ(t2.sla_stop_approver_c, '+00:00', '+08:00') AS sla_stop_approver_c,
            CONVERT_TZ(t2.acknowledge_time_c, '+00:00', '+08:00') AS acknowledge_time_c,
            CONVERT_TZ(t2.date_execution_time_c, '+00:00', '+08:00') AS date_execution_time_c,
            t1.id, 
            t2.id_c
        FROM cases c1
        JOIN cases_cstm c2 ON c1.id = c2.id_c
        JOIN tasks t1 ON t1.parent_id = c1.id AND t1.parent_type = 'Cases'
        JOIN tasks_cstm t2 ON t1.id = t2.id_c
        LEFT JOIN users u_created ON u_created.id = t1.created_by
        LEFT JOIN users u_modified ON u_modified.id = t1.modified_user_id
        WHERE c1.case_number = ?
        ";

            // Add deleted filter if not showing deleted items
            if (!$showDeleted) {
                $query .= " AND t1.deleted = 0";
            }

            $query .= " ORDER BY t2.task_number_c";

            $tasks = collect(DB::connection($dbConnection)->select($query, [$caseNumber]));

            $users = DB::connection($dbConnection)->table('users')->select('id', 'user_name')->orderBy('user_name', 'asc')->get();
        }

        // $slaSummary = collect(); // Initialize as empty collection
        // if ($caseNumber) { // Only fetch SLA summary if caseNumber is present
        //     $slaSummary = $this->getSlaSummary($caseNumber);
        // }
        // dd($slaSummary);

        return view('poms.incidentTasksFix', compact('tasks', 'users', 'caseNumber', 'showDeleted', 'dbConnection'));
    }

    public function updateTask(Request $request)
    {
        $id = $request->input('id');
        $field = $request->input('field');
        $value = $request->input('value');
        $dbConnection = $request->input('db_connection', 'mysql_crm_archive');

        // Define which fields belong to which table
        $tasksFields = [
            'name',
            'date_entered',
            'date_modified',
            'date_start',
            'date_due',
            'created_by',
            'modified_user_id',
            'deleted'
        ];

        $tasksCstmFields = [
            'task_number_c',
            'sla_task_flag_c',
            'sla_start_15min_c',
            'sla_stop_15min_c',
            'sla_start_4hr_c',
            'sla_stop_4hr_c',
            'sla_start_approver_c',
            'sla_stop_approver_c',
            'acknowledge_time_c',
            'date_execution_time_c'
        ];

        try {
            // Convert date fields back to UTC 0
            if (in_array($field, [
                'date_entered',
                'date_modified',
                'date_start',
                'date_due',
                'sla_start_15min_c',
                'sla_stop_15min_c',
                'sla_start_4hr_c',
                'sla_stop_4hr_c',
                'sla_start_approver_c',
                'sla_stop_approver_c',
                'acknowledge_time_c',
                'date_execution_time_c'
            ])) {
                $value = $value ? \Carbon\Carbon::parse($value, 'Asia/Singapore')->tz('UTC')->toDateTimeString() : null;
            }

            // Determine which table to update
            if (in_array($field, $tasksFields)) {
                $updated = DB::connection($dbConnection)->table('tasks')
                    ->where('id', $id)
                    ->update([$field => $value]);
            } elseif (in_array($field, $tasksCstmFields)) {
                $updated = DB::connection($dbConnection)->table('tasks_cstm')
                    ->where('id_c', $id)
                    ->update([$field => $value]);
            } else {
                return response()->json(['success' => false, 'message' => 'Invalid field']);
            }

            return response()->json(['success' => $updated]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ]);
        }
    }

    public function getSlaSummary($caseNumber)
    {
        if (!$caseNumber) {
            return collect(); // Return empty collection if no case number
        }
        $results = DB::connection('mysql_poms')->table('view_incident_sla_summary')
            ->where('case_number', $caseNumber)
            ->get();

        return $results;
    }

    private function getSlaTableAndFieldMap($slaType, $fieldAlias)
    {
        $commonDateFields = [
            'start_datetime', 'due_datetime', 'actual_start_datetime', 'completed_datetime'
        ];
        $commonOtherFields = ['actual_duration', 'deleted'];

        $map = [
            'cs' => [
                'table' => 'sla_cs',
                'prefix' => 'cs_',
                'pk_columns' => ['case_number' => 'case_number']
            ],
            'acknowledge' => [
                'table' => 'sla_itcoord',
                'prefix' => 'itcoord_',
                'pk_columns' => ['case_number' => 'case_number', 'task_number' => 'itcoord_task_number']
            ],
            'rit' => [
                'table' => 'sla_itspec',
                'prefix' => 'itspec_',
                'pk_columns' => ['case_number' => 'case_number', 'task_number' => 'itspec_task_number']
            ],
            's123' => [
                'table' => 'sla_itseverity',
                'prefix' => 'itseverity_',
                'pk_columns' => ['case_number' => 'case_number', 'task_number' => 'itseverity_task_number']
            ],
            's4' => [
                'table' => 'sla_byapprover',
                'prefix' => 'itapprover_',
                'pk_columns' => ['case_number' => 'case_number', 'task_number' => 'itapprover_task_number']
            ],
        ];

        if (!isset($map[$slaType])) {
            return null;
        }

        $tableConfig = $map[$slaType];
        $actualFieldName = null;

        if (in_array($fieldAlias, $commonDateFields)) {
            $actualFieldName = $tableConfig['prefix'] . $fieldAlias;
        } elseif ($fieldAlias === 'actual_duration') {
            $actualFieldName = $tableConfig['prefix'] . 'actual_duration';
        } elseif ($fieldAlias === 'deleted') {
            $actualFieldName = 'deleted'; // 'deleted' field is common and often not prefixed in the same way
             // Verify this assumption for each table or adjust if 'deleted' has a prefix too.
             // For example, if sla_cs.deleted is just 'deleted', this is fine.
        }


        if (!$actualFieldName) {
             // Check if the field exists directly without prefix (e.g. 'deleted')
            $tempDbCheck = DB::connection('mysql_poms')->getSchemaBuilder()->getColumnListing($tableConfig['table']);
            if(in_array($fieldAlias, $tempDbCheck)){
                $actualFieldName = $fieldAlias;
            } else {
                 return null;
            }
        }


        // Get the prefix from our map configuration
        $prefix = $this->map[$slaType]['prefix'] ?? '';

        return [
            'table_name' => $tableConfig['table'],
            'field_name' => $actualFieldName,
            'pk_map' => $tableConfig['pk_columns'],
            'prefix' => $prefix
        ];
    }

    public function updateSlaSummary(Request $request)
    {
        $caseNumber = $request->input('case_number');
        $taskNumber = $request->input('task_number'); // Can be null or empty string
        $slaType = $request->input('sla_type');
        $fieldAlias = $request->input('field');
        $value = $request->input('value');

        Log::info("SLA Summary Update Request:", $request->all());


        if (empty($caseNumber) || empty($slaType) || empty($fieldAlias)) {
            return response()->json(['success' => false, 'message' => 'Missing required parameters.'], 400);
        }

        $mapping = $this->getSlaTableAndFieldMap($slaType, $fieldAlias);

        if (!$mapping) {
            Log::error("SLA Summary Update: Invalid slaType or fieldAlias.", ['slaType' => $slaType, 'fieldAlias' => $fieldAlias]);
            return response()->json(['success' => false, 'message' => 'Invalid field or SLA type.'], 400);
        }

        $tableName = $mapping['table_name'];
        $actualField = $mapping['field_name'];
        $pkMap = $mapping['pk_map'];

        // Date fields that need timezone conversion and formatting
        $dateFieldsToConvert = [
            $mapping['prefix'] . 'start_datetime' ?? '',
            $mapping['prefix'] . 'due_datetime' ?? '',
            $mapping['prefix'] . 'actual_start_datetime' ?? '',
            $mapping['prefix'] . 'completed_datetime' ?? '',
        ];
        // Remove empty strings if prefix was not found (e.g. for 'deleted' field if it has no prefix mapping directly)
        $dateFieldsToConvert = array_filter($dateFieldsToConvert);


        try {
            if (in_array($actualField, $dateFieldsToConvert)) {
                $value = $value ? Carbon::parse($value, 'Asia/Singapore')->tz('UTC')->toDateTimeString() : null;
            } elseif ($actualField === 'deleted') {
                $value = ($value == '1' || $value === true) ? 1 : 0;
            } elseif (str_ends_with($actualField, 'actual_duration')) {
                 // Assuming duration is stored as a number (e.g., minutes or seconds)
                $value = is_numeric($value) ? $value : null;
            }


            $query = DB::connection('mysql_poms')->table($tableName);

            foreach ($pkMap as $viewKey => $tableColumn) {
                if ($viewKey === 'case_number') {
                    $query->where($tableColumn, $caseNumber);
                } elseif ($viewKey === 'task_number') {
                    // Task number might be null or an empty string from the request
                    if (!empty($taskNumber) && $taskNumber !== 'na') { // 'na' was used as a placeholder for null task_numbers in data-id
                        $query->where($tableColumn, $taskNumber);
                    } elseif (empty($taskNumber) || $taskNumber === 'na') {
                        // If task_number from view is null/empty, the source table might store it as NULL
                        // This depends on how NULL task numbers are handled in the PKs of source tables
                        // For safety, if task_number is crucial for PK and is empty, this might fail or update wrong rows.
                        // For now, we assume if $taskNumber is empty, it means the PK doesn't use it or it's NULL
                        // If the column in DB must be NULL:
                        // $query->whereNull($tableColumn);
                        // However, if it's part of a composite PK, it usually cannot be NULL.
                        // This logic might need adjustment based on actual DB schema for tables where task_number can be NULL but is part of PK.
                        // If task_number can be legitimately NULL and is part of the identifier:
                        if (DB::connection('mysql_poms')->getSchemaBuilder()->hasColumn($tableName, $tableColumn)) {
                            $query->whereNull($tableColumn); // More robust if the PK can actually be NULL
                        }

                    }
                }
            }
            
            // Check if any where clauses were added (i.e., if PK was resolved)
            // This is a basic check; more sophisticated validation of PK presence might be needed.
            if (empty($query->wheres)) {
                 Log::error("SLA Summary Update: Could not build WHERE clause for update.", $request->all());
                 return response()->json(['success' => false, 'message' => 'Could not identify the record to update. Primary key information missing or invalid.'], 400);
            }


            $updated = $query->update([$actualField => $value]);

            if ($updated) {
                return response()->json(['success' => true, 'message' => 'SLA summary updated successfully.']);
            } else {
                 // Check if the record exists with the old value, maybe no change was needed
                 // Or if the record was not found by the PKs
                Log::warning("SLA Summary Update: Update returned 0 affected rows.", $request->all());
                return response()->json(['success' => false, 'message' => 'Record not found or no change made.']);
            }

        } catch (\Exception $e) {
            Log::error("SLA Summary Update Error: " . $e->getMessage(), [
                'request' => $request->all(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'An error occurred: ' . $e->getMessage()
            ], 500);
        }
    }

    public function fetchSlaSummary(Request $request)
    {
        $caseNumber = $request->input('case_number');

        $slaSummaries = DB::connection('mysql_poms')
            ->table('view_incident_sla_summary')
            ->where('case_number', $caseNumber)
            ->get();

        return view('poms.partials.sla_summary', compact('slaSummaries'))->render();
    }


}
