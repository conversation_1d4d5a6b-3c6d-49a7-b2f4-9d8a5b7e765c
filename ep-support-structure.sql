/*
SQLyog Ultimate v12.09 (64 bit)
MySQL - 5.7.22-log : Database - ep_support
*********************************************************************
*/


/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`ep_support` /*!40100 DEFAULT CHARACTER SET latin1 */;

USE `ep_support`;

/*Table structure for table `ep_action_log` */

DROP TABLE IF EXISTS `ep_action_log`;

CREATE TABLE `ep_action_log` (
  `id` bigint(12) unsigned NOT NULL AUTO_INCREMENT,
  `action_name` varchar(255) NOT NULL,
  `action_type` varchar(255) NOT NULL,
  `action_data` mediumtext NOT NULL,
  `action_parameter` mediumtext,
  `action_status` varchar(100) NOT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(200) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(200) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_activity_access` */

DROP TABLE IF EXISTS `ep_activity_access`;

CREATE TABLE `ep_activity_access` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `username` varchar(100) NOT NULL,
  `access_url` varchar(255) NOT NULL,
  `access_detail` varchar(255) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_bpm_task_orphan` */

DROP TABLE IF EXISTS `ep_bpm_task_orphan`;

CREATE TABLE `ep_bpm_task_orphan` (
  `id` int(8) unsigned NOT NULL AUTO_INCREMENT,
  `composite_id` varchar(20) DEFAULT NULL,
  `composite_name` varchar(100) DEFAULT NULL,
  `composite_module` varchar(100) DEFAULT NULL,
  `composite_id_module` varchar(100) DEFAULT NULL,
  `component_instance_id` varchar(20) DEFAULT NULL,
  `component_name` varchar(100) DEFAULT NULL,
  `component_creation_date` datetime DEFAULT NULL,
  `doc_no` varchar(50) DEFAULT NULL,
  `doc_type` varchar(20) DEFAULT NULL,
  `activity_name` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `remarks` varchar(150) DEFAULT NULL,
  `is_terminate_instance` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'Action on program to terminate this instance. 0: ready to terminate, 1: success terminate, 2: failed terminate',
  `terminate_at` datetime DEFAULT NULL,
  `terminate_response` text,
  PRIMARY KEY (`id`),
  UNIQUE KEY `composite` (`composite_id`,`composite_module`,`component_name`,`doc_no`,`component_instance_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_crm_monitoring` */

DROP TABLE IF EXISTS `ep_crm_monitoring`;

CREATE TABLE `ep_crm_monitoring` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `full_name` varchar(100) NOT NULL,
  `total` int(20) DEFAULT NULL,
  `category` varchar(255) DEFAULT NULL,
  `module` varchar(255) DEFAULT NULL,
  `case_no` int(10) DEFAULT NULL,
  `doc_no` varchar(255) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `date_resolve` date DEFAULT NULL,
  `remarks` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `id` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1663 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_integration_statistic` */

DROP TABLE IF EXISTS `ep_integration_statistic`;

CREATE TABLE `ep_integration_statistic` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `group` varchar(30) DEFAULT NULL,
  `module` varchar(30) DEFAULT NULL,
  `service_code` varchar(7) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `total` bigint(12) DEFAULT '0',
  `last_updated` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_invoice_check` */

DROP TABLE IF EXISTS `ep_invoice_check`;

CREATE TABLE `ep_invoice_check` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `poco_no` varchar(100) DEFAULT NULL,
  `pa_no` varchar(50) DEFAULT NULL COMMENT 'eP Payment Advice No.',
  `is_pa_no_exist` varchar(3) DEFAULT NULL COMMENT 'eP Payment Advice No. is exist in eP or not?',
  `inv_no` varchar(100) DEFAULT NULL,
  `fl_order_id` bigint(12) DEFAULT NULL,
  `fl_req_id` bigint(12) DEFAULT NULL,
  `status_name` varchar(100) DEFAULT NULL,
  `status_id` varchar(100) DEFAULT NULL,
  `is_ap511` varchar(3) DEFAULT NULL,
  `file_name` varchar(500) DEFAULT NULL,
  `payment_reference_no` varchar(500) DEFAULT NULL,
  `inv_date_created` datetime DEFAULT NULL,
  `payment_date` varchar(500) DEFAULT NULL,
  `cancelation_date` varchar(500) DEFAULT NULL,
  `payment_advice_no` varchar(500) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `poco_no_inv_no` (`poco_no`,`inv_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_invoice_detail` */

DROP TABLE IF EXISTS `ep_invoice_detail`;

CREATE TABLE `ep_invoice_detail` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `batch_file_id` varchar(50) DEFAULT NULL,
  `file_name` varchar(50) DEFAULT NULL,
  `invoice_no` varchar(17) DEFAULT NULL,
  `payment_amount` varchar(13) DEFAULT NULL,
  `total_payment_amount` varchar(13) DEFAULT NULL,
  `ptj_code` varchar(8) DEFAULT NULL,
  `sap_vendor_code` varchar(10) DEFAULT NULL,
  `supplier_name` varchar(100) DEFAULT NULL,
  `bank_name` varchar(40) DEFAULT NULL,
  `1gfmas_payment_id` varchar(13) DEFAULT NULL,
  `payment_reference_no` varchar(18) DEFAULT NULL,
  `vendor_id_code` varchar(20) DEFAULT NULL,
  `entity_code` varchar(4) DEFAULT NULL,
  `payment_no` varchar(10) DEFAULT NULL,
  `cancelation_date` date DEFAULT NULL,
  `payment_advice_no` varchar(17) DEFAULT NULL,
  `payment_advice_date` date DEFAULT NULL,
  `invoice_date` date DEFAULT NULL,
  `payment_date` date DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `file_name` (`file_name`),
  KEY `invoiceno` (`invoice_no`),
  CONSTRAINT `file_name` FOREIGN KEY (`file_name`) REFERENCES `ep_osb_batch_file` (`file_name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_log_inactive_ptj` */

DROP TABLE IF EXISTS `ep_log_inactive_ptj`;

CREATE TABLE `ep_log_inactive_ptj` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `org_validity_id` bigint(12) NOT NULL,
  `org_profile_id` bigint(12) NOT NULL,
  `org_code` varchar(15) NOT NULL,
  `org_name` varchar(250) NOT NULL,
  `org_changed_date` datetime DEFAULT NULL COMMENT 'Last get Changed Date From PM_ORG_VALIDITY',
  `exp_date` datetime NOT NULL,
  `total_user` int(11) NOT NULL DEFAULT '0',
  `total_user_group` int(11) NOT NULL DEFAULT '0',
  `total_user_group_users` int(11) NOT NULL DEFAULT '0',
  `created_at` int(11) DEFAULT NULL COMMENT 'Date insert into ep_log_inactive_ptj',
  `process_remark` varchar(255) DEFAULT 'Pending To Inactive',
  `process_status` int(11) NOT NULL DEFAULT '0',
  `process_date` datetime DEFAULT NULL,
  `data` longtext,
  PRIMARY KEY (`id`),
  UNIQUE KEY `ep_log_inactive_ptj_org_validity_id_uindex` (`org_validity_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_login_history` */

DROP TABLE IF EXISTS `ep_login_history`;

CREATE TABLE `ep_login_history` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_id` varchar(50) NOT NULL,
  `username` varchar(100) NOT NULL,
  `groups` text,
  `date` date DEFAULT NULL,
  `last_login` datetime NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_id` (`user_id`,`date`),
  UNIQUE KEY `username` (`username`,`date`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_login_statistic` */

DROP TABLE IF EXISTS `ep_login_statistic`;

CREATE TABLE `ep_login_statistic` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `user_type` varchar(20) DEFAULT NULL,
  `user_name` varchar(100) DEFAULT NULL,
  `user_group` varchar(100) DEFAULT NULL,
  `date_login` date DEFAULT NULL,
  `time_login` time DEFAULT NULL,
  `login_total` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `composite-key` (`user_type`,`user_group`,`date_login`,`time_login`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_monitor_qt` */

DROP TABLE IF EXISTS `ep_monitor_qt`;

CREATE TABLE `ep_monitor_qt` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `qt_id` bigint(12) DEFAULT NULL,
  `qt_no` varchar(30) DEFAULT NULL,
  `qt_status_id` varchar(20) DEFAULT NULL,
  `qt_status` varchar(50) DEFAULT NULL,
  `qt_date` date DEFAULT NULL,
  `qt_monitor_type` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_osb_batch_file` */

DROP TABLE IF EXISTS `ep_osb_batch_file`;

CREATE TABLE `ep_osb_batch_file` (
  `batch_file_id` varchar(50) NOT NULL,
  `trans_id` varchar(50) DEFAULT NULL,
  `service_code` varchar(20) DEFAULT NULL,
  `file_name` varchar(50) DEFAULT NULL,
  `created_date` datetime DEFAULT NULL,
  `file_data` mediumtext,
  PRIMARY KEY (`batch_file_id`),
  KEY `Index_File_Name` (`file_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

/*Table structure for table `ep_payment_failed` */

DROP TABLE IF EXISTS `ep_payment_failed`;

CREATE TABLE `ep_payment_failed` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `supplier_id` bigint(12) DEFAULT NULL,
  `company_name` varchar(200) DEFAULT NULL,
  `ep_no` varchar(50) DEFAULT NULL,
  `order_id` bigint(12) NOT NULL,
  `payment_amt` varchar(10) DEFAULT NULL,
  `bill_no` varchar(50) DEFAULT NULL,
  `bill_type` varchar(10) DEFAULT NULL,
  `bill_date` date DEFAULT NULL,
  `bill_ref_id` varchar(50) DEFAULT NULL,
  `payment_due_date` date DEFAULT NULL,
  `payment_created` datetime DEFAULT NULL,
  `payment_gateway` varchar(50) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `order_id` (`order_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_phis_stuck` */

DROP TABLE IF EXISTS `ep_phis_stuck`;

CREATE TABLE `ep_phis_stuck` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `request_date` datetime DEFAULT NULL,
  `doc_co` varchar(50) DEFAULT NULL,
  `doc_co_type` varchar(50) DEFAULT NULL,
  `doc_cr` varchar(50) DEFAULT NULL,
  `doc_cr_type` varchar(50) DEFAULT NULL,
  `status_name` varchar(50) DEFAULT NULL,
  `ptj_code` varchar(50) DEFAULT NULL,
  `ptj_name` varchar(199) DEFAULT NULL,
  `state_name` varchar(100) DEFAULT NULL,
  `status` varchar(100) DEFAULT NULL,
  `created_at` datetime DEFAULT NULL,
  `changed_at` datetime DEFAULT NULL,
  `bpm_instance` varchar(50) DEFAULT NULL,
  `bpm_composite` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_ptj_info_infra` */

DROP TABLE IF EXISTS `ep_ptj_info_infra`;

CREATE TABLE `ep_ptj_info_infra` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `ministry_name` varchar(200) NOT NULL,
  `ministry_code` varchar(10) NOT NULL,
  `ptj_name` varchar(200) NOT NULL,
  `ptj_code` varchar(10) NOT NULL,
  `ptj_state` varchar(200) NOT NULL,
  `ptj_address` text,
  `login_id` varchar(200) NOT NULL,
  `fullname` varchar(200) NOT NULL,
  `phoneno` varchar(50) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `roles` text,
  `is_training_ep` varchar(255) DEFAULT NULL,
  `modul_digunapakai` text,
  `comp_os` varchar(100) DEFAULT NULL,
  `comp_os_type` varchar(100) DEFAULT NULL,
  `comp_cpu` varchar(100) DEFAULT NULL,
  `comp_harddisk` varchar(100) DEFAULT NULL,
  `comp_screen_reso` varchar(100) DEFAULT NULL,
  `comp_ram` varchar(100) DEFAULT NULL,
  `comp_browser` varchar(100) DEFAULT NULL,
  `comp_browser_ver` varchar(100) DEFAULT NULL,
  `comp_browser_reso` varchar(100) DEFAULT NULL,
  `comp_line` varchar(100) DEFAULT NULL,
  `comp_line_speed` varchar(100) DEFAULT NULL,
  `comp_mampu_allow` varchar(100) DEFAULT NULL,
  `kekerapan_ep` varchar(100) DEFAULT NULL,
  `bil_kekerapan_ep` int(8) DEFAULT NULL,
  `cadangan_ep` text,
  `modul_scale_dp` varchar(200) DEFAULT NULL,
  `modul_scale_sebutharga` varchar(200) DEFAULT NULL,
  `modul_scale_tender` varchar(200) DEFAULT NULL,
  `modul_scale_kontrak` varchar(200) DEFAULT NULL,
  `modul_scale_fl` varchar(200) DEFAULT NULL,
  `modul_scale_pplan` varchar(200) DEFAULT NULL,
  `modul_dp` varchar(200) DEFAULT NULL,
  `modul_sebutharga` varchar(200) DEFAULT NULL,
  `modul_tender` varchar(200) DEFAULT NULL,
  `modul_kontrak` varchar(200) DEFAULT NULL,
  `modul_fl` varchar(200) DEFAULT NULL,
  `modul_pplan` varchar(200) DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `changed_at` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_sm_rollback_appl` */

DROP TABLE IF EXISTS `ep_sm_rollback_appl`;

CREATE TABLE `ep_sm_rollback_appl` (
  `id` int(8) NOT NULL AUTO_INCREMENT,
  `appl_id` bigint(12) NOT NULL,
  `appl_no` varchar(20) DEFAULT NULL,
  `appl_status_id` int(8) DEFAULT NULL,
  `appl_status_name` varchar(150) DEFAULT NULL,
  `wf_status_id` int(8) DEFAULT NULL,
  `wf_status_name` varchar(150) DEFAULT NULL,
  `diary_status_id` int(8) DEFAULT NULL,
  `diary_status_name` varchar(150) DEFAULT NULL,
  `process_status` int(1) DEFAULT NULL COMMENT '0 - Not Start , 1 - Success , 2 - Failed , 9 - Not Valid',
  `created_date` datetime DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  `result` mediumtext,
  `retry_count` int(1) DEFAULT '0',
  PRIMARY KEY (`id`),
  UNIQUE KEY `appl_id` (`appl_id`),
  UNIQUE KEY `appl_no` (`appl_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=latin1;

/*Table structure for table `ep_task` */

DROP TABLE IF EXISTS `ep_task`;

CREATE TABLE `ep_task` (
  `task_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `category_id` bigint(12) NOT NULL,
  `case_no` bigint(12) DEFAULT NULL,
  `case_type` varchar(50) DEFAULT NULL,
  `entity_name` varchar(200) DEFAULT NULL,
  `description` text,
  `resolution` text,
  `status` tinyint(1) DEFAULT '0',
  `is_deleted` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `completed_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`task_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_task_category` */

DROP TABLE IF EXISTS `ep_task_category`;

CREATE TABLE `ep_task_category` (
  `category_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `category_name` varchar(200) DEFAULT NULL,
  `category_desc` text,
  PRIMARY KEY (`category_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_task_missing` */

DROP TABLE IF EXISTS `ep_task_missing`;

CREATE TABLE `ep_task_missing` (
  `task_id` bigint(12) NOT NULL AUTO_INCREMENT,
  `case_no` varchar(20) DEFAULT NULL,
  `case_status` varchar(100) DEFAULT NULL,
  `case_id` varchar(100) DEFAULT NULL,
  `case_created` datetime DEFAULT NULL,
  `batch` varchar(200) DEFAULT NULL,
  `doc_no` text,
  `module` varchar(100) DEFAULT NULL,
  `process_status` varchar(100) DEFAULT '00',
  `composite_instance_id` text,
  `problem` text,
  `resolution` text,
  `is_case_closed` tinyint(1) DEFAULT '0',
  `is_deleted` tinyint(1) DEFAULT '0',
  `created_at` datetime DEFAULT NULL,
  `created_by` varchar(100) DEFAULT NULL,
  `updated_at` datetime DEFAULT NULL,
  `updated_by` varchar(100) DEFAULT NULL,
  `completed_at` datetime DEFAULT NULL,
  `completed_by` varchar(100) DEFAULT NULL,
  PRIMARY KEY (`task_id`),
  KEY `case_no` (`case_no`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*Table structure for table `ep_terminatet_qt_validity` */

DROP TABLE IF EXISTS `ep_terminatet_qt_validity`;

CREATE TABLE `ep_terminatet_qt_validity` (
  `id` bigint(12) NOT NULL AUTO_INCREMENT,
  `doc_no` varchar(200) NOT NULL,
  `doc_id` varchar(50) NOT NULL,
  `doc_type` varchar(50) DEFAULT NULL,
  `status_id` varchar(50) DEFAULT NULL,
  `changed_date` datetime DEFAULT NULL,
  `proposal_validity_end_date` datetime DEFAULT NULL,
  `action_date` datetime DEFAULT NULL,
  `bpm_instance_id` varchar(50) DEFAULT NULL,
  `bpm_instance_version` varchar(200) DEFAULT NULL,
  `bpm_terminated_date` datetime DEFAULT NULL,
  `bpm_status_terminated` varchar(200) DEFAULT NULL,
  `bpm_remarks` text,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
