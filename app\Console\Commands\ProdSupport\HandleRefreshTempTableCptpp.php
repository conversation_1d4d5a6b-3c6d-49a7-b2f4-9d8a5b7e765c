<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use DB;
use App\Migrate\MigrateUtils;

class HandleRefreshTempTableCptpp extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ps-refresh-temp-table-cptpp';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will refresh in every morning';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */

    public function handle()
    {
        try {
            $tableQueries = [
                "CREATE TABLE cptpp_bidang AS
            SELECT *
            FROM (
                SELECT l1.category_l1_code || l2.category_l2_code || l3.category_l3_code kod_bidang,
                       l1.category_l1_code l1_code,
                       l1.category_name l1_name,
                       l2.category_l2_code l2_code,
                       l2.category_name l2_name,
                       l3.category_l3_code l3_code,
                       l3.category_name l3_name,
                       l1.record_status l1_stat,
                       l2.record_status l2_stat,
                       l3.record_status l3_stat
                FROM pm_category_l1 l1,
                     pm_category_l2 l2,
                     pm_category_l3 l3
                WHERE l1.category_l1_id = l2.category_l1_id
                  AND l2.category_l2_id = l3.category_l2_id
                ORDER BY 1, 2, 3 ASC
            ) bidang
            WHERE kod_bidang IN ('040101', '210107', '210111', '220101', '220115', '220116', '220117', '220118', '220119', '220120', '220121', '220122', '220123', '220124', '220401', '220402', '220403', '220509', '220705', '220706', '220707', '220708', '220709', '220710', '220711', '220712', '220713', '220714', '220801', '220802', '220803', '220804', '220902', '221010', '221103', '221105', '221109', '221112', '221202', '221203', '221301', '221305', '221306', '221307', '221308', '221309', '221310', '221311', '221401', '221402', '221506', '221508', '221509', '221607', '221608', '221701', '221708', '221801', '221802', '221901', '221902', '222004', '222008', '222101', '222103', '222104', '222105', '222106', '222107', '222108', '222202', '222303', '222304', '222305', '222404', '222501', '222502', '222503', '222504', '222704', '222706', '222801', '222901', '330204', '340103', '340401', '340402', '340403', '340404', '340501', '340502', '340503', '340504', '340505', '340506', '340801', '340802', '340901', '341002')
            ORDER BY 1 ASC",

                "CREATE TABLE cptpp_supplier AS
SELECT ss.created_date,
    ss.SUPPLIER_ID,
    ss.Company_Name, 
    (SELECT
        code_name
    FROM
        pm_parameter pp,
        pm_parameter_desc ppd
    WHERE
        pp.PARAMETER_ID = ppd.PARAMETER_ID
        AND pp.parameter_type = 'ST'
        AND ppd.language_code = 'en'
        AND parameter_code = sa.supplier_type) supplier_type,
    DECODE(ss.RECORD_STATUS, 1, 'Active', 2, 'Supplier Cancel', 9, 'Deleted', 0, 'In Active', 5, 'In-Progress Registration' ) Suplier_Status,
    ss.Business_Type,
    (SELECT paramdesc.code_name
            FROM pm_parameter param,
                 pm_parameter_desc paramdesc,
                 pm_parameter_type paramtype
           WHERE param.parameter_id = paramdesc.parameter_id
             AND param.parameter_type = paramtype.parameter_type
             AND param.parameter_type = 'SBT'
             AND paramdesc.language_code = 'en'
             AND param.parameter_code = ss.business_type
             AND param.record_status = 1
             AND paramdesc.record_status = 1
             AND paramtype.record_status = 1) business_type_desc,
    ss.reg_No,
    sma.MOF_NO,
    scb.ssm_company_country As Origin_Country_SSM,
    DECODE(sa.APPL_TYPE, 'N', 'New Application', 'R', 'Renew Application', 'U', 'Update Profile Application', 'B', 'Bumi Status Application', 'A', 'Add Category Application')
                                       || ' - ' || sa.APPL_NO || ' ( Status: ' || (SELECT status_name FROM pm_status_desc WHERE status_id = sa.status_id AND language_code = 'en') || ' )' As Latest_Supplier_Application
FROM
    sm_supplier ss,
    sm_company_basic scb,
    sm_mof_account sma,
    sm_appl sa
WHERE
    ss.SUPPLIER_ID = sma.SUPPLIER_ID(+)
    AND ss.SUPPLIER_ID = sa.SUPPLIER_ID
    AND sa.APPL_ID = scb.appl_id  
    AND sa.IS_ACTIVE_APPL = 1 
    AND scb.ssm_company_country NOT IN ('MAL', 'IND','HKG','KOR', 'SOK','USA','X', 'L')
   AND sa.supplier_type NOT IN ('B')
    AND ss.supplier_id not IN (1002732, 797814, 871927, 917415, 834719, 879936, 67119, 854651, 792691, 799614, 895021, 939308)
UNION
SELECT ss.created_date,
    ss.SUPPLIER_ID,
    ss.Company_Name,
    (SELECT
        code_name
    FROM
        pm_parameter pp,
        pm_parameter_desc ppd
    WHERE
        pp.PARAMETER_ID = ppd.PARAMETER_ID
        AND pp.parameter_type = 'ST'
        AND ppd.language_code = 'en'
        AND parameter_code = sa.supplier_type) supplier_type,
    DECODE(ss.RECORD_STATUS, 1, 'Active', 2, 'Supplier Cancel', 9, 'Deleted', 0, 'In Active', 5, 'In-Progress Registration' ) Suplier_Status,
    ss.Business_Type,
    (SELECT paramdesc.code_name
            FROM pm_parameter param,
                 pm_parameter_desc paramdesc,
                 pm_parameter_type paramtype
           WHERE param.parameter_id = paramdesc.parameter_id
             AND param.parameter_type = paramtype.parameter_type
             AND param.parameter_type = 'SBT'
             AND paramdesc.language_code = 'en'
             AND param.parameter_code = ss.business_type
             AND param.record_status = 1
             AND paramdesc.record_status = 1
             AND paramtype.record_status = 1) business_type_desc,
    ss.reg_No,
    sma.MOF_NO,
    scb.ssm_company_country As Origin_Country_SSM,
    CASE
        WHEN sa.APPL_NO IS NOT NULL THEN
                DECODE(sa.APPL_TYPE, 'N', 'New Application', 'R', 'Renew Application', 'U', 'Update Profile Application', 'B', 'Bumi Status Application', 'A', 'Add Category Application', 'C', 'Cancel MOF Application')
                                    || ' - ' || sa.APPL_NO 
                                    || ' ( Status: ' || (SELECT status_name FROM pm_status_desc WHERE status_id = sa.status_id AND language_code = 'en') || ' )' 
        ELSE 'N/A'
    END As Latest_Supplier_Application
FROM
    sm_supplier ss,
    sm_company_basic scb,
    sm_mof_account sma,
    sm_appl sa
WHERE
    ss.SUPPLIER_ID = sma.SUPPLIER_ID(+)
    AND ss.SUPPLIER_ID = sa.SUPPLIER_ID
    AND sa.APPL_ID = scb.appl_id  
    AND sa.SUPPLIER_ID NOT IN ( Select a.SUPPLIER_ID from sm_appl a where a.supplier_id = ss.SUPPLIER_ID and a.IS_ACTIVE_APPL = 1 )
    AND ss.latest_appl_id = sa.appl_id  
   AND scb.ssm_company_country NOT IN ('MAL', 'IND','HKG','KOR', 'SOK','USA','X', 'L')
   AND sa.supplier_type NOT IN ('B')
   AND ss.supplier_id not IN (1002732, 797814, 871927, 917415, 834719, 879936, 67119, 854651, 792691, 799614, 895021, 939308)
ORDER BY 3",

             "CREATE TABLE pp_item as     
SELECT  distinct TO_CHAR (pp.created_DATE,'DD/MM/YYYY HH24:MI:SS' ) PP_Created, ppi.CHANGED_DATE AS item_changed_date, pp.CHANGED_DATE AS pp_changed_date, max(ministry_code) MIN_CODE,max(ministry_name) MIN_NAME,max(ptj_code) PTJ_CODE, 
max(ptj_name) PTJ_NAME , pp.plan_year , pp.plan_no, d.STATUS_NAME,
trim(REPLACE(REPLACE (REPLACE (REPLACE (ppi.procurement_title, CHR (9), ' '), CHR (10), ' ' ), CHR (13),' ' ),  CHR (34), ' ' )) AS tajuk_pelan, ppi.procurement_item_id,
trim(REPLACE (REPLACE (REPLACE (REPLACE (procurement_desc, CHR (9), ' '), CHR (10), ' ' ), CHR (13), ' ' ),  CHR (34), ' '  )) AS perihal_plan, 
decode (ppi.PROCUREMENT_CAT_ID, '707','Bekalan/Goods','708','Perkhidmatan/Services - Non Consultant','709','Perkhidmatan Perunding/Consultancy_Services',
'710','Kerja/Construction_Services') AS Kategori_Jenis_Perolehan , ppi.PROCUREMENT_CAT_ID,
decode (ppi.PROCUREMENT_TYPE_ID, '711','Direct_Purchase/Pembelian Terus','712','Quotation/Sebut Harga','713','Tender/Tender','714','Direct_Appointment/Lantikan Terus','715'
,'Selective_Tender/Rundingan Terus','716','Limited Tender/Tender Terhad','87072','Selective Tendering/Tender Terbuka Pra-Kelayakan','87073','Limited Tendering/Rundingan Terus'
,'87074','Request For Proposal/Tender Terbuka Request For Proposal') AS Kaedah_Perolehan, ppi.invitation_date tarikh_jangkaan_pelawa, ppi.estimated_amt amaun_anggaran  
--ppi.RECORD_STATUS pp_item_stat --, TO_CHAR (b.created_DATE,'DD/MM/YYYY HH24:MI:SS' ) MRPP_Created, TO_char(b.created_DATE,'YYYY') as MRPP_year
FROM pp_plan pp, pp_procurement_item ppi, pm_procurement_cat_type ppct, pp_workflow_status b, pm_status c, pm_status_desc d,
(select ptj.ORG_PROFILE_ID, kem.ORG_CODE ministry_code, kem.ORG_NAME ministry_name, kem.EFF_DATE minis_effdate , kem.EXP_DATE minis_expdate,kem.RECORD_STATUS minis_record,
ptj.ORG_CODE  ptj_code , ptj.ORG_NAME ptj_name, ptj.EFF_DATE , ptj.EXP_DATE, ptj.RECORD_STATUS ptj_record from pm_org_validity kem, pm_org_profile kem2, pm_org_validity guard, 
pm_org_profile guard2, pm_org_validity jab, pm_org_profile jab2, pm_org_validity ptj, pm_org_profile ptj2 where ptj.org_profile_id = ptj2.org_profile_id
and jab2.org_profile_id = jab.org_profile_id and guard.org_profile_id = guard2.org_profile_id and kem2.org_profile_id = kem.org_profile_id 
and jab2.org_profile_id = ptj2.parent_org_profile_id and guard2.org_profile_id = jab2.parent_org_profile_id and kem2.org_profile_id = guard2.parent_org_profile_id 
and kem.RECORD_STATUS <> 9  and ptj.RECORD_STATUS <> 9 and guard.RECORD_STATUS <> 9 and jab.RECORD_STATUS <> 9 and ((kem.EXP_DATE > = sysdate) or (kem.EXP_DATE is null)) 
and ((guard.EXP_DATE > = sysdate) or (guard.EXP_DATE is null)) and ((jab.EXP_DATE > = sysdate) or (jab.EXP_DATE is null)) and ((ptj.EXP_DATE > = sysdate) or (ptj.EXP_DATE is null) 
or (ptj.exp_date <= sysdate and ptj.record_status = 0)) and substr(ptj.org_code,1,1) <> '0'  ) dtlptj 
WHERE pp.plan_id = ppi.plan_id AND pp.plan_id = b.doc_id AND b.status_id = c.status_id AND c.status_id = d.status_id AND d.language_code = 'en' AND b.doc_type IN ('PP')
AND b.is_current = 1 AND  pp.org_profile_id = dtlptj.org_profile_id  and pp.PLAN_YEAR in (2023, 2024) and ppi.RECORD_STATUS = 1 --and b.STATUS_ID not in (30005,30013)
GROUP BY d.STATUS_NAME,b.doc_type, pp.plan_no,pp.plan_year,  ppi.procurement_title, ppi.procurement_cat_id, ppi.procurement_type_id,  procurement_desc, ppi.procurement_item_id,ppi.PROCUREMENT_CAT_ID,--ppi.RECORD_STATUS,  
ppi.invitation_date,ppi.estimated_amt ,pp.created_DATE, ppi.CHANGED_DATE , pp.CHANGED_DATE --,b.created_DATE
ORDER BY 2,4,7 ASC",

"CREATE TABLE qt_cptpp as
   SELECT  DECODE (sq.procurement_mode_id,
                 185, 'Quotation',
                 186, 'Tender'
                ) AS jenis_perolehan,
         MAX (w.org_code) AS kod_kementerian, MAX (w.org_name) AS kementerian,
         MAX (e.org_code) AS kod_kumpulan_ptj, MAX (e.org_name) AS kptj,
         MAX (d.org_code) AS kod_ptj, MAX (d.org_name) AS ptj,  sq.qt_no AS qt_no, sq.qt_title AS tajuk_qt, pm.status_id,
         pm.status_name AS status, DECODE (pm.status_id,
                 60022, '3. Closed',
                 60023, '3. Closed',
                 60010, '3. Closed',
                 60011, '3. Closed',
                 60026, '1. Before Publish',
                 60004, '1. Before Publish',
                 60013, '3. Closed',
                 60003, '1. Before Publish',
                 60014, '4. Completed',
                 60009, '2. Published',
                 60028, '1. Before Publish',
                 60033, '1. Before Publish',
                 60024, '1. Before Publish',
                 60008, '2. Published',
                 60016, '3. Closed',
                 60005, '1. Before Publish',
                 60025, '1. Before Publish',
                 60012, '3. Closed'
                ) AS stage ,  (SELECT sum(loa.LOA_AMT) FROM SC_LOA loa,SC_LOI_LOA loi WHERE loa.LOI_LOA_ID(+) = loi.LOI_LOA_ID AND loi.doc_id = sq.QT_ID ) AS amount
                , sq.changed_date AS qt_changed_date , decode(sq.PROCUREMENT_TYPE_CAT_ID,815,'Bekalan',816,'Perkhidmatan') procurement_type_cat_id , s.CHANGED_DATE AS action_date, sq.publish_date , sq.closing_date 
    FROM sc_qt sq,
         sc_workflow_status s,
         pm_status_desc pm,
         pm_parameter mtr,
         pm_org_validity d,
         pm_org_profile pr,
         pm_org_validity e,                                              --jab
         pm_org_profile pr2,
         pm_org_validity y,
         pm_org_profile m,
         pm_org_validity w,
         pm_org_profile mi,
         SC_LOA loa, SC_LOI_LOA loi
   WHERE w.org_profile_id = mi.org_profile_id
     AND d.org_profile_id = pr.org_profile_id
     AND e.org_profile_id = pr.parent_org_profile_id
     AND pr.org_type_id = mtr.parameter_id
     AND pr2.org_profile_id = pr.parent_org_profile_id
     AND m.org_profile_id = pr2.parent_org_profile_id
     AND y.org_profile_id = m.org_profile_id
     AND mi.org_profile_id = m.parent_org_profile_id
     AND d.record_status = 1
     AND w.record_status = 1
     AND sq.qt_id = s.doc_id
     AND sq.org_profile_id = d.org_profile_id
     AND pm.status_id = s.status_id
      AND ((sq.PROCUREMENT_TYPE_CAT_ID  = 815 AND (SELECT sum(loa.LOA_AMT) FROM SC_LOA loa,SC_LOI_LOA loi WHERE loa.LOI_LOA_ID(+) = loi.LOI_LOA_ID AND loi.doc_id = sq.QT_ID ) > 8850000) 
       OR (sq.PROCUREMENT_TYPE_CAT_ID = 816 AND (SELECT sum(loa.LOA_AMT) FROM SC_LOA loa,SC_LOI_LOA loi WHERE loa.LOI_LOA_ID(+) = loi.LOI_LOA_ID AND loi.doc_id = sq.QT_ID ) > 11800000))
     AND sq.procurement_mode_id IN ('185', '186')
     AND pm.language_code = 'en'
     AND s.is_current = 1
     --AND sq.qt_no = 'QT220000000038626'
     --AND pm.status_id IN (60014)
     /*AND pm.status_id NOT IN
            (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
             60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
             60046)*/ --- uncomment to exclude qt cancel 
     AND s.doc_type = 'QT'
     AND sq.qt_no LIKE ('QT%')
     AND sq.created_date BETWEEN TO_DATE('01/11/2022', 'dd/mm/yyyy') AND SYSDATE
     AND sq.qt_id = loi.doc_id(+)
     AND loa.LOI_LOA_ID(+) = loi.LOI_LOA_ID
	--AND loi.DOC_TYPE = 'QT'
--     AND UPPER (sq.qt_title) NOT LIKE '%FTA%CPTPP%'
     --AND sq.publish_period < 40
GROUP BY sq.qt_title,
         sq.created_date,
         sq.qt_no,
         sq.procurement_mode_id,
         sq.procurement_type_cat_id,
         sq.closing_date,
         pm.status_name,
         pm.status_id,
         s.changed_date,
         sq.publish_date, 
         sq.closing_date, sq.QT_ID,
         sq.changed_date
         --loa.LOA_AMT
ORDER BY sq.qt_no"
            ];

            foreach ($tableQueries as $query) {
                if (!$this->tableExists($this->extractTableName($query))) {
                    DB::connection('oracle_nextgen_drc')->statement($query);
                    $this->info('Table created successfully.');
                } else {
                    $this->info('Table already exists.');
                }
            }
        } catch (\Exception $ex) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $ex->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($ex->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $ex->getTraceAsString());
        }
        MigrateUtils::logDump('Done! Completed');
    }

    protected function tableExists($tableName)
    {
        $query = "SELECT COUNT(*) AS object_count FROM USER_OBJECTS WHERE OBJECT_TYPE = 'TABLE' AND OBJECT_NAME = :table_name";
        $result = DB::connection('oracle_nextgen_drc')->select($query, ['table_name' => strtoupper($tableName)]);
        return $result[0]->object_count > 0;
    }

    protected function extractTableName($query)
    {
        // This function extracts the table name from a CREATE TABLE statement
        preg_match('/CREATE TABLE (\w+)/i', $query, $matches);
        return $matches[1];
    }
}
