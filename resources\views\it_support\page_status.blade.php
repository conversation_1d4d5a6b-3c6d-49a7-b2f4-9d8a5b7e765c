<div class="block">
    <form class="form-horizontal form-bordered" id="notes_form"
        action="{{ url('/it_support/acknowledgement/status/note/create') }}" method="post">
        {{ csrf_field() }}
        <input readonly id="page_type" name="page_type" type="text" value="{{ $page }}" class="form-control"
            style="display:none">
            <input readonly id="location_type" name="location_type" type="text" value="{{ $location }}" class="form-control" style="display:none">
        <div class="form-group">
            <div class="form-group">

                <div class="row">
                    <div class="col-md-6"></div>
                    @if (isset($listNote) && $listNote[0]->ack_status !== null)
                        <div class="col-md-6 text-right">
                            <h5 for="status">Status : <span class="text-info">{{ $listNote[0]->ack_status }}</span>
                            </h5>
                        </div>
                    @else
                        <div class="col-md-6 text-right">
                            <h5 for="status">Status : <span class="text-info" id="ack_status"></span></h5>
                        </div>
                    @endif
                </div>
                <input readonly="" id="selected_date" name="selected_date" type="text" value=""
                    class="form-control" style="width: 700px; display: none">

                <label class="col-md-1 text-left" for="remarks">Note : <span class="text-danger">*</span></label>
                {{-- <div class="col-md-12">
                    <textarea type="text" id="remarks" name="remarks" rows="5" required class="form-control"
                        placeholder="Please insert remarks here if any"
                        {{ $listNote && $listNote[0]->ack_status === 'Completed' ? 'disabled' : '' }}>{{ $listNote ? $listNote[0]->ack_remarks : '' }}
                    </textarea>
                </div> --}}
                <div class="col-md-12">
                    <textarea type="text" id="remarks" name="remarks" rows="5" required class="form-control"
                        placeholder="Please insert remarks here if any"
                        {{ $listNote && $listNote[0]->ack_status === 'Completed' ? 'disabled' : '' }}>{{ $listNote && $listNote[0]->ack_status !== 'Completed' ? $listNote[0]->ack_remarks ?? '' : '' }}
                    </textarea>
                </div>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-1 text-left" for="check_by">Check By</label>
            <div class="col-md-3">
                <input readonly="" id="check_by" name="check_by" type="text"
                    value="{{ $listNote ? $listNote[0]->ack_check_by : $user }} " class="form-control"
                    style="width: 700px;">
            </div>
            <div class="col-md-3">
            </div>

            <label class="col-md-1 text-left" for="ack_by">Acknowledge By</label>
            <div class="col-md-3">
                <input readonly="" id="ack_by" name="ack_by" type="text"
                    value="{{ isset($listNote) && count($listNote) > 0 && ($listNote[0]->ack_status === 'Pending Endorsement' || $listNote[0]->ack_status === 'Completed') ? $listNote[0]->ack_endorsement_by ?? $user : '' }}"
                    class="form-control" style="width: 700px;">
            </div>


        </div>
        <div class="form-group">
            <label class="col-md-1 text-left" for="check_date">Check Date</label>
            <div class="col-md-3">
                <input readonly="" id="check_date" name="check_date" type="text"
                    value="{{ $listNote ? $listNote[0]->ack_check_date : $dateNote }}" class="form-control"
                    style="width: 700px;">
            </div>
            <div class="col-md-3">
            </div>

            <label class="col-md-1 text-left" for="ack_date">Acknowledge Date</label>
            <div class="col-md-3">
                <input readonly="" id="ack_date" name="ack_date" type="text"
                    value="{{ isset($listNote) && count($listNote) > 0 && ($listNote[0]->ack_status === 'Pending Endorsement' || $listNote[0]->ack_status === 'Completed') ? $listNote[0]->ack_endorsement_date ?? $dateNote : '' }}"
                    class="form-control" style="width: 700px;">
            </div>
        </div>
        @if ((Auth::user()->isItSupportTeam() && isset($listNote) && $listNote[0]->ack_status === null) || $listNote === null)
            <div class="form-group form-actions">
                <div class="text-center">
                    <button type="submit" class="btn btn-sm btn-primary save_button"><i class="fa fa-save"></i>
                        Submit</button>
                </div>
            </div>
        @endif
        @if ($page === 'dba')
            @if (Auth::user()->isDbaUsersEpss())
                @if (isset($listNote) && $listNote[0]->ack_status === 'Pending Endorsement')
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @else
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white; display: none;"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @endif
            @endif
        @elseif($page === 'server' || $page === 'status')
            @if (Auth::user()->isGroupServerAdmin())
                @if (isset($listNote) && $listNote[0]->ack_status === 'Pending Endorsement')
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @else
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white; display: none;"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @endif
            @endif
        @elseif($page === 'network' || $page === 'network_per' || $page === 'network_am' || $page === 'backup_c' || $page === 'backup_w')
            @if (Auth::user()->isNetworkApprover())
                @if (isset($listNote) && $listNote[0]->ack_status === 'Pending Endorsement')
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @else
                    <div class="text-center">
                        <button type="submit" style="background-color: #414770; color: white; display: none;"
                            class="btn btn btn-primary endorse_button">Endorse</button>
                    </div>
                @endif
            @endif
        @endif
    </form>
</div>
