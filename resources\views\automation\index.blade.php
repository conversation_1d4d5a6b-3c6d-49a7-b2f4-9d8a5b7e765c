@extends('layouts.guest-dash')
<link href='/css/automation.css' rel='stylesheet' />
@section('content')
    @if (Auth::user())
        <h2>EP Automation (Selenium Runner)</h2>
        <hr />
        <div class="row automation">
            
            @if(Auth::user()->isAllowAccessSMModule() ) 
            <div class="col-lg-6">
                <div class="row">
                <div class="col-sm-6 col-lg-4">
                    
                    <div class="panel panel-default text-center">
                        <div class="panel-heading">Clear Portal Cookies</div>
                        <div class="panel-body" style="height:170px">
                            <center>
                                <img src="/img/automation/cookies.png" class="img-responsive" alt="Website Cookies" />
                            </center>
                        </div>
                        <div class="panel-footer">
                            <div class="row">
                                <div class="col-sm-6">
                                <a href="#" onClick="ClearCookies()" class="btn btn-link"><span class="fa fa-play"> </span></a>
                                </div>
                                <div class="col-sm-6">
                                <a  
                                    tabindex="0"
                                    class="btn btn-link automation-popover" 
                                    data-toggle="popover" 
                                    data-trigger="focus" 
                                    title="Automation information" data-content="Portal Info">
                                        <span class="fa fa-info"></span>
                                    </a>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                </div>
                </div>
            </div>

            <div class="col-lg-6">
                <div class="panel panel-default">
                    <div class="panel-heading">Automation Stdout Process (Please do not reload a page)</div>
                    <div class="panel-body stdout-container">
                        <div id='stdout-listener'></div>
                    </div>
                    
                </div>
            </div>
            @endif
        </div>
    @endif
@endsection


<script>
    let refcode = '{{ $refcode }}'
    var source = new EventSource(`./ep-portal/sse/${refcode}`, {withCredentials: true});
        let current_id = 0
        source.onmessage = function(event) {
            if(event.data!==""){
                let obj = JSON.parse(event.data);
                obj.map((value, key)=>{
                    document.getElementById("stdout-listener").innerHTML += value.timestamp + " : " + value.message + "<br>";
                })
            }
    };

    // $('.automation-popover').popover(options)
    
    function ClearCookies(){
        $('#stdout-listener').html('');
        $.ajax({
            type: 'GET',
            url: `./ep-portal/clear-cookies/${refcode}`
        })
    }
</script>