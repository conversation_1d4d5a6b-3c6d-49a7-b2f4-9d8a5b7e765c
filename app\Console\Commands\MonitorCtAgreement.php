<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use App\Model\Notify\NotifyModel;
use App\Services\Traits\ContractService;

class MonitorCtAgreement extends Command {  

    use ContractService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-ct-agreement';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will monitor wip list agreement not null';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $dateNow = Carbon::now()->format('Y-m-d H:i:s');
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $result = $this->listCtAgreementNotNullQuery("");
            $totalRecord = count($result);
            $ctNumbers = array();
            foreach($result as $data) {
                array_push($ctNumbers,$data['contract_no']);
            } 
            $ctNumber = implode(",",$ctNumbers);

            $ctNumbers2 = array();
            $result2 = $this->listCtAgreementMissingQuery("");
            $totalRecord2 = count($result2);
            foreach($result2 as $datas) {
                array_push($ctNumbers2,$datas['contract_no']);
            } 
            $ctNumber2 = implode(",",$ctNumbers2);

            $msg = "
*[ALERT] WIP LIST AGREEMENT NOT NULL*
*DATE CHECKING TIME:* $dateNow
*TOTAL RECORDS:* $totalRecord
*CT NUMBER:* $ctNumber";

$msg2 = "
*[ALERT] WIP LIST AGREEMENT MISSING*
*DATE CHECKING TIME:* $dateNow
*TOTAL RECORDS:* $totalRecord2
*CT NUMBER:* $ctNumber2";


            if($totalRecord > 0) {
                $this->saveNotify('CT_AGREEMENT',$msg);
            }
            if($totalRecord2 > 0) {
                $this->saveNotify('CT_AGREEMENT',$msg2);
            }
            
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            \Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
    }

    protected function saveNotify($receiver,$msg){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        $totNotify = NotifyModel::where('message',$msg)->count();
        if($totNotify > 0){
            MigrateUtils::logDump($clsInfo.'Skip! Notification already sent');
        }else{
            $nty = new NotifyModel;
            $nty->message = $msg;
            $nty->receiver_group = $receiver;
            $nty->process = 'notify group'; // 'notify group , 'notify personal
            $nty->status = 0;
            $nty->sent = 0;
            $nty->date_entered = Carbon::now();
            $nty->retry = 0;
            $nty->source_app = 'EPSS';
            $nty->source_class = __CLASS__;
            $nty->source_remark = 'Monitoring wip list agreement not null';
            $nty->save();
        }
        
    }

}
