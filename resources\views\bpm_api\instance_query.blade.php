@extends('layouts.guest-dash')
<style>
    input[type=number]::-webkit-inner-spin-button, 
    input[type=number]::-webkit-outer-spin-button { 
        -webkit-appearance: none; 
        margin: 0; 
    }
    input.largerCheckbox { 
        width: 15px; 
        height: 15px;
    }

    input.time{
        width: 30px;
        padding: 5px;
    }

</style>
@section('content')

<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
            </li>
            <li class="active">
                <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
            </li>
            <li >
                <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
            </li>
        </ul>
    </div>
</div>

<div class="row">
    <div class="col-lg-12">
        <div class="block">
            <div class="row">
                @if($status_api != null)
                <h5 class="alert alert-danger">{{$status_api}}</h5>
                @endif
                <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
                <form id="form-search-task" action="{{url("/bpm/instance/query")}}" method="post" class="form-horizontal form-bordered">
                    {{ csrf_field() }}
                    <div class="form-group">
                        <div class="col-lg-4">
                            <input type="checkbox" id="composite_cbx" name="composite_cbx" class="largerCheckbox"><span style="color: #003d7a;font-size: 15px"> <strong>Composite</strong></span> <br/><br/>
                            <div style="padding-left: 20px">
                                <select id="composite" name="composite" class="form-control select2" disabled="true" style="width: 400px;">
                                    @foreach($listComposite as  $key => $value)
                                    <option value="{{$key}}" @if($key == $composite ) selected @endif>{{$value}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <br/>
                            <div>
                                <fieldset id="change_state_div" name="change_state_div" disabled="true">
                                    <br/><br/><br/>
                                    <h5>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Change State : &nbsp;&nbsp;
                                        <button class="btn btn-sm btn-default" id = "running_instance" name="running_instance"><i class="fa fa-circle-o" style="color: blue"></i> Running</button> &nbsp; 
                                        <button class="btn btn-sm btn-default" id = "complete_instance" name="complete_instance"><i class="fa fa-check" style="color: blue"></i> Complete</button> &nbsp; 
                                        <button class="btn btn-sm btn-default" id = "terminate_instance" name="terminate_instance"><i class="fa fa-times" style="color: blue"></i> Terminate</button>
                                    </h5>
                                </fieldset>

                            </div>
                        </div>
                        <div class="col-lg-4">
                            <input type="checkbox" id="created_cbx" name="created_cbx" class="largerCheckbox"> <span style="color: #003d7a;font-size: 15px"> <strong>Created</strong></span><br/><br/>
                            <fieldset id="created_div" disabled="true"> 
                                <?php $now = Carbon\Carbon::now()->format('d-m-Y'); ?>
                                <p style="padding-left: 70px;"><span style="color: #003d7a;font-size: 15px""> <strong> From </strong></span> 
                                    <input type="text" id="datefrom" name="datefrom" style="padding: 5px;" @if($dateFrom !== null ) value="{{ $dateFrom }}" @else value="{{ $now }}" @endif> &nbsp;&nbsp;&nbsp;&nbsp; 
                                           <input id="time_from_hour" name="time_from_hour" type="number" class="time" @if($timeFromHour !== null ) value="{{ $timeFromHour }}" @else value="00" @endif> &nbsp;:&nbsp; 
                                           <input id="time_from_min" name="time_from_min" type="number" class="time" @if($timeFromMin !== null ) value="{{ $timeFromMin }}" @else value="00" @endif> 
                                </p>
                                <p style="padding-left: 70px;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 
                                    <span style="color: #003d7a;font-size: 15px""> <strong> To </strong></span> 
                                    <input type="text" id="dateto" name="dateto" style="padding: 5px;" @if($dateTo !== null ) value="{{ $dateTo }}" @else value="{{ $now }}" @endif> &nbsp;&nbsp;&nbsp;&nbsp; 
                                           <input id="time_to_hour" name="time_to_hour" type="number" class="time" @if($timeToHour !== null ) value="{{ $timeToHour }}" @else value="23" @endif> &nbsp;:&nbsp; 
                                           <input id="time_to_min" name="time_to_min" type="number" class="time" @if($timeToMin !== null ) value="{{ $timeToMin }}" @else value="59" @endif>
                                </p>
                            </fieldset>
                        </div>
                        <div class="col-lg-4">
                            <input type="checkbox" id="state_cbx" name="state_cbx" class="largerCheckbox"> <span style="color: #003d7a;font-size: 15px"> <strong>State</strong></span> <br/><br/>
                            <fieldset id="state_div" name="state_div" disabled="true" style="padding-left: 20px">
                                <input type="checkbox" id="state" name="state[]" value="RUNNING" <?php
                                if (isset($state)) {
                                    if (in_array("RUNNING", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="padding-right: 60px; font-size: 15px"> Running</span>
                                <input type="checkbox" id="state" name="state[]" value="COMPLETED" <?php
                                if (isset($state)) {
                                    if (in_array("COMPLETED", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="padding-right: 60px; font-size: 15px"> Completed</span>
                                <input type="checkbox" id="state" name="state[]" value="TERMINATED" <?php
                                if (isset($state)) {
                                    if (in_array("TERMINATED", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="font-size: 15px"> Terminated </span><br>
                                <input type="checkbox" id="state" name="state[]" value="SUSPENDED" <?php
                                if (isset($state)) {
                                    if (in_array("SUSPENDED", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="padding-right: 40px; font-size: 15px"> Suspended</span>
                                <input type="checkbox" id="state" name="state[]" value="FAULTED" <?php
                                if (isset($state)) {
                                    if (in_array("FAULTED", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="padding-right: 85px; font-size: 15px"> Faulted</span>
                                <input type="checkbox" id="state" name="state[]" value="RECOVERY" <?php
                                if (isset($state)) {
                                    if (in_array("RECOVERY", $state)) {
                                        echo 'checked';
                                    }
                                }
                                ?> class="largerCheckbox"> <span style="font-size: 15px"> Recovery </span>
                            </fieldset> <br/><br/><br/>
                            <div class="pull-right" style="padding-right: 20px">
                                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="row">
                <center>
                    <div id="spinner" style="display:none;">
                        <i class="fa fa-spinner fa-2x fa-spin"></i>
                    </div>
                </center>
                <div id="div_instance">
                    @if($listdata != null && count($listdata) > 0)
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="table_instance" style="margin: 0px;">
                            <thead style="background-color:#f2f2f2;">
                                <tr>    
                                    <th class="text-center" style="width: 50px"></th>
                                    <th class="text-center" style="width: 100px">Instance</th>
                                    <th class="text-center" style="width: 220px">Conversation</th>
                                    <th class="text-center" style="width: 220px">Source</th>
                                    <th class="text-center" style="width: 100px">Composite</th>
                                    <th class="text-center" style="width: 100px">Version</th>
                                    <th class="text-center" style="width: 100px">State</th>
                                    <th class="text-center" style="width: 100px">Created</th>
                                </tr>
                            </thead>

                            <tbody style="font-size:80%;">
                                @foreach ($listdata as $data)
                                <?php
                                $createDate = date("d/m/Y h:i:s A", substr($data["createDate"], 0, 10));
                                ?>
                                <tr>
                                    <td class="text-center"><input type="checkbox" id="checkbox_instance" name="checkbox_instance" value="{{ $data['id'] }}"/></td>
                                    <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{ $data['id'] }}" target="_blank" > {{ $data['id'] }}</a></td>
                                    <td class="text-center">{{ $data['conversation'] }}</td>
                                    <td class="text-center">{{ $data['source'] }}</td>
                                    <td class="text-center">{{ $data['compositeName'] }}</td>
                                    <td class="text-center">{{ $data['version'] }}</td>
                                    <td class="text-center"><i class="fa fa-{{ App\Services\BPMAPIService::$BPM_ICON_STATUS_INSTANCE_QUERY_[$data['state']] }}" style="{{ App\Services\BPMAPIService::$BPM_STYLE_STATUS_INSTANCE_QUERY_[$data['state']] }}">                                  
                                        </i> <span style="{{ App\Services\BPMAPIService::$BPM_STYLE_STATUS_INSTANCE_QUERY_[$data['state']] }}">
                                            @if($data["state"] === 'RUNNING')<i><strong>{{ App\Services\BPMAPIService::$BPM_STATUS_INSTANCE_QUERY[$data['state']] }}</strong></i>
                                            @else{{ App\Services\BPMAPIService::$BPM_STATUS_INSTANCE_QUERY[$data['state']] }}@endif</span></td>
                                    <td class="text-center">{{ $createDate }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="8" style="font-size:90%;text-align: right;"><i>Page {{ $offset + 1 }}</i></td>
                                </tr>
                            </tfoot>
                        </table>
                        <br/><br/>
                    </div>
                    <input type="hidden" id="offset" name="offset" value="{{ $offset }}"/>
                    <input type="hidden" id="limit" name="limit" value="{{ $limit }}"/>
                    <input type="hidden" id="states" name="states" value="{{ $states }}"/>
                    <center>
                        <button id="previous_page" name="previous_page" disabled="true" class="btn btn-sm btn-primary fa fa-arrow-left"> Previous </button>
                        <button id="next_page" name="next_page" class="btn btn-sm btn-primary fa fa-arrow-right"> Next </button>
                    </center>
                    @endif
                </div>
            </div>
        </div>
    </div>
    <input type="hidden" id="isCompositeChecked" name="isCompositeChecked" value="{{$isCompositeChecked}}">
    <input type="hidden" id="isCreatedChecked" name="isCreatedChecked" value="{{$isCreatedChecked}}">
    <input type="hidden" id="isStateChecked" name="isStateChecked" value="{{$isStateChecked}}">

<div id="modal_spinner" class="modal fade">
    <div class="modal-dialog modal-sm" style="width: 10%; transform: translate(0, -50%); top: 50%; margin: 0 auto">
        <div class="modal-content">
            <div class="modal-body">
                <center>
                <i class="fa fa-spinner fa-3x fa-spin" style="color: red"></i><br/><br/>Loading..
            </center>
            </div>    
        </div>
    </div>
</div>

    @endsection

    @section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->

    <script>$('#page-container').removeAttr('class');</script>
    <script>$(function () {TablesDatatables.init();});</script>
    <script>$(function () {ModalListActionLogDatatable.init();});</script>
    
    <script src="/js/pages/tablesDatatables.js"></script>
    <script src="/js/pages/modalListActionLogDatatable.js"></script>
    <script src="/js/bpm/instancequery/populatetable.js"></script>
    <script src="/js/bpm/instancequery/pagination.js"></script>

    <script>
        $(document).ready(function () {

            var runningBtn = document.getElementById("running_instance");
            var completeBtn = document.getElementById("complete_instance");
            var terminateBtn = document.getElementById("terminate_instance");
            runningBtn.onclick = function (e) {
                e.preventDefault();
            };
            completeBtn.onclick = function (e) {
                e.preventDefault();
            };
            terminateBtn.onclick = function (e) {
                e.preventDefault();
                var instanceArray = [];
                $.each($("input[name='checkbox_instance']:checked"), function () {
                    instanceArray.push($(this).val());
                });

//                console.log(instanceArray);
            };

            var composite = $("input[name=isCompositeChecked]").val();
            var created = $("input[name=isCreatedChecked]").val();
            var state = $("input[name=isStateChecked]").val();

            if (composite === '1') {
                $("#composite").prop('disabled', false);
                $("#composite_cbx").prop('checked', 'checked');
            } else {
                $("#composite").prop('disabled', true);
            }

            if (created === '1') {
                $("#created_div").prop('disabled', false);
                $("#created_cbx").prop('checked', 'checked');
            } else {
                $("#created_div").prop('disabled', true);
            }

            if (state === '1') {
                $("#state_div").prop('disabled', false);
                $("#state_cbx").prop('checked', 'checked');
            } else {
                $("#state_div").prop('disabled', true);
            }

            $("input[type=checkbox]").each(function () {
                $(this).change(updateCount);
            });

            updateCount();

            function updateCount() {
                var count = $("input[name=checkbox_instance]:checked").size();

                if (count <= 0) {
                    $("#change_state_div").prop('disabled', true);
                } else {
                    $("#change_state_div").prop('disabled', true); /** will set to false later on **/
                }
            }
            ;
        });
    </script>
    <script>
        $(function () {

            $("#datefrom").datepicker({
                format: 'dd-mm-yyyy'
            });
            $("#dateto").datepicker({
                format: 'dd-mm-yyyy'
            });

            $("#composite_cbx").click(function () {
                if ($(this).is(":checked")) {
                    $("#composite").prop('disabled', false);
                } else {
                    $("#composite").prop('disabled', true);
                }
            });

            $("#created_cbx").click(function () {
                if ($(this).is(":checked")) {
                    $("#created_div").prop('disabled', false);
                } else {
                    $("#created_div").prop('disabled', true);
                }
            });

            $("#state_cbx").click(function () {
                if ($(this).is(":checked")) {
                    $("#state_div").prop('disabled', false);
                } else {
                    $("#state_div").prop('disabled', true);
                }
            });

        }
        );

    </script>

    @endsection