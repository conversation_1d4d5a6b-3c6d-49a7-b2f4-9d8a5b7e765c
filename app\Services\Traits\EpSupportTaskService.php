<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use Log;


trait EpSupportTaskService {

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskPendingReportStat(){
        
        $list = DB::connection('mysql_ep_support')->table('ep_task as a')
                    ->where('a.is_deleted',0)
                    ->where('a.status',0)
                    ->join('ep_task_category as b','b.category_id','a.category_id')
                    ->groupBy('b.category_name')
                    ->select('b.category_name',DB::raw('count(*) as total'))->get();
        
        return $list;
    }
  
    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskMissingPendingReportStat(){
        
        $list = DB::connection('mysql_ep_support')->table('ep_task_missing as a')
                    ->where('a.is_deleted',0)
                    ->whereNotIn('a.process_status',['55'])
                    ->groupBy('a.module')
                    ->select('a.module',DB::raw('count(*) as total'))->get();
        
        return $list;
    }
    
    
    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function findInvoiceNoInOSBFile($docNo){
        
        $list = DB::connection('mysql_ep_support')->table('ep_osb_batch_file as a')
                    ->where('a.file_data','like','%'.$docNo.'%')
                    ->get();
        return $list;
    }
    
    
    /**
     * return String docNo
     * @param type $docNo
     * @return Array Invoices
     */
    protected function findDetailsInvoicesByAp511($fileName){
        $list = DB::connection('mysql_ep_support')->table('ep_invoice_detail as a')
                    ->where('a.file_name',$fileName)
                    ->get();
        return $list;
    }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskSpkiPendingReportStat()
    {

        $list = DB::connection('mysql_ep_support')->table('ep_task_spki as a')
        ->where('a.is_deleted', 0)
        ->whereIn('a.status', [0, 1])
        ->groupBy('a.provider_id')
        ->select('a.provider_id', DB::raw('count(*) as total'))->get();

        return $list;
    }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getTaskSpkiCompletedReportStat()
    {

        $list = DB::connection('mysql_ep_support')->table('ep_task_spki as a')
        ->where('a.is_deleted', 0)
        ->where('a.status', 2)
        ->groupBy('a.provider_id')
        ->select('a.provider_id', DB::raw('count(*) as total'))->get();

        return $list;
    }

    /**
     * return String docNo
     * @param type $docNo
     * @return String docNo
     */
    protected function getSpkiServiceReportStat()
    {

        $list = DB::connection('mysql_ep_support')->table('ep_log_spki as els')
        ->whereDate('els.created_at', Carbon::today())
        ->groupBy('els.provider', 'els.status')
        ->select('els.provider', 'els.status', DB::raw('count(*) as total'))->get();

        return $list;
    }
}
