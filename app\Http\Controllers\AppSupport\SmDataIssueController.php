<?php

namespace App\Http\Controllers\AppSupport;

use App\Http\Controllers\Controller;
use App\Services\Traits\SupplierService;
use App\Migrate\AppSupport\SmRejectBumiStatusFixIssue;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierFullGrantService;
use App\Services\Traits\BPMService;
use App\EpSupportActionLog;
use Carbon\Carbon;
use Log;
use Illuminate\Http\Request;
use stdClass;
use Exception;
use DB;
use Auth;
use Validator;

class SmDataIssueController extends Controller
{

    use SupplierService;
    use SupplierFullGrantService;
    use BPMService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findListMofDeletedButActuallyActive()
    {

        $list = $this->getListMofDeletedButActuallyActive();

        return view('app_support.mof_deleted_to_active', ['listdata' => $list]);
    }

    public function findListMofActiveButActuallyDeleted()
    {
        $list = $this->getListMofActiveButActuallyDeleted();

        return view('app_support.mof_active_to_deleted', ['listdata' => $list]);
    }

    /**
     * Get the SQL function for mof_active_to_deleted
     * 
     * @return \Illuminate\Http\JsonResponse
     */
    public function getMofActiveToDeletedSql()
    {
        try {
            // Get the trait using reflection
            $reflection = new \ReflectionClass(SupplierService::class); // Adjust namespace as needed
            $method = $reflection->getMethod('getListMofActiveButActuallyDeleted');

            // Get the file and lines where the method is defined
            $file = $method->getFileName();
            $startLine = $method->getStartLine();
            $endLine = $method->getEndLine();

            // Read the file and extract the method code
            $fileContent = file($file);
            $methodCode = implode('', array_slice($fileContent, $startLine - 1, $endLine - $startLine + 1));

            // Extract only the SQL using regex
            preg_match('/\$q\s*=\s*["\']([\s\S]*?)["\'];/m', $methodCode, $matches);
            $sqlQuery = isset($matches[1]) ? $matches[1] : 'SQL query not found';

            return response()->json([
                'function_content' => htmlspecialchars($sqlQuery)
            ]);
        } catch (Exception $e) {
            return response()->json([
                'error' => 'Unable to retrieve SQL query',
                'message' => $e->getMessage(),
                'function_content' => 'Error extracting SQL: ' . $e->getMessage()
            ], 200); // Return 200 to handle display in the modal
        }
    }

    public function getCountSmDataIssue()
    {
        $data = [
            [
                'type' => 'mof-deleted-but-active',
                'name' => 'MOF Deleted But Actually Active',
                'count' => $this->getCountMofDeletedButActuallyActive(),
                'link' => '/app-support/mof_deleted_to_active'
            ],
            [
                'type' => 'softcert-users-not-sync',
                'name' => 'Softcert Users Not Sync',
                'count' => $this->getCountSoftcertUserNotSync(),
                'link' => '/app-support/softcert-user-not-sync'
            ],
            [
                'type' => 'personnel-is-bumi-null',
                'name' => 'Personnel Is Bumi Null',
                'count' => $this->getCountPersonnelIsBumiNull(),
                'link' => '/app-support/personnel_is_bumi_null'
            ],
            [
                'type' => 'app-with-no-category',
                'name' => 'App With No Category',
                'count' => $this->getCountApplicationWithNoCategory(),
                'link' => '/app-support/application_with_no_category'
            ],
            [
                'type' => 'app-missing-sv',
                'name' => 'App Missing SV',
                'count' => $this->getCountAppMissingSV(),
                'link' => '/app-support/app_missing_sv'
            ],
            [
                'type' => 'bumi-status-cancellation',
                'name' => 'Bumi Status Cancellation',
                'count' => $this->getCountBumiStatusCancellation(),
                'link' => '/app-support/bumi_status_cancellation'
            ],
            [
                'type' => 'site-visit-rollback',
                'name' => 'Site Visit Rollback',
                'count' => $this->getCountSiteVisitRollback(),
                'link' => '/app-support/site_visit_rollback'
            ],
            [
                'type' => 'app-status-not-sync',
                'name' => 'App Status Not Sync',
                'count' => $this->getCountApplicationNotSync(),
                'link' => '/app-support/application_status_not_sync'
            ],
            [
                'type' => 'mof-cert-exceed-3-years',
                'name' => 'MOF Cert Exceed 3 Years',
                'count' => $this->getCountMOFCertExceedThreeYears(),
                'link' => '/app-support/mof_cert_exceed_3_years'
            ]
        ];

        return $this->renderTable($data);
    }

    private function renderTable($data)
    {
        $rows = '';
        foreach ($data as $item) {
            $rows .= $this->renderTableRow($item);
        }

        return $this->getTableTemplate($rows);
    }

    private function renderTableRow($item)
    {
        return "
            <tr class='clickable-row-monitor-sm' data-issue-type='{$item['type']}' data-href='{$item['link']}'>
                <td><strong>{$item['name']}</strong></td>
                <td class='text-center'>" . htmlspecialchars($item['count']) . "</td>
            </tr>";
    }

    private function getTableTemplate($rows)
    {
        return "
            <style>
            .clickable-row-monitor-sm {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }
            .clickable-row-monitor-sm:hover {
                background-color: green !important;
            }
            </style>
            <div style='overflow-x:auto;'>
                <table class='table table-borderless table-striped table-vcenter'>
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        $rows
                    </tbody>
                </table>
            </div>";
    }

    public function getSoftcertNotSync()
    {
        $list = collect([]);


        $listRenewalNotExpired = $this->getSoftcertRenewalNotExpiredYet();
        foreach ($listRenewalNotExpired as $ob) {
            $list->push($ob);
        }

        $listOne = $this->getSoftcertUserNotSync();
        foreach ($listOne as $obj1) {
            $list->push($obj1);
        }

        $listTwo = $this->getDeletedSoftcertUserNotSync();
        foreach ($listTwo as $obj2) {
            $list->push($obj2);
        }
        $listThree = $this->getSoftcertIssuedInvalidToApply();
        foreach ($listThree as $obj3) {
            $list->push($obj3);
        }

        // 610825025877 - shahril request ignore at 3/7/2023. user create eaduan
        $listCollect = $list->whereNotIn('identification_no', ['610825025877'])->all();

        return view('app_support.softcert_users_not_sync', ['listdata' => $listCollect]);
    }

    public function getListPersonnelIsBumiNull()
    {

        $list = $this->getListDataPersonnelIsBumiNull();

        return view('app_support.personnel_is_bumi_null', ['listdata' => $list]);
    }

    public function getListApplicationWithNoCategory()
    {

        $list = $this->getListDataApplicationWithNoCateogry();

        return view('app_support.application_with_no_category', ['listdata' => $list]);
    }

    public function getAppMissingSV()
    {

        $list = $this->getListAppMissingSV();

        return view('app_support.app_missing_sv', ['listdata' => $list]);
    }

    /**
     * this record will update if found in selected list pattern problem issue.
     */
    public function updateAppMissingSV($applNo)
    {

        MigrateUtils::logDump(__FUNCTION__ . ' > APPL_NO : ' . $applNo . ' , Action By :' . Auth::user()->user_name);
        $list = collect($this->getListAppMissingSV());
        $applObj = $list->where('appl_no', $applNo)->first();

        if ($applObj == null) {
            return "INVALID";
        }
        MigrateUtils::logDump("Found, this record is valid to fix this issue on " . url('app-support/app_missing_sv'));
        MigrateUtils::logDump(json_encode($applObj));
        MigrateUtils::logDump('########--------########');
        // SET INACTIVE IS_CURRENT
        $query = DB::connection('oracle_nextgen_fullgrant')
            ->table('SM_WORKFLOW_STATUS')
            ->where('status_id', 20199)
            ->where('doc_id', $applObj->appl_id);
        $wfReg = $query->first();
        if ($wfReg != null) {
            MigrateUtils::logDump("Record found Berdaftar :> WorkflowStatusID: " . $wfReg->workflow_status_id .
                ' ,status_id: ' . $wfReg->status_id . ' ,is_current: ' . $wfReg->is_current . ' ,appl_id: ' . $wfReg->doc_id);
            $query->update(['is_current' => 0]);
            MigrateUtils::logDump("Done updated is_current as 0");
            MigrateUtils::logDump(json_encode($query->first()));
        } else {
            MigrateUtils::logDump("Not found! Status: Berdaftar to set as inactive");
        }
        MigrateUtils::logDump('########--------########');

        // SET ACTIVE IS_CURRENT
        $query2 = DB::connection('oracle_nextgen_fullgrant')
            ->table('SM_WORKFLOW_STATUS')
            ->where('status_id', 20117)
            ->where('doc_id', $applObj->appl_id);
        $wfSvisit = $query2->first();
        if ($wfSvisit != null) {
            MigrateUtils::logDump("Record found Menunggu Tugasan Lawatan Tapak - Selepas Diluluskan :> WorkflowStatusID: " . $wfSvisit->workflow_status_id .
                ' ,status_id: ' . $wfSvisit->status_id . ' ,is_current: ' . $wfSvisit->is_current . ' ,appl_id: ' . $wfSvisit->doc_id);
            $query2->update(['is_current' => 1]);
            MigrateUtils::logDump("Done updated is_current as 1");
            MigrateUtils::logDump(json_encode($query2->first()));
        } else {
            MigrateUtils::logDump("Not found! Status: Menunggu Tugasan Lawatan Tapak - Selepas Diluluskan to set as active");
        }
        MigrateUtils::logDump('########--------########');
        MigrateUtils::logDump('COMPLETED');
    }

    // public function getLisSupplierDisciplinaryAction()
    // {

    //     $list = $this->getSupplierDisciplinaryAction();

    //     return view('app_support.supplier_disciplinary_action', ['listdata' => $list]);
    // }

    public function getBumiStatusCancellation()
    {

        $list = $this->listBumiStatusCancellation();

        return view('app_support.bumi_status_cancellation', ['listdata' => $list]);
    }

    public function getApplicationStatusNotSync()
    {

        $list = $this->listApplicationNotSync();
        foreach ($list as $obj) {
            $obj->instance_bpm = '';
            $obj->composite_bpm = '';
            $obj->flow_id = '';
            $obj->activity_name = '';
            $task = $this->getTaskDetailBpmByDocNo($obj->appl_no);
            if ($task != null) {
                $obj->instance_bpm = $task->compositeinstanceid;
                $obj->composite_bpm = $task->taskdefinitionid;
                $obj->flow_id = $task->flow_id;
                $obj->activity_name = $task->activityname;
            }
        }

        return view('app_support.application_status_not_sync', ['listdata' => $list]);
    }

    public function getPTJOrgValidityRollback()
    {

        $list = $this->listPTJValidityRollback();
        $list_record_status_1 = $this->listPTJValidityRollbackRecordStatus1();

        return view('app_support.ptj_org_validity_rollback', [
            'listdata' => $list,
            'listdata_record_status_1' => $list_record_status_1
        ]);
    }

    public function fixIssueBumiStatusCancellation()
    {
        $applNo = request()->applNo;
        if ($applNo && strlen($applNo) > 0) {
            $list = collect($this->listBumiStatusCancellation());
            $c = $list->where('appl_no', $applNo)->count();
            if ($c > 0) {
                $actionTypeLog = 'Script';
                $actionName = 'PatchDataTable';

                $parameters = collect([]);
                $parameters->put("remarks", 'Fix Issue Data SM in List of Bumi Status Cancellation');
                $parameters->put("table", array('SM_APPL', 'SM_MOF_CERT'));
                $parameters->put("appl_no", $applNo);
                $parameters->put("patching", array('SM_APPL', 'SM_MOF_CERT'));

                SmRejectBumiStatusFixIssue::fixIssueRejectBumiStatusByApplNo($applNo);

                $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Completed');
                return "Completed";
            }
            return "Doc No Is Not Valid!";
        }

        return "Invalid Parameter!";
    }

    public function findSiteVisitRollback()
    {
        $list = $this->listSiteVisitRollback();

        return view('app_support.site_visit_rollback', ['listdata' => $list]);
    }

    public function getReportingList()
    {
        $getModule = $this->getDataModule();
        $log_list = $this->listLogAction();
        return view('app_support.reporting_list', [
            'getModule' => $getModule,
            'file_name' => null,
            'number' => null,
            'link' => null,
            'log_list' => $log_list
        ]);
    }

    public function listModuleDetails($module)
    {
        $listDetails = $this->listModuleDetail($module);
        return response()->json($listDetails, 200);
    }

    public function listFieldName($name)
    {
        $listFields = $this->fieldNameList($name);

        return response($listFields[0]->rpt_config);
    }

    public function getReportVariable(Request $request)
    {
        $getModule = $this->getDataModule();
        $loginId = $request->user_id;
        $rpt_code = $request->rpt_code;
        $rpt_fileName = $request->rpt_file_name;
        $date_from = carbon::parse($request->date_from);
        $date_end = carbon::parse($request->date_end);
        $userId = null;
        $file_name = $request->report_name;
        $link = null;
        $number = null;
        $appl_id = null;
        $zone = null;
        $urlReport = env('EP_REPORT_URL', 'http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd');
        $log_list = $this->listLogAction();
        if ($loginId !== null) {
            $user = $this->pm_user($loginId);
            $userId = $user[0]->user_id;
        } else {
            $userId = 1;
        }
        if ($request->module1 == 'Direct Purchase') {
            $number = $request->rn_no;
            $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PGROUP=STATUS&PUSER=' . $userId . '&RQNO=' . $number;
            $user = auth()->user()->first_name;
            DB::connection('mysql_ep_support')->table('ep_report_log_action')->insert([
                ['log_user_name' => $user, 'log_module' => $request->module1, 'log_file_name' => $request->report_name, 'log_login_id' => $loginId, 'log_doc_no' => $number, 'log_created_date' => Carbon::now()],
            ]);
        }

        if ($request->module1 == 'Fulfilment') {
            if ($request->po_no != null) {
                $number = $request->po_no;
            } else if ($request->do_no != null) {
                $number = $request->do_no;
            } else if ($request->pa_no != null) {
                $number = $request->pa_no;
            } else if ($request->frn_no != null) {
                $number = $request->frn_no;
            } else if ($request->dan_no != null) {
                $number = $request->dan_no;
            } else if ($request->can_no != null) {
                $number = $request->can_no;
            } else if ($request->inv_no != null) {
                $number = $request->inv_no;
            }

            $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&PNO=' . $number;

            $user = auth()->user()->first_name;
            DB::connection('mysql_ep_support')->table('ep_report_log_action')->insert([
                ['log_user_name' => $user, 'log_module' => $request->module1, 'log_file_name' => $request->report_name, 'log_login_id' => $loginId, 'log_doc_no' => $number, 'log_created_date' => Carbon::now()],
            ]);
        }

        if ($request->module1 == 'Quotation Tender') {
            $number = $request->qt_no;
            $qt_id = $this->findQtNo($number);
            $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&QTID=' . $qt_id[0]->qt_id;

            $user = auth()->user()->first_name;
            DB::connection('mysql_ep_support')->table('ep_report_log_action')->insert([
                ['log_user_name' => $user, 'log_module' => $request->module1, 'log_file_name' => $request->report_name, 'log_login_id' => $loginId, 'log_doc_no' => $number, 'log_created_date' => Carbon::now()],
            ]);
        }

        if ($request->module1 == 'Supplier Management') {
            $number = $request->mof_no;
            if ($request->report_name == 'View Profile Company') {
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&PMOFNO=' . $number;
            }
            if ($request->report_name == 'Supplier Performance') {
                $number = $request->mof_no;
                $supp_id = $this->findSuppId($number);
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PSUPPID=' . $supp_id[0]->supplier_id . '&PFROMDATE=' . $date_from->format('d-M-Y') . '&PTOMDATE=' . $date_end->format('d-M-Y');
            }
            if ($request->report_name == 'Application Details') {
                $number = $request->appl_no;
                $appl_id = $this->findApplNo($number);
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PAPPLNO=' . $number . '&APPL_ID=' . $appl_id[0]->appl_id;
            }
            if ($request->report_name == 'Application Approved') {
                $number = $request->user_id;
                $zone = $request->zone;
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&PFROMDATE=' . $date_from->format('d-M-Y') . '&PTODATE=' . $date_end->format('d-M-Y') . '&PSUPPZONE=' . $zone;
            }
            if ($request->report_name == 'List of Daily Receipt Report') {
                $number = $date_from->format('d/m/Y') . ' - ' . $date_end->format('d/m/Y');
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&PFROMDATE=' . $date_from->format('d/m/Y') . '&PTODATE=' . $date_end->format('d/m/Y') . '&planguage=ms';
            }
            if ($request->report_name == 'Collection on Payment Method') {
                $number = $date_from->format('d/m/Y') . ' - ' . $date_end->format('d/m/Y');
                $payment_type = $request->payment_type;
                $link = $urlReport . '&report=' . $rpt_fileName . '&PREPORTID=' . $rpt_code . '&destype=cache&desformat=pdf&PUSER=' . $userId . '&PFROMDATE=' . $date_from->format('d/m/Y') . '&PTODATE=' . $date_end->format('d/m/Y') . '&ppaymentmenthod=' . $payment_type . '&ppaytype1=M&ppaytype2=P&ptype=TARIKH%20RESIT&planguage=ms';
            }

            $user = auth()->user()->first_name;
            DB::connection('mysql_ep_support')->table('ep_report_log_action')->insert([
                ['log_user_name' => $user, 'log_module' => $request->module1, 'log_file_name' => $request->report_name, 'log_login_id' => $loginId, 'log_doc_no' => $number, 'log_date_from' => $date_from, 'log_date_to' => $date_end, 'log_appl_id' => $appl_id, 'log_zone' => $zone, 'log_type' => 'R', 'log_created_date' => Carbon::now()],
            ]);
        }



        return view('app_support.reporting_list', [
            'number' => $number,
            'getModule' => $getModule,
            'file_name' => $file_name,
            'link' => $link,
            'userId' => $userId,
            'log_list' => $log_list
        ]);
    }

    public function getListMOFCertExceed3Years()
    {

        $list = $this->getListMOFCertExceedThreeYears();

        return view('app_support.mof_cer_exceed_three_years', ['listdata' => $list]);
    }

    public function getListPaymentByRazer()
    {

        $list = $this->getListSupplierPaymentCardDebitMismatch();
        foreach ($list as $d) {
            $pymnt = DB::connection('oracle_nextgen_rpt')->table('py_payment')->where('payment_id', $d->order_id)->first();
            $pymntOrd = DB::connection('oracle_nextgen_rpt')->table('py_payment_order')->where('payment_id', $d->order_id)->first();
            $d->ep_channel_name = null;
            $d->ep_card_type = null;
            if ($pymnt != null && $pymntOrd != null) {
                $d->ep_channel_name = $pymntOrd->channel_name;
                $d->ep_card_type = $pymnt->card_type;
            }

            if ($d->free_bin_type == null) {
                $objFreeBinExist = DB::connection('mysql_ep_support')->table('ep_support.ep_supplier_payment_razer')
                    ->where('card_bin', $d->card_bin)
                    ->whereNotNull('free_bin_type')
                    ->distinct()
                    ->select('free_bin_verify', 'free_bin_type')
                    ->first();
                // If found , mean already verify previosly . No need to verify manual.
                if ($objFreeBinExist != null) {
                    Log::info(__METHOD__ . " auto fix checking card_bin:$d->card_bin ==> orderid:$d->order_id , tran_id:$d->tran_id updated free_bin_verify:$objFreeBinExist->free_bin_verify ,free_bin_type:$objFreeBinExist->free_bin_type");

                    DB::connection('mysql_ep_support')
                        ->table('ep_support.ep_supplier_payment_razer')
                        ->where('order_id', $d->order_id)
                        ->where('tran_id', $d->tran_id)
                        ->update(
                            [
                                'free_bin_verify' => $objFreeBinExist->free_bin_verify,
                                'free_bin_type' => $objFreeBinExist->free_bin_type,
                                'verify_at' => Carbon::now(),
                                'verify_by' => Auth::user()->user_name . '- Auto Check'
                            ]
                        );
                    $d->free_bin_verify = $objFreeBinExist->free_bin_verify;
                    $d->free_bin_type = $objFreeBinExist->free_bin_type;
                }
            }
        }

        return view('app_support.supplier_payment_razer', ['listdata' => $list]);
    }

    public function updatePaymentRazerBinType(Request $request)
    {

        Validator::make(request()->all(), [
            'input_orderid' => 'required',
            'input_tranid' => 'required',
            'input_bintype' => 'required'
        ])->validate();

        $result = 'Failed to update record.';

        $query = DB::connection('mysql_ep_support')
            ->table('ep_supplier_payment_razer')
            ->where('order_id', $request->input_orderid)
            ->where('tran_id', $request->input_tranid);

        $updateRow = $query->first();

        if ($updateRow != null) {
            $freeBinVerify = 0;
            if ($updateRow->bin_type === $request->input_bintype) {
                $freeBinVerify = 1;
            }

            $query->update([
                'free_bin_verify' => $freeBinVerify,
                'free_bin_type' => $request->input_bintype,
                'verify_at' => Carbon::now(),
                'verify_by' => Auth::user()->user_name
            ]);
            $result = 'Record with Order ID ' . $updateRow->order_id . ' is successfully updated.';
        }

        $list = $this->getListSupplierPaymentCardDebitMismatch();
        foreach ($list as $d) {
            $pymnt = DB::connection('oracle_nextgen_rpt')->table('py_payment')->where('payment_id', $d->order_id)->first();
            $pymntOrd = DB::connection('oracle_nextgen_rpt')->table('py_payment_order')->where('payment_id', $d->order_id)->first();
            $d->ep_channel_name = null;
            $d->ep_card_type = null;
            if ($pymnt != null && $pymntOrd != null) {
                $d->ep_channel_name = $pymntOrd->channel_name;
                $d->ep_card_type = $pymnt->card_type;
            }
        }
        return view('app_support.supplier_payment_razer', ['listdata' => $list, 'successUpdate' => true, 'message' => $result]);
    }

    public function displaySmDashboardView()
    {
        return view('app_support.dashboard.sm');
    }

    public function getSupplierRegisterNotExistSAPError()
    {
        $result = $this->getSupplierRegisterNotExistSAPErrorService();

        $html = "
        <style>
            .clickable-row-sap {
                cursor: pointer;
                transition: background-color 0.2s ease;
            }
            .clickable-row-sap:hover {
                background-color: green !important;
            }
        </style>
        <div style='overflow-x:auto;'>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Supplier Type Code</th>
                        <th>Supplier Type Name</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
                    <tr class='clickable-row-sap' data-supplier-type='" . htmlspecialchars($data->supplier_type_code) . "'>
                        <td style='width: 33%;'><strong>" . htmlspecialchars($data->supplier_type_code) . "</strong></td>
                        <td class='text-center'>" . htmlspecialchars($data->supplier_type_name) . "</td>
                        <td class='text-center'>" . htmlspecialchars($data->total) . "</td>
                    </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function getListSupplierRegisterNotExistSAPError($supplierType)
    {
        $result = $this->getListSupplierRegisterNotExistSAPErrorService($supplierType);
        return $this->generateSupplierTable($result);
    }

    private function generateSupplierTable($result)
    {
        if (empty($result)) {
            return "<p>No data available.</p>";
        }

        $html = "
        <div class='table-responsive'>
            <table id='supplierTable' class='table table-bordered table-striped table-hover'>
                <thead>
                    <tr>
                        <th>Supplier ID</th>
                        <th>EP No</th>
                        <th>Company Name</th>
                        <th>Reg No</th>
                        <th>Changed Date</th>
                        <th>Supplier Type</th>
                        <th>Status ID</th>
                        <th>Appl Type</th>
                        <th>Total Same Reg No</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($result as $data) {
            $html .= "
            <tr data-supplier-type='" . htmlspecialchars($data->supplier_type) . "'>
                <td><a target='_blank' href='/find/epno/" . urlencode($data->ep_no) . "'>" . htmlspecialchars($data->supplier_id) . "</a></td>
                <td><a target='_blank' href='/trigger/gfmas/apive/?ep_no=" . urlencode($data->ep_no) . "'>" . htmlspecialchars($data->ep_no) . "</a></td>
                <td><a target='_blank' href='/find/byname/?nama_pembekal=" . urlencode($data->company_name) . "'>" . htmlspecialchars($data->company_name) . "</a></td>
                <td><a target='_blank' href='/find/byname/?nama_pembekal=" . urlencode($data->reg_no) . "'>" . htmlspecialchars($data->reg_no) . "</a></td>
                <td>" . htmlspecialchars($data->changed_date) . "</td>
                <td>" . htmlspecialchars($data->supplier_type) . "</td>
                <td>" . htmlspecialchars($data->status_id) . "</td>
                <td>" . htmlspecialchars($data->appl_type) . "</td>
                <td>" . htmlspecialchars($data->total_same_reg_no) . "</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>
        <script>
        $(document).ready(function() {
            $('#supplierTable').DataTable({
                responsive: true,
                'pageLength': 10,
                'lengthMenu': [[10, 25, 50, -1], [10, 25, 50, 'All']]
            });
        });
        </script>";

        return $html;
    }

    private function getListSupplierPaymentCardDebitMismatch()
    {
        $list = $this->getListSupplierPaymentByRazer();
        foreach ($list as $d) {
            $pymnt = DB::connection('oracle_nextgen_rpt')->table('py_payment')->where('payment_id', $d->order_id)->first();
            $pymntOrd = DB::connection('oracle_nextgen_rpt')->table('py_payment_order')->where('payment_id', $d->order_id)->first();
            $d->ep_channel_name = null;
            $d->ep_card_type = null;
            if ($pymnt != null && $pymntOrd != null) {
                $d->ep_channel_name = $pymntOrd->channel_name;
                $d->ep_card_type = $pymnt->card_type;
            }
        }
        return $list;
    }

    public function getAppSupplierDisciplinaryAction()
    {

        $list = $this->getAllListSupplierDisciplinaryAction();

        return view('app_support.supplier_disciplinary_action_app', ['listdata' => $list]);
    }

    public function getModuleFull()
    {
        $moduleList = $this->getListModuleFull();
        return view('app_support.report_all_module', [
            'moduleList' => $moduleList,
            // 'results' => null,
        ]);
    }

    public function getReportsByModule($module)
    {
        $reports = $this->getReportName($module);
        return response()->json($reports);
    }

    public function searchReports(Request $request)
    {
        $moduleName = $request->module_name;
        $reportName = $request->report_name;
        // Validate parameters
        if (!$moduleName || !$reportName) {
            return response()->json(['error' => 'Module and Report are required.'], 400);
        }

        try {
            $results = DB::connection('oracle_nextgen_rpt')
                ->select(DB::raw("
                SELECT pp.PARAMETER_CODE, ppd.CODE_NAME, pr.*
                FROM PM_PARAMETER pp
                JOIN PM_PARAMETER_DESC ppd ON pp.PARAMETER_ID = ppd.PARAMETER_ID
                JOIN PM_REPORT pr ON pp.PARAMETER_CODE = pr.MODULE_CODE
                WHERE pp.PARAMETER_TYPE = 'MOD'
                AND ppd.LANGUAGE_CODE = 'en'
                AND pp.PARAMETER_CODE NOT IN ('CD')
                AND pr.RECORD_STATUS = 1
                AND pr.MODULE_CODE = :moduleCode
                AND pr.REPORT_CODE = :reportCode
                ORDER BY ppd.CODE_NAME ASC
            "), [
                    'moduleCode' => $moduleName,
                    'reportCode' => $reportName
                ]);

            // Check if no results are found
            if (empty($results)) {
                return response()->json(['message' => 'No reports found.'], 404);
            }

            return response()->json($results);

        } catch (\Exception $e) {
            return response()->json(['error' => 'An error occurred. Please try again later.'], 500);
        }
    }
}
