@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('find/qt/checkingBSV')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="doc_no" name="doc_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Sila Masukkan No QT, LA, LI, BD, DL Sahaja..">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <!--            <div class="header-section">
                    <h1>
                        <i class="gi gi-search"></i>Sila Masukkan No QT, LA, LI, BD, DL <br>
                        <small></small>
                    </h1>
                </div>-->
</div>
@if($qtinfo == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Rekod Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif
<!--Akmal Region QT INFO & BRIEFING SITE VISIT-->
@if($qtinfo)
<div class="block block-alt-noborder full">
    <!--AKMAL REGION DETAILS INFO-->
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-users"></i> SUMMARY | <font color="yellow">{{ $qtinfo->qt_no }}</font></h6>

            @if($newqt!=null)
            <h6> NEW QT NO | <font color="orange">{{ $newqt->qtno2 }}</font> ( <font color="orange">{{ $newqt->qt_id }}</font> )</h6>  
            @endif

            @if($previousqt!=null)
            <h6> PREVIOUS QT NO | <font color="orange">{{ $previousqt->pqtno }}</font> ( <font color="orange">{{ $previousqt->pqtid }}</font> )</h6>  
            @endif

            <h6> STATUS: <font color="yellow"> {{ $qtinfo->status_name }}</font></h6>
        </div>
        <div class="row">
            <div class="col-md-12">
                <h6><strong>{{ $qtinfo->qt_title }}</strong></h6><br />
            </div>    
            <div class="col-md-3">
                <address>
                    <strong>Quotation/Tender</strong> : <font color="red">{{ $qtinfo->qt_no }}</font><br />
                    <strong>Procurement</strong> : <font color="red">{{ $qtinfo->procurement }}</font><br />
                    <strong>Publish Date</strong> : <font color="red">{{ $qtinfo->publish_date }}</font><br />
                    <strong>Closing Date</strong> : <font color="red">{{ $qtinfo->closing_date }}</font>
                    @if($qtinfo->checkvalidityiklan != null && $qtinfo->checkvalidityiklan == true)
                    <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Closed! "></i>
                    <span style="font-weight: bolder; color:red;"> CLOSED </span>
                    @endif 
                    <br />

                    <br />
                </address>
            </div>
            <div class="col-md-4">
                <address>
                <strong>PTJ</strong> : <font color="red"> {{ $qtinfo->for_ptj_code }} - {{ $qtinfo->for_ptj_name }}</font><br />
                @if($qtinfo->checkpanel != null && $qtinfo->checkpanel == '0')
                    <strong>PANEL</strong> : <font color="red">{{ $qtinfo->panel }}</font>
                    @endif 
                    <br />
                    <strong>ZONAL</strong> : <font color="red">{{ $qtinfo->is_zonal }} </font>
                    @if($kodzonal!=null && $qtinfo->is_zonal ='1')
                    <a href='#modal-list-trans-dofnzonal'
                    class='modal-list-data-action ' 
                    data-toggle='modal'  
                    data-url='/find/qt/checkingBSV/'
                    data-title='Senarai Zon Yang Telah Ditetapkan'>
                    <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="Klik Untuk Papar!"></i>
                    </strong>
                    </a>  
                    @endif
                    @if($qtinfo->checkpanel != null && $qtinfo->checkpanel == '1')
                    <strong>PANEL</strong> :
                    <i class="gi gi-circle_exclamation_mark  text-INFO" style="font-size: 10pt;" title="Panel! "></i>
                    <span style="font-weight: bolder; color:blue;"> PANEL </span>
                    @endif 
                    <br />
                    <strong>BSV</strong> : <font color="red">{{ $qtinfo->brefingsitevisit }}</font> 
                </address>
            </div>
        </div>
    </div>

    <!--AKMAL REGION BSV-->
    @if($listdata22 != null)
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-building-o"></i> BRIEFING SITE VISIT  | </h6>
            <h6>BSV DATE - <font color="yellow">{{ $getbsv }}</font></h6> 
        </div>
        <div class="row">
            <div class="col-md-12">
                <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered" style="text-shadow: TESTING">
                    <thead>
                        <tr>
                        <th class="text-center">Company Name</th>
                            <th class="text-center">Status</th>   
                            <th class="text-center">Attended & BSV Type</th>    
                            <th class="text-center">Zone</th>  
                            <th class="text-center">Title</th>    
                            <th class="text-center">Add Registration
                            <a href='#modal-list-trans-dofn6'
               class='modal-list-data-action' 
               data-toggle='modal'  
               data-url='find/qt/checkingBSV'
               data-title='Register by Desk Officer'>
                <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="QT Maintenance By Desk Officer"></i>
                </strong>
            </a> 
                            </th> 
                              
                    </br>   
                            <th class="text-center">Approver</th>    
                            <th class="text-center">Action</th>    
                        </tr>
                    </thead>
                    @foreach ($listdata22 as $key=>$sqtbsv)
                    <tr>
                        <td class="text-left">  {{ $sqtbsv->mof_no }} </a> | {{ $sqtbsv->company_name }}</td>                           
                        <td class="text-center">{{ $sqtbsv->disqualified_stage }}</td>
                        <td class="text-center">{{ $sqtbsv->is_attended }} | {{ $sqtbsv->bsv_type2 }}</td>
                        <td class="text-center">{{ $sqtbsv->zonename }}</td>
                        <td class="text-center">
                            <span class="less{{ $key }}">{{ str_limit($sqtbsv->bsv_desc, $limit = 38, $end = '...') }}</span>
                            <span class="details{{ $key }}" style="display:none">{{ $sqtbsv->bsv_desc }}</span>
                            <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                    });">See More</a>
                        </td>
                        <td class="text-center">{{ $sqtbsv->ispost_registered }}
                            @if($sqtbsv->np == true)
                            <a href='#modal-list-trans-dofn'
                               class='modal-list-data-action ' 
                               data-toggle='modal'  
                               data-url='/find/qt/checkingBSV{{ $sqtbsv->ispost_registered }}'
                               data-title='Need Patching {{ $sqtbsv->ispost_registered }}'>
                                <strong style="font-weight: bolder;">
                                    <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="Need Patching disqualified_stage!"></i> 
                                </strong><br />
                            </a>
                            @endif
                        </td>   
                        <td class="text-center">{{ $sqtbsv->qt_approval_request_id }}</td>
                        <td class="text-center">{{ $sqtbsv->approver_action_id }}</td>
                    </tr>
                    @endforeach                        
                </table>
            </div>
        </div>
    </div>
    @endif

  
</div>
@endif

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {TablesDatatables.init(); });</script>


<script>
                    var APP_URL = {!! json_encode(url('/')) !!}

            App.datatables();
                    /* Initialize Datatables */
                    var tableListData = $('#basic-datatable').DataTable({
            columnDefs: [{orderable: false, targets: [0]}],
                    pageLength: 10,
                    lengthMenu: [[10, 20, 30, - 1], [10, 20, 30, 'All']]
            });
                    $(document).ready(function () {
            $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
                    $('#basic-datatable').html('').fadeIn();
                    $('#modal-list-data-header').text($(this).attr('data-title'));
                    /* Destroy ID Datatable, To make sure reRun again ID */
                    tableListData.destroy();
                    $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                            type: "GET",
                            success: function (data) {
                            $data = $(data);
                                    $('#basic-datatable').html($data).fadeIn();
                                    /* Re-Initialize Datatable */
                                    tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [{orderable: false, targets: [0]}],
                                    pageLength: 10,
                                    lengthMenu: [[10, 20, 30, - 1], [10, 20, 30, 'All']]
                            });
                                    $('.spinner-loading').hide();
                            }
                    });
            });
            });
            });

</script>

@endsection