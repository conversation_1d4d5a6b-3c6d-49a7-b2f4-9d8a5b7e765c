<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 * <AUTHOR>
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PomService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use App\Services\CmsService;
use App\Services\CRMService;
use App\EpSupportActionLog;

class POMSCRMDashboardController extends Controller {

    public static function crmService() {
        return new CRMService;
    }

    public static function pomService() {
        return new PomService;
    }

    public static function cmsService() {
        return new CmsService;
    }

    public function __construct() {
        $this->middleware('auth');
    }

    public function main(Request $request) {
        try {
            $currentYear = Carbon::now()->year;
            $selectedYear = (int)$request->input('year', $currentYear);
            $results = [];
            
            // Generate years from 2024 to current year
            $years = range(2024, $currentYear);
            rsort($years); // Sort years in descending order

            // Validate selected year is within range
            if ($selectedYear < 2024 || $selectedYear > $currentYear) {
                $selectedYear = $currentYear;
            }

            // Get data for the selected year only
            $maxMonth = ($selectedYear == $currentYear) ? Carbon::now()->month : 12;
            for ($month = 1; $month <= $maxMonth; $month++) {
                $monthlyData = self::pomService()->getMonthlySlaCounts($selectedYear, $month);
                if ($monthlyData) {
                    $results[] = $monthlyData;
                }
            }

            // Sort results by year and month in descending order (newest first)
            usort($results, function($a, $b) {
                if ($a['YEAR'] == $b['YEAR']) {
                    return $b['MONTH'] - $a['MONTH'];
                }
                return $b['YEAR'] - $a['YEAR'];
            });

            return view('poms.main', [
                'data' => $results,
                'years' => $years,
                'selectedYear' => (int)$selectedYear
            ]);

        } catch (\Exception $e) {
            Log::error('Error in POMSCRMDashboardController@main: ' . $e->getMessage());
            return redirect()->back()->with('error', 'An error occurred while loading the dashboard.');
        }
    }

    public function getCaseList(Request $request) {
        try {
            $caseType = $request->input('type');
            $month = $request->input('month');
            $year = $request->input('year');
            
            // Validate inputs
            if (empty($caseType) || empty($month) || empty($year)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing required parameters: type, month, or year'
                ], 400);
            }
            
            $caseList = [];
            $title = '';
            
            switch ($caseType) {
                case 'ACK_COUNT':
                    $caseList = self::pomService()->getCaseListItCoord($month, $year);
                    $title = 'IT Coordinator Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'RIT_COUNT':
                    $caseList = self::pomService()->getCaseListItSpec($month, $year);
                    $title = 'IT Specialist Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'S123_COUNT':
                    $caseList = self::pomService()->getCaseListItSeverity($month, $year);
                    $title = 'IT Severity Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'ACK_DUPLICATE':
                    $caseList = self::pomService()->getDuplicateCaseListItCoord($month, $year);
                    $title = 'Duplicate IT Coordinator Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'RIT_DUPLICATE':
                    $caseList = self::pomService()->getDuplicateCaseListItSpec($month, $year);
                    $title = 'Duplicate IT Specialist Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'S123_DUPLICATE':
                    $caseList = self::pomService()->getDuplicateCaseListItSeverity($month, $year);
                    $title = 'Duplicate IT Severity Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid case type specified'
                    ], 400);
            }
            
            return response()->json([
                'success' => true,
                'title' => $title,
                'data' => $caseList
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error in POMSCRMDashboardController@getCaseList: ' . $e->getMessage());
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving case list.'
            ], 500);
        }
    }
    
    /**
     * Get modal data for POMS dashboard
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getModalData(Request $request)
    {
        try {
            $type = $request->input('type');
            $month = $request->input('month');
            $year = $request->input('year');
            
            // Validate inputs
            if (empty($type) || empty($month) || empty($year)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing required parameters: type, month, or year',
                    'debug' => [
                        'type' => $type,
                        'month' => $month,
                        'year' => $year
                    ]
                ], 400);
            }
            
            $data = [];
            $title = '';
            
            // Determine which service method to call based on the type
            switch ($type) {
                case 'ACK_COUNT':
                    $data = self::pomService()->getCaseListItCoord($month, $year);
                    $title = 'IT Coordinator Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'RIT_COUNT':
                    $data = self::pomService()->getCaseListItSpec($month, $year);
                    $title = 'IT Specialist Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'S123_COUNT':
                    $data = self::pomService()->getCaseListItSeverity($month, $year);
                    $title = 'IT Severity Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'ACK_DUPLICATE':
                    $data = self::pomService()->getDuplicateCaseListItCoord($month, $year);
                    $title = 'Duplicate IT Coordinator Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'RIT_DUPLICATE':
                    $data = self::pomService()->getDuplicateCaseListItSpec($month, $year);
                    $title = 'Duplicate IT Specialist Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                case 'S123_DUPLICATE':
                    $data = self::pomService()->getDuplicateCaseListItSeverity($month, $year);
                    $title = 'Duplicate IT Severity Cases - ' . date('F Y', mktime(0, 0, 0, $month, 1, $year));
                    break;
                    
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid case type specified'
                    ], 400);
            }
            
            return response()->json([
                'success' => true,
                'title' => $title,
                'data' => $data,
                'debug' => [
                    'type' => $type,
                    'month' => $month,
                    'year' => $year
                ]
            ]);
            
        } catch (\Exception $e) {
            $errorMessage = 'Error in POMSCRMDashboardController@getModalData: ' . $e->getMessage() . ' in ' . $e->getFile() . ' on line ' . $e->getLine();
            Log::error($errorMessage);
            Log::error('Stack trace: ' . $e->getTraceAsString());
            
            return response()->json([
                'success' => false,
                'message' => 'An error occurred while retrieving modal data.',
                'error' => [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'type' => get_class($e)
                ],
                'debug' => [
                    'type' => $type ?? 'not provided',
                    'month' => $month ?? 'not provided',
                    'year' => $year ?? 'not provided'
                ]
            ], 500);
        }
    }

}
