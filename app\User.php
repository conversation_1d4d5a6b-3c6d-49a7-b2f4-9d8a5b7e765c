<?php

namespace App;

use Illuminate\Notifications\Notifiable;
use Illuminate\Foundation\Auth\User as Authenticatable;
use DB;
use Auth;
use Config;
use Carbon\Carbon;

/** This class user re-pointing to CDCRM table users **/
class User extends Authenticatable {

    protected $primaryKey = 'id';

    public $incrementing = false;

    use Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'id', 'first_name', 'last_name', 'user_name',
    ];

    /**
     * The attributes that should be hidden for arrays.
     *
     * @var array
     */
    protected $hidden = [
        'user_hash', 'remember_token',
    ];

    
    public function hasRole($roles) {

        if (is_array($roles)) {
            foreach ($roles as $need_role) {
                if ($this->checkIfUserHasRole($need_role)) {
                    return true;
                }
            }
        } else {
            return $this->checkIfUserHasRole($roles);
        }
        return false;
    }

    public function selectedUser($users) {

        if (is_array($users)) {
            foreach ($users as $user) {
                if ($this->checkSelectedUser($user)) {
                    return true;
                }
            }
        } else {
            return $this->checkSelectedUser($users);
        }
        return false;
    }
    
    public function roles (){
        /*
        $query = DB::table('securitygroups_users as a');
        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
        $query->where('a.deleted',0);
        $query->where('a.user_id',Auth::user()->id);
        $query->select('b.name','b.id');
        return $query->get();
        */
        
        $dateToday = Carbon::now();
        $userLogin = DB::connection('mysql_ep_support')->table('ep_login_history')
                    ->where('user_id',Auth::user()->id)
                    ->whereDate('date',$dateToday->format('Y-m-d')) 
                    ->first();
        if($userLogin){
            return json_decode($userLogin->groups);
        }
        return null;
        
    }
    
    public function getLoginHistory(){
        
        $dateToday = Carbon::now();
        $userLogin = DB::connection('mysql_ep_support')->table('ep_login_history')
                    ->where('user_id',Auth::user()->id)
                    ->whereDate('date',$dateToday->format('Y-m-d')) 
                    ->first();
        return $userLogin;
        
    }
    
    public static function getEmailUser($userId){
        
        $query = DB::table('email_addr_bean_rel as a');
        $query->join('email_addresses as b', 'b.id', '=', 'a.email_address_id');
        $query->where('a.deleted',0);
        $query->where('b.deleted',0);
        $query->where('a.bean_id',$userId);
        $query->select('b.email_address');
        return $query->first();
        
        /*
         * 
SELECT b.email_address FROM email_addr_bean_rel a, email_addresses b  WHERE 
a.email_address_id = b.id 
AND a.bean_module = 'Users' 
AND bean_id = '6115672c-c555-4d61-8e80-03543f7bd264'  
AND a.`deleted` = 0 
AND b.`deleted` = 0 
 LIMIT 0,1 
         */
        
    }
    
    public static function listRolesCRM ($user){
        
        $query = DB::table('securitygroups_users as a');
        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
        $query->where('a.deleted',0);
        $query->where('a.user_id',$user->id);
        $query->select('b.name','b.id');
        return $query->get();
        
        
    }
    
    
    public static function isAllowAccessByGroupCRM($request) {

        $groups = Config::get('constant.group_access');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('username', $request->user_name)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');

//        $query = DB::table('securitygroups_users as a');
//        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
//        $query->where('a.deleted',0);
//        $query->where('a.user_id',Auth::user()->id);
//        $query->whereIn('b.name',  Config::get('constant.group_access') );
        $res = $query->count();
        if ($res > 0) {
            return true;
        }
        return false;
    }

    public function isAdvRolesEp() {

        $groups = Config::get('constant.roles_adv_ep');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
//        $query = DB::table('securitygroups_users as a');
//        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
//        $query->where('a.deleted',0);
//        $query->where('a.user_id',Auth::user()->id);
//        $query->whereIn('b.name',  Config::get('constant.roles_adv_ep') );
        return $query->count();
    }

    public function isITSpecialistRoles() {

        $groups = Config::get('constant.roles_it_specialist');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');

//        $query = DB::table('securitygroups_users as a');
//        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
//        $query->where('a.deleted',0);
//        $query->where('a.user_id',Auth::user()->id);
//        $query->whereIn('b.name',  Config::get('constant.roles_it_specialist') );
        return $query->count();
    }

    public function isPatcherRolesEp() {

        $groups = Config::get('constant.roles_patch_ep');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');

//        $query = DB::table('securitygroups_users as a');
//        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
//        $query->where('a.deleted',0);
//        $query->where('a.user_id',Auth::user()->id);
//        $query->whereIn('b.name',  Config::get('constant.roles_patch_ep') );
        return $query->count();
    }

    public function isDevUsersEp() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.users_dev_ep'));
//        $query = DB::table('users as a');
//        $query->where('a.deleted',0);
//        $query->where('a.id',Auth::user()->id);
//        $query->whereIn('a.user_name',  Config::get('constant.users_dev_ep') );
        return $query->count();
    }

    public function isUsersReport() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.users_report'));
        return $query->count();
    }

    public function isUsersEpOperation() {
        $groups = Config::get('constant.roles_ep_operation');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }
    
    public function isCSUsersEp() {

        $groups = Config::get('constant.roles_cs_ep');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isAllowAccessSMModule() {

        $groups = Config::get('constant.roles_access_smmodule');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isAllowAccessBasic() {
        $groups = Config::get('constant.roles_basic_access');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }
    
    /**
    Change Release Head Manager to view epss */
    public function isAllowUserCrMgmt() {
        $groups = Config::get('constant.users_cr_mgmt');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }
    
    public function isItSupportTeam() {
        $groups = Config::get('constant.roles_it_support');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }
    
    public function isCodiUsersEp() {

        $groups = Config::get('constant.roles_codi_ep');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isSpecialistUsers() {

        $groups = Config::get('constant.roles_specialist');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isEpSpecialistUsers() {

        $groups = Config::get('constant.roles_ep_specialist');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function checkIfUserHasRole($need_role) {
        
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date', Carbon::now()->format('Y-m-d'))
                        ->where('groups','like','%' .$need_role. '%')->orderBy('last_login', 'desc');
        $checkExist = $query->count();
        if ($checkExist == 1) {
            return true;
        }
        return false;
    }

    private function checkSelectedUser($user) {
        
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('username', $user)
                        ->whereDate('date', Carbon::now()->format('Y-m-d'))
                        ->orderBy('last_login', 'desc');
        $checkExist = $query->count();
        if ($checkExist == 1) {
            return true;
        }
        return false;
    }
    
    public function isItDeveloperUsers() {

        $groups = Config::get('constant.roles_it_developer');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function getRememberToken() {
        return null; // not supported
    }

    public function setRememberToken($value) {
        // not supported
    }

    public function getRememberTokenName() {
        return null; // not supported
    }

    /**
     * Overrides the method to ignore the remember token.
     */
    public function setAttribute($key, $value) {
        $isRememberTokenAttribute = $key == $this->getRememberTokenName();
        if (!$isRememberTokenAttribute) {
            parent::setAttribute($key, $value);
        }
    }

    public function isStlCrmUser() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.roles_stl_crm'));
        return $query->count();
    }
         
    public function isDbaUsersEpss() {

        $groups = Config::get('constant.roles_dba_users');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');

//        $query = DB::table('securitygroups_users as a');
//        $query->join('securitygroups as b', 'b.id', '=', 'a.securitygroup_id');
//        $query->where('a.deleted',0);
//        $query->where('a.user_id',Auth::user()->id);
//        $query->whereIn('b.name',  Config::get('constant.roles_patch_ep') );
        return $query->count();
    }
    
    public function isAllowHelpdesk() {
        
        $userName = Config::get('constant.users_helpdesk');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->whereIn('username',$userName)
                        ->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isPomsUser() {
        $userName = Config::get('constant.users_poms');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->whereIn('username',$userName)
                        ->orderBy('last_login', 'desc');
        return $query->count();
    }
    
    public function isGroupMiddleware() {

        $groups = Config::get('constant.roles_middleware');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public function isGroupServerAdmin() {
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.roles_server_admin'));
        return $query->count();
    }
    

    public function isNetworkApprover() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.roles_network_approver'));
        return $query->count();
    }

    public function isApproverTesting() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.roles_approver_testing'));
        return $query->count();
    }

    public function isSMTesting() {

        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                ->where('user_id', Auth::user()->id)
                ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                ->whereIn('username', Config::get('constant.roles_SM_testing'));
        return $query->count();
    }

    public function isGroupNetworkAdmin() {

        $groups = Config::get('constant.roles_network_admin');
        $query = DB::connection('mysql_ep_support')->table('ep_login_history')
                        ->where('user_id', Auth::user()->id)
                        ->whereDate('date',Carbon::now()->format('Y-m-d')) 
                        ->where(function ($query) use($groups) {
                            for ($i = 0; $i < count($groups); $i++) {
                                $query->orwhere('groups', 'like', '%' . $groups[$i] . '%');
                            }
                        })->orderBy('last_login', 'desc');
        return $query->count();
    }

    public static function getTokenEaduan($userName){

        $privateKey = 'CRM-eP-NextGen-2018';
        $time=strtotime("now");
        $b64Time = base64_encode($time);
        $timeCode = '5555';

        $pKey = base64_encode(hash("sha256", $privateKey, true));
        $b64PKey = base64_encode($pKey);
        $b64LoginID = base64_encode($userName);
        $b64PKeyLoginID = base64_encode(base64_encode(base64_encode($b64LoginID.$b64PKey)));
        $token = base64_encode(base64_encode($b64PKeyLoginID. $time));
        
        $timeCodeKey = base64_encode(hash("sha256", $timeCode, true));
        $b64TimeCode = base64_encode($timeCodeKey);
        $tid   = base64_encode(base64_encode($b64TimeCode.$b64Time));

        return "https://crm.eperolehan.gov.my/portal/?token=$token&tid=$tid";
    }
}
