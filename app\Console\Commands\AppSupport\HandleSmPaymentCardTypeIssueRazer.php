<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;
use App\Migrate\AppSupport\SmPaymentCardTypeSync;

class HandleSmPaymentCardTypeIssueRazer extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSmPaymentCardTypeIssueRazer';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To fix issue on channel_name for CREDIT / DEBIT cause of razer get the different info card with not same with maybank';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(__METHOD__. ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            MigrateUtils::logDump(__METHOD__. ' [HandleSmPaymentCardTypeIssueRazer] start ');
            SmPaymentCardTypeSync::syncCardTypeBin();
            SmPaymentCardTypeSync::syncUpdateMBBCardType();
            sleep(10);

            // For production : only fix issue on date today. Please do not specify any past day. This fixing impact on Payment Receipt and Integrate into IGFMAS
            $dateFind = Carbon::now()->format('Y-m-d');
            SmPaymentCardTypeSync::syncPatchingFixCardType($dateFind);
            SmPaymentCardTypeSync::checkAndPatchingCardTypePrepaid($dateFind);
            MigrateUtils::logDump(__METHOD__. ' [HandleSmPaymentCardTypeIssueRazer] Completed');
            
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(__METHOD__. ' error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(__METHOD__.' error happen!! ' . $exc->getTraceAsString());
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump(__METHOD__. ' error happen!! ' . $e->getMessage());
        }
    }
    
}
