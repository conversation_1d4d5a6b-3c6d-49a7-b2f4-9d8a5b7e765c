@extends('layouts.guest-dash')
@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-5 log-header-title">
            <span>Search Content OSB File <br /></span>
            <small>Valid for AP516 : Surat Setuju Terima (SST) or AP511 :  Payment Information
            File AP516, start with LA*******. 
            File AP511, find by Invoice No.
            </small>
        </div>
        <div class="col-md-6 log-header-menu">
            <a href="{{url('find/osb/log')}}"><span class="{{ Request::is('find/osb/log') ? 'active' : '' }}">Log</span></a> |
            <a href="{{url('find/osb/detail/log')}}"><span class="{{ Request::is('find/osb/detail/log') ? 'active' : '' }}">Log Detail</span></a> |
            <a href="{{url('find/osb/detail-rquid/log')}}"><span class="{{ Request::is('find/osb/detail-rquid/log') ? 'active' : '' }}">Log RQ-UID</span></a> | 
            <a href="{{url('find/osb/batch/file')}}"><span class="{{ Request::is('find/osb/batch/file') ? 'active' : '' }}">Batch File Log</span></a> | 
            <a href="{{url('osb/file/content/search')}}"><span class="{{ Request::is('osb/file/content/search') ? 'active' : '' }}">Find Content File</span></a> |
            <a href="{{url('find/osb/error')}}"><span class="{{ Request::is('find/osb/error') ? 'active' : '' }}">Error Log</span></a> | 
            <a href="{{url('find/1gfmas/ws')}}"><span class="{{ Request::is('find/1gfmas/ws') ? 'active' : '' }}">Log (IGFMAS)</span></a> | 
            <a href="{{url('find/phis/ws')}}"><span class="{{ Request::is('find/phis/ws') ? 'active' : '' }}">Log (PHIS)</span></a> 
        </div>
    </div>
</div>
@endsection

@section('content')
<div class="block">
    <form class="form-horizontal" id="carianform" action="{{url('/osb/file/content/search')}}/?cari" method="get" >
        <div class="row">
            <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cari">Carian / Search </label>
                        <div class="col-md-6">
                            <input type="text" id="cari" name="cari" class="form-control" value="{{$carian}}"  onfocus="this.select();" placeholder="Klik carian di sini ... ">
                        </div>
                    </div>
            </div>
        </div>
    </form>
</div>


@if($listdata == null || count($listdata) == 0)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title" >
            <h1><i class="fa fa-bars"></i> </h1>
        </div>
        <div class="row">
          <div class="col-sm-6">
              
              <p>Tiada rekod!</p>
          </div>
        </div>
    </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Log Fail OSB</strong></h1>
        </div>
        
        @if($listdata && count($listdata) > 0 )
        @foreach($listdata as $row)
        <div class="block collapse panel-payload" id="{{$row->batch_file_id}}">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$row->file_name}} | ({{$row->service_code}})</h2>
                <div class="block-options pull-right">
                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                        onclick="$(this).parentsUntil('div.panel-payload').parent().addClass('out'); $(this).parentsUntil('div.panel-payload').parent().removeClass('in');" >Close</span>
                </div>
            </div>
            <pre class="line-numbers">
                <code class="language-markup">{{$row->file_data}}</code>
            </pre>
        </div>
        @endforeach
        @endif
        <ul class="text-info">
            <li><span class="text-info bolder">AP516 Surat Setuju Terima (SST)</span> : GFM-350</li>
            <li><span class="text-info bolder">AP511 Payment Information</span> : GFM-350  </li>
            <li><span class="text-primary bolder">Klik pada BATCH FILE ID untuk lihat kandungan fail.</span></li>
        </ul>
        <div class="table-responsive">
            <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">BATCH FILE ID</th>
                        <th class="text-center">FILE NAME</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center"><a href="javascript:void(0)" data-toggle="collapse" data-target="#{{ $data->batch_file_id }}" >
                            {{$data->batch_file_id }}</a></td>
                        <td class="text-center">
                            <a href="{{url('/find/osb/batch/file/')}}/{{ $data->file_name }}" target="_blank"  >
                                {{ $data->file_name }}</a></td>
                        <td class="text-center">{{ $data->service_code }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    


</div>
@endif
@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>


@endsection
