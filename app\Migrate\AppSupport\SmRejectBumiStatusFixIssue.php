<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmRejectBumiStatusFixIssue {

    use SupplierService;

    public static function fixIssueListRejectBumiStatus(){
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' APPL NO: '.$applNo);
        $dtStartTime = Carbon::now();

        $thisCls = new SmRejectBumiStatusFixIssue();
        $listObjAppl = $thisCls->listBumiStatusCancellation();
        foreach($listObjAppl as $obj){
            $thisCls->fixIssueRejectBumiStatus($obj->appl_no);
        }
        //$thisCls->fixIssueRejectBumiStatus("***********-0046");

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    public static function fixIssueRejectBumiStatusByApplNo($applNo){
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' APPL NO: '.$applNo);
        $dtStartTime = Carbon::now();
        $thisCls = new SmRejectBumiStatusFixIssue();
        $thisCls->fixIssueRejectBumiStatus($applNo);

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    /**
     * Search APPL NO (Rejected) to fix issue. Update certain table cause of bug data.
     */
    public function fixIssueRejectBumiStatus($applNo) {

        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__. ' APPL NO: '.$applNo);
        $dtStartTime = Carbon::now();

        // STEP TO FIND APPL ID (Registered APPL ID  & REJECTED APPL ID)
        $objLatestAppl = $this->getLatestRegisteredAppl($applNo);
        if($objLatestAppl){
            $latestApplId = $objLatestAppl->latest_appl_id;
            $rejectedApplId = $objLatestAppl->rejected_appl_id;

            // 1) Fix Issue to update SM_APPL latest and rejected , patch data is_bumi and reg_status_id
            MigrateUtils::logDump('1) Fix Issue to update SM_APPL latest and rejected , patch data is_bumi and reg_status_id');
            $dataApplUpdateBumiRegStatus = ['is_bumi'=> 0,'reg_status_id' => 3];
            $this->updateSmAppl($latestApplId,$dataApplUpdateBumiRegStatus);
            $this->updateSmAppl($rejectedApplId,$dataApplUpdateBumiRegStatus);

            // STEP TO FIND (APPLICATION IN PROGRESS)
            $objInProgressAppl = $this->getActiveApplInProgress($latestApplId);
            if($objInProgressAppl){
                // 2) Fix Issue to update SM_APPL In Progress Application , patch data is_bumi
                MigrateUtils::logDump('2) Fix Issue to update SM_APPL In Progress Application , patch data is_bumi');
                $patchType = 'INPROGRESS';
                $dataApplUpdateBumi = ['is_bumi'=> 0];
                $this->updateSmAppl($objInProgressAppl->active_appl_in_progress,$dataApplUpdateBumi,$patchType);
            }else{
                MigrateUtils::logDump('2) Fix Issue to update SM_APPL In Progress Application , patch data is_bumi');
                MigrateUtils::logDump(' > No record found!');
            }

            // 3) Fix Issue to update SM_MOF_CERT by Latest APPL ID , patch data record_status to set ACTIVE
            MigrateUtils::logDump('3) Fix Issue to update SM_MOF_CERT by Latest APPL ID , patch data record_status to set ACTIVE');
            $mofCertTypeActive = 'FIX_ISSUE_ACTIVE';
            $dataMofCertUpdateActive = ['record_status'=> 1];
            $this->updateSmMofCert($latestApplId,$dataMofCertUpdateActive,$mofCertTypeActive);

            // 4) Fix Issue to update SM_MOF_CERT by Latest APPL ID , patch data record_status to set INACTIVE
            MigrateUtils::logDump('4) Fix Issue to update SM_MOF_CERT by Latest APPL ID , patch data record_status to set INACTIVE');
            $mofCertTypeInActive = 'FIX_ISSUE_INACTIVE';
            $dataMofCertUpdateInActive = ['record_status'=> 0];
            $this->updateSmMofCert($latestApplId,$dataMofCertUpdateInActive,$mofCertTypeInActive);
            // 5) Fix Issue to update SM_MOF_CERT by Rejected APPL ID , patch data record_status to set INACTIVE
            $mofCertTypeRejectInActive = 'FIX_ISSUE_REJECT_INACTIVE';
            $dataMofCertUpdateRejectInActive = ['record_status'=> 0];
            MigrateUtils::logDump('5) Fix Issue to update SM_MOF_CERT by Rejected APPL ID , patch data record_status to set INACTIVE');
            $this->updateSmMofCert($rejectedApplId,$dataMofCertUpdateRejectInActive,$mofCertTypeRejectInActive);
        }

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }
    
    
    protected function getLatestRegisteredAppl($applNo){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
                    ss.latest_appl_id,
                    sa.appl_id as rejected_appl_id
                FROM
                    sm_supplier ss,
                    sm_appl sa
                WHERE
                    ss.supplier_id = sa.supplier_id
                    AND sa.appl_no  = ? 
                           ", array($applNo));
        if($dataList != null && count($dataList) > 0){
            return $dataList[0];
        }
        return null;
    }

    protected function getActiveApplInProgress($applId){
        $dataList = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT distinct
                    saa.appl_id as active_appl_in_progress
                 FROM
                    sm_appl sa,
                    sm_appl saa
                 WHERE
                    sa.appl_id  = ? 
                    AND sa.supplier_id = saa.supplier_id
                    AND saa.is_active_appl = 1
                           ", array($applId));
        if($dataList != null && count($dataList) > 0){
            return $dataList[0];
        }
        return null;
    }

    /**
     * Field will update can be is_bumi,reg_status_id
     */
    protected function updateSmAppl($applId,$dataUpdate,$patchType = null){
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_APPL')
                ->where('appl_id', $applId);
        if($patchType && $patchType == 'INPROGRESS'){
            $query->whereNull('reg_status_id');
        }

        
        $queryCheck = $query; 
        $queryCheck->select('appl_id','appl_no','status_id','is_bumi','reg_status_id');
        MigrateUtils::logDump(' > Data before '.json_encode($queryCheck->get()));

        if($queryCheck->first()){
            $query->update($dataUpdate); 
            MigrateUtils::logDump(' > Data update '.json_encode($dataUpdate));
            MigrateUtils::logDump(' Updated Successful!');
        }else{
            MigrateUtils::logDump(' Not Updated');
        }
        
    }

    /**
     * Field will update can be record_status
     */
    protected function updateSmMofCert($applId,$dataUpdate,$patchType = null){
        $query =  DB::connection('oracle_nextgen_fullgrant')
                ->table('SM_MOF_CERT')
                ->where('appl_id', $applId);
        if($patchType && $patchType == 'FIX_ISSUE_ACTIVE'){
            $query->where('is_bumi_cert',0);
            $query->where('record_status','<>',1);
        }
        if($patchType && $patchType == 'FIX_ISSUE_INACTIVE'){
            $query->where('is_bumi_cert',1);
            $query->whereNotIn('record_status',[0, 4, 9]);
        }
        if($patchType && $patchType == 'FIX_ISSUE_REJECT_INACTIVE'){
            $query->whereNotIn('record_status',[0, 4, 9]);
        }

        $queryCheck = $query; 
        $queryCheck->select('mof_cert_id','appl_id','is_bumi_cert','record_status');
        MigrateUtils::logDump(' > Before update '.json_encode($queryCheck->get()));
        
        if($queryCheck->first()){
            $query->update($dataUpdate); 
            MigrateUtils::logDump(' > Data update '.json_encode($dataUpdate));
            MigrateUtils::logDump(' Updated Successful!');
        }else{
            MigrateUtils::logDump(' Not Updated');
        }
        
        
    }
    
    
}
