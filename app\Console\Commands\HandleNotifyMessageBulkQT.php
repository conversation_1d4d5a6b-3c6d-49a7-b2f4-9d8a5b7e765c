<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config;
use App\Migrate\FixEpNotificationMessageBulkQT;

/**
 * Program will set IS_SENT =1 for any Batch QT more than 30k users. Then program will select one record to be run to be set IS_SENT = 0
 * created on 8/10/2019 by  Shamsul. 
 */
class HandleNotifyMessageBulkQT extends Command {
    

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix-notify-message-bulk-qt';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Program will set IS_SENT = 1 for any Batch QT more than 30k users. Then program will select one record to be run to be set IS_SENT = 0 ';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        
        try {
            
            //We will update record when minutes last number 4 and 8 only. This to avoid cause invalidate data when eP Scheduler trigger time every 10 minutes
            $time = Carbon::now()->minute;
            $lenTime = strlen($time);
            $isProceedUpt = false;
            if($lenTime == 1){
                $num = intval(substr($time,0,1));
                if($num === 4 || $num === 8){
                    $isProceedUpt = true;
                }
            }else{
                $num = intval(substr($time,1,2));
                if($num === 4 || $num === 8){
                    $isProceedUpt = true;
                } 
            }

            $isProceedUpt = true;
            if($isProceedUpt == true){
                FixEpNotificationMessageBulkQT::run();
            }
            $logsdata = self::class . '  , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            MigrateUtils::logDump($logsdata);
        } catch (\Exception $exc) {
            
            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }

    
    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    


}
