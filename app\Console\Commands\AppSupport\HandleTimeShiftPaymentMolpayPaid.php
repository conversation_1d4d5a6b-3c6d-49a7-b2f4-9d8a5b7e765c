<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use App\Services\Traits\ProdSupport\PsDefectEpService;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;
use App\Services\Traits\PaymentReceiptService;

class HandleTimeShiftPaymentMolpayPaid extends Command {

    use PaymentReceiptService;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleTimeShiftPaymentMolpayPaid';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Issue cause of payment date in PY_PAYMENT (Receipt Generated) is not same with date paid transaction from MOLPAY.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        self::updatePaymentDateDifferent();
        self::updatePaymentReceiptSuccessWithStatusFailed();
        MigrateUtils::logDump($clsInfo.'Done! Completed');
    }

    protected function updatePaymentDateDifferent(){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $listdata = $this->listPaymentDateDiffWithTrasanctionPaidDate();

        if($listdata != null) {
            MigrateUtils::logDump($clsInfo.'Total receipt no found different with transaction paid date : ' . count($listdata));
            try {
                foreach ($listdata as $obj) {
                    $dataObj = json_encode($obj);
                    MigrateUtils::logDump($dataObj);
                    $datePaid = $obj->transaction_date;
                    $paymentDate = $obj->payment_date;
                    $paymentId = $obj->payment_id;

                    $query =  DB::connection('oracle_nextgen_fullgrant')
                        ->table('PY_PAYMENT')
                        ->where('PAYMENT_ID', $paymentId)
                        ->update(['payment_date'=>$datePaid]);

                    $actionName = 'PatchDataPayment';
                    $actionType = 'Script';
                    
                    $dataParam = collect();
                    $dataParam->put("action","Update records in table PY_PAYMENT");
                    $dataParam->put("table","PY_PAYMENT");
                    $dataParam->put("payment_id",$paymentId);
                    $dataParam->put("criteria","Found this receipt no, transaction date paid is different with payment date");
                    $dataParam->put("remark","To update PY_PAYMENT.payment_date same with transaction date paid");
                    $dataParam->put("transaction_paid_date",$datePaid);
                    $dataParam->put("payment_date",$paymentDate);
                    $dataParam->put("receipt_no",$obj->receipt_no);
                    //dump($dataParam);
                    $dataLog = collect();
                    $dataLog->put("remark","To update PY_PAYMENT.payment_date same with transaction date paid");
                    $dataLog->put("data",$dataObj);
                    
                    EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");
                    MigrateUtils::logDump($clsInfo.'Completed');   
                }
            } catch (\Exception $exc) {
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . json_encode($exc->getTrace()));
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
            }
        }
        
    }

    protected function updatePaymentReceiptSuccessWithStatusFailed(){
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..');
        $listdata = $this->listPaymentReceiptWithStatusFailed();
        if($listdata != null) {
            MigrateUtils::logDump($clsInfo.'Total receipt no found with status failed : ' . count($listdata));
            try {
                foreach ($listdata as $obj) {
                    $dataObj = json_encode($obj);
                    MigrateUtils::logDump($dataObj);
                    $paymentId = $obj->payment_id;
                    $paymentDate = $obj->payment_date;
                    $statusIdSuccess = 20405;
                    $query =  DB::connection('oracle_nextgen_fullgrant')
                        ->table('PY_PAYMENT')
                        ->where('PAYMENT_ID', $paymentId)
                        ->update(['status_id'=>$statusIdSuccess ]);
                    
                    $actionName = 'PatchDataPayment';
                    $actionType = 'Script';
                    
                    $dataParam = collect();
                    $dataParam->put("action","Update records in table PY_PAYMENT");
                    $dataParam->put("table","PY_PAYMENT");
                    $dataParam->put("payment_id",$paymentId);
                    $dataParam->put("criteria","This payment is succesfully paid but the status shown as failed. ");
                    $dataParam->put("remark","To update PY_PAYMENT.status_id as 20405");
                    $dataParam->put("status_id_before",$obj->status_id);
                    $dataParam->put("status_id_after",$statusIdSuccess );
                    $dataParam->put("payment_date",$paymentDate);
                    $dataParam->put("receipt_no",$obj->receipt_no);
                    //dump($dataParam);
                    $dataLog = collect();
                    $dataLog->put("remark","To update PY_PAYMENT.payment_date same with transaction date paid");
                    $dataLog->put("data",$dataObj);
                    
                    EpSupportActionLog::saveActionLog($actionName, $actionType, $dataLog, $dataParam, "Completed");
                    MigrateUtils::logDump($clsInfo.'Completed');   
                }
            } catch (\Exception $exc) {
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . json_encode($exc->getTrace()));
                MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
            }

        }
    }


}
