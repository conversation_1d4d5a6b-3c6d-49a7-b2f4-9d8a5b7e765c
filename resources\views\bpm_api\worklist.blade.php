@extends('layouts.guest-dash')

<style> 
    #table-scroll {
        height:50%;
        width:100%;
        overflow:auto;
    }
    .nested {
        display: none;
    }
    .active {
        display: block;
    }
    .hover { background-color: yellow; }
</style>

@section('content')

<div class="content-header">

    <ul class="nav-horizontal text-center">
        <li>
            <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
        </li>
        <li>
            <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
        </li>
        <li>
            <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
        </li>
        <li class="active">
            <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
        </li>
        <li>
            <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
        </li>
        <li>
            <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
        </li>
        <li >
            <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
        </li>
    </ul>

</div>
<div class="row">
    <div class="col-lg-12">
        <div class="col-lg-6">
            <div class="block">
                <div class="row">

                    @if($status_api != null)
                    <h5 class="alert alert-danger">{{$status_api}}</h5>
                    @endif

                    <h5 class="alert alert-success" id="success" style="display:none;"></h5>
                    <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
                    <form id="form-search-task" action="{{url("/bpm/worklist/find")}}" method="post" class="form-horizontal form-bordered">
                        {{ csrf_field() }}
                        <div class="form-group form-inline">
                            <label class="control-label" for="user_id" style="color:#003d7a;padding-left: 40px">User ID</label>
                            <div class="input-group" style="width:72%;padding-left: 20px">
                                <input id="user_id" name="user_id" required class="form-control" value="{{ $userId }}" placeholder=" - user login id -" type="text" >
                            </div>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-user-plus"></i> Login</button>       
                        </div>  

                        <fieldset id="fieldGroup" disabled="true">
                            <div class="input-group pull-left" style="padding-left: 35px;">
                                <button type="button" id="initiate" name="initiate" class="btn btn-sm btn-default"><i class="fa fa-plus" style="color: blue"></i> INITIATE </button>
                            </div>
                            <div class="input-group pull-right" style="padding-left: 15px;padding-right: 40px">
                                <button type="button" id="refresh" name="refresh" class="btn btn-sm btn-default"><i class="fa fa-refresh" style="color: blue"></i> Refresh </button>
                            </div>
                            <div class="input-group pull-right" style="padding-left: 15px; width: 120px">
                                <select id="state" name="state" class="form-control">
                                    @foreach($stateData as  $key => $value)
                                    <option value="{{$value}}" @if($value == old('state') ) selected @endif>{{$value}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="input-group pull-right" style="padding-left: 20px; width: 120px">
                                <select id="assignment" name="assignment" class="form-control">
                                    @foreach($assignmentData as  $key => $value)
                                    <option value="{{$value}}" @if($value == old('assignment') ) selected @endif>{{$value}}</option>
                                    @endforeach
                                </select>
                            </div>
                        </fieldset>
                        <br/>
                    </form>
                </div>
                <div class="table-responsive"> 
                    <center>
                        <div id="spinner" style="display:none;">
                            <i class="fa fa-spinner fa-2x fa-spin"></i>
                        </div>
                    </center>
                    <table class="table table-borderless table-striped" id="worklist_table"></table>
                    @if($firstData !== null)
                    <div class="table-responsive">  
                        <table class="table table-borderless " id="first_table" style="display:block">
                            <thead style="background-color:#f2f2f2;">
                                <tr>    
                                    <th class="text-center" style="width: 100px">Instance</th>
                                    <th class="text-center" style="width: 220px">Title</th>
                                    <th class="text-center" style="width: 220px">Activity</th>
                                    <th class="text-center" style="width: 100px">Created</th>
                                </tr>
                            </thead>
                            <tbody style="font-size:80%;">
                                @foreach($firstData["worklistItem"] as $data)
                                <?php
                                $date = date("d/m/Y h:i:s A", substr($data["createDate"], 0, 10));
                                $module = explode("*", $data['compositeDN']);
                                ?>
                                <tr>
                                    <td><a href="{{url('/bpm/instance/find')}}/?composite_module={{ $module[0] }}&composite_instance_id={{$data['instanceId']}}" target="_blank" > {{ $data['instanceId'] }}</a></td>
                                    <td data-id="{{$data["taskId"]}}" class="worklistProcess" data-target="worklist_flow"><a style="color:black;">{{ $data["title"] }} </a></td>
                                    <td>{{ $data["taskName"] }}</td>
                                    <td>{{ $date }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                            <tfoot>
                                <tr>
                                    <td colspan="4" style="font-size:90%;text-align: right;"><i>Page {{ $offset + 1 }}</i></td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                    <input type="hidden" id="offset" name="offset" value="{{ $offset }}"/>
                    <input type="hidden" id="limit" name="limit" value="{{ $limit }}"/>
                    <center>
                        <button id="previous_page" name="previous_page" disabled="true" class="btn btn-sm btn-primary fa fa-arrow-left"> Previous </button>
                        <button id="next_page" name="next_page" class="btn btn-sm btn-primary fa fa-arrow-right"> Next </button>
                    </center>
                    @endif
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-6">
                <h5 class="alert alert-danger" id="statusApi" style="display:none"></h5>
                <div id="worklist_flow" style="display:none;">
                    <div class="block">
                        <button id="claim_task" disabled="true" name="claim_task" class="btn btn-sm btn-primary fa fa-user"> Claim </button>
                        <button id="release_task" disabled="true" name="release_task" class="btn btn-sm btn-primary fa fa-users"> Release </button>
                        <button id = "delegate_task" name="delegate_task" class="btn btn-sm btn-primary fa fa-hand-grab-o pull-right"> Delegate </button>
                        <table id="worklist_task_table"  class="table table-borderless" style="table-layout: fixed; width: 100%;background-color: #f2f2f2">
                            <tbody style="font-size:80%;">
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Task ID : </strong></td>
                                    <td colspan="2" id="task_id"></td>
                                    <td><button class="btn btn-sm btn-primary fa fa-history pull-right" id="history_task"></button></td>
                                </tr>
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Assignees : </strong></td>
                                    <td style="word-wrap: break-word;" colspan="2" id="assignee"></td>
                                </tr>
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Acquired : </strong></td>
                                    <td id="acquired"></td>
                                    <td class="text-right"><strong style="color:#003d7a;">Composite : </strong></td>
                                    <td id="composite"></td>
                                </tr>
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Status : </strong></td>
                                    <td id="status"></td>
                                    <td class="text-right"><strong style="color:#003d7a;">Process : </strong></td>
                                    <td id="process"></td>
                                </tr>
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Outcome : </strong></td>
                                    <td id="outcome"></td>
                                    <td class="text-right"><strong style="color:#003d7a;">Activity : </strong></td>
                                    <td id="activity"></td>
                                </tr>
                                <tr>
                                    <td class="text-right"><strong style="color:#003d7a;">Created : </strong></td>
                                    <td id="created"></td>
                                    <td class="text-right"><strong style="color:#003d7a;">Expiry : </strong></td>
                                    <td id="expiry"></td>
                                </tr>
                            </tbody>
                        </table> 

                        <strong><p style="color:#003d7a;font-size:120%;">&nbsp;&nbsp;Payload </p></strong>
                        <textarea id="payload" style="width: 100%;" rows="48" > </textarea> <br/><br/>
                        <br/>
                        <fieldset>
                            <label class="col-md-2 control-label" for="string_1" style="color:#003d7a;">String 1 : </label>
                            <div class="input-group pull-left">
                                <input type="text" id="string_1" name="string_1" class="form-control">
                            </div>  
                            <label class="col-md-2 control-label" for="number_1" style="color:#003d7a;">Number 1 : </label>
                            <div class="input-group">
                                <input type="text" id="number_1" name="number_1" class="form-control">
                            </div>  
                            <label class="col-md-2 control-label" for="string_2" style="color:#003d7a;">String 2 : </label>
                            <div class="input-group pull-left">
                                <input type="text" id="string_2" name="string_2" class="form-control">
                            </div>  
                            <label class="col-md-2 control-label" for="number_2" style="color:#003d7a;">Number 2 : </label>
                            <div class="input-group">
                                <input type="text" id="number_2" name="number_2" class="form-control">
                            </div>
                        </fieldset>
                        <br/>
                        <div class="form-group">
                            <div class="input-group form-inline pull-right">
                                <select id="action_task" name="action_task" class="form-control" style="background-color: #f7f7f7;width:200px;"></select>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <button id="execute_task" name="execute_task" class="btn btn-sm btn-primary fa fa-fire"> Execute</button>
                            </div> 
                            <input type="hidden" id="total_assignee" name="total_assignee">
                        </div><br/><br/><br/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!--modal initiable task-->
<div id="modal_initiable" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h5> Choose a flow to start </h5>
                <h5 id="history_task_id"></h5>
            </div>
            <div class="modal-body">
                <div id="table-scroll">
                    <div id="initiateList"></div>
                </div>
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Close</button>
            </div>
        </div>
    </div>
</div>

<!--modal task history-->
<div id="modal_history" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h5> Log history for task </h5>
                <h5 id="history_task_id"></h5>
            </div>
            <div class="modal-body">
                <div class="table-responsive">
                    <table class="table table-condensed table-borderless" id="table_history"></table>
                </div> 
            </div>
            <br/><br/>
            <div class="modal-footer">
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Close</button>
            </div>
        </div>
    </div>
</div>

<!--modal delegate task -->
<div id="modal_delegate_user" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-left">
                <h4> Enter user to delegate task to </h4>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label class="col-md-2 control-label" for="delegate_user">User : <span class="text-danger">*</span></label>
                    <div class="col-md-10">
                        <input type="text" id="delegate_user" name="delegate_user" class="form-control">
                    </div>
                </div>
            </div>
            <br/><br/>
            <div class="modal-footer">
                <button type="button" id="delegate_user_btn" class="btn btn-sm btn-info">Ok</button>
                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Cancel</button>
            </div>
        </div>
    </div>
</div>
<div id="modal_spinner" class="modal fade">
    <div class="modal-dialog modal-sm" style="width: 10%; transform: translate(0, -50%); top: 50%; margin: 0 auto">
        <div class="modal-content">
            <div class="modal-body">
                <center>
                <i class="fa fa-spinner fa-3x fa-spin" style="color: red"></i><br/><br/>Loading..
            </center>
            </div>    
        </div>
    </div>
</div>

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>$('#page-container').removeAttr('class');</script>
<script>$(function () {TablesDatatables.init();});</script>
<script>$(function () {ModalListActionLogDatatable.init();});</script>

<script src="/js/pages/tablesDatatables.js"></script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script src="/js/bpm/worklist/populatetable.js"></script>
<script src="/js/bpm/worklist/worklisttable.js"></script>

<script>
    $(document).ready(function () {

        var userId = $("input[name=user_id]").val();

        if (userId == '') {
            $("#fieldGroup").prop('disabled', true);
        } else {
            $("#fieldGroup").prop('disabled', false);
        }

        $('#assignment').on('change', function () {

            var assignment = $(this).val();
            var csrf = $("input[name=_token]").val();
            var userId = $("input[name=user_id]").val();
            var state = $('#state option:selected').text();
            var offset = $("input[name=offset]").val();
            var limit = $("input[name=limit]").val();

            document.getElementById("worklist_flow").style.display = "none";
            populateTable(csrf, userId, assignment, state, offset, limit);

        });


        $('#state').on('change', function () {

            var state = $(this).val();
            var csrf = $("input[name=_token]").val();
            var userId = $("input[name=user_id]").val();
            var assignment = $('#assignment option:selected').text();
            var offset = $("input[name=offset]").val();
            var limit = $("input[name=limit]").val();

            document.getElementById("worklist_flow").style.display = "none";
            populateTable(csrf, userId, assignment, state, offset, limit);
        });

        $('#refresh').on('click', function () {

            var state = $('#state option:selected').text();
            var csrf = $("input[name=_token]").val();
            var userId = $("input[name=user_id]").val();
            var assignment = $('#assignment option:selected').text();
            var offset = $("input[name=offset]").val();
            var limit = $("input[name=limit]").val();

            $('#statusApi').hide();
            document.getElementById("worklist_flow").style.display = "none";
            populateTable(csrf, userId, assignment, state, offset, limit);
        });

        $('#history_task').on('click', function () {

            var taskId = $("#worklist_task_table #task_id").text();
            var csrf = $("input[name=_token]").val();

            $('#modal_spinner').modal('show');
            $.ajax({
                type: "POST",
                url: "/bpm/worklist/taskid/" + taskId,
                data: {"_token": csrf, "taskId": taskId},
                error: function (xhr, status, error) {
                    $('#failed').show();
                    document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
                }
            }).done(function (resp) {
                $('#modal_spinner').modal('hide');
                $("#history_task_id").html(resp.listdata["taskId"]);
                if (resp.statusHistory == 'success') {
                    $('#statusApi').hide();
                    $('#modal_history').modal('show');
                    var html = '<tbody style="font-size:80%;">';

                    for (const [history, value] of Object.entries(resp.history)) {
                        var d = new Date(value["updateDate"]);
                        let formatted_date = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear() + " " + d.getHours() + ":" + d.getMinutes() + ":" + d.getSeconds();
                        var outcome = '';
                        if (value["outcome"] !== null) {
                            outcome = " : " + value["outcome"];
                        }
                        html += "<tr><td>" + formatted_date + "</td>";
                        html += "<td><ul><li style='color:blue'>" + value["state"] + "<span style='color:black'>" + outcome + "</span></li><li>" + value["taskId"] + "</li><li>";

                        var assignee = '';
                        var arrLength = value["assignees"].length;
                        var i = 0;
                        for (const [val, data] of Object.entries(value["assignees"])) {
                            i++;
                            if (arrLength === 1) {
                                assignee += data;
                            } else if (i == arrLength) {
                                assignee += data;
                            } else {
                                assignee += data + ",";
                            }
                        }
                        html += assignee + "</li></ul></td>";
                    }
                    html += "</tr><tbody>";
                    document.getElementById("table_history").innerHTML = html;
                } else {
                    $('#modal_history').modal('hide');
                    $('#statusApi').show();
                    $("#statusApi").html(resp.statusHistory);
                }
            });
        });

        $('#delegate_task').on('click', function () {
            $('#modal_delegate_user').modal('show');

            $('#delegate_user_btn').on('click', function () {
                var taskId = $("#worklist_task_table #task_id").text();
                var userId = $("input[name=delegate_user]").val();
                var assignee = $("#worklist_task_table #assignee").text();
                var csrf = $("input[name=_token]").val();

                $('#modal_spinner').modal('show');
                $.ajax({
                    type: "POST",
                    url: "/bpm/worklist/delegate/taskId/" + taskId,
                    data: {"_token": csrf, "userId": userId, "taskId": taskId, "assignee": assignee, "action": "Delegate-Task"},
                    error: function (xhr, status, error) {
                        $('#failed').show();
                        document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
                    }
                }).done(function (resp) {
                    $('#modal_spinner').modal('hide');
                    if (resp.status === 'success') {
                        $('#success').show();
                        document.getElementById("success").innerHTML = resp.status;
                        location.reload();
                    } else {
                        $('#failed').show();
                        document.getElementById("failed").innerHTML = resp.statusApi;
                    }
                });
                $('#modal_delegate_user').modal('hide');
            });
        });

        $('#claim_task').on('click', function () {

            var taskId = $("#worklist_task_table #task_id").text();
            var userId = $("input[name=user_id]").val();
            var csrf = $("input[name=_token]").val();

            claimReleaseAction('Claim-Task', taskId, userId, csrf);
        });

        $('#release_task').on('click', function () {

            var taskId = $("#worklist_task_table #task_id").text();
            var userId = $("input[name=user_id]").val();
            var csrf = $("input[name=_token]").val();

            claimReleaseAction('Release-Task', taskId, userId, csrf);

        });

        $('#execute_task').on('click', function () {
            var updatedPayload = $('textarea#payload').val();
            var assignee = $("#worklist_task_table #assignee").text();
            var userId = $("input[name=user_id]").val();
            var action = 'Execute-Task';
            var taskId = $("#worklist_task_table #task_id").text();
            var string1 = $("input[name=string_1]").val();
            var string2 = $("input[name=string_2]").val();
            var number1 = $("input[name=number_1]").val();
            var number2 = $("input[name=number_2]").val();
            var csrf = $("input[name=_token]").val();

            var cdcadmin = ["Contract_Management.ProcessOwner", "Fulfilment.ProcessOwner", "Order.ProcessOwner",
                "SourcingDP.ProcessOwner", "SourcingQT.ProcessOwner", "Supplier_Management.ProcessOwner", "Profile_Management.ProcessOwner",
                "SCBusinessRule.ProcessOwner", "Codification.ProcessOwner"];

            var param = [string1, string2, number1, number2];
            var taskaction = $('#action_task option:selected').text();

            if (cdcadmin.includes(assignee)) {
                userId = 'cdcadmin';
                executeAction(action, taskId, userId, taskaction, updatedPayload, param, csrf);
            } else {
                executeAction(action, taskId, userId, taskaction, updatedPayload, param, csrf);
            }
        });

        $('#initiate').on('click', function () {
            var userId = $("input[name=user_id]").val();
            var csrf = $("input[name=_token]").val();

            $('#modal_spinner').modal('show');
            $.ajax({
                type: "POST",
                url: "/bpm/worklist/list/initiate/" + userId,
                data: {"_token": csrf},
                error: function (xhr, status, error) {
                    $('#failed').show();
                    document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
                }
            }).done(function (resp) {
                $('#modal_spinner').modal('hide');
                if (resp.status == 'success') {
                    $('#modal_initiable').modal('show');
                    var html = '<ul style="list-style-type: none;">';
                    for (const [key, value] of Object.entries(resp.initiable)) {
                        for (const [keys, vals] of Object.entries(value)) {
                            html += '<li  style="margin: 10px"><i class="caret-icon fa fa-caret-right"></i> ' + key + ' [  ' + keys + '  ]';
                            for (const [d, data] of Object.entries(vals)) {
                                html += '<ul id="myUL">';
                                html += '<li id="process_name" value="' + data["id"] + '" style="display:none;list-style-type: none;cursor: pointer; margin: 10px">' + data["id"] + '<span style="font-weight: bold;">' + data["processName"] + '</span></li>';
                                html += '</ul>';
                            }
                            html += '</li>';
                        }
                    }
                    html += '</ul>';
                    document.getElementById("initiateList").innerHTML = html;

                    var toggler = document.getElementsByClassName("fa-caret-right");
                    var i;

                    for (i = 0; i < toggler.length; i++) {
                        toggler[i].addEventListener("click", function () {
                            $(this).closest("li").find("[id='process_name']").slideToggle();
                            $(this).toggleClass('fa-caret-down fa-caret-right');
                        });
                    }

                    $("#myUL li").click(function () {
                        var processId = this.value;
                        var csrf = $("input[name=_token]").val();
                        $('#modal_initiable').modal('hide');
                        $('#modal_spinner').modal('show');

                        $.ajax({
                            type: "POST",
                            url: "/bpm/worklist/initiate/process/" + userId,
                            data: {"_token": csrf, "userId": userId, "processId": processId, "action": "Initiate-Process"},
                            error: function (xhr, status, error) {
                                $('#failed').show();
                                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
                            }
                        }).done(function (resp) {
                            $('#modal_spinner').modal('hide');
                            if (resp.status === 'success') {
                                $('#success').show();
                                document.getElementById("success").innerHTML = resp.status;
                            } else {
                                $('#failed').show();
                                document.getElementById("failed").innerHTML = resp.statusApi;
                            }
                        });

                    });
                } else {
                    $('#failed').show();
                    document.getElementById("failed").innerHTML = resp.status;
                }
//                setTimeout(location.reload.bind(location), 3000);
            });

        });
        showWorklistTask();

        $('#next_page').on('click', function () {
            var assignment = $('#assignment option:selected').text();
            var csrf = $("input[name=_token]").val();
            var userId = $("input[name=user_id]").val();
            var state = $('#state option:selected').text();
            var offset = $("input[name=offset]").val();
            var limit = $("input[name=limit]").val();
//            console.log('next page ' + offset + ' ' + limit + ' ' + assignment + ' ' + userId + ' ' + state + ' ' + csrf);

            var newOffset = +offset + 1;

            populateTable(csrf, userId, assignment, state, newOffset, limit);

            $("input[name=offset]").val(newOffset);
            $('#previous_page').attr("disabled", false);
        });

        $('#previous_page').on('click', function () {
            var assignment = $('#assignment option:selected').text();
            var csrf = $("input[name=_token]").val();
            var userId = $("input[name=user_id]").val();
            var state = $('#state option:selected').text();
            var offset = $("input[name=offset]").val();
            var limit = $("input[name=limit]").val();
//            console.log('previous page ' + offset + ' ' + limit + ' ' + assignment + ' ' + userId + ' ' + state + ' ' + csrf);

            var newOffset = +offset - 1;

            populateTable(csrf, userId, assignment, state, newOffset, limit);

            $("input[name=offset]").val(newOffset);

            if (newOffset < 1) {
                $('#previous_page').attr("disabled", true);
            } else {
                $('#previous_page').attr("disabled", false);
            }


        });
    });

    function claimReleaseAction(action, taskId, userId, csrf) {

        $('#modal_spinner').modal('show');
        $.ajax({
            type: "POST",
            url: "/bpm/worklist/action/taskId/" + taskId,
            data: {"_token": csrf, "userId": userId, "taskId": taskId, "action": action},
            error: function (xhr, status, error) {
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
            }
        }).done(function (resp) {
            $('#modal_spinner').modal('hide');
            if (resp.status === 'success') {
                $('#success').show();
                document.getElementById("success").innerHTML = resp.status;
                location.reload();
            } else {
                $('#failed').show();
                document.getElementById("failed").innerHTML = resp.statusApi;
            }
        });
    }

    //execute action for task 
    function executeAction(action, taskid, user, taskaction, updatedpayload, param, csrf) {
        $('#modal_spinner').modal('show');

        $.ajax({
            type: "POST",
            url: "/bpm/worklist/execute/taskid/" + taskid,
            data: {"_token": csrf, "action": action, "taskid": taskid, "userid": user, "taskaction": taskaction, "updatedpayload": updatedpayload, "param": param},
            error: function (xhr, status, error) {
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
            }
        }).done(function (resp) {
            $('#modal_spinner').modal('hide');
            if (resp.status === 'Success') {
                $('#success').show();
                document.getElementById("success").innerHTML = resp.status;
                location.reload();
            } else {

                $('#failed').show();
                document.getElementById("failed").innerHTML = resp.statusApi;
            }
        });
    }
</script>


@endsection