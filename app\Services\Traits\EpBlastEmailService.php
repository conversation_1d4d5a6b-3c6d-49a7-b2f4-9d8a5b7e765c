<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

trait EpBlastEmailService
{
    // find error message for today fail record
    public function getErrorMessageByDate($date)
    {
        $list = DB::connection('oracle_nextgen_rpt')->select(" 
SELECT a.CAMPAIGN_ID, 
    a.CAMPAIGN_NAME, a.is_completed,
    TRUNC(a.SCHEDULED_TIME) AS scheduled_date,ERROR_MESSAGE
FROM blast_campaign a
LEFT JOIN blast_campaign_target b ON a.campaign_id = b.campaign_id
LEFT JOIN blast_email c ON c.email_id = b.email_id
left join blast_history d on (b.target_id=d.target_id)
where error_message is not null
and to_char(SCHEDULED_TIME,'YYYY-MM-DD') = ?
GROUP BY a.CAMPAIGN_ID,a.CAMPAIGN_NAME,a.is_completed, TRUNC(a.SCHEDULED_TIME), ERROR_MESSAGE
            ", array($date));
        if (count($list) > 0) {
            return $list;
        }
        return null;
    }

    public function getListCampaignCompleted()
    {
        $list = DB::connection('oracle_nextgen_rpt')->select(" 
select  c.SCHEDULED_TIME AS schedule_datetime, c.CAMPAIGN_NAME,c.is_completed,
count(*) AS total_email
FROM blast_campaign c,
blast_campaign_target ct
where  
c.CAMPAIGN_ID = ct.CAMPAIGN_ID
AND c.is_completed <> 0 
GROUP BY c.SCHEDULED_TIME, c.CAMPAIGN_NAME,c.is_completed
            ", array());
        if (count($list) > 0) {
            return $list;
        }
        return null;
    }

    public function getListCampaignPending()
    {
        $list = DB::connection('oracle_nextgen_rpt')->select(" 
select  c.SCHEDULED_TIME AS schedule_datetime, c.CAMPAIGN_NAME,
count(*) AS total_email
FROM blast_campaign c,
blast_campaign_target ct
where  
c.CAMPAIGN_ID = ct.CAMPAIGN_ID
AND c.is_completed = 0 
GROUP BY c.SCHEDULED_TIME, c.CAMPAIGN_NAME
            ", array());
        if (count($list) > 0) {
            return $list;
        }
        return null;
    }
}
