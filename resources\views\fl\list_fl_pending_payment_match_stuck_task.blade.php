@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>FL (Pending Payment Match ) Stuck Task <br>
                <small>FL Stuck Task Pending Payment Match List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>FL (Pending Payment Match) Stuck Task List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>FL (Pending Payment Match) Stuck Task List </strong>
                        <small></small>
                    </h1>
                </div>
                <p class="text-danger bolder">
                    <h5>&nbsp;</h5> 
                    &nbsp;
                    Tracking diary show status 'Pending Payment Match', but current workflow is 'Pending Invoice'.<br/>&nbsp;
                    Workflow should be 'Pending Payment Match'.<br/>&nbsp;
                    Please refire all tasks..
                </p>
                <a href="{{url('/crm/guideline/stuck_gfmas/PENDING_INVOICE-STL_GL15082019.docx')}}"  class="hide" style="color: #3498db; text-decoration: underline;">Guideline Refire Task Pending Payment Match </a>
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC ID</th>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">DOC_NO</th>
                            <th class="text-center">FULFILMENT ORDER ID</th>
                            <th class="text-center">CREATED_DATE</th>
                            <th class="text-center">BPM INSTANCE ID</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">BPM FLOW ID</th>

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->doc_id }}</td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->doc_no  }}" >{{ $data->doc_no }}</a></td>
                                <td class="text-center">{{ $data->fulfilment_order_id }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center"><a href="{{url('/bpm/instance/find')}}/?composite_instance_id={{$data->instance_bpm}}" target="_blank" > {{ $data->instance_bpm }}</a></td>
                                <td class="text-center">{{ $data->composite_bpm }}</td>
                                <td class="text-center">{{ $data->flow_id }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



