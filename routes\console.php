<?php


use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Report\Crm\CaseDetailReport;
use App\Cases;
use App\Console\Commands\HandleLoginStatistic;
use App\Migrate\PHISStuckList;
use App\Migrate\PHISTaskListBPM;
use App\Migrate\CheckChargingStuckList;
use App\Services\ScheduleService;
use App\Migrate\StuckCreateInvoiceTask;
use App\Migrate\UpdateSAPOrderNo;
use App\Migrate\CheckInvoiceAP511;
use App\Migrate\TerminateWorkflowNotifyServiceBPM;
use App\Migrate\ExtractInvoiceDetailAP511;
use App\Migrate\CallEpProcessFileIN;
use App\Migrate\ReTriggerFileIGFMAS;
use App\Migrate\StuckCreateSQTask;
use App\Migrate\BPMTaskServiceProgram;
use App\Migrate\BPMServiceTerminationInstance;
use App\Migrate\BiMigrateMyGPIS;
use App\Migrate\ClientRazerApi;
use App\EpSupportActionLog;
use App\Migrate\SmRollbackSiteVisitToApproval;
use App\Migrate\InactiveMinistryRestructurePTJService;
use App\Migrate\TerminateAndRefireTask;
use App\Migrate\ProdSupport\MigrateReportStatPerkhidmatanPerbendaharaan;
use App\Migrate\ProdSupport\MigrateReportStatTransactionEp;
use App\Migrate\ProdSupport\MigrateDataPatchingExcel;
use App\Migrate\SmToCancelPersonelActivationUser;
use App\Migrate\ResendGFMASIntegrationPayload;
use App\Migrate\AppSupport\ReportSupplierDisciplinaryAction;
use App\Migrate\AppSupport\SmRejectBumiStatusFixIssue;
use App\Migrate\AppSupport\SmFixIssueSyncMofSupplierAdminRole;
use App\Migrate\AppSupport\SmResendSoftcert;
use App\Migrate\AppSupport\SmSupplierSyncIgfmas;
use App\Migrate\AppSupport\PmOrgMasterDataFixDuplicate;
use App\Migrate\AppSupport\SmPaymentCardTypeSync;
use App\Migrate\AppSupport\SmPaymentReceiptEpSync;
use App\Migrate\AppSupport\PmUserSyncRoleWithSsoLiferay;
use App\Migrate\AppSupport\SmSupplierResendApiveDueToSchedulerSkip;
use App\Migrate\CheckAp511ProcessIsUpdated;
use App\Services\Ep\SmSupplierService;
use App\Migrate\CheckUserActivityRep;
use App\Services\LogTrace\ExportLogService;
use App\Services\LogTrace\LogTraceService;
use App\Services\SsoApiService;
use App\Migrate\ResendFileBatchOUT;
use App\Migrate\AppSupport\SmFixPaymentProcess;
use App\Migrate\Bpm\BPMReAssignTaskProgram;
use App\Migrate\Bpm\BPMUpgrade12c;
use App\Migrate\Bpm\BpmAlterFlow;
use App\Migrate\Bpm\BpmQTRefireInstance;
use App\Migrate\Bpm\BpmStuckRunningInstance;
use App\Migrate\Osb\OsbRefireIgfmasPaymentMatchQuery;
use App\Migrate\Osb\OsbResendMM504ErrorKodItemFrn;
use App\Migrate\Bpm\BpmStuckVerifyLoa;
use App\Migrate\CheckSpkiServiceAvailibility;
use App\Migrate\ImageGeneratorVisitorCountEP;
use App\Http\Controllers\GFMASController;
use GuzzleHttp\Client;
use App\Migrate\Bpm\QtMissingEvaluationTask;
use App\Migrate\AppSupport\SmSupplierUpdateChangedDateToToday;

/*
  |--------------------------------------------------------------------------
  | Console Routes
  |--------------------------------------------------------------------------
  |
  | This file is where you may define all of your Closure based console
  | commands. Each Closure is bound to a command instance allowing a
  | simple approach to interacting with each command's IO methods.
  |
 */
Artisan::command('schedule-list', function () {
   $scheduleServ = new  ScheduleService;
   $result  = $scheduleServ->getCommands();
   dump($result);
});

Artisan::command('check-user-activity', function () {
    CheckUserActivityRep::run();
 });



Artisan::command('mr-inactive-list-ptj {org_profile_id?}', function () {
    $orgProfileId = $this->argument('org_profile_id');
    if($orgProfileId){
    InactiveMinistryRestructurePTJService::execute($orgProfileId, null);
    }else{
        InactiveMinistryRestructurePTJService::execute();
    }
});

Artisan::command('mr-inactive-by-proc_sts {process_status}', function () {
    $processStatus = $this->argument('process_status');
    InactiveMinistryRestructurePTJService::execute(null, $processStatus);
});

Artisan::command('mr-inactive-list-ptj-insert', function () {
    InactiveMinistryRestructurePTJService::insertInactivePTJToEpLogInactivePTJ();
});


Artisan::command('rollback-sm-sitevisit-to-approval-import', function () {
    SmRollbackSiteVisitToApproval::importFileApplsRollback();
});

Artisan::command('rollback-sm-sitevisit-to-approval-run {total_appl} {process_status} {isSkipTerminateTask}', function () {
    
    $totalRec = $this->argument('total_appl');
    $processStatus = $this->argument('process_status');
    $isSkipTerminateTask = $this->argument('isSkipTerminateTask');
    
    $param = collect();
    $param->put('total_appl', $totalRec);
    $param->put('process_status', $processStatus);
    $param->put('isSkipTerminateTask', $isSkipTerminateTask);
    
    SmRollbackSiteVisitToApproval::runRollbackSiteVisitToApproverByList($param);
    
    dump('Completed');
})->describe('parameter 1st [Total Get Record] , 2nd [Process Status 0 - Pending, 2 - Failed] , Sample : rollback-sm-sitevisit-to-approval-run 5 0 ');
Artisan::command('rollback-sm-sitevisit-to-approval {appl_no} {isSkipTerminateTask}', function () {
    $applNo = $this->argument('appl_no');
    $isSkipTerminateTask = $this->argument('isSkipTerminateTask');
    SmRollbackSiteVisitToApproval::runRollbackSiteVisitToApprover($applNo,$isSkipTerminateTask);
});

/**
 * To update personnel already invalid activation creation login ID
 */
Artisan::command('sm-cancel-personnel-activation-invalid', function () {
    $startDate = $this->ask('Start date (YYYY-MM-DD : 2018-01-01) : ');
    $endDate = $this->ask('End date (YYYY-MM-DD : 2018-01-02) : ');
    SmToCancelPersonelActivationUser::run($startDate,$endDate);
});



Artisan::command('phis', function () {
    PHISStuckList::run();
});
Artisan::command('phis-task', function () {
    PHISTaskListBPM::run();
});

Artisan::command('crm-resolved-phis', function () {
    App\Services\CRMService::resolveTaskByListCaseNo();
});

Artisan::command('task-create-invoice', function () {
    StuckCreateInvoiceTask::run();
});

Artisan::command('terminate-refire-task', function () {
    StuckCreateInvoiceTask::terminateFire();
});

Artisan::command('task-create-sq', function () {
    StuckCreateSQTask::run();
});

Artisan::command('update-saporderno-list', function () {
    UpdateSAPOrderNo::run();
});

Artisan::command('check-inv-ap511', function () {
    CheckInvoiceAP511::run();
});


/** Do not use this program. Last run for TRG eP environment only. To remove instance */
/*
Artisan::command('terminate-composite-instance {year}', function () {
    $year = $this->argument('year'); 
    BPMTaskServiceProgram::terminateBPMInstanceCreatedByYear($year);
    dump('Completed');
});
*/

Artisan::command('insert-instance-bpm', function () {
    MigrateUtils::logDump(__METHOD__ . ' insert-instance-bpm Started'); 
    BPMServiceTerminationInstance::mergeBpmInstanceOldVersion();
    MigrateUtils::logDump(__METHOD__ . ' insert-instance-bpm Completed'); 
});
Artisan::command('terminate-selected-instance-bpm', function () {
    MigrateUtils::logDump(__METHOD__ . ' terminate-selected-instance-bpm Started'); 
    BPMServiceTerminationInstance::terminateTasksBySelectionComposite();
    MigrateUtils::logDump(__METHOD__ . ' terminate-selected-instance-bpm Completed'); 
});


Artisan::command('terminate-list-task-causetimeout', function () {
    BPMTaskServiceProgram::terminateTasksPOCOCauseTimeOut();
    dump('Completed');
});
Artisan::command('terminate-task-notification-codification', function () {
    BPMTaskServiceProgram::terminateTasksNotificationCodification();
    dump('Completed');
});

Artisan::command('insert-instance-old-version', function () {
    MigrateUtils::logDump(__METHOD__ . ' insert-instance-old-version Started'); 
    dd('exit -- deprecated');
    BPMTaskServiceProgram::mergeBpmInstanceOldVersion();
    BPMTaskServiceProgram::reUpdateStatusDocBpmInstanceOldVersion();
    BPMTaskServiceProgram::reUpdateStatusDocBpmInstanceOldVersion(); // rerun
    BPMTaskServiceProgram::reUpdateDocNoInstanceAsOne();
    BPMTaskServiceProgram::reUpdateDocNoQuotationTenderInfo();
    BPMTaskServiceProgram::reUpdateDocNoContractInfo();
    BPMTaskServiceProgram::reUpdateDocNoContractInfoCms();

    BPMTaskServiceProgram::updatePreparationContractTechRefresh();
    BPMTaskServiceProgram::findPayloadCT();
    MigrateUtils::logDump(__METHOD__ . ' insert-instance-old-version Completed'); 
});
Artisan::command('reupdate-status-instance-old-version', function () {
    dd('exit -- deprecated');
    BPMTaskServiceProgram::reUpdateStatusDocBpmInstanceOldVersion();
    MigrateUtils::logDump(__METHOD__ . ' Completed');
}); 
Artisan::command('reupdate-set-single-doc', function () {
    // $compositeVersion = $this->argument('composite_version');
    BPMTaskServiceProgram::reUpdateDocNoInstanceAsOne();
    MigrateUtils::logDump(__METHOD__ . ' Completed');
});
Artisan::command('reupdate-instance-ct-info', function () {
    BPMTaskServiceProgram::reUpdateDocNoContractInfo();
    MigrateUtils::logDump(__METHOD__ . ' reupdate-instance-ct-info Completed');
});
Artisan::command('reupdate-instance-ct-info-cms', function () {
    BPMTaskServiceProgram::reUpdateDocNoContractInfoCms();
    MigrateUtils::logDump(__METHOD__ . ' reupdate-instance-ct-info Completed');
});
Artisan::command('reupdate-instance-qt-info', function () {
    BPMTaskServiceProgram::reUpdateDocNoQuotationTenderInfo();
    MigrateUtils::logDump(__METHOD__ . ' reupdate-instance-qt-info Completed');
});
Artisan::command('terminate-instance-selected-invalid', function () {
    $dtStartTime = Carbon::now();
    MigrateUtils::logDump(__METHOD__ . ' terminate-instance-selected-invalid Started');
    BPMTaskServiceProgram::terminateTasksBySelectionComposite();
    MigrateUtils::logDump(__METHOD__ . ' terminate-instance-selected-invalid Completed');
    MigrateUtils::logDump(__METHOD__ . ' terminate-instance-selected-invalid --- Taken Time :'. json_encode(MigrateUtils::getTakenTime($dtStartTime)));
});
Artisan::command('prepare-ct-tech-refresh-phase3', function () {
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-phase3 Started');
    BPMTaskServiceProgram::updatePreparationContractTechRefresh();
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-phase3 Completed');
});
Artisan::command('prepare-ct-tech-refresh-payload', function () {
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-payload Started');
    BPMTaskServiceProgram::findPayloadCT();
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-payload Completed');
});

Artisan::command('prepare-ct-tech-refresh-refire-instance', function () {
    $dtStartTime = Carbon::now();
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-refire-instance Started');

    //BPMTaskServiceProgram::refireCTSA(); MigrateUtils::logDump('');
    
    BPMTaskServiceProgram::refireCTAC();MigrateUtils::logDump('');
    
    //BPMTaskServiceProgram::refireCTFD();

    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-refire-instance Completed');
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-refire-instance --- Taken Time :'. json_encode(MigrateUtils::getTakenTime($dtStartTime)));
});

Artisan::command('prepare-ct-tech-refresh-updatever-instance', function () {
    $dtStartTime = Carbon::now();
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-updatever-instance Started');
    $limit = $this->ask('Please set limit number for selection data : ');
    BPMTaskServiceProgram::terminateUpdateVerCT($limit);

    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-updatever-instance Completed');
    MigrateUtils::logDump(__METHOD__ . ' prepare-ct-tech-refresh-updatever-instance --- Taken Time :'. json_encode(MigrateUtils::getTakenTime($dtStartTime)));
});





Artisan::command('terminate-instance-list', function () {
    // $compositeVersion = $this->argument('composite_version');
    BPMTaskServiceProgram::terminateBPMInstanceList();
    dump('terminate-instance-list Completed'); 
});


Artisan::command('refire-task-sm-softcert-paid', function () {
    $docNo = $this->ask('Please put Appl No. (Supplier Management): ');
    $res = BPMTaskServiceProgram::checkAndrefirePaidSoftcertTaskSmApplication($docNo);
    dump("RESULT : ".$res);
    dump('Completed');
});

Artisan::command('refire-force-task-sm-softcert-paid {login_id} {appl_id} {user_id} {pending_process_id} {personnel_id} {doc_no}', function () {
    $login_id = $this->argument('login_id');
    $appl_id = $this->argument('appl_id');
    $user_id = $this->argument('user_id');
    $pending_process_id = $this->argument('pending_process_id');
    $personnel_id = $this->argument('personnel_id');
    $doc_no = $this->argument('doc_no');

    dump("refire-force-task-sm-softcert-paid {login_id}=$login_id {appl_id}=$appl_id {user_id}=$user_id {pending_process_id}=$pending_process_id {personnel_id}=$personnel_id {doc_no}=$doc_no"); 
    $objData = collect([]);
    $objData->put('login_id',$login_id);
    $objData->put('appl_id',$appl_id);
    $objData->put('user_id',$user_id);
    $objData->put('pending_process_id',$pending_process_id);
    $objData->put('personnel_id',$personnel_id);
    $objData->put('doc_no',$doc_no);

    $res = BPMTaskServiceProgram::refirePaidSoftcertTaskSmApplication($objData);
    dump("refire-force-task-sm-softcert-paid : RESULT >> ".$res);
    dump('Completed');
});

Artisan::command('initiate-task-sm-appl', function () {
    $listApplNo =  array(
        'KB-15022024-0026' ,
    );
    //Before refire please check version module SM.. to update processID.
    foreach($listApplNo as $key => $docNo){
        dump('');
        dump("Start ".($key+1).'/'.count($listApplNo).' >> '.$docNo);
        $res = BPMTaskServiceProgram::initiateTaskSmApplication($docNo);
        dump("RESULT : ".json_encode($res));
    }
    
    dump('Completed');
});

Artisan::command('initiate-task-sm-appl-no {docNo}', function () {
    $docNo = $this->argument('docNo');
    dump("initiate-task-sm-appl-no $docNo");
    $res = BPMTaskServiceProgram::initiateTaskSmApplication($docNo);
    dump("RESULT : ".json_encode($res));
    dump('Completed');
});

Artisan::command('initiate-task-fl-delivery-order', function () {
    $listDeliveryNo =  array(
        '60000706502418895',
        
      
    );
    //Before refire please check version module SM.. to update processID.
    foreach($listDeliveryNo as $key => $docNo){
        dump('');
        dump("Start ".($key+1).'/'.count($listDeliveryNo).' >> '.$docNo);
        $res = BPMTaskServiceProgram::initiateTaskFlDeliveryOrder($docNo);
        dump("RESULT : ".$res);
    }
    
    dump('Completed');
});


/**
 * check table ep_invoice_check to reupdate Payment Advice doc no.
 */
Artisan::command('check-paymentadvice-ap511', function () {

    $listAP511 = DB::connection('mysql_ep_support')->table('ep_invoice_check')
            //->where('is_ap511', 'YES')
            ->whereNull('is_pa_no_exist')
            ->take('5000')
            ->orderBy('inv_date_created','desc')
            ->get();
    dump(' Total result : '.count($listAP511));
    
    foreach ($listAP511 as $row) {

        $pocoNo = $row->poco_no;
        dump('find ... ' . $pocoNo);
        $listPa = DB::connection('oracle_nextgen_rpt')->select("
            select distinct a.DOC_NO as poco_no, b.DOC_NO as pa_no from pm_tracking_diary a, pm_tracking_diary b
            where a.GROUP_ID = b.GROUP_ID
            and b.STATUS_ID = 46005
            and b.DOC_TYPE = 'PA'
            and b.GROUP_DOC_TYPE in ('PO','CO')
            and a.doc_no = ? 
        ", array($pocoNo));
        if (count($listPa) > 0) {
            $trackPa = $listPa[0];
            $paNo = $trackPa->pa_no;
            dump($trackPa);

            $checkPa = DB::connection('oracle_nextgen_rpt')->table('fl_payment_advice')
                            ->where('payment_advice_no', $paNo)->count();
            $isExistPa = 'NO';
            if ($checkPa > 0) {
                $isExistPa = 'YES';
            }
            dump('check is exist in PA table :- ' . $isExistPa);

            DB::connection('mysql_ep_support')
                    ->table('ep_invoice_check')
                    ->where('id', $row->id)
                    ->update(array('pa_no' => $paNo, 'is_pa_no_exist' => $isExistPa));
        } else {
            dump('Not Found! by search : ' . $pocoNo);
        }
    }

    dump('Completed');
});

Artisan::command('extract-inv-ap511', function () {
    ExtractInvoiceDetailAP511::run();
});

Artisan::command('extract-inv-ap511-by-list', function () {
    ExtractInvoiceDetailAP511::runAP511ByList();
});


Artisan::command('terminate-wfnotify-bpm', function () {
    TerminateWorkflowNotifyServiceBPM::run();
});

Artisan::command('refire-epp013', function(){
    CheckChargingStuckList::run();
})->describe('refire list stuck task waiting response 1gfmas check charging ');

Artisan::command('test-mail', function () {
    sendErrorEmail("Test On Email Only.");
});
Artisan::command('send-mail-ep-issue-qt', function () {
    $checkConfirm = $this->ask('Are you sure to send this email. This email will send to eP User Government.');
    
    dd('Set disable');
    /***
    QT240000000010904	
    01 Julai 2024 hingga 08 Julai 2024	
    YBrs. Dr.
    to: Izamin <NAME_EMAIL>
    cc: Mohd Shazele <NAME_EMAIL>;Mohd Ruslan <NAME_EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>
    Attached: SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN JKN KELANTAN.pdf

    QT240000000017790	
    25 Jun 2024 hingga 02 Julai 2024
    YBrs. Dr.	
    to: Dr Syirahaniza Binti <NAME_EMAIL>
    cc: Baizura <NAME_EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>
    Attached: SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN PEJABAT PERGIGIAN KOTA SETAR.pdf

    QT240000000018977	
    27 Jun 2024 hingga 04 Julai 2024
    YBhg. Datin	
    to: Datin Paduka Roslinah Binti <NAME_EMAIL>
    cc: Farrah Wahida <NAME_EMAIL>; <EMAIL>; <EMAIL>
    Attached: SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN BDSP.pdf


    QT240000000022123 dan QT240000000022124
    09 Julai 2024 hingga 16 Julai 2024	
    YBhg. Dato'
    to: <EMAIL>
    cc: <EMAIL>; <EMAIL>; <EMAIL>; <EMAIL>
    Attached: SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN JABATAN PERHUTANAN JOHOR.pdf

    ***/
    $collect = collect([]);

    // $data1 = collect([]);
    // $data1->put('qt_no','QT240000000010904');
    // $data1->put('greeting_salutation','YBrs. Dr.');
    // $data1->put('date_qt','01 Julai 2024 hingga 08 Julai 2024');
    // $data1->put('send_subject','Ralat Sistem ePerolehan (eP) Bagi Maklumat Lokaliti Liputan, Modul Sebut Harga / Tender');
    // $data1->put('send_to',['<EMAIL>']);
    // $data1->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data1->put('send_to',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data1->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // $data1->put('send_attach_file',storage_path('app/exports/epapp/qt-makluman').'/SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN JKN KELANTAN.pdf');
    // $collect->push($data1);

    // $data2 = collect([]);
    // $data2->put('qt_no','QT240000000017790');
    // $data2->put('date_qt','25 Jun 2024 hingga 02 Julai 2024');
    // $data2->put('greeting_salutation','YBrs. Dr.');
    // $data2->put('send_subject','Ralat Sistem ePerolehan (eP) Bagi Maklumat Lokaliti Liputan, Modul Sebut Harga / Tender');
    // $data2->put('send_to',['<EMAIL>']);
    // $data2->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data2->put('send_to',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data2->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // $data2->put('send_attach_file',storage_path('app/exports/epapp/qt-makluman').'/SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN PEJABAT PERGIGIAN KOTA SETAR.pdf');
    // $collect->push($data2);

    // $data3 = collect([]);
    // $data3->put('qt_no','QT240000000018977');
    // $data3->put('date_qt','27 Jun 2024 hingga 04 Julai 2024');
    // $data3->put('greeting_salutation','YBhg. Datin');
    // $data3->put('send_subject','Ralat Sistem ePerolehan (eP) Bagi Maklumat Lokaliti Liputan, Modul Sebut Harga / Tender');
    // $data3->put('send_to',['<EMAIL>']);
    // $data3->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data3->put('send_to',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // //$data3->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>']);
    // $data3->put('send_attach_file',storage_path('app/exports/epapp/qt-makluman').'/SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN BDSP.pdf');
    // $collect->push($data3);

    $data4= collect([]);
    $data4->put('qt_no','QT240000000022123 dan QT240000000022124');
    $data4->put('date_qt','09 Julai 2024 hingga 16 Julai 2024');
    $data4->put('greeting_salutation',"YBhg. Dato'");
    $data4->put('send_subject','Ralat Sistem ePerolehan (eP) Bagi Maklumat Lokaliti Liputan, Modul Sebut Harga / Tender');
    $data4->put('send_to',['<EMAIL>']);
    $data4->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']);
    //$data4->put('send_to',['<EMAIL>','<EMAIL>','<EMAIL>']);
    //$data4->put('send_cc',['<EMAIL>','<EMAIL>','<EMAIL>']);
    $data4->put('send_attach_file',storage_path('app/exports/epapp/qt-makluman').'/SURAT RALAT SISTEM eP BAGI MAKLUMAT LOKALITI LIPUTAN JABATAN PERHUTANAN JOHOR.pdf');
    $collect->push($data4);

    if($checkConfirm == 'APPROVED'){
        foreach($collect as $data){
            dump("######################");
            sendEmailePByBantuanPTJ($data);
        }
    }
    
});


Artisan::command('callback-ep', function () {
    CallEpProcessFileIN::run();
});


Artisan::command('sample', function () {
    $filename = '1000APOVE4000012018041602119.GPG';
    $date = substr($filename, 15, 8);

    $tarikhExtraFile = Carbon::createFromFormat('Ymd', '20180415')->startOfDay();
    $tarikh = Carbon::createFromFormat('Ymd', $date)->startOfDay();
    $isGreater = $tarikh->gt($tarikhExtraFile);
    dd($isGreater);

});


Artisan::command('get-icno', function(){
    $icno = $this->ask('Please put IC No. : ');
    $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>130101</AgencyCode><BranchCode>JPNCRS</BranchCode><UserId>JPNADM2</UserId><TransactionCode>T2</TransactionCode><RequestDateTime>2016-04-26T10:41:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
    $xmlContents = '"'.$xmlContents.'"';
    $urlIdentity = "https://esb.myidentity.gov.my:81/crsservice";
    $urlHeader = "'Content-Type: application/xml'";

    $curlRequest = "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents;

    $commands  = [
        $curlRequest,
    ];
    SSH::into('osb')->run($commands, function($line) use ($icno) {
        $data = $line.PHP_EOL;
        if (strpos($data, $icno) !== false) {
	    dump($data);
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);
            dd($vals);
        }
    });

})->describe('OSB get ICNO');

Artisan::command('get-ssm-info', function(){
    $ssmNo = $this->ask('Please put SSM No. Contoh (310628): ');
    $ssmType = $this->ask('Please put SSM Type Contoh (ROC,ROB)  : ');
    $uuid  = \Ramsey\Uuid\Uuid::uuid4();
    $xmlContents = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:ret='http://www.ep.gov.my/Schema/1-0/RetrieveSSMInfo' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'><x:Header/><x:Body><ret:EPMFRq><epm:RqHeader><epm:ConsumerID>EPP-001</epm:ConsumerID><epm:UID><epm:RqUID>$uuid</epm:RqUID></epm:UID></epm:RqHeader><ret:RetrieveSSMInfoRq><ret:BusinessRegistrationNo>$ssmNo</ret:BusinessRegistrationNo><ret:Type>$ssmType</ret:Type></ret:RetrieveSSMInfoRq></ret:EPMFRq></x:Body></x:Envelope>";
    $xmlContents = '"'.$xmlContents.'"';
    $urlIdentity = "http://192.168.63.205:7011/RetrieveSSMInfo/v1.0";
    $urlHeader = "'Content-Type: text/xml; charset=utf-8'";

    $curlRequest = "curl -k ".$urlIdentity." --header  ".$urlHeader."  -d ".$xmlContents;

    $commands  = [
        $curlRequest,
    ];
    dump($curlRequest);
    SSH::into('osb')->run($commands, function($line) {
        $data = $line.PHP_EOL;
        dump($data);
        $p = xml_parser_create();
        xml_parse_into_struct($p, $data, $vals, $index);
        xml_parser_free($p);
        dump($vals);
    });

})->describe('get SSM information');


Artisan::command('get-apive', function(){

    $epNo = "eP-1000I011E";
    $commands  = [
        'cd /batch/Temp',
        'grep -E "'.$epNo.'" 1000APIVE40000120*.GPG',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function($line) use ($epNo,&$filesFound)  {
        $data = $line.PHP_EOL;
        var_dump($data);

        if (strpos($data, $epNo) !== false) {
           $arrayData  = (explode("\n",$data));
           foreach($arrayData  as $str){
                $filename = substr($str, 0, 30);
                //var_dump($filename);
                if(strlen($filename) > 25)
                    array_push($filesFound,trim($filename));
           }
        }
        var_dump($filesFound);
    });
})->describe('Portal get APIVE');

Artisan::command('check-exist-filename-in-archive  {filename}', function(){
    //$searchFileName = '1007AP51140000120190716001.GPG';
    $filename = $this->argument('filename');
    $commands  = [
        'cd /batch/1GFMAS/ARCHIVE/IN',
        'find '.$filename,
        'exit',
    ];
    $filesFound = collect([]);
    SSH::into('portal')->run($commands, function($line) use (&$filesFound,$filename)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            if($str == $filename){
                $filesFound->push(trim($str));
            }
        }
    });
    dump($filesFound);
})->describe('Portal check APIVE OUT Folder');

Artisan::command('check-exist-filename-in', function(){
    $searchFileName = '1011AP51140000120210222001.GPG';
    $commands  = [
        'cd /batch/1GFMAS/ARCHIVE/IN',
        'find '.$searchFileName,
        'exit',
    ];
    $filesFound = collect([]);
    SSH::into('portal')->run($commands, function($line) use (&$filesFound,$searchFileName)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            if($str == $searchFileName){
                $filesFound->push(trim($str));
            }
        }
    });
    dump($filesFound);
})->describe('Portal check APIVE OUT Folder');

Artisan::command('get-file-batch-out', function(){
    $batchIntegration = $this->ask('Please put batch name such as (1GFMAS,ePerunding,LMS,MyGPIS,PHIS,SPA) : ');
    $lengthCheck = $this->ask('Please put minumum length filename to show in list : ');
    
    $commandGetList = 'ls -1  /batch/'.$batchIntegration.'/OUT';
    $commands  = [
        $commandGetList,
        'exit',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function($line) use (&$filesFound,$lengthCheck)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            //$filename = substr($str, 0, 32); 
            if(strlen($str) >= $lengthCheck ){
                array_push($filesFound,trim($str));   
            }
        }
    });
        
    dump($filesFound);
    dump('Completed');
})->describe('Check to get list file batch/out by parameter batch name');

Artisan::command('check-batch-out', function(){
    
    $commandsIGFMAS  = [
        'ls /batch/1GFMAS/OUT',
        'exit',
    ];
    $filesFoundGFMASOUT = array();
    SSH::into('portal')->run($commandsIGFMAS, function($line) use (&$filesFoundGFMASOUT)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 32);
            if(strlen($filename) > 25 ){
                array_push($filesFoundGFMASOUT,trim($filename));
            }
        }
    });
    var_dump('List IGFMAS OUT File :: total :- '.count($filesFoundGFMASOUT));
    //var_dump($filesFoundGFMASOUT);

    $commandsPHIS  = [
        'ls  /batch/PHIS/OUT',
        'exit',
    ];
    $filesFoundPHISOUT = array();
    SSH::into('portal')->run($commandsPHIS, function($line) use (&$filesFoundPHISOUT)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 32);
            if(strlen($filename) > 25 ){
                array_push($filesFoundPHISOUT,trim($filename));
            }
        }
    });
    var_dump('List PHIS OUT Files :: total :- '.count($filesFoundPHISOUT));
    //var_dump($filesFoundPHISOUT);


    $commandsEperunding  = [
        'ls  /batch/ePerunding/OUT',
        'exit',
    ];
    $filesFoundEperundingOUT = array();
    SSH::into('portal')->run($commandsEperunding, function($line) use (&$filesFoundEperundingOUT)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 32);
            if(strlen($filename) > 7 ){
                array_push($filesFoundEperundingOUT,trim($filename));
            }
        }
    });
    var_dump('List ePERUNDING OUT Files :: total :- '.count($filesFoundEperundingOUT));
    //var_dump($filesFoundEperundingOUT);


    $commandsLMS  = [
        'ls  /batch/LMS/OUT',
        'exit',
    ];
    $filesFoundLMSOUT = array();
    SSH::into('portal')->run($commandsLMS, function($line) use (&$filesFoundLMSOUT)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 32);
            if(strlen($filename) > 25 ){
                array_push($filesFoundLMSOUT,trim($filename));
            }
        }
    });
    var_dump('List LMS OUT Files :: total :- '.count($filesFoundLMSOUT));
    //var_dump($filesFoundLMSOUT);


    $commandsSPA  = [
        'ls  /batch/SPA/OUT',
        'exit',
    ];
    $filesFoundSPAOUT = array();
    SSH::into('portal')->run($commandsSPA, function($line) use (&$filesFoundSPAOUT)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 32);
            if(strlen($filename) > 21 ){
                array_push($filesFoundSPAOUT,trim($filename));
            }
        }
    });
    var_dump('List SPA OUT Files :: total :- '.count($filesFoundSPAOUT));
    //var_dump($filesFoundLMSOUT);


})->describe('Portal check APIVE OUT Folder');

Artisan::command('check-gfmas-out', function(){
    $commands  = [
        'cd /batch/1GFMAS/OUT',
        'find 1000APIVE40000*.GPG',
        'exit',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 31);
            $pos = strpos($filename, '1000APIVE40000120');
            if($pos !== false){
                if(strlen($filename) > 25 ){
                    array_push($filesFound,trim($filename));
                }
            }
        }
    });
    var_dump($filesFound);
})->describe('Portal check APIVE OUT Folder');

Artisan::command('check-gfmas-in', function(){
    $commands  = [
        'cd /batch/1GFMAS/IN',
        'find 1000APOVE40000120*.GPG',
        'exit',
    ];
    $filesFound = array();
    SSH::into('portal')->run($commands, function($line) use (&$filesFound)  {
        $data = $line.PHP_EOL;
        $arrayData  = (explode("\n",$data));
        foreach($arrayData  as $str){
            $filename = substr($str, 0, 31);
            //var_dump($filename);
            if(strlen($filename) > 25)
                array_push($filesFound,trim($filename));
        }
        var_dump($filesFound);
    });
})->describe('Portal check APIVE IN Folder');

Artisan::command('gfmas-alive', function(){
    $data = array();
    $commands  = [
        "echo 'exit' | sftp -oPort=2022 eperolehan@10.38.206.73",
    ];
    SSH::into('osb')->run($commands, function($line) use (&$data)  {
        $result = $line.PHP_EOL;
        var_dump($result);
        array_push($data,trim($result));
    });
    dd($data);

})->describe('OSB - OSB get connection 1GFMAS');

Artisan::command('gfmas-out', function(){
    $data = array();
    $commands  = [
        "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:OUT",
        "exit",
    ];
    SSH::into('osb')->run($commands, function($line) use (&$data)  {
        $result = $line.PHP_EOL;
        $arrayData  = (explode("\n",$result));
        foreach($arrayData  as $str){
            $pos = strpos($str, '.GPG');
            if($pos !== false){
                $filename = substr($str, -31);
                array_push($data,trim($filename));
             }
        }
        //array_push($data,trim($result));
    });
    dd($data);
})->describe('OSB - OSB get connection 1GFMAS');


Artisan::command('gfmas-in', function(){
    $data = array();
    $commands  = [
        "echo 'ls -lr' | sftp -oPort=2022 eperolehan@10.38.206.73:IN",
        "exit",
    ];
    SSH::into('osb')->run($commands, function($line) use (&$data)  {
        $result = $line.PHP_EOL;
        $arrayData  = (explode("\n",$result));
        foreach($arrayData  as $str){
            $pos = strpos($str, '.GPG');
            if($pos !== false){
                $filename = substr($str, -31);
                array_push($data,trim($filename));
             }
        }
    });
    dd($data);
})->describe('OSB - OSB get connection 1GFMAS');


Artisan::command('portal-get-doc-appl {appl_no}', function(){
    $applNo = $this->argument('appl_no');
    $listDocFiles = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM SM_APPL_DOC_FILE 
    where appl_support_doc_id in ( 
        select appl_support_doc_id from sm_appl_support_doc where appl_id in (
                select appl_id from sm_appl where appl_no = ?
            )
        )
        ", array($applNo));

    $prePathRepo = '/docuRepo/prd/ngep/';
    foreach ($listDocFiles as $doc){
        if($doc->file_name && $doc->file_path ){
            $remotePath =   $prePathRepo.$doc->file_path.$doc->file_name;
            $localPath = 'C:\\My\\GetFiles\\epss\\'.$doc->file_name;
            SSH::into('portal')->get($remotePath, $localPath);
            MigrateUtils::logDump(__METHOD__ . ' Download file : '. $remotePath );
        }
        
    }
})->describe('Portal to get file pdf');

// 2018/SM/KC-23022018-0004/SD/
// FARNOR-GLOBAL.pdf
Artisan::command('portal-get-cert-mof', function(){
    $remotePath =  "/docuRepo/prd/ngep/2018/SM/VIRTUAL_CERT/589670_20180116_143832.pdf";
    $localPath = "C:\\My\\GetFiles\\epss\\589670_20180116_143832.pdf";
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-get-file-supplier', function(){
    $remotePath =  "/docuRepo/prd/ngep/2018/SM/KC-23022018-0004/SD/FARNOR-GLOBAL.pdf";
    $localPath = "C:\\My\\GetFiles\\epss\\KC-23022018-0004_FARNOR-GLOBAL.pdf";
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-get-file-softcert {user_id}', function(){
    $userId = $this->argument('user_id');
    $listDocFiles = DB::connection('oracle_nextgen_rpt')->select("SELECT 
        s.company_name, s.ep_no,
        u.LOGIN_ID,u.USER_NAME, u.IDENTIFICATION_NO, 
        sr.is_free,sr.CREATED_DATE  AS softcert_req_date, sr.response_status,sr.remark,sr.SOFTCERT_PROVIDER ,
        sdf.file_name,sdf.FILE_PATH , d.DOC_DEFINITION_KEY ,d.DOC_NAME ,
        pd.CODE_NAME 
        FROM SM_SOFTCERT_DOC sd,SM_SOFTCERT_REQUEST sr,SM_SUPPLIER s,
        SM_SOFTCERT_DOC_FILE sdf ,
        PM_DOCUMENT d, PM_PARAMETER_DESC pd , PM_USER u
        WHERE sdf.SOFTCERT_DOC_ID =  sd.SOFTCERT_DOC_ID 
        AND d.DOCUMENT_ID  = sd.DOCUMENT_ID 
        AND pd.PARAMETER_ID  = d.DOC_CATEGORY_ID 
        AND pd.LANGUAGE_CODE  = 'en'
        AND sr.user_id = sd.user_id
        AND s.supplier_id = sr.SUPPLIER_ID 
        AND u.USER_ID = sd.USER_ID 
        AND sd.USER_ID = ? ", array($userId));

    $prePathRepo = '/docuRepo/prd/ngep/';
    foreach ($listDocFiles as $doc){
        if($doc->file_name && $doc->file_path ){
            $remotePath =   $prePathRepo.$doc->file_path.$doc->file_name;
            $localPath = "C:\\My\\GetFiles\\epss\\$userId\\".$doc->file_name;
            SSH::into('portal')->get($remotePath, $localPath);
            MigrateUtils::logDump(__METHOD__ . " Download file :  $doc->doc_name >> $remotePath" );
        }
        
    }
})->describe('Portal to get file pdf');


Artisan::command('portal-get-file-temp', function(){
    $filename = "1102AR50240000120220321001.GPG";
    $remotePath =  "/batch/Temp/".$filename;
    $localPath = "D:\\\GetFiles\\".$filename;
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file Batch');


Artisan::command('portal-get-file', function(){
    $filename = "1102AR50240000120180517001.GPG";
    $remotePath =  "/batch/1GFMAS/ARCHIVE/OUT".$filename;
    $localPath = "C:\\My\\GetFiles\\epss".$filename;
    SSH::into('portal')->get($remotePath, $localPath);
})->describe('Portal to get file pdf');

Artisan::command('portal-sent-file', function(){
    // SSH::into('staging')->put($localFile, $remotePath);
    $filename = "1000APIVE4000012018081600903.GPG";
    //$localPathPlainText = "D:\\Project\\JAVA\\eclipse-workspace\\Encryption\\file\\OUT\\apive\\encrypt\\".$filename;
    $localPathPlainText = "D:\\Project\\JAVA\\eclipse-workspace\\Encryption\\file\\OUT\\apive\\".$filename;

    $remoteTempPath =  "/batch/Temp/".$filename;
    SSH::into('portal')->put($localPathPlainText, $remoteTempPath);

    SSH::into('portal')->run([
        "chmod 666 $remoteTempPath"
    ]);

    $localPathEncryptText =  "D:\\Project\\JAVA\\eclipse-workspace\\Encryption\\file\\OUT\\apive\\encrypt\\".$filename;
    $remoteOutPath =  "/batch/1GFMAS/OUT/".$filename;
    SSH::into('portal')->put($localPathEncryptText, $remoteOutPath);

    SSH::into('portal')->run([
        "chmod 666 $remoteOutPath"
    ]);

})->describe('Portal sent file to Portal');




Artisan::command('refire-manual-epp013', function(){
    $docno = $this->ask('Please put DocNo (PO/CO) only : ');
    CheckChargingStuckList::triggerBPMCallbackCheckCharging($docno);

})->describe('Manual trigger DocNo PO/CO to refire callback CheckCharging EPP-013');



Artisan::command('report-cases', function(){
    $dateStart = Carbon::now()->yesterday()->format('Y-m-d');
    $dateEnd = Carbon::now()->yesterday()->format('Y-m-d');

    $dataParameter = collect([]);
    $dataParameter->put("date_start", $dateStart);  //mandatory
    $dataParameter->put("date_end", $dateEnd); //mandatory

    //Adding optinal, must add also checking optional in Query
//    $dataParameter->put("status", 'Open_Assigned'); //optional
    //$dataParameter->put("request_type", 'enquiry'); //optional

    dump($dataParameter);
    dump($dataParameter->get('date_start'));

//    CaseDetailReport::run($dataParameter);
    CaseDetailReport::run($dateStart,$dateEnd);

})->describe('Case Detail Incident/Service Report');



Artisan::command('decrypt-data', function(){
    $target = '1GFMAS'; //PHIS //EP
    $data = '-----BEGIN PGP MESSAGE-----
Version: BCPG v1.50

hI4DkObbW5fUX4kQAf4igzW+bSIf2GerDnZ+TIQG9oCEY5BsqTD/PcbtER5EXiF1
obHaPT2+C62tAYyrcdPuv00hXKICEavQeoaOTbKKAf9nj0tK0gHEHstgvTDzp2uT
hD+ZuHk2FiauyvnNTNTZ8F2uWcUFlk2CIyKS7Hwg7XuddOTUZ6Q8/uM+b8SMTmu5
0q8BBuJCySt6L2p9XcpY6oz4atL0FzdhgZiUn7QvxvDpNQm3dUA/b6i/8WiZE7bg
ZcPxoTDVhLTmTVO5lQSS5KbXOwW6W5+c4woLCNooCbDcYOLLjYTWIqywCz/qKYGz
IzTVv8BdWjPjxBiq+P7BFjTKLBTItab18TsvK8MQGacWMMmUsHqZTJ+2qFWkvXOP
5/zvrmXyF438qzZGBTA1jDpnRBHDoVItZfT6d0R7m86s
=c+2q
-----END PGP MESSAGE-----';

    $xmlContents = "<x:Envelope xmlns:x='http://schemas.xmlsoap.org/soap/envelope/' xmlns:pgp1='http://www.ep.gov.my/Schema/1-0/PGP' xmlns:epm='http://www.ep.gov.my/Schema/1-0/epmf'>
        <x:Header/>
        <x:Body>
            <pgp1:EPMFRq>
                <epm:RqHeader>
                </epm:RqHeader>
                <pgp1:PGPRq>
                    <pgp1:Mode>D</pgp1:Mode>
                    <pgp1:Target>$target</pgp1:Target>
                    <pgp1:Input>$data</pgp1:Input>
                </pgp1:PGPRq>
            </pgp1:EPMFRq>
        </x:Body>
        </x:Envelope>";
    //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:crs='http://tempuri.org/CRSService'><soapenv:Header/><soapenv:Body><crs:retrieveCitizensDataReq><AgencyCode>130101</AgencyCode><BranchCode>JPNCRS</BranchCode><UserId>JPNADM2</UserId><TransactionCode>T2</TransactionCode><RequestDateTime>2016-04-26T10:41:00</RequestDateTime><ICNumber>$icno</ICNumber><RequestIndicator>A</RequestIndicator></crs:retrieveCitizensDataReq></soapenv:Body></soapenv:Envelope>";
    $xmlContents = '"'.$xmlContents.'"';
    $url = "http://192.168.63.205:7011/Common/Utilities/PGPSO";
    $urlHeader = "'Content-Type: application/xml'";
    $commands  = [
        "curl -k ".$url." --header  ".$urlHeader."  -d ".$xmlContents,
    ];
    SSH::into('osb')->run($commands, function($line) {
        $data = $line.PHP_EOL;
        //dump($data);
        //if (strpos($data, $icno) !== false) {
            $p = xml_parser_create();
            xml_parse_into_struct($p, $data, $vals, $index);
            xml_parser_free($p);

            if(count($vals) > 0){
                foreach ($vals as $val){
                    if($val["tag"] == 'X:BODY') dump($val["value"]);
                }
            }

        //}
    });

})->describe('OSB get ICNO');


/**
 * Send an e-mail Test Only
 *
 * @param  Request  $error
 * @return Response
 */
function sendErrorEmail($error) {
    $data = array(
        "to" => ['<EMAIL>'],
        "subject" => 'Server:'.env('APP_ENV').'- Test Only'
    );
    try {
        Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
            $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
            //$m->from('<EMAIL>', 'Enquiry SSM');
            $m->to($data["to"])
                    //->cc($data["cc"])
                //->attach(storage_path('app/exports/cases').'/CaseDetailReport_2018-03-01_to_2018-03-31.xlsx')
                    ->subject($data["subject"]);
        });
        dump('done send');
    } catch (\Exception $e) {
        Log::error('Console :: sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        return $e;
    }
}

/**
 * Adhoc send email to specifc PTJ user. Please do not use this for your on testing.
 *
 * @param  Request  
 * @return Response
 */
function sendEmailePByBantuanPTJ($data) {
    $mailName = "Bantuan PTJ ePerolehan";

    try {
        // Using diff account mail provider. 
        $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
        ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
        ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
        ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

        $mailer = app(\Illuminate\Mail\Mailer::class);
        $mailer->setSwiftMailer(new \Swift_Mailer($transport));
        $mail = $mailer
        ->send('emails.maklumanQtIssueLocality', ['data' => $data, 'date' => Carbon::now()->toDateString()], 
            function($m) use ($data, $mailName ) {
            $m->from(Config::get('constant.email_sender_bantuan_ptj'), $mailName);
            // $m->replyTo($mailUser,$mailName); Off reply to
            $m->to( $data->get('send_to') );
            $m->cc( $data->get('send_cc') );
            $m->subject( $data->get('send_subject') );
            $m->attach( $data->get('send_attach_file'));
        });
        dump('done send : '.json_encode($data));
    } catch (\Exception $e) {
        Log::error('Console :: sendErrorEmail ', ['Email' => json_encode($data->get('send_to')), 'ERROR' => $e->getMessage()]);
        return $e;
    }
}



Artisan::command('case-redmine', function(){
    $redmineNoList = array(
       '19417','19419','19415','18986','18983','18982','18964','18479','18576','18889','18873','18867','18794','18665','18661','18655','18654','18625','18624','19051','19021','18962','18961','19101','19100','19099','19098','19404','19312','19298','19197','19186','19169','19172','19173','19176','19195','19200','19202','19299','19300','19310','19316','19341','19347'
    );

    foreach ($redmineNoList as $value){
        $list = Cases::where('redmine_number','like','%'.$value.'%')->get();
        foreach($list as $obj){
            //dump('Case No: '.$obj->case_number.'    ,Status: '.$obj->status.'    ,Resolution: '.$obj->resolution);
            dump('RedmineNo: '.$value.'     Caseno:'.$obj->case_number.'    Status:'.$obj->status);
        }
    }

})->describe('Find Case No. by Redmine No. ');

Artisan::command('ep_login_statistic', function(){
    HandleLoginStatistic::handle();
})->describe('Test');

Artisan::command('log_stat', function(){
    $ww = new HandleLoginStatistic;
    $ww->getePLoginDataDaily();
})->describe('Test');


Artisan::command('gfm-140', function(){
    HandleGFM140Schedule::handle();
})->describe('Test1');

/**
 * Run one time to get all task DO as ASSIGNED in BPM but this doc. no. not exist in eP Database
 */
Artisan::command('bpm-task-dono', function () {
   $list = DB::connection('oracle_bpm_rpt')->select("
            SELECT
                a.CUSTOMATTRIBUTESTRING1                                            AS DOC_NO,
                a.CUSTOMATTRIBUTESTRING2                                            AS DOC_TYPE,
                a.STATE,
                a.COMPOSITEINSTANCEID                                               AS composite_id,
                substr(a.TASKDEFINITIONID, 1, 24)                                   AS composite_module,
                a.TASKDEFINITIONID,
                (a.COMPOSITEINSTANCEID || ',' || substr(a.TASKDEFINITIONID, 1, 24)) AS comp_id_module,
                a.TASKNUMBER,
                a.PROCESSID,
                a.PROCESSNAME,
                a.COMPOSITENAME,
                a.ACTIVITYNAME,
                a.CREATEDDATE,
                a.UPDATEDDATE
              FROM wftask a
              WHERE a.CUSTOMATTRIBUTESTRING2 = 'DO' AND a.state = 'ASSIGNED'
              
              UNION 
              
              SELECT
                a.CUSTOMATTRIBUTESTRING1                                            AS DOC_NO,
                a.CUSTOMATTRIBUTESTRING2                                            AS DOC_TYPE,
                a.STATE,
                a.COMPOSITEINSTANCEID                                               AS composite_id,
                substr(a.TASKDEFINITIONID, 1, 24)                                   AS composite_module,
                a.TASKDEFINITIONID,
                (a.COMPOSITEINSTANCEID || ',' || substr(a.TASKDEFINITIONID, 1, 24)) AS comp_id_module,
                a.TASKNUMBER,
                a.PROCESSID,
                a.PROCESSNAME,
                a.COMPOSITENAME,
                a.ACTIVITYNAME,
                a.CREATEDDATE,
                a.UPDATEDDATE
              FROM wftask a
              WHERE a.ACTIVITYNAME = 'Create DO List' AND a.state = 'ASSIGNED'
            ");
        /*
        
         */
        $counterFound = 0;
        foreach ($list as $row){
            dump('Get composite_id -> '.$row->composite_id. ' , Doc No: '.$row->doc_no);
            $check = DB::connection('oracle_nextgen_rpt')->table('FL_DELIVERY_ORDER')->where('delivery_order_no',$row->doc_no)->count();
            if($check == 0){
                DB::connection('mysql_ep_support')
                            ->table('ep_bpm_task_orphan')->updateOrInsert(
                            [
                               'doc_no' =>$row->doc_no 
                            ],        
                            [
                                'composite_id' => $row->composite_id,
                                'composite_module' => $row->composite_module,
                                'composite_id_module' => $row->comp_id_module,
                                'component_instance_id' => $row->tasknumber,
                                'composite_name' => $row->compositename,
                                'component_name' => $row->processid,
                                'component_creation_date' => $row->createddate,
                                'doc_no' =>$row->doc_no,
                                'doc_type' => 'DO', // 'doc_type' =>$row->doc_type,
                                'activity_name' =>$row->activityname,
                                'created_at' =>Carbon::now(),
                                'remarks' =>'This document no. is not exists in eP Database.',
                            ]
                    );
                dump('  Success Inserted-> composite_instance_id '.$row->composite_id);
                $counterFound++;
            }
        }
        dump('Total doc no is not exists in eP Database : '.$counterFound);
        dump('Completed');
});


/**
 * Run one time to get all task SQ as ASSIGNED in BPM but this doc. no. not exist in eP Database
 */
Artisan::command('bpm-task-sqno', function () {
   $list = DB::connection('oracle_bpm_rpt')->select("
           SELECT
            a.CUSTOMATTRIBUTESTRING1                                            AS DOC_NO,
            a.CUSTOMATTRIBUTESTRING2                                            AS DOC_TYPE,
            a.STATE,
            a.COMPOSITEINSTANCEID                                               AS composite_id,
            substr(a.TASKDEFINITIONID, 1, 24)                                   AS composite_module,
            a.TASKDEFINITIONID,
            (a.COMPOSITEINSTANCEID || ',' || substr(a.TASKDEFINITIONID, 1, 24)) AS comp_id_module,
            a.TASKNUMBER,
            a.PROCESSID,
            a.PROCESSNAME,
            a.COMPOSITENAME,
            a.ACTIVITYNAME,
            a.CREATEDDATE,
            a.UPDATEDDATE
          FROM wftask a
          WHERE a.CUSTOMATTRIBUTESTRING2 = 'SQ' AND a.state = 'ASSIGNED'
                AND activityname = 'Submit Simple Quote'
               --  AND NOT EXISTS(SELECT * FROM SC_QUOTE@uatapp WHERE QUOTE_NO = a.CUSTOMATTRIBUTESTRING1)
        ");

        $counterFound = 0;
        
        foreach ($list as $row){
            dump('Get composite_id -> '.$row->composite_id. ' , Doc No: '.$row->doc_no);
            $check = DB::connection('oracle_nextgen_rpt')->table('sc_quote')->where('quote_no',$row->doc_no)->count();
            if($check == 0){
                DB::connection('mysql_ep_support')
                            ->table('ep_bpm_task_orphan')->updateOrInsert(
                            [
                               'doc_no' =>$row->doc_no 
                            ],         
                            [
                                'composite_id' => $row->composite_id,
                                'composite_module' => $row->composite_module,
                                'composite_id_module' => $row->comp_id_module,
                                'component_instance_id' => $row->tasknumber,
                                'composite_name' => $row->compositename,
                                'component_name' => $row->processid,
                                'component_creation_date' => $row->createddate,
                                'doc_no' =>$row->doc_no,
                                'doc_type' =>$row->doc_type,
                                'activity_name' =>$row->activityname,
                                'created_at' =>Carbon::now(),
                                'remarks' =>'This document no. is not exists in eP Database.',
                            ]
                    );
                dump('  Success Inserted-> composite_instance_id '.$row->composite_id);
                $counterFound++;
            }
            
        }
        dump('Total doc no is not exists in eP Database : '.$counterFound);
        dump('Completed');
});

/**
 * Run one time to get all task SM as ASSIGNED in BPM but this doc. no. not exist in eP Database
 */
Artisan::command('bpm-task-smappl', function () {
   $list = DB::connection('oracle_bpm_rpt')->select("
           SELECT
            a.CUSTOMATTRIBUTESTRING1                                            AS DOC_NO,
            a.CUSTOMATTRIBUTESTRING2                                            AS DOC_TYPE,
            a.STATE,
            a.COMPOSITEINSTANCEID                                               AS composite_id,
            substr(a.TASKDEFINITIONID, 1, 33)                                   AS composite_module,
            a.TASKDEFINITIONID,
            (a.COMPOSITEINSTANCEID || ',' || substr(a.TASKDEFINITIONID, 1, 33)) AS comp_id_module,
            a.TASKNUMBER,
            a.PROCESSID,
            a.PROCESSNAME,
            a.COMPOSITENAME,
            a.ACTIVITYNAME,
            a.CREATEDDATE,
            a.UPDATEDDATE
          FROM wftask a
          WHERE  a.state = 'ASSIGNED'
                AND activityname in ('Register Account','Draft Register Account') 
        ");

        $counterFound = 0;
        
        foreach ($list as $row){
            dump('Get composite_id -> '.$row->composite_id. ' , Doc No: '.$row->doc_no);
            $check = DB::connection('oracle_nextgen_rpt')->table('sm_appl')->where('appl_no',$row->doc_no)->count();
            if($check == 0){
                DB::connection('mysql_ep_support')
                            ->table('ep_bpm_task_orphan')->updateOrInsert(
                            [
                               'doc_no' =>$row->doc_no 
                            ],         
                            [
                                'composite_id' => $row->composite_id,
                                'composite_module' => $row->composite_module,
                                'composite_id_module' => $row->comp_id_module,
                                'component_instance_id' => $row->tasknumber,
                                'composite_name' => $row->compositename,
                                'component_name' => $row->processid,
                                'component_creation_date' => $row->createddate,
                                'doc_no' =>$row->doc_no,
                                'doc_type' =>$row->doc_type,
                                'activity_name' =>$row->activityname,
                                'created_at' =>Carbon::now(),
                                'remarks' =>'This document no. is not exists in eP Database.',
                            ]
                    );
                dump('  Success Inserted-> composite_instance_id '.$row->composite_id);
                $counterFound++;
            }
            
        }
        dump('Total doc no is not exists in eP Database : '.$counterFound);
        dump('Completed');
});


/**
 * Preparation get all tasks for YEP
 * Run one time to get all task ASSIGNED in BPM and flag as IS_YEP or MR_2020_1 or MR_2020_2
 * 
 * After run prepare.. using this command to start terminate with condition terminate-task-selection-mryeporphan
 */
Artisan::command('bpm-task-prepare-terminate', function () {

   $list = DB::connection('oracle_bpm_rpt')->select("
            SELECT  distinct
             a.CUSTOMATTRIBUTESTRING1                                            AS DOC_NO,
             a.CUSTOMATTRIBUTESTRING2                                            AS DOC_TYPE,
             a.STATE,
             a.COMPOSITEINSTANCEID                                               AS composite_id,
             SUBSTR(a.COMPOSITEDN , 1, Instr(a.COMPOSITEDN, '*', -1, 1) -1)  AS composite_module,
             a.TASKDEFINITIONID,
             SUBSTR(a.COMPOSITEDN , 1, Instr(a.COMPOSITEDN, '*', -1, 1) -1)  AS comp_id_module,
             a.TASKNUMBER,
             a.PROCESSID,
             a.PROCESSNAME,
             a.COMPOSITENAME,
             a.ACTIVITYNAME,
             a.CREATEDDATE,
             a.UPDATEDDATE,
             a.USERCOMMENT,
             a.taskid 
           FROM wftask a
           WHERE  
           a.USERCOMMENT  = 'ManualTerminate' 
           -- a.USERCOMMENT  = 'YEP-2024-5' 
           -- a.USERCOMMENT  is not NULL 
           -- AND a.state NOT IN ('STALE','EXPIRED') 
           -- AND a.USERCOMMENT  like 'MR%' 
           -- a.USERCOMMENT  like 'MR-%' 
           -- a.USERCOMMENT  is not null
        ");

        $counterFound = 0;
        //$docType = "MR"; // ministry restructuring
        //$docType = "HK-DP";  //House Keeping
        $docType = "SIT-ManualTerminate";
        dump("TOTAL : ".count($list));
       // dd('ok');
        foreach ($list as $row){
            dump('Get composite_id -> '.$row->composite_id. ' , Doc No: '.$row->doc_no);
           
                DB::connection('mysql_ep_support')
                            ->table('ep_bpm_task_orphan')->updateOrInsert(
                            [
                               'component_instance_id' => $row->tasknumber,
                               'composite_id' => $row->composite_id,
                               'doc_no' =>$row->doc_no,
                               'activity_name' =>$row->activityname,
                            ],         
                            [
                                'composite_module' => $row->composite_module,
                                'composite_id_module' => $row->comp_id_module,
                                'composite_name' => $row->compositename,
                                'component_name' => $row->processid,
                                'component_creation_date' => $row->createddate,
                                'doc_type' => $docType ,
                                'created_at' =>Carbon::now(),
                                'remarks' =>"Manual Terminate for BPM SIT",
                                //'remarks' =>"This document number flag as Housekeeping Direct Purchase : $docType",
                                'user_comment' => $row->usercomment,
                                'task_id'=> $row->taskid,
                                'task_state' => $row->state
                            ]
                    );
                dump('  Success Inserted-> composite_instance_id '.$row->composite_id);
                $counterFound++;
               
            
        }
        dump("Total doc no. flag as $docType : ".$counterFound);
        dump('Completed');
});

/**
 * To terminate instance under MR, YEP, Orphans
 * Table checking ep_bpm_task_orphan
 * 18/02/2023, 07/12/2024
 */
Artisan::command('terminate-task-selection-mryeporphan {type_terminate} {doc_type} {year} {limit} {month_no?} {terminate_flag?} {component_name?}', function () {
    $typeTerminate = $this->argument('type_terminate');
    $docType = $this->argument('doc_type');
    $year = $this->argument('year');
    $monthNo = $this->argument('month_no');
    $limit = $this->argument('limit');
    $terminateFlag = $this->argument('terminate_flag');
    $componentName = $this->argument('component_name');
    MigrateUtils::logDump(__METHOD__ . " terminate-task-selection-mryeporphan Started with param :$typeTerminate: typeTerminate, docType: $docType , year : $year, monthNo : $monthNo, limit: $limit , terminateFlag : $terminateFlag, componentName : $componentName"); 
    BPMServiceTerminationInstance::terminateTaskByMrYepOrphanSelection($typeTerminate,$docType,$year,$monthNo,$limit,$terminateFlag,$componentName);
    //BPMServiceTerminationInstance::terminateTaskByMrYepOrphanSelection($docType,$limit,$year);
    MigrateUtils::logDump(__METHOD__ . ' terminate-task-selection-mryeporphan Completed'); 
});

/**
 * Run one time to get all task ASSIGNED in BPM and flag as IS_YEP
 */
Artisan::command('bpm-task-yep-excel', function () {
    
    //$filename = '/app/Migrate/data/YEP_2019_TerminateInstance20191231.csv';
    //$filename = '/app/Migrate/data/YEP_2019_TerminateInstance20200101.csv';
    $filename = '/app/Migrate/data/MR_2020_2_TerminateInstance20200509.csv';
    Excel::load($filename, function($reader){
        $reader->each(function($row) {
            DB::connection('mysql_ep_support')
                            ->table('ep_bpm_task_orphan')->updateOrInsert(
                            [
                               'doc_no' =>$row->doc_no ,
                               'composite_id' =>$row->composite_id ,
                               'composite_module' =>$row->composite_module ,
                               'component_name' =>$row->processid 
                            ],         
                            [
                                'composite_id' => $row->composite_id,
                                'composite_module' => $row->comp_id_module,
                                'composite_id_module' => $row->comp_id_module,
                                'component_instance_id' => $row->tasknumber,
                                'composite_name' => $row->compositename,
                                'component_name' => $row->processid,
                                'component_creation_date' => Carbon::createFromFormat('d-M-y h.i.s.000000 A', $row->createddate),
                                'doc_no' =>$row->doc_no,
                                //'doc_type' => 'YEP',
                                'doc_type' => 'MR',
                                'activity_name' =>$row->activityname,
                                'created_at' =>Carbon::now(),
                                //'remarks' =>'This document number set flag as YEP for 2019. ',
                                'remarks' =>'This document number set flag as MR for 2020. ',
                            ]
                    );
        });
    });
    dd('DONE import by excel');
 });  

 
/**
 * This program will execute after successful run bpm-task-sqno , bpm-task-dono to insert record in ep_bpm_task_orphan.
 * This program will terminate instance in BPM by list in table ep_bpm_task_orphan
 */
Artisan::command('terminate-task-orphan {doc_type} {limit}', function(){
    $docType = $this->argument('doc_type');
    $limit = $this->argument('limit');
    $listTasksOrphan = DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')
                        ->where('doc_type',$docType)  // can be DO or SQ
                        ->where('is_terminate_instance',0)  // 0: available to terminate , 1: success terminate , 2: failed terminate
                        //->where('composite_module','default/Order!*******')
                        //->whereIn('component_name',['DeliveryOrderFulfilment','DOCancellation','DOModificationRequestSubmition','InvoiceAndPaymentCreation','ReSubmitOrderCancellation'])
                        //->whereIn('component_name',['ContractRequestInitiation','DeliveryOrderFulfilment','InvoiceAndPaymentCreation','PHISContractRequestInitiation','PurchaseRequestCreation'])
                        //->take(5) // set total record to get
                        //->skip(400)
                        ->take($limit)
                        ->get();
                      
    MigrateUtils::logDump('Total record found : '.count($listTasksOrphan));
    $successTerminate = 0;
    $failedTerminate = 0;
    foreach ($listTasksOrphan as $task){
        try {
            //dump($task);
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:6060/ep-support-middleware");
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/bpm/patch/instance/terminate";
            
            /*
            {
                ##### USING SEARCH DATE #####
                "compositeDN": "default/Contract_Management!1.0.3",
                "componentName": null,
                "createdDtFrom": "01-01-2018",
                "createdDtTo": "01-01-2018",
                "createdTsFrom": "00:00",
                "createdTsTo": "23:59",
                "state": "RUNNING, FAULTED, RECOVERY",
                "offset": 0,
                "limit": 50,
             
                ##### USING LIST COMPOSITE INSTANCE ID #####
                "instanceList": [
                 402753,402756,402776 --> terminate bulk instance provided by user.
                ],
                "bulkInstance": true, --> set flag to true to indicate terminate bulk instance
                "needTask": false, --> set flag to true if the instance need to check exist wftask before terminate it.
                "yep": true --> set flag to true if terminate instance for YEP.
             
            }
            */
            $arrayData = array();
            $arrayData['token'] = $token;
            $arrayData['bulkInstance'] = true;
            $arrayData['instanceList'] = array($task->composite_id);
            if($docType == 'YEP' ){
               //$arrayData['yep'] = true; 
               $arrayData['needTask'] = false; 
            }else{
               $arrayData['needTask'] = false; 
            }

            $options = ['json' => $arrayData ];
            MigrateUtils::logDump('start to terminate : '.$task->composite_id);
            MigrateUtils::logDump($options);
            //dump($url);
            $response = Guzzle::post($url, $options);
            $resultResp = json_decode($response->getBody(), true);

            /** SAMPLE RESPONSE
                {
                    "status": "Success",
                    "result": {
                        "isSuccess": false,
                        "instanceId": 0,
                        "nbSuccess": 0,
                        "nbFailed": 3,
                        "datas": [
                            {
                                "isSuccess": true,
                                "instanceId": 402753
                            },
                            {
                                "isSuccess": false,
                                "instanceId": 402756
                            },
                            {
                                "isSuccess": true,
                                "instanceId": 402776
                            }
                        ]
                    }
                }
             */
            MigrateUtils::logDump('Status terminate :-- '.json_encode($resultResp));
            //dump('Status terminate 2 :-- '.json_encode($resultResp['result']['datas'][0]['isSuccess']));
            //MigrateUtils::logDump(json_encode($resultResp));
            $isSuccessTerminate  = false;
            if($resultResp['status'] == 'Success'){
                if(json_encode($resultResp['result']['datas'][0]['isSuccess']) == 'true'){
                   $isSuccessTerminate = true; 
                }
            }
            if($isSuccessTerminate == true){
                DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')
                                ->where('composite_id',$task->composite_id)
                                ->where('composite_module',$task->composite_module)
                                ->where('component_name',$task->component_name)
                                ->where('doc_no',$task->doc_no)
                                ->update([
                                    'is_terminate_instance' => 1,
                                    'terminate_at' => Carbon::now(),
                                    'terminate_response' => json_encode($resultResp)
                                        ]);
                dump('RESULT >>> SUCCESS TERMINATE INSTANCE : '.$task->composite_id);
                $successTerminate++;

            }else{
               DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')
                                ->where('composite_id',$task->composite_id)
                                ->where('composite_module',$task->composite_module)
                                ->where('component_name',$task->component_name)
                                ->where('doc_no',$task->doc_no)
                                ->update([
                                    'is_terminate_instance' => 2,
                                    'terminate_at' => Carbon::now(),
                                    'terminate_response' => json_encode($resultResp)
                                        ]); 
                MigrateUtils::logDump('RESULT >>> FAILED! TERMINATE INSTANCE : '.$task->composite_id);
               $failedTerminate++;
            }
            //sleep(1);
            MigrateUtils::logDump('COMPLETED');
            MigrateUtils::logDump('Success Terminate : '.$successTerminate);
            MigrateUtils::logDump('Failed Terminate : '.$failedTerminate);
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            MigrateUtils::logDump($ex->getMessage());
            MigrateUtils::logDump($ex->getTraceAsString());
        }
    }

})->describe('Terminate Instance YEP or MR or Orphan Task');

Artisan::command('retrigger-igfmas-file', function(){
    $folder = $this->ask('Which folder? IN or OUT or EP-OUT : ');

    if($folder === 'IN'){
       ReTriggerFileIGFMAS::runAll_INFOLDER();
    }elseif($folder === 'OUT'){
       ReTriggerFileIGFMAS::runAll_ServerGFMAS_OUT_FOLDER();
    }elseif($folder === 'EP-OUT'){
       ReTriggerFileIGFMAS::runAll_OUTFOLDER_EP();
    }else {
        dump(' Invalid! ');
    }
})->describe('Re trigger all AP511 files  in /batch/1GFMAS/IN to re-process ');

Artisan::command('retrigger-file-in-server-gfmas-out', function(){
    $serviceCode = $this->ask('Service Code ');
    $filename = $this->ask('FileName ');

    ReTriggerFileIGFMAS::run_ServerGFMAS_OUT_FOLDER($filename, $serviceCode);
            
})->describe('Re trigger Files by service code , file name in OUT folder from IGFMAS Server ');

Artisan::command('retrigger-file-GFMAS-in-folder {service_code} {filename}', function(){
    $serviceCode = $this->argument('service_code');
    $filename = $this->argument('filename');

    ReTriggerFileIGFMAS::runIN($filename, $serviceCode);
            
})->describe('Re trigger Files by service code , file name in IN folder /batch/1GFMAS/IN ');


Artisan::command('initiate-sm-task', function(){
    $applNos = array (
     //'***********-0003',
     '***********-0002',

    );

    foreach ($applNos as $applNo){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

            $token = base64_encode(strtotime("now"));
            $url = $urlMiddleware."/ep/ejb-sm/initiate-task/?appl_no=".$applNo.'&token='.$token;

            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            dump($applNo .' :-- '.json_encode($resultResp));
            sleep(1);
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }

})->describe('Initiate task SM');

Artisan::command('initiate-order-task', function(){
    $docNoCrList = array (
        'CR240000000164000',

    );

    foreach ($docNoCrList as $crDocNo) {
        try {

            $queryCheck = DB::connection('oracle_nextgen_rpt')->select(" 
                            SELECT DISTINCT fr.fulfilment_req_id as doc_id,fr.doc_no,fr.doc_type, wf.created_date as wf_created_date
                                FROM fl_fulfilment_request fr, fl_workflow_status wf
                                WHERE fr.fulfilment_req_id = wf.doc_id(+)
                                --AND fr.DOC_TYPE='PR'
                                and fr.doc_no = ?
                                AND fr.record_status = 1
                                AND wf.DOC_ID is not null 
                                AND fr.created_date  < sysdate - interval '10' minute 
                ", array($crDocNo));

//            dump(count($queryCheck));

            if (count($queryCheck) === 0) {
                //Sample Date FORMAT must be:  2019-01-28
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
                //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

                $triggerBpm = true;
                $type = substr($crDocNo, 0, 2);

                if ($type === 'CR') {
                    $url = $urlMiddleware . "/bpm/fl/initiateContractRequest?doc_no=" . $crDocNo . "&is_trigger_bpm=" . $triggerBpm;
                } else {
                    $url = $urlMiddleware . "/bpm/fl/initiatePurchaseRequest?doc_no=" . $crDocNo . "&is_trigger_bpm=" . $triggerBpm;
                }

                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                dump($type . ' > ' . $crDocNo . ' :-- ' . $resultResp['status']);
                sleep(1);
            } else {
                dump('This Document Number already has status created');
                dump($queryCheck);
            }
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }
})->describe('Initiate task CR');

Artisan::command('initiate-deliveryorder-task', function(){
    $deliveryIdList = array (
        '20697399',
'20697390',
'20697372',
'20697368',
'20697362',
'20697354',
'20697336',
'20697323',
'20697302',
    );

    $counter = 0;
    foreach ($deliveryIdList as $deliveryId){
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

            $triggerBpm = true;

            $queryCheck = DB::connection('oracle_nextgen_rpt')->select(" 
                SELECT
                  A.DELIVERY_ORDER_NO AS dono,
                  A.DELIVERY_ORDER_ID AS doid,
                  B.STATUS_ID         AS statusid,
                  D.STATUS_NAME       AS statusname
                FROM FL_DELIVERY_ORDER A,
                  FL_WORKFLOW_STATUS B,
                  PM_STATUS_DESC D
                WHERE A.DELIVERY_ORDER_ID = B.DOC_ID
                      AND B.STATUS_ID = D.STATUS_ID
                      AND B.DOC_TYPE IN ('DO')
                      AND D.LANGUAGE_CODE = 'en'
                      AND B.IS_CURRENT = 1
                      AND A.DELIVERY_ORDER_ID = ?
                      AND b.status_id in (
                            42000  -- Acknowledgement
                          )
                ", array($deliveryId));
            
            /** To avoid duplicate refire.. If status already exist. SKIP it **/
            dump( " Checking valid to refire : total check ".count($queryCheck));
            
            if (count($queryCheck) === 0) {
                $url = $urlMiddleware . "/bpm/fl/updateDeliveryOrder?delivery_order_id=" . $deliveryId . "&is_trigger_bpm=" . $triggerBpm;
            
                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                dump($deliveryId .' :-- ',$resultResp);
                sleep(1);

                $counter++;
                if($counter % 15 == 0){
                    sleep(10);
                    dump('Do sleep 1 minutes');
                }
            }  else {
                dump('This delivery Order ID already has status created');
                dump($queryCheck);
            }  
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }

})->describe('Initiate task DO');


Artisan::command('bpm-fix-similar-activity', function () {
    $listDocNo = array(

    //'CO190000000799762',
    //'PO190000001000360'
    //    'PO190000000998510'

    );
    foreach ($listDocNo as $docNo){
        //dump('Start search docno: '.$docNo);
        $flObj = DB::connection('oracle_nextgen_rpt')->table('fl_fulfilment_order')->where('doc_no',$docNo)->first();
        if($flObj != null){
            dump('Start search docno: '.$docNo.' ,docID : '.$flObj->doc_no);
            $docIdSearch = '%'.$flObj->doc_no.'%';
            //-- $componentName = 'InvoiceAndPaymentCreation';
            $listDlv = DB::connection('oracle_bpm_rpt')->select(""
                    . "select distinct
                                (select distinct CMPST_ID from CUBE_INSTANCE ci where ci.cikey = ds.cikey) as composite_instance_id,
                                         ds.domain_name||'/' || ds.Composite_Name || '!' || ds.composite_revision as composite,
                       ds.cikey,ds.composite_name,to_char(ds.SUBSCRIPTION_DATE,'YYYY-MM-DD HH24:MI:SS') as subscription_date  , ds.state  , ds.component_name, ds.domain_name
                     from Dlv_Subscription ds
                      where
                                                ds.properties like ?
                                                -- and ds.component_name = ?
            ", array($docIdSearch));
            if(count($listDlv) > 0){
                dump( ' Found record DLV : '.count($listDlv));
                foreach($listDlv as $dlvObj){
                    if($dlvObj->composite_instance_id == null || $dlvObj->composite_instance_id = ''
                           || Carbon::parse($dlvObj->subscription_date)->lt(Carbon::createFromFormat('Y-m-d', '2019-08-29'))
                            ){
                        dump('  start patch to get CIKEY: '.$dlvObj->cikey);
                        DB::connection('oracle_nextgen_soa_fullgrant')->table('dlv_subscription')
                                ->where('cikey',$dlvObj->cikey)
                                ->update(['state' => 3]);

                        $countResSucc =  DB::connection('oracle_nextgen_soa_fullgrant')->table('dlv_subscription')
                                ->where('cikey',$dlvObj->cikey)
                                ->where('state',3)
                                ->count();

                        dump('  success patch updated: '.$countResSucc);
                    }else{
                        dump( ' SKIP!! : composite instance id is not null and more than 28/8/2019  >> CIKEY: '.$dlvObj->cikey);
                    }
                        ;
                }
            }
        }

    }
})->describe('Program to update cikey in DLV_SUBSCRIPTION as 3 ');


Artisan::command('check-health-bpm', function(){

    $remotePath = "/home/<USER>/script/data/data.txt";
    $contents = SSH::into('bpm')->getString($remotePath);
    $listHealthBPM = explode("\n", trim($contents));
    var_dump($listHealthBPM);
    
    $remotePath = "/home/<USER>/script/data/threadcount.txt";
    $contents2 = SSH::into('bpm')->getString($remotePath);
    $listThreadCountBPM = explode("\n", trim($contents2));
    var_dump($listThreadCountBPM);


})->describe('check status node instance BPM');



Artisan::command('sm-sync-role', function() {

    $personnelID = '8815372';

    try {
        //Sample Date FORMAT must be:  2019-01-28
        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
        //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

        $token = base64_encode(strtotime("now"));
        $url = $urlMiddleware . "/ep/ejb-sm/sync-role/?personnel_id=" . $personnelID . '&token=' . $token;

        $response = Guzzle::get($url);
        $resultResp = json_decode($response->getBody(), true);

        return $resultResp;
    } catch (\GuzzleHttp\Exception\ConnectException $ex) {
        dump($ex->getMessage());
        dump($ex->getTraceAsString());
    }

    dump('completed');
})->describe('sync Role SM into SSO n Liferay ');



Artisan::command('batch-check-ap511 {counter}', function(){
    $loop = $this->argument('counter');

    for($cnt=1;$cnt<=$loop;$cnt++){
        dump('counter : '.$cnt);
        $invoice =  DB::connection('mysql_ep_support')->table('ep_invoice_check')->where('is_ap511','YES')->where('is_pa_no_exist','YES')->first();
        
        if($invoice == null){
            dd(' Terminate! no record');
        }
        //$filename =  $invoice->file_name;
        try {
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");

            //foreach ($list as $invoice_check) {
                $batch = DB::connection('mysql_ep_support')->table('ep_invoice_detail')->where('invoice_no',$invoice->inv_no)->first();
                $token = base64_encode(strtotime("now"));
                dump('trigger service :  '.$batch->file_name.' -> update closed');
                $url = $urlMiddleware."/batch/au/payment/filename/?filename=".$batch->file_name.'&token='.$token;

                $response = Guzzle::get($url);
                $resultResp = json_decode($response->getBody(), true);

                //dump($resultResp);
                $actionName = 'Integration-IGFMAS-AP511-Reprocess';
                $actionTypeLog = 'Web Service';
                $parameters =  collect([]);
                $parameters->put("file_name", $batch->file_name);
                $parameters->put("action", "Request to reprocess file AP511 by batch file to update in eP");
                $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));

                $actionData =  collect([]);
                $actionData->put("file_name", $batch->file_name);
                $actionData->put("response", $resultResp);

                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $actionData, $parameters, "Completed","Batch Program");

                if($resultResp['status'] == "Success"){
                    $ids = DB::connection('mysql_ep_support')->select("select id from ep_invoice_check where inv_no in (SELECT  invoice_no FROM ep_invoice_detail WHERE  file_name = ?) AND is_ap511 = 'YES'", array($batch->file_name));
                    $collect =  collect($ids);
                    $listId = $collect->pluck('id');
                    DB::connection('mysql_ep_support')->table('ep_invoice_check')->whereIn('id',$listId)->delete();
                    dump('Total Invoice Deleted = '.count($listId));
                }

                //dump($resultResp);
                dump('File AP511: '.$batch->file_name);
            //}
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }

    }

})->describe('Check file AP511 cause of error updated in eP');

Artisan::command('create-di-interface-log', function() {

    try {
        //Sample Date FORMAT must be:  2019-01-28
        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
        //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

        $token = base64_encode(strtotime("now"));
        $url = $urlMiddleware . "/batch/diinterface/log/create";

        $options = [
                'json' => [
                    'token' => $token,
                    'counter' => 5,
                    'serviceCode' => 'GFM-140',
                    'processId' => 'AP511'
                ]
            ];

        $response = Guzzle::post($url, $options);

        $resultResp = json_decode($response->getBody(), true);

        dump($resultResp);

        return $resultResp;
    } catch (\GuzzleHttp\Exception\ConnectException $ex) {
        dump($ex->getMessage());
        dump($ex->getTraceAsString());
    }

    dump('completed');
})->describe('Create empty DI Interface Log ');


/**
 * to sync roles user ep into SSO and liferay
 */
Artisan::command('ep-sync-role-user', function() {

    //$icno = $this->ask('Please put IdentificationNo : ');
    
    $listIcno = array(
        'ckgbobenterprise',
    );
    
    $counter = 1;
    foreach ($listIcno as $icno) {
        $user = DB::connection('oracle_nextgen_rpt')->table('PM_USER')->where('login_id',$icno)
            ->where('record_status',1)->first();
    
        if($user == null){
            dd('No record found for identification no: '.$icno);
        }
        //dump($user);
        try {
            $host = '**************';
            $port = '4447';
            //Sample Date FORMAT must be:  2019-01-28
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
            //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

            //$token = base64_encode(strtotime("now"));
            $url = $urlMiddleware . "/ep/ejb-pm/sso_liferay_role/sync";
            //$url = $urlMiddleware . "/ep/ejb-pm/host/sso_liferay_role/sync?host=$host&port=$port";
            
            
            $isSupplierUser = false;
            if($user->org_type_id == 15){
                $isSupplierUser = true;
            }
            $isSupplierUser = false;
            $options = [
                    'json' => [
                        'userId' => $user->user_id,
                        'loginId' => $user->login_id,
                        'isSupplierUser' => $isSupplierUser,
                    ]
                ];
            dump($counter.') sync roles for user :'.$user->login_id);
            $response = Guzzle::post($url, $options);

            $resultResp = json_decode($response->getBody(), true);

            dump($resultResp);
            dump('....');
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
        $counter++;
    }
    

    dump('completed');
})->describe('to sync roles user ep into SSO and liferay ');

/**
 * to sync roles user ep into SSO and liferay
 */
Artisan::command('ep-sync-role-user-sm', function() {

    $personnelID = $this->ask('Please put sm personnel ID : ');
    
   

    $personnel = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL')->where('personnel_id',$personnelID)
        ->where('record_status',1)->first();

    if($personnel == null){
        dd('No record found for personnel ID with valid criteria: '.$personnelID);
    }
    //dump($user);
    try {
        //Sample Date FORMAT must be:  2019-01-28
        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
        //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

        $token = base64_encode(strtotime("now"));
        $url = $urlMiddleware . "/ep/ejb-sm/sync-role?personnel_id=$personnel->personnel_id&token=$token&";
        dump($url);
        $response = Guzzle::get($url);

        $resultResp = json_decode($response->getBody(), true);

        dump($resultResp);
        dump('....');
    } catch (\GuzzleHttp\Exception\ConnectException $ex) {
        dump($ex->getMessage());
        dump($ex->getTraceAsString());
    }

    dump('completed');
})->describe('to sync roles user SM  into SSO and liferay ');


Artisan::command('resent-out-file', function() {
    /***
    SELECT process_id,service_code,file_name, ''''||service_code||','||file_name||'''' AS c
FROM DI_INTERFACE_LOG  
WHERE file_name IN ('CompanyInfo_20210821.txt','ContactInfo_20210821.txt','GovUserInfo_20210821.txt','SupplierUserInfo_20210821.txt')
    ***/

    //$list = array(
        //'bidang2022013100.gpg',
        //'sst20240823001.gpg'
        //'1000APIVE4000012022012800503.GPG',
        //'2014PHS-20040000120241212002.EPN',
        //'2014PHS-20040000120240917012.EPN'
        //'1000APIVE4000012022012800503.GPG',
        //'1000APERR4000012022020200081.GPG'
        //'1000APIVE4000012022030300146.GPG',
        //'barang20241225001.gpg'
    //);

    
    $list = array(
       // 'GPI-060,iklan2022020301.gpg',
        //'LMS-140,GovUserInfo_20220122.txt',
        //'PHS-200,2014PHS-20040000120241212002.EPN'
       // 'GPI-070,sst20240823001.gpg'
        //'GFM-010,1000APIVE4000012024040300001.GPG'
        //'GPI-060,iklan2022032001.gpg',
        //'LMS-150,ContactInfo_20220320.txt'
        //'1102AR50240000120230121001.GPG'
        //'EPS-001,syarikat20250602001.gpg',
        // 'GPI-010,syarikat20231112005.gpg',
        //'EPS-001,syarikat20240214001.gpg',
        //'GPI-020,barang20241225001.gpg',
        //'GPI-020,barang20250601001.gpg',
        //'GPI-110,perancangan_perbelanjaan20250601001.gpg',
        //'GPI-110,perancangan_perbelanjaan20250602001.gpg',
        //'GPI-100,perancangan_perolehan20250601001.gpg',
        //'GPI-100,perancangan_perolehan20250602001.gpg',

        //'GPI-120,prestasi20250601001.gpg',
        //'GPI-120,prestasi20250602001.gpg',
        //'GPI-120,prestasi20250602002.gpg',
    );
     
    $usingCheckDB = false;
    if($usingCheckDB === true){
        foreach ($list as $fileName){
                $objFile = DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')->where('file_name',$fileName)
                    ->select('file_name','service_code')
                    ->first();
                $thCls = new ResendFileBatchOUT;
                if($objFile != null){
                    dump("found in batch-file");
                    $uuid  = \Ramsey\Uuid\Uuid::uuid4();
                    $resp = $thCls->callWSPickupFileOutFolderEP($uuid, $objFile->service_code, $fileName);
                    dump($resp);
                }else{
                    $filenameNew = str_replace('.gpg','',$fileName);
                    dump('filenameNew : '.$filenameNew);
                    $objFileIntLog = DB::connection('oracle_nextgen_rpt')->table('DI_INTERFACE_LOG')->where('file_name',$filenameNew)
                    ->select('file_name','service_code')
                    ->first();
                    if($objFileIntLog != null){
                        dump("found in interface-log");
                        dump($objFileIntLog);
                        $uuid  = \Ramsey\Uuid\Uuid::uuid4();
                        $resp = $thCls->callWSPickupFileOutFolderEP($uuid, $objFileIntLog->service_code, $fileName);
                        dump($resp);
                    }
                }
        }
    }else{
        ResendFileBatchOUT::sendByFileNamesServiceCode($list);
    }
    
    
    
})->describe('resend OUT file MyGPIS ');


/**
 * Run one time to get all task DO as ASSIGNED in BPM but this doc. no. not exist in eP Database
 */
Artisan::command('sm-updatesoftcert-issue', function () {
   $list = DB::connection('oracle_nextgen_rpt')->select("
            select distinct s.supplier_id,s.company_name,s.ep_no,s.reg_no, a.supplier_type, ret.*,
                p.identification_no,p.personnel_id, p.ep_role,
                CASE
                  WHEN a.supplier_type = 'K' THEN 'MOF_SUPPLIER_ADMIN'
                  WHEN a.supplier_type = 'J' THEN 'MOF_SUPPLIER_ADMIN'
                  WHEN a.supplier_type = 'B' THEN 'BASIC_SUPPLIER_ADMIN'
                  ELSE NULL
                END as role_convert
              from sm_supplier s, sm_appl a, sm_personnel p,
                (
                  select distinct o.remarks_1 as sofcert_request_id,
              o.remarks_2 as ic_no, o.remarks_3 as ep_no from osb_retry_dtl ret, osb_logging o
              where ret.trans_id = o.trans_id and o.trans_type = 'IBReq'
              and ret.service_name = 'UpdateSoftCert'
              and o.service_code = 'SPK-010'
                  ) ret
              where s.supplier_id =a.supplier_id
              and a.appl_id = p.appl_id
              and a.appl_id = s.latest_appl_id
              and s.record_status in (5,7) 
              and p.ep_role = 'SUPPLIER_TEMP'
              and  s.ep_no = ret.ep_no
              and  p.identification_no = ret.ic_no

            ");
        $counter = 1;
        foreach ($list as $row){
            //dump($row);
            $personnel = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL')->where('personnel_id', $row->personnel_id)->first();
            dump($counter++.') '.$personnel->personnel_id.' , '.$personnel->ep_role.' , convert to role: '.$row->role_convert. ' ,supplier_id : '.$row->supplier_id. ' ,epNo : '.$row->ep_no);
            DB::connection('oracle_nextgen_fullgrant')->table('SM_PERSONNEL')->where('personnel_id', $row->personnel_id)->update([ 'ep_role' => $row->role_convert]);
            DB::connection('oracle_nextgen_fullgrant')->table('SM_SUPPLIER')->where('supplier_id', $row->supplier_id)->update([ 'record_status' => 1,'changed_date'=> Carbon::now()->addMinutes(15)]);
           
            try {
                //Sample Date FORMAT must be:  2019-01-28
                $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");
                //http://**************:8080/ep-support-middleware/ep/ejb-sm/initiate-task/?appl_no=docno

                $token = base64_encode(strtotime("now"));
                $url = $urlMiddleware . "/ep/ejb-sm/sync-role?personnel_id=$personnel->personnel_id&token=$token&";
                //dump($url);
                $response = Guzzle::get($url);

                //$resultResp = json_decode($response->getBody(), true);

                //dump($resultResp);
                //dump('....');
            } catch (\GuzzleHttp\Exception\ConnectException $ex) {
                dump($ex->getMessage());
                dump($ex->getTraceAsString());
            }
        }
    dump('completed');
})->describe('Fixed Issue UpdateSoftcert cause of SupplierTemp Role.');


/**
 * command to execute sync SM users to create SSO and Liferay
 */
Artisan::command('sm-sync-create-user-ssoliferay', function () {

    Log::info('Entering command action: sm-sync-create-user-ssoliferay');
    try {
        
        $url = 'https://www.eperolehan.gov.my/NGeP-portlet/rest/pm/createliferay';
        Log::info(' URL trigger : '.$url);

        $client = new \GuzzleHttp\Client(["base_uri" => $url,'verify' => false]);
        $response = $client->get($url);
        

        $resultResp = json_decode($response->getStatusCode());
        dump('Status Code: '.$resultResp);
        Log::info(' Trigger Response : '.$resultResp);
        //dump('....');
    } catch (\GuzzleHttp\Exception\ConnectException $ex) {
        dump($ex->getMessage());
        dump($ex->getTraceAsString());
    }

    dump('completed');
    Log::info(' Completed: sm-sync-create-user-ssoliferay');
})->describe('command to execute sync SM users to create SSO and Liferay');


/**
 * command to execute sync SM for MOLPAY - Pending Process Payment Completed
 */
Artisan::command('sm-sync-pending-process', function () {

    Log::info('Entering command action: sm-sync-pending-process');
    try {
        
        $url = 'https://www.eperolehan.gov.my/NGeP-portlet/rest/sm/smpendingprocess';
        Log::info(' URL trigger : '.$url);
        //dump($url);
        //$client->setDefaultOption('verify', false);
        //$response = Guzzle::setDefaultOption('verify', false)->get($url);
        
        $client = new \GuzzleHttp\Client(["base_uri" => $url,'verify' => false]);
        $response = $client->get($url);
        

        $resultResp = json_decode($response->getStatusCode());
        dump('Status Code: '.$resultResp);
        Log::info(' Trigger Response : '.$resultResp);
        //dump('....');
    } catch (\GuzzleHttp\Exception\ConnectException $ex) {
        dump($ex->getMessage());
        dump($ex->getTraceAsString());
    }

    dump('completed');
    Log::info(' Completed: sm-sync-pending-process');
})->describe('command to execute sync SM for MOLPAY - Pending Process Payment Completed');



/**
 * Run one time to get all task DO as ASSIGNED in BPM but this doc. no. not exist in eP Database
 */
Artisan::command('poco-cre-adj-issue', function () {
    $begin = new DateTime('2020-01-01');
    $end = new DateTime('2020-06-05');

    $interval = DateInterval::createFromDateString('1 day');
    $period = new DatePeriod($begin, $interval, $end->add($interval));

    foreach ($period as $dt) {
        $date = $dt->format("Y-m-d");
        
        //$date  = '2020-06-04';
        $query = "select distinct a.remarks_2
                from OSB_LOGGING a, OSB_LOGGING_DTL b
                where
                  a.logging_id = b.logging_id
                and a.service_code = 'GFM-120'
                and a.trans_type = 'IBReq' 
                and trunc(a.trans_date) = to_date(?,'YYYY-MM-DD')  
                and a.trans_date > (select max(trans_date)
                                      from OSB_LOGGING
                                      where service_code = 'EPP-017'
                                            and remarks_2 = a.remarks_2
                                            and trans_type = 'OBRes' 
                                            and remarks_3 = 'N'
                                    )
                                    ";
        $query = $query . " and extractvalue(xmltype.createxml(b.payload_body), '//ActionCode', 'xmlns=";
        $query = $query .'"http://www.ep.gov.my/Schema/1-0/PaymentInstruction';
        $query = $query .'"';
        $query = $query ."') = 'CRE' ";
        //dump($query);
    
        $listRes = DB::connection('oracle_nextgen_rpt')->select($query,array($date));
        foreach ($listRes as $row){
            $docNo = $row->remarks_2;
            $query1 = "select trans_id,trans_type,trans_date,service_code,remarks_2, ";
            $query1 = $query1 . "extractvalue(xmltype.createxml(b.payload_body), '//ActionCode', 'xmlns=";
            $query1 = $query1 .'"http://www.ep.gov.my/Schema/1-0/PaymentInstruction';
            $query1 = $query1 .'"';
            $query1 = $query1 ."') as action_code ";
            $query1 = $query1 ."    from OSB_LOGGING a, OSB_LOGGING_DTL b
                where
                  a.logging_id = b.logging_id
                and a.service_code = 'GFM-120'
                and a.trans_type = 'IBReq' 
                and a.remarks_2 =  ? 
                and a.trans_date > (select max(trans_date)
                                      from OSB_LOGGING
                                      where service_code = 'EPP-017'
                                            and remarks_2 = a.remarks_2
                                            and trans_type = 'IBRes' 
                                            and remarks_3 = 'N'
                                    ) 
                
                order by trans_date desc                    ";
            
            $listRes1 = DB::connection('oracle_nextgen_rpt')->select($query1,array($docNo));
            //dump($listRes1);
            if(count($listRes1) > 0){
                $collectRes =  collect($listRes1);
                $collectRes->where('action_code', 'DEL');
                if($collectRes->where('action_code', 'DEL')->count() == 0){
                    
                    $resCheck = DB::connection('oracle_nextgen_rpt')->select("SELECT count(*) as total from fl_fulfilment_order o, fl_workflow_status w "
                            . "where o.fulfilment_order_id = w.doc_id "
                            . "and w.doc_type in ('PO','CO') "
                            . "and w.status_id in (41030,41035,41535,41530) "
                            . "and o.doc_no = ? ",array($docNo));
                    if($resCheck[0]->total == 0){
                       dump($date);
                       dump('DocNo : '.$docNo); 
                    } 
                }
            }
        }
    }
    
   
    dump('completed');
})->describe('Fixed Issue UpdateSoftcert cause of SupplierTemp Role.');



Artisan::command('check-list-apove', function () {
    $listApove = array(
        '1000APOVE4000012020072900145.GPG',
    );
    
    foreach ($listApove as $apoveFileName){
        dump('looking ... '.$apoveFileName);
        try {

            $objFile =  DB::connection('oracle_nextgen_rpt')->table('OSB_BATCH_FILE')
                ->where('FILE_NAME', $apoveFileName)
                ->orderBy('CREATED_DATE','desc')
                ->first();
            if($objFile){
               $token = base64_encode(strtotime("now"));
                $res = collect([]);
                if($objFile){
                    $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://**************:8080/ep-support-middleware");
                    $response = Guzzle::get($urlMiddleware."/batch/osb/find/idd?idd=".$apoveFileName."&service_code=".$objFile->service_code."&token=".$token);
                    $res->push(utf8_encode($response->getBody()));
                } else {
                    $res->push('Fail not found!  '.$apoveFileName);
                }

                $new_data = json_decode($res[0]); 
                //dump($new_data);
                if($new_data->result && isset($new_data->result->list)
                        ){
                    dump('ePNO : '.$new_data->result->list[0]->data[19]->value);
                }else{
                    dump('Invalid');
                }
                    
                
            }else{
                dump('Not found OSB_BATCH_FILE!!');
            }
            sleep(2);

            //dump('....');
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }
    

    dump('completed');
})->describe('Check APOVE file to get epNo');

Artisan::command('resend-request-softcert {ic_no} {softcert_request_id}', function(){
    $icNo = $this->argument('ic_no');
    $softcertRequestId = $this->argument('softcert_request_id');
    SmResendSoftcert::triggerSendSoftcertRequest($icNo,$softcertRequestId);
})->describe('Resend to request softcert digital');

Artisan::command('resend-request-softcert-failed-in-osb', function(){
    SmResendSoftcert::triggerFailedOSBRequestCertificate();
})->describe('Resend to request softcert digital after checking as failed in OSB');




Artisan::command('resend-mm501-payload', function () {
    ResendGFMASIntegrationPayload::resendMM501Payload();
})->describe('resend mm501 payload');
Artisan::command('resend-mm506-payload', function () {
    ResendGFMASIntegrationPayload::resendMM506Payload();
})->describe('resend mm506 payload');



Artisan::command('ep-mygpis-statistic', function () {
    BiMigrateMyGPIS::run();
})->describe('store hourly mygpis generated data ');


Artisan::command('prodsupport-migrate-report-perkhidmatan', function () {
    MigrateReportStatPerkhidmatanPerbendaharaan::run();
    
});

// run get default last month
Artisan::command('prodsupport-etl-rpt-stat-transaction', function () {
    MigrateReportStatTransactionEp::run();
});

// run get by specific year month
Artisan::command('prodsupport-etl-rpt-stat-transaction-param {year} {month}', function () {
    $year = $this->argument('year'); 
    $month = $this->argument('month'); 
    MigrateReportStatTransactionEp::run($year, $month);
});

Artisan::command('prodsupport-migrate-data-patching-excel', function () {
    MigrateDataPatchingExcel::run();
    
});

Artisan::command('appsupport-report-supplier-da-verify', function () {
    ReportSupplierDisciplinaryAction::run();
});

Artisan::command('appsupport-fix-reject-bumi-status {applNo}', function () {
    $applNo = $this->argument('applNo'); 
    SmRejectBumiStatusFixIssue::fixIssueRejectBumiStatusByApplNo($applNo);
});

Artisan::command('appsupport-fix-sync-mof-supplier-admin', function () {
    SmFixIssueSyncMofSupplierAdminRole::fixIssueToUpdateMofSupplierAdmin();
});

Artisan::command('fix-duplicate-role-sm-user {limit}', function () {
    //Set limit to 'null' if not necessary 
    $limit = $this->argument('limit'); 
    SmFixIssueSyncMofSupplierAdminRole::fixIssueToDeleteDuplicateRole($limit);
});

Artisan::command('fix-stuck-payment-process', function () {
    SmFixPaymentProcess::fixPaymentProcess();
});



/* Run one time to import invoice pending payment list from GFMAS into ep_payment_info_gfmas table */
Artisan::command('insert-payment-info-ap511', function () {

    $filename = '/app/Migrate/data/InvoicependingEP_2018.csv';
    dump('load file...');

    Excel::load($filename, function ($reader) {
        dump('processing...');
        $reader->sheet(0, function ($sheet) {
            $sheet->setColumnFormat(["A1:" . $sheet->getHighestColumn() . "1" => "@"]);
        });
        $reader->each(function ($row) {

            $invNo = "";
            $prNo = "";
            $sstNo = "";
            $remark = "";

            $invNo = ltrim($row->invoice_no, "/");
            $prNo = ltrim($row->pr_no, "/");
            $sstNo = ltrim($row->sst_no, "/");
            $remark = ltrim($row->remark, "/");

            $cancelationDate = null;
            $paymentDate = null;

            if (strlen($row->payment_date) > 0) {
                $paymentDate = Carbon::createFromFormat('d/m/Y', $row->payment_date);
            }

            if (strlen($row->cancelation_date) > 0) {
                $cancelationDate = Carbon::createFromFormat('d/m/Y', $row->cancelation_date);
            }

            // dump('inserting... ' . $invNo);
            DB::connection('mysql_ep_support')
                ->table('ep_payment_info_gfmas')->updateOrInsert(
                    [
                        'poco_no' => $row->poco_no,
                        'invoice_no' => $invNo
                    ],
                    [
                        'pa_no' => $row->pa_no,
                        'pr_no' => $prNo,
                        'payment_date' => $paymentDate,
                        'sst_no' => $sstNo,
                        'trans_type' => $row->trans_type,
                        'payment_amount' => $row->payment_amount,
                        'cancelation_date' => $cancelationDate,
                        'remark' => $remark,
                        'status_process' => 'PENDING',
                        'created_at' => Carbon::now()
                    ]
                );
        });
    });
    dd('DONE import by excel');
});


Artisan::command('update-ap511-manual {counter} {limit}', function () {

    $loop = $this->argument('counter');
    $limit = $this->argument('limit');
    $invoiceNo = null;
    $processStatus = 'PENDING';

    $start = microtime(true);
    for ($x = 0; $x < $loop; $x++) {
        dump('start.. '.($x+1).'/'.$loop.' for limit : '.$limit);
        try {
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://**************:8080/ep-support-middleware");

            $token = base64_encode(strtotime("now"));
            //$url = $urlMiddleware . "/batch/find/list/updatePaymentFromGfmasList?token=$token&process_status=$processStatus&limit=$limit&invoice_no=$invoiceNo";
            $host = '**************';
            $port = '4447';
            $url = $urlMiddleware . "/batch/find/list/updatePaymentFromGfmasListByHost?token=$token&process_status=$processStatus&limit=$limit&invoice_no=$invoiceNo&host=$host&port=$port";
            $response = Guzzle::get($url);
            $resultResp = json_decode($response->getBody(), true);

            dump($resultResp);

            if ($resultResp["status"] === "Invalid" || $resultResp["status"] === "Error") {
                $time_elapsed_secs = microtime(true) - $start;
                dump('Total time taken: ' . $time_elapsed_secs);
                dd("STOP! Error found.");
            }

            sleep(2);
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }
    $time_elapsed_secs = microtime(true) - $start;
    dump('Total time taken: ' . $time_elapsed_secs);
})->describe('update AP511 Manual Close');



Artisan::command('test-hola', function () {
    $list = array(
        'stdin: is not a tty',
        'Connecting to ************...',
        'Complete FTP server',
        'sftp> exit'
    );
    $coll = collect($list);

    $data = $coll->filter(function ($value, $key) {
        return ($value != 'stdin: is not a tty' && $value != 'sftp> exit');
    });

    $telnet = "";
    foreach ($data as $d) {
        $telnet .= $d . " ";
    }

    dd($telnet);
    
});

Artisan::command('export_user_requests {log_date}', function(){
    $log_date = $this->argument('log_date');
    $expsvc = new ExportLogService();
    $c_log_date = Carbon::createFromFormat('Y-m-d', $log_date);
    $expsvc->export_user_requests($c_log_date);

})->describe('Export Log pivot by user');

Artisan::command('export_cube_requests {log_date}', function(){
    try{
        $log_date = $this->argument('log_date');
        $expsvc = new ExportLogService();
        $c_log_date = Carbon::createFromFormat('Y-m-d', $log_date);
        $expsvc->export_cube_requests($c_log_date);
    } catch(Exception $e){
        dd($e);
    }

})->describe('Export Log pivot by node, url');

Artisan::command('fix_backlog_logtrace {login_id} {log_date}', function(){
    $login_id = $this->argument('login_id');
    $log_date = $this->argument('log_date');
    $lgtrcServ = new LogTraceService();
    $lgtrcServ->clearStuckLogByUserAndDate($login_id,$log_date);

})->describe('fix back log in logtrace by loginID');

/**
 * log_date  sample full format 20230627-23:53  or just date 20230627'
 */
Artisan::command('fix_backlog_logtrace_date {log_date}', function(){
    $log_date = $this->argument('log_date');
    $lgtrcServ = new LogTraceService();
    $lgtrcServ->clearStuckLogByDate($log_date);

})->describe('fix back log in logtrace by logdate , sample full format 20230627-23:53  or just date 20230627');





Artisan::command('download-qt-encrypt {remotePath} {localPath}', function () {
    $remote = $this->argument('remotePath');
    $local = $this->argument('localPath');
    SSH::into('portal')->get($remote, $local);
})->describe('download qt document');

Artisan::command('find-instance-bpm-by-docno', function () {
    $logInfo = __METHOD__ . ' Starting ... with command : find-instance-bpm-by-docno';
    $listDocNo = array('CO240000000624161','CO240000000623904');
    MigrateUtils::logDump( $logInfo .' with total docNo '.count($listDocNo));
    foreach($listDocNo as $docNo){
        $listWftask = DB::connection('oracle_bpm_rpt')->select("SELECT CUSTOMATTRIBUTESTRING1 as doc_no,flow_id, COMPOSITEINSTANCEID ,COMPOSITENAME ,
            COMPOSITEVERSION ,w.state  
            FROM WFTASK w WHERE CUSTOMATTRIBUTESTRING1 = ? and compositename='Fulfilment'
            GROUP BY flow_id, COMPOSITEINSTANCEID ,COMPOSITENAME ,COMPOSITEVERSION ,
            CUSTOMATTRIBUTESTRING1,state",array($docNo));
        $total = count($listWftask );
        if($total > 0){
        foreach($listWftask as $obj){
            MigrateUtils::logDump(json_encode($obj));
        }
        }else{
            $res = ['doc_no'=>$docNo];
            MigrateUtils::logDump(json_encode($res));
        }
        // if($total > 1){
        //     MigrateUtils::logDump( "$logInfo  >> $docNo found 2 flow ID");
        //     foreach($listWftask as $obj){
        //         MigrateUtils::logDump( " ## ".json_encode($obj));
        //     }
        // }elseif($total = 1){    
        //     MigrateUtils::logDump( "$logInfo  >> $docNo with COMPOSITEINSTANCEID : $docNo->compositeinstanceid , flowID : $docNo->flow_id");
        // }else{
        //     MigrateUtils::logDump( "$logInfo  >> $docNo not found");
        // }
        
    }
})->describe('get instance id bpm');


Artisan::command('check-tast-ct-ac-invalid', function(){
    $listWftask = DB::connection('oracle_bpm_rpt')->select("SELECT CREATEDDATE  , COMPOSITEINSTANCEID ,COMPOSITENAME ,COMPOSITEVERSION ,
    componentname ,PROCESSNAME  ,activityname,
    CUSTOMATTRIBUTESTRING1 as doc_no, STATE , CUSTOMATTRIBUTENUMBER1 as agreement_id  FROM WFTASK w WHERE 
    -- TASKNUMBER  = '420151332'
    COMPOSITENAME = 'Contract_Management' 
    AND STATE  ='ASSIGNED' 
    AND PROCESSNAME  = 'Start Agreement Creation'");

    foreach($listWftask as $row){
        $checkAgreement = DB::connection("oracle_nextgen_rpt")->table('CT_AGREEMENT')
            ->where('AGREEMENT_ID',$row->agreement_id)
            ->count();
        if($checkAgreement == 0){
            dump('composite_instance_id='.$row->compositeinstanceid. ',doc_no='.$row->doc_no.',agreement_id='.$row->agreement_id);
            
            $checkExist = DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')->where('composite_id',$row->compositeinstanceid)->count();
            if($checkExist == 0){
                DB::connection('mysql_ep_support')->table('ep_bpm_task_orphan')->insert(
                    [
                    'composite_id' => $row->compositeinstanceid,
                    'composite_name' => $row->compositename ,
                    'component_creation_date' => $row->createddate,
                    'doc_no' =>$row->doc_no,
                    'activity_name' =>$row->activityname,
                    'created_at' =>Carbon::now()
                    ]
                );
            }
            
            
        }
    }

})->describe('Checking list compositen instance with task CT AC invalid.');

Artisan::command('fetch_and_save_sm_supplier', function(){
    $sm_svc = new SmSupplierService();
    $sm_svc->fetch_and_save_sm_supplier();
})->describe('Fetch & Save SM Supplier');

Artisan::command('validate_and_update_sm_supplier {limit}', function(){
    $limit = $this->argument('limit');
    $sm_svc = new SmSupplierService();
    $sm_svc->validate_and_update_sm_supplier($limit);
})->describe('Validate & Update SM Supplier');

Artisan::command('runUpdateSupplierInternational', function(){
    $sm_svc = new SmSupplierService();
    $sm_svc->runUpdateSupplierInternational();
})->describe('Validate & Update SM Supplier');


Artisan::command('check-ap511-is-completed-process {limit}', function(){
    $limit = $this->argument('limit');
    CheckAp511ProcessIsUpdated::runAndCheckAp511($limit);
})->describe('To check Ap511 is already completed processing or not.');

Artisan::command('restart-support-middleware', function(){
    $commands  = [
        'service tomcat restart'
    ];
    SSH::into('epss-prod')->run($commands);
})->describe('Restart support middleware');

Artisan::command('razer-call-run', function(){
    ClientRazerApi::run();
    
 })->describe('Razer call');

Artisan::command('razer-call {beginDate} {endDate}', function(){
   // ClientRazerApi::run();

    
    $beginDate = $this->argument('beginDate');
    $endDate = $this->argument('endDate');

    $begin = new DateTime($beginDate);
    $end = new DateTime($endDate);

    $interval = DateInterval::createFromDateString('1 day');
    $period = new DatePeriod($begin, $interval, $end->add($interval));

    foreach ($period as $dt) {
        $date = $dt->format("Y-m-d");
        dump('Date : '.$date);
        ClientRazerApi::dailyReportTrans($date);
    }      
})->describe('Razer call');



Artisan::command('migrate-org-profile-crm', function(){
    $orgCodeList = array (
        '57110100',

    );

    foreach ($orgCodeList as $orgCode){
        try {
            $urlSync = "http://***************/rest/organization/orgcode/$orgCode";
            $response = Guzzle::get($urlSync);
            $resultResp = $response->getBody();

            dump($urlSync .' :-- '.$resultResp);
            dump("####");
            sleep(1);
        } catch (\GuzzleHttp\Exception\ConnectException $ex) {
            dump($ex->getMessage());
            dump($ex->getTraceAsString());
        }
    }

})->describe('Re-sync into CRM');

Artisan::command('download-report-receipt', function(){
    $paymentDate = '2022-05-09';
    $dateFolder = str_replace("-","",$paymentDate); 
    $list = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT DISTINCT 
                    pp.PAYMENT_ID ,pp.PAYMENT_DATE ,pp.PAYMENT_AMT ,pb.BILL_TYPE ,pb.BILL_NO ,pp.RECEIPT_NO,
                    ppo.CHANNEL_NAME , ppr.transaction_id ,ppr.TRANSACTION_DATE ,ppr.PAYMENT_STATUS 
                    FROM  py_payment pp, py_payment_order ppo,py_payment_response ppr, py_bill pb 
                    WHERE receipt_no IS NOT NULL
                    AND pp.PAYMENT_ID = ppo.PAYMENT_ID 
                    AND pp.BILL_ID  = pb.bill_id
                    AND ppo.payment_order_id = ppr.payment_order_id
                    AND pb.BILL_TYPE  IN ('P','R')
                    AND trunc(pp.payment_date) = to_date(?,'YYYY-MM-DD')",array($paymentDate));
    
    $path = storage_path('app/receipt-ep');
    $fullFolderPath = $path.'/'.$dateFolder;
    
    //Storage::makeDirectory($fullFolderPath);
    if (!file_exists($fullFolderPath)) {
        File::makeDirectory($fullFolderPath);
    }
    
    foreach($list as $rec){

        $rctNo = $rec->receipt_no;
        $fileNameReceipt = $rctNo.'_receipt.pdf';
        $filePath = $fullFolderPath.'/'.$fileNameReceipt;
        if (!file_exists($filePath)) {
            $urlSync = "http://prdfrmrpt.eperolehan.com.my:5000/reports/rwservlet?ngepprd&report=sm_payment_receipt.jsp&pmntid=$rec->payment_id&destype=cache&desformat=pdf";
            $response = Guzzle::get($urlSync, ['save_to' => $filePath]);
            $resultResp = $response->getStatusCode();
            dump($urlSync .' :-- status code'.$resultResp);
        }else{
            dump(' Existed : '.$filePath);
        }
    }
    
    $files = glob($fullFolderPath); //Get all folder n files under Path:FolderName
    $folderZipPath = storage_path('app/receipt-ep').'/zip';
    $zipPathName = $folderZipPath . '/' . $dateFolder . '.zip';
    dump('ZipPath: '.$zipPathName );
    if (file_exists($zipPathName)) {
        File::delete($zipPathName);
    }
    \Zipper::make($zipPathName)->add($files)->close();


   
})->describe('Download Receipt Payment');


Artisan::command('download-report-receipt-listed', function(){
    $dateFolder = "PATCH_CANCEL_RECEIPT_20230824"; 
    $list = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT DISTINCT p.PAYMENT_ID ,p.payment_date,p.RECEIPT_NO ,b.bill_no,
                c.cancel_receipt_no,c.REPLACE_RECEIPT_NO ,c.appl_no,c.channel_name AS CHANNEL_NAME_OLD,o.CHANNEL_NAME AS CHANNEL_NAME_NEW,
                p.SERVICE_CHARGE_GST_RATE AS CHARGE_NEW , p.CARD_TYPE AS card_TYPE_NEW, 
                (SELECT code_name FROM PM_PARAMETER_DESC  WHERE parameter_id = p.CARD_TYPE  AND LANGUAGE_code = 'en' ) AS card_TYPE_NEW_desc 
                FROM py_payment p ,PY_PAYMENT_MANUAL_CANCEL c ,py_payment_order o, py_bill b
               WHERE 
               p.PAYMENT_ID =  c.PAYMENT_ID(+)
               AND p.PAYMENT_ID  = o.PAYMENT_ID 
               AND b.BILL_ID = p.BILL_ID 
               AND p.payment_id IN
                        (776410) 
             ORDER BY p.payment_date" );
    
    $path = storage_path('app/receipt-ep');
    $fullFolderPath = $path.'/'.$dateFolder;
    
    //Storage::makeDirectory($fullFolderPath);
    if (!file_exists($fullFolderPath)) {
        File::makeDirectory($fullFolderPath);
    }
    
    foreach($list as $rec){
        // resit RC000000000509797 (KN-06102022-0149) - dibatalkan.pdf
        // resit RC000000000513540 (KR-17102022-0163) - baharu.pdf
        $rctNo = $rec->receipt_no;
        //$rctNo = $rec->cancel_receipt_no;
        $applNo = $rec->bill_no;
        //$fileNameReceipt = "resit ".$rctNo." (".$applNo.") - dibatalkan.pdf";
        $fileNameReceipt = "resit ".$rctNo." (".$applNo.") - baharu.pdf";
        $filePath = $fullFolderPath.'/'.$fileNameReceipt;
        if (!file_exists($filePath)) {
            //$baseURL = 'http://**************:5000';  //DRC
            $baseURL = 'http://prdfrmrpt.eperolehan.com.my:5000';  //PRD
            $urlSync = "$baseURL/reports/rwservlet?ngepprd&report=sm_payment_receipt.jsp&pmntid=$rec->payment_id&destype=cache&desformat=pdf";
            $response = Guzzle::get($urlSync, ['save_to' => $filePath]);
            $resultResp = $response->getStatusCode();
            dump($urlSync .' :-- status code'.$resultResp);
        }else{
            dump(' Existed : '.$filePath);
        }
    }
    
    $files = glob($fullFolderPath); //Get all folder n files under Path:FolderName
    $folderZipPath = storage_path('app/receipt-ep').'/zip';
    $zipPathName = $folderZipPath . '/' . $dateFolder . '.zip';
    dump('ZipPath: '.$zipPathName );
    if (file_exists($zipPathName)) {
        File::delete($zipPathName);
    }
    \Zipper::make($zipPathName)->add($files)->close();


   
})->describe('Download Receipt Payment');


/**
 * check ep-igfmas supplier
 */
Artisan::command('sync-ep-supplier-igfmas', function () {

    SmSupplierSyncIgfmas::syncSupplierWithIgfmasInfo();
    dump('Completed');
});

/**
 * run report ep-igfmas supplier
 */
Artisan::command('report-ep-supplier-igfmas', function () {

    SmSupplierSyncIgfmas::runReportSupplierIfgmas();
    SmSupplierSyncIgfmas::executeBankExistInEpOnly();
    dump('Completed');
});

/**
 * run get bin info card for razer
 */
Artisan::command('bin-check', function () {

    SmPaymentCardTypeSync::syncCardTypeBin();
    dump('Completed');
});
/**
 * run get bin info card for razer
 */
Artisan::command('import-bin-mbb-list', function () {

    SmPaymentCardTypeSync::importFileBinListFromMBB();
    dump('Completed');
});




/**
 * run get bin info card for razer
 */
Artisan::command('bin-net-check', function () {

    SmPaymentCardTypeSync::syncCardBinNoFromBinNet();
    dump('Completed');
});

/**
 * run get bin info card for razer
 */
Artisan::command('bin-mbb-sync', function () {

    SmPaymentCardTypeSync::syncUpdateMBBCardType();
    dump('Completed');
});


/**
 * run to fix issue razer card type
 */
Artisan::command('fix-razer-card-type {date}', function () {
    $dateFind = $this->argument('date');
    SmPaymentCardTypeSync::syncPatchingFixCardType($dateFind);
    dump('Completed');
});

/**
 * run to fix issue razer card type
 */
Artisan::command('fix-razer-card-type-prepaid {date}', function () {
    $dateFind = $this->argument('date');
    SmPaymentCardTypeSync::checkAndPatchingCardTypePrepaid($dateFind);
    dump('Completed');
});

/**
 * run report ep-igfmas supplier
 */
Artisan::command('ep-receipt-sync {date}', function () {
    $date = $this->argument('date');
    SmPaymentReceiptEpSync::syncEpSupplierPaymentReceipt($date);
    dump('Completed');
});


/**
 *  Masterdata OrgProfile - remove duplicate record_status =1 
 */
Artisan::command('remove-duplicate-org-profile-active', function () {

    PmOrgMasterDataFixDuplicate::fixIssueDuplicateRecordStatusOrgProfile();
    dump('Completed');
});



/**
 * remove select user with current task and re-assign tasks
 */
Artisan::command('reassign-task-user', function () {

    BPMReAssignTaskProgram::removeTaskReAssignedTaskList();
    dump('Completed');
});

Artisan::command('alter-flow', function () {

    BpmAlterFlow::run("Order"); // Order / Fulfilment
    dump('Completed');
});
Artisan::command('dp-alter-flow-cancel-item-codification', function () {

    BpmAlterFlow::runAlterFlowDpCancelItemCodification(); // Order / Fulfilment
    dump('Completed');
});
Artisan::command('dp-terminate-rn-codification-invalid', function () {

    BpmAlterFlow::runTerminateDpRNCodificationIsInvalid(); // Order / Fulfilment
    dump('Completed');
});



/**
 * On upgrade BPM 12c, this program will run to capture error log instance to get status doc in eP
 */
Artisan::command('sync-err-bpm-12c-get-status-doc', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : reassign-task-user');
    BPMUpgrade12c::syncErrorBpmWithStatusInEp();
    MigrateUtils::logDump(__METHOD__ . ' Completed');
});

/**
 * On after upgrade BPM 12c
 */
Artisan::command('sync-err-bpm-12c-sync', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... find payload');
    //BPMUpgrade12c::findPayloadCT();
    BPMUpgrade12c::reupdateTaskStatus();
    MigrateUtils::logDump(__METHOD__ . ' Completed');
});



/**
 * Issue on TG failed to send request softcert. WIP data will update to DG and resend to DG provider
 */
Artisan::command('resend-failed-tg-to-dg', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : resend-failed-tg-to-dg');
    SmResendSoftcert::triggerUpdateFailedTGToDG();
    MigrateUtils::logDump(__METHOD__ . ' Completed ... with command : resend-failed-tg-to-dg');
});


/**
 * Issue on TG failed to send request softcert. WIP data will update to DG and resend to DG provider
 */
Artisan::command('resend-request-softcert-tg-to-dg-all {dateFind}', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : resend-request-softcert-tg-to-dg-all');
    $dateFind = $this->argument('dateFind');
    $listReqSoftcert = DB::connection('oracle_nextgen_rpt')->select("SELECT s.company_name,s.ep_no,p.name,p.identification_no,p.is_softcert,u.record_status, 
        u.softcert_request_id,u.softcert_provider
        from sm_personnel p, SM_SOFTCERT_REQUEST u, sm_supplier s
        where p.user_id = u.user_id
        and p.appl_id = s.latest_appl_id
        AND trunc(u.CREATED_DATE) = to_date(?,'YYYY-MM-DD')
        AND u.SOFTCERT_PROVIDER  = 'TG'
        and p.is_softcert in (3) ", array($dateFind));
    MigrateUtils::logDump(__METHOD__ . 'Total found : '.count($listReqSoftcert) );
    foreach($listReqSoftcert as $req){
        $icNo = $req->identification_no;
        $softcertRequestId = $req->softcert_request_id;
        SmResendSoftcert::sendSwitchTgToDgSoftcertReq($icNo,$softcertRequestId);
    }
    
    MigrateUtils::logDump(__METHOD__ . ' Completed ... with command : resend-request-softcert-tg-to-dg-all');
});


/**
 * Issue on TG failed to send request softcert. WIP data will update to DG and resend to DG provider
 */
Artisan::command('resend-request-softcert-tg-to-dg {icNo} {softcertRequestId}', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : resend-request-softcert-tg-to-dg');
    $icNo = $this->argument('icNo');
    $softcertRequestId = $this->argument('softcertRequestId');
    SmResendSoftcert::sendSwitchTgToDgSoftcertReqByOneCompanyOnly($icNo,$softcertRequestId);
    MigrateUtils::logDump(__METHOD__ . ' Completed ... with command : resend-request-softcert-tg-to-dg');
});


Artisan::command('bpm-qt-refire-migrate-12c', function () {

    BpmQtRefireInstance::runRefireQTExistingInstance(); 
    dump('Completed');
});

Artisan::command('bpm-qt-terminate-cancelled-status', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-qt-terminate-cancelled-status');
    BpmStuckRunningInstance::runSourcingQtCancelledToTerminateTask(); 
    MigrateUtils::logDump(__METHOD__ . ' Completed');
});


Artisan::command('bpm-fl-stuck-running-fix', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-fl-stuck-running-fix');
    BpmStuckRunningInstance::runFulfilmentStuckRunning(); 
    dump('Completed');
});
Artisan::command('bpm-order-stuck-running-fix', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-order-stuck-running-fix');
    BpmStuckRunningInstance::runOrderStuckRunning(); 
    dump('Completed');
});

Artisan::command('bpm-sm-stuck-running-fix', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-sm-stuck-running-fix');
    BpmStuckRunningInstance::runSupplierManagementStuckRunning(); 
    dump('Completed');
});
Artisan::command('bpm-qt-stuck-running-fix', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-qt-stuck-running-fix');
    BpmStuckRunningInstance::runSourcingQtStuckRunning(); 
    dump('Completed');
});
Artisan::command('bpm-dp-stuck-running-fix', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : bpm-dp-stuck-running-fix');
    BpmStuckRunningInstance::runDirectPurchaseStuckRunning(); 
    dump('Completed');
});

Artisan::command('osb-igfmas-pmq-resend {docNo}', function () {
    $docNo = $this->argument('docNo');
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : osb-igfmas-pmq-resend with doc Number" '.$docNo);
    OsbRefireIgfmasPaymentMatchQuery::resendPaymentMatchQueryByDocNo($docNo); 
    dump('Completed');
})->describe('checking stuck PO/CO 	Pending Payment Instruction Query from 1GFMAS , then remove duplicate and resend again EPP-017 by specific docno');

Artisan::command('osb-igfmas-pmq-resend-all', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : osb-igfmas-pmq-resend-all" ');
    OsbRefireIgfmasPaymentMatchQuery::resendStuckPaymentMatchQuery(); 
    dump('Completed');
})->describe('checking stuck PO/CO 	Pending Payment Instruction Query from 1GFMAS , then remove duplicate and resend again EPP-017');

Artisan::command('osb-correlation-duplicate-remove {year} {month}', function () {
    $year = $this->argument('year');
    $month = $this->argument('month');
    MigrateUtils::logDump(__METHOD__ . " Starting ... with command : osb-correlation-duplicate-remove  $year $month");
    OsbRefireIgfmasPaymentMatchQuery::removeDuplicateOsbCorrelation($year, $month); 
    dump('Completed');
})->describe('remove duplicate osb_correlation by bulk list');

Artisan::command('osb-correlation-duplicate-remove-docno {docno}', function () {
    $docNo = $this->argument('docno');
    MigrateUtils::logDump(__METHOD__ . " Starting ... with command : osb-correlation-duplicate-remove-docno  $docNo");
    OsbRefireIgfmasPaymentMatchQuery::removeDuplicateOsbCorrelationDocNo($docNo); 
    //OsbRefireIgfmasPaymentMatchQuery::resendPaymentInstructionsQueryEpp017($docNo);
    dump('Completed');
})->describe('remove duplicate osb_correlation and resend again. payload will auto get the latest epp-017');


Artisan::command('osb-correlation-delete-invalid {docno}', function () {
    $docNo = $this->argument('docno');
    MigrateUtils::logDump(__METHOD__ . " Starting ... with command : osb-correlation-delete-invalid  $docNo");
    OsbRefireIgfmasPaymentMatchQuery::deleteInvalidDocNoOsbCorrelation($docNo); 
    dump('Completed');
})->describe('delete osb_correlation by doc no. no need to use it');

Artisan::command('osb-correlation-delete-invalid-bulk {year}', function () {
    $year = $this->argument('year');
    MigrateUtils::logDump(__METHOD__ . " Starting ... with command : osb-correlation-delete-invalid-bulk $year ");
    OsbRefireIgfmasPaymentMatchQuery::deleteAllInvalidDocNoOsbCorrelation($year); 
    dump('Completed');
})->describe('delete osb_correlation by doc no. no need to use it');


Artisan::command('osb-frn-failed-kod-item-resend {date}', function () {
    $dateSearch = $this->argument('date');
    MigrateUtils::logDump(__METHOD__ . " Starting ... with command : osb-frn-failed-kod-item-resend $dateSearch ");
    // OsbResendMM504ErrorKodItemFrn::resendFRNFailedCauseKodItemCubaSemula($dateSearch); 
    dump('Completed');
})->describe('Get list error business FRN cause of Kod Item Cuba Semula.. then resend back');


//stuck task qt verify loa 
Artisan::command('stuck-verify-loa-param', function () { 
    // $loginId = $this->argument('loginId');
    MigrateUtils::logDump(__METHOD__ . " Starting update stuck task qt verify loa");
    BpmStuckVerifyLoa::runStuckVerifyLoa(); 
    dump('Completed');
})->describe('find first 20 stuck verify loa and fix it');

Artisan::command('check-spki-service', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : check-spki-service');
    CheckSpkiServiceAvailibility::run(); 
    dump('Completed');
});

Artisan::command('ep-visitor-count', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : ep-visitor-count');
    ImageGeneratorVisitorCountEP::run(); 
    dump('Completed');
});


Artisan::command('ep-sync-ep-role-all', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : ep-sync-ep-role-all');
    PmUserSyncRoleWithSsoLiferay::fixIssueToSyncRoleEpWithSsoLiferay(); 
    MigrateUtils::logDump('ep-sync-ep-role-all Compleeted');
});

Artisan::command('ep-supplier-apive-issue-skip-scheduler', function () {
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : ep-supplier-apive-issue-skip-scheduler');
    SmSupplierResendApiveDueToSchedulerSkip::reUpdateChangeDateSmSupplierWithinRange1PM(); 
    MigrateUtils::logDump('ep-supplier-apive-issue-skip-scheduler Completed');
});

Artisan::command('ep-manual-inactive-user {verify-code}', function () {
    $verifyCode = $this->argument('verify-code');
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : ep-manual-inactive-user');

    if($verifyCode == "APPROVED"){
        $list = DB::connection('oracle_nextgen_fullgrant')
                ->select("SELECT u.LOGIN_ID ,u.USER_ID ,uo.USER_ORG_ID  FROM 
                pm_user u ,pm_user_org uo 
                WHERE u.USER_ID  = uo.user_id 
                AND u.LOGIN_ID IN ('940504145036','871218146410','950202146454','791223025437','890412065378',
                '891028026045','880518525249','911221145586','790424025287',
                '950608045450','761023017013','910920105570','940526035436',
                '880823065808','931120145630','870913295296')" );

        foreach($list  as $usr){
            MigrateUtils::logDump(' user set inactive >> '. json_encode($usr));
            DB::connection('oracle_nextgen_fullgrant')->table('pm_user_org')->where('user_id',$usr->user_id)->update(['record_status'=>0]);
            DB::connection('oracle_nextgen_fullgrant')->table('pm_user_role')->where('user_org_id',$usr->user_org_id)->update(['record_status'=>0]);
        }
    }else{
        MigrateUtils::logDump('verify code is not valid');
    }
    MigrateUtils::logDump('ep-manual-inactive-user Completed');
});


Artisan::command('ep-sync-user-liferay {verify-code}', function () {
    $verifyCode = $this->argument('verify-code');
    MigrateUtils::logDump(__METHOD__ . ' Starting ... with command : ep-sync-user-liferay');

    if($verifyCode == "APPROVED"){
        $list = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT * FROM (
                    SELECT tmp.* , (total_role_ep-total_role_liferay) AS diff_role 
                    FROM (
                        SELECT u.user_id,u.LOGIN_ID ,u.USER_NAME ,u.EMAIL ,
                        u.identification_no,u.org_type_id,uo.org_profile_id,
                        (SELECT max(login_date) FROM pm_login_history plh WHERE plh.user_id =u.user_id ) last_login_date,
                        count(ur.ROLE_CODE) AS total_role_ep  ,
                        ( 
                        SELECT count(*)
                        FROM User_@EPLFY u 
                        INNER JOIN UserGroupRole@EPLFY ON u.userId = UserGroupRole.userId 
                        INNER JOIN Role_@EPLFY r ON UserGroupRole.roleId = r.roleId 
                        WHERE u.screenName = u.login_id
                        ) AS total_role_liferay
                        
                        FROM pm_user u, pm_user_org uo, pm_user_role ur 
                        WHERE u.USER_ID  = uo.USER_ID  
                        AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                        AND u.RECORD_STATUS = 1 
                        AND uo.RECORD_STATUS = 1
                        AND ur.RECORD_STATUS = 1
                        GROUP BY  u.user_id,u.LOGIN_ID ,u.USER_NAME ,u.EMAIL,u.identification_no,u.org_type_id,uo.org_profile_id 
                    ) tmp
                    ) tmp2 
                    WHERE tmp2.diff_role <> 0" );

        MigrateUtils::logDump('Get Result: '.json_encode($list));
    }else{
        MigrateUtils::logDump('verify code is not valid');
    }
    MigrateUtils::logDump('ep-sync-user-liferay Completed');
});  

Artisan::command('osb-service-retry {createdDate} {target} {serviceName} {limit} {fromDateTime?} {toDateTime?}', function () {
    // osb-service-retry 2025-05-06 1GFMAS FulfillmentReceivingNote 10 
    // osb-service-retry 2025-05-06 1GFMAS PaymentInstruction 10
    // osb-service-retry 2025-05-06 1GFMAS POContractForGoodsAndServices 500
    $createdDate = $this->argument('createdDate'); 
    $target = $this->argument('target'); 
    $serviceName = $this->argument('serviceName');
    $limitRecord = $this->argument('limit');  
    $fromDateTime=$this->argument('fromDateTime'); 
    $toDateTime=$this->argument('toDateTime'); 
    GFMASController::handleOsbServiceRetry($createdDate,$target,$serviceName,$limitRecord,$fromDateTime,$toDateTime);
    MigrateUtils::logDump(__METHOD__ . ' Completed');
})->describe('OSB Service Retry by target name and service name');

Artisan::command('osb-service-retry-ago {target} {serviceName} {agoCode} {limit?} {docNo?}', function () {
    $target = $this->argument('target'); 
    $serviceName = $this->argument('serviceName');
    $agoCode = $this->argument('agoCode'); 
    $limit = $this->argument('limit');
    $docNo = $this->argument('docNo'); 
    GFMASController::handleOsbServiceRetryByAgo($target,$agoCode,$serviceName,$limit,$docNo);
    MigrateUtils::logDump(__METHOD__ . ' Completed');
})->describe('OSB Service Retry by Service Name and AGO');

Artisan::command('stl-qt-evaluation', function () { 
    QtMissingEvaluationTask::run();
    MigrateUtils::logDump(__METHOD__ . ' Completed');
})->describe('Check STL QT and refire/alter instance for missing bpm task');


/**
 * Get list of supplier branch active but not have sap_vendor_code. The program will set changed_date as currentdatetime to retrigger scheduler send file APIVE to 1GFMAS 
 */
Artisan::command('resend-apive-branch-sap-vendor-code-empty', function () {
    MigrateUtils::logDump(__METHOD__ . ' Start resend-apive-branch-sap-vendor-code-empty');
    $updater = new SmSupplierUpdateChangedDateToToday();
    $updater->resendApiveForSupplierBranchSapVendorCodeNull();
    MigrateUtils::logDump(__METHOD__ . ' Completed resend-apive-branch-sap-vendor-code-empty');
});

Artisan::command('sso-ws-change-password {login_id} {password} {notify} {verify_code}', function(){
    $loginId = $this->argument('login_id');
    $password = $this->argument('password');
    $notify = $this->argument('notify');
    $verifyCode = $this->argument('verify_code');
    if($verifyCode == 'OK'){
        MigrateUtils::logDump(__METHOD__ . "  loginId:$loginId  password:$password notify:$notify verifyCode:$verifyCode");
        $resp = SsoApiService::callUserChangePassword($loginId,$password,$notify);
        MigrateUtils::logDump(__METHOD__ . " Completed. Result: ".json_encode($resp) );
    }else{
        MigrateUtils::logDump(__METHOD__ . ' Not Valid!');
    }
})->describe('change password user in sso');

Artisan::command('ep-sso-sync-change-password-blast-user {batch_no} {verify_code}', function () {
    $commandlog = 'ep-sso-sync-change-password-blast-user';
    MigrateUtils::logDump( $commandlog . ' Starting ... ');
    $verifyCode = $this->argument('verify_code');
    $batchUser = $this->argument('batch_no');
    if($verifyCode == 'PROCEED'){

        $listLoginChangePassword = DB::connection('mysql_cms')
                ->select("SELECT s.usr_login  from cdccms.ep_org_sso_user s,
                cdccms.ep_blast_user_password_exp e 
                where 
                s.usr_login = e.user_login
                and s.usr_pwd_expire_date is not null
                and  e.blast_batch = '$batchUser'
                and e.is_change_password is null");
        
        foreach($listLoginChangePassword as  $obj){
            DB::connection('mysql_cms')->table('cdccms.ep_blast_user_password_exp')
            ->where('user_login',$obj->usr_login)
            ->update([
                'is_change_password' => 1,
                'changed_date' => Carbon::now()
            ]);
        }
        MigrateUtils::logDump( $commandlog . ' Done sync user already change password . total: '.count( $listLoginChangePassword));
    }
});

Artisan::command('ep-sso-sync-reset-password {limit} {verify_code}', function () {
    $commandlog = 'ep-sso-sync-reset-password';
    MigrateUtils::logDump( $commandlog . ' Starting ... ');
    $verifyCode = $this->argument('verify_code');
    $limitUser = $this->argument('limit');
    if($verifyCode == 'PROCEED'){

        $batchUser = 'BATCH_NO_1';
        $listLoginChangePassword = DB::connection('mysql_cms')
                ->select("SELECT s.usr_login  from cdccms.ep_org_sso_user s,
                cdccms.ep_blast_user_password_exp e 
                where 
                s.usr_login = e.user_login
                and s.usr_pwd_expire_date is not null
                and  e.blast_batch = '$batchUser'
                and e.is_change_password is null");
        
        foreach($listLoginChangePassword as  $obj){
            DB::connection('mysql_cms')->table('cdccms.ep_blast_user_password_exp')
            ->where('user_login',$obj->usr_login)
            ->update([
                'is_change_password' => 1,
                'changed_date' => Carbon::now()
            ]);
        }
        MigrateUtils::logDump( $commandlog . ' Done sync user already change password');
        MigrateUtils::logDump( $commandlog . ' start reset password user');
       
        if(!$limitUser || intval($limitUser) <= 0){
            $limitUser = 1;
        }
            
        $listLoginuser = DB::connection('mysql_cms')
                ->select("SELECT * from (
            select distinct s.usr_login,s.usr_pwd_expire_date, u.last_login_date  from cdccms.ep_org_sso_user s,
            cdccms.ep_blast_user_password_exp e ,  cdccms.ep_org_profile_user u 
            where 
            s.usr_login = e.user_login
            and u.login_id = s.usr_login 
            and e.is_change_password is null
            and  e.blast_batch = 'BATCH_NO_1'
            and u.role_status = 1
            order by u.last_login_date 
            ) tmp limit 0,$limitUser");

        $passwordTemp = 'P@55t3MP2025';
        $notify= 'false';
        foreach($listLoginuser as  $obj){
            MigrateUtils::logDump( $commandlog . ' reset password user: '.$obj->usr_login);
            $resp = SsoApiService::callUserChangePassword($obj->usr_login,$passwordTemp,$notify);
            MigrateUtils::logDump(__METHOD__ . " Completed. Result: ".json_encode($resp) );

            DB::connection('mysql_cms')->table('cdccms.ep_blast_user_password_exp')
            ->where('user_login',$obj->usr_login)
            ->update([
                'is_change_password' => 1,
                'changed_date' => Carbon::now(),
                'remarks' => 'Auto Program Change Password'
            ]);
        }
        MigrateUtils::logDump( $commandlog . ' done completed reset password user . total user : '.count($listLoginuser));
    }else{
        MigrateUtils::logDump( $commandlog . ' done not valid!');
    }
});  


/***
 * Issue on 26/6/2025 
 * Error on 
 * Error: Evaluation failed: TypeError: Cannot read properties of undefined (reading 'serialize')
    at window.WWebJS.getMessageModel 
 * Actually sent is success but response return code error. 
 * Temporary - do simple auto update status as is_sent = 1 to make sure no redundant message in notification.
 * 
 * Fix this issue on 30/6/2025 ->reupdate version puppeteer 1.31.0
 */
Artisan::command('ws-notify-auto-fix-error', function () { 
    $commandlog = 'ws-notify-auto-fix-error';
    MigrateUtils::logDump( $commandlog . ' Starting ... ');
    $listErrorNoti = DB::connection('mysql_notify')
                ->select("SELECT * from ep_notify.notification n 
                where  n.sent = 0 
                and status_desc = 'Error sending message' 
                and status = 2");
    foreach($listErrorNoti as  $obj){
        MigrateUtils::logDump( $commandlog . ' check to update as is_sent=1... '.json_encode($obj));
        DB::connection('mysql_notify')->table('ep_notify.notification')
            ->where('id',$obj->id)
            ->update([
                'sent' => 1,
                'date_modified' => Carbon::now()
            ]);
    }
    MigrateUtils::logDump( $commandlog . ' Completed ... ');

})->describe('Auto remove whatapp api error');

