<!-- MODAL: SPKI SOFTCERT -->
@if($xml)
    <div class="col-sm-12">
        <div class="block">
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{$xml->trans_type}} | ({{$xml->service_code}}) | {{$xml->trans_id}}</h2>
            </div>
            @if($xml->status == 'F' && $xml->service_code == 'SPK-020' )
            <p class="text-danger bolder">Sila hubungi Technical Support, Transaction WS ini gagal dihantar ke AP Portal SPKI. 
                <br />
                OSB Service Retry secara manual perlu dibuat untuk hantarkan transaction ini ke AP Portal SPKI.
                <br />
                <a target="_blank" href="{{url('/find/osb/log/')}}/{{$xml->remarks_3}}" style="color:darkblue" >Clik here to check details error</a> 
            </p>
            @endif
            
            @if($xml->status == 'F' && $xml->service_code == 'SPK-020' )
            <p class="text-danger bolder">Sila hubungi Technical Support, Transaction WS ini gagal dihantar ke AP Portal SPKI. 
                <br />
                OSB Service Retry secara manual perlu dibuat untuk hantarkan transaction ini ke AP Portal SPKI.
                <br />
                <a target="_blank" href="{{url('/find/osb/log/')}}/{{$xml->remarks_3}}" style="color:darkblue" >Clik here to check details error</a> 
            </p>
            @endif
            
            @if($xml->status_desc == 'Service successfully sent to JMS queue for retry')
            <p class="text-danger bolder">Sila hubungi Technical Support, Transaction WS ini gagal dikemaskinikan. 
                <br />
                OSB Service Retry secara manual perlu dibuat untuk kemaskini transaction ini.
                <br />
            </p>
            
            @endif
            
            
            @if($xml->remarks_1 != 'NA')
            <h6 class="text-info bolder"> DATA INFO </h6>
            <p>{{$xml->remarks_1}}, {{$xml->remarks_2}}, {{$xml->remarks_3}}</p>
            @endif
            
            <h6 class="text-info bolder"> DESCRIPTION RESPONSE TRANSACTION </h6>
            <pre class="line-numbers">
                <code class="language-markup">{{$xml->payload_body}}</code>
            </pre>
            
            <h6 class="text-info bolder"> DATA REQUEST TRANSACTION  </h6>
            <pre class="line-numbers">
                <code class="language-markup">{{$xml->ibreq->payload_body}}</code>
            </pre>
        </div>
    </div>
    @else
    <div class="">
        <div class="block-title">
            <h2><i class="fa fa-ban"></i> <strong>NOT FOUND</strong></h2>
        </div>
    </div>
@endif