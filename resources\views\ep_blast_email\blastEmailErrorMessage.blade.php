@extends('layouts.guest-dash')

@section('content')
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> Error Message</strong></h1>
        </div>
        <form class="form-horizontal form-bordered patch-form" id="patch-form"
            action="{{ url('/ep/blast/email/error/message') }}" method="post">
            {{ csrf_field() }}
            <div class="form-group">
                <label class="col-md-1 text-right" for="date">Date</label>
                <div class="col-md-2 date">
                    <input id = 'searching_date' name="searching_date" type="date"
                        value="{{ $date1 }}" class="form-control">
                </div>
                <div class="form-actions form-actions-button text-right buttonSearch" style="width: 1000px;">
                    <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
                </div>
            </div>
        </form>
        <div class="table-responsive">
            <table id="search_list_datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Campaign Id</th>
                        <th class="text-center">Campaign Name</th>
                        <th class="text-center">Is Completed</th>
                        <th class="text-center">Scheduled Date</th>
                        <th class="text-center">Error Message</th>
                    </tr>
                </thead>
                @if ($getListErrorMessage != null)
                    <tbody>
                        @foreach ($getListErrorMessage as $list)
                            <tr>
                                <td class="text-center">{{ $list->campaign_id }}</td>
                                <td class="text-center">{{ $list->campaign_name }}</td>
                                <td class="text-center">{{ $list->is_completed }}</td>
                                <td class="text-center">{{ $list->scheduled_date }}</td>
                                <td class="text-center">{{ $list->error_message }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                @endif
            </table>
        </div>

    </div>
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
                TablesDatatables.init();
            });
            App.datatables();
            $('#search_list_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
    </script>
@endsection
