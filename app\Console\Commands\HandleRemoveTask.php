<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\EpSupportActionLog;
use App\Services\Traits\OrganizationService;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use App\Services\Traits\SupplierFullGrantService;

class HandleRemoveTask extends Command
{
    use OrganizationService;
    use SupplierFullGrantService;

    protected $signature = 'patch-inprogress-remove-task';
    protected $description = 'Patch in progress remove task to status 8';

    public function handle()
    {
        $databaseConnection = 'oracle_nextgen_fullgrant';
        $tableName = 'PM_PENDING_REMOVE_TASK';
        $actionTypeLog = 'Script';
        $actionName = 'PatchInProgressRemoveTask';
        $logs = collect([]);

        $primaryField = 'PM_PENDING_REMOVE_TASK_ID';
        
        $updateScriptFields = ['record_status' => 8];
        $this->info('Starting Checking Process Remove Task Monitoring'); 

        try {
            $pendingTasks = $this->getDetailedPendingRemoveTasks(1);
            $total = count($pendingTasks); 

            if($total > 0) {
                try {
                    foreach($pendingTasks as $tasks) {
                        $primaryValue = $tasks->pm_pending_remove_task_id;
                        $this->saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields, $logs);

                        $parameters =  collect([]);
                        $parameters->put("remarks", request()->remarks);
                        $parameters->put("table", $tableName);
                        $parameters->put("reference_id", array($primaryField => $primaryValue));
                        $parameters->put("update_data", $updateScriptFields);
                        $parameters->put("patching", $tableName);

                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,'Completed','Scheduler');
                    } 
                    
                } catch (\Exception $e) {
                    Log::error('Error updating PM_PENDING_REMOVE_TASK_ID table: ' . $e->getMessage());
                    throw $e;
                }
            } 
        } catch (\Exception $e) {
            $this->error(__CLASS__ . $e->getMessage());
            Log::error(__CLASS__ . $e->getMessage());
        }
    } 

    public function saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,&$logs,$addWheres = null){
        $logQuery = $this->updateRecordByTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,$addWheres);
        $logs->put('action_patch_update_table',$logQuery);
    }
}