<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SourcingService;
use App\Services\Traits\BPMService;
use App\Services\Traits\BpmApiService;
use App\Services\Traits\PayloadGeneratorService;
use App\EpSupportActionLog;

class QtMissingEvaluationTask {

    use SourcingService;
    use BPMService;
    use BpmApiService;
    use PayloadGeneratorService;

    /**
     * BPM Status: Quotation / Tender Closed
     * Tracking Diary Status: Pending Confirmation Evaluation Committee / Pending Confirmation Opening Committee
     */
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(__METHOD__ . ' Starting ... ');
        $cls = new QtMissingEvaluationTask;

        $proceedFindInstance = false;
        $proceedRefire = false;
        $totalAll = 0;
        $totalAlter = 0;
        $totalRefire = 0;
        
        $list = $cls->getListStuctTaskQt();
        foreach ($list as $obj) {
            $qtNo = $obj->qt_no; 

            $task = $cls->getTaskDetailBpmByDocNo($qtNo);
            if ($task != null) {
                $workflowStatus = $obj->status_name;
                $trackingStatus = $obj->tracking_diary_status_name;
                if($workflowStatus === 'Quotation / Tender Closed') {
                    $evaluationType = ''; 
                    $subProcessId = '';
                    $comitteeStatus = '';
                    
                    if($trackingStatus === 'Pending Confirmation Evaluation Committee') {
                        $evaluationType = 'EC';
                        $subProcessId = 'ACT111166854504671';
                    }else if($trackingStatus === 'Pending Confirmation Opening Committee'){
                        $evaluationType = 'OC';
                        $subProcessId = 'ACT11116685450467';
                    } 

                    if($evaluationType != '' && $subProcessId != ''){
                    //check committee status
                        $committeeList = $cls->getListAllCommitteeMembersQT($qtNo);
                        foreach($committeeList as $list) {
                            if($list->member_code = $evaluationType && $list->statuscmt === 'Completed') {
                                $comitteeStatus = $list->statuscmt;
                                $proceedFindInstance = true;
                            }
                        }
                        if($proceedFindInstance = true) {
                            dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Status: ' .$workflowStatus .' > Tracking Status: ' .$trackingStatus . ' > Committee Status: ' .$comitteeStatus);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Status: ' .$workflowStatus .' > Tracking Status: ' .$trackingStatus. ' > Committee Status: ' .$comitteeStatus);
                             
                            $findTasks = collect($cls->getTaskBpmByDocNo($qtNo));
                            $listInstances = $findTasks->where('processname','QuotationTenderCreation')->first();
                            $listTask = $cls->findAPIProcessManagerBPMByInstance($listInstances->compositeinstanceid);
                            dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Composite Instance ID: ' .$listInstances->compositeinstanceid);
                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Composite Instance ID: ' .$listInstances->compositeinstanceid);
                            if ($listTask["status"] != null && $listTask["status"] === 'Success') {
                                $listdata = $listTask["result"];
                                if (isset($listdata["childs"])) {
                                    foreach ($listdata["childs"] as $child1) {
                                        if ($child1["componentType"] == 'bpmn' && $child1["componentName"] == 'StartQuotationTenderCreation' && $child1["componentState"] == 5) {
                                            if (isset($child1["childs"])) {
                                                foreach ($child1["childs"] as $child2) {
                                                    if ($child2["componentType"] == 'bpmn' && $child2["componentName"] == 'QuotationTenderCreation' && $child2["componentState"] == 5) {
                                                        if (isset($child2["childs"])) {
                                                            if(in_array('SiteVisitAndProposal', array_column($child2["childs"], 'componentName'))){
                                                                //check for running evaluation process
                                                                foreach ($child2["childs"] as $child3) {
                                                                    //option to alter running process
                                                                    if ($child3["componentType"] == 'bpmn' && $child3["componentName"] == 'SiteVisitAndProposal' && $child3["componentState"] == 5) {
                                                                        if (isset($child3["childs"])) {
                                                                            foreach ($child3["childs"] as $child4) {
                                                                                if ($child4["componentType"] == 'bpmn' && $child4["componentName"] == 'Evaluation') {
                                                                                    dump($child4);
                                                                                    if($child4["componentState"] == 1){
                                                                                        if (isset($child4["childs"])) {
                                                                                            dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > SKIP!!. Childs Exists');
                                                                                            Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > SKIP!!. Childs Exists');
                                                                                        }else{ 
                                                                                            $processId = $child4["componentInstanceId"];
                                                                                            $sourceId = ''; $sourceName= ''; $targetId = ''; $targetName = 'Subprocess1';
                                                                                            $listAlterFlow = $cls->alterflowBpmnProcessId($processId);
                                                                                            if ($listAlterFlow["status"] != null && $listAlterFlow["status"] === 'Success') {
                                                                                                $list = $listAlterFlow["result"];
                                                                                                foreach($list as $list) {
                                                                                                    if(isset($list["sources"])){
                                                                                                        $sourceId = $list["sources"][0]["id"];
                                                                                                        $sourceName = $list["sources"][0]["name"];
                                                                                                    }
                                                                                                    // if(isset($list["targets"])){
                                                                                                    //     foreach($list["targets"] as $target) {
                                                                                                    //         dump($target["id"]);
                                                                                                    //         if($target["id"] == $subProcessId) { 
                                                                                                                
                                                                                                                $targetId = $subProcessId; 
                                                                                                    //         }
                                                                                                    //     }
                                                                                                    // }                                    
                                                                                                }
                                                                                                dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Process ID: ' .$processId .' > Source ID: ' .$sourceId . ' > Source Name: ' .$sourceName);
                                                                                                dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' Process ID: ' .$processId .' > Target ID: ' .$targetId . ' > Target Name: ' .$targetName);
                                                                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Process ID: ' .$processId .' > Source ID: ' .$sourceId . ' > Source Name: ' .$sourceName);
                                                                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Process ID: ' .$processId .'  > Target ID: ' .$targetId . ' > Target Name: ' .$targetName);
                                                                                                if($sourceId != '' && $targetId != '') {
                                                                                                    $submit = $cls->submitAlterflowBpmnProcessId($processId, $sourceId, $targetId);
                                                                                                    if ($submit['status'] == 'Success') {
                                                                                                        dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Success Alter Flow');
                                                                                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Success Alter Flow');
                                                                                                        $totalAlter = $totalAlter + 1;
                                                                                                        sleep(10); 
                                                                                                        $findInstanceBpmAPI = $cls->findAPITaskBPMListDocAndModule($qtNo, 'SourcingQT');                                            
                                                                                                        if($findInstanceBpmAPI && $findInstanceBpmAPI["status"] === 'Success' &&  $findInstanceBpmAPI["status"] != null){
                                                                                                            $listTaskResult = collect($findInstanceBpmAPI['result']);
                                                                                                            $taskCommittee = $listTaskResult->where('state','ASSIGNED')->where('docNumber',$qtNo)->first();
                                                                                                            dump($taskCommittee);
                                                                                                            Log::info($taskCommittee);                                                                                                
                                                                                                        }
                                                                                                    }else {
                                                                                                        dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Failed Alter Flow');
                                                                                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Failed Alter Flow'); 
                                                                                                    }
                                                                                                }
                                                                                            } else {
                                                                                                dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Failed Get List Alter Flow');
                                                                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Failed Get List Alter Flow'); 
                                                                                            }
                                                                                        }
                                                                                    }else {
                                                                                        dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > SKIP!!. Please Check > Evaluation Activity State: ' .$child4["componentState"] .' > Composite Instance: '. $listInstances->compositeinstanceid);
                                                                                        Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > SKIP!!. Please Check > Evaluation Activity State: ' .$child4["componentState"].' > Composite Instance: '. $listInstances->compositeinstanceid);
                                                                                    }
                                                                                } 
                                                                            }
                                                                        }
                                                                    } 
                                                                }
                                                            }else {
                                                                //trigger using service manager
                                                                $name = 'SourcingQT';
                                                                $process = 'Evaluation';
                                                                $trigger = 'prestart';
                                                              
                                                                $scEvaluationPayload = $cls->getQtScEvaluation($qtNo);
                                                                $payload = str_replace("&", "&amp;", $scEvaluationPayload[0]->evaluation_payload);
                                                                $newPayload = '<SC_Evaluation_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_Evaluation_Data">'.$payload;
                                                                $taskPerformer = $scEvaluationPayload[0]->task_performer;
                                                                $elements = [
                                                                    ['name'=>'SC_Evaluation_Data','value'=>$newPayload,'type'=>'object'],
                                                                    ['name'=>'document_number','value'=>$qtNo,'type'=>'string'],
                                                                    ['name'=>'task_performer','value'=>$taskPerformer,'type'=>'string'],
                                                                    ['name'=>'reevaluate','value'=>'false','type'=>'boolean'],
                                                                   ];
                                                                
                                                                dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Refire');
                                                                Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Refire');
                                                                $createServiceManagerAPI = $cls->findApiBPMCreateServiceManager($name,$process,$trigger,$elements);
                                                                if($createServiceManagerAPI && $createServiceManagerAPI["status"] === 'Success' && $createServiceManagerAPI["status"] != null){
                                                                    dump(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Success Refire');
                                                                    Log::info(__CLASS__ . ' > ' .__FUNCTION__ . ' > QT: ' .$qtNo . ' > Success Refire');
                                                                    $totalRefire = $totalRefire + 1;
                                                                    sleep(10);
                                                                    $findInstanceBpmAPI = $cls->findAPITaskBPMListDocAndModule($qtNo, 'SourcingQT');                                            
                                                                    if($findInstanceBpmAPI && $findInstanceBpmAPI["status"] === 'Success' &&  $findInstanceBpmAPI["status"] != null){
                                                                        $listTaskResult = collect($findInstanceBpmAPI['result']);
                                                                        $taskCommittee = $listTaskResult->where('state','ASSIGNED')->where('docNumber',$qtNo)->first();
                                                                        dump($taskCommittee);
                                                                        Log::info($taskCommittee);
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }                                    
                                    }
                                }
                            } 
                        }
                    } 
                } 
            }         
        }
        $totalAll = $totalAlter + $totalRefire; 
        Log::info($totalAll);  
        $actionTypeLog = 'Web Service';
        $actionName = 'BPM-Alter-Refire-QT-Evaluation';

        $parameters =  collect([]);
        $parameters->put("total_alter", $totalAlter);  
        $parameters->put("total_refire", $totalRefire);  
        $parameters->put("total_all", $totalAll);  
        $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$parameters,$parameters,'Completed');
                           
        MigrateUtils::logDump(__METHOD__ . ' Completed ');
    }
}