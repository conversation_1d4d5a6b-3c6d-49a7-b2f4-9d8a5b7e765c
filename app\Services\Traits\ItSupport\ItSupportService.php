<?php

namespace App\Services\Traits\ItSupport;

use DB;

trait ItSupportService
{

    protected function listAllDataLook()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('ep_status_checklist')
            ->get();
    }

    protected function listDataLook($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('ep_status_checklist')
            ->where('esc_id', $Id)
            ->first();
    }

    protected function listTaskJob($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('esc_job_task')
            ->where('task_id', $Id)
            ->first();
    }

    protected function getMenu()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct esc_group from ep_status_checklist", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listSummaryJobDetails()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('esc_job_day_summary')
            ->get();
    }

    protected function getSummaryJobDetails($id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('esc_job_day_summary')
            ->where('job_day_id', $id)
            ->first();
    }

    protected function getHour($date, $shift)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct time_interval from ep_it_support.esc_job_task 
                            where task_date = ? 
                            and shift_interval = ?", array($date, $shift));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getLatestTestEp()
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select login_date, login_id 
                            from pm_login_history ph, pm_user pu
                            where pu.user_id = ph.user_id
                            and login_id = '357-02006140-1'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklistHourHeader($group, $date, $shift, $interval)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct ejt.task_time , ejt.time_format, ejt.shift_interval,ejt.task_date
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ? 
                            and ejt.shift_interval = ? 
                            and ejt.time_interval = ? ", array($group, $date, $shift, $interval));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklistHourHeaderAfter8PMandBefore12AM($group, $date, $shift, $interval)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct ejt.task_time , ejt.time_format, ejt.shift_interval, ejt.task_date
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            AND task_date = ? and ejt.task_time between 8 and 11 and time_format = 'PM'
                            and ejt.shift_interval = ? 
                            and ejt.time_interval = ? ", array($group, $date, $shift, $interval));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklistHourHeaderAfter12AMandBefore8AM($group, $yesterday, $interval, $group2, $date, $interval2)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct ejt.task_time , ejt.time_format, ejt.shift_interval, ejt.task_date 
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            AND task_date =  ?
                            and task_time between 8 and 11 
                            and time_format = 'PM'
                            and ejt.shift_interval = 'E' 
                            and ejt.time_interval = ?
                            union select distinct ejt.task_time , ejt.time_format, ejt.shift_interval, ejt.task_date
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ?  and time_format = 'AM'
                            and ejt.shift_interval = 'E' 
                            and ejt.time_interval =  ? ", array($group, $yesterday, $interval, $group2, $date, $interval2));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklist($group, $date, $shift, $interval)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc.esc_id ,esc.esc_seq ,ejt.task_name, esc.esc_group_sub 
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ? 
                            and ejt.shift_interval = ? 
                            and ejt.time_interval = ? ", array($group, $date, $shift, $interval));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklistAfter8PMandBefore12AM($group, $date, $shift, $interval)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc.esc_id ,esc.esc_seq ,ejt.task_name, esc.esc_group_sub 
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            AND task_date = ? and ejt.task_time between 8 and 11 and time_format = 'PM'
                            and ejt.shift_interval = ? 
                            and ejt.time_interval = ? ", array($group, $date, $shift, $interval));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getChecklistAfter12AMandBefore8AM($group, $yesterdayDate, $interval, $group2, $date, $interval2)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc.esc_id ,esc.esc_seq ,ejt.task_name, esc.esc_group_sub
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            AND task_date = ?
                            and task_time between 8 and 11 
                            and time_format = 'PM'
                            and ejt.shift_interval = 'E' 
                            and ejt.time_interval = ?
                            union select distinct esc.esc_id ,esc.esc_seq ,ejt.task_name, esc.esc_group_sub
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ? and time_format = 'AM'
                            and ejt.shift_interval = 'E' 
                            and ejt.time_interval =  ? ", array($group, $yesterdayDate, $interval, $group2, $date, $interval2));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getTaskId($date, $shift, $time, $id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct task_id
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and task_date = ? 
                            and ejt.shift_interval = ?
                            and ejt.task_time = ?
                            and ejt.esc_id = ? ", array($date, $shift, $time, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getTaskIdForYesterday($yesterdayDate, $time, $id, $date, $time2, $id2)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct task_id
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and task_date = ?
                            and ejt.shift_interval = 'E'
                            and time_format = 'PM'
                            and task_time between 8 and 11
                            and ejt.task_time = ?
                            and ejt.esc_id = ?
                            union 
                            select distinct task_id
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and task_date = ? 
                            and ejt.shift_interval = 'E'
                            and time_format = 'AM'
                            and ejt.task_time = ?
                            and ejt.esc_id =  ? ", array($yesterdayDate, $time, $id, $date, $time2, $id2));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCheckedList($getid)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc.esc_group, task_id ,esc.esc_seq ,ejt.task_name, esc.esc_group_sub , task_done, ejt.task_changed_by , ejt.task_changed_date , ejt.task_time, ejt.time_format, ejt.check_type, ejt.task_login_id, ejt.task_last_login_date
                            from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                            where esc.esc_id = ejt.esc_id 
                            and esc_status = 'active'
                            and task_done = 'done'
                            and ejt.task_id = ?", array($getid));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getHistoryMenu($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc.esc_group, task_date 
                        from ep_it_support.esc_job_task ejt , ep_it_support.ep_status_checklist esc 
                        where esc.esc_id = ejt.esc_id 
                        and task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getGroupSubLoginTest($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc_group_sub from ep_it_support.ep_status_checklist where esc_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getGroupTask($id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select esc_group from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
           where esc.esc_id = ejt.esc_id 
           and ejt.task_id = ? ", array($id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDocStatus($date, $group, $shift)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select * from ep_it_support.esc_job_task ejt, ep_it_support.ep_status_checklist esc 
            where esc.esc_id = ejt.esc_id 
            and task_date = ?
            and esc_group = ?
            and shift_interval = ? ", array($date, $group, $shift));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getRemarksDetails($id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select * from ep_it_support.esc_job_task ejt where task_id = ? ", array($id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    //    server checklist
    protected function listAllServerDataLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('ep_server_checklist')
            ->get();
    }

    protected function listServerDataLookUp($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('ep_status_checklist')
            ->where('esc_id', $Id)
            ->first();
    }

    protected function listServerJobTask($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('esc_server_job_task')
            ->where('task_id', $Id)
            ->first();
    }

    protected function getMenuServer()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct esc_group from ep_server_checklist where esc_location ='NETMYNE'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerHistory($date, $date1)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct  esc_group  
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'Inactive'
        and task.task_status = 'active'
        and esc_location ='NETMYNE'
        and task.task_date = ?
        union
         select distinct  esc_group 
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'active'
        and task.task_status = 'active'
        and esc_location ='NETMYNE'
          and task.task_date = ?", array($date, $date1));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerHelang()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct esc_group from ep_server_checklist where esc_location ='HELANG'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerHelangHistory($date, $date1)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct  esc_group
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'Inactive'
        and task.task_status = 'active'
        and esc_location ='HELANG'
        and task.task_date = ?
        union
         select distinct  esc_group 
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'active'
        and task.task_status = 'active'
        and esc_location ='HELANG'
          and task.task_date = ?", array($date, $date1));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerKvdc()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct esc_group from ep_server_checklist where esc_location ='KVDC'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerKvdcHistory($date, $date1)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct  esc_group
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'Inactive'
        and task.task_status = 'active'
        and esc_location ='KVDC'
        and task.task_date = ?
        union
         select distinct  esc_group 
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'active'
        and task.task_status = 'active'
        and esc_location ='KVDC'
          and task.task_date = ?", array($date, $date1));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getMenuServerKvdcActive()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct  esc_group 
        from ep_it_support.esc_server_job_task task , ep_it_support.ep_server_checklist list 
        where task.esc_id = list.esc_id 
        and esc_status  = 'active'
        and task.task_status = 'active'
        and esc_location ='KVDC'", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDetailsListServer()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from ep_server_checklist", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listBySearchingGroup($group, $date, $shift, $location)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from ep_it_support.ep_server_checklist esc, esc_server_job_task esjt 
                where esc.esc_id = esjt.esc_id 
                and esjt.task_status = 'active'
                and esc_group = ?
                and task_date = ?
                and shift_interval =  ?
                and esc_location =  ?", array($group, $date, $shift, $location));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerChecklist($group, $date, $id, $shift)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct task_id, esc.esc_id ,esc.esc_seq ,esjt.task_name,esc.esc_sub_group
                            from ep_it_support.ep_server_checklist esc , ep_it_support.esc_server_job_task esjt 
                            where esc.esc_id = esjt.esc_id 
                            and esjt.task_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ?  
                            and esc.esc_id = ? 
                            and shift_interval =  ?", array($group, $date, $id, $shift));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
    protected function getServerChecklistBySubGroup($group, $date, $id, $shift, $sub)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct task_id, esc.esc_id ,esc.esc_seq ,esjt.task_name,esc.esc_sub_group
                            from ep_it_support.ep_server_checklist esc , ep_it_support.esc_server_job_task esjt 
                            where esc.esc_id = esjt.esc_id 
                            and esjt.task_status = 'active'
                            and esc.esc_group = ?
                            and task_date = ?  
                            and esc.esc_id = ? 
                            and shift_interval =  ?
                            and esc.esc_sub_group = ?", array($group, $date, $id, $shift, $sub));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerTaskId($date, $id, $shift)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct task_id, checking, if_amber_light, task_remarks
                            from ep_it_support.ep_server_checklist esc , ep_it_support.esc_server_job_task esjt 
                            where esc.esc_id = esjt.esc_id 
                            and esjt.task_status = 'active'
                            and task_date = ? 
                            and esjt.esc_id = ? 
                            and shift_interval =  ?", array($date, $id, $shift));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getGroupSubServer($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct esc_sub_group from ep_it_support.ep_server_checklist where esc_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    //dba morning checklist
    protected function listAllDBAMorningDataLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('dba_data_lookup')
            ->get();
    }

    protected function dbaListDataLook($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('dba_data_lookup')
            ->where('dba_data_lookup_id', $Id)
            ->first();
    }

    protected function getCategoryName()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select  distinct dba_data_lookup_category_name from dba_data_lookup", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByCategory($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct dba_data_lookup_name from dba_data_lookup where dba_data_lookup_category_name = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByCategoryAndDate($group, $date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from dba_morning_checklist where dba_morning_checklist_category = ? and dba_morning_checklist_date = ?", array($group, $date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getDbaMorningTaskId($date, $id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from dba_morning_checklist where dba_morning_checklist_date = ? and dba_morning_checklist_task_id = ?", array($date, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function updateDbaMorningTask($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('dba_morning_checklist')
            ->where('dba_morning_checklist_id', $Id)
            ->first();
    }

    protected function getDbaMorningTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from dba_morning_checklist where dba_morning_checklist_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select * from ep_it_support.esc_server_job_task esjt where task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getStatusChecklistTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select * from ep_it_support.esc_job_task ejt where task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkStatus($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_checklist where netlist_task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkStatusTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT 
            SUM(IF(netlist_checked_kvdc = 'P' OR netlist_checked_failed_kvdc = 'F', 1, 0)) AS kvdc_count,
            SUM(IF(netlist_checked_star = 'P' OR netlist_checked_failed_star = 'F', 1, 0)) AS star_count,
            SUM(IF(netlist_checked_heitech = 'P' OR netlist_checked_failed_heitech = 'F', 1, 0)) AS heitech_count,
            SUM(IF(netlist_checked_wisma = 'P' OR netlist_checked_failed_wisma = 'F', 1, 0)) AS wisma_count
        FROM network_checklist 
        WHERE netlist_task_date = ?
    ", [$date]);

        if (!empty($query)) {
            $counts = (array) $query[0];
            $totalCount = array_sum($counts);
            return $totalCount;
        }

        return null;
    }

    protected function getListNotes($date, $module)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from ep_it_support.ep_acknowledge_status where ack_date = ? and ack_module = ?  ", array($date, $module));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBox($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT * FROM dba_morning_checklist 
        WHERE (dba_morning_checklist_status_ok = 'Y' 
        OR dba_morning_checklist_status_failed = 'Y')
        AND dba_morning_checklist_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxServer($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select esjt.* from ep_it_support.ep_server_checklist esc, ep_it_support.esc_server_job_task esjt 
                where esc.esc_id = esjt.esc_id 
                and esjt.checking = 'Y'
                and task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxStatus($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select ejt.* from ep_it_support.ep_status_checklist esc , ep_it_support.esc_job_task ejt 
                where esc.esc_id = ejt.esc_id 
                and ejt.task_done = 'done'
                and task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxNetworkStatus($date, $location)
    {

        $column = '';
        switch ($location) {
            case 'K':
                $column = 'kvdc';
                break;
            case 'S':
                $column = 'star';
                break;
            case 'H':
                $column = 'heitech';
                break;
            case 'W':
                $column = 'wisma';
                break;
            default:
                break;
        }
        $allLocationsData = [];

        if ($column !== '') {
            $query = DB::connection('mysql_ep_it_support')->select("
                SELECT * FROM ep_it_support.network_checklist 
                WHERE (netlist_checked_$column = 'P' OR netlist_checked_failed_$column = 'F')
                AND netlist_task_date = ?
            ", [$date]);

            if (count($query) > 0) {
                return $query;
            } else {
                $locations = ['K', 'S', 'H', 'W'];
                foreach ($locations as $loc) {
                    if ($loc !== $location) {
                        $col = '';
                        switch ($loc) {
                            case 'K':
                                $col = 'kvdc';
                                break;
                            case 'S':
                                $col = 'star';
                                break;
                            case 'H':
                                $col = 'heitech';
                                break;
                            case 'W':
                                $col = 'wisma';
                                break;
                        }
                        if ($col !== '') {
                            $query = DB::connection('mysql_ep_it_support')->select("
                                SELECT * FROM ep_it_support.network_checklist 
                                WHERE (netlist_checked_$col = 'P' OR netlist_checked_failed_$col = 'F')
                                AND netlist_task_date = ?
                            ", [$date]);
                            if (count($query) > 0) {
                                $allLocationsData[$loc] = $query;
                            }
                        }
                    }
                }
            }
        }
        return $allLocationsData;
    }

    protected function listAllNetworkDataLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('network_data_lookup')
            ->get();
    }

    protected function networkListDataLook($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('network_data_lookup')
            ->where('net_data_id', $Id)
            ->first();
    }

    protected function networkWismaListDataLook($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('amtek_data_lookup')
            ->where('am_data_id', $Id)
            ->first();
    }

    protected function getNetworkGroup()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct net_data_group from network_data_lookup", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkYear()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct YEAR(netlist_task_date) AS year from ep_it_support.network_checklist order by year asc", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByCategoryNetwork($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct net_data_name from network_data_lookup where net_data_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListBySubCategoryNetwork($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct netlist_task_subgroup from ep_it_support.network_checklist where netlist_task_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByNetworkCategoryAndDate($group, $date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_checklist where netlist_task_group = ? and netlist_task_date = ?", array($group, $date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkTaskId($date, $id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_checklist where netlist_task_date = ? and netlist_task_id = ?", array($date, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listAllNetworkPerformDataLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('network_perform_data_lookup')
            ->get();
    }

    protected function networkPerformListDataLook($Id)
    {
        return DB::connection('mysql_ep_it_support')
            ->table('network_perform_data_lookup')
            ->where('netper_data_id', $Id)
            ->first();
    }

    protected function getNetworkPerformGroup()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct netper_data_group from network_perform_data_lookup", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getPerformanceYear()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct YEAR(netperlist_task_date) AS year from ep_it_support.network_perform_checklist order by year asc", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByCategoryNetworkPerform($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct netper_data_name from network_perform_data_lookup where netper_data_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByNetworkPerformCategoryAndDate($group, $date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_perform_checklist where netperlist_task_group = ? and netperlist_task_date = ?", array($group, $date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkTaskIdPerform($date, $id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_perform_checklist where netperlist_task_date = ? and netperlist_task_id = ?", array($date, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkPerformStatusTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT 
            SUM(IF(netperlist_checked_kvdc = 'P' OR netperlist_checked_failed_kvdc = 'F', 1, 0)) AS kvdc_count,
            SUM(IF(netperlist_checked_star = 'P' OR netperlist_checked_failed_star = 'F', 1, 0)) AS star_count,
            SUM(IF(netperlist_checked_heitech = 'P' OR netperlist_checked_failed_heitech = 'F', 1, 0)) AS heitech_count,
            SUM(IF(netperlist_checked_wisma = 'P' OR netperlist_checked_failed_wisma = 'F', 1, 0)) AS wisma_count
        FROM network_perform_checklist 
        WHERE netperlist_task_date = ?
    ", [$date]);

        if (!empty($query)) {
            $counts = (array) $query[0];
            $totalCount = array_sum($counts);
            return $totalCount;
        }

        return null;
    }

    protected function getNetworkPerformStatus($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from network_perform_checklist where netperlist_task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxNetworkPerformStatus($date, $location)
    {
        $column = '';
        switch ($location) {
            case 'K':
                $column = 'kvdc';
                break;
            case 'S':
                $column = 'star';
                break;
            case 'H':
                $column = 'heitech';
                break;
            case 'W':
                $column = 'wisma';
                break;
            default:
                break;
        }
        $allLocationsData = [];

        if ($column !== '') {
            $query = DB::connection('mysql_ep_it_support')->select("
                SELECT * FROM ep_it_support.network_perform_checklist 
                WHERE (netperlist_checked_$column = 'P' OR netperlist_checked_failed_$column = 'F')
                AND netperlist_task_date = ?
            ", [$date]);

            if (count($query) > 0) {
                return $query;
            } else {
                $locations = ['K', 'S', 'H', 'W'];
                foreach ($locations as $loc) {
                    if ($loc !== $location) {
                        $col = '';
                        switch ($loc) {
                            case 'K':
                                $col = 'kvdc';
                                break;
                            case 'S':
                                $col = 'star';
                                break;
                            case 'H':
                                $col = 'heitech';
                                break;
                            case 'W':
                                $col = 'wisma';
                                break;
                        }
                        if ($col !== '') {
                            $query = DB::connection('mysql_ep_it_support')->select("
                                SELECT * FROM ep_it_support.network_perform_checklist 
                                WHERE (netperlist_checked_$col = 'P' OR netperlist_checked_failed_$col = 'F')
                                AND netperlist_task_date = ?
                            ", [$date]);
                            if (count($query) > 0) {
                                $allLocationsData[$loc] = $query;
                            }
                        }
                    }
                }
            }
        }
        return $allLocationsData;
    }

    protected function listAllAmtekDataLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('amtek_data_lookup')
            ->get();
    }

    protected function getNetworkAmtekGroup()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct am_data_group from amtek_data_lookup", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListByNetworkAmtekCategoryAndDate($group, $date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from amtek_checklist where amlist_task_group = ? and amlist_task_date = ? order by am_seq_id asc", array($group, $date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkAmtekTaskId($date, $id)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from amtek_checklist where amlist_task_date = ? and amlist_task_id = ?", array($date, $id));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListBySubCategoryNetworkAmtek($group)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select distinct amlist_task_subgroup from ep_it_support.amtek_checklist where amlist_task_group = ? ", array($group));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkAmtekStatusTaskNo($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT 
            SUM(IF(amlist_checked_kvdc = 'P' OR amlist_checked_failed_kvdc = 'F', 1, 0)) AS kvdc_count,
            SUM(IF(amlist_checked_star = 'P' OR amlist_checked_failed_star = 'F', 1, 0)) AS star_count,
            SUM(IF(amlist_checked_heitech = 'P' OR amlist_checked_failed_heitech = 'F', 1, 0)) AS heitech_count,
            SUM(IF(amlist_checked_wisma = 'P' OR amlist_checked_failed_wisma = 'F', 1, 0)) AS wisma_count
        FROM amtek_checklist 
        WHERE amlist_task_date = ?
    ", [$date]);

        if (!empty($query)) {
            $counts = (array) $query[0];
            $totalCount = array_sum($counts);
            return $totalCount;
        }

        return null;
    }

    protected function getNetworkAmtekStatus($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from amtek_checklist where amlist_task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxNetworkAmtekStatus($date, $location)
    {
        $column = '';
        switch ($location) {
            case 'K':
                $column = 'kvdc';
                break;
            case 'S':
                $column = 'star';
                break;
            case 'H':
                $column = 'heitech';
                break;
            case 'W':
                $column = 'wisma';
                break;
            default:
                break;
        }
        $allLocationsData = [];

        if ($column !== '') {
            $query = DB::connection('mysql_ep_it_support')->select("
                SELECT * FROM ep_it_support.amtek_checklist 
                WHERE (amlist_checked_$column = 'P' OR amlist_checked_failed_$column = 'F')
                AND amlist_task_date = ?
            ", [$date]);

            if (count($query) > 0) {
                return $query;
            } else {
                $locations = ['K', 'S', 'H', 'W'];
                foreach ($locations as $loc) {
                    if ($loc !== $location) {
                        $col = '';
                        switch ($loc) {
                            case 'K':
                                $col = 'kvdc';
                                break;
                            case 'S':
                                $col = 'star';
                                break;
                            case 'H':
                                $col = 'heitech';
                                break;
                            case 'W':
                                $col = 'wisma';
                                break;
                        }
                        if ($col !== '') {
                            $query = DB::connection('mysql_ep_it_support')->select("
                                SELECT * FROM ep_it_support.amtek_checklist 
                                WHERE (amlist_checked_$col = 'P' OR amlist_checked_failed_$col = 'F')
                                AND amlist_task_date = ?
                            ", [$date]);
                            if (count($query) > 0) {
                                $allLocationsData[$loc] = $query;
                            }
                        }
                    }
                }
            }
        }
        return $allLocationsData;
    }

    protected function getNetworAmtekkYear()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct YEAR(amlist_task_date) AS year from ep_it_support.amtek_checklist order by year asc", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerYear()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct YEAR(task_date) AS year from ep_it_support.esc_server_job_task order by year asc", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerGroup()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                   select distinct esc_group from ep_it_support.ep_server_checklist esc ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getServerSubGroup()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                   select distinct esc_sub_group from ep_it_support.ep_server_checklist esc ", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function listNetworkBackUpLookUp()
    {
        return DB::connection('mysql_ep_it_support')
            ->table('network_backup_lookup')
            ->get();
    }

    protected function getListTaskWisma($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                     select * from ep_it_support.network_backup_list_day where nbld_task_date = ? and nbl_location = 'WISMA CDC'  ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getListTask($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                     select * from ep_it_support.network_backup_list_day where nbld_task_date = ? and nbl_location <> 'WISMA CDC'  ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkBackupTotalTask($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from ep_it_support.network_backup_list_day  WHERE nbld_task_date = ? and nbl_location <> 'WISMA CDC'", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkBackupWismaTotalTask($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
                    select * from ep_it_support.network_backup_list_day  WHERE nbld_task_date = ? and nbl_location = 'WISMA CDC'", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxNetworkBackup($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT * FROM ep_it_support.network_backup_list_day  
        WHERE (nbld_checked_ok = 'Y' 
        OR nbld_checked_not_ok = 'N')
        and nbl_location <> 'WISMA CDC'
        AND nbld_task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getCountCheckBoxNetworkBackupWisma($date)
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        SELECT * FROM ep_it_support.network_backup_list_day  
        WHERE (nbld_checked_ok = 'Y' 
        OR nbld_checked_not_ok = 'N')
        and nbl_location = 'WISMA CDC'
        AND nbld_task_date = ? ", array($date));
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }

    protected function getNetworkBackupYear()
    {
        $query = DB::connection('mysql_ep_it_support')->select("
        select distinct YEAR(nbld_task_date) AS year from ep_it_support.network_backup_list_day order by year asc", array());
        if (count($query) > 0) {
            return $query;
        }
        return null;
    }
}
