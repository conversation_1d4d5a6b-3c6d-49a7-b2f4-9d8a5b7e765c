@extends('layouts.guest-dash')

@section('content')

<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                 <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li>
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                 <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                 <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div> 

<form class="form-horizontal form-bordered port-form" action="{{url('prod-support/data-patching')}}" method="post"  >
    {{ csrf_field() }}
    <input name="_method" id="_method" type="hidden" value="POST">
    <!--lunch port-->

    <div class="block block-alt-noborder full dateform">
        <div class="form-group" >
            <div class="col-md-4 date-port">
                <label class="col-md-4 control-label" for="date_porting">Datepicker</label>
                <div class="col-md-6">
                    <input readonly="" id="date_porting" name="date_porting" type="text" required class="form-control" value="{{$getPatchByScheduled?Carbon\Carbon::parse($getPatchByScheduled->datetime_porting)->format('Y-m-d'):Carbon\Carbon::now()->format('Y-m-d')}}"  style="width: 230px;">
                </div>
            </div>

            <div class="col-md-4 date-time-port">
                <label class="col-md-4 control-label" for="exampletimepicker">Time Porting</label>
                <div class="col-md-6">
                    <div class="input-group">
                        <input readonly="" type="text" id="time_porting" name="time_porting" class="form-control" value = "{{$getPatchByScheduled?Carbon\Carbon::parse($getPatchByScheduled->datetime_porting)->format('H:i:s'):Carbon\Carbon::now()->format('H').':00:00'}}" style="width: 230px;">
                    </div>
                </div>
            </div>
        </div>
        @if(isset($getPatchByScheduled))
        <div class="form-group">
            <div class="col-md-4 status-port" @if(!$getPatchByScheduled) style = 'display:none' @endif>
                 <label class="col-md-4 control-label" for="statusport"><i class="fa fa-pencil-square"></i> Status : </label>

                <div class="col-md-6">
                    <input readonly="" type="text" id="statusport" name="statusport" class="form-control" value= "{{$getPatchByScheduled->status}}" style="width: 230px;">
                </div>

            </div>
            <div class="col-md-4 createdby" @if(!$getPatchByScheduled) style = 'display:none' @endif>
                 <label class="col-md-4 control-label" for="createdport"><i class="fa fa-pencil-square"></i> Created by : </label>
                <div class="col-md-6">
                    <input readonly="" type="text" id="createdport" name="createdport" class="form-control" value = "{{$getPatchByScheduled->created_by}}" style="width: 230px;">
                </div>
            </div>
        </div>
        @endif


        <div class="form-group">
            @if(isset($getPatchByScheduled))
            @if($getPatchByScheduled->status == 'Open' || $getPatchByScheduled->status == 'Submit')
            <input id="datafixid" name="datafixid" type="hidden" value="{{$getPatchByScheduled->data_fix_id}}" class="form-control">
            <button type="button" class="btn btn-sm right btn-danger cancel-port"><i class="fa fa-close"></i> CANCEL PORTING</button>
            @endif
            @if($getPatchByScheduled->status == 'Open' )
            <button type="button" class="btn btn-sm btn-primary add-new-list"><i class="fa fa-save"></i> Add New List</button>
            @endif
            @if($getPatchByScheduled->status == 'Submit')
            <button type="button" class="btn btn-sm btn-primary cancel-submit"><i class="fa fa-save"></i> CANCEL SUBMIT</button>
            @endif
            @endif
            @if(!$getPatchByScheduled || ($getPatchByScheduled->status == 'Closed' && $getPatchByScheduled->is_sent == 1) || $getPatchByScheduled->status == 'Cancelled' )
            <button type="button" class="btn btn-sm right btn-primary add-new-seq"><i class="fa fa-save"></i> ADD NEW PORTING</button>
            @endif
        </div>
    </div>

    <div id="modal_confirm_newport" class="modal fade">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h5> CONFIRMATION</h5>
                </div>
                <div class="modal-body text-center">
                    <label>Are you sure to create new porting? </label> &nbsp;&nbsp;&nbsp;
                </div> 
                <br/><br/>
                <div class="modal-footer">
                    <button type="button" id="submit_confirm_newport" name="submit_confirm_newport" class="btn btn-sm btn-info pull-left">YES</button>
                    <button type="button" id="cancel_submit_newport" name="cancel_submit_newport"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                </div>
            </div>
        </div>
    </div>
</form>

@if($getPatchByScheduled != null)
<div class="block panel-heading add-new-form" style="display: none">@include('prod_support.addnewform')</div>

@foreach($listDataPatchingToday as $listByGroupSeq)
<div class ="block block-alt-noborder full tablelist" @if(!$listDataPatchingToday) style = 'display:none' @endif>
     <div id="notificationDiv" class="alert  alert-dismissable" style="display:none"></div>
     <input id="datafixid" name="datafixid" type="hidden" value="{{$listByGroupSeq[0]->data_fix_id}}" class="form-control">
    <div class="form-group" >
        @if($listByGroupSeq != null)
        @if($listByGroupSeq[0]->status == 'Open' && $status == 'Open' && $script == 'Completed')
        <a href="{{url('/prod-support/data-patching/download')}}/{{$listByGroupSeq[0]->data_fix_id}}" 
           target="_blank" class="btn btn-sm btn-primary submitpatch"><i class="fa fa-download"></i> Download</a>


        <button type="submit" class="btn btn-sm btn-primary closeport" title="Set close patch today."><i class="fa fa-lock"></i> Close</button>
        @endif

        @if($listByGroupSeq[0]->status == 'Closed' && $listByGroupSeq[0]->is_sent != 1)
        <button id='emailtoapprover' type="button" class="btn btn-sm btn-primary emailtoapprover"><i class="fa fa-envelope"></i> Email To Approver</button>
        @endif
        <div class="table-responsive table-port">
            <h5><strong><font color="black">(ID:{{$listByGroupSeq[0]->data_fix_id}}) {{$listByGroupSeq[0]->name}} || {{$listByGroupSeq[0]->status}}
                    @if($listByGroupSeq[0]->closed_by && strlen($listByGroupSeq[0]->closed_by) > 0)
                    on {{$listByGroupSeq[0]->changed_date}} by {{$listByGroupSeq[0]->closed_by}}
                    @endif </font></strong></h5>
           
            <table id="data1-listport-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">No.</th>
                        <th class="text-center">Date Porting</th>
                        @if($listByGroupSeq[0]->closed_by)
                        <th class="text-center">ID</th>
                        @endif
                        <th class="text-center">CRM Number</th>
                        <th class="text-center">Redmine Number</th>
                        <th class="text-center">Module</th>
                        <th class="text-center">Problem Description</th>
                        <th class="text-center">Problem Type</th>
                        <th class="text-center">Group Type</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Endorsed By</th>
                        <th class="text-center">Endorsement Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Total Valid CR</th>
                        <th class="text-center">Total Valid Script</th>
                        <th class="text-center">Created</th>
                        <th class="text-center">Action</th>
                        @if($listByGroupSeq[0]->closed_by)
                        <th class="text-center">HelpDesk</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    @foreach($listByGroupSeq as $data => $rowData)
                    <tr>
                        <td class="text-center">{{++$data}}</td>
                        <td class="text-center">{{$rowData->dateport}}</td>
                        @if($listByGroupSeq[0]->closed_by && strlen($listByGroupSeq[0]->closed_by) > 0)
                        <td class="text-center">{{$rowData->data_fix_dtl_id}}</td>
                        @endif
                        <td class="text-center">{{$rowData->crm_no}}</td>
                        <td class="text-center">{{$rowData->redmineno}}</td>
                        <td class="text-center">{{$rowData->module}}</td>
                        <td class="text-center">{{$rowData->prob_desc}}</td>
                        <td class="text-center">{{$rowData->probtype}}</td>
                        <td class="text-center">{{$rowData->requester}}</td>
                        <td class="text-center">{{$rowData->requester_name}}</td>
                        <td class="text-center">@if($rowData->endorsement_by == null)-@else{{$rowData->endorsement_by}}@endif</td>
                        <td class="text-center">@if($rowData->endorsement_date == null)-@else{{$rowData->endorsement_date}}@endif</td>
                        <td class="text-center">@if(($rowData->patchstatus) == 'Rejected'){{$rowData->patchstatus}} @else @if(($rowData->CRstatus) == null){{$rowData->patchstatus}} @elseif(($rowData->CRstatus) != null && ($rowData->scriptstatus) == null) {{$rowData->CRstatus}} @else{{$rowData->scriptstatus}} @endif @endif</td>
                        <td class="text-center">{{$rowData->total_valid_cr}}</td>
                        <td class="text-center">{{$rowData->total_valid_script}}</td>
                        <td class="text-center">{{$rowData->dtl_created_by}} ({{$rowData->dtl_created_date}})</td>
                        @if($rowData->status == 'Open')
                        <td class="text-center">
                            <div class="btn-group btn-group-xs">
                                <a href="/prod-support/edit-patch/{{ $rowData->data_fix_dtl_id }}" 
                                   crm-no='{{ $rowData->data_fix_dtl_id }}'
                                   data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                                @if($rowData->total_valid_script > 0 || $rowData->total_valid_cr > 0)<a href="{{url('/prod-support/history-data-patching/download')}}/{{$rowData->data_fix_dtl_id}}" data-toggle="tooltip" title="Download" target="_blank" style="alignment-adjust: middle" class="btn btn-xs btn-info"><i class="fa fa-download"></i></a>@endif
                                <a idnom ="{{$rowData->data_fix_dtl_id}}" 
                                   accesskey=""data-toggle="tooltip" title="Delete" class="btn btn-sm btn-danger delete_details"><i class="fa fa-times"></i></a>
                            </div>
                        </td>
                        @elseif($rowData->status == 'Closed' && $data_fix_manager == 'Shahril Anuar Bin Ismail') 
                        <td class="text-center"><a idnom ="{{$rowData->data_fix_dtl_id}}" 
                                                   data-toggle="tooltip" title="Reject Data Patch" class="btn btn-sm btn-danger reject_patch"><i class="fa fa-times"></i></a></td>
                        @else <td></td> 
                        @endif
                        @if($listByGroupSeq[0]->closed_by && strlen($listByGroupSeq[0]->closed_by) > 0)
                        <td class="text-center">@if($rowData->sent_helpdesk===1) SENT @elseif($rowData->sent_helpdesk===0) FAILED @else NOT YET @endif</td>
                        @endif
                    </tr>
                    @endforeach
                </tbody>
            </table>
            <input type="hidden" id="delid" name="delid" value="" class="form-control" style="width: 100px;">
            <div id="modal_confirm_delete_details" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Delete Data Patch Details? </label> &nbsp;&nbsp;&nbsp;
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="submit" id="submit_confirm_delete" name="submit_confirm_delete" class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>

            <div id="modal_confirm_reject_patch" class="modal fade">
                <div class="modal-dialog modal-sm">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h5> CONFIRMATION</h5>
                        </div>
                        <div class="modal-body text-center">
                            <label>Are You Sure To Reject Data Patch? </label> &nbsp;&nbsp;&nbsp;
                        </div> 
                        <br/><br/>
                        <div class="modal-footer">
                            <button type="submit" id="submit_confirm_reject_patch" name="submit_confirm_reject_patch" class="btn btn-sm btn-info pull-left">YES</button>
                            <button type="button" id="cancel_submit_delete" name="cancel_submit_delete"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif
    </div>

</div>
@endforeach

@endif


<div id="modal_confirm_email" class="modal fade">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <h2>Are you sure to e-mail to approver? </h2> &nbsp;&nbsp;&nbsp;

                <!-- Select Components Block -->
                <div class="block text-left">

                    <div class="form-group" style="height:50px;">
                        <label class="col-md-4 control-label" for="emailApprovers">Send To</label>
                        <div class="col-md-6">
                            <select id="emailApprovers" name="emailApprovers" class="select-chosen" data-placeholder="Choose .." style="width: 100%;" multiple>
                                @foreach($usersManager as $rowData)
                                <option value="{{$rowData->data_fix_user_id}}">{{$rowData->full_name}} ({{$rowData->email}})</option>
                                @endforeach
                            <br/><br/>
                            </select>
                            @foreach($listGroupMo as $rowData)
                                {{$rowData->full_name}} ({{$rowData->email}})
                                @endforeach
                            <br/><br/>
                        </div>
                    </div>
                    <div class="form-group" style="height:50px;">
                        <label class="col-md-4 control-label" for="emailApproversCC">CC To</label>
                        <div class="col-md-6">
                            @foreach($listGroupCc as $rowData)
                            {{$rowData->full_name}} ({{$rowData->email}});
                            @endforeach
                            @foreach($listGroupePCc as $rowData)
                            {{$rowData->full_name}} ({{$rowData->email}});<br/> 
                            @endforeach
                            <br/>
                        </div>
                    </div>
                    <br/><br/>

                </div>

            </div>
            <div class="modal-footer">
                <button type="button" id="submit_confirm_email" name="submit_confirm_email" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_email" name="cancel_submit_email"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>

<div id="modal_confirm_submit" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <label>Are you sure to Close Porting? </label> &nbsp;&nbsp;&nbsp;
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="button" id="submit_confirm_closedport" name="submit_confirm_closedport" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_closedport" name="cancel_submit_closedport"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>

<div id="modal_confirm_cancelport" class="modal fade">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> CONFIRMATION</h5>
            </div>
            <div class="modal-body text-center">
                <label>Are you sure to Cancel Porting? </label> &nbsp;&nbsp;&nbsp;
            </div> 
            <br/><br/>
            <div class="modal-footer">
                <button type="submit" id="submit_confirm_cancelport" name="submit_confirm_cancelport" class="btn btn-sm btn-info pull-left">YES</button>
                <button type="button" id="cancel_submit_cancelport" name="cancel_submit_cancelport"  data-dismiss="modal" class="btn btn-sm btn-default">NO</button>
            </div>
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<script>
    document.getElementById('page-container').classList = [];
</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>

<script>
    ModalListActionLogDatatable.init();
        TablesDatatables.init();
    App.datatables();
    function initializeDataTable(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 15, 20, -1],
                    [10, 15, 20, 'All']
                ]
            });
        }
        initializeDataTable('#data1-listport-datatable');
    
    $("#crmno").on("focusout", function () {
        $.ajax({
            url: APP_URL + '/prod-support/data-patching/crmnumber/' + $('#crmno').val(),
            dataType: 'json',
            type: "GET",
            success: function (data) {
                $data = $(data)
                console.log($data)
                $('#portmodule').val($data[0][0].id).trigger('change');
                $("#orgname").val($data[1].name);
                $("#grouptype").val($data[1].orgname);
            }
        })

    });

    $(".add-new-seq").on("click", function () {
        $(".add-new-seq").show();
        $('#modal_confirm_newport').modal('show');
    });

    $('#submit_confirm_newport').on('click', function () {
        $('#modal_confirm_newport').modal('hide');
        $.ajax({
            type: "POST",
            url: "/prod-support/data-patching/createport",
            data: $('.port-form').serialize(),
        }).done(function (resp) {
            console.log(resp);
            location.reload();
            if (resp.status_code === 'SUCCESS') {
                $('#successResolution').show();
                setTimeout(location.reload.bind(location), 1000);
            }
        });
    });

    $(".cancel-port").on("click", function () {
        $(".cancel-port").show();
        $('#modal_confirm_cancelport').modal('show');
    });

    $('#submit_confirm_cancelport').on('click', function () {
        $('#modal_confirm_cancelport').modal('hide');
        $Id = $("#datafixid").val();
        $.ajax({
            type: "POST",
            url: "/prod-support/data-patching/cancelporting/" + $Id,
            data : {'_token': $('input[name=_token]').val()},
        }).done(function (resp) {
            console.log(resp);
            location.reload();
            // if (resp.status_code === 'SUCCESS') {
            //     $('#successResolution').show();
            // }
        });
    });

    $(".add-new-list").on("click", function () {
        $(".add-new-seq").hide();
        $(".add-new-form").show();
        $(".add-new-port").show();
        $(".add-new-list").hide();
        $(".patch-form").show();
        $("#crmno").val("");
        $("#portmodule").val("");
        $("#prodesc").val("");
        $("#probtype").val("");
        $("#grouptype").val("");
        $("#endorse").val("");
        $('#redmineid').hide();
        $('.redmine').hide();
        $('#crmno').show();
        $('.crmno1').show();
    });
    $('.resetbutton').on('click', function () {
        $(".add-new-form").hide();
        $(".add-new-list").show();
    });

    var APP_URL = {!! json_encode(url('/')) !!}

    $('.type').on("change", '#portmodule', function () {
        $('#portmodule').val();
        $module = $('#portmodule').val();
        $('#date_endorse').val("");
        $('#prodesc').empty();
        $.ajax({
            url: APP_URL + '/prod-support/data-patching/module/' + $module,
            dataType: 'json',
            type: "GET",
            success: function (data) {
                $data = $(data)

                $($data).each(function ()
                {
                    console.log(this.id);
                    console.log(this.name);
                    var option = $('<option />');
                    option.attr('value', this.id).text(this.name);

                    $('#prodesc').append(option);
                });
            }

        });

    });

   
    document.getElementById('probtype').addEventListener('change', function () {
    // $("#probtype").change(function () {
        if ($(this).val() == "167") {
            $('#endorse').show();
            $('.endorse1').show();
            $('#date_endorse1').show();
            $('#endorse').attr('required', '');
            $('#endorse').attr('data-error', 'This field is required.');
            $('#date_endorse1').attr('required', '');
            $('#date_endorse1').attr('data-error', 'This field is required.');
        } else {
            $('#endorse').hide();
            $('.endorse1').hide();
            $('#date_endorse1').hide();
            $('#endorse').removeAttr('required');
            $('#endorse').removeAttr('data-error');
            $('#date_endorse1').removeAttr('required');
            $('#date_endorse1').removeAttr('data-error');
        }
    });

    $("#endorse").on("click", function () {
        editButtonChanged(this);
    });

    $("#prodesc").on("change",function () {
        editButtonChanged(this);
    });

    function editButtonChanged(a) {
        $idModule = $('#prodesc').val()
        $idEndorse = $('#endorse').val()
        if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.3 (1)") {
            $('#date_endorse').val("2023-11-03");
        } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S) 600-22/10/13 JLD.4 (64)") {
            $('#date_endorse').val("2025-04-07");
        } else if ($idEndorse == "Kelulusan Pukal MOF.BPK (S).600-22/10/9 JLD 21 (21)") {
            $('#date_endorse').val("2024-07-03");
        } else if($idEndorse == "Tuan Haji Khairuddin Bin Bakar - TSK (S)" && $idModule == 213 ) {
            $('#date_endorse').val("2022-12-02");
        } else if($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 213 ) {
            $('#date_endorse').val("2022-05-11");
        } else if($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 206 ) {
            $('#date_endorse').val("2022-03-22");
        } else if($idEndorse == "Unit Pendaftaran Syarikat (UPS)" && $idModule == 207 ) {
            $('#date_endorse').val("2022-03-22");
        } else if($idEndorse == "Puan Pinta Zulfitri Binti Mirza Asfian - KSK (ePP)" && $idModule == 226 ) {
            $('#date_endorse').val("2023-03-02");
        } else if($idEndorse == "Kelulusan Pukal MOF.BPK.600-22/10/17 JLD 5 (42)") {
            $('#date_endorse').val("2024-11-29");
        } else if($idEndorse == "Kelulusan Pukal MOF.BPK(S)600-22/10/13 JLD.4 (65)") {
            $('#date_endorse').val("2025-05-30");
        }
        else {
            $('#date_endorse').val("");
        }
    }
    
    $('#reset_date').on("click", function () {
        $('#date_endorse').val("");
    });
    // $("#probtype").trigger("change");
    var probtype = document.getElementById('probtype');
var changeEvent = new Event('change');

probtype.dispatchEvent(changeEvent);

    $('#redmineid').hide();
    $('.redmine').hide();
    $('#redmineid').removeAttr('required');

    $('#checkbox').click(function () {

        if ($('#checkbox').is(":checked")) {
            $('.redmine').show();
            $('#redmineid').show();
            $('#redmineid').Attr('required');
            $('#redmineid').attr('data-error', 'This field is required.');
        } else {
            $('#redmineid').hide();
            $('.redmine').hide();
            $('#redmineid').removeAttr('required');
        }
    });

    $('#checkboxcase').click(function () {

        if ($('#checkboxcase').is(":checked")) {
            $('#exampletags2').show();
            $('#exampletags').Attr('required');
            $('#exampletags').attr('data-error', 'This field is required.');
        } else {
            $('#exampletags2').hide();
            $('#exampletags').val("");
            $('#exampletags').removeAttr('required');
        }
    });

    $('.emailtoapprover').on('click', function () {
        $(".emailtoapprover").show();
        $("#emailApprovers").val('').trigger("chosen:updated");
        $('#modal_confirm_email').modal('show');

        $('#notificationDiv').hide();

    });
    $('#submit_confirm_email').on('click', function () {
        $(".add-new-seq").show();
        $('#modal_confirm_email').modal('hide');
        var dataFixId = $("#datafixid").val();
        var emailApprovers = $("#emailApprovers").val();
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching/emailtoapprover/?dataFixId=" + dataFixId + "&emailApprovers=" + emailApprovers,
        }).done(function (resp) {
            console.log(resp);
            if (resp.status_code === 'SUCCESS') {
                $('#emailtoapprover').hide();
                $('#notificationDiv').show();
                $('#notificationDiv').addClass("alert-success");
                $('#notificationDiv').removeClass("alert-warning");
                $('#notificationDiv').append(
                        "<button type='button' class='close' data-dismiss='alert' aria-hidden='true'>×</button>"
                        + "<h4><i class='fa fa-check-circle'></i> " + resp.status_code + "</h4>  <a href='javascript:void(0)' class='alert-link'>" + resp.status_desc + "</a>"
                        );
            } else {
                $('#notificationDiv').show();
                $('#notificationDiv').addClass("alert-warning");
                $('#notificationDiv').removeClass("alert-success");
                $('#notificationDiv').append(
                        "<button type='button' class='close' data-dismiss='alert' aria-hidden='true'>×</button>"
                        + "<h4><i class='fa fa-times-circle'></i> " + resp.status_code + "</h4>  <a href='javascript:void(0)' class='alert-link'>" + resp.status_desc + "</a>"
                        );
            }
        });
    });

    $('.closeport').on('click', function () {
        $(".closeport").show();
        $(".add-new-seq").hide();
        $('#modal_confirm_submit').modal('show');
    });
    $('#submit_confirm_closedport').on('click', function () {
        $('#modal_confirm_submit').modal('hide');
        $Id = $("#datafixid").val();
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patching/closed/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
            if (resp.status_code === 'SUCCESS') {
                $('#successResolution').show();
                setTimeout(location.reload.bind(location), 1000);
            }
        });
    });

    $(".delete_details").on("click", function () {
        $("#modal_confirm_delete_details").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
    });

    $('#submit_confirm_delete').on('click', function () {
        $('#modal_confirm_delete_details').modal('hide');
        $Id = $("#delid").val();
        $.ajax({
            type: "GET",
            url: "/prod-support/data-patch/remove/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });

    $(".reject_patch").on("click", function () {
        $("#modal_confirm_reject_patch").modal('show');
        let
        delid = $(this).attr('idnom');
        $('#delid').val(delid);
    });

    $('#submit_confirm_reject_patch').on('click', function () {
        $('#modal_confirm_reject_patch').modal('hide');
        $Id = $("#delid").val();
        $.ajax({
            type: "GET",
            url: "/prod-support/reject_data_patch/" + $Id,
        }).done(function (resp) {
            console.log(resp);
            location.reload();
        });

    });

</script>  
@endsection