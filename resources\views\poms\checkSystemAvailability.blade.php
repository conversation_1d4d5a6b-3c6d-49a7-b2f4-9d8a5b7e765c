@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
@if (Auth::user())
<div class="container-fluid text-center" style="display: flex; justify-content: center;">
    <div class="panel panel-default" style="width: 100%; max-width: 800px; padding: 15px; border-radius: 4px;">
        <div class="panel-heading">
            <h2 class="panel-title">SLA System Availability</h2>
        </div>
        <div class="panel-body">
            <!-- Year & Month Selection -->
            <div class="row">
                <div class="col-xs-6">
                    <label class="control-label">Year:</label>
                    <select id="yearPicker" class="form-control">
                        @for ($year = 2020; $year <= date('Y'); $year++)
                            <option value="{{ $year }}" {{ $year == date('Y') ? 'selected' : '' }}>{{ $year }}</option>
                        @endfor
                    </select>
                </div>
                <div class="col-xs-6">
                    <label class="control-label">Month:</label>
                    <input type="text" id="monthPicker" class="form-control" placeholder="Enter month (1-12)">
                </div>
            </div>
            
            <!-- Nagios Interval -->
            <div class="form-group" style="margin-top: 15px;">
                <label class="control-label">Nagios Interval (mins):</label>
                <input type="number" id="nagiosInterval" class="form-control" value="10" readonly>
            </div>

            <button onclick="calculateSLA()" class="btn btn-primary btn-block">
                Calculate SLA
            </button>

            <!-- Loading Spinner -->
            <div id="loading" class="text-center" style="margin-top: 15px; display: none;">
                <i class="fa fa-spinner fa-spin"></i>
                <p>Calculating...</p>
            </div>

            <div id="result" class="table-responsive" style="margin-top: 15px;"></div>
        </div>
    </div>
</div>
@endif
@endsection

@section('jsprivate')
<script>
    var slaUrl = "{{ url('/poms/calculate-sla') }}";

    function calculateSLA() {
        let year = document.getElementById("yearPicker").value;
        let month = document.getElementById("monthPicker").value;
        let nagiosInterval = 10;

        let resultDiv = document.getElementById("result");
        let loadingDiv = document.getElementById("loading");

        if (!year || !month || isNaN(month) || month < 1 || month > 12) {
            alert("Please select a valid year and month (1-12).");
            return;
        }

        resultDiv.innerHTML = "";
        loadingDiv.style.display = "block";

        fetch(slaUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "X-CSRF-TOKEN": "{{ csrf_token() }}"
                },
                body: JSON.stringify({
                    year: parseInt(year),
                    month: parseInt(month),
                    nagios_interval: nagiosInterval
                })
            })
            .then(response => response.json())
            .then(data => {
                loadingDiv.style.display = "none";
                resultDiv.innerHTML = `
                <table class="table table-bordered">
                    <thead>
                        <tr class="active">
                            <th>Metric</th>
                            <th>Value</th>
                            <th>Formula</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr><td><strong>Number of SA Down</strong></td><td>${data.num_of_sa_down}</td><td>${data.num_of_sa_down}</td></tr>
                        <tr><td><strong>Total Minutes in Month</strong></td><td>${data.total_minutes}</td><td>${data.total_days} * 1440</td></tr>
                        <tr><td><strong>Scheduled Downtime</strong></td><td>${data.scheduled_downtime}</td><td>${data.fridays.map(friday => friday.toLocaleString('en-GB')).join(", ")}</td></tr>
                        <tr><td><strong>Available Minutes</strong></td><td>${data.available_minutes}</td><td>${data.total_minutes} - ${data.scheduled_downtime}</td></tr>
                        <tr><td><strong>Total Downtime</strong></td><td>${data.total_downtime}</td><td>${data.num_of_sa_down} * ${nagiosInterval}</td></tr>
                        <tr><td><strong>SLA Percentage</strong></td><td>${data.sla_percentage.toFixed(2)}%</td><td>((${data.available_minutes} - ${data.total_downtime}) / ${data.available_minutes}) * 100</td></tr>
                        <tr><td><strong>SLA Non-Compliance</strong></td><td>${data.sla_non_compliance.toFixed(2)}%</td><td>max(99.6 - ${data.sla_percentage.toFixed(2)}, 0)</td></tr>
                        <tr><td><strong>Non-Compliance Minutes</strong></td><td>${data.non_compliance_minutes.toFixed(2)}</td><td>(${data.sla_non_compliance.toFixed(2)} / 100) * ${data.available_minutes}</td></tr>
                        <tr><td><strong>Penalty Amount (RM)</strong></td><td>${data.penalty_amount.toFixed(2)}</td><td>${data.non_compliance_minutes.toFixed(2)} * 10</td></tr>
                    </tbody>
                </table>`;
            })
            .catch(error => {
                loadingDiv.style.display = "none";
                resultDiv.innerHTML = `<p class='text-danger'>Error: Unable to fetch results.</p>`;
            });
    }
</script>
<style>
    table th:nth-child(1), table td:nth-child(1) {
        text-align: left;
    }
    table th:nth-child(2), table td:nth-child(2) {
        text-align: right;
    }
    table th:nth-child(3), table td:nth-child(3) {
        text-align: right;
    }
</style>
@endsection
