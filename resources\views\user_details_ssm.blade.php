@extends('layouts.guest-dash')

@section('header')
<style>
    .modal-dialog {
        width: 1050px !important;
    }
</style> 
@endsection


@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">  
        <div class="block"> 
            <form id="form-search-task" action="{{url("/find/ssmno")}}/{{$ssmNo}}/{{$businessType}}" method="post" class="form-horizontal form-bordered">
                {{ csrf_field() }} 
                <div class="row">
                    <div class="col-lg-5">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="ssm_no">SSM No.</label>
                            <div class="input-group">
                                <input id="ssm_no" name="ssm_no" class="form-control" required value="{{ $ssmNo }}" type="text" >
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-5">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="business_type">Business Type</label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="business_type" name="business_type" class="form-control typeCrm" required>
                                        <option value="">Please select</option>
                                        <option value="ROB" @if($businessType === "ROB") selected @endif>ROB</option> 
                                        <option value="ROC" @if($businessType === "ROC") selected @endif>ROC</option> 
                                    </select> 
                                </div>
                            </div>
                        </div>
                    </div>  
                    <div class="col-lg-2">
                        <div class="form-actions form-actions-button text-right" style="margin-right:30px;">
                            <br/><button type="submit" class="btn btn-sm btn-info">Search</button>
                        </div> 
                    </div>
                </div>  
            </form>
            @if($statusCode !== null)

            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i>
                @foreach ($data->getElementsByTagName("Status") as $listStatus)
                    @foreach ($listStatus->childNodes as $objStatus)
                        @if ($objStatus->nodeName !== '#text' && $objStatus->nodeName !== 'epmf:Status')
                            <strong>{{ str_replace('epmf:', '', $objStatus->nodeName) }}</strong> : {{ $objStatus->nodeValue }} <br />
                        @endif
                    @endforeach
                @endforeach
                </h4>
            </div>

            <div class="block" >
                <div class="block-title">
                    <h2><strong>XML Response SSM Ver 1.0</strong></h2>
                    <div class="block-options pull-right action-today">
                        <small> View Details </small>
                        <a href="#pre_json_desc" data-toggle="collapse" class="btn btn-alt btn-default"><i class="fa fa-angle-down"></i></a>
                    </div>
                </div>

                <pre id="pre_json_desc" class="collapse">
                        <code style="float: left; color:#fff;">{{ $xmlData }}</code>
                </pre>
            </div>
            <div class="block" >
                <div class="block-title">
                    <h2><strong>XML Response SSM Ver 2.0</strong></h2>
                    <div class="block-options pull-right action-today">
                        <small> View Details </small>
                        <a href="#pre_json_desc2" data-toggle="collapse" class="btn btn-alt btn-default"><i class="fa fa-angle-down"></i></a>
                    </div>
                </div>

                <pre id="pre_json_desc2" class="collapse">
                        <code style="float: left; color:#fff;">{{ $xmlDataVer2 }}</code>
                </pre>
            </div>

            @if($statusCode === '70060')
            <div class="row">
                <div class="col-md-4">
                    @if(count($data->getElementsByTagName("StatusFromSSM")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Status From SSM </h2>
                        </div>
                        <address>
                            @foreach ($data->getElementsByTagName("StatusFromSSM") as $listObj)
                                @foreach ($listObj->childNodes as $obj)
                                    <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : {{ $obj->nodeValue }} <br />
                                @endforeach
                            @endforeach
                        </address> 
                    </div>
                    @endif

                    <div class="block">
                        <div class="block-title">
                            <h2>Maklumat Syarikat </h2>
                        </div>

                        <address>
                            @foreach ($data->getElementsByTagName("CompanyInformation") as $listObj)
                                @foreach ($listObj->childNodes as $obj)
                                    
                                    @if($obj->nodeName != 'ret:AnnualRevenue' )
                                        @if($obj->nodeName == 'ret:CompanyRegAddress' )
                                            <h5>Company Reg Address </h5> 
                                            <address>
                                            @foreach ($obj->childNodes as $objExt)
                                                <strong>{{ str_replace('ret:', '', $objExt->nodeName) }}</strong> : {{ $objExt->nodeValue }} <br />
                                            @endforeach
                                            </address>
                                        @else
                                            @if($obj->nodeName == 'ret:BusinessCompanyName')
                                                <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : <a href="{{url('/find/epno')}}/{{ $obj->nodeValue }}" target="blank">{{ $obj->nodeValue }}</a> <br />
                                            @else
                                                <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : {{ $obj->nodeValue }} <br />
                                            @endif
                                        @endif
                                    @endif
                                @endforeach
                                <br />
                            @endforeach
                        </address> 
                    </div> 

                    @if(count($data->getElementsByTagName("BusinessDescription")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Register Category Code </h2>
                        </div>
                        <address>
                            @foreach ($data->getElementsByTagName("BusinessDescription") as $bussDesc)
                                <strong>{{ str_replace('ret:', '', $bussDesc->nodeName) }}</strong> : {{ $bussDesc->nodeValue }} <br />
                            @endforeach
                        </address> 
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("NatureOfBusiness")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Nature Of Business </h2>
                        </div>
                        <address>
                            @php $counterNatureBusiness= 1 @endphp
                            @foreach ($data->getElementsByTagName("NatureOfBusiness") as $objList)
                                @php $isCounterNatureBusinessDisplay = false @endphp
                                @foreach ($objList->childNodes as $obj)
                                    @if($isCounterNatureBusinessDisplay == false) 
                                        @php $isCounterNatureBusinessDisplay = true @endphp
                                            {{ $counterNatureBusiness }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : {{ $obj->nodeValue }} <br />
                                @endforeach
                                @php $counterNatureBusiness++ @endphp
                            @endforeach
                        </address>  
                    </div>
                    @endif

                </div>
                <div class="col-md-4"> 
                    @if(count($data->getElementsByTagName("CapitalEquityOwner")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Capital Equity Owner</h2>
                        </div>
                        <address>
                            @php $counterEquityOwner = 1 @endphp
                            @foreach ($data->getElementsByTagName("CapitalEquityOwner") as $owners)
                                @php $isCounterEquityOwnerDisplay = false @endphp
                                @foreach ($owners->childNodes as $objOwner)
                                    @if($isCounterEquityOwnerDisplay == false) 
                                        @php $isCounterEquityOwnerDisplay = true @endphp
                                            {{ $counterEquityOwner }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $objOwner->nodeName) }}</strong> : {{ $objOwner->nodeValue }} <br />
                                @endforeach
                                @php $counterEquityOwner++ @endphp
                                <br />
                            @endforeach
                        </address>  
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("CapitalEquityInformation")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Capital Equity Information </h2>
                        </div>
                        <address>
                            @foreach ($data->getElementsByTagName("CapitalEquityInformation") as $capEqInfos)
                                @foreach ($capEqInfos->childNodes as $capEqInfo)
                                    <strong>{{ str_replace('ret:', '', $capEqInfo->nodeName) }}</strong> : {{ $capEqInfo->nodeValue }} <br />
                                @endforeach
                            @endforeach
                        </address>
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("AnnualRevenue")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Total Annual Revenue</h2>
                        </div>
                        <address>
                            @php $counterAnnualRev = 1 @endphp
                            @foreach ($data->getElementsByTagName("AnnualRevenue") as $annualRevenue)
                                @php $isCounterAnnualRevDisplay = false @endphp
                                @foreach ($annualRevenue->childNodes as $property)
                                    @if ($property->nodeName !== '#text' && $property->nodeName !== 'ret:AnnualRevenue')
                                        @if($isCounterAnnualRevDisplay == false) 
                                            @php $isCounterAnnualRevDisplay = true @endphp
                                            {{ $counterAnnualRev }}. 
                                        @endif
                                        <strong>{{ str_replace('ret:', '', $property->nodeName) }}</strong> : {{ $property->nodeValue }} <br />
                                    @endif
                                @endforeach

                                @if ($annualRevenue->nodeName !== '#text' && $annualRevenue->nodeName !== 'ret:AnnualRevenue')
                                    @php $counterAnnualRev++ @endphp
                                <br />
                                @endif
                            @endforeach
                        </address>
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("ParticularsOfIndebtedness")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Particulars Of Indebtedness</h2>
                        </div>
                        <address>
                            @php $counterPoi = 1 @endphp
                            @foreach ($data->getElementsByTagName("ParticularsOfIndebtedness") as $listObj)
                                @php $isCounterPoiDisplay = false @endphp
                                @foreach ($listObj->childNodes as $obj)
                                    @if($isCounterPoiDisplay == false) 
                                        @php $isCounterPoiDisplay = true @endphp
                                            {{ $counterPoi }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : {{ $obj->nodeValue }} <br />
                                @endforeach
                                @php $counterPoi++ @endphp
                                <br />
                            @endforeach
                        </address>  
                    </div>
                    @endif
                </div> 
                    
                <div class="col-md-4"> 
                    @if(count($data->getElementsByTagName("BranchesDetails")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Branches Details</h2>
                        </div>
                        <address>
                            @php $counterBranchesDetails = 1 @endphp
                            @foreach ($data->getElementsByTagName("BranchesDetails") as $listObj)
                                @php $isCounterBranchesDetailsDisplay = false @endphp
                                @foreach ($listObj->childNodes as $obj)
                                    @if($isCounterBranchesDetailsDisplay == false) 
                                        @php $isCounterBranchesDetailsDisplay = true @endphp
                                            {{ $counterBranchesDetails }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $obj->nodeName) }}</strong> : {{ $obj->nodeValue }} <br />
                                @endforeach
                                @php $counterBranchesDetails++ @endphp
                                <br />
                            @endforeach
                        </address>   
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("ParticularsOfAuditors")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Particulars Of Auditors</h2>
                        </div>
                        <address>
                            @php $counterPoAudit = 1 @endphp
                            @foreach ($data->getElementsByTagName("ParticularsOfAuditors") as $poAuditors)
                                @php $isCounterPoAuditDisplay = false @endphp
                                @foreach ($poAuditors->childNodes as $objPoAuditor)
                                    @if($isCounterPoAuditDisplay == false) 
                                        @php $isCounterPoAuditDisplay = true @endphp
                                            {{ $counterPoAudit }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $objPoAuditor->nodeName) }}</strong> : {{ $objPoAuditor->nodeValue }} <br />
                                @endforeach
                                @php $counterPoAudit++ @endphp
                                <br />
                            @endforeach
                        </address>  
                    </div>
                    @endif

                    @if(count($data->getElementsByTagName("PersonnelInformation")) > 0)
                    <div class="block">
                        <div class="block-title">
                            <h2>Personnel Information</h2>
                        </div>
                        <address>
                            @php $counterPersInfo = 1 @endphp
                            @foreach ($data->getElementsByTagName("PersonnelInformation") as $persInfos)
                                @php $isCounterPersInfoDisplay = false @endphp
                                @foreach ($persInfos->childNodes as $objPersInfo)
                                    @if($isCounterPersInfoDisplay == false) 
                                        @php $isCounterPersInfoDisplay = true @endphp
                                            {{ $counterPersInfo }}. 
                                    @endif
                                    <strong>{{ str_replace('ret:', '', $objPersInfo->nodeName) }}</strong> : {{ $objPersInfo->nodeValue }} <br />
                                @endforeach
                                @php $counterPersInfo++ @endphp
                                <br />
                            @endforeach
                        </address>   
                    </div>
                    @endif
                    
                </div>
                @endif
            </div> 
            @endif
        </div> 
    </div> 
</div>

@endif

@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
$(function () {
    TablesDatatables.init();
});
</script> 
@endsection