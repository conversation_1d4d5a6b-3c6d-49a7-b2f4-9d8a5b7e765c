<?php

namespace App\Http\Controllers\ProdSupport;

use Validator;
use App\Http\Controllers\Controller;
use App\Services\CRMService;
use App\Services\Traits\ProdSupport\PsPatchingService;
use Barryvdh\DomPDF\Facade as PDF;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Log;
use DB;
use Carbon\Carbon;
use Auth;
use \PhpOffice\PhpWord\TemplateProcessor;
use File;
use Mail;
use Config;
use App\PsDataFixUser;
use Guzzle;
use GuzzleHttp\Client;
use DateTime;
use Excel;
use Illuminate\Support\Facades\Hash;
use App\Migrate\ProdSupport\HelpdeskIntegration;

class PsPatchingController extends Controller
{

    use PsPatchingService;

    public function __construct()
    {
        $this->middleware('auth');
    }

    public static function crmService()
    {
        return new CRMService;
    }

    public function getCrmnumber($caseNumber)
    {
        $caseTask = self::crmService()->getDetailCaseAndTaskLatestCRM($caseNumber);
        $case = self::crmService()->getDetailCase($caseNumber);
        $collectDataModuleandOrgName = array();
        if ($case != null) {
            $account_code = null;
            $account_name = null;
            $newNameKementerian = null;

            $modulefromCRM = self::crmService()->getValueLookupCRM('cdc_sub_category_list', $case->sub_category_c);
            $changeModuleIdtoNaming = $this->getDataModuleFromCRM($modulefromCRM);
            $account = self::crmService()->getDetailAccountCRM($case->account_id);

            if ($account != null) {
                $account_name = $account->name;
                if ($account->account_type == 'SUPPLIER') {
                    $account_code = $account->mof_no;
                    $newNameKementerian = $this->getNameFromMofNumber($account_code);
                    if ($account_code == null) {
                        $account_code = $account->ep_no;
                        $newNameKementerian = $this->getNameFromepNo($account_code);
                    }
                    if ($account_code == null) {
                        $account_code = $account->registration_no;
                        $newNameKementerian = $this->getNameFromeregNo($account_code);
                    }
                    array_push($collectDataModuleandOrgName, $changeModuleIdtoNaming, $newNameKementerian);
                } else if ($account->account_type == 'GOVERNMENT' && $account->org_gov_type == 2) {
                    $account_code = $account->org_gov_code;
                    $newNameKementerian = $this->getNameKumpulanKementerianAlone($account_code);
                    array_push($collectDataModuleandOrgName, $changeModuleIdtoNaming, $newNameKementerian);
                } else if ($account->account_type == 'GOVERNMENT') {
                    $account_code = $account->org_gov_code;
                    $newNameKementerian = $this->getNameKumpulanKementerian($account_code);
                    if ($changeModuleIdtoNaming != null) {
                        array_push($collectDataModuleandOrgName, $changeModuleIdtoNaming, $newNameKementerian);
                    } else {
                        array_push($collectDataModuleandOrgName, $newNameKementerian);
                    }
                }
                if ($account->name == 'COMMERCE DOT COM INTERNATIONAL' || $account->name == 'COMMERCE DOT COM SDN BHD') {
                    $newNameKementerian = 'COMMERCE DOT COM SDN BHD';
                    array_push($collectDataModuleandOrgName, $changeModuleIdtoNaming, $newNameKementerian);
                }
            } else {
                array_push($collectDataModuleandOrgName, $changeModuleIdtoNaming);
            }
        }
        return response()->json($collectDataModuleandOrgName, 200);
    }

    public function getListProbDescription($changeModuleIdtoNaming)
    {
        $getProbDescription = $this->getProblemdescription($changeModuleIdtoNaming);
        return response()->json($getProbDescription, 200);
    }

    public function getListProbDescForChange($probid)
    {
        $getProbDescForChange = $this->getProblemdescForChange($probid);
        return response()->json($getProbDescForChange, 200);
    }

    public function listDataPacthing()
    {
        $this->setCancelledFixPortingInvalid();

        $data = $this->listDataFixPatching();
        return view('prod_support.data_patch', $data);
    }

    public function getListAll(Request $request)
    {
        $porting1pm = null;
        $porting6pm = null;
        $portingUrgent = null;
        $result = null;
        $resultsix = null;
        $resulturgent = null;
        $byModule = null;
        $byCRM = null;
        $byDate = null;
        $byCompany = null;
        $getModule = $this->getDataModule();
        $listDataPatchingToday = $this->getListDataPatchNormalByToday();
        if ($request->method() == 'POST') {
            if ($request->modulename != null) {
                $byModule = ($request->modulename);
            } else {
                $byModule = null;
            }

            if ($request->crmno1 != null) {
                $byCRM = ($request->crmno1);
            } else {
                $byCRM = null;
            }

            if ($request->company_name != null) {
                $byCompany = '%' . ($request->company_name) . '%';
            } else {
                $byCompany = null;
            }

            if ($request->date1 != null) {
                $byDate = ($request->date1);
            } else {
                $byDate = null;
            }
            $porting1pm = $this->getListAllData($byDate, $byCRM, $byModule, $byCompany);
            $result = collect($porting1pm)->groupBy('datafixid');
            $porting6pm = $this->getListAllDatasixpm($byDate, $byCRM, $byModule, $byCompany);
            $resultsix = collect($porting6pm)->groupBy('datafixid');
            $portingUrgent = $this->getListAllDataurgent($byDate, $byCRM, $byModule, $byCompany);
            $resulturgent = collect($portingUrgent)->groupBy('datafixid');
        }
        $request->flash();
        return view('prod_support.history_data_patch', [
            'listdata' => $result,
            'listDataPatchingToday' => $listDataPatchingToday,
            'getModule' => $getModule,
            'listdatasix' => $resultsix,
            'listdataurgent' => $resulturgent
        ]);
    }

    public function downloadDocDataFixDtlIdByToken($token)
    {
        $objDataFix = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->where('token_access', $token)
            ->first();
        if ($objDataFix) {
            return $this->createWordandFileByDataFixDtlId($objDataFix->data_fix_dtl_id);
        }
        return "No record found!";
    }

    public function createWordandFileByDataFixDtlId($dataFixDtlId)
    {
        $folderName = null;
        $listData = $this->historyDataPatchDetailAll($dataFixDtlId);
        if ($listData && count($listData) > 0) {
            $dataFixDtlId = $listData[0]->data_fix_dtl_id;
            $carbonDatePorting = Carbon::parse($listData[0]->datetime_porting);
            if ($listData[0]->type_porting == 'S') {
                $folderName = "DataFixChangeRequest" . $carbonDatePorting->format('YmdHi');
            } else {
                $folderName = "DataFixUrgentChangeRequest" . $carbonDatePorting->format('YmdHi');
            }

            $nocrmredmine = null;

            if ($listData[0]->redmineno) {
                $nocrmredmine = $listData[0]->redmineno;
            } else {
                $nocrmredmine = $listData[0]->crm_no;
            }

            $crmfolder = strtolower($listData[0]->modulecode) . '-' . $nocrmredmine;

            $folderPathToCreated = 'prod_support/export/porting/temp/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName . '/' . $crmfolder;

            $folderPath = 'app/' . $folderPathToCreated;

            foreach ($listData as $data) {
                $date1 = DateTime::createFromFormat('d/m/Y', $listData[0]->dateport)->setTime(0, 0, 0);
                $date2 = DateTime::createFromFormat('d/m/Y', '22/01/2024')->setTime(0, 0, 0);

                if ($date1 >= $date2) {
                    $templateProcessor = new TemplateProcessor(storage_path('prod_support/template/CHANGE AND RELEASE REQUEST FORM-V1.0_template.docx'));
                } else {
                    $templateProcessor = new TemplateProcessor(storage_path('prod_support/template/eP-ISMS-PSM-CHANGE-AND-RELEASE-REQUEST-FORM-V5.5_template.docx'));
                }

                $getValueRequestType = json_decode($data->request_type);
                if ($getValueRequestType) {
                    $templateProcessor->setValue('requester_type', $this->checkRequestType(172, $getValueRequestType));
                    $templateProcessor->setValue('requester_type1', $this->checkRequestType(173, $getValueRequestType));
                    $templateProcessor->setValue('requester_type2', $this->checkRequestType(174, $getValueRequestType));
                    $templateProcessor->setValue('requester_type3', $this->checkRequestType(175, $getValueRequestType));
                    $templateProcessor->setValue('requester_type4', $this->checkRequestType(176, $getValueRequestType));
                    $templateProcessor->setValue('requester_type5', $this->checkRequestType(177, $getValueRequestType));
                    $templateProcessor->setValue('requester_type6', $this->checkRequestType(178, $getValueRequestType));
                }
                //Modify contents:
                if ($data->crm_no) {
                    $templateProcessor->setValue('crm_no', $data->crm_no);
                } else {
                    $templateProcessor->setValue('crm_no', $data->redmineno);
                }
                $collect_script = array();
                $test = $this->historyDataPatchDetailAll($dataFixDtlId);

                foreach ($test as $a) {
                    if ($a->crm_no === $data->crm_no) {
                        array_push($collect_script, $a->namescript);
                    }
                }
                $try = implode(" </w:t><w:br/><w:t> ", $collect_script);
                $templateProcessor->setValue('namescript', $try);

                $text = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->reason));
                $templateProcessor->setValue('reason', $text);

                $text2 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->impact_assessment));
                $templateProcessor->setValue('impact', $text2);

                $text3 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->description));
                $templateProcessor->setValue('problem_details', $text3);

                $text4 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->remarks));
                $templateProcessor->setValue('remarks', $text4);

                $text5 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->activity_plan));
                $templateProcessor->setValue('activity', $text5);

                $text6 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->expected_complete_date));
                $templateProcessor->setValue('expected', $text6);

                $templateProcessor->setValue('ministry', $data->req);
                $templateProcessor->setValue('problem_type', $data->problem);
                $templateProcessor->setValue('modulecode', strtolower($data->modulecode));
                $templateProcessor->setValue('modulecode2', $data->modulecode);
                $templateProcessor->setValue('requester_name', $data->requester_name);
                $templateProcessor->setValue('requester_date', $data->requester_date);
                $templateProcessor->setValue('request_cat', $this->checkRequestCategory(179, $data->request_category));
                $templateProcessor->setValue('request_cat1', $this->checkRequestCategory(180, $data->request_category));
                $templateProcessor->setValue('impact1', $this->checkRequestCategory(181, $data->impact_category));
                $templateProcessor->setValue('impact2', $this->checkRequestCategory(182, $data->impact_category));
                $templateProcessor->setValue('recommender_name', $data->recommender_name);
                $templateProcessor->setValue('recommender_date', $data->recommender_date);
                $templateProcessor->setValue('approver_name', $data->approver_name);
                $templateProcessor->setValue('approver_date', $data->approver_date);

                try {
                    $nocrmredmine = null;

                    if ($data->redmineno) {
                        $nocrmredmine = $data->redmineno;
                    } else {
                        $nocrmredmine = $data->crm_no;
                    }


                    $folderPatchDetailPath = $folderPathToCreated . '/' . strtolower($data->modulecode) . '-' . $nocrmredmine;
                    Storage::makeDirectory($folderPatchDetailPath);

                    if ($date1 >= $date2) {
                        $crPath = storage_path('app/' . $folderPatchDetailPath . '/' . 'CHANGE AND RELEASE REQUEST FORM-V1.0 ' . strtolower($data->modulecode) . '-' . $nocrmredmine . '.docx');
                    } else {
                        $crPath = storage_path('app/' . $folderPatchDetailPath . '/' . 'eP-ISMS-PSM-CHANGE AND RELEASE REQUEST FORM-V5.5 ' . strtolower($data->modulecode) . '-' . $nocrmredmine . '.docx');
                    }

                    $templateProcessor->saveAs($crPath);

                    $sqlPath = storage_path('app/' . $folderPatchDetailPath . '/' . $data->namescript);
                    File::put($sqlPath, $data->querysql);
                } catch (Exception $e) {
                    Log::error('{CLASS => ' . get_class($this) . '}{' . __FUNCTION__ . ' => ' . $e->getMessage());
                    echo $e;
                }
            }

            return $this->downloadHistory($dataFixDtlId, $carbonDatePorting, $folderPath, $folderName, $crmfolder);
        }
    }

    public function listDataLookup()
    {
        $getLookupdate = $this->getDataLookup();
        $getDistinctGroupType = $this->getDistinctGroupType();

        return view('prod_support.data_lookup', [
            'getLookupdate' => $getLookupdate,
            'getDistinctGroupType' => $getDistinctGroupType,
        ]);
    }

    public function addlistDataLookup(Request $request)
    {
        $user = auth()->user()->first_name;

        $result = DB::connection('mysql_ep_prod_support')->table('ps_lookup')->insert([
            ['code' => $request->code, 'name' => $request->name, 'description' => $request->description, 'group_type' => $request->grouptype, 'created_by' => $user, 'created_date' => Carbon::now(), 'status' => 'Created'],
        ]);

        return $this->listDataLookup();
    }

    public function editDataLookup(Request $request)
    {
        $oricode = null;
        $oriname = null;
        $oridescription = null;
        $origrouptype = null;
        $request->all();
        $user = auth()->user()->first_name;
        $getEditLookupdate = $this->getEditedDataLookup($request->editid)[0];
        if (!$request->editcode) {
            $oricode = $getEditLookupdate->code;
        } else
            $oricode = $request->editcode;
        if (!$request->editname) {
            $oriname = $getEditLookupdate->name;
        } else
            $oriname = $request->editname;
        if (!$request->editdescription) {
            $oridescription = $getEditLookupdate->description;
        } else
            $oridescription = $request->editdescription;
        if (!$request->editgrouptype) {
            $origrouptype = $getEditLookupdate->group_type;
        } else
            $origrouptype = $request->editgrouptype;
        if ($request->isMethod("POST")) {
            $updateData = [
                'code' => $oricode,
                'name' => $oriname,
                'description' => $oridescription,
                'group_type' => $origrouptype,
                'status' => 'Updated',
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_lookup')
                ->where('id', $request->editid)
                ->update($updateData);
        }
        return $this->listDataLookup();
    }

    public function cancelDataLookup($id)
    {
        $user = auth()->user()->first_name;

        $updateData = [
            'status' => 'Cancelled',
            'changed_by' => $user,
            'changed_date' => Carbon::now()
        ];
        DB::connection('mysql_ep_prod_support')->table('ps_lookup')
            ->where('id', $id)
            ->update($updateData);

        return $this->listDataLookup();
    }

    public function approverUser()
    {
        $getApprover = $this->getApproverUserList();

        return view('prod_support.approver', [
            'getApprover' => $getApprover,
        ]);
    }

    public function addNewApproverUser(Request $request)
    {
        $user = auth()->user()->first_name;
        if ($request->role == "1") {
            $role = 1;
        } else {
            $role = 0;
        }
        if ($request->role == "2") {
            $roles = 1;
        } else {
            $roles = 0;
        }
        if ($request->role == "3") {
            $statusroles = 1;
        } else {
            $statusroles = 0;
        }
        if ($request->role == "4") {
            $statusroles_ep = 1;
        } else {
            $statusroles_ep = 0;
        }
        if ($request->role == "5") {
            $statusroles_mo = 1;
        } else {
            $statusroles_mo = 0;
        }
        $result = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_user')->insert([
            ['full_name' => $request->name, 'email' => $request->email, 'is_manager_datafix' => $role, 'is_ep_endorsed' => $roles, 'is_group_cc' => $statusroles, 'is_ep_group_cc' => $statusroles_ep, 'is_mo_group' => $statusroles_mo, 'record_status' => 1, 'created_by' => $user, 'created_date' => Carbon::now()],
        ]);

        return $this->approverUser();
    }

    public function editApproverDetails(Request $request)
    {
        $oriname = null;
        $oriemail = null;
        $orirole = null;
        $oriroles = null;
        $emaillccgroup = null;
        $emaillccePgroup = null;
        $statusrealrole = null;
        $emailmogroup = null;
        $request->all();
        if ($request->editrole == "1") {
            $role = 1;
        } else {
            $role = 0;
        }
        if ($request->editrole == "2") {
            $roles = 1;
        } else {
            $roles = 0;
        }
        if ($request->editrole == "3") {
            $statusroles = 1;
        } else {
            $statusroles = 0;
        }
        if ($request->editrole == "4") {
            $statusroles_ep = 1;
        } else {
            $statusroles_ep = 0;
        }
        if ($request->editrole == "5") {
            $statusroles_mo = 1;
        } else {
            $statusroles_mo = 0;
        }

        if ($request->statusedit == "1") {
            $statusactive = 1;
        } elseif ($request->statusedit == "2") {
            $statusactive = 0;
        }
        $user = auth()->user()->first_name;
        $getApprovertoEdit = $this->getEditedApprover($request->editid)[0];
        if (!$request->editname) {
            $oriname = $getApprovertoEdit->full_name;
        } else {
            $oriname = $request->editname;
        }
        if (!$request->editemail) {
            $oriemail = $getApprovertoEdit->email;
        } else {
            $oriemail = $request->editemail;
        }
        if (!$request->editrole) {
            $orirole = $getApprovertoEdit->is_manager_datafix;
        } else {
            $orirole = $role;
        }
        if (!$request->editrole) {
            $oriroles = $getApprovertoEdit->is_ep_endorsed;
        } else {
            $oriroles = $roles;
        }
        if (!$request->editrole) {
            $emaillccgroup = $getApprovertoEdit->is_group_cc;
        } else {
            $emaillccgroup = $statusroles;
        }
        if (!$request->editrole) {
            $emaillccePgroup = $getApprovertoEdit->is_ep_group_cc;
        } else {
            $emaillccePgroup = $statusroles_ep;
        }
        if (!$request->editrole) {
            $emailmogroup = $getApprovertoEdit->is_mo_group;
        } else {
            $emailmogroup = $statusroles_mo;
        }

        if (!$request->statusedit) {
            $statusrealrole = $getApprovertoEdit->record_status;
        } else {
            $statusrealrole = $statusactive;
        }

        if ($request->isMethod("POST")) {
            $updateData = [
                'full_name' => $oriname,
                'email' => $oriemail,
                'is_manager_datafix' => $orirole,
                'is_ep_endorsed' => $oriroles,
                'is_group_cc' => $emaillccgroup,
                'is_ep_group_cc' => $emaillccePgroup,
                'is_mo_group' => $emailmogroup,
                'record_status' => $statusrealrole,
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix_user')
                ->where('data_fix_user_id', $request->editid)
                ->update($updateData);
        }
        return $this->approverUser();
    }

    public function cancelApprover($id)
    {
        $user = auth()->user()->first_name;
        DB::connection('mysql_ep_prod_support')->table('ps_data_fix_user')
            ->where('data_fix_user_id', $id)
            ->delete();

        return $this->approverUser();
    }

    /**
     * This result should be get today creation
     * @return type
     */
    public function listDataFixPatching()
    {
        $status = null;
        $script = null;
        $currenttime = intval(Carbon::now()->format('H'));
        $user = $this->getUserName();
        $getModule = $this->getDataModule();
        $data_fix_manager = null;
        $listPatchDetailByScheduleToday = $this->getPatchDetailScheduledNormalByToday();
        $patchDetailByScheduledToday = null;
        if ($listPatchDetailByScheduleToday && ($listPatchDetailByScheduleToday[0]->status === 'Open' || $listPatchDetailByScheduleToday[0]->status === 'Closed' || $listPatchDetailByScheduleToday[0]->status === 'Cancelled') && count($listPatchDetailByScheduleToday) > 0) {
            $patchDetailByScheduledToday = $listPatchDetailByScheduleToday[0];
        }
        $getProblemtype = $this->getProblemType();
        $getRequesterType = $this->getRequesterType();
        $getOpenTimePatch = $this->getTimeOpenPatch();
        $listDataPatchingToday = $this->getListDataPatchNormalByToday();
        if ($listDataPatchingToday != null) {
            $status = 'Open';
            $script = 'Completed';
            foreach ($listDataPatchingToday as $listData) {
                if ($listData->status == 'Open' && $listData->scriptstatus == null) {
                    $status = $listData->status;
                    $script = $listData->scriptstatus;
                }
            }
        }
        $size = $listDataPatchingToday != null ? sizeof($listDataPatchingToday) : 0;
        $collectDataPatchingToday = collect($listDataPatchingToday)->groupBy('porting_seq');
        $usersManager = PsDataFixUser::where('is_manager_datafix', 1)->where('record_status', 1)->orderBy('seq', 'ASC')->get();
        $usersEndorsed = PsDataFixUser::where('is_ep_endorsed', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupCc = PsDataFixUser::where('is_group_cc', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupePCc = PsDataFixUser::where('is_ep_group_cc', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupMo = PsDataFixUser::where('is_mo_group', 1)->where('record_status', 1)->orderBy('full_name')->get();

        $manager = $this->getManagerDataFix();
        foreach ($manager as $list) {
            if ($user == $list->full_name) {
                $data_fix_manager = $user;
            }
        }
        $data = [
            'usersManager' => $usersManager,
            'usersEndorsed' => $usersEndorsed,
            'listGroupCc' => $listGroupCc,
            'listGroupePCc' => $listGroupePCc,
            'listGroupMo' => $listGroupMo,
            'getModule' => $getModule,
            'size' => $size,
            'getPatchByScheduled' => $patchDetailByScheduledToday,
            'getProblemtype' => $getProblemtype,
            'getRequesterType' => $getRequesterType,
            'username' => $user,
            'currenttime' => $currenttime,
            'getOpenTimePatch' => $getOpenTimePatch,
            'listDataPatchingToday' => $collectDataPatchingToday,
            'data_fix_manager' => $data_fix_manager,
            'status' => $status,
            'script' => $script
        ];
        return $data;
    }

    public function getDatePort()
    {
        return $this->listDataPacthing();
    }

    public function findDetailsDataPatch($dataFixDetailId)
    {
        $getValueRequestType = null;
        $dataFixDetail = null;
        $dataFixPorting = null;
        $getProbDescription = null;
        $listScriptPatchData = null;
        $changeRequestData = null;
        $listdata_upload = null;
        $getModule = $this->getDataModule();
        $getProblemtype = $this->getProblemType();
        $getRequesterType = $this->getRequesterType();
        $usersManager = PsDataFixUser::where('is_manager_datafix', 1)->where('record_status', 1)->orderBy('seq', 'ASC')->get();
        $usersEndorsed = PsDataFixUser::where('is_ep_endorsed', 1)->where('record_status', 1)->orderBy('full_name')->get();

        if ($dataFixDetailId) {
            $dataFixDetail = $this->getDetailsDataPatch($dataFixDetailId);
            $listdata_upload = $this->listImpactAttached($dataFixDetailId);
            $caseTask = self::crmService()->getDetailCaseAndTaskLatestCRM($dataFixDetail->crm_no);
            $case = self::crmService()->getDetailCase($dataFixDetail->crm_no);
            $dataFixPorting = $this->getDetailDataFixPorting($dataFixDetail->data_fix_id);
            $changeRequestData = $this->getDetailsChangeRequest($dataFixDetailId);
            if ($changeRequestData) {
                $getValueRequestType = json_decode($changeRequestData->request_type);
            }

            $listScriptPatchData = $this->getListDetailsPatchScript($dataFixDetailId);

            $getModule = $this->getDataModule();
            if ($dataFixDetail) {
                $getProbDescription = $this->getListLookupGroupTypeById("Problem Description", $dataFixDetail->problem_description);
            }
        }
        $dataResult = [
            'getModule' => $getModule,
            'getProblemtype' => $getProblemtype,
            'getRequesterType' => $getRequesterType,
            'usersManager' => $usersManager,
            'case' => $case,
            'usersEndorsed' => $usersEndorsed,
            'dataFixPorting' => $dataFixPorting,
            'dataFixDetail' => $dataFixDetail,
            'changeRequestData' => $changeRequestData,
            'listScriptPatchData' => $listScriptPatchData,
            'getProbDescription' => $getProbDescription,
            'getValueRequestType' => $getValueRequestType,
            'listdata_upload' => $listdata_upload,
        ];

        return view('prod_support.patch_edit', $dataResult);
    }

    /**
     * Create porting by today.
     * @return type
     */
    public function createPortSchedulePorting(Request $request)
    {
        $date = date('d/m/Y', strtotime($request->date_porting));
        $hourNow = Carbon::now()->hour;
        $listPatchDetailbyScheduledToday = $this->getPatchDetailScheduledNormalByToday();
        if ($listPatchDetailbyScheduledToday != null) {
            $previousHour = substr($listPatchDetailbyScheduledToday[0]->datetime_porting, 11, 2);
        }
        $data = collect();
        $data->put("status_code", "DRAFT");
        $data->put("status_desc", "Start process insert data fix porting");

        $creationPortType = "MORNING";
        $isValidCreation = false;
        if ($listPatchDetailbyScheduledToday == null) {
            if ($hourNow >= 13) {
                $creationPortType = "EVENING";
                $isValidCreation = true;
            } else {
                $creationPortType = "MORNING";
                $isValidCreation = true;
            }
        } else {
            $collectPatchDetailbyScheduledToday = collect($listPatchDetailbyScheduledToday);
            if ($collectPatchDetailbyScheduledToday->whereIn('status', ['Open', 'Submit'])->count() == 0) {
                $objPatch = $collectPatchDetailbyScheduledToday->first();
                $hourCheck = Carbon::parse($objPatch->datetime_porting)->hour;
                if ($hourCheck == 13 && $listPatchDetailbyScheduledToday[0]->status === 'Closed') {
                    $creationPortType = "EVENING";
                    $isValidCreation = true;
                } elseif ($previousHour == 13 && $listPatchDetailbyScheduledToday[0]->status === 'Cancelled') {
                    $creationPortType = "MORNING";
                    $isValidCreation = true;
                } elseif ($previousHour == 18 && $listPatchDetailbyScheduledToday[0]->status === 'Cancelled') {
                    $creationPortType = "EVENING";
                    $isValidCreation = true;
                } elseif ($hourCheck >= 13 && $listPatchDetailbyScheduledToday[0]->status === 'Closed') {
                    $creationPortType = "MORNING";
                    $isValidCreation = true;
                } else {
                    $data->put("status_code", "ERROR");
                    $data->put("status_desc", "Data already exist 1");
                }
            } else {
                $data->put("status_code", "ERROR");
                $data->put("status_desc", "Data already exist");
            }
        }

        if ($isValidCreation == true) {
            $getMaxsequence = $this->getMaxSeq();
            $namePorting = 'Data Fix @ 1 PM';
            $descPorting = 'Data Fix @ 1 PM';
            $dateTimePorting = Carbon::now()->format('Y-m-d') . " 13:00:00";
            if ($creationPortType == "EVENING") {
                $namePorting = 'Data Fix @ 6 PM';
                $descPorting = 'Data Fix @ 6 PM';
                $dateTimePorting = Carbon::now()->format('Y-m-d') . " 18:00:00";
            }

            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')->insert(
                [
                    'status' => 'Open',
                    'name' => $namePorting . ' ' . $date,
                    'description' => $descPorting . ' ' . $date,
                    'datetime_porting' => $dateTimePorting,
                    'porting_seq' => $getMaxsequence + 1,
                    'created_by' => auth()->user()->first_name,
                    'created_date' => Carbon::now(),
                    'type_porting' => 'S'
                ]
            );
            $data->put("status_code", "SUCCESS");
            $data->put("status_desc", "Succesfully Save");
        }

        return response()->json($data, 200);
    }

    public function deletePortingToday(Request $request, $id)
    {
        $user = $this->getUserName();
        $dataAll = $this->getDetailDataFixPorting($id);
        if ($dataAll != null) {
            $getdatafixid13 = $this->getListDataOpenPatch();
            if ($getdatafixid13 != null && count($getdatafixid13) > 0) {
                foreach ($getdatafixid13 as $listid) {
                    $getid = $listid->data_fix_dtl_id;
                    if ($request->isMethod("POST")) {
                        $updateData = [
                            'status' => 'Cancelled',
                            'changed_by' => $user,
                            'changed_date' => Carbon::now()
                        ];
                        DB::connection('mysql_ep_prod_support')->table('ps_change_request')
                            ->where('data_fix_dtl_id', $getid)
                            ->update($updateData);
                        DB::connection('mysql_ep_prod_support')->table('ps_patch_script')
                            ->where('data_fix_dtl_id', $getid)
                            ->update($updateData);
                        DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                            ->where('data_fix_dtl_id', $getid)
                            ->update($updateData);
                    }
                }
                try {
                    $folderPath = ($dataAll->folder_path);
                    if ($folderPath !== null) {
                        File::deleteDirectory(storage_path($folderPath));
                    }
                } catch (Exception $ex) {
                    Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                    Log::error($ex->getTraceAsString());
                    return $ex->getMessage();
                }
            }
        }

        $updateData = [
            'status' => 'Cancelled',
            'changed_by' => $user,
            'changed_date' => Carbon::now()
        ];
        DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
            ->where('data_fix_id', $id)
            ->update($updateData);

        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthing');
    }

    public function rejectDataPatch($datafixdtlid)
    {
        $user = 'Shahril Anuar Bin Ismail';

        $updateData = [
            'status' => 'Rejected',
            'changed_by' => $user,
            'changed_date' => Carbon::now()
        ];
        DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
            ->where('data_fix_dtl_id', $datafixdtlid)
            ->update($updateData);

        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthing');
    }

    public function rejectDataPatchUrgent($datafixdtlid)
    {
        $user = $this->getUserName();

        $updateData = [
            'status' => 'Rejected',
            'changed_by' => $user,
            'changed_date' => Carbon::now()
        ];
        DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
            ->where('data_fix_dtl_id', $datafixdtlid)
            ->update($updateData);

        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthingUrgent');
    }

    public function updatePortingToClosed($datafixid)
    {

        $data = collect();
        $user = $this->getUserName();

        $dataFixObj = $this->getDetailDataFixPorting($datafixid);
        if ($dataFixObj && $dataFixObj->status == 'Open') {
            // Start to do validation 
            /*
             * Must check table ps_data_fix_dtl has record in ps_patch_script and ps_change_request 
             */
            $isValidClose = true; //Assume Valid to set close.
            $collectError = collect();
            $listDetailPatchDetail = $this->getListDetailsDataPatch($datafixid);
            if (count($listDetailPatchDetail) > 0) {
                $errObj = collect();
                foreach ($listDetailPatchDetail as $detailPatchObj) {
                    // Checking  ps_change_request
                    $crObj = $this->getDetailsChangeRequest($detailPatchObj->data_fix_dtl_id);
                    if ($crObj == null) {
                        $isValidClose = false;
                        $errObj->push(
                            array(
                                'detail_patch_id' => $detailPatchObj->data_fix_dtl_id,
                                'change_request_id' => null,
                                'remark' => 'Change Request not found'
                            )
                        );
                    } else if (strlen($crObj->description) < 4 || strlen($crObj->reason) < 4) {
                        $isValidClose = false;
                        $errObj->push(
                            array(
                                'detail_patch_id' => $detailPatchObj->data_fix_dtl_id,
                                'change_request_id' => $crObj->changed_request_id,
                                'remark' => 'Checking on field description & reason still not valid data.',
                            )
                        );
                    }

                    // Checking ps_patch_script 
                    $listPatchScript = $this->getListDetailsPatchScript($detailPatchObj->data_fix_dtl_id);
                    if ($listPatchScript) {
                        foreach ($listPatchScript as $patchScriptObj) {
                            if ($patchScriptObj->sql == '' || $patchScriptObj->sql == null || strlen($patchScriptObj->sql) < 10) {
                                $isValidClose = false;
                                $errObj->push(
                                    array(
                                        'detail_patch_id' => $detailPatchObj->data_fix_dtl_id,
                                        'ps_patch_script_id' => $patchScriptObj->id,
                                        'remark' => 'Checking on field sql still not valid data.',
                                    )
                                );
                            }
                        }
                    } else {
                        return "Please refresh";
                    }

                    $collectError->push($errObj);
                }
            } else {
                $data->put('status', "ERROR");
                $data->put('status_desc', "This Data Patch ID: " . $datafixid . "  , checking on List Detail Patch. There is no record found! ");
                return $data;
            }

            if ($isValidClose === true) {
                //token : for public access epss to download file without login
                $uuid = \Ramsey\Uuid\Uuid::uuid4();
                $updateData = [
                    'token_access' => $uuid,
                    'status' => 'Closed',
                    'closed_by' => $user,
                    'changed_by' => $user,
                    'changed_date' => Carbon::now()
                ];
                DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                    ->where('data_fix_id', $datafixid)
                    ->update($updateData);

                //TRIGGER TO IT-HELP-DESK INTEGRATION
                $resultHelpDesk = 'NOT INTEGRATE';
                $isUrgentPatchIntegrate = env('IS_URGENT_PATCH_INTEGRATE', false);
                $isHelpDeskIntegrate = env('IS_HELPDESK_INTEGRATE', false);
                $helpDeskIntegrateType = env('HELPDESK_INTEGRATE_SEND_TYPE', 'GROUP');
                if ($isHelpDeskIntegrate === true) {
                    $helpdeskIntegration = new HelpdeskIntegration;

                    try {
                        // By default we set Urgent Request not integrate to HELPDESK
                        if ($dataFixObj->type_porting === 'U' && $dataFixObj->urgent_type === 'internal') {
                            $data->put('helpdesk', 'This request is URGENT. No need to integrate to HELPDESK');
                        } else if ($helpDeskIntegrateType == 'Individual') {
                            $resultHelpDesk = $helpdeskIntegration->sendHelpDeskDataPatchRequestByEachPatchFixDetail($datafixid);
                            $data->put('helpdesk', $resultHelpDesk);
                        } else {
                            $resultHelpDesk = $helpdeskIntegration->sendHelpDeskDataPatchRequest($datafixid);
                            $data->put('helpdesk', $resultHelpDesk);
                        }
                    } catch (Exception $ex) {
                        $data->put('helpdesk', $ex->getMessage());
                    }
                }

                $data->put('status', "SUCCESS");
                $data->put('status_desc', "Successfully as 'Closed' status on Data Patch ID: " . $datafixid);

                $data->put('data', $this->getListDetailsDataPatch($datafixid));
                return $data;
            } else {
                $data->put('status', "ERROR");
                $data->put('status_desc', "");
                $data->put('error', $collectError);
                return $data;
            }

            // Start to set as Closed
        } else {
            $data->put('status', "ERROR");
            $data->put('status_desc', "This Data Patch ID: " . $datafixid . "  is not valid to Closed. ");
            $data->put('data', $dataFixObj);
            return $data;
        }
    }

    /**
      Manual Trigger resend to helpdesk - by package
     */
    public function sendHelpDeskDataPatchRequest($dataFixId)
    {
        $helpdeskIntegration = new HelpdeskIntegration;
        return $helpdeskIntegration->sendHelpDeskDataPatchRequest($dataFixId);
    }

    /**
      Manual Trigger resend to helpdesk - send individual case
     */
    public function sendHelpDeskDataPatchRequestByEachPatchFixDetail($dataFixId)
    {
        $helpdeskIntegration = new HelpdeskIntegration;
        return $helpdeskIntegration->sendHelpDeskDataPatchRequestByEachPatchFixDetail($dataFixId);
    }

    /**
      Manual Trigger resend to helpdesk - send individual case
     */
    public function sendHelpDeskDataPatchRequestByFixPatchDetail($dataFixId, $dataFixDetailId)
    {
        $helpdeskIntegration = new HelpdeskIntegration;
        return $helpdeskIntegration->sendHelpDeskDataPatchRequestByFixPatchDetail($dataFixId, $dataFixDetailId);
    }

    public function createPatching(Request $request)
    {
        $endorse = null;
        $date_endorse = null;
        $user = $this->getUserName();
        $getMaxFixId = $this->getMaxSeqFixId();
        $getMaxBill = $this->getMaxBillSeq();
        $listPatchDetailbyScheduledToday = $this->getPatchDetailScheduledNormalByToday();
        if ($request->probtype == 167) {
            $endorse = $request->endorse;
            $date_endorse = $request->date_endorse;
        } else {
            $endorse = '-';
        }
        if ($listPatchDetailbyScheduledToday[0]->status == 'Open') {
            if ($getMaxBill->bill == null) {
                $result = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                    ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => '1', 'datetime_porting' => $request->dateport, 'crm_no' => $request->crmno, 'redmineno' => $request->redmineid, 'module' => $request->portmodule, 'problem_description' => $request->prodesc, 'requester_type' => $request->grouptype, 'requester_name' => $request->orgname, 'requester_code' => $request->orgname, 'endorsement_by' => $endorse, 'endorsement_date' => $date_endorse, 'problem_type' => $request->probtype, 'created_by' => $user, 'created_date' => Carbon::now(), 'crm_tag_no' => $request->exampletags, 'remarks' => $request->remarks, 'status' => 'Created'],
                ]);
            } else {
                $result = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                    ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => $getMaxBill->bill + 1, 'datetime_porting' => $request->dateport, 'crm_no' => $request->crmno, 'redmineno' => $request->redmineid, 'module' => $request->portmodule, 'problem_description' => $request->prodesc, 'requester_type' => $request->grouptype, 'requester_name' => $request->orgname, 'requester_code' => $request->orgname, 'endorsement_by' => $endorse, 'endorsement_date' => $date_endorse, 'problem_type' => $request->probtype, 'created_by' => $user, 'created_date' => Carbon::now(), 'crm_tag_no' => $request->exampletags, 'remarks' => $request->remarks, 'status' => 'Created'],
                ]);
            }
        } else {
            return 'PORTING CLOSED';
        }

        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthing');
    }

    public function actionForSubmitUpdateAndView(Request $request)
    {

        if (isset($_POST['Closed'])) {
            return $this->getListAll($request);
        } elseif (isset($_POST['Submit'])) {
            return $this->updateDataPatchDetail($request);
        }
    }

    public function updateDataPatchDetail(Request $request)
    {
        $user = $this->getUserName();
        $orimodule = $request->portmodule1;
        $oriprobdesc = $request->prodesc1;
        $orirequester = $request->grouptype1;
        $oriprobtype = $request->probtype1;
        $date_endorse = null;

        if ($request->probtype1 == 167) {
            $endorse = $request->endorse;
            $date_endorse = $request->date_endorse;
        } else {
            $endorse = '-';
        }
        //        update details
        if ($request->isMethod("POST") && $request->crmno1 != null) {

            $updateData = [
                'crm_no' => $request->crmno1,
                'redmineno' => $request->redmineid,
                'module' => $orimodule,
                'problem_description' => $oriprobdesc,
                'requester_type' => $orirequester,
                'problem_type' => $oriprobtype,
                'requester_name' => $request->orgname1,
                'requester_code' => $request->orgname1,
                'endorsement_by' => $endorse,
                'endorsement_date' => $date_endorse,
                'crm_tag_no' => $request->exampletags,
                'changed_by' => $user,
                'changed_date' => Carbon::now(),
                'remarks' => $request->remarksedit
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                ->where('data_fix_dtl_id', $request->datafixid)
                ->update($updateData);

            $objDfd = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                ->where('data_fix_dtl_id', $request->datafixid)
                ->first();
            if ($objDfd && $objDfd->token_access == null || $objDfd->token_access == '') {
                $uuid = \Ramsey\Uuid\Uuid::uuid4();
                DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                    ->where('data_fix_dtl_id', $request->datafixid)
                    ->update(['token_access' => $uuid]);
            }

            $detectCrCreatedOrNot = $this->getDetailsChangeRequest($request->datafixid);

            if ($this->listImpactAttached($request->datafixid) != null) {
                $impact_assessment = 'Please refer attachment';
            } else {
                $impact_assessment = $request->impactassement;
            }

            if ($detectCrCreatedOrNot != null) {
                $checkbox1 = $request->request_type_checkbox;
                $chk = json_encode($checkbox1);

                if ($request->isMethod("POST")) {
                    $updateData = [
                        'request_type' => $chk,
                        'request_category' => $request->radio_reqcategory,
                        'description' => $request->descreq,
                        'reason' => $request->reareq,
                        'recommender_name' => $request->recommendedby,
                        'approver_name' => $request->approvedby,
                        'approver_date' => $request->approveddate,
                        'activity_plan' => $request->activityplan,
                        'expected_complete_date' => $request->expected,
                        'impact_category' => $request->radio_impact,
                        'remarks' => $request->remarksCR,
                        'system_affected' => $request->affectedat1,
                        'impact_assessment' => $impact_assessment,
                        'changed_by' => $user,
                        'changed_date' => Carbon::Now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_change_request')
                        ->where('changed_request_id', $request->changerequestid)
                        ->update($updateData);
                }
            } else {
                $checkbox1 = $request->request_type_checkbox;
                $chk = json_encode($checkbox1);
                DB::connection('mysql_ep_prod_support')->table('ps_change_request')->insert([
                    [
                        'data_fix_dtl_id' => $request->datafixid,
                        'request_type' => $chk,
                        'request_category' => $request->radio_reqcategory,
                        'description' => $request->descreq,
                        'reason' => $request->reareq,
                        'requester_name' => $user,
                        'requester_date' => $request->requesteddate,
                        'recommender_name' => $request->recommendedby,
                        'recommender_date' => $request->recommendeddate,
                        'approver_name' => $request->approvedby,
                        'approver_date' => $request->approveddate,
                        'activity_plan' => $request->activityplan,
                        'expected_complete_date' => $request->expected,
                        'impact_category' => $request->radio_impact,
                        'remarks' => $request->remarksCR,
                        'system_affected' => $request->affectedat1,
                        'impact_assessment' => $impact_assessment,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => 'CR Created'
                    ],
                ]);
            }
        }
        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthing');
    }

    public function createScriptPatching()
    {

        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $dataFixDtlId = request()->dataFixDtlId;
        $scriptName = rtrim(request()->scriptName, '.sql');
        $script = request()->script;
        if (strlen($dataFixDtlId) == 0 || strlen($script) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'datafixId or script is required'
            );
        } else {
            $encodescript = base64_decode($script);
            $user = $this->getUserName();
            DB::connection('mysql_ep_prod_support')->table('ps_patch_script')->insert([
                [
                    'data_fix_dtl_id' => $dataFixDtlId,
                    'name' => $scriptName . '.sql',
                    'sql' => $encodescript,
                    'created_by' => $user,
                    'created_date' => Carbon::Now(),
                    'status' => 'Completed'
                ],
            ]);

            $result = array(
                'status' => 'Success',
                'description' => 'Insert',
                'dataHtml' => $this->htmlListScriptData($dataFixDtlId)
            );
        }
        return response()->json($result, 200);
    }

    public function deleteScriptPatching()
    {

        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $dataFixDtlId = request()->dataFixDtlId;
        $scriptId = request()->scriptId;

        $objDataFixDtl = $this->getDetailsDataPatch($dataFixDtlId);
        $dataAll = $this->historyDataPatchDetail($dataFixDtlId, $scriptId);
        if ($dataAll[0]->redmineno) {
            $nocrmredmine = $dataAll[0]->redmineno;
        } else {
            $nocrmredmine = $dataAll[0]->crm_no;
        }
        if ($dataAll != null) {
            try {
                $folderPath = ($dataAll[0]->folder_path);
                $PathName = $folderPath . '/' . strtolower($dataAll[0]->modulecode) . '-' . $nocrmredmine . '/' . $dataAll[0]->namescript;
                if (file_exists(storage_path($PathName))) {
                    File::delete(storage_path($PathName));
                }
            } catch (Exception $ex) {
                Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                Log::error($ex->getTraceAsString());
                return $ex->getMessage();
            }
        }
        if (strlen($scriptId) == 0 || strlen($dataFixDtlId) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'scriptId and dataFixDtlId is required'
            );
        } else {

            DB::connection('mysql_ep_prod_support')->table('ps_patch_script')
                ->where('id', $scriptId)
                ->delete();
            $result = array(
                'status' => 'Success',
                'description' => 'Insert',
                'dataHtml' => $this->htmlListScriptData($dataFixDtlId)
            );
        }
        return response()->json($result, 200);
    }

    protected function htmlListScriptData($dataFixDtlId)
    {
        $listPatch = DB::connection('mysql_ep_prod_support')->table('ps_patch_script')
            ->where('data_fix_dtl_id', $dataFixDtlId)
            ->where('status', 'Completed')
            ->orderBy('created_date', 'asc')
            ->get();

        $html = "";
        $counter = 1;
        foreach ($listPatch as $key => $row) {
            $counter = $key + 1;
            $html = $html .
                "<div class='panel panel-default'>" .
                "<div class='panel-heading'>" .
                "<h4 class='panel-title'>$counter <i class='fa fa-angle-right'></i>" .
                "<a class='accordion-toggle' data-toggle='collapse' data-parent='#script_$row->id' href='#script_$row->id'> $row->name</a>" .
                "<div class='pull-right'>
                           <a href='javascript:void(0)' data-toggle='tooltip'
                           onclick='onClickDeleteScript(this)' 
                           data-script-id='$row->id' data-fix-dtl-id='$row->data_fix_dtl_id'
                           title='Delete this script' data-original-title=''><i class='fa fa-times'></i></a>  
                        </div>" .
                "</h4>" .
                "</div>" .
                "<div id='script_$row->id' class='panel-collapse collapse'>" .
                "<div class='panel-body'>" .
                "<pre>" .
                "<code style='float: left; color:#fff;' id='selected_script_table_json_desc'>$row->sql</code>" .
                "</pre>" .
                "</div>" .
                "</div>" .
                "</div>";
        }

        return $html;
    }

    public function deleteDataPatchDtl($dataFixDtlId)
    {
        $user = $this->getUserName();
        $dataResult = collect();
        $dataResult->put('status', 'error');
        $dataResult->put('status_desc', 'Invalid Data');

        if ($dataFixDtlId) {
            $objDataFixDtl = $this->getDetailsDataPatch($dataFixDtlId);
            $dataAll = $this->historyDataPatchDetailAll($dataFixDtlId);
            if ($dataAll[0]->redmineno != null) {
                $doc_crm_redmine = $dataAll[0]->redmineno;
            } else {
                $doc_crm_redmine = $dataAll[0]->crm_no;
            }
            if ($dataAll != null) {
                try {
                    $folderPath = ($dataAll[0]->folder_path);
                    $PathName = $folderPath . '/' . strtolower($dataAll[0]->modulecode) . '-' . $doc_crm_redmine;
                    File::deleteDirectory(storage_path($PathName));
                } catch (Exception $ex) {
                    Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                    Log::error($ex->getTraceAsString());
                    return $ex->getMessage();
                }
            }


            if ($objDataFixDtl) {
                $objDataFixDtl = $this->getDetailDataFixPorting($objDataFixDtl->data_fix_id);
                if ($objDataFixDtl && $objDataFixDtl->status == 'Open') {
                    $getdatafixdtl = $this->getIdForDeleteDetails($dataFixDtlId);
                    $deldatafixdtl = $getdatafixdtl->data_fix_dtl_id;
                    $deldatapcrid = $getdatafixdtl->pcrid;
                    $deldatappsid = $getdatafixdtl->ppsid;
                    if ($deldatappsid != null) {
                        DB::connection('mysql_ep_prod_support')->table('ps_patch_script')
                            ->where('data_fix_dtl_id', $deldatappsid)
                            ->delete();
                    }
                    if ($deldatapcrid != null) {
                        DB::connection('mysql_ep_prod_support')->table('ps_change_request')
                            ->where('data_fix_dtl_id', $deldatapcrid)
                            ->delete();
                    }
                    if ($deldatafixdtl != null) {
                        DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                            ->where('data_fix_dtl_id', $deldatafixdtl)
                            ->delete();
                    }
                }
            } else {
                $dataResult->put('status', 'error');
                $dataResult->put('status_desc', 'No record found');
            }
        }

        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthing');
    }

    //    URGENT

    public function createPort(Request $request)
    {
        $time = null;
        $openPatchDetail = $this->getOpenPatchDetail();
        $getMaxsequence = $this->getMaxSeq();
        if ($request->time_porting >= '12:00:00') {
            $time = date('g.i ', strtotime($request->time_porting)) . 'PM';
        } else {
            $time = date('g.i ', strtotime($request->time_porting)) . 'AM';
        }
        $date = date('d/m/Y', strtotime($request->date_porting));
        $data = collect();
        $data->put("status_code", "DRAFT");
        $data->put("status_desc", "Start process insert data fix porting");
        if ($openPatchDetail != 'Open') {
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')->insert(
                [
                    'name' => 'Data Fix ' . $date . ' @ ' . $time . ' - Urgent Porting',
                    'description' => 'Data Fix ' . $date . ' @ ' . $time . ' - Urgent Porting',
                    'status' => 'Open',
                    'urgent_type' => $request->radio_urgent_type,
                    'datetime_porting' => $request->date_porting . ' ' . $request->time_porting,
                    'porting_seq' => $getMaxsequence + 1,
                    'created_by' => auth()->user()->first_name,
                    'created_date' => Carbon::now(),
                    'type_porting' => 'U'
                ]
            );
            $data->put("status_code", "SUCCESS");
            $data->put("status_desc", "Succesfully Save");
        } else {
            $data->put("status_code", "ERROR");
            $data->put("status_desc", "Already has Data Fix Porting status Open");
        }

        return $this->listDataPacthingUrgent();
    }

    public function updateDataFixPortingUrgent(Request $request)
    {
        $time = null;
        if ($request->time_porting >= '12:00:00') {
            $time = date('g.i ', strtotime($request->time_porting)) . 'PM';
        } else {
            $time = date('g.i ', strtotime($request->time_porting)) . 'AM';
        }
        $date = date('d/m/Y', strtotime($request->date_porting));
        if ($request->isMethod("POST")) {
            $dataFixPorting = $this->getDetailDataFixPorting($request->data_fix_porting_id);

            if ($dataFixPorting) {
                $dataFixPorting->datetime_porting = $request->date_porting . " " . $request->time_porting;

                DB::connection('mysql_ep_prod_support')
                    ->table('ps_data_fix')
                    ->where('data_fix_id', $dataFixPorting->data_fix_id)
                    ->update([
                        'name' => 'Data Fix ' . $date . ' @ ' . $time . ' - Urgent Porting',
                        'description' => 'Data Fix ' . $date . ' @ ' . $time . ' - Urgent Porting',
                        'datetime_porting' => $dataFixPorting->datetime_porting,
                        'urgent_type' => $request->radio_urgent_type,
                        'changed_date' => Carbon::now(),
                        'changed_by' => null != auth()->user() ? auth()->user()->first_name : 'System'
                    ]);
            }
        }
        return $this->listDataPacthingUrgent();
    }

    public function actionForDeleteAndUpdatePorting(Request $request)
    {

        if (isset($_POST['update_button'])) {
            return $this->updateDataFixPortingUrgent($request);
        } elseif (isset($_POST['submit_confirm_cancelport'])) {
            return $this->deletePortingTodayurgent($request);
        }
    }

    public function deletePortingTodayurgent(Request $request)
    {
        $user = $this->getUserName();
        $getdatafixid = $this->getOpenPatchDetail();
        if (count($getdatafixid) > 0) {
            $getid = $getdatafixid[0]->data_fix_id;
        }
        $listDataOpenPatchforUrgent = $this->listDataOpenPatchforUrgent();

        $dataAll = $this->getDetailDataFixPorting($getid);
        if ($dataAll != null) {
            try {
                $folderPath = ($dataAll->folder_path);
                if ($folderPath !== null) {
                    File::deleteDirectory(storage_path($folderPath));
                }
            } catch (Exception $ex) {
                Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                Log::error($ex->getTraceAsString());
                return $ex->getMessage();
            }
        }

        if (count($listDataOpenPatchforUrgent) > 0) {
            foreach ($listDataOpenPatchforUrgent as $listid) {
                $getdtlid = $listid->data_fix_dtl_id;
                if ($request->isMethod("POST")) {
                    $updateData = [
                        'status' => 'Cancelled',
                        'changed_by' => $user,
                        'changed_date' => Carbon::now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_change_request')
                        ->where('data_fix_dtl_id', $getdtlid)
                        ->update($updateData);
                    DB::connection('mysql_ep_prod_support')->table('ps_patch_script')
                        ->where('data_fix_dtl_id', $getdtlid)
                        ->update($updateData);
                    DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                        ->where('data_fix_dtl_id', $getdtlid)
                        ->update($updateData);
                }
            }
            try {
                $folderPath = ($dataAll->folder_path);
                if ($folderPath !== null) {
                    File::deleteDirectory(storage_path($folderPath));
                }
            } catch (Exception $ex) {
                Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                Log::error($ex->getTraceAsString());
                return $ex->getMessage();
            }
        }
        if ($request->isMethod("POST")) {
            $updateData = [
                'status' => 'Cancelled',
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                ->where('data_fix_id', $getid)
                ->update($updateData);
        }
        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthingUrgent');
    }

    public function listDataPacthingUrgent()
    {
        $data = $this->listDataFixPatchingUrgent();
        return view('prod_support.urgent_patch', $data);
    }

    public function listDataFixPatchingUrgent()
    {
        $status = null;
        $script = null;
        $getpatch = null;
        $getEmailuser = null;
        $currenttime = intval(Carbon::now()->format('H'));
        $user = $this->getUserName();
        $getModule = $this->getDataModule();
        $getProblemtype = $this->getProblemType();
        $getRequesterType = $this->getRequesterType();
        $getMaxsequence = $this->getMaxSeq();
        $getOpenTimePatch = $this->getTimeOpenPatch();
        $usersManager = PsDataFixUser::where('is_manager_datafix', 1)->where('record_status', 1)->orderBy('seq', 'ASC')->get();
        $usersEndorsed = PsDataFixUser::where('is_ep_endorsed', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupCc = PsDataFixUser::where('is_group_cc', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupePCc = PsDataFixUser::where('is_ep_group_cc', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $listGroupMo = PsDataFixUser::where('is_mo_group', 1)->where('record_status', 1)->orderBy('full_name')->get();
        $closedPatchList = $this->getClosedPatchDetail();
        $getOpenPatchDetail = $this->getOpenPatchDetail();
        if ($getOpenPatchDetail && ($getOpenPatchDetail[0]->status === 'Open' || $getOpenPatchDetail[0]->status === 'Closed') && count($getOpenPatchDetail) > 0) {
            $getpatch = $getOpenPatchDetail[0];
        }
        $getAllUrgentPortingToday = $this->getListDataPatchUrgentByToday();
        if ($getAllUrgentPortingToday != null) {
            $status = 'Open';
            $script = 'Completed';
            foreach ($getAllUrgentPortingToday as $listData) {
                if ($listData->status == 'Open' && $listData->scriptstatus == null) {
                    $status = $listData->status;
                    $script = $listData->scriptstatus;
                }
            }
        }
        if ($getAllUrgentPortingToday && ($getAllUrgentPortingToday[0]->status === 'Open' || $getAllUrgentPortingToday[0]->status === 'Closed') && count($getAllUrgentPortingToday) > 0) {
            $getEmailuser = $getAllUrgentPortingToday[0];
        }
        $size = $getAllUrgentPortingToday != null ? sizeof($getAllUrgentPortingToday) : 0;
        $collectDataPatchingTodayUrgent = collect($getAllUrgentPortingToday)->groupBy('porting_seq');
        $data_fix_manager = null;
        $manager = $this->getManagerDataFix();
        foreach ($manager as $list) {
            if ($list->full_name == 'Shahril Anuar Bin Ismail') {
                $data_fix_manager = 'Shahril Anuar Bin Ismail';
            }
        }

        $data = [
            'getModule' => $getModule,
            'getProblemtype' => $getProblemtype,
            'getRequesterType' => $getRequesterType,
            'usersManager' => $usersManager,
            'size' => $size,
            'usersEndorsed' => $usersEndorsed,
            'listGroupCc' => $listGroupCc,
            'listGroupePCc' => $listGroupePCc,
            'listGroupMo' => $listGroupMo,
            'username' => $user,
            'currenttime' => $currenttime,
            'getMaxsequence' => $getMaxsequence,
            'getOpenTimePatch' => $getOpenTimePatch,
            'getpatch' => $getpatch,
            'getEmailuser' => $getEmailuser,
            'closedPatch' => $closedPatchList,
            'listUrgentToday' => $collectDataPatchingTodayUrgent,
            'data_fix_manager' => $data_fix_manager,
            'status' => $status,
            'script' => $script
        ];

        return $data;
    }

    public function cancelSubmitPortingTodayurgent(Request $request)
    {
        $user = $this->getUserName();
        $getdatafixid = $this->getOpenPatchDetail();
        if (count($getdatafixid) > 0) {
            $getid = $getdatafixid->data_fix_id;
        }
        if ($request->isMethod("GET")) {
            $updateData = [
                'status' => 'Open',
                'closed_by' => '',
                'changed_by' => $user,
                'changed_date' => Carbon::now()
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                ->where('data_fix_id', $getid)
                ->update($updateData);
        }

        return $this->listDataFixPatchingUrgent();
    }

    public function createPatchingUrgent(Request $request)
    {
        $endorse = null;
        $date_endorse = null;
        $user = $this->getUserName();
        $getMaxFixId = $this->getMaxSeqFixIdUrgent();
        $getMaxBill = $this->getMaxBillSeqUrgent();
        if ($request->probtype == 167) {
            $endorse = $request->endorse;
            $date_endorse = $request->date_endorse;
        } else {
            $endorse = '-';
        }
        if ($getMaxBill->bill == null) {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => '1', 'datetime_porting' => $request->dateport, 'crm_no' => $request->crmno, 'redmineno' => $request->redmineid, 'module' => $request->portmodule, 'problem_description' => $request->prodesc, 'requester_type' => $request->grouptype, 'requester_name' => $request->orgname, 'requester_code' => $request->orgname, 'endorsement_by' => $endorse, 'endorsement_date' => $date_endorse, 'problem_type' => $request->probtype, 'created_by' => $user, 'created_date' => Carbon::now(), 'redmineno' => $request->redmineid, 'crm_tag_no' => $request->exampletags, 'remarks' => $request->remarks, 'status' => 'Created'],
            ]);
        } else {
            $result = DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => $getMaxBill->bill + 1, 'datetime_porting' => $request->dateport, 'crm_no' => $request->crmno, 'redmineno' => $request->redmineid, 'module' => $request->portmodule, 'problem_description' => $request->prodesc, 'requester_type' => $request->grouptype, 'requester_name' => $request->orgname, 'requester_code' => $request->orgname, 'endorsement_by' => $endorse, 'endorsement_date' => $date_endorse, 'problem_type' => $request->probtype, 'created_by' => $user, 'created_date' => Carbon::now(), 'redmineno' => $request->redmineid, 'crm_tag_no' => $request->exampletags, 'remarks' => $request->remarks, 'status' => 'Created'],
            ]);
        }
        $data = $this->listDataPacthingUrgent();
        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthingUrgent');
    }

    public function editListDataPatchDetailsUrgent($datafixdtlid)
    {
        $getModule = $this->getDataModule();
        $getProblemtype = $this->getProblemType();
        $getRequesterType = $this->getRequesterType();
        $returnValueFromCRafterEdit = $this->returnValueCRafterEdit($datafixdtlid);
        $checkScriptNotExist = $this->checkIfScriptNotExist($datafixdtlid);
        $getListDataCRMnumberOpenPatchingToday = $this->getListDataOpenPatchbyCRMNumber($datafixdtlid);
        $case = self::crmService()->getDetailCase($getListDataCRMnumberOpenPatchingToday[0]->crm_no);
        $getProbDescription = $this->getProblemdescription($getListDataCRMnumberOpenPatchingToday[0]->moduleid);
        $getValueRequestType = null;
        $dataFixDetail = null;
        $dataFixPorting = null;
        $listScriptPatchData = null;
        $changeRequestData = null;
        $listdata_upload = null;
        $usersManager = PsDataFixUser::where('is_manager_datafix', 1)->where('record_status', 1)->orderBy('seq', 'ASC')->get();
        $usersEndorsed = PsDataFixUser::where('is_ep_endorsed', 1)->where('record_status', 1)->orderBy('full_name')->get();

        if ($datafixdtlid) {
            $getListDataCRMnumberOpenPatchingToday = $this->getListDataOpenPatchbyCRMNumber($datafixdtlid);
            $dataFixDetail = $this->getDetailsDataPatch($datafixdtlid);
            $dataFixPorting = $this->getDetailDataFixPorting($dataFixDetail->data_fix_id);
            $changeRequestData = $this->getDetailsChangeRequest($datafixdtlid);
            $listdata_upload = $this->listImpactAttached($datafixdtlid);
            if ($getListDataCRMnumberOpenPatchingToday) {
                $checkScriptNotExist = $this->checkIfScriptNotExist($getListDataCRMnumberOpenPatchingToday[0]->data_fix_dtl_id);
            }
            $returnValueFromCRafterEdit = $this->returnValueCRafterEdit($datafixdtlid);
            if ($returnValueFromCRafterEdit) {
                $getValueRequestType = json_decode($returnValueFromCRafterEdit[0]->request_type);
            }
            $getModule = $this->getDataModule();
            if ($getListDataCRMnumberOpenPatchingToday) {
                $getProbDescription = $this->getProblemdescription($getListDataCRMnumberOpenPatchingToday[0]->moduleid);
            }
            if ($changeRequestData) {
                $listScriptPatchData = $this->getListDetailsPatchScript($datafixdtlid);
                $getValueRequestType = json_decode($changeRequestData->request_type);
            }
            if ($dataFixDetail) {
                $getProbDescription = $this->getListLookupGroupTypeById("Problem Description", $dataFixDetail->problem_description);
            }
        }


        return view('prod_support.urgent_patch_edit', [
            'getModule' => $getModule,
            'case' => $case,
            'getProblemtype' => $getProblemtype,
            'getRequesterType' => $getRequesterType,
            'returnValueFromCRafterEdit' => $returnValueFromCRafterEdit,
            'getProbDescription' => $getProbDescription,
            'checkScriptNotExist' => $checkScriptNotExist,
            'listScriptPatchData' => $listScriptPatchData,
            'usersManager' => $usersManager,
            'usersEndorsed' => $usersEndorsed,
            'getValueRequestType' => $getValueRequestType,
            'listDataCrmNumberOpenPatchingToday' => $getListDataCRMnumberOpenPatchingToday,
            'changeRequestData' => $changeRequestData,
            'dataFixPorting' => $dataFixPorting,
            'dataFixDetail' => $dataFixDetail,
            'listdata_upload' => $listdata_upload,
        ]);
    }

    public function updatePatchingListTodayUrgent(Request $request)
    {
        $orimodule = $request->portmodule1;
        $oriprobdesc = $request->prodesc1;
        $orirequester = $request->grouptype1;
        $oriprobtype = $request->probtype1;
        $date_endorse = null;
        $user = $this->getUserName();

        if ($request->probtype1 == 167) {
            $endorse = $request->endorse;
            $date_endorse = $request->date_endorse;
        } else {
            $endorse = '-';
        }

        if ($request->isMethod("POST") && $request->crmno1 != null) {
            $updateData = [
                'crm_no' => $request->crmno1,
                'redmineno' => $request->redmineid,
                'module' => $orimodule,
                'problem_description' => $oriprobdesc,
                'requester_type' => $orirequester,
                'problem_type' => $oriprobtype,
                'requester_name' => $request->orgname1,
                'requester_code' => $request->orgname1,
                'endorsement_by' => $endorse,
                'endorsement_date' => $date_endorse,
                'crm_tag_no' => $request->exampletags,
                'changed_by' => $user,
                'changed_date' => Carbon::now(),
                'remarks' => $request->remarksedit
            ];
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')
                ->where('data_fix_dtl_id', $request->datafixid)
                ->update($updateData);

            $detectCrCreatedOrNot = $this->getDetailsChangeRequest($request->datafixid);

            if ($this->listImpactAttached($request->datafixid) != null) {
                $impact_assessment = 'Please refer attachment';
            } else {
                $impact_assessment = $request->impactassement;
            }

            if ($detectCrCreatedOrNot == null) {
                //           insert CR 
                $checkbox1 = $request->request_type_checkbox;
                $chk = json_encode($checkbox1);
                $result = DB::connection('mysql_ep_prod_support')->table('ps_change_request')->insert([
                    [
                        'data_fix_dtl_id' => $request->datafixid,
                        'request_type' => $chk,
                        'request_category' => $request->radio_reqcategory,
                        'description' => $request->descreq,
                        'reason' => $request->reareq,
                        'requester_name' => $user,
                        'requester_date' => $request->requesteddate,
                        'recommender_name' => $request->recommendedby,
                        'recommender_date' => $request->recommendeddate,
                        'approver_name' => $request->approvedby,
                        'approver_date' => $request->approveddate,
                        'activity_plan' => $request->activityplan,
                        'expected_complete_date' => $request->expected,
                        'impact_category' => $request->radio_impact,
                        'remarks' => $request->remarksCR,
                        'system_affected' => $request->affectedat1,
                        'impact_assessment' => $impact_assessment,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => 'CR Created'
                    ],
                ]);
            }

            //        update cr
            else {
                $checkbox1 = $request->request_type_checkbox;
                $chk = json_encode($checkbox1);

                if ($request->isMethod("POST")) {
                    $updateData = [
                        'request_type' => $chk,
                        'request_category' => $request->radio_reqcategory,
                        'description' => $request->descreq,
                        'reason' => $request->reareq,
                        'recommender_name' => $request->recommendedby,
                        'approver_name' => $request->approvedby,
                        'approver_date' => $request->approveddate,
                        'activity_plan' => $request->activityplan,
                        'expected_complete_date' => $request->expected,
                        'impact_category' => $request->radio_impact,
                        'remarks' => $request->remarksCR,
                        'system_affected' => $request->affectedat1,
                        'impact_assessment' => $impact_assessment,
                        'changed_by' => $user,
                        'changed_date' => Carbon::Now()
                    ];
                    DB::connection('mysql_ep_prod_support')->table('ps_change_request')
                        ->where('changed_request_id', $request->changerequestid)
                        ->update($updateData);
                }
            }
        }
        return redirect()->action('ProdSupport\PsPatchingController@listDataPacthingUrgent');
    }

    public function createScriptPatchingUrgent()
    {
        $result = array(
            'status' => 'Invalid',
            'description' => ''
        );
        $dataFixDtlId = request()->dataFixDtlId;
        $scriptName = rtrim(request()->scriptName, '.sql');
        $script = request()->script;
        if (strlen($dataFixDtlId) == 0 || strlen($script) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'datafixId or script is required'
            );
        } else {
            $encodescript = base64_decode($script);
            $user = $this->getUserName();
            DB::connection('mysql_ep_prod_support')->table('ps_patch_script')->insert([
                [
                    'data_fix_dtl_id' => $dataFixDtlId,
                    'name' => $scriptName . '.sql',
                    'sql' => $encodescript,
                    'created_by' => $user,
                    'created_date' => Carbon::Now(),
                    'status' => 'Completed'
                ],
            ]);

            $result = array(
                'status' => 'Success',
                'description' => 'Insert',
                'dataHtml' => $this->htmlListScriptData($dataFixDtlId)
            );
        }
        return response()->json($result, 200);
    }

    public function checkRequestType($fixid, $list)
    {
        $value = ' ';
        if (in_array($fixid, $list)) {
            $value = 'X';
        }
        return $value;
    }

    public function checkRequestCategory($fixid, $list)
    {
        $value = ' ';
        if ($fixid == $list) {
            $value = 'X';
        }
        return $value;
    }

    public function checkDataExisting($data)
    {
        $value = ' ';
        if ($data == null) {
            return $value;
        }
    }

    public function createWordandFile($datafixid)
    {
        $folderName = null;
        $listData = $this->listCompletedDataFix($datafixid);
        if ($listData && count($listData) > 0) {
            $dataFixId = $listData[0]->data_fix_id;
            $carbonDatePorting = Carbon::parse($listData[0]->datetime_porting);
            if ($listData[0]->type_porting == 'S') {
                $folderName = "DataFixChangeRequest" . $carbonDatePorting->format('YmdHi');
            } else {
                $folderName = "DataFixUrgentChangeRequest" . $carbonDatePorting->format('YmdHi');
            }
            $folderPathToCreated = 'prod_support/export/porting/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName;
            $folderPath = 'app/' . $folderPathToCreated;


            //By default using Storage::makeDirectory , all folder created under storage/app/*
            Storage::makeDirectory($folderPathToCreated);
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                ->where('data_fix_id', $dataFixId)
                ->update([
                    'folder_name' => $folderName,
                    'folder_path' => $folderPath
                ]);
            foreach ($listData as $data) {
                $date1 = DateTime::createFromFormat('d/m/Y', $listData[0]->dateport)->setTime(0, 0, 0);
                $date2 = DateTime::createFromFormat('d/m/Y', '22/01/2024')->setTime(0, 0, 0);

                if ($date1 >= $date2) {
                    $templateProcessor = new TemplateProcessor(storage_path('prod_support/template/CHANGE AND RELEASE REQUEST FORM-V1.0_template.docx'));
                } else {
                    $templateProcessor = new TemplateProcessor(storage_path('prod_support/template/eP-ISMS-PSM-CHANGE-AND-RELEASE-REQUEST-FORM-V5.5_template.docx'));
                }

                $getValueRequestType = json_decode($data->request_type);
                if ($getValueRequestType) {
                    $templateProcessor->setValue('requester_type', $this->checkRequestType(172, $getValueRequestType));
                    $templateProcessor->setValue('requester_type1', $this->checkRequestType(173, $getValueRequestType));
                    $templateProcessor->setValue('requester_type2', $this->checkRequestType(174, $getValueRequestType));
                    $templateProcessor->setValue('requester_type3', $this->checkRequestType(175, $getValueRequestType));
                    $templateProcessor->setValue('requester_type4', $this->checkRequestType(176, $getValueRequestType));
                    $templateProcessor->setValue('requester_type5', $this->checkRequestType(177, $getValueRequestType));
                    $templateProcessor->setValue('requester_type6', $this->checkRequestType(178, $getValueRequestType));
                }
                //Modify contents:
                if ($data->crm_no) {
                    $templateProcessor->setValue('crm_no', $data->crm_no);
                } else {
                    $templateProcessor->setValue('crm_no', $data->redmineno);
                }
                $collect_script = array();
                $test = $this->listCompletedDataFix($datafixid);

                foreach ($test as $a) {
                    if ($a->crm_no === $data->crm_no) {
                        array_push($collect_script, $a->namescript);
                    }
                }
                $try = implode(" </w:t><w:br/><w:t> ", $collect_script);
                $templateProcessor->setValue('namescript', $try);

                $text = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->reason));
                $templateProcessor->setValue('reason', $text);

                $text2 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->impact_assessment));
                $templateProcessor->setValue('impact', $text2);

                $text3 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->description));
                $templateProcessor->setValue('problem_details', $text3);

                $text4 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->remarks));
                $templateProcessor->setValue('remarks', $text4);

                $text5 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->activity_plan));
                $templateProcessor->setValue('activity', $text5);

                $text6 = str_replace('&', '', preg_replace('~\R~u', '</w:t><w:br/><w:t>', $data->expected_complete_date));
                $templateProcessor->setValue('expected', $text6);

                $templateProcessor->setValue('ministry', $data->req);
                $templateProcessor->setValue('problem_type', $data->problem);
                $templateProcessor->setValue('modulecode', strtolower($data->modulecode));
                $templateProcessor->setValue('modulecode2', $data->modulecode);
                $templateProcessor->setValue('requester_name', $data->requester_name);
                $templateProcessor->setValue('requester_date', $data->requester_date);
                $templateProcessor->setValue('request_cat', $this->checkRequestCategory(179, $data->request_category));
                $templateProcessor->setValue('request_cat1', $this->checkRequestCategory(180, $data->request_category));
                $templateProcessor->setValue('impact1', $this->checkRequestCategory(181, $data->impact_category));
                $templateProcessor->setValue('impact2', $this->checkRequestCategory(182, $data->impact_category));
                $templateProcessor->setValue('recommender_name', $data->recommender_name);
                $templateProcessor->setValue('recommender_date', $data->recommender_date);
                $templateProcessor->setValue('approver_name', $data->approver_name);
                $templateProcessor->setValue('approver_date', $data->approver_date);

                try {
                    $nocrmredmine = null;

                    if ($data->redmineno) {
                        $nocrmredmine = $data->redmineno;
                    } else {
                        $nocrmredmine = $data->crm_no;
                    }

                    $folderPatchDetailPath = $folderPathToCreated . '/' . strtolower($data->modulecode) . '-' . $nocrmredmine;
                    $impact_attachment = null;
                    $impact_attachment = $this->listImpactAttached($listData[0]->ppsdatafixdtlid);

                    Storage::makeDirectory($folderPatchDetailPath);
                    if ($date1 >= $date2) {
                        $crPath = storage_path('app/' . $folderPatchDetailPath . '/' . 'CHANGE AND RELEASE REQUEST FORM-V1.0 ' . strtolower($data->modulecode) . '-' . $nocrmredmine . '.docx');
                    } else {
                        $crPath = storage_path('app/' . $folderPatchDetailPath . '/' . 'eP-ISMS-PSM-CHANGE AND RELEASE REQUEST FORM-V5.5 ' . strtolower($data->modulecode) . '-' . $nocrmredmine . '.docx');
                    }
                    $templateProcessor->saveAs($crPath);

                    $sqlPath = storage_path('app/' . $folderPatchDetailPath . '/' . $data->namescript);
                    File::put($sqlPath, $data->querysql);
                } catch (Exception $e) {
                    Log::error('{CLASS => ' . get_class($this) . '}{' . __FUNCTION__ . ' => ' . $e->getMessage());
                    echo $e;
                }
            }

            return $this->createFolder($dataFixId, $carbonDatePorting, $folderPath, $folderName);
        }
    }

    /**
     * 
     * @param type $dataFixId
     * @param type $carbonDatePorting
     * @param type $folderPath  Folder Path All files porting
     * @param type $folderName  Folder Name
     * @return type
     */
    public function createFolder($dataFixId, $carbonDatePorting, $folderPath, $folderName)
    {
        try {
            //Sample should be prod_support/export/porting/zip/2020/9/DataFixChangeRequest202009201300
            $folderZipPath = 'app/prod_support/export/porting/zip/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month;
            $files = glob(storage_path($folderPath)); //Get all folder n files under Path:FolderName
            $zipPathName = $folderZipPath . '/' . $folderName . '.zip';
            if (file_exists(storage_path($zipPathName))) {
                File::delete(storage_path($zipPathName));
            }
            \Zipper::make(storage_path($zipPathName))->add($files)->close();

            DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                ->where('data_fix_id', $dataFixId)
                ->update([
                    'folder_zip_name' => $folderName . '.zip',
                    'folder_zip_path' => $folderZipPath,
                    'changed_date' => Carbon::now(),
                    'changed_by' => auth()->user()->user_name
                ]);
            return response()->download(storage_path($zipPathName));
        } catch (Exception $ex) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
            Log::error($ex->getTraceAsString());
            return $ex->getMessage();
        }
    }

    public function downloadHistory($dataFixDtlId, $carbonDatePorting, $folderPath, $folderName, $crmfolder)
    {
        try {
            $folderZipPath = 'app/prod_support/export/porting/temp/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month;
            $files = glob(storage_path($folderPath)); //Get all folder n files under Path:FolderName
            $zipPathName = $folderZipPath . '/' . $folderName;
            $folderPathName = $folderZipPath . '/' . $crmfolder . '.zip';;
            if (file_exists(storage_path($folderPathName))) {
                File::delete(storage_path($folderPathName));
            }
            $impact_attachment = null;
            $impact_attachment = $this->listImpactAttached($dataFixDtlId);
            if ($impact_attachment != null) {
                \Zipper::make(storage_path($folderPathName))->add(storage_path('app/prod_support/export/porting/temp/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName . '/' . $crmfolder . '/' . $crmfolder))->add($impact_attachment[0]->folder_path)->close();
            } else {
                \Zipper::make(storage_path($folderPathName))->add($files)->close();
            }
            if (file_exists(storage_path($zipPathName))) {
                File::deleteDirectory(storage_path($zipPathName));
            }
            return response()->download(storage_path($folderPathName));
        } catch (Exception $ex) {
            Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
            Log::error($ex->getTraceAsString());
            return $ex->getMessage();
        }
    }

    public function deleteUploadAttachment()
    {

        $result = array(
            'status' => 'Error',
            'description' => 'Invalid'
        );

        $dataFixDtlId = request()->dataFixDtlId;
        $attachId = request()->attachId;
        $fileName = request()->fileName;

        $dataAll = $this->listImpact($dataFixDtlId, $attachId);
        if ($dataAll[0]->redmineno) {
            $nocrmredmine = $dataAll[0]->redmineno;
        } else {
            $nocrmredmine = $dataAll[0]->crm_no;
        }
        if ($dataAll != null) {
            try {
                //                per crm no
                $folderPath_impact = ($dataAll[0]->folder_path);
                $PathName_impact = $folderPath_impact . '/' . $dataAll[0]->file_name;
                if (file_exists($PathName_impact)) {
                    File::delete($PathName_impact);
                }
                //                per data patch
                $folderPath = ($dataAll[0]->folder);
                $PathName = $folderPath . '/' . strtolower($dataAll[0]->modulecode) . '-' . $nocrmredmine . '/' . $dataAll[0]->file_name;
                if (file_exists(storage_path($PathName))) {
                    File::delete(storage_path($PathName));
                }
            } catch (Exception $ex) {
                Log::error(self::class . ' Error ... ' . __FUNCTION__ . ' ERROR' . $ex->getMessage());
                Log::error($ex->getTraceAsString());
                return $ex->getMessage();
            }
        }
        if (strlen($attachId) == 0 || strlen($dataFixDtlId) == 0) {
            $result = array(
                'status' => 'Error',
                'description' => 'attachId and dataFixDtlId is required'
            );
        } else {

            DB::connection('mysql_ep_prod_support')->table('ps_impact_attachment')
                ->where('impact_attachment_id', $attachId)
                ->delete();
            $result = array(
                'status' => 'Success',
                'description' => 'Delete',
                'data_upload' => $this->htmlListUploadAttachment($dataFixDtlId)
            );
        }
        return response()->json($result, 200);
    }

    public function uploadAttachment(Request $request)
    {
        $crm_id = request()->crm_id;
        $file = $request->file('upload_attachment');
        $path_name = $file->getRealPath();
        $file_name = $file->getClientOriginalName();
        $file_type = substr($file_name, strrpos($file_name, '.') + 1);
        $attachId = request()->attachId;

        $folder_path = storage_path('prod_support/export/porting/impact_attachment/' . $crm_id);
        Storage::makeDirectory($folder_path);
        $path = Storage::putFileAs('prod_support/export/porting/impact_attachment/' . $crm_id, $file, 'attachment - ' . $file_name);

        $user = $user = auth()->user()->first_name;

        if ($request != null) {
            DB::connection('mysql_ep_prod_support')->table('ps_impact_attachment')->insert([
                [
                    'data_fix_dtl_id' => $crm_id,
                    'file_name' => 'attachment - ' . $file_name,
                    'folder_path' => storage_path('app/prod_support/export/porting/impact_attachment/' . $crm_id),
                    'created_by' => $user,
                    'created_date' => Carbon::Now()
                ],
            ]);

            $folderName = null;
            $listData = $this->listImpactAll($crm_id);
            if ($listData && count($listData) > 0) {
                $dataFixId = $listData[0]->data_fix_id;
                $carbonDatePorting = Carbon::parse($listData[0]->datetime_porting);
                if ($listData[0]->type_porting == 'S') {
                    $folderName = "DataFixChangeRequest" . $carbonDatePorting->format('YmdHi');
                } else {
                    $folderName = "DataFixUrgentChangeRequest" . $carbonDatePorting->format('YmdHi');
                }
                $folderPathToCreated = 'prod_support/export/porting/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName;
                $folderPath = 'app/' . $folderPathToCreated;

                //By default using Storage::makeDirectory , all folder created under storage/app/*
                Storage::makeDirectory($folderPathToCreated);

                try {
                    $nocrmredmine = null;

                    if ($listData[0]->redmineno) {
                        $nocrmredmine = $listData[0]->redmineno;
                    } else {
                        $nocrmredmine = $listData[0]->crm_no;
                    }
                    $folderPatchDetailPath = $folderPathToCreated . '/' . strtolower($listData[0]->modulecode) . '-' . $nocrmredmine;

                    Storage::makeDirectory($folderPatchDetailPath);

                    $path1 = Storage::putFileAs('prod_support/export/porting/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName . '/' . strtolower($listData[0]->modulecode) . '-' . $nocrmredmine, $file, 'attachment - ' . $file_name);
                } catch (Exception $e) {
                    Log::error('{CLASS => ' . get_class($this) . '}{' . __FUNCTION__ . ' => ' . $e->getMessage());
                    echo $e;
                }
            }
        }
        $result = array(
            'status' => 'Success',
            'description' => 'Insert',
            'data_upload' => $this->htmlListUploadAttachment($crm_id)
        );

        return response()->json($result, 200);
    }

    protected function htmlListUploadAttachment($dataFixDtlId)
    {
        $listPatch = DB::connection('mysql_ep_prod_support')->table('ps_impact_attachment')
            ->where('data_fix_dtl_id', $dataFixDtlId)
            ->orderBy('created_date', 'asc')
            ->get();

        $html = "";
        $counter = 1;
        foreach ($listPatch as $key => $row) {
            $counter = $key + 1;
            $html = $html .
                "<div class='panel panel-default'>" .
                "<div class='panel-heading'>" .
                "<h4 class='panel-title'>$counter <i class='fa fa-angle-right'></i>" .
                "<a class='accordion-toggle' data-parent='#uploadAttached_$row->impact_attachment_id' href='#uploadAttached_$row->impact_attachment_id'> $row->file_name</a>" .
                "<div class='pull-right'>
                           <a href='javascript:void(0)' data-toggle='tooltip'
                           onclick='onClickDeleteAttachment(this)' 
                           data-impact_attachment_id='$row->impact_attachment_id' data-fix-dtl-id='$row->data_fix_dtl_id'
                           title='Delete this attachment' data-original-title=''><i class='fa fa-times'></i></a>  
                        </div>" .
                "</h4>" .
                "</div>" .
                "</div>" .
                "</div>";
        }

        return $html;
    }

    public function sendEmailtoApprover()
    {
        $collectResult = collect();
        $dataFixId = request()->dataFixId;
        $approverEmail = request()->emailApprovers;

        if (strlen($dataFixId) == 0 || strlen($approverEmail) == 0) {
            $collectResult->put('status_code', 'ERROR');
            $collectResult->put('status_desc', 'Invalid Parameters');
            return response()->json($collectResult, 200);
        }

        $listEmailId = explode(",", $approverEmail);
        $list = PsDataFixUser::whereIn('data_fix_user_id', $listEmailId)->get();
        $emailsSentTo = array();
        if ($list->count() > 0) {
            $emailsSentTo = $list->pluck('email')->toArray();
        } else {
            $collectResult->put('status_code', 'ERROR');
            $collectResult->put('status_desc', 'No emails found by list ID: ' . $approverEmail);
            return response()->json($collectResult, 200);
        }
        $emailsSentCc = array();
        $emailsSentToMo = array();
        $checkInternalExternalUser = $this->getDetailDataFixPorting($dataFixId);
        $listMO = null;
        $emailsSentToMo = null;
        if ($checkInternalExternalUser->urgent_type == 'internal') {
            $listCC = PsDataFixUser::where('is_group_cc', 1)->where('record_status', 1)->get();
        } else {
            $listCC = PsDataFixUser::where('is_group_cc', 1)->where('record_status', 1)->orwhere('is_ep_group_cc', 1)->where('record_status', 1)->get();
            $listMO = PsDataFixUser::where('is_mo_group', 1)->where('record_status', 1)->get();
        }
        if ($listCC->count() > 0) {
            $emailsSentCc = $listCC->pluck('email')->toArray();
        }

        if ($listMO != null) {
            if ($listMO->count() > 0) {
                $emailsSentToMo = $listMO->pluck('email')->toArray();
            }
        } else {
            $emailsSentToMo = null;
        }

        $dataAll = $this->listEmailDataFix($dataFixId);
        if ($dataAll && count($dataAll) > 0 && count($emailsSentTo) > 0 && ($dataAll[0]->is_sent === null || $dataAll[0]->is_sent === 0)) {
            $text = null;
            foreach ($dataAll as $list) {
                if ($list->endorsement_by == 'Tuan Haji Khairuddin Bin Bakar - TSK (S)' && ($list->id == 206 || $list->id == 208)) {
                    $text = '*Endorsement By: Tuan Haji Khairuddin Bin Bakar - TSK (S) - Kelulusan pukal berkenaan pindaan nombor pendaftaran SSM melalui surat kelulusan MOF.BPK(S)600-22/8/12 JLD 8(11)';
                }
            }
            $user = $this->getUserName();
            $team = $this->createTemplateEmailUserName();
            $this->createWordandFile($dataFixId);
            $carbonDatePorting = Carbon::parse($dataAll[0]->datetime_porting);
            $folderName = "DataFixChangeRequest" . $carbonDatePorting->format('YmdHi');

            if ($dataAll[0]->type_porting == 'S') {
                $folderName = "DataFixChangeRequest" . $carbonDatePorting->format('YmdHi');
            } else {
                $folderName = "DataFixUrgentChangeRequest" . $carbonDatePorting->format('YmdHi');
            }

            $folderPathToCreated = 'prod_support/export/porting/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month . '/' . $folderName;
            $folderPath = 'app/' . $folderPathToCreated;
            $folderZipPath = 'app/prod_support/export/porting/zip/' . $carbonDatePorting->year . '/' . $carbonDatePorting->month;
            $files = glob(storage_path($folderPath));
            $zipPathName = $folderZipPath . '/' . $folderName . '.zip';
            if (file_exists(storage_path($zipPathName))) {
                File::delete(storage_path($zipPathName));
            }
            \Zipper::make(storage_path($zipPathName))->add($files)->close();

            if ($emailsSentToMo != null) {
                $data = array(
                    "to" => array_merge($emailsSentTo, $emailsSentToMo),
                    "cc" => $emailsSentCc,
                    "subject" => $dataAll[0]->port,
                    "savefile" => storage_path($zipPathName),
                );
            } else {
                $data = array(
                    "to" => $emailsSentTo,
                    "cc" => $emailsSentCc,
                    "subject" => $dataAll[0]->port,
                    "savefile" => storage_path($zipPathName),
                );
            }
            try {

                // Using diff account mail provider. 
                $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
                    ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
                    ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
                    ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

                $mailer = app(\Illuminate\Mail\Mailer::class);
                $mailer->setSwiftMailer(new \Swift_Mailer($transport));
                $mail = $mailer
                    ->send('prod_support.email_to_approver', ['setdata' => $dataAll, 'user' => $user, 'team' => $team, 'txt' => $text], function ($m) use ($data) {
                        $m->from(Config::get('constant.email_sender_datafix'), auth()->user()->first_name);
                        //$m->replyTo(Auth()->user()->user_name . '@commercedc.com.my', auth()->user()->first_name);
                        $toEmails = $data["to"];
                        array_push($toEmails, Auth()->user()->user_name . '@commercedc.com.my');
                        $m->to($toEmails)
                            ->cc($data["cc"])
                            ->subject($data["subject"])
                            ->attach($data["savefile"]);
                    });
                if ($emailsSentToMo != null) {
                    $updateData = [
                        'is_sent' => 1,
                        'status' => 'Closed',
                        'sent_to' => json_encode($emailsSentTo) . json_encode($emailsSentToMo)
                    ];
                } else {
                    $updateData = [
                        'is_sent' => 1,
                        'status' => 'Closed',
                        'sent_to' => json_encode($emailsSentTo)
                    ];
                }
                DB::connection('mysql_ep_prod_support')->table('ps_data_fix')
                    ->where('data_fix_id', $dataFixId)
                    ->update($updateData);

                $collectResult->put('status_code', 'SUCCESS');
                $collectResult->put('status_desc', 'Successfully send email to : ' . json_encode($emailsSentTo));
                return response()->json($collectResult, 200);
            } catch (\Exception $e) {
                Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR']);
                Log::error($e->getTraceAsString());
                $collectResult->put('status_code', 'ERROR');
                $collectResult->put('status_desc', 'Error : ' . $e->getMessage() . ' >>>  send email to : ' . json_encode($emailsSentTo));
                return response()->json($collectResult, 200);
            }
        } else {
            $collectResult->put('status_code', 'ERROR');
            $collectResult->put('status_desc', 'Not in criteria! Already sent email');
            return response()->json($collectResult, 200);
        }

        return response()->json($collectResult, 200);
    }

    public function getUserName()
    {

        $user = 'Safinah Salleh';
        $userReal = auth()->user()->first_name;
        if ($userReal == 'Shahril Anuar Ismail') {
            $user;
        } else if ($userReal == 'Sharudin Abu Shah') {
            $user = 'MOHD YUSRI BIN ABD JABAR';
        } else {
            $user = auth()->user()->first_name;
        }

        return $user;
    }

    public function createTemplateEmailUserName()
    {
        $team = null;
        $UsersMiddleware = array("Nurun Nazifah", "Aminah Binti Abdul Muin", "IQBAL FIKRI MOHAMED MISMAN", "MOHD YUSRI BIN ABD JABAR", "Mohd Shamsul Bin Amerudin");
        $UsersProdSupport = array("Norhasfarine", "Safinah Salleh", "Siti Norazreen Bt Abdullah", "MUHD AZWANDY BIN IJMAEN", "Aema Binti Ismail", "AKMAL SAUFI BIN MASHAR", "Putera Mohd Hafizi Helmi", "Fauziah bt Mohd Mukhtar", "Amie Affriza");
        if ($UsersProdSupport) {
            $team = "Production Support";
        } elseif ($UsersMiddleware) {
            $team = "Middleware Team";
        } else {
            $team = '';
        }

        return $team;
    }

    public function downloadReport(Request $request)
    {
        $listReporting = null;
        $getMonthToDownload = null;
        $listYear = $this->findYear();
        if ($request->port_type !== null || $request->by_year !== null) {
            $listReporting = $this->listReportByYear($request->port_type, $request->by_year);
            if ($listReporting[0]->type_porting == 'S') {
                $getTypePorting = 'Normal Porting';
            } else {
                $getTypePorting = 'Urgent Porting';
            }
            $getMonth = null;
            $getYearToDownload = $listReporting[0]->year;
            if ($request->month !== null) {
                $listReporting = $this->listReportByMonth($request->port_type, $request->by_year, $request->month);
                if ($listReporting != null) {
                    $getMonth = $listReporting[0]->month;
                }
                if ($getMonth == '01') {
                    $getMonthToDownload = 'January';
                } else if ($getMonth == '02') {
                    $getMonthToDownload = 'February';
                } else if ($getMonth == '03') {
                    $getMonthToDownload = 'March';
                } else if ($getMonth == '04') {
                    $getMonthToDownload = 'April';
                } else if ($getMonth == '05') {
                    $getMonthToDownload = 'May';
                } else if ($getMonth == '06') {
                    $getMonthToDownload = 'June';
                } else if ($getMonth == '07') {
                    $getMonthToDownload = 'July';
                } else if ($getMonth == '08') {
                    $getMonthToDownload = 'August';
                } else if ($getMonth == '09') {
                    $getMonthToDownload = 'September';
                } else if ($getMonth == '10') {
                    $getMonthToDownload = 'October';
                } else if ($getMonth == '11') {
                    $getMonthToDownload = 'November';
                } else if ($getMonth == '12') {
                    $getMonthToDownload = 'December';
                }
            }
            $collectlistReporting = collect($listReporting)->groupBy('year');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Nextgen - Data Fix Details';

            Excel::create($fileName, function ($excel) use ($collectlistReporting, $getTypePorting, $getYearToDownload, $getMonthToDownload) {
                $excel->setTitle('Nextgen - Data Fix Details');

                $excel->sheet($getTypePorting . ' ' . $getYearToDownload . ' ' . $getMonthToDownload, function ($sheet) use ($collectlistReporting, $getTypePorting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));
                        if ($getTypePorting == 'Normal Porting') {
                            $sheet->setColumnFormat(array(
                                'A' => '@',
                                'B' => '@',
                                'C' => '@',
                                'D' => '@',
                                'E' => '@',
                                'F' => '@',
                                'G' => '@',
                                'H' => '@',
                                'I' => '@',
                            ));
                        } elseif ($getTypePorting == 'Urgent Porting') {
                            $sheet->setColumnFormat(array(
                                'A' => '@',
                                'B' => '@',
                                'C' => '@',
                                'D' => '@',
                                'E' => '@',
                                'F' => '@',
                                'G' => '@',
                                'H' => '@',
                                'I' => '@',
                                'J' => '@',
                            ));
                        }

                        // $sheet->row(1, array(
                        //     'Bil', 'Date/Time', 'CRM No', 'Redmine No', 'Module', 'Problem Description (from CRM)', 'Requester (supplier or PTJ, CDC, BPK)', 'Problem Type', 'Endorsement By', 'Urgent Type'
                        // ));

                        if ($getTypePorting == 'Normal Porting') {
                            $sheet->row(1, array(
                                'Bil',
                                'Date/Time',
                                'CRM No',
                                'Redmine No',
                                'Module',
                                'Problem Description (from CRM)',
                                'Requester (supplier or PTJ, CDC, BPK)',
                                'Problem Type',
                                'Endorsement By'
                            ));
                        } elseif ($getTypePorting == 'Urgent Porting') {
                            $sheet->row(1, array(
                                'Bil',
                                'Date/Time',
                                'CRM No',
                                'Redmine No',
                                'Module',
                                'Problem Description (from CRM)',
                                'Requester (supplier or PTJ, CDC, BPK)',
                                'Problem Type',
                                'Endorsement By',
                                'Urgent Type'
                            ));
                        }


                        $sheet->row(1, function ($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:G1', function ($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        //                    foreach ($collectlistReporting as $list){
                        if ($getTypePorting == 'Normal Porting') {
                            foreach ($list as $data => $obj) {
                                //                            dump($list);
                                $sheet->row(
                                    $count,
                                    array(
                                        ++$data,
                                        $obj->datetime_porting,
                                        $obj->crm_no,
                                        $obj->redmineno,
                                        $obj->module,
                                        $obj->problem_description,
                                        $obj->requester_name,
                                        $obj->problem_type,
                                        $obj->endorsement_by,
                                    )
                                );
                                $count++;
                            }
                        } elseif ($getTypePorting == 'Urgent Porting') {
                            foreach ($list as $data => $obj) {
                                                        //    dump($list);
                                $sheet->row(
                                    $count,
                                    array(
                                        ++$data,
                                        $obj->datetime_porting,
                                        $obj->crm_no,
                                        $obj->redmineno,
                                        $obj->module,
                                        $obj->problem_description,
                                        $obj->requester_name,
                                        $obj->problem_type,
                                        $obj->endorsement_by,
                                        $obj->urgent_type,
                                    )
                                );
                                $count++;
                            }
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }

        return view('prod_support.reporting', [
            'listYear' => $listYear,
        ]);
    }

    public function createNewPortingAfterCancelled(Request $request, $func, $id, $type)
    {

        if ($func == 'byCRM' && $type == 'S') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetailScheduledByToday();
            if ($checkIfHaveAnyValidPorting == null) {
                $this->createPortSchedulePorting($request);
            } else {
                $this->createNewRepatchSchedule($id);
            }
        }
        if ($func == 'byCRM' && $type == 'U') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetail();
            if ($checkIfHaveAnyValidPorting == null) {
                $this->createPort($request);
            } else {
                $this->createNewRepatchUrgent($id);
            }
        }
        if ($func == 'byPort' && $type == 'S') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetailScheduledByToday();
            if ($checkIfHaveAnyValidPorting == null) {
                $this->createPortSchedulePorting($request);
            } else {
                $listPatchingByPorting = $this->getListDetailsDataPatch($id);
                foreach ($listPatchingByPorting as $list) {
                    $id = $list->data_fix_dtl_id;
                    $this->createNewRepatchSchedule($id);
                }
            }
        }
        if ($func == 'byPort' && $type == 'U') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetail();
            if ($checkIfHaveAnyValidPorting == null) {
                $this->createPort($request);
            } else {
                $listPatchingByPorting = $this->getListDetailsDataPatch($id);
                foreach ($listPatchingByPorting as $list) {
                    $id = $list->data_fix_dtl_id;
                    $this->createNewRepatchUrgent($id);
                }
            }
        }
    }

    protected function createNewRepatchSchedule($id)
    {
        $listPatchDetailbyScheduledToday = $this->getPatchDetailScheduledNormalByToday();

        if ($listPatchDetailbyScheduledToday[0]->status == 'Open') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetailScheduledByToday();
            $getMaxFixId = $this->getMaxSeqFixId();
            $this->insertNewRepatchDetail($checkIfHaveAnyValidPorting, $id, $getMaxFixId);
        } else {
            return 'PORTING CLOSED';
        }
    }

    protected function createNewRepatchUrgent($id)
    {
        $listPatchDetailbyUrgent = $this->getOpenPatchDetail();

        if ($listPatchDetailbyUrgent[0]->status == 'Open') {
            $checkIfHaveAnyValidPorting = $this->getOpenPatchDetail();
            $getMaxFixId = $this->getMaxSeqFixIdUrgent();
            $this->insertNewRepatchDetail($checkIfHaveAnyValidPorting, $id, $getMaxFixId);
        } else {
            return 'PORTING CLOSED';
        }
    }

    protected function insertNewRepatchDetail($checkIfHaveAnyValidPorting, $id, $getMaxFixId)
    {
        $user = $this->getUserName();
        $getMaxBill = $this->getMaxBillSeq();

        $getPatchDetails = $this->getDetailsDataPatch($id);
        $getCRCreation = $this->getDetailCRCreationForRepatch($id);
        $getScript = $this->getDetailScriptForRepatch($id);
        if ($getMaxBill->bill == null) {
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => '1', 'datetime_porting' => $checkIfHaveAnyValidPorting[0]->datetime_porting, 'crm_no' => $getPatchDetails->crm_no, 'redmineno' => $getPatchDetails->redmineno, 'module' => $getPatchDetails->module, 'problem_description' => $getPatchDetails->problem_description, 'requester_type' => $getPatchDetails->requester_type, 'requester_name' => $getPatchDetails->requester_name, 'requester_code' => $getPatchDetails->requester_code, 'endorsement_by' => $getPatchDetails->endorsement_by, 'endorsement_date' => $getPatchDetails->endorsement_date, 'problem_type' => $getPatchDetails->problem_type, 'created_by' => $user, 'created_date' => Carbon::now(), 'crm_tag_no' => $getPatchDetails->crm_tag_no, 'remarks' => $getPatchDetails->remarks, 'status' => 'Created'],
            ]);
            $findMaxDataFixId = $this->getMaxDataFixDetailId();
            if ($getCRCreation != null) {
                DB::connection('mysql_ep_prod_support')->table('ps_change_request')->insert([
                    [
                        'data_fix_dtl_id' => $findMaxDataFixId->data_fix_dtl_id,
                        'request_type' => $getCRCreation->request_type,
                        'request_category' => $getCRCreation->request_category,
                        'description' => $getCRCreation->description,
                        'reason' => $getCRCreation->reason,
                        'requester_name' => $user,
                        'requester_date' => $getCRCreation->requester_date,
                        'recommender_name' => $getCRCreation->recommender_name,
                        'recommender_date' => $getCRCreation->recommender_date,
                        'approver_name' => $getCRCreation->approver_name,
                        'approver_date' => $getCRCreation->approver_date,
                        'activity_plan' => $getCRCreation->activity_plan,
                        'expected_complete_date' => $getCRCreation->expected_complete_date,
                        'impact_category' => $getCRCreation->impact_category,
                        'remarks' => $getCRCreation->remarks,
                        'system_affected' => $getCRCreation->system_affected,
                        'impact_assessment' => $getCRCreation->impact_assessment,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => $getCRCreation->status
                    ],
                ]);
            }
            if ($getScript != null) {
                DB::connection('mysql_ep_prod_support')->table('ps_patch_script')->insert([
                    [
                        'data_fix_dtl_id' => $findMaxDataFixId->data_fix_dtl_id,
                        'name' => $getScript->name . '.sql',
                        'sql' => $getScript->sql,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => 'Completed'
                    ],
                ]);
            }
        } else {
            DB::connection('mysql_ep_prod_support')->table('ps_data_fix_dtl')->insert([
                ['data_fix_id' => $getMaxFixId->port, 'bill_seq' => $getMaxBill->bill + 1, 'datetime_porting' => $checkIfHaveAnyValidPorting[0]->datetime_porting, 'crm_no' => $getPatchDetails->crm_no, 'redmineno' => $getPatchDetails->redmineno, 'module' => $getPatchDetails->module, 'problem_description' => $getPatchDetails->problem_description, 'requester_type' => $getPatchDetails->requester_type, 'requester_name' => $getPatchDetails->requester_name, 'requester_code' => $getPatchDetails->requester_code, 'endorsement_by' => $getPatchDetails->endorsement_by, 'endorsement_date' => $getPatchDetails->endorsement_date, 'problem_type' => $getPatchDetails->problem_type, 'created_by' => $user, 'created_date' => Carbon::now(), 'crm_tag_no' => $getPatchDetails->crm_tag_no, 'remarks' => $getPatchDetails->remarks, 'status' => 'Created'],
            ]);
            $findMaxDataFixId = $this->getMaxDataFixDetailId();
            if ($getCRCreation != null) {
                DB::connection('mysql_ep_prod_support')->table('ps_change_request')->insert([
                    [
                        'data_fix_dtl_id' => $findMaxDataFixId->data_fix_dtl_id,
                        'request_type' => $getCRCreation->request_type,
                        'request_category' => $getCRCreation->request_category,
                        'description' => $getCRCreation->description,
                        'reason' => $getCRCreation->reason,
                        'requester_name' => $user,
                        'requester_date' => $getCRCreation->requester_date,
                        'recommender_name' => $getCRCreation->recommender_name,
                        'recommender_date' => $getCRCreation->recommender_date,
                        'approver_name' => $getCRCreation->approver_name,
                        'approver_date' => $getCRCreation->approver_date,
                        'activity_plan' => $getCRCreation->activity_plan,
                        'expected_complete_date' => $getCRCreation->expected_complete_date,
                        'impact_category' => $getCRCreation->impact_category,
                        'remarks' => $getCRCreation->remarks,
                        'system_affected' => $getCRCreation->system_affected,
                        'impact_assessment' => $getCRCreation->impact_assessment,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => $getCRCreation->status
                    ],
                ]);
            }
            if ($getScript != null) {
                DB::connection('mysql_ep_prod_support')->table('ps_patch_script')->insert([
                    [
                        'data_fix_dtl_id' => $findMaxDataFixId->data_fix_dtl_id,
                        'name' => $getScript->name,
                        'sql' => $getScript->sql,
                        'created_by' => $user,
                        'created_date' => Carbon::Now(),
                        'status' => 'Completed'
                    ],
                ]);
            }
        }
    }
}
