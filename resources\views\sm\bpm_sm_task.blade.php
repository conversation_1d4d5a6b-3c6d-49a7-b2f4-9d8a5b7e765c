@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')
<style>
    code[class^="language-"], code[class*=" language-"], pre[class^="language-"], pre[class*=" language-"] {
        white-space: pre-line;
    }
</style>
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>SM BPM Task<br>
                <small>Create task BPM for SM</small>
            </h1>
        </div>
    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="row" >
                <div class="col-sm-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>Search</strong> SM Application Number</h2>
                        </div>

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <p class="text-danger bolder alert-success">
                        <h5 class=""><strong>Is Processing Fee Required</strong></h5> 
                            When choose Refire Task, Processing Fee is required?
                            Kindly check on tracking diary status :-  <br />
                            1) If document status to Pending Payment Processing Fee, use Refire Task and choose Is Processing Fee Required <b>'Required'</b> <br />
                            2) If document status for Pending Payment Processing Fee already <b>Completed</b> or Task Pending to SDO or Task Pending to Approval.  Select Refire Task and choose Is Processing Fee Required <b>'Not Required / Skip' .</b>  <br />
                               &nbsp;&nbsp;After refire is success. Kindly check on instance BPM to jump exact task. (##some data need to change in payload)<br />
                        </p>
                        <form action="{{url('/bpm/sm/task/find')}}" method="post" class="form-horizontal form-bordered" >
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="input-group">
                                    @if(isset($appl_no))
                                    <input type="text" id="appl_no" name="appl_no" class="form-control" placeholder="Application Number .. SM modules only" 
                                           required="required" value="{{$appl_no}}">
                                    @else
                                    <input type="text" id="appl_no" name="appl_no" class="form-control" placeholder="Application Number .. SM modules only" 
                                           required="required" value="{{old('appl_no')}}">
                                    @endif
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  Application Number <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="action_task_sm" name="action_task_sm" class="select-chosen" data-placeholder="Choose Type Action Task .." style="width: 250px;" required="required">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="initiate-task" @if(old('action_task_sm') === "initiate-task") selected @endif>Initiate Task</option>
                                        <option value="refire-task"   @if(old('action_task_sm') === "refire-task") selected @endif>Refire Task</option>
                                        <option value="refire-payment-softcert-task"   @if(old('action_task_sm') === "refire-payment-softcert-task") selected @endif>Refire Payment Softcert Task</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-gears"></i>  Choose Type Action Task <span class="text-danger">*</span></span>
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <div class="input-group">
                                    
                                    <!-- Chosen plugin (class is initialized in js/app.js -> uiInit()), for extra usage examples you can check out http://harvesthq.github.io/chosen/ -->
                                    <select id="is_payment_required" name="is_payment_required" class="select-chosen" data-placeholder="When choose Refire Task, Processing Fee is required? " style="width: 250px;">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="true" @if(old('is_payment_required') === "true") selected @endif>Required</option>
                                        <option value="false"   @if(old('is_payment_required') === "false") selected @endif>Not Required / Skip</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-dollar"></i>  Is Processing Fee Required </span>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="input-group">
                                    <select id="is_trigger_bpm" name="is_trigger_bpm" class="select-chosen" 
                                        data-placeholder="Choose true, Auto trigger BPM refire task. False to show payload only" style="width: 250px;">
                                        <option></option><!-- Required for data-placeholder attribute to work with Chosen plugin -->
                                        <option value="true" @if(old('is_trigger_bpm') === "true") selected @endif>YES</option>
                                        <option value="false"   @if(old('is_trigger_bpm') === "false") selected @endif>FALSE</option>
                                    </select>
                                    <span class="input-group-addon"><i class="fa fa-dollar"></i>  Is Trigger BPM </span>
                                </div>
                            </div>
                            
  
                            
                            <div class="form-group form-actions">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-sm btn-info pull-right">Submit</button>
                                </div>
                            </div>
                        </form>
                        
                        @if($listdata != null && count($listdata) > 0)
                        
                        <div class="alert alert-success">
                            <pre class="line-numbers">
                                <code class="language-markup">{{htmlentities(json_encode($listdata)) }}</code>
                            </pre>
                            @if($payload)
                            <h3>Payload</h3>
                            <h5><strong>Login ID : {{$listdata['result']['task_performer']}}</strong></h5>
                            <pre class="line-numbers">
                                <code class="language-markup">{{$payload}}</code>
                            </pre>
                            @endif
                        </div>

                        @endif

                    </div>

                </div>
            </div>

        </div>
    </div>
    



@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



