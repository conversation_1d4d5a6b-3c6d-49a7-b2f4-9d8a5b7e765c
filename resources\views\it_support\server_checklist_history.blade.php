@extends('layouts.guest-dash')
@php
    use Carbon\Carbon;
@endphp
@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/it_support/server/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li class="active">
                <a href="{{ url('/it_support/server/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/server/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>


    <div class="block">
        <form id ="it_support_checklist" action="{{ url('/it_support/server/checklist/history') }}" method="post">
            {{ csrf_field() }}

            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            <input readonly="" id="date_search" name="date_search" type="text" value="{{ $dateNote }}"
                class="form-control" style="width: 700px; display:none">

            <div class='text-center' style="background: #ffffff; padding: 5px;">
                <button type="button" id="button_submit_M" name="button_submit_M" value = 'M'
                    class="btn btn-sm btn-info text-center">
                    <div class="h5 mb-0" style="font-weight: 800">Morning Shift</div>
                </button>
                @if ($dateNote < Carbon::createFromFormat('Y-m-d', '2024-04-18'))
                    <button type="button" id="button_submit_E" name="button_submit_E" value = 'E'
                        class="btn btn-sm btn-info text-center">
                        <div class="h5 mb-0" style="font-weight: 800">Night Shift</div>
                    </button>
                @endif
            </div>

            <div id="Morning">
                <div class="table-options" id="clearfix_M">
                    <div class="text-center tab_M" style="padding: 5px; display:none">
                        <div class="block-options panel-heading">
                            <h4><strong>
                                    <span color="black" id="header-table_M"></span>
                                    <span color="black" id="header-table-date_M"></span>
                                </strong></h4>
                        </div>
                        <ul class="nav nav-tabs" data-toggle="tabs">
                            <li id="kvdcId_M" name="kvdcId_M" style="display:none"><a href="#kvdc">KVDC</a></li>
                            <li id="netmyneId_M" name="netmyneId_M" style="display:none"><a href="#netmyne">NETMYNE</a></li>
                            <li id="helangId_M" name="helangId_M" style="display:none"><a href="#helang">HELANG</a></li>
                        </ul>
                    </div>

                    <label id="menu_button" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left netmynetab_M" data-toggle="buttons"
                            style="display:none">
                        </div>
                    </label>

                    <label id="menu_button_helang" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left helangtab_M" data-toggle="buttons"
                            style="display:none">
                        </div>
                    </label>

                    <label id="menu_button_kvdc" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left kvdc_M" data-toggle="buttons" style="display:none">
                        </div>
                    </label>
                </div>
            </div>

            <div id="Evening">
                <div class="table-options" id="clearfix_E" style="padding: 5px; display:none">
                    <div class="text-center tab_E" style="padding: 5px; display:none">
                        <div class="block-options panel-heading">
                            <h4><strong>
                                    <span color="black" id="header-table_E"></span>
                                    <span color="black" id="header-table-date_E"></span>
                                </strong></h4>
                        </div>
                        <ul class="nav nav-tabs" data-toggle="tabs">
                            <li id="kvdcId_E" name="kvdcId_E" style="display:none"><a href="#kvdc">KVDC</a></li>
                            <li id="netmyneId_E" name="netmyneId_E" style="display:none"><a
                                    href="#netmyne">NETMYNE</a></li>
                            <li id="helangId_E" name="helangId_E" style="display:none"><a href="#helang">HELANG</a></li>
                        </ul>
                    </div>

                    <label id="menu_button_E" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left netmynetab_E" data-toggle="buttons"
                            style="display:none">
                        </div>
                    </label>

                    <label id="menu_button_helang_E" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left helangtab_E" data-toggle="buttons"
                            style="display:none">
                        </div>
                    </label>

                    <label id="menu_button_kvdc_E" data-toggle="tooltip">
                        <div class="btn-group btn-group-sm pull-left kvdc_E" data-toggle="buttons" style="display:none">
                        </div>
                    </label>
                </div>
            </div>
            <div class="text-center spinner-loading" style="padding: 20px; display:none">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>
            <div class="table-responsive">
                <table id="server_datatable" class="table table-vcenter table-condensed table-bordered"></table>
            </div>

            <div class="text-center button_update" style="display:none">
                <button type="submit" style="background-color: #414770; color: white"
                    class="btn btn btn-primary">Update</button>
            </div>
            <div id="total-checkbox-count"></div>
        </form>
    </div>
    {{-- <div class="block panel-heading notes_form" style="display:none">@include('it_support.page_status', ['page' => 'server'])</div> --}}
    @if (in_array($section, ['server_check', 'status_check']))
        @include('it_support.page_status', [
            'page' => $section == 'server_check' ? 'server' : 'status',
            'location' => old('radio_reqcategory', $section == 'server_check' ? 'R' : 'Y'),
        ])
    @endif
@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Auto-click the button once the page loads
            document.getElementById('button_submit_M').click();
        });

        function selectAll(ele) {
            var checkboxes = document.querySelectorAll('#checkbox_for_check');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'checkbox') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'checkbox') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        function handleTabClick(tabId, menuButtonId, tabClassToShow, shiftSuffix) {
            $(menuButtonId).click().addClass("active");
            // $(tabId).css({
            //     'color': 'yellow',
            //     'background-color': '#414770',
            //     'font-size': '150%'
            // });
            if (shiftSuffix === '_M') {
                $('#clearfix_E').hide();
                $('.tab_M').show();
                $('.kvdc_M, .netmynetab_M, .helangtab_M').hide();
            } else if (shiftSuffix === '_E') {
                $('#clearfix_M').hide();
                $('.tab_E').show();
                $('.kvdc_E, .netmynetab_E, .helangtab_E').hide();
            }

            $(tabClassToShow).show();

            if ($.fn.DataTable.isDataTable('#server_datatable')) {
                $('#server_datatable').DataTable().destroy();
            }
            $('#server_datatable thead').empty();
            $('#server_datatable tbody').empty();
        }

        function responIfSuccess(response, shift) {
            var today = new Date();
            var dayToday = today.getFullYear() + '-' + ('0' + (today.getMonth() + 1)).slice(-2) + '-' + ('0' + today
                .getDate()).slice(-2);

            if (response.listNote && response.listNote.length > 0) {
                var ackStatusq = response.listNote[0].ack_status;
                if (ackStatusq === 'Pending Endorsement') {
                    var ackStatus = response.listNote[0].ack_status;
                    $('.button_update').hide();
                    $('.save_button').hide();
                    $('.endorse_button').show();
                    $('#remarks').prop('readonly', false);
                    $('#ack_date').val(dayToday);
                    $('#ack_by').val(response.user);
                    if (document.getElementById('ack_status') !== null) {
                        document.getElementById('ack_status').textContent = ackStatus;
                    }
                }
                if (ackStatusq === 'Completed') {
                    var ackEndorsementBy = response.listNote[0].ack_endorsement_by;
                    var ackEndorsementDate = response.listNote[0].ack_endorsement_date;
                    var ackStatus = response.listNote[0].ack_status;
                    console.log(ackStatus);
                    $('.button_update').hide();
                    $('.save_button').hide();
                    $('.endorse_button').hide();
                    $('#remarks').prop('readonly', true);
                    $('#ack_date').val(ackEndorsementDate);
                    $('#ack_by').val(ackEndorsementBy);
                    if (document.getElementById('ack_status') !== null) {
                        document.getElementById('ack_status').textContent = ackStatus;
                    }
                }
            } else {
                $('.save_button').show();
                $('.endorse_button').hide();
                $('.button_update').show();
                $('#check_by').val(response.user);
                $('#ack_status').text("");
                $('#ack_by').val("");
            }

            if (response.listNote !== null && response.listNote.length > 0) {
                var ackRemarks = response.listNote[0].ack_remarks;
                var ackCheckBy = response.listNote[0].ack_check_by;
                var ackCheckDate = response.listNote[0].ack_check_date;
                if (document.getElementById('remarks') !== null) {
                    document.getElementById('remarks').value = ackRemarks;
                }
                if (document.getElementById('check_by') !== null) {
                    document.getElementById('check_by').value = ackCheckBy;
                }
                if (document.getElementById('check_date') !== null) {
                    document.getElementById('check_date').value = ackCheckDate;
                }
            }

            var shiftSuffix = shift === 'M' ? '_M' : '_E';

            if (response.menuKvdc) {
                var dateSelected = $('#date_search').val();
                if (dateSelected < '2024-04-18') {
                    $('#kvdcId' + shiftSuffix + ', #netmyneId' + shiftSuffix + ', #helangId' + shiftSuffix).show();
                } else {
                    $('#kvdcId' + shiftSuffix).show();
                }
                $('.notes_form').show();
                $('#clearfix' + shiftSuffix).show();
                $('#kvdcId' + shiftSuffix).click().addClass("active");
                $('.nav-tabs li').css({
                    'background-color': '#414770',
                    'color': 'black',
                    'font-size': '150%'
                });
                $(this).css({
                    'background-color': 'white',
                    'color': 'red',
                    'font-size': '150%'
                });

                var kvdcTab = $('.kvdc' + shiftSuffix);
                kvdcTab.empty();

                $.each(response.menuKvdc, function(index, item) {
                    kvdcTab.append(
                        '<label class="btn btn-primary" data-toggle="tooltip" title="' +
                        item.esc_group + '" group="' + item.esc_group +
                        '" shift="' + shift + '" location="kvdc"><input type="radio" name="menu" value="">' +
                        item.esc_group + '</label>');
                });
                $(".kvdc" + shiftSuffix + " label").first().click();
            } else {
                if ($.isEmptyObject(response) || (!response.menuKvdc && !response.menuHelang && !response.menu)) {
                    $('#clearfix' + shiftSuffix).hide();
                    if ($.fn.DataTable.isDataTable('#server_datatable')) {
                        $('#server_datatable').DataTable().destroy();
                    }
                    $('#server_datatable thead').empty();
                    $('#server_datatable tbody').empty();
                    $('#server_datatable').html('<p>No data available for selected date.</p>').fadeIn();
                    return;
                }
            }

            if (response.menuHelang) {
                $('#kvdcId' + shiftSuffix + ', #netmyneId' + shiftSuffix + ', #helangId' + shiftSuffix).show();
                var helangTab = $('.helangtab' + shiftSuffix);
                helangTab.empty();

                $.each(response.menuHelang, function(index, item) {
                    helangTab.append(
                        '<label class="btn btn-primary" data-toggle="tooltip" title="' +
                        item.esc_group + '" group="' + item.esc_group +
                        '" shift="' + shift + '" location="helang"><input type="radio" name="menu" value="">' +
                        item.esc_group + '</label>');
                });
            }

            if (response.menu) {
                $('#kvdcId' + shiftSuffix + ', #netmyneId' + shiftSuffix + ', #helangId' + shiftSuffix).show();
                var netmyneTab = $('.netmynetab' + shiftSuffix);
                netmyneTab.empty();

                $.each(response.menu, function(index, item) {
                    netmyneTab.append(
                        '<label class="btn btn-primary" data-toggle="tooltip" title="' +
                        item.esc_group + '" group="' + item.esc_group +
                        '" shift="' + shift + '" location="netmyne"><input type="radio" name="menu" value="">' +
                        item.esc_group + '</label>');
                });
            }

            handleTabClick('#kvdcId' + shiftSuffix, '#menu_button', '.kvdc' + shiftSuffix, shiftSuffix);

            if (response.menuKvdc === null && response.menuHelang === null && response.menu && response.menu.length > 0) {
                $('#clearfix' + shiftSuffix).show();
                $('#netmyneId' + shiftSuffix).show();
                $('.notes_form').show();
                $('#kvdcId' + shiftSuffix + ', #helangId' + shiftSuffix).hide();
                var netmyneTab = $('.netmynetab' + shiftSuffix);
                netmyneTab.empty();

                $.each(response.menu, function(index, item) {
                    netmyneTab.append(
                        '<label class="btn btn-primary" data-toggle="tooltip" title="' +
                        item.esc_group + '" group="' + item.esc_group +
                        '" shift="' + shift + '" location="netmyne"><input type="radio" name="menu" value="">' +
                        item.esc_group + '</label>');
                });
                $('#netmyneId' + shiftSuffix).click();
            }
        }

        function changeMenu(a) {
            $('.editSave').show();
            let group = $(a).attr('group');
            let shift = $(a).attr('shift');
            let location = $(a).attr('location');
            var dateSelection = $('#date_search').val();
            $('.spinner-loading').show();
            if ($.fn.DataTable.isDataTable('#server_datatable')) {
                $('#server_datatable').DataTable().destroy();
            }
            $('#server_datatable thead').empty();
            $('#server_datatable tbody').empty();
            $.ajax({
                type: "GET",
                url: "/it_support/server/find_by/" + group + "/" + dateSelection + "/" + shift + "/" + location,
                success: function(data) {
                    $('.spinner-loading').hide();
                    populateDataTable(data);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error('AJAX request failed:', errorThrown);
                }
            });
        }

        function populateDataTable(data) {
            if ($.isEmptyObject(data)) {
                console.log('Empty data or unexpected format');
                $('#server_datatable').html('<p>No data available for selected date.</p>').fadeIn();
                return;
            }

            if ($.fn.DataTable.isDataTable('#server_datatable')) {
                $('#server_datatable').DataTable().destroy();
            }

            $('#server_datatable thead').empty();
            $('#server_datatable tbody').empty();

            $('#server_datatable').html(data).fadeIn();
            $('#server_datatable').DataTable({
                ordering: false,
                lengthMenu: [
                    [20, 30, 50, -1],
                    [20, 30, 50, 'All']
                ]
            });
        }
        
        $(document).ready(function() {
            $('#date_search').on('change', function() {
                var selectedDate = $('#date_search').datepicker('getDate');
                var cutoffDate = new Date('01/15/2024');
                var actionUrl = "{{ url('/it_support/server/checklist/history') }}/" + selectedDate;
                $('#it_support_checklist').attr('action', actionUrl);

                if (selectedDate < cutoffDate) {
                    $('#button_submit_M').show();
                    $('#button_submit_E').show();
                } else {
                    $('#button_submit_M').show();
                    $('#button_submit_E').hide();
                }
            });
        });

        $(document).ready(function() {
            //click menu kvdc, netmyne, helang
            function handleTabClick_M(tabId, menuButtonId, tabContentClass) {
                handleTabClick(tabId, menuButtonId, tabContentClass, '_M');
                $(tabContentClass + ' label').first().click();
                $('#kvdcId_M, #helangId_M, #netmyneId_M').not(tabId).removeClass("active");
            }

            $('#kvdcId_M').click(function() {
                handleTabClick_M('#kvdcId_M', '#menu_button_kvdc', '.kvdc_M');
            });

            $('#netmyneId_M').click(function() {
                handleTabClick_M('#netmyneId_M', '#menu_button', '.netmynetab_M');
            });

            $('#helangId_M').click(function() {
                handleTabClick_M('#helangId_M', '#menu_button_helang', '.helangtab_M');
            });

            function handleTabClick_E(tabId, menuButtonId, tabContentClass) {
                handleTabClick(tabId, menuButtonId, tabContentClass, '_E');
                $(tabContentClass + ' label').first().click();
                $('#kvdcId_E, #helangId_E, #netmyneId_E').not(tabId).removeClass("active");
            }

            $('#kvdcId_E').click(function() {
                handleTabClick_E('#kvdcId_E', '#menu_button_kvdc_E', '.kvdc_E');
            });

            $('#netmyneId_E').click(function() {
                handleTabClick_E('#netmyneId_E', '#menu_button_E', '.netmynetab_E');
            });

            $('#helangId_E').click(function() {
                handleTabClick_E('#helangId_E', '#menu_button_helang_E', '.helangtab_E');
            });

            //click morning / night shift
            function handleButtonClick(shiftSuffix) {
                var checkbox = document.getElementById('checkbox_for_check');
                var checkboxId = checkbox ? checkbox.id : null;

                var dateSelected = $('#date_search').val();
                $('#selected_date').val(dateSelected);

                ['#remarks', '#check_by', '#ack_by', '#ack_date'].forEach(function(field) {
                    $(field).val("");
                });

                $('#ack_status').text("");
                $('#check_by, #ack_by').val("{{ $user }}");

                var today = new Date();
                var dateToday = today.getFullYear() + '-' + ('0' + (today.getMonth() + 1)).slice(-2) + '-' + ('0' +
                    today.getDate()).slice(-2);

                if (shiftSuffix === '_M') {
                    var Morning = 'Morning Shift';
                    $('#header-table' + shiftSuffix).text(Morning);
                } else {
                    var Evening = 'Night Shift';
                    $('#header-table' + shiftSuffix).text(Evening);
                }

                function setButtonStyle(buttonId, defaultColor, hoverColor, textColor, fontSize) {
                    let clicked = false; 
                    $(buttonId).css({
                        'background-color': defaultColor,
                        'color': textColor,
                        'font-size': fontSize
                    }).hover(
                        function() {
                            if (!clicked) { 
                                $(this).css({
                                    'background-color': hoverColor,
                                    'color': textColor
                                });
                            }
                        },
                        function() {
                            if (!clicked) {
                                $(this).css({
                                    'background-color': defaultColor,
                                    'color': textColor
                                });
                            }
                        }
                    ).click(function() {
                        clicked = true;
                        $(this).css({
                            'background-color': hoverColor,
                            'color': textColor
                        });
                    });
                }

                // Apply styles to both buttons
                setButtonStyle('#button_submit_M', '#2db0bf', '#333c79', 'white', '150%');
                setButtonStyle('#button_submit_E', '#2db0bf', '#333c79', 'white', '150%');

                $('#header-table-date' + shiftSuffix).text(dateSelected);
                $('#check_date').val(dateToday);

                var url = "/it_support/server/checklist/history";
                var method = dateSelected ? "POST" : "GET";

                if (dateSelected) url += "/date";

                $.ajax({
                    type: method,
                    url: url,
                    data: {
                        '_token': $('input[name=_token]').val(),
                        'dateSelection': dateSelected
                    },
                    success: function(response) {
                        responIfSuccess(response, shiftSuffix.replace('_', ''));
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX request failed:", error);
                    }
                });
            }

            $('#button_submit_E').click(function() {
                handleButtonClick('_E');
            });

            $('#button_submit_M').click(function() {
                handleButtonClick('_M');
            });



        });

        $(document).on("click",
            ".kvdc_M label, .netmynetab_M label, .helangtab_M label, .kvdc_E label , .netmynetab_E label, .helangtab_E label",
            function(e) {
                var shift = $(this).attr('shift');
                var location = $(this).attr('location');
                var group = $(this).attr('group');
                changeMenu(this);
            });

        $(document).ready(function() {

            $('.button_update').click(function(e) {
                e.preventDefault(); // Prevent default form submission behavior

                var selectedDate = $('#date_search').val();
                var actionUrl = "{{ route('statusChecklistServerHistoryDate') }}";

                var remarksData = {};
                var checkboxesData = {};
                var checkboxesDataAmber = {};

                $('#server_datatable input[type="text"]').each(function() {
                    var remarksName = $(this).attr(
                        'name');
                    var remarksValue = $(this).val();
                    remarksData[remarksName] =
                        remarksValue;
                });

                $('input[name^="check"]').each(function() {
                    if ($(this).is(':checked')) { // Only include checked checkboxes
                        var checkboxName = $(this).attr('name');
                        var taskIdMatch = checkboxName.match(/\[(\d+)\]$/);

                        // Check if taskIdMatch is not null and has a valid task ID
                        if (taskIdMatch && taskIdMatch[1]) {
                            var taskId = taskIdMatch[1];
                            checkboxesData[checkboxName] = 'checked'; // Set the value to 'checked'
                        }
                    }
                });

                $('input[name^="amber"]').each(function() {
                    if ($(this).is(':checked')) { // Only include checked checkboxes
                        var checkboxNameAmber = $(this).attr('name');
                        var taskIdMatch = checkboxNameAmber.match(/\[(\d+)\]$/);

                        // Check if taskIdMatch is not null and has a valid task ID
                        if (taskIdMatch && taskIdMatch[1]) {
                            var taskId = taskIdMatch[1];
                            checkboxesDataAmber[checkboxNameAmber] =
                                'checked'; // Set the value to 'checked'
                        }
                    }
                });
                $.ajax({
                    type: 'POST',
                    url: actionUrl,
                    data: {
                        '_token': $('input[name=_token]').val(),
                        'dateSelection': selectedDate,
                        'remarks': remarksData,
                        'check': checkboxesData,
                        'amber': checkboxesDataAmber
                    },
                    success: function(response) {
                        responIfSuccess(response, 'M')
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX request failed:", error);
                    }
                });
            });
        });
    </script>
@endsection
