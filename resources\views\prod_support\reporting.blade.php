@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/prod-support/data-patching') }}"><i class="fa fa-tasks"></i>Scheduled Patching</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/urgent-patching') }}"><i class="fa fa-pencil-square"></i>Urgent Patching</a>
            </li>
            <li class="">
                <a href="{{ url('/prod-support/history-data-patch') }}"><i class="fa fa-list-alt"></i>History</a>
            </li>
            <li class="active">
                <a href="{{ url('/prod-support/reporting') }}"><i class="fa fa-file-excel-o"></i>Report</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/data-lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
            <li @if(!Auth::user()->isPatcherRolesEp()) class="hide" @endif>
                <a href="{{ url('/prod-support/approver') }}"><i class="fa fa-user"></i>Approver</a>
            </li>
        </ul>
    </div>
</div>

<div class="block block-alt-noborder full">
    <form action="{{ url('/prod-support/reporting') }}" method="post">
        {{ csrf_field() }}

        <div class="form-group">
            <label class="col-md-1 text-left port_type_label" for="port_type">Porting Type<span class="text-danger">*</span></label>
            <div class="col-md-2">
                <select id="port_type" name = "port_type" required class="form-control" style="width: 200px;">
                    <option value="">Please Select</option>
                    <option value="S">NORMAL PORTING</option>
                    @if(Auth::user()->isPatcherRolesEp()) <option value="U">URGENT PORTING</option> @endif
                </select>
            </div>

            <label class="col-md-1 text-left by_year_label" for="by_year">Year<span class="text-danger">*</span></label>
            <div class="col-md-2">

                <select id="by_year" name = "by_year" required class="form-control" style="width: 200px;">
                    <option value="">Please Select</option>
                    @foreach($listYear as  $list)
                    <option value="{{$list->year}}">{{$list->year}}</option>
                    @endforeach
                </select>
            </div>

            <div class="col-md-1">
                <label for="type-checkbox1"> 
                    <input type="checkbox" id="checkbox" name="checkbox" value=""> Month
                </label>
            </div>
            <div class="col-md-2">
                <select id="month" name = "month" class="form-control" style="display:none" style="width: 200px;">
                    <option value="">Please Select</option>
                    <option value="01">January</option>
                    <option value="02">February</option>
                    <option value="03">March</option>
                    <option value="04">April</option>
                    <option value="05">May</option>
                    <option value="06">June</option>
                    <option value="07">July</option>
                    <option value="08">August</option>
                    <option value="09">September</option>
                    <option value="10">October</option>
                    <option value="11">November</option>
                    <option value="12">December</option>
                </select>
            </div>

            <div class="form-actions form-actions-button text-right ">
                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
            </div>

        </div>

    </form>
</div>
@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script> 
<script>
    $('#checkbox').click(function () {

        if ($('#checkbox').is(":checked")) {
            $('#month').show();
            $('#month').Attr('required');
            $('#month').attr('data-error', 'This field is required.');
        } else {
            $('#month').hide();
            $('#month').val("");
            $('#month').removeAttr('required');
        }
    });
</script>
@endsection

