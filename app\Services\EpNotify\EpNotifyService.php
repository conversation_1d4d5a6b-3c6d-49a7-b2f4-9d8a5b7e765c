<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Services\EpNotify;

use DB;
use Carbon\Carbon;
use Log;

class EpNotifyService {

    public static $statusPending = 0;
    public static $statusCompleted = 1;
    public static $statusError = 2;
    public static $statusDescCompleted = 'Completed';
    public static $statusDescFailed = 'Failed';
    public static $sentPending = 0;
    public static $sentCompleted = 1;
    public static $dbConnection = 'mysql_notify';
    public static $tableNotification = 'notification';
    public static $tableLog = 'log';
    public static $tableReceiver = 'receiver';
    public static $statusActive = 'Active';
    public static $statusInActive = 'In-Active';
    public static $tableReceiverGroup = 'receiver_group';

    public function getPendingNotification($dbConnection) {
        return DB::connection($dbConnection)->table(self::$tableNotification)
                        ->join(self::$tableReceiverGroup, 'notification.receiver_group', '=', 'receiver_group.group_name')
                        ->join(self::$tableReceiver, 'receiver.id', '=', 'receiver_group.receiver_id')
                        ->whereIn('notification.status', [self::$statusPending,self::$statusError])
                        ->where('receiver_group.group_status', self::$statusActive)
                        ->where('receiver.status', self::$statusActive)
                        ->where('sent', self::$sentPending)
                        ->select('notification.*','receiver_group.*','receiver.*')
                        ->addSelect('notification.date_entered as date_notified')
                        ->get();
    }

    public function getDetailPendingNotification($dbConnection, $receiverName, $dateEntered) {
        return DB::connection($dbConnection)->table(self::$tableNotification)
        ->join(self::$tableReceiverGroup, 'notification.receiver_group', '=', 'receiver_group.group_name')
        ->join(self::$tableReceiver, 'receiver.id', '=', 'receiver_group.receiver_id')
        ->whereIn('notification.status', [self::$statusPending,self::$statusError])
        ->where('receiver_group.group_status', self::$statusActive)
        ->where('receiver.status', self::$statusActive)
        ->where('sent', self::$sentPending)
        ->where('notification.date_entered', $dateEntered)
        ->select('notification.*','receiver_group.*','receiver.*')
        ->addSelect('notification.date_entered as date_notified')
        ->get();
    }

    public function getJobStatus($dbConnection,$status) {
        return DB::connection($dbConnection)->table(self::$tableLog)
                        ->join(self::$tableNotification, 'notification.id', '=', 'log.job_id')
                        ->where('log.status', $status)
                        ->whereNotNull('job_id')
                        ->orderBy('log.date_entered', 'desc')
                        ->select('notification.source_remark', 'log.description', 'log.date_entered', 'notification.message')
                        ->first();
    }

    public function getWhatsAppStatus($dbConnection) {
        return DB::connection($dbConnection)->table(self::$tableLog)
                        ->whereNull('job_id')
                        ->orderBy('date_entered', 'desc')
                        ->first();
    }

    public function getListReceiver($dbConnection) {
        return DB::connection($dbConnection)->table(self::$tableReceiver)
                        ->get();
    }

    public function updateReceiver($dbConnection, $receiverId, $updateGroup, $updateName, $receiverStatus, $whatappId) {
        DB::connection($dbConnection)
                ->table(self::$tableReceiver)
                ->where('id', $receiverId)
                ->update([
                    'is_group_personal' => $updateGroup,
                    'receiver_name' => $updateName,
                    'status' => $receiverStatus,
                    'date_modified' => Carbon::now(),
                    'whatapp_id' => $whatappId
        ]);
    }

    public function addReceiver($dbConnection, $updateGroup, $updateName, $whatappId) {
        DB::connection($dbConnection)
                ->insert('insert into receiver  
                    (is_group_personal,receiver_name,status,date_created,date_modified,whatapp_id) 
                    values (?, ?, ?, ?, ?, ?)', [
                    $updateGroup,
                    $updateName,
                    self::$statusActive,
                    Carbon::now(),
                    Carbon::now(),
                    $whatappId
        ]);
    }

    public function getListReceiverGroup($dbConnection){
        return DB::connection($dbConnection)
                ->table(self::$tableReceiverGroup)
                ->join(self::$tableReceiver,'receiver.id','=','receiver_group.receiver_id')
                ->select('receiver.*','receiver_group.*')
                ->addSelect('receiver_group.id as receiver_group_id','receiver.id as receiver_id')
                ->orderBy('receiver_group.group_name','ASC')
                ->get();
    }
    
    public function addReceiverGroup($dbConnection, $groupName, $receiverId, $groupDescription){
        DB::connection($dbConnection)
                ->insert('insert into receiver_group  
                    (group_name,receiver_id,date_entered,group_description) 
                    values (?, ?, ?, ?)', [
                    $groupName,
                    $receiverId,
                    Carbon::now(),
                    $groupDescription
        ]);
    }
    
    public function updateReceiverGroup($dbConnection, $group_name, $receiverGroupId, $receiverName, $groupStatus, $groupDescription) {
        $receiver = DB::connection($dbConnection)
                ->table(self::$tableReceiver)
                ->where('receiver_name',$receiverName)->first();
        if(isset($receiver)){
            DB::connection($dbConnection)
                ->table(self::$tableReceiverGroup)
                ->where('id', $receiverGroupId)
                ->update([
                    'group_name' => $group_name,
                    'receiver_id' => $receiver->id,
                    'group_status' => $groupStatus,
                    'date_modified' => Carbon::now(),
                    'group_description' => $groupDescription
        ]);
        }
    }
}
