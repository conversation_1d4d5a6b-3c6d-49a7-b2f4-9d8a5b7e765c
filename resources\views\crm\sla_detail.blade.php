@extends('layouts.guest-dash')

@section('header')
<style type="text/css"> 
    .highlight {
        background: yellow;
    }

</style>
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/stl/crm') }}"> SLA CRM/POMS</a>
            </li>
            <li>
                <a href="{{ url('/stl/crm/duplicatetask') }}"> DUPLICATE TASKS</a>
            </li>
            <li class="active">
                <a href="{{ url('/stl/crm/sla') }}"> TASK FLOW</a>
            </li>
        </ul>
    </div>
</div>
@if (Auth::user())
<div class="row">
    <form action=" {{ url('/stl/crm/sla') }}" method="post" class="form-horizontal form-bordered">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="col-md-6">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-6 control-label" for="caseNumber">Case Number</label>
                        <div class="col-md-6">
                            <input type="number" id="caseNumber" name="caseNumber" value="{{ old('caseNumber') }}" class="form-control">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-6">
                <fieldset>
                    <div class="form-group">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-sm btn-primary pull-right"><i class="fa fa-search"></i> Search </button>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </form>  
</div>
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class="widget-extra themed-background-dark">
                <h5 class='widget-content-light'>
                    TASK FLOW DETAILS - <strong>IT Incident</strong>
                </h5>
            </div>
            @if(isset($result) && count($result) > 0)
            <div class="table-responsive">
                <table class="table table-bordered">
                    <tbody>
                        <tr>
                            <td><strong>CASE NUMBER </strong></td>
                            <td style="width: 40%;"><a target="_blank" href="https://crm.eperolehan.gov.my/crm/index.php?module=Cases&action=DetailView&record={{ $result[0]->id }}" title="View Detail Case in CRM" >{{ $result[0]->case_number}}</a></td>
                            <td><strong>CONTACT MODE</strong></td>
                            <td style="width: 40%;">{{$result[0]->contact_mode_c}}</td>
                        </tr>
                        <tr>
                            <td><strong>STATE</strong></td>
                            <td>{{$result[0]->case_state}}</td>
                            <td><strong>STATUS</strong></td>
                            <td class="case_status_class"> 
                                    <a class="case_status_action"  title="" data-original-title="Trigger!"
                                       href="#modal-case-status" data-toggle="modal" 
                                       data-id="{{ $result[0]->id }}"
                                       data-status="{{ $result[0]->case_status }}">{{$result[0]->case_status}}</a> 
                                </td>
                        </tr>
                        <tr>
                            <td><strong>REQUEST TYPE</strong></td>
                            <td>{{$result[0]->request_type_c}}</td>
                            <td><strong>INCIDENT SERVICE TYPE</strong></td>
                            <td>{{$result[0]->incident_service_type_c}}</td>
                        </tr>
                        <tr>
                            <td><strong>CASE RESOLUTION</strong></td>
                            <td colspan="3" class="case_resolution">
                                <a class="case_resolution_action"
                                       href="#modal-case-resolution" data-toggle="modal" 
                                       data-id="{{ $result[0]->id }}"
                                       data-cresolution="{{ $result[0]->resolution }}">CLICK HERE</a> 
                            </td> 
                        </tr>
                    </tbody>
                </table> 
                <div class="block">
                    <div id="response" class="table-options clearfix display-none">
                        <div id="response-msg" class="text-center text-light" colspan="6"></div> 
                    </div>
                    <table id="table-sla" class="table table-vcenter table-bordered table-striped">
                        <thead>
                            <tr>
                                <th class="text-center">Task Name</th>
                                <th class="text-center">Date Created</th>
                                <th class="text-center">Date Modified</th>
                                <th class="text-center">Task Status</th>
                                <th class="text-center">Task Deleted</th>
                                <th class="text-center">Sla Flag</th>
                                <th class="text-center">Available Start</th>
                                <th class="text-center">Available Due</th>
                                <th class="text-center">Available Duration</th>
                                <th class="text-center">Actual Start</th>
                                <th class="text-center">Actual Due</th>
                                <th class="text-center">Actual Duration</th>
                                <th class="text-center">Task Duration</th>
                                <th class="text-center">Task Reassign</th>
                                <th class="text-center">Resolution</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($result as $val)
                            <tr>
                                <td class="text-left"> {{ $val->name }} </td>
                                <td class="text-center"> {{ $val->task_date_entered }} </td>
                                <td class="text-center"> {{ $val->task_date_modified }} </td>
                                <td class="text-center status_class"> 
                                    <a class="status_action"  title="" data-original-title="Trigger!"
                                       href="#modal-status" data-toggle="modal" 
                                       data-id="{{ $val->task_id }}"
                                       data-status="{{ $val->status }}">{{ $val->status }}</a> 
                                </td> 
                                <td class="text-center delete_class"> 
                                    <a class="delete_action"  title="" data-original-title="Trigger!"
                                       href="#modal-delete" data-toggle="modal" 
                                       data-id="{{ $val->task_id }}"
                                       data-status="{{ $val->deleted }}">@if($val->deleted == 0) Active @else In-Active @endif</a> 
                                </td> 
                                <td class="text-center"> {{ $val->sla_task_flag_c }} </td>
                                <td class="text-center"> {{ $val->sla_available_start }} </td>
                                <td class="text-center"> {{ $val->sla_available_due }} </td>
                                <td class="text-center"> {{ $val->available_duration }} </td>
                                <td class="text-center"> {{ $val->sla_actual_start }} </td>
                                <td class="text-center"> {{ $val->sla_stop_15min_c }} </td>
                                @if($val->actual_duration > $val->available_duration)
                                <td class="text-center detail"><strong><a style="color: red">{{ $val->actual_duration  }} </a></strong></td> 
                                @else 
                                <td class="text-center">{{ $val->actual_duration  }} </td> 
                                @endif
                                <td class="text-center"> {{ $val->task_duration_c }} </td>
                                <td class="text-center"> {{ $val->reassign_time_c }} </td>
                                <td class="task_resolution">
                                <a class="task_resolution_action"
                                       href="#modal-task-resolution" data-toggle="modal" 
                                       data-id="{{ $val->task_id }}"
                                       data-tresolution="{{ $val->resolution_c }}">CLICK HERE</a> 
                            </td> 
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
<div id="modal-status" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-status-header">Update Task Status</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <label class="col-md-3 control-label">Current Status</label>
                                <div class="col-md-9">
                                    <input type="hidden" id="task_id" value="" />
                                    <p id="current_status_display" class="form-control-static"></p>
                                </div>
                                <label class="col-md-3 control-label">New Status</label>
                                <div class="col-md-9">
                                    <select id="new_status" name="new_status" required class="form-control">

                                    </select>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-6 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_status">Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<div id="modal-delete" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-delete-header">Update Task Deleted Status</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <label class="col-md-3 control-label">Current Task Status</label>
                                <div class="col-md-9">
                                    <input type="hidden" id="task_id" value="" />
                                    <p id="current_delete_display" class="form-control-static"></p>
                                </div>
                                <label class="col-md-3 control-label">New Task Status</label>
                                <div class="col-md-9">
                                    <select id="new_delete" name="new_delete" required class="form-control">
                                        <option value="0">Active</option>
                                        <option value="1">In-Active</option>
                                    </select>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-6 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_delete">Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<div id="modal-case-status" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-case-status-header">Update Case Status</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <label class="col-md-3 control-label">Current Case Status</label>
                                <div class="col-md-9">
                                    <input type="hidden" id="case_id" value="" />
                                    <p id="current_case_status_display" class="form-control-static"></p>
                                </div>
                                <label class="col-md-3 control-label">New Case Status</label>
                                <div class="col-md-9">
                                    <select id="new_case_status" name="new_case_status" required class="form-control">

                                    </select>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-6 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_case_status">Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- MODAL FOR CASE RESOLUTION -->
<div id="modal-case-resolution" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-case-resolution-header">Update Case Resolution</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="col-lg-12">
                                    <input type="hidden" id="case_id" value="" />
                                    <textarea id="new_case_resolution" name="new_case_resolution" class="form-control" rows="20"></textarea>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-6 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_case_resolution">Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- MODAL FOR TASK RESOLUTION -->
<div id="modal-task-resolution" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"> <span id="modal-task-resolution-header">Update Task Resolution</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-lg-12">
                        <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="col-lg-12">
                                    <input type="hidden" id="task_id" value="" />
                                    <textarea id="new_task_resolution" name="new_task_resolution" class="form-control" rows="20"></textarea>
                                </div>
                            </div>
                            <div class="form-group form-actions">
                                <div class="col-md-6 col-md-offset-9">
                                    <button type="button" class="btn btn-sm btn-primary action_update_task_resolution">Update</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning">Cancel</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

</div>
@endif
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
    $('#table-sla').dataTable({
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        //action for update task status
        $('td.status_class').on("click", 'a.status_action', function () {
            var taskId = $(this).attr('data-id');
            var taskStatus = $(this).attr('data-status');
            var listStatus = <?php echo json_encode($listTaskStatus); ?>;
            var options = '';
            $.each(listStatus, function (index, value) {
                options += '<option value = "' + value["value_code"] + '">' + value["value_name"] + '</option>';
            });
            $('select[name="new_status"]').html(options);
            $("#current_status").val(taskStatus);
            $("#task_id").val(taskId);
            $("#current_status_display").text(taskStatus);
        });
        $('div.form-actions').on("click", 'button.action_update_status', function () {
            var taskId = $("#task_id").val();
            var newStatus = $("#new_status option:selected").val();
            var csrf = $("input[name=_token]").val();
            console.log("taskId: " + taskId);
            console.log("newStatus: " + newStatus);

            $.ajax({
                url: "/stl/crm/sla/update",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "taskId": taskId, "newStatus": newStatus},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#modal-status').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                    setTimeout(location.reload.bind(location), 5000); 
                } else {
                    $('#modal-status').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Update Failed! Please try again.");  
                }
            });
        });
        
        //action for update task deleted
        $('td.delete_class').on("click", 'a.delete_action', function () {
            var taskId = $(this).attr('data-id');
            var taskDeleted = $(this).attr('data-status'); 
            var taskDeletedVal = 'Active';
            if(taskDeleted === 1) {
                taskDeletedVal = 'In-Active';
            } 
            $("#task_id").val(taskId);
            $("#current_delete_display").text(taskDeletedVal);
        });
        $('div.form-actions').on("click", 'button.action_update_delete', function () {
            var taskId = $("#task_id").val();
            var newDelete = $("#new_delete option:selected").val();
            var csrf = $("input[name=_token]").val();
            console.log("taskId: " + taskId);
            console.log("newDelete: " + newDelete);

            $.ajax({
                url: "/stl/crm/sla/update",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "taskId": taskId, "newDelete": newDelete},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#modal-delete').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                    setTimeout(location.reload.bind(location), 5000); 
                } else {
                    $('#modal-delete').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Update Failed! Please try again.");
                    setTimeout(location.reload.bind(location), 5000); 
                }
            });
        });
        
        //action for update case status
        $('td.case_status_class').on("click", 'a.case_status_action', function () {
            var caseId = $(this).attr('data-id');
            var caseStatus = $(this).attr('data-status');
            var listCaseStatus = <?php echo json_encode($listCaseStatus); ?>;
            var options = '';
            $.each(listCaseStatus, function (index, value) {
                options += '<option value = "' + value["value_code"] + '">' + value["value_name"] + '</option>';
            });
            $('select[name="new_case_status"]').html(options);
            $("#current_case_status").val(caseStatus);
            $("#case_id").val(caseId);
            $("#current_case_status_display").text(caseStatus); 
        });
        $('div.form-actions').on("click", 'button.action_update_case_status', function () {
            var caseId = $("#case_id").val();
            var newCaseStatus = $("#new_case_status option:selected").val();
            var csrf = $("input[name=_token]").val();
            console.log("caseId: " + caseId);
            console.log("newCaseStatus: " + newCaseStatus);

            $.ajax({
                url: "/stl/crm/sla/update",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "caseId": caseId, "newCaseStatus": newCaseStatus},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#modal-case-status').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                    setTimeout(location.reload.bind(location), 5000); 
                } else {
                    $('#modal-case-status').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Update Failed! Please try again.");  
                }
            });
        });
        //action for update case resolution
        $('td.case_resolution').on("click", 'a.case_resolution_action', function () {
            var caseId = $(this).attr('data-id');
            var caseResolution = $(this).attr('data-cresolution');
            console.log(caseResolution);
            $("#case_id").val(caseId);
            $("#new_case_resolution").html(caseResolution); 
        });
        $('div.form-actions').on("click", 'button.action_update_case_resolution', function () {
            var caseId = $("#case_id").val();
            var newCaseResolution = $('textarea#new_case_resolution').val();
            var csrf = $("input[name=_token]").val(); 

            $.ajax({
                url: "/stl/crm/sla/updateresolution",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "module": "Cases", "id": caseId, "newResolution": newCaseResolution},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#modal-case-resolution').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                    setTimeout(location.reload.bind(location), 5000); 
                } else {
                    $('#modal-case-resolution').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Update Failed! Please try again.");  
                }
            });
        });
        //action for update task resolution
        $('td.task_resolution').on("click", 'a.task_resolution_action', function () {
            var taskId = $(this).attr('data-id');
            var taskResolution = $(this).attr('data-tresolution');
            $("#task_id").val(taskId);
            $("#new_task_resolution").html(taskResolution); 
        });
        $('div.form-actions').on("click", 'button.action_update_task_resolution', function () {
            var taskId = $("#task_id").val();
            var newTaskResolution = $('textarea#new_task_resolution').val();
            var csrf = $("input[name=_token]").val(); 

            $.ajax({
                url: "/stl/crm/sla/updateresolution",
                method: "POST",
                dataType: "json",
                data: {"_token": csrf, "module": "Tasks", "id": taskId, "newResolution": newTaskResolution},
                context: document.body
            }).done(function (resp) {
                if (resp.status === 'Success') {
                    $('#modal-task-resolution').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Data successfully updated. Page will be refresh in 5 second.");
                    setTimeout(location.reload.bind(location), 5000); 
                } else {
                    $('#modal-task-resolution').modal('hide');
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Update Failed! Please try again.");  
                }
            });
        });
    });
</script>

@endsection
