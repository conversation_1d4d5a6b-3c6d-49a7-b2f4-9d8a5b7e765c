<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<form action="<?php echo e(url('/find/supplier')); ?>/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="carian" name="carian" value="<?php if(isset($carian)): ?><?php echo e($carian); ?><?php endif; ?>" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Courses Header -->
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="fa fa-globe"></i>Search <strong>Supplier</strong><br><small>MOF NO. or eP No. or IC No. or Appl No.</small>
        </h1>
    </div>
</div>

<?php if(isset($data)): ?>
<!-- Success Alert Content -->
<div class="alert alert-success alert-dismissable">
    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
    <h4><i class="fa fa-check-circle"></i> Found <?php echo e($data->count()); ?> records supplier</h4>
</div>
<?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $supplierInfo): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <div class="block-options pull-right">
            <a href="javascript:void(0)" class="btn btn-alt btn-sm btn-primary" data-toggle="block-toggle-content"><i class="fa fa-arrows-v"></i></a>
        </div>
        <h1><i class="fa fa-building-o"></i> <strong><?php echo e($key+1); ?>) ePerolehan (Pembekal) : <?php echo e($supplierInfo['supplier']->company_name); ?></strong></h1>
    </div>
    <div class="row block-content">

        <!-- Main Row -->
        <div class="row">
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Supplier</strong> Info</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Company Name</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->company_name); ?>

                                            <?php if($supplierInfo['basicCompInfo'] != null && $supplierInfo['basicCompInfo']->is_name_hq_non_ascii): ?><i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Company name has special character Non-ASCII"></i><?php endif; ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">eP No.</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->ep_no); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">SSM No.</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->reg_no); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Business Type</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->business_type); ?> &raquo; (<?php echo e(App\Services\EPService::$BUSINESS_TYPE[$supplierInfo['supplier']->business_type]); ?>) </strong>
                                    </td>
                                </tr>

                                <tr>
                                    <td class="text-right">SAP Vendor Code</td>
                                    <td>
                                        <strong><?php if($supplierInfo['sapVendorCode']): ?> <?php echo e($supplierInfo['sapVendorCode']->sap_vendor_code); ?> <?php endif; ?></strong>
                                    </td>
                                </tr>
                                <?php if($supplierInfo['basicCompInfo'] != null): ?>
                                <tr>
                                    <td class="text-right">Country Origin</td>
                                    <td>
                                        <strong>
                                            <?php echo e($supplierInfo['basicCompInfo']->ssm_company_country); ?>

                                            <?php if( array_key_exists($supplierInfo['basicCompInfo']->ssm_company_country, App\Services\EPService::$CPTPP_COUNTRY) == true): ?>
                                            - <?php echo e(App\Services\EPService::$CPTPP_COUNTRY[$supplierInfo['basicCompInfo']->ssm_company_country]); ?>

                                            <i class="fa fa-info-circle" title='CPTPP Country'></i><br /> (CPTTP Country)</span>
                                            <?php endif; ?>
                                        </strong>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="text-right">Established Date</td>
                                    <td>
                                        <strong><?php echo e(null != $supplierInfo['supplier']->establish_date? Carbon\Carbon::parse($supplierInfo['supplier']->establish_date)->format('d-m-Y'):''); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Record Status</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->s_record_status); ?> &raquo; <span <?php if($supplierInfo['supplier']->s_record_status == 9): ?> class="text-danger" <?php endif; ?>>(<?php echo e(App\Services\EPService::$RECORD_STATUS[$supplierInfo['supplier']->s_record_status]); ?>)</span></strong>
                                    </td>
                                </tr>
                                <?php if($supplierInfo['suppMofStatus'] != null): ?>
                                <tr>
                                    <td class="text-right">Bumi Status</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['suppMofStatus']->bumi_status); ?></strong>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <?php if($supplierInfo['applDetail'] != null): ?>
                                <tr>
                                    <td class="text-right">Supplier Type</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['applDetail']->supplier_type); ?> &raquo; <?php echo e(App\Services\EPService::$SUPPLIER_TYPE[$supplierInfo['applDetail']->supplier_type]); ?></strong>
                                    </td>
                                </tr>
                                <?php endif; ?>

                                <tr>
                                    <td class="text-right">Total Items</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['totalItems']); ?></strong>
                                    </td>
                                </tr>

                                <?php if($supplierInfo['basicCompInfo'] != null): ?>
                                <tr>
                                    <td class="text-right">Is Federal?</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['basicCompInfo']->is_with_federal); ?></strong>
                                    </td>
                                </tr>
                                <?php endif; ?>
                                <tr>
                                    <td class="text-right">MOF No.</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['supplier']->mof_no); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">MOF Exp Date</td>
                                    <td>
                                        <strong><?php echo e(null != $supplierInfo['supplier']->ma_exp_date? Carbon\Carbon::parse($supplierInfo['supplier']->ma_exp_date)->format('d-m-Y'):''); ?></strong>
                                    </td>
                                </tr>
                                <?php if($supplierInfo['basicCompInfo'] != null): ?>
                                <tr>
                                    <td class="text-right">Address</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['basicCompInfo']->address_1); ?><br />
                                            <?php echo e($supplierInfo['basicCompInfo']->address_2); ?> <?php echo e($supplierInfo['basicCompInfo']->address_3); ?><br />
                                            <?php echo e($supplierInfo['basicCompInfo']->postcode); ?> <?php echo e($supplierInfo['basicCompInfo']->city_name); ?>, <?php echo e($supplierInfo['basicCompInfo']->district_name); ?><br />
                                            <?php echo e($supplierInfo['basicCompInfo']->state_name); ?>, <?php echo e($supplierInfo['basicCompInfo']->country_name); ?>

                                            <?php if($supplierInfo['basicCompInfo']->nonAsciiHqDetected): ?><i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Address has special character Non-ASCII"></i><?php endif; ?> </strong>
                                    </td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Application</strong> Registered</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        <?php if($supplierInfo['applDetail'] != null): ?>
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Appl No</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['applDetail']->appl_no); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Created</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['applDetail']->created_date); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Changed</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['applDetail']->changed_date); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Appl Status</td>
                                    <td>
                                        <strong>(<?php echo e($supplierInfo['applDetail']->status_id); ?>) - <?php echo e($supplierInfo['applDetail']->status_name); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Supporting Documents</td>
                                    <td>
                                        <strong><?php echo e(App\Services\EPService::$SUPPORTING_DOC_MODE[$supplierInfo['applDetail']->supporting_doc_mode]); ?></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <?php endif; ?>

                        <?php if(isset($supplierInfo['listApplSectionReview']) && count($supplierInfo['listApplSectionReview']) > 0): ?>
                        <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                        <?php $__currentLoopData = $supplierInfo['listApplSectionReview']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $objRev): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($objRev->remark_to_approver && strlen($objRev->remark_to_approver) > 0): ?>
                        <strong>Action By User Role: <?php echo e($objRev->role_type); ?></strong> <br />
                        <strong>Recommendation: <?php echo e($objRev->recommendation); ?></strong> on <?php echo e($objRev->remark_date); ?> <br />
                        <strong>Remark:</strong><span style="font-style: italic"><?php echo e($objRev->remark_to_approver); ?></span> <br />
                        <?php endif; ?>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <br /><br />
                        <?php endif; ?>

                        <?php if(isset($supplierInfo['listApplRejectReason']) && count($supplierInfo['listApplRejectReason']) > 0): ?>
                        <strong><span class="bolder">Reject Reason </span></strong><br />
                        <?php $__currentLoopData = $supplierInfo['listApplRejectReason']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $objRej): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <strong><?php echo e($key+1); ?> : on <?php echo e($objRev->changed_date); ?> </strong><br />
                        <strong>Remark:</strong><span style="font-style: italic"><?php echo e($objRej->reason_desc); ?></span> <br />
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <br /><br />
                        <?php endif; ?>

                        <?php if(isset($supplierInfo['listRemarksCancelReject']) && count($supplierInfo['listRemarksCancelReject']) > 0): ?>
                        <strong><span class="bolder">Remarks </span></strong><br />
                        <?php $__currentLoopData = $supplierInfo['listRemarksCancelReject']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objRemark): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <strong><?php echo e($objRemark->doc_type); ?></strong> : <span style="font-style: italic"><?php echo e($objRemark->remark); ?></span> <br />
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <br /><br />
                        <?php endif; ?>

                        <?php if(isset($supplierInfo['listAttachmentCancelReject']) && count($supplierInfo['listAttachmentCancelReject']) > 0): ?>
                        <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                        <?php $__currentLoopData = $supplierInfo['listAttachmentCancelReject']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objAttReject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <strong><a href="<?php echo e(url('/download/attachment/cancel-reject/')); ?>/<?php echo e($objAttReject->attachment_id); ?>" target="_blank"><?php echo e($objAttReject->doc_type); ?> - <?php echo e($objAttReject->file_name); ?></a> </strong>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        <br /><br />
                        <?php endif; ?>

                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
            <div class="col-md-4">
                <!-- Your Account Block -->
                <div class="block">
                    <!-- Your Account Title -->
                    <div class="block-title">
                        <h2><strong>Application</strong> In Progress</h2>
                    </div>
                    <!-- END Your Account Title -->

                    <!-- Your Account Content -->
                    <div class="block-section">
                        <?php if(isset($supplierInfo['listinProgressSuppProcessAppl']) && $supplierInfo['listinProgressSuppProcessAppl'] != null): ?>
                        <table class="table table-borderless table-striped table-vcenter">
                            <tbody>
                                <tr>
                                    <td class="text-right">Appl No</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->appl_no); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Created</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->appl_created_date); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Changed</td>
                                    <td>
                                        <strong><?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->appl_change_date); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Appl Status</td>
                                    <td>
                                        <strong>(<?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->appl_status_id); ?>) - <?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->appl_status); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">WorkFlow Status</td>
                                    <td>
                                        <strong>(<?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->wf_status_id); ?>) - <?php echo e($supplierInfo['listinProgressSuppProcessAppl'][0]->wf_status); ?></strong>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="text-right">Supporting Documents</td>
                                    <td>
                                        <strong><?php echo e(App\Services\EPService::$SUPPORTING_DOC_MODE[$supplierInfo['listinProgressSuppProcessAppl'][0]->supporting_doc_mode]); ?></strong>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <?php else: ?>
                        <p>Currently no application in progress.</p>
                        <?php endif; ?>

                    </div>
                    <!-- END Your Account Content -->
                </div>
                <!-- END Your Account Block -->
            </div>
        </div>
        <!-- Main Row -->
        <div class="row">
            <div class="col-md-12">
                <!-- Most Viewed Courses Block -->
                <div class="block">
                    <!-- Most Viewed Courses Title -->
                    <div class="block-title text-info">
                        <div class="block-options pull-right">
                            <a href="javascript:void(0)" class="btn btn-alt btn-sm btn-primary" data-toggle="block-toggle-content"><i class="fa fa-arrows-v"></i></a>
                        </div>
                        <h2><strong>Latest Application </strong> History
                            <small> <a href="javascript:void(0)" class="modal-list-data-action btn btn-info btn-xs">
                                    <?php if(isset($supplierInfo['listApplHistoryDetails']) && $supplierInfo['listApplHistoryDetails'] != null ): ?>
                                    <?php echo e(count($supplierInfo['listApplHistoryDetails'])); ?>

                                    <?php endif; ?>
                                </a> </small>
                        </h2>
                    </div>
                    <!-- END Most Viewed Courses Title -->
                    <div class="block-content">
                        <div class="row">
                            <div class="col-md-12">
                                <?php if(isset($supplierInfo['listApplHistoryDetails']) && $supplierInfo['listApplHistoryDetails'] != null ): ?>
                                <div class="table-responsive">
                                    <table class="table  table-condensed table-bordered table-vcenter">
                                        <thead>
                                            <tr>
                                                <th width="10%">Appl No</th>
                                                <th width="13%">Appl Status</th>
                                                <th>Remarks</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $supplierInfo['listApplHistoryDetails']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $appHistory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($appHistory->appl_no); ?></td>
                                                <td>
                                                    <?php echo e($appHistory->status_name); ?>

                                                </td>
                                                <td>
                                                    <?php if(isset($appHistory->listApplSectionReview ) && count($appHistory->listApplSectionReview ) > 0): ?>
                                                    <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                                                    <?php $__currentLoopData = $appHistory->listApplSectionReview; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $objRev): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <?php if($objRev->remark_to_approver && strlen($objRev->remark_to_approver) > 0): ?>
                                                    <strong>Action By User Role: <?php echo e($objRev->role_type); ?></strong> <br />
                                                    <strong>Recommendation: <?php echo e($objRev->recommendation); ?></strong> on <?php echo e($objRev->remark_date); ?> <br />
                                                    <strong>Remark:</strong><span style="font-style: italic"><?php echo e($objRev->remark_to_approver); ?></span> <br />
                                                    <?php endif; ?>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <br />
                                                    <?php endif; ?>

                                                    <?php if(isset($appHistory->listApplRejectReason) && count($appHistory->listApplRejectReason) > 0): ?>
                                                    <strong><span class="bolder">Reject Reason </span></strong><br />
                                                    <?php $__currentLoopData = $appHistory->listApplRejectReason; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $objRej): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <strong><?php echo e($key+1); ?> : on <?php echo e($objRev->changed_date); ?> </strong><br />
                                                    <strong>Remark:</strong><span style="font-style: italic"><?php echo e($objRej->reason_desc); ?></span> <br />
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <br />
                                                    <?php endif; ?>

                                                    <?php if(isset($appHistory->listRemarksCancelReject) && count($appHistory->listRemarksCancelReject) > 0): ?>
                                                    <strong><span class="bolder">Remarks </span></strong><br />
                                                    <?php $__currentLoopData = $appHistory->listRemarksCancelReject; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objRemark): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <strong><?php echo e($objRemark->doc_type); ?></strong> : <span style="font-style: italic"><?php echo e($objRemark->remark); ?></span> <br />
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <br />
                                                    <?php endif; ?>

                                                    <?php if(isset($appHistory->listAttachmentCancelReject) && count($appHistory->listAttachmentCancelReject) > 0): ?>
                                                    <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                                                    <?php $__currentLoopData = $appHistory->listAttachmentCancelReject; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $objAttReject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <strong><a href="<?php echo e(url('/download/attachment/cancel-reject/')); ?>/<?php echo e($objAttReject->attachment_id); ?>" target="_blank"><?php echo e($objAttReject->doc_type); ?> - <?php echo e($objAttReject->file_name); ?></a> </strong>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                    <div class='widget'>
                                                        <div id="count-application-inquiry-<?php echo e($appHistory->appl_id); ?>">
                                                            <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                                                        </div>
                                                    </div>
                                                    <div class='widget'>
                                                        <div id="count-application-rejected-<?php echo e($appHistory->appl_id); ?>">
                                                            <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <!-- END Most Viewed Courses Content -->
                </div>
                <!-- END Most Viewed Courses Block -->
            </div>

        </div>
        <!-- END Main Row -->

        <!-- Main Row -->
        <div class="row">
            <div class="col-md-12">
                <!-- Most Viewed Courses Block -->
                <div class="block">
                    <!-- Most Viewed Courses Title -->
                    <div class="block-title text-info">
                        <h2><strong>Users </strong> Info </h2>
                    </div>
                    <!-- END Most Viewed Courses Title -->
                    <div class="block-section">
                        <div class="row">
                            <div class="col-md-12">
                                <?php if(isset($supplierInfo['listPersonnel']) && $supplierInfo['listPersonnel'] != null ): ?>
                                <?php $__currentLoopData = $supplierInfo['listPersonnel']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $personnel): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="block">
                                    <!-- Your Account Title -->
                                    <div class="block-title">
                                        <h2><strong><?php echo e($key+1); ?>) <?php echo e($personnel->p_name); ?></h2>
                                        <span><?php echo e($personnel->p_ep_role); ?></span>
                                    </div>
                                    <!-- END Your Account Title -->

                                    <!-- Your Account Content -->
                                    <div class="block-section">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Login</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <?php if($personnel->login_id != null): ?>
                                                        <table class="table table-borderless table-striped table-vcenter">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="text-right">Login ID</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->login_id); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Name</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->fullname); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Email</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->email); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">IC No.</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->identification_no); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Mobile</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->mobile_country); ?><?php echo e($personnel->mobile_area); ?><?php echo e($personnel->mobile_no); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Last Login Date</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->login_date); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Record Status</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->u_record_status); ?></strong>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                        <?php else: ?>
                                                        <p>This personnel do not has login info.</p>
                                                        <?php endif; ?>
                                                    </div>
                                                    <!-- END Your Account Content -->
                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>
                                            <div class="col-md-6">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Personnel</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <table class="table table-borderless table-striped table-vcenter">
                                                            <tbody>
                                                                <tr>
                                                                    <td class="text-right">Name</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_name); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Email</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_email); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">IC No.</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_identification_no); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Mobile</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_mobile_country); ?><?php echo e($personnel->p_mobile_area); ?><?php echo e($personnel->p_mobile_no); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Designation</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_designation); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Record Status</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_record_status); ?></strong>
                                                                    </td>
                                                                </tr>
                                                                <tr>
                                                                    <td class="text-right">Softcert Status</td>
                                                                    <td>
                                                                        <strong><?php echo e($personnel->p_is_softcert); ?></strong>
                                                                    </td>
                                                                </tr>
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                    <!-- END Your Account Content -->
                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>

                                            <?php if(isset($personnel->listSoftCert) && $personnel->listSoftCert != null ): ?>
                                            <div class="col-md-12">
                                                <!-- Your Account Block -->
                                                <div class="block">
                                                    <!-- Your Account Title -->
                                                    <div class="block-title">
                                                        <h2><strong>Softcert</strong> Info</h2>
                                                    </div>
                                                    <!-- END Your Account Title -->

                                                    <!-- Your Account Content -->
                                                    <div class="block-section">
                                                        <div class="table-responsive">
                                                            <table class="table  table-condensed table-bordered table-vcenter">
                                                                <thead>
                                                                    <tr>
                                                                        <th>Request ID</th>
                                                                        <th>Provider</th>
                                                                        <th>Record Status</th>
                                                                        <th>Created</th>
                                                                        <th>Is Free</th>
                                                                        <th>Apply Document</th>
                                                                        <th>Issuer</th>
                                                                        <th>Valid From</th>
                                                                        <th>Valid To</th>
                                                                        <th>Cert Updated</th>
                                                                    </tr>
                                                                </thead>
                                                                <tbody>
                                                                    <?php $__currentLoopData = $personnel->listSoftCert; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $softcert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <tr <?php if($softcert->is_active_cert === true): ?> class="info" <?php endif; ?>>
                                                                        <td><?php echo e($softcert->softcert_request_id); ?> <?php echo e($softcert->is_active_cert); ?></td>
                                                                        <td><?php echo e($softcert->softcert_provider); ?></td>
                                                                        <td><?php echo e($softcert->record_status); ?>

                                                                        <?php if($softcert->record_status == 1 && $softcert->softcert_provider == 'TG' && $personnel->p_is_softcert == 3): ?> 
                                                                            <a target="_blank" href="https://www.msctrustgate.com/mytrustid/client/cdc_support?token=sign" 
                                                                                style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                                RESEND UPDATE SOFTCERT <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt;" title="Check on TG Portal if already issued! "></i>
                                                                            </a>
                                                                        <?php endif; ?>

                                                                        </td>
                                                                        <td><?php echo e($softcert->created_date); ?></td>
                                                                        <td><?php echo e($softcert->is_free); ?></td>
                                                                        <td><?php echo e($softcert->type_apply); ?></td>
                                                                        <td> <?php if($softcert->valid_to != null): ?> <?php if($softcert->cert_issuer=='T'): ?> TrustGate <?php else: ?> Digicert <?php endif; ?> <?php endif; ?></td>
                                                                        <td><?php echo e($softcert->valid_from); ?></td>
                                                                        <td><?php echo e($softcert->valid_to); ?></td>
                                                                        <td><?php echo e($softcert->pdc_changed_date); ?></td>
                                                                    </tr>
                                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                    <!-- END Your Account Content -->

                                                </div>
                                                <!-- END Your Account Block -->
                                            </div>
                                            <?php endif; ?>

                                        </div>

                                    </div>
                                    <!-- END Your Account Content -->
                                </div>

                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <?php else: ?>
                                <div class="notify-alert alert alert-warning alert-dismissable">
                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                    <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <!-- END Most Viewed Courses Content -->
                </div>
                <!-- END Most Viewed Courses Block -->
            </div>

        </div>
        <!-- END Main Row -->
    </div>
</div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


<?php endif; ?>
<div id="modal-list-data-inquiries-reject" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-inquiries-reject">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-inquiries-reject" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    var APP_URL = <?php echo json_encode(url('/')); ?>


    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable-inquiries-reject').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });
</script>
<?php if(isset($supplierInfo['listApplHistoryDetails']) && count($supplierInfo['listApplHistoryDetails']) > 0 ): ?>
<script>
    $(document).ready(function() {
        $('.widget').on("click", '.modal-list-data-action', function() {
            $('.spinner-loading').show();
            $('#basic-datatable-inquiries-reject').html('').fadeIn();
            $('#modal-list-data-header-inquiries-reject').text($(this).attr('data-title'));

            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-inquiries-reject').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-inquiries-reject').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });
            $.ajax({
                url: APP_URL + "/find/supplier/application-inquiry/count/<?php echo e($appHistory->appl_id); ?>",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-inquiry-<?php echo e($appHistory->appl_id); ?>').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + "/find/supplier/application-rejected/count/<?php echo e($appHistory->appl_id); ?>",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-rejected-<?php echo e($appHistory->appl_id); ?>').hide().html($data).fadeIn();
                }
            });

        })
    });
</script>
<?php $__currentLoopData = $supplierInfo['listApplHistoryDetails']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $appHistory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
<script>
    $(document).ready(function() {
        $.ajax({
            url: APP_URL + "/find/supplier/application-inquiry/count/<?php echo e($appHistory->appl_id); ?>",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-inquiry-<?php echo e($appHistory->appl_id); ?>').hide().html($data).fadeIn();
            }
        });

        $.ajax({
            url: APP_URL + "/find/supplier/application-rejected/count/<?php echo e($appHistory->appl_id); ?>",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-rejected-<?php echo e($appHistory->appl_id); ?>').hide().html($data).fadeIn();
            }
        });
    });
</script>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.guest-dash', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>