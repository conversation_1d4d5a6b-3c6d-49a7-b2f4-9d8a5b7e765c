<?php

namespace App\Console\Commands\SPKI;

use App\Migrate\CheckSpkiServiceAvailibility;
use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use App\Services\LogTrace\ExportLogService;
use Log;
use Mail;
use Config;

class LogSpkiAvailabilitySchedule extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'LogSpkiAvailabilitySchedule';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To log SPKI availability';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(self::class . ' starting ..', ['Date' => Carbon::now()]);
        $expsvc = new LogSpkiAvailabilitySchedule();
        try {
            CheckSpkiServiceAvailibility::run();
            MigrateUtils::logDump('Completed');
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $expsvc->sendErrorEmail(json_encode($exc->getTrace()));
        }
    }

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error)
    {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server (' . env('APP_ENV') . ') - Error: LogSpkiAvailabilitySchedule'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function ($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                    ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error(__METHOD__.' Error >> '.json_encode( ['Email' => $data["to"], 'ERROR' => $e->getMessage()] ));
            return $e;
        }
    }
}
