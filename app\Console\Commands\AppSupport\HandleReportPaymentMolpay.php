<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;
use App\Migrate\ClientRazerApi;


class HandleReportPaymentMolpay extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleReportPaymentMolpay';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Integrate call API Razer to get daily transaction razer';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     * 
     * 1) Integrate with Razer API Daily Transaction
     * 2) Compare with eP Receipt Payment Transaction
     * 3) If Receipt Payment in eP less than Ra<PERSON>, resend callback to eP
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__.' > '.__FUNCTION__.' >> ';
        MigrateUtils::logDump($clsInfo.'starting ..', [
            'Date' => Carbon::now()]);
        try{    
            $time = Carbon::now()->format('H');
            MigrateUtils::logDump($clsInfo.'Hour now : '. $time);
            if($time==0 || $time=='00'){
                $dateNowSearch = Carbon::yesterday()->format('Y-m-d');
                ClientRazerApi::dailyReportTrans($dateNowSearch);
            }

            $dateNowSearch = Carbon::now()->format('Y-m-d');
            ClientRazerApi::dailyReportTrans($dateNowSearch);

        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump($clsInfo.'error happen!! ' . $exc->getTraceAsString());
        }
        MigrateUtils::logDump($clsInfo.'Done! Completed');
    }

    

}
