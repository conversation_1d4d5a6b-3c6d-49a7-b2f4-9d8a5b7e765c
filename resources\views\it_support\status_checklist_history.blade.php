@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/it_support/checklist') }}"><i class="fa fa-tasks"></i>CheckList</a>
            </li>
            <li class="">
                <a href="{{ url('/it_support/summary_details') }}"><i class="fa fa-list-alt"></i>Summary Details</a>
            </li>
            <li class="active">
                <a href="{{ url('/it_support/checklist/history') }}"><i class="fa fa-history"></i>CheckList History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>


    <div class="block">
        <form id ="it_support_checklist" action="{{ url('/it_support/checklist/history') }}" method="post">
            {{ csrf_field() }}
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            <input readonly="" id="date_search" name="date_search" type="text" value="{{ $date }}"
                class="form-control" style="width: 700px; display:none">

            @if (!empty($listNote))
                <input readonly="" id="list_status" name="list_status" type="text"
                    value="{{ $listNote[0]->ack_status }}" class="form-control" style="width: 700px; display:none">
                <input readonly="" id="list_remarks" name="list_remarks" type="text"
                    value="{{ $listNote[0]->ack_remarks }}" class="form-control" style="width: 700px; display:none">
            @endif

            <div class='text-center' style="background: #ffffff; padding: 5px;">
                <button type="button" id="button_submit_M" name="button_submit_M" class="btn btn-sm btn-info text-center">
                    <div class="h5 mb-0" style="font-weight: 800">Morning Shift</div>
                </button>
                <button type="button" id="button_submit_E" name="button_submit_E" class="btn btn-sm btn-info text-center">
                    <div class="h5 mb-0" style="font-weight: 800">Night Shift</div>
                </button>
            </div>

            <div id="Morning" style="display:none">
                <div class="block-options panel-heading">
                    <h4><strong>
                            <font color="black"id="header-table">Morning Shift</font>
                            <span color="black" id="header-table-date_M">{{ $date }}</span>
                        </strong></h4>
                </div>
                <div class="table-options clearfix" id="clearfix_M">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($menu))
                            @foreach ($menu as $key => $list)
                                <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                    title="{{ $list->esc_group }}" group ="{{ $list->esc_group }}" shift="M">
                                    <input type="radio" name="menu" value=""> {{ $list->esc_group }}
                                </label>
                            @endforeach
                        @endif
                    </div>
                    <div class="text-left spinner-loading" style="padding: 20px;">
                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                    </div>
                </div>
            </div>

            <div id="Evening" style="display:none">
                <div class="block-options panel-heading">
                    <h4><strong>
                            <font color="black"id="header-table">Night Shift</font>
                        </strong></h4>
                </div>

                <div class="table-options clearfix" id="clearfix_E">
                    <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                        @if (isset($menu))
                            @foreach ($menu as $key => $list)
                                @if (in_array($list->esc_group, [
                                        'Common',
                                        'Login test',
                                        'Integration',
                                        'Batch Check',
                                        'SCT Status',
                                        'Backup Status',
                                        'CCTV Review',
                                    ]))
                                    <label id="menu_button1" class="btn btn-primary" data-toggle="tooltip"
                                        title="{{ $list->esc_group }}" group ="{{ $list->esc_group }}" shift="E">
                                        <input type="radio" name="menu" value=""> {{ $list->esc_group }}
                                    </label>
                                @endif
                            @endforeach
                        @endif
                    </div>
                </div>
                <div class="text-center spinner-loading" style="padding: 20px;">
                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                </div>
            </div>

            <div class="table-responsive">

                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
                <div id='helang_dc' style="display:none">
                    <label class="col-md-1 text-right" for="name">Name</label>
                    <div class="col-md-2 helang_remarks">
                        <select id="teststatus" name = "teststatus" class="form-control" style="width: 700px;">
                        </select>
                    </div>
                    <div class="col-md-8" style="display:none">
                        <textarea type="text" id="trytest" name="trytest" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Remarks</label>
                    <div class="col-md-8">
                        <textarea type="text" id="remarks_sct" name="remarks_sct" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-4 text-left notes_aircond" style="display: none">
                        Aircond <br />
                        Temperature : Below 25 Celcius <br />
                        Humidity : Below 80% <br />
                    </label>
                    <label class="col-md-4 text-left notes_fm200" style="display: none">
                        FM 200: <br />
                        Pressure : 27psi - 30psi <br />
                        Note : <br />
                        Below 27psi - Gas has been discharged <br />
                        Above 30psi - Room temperature is high
                    </label>
                </div>

                <div id='cctv_review' style="display:none">
                    <label class="col-md-1 text-right" for="name">Name</label>
                    <div class="col-md-2 cctv_remarks">
                        <select id="cctv_name" name = "cctv_name" class="form-control" style="width: 700px;">
                        </select>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Incident Details</label>
                    <div class="col-md-2">
                        <textarea type="text" id="incident_detail" name="incident_detail" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">Incident Number</label>
                    <div class="col-md-2">
                        <textarea type="text" id="incident_number" name="incident_number" rows="2" class="form-control"></textarea>
                    </div>
                    <label class="col-md-1 text-right" for="remark">CRM Log No</label>
                    <div class="col-md-2">
                        <textarea type="text" id="crm_log_no" name="crm_log_no" rows="2" class="form-control"></textarea>
                    </div>
                </div>
            </div>

            {{-- <div class="block-options">
                <button type="submit" class="btn btn btn-primary editSave" style="display:none"
                    style="float: right;">Update</button>
            </div> --}}
            <div class="text-center editSave" style="display:none">
                <button type="submit" style="background-color: #414770; color: white"
                    class="btn btn btn-primary">Update</button>
            </div>
        </form>
    </div>

    @if (in_array($section, ['server_check', 'status_check']))
        @include('it_support.page_status', [
            'page' => $section == 'status_check' ? 'status' : 'server',
            'location' => old('radio_reqcategory', $section == 'status_check' ? 'Y' : 'R'),
        ])
    @endif
@endsection

@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();

        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('button_submit_M').click();
        });
    </script>
    <script>
        function selectAll(ele) {
            var checkboxes = document.querySelectorAll('input[type="checkbox"][name^="id["]');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].checked = true;
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    checkboxes[i].checked = false;
                }
            }
        }

        function selectColumn(ele, column) {
            // Select all checkboxes for the specified column only
            var checkboxes = document.querySelectorAll('input[type="checkbox"][data-column="' + column + '"]');

            if (ele.checked) {
                // Check all checkboxes in the specified column
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = true;
                });
            } else {
                // Uncheck all checkboxes in the specified column
                checkboxes.forEach(function(checkbox) {
                    checkbox.checked = false;
                });
            }
        }

        $(document).ready(function() {
            function handleClick(button, menu, showElem, hideElem1, hideElem2, clearfixShow, clearfixHide) {
                $(button).click(function() {
                    var dateSelection = $('#date_search').val();
                    $('#selected_date').val(dateSelection);

                    var ack_status = $('#list_status').val();
                    var ack_remarks = $('#list_remarks').val();
                    if (ack_status === undefined || ack_status === null || ack_status.trim() === "") {
                        $('#remarks').val("");
                    } else if (ack_status === 'Completed') {
                        $('#remarks').val(ack_remarks);
                    }
                    $(button).addClass("active");
                    $(menu).click().addClass("active");
                    $(showElem).show();
                    $(hideElem1).hide();
                    $(clearfixShow).show();
                    $(clearfixHide).hide();
                    $('.editSave').show();
                    if ($.fn.DataTable.isDataTable('#list_datatable')) {
                        $('#list_datatable').DataTable().destroy();
                        $(menu).click().addClass("active");
                    }
                    $('#list_datatable thead, #list_datatable tbody').empty();
                });
            }

            function setButtonStyle(buttonId, defaultColor, hoverColor, textColor, fontSize) {
                let clicked = false;
                $(buttonId).css({
                    'background-color': defaultColor,
                    'color': textColor,
                    'font-size': fontSize
                }).hover(
                    function() {
                        if (!clicked) {
                            $(this).css({
                                'background-color': hoverColor,
                                'color': textColor
                            });
                        }
                    },
                    function() {
                        if (!clicked) {
                            $(this).css({
                                'background-color': defaultColor,
                                'color': textColor
                            });
                        }
                    }
                ).click(function() {
                    clicked = true;
                    $(this).css({
                        'background-color': hoverColor,
                        'color': textColor
                    });
                });
            }

            // Apply styles to both buttons
            setButtonStyle('#button_submit_M', '#2db0bf', '#333c79', 'white', '150%');
            setButtonStyle('#button_submit_E', '#2db0bf', '#333c79', 'white', '150%');

            handleClick('#button_submit_M', '#menu_button', '#Morning', '#Evening', '#clearfix_M', '#clearfix_E');
            handleClick('#button_submit_E', '#menu_button1', '#Evening', '#Morning', '#clearfix_E', '#clearfix_M');
        });

        function changeMenu(a) {
            let
                group = $(a).attr('group');
            let
                shift = $(a).attr('shift');
            var dateSelection = $('#date_search').val();
            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
            }

            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();

            $('.spinner-loading').show();

            $.ajax({
                type: "GET",
                url: "/it_support/find_by/" + group + "/" + dateSelection + "/" + shift
            }).done(function(data) {
                $('.spinner-loading').hide();
                $('#list_datatable').html(data).fadeIn();
                $('#list_datatable').dataTable({
                    ordering: false,
                    lengthMenu: [
                        [20, 30, 50, -1],
                        [20, 30, 50, 'All']
                    ]
                });
            })

            $('#cctv_review').hide();
            $('#helang_dc').hide();
            $('.notes_aircond').hide();
            $('.notes_fm200').hide();

            $('.editSave').show();

            function fetchData(group, dateSelection, shift, elementToUpdate) {
                $.ajax({
                    type: "GET",
                    url: "/it_support/checklist/remarks/helangdcstatus/" + group + "/" + dateSelection + "/" +
                        shift,
                }).done(function(data) {
                    let items = "<option value='' disabled selected>Please select</option>";
                    $.each(data, function(i, item) {
                        items += "<option value='" + item.task_id + "'>" + item.esc_name + "</option>";
                    });
                    $(elementToUpdate).html(items).trigger("change");
                });
            }

            function resetCommonFields() {
                $('#remarks_sct').val("");
                $('#teststatus').empty();
            }

            if (group === 'SCT Status' || group === 'Backup Status') {
                $('#helang_dc').show();
                resetCommonFields();
                fetchData(group, dateSelection, shift, '#teststatus');
            }

            if (group === 'CCTV Review') {
                $('#cctv_review').show();
                $('#cctv_name').empty();
                $("#incident_detail, #incident_number, #crm_log_no").val("");
                fetchData(group, dateSelection, shift, '#cctv_name');
            }
        }

        $(".btn-group").on("click", "#menu_button", function(e) {
            changeMenu(this)
        });

        $(".btn-group").on("click", "#menu_button1", function(e) {
            changeMenu(this)
        });

        function updateRemarks(taskId, updateFields) {
            $.ajax({
                url: "/it_support/checklist/remarks/status/" + taskId,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    const taskData = data[0];
                    if (taskData) {
                        for (const field in updateFields) {
                            $(field).val(taskData[updateFields[field]]);
                        }
                    }
                }
            });
        }

        function toggleNotes(selectedText, aircondClass, fm200Class) {
            if (selectedText === 'Aircon' || selectedText === 'Air Con') {
                $(aircondClass).show();
                $(fm200Class).hide();
            } else if (selectedText === 'FM200 Gas Pressure') {
                $(fm200Class).show();
                $(aircondClass).hide();
            } else {
                $(aircondClass).hide();
                $(fm200Class).hide();
            }
        }

        $('.helang_remarks').on("change", '#teststatus', function() {
            $("#remarks_sct").val("");
            const taskId = $('#teststatus').val();
            const selectedText = $('#teststatus option:selected').text();

            console.log("Selected Text:", selectedText);
            toggleNotes(selectedText, '.notes_aircond', '.notes_fm200');

            $.ajax({
                url: "/it_support/checklist/remarks/status/" + taskId,
                dataType: 'json',
                type: "GET",
                success: function(data) {
                    if (data && data.length > 0) {
                        $("#remarks_sct").val(data[0].task_remarks);
                        $("#trytest").val(data[0].task_name);

                        console.log("Task Name:", data[0].task_name);
                        const taskName = data[0].task_name;
                        toggleNotes(taskName, '.notes_aircond', '.notes_fm200');
                    } else {
                        console.log("No data found for taskId:", taskId);
                    }
                },
                error: function() {
                    console.log("Error fetching data for taskId:", taskId);
                }
            });
        });

        $('.cctv_remarks').on("change", '#cctv_name', function() {
            const taskId = $('#cctv_name').val();
            updateRemarks(taskId, {
                "#incident_detail": "incident_detail",
                "#incident_number": "incident_number",
                "#crm_log_no": "crm_log_no"
            });
        });
    </script>
@endsection
