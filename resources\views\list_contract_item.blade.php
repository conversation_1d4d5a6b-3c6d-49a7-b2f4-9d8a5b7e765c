@extends('layouts.guest-dash')

@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }
    .table tbody > tr > td {
        font-size: 10px;
    }
</style>   

@if ($listcontractitem == null)
<div class="block block-alt-noborder full">
    <div class="block">
        <div class="content-header">
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>e<PERSON><PERSON><PERSON>han (Kontrak) : {{$contractno}} </strong>
                        <small>Versi Kontrak : {{$version}}</small>
                    </h1>
                </div>
            </div>
            <div class="block">
                <p>Tiada Rekod!</p>
            </div>
        </div>
    </div>
</div>
@endif
@if($listcontractitem != null)
<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>e<PERSON><PERSON><PERSON>han (Kontrak) : {{ $contractno }}</strong>
                <small>Versi Kontrak : {{$listcontractitem[0]->latest_contract_ver_id}}</small>
            </h1>
        </div>
        @if(Auth::user()->isPatcherRolesEp())
        <div class="block-options pull-right action-today">
            <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
               data-toggle="modal" data-url="{{url('/support/report/log/patch-data-contract-item')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
               data-title="List Action Patch Data Today ">View Today Action</a>
        </div> 
        <br/><br/><br/>
        <div class="block original-table">
            <div class="block-title  panel-heading">
                <h1><i class="fa"></i> <strong>Contract Amount</strong></h1>
            </div>
            <form id="form-contract-item" action="" method="post" class="form-horizontal" onsubmit="return true;">

                <input name="_method" id="_method" type="hidden" value="POST">
                <div class="table-responsive form-group">
                    <table class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Contract Version Id</th>
                                <th class="text-center">Contract Amount</th>
                                <th class="text-center">Update Contract Amount</th>
                                <th class="text-center">GST Amount</th>
                                <th class="text-center">Update GST Amount</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td class="text-center">{{ $listcontractitem[0]->latest_contract_ver_id }}</td>
                                <td class="text-center">{{ $listcontractitem[0]->contract_amount }}</td>
                                <td class="text-center">
                                    <input type="number" name="update-contract-amount" id="update-contract-amount" value="{{ $listcontractitem[0]->contract_amount }}" oninput="validate(this)"/>
                                </td>
                                <td class="text-center">{{ $listcontractitem[0]->gst_amt }}</td>
                                <td class="text-center">
                                    <input type="number" name="update-gst-amount" id="update-gst-amount" value="{{ $listcontractitem[0]->gst_amt }}" oninput="validate(this)"/>
                                </td>
                                <td hidden="true">
                                    <input type="number" name="contract-amount" id="contract-amount" value="{{ $listcontractitem[0]->contract_amount }}"/>
                                    <input type="number" name="gst-amount" id="gst-amount" value="{{ $listcontractitem[0]->gst_amt }}"/>
                                    <input type="number" name="ct-version-id" id="ct-version-id" value="{{ $listcontractitem[0]->latest_contract_ver_id }}"/>
                                </td>
                                <td>
                                    <div id="form-button-ct">
                                        <center>
                                            <button type="button" class="btn btn-sm btn-info center" id="updatecontractlevel" name="updatecontractlevel" onclick="updateCtAmount()">Update</button>
                                        </center>
                                    </div>
                                    <!--<button type="button" class="btn btn-sm btn-info center" id="updatecontractlevel" name="updatecontractlevel" onclick="updateCtAmount()">Update</button></center>-->
                                </td>
                            </tr>
                        </tbody>
                    </table> 
                </div>
            </form>
        </div>
        @endif
        <!--end table contract level-->
        <!--start table contract item-->
        <div class="block original-table">
            <div class="block-title  panel-heading">
                <h1><i class="fa"></i> <strong>Contract Item</strong></h1>
            </div>
            <div class="table-responsive">
                <table id="table-contract-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Request Item Id</th>
                            <th class="text-center">Item Name</th>
                            <th class="text-center">Extension Code</th>
                            <th class="text-center">Item Price Id</th>
                            @if(Auth::user()->isPatcherRolesEp() || Auth::user()->isAllowUserCrMgmt())
                            <th class="text-center">Unit Price</th>
                            @endif
                            @if(Auth::user()->isPatcherRolesEp())
                            <th class="text-center"></th>
                            @endif
                            <th class="text-center" style="display:none"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($listcontractitem as $data)
                        <tr class="{{ $data->request_item_id }}">
                            <td class="text-center">{{ $data->request_item_id }}</td>
                            <td class="text-center">{{ $data->item_name }}</td>
                            <td class="text-center">{{ $data->extension_code }}</td>
                            <td class="text-center">{{ $data->item_price_id }}</td>
                            @if(Auth::user()->isPatcherRolesEp() || Auth::user()->isAllowUserCrMgmt())
                            <td class="text-center">{{ $data->unit_price }}</td>
                            @endif
                            @if(Auth::user()->isPatcherRolesEp())
                            <td>
                                <button id="updatebtn" name="updatebtn" data-toggle="modal" 
                                        class="open-myModal btn btn-sm btn-info"
                                        data-req-id="{{ $data->request_item_id }}"
                                        data-item-name="{{ $data->item_name }}"
                                        data-ext-code="{{ $data->extension_code }}"
                                        data-item-price-id="{{ $data->item_price_id }}"
                                        data-unit-price="{{ $data->unit_price }}"
                                        data-contract-no="{{ $contractno }}"
                                        data-contract-version="{{ $listcontractitem[0]->latest_contract_ver_id }}"
                                        onclick="openModal()">Detail</button>
                            </td>
                            @endif
                            <td class="text-center" style="display:none"></td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>    
            </div>
        </div>
        <!--end table-->
        @if(Auth::user()->isPatcherRolesEp())
        <!--start table update-->
        <div class="block original-table">
            <div class="block-title  panel-heading">
                <h1><i class="fa"></i> <strong>Update Contract Item</strong></h1>
            </div>
            <form id="form-contract-item" action="" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST">
                <div class="table-responsive form-group">
                    <table id="table-contractedit-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center"></th>
                                <th class="text-center">Request Item Id</th>
                                <th class="text-center">Item Name</th>
                                <th class="text-center">Extension Code</th>
                                <th class="text-center">Item Price Id</th>
                                <th class="text-center">Current Unit Price</th>
                                <th class="text-center">Updated Unit Price</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($listcontractitem as $data)
                            <tr class="text-center" id = "{{ $data->request_item_id }}" style="display:none">
                                <td class="text-center" ><input type="checkbox" class="mycheckbox" id="checkbox" name="checkbox[]" value="{{ $data->request_item_id }}"></td>
                                <td class="text-center" >{{ $data->request_item_id }}</td>
                                <td class="text-center" >{{ $data->item_name }}</td>
                                <td class="text-center" >{{ $data->extension_code }}</td>
                                <td class="text-center" >{{ $data->item_price_id }}</td>
                                <td class="text-center" >{{ $data->unit_price }}</td>
                                <td class="text-center">
                                    <input type="number" name="update-unit-price" id="update-unit-price" value="{{ $data->unit_price }}" oninput="validate(this)"/>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table> 
                </div>
            </form>
        </div>  
        <div class="form-group form-actions" id="form-button" style="display:none">
            <center><button type="submit" id="submit-btn" class="btn btn-sm btn-primary center action_confirm_submit">Submit</button>
                <button type="button" class="btn btn-sm btn-danger center" id="delete-button" name="delete-button" onclick="deleteRow()">Delete</button></center>
        </div>
        <!--end table update-->
        <!--start modal contract item--> 
        <div class="modal" id="myModal" style="display:none">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <center><h4 class="modal-title" id="myModalLabel" >Update Contract Item</h4></center>
                    </div>
                    <div class="modal-body">
                        <table id="table-modal" class="table table-vcenter table-condensed table-bordered">
                            <tbody>
                                <tr>
                                    <th>Unit Price <span class="text-danger">*</span></th>
                                    <td>
                                        <input type="number" name="unitPrice" id="unitPrice" style="width:100%" class="form-control" oninput="validate(this)"/>
                                    </td>
                                </tr>
                                <tr>
                                    <th>Item Price Id</th>
                                    <td><input type="text" name="priceId" id="priceId" readonly="true" style="width:100%" class="form-control"/></td>
                                </tr>
                                <tr hidden="true">
                                    <td><input type="text" name="reqId" id="reqId" hidden="true"/></td>
                                    <td><input type="text" name="itemName" id="itemName" hidden="true"/></td>
                                    <td><input type="text" name="extCode" id="extCode" hidden="true"/></td>
                                    <td><input type="text" name="originalPrice" id="originalPrice" hidden="true"/></td>
                                    <td><input type="text" name="contractNo" id="contractNo" hidden="true"/></td>
                                    <td><input type="text" name="contractVersion" id="contractVersion" hidden="true"/></td>
                                </tr>
                            </tbody>
                        </table>
                        <center><button type="button" class="btn btn-primary center" id="save" name="save" onclick="createTable()">Update</button></center>
                    </div>
                </div>
            </div>
        </div>
        <!--end modal contract item-->
        <div class="block">
            <div class="block-title">
                <h2><i class="fa"></i> <strong>Script to Update Contract</strong></h2>
                <div class="block-options pull-right">
                <!--  <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                          onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>-->
                </div>
            </div>
            <pre class="language-markup">
                <p>/* <br/>Contract No: {{ $contractno }} <br/>Contract Ver ID: {{ $listcontractitem[0]->latest_contract_ver_id }}<br/>*/</p>
                
                <div class="ctamount">
                    <p>/* <br/>Contract Amount <br/>*/</p>
                    <div class="div-ct-amount" id="div-ct-amount"></div>
                </div>
                <div id="container" class="inner">
                    <p>/* <br/>Contract Item <br/>*/</p>
                </div>
            </pre>
        </div>
        @endif
    </div>
</div>
@endif

@include('_shared._modalListLogAction')

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script>
    App.datatables();
    $('#table-contract-datatable').dataTable({
        order: [[1, "desc"]],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });
    $('#table-contractedit-datatable').dataTable({
        order: [[2, "desc"]],
        columnDefs: [],
        pageLength: -1,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });
</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
        ModalListActionLogDatatable.init();
    });</script>
<script>
    var chkArray = [];
    var objTable = document.getElementById('table-contractedit-datatable');
    var randomnumber = Math.floor(Math.random() * 100000) + 1;

    function openModal() {
        var modal = document.getElementById('myModal');
        var span = document.getElementsByClassName("close")[0];

        modal.style.display = "block";

        $(document).on("click", ".open-myModal", function () {

            var reqId = $(this).data('req-id');
            var itemName = $(this).data('item-name');
            var extCode = $(this).data('ext-code');
            var itemPriceId = $(this).data('item-price-id');
            var unitPrice = $(this).data('unit-price');
            var contractNo = $(this).data('contract-no');
            var contractVersion = $(this).data('contract-version');

            $("#reqId").val(reqId);
            $("#itemName").val(itemName);
            $("#extCode").val(extCode);
            $("#priceId").val(itemPriceId);
            $("#unitPrice").val(unitPrice);
            $("#originalPrice").val(unitPrice);
            $("#contractNo").val(contractNo);
            $("#contractVersion").val(contractVersion);
            $("#editUnitPrice").val(unitPrice);
        })


        span.onclick = function () {
            modal.style.display = "none";
        }
    }

    function createTable() {

        var reqId = $("#reqId").val();
        var modal = document.getElementById('myModal');
        modal.style.display = "none";

        var submitbtn = document.getElementById('form-button');
        submitbtn.style.display = "block";

        $('#' + reqId).show();
        var unitPrice = $("#unitPrice").val();

        var tr = document.getElementById(reqId);
        var tds = tr.getElementsByTagName("td");

        if ($('#' + reqId + ':visible').length !== 0) {
            for (var i = 0; i < tds.length; i++) {
                document.getElementById(reqId).getElementsByTagName('td')[6].getElementsByTagName('input')[0].value = unitPrice;
            }
        }
    }

    function deleteRow() {

        var chkArray = [];
        $(".mycheckbox:checked").each(function () {
            chkArray.push($(this).val());
        });

        for (var i = 0; i < chkArray.length; i++) {
            $('#' + chkArray[i]).hide();
            document.getElementById(chkArray[i]).getElementsByTagName('td')[0].getElementsByTagName('input')[0].checked = false;
            document.getElementById("container").innerHTML = "/* <br/> Contract Item <br/> */";
        }

        var reqId = $("#reqId").val();
        if ($('#' + reqId + ':visible').length === 0) {
            var submitbtn = document.getElementById('form-button');
            submitbtn.style.display = "none";
            console.log('aaa');
        } else {
            submitbtn.style.display = "block";
            console.log('bbb');
        }
    }

    function validate(evt) {
        var t = evt.value;
        evt.value = (t.indexOf(".") >= 0) ? (t.substr(0, t.indexOf(".")) + t.substr(t.indexOf("."), 3)) : t;
    }

    function updateCtAmount() {
        var submitbtn = document.getElementById('form-button');
        submitbtn.style.display = "block";

        var contractVersion = $("#ct-version-id").val();
        var ctAmount = $("#contract-amount").val();
        var gstAmount = $("#gst-amount").val();
        var latestCtAmount = $("#update-contract-amount").val();
        var latestGstAmount = $("#update-gst-amount").val();

        var ctAmountData = 'update ct_contract_amount set contract_amount = ' + latestCtAmount + ' where contract_ver_id = ' + contractVersion + ' and contract_amount = ' + ctAmount + ';' + '<br/>';
        var gstAmountDate = 'update ct_contract_amount set gst_amt = ' + latestGstAmount + ' where contract_ver_id = ' + contractVersion + ' and gst_amt = ' + gstAmount + ';';
        var ctdata = '';

        if (latestCtAmount !== ctAmount && latestGstAmount !== gstAmount) {

            ctdata = ctdata + ctAmountData + gstAmountDate;
            document.getElementById('div-ct-amount').innerHTML = ctdata;

        } else if (latestCtAmount !== ctAmount && latestGstAmount === gstAmount) {

            ctdata = ctdata + ctAmountData;
            document.getElementById('div-ct-amount').innerHTML = ctdata;

        } else if (latestCtAmount === ctAmount && latestGstAmount !== gstAmount) {

            ctdata = ctdata + gstAmountDate;
            document.getElementById('div-ct-amount').innerHTML = ctdata;

        } else {
            document.getElementById('div-ct-amount').innerHTML = ctdata;

        }

    }
    $('div.form-actions').on("click", 'button.action_confirm_submit', function (event) {
        for (var i = 1; i < objTable.rows.length; i++) {
            var objCells = objTable.rows.item(i).cells;
            var reqId = '';
            var itemPriceId = '';
            var originalPrice = '';
            var latestPrice = '';
            var data = '';

            for (var j = 0; j < objCells.length; j++) {
                reqId = objCells.item(1).innerHTML;
                itemPriceId = objCells.item(4).innerHTML;
                originalPrice = objCells.item(5).innerHTML;
                latestPrice = objCells.item(6).children[0].value;

                if ($('#' + reqId + ':visible').length !== 0) {
                    data = 'update ct_item_price set unit_price = ' + latestPrice + ' where item_price_id = ' + itemPriceId + ' and unit_price = ' + originalPrice + ';';

                    if (latestPrice !== '' && latestPrice !== originalPrice) {
                        var divScript = document.getElementById(itemPriceId);
                        var div = document.createElement('div');
                        if (divScript === null) {
                            div.setAttribute('id', itemPriceId);
                            $(div).addClass("mydata").html(data);
                            $("#container").append(div);
                        } else {
                            document.getElementById(itemPriceId).innerHTML = data;
                        }
                    }
                }
            }

        }
        event.preventDefault();
        var contractNo = $("#contractNo").val();
        var contractVersion = $("#contractVersion").val();
        var mydata = $('.mydata').text();
        var ctdata = $('.div-ct-amount').text();
        var csrf = $("input[name=_token]").val();


        $.ajax({
            url: "/find/contractInfo/" + contractNo + "/" + contractVersion,
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "data": mydata, "ctdata": ctdata, "randomnumber": randomnumber},
            context: document.body
        });
    });
</script>
@endsection



