@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/app-support/dashboard/pm') }}">PM</a>
            </li>
            <li class="active">
                <a href="{{ url('/app-support/dashboard/sm') }}">SM</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
    <div class="row">
        <div class="col-lg-4">
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_supplierRegisterNotExistSAP_refresh"
                                class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                    class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        List of Supplier Registered With Not Exist SAP Vendor Code
                    </h5>
                </div>
                <div id="dash_supplierRegisterNotExistSAPError" style="padding: 20px;">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_monitorSmDataIssue_refresh" class="btn btn-default"
                                data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                    class="fa fa-refresh"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        Monitor - SM Data Issue
                    </h5>
                </div>
                <div id="dash_monitorSmDataIssue" style="padding: 20px;">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div class="modal fade" id="supplierModal" tabindex="-1" role="dialog" aria-labelledby="supplierModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="supplierModalLabel">Supplier Details</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="supplierModalBody">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
@endif
<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!!json_encode(url('/'))!!};

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });

    $(document).ready(function () {
        $('#dash_supplierRegisterNotExistSAP_refresh').on("click", function () {
            $('#dash_supplierRegisterNotExistSAPError').html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
            $.ajax({
                url: APP_URL + '/app-support/dashboard/supplier-register-not-exist-sap',
                success: function (data) {
                    $data = $(data);
                    $('#dash_supplierRegisterNotExistSAPError').hide().html($data).fadeIn();
                }
            });
        });

        $('#dash_monitorSmDataIssue_refresh').on("click", function () {
            $('#dash_monitorSmDataIssue').html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
            $.ajax({
                url: APP_URL + '/app-support/dashboard/monitor-sm-data-issue',
                success: function (data) {
                    $data = $(data);
                    $('#dash_monitorSmDataIssue').hide().html($data).fadeIn();
                }
            });
        });

        $.ajax({
            url: APP_URL + '/app-support/dashboard/supplier-register-not-exist-sap',
            success: function (data) {
                $data = $(data);
                $('#dash_supplierRegisterNotExistSAPError').hide().html($data).fadeIn();
            }
        });

        $.ajax({
            url: APP_URL + '/app-support/dashboard/monitor-sm-data-issue',
            success: function (data) {
                $data = $(data);
                $('#dash_monitorSmDataIssue').hide().html($data).fadeIn();
            }
        });

        $(document).on('click', '.clickable-row-sap', function () {
            var supplierTypeCode = $(this).data('supplier-type');
            console.log(supplierTypeCode)
            $('#supplierModal').modal('show');
            $('#supplierModalBody').html("<div class='text-center'><i class='fa fa-spinner fa-4x fa-spin'></i></div>");
            var url = APP_URL + '/app-support/dashboard/list-supplier-register-not-exist-sap/'+supplierTypeCode;
            if (url) {
                $.ajax({
                    url: url,
                    method: 'GET',
                    success: function (data) {
                        $('#supplierModalBody').html(data);
                    },
                    error: function (xhr, status, error) {
                        console.error("AJAX Error: " + status + "\nError: " + error);
                        $('#supplierModalBody').html("An error occurred while fetching data.");
                    }
                });
            }
        });

        $(document).on('click', '.clickable-row-monitor-sm', function (e) {
            e.preventDefault();

            var issueType = $(this).data('issue-type');
            var href = $(this).data('href');

            console.log('Issue Type:', issueType);
            console.log('URL:', href);

            if (href) {
                // Open the link in a new tab
                window.open(href, '_blank');
            } else {
                console.error('No href found for this row');
            }
        });
    })
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection