/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
$(document).ready(function () {
    var terminateTask = document.getElementById("terminate_instance");
    if (terminateTask) {
        terminateTask.onclick = function () {
            document.getElementById("failed_ul").style.display = "none";
            var instanceId = $("input[name=composite_instance_id]").val();
            var module = terminateTask.getAttribute("compModule");
            var csrf = $("input[name=_token]").val();
            $('#modal_terminate_instance').modal('show');
            document.getElementById("terminate_instance_id").innerHTML = instanceId;
            var submitTerminate = document.getElementById("submit_terminate");
            submitTerminate.onclick = function () {
                document.getElementById("failed_ul").style.display = "none";
                $('#modal_terminate_instance').modal('hide');
                $('#modal_spinner').modal('show');
                $.ajax({
                    type: "POST",
                    url: "/bpm/instance/terminate/" + instanceId,
                    data: {
                        "_token": csrf, 
                        "instance_id": instanceId, 
                        "module": module,
                        "flow_id": $("input[name=flow_id]").val(),
                        "doc_no": $("input[name=doc_no]").val()
                    },
                    error: function (xhr, status, error) {
                        console.log('call ajax submitTerminate onclick ' + xhr.status + ': ' + xhr.statusText);
                        document.getElementById("failed_ul").style.display = "block";
                        $("#failed").html(xhr.status + ': ' + xhr.statusText);
                    }
                }).done(function (resp) {
                    $('#modal_spinner').modal('hide');
                    if (resp.status === 'Success Terminate Instance') {
                        $('#success').show();
                        document.getElementById("success").innerHTML = resp.status;
                        location.reload();
                    } else {
                        console.log('failed submitTerminate onclick ' + resp.statusApi);
                        document.getElementById("failed_ul").style.display = "block";
                        $("#failed").html(resp.statusApi);
                    }
                });
            };
        };
    }
});
//suspend resume action for both bpmn and task flow
function suspendResumeAction(action, id, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";
    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/process/action/id/" + id + "/status/" + status,
        data: {"_token": csrf, "id": id, "action": action, "composite_id": cmpstId},
        error: function (xhr, status, error) {
            console.log('suspendResumeAction ' + xhr.status + ': ' + xhr.statusText);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.status === 'success') {
            $('#success').show();
            document.getElementById("success").innerHTML = resp.status;
            location.reload();
        } else {
            console.log('suspendResumeAction failed ' + resp.status);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.status);
        }
    });
}

//withdraw action for task flow
function withdrawAction(action, id, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";
    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/task/withdraw/id/" + id + "/status/" + status,
        data: {"_token": csrf, "id": id, "action": action, "composite_id": cmpstId},
        error: function (xhr, status, error) {
            console.log('withdrawAction ' + xhr.status + ': ' + xhr.statusText);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.status === 'Success') {
            $('#success').show();
            document.getElementById("success").innerHTML = resp.status;
            location.reload();
        } else {
            console.log('withdrawAction failed ' + resp.status);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.status);
        }
    });
}

//reassign action for task flow
function reassignAction(action, id, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";
    $('#modal_reassign').modal('show');
    var submitReassignBtn = document.getElementById("submit_reassign");
    submitReassignBtn.onclick = function () {

        var assignUser = document.getElementById('assign_user');
        var assignUserValue = assignUser.value;
        if (assignUserValue == '') {

            $('#assign_user').after('<div style="color:red"> User cannot be null</div>');
        } else {

            $('#modal_reassign').modal('hide');
            $('#modal_spinner').modal('show');
            $.ajax({
                type: "POST",
                url: "/bpm/task/reassign/id/" + id + "/status/" + status,
                data: {"_token": csrf, "id": id, "assignee": assignUserValue, "action": action, "composite_id": cmpstId},
                error: function (xhr, status, error) {
                    console.log('reassignAction ' + xhr.status + ': ' + xhr.statusText);
                    document.getElementById("failed_ul").style.display = "block";
                    $("#failed").html(xhr.status + ': ' + xhr.statusText);
                }
            }).done(function (resp) {
                $('#modal_spinner').modal('hide');
                if (resp.status === 'Success') {
                    $('#success').show();
                    document.getElementById("success").innerHTML = resp.status;
                    location.reload();
                } else {
                    console.log('reassignAction failed ' + resp.status);
                    document.getElementById("failed_ul").style.display = "block";
                    $("#failed").html(resp.status);
                }
            });
        }
    }
}

//execute action for task flow
function executeAction(action, taskid, user, taskaction, updatedpayload, param, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";

    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/task/execute/taskid/" + taskid,
        data: {"_token": csrf, "action": action, "taskid": taskid, "user": user, "taskaction": taskaction, 
            "updatedpayload": updatedpayload, "status": status, "param": param, "composite_id": cmpstId},
        error: function (xhr, status, error) {
            console.log('executeAction ' + xhr.status + ': ' + xhr.statusText);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.status === 'Success') {
            $('#success').show();
            document.getElementById("success").innerHTML = resp.status;
            location.reload();
        } else {
            console.log('executeAction ' + resp.status);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.status);
        }
    });
}

//alterflow action for bpmn flow
function alterflowAction(action, processId, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";
    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/process/action/alterflow/processid/" + processId + "/status/" + status,
        data: {"_token": csrf, "process_id": processId, "action": action, "composite_id": cmpstId},
        error: function (xhr, status, error) {
            console.log('alterflowAction ' + xhr.status + ': ' + xhr.statusText);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.status != 'success alter flow') {
            console.log('alterflowAction failed ' + resp.statusAPI);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.statusAPI);
        } else {
            $('#failed').hide();
            for (const [listdata, value] of Object.entries(resp.listdata)) {
                var sourceValue = document.getElementById('source_value');
                var targetValue = document.getElementById('target_value');
                for (const [data, val] of Object.entries(value["sources"])) {
                    var sourcename = val.id + ' - ' + val.name;
                    sourceValue.add(new Option(sourcename));
                }

                for (const [data, val] of Object.entries(value["targets"])) {
                    var targetname = val.id + ' - ' + val.name;
                    targetValue.add(new Option(targetname));
                }
            }

            $('#modal_alterflow').modal('show');
            //click submit alter flow button
            var submitAlterFlowBtn = document.getElementById("submit-alterflow");
            submitAlterFlowBtn.onclick = function () {
                document.getElementById("failed_ul").style.display = "none";
                var sourceValueVal = sourceValue.value.substring(0, sourceValue.value.indexOf(" - "));
                var targetValueVal = targetValue.value.substring(0, targetValue.value.indexOf(" - "));
                $('#modal_alterflow').modal('hide');
                $('#modal_spinner').modal('show');
                $.ajax({
                    type: "POST",
                    url: "/bpm/process/action/alterflow/processid/" + processId + "/source/" + sourceValueVal + "/target/" + targetValueVal,
                    data: {"_token": csrf, "process_id": processId, "source": sourceValueVal, "target": targetValueVal, "composite_id": cmpstId},
                    error: function (xhr, status, error) {
                        console.log('submitAlterFlowBtn ' + xhr.status + ': ' + xhr.statusTex);
                        document.getElementById("failed_ul").style.display = "block";
                        $("#failed").html(xhr.status + ': ' + xhr.statusText);
                    }
                }).done(function (resp) {
                    $('#modal_spinner').modal('hide');
                    if (resp.status === 'Success Submit Alter Flow') {
                        $('#success').show();
                        document.getElementById("success").innerHTML = resp.status;
                        setTimeout(location.reload.bind(location), 3000);
                    } else {
                        console.log('submitAlterFlowBtn failed ' + resp.statusApi);
                        document.getElementById("failed_ul").style.display = "block";
                        $("#failed").html(resp.statusApi);
                    }
                });
            };
        }

    });
}

//variable action for bpmn flow
function variableAction(action, processId, status, csrf, cmpstId) {
    document.getElementById("failed_ul").style.display = "none";

    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/process/action/variable/processid/" + processId + "/status/" + status,
        data: {"_token": csrf, "process_id": processId, "action": action, "composite_id": cmpstId},
        error: function (xhr, status, error) {
            console.log('variableAction ' + xhr.status + ': ' + xhr.statusText);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.status === 'success variable') {
            var html = '';
            var datas = [];
            for (const [listdata, value] of Object.entries(resp.listdata)) {

                for (const [data, val] of Object.entries(value["variables"])) {

                    html += '<tbody style="font-size:80%;width: 100%;overflow: auto;height: 20%"><tr>';
                    html += '<td class="variableDetail" data-target="variable-detail" data-id="' + val["key"] + '"><a style="color:black;text-decoration:none;">' + val["key"] + '</a></td>';
                    datas.push(val);
                    html += '</tr></tbody>';
                }
            }

            document.getElementById("table-variable").innerHTML = html;
            $('#modal_variable').modal('show');
            //click variable on table row
            $('.variableDetail').click(function () {
                var variables = $(this).attr('data-id');
                for (const [data, val] of Object.entries(datas)) {
                    if (variables === val["key"]) {
                        var newValue = String(val["value"]).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
//                            console.log(newValue);                            
                        $("textarea#variablekey").val(val["key"]);
                        $("textarea#variablevalue").val(val["value"]);
                    }
                }
            });
            //click submit variable button
            var submitVariableBtn = document.getElementById("submit-variable");
            submitVariableBtn.onclick = function () {
                document.getElementById("failed_ul").style.display = "none";
                var variableKeyVal = $('textarea#variablekey').val();
                var variableValueVal = $('textarea#variablevalue').val();
//                        console.log(variableKeyVal + variableValueVal);

                $('#modal_variable').modal('hide');
                if (variableKeyVal !== '' && variableValueVal !== '') {
                    $('#modal_spinner').modal('show');
                    $.ajax({
                        type: "POST",
                        url: "/bpm/process/submit/variable/processid/" + processId,
                        data: {"_token": csrf, "process_id": processId, "key": variableKeyVal, "value": variableValueVal, "composite_id": cmpstId},
                        error: function (xhr, status, error) {
                            console.log('submitVariableBtn ' + xhr.status + ': ' + xhr.statusText);
                            document.getElementById("failed_ul").style.display = "block";
                            $("#failed").html(xhr.status + ': ' + xhr.statusText);
                        }
                    }).done(function (resp) {
                        $('#modal_spinner').modal('hide');
                        if (resp.status === 'Success Submit Variable') {
                            $('#success').show();
                            document.getElementById("success").innerHTML = resp.status;
                        } else {
                            console.log('submitVariableBtn failed ' + resp.statusApi);
                            document.getElementById("failed_ul").style.display = "block";
                            $("#failed").html(resp.statusApi);
                        }
                    });
                } else {
                    console.log('submitVariableBtn failed empty var ' + resp.statusApi);
                    document.getElementById("failed_ul").style.display = "block";
                    $("#failed").html('Failed Submit Variable');
                }

                setTimeout(location.reload.bind(location), 3000);
            };
        } else {
            console.log('variableBtn failed else ' + resp.statusApi);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.statusApi);
        }

    });
}