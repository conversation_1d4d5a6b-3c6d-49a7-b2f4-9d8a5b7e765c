<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use SSH;
use App\Migrate\MigrateUtils;
use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\SSHService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\EpSupportTaskService;
use Guzzle;
use GuzzleHttp\Client;
use DB;
use GuzzleHttp\Exception\GuzzleException;

/**
 * Program to re-trigger files form IGFMAS to re-process.
 */
class CheckAp511ProcessIsUpdated {
    use OSBService;
    use SSHService;
    use FulfilmentService;
    use OSBWebService;
    use EpSupportTaskService;



    public static function runAndCheckAp511($limit) {
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        $cls = new CheckAp511ProcessIsUpdated;
        $listA511 = self::getListAP511FilesNotUpdatedInInterfaceLog();
        $totFiles = count($listA511);
        MigrateUtils::logDump('Total Files : '.$totFiles);
        MigrateUtils::logDump('Total to check : '.$limit);
        
        $counter = 0;
        foreach(collect($listA511)->take($limit) as $obj){
            $listInvoice = $cls->findDetailsInvoicesByAp511($obj->file_name);
            dump('File Name : '.$obj->file_name. ', Total Invoices : '.count($listInvoice));
            $isAp511Completed = true;
            if($listInvoice && count($listInvoice) > 0){
                foreach($listInvoice as $invObj){
                    if(strlen($invObj->invoice_no) > 15){
                        $statusObj = $cls->getLatestStatusPOCOByInvoiceNo($invObj->invoice_no);
                        //dump($statusObj->doc_no . ' , '.$statusObj->status_name);
                        if( $statusObj == null || $statusObj->status_name != 'Closed'){
                            $isAp511Completed = false;
                        }
                    }
                }
            }
            if($isAp511Completed == true){
                $counter++;
                MigrateUtils::logDump("$counter/$totFiles This file AP511 : ".$obj->file_name.' >> can set as updated di_interface_log');
                $interfaceLogObj= DB::connection('oracle_nextgen_fullgrant')->table('di_interface_log')
                                    ->where('service_code','GFM-140')->whereNull('file_name')->select('interface_log_id')->first();

                if($interfaceLogObj){
                    DB::connection('oracle_nextgen_fullgrant')
                        ->table('di_interface_log')
                        ->where('interface_log_id',$interfaceLogObj->interface_log_id)
                        ->update(
                            [
                                'file_name'=>$obj->file_name,
                                'changed_by'=>1,
                                'changed_date'=> $obj->created_date]
                        );
                    MigrateUtils::logDump("  >> DONE Updated");

                    $objRes = DB::connection('oracle_nextgen_fullgrant')
                    ->table('di_interface_log')
                    ->where('interface_log_id',$interfaceLogObj->interface_log_id)->first();
                    dump($objRes);
                }else{
                    MigrateUtils::logDump("##### Trans ID not found! Kindly create more records GFM-140 with trans ID.");
                }                    
                
              

            }else{
                MigrateUtils::logDump("############################## This file AP511 : ".$obj->file_name.' >>poco docno not closed!');
            }
            //dd('die');
        }
        
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    protected static function getListAP511FilesNotUpdatedInInterfaceLog() {

        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT tmp.*
                FROM (
                    SELECT to_char(created_date,'YYYY-MM') AS year_month, service_code,
                    (SELECT s.service_name FROM osb_service s WHERE s.service_code = obf.service_code ) AS service_name,
                    file_name,
                    CREATED_DATE ,
                    (SELECT min(changed_date) FROM DI_INTERFACE_LOG dil WHERE dil.service_code = obf.service_code AND dil.FILE_NAME = obf.FILE_NAME)  AS process_date
                    FROM OSB_BATCH_FILE obf WHERE service_code IN ('GFM-140')
                    AND  trunc(created_date) between to_date('2021-11-01','YYYY-MM-DD') AND to_date('2021-11-30','YYYY-MM-DD')
                )  tmp WHERE process_date IS null");

        return $query;
    }
}
