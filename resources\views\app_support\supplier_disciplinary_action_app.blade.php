@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Supplier Disciplinary Action</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="list_disciplinary_datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">No</th>
                    <th class="text-center">Tarikh Mula TT</th>
                    <th class="text-center">Tarikh Tamat TT</th>
                    <th class="text-center"><PERSON><PERSON><PERSON></th>
                    <th class="text-center">Nama <PERSON>yarikat</th>
                    <th class="text-center">Nombor Pendaftaran</th>
                    <th class="text-center">Nombor eP</th>
                    <th class="text-center">Nombor MOF</th>
                    <th class="text-center">Status Syarikat Te<PERSON>i</th>
                    <th class="text-center">Kontrak Berkuatkuasa</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $rowData => $user)
                <tr>
                    <td class="text-center">{{ ++$rowData }}</td>
                    <td class="text-center">{{ $user->tarikh_mula_tt }}</td>
                    <td class="text-center">{{ $user->tarikh_tamat_tt }}</td>
                    <td class="text-center">{{ $user->hukuman }}</td>
                    <td class="text-center">{{$user->nama_syarikat}}</td>
                    <td class="text-center">{{$user->nombor_pendaftaran}}</td>
                    <td class="text-center"><a href="{{url("/find/epno")}}/{{ $user->nombor_ep }}" target='_blank'>{{ $user->nombor_ep }}</td>
                    <td class="text-center"><a href="{{url('/find/mofno/')}}/{{ $user->nombor_mof }}" target="_blank">{{ $user->nombor_mof }}</a></td>
                    <td class="text-center">{{$user->status_syarikat_terkini}}</td>
                    <td class="text-center">{{$user->total_count}}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
$(function() {
    TablesDatatables.init();
});
App.datatables();
$('#list_disciplinary_datatable').dataTable({
    order: [
        [0, "asc"]
    ],
    columnDefs: [],
    pageLength: 10,
    lengthMenu: [
        [10, 20, 30, 50, -1],
        [10, 20, 30, 50, 'All']
    ]
});
</script>
@endsection



