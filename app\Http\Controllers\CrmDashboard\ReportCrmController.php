<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\CrmDashboard;

use App\Http\Controllers\Controller;
use Log;
use Carbon\Carbon;
use DB;
use Illuminate\Http\Request;

class ReportCrmController {

    public function reportCptpp() {

        $data = self::getCptppData();
        return view('crm.report_cptpp', [
            'data' => $data
        ]);
    }

    public function reportPerubahanData(Request $request) {
        $dateStart = null;
        $dateEnd = null;
        $data = null;
        $dateQueryStart = null;
        $dateQueryEnd = null;
        
        if (request()->isMethod('post')) {
            $dateStart = $request->dateStart;
            $dateEnd = $request->dateEnd;

            $startOfMonth = Carbon::now()->startOfMonth()->format('Y-m-d');
            $yesterday = Carbon::yesterday()->format('Y-m-d');
            $today = Carbon::now()->format('Y-m-d');

            $dateQueryStart = $startOfMonth;
            $dateQueryEnd = $yesterday;

            if ($dateStart !== null && $dateEnd !== null) {
                $dateQueryStart = Carbon::parse($dateStart)->format('Y-m-d');
                $dateQueryEnd = Carbon::parse($dateEnd)->format('Y-m-d');
            } else {
                if ($startOfMonth === $today) {
                    $dateQueryStart = $today;
                    $dateQueryEnd = $today;
                }
            }

            $data = self::getPerubahanData($dateQueryStart, $dateQueryEnd);
        }

        return view('crm.report_perubahandata', [
            'data' => $data,
            'dateStart' => $dateQueryStart,
            'dateEnd' => $dateQueryEnd,
        ]);
    }

    public function s4CrmCaseMonitoring(Request $request) {
      $list = DB::connection('mysql_crm')
        ->select("SELECT
        distinct case_number,
        redmine_number,
        name,
        description,
        status
      from
        cases
      where
        -- redmine_number in ('34796','34815','34799')
         redmine_number not in ('', '#Redmine')
        and cases.state = 'Open'
        and cases.status <> 'Open_Resolved'
        and redmine_number not in (
        SELECT
          redmine_number
        FROM
          cases
        WHERE
          redmine_number not in ('', '#Redmine')
            and
          created_by = '8e1aee12-eb19-4209-b9c2-c327d1cc436f')
      order by
        2,
        1");

      return view('crm.s4_crm_case_monitoring', [
          'data' => $list,
      ]);
  }
  
    public function getCptppData() {
        $query = DB::connection('mysql_crm')->select(
                "SELECT
                    c.case_number AS caseNo,
                    c.redmine_number AS redmine,   
                    CASE
                      WHEN lc.leads_cases_1leads_ida IS NULL
                      THEN acctype.value_name
                      ELSE 'Leads'
                    END AS acctTypeName, 
                    ct.value_name AS catName, 
                    sc.value_name AS subCatName,  
                    c.description AS problem, 
                    CONVERT_TZ(
                      c.date_entered,
                      '+00:00',
                      '+08:00'
                    ) AS dateCreated,  
                    sts.value_name AS statusName,
                    cptpp_flag as cptppFlag
                  FROM
                    cases c -- account as kumpulan ptj
                    LEFT JOIN accounts a
                      ON (
                        a.`id` = c.`account_id`

                      ) 
                    LEFT JOIN leads_cases_1_c lc
                      ON (c.id = lc.leads_cases_1cases_idb)
                    LEFT JOIN leads l
                      ON (lc.leads_cases_1leads_ida = l.id)
                    LEFT JOIN leads_cstm lcc
                      ON (l.id = lcc.id_c)
                    LEFT JOIN cstm_list_app acctype
                      ON (
                        a.account_type = acctype.value_code
                        AND TRIM(acctype.value_code) <> ''
                        AND acctype.type_code = 'cdc_account_type'
                      ) 
                    JOIN cases_cstm cc
                      ON (c.id = cc.id_c) 
                    LEFT JOIN cstm_list_app ct
                      ON (
                        cc.category_c = ct.value_code
                        AND TRIM(ct.value_code) <> ''
                        AND ct.type_code = 'category_list'
                      )
                    LEFT JOIN cstm_list_app sc
                      ON (
                        cc.sub_category_c = sc.value_code
                        AND TRIM(sc.value_code) <> ''
                        AND sc.type_code = 'cdc_sub_category_list'
                      )

                    LEFT JOIN cstm_list_app sts
                      ON (
                        c.status = sts.value_code
                        AND TRIM(sts.value_code) <> ''
                        AND sts.type_code = 'case_status_dom'
                      ) 
                  WHERE  c.deleted = 0 AND cptpp_flag = 1 
                  ORDER BY 1,2,3,4,5,6,7,8");
        return $query;
    }

    public function getPerubahanData($dateStart, $dateEnd) {
        $query = DB::connection('mysql_crm')->select(
                "SELECT c.id, case_number,redmine_number, e2.name as kementerian_name,b2.name as jabatan_name,
                    a.name as ptj_name,a.org_gov_code as ptj_code, cstma.value_name AS org_type,a.name as account_name,
                    a.mof_no,l.first_name as lead_name,lcc.ssm_no_c,
                    CASE
                        WHEN lc.leads_cases_1leads_ida IS NULL
                        THEN acctype.value_name
                        ELSE 'Leads'
                      END AS acc_type_name,rt.value_name as req_type,inctype.value_name as inc_type,portal.value_name as portal_cat,
                      ct.value_name as cat_name,sc.value_name as subcat_name,sc2.value_name as subcat2_name,
                      c.name AS csubject,c.description AS problem,c.doc_no,c.resolution,pr.value_name as priority,
                      CONVERT_TZ(c.date_entered,'+00:00','+08:00') AS date_created,ctc.first_name as ctc_name,
                      u.first_name as owner_name,u.id as user_id,assignedTo.user_name as assigned_name,st.value_name as state_name,
                      sts.value_name as status_name,caseinfo.value_name as case_info,CONVERT_TZ(c.date_modified,'+00:00','+08:00') AS date_modified,
                      modifieduser.user_name as modified_by,cm.value_name as ctcmode_name,c.status as status_code,email.email_address AS email,
                      CASE
                        WHEN (a.billing_address_state IS NULL)
                        THEN stateL.value_name
                        WHEN (l.primary_address_state IS NULL)
                        THEN stateA.value_name
                        ELSE NULL
                      END AS addr_state,CASE WHEN c.cptpp_flag = '0' THEN 'No' ELSE 'Yes' END AS cptpp,
                      (SELECT value_name FROM tasks ts LEFT JOIN cstm_list_app a ON a.value_code = ts.data_change_status 
                        WHERE a.type_code = 'data_change_status_list' AND ts.parent_id = c.id AND ts.data_change_status IS NOT NULL ORDER BY ts.date_entered DESC LIMIT 1) data_approve,
                      (SELECT CONVERT_TZ(t.date_entered,'+00:00','+08:00') FROM tasks t WHERE t.parent_id = c.id
                        AND t.name = 'Assigned to Group ePP1' ORDER BY t.date_entered ASC LIMIT 1) task_assigned_to_epp1,
                      (SELECT CONVERT_TZ(t.date_modified,'+00:00','+08:00') FROM tasks t WHERE t.parent_id = c.id
                        AND t.name = 'Assigned to Group ePP1' AND t.status = 'Completed' ORDER BY t.date_entered DESC LIMIT 1) task_closed_by_epp1,
                      (SELECT CONVERT_TZ(t.date_entered,'+00:00','+08:00') FROM tasks t WHERE t.parent_id = c.id
                        AND (t.name = 'Assigned to Group ePA1' OR t.name = 'Assigned to Group ePA2' OR t.name = 'Assigned to Group ePA3') ORDER BY t.date_entered DESC LIMIT 1) task_epp1_to_epa,
                      (SELECT t.name FROM tasks t WHERE t.parent_id = c.id
			                  AND (t.name = 'Assigned to Group ePA1' OR t.name = 'Assigned to Group ePA2' OR t.name = 'Assigned to Group ePA3') ORDER BY t.date_entered DESC LIMIT 1) task_epa,
                      (SELECT CONVERT_TZ(t.date_modified,'+00:00','+08:00') FROM tasks t WHERE t.parent_id = c.id
                        AND (t.name = 'Assigned to Group ePA1' OR t.name = 'Assigned to Group ePA2' OR t.name = 'Assigned to Group ePA3') AND t.status = 'Completed' ORDER BY t.date_entered DESC LIMIT 1) task_closed_by_epa,
                      (SELECT CONVERT_TZ(t.date_modified,'+00:00','+08:00') FROM tasks t WHERE t.parent_id = c.id
			                  AND t.name = 'Assigned to Group IT Specialist(Production Support)' AND t.status = 'Completed' ORDER BY t.date_entered DESC LIMIT 1) task_closed_by_support
                    FROM cases c JOIN cases_cstm cc ON cc.id_c = c.id
                    LEFT JOIN accounts a ON (a.`id` = c.`account_id`)
                    LEFT JOIN accounts b ON (b.`id` = a.`parent_id`)
                    LEFT JOIN accounts d ON (d.`id` = b.`parent_id`)
                    LEFT JOIN accounts e ON (e.`parent_id` = a.`id`) -- account as kementerian
                    LEFT JOIN accounts b1 ON (b1.id = a.parent_id)
                    LEFT JOIN accounts d1 ON (d1.`id` = b1.`parent_id`) -- account as ptj
                    LEFT JOIN accounts b2 ON (b2.`id` = a.`parent_id`)
                    LEFT JOIN accounts d2 ON (d2.`id` = b2.`parent_id`)
                    LEFT JOIN accounts e2 ON (e2.id = d2.parent_id)
                    LEFT JOIN cstm_list_app cstma ON (cstma.`value_code` = a.org_gov_type AND cstma.`type_code` = 'accounts_nextgen_org_gov_code')
                    LEFT JOIN leads_cases_1_c lc ON (c.id = lc.leads_cases_1cases_idb)
                    LEFT JOIN leads l ON (lc.leads_cases_1leads_ida = l.id)
                    LEFT JOIN leads_cstm lcc ON (l.id = lcc.id_c)
                    LEFT JOIN cstm_list_app acctype ON (a.account_type = acctype.value_code AND TRIM(acctype.value_code) <> '' AND acctype.type_code = 'cdc_account_type')
                    LEFT JOIN cstm_list_app rt ON (cc.request_type_c = rt.value_code AND TRIM(rt.value_code) <> '' AND rt.type_code = 'request_type_list')
                    LEFT JOIN cstm_list_app inctype ON ( cc.incident_service_type_c = inctype.value_code AND TRIM(inctype.value_code) <> '' AND inctype.type_code = 'incident_service_type_list')               
                    LEFT JOIN cstm_list_app portal ON (c.portal_category = portal.value_code AND TRIM(portal.value_code) <> '' AND portal.type_code = 'portal_category_list') 
                    LEFT JOIN cstm_list_app ct ON (cc.category_c = ct.value_code AND TRIM(ct.value_code) <> '' AND ct.type_code = 'category_list')
                    LEFT JOIN cstm_list_app sc ON (cc.sub_category_c = sc.value_code AND TRIM(sc.value_code) <> '' AND sc.type_code = 'cdc_sub_category_list')
                    LEFT JOIN cstm_list_app sc2 ON (cc.sub_category_2_c = sc2.value_code AND TRIM(sc2.value_code) <> '' AND sc2.type_code = 'cdc_sub_category_2_list')
                    LEFT JOIN cstm_list_app pr ON (c.priority = pr.value_code AND TRIM(pr.value_code) <> '' AND pr.type_code = 'case_priority_dom')
                    LEFT JOIN contacts ctc ON (cc.contact_id_c = ctc.id)
                    LEFT JOIN users u ON (c.created_by = u.id)
                    LEFT JOIN users assignedTo ON (c.assigned_user_id = assignedTo.id)
                    LEFT JOIN cstm_list_app st ON (c.state = st.value_code AND TRIM(st.value_code) <> '' AND st.type_code = 'case_state_dom')
                    LEFT JOIN cstm_list_app sts ON (c.status = sts.value_code AND TRIM(sts.value_code) <> '' AND sts.type_code = 'case_status_dom')
                    LEFT JOIN cstm_list_app caseinfo ON (cc.case_info_c = caseinfo.value_code AND TRIM(caseinfo.value_code) <> '' AND caseinfo.type_code = 'case_info_list')
                    LEFT JOIN users modifieduser ON (c.modified_user_id = modifieduser.id)
                    LEFT JOIN cstm_list_app cm ON (cc.contact_mode_c = cm.value_code AND TRIM(cm.value_code) <> '' AND cm.type_code = 'cdc_contact_mode_list')
                    LEFT JOIN email_addr_bean_rel emailbean ON (ctc.id = emailbean.bean_id AND bean_module = 'Contacts' AND emailbean.deleted = 0)
                    LEFT JOIN email_addresses email ON (emailbean.email_address_id = email.id)
                    LEFT JOIN cstm_list_app stateA ON (a.billing_address_state = stateA.value_code AND TRIM(stateA.value_code) <> '' AND stateA.type_code = 'state_list')
                    LEFT JOIN cstm_list_app stateL ON (l.primary_address_state = stateL.value_code AND TRIM(stateL.value_code) <> '' AND stateL.type_code = 'state_list')
                    WHERE c.deleted = 0
                    AND (c.portal_category IN ('EA055','EA056','EA057') || cc.sub_category_c IN ('10713_15541','10713_15542','10713_15543'))
                    AND c.status NOT IN ('Closed_Cancelled_Eaduan','Open_Pending Input')
                    AND DATE(CONVERT_TZ(c.date_entered, '+00:00', '+08:00'))
                    BETWEEN STR_TO_DATE('$dateStart','%Y-%m-%d') AND STR_TO_DATE('$dateEnd','%Y-%m-%d')");
        return $query;
    }

    public function pemantauanCptppQT() {

      $data = self::getPemantauanCptppQTData();
      return view('crm.pemantauan_cptpp_qt', [
          'data' => $data
      ]);
  }

    public function getPemantauanCptppQTData() {
      $query = DB::connection('oracle_nextgen_rpt')->select(
              "SELECT  DECODE (sq.procurement_mode_id,
              185, 'Quotation',
              186, 'Tender'
             ) AS Jenis_Perolehan,
      MAX (w.org_code) AS Kod_Kementerian, MAX (w.org_name) AS Kementerian,
      MAX (e.org_code) AS Kod_Kumpulan_PTJ, MAX (e.org_name) AS Kumpulan_PTJ,
      MAX (d.org_code) AS Kod_PTJ, MAX (d.org_name) AS PTJ,  sq.qt_no AS Nombor_QT, sq.qt_title AS Tajuk_QT,
      pm.status_name AS Status_1, sq.publish_date AS Publish_Date, sq.closing_date AS Closing_Date
 FROM sc_qt sq,
      sc_workflow_status s,
      pm_status_desc pm,
      pm_parameter mtr,
      pm_org_validity d,
      pm_org_profile pr,
      pm_org_validity e,                                              --jab
      pm_org_profile pr2,
      pm_org_validity y,
      pm_org_profile m,
      pm_org_validity w,
      pm_org_profile mi
WHERE w.org_profile_id = mi.org_profile_id
  AND d.org_profile_id = pr.org_profile_id
  AND e.org_profile_id = pr.parent_org_profile_id
  AND pr.org_type_id = mtr.parameter_id
  AND pr2.org_profile_id = pr.parent_org_profile_id
  AND m.org_profile_id = pr2.parent_org_profile_id
  AND y.org_profile_id = m.org_profile_id
  AND mi.org_profile_id = m.parent_org_profile_id
  AND d.record_status = 1
  AND w.record_status = 1
  AND sq.qt_id = s.doc_id
  AND sq.org_profile_id = d.org_profile_id
  AND pm.status_id = s.status_id
  AND sq.procurement_mode_id IN ('185', '186')
  AND pm.language_code = 'en'
  AND s.is_current = 1
  --AND pm.status_id IN (60014)
  /*AND pm.status_id NOT IN
         (60015, 60041, 60858, 60854, 60007, 60026, 60043, 60042, 60040,
          60033, 60034, 60031, 60030, 60028, 60027, 60025, 60024, 60047,
          60046)*/ --- uncomment to exclude qt cancel 
  AND s.doc_type = 'QT'
  AND sq.qt_no LIKE ('QT%')
  --AND sq.created_date BETWEEN TO_DATE ('01/01/2023', 'dd/mm/yyyy') AND TO_DATE ('31/12/2023', 'dd/mm/yyyy')
  AND UPPER (sq.qt_title) LIKE '%FTA%CPTPP%'
GROUP BY sq.qt_title,
      sq.created_date,
      sq.qt_no,
      sq.procurement_mode_id,
      sq.procurement_type_cat_id,
      sq.closing_date,
      pm.status_name,
      sq.publish_date, 
      sq.closing_date
ORDER BY sq.qt_no");
      return $query;
  }

    public static function getListDetailGroupCRM($userId) {
        $query = DB::table('securitygroups');
        $query->join('securitygroups_users', 'securitygroups.id', '=', 'securitygroups_users.securitygroup_id');
        $query->where('securitygroups_users.user_id', $userId);
        $query->where('securitygroups_users.deleted', 0);
        $query->select('securitygroups.name as groupname');
        $data = $query->get();
        return $data;
    }

    public static function getDetailTaskLatestCRM($caseId) {
        $query = DB::table('tasks');
        $query->join('tasks_cstm', 'tasks.id', '=', 'tasks_cstm.id_c');
        $query->where('tasks.parent_id', $caseId);
        $query->where('tasks.parent_type', 'Cases');
        $query->where('tasks.deleted', 0);
        $query->select('tasks.*', 'tasks_cstm.*', 'tasks.resolution_category_c as resolution_category');
        $query->orderBy('tasks_cstm.task_number_c', 'desc');
        $data = $query->first();
        return $data;
    }

}
