<?php

namespace App\Services\Traits;

use DB;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use SSH;
use Log;

/**
 * Description of DB Monitoring Purge Table
 *
 * <AUTHOR>
 */
trait DBMonitoringService {

    /**
     * show total total of new instance, backlog and total purged
     * @return list
     */    
    // About Composite
    protected function getStatisticCompositeInstanceByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(created_date) as dateCreated, sum(PURGE_COMPOSITE_INSTANCE) as purgecompinst
                 from purge_report 
                 where trunc(created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 group by trunc(created_date)
                 order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About Composite
    protected function getStatisticBackLogCompositeByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.BACKLOG_COMPOSITE_INSTANCE as bcklogcompinst
                 from backlog_report br 
                 join purge_report nr
                 on trunc(br.created_date) = trunc(nr.created_date) 
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About Composite
    protected function getStatisticNewInstCompositeByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.NEW_COMPOSITE_INSTANCE as newcompinst
                from new_instance_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date) 
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                  and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                  order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About Cube Instance
    protected function getStatisticCubeInstanceByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.NEW_CUBE_INSTANCE as newcubeinst
                from new_instance_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About Cube Instance
    protected function getStatisticBackLogCubeInstanceByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.BACKLOG_PURGE_CUBE_INSTANCE as bcklogcubeinst
                    from backlog_report br 
                    join purge_report nr
                    on trunc(br.created_date) = trunc(nr.created_date) 
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About Cube Instance
    protected function getStatisticPurgedCubeInstanceByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(created_date) as dateCreated, sum(PURGE_CUBE_INSTANCE) as purgecubepinst
                    from purge_report 
                 where trunc(created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                     group by trunc(created_date)
                 order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About WFTask
    protected function getStatisticWFTaskByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.NEW_WFTASK as newwft
                from new_instance_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About WFTask
    protected function getStatisticBackLogWFTaskByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.BACKLOG_PURGE_WFTASK as bcklogwft
                from backlog_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About WFTask
    protected function getStatisticPurgedWFTaskByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(created_date) as dateCreated, sum(PURGE_WFTASK) as purgewft
                    from purge_report 
                 where trunc(created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                     group by trunc(created_date)
                 order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    
     // About SCAFlowInst
    protected function getStatisticSCAFlowInstByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.NEW_SCA_FLOW_INSTANCE as newsca
                from new_instance_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About SCAFlowInst
    protected function getStatisticBackLogSCAFlowInstByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.BACKLOG_SCA_FLOW_INSTANCE as bcklogsca
                from backlog_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About SCAFlowInst
    protected function getStatisticPurgedSCAFlowInstByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(created_date) as dateCreated, sum(PURGE_SCA_FLOW_INSTANCE) as purgesca
                    from purge_report 
                 where trunc(created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                     group by trunc(created_date)
                 order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    
     // About DLV Message
    protected function getStatisticDLVMsgByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.NEW_DLV_MESSAGE as newdlv
                from new_instance_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About DLV Message
    protected function getStatisticBackLogDLVMsgByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select distinct trunc(br.created_date) as dateCreated, br.BACKLOG_PURGE_DLV_MESSAGE as bcklogdlv
                from backlog_report br 
                join purge_report nr
                on trunc(br.created_date) = trunc(nr.created_date)
                 where trunc(br.created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(br.created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                 order by trunc(br.created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    // About DLV Message
    protected function getStatisticPurgedDLVMsgByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(created_date) as dateCreated, sum(PURGE_DLV_MESSAGE) as purgedlv
                    from purge_report 
                 where trunc(created_date) >= TO_DATE('$StartDate', 'YYYY-MM-DD')
                 and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD')
                     group by trunc(created_date)
                 order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);

        return $results;
    }
    
    protected function getStatisticTableGrowthByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();

        $query = "select trunc(CREATED_DATE) as datecreated , BAQ_ROWS as baqrows, COMPOSITE_ROWS as comprows, CUBE_INSTANCE_ROWS as cubeinstrows, CUBE_SCOPE_ROWS as cubescoperows, WFTASK_ROWS as wftaskrows, BPM_CUBE_AUDITINSTANCE_ROWS as bpmcubeaudrows, SCA_FLOW_INSTANCE_ROWS as scaflowrows, DLV_MESSAGE_ROWS as dlvmsgrows
                  from new_instance_report 
                  where trunc(created_date) >=  TO_DATE('$StartDate', 'YYYY-MM-DD') 
                  and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD') order by trunc(created_date) asc";

        $results = DB::connection('oracle_ngepsoa_read')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);
        return $results;
    }
    
    // app table - monitoring data growth - added 27102021
    protected function getStatisticAppDataGrowthByDate($StartDate, $EndDate) {

        $dtStartTime = Carbon::now();
        
        $query = "select trunc(CREATED_DATE) as datecreated , SC_QT_ENCRYPT_DATA_ROWS as scqtenrows, SC_QT_SUPPLIER_ROWS as scqtsupprows, TPL_SPEC_ANSWER_ROWS as tplspecrows, OSB_LOGGING_ROWS as osblogrows, OSB_LOGGING_DTL_ROWS osblogdtlrows
                  from DATA_GROWTH_REPORT
                  where trunc(created_date) >=  TO_DATE('$StartDate', 'YYYY-MM-DD') 
                  and trunc(created_date) <= TO_DATE('$EndDate', 'YYYY-MM-DD') order by trunc(created_date) asc";

        $results = DB::connection('oracle_nextgen_rpt')->select($query);
        $logsdata = self::class . ' Query Date Start : ' . $StartDate . ' , Query Date End : ' . $EndDate . ' , Completed --- ' .
                ' , Total: ' . count($results) . ' Taken Time : '
                . json_encode(['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        Log::info($logsdata);
        return $results;
    }
        
}
