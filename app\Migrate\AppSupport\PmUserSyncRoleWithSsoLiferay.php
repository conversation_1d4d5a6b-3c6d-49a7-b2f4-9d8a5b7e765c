<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\EpWebService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class PmUserSyncRoleWithSsoLiferay {
    use EpWebService;
    public static function fixIssueToSyncRoleEpWithSsoLiferay(){
        MigrateUtils::logDump(__METHOD__. ' Entering... ');
        $dtStartTime = Carbon::now();
        $thisClass = new PmUserSyncRoleWithSsoLiferay;
        /**  
        $listOrg =   DB::connection('oracle_nextgen_rpt')
                        ->select('SELECT * FROM (
                            SELECT tmp.* , (total_role_ep-total_role_liferay) AS diff_role 
                            FROM (
                                SELECT u.user_id,u.LOGIN_ID ,u.USER_NAME ,u.EMAIL ,
                                u.identification_no,u.org_type_id,uo.org_profile_id,
                                (SELECT max(login_date) FROM pm_login_history plh WHERE plh.user_id =u.user_id ) last_login_date,
                                count(ur.ROLE_CODE) AS total_role_ep  ,
                                ( 
                                SELECT count(*)
                                FROM User_@EPLFY u 
                                INNER JOIN UserGroupRole@EPLFY ON u.userId = UserGroupRole.userId 
                                INNER JOIN Role_@EPLFY r ON UserGroupRole.roleId = r.roleId 
                                WHERE u.screenName = u.login_id
                                ) AS total_role_liferay
                                
                                FROM pm_user u, pm_user_org uo, pm_user_role ur 
                                WHERE u.USER_ID  = uo.USER_ID  
                                AND uo.USER_ORG_ID  = ur.USER_ORG_ID  
                                AND u.RECORD_STATUS = 1 
                                AND uo.RECORD_STATUS = 1
                                AND ur.RECORD_STATUS = 1
                                AND u.ORG_TYPE_ID <> 15
                                GROUP BY  u.user_id,u.LOGIN_ID ,u.USER_NAME ,u.EMAIL,u.identification_no,u.org_type_id,uo.org_profile_id 
                            ) tmp
                            
                            ) tmp2 
                            WHERE tmp2.diff_role <> 0');
        
        **/

        $listOrg = [
            '551208,670127015393,5',
'687600,720522025789,5',
'892898,910616145324,5',
'590174,760120125628,5',
'533933,781115125394,5',
'896282,961018045200,5',
'836624,950402136605,5',
'551487,801107025954,5',
'653395,790831145227,5',
'798715,910112135499,5',
'629061,hafizsulaiman86,5',
'633613,861008295523,5',
'476018,781214016329,5',
'528345,810122105017,5',
'922027,670604086777,5',
'884504,901127125109,5',
'536567,790521125634,5',
'93395,861229155014, 5',
'809848,790506145303,5',
'820570,850507015801,5',
'531412,860201025387,5',
'574307,901203035974,5',
'807098,780219065950,5',
'528103,800724035504,5',
'729113,921001025776,5',
'507327,670810135613,5',
'801457,930819015974,5',
'785831,861016305226,5',
'846707,831215065336,5',
'889576,000207030793,5',
'530141,800630055408,5',
'852517,650207086396,5',
'864986,750501136487,5',
'551176,760422125690,5',
'540791,640103037005,5',
'846737,920802036331,5',
'21105,741126125094, 5',
'531400,800206025306,5',
'588728,870629385358,5',
'562487,800531115233,5',
'135590,357-02144308-5025',
'535799,810120125401,5',
'536550,831115015411,5',
'467284,780408135559,5',
'526819,621109045555,5',
'836832,920726025613,5',
'900369,890819045299,5',
'533013,800106085635,5',
        ];

        MigrateUtils::logDump(__METHOD__. ' Total '.count($listOrg ));
        foreach ($listOrg as $user){
            $obj = explode( ',', $user );
            $userId = $obj[0];
            $loginId = $obj[1];
            $orgTypeId = $obj[2];
            dump($user);
            $result = $thisClass->wsSyncRoleUserEp($userId, $loginId, $orgTypeId);
            MigrateUtils::logDump(__METHOD__. ' '.json_encode($result));
            
        }

        MigrateUtils::logDump(__METHOD__.  ' Completed--- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

}
