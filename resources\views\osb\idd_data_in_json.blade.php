<div class="block-title">
    <h2><i class="fa fa-file-text-o"></i> <strong>List File Data in JSON Format </strong> >> {{$objFile->file_name}}</h2>
    <div class="block-options pull-right">
        <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
            onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
    </div>
</div>
<div class="row" >
    <div class="col-sm-12">
        @if($listDataJson != null && count($listDataJson) > 0)
        <pre style="color:white;height:600px"><code class="language-json">{{ json_encode($listDataJson, JSON_PRETTY_PRINT) }}</code></pre>
        @endif
    </div>
</div>