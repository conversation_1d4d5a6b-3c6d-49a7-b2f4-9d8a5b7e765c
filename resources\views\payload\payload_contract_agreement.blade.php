@extends('layouts.guest-dash')

@section('header')
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }

    .table tbody > tr > td {
        font-size: 10px;
    }
</style>
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Recreate Contract Agreement<br>
            <small>Masukkan <span class="text-info">CT No.</span> pada carian berikut</small>
        </h1>
    </div>
    <div class="block">
        <form id="form-search" action="{{url("/find/contract/agreement")}}/" method="get" class="form-horizontal" onsubmit="return true;">
            <div class="form-group">
                <label class="col-md-3 control-label" for="doc_no">CT No. <span class="text-danger">*</span></label>
                <div class="col-md-5">
                    <input id="doc_no" name="doc_no" class="form-control" placeholder="CT No.." type="text" required
                           value="{{ old('doc_no') }}">
                </div>
            </div>
            <div class="form-group form-actions">
                <div class="col-md-9 col-md-offset-3">
                    <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                    <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                </div>
            </div>
            @if($remarks !== null)
                <div class="alert alert-danger">
                    <ul>
                        <li>{{ $remarks }}</li>
                    </ul>
                </div>
            @endif
        </form>
    </div>
</div>

@if($agreementData != null && $minContractAdmin != null && $ctApprover != null) 
<div class="block">
    <h4>Payload</h4>
    <?php
                
$xml = '<CT_Agreement_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/CT_Agreement_Data" xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/CT_User_Data">
        <agreementId>'.$agreementData[0]->agreement_id.'</agreementId>
        <contractId>'.$agreementData[0]->contract_id.'</contractId>
        <title>'.$agreementData[0]->contract_name.'</title>
        <contractAdmin>
            <ns2:userId>'.$minContractAdmin[0]->user_id.'</ns2:userId>
            <ns2:userName>'.$minContractAdmin[0]->login_id.'</ns2:userName>
            <ns2:calendarName>PUTRAJAYA</ns2:calendarName>
            <ns2:userDisplayName>'.$minContractAdmin[0]->user_name.'</ns2:userDisplayName>
        </contractAdmin>'; 

    $xml = $xml .'
        <agreementType>agreement</agreementType>
        <approver>
            <ns2:userId>'.$ctApprover[0]->user_id.'</ns2:userId>
            <ns2:userName>'.$ctApprover[0]->login_id.'</ns2:userName>
            <ns2:calendarName>PUTRAJAYA</ns2:calendarName>
            <ns2:userDisplayName>'.$ctApprover[0]->user_name.'</ns2:userDisplayName>
        </approver>';
    

    $xml = $xml .'
        <approverList>
            <ns2:userId>'.$ctApprover[0]->user_id.'</ns2:userId>
            <ns2:userName>'.$ctApprover[0]->login_id.'</ns2:userName>
            <ns2:calendarName>PUTRAJAYA</ns2:calendarName>
            <ns2:userDisplayName>'.$ctApprover[0]->user_name.'</ns2:userDisplayName>
        </approverList>';
        
$xml = $xml .'
        <agreementType/>
        <approver/>
        <docId>'.$agreementData[0]->agreement_id.'</docId>
        <docNo>'.$agreementData[0]->contract_no.'</docNo>
        <docType>'.$agreementData[0]->doc_type.'</docType>
        <statusId/>
        <contractNo>'.$agreementData[0]->contract_no.'</contractNo>
        <loaNo>'.$agreementData[0]->loa_no.'</loaNo>
        <kpi_value/>
        <isCentral/>
    </CT_Agreement_Data>
';
            ?>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($xml) }}</code> 
            </pre>
</div>
@endif 

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
                TablesDatatables.init();
            });</script>
<script>
    $('#page-container').removeAttr('class');
</script>
@endsection



