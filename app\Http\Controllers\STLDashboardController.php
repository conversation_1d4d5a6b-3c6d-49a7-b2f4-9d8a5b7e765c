<?php

namespace App\Http\Controllers;

use Carbon\Carbon;
use DB;
use Auth;
use SSH;
use DateTime;
use Log;
use App\Services\Traits\GFMASService;
use App\Services\Traits\FulfilmentService;
use App\Migrate\MigrateUtils;
use Guzzle;
use Response;
use App\Services\Traits\SourcingService;
use App\Services\Traits\SupplierService;
use App\Services\Traits\BPMService;
use App\Services\Traits\InvoiceService;

class STLDashboardController extends Controller {

    use GFMASService;
    use BPMService;
    use FulfilmentService;
    use SourcingService;
    use SupplierService;
    use InvoiceService;
    
     public function __construct()
    {
        $this->middleware('auth');
    }

    public function getDashboardSTL(){
      
        return $this->getDashboardSTLDetail();
    }

    public function getDashboardSTLDetail() {
        return view('dashboard_stl', []);
    }
    
    public function checkSTLMonitoringSM(){
        //        $dtStartTime = Carbon::now();
        $listOne = count($this->getFailedTaskApplNoInitiate());
        $listTwo = count($this->getFailedApplNoTaskProcessingFeeAdv());
        $listThree = count($this->getFailedApplNoTaskToSDO());       
        $listFour = count($this->getFailedApplNoTaskToProcessingFee());  
        $listFive = count($this->getFailedApplNoTaskToPaymentRegistration());
        $listSix = count($this->getFailedApplNoTaskToPaymentRegistrationOther());
        $listSeven = count($this->getFailedApplNoTaskToPaymentRegistrationOther2());
        $listEight = count($this->getFailedApplNoTaskToProcessingFeeOther());
        $listNine = count($this->getStuckPaymentRegistrationIsPaid());
        $listTen = count($this->getStuckPendingReviewApplication());

        
        $TotalStuckSM = $listOne + $listTwo + $listThree + $listFour + $listFive + $listSix + $listSeven + $listEight + $listNine + $listTen;

        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/sm/stuck-task/' >
                        <strong>$TotalStuckSM</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";   
        return $html;
        
    }
    
     public function checkSTLMonitoringQT(){
        //        $dtStartTime = Carbon::now(); 
        $listStuckQT = $this->getListStuctTaskQt();
        $TotalStuckQT = count($listStuckQT);
//        $logsdata = self::class . ' Total Query for Stuck QT : '.$TotalStuckQT.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);  
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/qt/stuck/'>
                        <strong>$TotalStuckQT</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;
         
     }
     
      public function checkSTLMonitoringDPSQ(){
        //        $dtStartTime = Carbon::now(); 
        $listStuckDPSQ = $this->getFailedTaskSourcingDPSQCreateSimpleQuote();
        $listTrackSqClose = $this->getFailedTrackSQClose();
        $TotalStuckDPSQ = count($listStuckDPSQ)+count($listTrackSqClose);
//        $logsdata = self::class . ' Total Query for Stuck DP SQ : '.$TotalStuckDPSQ.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);    
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/dp/stuck-task/sq'>
                        <strong>$TotalStuckDPSQ</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;
         
     }
     
    public function checkSTLMonitoringDPRN(){
        //        $dtStartTime = Carbon::now(); 
        $listStuckDPRN = $this->getFailedTaskSourcingDPRNPendingApproval();
        $TotalStuckDPRN = count($listStuckDPRN);
//        $logsdata = self::class . ' Total Query for Stuck DP RN : '.$TotalStuckDPRN.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);     
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/dp/stuck-task/rn'>
                        <strong>$TotalStuckDPRN</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;
         
    }
     
    public function checkSTLMonitoringDPCodify(){
        //        $dtStartTime = Carbon::now(); 
        //$listStuckDPCodify = $this->getFailedTaskSourcingDPRNCodification();
        $list = $this->getFailedTaskSourcingDPRNCodification();
        $arrayFilterOut = array();
        foreach ($list as $obj){
            $task = $this->getTaskDetailBpmByDocNo($obj->doc_no); 
            if ($task != null) {
                //When Task BPM Show -->> PrepareLetter , Actually still in Normal Flow. 
                if(str_contains($task->taskdefinitionid,'PrepareLetter') == true){
                    array_push($arrayFilterOut,$obj->doc_no);
                }
            }
        }
        
        $listFilter = collect($list);
        $listFilterData = $listFilter->whereNotIn('doc_no', $arrayFilterOut);
        
        $TotalStuckDPCodify = $listFilterData->count();
     
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/dp/stuck-task/codification'>
                        <strong>$TotalStuckDPCodify</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;
         
    }

    public function checkSTLMonitoringRNTriggerOrder(){ 
        $listStuckRNTriggerOrder = $this->getFailedTaskRNTriggerOrder();
        $TotalStuckRNTriggerOrder = count($listStuckRNTriggerOrder);

        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/dp/stuck-task/rn-trigger-order'>
                        <strong>$TotalStuckRNTriggerOrder</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
        return $html;
         
    }

    public function checkSTLMonitoringPRCRInitiate(){
        //        $dtStartTime = Carbon::now(); 
        $listInitiatePRCR = $this->getFailedTaskPRCRInitiate();
        $TotalInitiatePRCR = count($listInitiatePRCR);
//        $logsdata = self::class . ' Total Query for Stuck FL Initiate PRCR : '.$TotalInitiatePRCR.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);      
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/fl/stuck-task/'>
                        <strong>$TotalInitiatePRCR</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;         

     }
     
       public function checkSTLMonitorinDO(){
        //        $dtStartTime = Carbon::now(); 
        $listDO = $this->getFailedListInitiateTaskDO();
        $TotalSTLDO = count($listDO);
//        $logsdata = self::class . ' Total Query for Stuck FL Initiate DO : '.$TotalSTLDO.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);        
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='/find/fl/stuck-task-initiate-do/'>
                        <strong>$TotalSTLDO</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;         

     }
     
      public function checkSTLMonitorinIntegrationPRCR(){
        //        $dtStartTime = Carbon::now(); 
        $listPRCR = $this->getFailedListIntegrationPRCRApprovalGFMASServiceCall();
        $TotalSTLStuckIntegrationPRCR = count($listPRCR);
//        $logsdata = self::class . ' Total Query for Stuck Integration PRCR : '.$TotalSTLStuckIntegrationPRCR.',  Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
//        Log::info($logsdata);        
        
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='find/fl/stuck-task/integration/?type=PRCR'>
                        <strong>$TotalSTLStuckIntegrationPRCR</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;    
         

     }
     
       public function checkSTLMonitorinIntegrationFRN(){
        //        $dtStartTime = Carbon::now(); 
        $listStuckIntegrationFRN = $this->getFailedListIntegrationFRNApprovalGFMASServiceCall();
        $listStuckFRNOnSubmittion= $this->getFailedListStuckFRNOnSubmittion();
        $totalStuckIntegrationFRN = count($listStuckIntegrationFRN)+count($listStuckFRNOnSubmittion);
  
        $html = "";
        
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                    <h1>
                    <a target='_blank' href='find/fl/stuck-task/integration/?type=FRN'>
                        <strong>$totalStuckIntegrationFRN</strong></a>
                    </h1>
                </div>";
        $html .= "</div>";
         return $html;    
         

     }
     
    public function checkSTLMonitorinIntegrationDAN(){
        $listDAN = $this->getFailedIntegrationDANApprovalGFMASServiceCall();
        return $this->populateTotalHTML(count($listDAN),'find/fl/stuck-task/integration/?type=DAN');
    }
     
    public function checkSTLMonitorinIntegrationSD(){
        $listSD = $this->getFailedListIntegrationSDApprovalGFMASServiceCall();
        return $this->populateTotalHTML(count($listSD),'find/fl/stuck-task/integration/?type=SD');
    }
     
    public function checkSTLMonitorinIntegrationPA(){
        $listPa2 = $this->getFailedIntegrationPaymentAdviceApproval();  
        $listPa = $this->getFailedIntegrationPaymentAdviceApprovalGFMASServiceCall();
        $totalSTLPA = count($listPa)+count($listPa2);
        return $this->populateTotalHTML($totalSTLPA,'find/fl/stuck-task/integration/?type=PA');
    }
     
    public function checkSTLMonitoringFLApproverPRCR(){
        $listApproverPRCR = $this->getFailedTaskPRCRPendingApproval();
        return $this->populateTotalHTML(count($listApproverPRCR),'/find/fl/stuck-task-pending-approval/');
    }
     
    public function checkSTLPendingInvoiceCreation() {
        $listPendingInvoice = $this->getStuckPendingInvoice();
        return $this->populateTotalHTML(count($listPendingInvoice),'/find/fl/stuck-task-pending-invoice/');
    }
     
    public function checkSTLPendingPaymentMatch() {
        $listPendingPaymentMatch = $this->getStuckPendingPaymentMatch();
        return $this->populateTotalHTML(count($listPendingPaymentMatch),'/find/fl/stuck-task-pending-payment-match/');
    }
     
    public function checkSTLPendingPrCrReview() {
        $listPrCrReview = $this->getListStuckTaskPendingPRCRReviewFromIgfmas();
        return $this->populateTotalHTML(count($listPrCrReview),'/find/gfmas/stuck-pending-prcr-review/');
    }

    public function checkSTLQueryPRCR() {
        $listStuck= $this->getFailedListStuckTaskQueryPRCR();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=QUERY-PRCR');
    }
    public function checkSTLAwaitingIgfmasPRCR() {
        $listStuck= $this->getFailedListStuckTaskAwaitingIgfmasPRCR();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=IGFMAS-PRCR');
    }
    public function checkSTLOrderPOCO() {
        $listStuck= $this->getFailedListStuckTaskOrderPOCO();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=ORDER-POCO');
    }
    public function checkSTLFLDebit() {
        $listStuck= $this->getFailedListStuckTaskFLDebit();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=FL-DEBIT');
    }
    public function checkSTLFrnAwaitingIgfmas() {
        $listStuck= $this->getFailedListStuckTaskFrnAwaitingIgfmas();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=FL-IGFMAS-FRN');
    }
    public function checkSTLPendingRevisionApprovalPRCR() {
        $listStuck= $this->getFailedListStuckTaskPendingRevisionApprovalPRCR();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/workflow?type=PEND-REVISION-APRV-PRCR');
    }
    public function checkSTLCancelPOCO() {
        $listStuck= $this->getFailedListStuckTaskCancelPOCO();
        return $this->populateTotalHTML(count($listStuck),'find/fl/stuck-task/CANCEL-POCO');
    }

    public function checkSTLMonitoringPRCREpp13Y(){
        $listPRCREpp13Y = $this->getPRStuckEpp13Y();
        return $this->populateTotalHTML(count($listPRCREpp13Y),'/find/fl/stuck-task-prcr-epp-013-y/');
    }

    public function checkSTLMonitoringPRCRMM501Del(){
        $listPRCRMM501Del = $this->getPRStuckMM501Del();
        return $this->populateTotalHTML(count($listPRCRMM501Del),'/find/fl/stuck-task-prcr-mm-501-del/');
    }

    public function checkSTLMonitoringFLPendingPaymentEpp017Y(){
        $listFLPendingPaymentEpp017Y = $this->getFLPendingPaymentEpp017Y();
        return $this->populateTotalHTML(count($listFLPendingPaymentEpp017Y),'/find/fl/stuck-task-fl-pending-payment-epp017-Y');
    }

    public function checkSTLMonitoringStuckYEPCF(){
        $listStuckYEPCF = $this->getStuckYEPCF();
        return $this->populateTotalHTML(count($listStuckYEPCF),'/find/fl/stuck-yep-cf');
    }

    public function checkDuplicateInvoice() {
        $listStuck= $this->getDuplicateInvoice();
        return $this->populateTotalHTML(count($listStuck),'find/fl/duplicate-invoice');
    }
    
    
    
    private function populateTotalHTML($total, $linkUrl){
        $html = "";
       
        $html .= "<div class='row text-center'>";
        $html .= "<div>
                   <h1>
                   <a target='_blank' href='$linkUrl'>
                       <strong>$total</strong></a>
                   </h1>
               </div>";
        $html .= "</div>";
        return $html; 
    }

}
