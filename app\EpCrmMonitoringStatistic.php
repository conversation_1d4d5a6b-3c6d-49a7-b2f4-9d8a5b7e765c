<?php

namespace App;


use DB;
use Auth;
use Illuminate\Database\Eloquent\Model;

/** This class mysql_ep_support re-pointing to mysql_ep_support table ep_action_log **/
class EpCrmMonitoringStatistic extends Model {
    protected $connection= 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_crm_monitoring";

    public static function createCrmMonitoring($userId,$fullName,$total,$category, $module, $docNo, $caseNo, $dateResolve, $remarks, $username = null){
        if($username == null){
           $username = Auth::user()->user_name ;
        }
        $actionLog  = new EpCrmMonitoringStatistic;
        $actionLog->user_id = $userId;
        $actionLog->full_name = $fullName;
        $actionLog->total = $total;
        $actionLog->category = $category;
        $actionLog->module = $module;
        $actionLog->doc_no = $docNo;
        $actionLog->case_no = $caseNo;
        $actionLog->date_resolve = $dateResolve;
        $actionLog->remarks = $remarks;
        $actionLog->created_by = $username;
        $actionLog->save();
        
        return $actionLog;
    }
    
    public static function getUserStatistic($userId){
        
        $results = DB::connection('mysql_ep_support')->select(
                "SELECT 
                SUM(CASE WHEN (YEAR(date_resolve)) = YEAR(NOW()) AND (DAY(date_resolve) = DAY(NOW())) AND (MONTH(date_resolve) = MONTH(NOW())) THEN total ELSE NULL END) AS totalresolvedaily,
                SUM(CASE WHEN  (YEAR(date_resolve)) = YEAR(NOW()) AND (DAY(date_resolve) = DAY(NOW() - INTERVAL 1 DAY)) AND (MONTH(date_resolve) = MONTH(NOW() - INTERVAL 1 DAY)) THEN total ELSE NULL END) AS totalresolveyesterday,
                SUM(CASE WHEN (YEAR(date_resolve)) = YEAR(NOW()) AND MONTH(date_resolve) = MONTH(NOW()) THEN total ELSE NULL END) AS totalresolvemonthly,
                SUM(CASE WHEN (YEAR(date_resolve)) = YEAR(NOW()) THEN total ELSE NULL END) AS totalresolveyearly,
                SUM(CASE WHEN (YEAR(date_resolve)) = YEAR(NOW() - INTERVAL 1 YEAR) THEN total ELSE NULL END) AS totalresolvelastyear,
                full_name FROM ep_crm_monitoring
                WHERE user_id = '$userId'
                GROUP BY full_name");
        return $results;
        
    }
    
    public static function getUserStatisticByModule($userId,$day){
        $whereDay = 'AND DATE(date_resolve) = DATE(NOW())';
        if($day == 'yesterday') {
            $whereDay = 'AND DATE(date_resolve) = (DATE(NOW() - INTERVAL 1 DAY))';
        } else if($day == 'month') {
            $whereDay = 'AND YEAR(date_resolve) = YEAR(NOW()) AND MONTH(date_resolve) = MONTH(NOW())';
        } else if($day == 'year') {
            $whereDay = 'AND YEAR(date_resolve) = YEAR(NOW())';
        } else if($day == 'lastyear') {
            $whereDay = 'AND YEAR(date_resolve) = (YEAR(NOW() - INTERVAL 1 YEAR))';
        }

        $results = DB::connection('mysql_ep_support')->select(
                "SELECT 
              --  SUM(CASE WHEN (DATE(date_resolve) = DATE(NOW())) THEN total ELSE NULL END) AS totalresolvedaily,
             --   SUM(CASE WHEN (DATE(date_resolve) = DATE(NOW() - INTERVAL 1 DAY)) AND (MONTH(date_resolve) = MONTH(NOW() - INTERVAL 1 DAY)) THEN total ELSE NULL END) AS totalresolveyesterday,
             --   SUM(CASE WHEN MONTH(date_resolve) = MONTH(NOW()) THEN total ELSE NULL END) AS totalresolvemonthly,
                SUM(total) AS total,
                full_name,
                category,
                module
                FROM ep_crm_monitoring
                WHERE user_id = '$userId' $whereDay
                AND module IS NOT NULL AND module NOT IN ('Please Select')
                AND category IS NOT NULL AND category NOT IN ('Please Select')
                GROUP BY full_name,category,module");
        return $results;
        
    }

}
