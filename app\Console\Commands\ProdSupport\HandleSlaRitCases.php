<?php

namespace App\Console\Commands\ProdSupport;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use DateTime;
use Config;
use Mail;
use App\Migrate\MigrateUtils;
use App\Services\CRMService;
use App\Model\Notify\NotifyModel;

class HandleSlaRitCases extends Command {

    public static function crmService() {
        return new CRMService;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSlaRitCases';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To send alert for cases with flag 3 (4 Hour)';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        $clsInfo = __CLASS__ . ' > ' . __FUNCTION__ . ' >> ';
        MigrateUtils::logDump($clsInfo . 'starting ..');
        $listTasks = self::crmService()->getDashboardCRMITSpec();
        MigrateUtils::logDump($clsInfo . ' found total : '.count($listTasks));
        $current = Carbon::now();
        if (count($listTasks) > 0) {
            foreach ($listTasks as $tasks) {
                $collect = collect();
                $datedue = Carbon::parse($tasks->datedue)->addHour(8)->format("Y-m-d H:i:s");
                $dueTime = Carbon::parse($tasks->acknowledgetime)->addHour(12)->format("Y-m-d H:i:s");
                if ($tasks->acknowledgetime != '' && $tasks->taskStatus == 'Acknowledge' && $tasks->taskSeverity == null && $tasks->taskFlag == 3) {

                    $dateDiff = $current->diff(new DateTime($dueTime));
                    $diffHour = $dateDiff->h;
                    $exceedRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';
                    Log::info($tasks->caseNumber . ' diff hour > ' . $diffHour . ' > ' . $exceedRemaining);

                    if ($datedue <= $current && $diffHour >= 3 && $diffHour < 4) {
                        $msg = "
*[ALERT] RIT Tasks (4 Hour) Is About To Burst.* 
  *Case Number :* $tasks->caseNumber 
  *Sub Category :* $tasks->subCategory 
  *Sub Category 2 :* $tasks->subCategory2 
  *Assigned Group :* Group IT Specialist(Production Support)
  *Time Remaining :* $exceedRemaining ";

                        $collect->put('msg', $msg);
                        $this->saveNotify('CASES_SEVERITY', $collect);
                    } else if($current > $datedue && $diffHour > 4) {
                        $msg = "
*[ALERT] RIT Tasks (4 Hour) Is Already Burst!! Please Solve Immediately.* 
  *Case Number :* $tasks->caseNumber 
  *Sub Category :* $tasks->subCategory 
  *Sub Category 2 :* $tasks->subCategory2 
  *Assigned Group :* Group IT Specialist(Production Support)
  *Time Remaining :* $exceedRemaining ";

                        $collect->put('msg', $msg);
                        $this->saveNotify('CASES_SEVERITY', $collect);
                    }
                }
            }
        }

        MigrateUtils::logDump($clsInfo . 'completed');
    }

    public function saveNotify($receiver, $collect) {
        MigrateUtils::logDump(__METHOD__ . 'starting ..');
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group'; // 'notify group , 'notify personal
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring sla tasks for RIT (4 Hour)';
        $nty->save();

        $this->sendNotifyEmail($nty->message);
    }


    protected function sendNotifyEmail($dataContents) {
        MigrateUtils::logDump(__METHOD__ . 'starting ..');
        // Process content to replace line breaks with <br> tags
        $dataContents = nl2br($dataContents);

        // Process content to replace asterisks with <strong> tags
        $dataContents = str_replace(".*", "</strong>", $dataContents);
        $dataContents = str_replace(":*", "</strong>", $dataContents);
        $dataContents = str_replace("*", "<strong>", $dataContents);

        $dataSend = array(
            "to" => [
                    '<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>'
                ],
            "subject" => '['.env('APP_ENV').'] - CRM eP [ALERT] RIT Tasks (4 Hour) Is About To Burst.'
        );
        try {
            // Using diff account mail provider. 
            $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mailer
                ->send('emails.notifyCaseIsAboutToBurstSLA', ['data' => $dataContents], function ($m) use ($dataSend) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($dataSend["to"]);
                    $m->subject($dataSend["subject"]);
                });
        } catch (\Exception $e) {
            Log::error(__CLASS__.' >>  '.__FUNCTION__. ' email : '.json_encode($dataSend["to"]). ' ERROR '. $e->getMessage());
        }

         
    }
}
