<?php $__env->startSection('header'); ?>
<!-- Search Form -->
<!-- END Search Form -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<!-- Content -->
<?php if(Auth::user()): ?>
<div class="row">
  <div class="col-lg-6">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'  style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Top 10 for <strong>Enquiry : <?php echo e(Carbon\Carbon::now()->format('d-m-Y')); ?></strong>
                </h5>
            </div>
            <div id="dash_top_enq">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>  
    <div class="col-lg-6">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'  style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Top 10 for <strong>Incident / Service : <?php echo e(Carbon\Carbon::now()->format('d-m-Y')); ?></strong>
                </h5>
            </div>
            <div id="dash_top_incserv">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
<div class="row">   
    <div class="col-lg-6">
        <div class='widget'>
            <div class='widget-extra themed-background-dark' style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Cases Pending Response <strong>CS</strong>
                </h5>
            </div>
            <div id="dash_pending_response">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>  
    <div class="col-lg-6">
        <div class='widget'>
            <div class='widget-extra themed-background-dark' style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Email Inbound <strong>Scheduler</strong>
                </h5>
            </div>
            <div id="dash_email_inbound">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>    
</div>
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'  style='background:MidnightBlue'>
                <h5 class='widget-content-light'>
                    Total Pending Cases <strong>CS</strong>
                </h5>
            </div>
            <div id="dash_cs">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<div class="modal fade" tabindex="-1" role="dialog" aria-labelledby="mySmallModalLabel" aria-hidden="true" id="confirm-modal">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="myModalLabel">Resume job que?</h4>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-warning" id="modal-btn-yes">Confirm</button>
                <button type="button" class="btn btn-primary" id="modal-btn-no">No</button>
            </div>
        </div>
    </div>
</div>

<!-- END Content -->
<?php $__env->stopSection(); ?>

<?php $__env->startSection('jsprivate'); ?>
<!-- Load and execute javascript code used only in this page -->

<script>
    var APP_URL = <?php echo json_encode(url('/')); ?>


    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $.ajax({
            url: APP_URL + '/dashboard/cs/topEnqSubCategory',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_top_enq').hide().html($data).fadeIn();
            }
        });
        $.ajax({
            url: APP_URL + '/dashboard/cs/topIncServSubCategory',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_top_incserv').hide().html($data).fadeIn();
            }
        }); 
        $.ajax({
            url: APP_URL + '/dashboard/cs/slacs',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_cs').hide().html($data).fadeIn();
            }
        });  //1min
        $.ajax({
            url: APP_URL + '/dashboard/cs/backdated/pendingresponse',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_pending_response').hide().html($data).fadeIn();
            }
        });
//        $.ajax({
//            url: APP_URL + '/dashboard/cs/email-inbound',
//            type: "GET",
//            success: function (data) {
//                $data = $(data);
//                $('#dash_email_inbound').hide().html($data).fadeIn();
//            }
//        });

        setInterval(function () {
            $.ajax({
                url: APP_URL + '/dashboard/cs/email-inbound',
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#dash_email_inbound').hide().html($data).fadeIn();
                }
            });
        }, 60000); //1min

        /* CONFIRM RESUME JOB QUE */
        $('.widget').on("click", '.modal-confirm-action', function () {

            let job_id = $(this).attr('data-id');
            let url = APP_URL + '/dashboard/cs/update-jobque';

            $('#confirm-modal').on("click", '#modal-btn-yes', function () {
                $('#confirm-modal').modal('hide');
                $('.spinner-loading').show();
                $.ajax({
                    url: url,
                    type: 'post',
                    data: {
                        '_token': $('input[name=_token]').val(),
                        'job_id': job_id,
                    },
                    success: function (data) {
                        let $data = $(data);

                        if ($data.selector === 'update-success') {
                            alert('Successfully Updated.');
                        } else {
                            alert('Failed.');
                        }

                        $('.spinner-loading').hide();
                    }
                });


            });

            $('#confirm-modal').on("click", '#modal-btn-no', function () {
                $('#confirm-modal').modal('hide');
            });

        });
        
    });

</script>
<script>
    $('#page-container').removeAttr('class');
</script>
<!-- Load and execute javascript code used only in this page -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.guest-dash', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>