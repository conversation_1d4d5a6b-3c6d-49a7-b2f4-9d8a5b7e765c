<?php

namespace App\Http\Controllers;

use App\Services\Traits\ePLoginStatisticService;
use Carbon\Carbon;
use Log;
use Illuminate\Http\Request;
use Excel;

class AppSchedulerController extends Controller {  
    
     use ePLoginStatisticService;
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }        

    
    public function getSIT(){
        return $this->getAppSchedulerSIT();
    }

    public function getAppSchedulerSIT() {
        return view('scheduler_ep.list_app_scheduler_sit', []);
    }
    
    public function getPds(){
        return $this->getAppSchedulerPds();
    }
    
    public function getAppSchedulerPds() {
        return view('scheduler_ep.list_app_scheduler_pds', []);
    }

    public function getProd(){
        return $this->getAppSchedulerProd();
    }
    
    public function getAppSchedulerProd() {
        return view('scheduler_ep.list_app_scheduler_prod', []);
    }
    
    public function showLoginStatisticReport(Request $request) {
                
        $displayePTotalLoginHourDetails = $this->getTotallogePByHourDetails();
        $displayePTotalLoginDailyDetails = $this->getTotallogePByDailyDetails();
        
        $carbonNow = Carbon::now();
        $carbonToday = Carbon::today()->toDateString(); 
        $thisYear = Carbon::now()->year;
        $searchingDate = $request->entry_date;
        $searchingMonth = $request->entry_month;
        $searchingYear = $request->entry_year;
      //$carbonMonth = $thisYear.'-'.$searchingMonth.'%';
        $carbonMonth = $searchingYear.'-'.$searchingMonth.'%';
        
        if( $searchingYear == null){
            $searchingYear = $thisYear ;
        }

        // for Date
        if ($searchingDate != null) {
            $ActualDate = Carbon::parse($searchingDate)->format('Y-m-d');            
        } else {
            $ActualDate = $carbonToday;
        }
        
        // for Month
        if($searchingMonth == 00){  
            $thisMonth = Carbon::now()->month;
            $ActualMonth = $thisYear.'-'.'0'.$thisMonth.'%';
            $monthName = sprintf("%'02d", $thisMonth);
            $exactMonth = Carbon::create($monthName)->format('F');
        // Daily Chart DATA
        $dateNow = Carbon::now()->day;
        $monthNow = Carbon::now()->month;
            if($monthNow < 10){
                $YearMonthNow = $thisYear.'-0'.$monthNow.'-';
                $ActualMonth = $thisYear.'-'.'0'.$thisMonth.'%'; 
            }else{
                $YearMonthNow = $thisYear.'-'.$monthNow.'-';
                $ActualMonth = $thisYear.'-'.$thisMonth.'%'; 
            }
            
        }else{  
            $monthName = substr($carbonMonth, 5, -1);
            $test = $searchingYear.'-'.$monthName;
            $time = strtotime($test);
            $exactMonth = date('F',$time);
            $thisMonth = Carbon::now()->month;
            $ActualMonth = $searchingYear.'-'.$searchingMonth.'%';
            $trim = substr($ActualMonth,0,-1);
            $formatDate = $trim.'-02 00:00:00';
            $formatDatetime = Carbon::createFromFormat('Y-m-d H:i:s',$formatDate);
            $dateNow = $formatDatetime->lastOfMonth()->day;
            $monthNow = $formatDatetime->lastOfMonth()->month;
            if($monthNow < 10){
                $monthN = '0'.$monthNow;
            }else{
                $monthN = $monthNow;
            }
            $YearMonthNow = $searchingYear.'-'.$monthN.'-'; 
        }
        
        //PTJ based on User Type (Hourly)
        $displayPtjChartByHourly = $this->getPTJHourlybyChart($ActualDate);
        $displayUserTypePTJHourlybyChart5 = $this->getUserTypePTJHourlybyChart5($ActualDate);
        $displayUserTypePTJHourlybyChart2 = $this->getUserTypePTJHourlybyChart2($ActualDate);
        $displayUserTypePTJHourlybyChart7 = $this->getUserTypePTJHourlybyChart7($ActualDate);
        $displayUserTypePTJHourlybyChart10 = $this->getUserTypePTJHourlybyChart10($ActualDate);
        $displayUserTypePTJHourlybyChart13 = $this->getUserTypePTJHourlybyChart13($ActualDate);
        $displayUserTypePTJHourlybyChart4 = $this->getUserTypePTJHourlybyChart4($ActualDate);
        $displayUserTypePTJHourlybyChart11 = $this->getUserTypePTJHourlybyChart11($ActualDate);
        $displayUserTypePTJHourlybyChart1 = $this->getUserTypePTJHourlybyChart1($ActualDate);
        $displayUserTypePTJHourlybyChart6 = $this->getUserTypePTJHourlybyChart6($ActualDate);
        $displaySupplierChartByHourly = $this->getSupplierHourlybyChart($ActualDate);    
        //PTJ based on User Type (Daily)
        $DailySummaryPTJ = $this->getPTJDailybylineChart($ActualMonth);
        $displayUserTypePTJDailybyChart5 = $this->getUserTypePTJDailybyChart5($ActualMonth);
        $displayUserTypePTJDailybyChart2 = $this->getUserTypePTJDailybyChart2($ActualMonth);
        $displayUserTypePTJDailybyChart7 = $this->getUserTypePTJDailybyChart7($ActualMonth);
        $displayUserTypePTJDailybyChart10 = $this->getUserTypePTJDailybyChart10($ActualMonth);
        $displayUserTypePTJDailybyChart13 = $this->getUserTypePTJDailybyChart13($ActualMonth);
        $displayUserTypePTJDailybyChart4 = $this->getUserTypePTJDailybyChart4($ActualMonth);
        $displayUserTypePTJDailybyChart11 = $this->getUserTypePTJDailybyChart11($ActualMonth);
        $displayUserTypePTJDailybyChart1= $this->getUserTypePTJDailybyChart1($ActualMonth);
        $displayUserTypePTJDailybyChart6 = $this->getUserTypePTJDailybyChart6($ActualMonth);
        $DailySummarySupp = $this->getSupplierDailybylineChart($ActualMonth);
        
        //getSupplierbyMonthly
        $displaySupplierChartByMonthly = $this->getSupplierMOnthlybyChart($searchingYear);  
        //getPTJbyMonthly
        $displayPTJChartByMonthly = $this->getSPTJMOnthlybyChart($searchingYear);
        
        
        $TIME_RANGE_LOGIN =  15;
        $DisplayData15MinuteBefore = $this->getLoginStatisticCurr15MinuteBefore($carbonNow->toDateTimeString(),$TIME_RANGE_LOGIN); 
        $displaySessionPTJ = $this->getLoginStatisticSessionPTJ();
        $displaySessionSupp = $this->getLoginStatisticSessionSupp();
        
        // get total user by session
        $countSessionPTJ = 0;
        foreach($displaySessionPTJ as $obj){
            
            $countSessionPTJ = $countSessionPTJ+$obj->ptj_total;
        }
        $countSessionSupp = 0;
        foreach($displaySessionSupp as $obj){
            
            $countSessionSupp = $countSessionSupp+$obj->supplier;
        }
        $totalSession = $countSessionPTJ+$countSessionSupp;
        
        // get time within 15 minute before curr time
        $today = $carbonToday; 
        //$timeToday = Carbon::now()->toTimeString();
        $currTimeResult = $carbonNow->format('H:i A');

        if($TIME_RANGE_LOGIN != null && $TIME_RANGE_LOGIN > 0){
            $timeBefore = $carbonNow->subMinutes($TIME_RANGE_LOGIN);
            $timeBeforeResult = $timeBefore->format('H:i A');  
            $currTimeResult = ' within '.$TIME_RANGE_LOGIN .' minutes from '.$timeBeforeResult. ' to '.$currTimeResult;
        }
        
        $resultTime = '('.$currTimeResult.')';
        
        // Get data PTJ & Supplier login to eP current time
        $countPTJ = 0;
        $countSupp = 0;
        $countTotal = 0;
        foreach ($DisplayData15MinuteBefore as $obj) {
            if ($obj->user_group == 'PTJ') {

                $countPTJ = $countPTJ + $obj->login_total;
            } else{

                $countSupp = $countSupp + $obj->login_total;
            }
            
            $countTotal = $countPTJ + $countSupp;
        } 
        
        //Hourly Chart DATA
        $hourNow = Carbon::now()->hour;
        
        // Display data for PTJ hourly chart
        $dataPTJHourlyStat = $this->populateHourByUserType($displayPtjChartByHourly, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 5
        $dataUserTypePTJHourlyStat5 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart5, $hourNow);

        // Display data for PTJ hourly chart based on user type = 2
        $dataUserTypePTJHourlyStat2 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart2, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 7
        $dataUserTypePTJHourlyStat7 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart7, $hourNow);

        // Display data for PTJ hourly chart based on user type = 10
        $dataUserTypePTJHourlyStat10 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart10, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 13
        $dataUserTypePTJHourlyStat13 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart13, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 4
        $dataUserTypePTJHourlyStat4 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart4, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 11
        $dataUserTypePTJHourlyStat11 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart11, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 1
        $dataUserTypePTJHourlyStat1 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart1, $hourNow);
        
        // Display data for PTJ hourly chart based on user type = 6
        $dataUserTypePTJHourlyStat6 = $this->populateHourByUserType($displayUserTypePTJHourlybyChart6, $hourNow);
        
        // Display data for supplier hourly chart
        $dataSupplierHourlyStat = array();   
        foreach ($displaySupplierChartByHourly as $suppData) { 
              $time = $suppData->timeLog;
             $DisplayTime = Carbon::parse($time)->format('H A');
                array_push($dataSupplierHourlyStat, ['label' => $DisplayTime,'y' => $suppData->totalLog]);
        }
        
        //Display data for supplier Monthly
        $dataSupplierMonthlyStat = array();   
        foreach ($displaySupplierChartByMonthly as $suppDataMonth) { 
                array_push($dataSupplierMonthlyStat, ['label' => $suppDataMonth->MONTH,'y' => $suppDataMonth->TOTAL_LOGIN]);
        }
                
                //Display data for supplier Monthly
        $dataptjMonthlyStat = array();   
        foreach ($displayPTJChartByMonthly as $ptjDataMonth) { 
                array_push($dataptjMonthlyStat, ['label' => $ptjDataMonth->MONTH,'y' => $ptjDataMonth->TOTAL_LOGIN]);
        }
                
        // Display data for PTJ daily chart
        $displayPtjLoginDaily = $this->populateDailyByUserType($DailySummaryPTJ, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 5
        $dataUserTypePTJDailyStat5 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart5, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 2
        $dataUserTypePTJDailyStat2 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart2, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 7
        $dataUserTypePTJDailyStat7 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart7, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 10
        $dataUserTypePTJDailyStat10 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart10, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 13
        $dataUserTypePTJDailyStat13 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart13, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 4
        $dataUserTypePTJDailyStat4 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart4, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 11
        $dataUserTypePTJDailyStat11 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart11, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 13
        $dataUserTypePTJDailyStat1 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart1, $dateNow, $YearMonthNow);
        
        // Display data for PTJ daily chart based on user type = 6
        $dataUserTypePTJDailyStat6 = $this->populateDailyByUserType($displayUserTypePTJDailybyChart6, $dateNow, $YearMonthNow);
               
        //Display data for line chart for Supplier
        $displaySuppLoginDaily = array();
        foreach ($DailySummarySupp as $pInfo) { 
             $days = 'day '.$pInfo->LogDate;
                array_push($displaySuppLoginDaily, ['label' =>$days, 'y' =>$pInfo->totalPtjLogin]);
        }  
        
        // Sets the top option to be the current year. (IE. the option that is chosen by default).
        $currently_selected = date('Y');
        // Year to start available options at
        $earliest_year = 2018;
        // Set your latest year you want in the range, in this case we use PHP to just set it to the current year.
        $latest_year = date('Y');

        session()->flashInput($request->input());
        return view('ep_login_stat', [
            'dataePHourDetails' => $displayePTotalLoginHourDetails,
            'dataSupplierHourlyStat' => $dataSupplierHourlyStat,
            'dataPTJHourlyStat' => $dataPTJHourlyStat,
            'dataUserTypePTJHourlyStat5' => $dataUserTypePTJHourlyStat5,
            'dataUserTypePTJHourlyStat2' => $dataUserTypePTJHourlyStat2,
            'dataUserTypePTJHourlyStat7' => $dataUserTypePTJHourlyStat7,
            'dataUserTypePTJHourlyStat10' => $dataUserTypePTJHourlyStat10,
            'dataUserTypePTJHourlyStat13' => $dataUserTypePTJHourlyStat13,
            'dataUserTypePTJHourlyStat4' => $dataUserTypePTJHourlyStat4,
            'dataUserTypePTJHourlyStat11' => $dataUserTypePTJHourlyStat11,
            'dataUserTypePTJHourlyStat1' => $dataUserTypePTJHourlyStat1,
            'dataUserTypePTJHourlyStat6' => $dataUserTypePTJHourlyStat6,
            'dataePDailyDetails' => $displayePTotalLoginDailyDetails,
            'displayPtjLoginDaily' => $displayPtjLoginDaily,
            'dataUserTypePTJDailyStat5' => $dataUserTypePTJDailyStat5,
            'dataUserTypePTJDailyStat2' => $dataUserTypePTJDailyStat2,
            'dataUserTypePTJDailyStat7' => $dataUserTypePTJDailyStat7,
            'dataUserTypePTJDailyStat10' => $dataUserTypePTJDailyStat10,
            'dataUserTypePTJDailyStat13' => $dataUserTypePTJDailyStat13,
            'dataUserTypePTJDailyStat4' => $dataUserTypePTJDailyStat4,
            'dataUserTypePTJDailyStat11' => $dataUserTypePTJDailyStat11,
            'dataUserTypePTJDailyStat1' => $dataUserTypePTJDailyStat1,
            'dataUserTypePTJDailyStat6' => $dataUserTypePTJDailyStat6,
            'displaySuppLoginDaily' => $displaySuppLoginDaily,
            'sumSupp' => $countSupp,
            'sumPTJ' => $countPTJ,
            'sumUserLogin' => $countTotal,
            'totalSessionPTJ' =>$countSessionPTJ,
            'totalSessionSupp' => $countSessionSupp,
            'totalSession' => $totalSession,
            'today' => $today,
            'ActualDate' => $ActualDate,
            'exactMonth' => $exactMonth,
            'resultTime' => $resultTime,
            'currently_selected' => $currently_selected,
            'earliest_year' => $earliest_year,
            'latest_year' => $latest_year ,
            'dataSupplierMonthlyStat' => $dataSupplierMonthlyStat,
            'dataptjMonthlyStat' => $dataptjMonthlyStat
       ]);
        
    }   
    
    protected function populateHourByUserType($list,$hourNow){
 
        $listResult = array();
       
        for($int = 0; $int < $hourNow; $int++){
            $time = sprintf("%'02d", $int).':00:00';
            
            
            $DisplayTime = Carbon::parse($time)->format('H A'); 
            $objHourLogin = collect($list)->where('time_login',$time)->first();
            if($objHourLogin != null){
                array_push($listResult, ['label' => $DisplayTime,'y' => $objHourLogin->totalLog]);
            }else{
                array_push($listResult, ['label' => $DisplayTime,'y' => 0]);
            }

        }
        
        return $listResult;
    }
    
    protected function populateDailyByUserType($list,$dateNow,$YearMonthNow){
        $listResult = array();
        for($int = 1; $int <= $dateNow; $int++){                     
            $time = $YearMonthNow.sprintf("%'02d", $int);  
            $DisplayTime = Carbon::parse($time)->format('d').' days';
            $objDailyLogin = collect($list)->where('date_login',$time)->first();           
            if($objDailyLogin != null){
                array_push($listResult, ['label' => $DisplayTime,'y' => $objDailyLogin->totalPtjLogin]);
            }else{
                array_push($listResult, ['label' => $DisplayTime,'y' => 0]);
            }

        }
        return $listResult;
        
    }
    
    // download
    public function downloadReportMonthly($currYear){

        ini_set('memory_limit', '-1');
        ini_set('max_execution_time', 300); //3 minutes

        $dateNow = Carbon::now()->format('Ymd-hi');
        $fileName = 'epLoginSummaryReport_'.$dateNow;
        $list = $this->getDetailsDownload($currYear);
        Excel::create($fileName, function($excel)use($list) {
            $excel->setTitle('List of Top 10 Enquiry');

            $excel->sheet('TopEnquiry', function($sheet) use ($list) {
                $sheet->setOrientation('landscape');

                $sheet->setStyle(array(
                    'font' => array(
                        'name' => 'Calibri',
                        'size' => 11,
                        'bold' => false
                    )
                ));

                //http://www.maatwebsite.nl/laravel-excel/docs/reference-guide#formatting
                $sheet->setColumnFormat(array(
                    'A' => '@',
                    'B' => '@',
                    'C' => '@',
                    'D' => '@',
                ));

                $sheet->row(1, array(
                    'YEAR', 'MONTH','ORG TYPE', 'TOTAL LOGIN'
                ));    
                $sheet->row(1, function($row) {
                    // call cell manipulation methods
                    $row->setBackground('#684E49');
                });
                $sheet->setAutoSize(true);

                $sheet->cells('A1:F1', function($cells) {
                    // manipulate the range of cells
                    // Set with font color
                    $cells->setFontColor('#fff');

                    // Set font family
                    $cells->setFontFamily('Calibri');

                    // Set font size
                    $cells->setFontSize(11);

                    // Set font weight to bold
                    $cells->setFontWeight('bold');

                    // Set all borders (top, right, bottom, left)
                    $cells->setBorder('solid', 'solid', 'solid', 'solid');
                });

                $count = 2;

                foreach ($list as $obj) {
                                        
                    $sheet->row($count, array(                            
                            $obj->YEAR,
                            $obj->MONTH,
                            $obj->ORG_TYPE,
                            $obj->TOTAL_LOGIN,
                        )
                    );
                    $count++;
                }
            });         

        })->store('xlsx', storage_path('app/Report'));

        $headers = [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
            'Cache-Control' => 'max-age=0',
        ];

        $fullPath = storage_path('app/Report/'.$fileName.'.xlsx');
        return response()->download($fullPath, $fileName.'.xlsx', $headers);
        
    }      
}
