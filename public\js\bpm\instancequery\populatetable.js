/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
function populateTable(listdata, newOffset) {
    var html = '<th class="text-center" style="width: 50px"></th><th class="text-center" style="width: 100px">Instance</th><th class="text-center" style="width: 220px">Conversation</th><th class="text-center" style="width: 220px">Source</th><th class="text-center" style="width: 100px">Composite</th><th class="text-center" style="width: 100px">Version</th><th class="text-center" style="width: 100px">State</th><th class="text-center" style="width: 100px">Created</th>';

    var pageNo = 0;
    if (newOffset < 0) {
        pageNo = 1;
    } else {
        pageNo = +newOffset + 1;
    }
    listdata.forEach(function (entry) {

        html += '<tbody style="font-size:80%;"><tr>';

        var link = '<a href="/bpm/instance/find/?composite_instance_id=';
        link += entry["id"];
        link += '" target="_blanks">';
        link += entry["id"];
        link += '</a>';

        html += '<td class="text-center"><input type="checkbox" id="checkbox_instance" name="checkbox_instance" value="' + entry["id"] + '"/></td>';
        html += "<td class='text-center'>" + link + "</td>";
        html += "<td class='text-center'>" + entry["conversation"] + "</td>";
        html += "<td class='text-center'>" + entry["source"] + "</td>";
        html += "<td class='text-center'>" + entry["compositeName"] + "</td>";
        html += "<td class='text-center'>" + entry["version"] + "</td>";

        if (entry["state"] === 'RUNNING') {
            html += "<td class='text-center'><i><strong> Running</strong></i></td>";
        } else if (entry["state"] === 'FAULTED') {
            html += "<td class='text-center'><i class ='fa fa-times-circle' style='color:red;'> Faulted</i><span style='color:red;'></span></td>";
        } else if (entry["state"] === 'COMPLETED') {
            html += "<td class='text-center'><i class ='fa fa-check-circle' style='color:green;'> Completed</i><span style='color:green;'></span></td>";
        } else if (entry["state"] === 'TERMINATED') {
            html += "<td class='text-center'><i class ='fa fa-minus-circle' style='color:grey;'> Terminated</i><span style='color:grey;'></span></td>";
        } else if (entry["state"] === 'RECOVERY') {
            html += "<td class='text-center'><i class ='fa fa-exclamation' style='color:red;'> Recovery</i><span style='color:red;'></span></td>";
        } else if (entry["state"] === 'SUSPENDED') {
            html += "<td class='text-center'> Suspended</td>";
        }
        var d = new Date(entry["createDate"]);
        let formatted_date = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear() + " " + d.getHours() + ":" + (d.getMinutes() < 10 ? '0' : '') + d.getMinutes() + ":" + (d.getSeconds() < 10 ? '0' : '') + d.getSeconds();
        var hours = d.getHours();
        var ampm = hours >= 12 ? 'PM' : 'AM';

        html += "<td class='text-center'>" + formatted_date + ' ' + ampm + "</td>";
    });

    html += "</tr><tbody>";
    html += '<tfoot><tr><td colspan="8" style="font-size:90%;text-align: right;"><i>Page ' + pageNo + '</i></td></tr></tfoot>';
    document.getElementById("table_instance").innerHTML = html;
}