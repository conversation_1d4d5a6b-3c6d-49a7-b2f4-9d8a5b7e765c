<?php

namespace App;

use DB;
use Auth;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Ramsey\Uuid\Uuid;

/** This class mysql_ep_support re-pointing to mysql_ep_support table ep_action_log * */
class EpSupportOsbLog extends Model {

    protected $connection = 'mysql_ep_support';
    protected $primaryKey = 'id';
    protected $table = "ep_osb_batch_trigger";

    public static function createOsbLog($username, $retryId, $transId, $params, $status, $type) {
        if ($username == null) {
            if (Auth::user() != null) {
                $username = Auth::user()->user_name;
            } else {
                $username = 'TechnicalMiddlewareUser';
            }
        }
        $osbLog = new EpSupportOsbLog;
        $osbLog->batch_retry_dtl_id = $retryId;
        $osbLog->trans_id = $transId;
        $osbLog->params = $params;
        $osbLog->status = $status;
        $osbLog->created_by = $username;
        $osbLog->type = $type;
        $osbLog->created_at = Carbon::now();
        $osbLog->save();

        return $osbLog;
    }
    
    public static function createRunningLog($username, $total, $status, $type) {
        if ($username == null) {
            if (Auth::user() != null) {
                $username = Auth::user()->user_name;
            } else {
                $username = 'TechnicalMiddlewareUser';
            }
        }
        $osbLog = new EpSupportOsbLog;
        $osbLog->message = $total;
        $osbLog->status = $status;
        $osbLog->created_by = $username;
        $osbLog->type = $type;
        $osbLog->created_at = Carbon::now();
        $osbLog->save();

        return $osbLog;
    }
    
    public static function updateRunningLog($id, $username, $status, $params) {
        if ($username == null) {
            if (Auth::user() != null) {
                $username = Auth::user()->user_name;
            } else {
                $username = 'TechnicalMiddlewareUser';
            }
        }
        $result = DB::connection('mysql_ep_support')
                ->table('ep_osb_batch_trigger')
                ->where('id', $id)
                ->update([
            'status' => $status,
            'params' => $params,
            'updated_at' => Carbon::now(),
            'updated_by' => $username,
        ]);

        return $result;
    }

    public static function checkingRunningTriggerOsb($transId=null, $status=null, $type) {
        $result = DB::connection('mysql_ep_support')
                ->table('ep_osb_batch_trigger');
        
        if($transId !== '' && $status !== '') {
            $result->where('trans_id', '=', $transId)
                ->where('status', '=', $status);
        }else{
            $result->where('status', '=', 'Processing');
        }
            $result->where('type','=', $type);
            
        return $result->get();
    }

    public static function updateOsbLog($id, $username, $status, $transId, $params, $serviceName) {
        $result = DB::connection('mysql_ep_support')
                ->table('ep_osb_batch_trigger')
                ->where('id', $id)
                ->where('trans_id', $transId)
                ->update([
            'status' => $status,
            'updated_at' => Carbon::now(),
            'updated_by' => $username,
            'params' => $params,
            'message' => $serviceName
        ]);

        return $result;
    }

}
