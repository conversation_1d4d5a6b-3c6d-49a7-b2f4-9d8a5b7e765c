@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
@if($connectionType === 'ep')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/wsnotify/dashboard/ep') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/ep') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/ep') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@elseif($connectionType === 'crm')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/wsnotify/dashboard/crm') }}">Dashboard</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/reminder/ep') }}">Reminder</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receiver/crm') }}">Receiver</a>
            </li>
            <li>
                <a href="{{ url('/wsnotify/receivergroup/crm') }}">Receiver Group</a>
            </li>
        </ul>
    </div>
</div>
@endif
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
@if($connectionType === 'ep')
<h2>EP NOTIFICATION</h2>
@elseif($connectionType === 'crm')
<h2>CRM JBAL NOTIFICATION</h2>
@endif
<div class="row">
    <div class="col-lg-12">
        <div class="widget">
            <div class="widget-extra themed-background-dark">
                <h5 class="widget-content-light">
                    <strong>Pending Notification</strong>
                </h5>
            </div>
            <div id="pending_notification" class="widget-extra-full">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    <strong>Last Failed Job</strong>
                </h5>
            </div>
            <div id="last_failed_job" class="widget-extra-full">
                <table style="width:100%">
                    <tr>
                        <th>JOB NAME</th>
                        <th>MESSAGE</th>
                        <th>DESCRIPTION</th>
                        <th>DATE ENTERED</th>
                    </tr>
                    @if($lastJobFailed)
                    <tr>
                        <td>@if($lastJobFailed->source_remark != null){{$lastJobFailed->source_remark}}@endif</td>
                        <td>{{$lastJobFailed->message}}</td>
                        <td>{{$lastJobFailed->description}}</td>
                        <td>{{$lastJobFailed->date_entered}}</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    <strong>Last Success Job</strong>
                </h5>
            </div>
            <div id="last_success_job" class="widget-extra-full">
                <table style="width:100%">
                    <tr>
                        <th>JOB NAME</th>
                        <th>MESSAGE</th>
                        <th>DESCRIPTION</th>
                        <th>DATE ENTERED</th>
                    </tr>
                    @if($lastJobFailed)
                    <tr>
                        <td>@if(isset($lastJobCompleted->source_remark)){{$lastJobCompleted->source_remark}}@endif</td>
                        <td>{{$lastJobCompleted->message}}</td>
                        <td>@if(isset($lastJobCompleted->description)){{$lastJobCompleted->description}}@endif</td>
                        <td>@if(isset($lastJobCompleted->date_entered)){{$lastJobCompleted->date_entered}}@endif</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    <strong>WhatsApp Account Status</strong>
                </h5>
            </div>
            <div id="whatsapp_status" class="widget-extra-full">
                <table style="width:100%">
                    <tr>
                        <th>STATUS</th>
                        <th>DESCRIPTION</th>
                        <th>DATE ENTERED</th>
                    </tr>
                    @if($whatsAppStatus)
                    <tr>
                        <td>{{$whatsAppStatus->status}}</td>
                        <td>{{$whatsAppStatus->description}}</td>
                        <td>{{$whatsAppStatus->date_entered}}</td>
                    </tr>
                    @endif
                </table>
            </div>
        </div>
    </div>
</div>

@endif
<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- END Content -->
@endsection

@section('jsprivate')
<script>
    $('#page-container').removeAttr('class');
</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    // $(function() {
    //     TablesDatatables.init();
    // });
</script>
@if($connectionType === 'ep')
<script>
    var APP_URL = {!!json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });

    $(document).ready(function() {
        $('.widget').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $.ajax({
            url: APP_URL + '/wsnotify/total-pending-notifications/ep',
            success: function(data) {
                $data = $(data);
                $('#pending_notification').hide().html($data).fadeIn();
            }
        });
    })
</script>
@elseif($connectionType === 'crm')
<script>
    var APP_URL = {!!json_encode(url('/')) !!}
    
    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });
    
    $(document).ready(function() {
        $('.widget').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $.ajax({
            url: APP_URL + '/wsnotify/total-pending-notifications/crm',
            success: function(data) {
                $data = $(data);
                $('#pending_notification').hide().html($data).fadeIn();
            }
        });
    })
</script>
@endif
@endsection