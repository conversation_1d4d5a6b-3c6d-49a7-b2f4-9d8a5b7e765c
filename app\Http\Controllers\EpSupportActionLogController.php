<?php

namespace App\Http\Controllers;

use App\EpSupportActionLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class EpSupportActionLogController extends Controller
{
    public function main()
    {
        return view('actionlog.main');
    }

    public function fetch()
    {
        $actionTypeName = Cache::remember('actionTypeName', 3600, function () {
            return EpSupportActionLog::select('action_type', 'action_name')
                ->groupBy('action_type', 'action_name')
                ->orderBy('action_type', 'asc')
                ->get();
        });

        $actionTypeArray = [];

        $userLists = Cache::remember('userLists', 3600, function () {
            $userListsQuery = EpSupportActionLog::select('created_by')
                ->groupBy('created_by')
                ->orderBy('created_by', 'asc')
                ->get();

            return $userListsQuery->pluck('created_by')->toArray();
        });

        // Organize the data into the desired format
        foreach ($actionTypeName as $action) {
            $actionType = $action['action_type'];
            $actionName = $action['action_name'];

            if (!array_key_exists($actionType, $actionTypeArray)) {
                $actionTypeArray[$actionType] = [];
            }

            $actionTypeArray[$actionType][] = $actionName;
        }

        return response()->json(['actionTypeName' => $actionTypeArray, 'userLists' => $userLists]);
    }


    public function search(Request $request)
    {

        $query = EpSupportActionLog::query();

        if ($request->has('actionType')) {
            $query->where('action_type', $request->input('actionType'));
        }

        if ($request->has('actionName')) {
            $query->where('action_name', $request->input('actionName'));
        }

        if ($request->has('actionUser')) {
            $query->where('created_by', $request->input('actionUser'));
        }

        if ($request->has('actionDate')) {
            // dd(Carbon::createFromFormat('d/m/Y', $request->input('actionDate')));
            // $query->whereDate('created_at', '2023-11-01');
            $date = Carbon::createFromFormat('d/m/Y', $request->input('actionDate'))->format('Y-m-d');
            $query->whereDate('created_at', $date);
        }

        if ($request->has('actionData')) {
            $query->where('action_data', 'like', '%' . $request->input('actionData') . '%');
        }

        $logs = $query->orderBy('id', 'desc')->get();

        return response()->json(['resultLists' => $logs]);
    }
}
