@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')



    <div class="block">
        <div class="block-title">
            <h2><strong>PHIS</strong> : Order Details</h2>
        </div>

        <div class="block">
            <div class="block-title">
                <ul id="myTab" class="nav nav-tabs" data-toggle="tabs">
                    <li class="{{ empty($tabName) || $tabName == 'search-tab-docno' ? 'active' : '' }}"><a href="#search-tab-docno">Document No.</a></li>                   
                </ul>
            </div>

            <div class="tab-content">
                <div class="tab-pane {{ empty($tabName) || $tabName == 'search-tab-docno' ? 'active' : '' }}" id="search-tab-docno">
                    <form id="form-search-mminf" action="{{url("/find/phis/search")}}" method="post" class="form-horizontal" onsubmit="return true;">
                        {{ csrf_field() }}
                        <input name="_method" id="_method"  type="hidden" value="POST">
                        <input id="searchType" name="searchType" type="hidden" value="search_docno">
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="doc_no">Document No. (CR/CO) </label>
                            <div class="col-md-5">
                                <input id="doc" name="doc_no" class="form-control" placeholder="Document No. (CR/CO).." type="text" 
                                       @if(isset($formSearch, $formSearch["doc_no"])) value="{{ $formSearch["doc_no"] }}" @endif>
                            </div>
                        </div>   
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="phis_no">Order Reference No. (Phis No) </label>
                            <div class="col-md-5">
                                <input id="doc" name="phis_no" class="form-control" placeholder="eg: 180011301110070294P" type="text" 
                                       @if(isset($formSearch, $formSearch["phis_no"])) value="{{ $formSearch["phis_no"] }}" @endif>
                            </div>
                        </div>                   
                        <div class="form-group form-actions">
                            <div class="col-md-9 col-md-offset-3">
                                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                            </div>
                        </div>
                    </form>
                </div>

            </div>
        </div>
    </div>
    @if($result && $result != 'notfound')
        <div class="block">
            <ul class="text-info">
                <li><strong>Note</strong> : If column 'Order Reference No./PHIS No.' is null then the order is not through integration Phis-eP.</li>
                
            </ul>           
            <div class="table-responsive">
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">No.</th>
                        <th class="text-center">Order Reference No./PHIS No.</th>
                        <th class="text-center">Created Date</th> 
                        <th class="text-center">CR No.</th> 
                        <th class="text-center">CO No.</th> 
                        <th class="text-center">Current Status CR No.</th> 
                        <th class="text-center">PTJ Code</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach ($result as $indexKey =>$data)
                        <tr>
                            <td class="text-center">{{ ++$indexKey }}</td>
                            <td class="text-center">{{ $data->order_reference_no }}</td>
                            <td class="text-center">{{ $data->created_date }}</td>
                            <td class="text-center"><a target="_blank" href="{{ url('/find/trans/track/docno') }}/{{ $data->cr_no }}" >{{ $data->cr_no }}</a></td>
                            <td class="text-center"><a target="_blank" href="{{ url('/find/trans/track/docno') }}/{{ $data->co_no }}" >{{ $data->co_no }}</a></td>
                            <td class="text-center">{{ $data->status_ep }}</td>
                            <td class="text-center">{{ $data->ptj_code}}</td>

                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif
    @if($result == 'notfound')
        <div class="block block-alt-noborder full text-center label-primary">
              <span style="color: #FFF;">Tidak dijumpai!</span>
        </div>
    @endif

   

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    @include('_shared._modalListLogAction')
    <!-- END Content -->

@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function(){ TablesDatatables.init(); });</script>
    <script src="/js/pages/modalListActionLogDatatable.js"></script>
    <script>$(function(){ ModalListActionLogDatatable.init(); });</script>
    <script>

@endsection