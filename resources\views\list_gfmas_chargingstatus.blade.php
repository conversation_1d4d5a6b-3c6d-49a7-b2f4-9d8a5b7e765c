@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Check Charging Status Stuck BPM <br>
                <small>Check Charging Status Stuck BPM List. Query check date NOW - 2 days </small>
            </h1>
        </div>
    </div>

    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Check Charging Status Stuck BPM  </strong>
                        <small></small> 
                    </h1>
                   <small><a href="{{url('/crm/guideline/stuck_gfmas/EPP013_ERROR_HANDLER.docx')}}" style="color: white; text-decoration: underline;">Guideline Charging Status Stuck BPM</a></small>
                </div>
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                {{ csrf_field() }}
                <div class="table-responsive">
                    <table id="stuck-bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DATE</th>
                            <th class="text-center">STATUS</th>
                            <th class="text-center">DOC REQUEST</th>
                            <th class="text-center">DOC ORDER</th>
                            <th class="text-center">SAP ORDER NO.</th>
                            <th class="text-center">BPM INSTANCE</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">STUCK </th>
                            <th class="text-center">TRIGGER CHECK CHARGING STATUS TO BPM </th>
                            <th class="text-center">DATE GFM-100 </th>
                            <th class="text-center">DATE EPP-013 (Check Charging) </th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->status_id }}</td>
                                <td class="text-center"><a href="{{url('/find/trans/track/docno')}}/{{ $data->doc_no_request }}" target="_blank" title="Find Tracking Diary"> {{ $data->doc_no_request }}</a></td>
                                <td class="text-center"><a href="{{url('/find/osb/log')}}/{{ $data->doc_no_order }}" target="_blank" title="Find OSB Log"> {{ $data->doc_no_order }}</a></td>
                                <td class="text-center">{{ $data->sap_order_no }}</td>
                                <td class="text-center"><a href="{{url('bpm/instance/find')}}?composite_instance_id={{ $data->bpm_instance }}" target="_blank" title="Find BPM Instance">{{ $data->bpm_instance }}</a></td>
                                <td class="text-center">{{ $data->bpm_composite }}</td>
                                <td class="text-center">{{ $data->stuck_charging_status }}</td>
                                <td class="text-center">
                                    @if($data->stuck_charging_status == 'YES' ) 
                                        <a href="javascript:void(0)" class="text-primary"  data-docno="{{ $data->doc_no_order }}"  id="trigger_{{ $data->doc_no_order }}"
                                           data-act="{{url('/trigger/gfmas/triggerBPMCallback/')}}" 
                                           onclick="triggerCallBack(this)">Trigger Here</a>
                                    @else
                                     No Action
                                    @endif
                                </td>
                                <td class="text-center"><a href="javascript:void(0)" class="text-primary" 
                                           data-act="{{url('/find/osb/log')}}/{{ $data->doc_no_order }}" 
                                           >{{ $data->transDate100 }}</a></td>
                                <td class="text-center"><a href="javascript:void(0)" class="text-primary" 
                                           data-act="{{url('/find/osb/log')}}/{{ $data->doc_no_order }}" 
                                           >{{ $data->transDate013 }}</a></td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif

    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    function triggerCallBack(obj){
        var docNo =$(obj).attr('data-docno');
        var triggerId =$(obj).attr('id');
        var urlAct =$(obj).attr('data-act');
        var csrf = $("input[name=_token]").val();
        
        $("#response").hide();
        $("#response-msg").html("");
        
        $('#wait-modal').modal('toggle');

        $.ajax({
            url: urlAct,
            method : "POST",
            dataType : "json",
            data : {"_token":csrf,"doc_no":docNo},
            context: document.body
        }).done(function(resp) {
            if(resp.status === 'success'){
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("<h3 class='pull-left' style='margin:10px;width:100%;'> <i class='fa fa-check-circle'></i> Success! TriggerBPMCallback => "+resp.doc_no+" </h3>\n\
                    ");

                $("#"+triggerId).attr('data-docno','');   
                $("#"+triggerId).text('DONE');
                $('#wait-modal').modal('hide');
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("<h3 class='pull-left' style='margin:10px;width:100%;'> <i class='fa fa-close'></i> Failed! TriggerBPMCallback => "+resp.doc_no+" </h3>");
                $('#wait-modal').modal('hide');
            }
        });
    }
    App.datatables();
    $('#stuck-bpm-datatable').dataTable({
                order: [[ 6, "desc" ],[ 4, "desc" ]],
                columnDefs: [  ],
                pageLength: 5,
                lengthMenu: [[5,10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
            });
            
    
</script>
@endsection



