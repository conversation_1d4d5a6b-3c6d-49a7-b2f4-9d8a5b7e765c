<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Services\Traits\BPMService;
use Log;
use App\Model\Notify\NotifyModel;
use App\Services\Traits\OSBService;

class MonitorBpmStlRunning extends Command {

    use BPMService;
    use OSBService;

    /* DO NOT CHANGE THE FORMAT ON MSG NOTIFY */
    public static $module = '
        
*MODULE :*';
    public static $total = '
*TOTAL :*';
    public static $url = '
*URL :*';
    public static $basicUrl = 'https://epss.eperolehan.gov.my/dashboard/bpm/stl'; 

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-bpm-stl-running';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will monitor total count of stl bpm with running status. Count more than 0, send whatsapp notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            $collect = collect();
            $msg = "*BPM STL WITH STATUS RUNNING*
*".Carbon::now()."*";
            $totalCount = 0;

            $listPM = $this->findListInstanceProfileMgmtRunningStl();
            $totalPM = count($listPM);
            $listSM = $this->findListInstanceSupplierMgmtRunningStl();
            $totalSM = count($listSM);
            $listCodification = $this->findListInstanceCodificationRunningStl();
            $totalCodi = count($listCodification);
            $listPP = $this->findListInstanceProcurementPlanRunningStl();
            $totalPP = count($listPP);
            $listCM = $this->findListInstanceContractMgmtRunningStl();
            $totalCM = count($listCM);
            $listPhis = $this->getListTransWsPhisFailedSend();
            $totalPhis = count($listPhis);
            $listOrder = $this->findListInstanceOrderRunningStl();
            $totalOrder = count($listOrder);
            $listFL = $this->findListInstanceFulfilmentRunningStl();
            $totalFL = count($listFL);
            $listDP = $this->findListInstanceSourcingDpRunningStl();
            $totalDP = count($listDP);
            $listQT = $this->findListInstanceSourcingQtRunningStl();
            $totalQT = count($listQT);
            $listYepOrder = $this->findListInstanceYEPOrderRunningStl();
            $totalYepOrder = count($listYepOrder);
            $listYepFulfilment = $this->findListInstanceYEPFulfilmentRunningStl();
            $totalYepFulfilment = count($listYepFulfilment);

            if ($totalPM > 0) {
                $totalCount = $totalCount + $totalPM;
                $msg = self::msgMethod($msg, $totalPM, 'Profile Management', 'Profile_Management');
            }
            if ($totalSM > 0) {
                $totalCount = $totalCount + $totalSM;
                $msg = self::msgMethod($msg, $totalSM, 'Supplier Management', 'Supplier_Management');
            }
            if ($totalCodi > 0) {
                $totalCount = $totalCount + $totalCodi;
                $msg = self::msgMethod($msg, $totalCodi, 'Codification', 'Codification');
            }
            if ($totalPP > 0) {
                $totalCount = $totalCount + $totalPP;
                $msg = self::msgMethod($msg, $totalPP, 'Procurement Plan', 'Procurement_Plan');
            }
            if ($totalCM > 0) {
                $totalCount = $totalCount + $totalCM;
                $msg = self::msgMethod($msg, $totalCM, 'Contract Management', 'Contract_Management');
            }
            if($totalOrder > 0) {
                $totalCount = $totalCount + $totalOrder;
                $msg = self::msgMethod($msg, $totalOrder, 'Order', 'Order');
            }
            if($totalFL > 0) {
                $totalCount = $totalCount + $totalFL;
                $msg = self::msgMethod($msg, $totalFL, 'Fulfilment', 'Fulfilment');
            }
            if($totalYepOrder > 0) {
                $totalCount = $totalCount + $totalYepOrder;
                $msg = self::msgMethod($msg, $totalYepOrder, 'YEP_Order', 'YEP_Order');
            }
            if($totalYepFulfilment > 0) {
                $totalCount = $totalCount + $totalYepFulfilment;
                $msg = self::msgMethod($msg, $totalYepFulfilment, 'YEP_Fulfilment', 'YEP_Fulfilment');
            }
            if ($totalPhis > 0) {
                $totalCount = $totalCount + $totalPhis;
                $msg = $msg . 

self::$module ." PHIS " .  
self::$total . " " .$totalPhis .
self::$url ." https://epss.eperolehan.gov.my/dashboard/phis";
            }
            if($totalDP > 0) {
                $totalCount = $totalCount + $totalDP;
                $msg = self::msgMethod($msg, $totalDP, 'Sourcing DP', 'SourcingDP');
            }
            if ($totalQT > 0) {
                $totalCount = $totalCount + $totalQT;
                $msg = self::msgMethod($msg, $totalQT, 'Sourcing QT', 'SourcingQT');
            }

            $collect->put('msg', $msg);
            Log::info(self::class . ' Total count bpm stl with running status: ' . $totalCount);

            if ($totalCount > 0) {
                Log::info(self::class . '>> ' . $msg);
                $this->saveNotify('BPM_STL_RUNNING', $collect);
            }
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            \Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
    }

    public function msgMethod($msg, $totalModule, $moduleName, $url) {
        return $msg .   

self::$module .' ' .$moduleName .
self::$total . ' ' .$totalModule .
self::$url . ' ' .self::$basicUrl.'/'.$url             
;
    }

    public function saveNotify($receiver, $collect) {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group'; // 'notify group , 'notify personal
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring bpm stl with running status';
        $nty->save();
    }

}
