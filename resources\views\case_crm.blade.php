@extends('layouts.guest-dash')

@section('cssprivate')
<style type="text/css">
    .popover.top {
        min-width: 600px;
        font-size: 10px;
    }
</style>
@endsection

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/support/crm/case')}}" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="case_number" name="case_number" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>CRM Case<br>
            <small>Masukkan CASE NO pada carian diatas...</small>
        </h1>
    </div>
</div>
<!--start if($case != null)-->
@if($case != null)
<div class="row">
    <div class="col-lg-12">
        <div class="block">
            <input type="hidden" id="case_id" name="case_id" value="{{$case->id}}" />
            <input type="hidden" id="case_no" name="case_no" value="{{$case->case_number}}" />
            <div class="block-title">
                <h2><i class="fa fa-building-o"></i> <strong>CRM</strong> Case</h2>
            </div>
            <div class="alert alert-success alert-dismissable" id="success" style="display: none;">
                <h4><i class="fa fa-check-circle"></i> Success! PIC Specialist Has Been Assigned</h4>
            </div>
            <div class="alert alert-success alert-dismissable" id="failed" style="display: none;">
                <h4><i class="fa fa-check-circle"></i> Failed to Update Case!</h4>
            </div>
            <div class="alert alert-success alert-dismissable" id="successResolution" style="display: none;">
                <h4><i class="fa fa-check-circle"></i> Success! Case Has Been Updated</h4>
            </div>
            @if($status_action && $status_action == 'success')
            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> Success! </h4> Data <a href="javascript:void(0)" class="alert-link">has been saved.</a>!
            </div>
            @elseif($status_action && $status_action == 'failed')
            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> Failed! </h4> Case & Task <a href="javascript:void(0)" class="alert-link">failed to save.</a>!
            </div>
            @endif
            @if($deleteNoteStatus && $deleteNoteStatus == 'success')
            <div class="alert alert-success alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> Success! </h4> Note <a href="javascript:void(0)" class="alert-link">has been deleted.</a>!
            </div>
            @elseif($deleteNoteStatus && $deleteNoteStatus == 'fail')
            <div class="alert alert-danger alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-check-circle"></i> Failed! </h4> Error <a href="javascript:void(0)" class="alert-link">processing note deletion request.</a>!
            </div>
            @endif

            <div class="row">
                <div class="col-lg-6">
                    <div class="block">
                        <div class="block-title">
                            <h2>Case Summary </h2>
                            <small class="pull-right">Created on {{$case->created_date}}, status record {{$case->record_status}}</small>
                        </div>
                        <h4><strong>{{$case->name}}</strong></h4>
                        <table class="table table-borderless table-striped">
                            <tbody>
                                <tr>
                                    <td style="width: 50%;"><strong>Case Number</strong></td>
                                    <td><a target="_blank" href="@if(env('APP_ENV') === 'PRD') https://crm.eperolehan.gov.my/crm/index.php? @elseif(env('APP_ENV') === 'SIT') http://***************/cdccrm-v2/index.php? @else https://crm.eperolehan.gov.my/crm/index.php? @endif module=Cases&action=DetailView&record={{ $case->id }}" title="View Detail Case in CRM">{{ $case->case_number}}</a></td>
                                </tr>
                                <tr>
                                    <td><strong>Account</strong></td>
                                    <td>({{$case->account_code}}) - <a target="_blank" href="{{$case->account_url}}" title="View Detail Account in eP">{{$case->account_name}}</a></td>
                                </tr>
                                <tr>
                                    <td><strong>Status</strong></td>
                                    <td>{{$case->case_status}}</td>
                                </tr>
                                <tr>
                                    <td style="width: 50%;"><strong>Contact Mode</strong></td>
                                    <td><b>{{$case->contact_mode_c}}</b></td>
                                </tr>
                                <tr style="background-color: orange;">
                                    <td style="width: 50%;"><strong>Portal Category</strong></td>
                                    <td><b>{{$case->portal_category_value}}</b></td>
                                </tr>
                                <tr>
                                    <td style="width: 50%;"><strong>Request Type</strong></td>
                                    <td>{{$case->request_type}}</td>
                                </tr>
                                <tr>
                                    <td style="width: 50%;"><strong>Incident/Service Type</strong></td>
                                    <td>{{$case->incident_service_type}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sub Category</strong></td>
                                    <td>{{$case->sub_category}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sub Category 2</strong></td>
                                    <td>{{$case->sub_sub_category}}</td>
                                </tr>

                                <tr>
                                    <td><strong>Redmine No.</strong></td>
                                    <td>{{$case->redmine_number}}</td>
                                </tr>
                                <tr>
                                    <td colspan="2"><strong>Notes / Attachments</strong></td>

                                </tr>
                                <tr>
                                    <td colspan="2">
                                        @if(count($case->filecaseupdated) > 0 || count($case->filecases) > 0 )
                                        <div class="block">
                                            <ul>
                                                @foreach($case->filecaseupdated as $file1)
                                                @if($file1->filename == null || $file1->filename == '')
                                                <li>{{$file1->name}} : {{$file->description}} on {{$file1->date_entered}} <br /></li>
                                                @else
                                                <div style="display: flex;">
                                                    <li><a target="_blank" href="@if(env('APP_ENV') === 'PRD') https://crm.eperolehan.gov.my/crm/index.php? @elseif(env('APP_ENV') === 'SIT') http://***************/cdccrm-v2/index.php? @else https://crm.eperolehan.gov.my/crm/index.php? @endif entryPoint=download&id={{$file1->id}}&type=Notes">                                                            
                                                            <span><strong>Module : {{$file1->parent_type}}</strong></span><br />
                                                            Name : {{$file1->name}} <br />
                                                            Description : {{$file1->description}} <br />
                                                            Created on : {{$file1->date_entered}}
                                                        </a><br />
                                                    </li>
                                                    @if($file1->file_mime_type == 'image/jpeg' || $file1->file_mime_type == 'image/png')
                                                    <button style="margin-left: auto;" type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-{{$file1->id}}">
                                                        Delete
                                                    </button>
                                                    @endif
                                                </div>
                                                <div class="modal fade" id="modal-{{$file1->id}}" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h3 class="modal-title" id="modal-{{$file1->id}}-Label">DELETE NOTE</h3>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p class="h4">Are you sure you want to delete this note? {{$file1->name}} <br>
                                                                    Please make sure to delete the image if it is unrelated to the case
                                                                </p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <form action="/support/crm/case/delete-note-files/{{$case->id}}" method="post" style="margin-top: 1rem; margin-left: auto;">
                                                                    <input type="hidden" name="fileId" value="{{$file1->id}}" />
                                                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                    <button type="submit" class="btn btn-danger">Confirm</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @endif
                                                @endforeach

                                                @foreach($case->filecases as $file2)
                                                @if($file2->filename == null || $file2->filename == '')
                                                <li>{{$file2->name}} : {{$file2->description}} on {{$file2->date_entered}} <br /></li>
                                                @else
                                                <div style="display: flex;">
                                                    <li><a target="_blank" href="@if(env('APP_ENV') === 'PRD') https://crm.eperolehan.gov.my/crm/index.php? @elseif(env('APP_ENV') === 'SIT') http://***************/cdccrm-v2/index.php? @else https://crm.eperolehan.gov.my/crm/index.php? @endif entryPoint=download&id={{$file2->id}}&type=Notes">
                                                            <span><strong>Module : {{$file2->parent_type}}</strong></span><br />
                                                            Name : {{$file2->name}} ( {{$file2->filename}} ) <br />
                                                            Description : {{$file2->description}} <br />
                                                            Created on : {{$file2->date_entered}}
                                                        </a><br />
                                                    </li>
                                                    @if($file2->file_mime_type == 'image/jpeg' || $file2->file_mime_type == 'image/png')
                                                    <button style="margin-left: auto;" type="button" class="btn btn-primary btn-sm" data-toggle="modal" data-target="#modal-{{$file2->id}}">
                                                        Delete
                                                    </button>
                                                    @endif
                                                </div>
                                                <div class="modal fade" id="modal-{{$file2->id}}" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
                                                    <div class="modal-dialog modal-lg" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-header">
                                                                <h3 class="modal-title" id="modal-{{$file2->id}}-Label">DELETE NOTE</h3>
                                                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                                                    <span aria-hidden="true">&times;</span>
                                                                </button>
                                                            </div>
                                                            <div class="modal-body">
                                                                <p class="h4">Are you sure you want to delete this note? {{$file2->name}} <br>
                                                                    Please make sure to delete the image if it is unrelated to the case
                                                                </p>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <form action="/support/crm/case/delete-note-files/{{$case->id}}" method="post" style="margin-top: 1rem; margin-left: auto;">
                                                                    <input type="hidden" name="fileId" value="{{$file2->id}}" />
                                                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                                                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                                                                    <button type="submit" class="btn btn-danger">Confirm</button>
                                                                </form>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                @endif
                                                @endforeach
                                                <br />
                                            </ul>
                                        </div>
                                        @endif

                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="media-body" style="white-space: pre-wrap; display: inline">
                            <a href="javascript:void(0)" class="pull-left"><strong>Problem Description</strong></a>
                            <p style="word-break: break-all">{{$case->description}}</p>
                        </div>
                        <div class="media-body" style="white-space: pre-wrap;">
                            <a href="javascript:void(0)" class="pull-left"><strong>Case Resolution</strong></a>
                            <p class="text-justify" style="word-break: break-all">{{$case->resolution}}</p>
                        </div>
                    </div>
                </div>
                <!--start if($task != null)-->
                @if($task != null)
                <div class="col-lg-6">
                    <div class="block">
                        <div class="block-title">
                            <h2>Latest Task Summary</h2>
                            <small class="pull-right">Created by {{$task->created_user?$task->created_user->user_name:''}} on {{$task->created_date}} || Changed by {{$task->changed_user?$task->changed_user->user_name:''}} on {{$task->changed_date}}</small>
                        </div>
                        <h4><strong>{{$task->name}}</strong></h4>
                        <table class="table table-borderless table-striped">
                            <tbody>
                                <tr>
                                    <td style="width: 50%;"><strong>Task Number</strong></td>
                                    <td><a target="_blank" href="@if(env('APP_ENV') === 'PRD') http://***************/crm/index.php? @else http://***************/cdccrm-v2/index.php? @endif module=Tasks&action=DetailView&record={{ $task->id }}" title="View Detail Task in CRM">{{ $task->task_number_c}}</a></td>                                </tr>
                                <tr>
                                    <td><strong>Task Status</strong></td>
                                    <td>{{$task->task_status}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Resolution Category</strong></td>
                                    <td>{{$task->task_resolution_category}}</td>
                                </tr>
                                @if($case->incident_service_type_c === 'incident_it')
                                <tr>
                                    <td><strong>Incident Classification</strong></td>
                                    <td>{{$task->incident_classification}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Category Factor</strong></td>
                                    <td>{{$task->task_category_factor}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sla Start Date</strong></td>
                                    <td>{{$taskSlaStart}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sla Due Date</strong></td>
                                    <td>{{$taskSlaDue}}</td>
                                </tr>
                                <tr>
                                    <td><strong>Sla Duration</strong></td>
                                    @if($task->sla_task_flag_c && $task->task_severity !== '' && $task->task_severity !== null)
                                    <td>{{$task->sla_task_flag_c}} : {{$taskDuration}}</td>
                                    @else
                                    <td>{{$taskDuration}}</td>
                                    @endif
                                </tr>
                                @elseif($case->request_type_c === 'service' && $case->incident_service_type_c === 'service_it'
                                && ($case->sub_category_2_c === '10713_15506_16641' || $case->sub_category_2_c === '10713_15506_16643')
                                && ($task->assigned_user_id === App\Services\CRMService::$PRODUCTION_SUPPORT || $task->assigned_user_id === App\Services\CRMService::$MIDDLEWARE))
                                <tr>
                                    <td><strong>Proceed eKontrak</strong></td>
                                    <td>{{$task->task_econtract_status}}</td>
                                </tr>
                                @endif
                                <tr>
                                    <td><strong>Task Justification</strong></td>
                                    <td>{{$task->task_task_justification}}</td>
                                </tr>
                                @if(Auth::user()->isSpecialistUsers())
                                <tr>
                                    <td><strong>PIC Specialist</strong></td>
                                    <td>@if(isset($specialistName)){{$specialistName->first_name}}@endif</td>
                                </tr>
                                @endif
                                @if($task->task_econtract_status !== null )
                                <tr>
                                    <td><strong>EKontrak Status</strong></td>
                                    <td>{{ $task->task_econtract_status }}</td>
                                </tr>
                                @endif
                                @if($task->data_change_status !== null )
                                <tr>
                                    <td><strong>Approve Data Change</strong></td>
                                    <td>{{ $task->data_change_status }}</td>
                                </tr>
                                @endif
                            </tbody>
                        </table>
                        <div class="media-body" style="display:block;">
                            <a href="javascript:void(0)"><strong>Doc No</strong></a>
                            <p>{{$case->doc_no}}</p>
                        </div>
                        <div class="media-body" style="white-space: pre-wrap;">
                            <a href="javascript:void(0)" class="pull-left"><strong>Resolution</strong></a>
                            <p style="word-break: break-all">{{$task->resolution_c}}</p>
                        </div>
                        @if(($case->status === 'Open_Resolved' || $case->status === 'Open_Assigned') && $task->status === 'Pending Acknowledgement'
                        && $task->name === 'Assigned to Case Owner')
                        <br />
                        <div class="media-body" style="white-space: pre-wrap;">
                            <input type="button" id="button_update_resolution" name="button_update_resolution" class="btn btn-sm btn-primary" value="Update Resolution">
                        </div>
                        @endif
                    </div>

                    <!--start if($case->sub_category_c !== '10713_15540'-->
                    @if($case->sub_category_c !== '10713_15540'
                    && in_array($case->request_type_c,['service','incident'])
                    && in_array($case->incident_service_type_c,['incident_it','service_it'])
                    && in_array($case->status,['In_Progress','Open_Assigned'])
                    && $task->assigned_user_id!== App\Services\CRMService::$PMO && $task->sla_task_flag_c !== '4'
                    && $task->assigned_user_id!== App\Services\CRMService::$APPROVER
                    && $task->assigned_user_id!== App\Services\CRMService::$EPP1)

                    <div class="block full">
                        <div class="block-title">
                            <h2><i class="fa fa-file-text-o"></i> <strong>Task</strong> Resolution</h2>
                        </div>
                        <!--                        @if($task->reserve_task_userid !== auth()->user()->id)
                        <p>This Task is Reserved By User : <strong>{{ $reservedBy }} </strong></p>
                        @endif-->
                        @if ($errors->any())
                        <div class="alert alert-danger">
                            <ul>
                                @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                        @endif

                        <form id="case_form" name="case_form" class="form-horizontal form-bordered" onsubmit="false">
                            <input type="hidden" id="task_id" name="task_id" value="{{$task->id}}" />
                            {{ csrf_field() }}

                            <!--start if($task->reserve_task_userid === '')-->
                            @if(($task->reserve_task_userid === '' || $task->reserve_task_userid === null) ||
                            ($task->reserve_task_userid !== auth()->user()->id && $task->assign_group_c === 'Group Middleware' && in_array(auth()->user()->id,$groupCRM)))

                            @if(isset($reservedBy))
                            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This Task is Previously Reserved By User : <strong>{{ $reservedBy }}</strong></p>
                            @if(in_array(auth()->user()->id,App\Services\CRMService::$RESETRESERVEUSER))
                            <div class="form-group form-actions form-actions-button text-right" style="margin:15px;">
                                <button type="button" id="button_submit" name="button_submit" class="btn btn-sm btn-primary pull-right" value="reset"><i class="fa fa-check"></i> Reset Reserve</button>&nbsp;&nbsp;&nbsp;
                            </div>
                            @endif
                            @endif

                            @if($case->incident_service_type_c === 'service_it' || ($case->incident_service_type_c === 'incident_it' && $case->sla_flag !== '' && $case->sla_flag !== null))
                            <div class="form-group form-actions form-actions-button text-right" style="margin:15px;">
                                <button type="button" id="button_submit" name="button_submit" class="btn btn-sm btn-primary" value="own"><i class="fa fa-check"></i> Reserve Task</button>&nbsp;&nbsp;&nbsp;
                                @if(auth()->user()->id === '9d3de429-ee11-413e-8f46-a04e8534da44' || auth()->user()->id === '8e1aee12-eb19-4209-b9c2-c327d1cc436f' || auth()->user()->id === 'd9fe2771-2296-4b33-bc10-23abe9cdbc8a')
                                <input type="button" id="button_assign_pic" name="button_assign_pic" class="btn btn-sm btn-primary" value="Assign PIC Specialist">
                                @endif
                            </div>
                            @endif
                            <!--start else if($task->reserve_task_userid === '')-->
                            @elseif($task->reserve_task_userid !== '' && $task->reserve_task_userid === auth()->user()->id)
                            <!--start if($task->status === 'Pending Acknowledgement' && $task->name !== 'Assigned to Case Owner')-->
                            @if($task->status === 'Pending Acknowledgement' && $task->name !== 'Assigned to Case Owner')
                            <div class="form-group form-actions form-actions-button text-right" style="margin:15px;">
                                <button type="button" id="button_submit" name="button_submit" class="btn btn-sm btn-primary" value="acknowledge"><i class="fa fa-check"></i> Acknowledge Task</button>&nbsp;&nbsp;&nbsp;
                                @if(auth()->user()->id === '9d3de429-ee11-413e-8f46-a04e8534da44' || auth()->user()->id === '8e1aee12-eb19-4209-b9c2-c327d1cc436f' || auth()->user()->id === 'd9fe2771-2296-4b33-bc10-23abe9cdbc8a')
                                <input type="button" id="button_assign_pic" name="button_assign_pic" class="btn btn-sm btn-primary" value="Assign PIC Specialist">
                                @endif
                            </div>
                            <!--start else if($task->status === 'Pending Acknowledgement' && $task->name !== 'Assigned to Case Owner')-->
                            @else
                            <div id="notify_resolution" class="alert alert-info">
                                <i class="fa fa-fw fa-info-circle"></i> This resolution will create a new task to Case Owner. Case will update Resolved.
                            </div>
                            <?php $now = Carbon\Carbon::now()->format("Y-m-d H:i"); ?>
                            <div class="form-group">
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="col-md-3 control-label" for="user_group">Assign Task</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="user_group" name="user_group" class="form-control" required="true">
                                                        <option value="">Please select</option>
                                                        <!-- Initial Task can only be assigned to Group Production Support -->
                                                        @if($task->assigned_user_id == App\Services\CRMService::$IT_COORDINATOR_ID && $case->incident_service_type_c === 'incident_it')
                                                        <option value="Group Production Support">Group Production Support </option>
                                                        <!-- RIT Task (Prod Support) must assign to specialist (severity) first before resolve case -->
                                                        @elseif($task->assigned_user_id == App\Services\CRMService::$PRODUCTION_SUPPORT && $task->sla_task_flag_c == '3' && $case->incident_service_type_c === 'incident_it')
                                                            @foreach(App\Services\CRMService::$CRM_GROUP_SPECIALIST as $key => $obj)
                                                            <option value="{{$key}}">{{$obj['name']}}</option>
                                                            @endforeach
                                                        <!-- Incident Task that already have severity can only assign task to PMO or case owner-->
                                                        @elseif($case->incident_service_type_c === 'incident_it' && $task->task_severity !== '' && $task->task_severity !== null) 
                                                            @foreach(App\Services\CRMService::$CRM_GROUP_CASE_OWNER_PMO as $key=> $obj)
                                                            <option value="{{$key}}">{{$obj['name']}}</option>
                                                            @endforeach
                                                        <!-- Incident Task that already burst can only be assigned to case owner. Extra duration can be apply through crm-->
                                                        @elseif($case->incident_service_type_c === 'incident_it' && $task->task_severity !== '' && $task->task_severity !== null && $taskSlaDue <= $now) 
                                                            @foreach(App\Services\CRMService::$CRM_GROUP_CASE_OWNER as $key=> $obj)
                                                            <option value="{{$key}}">{{$obj['name']}}</option>
                                                            @endforeach
                                                        @else
                                                            @foreach(App\Services\CRMService::$CRM_GROUP_USER as $key => $obj)
                                                            <option value="{{$key}}">{{$obj['name']}}</option>
                                                            @endforeach
                                                        @endif
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <!-- 1. Allow IT Incident/IT Service Only to be update in EPSS.. 
                                         2. Do not allow Group PMO to update task in EPSS 
                                         3. Sub Category Server Maintenance & Configuration can only be update in CRM
                                            -->

                                        <!--start if($case->sub_category_c !== '10713_15540' && ($case->incident_service_type_c === 'incident_it' || $case->incident_service_type_c === 'service_it'))-->
                                        @if($case->sub_category_c !== '10713_15540' && ($case->incident_service_type_c === 'incident_it' || $case->incident_service_type_c === 'service_it'))
                                        <div class="form-group" id="panel_resolution_category_form" style="display:block;">
                                            <label class="col-md-3 control-label" for="resolution_category">Resolution Category</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="resolution_category" name="resolution_category" class="form-control" required="true">
                                                        <option value="">Please Select</option>
                                                        @foreach($resolutionCategoryList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->resolution_category == $data->value_code) selected @endif>{{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>
                                                    <span class="input-group-addon" data-placement="top" tabindex="0" role="button" data-trigger="focus" data-html="true" data-toggle="popover" data-content="
                                                              <table class='table table-borderless table-striped'>
                                                              <tr><th>Name</th><th>Description</th></tr>
                                                              <tr><td>Data Fix - System Problem </td><td>Resolution that has only datapatch due to system problem.</td></tr>
                                                              <tr><td>Data Fix - User Request </td><td>Resolution that has only datapatch requested by user. Normally the request comes with MOF endorsement.</td></tr>
                                                              <tr><td>Profile Maintenance </td><td>Resolution that related to profile maintenance and doesn't involve datapatch (example: liferay account deactivation). Change email, reset password and anything related to certification authority (CA) request.</td></tr>
                                                              <tr><td>Program Fix </td><td>Resolution that has only program fix deployment. Normally this type of resolution applicable to problem without interim solution.</td></tr>
                                                              <tr><td>System Configuration </td><td>Resolution that related to system configuration. BPM related stuck / resume / refire task, molpay issue and network whitelist.</td></tr>
                                                              <tr><td>User Familiarity </td><td>Resolation that only involve explanation due to user limited understanding on certain issue. </td></tr>
                                                              <tr><td>Lampiran A - User Reques </td><td>Resolation that only involve eKontrak Lampiran A approval. </td></tr>
                                                              <tr><td>Lampiran C - User Request </td><td>Resolation that only involve eKontrak Lampiran C approval. </td></tr>
                                                              </table>"><i class="gi gi-circle_info"></i></span>
                                                </div>
                                                <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                                            </div>
                                        </div>
                                    </div>
                                    <!--start if($task->assigned_user_id !== App\Services\CRMService::$IT_COORDINATOR_ID && (strpos($caseTask->name, 'Initial Task') === false))-->
                                    @if($task->assigned_user_id !== App\Services\CRMService::$IT_COORDINATOR_ID && (strpos($caseTask->name, 'Initial Task') === false))

                                    <div class="col-lg-6">
                                        <div class="form-group" id="panel_severity">
                                            <label class="col-md-3 control-label" for="severity">Severity</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="severity" name="severity" class="form-control" required="false">
                                                        <option value="">Please select</option>
                                                        @foreach($slaSeverityList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->task_severity == $data->value_code) selected @endif>{{$data->value_code}} : {{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--end if($task->assigned_user_id !== App\Services\CRMService::$IT_COORDINATOR_ID && (strpos($caseTask->name, 'Initial Task') === false))-->
                                    @endif
                                    <!--end if($case->sub_category_c !== '10713_15540' && ($case->incident_service_type_c === 'incident_it' || $case->incident_service_type_c === 'service_it'))-->
                                    @endif
                                </div>
                                <!--start if($case->incident_service_type_c === 'incident_it')-->
                                @if($case->incident_service_type_c === 'incident_it')
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <div class="form-group" id="panel_incident_classification" style="display:block;">
                                            <label class="col-md-3 control-label" for="incident_classification">Incident Classification</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="incident_classification" name="incident_classification" class="form-control" required="true">
                                                        <option value="">Please Select</option>
                                                        <option value="notApplicable">Not Applicable</option>
                                                        <option value="internal">Internal</option>
                                                        <option value="normal" selected>Normal</option>
                                                        <option value="major">Major</option>
                                                        <option value="security">Security</option>
                                                    </select>
                                                    <span class="input-group-addon" data-placement="top" tabindex="0" role="button" data-trigger="focus" data-html="true" data-toggle="popover" data-content="
                                                              <table class='table table-borderless table-striped'>
                                                              <tr><th>Name</th><th>Description</th></tr>
                                                              <tr><td>Not Applicable </td><td>Default value when Incident is NOT IT Incident</td></tr>
                                                              <tr><td>Internal </td><td>No description yet</td></tr>
                                                              <tr><td>Normal </td><td>Default value when Incident is IT Incident</td></tr>
                                                              <tr><td>Major </td><td>No description yet</td></tr>
                                                              <tr><td>Security </td><td>No description yet</td></tr>
                                                              </table>"><i class="gi gi-circle_info"></i></span>

                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <div class="form-group" id="panel_category_factor" style="display:block;">
                                            <label class="col-md-3 control-label" for="category_factor">Category Factor</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="category_factor" name="category_factor" class="form-control" required="true">
                                                        <option value="">Please Select</option>
                                                        @foreach($taskFactorList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->category_factor_c == $data->value_code) selected @endif>{{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>
                                                    <span class="input-group-addon" data-placement="top" tabindex="0" role="button" data-trigger="focus" data-html="true" data-toggle="popover" data-content="
                                                              <table class='table table-borderless table-striped'>
                                                              <tr><th>Name</th><th>Description</th></tr>
                                                              <tr><td>External Factor </td><td>Applicable to external factor such as user missunderstanding, user connectivity issue or 3rd party integration)</td></tr>
                                                              <tr><td>Internal Factor </td><td>Applicable to internal factor such as server error, database locking or BPM issue</td></tr>
                                                              </table>"><i class="gi gi-circle_info"></i></span>
                                                </div>
                                                <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="form-group" id="panel_incident_factor" style="display:none;">
                                            <label class="col-md-3 control-label" for="incident_factor">Incident Category</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="incident_factor" name="incident_factor" class="form-control" required="true">
                                                        <option value="">Please Select</option>
                                                        @foreach($combineCategoryList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->tasks_combine_c == $data->value_code) selected @endif>{{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>
                                                    <span class="input-group-addon" data-placement="top" tabindex="0" role="button" data-trigger="focus" data-html="true" data-toggle="popover" data-content="
                                                              <table class='table table-borderless table-striped'>
                                                              <tr><th>Name</th><th>Description</th></tr>
                                                              <tr><td>IT SERVICE REQUEST / CHANGE REQUEST </td><td>Requested by user</td></tr>
                                                              <tr><td>NORMAL INCIDENT </td><td>Incident or issue that normally happen</td></tr>
                                                              <tr><td>SERVICE DOWN - CORE MODULE </td><td>Use normal incident instead</td></tr>
                                                              <tr><td>SERVICE DOWN - CUSTOMER SUPPORT MANAGEMENT (CONTACT CENTRE) </td><td>Use normal incident instead</td></tr>
                                                              <tr><td>SERVICE DOWN - SUPPORTING MODULES </td><td>Use normal incident instead</td></tr>
                                                              <tr><td>SYSTEM SECURITY / DATA INTEGRITY </td><td>Use normal incident instead. This category have a financial implication to the company. Do not use.</td></tr>
                                                              </table>"><i class="gi gi-circle_info"></i></span>
                                                </div>
                                                <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--start else if($case->incident_service_type_c === 'incident_it')-->
                                @elseif($case->request_type_c === 'service' && $case->incident_service_type_c === 'service_it'
                                && ($case->sub_category_2_c === '10713_15506_16641' || $case->sub_category_2_c === '10713_15506_16643')
                                && ($task->assigned_user_id === App\Services\CRMService::$PRODUCTION_SUPPORT || $task->assigned_user_id === App\Services\CRMService::$MIDDLEWARE))
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <div class="form-group" id="panel_status_ek" style="display:block;">
                                            <label class="col-md-3 control-label" for="severity">Proceed eKontrak</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="status_ek" name="status_ek" class="form-control" required="false">
                                                        <option value="">Please select</option>
                                                        @foreach($statusEkList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->econtract_status == $data->value_code) selected @endif>{{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>

                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--end if($case->incident_service_type_c === 'incident_it')-->
                                @endif
                                <div class="col-lg-12">
                                    <div class="col-lg-6">
                                        <div class="form-group">
                                            <label class="col-md-3 control-label" for="task_justification">Task Justification</label>
                                            <div class="col-md-9">
                                                <div class="input-group">
                                                    <select id="task_justification" name="task_justification" class="form-control" required="true">
                                                        <option value="">Please Select</option>
                                                        @foreach($taskJustificationList as $data)
                                                        <option value="{{$data->value_code}}" @if($task->task_justification == $data->value_code) selected @endif>{{$data->value_name}}</option>
                                                        @endforeach
                                                    </select>
                                                    <span class="input-group-addon" data-placement="top" tabindex="0" role="button" data-trigger="focus" data-html="true" data-toggle="popover" data-content="
                                                              <table class='table table-borderless table-striped'>
                                                              <tr><th>Name</th><th>Description</th></tr>
                                                              <tr><td>Human </td><td>Issue happen due to user limited understanding</td></tr>
                                                              <tr><td>Technical </td><td>Issue happen due to technical or system problem</td></tr>
                                                              <tr><td>Technical Digicert </td><td>Issue happen due to softcert integration with Digicert</td></tr>
                                                              <tr><td>Technical Trusgate </td><td>Issue happen due to softcert integration with Trusgate</td></tr>
                                                              </table>"><i class="gi gi-circle_info"></i></span>
                                                </div>
                                                <h5 class="text-info bolder" id="selected_process_id_desc"></h5>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div id="new_redmine_no_panel_form" class="col-md-12" style="display:none;">
                                            <div class="form-group">
                                                <input type="number" id="new_redmine_no" name="new_redmine_no" class="form-control" placeholder="Redmine No..">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <textarea id="new_task_resolution" name="new_task_resolution" class="form-control" rows="4" placeholder="Your resolution.." minlength="10" required="true"></textarea>
                                    </div>
                                </div>
                                <div class="form-group form-actions form-actions-button text-right" style="margin:15px;">
                                    <button type="button" id="button_submit" name="button_submit" class="btn btn-sm btn-primary" value="assign-task"><i class="fa fa-check"></i> Assign Task</button>&nbsp;&nbsp;&nbsp;
                                    @if(auth()->user()->id === '9d3de429-ee11-413e-8f46-a04e8534da44' || auth()->user()->id === '8e1aee12-eb19-4209-b9c2-c327d1cc436f' || auth()->user()->id === 'd9fe2771-2296-4b33-bc10-23abe9cdbc8a')
                                    <input type="button" id="button_assign_pic" name="button_assign_pic" class="btn btn-sm btn-primary" value="PIC Specialist">
                                    @endif
                                </div>
                            </div>
                            <!--end if($task->status === 'Pending Acknowledgement' && $task->name !== 'Assigned to Case Owner')-->
                            @endif
                            <!--start else if($task->reserve_task_userid === '')-->
                            @else
                            <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;This Task is Reserved By User : <strong>{{ $reservedBy }} </strong></p>
                            @if(in_array(auth()->user()->id,App\Services\CRMService::$RESETRESERVEUSER))
                            <div class="form-group form-actions form-actions-button text-right" style="margin:15px;">
                                <button type="button" id="button_submit" name="button_submit" class="btn btn-sm btn-primary pull-right" value="reset"><i class="fa fa-check"></i> Reset Reserve</button>&nbsp;&nbsp;&nbsp;
                            </div>
                            @endif
                            <!--end if($task->reserve_task_userid === '')-->
                            @endif
                        </form>

                    </div>
                    <!--end if($case->sub_category_c !== '10713_15540'-->
                    @endif
                </div>
                <div id="modal_assign_pic" class="modal fade">
                    <div class="modal-dialog modal-md">
                        <div class="modal-content">
                            <div class="modal-header text-left">
                                @if($task != null)
                                <h5> Assign pic specialist for task <strong>{{ $task->task_number_c}}</strong></h5>
                                <input type="hidden" id="taskId" name="taskId" value="{{$task->id}}">
                                <h5 id="history_task_id"></h5>
                                @endif
                            </div>
                            <div class="modal-body">
                                <label>Specialist : </label> &nbsp;&nbsp;&nbsp;
                                <select id="specialist_name" name="specialist_name" class="select2" style="width: 60%">
                                    @foreach($specialistUsers as $value)
                                    <option id="{{$value->userid}}" value="{{$value->firstname}}">{{$value->firstname}}</option>
                                    @endforeach
                                </select>
                            </div>
                            <br /><br />
                            <div class="modal-footer">
                                <button type="button" id="submit_assign_pic" class="btn btn-sm btn-info">Assign</button>
                                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="modal_confirm_resolve" class="modal fade">
                    <div class="modal-dialog modal-sm">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                <h5> RESOLVE CASE <strong>{{ $carian }}</strong></h5>
                            </div>
                            <div class="modal-body text-center">
                                <label>Adakah Anda Pasti Untuk Resolve Kes Ini? </label> &nbsp;&nbsp;&nbsp;
                            </div>
                            <br /><br />
                            <div class="modal-footer">
                                <button type="button" id="submit_confirm_resolve" name="submit_confirm_resolve" class="btn btn-sm btn-info pull-left">YA</button>
                                <button type="button" id="cancel_submit_resolve" name="cancel_submit_resolve" data-dismiss="modal" class="btn btn-sm btn-default">TIDAK</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="modal_update_resolution" class="modal fade">
                    <div class="modal-dialog modal-md">
                        <div class="modal-content">
                            <div class="modal-header text-center">
                                @if(isset($case) && isset($task))
                                <input type="hidden" id="case_id" name="case_id" value="{{$case->id}}" />
                                <input type="hidden" id="task_id" name="task_id" value="{{$task->id}}" />
                                @endif
                                <h5> UPDATE CASE <strong>{{ $carian }}</strong></h5>
                            </div>
                            <div class="modal-body text-center">
                                <label>Resolution : </label> &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <textarea id="old_resolution" name="old_resolution" style="width: 70%;height: 70%;" readonly="true"> {{$case->resolution}} </textarea> <br />
                                <label>New Resolution : </label> &nbsp;&nbsp;&nbsp;
                                <textarea id="new_resolution" name="new_resolution" style="width: 70%;height: 70%;" required="true"> {{$case->resolution}} </textarea> <br />
                            </div>
                            <br /><br />
                            <div class="modal-footer">
                                <button type="button" id="submit_confirm_resolution" class="btn btn-sm btn-info pull-left">UPDATE</button>
                                <button type="button" data-dismiss="modal" class="btn btn-sm btn-default">CANCEL</button>
                            </div>
                        </div>
                    </div>
                </div>
                <!--end if($task != null)-->
                @endif
            </div>
        </div>
    </div>
</div>
<!--end if($case != null)-->
@endif
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    $(document).ready(function() {
        $('.select2').select2();
    });
</script>
<script>
    $(function() {
        TablesDatatables.init();
    });
</script>
<script>
    $("form#carianform").on("submit", function() {
        var gourl = $(this).attr('action') + '?case_number=' + $('#cari').val();
        $(this).attr('action', gourl);
    });
</script>
<script>
    $(document).ready(function() {

        $('#button_submit').on('click', function() {

            var valid = $("#case_form").valid();
            console.log('valid ' + valid);
            var csrf = $("input[name=_token]").val();
            var submitBtnValue = $('#button_submit').val();
            var caseId = $("input[name=case_id]").val();
            var caseNo = $("input[name=case_no]").val();
            var taskId = $("input[name=task_id]").val();
            var usergroup = $("#user_group option:selected").attr("value");
            var resolutionCategory = $("#resolution_category option:selected").attr("value");
            var categoryFactor = $("#category_factor option:selected").attr("value");
            var incidentFactor = $("#incident_factor option:selected").attr("value");
            var taskJustification = $("#task_justification option:selected").attr("value");
            var severity = $("#severity option:selected").attr("value");
            var resolution = $('textarea#new_task_resolution').val();
            var redmineNo = $("input[name=new_redmine_no]").val();
            var statusEk = $("#status_ek option:selected").attr("value");
            var incidentClassification = $("#incident_classification option:selected").attr("value")
            console.log(statusEk);

            if (submitBtnValue === 'own' || submitBtnValue === 'reset' || submitBtnValue === 'acknowledge') {
                submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk, incidentClassification);
                //            }
                //            else if( === 'reset')
                //                submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk);
                //            }else if (submitBtnValue === 'acknowledge') {
                //                submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk);
            } else {
                if (valid === true) {
                    if (usergroup === 'Group Business Coordinator') {
                        console.log('resolve');
                        submitBtnValue = 'resolve';
                        $('#modal_confirm_resolve').modal('show');

                        $('#cancel_submit_resolve').on('click', function() {
                            setTimeout(location.reload.bind(location), 1000);
                        });

                        $('#submit_confirm_resolve').on('click', function() {
                            console.log('ok');
                            $('#modal_confirm_resolve').modal('hide');
                            submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk, incidentClassification);
                        });

                    } else {
                        console.log('reassigned');
                        submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk, incidentClassification);
                    }
                }
            }

        });
    });

    function submitForm(csrf, caseId, caseNo, taskId, submitBtnValue, usergroup, resolutionCategory, categoryFactor, incidentFactor, taskJustification, severity, resolution, redmineNo, statusEk, incidentClassification) {
        document.getElementById("button_submit").disabled = true;
        console.log('submitForm ' + submitBtnValue);
        $.ajax({
            type: "POST",
            url: "/support/crm/case/update/" + caseId,
            data: {
                "_token": csrf,
                "caseNo": caseNo,
                "taskId": taskId,
                "submitBtnValue": submitBtnValue,
                "usergroup": usergroup,
                "resolutionCategory": resolutionCategory,
                "categoryFactor": categoryFactor,
                "incidentFactor": incidentFactor,
                "taskJustification": taskJustification,
                "severity": severity,
                "resolution": resolution,
                "redmineNo": redmineNo,
                "statusEk": statusEk,
                "incidentClassification": incidentClassification
            }
        }).done(function(resp) {
            console.log(resp.status);
            if (resp.status === 'success') {
                $('#successResolution').show();
                setTimeout(location.reload.bind(location), 1000);
            } else {
                $('#failed').show();
            }
        });
    }
</script>
<script>
    $('#panel_severity').hide();
    $("[name='severity']").prop("required", false);
    $("#user_group").bind("change", function() {

        var usergroup = $(this).find(":selected").val();
        console.log('usergroup', usergroup);

        var reqType = "<?php
                        if (isset($case->incident_service_type_c)) {
                            echo $case->incident_service_type_c;
                        }
                        ?>";

        if (reqType === 'incident_it') {

            if (usergroup === 'Group Business Coordinator') {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Case Owner. Case will update Resolved.");
                $('#button_submit').html("<i class='fa fa-check'></i> Resolve Case");
                $('#button_submit').val("resolve");

                $('#panel_severity').hide();
                $("[name='severity']").prop("required", false);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html("Mohon pengguna untuk semak semula. Jika masih memerlukan maklum balas berkenaan penyelesaian yang diberikan, sila pilih butang SAH & TUTUP, dan kemudian log kes eAduan yang baharu dengan menyatakan nombor kes asal serta muat naik tangkap layar atau dokumen yang berkaitan. Manakala, sekiranya ralat/masalah masih berulang, sila pilih butang DITOLAK & TUTUP, dan kemudian log kes eAduan yang baharu dengan menyatakan nombor kes asal serta muat naik tangkap layar atau dokumen yang berkaitan. Sekiranya tiada maklumbalas selepas 7 hari, kes ini akan ditutup. Terima kasih. \n\
                        \n\
Kindly need user to verify. If still having enquiry with the resolution, kindly choose VERIFIED & CLOSED and log a new case by mentioning this case number and upload pertaining screen shot or document. Otherwise, if error/issue persist, kindly choose REJECT & CLOSED and log a new case by mentioning this case number and upload pertaining screen shot or document. If there is no feedback after 7 days, this case will be closed. Thank you.");
            } else if (usergroup === 'Group PMO') {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Build Team.");
                $('#button_submit').html("<i class='fa fa-arrow-right'></i> Assign Task");
                $('#button_submit').val("assign-task");

                $('#panel_severity').show();
                $("[name='severity']").prop("required", true);
                //As per mutual agreement with PMO, task assigend to PMO will be set default to severity 1 (1 days) -- 29/06/2020
                $('#severity').val("s5");
                $("select option[value*='']").prop('disabled', true);
                $("select option[value*='s5']").prop('disabled', false);
                $("select option[value*='s1']").prop('disabled', true);
                $("select option[value*='s2']").prop('disabled', true);
                $("select option[value*='s3']").prop('disabled', true);

                $('#new_redmine_no_panel_form').show();
                $("[name='new_redmine_no']").prop("required", true);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html('');
            } else {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Specialist Group.");
                $('#button_submit').html("<i class='fa fa-arrow-right'></i> Assign Task");
                $('#button_submit').val("assign-task");

                $('#panel_severity').show();
                $("[name='severity']").prop("required", true);
                $('#severity').val("s1");
                $("select option[value*='s1']").prop('disabled', false);
                $("select option[value*='s2']").prop('disabled', false);
                $("select option[value*='s3']").prop('disabled', false);
                $("select option[value*='s5']").prop('disabled', true);

                $('#new_redmine_no_panel_form').show();
                $("[name='new_redmine_no']").prop("required", false);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html('');
            }
        } else {
            $('#panel_severity').hide();
            $("[name='severity']").prop("required", false);
            $('#severity').val("s1");
            $("select option[value*='s1']").prop('disabled', false);
            $("select option[value*='s2']").prop('disabled', false);
            $("select option[value*='s3']").prop('disabled', false);
            $("select option[value*='s5']").prop('disabled', true);

            if (usergroup === 'Group Business Coordinator') {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Case Owner. Case will update Resolved.");
                $('#button_submit').html("<i class='fa fa-check'></i> Resolve Case");
                $('#button_submit').val("resolve");

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html("Mohon pengguna untuk semak semula. Jika masih memerlukan maklum balas berkenaan penyelesaian yang diberikan, sila pilih butang SAH & TUTUP, dan kemudian log kes eAduan yang baharu dengan menyatakan nombor kes asal serta muat naik tangkap layar atau dokumen yang berkaitan. Manakala, sekiranya ralat/masalah masih berulang, sila pilih butang DITOLAK & TUTUP, dan kemudian log kes eAduan yang baharu dengan menyatakan nombor kes asal serta muat naik tangkap layar atau dokumen yang berkaitan. Sekiranya tiada maklumbalas selepas 7 hari, kes ini akan ditutup. Terima kasih. \n\
                        \n\
Kindly need user to verify. If still having enquiry with the resolution, kindly choose VERIFIED & CLOSED and log a new case by mentioning this case number and upload pertaining screen shot or document. Otherwise, if error/issue persist, kindly choose REJECT & CLOSED and log a new case by mentioning this case number and upload pertaining screen shot or document. If there is no feedback after 7 days, this case will be closed. Thank you.");

            } else if (usergroup === 'Group PMO') {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Specialist Group.");
                $('#button_submit').html("<i class='fa fa-arrow-right'></i> Assign Task");
                $('#button_submit').val("assign-task");

                $('#new_redmine_no_panel_form').show();
                $("[name='new_redmine_no']").prop("required", true);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html('');
            } else if (usergroup === 'Group ePP1') {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Group ePP1.");
                $('#button_submit').html("<i class='fa fa-arrow-right'></i> Assign Task");
                $('#button_submit').val("assign-task");

                $('#panel_severity').hide();
                $("[name='severity']").prop("required", false);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html('');
            } else {
                $('#notify_resolution').html("<i class='fa fa-fw fa-info-circle'></i> This resolution will create a new task to Specialist Group.");
                $('#button_submit').html("<i class='fa fa-arrow-right'></i> Assign Task");
                $('#button_submit').val("assign-task");

                $('#new_redmine_no_panel_form').hide();
                $("[name='new_redmine_no']").prop("required", false);

                $("[name='incident_factor']").prop("required", false);
                categoryFactor();
                $('#new_task_resolution').html('');
            }
        }
    });

    $("#category_factor").bind("change", function() {
        var category_factor = $(this).find(":selected").val();

        if (category_factor === 'internal_factor') {
            console.log('internal');
            $('#panel_incident_factor').show();
            $("[name='incident_factor']").prop("required", true);
        } else {
            console.log('external');
            $('#panel_incident_factor').hide();
            $("[name='incident_factor']").prop("required", false);
        }
    });

    function categoryFactor() {
        $("#category_factor").bind("change", function() {
            var category_factor = $(this).find(":selected").val();

            if (category_factor === 'internal_factor') {
                $('#panel_incident_factor').show();
                $("[name='incident_factor']").prop("required", true);
            } else {
                $('#panel_incident_factor').hide();
                $("[name='incident_factor']").prop("required", false);
            }

        });
    }
</script>
<script type="text/javascript">
    var incidentFactor = "<?php
                            if (isset($task->task_incident_factor)) {
                                echo $task->task_incident_factor;
                            }
                            ?>";

    if (incidentFactor !== '') {
        $('#panel_incident_factor').show();
        $("[name='incident_factor']").prop("required", true);
    }

    $('#button_assign_pic').on('click', function() {
        $('#modal_assign_pic').modal('show');
    });
    $('#submit_assign_pic').on('click', function() {

        var taskid = $("input[name=taskId]").val();
        var userid = $("#specialist_name option:selected").attr("id");
        var firstname = $("#specialist_name option:selected").attr("value");
        var csrf = $("input[name=_token]").val();

        $.ajax({
            type: "POST",
            url: "/support/crm/case/assignpic/" + userid,
            data: {
                "_token": csrf,
                "userid": userid,
                "firstname": firstname,
                "taskid": taskid
            }
        }).done(function(resp) {
            $('#modal_assign_pic').modal('hide');
            if (resp.status === 'Success') {
                $('#success').show();
                setTimeout(location.reload.bind(location), 3000);
            }
        });


    });

    $('#button_update_resolution').on('click', function() {
        $('#modal_update_resolution').modal('show');
    });

    $('#submit_confirm_resolution').on('click', function() {

        var caseId = $("input[name=case_id]").val();
        var taskId = $("input[name=task_id]").val();
        var oldResolution = $('textarea#old_resolution').val();
        var newResolution = $('textarea#new_resolution').val();
        var csrf = $("input[name=_token]").val();

        $.ajax({
            type: "POST",
            url: "/support/crm/case/updateResolution/" + caseId,
            data: {
                "_token": csrf,
                "taskId": taskId,
                "oldResolution": oldResolution,
                "newResolution": newResolution
            }
        }).done(function(resp) {
            $('#modal_update_resolution').modal('hide');
            if (resp.status === 'Success') {
                $('#successResolution').show();
                setTimeout(location.reload.bind(location), 3000);
            }
        });
    });
</script>
@endsection