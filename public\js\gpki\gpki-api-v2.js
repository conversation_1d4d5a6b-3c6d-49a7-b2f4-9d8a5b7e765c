/**
 * Agent 3.0
 *
 * Ultimate API
 */

/**
 * hostSTR : Address of GPKI Agent's servlet
 * hostOTP : Address of GPKI Roaming OTP server
 * hostBroker : Address of GPKI Broker server
 *
 * host prod : https://mygpki.gov.my/gpki_roaming_ws/ , https://mygpki.gov.my/gpki_broker/
 */
var hostSTR = "https://127.0.0.1:8441/";
var hostOTP = "https://mygpki.gov.my/gpki_roaming_ws/";
var hostBroker = "https://mygpki.gov.my/gpki_broker/";

/**
 * The time for Agent to response
 * @type Number
 */
var transaction_timeout = 60000;
var url_timeout = 60000;

/**
 * Gpki Class to add functions to GPKI-Agent API
 * @type GpkiClass
 */
var Gpki = {
  /**
   * Loading JS file and scripts
   * @param {type} src address of file
   * @param {type} callback The function that will be running after a successfull loading
   * @returns {undefined}
   */
  load: function (src, callback) {
    var script = document.createElement("script"),
      loaded;
    script.setAttribute("src", src);
    if (callback) {
      script.onreadystatechange = script.onload = function () {
        if (!loaded) {
          callback();
        }
        loaded = true;
      };
    }
    document.getElementsByTagName("head")[0].appendChild(script);
  },
  /**
   * Run an Gpki specified function
   * @param {type} src address of function
   * @param {type} callback function to be run after a successful calling
   * @param {type} failedcallback function to be run after failing
   * @returns {undefined} Not applicable
   *function(src,callback,failedcallback)
   */
  runScript: function (src, data, callback, failedcallback, method) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,

        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          parseSignResult(jqxhr.responseText);

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          //else if (textStatus == "abort"){
          //Gpki.alert("Aborting reque");
          //}
          else {
            //Gpki.alert("A "+textStatus+" error hass happened. \nPlease try again later.");
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    } else {
      jqxhr = $.ajax({
        url: src,
        type: "GET",
        cache: false,
        async: false,
        dataType: "script",
        statusCode: {
          404: function () {
            Gpki.alert("page not found");
          },
        },
        timeout: transaction_timeout,
      })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXHR, textStatus) {
          console.log(jqXHR);
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          //else if (textStatus == "abort"){
          //Gpki.alert("Aborting reque");
          //}
          else {
            //Gpki.alert("A "+textStatus+" error hass happened. \nPlease try again later.");
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  runScriptRequestMedium: function (
    src,
    data,
    callback,
    failedcallback,
    method
  ) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,
        contentType: "application/json; charset=utf-8",
        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          // Gpki.alert(jqxhr.responseText);
          parseResultMedium(jqxhr.responseText); // for rotp

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  runScriptRequestOtp: function (src, data, callback, failedcallback, method) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,
        contentType: "application/json; charset=utf-8",
        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          // Gpki.alert(jqxhr.responseText);
          parseTxResultRequestOTP(jqxhr.responseText); // for rotp

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  runScriptSigningRoaming: function (
    src,
    data,
    callback,
    failedcallback,
    method
  ) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,
        contentType: "application/json; charset=utf-8",
        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          parseTxResultSigningRoaming(jqxhr.responseText); // for rotp

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  runScriptSigningRoamingNext: function (
    src,
    data,
    callback,
    failedcallback,
    method
  ) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,
        contentType: "application/json; charset=utf-8",
        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          parseTxResultSigningRoamingNext(jqxhr.responseText); // for rotp

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  runScriptVerifyRoaming: function (
    src,
    data,
    callback,
    failedcallback,
    method
  ) {
    $.ajaxSetup({ cache: false });
    Gpki.start();
    $("#spacer").ajaxError(function (e, jqxhr, settings, exception) {
      if (settings.dataType == "script") {
        Gpki.alert("Error in parsing data" + exception);
      }
    });

    var jqxhr;
    var reqURLTimeout = setTimeout(function () {
      Gpki.end();
      Gpki.alert(
        "Keputusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
      );
      if (jqxhr != null) {
        jqxhr.abort();
      }
    }, url_timeout);
    if (method == "POST") {
      jqxhr = $.ajax({
        url: src,
        method: "POST",
        type: "POST",
        cache: false,
        async: false,
        dataType: "json",
        data: data,
        contentType: "application/json; charset=utf-8",
        timeout: transaction_timeout,
      })
        .success(function (data, textStatus, jqxhr) {
          parseTxResultVerifyRoaming(jqxhr.responseText); // for rotp

          if (callback) {
            callback();
          }
        })
        .done(function () {
          if (callback) {
            callback();
          }
        })
        .fail(function (jqXhr, textStatus, errorThrown) {
          if (textStatus == "timeout") {
            Gpki.alert(
              "Keptusan tidak diterima dalam tempoh yang ditetapkan. Sila pastikan agen anda telah diaktifkan dan cuba lagi."
            );
          }
          if (failedcallback) {
            failedcallback();
          }
        })
        .always(function () {
          clearTimeout(reqURLTimeout);
          Gpki.end();
        });
    }
  },

  /*
   * Shows loading bar
   * @param {String()} message message that should be shown while waiting, it is "Please Wait..." by defualt
   * @returns {undefined}
   */
  start: function (message) {
    const modalHtml = `
      <div class="modal fade" id="gpkiLoadingModal" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog">
        <div class="modal-dialog modal-dialog-centered" role="document">
          <div class="modal-content">
            <div class="modal-body text-center">
              <div class="spinner-border text-primary mb-3" role="status">
                <span class="sr-only">Loading...</span>
              </div>
              <p class="mb-0">${message || "Please Wait..."}</p>
            </div>
          </div>
        </div>
      </div>
    `;

    // Remove existing modal if present
    $("#gpkiLoadingModal").remove();

    // Add new modal to page-content
    const pageContent = document.getElementById("page-content");
    pageContent.insertAdjacentHTML("beforeend", modalHtml);
    $("#gpkiLoadingModal").modal("show");
  },

  /*
   * Hides loading bar
   * @returns {undefined}
   */
  end: function () {
    const modal = $("#gpkiLoadingModal");
    modal.modal("hide");

    // Remove modal from DOM after it's hidden
    modal.on("hidden.bs.modal", function () {
      modal.remove();
    });
  },

  alert: function (message) {
    setTimeout(function () {
      let existingModal = document.getElementById("gpkiAlertModal");

      if (existingModal) {
        // If modal exists, append the new message to existing content
        const modalBody = existingModal.querySelector(".modal-body");
        modalBody.innerHTML += "<hr>" + message;
      } else {
        // Create new modal if none exists
        const modalHtml = `
            <div class="modal fade" id="gpkiAlertModal" tabindex="-1" role="dialog" aria-labelledby="gpkiAlertModalLabel">
                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                            <h4 class="modal-title" id="gpkiAlertModalLabel">Notice</h4>
                        </div>
                        <div class="modal-body">
                            ${message}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-primary" data-dismiss="modal">OK</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        const pageContent = document.getElementById("page-content");
        pageContent.insertAdjacentHTML("beforeend", modalHtml);

        const modal = $("#gpkiAlertModal");
        modal.modal("show");

        // Remove modal from DOM after it's hidden
        modal.on("hidden.bs.modal", function () {
          modal.remove();
        });
      }
    }, 0);
    $("body").attr("style", "");
  },

  showError: function (message, header) {
    Gpki.showMessage(header + "<br>" + message, { type: "error" });
  },

  showMessage: function (message, args, callback) {
    const getModalHtml = (message, args) => {
      let buttons = "";
      if (args && (args.confirm || args.input)) {
        buttons = `
          <button type="button" class="btn btn-primary" value="ok">${args.textOk}</button>
          <button type="button" class="btn btn-secondary" value="cancel">${args.textCancel}</button>
        `;
      } else if (args && args.verify) {
        buttons = `
          <button type="button" class="btn btn-primary" value="ok">${args.textYes}</button>
          <button type="button" class="btn btn-secondary" value="cancel">${args.textNo}</button>
        `;
      } else {
        buttons = `<button type="button" class="btn btn-primary" value="ok">${
          (args && args.textOk) || "Ok"
        }</button>`;
      }

      const inputField =
        args && args.input
          ? `
        <div class="form-group">
          <input type="text" class="form-control aTextbox" value="${
            typeof args.input === "string" ? args.input : ""
          }">
        </div>
      `
          : "";

      return `
        <div class="modal fade" id="gpkiMessageModal" tabindex="-1" role="dialog">
          <div class="modal-dialog" role="document">
            <div class="modal-content">
              <div class="modal-header">
                <h5 class="modal-title">${
                  args && args.type
                    ? args.type.charAt(0).toUpperCase() + args.type.slice(1)
                    : "Message"
                }</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                  <span aria-hidden="true">&times;</span>
                </button>
              </div>
              <div class="modal-body">
                ${message}
                ${inputField}
              </div>
              <div class="modal-footer">
                ${buttons}
              </div>
            </div>
          </div>
        </div>
      `;
    };
    // Remove existing modal if present
    $("#gpkiMessageModal").remove();

    // Add new modal
    $("body").append(getModalHtml(message, args));
    const modal = $("#gpkiMessageModal");

    // Handle button clicks
    modal.find(".modal-footer button").on("click", function () {
      const value = $(this).val();
      const inputText = modal.find(".aTextbox").val();

      modal.modal("hide");

      if (callback) {
        if (value === "ok") {
          callback(args && args.input ? inputText : true);
        } else if (value === "cancel") {
          callback(false);
        }
      }
    });

    // Remove modal after hidden
    modal.on("hidden.bs.modal", function () {
      $(this).remove();
    });

    // Show modal with animation if specified
    if (args && args.animate) {
      const speed = typeof args.animate === "number" ? args.animate : 400;
      modal.find(".modal-dialog").css("top", "-200px");
      modal.modal("show");
      modal.find(".modal-dialog").animate({ top: "0" }, speed);
    } else {
      modal.modal("show");
    }
  },
};

// SOFTCERT TOKEN ROAMING FUNCTION===============================================

function activate(type, certID, pin, question, answer) {
  var url = hostSTR + "activate_cert?";
  var params =
    "type=" +
    type +
    "&certID=" +
    certID +
    "&pin=" +
    pin +
    "&question=" +
    question +
    "&answer=" +
    answer;
  Gpki.runScript(url + params);
}

/**
 *
 * DTS Function - Timestamp Request
 * @param data - data that intended for timestamping
 * @param agencyid - agencyid that is given by MAMPU
 * @returns
 */
function timestamp_request(data, agencyid, id) {
  var url = hostSTR + "timestamp_request?";
  var params = "";
  params = "data=" + data + "&agencyid=" + agencyid + "&id=" + id;
  Gpki.runScript(url + params);
}

/**
 *
 * DTS Function - Timestamp Verify
 * @param imprint - data that is intended to be validated
 * @param agencyid - agencyid that is given by MAMPU
 * @returns
 */
function timestamp_verify(imprint, timestampresponse, agencyid, id) {
  var url = hostSTR + "timestamp_verify?";
  var params = "";
  params =
    "imprint=" +
    imprint +
    "&timestampresponse=" +
    timestampresponse +
    "&agencyid=" +
    agencyid +
    "&id=" +
    id;
  Gpki.runScript(url + params);
}

/**
 *
 * DTS Function - Timestamp PDF send as base64
 * @param imprint - data that is intended to be validated
 * @param agencyid - agencyid that is given by MAMPU
 * @returns
 */

function timestamp_fileRequest(userInfo, data, medium, pin, agencyID, userID) {
  var url = hostSTR + "timestamp_pdfrequest";
  var pdf = {
    userInfo: userInfo,
    data: data,
    medium: medium,
    pin: pin,
    agencyID: agencyID,
    userID: userID,
  };
  console.log("params :", pdf, url);
  Gpki.runScript(url, pdf, null, null, "POST");
}

/**
 *
 * Sign Function
 * @param type - softcert/roaming/token
 * @param id - certificate id
 * @param pin - certificate pin
 * @param plainText - data to sign
 * @param detachMode - sign attach/detach
 * @param signingType - data/hash
 * @returns
 */
function sign(type, id, pin, plainText, detachMode, signingType) {
  console.log("detachMode ", detachMode);
  var url;
  if (type == "token") {
    url = hostSTR + "sign_st?";
  } else {
    url = hostSTR + "sign?";
  }
  console.log(signingType);
  console.log("arguments.length - ", arguments.length);
  var params = "";
  if (arguments.length == 5) {
    params =
      "type=" +
      type +
      "&text=" +
      plainText +
      "&id=" +
      id +
      "&pin=" +
      pin +
      "&signingType=" +
      signingType;
    params += "&detach=false";
  } else if (arguments.length == 4) {
    params =
      "type=" +
      type +
      "&text=" +
      pin +
      "&sessionID=" +
      id +
      "&signingType=" +
      signingType;
    params += "&detach=false";
  } else {
    params =
      "type=" +
      type +
      "&text=" +
      plainText +
      "&id=" +
      id +
      "&pin=" +
      pin +
      "&detach=" +
      detachMode +
      "&signingType=" +
      signingType;
  }

  console.log(url + params);
  Gpki.runScript(url + params);
}

/**
 * Sign Batch Agent For Token Function
 * @param id - certificate id
 * @param pin - certificate pin
 * @param plainText - data to sign
 * @param detachMode - sign attach/detach
 * @param batchFlag - indicator : 0 - login token,1 next signing, 2 log out token
 * @returns
 */
function signbatch(id, pin, plainText, detachMode, batchFlag, signingType) {
  var url = hostSTR + "sign_batch?";
  var params =
    "&text=" +
    plainText +
    "&id=" +
    id +
    "&pin=" +
    pin +
    "&detach=" +
    detachMode +
    "&flag=" +
    batchFlag +
    "&signingType=" +
    signingType;
  Gpki.runScript(url + params);
}

/**
 * Sign Batch Agent For Softcert/Roaming Function
 * @param medium - softcert/roaming
 * @param id - certificate id
 * @param pin - certificate pin
 * @param plainText - data to sign
 * @param detachMode - sign attach/detach
 * @param signingType - data/hash
 * @param batchFlag - indicator : 0 - 1st sign,1 next signing, 2 end sign
 * @param sessionId - sessionId for signing next
 * @returns
 */
function signbatchCert(
  medium,
  id,
  pin,
  plainText,
  detachMode,
  signingType,
  batchFlag,
  sessionId
) {
  var url = hostSTR + "sign_batchCert?";
  var params =
    "&medium=" +
    medium +
    "&text=" +
    plainText +
    "&id=" +
    id +
    "&pin=" +
    pin +
    "&detach=" +
    detachMode +
    "&signingType=" +
    signingType +
    "&flag=" +
    batchFlag +
    "&sessionId=" +
    sessionId;

  Gpki.runScript(url + params);
}

function sign_detach(type, id, pin, plainText) {
  var url = hostSTR + "sign?";
  var params = "";
  if (arguments.length == 4)
    params =
      "type=" + type + "&text=" + plainText + "&id=" + id + "&pin=" + pin;
  else params = "type=" + type + "&text=" + pin + "&sessionID=" + id;
  params += "&detach=true";
  Gpki.runScript(url + params);
}

/**
 * To sign using smart cards
 * @param pin PIN number of smart card
 * @param plainText the text that we want to sign
 * @returns
 */
function sign_sc(pin, plainText) {
  var url = hostSTR + "sign_sc?";
  var params = "pin=" + pin + "&text=" + plainText;
  params += "&detach=false";
  Gpki.runScript(url + params);
}

/**
 * To detach_sign using smart cards
 * @param pin PIN number of smart card
 * @param plainText the text that we want to sign
 * @returns
 */
function sign_sc_detach(pin, plainText) {
  var url = hostSTR + "sign_sc?";
  var params = "pin=" + pin + "&text=" + plainText;
  params += "&detach=true";
  Gpki.runScript(url + params);
}

/**
 * To sign using Secure Token
 * @param pin PIN number of smart card
 * @param plainText the text that we want to sign
 * @returns
 */
function sign_st(pin, plainText) {
  var url = hostSTR + "sign_st?";
  var params = "pin=" + pin + "&text=" + plainText;
  params += "&detach=false";
  Gpki.runScript(url + params);
}

/**
 * To detach_sign using Secure Token
 * @param pin PIN number of smart card
 * @param plainText the text that we want to sign
 * @returns
 */
function sign_st_detach(pin, plainText) {
  var url = hostSTR + "sign_st?";
  var params = "pin=" + pin + "&text=" + plainText;
  params += "&detach=true";
  Gpki.alert(url + params);
  Gpki.runScript(url + params);
}

/**
 * Verifies the signed text, it checks if the signed text is the same with the original text
 * use verify(orignalText, signedText) if want to verify detached signature
 * use verify(signedText) if want to verify attached signature
 * @param originalText Text before signing
 * @param signedText Text after signing
 * @returns
 */
function verify(originalText, signedText) {
  var url = hostSTR + "verify?";
  var params;
  if (arguments.length == 2) {
    params = "originalText=" + originalText + "&signedText=" + signedText;
    params += "&detach=true";
  } else {
    params = "signedText=" + originalText; //originalText = signedText
    params += "&detach=false";
  }
  Gpki.runScript(url + params);
}

/**
 * Verifies the detached signed text, it checks if the signed text is the same with the original text
 * @param originalText Text before signing
 * @param signedText Text after signing
 * @returns
 */
function verify_detach(originalText, signedText) {
  var url = hostSTR + "verify?";
  var params = "originalText=" + originalText + "&signedText=" + signedText;
  params += "&detach=true";

  Gpki.runScript(url + params);
}
/**
 * Encrypt a text for a recipent
 * @param recipient_id id of the reciever
 * @param plainText Text to be encrypted
 * @returns
 */
function encrypt(recipient_id, plainText) {
  var url = hostSTR + "encrypt?";
  var params = "recipient=" + recipient_id + "&text=" + plainText;
  Gpki.runScript(url + params);
}

/**
 * Decrypt a text
 * @param cipherText encrypted text
 * @param id id of the user
 * @param pin pin number of user
 * @returns
 */
function decrypt(cipherText, id, pin) {
  var url = hostSTR + "decrypt?";
  if (api_version == "1.3")
    var params =
      "type=softcert&text=" + cipherText + "&id=" + id + "&pin=" + pin;
  else var params = "type=" + cipherText + "&text=" + pin + "&sessionID=" + id;
  Gpki.runScript(url + params);
}

function download(certID, pinNo) {
  var url = hostSTR + "download?";
  var params = "id=" + certID + "&pin=" + pinNo;
  Gpki.runScript(url + params);
}
/**
 * List all certificates
 * @returns
 */
function list() {
  var url = hostSTR + "list?";
  Gpki.runScript(url);
}

/**
 * Verify ID in the roaming server
 * @param type softcert, roaming or smartcard
 * @param certID id of cert
 * @returns appends callback_verify_id() java script function
 */
function verify_id(type, certID) {
  var url = hostSTR + "verify_id?";
  var params = "certID=" + certID + "&type=" + type;
  Gpki.runScript(url + params);
}

function getagentinfo(type, ignoreerrors) {
  var url = hostSTR + "agent_info?";
  var params = "type=" + type;
  Gpki.runScript(url + params, null, null, ignoreerrors);
}

/**
 * Check user's security questions
 * @param type type of certificates
 * @param certID ID of cert
 * @param pin Pin number
 * @param question choosen security question
 * @param answer answer to the security question
 * @param timeout the timeout limitation
 * @returns
 */
function verify_question(type, certID, pin, question, answer, timeout) {
  var url = hostSTR + "verify_question?";
  var params =
    "certID=" +
    certID +
    "&type=" +
    type +
    "&pin=" +
    pin +
    "&question=" +
    question +
    "&answer=" +
    answer +
    "&timeout=" +
    timeout;
  Gpki.runScript(url + params);
}

function get_token_id() {
  var url = hostSTR + "get_token_id";
  Gpki.runScript(url);
}

function change_so_pin(sopin, newsopin) {
  var url = hostSTR + "change_sopin?";
  var params = "sopin=" + sopin + "&newsopin=" + newsopin;
  Gpki.runScript(url + params);
}

function verify_so_pin(sopin) {
  var url = hostSTR + "verify_sopin?";
  var params = "sopin=" + sopin;
  return Gpki.runScript(url + params);
}

// ROAMING OTP FUNCTION===============================================
/**
 *
 * Roaming Service Function - Request OTP
 * @param nric - nric user
 * @param agencyid - agencyid that is given by MAMPU
 * @returns nonce value & send otp to mobile
 */

function request_otp(
  nric,
  agencyID,
  userId,
  modeUsed,
  otpExp,
  saluran,
  emailuser
) {
  //mode - Training/Production
  var url = hostOTP + "request_otp_new/";
  console.log("url - ", url);
  var param = {
    nric: nric,
    agencyId: agencyID,
    userId: userId,
    mode: modeUsed,
    otpExp: otpExp,
    saluran: saluran,
    emailuser: emailuser,
  };
  var encodedParam = "";
  if (window.btoa) {
    console.log("window.btoa");
    encodedParam = btoa(JSON.stringify(param));
  } else {
    //for <= IE9
    console.log("<= IE9");
    encodedParam = Base64.encode(JSON.stringify(param));
  }
  console.log("encodedParam - ", encodedParam);

  Gpki.runScriptRequestOtp(url, encodedParam, null, null, "POST");
}

/**
 *
 * Roaming Service Function - Sign Roaming
 * @param nric - nric user
 * {"nric": "931002015155", "nonce": "833842127629", "otpCode": "595974", "data": "helloworld2", "password": "12345678","agencyId":"GOVICT_617244"}
 */

// function sign_roaming(nric,nonce,otpCode,data,pin,agencyId,modeUsed,signType)
// {
//     var url = hostOTP+"sign_roaming/";
// 	var param = {nric:nric, nonce:nonce, otpCode:otpCode, data:data, pin:pin, agencyId:agencyId, mode:modeUsed,signType:signType};
// 	console.log("param" ,param)
// 	var encodedParam = btoa(JSON.stringify(param));
//     console.log("encodedParam" ,encodedParam)
// 	Gpki.runScriptSigningRoaming(url,encodedParam,null,null,"POST");

// }

function sign_roaming(
  nric,
  nonce,
  otpCode,
  data,
  pin,
  agencyId,
  modeUsed,
  signType,
  dataSeq
) {
  var url = hostOTP + "sign_roaming/";
  console.log("url - ", url);
  var param = {
    nric: nric,
    nonce: nonce,
    otpCode: otpCode,
    data: data,
    pin: pin,
    agencyId: agencyId,
    mode: modeUsed,
    signType: signType,
    dataSeq: dataSeq,
  };
  var encodedParam = "";
  if (window.btoa) {
    console.log("window.btoa");
    encodedParam = btoa(JSON.stringify(param));
  } else {
    //for <= IE9
    console.log("<= IE9");
    encodedParam = Base64.encode(JSON.stringify(param));
  }
  console.log("encodedParam - ", encodedParam);

  Gpki.runScriptSigningRoaming(url, encodedParam, null, null, "POST");
}

function sign_roaming_next(sessionId, dataSeq, data) {
  var url = hostOTP + "sign_roaming_next/";
  console.log("url - ", url);
  var param = { sessionId: sessionId, dataSeq: dataSeq, data: data };
  var encodedParam = "";
  if (window.btoa) {
    console.log("window.btoa");
    encodedParam = btoa(JSON.stringify(param));
  } else {
    //for <= IE9
    console.log("<= IE9");
    encodedParam = Base64.encode(JSON.stringify(param));
  }
  console.log("encodedParam - ", encodedParam);

  Gpki.runScriptSigningRoamingNext(url, encodedParam, null, null, "POST");
}

function request_mediumList(nric, applicationCode, modeUsed) {
  //mode - Training/Production
  var url = hostBroker + "mediumList_new/";
  console.log("url - ", url);
  var param = { nric: nric, applicationCode: applicationCode, mode: modeUsed };
  var encodedParam = "";
  if (window.btoa) {
    console.log("window.btoa");
    encodedParam = btoa(JSON.stringify(param));
  } else {
    //for <= IE9
    console.log("<= IE9");
    encodedParam = Base64.encode(JSON.stringify(param));
  }
  console.log("encodedParam - ", encodedParam);

  Gpki.runScriptRequestMedium(url, encodedParam, null, null, "POST");
}

/**
 *
 * Roaming Service Function - Request OTP
 * @param nric - nric user
 * @param agencyid - agencyid that is given by MAMPU
 * {"nric": "931002015155", "nonce": "245682795160", "otpCode": "366839", "plaintext": "helloworld2","agencyId":"GOVICT_617244", "signature":"MIAGCSqGSIb3DQEHAqCAMIACAQExDzANBglghkgBZQMEAgEFADCABgkqhkiG9w0BBwEAAKCAMIIC3zCCAmSgAwIBAgIQfwaSX2ITUtQTbarlLiWGJDAKBggqhkjOPQQDAjCBrDELMAkGA1UEBhMCTVkxRDBCBgNVBAoTO1VuaXQgUGVtb2RlbmFuIFRhZGJpcmFuIERhbiBQZXJhbmNhbmdhbiBQZW5ndXJ1c2FuIE1hbGF5c2lhMQ0wCwYDVQQLEwRHUEtJMSIwIAYDVQQLExlGT1IgVEVTVElORyBQVVJQT1NFUyBPTkxZMSQwIgYDVQQDExtNQU1QVSBDbGFzcyAyIFRlc3QgQ0EgKEVDQykwHhcNMjAwNjAyMDAwMDAwWhcNMjEwNjAyMjM1OTU5WjBjMSYwJAYDVQQDDB1KQUFGQVIgQklOIE9TTUFOIDkzMTAwMjAxNTE1NTEVMBMGA1UEBRMMOTMxMDAyMDE1MTU1MRUwEwYDVQQEEww5MzEwMDIwMTUxNTUxCzAJBgNVBAYTAk1ZMFkwEwYHKoZIzj0CAQYIKoZIzj0DAQcDQgAEhkhgUnXkAcSQBAoeO\\/0YI16nI\\/nmaYCCvsqwn3C74zpBP05ZrClfGg73a11V7P0NEsq8zPPbxp96cRTKt4C\\/\\/6OBrzCBrDAMBgNVHRMBAf8EAjAAMEwGA1UdHwRFMEMwQaA\\/oD2GO2h0dHA6Ly9jcmwtdGVzdC5tc2N0cnVzdGdhdGUuY29tL01BTVBVR1BLSUVDQy9MYXRlc3RDUkwuY3JsMA4GA1UdDwEB\\/wQEAwIE8DAdBgNVHQ4EFgQUOFk5fZvpWYX1SIwyDAB2Ntn1eTMwHwYDVR0jBBgwFoAUSi19sH5u3psXns70MdiPy8Ral1IwCgYIKoZIzj0EAwIDaQAwZgIxAMQqOAmO4VsedduAFI419IRSaPMWvAQcAzRXW+GpHzRtzZ5Aw3DDWRkBE6BaixBjCwIxAKO+vczFCreO0p56w0En54y88jWbXUnrPX8m4CtvvUXG9agDSFn3w+wuxcr+4DTkEAAAMYIBxzCCAcMCAQEwgcEwgawxCzAJBgNVBAYTAk1ZMUQwQgYDVQQKEztVbml0IFBlbW9kZW5hbiBUYWRiaXJhbiBEYW4gUGVyYW5jYW5nYW4gUGVuZ3VydXNhbiBNYWxheXNpYTENMAsGA1UECxMER1BLSTEiMCAGA1UECxMZRk9SIFRFU1RJTkcgUFVSUE9TRVMgT05MWTEkMCIGA1UEAxMbTUFNUFUgQ2xhc3MgMiBUZXN0IENBIChFQ0MpAhB\\/BpJfYhNS1BNtquUuJYYkMA0GCWCGSAFlAwQCAQUAoIGVMBgGCSqGSIb3DQEJAzELBgkqhkiG9w0BBwEwHAYJKoZIhvcNAQkFMQ8XDTIwMDkwODAxMTU0N1owKgYJKoZIhvcNAQk0MR0wGzANBglghkgBZQMEAgEFAKEKBggqhkjOPQQDAjAvBgkqhkiG9w0BCQQxIgQgbr8L1Aq3VxJKrl+OQAUhtgtDzkAOTV1hc06qtkdA1dEwCgYIKoZIzj0EAwIERzBFAiEAoLdxXoTyidZZjwMyn4fbXr\\/gkBU2BJEE3Ys31GEYdzcCIHdG24VbSIELBTogAy4lp1+Fa6SPxFuTQvwo71Gm6Aj7AAAAAAAA"}
 */

function verify_roaming(nric, nonce, otpCode, plaintext, agencyId, signature) {
  var url = hostOTP + "verify_roaming/";
  console.log("url - ", url);
  var param = {
    nric: nric,
    nonce: nonce,
    otpCode: otpCode,
    plaintext: plaintext,
    agencyId: agencyId,
    signature: signature,
  };
  var encodedParam = "";
  if (window.btoa) {
    console.log("window.btoa");
    encodedParam = btoa(JSON.stringify(param));
  } else {
    //for <= IE9
    console.log("<= IE9");
    encodedParam = Base64.encode(JSON.stringify(param));
  }
  console.log("encodedParam - ", encodedParam);

  Gpki.runScriptVerifyRoaming(url, encodedParam, null, null, "POST");
}

/**
 * Generates SHA-1 hash of string
 *
 * @param {String} msg                String to be hashed
 * @param {Boolean} [utf8encode=true] Encode msg as UTF-8 before generating hash
 * @returns {String}                  Hash of msg as hex character string
 */
Gpki.hash = function (msg, utf8encode) {
  utf8encode = typeof utf8encode == "undefined" ? true : utf8encode;

  // convert string to UTF-8, as SHA only deals with byte-streams
  if (utf8encode) msg = Utf8.encode(msg);

  // constants [§4.2.1]
  var K = [0x5a827999, 0x6ed9eba1, 0x8f1bbcdc, 0xca62c1d6];

  // PREPROCESSING

  msg += String.fromCharCode(0x80); // add trailing '1' bit (+ 0's padding) to string [§5.1.1]

  // convert string msg into 512-bit/16-integer blocks arrays of ints [§5.2.1]
  var l = msg.length / 4 + 2; // length (in 32-bit integers) of msg + ‘1’ + appended length
  var N = Math.ceil(l / 16); // number of 16-integer-blocks required to hold 'l' ints
  var M = new Array(N);

  for (var i = 0; i < N; i++) {
    M[i] = new Array(16);
    for (var j = 0; j < 16; j++) {
      // encode 4 chars per integer, big-endian encoding
      M[i][j] =
        (msg.charCodeAt(i * 64 + j * 4) << 24) |
        (msg.charCodeAt(i * 64 + j * 4 + 1) << 16) |
        (msg.charCodeAt(i * 64 + j * 4 + 2) << 8) |
        msg.charCodeAt(i * 64 + j * 4 + 3);
    } // note running off the end of msg is ok 'cos bitwise ops on NaN return 0
  }
  // add length (in bits) into final pair of 32-bit integers (big-endian) [§5.1.1]
  // note: most significant word would be (len-1)*8 >>> 32, but since JS converts
  // bitwise-op args to 32 bits, we need to simulate this by arithmetic operators
  M[N - 1][14] = ((msg.length - 1) * 8) / Math.pow(2, 32);
  M[N - 1][14] = Math.floor(M[N - 1][14]);
  M[N - 1][15] = ((msg.length - 1) * 8) & 0xffffffff;

  // set initial hash value [§5.3.1]
  var H0 = 0x67452301;
  var H1 = 0xefcdab89;
  var H2 = 0x98badcfe;
  var H3 = 0x10325476;
  var H4 = 0xc3d2e1f0;

  // HASH COMPUTATION [§6.1.2]

  var W = new Array(80);
  var a, b, c, d, e;
  for (var i = 0; i < N; i++) {
    // 1 - prepare message schedule 'W'
    for (var t = 0; t < 16; t++) W[t] = M[i][t];
    for (var t = 16; t < 80; t++)
      W[t] = Gpki.ROTL(W[t - 3] ^ W[t - 8] ^ W[t - 14] ^ W[t - 16], 1);

    // 2 - initialise five working variables a, b, c, d, e with previous hash value
    a = H0;
    b = H1;
    c = H2;
    d = H3;
    e = H4;

    // 3 - main loop
    for (var t = 0; t < 80; t++) {
      var s = Math.floor(t / 20); // seq for blocks of 'f' functions and 'K' constants
      var T =
        (Gpki.ROTL(a, 5) + Gpki.f(s, b, c, d) + e + K[s] + W[t]) & 0xffffffff;
      e = d;
      d = c;
      c = Gpki.ROTL(b, 30);
      b = a;
      a = T;
    }

    // 4 - compute the new intermediate hash value
    H0 = (H0 + a) & 0xffffffff; // note 'addition modulo 2^32'
    H1 = (H1 + b) & 0xffffffff;
    H2 = (H2 + c) & 0xffffffff;
    H3 = (H3 + d) & 0xffffffff;
    H4 = (H4 + e) & 0xffffffff;
  }

  return (
    Gpki.toHexStr(H0) +
    Gpki.toHexStr(H1) +
    Gpki.toHexStr(H2) +
    Gpki.toHexStr(H3) +
    Gpki.toHexStr(H4)
  );
};

//
// function 'f' [§4.1.1]
//
Gpki.f = function (s, x, y, z) {
  switch (s) {
    case 0:
      return (x & y) ^ (~x & z); // Ch()
    case 1:
      return x ^ y ^ z; // Parity()
    case 2:
      return (x & y) ^ (x & z) ^ (y & z); // Maj()
    case 3:
      return x ^ y ^ z; // Parity()
  }
};

//
// rotate left (circular left shift) value x by n positions [§3.2.5]
//
Gpki.ROTL = function (x, n) {
  return (x << n) | (x >>> (32 - n));
};

//
// hexadecimal representation of a number
//   (note toString(16) is implementation-dependant, and
//   in IE returns signed numbers when used on full words)
//
Gpki.toHexStr = function (n) {
  var s = "",
    v;
  for (var i = 7; i >= 0; i--) {
    v = (n >>> (i * 4)) & 0xf;
    s += v.toString(16);
  }
  return s;
};

/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */
/*  Utf8 class: encode / decode between multi-byte Unicode characters and UTF-8 multiple          */
/*              single-byte character encoding (c) Chris Veness 2002-2010                         */
/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */

var Utf8 = {}; // Utf8 namespace

/**
 * Encode multi-byte Unicode string into utf-8 multiple single-byte characters
 * (BMP / basic multilingual plane only)
 *
 * Chars in range U+0080 - U+07FF are encoded in 2 chars, U+0800 - U+FFFF in 3 chars
 *
 * @param {String} strUni Unicode string to be encoded as UTF-8
 * @returns {String} encoded string
 */
Utf8.encode = function (strUni) {
  // use regular expressions & String.replace callback function for better efficiency
  // than procedural approaches
  var strUtf = strUni.replace(
    /[\u0080-\u07ff]/g, // U+0080 - U+07FF => 2 bytes 110yyyyy, 10zzzzzz
    function (c) {
      var cc = c.charCodeAt(0);
      return String.fromCharCode(0xc0 | (cc >> 6), 0x80 | (cc & 0x3f));
    }
  );
  strUtf = strUtf.replace(
    /[\u0800-\uffff]/g, // U+0800 - U+FFFF => 3 bytes 1110xxxx, 10yyyyyy, 10zzzzzz
    function (c) {
      var cc = c.charCodeAt(0);
      return String.fromCharCode(
        0xe0 | (cc >> 12),
        0x80 | ((cc >> 6) & 0x3f),
        0x80 | (cc & 0x3f)
      );
    }
  );
  return strUtf;
};

/**
 * Decode utf-8 encoded string back into multi-byte Unicode characters
 *
 * @param {String} strUtf UTF-8 string to be decoded back to Unicode
 * @returns {String} decoded string
 */
Utf8.decode = function (strUtf) {
  // note: decode 3-byte chars first as decoded 2-byte strings could appear to be 3-byte char!
  var strUni = strUtf.replace(
    /[\u00e0-\u00ef][\u0080-\u00bf][\u0080-\u00bf]/g, // 3-byte chars
    function (c) {
      // (note parentheses for precence)
      var cc =
        ((c.charCodeAt(0) & 0x0f) << 12) |
        ((c.charCodeAt(1) & 0x3f) << 6) |
        (c.charCodeAt(2) & 0x3f);
      return String.fromCharCode(cc);
    }
  );
  strUni = strUni.replace(
    /[\u00c0-\u00df][\u0080-\u00bf]/g, // 2-byte chars
    function (c) {
      // (note parentheses for precence)
      var cc = ((c.charCodeAt(0) & 0x1f) << 6) | (c.charCodeAt(1) & 0x3f);
      return String.fromCharCode(cc);
    }
  );
  return strUni;
};
/* - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -  */

var Base64 = {
  _keyStr: "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",

  encode: function (input) {
    var output = "";
    var chr1, chr2, chr3, enc1, enc2, enc3, enc4;
    var i = 0;

    input = Base64._utf8_encode(input);

    while (i < input.length) {
      chr1 = input.charCodeAt(i++);
      chr2 = input.charCodeAt(i++);
      chr3 = input.charCodeAt(i++);

      enc1 = chr1 >> 2;
      enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
      enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
      enc4 = chr3 & 63;

      if (isNaN(chr2)) {
        enc3 = enc4 = 64;
      } else if (isNaN(chr3)) {
        enc4 = 64;
      }

      output =
        output +
        this._keyStr.charAt(enc1) +
        this._keyStr.charAt(enc2) +
        this._keyStr.charAt(enc3) +
        this._keyStr.charAt(enc4);
    }

    return output;
  },

  decode: function (input) {
    var output = "";
    var chr1, chr2, chr3;
    var enc1, enc2, enc3, enc4;
    var i = 0;

    input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");

    while (i < input.length) {
      enc1 = this._keyStr.indexOf(input.charAt(i++));
      enc2 = this._keyStr.indexOf(input.charAt(i++));
      enc3 = this._keyStr.indexOf(input.charAt(i++));
      enc4 = this._keyStr.indexOf(input.charAt(i++));

      chr1 = (enc1 << 2) | (enc2 >> 4);
      chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
      chr3 = ((enc3 & 3) << 6) | enc4;

      output = output + String.fromCharCode(chr1);

      if (enc3 != 64) {
        output = output + String.fromCharCode(chr2);
      }
      if (enc4 != 64) {
        output = output + String.fromCharCode(chr3);
      }
    }

    output = Base64._utf8_decode(output);

    return output;
  },

  _utf8_encode: function (string) {
    string = string.replace(/\r\n/g, "\n");
    var utftext = "";

    for (var n = 0; n < string.length; n++) {
      var c = string.charCodeAt(n);

      if (c < 128) {
        utftext += String.fromCharCode(c);
      } else if (c > 127 && c < 2048) {
        utftext += String.fromCharCode((c >> 6) | 192);
        utftext += String.fromCharCode((c & 63) | 128);
      } else {
        utftext += String.fromCharCode((c >> 12) | 224);
        utftext += String.fromCharCode(((c >> 6) & 63) | 128);
        utftext += String.fromCharCode((c & 63) | 128);
      }
    }

    return utftext;
  },

  _utf8_decode: function (utftext) {
    var string = "";
    var i = 0;
    var c = (c1 = c2 = 0);

    while (i < utftext.length) {
      c = utftext.charCodeAt(i);

      if (c < 128) {
        string += String.fromCharCode(c);
        i++;
      } else if (c > 191 && c < 224) {
        c2 = utftext.charCodeAt(i + 1);
        string += String.fromCharCode(((c & 31) << 6) | (c2 & 63));
        i += 2;
      } else {
        c2 = utftext.charCodeAt(i + 1);
        c3 = utftext.charCodeAt(i + 2);
        string += String.fromCharCode(
          ((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63)
        );
        i += 3;
      }
    }

    return string;
  },
};
