<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\Exception\RequestException;

class Handle<PERSON>heckGPKIMedium extends Command
{
    protected $signature = 'gpki:check-medium 
    {--all : Check all records regardless of status}
    {--null-only : Check only records with null status}
    {--current-only : Process only current year table}
    {--historical-only : Process only historical (2024) table}
    {--batch-size=10 : Number of records to process in a batch}';

    protected $description = 'Check GPKI medium availability for users from current and historical tables';

    private $applicationCode = 'ePerolehan';
    private $brokerUrl = 'https://mygpki.gov.my/gpki_broker/mediumList_new/';
    private $client;
    private $connection = 'mysql_ep_support';
    private $currentTable = 'ep_gpki_user_signing_list';
    private $historicalTable = 'ep_gpki_user_signing_list_2024';
    private $oracleConnection = 'oracle_nextgen_rpt';

    public function __construct()
    {
        parent::__construct();

        $this->client = new Client([
            'verify' => false,
            'timeout' => 30,
            'headers' => [
                'Content-Type' => 'application/json',
            ]
        ]);
    }

    public function handle()
    {
        Log::info(__NAMESPACE__ . '::' . __FUNCTION__ . ' Starting GPKI medium check for multiple tables');

        // Determine which tables to process based on options
        $processCurrentTable = !$this->option('historical-only');
        $processHistoricalTable = !$this->option('current-only');

        $tablesToProcess = [];
        if ($processCurrentTable) {
            $tablesToProcess[] = $this->currentTable;
        }
        if ($processHistoricalTable) {
            $tablesToProcess[] = $this->historicalTable;
        }

        if (empty($tablesToProcess)) {
            $this->error('No tables selected for processing');
            return 1;
        }

        // Verify tables exist
        foreach ($tablesToProcess as $table) {
            if (!DB::connection($this->connection)->getSchemaBuilder()->hasTable($table)) {
                $this->error("Table '{$table}' does not exist");
                return 1;
            }

            // Verify required columns exist
            $columns = DB::connection($this->connection)->getSchemaBuilder()->getColumnListing($table);
            $requiredColumns = ['identification_no', 'status', 'remark', 'updated_at', 'status_ep_user', 'status_ep_org'];

            $missingColumns = array_diff($requiredColumns, $columns);
            if (!empty($missingColumns)) {
                $this->error("Table '{$table}' is missing required columns: " . implode(', ', $missingColumns));
                return 1;
            }
        }

        // Get flags - using explicit casts to boolean
        $checkAll = (bool) $this->option('all');
        $nullOnly = (bool) $this->option('null-only');
        $batchSize = (int) $this->option('batch-size');

        // Collect unique identification numbers across all tables
        $uniqueIdentificationNumbers = $this->collectUniqueIdentificationNumbers($tablesToProcess, $checkAll, $nullOnly);

        $totalUniqueIds = count($uniqueIdentificationNumbers);
        Log::info(__NAMESPACE__ . '::' . __FUNCTION__ . " Found {$totalUniqueIds} unique identification numbers to process");

        if ($totalUniqueIds === 0) {
            $this->error('No records found with identification numbers that need processing');
            return 1;
        }

        $this->info("Processing {$totalUniqueIds} unique identification numbers across " . count($tablesToProcess) . " tables...");
        $bar = $this->output->createProgressBar($totalUniqueIds);

        // Process identification numbers in batches
        $batches = array_chunk($uniqueIdentificationNumbers, $batchSize);

        foreach ($batches as $batch) {
            // Container for API responses
            $responseBatch = [];

            // Process each ID in the batch
            foreach ($batch as $idNumber) {
                try {
                    // Call API for this identification number
                    $apiResponse = $this->callGpkiApi($idNumber);

                    // Check if the identification_no exists in ePerolehan
                    $epUserStatus = $this->checkUserExists($idNumber);

                    // Initialize organization status
                    $epOrgStatus = 'not_found';

                    // If the user exists, check organization type and status
                    if ($epUserStatus === 'exist') {
                        // Get user and organization details
                        $userDetails = $this->getUserDetails($idNumber);

                        if ($userDetails) {
                            // Check if user is a supplier (org_type_id == 15)
                            if ($userDetails->org_type_id == 15) {
                                $epOrgStatus = 'supplier';
                            } else {
                                // Regular government organization
                                // Get the org_code using the org_profile_id
                                $orgCode = $this->getOrgCodeForProfileId($userDetails->org_profile_id);

                                if ($orgCode) {
                                    $epOrgStatus = 'exist';
                                }
                            }
                        }
                    }

                    // Store the response for batch update with additional statuses
                    $apiResponse['status_ep_user'] = $epUserStatus;
                    $apiResponse['status_ep_org'] = $epOrgStatus;
                    $responseBatch[$idNumber] = $apiResponse;

                } catch (\Exception $e) {
                    Log::error("Error processing ID: {$idNumber} - " . $e->getMessage());

                    // Store error response
                    $errorResponse = [
                        'status' => 'error',
                        'remark' => "General Error:\nMessage: " . $e->getMessage() . "\nCode: " . $e->getCode(),
                        'status_ep_user' => 'error',
                        'status_ep_org' => 'error',
                    ];
                    $responseBatch[$idNumber] = $errorResponse;
                }

                $bar->advance();
                usleep(200); // Rate limit API calls
            }

            // Batch update the database for this batch of responses
            $this->batchUpdateRecords($tablesToProcess, $responseBatch);
        }

        $bar->finish();
        $this->line('');
        $this->info('Processing completed. Database records updated.');

        // Show summary for each table
        foreach ($tablesToProcess as $table) {
            $summary = DB::connection($this->connection)
                ->table($table)
                ->selectRaw('status, count(*) as count')
                ->whereNotNull('status')
                ->groupBy(['status'])
                ->get();

            $this->info("\nProcessing Summary for {$table}:");
            foreach ($summary as $stat) {
                $this->info(sprintf("%s: %d records", $stat->status, $stat->count));
            }

            // Show summary for ePerolehan User Status
            $epUserSummary = DB::connection($this->connection)
                ->table($table)
                ->selectRaw('status_ep_user, count(*) as count')
                ->whereNotNull('status_ep_user')
                ->groupBy(['status_ep_user'])
                ->get();

            $this->info("\nePerolehan User Status Summary for {$table}:");
            foreach ($epUserSummary as $stat) {
                $this->info(sprintf("%s: %d records", $stat->status_ep_user, $stat->count));
            }

            // Show summary for ePerolehan Org Status
            $epOrgSummary = DB::connection($this->connection)
                ->table($table)
                ->selectRaw('status_ep_org, count(*) as count')
                ->whereNotNull('status_ep_org')
                ->groupBy(['status_ep_org'])
                ->get();

            $this->info("\nePerolehan Organization Status Summary for {$table}:");
            foreach ($epOrgSummary as $stat) {
                $this->info(sprintf("%s: %d records", $stat->status_ep_org, $stat->count));
            }
        }

        return 0;
    }

    /**
     * Get complete user details including organization type
     */
    private function getUserDetails(string $identificationNo)
    {
        try {
            $query = DB::connection($this->oracleConnection)->table('pm_user as pmu');
            $query->join('pm_user_org as pmuo', 'pmu.user_id', '=', 'pmuo.user_id');
            $query->where('pmu.record_status', 1);
            $query->where('pmuo.record_status', 1);
            $query->where('pmu.identification_no', $identificationNo);
            $query->select('pmu.user_id', 'pmu.org_type_id', 'pmuo.org_profile_id');

            return $query->first();
        } catch (\Exception $e) {
            Log::error("Error getting user details: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the org_code for an org_profile_id
     */
    private function getOrgCodeForProfileId(string $orgProfileId)
    {
        try {
            // First try in PM_ORG_VALIDITY
            $query = DB::connection($this->oracleConnection)->table('pm_org_validity');
            $query->where('record_status', 1);
            $query->where('org_profile_id', $orgProfileId);
            $query->select('org_code');

            $result = $query->first();

            if ($result && !empty($result->org_code)) {
                return $result->org_code;
            }

            // If not found, check if it's a factoring organization
            $query = DB::connection($this->oracleConnection)->table('pm_org_profile as pmop');
            $query->join('pm_financial_org as pmfo', 'pmop.factoring_org_id', '=', 'pmfo.financial_org_id');
            $query->where('pmfo.record_status', 1);
            $query->where('pmop.org_profile_id', $orgProfileId);
            $query->select('pmfo.biz_reg_no as org_code');

            $result = $query->first();

            return $result ? $result->org_code : null;
        } catch (\Exception $e) {
            Log::error("Error getting org_code for profile ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if a user exists in the ePerolehan system
     */
    private function checkUserExists(string $identificationNo)
    {
        try {
            $query = DB::connection($this->oracleConnection)->table('pm_user as pmu');
            $query->join('pm_user_org as pmuo', 'pmu.user_id', '=', 'pmuo.user_id');
            $query->where('pmu.record_status', 1);
            $query->where('pmuo.record_status', 1);
            $query->where('pmu.identification_no', $identificationNo);
            $query->select('pmu.user_id');

            $result = $query->get();

            if (count($result) > 0) {
                return 'exist';
            } else {
                return 'not_found';
            }
        } catch (\Exception $e) {
            Log::error("Error checking user existence: " . $e->getMessage());
            return 'error';
        }
    }

    /**
     * Collect unique identification numbers that need processing across all tables
     */
    private function collectUniqueIdentificationNumbers(array $tables, bool $checkAll, bool $nullOnly)
    {
        $uniqueIds = [];

        foreach ($tables as $table) {
            $query = DB::connection($this->connection)
                ->table($table)
                ->whereNotNull('identification_no');

            // Apply status conditions based on flags
            if (!$checkAll) {
                if ($nullOnly) {
                    // If --null-only flag is present, only get records with null status
                    $query->whereNull('status');
                } else {
                    // Original behavior: get records where status != 'exist' OR status is null
                    $query->where(function ($query) {
                        $query->where('status', '!=', 'exist')
                            ->orWhereNull('status');
                    });
                }
            }

            $tableIds = $query->pluck('identification_no')->toArray();
            $uniqueIds = array_merge($uniqueIds, $tableIds);
        }

        // Return unique values only
        return array_unique($uniqueIds);
    }

    /**
     * Call the GPKI API for a specific identification number
     */
    private function callGpkiApi(string $identificationNo)
    {
        $params = [
            'nric' => $identificationNo,
            'applicationCode' => $this->applicationCode,
            'mode' => 'Production'
        ];

        $encodedParam = base64_encode(json_encode($params));

        try {
            $response = $this->client->post($this->brokerUrl, [
                'body' => $encodedParam
            ]);

            $responseBody = $response->getBody()->getContents();
            $decodedResponse = json_decode($responseBody, true);

            // Create a readable response string with full request and response details
            $responseString = "Request Parameters:\n";
            $responseString .= json_encode($params, JSON_PRETTY_PRINT) . "\n\n";

            $responseString .= "Encoded Parameters:\n";
            $responseString .= $encodedParam . "\n\n";

            $responseString .= "API Response:\n";
            foreach ($decodedResponse as $key => $value) {
                if (is_bool($value)) {
                    $value = $value ? 'true' : 'false';
                } elseif (is_array($value)) {
                    $value = json_encode($value, JSON_PRETTY_PRINT);
                }
                $responseString .= "{$key}: {$value}\n";
            }

            // Determine status based on API response
            if (isset($decodedResponse['status']) && $decodedResponse['status'] === true) {
                $status = 'exist';
            } else {
                $message = $decodedResponse['message'] ?? 'Unknown error';
                if (strpos($message, 'tidak wujud di dalam sistem') !== false) {
                    $status = 'not_found';
                } else {
                    $status = 'error';
                }
            }

            return [
                'status' => $status,
                'remark' => $responseString,
            ];

        } catch (GuzzleException $e) {
            Log::error("Guzzle error processing ID: {$identificationNo} - " . $e->getMessage());

            // Create a readable error message with request details
            $errorString = "Request Parameters:\n";
            $errorString .= json_encode($params, JSON_PRETTY_PRINT) . "\n\n";

            $errorString .= "Encoded Parameters:\n";
            $errorString .= $encodedParam . "\n\n";

            $errorString .= "Guzzle Error:\n";
            $errorString .= "Message: " . $e->getMessage() . "\n";
            $errorString .= "Code: " . $e->getCode() . "\n";

            // Check if it's a RequestException that has a response
            if ($e instanceof RequestException && $e->hasResponse()) {
                $errorString .= "Response: " . $e->getResponse()->getBody()->getContents() . "\n";
            }

            return [
                'status' => 'error',
                'remark' => $errorString,
            ];
        }
    }

    /**
     * Update records in multiple tables based on batch of API responses
     */
    private function batchUpdateRecords(array $tables, array $responsesBatch)
    {
        $now = Carbon::now();

        // Begin transaction to ensure all updates are atomic
        DB::connection($this->connection)->beginTransaction();

        try {
            foreach ($tables as $table) {
                foreach ($responsesBatch as $identificationNo => $response) {
                    $updateData = [
                        'status' => $response['status'],
                        'remark' => $response['remark'],
                        'updated_at' => $now
                    ];

                    // Add the new column values if they exist
                    if (isset($response['status_ep_user'])) {
                        $updateData['status_ep_user'] = $response['status_ep_user'];
                    }

                    if (isset($response['status_ep_org'])) {
                        $updateData['status_ep_org'] = $response['status_ep_org'];
                    }

                    DB::connection($this->connection)
                        ->table($table)
                        ->where('identification_no', $identificationNo)
                        ->update($updateData);
                }
            }

            // Commit all updates if everything was successful
            DB::connection($this->connection)->commit();

        } catch (\Exception $e) {
            // Rollback in case of error
            DB::connection($this->connection)->rollBack();
            Log::error("Error during batch update: " . $e->getMessage());
            throw $e;
        }
    }
}