@extends('layouts.guest-dash')

@section('header')
<style type="text/css">  
.dt-buttons {
    display: none;
}

</style>
@endsection

@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">  
        <div class="block">
            <div class="block-title">
                <h2><strong>PEMANTAUAN CPTPP QT</strong></h2>  
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="card"> 
                        <div class="card-header"> 
                        </div>
                        <div class="card-body">
                            <p>Pemauntaun CPTPP QT</p> 
                            <a href="#">
                                <input type="button" style="background-color: black; color: white; padding: 5px" id="download" name="download" value="DOWNLOAD" />
                            </a>
                            <div class="table-responsive">
                                <table class="table" id="cptpptable">
                                    <thead class="text-darken">
                                        <tr>
                                            <th class="text-left" style="width:5%">No.</th> 
                                            <th class="text-left"><PERSON><PERSON></th>
                                            <th class="text-left">Kod Kementerian</th>
                                            <th class="text-left">Kementerian</th>
                                            <th class="text-left">Kod Kumpulan PTJ</th>
                                            <th class="text-left">Kumpulan PTJ</th>
                                            <th class="text-left">Kod PTJ</th>
                                            <th class="text-left">PTJ</th>
                                            <th class="text-left">Nombor QT</th> 
                                            <th class="text-left">Tajuk QT</th> 
                                            <th class="text-left">Status</th> 
                                            <th class="text-left">Publish Date</th> 
                                            <th class="text-left">Closing Date</th> 
                                        </tr> 
                                    </thead>
                                    <tbody>
                                        @foreach ($data as $indexKey => $row)
                                        <tr>
                                            <td class="text-left"> {{ ++$indexKey }} </td>
                                            <td class="text-left"> {{ $row->jenis_perolehan }} </td>
                                            <td class="text-left"> {{ $row->kod_kementerian }} </td>
                                            <td class="text-left"> {{ $row->kementerian }} </td>
                                            <td class="text-left"> {{ $row->kod_kumpulan_ptj }} </td>
                                            <td class="text-left"> {{ $row->kumpulan_ptj }} </td>
                                            <td class="text-left"> {{ $row->kod_ptj }} </td>
                                            <td class="text-left"> {{ $row->ptj }} </td>
                                            <td class="text-left"> {{ $row->nombor_qt }} </td>
                                            <td class="text-left"> {{ $row->tajuk_qt }} </td>
                                            <td class="text-left"> {{ $row->status_1 }} </td>
                                            <td class="text-left"> {{ $row->publish_date }} </td>
                                            <td class="text-left"> {{ $row->closing_date }} </td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div> 
    </div> 
</div> 
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page --> 

<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();</script>  
<script>

    $('#page-container').removeAttr('class');
    $('#cptpptable').DataTable({
        dom: "Blfrtip",
        buttons: [
            {
                text: 'csv',
                extend: 'csvHtml5',
                exportOptions: {
                    columns: ':visible:not(.not-export-col)'
                }
            },
            {
                text: 'excel',
                extend: 'excelHtml5',
                exportOptions: {
                    columns: ':visible:not(.not-export-col)'
                }
            }
        ],
        order: [[1, "asc"]],
        columnDefs: [],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']],
    });

    $('#download').on('click', function () {
        var table = $('#cptpptable').DataTable();
        table.button('.buttons-csv').trigger();
    });
</script>

@endsection