@extends('layouts.guest-dash')

@section('header')
<style type="text/css">  
    .dt-buttons {
        display: none;
    }
</style>
@endsection

@section('content')

@if (Auth::user())
<div class="row">
    <div class="block block-alt-noborder full">  
        <div class="block">
            <div class="block-title">
                <h2><strong>S4 CRM CASE MONITORING</strong></h2>  
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="block">
                        <div class="card"> 
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table" id="cptpptable">
                                        <thead class="text-darken">
                                            <tr>	 
                                                <th class="text-left">Case No</th> 
                                                <th class="text-left">Redmine No</th>
                                                <th class="text-left">Subject</th>
                                                <th class="text-left">Description</th>
                                                <th class="text-left">Status</th>
                                            </tr> 
                                        </thead>
                                        <tbody>
                                            @foreach($data as $row)
                                            <tr> 
                                                <td class="text-left"> <a href="{{url('/support/crm/case')}}/?case_number={{ $row->case_number }}" target="_blank">{{ $row->case_number }} </a></td>
                                                <td class="text-left"> {{ $row->redmine_number }} </td>
                                                <td class="text-left"> {{ $row->name }} </td>
                                                <td class="text-left"> {{ $row->description }} </td>
                                                <td class="text-left"> {{ $row->status }} </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div> 
                </div>
            </div>
        </> 
    </div> 
</div> 
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page --> 

<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();</script>  
<script>

    $('#page-container').removeAttr('class');
    $('#cptpptable').DataTable({
        dom: "Blfrtip",
        order: [[0, "asc"]],
        columnDefs: [],
        pageLength: 20,
        lengthMenu: [[20, 30, 50, -1], [20, 30, 50, 'All']],
    });
</script>

@endsection