@extends('layouts.guest-dash')

@section('header')

@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/stl/crm') }}"> SLA CRM/POMS</a>
            </li>
            <li>
                <a href="{{ url('/stl/crm/duplicatetask') }}"> DUPLICATE TASKS</a>
            </li>
            <li>
                <a href="{{ url('/stl/crm/sla') }}"> TASK FLOW</a>
            </li>
        </ul>
    </div>
</div>
@if (Auth::user())
<div class="row">
    <form action=" {{ url('/stl/crm') }}" method="post" class="form-horizontal form-bordered">
        {{ csrf_field() }}
        <div class="col-lg-12">
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-6 control-label" for="dateStart">Date Start</label>
                        <div class="col-md-6">
                            <input type="date" id="dateStart" name="dateStart" class="form-control" value="{{ $dateStart }}">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <label class="col-md-6 control-label" for="dateEnd">Date End</label>
                        <div class="col-md-6">
                            <input type="date" id="dateEnd" name="dateEnd" class="form-control" value="{{ $dateEnd }}">
                        </div>
                    </div>
                </fieldset>
            </div>
            <div class="col-md-4">
                <fieldset>
                    <div class="form-group">
                        <div class="col-md-6">
                            <button type="submit" class="btn btn-sm btn-primary pull-right"><i class="fa fa-search"></i> Search </button>
                        </div>
                    </div>
                </fieldset>
            </div>
        </div>
    </form>  
</div>
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class="widget-extra themed-background-dark">
                <h5 class='widget-content-light'>
                    SLA - <strong>IT Incident</strong>
                </h5>
            </div>
            <div class="block">
                <table class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-left">SLA</th>
                            <th class="text-center">TOTAL CRM</th>
                            <th class="text-center">TOTAL CRM (Null SLA)</th>
                            <th class="text-center">TOTAL POMS</th>
                            <th class="text-center">TOTAL DIFFERENCE</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if(isset($data))
                        @foreach($data as $key=>$val)
                        <tr>
                            <td class="text-left"> {{ $val->name }}</td>
                            <td class="text-center"> {{ $val->total }}</td>
                            @if($val->bugs > 0)
                            <td class="text-center detail" data-id="{{ $val->name }}" data-target="case-detail"><strong><a style="color: red">{{ $val->bugs  }} </a></strong></td> 
                            @else 
                            <td class="text-center">{{ $val->bugs  }} </td> 
                            @endif
                            <td class="text-center"> {{ $val->poms }}</td>
                            <td class="text-center">@if((-($val->poms - $val->total) > 0))<strong class="text-danger">{{ -($val->poms - $val->total) }}</strong> @else {{ $val->poms - $val->total }}@endif</td>

                        </tr>
                        @endforeach
                        @endif 
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div class="row" id="case-row" style="display: none">
    <div class="col-lg-12" id="case_detail_div">
        <div class="widget">
            <div class="widget-extra themed-background-coral">
                <h5 class='widget-content-light'>
                    CASE CRM - <strong>WITH NULL SLA STOP</strong>
                </h5>
            </div>
            <table class="table table table-vcenter table-striped" id="case-table">
                <thead>
                    <tr>
                        <th class="text-center">Case No.</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Modified Date</th>
                        <th class="text-center">Status</th>
                        <th class="text-center">Request Type</th>
                        <th class="text-center">Incident Type</th>
                        <th class="text-center">Contact Mode</th>
                        <th class="text-center">SLA Start</th>
                        <th class="text-center">SLA Stop</th>
                        <th class="text-center"></th>
                    </tr>
                </thead>
                <tbody id="case-body">

                </tbody>
            </table>
        </div>
    </div>
</div>
<div id="myModal" class="modal fade" role="dialog">
    <div class="modal-dialog modal-lg">
        <!-- Modal content-->
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal">&times;</button>
                <h4 class="modal-title text-center">UPDATE SLA CASE</h4>
            </div>
            <div class="modal-body">
                <form id="form-search-task" action="{{url("/stl/crm/update")}}" method="post" class="form-horizontal form-bordered">
                    {{ csrf_field() }}
                    <input type="hidden" id="slatype" name="slatype" value="" class="form-control"/>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="caseNumber">Case Number</label> 
                                    <div class="input-group">
                                        <input type="text" id="caseNumber" name="caseNumber" class="form-control" readonly="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="requestType">Request Type</label> 
                                    <div class="input-group">
                                        <input type="datetime" id="requestType" name="requestType" class="form-control" readonly="true"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="createdDate">Created Date</label> 
                                    <div class="input-group">
                                        <input type="datetime" id="createdDate" name="createdDate" class="form-control" readonly="true"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="modifiedDate">Modified Date</label> 
                                    <div class="input-group">
                                        <input type="text" id="modifiedDate" name="modifiedDate" class="form-control" readonly="true"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-md-2 control-label" for="contactMode">Contact Mode</label> 
                                    <div class="input-group">
                                        <input type="text" id="contactMode" name="contactMode" class="form-control" readonly="true"/>
                                    </div>
                                </div>
                            </div
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <center>
                                <button type="button" class="btn btn-xs btn-info" id="updateCase" name="updateReport" disabled="true">Update</button><br/><br/>
                            </center>
                        </div>
                    </div>
                </form>                
            </div>
        </div>
    </div>
</div>
</div>
@endif
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script>
    $('.detail').click(function () {
        $('#case-row').show();

        var slaName = $(this).attr('data-id');
        var Cs = <?php echo json_encode($CS); ?>;

        if (slaName === 'CS') {
            var html = '';
            $.each(Cs, function (index, value) {

                html += '<tbody><tr><td class="text-center">' + value["case_number"] + '</td>';
                html += '<td class="text-center">' + value["created_date"] + '</td>';
                html += '<td class="text-center">' + value["date_modified"] + '</td>';
                html += '<td class="text-center">' + value["case_status"] + '</td>';
                html += '<td class="text-center">' + value["request_type"] + '</td>';
                html += '<td class="text-center">' + value["type_of_incident"] + '</td>';
                html += '<td class="text-center">' + value["contact_mode"] + '</td>';
                html += '<td class="text-center">' + value["cs_actual_start_datetime"] + '</td>';
                html += '<td class="text-center">' + value["cs_completed_datetime"] + '</td>';
                html += '<td class="text-center"><div class="btn-group"><a data-toggle="modal" data-id="' + value["case_number"] +
                        '" data-value="' + value["request_type"] +
                        '" data-createddate="' + value["created_date"] +
                        '" data-modifieddate="' + value["date_modified"] +
                        '" data-slatype="' + slaName +
                        '" data-contactmode="' + value["contact_mode"] +
                        '" title="ReportDetails" class="myModal btn btn-xs btn-primary" href="#myModal">Detail</a></td></tr></tbody>';
            });
            document.getElementById("case-body").innerHTML = html;
        }

    });
</script>
<script>
    $(document).on("click", ".myModal", function () {

        $('#myModal').modal('show');

        $(".modal-body #caseNumber").val($(this).data('id'));
        $(".modal-body #requestType").val($(this).data('value'));
        $(".modal-body #createdDate").val($(this).data('createddate'));
        $(".modal-body #modifiedDate").val($(this).data('modifieddate'));
        $(".modal-body #slatype").val($(this).data('slatype'));
        $(".modal-body #contactMode").val($(this).data('contactmode'));

        console.log($(this).data('slatype'));
    });

</script>
<script>
    $('#updateCase').on('click', function () {

        var caseNumber = $("input[name=caseNumber]").val();
        var requestType = $("input[name=requestType]").val();
        var createdDate = $("input[name=createdDate]").val();
        var modifiedDate = $("input[name=modifiedDate]").val();
        var slaType = $("input[name=slatype]").val();
        var contactMode = $("input[name=contactMode]").val();
        var csrf = $("input[name=_token]").val();

        console.log(slaType);
        $.ajax({
            type: "POST",
            url: "/stl/crm/update",
            data: {"_token": csrf, "caseNumber": caseNumber, "requestType": requestType,
                "createdDate": createdDate, "modifiedDate": modifiedDate, "slaType": slaType,
                "contactMode": contactMode}
        }).done(function (resp) {
            $('#myModal').modal('hide');
            if (resp === 1) {
                setTimeout(location.reload.bind(location), 2000);
            }
        });


    });
</script>
@endsection
