<?php

/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 2/27/2018
 * Time: 12:46 PM
 */

namespace App\Http\Controllers;

use App\Services\EPService;
use App\EpSupportMonitoringQtEp;
use App\Services\Traits\SupplierService;
use App\Services\Traits\GFMASService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\SourcingService;
use App\Services\Traits\SSHService;
use App\Services\Traits\OrganizationService;
use Carbon\Carbon;
use SSH;
use DB;
use Excel;

class DashboardController extends Controller
{
    use SupplierService;
    use GFMASService;
    use OSBService;
    use ProfileService;
    use SSHService;
    use FulfilmentService;
    use SourcingService;
    use OrganizationService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function getDashboardGfmas()
    {
        $serviceCode = 'GFM-010%';
        //        $list = $this->getDashboardGfmasAll($serviceCode);
        return $this->getDashboardDetail();
    }

    public function getDashboardDetail()
    {
        return view('dashboard_gfmas', []);
    }

    public function getDashboardApiveOutbound()
    {
        $dataGfm010 = $this->getDashboardGfmasByServiceCode('GFM-010%');
        $apiveFiles = $this->getListAPIVEOutFolder();
        $totalApiveOutPending = count($apiveFiles);
        if ($dataGfm010) {
            $dataGfm010[0]->totalPercent = round($dataGfm010[0]->apive_1gfmas / 999 * 100);
            //$dataGfm010[0]->totalPercent = 52;
            if ($dataGfm010[0]->totalPercent > 50 && $dataGfm010[0]->totalPercent < 90) {
                $chartColor = '#f39c12';
            } elseif ($dataGfm010[0]->totalPercent >= 90) {
                $chartColor = '#c0392b';
            } else {
                $chartColor = '#2ecc71';
            }
            $dataGfm010[0]->chartColor = $chartColor;

            $dataGfm010[0]->dates = Carbon::parse($dataGfm010[0]->dates)->format('d/m/Y');
        } else {
            $dataGfm010[] = (object)array(
                'service_code' => 'GFM-370',
                'trans_type' => '-',
                'dates' => '-',
                'apive_1gfmas' => 0,
                'totalPercent' => 0,
                'chartColor' => '#fff',
            );
        }

        $html = "
            <!-- Dashboard Info Title -->
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    1GFMAS MasterDataVendor APIVE <strong>(Outbound)</strong>
                </h5>
            </div>
            <!-- END Dashboard Info Title -->

            <!-- Dashboard Info -->
            <div class='block-section text-center' style='padding-top: 20px;'>
                <div class='pie-chart block-section' data-percent='{$dataGfm010[0]->totalPercent}'
                     data-size='90' data-line-width='2'
                     data-bar-color='{$dataGfm010[0]->chartColor}' data-track-color='#ffffff'>
                    <span>{$dataGfm010[0]->totalPercent}%</span>
                </div>
            </div>
            <table class='table table-borderless table-striped table-vcenter'>
                <tbody>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                    <td><strong>{$dataGfm010[0]->service_code}</strong></td>
                </tr>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                    <td>{$dataGfm010[0]->trans_type}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Dates</strong></td>
                    <td>{$dataGfm010[0]->dates}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Transactions</strong></td>
                    <td><strong>{$dataGfm010[0]->apive_1gfmas}</strong> /999</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Pending (OUT Folder)</strong></td>
                    <td><strong><a href='#modal-list-data' 
                        class='modal-list-data-action label label-danger' 
                        data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-apive-outbound'
                        data-title='List Pending Transactions APIVE' >{$totalApiveOutPending}</a></strong></td>
                </tr>
                </tbody>
            </table>
            <!-- END Dashboard Info -->";

        return $html;
    }

    public function getDashboardApoveInbound()
    {
        $dataGfm370 = $this->getDashboardGfmasByServiceCode('GFM-370%');
        $apoveFiles = $this->getListAPOVEInFolder();
        $totalApoveInPending = count($apoveFiles);
        if ($dataGfm370) {
            $dataGfm370[0]->dates = Carbon::parse($dataGfm370[0]->dates)->format('d/m/Y');
        } else {
            $dataGfm370[] = (object)array(
                'service_code' => 'GFM-370',
                'trans_type' => '-',
                'dates' => '-',
                'apive_1gfmas' => 0,
            );
        }

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS MasterDataVendor APOVE <strong>(Inbound)</strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                        <td><strong>{$dataGfm370[0]->service_code}</strong></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                        <td>{$dataGfm370[0]->trans_type}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Dates</strong></td>
                        <td>{$dataGfm370[0]->dates}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Transactions</strong></td>
                        <td><strong>{$dataGfm370[0]->apive_1gfmas}</strong></td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>Pending (IN Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-apove-inbound'
                            data-title='List Pending Transactions APOVE' >{$totalApoveInPending}</a></strong></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

    public function getDashboard1GfmasOutbound()
    {
        $outboundFiles = $this->getList1GfmasFolderOUT();
        $totalOutboundFilesPending = count($outboundFiles);

        $inbound1GFMASServerFiles = $this->getList1GfmasServerFolderIN();
        $inbound1GFMASServerFilesPending = count($inbound1GFMASServerFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP transfer files to IGFMAS</small></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (IN 1GFMAS Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/1gfmas-inbound'
                            data-title='List Pending Files (IN 1GFMAS  FOLDER)' >{$inbound1GFMASServerFilesPending}</a></strong> <small class='text-info' style='font-style: italic;'>Waiting 1GFMAS to process files </small></td>
                    </tr>
                    
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

    public function getDashboard1GfmasInbound()
    {
        $inboundFiles = $this->getList1GfmasFolderIN();
        $totalinboundFilesPending = count($inboundFiles);

        $outbound1GFMASServerFiles = $this->getList1GfmasServerFolderOUT();
        $outbound1GFMASServerFilesPending = count($outbound1GFMASServerFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        1GFMAS Monitoring <strong>(INBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT 1GFMAS Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/1gfmas-outbound'
                            data-title='List Pending Files (OUT 1GFMAS FOLDER)' >{$outbound1GFMASServerFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP to pick up files</small></td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (IN eP FOLDER)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-inbound'
                            data-title='List Pending Files (IN eP FOLDER)' >{$totalinboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP to process files</small></td>
                    </tr>
                    
                    
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

    public function getDashboardQuartz()
    {
        $dataFired = $this->getDashboardQuartzFired();
        $dataExecution = $this->getDashboardQuartzExecution();

        if ($dataFired) {
            $dataFired[0]->prev_fired = Carbon::parse($dataFired[0]->prev_fired)->format('d/m/Y H:i:s A');
            $dataFired[0]->next_fired = Carbon::parse($dataFired[0]->next_fired)->format('d/m/Y H:i:s A');
            if ($dataFired[0]->trigger_state === 'WAITING') {
                $dataFired[0]->state_color = 'label-warning';
            } elseif ($dataFired[0]->trigger_state === 'BLOCKED') {
                $dataFired[0]->state_color = 'label-danger';
            } else {
                $dataFired[0]->state_color = 'label-info';
            }
        }

        if ($dataExecution) {
            $dataExecution[0]->finish_execution_date = Carbon::parse($dataExecution[0]->finish_execution_date)->format('d/m/Y H:i:s A');
        }
        $html = '';
        if (count($dataFired) > 0) {
            $html = "
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    Quartz <strong>Log</strong>
                </h5>
            </div>
            <!-- END Dashboard Info Title -->

            <!-- Dashboard Info -->
            <table class='table table-borderless table-striped table-vcenter'>
                <tbody>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Service Code</strong></td>
                    <td><strong>{$dataFired[0]->job_group}</strong></td>
                </tr>
                <tr>
                    <td class='text-right' style='width: 50%;'><strong>Transaction Type</strong></td>
                    <td>{$dataFired[0]->job_name}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Trigger State</strong></td>
                    <td><span class='label {$dataFired[0]->state_color}'>{$dataFired[0]->trigger_state}</span></td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Previous Fired</strong></td>
                    <td>{$dataFired[0]->prev_fired}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Next Fire</strong></td>
                    <td>{$dataFired[0]->next_fired}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Finish Execution Date</strong></td>
                    <td>{$dataExecution[0]->finish_execution_date}</td>
                </tr>
                <tr>
                    <td class='text-right'><strong>Duration</strong></td>
                    <td>{$dataExecution[0]->duration}</td>
                </tr>
                </tbody>
            </table>
            <!-- END Dashboard Info -->";
        }
        return $html;
    }

    public function getDashboardDiInterfaceLogApive()
    {
        $data = $this->getDashboardDiInterfaceLog();

        if ($data) {
        } else {
        }

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        Di Interface Log - <strong>APIVE (Last Execute)</strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Start Time</strong></td>
                        <td>{$data[0]->data_start_time}</td>
                    </tr>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>End Time</strong></td>
                        <td>{$data[0]->data_end_time}</td>
                    </tr>
                    <tr>
                        <td class='text-right'><strong>File Name</strong></td>
                        <td>{$data[0]->file_name}</td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

    public function checkQtMonitoring()
    {
        $listQtPublished = $this->getListQtPublished();
        $totalQtPublished = count($listQtPublished);

        $listQtClosing = $this->getListQtClosing();
        $totalQtClosing = count($listQtClosing);

        $listQtPendingReschedulePublication = $this->getListQtPendingReschedulePublication();
        $totalQtPendingReschedulePublication = count($listQtPendingReschedulePublication);

        $listQtPendingRescheduleProposalClosingDate = $this->getListQtPendingRescheduleProposalClosingDate();
        $totalQtPendingRescheduleProposalClosingDate = count($listQtPendingRescheduleProposalClosingDate);


        $totalAccumulatedQtPublished = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_PUBLISHED')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        $totalAccumulatedQtClosing = EpSupportMonitoringQtEp::where('qt_monitor_type', 'QT_CLOSING')
            ->whereDate('qt_date', Carbon::now()->format('Y-m-d'))
            ->count();

        //text-warning  //text-danger
        $colorFontClassPublished = "text-info";
        $colorFontClassClosing = "text-info";
        if (Carbon::now()->hour >= 12) {
            if ($totalQtPublished > 0) {
                $colorFontClassPublished = "text-danger";
            } else {
                $colorFontClassPublished = "text-success";
            }

            if ($totalQtClosing > 0) {
                $colorFontClassClosing = "text-danger";
            } else {
                $colorFontClassClosing = "text-success";
            }
        }

        $clrFontTotalQtPendingReschedulePublication = "text-success";
        $clrFontTotalQtPendingRescheduleProposalClosingDate = "text-success";
        if ($totalQtPendingReschedulePublication > 0) {
            $clrFontTotalQtPendingReschedulePublication = "text-danger";
        }
        if ($totalQtPendingRescheduleProposalClosingDate > 0) {
            $clrFontTotalQtPendingRescheduleProposalClosingDate = "text-danger";
        }


        $html = "";

        $html .= "<div class='row text-center'>";
        $html .= "<div class='col-xs-6'>
                    <h3>
                    <strong class='$colorFontClassPublished' title='QT must be clear after 12PM. If total QT still exist, Ask Technical team to recover. '>$totalQtPublished</strong> <small>/$totalAccumulatedQtPublished</small><br>
                        <small><i class='fa fa-thumbs-up'></i> Published</small>
                    </h3>
                </div>";
        $html .= "<div class='col-xs-6'>
                    <h3>
                        <strong class='$colorFontClassClosing' title='QT must be clear after 12PM. If total QT still exist, Ask Technical team to recover. '>$totalQtClosing</strong> <small>/$totalAccumulatedQtClosing</small><br>
                        <small><i class='fa fa-power-off'></i> Closing</small>
                    </h3>
                </div>";
        $html .= "</div>";

        $html .= "<div class='row text-center'>";
        $html .= "<div class='col-xs-6'>
                    <h3>
                    <strong class='$clrFontTotalQtPendingReschedulePublication' title='If total QT still exist, Ask Technical team to recover. '>$totalQtPendingReschedulePublication</strong><br>
                        <small> Pending Reschedule Publication</small>
                    </h3>
                </div>";
        $html .= "<div class='col-xs-6'>
                    <h3>
                        <strong class='$clrFontTotalQtPendingRescheduleProposalClosingDate' title='If total QT still exist, Ask Technical team to recover. '>$totalQtPendingRescheduleProposalClosingDate</strong><br>
                        <small> Pending Reschedule Proposal Closing Date</small>
                    </h3>
                </div>";
        $html .= "</div>";


        return $html;
    }

    public function getQTPublishedByDay()
    {
        $list = $this->getListQtPublishedByDay();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th class='text-center'>QT Published Date</th>
                        <th class='text-center'>Status</th>
                        <th class='text-center'>Total QT</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 30%;' class='text-center'><strong>$data->publish_date</strong></td>
                <td class='text-center'>$data->qt_status_id - $data->qt_status</td>
                <td class='text-center'><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/checkQtMonitoringPublished/find/$data->publish_date'
                            data-title='List Published QT By Ministry' >$data->total</a></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkQtMonitoringAsCloseTooEarly()
    {
        $list = $this->getDashboardQtListStatusClosedTooEarly();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th class='text-center'>QT ID</th>
                        <th class='text-center'>QT No.</th>
                        <th class='text-center'>Status ID</th>
                        <th class='text-center'>Status Name</th>
                        <th class='text-center'>QT Created Date</th>
                        <th class='text-center'>QT Published Date</th>
                        <th class='text-center'>QT Closing Date</th>
                        <th class='text-center'>QT Created By Date</th>
                        
                    </tr>
                </thead>
                <tbody>";
        if (count($list) > 0) {
            foreach ($list as $data) {
                $html .= "
                <tr>
                    <td class='text-center'><strong>$data->qt_id</strong></td>
                    <td class='text-center'><strong><a href='" . url('/find/qt/summary') . "/$data->qt_no' target='_blank'>$data->qt_no</a></strong></td>
                    <td class='text-center'><strong>$data->status_id</strong></td>
                    <td class='text-center'><strong>$data->status_name</strong></td>   
                    <td class='text-center'><strong>$data->created_date</strong></td>
                    <td class='text-center'><strong>$data->publish_date</strong></td>   
                    <td class='text-center'><strong>$data->closing_date</strong></td>
                    <td class='text-center'><strong>$data->created_by</strong></td>    
                </tr>";
            }
        } else {
            $html .= "
                <tr>
                    <td colspan='8' class='text-center'><strong>Tiada Rekod</strong></td>    
                </tr>";
        }
        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    /**
     * Refer: Display for POPUP page **/
    public function getQTPublishedByMinistry($date)
    {
        if ($date == '') {
            $list = array();
        } else {
            $list = $this->getListQtPublishedByMinistry($date);
        }
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>Published Date</th>
                            <th class='text-left'>Ministry</th>
                            <th class='text-center'>Total QT</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";

        foreach ($list as $data) {
            $url = url('/find/osb/batch/file');
            $data =  "
                    <tr>
                        <td class='text-center'><strong>$data->publish_date</strong></td>
                        <td class='text-left'>$data->ministry_code - $data->ministry_name</td>
                        <td class='text-center'>$data->total</td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody>";


        return $html;
    }

    public function getQTClosingByDay()
    {
        $list = $this->getListQtClosingByDay();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th class='text-center'>QT Closing Date</th>
                        <th class='text-center'>Status</th>
                        <th class='text-center'>Total QT</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td style='width: 30%;' class='text-center'><strong>$data->closing_date</strong></td>
                <td class='text-center'>$data->qt_status_id - $data->qt_status</td>
                <td class='text-center'><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/dashboard/checkQtMonitoringClosing/find/$data->closing_date'
                            data-title='List Closing QT By Ministry' >$data->total</a></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }


    /**
     * Refer: Display for POPUP page **/
    public function getQTClosingByMinistry($date)
    {
        if ($date == '') {
            $list = array();
        } else {
            $list = $this->getListQtClosingByMinistry($date);
        }
        $html =    "<thead>
                        <tr>
                            <th class='text-center'>Closing Date</th>
                            <th class='text-left'>Ministry</th>
                            <th class='text-center'>Total QT</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";

        foreach ($list as $data) {
            $url = url('/find/osb/batch/file');
            $data =  "
                    <tr>
                        <td  class='text-center'><strong>$data->closing_date</strong></td>
                        <td class='text-left'>$data->ministry_code - $data->ministry_name</td> 
                        <td class='text-center'>$data->total</td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody>";


        return $html;
    }


    public function checkEjbOsb()
    {
        $list = $this->getDashboardEjbOsb();
        //        dd($list);
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Trans Date</th>
                        <th>Total Invoke</th>
                        <th>No EJB Receiver</th>
                        <th>EJB Timeout</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if ($data->no_ejb_receiver > 0) {
                $data->no_ejb_receiver = "<a href='/find/1gfmas/ws/log/checkWsOsbEjbNoReceiver' target='_blank' ><span class='badge label-danger'><strong>$data->no_ejb_receiver</strong></span></a>";
            }
            if ($data->ejb_timeout > 0) {
                $data->ejb_timeout = "<a href='/find/1gfmas/ws/log/checkWsOsbEjbTimeOut' target='_blank' ><span class='badge label-danger'><strong>$data->ejb_timeout</strong></span></a>";
            }
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->total_invoke</td>
                <td class='text-center'>$data->no_ejb_receiver</td>
                <td class='text-center'>$data->ejb_timeout</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkWsValidationException()
    {
        $list = $this->getDashboardStatisticWsValidationException();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Total Records</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsValidation' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";

            $html .= "
            <tr>
                <td style='width: 60%;' class='text-center'><strong>$data->trans_date</strong></td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }



    public function checkOsbRetry()
    {
        $collect = collect([]);
        $list = $this->getDashboardOsbRetry();
        $html = "";
        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Service Name</th>
                        <th>Target System</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if ($data->counts > 0) {
                $data->counts = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/osb/batch/retry/$data->service_name'
                            data-title='Details of OSB Service Retry' >{$data->counts}</a>";
            }
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->service_name</strong></td>
                <td>$data->target_system</td>
                <td class='text-center'><span class='badge label-danger'>$data->counts</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";

        $collect->put('data', $html);

        $totalcounts = $this->getTotalDashboardOsbRetry();
        $collect->put('totalcounts', $totalcounts);

        return $collect;
    }

    public function listSqCloseTimeDaily()
    {


        $list = $this->getListSqCloseTimeDaily();
        $html = "";

        $html .= "
        <thead>
                    <tr> 
                        <th style='width: 15%;'>No.</th>
                        <th>SQ Closed Time</th>
                        <th class='text-center'>Total SQ</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $html .= "
            <tr>
                <td><strong>$data->counter</strong></td>
                <td><strong>$data->time_closed</strong></td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>";



        return $html;
    }

    public function checkWsItemCodeErrorInGFM100()
    {
        $list = $this->getDashboardStatisticWsItemCodeInGFM100();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Error 'Tidak Wujud' </th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsItemCodeErrorInGFM100' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";

            $html .= "
            <tr>
                <td  class='text-left'><strong>$data->trans_date</strong></td>
                <td style='width: 40%;' class='text-left'>$data->error_type</td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkWsItemCodeErrorInMMINF()
    {
        $list = $this->getDashboardStatisticWsItemCodeInMMINF();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Transaction Date</th>
                        <th>Error Type</th>
                        <th>Transactions</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            $data->total_desc = "<a href='/find/1gfmas/ws/log/checkWsItemCodeErrorInMMINF' target='_blank' ><span class='badge label-danger'><strong>$data->total</strong></span></a>";

            $typeError = $data->error_type;
            $type = '';

            if (strpos($typeError, 'Maklumat') !== false) {
                $type = 'CRE : Item sudah wujud';
            } else if (strpos($typeError, 'KOD ITEM') !== false) {
                $type = 'Locked! Cuba semula';
            } else if (strpos($typeError, 'Kemaskin') !== false) {
                $type = 'ADJ : Item tidak wujud';
            } else {
                $type = $data->error_type;
            }

            $html .= "
            <tr>
                <td  class='text-left'><strong>$data->trans_date</strong></td>
                <td style='width: 40%;' class='text-left'>$type</td>
                <td class='text-center'>$data->total_desc</td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    //    public function checkBatchFilePending(){
    //        $list = $this->getDashboardBatchFilePending(); 
    //        
    //        
    //        
    //        $html = "";
    //        $html .= "
    //        <div>
    //            <table class='table table-borderless table-striped table-vcenter'>
    //                <thead>
    //                    <tr>
    //                        <th>Process ID</th>
    //                        <th>Process name</th>
    //                        <th>Total Pending</th>
    //                    </tr>
    //                </thead>
    //                <tbody>";
    //
    //        
    //            $html .= "
    //            <tr>
    //                
    //                <tr>
    //                        <td class='text-right' style='width: 50%;'><strong>$data->process_name</strong></td>
    //                            
    //                        <td><strong><a href='#modal-list-data' 
    //                            class='modal-list-data-action label label-danger' 
    //                            data-title='List Pending Files in Processing to eP' >$data->total</a></strong></td>
    //                    </tr>
    //            </tr>";
    //        
    //        
    //        $html .= "
    //                </tbody>
    //            </table>
    //        </div>";
    //
    //
    //        return $html;
    //    }

    public function backlogmminf()
    {
        $list = $this->getListBackLogMMINF();
        $carbonNow = Carbon::now();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
              
                <tbody>";

        foreach ($list as $data) {
            if ($data->total > 0) {

                $html .= "
             <tr>
                        <td class='text-left' style='width: 70%;'><strong>Total (Pending send to IGFMAS) </strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/support/report/log/di-mminf/$carbonNow'
                            data-title='List MMINF on pending send to 1GFMAS' >{$data->total}</a></strong></td>
            </tr>";
            }
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function listPendingUserCreation()
    {

        $list = $this->getPendingUserCreation();
        $html = "";

        $html .= "
        <thead>
                    <tr> 
                        <th class='text-center'>Pending Liferay Id</th>
                        <th class='text-center'>Changed Date</th>
                        <th class='text-center'>User Id</th>
                        <th class='text-center'>Login Id</th>
                        <th class='text-center'>User Name</th>
                        <th class='text-center'>Identification No.</th>
                    </tr>
                </thead>
                <tbody>";

        if (count($list) > 0) {
            foreach ($list as $data) {

                $html .= "
                <tr>
                    <td class='text-center'><a href='" . url('/find/patch-ep') . "?patch=PM_PENDING_LIFERAY&record=" . $data->pending_liferay_id . "' target='_blank'>" . $data->pending_liferay_id . "</a></strong></td>
                    <td class='text-center'>$data->changed_date</td>
                    <td class='text-center'>$data->user_id</td>
                    <td class='text-center'>$data->login_id</td>
                    <td class='text-center'>$data->user_name</td>
                    <td class='text-left'><strong><a href='" . url('/find/icno') . "/" . $data->identification_no . "' target='_blank'>" . $data->identification_no . "</a></strong></td>
                </tr>";
            }
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function listStuckUserCreation()
    {

        $list = $this->getStuckUserCreation();
        $html = "";

        $html .= "
        <thead>
                    <tr> 
                        <th class='text-center'>Pending Liferay Id</th>
                        <th class='text-center'>Changed Date</th>
                        <th class='text-center'>User Id</th>
                        <th class='text-center'>Login Id</th>
                        <th class='text-center'>User Name</th>
                        <th class='text-center'>Identification No.</th>
                    </tr>
                </thead>
                <tbody>";

        if (count($list) > 0) {
            foreach ($list as $data) {

                $html .= "
                <tr>
                    <td class='text-center'><a href='" . url('/find/patch-ep') . "?patch=PM_PENDING_LIFERAY&record=" . $data->pending_liferay_id . "' target='_blank'>" . $data->pending_liferay_id . "</a></strong></td>
                    <td class='text-center'>$data->changed_date</td>
                    <td class='text-center'>$data->user_id</td>
                    <td class='text-center'>$data->login_id</td>
                    <td class='text-center'>$data->user_name</td>
                    <td class='text-left'><strong><a href='" . url('/find/icno') . "/" . $data->identification_no . "' target='_blank'>" . $data->identification_no . "</a></strong></td>
                </tr>";
            }
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function listPendingProcessPayment()
    {

        $list = $this->getPendingProcessPayment();
        $html = "";

        $html .= "
        <thead>
                    <tr> 
                        <th class='text-center'>Id</th>
                        <th class='text-center'>Order Id</th>
                        <th class='text-center'>Change Date</th>
                        <th class='text-center'>Appl Id</th>
                        <th class='text-center'>Appl No</th>
                        <th class='text-center'>Company Name</th>
                        <th class='text-center'>Ep No.</th>
                        <th class='text-center'>PY Change Date</th>
                        <th class='text-center'>Payment Date.</th>
                        <th class='text-center'>Payment Amount</th>
                    </tr>
                </thead>
                <tbody>";

        if (count($list) > 0) {
            foreach ($list as $data) {
                $datePayment = Carbon::parse($data->payment_date)->format('Y-m-d');
                $html .= "
                <tr>
                    <td class='text-center'><a href='" . url('/find/patch-ep') . "?patch=SM_PENDING_PROCESS&record=" . $data->pending_process_id . "' target='_blank'>" . $data->pending_process_id . "</a></strong></td>
                    <td class='text-center'>$data->payment_id</td>
                    <td class='text-center'>$data->changed_date</td>
                    <td class='text-center'>$data->appl_id</td>
                    <td class='text-center'><a href='" . url('/find/trans/track/docno/') . "/" . $data->appl_no . "' target='_blank'>" . $data->appl_no . "</a></strong></td>
                    <td class='text-center'>$data->company_name</td>
                    <td class='text-left'><strong><a href='" . url('/find/epno/') . "/" . $data->ep_no . "' target='_blank'>" . $data->ep_no . "</a></strong></td>
                    <td class='text-center'>$data->py_changed_date</td>
                    <td class='text-center'>$datePayment</td>
                    <td class='text-center'>$data->payment_amt</td>
                </tr>";
            }
        }

        $html .= "
                </tbody>";

        return $html;
    }

    public function listStuckProcessPayment()
    {

        $list = $this->getStuckProcessPayment();
        $html = "";

        $html .= "
            <thead>
                <tr> 
                    <th class='text-center'>Id</th>
                    <th class='text-center'>Order Id</th>
                    <th class='text-center'>Change Date</th>
                    <th class='text-center'>Appl Id</th>
                    <th class='text-center'>Appl No</th>
                    <th class='text-center'>Company Name</th>
                    <th class='text-center'>Ep No.</th>
                    <th class='text-center'>PY Change Date</th>
                    <th class='text-center'>Payment Date.</th>
                    <th class='text-center'>Payment Amount</th>
                </tr>
            </thead>
        <tbody>";

        if (count($list) > 0) {
            foreach ($list as $data) {
                $datePayment = Carbon::parse($data->payment_date)->format('Y-m-d');

                // /bpm/sm/task/find?appl_no=KN-08052023-00110
                $linkRefire = "";
                If($data->payment_amt == 120){
                    $linkRefire = "<a href='" . url('/bpm/sm/task/find')."?appl_no=".$data->appl_no ."' target='_blank'> Refire Softcert Payment Task </a>";
                }
                $html .= "
                <tr>
                   <td class='text-center'><a href='" . url('/find/patch-ep') . "?patch=SM_PENDING_PROCESS&record=" . $data->pending_process_id . "' target='_blank'>" . $data->pending_process_id . "</a></strong></td>
                   <td class='text-center'>$data->payment_id</td>
                   <td class='text-center'>$data->changed_date</td>
                   <td class='text-center'>$data->appl_id</td>
                   <td class='text-center'><a href='" . url('/find/trans/track/docno/') . "/" . $data->appl_no . "' target='_blank'>" . $data->appl_no . "</a></strong></td>
                   <td class='text-center'>$data->company_name</td>
                   <td class='text-left'><strong><a href='" . url('/find/epno/') . "/" . $data->ep_no . "' target='_blank'>" . $data->ep_no . "</a></strong></td>
                   <td class='text-center'>$data->py_changed_date</td>
                   <td class='text-center'>$datePayment</td>
                   <td class='text-center'>$data->payment_amt  >> $linkRefire 
                   </td>
                </tr>";
            }
        }

        $html .= "
                </tbody>";

        return $html;
    }


    public function getEmailNotificationMonitoring()
    {

        $date1 = Carbon::now();
        $pendingWithinSevenDaysNotifyEmail = $this->getListPendingWithinSevenDaysNotifyEmail("COUNT");
        $totalNotifyMailPending = $pendingWithinSevenDaysNotifyEmail[0]->total;
        //$totalNotifyMailPending = 0;
        $totalNotifyMailPending = $totalNotifyMailPending . '<span style="visibility:hidden;" > Taken time : ' . Carbon::now()->diffForHumans($date1) . '</span>';

        $date2 = Carbon::now();
        $pendingWithinSevenDaysBulkNotifyEmail = $this->getListPendingWithinSevenDaysBulkNotifyEmail("COUNT");
        $totalBulkNotifyMailPending = $pendingWithinSevenDaysBulkNotifyEmail[0]->total;
        //$totalBulkNotifyMailPending = 0;
        $totalBulkNotifyMailPending = $totalBulkNotifyMailPending . '<span style="visibility:hidden;" > Taken time : ' . Carbon::now()->diffForHumans($date2) . '</span>';

        $date3 = Carbon::now();
        $notSendMoreSevenDaysNotifyEmail = $this->getListNotSendMoreSevenDaysNotifyEmail("COUNT");
        $totalNotifyMailNotSend = $notSendMoreSevenDaysNotifyEmail[0]->total;
        //$totalNotifyMailNotSend = 0;
        //$totalNotifyMailNotSend = 'Skip!';
        $totalNotifyMailNotSend = $totalNotifyMailNotSend . '<span style="visibility:hidden;" > Taken time : ' . Carbon::now()->diffForHumans($date3) . '</span>';

        $date4 = Carbon::now();
        $notSendMoreSevenDaysBulkNotifyEmail = $this->getListNotSendMoreSevenDaysBulkNotifyEmail("COUNT");
        $totalBulkNotifyMailNotSend = $notSendMoreSevenDaysBulkNotifyEmail[0]->total;
        //$totalBulkNotifyMailNotSend = 0;
        //$totalBulkNotifyMailNotSend = 'Skip!';
        $totalBulkNotifyMailNotSend = $totalBulkNotifyMailNotSend . '<span style="visibility:hidden;" > Taken time : ' . Carbon::now()->diffForHumans($date4) . '</span>';

        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
              
                <tbody>";

        $html .= "
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Real Time Email Notification Pending In 7 Days. </strong></td>
                               <td><strong>{$totalNotifyMailPending}</strong></td>
                    </tr>
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Bulk Email Notification Pending In 7 Days. </strong></td>
                               <td><strong>{$totalBulkNotifyMailPending}</strong></td>
                    </tr>
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Real Time Email Notification Overdue 7 Days. </strong></td>
                               <td><strong>{$totalNotifyMailNotSend}</strong></td>
                   </tr> 
                   <tr>
                               <td class='text-left' style='width: 70%;'><strong>Bulk Email Notification Overdue 7 Days. </strong></td>
                               <td><strong>{$totalBulkNotifyMailNotSend}</strong></td>
                   </tr>";

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }


    public function getSqNotClosingMonitoring()
    {

        $sqNotClosingOldBpmDP = $this->getSQNotClosingInBpmOldVersion();
        $totalSqNotClosingOldBpmDP = count($sqNotClosingOldBpmDP);

        $sqNotClosingNewBpmDP = $this->getSQNotClosingInBpmNewVersion("COUNT");
        $totalSqNotClosingNewBpmDP = count($sqNotClosingNewBpmDP);


        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
              
                <tbody>";

        $html .= "
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Check Simple Quote not closing for BPM DP < 1.0.7  </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listPendingUserCreation'
                                   data-title='List Total Pending User Creation' >{$totalSqNotClosingOldBpmDP}</a></strong></td>
                    </tr>
                    <tr>
                               <td class='text-left' style='width: 70%;'><strong>Check Simple Quote not closing for BPM DP >= 1.0.7 </strong></td>
                               <td><strong><a href='#modal-list-data' 
                                   class='modal-list-data-action label label-danger' 
                                   data-toggle='modal' data-url='/dashboard/listPendingUserCreation'
                                   data-title='List Total Pending User Creation' >{$totalSqNotClosingNewBpmDP}</a></strong></td>
                    </tr>";

        $html .= "
                </tbody>
            </table>
        </div>";

        return $html;
    }

    public function searchDatatableInePTable()
    {
        return view('dashboard_data_lookup', []);
    }

    public function getTableColumns($tableName)
    {
        $columns = [
            'PM_CITY' => [
                'table_name' => 'PM_CITY',
                'field_allow' => [
                    'city_id' => [
                        'field' => 'city_id',
                        'display' => 'NO',
                    ],
                    'city_code' => [
                        'field' => 'city_code',
                        'display' => 'CITY CODE',
                    ],
                    'city_name' => [
                        'field' => 'city_name',
                        'display' => 'CITY NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_COUNTRY' => [
                'table_name' => 'PM_COUNTRY',
                'field_allow' => [
                    'country_id' => [
                        'field' => 'country_id',
                        'display' => 'NO',
                    ],
                    'country_code' => [
                        'field' => 'country_code',
                        'display' => 'COUNTRY CODE',
                    ],
                    'country_name' => [
                        'field' => 'country_name',
                        'display' => 'COUNTRY NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_DISTRICT' => [
                'table_name' => 'PM_DISTRICT',
                'field_allow' => [
                    'district_id' => [
                        'field' => 'district_id',
                        'display' => 'NO',
                    ],
                    'district_code' => [
                        'field' => 'district_code',
                        'display' => 'DISTRICT CODE',
                    ],
                    'district_name' => [
                        'field' => 'district_name',
                        'display' => 'DISTRICT NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_DIVISION' => [
                'table_name' => 'PM_DIVISION',
                'field_allow' => [
                    'division_id' => [
                        'field' => 'division_id',
                        'display' => 'NO',
                    ],
                    'division_code' => [
                        'field' => 'division_code',
                        'display' => 'DIVISION CODE',
                    ],
                    'division_name' => [
                        'field' => 'division_name',
                        'display' => 'DIVISION NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_SALUTATION' => [
                'table_name' => 'PM_SALUTATION',
                'field_allow' => [
                    'salutation_id' => [
                        'field' => 'salutation_id',
                        'display' => 'NO',
                    ],
                    'salutation_code' => [
                        'field' => 'salutation_code',
                        'display' => 'SALUTATION CODE',
                    ],
                    'salutation_name' => [
                        'field' => 'salutation_name',
                        'display' => 'SALUTATION NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_STATE' => [
                'table_name' => 'PM_STATE',
                'field_allow' => [
                    'state_id' => [
                        'field' => 'state_id',
                        'display' => 'NO',
                    ],
                    'state_code' => [
                        'field' => 'state_code',
                        'display' => 'STATE CODE',
                    ],
                    'state_name' => [
                        'field' => 'state_name',
                        'display' => 'STATE NAME',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
            'PM_UOM' => [
                'table_name' => 'PM_UOM',
                'field_allow' => [
                    'uom_id' => [
                        'field' => 'uom_id',
                        'display' => 'NO',
                    ],
                    'uom_code' => [
                        'field' => 'uom_code',
                        'display' => 'UOM CODE',
                    ],
                    'uom_name' => [
                        'field' => 'uom_name',
                        'display' => 'UOM NAME',
                    ],
                    'decimal_scale' => [
                        'field' => 'decimal_scale',
                        'display' => 'DECIMAL SCALE',
                    ],
                    'record_status' => [
                        'field' => 'record_status',
                        'display' => 'RECORD STATUS',
                        'value_map' => [
                            1 => '1 - ACTIVE',
                            0 => '0 - INACTIVE',
                            9 => '9 - DELETED',
                        ],
                    ],
                ],
            ],
        ];

        if (!array_key_exists($tableName, $columns)) {
            // Log the table name for debugging
            \Log::error('Table not found: ' . $tableName);
            return null;
        }

        return $columns[$tableName];
    }

    public function searchTable($data)
    {
        $tableName = strtoupper($data);
        $columns = $this->getTableColumns($tableName);

        if (!$columns) {
            return response()->json(['error' => 'Invalid table name: ' . $tableName], 400);
        }

        $allowedFields = $columns['field_allow'];
        $columnNames = array_map(fn($field) => $field['field'], $allowedFields);
        $displayNames = array_values(array_map(fn($field) => $field['display'], $allowedFields));

        try {
            $query = DB::connection('oracle_nextgen_rpt')->table($tableName)->select($columnNames)->get();
        } catch (\Exception $e) {
            return response()->json(['error' => 'Error executing query: ' . $e->getMessage()], 500);
        }
        
        $mappedData = $query->map(function ($row) use ($allowedFields) {
            $rowArray = (array) $row;
            foreach ($allowedFields as $key => $field) {
                if (isset($field['value_map']) && isset($rowArray[$key])) {
                    $rowArray[$key] = $field['value_map'][$rowArray[$key]] ?? $rowArray[$key];
                }
            }
            return $rowArray;
        });

        $response = [
            'headers' => $displayNames,
            'data' => $mappedData->values()
        ];

        return response()->json($response);
    }

    public function downloadData($table)
    {
        $table = strtoupper($table);
        $columns = $this->getTableColumns($table);
        if (!$columns) {
            return response()->json(['error' => 'Invalid table name: ' . $table], 400);
        }
        $allowedFields = $columns['field_allow'];
        $columnNames = array_map(fn($field) => $field['field'], $allowedFields);
        $displayNames = array_values(array_map(fn($field) => $field['display'], $allowedFields));

        $listdata = DB::connection('oracle_nextgen_rpt')->table($table)->select($columnNames)->get();

        if ($listdata !== null && $listdata->isNotEmpty()) {

            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300);
            $fileName = $table;
            Excel::create($fileName, function ($excel) use ($fileName, $listdata, $displayNames) {
                $excel->setTitle($fileName);

                $excel->sheet($fileName, function ($sheet) use ($listdata, $displayNames) {
                    $sheet->setOrientation('landscape');
                    $sheet->setStyle([
                        'font' => [
                            'name' => 'Calibri',
                            'size' => 9,
                            'bold' => false
                        ]
                    ]);

                    $sheet->row(1, $displayNames);
                    $sheet->row(1, function ($row) {
                        $row->setBackground('#D9D9D9');
                    });

                    $sheet->setAutoSize(true);

                    $sheet->cells('A1:' . chr(64 + count($displayNames)) . '1', function ($cells) {
                        $cells->setFontColor('#070606');
                        $cells->setFontFamily('Calibri');
                        $cells->setFontSize(9);
                        $cells->setFontWeight('bold');
                        $cells->setBorder('solid', 'solid', 'solid', 'solid');
                    });
                    $count = 2;
                    foreach ($listdata as $obj) {
                        $rowData = [];
                        foreach ($obj as $value) {
                            $rowData[] = $value;
                        }
                        $sheet->row($count, $rowData);
                        $count++;
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));
            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '.xlsx"',
                'Cache-Control' => 'max-age=0',
            ];
            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        } else {
            return response()->json(['error' => 'No data found for this table'], 404);
        }
    }
}
