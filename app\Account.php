<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Account extends Model {
    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;

    public function accountCustom() {
        return $this->hasOne('App\AccountCustom', 'id_c');
    }

    public function contacts() {
        return $this->belongsToMany('App\Contact', 'accounts_contacts', 'account_id', 'contact_id')
                ->withPivot('id');
    }
    
    public function invoices() {
        return $this->belongsToMany('App\Invoices', 'aos_invoices', 'billing_account_id', 'billing_contact_id')
                ->withPivot('id');
    }
}
