@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/qt/committee/')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Ahli Jawatan Kuasa Sebut Harga <br>
                <small>Senarai ahli jawatan kuasa bagi sebut harga.</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian Ahli Jawatan Kuasa Sebut Harga </strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">QT NO.</th>
                            <th class="text-center">COMMITTEE ID</th>
                            <th class="text-center">COMMITTEE TYPE ID</th>
                            <th class="text-center">MEMBER CODE</th>
                            <th class="text-center">MEMBER DESC</th>
                            <th class="text-center">MEMBER NAME</th>
                            <th class="text-center">ICNO / PASSPORT</th>
                            <th class="text-center">ROLE CODE</th>
                            <th class="text-center">ROLE DESC</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->qt_no }}</td>
                                <td class="text-center">{{ $data->committee_id }}</td>
                                <td class="text-left">{{ $data->committee_type_id }}</td>
                                <td class="text-left">{{ $data->member_code }}</td>
                                <td class="text-left">{{ $data->member_desc }}</td>
                                <td class="text-left">{{ $data->member_name }}</td>
                                <td class="text-left">{{ $data->ic_passport }}</td>
                                <td class="text-left">{{ $data->role_code }}</td>
                                <td class="text-left">{{ $data->role_desc }}</td>
                                
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



