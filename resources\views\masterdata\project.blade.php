@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/interfacelog') }}"> INTERFACE LOG </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/ptj') }}"> PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/kumpptj') }}"> KUMP. PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/pegpengawal') }}"> PEG. PENGAWAL </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/vot') }}"> VOT </a>
            </li>
        </ul>
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/glaccount') }}"> GL ACCOUNT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/programactivity') }}"> PROGRAM ACTIVITY </a>
            </li>
            <li class="active">
                <a href="{{ url('/find/masterdata/project') }}"> PROJECT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/agoffice') }}"> AG OFFICE </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/dana') }}"> DANA </a>
            </li>
        </ul>
    </div>
</div>
<div class="widget">
    <div class="block-title widget-extra themed-background-dark">
        <div class="widget-extra themed-background-dark">
            <h5 class='widget-content-light'>
                PROJECT - <strong>Master Data</strong>
            </h5>
        </div>
        <div class="block">
            <form id="form-project" action="{{url("/find/masterdata/project")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST"/>
                <input name="master_data_type" id="master_data_type" type="hidden" value="PROJECT"/>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="project_code">Project Code </label>
                    <div class="col-md-5">
                        <input id="project_code" name="project_code" class="form-control" placeholder="Project Code" type="text"  value="{{ old('project_code') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="project_desc">Project Description </label>
                    <div class="col-md-5">
                        <input id="project_desc" name="project_desc" class="form-control" placeholder="Project Description" type="text" value="{{ old('project_desc') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="created_date">Created Date </label>
                    <div class="col-md-5">
                        <input id="created_date" name="created_date" class="form-control" placeholder="Created Date" type="date"
                               value="{{ $date }}" />  
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @if(isset($result))
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="mastertable-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Project ID</th>
                    <th class="text-center">Project Code</th>
                    <th class="text-center">Project Desc</th>
                    <th class="text-center">Process Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Change Date</th>
                    <th class="text-center">Service Code</th>
                    <th class="text-center">File Name</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($result as $data)
                <tr>
                    <td class="text-center">{{ $data->im_project_id }}</td>
                    <td class="text-center"><a href="{{url("/find/masterdata/ep?code=")}}{{ $data->project_code }}&type=vot" target="_blank">{{ $data->project_code }}</a></td>
                    <td class="text-left">{{ $data->project_desc }}</td>
                    <td class="text-center">{{ $data->process_status }}</td>
                    <td class="text-center">{{ $data->record_status }}</td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center">{{ $data->changed_date }}</td>
                    <td class="text-center">{{ $data->service_code }} - {{ $data->process_id }}</td>
                    <td class="text-center"><a href="{{url("/find/osb/batch/file")}}/{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</div>

@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
    $('#mastertable-datatable').dataTable({
        order: [0, "desc"],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
</script>           
@endsection