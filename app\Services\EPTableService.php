<?php

namespace App\Services;

class EPTableService {

    
    /**
     * Rules validation on handle for String and Integer. 
     * @var type 
     */
    public static $LIST_TABLE = array(
        'CT_ADDRESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'CT_ADDRESS',
            'primary_field' => 'ADDRESS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'address_1' => 
                        array(
                            'field' => 'ADDRESS_1',
                            'display' => 'FIELD ADDRESS 1',
                            'description' => 'MAX VARCHAR(40) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '40',
                            'validation_value'  => [],
                        ),
                ), 
        ),
        'CT_AGREEMENT_CANCEL_TEMP' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'CT_AGREEMENT_CANCEL_TEMP',
            'primary_field' => 'DOC_NO',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'progress_status' => 
                        array(
                            'field' => 'PROGRESS_STATUS',
                            'display' => 'FIELD PROGRESS STATUS',
                            'description' => 'MAX INTEGER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [1,2,3,4,5]
                        ),
                    'is_complete' => 
                        array(
                            'field' => 'IS_COMPLETE',
                            'display' => 'FIELD IS COMPLETE',
                            'description' => 'MAX INTEGER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'creatorby' => 
                        array(
                            'field' => 'CREATORBY',
                            'display' => 'FIELD CREATORBY',
                            'description' => 'MAX VARCHAR(15)',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'CT_CONTRACT' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'CT_CONTRACT',
            'primary_field' => 'CONTRACT_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   
                    'current_contract_ver_id' => 
                        array(
                            'field' => 'CURRENT_CONTRACT_VER_ID',
                            'display' => 'FIELD CURRENT CONTRACT VER ID',
                            'description' => 'MAX NUMBER(12) Please make sure contract ver id is valid key',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => []
                        ),
                    'latest_contract_ver_id' => 
                        array(
                            'field' => 'LATEST_CONTRACT_VER_ID',
                            'display' => 'FIELD LATEST CONTRACT VER ID',
                            'description' => 'MAX NUMBER(12) Please make sure contract ver id is valid key',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => []
                        ),
                        
                ),
        ),
        'CT_CONTRACT_VER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'CT_CONTRACT_VER',
            'primary_field' => 'CONTRACT_VER_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   
                    'contract_physical_no' => 
                        array(
                            'field' => 'CONTRACT_PHYSICAL_NO',
                            'display' => 'FIELD CONTRACT PHYSICAL NO',
                            'description' => 'MAX STRING(255) Please make sure contract physical no is unique.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '255',
                            'validation_value'  => []
                        ),
                        
                ),
        ),
        'CT_WORKFLOW_STATUS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'CT_WORKFLOW_STATUS',
            'primary_field' => 'WORKFLOW_STATUS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'is_current' => 
                        array(
                            'field' => 'IS_CURRENT',
                            'display' => 'FIELD IS CURRENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5). ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [50050,50150,50200,50250,50300,50400,51050,53200,57000,57050,57100,57150,57250,57300,57350,57400,57600,57700,57701]
                            
                        ),
                        
                ),
        ),
        'DI_IM_PTJ' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'DI_IM_PTJ',
            'primary_field' => 'IM_PTJ_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'process_status' => 
                        array(
                            'field' => 'PROCESS_STATUS',
                            'display' => 'FIELD PROCESS STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                ),
        ),
        'DLV_SUBSCRIPTION' => array(
            'database_connection' => 'oracle_nextgen_soa_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'DLV_SUBSCRIPTION',
            'primary_field' => 'CIKEY',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'state' => 
                        array(
                            'field' => 'STATE',
                            'display' => 'FIELD STATE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3],
                            'remarks' => 'This request to allow technical team update STATE as 3 (COMPLETED). To disable similar activity docID when recreate new instance'
                        ),
                ), 
        ),
        'FL_ADJUSTMENT' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_ADJUSTMENT',
            'primary_field' => 'ADJUSTMENT_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                ),
        ),
        'FL_DELIVERY_ADDRESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_DELIVERY_ADDRESS',
            'primary_field' => 'DELIVERY_ADDRESS_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'address_id' => 
                        array(
                            'field' => 'ADDRESS_ID',
                            'display' => 'FIELD ADDRESS ID',
                            'description' => 'MAX NUMBER(12) Please make sure value ID is exist in FL_ADDRESS!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '12',
                            'validation_value'  => []
                        ),
                    'pm_address_id' => 
                        array(
                            'field' => 'PM_ADDRESS_ID',
                            'display' => 'FIELD PM ADDRESS ID',
                            'description' => 'MAX NUMBER(12) Please make sure value ID is exist in PM_ADDRESS!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '12',
                            'validation_value'  => []
                        ),    
                ), 
        ),
        'FL_DELIVERY_ORDER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_DELIVERY_ORDER',
            'primary_field' => 'DELIVERY_ORDER_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                ),
        ),
        /**
            VOT_FUND_ID
            PRG_ACTIVITY_ID
            SUB_SETIA_ID
            GL_ACCOUNT_ID
            UOM_ID
            ASSET_IND
         */
        'FL_FULFILMENT_ITEM_ADDR' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_FULFILMENT_ITEM_ADDR',
            'primary_field' => 'FULFILMENT_ITEM_ADDR_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'vot_fund_id' => 
                        array(
                            'field' => 'VOT_FUND_ID',
                            'display' => 'FIELD VOT FUND ID',
                            'description' => 'Must be existed in PM_VOT_FUND -> VOT_FUND_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
					'prg_activity_id' => 
                        array(
                            'field' => 'PRG_ACTIVITY_ID',
                            'display' => 'FIELD PRG ACTIVITY ID',
                            'description' => 'Must be existed in PM_PRG_ACTIVITY -> PRG_ACTIVITY_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
					'sub_setia_id' => 
                        array(
                            'field' => 'SUB_SETIA_ID',
                            'display' => 'FIELD SUB SETIA ID',
                            'description' => 'Must be existed in PM_SUB_SETIA -> SUB_SETIA_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
					'gl_account_id' => 
                        array(
                            'field' => 'GL_ACCOUNT_ID',
                            'display' => 'FIELD GL ACCOUNT ID',
                            'description' => 'Must be existed in PM_GL_ACCOUNT -> GL_ACCOUNT_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
					'uom_id' => 
                        array(
                            'field' => 'UOM_ID',
                            'display' => 'FIELD UOM ID',
                            'description' => 'Must be existed in PM_UOM -> UOM_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
					'asset_ind' => 
                        array(
                            'field' => 'ASSET_IND',
                            'display' => 'FIELD ASSET IND',
                            'description' => 'Must be integer max (20)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => []
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                    'received_qty' => 
                        array(
                            'field' => 'RECEIVED_QTY',
                            'display' => 'FIELD RECEIVED QTY',
                            'description' => 'MAX NUMBER(6)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '6',
                            'validation_value'  => []
                        ),
                         
                ),
        ),
        'FL_FULFILMENT_NOTE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_FULFILMENT_NOTE',
            'primary_field' => 'FULFILMENT_NOTE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                ),
        ),
        'FL_FULFILMENT_ORDER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_FULFILMENT_ORDER',
            'primary_field' => 'FULFILMENT_ORDER_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'hide_task_yep' => 
                        array(
                            'field' => 'HIDE_TASK_YEP',
                            'display' => 'FIELD HIDE TASK YEP',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),  
                    'factoring_org_id' => 
                        array(
                            'field' => 'FACTORING_ORG_ID',
                            'display' => 'FIELD FACTORING ORG ID',
                            'description' => 'Must be PM_FINANCIAL_ORG -> FINANCIAL_ORG Fields',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
                    'is_ap58' => 
                        array(
                            'field' => 'IS_AP58',
                            'display' => 'FIELD IS AP58',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_ap59' => 
                        array(
                            'field' => 'IS_AP59',
                            'display' => 'FIELD IS AP59',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_carry_forward' => 
                        array(
                            'field' => 'IS_CARRY_FORWARD',
                            'display' => 'FIELD IS CARRY FORWARD',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_closed_manual' => 
                        array(
                            'field' => 'IS_CLOSED_MANUAL',
                            'display' => 'FIELD IS CLOSED MANUAL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_factored' => 
                        array(
                            'field' => 'IS_FACTORED',
                            'display' => 'FIELD IS FACTORED',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_yep' => 
                        array(
                            'field' => 'IS_YEP',
                            'display' => 'FIELD IS YEP',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),  
                    'sap_order_no' => 
                        array(
                            'field' => 'SAP_ORDER_NO',
                            'display' => 'FIELD SAP ORDER NO',
                            'description' => 'MAX VARCHAR(17) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept  integer */
                            'validation_length' => '17',
                            'validation_value'  => [],
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                       
                      
                ),
        ),
        'FL_FULFILMENT_REQUEST' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_FULFILMENT_REQUEST',
            'primary_field' => 'FULFILMENT_REQ_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                    'ag_approved_date' => 
                        array(
                            'field' => 'AG_APPROVED_DATE',
                            'display' => 'FIELD AG APPROVED DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ),
                ),
        ),
        'FL_INVOICE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_INVOICE',
            'primary_field' => 'INVOICE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                    'is_sent_idd_cre' => 
                        array(
                            'field' => 'IS_SENT_IDD_CRE',
                            'display' => 'FIELD IS SENT IDD CRE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'ap59_status_id' => 
                        array(
                            'field' => 'AP59_STATUS_ID',
                            'display' => 'FIELD AP59 STATUS ID',
                            'description' => 'MAX NUMBER(6)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '6',
                            'validation_value'  => [49010]
                        ),
                        
                    'submission_date' => 
                        array(
                            'field' => 'SUBMISSION_DATE',
                            'display' => 'FIELD SUBMISSION DATE ',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ),
                ),  
        ),
        'FL_PAYMENT_ADVICE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_PAYMENT_ADVICE',
            'primary_field' => 'PAYMENT_ADVICE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                    'invoice_id' => 
                        array(
                            'field' => 'INVOICE_ID ',
                            'display' => 'FIELD INVOICE ID ',
                            'description' => 'MAX NUMBER(20)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => []
                        ),
                ),
        ),
        'FL_SUPPLIER_DTL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_SUPPLIER_DTL',
            'primary_field' => 'SUPPLIER_DTL_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'branch_name' => 
                        array(
                            'field' => 'BRANCH_NAME',
                            'display' => 'FIELD BRANCH_NAME',
                            'description' => 'MAX VARCHAR(50) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept  integer */
                            'validation_length' => '50',
                            'validation_value'  => [],
                        ),
                    'branch_code' => 
                        array(
                            'field' => 'BRANCH_CODE',
                            'display' => 'FIELD BRANCH_CODE',
                            'description' => 'MAX VARCHAR(10) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => [],
                        ),
                    'address_id' => 
                        array(
                            'field' => 'ADDRESS_ID ',
                            'display' => 'FIELD ADDRESS_ID',
                            'description' => 'MAX NUMBER(20)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => []
                        ),
                ),
        ),
        'FL_STOP_INSTR' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_STOP_INSTR',
            'primary_field' => 'STOP_INSTR_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,9]
                        ),
                ),
        ),
        'FL_TEMP_ITEM' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_TEMP_ITEM',
            'primary_field' => 'TEMP_ITEM_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'is_allow_delete' => true, // Helpdesk Ticket #0000004087  . Please be careful on this function. Must be has approval.
            'field_allow' => 
                array(
                    'request_item_id' => 
                        array(
                            'field' => 'REQUEST_ITEM_ID',
                            'display' => 'FIELD REQUEST ITEM ID',
                            'description' => 'MAX NUMBER(15)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                    'fulfilment_req_id' => 
                        array(
                            'field' => 'FULFILMENT_REQ_ID',
                            'display' => 'FIELD FULFILMENT_ REQ ID',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                ),
        ),
    
        'FL_WORKFLOW_STATUS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_WORKFLOW_STATUS',
            'primary_field' => 'WORKFLOW_STATUS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'is_current' => 
                        array(
                            'field' => 'IS_CURRENT',
                            'display' => 'FIELD IS CURRENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5). 42000 | DO, CN	45900	Cancelled,CO	41900	Cancelled,CR	40900	Cancelled,DN	45400	Cancelled,DO	42900	Cancelled,FN	43900	Cancelled,IN	44900	Cancelled,PA	46900	Cancelled,PO	41400	Cancelled,PR	40400	Cancelled,RS	49220	Cancelled,SD	47900	Cancelled,SI	48900	Cancelled',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [42000, 45900,41900,40900,45400,42900,43900,44900,46900,41400,40400,49220,47900,48900,41910,41510,41410,41010,40910,40810,40410,40310,40401,40402,42910,43910,44911,46910]
                            
                        ),
                        
                ),
        ),
        'FL_YEP_BPMTASK' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'FL_YEP_BPMTASK',
            'primary_field' => 'FL_YEP_TASK_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'fulfilment_order_id' => 
                        array(
                            'field' => 'FULFILMENT_ORDER_ID',
                            'display' => 'FIELD FULFILMENT ORDER ID',
                            'description' => 'Must be FULFILMENT TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
                    'fulfilment_req_id' => 
                        array(
                            'field' => 'FULFILMENT_REQ_ID',
                            'display' => 'FIELD FULFILMENT REQ ID',
                            'description' => 'Must be FULFILMENT TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
                    'doc_id' => 
                        array(
                            'field' => 'DOC_ID',
                            'display' => 'FIELD DOC ID',
                            'description' => 'Must be FULFILMENT TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
                    'doc_type' => 
                        array(
                            'field' => 'DOC_TYPE',
                            'display' => 'FIELD DOC TYPE',
                            'description' => 'MAX VARCHAR(2) , Must be Document Type in Fulfilment Module.',
                            'validation_type' => 'string', /* only accept string value */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),
                    'instance_id' => 
                        array(
                            'field' => 'INSTANCE_ID',
                            'display' => 'FIELD INSTANCE ID',
                            'description' => 'Must be BPM TABLE -> Refer tuh INSTANCE ID Fields',
                            'validation_type' => 'integer', /* only integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
					'phase' => 
                        array(
                            'field' => 'PHASE',
                            'display' => 'FIELD PHASE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [3,5]
                        ),
					'progress_status' => 
                        array(
                            'field' => 'PROGRESS_STATUS',
                            'display' => 'FIELD PROGRESS STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,5]
                        ),
					'task_id' => 
                        array(
                            'field' => 'TASK_ID',
                            'display' => 'FIELD TASK ID',
                            'description' => 'MAX VARCHAR(100) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string value */
                            'validation_length' => '100',
                            'validation_value'  => [],
                        ),
                       
                      
                ),
        ),
        'FL_YEP_TASKLIST' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'FL_YEP_TASKLIST',
            'primary_field' => 'YEP_TASK_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'fulfilment_order_id' => 
                        array(
                            'field' => 'FULFILMENT_ORDER_ID',
                            'display' => 'FIELD FULFILMENT ORDER ID',
                            'description' => 'Must be FULFILMENT TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
                    'fulfilment_req_id' => 
                        array(
                            'field' => 'FULFILMENT_REQ_ID',
                            'display' => 'FIELD FULFILMENT REQ ID',
                            'description' => 'Must be FULFILMENT TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ),
                    'is_ap58' => 
                        array(
                            'field' => 'IS_AP58',
                            'display' => 'FIELD IS AP58',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_carry_forward' => 
                        array(
                            'field' => 'IS_CARRY_FORWARD',
                            'display' => 'FIELD IS CARRY FORWARD',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
					'phase' => 
                        array(
                            'field' => 'PHASE',
                            'display' => 'FIELD PHASE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [1,2,3,5]
                        ),
					'flag' => 
                        array(
                            'field' => 'FLAG',
                            'display' => 'FIELD FLAG',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'task_id' => 
                        array(
                            'field' => 'TASK_ID',
                            'display' => 'FIELD TASK_ID',
                            'description' => 'MAX VARCHAR(100) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '100',
                            'validation_value'  => [],
                        ),
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'progress_status' => 
                        array(
                            'field' => 'PROGRESS_STATUS',
                            'display' => 'FIELD PROGRESS STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,2,3,4,5]
                        ),
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => []
                        ),    

                ), 
        ),
        'OSB_RETRY_DTL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'OSB_RETRY_DTL',
            'primary_field' => 'RETRY_DTL_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'is_allow_delete' => true, // Helpdesk Ticket #0000004087  . Please be careful on this function. Must be has approval.
            'field_allow' => 
                array(
                    'retry_count' => 
                        array(
                            'field' => 'RETRY_COUNT',
                            'display' => 'FIELD RETRY COUNT',
                            'description' => 'MAX NUMBER(3)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => []
                        )
                ),
        ),
        'OSB_BATCH_RETRY_DTL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'OSB_BATCH_RETRY_DTL',
            'primary_field' => 'BATCH_RETRY_DTL_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'is_allow_delete' => true, // Helpdesk Ticket #0000004087  . Please be careful on this function. Must be has approval.
            'field_allow' => 
                array(
                    'retry_count' => 
                        array(
                            'field' => 'RETRY_COUNT',
                            'display' => 'FIELD RETRY COUNT',
                            'description' => 'MAX NUMBER(3)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => []
                        )
                ),
        ),
        'PM_ADDRESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_ADDRESS',
            'primary_field' => 'ADDRESS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                ), 
        ),
        'PM_ADDRESS_TYPE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_ADDRESS_TYPE',
            'primary_field' => 'ADDRESS_TYPE_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                ), 
        ),
        'PM_CATEGORY_L3' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_CATEGORY_L3',
            'primary_field' => 'CATEGORY_L3_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'is_ssm_exclusive' => 
                        array(
                            'field' => 'IS_SSM_EXCLUSIVE',
                            'display' => 'FIELD IS SSM EXCLUSIVE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_printing' => 
                        array(
                            'field' => 'IS_PRINTING',
                            'display' => 'FIELD IS PRINTING',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_other_desc_req' => 
                        array(
                            'field' => 'IS_OTHER_DESC_REQ',
                            'display' => 'FIELD IS OTHER DESC REQ',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                ), 
        ),
        'PM_DIGI_CERT' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_DIGI_CERT',
            'primary_field' => 'DIGI_CERT_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'cert_issuer' => 
                        array(
                            'field' => 'CERT_ISSUER',
                            'display' => 'FIELD CERT ISSUER',
                            'description' => 'MAX VARCHAR(1) , T or null. T: trustgate, null: Digicert',
                            'validation_type' => 'string', /* only accept string */
                            'validation_length' => '4',
                            'validation_value'  => ['T','null'],
                        ),
                    'orn' => 
                        array(
                            'field' => 'ORN',
                            'display' => 'FIELD ORN',
                            'description' => 'MAX VARCHAR(20) ,eP Number',
                            'validation_type' => 'string', /* only accept string */
                            'validation_length' => '20',
                            'validation_value'  => [],
                        ),
                    'prn' => 
                        array(
                            'field' => 'PRN',
                            'display' => 'FIELD PRN',
                            'description' => 'MAX VARCHAR(20) , Identificaton Number',
                            'validation_type' => 'string', /* only accept string */
                            'validation_length' => '20',
                            'validation_value'  => [],
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),    
                ), 
        ),
        'PM_NOTIFY_MESSAGE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_NOTIFY_MESSAGE',
            'primary_field' => 'NOTIFY_MESSAGE_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'is_sent' => 
                        array(
                            'field' => 'IS_SENT',
                            'display' => 'FIELD IS SENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                ), 
        ),
        'PM_PENDING_LIFERAY' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_PENDING_LIFERAY',
            'primary_field' => 'PENDING_LIFERAY_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9,8]
                        ),
                    'attempt' => 
                        array(
                            'field' => 'ATTEMPT',
                            'display' => 'FIELD ATTEMPT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3]
                        ), 
                ), 
        ),
        'PM_PENDING_REMOVE_TASK' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_PENDING_REMOVE_TASK',
            'primary_field' => 'PM_PENDING_REMOVE_TASK_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1),  8 : To set scheduler to pickup this record',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [8]
                        ),
                ), 
        ),
        'PM_PRG_ACTIVITY' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'PM_PRG_ACTIVITY',
            'primary_field' => 'PRG_ACTIVITY_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'changed_date' => 
                        array(
                            'field' => 'CHANGED_DATE',
                            'display' => 'FIELD CHANGED DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ),
                ),
        ),
        'PM_TRACKING_DIARY' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_TRACKING_DIARY',
            'primary_field' => 'TRACKING_DIARY_ID',
            'required_changed_date' => false,  // PM_TRACKING_DIARY do not have change_date
            // 'is_allow_delete' => true, // Helpdesk Ticket #0000003113  . Please be careful on this function. Must be has approval.
            'field_allow' => 
                array(
                   'group_id' => 
                        array(
                            'field' => 'GROUP_ID',
                            'display' => 'FIELD GROUP ID',
                            'description' => 'MAX INTEGER(1) , available for value : 0 only',
                            'validation_type' => 'integer', /* only accept string */
                            'validation_length' => '1',
                            'validation_value'  => [0],
                        ),
                    'doc_type' => 
                        array(
                            'field' => 'DOC_TYPE',
                            'display' => 'FIELD DOC TYPE',
                            'description' => 'MAX VARCHAR(2) , Document Type.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),
                    'doc_id' => 
                        array(
                            'field' => 'DOC_ID',
                            'display' => 'FIELD DOC ID',
                            'description' => 'Must be TABLE -> PRIMARY KEY ID Fields',
                            'validation_type' => 'integer', /* only accept integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ), 
                ), 
        ),
        'PM_USER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_USER',
            'primary_field' => 'USER_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'designation' => 
                        array(
                            'field' => 'DESIGNATION',
                            'display' => 'FIELD DESIGNATION',
                            'description' => 'MAX VARCHAR(200) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '200',
                            'validation_value'  => [],
                        ),
                    'identification_no' => 
                        array(
                            'field' => 'IDENTIFICATION_NO',
                            'display' => 'FIELD IDENTIFICATION NO',
                            'description' => 'MAX VARCHAR(16) , Identification Number MyKAD.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '16',
                            'validation_value'  => [],
                        ),
                    'user_name' => 
                        array(
                            'field' => 'USER_NAME',
                            'display' => 'FIELD USER NAME',
                            'description' => 'MAX VARCHAR(200) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '200',
                            'validation_value'  => [],
                        ),
                    'login_id' => 
                        array(
                            'field' => 'LOGIN_ID',
                            'display' => 'FIELD LOGIN ID',
                            'description' => 'MAX VARCHAR(50) , Special Character Not Allowed!. Please ensure this issue cause of SSO OIM',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '50',
                            'validation_value'  => [],
                        ),
                    'email' => 
                        array(
                            'field' => 'EMAIL',
                            'display' => 'FIELD EMAIL',
                            'description' => 'MAX VARCHAR(100) , Special Character Not Allowed!. Please make sure valid email format',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '100',
                            'validation_value'  => [],
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                ), 
        ),
        'PM_USER_ORG' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_USER_ORG',
            'primary_field' => 'USER_ORG_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'exp_date' => 
                        array(
                            'field' => 'EXP_DATE',
                            'display' => 'FIELD EXP DATE',
                            'description' => 'Only NULL value will be updated. This scenario patch data to update NULL value only.',
                            'validation_type' => 'null', /* This for field to update value as NULL */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),    
                ), 
        ),
        'PM_USER_ROLE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PM_USER_ROLE',
            'primary_field' => 'USER_ROLE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'role_code' => 
                        array(
                            'field' => 'ROLE_CODE',
                            'display' => 'FIELD ROLE CODE',
                            'description' => 'MAX VARCHAR(20) , Special Character Not Allowed!. Kindly follow on validation value. null mean set as null value.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '25',
                            'validation_value'  => ['MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','SUPPLIER_TEMP','G2G_ADMIN','G2G_USER'],
                        ), 
                ), 
        ),
        'PP_EXPENDITURE_ITEM' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PP_EXPENDITURE_ITEM',
            'primary_field' => 'EXPENDITURE_ITEM_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_disablerow' => 
                        array(
                            'field' => 'IS_DISABLEROW',
                            'display' => 'FIELD IS DISABLEROW',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'is_searchreset' => 
                        array(
                            'field' => 'IS_SEARCHRESET',
                            'display' => 'FIELD IS SEARCHRESET',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                ), 
        ),
        
        'PP_PLAN' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PP_PLAN',
            'primary_field' => 'PLAN_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_cancel' => 
                        array(
                            'field' => 'IS_CANCEL',
                            'display' => 'FIELD IS CANCEL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                ), 
        ),
        'PP_PLAN_ACTOR' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PP_PLAN_ACTOR',
            'primary_field' => 'PLAN_ACTOR_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'user_id' => 
                        array(
                            'field' => 'USER_ID',
                            'display' => 'FIELD USER ID',
                            'description' => 'Must be PM_USER -> USER_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                ), 
        ),
	    'PP_PROCUREMENT_ITEM' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PP_PROCUREMENT_ITEM',
            'primary_field' => 'PROCUREMENT_ITEM_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_disablerow' => 
                        array(
                            'field' => 'IS_DISABLEROW',
                            'display' => 'FIELD IS DISABLEROW',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                ), 
        ),
        'PP_WORKFLOW_STATUS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'PP_WORKFLOW_STATUS',
            'primary_field' => 'WORKFLOW_STATUS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'is_current' => 
                        array(
                            'field' => 'IS_CURRENT',
                            'display' => 'FIELD IS CURRENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [30006, 30007, 30008, 30009, 30012, 30014, 30016, 30001, 30002, 30003, 30004, 30005, 30010, 30011, 30013, 30015]
                            
                        ),
                        
                ),
        ),
        'PY_BILL' => array(
             'database_connection' => 'oracle_nextgen_fullgrant',
             'table_name' => 'PY_BILL',
             'primary_field' => 'BILL_ID',
             'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
             'where_add' => array('bill_type' => 'S'),
             'field_allow' => 
                 array(
                    'record_status' => 
                         array(
                             'field' => 'RECORD_STATUS',
                             'display' => 'FIELD RECORD STATUS',
                             'description' => 'MAX NUMBER(1). Kindly check proper this record.',
                             'validation_type' => 'integer', /* only accept string or integer */
                             'validation_length' => '1',
                             'validation_value'  => [0,1]
                         ),
                 ), 
        ),
        'PY_PAYMENT_RESPONSE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'PY_PAYMENT_RESPONSE',
            'primary_field' => 'PAYMENT_RESPONSE_ID',  
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'transaction_id' => 
                        array(
                            'field' => 'TRANSACTION_ID',
                            'display' => 'FIELD TRANSACTION ID',
                            'description' => 'MAX VARCHAR(20) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => [],
                        ),
                ), 
        ),
        'SC_ACTOR' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_ACTOR',
            'primary_field' => 'ACTOR_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'role_code' => 
                        array(
                            'field' => 'ROLE_CODE',
                            'display' => 'FIELD ROLE CODE',
                            'description' => 'MAX VARCHAR(20) , Rolecode allow : DESK_OFFICER',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => ['DESK_OFFICER',],
                        ),
                    'action_date' => 
                        array(
                            'field' => 'ACTION_DATE ',
                            'display' => 'FIELD ACTION DATE',
                            'description' => 'Only sysdate value will be updated. This scenario patch data to update sysdate value only.',
                            'validation_type' => 'sysdate', /* This for field to update value as sysdate */
                            'validation_length' => '7',
                            'validation_value'  => ['sysdate']
                        ),
                ), 
        ),
        'SC_QT_SUPPLIER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_QT_SUPPLIER',
            'primary_field' => 'QT_SUPPLIER_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'disqualified_stage' => 
                        array(
                            'field' => 'DISQUALIFIED_STAGE',
                            'display' => 'FIELD DISQUALIFIED STAGE',
                            'description' => 'Only NULL value will be updated ',
                            'validation_type' => 'null', /* This for field to update value as NULL */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),
                ), 
        ),
        'SC_QUOTE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_QUOTE',
            'primary_field' => 'QUOTE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'supplier_catalogue_id' => 
                        array(
                            'field' => 'SUPPLIER_CATALOGUE_ID',
                            'display' => 'FIELD SUPPLIER CATALOGUE ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => []
                        ),
                ),

                
        ),
        'SC_PURCHASE_REQUEST' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_PURCHASE_REQUEST',
            'primary_field' => 'PURCHASE_REQUEST_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'record_status' => 
                        array(
                        'field' => 'RECORD_STATUS',
                        'display' => 'FIELD RECORD STATUS',
                        'description' => 'MAX NUMBER(1)',
                        'validation_type' => 'integer', /* only accept string or integer */
                        'validation_length' => '1',
                        'validation_value'  => [0,1,2,9]
                        ),
                    'request_note_id' => 
                        array(
                            'field' => 'REQUEST_NOTE_ID',
                            'display' => 'FIELD REQUEST NOTE ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SC_REQUEST_ITEM' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_REQUEST_ITEM',
            'primary_field' => 'REQUEST_ITEM_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'item_desc' => 
                        array(
                            'field' => 'ITEM_DESC',
                            'display' => 'FIELD ITEM DESC',
                            'description' => 'MoAX NUMBER(1000). Please do not put special character. Only ASCII characters are allowed',
                            'validation_type' => 'string', /* only accept  integer */
                            'validation_length' => '1000',
                            'validation_value'  => []
                        ),
                    'order_qty' => 
                        array(
                            'field' => 'ORDER_QTY',
                            'display' => 'FIELD ORDER QTY',
                            'description' => 'MAX NUMBER(8) Please make sure contract ver id is valid key',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '8',
                            'validation_value'  => []
                        ),
                    'item_id' => 
                        array(
                            'field' => 'ITEM_ID',
                            'display' => 'FIELD ITEM ID',
                            'description' => 'MAX NUMBER(15)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SC_REQUEST_NOTE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_REQUEST_NOTE',
            'primary_field' => 'REQUEST_NOTE_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'approver_id' => 
                        array(
                            'field' => 'APPROVER_ID',
                            'display' => 'FIELD APPROVER ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => ['null']
                        ),
                    'user_group_id' => 
                        array(
                            'field' => 'USER_GROUP_ID',
                            'display' => 'FIELD USER GROUP ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => ['null']
                        ),    
                ), 
        ),
        'SC_REQUEST_NOTE_DTL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_REQUEST_NOTE_DTL',
            'primary_field' => 'REQUEST_NOTE_DTL_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array( 
                   'request_supplier_item_id' => 
                        array(
                            'field' => 'REQUEST_SUPPLIER_ITEM_ID',
                            'display' => 'FIELD REQUEST SUPPLIER ITEM ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => []
                        ),
                    'purchase_request_id' => 
                        array(
                            'field' => 'PURCHASE_REQUEST_ID',
                            'display' => 'FIELD PURCHASE REQUEST ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => ['null']
                        ),
                    'supplier_address_id' => 
                        array(
                            'field' => 'SUPPLIER_ADDRESS_ID',
                            'display' => 'FIELD SUPPLIER ADDRESS ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept  integer */
                            'validation_length' => '10',
                            'validation_value'  => ['null']
                        ),   
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,9]
                        ),  
                ), 
        ),
        'SC_WORKFLOW_STATUS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SC_WORKFLOW_STATUS',
            'primary_field' => 'WORKFLOW_STATUS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'is_current' => 
                        array(
                            'field' => 'IS_CURRENT',
                            'display' => 'FIELD IS CURRENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [60702,60851,60856,60857,60853,60854,60855,60852,60850,60858,60859,60860,60861,60700,60701,60703,60704,60705,60706,60707,60708,60709,60710,60711,60712,60715,62509,62510,62511,62512,62513,62516,62515,60804,60807,60815,60753,60754,60755 ]
                        ),
                        /*** 
                        SQ :-
                        60855 - Cancelled due to Purchase in Default validity period is expired (60 days).
                        60858 - Cancel Due To Ministry Restructuring
                        60859 - Cancelled due to PTJ Code Changed
                        60860 - Simple Quote Validity Period Expired
                        60861 - Cancelled due to Simple Quote validity period is expired (60 days)

                        
                        RN :-
                        60701 - Pending Approval
                        60706 - Cancelled due to Purchase in Default validity period is expired (60 days).
                        60707 - Pending Revision Approval
                        60708 - Pending Assign Item Code
                        60709 - Cancel Due To Ministry Restructuring
                        60710 - Cancelled due to Simple Quote validity period is expired (60 days)
                        60711 - Rejected due to Simple Quote validity period is expired (60 days).
                        60712 - Cancelled due to PTJ Code Changed 
                        60715 - Rejected due to Purchase in Default validity period is expired (60 days).
                        60702 - Pending Revision-RN
                        60851 - Pending Supplier Response-SQ
                        60856 - Response Submitted-SQ
                        60857 - Pending Item Code Assignment-SQ
, 
                        
                        60804 - Selesai-PD
                        60807 - Tempoh Sah Laku Pembelian Luar Aturan Telah Tamat-PD
                        60815 - Dibatalkan-PD

                        60753 - Selesai-PI
                        60754 - Dibatalkan-PI
                        60755 - Tamat Tempoh-PI
                        ***/
                ), 
        ),
        'SM_ADDRESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_ADDRESS',
            'primary_field' => 'ADDRESS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'address_1' => 
                        array(
                            'field' => 'ADDRESS_1',
                            'display' => 'FIELD ADDRESS 1',
                            'description' => 'MAX VARCHAR(40) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '40',
                            'validation_value'  => [],
                        ),
                    'address_2' => 
                        array(
                            'field' => 'ADDRESS_2',
                            'display' => 'FIELD ADDRESS 2',
                            'description' => 'MAX VARCHAR(40) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '40',
                            'validation_value'  => [],
                        ),   
                    'address_3' => 
                        array(
                            'field' => 'ADDRESS_3',
                            'display' => 'FIELD ADDRESS 3',
                            'description' => 'MAX VARCHAR(40) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '40',
                            'validation_value'  => [],
                        ),  
                    'postcode' => 
                        array(
                            'field' => 'POSTCODE',
                            'display' => 'FIELD POSTCODE',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ), 
                    'state_id' => 
                        array(
                            'field' => 'STATE_ID',
                            'display' => 'FIELD STATE ID',
                            'description' => 'MAX NUMBER(5) , Please make sure value ID is exist in PM_STATE!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ),
                    'city_id' => 
                        array(
                            'field' => 'CITY_ID',
                            'display' => 'FIELD CITY ID',
                            'description' => 'MAX NUMBER(5) , Please make sure value ID is exist in PM_CITY!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ),
                    'district_id' => 
                        array(
                            'field' => 'DISTRICT_ID',
                            'display' => 'FIELD DISTRICT ID',
                            'description' => 'MAX NUMBER(5) , Please make sure value ID is exist in PM_DISTRICT!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ),
                    'division_id' => 
                        array(
                            'field' => 'DIVISION_ID',
                            'display' => 'FIELD DIVISION ID',
                            'description' => 'MAX NUMBER(5) , Please make sure value ID is exist in PM_DIVISION!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ),
                    'ssm_data' => 
                        array(
                            'field' => 'SSM_DATA',
                            'display' => 'FIELD SSM DATA',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'country_id' => 
                        array(
                            'field' => 'country_id',
                            'display' => 'FIELD COUNTRY DATA',
                            'description' => 'MAX NUMBER(5) , Please make sure value ID is exist in PM_COUNTRY!',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [],
                        ), 
                ), 
        ),
        'SM_APPL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_APPL',
            'primary_field' => 'APPL_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_active_appl' => 
                        array(
                            'field' => 'IS_ACTIVE_APPL',
                            'display' => 'FIELD IS ACTIVE APPL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'is_resubmit' => 
                        array(
                            'field' => 'IS_RESUBMIT',
                            'display' => 'FIELD IS RESUBMITL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),     
                    'created_by' => 
                        array(
                            'field' => 'CREATED_BY',
                            'display' => 'FIELD IS CREATED BY',
                            'description' => 'Must be PM_USER -> USER_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
                    'changed_by' => 
                        array(
                            'field' => 'CHANGED_BY',
                            'display' => 'FIELD IS CHANGED BY',
                            'description' => 'Must be PM_USER -> USER_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [20202,20103,20105,20118,20100,20101,20102,20104,20251,20112,20114,20116,20117,20102,20107,20108,20118,20119,20121,20199,20200,20399,20110,20122, 20189, 20190, 20123, 20398]
                        ),
                    'appl_category' => 
                        array(
                            'field' => 'APPL_CATEGORY',
                            'display' => 'FIELD APPL CATEGORY',
                            'description' => 'MAX VARCHAR(1) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => ['N','S'],
                        ),
                    'is_questionnaire' => 
                        array(
                            'field' => 'IS_QUESTIONNAIRE',
                            'display' => 'FIELD IS QUESTIONNAIRE',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_appl_valid_with_ssm' => 
                        array(
                            'field' => 'IS_APPL_VALID_WITH_SSM',
                            'display' => 'FIELD IS APPL VALID WITH SSM',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_bumi' => 
                        array(
                            'field' => 'IS_BUMI',
                            'display' => 'FIELD IS BUMI',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'reg_status_id' => 
                        array(
                            'field' => 'REG_STATUS_ID',
                            'display' => 'FIELD REG STATUS ID',
                            'description' => 'MAX NUMBER(1), type null if want to set as null',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => ['null',1,2,3,4,5,6,7,8]
                        ),
                    'supporting_doc_mode' => 
                        array(
                            'field' => 'SUPPORTING_DOC_MODE',
                            'display' => 'FIELD SUPPORTING DOC MODE',
                            'description' => 'MAX VARCHAR(1) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => ['H','S'],
                        ),
                    'original_appl_id' => 
                        array(
                            'field' => 'ORIGINAL_APPL_ID',
                            'display' => 'FIELD ORIGINAL APPL ID',
                            'description' => 'Only NULL value will be updated. This scenario patch data to update NULL value only',
                            'validation_type' => 'null', /* only accept string */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),
                        
                ), 
        ),
        'SM_COMPANY_BASIC' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_COMPANY_BASIC',
            'primary_field' => 'COMPANY_BASIC_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'company_name' => 
                        array(
                            'field' => 'COMPANY_NAME ',
                            'display' => 'PHONE COMPANY NAME ',
                            'description' => 'MAX VARCHAR(100) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '100',
                            'validation_value'  => [],
                        ),
                   'is_with_federal' => 
                        array(
                            'field' => 'IS_WITH_FEDERAL',
                            'display' => 'IS WITH FEDERAL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'phone_country' => 
                        array(
                            'field' => 'PHONE_COUNTRY',
                            'display' => 'PHONE COUNTRY',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),   
                    'phone_area' => 
                        array(
                            'field' => 'PHONE_AREA',
                            'display' => 'PHONE AREA',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ), 
                    'phone_no' => 
                        array(
                            'field' => 'PHONE_NO',
                            'display' => 'PHONE NO',
                            'description' => 'MAX VARCHAR(15) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => [],
                        ),
                    'ssm_company_country' => 
                        array(
                            'field' => 'SSM_COMPANY_COUNTRY',
                            'display' => 'SSM COMPANY COUNTRY',
                            'description' => 'MAX VARCHAR(3) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => [],
                        ),  
                    'annual_revenue_id' => 
                        array(
                            'field' => 'ANNUAL_REVENUE_ID',
                            'display' => 'FIELD ANNUAL REVENUE ID',
                            'description' => 'MAX NUMBER(4)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '4',
                            'validation_value'  => []
                        ),
                    'paid_up_capital' => 
                        array(
                            'field' => 'PAID_UP_CAPITAL',
                            'display' => 'FIELD PAID UP CAPITAL',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0]
                        ),
                ), 
        ),  
        'SM_MOF_ACCOUNT' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_MOF_ACCOUNT',
            'primary_field' => 'MOF_ACCOUNT_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,3,9]
                        ),
                    'eff_date' => 
                        array(
                            'field' => 'EFF_DATE',
                            'display' => 'FIELD EFF DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI:SS)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '19',
                            'validation_value'  => []
                        ),
                    'exp_date' => 
                        array(
                            'field' => 'EXP_DATE',
                            'display' => 'FIELD EXP DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI:SS)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '19',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SM_MOF_CERT' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_MOF_CERT',
            'primary_field' => 'MOF_CERT_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,4,9]
                        ),
                    'appl_id' => 
                        array(
                            'field' => 'APPL_ID',
                            'display' => 'FIELD APPL ID',
                            'description' => 'MAX NUMBER(10)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '10',
                            'validation_value'  => []
                        ),
                    'eff_date' => 
                        array(
                            'field' => 'EFF_DATE',
                            'display' => 'FIELD EFF DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI:SS)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '19',
                            'validation_value'  => []
                        ),
                    'exp_date' => 
                        array(
                            'field' => 'EXP_DATE',
                            'display' => 'FIELD EXP DATE',
                            'description' => 'DATE FORMAT (YYYY-MM-DD HH24:MI:SS)  sample 2023-12-01 13:04',
                            'validation_type' => 'datetime', /* only accept string or integer */
                            'validation_length' => '19',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SM_PENDING_PROCESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_PENDING_PROCESS',
            'primary_field' => 'PENDING_PROCESS_ID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,8,9]
                        ),
                    'attempt' => 
                        array(
                            'field' => 'ATTEMPT',
                            'display' => 'FIELD ATTEMPT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3]
                        ), 
                    'is_pickup' => 
                        array(
                            'field' => 'IS_PICKUP',
                            'display' => 'FIELD IS PICKUP',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                ), 
        ),
        'SM_PERSONNEL' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_PERSONNEL',
            'primary_field' => 'PERSONNEL_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'designation' => 
                        array(
                            'field' => 'DESIGNATION',
                            'display' => 'FIELD DESIGNATION',
                            'description' => 'MAX VARCHAR(50) , Special Character Not Allowed!. Please make sure valid email format',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '50',
                            'validation_value'  => [],
                        ),
                    'ep_role' => 
                        array(
                            'field' => 'EP_ROLE',
                            'display' => 'FIELD EP ROLE',
                            'description' => 'MAX VARCHAR(20) , Special Character Not Allowed!. Kindly follow on validation value. null mean set as null value.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => ['MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','SUPPLIER_TEMP','G2G_ADMIN','GOVT_SELLER','FL_USER','CM_USER','G2G_USER','null'],
                        ),
                    'email' => 
                        array(
                            'field' => 'EMAIL',
                            'display' => 'FIELD EMAIL',
                            'description' => 'MAX VARCHAR(100) , Special Character Not Allowed!. Please make sure valid email format',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '100',
                            'validation_value'  => [],
                        ),
                    'identity_resident_status' => 
                        array(
                            'field' => 'IDENTITY_RESIDENT_STATUS',
                            'display' => 'FIELD IDENTITY_RESIDENT_STATUS',
                            'description' => 'MAX VARCHAR(1) , Special Character Not Allowed!. Please make sure refer JPN Identity',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [],
                        ),
                    
                    'identity_response_code' => 
                        array(
                            'field' => 'IDENTITY_RESPONSE_CODE',
                            'display' => 'FIELD IDENTITY RESPONSE CODE',
                            'description' => 'Response code result query to OSB (Integration)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [70032,70020,70040,70018,70031,70019]
                        ),
                        
                    'is_authorized' => 
                        array(
                            'field' => 'IS_AUTHORIZED',
                            'display' => 'FIELD IS AUTHORIZED',
                            'description' => '0 as NO, 1 as YES . Please make sure to set as 1, maximum person as is_authorized = 1 limit to 10 only. ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_higher_mgt' => 
                        array(
                            'field' => 'IS_HIGHER_MGT',
                            'display' => 'FIELD IS HIGHER MGT',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_mgt' => 
                        array(
                            'field' => 'IS_MGT',
                            'display' => 'FIELD IS MGT',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'is_muslim' => 
                        array(
                            'field' => 'IS_MUSLIM',
                            'display' => 'FIELD IS MUSLIM',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_softcert' => 
                        array(
                            'field' => 'IS_SOFTCERT',
                            'display' => 'FIELD IS SOFTCERT',
                            'description' => '0 as Softcert is not required, 1 as Softcert issued, 2 as Softcert requested, 3 as Softcert processing, 4 as Pending for softcert request, 7 as Softcert ready for renewal',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3,4,6,7]
                        ),
                    'is_clerk_staff' => 
                        array(
                            'field' => 'IS_CLERK_STAFF',
                            'display' => 'FIELD IS CLERK STAFF',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_equity_owner' => 
                        array(
                            'field' => 'IS_EQUITY_OWNER',
                            'display' => 'FIELD IS EQUITY OWNER',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_director' => 
                        array(
                            'field' => 'IS_DIRECTOR',
                            'display' => 'FIELD IS DIRECTOR',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_bumi' => 
                        array(
                            'field' => 'IS_BUMI',
                            'display' => 'FIELD IS BUMI',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_equity_owner_ssm' => 
                        array(
                            'field' => 'IS_EQUITY_OWNER_SSM',
                            'display' => 'FIELD IS EQUITY OWNER SSM',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),  
                    'is_director_ssm' => 
                        array(
                            'field' => 'IS_DIRECTOR_SSM',
                            'display' => 'FIELD IS DIRECTOR SSM',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'is_mgt_ssm' => 
                        array(
                            'field' => 'IS_MGT_SSM',
                            'display' => 'FIELD IS MGT SSM',
                            'description' => '0 as NO, 1 as YES',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),  
                    'identification_type_id' => 
                        array(
                            'field' => 'IDENTIFICATION_TYPE_ID',
                            'display' => 'FIELD IDENTIFICATION TYPE ID',
                            'description' => 'MAX DIGIT (3) only. [114, 115]',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => [114,115]
                        ),
                    'name' => 
                        array(
                            'field' => 'NAME',
                            'display' => 'FIELD NAME',
                            'description' => 'MAX CHAR (120) only. Follow name on MyKAD or Passport',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '120',
                            'validation_value'  => []
                        ), 
                    'name_in_ssm' => 
                        array(
                            'field' => 'NAME_IN_SSM',
                            'display' => 'FIELD NAME IN SSM',
                            'description' => 'MAX CHAR (120) only. Name in SSM',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '120',
                            'validation_value'  => []
                        ),  
                    'nationality_id' => 
                        array(
                            'field' => 'NATIONALITY_ID',
                            'display' => 'FIELD NATIONALITY ID',
                            'description' => 'MAX DIGIT (3) only.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => []
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,8,9]
                        ),
                    'rev_no' => 
                        array(
                            'field' => 'REV_NO',
                            'display' => 'FIELD REV NO',
                            'description' => 'Revision No. for record this personnel',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,3]
                        ),
                    'ssm_data' => 
                        array(
                            'field' => 'SSM_DATA',
                            'display' => 'FIELD SSM DATA',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_prof' => 
                        array(
                            'field' => 'IS_PROF',
                            'display' => 'FIELD IS PROF',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'is_semi_prof' => 
                        array(
                            'field' => 'IS_SEMI_PROF',
                            'display' => 'FIELD IS SEMI PROF',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),       
                    'user_id' => 
                        array(
                            'field' => 'USER_ID',
                            'display' => 'FIELD IS USER ID',
                            'description' => 'MAX NUMBER(8), Please make sure value is exist in PM_USER, set null to set null value in user_id',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '8',
                            'validation_value'  => ['null']
                        ),
                    'appl_id' => 
                        array(
                            'field' => 'APPL_ID',
                            'display' => 'FIELD IS APPL ID',
                            'description' => 'MAX NUMBER(8), Please make sure value is exist in SM_APPL,',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '8',
                            'validation_value'  => []
                        ),
                    'race_id' => 
                        array(
                            'field' => 'RACE_ID',
                            'display' => 'FIELD IS RACE ID',
                            'description' => 'MAX NUMBER(3), Please make sure value is exist in PM_RACE,',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '3',
                            'validation_value'  => []
                        ),   
                    'phone_country' => 
                        array(
                            'field' => 'PHONE_COUNTRY',
                            'display' => 'PHONE COUNTRY',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),   
                    'phone_area' => 
                        array(
                            'field' => 'PHONE_AREA',
                            'display' => 'PHONE AREA',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ), 
                    'phone_no' => 
                        array(
                            'field' => 'PHONE_NO',
                            'display' => 'PHONE NO',
                            'description' => 'MAX VARCHAR(15) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => [],
                        ), 
                    'mobile_country' => 
                        array(
                            'field' => 'PHONE_COUNTRY',
                            'display' => 'PHONE COUNTRY',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),   
                    'mobile_area' => 
                        array(
                            'field' => 'PHONE_AREA',
                            'display' => 'PHONE AREA',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ), 
                    'mobile_no' => 
                        array(
                            'field' => 'PHONE_NO',
                            'display' => 'PHONE NO',
                            'description' => 'MAX VARCHAR(15) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => [],
                        ),
                ), 
        ),      
        'SM_PERSONNEL_ADDRESS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_PERSONNEL_ADDRESS',
            'primary_field' => 'PERSONNEL_ADDRESS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,5,9]
                        ),
                    'personnel_id' => 
                        array(
                            'field' => 'PERSONNEL_ID',
                            'display' => 'FIELD PERSONNEL ID',
                            'description' => 'MAX NUMBER(12). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '12',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SM_SAP_VENDOR_CODE' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SAP_VENDOR_CODE',
            'primary_field' => 'SAP_VENDOR_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'sap_vendor_code' => 
                        array(
                            'field' => 'SAP_VENDOR_CODE',
                            'display' => 'FIELD SAP VENDOR CODE',
                            'description' => 'MAX VARCHAR(10) , Please ensure SAP_VENDOR_CODE must formatted and follow apove file.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '10',
                            'validation_value'  => [],
                        ),
                    'ep_no' => 
                        array(
                            'field' => 'EP_NO',
                            'display' => 'FIELD EP NO',
                            'description' => 'MAX VARCHAR(12) , Please ensure the eP No must be valid and formatted',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '12',
                            'validation_value'  => [],
                        ),
                    'branch_code' => 
                        array(
                            'field' => 'BRANCH_CODE',
                            'display' => 'FIELD BRANCH CODE',
                            'description' => 'MAX INTEGER(20) , This may valid to update branch only. For HQ to set null is not available',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '20',
                            'validation_value'  => [],
                        ),
                ), 
        ),
        'SM_SOFTCERT_REQUEST' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SOFTCERT_REQUEST',
            'primary_field' => 'SOFTCERT_REQUEST_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_free' => 
                        array(
                            'field' => 'IS_FREE',
                            'display' => 'FIELD IS FREE',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'softcert_provider' => 
                        array(
                            'field' => 'SOFTCERT_PROVIDER',
                            'display' => 'FIELD SOFTCERT PROVIDER',
                            'description' => 'MAX VARCHAR(2) , DG : Digicert , TG : Trustgate',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => ['DG','TG'],
                        ),
                    'reason_code' => 
                        array(
                            'field' => 'REASON_CODE',
                            'display' => 'FIELD REASON CODE',
                            'description' => 'MAX VARCHAR(1) , R , B, I, N',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => ['R','B','I','N'],
                        ),
                ), 
        ),
        'SM_SUPPLIER' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SUPPLIER',
            'primary_field' => 'SUPPLIER_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,4,5,9]
                        ),
                    'latest_appl_id' => 
                        array(
                            'field' => 'LATEST_APPL_ID',
                            'display' => 'FIELD LATEST APPL ID',
                            'description' => 'MAX NUMBER(12). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '12',
                            'validation_value'  => []
                        ),
                    'is_bumi' => 
                        array(
                            'field' => 'IS_BUMI',
                            'display' => 'FIELD IS BUMI',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                ), 
        ),
        'SM_SUPPLIER_BANK' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SUPPLIER_BANK',
            'primary_field' => 'SUPPLIER_BANK_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'account_no' => 
                        array(
                            'field' => 'ACCOUNT_NO',
                            'display' => 'FIELD ACCOUNT NO',
                            'description' => 'MAX NUMBER(16). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '16',
                            'validation_value'  => []
                        ),
                    'account_purpose' => 
                        array(
                            'field' => 'ACCOUNT_PURPOSE',
                            'display' => 'FIELD ACCOUNT PURPOSE',
                            'description' => 'MAX STRING(1). Kindly check proper this record.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => ['B','O','P','R']
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1). Kindly check proper this record.',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,2,9]
                        ),
                ), 
        ),
        'SM_SUPPLIER_BRANCH' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SUPPLIER_BRANCH',
            'primary_field' => 'SUPPLIER_BRANCH_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                    'ssm_data' => 
                        array(
                            'field' => 'SSM_DATA',
                            'display' => 'FIELD SSM DATA',
                            'description' => '0 as NO, 1 as YES ',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'branch_name' => 
                        array(
                            'field' => 'BRANCH_NAME',
                            'display' => 'FIELD BRANCH NAME',
                            'description' => 'MAX CHAR (200) only.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '200',
                            'validation_value'  => []
                        ),
                    'phone_country' => 
                        array(
                            'field' => 'PHONE_COUNTRY',
                            'display' => 'PHONE COUNTRY',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ),   
                    'phone_area' => 
                        array(
                            'field' => 'PHONE_AREA',
                            'display' => 'PHONE AREA',
                            'description' => 'MAX VARCHAR(2) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '2',
                            'validation_value'  => [],
                        ), 
                    'phone_no' => 
                        array(
                            'field' => 'PHONE_NO',
                            'display' => 'PHONE NO',
                            'description' => 'MAX VARCHAR(15) , Special Character Not Allowed!.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => [],
                        ),
                ), 
        ),
        'SM_SUPPLIER_CATEGORY' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SUPPLIER_CATEGORY',
            'primary_field' => 'SUPPLIER_CATEGORY_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'is_approved_by_po' => 
                        array(
                            'field' => 'IS_APPROVED_BY_PO',
                            'display' => 'FIELD IS APPROVED BY PO',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'is_approved_by_ap' => 
                        array(
                            'field' => 'IS_APPROVED_BY_AP',
                            'display' => 'FIELD IS APPROVED BY AP',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,8,9]
                        ),
                    'cancelled_date' => 
                        array(
                            'field' => 'CANCELLED_DATE',
                            'display' => 'FIELD CANCELLED DATE',
                            'description' => 'Only NULL value will be updated. This scenario patch data to update NULL value only.',
                            'validation_type' => 'null', /* This for field to update value as NULL */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),	
                    'approved_date' => 
                        array(
                            'field' => 'APPROVED_DATE',
                            'display' => 'FIELD APPROVED DATE',
                            'description' => 'Only NULL value will be updated. This scenario patch data to update NULL value only.',
                            'validation_type' => 'null', /* This for field to update value as NULL */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),
                    'category_eff_date' => 
                        array(
                            'field' => 'CATEGORY_EFF_DATE',
                            'display' => 'FIELD CATEGORY EFF DATE',
                            'description' => 'Only NULL value will be updated. This scenario patch data to update NULL value only.',
                            'validation_type' => 'null', /* This for field to update value as NULL */
                            'validation_length' => '4',
                            'validation_value'  => ['null']
                        ),
                    'previous_approved_by_po' => 
                        array(
                            'field' => 'PREVIOUS_APPROVED_BY_PO',
                            'display' => 'FIELD PREVIOUS APPROVED BY PO',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'previous_approved_by_ap' => 
                        array(
                            'field' => 'PREVIOUS_APPROVED_BY_AP',
                            'display' => 'FIELD PREVIOUS APPROVED BY AP',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ),
                    'previous_other_desc' => 
                        array(
                            'field' => 'PREVIOUS_OTHER_DESC',
                            'display' => 'FIELD PREVIOUS OTHER DESC',
                            'description' => 'MAX CHAR (200) only.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '200',
                            'validation_value'  => []
                        ), 
                    'other_desc' => 
                        array(
                            'field' => 'OTHER_DESC',
                            'display' => 'FIELD OTHER DESC',
                            'description' => 'MAX CHAR (200) only.',
                            'validation_type' => 'string', /* only accept string or integer */
                            'validation_length' => '200',
                            'validation_value'  => []
                        ), 
                ), 
        ),
        'SM_SV_TASK' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_SV_TASK',
            'primary_field' => 'SV_TASK_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'allow_delete' => true,  // Please be careful. This will delete permenant record.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'appl_id' => 
                        array(
                            'field' => 'APPL_ID',
                            'display' => 'FIELD APPL ID',
                            'description' => 'MAX NUMBER(15)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ),
                ), 
        ),
        'SM_WORKFLOW_STATUS' => array(
            'database_connection' => 'oracle_nextgen_fullgrant',
            'table_name' => 'SM_WORKFLOW_STATUS',
            'primary_field' => 'WORKFLOW_STATUS_ID',
            'required_changed_date' => true,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'record_status' => 
                        array(
                            'field' => 'RECORD_STATUS',
                            'display' => 'FIELD RECORD STATUS',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1,9]
                        ),
                    'is_current' => 
                        array(
                            'field' => 'IS_CURRENT',
                            'display' => 'FIELD IS CURRENT',
                            'description' => 'MAX NUMBER(1)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '1',
                            'validation_value'  => [0,1]
                        ), 
                    'status_id' => 
                        array(
                            'field' => 'STATUS_ID',
                            'display' => 'FIELD STATUS ID',
                            'description' => 'MAX NUMBER(5)',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '5',
                            'validation_value'  => [20100,20202,20118,20101,20251,20112,20102,20118,20122, 20119, 20189, 20190]
                        ),
                    'created_by' => 
                        array(
                            'field' => 'CREATED_BY',
                            'display' => 'FIELD IS CREATED BY',
                            'description' => 'Must be PM_USER -> USER_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
                    'changed_by' => 
                        array(
                            'field' => 'CHANGED_BY',
                            'display' => 'FIELD IS CHANGED BY',
                            'description' => 'Must be PM_USER -> USER_ID Fields',
                            'validation_type' => 'integer', /* only accept string or integer */
                            'validation_length' => '15',
                            'validation_value'  => []
                        ), 
                ), 
        ),
        'WFTASK' => array(
            'database_connection' => 'oracle_nextgen_soa_fullgrant',  // it refer which database connection.. eP DB or SOA DB or any DB setup.
            'table_name' => 'WFTASK',
            'primary_field' => 'TASKID',
            'required_changed_date' => false,  // When script update,set anothers fields changed_date & changed_by require to update or not.
            'field_allow' => 
                array(
                   'state' => 
                        array(
                            'field' => 'STATE',
                            'display' => 'FIELD STATE',
                            'description' => 'MAX NUMBER(4)',
                            'validation_type' => 'null', /* only accept string or integer */
                            'validation_length' => '4',
                            'validation_value'  => ['null'],
                            'remarks' => 'To set task as completed, just put as null value.'
                        ),
                ), 
        ),
       
    );
    


}
