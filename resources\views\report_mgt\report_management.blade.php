@extends('layouts.guest-dash')
@section('cssprivate')
    <link href="{{ asset('css/reportManagement-v1.0.css') }}" rel="stylesheet" />
@endsection
@section('content')
<div class="block log-header">
    <h1>
        <i class=""></i>Report Management
    </h1>
</div>
    <!--menu-->
    <div class="block-alt-noborder">
            <form class="form-horizontal form-bordered" id ="report_form" action="{{ url('/report/management') }}"
                method="post">
                {{ csrf_field() }}
                <div class="block modal-contents">
                    <input type="hidden" id="editid" name="editid" value="" class="form-control"
                        style="width: 100px;">
                    <div class="form-group">
                        <label class="col-md-1 text-left" for="helpdesk_no">Helpdesk No</label>
                        <div class="col-md-5">
                            <input id="helpdesk_no" name="helpdesk_no" type="text" class="form-control">
                        </div>
                        <label class="col-md-1 text-left" for="requester">Requester</label>
                        <div class="col-md-5">
                            <input id="requester" name="requester" type="text" class="form-control">
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-md-1 text-left" for="report_name">Report Name</label>
                        <div class="col-md-11">
                            <textarea type="text" id="report_name" name="report_name" rows="1" class="form-control"></textarea>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="report_description">Description</label>
                        <div class="col-md-11">
                            <textarea type="text" id="report_description" name="report_description" rows="5" class="form-control"></textarea>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <div class="col-md-12 col-md-offset-5">
                        <input type="submit" name="Submit" value="Submit" class="btn btn-sm btn-primary" id="submitbutton">
                    </div>
                </div>
            </form>

            <div class="block modal-contents">
                <h3><i class="fa fa-tasks"></i> <strong>LIST OF REPORT</strong></h3>
                <div class="table-responsive">
                    <table id="report_list_datatable" class="table table-vcenter table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Helpdesk No</th>
                                <th class="text-center">Requester</th>
                                <th class="text-center">Report Name</th>
                                <th class="text-center">Description</th>
                                <th class="text-center">Created Date</th>
                                <th class="text-center">Created By</th>
                                <th class="text-center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($list_report as $row=>$data)
                            <tr>
                                <td class="text-center">{{ $data->helpdesk_no }}</td>
                                <td class="text-center">{{ $data->report_requester }}</td>
                                <td class="text-center">{{ $data->report_name }}</td>
                                <td class="text-left" width="20%" >
                                    <textarea rows="5" class="form-control" style="width: 100%" readonly>{{ $data->report_description }}</textarea>
                                </td>
                                <td class="text-center">{{ $data->created_date }}</td>
                                <td class="text-center">{{ $data->created_by }}</td>
                                <td class="text-center">
                                    <div class="btn-group btn-group-xs">
                                        <a href="/report/edit-report/{{ $data->report_id }}" 
                                           data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                <div><h3></h3></div>
            </div>          
    </div>
@endsection

@section('jsprivate')
<script>
    document.getElementById('page-container').classList = [];
    </script>
<script src="/js/pages/tablesDatatables.js"></script>
    <script>
        App.datatables();
        var APP_URL = {!! json_encode(url('/')) !!}

        function initializeDataTable(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "asc"]
                ],
                columnDefs: [],
                pageLength: 10,
                lengthMenu: [
                    [10, 15, 20, -1],
                    [10, 15, 20, 'All']
                ]
            });
        }
        initializeDataTable('#report_list_datatable');
    </script>
@endsection
