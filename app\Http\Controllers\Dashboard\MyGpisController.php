<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;

class MyGpisController extends Controller {

    use OSBService;
    use SSHService;

    public function getDashboardMyGpis() {
        return view('dashboard.mygpis', []);
    }

    public function checkMonitoringMyGPIS() {
        $listStatisticTotalETLMyGPIS = $this->getListStatisticTotalETLMyGPIS();

        $collect = collect();
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-010', 'MYGPIS_SYARIKAT')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-020', 'MYGPIS_BARANG')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-030', 'MYGPIS_KUMP_PTJ')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-040', 'MYGPIS_PTJ')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-050', 'MYGPIS_BIDANG')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-060', 'MYGPIS_IKLAN')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-070', 'MYGPIS_SST')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-080', 'MYGPIS_KONTRAK')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-090', 'MYGPIS_PEMENUHAN')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-100', 'MYGPIS_PPEROLEHAN')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-110', 'MYGPIS_PPERBELANJAAN')[0]);
        $collect->push($this->getListStatisticTotalFileCreationTransferredBySchedulerToday('GPI-120', 'MYGPIS_PRESTASI')[0]);

        $html = "";
        $html .= "
                <div class='row'>
                    <div class='col-lg-6'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Data MyGPIS</strong> Generated today</h2>
                            </div>
                            <table class='table table-borderless table-striped table-vcenter'>
                                <thead>
                                    <tr>
                                        <th>Module</th>
                                        <th>Service Code</th>
                                        <th>Date</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>";

        foreach ($listStatisticTotalETLMyGPIS as $data) {
            $html .= "
                                    <tr>
                                        <td style='width: 35%;'><strong>$data->modul</strong></td>
                                        <td>$data->service_code</td>
                                        <td>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total</span></td>
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class='col-lg-6'>
                        <div class='block'>
                            <div class='block-title'>
                                <h2><strong>Files Created & Transferred</strong> (2am 11 jobs & 6pm 1 job)</h2>
                            </div>
                                <table class='table table-borderless table-striped table-vcenter'>
                                    <thead>
                                        <tr>
                                            <th>Module</th>
                                            <th>Service Code</th>
                                            <th>Date</th>
                                            <th>Total Created</th>
                                            <th>Total Transferred</th>
                                        </tr>
                                    </thead>
                                <tbody>";

        foreach ($collect as $data) {
            $html .= "
                                    <tr>
                                        <td style='width: 35%;'><strong>$data->modul</strong></td>
                                        <td>$data->service_code</td>
                                        <td style='width:75px'>$data->created_date</td>    
                                        <td class='text-center'><span class='badge label-danger'>$data->total_created</span></td>
                                        <td class='text-center'><span class='badge label-danger'>$data->total_transferred</span></td>    
                                    </tr>";
        }

        $html .= "
                                </tbody>
                            </table>
                        </div>
                    </div> 
                </div>";


        return $html;
    }

    public function getDashboardBatchOutFolder() {
        $batchName = request()->batch_name;
        $lengthFileName = request()->length_filename;

        if ($batchName == null && $lengthFileName == null) {
            return "Invalid Request";
        }
        $outboundFiles = null; // $this->getListEpBatchFolderOUT($batchName, $lengthFileName);
        $totalOutboundFilesPending = 0; // count($outboundFiles);

        $html = "
            <!-- Dashboard Info Block -->
            <div class='widget'>
                <!-- Dashboard Info Title -->
                <div class='widget-extra themed-background-dark'>
                    <h5 class='widget-content-light'>
                        $batchName Monitoring <strong>(OUTBOUND) </strong>
                    </h5>
                </div>
                <!-- END Dashboard Info Title -->

                <!-- Dashboard Info -->
                <table class='table table-borderless table-striped table-vcenter'>
                    <tbody>
                    <tr>
                        <td class='text-right' style='width: 50%;'><strong>Pending (OUT eP Folder)</strong></td>
                        <td><strong><a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/pending/batch/ep-outbound?batch_name=$batchName&length_filename=$lengthFileName'
                            data-title='List Pending Files (OUT eP FOLDER)' >{$totalOutboundFilesPending}</a></strong><small class='text-info' style='font-style: italic;'> Waiting eP transfer files to Integration Server</small></td>
                    </tr>
                    </tbody>
                </table>
                <!-- END Dashboard Info -->
            </div>
            <!-- END Dashboard Info Block -->";

        return $html;
    }

}
