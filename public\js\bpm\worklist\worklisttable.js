/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
function showWorklistTask() {
    $('.worklistProcess').click(function () { 
        
    $('tr').removeClass('hover');
    $(this).parent().addClass('hover');
    
        var taskId = $(this).attr('data-id');
        var csrf = $("input[name=_token]").val();

        document.getElementById("worklist_flow").style.display = "none";
        $('#modal_spinner').modal('show');
        $.ajax({
            type: "POST",
            url: "/bpm/worklist/taskid/" + taskId,
            data: {"_token": csrf, "taskId": taskId},
            error: function (xhr, status, error) {
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
            }
        }).done(function (resp) {
            $('#modal_spinner').modal('hide');
            if (resp.statusApiTaskDetail === 'success') {
                $('#statusApi').hide();
                document.getElementById("worklist_flow").style.display = "block";
                $("#task_id").html(resp.listdata["taskId"]);
                if (resp.listdata["assignees"] !== '') {
                    var link = '';
                    var arrLength = resp.listdata["assignees"].length;
                    var i = 0;
                    resp.listdata["assignees"].forEach(function (entry) {
                        i++;
                        link += '<a href="/find/userlogin?login_id=';
                        link += entry;
                        link += '" target="_blanks">';
                        if (arrLength === 1) {
                            link += entry;
                        } else if (i == arrLength) {
                            link += entry;
                        } else {
                            link += entry + ",";
                        }
                        link += '</a>';
                    });
                    document.getElementById("assignee").innerHTML = link;

                    //enable/disable claim and release button
                    if (arrLength === 1) {
                        $('#claim_task').attr("disabled", true);
                        $('#release_task').attr("disabled", true);
                    } else {
                        if (resp.listdata["acquiredBy"] === null) {
                            $('#claim_task').attr("disabled", false);
                            $('#release_task').attr("disabled", true);
                        } else {
                            $('#claim_task').attr("disabled", true);
                            $('#release_task').attr("disabled", false);
                        }
                    }

                    //enable disable action option and execute button
                    var state = $('#state option:selected').text();
                    if (state === 'ASSIGNED') {
                        $('#action_task').attr("disabled", false);
                        $('#execute_task').attr("disabled", false);
                    } else {
                        $('#action_task').attr("disabled", true);
                        $('#execute_task').attr("disabled", true);
                    }

                } else {
                    document.getElementById("assignee").style.color = "grey";
                    $("#assignee").html('null');
                }
                if (resp.listdata["acquiredBy"] !== '') {
                    if (resp.listdata["acquiredBy"] == null) {
                        document.getElementById("acquired").style.color = "grey";
                        $("#acquired").html('null');
                    } else {
                        var link = '<a href="/find/userlogin?login_id=';
                        link += resp.listdata["acquiredBy"];
                        link += '" target="_blanks">';
                        link += resp.listdata["acquiredBy"];
                        link += '</a>';
                        document.getElementById("acquired").style.color = "black";
                        document.getElementById("acquired").innerHTML = link;
                    }
                } else {
                    document.getElementById("acquired").style.color = "grey";
                    $("#acquired").html('null');
                }
                //composite
                $("#composite").html(resp.listdata["compositeName"] + '!' + resp.listdata["instanceVersion"]);
                //status
                $("#status").html(resp.listdata["state"]);
                //process
                $("#process").html(resp.listdata["process"]);
                //outcome
                if (resp.listdata["outcome"] !== '') {
                    document.getElementById("outcome").style.color = "black";
                    $("#outcome").html(resp.listdata["outcome"]);
                } else {
                    document.getElementById("outcome").style.color = "grey";
                    $("#outcome").html('null');
                }
                //activity
                $("#activity").html(resp.listdata["taskName"]);
                //created
                $("#created").html(resp.listdata["createDateString"]);
                //expiry
                if (resp.listdata["expirationDateString"] !== '') {
                    document.getElementById("expiry").style.color = "black";
                    $("#expiry").html(resp.listdata["expirationDateString"]);
                } else {
                    document.getElementById("expiry").style.color = "grey";
                    $("#expiry").html('null');
                }


                //var newPayload = String(resp.payload).replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;');
                $("textarea#payload").val(resp.payload);

                $("input[name=string_1]").val(resp.listdata["docNumber"]);
                $("input[name=number_1]").val(resp.listdata["docId"]);
                $("input[name=string_2]").val(resp.listdata["docType"]);
                $("input[name=number_2]").val(resp.listdata["docStatus"]);

                //list action
                $('#action_task').empty();
                var listAction = document.getElementById('action_task');

                if (resp.statusListTask == 'success') {
                    $('#statusApi').hide();
                    listAction.add(new Option('SAVE'));
                    for (const [listdata, value] of Object.entries(resp.listaction['action'])) {
                        listAction.add(new Option(value));
                    }
                } else {
                    $('#statusApi').show();
                    $("#statusApi").html(resp.statusListTask);
                }

                //totalAssignee
                $("input[name=total_assignee]").val(resp.totalassignee);
            } else {
                document.getElementById("worklist_flow").style.display = "none";
                $('#statusApi').show();
                $("#statusApi").html(resp.statusApiTaskDetail);

            }
        });
    });
}