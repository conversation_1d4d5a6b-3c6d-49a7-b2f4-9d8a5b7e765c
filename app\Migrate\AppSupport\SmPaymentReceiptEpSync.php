<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\PaymentReceiptService;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmPaymentReceiptEpSync {
 
    use PaymentReceiptService;
    use SupplierService;

    public static function syncEpSupplierPaymentReceipt($date){
        MigrateUtils::logDump(__METHOD__. ' Starting ... ' . __FUNCTION__. '..');
        $dtStartTime = Carbon::now();

        $thisCls = new SmPaymentReceiptEpSync();
        $listReceipts = $thisCls->getListPaymentReceiptEpByDate($date);
        MigrateUtils::logDump(__METHOD__. " Found receipt date on $date , total : ".count($listReceipts ));
        foreach ($listReceipts as $obj){
            //dd($obj);
            $thisCls->saveEpSupplierPaymentReceipt($obj );
        }
       

        MigrateUtils::logDump(__METHOD__. ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)['TakenTime']]);
    }

    protected function saveEpSupplierPaymentReceipt($dataObj) {
      
        $data = [
            'payment_id' => $dataObj->payment_id,
            'payment_date'=> $dataObj->payment_date,
            'payment_paid'=> $dataObj->created_date,
            'payment_amount'=> $dataObj->payment_amt,
            'payment_receipt'=> $dataObj->receipt_no,
            'payment_type'=> $dataObj->bill_type,
            'payment_channel'=> $dataObj->channel_name,
            'payment_card_type'=> $dataObj->card_type,
            'payment_card_type_desc'=> $dataObj->card_type_desc,
            'payment_charge_rate'=> $dataObj->service_charge_gst_rate,
            'payment_molpay_id'=> $dataObj->molpay_transaction_id,
            'payment_reference_id' => $dataObj->transaction_id

        ];

        //MigrateUtils::logDump(__METHOD__. ' >> '.json_encode($data));
        $check = DB::connection('mysql_ep_support')->table('ep_supplier_payment_ep')
                        ->where('payment_id',$dataObj->payment_id)->count();
        if($check > 0){
            $data['changed_at'] = Carbon::now();
            DB::connection('mysql_ep_support')->table('ep_supplier_payment_ep')
                        ->where('payment_id',$dataObj->payment_id)
                        ->update($data);
        }else{
            $data['created_at'] = Carbon::now();
            DB::connection('mysql_ep_support')->table('ep_supplier_payment_ep')
            ->insert($data);
        }
         
    }

}
