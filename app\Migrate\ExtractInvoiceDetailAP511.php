<?php

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;

class ExtractInvoiceDetailAP511
{

    

    public static function run()
    {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        $dateStart = '2018-01-01 00:00:00';
        $dateEnd = '2019-01-01 00:00:00';

//        $dateStart = Carbon::now()->subDay(12)->format('Y-m-d H:i:s');
//        $dateEnd = Carbon::now()->format('Y-m-d H:i:s');

        self::extractInvoiceDetail($dateStart, $dateEnd);

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function runBySchedule($dateStart, $dateEnd)
    {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        self::extractInvoiceDetail($dateStart, $dateEnd);

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }

    public static function runAP511ByList(){
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        $listFile = self::listFilename();
        foreach ($listFile as $data ){
            dump($data);
            $fileObj = DB::connection('mysql_ep_support')
                    ->table('ep_osb_batch_file')
                    ->where('batch_file_id',$data['batch_file_id'])
                    ->first();
            if($fileObj){
                DB::connection('mysql_ep_support')
                    ->table('ep_invoice_detail')
                    ->where('batch_file_id',$fileObj->batch_file_id)
                    ->delete();
                self::insertExtractionInvoice($fileObj); 

            }
  
        }
       
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);

    }
    
    protected static function extractInvoiceDetail($dateStart, $dateEnd)
    {

        $start = microtime(true);

//        $dateStart = '2019-01-01 00:00:00';
//        $dateEnd = '2019-04-01 00:00:00';

        dump('$dateStart :: ' . $dateStart);
        dump('$dateEnd :: ' . $dateEnd);
        dump('Start to extract invoice detail: ' . $dateStart . ' to ' . $dateEnd);

        $totalRecord = DB::connection('mysql_ep_support')->table('ep_osb_batch_file')
            ->where('created_date', '>=', $dateStart)
            ->where('created_date', '<', $dateEnd)->count();

        dump('Total files: ' . $totalRecord);

        try {

            DB::connection('mysql_ep_support')->table('ep_osb_batch_file')
                ->where('created_date', '>=', $dateStart)
                ->where('created_date', '<', $dateEnd)
                ->orderBy('created_date')
                ->chunk(100, function ($rows) use (&$totalRecord) {

                    $fileCount = 0;
                    
                    foreach ($rows as $file) {
                        dump('[' . $file->file_name . ']  File ' . $fileCount . " of " . $totalRecord);
                        self::insertExtractionInvoice($file);
                        $fileCount++;
                    }
                });

            $time_elapsed_secs = microtime(true) - $start;
            dump('Execution time (sec): ' . $time_elapsed_secs);

        } catch (\Exception $exc) {
            $time_elapsed_secs = microtime(true) - $start;
            dump('ERROR: ' . $exc->getMessage());
            dump('Execution time (sec): ' . $time_elapsed_secs);
        }


    }

    protected static function checkIfInvoiceExist($fileId, $fileName, $createdDate)
    {
        $invoice = DB::connection('mysql_ep_support')->table('ep_invoice_detail')
            // ->where('batch_file_id', '=', $fileId)
            ->where('file_name', '=', $fileName)
            ->where('created_date', '=', $createdDate)
            ->count();

        return $invoice;
    }
    
    protected static function insertExtractionInvoice($file)
    {
        if ($file->file_data !== 'Decrypt data is failed!'
            && self::checkIfInvoiceExist($file->batch_file_id, $file->file_name, $file->created_date) === 0) {
            $lineCount = substr_count($file->file_data, "\n");
            $count = 0;
            $ptjCode = "";
            $sapVendorCode = "";
            $bankName = "";
            $supplierName = "";
            $gfmasPaymentId = "";
            $paymentRefNo = "";
            $totalPaymentAmt = "";
            $vendorIdCode = "";
            $entityCode = "";
            $paymentNo = "";
            $cancellationDate = "";
            $paymentAdviceNo = "";
            $paymentAdviceDate = "";
            $paymentDate = "";

            dump('[' . $file->file_name . '] File content line count: ' . $lineCount);
            /* SPLIT DATA HERE */
            foreach (preg_split("/((\r?\n)|(\r\n?))/", $file->file_data) as $line) {

                if ($count % 2 == 0) { // 1st line data
                    $ptjCode = substr($line, 314, 8);
                    $sapVendorCode = ltrim(substr($line, 115, 10));
                    $bankName = rtrim(substr($line, 51, 40));
                    $supplierName = rtrim(substr($line, 133, 100));
                    $totalPaymentAmt = ltrim(substr($line, 276, 13), '0');
                    $paymentRefNo = trim(substr($line, 33, 18));
                    $gfmasPaymentId = trim(substr($line, 20, 13));
                    $vendorIdCode = trim(substr($line, 91, 20));
                    $entityCode = trim(substr($line, 111, 4));
                    $paymentNo = trim(substr($line, 254, 10));
                    $cancellationDate = trim(substr($line, 264, 8));
                    $paymentAdviceNo = trim(substr($line, 289, 17));
                    $paymentAdviceDate = trim(substr($line, 306, 8));
                    $paymentDate = trim(substr($line, 11, 8));

                    if ($cancellationDate == "********" || strlen($cancellationDate) < 8) {
                        $cancellationDate = null;
                    } else {
                        $cancellationDate = Carbon::createFromFormat('dmY', $cancellationDate);
                    }

                    if ($paymentAdviceDate && strlen($paymentAdviceDate) == 8) {
                        $paymentAdviceDate = Carbon::createFromFormat('dmY', $paymentAdviceDate);
                    } else {
                        $paymentAdviceDate = null;
                    }

                    if ($paymentDate && strlen($paymentDate) == 8) {
                        $paymentDate = Carbon::createFromFormat('dmY', $paymentDate);
                    } else {
                        $paymentDate = null;
                    }

                } else { //2nd line data
                    $invoiceNo = rtrim(substr($line, 9, 17));
                    $paymentAmt = ltrim(substr($line, 26, 13), '0');
                    $invoiceDate = trim(substr($line, 1, 8));

                    if ($invoiceDate && strlen($invoiceDate) == 8) {
                        $invoiceDate = Carbon::createFromFormat('dmY', $invoiceDate);
                    } else {
                        $invoiceDate = null;
                    }

                    $dataset = array(
                        [
                            'batch_file_id' => $file->batch_file_id,
                            'file_name' => $file->file_name,
                            'invoice_no' => $invoiceNo,
                            'payment_amount' => $paymentAmt,
                            'total_payment_amount' => $totalPaymentAmt,
                            'sap_vendor_code' => $sapVendorCode,
                            'supplier_name' => $supplierName,
                            'ptj_code' => $ptjCode,
                            'bank_name' => $bankName,
                            '1gfmas_payment_id' => $gfmasPaymentId,
                            'payment_reference_no' => $paymentRefNo,
                            'vendor_id_code' => $vendorIdCode,
                            'entity_code' => $entityCode,
                            'payment_no' => $paymentNo,
                            'cancelation_date' => $cancellationDate,
                            'payment_advice_no' => $paymentAdviceNo,
                            'payment_advice_date' => $paymentAdviceDate,
                            'invoice_date' => $invoiceDate,
                            'payment_date' => $paymentDate,
                            'created_date' => $file->created_date,
                        ]
                    );

                    dump("-------------------- INSERT -------------------- : InvoiceNo: ".$dataset[0]['invoice_no']. ' , PaymentDate: '.$dataset[0]['payment_date']);
                    //dump(\GuzzleHttp\json_encode($dataset));

                    /* INSERTING */
                    DB::connection('mysql_ep_support')->table('ep_invoice_detail')->insert($dataset);
                    dump("-------------------- SUCCESS --------------------");
                    dump("");
                    dump("");
                }
                $count++;
            }
        } else {
            dump('[' . $file->file_name . '] skipped!');
        }
        
    }
    
    
    /**
     * Issue duplicate data in ep_invoice_detail
     * @return type
     */
    protected static function listFilename(){
        $collection = collect([
            ['batch_file_id'=>'017a9353-5031-4879-aa00-f347c01882ba','file_name'=>'1011AP51140000120180320002.GPG'],
            ['batch_file_id'=>'018ae3cc-4ea4-4e1a-8c63-c3c971109689','file_name'=>'1104AP51140000120181024001.GPG'],
            ['batch_file_id'=>'01f0028d-9325-4fe8-8979-b37469ce7b31','file_name'=>'1104AP51140000120190826001.GPG'],
            ['batch_file_id'=>'03594154-b8e2-49e5-8f62-442b79204df2','file_name'=>'1002AP51140000120200308003.GPG'],
            ['batch_file_id'=>'035e05a7-3572-4afc-bd66-43f5ae6fd17d','file_name'=>'1010AP51140000120180227002.GPG'],
            ['batch_file_id'=>'036bfb3d-788c-49c0-aa2f-feec2e55b980','file_name'=>'1104AP51140000120180925001.GPG'],
            ['batch_file_id'=>'03eb9fbb-ff9b-4315-9b34-0bd36985fcb8','file_name'=>'1104AP51140000120181211001.GPG'],
            ['batch_file_id'=>'04d32070-64e1-4d03-b738-fc2035e3f31e','file_name'=>'1110AP51140000120180725001.GPG'],
            ['batch_file_id'=>'0558a9ec-ba74-44b0-93cd-20c8bf2fe2eb','file_name'=>'1104AP51140000120190724001.GPG'],
            ['batch_file_id'=>'0566e2dd-9b47-40b4-b939-7f011122736a','file_name'=>'1032AP51140000120180808002.GPG'],
            ['batch_file_id'=>'05700372-e43b-4109-a819-ece660480755','file_name'=>'1006AP51140000120180223002.GPG'],
            ['batch_file_id'=>'05c1c4ad-e068-42ac-bddd-e88d09d36e1d','file_name'=>'1104AP51140000120180828001.GPG'],
            ['batch_file_id'=>'0628e013-8d6a-4174-b99d-c333575154f1','file_name'=>'1006AP51140000120180223001.GPG'],
            ['batch_file_id'=>'063e785e-68aa-4467-a471-b0bb08efee97','file_name'=>'1032AP51140000120180807002.GPG'],
            ['batch_file_id'=>'0645ecce-8ee5-4e68-a64d-4152595b5e30','file_name'=>'1110AP51140000120180403001.GPG'],
            ['batch_file_id'=>'06ea78e9-75e7-4c43-a894-31555f688365','file_name'=>'1104AP51140000120191220001.GPG'],
            ['batch_file_id'=>'0742561f-875f-4573-815a-b61199969ece','file_name'=>'1110AP51140000120180301001.GPG'],
            ['batch_file_id'=>'07e13549-e243-4106-b40b-a497da3cfcd6','file_name'=>'1106AP51140000120180810002.GPG'],
            ['batch_file_id'=>'feb236cc-df29-463d-a673-f4b6e8d701fa','file_name'=>'1106AP51140000120190509003.GPG'],
            ['batch_file_id'=>'ff5d9b12-2bdd-4edf-b402-597d2fb701e1','file_name'=>'1106AP51140000120180717001.GPG'],
            ['batch_file_id'=>'fff5066d-ecef-4e0c-9271-457560e92368','file_name'=>'1104AP51140000120201008001.GPG']
                ]
        );
        
        return $collection;
    }

}
