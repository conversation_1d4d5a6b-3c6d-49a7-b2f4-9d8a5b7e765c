@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/interfacelog') }}"> INTERFACE LOG </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/ptj') }}"> PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/kumpptj') }}"> KUMP. PTJ </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/pegpengawal') }}"> PEG. PENGAWAL </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/vot') }}"> VOT </a>
            </li>
        </ul>
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/find/masterdata/glaccount') }}"> GL ACCOUNT </a>
            </li>
            <li class="active">
                <a href="{{ url('/find/masterdata/programactivity') }}"> PROGRAM ACTIVITY </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/project') }}"> PROJECT </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/agoffice') }}"> AG OFFICE </a>
            </li>
            <li>
                <a href="{{ url('/find/masterdata/dana') }}"> DANA </a>
            </li>
        </ul>
    </div>
</div>
<div class="widget">
    <div class="block-title widget-extra themed-background-dark">
        <div class="widget-extra themed-background-dark">
            <h5 class='widget-content-light'>
                PROGRAM ACTIVITY - <strong>Master Data</strong>
            </h5>
        </div>
        <div class="block">
            <form id="form-project" action="{{url("/find/masterdata/programactivity")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method" type="hidden" value="POST"/>
                <input name="master_data_type" id="master_data_type" type="hidden" value="PROGRAM_ACTIVITY"/>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="prgcode">Program Activity Code</label>
                    <div class="col-md-5">
                        <input id="prgcode" name="prgcode" class="form-control" placeholder="Program Activity Code" type="text" value="{{ old('prgcode') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="prgname">Program Activity Name </label>
                    <div class="col-md-5">
                        <input id="prgname" name="prgname" class="form-control" placeholder="Program Activity Name" type="text"  value="{{ old('prgname') }}"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="created_date">Created Date </label>
                    <div class="col-md-5">
                        <input id="created_date" name="created_date" class="form-control" placeholder="Created Date" type="date"
                               value="{{ $date }}" />  
                    </div>
                </div>
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    @if(isset($result))
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="mastertable-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Program Activity ID</th>
                    <th class="text-center">Program Activity Code</th>
                    <th class="text-center">Program Activity Desc</th>
                    <th class="text-center">Code Status</th>
                    <th class="text-center">Process Status</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Created Date</th>
                    <th class="text-center">Changed Date</th>
                    <th class="text-center">Service Code</th>
                    <th class="text-center">File Name</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($result as $data)
                <tr>
                    <td class="text-center">{{ $data->im_prg_act_id }}</td>
                    <td class="text-center"><a href="{{url("/find/masterdata/ep?code=")}}{{ $data->prg_act_code }}&type=vot" target="_blank">{{ $data->prg_act_code }}</a></td>
                    <td class="text-center">{{ $data->prg_act_desc }}</td>
                    <td class="text-center">{{ $data->code_status }}</td>
                    <td class="text-center">{{ $data->process_status }}</td>
                    <td class="text-center">{{ $data->record_status }}</td>
                    <td class="text-center">{{ $data->created_date }}</td>
                    <td class="text-center">{{ $data->changed_date }}</td>
                    <td class="text-center">{{ $data->service_code }} - {{ $data->process_id }}</td>
                    <td class="text-center"><a href="{{url("/find/osb/batch/file")}}/{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td> 
                </tr>
                @endforeach
            </tbody>
        </table>
    </div>
    @endif
</div>
<div id="modal-list-vot" class="modal fade" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive ">
                            <table id="mastertable-datatable" class="table table-vcenter table-condensed table-bordered trans-div-detail">
                                
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
    $('#mastertable-datatable').dataTable({
        order: [0, "desc"],
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, 50, -1], [10, 20, 30, 50, 'All']]
    });
</script>
<script>
var APP_URL = {!! json_encode(url('/')) !!}
    $(document).ready(function () {
        $('.select2').select2();

        $('.list-vot-fund').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('.trans-div-detail').html('Please wait ...').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));
            var type = $(this).attr('data-id');
            
            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    var html = '<thead>';
                    if(type === 'B' || type === 'T'){
                        html += '<tr><th class="text-center">PRG ACTIVITY ID</th><th class="text-center">PRG ACTIVITY CODE</th><th class="text-center">DESCRIPTION</th>\n\
                                    <th class="text-center">RECORD STATUS</th><th class="text-center">EFF DATE</th><th class="text-center">EXP DATE</th></tr></thead>';
                    }else{
                        html += '<thead><tr><th class="text-center">SUB SETIA ID</th><th class="text-center">PROJECT CODE - SETIA CODE - SUB SETIA CODE</th><th class="text-center">DESCRIPTION</th>\n\
                                    <th class="text-center">RECORD STATUS</th><th class="text-center">EFF DATE</th><th class="text-center">EXP DATE</th></tr></thead>';
                    }
                    
                    html += '<tbody>';
                    for (const [key, value] of Object.entries(data)) {
                        html += '<tr><td class="text-center">' +value["id"]+'</td>';
                        html += '<td class="text-center">' +value["code"]+'</td>';
                        html += '<td class="text-left">' +value["description"]+'</td>';
                        html += '<td class="text-center">' +value["record_status"]+'</td>';
                        html += '<td class="text-center">' +value["eff_date"]+'</td>';
                        html += '<td class="text-center">' +value["exp_date"]+'</td></tr>';
                    }
                    html += "</tbody>";
                    $('.spinner-loading').hide();
                    $('.trans-div-detail').html(html).fadeIn();
                }
            });

        });
    });

</script>
@endsection