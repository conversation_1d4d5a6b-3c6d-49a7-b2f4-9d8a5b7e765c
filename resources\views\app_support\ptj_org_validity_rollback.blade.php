@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of PTJ Org Validity Rollback</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="ptj_org_validity_rollback_datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Update Script</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center">{{ $user->tab }}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
    <div class="table-responsive">  
        <table id="ptj_org_validity_rollback_1_datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Update Script</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata_record_status_1))
                @foreach ($listdata_record_status_1 as $user)
                <tr>
                    <td class="text-center">{{ $user->tab }}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
App.datatables();
$(document).ready(function () {
    $('#ptj_org_validity_rollback_datatable').DataTable({
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]

    });
    $('#ptj_org_validity_rollback_1_datatable').DataTable({
        columnDefs: [],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]

    });
});
</script>
@endsection



