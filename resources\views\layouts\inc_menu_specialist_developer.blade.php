{{-- GROUP PMO --}}
@if(Auth::user()->hasR<PERSON>('Group PMO')) 
<ul class="sidebar-nav">
<li>
    <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
        <i class="gi gi-stopwatch sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Dashboard CRM</span></a>
    <ul>
        <li class="{{ Request::is('dashboard/crm/pmo') ? 'active' : '' }}">
            <a href="{{url("/dashboard/crm/pmo")}}"><i class="fa fa-folder-open sidebar-nav-icon"></i>
                <span class="sidebar-nav-mini-hide">CRM - PMO</span></a>
        </li>
    </ul>    
</li>
</ul>
@endif

{{-- GROUP DEVELOVER --}}
@if(Auth::user()->isItDeveloperUsers()) 
<ul class="sidebar-nav">
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="gi gi-certificate sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">Sourcing & Fulfillment</span></a>
        <ul>
            <li class="{{ Request::is('find/trans/docno/') ? 'active' : '' }}">
                <a href="{{url("/find/trans/docno/insert_docno")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Carian Transaksi Doc No.</span></a>
            </li>
            <li class="{{ Request::is('find/trans/track/docno/') ? 'active' : '' }}">
                <a href="{{url("/find/trans/track/docno")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Carian Tracking Diary</span></a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-bars sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">BPM API</span></a>
        <ul>
            <li class="">
                <a href="{{url("/bpm/task/find")}}">Task By DocNo</a>
            </li>
            <li class="">
                <a href="{{url("/bpm/instance/query")}}">Instance Query</a>
            </li>
            <li class="">
                <a href="{{url("/bpm/instance/find")}}">Process Manager</a>
            </li>
            <li class="">
                <a href="{{url("/bpm/worklist/find")}}">Worklist Manager</a>
            </li>
            <li class="">
                <a href="{{url("/bpm/service/manager")}}">Service Manager</a>
            </li>
        </ul>
    </li>
    <li>
        <a href="#" class="sidebar-nav-menu"><i class="fa fa-angle-left sidebar-nav-indicator sidebar-nav-mini-hide"></i>
            <i class="fa fa-hourglass-half sidebar-nav-icon"></i><span class="sidebar-nav-mini-hide">OSB</span></a>
        <ul>

            <li>
                <a href="{{url("/find/osb/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/batch/file")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Batch File Log</span></a>
            </li>
            <li>
                <a href="{{url("/osb/file/content/search")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">Find Content File</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/detail/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log Details</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/detail-rquid/log")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log RQ-UID</span></a>
            </li>
            <li>
                <a href="{{url("/find/osb/error")}}"><i class="gi gi-table sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">OSB Log Error</span></a>
            </li>
        </ul>
    </li>
    <li class="sidebar-header">
        <span class="sidebar-header-options clearfix"><a href="javascript:void(0)" data-toggle="tooltip" title="" data-original-title="Tech Refresh Issue"><i class="gi gi-notes_2"></i></a></span>
        <span class="sidebar-header-title">Tech Refresh Issue</span>
    </li>
    <li>
        <a href="#" class="sidebar-nav-submenu"><i class="fa fa-angle-left sidebar-nav-indicator"></i>Monitoring List</a>
        <ul>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-create-sst') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-create-sst")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Create SST</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-closed') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-closed")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Closed</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-spec') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-spec")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task Spec</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-ec') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-ec")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task EC</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-oc') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-oc")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task OC</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-tec') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-tec")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task TEC</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-fec') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-fec")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task FEC</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=qt-stuck-award') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=qt-stuck-award")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">QT - Stuck Task Awarded</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=fl-invoice-cancellation') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=fl-invoice-cancellation")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - Invoice Cancellation</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=fl-poco-cancellation') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=fl-poco-cancellation")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - POCO Cancellation</span></a>
            </li>
            <li class="{{ Request::is('/techrefresh/monitoring/?page=fl-po-expired') ? 'active' : '' }}">
                <a href="{{url("/techrefresh/monitoring/?page=fl-po-expired")}}"><i class="hi hi-search sidebar-nav-icon"></i>
                    <span class="sidebar-nav-mini-hide">FL - PO Expired</span></a>
            </li>
        </ul>
    </li>
</ul>
@endif