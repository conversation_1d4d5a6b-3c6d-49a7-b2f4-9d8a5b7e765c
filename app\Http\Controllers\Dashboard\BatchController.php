<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\Traits\SupplierService;
use SSH;

class BatchController extends Controller {

    use SupplierService;

    public function getDashboardBatch() {
        return view('dashboard.batch', []);
    }

    public function checkBatchFilePending() {
        $list = $this->getDashboardBatchFilePending();
        $html = "";

        $html .= "
        <div>
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>                        
                        <th>Process ID</th>
                        <th>Process Name</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($list as $data) {
            if ($data->total > 0) {
                $data->total = "<a href='#modal-list-data' 
                            class='modal-list-data-action label label-danger' 
                            data-toggle='modal' data-url='/list/1gfmas/batch/pendingfiles/$data->process_id'
                            data-title='List Files (MasterData) Pending in Processing to eP' >{$data->total}</a>";
            }
            $html .= "
            <tr>
                <td style='width: 30%;'><strong>$data->process_id</strong></td>
                <td class='text-center'>$data->process_name</td>
                <td class='text-center'><strong>$data->total</strong></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

    public function checkConnectionGFMAS() {
        $SSH_IGFMAS_SERVER = env('SSH_IGFMAS_SERVER');
        $SSH_IGFMAS_SERVER_USERNAME = env('SSH_IGFMAS_SERVER_USERNAME');
        $data = array();
        $commands = [
            "echo 'exit' | sftp -oPort=2022 $SSH_IGFMAS_SERVER_USERNAME@$SSH_IGFMAS_SERVER",
        ];

        SSH::into('osb')->run($commands, function($line) use (&$data) {
            $result = $line . PHP_EOL;
            //var_dump($result);
            array_push($data, trim($result));
        });

        $collection = collect($data);
        $data = $collection->filter(function ($value, $key) {
            return ( $value != 'stdin: is not a tty' && $value != 'sftp> exit' );
        });

        $telnet = "";
        foreach ($data as $d) {
            $telnet .= $d . " ";
        }

        $html = "<div><strong>{$telnet}</strong></div>";

        return $html;
    }

    public function checkMonitoringSoftcert() {
        //$latestSigningDigicert = $this->getDashboardLatestDigiCertSuccessSigning();
        //$latestSigningTrustgate = $this->getDashboardLatestTrustgateSuccessSigning();
        //$todaylistDashboardSoftcert = $this->getDashboardTotalCertificationAuthority();
        //$latestSigningDigicertDate = $latestSigningDigicert[0]->latest_signing;
        //$latestSigningTrustgateDate = $latestSigningTrustgate[0]->latest_signing;
        $date = request()->dateSearch;
        if ($date == null) {
            $date = Carbon::now()->format('Y-m-d');
        }
        $resultData = $this->getDashboardSpkiSigningSubmitionQtByDate($date);
        $html = "";
        $html .= "
        <div>
            
            <table class='table table-borderless table-striped table-vcenter'>
                <thead>
                    <tr>
                        <th>Certification Authority (CA)</th>
                        <th>Latest Signing Date</th>
                        <th>Total (Today)</th>
                    </tr>
                </thead>
                <tbody>";

        foreach ($resultData as $data) {
            $html .= "
            <tr>
                <td style='width: 50%;'><strong>$data->softcert_provider</strong></td>
                <td style='width: 50%;'><strong>$data->latest_date</strong></td>
                <td class='text-center'><span class='badge label-danger'>$data->total</span></td>
            </tr>";
        }

        $html .= "
                </tbody>
            </table>
        </div>";


        return $html;
    }

}
