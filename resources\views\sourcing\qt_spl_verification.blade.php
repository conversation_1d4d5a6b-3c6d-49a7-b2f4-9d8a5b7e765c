@extends('layouts.guest-dash')

@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Semakan Kelayakan Pembekal Mengikut Kategori
            </h1>
        </div>
    </div>

    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="block-title">
                <h1><i class="fa fa-tasks"></i> <strong>Semakan Kelayakan Pembekal</strong></h1>
            </div>
            <div class="block-title panel-heading epss-title-s1"><br>
                <form id="form-search-task" action="{{ url('find/qt/verifysupp') }}" method="get"
                    class="form-horizontal form-bordered">
                    <label class="col-md-1 control-label" for="module">QT No <span class="text-danger">*</span></label>
                    <div class="col-md-3">
                        <input type="text" id="doc_no" name="doc_no" value="{{ $cariandocno }}" required
                            class="form-control">
                    </div>

                    <label class="col-md-1 control-label" for="module"> Kategori <span
                            class="text-danger">*</span></label>
                    <div class="col-md-5">
                        <select id="kategori" name="kategori"required class="form-control">
                            <option value="0">--Sila Pilih--</option>
                            <option value="C" @if ($cariankategori == 'C') selected @endif>C - Compliance Check( 1
                                tier by EC and 2 tiers by OC)</option>
                            <option value="T" @if ($cariankategori == 'T') selected @endif>T - Technical
                                Evaluation ( 1 tier by EC and 2 tiers by TEC)</option>
                            <option value="P" @if ($cariankategori == 'P') selected @endif>P - Demo/Presentation
                                Evaluation (2 tiers by TEC)</option>
                            <option value="FP" @if ($cariankategori == 'FP') selected @endif>FP - Financial
                                Evaluation by Package (1 tier by EC and 2 tiers by FEC)</option>
                            <option value="FI" @if ($cariankategori == 'FI') selected @endif>FI - Financial
                                Evaluation by Item ( 1 tier by EC and 2 tiers by FEC)</option>
                            <option value="RP" @if ($cariankategori == 'RP') selected @endif>RP - Recommendation by
                                Package (1 tier by EC and 2 tiers by FEC)</option>
                            <option value="RI" @if ($cariankategori == 'RI') selected @endif>RI - Recommendation by
                                Item (1 tier by EC and 2 tiers by FEC)</option>
                        </select>
                    </div>
                    <div class="form-actions form-actions-button text-right " style="margin-right:30px;">
                        <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i> Search</button>
                    </div>
                </form>
            </div>

        </div>
        <!--    AKMAL PEMBEKAL LULUS-->
        @if ($verifylulus != null)

            <div class="block-title">
                <form action="{{ url('/qt/pembekal/lulus/download') }}" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" id="doc_no_input" name="doc_no" value="">
                    <input type="hidden" id="kategori_input" name="kategori" value="">
                    <div class="form-actions form-actions-button text-right ">
                        <button type="submit" value="1" id="downloadfromdb" class="btn btn btn-primary"
                            style="float: right;"><i class="fa fa-download"> Download</i></button>
                    </div>
                </form>
                <h1><i class="fa fa-tasks"></i> <strong>Pembekal Lulus</strong></h1>
            </div>



            <div class="table-responsive">
                <table id="basic-datatable" class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">QT No</th>
                            <th class="text-center">Supplier ID</th>
                            <th class="text-center">Supplier Name</th>
                            <th class="text-center">Supplier Code</th>
                            <th class="text-center">Proposal No</th>
                            <th class="text-center">Submit Date</th>
                            <th class="text-center">Score</th>
                            <th class="text-center">Ranking</th>
                            <th class="text-center">Item Name</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($verifylulus != null)
                            @if (count($verifylulus) > 0)
                                @foreach ($verifylulus as $data)
                                    <tr>
                                        <td class="text-center">{{ $data->qt_no }}</td>
                                        <td class="text-center">{{ $data->supplier_id }}</td>
                                        <td class="text-center">{{ $data->supplier_name }}</td>
                                        <td class="text-center">{{ $data->supplier_no }}</td>
                                        <td class="text-center">{{ $data->proposal_no }}</td>
                                        <td class="text-center">{{ $data->proposal_submit_date }}</td>
                                        <td class="text-center">{{ $data->total_score }}</td>
                                        <td class="text-center">{{ $data->eval_ranking }}</td>
                                        <td class="text-center">{{ $data->item_name }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="12">No Records</td>
                                </tr>
                            @endif
                        @endif
                    </tbody>
                </table>
            </div>
        @endif
        <br><br>
        @if ($verifyGagal != null)
            <div class="block-title">
                <form action="{{ url('/qt/pembekal/gagal/download') }}" method="post">
                    {{ csrf_field() }}
                    <input type="hidden" id="doc_no_gagal" name="doc_no" value="">
                    <input type="hidden" id="kategori_gagal" name="kategori" value="">
                    <div class="form-actions form-actions-button text-right ">
                        <button type="submit" value="1" id="downloadfromdbGagal" class="btn btn btn-primary"
                            style="float: right;"><i class="fa fa-download"> Download</i></button>
                    </div>
                </form>
                <h1><i class="fa fa-tasks"></i> <strong>Pembekal Gagal</strong></h1>
            </div>
            <!--    AKMAL PEMBEKAL GAGAL-->
            <div class="table-responsive">
                <table id="statuswork-datatable" class="table table table-vcenter table-striped">
                    <thead>
                        <tr>
                            <th class="text-center">QT No</th>
                            <th class="text-center">Supplier ID</th>
                            <th class="text-center">Supplier Name</th>
                            <th class="text-center">Supplier Code</th>
                            <th class="text-center">Proposal No</th>
                            <th class="text-center">Submit Date</th>
                            <th class="text-center">Score</th>
                            <th class="text-center">Ranking</th>
                            <th class="text-center">Item Name</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($verifyGagal != null)
                            @if (count($verifyGagal) > 0)
                                @foreach ($verifyGagal as $data)
                                    <tr>
                                        <td class="text-center">{{ $data->qt_no }}</td>
                                        <td class="text-center">{{ $data->supplier_id }}</td>
                                        <td class="text-center">{{ $data->supplier_name }}</td>
                                        <td class="text-center">{{ $data->supplier_no }}</td>
                                        <td class="text-center">{{ $data->proposal_no }}</td>
                                        <td class="text-center">{{ $data->proposal_submit_date }}</td>
                                        <td class="text-center">{{ $data->total_score }}</td>
                                        <td class="text-center">{{ $data->eval_ranking }}</td>
                                        <td class="text-center">{{ $data->item_name }}</td>
                                    </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td class="text-center" colspan="12">No Records</td>
                                </tr>
                            @endif
                        @endif
                    </tbody>
                </table>
            </div>
        @endif
    </div>
    <!-- END Customer Addresses Block -->
    </div>

@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            TablesDatatables.init();
        });
    </script>
    <script>
        $("#downloadfromdb").on("click", function () {
        $(document).ready(function() {
    
                var doc_no = $("#doc_no").val();
                var kategori = $("#kategori").val();
                $("#doc_no_input").val(doc_no);
                $("#kategori_input").val(kategori);
        });
    });
    $("#downloadfromdbGagal").on("click", function () {
        $(document).ready(function() {
    
                var doc_no = $("#doc_no").val();
                var kategori = $("#kategori").val();
                $("#doc_no_gagal").val(doc_no);
                $("#kategori_gagal").val(kategori);
        });
    });
    </script>
@endsection
