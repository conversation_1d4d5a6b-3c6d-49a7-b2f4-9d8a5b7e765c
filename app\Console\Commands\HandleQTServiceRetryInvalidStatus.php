<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;

class HandleQTServiceRetryInvalidStatus extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qt-service-retry-invalid-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To set status completed for QT status not in retry';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            // oracle_nextgen_rpt ,oracle_nextgen_fullgrant 
            $listFilesInvalid = DB::connection('oracle_nextgen_rpt')->select(
              " 
                SELECT sdl.disruption_log_id,sq.qt_no, sdl.DISRUPTION_STAGE,psd.status_name
                  FROM sc_disruption_log sdl,
                       sc_workflow_status sws,
                       pm_status_desc psd,
                       sc_qt sq
                 WHERE sdl.doc_id = sws.doc_id
                   AND sws.doc_id = sq.qt_id
                   AND sws.status_id = psd.status_id
                   AND psd.language_code = 'en'
                   AND sws.doc_type = 'QT'
                   AND sdl.doc_type = 'QT'
                   AND is_success = 0
                   AND psd.status_name IN
                          ('Pending Proposal Evaluation', 'Pending Supplier Finalization',
                           'Pending Letter of Intent Preparation',
                           'Pending Letter of Acceptance Preparation', 'Awarded', 'Cancelled',
                           'Quotation / Tender Closed', 'Pending Recommendation',
                           'Quotation Has Been Cancelled Due To Proposal Validity Expired. Mof Approval Is Required For Further Action (If Any)',
                           'Proceed Manual Due To Ministry Restructuring')
                   AND sws.is_current = 1
                    union                                  
                SELECT sdl.disruption_log_id,sq.qt_no,sdl.DISRUPTION_STAGE, psd.status_name
                  FROM sc_disruption_log sdl,
                       sc_workflow_status sws,
                       pm_status_desc psd,
                       sc_qt sq
                 WHERE sdl.doc_id = sws.doc_id
                   AND sws.doc_id = sq.qt_id
                   AND sws.status_id = psd.status_id
                   AND psd.language_code = 'en'
                   AND sws.doc_type = 'QT'
                   AND sdl.doc_type = 'QT'
                   AND is_success = 0
                   and sws.STATUS_ID in (60008,60009)
                   AND sws.is_current = 1
                   and trunc(sq.PROPOSAL_START_DATE) < trunc(sysdate)     
                   and sq.IS_BSV_REQ=1
                   
                ");
            
            MigrateUtils::logDump('Total Files QT invalid to retry : '.count($listFilesInvalid));
            
            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($listFilesInvalid) {   
                $countRetry = DB::connection('oracle_nextgen_fullgrant')->table('SC_DISRUPTION_LOG')
                        ->whereIn('disruption_log_id',collect($listFilesInvalid)->pluck('disruption_log_id')->toArray())
                        ->count();
                MigrateUtils::logDump('Total in  SC_DISRUPTION_LOG to be set process as 1: '.$countRetry);
                DB::connection('oracle_nextgen_fullgrant')->table('SC_DISRUPTION_LOG')
                        ->whereIn('disruption_log_id',collect($listFilesInvalid)->pluck('disruption_log_id')->toArray())
                        ->update([
                            'is_success' => 1,
                            'changed_date' => Carbon::now(),
                            'changed_by' => 1
                        ]);
                MigrateUtils::logDump('Done update is_success = 1 in SC_DISRUPTION_LOG');
            });  
            
            MigrateUtils::logDump('Completed');
        
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>','<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error '.$this->signature
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            MigrateUtils::logErrorDump('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        }
    }
    
}
