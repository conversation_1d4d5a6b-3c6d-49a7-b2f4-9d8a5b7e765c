@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/app-support/dashboard/pm') }}">PM</a>
            </li>
            <li>
                <a href="{{ url('/app-support/dashboard/sm') }}">SM</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- New Modal for Detail View -->
<div id="detailModal_duplicateRoleError" class="modal fade" tabindex="-1" role="dialog"
    aria-labelledby="detailModalLabel_duplicateRoleError" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <!-- use modal-lg for larger modal size -->
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="detailModalLabel_duplicateRoleError">Duplicate Role Error Detail</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="detailContent_duplicateRoleError">
                <!-- Detail view content will be dynamically loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Modals -->
<div id="infoModal_duplicateRoleError" class="modal fade" tabindex="-1" role="dialog"
    aria-labelledby="infoModalLabel_duplicateRoleError" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="infoModalLabel_duplicateRoleError">Information</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>This dashboard will list users that have duplicate roles.</p>
                <p>1. Click the user record in the dashboard to display detail. Get the User Role ID</p>
                <p>2. Go to Data Patch and Select PM_USER_ROLE. Insert the User Role ID into the Record ID</p>
                <p>3. Modify record_status to 9</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<div id="infoModal_userRoleIssues" class="modal fade" tabindex="-1" role="dialog"
    aria-labelledby="infoModalLabel_userRoleIssues" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="infoModalLabel_userRoleIssues">How to fix?</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p>This dashboard will list users with their roles desync from each other.</p>
                <p>1. Check User Detail in EPSS with Carian User Login</p>
                <p>2. Check SSO and Liferay Roles. Both of these roles and status must be the same with Ep Roles. Any
                    differences must be added / removed</p>
                <p>3. SSO Roles can be modified in identity. Liferay Roles can be modified in EP App.</p>
                <p>Sometimes, there will be Error Message ORA-12154. If this happens, simply refetch again on interval
                    until it is successful</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
<!-- End Modals -->

<!-- Content -->
@if (Auth::user())
    <div class="row">
        <div class="col-lg-4">
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_duplicateRoleError_refresh" class="btn btn-default"
                                data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                    class="fa fa-refresh"></i></a>
                            <a href="javascript:void(0)" id="dash_duplicateRoleError_explain" class="btn btn-warning"
                                data-toggle="tooltip" title="" data-original-title="Info"><i
                                    class="fa fa-question-circle"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        DUPLICATE ROLE ERROR IN EP <strong>(REQUIRES PATCHING PM_USER_ROLE)</strong>
                    </h5>
                </div>
                <div id="dash_duplicateRoleError" style="padding: 20px;">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
            <div class='widget'>
                <div class='widget-extra themed-background-dark'>
                    <div class="widget-options">
                        <div class="btn-group btn-group-xs">
                            <a href="javascript:void(0)" id="dash_userRoleIssues_refresh" class="btn btn-default"
                                data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                    class="fa fa-refresh"></i></a>
                            <a href="javascript:void(0)" id="dash_userRoleIssues_explain" class="btn btn-warning"
                                data-toggle="tooltip" title="" data-original-title="Info"><i
                                    class="fa fa-question-circle"></i></a>
                        </div>
                    </div>
                    <h5 class='widget-content-light'>
                        Users with <strong>Role Issues</strong>
                    </h5>
                </div>
                <div id="dash_userRoleIssues" style="padding: 20px;">
                    <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
@endif

<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!!json_encode(url('/'))!!};

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });

    $(document).ready(function () {
        // Refresh Duplicate Role Error
        $('#dash_duplicateRoleError_refresh').on("click", function () {
            $('#dash_duplicateRoleError').html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
            $.ajax({
                url: APP_URL + '/app-support/dashboard/duplicate-role-error',
                success: function (data) {
                    $data = $(data);
                    $('#dash_duplicateRoleError').hide().html($data).fadeIn();
                }
            });
        });

        // Refresh Users with Role Issues
        $('#dash_userRoleIssues_refresh').on("click", function () {
            $('#dash_userRoleIssues').html("<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>").fadeIn();
            $.ajax({
                url: APP_URL + '/app-support/dashboard/user-role-issue',
                success: function (data) {
                    $data = $(data);
                    $('#dash_userRoleIssues').hide().html($data).fadeIn();
                }
            });
        });

        // DUPLICATE ROLE ERROR
        $.ajax({
            url: APP_URL + '/app-support/dashboard/duplicate-role-error',
            success: function (data) {
                $data = $(data);
                $('#dash_duplicateRoleError').hide().html($data).fadeIn();
            }
        });

        // USER WITH ROLE ISSUES
        $.ajax({
            url: APP_URL + '/app-support/dashboard/user-role-issue',
            success: function (data) {
                $data = $(data);
                $('#dash_userRoleIssues').hide().html($data).fadeIn();
            }
        });

        // Handle row click to open modal and load details
        $(document).on('click', '#dash_duplicateRoleError table tbody tr', function () {
            var userId = $(this).find('td').eq(0).text();
            var roleCode = $(this).find('td').eq(3).text();

            $('#detailContent_duplicateRoleError').html("<div class='text-center'><i class='fa fa-spinner fa-4x fa-spin'></i></div>");

            $.ajax({
                url: APP_URL + '/app-support/dashboard/duplicate-role-error-detail/' + userId + '/' + roleCode,
                success: function (data) {
                    $('#detailContent_duplicateRoleError').html(data);
                }
            });

            $('#detailModal_duplicateRoleError').modal('show');
        });

        // Explain Button Click Handlers
        $('#dash_duplicateRoleError_explain').on("click", function () {
            $('#infoModal_duplicateRoleError').modal('show');
        });

        $('#dash_userRoleIssues_explain').on("click", function () {
            $('#infoModal_userRoleIssues').modal('show');
        });
    });
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection