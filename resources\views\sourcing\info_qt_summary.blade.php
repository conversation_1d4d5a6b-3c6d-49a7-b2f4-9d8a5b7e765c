@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('find/qt/summary')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="doc_no" name="doc_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Sila Masukkan No QT, LA, LI, BD, DL Sahaja..">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
    <!--            <div class="header-section">
                    <h1>
                        <i class="gi gi-search"></i>Sila Masukkan No QT, LA, LI, BD, DL <br>
                        <small></small>
                    </h1>
                </div>-->
</div>
@if($qtinfo == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Rekod Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif
<!--Akmal Region QT INFO & BRIEFING SITE VISIT-->
@if($qtinfo)
<div class="block block-alt-noborder full">
    <!--AKMAL REGION DETAILS INFO-->
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-users"></i> SUMMARY | <font color="yellow">{{ $qtinfo->qt_no }}</font> | <font color="yellow">{{ $qtinfo->qt_id }}</font> </h6>

            @if($newqt!=null)
            <h6> NEW QT NO | <font color="orange">{{ $newqt->qtno2 }}</font> ( <font color="orange">{{ $newqt->qt_id }}</font> )</h6>  
            @endif

            @if($previousqt!=null)
            <h6> PREVIOUS QT NO | <font color="orange">{{ $previousqt->pqtno }}</font> ( <font color="orange">{{ $previousqt->pqtid }}</font> )</h6>  
            @endif

            <h6> STATUS: <font color="yellow">{{ $qtinfo->status_id }} - {{ $qtinfo->status_name }}</font></h6>
        </div>
        <div class="row">
            <div class="col-md-12">
                <h6><strong>{{ $qtinfo->qt_title }}</strong></h6><br />
            </div>    
            <div class="col-md-2">
                <address>
                    <strong>Procurement</strong> : <font color="red">{{ $qtinfo->procurement }}</font><br />
                    <strong>File No</strong> : <font color="red">{{ $qtinfo->file_no }} </font><br />
                    <strong>Evaluation Type</strong> : <font color="red">{{ $qtinfo->evaluation_type2 }}</font><br />
                    <strong>Item/Pakej</strong> : <font color="red">{{ $qtinfo->item_or_pakej }}</font><br />
                    <strong>QT Type</strong> : <font color="red">{{ $qtinfo->jenis_qt }}  |  {{ $qtinfo->openfor2 }}</font><br />
                    @if($qtinfo->checkpanel != null && $qtinfo->checkpanel == '0')
                    <strong>PANEL</strong> : <font color="red">{{ $qtinfo->panel }}</font>
                    @endif 
                    &nbsp;&nbsp;<strong>ZONAL</strong> : <font color="red">{{ $qtinfo->is_zonal }} </font>
                    @if($kodzonal!=null && $qtinfo->is_zonal ='1')
                    <a href='#modal-list-trans-dofnzonal'
                    class='modal-list-data-action ' 
                    data-toggle='modal'  
                    data-url='/find/qt/summary/'
                    data-title='Senarai Zon Yang Telah Ditetapkan'>
                    <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="Klik Untuk Papar!"></i>
                    </strong>
                    </a>  
                    @endif
                    @if($qtinfo->checkpanel != null && $qtinfo->checkpanel == '1')
                    <strong>PANEL</strong> :
                    <i class="gi gi-circle_exclamation_mark  text-INFO" style="font-size: 10pt;" title="Panel! "></i>
                    <span style="font-weight: bolder; color:blue;"> PANEL </span>
                    @endif 
                    <br />
                    <strong>BSV</strong> : <font color="red">{{ $qtinfo->brefingsitevisit }}</font> &nbsp;&nbsp;
                    <strong>Duration</strong> : <font color="red">{{ $qtinfo->contract_duration }} Month </font><br /> 
                </address>
            </div>
            <div class="col-md-4">
                <address>
                    <strong>Publish Date</strong> : <font color="red">{{ $qtinfo->publish_date }}</font><br />
                    <strong>Closing Date</strong> : <font color="red">{{ $qtinfo->closing_date }}</font>
                    @if($qtinfo->checkvalidityiklan != null && $qtinfo->checkvalidityiklan == true)
                    <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Closed! "></i>
                    <span style="font-weight: bolder; color:red;"> CLOSED </span>
                    @endif 
                    <br />
                    <strong>Publish Period</strong> : <font color="red">{{ $qtinfo->publish_period }} Days</font><br />
                    <strong>Proposal Validity End Date</strong> : <font color="red">{{ $qtinfo->proposal_validity_end_date }}</font>
                    @if($qtinfo->checkvalidityqt != null && $qtinfo->checkvalidityqt == true)
                    <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Expired! "></i>
                    <span style="font-weight: bolder; color:red;"> EXPIRED </span>
                    @endif 
                    <br />
                    <strong>Proposal Validity Period</strong> : <font color="red">{{ $qtinfo->qt_validity_period }} Days </font><br />
                    <strong>Fulfillment Type</strong> : <font color="red">{{ $qtinfo->ff_type }} </font><br />
                    <strong>Category | Type</strong> : <font color="red">{{ $qtinfo->category }} | {{ $qtinfo->type }}</font><br />
                </address>
            </div>
            <div class="col-md-6">
                <address>
                    <strong>Created By </strong> : {{ $qtinfo->login_id }} | {{ strtoupper($qtinfo->user_name) }} (DESK OFFICER)<br />
                    <strong>Ministry</strong> : {{ $qtinfo->ministry_code }} - {{ $qtinfo->ministry_name }}<br />
                    <!--<strong>Pegawai Pengawal</strong> : {{ $qtinfo->pp_code }} - {{ $qtinfo->pp_name }}<br />-->
                    <strong>Kumpulan PTJ</strong> : {{ $qtinfo->kptj_code }} - {{ $qtinfo->kptj_name }}<br />
                    <strong>Owner PTJ</strong> : {{ $qtinfo->ptj_code }} - {{ $qtinfo->ptj_name }}<br />
                    <strong>Create For PTJ</strong> : {{ $qtinfo->for_ptj_code }} - {{ $qtinfo->for_ptj_name }}<br />
                    <strong>State</strong> : {{ $qtinfo->state_name }}<br />
                </address>
            </div>
        </div>
    </div>

    <!--AKMAL REGION COMMITTEE edit 12112024-->
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-users"></i> COMMITTEE MEMBER |  </h6>
            @if($kodbinfo!=null)
            <h6><i class="fa fa-building-o"></i> SEMAK KOD BIDANG >>></h6>
            <a href='#modal-list-trans-dofn'
               class='modal-list-data-action ' 
               data-toggle='modal'  
               data-url='/find/qt/summary/'
               data-title='Semak Kod Bidang QT'>
                <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="Kod Bidang yang Diperlukan!"></i>
                </strong>
            </a>   
            @endif

            <h6><i class="fa fa-building-o"></i> PAYLOAD >>></h6>
            <a href='#modal-list-trans-dofn2'
               class='modal-list-data-action ' 
               data-toggle='modal'  
               data-url='/find/qt/summary/'
               data-title='Senarai Kategori Payload'>
                <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="Kategori Janaan Payload"></i>
                </strong>
            </a>   
                    </br>

            <div id="modal-list-trans-dofnzonal" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List of Zone</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
}                                           <th class="text-center">Zone Name</th>
                                            <th class="text-center">State</th>
                                            <th class="text-center">District</th>
                                            <th class="text-center">Division</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if($kodzonal != null)
                                            @foreach($kodzonal as $datazonal)  
                                            <tr>
                                                <td class="text-center">{{ $datazonal->zone_name }}</td>
                                                <td class="text-left">{{ $datazonal->state }}</td>
                                                <td class="text-left">{{ $datazonal->district }}</td>
                                                <td class="text-left">{{ $datazonal->division }}</td> 
                                            </tr>
                                            @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div> 

            <div id="modal-list-trans-dofn" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Semakan Kod Bidang</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Kod Bidang</th>
                                            <th class="text-center">Bidang</th>
                                            <th class="text-center">Sub Bidang</th>
                                            <th class="text-center">Pecahan Sub Bidang</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @if($kodbinfo != null)
                                        @foreach($kodbinfo as $dataA)
                                        <tr>
                                            <td class="text-center">{{ $dataA->categorycode }}</td>
                                            <td class="text-center">{{ $dataA->bidang }}</td>
                                            <td class="text-center">{{ $dataA->subbidang }}</td>
                                            <td class="text-center">{{ $dataA->pecahansubbidang }}</td>
                                        </tr>
                                        @endforeach
                                        @endif
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div> 

            <div id="modal-list-trans-dofn2" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">GENERATED PAYLOAD</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">PENERANGAN INFO</th>
                                            <th class="text-center">KATEGORI PAYLOAD</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                        <td class="text-left"> <i class="gi gi-circle_exclamation_mark text-info"> REFIRE BPM Instance bagi {{$qtinfo->qt_no }} Diperingkat Penciptaan Sebutharga/Tender </td>
                                            <td class="text-centre"> <a class="modal-list-data-action"
                                            href="{{url('/bpm/sourcing/task/qtcreation/')}}?cari={{$qtinfo->qt_no }} " target='_blank' >
                                            <<< Klik Di Sini - Payload QT CREATION >>> </a></td>  
                                        </tr>
                                        <tr>
                                        <td class="text-left"> <i class="gi gi-circle_exclamation_mark text-info"> REFIRE BPM Instance bagi {{$qtinfo->qt_no }} Diperingkat Penilaian (EC, OC, TEC, FEC) </td> 
                                        <td class="text-centre"><a class="modal-list-data-action"
                                            href="{{url('/bpm/sourcing/task/evaluation/')}}?cari={{$qtinfo->qt_no }} " target='_blank' >
                                            <<< Klik Di Sini - Payload QT EVALUATION >>> </a></td>  
                                        </tr>
                                        <tr>
                                        <td class="text-left"> <i class="gi gi-circle_exclamation_mark text-info"> REFIRE BPM Instance bagi {{$qtinfo->qt_no }} Diperingkat Pemilihan Akhir Pembekal (FC) </td>
                                        <td class="text-centre"><a class="modal-list-data-action"
                                            href="{{url('/bpm/sourcing/task/finalization/')}}?cari={{$qtinfo->qt_no }} " target='_blank' >
                                            <<< Klik Di Sini - Payload QT FINALIZATION >>> </a></td> 
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="modal-footer text-center">
                            <font color="black"><h1 class="modal-title"><span id="modal-list-data-footer"><i class="gi gi-circle_exclamation_mark  text-danger"></i> Amaran! Sebarang tindakan adalah direkodkan.</span></h1>  
                            <font color="black"><h1 class="modal-title"><span id="modal-list-data-footer">Tindakan anda berkemungkinan boleh menyebabkan kerosakan pada Aliran Kerja QT!  </span></h1>
                        </div>
                    </div>
                </div>
            </div> 
    </div>
        <div class="row">
            <div class="col-md-7">
                <h6> COMMITTEE MEMBER</h6>
                <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">LOGIN & NAME</th>
                            <th class="text-center">ROLE</th>    
                            <th class="text-center">STATUS</th> 
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($listdata as $datajwtn)
                        <tr>
                            <td class="text-center">{{ strtoupper($datajwtn->committee_id) }}</td>
                            <td class="text-left">{{ $datajwtn->ic_passport }} | {{ strtoupper($datajwtn->member_name) }}</td>
                            <td class="text-left">{{ $datajwtn->member_code }} | {{ $datajwtn->role_desc }}</td>
                            <td class="text-center">{{ $datajwtn->statuscmt }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            <!--akmal add untk committe ptj-->
            <div class="col-md-5">
                <h6>OTHERS COMMITTEE</h6>
                <table id="summary-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">LOGIN & NAME</th>
                            <th class="text-center">ROLE</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($listcommittee as $datacomm)
                        <tr>
                        <!--
                        <td class="text-left">{{ $datacomm->login_id }} | {{ strtoupper($datacomm->name) }}</td>
                        -->
                        <td class="text-left content-link">
                            <a class="modal-list-data-action"
                               href="{{url('/find/org/icno/')}}/{{$datacomm->login_id }}" 
                               target='_blank' >
                                {{ $datacomm->login_id }} </a> 
                                <strong style="font-weight: bolder;">
                                <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="Semak Status"></i>
                                </strong>
                               | {{ $datacomm->name }} </td>
                            <td class="text-left">{{ $datacomm->role }}</td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!--AKMAL REGION BSV-->
    @if($listdata22 != null)
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-building-o"></i> BRIEFING SITE VISIT  | </h6>
            <h6>BSV DATE - <font color="yellow">{{ $getbsv }}</font></h6>
        </div>
        <div class="row">
            <div class="col-md-12">
                <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered" style="text-shadow: TESTING">
                    <thead>
                        <tr>
                            <th class="text-center">Company Name</th>
                            <th class="text-center">Status</th>   
                            <th class="text-center">Attended & BSV Type</th>    
                            <th class="text-center">Zone</th>  
                            <th class="text-center">Title</th>    
                            <th class="text-center">Add Registration
                            <a href='#modal-list-trans-dofn6'
                                class='modal-list-data-action' 
                                data-toggle='modal'  
                                data-url='find/qt/checkingBSV'
                                data-title='Register by Desk Officer'>
                                <strong style="font-weight: bolder;">
                                <i class="gi gi-circle_exclamation_mark text-info" style="font-size: 10pt; padding-left:10px;" title="QT Maintenance By Desk Officer"></i>
                                </strong>
                            </a>   
                            </th>
                            <th class="text-center">Approver</th>    
                            <th class="text-center">Action</th>    
                        </tr>
                    </thead>
                    @foreach ($listdata22 as $key=>$sqtbsv)
                    <tr>
                        <td class="text-left content-link">
                            <a class="modal-list-data-action"
                               href="{{url('/find/mofno')}}/{{$sqtbsv->mof_no }}" 
                               target='_blank' >
                                {{ $sqtbsv->mof_no }} </a> | {{ $sqtbsv->company_name }}</td>
                        <td class="text-center">{{ $sqtbsv->disqualified_stage }}</td>
                        <td class="text-center">{{ $sqtbsv->is_attended }} | {{ $sqtbsv->bsv_type2 }}</td>
                        <td class="text-center">{{ $sqtbsv->zonename }}</td>
                        <td class="text-center">
                            <span class="less{{ $key }}">{{ str_limit($sqtbsv->bsv_desc, $limit = 30, $end = '...') }}</span>
                            <span class="details{{ $key }}" style="display:none">{{ $sqtbsv->bsv_desc }}</span>
                            <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                    });">See More</a>
                        </td>
                        <td class="text-center">{{ $sqtbsv->ispost_registered }}
                            @if($sqtbsv->np == true)
                            <a href='#modal-list-trans-dofn'
                               class='modal-list-data-action ' 
                               data-toggle='modal'  
                               data-url='/find/qt/summary{{ $sqtbsv->ispost_registered }}'
                               data-title='Need Patching {{ $sqtbsv->ispost_registered }}'>
                                <strong style="font-weight: bolder;">
                                    <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt; padding-left:10px;" title="Need Patching disqualified_stage!"></i> 
                                </strong><br />
                            </a>
                            @endif
                        </td>   
                        <td class="text-center">{{ $sqtbsv->qt_approval_request_id }} </a> - {{ $sqtbsv->user_name }}</td>
                        <td class="text-center">{{ $sqtbsv->approver_action_id }}</td>
                    </tr>
                    @endforeach                        
                </table>
            </div>
        </div>
    </div>
    @endif

    <!--AKMAL REGION SEMAKAN TAWARAN-->
    @if($listinv != null)
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-building-o"></i> SEMAKAN TAWARAN SEBUT HARGA | </h6>

            @if($listinv!=null) 
            @foreach($listinv as $datainv)
            <h6> Total Invitation: <font color="yellow">{{ $datainv->totalinv }}</font></h6>
            @endforeach
            @endif

            @if($listRP!=null) 
            @foreach($listRP as $dataRP)
            <h6> Open Link: <font color="yellow">{{ $dataRP->total }}</font></h6>
            @endforeach
            @endif

            @if($listSP!=null) 
            @foreach($listSP as $dataSP)
            <h6> Submitted: <font color="yellow">{{ $dataSP->total }}</font></h6>
            @endforeach
            @endif

            @if($listXSP!=null) 
            @foreach($listXSP as $dataXSP)
            <h6> Not Submitted: <font color="yellow">{{ $dataXSP->total }}</font></h6>
            @endforeach
            @endif

            @if($listdatasemakanx!=null)
            <a href='#modal-list-trans-abc'
               class='modal-list-data-action ' 
               data-toggle='modal'  
               data-url='/find/qt/summary/'
               data-title='Semak Pembekal Tidak Hantar Proposal'>
                <strong style="font-weight: bolder;">
                    <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt; padding-left:10px;" title="Supplier Not Submit Proposal!"></i>
                </strong><br />
            </a>

            <div id="modal-list-trans-abc" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Senarai Pembekal Tidak Hantar Proposal</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">MOF NO | SUPPLIER NAME</th>
                                            <th class="text-center">PROPOSAL NO</th>
                                            <th class="text-center">SUBMIT PROPOSAL</th>
                                            <th class="text-center">SUBMITTION DATE</th>
                                            <th class="text-center">UPDATE DATE</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($listdatasemakanx as $datax)
                                        <tr>
                                            <td class="text-left content-link">
                                                <a class="modal-list-data-action"
                                                   href="{{url('/find/mofno')}}/{{$datax->mof_no }}" 
                                                   target='_blank' >
                                                    {{ $datax->mof_no }} </a> | {{ $datax->supplier_name }}</td>
                                            <td class="text-center">{{ $datax->proposal_no }}</td>
                                            <td class="text-center">{{ $datax->submitted }}</td>
                                            <td class="text-center">{{ $datax->proposal_submit_date }}</td>
                                            <td class="text-center">{{ $datax->changed_date }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div> 
            @endif
        </div>
        @if($listdatasemakan != null)
        <div class="table-responsive">
            <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">MOF NO | SUPPLIER NAME</th>
                        <th class="text-center">PROPOSAL NO</th>
                        <th class="text-center">SUBMIT PROPOSAL</th>
                        <th class="text-center">SUBMITTION DATE</th>
                        <th class="text-center">UPDATE DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($listdatasemakan as $data)
                    <tr>
                        <td class="text-left content-link">
                            <a class="modal-list-data-action"
                               href="{{url('/find/mofno')}}/{{$data->mof_no }}" 
                               target='_blank' >
                                {{ $data->mof_no }} </a> | {{ $data->supplier_name }}</td>
                        <td class="text-center">{{ $data->proposal_no }}</td>
                        <td class="text-center">{{ $data->submitted }}</td>
                        <td class="text-center">{{ $data->proposal_submit_date }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif
    </div>
    @endif

    <!--AKMAL REGION SEMAKAN TAMBAH KOD BIDANG-->
    @if($listdetailbidang != null)
    <div class="block">
        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-building-o"></i> SEMAKAN TAMBAH KOD BIDANG | </h6>
        </div>
        <div class="table-responsive">
            <table id="suppliers-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">MOF NO | SUPPLIER NAME</th>
                        <th class="text-center">APPL NO</th>
                        <th class="text-center">CATEGORY CODE</th>
                        <th class="text-center">RECORD STATUS</th>
                        <th class="text-center">APPLY DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($listdetailbidang as $dataB)
                    <tr>
                        <td class="text-left content-link">
                            <a class="modal-list-data-action"
                               href="{{url('/find/mofno')}}/{{$dataB->mof_no }}" 
                               target='_blank' >
                                {{ $dataB->mof_no }} </a> | {{ $dataB->company_name }}
                        <td class="text-center">{{ $dataB->appl_no }}</td>
                        <td class="text-center">{{ $dataB->categorycode }}</td>
                        <td class="text-center">{{ $dataB->record_status }}</td>
                        <td class="text-center">{{ $dataB->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
    </div>
    @endif
</div>
@endif

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {TablesDatatables.init(); });</script>


<script>
                    var APP_URL = {!! json_encode(url('/')) !!}

            App.datatables();
                    /* Initialize Datatables */
                    var tableListData = $('#basic-datatable').DataTable({
            columnDefs: [{orderable: false, targets: [0]}],
                    pageLength: 10,
                    lengthMenu: [[10, 20, 30, - 1], [10, 20, 30, 'All']]
            });
                    $(document).ready(function () {
            $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
                    $('#basic-datatable').html('').fadeIn();
                    $('#modal-list-data-header').text($(this).attr('data-title'));
                    /* Destroy ID Datatable, To make sure reRun again ID */
                    tableListData.destroy();
                    $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                            type: "GET",
                            success: function (data) {
                            $data = $(data);
                                    $('#basic-datatable').html($data).fadeIn();
                                    /* Re-Initialize Datatable */
                                    tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [{orderable: false, targets: [0]}],
                                    pageLength: 10,
                                    lengthMenu: [[10, 20, 30, - 1], [10, 20, 30, 'All']]
                            });
                                    $('.spinner-loading').hide();
                            }
                    });
            });
            });
            });

</script>

@endsection