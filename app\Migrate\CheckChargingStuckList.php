<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService;
use App\Services\Traits\SourcingService;
use App\EpSupportActionLog;

class CheckChargingStuckList {
    use BPMService;
    use SourcingService;
    
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        //self::insertFromExcel();
        //self::getBPMTask();
        self::refireTaskWaitingCheckCharging();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function refireTaskWaitingCheckCharging() {
        $th = new CheckChargingStuckList;
        $list = $th->getListStuckTaskChargingStatus();
        dump("Total Result: ".count($list));
        
        $counterSuccess = 0;
        foreach ($list as $objData) {
            
            $result = self::triggerBPMCallback($objData->doc_no_order,$objData->doc_no_request);
            if($result["status"]=='success'){
               $counterSuccess++; 
               dump($result);
            }
            
        }
        dump("Total Success Refire : : ".$counterSuccess);
    }
    
    protected static  function triggerBPMCallback($docno,$docnoPRCR) {
        $th = new CheckChargingStuckList;
        
        //Status on the list
        //Check GFM100
        //Check EPP-013
        
        
        /*
         curl http://192.168.63.205:7011/TriggerBPMCallback/v1.0 --header "Content-Type: application/xml" -d "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><quer:QueryBPMCallbackInput xmlns:quer='http://xmlns.oracle.com/pcbpel/adapter/db/QueryBPMCallback'><quer:PoCoNo>CO180000000000000,PO180000000000000</quer:PoCoNo></quer:QueryBPMCallbackInput></soapenv:Body></soapenv:Envelope>"
         */
        if ($docno && strlen($docno) > 8) {
            $checkPOCO = substr($docno, 0, 2);
            if($checkPOCO == 'PO' || $checkPOCO == 'CO'){
                
                $queryCheck = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING');
                $queryCheck->where('service_code', 'EPP-013');
                $queryCheck->where('remarks_1', $docno);
                $checkEpp013ISExist = $queryCheck->count();
                
                if($checkEpp013ISExist > 0 ){
                    
                    $checkingValid = false;
                    $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING');
                    $query->where('service_code', 'GFM-100');
                    $query->where('remarks_1', $docno);
                    $query->where('trans_type', 'OBRes');
                    $query->orderBy('trans_date', 'desc');
                    $gfm100Obj = $query->first();
                    if ($gfm100Obj) {
                        $transDate100 = Carbon::parse($gfm100Obj->trans_date);


                        $query = DB::connection('oracle_nextgen_rpt')->table('OSB_LOGGING');
                        $query->where('service_code', 'EPP-013');
                        $query->where('remarks_1', $docno);
                        $query->where('trans_type', 'OBRes');
                        $query->orderBy('trans_date', 'desc');
                        $epp013Obj = $query->first();
                        if ($epp013Obj) {
                            
                            if($epp013Obj->remarks_2 != null && strlen($epp013Obj->remarks_2) == 10){
                                $checkingValid = true;
                            }
                            
                            /*
                            $transDate013 = Carbon::parse($epp013Obj->trans_date);
                            if ($transDate013->gt($transDate100)) {
                                $checkingValid = true;
                            }
                            */
                        }
                    }


                    if($checkingValid == true){
                        dump('refire ----> '.$docno);
                        $objResult = collect();
                        $resListCr = $th->getTaskBpmByDocNo($docnoPRCR);
                        if ($resListCr && count($resListCr) > 0) {
                            $objBpm2 = $resListCr->first();
                            if($objBpm2){
                                $objResult->put("bpm_instance", $objBpm2->compositeinstanceid);
                                $objResult->put("bpm_composite", $objBpm2->taskdefinitionid);
                                $objResult->put("prcr_doc_no", $docnoPRCR);
                            }
                        }

                        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><quer:QueryBPMCallbackInput xmlns:quer='http://xmlns.oracle.com/pcbpel/adapter/db/QueryBPMCallback'><quer:PoCoNo>$docno</quer:PoCoNo></quer:QueryBPMCallbackInput></soapenv:Body></soapenv:Envelope>";
                        $xmlContents = '"' . $xmlContents . '"';

                        $urlIdentity = "http://192.168.63.205:7011/TriggerBPMCallback/v1.0";
                        $urlHeader = "'Content-Type: application/xml'";
                        
                        $commands = [
                            "curl -k " . $urlIdentity . " --header  " . $urlHeader . "  -d " . $xmlContents,
                        ];
                        SSH::into('osb')->run($commands);

                        sleep(1);
                        $status = 'success';
                        //EpSupportActionLog::updateActionLog($actionLog, 'Completed');

                        return array('status' => $status,'doc_no' => $docno,'data' => $objResult);
                    } 
                }
                
                
            }

        }
        return array('status' => 'failed','doc_no' => $docno,'data'=>'');
  
    }
    
    public static  function triggerBPMCallbackCheckCharging($docno) {
        $xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><quer:QueryBPMCallbackInput xmlns:quer='http://xmlns.oracle.com/pcbpel/adapter/db/QueryBPMCallback'><quer:PoCoNo>$docno</quer:PoCoNo></quer:QueryBPMCallbackInput></soapenv:Body></soapenv:Envelope>";
                        $xmlContents = '"' . $xmlContents . '"';

        $urlIdentity = "http://192.168.63.205:7011/TriggerBPMCallback/v1.0";
        $urlHeader = "'Content-Type: application/xml'";

        $commands = [
            "curl -k " . $urlIdentity . " --header  " . $urlHeader . "  -d " . $xmlContents,
        ];
        SSH::into('osb')->run($commands);
    }
}
