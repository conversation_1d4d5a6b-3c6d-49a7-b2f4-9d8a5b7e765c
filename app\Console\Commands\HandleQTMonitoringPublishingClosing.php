<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use App\Model\Notify\NotifyModel;
use App\Services\Traits\FulfilmentService;

class HandleQTMonitoringPublishingClosing extends Command
{
    use FulfilmentService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-qt-publishing-closing {--test : Run in test mode and send notifications to TEST_PERSONAL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor quotations with high publishing or closing count (300 or more) and send notification';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $isTest = (bool) $this->option('test');
        $mode = $isTest ? 'TEST' : 'PRODUCTION';

        Log::info(self::class . ' starting in ' . $mode . ' mode..', [
            'Date' => Carbon::now()
        ]);

        try {
            // Monitor QT Publishing
            $this->monitorQtPublishing($isTest);

            // Monitor QT Closing
            $this->monitorQtClosing($isTest);

        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
    }

    /**
     * Monitor and notify for QT Publishing
     * 
     * @param bool $isTest
     * @return void
     */
    private function monitorQtPublishing($isTest)
    {
        // Get publishing records
        $publishingRecords = $this->getListQtPublished();
        $totalCount = count($publishingRecords);

        Log::info(self::class . ' Total count of quotations being published: ' . $totalCount);

        // Modified condition: Always send notification in test mode as long as count > 0
        if ($totalCount > 0 && ($isTest || $totalCount >= 300)) {
            // Use TEST_PERSONAL as receiver if in test mode
            $receiver = $isTest ? 'TEST_PERSONAL' : 'NOTIFICATION_QT_PUBLISHING_CLOSING';

            // Create the notification message with test prefix if in test mode
            $currentTime = Carbon::now()->format('Y-m-d h:i A');
            $testPrefix = $isTest ? "**TEST NOTIFICATION**\n" : "";
            $msg = $testPrefix . "📢 *Notification – {$currentTime}*\n" .
                "⚠️ *[ALERT] {$totalCount} Quotation Tenders Scheduled for Publishing at 12 PM*\n" .
                "Please standby and monitor accordingly.";

            $collect = collect(['msg' => $msg]);
            Log::info(self::class . ' >> Publication alert notification created');

            $this->saveNotify($receiver, $collect, 'QT Publishing Alert');

            if ($isTest) {
                Log::info(self::class . ' Publication notification sent to TEST_PERSONAL');
            }
        }
    }

    /**
     * Monitor and notify for QT Closing
     * 
     * @param bool $isTest
     * @return void
     */
    private function monitorQtClosing($isTest)
    {
        // Get closing records
        $closingRecords = $this->getListQtClosing();
        $totalCount = count($closingRecords);

        Log::info(self::class . ' Total count of quotations being closed: ' . $totalCount);

        // Modified condition: Always send notification in test mode as long as count > 0
        if ($totalCount > 0 && ($isTest || $totalCount >= 300)) {
            // Use TEST_PERSONAL as receiver if in test mode
            $receiver = $isTest ? 'TEST_PERSONAL' : 'NOTIFICATION_QT_PUBLISHING_CLOSING';

            // Create the notification message with test prefix if in test mode
            $currentTime = Carbon::now()->format('Y-m-d h:i A');
            $testPrefix = $isTest ? "**TEST NOTIFICATION**\n" : "";
            $msg = $testPrefix . "📢 *Notification – {$currentTime}*\n" .
                "⚠️ *[ALERT] {$totalCount} Quotation Tenders Scheduled for Closing at 12 PM*\n" .
                "Please standby and monitor accordingly.";

            $collect = collect(['msg' => $msg]);
            Log::info(self::class . ' >> Closing alert notification created');

            $this->saveNotify($receiver, $collect, 'QT Closing Alert');

            if ($isTest) {
                Log::info(self::class . ' Closing notification sent to TEST_PERSONAL');
            }
        }
    }

    /**
     * Save notification to the database
     * 
     * @param string $receiver
     * @param \Illuminate\Support\Collection $collect
     * @param string $alertType
     * @return void
     */
    public function saveNotify($receiver, $collect, $alertType)
    {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group';
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring quotations with high ' . strtolower($alertType) . ' count';
        $nty->save();
    }
}