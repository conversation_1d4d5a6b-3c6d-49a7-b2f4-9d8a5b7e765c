@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of Supplier Disciplinary Action</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Mof No</th>
                    <th class="text-center">QT No</th>
                    <th class="text-center">PTJ Code</th>
                    <th class="text-center">PTJ Name</th>
                    <th class="text-center">Jabatan Code</th>
                    <th class="text-center">Jabatan Name</th>
                    <th class="text-center">Ministry Code</th>
                    <th class="text-center">Ministry Name</th>
                    <th class="text-center">Procurement</th>
                    <th class="text-center">Proposal No</th>
                    <th class="text-center">Submitted</th>
                    <th class="text-center">Status Id</th>
                    <th class="text-center">Status Name</th>
                    <th class="text-center">Proposal Validity End Date</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $rowData => $user)
                <tr>
                    <td class="text-center">{{ $user->mof_no }}</td>
                    <td class="text-center">{{ $user->qt_no }}</td>
                    <td class="text-center">{{ $user->ptj_code }}</td>
                    <td class="text-center">{{ $user->ptj_name }}</td>
                    <td class="text-center">{{$user->jabatan_code}}</td>
                    <td class="text-center">{{$user->jabatan_name}}</td>
                    <td class="text-center">{{$user->ministry_code}}</td>
                    <td class="text-center">{{$user->ministry_name}}</td>
                    <td class="text-center">{{$user->procurement}}</td>
                    <td class="text-center">{{$user->proposal_no}}</td>
                    <td class="text-center">{{$user->submitted}}</td>
                    <td class="text-center">{{$user->status_id}}</td>
                    <td class="text-center">{{$user->status_name}}</td>
                    <td class="text-center">{{$user->proposal_validity_end_date}}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



