<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
        <title> DATA FIX</title>
        <meta name="viewport" content="width=device-width" />
        <style type="text/css">
            @media only screen and (max-width: 1024px), screen and (max-device-width: 1024px) {
                body[yahoo] .buttonwrapper { background-color: transparent !important; }
                body[yahoo] .button { padding: 0 !important; }
                body[yahoo] .button a { background-color: #fff; padding: 15px 25px !important; }
            }

            @media only screen and (min-device-width: 1024px) {
                .content { width: 960px !important; }
            }

            table {
                counter-reset: section;
            }

            .count:before {
                counter-increment: section;
                content: counter(section);
            }
        </style>
    </head>
    <body style="margin: 0; padding: 0; font-family: Tahoma,sans-serif; background-color:#fff" yahoo="fix">
        <label class="col-md-1 text-left">Dear All,</label>
        <br /><br />
        @if($setdata)
        <label class="col-md-1 text-left">Attached above is the sql to be {{$setdata[0]->port}}. Kindly be informed.</label>
        <br /><br />
        @endif
        <table border="1" cellpadding="0" cellspacing="0" 
               style="border-collapse: collapse; width: 100%;" class="content">
            <thead style="background-color:#D9D9D9;">
                <tr>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Bil
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Date/Time
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        CRM No. /Redmine No.
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Module
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Problem Description (from CRM)
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Requester (supplier or PTJ, CDC, BPK)
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Problem Type
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Endorsement By
                    </th>
                    <th align="center" width="250px" style="padding: 0 5.4pt; font-size: 9pt !important; border: 1pt solid windowtext; font-size:13px; background-color:#D9D9D9;height: 12pt; ">
                        Endorsement Date
                    </th>
                </tr>
            </thead>
            @foreach($setdata as $data => $listdata)
            <tr>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{++$data}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$listdata->datetime_porting}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    @if($listdata->redmineno != null) {{$listdata->redmineno}}  @else {{$listdata->crm_no}} @endif
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$listdata->modulecode}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;  "> 
                    {{$listdata->problem}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$listdata->req}}  
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;   "> 
                    {{$listdata->probtype}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;  "> 
                    {{$listdata->endorsement_by}} 
                </td>
                <td align="center" width="250px" style="padding-left: 5px;padding-right: 5px; font-size: 9pt !important;  "> 
                    {{$listdata->endorsement_date}} 
                </td>
            </tr>
            @endforeach
        </table>
        <br />
        {{$txt}}
        <br /><br />
        <label class="col-md-1 text-left">Thanks and Regards,</label>
        <br />
        @if($user == 'Norhasfarine')
        <strong><span style="color: #D98D03; font-size:18px; font-family: Monotype Corsiva">{{$user}} Mohd Noor</span><br /></strong>
        {{$team}}
        @elseif ($user == 'Safinah Salleh')
        <strong><span style="color: #683AC7; font-size:14px; font-style: italic; font-family: Tahoma">-{{$user}}-</span><br /></strong>
        <strong><span style="color: #D890F3; font-size:14px; font-family: Tahoma">-Technical & Service Management-</span><br /></strong>
        @elseif ($user == 'Siti Norazreen Bt Abdullah')
        <strong><span style="color: #0F94F4; font-size:12px;font-family: Times New Roman">{{$user}}</span><br /></strong>
        <strong><span style="color: #0F94F4; font-size:12px;font-family: Times New Roman">{{$team}}</span><br /></strong>
        @else
        {{$user}}<br />
        {{$team}}
        @endif<br />
        <img src="{{url('img/template_email.png')}}"/>  
    </body>


</html>
