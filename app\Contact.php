<?php

namespace App;

use Illuminate\Database\Eloquent\Model;

class Contact extends Model {

    protected $primaryKey = "id";
    public $incrementing = false;
    public $timestamps = false;

    /**
     * The accounts that belong to the contact.
     */
    public function accounts() {
        return $this->belongsToMany('App\Contact', 'accounts_contacts', 'contact_id', 'account_id')
                        ->withPivot('id','date_modified');
    }

    /**
     * The email that belong to the contact.
     */
    public function emailsRelation() {
        return $this->belongsToMany('App\EmailAddress', 'email_addr_bean_rel', 'bean_id', 'email_address_id')
                        ->withPivot('id', 'bean_module', 'primary_address', 'reply_to_address', 'date_created', 'date_modified', 'deleted');
    }

    /**
     * The email relation that belong to the contact.
     */
    public function emails() {
        return $this->belongsToMany('App\EmailAddress', 'emails_beans', 'bean_id', 'email_id')
                        ->withPivot('id', 'bean_module', 'campaign_data', 'date_modified', 'deleted');
    }

}
