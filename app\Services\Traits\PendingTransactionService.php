<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description : Get list pending transaction when user unable to delete MOF
 * <AUTHOR>
 */
trait PendingTransactionService {
     
    /**
     * Get list using collect([])
     * @param type $supplierId
     * @return results
     */
    protected function getListPendingTransaction($supplierId) { 
        
        $list = collect([]); 
        
        //query 1      
        $total1 = $this->getListContractIntegrationReqDTONxtgen($supplierId);
        $obj1 = collect([]);
        $obj1->put("transaction",'Contract Integration ReqDTO : CT');
        $obj1->put("total",$total1);
        $list->push($obj1);                
                
        //query 2
        $total2 = $this->getListContractRequestNextgen($supplierId);
        $obj2 = collect([]);
        $obj2->put("transaction",'FulfilmentReqDTO : Contract Request in eP');
        $obj2->put("total",$total2);
        $list->push($obj2);       
                
        //query 3
        $total3 = $this->getListContractOrderNextgen($supplierId);
        $obj3 = collect([]);
        $obj3->put("transaction",'FulfilmentReqDTO : Contract Order in eP');
        $obj3->put("total",$total3);
        $list->push($obj3);        
                       
        //query 4
        $total4 = $this->getListPurchaseRequestNextgen($supplierId);
        $obj4 = collect([]);
        $obj4->put("transaction",'FulfilmentReqDTO : Purchase Request in eP');
        $obj4->put("total",$total4);
        $list->push($obj4);
                        
        //query 5
        $total5 = $this->getListPurchaseOrderNextgen($supplierId);
        $obj5 = collect([]);
        $obj5->put("transaction",'FulfilmentReqDTO : Purchase Order in eP');
        $obj5->put("total",$total5);
        $list->push($obj5);
        
        //query 6
        $total6 = $this->getListContractRequestEP($supplierId);
        $obj6 = collect([]);
        $obj6->put("transaction",'FulfilmentReqDTO : Contract Request in old eP');
        $obj6->put("total",$total6);
        $list->push($obj6);
        
        //query 7
        $total7 = $this->getListContractOrderEP($supplierId);
        $obj7 = collect([]);
        $obj7->put("transaction",'FulfilmentReqDTO : Contract Order in old eP');
        $obj7->put("total",$total7);
        $list->push($obj7);
        
        //query 8
        $total8 = $this->getListPurchaseRequestEP($supplierId);
        $obj8 = collect([]);
        $obj8->put("transaction",'FulfilmentReqDTO : Purchase Request in old eP');
        $obj8->put("total",$total8);
        $list->push($obj8);
        
        //query 9
        $total9 = $this->getListPurchaseOrderEP($supplierId);
        $obj9 = collect([]);
        $obj9->put("transaction",'FulfilmentReqDTO : Purchase Order in old eP');
        $obj9->put("total",$total9);
        $list->push($obj9);   
        
        //query 10
        $total10 = $this->getListDirectPurchaseEP($supplierId);
        $obj10 = collect([]);
        $obj10->put("transaction",'FulfilmentReqDTO : Direct Purchase in eP');
        $obj10->put("total",$total10);
        $list->push($obj10); 
        
        //query 11
        $total11 = $this->getListPurchaseInquiryEP($supplierId);
        $obj11 = collect([]);
        $obj11->put("transaction",'FulfilmentReqDTO : Purchase Inquiry in eP');
        $obj11->put("total",$total11);
        $list->push($obj11);
        
        //query 12 
        $total12 = $this->getListRequestNoteEP($supplierId);
        $obj12 = collect([]);
        $obj12->put("transaction",'FulfilmentReqDTO : Request Note in eP');
        $obj12->put("total",$total12);
        $list->push($obj12);
        
        //query 13 QT(LA)
        $total13 = $this->getListQuotationTenderEP($supplierId);
        $obj13 = collect([]);
        $obj13->put("transaction",'FulfilmentReqDTO : Quotation Tender in eP');
        $obj13->put("total",$total13);
        $list->push($obj13);
        
        //query 14 FL(CO) update
        $total14 = $this->getListContractOrderFLEP($supplierId);
        $obj14 = collect([]);
        $obj14->put("transaction",'FulfilmentReqDTO : FL(Contract Order) in old eP');
        $obj14->put("total",$total14);
        $list->push($obj14);
        
        //query 15
        $total15 = $this->getListRequestNoteOldEP($supplierId);
        $obj15 = collect([]);
        $obj15->put("transaction",'FulfilmentReqDTO : Request Note in old eP');
        $obj15->put("total",$total15);
        $list->push($obj15);
        
        return $list;

    }
    
    /**
     * Get list for CT
     * @param type $supplierID
     * @return results
     */
    protected function getListContractIntegrationReqDTONxtgen($supplierID) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as total 
                FROM ct_contract
                WHERE supplier_id = $supplierID AND record_status = 1");
                return $results[0]->total;
    }
    
    /**
     * Get list for CR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListContractRequestEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'CR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                               (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListContractRequestNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'CR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                               (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CO pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListContractOrderEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'CO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for CO pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListContractOrderNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'CO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseRequestEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'PR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseRequestNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_req_id) as total
                FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_req_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                WHERE a.supplier_id = $supplierID
                AND a.doc_type = 'PR'
                AND a.record_status = 1
                AND b.status_id NOT IN
                (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in old eP
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseOrderEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for PR pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListPurchaseOrderNextgen($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431)
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for DP(SQ) pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     */
    protected function getListDirectPurchaseEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(    
                "SELECT COUNT (a.quote_id) as total
                FROM sc_quote a, sc_quote_supplier b, sc_workflow_status c
                WHERE a.record_status = 1
                AND b.record_status = 1
                AND b.is_submitted = 1
                AND b.supplier_id = $supplierID 
                AND c.record_status = 1
                AND c.is_current = 1
                AND c.doc_type = 'SQ'
                AND c.status_id IN (60852, 60853, 60856, 60857)
                AND a.quote_id = b.quote_id
                AND a.quote_id = c.doc_id
                 ");
        return $results[0]->total;
        
    }
    
    /**
     * Get list for Purchase Inquiry pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     * -- PI -- (pi.created_date < 2018 ) 
     * AND pi.date_to_response >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
     */
    protected function getListPurchaseInquiryEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (pi.purchase_inquiry_id) as total
                FROM sc_purchase_inquiry pi,sc_request_supplier_item rsi,sc_workflow_status ws
                WHERE pi.record_status = 1
                AND rsi.record_status = 1
                AND ws.record_status = 1
                AND ws.is_current = 1
                AND ws.doc_type = 'PI'
                AND rsi.supplier_id = $supplierID 
                AND ws.status_id IN (60751)
                AND  TO_CHAR(pi.CREATED_DATE, 'YYYY-MM-DD') < '2018-01-01'
                AND pi.purchase_inquiry_id = rsi.purchase_inquiry_id
                AND pi.purchase_inquiry_id = ws.doc_id
                ORDER BY pi.purchase_inquiry_id, ws.created_date");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for Request Note pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     * RN -- ( a.created_date < 2018 ) 
     * RN 
     */
    protected function getListRequestNoteEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as total
                FROM sc_request_note a,
                sc_request_item b,
                sc_request_supplier_item c,
                sc_workflow_status d
                WHERE d.doc_type = 'RN'
                AND d.is_current = 1
                AND d.status_id IN (60708,60703)
                AND c.supplier_id IN ($supplierID)
                AND a.request_note_id = b.request_note_id
                AND b.request_item_id = c.request_item_id
                AND a.request_note_id = d.doc_id
                AND TO_CHAR(a.created_date, 'YYYY-MM-DD') > '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for Request Note pending transaction in old Nextgen
     * @param type $supplierID
     * @return results
     * RN -- ( a.created_date < 2018 ) 
     * RN 
     */
    protected function getListRequestNoteOldEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (*) as total
                FROM sc_request_note a,
                sc_request_item b,
                sc_request_supplier_item c,
                sc_workflow_status d
                WHERE d.doc_type = 'RN'
                AND d.is_current = 1
                AND d.status_id IN (60708,60703)
                AND c.supplier_id IN ($supplierID)
                AND a.request_note_id = b.request_note_id
                AND b.request_item_id = c.request_item_id
                AND a.request_note_id = d.doc_id
                AND TO_CHAR(a.created_date, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for Request Note pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     * QT(LA) > 2017
     */
    protected function getListQuotationTenderEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (b.loa_no) as total
                FROM sc_loi_loa a, sc_loa b, sc_workflow_status c
                WHERE a.record_status = 1
                AND a.supplier_id = 54891
                AND b.record_status = 1
                AND c.doc_type = 'LA'
                AND c.is_current = 1
                AND c.status_id = $supplierID
                AND a.loi_loa_id = b.loi_loa_id
                AND TO_CHAR(a.CREATED_DATE, 'YYYY-MM-DD') > '2017-12-31'
                AND b.loa_id = c.doc_id");
            return $results[0]->total;
        
    }
    
    /**
     * Get list for Request Note pending transaction in Nextgen
     * @param type $supplierID
     * @return results
     * (a.CREATED_DATE & c.CREATED_DATE < 2018 ) 
     * FL(CO) 
     */
    protected function getListContractOrderFLEP($supplierID){
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT COUNT (a.fulfilment_order_id) as total
                FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
                ON b.doc_id = a.fulfilment_order_id
                AND b.doc_type = a.doc_type
                AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = $supplierID
                AND a.doc_type = 'CO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41535, 41900, 41910, 41930, 41431)
                AND TO_CHAR(a.created_date, 'YYYY-MM-DD') < '2018-01-01'
                AND TO_CHAR(c.created_date, 'YYYY-MM-DD') < '2018-01-01'");
            return $results[0]->total;
        
    }
    
}

