@extends('layouts.guest-dash')
@section('cssprivate')
    <link href="{{ asset('css/reportManagement-v1.0.css') }}" rel="stylesheet" />
@endsection
@section('content')
    <div class="block log-header">
        <h1>
            <i class=""></i>Edit Report
        </h1>
        <input type="submit" name="back" value="Back" class="col-md-1 col-md-offset-5 btn btn-sm btn-primary" id="backbutton" style="float: right;">
    </div>
    <!--menu-->
    <div class="block-alt-noborder">
        <form class="form-horizontal form-bordered" id ="report_form" action="{{ url('/report/management') }}"
            method="post">
            {{ csrf_field() }}
            <div class="block modal-contents">
                <input type="hidden" id="editid" name="editid" value="{{ $get_report ? $get_report->report_id : '' }}"
                    class="form-control" style="width: 100px;">
                <div class="form-group">
                    <label class="col-md-1 text-left" for="helpdesk_no">Helpdesk No<span
                            class="text-danger">*</span></label>
                    <div class="col-md-5">
                        <input id="helpdesk_no" name="helpdesk_no" type="text" required class="form-control"
                            value="{{ $get_report ? $get_report->helpdesk_no : '' }}">
                    </div>
                    <label class="col-md-1 text-left" for="requester">Requester<span class="text-danger">*</span></label>
                    <div class="col-md-5">
                        <input id="requester" name="requester" type="text" required class="form-control"
                            value="{{ $get_report ? $get_report->report_requester : '' }}">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-1 text-left" for="report_name">Report Name<span
                            class="text-danger">*</span></label>
                    <div class="col-md-11">
                        <textarea type="text" id="report_name" name="report_name" rows="1" required class="form-control">
@if ($get_report)
{{ $get_report ? $get_report->report_name : '' }}
@endif
</textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-1 text-left" for="report_description">Description</label>
                    <div class="col-md-11">
                        <textarea type="text" id="report_description" name="report_description" rows="5" class="form-control">
@if ($get_report)
{{ $get_report ? $get_report->report_description : '' }}
@endif
</textarea>
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-md-1 text-left" for="created_by">Created By</label>
                    <div class="col-md-5">
                        <input id="created_by" name="created_by" class="form-control" readonly
                            value="{{ $get_report ? $get_report->created_by : '' }} {{ $get_report ? $get_report->created_date : '' }}">
                    </div>
                    <label class="col-md-1 text-left" for="changed_by">Changed By</label>
                    <div class="col-md-5">
                        <input id="changed_by" name="changed_by" class="form-control" readonly
                            @if ($get_report) value="{{ $get_report ? $get_report->changed_by : '' }} {{ $get_report ? $get_report->changed_date : '' }}" @endif>
                    </div>
                </div>
            </div>

            <div class="block modal-contents script_info">
                <h3 class="sub-header"><strong>Script Info</strong></h3> <button type="button" id="addScript"
                    class="btn btn btn-primary">Add Script</button>
                <div class="form-group from_script" style="display:none">

                    <input type="hidden" id="scriptid" name="scriptid">
                    <div class="block-options pull-right">
                        <span class="btn btn btn-default pull-right" id="closeFormScript">Close</span>
                    </div>
                    <br /><br />

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="database_env">Database<span
                                class="text-danger">*</span></label>
                        <div class="col-md-5">
                            <select id="database_env" name = "database_env" required class="form-control"
                                style="width: 700px;">
                                <option value="">Please Select</option>
                                <option value="prod">ePerolehan</option>
                                <option value="cms">CMS</option>
                                <option value="crm">ePerolehan CRM</option>
                            </select>
                        </div>
                        <label class="col-md-1 text-left" for="schema_env">Schema<span
                                class="text-danger">*</span></label>
                        <div class="col-md-5">
                            <select id="schema_env" name = "schema_env" required class="form-control"
                                style="width: 700px;">
                                <option value="">Please Select</option>
                                <option value="ngeprpt">eP Rpt</option>
                                <option value="ngepdbs">eP DRC</option>
                                <option value="cdccms">CDC CMS</option>
                                <option value="cdccrs">CDC CRM</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-md-1 text-left" for="script_remarks">Remarks</label>
                        <div class="col-md-11">
                            <textarea type="text" id="script_remarks" name="script_remarks" rows="5" class="form-control"></textarea>
                        </div>
                    </div>

                    <label class="col-md-1 text-left" for="select_script">Script file</label>
                    <input id="choosefile" class="col-md-11" type='file'
                        onchange='onChooseFile(event, onFileLoad.bind(this, "contents"))'
                        accept=".sql" /><br /><br /><br />
                    <p id="contents" style="background-color: black; color: #faf7f7;"></p>
                    <br />
                    <div style="width: 10%; margin: 0 auto;">
                        <button type="button" class="btn btn-sm right btn-primary savescript" style="display: none">
                            <i class="fa fa-save"></i> SAVE SCRIPT
                        </button>
                    </div>
                    <div class="block" id="panel_selected_script" style="display:none;">
                        <div class="block-title">
                            <h2><strong>Script</strong></h2>
                            <div class="block-options pull-right action-today">
                                <a href="#pre_json_desc" data-toggle="collapse" class="btn btn-alt btn-default"><i
                                        class="fa fa-angle-down"></i></a>
                            </div>
                        </div>
                        <pre id="pre_json_desc" class="" class="collapse show">
                                 <code style="float: left; color:#f00c0c;" id="selected_script_table_json_desc"></code>
                                </pre>
                    </div>
                </div>

                <h5 class="sub-header"><strong>List of script</strong></h5>
                <div id="report-script-data" class="panel-group">
                    @if ($get_script && count($get_script) > 0)
                        @foreach ($get_script as $key => $patchRow)
                            <div class='panel panel-default'>
                                <div class='panel-heading'>
                                    <div class="form-group">
                                        <div class="col-md-9">
                                            <h4 class='panel-title' style='margin-bottom: 5px;'>{{ $key + 1 }} <i
                                                    class='fa fa-angle-right'></i>
                                                <a class='accordion-toggle' data-toggle='collapse' data-parent='#faq1'
                                                    href='#script_{{ $patchRow->script_id }}'>
                                                    {{ $patchRow->script_name }}</a>
                                                <a class='accordion-toggle' style='margin-left: 30px;'>(Created By :
                                                    {{ $patchRow->created_by }} | {{ $patchRow->created_date }})</a>
                                            </h4>
                                        </div>
                                        <div class="col-md-2">
                                            <a script_id = "{{ $patchRow->script_id }}"
                                                db_env = "{{ $patchRow->env_database }}"
                                                sc_env = "{{ $patchRow->env_schema }}"
                                                sc_remark = "{{ $patchRow->remarks }}" data-toggle="tooltip"
                                                class="btn btn-sm btn-primary editbutton"><i class="fa fa-edit">
                                                    Edit</i></a>
                                            <a href="{{ url('/report/edit-report/script/download') }}/{{ $patchRow->script_id }}"
                                                target="_blank" class="btn btn-sm btn-primary"><i
                                                    class="fa fa-download"></i> Download</a>
                                        </div>
                                        <div class="pull-right">
                                            <a href="javascript:void(0)" data-toggle="tooltip"
                                                onclick="onClickDeleteScript(this)"
                                                data-script-id="{{ $patchRow->script_id }}"
                                                data-report-id="{{ $patchRow->report_id }}" title=""
                                                data-original-title=""><i class="fa fa-times"></i></a>
                                        </div>
                                    </div>
                                </div>

                                <div id='script_{{ $patchRow->script_id }}' class='panel-collapse collapse'>
                                    <div class='panel-body'>
                                        <pre>
                                        <code style='float: left; color:#faf7f7;' id='selected_script_table_json_desc'>{{ $patchRow->script_text }}</code>
                                    </pre>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
            <div class="block modal-contents attach_info">
                <input type="hidden" id="attachid" name="attachid">

                <h3 class="sub-header"><strong>Result Info</strong></h3><button type="button" id="addAttach"
                    class="btn btn btn-primary">Add Attachment</button>
                <div class="form-group form_attach" style="display:none">
                    <div class="form-group">
                        <div class="block-options pull-right">
                            <span class="btn btn btn-default pull-right" id="closeFromAttach">Close</span>
                        </div>
                        <br /><br />
                        <label class="col-md-1 text-left" for="output_remarks">Remarks</label>
                        <div class="col-md-11">
                            <textarea type="text" id="output_remarks" name="output_remarks" rows="5" class="form-control"></textarea>
                        </div>
                    </div>

                    <label class="col-md-1 text-left" for="select_attachment">Output file</label>
                    <div class="ux-a-file-upload" id ="upload_attach">
                        <input id="choosefileUpload" type='file'
                            onchange='onChooseFileUpload(event, onFileLoad.bind(this, "content"))' />
                        <div style="width: 10%; margin: 0 auto;">
                            <button type="button" class="btn btn-sm right btn-primary save_attachment"
                                style="display: none">
                                <i class="fa fa-save"></i> Save Result
                            </button>
                        </div>
                    </div>
                </div>

                <h5 class="sub-header"><strong>List of Attachment</strong></h5>
                <div id="upload_attachment_data" class="panel-group">
                    @if (isset($get_attachment) && count($get_attachment) > 0)
                        @foreach ($get_attachment as $key => $patchRow)
                            <div class='panel panel-default'>
                                <div class='panel-heading'>
                                    <div class="form-group">
                                        <h4 class='panel-title' style='margin-bottom: 5px;'>
                                            <div class="col-md-9">
                                                {{ $key + 1 }} <i class='fa fa-angle-right'></i>
                                                <a class='accordion-toggle' data-toggle='collapse' data-parent='#faq1'
                                                    href='#uploadAttached_{{ $patchRow->result_id }}'>
                                                    {{ $patchRow->file_name }}</a>
                                                <a class='accordion-toggle' style='margin-left: 30px;'>(Created By :
                                                    {{ $patchRow->created_by }} | {{ $patchRow->created_date }})</a>
                                            </div>
                                            <div class="col-md-2">
                                                <a result_id = "{{ $patchRow->result_id }}"
                                                    description = "{{ $patchRow->result_description }}"
                                                    data-toggle="tooltip" class="btn btn-sm btn-primary editattach"><i
                                                        class="fa fa-edit">
                                                        Edit</i></a>
                                                <a href="{{ url('/report/edit-report/attachment/download') }}/{{ $patchRow->result_id }}"
                                                    target="_blank" class="btn btn-sm btn-primary"><i
                                                        class="fa fa-download"></i> Download</a>
                                            </div>
                                            <div class="pull-right">
                                                <a href="javascript:void(0)" data-toggle="tooltip"
                                                    onclick="onClickDeleteAttachment(this)"
                                                    data-result-id="{{ $patchRow->result_id }}"
                                                    data-record-id="{{ $patchRow->report_id }}" title=""
                                                    data-original-title=""><i class="fa fa-times"></i></a>
                                            </div>
                                        </h4>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @endif
                </div>
            </div>
            <div class="form-group">
                <div class="col-md-12 col-md-offset-5">
                    <input type="submit" name="Submit" value="Submit" class="btn btn-sm btn-primary"
                        id="submitbutton">
                </div>
            </div>
        </form>
    </div>
@endsection

@section('jsprivate')
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        $("#addScript").on("click", function() {
            $('.from_script').show();
            $('#scriptid').val("");
            $('#database_env').val("");
            $("#database_env").val("");
            $("#script_remarks").val("");
            $("#addScript").hide();
        });

        $("#closeFormScript").on("click", function() {
            $('.from_script').hide();
            $('#scriptid').val("");
            $('#database_env').val("");
            $("#database_env").val("");
            $("#script_remarks").val("");
            $("#addScript").show();
        });

        $("#addAttach").on("click", function() {
            $('.form_attach').show();
            $('#output_remarks').val("");
            $("#addAttach").hide();
        });

        $("#closeFromAttach").on("click", function() {
            $('.form_attach').hide();
            $('#output_remarks').val("");
            $("#addAttach").show();
        });

        let
            contentScript = '';
        let
            fileNameScript = '';

        function onFileLoad(elementId, event) {
            document.getElementById(elementId).innerText = event.target.result;
            contentScript = event.target.result;
        }

        function onChooseFile(event, onLoadFileHandler) {
            if (typeof window.FileReader !== 'function')
                throw ("The file API isn't supported on this browser.");
            let input = event.target;
            if (!input)
                throw ("The browser does not properly implement the event object");
            if (!input.files)
                throw ("This browser does not support the `files` property of the file input.");
            if (!input.files[0])
                return undefined;
            let file = input.files[0];
            let fr = new FileReader();
            fr.onload = onLoadFileHandler;
            fileNameScript = file.name;
            fr.readAsText(file);
            console.log('2222')
            $('.savescript').show();
        }

        function onClickDeleteScript(obj) {
            var report_id = $('#editid').val();
            var scriptId = $(obj).data('scriptId');

            $.ajax({
                type: "POST",
                url: "/report/edit-report/delete-script/" + report_id,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'report_id': report_id,
                    'script_Id': scriptId
                },
                success: function(data) {
                    $("#report-script-data").html("");
                    if (data.hasOwnProperty('dataHtml')) {
                        $("#report-script-data").append(data.dataHtml);
                    }

                }
            });
        }

        $('.savescript').click(function() {
            console.log('try')
            var script_id = $('#scriptid').val();
            var report_id = $('#editid').val();
            var schema = $(schema_env).val();
            var database = $(database_env).val();
            var remarks = $(script_remarks).val();


            function checkMandatoryField(field, fieldName) {
                if (!field) {
                    alert(fieldName + ' is mandatory. Please provide a value.');
                    return false; // Stop further execution
                }
                return true;
            }

            // Check mandatory fields
            if (!checkMandatoryField(database, 'Database') || !checkMandatoryField(schema, 'Schema')) {
                return; // Stop further execution
            }

            $('.savescript').hide();
            $('#panel_selected_script').hide();
            var script = contentScript;
            var encodedString = btoa(script);

            $('#contents').html("");
            $.ajax({
                type: "POST",
                url: "/report/edit-report/createscript/" + report_id,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'script': encodedString,
                    'scriptName': fileNameScript,
                    'schema_env': schema,
                    'script_remarks': remarks,
                    'database_env': database,
                    'script_id': script_id,
                    'report_id': report_id
                },
                success: function(data) {
                    $("#report-script-data").html("");
                    if (data.hasOwnProperty('dataHtml')) {
                        $("#report-script-data").append(data.dataHtml);
                    }
                }
            });
        });

        function onChooseFileUpload(event, onLoadFileHandler) {
            if (typeof window.FileReader !== 'function')
                throw ("The file API isn't supported on this browser.");
            let input = event.target;
            if (!input)
                throw ("The browser does not properly implement the event object");
            if (!input.files)
                throw ("This browser does not support the `files` property of the file input.");
            if (!input.files[0])
                return undefined;
            let file = input.files[0];
            let fr = new FileReader();
            fr.onload = onLoadFileHandler;
            fileNameScript = file.name;
            fr.readAsText(file);
            $('.save_attachment').show();
        }

        $('.save_attachment').click(function() {
            var documentData = new FormData();
            var report_id = $('#editid').val();
            var attach_id = $('#attachid').val();
            var remarks = $('#output_remarks').val();
            $('.save_attachment').hide();
            documentData.append('upload_attachment', $('input#choosefileUpload')[0].files[0]);
            documentData.append("_token", "{{ csrf_token() }}");
            documentData.append("report_id", report_id);
            documentData.append("attach_id", attach_id);
            documentData.append("remarks", remarks)

            $.ajax({
                type: "POST",
                url: "/report/edit-report/upload-attachment/" + report_id,
                '_token': $('input[name=_token]').val(),
                data: documentData,
                cache: false,
                contentType: false,
                processData: false,
            }).done(function(data) {
                $("#upload_attachment_data").html("");
                if (data.hasOwnProperty('data_upload')) {
                    $("#upload_attachment_data").append(data.data_upload);
                }
            });
        });

        function onClickDeleteAttachment(obj) {
            var report_id = $('#editid').val();
            var attach_id = $(obj).data('resultId');

            $.ajax({
                type: "POST",
                url: "/report/edit-report/delete-attachment/" + attach_id,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'attachId': attach_id,
                    'reportId': report_id
                },
                success: function(data) {
                    $("#upload_attachment_data").html("");
                    if (data.hasOwnProperty('dataUpload')) {
                        $("#upload_attachment_data").append(data.dataUpload);
                    }

                }
            });
        }

        $('#submitbutton').click(function() {
            var databaseInput = document.getElementById('database_env');
            var schemeInput = document.getElementById('schema_env');

            databaseInput.removeAttribute('required');
            schemeInput.removeAttribute('required');
        });

        $('#backbutton').click(function() {
            window.location.href = '/report/management';
        });

        $(document).ready(function() {
            $('.script_info').on("click", '.editbutton', function() {
                $('.from_script').show();
                let
                    script_id = $(this).attr('script_id');
                let
                    db_name = $(this).attr('db_env');
                let
                    sc_name = $(this).attr('sc_env');
                let
                    sc_remark = $(this).attr('sc_remark');
                $("#scriptid").val(script_id);
                $("#database_env").val(db_name);
                $("#schema_env").val(sc_name);
                $("#script_remarks").val(sc_remark);
            });
        });

        $(document).ready(function() {
            $('.attach_info').on("click", '.editattach', function() {
                $(".save_attachment").show();
                let
                    result_id = $(this).attr('result_id');
                let
                    desc = $(this).attr('description');
                $("#attachid").val(result_id);
                $("#output_remarks").val(desc);
            });
        });
    </script>
@endsection
