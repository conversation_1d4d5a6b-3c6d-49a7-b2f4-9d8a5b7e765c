<?php

namespace App\Services\Traits;

use DB;
use Carbon\Carbon;
use App\Migrate\MigrateUtils;
use SSH;

/**
 * Description of ePLoginStatisticService
 *
 * <AUTHOR>
 */
trait ePLoginStatisticService {

    /**
     * show list of user login (PTJ&SUPPLIER), data will be saved into table ep_login_statistic
     * This to capture every one hour. The query to get info statistic within one hour.
     * @return list
     */    
    protected function getLoginStatistic($dateStart,$dateEnd) {
        
         $dtStartTime = Carbon::now();
         
         $query = "SELECT pu.ORG_TYPE_ID as user_type, ppd.CODE_NAME as user_name, CASE WHEN pu.org_type_id in ('5','2','7','10','13','4','11','1','6') THEN 'PTJ'
                WHEN pu.org_type_id = 15 THEN 'SUPPLIER'
                END AS user_group
                ,to_char(plh.LOGIN_DATE,'yyyy-mm-dd') as date_login,to_char(plh.LOGIN_DATE,'HH24') as time_login, count(DISTINCT pu.user_id) as login_total 
                FROM pm_login_history plh, pm_user pu, pm_parameter_desc ppd
                where plh.USER_ID = pu.USER_ID 
                AND pu.org_type_id = ppd.parameter_id
                AND ppd.language_code = 'en'
                 and pu.org_type_id in ('5','2','7','10','13','4','11','1','6', '15')
                AND plh.LOGIN_DATE >= TO_DATE('$dateStart', 'YYYY-MM-DD HH24:MI:SS')
                AND plh.LOGIN_DATE < TO_DATE('$dateEnd', 'YYYY-MM-DD HH24:MI:SS')
                group by  pu.ORG_TYPE_ID, ppd.CODE_NAME,to_char(plh.LOGIN_DATE,'yyyy-mm-dd'),to_char(plh.LOGIN_DATE,'HH24')"; 
         
        MigrateUtils::logDump(__METHOD__.'>> '.$query);
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query );
        
        $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- '.
                    ' , Total: '.count($results).' Taken Time : '
                    .  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        MigrateUtils::logDump(__METHOD__.'>> '.$logsdata);
        return $results;
    }
    
    /**
     * show list of user login (PTJ&SUPPLIER), data will be saved into table ep_login_statistic based on date, time_login will be null
     * @return list
     */    
    protected function getLoginDailyStatistic($dateStartD,$dateEndD) {
        
         $dtStartTime = Carbon::now();
         
         $query = "SELECT pu.ORG_TYPE_ID as user_type, ppd.CODE_NAME as user_name, CASE WHEN pu.org_type_id in ('5','2','7','10','13','4','11','1','6') THEN 'PTJ'
                WHEN pu.org_type_id = 15 THEN 'SUPPLIER'
                END AS user_group
                ,to_char(plh.LOGIN_DATE,'yyyy-mm-dd') as date_login, count(DISTINCT pu.user_id) as login_total 
                FROM pm_login_history plh, pm_user pu, pm_parameter_desc ppd
                where plh.USER_ID = pu.USER_ID 
                AND pu.org_type_id = ppd.parameter_id
                AND ppd.language_code = 'en'
                 and pu.org_type_id in ('5','2','7','10','13','4','11','1','6', '15')
                AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI:SS') >= '$dateStartD'
                AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI:SS') <= '$dateEndD'
                group by  pu.ORG_TYPE_ID, ppd.CODE_NAME,to_char(plh.LOGIN_DATE,'yyyy-mm-dd')"; 
         
        MigrateUtils::logDump(__METHOD__.' Quert:: '.$query);
        
        $results = DB::connection('oracle_nextgen_rpt')->select($query );
        
        $logsdata = self::class . ' Query Date Start : '.$dateStartD.' , Query Date End : '.$dateEndD.' , Completed --- '.
                    ' , Total: '.count($results).' Taken Time : '
                    .  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        MigrateUtils::logDump(__METHOD__.'>> '.$logsdata);
        return $results;
    }
    
    /**
     * show list of user login (PTJ&SUPPLIER), data will be saved into table ep_login_statistic based on range date monthly, time_login will be null, data capture year, month
     * @return list
     */    
    protected function getLoginMonthlyStatistic($dateStartD,$dateEndD) {
        
        $dtStartTime = Carbon::now();
        
        $query = "SELECT pu.ORG_TYPE_ID as user_type, ppd.CODE_NAME as user_name, CASE WHEN pu.org_type_id in ('5','2','7','10','13','4','11','1','6') THEN 'PTJ'
        WHEN pu.org_type_id = 15 THEN 'SUPPLIER'
        END AS user_group,
        to_char(plh.LOGIN_DATE,'yyyy') as year_login, 
        TO_NUMBER(to_char(plh.LOGIN_DATE,'mm')) as month_login,
        count(DISTINCT pu.user_id) as login_total 
        FROM pm_login_history plh, pm_user pu, pm_parameter_desc ppd
        where plh.USER_ID = pu.USER_ID 
        AND pu.org_type_id = ppd.parameter_id
        AND ppd.language_code = 'en'
         and pu.org_type_id in ('5','2','7','10','13','4','11','1','6', '15')
        AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI:SS') >= '$dateStartD'
        AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI:SS') <= '$dateEndD'
        group by  pu.ORG_TYPE_ID, ppd.CODE_NAME,to_char(plh.LOGIN_DATE,'yyyy'),to_char(plh.LOGIN_DATE,'mm')"; 
        
       MigrateUtils::logDump(__METHOD__.' Quert:: '.$query);
       
       $results = DB::connection('oracle_nextgen_rpt')->select($query );
       
       $logsdata = self::class . ' Query Date Start : '.$dateStartD.' , Query Date End : '.$dateEndD.' , Completed --- '.
                   ' , Total: '.count($results).' Taken Time : '
                   .  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
       MigrateUtils::logDump(__METHOD__.'>> '.$logsdata);
       return $results;
   }

    protected function getLoginStatisticCurr15MinuteBefore($DateTimeString,$rangeTime = null) {
        
        $timeNowResult = Carbon::parse($DateTimeString)->format('Y-m-d H:i');
        
        $timeBeforeResult = null;
        if($rangeTime != null){
            $timeBefore = Carbon::parse($DateTimeString)->subMinutes($rangeTime);
            $timeBeforeResult = $timeBefore->format('Y-m-d H:i');  
        }
        
        
        $query ="SELECT pu.ORG_TYPE_ID as user_type, CASE WHEN pu.org_type_id in ('5','2','7','10','13','4','11','1','6') THEN 'PTJ'
                WHEN pu.org_type_id = 15 THEN 'SUPPLIER'
                END AS user_group
                , count(*) as login_total 
                FROM pm_login_history plh, pm_user pu, pm_parameter_desc ppd
                where plh.USER_ID = pu.USER_ID 
                AND pu.org_type_id = ppd.parameter_id
                AND ppd.language_code = 'en'
                and pu.org_type_id in ('5','2','7','10','13','4','11','1','6', '15') ";
       
        if($rangeTime == null){
            $query = $query." AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI') = '$timeNowResult'";
        }else{
            $query = $query." AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI') >= '$timeBeforeResult'";   
            $query = $query." AND TO_CHAR(plh.LOGIN_DATE, 'YYYY-MM-DD HH24:MI') <= '$timeNowResult'";   
        }
        $query = $query." group by  pu.ORG_TYPE_ID";
                 
        $results = DB::connection('oracle_nextgen_rpt')->select($query );  
        return $results;
    }
    
     //count_session-supplier.sql 
    protected function getLoginStatisticSessionPTJ() {
               
        $query ="select count(*) as ptj_total from oam_session where LENGTH(USERID)=12";                 
        $results = DB::connection('oracle_oam')->select($query );  
        return $results;
    }
    
    //count_session.sql
    protected function getLoginStatisticSessionSupp() {
               
        $query ="select ((select count(*) from  (select distinct(USERID) from oam_session)) - ( select count(*) from oam_session where length(USERID) = 12)) supplier from dual";                 
        $results = DB::connection('oracle_oam')->select($query );  
        return $results;
    }
    
    protected function getTotallogePByHourDetails(){
        
        $timeToday = Carbon::now()->subHour(1)->toTimeString();
        $currTime = $timeToday;        
        $currTimeResult = Carbon::parse($currTime)->format('H');
        $LastResult = $currTimeResult.':00:00';
        $dateNow = Carbon::now()->toDateString();  
               
        $results = DB::connection('mysql_ep_support')->select(
                 
            "SELECT user_group, user_name,time_login, login_total
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$dateNow'
            AND time_login = '$LastResult'
            UNION
            SELECT user_group, user_name,time_login, login_total
            FROM ep_login_statistic 
            WHERE user_group = 'SUPPLIER' 
            AND DATE(date_login) = '$dateNow'
            AND time_login = '$LastResult'
            ORDER BY time_login DESC");    
        return $results;
        
    }
    
    protected function getTotallogePByDailyDetails(){
        
         $results = DB::connection('mysql_ep_support')->select(
                 
            "SELECT  user_name AS userName, user_group AS userGroup, user_type AS userType, date_login AS LogDate,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            GROUP BY userName ,userGroup, userType, LogDate
            UNION
            SELECT user_name AS userName, user_group AS userGroup, user_type AS userType, date_login AS LogDate,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'SUPPLIER'
            GROUP BY userName ,userGroup, userType, LogDate
            ORDER BY LogDate DESC");
         
        return $results;
        
    }    
        
    protected function getPTJHourlybyChart($carbonToday){ 
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_group, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login
            ORDER BY time_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart(){ 
        
        $dateNow = Carbon::now()->toDateString();  
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_name,user_group, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$dateNow'
            GROUP BY user_group, time_login, user_name
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart5($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '5'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart2($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '2'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart7($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '7'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart10($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '10'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart13($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '13'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart4($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '4'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart11($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '11'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart1($carbonToday){
                
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '1'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getUserTypePTJHourlybyChart6($carbonToday){
                
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type, time_login, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'PTJ' 
            AND DATE(date_login) = '$carbonToday'
            AND user_type = '6'
            AND time_login IS NOT NULL
            GROUP BY user_group, time_login,user_type
            ORDER BY time_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart5($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '5'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart2($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '2'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart7($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '7'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart10($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '10'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart13($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '13'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart4($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '4'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart11($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '11'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart1($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '1'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getUserTypePTJDailybyChart6($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_type AS userType, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND user_type = '6'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userType, date_login 
            ORDER BY date_login ASC");
        return $results;
        
    }
    
    protected function getSupplierHourlybyChart($carbonToday){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT user_group, time_login as timeLog, SUM(login_total) AS totalLog
            FROM ep_login_statistic 
            WHERE user_group = 'SUPPLIER' 
            AND DATE(date_login) = '$carbonToday'
                AND time_login IS NOT NULL
            GROUP BY user_group, timeLog
            ORDER BY time_login ASC");
        
        return $results;
        
    }
    
    protected function getSPTJMOnthlybyChart($currYear){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT MONTH(date_login) seq_month, YEAR(date_login) AS YEAR, DATE_FORMAT(date_login,'%b') AS MONTH, user_group AS GROUP_TYPE, SUM(login_total) AS TOTAL_LOGIN 
                FROM ep_login_statistic 
                WHERE 
                time_login IS NULL 
                AND YEAR(date_login) = '$currYear' 
                AND user_type IN (5,15)
                AND user_group = 'PTJ'
                GROUP BY   MONTH(date_login),YEAR(date_login) , DATE_FORMAT(date_login,'%b') ,user_group  
                ORDER BY  MONTH(date_login)  ASC");
        
        return $results;
        
    }
    
    protected function getSupplierMOnthlybyChart($currYear){
        
        $results = DB::connection('mysql_ep_support')->select("SELECT MONTH(date_login) seq_month, YEAR(date_login) AS YEAR, DATE_FORMAT(date_login,'%b') AS MONTH, user_group AS GROUP_TYPE, SUM(login_total) AS TOTAL_LOGIN 
                FROM ep_login_statistic 
                WHERE 
                time_login IS NULL 
                AND YEAR(date_login) = '$currYear' 
                AND user_type IN (5,15)
                AND user_group = 'SUPPLIER'
                GROUP BY   MONTH(date_login),YEAR(date_login) , DATE_FORMAT(date_login,'%b') ,user_group  
                ORDER BY  MONTH(date_login)  ASC");
        
        return $results;
        
    }
    
    protected function getPTJDailybylineChart($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select(
                 
            "SELECT user_group AS userGroup, date_login,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'PTJ'
            AND time_login IS NULL
            AND date_login like '$CurrMonth'
            GROUP BY userGroup, date_login
            ORDER BY date_login ASC");
        
        return $results;
        
        
    }
    
    protected function getSupplierDailybylineChart($CurrMonth){
        
        $results = DB::connection('mysql_ep_support')->select(
                 
            "SELECT user_group AS userGroup, DATE_FORMAT(date_login,'%d') AS LogDate,
            SUM(login_total) AS totalPtjLogin
            FROM ep_login_statistic
            WHERE user_group = 'SUPPLIER'
            AND time_login IS NULL
            AND date_login like'$CurrMonth'
            GROUP BY userGroup, LogDate");
         
        return $results;
        
        
    }
    
    protected function getDetailsDownload($CurrYear) {

        $results = DB::connection('mysql_ep_support')->select(
                "SELECT month(date_login) seq_month, year(date_login) as YEAR, date_format(date_login,'%b') as MONTH,user_name as ORG_TYPE, SUM(login_total) AS TOTAL_LOGIN 
FROM ep_login_statistic 
WHERE time_login is null 
and YEAR(date_login) = '$CurrYear'
AND user_type in (1,2,4,5,6,7,10,11,13,15)
GROUP BY   month(date_login), year(date_login) , date_format(date_login,'%b')  ,user_name
order by  month(date_login)  asc");

        return $results;
        
     
    }

}
