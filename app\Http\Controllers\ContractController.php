<?php

namespace App\Http\Controllers;

use App\Services\Traits\ContractService;
use Illuminate\Support\Collection;
use Carbon\Carbon;
use Excel;

class ContractController extends Controller
{

    use ContractService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function findContract()
    {
        $carian = request()->cari;
        $carianTemp = trim($carian);

        $list = array();
        $listEkontrak = array();
        $listContractAgreementStatus =  array();
        $contractObj = null;
        if ($carianTemp != null) {
            $list = $this->getListContract($carianTemp);

            $cariEkontrak = $carianTemp;
            if (count($list) > 0) {
                $contract = $list[0];
                $contractObj = $this->getContractOverview($list[0]->contract_id);
                $cariEkontrak = $contract->contract_physical_no;
            }

            $listContractAgreementStatus = $this->getListContractAgreementStatus($list[0]->contract_id);
            $listEkontrak = $this->getListContractFromEkontrak($cariEkontrak);
        }
        //dump($contract);
        return view('list_contract', [
            'listdata' => $list,
            'listAgreement' => $listContractAgreementStatus,
            'listdataEkontrak' => $listEkontrak,
            'contract'  => $contractObj,
            'carian' => $carianTemp
        ]);
    }

    public function eKontrakDetail($contractId)
    {
        $contractDetail = $this->getEkontrakDetails($contractId);
        //        dd($contractDetail);
        if ($contractDetail) {

            $returnHTML = null;
            $bond = null;

            $itemList = $this->getEkontrakItem($contractId);

            if ($contractDetail->template === 'TEMPLATE_A') {
                $agency = $this->getEkontrakAgency($contractId);

                if ($contractDetail->is_bond_req === 'Yes (Ya)') {
                    $bond = $this->getEkontrakBond($contractId);
                }

                $returnHTML = view('include.ajaxView_ekontrak')
                    ->with('contract', $contractDetail)
                    ->with('agency', $agency)
                    ->with('item', $itemList)
                    ->with('bond', $bond)
                    ->render();
            } else {

                $returnHTML = view('include.ajaxView_ekontrak')
                    ->with('contract', $contractDetail)
                    ->with('item', $itemList)
                    ->render();
            }

            return response()->json(array('success' => true, 'html' => $returnHTML));
        }
        return null;
    }


    public function searchCTCommitteMembersByContractNo()
    {
        $carian = request()->cari;
        $carianTemp = trim(strtoupper($carian));
        $list = array();
        if ($carianTemp != null) {

            $listLatestVer = $this->getListAllCommitteeMembersCTByLatestVersion($carian);
            $listCurrentVer = $this->getListAllCommitteeMembersCTByCurrentVersion($carian);
            $listAll = array_merge($listLatestVer, $listCurrentVer);
            $list =  array_unique($listAll, SORT_REGULAR);
        }
        return view('list_ct_committees', [
            'listdata' => $list,
            'carian' => $carianTemp
        ]);
    }

    public function contractItems($contractNo, $contractVersion)
    {

        $listContractItem = $this->getContractItems($contractNo, $contractVersion);

        if (count($listContractItem) > 0) {
            return view('list_contract_item', [
                'listcontractitem' => $listContractItem,
                'contractno' => $contractNo,
                'version' => $contractVersion
            ]);
        } else {
            return view('list_contract_item', [
                'listcontractitem' => '',
                'contractno' => $contractNo,
                'version' => $contractVersion
            ]);
        }
    }

    public function contractItemsLatestVersion($contractNo)
    {

        $listContractItem = $this->getContractItemsLatestVersion($contractNo);

        if (count($listContractItem) > 0) {
            return view('list_contract_item', [
                'listcontractitem' => $listContractItem,
                'contractno' => $contractNo,
                'version' => ''
            ]);
        }
    }

    // contract page

    public function contractInfoPage()
    {
        $ver = null;
        $docNo = request()->doc_no;
        $getContract = $this->getContractDetails($docNo);
        if (isset($getContract) && $getContract != null) {
            if($getContract->latest_contract_ver_id != null){
                $ver = $getContract->latest_contract_ver_id;
            } else {
                $ver = $getContract->current_contract_ver_id;
            }
            
        }

        return $this->contractInfo($docNo, $ver);
    }

    public function contractVer($ver)
    {
        $docNo = $this->getContractVer($ver);
        return $this->contractInfo($docNo[0]->contract_no, $ver);
    }

    public function agencyId($agency_id, $ver)
    {
        $getAgencyAddress = $this->getListAgencyAddress($agency_id, $ver);
        $html = "<table class='table table-bordered table-vcenter'><thead>
                            <tr>
                            <th class='text-center'>Address Id</th>
                            <th class='text-center'>Address</th>
                        </tr>
                    </thead>";
        $html = $html . "<tbody>";
        $counter = 0;
        foreach ($getAgencyAddress as $value) {
            $data = "
                    <tr>
                        <td class='text-center'><strong>$value->address_id</strong></td> 
                        <td class='text-center'><strong>$value->address_name, $value->address_1, $value->address_2, $value->address_3, $value->district_name, $value->postcode $value->city_name, $value->state_name</strong></td>
                    </tr>";
            $html = $html . $data;
        }
        $html = $html . "<tbody></table>";
        return $html;
    }

    public function contractInfo($docNo, $ver)
    {

        $currentDate = Carbon::now();
        $date = $currentDate->format("d-m-Y");
        $contractDetails = $contractDetailsChanges = $getContractAgencies = $getContract = $getVerId = $agencyNotSame = $itemNotSame = $itemZoneNotSame = $getContractDeduction = $getFulfilmentStatus = null;
        $getContractAgenciesCurrentVer = $DeductNotSame = $getContractBond = $BondNotSame = $getContractNotification = $getAgreementStatus = $getKodBidangQt = $getContractItemZoneCurrentVer = null;
        $NotiNotSame = $getContractAdmin = $getContractApprover = $getContractApproverSelected = $getContractApproverSelectedChanges = $getContractItem = $getItemZone = $getAmendmentWithSupp = $getFactoringList = $getContractItemCurrentVer = null;
        $getAmendmentWithSuppCurrentVer = $getUnitPriceCurrentVer = $unitPriceNotSame = $getSchedule = $getSupplierDetails = $getContractVer = $getContractTerminate = null;

        $getContract = $this->getContractDetails($docNo);
        $getContractAdmin = $this->getListContractAdmin($docNo);
        $getContractApprover = $this->getListContractApprover($docNo);
        $getContractApproverSelected = $this->getListContractApproverSelected($ver);
        $getAgreementStatus = $this->getAgreement($docNo);
        $getContractTerminate = $this->getContractTerminateion($docNo);
        $getContractVer = $this->getListContractVer($docNo, $ver);


        if ($getContract && is_object($getContract)) {
            if($getContract->latest_contract_ver_id != null){
                $getSupplierDetails = $this->getSupplierList($ver, $docNo);
                $contractDetails = $this->getDetailContract($ver, $ver, $getContract->contract_no);
                $getSchedule = $this->getListSchedule($ver, $getContract->contract_no, $ver, $ver, $getContract->contract_no);
            }
            
            $getContractAgencies = $this->getListAgencies($ver);
            if ($getContract->supplier_id != 37074) {
                $getContractItem = $this->getListItem($ver);
                $getItemZone = $this->getListItemZone($ver);
            }
            $getUnitPrice = $this->getUnitPriceItem($ver);
            $getContractDeduction = $this->getListDeduct($ver);
            $getContractBond = $this->getListBond($ver, $ver);
            $getContractNotification = $this->getListNotification($ver);
            $getAmendmentWithSupp = $this->getListAmendmentWithSupp($ver);
            $getFactoringList = $this->getListFactoring($getContract->contract_id);
            $getFulfilmentStatus = $this->getFulfilmentStatus($getContract->current_contract_ver_id);
            $getKodBidangQt = $this->getQtKodBidang($getContract->qt_id);

            if ($getContract->current_contract_ver_id != $ver) {
                $getVerId = $this->getListVerContract($getContract->contract_id);
                $contractDetailsChanges = $this->getDetailContract($getVerId[0]->contract_ver_id, $getVerId[0]->contract_ver_id, $getContract->contract_no);
                $getContractApproverSelectedChanges = $this->getListContractApproverSelected($getVerId[0]->contract_ver_id);
                $getScheduleChanges = $this->getListSchedule($getVerId[0]->contract_ver_id, $getContract->contract_no, $getVerId[0]->contract_ver_id, $getVerId[0]->contract_ver_id, $getContract->contract_no);
                $getAmendmentWithSuppCurrentVer = $this->getListAmendmentWithSupp($getVerId[0]->contract_ver_id);
                $getContractAgenciesCurrentVer = $this->getListAgencies($getVerId[0]->contract_ver_id);
                $collectAgency1 = new Collection($getContractAgencies);
                $collectAgency2 = new Collection($getContractAgenciesCurrentVer);
                $attributesToCompareAgency = ['agency_id','ceiling_amount','eff_date', 'exp_date', 'record_status', 'org_code', 'ptj', 'kumpulan_ptj', 'kementerian'];
                $differencesAgency = collect();
                foreach ($collectAgency2 as $item2) {
                    $matched = false;

                    foreach ($collectAgency1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareAgency as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesAgency->push($item2);
                    }
                }

                if (!$differencesAgency->isEmpty()) {
                    $agencyNotSame = $differencesAgency;
                }
                if ($getContract->supplier_id != 37074) {

                    $getContractItemCurrentVer = $this->getListItem($getVerId[0]->contract_ver_id);
                }
                $collectItem1 = new Collection($getContractItem);
                $collectItem2 = new Collection($getContractItemCurrentVer);
                $attributesToCompareItem = ['extension_code', 'item_name', 'eff_date', 'exp_date', 'status', 'jenis_item', 'jenis_barang', 'max_quantity', 'minimum_order_qty'];
                $differencesItem = collect();
                foreach ($collectItem2 as $item2) {
                    $matched = false;

                    foreach ($collectItem1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareItem as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesItem->push($item2);
                    }
                }

                if (!$differencesItem->isEmpty()) {
                    $itemNotSame = $differencesItem;
                }

                if ($getContract->supplier_id != 37074) {
                    $getContractItemZoneCurrentVer = $this->getListItemZone($getVerId[0]->contract_ver_id);
                }
                $collectItemZone1 = new Collection($getItemZone);
                $collectItemZone2 = new Collection($getContractItemZoneCurrentVer);
                $attributesToCompareItemZone = ['zone_name', 'state_name', 'city_name', 'division_name', 'district_name', 'extension_code', 'item_name', 'eff_date', 'exp_date', 'status', 'jenis_item', 'jenis_barang', 'max_quantity', 'minimum_order_qty'];
                $differencesItemZone = collect();
                foreach ($collectItemZone2 as $item2) {
                    $matched = false;

                    foreach ($collectItemZone1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareItemZone as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesItemZone->push($item2);
                    }
                }

                if (!$differencesItemZone->isEmpty()) {
                    $itemZoneNotSame = $differencesItemZone;
                }
                $getUnitPriceCurrentVer = $this->getUnitPriceItem($getVerId[0]->contract_ver_id);
                $collectUnitPrice1 = new Collection($getUnitPrice);
                $collectUnitPrice2 = new Collection($getUnitPriceCurrentVer);
                $attributesToCompareUnitPrice = ['unit_price', 'extension_code', 'item_name', 'eff_date', 'exp_date', 'status', 'jenis_item', 'jenis_barang', 'minimum_order_qty'];
                $differencesUnitPrice = collect();
                foreach ($collectUnitPrice2 as $item2) {
                    $matched = false;

                    foreach ($collectUnitPrice1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareUnitPrice as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesUnitPrice->push($item2);
                    }
                }

                if (!$differencesUnitPrice->isEmpty()) {
                    $unitPriceNotSame = $differencesUnitPrice;
                }

                $getContractDeductionCurrentVer = $this->getListDeduct($getVerId[0]->contract_ver_id);
                $collectDeduct1 = new Collection($getContractDeduction);
                $collectDeduct2 = new Collection($getContractDeductionCurrentVer);
                $attributesToCompareDeduct = ['code_name', 'deduction_desc', 'clause_description'];
                $differencesDeduct = collect();
                foreach ($collectDeduct2 as $item2) {
                    $matched = false;

                    foreach ($collectDeduct1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareDeduct as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesDeduct->push($item2);
                    }
                }

                if (!$differencesDeduct->isEmpty()) {
                    $DeductNotSame = $differencesDeduct;
                }

                $getContractBondCurrentVer = $this->getListBond($getVerId[0]->contract_ver_id, $getVerId[0]->contract_ver_id);
                $collectBond1 = new Collection($getContractBond);
                $collectBond2 = new Collection($getContractBondCurrentVer);
                $attributesToCompareBond = ['ref_no', 'financial_org_name', 'eff_date', 'exp_date', 'is_bond_waived'];
                $differencesBond = collect();
                foreach ($collectBond2 as $item2) {
                    $matched = false;

                    foreach ($collectBond1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareBond as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesBond->push($item2);
                    }
                }

                if (!$differencesBond->isEmpty()) {
                    $BondNotSame = $differencesBond;
                }

                $getContractNotificationCurrentVer = $this->getListNotification($getVerId[0]->contract_ver_id);
                $collectNoti1 = new Collection($getContractNotification);
                $collectNoti2 = new Collection($getContractNotificationCurrentVer);
                $attributesToCompareNoti = ['ct_notify_days', 'ag_notify_days_b4_ag', 'bond_notify_days', 'termination_notify_days', 'supplier_do_days', 'approver_mto_days', 'last_co_b4_exp_days'];
                $differencesItem = collect();
                foreach ($collectNoti2 as $item2) {
                    $matched = false;

                    foreach ($collectNoti1 as $item1) {
                        $isMatch = true;

                        foreach ($attributesToCompareNoti as $attribute) {
                            if (strtolower($item1->$attribute) !== strtolower($item2->$attribute)) {
                                $isMatch = false;
                                break;
                            }
                        }

                        if ($isMatch) {
                            $matched = true;
                            break;
                        }
                    }

                    if (!$matched) {
                        $differencesItem->push($item2);
                    }
                }

                if (!$differencesItem->isEmpty()) {
                    $NotiNotSame = $differencesItem;
                }
            }
        }




        return view('list_ct_summary', [
            'contractDetails' => $contractDetails,
            'contractDetailsChanges' => $contractDetailsChanges,
            'getSchedule' => $getSchedule,
            'getContractAgencies' => $getContractAgencies,
            'getVerId' => $getVerId,
            'agencyNotSame' => $agencyNotSame,
            'getContractItem' => $getContractItem,
            'itemNotSame' => $itemNotSame,
            'getItemZone' => $getItemZone,
            'itemZoneNotSame' => $itemZoneNotSame,
            'unitPriceNotSame' => $unitPriceNotSame,
            'getContractDeduction' => $getContractDeduction,
            'DeductNotSame' => $DeductNotSame,
            'getContractBond' => $getContractBond,
            'BondNotSame' => $BondNotSame,
            'getContractNotification' => $getContractNotification,
            'NotiNotSame' => $NotiNotSame,
            'getContractAdmin' => $getContractAdmin,
            'getContractApprover' => $getContractApprover,
            'getContractApproverSelected' => $getContractApproverSelected,
            'getContractApproverSelectedChanges' => $getContractApproverSelectedChanges,
            'getAmendmentWithSupp' => $getAmendmentWithSupp,
            'getAmendmentWithSuppCurrentVer' => $getAmendmentWithSuppCurrentVer,
            'getFactoringList' => $getFactoringList,
            'getSupplierDetails' => $getSupplierDetails,
            'getAgreementStatus' => $getAgreementStatus,
            'getFulfilmentStatus' => $getFulfilmentStatus,
            'getContractVer' => $getContractVer,
            'getKodBidangQt' => $getKodBidangQt,
            'getContractTerminate' => $getContractTerminate,
            'docno' => $docNo,
            'date' => $date,
            'ver' => $ver,
            'carian' => request()->doc_no
        ]);
    }

    public function downloadItem($ver,$docNo){
        $listdata = $this->getListItem($ver);
        if ($listdata !== null) {
            $collectlistReporting = collect($listdata)->groupBy('1');
            ini_set('memory_limit', '-1');
            ini_set('max_execution_time', 300); //3 minutes
            $fileName = 'Contract '.$docNo;

            Excel::create($fileName, function($excel)use($collectlistReporting) {
                $excel->setTitle('Contract');

                $excel->sheet('Contract', function($sheet) use ($collectlistReporting) {
                    $sheet->setOrientation('landscape');
                    foreach ($collectlistReporting as $list) {
                        $sheet->setStyle(array(
                            'font' => array(
                                'name' => 'Calibri',
                                'size' => 9,
                                'bold' => false
                            )
                        ));

                        $sheet->setColumnFormat(array(
                            'A' => '@',
                            'B' => '@',
                            'C' => '@',
                            'D' => '@',
                            'E' => '@',
                            'F' => '@',
                            'G' => '@',
                        ));

                        $sheet->row(1, array(
                            'Request_Item_Id', 'Item_Name', 'Eff_date', 'Exp_Date', 'Max_qty', 'Order_qty', 'Balance_avl_qty'
                        ));

                        $sheet->row(1, function($row) {
                            // call cell manipulation methods
                            $row->setBackground('#D9D9D9');
                        });

                        $sheet->setAutoSize(true);

                        $sheet->cells('A1:G1', function($cells) {
                            // manipulate the range of cells
                            // Set with font color
                            $cells->setFontColor('##070606');

                            // Set font family
                            $cells->setFontFamily('Calibri');

                            // Set font size
                            $cells->setFontSize(9);

                            // Set font weight to bold
                            $cells->setFontWeight('bold');

                            // Set all borders (top, right, bottom, left)
                            $cells->setBorder('solid', 'solid', 'solid', 'solid');
                        });

                        $count = 2;
                        foreach ($list as $obj) {
                            $sheet->row($count, array(
                                $obj->request_item_id,
                                $obj->item_name,
                                $obj->eff_date,
                                $obj->exp_date,
                                $obj->max_quantity,
                                $obj->qty,
                                $obj->balance,
                                    )
                            );
                            $count++;
                        }
                    }
                });
            })->store('xlsx', storage_path('app/Report/prod_support/'));

            $headers = [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'max-age=0',
            ];

            $fullPath = storage_path('app/Report/prod_support/' . $fileName . '.xlsx');
            return response()->download($fullPath, $fileName . '.xlsx', $headers);
        }
    }
}
