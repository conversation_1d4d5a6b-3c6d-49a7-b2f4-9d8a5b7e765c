<?php

namespace App\Console\Commands\ItSupport;

use Illuminate\Console\Command;
use App\Services\Traits\ItSupport\ItSupportService;
use Carbon\Carbon;
use Log;
use DB;
use Config;
use App\Migrate\MigrateUtils;

class HandleInsertNewTaskEverDay extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ItS-insert-new-task {date?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will insert new task every day';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $date = $this->argument('date') ?? Carbon::now()->toDateString();
        $this->processTasks($date);
    }

    public function processTasks($date)
    {
        MigrateUtils::logDump(self::class . ' starting ..', [
            'Date' => Carbon::now()
        ]);
        $listdata = $this->listActiveTask();
        MigrateUtils::logDump(' Total task to be insert. ' . count($listdata));
        try {
            $listdata = $this->listActiveTask();
            $checkDataExist = $this->listDataExist();
            if($checkDataExist == null){
                $seq_ack = 1;
            }
            else {
                $seq_ack = $checkDataExist[0]->ack_task_no + 1;
            }

            if ($listdata) {
                $hour = 20;

                foreach ($listdata as $list) {
                    $count_checking = $list->total_hour_checking;
                    $start_time = 8;
                    $interval = 0;
                    $morning_shift_start = 8;
                    $morning_shift_end = 19;
                    $evening_shift_start = 20;
                    $evening_shift_end = 7;

                    for ($i = 0; $i < $count_checking; $i++) {
                        $interval_count = 12 / $count_checking;
                        if ($i == 0) {
                            $interval = 0;
                        } else {
                            $interval = 12 / $count_checking;
                        }
                        $start_time = $start_time + $interval;

                        if ($start_time > 12) {
                            $realTime = $start_time - 12;
                        } else {
                            $realTime = $start_time;
                        }
                        $seq = $i + 1;

                        if ($morning_shift_start >= 8 && $morning_shift_end < 20) {
                            $shift = 'M';
                            if ($start_time < 12) {
                                $time = 'AM';
                            } else {
                                $time = 'PM';
                            }

                            if ($list->esc_group == 'DRC') {
                                $realTime = 11;
                            }

                            DB::connection('mysql_ep_it_support')->table('esc_job_task')->insert([
                                [
                                    'task_date' => $date,
                                    'task_time' => $realTime,
                                    'time_format' => $time,
                                    'shift_interval' => $shift,
                                    'esc_id' => $list->esc_id,
                                    'time_interval' => $interval_count,
                                    'task_name' => $list->esc_name,
                                    'task_seq' => $seq,
                                    'ack_task_no' => $seq_ack,
                                    'task_created_by' => 'AutoSystem',
                                    'task_created_date' => Carbon::now(),
                                ],
                            ]);
                        }
                        if ($evening_shift_start >= 20 && $evening_shift_end < 8) {
                            $shift = 'E';
                            if ($start_time < 12) {
                                $time = 'PM';
                            } else {
                                $time = 'AM';
                            }
                            if ($list->esc_group != 'DRC') {
                                DB::connection('mysql_ep_it_support')->table('esc_job_task')->insert([
                                    [
                                        'task_date' => $date,
                                        'task_time' => $realTime,
                                        'time_format' => $time,
                                        'shift_interval' => $shift,
                                        'esc_id' => $list->esc_id,
                                        'time_interval' => $interval_count,
                                        'task_name' => $list->esc_name,
                                        'task_seq' => $seq,
                                        'ack_task_no' => $seq_ack,
                                        'task_created_by' => 'AutoSystem',
                                        'task_created_date' => Carbon::now(),
                                    ],
                                ]);
                            }
                        }
                    }
                }
            }
        } catch (\Exception $exc) {
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
        MigrateUtils::logDump(' Done! Completed');
    }

    protected function listActiveTask()
    {
        $query = "select * from ep_it_support.ep_status_checklist where esc_status = 'active' ";
        $result = DB::connection('mysql_ep_prod_support')->select($query);
        return $result;
    }

    protected function listDataExist() {
        $query = "select ack_task_no from ep_it_support.esc_job_task order by ack_task_no desc";
        $result = DB::connection('mysql_ep_prod_support')->select($query);
        return $result;
    }
}
