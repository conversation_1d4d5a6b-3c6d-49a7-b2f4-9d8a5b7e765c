<?php
/**
 * Created by PhpStorm.
 * User: iqbalfikri
 * Date: 3/14/2018
 * Time: 11:56 AM
 */

namespace App\Services\Traits;

use Log;
use DB;

trait OrganizationService
{

    /**
     * Get Query for   Users Info. Not include roles.
     * @param type $icno
     * @return type
     */
    public function getUserList($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU');
        $query->join('PM_USER_ORG as PMUO', 'PMU.USER_ID', '=', 'PMUO.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as PMLH', 'PMU.USER_ID', '=', 'PMLH.USER_ID');
        $query->where('PMU.RECORD_STATUS', 1);
        $query->where('PMUO.RECORD_STATUS', 1);
        if ($type == 'identification_no') {
            $query->where('PMU.IDENTIFICATION_NO', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMUO.ORG_PROFILE_ID', $value);
        }
        $query->select('PMU.USER_ID', 'PMU.LOGIN_ID', 'PMU.USER_NAME AS FULLNAME', 'PMU.NATIONALITY_ID', 'PMU.IDENTIFICATION_TYPE_ID');
        $query->addSelect('PMUO.ORG_PROFILE_ID','PMU.ORG_TYPE_ID', 'PMU.IDENTIFICATION_NO', 'PMU.DESIGNATION', 'PMU.EMAIL', 'PMU.RECORD_STATUS AS PMU_RECORD_STATUS');
        $query->addSelect('PMU.CREATED_DATE', 'PMU.CHANGED_DATE', 'PMU.MOBILE_COUNTRY', 'PMU.MOBILE_AREA', 'PMU.MOBILE_NO');
        $query->addSelect('PMU.PHONE_COUNTRY', 'PMU.PHONE_AREA', 'PMU.PHONE_NO', 'PMU.FAX_COUNTRY', 'PMU.FAX_AREA', 'PMU.FAX_NO', 'PMU.SALUTATION_ID');
        $query->addSelect('PMUO.CREATED_DATE as USERORG_CREATED_DATE','PMUO.CHANGED_DATE as USERORG_CHANGED_DATE','PMUO.RECORD_STATUS AS PMUO_RECORD_STATUS');
        $query->addSelect('PMLH.LOGIN_DATE');

        return $query->get();

    }
    

    /**
     * Get Query for  users roles.
     * @param type $userId
     * @return type
     */
    public function getUserRole($userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER as PMU');
        $query->leftJoin('PM_USER_ORG as PMUO', 'PMU.USER_ID', '=', 'PMUO.USER_ID');
        $query->leftJoin('PM_USER_ROLE as PMUR', 'PMUO.USER_ORG_ID', '=', 'PMUR.USER_ORG_ID');
        $query->leftJoin('PM_ROLE_DESC as PMRD', 'PMUR.ROLE_CODE', '=', 'PMRD.ROLE_CODE');
        $query->where('PMUO.RECORD_STATUS', 1);
        $query->where('PMUR.RECORD_STATUS', 1);
        $query->where('PMRD.LANGUAGE_CODE', 'en');
        if ($userId != null) {
            $query->where('PMU.USER_ID', $userId);
        }
        $query->select('PMU.USER_ID', 'PMUR.USER_ROLE_ID', 'PMUR.ROLE_CODE', 'PMRD.ROLE_NAME','PMUR.APPROVAL_LIMIT');

        return $query->get();

    }
    
    /**
     * Get Query for  users roles.
     * @param type $userId
     * @return type
     */
    public function getRoleDesc($roleCode,$language)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ROLE_DESC as PMRD');
        $query->where('PMRD.role_code', $roleCode);
        $query->where('PMRD.LANGUAGE_CODE', $language);
        return $query->first();

    }

    public function getPmOrganization($type,$value){
        $data = $this->getPmOrgValidity($type,$value);
        if(count($data) == 0){
            $data = $this->getPmOrgFactoring($type,$value);
        }
        return $data; 
    }        
   
    /**
     * Get Query for  Oranization Factoring Info.
     * @param type $orgCode
     * @return type
     */
    public function getPmOrgFactoring($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_FINANCIAL_ORG as PMFO');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMFO.FINANCIAL_ORG_ID', '=', 'PMOP.FACTORING_ORG_ID');
        $query->join('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMFO.RECORD_STATUS', 1);
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        if ($type == 'org_code') {
            $query->where('PMFO.BIZ_REG_NO', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMOP.ORG_PROFILE_ID', $value);
        }
        $query->select('PMOP.ORG_PROFILE_ID','PMOP.FACTORING_ORG_ID', 'PMFO.BIZ_REG_NO as ORG_CODE', 'PMFO.FIN_ORG_NAME as ORG_NAME', 'PMFO.RECORD_STATUS AS PMOV_STATUS', 'PMFO.CHANGED_DATE',
                 DB::raw("NULL as EFF_DATE"),DB::raw("NULL as EXP_DATE"));
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for  Oranization Info..
     * @param type $orgCode
     * @return type
     */
    public function getPmOrgValidity($type,$value)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ORG_VALIDITY as PMOV');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMOV.ORG_PROFILE_ID', '=', 'PMOP.ORG_PROFILE_ID');
        $query->join('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        $query->whereRaw('PMOV.EFF_DATE in (SELECT MAX(EFF_DATE) FROM PM_ORG_VALIDITY v where v.ORG_PROFILE_ID = PMOV.ORG_PROFILE_ID ) ');
        // $query->where('PMOV.RECORD_STATUS', 1);
        if ($type == 'org_code') {
            $query->where('PMOV.ORG_CODE', $value);
        }
        if ($type == 'org_profile_id') {
            $query->where('PMOV.ORG_PROFILE_ID', $value);
        }
        $query->select('PMOV.ORG_PROFILE_ID', 'PMOV.ORG_CODE', 'PMOV.ORG_NAME', 'PMOV.EFF_DATE', 'PMOV.EXP_DATE', 'PMOV.RECORD_STATUS AS PMOV_STATUS', 'PMOV.CHANGED_DATE');
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE','PMOP.IS_EP_PTJ');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for  Organization  Info. 
     * @param type $orgProfileId
     * @return type
     */
    public function getPmOrgValidityByParentId($orgProfileId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_ORG_VALIDITY as PMOV');
        $query->join('PM_ORG_PROFILE as PMOP', 'PMOV.ORG_PROFILE_ID', '=', 'PMOP.ORG_PROFILE_ID');
        $query->leftJoin('PM_PARAMETER_DESC as PMPD', 'PMOP.ORG_TYPE_ID', '=', 'PMPD.PARAMETER_ID');
        $query->where('PMPD.LANGUAGE_CODE', 'ms');
        $query->where('PMOV.RECORD_STATUS', 1);
        if ($orgProfileId != null) {
            $query->where('PMOV.ORG_PROFILE_ID', $orgProfileId);
        }
        $query->select('PMOV.ORG_PROFILE_ID', 'PMOV.ORG_CODE', 'PMOV.ORG_NAME', 'PMOV.EFF_DATE', 'PMOV.EXP_DATE', 'PMOV.RECORD_STATUS AS PMOV_STATUS', 'PMOV.CHANGED_DATE');
        $query->addSelect('PMOP.ORG_TYPE_ID', 'PMOP.PARENT_ORG_PROFILE_ID', 'PMOP.RECORD_STATUS AS PMOP_STATUS', 'PMOP.CHANGED_DATE');
        $query->addSelect('PMPD.CODE_DESC');

        return $query->get();
    }

    /**
     * Get Query for   Users Group Info.
     * @param type $orgProfileId
     * @return type
     */
    public function getPmUserGroupByUserId($userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER_GROUP_USER as PMUGU');
        $query->leftJoin('PM_USER_GROUP as PMUG', 'PMUGU.USER_GROUP_ID', '=', 'PMUG.USER_GROUP_ID');
        $query->where('PMUGU.RECORD_STATUS', 1);
        $query->where('PMUG.RECORD_STATUS', 1);
        $query->where('PMUGU.USER_ID', $userId);
        $query->select('PMUG.GROUP_CODE', 'PMUG.GROUP_NAME');

        return $query->get();
    }
    
    /**
     * Get Query for   Users Group Info.
     * @param type $orgProfileId
     * @return type
     */
    public function getPmUserGroupByOrgProfileId($orgProfileId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_USER_GROUP');
        $query->where('org_profile_id', $orgProfileId);
        return $query->get();
    }
    
    protected function getListReceivingAddressByUser($selectType, $orgProfileId, $icno = null) {

        if ($selectType == "COUNT") {
            $query = " SELECT COUNT(*) AS TOTAL ";
        } else if ($selectType == "SELECT") {
            $query = " SELECT  D.USER_NAME, D.IDENTIFICATION_NO, A.ADDRESS_TYPE, 
                A.ADDRESS_ID,  C.ADDRESS_NAME, C.ADDRESS_1, C.ADDRESS_2, C.ADDRESS_3, 
                C.POSTCODE, C.DISTRICT_ID, C.DIVISION_ID, C.CITY_ID, 
                B.CHANGED_DATE, C.PHONE_COUNTRY, C.PHONE_AREA, C.PHONE_NO, A.RECORD_STATUS ";
        } else {
            return null;
        }
        
        $query = $query . " FROM PM_ADDRESS_TYPE A, PM_ORG_VALIDITY B, PM_ADDRESS C, PM_USER D
                    WHERE B.ORG_PROFILE_ID = A.ORG_PROFILE_ID
                    AND A.ADDRESS_ID = C.ADDRESS_ID
                    AND D.USER_ID = A.USER_ID
                    AND A.ADDRESS_TYPE = 'R' 
                    AND D.RECORD_STATUS = 1  
                    AND B.RECORD_STATUS = 1  
                    AND A.RECORD_STATUS = 1
                    AND B.ORG_PROFILE_ID = ? ";
        
        if ($icno != null) {
            $query = $query . " AND D.IDENTIFICATION_NO = ?  ";
            return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($orgProfileId, $icno));
        }

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($orgProfileId));
    }
    
    protected function getListDeliveryAddressByPtj($selectType, $orgProfileId) {
        if ($selectType == "COUNT") {
            $query = " SELECT COUNT(*) AS TOTAL ";
        } else if ($selectType == "SELECT") {
            $query = " SELECT A.ADDRESS_TYPE, 
                A.ADDRESS_ID,  C.ADDRESS_NAME, C.ADDRESS_1, C.ADDRESS_2, C.ADDRESS_3, 
                C.POSTCODE, C.DISTRICT_ID, C.DIVISION_ID, C.CITY_ID, 
                B.CHANGED_DATE, C.PHONE_COUNTRY, C.PHONE_AREA, C.PHONE_NO, A.RECORD_STATUS ";
        } else {
            return null;
        }
        
        $query = $query . " FROM PM_ADDRESS_TYPE A, PM_ORG_VALIDITY B, PM_ADDRESS C 
                    WHERE B.ORG_PROFILE_ID = A.ORG_PROFILE_ID
                    AND A.ADDRESS_ID = C.ADDRESS_ID
                    AND A.ADDRESS_TYPE = 'D' 
                    AND B.RECORD_STATUS = 1  
                    AND A.RECORD_STATUS = 1 
                    AND B.ORG_PROFILE_ID = ? ";
        

        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($orgProfileId));
    }
    
    protected function getPendingUserCreation () {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PENDING_LIFERAY as PML');
        $query->join('PM_USER as PMU', 'PMU.USER_ID', '=', 'PML.USER_ID');
        $query->where('PML.record_status', 8);
        $query->where('PML.attempt', '<', 3);
        $query->select('PML.pending_liferay_id','PML.user_id','PML.attempt','PML.record_status','PML.created_date','PML.changed_date','PML.err_msg');
        $query->addSelect('PMU.user_id','PMU.login_id','PMU.user_name','PMU.identification_no','PMU.designation','PMU.email','PMU.record_status as user_record_status');

        return $query->get();
    }
    
    protected function getStuckUserCreation () {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PENDING_LIFERAY as PML');
        $query->join('PM_USER as PMU', 'PMU.USER_ID', '=', 'PML.USER_ID');
        $query->where('PML.record_status', 8);
        $query->where('PML.attempt', '>=', 3);
        $query->select('PML.pending_liferay_id','PML.user_id','PML.attempt','PML.record_status','PML.created_date','PML.changed_date','PML.err_msg');
        $query->addSelect('PMU.user_id','PMU.login_id','PMU.user_name','PMU.identification_no','PMU.designation','PMU.email','PMU.record_status as user_record_status');

        return $query->get();
    }
    
    protected function getPendingProcessPayment () {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PENDING_PROCESS as SMP');
        $query->join('SM_APPL as APPL', 'APPL.appl_id', '=', 'SMP.appl_id');
        $query->join('SM_SUPPLIER as SUPP', 'SUPP.supplier_id', '=', 'APPL.supplier_id');
        $query->join('PY_PAYMENT as PYMT', 'SMP.payment_id', '=', 'PYMT.payment_id');
        $query->where('SMP.record_status', 8);
        $query->where('SMP.attempt', '<', 3);
        $query->select('SMP.pending_process_id','SMP.payment_id','SMP.appl_id','SMP.attempt','SMP.record_status','SMP.created_date','SMP.changed_date','SMP.err_msg');
        $query->addSelect('SUPP.supplier_id','SUPP.company_name','SUPP.reg_no','SUPP.ep_no','SUPP.record_status AS supplier_record_status','APPL.appl_no','SUPP.company_name');
        $query->addSelect('PYMT.CHANGED_DATE as py_changed_date','PYMT.PAYMENT_DATE','PYMT.PAYMENT_AMT');
        
        return $query->get();
    }

    protected function getSummaryOfPendingAndInProgressRemoveTasks()
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PENDING_REMOVE_TASK as PMT');

        $query->select(DB::raw("decode(record_status, 8, 'Pending Process Remove Task', '1', 'In Progress Remove Task') AS status"), DB::raw('count(*) AS total'))
            ->whereIn('record_status', [1, 8])
            ->groupBy('record_status');

        return $query->get();
    }

    protected function getDetailedPendingRemoveTasks($recordStatus)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PENDING_REMOVE_TASK as PMT');
    
        $query->select('pm_pending_remove_task_id', 'param_task', 'record_status', 'module', 'user_id', 'created_date', 'changed_date', 'total_task_deleted', DB::raw('EXTRACT(DAY FROM (sysdate - changed_date)) AS ageing_day'))
            ->where('record_status', $recordStatus)
            ->orderBy('created_date', 'asc');
    
        return $query->get();
    }    
    
    protected function getStuckProcessPayment () {
        
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PENDING_PROCESS as SMP');
        $query->join('SM_APPL as APPL', 'APPL.appl_id', '=', 'SMP.appl_id');
        $query->join('SM_SUPPLIER as SUPP', 'SUPP.supplier_id', '=', 'APPL.supplier_id');
        $query->join('PY_PAYMENT as PYMT', 'SMP.payment_id', '=', 'PYMT.payment_id');
        $query->where('SMP.record_status', 8);
        $query->where('SMP.attempt', '>=', 3);
        $query->select('SMP.pending_process_id','SMP.payment_id','SMP.appl_id','SMP.attempt','SMP.record_status','SMP.created_date','SMP.changed_date','SMP.err_msg');
        $query->addSelect('SUPP.supplier_id','SUPP.company_name','SUPP.reg_no','SUPP.ep_no','SUPP.record_status AS supplier_record_status','APPL.appl_no','SUPP.company_name');
        $query->addSelect('PYMT.CHANGED_DATE as py_changed_date','PYMT.PAYMENT_DATE','PYMT.PAYMENT_AMT');
   
        return $query->get();
    }
    
    protected function getListNotSendMoreSevenDaysNotifyEmail($selectType) {

        if ($selectType != null && $selectType == "COUNT") {
            $query = " SELECT count(u.NOTIFY_USER_ID) AS TOTAL ";
        } else if ($selectType != null && $selectType == "SELECT") {
            $query = " SELECT *  ";
        } else {
            return null;
        }
        
        $query = $query . " FROM pm_notify_user u,
                            pm_notify_email e,
                            pm_notification n,
                            pm_user p
                          WHERE trunc(u.created_date) between trunc(sysdate-30) and trunc(sysdate-1-7)
                          and u.notify_email_id  = e.notify_email_id
                          AND u.notification_id    = n.notification_id
                          AND u.recipient_id       = p.user_id
                          AND u.email             IS NOT NULL
                          AND u.record_status      = 1
                          AND u.uuid              IS NOT NULL
                          AND u.batch_no          IS NULL
                          AND u.is_email_success  = 0
                          AND u.email_retry_count = 0
                          AND u.record_status      = 1
                          AND n.is_email_req       = 1
                          AND n.record_status      = 1
                          AND p.is_notify_by_email = 1
                          AND p.record_status      = 1  ";
        

        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }
    
    protected function getListPendingWithinSevenDaysNotifyEmail($selectType) {

        if ($selectType != null && $selectType == "COUNT") {
            $query = " SELECT count(u.NOTIFY_USER_ID) AS TOTAL ";
        } else if ($selectType != null && $selectType == "SELECT") {
            $query = " SELECT *  ";
        } else {
            return null;
        }
        
        $query = $query . " FROM pm_notify_user u,
                            pm_notify_email e,
                            pm_notification n,
                            pm_user p
                          WHERE u.created_date between trunc(sysdate-7) and sysdate 
                          and u.notify_email_id  = e.notify_email_id
                          AND u.notification_id    = n.notification_id
                          AND u.recipient_id       = p.user_id
                          AND u.email             IS NOT NULL
                          AND u.record_status      = 1
                          AND u.uuid              IS NOT NULL
                          AND u.batch_no          IS NULL
                          AND u.is_email_success  = 0
                          AND u.email_retry_count = 0
                          AND u.record_status      = 1
                          AND n.is_email_req       = 1
                          AND n.record_status      = 1
                          AND p.is_notify_by_email = 1
                          AND p.record_status      = 1 ";
        

        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }

    protected function getListNotSendMoreSevenDaysBulkNotifyEmail($selectType) {

        if ($selectType != null && $selectType == "COUNT") {
            $query = " SELECT count(u.NOTIFY_USER_ID) AS TOTAL ";
        } else if ($selectType != null && $selectType == "SELECT") {
            $query = " SELECT *  ";
        } else {
            return null;
        }
        
        $query = $query . " FROM pm_notify_user u,
                            pm_notify_email e,
                            pm_notification n,
                            pm_user p
                          WHERE trunc(u.created_date) between trunc(sysdate-30) and trunc(sysdate-1-7)
                          and u.notify_email_id  = e.notify_email_id
                          AND u.notification_id    = n.notification_id
                          AND u.recipient_id       = p.user_id
                          AND u.is_email_success  = 0
                          and u.email_retry_count  IS NULL
                          AND u.uuid              IS NULL
                          AND u.batch_no          IS NOT NULL
                          AND u.email             IS NOT NULL
                          AND u.record_status      = 1
                          AND u.record_status      = 1
                          AND n.is_email_req       = 1
                          AND n.record_status      = 1
                          AND p.is_notify_by_email = 1
                          AND p.record_status      = 1 ";
        

        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }
    
    protected function getListPendingWithinSevenDaysBulkNotifyEmail($selectType) {

        if ($selectType != null && $selectType == "COUNT") {
            $query = " SELECT count(u.NOTIFY_USER_ID) AS TOTAL ";
        } else if ($selectType != null && $selectType == "SELECT") {
            $query = " SELECT *  ";
        } else {
            return null;
        }
        
        $query = $query . " FROM pm_notify_user u,
                            pm_notify_email e,
                            pm_notification n,
                            pm_user p
                          WHERE u.created_date between trunc(sysdate-7) and sysdate 
                          and u.notify_email_id  = e.notify_email_id
                          AND u.notification_id    = n.notification_id
                          AND u.recipient_id       = p.user_id
                          AND u.is_email_success  = 0
                          and u.email_retry_count  IS NULL
                          AND u.uuid              IS NULL
                          AND u.batch_no          IS NOT NULL
                          AND u.email             IS NOT NULL
                          AND u.record_status      = 1
                          AND u.record_status      = 1
                          AND n.is_email_req       = 1
                          AND n.record_status      = 1
                          AND p.is_notify_by_email = 1
                          AND p.record_status      = 1 ";
        

        return $results = DB::connection('oracle_nextgen_rpt')->select($query);
    }
}