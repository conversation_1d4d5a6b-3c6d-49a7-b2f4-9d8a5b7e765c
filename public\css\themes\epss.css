/*
 *  Document   : fancy.css
 *  Author     : pixelcave
 *  Description: THEME FANCY
 *
 */

/* Main Dark Colors */
body,
.nav.navbar-nav-custom > li > a,
.navbar-default .navbar-nav > li > a,
.form-control,
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus,
li.dropdown-header,
.chosen-container-single .chosen-single,
.themed-color-dark {
    color: #333333;
}

#page-container,
#sidebar,
#sidebar-alt,
.table-pricing.table-featured td,
.table-pricing td.table-featured{
    background-color: #333333;
}

.themed-background-dark {
    background-color: #414770;
}

.themed-border-dark {
    border-color: #333333;
}

header.navbar-inverse.navbar-fixed-bottom {
    border-top-color: #333333;
}

header.navbar-inverse.navbar-fixed-top,
.table-pricing.table-featured th,
.table-pricing th.table-featured {
    border-bottom-color: #333333;
}

.navbar.navbar-inverse {
    background-color: #483f5f;
}

/* Main Light Colors */
.sidebar-nav a,
.header-section h1 i,
blockquote:before {
    color: #f5f0f0;
}

/*.sidebar-nav a:hover, .sidebar-nav a:focus, .sidebar-nav a.open, .sidebar-nav li.active > a {*/
    /*color: #ffffff;*/
    /*text-decoration: none;*/
    /*background-color: #57b846;*/
    /*!*background: rgba(0, 0, 0, 0.15);*!*/
/*}*/

.sidebar-nav a:hover > .sidebar-nav-icon,
.sidebar-nav a:hover > .sidebar-nav-indicator {
    color: #57b846;
}

#page-content,
.table-hover > tbody > tr:hover > td,
.table-hover > tbody > tr:hover > th,
ul.wysihtml5-toolbar a.btn.wysihtml5-command-active,
.slider-track,
.nav-horizontal a {
    background-color: #f5f0f0;
}

blockquote,
.table thead > tr > th,
.table tbody > tr > th,
.table tfoot > tr > th,
.table thead > tr > td,
.table tbody > tr > td,
.table tfoot > tr > td,
.table tbody + tbody,
.table-bordered,
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > th,
.table-bordered > tfoot > tr > th,
.table-bordered > thead > tr > td,
.table-bordered > tbody > tr > td,
.table-bordered > tfoot > tr > td,
.list-group-item,
.nav-tabs > li > a:hover,
.pager > li > a,
.pager > li > span,
.pager > li.disabled > a:hover,
.dropzone,
ul.wysihtml5-toolbar a.btn.wysihtml5-command-active {
    border-color: #f5f0f0;
}

.dataTables_wrapper > div {
    border-color: #f5f0f0;
    border-top-width: 0;
}

header.navbar-default.navbar-fixed-bottom,
.content-header,
li.dropdown-header,
.breadcrumb-top,
.style-alt footer {
    border-top-color: #f5f0f0;
}

header.navbar-default.navbar-fixed-top,
.block-title,
fieldset legend,
.form-bordered .form-group,
.wizard-steps,
.nav-tabs,
li.dropdown-header,
.style-alt .content-header,
.style-alt .breadcrumb-top {
    border-bottom-color: #f5f0f0;
}

.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
    border-color: #f5f0f0;
    border-bottom-color: transparent;
}

.block-title .nav-tabs > li.active > a,
.block-title .nav-tabs > li.active > a:hover,
.block-title .nav-tabs > li.active > a:focus {
    border-color: #f5f0f0;
    border-bottom-color: #ffffff;
}

.block-title,
.navbar.navbar-default,
.form-bordered .form-group.form-actions,
.table tfoot > tr > th,
.table tfoot > tr > td,
a.list-group-item:hover,
a.list-group-item:focus,
.nav > li > a:hover,
.nav > li > a:focus,
li.dropdown-header,
.style-alt .content-header + .breadcrumb-top,
.style-alt .breadcrumb-top + .content-header,
.style-alt footer,
.dropzone,
.dataTables_wrapper > div {
    background-color: #fcfafb;
}

.nav-horizontal i {
    color: #e8dade;
}

.switch-default input:checked + span,
.style-alt .block-title {
    background-color: #e8dade;
}

.block,
.form-control,
.input-group-addon,
.switch-default span,
.dropdown-menu,
.style-alt .block,
.chosen-container-single .chosen-single,
.chosen-container-single .chosen-search input[type="text"],
.chosen-container-multi .chosen-choices,
div.tagsinput,
.select2-container .select2-dropdown,
.select2-container--default .select2-selection--single,
.select2-container--default .select2-selection--multiple,
.select2-container--default.select2-container--focus .select2-selection--multiple {
    border-color: #e8dade;
}

footer,
.media-feed > .media {
    border-top-color: #e8dade;
}

.content-header,
.content-top,
.block-top,
.breadcrumb-top,
.style-alt .block-title {
    border-bottom-color: #e8dade;
}

.block-title h1 small, .block-title h2 small, .block-title h3 small, .block-title h4 small, .block-title h5 small, .block-title h6 small {
    color: #dddddd;
}

.content-header-media {
    border-top-color: #222222;
}

/* Main Highlight Colors */
.text-primary,
.text-primary:hover,
a,
a:hover,
a:focus,
.nav-pills > .active > a > .badge,
.pagination > li > a,
.pagination > li > span,
.btn-link,
.btn-link:hover,
.btn-link:focus,
.btn-link.btn-icon:hover,
.btn-link.btn-icon:focus,
.themed-color {
    color: #f46036;
}

.nav.navbar-nav-custom > li.open > a,
.nav.navbar-nav-custom > li > a:hover,
.nav.navbar-nav-custom > li > a:focus,
.navbar-default .navbar-nav > li > a:hover,
.navbar-default .navbar-nav > li > a:focus,
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus,
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus,
.navbar-inverse .navbar-nav > li > a:hover,
.navbar-inverse .navbar-nav > li > a:focus,
.navbar-inverse .navbar-nav > .active > a,
.navbar-inverse .navbar-nav > .active > a:hover,
.navbar-inverse .navbar-nav > .active > a:focus,
.navbar-inverse .navbar-nav > .open > a,
.navbar-inverse .navbar-nav > .open > a:hover,
.navbar-inverse .navbar-nav > .open > a:focus,
a.sidebar-brand:hover,
a.sidebar-brand:focus,
a.sidebar-title:hover,
a.sidebar-title:focus,
#to-top:hover,
.timeline-list .active .timeline-icon,
.table-pricing.table-featured th,
.table-pricing th.table-featured,
.wizard-steps div.done span,
.wizard-steps div.active span,
.switch-primary input:checked + span,
a.list-group-item.active,
a.list-group-item.active:hover,
a.list-group-item.active:focus,
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus,
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus,
.dropdown-menu > .active > a,
.dropdown-menu > .active > a:hover,
.dropdown-menu > .active > a:focus,
.nav .open > a,
.nav .open > a:hover,
.nav .open > a:focus,
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus,
.pager > li > a:hover,
.pagination > li > a:hover,
.label-primary,
.chosen-container .chosen-results li.highlighted,
.chosen-container-multi .chosen-choices li.search-choice,
.datepicker table tr td.active,
.datepicker table tr td.active:hover,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active:hover,
.datepicker table tr td.active:hover:hover,
.datepicker table tr td.active.disabled:hover,
.datepicker table tr td.active.disabled:hover:hover,
.datepicker table tr td.active:active,
.datepicker table tr td.active:hover:active,
.datepicker table tr td.active.disabled:active,
.datepicker table tr td.active.disabled:hover:active,
.datepicker table tr td.active.active,
.datepicker table tr td.active:hover.active,
.datepicker table tr td.active.disabled.active,
.datepicker table tr td.active.disabled:hover.active,
.datepicker table tr td.active.disabled,
.datepicker table tr td.active:hover.disabled,
.datepicker table tr td.active.disabled.disabled,
.datepicker table tr td.active.disabled:hover.disabled,
.datepicker table tr td.active[disabled],
.datepicker table tr td.active:hover[disabled],
.datepicker table tr td.active.disabled[disabled],
.datepicker table tr td.active.disabled:hover[disabled],
.datepicker table tr td span.active:hover,
.datepicker table tr td span.active:hover:hover,
.datepicker table tr td span.active.disabled:hover,
.datepicker table tr td span.active.disabled:hover:hover,
.datepicker table tr td span.active:active,
.datepicker table tr td span.active:hover:active,
.datepicker table tr td span.active.disabled:active,
.datepicker table tr td span.active.disabled:hover:active,
.datepicker table tr td span.active.active,
.datepicker table tr td span.active:hover.active,
.datepicker table tr td span.active.disabled.active,
.datepicker table tr td span.active.disabled:hover.active,
.datepicker table tr td span.active.disabled,
.datepicker table tr td span.active:hover.disabled,
.datepicker table tr td span.active.disabled.disabled,
.datepicker table tr td span.active.disabled:hover.disabled,
.datepicker table tr td span.active[disabled],
.datepicker table tr td span.active:hover[disabled],
.datepicker table tr td span.active.disabled[disabled],
.datepicker table tr td span.active.disabled:hover[disabled],
.bootstrap-timepicker-widget table td a:hover,
div.tagsinput span.tag,
.slider-selection,
.themed-background,
.select2-container--default .select2-selection--multiple .select2-selection__choice,
.select2-container--default .select2-results__option--highlighted[aria-selected],
.nav-horizontal a:hover,
.nav-horizontal li.active a {
    background-color: #57b846;
}

.timeline-list .active .timeline-icon,
.form-control:focus,
.wizard-steps span,
.switch-primary span,
a.list-group-item.active,
a.list-group-item.active:hover,
a.list-group-item.active:focus,
.pager > li > a:hover,
.pagination > li > a:hover,
.chosen-container .chosen-drop,
.chosen-container-multi .chosen-choices li.search-choice,
.chosen-container-active .chosen-single,
.chosen-container-active.chosen-with-drop .chosen-single,
.chosen-container-active .chosen-choices,
div.tagsinput span.tag,
.themed-border,
.select2-container.select2-container--open .select2-dropdown,
.select2-container--default.select2-container--open .select2-selection--single,
.select2-container--default.select2-container--open .select2-selection--multiple,
.select2-container--default.select2-container--focus.select2-container--open .select2-selection--multiple,
.select2-container--default.select2-container--focus .select2-selection--single {
    border-color: #57b846;
}

.nav .caret,
.nav a:hover .caret,
.nav a:focus .caret {
    border-top-color: #57b846;
    border-bottom-color: #57b846;
}

.sidebar-nav a.active,
.sidebar-nav ul a.active,
.sidebar-nav ul a.active:hover {
    border-left-color: #57b846;
}

/* NProgress */
#nprogress .bar {
    background-color: #57b846;
}

#nprogress .peg {
    box-shadow: 0 0 10px #57b846, 0 0 5px #57b846;
}

#nprogress .spinner-icon {
    border-top-color:  #57b846;
    border-left-color: #57b846;
}

/* FullCalendar buttons */
.fc-state-default {
    background-color: #eb6a8e;
    border-color: #57b846;
}

.fc-state-hover,
.fc-state-down,
.fc-state-active,
.fc-state-disabled {
    background-color: #57b846;
}

.fc-state-highlight {
    background-color: #fcfafb;
}

/* Specific for default & primary button */
.btn-default {
    background-color: #f5f0f1;
    border-color: #e8dade;
    color: #333333;
}

.btn-default:hover {
    background-color: #f5f0f0;
    border-color: #cfc2c6;
}

.btn-default:focus,
.btn-default:active,
.btn-default.active,
.open .btn-default.dropdown-toggle {
    background-color: #e5dfe1;
    border-color: #e5dfe1;
}

.btn-default.disabled,
.btn-default.disabled:hover,
.btn-default.disabled:focus,
.btn-default.disabled:active,
.btn-default.disabled.active,
.btn-default[disabled]:hover,
.btn-default[disabled]:focus,
.btn-default[disabled]:active,
.btn-default[disabled].active,
fieldset[disabled] .btn-default:hover,
fieldset[disabled] .btn-default:focus,
fieldset[disabled] .btn-default:active,
fieldset[disabled] .btn-default.active {
    background-color: #e5dfe1;
    border-color: #e5dfe1;
}

.btn-primary {
    background-color: #414770;
    border-color: #414770;
}

.btn-primary.btn-alt {
    color: #57b846;
}

.btn-primary:hover {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}

.btn-primary:focus,
.btn-primary.focus,
.btn-primary:active,
.btn-primary:active:hover,
.btn-primary:active:focus,
.btn-primary.active,
.btn-primary.active:hover,
.btn-primary.active:focus,
.open .btn-primary.dropdown-toggle,
.open .btn-primary.dropdown-toggle:hover,
.open .btn-primary.dropdown-toggle:focus,
.open .btn-primary.dropdown-toggle.focus {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}

.btn-primary.disabled,
.btn-primary.disabled:hover,
.btn-primary.disabled:focus,
.btn-primary.disabled:active,
.btn-primary.disabled.active,
.btn-primary[disabled]:hover,
.btn-primary[disabled]:focus,
.btn-primary[disabled].focus,
.btn-primary[disabled]:active,
.btn-primary[disabled].active,
.btn-primary[disabled]:active:focus,
.btn-primary[disabled].active:focus,
fieldset[disabled] .btn-primary:hover,
fieldset[disabled] .btn-primary:focus,
fieldset[disabled] .btn-primary.focus,
fieldset[disabled] .btn-primary:active,
fieldset[disabled] .btn-primary.active,
fieldset[disabled] .btn-primary:active:focus,
fieldset[disabled] .btn-primary.active:focus {
    background-color: #57b846;
    border-color: #57b846;
}

.btn-warning {
    background-color: #f46036;
    border-color: #f46036;
}

.btn-warning.btn-alt {
    color: #f46036;
}

.btn-warning:hover {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}

.btn-warning:focus,
.btn-warning.focus,
.btn-warning:active,
.btn-warning:active:hover,
.btn-warning:active:focus,
.btn-warning.active,
.btn-warning.active:hover,
.btn-warning.active:focus,
.open .btn-warning.dropdown-toggle,
.open .btn-warning.dropdown-toggle:hover,
.open .btn-warning.dropdown-toggle:focus,
.open .btn-warning.dropdown-toggle.focus {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}

.btn-warning.disabled,
.btn-warning.disabled:hover,
.btn-warning.disabled:focus,
.btn-warning.disabled:active,
.btn-warning.disabled.active,
.btn-warning[disabled]:hover,
.btn-warning[disabled]:focus,
.btn-warning[disabled].focus,
.btn-warning[disabled]:active,
.btn-warning[disabled].active,
.btn-warning[disabled]:active:focus,
.btn-warning[disabled].active:focus,
fieldset[disabled] .btn-warning:hover,
fieldset[disabled] .btn-warning:focus,
fieldset[disabled] .btn-warning.focus,
fieldset[disabled] .btn-warning:active,
fieldset[disabled] .btn-warning.active,
fieldset[disabled] .btn-warning:active:focus,
fieldset[disabled] .btn-warning.active:focus {
    background-color: #57b846;
    border-color: #57b846;
}

.btn-info {
    background-color: #00acbf;
    border-color: #00acbf;
}

.btn-info.btn-alt {
    color: #00acbf;
}

.btn-info:hover {
    background-color: #414770;
    border-color: #414770;
    color: #ffffff;
}

.btn-info:focus,
.btn-info.focus,
.btn-info:active,
.btn-info:active:hover,
.btn-info:active:focus,
.btn-info.active,
.btn-info.active:hover,
.btn-info.active:focus,
.open .btn-info.dropdown-toggle,
.open .btn-info.dropdown-toggle:hover,
.open .btn-info.dropdown-toggle:focus,
.open .btn-info.dropdown-toggle.focus {
    background-color: #31375d;
    border-color: #31375d;
    color: #ffffff;
}

.btn-info.disabled,
.btn-info.disabled:hover,
.btn-info.disabled:focus,
.btn-info.disabled:active,
.btn-info.disabled.active,
.btn-info[disabled]:hover,
.btn-info[disabled]:focus,
.btn-info[disabled].focus,
.btn-info[disabled]:active,
.btn-info[disabled].active,
.btn-info[disabled]:active:focus,
.btn-info[disabled].active:focus,
fieldset[disabled] .btn-info:hover,
fieldset[disabled] .btn-info:focus,
fieldset[disabled] .btn-info.focus,
fieldset[disabled] .btn-info:active,
fieldset[disabled] .btn-info.active,
fieldset[disabled] .btn-info:active:focus,
fieldset[disabled] .btn-info.active:focus {
    background-color: #414770;
    border-color: #414770;
}

/* Mini Sidebar */
@media screen and (min-width: 992px) {
    .sidebar-mini.sidebar-visible-lg-mini #sidebar .sidebar-nav .sidebar-nav-menu + ul {
        border-left-color: #57b846;
        background-color: #333333;
    }
}

.epss-title-s1 {
    background-color: #414770;
    color: #FFF;
    border-radius: 0;
    border: none;
}

.epss-title-s2 {
    background-color: #f46036;
    color: #FFF;
    border: none;
}

.epss-bg-dark {
    background-color: #414770;
    color: #FFF;
}

.loading-modal .modal-dialog{
    display: table;
    position: relative;
    margin: 0 auto;
    top: calc(50% - 24px);
}

.loading-modal .modal-dialog .modal-content{
    background-color: transparent;
    border: none;
    box-shadow: none;
}

.nowrap {
  white-space: nowrap;
}

.btn-secondary {
  color: black;
  font-weight: bold !important;
  background-color: #dcdcdc;
  border-color: #dcdcdc;
}
.btn-secondary:hover {
  background-color: #c0c0c0;
  border-color: #c0c0c0;
}

.btn-white {
  color:black !important; 
  font-weight: bold !important;
  background-color: #FFFFFF;
  border-color: #FFFFFF;
}

.btn-yellow {
  color:#000 !important; 
  font-weight: bold !important;
  background-color: #FFC312;
  border-color: #FFC312;
}

.btn-yellow:hover {
  background-color: #F79F1F;
  border-color: #F79F1F;
}

.btn-red {
  color:white !important; 
  font-weight: bold !important;
  background-color: #e84118;
  border-color: #e84118;
}

.btn-red:hover {
  background-color: #c23616;
  border-color: #c23616;
}
 
.btn-green {
  color:white !important; 
  font-weight: bold !important;
  background-color: #008000;
  border-color: #008000;
}

.btn-green:hover {
  background-color: #32CD32;
  border-color: #32CD32;
}

.buttonblink {
  background-color: #004A7F;
  -webkit-border-radius: 3px;
  font-weight: bold !important;
  border-radius: 3px;
  border: none;
  color: #FFFFFF;  
  cursor: pointer;
  display: inline-block;
  font-family: Arial;
  font-size: 12px;
  padding: 5px 10px;
  text-align: center;
  text-decoration: none;
  -webkit-animation: glowing 1500ms infinite;
  -moz-animation: glowing 1500ms infinite;
  -o-animation: glowing 1500ms infinite;
  animation: glowing 1500ms infinite;
}
@-webkit-keyframes glowing {
  0% { background-color: #B20000; -webkit-box-shadow: 0 0 1px #B20000; }
  50% { background-color: #FF0000; -webkit-box-shadow: 0 0 20px #FF0000; }
  100% { background-color: #B20000; -webkit-box-shadow: 0 0 1px #B20000; }
}

@-moz-keyframes glowing {
  0% { background-color: #B20000; -moz-box-shadow: 0 0 1px #B20000; }
  50% { background-color: #FF0000; -moz-box-shadow: 0 0 20px #FF0000; }
  100% { background-color: #B20000; -moz-box-shadow: 0 0 1px #B20000; }
}

@-o-keyframes glowing {
  0% { background-color: #B20000; box-shadow: 0 0 1px #B20000; } // maroon color
  50% { background-color: #FF0000; box-shadow: 0 0 20px #FF0000; } // red color
  100% { background-color: #B20000; box-shadow: 0 0 1px #B20000; } // maroon color
}

@keyframes glowing {
  0% { background-color: #B20000; box-shadow: 0 0 3px #B20000; } // maroon color
  50% { background-color: #FF0000; box-shadow: 0 0 40px #FF0000; } // red color
  100% { background-color: #B20000; box-shadow: 0 0 3px #B20000; } // maroon color
}

.btn-blinkyellow {
  color:black !important; 
  font-weight: bold !important;
  background-color: #FFFF00;
  border-color: #FFFF00;
   -webkit-border-radius: 3px;
  font-weight: bold !important;
  border-radius: 3px;
  border: none;
  cursor: pointer;
  display: inline-block;
  font-family: Arial;
  font-size: 12px;
  padding: 5px 10px;
  text-align: center;
  text-decoration: none;
  -webkit-animation: glowing_yellow 1500ms infinite;
  -moz-animation: glowing_yellow 1500ms infinite;
  -o-animation: glowing_yellow 1500ms infinite;
  animation: glowing_yellow 1500ms infinite;
}

@-webkit-keyframes glowing_yellow {
  0% { background-color: #ffc40c; -webkit-box-shadow: 0 0 1px #ffc40c; }
  50% { background-color: #ffff00; -webkit-box-shadow: 0 0 20px #ffff00; }
  100% { background-color: #ffc40c; -webkit-box-shadow: 0 0 1px #ffc40c; }
}

@-moz-keyframes glowing_yellow {
  0% { background-color: #ffc40c; -moz-box-shadow: 0 0 1px #ffc40c; }
  50% { background-color: #ffff00; -moz-box-shadow: 0 0 20px #ffff00; }
  100% { background-color: #ffc40c; -moz-box-shadow: 0 0 1px #ffc40c; }
}

@-o-keyframes glowing_yellow {
  0% { background-color: #ffc40c; box-shadow: 0 0 1px #ffc40c; } // maroon color
  50% { background-color: #ffff00; box-shadow: 0 0 20px #ffff00; } // red color
  100% { background-color: #ffc40c; box-shadow: 0 0 1px #ffc40c; } // maroon color
}

@keyframes glowing_yellow {
  0% { background-color: #ffc40c; box-shadow: 0 0 3px #ffc40c; } // maroon color
  50% { background-color: #ffff00; box-shadow: 0 0 40px #ffff00; } // red color
  100% { background-color: #ffc40c; box-shadow: 0 0 3px #ffc40c; } // maroon color
}

.btn-choose {
    /* color:black !important;  */
    font-weight: bold !important;
    background-color: #ffffff;
}

.btn-choose:hover {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}

.btn-choose:focus,
.btn-choose.focus,
.btn-choose:active,
.btn-choose:active:hover,
.btn-choose:active:focus,
.btn-choose.active,
.btn-choose.active:hover,
.btn-choose.active:focus,
.open .btn-choose.dropdown-toggle,
.open .btn-choose.dropdown-toggle:hover,
.open .btn-choose.dropdown-toggle:focus,
.open .btn-choose.dropdown-toggle.focus {
    background-color: #57b846;
    border-color: #57b846;
    color: #ffffff;
}
