<?php

namespace App\Console\Commands;

use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class HandleUpdateEpGPKISigningListStatusUserOrg extends Command
{
    protected $signature = 'gpki:update-status-user-org 
    {--current-only : Process only current year table}
    {--historical-only : Process only historical (2024) table}
    {--batch-size=500 : Number of records to process in a batch}';

    protected $description = 'Update ePerolehan user and organization status for all records in the tables';

    private $connection = 'mysql_ep_support';
    private $currentTable = 'ep_gpki_user_signing_list';
    private $historicalTable = 'ep_gpki_user_signing_list_2024';
    private $oracleConnection = 'oracle_nextgen_rpt';

    public function handle()
    {
        Log::info(__NAMESPACE__ . '::' . __FUNCTION__ . ' Starting ePerolehan status update for multiple tables');

        // Determine which tables to process based on options
        $processCurrentTable = !$this->option('historical-only');
        $processHistoricalTable = !$this->option('current-only');

        $tablesToProcess = [];
        if ($processCurrentTable) {
            $tablesToProcess[] = $this->currentTable;
        }
        if ($processHistoricalTable) {
            $tablesToProcess[] = $this->historicalTable;
        }

        if (empty($tablesToProcess)) {
            $this->error('No tables selected for processing');
            return 1;
        }

        // Verify tables exist
        foreach ($tablesToProcess as $table) {
            if (!DB::connection($this->connection)->getSchemaBuilder()->hasTable($table)) {
                $this->error("Table '{$table}' does not exist");
                return 1;
            }

            // Verify required columns exist
            $columns = DB::connection($this->connection)->getSchemaBuilder()->getColumnListing($table);
            $requiredColumns = ['identification_no', 'updated_at', 'status_ep_user', 'status_ep_org'];

            $missingColumns = array_diff($requiredColumns, $columns);
            if (!empty($missingColumns)) {
                $this->error("Table '{$table}' is missing required columns: " . implode(', ', $missingColumns));
                return 1;
            }
        }

        $batchSize = (int) $this->option('batch-size');

        // Process each table
        foreach ($tablesToProcess as $table) {
            $this->info("Processing table: {$table}");

            // Count total records
            $totalRecords = DB::connection($this->connection)
                ->table($table)
                ->whereNotNull('identification_no')
                ->count();

            if ($totalRecords === 0) {
                $this->warn("No records found in {$table} with identification numbers");
                continue;
            }

            $this->info("Found {$totalRecords} records to process in {$table}");
            $bar = $this->output->createProgressBar($totalRecords);

            // Process in batches
            $processedCount = 0;
            while ($processedCount < $totalRecords) {
                // Get a batch of records
                $records = DB::connection($this->connection)
                    ->table($table)
                    ->whereNotNull('identification_no')
                    ->orderBy('id')
                    ->skip($processedCount)
                    ->take($batchSize)
                    ->get(['id', 'identification_no']);

                if ($records->isEmpty()) {
                    break;
                }

                // Process each record in this batch
                $updateBatch = [];
                foreach ($records as $record) {
                    try {
                        // Check if the identification_no exists in ePerolehan
                        $epUserStatus = $this->checkUserExists($record->identification_no);

                        // Initialize organization status
                        $epOrgStatus = 'not_found';

                        // If the user exists, check organization type and status
                        if ($epUserStatus === 'exist') {
                            // Get user and organization details
                            $userDetails = $this->getUserDetails($record->identification_no);

                            if ($userDetails) {
                                // Check if user is a supplier (org_type_id == 15)
                                if ($userDetails->org_type_id == 15) {
                                    $epOrgStatus = 'supplier';
                                } else {
                                    // Regular government organization
                                    // Get the org_code using the org_profile_id
                                    $orgCode = $this->getOrgCodeForProfileId($userDetails->org_profile_id);

                                    if ($orgCode) {
                                        $epOrgStatus = 'exist';
                                    }
                                }
                            }
                        }

                        // Add to update batch
                        $updateBatch[] = [
                            'id' => $record->id,
                            'status_ep_user' => $epUserStatus,
                            'status_ep_org' => $epOrgStatus
                        ];

                    } catch (\Exception $e) {
                        Log::error("Error processing ID {$record->id}, identification_no: {$record->identification_no} - " . $e->getMessage());

                        // Add error status
                        $updateBatch[] = [
                            'id' => $record->id,
                            'status_ep_user' => 'error',
                            'status_ep_org' => 'error'
                        ];
                    }

                    $bar->advance();
                }

                // Update database with this batch
                $this->batchUpdateRecords($table, $updateBatch);

                $processedCount += count($records);
                usleep(100); // Small pause between batches
            }

            $bar->finish();
            $this->line('');
            $this->info("Completed processing {$table}");

            // Show summary for this table
            $userSummary = DB::connection($this->connection)
                ->table($table)
                ->selectRaw('status_ep_user, count(*) as count')
                ->whereNotNull('status_ep_user')
                ->groupBy(['status_ep_user'])
                ->get();

            $this->info("\nePerolehan User Status Summary for {$table}:");
            foreach ($userSummary as $stat) {
                $this->info(sprintf("%s: %d records", $stat->status_ep_user, $stat->count));
            }

            $orgSummary = DB::connection($this->connection)
                ->table($table)
                ->selectRaw('status_ep_org, count(*) as count')
                ->whereNotNull('status_ep_org')
                ->groupBy(['status_ep_org'])
                ->get();

            $this->info("\nePerolehan Organization Status Summary for {$table}:");
            foreach ($orgSummary as $stat) {
                $this->info(sprintf("%s: %d records", $stat->status_ep_org, $stat->count));
            }
        }

        return 0;
    }

    /**
     * Get complete user details including organization type
     */
    private function getUserDetails(string $identificationNo)
    {
        try {
            $query = DB::connection($this->oracleConnection)->table('pm_user as pmu');
            $query->join('pm_user_org as pmuo', 'pmu.user_id', '=', 'pmuo.user_id');
            $query->where('pmu.record_status', 1);
            $query->where('pmuo.record_status', 1);
            $query->where('pmu.identification_no', $identificationNo);
            $query->select('pmu.user_id', 'pmu.org_type_id', 'pmuo.org_profile_id');

            return $query->first();
        } catch (\Exception $e) {
            Log::error("Error getting user details: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Get the org_code for an org_profile_id
     */
    private function getOrgCodeForProfileId(string $orgProfileId)
    {
        try {
            // First try in PM_ORG_VALIDITY
            $query = DB::connection($this->oracleConnection)->table('pm_org_validity');
            $query->where('record_status', 1);
            $query->where('org_profile_id', $orgProfileId);
            $query->select('org_code');

            $result = $query->first();

            if ($result && !empty($result->org_code)) {
                return $result->org_code;
            }

            // If not found, check if it's a factoring organization
            $query = DB::connection($this->oracleConnection)->table('pm_org_profile as pmop');
            $query->join('pm_financial_org as pmfo', 'pmop.factoring_org_id', '=', 'pmfo.financial_org_id');
            $query->where('pmfo.record_status', 1);
            $query->where('pmop.org_profile_id', $orgProfileId);
            $query->select('pmfo.biz_reg_no as org_code');

            $result = $query->first();

            return $result ? $result->org_code : null;
        } catch (\Exception $e) {
            Log::error("Error getting org_code for profile ID: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Check if a user exists in the ePerolehan system
     */
    private function checkUserExists(string $identificationNo)
    {
        try {
            $query = DB::connection($this->oracleConnection)->table('pm_user as pmu');
            $query->join('pm_user_org as pmuo', 'pmu.user_id', '=', 'pmuo.user_id');
            $query->where('pmu.record_status', 1);
            $query->where('pmuo.record_status', 1);
            $query->where('pmu.identification_no', $identificationNo);
            $query->select('pmu.user_id');

            $result = $query->get();

            if (count($result) > 0) {
                return 'exist';
            } else {
                return 'not_found';
            }
        } catch (\Exception $e) {
            Log::error("Error checking user existence: " . $e->getMessage());
            return 'error';
        }
    }

    /**
     * Update records in the table based on batch of status updates
     */
    private function batchUpdateRecords(string $table, array $updateBatch)
    {
        $now = Carbon::now();

        // Begin transaction to ensure all updates are atomic
        DB::connection($this->connection)->beginTransaction();

        try {
            foreach ($updateBatch as $update) {
                $updateData = [
                    'status_ep_user' => $update['status_ep_user'],
                    'status_ep_org' => $update['status_ep_org'],
                    'updated_at' => $now
                ];

                DB::connection($this->connection)
                    ->table($table)
                    ->where('id', $update['id'])
                    ->update($updateData);
            }

            // Commit all updates if everything was successful
            DB::connection($this->connection)->commit();

        } catch (\Exception $e) {
            // Rollback in case of error
            DB::connection($this->connection)->rollBack();
            Log::error("Error during batch update: " . $e->getMessage());
            throw $e;
        }
    }
}