@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianforms" action="{{url('/find/invoice')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }
    .table tbody > tr > td {
        font-size: 10px;
    }
</style>    
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Invois<br>
            <small>Masukkan <span class="text-info" >PO/CO No </span> pada carian diatas...</small>
        </h1>
    </div>
</div>

@if($approver == null || $supplier == null || $order == null || $invoice == null || $listsupplier == null || $phis == null || $stopinst == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Invois </strong>
                <small>Hasil Carian : {{$carian}}</small>
            </h1>
        </div>
        <div class="row">
            <div class="col-sm-6">

                <li>Status terkini carian diari pengesanan mestilah Penciptaan Invois!</li>
                <li>Carian hendaklah menggunakan nombor PO/CO sahaja!</li>
            </div>
        </div>
    </div>

</div>
@endif


@if($approver != null)
<div class="block block-alt-noborder full">

    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Invois </strong>
                <small>Hasil Carian : {{$carian}}</small>
            </h1>
        </div>
        
    </div>

    <!--Fl_Order_Data Block-->
    <div class="block">
        <a href="{{url('/crm/guideline/stuck_gfmas/PENDING_INVOICE-STL_GL15082019.docx')}}" style="color: #3498db; text-decoration: underline;">Guideline Refire Task Pending Invoice </a><br/><br/>
        <div class="block">
            
            <div class="block-title">
                <h2>FLOrderData  </h2>
            </div>

            <?php
            $ordername = str_replace("&", "&amp;", $invoice->ordername);
            $approverDesignation = str_replace("&", "&amp;", $approver->designation);
            $supplierDesignation = str_replace("&", "&amp;", $supplier->designation);
            $xmlOrder = '<ns2:FL_Order_Data xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/Order/FL_Order_Data">
    <ns2:requestId>' . $invoice->requestid . '</ns2:requestId>
    <ns2:orderId>' . $invoice->orderid . '</ns2:orderId>
    <ns2:orderName>' . $ordername . '</ns2:orderName>
    <ns2:orderType>' . $invoice->ordertype . '</ns2:orderType>
    <ns2:reqDocNo/>
    <ns2:orderDocNumber>' . $invoice->orderdocnmber . '</ns2:orderDocNumber>
    <ns2:approverList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">
        <ns4:userId>' . $approver->userid . '</ns4:userId>
        <ns4:userLoginId>' . $approver->loginid . '</ns4:userLoginId>
        <ns4:userName>' . $approver->username . '</ns4:userName>
        <ns4:calendarName>PUTRAJAYA</ns4:calendarName>
        <ns4:designation>' . $approverDesignation . '</ns4:designation>
    </ns2:approverList>
    <ns2:supplierList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">
        <ns4:userId>' . $supplier->userid . '</ns4:userId>
        <ns4:userLoginId>' . $supplier->loginid . '</ns4:userLoginId>
        <ns4:userName>' . $supplier->username . '</ns4:userName>
        <ns4:calendarName>PUTRAJAYA</ns4:calendarName>
        <ns4:designation>' . $supplierDesignation . '</ns4:designation>
    </ns2:supplierList>';

            foreach ($order as $order) {
                $xmlSap = '    <ns2:sapOrderNo>' . $order->orderno . '</ns2:sapOrderNo>
    <ns2:businessArea>' . $order->businessarea . '</ns2:businessArea>                     
</ns2:FL_Order_Data>';
            }
            ?>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($xmlOrder) }}</code>
                <code class="language-markup">{{ htmlentities($xmlSap) }}</code>
            </pre>
            <br/><br/>
        </div>

        <div class="block">
            <div class="block-title">
                <h2>createdBy  </h2>
            </div>

            {{$supplier->loginid}}<br/><br/>
        </div>

        <div class="block">
            <div class="block-title">
                <h2>isPhis  </h2>
            </div>

            {{$phis}}<br/><br/>

        </div>

        <div class="block">
            <div class="block-title">
                <h2>hasStopFulfilment  </h2>
            </div>

            {{$stopinst}}<br/><br/>

        </div>

        <div class="block">
            <div class="block-title">
                <h2>FlStopDeliveryReq  </h2>
            </div>

<?php
$flDeliveryReq = '<ns3:FL_Stop_Delivery_Req_Data xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/GFMAS/SD/FL_Stop_Delivery_Req_Data">
    <ns3:stopInstrutionReqId xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance" ns4:nil="true"/>
    <ns3:orderNo/>
    <ns3:stopInstructionNo/>
    <ns3:postingDate xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance" ns4:nil="true"/>
    <ns3:instructionItemList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Item_Data">
        <ns4:itemCode xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true"/>
        <ns4:lineItem xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true"/>
        <ns4:amt xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true"/>
        <ns4:quantity xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true"/>
        <ns4:unitOfMeasure xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true"/>
    </ns3:instructionItemList>
    <ns3:stopInstructionDate xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance" ns4:nil="true"/>
    <ns3:createdBy/>
    <ns3:approvedBy/>
    <ns3:sapPOCONo/>
    <ns3:orderId xmlns:ns4="http://www.w3.org/2001/XMLSchema-instance" ns4:nil="true"/>
    <ns3:uuid/>
    <ns3:businessArea/>
</ns3:FL_Stop_Delivery_Req_Data>';

?>
            <pre class="line-numbers">
                <code class="language-markup">{{ htmlentities($flDeliveryReq) }}</code>
            </pre>
            <br/><br/>
        </div>

        <div class="block">
            <div class="block-title">
                <h2>taxInvoice  </h2>
            </div>

            true <br/><br/>

        </div>

        <div class="block">
            <div class="block-title">
                <h2>taxRelief  </h2>
            </div>

            false <br/><br/>

        </div>

        <div class="block">
            <div class="block-title">
                <h2>skipTax  </h2>
            </div>

            true <br/><br/>

        </div>

        <div class="block">
            <div class="block-title">
                <h2>List Supplier</h2>
            </div>

<?php
echo implode(",", $listsupplier);
?><br/><br/>

        </div>
    </div>

    @endif


    @endsection

    @section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>$(function () {
                TablesDatatables.init();
            });</script>
    <script>

        $('#page-container').removeAttr('class');
    </script>
    @endsection



