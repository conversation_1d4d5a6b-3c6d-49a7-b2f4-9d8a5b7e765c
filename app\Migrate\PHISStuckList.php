<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BPMService;


class PHISStuckList {
    use BPMService;
    
    public static function crmService() {
        return new CRMService;
    }
    
    public static function run() {
        ini_set('memory_limit', '-1');
        Log::debug(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();
        //self::insertFromExcel();
        //self::getBPMTask();
        self::checkInvoiceNoPendingPayment();
        
        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        dump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
   
        
    }
    
    protected static function checkInvoiceNoPendingPayment() {
        $list = DB::connection('mysql_ep_support')->table('ep_phis_stuck')
                ->whereNull('status')->get();
        foreach ($list as $obj) {
            $th = new PHISStuckList;
            if($obj->bpm_instance == null){
                $resListCr = $th->getTaskBpmByDocNo($obj->doc_cr);
                if ($resListCr && count($resListCr) > 0) {
                    $objBpm2 = $resListCr->whereIn('activityname', ['Review PR','Review CR'])->first();
                    if($objBpm2){
                        dump('Get Instance only : '.$obj->doc_co);
                        DB::connection('mysql_ep_support')
                                ->table('ep_phis_stuck')
                                ->where('id', $obj->id)
                                ->update([
                                    'bpm_instance' =>  $objBpm2->compositeinstanceid,
                                    'bpm_composite' => $objBpm2->taskdefinitionid
                        ]);
                    }
                }
            }
            //Checking status dibatalkan
            $checkObjDiary = DB::connection('oracle_nextgen_rpt')
                    ->table('PM_TRACKING_DIARY')
                    ->where('DOC_NO',$obj->doc_cr)
                    ->where('status_id','40400')
                    ->count();
            if($checkObjDiary > 0){
                dump('Fixed Done check is Cancelled: '.$obj->doc_cr);
                DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'status' => '1',
                                'changed_at' => Carbon::now()
                    ]);
            }
            //Checking status already changed and not stuck
            $resList = $th->getTaskBpmByDocNo($obj->doc_co);
            if ($resList && count($resList) > 0) {
                $objBpm = $resList->whereIn('activityname', ['Acknowledge CO','Acknowledge PO'])->first();
                if($objBpm){
                    dump('Fixed Done : '.$obj->doc_co);
                    DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'bpm_instance' => $objBpm->compositeinstanceid,
                                'status' => '1',
                                'changed_at' => Carbon::now()
                    ]);
                }
            }    
        }
    }
    
    protected static function getBPMTask() {

        $list = DB::connection('mysql_ep_support')->table('ep_phis_stuck')->get();
        foreach ($list as $obj) {
            $th = new PHISStuckList;
            
            $resListCr = $th->getTaskBpmByDocNo($obj->doc_cr);
            if ($resListCr && count($resListCr) > 0) {
                $objBpm2 = $resListCr->where('activityname', 'Review CR')->first();
                if($objBpm2){
                    dump('Get Instance only : '.$obj->doc_co);
                    DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'bpm_instance' =>  $objBpm2->compositeinstanceid,
                                'bpm_composite' => $objBpm2->taskdefinitionid
                    ]);
                }
            }
                
            $resList = $th->getTaskBpmByDocNo($obj->doc_co);
            if ($resList && count($resList) > 0) {
                $objBpm = $resList->where('activityname', 'Acknowledge CO')->first();
                if($objBpm){
                    dump('Fixed Done : '.$obj->doc_co);
                    DB::connection('mysql_ep_support')
                            ->table('ep_phis_stuck')
                            ->where('id', $obj->id)
                            ->update([
                                'bpm_instance' => $objBpm->compositeinstanceid,
                                'status' => '1'
                    ]);
                }
            } 
        }
    }

    protected static function insertFromExcel() {
        $filename = '/app/Migrate/data/PHISStuckList.xls';
        Excel::load($filename, function($reader){
            
            $reader->each(function($row) {
                DB::connection('mysql_ep_support')
                        ->table('ep_phis_stuck')->insert(
                        [
                            'id' => intval($row->id),
                            'request_date' => Carbon::parse($row->request_date),
                            'doc_co' => trim($row->co_po_number),
                            'doc_co_type' => trim($row->doc_co),
                            'doc_cr' => trim($row->cr_pr_number),
                            'doc_cr_type' => trim($row->doc_cr),
                            'status_name' => trim($row->status_name),
                            'ptj_code' => trim($row->ptj_code),
                            'ptj_name' => trim($row->ptj_name),
                            'state_name' => trim($row->state_name),
                            'status' => null,
                            'created_at' => Carbon::now(),
                        ]
                );
            });
        });
        
    }


}
