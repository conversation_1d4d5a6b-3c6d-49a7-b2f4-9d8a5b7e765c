<?php

namespace App\Services;

class EPService {

    //AS REFERENCE ONLY
    public static $RECORD_STATUS = array(
            '' => '',
            0 => 'RECORD_STATUS_SUSPENDED',
            1 => 'RECORD_STATUS_ACTIVE',
            2 => 'RECORD_STATUS_CANCELLED',
            3 => 'RECORD_STATUS_EXPIRED',
            4 => 'RECORD_STATUS_REJECTED',
            5 => 'RECORD_STATUS_IN_PROGRESS',
            6 => 'RECORD_STATUS_PENDING_RE_APPROVAL',
            7 => 'RECORD_STATUS_7_MIGRATION_DATA', /** Not sure, Not in document **/
            8 => 'RECORD_STATUS_PENDING_ACTIVATION',
            9 => 'RECORD_STATUS_DELETED'
            );
    
    public static $SOFTCERT_STATUS = array(
            '' => '',
            0 => 'Softcert is not required',
            4 => 'Softcert is required/Pending for softcert request',
            2 => 'Softcert requested',
            3 => 'Softcert processing',
            1 => 'Softcert issued',
            6 => 'Softcert rejected',
            9 => 'Softcert request failed (E.g. No response from spki)',
            5 => 'Softcert revoked',
            7 => 'Softcert ready for renewal',
        );
    
    public static $YES_NO= array(
            '' => '',
            1 => 'Yes',
            0 => 'No',
        );
    
    public static $SUPPORTING_DOC_MODE= array(
            '' => '',
            'S' => 'Online',
            'H' => 'Offline',
        );
    
            
    public static $OSB_STATUS= array(
            'S' => 'Success',
            'F' => 'Failed',
        );

    public static $JPN_STATE= array(
            '' => '',
            '01' => 'Johor',
            '02' => 'Kedah',
            '03' => 'Kelantan',
            '04' => 'Melaka',
            '05' => 'Negeri Sembilan',
            '06' => 'Pahang',
            '07' => 'Pulau Pinang',
            '08' => 'Perak',
            '09' => 'Perlis',
            '10' => 'Selangor',
            '11' => 'Terengganu',
            '12' => 'Sabah',
            '13' => 'Sarawak',
            '14' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '15' => 'Wilayah Persekutuan (Labuan)',
            '16' => 'Wilayah Persekutuan (Putrajaya)',
            '21' => 'Johor',
            '22' => 'Johor',
            '23' => 'Johor',
            '24' => 'Johor',
            '25' => 'Kedah',
            '26' => 'Kedah',
            '27' => 'Kedah',
            '28' => 'Kelantan',
            '29' => 'Kelantan',
            '30' => 'Melaka',
            '31' => 'Negeri Sembilan',
            '32' => 'Pahang',
            '33' => 'Pahang',
            '34' => 'Pulau Pinang',
            '35' => 'Pulau Pinang',
            '36' => 'Perak',
            '37' => 'Perak',
            '38' => 'Perak',
            '39' => 'Perak',
            '40' => 'Perlis',
            '41' => 'Selangor',
            '42' => 'Selangor',
            '43' => 'Selangor',
            '44' => 'Selangor',
            '45' => 'Terengganu',
            '46' => 'Terengganu',
            '47' => 'Sabah',
            '48' => 'Sabah',
            '49' => 'Sabah',
            '50' => 'Sarawak',
            '51' => 'Sarawak',
            '52' => 'Sarawak',
            '53' => 'Sarawak',
            '54' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '55' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '56' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '57' => 'Wilayah Persekutuan (Kuala Lumpur)',
            '58' => 'Wilayah Persekutuan (Labuan)',
            '59' => 'Negeri Sembilan',
            '82' => 'Negeri Tidak Diketahui',
            '00' => '00',
            '80' => '80'
    );

    public static $JPN_GENDER= array(
        '' => '',
        'L' => 'LELAKI',
        'P' => 'PEREMPUAN',
        'R' => 'RAGU',
    );

    
    public static $JPN_RESIDENTIAL= array(
        '' => 'BUKAN WARGANEGARA DAN BUKAN PEMASTAUTIN TETAP',
        'B' => 'WARGANEGARA',
        'C' => 'WARGANEGARA',
        'M' => 'PEMASTAUTIN TETAP',
        'P' => 'PEMASTAUTIN TETAP',
        'H' => 'BUKAN WARGANEGARA DAN BUKAN PEMASTAUTIN TETAP',
        'Q' => 'BUKAN WARGANEGARA DAN BUKAN PEMASTAUTIN TETAP',
        'X' => 'BUKAN WARGANEGARA DAN BUKAN PEMASTAUTIN TETAP',
    );

    public static $JPN_RECORD_STATUS= array(
        '' => '',
        'A' => 'AKTIF',
        '1' => 'AKTIF',
        'F' => 'AKTIF',
        '2' => 'MENINGGAL DUNIA',
        'B' => 'MENINGGAL DUNIA',
        'H' => 'MENINGGAL DUNIA',
    );

    public static $JPN_RELIGION= array(
        '' => '',
        '01' => 'ISLAM',
        '02' => 'KRISTIAN',
        '03' => 'BUDDHA',
        '04' => 'HINDU',
        '05' => 'SIKHISM',
        '06' => 'TOA',
        '07' => 'KONFUSIANISMA',
        '08' => 'BAHAI',
        '09' => 'PUAK/SUKU',
        '10' => 'TIADA AGAMA',
        '98' => 'LAIN-LAIN AGAMA',
    );


    public static $CATEGORY_IS_APPROVED= array(
            '' => '',
            0 => 'Rejected',
            1 => 'Approved',
    );
    
    public static $TASK_STATUS= array(
            0 => 'Pending Action',
            1 => 'Completed',
            2 => 'Pending Information',
            3 => 'In Progress',
        );
    
    public static $BUSINESS_TYPE = array(
            'A' => 'Individual',
            'B' => 'Limited Liability Partnership (LLP)',
            'C' => 'ROB - Partnership',
            'D' => 'Business Registration in Sabah (partnership)',
            'E' => 'Business Registration in Sarawak (partnership)',
            'F' => 'ROB - Sole-Proprietorship',
            'G' => 'Business Registration in Sabah (Sole-Proprietorship)',
            'H' => 'Business Registration in Sarawak (Sole-Proprietorship)',
            'I' => 'ROC - Sdn Bhd',
            'J' => 'ROC - Bhd',
            'K' => 'ROS - Organization (Registered with Jabatan Pendaftaran Pertubuhan Malaysia)',
            'L' => 'Cooperative (Registered with Suruhanjaya Koperasi Malaysia)',
            'M' => 'Society',
            'O' => 'Organization (Others)',
            'P' => 'Cooperative (Others)',
            'R' => 'Professional Body',
            'Q' => 'G2G Basic (e.g. Federal Statutory Body, State Govt. Local Authority)',
            'T' => 'PTJ Govt. Seller',
    );
    
    public static $BILL_TYPE= array(
            'S' => 'Softcert Fee',
            'R' => 'Registration Fee',
            'P' => 'Processing Fee',
        );

    public static $IS_CHECKED = array(
            0 => '<i class="fa fa-times-circle fa-2x text-danger"></i>',
            1 => '<i class="fa fa-check-circle fa-2x text-success"></i>',
    );

    public static $QT_QUALIFY = array(
            '' => '-',
            'B' => '<span class="badge label-danger"><strong>B</strong></span>',
            'C' => '<span class="badge label-danger"><strong>C</strong></span>',
    );

    public static $QT_BSV_REGISTRATION_STATUS = array(
            1 => 'Active',
            4 => 'Rejected',
            5 => 'In Progress',
    );
    
    public static $TASK_MISSING_STATUS = array(
            '00' => 'PENDING ACTION',
            '11' => 'NEED MORE INFO',
            '22' => 'IN PROGRESS',
            '33' => 'RUNNING, MAY NEED ATTENTION',
            '44' => 'NEED TO CHECK',
            '55' => 'DONE',
            '66' => 'DATA ISSUE',
    );
    public static $TASK_MISSING_CASE_STATUS = array(
            '0' => 'Case Still Open',
            '1' => 'Case Closed',
    );
    public static $TASK_MISSING_MODULE = array(
            'Direct Purchase' => 'Direct Purchase',
            'Fulfillment' => 'Fulfillment',
            'Supplier Management' => 'Supplier Management',
            'Quotation' => 'Quotation',
            'Direct Purchase / Fulfillment' => 'Direct Purchase / Fulfillment',
            'Contract' => 'Contract',
            'Unknown' => 'Unknown',
            'Catalogue' => 'Catalogue',
            'Quotation/Tender' => 'Quotation/Tender',
            'Contract Management' => 'Contract Management',
            'Profile Management' => 'Profile Management'
    );

    public static $CASE_STATUS = array(
    //        'Open_Resolved' => 'Resolved',
    //        'Open_New' => 'New',
    //        'Open_Assigned' => 'Assigned',
    //        'Closed_Closed' => 'Closed',
            'Pending_User_Verification Input' => 'Pending_User_Verification',
    //        'Closed_Rejected' => 'Rejected',
            'In_Progress' => 'In_Progress',
            'Open_Resolved' => 'Open_Resolved',
            //'Open_New' => 'Open_New',
            'Open_Assigned' => 'Open_Assigned',
            'Closed_Closed' => 'Closed_Closed',
            //'Open_Pending Input' => 'Open_Pending Input',
            'Closed_Rejected' => 'Closed_Rejected',
            //'Closed_Duplicate' => 'Closed_Duplicate',
    );
    
    public static $UNSPSC_LEVEL = array(
            '0' => 'Segment',
            '1' => 'Family',
            '2' => 'Class',
            '3' => 'Commodity',
    );
    
    /**
     * select a.PARAMETER_ID, b.CODE_NAME ,a.PARAMETER_CODE from pm_parameter a, pm_parameter_desc b 
        where a.PARAMETER_ID = b.PARAMETER_ID and b.LANGUAGE_CODE = 'en' and a.parameter_type = 'AT';
     * 
     */
    public static $APPL_TYPE = array(
        'O' => 'Application for Basic Online Account',
        'N' => 'New Application for MOF',
        'B' => 'Application for Bumiputera Status',
        'A' => 'Application for add Category Code',
        'U' => 'Application for update MOF profile',
        'R' => 'Renew MOF Account',
        'C' => 'Cancel MOF Account'
    );
    
    /** 
     * select a.PARAMETER_ID, b.CODE_NAME,a.PARAMETER_CODE from pm_parameter a, pm_parameter_desc b where a.PARAMETER_ID = b.PARAMETER_ID and b.LANGUAGE_CODE = 'en' and a.parameter_type = 'ST';
     'K' => 'Contractor',
     'J' => 'Consultant (Perunding)',
     'B' => 'Basic/Online without for going MOF-Registered',
     'G' => 'G2G Basic (E.g. State Government, Local Council, Federal Statutory Bodies)',
     'P' => 'PTJ Government Seller',
     * **/
    public static $SUPPLIER_TYPE = array(
        'K' => 'Contractor',
        'J' => 'Consultant (Perunding)',
        'B' => 'Basic/Online without for going MOF-Registered',
        'G' => 'G2G Basic (E.g. State Government, Local Council, Federal Statutory Bodies)',
        'P' => 'PTJ Government Seller',
    );
    
    public static $USER_SUPPLIER_PROCESS_PATCHING = array(
            'user_set_active' =>  
                array(
                    'name' => 'Change User & Personnel To (1) Active',
                    'description' => 'Program will checking if PM_USER record status (0) or (9) to set Active PM_USER, PM_USER_ORG, PM_USER_ROLE, SM_PERSONNEL. Do not forget to set Active and also roles in SSO and Liferay by manually.',
                ),
            'personnel_softcert_4' =>  
                array(
                    'name' => 'Change Softcert Status To (4)',
                    'description' => 'Program will checking if softCert status (0) or (1) or (2) or (3) or (7) will change to (4).',
                ),
            'personnel_softcert_1' => 
                array(
                    'name' => 'Change Softcert Status To (1)',
                    'description' => 'Program will checking if softCert status (0) or(3) or (4) or (6) or (7) will change to (1).',
                ),
            'personnel_is_authorized_1' => 
                array(
                    'name' => 'Change IS AUTHORIZED To (1)',
                    'description' => 'Program will checking is not allow for FL_USER or CM_USER. Maximum for 10 users',
                ),
            'personnel_is_contract_signer_1' => 
                array(
                    'name' => 'Change IS CONTRACT SIGNER To (1)',
                    'description' => 'Program will checking is not allow for FL_USER or CM_USER. Maximum for 10 users',
                ),
            'personnel_1' => 
                array(
                    'name' => 'Change Personnel Record Status To (1)',
                    'description' => 'Program will checking if record status (0) or (9) will change to (1).',
                ),
            'personnel_9' => 
                array(
                    'name' => 'Change Personnel Record Status To (9)',
                    'description' => 'Program will checking if record status (0) or (1) will change to (9).',
                ),
/** Disabled on 26/6/2019 , Request by Fauziah, change ICNO can't do by patching. **/        
//            'change_identification_no' => 
//                array(
//                    'name' => 'Change Identification No',
//                    'description' => 'Change IC No. in PM_USER and SM_PERSONNEL. Please make sure, already verified using seach Identity JPN.',
//                ),
            'change_fullname' => 
                array(
                    'name' => 'Change Full Name',
                    'description' => 'Change Full Name in PM_USER and SM_PERSONNEL. Please make sure, already verified using seach Identity JPN.',
                ),    
            'user_set_inactive' =>  
                array(
                    'name' => 'Change User (InActive) and Personnel (Role will remove)',
                    'description' => 'Program will checking if PM_USER record status (1)to set IN Active (Record Status : 0)  PM_USER, PM_USER_ORG, PM_USER_ROLE. Program will set ROLE as NULL in SM_PERSONNEL .'
                    . 'Do not forget to set InActive in SSO and Liferay by manually.',
                ),
            /** personnel_reset_activation added on 2020-01-20 by acoi**/
            'personnel_reset_activation' =>  
                array(
                    'name' => 'Change Personnel To Reset Activation (Creation Login ID)',
                    'description' => 'Program will checking if SM_PERSONNEL record status (8) and user_id is NULL to set  SM_PERSONNEL update email = NULL, ep_role = NULL, user_id = NULL, record_status = 1 ',
                ),
//            'softcert_request_1' => 
//                array(
//                    'name' => 'Change Softcert Request Record Status To (1)',
//                    'description' => 'Program will checking if record status (9) will change to (1).',
//                ),
//            'softcert_request_9' => 
//                array(
//                    'name' => 'Change Softcert Request Record Status To (9)',
//                    'description' => 'Program will checking if record status (1) will change to (9).',
//                ),
//            'personnel_1_role_null' => 
//                array(
//                    'name' => 'Change Personnel Record Status To (1) AND Role to NULL',
//                    'description' => 'Program will checking if record status (8) and Role in personnel is not null',
//                ),
            'request_softcert_dg' => 
                array(
                    'name' => 'Change Softcert Request From TG to DG And Send Web Service RequestSoftcert to DG Portal ',
                    'description' => 'Program will check a record softcert request from TG then send to DG. If list selection is EMPTY. There is no record softcert request TG available to patch it.',
                ),
            'trim_identification_no' => 
                array(
                    'name' => 'Remove SPACE Identification No.' ,
                    'description' => 'Remove SPACE Identification No from beginning and end string. Table related PM_USER & SM_PERSONNEL.',
                ),    
    );

    public static $SUPPLIER_PROCESS_PATCHING = array(
            'supplier_9' => 
                array(
                    'name' => 'Change Supplier Record Status As  DELETED (9)',
                    'description' => 'Program will checking if supplier (record_status IN [0,1,8] ) which is as a basic company or new registration or MOF exp more than one year '
                                     .'If users already in this supplier, program will update record status user to 9 also.',
                ),
        
        /** 1) We allow for migrated supplier or supplier has  MOF 
                        2) We allow for supplier first register KN **/
                    //Checking duplicate SSM
            'supplier_1' => 
                array(
                    'name' => 'Change Supplier Record Status As ACTIVE (1)',
                    'description' => 'Program will checking if record status supplier as (7) or (5) or (9) to set as Active . '
                    . 'There are 2 scenario. \n 1) Program will check MOF still valid (not expired) and no duplicate SSM_NO with same Supplier Type.\n '
                    . '2) Program will application is New Application as ApplType(N,R,O) only and no duplicate SSM_NO with same Supplier Type.'
                                     .'Please make sure supplier information is valid and User role already has Supplier Admin.',
                ),    
            'supplier_2' => 
                array(
                    'name' => 'Change Supplier Record Status As CANCELLED (2)',
                    'description' => 'Program will checking if record status supplier as (5)  to set as Cancel. '
                                     .'If users already in this supplier, program will update record status user to 9 also.',
                ), 
            /**  
             * supplier_business_type  , supplier_ssm_no , supplier_company_name --> hide from patching start on 22/11/2021. Agreed by Shahril
            'supplier_business_type' => 
                array(
                    'name' => "Change Supplier's Business Type",
                    'description' => 'Change Business Type, Integration with IGFMAS, will return new SAP VENDOR CODE. '
                                     .'(IMPORTANT!) This required ENDORSEMENT ePEROLEHAN. Kindly check , there is no pending transaction on this supplier. ',
                ),   
            
            'supplier_ssm_no' => 
                array(
                    'name' => "Change Supplier SSM NO. (Registration No.)",
                    'description' => 'Change SSM NO / Registration No. , Integration with IGFMAS, will return new SAP VENDOR CODE. '
                                    .'(IMPORTANT!) This required ENDORSEMENT ePEROLEHAN if mistaken too much different. Kindly check , there is no pending transaction on this supplier. ',
                ), 
            'supplier_company_name' => 
                array(
                    'name' => "Change Supplier Company Name ",
                    'description' => 'Change Company Name, Integration with IGFMAS, will return new SAP VENDOR CODE. Program will update in SM_SUPPLIER and SM_COMPANY_BASIC. '
                                     .'(IMPORTANT!) This required ENDORSEMENT ePEROLEHAN. Kindly check , there is no pending transaction on this supplier. ',
                ),  
            **/   
            'reset_download_cert_mof' => 
                array(
                    'name' => "Reset Download Virtual Cert MOF Supplier",
                    'description' => 'Program will check IF supplier has MOF Cert or Bumi Cert and then program will set PATH pdf file to NULL.'
                                     .' Supplier need to re-login and re-download MOF Cert.',
                ),  
            'cancel_appl_inprogress' => 
                array(
                    'name' => "Make Cancellation Application (InProgress) ",
                    'description' => 'Program will check Appl No. (InProgress) to set as record_status (9) and is_active_appl (0) ',
                ), 
            'inactive_appl_inprogress' => 
                array(
                    'name' => "Make InActive Application (InProgress) ",
                    'description' => 'Program will check Appl No. (InProgress) to set as is_active_appl (0) ',
                ),    
            'setcancel_appl_history' => 
                array(
                    'name' => "Set Cancellation Status Application (20202)",
                    'description' => 'Program will check Appl ID. (Deleted) to update status ID as Cancel(20202) . After update this, supplier will check on Application History, last status application is Cancelled.',
                ),   
            'mof_account_9' => 
                array(
                    'name' => "Change MOF Account Record Status To (9)",
                    'description' => 'Program will check supplier must have data MOF and expired date more than one year .',
                ),  
            'mof_activate' => 
                array(
                    'name' => "To Activate MOF Certicate.",
                    'description' => 'Program will check SM_APPL , SM_MOF_ACCOUNT , PY_PAYMENT. Table SM_MOF_ACCOUNT will change exp_date,changed_date follow date_changed sm_appl. Table SM_MOF_CERT will set all record active to inactive. ',
                ),
            'sync_personnel_identity_resident' => 
                array(
                    'name' => "Sync each personnel to get latest Resident Status",
                    'description' => 'Program will check all personnel in supplier ID.',
                ),      
        

    );
    
    public static $MMINF_PROCESS_PATCHING = array(
            'mminf_isSent_status_0' =>  
                array(
                    'name' => 'Change Is Sent record status to (0)',
                    'description' => 'Program will checking if Is Sent record status (1) will change to (0).',
                ),
            'mminf_update_material_desc' =>  
                array(
                    'name' => 'Update field MATERIAL_DESC ',
                    'description' => 'Maximun 40 Char only. Program will update all records with same Kod Item for field MATERIAL_DESC. Please be careful, this will impact sending data to 1GFMAS.',
                ),
            'mminf_update_material_add_desc' =>  
                array(
                    'name' => 'Update field MATERIAL_ADD_DESC ',
                    'description' => 'Maximun 960 Char only. Program will update all records with same Kod Item for field MATERIAL_ADD_DESC. Please be careful, this will impact sending data to 1GFMAS.',
                ),
            'mminf_delete' =>  
                array(
                    'name' => 'Delete record mminf',
                    'description' => 'Program will delete permenantly this record. After delete this, please find SC_REQUEST_ITEM ID to patch update changed date fields.  ',
                ),
           
    );
    
    public static $EKONTRAK_STATUS = array(
        '' => '',
        'draft' => 'Draf',
        'pending_codification' => 'Menunggu Kodifikasi',
        'completed_ready_migration' => 'Selesai Dan Menunggu Migrasi',
        'completed_sent_migration' => 'Selesai',
        'failed_migrate' => 'Gagal Migrasi'
    );
    
    public static $BPM_COMPOSITE_MODULE = array(
        'Codification' => 'Codification',
    	'Contract_Management' => 'Contract_Management',
        'Fulfilment' => 'Fulfilment',
        'Order' => 'Order',
        'Procurement_Plan' => 'Procurement_Plan',
        'Profile_Management' => 'Profile_Management',
        'SourcingQT' => 'SourcingQT',
        'SourcingDP' => 'SourcingDP',
        'Supplier_Management' => 'Supplier_Management',
        'YEP_Fulfilment' => 'YEP_Fulfilment',
        'YEP_Order' => 'YEP_Order'
    );
    
    public static $DATA_PORTING_TYPE = array(
    	'Lunch_Porting' => 'Lunch_Porting',
        'Evening_Porting' => 'Evening_Porting',
        'Urgent_Porting' => 'Urgent_Porting',
    );
    
    public static $DATA_PORTING_MODULE = array(
            153 => 'Procument Plan',
            154 => 'Direct Purchase',
            155 => 'Contract',
            156 => 'Quotation Tender',
            157 => 'Fulfilment',
            158 => 'Supplier Management',
            159 => 'Personal Management',
            160 => 'BPM',
            161 => 'Other',
            );
    
    public static $SERVICE_CODE_PHIS = array(
    	'PHS-080' => 'PHS-080',
        'PHS-150' => 'PHS-150',
        'PHS-160' => 'PHS-160',
        'PHS-170' => 'PHS-170',
        'PHS-180' => 'PHS-180',
        'PHS-190' => 'PHS-190',
        'PHS-210' => 'PHS-210',
        'PHS-220' => 'PHS-220',
    );

    public static $SPKI_PROVIDER= array(
        0 => 'Trustgate',
        1 => 'DigiCert',
    );


    // The CPTPP was signed by all 11 participating countries - Australia, Brunei Darussalam, Canada, Chile, Japan, Malaysia, Mexico, New Zealand, Peru, Singapore and Viet Nam
    public static $CPTPP_COUNTRY= array(
        'AUS' => 'Australia', // AUSTRALIAN AIRLINES LIMITED  200302000018 (994166-T)
        'BRU' => 'Brunei Darussalam', // BRUNEI SHELL PETROLEUM CO.LTD.   (993237-A)
        'CAN' => 'Canada',
        'CHI' => 'Chile',
        'JAP' => 'Japan',
        'MAL' => 'Malaysia', //
        'MEX' => 'Mexico',
        'NZD' => 'New Zealand', // NEW ZEALAND INSURANCE COMPANY LTD.   196102000001 (990087-K)
        'PER' => 'Peru',
        'SIN' => 'Singapore',  // HITACHI T&D SYSTEMS ASIA PTE. LTD.   201102000007 (995297-H)
        'VIE' => 'VietNam', // VIETNAM AIRLINES JSC   199702000018 (993799-D)
    );

    public static $TASK_SPKI_STATUS= array(
        0 => 'Pending Response',
        1 => 'Pending Resolution',
        2 => 'Closed',
    );
    
    public static $JPN_RACE= array(
        '' => '',
        '0100' => 'MELAYU',
        '0101' => 'BUGIS',
        '0102' => 'BOYAN',
        '0103' => 'BANJAR',
        '0104' => 'JAWA',
        '0105' => 'JAWI PEKAN',
        '0106' => 'MINANGKABAU',
        '0200' => 'CINA',
        '0201' => 'CANTONESE',
        '0202' => 'FOOCHOW',
        '0203' => 'HAINANESE',
        '0204' => 'HENGHUA',
        '0205' => 'HOKCHIA',
        '0206' => 'HOKCHIU',
        '0207' => 'HOKKIEN',
        '0208' => 'KHEK (HAKKA)',
        '0209' => 'KWONGSAI',
        '0210' => 'TEOCHEW',
        '0211' => 'KONGFOO',
        '0212' => 'HYLAM',
        '0300' => 'INDIA',
        '0301' => 'MALAYALI',
        '0302' => 'PUNJABI',
        '0303' => 'SIKH',
        '0304' => 'TAMIL',
        '0305' => 'TELEGU',
        '0306' => 'MALABARI',
        '0307' => 'INDIA MUSLIM',
        '0308' => 'TELUGU',
        '0309' => 'ORISSA',
        '0400' => 'BANGLADESHI',
        '0500' => 'PAKISTANI',
        '0600' => 'SRI LANKA',
        '0601' => 'TAMIL SRI LANKA',
        '0602' => 'MELAYU SRI LANKA',
        '0603' => 'SINHALESE',
        '0700' => 'INDONESIA',
        '0701' => 'TIDUNG',
        '0800' => 'BUMIPUTERA SABAH',
        '0801' => 'BAJAU',
        '0802' => 'DUSUN',
        '0803' => 'KADAZAN',
        '0804' => 'MURUT',
        '0805' => 'SINO-NATIVE',
        '0806' => 'SULUK',
        '0901' => 'BINADAN',
        '0902' => 'BISAYA',
        '0903' => 'BONGOL',
        '0904' => 'BRUNEI',
        '0905' => 'DUMPAS',
        '0906' => 'IRANUN',
        '0907' => 'IDAHAN',
        '0908' => 'KWIJAU',
        '0909' => 'KEDAYAN',
        '0910' => 'LINGKABAU',
        '0911' => 'LUNDAYEH',
        '0912' => 'LASAU',
        '0913' => 'MELANAU',
        '0914' => 'MANGKAAK',
        '0915' => 'MATAGANG',
        '0916' => 'MINOKOK',
        '0917' => 'MELAYU SABAH',
        '0918' => 'MOMOGUN',
        '0919' => 'PAITAN',
        '0920' => 'RUMANAU',
        '0921' => 'RUNGUS',
        '0922' => 'SUNGAI',
        '0923' => 'SONSONGAN',
        '0924' => 'SINULIHAN',
        '0925' => 'TOMBONUO',
        '0926' => 'TAGAL',
        '0927' => 'TINAGAS',
        '0928' => 'COCOS',
        '0929' => 'KIMARAGANG',
        '0930' => 'BOLONGAN',
        '0931' => 'BUTON',
        '0932' => 'KAGAYAN',
        '1000' => 'BUMIPUTERA SARAWAK',
        '1001' => 'MELAYU SARAWAK',
        '1002' => 'MELANAU',
        '1003' => 'KEDAYAN',
        '1004' => 'IBAN ATAU SEA DAYAK',
        '1005' => 'BIDAYUH ATAU LAND DAYAK',
        '1006' => 'KAYAN',
        '1007' => 'KENYAH',
        '1008' => 'MURUT ATAU LUN BAWANG',
        '1009' => 'KELABIT',
        '1010' => 'PUNAN',
        '1011' => 'PENAN',
        '1101' => 'BISAYA',
        '1102' => 'BERAWAN',
        '1103' => 'BELOT',
        '1104' => 'BUKET',
        '1105' => 'BALAU',
        '1106' => 'BATANG AI',
        '1107' => 'BATU ELAH',
        '1108' => 'BAKETAN',
        '1109' => 'BINTULU',
        '1110' => 'BADANG',
        '1111' => 'DUSUN',
        '1112' => 'JAGOI',
        '1113' => 'LAKIPUT',
        '1114' => 'KAJANG',
        '1115' => 'KEJAMAN',
        '1116' => 'KANOWIT',
        '1117' => 'LIRONG',
        '1118' => 'LEMANAK',
        '1119' => 'LAHANAN',
        '1120' => 'LISUM ATAU LUGUM',
        '1121' => 'MATU',
        '1122' => 'MEMALOH',
        '1123' => 'MELIKIN',
        '1124' => 'MELAING',
        '1125' => 'NGURIK',
        '1126' => 'MENONDO',
        '1127' => 'NYAMOK',
        '1128' => 'SEBOP',
        '1129' => 'SEDUAN',
        '1130' => 'SEKAPAN',
        '1131' => 'SEGALANG',
        '1132' => 'SIHAN',
        '1133' => 'SIPENG',
        '1134' => 'SARIBAS',
        '1135' => 'SEBUYAU',
        '1136' => 'SKRANG',
        '1137' => 'SABAN',
        '1138' => 'SELAKAN',
        '1139' => 'SELAKO',
        '1140' => 'TAGAL',
        '1141' => 'TABUN',
        '1142' => 'TUTONG',
        '1143' => 'TANJONG',
        '1144' => 'TATAU',
        '1145' => 'TAUP',
        '1146' => 'UKIT',
        '1147' => 'UNKOP',
        '1148' => 'ULU AI',
        '1149' => 'TORAJA',
        '1150' => 'TIMOR',
        '1151' => 'MENADO',
        '1152' => 'MANURA',
        '1153' => 'BATAK',
        '1154' => 'PATHAN',
        '1155' => 'TONGANS',
        '1200' => 'ORANG ASLI (SEMENANJUNG)',
        '1201' => 'JAKUN',
        '1202' => 'NEGRITO',
        '1203' => 'SAKAI',
        '1204' => 'SEMAI',
        '1205' => 'SEMALAI',
        '1206' => 'TEMIAR',
        '1207' => 'SENOI',
        '1300' => 'LAIN-LAIN ASIA',
        '1301' => 'ARAB',
        '1302' => 'BURMESE',
        '1303' => 'EURASIAN',
        '1304' => 'FIJIAN',
        '1305' => 'FILIPINO',
        '1306' => 'GURKHA',
        '1307' => 'JAPANESE',
        '1308' => 'KHMER',
        '1309' => 'KOREAN',
        '1310' => 'MALTESE',
        '1311' => 'PORTUGESE',
        '1312' => 'THAI',
        '1313' => 'VIETNAMESE',
        '1314' => 'IRANIAN',
        '1315' => 'AFGHAN',
        '1316' => 'CAUCASIAN',
        '1317' => 'KYRGYZ',
        '1318' => 'UBIAN',
        '1319' => 'UZBEKISTAN',
        '1320' => 'AZERBAIJAN',
        '1321' => 'SIAM',
        '1322' => 'KAZAKHSTAN',
        '1400' => 'EUROPEAN',
        '1401' => 'BRITISH',
        '1402' => 'ALGERIA',
        '1403' => 'ANTIGUA-BARBUDA',
        '1404' => 'AUSTRALIA',
        '1405' => 'ANGOLA',
        '1406' => 'ARGENTINA',
        '1407' => 'ALBANIA',
        '1408' => 'AUSTRIA',
        '1410' => 'MIDDLE AFRICA',
        '1411' => 'SOUTH AFRICA',
        '1412' => 'BAHRAIN',
        '1413' => 'BAHAMAS',
        '1414' => 'BARBADOS',
        '1415' => 'BELIZE',
        '1416' => 'BOTSWANA',
        '1418' => 'BENIN',
        '1419' => 'BHUTAN',
        '1420' => 'BOLIVIA',
        '1421' => 'BRAZIL',
        '1422' => 'BURUNDI',
        '1423' => 'BULGARIA',
        '1424' => 'BELGIUM',
        '1425' => 'BELARUS',
        '1427' => 'BOSNIA-HERZEGOVINA',
        '1428' => 'CAMEROON',
        '1429' => 'CHAD',
        '1430' => 'CANADA',
        '1431' => 'CYPRUS',
        '1432' => 'CAPE VERDE',
        '1433' => 'CROTIA',
        '1434' => 'CHILE',
        '1435' => 'COLOMBIA',
        '1436' => 'COMOROS',
        '1437' => 'COSTA-RICA',
        '1438' => 'CUBA',
        '1439' => 'DJBOUTI',
        '1440' => 'DOMINICA',
        '1441' => 'DAHOMEY',
        '1442' => 'DENMARK',
        '1443' => 'EQUADOR',
        '1444' => 'EL SALVADOR',
        '1445' => 'EQUATORIAL GUINEA',
        '1446' => 'ETOPIA',
        '1448' => 'FRANCE',
        '1449' => 'FINLAND',
        '1451' => 'GABON',
        '1452' => 'GAMBIA',
        '1453' => 'GUINEA',
        '1454' => 'GUINEA-BISSAU',
        '1455' => 'GHANA',
        '1456' => 'GRENADA',
        '1457' => 'GUYANA',
        '1458' => 'GUATEMALA',
        '1459' => 'GREECE',
        '1460' => 'GERMANY',
        '1461' => 'HAITI',
        '1462' => 'HONDURAS',
        '1463' => 'HUNGARY',
        '1464' => 'HONG KONG',
        '1466' => 'IRAQ',
        '1467' => 'IVORY COAST',
        '1468' => 'ISRAEL',
        '1469' => 'IRELAND',
        '1470' => 'ITALY',
        '1471' => 'ICELAND',
        '1472' => 'JORDAN',
        '1473' => 'JAMAICA',
        '1475' => 'KUWAIT',
        '1476' => 'KENYA',
        '1477' => 'KIRIBATI',
        '1479' => 'KOREA(UTARA)',
        '1480' => 'KEMBOJA',
        '1481' => 'LEBANON',
        '1482' => 'LIBYA',
        '1483' => 'LESOTHO',
        '1484' => 'LAOS',
        '1485' => 'LIBERIA',
        '1486' => 'LUXEMBOURG',
        '1487' => 'MALI',
        '1488' => 'MALDIVES',
        '1489' => 'MAURITANIA',
        '1490' => 'MOROCCO',
        '1491' => 'MALAWI',
        '1493' => 'MADAGASCAR',
        '1494' => 'MAURITIUS',
        '1495' => 'MEXICO',
        '1496' => 'MOZAMBIQUE',
        '1497' => 'MONGOLIA',
        '1498' => 'MESIR',
        '1499' => 'MYANMAR',
        '1500' => 'LAIN-LAIN',
        '1501' => 'NAMIBIA',
        '1502' => 'NAURU',
        '1503' => 'NEW ZEALAND',
        '1504' => 'NIGERIA',
        '1505' => 'NEPAL',
        '1506' => 'NICARAGUA',
        '1507' => 'NETHERLAND',
        '1508' => 'NORWAY',
        '1509' => 'OMAN',
        '1510' => 'PAPUA NEW GUINEA',
        '1511' => 'PALESTIN',
        '1512' => 'PANAMA',
        '1513' => 'PARAGUAY',
        '1514' => 'PERU',
        '1515' => 'POLAND',
        '1516' => '1516',
        '1517' => 'QATAR',
        '1518' => 'ROMANIA',
        '1519' => 'RWANDA',
        '1520' => 'REPUBLIK CZECH',
        '1521' => 'REPUBLIK SLOVAKIA',
        '1522' => 'SENEGAL',
        '1523' => 'SIERRA LEONE',
        '1524' => 'SOMALIA',
        '1525' => 'SUDAN',
        '1526' => 'SYRIA',
        '1527' => 'ST.LUCIA',
        '1528' => 'ST.VINCENT',
        '1529' => 'SYCHELLES',
        '1530' => 'SOLOMON ISLAND',
        '1531' => 'SRI LANKA',
        '1532' => 'SWAZILAND',
        '1533' => 'SAMOA',
        '1534' => 'SAO TOME & PRINCIPE',
        '1535' => 'SURINAM',
        '1536' => 'SAMOA BARAT',
        '1537' => 'SWEDEN',
        '1538' => 'SPAIN',
        '1539' => 'SWITZERLAND',
        '1540' => 'TUNISIA',
        '1541' => 'TURKEY',
        '1542' => 'TANZANIA',
        '1543' => 'TONGA',
        '1544' => 'TRINIDAD & TOBAGO',
        '1545' => 'TUVALI',
        '1547' => 'TOGO',
        '1548' => 'TAIWAN',
        '1549' => 'UGANDA',
        '1550' => 'UNITED ARAB EMIRATES',
        '1551' => 'UPPER VOLTA',
        '1552' => 'URUGUAY',
        '1553' => 'RUSSIA',
        '1554' => 'UKRAINE',
        '1555' => 'UNITED STATES',
        '1556' => 'VANUATU',
        '1557' => 'VENEZUELA',
        '1559' => 'YEMEN',
        '1560' => 'YUGOSLAVIA',
        '1561' => 'MACEDONIA',
        '1562' => 'ZAIRE',
        '1563' => 'ZAMBIA',
        '1564' => 'ZIMBABWE',
        '9999' => 'MAKLUMAT TIADA',

    );


    public static $CT_CANCELLATION_AGREEMENT_PROGRESS= array(
        '' => '',
        0 => 'Pending Insert Workflow',
        1 => 'Pending Initiate Task',
        2 => 'Pending Terminate Task',
        3 => 'Completed',
        4 => 'In-progress Terminate Task',
        5 => 'In-progress Initiate Task',

    );

}
