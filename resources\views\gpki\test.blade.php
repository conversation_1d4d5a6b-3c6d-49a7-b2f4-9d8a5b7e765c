@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-notes_2"></i>GPKI Test Form<br>
            <small>To Run Test by Parameter!</small>
        </h1>
    </div>
</div>
<div class="block block-alt-noborder full">
    <div class="row">
        <div class="col-md-12">
            <div class="block">
                <div class="block-title">
                    <h2>GPKI Test Simulator</h2>
                </div>
                <div class="pull-right" id="duration"></div>
                <form class="form-horizontal form-bordered" id="gpki-form">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="pin">Pin<span class="text-danger">*</span></label>
                            <div class="col-md-3">
                                <input type="password" id="pin" name="pin" class="form-control" required/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="textToSign">Data To Sign</label>
                            <div class="col-md-6">
                                <textarea id="textToSign" name="textToSign" class="form-control"></textarea>
                            </div>
                        </div>
                    </fieldset>
                    <div class=" pull-right form-group form-actions">
                        <div class="col-xs-12">
                            <input type="button" id="signButton" value="Sign" class="btn btn-sm btn-danger" />
                        </div>
                    </div>
                </form>
                <div class="gpki-load" style="display: none">   
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    <div class='row text-center'><h3><small>Processing ...</small></h3></div>
                </div>
                <div id="gpki-data"></div>
            </div>
        </div>
    </div>
</div>
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/gpki/gpki-api.js"></script>
<script type="text/javascript" language="JavaScript">
    var STARTDATE = null;
    $(document).ready(function () {
        $("#signButton").click(function () {
            if ($("#pin").val() == "") {
                Gpki.alert("Please enter your PIN");
            } else {
                STARTDATE = new Date();
                $('.gpki-load').show();
                $('#gpki-form').hide();
                var plainText = encodeURIComponent(Gpki.hash($("#textToSign").val()));
                var pin = $("#pin").val();
                sign_st(pin, plainText);
            }
        });
    });

    function parseSignResult(msg) {
        console.log('parseSignResult');
        console.log(msg);
        var endDate = new Date();
        var duration = endDate.getTime() - STARTDATE.getTime();
        $('#duration').html("<strong>Duration Taken: " + duration/1000 + " seconds</strong>");

        $(document).ready(function () {
            var startDate = new Date();
            var obj = jQuery.parseJSON(msg);
            var statusCode = obj.status_code;
            var statusMsg = obj.status_message;
            if (statusCode == "0") {
                var startDate = obj.start_date;
                var endDate = obj.end_date;
                var subjectDN = obj.subject_dn;
                var serialNo = obj.serial_no;
                var ic = obj.ic;
                var signedData = encodeURIComponent(obj.signed_data);
                var certinfo =
                        "<h1>Certificate Data<h1><ul><li>User IC : " +
                        ic +
                        " </li><li>Start Date: " +
                        startDate +
                        "</li><li>End Date: " +
                        endDate +
                        "</li><li>Certificate's Subject DN: " +
                        subjectDN +
                        "</li><li>Serial No: " +
                        serialNo +
                        "</li></ul>";
                $("#gpki-data").html(certinfo + "<textarea>" + signedData + "</textarea>");
                Gpki.alert("Successfully Signed the Data");

                /* put your logic to handle the result here */
            } else if (statusCode == "50") {
                Gpki.alert("Wrong PIN");
                
            } else if (statusCode == "40") {
                Gpki.alert("CERT_EXPIRED");
            } else if (statusCode == "41") {
                Gpki.alert("CERT_REVOKED");
            } else if (statusCode == "43") {
                Gpki.alert("CERT_NOT_YET_VALID");
            } else {
                Gpki.alert(statusMsg);
            }

            $('#gpki-form').show();
            $('.gpki-load').hide();
            document.getElementById('pin').value = '';
            document.getElementById('textToSign').value = '';
        });
    }
</script>
@endsection
