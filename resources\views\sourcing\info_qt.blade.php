@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="carian_qt_form" action="{{url('find/qt/info')}}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
                   placeholder="Klik carian di sini ...">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')
    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Detail Quotation Tender <br>
                <small></small>
            </h1>
        </div>
    </div>
    @if($qtinfo == null)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tidak dijumpai!</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($qtinfo)
        <div class="block block-alt-noborder full">
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Quotation/Tender : {{ $qtinfo->qt_no }}</strong></h1>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <h6><strong>{{ $qtinfo->qt_title }}</strong></h6>
                    </div>    
                    <div class="col-md-6">
                        <address>
                            <strong>QT ID</strong> : {{ $qtinfo->qt_id }}<br />
                            <strong>Procurement</strong> : {{ $qtinfo->procurement }}<br />
                            <strong>Status</strong> : {{ $qtinfo->status_id }} - {{ $qtinfo->status_name }}<br />
                            <strong>Procurement Category</strong> : {{ $qtinfo->procurement_category }}<br />
                            <strong>Procurement Type Category</strong> : {{ $qtinfo->procurement_type_category }}<br />
                            <strong>Evaluation Type</strong> : {{ $qtinfo->evaluation_type }} Tiers<br />
                            <strong>Publish Date</strong> : {{ $qtinfo->publish_date }}<br />
                            <strong>Closing Date</strong> : {{ $qtinfo->closing_date }}<br />
                            <strong>Proposal Validity End Date</strong> : {{ $qtinfo->proposal_validity_end_date }}<br />
                            <strong>BSV</strong> : {{ $qtinfo->bsv }}<br />
                            <strong>State</strong> : {{ $qtinfo->state_name }}<br />
                        </address>
                    </div>
                    <div class="col-md-6">
                        <address>
                            <strong>Created By </strong> : {{ $qtinfo->login_id }} - {{ $qtinfo->user_name }}<br />
                            <strong>Ministry</strong> : {{ $qtinfo->ministry_name }} - {{ $qtinfo->ministry_code }}<br />
                            <strong>Pegawai Pengawal</strong> : {{ $qtinfo->pp_code }} - {{ $qtinfo->pp_name }}<br />
                            <strong>Kumpulan PTJ</strong> : {{ $qtinfo->kptj_code }} - {{ $qtinfo->kptj_name }}<br />
                            <strong>PTJ</strong> : {{ $qtinfo->ptj_code }} - {{ $qtinfo->ptj_name }}<br />
                        </address>
                    </div>
                </div>
            </div>
        </div>
    @endif

@endsection

@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection