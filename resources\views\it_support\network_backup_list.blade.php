@extends('layouts.guest-dash')

@section('content')
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/it_support/network/backup/checklist') }}"><i class="fa fa-tasks"></i>Check
                    List</a>
            </li>
            <li>
                <a href="{{ url('/it_support/network/backup/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                    History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/network/backup/data_lookup') }}"><i class="fa fa-cubes"></i>Data
                    Lookup</a>
            </li>
        </ul>
    </div>

    <div class="block">
        <form id="network_checklist" action="{{ url('/it_support/network/backup/checklist') }}" method="post">
            {{ csrf_field() }}

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            <div class="col-md-2 date hide">
                <input id="date_search" name="date_search" type="date" value="{{ isset($dateNote) ? $dateNote : '' }}"
                    class="form-control">
            </div>

            <div id="customDiv">
                <label class="col-md-2 text-center">Date : @if (isset($dateNote))
                        {{ $dateNote }}
                    @endif
                </label>
                <div class="col-md-11">NETWORK BACKUP CHECKLIST </div>
                <label class="col-md-2 text-center"></label>
            </div>

            <div class="table-responsive">
                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">No</th>
                            <th class="text-center">Device</th>
                            <th class="text-center">Description</th>
                            <th class="text-center">Hostname</th>
                            <th class="text-center">IP Address</th>
                            <th class="text-center">Location</th>
                            <th class="text-center">Ok <input type='checkbox' id='selectall' onchange='selectAll(this)'></th>
                            <th class="text-center">Not Ok</th>
                            <th class="text-center">Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($listTask != null)
                            @foreach ($listTask as $rowData => $data)
                                <tr>
                                    <td class="text-center">{{ ++$rowData }}</td>
                                    <td class="text-center">{{ $data->nbl_device }}</td>
                                    <td class="text-center">{{ $data->nbl_description }}</td>
                                    <td class="text-center">{{ $data->nbl_hostname }}</td>
                                    <td class="text-center">{{ $data->nbl_ip_address }}</td>
                                    <td class="text-center">{{ $data->nbl_location }}</td>
                                    <td class="text-center">
                                        <input type="radio" id="radio_for_status_okay" name="{{ $data->nbld_list_id }}"
                                            value="Y" @if ($data->nbld_checked_ok == 'Y') checked @endif>
                                    </td>
                                    <td class="text-center">
                                        <input type="radio" id="radio_for_status_failed" name="{{ $data->nbld_list_id }}"
                                            value="N" @if ($data->nbld_checked_not_ok == 'N') checked @endif>
                                    </td>
                                    <td class="text-full">
                                        <textarea name="remarks_{{ $data->nbld_list_id }}" style="width: 100%; height: 100%;">{{ $data->nbld_remarks }}</textarea>
                                    </td>
                                </tr>
                            @endforeach
                        @endif
                    </tbody>
                </table>
            </div>

            <div class="table-responsive">
                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
            </div>
            @if (isset($listdata) == false ||
                    $listdata[0] == null ||
                    (isset($listdata) &&
                        $listdata[0] &&
                        ($listdata[0]->ack_status != 'Pending Endorsement' && $listdata[0]->ack_status != 'Completed')))
                <div class="text-center">
                    <button type="submit" style="background-color: #414770; color: white"
                        class="btn btn btn-primary editSave">Update</button>
                </div>
            @endif
        </form>
    </div>

    <div class="block panel-heading">@include('it_support.page_status', ['page' => 'backup_c'])</div>
@endsection
@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        var customDiv = document.getElementById('customDiv');
        customDiv.style.width = '93vw'; // Set the width
        customDiv.style.height = '50px'; // Set the height
        customDiv.style.backgroundColor = '#414770'; // Set the background color
        customDiv.style.color = 'white';
        customDiv.style.textAlign = 'center';
        customDiv.style.fontSize = '16px';
        customDiv.style.display = 'flex';
        customDiv.style.alignItems = 'center';
    </script>
    <script>
        function selectAll(ele) {
            var checkboxes = document.querySelectorAll('#radio_for_status_okay');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        $(document).ready(function() {
            $('#list_datatable').dataTable({
                order: [
                    [0, "asc"]
                ],
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
        });

        $(document).ready(function() {
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("");
            }
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {} else {
                $('#remarks').val("{{ $listNote[0]->ack_remarks ?? '' }}");
            }
            var checkDate = "{{ $listNote[0]->ack_check_date ?? '' }}";
            if (!checkDate) {
                checkDate = new Date().toISOString().split('T')[0];
            }
            $('#check_date').val(checkDate);

            var ackStatus = "{{ $listNote[0]->ack_status ?? '' }}";
            if (ackStatus === 'Pending Endorsement' || ackStatus === 'Completed') {
                $('#ack_date').val(new Date().toISOString().split('T')[0]);
                $('.editSave').hide();
                $('input[type="radio"]').prop('disabled', true);
            } else {
                $('.editSave').show();

            }

            var dateNote = "{{ $dateNote }}";
            $('#selected_date').val(dateNote);



        });
    </script>
@endsection
