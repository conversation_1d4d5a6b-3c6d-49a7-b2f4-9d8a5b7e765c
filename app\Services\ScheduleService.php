<?php

namespace App\Services;

use Illuminate\Console\Scheduling\Schedule;
use Cron\CronExpression;

class ScheduleService {

    public function getCommands() {
        $schedule = app(Schedule::class);

        //$this->registerCommands($schedule);

        $scheduledCommands = collect($schedule->events())
                ->map(function ($event) {
            $expression = CronExpression::factory($event->expression);
            //dump($event);
            //exit();
            return [
                'command' => $event->command,
                'expression' => $event->expression,
                'next-execution' => $expression->getNextRunDate()
            ];
        });

        return $scheduledCommands;
    }

}
