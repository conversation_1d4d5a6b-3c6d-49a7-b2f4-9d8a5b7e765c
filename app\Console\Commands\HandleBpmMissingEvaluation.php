<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\Bpm\QtMissingEvaluationTask;
use Log;
use Config;
use SSH;
use App\Migrate\MigrateUtils;


class HandleBpmMissingEvaluation extends Command {
    
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'qt-bpm-missing-evaluation';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'To clear qt stuck list with tracking status Pending Committee but workflow status QT close';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting .. '.$this->description, [
            'Date' => Carbon::now()]);
        
        try {
            MigrateUtils::logDump(self::class .' > '. __METHOD__ . ' Starting....');
            QtMissingEvaluationTask::run(); 
            
        } catch (\Exception $exc) {

            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getMessage());
            MigrateUtils::logErrorDump(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
        
    }

}
