<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\SupplierFullGrantService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\OSBService;
use App\Services\Traits\SSHService;
use App\Services\Traits\ContractService;
use App\Services\Traits\EpWebService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Validator;
use App\EpSupportActionLog;
use App\Services\EPService;
use App\Services\EPTableService;
use DB;
use Exception;
use Log;
use App\Migrate\SmRollbackSiteVisitToApproval;


/**
 * This controller class will update into DB eP. Please be careful
 */
class ActionEpController extends Controller {

    use SupplierService;
    use ProfileService;
    use SupplierFullGrantService;
    use OSBService;
    use ContractService;
    use SSHService;
    use EpWebService;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    
    public function revertSiteVisitToApproval(){
        $applNo = request()->applNo;
        $isSkipTerminateTask = request()->isSkipTerminateTask;
        SmRollbackSiteVisitToApproval::runRollbackSiteVisitToApprover($applNo,$isSkipTerminateTask);
        dump("COMPLETED");
    }

    public function patchTableEp(){
        $patchName = request()->patch;
        $recordId = request()->record;
        return view('patch.ep_table_patch', [
            'data' => null,
            'selected_process_table' => $patchName,
            'record_id' => $recordId,
                ]);
    }

    /**
     * This update table will follow rules on EPTableService.
     * Each update executaion table will update one field only.
     *
     * @return type
     */
    public function updatePatchTableEp(){

        /**
         * Check Input from OUTSIDE, WHEN update must use checking data from EPTableService as rules.
         */
        $validator  = Validator::make(request()->all(), [
                'selected_process_table' => 'required',
                'selected_field' => 'required',
                'field_value' => 'required',
                "record_id" => "required",
                "remarks" => "required"
            ]);
        if($validator->fails()){
            return $validator->errors();
        }

        $tableSelected = request()->selected_process_table;
        //dump(request()->all());

        $tableInfo = EPTableService::$LIST_TABLE[$tableSelected];

        $databaseConnection = $tableInfo['database_connection'];
        $tableName = $tableInfo['table_name'];
        $primaryField = $tableInfo['primary_field'];
        $requiredAddChangedDate = $tableInfo['required_changed_date'];

        $addWheres = null;
        if(isset($tableInfo['where_add']) ) { $addWheres = $tableInfo['where_add']; }  // some table we put another where clause to add more filter 
        
        $selectedField = request()->selected_field;
        $valueField = request()->field_value;
        $primaryValue = request()->record_id;




        /**Start to do validation based on rules. **/
        $fieldSelectedInfo = EPTableService::$LIST_TABLE[$tableSelected]['field_allow'][$selectedField];
        $validationType = $fieldSelectedInfo['validation_type'];
        $validationLength = $fieldSelectedInfo['validation_length'];
        $validationValue = $fieldSelectedInfo['validation_value'];
        //dump($fieldSelectedInfo);

        $validationFilterValueInputDesc = null;
        $isValid = false;

        if($validationType == "integer" && is_numeric($valueField) && strlen($valueField) <= $validationLength){

            //Filter value follow based on selection rules
            if(count($validationValue) > 0 && in_array($valueField, $validationValue ) ){
                $isValid = true;
            }

            //Skip filter based on value filtering
            if(count($validationValue) == 0 ){
                $isValid = true;
            }
            
            if(count($validationValue) == 1 && in_array('null', $validationValue ) ){
                $isValid = true;
            }    
            

        }
        else if($validationType == "integer" && strlen($valueField) == 4 && $valueField=='null' 
                &&  in_array($valueField, $validationValue,true )){
           
            //This to set field required integer , but some how need update value as NULL
            $isValid = true;
        }
        else{
            $validationFilterValueInputDesc = "Invalid Value. Please follow the rules";
        }

        if($validationType == "string"  && strlen($valueField) <= $validationLength
                && mb_check_encoding($valueField, 'ASCII')){
            //Filter value follow based on selection rules
            if(count($validationValue) > 0 && in_array($valueField, $validationValue ) ){
                $isValid = true;
            }

            //Skip filter based on value filtering
            if(count($validationValue) == 0 ){
                $isValid = true;
            }

        }else{
            $validationFilterValueInputDesc = "Invalid Value. Please follow the rules";
        }

        if($validationType == "null"  && strlen($valueField) == $validationLength
                && mb_check_encoding($valueField, 'ASCII')){
            //Filter value follow based on selection rules
            if(count($validationValue) > 0 && in_array($valueField, $validationValue ) ){
                $isValid = true;
            }

            //Skip filter based on value filtering
            if(count($validationValue) == 0 ){
                $isValid = true;
            }

        }else{
            $validationFilterValueInputDesc = "Invalid Value. Please follow the rules";
        }

        if($validationType == "sysdate"  && strlen($valueField) == $validationLength
                && mb_check_encoding($valueField, 'ASCII')){
            //Filter value follow based on selection rules
            if(count($validationValue) > 0 && in_array($valueField, $validationValue ) ){
                $isValid = true;
            }

        }else{
            $validationFilterValueInputDesc = "Invalid Value. Please follow the rules";
        }

        if($validationType == "datetime"  && strlen($valueField) == $validationLength
                && mb_check_encoding($valueField, 'ASCII')){
            
            try{
                Carbon::parse($valueField);
                $isValid = true;
            }catch(Exception $e){
                Log::error('ERROR : '.$e->getMessage());
                $validationFilterValueInputDesc =  $e->getMessage();
            }
        }else{
            $validationFilterValueInputDesc = "Invalid Value. Please follow the rules";
        }

        $isSuccessSave = false;
        if($isValid == true){

            //** JUST FOR SAMPLE SHOW*/
            //$script = "UPDATE $tableName SET $selectedField = '$valueField' WHERE  $primaryField = '$primaryValue' ";
            //dump($script);


            $logs = collect([]);

            /* Set as default script update  */
            $updateScriptFields = [$selectedField => $valueField];

            if($valueField === 'null'){
               $updateScriptFields = [$selectedField => null]; 
            }
            
            /* Overwrite script for validationType is NULL  */
            if($validationType === 'null'){
               $updateScriptFields = [$selectedField => null];
            }

            /* Overwrite script for validationType is NULL  */
            if($validationType === 'sysdate' && $valueField === 'sysdate'){
                $updateScriptFields = [$selectedField => Carbon::now()];
            }

            /* Overwrite script for validationType is datetime  */
            if($validationType === 'datetime'){
                $updateScriptFields = [$selectedField => Carbon::parse($valueField)];
            }
            

            if($requiredAddChangedDate == true){
                $updateScriptFields['changed_date'] =  Carbon::now();
                if($selectedField != 'changed_by'){
                    $updateScriptFields['changed_by'] =  1;
                }
            }
            
            //If failed, it will rollback
            DB::connection($databaseConnection)->transaction(function() use ($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields,&$logs,&$isSuccessSave,$addWheres) {

                $this->saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName, $primaryField, $primaryValue, $updateScriptFields, $logs,$addWheres);
                $isSuccessSave = true;

            });

            $actionTypeLog = 'Script';
            $actionName = 'PatchDataTable';

            $parameters =  collect([]);
            $parameters->put("remarks", request()->remarks);
            $parameters->put("table", $tableName);
            $parameters->put("reference_id", array($primaryField => $primaryValue));
            $parameters->put("update_data", $updateScriptFields);
            $parameters->put("patching", $tableName);

            $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
            dump($actionLog->attributesToArray());

        }

        if($isSuccessSave === true){
            $resultStatus = 'success';
            $resultDesc = "Kemaskini maklumat TABLE > $tableName bagi Medan > $selectedField  berjaya disimpan.";
        }else{
            $resultStatus = 'failed';
            $resultDesc = "Kemaskini maklumat TABLE > $tableName bagi Medan > $selectedField  gagal disimpan. $validationFilterValueInputDesc. ";
            dump($fieldSelectedInfo);
        }

        return view('patch.ep_table_patch', [
            'data' => null,
            'selected_process_table' => $tableSelected,
            'record_id' => $primaryValue,
            'result_status' => $resultStatus,
            'result_desc'   => $resultDesc,
                ]);
    }

    public function getDetailRecordByTable(){
        $validator  = Validator::make(request()->all(), [
                'table_name' => 'required',
                'record_field' => 'required',
                "record_id" => "required"
            ]);
        if($validator->fails()){
            return $validator->errors();
        }
        $tableNameReq = request()->table_name;
        //$field = request()->record_field;  // Ignored.. Preventive by FORM BruteForce.
        $recordId = request()->record_id;

        try {
            $tableInfo = EPTableService::$LIST_TABLE[$tableNameReq];
            $databaseConnection = $tableInfo['database_connection'];
            $tableName = $tableInfo['table_name'];
            $primaryField = $tableInfo['primary_field'];

            $objResult = $this->getDetailsByTable($databaseConnection,$tableName, $primaryField, $recordId);

            return collect($objResult);
        } catch (Exception $e) {
           Log::error('getDetailRecordByTable ', ['table_name' => $tableNameReq, 'record_field' => request()->record_field,'record_id' => $recordId, 'ERROR' => $e->getMessage()]);
        }
       
        return collect([]);
    }

    public function deleteDetailRecordByTable(){
        $data = collect([]);
        $validator  = Validator::make(request()->all(), [
                'table_name' => 'required',
                "record_id" => "required"
            ]);
        if($validator->fails()){
            $data->put('status','failed');
            $data->put('data',$validator->errors());
        }
        $tableNameReq = request()->table_name;
        $recordId = request()->record_id;

        try {
            $tableInfo = EPTableService::$LIST_TABLE[$tableNameReq];
            $databaseConnection = $tableInfo['database_connection'];
            $tableName = $tableInfo['table_name'];
            $primaryField = $tableInfo['primary_field'];

            $isAllowDelete = false;
            if(isset($tableInfo['is_allow_delete']) === true){
                $isAllowDelete = $tableInfo['is_allow_delete'];
            }
            
            if($isAllowDelete === true){
                $logQuery = $this->deleteDetailsByTable($databaseConnection,$tableName, $primaryField,$recordId);
                $data->put('status',$logQuery['status']);
                $data->put('data',json_encode($logQuery));
            }else{  
                $errorMsg = __METHOD__.json_encode(
                    [   
                        'error' => 'This table not allow to delete record',
                        'table_name' => $tableNameReq, 
                        'record_id' => $recordId, 
                        ]);
                $data->put('status','failed');
                $data->put('data',$errorMsg);
            }
        } catch (Exception $e) {
            $errorMsg = __METHOD__.json_encode(
                [   
                    'error' => $e->getMessage(),
                    'table_name' => $tableNameReq, 
                    'record_id' => $recordId, 
                    ]);
            Log::error($errorMsg);
            $data->put('status','failed');
            $data->put('data',$errorMsg);
        }
        
        $actionTypeLog = 'Script';
        $actionName = 'PatchDataTable';

        $parameters =  collect([]);
        $parameters->put("remarks", 'To delete record');
        $parameters->put("table", $tableNameReq);
        $parameters->put("reference_id", $recordId);

        $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$data,$parameters,'Completed');
        Log::info(__METHOD__.' '.json_encode($actionLog));
        return $data;
    }

    public function saveTransactionUpdateRecordDynamicTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,&$logs,$addWheres = null){
        $logQuery = $this->updateRecordByTable($databaseConnection,$tableName,$primaryField,$primaryValue,$updateScriptFields,$addWheres);
        $logs->put('action_patch_update_table',$logQuery);
    }

    public function searchUserPersonnelDetails($applId,$personnelId){
        $personnelDetails = null;
        $personnelDetailsInProgress = null;
        $usersInfo = null;
        if($applId == null || $personnelId == null){
            return view('patch.user_personnel_patch', [
                'data' => $personnelDetails
                    ]);
        }else{
            $personnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            
            if($personnelDetails){
                $this->populatePersonnelUserData($personnelDetails);
                
                $usersInfo = $this->getListSMSupplierUserDetailsBySpecificUser($personnelDetails->supplier_id, $personnelDetails->user_id);

                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant($personnelDetails->supplier_id,$personnelDetails->p_identification_no);
                if(count($listinProgressSuppProcessAppl) > 0){
                  $inAppl = $listinProgressSuppProcessAppl[0];
                  if($inAppl){
                    $personnelDetailsInProgress = $this->getSMSupplierUsersDetailsInProgressApplByPersonnel($inAppl->appl_id, $inAppl->personnel_id);
                    $this->populatePersonnelUserData($personnelDetailsInProgress);
                  }
                }
                
                $listDataSoftcertRequestTG = $this->getListSMSoftcertRequestBySoftcertProvider($personnelDetails->supplier_id, $personnelDetails->user_id, 'TG');
            }

        }
        //dump($personnelDetailsInProgress);
        //return json_encode($personnelDetails);
        return view('patch.user_personnel_patch', [
            'data' => $personnelDetails,
            'data_inprogress' => $personnelDetailsInProgress,
            'data_softcert_tg' => $listDataSoftcertRequestTG,
            'data_user' => $usersInfo,
                ]);
    }

    public function updateUserPersonnelDetails($applId,$personnelId){
        $resultStatus = null;
        $resultDesc = null;
        $isSuccessSave = false;
        $patchingProcess = '';

        if($applId == null || $personnelId == null){
            return view('patch.user_personnel_patch', [
                'data' => null,
                    ]);
        }else{
            Validator::make(request()->all(), [
                'selected_process_id' => 'required',
                'remarks' => 'required',
                "selected_user_role" => "required_if:selected_process_id,==,user_set_active",
                "identification_no" => "required_if:selected_process_id,==,change_identification_no",
                "full_name" => "required_if:selected_process_id,==,change_fullname",
                "selected_softcert_request_tg_id" => "required_if:selected_process_id,==,request_softcert_dg",
            ])->validate();

            $personnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant(
                    $personnelDetails->supplier_id,$personnelDetails->p_identification_no);

            //$usersInfo = $this->getListSMSupplierUserDetailsBySpecificUser($personnelDetails->supplier_id, $personnelDetails->user_id);

            $processId = request()->selected_process_id;
            $actionTypeLog = 'Script';
            $actionName = 'PatchData';
            $logs = collect([]);

            $parameters =  collect([]);
            $parameters->put("remarks", request()->remarks);
            switch ($processId) {
                case "user_set_active":

                    $dataUserIDs = explode("_",request()->selected_user_role);
                    $userOrgId = $dataUserIDs[0];
                    $userRoleId = $dataUserIDs[1];
                    $userId = $personnelDetails->user_id;

                    if($userOrgId != null && $userRoleId != null &&
                            $personnelDetails->u_record_status == 0
                            || $personnelDetails->u_record_status == 9){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;

                        $updateFields = ['record_status' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$userId,$userOrgId,$userRoleId,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionPmUser($updateFields, $userId, $logs);
                            $this->saveTransactionPmUserOrg($updateFields, $userOrgId, $logs);
                            $this->saveTransactionPmUserRole($updateFields, $userRoleId, $logs);

                            $userRole = $query =  DB::connection('oracle_nextgen_fullgrant')
                                        ->table('PM_USER_ROLE')
                                        ->where('user_role_id', $userRoleId)
                                        ->first();

                            $updatePersonnelFields = ['ep_role' => $userRole->role_code,'record_status' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                            $this->saveTransactionSmPersonnel($updatePersonnelFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "user_set_inactive":

                    $userId = $personnelDetails->user_id;

                    if($userId != null &&
                            $personnelDetails->u_record_status == 1 ){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;

                        $updateFields = ['record_status' => 0,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$userId,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionPmUser($updateFields, $userId, $logs);

                            $userOrg = $query =  DB::connection('oracle_nextgen_fullgrant')
                                        ->table('PM_USER_ORG')
                                        ->where('user_id', $userId)
                                        ->where('record_status',1)
                                        ->first();
                            if($userOrg){
                                $this->saveTransactionPmUserOrg($updateFields, $userOrg->user_org_id, $logs);

                                $userRoles = $query =  DB::connection('oracle_nextgen_fullgrant')
                                        ->table('PM_USER_ROLE')
                                        ->where('user_org_id', $userOrg->user_org_id)
                                        ->where('record_status',1)
                                        ->get();
                                foreach ($userRoles as $role){
                                   $this->saveTransactionPmUserRole($updateFields, $role->user_role_id, $logs);
                                }

                            }
                            $updatePersonnelFields = ['ep_role' => null,'changed_date' => Carbon::now(),'changed_by' => 1];
                            $this->saveTransactionSmPersonnel($updatePersonnelFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "change_identification_no":

                    $identificationNo = request()->identification_no;
                    $checkIcnoIsNotExisted = $this->totalIdentificationNoSmPersonnel($applId, $identificationNo);

                    if($identificationNo != null && strlen($identificationNo) == 12
                            && $checkIcnoIsNotExisted == 0){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;
                        $userId = $personnelDetails->user_id; //If not null will updated

                        $updateFields = ['identification_no' => $identificationNo,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$userId,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {
                            if($userId != null && $userId > 0){
                                $this->saveTransactionPmUser($updateFields, $userId, $logs);
                            }

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "change_fullname":

                    $fullName = strtoupper(request()->full_name);

                    if($fullName != null && strlen($fullName) > 6){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;
                        $userId = $personnelDetails->user_id; //If not null will updated

                        $updateFields = ['name' => $fullName,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$fullName,$userId,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {
                            if($userId != null && $userId > 0){
                                $updateFieldsPMUser = ['user_name' => $fullName,'changed_date' => Carbon::now(),'changed_by' => 1];
                                $this->saveTransactionPmUser($updateFieldsPMUser, $userId, $logs);
                            }

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_softcert_4":
                    if($personnelDetails->p_is_softcert == 0
                            || $personnelDetails->p_is_softcert == 1
                            || $personnelDetails->p_is_softcert == 2
                            || $personnelDetails->p_is_softcert == 3
                            || $personnelDetails->p_is_softcert == 7){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_softcert' => 4,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,  $personnelDetails,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {


                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_softcert_1":
                    if($personnelDetails->p_is_softcert == 4
                            || $personnelDetails->p_is_softcert == 3
                            || $personnelDetails->p_is_softcert == 6
                            || $personnelDetails->p_is_softcert == 0
                            || $personnelDetails->p_is_softcert == 7){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_softcert' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_is_authorized_1":

                    $countAuthorized = $this->totalAuthorizedUserSupplier($applId);
                    if($personnelDetails->ep_role != null
                            && $personnelDetails->ep_role !=  'CM_USER'
                            && $personnelDetails->ep_role !=  'FL_USER'
                            && $personnelDetails->p_record_status == 1 
                            && $personnelDetails->p_is_softcert == 1
                            || $countAuthorized < 10){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_authorized' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_is_contract_signer_1":

                    $countContractSigner = $this->totalContractSignerUserSupplier($applId);
                    if($personnelDetails->ep_role != null 
                            && $personnelDetails->ep_role !=  'CM_USER'
                            && $personnelDetails->ep_role !=  'FL_USER'
                            && $personnelDetails->p_record_status == 1 
                            && $personnelDetails->p_is_softcert == 1
                            || $countContractSigner < 10){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_contract_signer' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_1":
                    if($personnelDetails->p_record_status == 0
                            || $personnelDetails->p_record_status == 9 ){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;

                        $updateFields = ['record_status' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_9":
                    if($personnelDetails->p_record_status == 0
                            || $personnelDetails->p_record_status == 1 ){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;

                        $updateFields = ['record_status' => 9,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId, $supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "personnel_reset_activation":
                    //this for activation user to create login ID
                    if($personnelDetails->p_record_status == 8
                            || $personnelDetails->p_user_id == null ){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];


                        $updateFields = ['record_status' => 1,'email' => null,'ep_role' => null,'user_id' => null,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;  
                case "request_softcert_dg":
                    $softcertRequestDGId = request()->selected_softcert_request_tg_id;
                    //This is temporary solution cause of user apply renewal to TG softcert but error. So we change it softcert request from TG to DG then send to DG portal.
                    if($softcertRequestDGId != null ){
                        $checkIsTG = $this->checkSoftcertRequestTG($softcertRequestDGId);
                        
                        if($checkIsTG == 1){
                            $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                            $softcertProvider = 'DG';
                            $updateFields = ['softcert_provider' => $softcertProvider];
                            $parameters->put("patching", $patchingProcess);

                            DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$softcertRequestDGId,&$logs,&$isSuccessSave) {

                                $this->saveTransactionSmSoftcertRequestSingleRecord($updateFields, $softcertRequestDGId, $logs);

                                $isSuccessSave = true;

                            });
                            if($isSuccessSave == true){
                                // call WS send request certicate to provider
                                $this->wsSendRequestSoftcertToProvider($personnelId, $softcertRequestDGId, $softcertProvider);
                            }
                            EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                        }
                        

                    }
                    break; 
                case "trim_identification_no":
                    
                    $icNo = $personnelDetails->p_identification_no;
                    $icNoAfterTrim = trim($icNo);
                    
                    if(strlen($icNo) > strlen($icNoAfterTrim) ){
                        $patchingProcess = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $supplierId = $personnelDetails->supplier_id;
                        $userId = $personnelDetails->user_id; //If not null will updated

                        $updateFields = ['identification_no' => $icNoAfterTrim,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$userId,$personnelId,$supplierId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {
                            if($userId != null && $userId > 0){
                                $this->saveTransactionPmUser($updateFields, $userId, $logs);
                            }

                            $this->saveTransactionSmPersonnel($updateFields, $personnelId, $listinProgressSuppProcessAppl,$logs);

                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;    
                default:
                    dd('Process Not found');
            }

            $latestPersonnelDetails = $this->getSMSupplierUsersDetailsByPersonnel($applId, $personnelId);
            $this->populatePersonnelUserData($latestPersonnelDetails);

            $latestPersonnelDetailsInProgress = null;
            $latestListinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrant($latestPersonnelDetails->supplier_id,$latestPersonnelDetails->p_identification_no);
            if(count($latestListinProgressSuppProcessAppl) > 0){
              $latestInAppl = $latestListinProgressSuppProcessAppl[0];
              if($latestInAppl){
                $latestPersonnelDetailsInProgress = $this->getSMSupplierUsersDetailsInProgressApplByPersonnel($latestInAppl->appl_id, $latestInAppl->personnel_id);
                $this->populatePersonnelUserData($latestPersonnelDetailsInProgress);
              }
            }
            
            $latestUsersInfo = $this->getListSMSupplierUserDetailsBySpecificUser($personnelDetails->supplier_id, $personnelDetails->user_id);
            
            $listDataSoftcertRequestTG = $this->getListSMSoftcertRequestBySoftcertProvider($latestPersonnelDetails->supplier_id, $latestPersonnelDetails->user_id, 'TG');
            
            if($isSuccessSave == true){
                $resultStatus = 'success';
                $resultDesc = "Kemaskini maklumat personnel > $patchingProcess  berjaya disimpan.";
            }else{
                $resultStatus = 'failed';
                $rules = EPService::$USER_SUPPLIER_PROCESS_PATCHING[$processId]['description'];
                $resultDesc = "Kemaskini maklumat personnel > $patchingProcess  gagal disimpan. ".$rules;
            }
        }

        session()->flashInput(request()->input());

        return view('patch.user_personnel_patch', [
                'data' => $latestPersonnelDetails,
                'data_inprogress' => $latestPersonnelDetailsInProgress,
                'data_user' => $latestUsersInfo,
                'data_softcert_tg' => $listDataSoftcertRequestTG,
                'result_status' => $resultStatus,
                'result_desc'   => $resultDesc,
            ]);
    }

    /**
     * Generic Update/Data Patch Personnel Table only. include also in progress application
     * @param type $isSuccessSave
     * @param type $updateFields
     * @param type $personnelId
     * @param type $listinProgressSuppProcessAppl
     * @param type $logs
     * @return type
     */
    public function saveTransactionSmPersonnel($updateFields,$personnelId,$listinProgressSuppProcessAppl,&$logs){

        //Update main pm_softcert as APPL ACTIVE
        $logQuery = $this->updateSMPersonnel($personnelId,$updateFields);
        $logs->put('action_sm_personnel_active_appl',$logQuery);

        /** If got another data personnel in current Supplier Progress Application **/
        if(count($listinProgressSuppProcessAppl) > 0){
            $inAppl = $listinProgressSuppProcessAppl[0];
            $logQueryInProg = $this->updateSMPersonnel($inAppl->personnel_id,$updateFields);
            $logs->put('action_sm_personnel_in_progress_appl',$logQueryInProg);
        }
    }
    
    /**
     * Generic Update/Data Patch Personnel Table only.
     * @param type $updateFields
     * @param type $personnelId
     * @param type $logs
     */
    public function saveTransactionSmPersonnelOnly($updateFields,$personnelId,&$logs){
        $logQuery = $this->updateSMPersonnel($personnelId,$updateFields);
        $logs->put('action_sm_personnel',$logQuery);
    }

    public function saveTransactionSmSoftcertRequest($updateFields,$supplierId,$userId,&$logs){

        $softcertRequest = $this->getSMSoftcertRequestWihoutCert($supplierId, $userId);
        if($softcertRequest){
           $logQuery = $this->updateSMSoftcertRequest($softcertRequest->softcert_request_id, $updateFields);
           $logs->put('action_sm_softcert_request',$logQuery);
        }

    }
    
    public function saveTransactionSmSoftcertRequestSingleRecord($updateFields,$softcertRequestId,&$logs){

        $logQuery = $this->updateSMSoftcertRequest($softcertRequestId, $updateFields);
        $logs->put('action_sm_softcert_request',$logQuery);
       
    }

    public function saveTransactionMminfDetails(&$isSuccessSave,$updateFields,$mminfId,&$logs){
        //If failed, it will rollback
        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use (&$isSuccessSave,$updateFields,$mminfId,&$logs) {

            $logs = $this->updateMminfIDDetails($mminfId,$updateFields);
          //  dd($updateFields);
            $isSuccessSave = true;
        });
        return $isSuccessSave;
    }

    public function deleteTransactionMminfDetails(&$isSuccessSave,$mminfId,&$logs){
        //If failed, it will rollback
        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use (&$isSuccessSave,$mminfId,&$logs) {
            $logs = $this->deleteDIMminf($mminfId);
            $isSuccessSave = true;
        });
        return $isSuccessSave;
    }

    public function searchSupplierDetails($applId,$supplierId){
        $supplierDetails = null;
        $supplierDetailsInProgress = null;
        $supplierUsers = null;
        $inAppl = null;
        if($applId == null || $supplierId == null ){
            return view('patch.supplier_patch', [
                'data' => $supplierDetails
                    ]);
        }else{
            $supplierDetails = $this->getSMSupplierDetails($applId,$supplierId);
            $supplierUsers = $this->getListSMSupplierPMUser($supplierId);
            if($supplierDetails){

                $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrantBySupplierId($supplierDetails->supplier_id);
                if(count($listinProgressSuppProcessAppl) > 0){
                  $inAppl = $listinProgressSuppProcessAppl[0];
                  if($inAppl){
                    $supplierDetailsInProgress = $this->getSMSupplierDetails($inAppl->appl_id, $inAppl->supplier_id);
                  }
                }
            }

        }

        $data = [
            'data' => $supplierDetails,
            'data_inprogress' => $supplierDetailsInProgress,
            'appl_inprogress' => $inAppl,
            'listUsers' => $supplierUsers
            ];
        return view('patch.supplier_patch', $data);
    }

    public function updateSupplierDetails($applId,$supplierId){
        $resultStatus = null;
        $resultDesc = null;
        $isSuccessSave = false;
        $patchingProcess = '';

        if($applId == null || $supplierId == null){
            return view('patch.supplier_patch', [
                'data' => null,
                    ]);
        }else{
            Validator::make(request()->all(), [
                'selected_process_id' => 'required',
                'remarks' => 'required',
                "selected_business_type" => "required_if:selected_process_id,==,supplier_business_type",
                "ssm_no" => "required_if:selected_process_id,==,supplier_ssm_no",
                "company_name" => "required_if:selected_process_id,==,supplier_company_name",
                "selected_inprogress_appl" => "required_if:selected_process_id,==,cancel_appl_inprogress",
                "selected_history_appl" => "required_if:selected_process_id,==,setcancel_appl_history",

            ])->validate();

            $supplierDetails = $this->getSMSupplierDetails($applId,$supplierId);
            $supplierUsers = $this->getListSMSupplierPMUser($supplierId);
            $listinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrantBySupplierId($supplierDetails->supplier_id);


            $processId = request()->selected_process_id;
            $actionTypeLog = 'Script';
            $actionName = 'PatchDataSupplier';
            $logs = collect([]);

            $parameters =  collect([]);
            $parameters->put("remarks", request()->remarks);

            switch ($processId) {
                case "supplier_9":
                    //  $APPL_TYPE  ->  'O' => 'Application for Basic Online Account', 'N' => 'New Application for MOF',
                    $supplierMof= $this->getMOFDetails($supplierId,$supplierDetails->supplier_type); //Checking exp more than 365
                    if( ($supplierDetails->record_status == 0 || $supplierDetails->record_status == 1 || $supplierDetails->record_status == 8 ) &&
                            ( $supplierDetails->appl_type == 'O'
                                || $supplierDetails->appl_type == 'N' 
                                || $supplierDetails->appl_type == 'C'
                                || ($supplierMof != null && Carbon::now()->diffInDays(Carbon::parse($supplierMof->exp_date)) > 365)
                            )
                        )  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['record_status' => 9,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER and PM_USER (IF RELATED)
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,
                                $supplierUsers,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            if(count($supplierUsers) > 0){
                                foreach ($supplierUsers as $user){
                                   $this->saveTransactionPmUser($updateFields, $user->user_id, $logs);
                                }
                            }
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "supplier_2":
                    //Set Cancellation to Company in progress application
                    if($supplierDetails->record_status == 5 )  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['record_status' => 2,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER and PM_USER (IF RELATED)
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,
                                $supplierUsers,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            if(count($supplierUsers) > 0){
                                foreach ($supplierUsers as $user){
                                   $updateFieldsUser = ['record_status' => 9,'changed_date' => Carbon::now(),'changed_by' => 1];
                                   $this->saveTransactionPmUser($updateFieldsUser, $user->user_id, $logs);
                                }
                            }
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "supplier_1":
                    /** 1) We allow for migrated supplier or supplier has  MOF 
                        2) We allow for supplier first register KN **/
                    //Checking duplicate SSM
                    $isExistSSM= $this->getSMSupplierDetailByRegNoAndSupplierType($supplierDetails->reg_no, $supplierDetails->supplier_type);
                    $supplierMof= $this->getMOFDetails($supplierId,$supplierDetails->supplier_type); //Checking exp more than 365
                    if( $isExistSSM == false && 
                            (($supplierDetails->record_status == 7 || $supplierDetails->record_status == 5 ) && ($supplierMof != null && Carbon::parse($supplierMof->exp_date)->diffInDays(Carbon::now()) > 0))
                            ||
                            ($supplierDetails->appl_type == 'N' || $supplierDetails->appl_type == 'R' || $supplierDetails->appl_type == 'O')
                            
                        )  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['record_status' => 1,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,
                                &$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "supplier_business_type":
                    $selectedBusinessType = request()->selected_business_type;
                    if($selectedBusinessType != '')  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['business_type' => $selectedBusinessType,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            $this->resetMofCertPath($supplierId, $logs);

                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "supplier_ssm_no":
                    $ssmNo = request()->ssm_no;

                    //Checking duplicate
                    $isExistSSM= $this->getSMSupplierDetailByRegNoAndSupplierType($ssmNo, $supplierDetails->supplier_type);

                    if($isExistSSM == false && mb_check_encoding($ssmNo, 'ASCII'))  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['reg_no' => $ssmNo,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            $this->resetMofCertPath($supplierId, $logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "supplier_company_name":
                    $companyName = request()->company_name;

                    if($companyName != null && mb_check_encoding($companyName, 'ASCII') )  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['company_name' => strtoupper($companyName),'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER
                        $parameters->put("patching", $patchingProcess);

                        $companyBasicId = $supplierDetails->company_basic_id;
                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$supplierId,$companyBasicId,
                                $listinProgressSuppProcessAppl,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmSupplier($updateFields, $supplierId, $logs);
                            $this->saveTransactionSmBasicCompany($updateFields, $companyBasicId, $listinProgressSuppProcessAppl, $logs);
                            $this->resetMofCertPath($supplierId, $logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "reset_download_cert_mof":
                    $listMofCert = $this->getListSMMofCert($supplierId);

                    if(count($listMofCert) > 0){

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['file_name' => null,'file_path' => null,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$listMofCert,&$logs,&$isSuccessSave) {

                            foreach($listMofCert as $key => $mofCertObj){
                                $this->saveTransactionSmMofCert($updateFields, $mofCertObj->mof_cert_id,$key,$logs);
                            }
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "cancel_appl_inprogress":
                    $selectedInProgressAppl = request()->selected_inprogress_appl;
                    $query      =   DB::connection('oracle_nextgen_fullgrant')->table('SM_APPL');
                                    $query->where('SUPPLIER_ID', $supplierId);
                                    $query->where('APPL_ID', $selectedInProgressAppl);
                    $checkExist =   $query->count();

                    if($checkExist > 0)  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['record_status' => '9','is_active_appl' => '0','changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$selectedInProgressAppl,&$logs,&$isSuccessSave) {
                            $this->saveTransactionSmAppl($updateFields, $selectedInProgressAppl,$logs);
                            $isSuccessSave = true;
                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "inactive_appl_inprogress":
                    $selectedInProgressAppl = request()->selected_inprogress_appl;
                    $query      =   DB::connection('oracle_nextgen_fullgrant')->table('SM_APPL');
                                    $query->where('SUPPLIER_ID', $supplierId);
                                    $query->where('APPL_ID', $selectedInProgressAppl);
                    $checkExist =   $query->count();

                    if($checkExist > 0)  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_active_appl' => '0','changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$selectedInProgressAppl,&$logs,&$isSuccessSave) {
                            $this->saveTransactionSmAppl($updateFields, $selectedInProgressAppl,$logs);
                            $isSuccessSave = true;
                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "setcancel_appl_history":
                    $selectedHistoryApplID = request()->selected_history_appl;
                    $query      =   DB::connection('oracle_nextgen_fullgrant')->table('SM_APPL');
                                    $query->where('SUPPLIER_ID', $supplierId);
                                    $query->where('APPL_ID', $selectedHistoryApplID);
                                    $query->whereIn('RECORD_STATUS', ['0','9']);
                                    $query->where('IS_ACTIVE_APPL', 0);
                    $checkExist =   $query->count();

                    if($checkExist > 0)  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['status_id' => '20202','changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$selectedHistoryApplID,&$logs,&$isSuccessSave) {
                            $this->saveTransactionSmAppl($updateFields, $selectedHistoryApplID,$logs);
                            $isSuccessSave = true;
                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;

                case "mof_account_9":

                    $supplierMof= $this->getMOFDetails($supplierId,$supplierDetails->supplier_type); //Checking exp more than 365
                    if($supplierMof != null && Carbon::now()->diffInDays(Carbon::parse($supplierMof->exp_date)) > 365)  {

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];
                        $mofAccountId = $supplierMof->mof_account_id;
                        $updateFields = ['record_status' => 9,'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_MOF_ACCOUNT
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$mofAccountId,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmMofAccount($updateFields, $mofAccountId, $logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                    
                case "mof_activate":
                    $listCheckSupplierNotActivateMOF= $this->checkSupplierMofExpiredNotActivate($supplierId); 
                    $supplierMof= $this->getMOFDetails($supplierId,$supplierDetails->supplier_type); 
                    if($supplierMof != null && count($listCheckSupplierNotActivateMOF) > 0){

                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];
                        $mofAccountId = $supplierMof->mof_account_id;
                        $applObj = $this->getSmApplWithWOrkFlowLatestDetail($applId);
                        $effDate = Carbon::parse($applObj->status_created_date);
                        $yearBegin = $effDate->year;
                        $yearEnd = $yearBegin + 3;
                        $expDate = Carbon::create($yearEnd, $effDate->month, $effDate->day);
                        $updateFields = [ 
                            'record_status' => 1,
                            'eff_date' => $effDate->format('Y-m-d'),
                            'exp_date' => $expDate->format('Y-m-d'),
                            'changed_date' => Carbon::now(),
                            'changed_by' => 1
                        ]; // Will update on table SM_MOF_ACCOUNT
                        
                        $parameters->put("patching", $patchingProcess);

                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$mofAccountId,&$logs,&$isSuccessSave) {

                            $this->saveTransactionSmMofAccount($updateFields, $mofAccountId, $logs);
                            $this->setInActiveMofCert($mofAccountId, $logs);
                            $isSuccessSave = true;

                        });
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;    
                
                case "sync_personnel_identity_resident":
                   
                    $listPersonnel= $this->getListPersonnelIcBySupplierId($supplierId); 

                    $listPersonnelNeedUpdate = collect();
                    foreach($listPersonnel as $rowPersonnel){

  
                        $dataIdentity = $this->getIdentityInfoJPN($rowPersonnel->identification_no);
                        $icJpn = $dataIdentity['result'];
                        if($icJpn != null && count($icJpn) >  0 && $dataIdentity['status'] == 'success' 
                                && array_key_exists('name', $icJpn)){
                            
                            $collectData = collect();
                            $collectData->put('personnel_id', $rowPersonnel->personnel_id);
                            $collectData->put('appl_id', $rowPersonnel->appl_id);
                            $collectData->put('ic_no', $rowPersonnel->identification_no);
                            $collectData->put('name', $rowPersonnel->name);
                            $collectData->put('identity_resident_status', $icJpn['residential_status']);
                            $collectData->put('identity_response_code', '70018');
                            $listPersonnelNeedUpdate->push($collectData);
                        }
                    }
                    
                    $logsUpd = collect();
                    foreach($listPersonnelNeedUpdate as $collectRow){
                        $patchingProcess = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['name'];
                        $personnelId = $collectRow->get('personnel_id');
                        $updateFields = [   'identity_resident_status' => $collectRow->get('identity_resident_status'),
                                            'identity_response_code' => $collectRow->get('identity_response_code'),
                                            'changed_date' => Carbon::now(),'changed_by' => 1]; // Will update on table SM_SUPPLIER and PM_USER (IF RELATED)
                        $parameters->put("patching", $patchingProcess);
                        
                        
                        //If failed, it will rollback
                        DB::connection('oracle_nextgen_fullgrant')->transaction(function() use ($updateFields,$personnelId,&$logsUpd,&$isSuccessSave) {

                            $logUpdate = collect();
                            $this->saveTransactionSmPersonnelOnly($updateFields, $personnelId, $logUpdate);
                            $isSuccessSave = true;
                            $logsUpd->push($logUpdate);

                        });
                    }
                    
                    $logs->put('data_before',$listPersonnel);
                    $logs->put('data_will_update',$listPersonnelNeedUpdate);
                    $logs->put('data_query_update',$logsUpd);
                    EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');
                    dump($logs);     
                    
                    break;
                    
                default:
                    dd('Process Not found');
            }

            $latestSupplierDetails = $this->getSMSupplierDetails($applId,$supplierId);
            $latestSupplierDetailsInProgress = null;
            $latestSupplierUsers = null;
            $latestInAppl = null;
            if($latestSupplierDetails){

                $latestListinProgressSuppProcessAppl = $this->getInProgressWorkFlowSupplierProcessUsingFullGrantBySupplierId($latestSupplierDetails->supplier_id);
                if(count($latestListinProgressSuppProcessAppl) > 0){
                  $latestInAppl = $latestListinProgressSuppProcessAppl[0];
                  if($latestInAppl){
                    $latestSupplierDetailsInProgress = $this->getSMSupplierDetails($latestInAppl->appl_id, $latestInAppl->supplier_id);
                  }
                }

                $latestSupplierUsers = $this->getListSMSupplierPMUser($supplierId);
            }


            if($isSuccessSave == true){
                $resultStatus = 'success';
                $resultDesc = "Kemaskini maklumat pembekal > $patchingProcess  berjaya disimpan.";
            }else{
                $resultStatus = 'failed';
                $rules = EPService::$SUPPLIER_PROCESS_PATCHING[$processId]['description'];
                $resultDesc = "Kemaskini maklumat pembekal > $patchingProcess  gagal disimpan. ".$rules;
            }
        }
        //return json_encode($personnelDetails);

        session()->flashInput(request()->input());

        return view('patch.supplier_patch', [
                'data' => $latestSupplierDetails,
                'data_inprogress' => $latestSupplierDetailsInProgress,
                'appl_inprogress' => $latestInAppl,
                'listUsers' => $latestSupplierUsers,
                'result_status' => $resultStatus,
                'result_desc'   => $resultDesc,
            ]);
    }


    public function saveTransactionSmSupplier($updateFields,$supplierId,&$logs){
        $logQuery = $this->updateSMSupplier($supplierId,$updateFields);
        $logs->put('action_sm_supplier',$logQuery);
    }

    public function saveTransactionSmMofAccount($updateFields,$mofAccountId,&$logs){
        $logQuery = $this->updateSMMofAccount($mofAccountId,$updateFields);
        $logs->put('action_sm_supplier',$logQuery);
    }

    /*
     * Check if Exist and Reset Path
     */
    public function resetMofCertPath($supplierId,&$logs){
        //Reset MOF Virtual Cert
        $listMofCert = $this->getListSMMofCert($supplierId);
        if(count($listMofCert) > 0){
            $updateMofFields = ['file_name' => null,'file_path' => null,'changed_date' => Carbon::now(),'changed_by' => 1];
            foreach($listMofCert as $key => $mofCertObj){
                $this->saveTransactionSmMofCert($updateMofFields, $mofCertObj->mof_cert_id,$key,$logs);
            }
        }
    }
    
     /*
     * Check if Active Cert by MOF Account ID, Set as InActive
     */
    public function setInActiveMofCert($mofAccountId,&$logs){
        //Reset MOF Virtual Cert
        $listMofCert = $this->getListSMMofCertActiveByMofAccountId($mofAccountId);
        if(count($listMofCert) > 0){
            $updateMofFields = ['record_status' => 1,'changed_date' => Carbon::now(),'changed_by' => 1];
            foreach($listMofCert as $key => $mofCertObj){
                $this->saveTransactionSmMofCert($updateMofFields, $mofCertObj->mof_cert_id,$key,$logs);
            }
        }
    }

    public function saveTransactionSmMofCert($updateFields,$mofCertId,$counter,&$logs){
        $logQuery = $this->updateSMMofCert($mofCertId, $updateFields);
        if($counter == 0){
            $logs->put('action_sm_mof_cert',$logQuery);
        }else{
            $logs->put('action_sm_mof_cert_'.$counter,$logQuery);
        }
    }

    public function saveTransactionSmBasicCompany($updateFields,$companyBasicId, $listinProgressSuppProcessAppl,&$logs){
        $logQuery = $this->updateSMCompanyBasic($companyBasicId, $updateFields);
        $logs->put('action_sm_basic_company_active_appl',$logQuery);

        /** If got another data personnel in current Supplier Progress Application **/
        if(count($listinProgressSuppProcessAppl) > 0){
            $inAppl = $listinProgressSuppProcessAppl[0];
            $dataObj =  DB::connection('oracle_nextgen_fullgrant') ->table('SM_COMPANY_BASIC')->where('appl_id', $inAppl->appl_id)->first();
            $logQueryInProg = $this->updateSMCompanyBasic($dataObj->company_basic_id, $updateFields);
            $logs->put('action_sm_supplier_in_progress_appl',$logQueryInProg);
        }

    }

    public function saveTransactionPmUser($updateFields,$userId,&$logs){
        $logQuery = $this->updatePMUser($userId,$updateFields);
        $logs->put('action_pm_user',$logQuery);
    }

    public function saveTransactionSmAppl($updateFields,$applId,&$logs){
        $logQuery = $this->updateSMAppl($applId,$updateFields);
        $logs->put('action_sm_appl',$logQuery);
    }

    public function saveTransactionPmUserOrg($updateFields,$userOrgId,&$logs){
        $logQuery = $this->updatePMUserOrg($userOrgId,$updateFields);
        $logs->put('action_pm_user_org',$logQuery);
    }

    public function saveTransactionPmUserRole($updateFields,$userRoleId,&$logs){
        $logQuery = $this->updatePMUserRole($userRoleId,$updateFields);
        $logs->put('action_pm_user_role',$logQuery);
    }

    // MMINF data patch (is_sent = 0)
    public function updateMminfDetails($mminfId, $materialCode){
        $resultStatus = null;
        $resultDesc = null;
        $isSuccessSave = false;
        $patchingProcess = '';

            if ($mminfId == null || $materialCode == null) {
            return view('patch.mminf_details_patch', [
                'data' => null,
            ]);
        } else {
            Validator::make(request()->all(), [
                'selected_process_id' => 'required',
                'remarks' => 'required',
                "material_desc" => "required_if:selected_process_id,==,mminf_update_material_desc",
                "material_add_desc" => "required_if:selected_process_id,==,mminf_update_material_add_desc",
            ])->validate();

            $mminfIdDetails = $this->getRecordByItemDetails($mminfId, $materialCode);

            $processId = request()->selected_process_id;
            $actionTypeLog = 'Script';
            $actionName = 'PatchDataMMINF';
            $logs = collect([]);

            $parameters =  collect([]);
            $parameters->put("remarks", request()->remarks);

            switch ($processId) {
                case "mminf_isSent_status_0":
                    if($mminfIdDetails->is_sent == 1){
                        $patchingProcess = EPService::$MMINF_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['is_sent' => 0,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        $this->saveTransactionMminfDetails($isSuccessSave, $updateFields, $mminfId, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                case "mminf_update_material_desc":
                    $materialDesc = request()->material_desc;  // This char must 40 (Follow MMINF Web Service IDD)
                    $isSpecialCharacterExist = false;
                    if(mb_check_encoding($materialDesc, 'ASCII') == false){
                        $isSpecialCharacterExist = true;
                    }
                    $oldMaterialDescSize = strlen($mminfIdDetails->material_desc);

                    if(strlen($materialDesc) > (strlen($oldMaterialDescSize) - 6) && strlen($materialDesc) <= 40 && $mminfIdDetails != null && $isSpecialCharacterExist == false){
                        $patchingProcess = EPService::$MMINF_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['material_desc' => $materialDesc,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        $this->saveTransactionMminfDetails($isSuccessSave, $updateFields, $mminfId, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;

                case "mminf_update_material_add_desc":
                    $materialAddDesc = request()->material_add_desc; //Max char 960
                    $isSpecialCharacterExist = false;
                    if(mb_check_encoding($materialAddDesc, 'ASCII') == false){
                        $isSpecialCharacterExist = true;
                    }
                    $oldMaterialAddDescSize = strlen($mminfIdDetails->material_add_desc);
                    if($materialAddDesc &&  strlen($materialAddDesc) > ($oldMaterialAddDescSize - 3) && $mminfIdDetails != null && $isSpecialCharacterExist == false){
                        $patchingProcess = EPService::$MMINF_PROCESS_PATCHING[$processId]['name'];

                        $updateFields = ['material_add_desc' => $materialAddDesc,'changed_date' => Carbon::now(),'changed_by' => 1];
                        $parameters->put("patching", $patchingProcess);

                        $this->saveTransactionMminfDetails($isSuccessSave, $updateFields, $mminfId, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;

                case "mminf_delete":
                    if($mminfIdDetails != null && $mminfIdDetails->mminf_id != null ){
                        $patchingProcess = EPService::$MMINF_PROCESS_PATCHING[$processId]['name'];

                        $parameters->put("patching", $patchingProcess);

                        $this->deleteTransactionMminfDetails($isSuccessSave, $mminfId, $logs);
                        EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$logs,$parameters,($isSuccessSave == true) ? 'Completed' : 'Failed');

                    }
                    break;
                default:
                    dd('Process Not found');
            }

            $mminfIdDetailsUpdated = $this->getRecordByItemDetailsUpdated($mminfId, $materialCode);

            foreach($mminfIdDetailsUpdated as $obj){
                $obj->notAsciiMaterialDesc = false;
                if(mb_check_encoding($obj->material_desc, 'ASCII') == false){
                    $obj->notAsciiMaterialDesc   = true;
                }

                $obj->notAsciiMaterialAddDesc  = false;
                if(mb_check_encoding($obj->material_add_desc, 'ASCII') == false){
                    $obj->notAsciiMaterialAddDesc  = true;
                }

            }


            if($isSuccessSave == true){
                $resultStatus = 'success';
                $resultDesc = "Kemaskini maklumat MMINF > $patchingProcess  berjaya disimpan.";
            }else{
                $resultStatus = 'failed';
                $rules = EPService::$MMINF_PROCESS_PATCHING[$processId]['description'];
                $resultDesc = "Kemaskini maklumat MMINF > $patchingProcess  gagal disimpan. ".$rules;
            }
        }
        session()->flashInput(request()->input());
        return view('patch.mminf_details_patch', [
                'listdata' => $mminfIdDetailsUpdated,
                'result_status' => $resultStatus,
                'result_desc'   => $resultDesc,
            ]);
    }

    public function updateContractItems($contractno, $contractVersion) {

        $contractItems = $this->getContractItems($contractno, $contractVersion);

        $actionTypeLog = 'Script';
        $actionName = 'PatchDataContractItem';
        $logs = collect([]);

        $parameters = collect([]);
        $data = Request()->data;
        $ctdata = Request()->ctdata;
        $randomnumber = Request()->randomnumber;
        $number = $randomnumber . '-' . Carbon::now()->format('Y-m-d');

        if (count($contractItems) > 0) {

            $parameters->put("randomnumber: ", $number);
            $parameters->put("data:", $data);
            $parameters->put("ctdata:", $ctdata);
            $logs->put("data_before: ", $contractItems);

            $actionLog = EpSupportActionLog::where('action_parameter', 'like', '%' . $number . '%')
                    ->where('action_name', 'PatchDataContractItem')
                    ->first();

            if ($data != null || $ctdata != null) {

                if (count($actionLog) > 0) {
                    EpSupportActionLog::updateActionLogData($actionLog, $parameters);
                } else {
                    EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $logs, $parameters, 'Completed');
                }
            }
        }

        session()->flashInput(request()->input());
    }
   

}
