<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\Bpm;

use Carbon\Carbon;
use Log;
use DB;
use Config;
use Excel;
use Ramsey\Uuid\Uuid;
use App\Migrate\MigrateUtils;
use App\Services\Traits\BpmApiService;


class BPMReAssignTaskProgram {
    use BpmApiService;

    /**
     * 27/02/2024 start to re-assigned task for user PHIS plsb5169
     */
    public static function removeTaskReAssignedTaskList() {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $th = new BPMReAssignTaskProgram;
        $totalMax = 10;  //mahadi: ************ // ************ on 16/6/2025
        $loginId = '************';
        $counter = 1;
        do {
            MigrateUtils::logDump(__METHOD__ . " ########## COUNTER : ".$counter. ' >>> loginID: '.$loginId);
            $listTask = $th->findApiWorklist( $loginId, 'ASSIGNED', 'ALL', 0, $totalMax);
            self::reAssignTaskLists($listTask,$loginId);
            $total = count($listTask['result']['worklistItem']);
            $counter++;

        } while ( $total > 1);
        MigrateUtils::logDump(__METHOD__ . ' Completed ' );
   
        
    }

    public static function reAssignTaskLists($listTask,$loginId){
        MigrateUtils::logDump(__METHOD__ . ' Starting ... ');
        $th = new BPMReAssignTaskProgram;
        try{
            $data = $listTask['result']['worklistItem'];
            MigrateUtils::logDump(__METHOD__ . ' TOTAL : '.count($data ));
            $counter = 0;
            foreach( $data as $obj){
                $year = Carbon::parse($obj['createDateString'])->year;
                MigrateUtils::logDump(__METHOD__ . ' Year : '.$year);
                MigrateUtils::logDump(__METHOD__ . ' TaskID : '.$obj['taskId']);
                MigrateUtils::logDump(__METHOD__ . ' CompositeInstanceId : '.$obj['instanceId']);
                MigrateUtils::logDump(__METHOD__ . ' compositeDN : '.$obj['compositeDN']);
                MigrateUtils::logDump(__METHOD__ . ' substate : '.$obj['substate']);
                MigrateUtils::logDump(__METHOD__ . ' acquiredBy : '.$obj['acquiredBy']);
                $composite =$obj['compositeDN'];
                $docType =$obj['docType'];
                //dd($obj);
                $isAllowReAssigned= false;
                $userLoginClaim = null;
                $isClaimedTask = false;
                if($obj['substate']=='ACQUIRED'){
                    $userLoginClaim = $obj['acquiredBy'];
                    $isClaimedTask = true;
                }
                //if(str_contains($composite , 'SourcingDP')){
                //    $isAllowReAssigned = true;
                //}

                if(str_contains($docType , 'SR')){
                    $isAllowReAssigned = true;
                }

                if(str_contains($docType , 'CP')){
                    $isAllowReAssigned = true;
                }
               
                $assignees = $obj['assignees'];
                
                $totBef = count($assignees);
                MigrateUtils::logDump(__METHOD__ . ' total assignees before : '.$totBef);
                //delete element in array by value loginId
                if (($key = array_search( $loginId, $assignees)) !== false) {
                    unset($assignees[$key]);
                }
                $totAft = count($assignees);
                MigrateUtils::logDump(__METHOD__ . ' total assignees after : '.$totAft);
                if($isAllowReAssigned === true && $totAft < $totBef){
                    $counter ++;
                    $str_assignees = implode(",",$assignees); 
                    //$str_assignees = '901129086369,920103086145,900422145781,741019145380'; // LATEST CM_ADMIN
                    //MigrateUtils::logDump(__METHOD__ . ' task str_assignees :'.$str_assignees);
                    $res = $th->reassignTask($obj['taskId'], $str_assignees);
                    //dump($res);
                    if ($res['status'] == 'Success' 
                        && $isClaimedTask === true) {
                        $claimTaskAPI = $th->actionWorklistTaskAPI('Claim-Task',$obj['taskId'],$userLoginClaim);
                        if($claimTaskAPI && $claimTaskAPI["status"] === 'Success' &&  $claimTaskAPI["status"] != null){
                            MigrateUtils::logDump(__METHOD__ . ' success claim by '.$userLoginClaim);
                        }
                    }
                    MigrateUtils::logDump(__METHOD__ . " $counter) done reassigned task.... ");
                    //dd('Done');
                }else{
                    MigrateUtils::logDump(__METHOD__ . ' not in criteria');
                }
                
            }
        }catch(Exception $ex){
            MigrateUtils::logErrorDump(__METHOD__ . ' ERROR ' .$ex->getMessage());
        }
    }



}
