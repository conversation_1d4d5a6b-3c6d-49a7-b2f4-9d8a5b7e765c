<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Mail;
use App\Migrate\MigrateUtils;
use Log;
use Config; 
use DB;
use Excel; 

class CasePerubahanDataSchedule extends Command {

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'case-perubahan-data-monthly';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will generate Case Perubahan Data Statistic Report';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */

    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        $dtStartTime = Carbon::now();
        $dateStart  = '2024-01-01';
        $dateEnd    = Carbon::yesterday()->format('Y-m-d');
        try {
            self::createExcelReport($dateStart, $dateEnd);
            $logsdata = self::class . ' Query Date Start : '.$dateStart.' , Query Date End : '.$dateEnd.' , Completed --- Taken Time : '.  json_encode([ 'Time' => MigrateUtils::getTakenTime($dtStartTime)]);
            Log::info($logsdata);
        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            $err = $exc->getTrace();
            \Log::error(self::class . '>> error happen!! ' . json_encode($err));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($err));
            echo $exc->getTraceAsString();
        }
        
    }

    public static function createExcelReport($dateStart, $dateEnd) {
        $dtStartTime = Carbon::now();

        $dateReport = self::generateReport($dateStart, $dateEnd);
        self::sendEmail($dateReport);
        
        $durationTime = MigrateUtils::getTakenTime($dtStartTime);

        Log::debug(self::class . ' Taken Time :', ['Time' => $durationTime]);
    }

    public static function generateReport($dateStart, $dateEnd, $status = null) {
        $start = 0;
        $skip = 500;
        $take = 500;
        $totalRecords = 0;

        $dtStartTimeOP = Carbon::now();

        $CsvData = array('Kementerian,PTJ,Sub Category,Sub Sub Category,Status,Total Case');
        
        do {
            $nextSkip = $start++ * $skip; 

                $dataSub1 = self::getQuery($dateStart,$dateEnd,$take,$nextSkip,$status);
                $totalRecords = $totalRecords + count($dataSub1);

                $dtStartTimeEachLoop = Carbon::now();

                var_dump(self::class .' current totalrecords ' .count($dataSub1));
                
                foreach ($dataSub1 as $obj) {
                     $kementerianName = preg_replace('/[,]+/', ' ', trim($obj->kementerianName));
                     $orgNameName = preg_replace('/[,]+/', ' ', trim($obj->ptjName));
                     $caseSubCategory = preg_replace('/[,]+/', ' ', trim($obj->subCategory1));
                     $caseSubSubCategory = preg_replace('/[,]+/', ' ', trim($obj->subCategory2));
                     $statusCase = $obj->caseStatus;
                     $totalCase = $obj->total;

                     $CsvData[] = (
                        $kementerianName . ',' .
                        $orgNameName . ',' . 
                        $caseSubCategory . ',' .
                        $caseSubSubCategory . ',' . 
                        $statusCase . ',' . 
                        $totalCase
                        );
                }

                $takentimeeachLoop = array(
                'Counter' => $start,
                'Taken Time per Minutes' => $dtStartTimeEachLoop->diffInMinutes(Carbon::now()),
                'Taken Time per Seconds' => $dtStartTimeEachLoop->diffInSeconds(Carbon::now())
            );
            var_dump(self::class . '    :: LoopTakenTime >> Time   :   ', [$takentimeeachLoop]);
            var_dump(self::class . '    :: sum total current  :   ' . $totalRecords);
            
        } while (count($dataSub1) > 0 && count($dataSub1) == 500);

        $takentimeOP = array(
            'Counter' => $start,
            'Taken Time per Minutes' => $dtStartTimeOP->diffInMinutes(Carbon::now()),
            'Taken Time per Seconds' => $dtStartTimeOP->diffInSeconds(Carbon::now())
        );
        var_dump(self::class . '    :: AllLoopTakenTime >> Time   :   ', [$takentimeOP]);
        var_dump(self::class . ' queryReport. Total All :  ' . $totalRecords);
        var_dump(self::class . '--------------------------------------------');

        $filename = 'StatistikPerubahanData' . $dateStart . '_to_' . $dateEnd. ".csv";
        $file_path = storage_path('app/exports/cases/'. $filename);
        $file = fopen($file_path, "w+");
        foreach ($CsvData as $exp_data) {
            fputcsv($file, explode(',', $exp_data));
        }
        fclose($file);

        $dataReport = collect([]);
        $dataReport->put("date_start", $dateStart);
        $dataReport->put("date_end",$dateEnd);
        $dataReport->put("report_name",'Statistik Perubahan Data');
        $dataReport->put("file_name",$filename);
        $dataReport->put("report_path",$file_path);
        
        return $dataReport;
        
        //$headers = ['Content-Type' => 'application/csv'];
        //return response()->download($file_path, $filename, $headers);
    }

    protected static function getQuery($dateStart,$dateEnd,$take,$nextSkip,$status) {
        var_dump('DateStart:'.$dateStart,'DateEnd:'.$dateEnd,'Take:'.$take,'Skip:'.$nextSkip);
        $perubahandata = array('10713_15541');

        $sql = DB::connection('mysql_crm')->select("SELECT
        CASE
            WHEN (
              cstma.`value_name` NOT IN (
                'KUMPULAN PTJ',
                'KEMENTERIAN',
                'PEGAWAI PENGAWAL'
              )
            )
            THEN a.name
            ELSE a.name
          END AS ptjName,
          CASE
            WHEN (
              cstma.`value_name` = 'KUMPULAN PTJ'
            )
            THEN d.name
            WHEN (
              cstma.`value_name` = 'KEMENTERIAN'
            )
            THEN a.name
            WHEN (
              cstma.`value_name` NOT IN (
                'KUMPULAN PTJ',
                'KEMENTERIAN',
                'PEGAWAI PENGAWAL'
              )
            )
            THEN e2.name
            ELSE NULL
          END AS kementerianName,
          cc.sub_category_desc_c AS subCategory1,
          cc.sub_category_2_desc_c AS subCategory2,
          c.state caseStatus, -- c.case_number
         COUNT(c.case_number) AS total
          FROM
          cases c
          JOIN cases_cstm cc ON cc.id_c = c.id
          LEFT JOIN accounts a
            ON (
              a.`id` = c.`account_id`
        
            )
          LEFT JOIN accounts b
            ON (
              b.`id` = a.`parent_id`
        
            )
          LEFT JOIN accounts d
            ON (
              d.`id` = b.`parent_id`
        
            )
          LEFT JOIN accounts e
            ON (
              e.`parent_id` = a.`id`
        
            ) -- account as kementerian
          LEFT JOIN accounts b1
            ON (
              b1.id = a.parent_id
        
            )
          LEFT JOIN accounts d1
            ON (
              d1.`id` = b1.`parent_id`
        
            ) -- account as ptj
          LEFT JOIN accounts b2
            ON (
              b2.`id` = a.`parent_id`
        
            )
          LEFT JOIN accounts d2
            ON (
              d2.`id` = b2.`parent_id`
        
            )
          LEFT JOIN accounts e2
            ON (
              e2.id = d2.parent_id
        
            )
            LEFT JOIN cstm_list_app cstma
            ON (
              cstma.`value_code` = a.org_gov_type
              AND cstma.`type_code` = 'accounts_nextgen_org_gov_code'
            )
            WHERE c.deleted = 0
            AND DATE(CONVERT_TZ(c.`date_entered`, '+00:00', '+08:00')) BETWEEN '$dateStart' AND '$dateEnd'
            AND cc.sub_category_c = '10713_15541'
            AND c.state = 'Closed'
            AND a.account_type = 'GOVERNMENT'
            GROUP BY kementerianName, ptjName, subCategory1, subCategory2, caseStatus"); 

//        dump(self::class . ' :: getQuery >> SQL   :   ', [$data]);
return $result = $sql;
}

protected static function  sendEmail($dataReport) {
    $path = storage_path('app/exports/cases/'. $dataReport->get("file_name"));
    $data = array(
        //   "to" => ['<EMAIL>'],
        "to" => ['<EMAIL>','<EMAIL>',
            '<EMAIL>','syarifuddin @treasury.gov.my','<EMAIL>',
            '<EMAIL>'],
        "subject" => "Server (".env('APP_ENV').") > CRM Report : ".$dataReport->get('report_name')." pada ".$dataReport->get('date_start')." sehingga ".$dataReport->get('date_end'),
     
    );
    try {
        /*
        Mail::send('emails.report',['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data) {
            $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
            $m->to($data["to"])->subject($data["subject"]);
            // ->setBody('<a href="'.url('/crm/casedetail/download/'.$fileName).'" target =" blank"> Download Report </a>', 'text/html');
        });
        var_dump('done send'); */
        
        $transport = (new \Swift_SmtpTransport(env('MAIL_EP_HOST', '*************'), env('MAIL_EP_PORT', '*************')))
            ->setEncryption(env('MAIL_EP_ENCRYPTION', null))
            ->setUsername(env('MAIL_EP_USERNAME', 'mailuser'))
            ->setPassword(env('MAIL_EP_PASSWORD', 'cdc2mail2014'));

            $mailer = app(\Illuminate\Mail\Mailer::class);
            $mailer->setSwiftMailer(new \Swift_Mailer($transport));
            $mail = $mailer
                ->send('emails.report', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'data' => $dataReport], function($m) use ($data,$path) {
                    $m->from(Config::get('constant.email_sender_ep-auto-notify'), 'eP Auto Notify');
                    $m->to($data["to"]);
                    $m->attach($path);
                    $m->subject($data["subject"]); 
                });
                var_dump('done send');
    } catch (\Exception $e) {
        $reportName = $dataReport->get('report_name');
        $msg = '*[ALERT]* Failed send report ' .$reportName .'.Please Check!!';
       // self::crmService()->notifyWhatsapp($msg, 'CRM_REPORT_INTEGRATION', 'CrmIntegration', 'Failed Send Email Report');
        // echo $e;
        // Log::error(self::class . ' Error ... ' . __FUNCTION__.' ::  ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
        var_dump('error' .$e);
        // return $e;
    }
}

    /**
     * Send an e-mail Test Only
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error Generate Case Detail Incident/Service Report'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }

}
