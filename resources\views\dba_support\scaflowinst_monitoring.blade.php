@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
             <i class="gi gi-charts"></i>Report </br> <small>Monitoring of SCA Flow Instance</small>
        </h1>        
    </div>
@endsection
@section('content')
<div class="row">
    <form id="form-search-login" action="{{url("dba-support/report/SCAFlowInstMonitoringReport")}}" method="post" class="form-horizontal" onsubmit="return true;">
        {{ csrf_field() }}
        <div class="col-md-12">            
            <div class="widget" class="inset">
                <center><div class="block">                
                        <input id="searchType" name="searchType" type="hidden" value="search_date">                    
                        <strong> <label class="col-md-3 control-label" for="entry_date">Search by Date :</label></strong>
                        <div class="col-md-5">
                            <input class="form-control input-datepicker-close" id ="entry_date1" name="entry_date1" type="text" 
                                   data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                   value="{{$StartDate}}">
                            </br>
                            <input class="form-control input-datepicker-close" id ="entry_date2" name="entry_date2" type="text" 
                                   data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                   value="{{$EndDate}}">
                        </div>
                        
                        <div class="form-group form-actions form-actions-button text-center">                        
                            <div class="col-md-11">
                                </br>
                                <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                            </div>
                        </div>

                    </div></center>
                <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
                <div id="chartWFTask" style="height: 370px; width: 100%; border-style: groove;"></div>
                </br>
            </div>
        </div>
    </form>
</div>
 
 
@endsection

@section('jsprivate')
<script type="text/javascript">   
    $('#page-container').removeAttr('class');
</script>
<script>
window.onload = function () {  
    
// Composite Chart
var chart = new CanvasJS.Chart("chartWFTask", {
   
	title: {
		text: "SCA Flow Instance Summary Report"
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
        axisX: {
		valueFormatString: "DD MMM,YY"
	},
	axisY: {
		title: "Total of Volume",
                interval : 200000
	},
	legend: {
		cursor: "pointer",
                fontSize: 16,
		itemclick: toggleDataSeries
	},      
	data: [{			
			name: "Purged SCA Flow Instance",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($purgesca) !!}
		},{
			name: "BackLog SCA Flow Instance",
                        type: "line",
			showInLegend: true,
                        dataPoints : {!! json_encode($Bcklogsca) !!}
		},{			
			name: "New SCA Flow Instance",
                        type: "line",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($Newsca) !!}
		}

	]
});
 
chart.render();
 
function toggleDataSeries(e){
	if (typeof(e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	}
	else{
		e.dataSeries.visible = true;
	}
	chart.render();
}
}
</script>
@endsection