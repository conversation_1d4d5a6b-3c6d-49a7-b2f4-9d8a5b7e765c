@extends('layouts.guest-dash')

@section('header')
    <!-- Search Form -->
    <form id="cariandpform" action="{{ url('/find/dp-summary/') }}/" method="get" class="navbar-form-custom">
        <div class="form-group">
            <input type="text" id="doc_no" name="doc_no" value="{{ $carian }}" class="form-control"
                onfocus="this.select();" placeholder="Klik carian di sini (SQ, RN, PR, PO)... ">
        </div>
    </form>
    <!-- END Search Form -->
@endsection

@section('content')


    @if ($listRNdetail == null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{ $carian }}</strong></h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>Tidak dijumpai!</p>
                    </div>
                </div>
            </div>
        </div>
    @endif


    @if ($listRNdetail != null)
        <div class="block block-alt-noborder full">
            <!--DETAILS INFO-->
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> Doc Number : <font color="yellow">{{ $docno }}</font></strong></h1>
                <h1><strong> Procurement Type : <font color="yellow">{{ $listRNdetail[0]->jenisperolehan }}</font>
                    </strong></h1>
                <h1><strong>Category Procurement Type : <font color="yellow">{{ $listRNdetail[0]->kategorijenisperolehan }}
                        </font>
                    </strong></h1>
            </div>
            <div class="row">
                <div class="col-md-15">
                    <h6><strong>
                            <font color="black">{{ $listRNdetail[0]->title }}</font>
                        </strong></h6><br />
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>SQ No</strong> : <font color="black">{{ $listRNdetail[0]->quote_no }} |
                            {{ $listRNdetail[0]->quote_id }}</font> |
                        <strong>Status : <font color="black">{{ $listRNdetail[0]->status }}</font></strong><br />
                        <strong>RN No</strong> : <font color="black">{{ $listRNdetail[0]->request_note_no }} |
                            {{ $listRNdetail[0]->request_note_id }}</a>
                        </font> | <strong> Status : <font color="black">{{ $listRNdetail[0]->status_name }}</font>
                        </strong><br />
                        <strong>Panel</strong> : <font color="black">{{ $listRNdetail[0]->ispanel }}</font>
                        <strong>Zone</strong> : <font color="black">
                            {{ $listRNdetail[0]->is_zonal1 }}</font>
                    </address>

                </div>
                <div class="col-md-3">
                    <address>
                        <strong>Start Date</strong> : <font color="red">{{ $listRNdetail[0]->start_date }} @if (!is_null($listRNdetail[0]) && property_exists($listRNdetail[0], 'date1'))
                                {{ $listRNdetail[0]->date1 }}
                            @endif
                        </font><br />
                        <strong>End Date</strong> : <font color="red">{{ $listRNdetail[0]->end_date }} @if (!is_null($listRNdetail[0]) && property_exists($listRNdetail[0], 'date2'))
                                {{ $listRNdetail[0]->date2 }}
                            @endif
                        </font>
                        <br />
                        <strong>SQ Type</strong> : {{ $listRNdetail[0]->jenis_pelawaan_tawaran_harga }}
                        <br />
                    </address>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>Created By</strong> : <font color="black">
                            @if ($req !== null)
                                {{ $req }}
                            @else
                                {{ $listRNdetail[0]->login_id }} | {{ $listRNdetail[0]->user_name }}
                            @endif
                        </font><br />
                        <strong>Approver</strong> : <font color="black">
                            @if ($app !== null)
                                {{ $app }}
                            @else
                                {{ $listRNdetail[0]->login }} | {{ $listRNdetail[0]->approver }}
                            @endif
                        </font><br />
                        <strong>Approver Group</strong> : <font color="black">{{ $listRNdetail[0]->group_name }}
                        </font><br />
                        <strong>Prepared For PTJ</strong> : <font><a class="modal-list-data-action"
                                href="{{ url('/find/orgcode/') }}/{{ $listRNdetail[0]->org_code }}" target='_blank'>
                                {{ $listRNdetail[0]->org_code }}</a> | {{ $listRNdetail[0]->org_name }}
                        </font>

                    </address>
                </div>
            </div>
            @if ($listZonalLocation != null)
                <div class="block">
                    <div class="block-title panel-heading epss-title-s1">
                        <h1><i class="fa fa-building-o"></i></h1>
                        <h1><strong>Zonal Location<font color="yellow"></font></strong></h1>
                    </div>

                    <table id="zonal_datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th class="text-center">Zone Name</th>
                                <th class="text-center">State Name</th>
                                <th class="text-center">Division Name</th>
                                <th class="text-center">District Name</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($listZonalLocation as $list)
                                <tr>
                                    <td class="text-center">{{ $list->zone_name }}</td>
                                    <td class="text-center">{{ $list->state_name }}</td>
                                    <td class="text-center">{{ $list->division_name }}</td>
                                    <td class="text-center">{{ $list->district_name }}</td>
                                </tr>
                            @endforeach

                        </tbody>
                    </table>
                </div>
            @endif
        </div>
    @endif

    {{-- item when sq submitted --}}
    @if ($getItemWhenSqSubmitted != null)
        <div class="block table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong>Item SQ Submitted</strong></h1>
                <font color="black">
                    @if (!empty($listSQClone))
                        <a href='#modal-list-trans-sq_clone' class='modal-list-data-action ' data-toggle='modal'
                            data-url='/find/dp-summary/'>
                            <strong style="font-weight: bolder;">List SQ Clone</strong>
                        </a>
                    @endif
                    <div id="modal-list-trans-sq_clone" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                        style="display: none;">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header text-center">
                                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                            id="modal-list-data-header">SQ Clone</span></h2>
                                </div>
                                <div class="modal-body">
                                    <table id="sq_clone_list-datatable"
                                        class="table table-vcenter table-condensed table-bordered table-responsive">
                                        <thead>
                                            <tr>
                                                <th class="text-center">Quote Id</th>
                                                <th class="text-center">Quote No</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @if ($listSQClone != null)
                                                @foreach ($listSQClone as $list)
                                                    <tr>
                                                        <td class="text-center"> {{ $list->quote_id }}</td>
                                                        <td class="text-center"><a class="modal-list-data-action"
                                                                href="{{ url('/find/dp-summary/') }}?doc_no={{ $list->quote_no }}"
                                                                target='_blank'>
                                                                {{ $list->quote_no }}</a></td>
                                                    </tr>
                                                @endforeach
                                            @endif
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </font>

            </div>

            <table id="item_sq_datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                <thead>
                    <tr>
                        <th class="text-center">Spec Question Id</th>
                        <th class="text-center">Item Type</th>
                        <th class="text-center">Fulfilment Type</th>
                        <th class="text-center">Item Name</th>
                        <th class="text-center">Specification</th>
                        <th class="text-center">Price Type</th>
                        <th class="text-center">UOM</th>
                        <th class="text-center">Quantity</th>
                        <th class="text-center">Per Day</th>
                        <th class="text-center">Per Month</th>
                        <th class="text-center">Duration</th>
                        <th class="text-center">Item Clone ?</th>
                        <th class="text-center">Catalogue ?</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach ($getItemWhenSqSubmitted as $list)
                        <tr>
                            <td class="text-center">
                                @if (!empty($listIsItemClone))
                                    <a href='#modal-list-trans-item_clone' class='modal-list-data-action '
                                        data-toggle='modal' data-url='/find/dp-summary/'>
                                        <strong
                                            style="font-weight: bolder;"{{ $list->spec_question_id }}>{{ $list->spec_question_id }}</strong>
                                    @else
                                        {{ $list->spec_question_id }}
                                    </a>
                                @endif
                            </td>
                            <td class="text-center">{{ $list->item_type }}</td>
                            <td class="text-center">{{ $list->jenis_pemenuhan }}</td>
                            <td class="text-center">{{ $list->cl_item }}</td>
                            <td class="text-center">{{ $list->cl_spec_detail }}</td>
                            <td class="text-center">{{ $list->code_name }}</td>
                            <td class="text-center">{{ $list->uom_name }} ({{ $list->uom_code }})</td>
                            <td class="text-center">{{ $list->kuantiti }}</td>
                            <td class="text-center">{{ $list->cl_uom_per_day }}</td>
                            <td class="text-center">{{ $list->cl_uom_per_month }}</td>
                            <td class="text-center">{{ $list->cl_duration_month }}</td>
                            <td class="text-center"><a class="modal-list-data-action"
                                href="{{ url('/find/dp-summary/') }}?doc_no={{ $list->item_clone }}"
                                target='_blank'>
                                {{ $list->item_clone }}</a></td>
                            <td class="text-center">
                                @if ($list->catalogu === 'yes')
                                    <a href='#modal-list-trans-item_catalogue' class='modal-list-data-action '
                                        data-toggle='modal' data-url='/find/dp-summary/'>
                                        <strong
                                            style="font-weight: bolder;"{{ $list->item_id }}>{{ $list->item_id }}</strong>
                                    </a>
                                @endif
                                <div id="modal-list-trans-item_catalogue" class="modal fade" tabindex="-1"
                                    role="dialog" aria-hidden="true" style="display: none;">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header text-center">
                                                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                        id="modal-list-data-header">Item Info</span></h2>
                                            </div>
                                            <div class="modal-body">
                                                <table id="item-info-datatable"
                                                    class="table table-vcenter table-condensed table-bordered table-responsive">
                                                    <thead>
                                                        <tr>
                                                            <th class="text-center">Item Name</th>
                                                            <th class="text-center">Extension Code</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @if ($itemFromCatalogue != null)
                                                            @foreach ($itemFromCatalogue as $list)
                                                                <tr>
                                                                    <td class="text-center"> {{ $list->item_name }}</td>
                                                                    <td class="text-center">{{ $list->extension_code }}
                                                                    </td>
                                                                </tr>
                                                            @endforeach
                                                        @endif
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @endforeach

                </tbody>
            </table>
        </div>
    @endif
    <!--PTJ INFO-->
    @if ($ptj != null)
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong>PTJ<font color="yellow"></font></strong></h1>
            </div>
            <div>
                <div class="row">
                    <div class="col-md-6">
                        <table id="rnapp_ptj_datatable"
                            class="table table-vcenter table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="text-center">PTJ Group</th>
                                    <th class="text-center">Role Code</th>
                                    <th class="text-center">Group Name</th>
                                    <th class="text-center">Group Status</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($ptj as $ptjgroup)
                                    <tr>
                                        <td class="text-center">{{ $ptjgroup->ptj }}
                                            <br />
                                            @if ($ptjgroup->ptj !== null)
                                                <a class="modal-list-data-action"
                                                    href="{{ url('/find/orgcode/') }}/{{ $ptjgroup->org_code }}"
                                                    target='_blank'>
                                                    {{ $ptjgroup->org_code }}</a>
                                            @endif
                                        </td>
                                        <td class="text-center">
                                            <div class="modal-list-data-rnapprover">
                                                <a href='#modal-list-trans-dofn2' class='modal-list-data-action3'
                                                    data-toggle='modal'
                                                    data-url='/find/dp-summary/item/approver/{{ $ptjgroup->group_code }}/{{ $ptjgroup->request_note_no }}'
                                                    data-id='{{ $ptjgroup->group_code }}'>
                                                    <strong style="font-weight: bolder;">RN Approver
                                                        <i style="font-size: 10pt; padding-left:10px;"
                                                            title="RN Approver"></i>
                                                    </strong>
                                                    <br />
                                                </a>
                                            </div>

                                        </td>
                                        <td class="text-center">{{ $ptjgroup->group_name }} ({{ $ptjgroup->group_code }})
                                        </td>
                                        <td class="text-center">{{ $ptjgroup->group_status }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                        <div id="modal-list-trans-dofn2" class="modal fade" tabindex="-1" role="dialog"
                            aria-hidden="true" style="display: none;">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header text-center">
                                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                id="modal-list-data-header">RN Approver</span></h2>
                                    </div>
                                    <div class="modal-body">
                                        <div class="row">
                                            <div class="col-sm-12">
                                                <div class="text-center spinner-loading"
                                                    style="padding: 20px; display: none;">
                                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                                </div>
                                                <div
                                                    class="rnapp_try table table-vcenter table-condensed table-bordered table-responsive">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                    </div>
                    <div class="col-md-6 table-responsive">
                        <table id="item-datatable" class="table table-vcenter table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th class="text-center">ORG Profile Id</th>
                                    <th class="text-center">Item Name</th>
                                    <th class="text-center">Group Name</th>
                                    <th class="text-center">Extension Code</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if ($extension != null)
                                    @foreach ($extension as $extensions)
                                        <tr>
                                            <td class="text-center">{{ $extensions->org_profile_id }}</td>
                                            <td class="text-center">{{ $extensions->item_name }}</td>
                                            <td class="text-center">{{ $extensions->group_name }}</td>
                                            <td class="text-center">

                                                <a href='#modal-list-trans-dofn-test' class='modal-list-data-action2'
                                                    data-toggle='modal' data-id='{{ $extensions->extension_code }}'
                                                    data-org='{{ $extensions->org_profile_id }}'
                                                    data_year={{ $extensions->year }}>
                                                    <strong style="font-weight: bolder;">{{ $extensions->extension_code }}
                                                        <i style="font-size: 10pt; padding-left:10px;"
                                                            title="GROUP NAME"></i>
                                                    </strong>
                                                    <br />
                                                </a>
                                                <div id="modal-list-trans-dofn-test" class="modal fade" tabindex="-1"
                                                    role="dialog" aria-hidden="true" style="display: none;">
                                                    <div class="modal-dialog modal-lg">
                                                        <div class="modal-content">
                                                            <div class="modal-header text-center">
                                                                <h2 class="modal-title"><i class="fa fa-info-circle"></i>
                                                                    <span id="modal-list-data-header">Check
                                                                        Accumulative</span>
                                                                </h2>
                                                            </div>
                                                            <div class="modal-body">
                                                                <div class="row data-script">
                                                                    select sum(fis.item_amt) <br />
                                                                    from sc_request_item sri , fl_item_summary fis <br />
                                                                    where fis.REQUEST_ITEM_ID = sri.REQUEST_ITEM_ID <br />
                                                                    and fis.FINANCIAL_YEAR = <span
                                                                        id ='val_extension_year'></span> <br />
                                                                    and fis.ORG_PROFILE_ID = <span
                                                                        id='val_org_profile_id'></span> <br />
                                                                    and sri.QT_ID is null <br />
                                                                    and fis.CONTRACT_ID is null <br />
                                                                    and sri.item_id in (select item_id from cm_item where
                                                                    EXTENSION_CODE like '<span
                                                                        id='val_extension_code'></span>%')
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </td>

                                        </tr>
                                    @endforeach
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    @endif

    <!--Supplier INFO-->
    @if ($listed != null)
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> SUPPLIER<font color="yellow"></font></strong></h1>
            </div>
            @if (Auth::user()->isSpecialistUsers())
                @if ($Rniklan == null)
                    <div class="col-md-12">
                        <h5><strong> SUPPLIER RESPOND </strong></h5>
                        <table id="suppliers-respond-datatable"
                            class="table table-vcenter table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="text-center">Ranking</th>
                                    <th class="text-center">Supplier Name</th>
                                    @if (Auth::user()->isApproverTesting())
                                        <th class="text-center">Rate</th>
                                        <th class="text-center">Total</th>
                                    @endif
                                    <th class="text-center">Delivery Term</th>
                                </tr>
                            </thead>
                            <tbody>

                                @foreach ($respond as $list)
                                    <tr>
                                        <td class="text-center">{{ $list->ranking }}</td>
                                        <td class="text-center">{{ $list->company_name }}</td>
                                        @if (Auth::user()->isApproverTesting())
                                            <td class="text-center">{{ $list->rate_per_uom }}</td>
                                            <td class="text-center">{{ $list->answer_numeric }}</td>
                                        @endif
                                        <td class="text-center">{{ $list->cl_delivery_term }}</td>
                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>
                @endif
            @endif

            <div class="row">

                <div class="col-md-4">
                    <h5><strong> SUPPLIER LIST </strong></h5>
                    <table id="suppliers-datatable"
                        class="table table-vcenter table-condensed table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th class="text-center">Is Submitted</th>
                                <th class="text-center">Supplier Name</th>
                                <th class="text-center">eP Number</th>
                            </tr>
                        </thead>
                        <tbody>

                            @foreach ($listed as $supplier)
                                <tr>
                                    <td class="text-center">{{ $supplier->submitted }}</td>
                                    <td class="text-left">{{ $supplier->supplier_name }}</td>
                                    <td class="text-left content-link">
                                        <a class="modal-list-data-action"
                                            href="{{ url('/find/mofno') }}/{{ $supplier->ep_no }}" target='_blank'>
                                            {{ $supplier->ep_no }} </a> ({{ $supplier->mof_no }})
                                    </td>
                                </tr>
                            @endforeach

                        </tbody>
                    </table>
                </div>

                <div class="col-md-8 table-responsive">
                    <h5><strong> ITEM </strong></h5>
                    <strong>
                        <font color="red">If TEMP_ITEM_CODE not null, supplier need to assign code item </font><br />
                    </strong>
                    <table id="after_supplier_response_datatable"
                        class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Item Name</th>
                                <th class="text-center">Item Description</th>
                                <th class="text-center">Temp Item Code</th>
                                <th class="text-center">Item Code</th>
                                <th class="text-center">Order Quantity</th>
                                <th class="text-center">Fulfilment Type</th>
                                <th class="text-center">UOM Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ($Rniklan != null)
                                @foreach ($Rniklan as $iklan)
                                    <tr>
                                        <td class="text-center"><a href='#modal-list-trans-dofn10'
                                                class='modal-list-data-action10' data-toggle='modal'
                                                data-url='/find/dp-summary/item/{{ $iklan->request_item_id }}/{{ $iklan->request_note_no }}'
                                                data-id='{{ $iklan->request_item_id }}'>
                                                <strong style="font-weight: bolder;">{{ $iklan->item_name }}
                                                    <i style="font-size: 10pt; padding-left:10px;"
                                                        title="Supplier Reply"></i>
                                                </strong>
                                                <br />
                                            </a>
                                        </td>
                                        <td class="text-center">{{ $iklan->item_desc }}</td>
                                        <td class="text-center">{{ $iklan->temp_item_code }}</td>
                                        <td class="text-center">{{ $iklan->extension_code }}</td>
                                        <td class="text-center">{{ $iklan->order_qty }}</td>
                                        <td class="text-center">{{ $iklan->jenis_pemenuhan }}</td>
                                        <td class="text-center">{{ $iklan->uom_name }}</td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                    <div class="text-center"><a href='#modal-list-trans-dofn' class='modal-list-data-action10'
                            data-toggle='modal'>
                            <strong style="font-weight: bolder;">
                                <i style="font-size: 10pt; padding-left:10px;" title="Supplier Reply"></i>
                            </strong>
                            <br />
                        </a>
                        <div id="modal-list-trans-dofn10" class="modal fade" tabindex="-1" role="dialog"
                            aria-hidden="true" style="display: none;">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header text-center">
                                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                id='modal-list-data-header'>Supplier Reply</span></h2>
                                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                                        </div>
                                        <table
                                            class="statusPOCO1-datatable table table-vcenter table-condensed table-bordered table-responsive">

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!--PR INFO-->

    @if ($asigneeNameApproverRN != null)
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> USER TASK </strong></h1>
            </div>

            @foreach ($asigneeNameApproverRN as $docNumber => $data)
                <div class="task-group">
                    <h4 style="font-weight: bold; color: black; border-bottom: 1px solid #ccc;">
                        {{ $docNumber }}
                    </h4>
                    @foreach ($data as $task)
                        <div class="task-item" style="margin-bottom: 15px; padding-left: 10px;">
                            <h4 style="color: #333;">
                                Task: {{ $task['taskName'] }}
                            </h4>
                            @if (!empty($task['assigneeDetails']) && isset($task['assigneeDetails'][0]['companyName']))
                                <p style="font-style: italic; color: #666;">
                                    Company: {{ $task['assigneeDetails'][0]['companyName'] }}
                                </p>
                            @endif
                            <p>Assignees:</p>
                            @foreach ($task['assigneeDetails'] as $assignee)
                                <a class="modal-list-data-action"
                                    href="{{ url('/find/userlogin') }}?login_id={{ $assignee['loginId'] }}"
                                    target='_blank' style="display: inline-block; margin-right: 5px;">
                                    {{ $assignee['userName'] }}
                                </a>
                                {{ !$loop->last ? ',' : '' }}
                            @endforeach
                        </div>
                    @endforeach
                </div>
                <hr />
            @endforeach
        </div>
    @endif

    @if ($tracking_diary != null)
        <div class="block table-responsive">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> STATUS INFO </strong></h1>
            </div>
            <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Doc Type</th>
                        <th class="text-center">Doc No</th>
                        <th class="text-center">Action Description</th>
                        <th class="text-center">Actioned Date</th>
                        <th class="text-center">Status</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($tracking_diary as $status)
                        <tr>
                            <td class="text-center">{{ $status->doc_type }}</td>
                            <td class="text-center list-trans-dofn-wf">
                                <a href='#modal-list-trans-dofn-wf' class='modal-list-data-action ' data-toggle='modal'
                                    data-url='/find/trans/docno/workflow/{{ $status->doc_no }}'
                                    data-title='Status WorkFlow Search By {{ $status->doc_no }}'>
                                    <strong style="font-weight: bolder;">
                                        {{ $status->doc_no }} </strong><br />
                                </a>

                            </td>
                            <td class="text-center">{{ $status->action_desc }}</td>
                            <td class="text-center">{{ $status->actioned_date }}</td>
                            <td class="text-center">{{ $status->status_name }}</td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </DIV>
    @endif
    <div id="modal-list-trans-dofn-wf" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List
                            Title</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    @if (
        $asigneeNameApproverRN != null &&
            isset($asigneeNameApproverRN[$docNumber]) &&
            $asigneeNameApproverRN[$docNumber][0]['taskName'] === 'Submit PR')
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> REQUEST NOTE TASK </strong></h1>
            </div>
            @if (isset($approverRN))
                @foreach ($approverRN as $key => $row)
                    <strong style="font-weight: bolder; color: black">
                        <h5><strong>
                                @if ($asigneeNameApproverRN != null)
                                    @foreach ($asigneeNameApproverRN as $docNo => $rows)
                                        @foreach ($rows as $row)
                                            @if (!empty($row['assigneeDetails']) && isset($row['assigneeDetails'][0]))
                                                Nota minta {{ $docNo }} berada ditugasan
                                                <a href="{{ url('/find/userlogin') }}?login_id={{ $row['assigneeDetails'][0]['loginId'] }}"
                                                    target='_blank'>
                                                    {{ $row['assigneeDetails'][0]['userName'] }}</a> untuk penciptaan
                                                permintaan
                                                pembelian.
                                                <br><br>
                                            @endif
                                        @endforeach
                                    @endforeach
                                @endif
                @endforeach
            @endif
        </div>
    @endif

    @if ($items != null && $items[0]->code_name == 'One-Off')
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i></h1>
                <h1><strong> PURCHASE REQUEST ITEM<font color="yellow"></font></strong></h1>
            </div>

            <div class="col-md-14 table-responsive">
                <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">JENIS PEMENUHAN</th>
                            <th class="text-center">KATEGORI JENIS PEROLEHAN</th>
                            <th class="text-center">TITLE</th>
                            <th class="text-center">SUPPLIER NAME</th>
                            <th class="text-center">Day of Delivery</th>
                            <th class="text-center">Assignee to</th>
                            <th class="text-center">PR Number</th>
                            <th class="text-center">PR Status</th>
                            <th class="text-center">REQ ITEM ID</th>
                            <th class="text-center">REQ NOTE DTL ID</th>
                            <th class="text-center">ITEM NAME</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach ($items as $SuppItem)
                            <tr>
                                <td class="text-left">{{ $SuppItem->code_name }}</td>
                                <td class="text-left">{{ $SuppItem->pcode }}</td>
                                <td class="text-left">{{ $SuppItem->procurement_title }} <h6><small>
                                            <font color="red">length ({{ $SuppItem->len }})</font>
                                        </small></h6>
                                </td>
                                <td class="text-left">{{ $SuppItem->supplier_name }}</td>
                                <td class="text-center">{{ $SuppItem->delivery_term }}</td>
                                <td class="text-left">{{ $SuppItem->user_name }}</td>
                                <td class="text-left">
                                    <a class="modal-list-data-action"
                                        href="{{ url('/find/fn-summary/') }}?doc_no={{ $SuppItem->doc_no }}"
                                        target='_blank'>
                                        {{ $SuppItem->doc_no }}</a>
                                </td>
                                <td class="text-left">{{ $SuppItem->status_pr }}</td>
                                <td class="text-center">{{ $SuppItem->reqdetailid }}
                                    @if ($itemproblem != null)
                                        @if ($SuppItem->try == true)
                                            <a href='#modal-list-trans-dofn-test1' class='modal-list-data-action2'
                                                data-toggle='modal' data-req_item_id='{{ $SuppItem->reqdetailid }}'>
                                                <strong style="font-weight: bolder;">
                                                    <i class="gi gi-circle_exclamation_mark  text-danger"
                                                        style="font-size: 10pt; padding-left:10px;"
                                                        title="Id is not same!"></i>
                                                </strong><br />
                                            </a>
                                            <div id="modal-list-trans-dofn-test1" class="modal fade" tabindex="-1"
                                                role="dialog" aria-hidden="true" style="display: none;">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header text-center">
                                                            <h2 class="modal-title"><i class="fa fa-info-circle"></i>
                                                                <span id="modal-list-data-header">Update
                                                                    REQUEST_SUPPLIER_ITEM_ID</span>
                                                            </h2>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">

                                                                @foreach ($itemproblem as $itemproblems)
                                                                    @if (count($itemproblems) > 0)
                                                                        <div class="col-sm-12" class="text-left">
                                                                            <strong>REQUEST NOTE DTL ID :
                                                                                {{ $itemproblems[0]->request_note_dtl_id }}</strong><br />
                                                                            --pre <br />
                                                                            <strong>select * from SC_REQUEST_NOTE_DTL where
                                                                                REQUEST_NOTE_ID =
                                                                                {{ $SuppItem->request_note_id }} and
                                                                                REQUEST_ITEM_ID =
                                                                                {{ $itemproblems[0]->req_item_id }};
                                                                            </strong><br /><br />
                                                                            --patch <br />
                                                                            <strong>update SC_REQUEST_NOTE_DTL set
                                                                                REQUEST_SUPPLIER_ITEM_ID =
                                                                                {{ $itemproblems[0]->requestsupplieritemid }}
                                                                                where REQUEST_NOTE_ID =
                                                                                {{ $SuppItem->request_note_id }} and
                                                                                REQUEST_ITEM_ID =
                                                                                {{ $itemproblems[0]->req_item_id }};</strong><br /><br />
                                                                            --post<br />
                                                                            <strong>select * from SC_REQUEST_NOTE_DTL where
                                                                                REQUEST_NOTE_ID =
                                                                                {{ $SuppItem->request_note_id }} and
                                                                                REQUEST_ITEM_ID =
                                                                                {{ $itemproblems[0]->req_item_id }};
                                                                            </strong><br /><br />
                                                                            <div class="text-center spinner-loading"
                                                                                style="padding: 20px; display: none;">
                                                                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                                                                            </div>
                                                                            <div class="trans-div-detail"></div>
                                                                        </div>
                                                                    @endif
                                                                @endforeach

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                </td>

                                <td class="text-center">
                                    @if ($SuppItem->try === '!')
                                        <a class="modal-list-data-action" href="{{ url('/find/patch-ep') }}"
                                            target="_blank"> {{ $SuppItem->request_note_dtl_id }}
                                        </a>
                                    @else
                                        {{ $SuppItem->request_note_dtl_id }}
                                    @endif
                                </td>
                                <td class="text-left">{{ $SuppItem->item_name }}</td>
                            </tr>
                        @endforeach

                    </tbody>
                </table>
                @if ($findFLAddrErrorPrices != null)
                    <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th class="text-center">Item Name</th>
                                <th class="text-center">Request Item Id</th>
                                <th class="text-center">Supplier Item Price Id (RN)</th>
                                <th class="text-center">Supplier Item Price Id (FL)</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($findFLAddrErrorPrices as $errorsup)
                                <tr>
                                    <td class="text-center">{{ $errorsup->name }}</td>
                                    <td class="text-center">{{ $errorsup->addr_request_item_id }}</td>
                                    <td class="text-center">{{ $errorsup->supplier_item_price_id }}

                                    </td>
                                    <td class="text-center">{{ $errorsup->latest_supplier_item_price_id }}
                                        <a href='#modal-list-trans-dofn4' class='modal-list-data-action'
                                            data-toggle='modal'
                                            data-url='/find/dp-summary/{{ $errorsup->latest_supplier_item_price_id }}'
                                            data-title='Update Supplier Item Price Id {{ $errorsup->latest_supplier_item_price_id }}'>
                                            <strong style="font-weight: bolder;">
                                                <i class="gi gi-circle_exclamation_mark  text-danger"
                                                    style="font-size: 10pt; padding-left:10px;"
                                                    title="Id is not same!"></i>
                                            </strong><br />
                                        </a>
                                        <!-- MODAL:-->
                                        <div id="modal-list-trans-dofn4" class="modal fade" tabindex="-1"
                                            role="dialog" aria-hidden="true" style="display: none;">
                                            <div class="modal-dialog modal-lg">
                                                <div class="modal-content">
                                                    <div class="modal-header text-center">
                                                        <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span
                                                                id="modal-list-data-header">Update
                                                                SUPPLIER_ITEM_PRICE_ID</span></h2>
                                                    </div>
                                                    <div class="modal-body">
                                                        <div class="row">
                                                            @if ($findFLAddrErrorPrices)
                                                                @foreach ($findFLAddrErrorPrices as $supproblems)
                                                                    <div class="col-sm-12" class="text-left">--pre <br />
                                                                        <strong>select
                                                                            request_item_id,supplier_item_price_id,ordered_qty,unit_price,
                                                                            gst_unit_price,ordered_amt,addr.*
                                                                            from FL_FULFILMENT_ITEM_ADDR addr where
                                                                            fulfilment_item_addr_id in
                                                                            ({{ $supproblems->fulfilment_item_addr_id }})
                                                                            AND not exists (SELECT * FROM FL_fulfilment_item
                                                                            item WHERE item.FULFILMENT_ITEM_ID =
                                                                            addr.FULFILMENT_ITEM_ID ); </strong><br /><br />
                                                                        --patch <br />
                                                                        <strong>
                                                                            update FL_FULFILMENT_ITEM_ADDR addr set
                                                                            SUPPLIER_ITEM_PRICE_ID
                                                                            ={{ $supproblems->latest_supplier_item_price_id }},
                                                                            unit_price ='{{ $supproblems->unit_price }}',
                                                                            gst_unit_price
                                                                            ='{{ $supproblems->unit_price }}',
                                                                            ordered_amt
                                                                            = '{{ $supproblems->total }}'
                                                                            where
                                                                            fulfilment_item_addr_id in
                                                                            ({{ $supproblems->fulfilment_item_addr_id }})
                                                                            AND not exists (SELECT * FROM FL_fulfilment_item
                                                                            item WHERE item.FULFILMENT_ITEM_ID =
                                                                            addr.FULFILMENT_ITEM_ID );</strong><br /><br />
                                                                        --post<br />
                                                                        <strong>select
                                                                            request_item_id,supplier_item_price_id,ordered_qty,unit_price,
                                                                            gst_unit_price,ordered_amt,addr.*
                                                                            from FL_FULFILMENT_ITEM_ADDR addr where
                                                                            fulfilment_item_addr_id in
                                                                            ({{ $supproblems->fulfilment_item_addr_id }})
                                                                            AND not exists (SELECT * FROM FL_fulfilment_item
                                                                            item WHERE item.FULFILMENT_ITEM_ID =
                                                                            addr.FULFILMENT_ITEM_ID ); </strong><br /><br />
                                                                        <div class="text-center spinner-loading"
                                                                            style="padding: 20px; display: none;">
                                                                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                                                                        </div>
                                                                        <div class="trans-div-detail"></div>
                                                                    </div>
                                                                @endforeach
                                                            @endif
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                @endif
            </div>
            <!--LOA INFO-->
        @endif
        @if($loaitems != null)
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1 style="display: flex; justify-content: space-between; align-items: center;">
                        <strong>LOA</strong>
                        <strong>
                            @if (!empty($differentRnSyorNumbers) && count($differentRnSyorNumbers) > 0)
                                <font color="yellow">
                                    Di syorkan semula :
                                    <a class="modal-list-data-action"
                                        href="{{ url('/find/dp-summary/') }}?doc_no={{ urlencode(implode(',', $differentRnSyorNumbers)) }}"
                                        target="_blank">
                                        {{ implode(', ', $differentRnSyorNumbers) }}
                                    </a>
                                </font>
                            @endif
                        </strong>
                    </h1>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>FILE REFERENCE NUMBER: </strong>
                        <font color="black">{{ $loaitems[0]->file_no }}</font><br />
                        <strong>CONTRACT TYPE : </strong>
                        <font color="black">{{ $loaitems[0]->contract_type_id }}</font><br />
                        <strong>CONTRACT DURATION : </strong>
                        <font color="black">{{ $loaitems[0]->contract_duration }}</font><br />
                        <strong>ISSUE DATE : </strong>
                        <font color="black">{{ $loaitems[0]->issue_date }}</font><br />
                        <strong>ACK DATE : </strong>
                        <font color="black">{{ $loaitems[0]->ack_due_date }}</font><br />
                        <strong>PROCUREMENT METHOD : </strong>
                        <font color="black">{{ $loaitems[0]->procurement_mode_id }}</font>
                    </address>
                </div>
                <div class="col-md-4">
                    <address>
                        <strong>EFFECTIVE DATE : </strong>
                        <font color="black">{{ $loaitems[0]->contract_eff_date }}</font><br />
                        <strong>EXPIRY DATE : </strong>
                        <font color="black">{{ $loaitems[0]->contract_exp_date }}</font><br />
                        <strong>SIGN DATE : </strong>
                        <font color="black">{{ $loaitems[0]->supplier_sign_date }}</font><br />
                        <strong>SIGN BY : </strong>
                        <font color="black">{{ $loaitems[0]->supplier_ack }}</font><br />
                        <strong>WITNESS BY : </strong>
                        <font color="black">{{ $loaitems[0]->witness_name }}</font><br />
                        <strong>AGREEMENT : </strong>
                        <font color="black">{{ $loaitems[0]->is_agreement_req }}</font><br />
                        <strong>IS BOND? : </strong>
                        <font color="black">{{ $loaitems[0]->is_bond_req }}</font><br />
                    </address>
                </div>
                <div class="col-md-14">
                    <table id="loa_summary_datatable"
                        class="table table-vcenter table-condensed table-bordered table-responsive">
                        <thead>
                            <tr>
                                <th class="text-center">Loa Number</th>
                                <th class="text-center">Title</th>
                                <th class="text-center">Fulfilment Type</th>
                                <th class="text-center">Supplier Name</th>
                                <th class="text-center">Procurement Type</th>
                                <th class="text-center">Contract Duration</th>
                                <th class="text-center">Assign To</th>
                                <th class="text-center">Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($loaitems as $list)
                                <tr>
                                    <td class="text-center">{{ $list->loa_no }}</td>
                                    <td class="text-center">{{ $list->procurement_title }}</td>
                                    <td class="text-center">{{ $list->fulfilment_type_id }}</td>
                                    <td class="text-center">{{ $list->supplier_name }}</td>
                                    <td class="text-center">{{ $list->procurement_type_cat_id }}</td>
                                    <td class="text-center">{{ $list->contract_duration }}</td>
                                    <td class="text-center">{{ $list->user_name }} <br /> {{ $list->login_id }}</td>
                                    <td class="text-center">{{ $list->status_name }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                @if ($loaitemarrays != null)
                    <div class="col-md-14 table-responsive">
                        <table id="summary-datatable"
                            class="table table-vcenter table-condensed table-bordered table-responsive">
                            <thead>
                                <tr>
                                    <th class="text-center">Loa Number</th>
                                    <th class="text-center">Request Item Id</th>
                                    <th class="text-center">Item Name</th>
                                    <th class="text-center">Quantity</th>
                                    <th class="text-center">Freq. per UOM</th>
                                    <th class="text-center">No. UOM per day</th>
                                    <th class="text-center">No. UOM per month</th>
                                    <th class="text-center">Duration (Month)</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($loaitemarrays as $loaarray)
                                    <tr>
                                        <td class="text-center">{{ $loaarray->loa_no }}</td>
                                        <td class="text-center">{{ $loaarray->request_item_id }}</td>
                                        <td class="text-center">{{ $loaarray->item_name }}</td>
                                        <td class="text-center">{{ $loaarray->qty }}</td>
                                        <td class="text-center">{{ $loaarray->cl_uom_frequency }}</td>
                                        <td class="text-center">{{ $loaarray->cl_uom_per_day }}</td>
                                        <td class="text-center">{{ $loaarray->cl_uom_per_month }}</td>
                                        <td class="text-center">{{ $loaarray->cl_duration_month }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @endif
            </div>
    @endif

    <div id="modal-list-trans-item_clone" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
        style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Item
                            Clone Info</span></h2>
                </div>
                <div class="modal-body">
                    <table id="item_clone_info-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">Request Item Id</th>
                                <th class="text-center">Quote No</th>
                            </tr>
                        </thead>
                        <tbody>
                            @if ($listIsItemClone != null)
                                @foreach ($listIsItemClone as $listItemClone)
                                    <tr>
                                        <td class="text-center"> {{ $listItemClone->request_item_id }}</td>
                                        <td class="text-center">
                                            <a class="modal-list-data-action"
                                                href="{{ url('/find/dp-summary/') }}?doc_no={{ $listItemClone->quote_no }}"
                                                target='_blank'>{{ $listItemClone->quote_no }}</a>
                                        </td>
                                    </tr>
                                @endforeach
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            TablesDatatables.init();
        });
        App.datatables();

        function initializeDataTable(elementId) {
            $(elementId).dataTable({
                order: [
                    [0, "asc"]
                ],
                lengthMenu: [
                    [5, 10, 15, 20, -1],
                    [5, 10, 15, 20, 'All']
                ]
            });
        }
        initializeDataTable('#suppliers-respond-datatable');
        initializeDataTable('#sq_clone_list-datatable');
        initializeDataTable('#item_clone_info-datatable');
        initializeDataTable('#item-info-datatable');
        initializeDataTable('#zonal_datatable');
        initializeDataTable('#item_sq_datatable');
        initializeDataTable('#rnapp_ptj_datatable');
        initializeDataTable('#after_supplier_response_datatable');
        initializeDataTable('#loa_summary_datatable');

        $('#tracking-diary-datatable').dataTable({
            order: [
                [4, "desc"],
                [0, "desc"]
            ],
            columnDefs: [],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, 50, -1],
                [10, 20, 30, 50, 'All']
            ]
        });

        var APP_URL = {!! json_encode(url('/')) !!}

        $(document).on("click", '.modal-list-data-action2', function() {
            $('#modal-list-data-header').text($(this).attr('data-title'));
            let itemId = $(this).attr('data-id');
            let year = $(this).attr('data_year');
            let itemId1 = $(this).attr('data-org');
            let request_note_id = $(this).attr('data-request_note_id');
            let
                req_item_id = $(this).attr('data-req_item_id');
            let
                requestsupplieritemid = $(this).attr('data-requestsupplieritemid');
            console.log(req_item_id);
            $('#val_extension_code').text(itemId);
            $('#val_extension_year').text(year);
            $('#val_org_profile_id').text(itemId1);
            $('#val_request_note_id').text(request_note_id);
            $('#val_req_item_id').text(req_item_id);
            $('#val_requestsupplieritemid').text(requestsupplieritemid);
        });

        $(document).ready(function() {

            $('.list-trans-dofn-wf').on("click", '.modal-list-data-action', function() {

                $('.spinner-loading').show();
                $('.trans-div-detail').html('Please wait ...').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('.trans-div-detail').html($data).fadeIn();
                    }
                });

            });

        });

        $(document).ready(function() {

            $('.list-trans-dofn').on("click", '.modal-list-data-action', function() {
                console.log('masuk');

                $('.spinner-loading').show();
                $('.trans-div-detail').html('Please wait ...').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $data = $(data)
                        $('.spinner-loading').hide();
                        $('.trans-div-detail').html($data).fadeIn();
                    }
                });

            });

        });

        $(document).on("click", '.modal-list-data-action10', function() {
            $('#modal-list-data-header').text($(this).attr('data-title'));
            $('.spinner-loading').show();
            $('#statusPOCO1-datatable').hide();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    data = $(data)
                    console.log(data);
                    $('.spinner-loading').hide();
                    $('.statusPOCO1-datatable').html(data).fadeIn();
                    $('#statusPOCO1-datatable').dataTable({
                        order: [
                            [0, "asc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    console.log($data);
                    $('.spinner-loading').hide();
                }
            });
        });

        $('.modal-list-data-rnapprover').on("click", '.modal-list-data-action3', function() {
            $('#rnapp_try').hide();
            $('.spinner-loading').show();
            $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.rnapp_try').html($data).fadeIn();
                    $('#rnapp_try').dataTable({
                        order: [
                            [0, "desc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

        });
    </script>
@endsection
