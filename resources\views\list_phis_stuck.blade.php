@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>PHIS Stuck BPM <br>
                <small>PHIS Stuck List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong></strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tiada rekod</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>PHIS Stuck List </strong>
                        <small></small>
                    </h1>
                </div>

                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">REQUEST DATE</th>
                            <th class="text-center">DOC CO</th>
                            <th class="text-center">DOC CR</th>
                            <th class="text-center">BPM INSTANCE</th>
                            <th class="text-center">BPM COMPOSITE</th>
                            <th class="text-center">STATUS DOC</th>
                            <th class="text-center">RESOLVED</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->request_date }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/bpm/task/docno')}}/{{ $data->doc_co }}" >{{ $data->doc_co }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/bpm/task/docno')}}/{{ $data->doc_cr }}" >{{ $data->doc_cr }}</a></td>
                                <td class="text-center">{{ $data->bpm_instance }}</td>
                                <td class="text-center">{{ $data->bpm_composite }}</td>
                                <td class="text-center">{{ $data->status_name }}</td>
                                <td class="text-center">{{App\Services\EPService::$YES_NO[ $data->status ] }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



