<?php

namespace App\Http\Controllers;

use App\Services\Traits\BpmApiService;
use DB;
use Illuminate\Http\Request;
use Log;
use App\EpSupportActionLog;
use Carbon\Carbon;
use App\Services\Traits\SourcingService;

class BpmApiServiceManagerController extends Controller {

    use BpmApiService;
    use SourcingService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function searchServiceManager(Request $request) {

        $listdataService = null;
        $statusApi = null;

        $listService = $this->findApiBPMGetListServiceManager();
        if ($listService["status"] != null && $listService["status"] === 'Success') {
            $listdataService = $listService["result"];
        }else{
            $statusApi = $listService["result"];
        }

        return view('bpm_api.service_manager', [
            'listdata' => $listdataService,
            'status_api' => $statusApi
        ]);
    }

    public function createServiceManager($process, Request $request) {

        $data = collect([]);
        $url = $request->url;
        $trigger = $request->trigger;
        $elements = $request->elements; 
        
        $createService = $this->findApiBPMCreateServiceManager($url, $process, $trigger, $elements);

        $actionTypeLog = 'Web Service';

        $parameters = collect([]);
        $parameters->put("name", $url);
        $parameters->put("process", $process);
        $parameters->put("trigger", $trigger);
        $parameters->put("elements", $elements);
        $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
        $actionName = 'BPM-Create-Service-Manager';

        EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $createService["status"]);

        return $createService;
    }

    public function detailService(Request $request) {
        
        $qtId = $request->value;
        $result = $this->qtSiteVisitDetail($qtId);
        
        return $result;
        
    }
}
