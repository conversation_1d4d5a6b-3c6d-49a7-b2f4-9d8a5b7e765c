@extends('layouts.guest-dash')


@section('content')
<style>
    .dropzone {
        margin-bottom: 20px;
    }
    .dropzone .dz-message {
        margin : 5px 0;
    }
    .dropzone .dz-preview {
        display: block;
    }
    .dropzone .dz-preview .dz-image{
        display:none;
    }
</style>

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Upload And Transfer File <br>
            <small>Upload file for .GPG only. Content file must be <span class="text-info">Clear Text (Not Encrypted)</span> </small>
        </h1>
    </div>

</div>

<div class="block block-alt-noborder full">
    <div class="block">
        <div class="block-title">
            <h2><strong>Upload File</strong></h2>
            <div class="block-options pull-right  action-today">
                <a href="{{url('osb/file/upload')}}" class="btn btn-alt btn-sm btn-default" data-toggle="tooltip" title="" data-original-title="Reset"><i class="fa fa-refresh"></i></a>
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " data-toggle="modal" 
                    data-url="{{url('support/report/log/patch-transfer-file')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                    data-title="List Action Transfer File Today ">View Today Action</a>
            </div>

        </div>

        <form id="form-transfer-file" action="{{url("/osb/file/upload")}}" method="post" class=" dropzone dz-clickable" >
            {{ csrf_field() }}
            <input name="_method" id="_method"  type="hidden" value="POST">
            <div class="dz-default dz-message"><span>Drop file here to upload</span></div>
        </form>


    </div>

    <div class="block" id="panel_file_detail" style="display:none;">
        <div class="block-title" >
            <h1><i class="fa fa-file-text"></i> <strong>File Details</strong></h1>
        </div>

        <h3 class="text-primary bold" id="file-upload-name" >File Name</h3>
        <pre class="line-numbers language-css">
            <code class="language-css" id="file-upload-content"></code>
        </pre>
        
        <h3 class="text-primary bold"  >Encrypted content</h3>
        <pre class="line-numbers language-css">
            <code class="language-css" id="file-upload-content-encrypted"></code>
        </pre>

    </div>

    <div class="block panel_transfer_file" id="panel_transfer_file" style="display:none;">
            <div id="response" class="table-options clearfix display-none">
                <div id="response-msg" class="text-center text-light" colspan="6"></div>
            </div>
            <div class="block-section">
                <h3 class="sub-header text-center"><strong>Transfer File (1GFMAS) </strong></h3>
                <p class="clearfix"><i class="fa fa-send fa-5x text-primary pull-left"></i>
                    Type of file must be <span class="text-success"><strong>APIVE , AR502 , AP516</strong></span>. Other than that, will not proceed transfer file.<br />
                    File will transfer to Temp Folder <span class="text-success"><strong>/batch/Temp</strong></span> refer to clear content file.<br />
                    File will transfer to OUT Folder <span class="text-success"><strong>/batch/1GFMAS/OUT</strong></span> refer to encrypted content file.</span>
                </p>
                
                <p>
                    <a class="btn btn-lg btn-primary btn-block action_transfer" accesskey="title="" data-original-title="Trigger!"
                                       href="#modal_confirm_transfer" data-toggle="modal" ">TRANSFER <i class="fa fa-figter-jet"></i></a>
                </p>
            </div>
        
    </div>
    
    <div id="modal_confirm_transfer" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Are you sure want to transfer this file? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="" method="post" class="form-horizontal form-bordered" onsubmit="return false;">
                                {{ csrf_field() }}
                                <div class="form-group">
                                    <label class="col-md-3 control-label">File Name </label>
                                    <div class="col-md-9">
                                        <input type="hidden" id="transfer_filename" value="" />
                                        <p id="transfer_filename_display" class="form-control-static"></p>
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="button" class="btn btn-sm btn-primary action_confirm_transfer"><i class="gi gi-ok_2"></i> Yes</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div id="wait-modal" class="modal fade" data-backdrop="static" data-keyboard="false" tabindex="-1" role="dialog" aria-hidden="true" style="padding-top: 15%; overflow-y: visible; display: none;">
        <div class="modal-dialog modal-sm">
            <div class="modal-content">
                <div>
                    <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                </div>
            </div>
        </div>
    </div>
    
    @include('_shared._modalListLogAction')
</div>
@if($data && count($data) > 0)
@endif

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
    TablesDatatables.init();
});</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    //form-transfer-file
    Dropzone.options.formTransferFile = {
        paramName: "uploadFile", // The name that will be used to transfer the file
        maxFilesize: 20, // MB
        maxFiles: 1,
        acceptedFiles: '.GPG',
        accept: function (file, done) {
            console.log('File Name: ', file.name);
            done();
        },
        success: function (file, response) {
            $('#panel_transfer_file').hide();
            $('#panel_file_detail').hide();
            if (response && response.status_upload === 'valid') {
                $('#panel_file_detail').show();
                $('#file-upload-name').text(response.file_name);
                $('#file-upload-content').text(response.content);
                $('#file-upload-content-encrypted').text(response.content_encrypted);
                $('#panel_transfer_file').show();

            }else{
                alert('Please make sure you upload a correct file. File content must be a clear text!');
            }
        }
    };
    
    
    $('.panel_transfer_file').on("click",'a.action_transfer', function(){
        var filename = $('#file-upload-name').text();
        $("#transfer_filename").val(filename);
        $("#transfer_filename_display").text(filename);
    });
    $('div.form-actions').on("click",'button.action_confirm_transfer', function(){
        var filename = $("#transfer_filename").val();
        var csrf = $("input[name=_token]").val();

        $("#response-msg").html("");
        $("#response").hide();
        $('#modal_confirm_transfer').modal('hide');
        $('#wait-modal').modal('toggle');

        $.ajax({
            url: "/osb/file/transfer",
            method : "POST",
            dataType : "json",
            data : {"_token":csrf,"filename":filename},
            context: document.body
        }).done(function(resp) {
            console.log(resp);
            if(resp.status === 'success'){
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("<h1> <i class='fa fa-check-circle'></i> "+resp.status_description+" </h1>");
                $('#wait-modal').modal('hide');
            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("<h1> <i class='fa fa-times-circle'></i> "+resp.status_description+" </h1>");
                $('#wait-modal').modal('hide');
            }
        });
    });
</script>    


@endsection
