<?php

namespace App\Services\Traits;

use Log;
use DB;
use App\Services\EPService;
use Carbon\Carbon;

/**
 * Description of Profile Management PMServiceTrait
 *
 * <AUTHOR>
 */
trait SupplierService
{

    /**
     * Get list of softcert request (with cert if exist) each supplier & user
     * @param type $epNo
     * @param type $userId
     * @return type
     */
    public function getListSoftCertRequest($epNo, $userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SOFTCERT_REQUEST as PSR');
        $query->leftJoin('PM_DIGI_CERT as PDC', 'PSR.SOFTCERT_REQUEST_ID', '=', 'PDC.SOFTCERT_REQUEST_ID');
        $query->where('PSR.EP_NO', $epNo);
        $query->where('PSR.USER_ID', $userId);
        $query->select('PSR.SOFTCERT_REQUEST_ID', 'PSR.USER_ID', 'PSR.EP_NO', 'PSR.RECORD_STATUS', 'PSR.CREATED_DATE', 'PSR.CHANGED_DATE', 'PSR.SOFTCERT_PROVIDER');
        $query->addSelect('PSR.IS_FREE', 'PSR.RESPONSE_STATUS', 'PSR.REQUEST_MODE', 'PSR.REMARK', 'PSR.REASON_CODE');
        $query->addSelect('PDC.RECORD_STATUS AS PDC_RECORD_STATUS', 'PDC.CERT_SERIAL_NO', 'PDC.VALID_FROM', 'PDC.VALID_TO', 'PDC.ORN', 'PDC.PRN', 'PDC.CERT_MODE', 'PDC.CERT_ISSUER', 'PDC.CREATED_DATE as PDC_CREATED_DATE', 'PDC.CHANGED_DATE as PDC_CHANGED_DATE');
        $query->orderBy('PSR.CREATED_DATE', 'desc');
        $result = $query->get();
        if (count($result) > 0) {
            return $result;
        }
        return array();
    }

    /**
     * Checking softcert apply ONLINE OR OFFLINE
     */
    public function checkSoftcertIsONLINE($softcertRequestId, $userId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SOFTCERT_REQUEST as PSR');
        $query->join('SM_SOFTCERT_DOC as SD', 'PSR.USER_ID', '=', 'SD.USER_ID');
        $query->where('SD.record_status', 1);
        $query->where('PSR.softcert_request_id', $softcertRequestId);
        $query->where('PSR.user_id', $userId);
        $query->whereRaw("trunc(PSR.changed_date) = trunc(SD.changed_date)");
        return $query->count();
    }

    /**
     * get Personnel With Softcert Request 
     */
    public function getUserPersonnelBySoftcertRequest($icNo, $softcertRequestId)
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("
            select s.company_name,p.personnel_id,p.name,p.identification_no,p.is_softcert,u.record_status, u.softcert_request_id,u.softcert_provider 
            from sm_personnel p, SM_SOFTCERT_REQUEST u, sm_supplier s
            where p.user_id = u.user_id
            and p.appl_id = s.latest_appl_id
            and p.identification_no = ?
            and u.softcert_request_id = ? 
            and p.is_softcert in (3,7)
            ", array($icNo, $softcertRequestId));
        if (count($list) > 0) {
            return $list[0];
        }
        return null;
    }

    /**
     * Gte eP No in SAP_VENDOR_CODE
     * @return String
     */
    public function getEpNoBySapVendorCode($sapVendorCode)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as A');
        $query->where('A.SAP_VENDOR_CODE', $sapVendorCode);
        $query->where('A.RECORD_STATUS', 1);
        $result = $query->first();
        if (count($result) > 0) {
            return $result->ep_no;
        }
        return null;
    }

    /**
     * Gte basic info for supplier.
     * @param $search
     * @return String  eP NO
     */
    public function getEpNoSmSupplier($search)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
            $query->orWhere('A.REG_NO', strtoupper($search))
                ->orWhere('A.COMPANY_NAME', strtoupper($search))
                ->orWhere('A.LATEST_APPL_ID', intval($search));
        });
        $query->orderBy('A.RECORD_STATUS', 'asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj) {
                if ($obj->record_status == 1) {
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }

    /**
     * Gte basic info for supplier.
     * @param $search
     * @return String  eP NO
     */
    public function getEpNoSmSupplierByApplNo($search)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as A');
        $query->join('SM_APPL as B', 'A.SUPPLIER_ID', '=', 'B.SUPPLIER_ID');
        $query->whereNotNull('A.EP_NO');
        $query->where(function ($query) use ($search) {
            $query->orWhere('B.APPL_NO', strtoupper($search))
                ->orWhere('B.APPL_ID', intval($search));
        });
        $query->orderBy('A.RECORD_STATUS', 'asc');
        $result = $query->get();
        if (count($result) > 0) {
            foreach ($result as $obj) {
                if ($obj->record_status == 1) {
                    return $obj->ep_no;
                }
            }
            return $result[0]->ep_no;
        }
        return null;
    }

    /**
     * Gte basic info for supplier.
     * @param type $applID
     * @return type
     */
    public function getBasicSupplierInfo($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_COMPANY_BASIC as SCB');
        $query->leftJoin('SM_COMPANY_ADDRESS as SCD', 'SCB.COMPANY_BASIC_ID', '=', 'SCD.COMPANY_BASIC_ID');
        $query->leftJoin('SM_ADDRESS as SA', 'SCD.ADDRESS_ID', '=', 'SA.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as PD', 'SA.DIVISION_ID', '=', 'PD.DIVISION_ID');
        $query->leftJoin('PM_CITY as PCT', 'SA.CITY_ID', '=', 'PCT.CITY_ID');
        $query->leftJoin('PM_DISTRICT as PDR', 'SA.DISTRICT_ID', '=', 'PDR.DISTRICT_ID');
        $query->leftJoin('PM_STATE as PS', 'SA.STATE_ID', '=', 'PS.STATE_ID');
        $query->leftJoin('PM_COUNTRY as PCR', 'SA.COUNTRY_ID', '=', 'PCR.COUNTRY_ID');
        $query->where('SCB.APPL_ID', $applID);
        $query->where('SCB.REV_NO', DB::raw("(select max(rev_no) from SM_COMPANY_BASIC WHERE APPL_ID=SCB.APPL_ID)"));
        $query->select('SCB.COMPANY_BASIC_ID', 'SCB.APPL_ID', 'SCB.COMPANY_NAME');
        $query->addSelect('SCB.IS_WITH_FEDERAL', 'SCB.IS_WITH_STATE', 'SCB.IS_WITH_STATUTORY', 'SCB.IS_WITH_GLC', 'SCB.IS_WITH_OTHERS', 'SCB.RECORD_STATUS');
        $query->addSelect('SCB.PHONE_COUNTRY', 'SCB.PHONE_AREA', 'SCB.PHONE_NO', 'SCB.FAX_COUNTRY', 'SCB.FAX_AREA', 'SCB.FAX_NO');
        $query->addSelect('SCB.IS_REVENUE_SSM_NULL', 'SCB.IS_ROB_EXPIRY_SSM_NULL', 'SCB.IS_AUTHORIZED_CAPITAL_SSM_NULL', 'SCB.COMPANY_SSM_STATUS', 'SCB.ROB_EXPIRY_DATE', 'SCB.BUSINESS_DESC', 'SCB.ANNUAL_REVENUE_SSM', 'SCB.PAID_UP_CAPITAL_SSM', 'SCB.SSM_COMPANY_COUNTRY');
        $query->addSelect('SA.ADDRESS_ID', 'SA.ADDRESS_1', 'SA.ADDRESS_2', 'SA.ADDRESS_3', 'SA.POSTCODE');
        $query->addSelect('PD.DIVISION_NAME', 'PCT.CITY_NAME', 'PDR.DISTRICT_NAME', 'PS.STATE_NAME', 'PCR.COUNTRY_NAME');
        //$query->orderBy('SCB.REV_NO', 'desc');
        return $query->first();
    }

    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListAttachmentRejectOrCancel($applID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_attachment as att', 'appl.appl_id', '=', 'att.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id', 'appl.supplier_id', 'appl.appl_type', 'appl.appl_no');
        $query->addSelect('att.attachment_id', 'att.doc_type', 'att.file_name', 'att.file_desc', 'att.file_path', 'att.created_date as att_created_date');
        return $query->get();
    }

    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListRemarksRejectOrCancel($applID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('sm_appl as appl');
        $query->join('sm_remark as rem', 'appl.appl_id', '=', 'rem.doc_id');
        $query->where('appl.appl_id', $applID);
        $query->select('appl.appl_id', 'appl.supplier_id', 'appl.appl_type', 'appl.appl_no');
        $query->addSelect('rem.remark_id', 'rem.doc_type', 'rem.doc_id', 'rem.remark', 'rem.created_date as rem_created_date');
        return $query->get();
    }

    /**
     * Get List Reject Reason on APPL
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListApplRejectReason($applID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_APPL_REJECT_REASON rej');
        $query->join('PM_REASON_DESC as rea', 'rea.reason_id', '=', 'rej.reason_id');
        $query->where('rej.appl_id', $applID);
        $query->where('rej.record_status', 1);
        $query->where('rea.language_code', 'en');
        $query->select('rea.reason_desc', 'rea.reason_id');
        $query->addSelect('rej.appl_id', 'rej.appl_reject_reason_id', 'rej.changed_date', 'rej.created_date');
        return $query->get();
    }

    /**
     * Get List Section Review
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getListApplSectionReview($applID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_APPL_SECTION_REVIEW');
        $query->where('appl_id', $applID);
        $query->where('record_status', 1);
        $query->select('appl_section_review_id', 'role_type', 'created_date', 'changed_date', 'remark_date', 'basic_remark', 'staff_remark', 'office_facility_remark', 'trns_network_remark', 'branch_remark', 'associate_remark', 'cert_remark', 'experience_remark', 'capital_remark', 'shareholder_remark', 'equity_comp_remark', 'personnel_remark', 'bank_remark', 'category_remark', 'factory_remark', 'manufacturing_remark', 'machinery_remark', 'sitevisit_remark', 'recommendation', 'remark_to_approver', 'license_remark', 'assoc_body_remark', 'sitevisit_verified_remark', 'supplier_system_remark', 'recomm_reg_status_id');
        return $query->get();
    }

    public function getCountApplicationInquiriesService($appId)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT count(*) FROM sm_appl a, SM_APPL_INQUIRY i WHERE 
            a.appl_id = i.appl_id
            AND i.appl_id = ? ",
            array($appId)
        );

        return $result;
    }

    public function getListApplicationInquiriesService($appId)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT a.appl_no,a.status_id, a.IS_ACTIVE_APPL , i.* FROM sm_appl a, SM_APPL_INQUIRY i WHERE 
            a.appl_id = i.appl_id
            AND i.appl_id = ? ",
            array($appId)
        );

        return $result;
    }

    public function getCountApplicationRejectedService($appId)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT 	
            count(*)
            FROM SM_SUPPLIER_CATEGORY sc, 
            PM_CATEGORY_L1 catLvl1,PM_CATEGORY_L2 catLvl2,PM_CATEGORY_L3 catLvl3
            WHERE 
            sc.appl_id = ?
            AND sc.IS_APPROVED_BY_AP = 0 
            AND sc.RECORD_STATUS  = 0
            AND sc.category_l1_id = catLvl1.category_l1_id
            AND sc.category_l2_id = catLvl2.category_l2_id
            AND sc.category_l3_id = catLvl3.category_l3_id",
            array($appId)
        );

        return $result;
    }

    public function getListApplicationRejectedService($appId)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT 	
            sc.PO_REMARK ,
            sc.AP_REMARK ,
            sc.changed_date,
            sc.is_approved_by_po,
            sc.is_approved_by_ap,
            sc.record_status AS cat_sup_status ,
            catLvl1.CATEGORY_L1_CODE||catlvl2.CATEGORY_L2_CODE||catlvl3.CATEGORY_L3_CODE as category_code,
            catLvl1.CATEGORY_L1_CODE,
            catLvl2.CATEGORY_L2_CODE,
            catLvl3.CATEGORY_L3_CODE,
            catLvl1.CATEGORY_NAME as CATEGORY_L1_NAME,
            catLvl2.CATEGORY_NAME as CATEGORY_L2_NAME,
            catLvl3.CATEGORY_NAME as CATEGORY_L3_NAME 
        FROM SM_SUPPLIER_CATEGORY sc, 
          PM_CATEGORY_L1 catLvl1,PM_CATEGORY_L2 catLvl2,PM_CATEGORY_L3 catLvl3
        WHERE 
        sc.appl_id = ?
        AND sc.IS_APPROVED_BY_AP = 0 
        AND sc.RECORD_STATUS  = 0
        AND sc.category_l1_id = catLvl1.category_l1_id
        AND sc.category_l2_id = catLvl2.category_l2_id
        AND sc.category_l3_id = catLvl3.category_l3_id",
            array($appId)
        );

        return $result;
    }

    /**
     * Get Application No (Approved)
     * @param type $supplierID
     * @param type $applID
     * @return type
     */
    public function getWorkFlowSupplierProcess($supplierID, $applID)
    {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   wf.is_current, sup.supplier_id, sup.company_name,
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    appl.record_status,
                    appl.supporting_doc_mode,
                    appl.appl_no,
                    appl.appl_category,
                    appl.is_resubmit,
                    appl.is_active_appl,
                    appl.original_appl_id,
                    appl.supporting_doc_mode,
                    appl.is_appl_valid_with_ssm,
                    appl.reg_status_id,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'
                    and ppd.RECORD_STATUS = 1
                    and pp.RECORD_STATUS = 1
                    and pp.PARAMETER_TYPE = 'AT'
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type ,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    wf.workflow_status_id,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id(+)
                AND appl.appl_id = wf.doc_id
                AND sup.SUPPLIER_ID = ?
                AND appl.appl_id = ?
           ORDER BY wf.created_date DESC, wf.is_current DESC ",
            array($supplierID, $applID)
        );

        return $results;
    }

    /**
     * Get list info for process current application for supplier
     * @param type $supplierID
     * @return type
     */
    public function getInProgressWorkFlowSupplierProcess($supplierID, $applId = null)
    {

        $addWhereApplIdClause = '';
        if ($applId != null) {
            $addWhereApplIdClause = "AND appl.appl_id = '$applId' ";
        }
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT   wf.is_current, sup.supplier_id, sup.company_name,
                    CASE
                       WHEN appl.supplier_type IN ('K', 'J')
                          THEN 'MOF'
                       ELSE 'Basic'
                    END AS supplier_type,
                    sup.ep_no, appl.appl_id,
                    appl.supporting_doc_mode,
                    (select ppd.code_name from pm_parameter pp , pm_parameter_desc ppd where
                    pp.PARAMETER_ID = ppd.PARAMETER_ID and ppd.LANGUAGE_CODE = 'en'
                    and ppd.RECORD_STATUS = 1
                    and pp.RECORD_STATUS = 1
                    and pp.PARAMETER_TYPE = 'AT'
                    and pp.PARAMETER_CODE = appl.appl_type) appl_type ,
                    appl.appl_no,appl.is_active_appl,appl.changed_date,
                    (SELECT usr.user_name
                       FROM pm_user usr
                      WHERE usr.user_id = appl.created_by) appl_created_by,
                    appl.created_date AS appl_created_date, appl.changed_date AS appl_change_date,
                    wf.created_date AS wf_created_date, wf.changed_date AS wf_changed_date, appl.is_questionnaire,
                    wf.workflow_status_id,
                    appl.is_resubmit,
                    appl.status_id  appl_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = appl.status_id
                        AND language_code = 'en')  appl_status,
                    wf.status_id AS wf_status_id,
                    (SELECT status_name
                       FROM pm_status_desc
                      WHERE status_id = wf.status_id
                        AND language_code = 'en') AS wf_Status
               FROM sm_supplier sup,
                    sm_appl appl,
                    sm_workflow_status wf
              WHERE sup.supplier_id = appl.supplier_id
                AND appl.appl_id = wf.doc_id (+)
                AND sup.SUPPLIER_ID = ?
                $addWhereApplIdClause 
                AND appl.IS_ACTIVE_APPL = 1
           ORDER BY wf.created_date DESC, wf.is_current DESC ",
            array($supplierID)
        );

        return $results;
    }

    public function getTrackingDiarySupplierByDocNo($docNo)
    {
        $query = "select d.* ,
                        (SELECT status_name
                                            FROM pm_status_desc
                                           WHERE status_id = d.status_id
                                             AND language_code = 'en') AS status_name,
                        (SELECT usr.user_name
                                FROM pm_user usr
                               WHERE usr.user_id = d.actioned_by) diary_actioned_by                     

                     from pm_tracking_diary d
                     where d.doc_no = ? 
                     order by d.tracking_diary_id desc ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($docNo));
        return $results;
    }

    public function getSmApplDetail($applId)
    {
        $query = "select a.* ,
                        (SELECT status_name
                                            FROM pm_status_desc
                                           WHERE status_id = a.status_id
                                             AND language_code = 'en') AS status_name                   

                     from sm_appl a
                     where a.appl_id = ? ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($applId));
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    public function getSmApplWithWOrkFlowLatestDetail($applId)
    {
        $query = "select a.* ,
                        (SELECT status_name
                                            FROM pm_status_desc
                                           WHERE status_id = a.status_id
                                             AND language_code = 'en') AS status_name   ,
                        s.created_date as status_created_date                     

                     from sm_appl a, sm_workflow_status s 
                     where 
                        a.appl_id = s.doc_id 
                        and a.appl_id = ?  
                        and s.doc_type = 'SR' 
                        and s.is_current = 1 ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($applId));
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    /**
     * Get activation link. This link still not unique by personnel. At least 99% tepat if email is not same others personnel.
     * @param type $companyName
     * @param type $ssmNO
     * @param type $icno
     * @param type $email
     * @return type
     */
    public function getActivationLink($companyName, $ssmNO, $icno, $email)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_NOTIFY_MESSAGE as P');
        $query->where('P.notify_mode', 'D');
        $query->where('P.notification_id', '399999');
        $query->where('P.EMAIL_LIST', $email);
        $query->where('P.subject_param', 'like', $companyName . '%');
        $query->orderBy('CREATED_DATE', 'desc');
        return $query->first();
    }

    /**
     * Get trustgate info from List Migration Trustgate
     * @param type $mofNo
     * @param type $icNo
     * @return type
     */
    protected function getTrustgateDetailInfo($mofNo, $icNo)
    {
        $query = DB::table('softcert_trustgate_mig as stm');
        $query->where('stm.mof_no', $mofNo);
        $query->where('stm.ic_no', $icNo);
        return $query->first();
    }

    /**
     * Get Query for  Supplier Users Info. Not include roles.
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public function getSMSupplierUsersActiveByICNO($icno)
    {
        /*
         * Sample Query test to Oracle Query
         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->where('P.IDENTIFICATION_NO', $icno);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));

        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->get();
    }

    public function getSMSupplierUsersByIcNo($icno)
    {
        /*
         * Sample Query test to Oracle Query
         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->where('P.IDENTIFICATION_NO', $icno);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->orderBy('P.EP_ROLE');
        return $query->get();
    }

    public function getSMSupplierUsersByParam($icno, $epNo)
    {
        /*
         * Sample Query test to Oracle Query
         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->where('P.IDENTIFICATION_NO', $icno);
        $query->where('S.EP_NO', $epNo);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->orderBy('P.EP_ROLE');
        return $query->get();
    }

    public function getSMSupplierUsersActiveBySupplierID($supplierId)
    {
        /*
         * Sample Query test to Oracle Query
         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        $query->where('S.SUPPLIER_ID', $supplierId);
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));

        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.APPL_ID', 'P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'P.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID', 'P.IS_EQUITY_OWNER as P_IS_EQUITY_OWNER');
        $query->addSelect('P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE');
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        $query->orderBy('P.EP_ROLE');
        return $query->get();
    }

    public function getSMSupplierInfo($carian, $type)
    {
        /*
         * Sample Query test to Oracle Query
         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as S');
        $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        if ($type === 'ID') {
            $query->where('S.SUPPLIER_ID', $carian);
        } else if ($type === 'SUPPLIER_NAME' && strlen($carian) > 0) {
            $query->whereRaw("UPPER(S.COMPANY_NAME) =  '" . strtoupper($carian) . "'");
        } else if ($type === 'ePNo' && strlen($carian) > 0) {
            $query->where('S.EP_NO', $carian);
        } else if ($type === 'MOF' && strlen($carian) > 0) {
            $query->where('MA.MOF_NO', $carian);
        } else if ($type === 'ApplNo' && strlen($carian) > 0) {
            $query->leftJoin('SM_APPL as SA', 'SA.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID')
                ->where('SA.APPL_NO', $carian);
        } else {
            return null;  //Must return null, if not meet conditions.
        }
        $query->select('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.BUSINESS_TYPE', 'S.IS_BUMI', 'S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID');
        $query->addSelect('MA.MOF_NO', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');

        //dump($query->toSql());
        return $query->get();
    }

    /**
     * Get Single Object : MOF Detail
     * @param type $supplierId
     */
    public function getSMSupplierMofDetail($supplierId)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_MOF_ACCOUNT as M');
        $query->where('M.SUPPLIER_ID', $supplierId);
        $query->whereRaw('M.EXP_DATE IN ( select max(exp_date) from SM_MOF_ACCOUNT where SUPPLIER_ID = M.SUPPLIER_ID) ');
        $result = $query->get();
        if (count($result) > 0) {
            return $result[0];
        } else {
            return null;
        }
    }

    /**
     * populate $data put another fields
     * @param type $data  = refer $dataObj from query selection
     */
    /*
      SUPPLIER_TEMP
      BASIC_SUPPLIER_ADMIN
      MOF_SUPPLIER_ADMIN
     */
    public function populatePersonnelUserData(&$data)
    {
        $data->is_activate_key = false;
        if (strlen($data->p_ep_role) > 0 && strlen($data->p_email) > 0 && $data->p_record_status == 8) {

            $data->is_activate_key = true;
            $data->content_param = '';
            $contents = '';
            $data->link = 'Supplier Admin will able to resend activation link.';
            $data->activation_key = '';
            $data->is_sent = false;
            $data->activation_changed_date = null;
            if ($data->p_ep_role == 'SUPPLIER_TEMP' || $data->p_ep_role == 'BASIC_SUPPLIER_ADMIN' || $data->p_ep_role == 'MOF_SUPPLIER_ADMIN') {
                $recNotify = $this->getActivationLink($data->company_name, $data->reg_no, $data->p_identification_no, $data->p_email);
                if ($recNotify != null) {
                    $data->content_param = $recNotify->content_param;
                    $contents = explode("|||", $recNotify->content_param);
                    $data->link = $contents[3];
                    $data->activation_key = $contents[2];
                    $data->is_sent = $recNotify->is_sent;
                    $data->activation_changed_date = $recNotify->changed_date;
                }
            }
        }

        $data->u_record_status = $data->u_record_status . ' - ' . EPService::$RECORD_STATUS[$data->u_record_status];
        $data->supp_record_status = $data->s_record_status;
        $data->s_record_status = $data->s_record_status . ' - ' . EPService::$RECORD_STATUS[$data->s_record_status];
        $data->ma_record_status = $data->ma_record_status . ' - ' . EPService::$RECORD_STATUS[$data->ma_record_status];

        $data->p_is_softcert = $data->p_is_softcert . ' - ' . EPService::$SOFTCERT_STATUS[$data->p_is_softcert];
        $data->p_record_status = $data->p_record_status . ' - ' . EPService::$RECORD_STATUS[$data->p_record_status];

        $data->is_equity_owner = EPService::$YES_NO[$data->is_equity_owner];
        $data->is_authorized = EPService::$YES_NO[$data->is_authorized];
        $data->is_contact_person = EPService::$YES_NO[$data->is_contact_person];
        $data->is_contract_signer = EPService::$YES_NO[$data->is_contract_signer];
        $data->is_mgt = EPService::$YES_NO[$data->is_mgt];
        $data->is_director = EPService::$YES_NO[$data->is_director];
        $data->is_bumi = EPService::$YES_NO[$data->is_bumi];

        /** Get Softcert Request && (cert info if exist) * */
        $listSoftCert = $this->getListSoftCertRequest($data->ep_no, $data->user_id);
        if (count($listSoftCert) > 0) {
            foreach ($listSoftCert as $sCert) {
                $sCert->record_status = $sCert->record_status . ' - ' . EPService::$RECORD_STATUS[$sCert->record_status];
                $sCert->is_free = EPService::$YES_NO[$sCert->is_free];

                /** Checking in OSB * */
                $sCert->is_success_SPK010 = 0;
                $sCert->is_success_SPK020 = 0;
                if ($sCert->cert_serial_no == null) {
                    $sCert->is_success_SPK010 = $this->checkSuccessReceiveCertSPKI($sCert->softcert_request_id, $data->p_identification_no);
                    $sCert->is_success_SPK020 = $this->checkSuccessSentSPKI($sCert->softcert_request_id, $data->p_identification_no);
                }

                /** For Trustgate in List Migration * */
                $date = Carbon::parse($sCert->created_date);
                $dateNotTrustgateMigrate = Carbon::create(2017, 12, 31);
                $sCert->is_trustgate = false;
                if ($date->lte($dateNotTrustgateMigrate)) {
                    $sCert->is_trustgate = true;
                    $sCert->is_trustgate_data = false;
                    $trustGateSoftData = $this->getTrustgateDetailInfo($data->mof_no, $data->identification_no);
                    if ($trustGateSoftData) {
                        $sCert->is_trustgate_data = true;
                        $sCert->trustgate_expired_date = $trustGateSoftData->cert_expiry_date;
                    }
                }

                $sCert->type_apply = 'ONLINE';
                $countHasLampiranOnline = $this->checkSoftcertIsONLINE($sCert->softcert_request_id, $data->user_id);
                if ($countHasLampiranOnline == 0) {
                    $sCert->type_apply = 'OFFLINE';
                }


                $sCert->is_expired = null;

                /* Checking is Expired? * */
                if (
                    $sCert->valid_to != null &&
                    Carbon::parse($sCert->valid_to)->lte(Carbon::now())
                ) {
                    $sCert->is_expired = true;
                } else {
                    $sCert->is_expired = false;
                }

                $sCert->is_active_cert = false;
                /* Checking active? * */
                if (
                    $sCert->valid_to != null &&
                    Carbon::parse($sCert->valid_to)->gte(Carbon::now())
                ) {
                    $sCert->is_active_cert = true;
                }
            }
        }
        $data->listSoftCert = $listSoftCert;

        /* Get Total Address Personnel */
        $totalAddressPersonnel = $this->getTotalPersonnelAddress($data->personnel_id);
        $data->total_address = $totalAddressPersonnel;

        /* Get Roles for users has Login ID */
        $roles = $this->getUserRoles($data->user_id);
        $data->roles = $roles;
    }

    /**
     * Get Query for  Supplier Users Info. Not include roles.
     * @param type $dateStart
     * @param type $dateEnd
     * @param type $take
     * @param type $nextSkip
     * @return type
     */
    public function getSMSupplierUsersDetailsByMofNOorEpNo($mofno, $epNo)
    {
        /*
         * Sample Query test to Oracle Query

         */
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_PERSONNEL as P');
        $query->join('SM_SUPPLIER as S', 'P.APPL_ID', '=', 'S.LATEST_APPL_ID');
        $query->join('SM_APPL as A', 'A.APPL_ID', '=', 'P.APPL_ID');
        if ($mofno != null) {
            $query->join('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        if ($epNo != null && $mofno == null) {
            $query->leftJoin('SM_MOF_ACCOUNT as MA', 'S.SUPPLIER_ID', '=', 'MA.SUPPLIER_ID');
        }
        $query->leftJoin('PM_USER as U', 'P.USER_ID', '=', 'U.USER_ID');
        $query->leftJoin('PM_LOGIN_HISTORY as LH', 'U.USER_ID', '=', 'LH.USER_ID');
        //$query->whereNotIn('P.RECORD_STATUS', [9]);
        if ($mofno != null) {
            $query->where('MA.MOF_NO', $mofno);
        }
        if ($epNo != null) {
            $query->where('S.EP_NO', $epNo);
        }
        // SM_PERSONNEL has REV_NO, need to get latest one
        $query->where('P.REV_NO', DB::raw("(select max(rev_no) from SM_PERSONNEL WHERE APPL_ID=P.APPL_ID and IDENTIFICATION_NO = P.IDENTIFICATION_NO)"));

        $query->select('U.USER_ID', 'U.LOGIN_ID', 'U.USER_NAME AS FULLNAME', 'U.NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID');
        $query->addSelect('U.PREFERRED_LANGUAGE', 'U.IS_NOTIFY_BY_SMS');
        $query->addSelect('U.ORG_TYPE_ID', 'U.IDENTIFICATION_NO', 'U.DESIGNATION', 'U.EMAIL', 'U.RECORD_STATUS AS U_RECORD_STATUS');
        $query->addSelect('U.CREATED_DATE', 'U.CHANGED_DATE', 'U.MOBILE_COUNTRY', 'U.MOBILE_AREA', 'U.MOBILE_NO');
        $query->addSelect('U.PHONE_COUNTRY', 'U.PHONE_AREA', 'U.PHONE_NO', 'U.FAX_COUNTRY', 'U.FAX_AREA', 'U.FAX_NO', 'U.SALUTATION_ID');
        $query->addSelect('P.PERSONNEL_ID', 'P.RECORD_STATUS AS P_RECORD_STATUS', 'P.TITLE_ID', 'P.NAME as P_NAME', 'P.IDENTIFICATION_NO as P_IDENTIFICATION_NO', 'P.NATIONALITY_ID as P_NATIONALITY_ID', 'U.IDENTIFICATION_TYPE_ID as P_IDENTIFICATION_TYPE_ID');
        $query->addSelect('P.APPL_ID', 'P.DESIGNATION as P_DESIGNATION', 'P.IS_SOFTCERT as P_IS_SOFTCERT', 'P.EP_ROLE as P_EP_ROLE', 'P.IDENTITY_RESIDENT_STATUS');
        $query->addSelect(DB::raw("(select race_name from pm_race where race_id=p.race_id) as race_name"));
        $query->addSelect('P.CREATED_DATE as P_CREATED_DATE', 'P.CHANGED_DATE as P_CHANGED_DATE', 'P.REV_NO as P_REV_NO');
        $query->addSelect('P.IS_AUTHORIZED', 'P.IS_CONTACT_PERSON', 'P.IS_CONTRACT_SIGNER', 'P.IS_EQUITY_OWNER', 'P.IS_MGT', 'P.IS_DIRECTOR', 'P.IS_BUMI');
        $query->addSelect('P.PHONE_COUNTRY as P_PHONE_COUNTRY', 'P.PHONE_AREA as P_PHONE_AREA', 'P.PHONE_NO as P_PHONE_NO', 'P.MOBILE_COUNTRY as P_MOBILE_COUNTRY', 'P.MOBILE_AREA as P_MOBILE_AREA', 'P.MOBILE_NO as P_MOBILE_NO', 'P.EMAIL as P_EMAIL');
        $query->addSelect('S.SUPPLIER_ID', 'S.COMPANY_NAME', 'S.ESTABLISH_DATE', 'S.EP_NO', 'S.REG_NO', 'S.BUSINESS_TYPE', 'S.STATUS_ID as S_STATUS_ID', 'S.RECORD_STATUS as S_RECORD_STATUS', 'S.CREATED_DATE as S_CREATED_DATE', 'S.CHANGED_DATE as S_CHANGED_DATE');
        $query->addSelect('S.IS_CATALOGUE_UPLOADED', 'S.LATEST_APPL_ID', 'S.LOCAL_AUTHORITY_ID');
        $query->addSelect('A.APPL_NO', 'A.SUPPLIER_TYPE', 'A.APPL_TYPE', 'A.SUPPORTING_DOC_MODE');
        $query->addSelect('MA.MOF_NO', 'MA.EFF_DATE as MA_EFF_DATE', 'MA.EXP_DATE as MA_EXP_DATE', 'MA.RECORD_STATUS AS MA_RECORD_STATUS');
        $query->addSelect('LH.LOGIN_DATE');
        if ($epNo != null && $mofno == null) {
            $query->orderBy('MA.EXP_DATE', 'desc');
            $query->orderBy('P.EP_ROLE');
        } else {
            $query->orderBy('P.EP_ROLE');
        }
        $data = array(
            "sql" => $query->toSql(),
            "parameter" => $query->getBindings()
        );

        //Log::debug(self::class . ' :: ' . __FUNCTION__ . ' >> SQL   :   ' . json_encode($data));
        return $query->get();
    }

    /**
     * Get total catalogue for suppliers
     * @param type $supplierId
     * @return type
     */
    protected function getTotalItemsSupplier($supplierID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_SUPPLIER_ITEM as csi');
        $query->where('csi.supplier_id', $supplierID);
        $query->whereIn('csi.item_status', ['A', 'U']);
        $query->where('csi.record_status', 1);
        return $query->count();
    }

    /**
     * Get total catalogue for suppliers
     * @param type $supplierId
     * @return type
     */
    protected function getItemsSupplier($supplierID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_SUPPLIER_ITEM as csi');
        $query->join('CM_ITEM as ci', 'ci.item_id', '=', 'csi.ITEM_ID');
        $query->leftJoin('PM_UOM as pu', 'csi.UOM_ID', '=', 'pu.UOM_ID');
        $query->where('csi.supplier_id', $supplierID);
        $query->whereIn('csi.item_status', ['A', 'U']);
        $query->where('csi.record_status', 1);
        $query->select('csi.supp_item_id', 'csi.supplier_id', 'csi.item_id', 'csi.item_status', 'csi.uom_id', 'csi.created_date', 'csi.changed_date');
        $query->addSelect('ci.extension_code', 'ci.item_name');
        $query->addSelect('pu.uom_name');
        return $query->get();
    }

    /**
     * Get list for supplier branch
     * @param type $supplierId
     * @return type
     */
    protected function getListSupplierBranch($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_BRANCH as sb');
        $query->join('SM_ADDRESS as sa', 'sb.ADDRESS_ID', '=', 'sa.ADDRESS_ID');
        $query->leftJoin('PM_DIVISION as pd', 'sa.DIVISION_ID', '=', 'pd.DIVISION_ID');
        $query->leftJoin('PM_CITY as pct', 'sa.CITY_ID', '=', 'pct.CITY_ID');
        $query->leftJoin('PM_DISTRICT as pdr', 'sa.DISTRICT_ID', '=', 'pdr.DISTRICT_ID');
        $query->leftJoin('PM_STATE as ps', 'sa.STATE_ID', '=', 'ps.STATE_ID');
        $query->leftJoin('PM_COUNTRY as pcr', 'sa.COUNTRY_ID', '=', 'pcr.COUNTRY_ID');
        $query->leftJoin('SM_GST_SUPPLIER as gst', function ($join) {
            $join->on('gst.BRANCH_CODE', '=', 'sb.BRANCH_CODE')
                ->on('gst.IS_CURRENT', DB::raw('1'));
        });
        $query->where('sb.APPL_ID', $applID);
        $query->where('sb.REV_NO', DB::raw("(select max(rev_no) from SM_SUPPLIER_BRANCH WHERE APPL_ID=sb.APPL_ID  and branch_code = sb.branch_code  )"));
        $query->select('sb.*');
        $query->addSelect('sa.ADDRESS_ID', 'sa.ADDRESS_1', 'sa.ADDRESS_2', 'sa.ADDRESS_3', 'sa.POSTCODE');
        $query->addSelect('pd.DIVISION_NAME', 'pct.CITY_NAME', 'pdr.DISTRICT_NAME', 'ps.STATE_NAME', 'pcr.COUNTRY_NAME');
        $query->addSelect('gst.GST_REG_NO', 'gst.GST_EFF_DATE', 'gst.GST_DECLARED_DATE');
        return $query->get();
    }

    /**
     * Get list for supplier bank
     * @param type $supplierId
     * @return type
     */
    protected function getListSupplierBank($applID)
    {
        return $query = DB::connection('oracle_nextgen_rpt')->select("SELECT fo.FIN_ORG_NAME,
            b.supplier_bank_id,
            b.appl_id,b.account_no, 
            CASE 
                WHEN b.account_no LIKE '% %' OR b.account_no LIKE '%-%' THEN 1
                ELSE 0
            END AS is_invalid_bank_no,
            b.account_purpose,b.is_default_account,b.IS_FOR_HQ,b.BANK_BRANCH,b.changed_date,
            sb.branch_name,sb.BRANCH_CODE,sb.APPL_ID, fo.record_status as bank_status ,
            b.record_status as supplier_bank_status
            FROM SM_SUPPLIER_BANK b
            INNER JOIN PM_FINANCIAL_ORG fo on fo.FINANCIAL_ORG_ID = b.FINANCIAL_ORG_ID
            LEFT JOIN SM_SUPPLIER_BRANCH_BANK bb on bb.SUPPLIER_BANK_ID = b.SUPPLIER_BANK_ID and bb.record_status = 1
            LEFT JOIN SM_SUPPLIER_BRANCH sb on sb.SUPPLIER_BRANCH_ID  = bb.SUPPLIER_BRANCH_ID  and  sb.record_status = 1
            WHERE
            b.appl_id  = ?
            AND b.REV_NO = (SELECT max(x.rev_no) FROM  SM_SUPPLIER_BANK x WHERE x.appl_id = b.appl_id )
            ", array($applID));
    }

    /**
     * Get SapVendorCode for supplier. This to verify this supplier already registered into 1GFMAS or not.
     * @param type $epNo
     * @return type
     */
    protected function getMainSapVendorCode($epNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.record_status', 1);
        $query->whereNull('svc.branch_code');
        return $query->first();
    }

    /**
     * Get SapVendorCode for supplier. This to verify this supplier already registered into 1GFMAS or not.
     * @param type $branchCode
     * @return first row
     */
    protected function getMainSapVendorCodeByBranchCode($epNo, $branchCode)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SAP_VENDOR_CODE as svc');
        $query->where('svc.ep_no', $epNo);
        $query->where('svc.branch_code', $branchCode);
        $query->where('svc.record_status', 1);
        return $query->first();
    }

    /**
     * Get MOF Supplier Status
     * @param type $supplierId
     * @return type
     */
    protected function getSupplierMofStatus($supplierId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT ss.ep_no,sa.appl_id,ss.supplier_id,
                    DECODE (ss.is_bumi, 1, 'Bumi', 'Non-Bumi') bumi_status,
                    CASE
                       WHEN sa.reg_status_id IN ('1')
                          THEN 'Not defined'
                       WHEN sa.reg_status_id IN ('2')
                          THEN 'Bumiputera'
                       WHEN sa.reg_status_id IN ('3')
                          THEN 'Registered'
                       WHEN sa.reg_status_id IN ('4')
                          THEN 'Bumi operate from home'
                       WHEN sa.reg_status_id IN ('5')
                          THEN 'Non bumi operate from home'
                       WHEN sa.reg_status_id IN ('6')
                          THEN 'Joint venture bumi'
                       WHEN sa.reg_status_id IN ('7')
                          THEN 'Joint venture non bumi'
                       WHEN sa.reg_status_id IN ('8')
                          THEN 'Foreign company'
                    END AS type
               FROM sm_appl sa, sm_supplier ss
              WHERE sa.appl_id = ss.latest_appl_id AND ss.supplier_id = ? ",
            array($supplierId)
        );
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    protected function getHistorySupplierMofStatus($applid, $supplierId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT ss.ep_no,sa.appl_id,ss.supplier_id,
                    DECODE (ss.is_bumi, 1, 'Bumi', 'Non-Bumi') bumi_status,
                    CASE
                       WHEN sa.reg_status_id IN ('1')
                          THEN 'Not defined'
                       WHEN sa.reg_status_id IN ('2')
                          THEN 'Bumiputera'
                       WHEN sa.reg_status_id IN ('3')
                          THEN 'Registered'
                       WHEN sa.reg_status_id IN ('4')
                          THEN 'Bumi operate from home'
                       WHEN sa.reg_status_id IN ('5')
                          THEN 'Non bumi operate from home'
                       WHEN sa.reg_status_id IN ('6')
                          THEN 'Joint venture bumi'
                       WHEN sa.reg_status_id IN ('7')
                          THEN 'Joint venture non bumi'
                       WHEN sa.reg_status_id IN ('8')
                          THEN 'Foreign company'
                    END AS type
               FROM sm_appl sa, sm_supplier ss
              WHERE sa.appl_id = ? AND ss.supplier_id = ? ",
            array($applid, $supplierId)
        );
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    /**
     * Get list cert for Supplier
     * @param type $supplierId
     * @return List type
     */
    protected function getSupplierMofVirtCert($supplierId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT  smc.mof_cert_id, DECODE (smc.is_bumi_cert, 1, 'Bumi Cert', 'Mof Cert')  cert_type, smc.cert_serial_no, smc.eff_date, smc.exp_date,
                         smc.record_status,smc.appl_id
                    FROM sm_mof_account sma, sm_mof_cert smc
                   WHERE sma.MOF_ACCOUNT_ID = smc.MOF_ACCOUNT_ID
                     AND sma.RECORD_STATUS = 1
                     AND smc.RECORD_STATUS = 1
                     AND sma.supplier_id = ? ",
            array($supplierId)
        );
        if (count($results) > 0) {
            return $results;
        }
        return null;
    }

    /**
     * Get Total Personnel Address
     * @param type $personnelId
     * @return List type
     */
    protected function getTotalPersonnelAddress($personnelId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " select count(*) as total_address from SM_PERSONNEL_ADDRESS
                    where
                      record_status = 1
                      and personnel_id =  ? ",
            array($personnelId)
        );
        if (count($results) > 0) {
            return $results[0]->total_address;
        }
        return 0;
    }

    protected function getListTotalAddressService($personnelId)
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT ADDRESS_TYPE,a.address_id ,a.ADDRESS_1 ,a.ADDRESS_2,a.ADDRESS_3,
            a.POSTCODE ,a.COUNTRY_ID ,a.STATE_ID ,a.DIVISION_ID ,a.DISTRICT_ID ,a.CITY_ID ,a.SSM_DATA FROM SM_PERSONNEL_ADDRESS pa, SM_ADDRESS a 
            WHERE pa.ADDRESS_ID  = a.ADDRESS_ID 
            AND pa.PERSONNEL_ID  = ?",
            array($personnelId)
        );
        return $result;
    }

    /**
     * Get list cert for Supplier by ApplID
     * @param type ApplID
     * @return List type
     */
    protected function getSupplierMofVirtCertByApplId($applId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            " SELECT  smc.mof_cert_id, DECODE (smc.is_bumi_cert, 1, 'Bumi Cert', 'Mof Cert')  cert_type, smc.cert_serial_no, smc.eff_date, smc.exp_date,
                         smc.record_status,smc.appl_id
                    FROM sm_mof_account sma, sm_mof_cert smc
                   WHERE sma.MOF_ACCOUNT_ID = smc.MOF_ACCOUNT_ID
                     AND smc.appl_id = ? ",
            array($applId)
        );
        if (count($results) > 0) {
            return $results;
        }
        return null;
    }

    /**
     * Get list of records payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getPaymentSuppliers($supplierID)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select distinct ss.supplier_id,ss.company_name,ss.ep_no,
                    pb.bill_no,pb.bill_type,pb.bill_date,pb.bill_ref_id,pb.payment_due_date,pb.bill_amt,
                    pp.payment_date,pp.payment_id,pp.bill_id,
                    CONCAT(CONCAT((select code_desc from pm_parameter_desc where parameter_id = pp.payment_mode_id and language_code = 'ms'), ' - '), pp.payment_mode_id) as payment_mode,
                    pp.payment_amt,
                    CASE 
                    	WHEN  pp.RECEIPT_FILE_NAME  IS NULL AND pb.bill_type = 'S' THEN 'SM_TAX_INVOICE.jsp'
                    	WHEN  pp.RECEIPT_FILE_NAME  IS NULL AND pb.bill_type IN ('P','R') THEN 'sm_payment_receipt.jsp'
                    	ELSE pp.RECEIPT_FILE_NAME
                    END AS receipt_filename,
                    CONCAT(CONCAT((select status_name from PM_STATUS_DESC where status_id = pp.status_id and language_code = 'ms'), ' - '), pp.status_id) as status,
                    pp.receipt_no , ppo.payment_gateway,
                    pbd.bill_dtl_ref_id as ref_id,
                    (select name||' - '|| identification_no from sm_personnel where personnel_id = pbd.bill_dtl_ref_id) as personnel,
                    spp.pending_process_id,spp.attempt as pending_process_attempt,spp.err_msg as pending_process_err_msg,spp.record_status  as pending_process_status
                  from sm_supplier ss , py_bill pb ,
                    py_payment pp ,
                    py_payment_dtl  ppd ,
                    py_payment_order ppo,
                    PY_BILL_DTL pbd,
                    SM_PENDING_PROCESS spp
                    where
                    ss.supplier_id = pb.ORG_PROFILE_ID
                    and pb.BILL_ID = pp.BILL_ID (+)
                    and pp.PAYMENT_ID = ppd.PAYMENT_ID (+)
                    and ppd.BILL_DTL_ID = pbd.BILL_DTL_ID
                    and pb.BILL_ID = pbd.BILL_ID (+)
                    and pp.payment_id = spp.payment_id (+)
                    AND pp.PAYMENT_ID = ppo.PAYMENT_ID (+)
                    and ss.SUPPLIER_ID = ?
                    and pp.payment_id is not null
                  ORDER BY pp.payment_date desc ",
            array($supplierID)
        );
        return $results;
    }


    /**
     * Get list of personnel status is_softcert 3 and already paid for softcert for prepare data resend request certificate SPKI 
     * @param type $personnelId
     * @return type
     */
    public function getPersonnelInfoDtlSoftcertPaid($personnelId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT
            DISTINCT 
            pu.login_id,ap.appl_id,pu.user_id,
            spp.pending_process_id,sp.personnel_id,ap.appl_no,
            pb.bill_no, pb.bill_type,pp.payment_date,
            pp.payment_amt,pp.receipt_no,pb.bill_type
        FROM
            sm_supplier ss ,
            py_bill pb ,
            sm_appl ap,
            sm_personnel sp,
            pm_user pu,
            py_payment pp ,
            PY_BILL_DTL pbd,
            SM_PENDING_PROCESS spp
        WHERE
            ss.supplier_id = pb.ORG_PROFILE_ID
            AND sp.personnel_id = pbd.bill_dtl_ref_id
            AND pu.user_id = sp.user_id
            AND ss.latest_appl_id = ap.appl_id
            AND pb.BILL_ID = pp.BILL_ID (+)
            AND pb.BILL_ID = pbd.BILL_ID (+)
            AND pp.payment_id = spp.payment_id (+)
            AND sp.personnel_id = ?
            AND pp.payment_id IS NOT NULL 
            AND pb.bill_type = 'S' 
            AND sp.is_softcert = 3
        ORDER BY spp.pending_process_id DESC", array($personnelId));
        return $results;
    }

    public function getPaymentHistoryByPaymentId($paymentId)
    {
        $results = DB::connection('oracle_nextgen_rpt')
            ->table('py_payment as p')
            ->join('py_payment_order as o', 'p.payment_id', '=', 'o.payment_id')
            ->join('py_payment_response as r', 'o.payment_order_id', '=', 'r.payment_order_id')
            ->select('p.PAYMENT_ID', 'p.PAYMENT_DATE', 'p.PAYMENT_AMT', 'p.PAYMENT_MODE_ID', 'o.CHANNEL_NAME', 'r.*')
            ->where('p.payment_id', $paymentId)
            ->get();

        return $results;
    }

    /**
     * Get list of records pending payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getListPendingPaymentSuppliers()
    {

        $dateCreatedFilter = '2019-01-01';
        /*
          $results = DB::connection('oracle_nextgen_rpt')->select(
          "SELECT SS.SUPPLIER_ID,SS.COMPANY_NAME,SS.EP_NO,PP.PAYMENT_ID AS ORDER_ID,PP.PAYMENT_AMT,
          PB.BILL_NO,PB.BILL_TYPE,PB.BILL_DATE,PB.BILL_REF_ID,PB.PAYMENT_DUE_DATE,PP.CREATED_DATE,
          PPO.PAYMENT_GATEWAY,  PPO.PAYMENT_ORDER_ID, PP.RECEIPT_NO
          FROM  SM_SUPPLIER SS, PY_BILL PB , PY_PAYMENT PP  , PY_PAYMENT_ORDER PPO , PY_PAYMENT_RESPONSE PPR
          WHERE SS.SUPPLIER_ID = PB.ORG_PROFILE_ID AND PB.BILL_ID = PP.BILL_ID
          AND PP.PAYMENT_ID = PPO.PAYMENT_ID
          AND PPO.PAYMENT_ORDER_ID = PPR.PAYMENT_ORDER_ID (+)
          AND PPR.PAYMENT_ORDER_ID is null
          ORDER BY PB.BILL_DATE desc");
         */

        //dd($listOrderIdFailed);
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as ss');
        $query->join('PY_BILL as PB', 'SS.SUPPLIER_ID', '=', 'PB.ORG_PROFILE_ID');
        $query->join('PY_PAYMENT as PP', 'PB.BILL_ID', '=', 'PP.BILL_ID');
        $query->join('PY_PAYMENT_ORDER as PPO', 'PP.PAYMENT_ID', '=', 'PPO.PAYMENT_ID');
        $query->leftJoin('PY_PAYMENT_RESPONSE as PPR', 'PPO.PAYMENT_ORDER_ID', '=', 'PPR.PAYMENT_ORDER_ID');
        $query->whereDate('PP.CREATED_DATE', '>', $dateCreatedFilter);
        $query->whereNull('PPR.PAYMENT_ORDER_ID');
        $query->select('SS.SUPPLIER_ID', 'SS.COMPANY_NAME', 'SS.EP_NO', 'PP.PAYMENT_ID AS ORDER_ID', 'PP.PAYMENT_AMT');
        $query->addSelect('PB.BILL_NO', 'PB.BILL_TYPE', 'PB.BILL_DATE', 'PB.BILL_REF_ID', 'PB.PAYMENT_DUE_DATE', 'PP.CREATED_DATE');
        $query->addSelect('PPO.PAYMENT_GATEWAY', 'PPO.PAYMENT_ORDER_ID', 'PP.RECEIPT_NO');
        $query->orderBy('PB.BILL_DATE', 'desc');

        $result = $query->get();

        $data = DB::connection('mysql_ep_support')->table('ep_payment_failed')
            ->whereDate('payment_created', '>', $dateCreatedFilter)
            ->select('order_id')->get();

        $newdata = collect([]);
        foreach ($result as $obj) {
            //$listOrderIdFailed->contains('order_id', $obj->order_id);
            if ($data->contains('order_id', $obj->order_id)) {
            } else {
                $newdata->push($obj);
            }
        }

        //dd(count($newdata));
        return $newdata;
    }

    /**
     * Get list of records pending payment for supplier
     * @param type $supplierID
     * @return type
     */
    public function getPendingPaymentSuppliersByOrderID($orderID)
    {

        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as ss');
        $query->join('PY_BILL as PB', 'SS.SUPPLIER_ID', '=', 'PB.ORG_PROFILE_ID');
        $query->join('PY_PAYMENT as PP', 'PB.BILL_ID', '=', 'PP.BILL_ID');
        $query->join('PY_PAYMENT_ORDER as PPO', 'PP.PAYMENT_ID', '=', 'PPO.PAYMENT_ID');
        $query->leftJoin('PY_PAYMENT_RESPONSE as PPR', 'PPO.PAYMENT_ORDER_ID', '=', 'PPR.PAYMENT_ORDER_ID');
        $query->whereNull('PPR.PAYMENT_ORDER_ID');
        $query->where('PP.PAYMENT_ID', $orderID);
        $query->select('SS.SUPPLIER_ID', 'SS.COMPANY_NAME', 'SS.EP_NO', 'PP.PAYMENT_ID AS ORDER_ID', 'PP.PAYMENT_AMT');
        $query->addSelect('PB.BILL_NO', 'PB.BILL_TYPE', 'PB.BILL_DATE', 'PB.BILL_REF_ID', 'PB.PAYMENT_DUE_DATE', 'PP.CREATED_DATE');
        $query->addSelect('PPO.PAYMENT_GATEWAY', 'PPO.PAYMENT_ORDER_ID', 'PP.RECEIPT_NO');
        $query->orderBy('PB.BILL_DATE', 'desc');

        return $query->first();
    }

    /**
     * Get details response payment (if necessary call this method)
     * @param type $paymentOrderID
     * @return type
     */
    public function getPaymentSuppliersResponse($paymentOrderID)
    {
        return DB::connection('oracle_nextgen_rpt')->table('PY_PAYMENT_RESPONSE')
            ->where('PAYMENT_ORDER_ID', $paymentOrderID)->first();
    }

    /**
     * Get HQ GST info from SM_GST_SUPPLIER
     * @param type $supplierId
     * @return type
     */
    protected function getHqGstInfo($supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_GST_SUPPLIER as GST');
        $query->where('GST.SUPPLIER_ID', $supplierId);
        $query->where('GST.BRANCH_CODE', null);
        $query->where('GST.IS_CURRENT', 1);
        return $query->first();
    }

    /**
     * Get list for supplier branch
     * @param type $applID
     * @return type
     */
    protected function getListSupplierCategoryCode($applID)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER_CATEGORY as ssc');
        $query->leftJoin('PM_CATEGORY_L1 as l1', 'ssc.CATEGORY_L1_ID', '=', 'l1.CATEGORY_L1_ID');
        $query->leftJoin('PM_CATEGORY_L2 as l2', 'ssc.CATEGORY_L2_ID', '=', 'l2.CATEGORY_L2_ID');
        $query->leftJoin('PM_CATEGORY_L3 as l3', 'ssc.CATEGORY_L3_ID', '=', 'l3.CATEGORY_L3_ID');
        $query->where('ssc.APPL_ID', $applID);
        $query->whereIn('ssc.RECORD_STATUS', [1, 8]);
        //$query->whereNotNull('ssc.APPROVED_DATE');
        $query->select('ssc.*');
        $query->addSelect('l1.CATEGORY_L1_CODE', 'l2.CATEGORY_L2_CODE', 'l3.CATEGORY_L3_CODE');
        $query->addSelect(DB::raw(" l1.CATEGORY_L1_CODE||l2.CATEGORY_L2_CODE||l3.CATEGORY_L3_CODE as category_code"));
        return $query->get();
    }

    /**
     * Get list for supplier branch
     * @param type $serviceCode
     * @return list
     */
    protected function getDashboardGfmasByServiceCode($serviceCode)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select a.service_code, a.trans_type, to_char(a.trans_date, 'yyyy-mm-dd') dates, count(a.trans_id) as APIVE_1GFMAS
            from osb_logging a
            where a.service_code like ?
            and a.TRANS_TYPE='Status-BATCH'
            and (a.status_desc = 'Batch File Successfully Transferred' or a.status_desc = 'Batch File Transfer Successfully Initiated')
            and trans_date >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            and trans_date <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            group by a.service_code, a.trans_type, to_char(a.trans_date, 'yyyy-mm-dd')
            order by a.service_code, a.trans_type",
            array($serviceCode)
        );

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz
     * @return list
     */
    protected function getDashboardQuartzFired()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, TRIGGER_STATE,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(PREV_FIRE_TIME / 1000, 'SECOND') AS PREV_FIRED,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(NEXT_FIRE_TIME / 1000, 'SECOND') AS NEXT_FIRED
            FROM QRTZ_TRIGGERS
            WHERE JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA VENDOR JOB(Outbound)'
            ORDER BY JOB_GROUP"
        );

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardQuartzExecution()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, FINISH_EXECUTION_DATE, EXECUTION_DURATION,
            to_char(to_date(ROUND(EXECUTION_DURATION / 1000), 'SSSSS'), 'HH24:MI:SS') AS DURATION
            FROM QRTZ_AUDIT_LOG
            WHERE FINISH_EXECUTION_DATE >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            AND FINISH_EXECUTION_DATE <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            AND JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA VENDOR JOB(Outbound)'
            AND ROWNUM < 2
            ORDER BY FINISH_EXECUTION_DATE DESC"
        );

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardDiInterfaceLog()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM DI_INTERFACE_LOG WHERE PROCESS_ID = 'APIVE' AND IS_CURRENT = 1"
        );

        return $results;
    }

    /**
     * Get list for dashboard EJB OSB
     * @return list
     */
    protected function getDashboardEjbOsb()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select to_char(a.trans_date,'yyyy-mm-dd') trans_date, count(distinct(a.trans_id)) total_invoke,
            count(case when a.status_desc = 'Service Not Found: OSB Service Callout action received SOAP Fault response' and extractvalue(xmltype.createxml(b.payload_body),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%EJBCLIENT000025%' then 1 else null end) no_ejb_receiver,
            count(case when a.status_desc = 'Service Not Found: OSB Service Callout action received SOAP Fault response' and extractvalue(xmltype.createxml(b.payload_body),'//con1:faultstring','xmlns:con1=\"http://www.bea.com/wli/sb/stages/transform/config\"') like '%java.util.concurrent.TimeoutException: No invocation response received%' then 1 else null end) ejb_timeout
            from osb_logging a, osb_logging_dtl b
            where a.trans_id in (
                select distinct(trans_id) from osb_logging
                where service_code in ('EPP-201','EPP-405','EPP-102','EPP-100','EPP-501','EPP-403','EPP-302','EPP-206','EPP-301','EPP-105','EPP-402','EPP-200','PHS-210','EPP-412','EPP-101','EPP-106','EPP-205','EPP-013','EPP-204','EPP-404','SPK-010','EPP-400','EPP-202','EPP-406','EPP-103','EPP-500','EPP-502')
                and trans_date >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS') )
            and a.logging_id = b.logging_id
            and a.trans_date >= to_date(to_char(sysdate-2, 'yyyy-mm-dd') || '00:00:00','YYYY-MM-DD HH24:MI:SS')
            group by to_char(a.trans_date,'yyyy-mm-dd')
            order by to_char(a.trans_date,'yyyy-mm-dd') desc"
        );

        return $results;
    }

    /**
     * Get list for dashboard OSB Retry
     * @return list
     */
    protected function getDashboardOsbRetry()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_NAME, TARGET_SYSTEM, count(SERVICE_NAME) AS counts FROM OSB_RETRY_DTL
            GROUP BY SERVICE_NAME, TARGET_SYSTEM"
        );

        return $results;
    }

    /**
     * Get total list for dashboard OSB Retry
     * @return list
     */
    protected function getTotalDashboardOsbRetry()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SUM(counts) as totalcounts FROM
                (SELECT SERVICE_NAME, TARGET_SYSTEM, count(SERVICE_NAME) AS counts FROM OSB_RETRY_DTL
                GROUP BY SERVICE_NAME, TARGET_SYSTEM) retry"
        );

        return $results;
    }

    /**
     * Get list for dashboard OSB Notification Retry
     * @return list
     */
    protected function getDashboardOsbNotifyRetry()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_NAME, TARGET_SYSTEM, count(SERVICE_NAME) AS counts FROM OSB_NOTIFY_RETRY_DTL
            GROUP BY SERVICE_NAME, TARGET_SYSTEM"
        );

        return $results;
    }

    /**
     * Get list for dashboard OSB OSB Batch Retry
     * @return list
     */
    protected function getDashboardOsbBatchRetry()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SERVICE_CODE, TARGET_SYSTEM, count(SERVICE_CODE) AS counts FROM OSB_BATCH_RETRY_DTL
            GROUP BY SERVICE_CODE, TARGET_SYSTEM"
        );

        return $results;
    }

    protected function getDashboardErrorEPScheduler()
    {
        $result = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT to_char(FINISH_EXECUTION_DATE,'YYYY-MM-DD') AS DATE_FINISH,JOB_NAME,JOB_GROUP, 
            count(*) AS total FROM QRTZ_AUDIT_LOG qal 
            WHERE 
            trunc(FINISH_EXECUTION_DATE) >= trunc(sysdate-3)
            --to_char(FINISH_EXECUTION_DATE,'YYYY') >= '2022'
            AND ERR_MSG IS NOT NULL
            GROUP BY to_char(FINISH_EXECUTION_DATE,'YYYY-MM-DD'),JOB_NAME,JOB_GROUP 
            ORDER BY 1 ASC"
        );

        return $result;
    }

    /**
     * Get total list for dashboard OSB Batch Retry
     * @return list
     */
    protected function getTotalDashboardOsbBatchRetry()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SUM(counts) as totalcounts FROM             
                (SELECT SERVICE_CODE, TARGET_SYSTEM, count(SERVICE_CODE) AS counts FROM OSB_BATCH_RETRY_DTL
                GROUP BY SERVICE_CODE, TARGET_SYSTEM) retry"
        );

        return $results;
    }

    /**
     * Get list for dashboard OSB Notification Retry
     * @return list
     */
    protected function getDashboardBatchFilePending()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select a.process_id as process_id,substr(b.service_name,5) as process_name,a.SERVICE_CODE as service_code,count(*) as total
            from di_interface_log a, OSB_SERVICE b
            where  a.SERVICE_CODE=b.SERVICE_CODE
            and a.process_status = 0 group by a.process_id,b.service_name,a.SERVICE_CODE"
        );
        return $results;
    }

    /**
     * check supplier attendance for QT
     * @return list
     */
    protected function checkQTSupplierAttendance($qtNo)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT e.mof_no, a.QT_NO, a.QT_TITLE, f.*, c.BSV_DATE,
              d.SUPPLIER_ID, d.QT_APPROVAL_REQUEST_ID, d.IS_ATTENDED, d.IS_POST_REGISTERED, d.RECORD_STATUS AS APPROVAL_STATUS
              FROM sc_qt a, sc_qt_bsv b, SC_QT_BSV_DTL c, SC_QT_BSV_REGISTRATION d, sm_mof_account e, SC_QT_BSV_ATTENDANCE f
             WHERE a.qt_id = b.qt_id
               AND b.QT_BSV_ID = c.QT_BSV_ID
               AND c.QT_BSV_DTL_ID = d.QT_BSV_DTL_ID
               AND d.SUPPLIER_ID = e.SUPPLIER_ID
               AND d.QT_BSV_REGISTRATION_ID = f.QT_BSV_REGISTRATION_ID
               AND a.qt_no = ?
               ORDER BY f.is_attended DESC",
            array($qtNo)
        );

        return $results;
    }

    /**
     * check supplier attendance for QT
     * @return list
     */
    protected function getScQt($qtNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->where('QT.QT_NO', $qtNo);
        return $query->first();
    }

    /**
     * check supplier qualify for QT
     * @return list
     */
    protected function checkDisqualifiedStage($qtNo, $supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->leftJoin('SC_QT_SUPPLIER as QTS', 'QT.QT_ID', '=', 'QTS.QT_ID');
        $query->where('QT.QT_NO', $qtNo);
        $query->where('QTS.SUPPLIER_ID', $supplierId);
        $query->addSelect('QTS.QT_ID', 'QTS.INVITATION_TYPE', 'QTS.DISQUALIFIED_STAGE', 'QTS.RECORD_STATUS');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function checkQTApproveRequest($supplierId, $qtNo)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT as QT');
        $query->leftJoin('SC_QT_BSV as QTB', 'QT.QT_ID', '=', 'QTB.QT_ID');
        $query->leftJoin('SC_QT_BSV_DTL as QTBD', 'QTB.QT_BSV_ID', '=', 'QTBD.QT_BSV_ID');
        $query->leftJoin('SC_QT_BSV_REGISTRATION as QTBR', 'QTBD.QT_BSV_DTL_ID', '=', 'QTBR.QT_BSV_DTL_ID');
        $query->where('QT.QT_NO', $qtNo);
        $query->where('QTBR.SUPPLIER_ID', $supplierId);
        $query->addSelect('QTBR.*', 'QTBD.BSV_DATE');
        //        $query->orderBy('QTBR.CHANGED_DATE', 'desc');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function checkQTApproveRequestDetail($qtApprovalReqId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('sc_qt_approval_request as QTAR');
        $query->where('QTAR.qt_approval_request_id', $qtApprovalReqId);
        $query->addSelect('QTAR.RECORD_STATUS as APPROVAL_STATUS', 'QTAR.approval_request_type_id', 'QTAR.approver_action_id');
        return $query->first();
    }

    /**
     * check supplier approve request for QT
     * @return list
     */
    protected function getParameterDesc($paramId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('PM_PARAMETER_DESC as PD');
        $query->where('PD.LANGUAGE_CODE', 'ms');
        $query->where('PD.PARAMETER_ID', $paramId);
        $query->addSelect('PD.CODE_DESC');
        return $query->first();
    }

    /**
     * get supplier detail
     * @return row
     */
    protected function getSupplierDetail($supplierId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as SS');
        $query->where('SS.SUPPLIER_ID', $supplierId);
        return $query->first();
    }

    /**
     * get qt bsv attendance detail
     * @return row
     */
    protected function getQtBsvAttendance($qtBsvAttendanceId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('SC_QT_BSV_ATTENDANCE as BSVA');
        $query->where('BSVA.QT_BSV_ATTENDANCE_ID', $qtBsvAttendanceId);
        return $query->first();
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function selectPreMminf($extCode, $uomCode, $itemName)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select sri.request_item_id, ci.extension_code, sri.item_name, pu.uom_code, sri.changed_date
            from cm_item ci, sc_request_item sri, pm_uom pu
            where ci.item_id = sri.item_id
            and sri.uom_id = pu.uom_id
            and ci.extension_code = ?
            and pu.uom_code = ?
            and sri.item_name = ?",
            array($extCode, $uomCode, $itemName)
        );

        return $results;
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function getMminfReqItemId($docNo, $itemCode)
    {

        $parameters = array();
        $query = "SELECT
                FFR.DOC_NO ,
                FFID.*
                FROM FL_FULFILMENT_ITEM_ADDR FFID ,
                     FL_DELIVERY_ADDRESS FDA ,
                     FL_FULFILMENT_REQUEST FFR
                WHERE FFID.FULFILMENT_ADDR_ID = FDA.DELIVERY_ADDRESS_ID
                AND FDA.FULFILMENT_REQ_ID  = FFR.FULFILMENT_REQ_ID ";
        if ($docNo != null && strlen($docNo) > 0) {
            $query = $query . " AND FFR.DOC_NO = ? ";
            array_push($parameters, $docNo);
        }
        if ($itemCode != null && strlen($itemCode) > 0) {
            $query = $query . " AND FFID.ITEM_CODE = ? ";
            array_push($parameters, $itemCode);
        }
        $results = DB::connection('oracle_nextgen_rpt')->select($query, $parameters);
        return $results;
    }

    /**
     * check item before trigger
     * @return list
     */
    protected function getMminfSearchDetail($reqItemId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT ci.ITEM_NAME,
              ci.EXTENSION_CODE,
              sri.REQUEST_ITEM_ID,
              sri.ITEM_NAME,
              sri.ITEM_DESC,
              sri.CREATED_DATE,
              sri.CHANGED_DATE,
              pu.UOM_CODE
            FROM cm_item ci,
              sc_request_item sri,
              pm_uom pu
            WHERE ci.ITEM_ID        = sri.ITEM_ID
            AND sri.UOM_ID          = pu.UOM_ID
            AND sri.REQUEST_ITEM_ID = ? ",
            array($reqItemId)
        );

        return $results;
    }


    protected function getMminfSearchDetailByListReqItemId($listReqItemId)
    {
        $query = DB::connection('oracle_nextgen_rpt')->table('CM_ITEM CI');
        $query->join('SC_REQUEST_ITEM as SRI', 'CI.ITEM_ID', '=', 'SRI.ITEM_ID');
        $query->join('PM_UOM as PU', 'SRI.UOM_ID', '=', 'PU.UOM_ID');
        $query->whereIn('SRI.REQUEST_ITEM_ID', $listReqItemId);
        $query->select('CI.ITEM_NAME', 'CI.EXTENSION_CODE');
        $query->addSelect('SRI.REQUEST_ITEM_ID', 'SRI.ITEM_NAME as SRI_ITEM_NAME', 'SRI.ITEM_DESC', 'SRI.CREATED_DATE', 'SRI.CHANGED_DATE');
        $query->addSelect('PU.UOM_CODE');
        $query->orderBy('SRI.CHANGED_DATE');
        return $query->get();
    }

    /**
     * Get list for mminf dashboard DI_INTERFACE_LOG
     * @return list
     */
    protected function getDashboardMminfDiInterfaceLog()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT * FROM DI_INTERFACE_LOG WHERE PROCESS_ID = 'MMINF' AND IS_CURRENT = 1"
        );

        return $results;
    }

    /**
     * Get list for mminf dashboard QRTZ_TRIGGERS
     * @return list
     */
    protected function getDashboardMMinfQuartz()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, TRIGGER_STATE,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(PREV_FIRE_TIME / 1000, 'SECOND') AS PREV_FIRED,
            TIMESTAMP '1970-01-01 08:00:00.000' + NUMTODSINTERVAL(NEXT_FIRE_TIME / 1000, 'SECOND') AS NEXT_FIRED
            FROM QRTZ_TRIGGERS
            WHERE JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA MATERIAL INFORMATION JOB'
            ORDER BY JOB_GROUP"
        );

        return $results;
    }

    /**
     * Get list for dashboard 1gfmas-apive quartz execution
     * @return list
     */
    protected function getDashboardMMinfQuartzExecution()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT JOB_GROUP, JOB_NAME, FINISH_EXECUTION_DATE, EXECUTION_DURATION,
            to_char(to_date(ROUND(EXECUTION_DURATION / 1000), 'SSSSS'), 'HH24:MI:SS') AS DURATION
            FROM QRTZ_AUDIT_LOG
            WHERE FINISH_EXECUTION_DATE >= to_date(to_char(sysdate, 'yyyy-mm-dd') || '00:00:00','yyyy-mm-dd hh24:mi:ss')
            AND FINISH_EXECUTION_DATE <= to_date(to_char(sysdate, 'yyyy-mm-dd') || '23:59:59','yyyy-mm-dd hh24:mi:ss')
            AND JOB_GROUP = '1GFMAS INTEGRATION JOB GROUP'
            AND JOB_NAME = 'MASTER DATA MATERIAL INFORMATION JOB'
            AND ROWNUM < 2
            ORDER BY FINISH_EXECUTION_DATE DESC"
        );

        return $results;
    }

    /**
     * check supplier before trigger apive
     * @return list
     */
    protected function getApiveTriggerInfo($ePno)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT SS.SUPPLIER_ID, SS.COMPANY_NAME, SS.EP_NO, SS.CREATED_DATE, SS.CHANGED_DATE, SS.RECORD_STATUS, SS.REG_NO, SMA.MOF_NO
            FROM SM_SUPPLIER SS
            LEFT JOIN SM_MOF_ACCOUNT SMA ON SS.SUPPLIER_ID = SMA.SUPPLIER_ID
            WHERE SS.EP_NO = ? ",
            array($ePno)
        );

        return $results;
    }

    /**
     * list of data in di_mminf based on material_code
     * @return result
     */
    protected function MminfCodeDetails($materialCode)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select * from DI_MMINF
            WHERE RECORD_STATUS = 1 AND MATERIAL_CODE = ? ",
            array($materialCode)
        );
        return $results;
    }

    /**
     * return one object record if exist
     * @return result
     */
    protected function getMminfCodeDetailsByUOM($materialCode, $uom)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM 
                DI_MMINF m 
                WHERE m.RECORD_STATUS = 1 AND m.MATERIAL_CODE = ? 
                AND m.ALT_UOM = ? 
                AND m.CHANGED_DATE  = (SELECT max(changed_date) FROM  DI_MMINF xm WHERE xm.MATERIAL_CODE = m.MATERIAL_CODE AND xm.ALT_UOM = m.ALT_UOM )
                AND rownum < 2",
            array($materialCode, $uom)
        );
        if (count($results) > 0) {
            return $results[0];
        }
        return null;
    }

    protected function getMminfCreatedByToday($defaultToday)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select * from DI_MMINF
            WHERE RECORD_STATUS = 1 AND TO_CHAR(CREATED_DATE,'DD-MM-YY') = ? and rownum <= 300 ",
            array($defaultToday)
        );
        return $results;
    }

    protected function getDetailMminfID($mminfId, $materialCode)
    {

        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select * from DI_MMINF
            WHERE RECORD_STATUS = 1
            AND MMINF_ID = ? AND MATERIAL_CODE = ? ",
            array($mminfId, $materialCode)
        );

        return $results;
    }

    protected function getDashboardSpkiSigningSubmitionQtByDate($date)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT decode(e.cert_issuer,null,'Digicert','T','Trustgate') as softcert_provider, max(c.proposal_submit_date) as latest_date, count(*) as total
                    FROM sc_qt a, sc_qt_supplier b, sc_qt_proposal c, pm_digi_cert e, sm_softcert_request g
                   WHERE a.qt_id = b.qt_id
                     AND b.qt_supplier_id = c.qt_supplier_id
                     and g.user_id =  c.proposal_submit_by
                     and g.softcert_request_id = e.softcert_request_id
                     and b.supplier_id = g.supplier_id
                     and TRUNC (c.proposal_submit_date) = trunc(to_date(?,'YYYY-MM-DD'))
                     and e.record_status = 1
                     AND c.is_submitted = 1
                     and g.record_status = 1
                  group by e.cert_issuer ",
            array($date)
        );

        return $results;
    }

    protected function getDashboardLatestTrustgateSuccessSigning()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select 'Latest TrustGate success signing: ' || to_char(max(pds.created_date), 'dd-Mon-yyyy hh:mi:ss AM')  as latest_signing
                from PM_DIGI_SIGN pds, sm_softcert_request ssr, pm_digi_cert pdc
                where pds.created_by = ssr.user_id
                and ssr.softcert_request_id = pdc.softcert_request_id
                and trunc(pds.changed_date) = trunc(sysdate)
                and pdc.cert_issuer = 'T'
                and ssr.record_status = 1
                order by pds.created_date desc"
        );

        return $results;
    }

    protected function getDashboardLatestDigiCertSuccessSigning()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select 'Latest DigiCert success signing: ' || to_char(max(pds.created_date), 'dd-Mon-yyyy hh:mi:ss AM') as latest_signing
                from PM_DIGI_SIGN pds, sm_softcert_request ssr, pm_digi_cert pdc
                where pds.created_by = ssr.user_id
                and ssr.softcert_request_id = pdc.softcert_request_id
                and trunc(pds.changed_date) = trunc(sysdate)
                and pdc.cert_issuer is null
                and ssr.record_status = 1
                order by pds.created_date desc"
        );

        return $results;
    }

    protected function getLatestGpkiSuccessSigning($userId)
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select upper(to_char(max(pds.created_date), 'dd-Mon-yyyy hh:mi:ss AM')) as latest_signing
                from PM_DIGI_SIGN pds 
                where pds.created_by = ?
                and pds.GPKI_SIGNED_DATA IS NOT NULL",
            array($userId)
        );

        return $results;
    }

    protected function getDashboardTotalCertificationAuthority()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select DECODE(replace(translate(pdc.cert_serial_no ,'1234567890','##########'),'#'),NULL,'DigiCert','TrustGate') as certification_authority, count(*) as total
                from PM_DIGI_SIGN pds, sm_softcert_request ssr, pm_digi_cert pdc
                where pds.created_by = ssr.user_id
                and ssr.softcert_request_id = pdc.softcert_request_id
                and trunc(pds.changed_date) = trunc(sysdate)
                and (pds.original_data like 'SQ%'
                or pds.original_data like 'QT%')
                and ssr.record_status = 1
                group by DECODE(replace(translate(pdc.cert_serial_no ,'1234567890','##########'),'#'),NULL,'DigiCert','TrustGate')"
        );

        return $results;
    }

    protected function getFailedTaskApplNoInitiate()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select appl_id,appl_no, created_date, 'FailedInitiateTask' as type_error from sm_appl where appl_id in(
                select appl_id from sm_appl where created_date >= TO_DATE('2019-01-10','yyyy-mm-dd')
                and   created_date  < sysdate - interval '10' minute
                and status_id = 20100 and is_active_appl = 1 and record_status = 1
                minus
                select doc_id from sm_workflow_status where created_date >= TO_DATE('2019-01-01','yyyy-mm-dd')
                and status_id = 20100)"
        );

        return $results;
    }

    protected function getFailedApplNoTaskProcessingFeeAdv()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "select distinct appl_id,a.appl_no, created_date, 'FailedTaskToPendingApplDeclaration' as type_error   from sm_appl a where a.IS_ACTIVE_APPL=1 and exists(
                select w.doc_id from SM_WORKFLOW_STATUS w where w.IS_CURRENT=1 and w.STATUS_ID=20114 and w.doc_id=a.appl_id and not exists(
                select t.* from PM_TRACKING_DIARY t where t.DOC_ID=w.DOC_ID and t.DOC_TYPE='SR' and (t.STATUS_ID=20101 or t.STATUS_ID=20114)
                )
              )"
        );

        return $results;
    }

    protected function getFailedApplNoTaskToProcessingFee()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "
                SELECT DISTINCT
                    appl_id,
                    a.appl_no,
                    created_date,
                    'FailedTaskToProcessingFee' AS type_error
                  FROM sm_appl a
                  WHERE a.IS_ACTIVE_APPL = 1 AND exists(
                      SELECT w.doc_id
                      FROM SM_WORKFLOW_STATUS w
                      WHERE w.IS_CURRENT = 1 AND w.STATUS_ID = 20101 --Pending Supporting Document Verification
                            AND w.doc_id = a.appl_id AND
                            exists(
                                SELECT t.appl_id
                                FROM SM_APPL t
                                WHERE t.appl_id = w.DOC_ID
                                      AND t.STATUS_ID in ( 20114,20401)  -- 20114 Pending Payment Processing Fee , -- 20401 Pending Payment Response
                            )
                  )
                "
        );

        return $results;
    }

    protected function getFailedApplNoTaskToProcessingFeeOther()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  sa.appl_id,
                                sa.appl_no,
                                sa.created_date,
                                'FailedTaskToProcessingFee' AS type_error
              FROM sm_appl sa, sm_workflow_status sws, pm_tracking_diary ptd
             WHERE sa.appl_id = ptd.doc_id
               AND sa.appl_id = sws.doc_id
               AND sws.is_current = 1
               AND ptd.doc_type = 'SR'
               AND sa.status_id = 20114
               AND sws.status_id = 20100
               AND sa.record_status = 1
               AND sa.is_active_appl = 1
               --AND sa.appl_no = 'KR-01082019-0248'
               AND ptd.tracking_diary_id = (SELECT MAX (tracking_diary_id)
                                              FROM pm_tracking_diary
                                             WHERE doc_id = ptd.doc_id)
               AND sa.appl_no not in ('JN-05092019-0006','KR-08082018-0135')
            "
        );

        return $results;
    }

    protected function getFailedApplNoTaskToPaymentRegistration()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT DISTINCT
                    appl_id,
                    a.appl_no,
                    created_date,
                    'FailedTaskToPaymentRegistration' AS type_error
                  FROM sm_appl a
                  WHERE a.IS_ACTIVE_APPL = 1 AND exists(
                      SELECT w.doc_id
                      FROM SM_WORKFLOW_STATUS w
                      WHERE w.IS_CURRENT = 1 AND w.STATUS_ID = 20199 --Pending Supporting Document Verification
                            AND w.doc_id = a.appl_id AND
                            exists(
                                SELECT t.*
                                FROM PM_TRACKING_DIARY t
                                WHERE t.DOC_ID = w.DOC_ID
                                      AND t.DOC_TYPE = 'SR'
                                      AND t.STATUS_ID = 20401    -- 20401 Pending Payment Response
                                      AND t.actioned_date = (SELECT max(f.actioned_date)
                                                             FROM PM_TRACKING_DIARY f
                                                             WHERE f.DOC_ID = t.DOC_ID AND f.DOC_TYPE = 'SR')
                            )
                  )"
        );

        return $results;
    }

    protected function getFailedApplNoTaskToPaymentRegistrationOther()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT DISTINCT
                    appl_id,
                    a.appl_no,
                    created_date,
                    'FailedTaskToPaymentRegistration' AS type_error
                  FROM sm_appl a
                  WHERE a.IS_ACTIVE_APPL = 1 AND exists(
                      SELECT w.doc_id
                      FROM SM_WORKFLOW_STATUS w
                      WHERE w.IS_CURRENT = 1 AND w.STATUS_ID = 20104  --20104 - Pending Review Application
                            AND w.doc_id = a.appl_id AND
                            exists(
                                SELECT t.*
                                FROM PM_TRACKING_DIARY t
                                WHERE t.DOC_ID = w.DOC_ID
                                      AND t.DOC_TYPE = 'SR'
                                      AND t.STATUS_ID = 20251    -- 20251 Pending Payment Registration Fee
                                      AND t.actioned_date = (SELECT max(f.actioned_date)
                                                             FROM PM_TRACKING_DIARY f
                                                             WHERE f.DOC_ID = t.DOC_ID AND f.DOC_TYPE = 'SR')
                            )
                  )"
        );

        return $results;
    }

    protected function getFailedApplNoTaskToPaymentRegistrationOther2()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT DISTINCT
                                    sa.appl_id,
                                    sa.appl_no,
                                    sa.created_date,
                                    'FailedTaskToPaymentRegistration' AS type_error
                  FROM sm_appl sa, sm_workflow_status sws, pm_tracking_diary ptd
                 WHERE sa.appl_id = ptd.doc_id
                   AND sa.appl_id = sws.doc_id
                   AND sws.is_current = 1
                   AND ptd.doc_type = 'SR'
                   --AND sa.status_id = 20251
                   AND sws.status_id = 20108
                   AND sa.record_status = 1
                   AND sa.is_active_appl = 1
                   AND ptd.tracking_diary_id = (SELECT MAX (tracking_diary_id)
                                                  FROM pm_tracking_diary
                                                 WHERE doc_id = ptd.doc_id and doc_type=ptd.doc_type and status_id = 20251)"
        );

        return $results;
    }

    protected function getStuckPaymentRegistrationIsPaid()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  sa.appl_id,
                                sa.appl_no,
                                sa.created_date,
                                'StuckPaymentRegistrationIsPaid' AS type_error
              FROM sm_appl sa, sm_workflow_status sws, pm_tracking_diary ptd
             WHERE sa.appl_id = ptd.doc_id
               AND sa.appl_id = sws.doc_id
               AND sws.is_current = 1
               AND ptd.doc_type = 'SR'
               AND sa.status_id = 20401
               AND sws.status_id = 20251
               AND sa.record_status = 1
               AND sa.is_active_appl = 1 
               AND ptd.tracking_diary_id = (SELECT MAX (tracking_diary_id)
                                              FROM pm_tracking_diary
                                             WHERE doc_id = ptd.doc_id)
               AND EXISTS (select * from py_payment pp , py_bill pb  where  pp.bill_id = pb.bill_id and pp.receipt_no is not null and pp.status_id = 20405 and pb.bill_no  = sa.appl_no and pb.bill_type = 'R')
               "
        );

        return $results;
    }

    protected function getStuckPendingReviewApplication()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT  sa.appl_id,
                                sa.appl_no,
                                sa.created_date,
                                'FailedTaskToPendingReviewApplication' AS type_error
              FROM sm_appl sa, sm_workflow_status sws, pm_tracking_diary ptd
             WHERE sa.appl_id = ptd.doc_id
               AND sa.appl_id = sws.doc_id
               AND sws.is_current = 1
               AND ptd.doc_type = 'SR'
               AND sa.status_id = 20102   -- Menunggu Pengesahan Permohonan
               AND sws.status_id = 20101  -- Pending Supporting Document Verification
               AND sa.record_status = 1
               AND sa.is_active_appl = 1
               AND ptd.tracking_diary_id = (SELECT MAX (tracking_diary_id)
                                              FROM pm_tracking_diary
                                             WHERE doc_id = ptd.doc_id and doc_type = ptd.doc_type)
               AND ptd.status_id  = 20102"
        );

        return $results;
    }

    protected function getFailedApplNoTaskToSDO()
    {
        $results = DB::connection('oracle_nextgen_rpt')->select(
            "SELECT DISTINCT appl_id, a.appl_no, created_date, 'StuckTaskToSDO' AS type_error
                FROM sm_appl a
                WHERE a.IS_ACTIVE_APPL = 1
                AND a.CREATED_DATE > to_date('2018-12-31','yyyy-mm-dd')
                AND exists(
                     SELECT w.doc_id
                     FROM SM_WORKFLOW_STATUS w
                     WHERE w.IS_CURRENT = 1 AND w.STATUS_ID = 20100 AND w.doc_id = a.appl_id AND exists(
                         SELECT t.*
                         FROM PM_TRACKING_DIARY t
                         WHERE t.DOC_ID = w.DOC_ID AND
                                 t.DOC_TYPE = 'SR' AND
                                 (t.STATUS_ID = 20101)
                     )
                )"
        );

        return $results;
    }

    protected function getSupplierByCompanyName($searchbyname)
    {
        $searchbynameTemp = '%' . strtoupper($searchbyname) . '%';

        /*
          $results = DB::connection('oracle_nextgen_rpt')->select(

          " SELECT supplier_id, company_name , business_type, reg_no, ep_no, latest_appl_id,
          decode ((record_status), '0', '0-INACTIVE', '1', '1-ACTIVE','5', '5', '9', '9-DELETED','7', '7', 'TIADA MAKLUMAT') as record_status,
          created_date, changed_date
          from SM_SUPPLIER
          WHERE UPPER(company_name) like upper(?)
          order by LATEST_APPL_ID desc
          ", array($searchbyname));
         */
        $results = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as S')
            ->leftJoin('SM_APPL as A', 'S.latest_appl_id', '=', 'A.appl_id')
            ->where(DB::raw('UPPER(s.company_name)'), 'like', $searchbynameTemp)
            ->select('S.*')
            ->addSelect('A.supplier_type', 'A.appl_type', 'A.appl_no')
            ->take(50)
            ->get();
        //dump($searchbyname);
        return $results;
    }

    protected function getSupplierByRegistrationNo($regNo)
    {
        $regNoTemp = '%' . strtoupper($regNo) . '%';

        $results = DB::connection('oracle_nextgen_rpt')->table('SM_SUPPLIER as S')
            ->leftJoin('SM_APPL as A', 'S.latest_appl_id', '=', 'A.appl_id')
            ->where(DB::raw('UPPER(S.reg_no)'), 'like', $regNoTemp)
            ->select('S.*')
            ->addSelect('A.supplier_type', 'A.appl_type', 'A.appl_no')
            ->take(50)
            ->get();
        return $results;
    }

    //get supplier info by MOF no
    //protected function getSupplierInfoDEL($mof_no) {
    protected function getSupplierInfoDEL($supp_id)
    {
        $query = "
            SELECT  
            supp.company_name, supp.reg_no, supp.ep_no, supp.supplier_id, 
            mof.mof_no as mofnoo, mof.eff_date, mof.exp_date, mof.reg_status_id, 
            mof.record_status, mof.created_date
            FROM sm_supplier supp,
            sm_mof_account mof
            WHERE supp.supplier_id = mof.supplier_id(+)
            and supp.supplier_id = ?
         ";

        //  SELECT supp.company_name, supp.reg_no, supp.ep_no, mof.supplier_id, mof.mof_no as mofnoo, mof.eff_date, mof.exp_date, mof.reg_status_id, 
        //   mof.record_status, mof.created_date
        //FROM sm_mof_account mof, sm_supplier supp
        //WHERE supp.supplier_id = mof.supplier_id 
        //AND mof.mof_no = ?
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
        //  return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($mof_no));
    }

    //get supplier info by supplier id
    protected function getSupplierInfobySupplierID($Spp_id)
    {
        $query = "
SELECT supp.supplier_id, mof.mof_no as mofnoo, mof.record_status AS mof_record_status,
      DECODE(appl.supplier_type, 'K', appl.supplier_type || '-'  || 'MOF Account - Contractor',
                                 'J', appl.supplier_type || '-'  ||  'MOF Account - Consultant',
                                 'B', appl.supplier_type || '-'  ||  'Basic Account',
                                 'G', appl.supplier_type || '-'  ||  'G2G') Suplier_Type,
        supp.reg_no , supp.Company_Name, supp.BUSINESS_TYPE,supp.RECORD_STATUS
FROM sm_supplier supp, sm_mof_account mof, sm_appl appl
WHERE supp.supplier_id = mof.supplier_id(+) 
AND supp.SUPPLIER_ID = appl.SUPPLIER_ID and supp.LATEST_APPL_ID = appl.APPL_ID
AND supp.supplier_id = ? 
order by supp.company_name, supp.reg_no
            ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($Spp_id));
    }

    //check 001 - ActiveCancelApp
    protected function check001($supp_id)
    {
        $query = "
        SELECT COUNT (appl_id) as resultc
        FROM sm_appl
        WHERE is_active_appl = 1
        AND supplier_id = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 001 - ActiveCancelApp
    protected function check001Z($supp_id)
    {
        $query = "
        SELECT supplier_id, appl_id,appl_no
        FROM sm_appl
        WHERE is_active_appl = 1
        AND supplier_id = ?
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 002 - QT transaction
    protected function check002($supp_id1, $supp_id2)
    {
        $query = "
        SELECT count                                 
           (t2.qt_id)resultc
        FROM sc_qt_supplier t0 LEFT OUTER JOIN sc_qt_proposal t3
            ON (t3.qt_supplier_id = t0.qt_supplier_id)
            , sc_qt t2, sc_workflow_status t1
        WHERE (    (    (    ((t0.supplier_id = ?) AND (t1.is_current = 1))
                  AND (t3.is_submitted = 1)
                 )
             AND (   (   (    (t1.status_id IN (60008))
                          AND EXISTS (
                                 SELECT DISTINCT t4.qt_bsv_registration_id
                                            FROM sc_qt_bsv t7, sc_qt t6, sc_qt_bsv_attendance t5, sc_qt_bsv_registration t4, sc_qt_bsv_dtl t8
                                           WHERE (    
                                                      (    ((t6.qt_id = t2.qt_id) AND (t4.supplier_id = ?))
                                                       AND ((t5.is_pre_registered = 1) OR (t5.is_attended = 1))
                                                      )
                                                  AND ((    
                                                           ((t4.qt_bsv_registration_id = t5.qt_bsv_registration_id)
                                                                 AND (t8.qt_bsv_dtl_id =t4.qt_bsv_dtl_id))
                                                           AND (t7.qt_bsv_id = t8.qt_bsv_id))
                                                       AND (t6.qt_id = t7.qt_id)
                                                      )
                                                 )
                                       )
                         )
                      OR ((t1.status_id IN (60009, 60036))
                          AND (t3.qt_proposal_id IS NOT NULL))
                     )
                  OR (    (t1.status_id IN
                              (60022, 60010, 60023, 60011, 60016, 60012, 60017, 60013))
                      AND (t3.is_submitted = 1)
                     )
                 )
            )
        AND (    (t2.qt_id = t0.qt_id)
             AND ((t1.doc_id = t2.qt_id) AND (t1.doc_type = 'QT'))
            )
       )
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 002 - QT transaction
    protected function check002Z($supp_id1, $supp_id2)
    {
        $query = "
        SELECT t2.qt_id, t2.qt_no, t2.proposal_validity_end_date
        FROM sc_qt_supplier t0 LEFT OUTER JOIN sc_qt_proposal t3
            ON (t3.qt_supplier_id = t0.qt_supplier_id)
            , sc_qt t2, sc_workflow_status t1
        WHERE (    (    (    ((t0.supplier_id = ?) AND (t1.is_current = 1))
                  AND (t3.is_submitted = 1)
                 )
             AND (   (   (    (t1.status_id IN (60008))
                          AND EXISTS (
                                 SELECT DISTINCT t4.qt_bsv_registration_id
                                            FROM sc_qt_bsv t7, sc_qt t6, sc_qt_bsv_attendance t5, sc_qt_bsv_registration t4, sc_qt_bsv_dtl t8
                                           WHERE (    
                                                      (    ((t6.qt_id = t2.qt_id) AND (t4.supplier_id = ?))
                                                       AND ((t5.is_pre_registered = 1) OR (t5.is_attended = 1))
                                                      )
                                                  AND ((    
                                                           ((t4.qt_bsv_registration_id = t5.qt_bsv_registration_id)
                                                                 AND (t8.qt_bsv_dtl_id =t4.qt_bsv_dtl_id))
                                                           AND (t7.qt_bsv_id = t8.qt_bsv_id))
                                                       AND (t6.qt_id = t7.qt_id)
                                                      )
                                                 )
                                       )
                         )
                      OR ((t1.status_id IN (60009, 60036))
                          AND (t3.qt_proposal_id IS NOT NULL))
                     )
                  OR (    (t1.status_id IN
                              (60022, 60010, 60023, 60011, 60016, 60012, 60017, 60013))
                      AND (t3.is_submitted = 1)
                     )
                 )
            )
        AND (    (t2.qt_id = t0.qt_id)
             AND ((t1.doc_id = t2.qt_id) AND (t1.doc_type = 'QT'))
            )
       )
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 003 - CT transactions
    protected function check003($supp_id)
    {
        $query = "
        SELECT count
        (ct.contract_no) as resultc
        FROM ct_contract ct
        WHERE
        ct.supplier_id = ?
        AND ct.record_status = 1
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 003 - CT transactions fix new 20/02/2020
    protected function check003Z($supp_id)
    {
        $query = "
        SELECT   MAX (d.org_name) AS ptj_name, MAX (d.org_code) AS ptj_code,
         ct.contract_no, ct.contract_name, ct.supplier_id,
         (SELECT x.mof_no
            FROM sm_mof_account x
           WHERE x.supplier_id = ct.supplier_id) mof_no, CV.exp_date as contract_expiry_date
    FROM ct_contract ct,
         ct_contract_value CV,
         pm_parameter mtr,
         pm_org_validity d,
         pm_org_profile pr,
         pm_org_validity e,
         pm_org_profile pr2,
         pm_org_validity y,
         pm_org_profile m,
         pm_org_validity w,
         pm_org_profile mi
   WHERE (   CV.contract_ver_id = ct.current_contract_ver_id
          OR CV.contract_ver_id = ct.latest_contract_ver_id
         )
     AND w.org_profile_id = mi.org_profile_id
     AND d.org_profile_id = pr.org_profile_id
     AND e.org_profile_id = pr.parent_org_profile_id
     AND pr.org_type_id = mtr.parameter_id
     AND pr2.org_profile_id = pr.parent_org_profile_id
     AND m.org_profile_id = pr2.parent_org_profile_id
     AND y.org_profile_id = m.org_profile_id
     AND mi.org_profile_id = m.parent_org_profile_id
     AND w.record_status = 1
     AND ct.owner_org_profile_id = d.org_profile_id
     AND ct.supplier_id = ?
     AND ct.record_status = 1
GROUP BY ct.contract_no,
         ct.supplier_id,
         ct.current_contract_ver_id,
         ct.latest_contract_ver_id,
         ct.contract_name,
         ct.supplier_id,
         CV.exp_date
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 004 - FL1.1
    protected function check004_1($supp_id1, $supp_id2)
    {
        $query = "
 SELECT COUNT (a.fulfilment_req_id) as resultc
  FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
       ON b.doc_id = a.fulfilment_req_id
     AND b.doc_type = a.doc_type
     AND b.is_current = 1
 WHERE a.supplier_id = ?
   AND a.doc_type = 'CR'
   AND a.record_status = 1
   AND b.status_id NOT IN
              (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431, 40960)
   AND a.fulfilment_req_id NOT IN (
          SELECT ab.fulfilment_req_id
            FROM fl_fulfilment_request ab LEFT JOIN fl_workflow_status bb
                 ON bb.doc_id = ab.fulfilment_req_id
               AND bb.doc_type = ab.doc_type
               AND bb.is_current = 1
           WHERE ab.supplier_id = ?
             AND ab.doc_type = 'CR'
             AND bb.status_id IN (41530, 41535)
             AND ab.financial_year < 2018)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 004 - FL1.1
    protected function check004_1Z($supp_id1, $supp_id2)
    {
        $query = "
  SELECT a.fulfilment_req_id, a.doc_no, a.ag_office_name
  FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
       ON b.doc_id = a.fulfilment_req_id
     AND b.doc_type = a.doc_type
     AND b.is_current = 1
 WHERE a.supplier_id = ?
   AND a.doc_type = 'CR'
   AND a.record_status = 1
   AND b.status_id NOT IN
              (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431, 40960)
   AND a.fulfilment_req_id NOT IN (
          SELECT ab.fulfilment_req_id
            FROM fl_fulfilment_request ab LEFT JOIN fl_workflow_status bb
                 ON bb.doc_id = ab.fulfilment_req_id
               AND bb.doc_type = ab.doc_type
               AND bb.is_current = 1
           WHERE ab.supplier_id = ?
             AND ab.doc_type = 'CR'
             AND bb.status_id IN (41530, 41535)
             AND ab.financial_year < 2018)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 004 - FL1.2
    protected function check004_2($supp_id1, $supp_id2)
    {
        $query = "
        SELECT count (a.doc_no) as resultc
        FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
        WHERE c.supplier_id = ?
            AND a.doc_type = 'CO'
            AND a.record_status = 1
            AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431, 41940)
            AND a.fulfilment_order_id NOT IN (
                SELECT ab.fulfilment_order_id
                FROM fl_fulfilment_order ab LEFT JOIN fl_workflow_status bb
                    ON bb.doc_id = ab.fulfilment_order_id
                    AND bb.doc_type = ab.doc_type
                    AND bb.is_current = 1
                        LEFT JOIN fl_fulfilment_request cb
                        ON ab.fulfilment_req_id = cb.fulfilment_req_id
                WHERE cb.supplier_id = ?
                    AND ab.doc_type = 'CO'
                    AND ab.record_status = 1
                    AND bb.status_id IN (41530, 40810, 40910)
                    AND cb.financial_year < 2018)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 004 - FL1.2
    protected function check004_2Z($supp_id1, $supp_id2)
    {
        $query = "
        SELECT a.doc_no, a.sap_order_no
        FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
                LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
        WHERE c.supplier_id = ?
            AND a.doc_type = 'CO'
            AND a.record_status = 1
            AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431, 41940)
            AND a.fulfilment_order_id NOT IN (
                SELECT ab.fulfilment_order_id
                FROM fl_fulfilment_order ab LEFT JOIN fl_workflow_status bb
                    ON bb.doc_id = ab.fulfilment_order_id
                    AND bb.doc_type = ab.doc_type
                    AND bb.is_current = 1
                        LEFT JOIN fl_fulfilment_request cb
                        ON ab.fulfilment_req_id = cb.fulfilment_req_id
                WHERE cb.supplier_id = ?
                    AND ab.doc_type = 'CO'
                    AND ab.record_status = 1
                    AND bb.status_id IN (41530, 40810, 40910)
                    AND cb.financial_year < 2018)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id1, $supp_id2));
    }

    //check 005 - FL2.1
    protected function check005_1($supp_id)
    {
        $query = "
        SELECT count (a.doc_no) as resultc
        FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_req_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
        WHERE a.supplier_id = ?
            AND a.doc_type = 'PR'
            AND a.record_status = 1
            AND b.status_id NOT IN (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035, 40460)
            AND a.financial_year IN (SELECT TO_CHAR (SYSDATE, 'yyyy')
            FROM DUAL)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 005 - FL2.1
    protected function check005_1Z($supp_id)
    {
        $query = "
        SELECT a.supplier_id, a.doc_no, a.ag_office_name
        FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_req_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
        WHERE a.supplier_id = ?
            AND a.doc_type = 'PR'
            AND a.record_status = 1
            AND b.status_id NOT IN (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035, 40460)
            AND a.financial_year IN (SELECT TO_CHAR (SYSDATE, 'yyyy')
            FROM DUAL)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 005 - FL2.2
    protected function check005_2($supp_id)
    {
        $query = "
        SELECT count (a.doc_no) as resultc
        FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
            LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = ?
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431, 41430, 41440)
                AND c.financial_year IN (SELECT TO_CHAR (SYSDATE, 'yyyy')
                FROM DUAL)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 005 - FL2.2
    protected function check005_2Z($supp_id)
    {
        $query = "
        SELECT a.doc_no, a.sap_order_no
        FROM fl_fulfilment_order a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
            LEFT JOIN fl_fulfilment_request c
                ON a.fulfilment_req_id = c.fulfilment_req_id
                WHERE c.supplier_id = ?
                AND a.doc_type = 'PO'
                AND a.record_status = 1
                AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431, 41430, 41440)
                AND c.financial_year IN (SELECT TO_CHAR (SYSDATE, 'yyyy')
                FROM DUAL)
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 006 - DP transaction
    protected function check006($supp_id)
    {
        $query = "
        SELECT count (a.quote_no) as resultc
        FROM sc_quote a, sc_quote_supplier b, sc_workflow_status c
        WHERE a.record_status = 1
            AND b.record_status = 1
            AND b.is_submitted = 1
            AND b.supplier_id = ?
            AND c.record_status = 1
            AND c.is_current = 1
            AND c.doc_type = 'SQ'
            AND c.status_id IN (60852, 60856, 60857) -- add 60700 Pending Sibmission --
            AND a.quote_id = b.quote_id
            AND a.quote_id = c.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 006 - DP transaction
    protected function check006Z($supp_id)
    {
        $query = "
        SELECT a.quote_no, b.supplier_id
        FROM sc_quote a, sc_quote_supplier b, sc_workflow_status c
        WHERE a.record_status = 1
            AND b.record_status = 1
            AND b.is_submitted = 1
            AND b.supplier_id = ?
            AND c.record_status = 1
            AND c.is_current = 1
            AND c.doc_type = 'SQ'
            AND c.status_id IN (60852, 60856, 60857) -- add 60700 Pending Sibmission --
            AND a.quote_id = b.quote_id
            AND a.quote_id = c.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 007 - SC(RN) transactions
    protected function check007($supp_id)
    {
        $query = "
        SELECT COUNT (DISTINCT (a.request_note_no)) as resultc
        FROM sc_request_note a,
                sc_request_item b,
                sc_request_supplier_item c,
                sc_workflow_status d
        WHERE d.doc_type = 'RN'
            AND d.is_current = 1
            AND d.status_id IN (60700, 60701, 60702, 60707, 60708)
            AND c.supplier_id = ?
            AND a.request_note_id = b.request_note_id
            AND b.request_item_id = c.request_item_id
            AND a.request_note_id = d.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 007 - SC(RN) transactions
    protected function check007Z($supp_id)
    {
        $query = "
        SELECT DISTINCT (a.request_note_no), a.org_profile_id, a.title
        FROM sc_request_note a,
                sc_request_item b,
                sc_request_supplier_item c,
                sc_workflow_status d
        WHERE d.doc_type = 'RN'
            AND d.is_current = 1
            AND d.status_id IN (60700, 60701, 60702, 60707, 60708)
            AND c.supplier_id = ?
            AND a.request_note_id = b.request_note_id
            AND b.request_item_id = c.request_item_id
            AND a.request_note_id = d.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 008 - PI transactions
    protected function check008($supp_id)
    {
        $query = "
        SELECT COUNT (pi.purchase_inquiry_id) as resultc
        FROM sc_purchase_inquiry pi,
            sc_request_supplier_item rsi,
            sc_workflow_status ws
        WHERE pi.record_status = 1
            AND rsi.record_status = 1
            AND ws.record_status = 1
            AND ws.is_current = 1
            AND ws.doc_type = 'PI'
            AND rsi.supplier_id = ?
            AND ws.status_id IN (60751)
            AND pi.date_to_response >= TO_DATE (SYSDATE)
            AND pi.purchase_inquiry_id = rsi.purchase_inquiry_id
            AND pi.purchase_inquiry_id = ws.doc_id
        ORDER BY pi.purchase_inquiry_id, ws.created_date
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 008 - PI transactions
    protected function check008Z($supp_id)
    {
        $query = "
         SELECT pi.purchase_inquiry_id, pi.purchase_inquiry_no,  pi.title
        FROM sc_purchase_inquiry pi,
            sc_request_supplier_item rsi,
            sc_workflow_status ws
        WHERE pi.record_status = 1
            AND rsi.record_status = 1
            AND ws.record_status = 1
            AND ws.is_current = 1
            AND ws.doc_type = 'PI'
            AND rsi.supplier_id = ?
            AND ws.status_id IN (60751)
            AND pi.date_to_response >= TO_DATE (SYSDATE)
            AND pi.purchase_inquiry_id = rsi.purchase_inquiry_id
            AND pi.purchase_inquiry_id = ws.doc_id
        ORDER BY pi.purchase_inquiry_id, ws.created_date
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 009 - QT(LA) transactions
    protected function check009($supp_id)
    {
        $query = "
        SELECT COUNT (b.loa_no) as resultc
        FROM sc_loi_loa a, sc_loa b, sc_workflow_status c
        WHERE a.record_status = 1
            AND a.supplier_id = ?
            AND c.doc_type = 'LA'
            AND c.status_id IN (62500, 62501, 62502, 62504, 62505, 62508)
            AND b.record_status = 1 
            AND c.is_current = 1
            AND a.loi_loa_id = b.loi_loa_id
            AND b.loa_id = c.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //check 009 - QT(LA) transactions
    protected function check009Z($supp_id)
    {
        $query = "
       SELECT a.supplier_id, a.supplier_name, a.mof_no, b.loa_no
        FROM sc_loi_loa a, sc_loa b, sc_workflow_status c
        WHERE a.record_status = 1
            AND a.supplier_id = ?
            AND c.doc_type = 'LA'
            AND c.status_id IN (62500, 62501, 62502, 62504, 62505, 62508)
            AND b.record_status = 1 
            AND c.is_current = 1
            AND a.loi_loa_id = b.loi_loa_id
            AND b.loa_id = c.doc_id
         ";
        return $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supp_id));
    }

    //appl id history
    protected function getHistoryApplId($suppid)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    select paid_up_capital,a.supplier_id, a.appl_id,a.appl_no, a.appl_type, a.appl_submit_date, a.changed_date , b.status_name, 
                        case when a.supporting_doc_mode = 'H' then 'Offline' when a.supporting_doc_mode = 'S' then 'Online' else '' end as supporting_doc_mode, a.appl_category,
                        case when a.is_resubmit = 1 then 'Yes' else 'No' end as is_resubmit, a.original_appl_id,case when a.is_appl_valid_with_ssm=1 then 'Yes' else 'No' end as is_appl_valid_with_ssm
                        from sm_appl a, pm_status_desc b  , SM_COMPANY_BASIC scb 
                        where a.status_id = b.status_id
                        AND a.APPL_ID = scb.APPL_ID
                        and language_code = 'en'
                        and supplier_id = ? ", array($suppid));
        return $query;
    }

    /**
     * purposely for Customer Servuce User view this result. 
     * maximum 5 records only
     */
    protected function getHistoryApplCsView($supplierId)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("
                    SELECT
                    a.supplier_id,
                    a.appl_id,
                    a.appl_no,
                    a.appl_type,
                    a.appl_submit_date,
                    a.changed_date ,
                    a.status_id,
                    b.status_name,
                CASE
                        WHEN a.supporting_doc_mode = 'H' THEN 'Offline'
                        WHEN a.supporting_doc_mode = 'S' THEN 'Online'
                        ELSE ''
                    END AS supporting_doc_mode,
                    a.appl_category,
                CASE
                        WHEN a.is_resubmit = 1 THEN 'Yes'
                        ELSE 'No'
                    END AS is_resubmit
                FROM
                    sm_appl a,
                    pm_status_desc b
                WHERE
                    a.status_id = b.status_id
                    AND language_code = 'en'
                    AND supplier_id = ?
                    AND a.appl_no IS NOT NULL 
                    AND (b.status_name = 'Registered' OR b.status_name LIKE '%ejec%' ) 
                ORDER BY appl_id desc ", array($supplierId));
        return $query;
    }

    protected function getSupplierIdFromApplId($applId)
    {

        $result = DB::connection('oracle_nextgen_rpt')->select("
                    select a.supplier_id, a.appl_id, sp.ep_no 
                        from sm_appl a, SM_SUPPLIER sp
                        where sp.supplier_id = a.supplier_id
                       and  a.appl_id = ? ", array($applId));
        if (count($result) > 0) {
            return $result[0];
        }
        return null;
    }

    protected function getHistorySupplierDetailFromApplId($suppid, $applid)
    {

        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT sm.supplier_id,sm.company_name, sm.supplier_id,sm.ep_no,sm.reg_no, 
                sm.BUSINESS_TYPE, sm.establish_date, sm.changed_date as changed_date_sm, sm.local_authority_id, 
                case sm.record_status when 1 then '1 - RECORD_STATUS_ACTIVE' end AS record_status_sm,
                mof.mof_no, mof.eff_date, mof.exp_date, 
                case when mof.supplier_type = 'K' then 'Contractor' when mof.supplier_type = 'J' then 'Consultant (Perunding)' when mof.supplier_type = 'B' then 'Basic/Online without for going MOF-Registered' when mof.supplier_type = 'G' then 'G2G Basic (E.g. State Government, Local Council, Federal Statutory Bodies)' when mof.supplier_type = 'P' then 'PTJ Government Seller' else '-' end 
                as supplier_type, 
                case mof.record_status when 1 then '1 - RECORD_STATUS_ACTIVE' end AS record_status_mof,
                sp.appl_type, 
                sp.appl_no,
                smp.appl_id as appl_id,smp.user_id, smp.identification_no, smp.email,smp.personnel_id, smp.name, smp.designation, 
                smp.ep_role, 
                concat(concat(smp.mobile_country , smp.mobile_area), smp.mobile_no) as mobile , 
                smp.identity_resident_status, case smp.is_softcert when 0 then '0 - Softcert is not required' end AS is_softcert,
                case smp.is_equity_owner when 1 then 'Yes' else 'No' end AS is_equity_owner,case is_contract_signer when 1 then 'Yes' else 'No' end AS is_contract_signer,
                case smp.is_bumi when 1 then 'Yes' else 'No' end AS is_bumi,
                case smp.record_status when 1 then '1 - RECORD_STATUS_ACTIVE' end AS record_status_smp, 
                smp.changed_date, smp.rev_no, 
                smp.changed_date as changed_date_smp, 
                case smp.is_contact_person when 1 then 'Yes' else 'No' end AS is_contact_person,
                case smp.is_authorized when 1 then 'Yes' else 'No' end AS is_authorized, 
                case smp.is_mgt when 1 then 'Yes' else 'No' end AS is_mgt, 
                case smp.is_director when 1 then 'Yes' else 'No' end AS is_director, 
                pr.race_name, 
                ph.login_date, pu.login_id, 
                pu.identification_no AS user_identification_no, pu.email AS user_email, pu.user_name, pu.DESIGNATION AS user_designation , 
                concat(concat(pu.mobile_country , pu.mobile_area), pu.mobile_no) as user_mobile , 
                case pu.record_status when 1 then '1 - RECORD_STATUS_ACTIVE' end AS record_status_pu,
                pu.changed_date as changed_date_pu  
            FROM SM_SUPPLIER sm 
            INNER JOIN SM_APPL sp ON sp.supplier_id = sm.supplier_id
            INNER JOIN SM_PERSONNEL smp ON smp.APPL_ID = sp.APPL_ID  
            LEFT JOIN SM_MOF_ACCOUNT mof ON mof.SUPPLIER_ID  = sm.SUPPLIER_ID  AND mof.exp_date IN (SELECT max(exp_date) FROM SM_MOF_ACCOUNT mofx WHERE mofx.supplier_id = mof.SUPPLIER_ID )
            LEFT JOIN pm_race pr ON pr.RACE_ID = smp.RACE_ID 
            LEFT JOIN pm_user pu ON pu.USER_ID = smp.USER_ID 
            LEFT JOIN PM_LOGIN_HISTORY ph ON ph.USER_ID = pu.USER_ID 
            WHERE sm.supplier_id = ? 
            AND sp.appl_id =  ?
            ORDER BY smp.ep_role", array($suppid, $applid));
        return $query;
    }

    public function getSoftcertUserNotSync()
    {

        $p_dt = Carbon::now()->firstOfYear()->format('d/m/Y');

        $q = "SELECT sp.name,sp.identification_no,sp.is_softcert,
                    sp.created_date,sp.changed_date,
                    su.ep_no,su.company_name
                FROM sm_personnel sp,
                     pm_user pu,
                     sm_softcert_request ssr,
                     pm_digi_cert pdc,
                     sm_supplier su
               WHERE sp.user_id = pu.user_id
                 AND pu.user_id = ssr.user_id
                 AND ssr.supplier_id = su.supplier_id
                 AND su.latest_appl_id = sp.appl_id
                 AND ssr.softcert_request_id = pdc.softcert_request_id
                 AND sp.is_softcert = 3
                 AND sp.ep_role IS NOT NULL
                 AND sp.personnel_id NOT IN (10009635)
                 AND TRUNC (pdc.valid_from) >= to_date(?, 'DD/MM/YYYY')
                 AND TRUNC (sp.changed_date) >= to_date(?,'DD/MM/YYYY') ";

        $result = DB::connection('oracle_nextgen_rpt')->select($q, array($p_dt, $p_dt));

        return $result;
    }

    public function getCountSoftcertUserNotSync()
    {
        $list = collect([]);

        $listRenewalNotExpired = $this->getSoftcertRenewalNotExpiredYet();
        $list = $list->merge($listRenewalNotExpired);

        $listOne = $this->getSoftcertUserNotSync();
        $list = $list->merge($listOne);

        $listTwo = $this->getDeletedSoftcertUserNotSync();
        $list = $list->merge($listTwo);

        $listThree = $this->getSoftcertIssuedInvalidToApply();
        $list = $list->merge($listThree);

        // Filter out the specific identification number
        $filteredList = $list->whereNotIn('identification_no', ['610825025877']);

        // Return the count of the filtered list
        return $filteredList->count();
    }

    public function getSoftcertIssuedInvalidToApply()
    {

        $q = "select
                sp.name,sp.identification_no,sp.is_softcert,
                sp.created_date,sp.changed_date,
                su.ep_no,su.company_name
                from
                  SM_SUPPLIER su,
                  PM_USER u,
                  SM_PERSONNEL sp,
                  SM_SOFTCERT_REQUEST sr,
                  PM_DIGI_CERT dc
                WHERE
                   su.supplier_id = sr.SUPPLIER_ID
                AND sr.user_id = sp.user_id
                AND sp.appl_id = su.latest_appl_id
                AND sr.SOFTCERT_REQUEST_ID = dc.SOFTCERT_REQUEST_ID
                AND sr.user_id = u.user_id 
                AND u.record_status NOT IN (0,9) 
                AND sp.is_softcert = 4
                AND sp.record_status = 1 
                AND dc.record_status NOT IN (0)
                AND trunc(dc.valid_to) > trunc(sysdate+60) ";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getDeletedSoftcertUserNotSync()
    {

        $p_dt = Carbon::now()->firstOfYear()->format('d/m/Y');

        $q = "SELECT sp.NAME, sp.identification_no, sp.is_softcert, sp.created_date,
                        sp.changed_date, su.ep_no, su.company_name
                   FROM sm_personnel sp,
                        pm_user pu,
                        sm_softcert_request ssr,
                        pm_digi_cert pdc,
                        sm_supplier su
                  WHERE sp.user_id = pu.user_id
                    AND pu.user_id = ssr.user_id
                    AND ssr.supplier_id = su.supplier_id
                    AND su.latest_appl_id = sp.appl_id
                    AND ssr.softcert_request_id = pdc.softcert_request_id
                    AND ssr.record_status = 9
                    --AND ssr.softcert_provider = 'DG'
                    AND sp.ep_role IS NOT NULL
                    --AND TRUNC (pdc.valid_from) >= TO_DATE ('01/01/2018', 'dd/mm/yyyy')
                    AND TRUNC (pdc.valid_to) >= TRUNC (SYSDATE + 60)
                    AND TRUNC (sp.changed_date) >= ADD_MONTHS (TRUNC (SYSDATE), -36)
                    AND sp.personnel_id NOT IN (0)
                 MINUS
                 SELECT sp.NAME, sp.identification_no, sp.is_softcert, sp.created_date,
                        sp.changed_date, su.ep_no, su.company_name
                   FROM sm_personnel sp,
                        pm_user pu,
                        sm_softcert_request ssr,
                        pm_digi_cert pdc,
                        sm_supplier su
                  WHERE sp.user_id = pu.user_id
                    AND pu.user_id = ssr.user_id
                    AND ssr.supplier_id = su.supplier_id
                    AND su.latest_appl_id = sp.appl_id
                    AND ssr.softcert_request_id = pdc.softcert_request_id
                    AND ssr.record_status = 1
                    AND sp.ep_role IS NOT NULL
                    AND TRUNC (pdc.valid_to) >= TRUNC (SYSDATE + 60)
                    AND TRUNC (sp.changed_date) >= ADD_MONTHS (TRUNC (SYSDATE), -36)";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);
        return $result;
    }

    public function getSoftcertRenewalNotExpiredYet()
    {

        $q = "SELECT
                s.COMPANY_NAME ,
                s.EP_NO ,
                p.name,
                p.identification_no,
                p.ep_role,
                p.is_softcert ,
                p.created_date,
                p.changed_date,
                dc.valid_to
            FROM
                sm_supplier s,
                sm_personnel p ,
                sm_softcert_request sr ,
                pm_digi_cert dc
            WHERE
                s.latest_appl_id = p.appl_id
                AND p.user_id = sr.user_id
                AND s.SUPPLIER_ID = sr.supplier_id
                AND sr.softcert_request_id = dc.SOFTCERT_REQUEST_ID
                AND p.is_softcert = 7
                AND trunc(dc.valid_to) > trunc(sysdate + 30)
                AND sr.record_status = 1 
                 ";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    protected function getIvrMofNo($mofno)
    {
        $query = "SELECT supplier_id, mof_no, record_status
        FROM SM_MOF_ACCOUNT
        WHERE mof_no = ? ";

        return $result = DB::connection('oracle_nextgen_rpt')->select($query, array($mofno));
    }

    protected function getIvrApplNo($suppid, $applNo)
    {
        $query = "SELECT d.language_code, c.status_id, d.status_name, c.appl_no
        FROM SM_APPL c, PM_STATUS_DESC d
        WHERE c.RECORD_STATUS = 1 AND c.supplier_id = ? AND to_char(c.APPL_NO) LIKE ? AND c.STATUS_ID = d.STATUS_ID";

        return $result = DB::connection('oracle_nextgen_rpt')->select($query, array($suppid, $applNo));
    }

    protected function getIvrSoftcertStatus($suppid, $applno)
    {
        $query = "SELECT a.IS_SOFTCERT
        FROM SM_PERSONNEL a, SM_SUPPLIER b, SM_APPL c
        WHERE a.APPL_ID = b.LATEST_APPL_ID AND b.SUPPLIER_ID = ? AND a.IDENTIFICATION_NO LIKE ? AND
                (a.EP_ROLE = 'MOF_SUPPLIER_ADMIN' OR a.EP_ROLE = 'BASIC_SUPPLIER_ADMIN' OR a.EP_ROLE = 'SUPPLIER_TEMP') AND
                b.LATEST_APPL_ID = c.APPL_ID";

        return $result = DB::connection('oracle_nextgen_rpt')->select($query, array($suppid, $applno));
    }

    /**
     * To check this supplier has issue :-
     * This supplier apply RENEW application and APPROVED but MOF still not activated.
     * @param type $docNo
     * @return type
     */
    public function checkSupplierMofExpiredNotActivate($supplierId)
    {
        $query = "
            select s.supplier_id,s.company_name,s.ep_no,
                a.appl_no,a.supplier_type,a.created_date,a.changed_date ,
                  m.mof_no,m.exp_date
                from sm_supplier s , sm_appl a, sm_mof_account m
                where s.latest_appl_id = a.appl_id
                and s.supplier_id = m.supplier_id
                and a.status_id = 20199
                and a.appl_type in ('R','N')
                and s.supplier_id = ?
                -- and to_char(a.changed_date,'YYYY') = to_char(sysdate,'YYYY')
                and a.changed_date in (select max(d.changed_date) from sm_appl d where d.supplier_id = s.supplier_id and d.appl_type in ('R','N') and d.status_id = 20199) 
                and m.record_status = 9
                and not exists (select 1 from sm_mof_account where supplier_id = s.supplier_id and record_status = 1)
                and exists (
                select p.payment_id,p.receipt_no,b.bill_type,b.bill_no from py_payment p, py_bill b
                where p.bill_id = b.bill_id
                and p.receipt_no is not null
                and b.bill_type = 'R' 
                and b.bill_no = a.appl_no 
                and b.org_profile_id = s.supplier_id) 
                ";
        $results = DB::connection('oracle_nextgen_rpt')->select($query, array($supplierId));
        return $results;
    }

    public function getListDataPersonnelIsBumiNull()
    {

        $q = "SELECT personnel_id, appl_id, name, identification_no, is_bumi
                FROM sm_personnel
                            WHERE appl_id IN (
                      SELECT sa.appl_id
                        FROM pm_tracking_diary ptd, sm_workflow_status sws, sm_appl sa
                       WHERE ptd.doc_type = 'SR'
                         AND ptd.doc_id = sws.doc_id
                         AND sws.doc_id = sa.appl_id
                         AND sws.is_current = 1
                         AND sws.doc_type = 'SR'
                         AND sa.record_status = 1
                         --AND sa.status_id = 20101
                         AND sws.status_id = 20105
                         -- AND sa.status_id = 20110
                         -- AND sa.appl_no IN ('KB-09092020-0023','KB-09092020-0073')
                         AND ptd.actioned_date =
                                          (SELECT MAX (actioned_date)
                                             FROM pm_tracking_diary
                                            WHERE doc_type = 'SR' AND doc_no = ptd.doc_no))
               AND is_bumi IS NULL";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountPersonnelIsBumiNull()
    {
        $list = collect([]);
        $list = $list->merge($this->getListDataPersonnelIsBumiNull());

        return $list->count();
    }

    public function getListMofDeletedButActuallyActive()
    {
        $q = "SELECT
                    DISTINCT ss.company_name,ss.ep_no, ss.record_status ,sma.mof_no, 
                    ss.supplier_id,
                    ss.latest_appl_id,
                    ss.changed_date as supplier_changed_date, 
                    sa.changed_date as appl_changed_date, smc.exp_date
                FROM
                    sm_supplier ss,
                    sm_appl sa,
                    sm_mof_account sma,
                    sm_mof_cert smc
                WHERE
                    ss.supplier_id = sma.supplier_id
                    AND ss.latest_appl_id = sa.appl_id
                    AND sa.status_id = '20199'
                    AND sma.mof_account_id = smc.mof_account_id
                    AND smc.record_status = 1
                    AND ss.record_status = 9
                    AND trunc(ss.changed_date) = trunc(sa.changed_date)
                    --AND sma.mof_no = '357-**********'
                    AND sysdate + 365 < smc.exp_date 
                ORDER BY 1 asc";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountMofDeletedButActuallyActive()
    {
        $list = collect([]);
        $list = $list->merge($this->getListMofDeletedButActuallyActive());

        return $list->count();
    }

    public function getListMofActiveButActuallyDeleted()
    {
        $q = "SELECT 
                MAX(plh.login_date) AS login_date,
                pu.login_id,
                sp.ep_role,
                ss.ep_no,
                ss.supplier_id,
                ss.company_name,
                sma.mof_no,
                sa.appl_id,
                sma.exp_date,
                FLOOR(SYSDATE - sma.exp_date) AS days_expired,
                FLOOR(SYSDATE - TRUNC(MAX(plh.login_date))) AS days_not_login
            FROM
                sm_personnel sp
                JOIN pm_user pu ON sp.user_id = pu.user_id
                JOIN pm_login_history plh ON pu.user_id = plh.user_id
                JOIN sm_appl sa ON sp.appl_id = sa.appl_id
                JOIN sm_supplier ss ON sa.appl_id = ss.latest_appl_id
                JOIN sm_mof_account sma ON ss.supplier_id = sma.supplier_id
            WHERE
                sma.exp_date < ADD_MONTHS(SYSDATE, -24)
                AND ss.record_status NOT IN (0, 2, 9)
                AND sp.ep_role IN ('BASIC_SUPPLIER_ADMIN','MOF_SUPPLIER_ADMIN')
            GROUP BY
                pu.login_id,
                sp.ep_role,
                ss.ep_no,
                ss.supplier_id,
                ss.company_name,
                sma.mof_no,
                sa.appl_id,
                sma.exp_date
            HAVING
                MAX(plh.login_date) < ADD_MONTHS(SYSDATE, -12)
            ORDER BY 
                sma.mof_no";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);
        return $result;
    }

    public function getCountMofActiveButActuallyDeleted()
    {
        $list = collect([]);
        $list = $list->merge($this->getListMofActiveButActuallyDeleted());

        return $list->count();
    }

    public function getListDataApplicationWithNoCateogry()
    {

        $q = "SELECT sa.appl_no as appl_no, ptd.tracking_diary_id as tracking_diary_id,
                        ptd.status_id as tracking_status_id,
                        (SELECT status_name
                           FROM pm_status_desc
                          WHERE status_id = ptd.status_id
                            AND language_code = 'ms') as tracking_status,
                        sws.workflow_status_id as workflow_id,
                        sws.status_id as workflow_status_id,
                        (SELECT status_name
                           FROM pm_status_desc
                          WHERE status_id = sws.status_id
                            AND language_code = 'ms') as workflow_status,
                        sa.appl_id as appl_id, sa.status_id as appl_status_id,
                        (SELECT status_name
                           FROM pm_status_desc
                          WHERE status_id = sa.status_id
                                AND language_code = 'ms') as appl_status,
                        sa.record_status, sa.is_active_appl
                FROM pm_tracking_diary ptd, sm_workflow_status sws, sm_appl sa
               WHERE ptd.doc_type = 'SR'
                 AND ptd.doc_id = sws.doc_id
                 AND sws.doc_id = sa.appl_id
                 AND sws.is_current = 1
                 AND sa.is_active_appl = 1
                 AND sws.doc_type = 'SR'
                 --AND sa.record_status = 1
                 AND sws.status_id = 20108
                 AND sa.status_id in (20701, 20702, 20703,20251)
                 --AND sa.appl_no IN ('KR-14122020-0240')
                 AND ptd.actioned_date = (SELECT MAX (actioned_date)
                              FROM pm_tracking_diary
                             WHERE doc_type = 'SR' AND doc_no = ptd.doc_no)";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountApplicationWithNoCategory()
    {
        $list = collect([]);
        $list = $list->merge($this->getListDataApplicationWithNoCateogry());

        return $list->count();
    }

    public function getListAppMissingSV()
    {

        $q = "SELECT 
            sa.appl_id AS appl_id, 
            sa.appl_no AS appl_no, 
            ptd.tracking_diary_id AS tracking_diary_id, 
            ptd.status_id AS tracking_status_id, 
            (
                SELECT status_name FROM pm_status_desc WHERE status_id = ptd.status_id AND language_code = 'ms'
            ) AS tracking_status, 
            sws.workflow_status_id AS workflow_id, 
            sws.status_id AS workflow_status_id, 
            (
                SELECT status_name FROM pm_status_desc WHERE status_id = sws.status_id AND language_code = 'ms'
            ) AS workflow_status, 
            sa.appl_id AS appl_id, 
            sa.status_id AS appl_status_id, 
            (
                SELECT status_name FROM pm_status_desc WHERE status_id = sa.status_id AND language_code = 'ms'
            ) AS appl_status, 
            sa.record_status, 
            sa.is_active_appl 
        FROM 
            pm_tracking_diary ptd, 
            sm_workflow_status sws, 
            sm_appl sa 
        WHERE 
            NOT EXISTS(
                SELECT * FROM pm_tracking_diary ptd1 WHERE sa.appl_id = ptd1.doc_id AND ptd1.doc_type = 'SR' AND ptd1.status_id IN (20103, 20116)
            ) 
            AND EXISTS(
                SELECT * FROM sm_workflow_status sws2 
                WHERE sa.appl_id = sws2.doc_id AND sws2.doc_type = 'SR' AND sws2.status_id IN (20117)
            ) 
            AND ptd.doc_type = 'SR' 
            AND ptd.doc_id = sws.doc_id 
            AND sws.doc_id = sa.appl_id 
            AND sws.is_current = 1 --AND sa.appl_id = 2966091
            --AND sa.is_active_appl = 1
            --AND sws.doc_type = 'SR'
            --AND sa.record_status = 9
            --AND ptd.status_id = 20400
            --AND sws.status_id = 20199
            --AND sa.status_id NOT in (20399, 20189, 20190, 20113, 20199, 20202)
            --AND sa.status_id = 20108
            AND sws.status_id IN (20199) 
            AND substr(sa.appl_no, 8, 5) NOT IN ('2018-', '2019-') 
            AND sa.appl_id IN (
                SELECT sa1.appl_id 
                FROM sm_appl sa1, sm_workflow_status sws1 
                WHERE sa1.appl_id = sws1.doc_id AND sws1.doc_type = 'SR' AND sws1.status_id = 20252 AND sa1.appl_no LIKE 'KB-%'
            ) --AND sa.appl_no IN ('KB-15022021-0016')
            AND ptd.actioned_date = (
                SELECT MAX(actioned_date) FROM pm_tracking_diary WHERE doc_type = 'SR' AND doc_no = ptd.doc_no
            ) 
        ORDER BY 8 DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountAppMissingSV()
    {
        $list = collect([]);
        $list = $list->merge($this->getListAppMissingSV());

        return $list->count();
    }

    //     public function getSupplierDisciplinaryAction()
//     {

    //         $q = "SELECT qp.mof_no as mof_no, qt.qt_no as qt_no, vp.org_code AS ptj_code,
//        vp.org_name AS ptj_name, vj.org_code AS jabatan_Code,
//        vj.org_name AS Jabatan_Name, vm.org_code AS Ministry_Code,
//        vm.org_name AS Ministry_Name,
//        (SELECT pd.code_name
//           FROM pm_parameter_desc pd
//          WHERE qt.procurement_mode_id = pd.parameter_id
//            AND pd.language_code = 'en') AS Procurement,
//        qp.proposal_no as Proposal_No,
//        DECODE (qp.is_submitted, '1', 'yes', 'no') as Submitted,
//        ws.status_id as Status_ID, s.status_name as Status_Name,
//        qt.proposal_validity_end_date as Proposal_Validity_End_Date
//   FROM sc_qt qt,
//        sc_qt_supplier qs,
//        sc_qt_proposal qp,
//        sc_workflow_status ws,
//        pm_status_desc s,
//        pm_org_validity vp,
//        pm_org_profile pp,
//        pm_org_validity vj,
//        pm_org_profile pj,
//        pm_org_validity vpp,
//        pm_org_profile ppp,
//        pm_org_validity vm,
//        pm_org_profile pm
//  WHERE qt.qt_id = qs.qt_id
//    AND qs.qt_supplier_id = qp.qt_supplier_id
//    AND qt.qt_id = ws.doc_id
//    AND ws.doc_type = 'QT'
//    AND qp.mof_no IN
//           ('357-02080518', '357-02185785', '357-02183553', '357-02273318',
//            '357-02048052', '357-02232842', '357-02136426', '357-02257045',
//            '357-00051844', '357-02247307', '357-02237257', '357-02270612',
//            '357-02234296', '357-02155044', '357-02202788', '357-02169796',
//            '357-02265803', '357-02221517', '357-02238427', '357-02259029',
//            '357-02259028', '357-02130814', '357-02232347', '357-02226137',
//            '357-02065765')
//    AND ws.is_current = 1
//    AND ws.status_id NOT IN (60014, 60015, 60045, 60041, 60042)
//    AND ws.status_id = s.status_id
//    AND s.language_code = 'en'
//    AND qt.owner_org_profile_id = vp.org_profile_id
//    AND vp.org_profile_id = pp.org_profile_id
//    AND pp.parent_org_profile_id = vj.org_profile_id
//    AND vj.org_profile_id = pj.org_profile_id
//    AND pj.parent_org_profile_id = vpp.org_profile_id
//    AND vpp.org_profile_id = ppp.org_profile_id
//    AND ppp.parent_org_profile_id = vm.org_profile_id
//    AND vm.org_profile_id = pm.org_profile_id
//    AND vp.record_status = 1
//    AND vj.record_status = 1
//    AND vpp.record_status = 1
//    AND vm.record_status = 1";

    //         $result = DB::connection('oracle_nextgen_rpt')->select($q);

    //         return $result;
//     }

    public function listBumiStatusCancellation()
    {

        $q = "SELECT distinct
	sa.appl_no,
	ss.company_name,
	ss.ep_no,
	sws.created_date
FROM
	sm_appl sa,
	sm_supplier ss,
	sm_workflow_status sws,
	sm_mof_cert smc
WHERE
	sa.supplier_id = ss.supplier_id
	AND sa.appl_id = smc.appl_id
	AND sa.appl_id = sws.doc_id
	AND sws.is_current = 1
	AND sws.status_id IN (20112)
	AND trunc(sws.created_date) > TO_DATE ('01/01/2021', 'dd/mm/yyyy')
	--AND sa.appl_no = 'KB-17012020-0032'
	AND sa.is_bumi = 1
	AND sa.appl_type = 'B'
	--AND smc.record_status = 1
	AND smc.is_bumi_cert = 1
	AND sa.appl_id IN (
	SELECT
		sa.appl_id
	FROM
		sm_appl sa,
		sm_supplier ss,
		sm_workflow_status sws
	WHERE
		sa.supplier_id = ss.supplier_id
		AND sa.appl_id = sws.doc_id
		--AND sws.is_current = 1
		AND sws.status_id IN (20116, 20117)
		--AND trunc(sws.created_date) > TO_DATE ('01/01/2021', 'dd/mm/yyyy')
		--AND sa.appl_no in ('KB-25022021-0039', 'KB-07042020-0018')
		AND sa.appl_type = 'B' )";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);
        return $result;
    }

    public function getCountBumiStatusCancellation()
    {
        $list = collect([]);
        $list = $list->merge($this->listBumiStatusCancellation());

        return $list->count();
    }

    public function getFacilityInfo($applId)
    {

        $query = "SELECT scb.paid_up_capital,
       scb.paid_up_capital_ssm,
          DECODE (fac.facility_type,
                  'H', 'Home Office',
                  'B', 'Business Office',
                  'F', 'Factory/Kilang',
                  'W', 'Workshop/Bengkel',
                  'D', 'Dockyard/Limbungan',
                  'Unknown'
                 )
       || ' : '
       || fac.owner_name
       || ' - Ownership:  '
       || DECODE (fac.ownership_type, 'R', 'Rented', 'O', 'Owned', 'Unknown') as facility_type
  FROM sm_facility fac, sm_company_basic scb
 WHERE fac.appl_id = scb.appl_id
   AND fac.appl_id = $applId
   AND scb.record_status = 1
   AND fac.record_status = 1  
   AND fac.facility_type in ('H','B')";

        $result = DB::connection('oracle_nextgen_rpt')->select($query);

        return $result;
    }

    public function listSiteVisitRollback()
    {
        $query = "SELECT
                    sa.appl_no as appl_no,
                    sa.supplier_id as supplier_id,
                    sa.appl_id as document_id,
                    pu.LOGIN_ID as supplier_list,
                    sst.SV_TASK_ID as svoTaskIdList,
                    'follow tracking' as svoUserList,
                    sst.SV_TASK_ID as svoTaskId,
                    'follow tracking' as svoUserListStr,
                    ss.company_name as companyName,
                    ss.reg_no as businessRegNo,
                    'follow tracking' as poUsers
                FROM
                    sm_sv_task sst,
                    sm_supplier ss,
                    sm_appl sa,
                    sm_workflow_status sws,
                    pm_user pu
                WHERE
                    sst.appl_id = sws.doc_id
                    AND sst.APPL_ID = sa.appl_id
                    AND sa.supplier_id = ss.supplier_id
                    AND sa.created_by = pu.user_id
                    AND sws.is_current = 1
                    AND sws.status_id = 20110
                    AND sst.schedule_date IS NOT NULL";

        $result = DB::connection('oracle_nextgen_rpt')->select($query);

        return $result;
    }

    public function getCountSiteVisitRollback()
    {
        $list = collect([]);
        $list = $list->merge($this->listSiteVisitRollback());

        return $list->count();
    }

    public function listApplicationNotSync()
    {

        $q = "/*
 * SM MONITORING
 * 1. Below script will extract List of Application Status Not Sync based on Workflow & Application table
 * 2. Based on the list, further checking may be done by cross check with BPM Task Status fror each data. 
 *    Additional check in the list; exclude from below script.
 */

SELECT sa.appl_no as appl_no, ptd.tracking_diary_id as tracking_diary_id,
       ptd.status_id as tracking_status_id,
       (SELECT status_name
          FROM pm_status_desc
         WHERE status_id = ptd.status_id
           AND language_code = 'ms') as tracking_status,
       sws.workflow_status_id as workflow_id,
       sws.status_id as workflow_status_id,
       (SELECT status_name
          FROM pm_status_desc
         WHERE status_id = sws.status_id
           AND language_code = 'ms') as workflow_status,
       sa.appl_id as appl_id, sa.status_id as appl_status_id,
       (SELECT status_name
          FROM pm_status_desc
         WHERE status_id = sa.status_id
               AND language_code = 'ms') as appl_status,
       sa.record_status, sa.is_active_appl
  FROM pm_tracking_diary ptd, sm_workflow_status sws, sm_appl sa
 WHERE ptd.doc_type = 'SR'
   AND ptd.doc_id = sws.doc_id
   AND sws.doc_id = sa.appl_id
   AND sws.is_current = 1
   --AND sa.appl_id = 2966091
   --AND sa.is_active_appl = 1
   --AND sws.doc_type = 'SR'
   --AND sa.record_status = 9
   --AND ptd.status_id = 20103
   --AND sws.status_id = 20399
   --AND sa.status_id NOT in (20399, 20189, 20190, 20113, 20199)
   --AND sa.status_id NOT in (20399, 20189, 20190, 20202)
   --AND sws.status_id = 20103
   --AND sa.status_id = 20202
   --AND sws.status_id IN (20108, 20117)
   --AND sa.appl_no IN ('KA-18062021-0016')
   AND sa.IS_ACTIVE_APPL = 1                                          -- ONLY EXTRACT DATA FOR IN-progress appl 
   AND sws.status_id <> sa.status_id                                  -- ONLY EXTRACT id WF & Appl Status NOT sync
                                                                      -- But script will consider below filtering:-
   AND NOT(sws.status_id IN ( 20114, 20251) AND sa.status_id = 20401) -- i. EXCLUDE IF WF = Menunggu Bayaran Pendaftaran AND Appl = Menunggu Status Bayaran
   AND NOT(sws.status_id = 20104 AND sa.status_id = 20102)            -- ii. Exclude Pending Appl Verification (20102 vs 20104) 
   AND sws.status_id NOT IN ( 20252 )                                 -- iii. EXCLUDE WF Status: Pending Softcert Fee due TO this status ONLY existed IN WF
   AND sa.status_id NOT IN (20193)                                    -- iv. EXCLUDE Appl Status: Cancelled Pre-Registration Account due to When Supplier Cancel Pre-Registration, WF still keep the 20199 & 20193 as Is_Current = 1 too
   AND ptd.actioned_date = (SELECT MAX (actioned_date)
                              FROM pm_tracking_diary
                             WHERE doc_type = 'SR' AND doc_no = ptd.doc_no) ORDER BY 2 DESC";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountApplicationNotSync()
    {
        $list = collect([]);
        $list = $list->merge($this->listApplicationNotSync());
        return $list->count();
    }

    public function listPTJValidityRollback()
    {

        $q = "SELECT
    'update pm_org_validity set record_status = 1, exp_date = to_date(''31-DEC-2021'',''dd/mm/yyyy'') where org_validity_id = ' ||
    pov.org_validity_id || ' and record_status = 0;' as tab
FROM
    pm_org_validity pov
WHERE
    pov.org_code IN (
    SELECT
        org_code
    FROM
        pm_org_validity
    WHERE
        eff_date > sysdate
        AND record_status = 1)
    AND pov.exp_date < sysdate
    AND pov.record_status IN (0)
    AND trunc(pov.eff_date) = (
    SELECT
                max(trunc(a.eff_date))
    FROM
                pm_org_validity a
    WHERE
                a.org_code = pov.org_code
        AND a.record_status IN (0))";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);
        return $result;
    }

    public function listPTJValidityRollbackRecordStatus1()
    {

        $q = "select
    'update pm_org_validity set record_status = 0 where org_validity_id = ' ||
    pov1.org_validity_id || ' and record_status = 1;' as tab
FROM
    pm_org_validity pov1
WHERE
    pov1.record_status = 1
    AND pov1.eff_date > sysdate
    AND pov1.org_code IN (
    SELECT
        pov.org_code
    FROM
        pm_org_validity pov
    WHERE
        pov.org_code IN (
        SELECT
            org_code
        FROM
            pm_org_validity
        WHERE
            eff_date > sysdate
            AND record_status = 1)
        AND pov.exp_date < sysdate
        AND pov.record_status IN (0)
            AND trunc(pov.eff_date) = (
            SELECT
                max(trunc(a.eff_date))
            FROM
                pm_org_validity a
            WHERE
                a.org_code = pov.org_code
                AND a.record_status IN (0)))";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);
        return $result;
    }

    protected function pm_user($loginId)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select user_id from pm_user where login_id = ? ", array($loginId));
        return $query;
    }

    protected function getReportDetails($module_code)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select * from pm_report where module_code = ? and record_status = 1", array($module_code));
        return $query;
    }

    protected function getQtID($docno)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select qt_id from sc_qt where qt_no = ?", array($docno));
        return $query;
    }

    //    protected function listDoChecking($docNo) {
    //        $query = DB::connection('oracle_nextgen_rpt')
    //                ->select("select do.delivery_order_no, status_name
    //                                        from fl_delivery_order do, fl_workflow_status statdo, pm_status_desc desdo, fl_fulfilment_order ord
    //                                        where do.fulfilment_req_id = ord.FULFILMENT_REQ_ID
    //                                        and statdo.doc_type in ('DO') 
    //                                        and desdo.status_id = statdo.status_id  
    //                                        and do.delivery_order_id = statdo.doc_id 
    //                                        and statdo.IS_CURRENT = 1 
    //                                        and desdo.LANGUAGE_CODE = 'ms'
    //                                        and doc_no = ? ", array($docNo));
    //        return $query;
    //    }

    protected function getDataModule()
    {
        $query = DB::connection('mysql_ep_support')->select("
         select distinct rpt_module from ep_report_config order by rpt_module
        ", array());
        return $query;
    }

    protected function listModuleDetail($module_code)
    {
        $query = DB::connection('mysql_ep_support')->select("
           select rpt_name from ep_report_config where rpt_module = ?
        ", array($module_code));

        return $query;
    }

    protected function fieldNameList($report_name)
    {
        $query = DB::connection('mysql_ep_support')->select("
           select * from ep_report_config where rpt_name = ?
        ", array($report_name));

        return $query;
    }

    protected function findSuppId($number)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select sm.SUPPLIER_ID from sm_mof_account mof, SM_SUPPLIER sm WHERE sm.SUPPLIER_ID = mof.SUPPLIER_ID AND mof_no = ?", array($number));
        return $query;
    }

    protected function findApplNo($number)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT appl_id FROM sm_appl WHERE appl_no = ?", array($number));
        return $query;
    }

    protected function findQtNo($number)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("select qt_id from sc_qt where qt_no = ?", array($number));
        return $query;
    }

    protected function listLogAction()
    {
        $query = DB::connection('mysql_ep_support')
            ->select("select * from ep_report_log_action where log_type = 'R' ", array());
        return $query;
    }

    public function getListMOFCertExceedThreeYears()
    {

        $q = "SELECT
        DISTINCT  mof.MOF_ACCOUNT_ID, mof.SUPPLIER_ID, supp.EP_NO,appl.appl_id,  mof.mof_no , mof.EFF_DATE, mof.EXP_DATE ,  mof.RECORD_STATUS,  ( (mof.exp_date - mof.eff_date) / 365) total_Validity
   FROM sm_mof_cert cert , sm_appl appl, sm_mof_account mof, sm_supplier supp     
           where 
           EXTRACT(YEAR FROM mof.exp_date) - EXTRACT(YEAR FROM mof.eff_date) > 4
           AND supp.supplier_id = appl.supplier_id
           AND supp.supplier_id = mof.supplier_id
           AND cert.mof_account_id = mof.MOF_ACCOUNT_ID
           AND appl.appl_id = supp.latest_appl_id 
           AND appl.appl_id = cert.appl_id
           -- AND supp.EP_NO = 'eP-1400I08TT'
           AND mof.SUPPLIER_ID = supp.SUPPLIER_ID        
           AND trunc( mof.EXP_DATE)> trunc(sysdate-1)
           Order by mof.EFF_DATE Desc";

        $result = DB::connection('oracle_nextgen_rpt')->select($q);

        return $result;
    }

    public function getCountMOFCertExceedThreeYears()
    {
        $list = collect([]);
        $list = $list->merge($this->getListMOFCertExceedThreeYears());

        return $list->count();
    }

    public function getListSupplierPaymentByRazer()
    {

        $q = "SELECT
        order_id,
        billing_date,
        tran_id,
        channel,
        amount,
        stat_code,
        card_type,
        card_scheme,
        card_bin,
        bin_bank,
        bin_card,
        bin_type,
        bin_level,
        bin_country,
        free_bin_verify,
        free_bin_type
      FROM
        ep_supplier_payment_razer
      WHERE card_bin IS NOT NULL
        AND bin_card IS NOT NULL
        AND card_type <> bin_type
        AND card_type = 'DEBIT'
        
    UNION ALL 

    SELECT
                order_id,
                billing_date,
                tran_id,
                channel,
                amount,
                stat_code,
                card_type,
                card_scheme,
                card_bin,
                bin_bank,
                bin_card,
                bin_type,
                bin_level,
                bin_country,
                free_bin_verify,
                free_bin_type
            FROM
                ep_supplier_payment_razer
            WHERE card_bin IS NOT NULL
                AND bin_card IS NOT NULL
                AND card_type = 'PREPAID'  
                AND DATE(billing_date) >= '2022-12-06'     
        
        ";

        $result = DB::connection('mysql_ep_support')->select($q);

        return $result;
    }

    protected function getListPaymentReceiptEpByDate($date)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT DISTINCT p.PAYMENT_ID ,p.payment_date,p.RECEIPT_NO ,p.CREATED_DATE ,r.TRANSACTION_ID ,r.MOLPAY_TRANSACTION_ID ,
                o.CHANNEL_NAME ,b.BILL_TYPE ,p.PAYMENT_AMT ,
                  p.SERVICE_CHARGE_GST_RATE  , p.CARD_TYPE , r.PAYMENT_STATUS,r.BANK_DATE ,
                  (SELECT code_name FROM PM_PARAMETER_DESC  WHERE parameter_id = p.CARD_TYPE  AND LANGUAGE_code = 'en' ) AS card_type_desc,
                  r.FPX_TRAN_ID 
                  FROM py_payment p ,py_payment_order o, py_payment_response r , PY_BILL b 
                 WHERE 
                 p.PAYMENT_ID  = o.PAYMENT_ID 
                 AND o.payment_order_id = r.PAYMENT_ORDER_ID 
                 AND p.BILL_ID = b.BILL_ID 
                 AND trunc(p.PAYMENT_DATE) = to_date(?,'YYYY-MM-DD')
                 AND p.RECEIPT_NO  IS NOT NULL 
                 AND r.PAYMENT_STATUS = '00'
                 AND r.MOLPAY_TRANSACTION_ID  IS NOT NULL 
                 AND b.BILL_TYPE  IN ('R','P')
                 ORDER BY p.RECEIPT_NO  ", array($date));
        return $query;
    }

    protected function getSupplierRegisterNotExistSAPErrorService()
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT SUPPLIER_TYPE AS supplier_type_code,
                decode(SUPPLIER_TYPE,'K','Contrator','P','PTJ Seller','G','G2G','Basic') AS supplier_type_name,
                count(*) AS total 
                FROM (
                    SELECT s.SUPPLIER_ID, 
                    s.EP_NO,
                    s.COMPANY_NAME,
                    s.RECORD_STATUS,
                    s.REG_NO,
                    s.CHANGED_DATE,
                    a.SUPPLIER_TYPE,
                    a.STATUS_ID,
                    a.APPL_TYPE,
                    v.SAP_VENDOR_CODE 
                    FROM SM_SUPPLIER s,
                    SM_APPL a,
                    SM_SUPPLIER_BANK b,
            		PM_FINANCIAL_ORG o, 
                    SM_SAP_VENDOR_CODE v
                    WHERE s.LATEST_APPL_ID = a.APPL_ID 
                    AND s.RECORD_STATUS = 1
                    AND a.SUPPLIER_TYPE IN ('B','K','P','G')
                    AND a.APPL_ID =  b.APPL_ID
                    AND b.financial_org_id = o.financial_org_id
                    AND s.EP_NO = v.EP_NO (+)
                    AND v.SAP_VENDOR_CODE IS NULL
                    AND s.EP_NO IS NOT NULL 
                    AND o.record_status = 1
                ) tmp 
                GROUP BY SUPPLIER_TYPE ");
        return $query;
    }

    protected function getListSupplierRegisterNotExistSAPErrorService($supplierType)
    {
        $query = DB::connection('oracle_nextgen_rpt')
            ->select("SELECT 
            s.SUPPLIER_ID, 
            s.EP_NO,
            s.COMPANY_NAME,
            s.RECORD_STATUS,
            s.REG_NO,
            s.CHANGED_DATE,
            a.SUPPLIER_TYPE,
            a.STATUS_ID,
            a.APPL_TYPE,
            (SELECT COUNT(*) FROM SM_SUPPLIER s2 , SM_APPL a2
             WHERE s2.LATEST_APPL_ID = a2.APPL_ID AND s2.REG_NO = s.REG_NO 
             AND s2.SUPPLIER_ID != s.SUPPLIER_ID 
             AND s2.REG_NO IS NOT NULL 
             AND a2.SUPPLIER_TYPE  NOT IN ('J') ) as total_same_reg_no
            FROM SM_SUPPLIER s
            JOIN SM_APPL a ON s.LATEST_APPL_ID = a.APPL_ID 
            JOIN SM_SUPPLIER_BANK b ON a.APPL_ID =  b.APPL_ID
            JOIN PM_FINANCIAL_ORG o ON b.financial_org_id = o.financial_org_id
            LEFT JOIN SM_SAP_VENDOR_CODE v ON s.EP_NO = v.EP_NO
            WHERE s.RECORD_STATUS = 1
            AND a.SUPPLIER_TYPE = '$supplierType'
            AND v.SAP_VENDOR_CODE IS NULL
            AND s.EP_NO IS NOT NULL 
            AND o.record_status = 1");
        return $query;
    }


    /**
     * get Single object  SM_DISCIPLINARY_ACTION
     */
    public function getDetailSupplierDisciplinaryAction($supplierId)
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM SM_DISCIPLINARY_ACTION  a
            WHERE a.SUPPLIER_ID = ? 
            AND a.created_date = (SELECT max(created_date) FROM SM_DISCIPLINARY_ACTION x WHERE x.supplier_id = a.supplier_id )
            ", array($supplierId));
        if (count($list) > 0) {
            return $list[0];
        }
        return null;
    }

    public function getAllListSupplierDisciplinaryAction()
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("SELECT DISTINCT tarikh_mula_tt,  tarikh_tamat_tt, hukuman, nama_syarikat, nombor_pendaftaran, nombor_ep, nombor_mof, status_syarikat_terkini, (co_count + po_count + pr_count + cr_count + ct_count) AS total_count
FROM (
    SELECT  tarikh_mula_tt,  tarikh_tamat_tt, hukuman, nama_syarikat, nombor_pendaftaran, nombor_ep, nombor_mof, status_syarikat_terkini, 
        (SELECT COUNT(a.doc_no)
        FROM fl_fulfilment_order a 
        LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
        LEFT JOIN fl_fulfilment_request c
            ON a.fulfilment_req_id = c.fulfilment_req_id
        WHERE c.supplier_id = main_table.SUPPLIER_ID
            AND a.doc_type = 'CO'
            AND a.record_status = 1
            AND b.status_id NOT IN (41535, 41900, 41910, 41920, 41930, 41431, 41940)
            AND a.fulfilment_order_id NOT IN (
                SELECT ab.fulfilment_order_id
                FROM fl_fulfilment_order ab 
                LEFT JOIN fl_workflow_status bb
                    ON bb.doc_id = ab.fulfilment_order_id
                    AND bb.doc_type = ab.doc_type
                    AND bb.is_current = 1
                LEFT JOIN fl_fulfilment_request cb
                    ON ab.fulfilment_req_id = cb.fulfilment_req_id
                WHERE cb.supplier_id = main_table.SUPPLIER_ID
                    AND ab.doc_type = 'CO'
                    AND ab.record_status = 1
                    AND bb.status_id IN (41530, 40810, 40910)
                    AND cb.financial_year < 2018
            )) AS co_count,
        (SELECT COUNT(a.doc_no)
        FROM fl_fulfilment_order a 
        LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_order_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
        LEFT JOIN fl_fulfilment_request c
            ON a.fulfilment_req_id = c.fulfilment_req_id
        WHERE c.supplier_id = main_table.SUPPLIER_ID
            AND a.doc_type = 'PO'
            AND a.record_status = 1
            AND b.status_id NOT IN (41035, 41400, 41410, 41310, 41420, 41430, 41431, 41430, 41440)
            AND c.financial_year = TO_CHAR(SYSDATE, 'yyyy')
        ) AS po_count,
        (SELECT count (a.doc_no)
        FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
            ON b.doc_id = a.fulfilment_req_id
            AND b.doc_type = a.doc_type
            AND b.is_current = 1
        WHERE a.supplier_id = main_table.SUPPLIER_ID
            AND a.doc_type = 'PR'
            AND a.record_status = 1
            AND b.status_id NOT IN (40300, 40310, 40400, 40410, 40430, 40440, 40450, 41431, 41035, 40460)
            AND a.financial_year IN (SELECT TO_CHAR (SYSDATE, 'yyyy')
            FROM DUAL)) AS pr_count ,
            (SELECT COUNT (a.fulfilment_req_id) as resultc
  FROM fl_fulfilment_request a LEFT JOIN fl_workflow_status b
       ON b.doc_id = a.fulfilment_req_id
     AND b.doc_type = a.doc_type
     AND b.is_current = 1
 WHERE a.supplier_id = main_table.SUPPLIER_ID
   AND a.doc_type = 'CR'
   AND a.record_status = 1
   AND b.status_id NOT IN
              (40800, 40810, 40900, 40910, 40911, 40940, 40950, 41431, 40960)
   AND a.fulfilment_req_id NOT IN (
          SELECT ab.fulfilment_req_id
            FROM fl_fulfilment_request ab LEFT JOIN fl_workflow_status bb
                 ON bb.doc_id = ab.fulfilment_req_id
               AND bb.doc_type = ab.doc_type
               AND bb.is_current = 1
           WHERE ab.supplier_id = main_table.SUPPLIER_ID
             AND ab.doc_type = 'CR'
             AND bb.status_id IN (41530, 41535)
             AND ab.financial_year < 2018)) AS cr_count,
             (SELECT count
        (ct.contract_no) as resultc
        FROM ct_contract ct
        WHERE
        ct.supplier_id = main_table.SUPPLIER_ID
        AND ct.record_status = 1) AS ct_count
    FROM DUAL, (SELECT
	DISTINCT ss.SUPPLIER_ID , sda.START_DATE AS tarikh_mula_tt,
	sda.END_DATE AS tarikh_tamat_tt,
	sda.REMARK AS hukuman,
	ss.COMPANY_NAME AS nama_syarikat,
	ss.REG_NO AS nombor_pendaftaran,
	ss.EP_NO AS nombor_ep,
	sda.MOF_NO AS nombor_mof,
	decode(ppd.status_NAME, 'Inactive', 'Suspended', 'Active', 'Active', 'Deleted', 'Deleted') AS status_syarikat_terkini
FROM
	SM_DISCIPLINARY_ACTION sda,
	sm_supplier ss,
	pm_status_desc ppd
WHERE
	sda.SUPPLIER_ID = ss.SUPPLIER_ID
	AND ss.RECORD_STATUS = ppd.status_ID
	AND ppd.LANGUAGE_CODE = 'en'
	AND sda.record_status = 1) main_table
) ORDER BY 2", array());
        return $list;
    }

    public function getListModuleFull()
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("SELECT pp.PARAMETER_CODE, ppd.CODE_NAME 
            FROM PM_PARAMETER pp , PM_PARAMETER_DESC ppd
            WHERE pp.PARAMETER_ID = ppd.PARAMETER_ID 
            AND pp.PARAMETER_TYPE = 'MOD'
            AND ppd.LANGUAGE_CODE = 'en'
            AND pp.PARAMETER_CODE NOT IN 'CD'
            ORDER BY ppd.CODE_NAME asc
            ", array());
        if (count($list) > 0) {
            return $list;
        }
        return null;
    }

    public function getReportName($doc_no)
    {
        $list = DB::connection('oracle_nextgen_rpt')->select("SELECT * FROM PM_REPORT pr WHERE MODULE_CODE = ? AND RECORD_STATUS = 1 ORDER BY REPORT_TITLE asc
            ", array($doc_no));
        if (count($list) > 0) {
            return $list;
        }
        return null;
    }
}
