<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use DateTime;
use DateInterval;
use DatePeriod;

class SmToCancelPersonelActivationUser {

    public static function run($beginDate,$endDate) {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__.' ('.$beginDate.','.$endDate.')');
        $dtStartTime = Carbon::now();
        self::findSmPersonnelInvalidActivation($beginDate,$endDate);

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    public static function findSmPersonnelInvalidActivation($beginDate,$endDate) {
        $begin = new DateTime($beginDate);
        $end = new DateTime($endDate);

        $interval = DateInterval::createFromDateString('1 day');
        $period = new DatePeriod($begin, $interval, $end->add($interval));

        foreach ($period as $dt) {
            $date = $dt->format("Y-m-d");
            MigrateUtils::logDump('Query Date: '.$date.' > getListPersonnelInvalidActivationUser .. ');
            $result = self::getListPersonnelInvalidActivationUser($date);
            $totalRes = count($result);
            MigrateUtils::logDump('Query Date: '.$date.' > Found Total Personnel: '.count($result));
            if ($result) {
                foreach ($result as $key => $row) {
                    MigrateUtils::logDump('  '.($key+1).'/'.$totalRes.' >> Update Record : PersonnelId: '.$row->personnel_id. ', ICNO: '.$row->identification_no);
                    self::updatePersonnelToActiveRecord($row->personnel_id);
                }
            }
        }
    }
    
    /**
     * Update Personnel
     * @param type $personnelId
     */
    protected static function updatePersonnelToActiveRecord($personnelId) {
        if($personnelId && $personnelId > 1){
            DB::connection('oracle_nextgen_fullgrant')->table('sm_personnel')
                    ->where('personnel_id',$personnelId)
                    ->update([
                        'ep_role' => null,
                        'record_status' => 1
                    ]);
        }
    }

    /**
     * To Check Personnel Activation No Valid
     * @return list
     */
    protected static function getListPersonnelInvalidActivationUser($date) {
        $results = DB::connection('oracle_nextgen_rpt')->select(
                "  
                SELECT personnel_id,identification_no,
                  ep_role,email,sp.record_status,sp.changed_date
                  FROM sm_personnel sp, sm_supplier ss
                 WHERE sp.appl_id = ss.latest_appl_id
                   AND ss.record_status in (1,5,7)
                   AND sp.record_status = 8
                   AND sp.ep_role IS NOT NULL 
                   AND TRUNC (sp.changed_date) = to_date(?,'YYYY-MM-DD')

                ",array($date));

        return $results;
    }

}
