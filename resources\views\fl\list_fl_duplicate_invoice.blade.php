@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Duplicate Invoice List<br>
                <small>Duplicate Invoice List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>Duplicate Invoice List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No List Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Duplicate Invoice List</strong>
                        <small></small>
                    </h1>
                </div>
             
                <div class="table-responsive">
                    <table id="fl_duplicate_inv" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Count</th>
                            <th class="text-center">Fulfilment Order Id</th>
                            <th class="text-center">Doc No</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->total }}</td>
                                <td class="text-center">{{ $data->fulfilment_order_id }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->doc_no  }}" >Link</a></td>  
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->\
<script>
    $(function() {
            TablesDatatables.init();
        });
        App.datatables();
    $('#fl_duplicate_inv').dataTable({
            order: [
                [0, "desc"]
            ],
            columnDefs: [],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, 50, -1],
                [10, 20, 30, 50, 'All']
            ]
        });
    
</script>

@endsection



