@extends('layouts.guest-dash')

@section('content')
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i></h1>
        <h1><strong> List of MOF CERT EXCEED 3 YEARS</strong></h1> 
    </div> 

    <div class="table-responsive">  
        <table id="supplier-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">Appl ID</th>
                    <th class="text-center">MOF Account ID</th>
                    <th class="text-center">Supplier ID</th>
                    <th class="text-center">EP No</th>
                    <th class="text-center">Record Status</th>
                    <th class="text-center">Eff Date</th>
                    <th class="text-center">Exp Date</th>
                    <th class="text-center">Total Validity</th>
                </tr>
            </thead>
            <tbody>
                @if(isset($listdata))
                @foreach ($listdata as $user)
                <tr>
                    <td class="text-center">{{ $user->appl_id }}</td>
                    <td class="text-center">{{ $user->mof_account_id }}</td>
                    <td class="text-center"><a class="modal-list-data-action"
                        href="{{url('/find/mofno/')}}/{{$user->mof_no }}" 
                        target='_blank' >{{ $user->supplier_id }}</td>
                    <td class="text-center">{{ $user->ep_no }}</td>
                    <td class="text-center">{{ $user->record_status }}</td>
                    <td class="text-center">{{ $user->eff_date }}</td>
                    <td class="text-center">{{ $user->exp_date }}</td>
                    <td class="text-center">{{ $user->total_validity }}</td>
                </tr>
                @endforeach
                @endif
            </tbody>
        </table>
    </DIV>
</div>
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
            TablesDatatables.init();
        });
        App.datatables();
</script>
@endsection



