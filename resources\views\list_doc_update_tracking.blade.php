@extends('layouts.guest-dash')

@section('header')
<form id="carianforms" action="{{url('/find/updatetracking')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }
    .table tbody > tr > td {
        font-size: 10px;
    }
</style>    
<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="gi gi-search"></i>Carian Tracking Diary<br>
            <small>Masukkan <span class="text-info" >PO/CO No </span> pada carian diatas...</small>
        </h1>
    </div>
</div>


@if($trackingdiary == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">  
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Senarai Dokumen </strong>
                <small>Hasil Carian : {{$carian}}</small>
            </h1>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <p>Status terkini carian diari pengesanan mestilah "menunggu antaramuka dengan 1GFMAS untuk arahan bayaran !"</p>
            </div>
        </div>
    </div>

</div>
</div>

@endif

@if($trackingdiary != null)

<div class="block block-alt-noborder full">
    <div class="block-title">
        </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="block">
                <div class="block-title">
                    <h2><i class="fa fa-building-o"></i> <strong>Update Tracking Diary</strong></h2>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="{{url('/support/report/log/update-tracking-diary')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                           data-title="List Action Patch Data Today ">View Today Action</a>
                    </div>
                </div>
                
                <div id="response" class="table-options clearfix display-none">
                    <div id="response-msg" class="text-center text-light" colspan="6"></div>
                </div>
                <div class="row">
                    <div class="col-lg-6">
                        <div class="block">
                            <div class="block-title">
                                <h2>Tracking Diary</h2>
                            </div>
                            <div class="table-responsive">
                                <table id="tracking-diary-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">ID</th>
                                            <th class="text-center">DOC NO.</th>
                                            <th class="text-center">ACTION DESCRIPTION</th>
                                            <th class="text-center">ACTION DATE</th>
                                            <th class="text-center">STATUS</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        @foreach ($trackingdiary as $tracking)
                                        <tr>
                                            <td class="text-center">{{ $tracking->tracking_diary_id }}</td>
                                            <td class="text-center">{{ $tracking->doc_no }}</td>
                                            <td class="text-left">{{ $tracking->action_desc }}</td>
                                            <td class="text-center">{{ $tracking->actioned_date }}</td>
                                            <td class="text-center">{{ $tracking->status_name }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>

                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="block">
                            <div class="block-title">
                                <h2>Workflow</h2>
                            </div>
                            <table id="tracking-diary-datatable" class="table table-vcenter table-condensed table-bordered">
                                <thead>
                                    <tr>
                                        <th class="text-center">DOC ID</th>
                                        <th class="text-center">DOC TYPE</th>
                                        <th class="text-center">DOC NO.</th>
                                        <th class="text-center">CREATED DATE</th>
                                        <th class="text-center">STATUS</th>
                                        <th class="text-center">IS CURRENT</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($workflow as $flow)
                                    <tr>
                                        <td class="text-center">{{ $flow->doc_id }}</td>
                                        <td class="text-center">{{ $flow->doc_type }}</td>
                                        <td class="text-center">{{ $flow->doc_no }} @if(isset($flow->po_co)) &nbsp;>>&nbsp; {{ $flow->po_co }}@endif</td>
                                        <td class="text-center">{{ $flow->fws_date_created }}</td>
                                        <td class="text-left">{{ $flow->status_id }} - {{ $flow->status_name }}</td>
                                        <td class="text-center">{{ $flow->is_current }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-lg-12">

                        <div class="block">

                            <div class="block-title">
                                <h2>OSB Log</h2>
                            </div>
                            <div class="table-responsive">
                                <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">TRANS ID</th>
                                            <th class="text-center">TRANS TYPE</th>
                                            <th class="text-center">SERVICE CODE</th>
                                            <th class="text-center">TRANS DATE</th>
                                            <th class="text-center">STATUS</th>
                                            <th class="text-center">STATUS CODE</th>
                                            <th class="text-center">STATUS DESC</th>
                                            <th class="text-center">REMARKS 1</th>
                                            <th class="text-center">REMARKS 2</th>
                                            <th class="text-center">REMARKS 3</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($osblog as $osb)
                                        <tr>
                                            <td class="text-center"><a href="{{url('/find/osb/detail/log')}}?cari={{$osb->trans_id }}" target="_blank" >{{$osb->trans_id }}</a></td>
                                            <td class="text-center">{{ $osb->trans_type }}</td>
                                            <td class="text-center">{{$osb->service_code }}</a></td>
                                            <td class="text-center">{{ $osb->trans_date }}</td>
                                            <td class="text-left">{{ App\Services\EPService::$OSB_STATUS[$osb->status] }}</td>
                                            <td class="text-left">{{ $osb->status_code }}</td>
                                            <td class="text-left">{{ $osb->status_desc }}</td>
                                            <td class="text-center">{{ $osb->remarks_1 }}</td>
                                            <td class="text-center">{{ $osb->remarks_2 }}</td>
                                            <td class="text-center">{{ $osb->remarks_3 }}</td>
                                        </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                @if($listdata != null && $flowtrue == 1 && $logtrue == 1 && $trackingtrue != 1) 
                @foreach ($payment as $pymt)
                <center>
                    <div class="btn-group btn-group-xs action-form">
                        <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                           href="#modal_confirm" data-toggle="modal" 
                           data-doctype="{{ $listdata->doctype }}"
                           data-docid="{{ $listdata->docid }}"
                           data-docno="{{ $listdata->docno }}"
                           data-actionby="{{ $listdata->actionby }}"
                           data-statusid="{{ $listdata->statusid }}"
                           data-groupdoctype="{{ $listdata->groupdoctype }}"
                           data-groupdocid="{{ $listdata->groupdocid }}"
                           data-tdiarydatalist1="{{ $listdata->tdiarydatalist1 }}"
                           data-tdiarydatalist2="{{ $listdata->tdiarydatalist2 }}"
                           data-doctypepa="{{ $pymt->doctype }}"
                           data-docidpa="{{ $pymt->docid }}"
                           data-docnopa="{{ $pymt->docno }}"
                           data-statusidpa="{{ $pymt->statusid }}"
                           data-groupdoctypepa="{{ $pymt->groupdoctype }}"
                           data-groupdocidpa="{{ $pymt->groupdocid }}"
                           data-tdiarydatalist1pa="{{ $pymt->tdiarydatalist1 }}"
                           ><i class="hi hi-transfer"></i> Update</a>
                    </div>
                </center>
                @endforeach
                @endif
            </div>
        </div> 
    </div>
</div>

<div id="modal_confirm" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"> Are you sure want to update tracking for this document? </h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <label class="col-md-3 control-label">PO/CO No. </label>
                            <div class="col-md-9">
                                <input type="hidden" id="doctype" value="" />
                                <input type="hidden" id="docid" value="" />
                                <input type="hidden" id="docno" value="" />
                                <input type="hidden" id="actionby" value="" />
                                <input type="hidden" id="statusid" value="" />
                                <input type="hidden" id="groupdoctype" value="" />
                                <input type="hidden" id="groupdocid" value="" />
                                <input type="hidden" id="tdiarydatalist1" value="" />
                                <input type="hidden" id="tdiarydatalist2" value="" />
                                <input type="hidden" id="doctypepa" value="" />
                                <input type="hidden" id="docidpa" value="" />
                                <input type="hidden" id="docnopa" value="" />
                                <input type="hidden" id="statusidpa" value="" />
                                <input type="hidden" id="groupdoctypepa" value="" />
                                <input type="hidden" id="groupdocidpa" value="" />
                                <input type="hidden" id="tdiarydatalist1pa" value="" />
                                <p id="docno_display" class="form-control-static"></p>
                            </div>
                        </div>
                        <div class="form-group form-actions">
                            <form id="form-search-mminf" action="" method="post" class="form-horizontal" onsubmit="return true;">
                                {{ csrf_field() }}
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="submit" id="submit-btn" class="btn btn-sm btn-primary action_confirm"><i class="gi gi-ok_2"></i> Yes</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                </div>
                            </form>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    
@endif
@include('_shared._modalListLogAction')
@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
        ModalListActionLogDatatable.init();
    });</script>
<script>
         //Confirm Trigger Dialogue
        $('div.action-form').on("click", 'a.action_trigger', function () {
        var doctype = $(this).attr('data-doctype');
        var docid = $(this).attr('data-docid');
        var docno = $(this).attr('data-docno');
        var actionby = $(this).attr('data-actionby');
        var statusid = $(this).attr('data-statusid');
        var groupdoctype = $(this).attr('data-groupdoctype');
        var groupdocid = $(this).attr('data-groupdocid');
        var tdiarydatalist1 = $(this).attr('data-tdiarydatalist1');
        var tdiarydatalist2 = $(this).attr('data-tdiarydatalist2');
        var doctypepa = $(this).attr('data-doctypepa');
        var docidpa = $(this).attr('data-docidpa');
        var docnopa = $(this).attr('data-docnopa');
        var statusidpa = $(this).attr('data-statusidpa');
        var groupdoctypepa = $(this).attr('data-groupdoctypepa');
        var groupdocidpa = $(this).attr('data-groupdocidpa');
        var tdiarydatalist1pa = $(this).attr('data-tdiarydatalist1pa');
        console.log("PO/CO No.: " + docno);
        $("#doctype").val(doctype);
        $("#docid").val(docid);
        $("#docno").val(docno);
        $("#actionby").val(actionby);
        $("#statusid").val(statusid);
        $("#groupdoctype").val(groupdoctype);
        $("#groupdocid").val(groupdocid);
        $("#tdiarydatalist1").val(tdiarydatalist1);
        $("#tdiarydatalist2").val(tdiarydatalist2);
        $("#doctypepa").val(doctypepa);
        $("#docidpa").val(docidpa);
        $("#docnopa").val(docnopa);
        $("#statusidpa").val(statusidpa);
        $("#groupdoctypepa").val(groupdoctypepa);
        $("#groupdocidpa").val(groupdocidpa);
        $("#tdiarydatalist1pa").val(tdiarydatalist1pa);
        $("#docno_display").text(docno);
        });
        $('div.form-actions').on("click", 'button.action_confirm', function () {
        var doctype = $("#doctype").val();
        var docid = $("#docid").val();
        var docno = $("#docno").val();
        var actionby = $("#actionby").val();
        var statusid = $("#statusid").val();
        var groupdoctype = $("#groupdoctype").val();
        var groupdocid = $("#groupdocid").val();
        var tdiarydatalist1 = $("#tdiarydatalist1").val();
        var tdiarydatalist2 = $("#tdiarydatalist2").val();
        var doctypepa = $("#doctypepa").val();
        var docidpa = $("#docidpa").val();
        var docnopa = $("#docnopa").val();
        var statusidpa = $("#statusidpa").val();
        var groupdoctypepa = $("#groupdoctypepa").val();
        var groupdocidpa = $("#groupdocidpa").val();
        var tdiarydatalist1pa = $("#tdiarydatalist1pa").val();
        var csrf = $("input[name=_token]").val();
        console.log("PO/CO...: " + docno);

        $('#modal_confirm').modal('hide');
        $('#wait-modal').modal('toggle');

            $.ajax({
                url: "/update/trackingdiary/payment",
                method : "POST",
                dataType : "json",
                data : {"_token":csrf,"doc_type":doctype,"doc_id":docid,"doc_no":docno,"action_by":actionby,"status_id":statusid,"groupdoc_type":groupdoctype,
                    "groupdoc_id":groupdocid,"tdiary_list1":tdiarydatalist1,"tdiary_list2":tdiarydatalist2,"doc_type_pa":doctypepa,"doc_id_pa":docidpa,"doc_no_pa":docnopa,
                "status_id_pa":statusidpa,"groupdoc_type_pa":groupdoctypepa,"groupdoc_id_pa":groupdocidpa,"tdiary_datalist1_pa":tdiarydatalist1pa},
                context: document.body
            }).done(function(resp) {
                if(resp.docno !== ''){
                    console.log('POCompleted : ' + resp.xmlPOCompleted + ' PACompleted : ' + resp.xmlPACompleted );
                    $("#response").show();
                    $("#response").addClass("label-success");
                    $("#response-msg").html("Successfully Updated!");
                    $('#wait-modal').modal('hide');
                    
                } else {
                    $("#response").show();
                    $("#response").addClass("label-danger");
                    $("#response-msg").html("Updated Failed! Please try again.");
                    $('#wait-modal').modal('hide');
                }
            });
        });
</script>
@endsection
