<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use Log;
use App\Model\Notify\NotifyModel;
use App\Services\Traits\FulfilmentService;

class HandleCasesMonitoringBPK extends Command
{
    use FulfilmentService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'monitor-bpk-cases {--test : Run in test mode and send notifications to TEST_PERSONAL}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor SuiteCRM cases related to BPK account with Open_Assigned status and send notification';

    /**
     * The threshold for sending notifications in production mode.
     *
     * @var int
     */
    protected $notificationThreshold = 1;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $isTest = (bool) $this->option('test');
        $mode = $isTest ? 'TEST' : 'PRODUCTION';

        Log::info(self::class . ' starting in ' . $mode . ' mode..', [
            'Date' => Carbon::now()
        ]);

        try {
            // Monitor BPK Open Cases
            $this->monitorBPKOpenCases($isTest);

        } catch (\Exception $exc) {
            Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
        }
    }

    private function getBPKOpenCases()
    {
        return \DB::connection('mysql_crm')
            ->table('tasks')
            ->join('tasks_cstm', 'tasks_cstm.id_c', '=', 'tasks.id')
            ->join('cases', 'cases.id', '=', 'tasks.parent_id')
            ->join('cases_cstm', 'cases_cstm.id_c', '=', 'cases.id')
            ->join('accounts', 'accounts.id', '=', 'cases.account_id')
            ->leftJoin('users', 'users.id', '=', 'tasks.task_pic')
            ->select(
                'tasks_cstm.task_number_c as taskno',
                'cases.case_number',
                'tasks.status as taskStatus',
                'tasks.name as taskname',
                'tasks_cstm.sla_task_flag_c as taskFlag',
                'users.first_name as IndividualAssigned',
                'tasks.date_start as datestart',
                'tasks.date_due as datedue',
                'tasks_cstm.acknowledge_time_c as acknowledgetime',
                'tasks_cstm.sla_start_4hr_c as actualstart',
                'tasks_cstm.sla_stop_4hr_c as actualstop',
                'tasks_cstm.task_duration_c as taskduration',
                'tasks.task_severity as taskSeverity',
                'cases.name as caseName',
                'cases_cstm.sub_category_desc_c as subCategory',
                'cases_cstm.sub_category_2_desc_c as subCategory2',
                'cases.status as caseStatus'
            )
            ->where('accounts.org_gov_code', '=', 'BPK')
            ->where('cases.status', '=', 'Open_Assigned')
            ->whereIn('cases_cstm.sub_category_desc_c', ['Supplier Management', 'Reporting'])
            ->whereIn('tasks.status', ['Acknowledge', 'Pending Acknowledgement'])
            ->where('cases.deleted', '=', 0)
            ->where('tasks.deleted', '=', 0)
            ->orderBy('tasks.date_start', 'desc')
            ->get()
            ->all();
    }

    private function monitorBPKOpenCases($isTest)
    {
        $openCases = $this->getBPKOpenCases();
        $totalCount = count($openCases);
        $current = Carbon::now();

        Log::info(self::class . ' Total count of BPK open tasks: ' . $totalCount);

        if (($isTest || $totalCount >= $this->notificationThreshold)) {
            $receiver = $isTest ? 'TEST_PERSONAL_LUQMAN' : 'NOTIFICATION_SUITECRM_CASES';
            $testPrefix = $isTest ? "**TEST NOTIFICATION**\n\n" : "";

            foreach ($openCases as $case) {
                // Calculate SLA based on task status
                if ($case->taskStatus == 'Pending Acknowledgement') {
                    $datestart = Carbon::parse($case->datestart)->addHour(8)->format("Y-m-d H:i:s");
                    $datedue = Carbon::parse($case->datedue)->addHour(8)->format("Y-m-d H:i:s");
                } else {
                    $datestart = Carbon::parse($case->actualstart)->addHour(8)->format("Y-m-d H:i:s");
                    $datedue = Carbon::parse($case->actualstart)->addDay(5)->addHour(8)->format("Y-m-d H:i:s");
                }

                $dateDiff = $current->diff(new \DateTime($datedue));
                $timeRemaining = $dateDiff->days . ' days ' . $dateDiff->h . ' hours ' . $dateDiff->i . ' minutes ' . $dateDiff->s . ' seconds';

                // Determine if we've exceeded the due date
                $isExceeded = $datedue < Carbon::now();
                $timeLabel = $isExceeded ? "Time Exceed" : "Time Remaining";

                $msg = $testPrefix . "*[ALERT] Cases under BPK to take action.*\n"
                    . "*Case Number :* " . $case->case_number . "\n"
                    . "*Case Subject :* " . $case->caseName . "\n"
                    . "*Sub Category :* " . ($case->subCategory ?? 'N/A') . "\n"
                    . "*Sub Category 2 :* " . ($case->subCategory2 ?? 'N/A') . "\n"
                    . "*Task Status :* " . $case->taskStatus . "\n"
                    . "*Assigned To :* " . $case->taskname . "\n"
                    . "*Sla Due :* " . $datedue . "\n"
                    . "*{$timeLabel} :* " . $timeRemaining;

                $collect = collect(['msg' => $msg]);

                if ($isExceeded) {
                    Log::error(self::class . ' >> ' . $msg);
                }

                $this->saveNotify($receiver, $collect, 'BPK Open Tasks Alert');
            }

            if ($isTest) {
                $this->info('Test notification created and sent to TEST_PERSONAL');
                Log::info(self::class . ' BPK open tasks notification sent to TEST_PERSONAL');
            } else {
                $this->info('Notification created and sent to NOTIFICATION_SUITECRM_CASES');
            }
        } else {
            $this->info('No notifications sent. Task count: ' . $totalCount);
        }
    }

    /**
     * Save notification to the database
     * 
     * @param string $receiver
     * @param \Illuminate\Support\Collection $collect
     * @param string $alertType
     * @return void
     */
    public function saveNotify($receiver, $collect, $alertType)
    {
        $nty = new NotifyModel;
        $nty->message = $collect->get('msg');
        $nty->receiver_group = $receiver;
        $nty->process = 'notify group';
        $nty->status = 0;
        $nty->sent = 0;
        $nty->date_entered = Carbon::now();
        $nty->retry = 0;
        $nty->source_app = 'EPSS';
        $nty->source_class = __CLASS__;
        $nty->source_remark = 'Monitoring ' . strtolower($alertType);
        $nty->save();
    }
}