@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/logtrace-v1.0.css') }}" rel="stylesheet" />
@endsection

@section('content')
    <div class="block log-header">
        <div class="row">
            <div class="col-md-1">
                <div class="log-trace-icon">eP</div>
            </div>
            <div class="col-md-4 log-header-title">
                <span>Log Trace Patch Management<br /></span>
                <small>To Support and Monitor File Stuck and Errors.</small>
            </div>
            <div class="col-md-6 log-header-menu">
                <a href="/home"><i class="fa fa-home"></i></a> |
                <a href="/log/dashboard">Dashboard</a> |
                <a href="/log">User Activity</a> |
                <a href="/log/login-history">Login History</a> |
                @if (Auth::user()->isDevUsersEp()) <span class="active">Patch
                        Management</span> @endif
            </div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="row">
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <div class="row patch-header">
                        <span class="pull-left chart-title"><i class="fa fa-keyboard-o"></i> PyPatch - Tmp Log File to
                            Postgres DB</span>
                        <span class="pull-right" style="color: #fff;"><i class="fa fa-wrench"></i></span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <input type="text" id="patch_dt" style="text-align: center;" class="form-control input-sm"
                                name="patch_dt" value="">
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input id="patch_ts_from" name="patch_ts_from" type="text"
                                            style="text-align: center;" class="form-control input-sm" value="">
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                                <div class="col-md-1" style="color: #fff;">-</div>
                                <div class="col-md-5">
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input id="patch_ts_to" name="patch_ts_to" type="text" style="text-align: center;"
                                            class="form-control input-sm" value="">
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3" style="display: inline-block;">
                            <a class="btn btn-sm" onclick="patch_log_files()"><b>Patch</b></a>
                            <a class="btn btn-sm" onclick="clear_error()"><b>Clear</b></a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <div class="row patch-header">
                        <span class="pull-left chart-title"><i class="fa fa-keyboard-o"></i> GrepPatch - Rsyslog to Tmp Log
                            File</span>
                        <span class="pull-right" style="color: #fff;"><i class="fa fa-wrench"></i></span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row" style="color: #fff;">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-4 control-label">Server Name: </label>
                                <div class="col-md-8">
                                    <select id="server_name" name="server_name" class="form-control"
                                        style="text-align: center;">
                                        <option value="">Please select</option>
                                        <option value="all">All</option>
                                        <option value="prdportal01">Production Portal 01</option>
                                        <option value="prdportal02">Production Portal 02</option>
                                        <option value="prdportal03">Production Portal 03</option>
                                        <option value="prdportal04">Production Portal 04</option>
                                        <option value="prdportal05">Production Portal 05</option>
                                        <option value="prdportal06">Production Portal 06</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-md-4 control-label">Server Node: </label>
                                <div class="col-md-8">
                                    <select id="server_node" name="server_node" class="form-control"
                                        style="text-align: center;">
                                        <option value="">Please select</option>
                                        <option value="node1">Node 01</option>
                                        <option value="node2">Node 02</option>
                                        <option value="node3">Node 03</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="row" style="color: #fff;">
                        <div class="col-md-6">
                            <label class="col-md-4 control-label">Grep Date Ts: </label>
                            <div class="col-md-8">
                                <input type="text" id="grep_dt" style="text-align: center;" class="form-control input-sm"
                                    name="grep_dt" value="">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="row">
                                <div class="col-md-5">
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input id="grep_ts_from" name="grep_ts_from" type="text" style="text-align: center;"
                                            class="form-control input-sm" value="">
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                                <div class="col-md-1" style="color: #fff;">-</div>
                                <div class="col-md-5">
                                    <div class="input-group bootstrap-timepicker timepicker">
                                        <input id="grep_ts_to" name="grep_ts_to" type="text" style="text-align: center;"
                                            class="form-control input-sm" value="">
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <br />
                    <div class="row" style="padding-right: 30px; color:#fff;">
                        <span class="pull-left"><span id="grep_msg"></span></span>
                        <span class="pull-right"><a class="btn btn-sm" onclick="grep_log()"><b>Grep</b></a></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <span class="pull-left chart-title"><i class="fa fa-keyboard-o"></i> Log Trace Stuck
                        <span class="badge" style="background: #33ff33; font-weight: bold;" id="tot_log_fail"></span></span>
                    <span class="pull-right"><a href="javascript:void(0)" onclick='get_tmp_log_fail_to_proc()'
                            class="btn btn-sm" data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                class="fa fa-refresh"></i></a></span>
                </div>
                <div class="card-body">
                    <table id="file_list" class="table table-condensed table-bordered"
                        style="height: 150px; overflow-y: scroll;">
                    </table>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card" id="chart-box-table">
                <div class="card-header">
                    <span class="pull-left chart-title"><i class="fa fa-keyboard-o"></i> Log Trace Error</span>
                    <span class="pull-right">
                        <a href="javascript:void(0)" onclick='get_tmp_log_error_msg()'
                            class="btn btn-sm" data-toggle="tooltip" title="" data-original-title="Refresh"><i
                                class="fa fa-refresh"></i></a>
                        <a href="javascript:void(0)" onclick="clear_error()"
                            class="btn btn-sm" data-toggle="tooltip" title="" data-original-title="Clear Log"><i
                                class="fa fa-trash-o"></i></a></span>
                </div>
                <br />
                <div class="card-body">
                    <table id="file_errors" class="table table-condensed table-bordered"
                        style="height: 150px; overflow-y: scroll;">
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!};

        App.datatables();

        function clear_error() {
            $('#file_errors').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();

            $.ajax({
                url: APP_URL + '/log/clear',
                type: "GET",
                success: function(data) {
                    get_tmp_log_error_msg()
                },
                error: function(error) {
                    $('#file_errors').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function grep_log() {
            $('#grep_msg').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();

            let name = $('#server_name').val();
            let node = $('#server_node').val();
            let grep_dt = $('#grep_dt').val();
            let grep_ts_fr = $('#grep_ts_from').val();
            let grep_ts_to = $('#grep_ts_to').val();

            if (patch_dt === '') {
                alert('Oops! Please Specify Both Grep Date and Time.');
                return;
            }

            if (grep_ts_fr === '') {
                grep_ts_fr = "00:00";
            }
            grep_ts_fr = grep_ts_fr.length < 5 ? "0" + grep_ts_fr : grep_ts_fr;

            if (grep_ts_to === '') {
                grep_ts_to = "00:00";
            }
            grep_ts_to = grep_ts_to.length < 5 ? "0" + grep_ts_to : grep_ts_to;

            $.ajax({
                url: APP_URL + '/log/patch/grep',
                type: "GET",
                data: {
                    name,
                    node,
                    grep_dt,
                    grep_ts_fr,
                    grep_ts_to
                },
                success: function(data) {
                    $('#grep_msg').hide().html(
                            "<div class='text-center' style='padding: 20px;'><i class='fa fa-check-circle fa-4x'></i></div>"
                        )
                        .fadeIn();
                },
                error: function(error) {
                    $('#grep_msg').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function patch_log_files() {
            $('#file_list').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            let patch_dt = $('#patch_dt').val();
            let patch_ts_fr = $('#patch_ts_from').val();
            let patch_ts_to = $('#patch_ts_to').val();

            if (patch_dt === '') {
                alert('Oops! Please Specify Both Patch Date and Time.');
                return;
            }

            if (patch_ts_fr === '') {
                patch_ts_fr = "00:00";
            }
            patch_ts_fr = patch_ts_fr.length < 5 ? "0" + patch_ts_fr : patch_ts_fr;

            if (patch_ts_to === '') {
                patch_ts_to = "00:00";
            }
            patch_ts_to = patch_ts_to.length < 5 ? "0" + patch_ts_to : patch_ts_to;

            $.ajax({
                url: APP_URL + '/log/patch/exec',
                type: "GET",
                data: {
                    patch_dt,
                    patch_ts_fr,
                    patch_ts_to
                },
                success: function(data) {
                    get_tmp_log_error_msg()
                    get_tmp_log_fail_to_proc()
                },
                error: function(error) {
                    $('#file_list').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function get_tmp_log_error_msg() {
            $('#file_errors').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $.ajax({
                url: APP_URL + '/log/patch/error',
                type: "GET",
                success: function(data) {
                    var errTbl = `<tbody>`;
                    if (data.length > 0) {
                        data.forEach(function(err) {
                            errTbl += `<tr><td>${err}</td></tr>`;
                        });
                    } else {
                        errTbl +=
                            '<tr><td style="text-align:center; font-weight:bold;">No Error Found.</td></tr>'
                    }
                    errTbl += `</tbody>`;
                    $('#file_errors').hide().html(errTbl).fadeIn();
                },
                error: function(error) {
                    $('#file_errors').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div>`
                    ).fadeIn();
                }
            });
        }

        function get_tmp_log_fail_to_proc() {
            $('#file_list').hide().html(
                    "<div class='text-center' style='padding: 20px;'><i class='fa fa-spinner fa-4x fa-spin'></i></div>")
                .fadeIn();
            $.ajax({
                url: APP_URL + '/log/patch/fail',
                type: "GET",
                success: function(data) {
                    var fileTbl = `<tbody>`;
                    if (data.length > 0) {
                        data.forEach(function(f) {
                            fileTbl += `<tr><td>${f}</td></tr>`;
                        });
                    } else {
                        fileTbl +=
                            '<tr><td style="text-align:center;font-weight:bold;">No Stuck Found.</td></tr>'
                    }

                    fileTbl += `</tbody>`;
                    $('#file_list').hide().html(fileTbl).fadeIn();
                    document.getElementById('tot_log_fail').textContent = `Total: ${data.length}`;
                },
                error: function(error) {
                    document.getElementById('tot_log_fail').textContent = `Total: 0`;
                    $('#file_list').hide().html(
                        `<div class="text-center"><strong>${error.status}-${error.statusText}</strong></div`
                    ).fadeIn();
                }
            });
        }

        function formatDate(date) {
            var d = new Date(date),
                month = '' + (d.getMonth() + 1),
                day = '' + d.getDate(),
                year = d.getFullYear();

            if (month.length < 2)
                month = '0' + month;
            if (day.length < 2)
                day = '0' + day;

            return year + '' + month + '' + day;
        }

        $(document).ready(function() {
            $('#page-container').removeAttr('class');

            $("#patch_dt").datepicker({
                format: 'yyyymmdd'
            });

            $("#patch_dt").val(formatDate(new Date()));

            $('#patch_ts_from').timepicker({
                showSeconds: false,
                showMeridian: false,
            });

            $('#patch_ts_to').timepicker({
                showSeconds: false,
                showMeridian: false,
            });

            $("#grep_dt").datepicker({
                format: 'yyyymmdd'
            });

            $("#grep_dt").val(formatDate(new Date()));

            $('#grep_ts_from').timepicker({
                showSeconds: false,
                showMeridian: false,
            });

            $('#grep_ts_to').timepicker({
                showSeconds: false,
                showMeridian: false,
            });

            get_tmp_log_error_msg();
            get_tmp_log_fail_to_proc();
        });
    </script>
@endsection
