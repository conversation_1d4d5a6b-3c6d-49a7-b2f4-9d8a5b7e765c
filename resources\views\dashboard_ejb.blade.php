@extends('layouts.guest-dash')

@section('header')
    
@endsection

@section('content')
    <!-- Content -->
<style>
    code[class^="language-"], code[class*=" language-"], pre[class^="language-"], pre[class*=" language-"] {
        white-space: pre-line;
    }
</style>

<div class="content-header">
    <div class="header-section">
        <h1>
            <i class="fa fa-cubes"></i>Monitoring EJB Services <br>
              
        </h1>
        <div class="widget-options">
            <div class="btn-group btn-group-lg">
                <a href="#modal-confirm-request" data-toggle="modal"
                   class="btn btn-default" data-toggle="tooltip" title="" data-original-title="Refresh">
                    <i class="fa fa-tachometer"></i></a>
            </div>
        </div>
    </div>

</div>

    @foreach ($listdata as $key => $node)

        @if(($key % 2) == 0) 
        <div class="row">
        @endif
            <div class="col-lg-6">
                
                @if($node['status'] === 'Error')
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Server EJB : <strong>{{$node['host']}} </strong>
                            <span class="label label-success"> {{$node['post']}}</span>
                        </h5>
                        <div  class="widget-extra-full">
                            <table class="table table-borderless table-striped table-vcenter table-bordered">
                                <tbody>
                                    <tr>
                                        <th>Result</th>
                                    </tr>
                                    <tr>
                                        <td >{{$node['result']}}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                @else
                <div class="widget">
                   
                    <div class="widget-extra themed-background-dark">
                        
                        <h5 class="widget-content-light">
                            Server EJB : <strong>{{$node['result']['Host']}}</strong>
                            <span class="label label-success"> {{count($node['result']['Services'])}} Services Running </span>
                        </h5>
                        
                    </div>
                    <div  class="widget-extra-full">
                        <table class="table table-borderless table-striped table-vcenter table-bordered">
                            <tbody>
                                <tr>
                                    <th>Service Name</th>
                                    <th>DateTime Start Execute</th>
                                    <th>Thread Name</th>
                                    <th>In Duration (Time)</th>
                                </tr>
                                @foreach ($node['result']['Services'] as $service)
                                <tr @if($service['DurationTime'] >= 60000) style="background-color: darkred;color: #fff;" @endif>
                                    <td class="text-left ejb-service-running">
                                        @if($service['DurationTime'] >= 60000)
                                        <i class="fa fa-warning"></i> &nbsp; &nbsp; 
                                        @endif
                                        <a href="#modal-list-data" 
                                           class="modal-list-data-action label label-danger" 
                                           data-toggle="modal" data-url="/find/ejb/service/detail?host={{$node['result']['Host']}}&port={{$node['result']['Port']}}&key={{$service['key']}}" 
                                           data-title="List of parameters service">
                                           <strong>{{ $service['ServiceName'] }}</strong>
                                        </a>

                                    </td>
                                    <td ><a href="javascript:void(0)">{{Carbon\Carbon::createFromTimestampMs($service['StartTime'])->toDateTimeString()  }}</a></td>
                                    <td >{{ $service['ThreadName'] }}</td>
                                    <td class="text-right"><strong>{{ $service['DurationTime'] }}   &raquo;  {{gmdate("H:i:s", ($service['DurationTime']/1000))}}</strong></td>
                                </tr>
                                @endforeach

                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
               
            </div>
        
        @if(($key % 2) != 0) 
        </div>
        @endif
    
    @endforeach
   
    
    <div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="table-responsive">
                                <table id="basic-datatable" class="table table-striped table-vcenter">
                                </table>
                                <div class='block' id='panel_json_desc' style=''>
                                    <div class='block-title'>
                                        <h2><strong>Parameter Service</strong> <span id="modal-service-name-display" class="label label-success">Service</span></h2>
                                    </div>

                                    <pre id='pre_json_desc' class=''>                         
                                        <code style='float: left; color:#fff;' id='code_json_desc'>

                                        </code>
                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    
    
    <div id="modal-confirm-request" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title-request"><i class="fa fa-list"></i> <span id="modal-list-data-header">Start Service EJB Server</span></h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            
                            <div class='block' >
                                <div class='block-title'>
                                    <h2><strong>Start All Servers EJB </strong> <span id="modal-service-name-display" class="label label-success">
                                        This action will start up Service Monitoring Map for each EJB Server. All data services running in Map Memory will be reset.
                                        </span></h2>
                                </div>
                                <div class="text-center spinner-loading-request" style="padding: 20px; display:none;">
                                    <i class="fa fa-spinner fa-4x fa-spin"></i>
                                </div>
                                <div id="response_data">
                                    
                                </div>    
                                <form id="form-task" action="{{url("/find/ejb/service/start")}}" method="get" class="form-horizontal form-bordered" >
                                    <div class="col-md-12">
                                        <fieldset>

                                            <div class="form-group">
                                                <label class="col-md-3 control-label" for="security_phrase">Please type OK <span class="text-danger">*</span></label>
                                                <div class="col-md-9">
                                                    <div class="input-group">
                                                        <input type="text" id="security_phrase" name="security_phrase" class="form-control" 
                                                               placeholder="Please type OK" required="true">
                                                        <span class="input-group-addon"><i class="gi gi-qrcode"></i></span>
                                                    </div>
                                                </div>
                                            </div>


                                        </fieldset>
                                    </div>

                                    <div class="form-group form-actions form-actions-button">
                                        <div class="col-md-8 col-md-offset-4">
                                            <span id="btn_start_span" class="btn btn-sm btn-primary"><i class="fa fa-arrow-right"></i>
                                               START MONITORING SERVICE EJB </span>
                                            <a  id="btn_refresh_span" href="{{url("/dashboard/ejb")}}"  class="btn btn-sm btn-info" style="display:none;"><i class="fa fa-refresh"></i>
                                               REFRESH PAGE </a>    
                                        </div>
                                    </div>
                                </form>
                            </div>
                            
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    
    
  
    <!-- END Content -->
@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->

    <script>
        var APP_URL = {!! json_encode(url('/')) !!}
        
            
        $(document).ready(function () {
            
            $('.ejb-service-running').on("click", '.modal-list-data-action', function () {

                $('.spinner-loading').show();

                $('#modal-list-data-header').text($(this).attr('data-title'));

                $.ajax({
                    url: APP_URL + $(this).attr('data-url'),
                    type: "GET",
                    success: function (data) {
                        //console.log(data);
                        $('#modal-service-name-display').html(JSON.stringify(data.result.Key).trim()).fadeIn();
                        $('#code_json_desc').html(JSON.stringify(data.result.ParameterDetail).trim()).fadeIn();
                        $('.spinner-loading').hide();
                    }
                });

            });
           
            $('#btn_start_span').on("click", function(){
                $("#btn_refresh_span").hide();
                $('.spinner-loading-request').show();
                var security_phrase = $("#security_phrase").val();
                $.ajax({
                    url: APP_URL + '/find/ejb/service/start?security_phrase='+security_phrase,
                    type: "GET",
                    success: function (data) {
                        
                        $('.spinner-loading-request').hide();
                        
                        var textResp = "<div class='alert alert-danger alert-dismissable'><h4><i class='fa fa-check-circle'></i> Failed</h4> Start Service Monitoring EJB  <a href='javascript:void(0)' class='alert-link'>not successful</a>!</div>";
                        if(data.status === 'Success'){
                            textResp = "<div class='alert alert-success alert-dismissable'><h4><i class='fa fa-check-circle'></i> Success</h4> Start Service Monitoring EJB  <a href='javascript:void(0)' class='alert-link'>Done</a>!</div>";
                            $("#btn_start_span").hide();
                            $("#btn_refresh_span").show();
                        }
                        $("#response_data").html('');
                        $("#response_data").append(textResp);
                    }
                });
            });
            
        });
    </script>
    <!-- Load and execute javascript code used only in this page -->
@endsection
