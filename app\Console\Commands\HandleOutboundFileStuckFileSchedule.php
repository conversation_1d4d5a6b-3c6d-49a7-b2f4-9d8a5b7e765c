<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Carbon\Carbon;
use App\Migrate\ResendFileBatchOUT;
use Mail;
use Log;
use Config;
use DB;

class HandleOutboundFileStuckFileSchedule extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'ep-out-trigger-file-stuck';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'This will check file in OUT folder to call OSB pickup file then send file to Integration Third Party Server';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct() {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle() {
        Log::info(self::class . ' starting ..', [
            'Date' => Carbon::now()]);
        try {
            ResendFileBatchOUT::triggerStuckFileMyGpis();
            sleep(5);
            //Remove MyGPIS in osb_batch_retry_dtl.
            DB::connection('oracle_nextgen_fullgrant')->table('osb_batch_retry_dtl')->where('target_system','MyGPIS')->delete();

            ResendFileBatchOUT::triggerStuckFilePHIS(); 
            sleep(5);
            //Remove PHIS in osb_batch_retry_dtl.
            DB::connection('oracle_nextgen_fullgrant')->table('osb_batch_retry_dtl')->where('target_system','PHIS')->delete();

            ResendFileBatchOUT::triggerStuckFileLMS();
            sleep(5);
            //Remove LMS in osb_batch_retry_dtl.
            DB::connection('oracle_nextgen_fullgrant')->table('osb_batch_retry_dtl')->where('target_system','LMS')->delete();

        } catch (\Exception $exc) {

            \Log::error(self::class . '>> error happen!! ' . $exc->getMessage());
            \Log::error(self::class . '>> error happen!! ' . json_encode($exc->getTrace()));
            \Log::error(self::class . '>> error happen!! ' . $exc->getTraceAsString());
            $this->sendErrorEmail(json_encode($exc->getTrace()));
        }
        
    }

    /**
     * Send  error
     *
     * @param  Request  $error
     * @return Response
     */
    protected function sendErrorEmail($error) {
        $data = array(
            "to" => ['<EMAIL>'],
            "subject" => get_class($this) . ':: Server ('.env('APP_ENV').') - Error'
        );
        try {
            Mail::send('emails.errorSchedulerMail', ['subject' => $data["subject"], 'date' => Carbon::now()->toDateString(), 'error' => $error], function($m) use ($data) {
                $m->from(Config::get('constant.email_sender'), Config::get('constant.email_sender_name'));
                $m->to($data["to"])
                        //->cc($data["cc"])
                        ->subject($data["subject"]);
            });
        } catch (\Exception $e) {
            Log::error('{CLASS => ' . get_class($this) . '}{FUNCTION => } => sendErrorEmail ', ['Email' => $data["to"], 'ERROR' => $e->getMessage()]);
            return $e;
        }
    }
    
}
