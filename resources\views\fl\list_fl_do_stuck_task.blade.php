@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>FL (DO) Stuck Task <br>
                <small>FL Stuck Task DO List</small>
            </h1>
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title " >
                <h1><i class="fa fa-tasks"></i> <strong>FL (DO) Stuck Task List</strong></h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>No Stuck Tasks Available </p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>FL  (DO)  Stuck Task List </strong>
                        <small></small>
                    </h1>
                </div>
                <p class="text-danger bolder">
                    <h5>&nbsp;</h5> 
                    &nbsp;
                    Ask Middleware team to refire all tasks
                </p>
                <a href="{{url('/crm/guideline/stuck_gfmas/PENDING_DO-STL_GL15082019.docx')}}" style="color: #3498db; text-decoration: underline;">Guideline Refire Task Pending DO Acknowledgement </a><br/><br/>
                <div class="table-responsive">
                    <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">DOC ID</th>
                            <th class="text-center">DOC_NO</th>
                            <th class="text-center">PO/CO NO</th>
                            <th class="text-center">TRACKING DIARY STATUS</th>
                            <th class="text-center">CREATED_DATE</th>
                            

                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->doc_id }}</td>
                                <td class="text-center"><a target="_blank" href="{{url('find/fulfilment/do/search/?poco_no=')}}{{ $data->poco_no  }}&do_no={{ $data->doc_no }}" >{{ $data->doc_no }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('find/trans/track/docno')}}/{{ $data->poco_no  }}" >{{ $data->poco_no }}</a></td>
                                <td class="text-center">{{ $data->tracking_diary_status }}</td>
                                <td class="text-center">{{ $data->created_date }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script>
    
    
</script>
@endsection



