/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */

//click on bpmn flow
$('.bpmnProcess').click(function () {

    $('tr').removeClass('hover');
    $(this).parent().addClass('hover');

    document.getElementById("failed_ul").style.display = "none";
    var id = this.id;
    var processId = $(this).attr('data-id');
    var status = $(this).attr('data-status');
    var cmpstId = $(this).attr('data-cmpstId');
   
    var csrf = $("input[name=_token]").val();
    var suspendBtn = document.getElementById("suspend_bpmn");
    var resumeBtn = document.getElementById("resume_bpmn");
    var alterflowBtn = document.getElementById("alterflow_bpmn");
    var variableBtn = document.getElementById("variable_bpmn");

    document.getElementById("bpmn-flow").style.display = "none";
    document.getElementById("task-flow").style.display = "none";
    $('#modal_spinner').modal('show');
    $.ajax({
        type: "POST",
        url: "/bpm/process/bpmn/" + processId + "/" + status,
        data: {"_token": csrf, "process_id": processId, "status": status, "composite_id":cmpstId },
        error: function (xhr, status, error) {
            document.getElementById("failed_ul").style.display = "block";
            console.log('click bpmnProcess ' + xhr.status + ': ' + xhr.statusText);
            $("#failed").html(xhr.status + ': ' + xhr.statusText);
        }
    }).done(function (resp) {
        $('#modal_spinner').modal('hide');
        if (resp.statusAPI === null) {
            $('#failed').hide();
            //Disable suspend and resume button when status is completed (5), terminated (8)
            if (resp.status == 5 || resp.status == 8) {
                suspendBtn.disabled = true;
                resumeBtn.disabled = true;
                alterflowBtn.disabled = true;
                variableBtn.disabled = true;

                //allow to resume when status suspended (2)
            } else if (resp.status == 2) {
                suspendBtn.disabled = true;
                resumeBtn.disabled = false;
                alterflowBtn.disabled = false;
                variableBtn.disabled = false;
            } else {
                suspendBtn.disabled = false;
                resumeBtn.disabled = false;
                alterflowBtn.disabled = false;
                variableBtn.disabled = false;
            }

            var html = '<thead style="background-color:#f2f2f2;"><tr><th>Name</th><th>Activity</th><th>Flow In</th><th>Flow Out</th></tr></thead>';

            for (const [listdata, value] of Object.entries(resp.audits)) {
                html += '<tbody style="font-size:80%;"><tr>';
                if (value["operation_flow"] === 'INSTANCE_CREATED') {
                    html += "<td><strong>CREATED</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_SUSPENDED') {
                    html += "<td><strong>SUSPENDED</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_TERMINATED') {
                    html += "<td><strong>TERMINATED</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_ABORTED') {
                    html += "<td><strong>ABORTED</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_SYSTEM_FAULT') {
                    html += "<td><strong>SYSTEM_FAULT</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_RESUMED') {
                    html += "<td><strong>RESUMED</strong></td>";
                    html += "<td></td>";
                } else if (value["operation_flow"] === 'INSTANCE_UPDATED') {
                    html += "<td><strong>UPDATED</strong></td>";
                    html += "<td></td>";
                } else {
                    html += "<td>" + value["name"] + "</td>";
                    html += "<td>" + value["activity_id"] + "</td>";
                }

                var flowIn = "<td style='color: green'><i class='fa fa-arrow-circle-down'></i> ";
                var flowOut = "<td style='color: blue'><i class='fa fa-arrow-circle-up'></i> ";
                var flowMoved = "<td style='color: purple'><i class='fa fa-spinner'></i> ";
                var flowFaulted = "<td style='color: red'><i class='fa fa-times-circle'></i> ";
                var flowDefaulted = "<td style='color: grey'><i class='fa fa-circle'></i> ";

                for (const [data, val] of Object.entries(value["create_time_long"])) {

                    if (value["create_time_long"].length === 1) {

                        if (val[0] === 'INSTANCE_SYSTEM_FAULT') {
                            html += flowFaulted + val[1] + "</td>";
                            html += flowFaulted + val[1] + "</td>";
                        } else if (val[0] === 'FLOW_NODE_IN') {
                            html += flowIn + val[1] + "</td>";
                            html += flowDefaulted + val[1] + "</td>";
                        } else {
                            html += flowDefaulted + val[1] + "</td>";
                            html += flowDefaulted + val[1] + "</td>";
                        }

                    } else if (value["create_time_long"].length === 2) {

                        if (val[0] === 'FLOW_NODE_OUT') {
                            html += flowOut + val[1] + "</td>";
                        } else if (val[0] === 'FLOW_NODE_IN') {
                            html += flowIn + val[1] + "</td>";
                        } else if (val[0] === 'FLOW_NODE_MOVED') {
                            html += flowMoved + val[1] + "</td>";
                        }

                    } else {

                        var len = value["create_time_long"].length;
                        len--;
                        if (data == len) {
                            if (val[0] === 'FLOW_NODE_IN' || val[0] === 'FLOW_NODE_OUT') {
                                html += flowIn + val[1] + "</td>";
                                html += flowOut + val[1] + "</td>";
                            } else if (val[0] === 'FLOW_NODE_IN' || val[0] === 'FLOW_NODE_MOVED') {
                                html += flowIn + val[1] + "</td>";
                                html += flowMoved + val[1] + "</td>";
                            } else {
                                html += "<td>" + val[1] + "</td>";
                                html += "<td>" + val[1] + "</td>";
                            }
                        }
                    }
                }

                //faults
                if (typeof (resp.faults["message"]) != "undefined" && resp.faults["message"] !== null) {
                    document.getElementById("faulted").style.display = "block";
                    $("#fault_body").html(resp.faults["message"]); 

                    var createdDate = new Date(resp.faults["createdDate"]);
                    let formatted_date = createdDate.getDate() + "/" + (createdDate.getMonth() + 1) + "/" + createdDate.getFullYear() + " " + createdDate.getHours() + ":" + (createdDate.getMinutes() < 10 ? '0' : '') + createdDate.getMinutes() + ":" + (createdDate.getSeconds() < 10 ? '0' : '') + createdDate.getSeconds();
                    let span = document.getElementById("faulted");
                    span.style.color = "Red";
                    span.style.textDecoration  = "underline";
                    span.textContent = "FAULT " + formatted_date;
                } else {
                    document.getElementById("faulted").style.display = "none";
                }
            }
            html += "</tr><tbody>";
            document.getElementById("bpmn-table").innerHTML = html;

            suspendBtn.value = 'suspend';
            suspendBtn.onclick = function () {
                suspendResumeAction(suspendBtn.value, processId, status, csrf, resp.instance_id);
            };

            resumeBtn.value = 'resume';
            resumeBtn.onclick = function () {
                suspendResumeAction(resumeBtn.value, processId, status, csrf, resp.instance_id);
            };

            alterflowBtn.value = 'alterflow';
            alterflowBtn.onclick = function () {
                alterflowAction(alterflowBtn.value, processId, status, csrf, resp.instance_id);
            };

            variableBtn.value = 'variable';
            variableBtn.onclick = function () {
                variableAction(variableBtn.value, processId, status, csrf, resp.instance_id);
            };

            document.getElementById("bpmn-flow").style.display = "block";
        } else {
            document.getElementById("bpmn-flow").style.display = "none";
            console.log('else click bpmnProcess ' + resp.statusAPI);
            document.getElementById("failed_ul").style.display = "block";
            $("#failed").html(resp.statusAPI);
        }
    });

});