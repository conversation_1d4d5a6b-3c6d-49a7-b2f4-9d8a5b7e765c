@extends('layouts.guest-dash')

@section('header')
<div class="content-header">
    <div class="header-section">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/dashboard/main/') }}">Main</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/batch/') }}">Batch File</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/osb/') }}">OSB</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/igfmas/') }}">IGFMAS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/phis/') }}">PHIS</a>
            </li>
            <li class="active">
                <a href="{{ url('/dashboard/paymentreceipt/') }}">Payment AR502</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/gfmascrm/') }}">CRM</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/mygpis/') }}">MyGPIS</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/egpa/') }}">EGPA</a>
            </li>
            <li>
                <a href="{{ url('/dashboard/spki/') }}">SPKI</a>
            </li>
        </ul>
    </div>
</div>
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
    <div class="col-lg-12">
        <div class='widget'>
            <div class='widget-extra themed-background-dark'>
                <h5 class='widget-content-light'>
                    Statistic Sync eP Payment Receipt With Razer <strong> </strong>
                    <small>Last 14 days</small>
                </h5>
            </div>
            <div id="dash_monitoringSyncPaymentReceipt">
                <div class="text-center" style="padding: 20px;"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
            </div>
        </div>
    </div>
</div>
<div class="row">
    <div class="col-lg-12">
        <div class="widget">
            
            <div class="widget-extra themed-background-dark">
                <h5 class="widget-content-light">
                        <strong>List Transaction Wrong Card Type With MBB & Razer</strong>
                        <small>Checking card type from MBB report is not same with Razer report</small>
                </h5>
            </div>
            <div id="dash_maybank_razer_monitoring_card_type" class="widget-extra-full">
                <div class="table-responsive">
                    <table id="monitor-cardtype-maybank-razer-datatable" class="table table-vcenter table-condensed">
                        
                    </table>
                </div>
            </div>
        </div>
    </div>
 </div>
<div class="row">
    <div class="col-lg-12">
        <div class="widget">
            
            <div class="widget-extra themed-background-dark">
                <div class="widget-options">
                    <div class="btn-group btn-group-xs">
                        <a href="{{url("/app-support/file/upload-mbb")}}" target="_blank" class="btn btn-info"
                            data-toggle="tooltip" title="" data-original-title="Upload MBB File"><i class="fa fa-upload"> Upload Maybank File</i></a>
                    </div>
                </div>
                <h5 class="widget-content-light">
                        <strong>List Transaction Maybank But Failed In Razer</strong>
                        <small>Technical team to do manual patching Callback URL</small>
                </h5>
            </div>
            <div id="dash_maybank_razer_monitoring" class="widget-extra-full">
                <div class="table-responsive">
                    <table id="monitor-maybank-datatable" class="table table-vcenter table-condensed">
                        
                    </table>
                </div>
            </div>
        </div>
    </div>
 </div>
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div  class="row" style="padding: 0 15px 15px 0; display: block;">
                    <div id="pnl-top-right-btn" class="hide pull-right">
                        <a href="{{ url('dashboard/paymentreceipt/download-all-receipts') }}" id="req-download-btn" class="btn btn-sm btn-primary toggle-bordered enable-tooltip" title="" 
                        data-original-title="Request to download all receipts"><i class="gi gi-disk_import"></i> Download All</a>
                        
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>
<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>
    $('#page-container').removeAttr('class');
</script>
<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    /* Initialize Datatables */
    var tableListMbbMonitorData = $('#monitor-maybank-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    /* Initialize Datatables */
    var tableListMbbCardTypeMonitorData = $('#monitor-cardtype-maybank-razer-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    



    $(document).ready(function () {

        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            var isremoveDatatable = $(this).attr('data-isremoveDatatable');
            var isbtndownload = $(this).attr('data-isbtndownload');
            var urldownloadall = $(this).attr('data-urldownloadall');
            
            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    //Checking to enable download receipt eP
                    if (typeof isbtndownload !== 'undefined' && isbtndownload === 'true') {
                        $('#pnl-top-right-btn').removeClass( "hide" );
                        $('#req-download-btn').attr('href', APP_URL +urldownloadall);
                    }else{
                        $('#pnl-top-right-btn').addClass( "hide" );
                    }

                    if (typeof isremoveDatatable !== 'undefined' && isremoveDatatable === 'true') {
                        console.log(isremoveDatatable);
                         /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            searching: false, paging: false, info: false, dom: 'lrt',bFilter: false,ordering: false,
                            "sScrollY": "70px", "sScrollX": "100%" 
                        });
                    }else {
                        /* Re-Initialize Datatable */
                        tableListData = $('#basic-datatable').DataTable({
                            columnDefs: [{orderable: false, targets: [0]}],
                            pageLength: 10,
                            lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                        });
                    }
                    $('.spinner-loading').hide();
                }
            });

        });
        //Monitoring Payment
        $.ajax({
            url: APP_URL + '/dashboard/paymentreceipt/checkSyncPaymentReceiptEpRazer',
            type: "GET",
            success: function (data) {
                $data = $(data);
                $('#dash_monitoringSyncPaymentReceipt').hide().html($data).fadeIn();
            }
        });

        //Monitoring Maybank Success Razer Failed
        $.ajax({
            url: APP_URL + '/dashboard/paymentreceipt/show-maybank-with-razer-failed',
            type: "GET",
            success: function (data) {
                tableListMbbMonitorData.destroy();
                $('#monitor-maybank-datatable').html(data).fadeIn();
                        
                /* Re-Initialize Datatable */
                tableListMbbMonitorData = $('#monitor-maybank-datatable').DataTable({
                    columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                    pageLength: 10,
                    lengthMenu: [[10, -1], [10, 'All']]
                });
            }
        });

        //Monitoring Maybank Card Type not tally with razer
        $.ajax({
            url: APP_URL + '/dashboard/paymentreceipt/show-wrong-card-type',
            type: "GET",
            success: function (data) {
                tableListMbbCardTypeMonitorData.destroy();
                $('#monitor-cardtype-maybank-razer-datatable').html(data).fadeIn();
                        
                /* Re-Initialize Datatable */
                tableListMbbCardTypeMonitorData = $('#monitor-cardtype-maybank-razer-datatable').DataTable({
                    columnDefs: [ { orderable: false, targets: [ 0 ] } ],
                    pageLength: 10,
                    lengthMenu: [[10, -1], [10, 'All']]
                });
            }
        });
        

    });
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
