<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\AppSupport;

use Carbon\Carbon;
use Log;
use DB;
use SSH;
use Excel;
use App\Migrate\MigrateUtils;
use App\Services\Traits\SupplierService;
use Guzzle;
use GuzzleHttp\Client;
use App\EpSupportActionLog;

class SmSupplierResendApiveDueToSchedulerSkip {

    /**
     * to get pattern list of supplier changed_date range 1pm to 1:59pm . At range this time, scheduler sent to APIVE is not running.
     */
    public static function reUpdateChangeDateSmSupplierWithinRange1PM(){
        $list =  DB::connection('oracle_nextgen_fullgrant')
                    ->select("SELECT 
                    DISTINCT 
                        ss.SUPPLIER_ID ,
                        ss.company_name,
                        ss.ep_no,
                        ss.changed_date
                    FROM
                        sm_supplier ss,
                        sm_appl sa
                    WHERE ss.LATEST_APPL_ID = sa.APPL_ID 
                        AND ss.changed_date >= to_date('2024-01-01','YYYY-MM-DD')
                        AND ss.record_status = 1 
                        AND TO_CHAR(ss.changed_date,'HH24') = 13
                        AND sa.status_id IN (20199) 
                        AND ROWNUM < 11 ");
        MigrateUtils::logDump(__METHOD__ .' total record : '.count($list));
        foreach($list as $obj){
            MigrateUtils::logDump(__METHOD__ .' data >> '.json_encode($obj));
            $changeDateHour = Carbon::parse($obj->changed_date)->hour;
            if($changeDateHour == 13){
                DB::connection('oracle_nextgen_fullgrant')->table('sm_supplier')->where('supplier_id',$obj->supplier_id)
                    ->update(['changed_date'=>Carbon::now()->addMinute(1),'changed_by'=>1]);
            } 
        }
    }
    
}
