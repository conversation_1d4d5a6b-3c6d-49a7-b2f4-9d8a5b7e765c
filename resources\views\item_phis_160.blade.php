@extends('layouts.guest-dash')

@section('header')

<!-- END Search Form -->
@endsection
@section('content')
<style>
    .table thead > tr > th {
        font-size: 11px;
    }
    .table tbody > tr > td {
        font-size: 10px;
    }
</style> 
<div class="block"> 
    <form id="carianforms" class="form-horizontal">
        <div class="form-group">
            <label class="col-md-3 control-label" for="doc__no">Document No.<span class="text-danger">*</span></label>
            <div class="col-md-5">
                <input type="text" id="doc_no" name="doc_no" value="{{$docNo}}" class="form-control" placeholder="CO/CR/PHIS No..." required>
            </div>
        </div>
        <div class="form-group">
            <label class="col-md-3 control-label" for="action">Action<span class="text-danger">*</span></label>
            <div class="col-md-5">
                <select id='triggeraction' name='triggeraction' class="form-control" data-placeholder="Trigger Action.." required>
                    <option></option> 
                    <option value="CRE" @if(old('triggeraction') === "CRE") selected @endif>CREATE</option>
                    <option value="REJ" @if(old('triggeraction') === "REJ") selected @endif>REJECT</option>
                </select>
            </div>
        </div>
        <div class="form-group form-actions">
            <div class="col-md-9 col-md-offset-3">
                <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
            </div>
        </div>
    </form>
</div>
@if ($contractOrderInfo == null && $docNo !== null)
<div class="block block-alt-noborder full text-center label-primary">
    <span style="color: #FFF;">Tidak dijumpai! Sila masukkan nombor dokumen (CO/CR/PHIS) sahaja.</span>
</div>
@endif
@if($contractOrderInfo !== '')
@if(count($contractOrderInfo) > 0)
<div class="block"> 
    <div class="block-title panel-heading epss-title-s1">
        <h2><i class="fa fa-building-o"></i> <strong>CONTRACT ORDER INFORMATION : PHIS-160</strong></h2>
        <div class="block-options pull-right action-today">
            <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
               data-toggle="modal" data-url="{{url('/support/report/log/trigger-item-phis-160')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
               data-title="List Action Patch Data Today ">View Today Action</a>
        </div>
    </div>
    <div class="table-responsive">
        <div id="response" class="table-options clearfix display-none">
            <div id="response-msg" class="text-center text-light" colspan="6"></div>
        </div>
        <table id="phis-datatable" class="table table-vcenter table-condensed table-bordered">
            <thead>
                <tr>
                    <th class="text-center">CONTRACT REQUEST NO</th>
                    <th class="text-center">CONTRACT ORDER NO</th>
                    <th class="text-center">PHIS NO</th>
                    <th class="text-center">CONTRACT PHYSICAL NO</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($contractOrderInfo as $contract)
                <tr>
                    <td class="text-center">{{ $contract->contractreqno }}</td>
                    <td class="text-center">{{ $contract->contractorderno }}</td>
                    <td class="text-center">{{ $contract->phisno }}</a></td>
                    <td class="text-center">{{ $contract->contractphysicalno }}</td>
                </tr>
                @endforeach 
            </tbody>
        </table>
        @if($phis !== '')
        <a href="javascript:void(0)" data-toggle="collapse" data-target="#payload">Payload Here</a>
        <div class="btn-group btn-group-xs action-form pull-right">

            <div class="btn-group btn-group-xs action-form">

                <a class="btn btn-primary action_trigger"  title="" data-original-title="Trigger!"
                   href="#modal_confirm" data-toggle="modal"
                   data-docno="{{$docNo}}"
                   data-payload="{{$payload}}"
                   ><i class="hi hi-transfer"></i>Trigger</a>

            </div>

        </div>
        @endif
    </div>
    <div class="block collapse panel-xml" id="payload">
        <div class="block-title">
            <div class="block-options pull-right">
                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                      onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');" >Close</span>
            </div>
        </div>
        <pre class="line-numbers">
                <code class="language-markup">{{$payload}}</code>
        </pre>
    </div>
</div>
@endif
@endif

<div id="modal_confirm" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
    <form id="form-search-mminf" action="" method="post" class="form-horizontal" onsubmit="return true;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Are you sure you want to trigger PHIS-160? </h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="form-group">
                                <label class="col-md-3 control-label">Doc No. </label>
                                <div class="col-md-9">
                                    <input type="hidden" id="docno" value="" />
                                    <input type="hidden" id="payload" value="" />
                                    <p id="docno_display" class="form-control-static"></p>
                                </div>
                            </div>
                            <div class="form-group form-actions">

                                {{ csrf_field() }}
                                <div class="col-md-3 col-md-offset-9">
                                    <button type="submit" id="submit-btn" class="btn btn-sm btn-primary action_confirm"><i class="gi gi-ok_2"></i> Yes</button>
                                    <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="gi gi-remove_2"></i> No</button>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div></form>
</div>

@include('_shared._modalListLogAction')
@endsection

@section('jsprivate')
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function () {
            ModalListActionLogDatatable.init();
        });</script>
<script>
    $('div.action-form').on("click", 'a.action_trigger', function () {
        var docno = $(this).attr('data-docno');
        var payload = $(this).attr('data-payload');
        $("#docno").val(docno);
        $("#payload").val(payload);
        $("#docno_display").text(docno);
        console.log("Doc Co No : " + docno);
    });
    $('div.form-actions').on("click", 'button.action_confirm', function (e) {
        var docno = $("#docno").val();
        var payload = $("#payload").val();
        var csrf = $("input[name=_token]").val();
        console.log("Doc Co No After Click Yes : " + docno);

        $('#modal_confirm').modal('hide');
        $('#wait-modal').modal('toggle');
        $.ajax({
            url: "/find/updatephis160/update",
            method: "POST",
            dataType: "json",
            data: {"_token": csrf, "doc_no": docno, "payload_full": payload},
            context: document.body
        }).done(function (resp) {
            if (resp.docCoNo !== '') {
                $("#response").show();
                $("#response").addClass("label-success");
                $("#response-msg").html("Successfully Updated!");
                $('#wait-modal').modal('hide');

            } else {
                $("#response").show();
                $("#response").addClass("label-danger");
                $("#response-msg").html("Updated Failed! Please try again.");
                $('#wait-modal').modal('hide');
            }
        });
    });
</script>
@endsection
