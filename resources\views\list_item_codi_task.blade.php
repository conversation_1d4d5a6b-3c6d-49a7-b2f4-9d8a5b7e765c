@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/items/codi-task')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Carian Item <PERSON>embekal (Codification Task)<br>
                <small>Masukkan Dokumen Number (RNC), Nama Item, Identification No (ICNO)  pada carian diatas...
                <br />
                <strong>Senarai Item yang ditugaskan kepada Codification Team mengikut status</strong>
                </small>
            </h1>
        </div>
    </div>

    
    
    <div class="block">
        <div class="block-title">
            <h2><strong>Carian</strong></h2>
            <div class="block-options pull-right action-today">
                <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                   data-toggle="modal" data-url="{{url('/support/report/log/item-codification')}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                   data-title="List Action Today ">View Today Action</a>
            </div>
        </div>

        <div class="block">

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <form id="form-search-mminf" action="{{url("/find/items/codi-task")}}" method="post" class="form-horizontal" onsubmit="return true;">
                {{ csrf_field() }}
                <input name="_method" id="_method"  type="hidden" value="POST">

                <div class="form-group">
                    <label class="col-md-3 control-label" for="date_start">Carian </label>

                    <div class="col-md-5">
                        <input type="text" id="cari" name="cari" class="form-control" 
                                placeholder="Identification No. " 
                                value="@if(old('cari') != null){{old('cari')}}@else{{$carian}}@endif">
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-md-3 control-label" for="date_start">Tarikh </label>

                    <div class="col-md-5">
                        <input type="text" id="tarikh" name="tarikh" class="form-control input-datepicker-close" 
                                data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                                value="@if(old('tarikh') != null){{old('tarikh')}}@else{{Carbon\Carbon::now()->format('Y-m-d')}}@endif">
                    </div>
                </div>

                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <button type="reset" class="btn btn-sm btn-primary"><i class="gi gi-restart"></i> Reset</button>
                    </div>
                </div>
            </form>

        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
                </div>
                <div class="row">
                  <div class="col-sm-6">
                      @if(isset($error))
                      <p>Carian mesti lebih 3 aksara!</p>
                      @else
                      <p>Tidak dijumpai!</p>
                      @endif
                  </div>
                </div>
            </div>
        </div>
        @endif
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Senarai Item Pembekal  (Codification Task)</strong>
                        <small>Hasil Carian : {{$carian}}</small>
                    </h1>
                    
                </div>

                <div class="table-responsive">
                    <table id="item-codification-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">TARIKH</th>
                            <th class="text-center">IC NO.</th>
                            <th class="text-center">NAME</th>
                            <th class="text-center">DOC_NO</th>
                            <th class="text-center">PRODUCT_NAME</th>
                            <th class="text-center">COMPANY_NAME</th>
                            <th class="text-center">EP_NO</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-left">{{ $data->created_date_status }}</td>
                                <td class="text-left">{{ $data->identification_no }}</td>
                                <td class="text-left">{{ $data->name }}</td>
                                <td class="text-left">{{ $data->doc_no }}</td>
                                <td class="text-left">{{ $data->item_name }}</td>
                                <td class="text-left">{{ $data->company_name }}</td>
                                <td class="text-left">{{ $data->ep_no }}</td>
                                <td class="text-left">{{ $data->status_name }}</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    
       
    @endif

    @include('_shared._modalListLogAction')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    
</script>
@endsection



