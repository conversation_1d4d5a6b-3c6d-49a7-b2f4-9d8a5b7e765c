/*
 *  Document   : plugins.css
 *  Author     : Various
 *  Description: Stylesheet of various plugins in one file for consistency.
 *
 *  Includes (with shortcode):
 *      (#01fas) Font Awesome
 *      (#02gps) Glyphicons PRO
 *      (#03cas) CSS3 ANIMATION CHEAT SHEET
 *      (#04mps) Magnific Popup
 */

/*
=================================================================
(#01fas)  Font Awesome 4.5.0 by @davegandy - http://fontawesome.io - @fontawesome

License - http://fontawesome.io/license (Font: SIL OFL 1.1, CSS: MIT License)
=================================================================
 */
@font-face{font-family:'FontAwesome';src:url('fonts/fontawesome/fontawesome-webfont.eot?v=4.5.0');src:url('fonts/fontawesome/fontawesome-webfont.eot?#iefix&v=4.5.0') format('embedded-opentype'),url('fonts/fontawesome/fontawesome-webfont.woff2?v=4.5.0') format('woff2'),url('fonts/fontawesome/fontawesome-webfont.woff?v=4.5.0') format('woff'),url('fonts/fontawesome/fontawesome-webfont.ttf?v=4.5.0') format('truetype'),url('fonts/fontawesome/fontawesome-webfont.svg?v=4.5.0#fontawesomeregular') format('svg');font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=1);-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2);-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=3);-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1);-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{filter:progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1);-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}

/*
=================================================================
(#02gps) Glyphicons PRO

Project:  GLYPHICONS
Author:   Jan Kovarik - www.glyphicons.com
Twitter:  @jankovarik
=================================================================
*/
@font-face{font-family:'Glyphicons Regular';src:url('fonts/glyphicons.pro/glyphicons-regular.eot');src:url('fonts/glyphicons.pro/glyphicons-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.pro/glyphicons-regular.woff') format('woff'),url('fonts/glyphicons.pro/glyphicons-regular.ttf') format('truetype'),url('fonts/glyphicons.pro/glyphicons-regular.svg#glyphiconsregular') format('svg')}@font-face{font-family:'Glyphicons Halflings Regular';src:url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.eot');src:url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.woff') format('woff'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.ttf') format('truetype'),url('fonts/glyphicons.halflings.pro/glyphicons-halflings-regular.svg#glyphicons_halflingsregular') format('svg')}@font-face{font-family:'Glyphicons Social Regular';src:url('fonts/glyphicons.social.pro/glyphicons-social-regular.eot');src:url('fonts/glyphicons.social.pro/glyphicons-social-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.woff') format('woff'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.ttf') format('truetype'),url('fonts/glyphicons.social.pro/glyphicons-social-regular.svg#glyphicons_socialregular') format('svg')}@font-face{font-family:'Glyphicons Filetypes Regular';src:url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.eot');src:url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.eot?#iefix') format('embedded-opentype'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.woff') format('woff'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.ttf') format('truetype'),url('fonts/glyphicons.filetypes.pro/glyphicons-filetypes-regular.svg#glyphicons_filetypesregular') format('svg')}.fi,.gi,.hi,.si{display:inline-block;font-style:normal;font-weight:400;line-height:.8;vertical-align:middle;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.gi{font-family:'Glyphicons Regular'}.hi{font-family:'Glyphicons Halflings Regular'}.si{font-family:'Glyphicons Social Regular'}.fi{font-family:'Glyphicons Filetypes Regular'}.gi-white{color:#fff}.gi-glass:before{content:"\E001"}.gi-leaf:before{content:"\E002"}.gi-dog:before{content:"\E003"}.gi-user:before{content:"\E004"}.gi-girl:before{content:"\E005"}.gi-car:before{content:"\E006"}.gi-user_add:before{content:"\E007"}.gi-user_remove:before{content:"\E008"}.gi-film:before{content:"\E009"}.gi-magic:before{content:"\E010"}.gi-envelope:before{content:"\2709"}.gi-camera:before{content:"\E011"}.gi-heart:before{content:"\E013"}.gi-beach_umbrella:before{content:"\E014"}.gi-train:before{content:"\E015"}.gi-print:before{content:"\E016"}.gi-bin:before{content:"\E017"}.gi-music:before{content:"\E018"}.gi-note:before{content:"\E019"}.gi-heart_empty:before{content:"\E020"}.gi-home:before{content:"\E021"}.gi-snowflake:before{content:"\2744"}.gi-fire:before{content:"\E023"}.gi-magnet:before{content:"\E024"}.gi-parents:before{content:"\E025"}.gi-binoculars:before{content:"\E026"}.gi-road:before{content:"\E027"}.gi-search:before{content:"\E028"}.gi-cars:before{content:"\E029"}.gi-notes_2:before{content:"\E030"}.gi-pencil:before{content:"\270F"}.gi-bus:before{content:"\E032"}.gi-wifi_alt:before{content:"\E033"}.gi-luggage:before{content:"\E034"}.gi-old_man:before{content:"\E035"}.gi-woman:before{content:"\E036"}.gi-file:before{content:"\E037"}.gi-coins:before{content:"\E038"}.gi-airplane:before{content:"\2708"}.gi-notes:before{content:"\E040"}.gi-stats:before{content:"\E041"}.gi-charts:before{content:"\E042"}.gi-pie_chart:before{content:"\E043"}.gi-group:before{content:"\E044"}.gi-keys:before{content:"\E045"}.gi-calendar:before{content:"\E046"}.gi-router:before{content:"\E047"}.gi-camera_small:before{content:"\E048"}.gi-dislikes:before{content:"\E049"}.gi-star:before{content:"\E050"}.gi-link:before{content:"\E051"}.gi-eye_open:before{content:"\E052"}.gi-eye_close:before{content:"\E053"}.gi-alarm:before{content:"\E054"}.gi-clock:before{content:"\E055"}.gi-stopwatch:before{content:"\E056"}.gi-projector:before{content:"\E057"}.gi-history:before{content:"\E058"}.gi-truck:before{content:"\E059"}.gi-cargo:before{content:"\E060"}.gi-compass:before{content:"\E061"}.gi-keynote:before{content:"\E062"}.gi-paperclip:before{content:"\E063"}.gi-power:before{content:"\E064"}.gi-lightbulb:before{content:"\E065"}.gi-tag:before{content:"\E066"}.gi-tags:before{content:"\E067"}.gi-cleaning:before{content:"\E068"}.gi-ruller:before{content:"\E069"}.gi-gift:before{content:"\E070"}.gi-umbrella:before{content:"\2602"}.gi-book:before{content:"\E072"}.gi-bookmark:before{content:"\E073"}.gi-wifi:before{content:"\E074"}.gi-cup:before{content:"\E075"}.gi-stroller:before{content:"\E076"}.gi-headphones:before{content:"\E077"}.gi-headset:before{content:"\E078"}.gi-warning_sign:before{content:"\E079"}.gi-signal:before{content:"\E080"}.gi-retweet:before{content:"\E081"}.gi-refresh:before{content:"\E082"}.gi-roundabout:before{content:"\E083"}.gi-random:before{content:"\E084"}.gi-heat:before{content:"\E085"}.gi-repeat:before{content:"\E086"}.gi-display:before{content:"\E087"}.gi-log_book:before{content:"\E088"}.gi-address_book:before{content:"\E089"}.gi-building:before{content:"\E090"}.gi-eyedropper:before{content:"\E091"}.gi-adjust:before{content:"\E092"}.gi-tint:before{content:"\E093"}.gi-crop:before{content:"\E094"}.gi-vector_path_square:before{content:"\E095"}.gi-vector_path_circle:before{content:"\E096"}.gi-vector_path_polygon:before{content:"\E097"}.gi-vector_path_line:before{content:"\E098"}.gi-vector_path_curve:before{content:"\E099"}.gi-vector_path_all:before{content:"\E100"}.gi-font:before{content:"\E101"}.gi-italic:before{content:"\E102"}.gi-bold:before{content:"\E103"}.gi-text_underline:before{content:"\E104"}.gi-text_strike:before{content:"\E105"}.gi-text_height:before{content:"\E106"}.gi-text_width:before{content:"\E107"}.gi-text_resize:before{content:"\E108"}.gi-left_indent:before{content:"\E109"}.gi-right_indent:before{content:"\E110"}.gi-align_left:before{content:"\E111"}.gi-align_center:before{content:"\E112"}.gi-align_right:before{content:"\E113"}.gi-justify:before{content:"\E114"}.gi-list:before{content:"\E115"}.gi-text_smaller:before{content:"\E116"}.gi-text_bigger:before{content:"\E117"}.gi-embed:before{content:"\E118"}.gi-embed_close:before{content:"\E119"}.gi-table:before{content:"\E120"}.gi-message_full:before{content:"\E121"}.gi-message_empty:before{content:"\E122"}.gi-message_in:before{content:"\E123"}.gi-message_out:before{content:"\E124"}.gi-message_plus:before{content:"\E125"}.gi-message_minus:before{content:"\E126"}.gi-message_ban:before{content:"\E127"}.gi-message_flag:before{content:"\E128"}.gi-message_lock:before{content:"\E129"}.gi-message_new:before{content:"\E130"}.gi-inbox:before{content:"\E131"}.gi-inbox_plus:before{content:"\E132"}.gi-inbox_minus:before{content:"\E133"}.gi-inbox_lock:before{content:"\E134"}.gi-inbox_in:before{content:"\E135"}.gi-inbox_out:before{content:"\E136"}.gi-cogwheel:before{content:"\E137"}.gi-cogwheels:before{content:"\E138"}.gi-picture:before{content:"\E139"}.gi-adjust_alt:before{content:"\E140"}.gi-database_lock:before{content:"\E141"}.gi-database_plus:before{content:"\E142"}.gi-database_minus:before{content:"\E143"}.gi-database_ban:before{content:"\E144"}.gi-folder_open:before{content:"\E145"}.gi-folder_plus:before{content:"\E146"}.gi-folder_minus:before{content:"\E147"}.gi-folder_lock:before{content:"\E148"}.gi-folder_flag:before{content:"\E149"}.gi-folder_new:before{content:"\E150"}.gi-edit:before{content:"\E151"}.gi-new_window:before{content:"\E152"}.gi-check:before{content:"\E153"}.gi-unchecked:before{content:"\E154"}.gi-more_windows:before{content:"\E155"}.gi-show_big_thumbnails:before{content:"\E156"}.gi-show_thumbnails:before{content:"\E157"}.gi-show_thumbnails_with_lines:before{content:"\E158"}.gi-show_lines:before{content:"\E159"}.gi-playlist:before{content:"\E160"}.gi-imac:before{content:"\E161"}.gi-macbook:before{content:"\E162"}.gi-ipad:before{content:"\E163"}.gi-iphone:before{content:"\E164"}.gi-iphone_transfer:before{content:"\E165"}.gi-iphone_exchange:before{content:"\E166"}.gi-ipod:before{content:"\E167"}.gi-ipod_shuffle:before{content:"\E168"}.gi-ear_plugs:before{content:"\E169"}.gi-record:before{content:"\E170"}.gi-step_backward:before{content:"\E171"}.gi-fast_backward:before{content:"\E172"}.gi-rewind:before{content:"\E173"}.gi-play:before{content:"\E174"}.gi-pause:before{content:"\E175"}.gi-stop:before{content:"\E176"}.gi-forward:before{content:"\E177"}.gi-fast_forward:before{content:"\E178"}.gi-step_forward:before{content:"\E179"}.gi-eject:before{content:"\E180"}.gi-facetime_video:before{content:"\E181"}.gi-download_alt:before{content:"\E182"}.gi-mute:before{content:"\E183"}.gi-volume_down:before{content:"\E184"}.gi-volume_up:before{content:"\E185"}.gi-screenshot:before{content:"\E186"}.gi-move:before{content:"\E187"}.gi-more:before{content:"\E188"}.gi-brightness_reduce:before{content:"\E189"}.gi-brightness_increase:before{content:"\E190"}.gi-circle_plus:before{content:"\E191"}.gi-circle_minus:before{content:"\E192"}.gi-circle_remove:before{content:"\E193"}.gi-circle_ok:before{content:"\E194"}.gi-circle_question_mark:before{content:"\E195"}.gi-circle_info:before{content:"\E196"}.gi-circle_exclamation_mark:before{content:"\E197"}.gi-remove:before{content:"\E198"}.gi-ok:before{content:"\E199"}.gi-ban:before{content:"\E200"}.gi-download:before{content:"\E201"}.gi-upload:before{content:"\E202"}.gi-shopping_cart:before{content:"\E203"}.gi-lock:before{content:"\E204"}.gi-unlock:before{content:"\E205"}.gi-electricity:before{content:"\E206"}.gi-ok_2:before{content:"\E207"}.gi-remove_2:before{content:"\E208"}.gi-cart_out:before{content:"\E209"}.gi-cart_in:before{content:"\E210"}.gi-left_arrow:before{content:"\E211"}.gi-right_arrow:before{content:"\E212"}.gi-down_arrow:before{content:"\E213"}.gi-up_arrow:before{content:"\E214"}.gi-resize_small:before{content:"\E215"}.gi-resize_full:before{content:"\E216"}.gi-circle_arrow_left:before{content:"\E217"}.gi-circle_arrow_right:before{content:"\E218"}.gi-circle_arrow_top:before{content:"\E219"}.gi-circle_arrow_down:before{content:"\E220"}.gi-play_button:before{content:"\E221"}.gi-unshare:before{content:"\E222"}.gi-share:before{content:"\E223"}.gi-chevron-right:before{content:"\E224"}.gi-chevron-left:before{content:"\E225"}.gi-bluetooth:before{content:"\E226"}.gi-euro:before{content:"\20AC"}.gi-usd:before{content:"\E228"}.gi-gbp:before{content:"\E229"}.gi-retweet_2:before{content:"\E230"}.gi-moon:before{content:"\E231"}.gi-sun:before{content:"\2609"}.gi-cloud:before{content:"\2601"}.gi-direction:before{content:"\E234"}.gi-brush:before{content:"\E235"}.gi-pen:before{content:"\E236"}.gi-zoom_in:before{content:"\E237"}.gi-zoom_out:before{content:"\E238"}.gi-pin:before{content:"\E239"}.gi-albums:before{content:"\E240"}.gi-rotation_lock:before{content:"\E241"}.gi-flash:before{content:"\E242"}.gi-google_maps:before{content:"\E243"}.gi-anchor:before{content:"\2693"}.gi-conversation:before{content:"\E245"}.gi-chat:before{content:"\E246"}.gi-male:before{content:"\E247"}.gi-female:before{content:"\E248"}.gi-asterisk:before{content:"\002A"}.gi-divide:before{content:"\00F7"}.gi-snorkel_diving:before{content:"\E251"}.gi-scuba_diving:before{content:"\E252"}.gi-oxygen_bottle:before{content:"\E253"}.gi-fins:before{content:"\E254"}.gi-fishes:before{content:"\E255"}.gi-boat:before{content:"\E256"}.gi-delete:before{content:"\E257"}.gi-sheriffs_star:before{content:"\E258"}.gi-qrcode:before{content:"\E259"}.gi-barcode:before{content:"\E260"}.gi-pool:before{content:"\E261"}.gi-buoy:before{content:"\E262"}.gi-spade:before{content:"\E263"}.gi-bank:before{content:"\E264"}.gi-vcard:before{content:"\E265"}.gi-electrical_plug:before{content:"\E266"}.gi-flag:before{content:"\E267"}.gi-credit_card:before{content:"\E268"}.gi-keyboard-wireless:before{content:"\E269"}.gi-keyboard-wired:before{content:"\E270"}.gi-shield:before{content:"\E271"}.gi-ring:before{content:"\02DA"}.gi-cake:before{content:"\E273"}.gi-drink:before{content:"\E274"}.gi-beer:before{content:"\E275"}.gi-fast_food:before{content:"\E276"}.gi-cutlery:before{content:"\E277"}.gi-pizza:before{content:"\E278"}.gi-birthday_cake:before{content:"\E279"}.gi-tablet:before{content:"\E280"}.gi-settings:before{content:"\E281"}.gi-bullets:before{content:"\E282"}.gi-cardio:before{content:"\E283"}.gi-t-shirt:before{content:"\E284"}.gi-pants:before{content:"\E285"}.gi-sweater:before{content:"\E286"}.gi-fabric:before{content:"\E287"}.gi-leather:before{content:"\E288"}.gi-scissors:before{content:"\E289"}.gi-bomb:before{content:"\E290"}.gi-skull:before{content:"\E291"}.gi-celebration:before{content:"\E292"}.gi-tea_kettle:before{content:"\E293"}.gi-french_press:before{content:"\E294"}.gi-coffee_cup:before{content:"\E295"}.gi-pot:before{content:"\E296"}.gi-grater:before{content:"\E297"}.gi-kettle:before{content:"\E298"}.gi-hospital:before{content:"\E299"}.gi-hospital_h:before{content:"\E300"}.gi-microphone:before{content:"\E301"}.gi-webcam:before{content:"\E302"}.gi-temple_christianity_church:before{content:"\E303"}.gi-temple_islam:before{content:"\E304"}.gi-temple_hindu:before{content:"\E305"}.gi-temple_buddhist:before{content:"\E306"}.gi-bicycle:before{content:"\E307"}.gi-life_preserver:before{content:"\E308"}.gi-share_alt:before{content:"\E309"}.gi-comments:before{content:"\E310"}.gi-flower:before{content:"\2698"}.gi-baseball:before{content:"\26BE"}.gi-rugby:before{content:"\E313"}.gi-ax:before{content:"\E314"}.gi-table_tennis:before{content:"\E315"}.gi-bowling:before{content:"\E316"}.gi-tree_conifer:before{content:"\E317"}.gi-tree_deciduous:before{content:"\E318"}.gi-more_items:before{content:"\E319"}.gi-sort:before{content:"\E320"}.gi-filter:before{content:"\E321"}.gi-gamepad:before{content:"\E322"}.gi-playing_dices:before{content:"\E323"}.gi-calculator:before{content:"\E324"}.gi-tie:before{content:"\E325"}.gi-wallet:before{content:"\E326"}.gi-piano:before{content:"\E327"}.gi-sampler:before{content:"\E328"}.gi-podium:before{content:"\E329"}.gi-soccer_ball:before{content:"\E330"}.gi-blog:before{content:"\E331"}.gi-dashboard:before{content:"\E332"}.gi-certificate:before{content:"\E333"}.gi-bell:before{content:"\E334"}.gi-candle:before{content:"\E335"}.gi-pushpin:before{content:"\E336"}.gi-iphone_shake:before{content:"\E337"}.gi-pin_flag:before{content:"\E338"}.gi-turtle:before{content:"\E339"}.gi-rabbit:before{content:"\E340"}.gi-globe:before{content:"\E341"}.gi-briefcase:before{content:"\E342"}.gi-hdd:before{content:"\E343"}.gi-thumbs_up:before{content:"\E344"}.gi-thumbs_down:before{content:"\E345"}.gi-hand_right:before{content:"\E346"}.gi-hand_left:before{content:"\E347"}.gi-hand_up:before{content:"\E348"}.gi-hand_down:before{content:"\E349"}.gi-fullscreen:before{content:"\E350"}.gi-shopping_bag:before{content:"\E351"}.gi-book_open:before{content:"\E352"}.gi-nameplate:before{content:"\E353"}.gi-nameplate_alt:before{content:"\E354"}.gi-vases:before{content:"\E355"}.gi-bullhorn:before{content:"\E356"}.gi-dumbbell:before{content:"\E357"}.gi-suitcase:before{content:"\E358"}.gi-file_import:before{content:"\E359"}.gi-file_export:before{content:"\E360"}.gi-bug:before{content:"\E361"}.gi-crown:before{content:"\E362"}.gi-smoking:before{content:"\E363"}.gi-cloud-download:before{content:"\E364"}.gi-cloud-upload:before{content:"\E365"}.gi-restart:before{content:"\E366"}.gi-security_camera:before{content:"\E367"}.gi-expand:before{content:"\E368"}.gi-collapse:before{content:"\E369"}.gi-collapse_top:before{content:"\E370"}.gi-globe_af:before{content:"\E371"}.gi-global:before{content:"\E372"}.gi-spray:before{content:"\E373"}.gi-nails:before{content:"\E374"}.gi-claw_hammer:before{content:"\E375"}.gi-classic_hammer:before{content:"\E376"}.gi-hand_saw:before{content:"\E377"}.gi-riflescope:before{content:"\E378"}.gi-electrical_socket_eu:before{content:"\E379"}.gi-electrical_socket_us:before{content:"\E380"}.gi-message_forward:before{content:"\E381"}.gi-coat_hanger:before{content:"\E382"}.gi-dress:before{content:"\E383"}.gi-bathrobe:before{content:"\E384"}.gi-shirt:before{content:"\E385"}.gi-underwear:before{content:"\E386"}.gi-log_in:before{content:"\E387"}.gi-log_out:before{content:"\E388"}.gi-exit:before{content:"\E389"}.gi-new_window_alt:before{content:"\E390"}.gi-video_sd:before{content:"\E391"}.gi-video_hd:before{content:"\E392"}.gi-subtitles:before{content:"\E393"}.gi-sound_stereo:before{content:"\E394"}.gi-sound_dolby:before{content:"\E395"}.gi-sound_5_1:before{content:"\E396"}.gi-sound_6_1:before{content:"\E397"}.gi-sound_7_1:before{content:"\E398"}.gi-copyright_mark:before{content:"\E399"}.gi-registration_mark:before{content:"\E400"}.gi-radar:before{content:"\E401"}.gi-skateboard:before{content:"\E402"}.gi-golf_course:before{content:"\E403"}.gi-sorting:before{content:"\E404"}.gi-sort-by-alphabet:before{content:"\E405"}.gi-sort-by-alphabet-alt:before{content:"\E406"}.gi-sort-by-order:before{content:"\E407"}.gi-sort-by-order-alt:before{content:"\E408"}.gi-sort-by-attributes:before{content:"\E409"}.gi-sort-by-attributes-alt:before{content:"\E410"}.gi-compressed:before{content:"\E411"}.gi-package:before{content:"\E412"}.gi-cloud_plus:before{content:"\E413"}.gi-cloud_minus:before{content:"\E414"}.gi-disk_save:before{content:"\E415"}.gi-disk_open:before{content:"\E416"}.gi-disk_saved:before{content:"\E417"}.gi-disk_remove:before{content:"\E418"}.gi-disk_import:before{content:"\E419"}.gi-disk_export:before{content:"\E420"}.gi-tower:before{content:"\E421"}.gi-send:before{content:"\E422"}.gi-git_branch:before{content:"\E423"}.gi-git_create:before{content:"\E424"}.gi-git_private:before{content:"\E425"}.gi-git_delete:before{content:"\E426"}.gi-git_merge:before{content:"\E427"}.gi-git_pull_request:before{content:"\E428"}.gi-git_compare:before{content:"\E429"}.gi-git_commit:before{content:"\E430"}.gi-construction_cone:before{content:"\E431"}.gi-shoe_steps:before{content:"\E432"}.gi-plus:before{content:"\002B"}.gi-minus:before{content:"\2212"}.gi-redo:before{content:"\E435"}.gi-undo:before{content:"\E436"}.gi-golf:before{content:"\E437"}.gi-hockey:before{content:"\E438"}.gi-pipe:before{content:"\E439"}.gi-wrench:before{content:"\E440"}.gi-folder_closed:before{content:"\E441"}.gi-phone_alt:before{content:"\E442"}.gi-earphone:before{content:"\E443"}.gi-floppy_disk:before{content:"\E444"}.gi-floppy_saved:before{content:"\E445"}.gi-floppy_remove:before{content:"\E446"}.gi-floppy_save:before{content:"\E447"}.gi-floppy_open:before{content:"\E448"}.gi-translate:before{content:"\E449"}.gi-fax:before{content:"\E450"}.gi-factory:before{content:"\E451"}.gi-shop_window:before{content:"\E452"}.gi-shop:before{content:"\E453"}.gi-kiosk:before{content:"\E454"}.gi-kiosk_wheels:before{content:"\E455"}.gi-kiosk_light:before{content:"\E456"}.gi-kiosk_food:before{content:"\E457"}.gi-transfer:before{content:"\E458"}.gi-money:before{content:"\E459"}.gi-header:before{content:"\E460"}.gi-blacksmith:before{content:"\E461"}.gi-saw_blade:before{content:"\E462"}.gi-basketball:before{content:"\E463"}.gi-server:before{content:"\E464"}.gi-server_plus:before{content:"\E465"}.gi-server_minus:before{content:"\E466"}.gi-server_ban:before{content:"\E467"}.gi-server_flag:before{content:"\E468"}.gi-server_lock:before{content:"\E469"}.gi-server_new:before{content:"\E470"}.hi-glass:before{content:"\E001"}.hi-music:before{content:"\E002"}.hi-search:before{content:"\E003"}.hi-envelope:before{content:"\2709"}.hi-heart:before{content:"\E005"}.hi-star:before{content:"\E006"}.hi-star-empty:before{content:"\E007"}.hi-user:before{content:"\E008"}.hi-film:before{content:"\E009"}.hi-th-large:before{content:"\E010"}.hi-th:before{content:"\E011"}.hi-th-list:before{content:"\E012"}.hi-ok:before{content:"\E013"}.hi-remove:before{content:"\E014"}.hi-zoom-in:before{content:"\E015"}.hi-zoom-out:before{content:"\E016"}.hi-off:before{content:"\E017"}.hi-signal:before{content:"\E018"}.hi-cog:before{content:"\E019"}.hi-trash:before{content:"\E020"}.hi-home:before{content:"\E021"}.hi-file:before{content:"\E022"}.hi-time:before{content:"\E023"}.hi-road:before{content:"\E024"}.hi-download-alt:before{content:"\E025"}.hi-download:before{content:"\E026"}.hi-upload:before{content:"\E027"}.hi-inbox:before{content:"\E028"}.hi-play-circle:before{content:"\E029"}.hi-repeat:before{content:"\E030"}.hi-refresh:before{content:"\E031"}.hi-list-alt:before{content:"\E032"}.hi-lock:before{content:"\E033"}.hi-flag:before{content:"\E034"}.hi-headphones:before{content:"\E035"}.hi-volume-off:before{content:"\E036"}.hi-volume-down:before{content:"\E037"}.hi-volume-up:before{content:"\E038"}.hi-qrcode:before{content:"\E039"}.hi-barcode:before{content:"\E040"}.hi-tag:before{content:"\E041"}.hi-tags:before{content:"\E042"}.hi-book:before{content:"\E043"}.hi-bookmark:before{content:"\E044"}.hi-print:before{content:"\E045"}.hi-camera:before{content:"\E046"}.hi-font:before{content:"\E047"}.hi-bold:before{content:"\E048"}.hi-italic:before{content:"\E049"}.hi-text-height:before{content:"\E050"}.hi-text-width:before{content:"\E051"}.hi-align-left:before{content:"\E052"}.hi-align-center:before{content:"\E053"}.hi-align-right:before{content:"\E054"}.hi-align-justify:before{content:"\E055"}.hi-list:before{content:"\E056"}.hi-indent-left:before{content:"\E057"}.hi-indent-right:before{content:"\E058"}.hi-facetime-video:before{content:"\E059"}.hi-picture:before{content:"\E060"}.hi-pencil:before{content:"\270F"}.hi-map-marker:before{content:"\E062"}.hi-adjust:before{content:"\E063"}.hi-tint:before{content:"\E064"}.hi-edit:before{content:"\E065"}.hi-share:before{content:"\E066"}.hi-check:before{content:"\E067"}.hi-move:before{content:"\E068"}.hi-step-backward:before{content:"\E069"}.hi-fast-backward:before{content:"\E070"}.hi-backward:before{content:"\E071"}.hi-play:before{content:"\E072"}.hi-pause:before{content:"\E073"}.hi-stop:before{content:"\E074"}.hi-forward:before{content:"\E075"}.hi-fast-forward:before{content:"\E076"}.hi-step-forward:before{content:"\E077"}.hi-eject:before{content:"\E078"}.hi-chevron-left:before{content:"\E079"}.hi-chevron-right:before{content:"\E080"}.hi-plus-sign:before{content:"\E081"}.hi-minus-sign:before{content:"\E082"}.hi-remove-sign:before{content:"\E083"}.hi-ok-sign:before{content:"\E084"}.hi-question-sign:before{content:"\E085"}.hi-info-sign:before{content:"\E086"}.hi-screenshot:before{content:"\E087"}.hi-remove-circle:before{content:"\E088"}.hi-ok-circle:before{content:"\E089"}.hi-ban-circle:before{content:"\E090"}.hi-arrow-left:before{content:"\E091"}.hi-arrow-right:before{content:"\E092"}.hi-arrow-up:before{content:"\E093"}.hi-arrow-down:before{content:"\E094"}.hi-share-alt:before{content:"\E095"}.hi-resize-full:before{content:"\E096"}.hi-resize-small:before{content:"\E097"}.hi-plus:before{content:"\002B"}.hi-minus:before{content:"\2212"}.hi-asterisk:before{content:"\002A"}.hi-exclamation-sign:before{content:"\E101"}.hi-gift:before{content:"\E102"}.hi-leaf:before{content:"\E103"}.hi-fire:before{content:"\E104"}.hi-eye-open:before{content:"\E105"}.hi-eye-close:before{content:"\E106"}.hi-warning-sign:before{content:"\E107"}.hi-plane:before{content:"\E108"}.hi-calendar:before{content:"\E109"}.hi-random:before{content:"\E110"}.hi-comments:before{content:"\E111"}.hi-magnet:before{content:"\E112"}.hi-chevron-up:before{content:"\E113"}.hi-chevron-down:before{content:"\E114"}.hi-retweet:before{content:"\E115"}.hi-shopping-cart:before{content:"\E116"}.hi-folder-close:before{content:"\E117"}.hi-folder-open:before{content:"\E118"}.hi-resize-vertical:before{content:"\E119"}.hi-resize-horizontal:before{content:"\E120"}.hi-hdd:before{content:"\E121"}.hi-bullhorn:before{content:"\E122"}.hi-bell:before{content:"\E123"}.hi-certificate:before{content:"\E124"}.hi-thumbs-up:before{content:"\E125"}.hi-thumbs-down:before{content:"\E126"}.hi-hand-right:before{content:"\E127"}.hi-hand-left:before{content:"\E128"}.hi-hand-top:before{content:"\E129"}.hi-hand-down:before{content:"\E130"}.hi-circle-arrow-right:before{content:"\E131"}.hi-circle-arrow-left:before{content:"\E132"}.hi-circle-arrow-top:before{content:"\E133"}.hi-circle-arrow-down:before{content:"\E134"}.hi-globe:before{content:"\E135"}.hi-wrench:before{content:"\E136"}.hi-tasks:before{content:"\E137"}.hi-filter:before{content:"\E138"}.hi-briefcase:before{content:"\E139"}.hi-fullscreen:before{content:"\E140"}.hi-dashboard:before{content:"\E141"}.hi-paperclip:before{content:"\E142"}.hi-heart-empty:before{content:"\E143"}.hi-link:before{content:"\E144"}.hi-phone:before{content:"\E145"}.hi-pushpin:before{content:"\E146"}.hi-euro:before{content:"\20AC"}.hi-usd:before{content:"\E148"}.hi-gbp:before{content:"\E149"}.hi-sort:before{content:"\E150"}.hi-sort-by-alphabet:before{content:"\E151"}.hi-sort-by-alphabet-alt:before{content:"\E152"}.hi-sort-by-order:before{content:"\E153"}.hi-sort-by-order-alt:before{content:"\E154"}.hi-sort-by-attributes:before{content:"\E155"}.hi-sort-by-attributes-alt:before{content:"\E156"}.hi-unchecked:before{content:"\E157"}.hi-expand:before{content:"\E158"}.hi-collapse:before{content:"\E159"}.hi-collapse-top:before{content:"\E160"}.hi-log_in:before{content:"\E161"}.hi-flash:before{content:"\E162"}.hi-log_out:before{content:"\E163"}.hi-new_window:before{content:"\E164"}.hi-record:before{content:"\E165"}.hi-save:before{content:"\E166"}.hi-open:before{content:"\E167"}.hi-saved:before{content:"\E168"}.hi-import:before{content:"\E169"}.hi-export:before{content:"\E170"}.hi-send:before{content:"\E171"}.hi-floppy_disk:before{content:"\E172"}.hi-floppy_saved:before{content:"\E173"}.hi-floppy_remove:before{content:"\E174"}.hi-floppy_save:before{content:"\E175"}.hi-floppy_open:before{content:"\E176"}.hi-credit_card:before{content:"\E177"}.hi-transfer:before{content:"\E178"}.hi-cutlery:before{content:"\E179"}.hi-header:before{content:"\E180"}.hi-compressed:before{content:"\E181"}.hi-earphone:before{content:"\E182"}.hi-phone_alt:before{content:"\E183"}.hi-tower:before{content:"\E184"}.hi-stats:before{content:"\E185"}.hi-sd_video:before{content:"\E186"}.hi-hd_video:before{content:"\E187"}.hi-subtitles:before{content:"\E188"}.hi-sound_stereo:before{content:"\E189"}.hi-sound_dolby:before{content:"\E190"}.hi-sound_5_1:before{content:"\E191"}.hi-sound_6_1:before{content:"\E192"}.hi-sound_7_1:before{content:"\E193"}.hi-copyright_mark:before{content:"\E194"}.hi-registration_mark:before{content:"\E195"}.hi-cloud:before{content:"\2601"}.hi-cloud_download:before{content:"\E197"}.hi-cloud_upload:before{content:"\E198"}.hi-tree_conifer:before{content:"\E199"}.hi-tree_deciduous:before{content:"\E200"}.si-pinterest:before{content:"\E001"}.si-dropbox:before{content:"\E002"}.si-google_plus:before{content:"\E003"}.si-jolicloud:before{content:"\E004"}.si-yahoo:before{content:"\E005"}.si-blogger:before{content:"\E006"}.si-picasa:before{content:"\E007"}.si-amazon:before{content:"\E008"}.si-tumblr:before{content:"\E009"}.si-wordpress:before{content:"\E010"}.si-instapaper:before{content:"\E011"}.si-evernote:before{content:"\E012"}.si-xing:before{content:"\E013"}.si-zootool:before{content:"\E014"}.si-dribbble:before{content:"\E015"}.si-deviantart:before{content:"\E016"}.si-read_it_later:before{content:"\E017"}.si-linked_in:before{content:"\E018"}.si-forrst:before{content:"\E019"}.si-pinboard:before{content:"\E020"}.si-behance:before{content:"\E021"}.si-github:before{content:"\E022"}.si-youtube:before{content:"\E023"}.si-skitch:before{content:"\E024"}.si-foursquare:before{content:"\E025"}.si-quora:before{content:"\E026"}.si-badoo:before{content:"\E027"}.si-spotify:before{content:"\E028"}.si-stumbleupon:before{content:"\E029"}.si-readability:before{content:"\E030"}.si-facebook:before{content:"\E031"}.si-twitter:before{content:"\E032"}.si-instagram:before{content:"\E033"}.si-posterous_spaces:before{content:"\E034"}.si-vimeo:before{content:"\E035"}.si-flickr:before{content:"\E036"}.si-last_fm:before{content:"\E037"}.si-rss:before{content:"\E038"}.si-skype:before{content:"\E039"}.si-e-mail:before{content:"\E040"}.si-vine:before{content:"\E041"}.si-myspace:before{content:"\E042"}.si-goodreads:before{content:"\E043"}.si-apple:before{content:"\F8FF"}.si-windows:before{content:"\E045"}.si-yelp:before{content:"\E046"}.si-playstation:before{content:"\E047"}.si-xbox:before{content:"\E048"}.si-android:before{content:"\E049"}.si-ios:before{content:"\E050"}.fi-txt:before{content:"\E001"}.fi-doc:before{content:"\E002"}.fi-rtf:before{content:"\E003"}.fi-log:before{content:"\E004"}.fi-tex:before{content:"\E005"}.fi-msg:before{content:"\E006"}.fi-text:before{content:"\E007"}.fi-wpd:before{content:"\E008"}.fi-wps:before{content:"\E009"}.fi-docx:before{content:"\E010"}.fi-page:before{content:"\E011"}.fi-csv:before{content:"\E012"}.fi-dat:before{content:"\E013"}.fi-tar:before{content:"\E014"}.fi-xml:before{content:"\E015"}.fi-vcf:before{content:"\E016"}.fi-pps:before{content:"\E017"}.fi-key:before{content:"\E018"}.fi-ppt:before{content:"\E019"}.fi-pptx:before{content:"\E020"}.fi-sdf:before{content:"\E021"}.fi-gbr:before{content:"\E022"}.fi-ged:before{content:"\E023"}.fi-mp3:before{content:"\E024"}.fi-m4a:before{content:"\E025"}.fi-waw:before{content:"\E026"}.fi-wma:before{content:"\E027"}.fi-mpa:before{content:"\E028"}.fi-iff:before{content:"\E029"}.fi-aif:before{content:"\E030"}.fi-ra:before{content:"\E031"}.fi-mid:before{content:"\E032"}.fi-m3v:before{content:"\E033"}.fi-e_3gp:before{content:"\E034"}.fi-shf:before{content:"\E035"}.fi-avi:before{content:"\E036"}.fi-asx:before{content:"\E037"}.fi-mp4:before{content:"\E038"}.fi-e_3g2:before{content:"\E039"}.fi-mpg:before{content:"\E040"}.fi-asf:before{content:"\E041"}.fi-vob:before{content:"\E042"}.fi-wmv:before{content:"\E043"}.fi-mov:before{content:"\E044"}.fi-srt:before{content:"\E045"}.fi-m4v:before{content:"\E046"}.fi-flv:before{content:"\E047"}.fi-rm:before{content:"\E048"}.fi-png:before{content:"\E049"}.fi-psd:before{content:"\E050"}.fi-psp:before{content:"\E051"}.fi-jpg:before{content:"\E052"}.fi-tif:before{content:"\E053"}.fi-tiff:before{content:"\E054"}.fi-gif:before{content:"\E055"}.fi-bmp:before{content:"\E056"}.fi-tga:before{content:"\E057"}.fi-thm:before{content:"\E058"}.fi-yuv:before{content:"\E059"}.fi-dds:before{content:"\E060"}.fi-ai:before{content:"\E061"}.fi-eps:before{content:"\E062"}.fi-ps:before{content:"\E063"}.fi-svg:before{content:"\E064"}.fi-pdf:before{content:"\E065"}.fi-pct:before{content:"\E066"}.fi-indd:before{content:"\E067"}.fi-xlr:before{content:"\E068"}.fi-xls:before{content:"\E069"}.fi-xlsx:before{content:"\E070"}.fi-db:before{content:"\E071"}.fi-dbf:before{content:"\E072"}.fi-mdb:before{content:"\E073"}.fi-pdb:before{content:"\E074"}.fi-sql:before{content:"\E075"}.fi-aacd:before{content:"\E076"}.fi-app:before{content:"\E077"}.fi-exe:before{content:"\E078"}.fi-com:before{content:"\E079"}.fi-bat:before{content:"\E080"}.fi-apk:before{content:"\E081"}.fi-jar:before{content:"\E082"}.fi-hsf:before{content:"\E083"}.fi-pif:before{content:"\E084"}.fi-vb:before{content:"\E085"}.fi-cgi:before{content:"\E086"}.fi-css:before{content:"\E087"}.fi-js:before{content:"\E088"}.fi-php:before{content:"\E089"}.fi-xhtml:before{content:"\E090"}.fi-htm:before{content:"\E091"}.fi-html:before{content:"\E092"}.fi-asp:before{content:"\E093"}.fi-cer:before{content:"\E094"}.fi-jsp:before{content:"\E095"}.fi-cfm:before{content:"\E096"}.fi-aspx:before{content:"\E097"}.fi-rss:before{content:"\E098"}.fi-csr:before{content:"\E099"}.fi-less:before{content:"\003C"}.fi-otf:before{content:"\E101"}.fi-ttf:before{content:"\E102"}.fi-font:before{content:"\E103"}.fi-fnt:before{content:"\E104"}.fi-eot:before{content:"\E105"}.fi-woff:before{content:"\E106"}.fi-zip:before{content:"\E107"}.fi-zipx:before{content:"\E108"}.fi-rar:before{content:"\E109"}.fi-targ:before{content:"\E110"}.fi-sitx:before{content:"\E111"}.fi-deb:before{content:"\E112"}.fi-e_7z:before{content:"\E113"}.fi-pkg:before{content:"\E114"}.fi-rpm:before{content:"\E115"}.fi-cbr:before{content:"\E116"}.fi-gz:before{content:"\E117"}.fi-dmg:before{content:"\E118"}.fi-cue:before{content:"\E119"}.fi-bin:before{content:"\E120"}.fi-iso:before{content:"\E121"}.fi-hdf:before{content:"\E122"}.fi-vcd:before{content:"\E123"}.fi-bak:before{content:"\E124"}.fi-tmp:before{content:"\E125"}.fi-ics:before{content:"\E126"}.fi-msi:before{content:"\E127"}.fi-cfg:before{content:"\E128"}.fi-ini:before{content:"\E129"}.fi-prf:before{content:"\E130"}

/*
==============================================
(#03cas) CSS3 ANIMATION CHEAT SHEET

Made by Justin Aguilar

www.justinaguilar.com/animations/

Questions, comments, concerns, love letters:
<EMAIL>
==============================================
*/

/*
==============================================
slideDown
==============================================
*/

.animation-slideDown {
    animation-name: slideDown;
    -webkit-animation-name: slideDown;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes slideDown {
    0% {
        transform: translateY(-100%);
    }
    50%{
        transform: translateY(8%);
    }
    65%{
        transform: translateY(-4%);
    }
    80%{
        transform: translateY(4%);
    }
    95%{
        transform: translateY(-2%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes slideDown {
    0% {
        -webkit-transform: translateY(-100%);
    }
    50%{
        -webkit-transform: translateY(8%);
    }
    65%{
        -webkit-transform: translateY(-4%);
    }
    80%{
        -webkit-transform: translateY(4%);
    }
    95%{
        -webkit-transform: translateY(-2%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
slideUp
==============================================
*/

.animation-slideUp {
    animation-name: slideUp;
    -webkit-animation-name: slideUp;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes slideUp {
    0% {
        transform: translateY(100%);
    }
    50%{
        transform: translateY(-8%);
    }
    65%{
        transform: translateY(4%);
    }
    80%{
        transform: translateY(-4%);
    }
    95%{
        transform: translateY(2%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes slideUp {
    0% {
        -webkit-transform: translateY(100%);
    }
    50%{
        -webkit-transform: translateY(-8%);
    }
    65%{
        -webkit-transform: translateY(4%);
    }
    80%{
        -webkit-transform: translateY(-4%);
    }
    95%{
        -webkit-transform: translateY(2%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
slideLeft
==============================================
*/

.animation-slideLeft {
    animation-name: slideLeft;
    -webkit-animation-name: slideLeft;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes slideLeft {
    0% {
        transform: translateX(150%);
    }
    50%{
        ransform: translateX(-8%);
    }
    65%{
        transform: translateX(4%);
    }
    80%{
        transform: translateX(-4%);
    }
    95%{
        transform: translateX(2%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideLeft {
    0% {
        -webkit-transform: translateX(150%);
    }
    50%{
        -webkit-transform: translateX(-8%);
    }
    65%{
        -webkit-transform: translateX(4%);
    }
    80%{
        -webkit-transform: translateX(-4%);
    }
    95%{
        -webkit-transform: translateX(2%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
slideRight
==============================================
*/

.animation-slideRight {
    animation-name: slideRight;
    -webkit-animation-name: slideRight;
    animation-duration: 1s;
    -webkit-animation-duration: 1s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes slideRight {
    0% {
        transform: translateX(-150%);
    }
    50%{
        transform: translateX(8%);
    }
    65%{
        transform: translateX(-4%);
    }
    80%{
        transform: translateX(4%);
    }
    95%{
        transform: translateX(-2%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes slideRight {
    0% {
        -webkit-transform: translateX(-150%);
    }
    50%{
        -webkit-transform: translateX(8%);
    }
    65%{
        -webkit-transform: translateX(-4%);
    }
    80%{
        -webkit-transform: translateX(4%);
    }
    95%{
        -webkit-transform: translateX(-2%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
slideExpandUp
==============================================
*/

.animation-slideExpandUp {
    animation-name: slideExpandUp;
    -webkit-animation-name: slideExpandUp;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease -out;
    visibility: visible !important;
}

@keyframes slideExpandUp {
    0% {
        transform: translateY(100%) scaleX(0.5);
    }
    30%{
        transform: translateY(-8%) scaleX(0.5);
    }
    40%{
        transform: translateY(2%) scaleX(0.5);
    }
    50%{
        transform: translateY(0%) scaleX(1.1);
    }
    60%{
        transform: translateY(0%) scaleX(0.9);
    }
    70% {
        transform: translateY(0%) scaleX(1.05);
    }
    80%{
        transform: translateY(0%) scaleX(0.95);
    }
    90% {
        transform: translateY(0%) scaleX(1.02);
    }
    100%{
        transform: translateY(0%) scaleX(1);
    }
}

@-webkit-keyframes slideExpandUp {
    0% {
        -webkit-transform: translateY(100%) scaleX(0.5);
    }
    30%{
        -webkit-transform: translateY(-8%) scaleX(0.5);
    }
    40%{
        -webkit-transform: translateY(2%) scaleX(0.5);
    }
    50%{
        -webkit-transform: translateY(0%) scaleX(1.1);
    }
    60%{
        -webkit-transform: translateY(0%) scaleX(0.9);
    }
    70% {
        -webkit-transform: translateY(0%) scaleX(1.05);
    }
    80%{
        -webkit-transform: translateY(0%) scaleX(0.95);
    }
    90% {
        -webkit-transform: translateY(0%) scaleX(1.02);
    }
    100%{
        -webkit-transform: translateY(0%) scaleX(1);
    }
}

/*
==============================================
expandUp
==============================================
*/

.animation-expandUp {
    animation-name: expandUp;
    -webkit-animation-name: expandUp;
    animation-duration: 0.7s;
    -webkit-animation-duration: 0.7s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    visibility: visible !important;
}

@keyframes expandUp {
    0% {
        transform: translateY(100%) scale(0.6) scaleY(0.5);
    }
    60%{
        transform: translateY(-7%) scaleY(1.12);
    }
    75%{
        transform: translateY(3%);
    }
    100% {
        transform: translateY(0%) scale(1) scaleY(1);
    }
}

@-webkit-keyframes expandUp {
    0% {
        -webkit-transform: translateY(100%) scale(0.6) scaleY(0.5);
    }
    60%{
        -webkit-transform: translateY(-7%) scaleY(1.12);
    }
    75%{
        -webkit-transform: translateY(3%);
    }
    100% {
        -webkit-transform: translateY(0%) scale(1) scaleY(1);
    }
}

/*
==============================================
fadeIn
==============================================
*/

.animation-fadeIn {
    animation-name: fadeIn;
    -webkit-animation-name: fadeIn;
    animation-duration: 1.0s;
    -webkit-animation-duration: 1.0s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    visibility: visible !important;
}

@keyframes fadeIn {
    0% {
        transform: scale(0);
        opacity: 0.0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeIn {
    0% {
        -webkit-transform: scale(0);
        opacity: 0.0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
expandOpen
==============================================
*/

.animation-expandOpen {
    animation-name: expandOpen;
    -webkit-animation-name: expandOpen;
    animation-duration: 1.2s;
    -webkit-animation-duration: 1.2s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes expandOpen {
    0% {
        transform: scale(1.8);
    }
    50% {
        transform: scale(0.95);
    }
    80% {
        transform: scale(1.05);
    }
    90% {
        transform: scale(0.98);
    }
    100% {
        transform: scale(1);
    }
}

@-webkit-keyframes expandOpen {
    0% {
        -webkit-transform: scale(1.8);
    }
    50% {
        -webkit-transform: scale(0.95);
    }
    80% {
        -webkit-transform: scale(1.05);
    }
    90% {
        -webkit-transform: scale(0.98);
    }
    100% {
        -webkit-transform: scale(1);
    }
}

/*
==============================================
bigEntrance
==============================================
*/

.animation-bigEntrance {
    animation-name: bigEntrance;
    -webkit-animation-name: bigEntrance;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes bigEntrance {
    0% {
        transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
        opacity: 0.2;
    }
    30% {
        transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
        opacity: 1;
    }
    45% {
        transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    60% {
        transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    75% {
        transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    90% {
        transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    100% {
        transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
}

@-webkit-keyframes bigEntrance {
    0% {
        -webkit-transform: scale(0.3) rotate(6deg) translateX(-30%) translateY(30%);
        opacity: 0.2;
    }
    30% {
        -webkit-transform: scale(1.03) rotate(-2deg) translateX(2%) translateY(-2%);
        opacity: 1;
    }
    45% {
        -webkit-transform: scale(0.98) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    60% {
        -webkit-transform: scale(1.01) rotate(-1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    75% {
        -webkit-transform: scale(0.99) rotate(1deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    90% {
        -webkit-transform: scale(1.01) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(1) rotate(0deg) translateX(0%) translateY(0%);
        opacity: 1;
    }
}

/*
==============================================
hatch
==============================================
*/

.animation-hatch {
    animation-name: hatch;
    -webkit-animation-name: hatch;
    animation-duration: 2s;
    -webkit-animation-duration: 2s;
    animation-timing-function: ease-in-out;
    -webkit-animation-timing-function: ease-in-out;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
    visibility: visible !important;
}

@keyframes hatch {
    0% {
        transform: rotate(0deg) scaleY(0.6);
    }
    20% {
        transform: rotate(-2deg) scaleY(1.05);
    }
    35% {
        transform: rotate(2deg) scaleY(1);
    }
    50% {
        transform: rotate(-2deg);
    }
    65% {
        transform: rotate(1deg);
    }
    80% {
        transform: rotate(-1deg);
    }
    100% {
        transform: rotate(0deg);
    }
}

@-webkit-keyframes hatch {
    0% {
        -webkit-transform: rotate(0deg) scaleY(0.6);
    }
    20% {
        -webkit-transform: rotate(-2deg) scaleY(1.05);
    }
    35% {
        -webkit-transform: rotate(2deg) scaleY(1);
    }
    50% {
        -webkit-transform: rotate(-2deg);
    }
    65% {
        -webkit-transform: rotate(1deg);
    }
    80% {
        -webkit-transform: rotate(-1deg);
    }
    100% {
        -webkit-transform: rotate(0deg);
    }
}

/*
==============================================
bounce
==============================================
*/

.animation-bounce {
    animation-name: bounce;
    -webkit-animation-name: bounce;
    animation-duration: 1.6s;
    -webkit-animation-duration: 1.6s;
    animation-timing-function: ease;
    -webkit-animation-timing-function: ease;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
}

@keyframes bounce {
    0% {
        transform: translateY(0%) scaleY(0.6);
    }
    60%{
        transform: translateY(-100%) scaleY(1.1);
    }
    70%{
        transform: translateY(0%) scaleY(0.95) scaleX(1.05);
    }
    80%{
        transform: translateY(0%) scaleY(1.05) scaleX(1);
    }
    90%{
        transform: translateY(0%) scaleY(0.95) scaleX(1);
    }
    100%{
        transform: translateY(0%) scaleY(1) scaleX(1);
    }
}

@-webkit-keyframes bounce {
    0% {
        -webkit-transform: translateY(0%) scaleY(0.6);
    }
    60%{
        -webkit-transform: translateY(-100%) scaleY(1.1);
    }
    70%{
        -webkit-transform: translateY(0%) scaleY(0.95) scaleX(1.05);
    }
    80%{
        -webkit-transform: translateY(0%) scaleY(1.05) scaleX(1);
    }
    90%{
        -webkit-transform: translateY(0%) scaleY(0.95) scaleX(1);
    }
    100%{
        -webkit-transform: translateY(0%) scaleY(1) scaleX(1);
    }
}

/*
==============================================
pulse
==============================================
*/

.animation-pulse {
    animation-name: pulse;
    -webkit-animation-name: pulse;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes pulse {
    0% {
        transform: scale(0.9);
        opacity: 0.7;
    }
    50% {
        transform: scale(1);
        opacity: 1;
    }
    100% {
        transform: scale(0.9);
        opacity: 0.7;
    }
}

@-webkit-keyframes pulse {
    0% {
        -webkit-transform: scale(0.95);
        opacity: 0.7;
    }
    50% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
    100% {
        -webkit-transform: scale(0.95);
        opacity: 0.7;
    }
}

/*
==============================================
floating
==============================================
*/

.animation-floating {
    animation-name: floating;
    -webkit-animation-name: floating;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes floating {
    0% {
        transform: translateY(0%);
    }
    50% {
        transform: translateY(8%);
    }
    100% {
        transform: translateY(0%);
    }
}

@-webkit-keyframes floating {
    0% {
        -webkit-transform: translateY(0%);
    }
    50% {
        -webkit-transform: translateY(8%);
    }
    100% {
        -webkit-transform: translateY(0%);
    }
}

/*
==============================================
tossing
==============================================
*/

.animation-tossing {
    animation-name: tossing;
    -webkit-animation-name: tossing;
    animation-duration: 2.5s;
    -webkit-animation-duration: 2.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes tossing {
    0% {
        transform: rotate(-4deg);
    }
    50% {
        transform: rotate(4deg);
    }
    100% {
        transform: rotate(-4deg);
    }
}

@-webkit-keyframes tossing {
    0% {
        -webkit-transform: rotate(-4deg);
    }
    50% {
        -webkit-transform: rotate(4deg);
    }
    100% {
        -webkit-transform: rotate(-4deg);
    }
}

/*
==============================================
pullUp
==============================================
*/

.animation-pullUp {
    animation-name: pullUp;
    -webkit-animation-name: pullUp;
    animation-duration: 1.1s;
    -webkit-animation-duration: 1.1s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 50% 100%;
    -ms-transform-origin: 50% 100%;
    -webkit-transform-origin: 50% 100%;
}

@keyframes pullUp {
    0% {
        transform: scaleY(0.1);
    }
    40% {
        transform: scaleY(1.02);
    }
    60% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(1);
    }
}

@-webkit-keyframes pullUp {
    0% {
        -webkit-transform: scaleY(0.1);
    }
    40% {
        -webkit-transform: scaleY(1.02);
    }
    60% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(1);
    }
}

/*
==============================================
pullDown
==============================================
*/

.animation-pullDown {
    animation-name: pullDown;
    -webkit-animation-name: pullDown;
    animation-duration: 1.1s;
    -webkit-animation-duration: 1.1s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 50% 0%;
    -ms-transform-origin: 50% 0%;
    -webkit-transform-origin: 50% 0%;
}

@keyframes pullDown {
    0% {
        transform: scaleY(0.1);
    }
    40% {
        transform: scaleY(1.02);
    }
    60% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(0.98);
    }
    80% {
        transform: scaleY(1.01);
    }
    100% {
        transform: scaleY(1);
    }
}

@-webkit-keyframes pullDown {
    0% {
        -webkit-transform: scaleY(0.1);
    }
    40% {
        -webkit-transform: scaleY(1.02);
    }
    60% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(0.98);
    }
    80% {
        -webkit-transform: scaleY(1.01);
    }
    100% {
        -webkit-transform: scaleY(1);
    }
}

/*
==============================================
stretchLeft
==============================================
*/

.animation-stretchLeft {
    animation-name: stretchLeft;
    -webkit-animation-name: stretchLeft;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 100% 0%;
    -ms-transform-origin: 100% 0%;
    -webkit-transform-origin: 100% 0%;
}

@keyframes stretchLeft {
    0% {
        transform: scaleX(0.3);
    }
    40% {
        transform: scaleX(1.02);
    }
    60% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(1);
    }
}

@-webkit-keyframes stretchLeft {
    0% {
        -webkit-transform: scaleX(0.3);
    }
    40% {
        -webkit-transform: scaleX(1.02);
    }
    60% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(1);
    }
}

/*
==============================================
stretchRight
==============================================
*/

.animation-stretchRight {
    animation-name: stretchRight;
    -webkit-animation-name: stretchRight;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    transform-origin: 0% 0%;
    -ms-transform-origin: 0% 0%;
    -webkit-transform-origin: 0% 0%;
}

@keyframes stretchRight {
    0% {
        transform: scaleX(0.3);
    }
    40% {
        transform: scaleX(1.02);
    }
    60% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(0.98);
    }
    80% {
        transform: scaleX(1.01);
    }
    100% {
        transform: scaleX(1);
    }
}

@-webkit-keyframes stretchRight {
    0% {
        -webkit-transform: scaleX(0.3);
    }
    40% {
        -webkit-transform: scaleX(1.02);
    }
    60% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(0.98);
    }
    80% {
        -webkit-transform: scaleX(1.01);
    }
    100% {
        -webkit-transform: scaleX(1);
    }
}

/* Extend with more animations */

/*
==============================================
pulseSlow
==============================================
*/

.animation-pulseSlow {
    animation-name: pulseSlow;
    -webkit-animation-name: pulseSlow;
    animation-duration: 30s;
    -webkit-animation-duration: 30s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
    animation-timing-function: linear;
    -webkit-animation-timing-function: linear;
}

@keyframes pulseSlow {
    0% {
        transform: scale(1.1);
    }
    50% {
        transform: scale(1);
    }
    100% {
        transform: scale(1.1);
    }
}

@-webkit-keyframes pulseSlow {
    0% {
        -webkit-transform: scale(1.1);
    }
    50% {
        -webkit-transform: scale(1);
    }
    100% {
        -webkit-transform: scale(1.1);
    }
}

/*
==============================================
floatingHor
==============================================
*/

.animation-floatingHor {
    animation-name: floatingHor;
    -webkit-animation-name: floatingHor;
    animation-duration: 1.5s;
    -webkit-animation-duration: 1.5s;
    animation-iteration-count: infinite;
    -webkit-animation-iteration-count: infinite;
}

@keyframes floatingHor {
    0% {
        transform: translateX(0%);
    }
    50% {
        transform: translateX(8%);
    }
    100% {
        transform: translateX(0%);
    }
}

@-webkit-keyframes floatingHor {
    0% {
        -webkit-transform: translateX(0%);
    }
    50% {
        -webkit-transform: translateX(8%);
    }
    100% {
        -webkit-transform: translateX(0%);
    }
}

/*
==============================================
fadeInQuick
==============================================
*/

.animation-fadeInQuick {
    animation-name: fadeInQuick;
    -webkit-animation-name: fadeInQuick;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInQuick {
    0% {
        transform: scale(0.9);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInQuick {
    0% {
        -webkit-transform: scale(0.9);
        opacity: 0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
fadeInQuickInv
==============================================
*/

.animation-fadeInQuickInv {
    animation-name: fadeInQuickInv;
    -webkit-animation-name: fadeInQuickInv;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInQuickInv {
    0% {
        transform: scale(1.1);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInQuickInv {
    0% {
        -webkit-transform: scale(1.1);
        opacity: 0;
    }
    100% {
        -webkit-transform: scale(1);
        opacity: 1;
    }
}

/*
==============================================
fadeIn360
==============================================
*/

.animation-fadeIn360 {
    animation-name: fadeIn360;
    -webkit-animation-name: fadeIn360;
    animation-duration: 0.5s;
    -webkit-animation-duration: 0.5s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeIn360 {
    0% {
        transform: rotate(0deg) scale(1.3);
        opacity: 0;
    }
    100% {
        transform: rotate(360deg) scale(1);
        opacity: 1;
    }
}

@-webkit-keyframes fadeIn360 {
    0% {
        -webkit-transform: rotate(0deg) scale(1.3);
        opacity: 0;
    }
    100% {
        -webkit-transform: rotate(360deg) scale(1);
        opacity: 1;
    }
}

/*
==============================================
FadeInRight
==============================================
*/

.animation-fadeInRight {
    animation-name: fadeInRight;
    -webkit-animation-name: fadeInRight;
    animation-duration: 0.75s;
    -webkit-animation-duration: 0.75s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInRight {
    0% {
        transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0%);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInRight {
    0% {
        -webkit-transform: translateX(-100%);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0%);
        opacity: 1;
    }
}

/*
==============================================
FadeInLeft
==============================================
*/

.animation-fadeInLeft {
    animation-name: fadeInLeft;
    -webkit-animation-name: fadeInLeft;
    animation-duration: 0.75s;
    -webkit-animation-duration: 0.75s;
    animation-timing-function: ease-out;
    -webkit-animation-timing-function: ease-out;
    visibility: visible !important;
}

@keyframes fadeInLeft {
    0% {
        transform: translateX(+100%);
        opacity: 0;
    }
    100% {
        transform: translateX(0%);
        opacity: 1;
    }
}

@-webkit-keyframes fadeInLeft {
    0% {
        -webkit-transform: translateX(+100%);
        opacity: 0;
    }
    100% {
        -webkit-transform: translateX(0%);
        opacity: 1;
    }
}

/*
=================================================================
(#04mps) Magnific Popup CSS
=================================================================
*/

.mfp-bg {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1042;
    overflow: hidden;
    position: fixed;
    background: #0b0b0b;
    opacity: 0.8;
    filter: alpha(opacity=80);
}
.mfp-wrap {
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1043;
    position: fixed;
    outline: none !important;
    -webkit-backface-visibility: hidden;
}
.mfp-container {
    text-align: center;
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    padding: 0 8px;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}
.mfp-container:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
}
.mfp-align-top .mfp-container:before {
    display: none;
}
.mfp-content {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    margin: 0 auto;
    text-align: left;
    z-index: 1045;
}
.mfp-inline-holder .mfp-content,
.mfp-ajax-holder .mfp-content {
    width: 100%;
    cursor: auto;
}
.mfp-ajax-cur {
    cursor: progress;
}
.mfp-zoom-out-cur,
.mfp-zoom-out-cur .mfp-image-holder .mfp-close {
    cursor: -moz-zoom-out;
    cursor: -webkit-zoom-out;
    cursor: zoom-out;
}
.mfp-zoom {
    cursor: pointer;
    cursor: -webkit-zoom-in;
    cursor: -moz-zoom-in;
    cursor: zoom-in;
}
.mfp-auto-cursor .mfp-content {
    cursor: auto;
}
.mfp-close,
.mfp-arrow,
.mfp-preloader,
.mfp-counter {
    -webkit-user-select: none;
    -moz-user-select: none;
    user-select: none;
}
.mfp-loading.mfp-figure {
    display: none;
}
.mfp-hide {
    display: none !important;
}
.mfp-preloader {
    color: #CCC;
    position: absolute;
    top: 50%;
    width: auto;
    text-align: center;
    margin-top: -0.8em;
    left: 8px;
    right: 8px;
    z-index: 1044;
}
.mfp-preloader a {
    color: #CCC;
}
.mfp-preloader a:hover {
    color: #FFF;
}
.mfp-s-ready .mfp-preloader {
    display: none;
}
.mfp-s-error .mfp-content {
    display: none;
}
button.mfp-close,
button.mfp-arrow {
    overflow: visible;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
    display: block;
    outline: none;
    padding: 0;
    z-index: 1046;
    -webkit-box-shadow: none;
    box-shadow: none;
}
button::-moz-focus-inner {
    padding: 0;
    border: 0;
}
.mfp-close {
    width: 44px;
    height: 44px;
    line-height: 44px;
    position: absolute;
    right: 0;
    top: 0;
    text-decoration: none;
    text-align: center;
    opacity: 0.65;
    filter: alpha(opacity=65);
    padding: 0 0 18px 10px;
    color: #FFF;
    font-style: normal;
    font-size: 28px;
    font-family: Arial, Baskerville, monospace;
}
.mfp-close:hover,
.mfp-close:focus {
    opacity: 1;
    filter: alpha(opacity=100);
}
.mfp-close:active {
    top: 1px;
}
.mfp-close-btn-in .mfp-close {
    color: #333;
}
.mfp-image-holder .mfp-close,
.mfp-iframe-holder .mfp-close {
    color: #FFF;
    right: -6px;
    text-align: right;
    padding-right: 6px;
    width: 100%;
}
.mfp-counter {
    position: absolute;
    top: 0;
    right: 0;
    color: #CCC;
    font-size: 12px;
    line-height: 18px;
    white-space: nowrap;
}
.mfp-arrow {
    position: absolute;
    opacity: 0.65;
    filter: alpha(opacity=65);
    margin: 0;
    top: 50%;
    margin-top: -55px;
    padding: 0;
    width: 90px;
    height: 110px;
    -webkit-tap-highlight-color: transparent;
}
.mfp-arrow:active {
    margin-top: -54px;
}
.mfp-arrow:hover,
.mfp-arrow:focus {
    opacity: 1;
    filter: alpha(opacity=100);
}
.mfp-arrow:before,
.mfp-arrow:after,
.mfp-arrow .mfp-b,
.mfp-arrow .mfp-a {
    content: '';
    display: block;
    width: 0;
    height: 0;
    position: absolute;
    left: 0;
    top: 0;
    margin-top: 35px;
    margin-left: 35px;
    border: medium inset transparent;
}
.mfp-arrow:after,
.mfp-arrow .mfp-a {
    border-top-width: 13px;
    border-bottom-width: 13px;
    top: 8px;
}
.mfp-arrow:before,
.mfp-arrow .mfp-b {
    border-top-width: 21px;
    border-bottom-width: 21px;
    opacity: 0.7;
}
.mfp-arrow-left {
    left: 0;
}
.mfp-arrow-left:after,
.mfp-arrow-left .mfp-a {
    border-right: 17px solid #FFF;
    margin-left: 31px;
}
.mfp-arrow-left:before,
.mfp-arrow-left .mfp-b {
    margin-left: 25px;
    border-right: 27px solid #3F3F3F;
}
.mfp-arrow-right {
    right: 0;
}
.mfp-arrow-right:after,
.mfp-arrow-right .mfp-a {
    border-left: 17px solid #FFF;
    margin-left: 39px;
}
.mfp-arrow-right:before,
.mfp-arrow-right .mfp-b {
    border-left: 27px solid #3F3F3F;
}
.mfp-iframe-holder {
    padding-top: 40px;
    padding-bottom: 40px;
}
.mfp-iframe-holder .mfp-content {
    line-height: 0;
    width: 100%;
    max-width: 900px;
}
.mfp-iframe-holder .mfp-close {
    top: -40px;
}
.mfp-iframe-scaler {
    width: 100%;
    height: 0;
    overflow: hidden;
    padding-top: 56.25%;
}
.mfp-iframe-scaler iframe {
    position: absolute;
    display: block;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #000;
}
/* Main image in popup */

img.mfp-img {
    width: auto;
    max-width: 100%;
    height: auto;
    display: block;
    line-height: 0;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
    padding: 40px 0 40px;
    margin: 0 auto;
}
/* The shadow behind the image */

.mfp-figure {
    line-height: 0;
}
.mfp-figure:after {
    content: '';
    position: absolute;
    left: 0;
    top: 40px;
    bottom: 40px;
    display: block;
    right: 0;
    width: auto;
    height: auto;
    z-index: -1;
    box-shadow: 0 0 8px rgba(0, 0, 0, 0.6);
    background: #444;
}
.mfp-figure small {
    color: #BDBDBD;
    display: block;
    font-size: 12px;
    line-height: 14px;
}
.mfp-figure figure {
    margin: 0;
}
.mfp-bottom-bar {
    margin-top: -36px;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    cursor: auto;
}
.mfp-title {
    text-align: left;
    line-height: 18px;
    color: #F3F3F3;
    word-wrap: break-word;
    padding-right: 36px;
}
.mfp-image-holder .mfp-content {
    max-width: 100%;
}
.mfp-gallery .mfp-image-holder .mfp-figure {
    cursor: pointer;
}
@media screen and (max-width: 800px) and (orientation: landscape),
screen and (max-height: 300px) {
    /**
       * Remove all paddings around the image on small screen
       */

    .mfp-img-mobile .mfp-image-holder {
        padding-left: 0;
        padding-right: 0;
    }
    .mfp-img-mobile img.mfp-img {
        padding: 0;
    }
    .mfp-img-mobile .mfp-figure:after {
        top: 0;
        bottom: 0;
    }
    .mfp-img-mobile .mfp-figure small {
        display: inline;
        margin-left: 5px;
    }
    .mfp-img-mobile .mfp-bottom-bar {
        background: rgba(0, 0, 0, 0.6);
        bottom: 0;
        margin: 0;
        top: auto;
        padding: 3px 5px;
        position: fixed;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
    }
    .mfp-img-mobile .mfp-bottom-bar:empty {
        padding: 0;
    }
    .mfp-img-mobile .mfp-counter {
        right: 5px;
        top: 3px;
    }
    .mfp-img-mobile .mfp-close {
        top: 0;
        right: 0;
        width: 35px;
        height: 35px;
        line-height: 35px;
        background: rgba(0, 0, 0, 0.6);
        position: fixed;
        text-align: center;
        padding: 0;
    }
}
@media all and (max-width: 900px) {
    .mfp-arrow {
        -webkit-transform: scale(0.75);
        transform: scale(0.75);
    }
    .mfp-arrow-left {
        -webkit-transform-origin: 0;
        transform-origin: 0;
    }
    .mfp-arrow-right {
        -webkit-transform-origin: 100%;
        transform-origin: 100%;
    }
    .mfp-container {
        padding-left: 6px;
        padding-right: 6px;
    }
}
.mfp-ie7 .mfp-img {
    padding: 0;
}
.mfp-ie7 .mfp-bottom-bar {
    width: 600px;
    left: 50%;
    margin-left: -300px;
    margin-top: 5px;
    padding-bottom: 5px;
}
.mfp-ie7 .mfp-container {
    padding: 0;
}
.mfp-ie7 .mfp-content {
    padding-top: 44px;
}
.mfp-ie7 .mfp-close {
    top: 0;
    right: 0;
    padding-top: 0;
}
.fc-content {
    font-size: 1.2em !important;
}
