<?php

namespace App\Migrate\AppSupport;

use App\Services\Traits\SupplierFullGrantService;
use App\Services\Traits\EpWebService;
use App\Migrate\MigrateUtils;
use App\EpSupportActionLog;
use DB;
use Log;
use Carbon\Carbon;

class SmSupplierUpdateChangedDateToToday
{
    use SupplierFullGrantService;
    use EpWebService;
    

    /**
     * Get list of supplier active but not have sap_vendor_code. The program will set changed_date as currentdatetime to retrigger scheduler send file APIVE to 1GFMAS 
     */
    public function fixSupplierChangedDateToToday()
    {
        MigrateUtils::logDump(__METHOD__ . ' Starting ' . __FUNCTION__ . ' ...');
        $dtStartTime = Carbon::now();

        $connection = DB::connection('oracle_nextgen_fullgrant');

        $query = "SELECT
                      s.SUPPLIER_ID,
                      s.COMPANY_NAME,
                      s.REG_NO,
                      s.CHANGED_DATE,
                      a.SUPPLIER_TYPE,
                      a.STATUS_ID,
                      a.APPL_TYPE,
                      s.EP_NO,
                      v.SAP_VENDOR_CODE
                  FROM
                      SM_SUPPLIER s
                  JOIN SM_APPL a ON s.LATEST_APPL_ID = a.APPL_ID 
		            JOIN SM_SUPPLIER_BANK b ON a.APPL_ID =  b.APPL_ID
		            JOIN PM_FINANCIAL_ORG o ON b.financial_org_id = o.financial_org_id
                  LEFT JOIN SM_SAP_VENDOR_CODE v ON s.EP_NO = v.EP_NO
                  WHERE
                      s.RECORD_STATUS = 1
                  AND a.SUPPLIER_TYPE IN ('B', 'K','G','P')  
                  -- AND s.CHANGED_DATE < TRUNC(SYSDATE)
                  AND s.CHANGED_DATE < SYSDATE - INTERVAL '18' HOUR
                  AND v.SAP_VENDOR_CODE IS NULL 
                  AND s.EP_NO IS NOT NULL
                  AND o.record_status = 1
                  ORDER BY
                      s.CHANGED_DATE ASC
                  FETCH FIRST 50 ROWS ONLY";

        $suppliers = $connection->select($query);

        MigrateUtils::logDump(__METHOD__ . ' Total suppliers found: ' . count($suppliers));

        $logs = collect([]);
        $parameters = collect([]);

        foreach ($suppliers as $supplier) {
            $currentDate = Carbon::now();
            $fieldsToUpdate = ['changed_date' => $currentDate];

            //$logQuery = $this->updateSMSupplier($supplier->supplier_id, $fieldsToUpdate);

            $res = $this->wsRetriggerApiveSupplier($supplier->supplier_id);

            $logs->push($res);

            $parameters->push([
                "remarks" => "Updated supplier changed date to today",
                "patching" => "supplier_changed_date",
                "ep_no" => $supplier->ep_no,
                "supplier_id" => $supplier->supplier_id,
                "changed_date" => $currentDate->toDateTimeString(),
            ]);
        }

        EpSupportActionLog::saveActionLog(
            "WebServiceSupplierChangedDateScheduler",
            "Update Table Ep SM_SUPPLIER",
            $logs,
            $parameters,
            "Completed",
            'SchedulerAdmin'
        );

        MigrateUtils::logDump(__METHOD__ . ' >> '.json_encode($logs)); 
        MigrateUtils::logDump(__METHOD__ . ' Updated CHANGED_DATE for ' . count($suppliers) . ' suppliers.');

        MigrateUtils::logDump(__METHOD__ . ' Completed ' . __FUNCTION__ . ' --- Taken Time: ' . MigrateUtils::getTakenTime($dtStartTime)['TakenTime']);
    }


    /**
     * Get list of supplier branch active but not have sap_vendor_code. The program will set changed_date as currentdatetime to retrigger scheduler send file APIVE to 1GFMAS 
     */
    public function resendApiveForSupplierBranchSapVendorCodeNull()
    {
        MigrateUtils::logDump(__METHOD__ . ' Starting  ...');
        $dtStartTime = Carbon::now();

        //$connection = DB::connection('oracle_nextgen_fullgrant');
        $connection = DB::connection('oracle_nextgen_rpt');
        

        $query = "SELECT DISTINCT  supplier_id,company_name,ep_no,supplier_type,changed_date,exp_date FROM (
                    SELECT s.supplier_id,s.company_name, s.ep_no, a.supplier_type,s.changed_date,
                    b.branch_name, b.branch_code , c.sap_vendor_code , m.exp_date
                    FROM sm_supplier s 
                    INNER JOIN sm_appl a ON  s.latest_appl_id = a.appl_id 
                    INNER JOIN sm_supplier_branch b ON b.appl_id =a.appl_id
                    LEFT JOIN SM_SAP_VENDOR_CODE  c ON s.ep_no = c.ep_no AND b.branch_code =  c.branch_code 
                    LEFT JOIN SM_MOF_CERT m ON b.appl_id = m.appl_id AND m.record_status = 1 AND m.file_name IS NOT null
                    WHERE
                    a.supplier_type NOT IN ('B','J')
                    AND s.ep_no IS NOT NULL 
                    AND s.record_status = 1
                    AND trunc(s.changed_Date) >= to_date('2021-01-01','YYYY-MM-DD')
                    AND trunc(s.changed_Date) <= to_date('2025-04-10','YYYY-MM-DD')
                    ) tmp WHERE tmp.sap_vendor_code IS NULL  
                    AND to_char(exp_date,'YYYY') >= 2023
                    ORDER BY tmp.changed_date
                  FETCH FIRST 30 ROWS ONLY";

        $suppliers = $connection->select($query);

        MigrateUtils::logDump(__METHOD__ . ' Total suppliers found: ' . count($suppliers));

        $logs = collect([]);
        $parameters = collect([]);

        foreach ($suppliers as $supplier) {
            $currentDate = Carbon::now();
            $fieldsToUpdate = ['changed_date' => $currentDate];

            //$logQuery = $this->updateSMSupplier($supplier->supplier_id, $fieldsToUpdate);

            $res = $this->wsRetriggerApiveSupplier($supplier->supplier_id);

            $logs->push($res);

            $parameters->push([
                "remarks" => "Updated supplier changed date to today",
                "patching" => "supplier_changed_date",
                "ep_no" => $supplier->ep_no,
                "supplier_id" => $supplier->supplier_id,
                "changed_date" => $currentDate->toDateTimeString(),
            ]);
        }

        EpSupportActionLog::saveActionLog(
            "WebServiceSupplierChangedDateScheduler",
            "Update Table Ep SM_SUPPLIER",
            $logs,
            $parameters,
            "Completed",
            'SchedulerAdmin'
        );

        MigrateUtils::logDump(__METHOD__ . ' >> '.json_encode($logs)); 
        MigrateUtils::logDump(__METHOD__ . ' Updated CHANGED_DATE for ' . count($suppliers) . ' suppliers.');

        MigrateUtils::logDump(__METHOD__ . ' Completed ' . __FUNCTION__ . ' --- Taken Time: ' . MigrateUtils::getTakenTime($dtStartTime)['TakenTime']);
    }
}
