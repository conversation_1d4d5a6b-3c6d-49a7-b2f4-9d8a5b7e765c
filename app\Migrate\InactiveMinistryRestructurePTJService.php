<?php

namespace App\Migrate;

use Carbon\Carbon;
use Log;
use DB;
use App\Migrate\MigrateUtils;
use Guzzle;

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

class InactiveMinistryRestructurePTJService {

    public static function execute($orgProfileId = null, $processStatus = 0) {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        MigrateUtils::logDump('orgProfileId : '.$orgProfileId);
        MigrateUtils::logDump('processStatus : '.$processStatus);
        
        $dtStartTime = Carbon::now();

        $inactivePTJs = collect(self::getListInactivePTJs($orgProfileId, $processStatus));
        MigrateUtils::logDump('Check PTJ on list : ' . $inactivePTJs->count());

        $counter = 0;
        foreach ($inactivePTJs as $ptj) {
//        foreach ($inactivePTJs->take(10) as $ptj) {
            $org_profile_id = $ptj->org_profile_id;
            $counter++;
            $ptjUserCheck = self::getUserActiveAndUserGroup($org_profile_id);

            if ($ptjUserCheck) {

                MigrateUtils::logDump($counter . ') OrgCode: ' . $ptjUserCheck->org_code .
                        ' ,OrgProfileId: ' . $ptjUserCheck->org_profile_id .
                        ' ,Name: ' . $ptjUserCheck->org_name .
                        ' ,TotalUser: ' . $ptjUserCheck->total_user .
                        ' ,TotalUserGroup: ' . $ptjUserCheck->total_user_group .
                        ' ,TotalUserGroupUsers: ' . $ptjUserCheck->total_user_group_users
                );

                $isPendingDocument = self::validatePTJ($org_profile_id);

                if ($isPendingDocument > 0) {
                    MigrateUtils::logDump($ptjUserCheck->org_code . ' > ######### Has pending document! Skip to Inactive. Total: ' . $isPendingDocument);
                } else {
                    MigrateUtils::logDump('Start do action.. ');

                    // check ptj user group status
                    if ($ptjUserCheck->total_user_group > 0) {
                        MigrateUtils::logDump('Do action Update User Group');
                        self::updateUserGroup($org_profile_id);
                        self::updateUserGroupUser($org_profile_id);
                    }
                    // check ptj user with roles record status
                    if ($ptjUserCheck->total_user > 0) {
                        MigrateUtils::logDump('Do action Update User Role');
                        self::updateUserRole($org_profile_id);

                        MigrateUtils::logDump(' sleep 5 second');
                        sleep(5);
                        // call api middleware to remove roles
                        MigrateUtils::logDump('Do action sync roles to LDAP and liferay');
                        self::syncUserRoles($org_profile_id);

                        MigrateUtils::logDump('Recheck sync roles!');
                        $isSuccess = self::checkSyncRoleStatus($org_profile_id);

                        // 1 mean as success 
                        if ($isSuccess == 1) {
                            // check ptj user status
                            MigrateUtils::logDump('Do action to update user org. ');
                            self::updateUserOrg($org_profile_id);

                            //checking all user is clear inactive.
                            $ptjUserCheckAfter = self::getUserActiveAndUserGroup($org_profile_id);
                            if ($ptjUserCheckAfter->total_user == 0 && $ptjUserCheckAfter->total_user_group == 0 && $ptjUserCheckAfter->total_user_group_users == 0) {
                                MigrateUtils::logDump('Done.. completed inactive list users .. ');
                            } else {
                                MigrateUtils::logDump('Still has users active! ');
                            }
                        } else {
                            MigrateUtils::logDump('Sync roles failed for ptj - '.$org_profile_id);
                            continue;
                        }
                    }

                    MigrateUtils::logDump('Do action Update PTJ record');
                    // user group = 0, user with roles record status = 0
                    self::updateInactivePTJ($org_profile_id);
                    DB::connection('mysql_ep_support')->table('ep_log_inactive_ptj')->where('org_validity_id', $ptjUserCheck->org_validity_id)
                            ->update(array(
                                'process_status' => 1,
                                'process_date' => Carbon::now(),
                                'process_remark' => 'Successful Sync Roles for PTJ - '.$org_profile_id
                    ));
                    MigrateUtils::logDump('############ Done.. completed inactive ptj .. ##############');
                }
            } else {
                MigrateUtils::logDump('PTJ already inactive. Not Found!!');
            }
        }

        Log::info(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', ['Time' => MigrateUtils::getTakenTime($dtStartTime)]);
    }

    private static function updateUserGroupUser($org_profile_id) {
        $userGroups = DB::connection('oracle_nextgen_fullgrant')->table('pm_user_group')->where('org_profile_id', $org_profile_id)->get();
        
        if(count($userGroups) > 0){
            foreach($userGroups as $userGroup){
                DB::connection('oracle_nextgen_fullgrant')->table('pm_user_group_user')
                        ->where('user_group_id', $userGroup->user_group_id)
                        ->update([
                            'record_status' => 0,
                            'changed_date' => Carbon::now()
                ]);
            }
        }
    }

    private static function updateUserGroup($org_profile_id) {
        DB::connection('oracle_nextgen_fullgrant')->table('pm_user_group')
                        ->where('org_profile_id', $org_profile_id)
                        ->update([
                            'record_status' => 0,
                            'changed_date' => Carbon::now()
                ]);
    }

    private static function updateInactivePTJ($org_profile_id) {
        DB::connection('oracle_nextgen_fullgrant')->table('pm_org_validity')
                ->where('org_profile_id', $org_profile_id)
                ->where('record_status', 1)
                ->update([
                    'record_status' => 0,
                    'exp_date' => Carbon::now(),
                    'changed_date' => Carbon::now()
        ]);
    }

    private static function updateUserRole($org_profile_id) {
        $userOrgs = DB::connection('oracle_nextgen_fullgrant')->table('pm_user_org')->where('org_profile_id', $org_profile_id)->get();

        if (count($userOrgs) > 0) {
            foreach ($userOrgs as $userOrg) {
                DB::connection('oracle_nextgen_fullgrant')->table('pm_user_role')
                        ->where('user_org_id', $userOrg->user_org_id)
                        ->update([
                            'record_status' => 0,
                            'changed_date' => Carbon::now()
                ]);
            }
        }
    }

    private static function updateUserOrg($org_profile_id) {
        DB::connection('oracle_nextgen_fullgrant')->table('pm_user_org')
                    ->where('org_profile_id', $org_profile_id)
                    ->update([
                        'record_status' => 0,
                        'changed_date' => Carbon::now()
            ]);
    }

    private static function checkSyncRoleStatus($org_profile_id) {
        $user_orgs = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER_ORG')->where('org_profile_id', $org_profile_id)
                        ->where('record_status', 1)->get();
        if (count($user_orgs) > 0) {
            $errors = array();
            foreach ($user_orgs as $user_org) {
                $user = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER')->where('user_id', $user_org->user_id)->first();

                if ($user == null) {
                    MigrateUtils::logDump('No user found for org profile id: ' . $org_profile_id);
                }

                try {
                    //Sample Date FORMAT must be:  2019-01-28
                    $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");

                    $url = $urlMiddleware . "/ep/ejb-pm/userlogin-status?loginId=" . $user->login_id;

                    $response = Guzzle::get($url);
                    $resultResp = json_decode($response->getBody(), true);

//                    MigrateUtils::logDump($resultResp);
                    $result = $resultResp['result'];

                    if (isset($result['PmRole'])) {
                        if (isset($result['PmRole']) && $result['PmRole'] === "[]" && $result['OimRole'] === "[ALL USERS, PORTAL_USER]" && $result['LiferayRole'] === "[]") {
                            MigrateUtils::logDump('Successfully synced - ' . $user->login_id);
                        } else {
                            MigrateUtils::logDump('Failed synced - ' . $user->login_id);

                            array_push($errors, array(
                                'login_id' => $user->login_id,
                                'result' => json_encode($result)
                            ));
                        }
                    }
                } catch (\GuzzleHttp\Exception\ClientException $ex) {
                    return array(
                        "status" => "Error",
                        "result" => 'Failed to connect' + $ex->getMessage());
                }
            }

            $proc_sts = 1;
            $remarks = 'Successful Sync Roles';

            if ($errors && count($errors) > 0) {
                $proc_sts = 2;
                $remarks = 'Failed Sync Roles for PTJ - '.$org_profile_id;
                
                DB::connection('mysql_ep_support')->table('ep_log_inactive_ptj')->where('org_profile_id', $org_profile_id)
                        ->update(array(
                            'process_status' => $proc_sts,
                            'process_date' => Carbon::now(),
                            'process_remark' => $remarks,
                            'data' => json_encode($errors)
                ));
            }

            

            return $proc_sts;
        }
    }

    protected static function syncUserRoles($org_profile_id) {
        MigrateUtils::logDump(__FUNCTION__ . ' start sync roles by each user');
        $user_orgs = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER_ORG')->where('org_profile_id', $org_profile_id)
                        ->where('record_status', 1)->get();

        if (count($user_orgs) > 0) {
            foreach ($user_orgs as $user_org) {
                $user = DB::connection('oracle_nextgen_fullgrant')->table('PM_USER')->where('user_id', $user_org->user_id)->first();

                if ($user == null) {
                    MigrateUtils::logDump('No user found for org profile id: ' . $org_profile_id);
                }

                try {
                    MigrateUtils::logDump('  > sync ' . $user->login_id);
                    //Sample Date FORMAT must be:  2019-01-28
                    $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL", "http://192.168.62.132:8080/ep-support-middleware");
                    $url = $urlMiddleware . "/ep/ejb-pm/sso_liferay_role/sync";


                    $isSupplierUser = false;
                    if ($user->org_type_id == 15) {
                        $isSupplierUser = true;
                    }
                    $options = [
                        'json' => [
                            'userId' => $user->user_id,
                            'loginId' => $user->login_id,
                            'isSupplierUser' => $isSupplierUser,
                        ]
                    ];
                    $response = Guzzle::post($url, $options);

                    $resultResp = json_decode($response->getBody(), true);

                    MigrateUtils::logDump($resultResp);
                    MigrateUtils::logDump('....');
                    sleep(2);
                } catch (\GuzzleHttp\Exception\ConnectException $ex) {
                    MigrateUtils::logDump($ex->getMessage());
                    MigrateUtils::logDump($ex->getTraceAsString());
                }
            }
        }
    }

    private static function validatePTJ($org_profile_id) {
        // 1: check each ptj in module (DP,QT,CT,FL)
        $totalCheck = self::isTransactionExist($org_profile_id);
        MigrateUtils::logDump(__FUNCTION__ . ' >> ' . $totalCheck);
        return $totalCheck;
    }

    private static function isTransactionExist($org_profile_id) {

        $isPtjDP = self::checkPTJModuleDP($org_profile_id);
        if ($isPtjDP > 0) {
            MigrateUtils::logDump('pending checkPTJModuleDP!! > ' . $isPtjDP);
            return $isPtjDP;
        }

        $isPtjQT = self::checkPTJModuleQT($org_profile_id);
        if ($isPtjQT > 0) {
            MigrateUtils::logDump('pending checkPTJModuleQT!! > ' . $isPtjQT);
            return $isPtjQT;
        }

        $isPtjFL = self::checkTotalPTJModuleFL($org_profile_id);
        if ($isPtjFL > 0) {
            MigrateUtils::logDump('pending checkPTJModuleFL!!  > ' . $isPtjFL);
            return $isPtjFL;
        }

        return 0;
    }

    private static function checkTotalPTJModuleFL($org_profile_id) {
        MigrateUtils::logDump(' ' . __FUNCTION__ . ' ... ');
        $year = Carbon::now()->year;
        $query = "SELECT   s.status_id, d.status_name, fo.doc_type, COUNT (*)
                    FROM fl_workflow_status s,
                         pm_status_desc d,
                         fl_fulfilment_order fo,
                         fl_fulfilment_request fr
                   WHERE s.doc_type IN ('CO', 'PO')
                     AND d.status_id = s.status_id
                     AND fo.fulfilment_order_id = s.doc_id
                     AND s.is_current = 1
                     AND d.language_code = 'ms'
                     AND fr.financial_year >= ?
                     AND fr.fulfilment_req_id = fo.fulfilment_req_id 
                     AND s.status_id NOT IN (41530, 41535, 41900, 41035, 41400, 41430, 41030, 41440, 41940, 41310, 41410, 41431)
                     AND ((fr.issued_org_profile_id = ? ) OR (fr.charge_org_profile_id = ? ))
                GROUP BY s.status_id, d.status_name, fo.doc_type
                ORDER BY fo.doc_type, s.status_id";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($year, $org_profile_id, $org_profile_id));
        return count($result);
    }

    private static function checkPTJModuleQT($org_profile_id) {
        MigrateUtils::logDump(' ' . __FUNCTION__ . ' ... ');
        $query = "
                    /* Formatted on 2020/06/16 16:27 (Formatter Plus v4.8.8) */
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN
                                (60002, 60003, 60004, 60005, 60006, 60007, 60008, 60009, 60010,
                                 60016, 60024, 60025, 60026, 60027, 60028, 60029, 60030, 60031,
                                 60035, 60036, 60037, 60038, 60039, 60040)
                         AND ws.doc_type = 'QT'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ? 
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (60150, 60153, 60154)
                         AND ws.doc_type = 'BPK'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (62050, 62051, 62053, 62054)
                         AND ws.doc_type = 'EC'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (62100, 62101, 62103, 62104)
                         AND ws.doc_type = 'TEC'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (62150, 62151, 62152, 62153)
                         AND ws.doc_type = 'FEC'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (62000, 62001, 62002, 62003)
                         AND ws.doc_type = 'OC'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'C', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (63550, 63551, 63552, 63553)
                         AND ws.doc_type = 'SE'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'P', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (60011, 60012, 60013, 60023, 60033, 60034)
                         AND ws.doc_type = 'QT'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id  = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'P', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (63350, 63355)
                         AND ws.doc_type = 'BD'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'P', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (60200, 60201, 60203, 60204)
                         AND ws.doc_type = 'CMT'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id  = ?
                    UNION
                    SELECT   ws.workflow_status_id, qt.qt_id, ws.doc_type, ws.doc_id, qt.qt_no,
                             ws.status_id, 'P', st.status_name, ws.created_date,
                             qt.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_qt qt, pm_status_desc st
                       WHERE qt.qt_id = ws.doc_id
                         AND ws.status_id IN (62201, 62202, 62209, 62210, 62211, 62213)
                         AND ws.doc_type = 'SF'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND qt.org_profile_id  = ?
                    UNION
                    SELECT   ws.workflow_status_id, ll.doc_id qt_id, ws.doc_type, ws.doc_id,
                             li.loi_no, ws.status_id, 'P', st.status_name, ws.created_date,
                             ll.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_loi li, sc_loi_loa ll, pm_status_desc st
                       WHERE li.loi_id = ws.doc_id
                         AND ll.loi_loa_id = li.loi_loa_id
                         AND ll.doc_type = 'QT'
                         AND ws.status_id IN (62400, 62401, 62402, 62403, 62407)
                         AND ws.doc_type = 'LI'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND ll.org_profile_id = ?
                    UNION
                    SELECT   ws.workflow_status_id, ll.doc_id qt_id, ws.doc_type, ws.doc_id,
                             la.loa_no, ws.status_id, 'P', st.status_name, ws.created_date,
                             ll.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_loa la, sc_loi_loa ll, pm_status_desc st
                       WHERE la.loa_id = ws.doc_id
                         AND ll.loi_loa_id = la.loi_loa_id
                         AND ll.doc_type = 'QT'
                         AND ws.status_id IN (62500, 62501, 62502, 62504, 62505, 62508)
                         AND ws.doc_type = 'LA'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND ll.org_profile_id  = ?
                    UNION
                    SELECT   ws.workflow_status_id, ll.doc_id qt_id, ws.doc_type, ws.doc_id,
                             la.loa_no, ws.status_id, 'P_NG', st.status_name, ws.created_date,
                             ll.org_profile_id, '202001'
                        FROM sc_workflow_status ws, sc_loa la, sc_loi_loa ll, pm_status_desc st
                       WHERE la.loa_id = ws.doc_id
                         AND ll.loi_loa_id = la.loi_loa_id
                         AND ll.doc_type = 'NG'
                         AND ws.status_id IN (62500, 62501, 62502, 62504, 62505, 62508)
                         AND ws.doc_type = 'LA'
                         AND ws.is_current = 1
                         AND ws.record_status = 1
                         AND st.status_id = ws.status_id
                         AND st.language_code = 'ms'
                         AND ll.org_profile_id  = ?
                    ORDER BY 1";

        $result = DB::connection('oracle_nextgen_rpt')->select($query,
                array($org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id,
                    $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id, $org_profile_id));

        return count($result);
    }

    private static function checkPTJModuleDP($org_profile_id) {
        MigrateUtils::logDump(' ' . __FUNCTION__ . ' ... ');
        $isScSq = self::checkTransactionSCSQ($org_profile_id);
        if ($isScSq > 0) {
            MigrateUtils::logDump('pending checkTransactionSCSQ > ' . $isScSq);
            return $isScSq;
        }

        $isScPd = self::checkTransactionSCPD($org_profile_id);
        if ($isScPd > 0) {
            MigrateUtils::logDump('pending checkTransactionSCPD > ' . $isScPd);
            return $isScPd;
        }

        $isScPi = self::checkTransactionSCPI($org_profile_id);
        if ($isScPi > 0) {
            MigrateUtils::logDump('pending checkTransactionSCPI > ' . $isScPi);
            return $isScPi;
        }

        $isScRn = self::checkTransactionSCRN($org_profile_id);
        if ($isScRn > 0) {
            MigrateUtils::logDump('pending checkTransactionSCRN > ' . $isScRn);
            return $isScRn;
        }

        $isScLa = self::checkTransactionSCLA($org_profile_id);
        if ($isScLa > 0) {
            MigrateUtils::logDump('pending checkTransactionSCLA > ' . $isScLa);
            return $isScLa;
        }

        $isScLaPm = self::checkTransactionSCLA_procManual($org_profile_id);
        if ($isScLaPm > 0) {
            MigrateUtils::logDump('pending checkTransactionSCLA_procManual > ' . $isScLaPm);
            return $isScLaPm;
        }

        $isScRn1Off = self::checkTransactionSCRN_oneOff($org_profile_id);
        if ($isScRn1Off > 0) {
            MigrateUtils::logDump('pending checkTransactionSCRN_oneOff > ' . $isScRn1Off);
            return $isScRn1Off;
        }

        //if ($isScSq || $isScPd || $isScPi || $isScRn || $isScLa || $isScLaPm || $isScRn1Off) { return true;}

        return 0;
    }

    private static function checkTransactionSCRN_oneOff($org_profile_id) {
        $query = "SELECT rn.request_note_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_request_note rn
                  WHERE  s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'RN'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (60708)
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = rn.request_note_id
                     AND EXISTS (
                            SELECT request_note_id
                              FROM sc_request_note_dtl rndtl
                             WHERE rndtl.request_note_id = rn.request_note_id
                               AND rndtl.fulfilment_type_id = 128)
                     AND rn.org_profile_id = ? ";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCLA_procManual($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT loa.loa_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_loa loa,
                         sc_loi_loa loiloa,
                         sc_purchase_request pr,
                         sc_request_note rn
                   WHERE s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'LA'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (62504, 62505)
                     AND loiloa.doc_type NOT IN ('NG', 'QT')
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = loa.loa_id
                     AND loa.loi_loa_id = loiloa.loi_loa_id
                     AND loiloa.doc_id = pr.purchase_request_id
                     AND pr.request_note_id = rn.request_note_id
                     AND loiloa.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCLA($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT loa.loa_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_loa loa,
                         sc_loi_loa loiloa,
                         sc_purchase_request pr,
                         sc_request_note rn
                   WHERE s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'LA'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (62500, 62501, 62502, 62508)
                     AND loiloa.doc_type NOT IN ('NG', 'QT')
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = loa.loa_id
                     AND loa.loi_loa_id = loiloa.loi_loa_id
                     AND loiloa.doc_id = pr.purchase_request_id
                     AND pr.request_note_id = rn.request_note_id
                     AND loiloa.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCRN($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT rn.request_note_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_request_note rn
                  WHERE  s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'RN'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (60700, 60701, 60702, 60707)
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = rn.request_note_id
                     AND rn.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCPI($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT pi.purchase_inquiry_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_request_note rn,
                         sc_request_note_dtl rndtl,
                         sc_request_supplier_item rsi,
                         sc_purchase_inquiry pi
                  WHERE  s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'PI'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (60750, 60751, 60752)
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = rn.request_note_id
                     AND rn.request_note_id = rndtl.request_note_id
                     AND rndtl.request_supplier_item_id = rsi.request_supplier_item_id
                     AND rsi.purchase_inquiry_id = pi.purchase_inquiry_id
                     AND rn.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCPD($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT pid.pid_id AS doc_id
                    FROM pm_status s, pm_status_desc sdesc, sc_workflow_status wf, sc_pid pid
                  WHERE  s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'PD'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (60800, 60801, 60802, 60803, 60806)
                     AND pid.record_status = 1
                     AND s.status_id = wf.status_id
                     AND pid.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    private static function checkTransactionSCSQ($org_profile_id) {
        MigrateUtils::logDump('  ... ' . __FUNCTION__);
        $query = "SELECT q.quote_id AS doc_id
                    FROM pm_status s,
                         pm_status_desc sdesc,
                         sc_workflow_status wf,
                         sc_quote q,
                         sc_request_note rn
                  WHERE  s.status_id = sdesc.status_id
                     AND s.record_status = 1
                     AND sdesc.record_status = 1
                     AND sdesc.language_code = 'en'
                     AND s.module_code = 'SC'
                     AND s.doc_type = 'SQ'
                     AND wf.record_status = 1
                     AND wf.is_current = 1
                     AND wf.status_id IN (60850, 60851, 60852)
                     AND rn.record_status = 1
                     AND s.status_id = wf.status_id
                     AND wf.doc_id = q.quote_id
                     AND q.quote_id = rn.quote_id
                     AND rn.org_profile_id = ?";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));

        return count($result);
    }

    public static function getUserActiveAndUserGroup($org_profile_id) {
        $query = "SELECT DISTINCT d.org_validity_id,d.org_code, d.org_name, d.record_status, d.exp_date,d.changed_date,
                d.org_profile_id,
                (SELECT COUNT (*)
                   FROM pm_user u, pm_user_org uo
                  WHERE u.user_id = uo.user_id
                    AND uo.org_profile_id = d.org_profile_id
                    AND uo.record_status = 1) AS total_user,
                (SELECT COUNT (*)
                   FROM pm_user_group
                  WHERE org_profile_id = d.org_profile_id
                    AND record_status = 1) AS total_user_group,
                (SELECT COUNT (*)
                   FROM pm_user_group ug,
                        pm_user_group_user ugu
                  WHERE ug.user_group_id = ugu.user_group_id
                    AND ug.org_profile_id = d.org_profile_id
                    AND ugu.record_status = 1) AS total_user_group_users
           FROM pm_org_validity d
          WHERE d.record_status = 1
            AND d.org_profile_id = ? ";

        $result = DB::connection('oracle_nextgen_rpt')->select($query, array($org_profile_id));
        if (count($result) > 0) {
            return $result[0];
        }
        return null;
    }

    private static function getListInactivePTJs($orgProfileId, $processStatus) {
        $ptjs = null;
        if($orgProfileId != null){
            $ptjs = DB::connection('mysql_ep_support')->table('ep_log_inactive_ptj')
                    ->where('org_profile_id', $orgProfileId)->get();
        } else {
            $ptjs = DB::connection('mysql_ep_support')->table('ep_log_inactive_ptj')
                    ->where('process_status', $processStatus)->get();
        }
        
        return $ptjs;
    }
    
    public static function insertInactivePTJToEpLogInactivePTJ(){
        foreach(self::getInactivePTJs() as $orgProfileId){
            $inactivePtj = self::getUserActiveAndUserGroup($orgProfileId);
            if($inactivePtj){
                DB::connection('mysql_ep_support')->table('ep_log_inactive_ptj')
                    ->insert([
                        'org_validity_id' => $inactivePtj->org_validity_id,
                        'org_profile_id' => $inactivePtj->org_profile_id,
                        'org_code' => $inactivePtj->org_code,
                        'org_name' => $inactivePtj->org_name,
                        'exp_date' => $inactivePtj->exp_date,
                        'org_changed_date' => $inactivePtj->changed_date,
                        'total_user' => $inactivePtj->total_user,
                        'total_user_group' => $inactivePtj->total_user_group,
                        'total_user_group_users' => $inactivePtj->total_user_group_users,
                        'process_status' => 0   
                    ]);
            }
            
        }
    }
    
    /*
     * List of Inactive MR PTJ for year 2020
     */
    private static function getInactivePTJs(){
        $ptjs = array(981952, 981953, 981954, 981955, 981956, 981957, 981958,
                    981959, 981960, 981961, 981962, 981963, 981964, 981965,
                    981966, 981967, 981968, 981969, 981970, 981971, 981972,
                    981973, 981974, 981975, 981976, 982922, 982925, 983035,
                    983050, 983051, 983052, 983053, 983054, 983055, 983056,
                    983057, 983058, 983059, 983060, 983061, 983062, 983063,
                    983064, 983065, 983066, 983067, 983068, 983069, 983070,
                    983071, 983072, 983073, 983074, 983075, 983076, 983077,
                    983078, 983079, 983080, 983081, 983082, 983083, 983084,
                    983565, 983566, 983567, 996098, 998413, 999605, 999747,
                    999848, 999856, 1000345, 1000671, 1004055, 1004057,
                    1004082, 1004085, 1004086, 1004096, 1032025, 1032026,
                    1032027, 1032028, 1032029, 1032038, 1032039, 1032040,
                    1032041, 1032042, 1032043, 1032044, 1032045, 1032046,
                    1032047, 1032048, 1032049, 1032050, 1032051, 1032052,
                    1032053, 1032054, 1032055, 1032056, 1032057, 1032058,
                    1032059, 1032060, 1032061, 1032062, 1032063, 1032064,
                    1032065, 1032066, 1032067, 1032068, 1032069, 1032070,
                    1032074, 1032091, 1032094, 1032159, 1032160, 1032162,
                    1032164, 1032165, 1032166, 1032167, 1032168, 1032169,
                    1032170, 1032172, 1032174, 1032179, 1032180, 1032181,
                    1032182, 1032183, 1032184, 1032185, 1032186, 1032187,
                    1032188, 1032189, 1032190, 1032191, 1032192, 1032193,
                    1032194, 1032195, 1032196, 1032197, 1032198, 1032199,
                    1032200, 1032201, 1032202, 1032203, 1032204, 1032205,
                    1032206, 1032207, 1032208, 1032209, 1032210, 1032211,
                    1032212, 1032213, 1032214, 1032215, 1032216, 1032217,
                    1032218, 1032219, 1032220, 1032221, 1032222, 1032223,
                    1032224, 1032225, 1032226, 1032227, 1032228, 1032229,
                    1032230, 1032231, 1032232, 1032233, 1032234, 1032235,
                    1032236, 1032237, 1032238, 1032239, 1032240, 1032241,
                    1032242, 1032243, 1032244, 1032245, 1032246, 1032247,
                    1032248, 1032249, 1032250, 1032251, 1032252, 1032253,
                    1032254, 1032255, 1032256, 1032257, 1032258, 1032259,
                    1032260, 1032261, 1032262, 1032263, 1032264, 1032265,
                    1032266, 1032267, 1032268, 1032269, 1032270, 1032271,
                    1032272, 1032273, 1032274, 1032275, 1032276, 1032277,
                    1032278, 1032279, 1032280, 1032281, 1032282, 1032283,
                    1032284, 1032285, 1032286, 1032287, 1032288, 1032289,
                    1032290, 1032291, 1032292, 1032293, 1032294, 1032295,
                    1032296, 1032297, 1032298, 1032299, 1032300, 1032301,
                    1032302, 1032303, 1032304, 1032305, 1032306, 1032359,
                    1032360, 1032361, 1032362, 1032363, 1032364, 1032366,
                    1032367, 1032368, 1032369, 1032370, 1032371, 1032372,
                    1032373, 1032681, 1041446, 1041447, 1041448, 1041449,
                    1041450, 1041451, 1041452, 1041453, 1041454, 1041455,
                    1041456, 1041457, 1041692, 1051978, 1052273, 1052274,
                    1052451, 1078095, 1081009, 1081010, 1081011, 1081012,
                    1081014, 1081016, 1081018, 1081019, 1081021, 1081022,
                    1081033);
        
        return $ptjs;
    }
}
