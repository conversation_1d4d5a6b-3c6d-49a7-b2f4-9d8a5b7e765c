@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/support/task-missing/list')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...  (Case No, Doc No. , Resolution)">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

    @if ($listdata == null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    @if($result == 'notfound')
                        <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
                        <small>Rekod tidak dijumpai!</small>
                    @else
                    <i class="gi gi-search"></i>Carian <span class="text-info">Stuck Task</span><br>
                        <small>Masukkan carian diatas...</small>
                    @endif
                </h1>
            </div>
        </div>
    @endif

    @if($listdata != null)
        <div class="content-header">
            <div class="header-section">
                <h1>
                    <i class="gi gi-search"></i>Stuck Task List in eP<br>
                    <small>Investigating Stuck Task List </small>
                </h1>
            </div>
        </div> 
        
        <div class="widget">
                   
            <div class="widget-extra themed-background-dark">
                
                <h5 class="widget-content-light">
                    Pending <strong>Tasks</strong>
                </h5>
            </div>
            <div class="widget-extra-full">
                <div class="row text-center">
                    @foreach($reportStatPending as $rep)   
                    <div class="col-xs-6 col-lg-2">
                        <h4 style="margin:0px;padding:0px;">
                            <strong>{{$rep->total}}</strong><br>
                            <small>{{$rep->module}}</small>
                        </h4>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    
        @if($success && $success == 'success')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> Task <a href="javascript:void(0)" class="alert-link">saved</a>!
        </div>
        @elseif($success && $success == 'duplicate')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Error</h4> Duplicate record <a href="javascript:void(0)" class="alert-link">Failed</a>!
        </div>
        @elseif($success && $success == 'case-not-exist')
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Error</h4> Case Number is not exist in CRM <a href="javascript:void(0)" class="alert-link">Failed</a>!
        </div>
        @elseif($success && strlen($success) > 0)
        <div class="alert alert-success alert-dismissable">
            <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
            <h4><i class="fa fa-check-circle"></i> Success</h4> Task <a href="javascript:void(0)" class="alert-link">{{$success}}</a>!
        </div>
        @endif

        @if ($errors->any())
            <div class="alert alert-danger">
                <ul>
                    @foreach ($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
            </div>
        @endif
         
        <div class="block block-alt-noborder full">
            
            <span style="display:none" id="loadingTaskForm"> Repopulate Data. Please Wait... <i class="fa fa-spinner fa-spin"></i> </span>
            <div class="row" id="panelFormTask" style="display:none" >
                <div class="col-md-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong><span id="titleTask">Add Missing Task</span></strong></h2>
                            <div class="block-options pull-right">
                                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                                    onclick="$('#panelFormTask').hide();    $('#taskViewOther').hide();" >Close</span>
                            </div>
                        </div>
                        
                        <form id="form-task" action="{{url("/support/task-missing")}}" method="post" class="form-horizontal form-bordered">
                            {{ csrf_field() }}
                            <input name="_method" id="_method"  type="hidden" value="POST">
                            <input type="hidden" name="task_id"  id="task_id" value="" />

                            <div class="col-md-6">
                                <fieldset>
                                    
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="case_no">Case No. <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="case_no" name="case_no" class="form-control" placeholder="Case No CRM.."
                                                    value="{{old('case_no')}}">
                                                <input type="hidden" name="case_type"  id="case_type" value="" />
                                                <span class="input-group-addon"><i class="gi gi-qrcode"></i></span>
                                            </div>
                                            <span id="alert-exist-record" class="text-danger" style="display:none;">This case number already exist in Task Missing!</span>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="case_status">Case Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="case_status" name="case_status" class="form-control" placeholder="Case Status.."
                                                    value="{{old('case_status')}}" readonly>
                                                <span class="input-group-addon"><i class="gi gi-qrcode"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="batch">Batch </label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <input type="text" id="batch" name="batch" class="form-control" placeholder="Batch.."
                                                value="{{old('batch')}}">
                                                <span class="input-group-addon"><i class="gi gi-nameplate"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="module_task">Module <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="module_task" name="module_task" class="form-control">
                                                    <option value="">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_MISSING_MODULE as  $key => $value)
                                                    
                                                    <option value="{{$key}}" @if(old('module_task') == $key) selected @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    
                                </fieldset>
                            </div>
                            <div class="col-md-6 hide" id="taskViewOther">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="process_status">Status <span class="text-danger">*</span></label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="process_status" name="process_status" class="form-control">
                                                    <option value="process_status">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_MISSING_STATUS as  $key => $value)
                                                    <option value="{{$key}}" @if(old('process_status') == $key) selected @endif>{{$value}}</option>
                                                    @endforeach
                                                </select>
                                                <span class="input-group-addon"><i class="gi gi-pin"></i></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Created</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewCreated"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Modified</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewModified"></span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label">Completed</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <span class="form-control" id="viewCompleted"></span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                </fieldset>
                            </div>
                            <div class="col-md-12">
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="doc_no">Document Number<span class="text-danger">*</span></label>
                                    <div class="col-md-9">
                                        <textarea id="doc_no" name="doc_no" rows="6" class="form-control" placeholder="Document Number">{{old('doc_no')}}</textarea>
                                    </div>
                                </div>
                                
                                <div class="form-group form-group-resolution" >
                                    <label class="col-md-3 control-label" for="problem">Problem Details</label>
                                    <div class="col-md-9">
                                        <textarea id="problem" name="problem" rows="6" class="form-control" placeholder="Problem Information will auto collect from Case CRM Description"></textarea>
                                    </div>
                                </div>
                                
                                <div class="form-group form-group-resolution" >
                                    <label class="col-md-3 control-label" for="resolution">Resolution</label>
                                    <div class="col-md-9">
                                        <textarea id="resolution" name="resolution" rows="6" class="form-control" placeholder="Resolution Information.."></textarea>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group form-actions form-actions-button">
                                <div class="col-md-8 col-md-offset-4">
                                    <button type="submit" class="btn btn-sm btn-primary"><i class="fa fa-arrow-right"></i><span id="btn_save_span"> Save</span></button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <!-- Customer Addresses Block -->
            <div class="block" id="panelListTask">
                <div class="block-title" >
                    <h1><i class="fa fa-tasks"></i> <strong>Stuck Task List</strong>
                    </h1>
                    <div class="block-options pull-right action-today">
                        <a href="#modal-list-data" class="modal-list-data-action btn btn-sm btn-info " 
                           data-toggle="modal" data-url="{{url("/support/report/log/task-missing")}}/{{Carbon\Carbon::now()->format('Y-m-d')}}" 
                           data-title="List Action Tasks Missing by Today ">View Today Action</a>
                        @if(Auth::user()->isPatcherRolesEp() || Auth::user()->isDevUsersEp())
                        <a href="#modal_task_missing_report" class="btn btn-sm btn-info " 
                           data-toggle="modal" >Statistic Report</a>
                        @endif
                    </div>
                    
                    
                </div>
                <div class="col-md-12" >
                    <div class="block">
                        <form id="form-search-task" action="{{url("/support/task-missing/list")}}" method="POST" class="form-horizontal form-bordered">
                            {{ csrf_field() }}
                            <input name="_method" id="_method"  type="hidden" value="POST">
                            
                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_status_id">Status</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_status_id" name="search_status_id" class="form-control">
                                                    <option value="">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_MISSING_STATUS as  $key => $value)
                                                    
                                                    <option value="{{$key}}"
                                                            @if(isset($formSearch))
                                                                @if($key == $formSearch["search_status_id"] ) selected @endif
                                                            @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                         
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-3 control-label" for="search_module">Module</label>
                                        <div class="col-md-9">
                                            <div class="input-group">
                                                <select id="search_module" name="search_module" class="form-control">
                                                    <option value="">Please select</option>
                                                    @foreach(App\Services\EPService::$TASK_MISSING_MODULE as  $key => $value)
                                                    
                                                    <option value="{{$key}}"
                                                            @if(isset($formSearch))
                                                                @if($key == $formSearch["search_module"] ) selected @endif
                                                            @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                        
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="col-md-4">
                                <fieldset>
                                    <div class="form-group">
                                        <label class="col-md-4 control-label" for="search_case_status">Case Status</label>
                                        <div class="col-md-8">
                                            <div class="input-group">
                                                <select id="search_case_status" name="search_case_status" class="form-control">
                                                    <option value="">Please select</option>
                                                    @foreach(App\Services\EPService::$CASE_STATUS as  $key => $value)
                                                    
                                                    <option value="{{$key}}"
                                                            @if(isset($formSearch))
                                                                @if($key == $formSearch["search_case_status"] ) selected @endif
                                                            @endif>{{$value}}</option>
                                                   
                                                    @endforeach
                                                </select>
                                             
                                            </div>
                                        </div>
                                    </div>
                                </fieldset>
                            </div>
                            <div class="form-group form-actions form-actions-button text-right">
                                 
                                    <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                                    <a href="{{url('/support/task-missing')}}" class="btn btn-sm btn-warning"><i class="fa fa-repeat"></i> Reset</a>
                               
                            </div>
                        </form>
                        
                    </div>
                </div>
                <button type="button" id="openAddTask" class="btn btn btn-info" style="margin:5px;">Add Task</button>
                <a href="{{url('/support/task-missing/download')}}" target="_blank" id="downloadExcel" class="btn btn btn-default pull-right"  style="margin:5px;">Download All to Excel</a>
                <a href="#modal_upload_file"  data-toggle="modal"  id="uploadExcel" class="btn btn btn-default pull-right"  style="margin:5px;">Upload Excel File</a>
                <div class="table-responsive">
                    <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">Case Created</th>
                            <th class="text-center">Case No</th>
                            <th class="text-center">Case Status</th>
                            <th class="text-center">Batch</th>
                            <th class="text-center">Module</th>
                            <th class="text-center">Doc No</th>
                            <th class="text-center">Status</th>
                            <th class="text-center">Resolution</th>
                            <th class="text-center">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center"><a target="_blank" href="{{url('/support/task-missing/list')}}/{{ $data->case_no }}" title="Go Details Task" >{{ $data->case_created }}</a></td>
                                <td class="text-center"><a target="_blank" href="{{url('support/crm/case/')}}/{{ $data->case_no }}" title="View Detail Case" >{{ $data->case_no }}</a></td>
                                <td class="text-center">{{ $data->case_status }}</td>
                                <td class="text-center">{{ $data->batch }}</td>
                                <td class="text-center">{{ $data->module }}</td>
                                <td class="text-left" width="30%" >
                                    <textarea rows="3" class="form-control" style="width: 100%" readonly>{{ $data->doc_no }}</textarea>
                                </td>
                                <td class="text-center">{{ App\Services\EPService::$TASK_MISSING_STATUS[$data->process_status] }}</td>
                                <td class="text-left" width="30%" >
                                    <textarea rows="3" class="form-control" style="width: 100%" readonly>{{ $data->resolution }}</textarea>
                                </td>
                                <td class="text-center action_table_task">
                                    <div class="btn-group btn-group-xs">
                                        
                                        <a class="btn btn-primary action_table_view_task" data-toggle="tooltip" title="" data-original-title="Show Details"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="hi hi-eye-open"></i></a>
                                        <a class="btn btn-primary action_table_edit_task" data-toggle="tooltip" title="" data-original-title="Edit Task"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="fa fa-pencil"></i></a>
                                        <a class="btn btn-primary action_table_complete_task" data-toggle="tooltip" title="" data-original-title="Set to complete"
                                            href="javascript:void(0)" data-toggle="modal" data-id="{{ $data->task_id }}"><i class="hi hi-ok"></i></a>
                                        
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif
    
    <div id="modal_upload_file" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Upload File Excel</h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <form action="{{url('/support/task-missing/upload')}}" method="post" enctype="multipart/form-data" class="form-horizontal form-bordered" >
                                {{ csrf_field() }}
                                
                                <div class="form-group">
                                    <label class="col-md-3 control-label" for="file-input-excel">File input</label>
                                    <div class="col-md-9">
                                        <input type="file" id="file-input-excel" name="file-input-excel">
                                    </div>
                                </div>
                                <div class="form-group form-actions">
                                    <div class="col-md-3 col-md-offset-9">
                                        <button type="submit" class="btn btn-sm btn-primary action_upload_file"><i class="fa fa-upload"></i> Submit</button>
                                        <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning"><i class="fa fa-close"></i> Tutup</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    
                    </div>
                    <br/>
                    <h4>Sample Excel Format</h4>
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="table-responsive">
                            <table class="table table-bordered">
                                <thead>
                                <tr>
                                    <th class="text-center">Batch</th>
                                    <th class="text-center">CRM Case No</th>
                                    <th class="text-center">Document Number</th>
                                    <th class="text-center">Module</th>
                                    <th class="text-center">Status</th>
                                    <th class="text-center">Composite Instance ID</th>
                                    <th class="text-center">Remarks</th>
                                </tr>
                                <tr>
                                    <td class="text-center">Batch #</td>
                                    <td class="text-center">0000000</td>
                                    <td class="text-center">SQ180000000000000, SQ180000000000000</td>
                                    <td class="text-center">Direct Purchase</td>
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                    <td class="text-center"></td>
                                </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    
    @if(Auth::user()->isPatcherRolesEp() || Auth::user()->isDevUsersEp())
    <div id="modal_task_missing_report" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" data-backdrop="static" data-keyboard="false" >
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <!-- Modal Header -->
                <div class="modal-header text-center">
                    <h2 class="modal-title"> Statistic Report Task Missing</h2>
                </div>
                <!-- END Modal Header -->

                <!-- Modal Body -->
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <address>
                                <strong class="bolder text-info">Missing tasklist statistic  FROM {{$report->carbon_date_start->day}} - {{$report->carbon_today->day}} {{$report->carbon_today->shortEnglishMonth}} @ {{$report->carbon_today->format('h:i A')}}</strong> <br /><br />
                                <strong>Total cases logged : </strong> {{$report->total_all_created}} <br />
                                <strong>Total cases resolved : </strong> {{$report->total_all_solved}} <br />
                                <strong>Total cases pending : </strong> {{$report->total_all_pending}} <br /><br />
                                <strong>Cases created today ( {{$report->carbon_today->day}} {{$report->carbon_today->shortEnglishMonth}} ) : </strong> {{$report->total_created_today}} <br />
                                <strong>Cases resolved today ( ALL cases FROM {{$report->carbon_date_start->day}} - {{$report->carbon_today->day}} {{$report->carbon_today->shortEnglishMonth}} ) : </strong> {{$report->total_solved_today}} <br />
                                <strong>Cases created and resolved today : </strong> {{$report->total_created_solved_today}} <br />
                            </address>   
                        </div>
                        <div class="col-md-12">
                            <button type="button" data-dismiss="modal" class="btn btn-sm btn-warning pull-right"><i class="fa fa-close"></i> Tutup</button>
                        </div>
                    </div>
                    
                </div>
                <!-- END Modal Body -->
            </div>
        </div>
    </div>
    @endif
    
    
    @include('_shared._modalListLogAction')
    
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalListActionLogDatatable.js"></script>
<script>$(function(){ ModalListActionLogDatatable.init(); });</script>
<script>
    
    function openFormTask(typeForm){
        $('#to-top').click(); //move scroll up to top page
        
        $('#panelFormTask').removeClass('hide'); 
        $('#panelFormTask').show(); 
            
        if(typeForm === 'add'){
            $('#taskViewOther').hide();
            $('.form-actions-button').show();
            $("#titleTask").text("Add Missing Task");
            $("#btn_save_span").text("Save");
            $("#_method").val("POST");
            $("#case_no").removeAttr("readonly");
        }else if(typeForm === 'edit'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $("#titleTask").text("Update Missing Task");
            $("#btn_save_span").text("Save");
            $("#_method").val("POST");
            $("#case_no").attr("readonly","readonly");
        }else if(typeForm === 'complete'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $("#titleTask").text("To Complete Missing Task");
            $("#btn_save_span").text("Set Completed");
            $("#_method").val("PUT");
            
            $("#process_status").attr("readonly","readonly");
            $("#case_no").attr("readonly","readonly");
        }else if(typeForm === 'view'){
            $('#taskViewOther').removeClass('hide'); 
            $('#taskViewOther').show();
            $('.form-actions-button').show();
            $("#titleTask").text("View Detail Missing Task");
            $("#btn_save_span").parent().hide();
            $("#_method").val("GET");
            
            $("#process_status").attr("readonly","readonly");
            $("#case_no").attr("readonly","readonly");
        }
        
        $("#process_status").val("");
        $("#task_id").val("");
        $("#case_no").val("");
        $("#case_type").val("");
        $("#case_status").val("");
        $("#module_task").val("");
        $("#batch").val("");
        $("#doc_no").text("");
        $("#resolution").text("");
        $("#viewModified").text('');
        $("#viewCompleted").text('');
        $('#alert-exist-record').hide();
    }
 
    function populateFormData(obj){
        $("#task_id").val(obj.task_id);
        $("#case_type").val(obj.case_type);
        $("#case_no").val(obj.case_no);
        $("#case_status").val(obj.case_status);
        $("#module_task").val(obj.module);
        $("#doc_no").val(obj.doc_no);
        $("#batch").val(obj.batch);
        if(obj.resolution !== null){
            $("#resolution").text(obj.resolution);
        }
        if(obj.problem !== null){
            $("#problem").text(obj.problem);
        }
        $("#process_status").val(obj.process_status);
        $("#viewCreated").text(obj.created_by+ ' on '+obj.created_at);
        if(obj.updated_by !== null){
            $("#viewModified").text(obj.updated_by+ ' on '+obj.updated_at);
        }
        if(obj.completed_by !== null){
            $("#viewCompleted").text(obj.completed_by+ ' on '+obj.completed_at);
        }
    }
    
    $( "#case_no" ).bind( "change focusout", function() {
        $('#alert-exist-record').hide();
        var readonly = $("#case_no").attr("readonly");
        if( readonly !== 'readonly'){
            var case_no = $(this).val();
            console.log('caseno: '+case_no);
            $('#case_type').val('');
            $('#case_status').val('');
            

            
            $.ajax({
                url: "/support/task-missing/check/"+case_no,
                context: document.body
            }).done(function(obj) {
                if(obj !== null){

                    if(obj.hasOwnProperty('is_exist_task')){
                        if(obj.is_exist_task === 1){
                            $('#alert-exist-record').show();
                        }
                    }
                    var caseObj = JSON.parse(obj.case);
                    if(caseObj.hasOwnProperty('status')){
                        $('#case_status').val(caseObj.status);
                    }
                    if(caseObj.hasOwnProperty('request_type_c')){
                        $('#case_type').val(caseObj.request_type_c);
                    }
                }
            });
        }
    });

    $('div#panelListTask').on("click",'#openAddTask', function(){
        openFormTask("add");
    });

    $('td.action_table_task').on("click",'a.action_table_edit_task', function(){
        
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("edit");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task-missing/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);
            populateFormData(obj);
            
        });
    });

    $('td.action_table_task').on("click",'a.action_table_complete_task', function(){
        $('#to-top').click();
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("complete");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task-missing/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);
            populateFormData(obj);
        });
    });

    $('td.action_table_task').on("click",'a.action_table_view_task', function(){
        $('#to-top').click();
        var taskID = $(this).attr('data-id');
        console.log("Task ID: "+taskID);
        openFormTask("view");
        
        $('#loadingTaskForm').show();
        $.ajax({
            url: "/support/task-missing/detail/"+taskID,
            context: document.body
        }).done(function(resp) {
            $('#loadingTaskForm').hide();
            var obj = JSON.parse(resp);
            populateFormData(obj);
        });
    });

</script>
@endsection



