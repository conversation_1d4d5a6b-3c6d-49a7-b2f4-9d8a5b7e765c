@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<!-- END Search Form -->
@endsection

@section('content')
<!-- Content -->
@if (Auth::user())
<div class="row">
        <div class="col-lg-12">
            <div class="widget">
                <div class='widget-extra themed-background-dark center-block'>
                 <h5 class='widget-content-light'>
                     Case Management - <strong>By Module : </strong>      
                 </h5>
             </div>
                <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
                <div id="chartContainerStack" style="height: 300px; width: 100%; border-style: groove;"></div>
            </div>
        </div>
    </div>
 <div class="row">
     <div class="col-lg-12">
         <div class='widget'>            
             <div class='text-center' style="background: #ffffff; padding: 5px;">  
                <button type="submit" id="button_submit_FL" name="button_submit_DP" class="btn btn-sm btn-info text-center" value="10712_15005">
                        <div class="h5 mb-0" style="font-weight: 800">FL</div>
                        <span>Fulfillment</span>
                    </button>                 
                    <button type="submit" id="button_submit_SM" name="button_submit_SM" class="btn btn-sm btn-info text-center" value="10712_15002">
                        <div class="h5 mb-0" style="font-weight: 800">SM</div>
                            <span>Supplier Management</span>
                    </button>
                    <button type="submit" id="button_submit_QT" name="button_submit_QT" class="btn btn-sm btn-info text-center" value="10712_15004"> 
                        <div class="h5 mb-0" style="font-weight: 800">QT</div>
                            <span>Quotation/Tender</span>
                    </button>
                    <button type="submit" id="button_submit_DP" name="button_submit_DP" class="btn btn-sm btn-info text-center" value="10712_15003">
                        <div class="h5 mb-0" style="font-weight: 800">DP</div>
                            <span>Direct Purchase</span>
                    </button>
                    <button type="submit" id="button_submit_CT" name="button_submit_CT" class="btn btn-sm btn-info text-center" value="10712_15006">
                        <div class="h5 mb-0" style="font-weight: 800">CT</div>
                        <span>Contract Management</span>
                    </button>
                    <button type="submit" id="button_submit_PM" name="button_submit_PM" class="btn btn-sm btn-info text-center" value="10712_15008">
                        <div class="h5 mb-0" style="font-weight: 800">PM</div>
                        <span>Profile Management</span>
                    </button>
                    <button type="submit" id="button_submit_PP" name="button_submit_PP" class="btn btn-sm btn-info text-center" value="10712_15010">
                        <div class="h5 mb-0" style="font-weight: 800">PP</div>
                        <span>Pplan</span>
                    </button>
                    <button type="submit" id="button_submit_Other" name="button_submit_Other" class="btn btn-sm btn-info text-center" >
                        <div class="h5 mb-0" style="font-weight: 800">O</div>
                        <span>Others</span>
                    </button>
                </div>
             <div id="dash_incident_itspec_module">
                 <div class="text-center"><i class=""></i></div>
             </div>
         </div>
     </div>
     </div>
     
    
@endif

<div id="modal-list-data" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- END Content -->
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->

<script>
    var APP_URL = {!! json_encode(url('/')) !!}

    App.datatables();
    /* Initialize Datatables */
    var tableListData = $('#basic-datatable').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 10,
        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
    });

    $(document).ready(function () {
        $('.widget').on("click", '.modal-list-data-action', function () {

            $('.spinner-loading').show();
            $('#basic-datatable').html('').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function (data) {
                    $data = $(data);
                    $('#basic-datatable').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable').DataTable({
                        columnDefs: [{orderable: false, targets: [0]}],
                        pageLength: 10,
                        lengthMenu: [[10, 20, 30, -1], [10, 20, 30, 'All']]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });
        $('#button_submit_SM').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_DP').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_FL').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_QT').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_PM').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });    
        $('#button_submit_CT').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_PP').click(function() {
        var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module',
            data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });
        $('#button_submit_Other').click(function() {
       // var SubCatID = $(this).val();
        $.ajax({
            type: 'GET',
            url: APP_URL + '/dashboard/crm/incident/itspec/module/others',
           // data: {"subcatid": SubCatID},
            success: function(data) {
               $data = $(data);
               $('#dash_incident_itspec_module').hide().html($data).fadeIn();
            }
            });
        });        
    });

</script>
<script>
    $('#page-container').removeAttr('class');
</script>

<script>
window.onload = function () {
    
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth()+1; //January is 0!
    var yyyy = today.getFullYear();
    if(dd<10) {
        dd = '0'+dd
    } 

    if(mm<10) {
        mm = '0'+mm
    } 
    today = yyyy + '-' + mm + '-' + dd;
    
    var months    = ['January','February','March','April','May','June','July','August','September','October','November','December'];
    var now       = new Date();
    var thisMonth = months[now.getMonth()]; // getMonth method returns the month of the date (0-January :: 11-December)
    var output = document.getElementById('output');
     
// second chart1
var chart = new CanvasJS.Chart("chartContainerStack", {
   
	title: {
		text: "Case-Statistics Assigned to Specialist By Module"
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
	axisY: {
		title: "Total Case Assigned",
                interval : 2
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [
		{
			type: "column",
			name: "Group IT Specialist(Developer)",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($dataDeveloperbyModuleStat) !!}
		},
                {
			type: "column",
			name: "Group IT Specialist(Production Support)",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($dataProdSupportbyModuleStat) !!}
		},{
			type: "column",
			name: "Group Middleware",
			showInLegend: true,
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                        dataPoints : {!! json_encode($dataMiddlewaredbyModuleStat) !!}
		}

	]
});
 
chart.render();
 
function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;    
	}
	e.chart.render();
}
 
}
</script>
<!-- Load and execute javascript code used only in this page -->
@endsection
