@if (count($slaSummaries))
<div class="table-responsive mt-4" style="max-height: 500px; overflow-y: auto; background-color: #f8f9fa">
    <table class="table table-bordered table-striped">
        <thead class="table-dark">
            <tr>
                <th>SLA Type</th>
                <th>Case Number</th>
                <th>Task Number</th>
                <th>SLA Flag</th>
                <th>Start Datetime</th>
                <th>Due Datetime</th>
                <th>Actual Start Datetime</th>
                <th>Completed Datetime</th>
                <th>Available Duration</th>
                <th>Actual Duration</th>
                <th>Duration (Calculated)</th>
                <th>Deleted</th>
            </tr>
        </thead>
        <tbody>
            @foreach ($slaSummaries as $row)
            <tr class="{{ $row->deleted == 1 ? 'table-danger' : '' }}"
                data-sla-type="{{ $row->sla_type }}"
                data-case-number="{{ $row->case_number }}"
                data-task-number="{{ $row->task_number ?? '' }}" {{-- Ensure task_number is present or empty string --}}
                data-id="{{ $row->case_number }}_{{ $row->sla_type }}_{{ $row->task_number ?? 'na' }}"> {{-- Unique ID for the row if needed by JS --}}
                <td>{{ $row->sla_type }}</td>
                <td>{{ $row->case_number }}</td>
                <td>{{ $row->task_number }}</td>
                <td>{{ $row->sla_flag }}</td>
                <td>
                    <input type="text" class="form-control form-control-sm update-sla-summary"
                        data-field="start_datetime"
                        value="{{ $row->start_datetime }}" style="width: 145px;"
                        placeholder="YYYY-MM-DD HH:MM:SS">
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm update-sla-summary"
                        data-field="due_datetime"
                        value="{{ $row->due_datetime }}" style="width: 145px;"
                        placeholder="YYYY-MM-DD HH:MM:SS">
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm update-sla-summary actual-start-datetime-input"
                        data-field="actual_start_datetime"
                        value="{{ $row->actual_start_datetime }}" style="width: 145px;"
                        placeholder="YYYY-MM-DD HH:MM:SS">
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm update-sla-summary completed-datetime-input"
                        data-field="completed_datetime"
                        value="{{ $row->completed_datetime }}" style="width: 145px;"
                        placeholder="YYYY-MM-DD HH:MM:SS">
                </td>
                <td>{{ $row->available_duration }}</td>
                <td>
                    <input type="text" class="form-control form-control-sm update-sla-summary actual-duration-input"
                        data-field="actual_duration"
                        value="{{ $row->actual_duration }}" style="width: 100px; {{ $row->actual_duration > $row->available_duration ? 'color: red; border-color: red;' : '' }}">
                </td>
                <td class="calculated-duration">
                    <span class="{{ $row->actual_start_datetime && $row->completed_datetime && 
                            (strtotime($row->completed_datetime) - strtotime($row->actual_start_datetime)) != $row->actual_duration 
                            ? 'text-danger font-weight-bold' : '' }}">
                        {{ ($row->actual_start_datetime && $row->completed_datetime) ? 
                                (strtotime($row->completed_datetime) - strtotime($row->actual_start_datetime)) : '' }}
                    </span>
                </td>
                <td>
                    <select class="form-control form-control-sm update-sla-summary" data-field="deleted" style="width:70px;">
                        <option value="0" {{ $row->deleted == 0 ? 'selected' : '' }}>No</option>
                        <option value="1" {{ $row->deleted == 1 ? 'selected' : '' }}>Yes</option>
                    </select>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
@else
    <div class="alert alert-warning mt-4">No SLA summary found for this case number.</div>
@endif