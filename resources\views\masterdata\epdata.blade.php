@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')

<div class="widget">
    <div class="block-title widget-extra themed-background-dark">
        <div class="widget-extra themed-background-dark">
            <h5 class='widget-content-light'>
                EP - <strong>Master Data</strong>
            </h5>
        </div>
        <div class="block">
            <form id="form-search-task" action="{{url("find/masterdata/ep")}}" method="post" class="form-horizontal form-bordered" onsubmit="return true;">
                {{ csrf_field() }}
                <div class="col-md-4">
                    <fieldset>
                        <div class="form-group">
                            <label class="col-md-3 control-label" for="type">Type </label>
                            <div class="col-md-9">
                                <div class="input-group">
                                    <select id="type" name="type" class="form-control typeSelect" required="true">
                                        <option value="">Please Select</option>
                                        <option value="vot" <?php echo (isset($_POST['type']) && $_POST['type'] == 'vot') ? 'selected="selected"' : ''; ?>> Vot </option>
                                        <option value="ptj" <?php echo (isset($_POST['type']) && $_POST['type'] == 'ptj') ? 'selected="selected"' : ''; ?>> Organization </option>
                                        <option value="ag" <?php echo (isset($_POST['type']) && $_POST['type'] == 'ag') ? 'selected="selected"' : ''; ?>> AG </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="col-md-4">
                    <fieldset>
                        <div id="div_dynamic" class="form-group">
                            <label class="col-md-3 control-label" for="code">Code </label>
                            <div class="col-md-9">
                                <input type="text" id="code" name="code" class="form-control" value="{{old('code')}}">
                            </div>
                        </div>
                    </fieldset>
                </div><br/>
                <div class="col-md-4">
                    <fieldset>
                        <div class="form-actions form-actions-button">
                            <button type="submit" class="btn btn-sm btn-info pull-right"><i class="fa fa-search"> Search </i></button>
                        </div>
                    </fieldset>
                </div>
            </form><br/><br/><br/><br/><br/>
            @if($type === 'vot')
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Vot Fund Id</th>
                        <th class="text-center">Vot Fund Type</th>
                        <th class="text-center">Vot Fund Code</th>
                        <th class="text-center">Description</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">PTJ Group ID</th>
                        <th class="text-center">Org Code</th>
                        <th class="text-center">Total</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($result))
                    @foreach ($result as $data)
                    <tr>
                        <td class="text-center">{{ $data->vot_fund_id }}</td>
                        <td class="text-center">{{ $data->vot_fund_type }}</td>
                        <td class="text-center">{{ $data->vot_fund_code }}</td>
                        <td class="text-center">{{ $data->description }}</td>
                        <td class="text-center">{{ $data->record_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                        <td class="text-center">{{ $data->ptj_group_id }}</td> 
                        @if(isset($data->org_code))<td class="text-center">{{ $data->org_code }}</td> @else <td class="text-center"></td> @endif
                        @if(isset($data->total))
                        @if($data->total > 0)
                        @if($valueCode !== null || $setiaCode !== null)
                        <td data-id="{{ $data->vot_fund_id }}" data-type="{{ $data->vot_fund_type }}" data-value="{{$valueCode}}" data-setia="{{$setiaCode}}" class="text-center epMasterDetailVot"> <a>{{ $data->total }}</a></td>
                        @else
                        <td data-id="{{ $data->vot_fund_id }}" data-type="{{ $data->vot_fund_type }}" data-value="" class="text-center epMasterDetailVot"> <a>{{ $data->total }}</a></td>
                        @endif
                        @else <td class="text-center">{{ $data->total }}</td> @endif @endif
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table> <br/><br/>
            <table class="table table-vcenter table-condensed table-bordered" id="vot-detail-table" style="display: none">
                <thead>
                    <tr style="display: none">
                        <th>Order</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            @elseif($type === 'ptj')
            <table id="basic-datatable-2" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">Org Profile Id</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Changed By</th>
                        <th class="text-center">Org Validity Id</th>
                        <th class="text-center">Org Code</th>
                        <th class="text-center">Org Name</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">AG Office Id</th>
                        @if($result[0]->parenta !== null)<th class="text-center">Parent Profile Id</th>@endif
                        @if($result[0]->parentb !== null)<th class="text-center">Parent Profile Id</th>@endif
                        @if($result[0]->parentc !== null)<th class="text-center">Parent Profile Id</th>@endif
                    </tr>
                </thead>
                <tbody>
                    @if(isset($result))
                    @foreach ($result as $data)
                    <tr>
                        <td class="text-center">{{ $data->org_profile_id }} <br/> {{ $data->org_type_id }} - {{ $data->type}}</td>
                        <td class="text-center">{{ $data->record_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                        <td class="text-center">{{ $data->changed_by }}</td>
                        <td class="text-center">{{ $data->org_validity_id }}</td>
                        <td class="text-center">{{ $data->org_code }}</td>
                        <td class="text-center">{{ $data->org_name }}</td>
                        <td class="text-center">{{ $data->ov_record_status }}</td>
                        <td class="text-center">{{ $data->ag_office_id }}</td>
                        @if($data->parenta !== null)
                        <td data-id="{{ $data->parenta }}" class="text-center epMasterDetailOrg"> <a>{{ $data->parenta }} <br/> {{ $data->parenttypeida }} - {{ $data->parenttypea}}</a> </td>
                        @endif
                        @if($data->parentb !== null)
                        <td data-id="{{ $data->parentb }}" class="text-center epMasterDetailOrg"> <a>{{ $data->parentb }} <br/> {{ $data->parenttypeidb }} - {{ $data->parenttypeb}}</a> </td>
                        @endif
                        @if($data->parentc !== null)
                        <td data-id="{{ $data->parentc }}" class="text-center epMasterDetailOrg"> <a>{{ $data->parentc }} <br/> {{ $data->parenttypeidc }} - {{ $data->parenttypec}}</a> </td>
                        @endif
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table> <br/><br/>
            <table class="table table-vcenter table-condensed table-bordered" id="org-detail-table" style="display: none; width: 100%">
                <thead>
                    <tr>
                        <th class="text-center">Org Profile Id</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Changed By</th>
                        <th class="text-center">Org Validity Id</th>
                        <th class="text-center">Org Code</th>
                        <th class="text-center">Org Name</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Ag Office Id</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            @else
            <table id="basic-datatable-3" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">AG Office Id</th>
                        <th class="text-center">Office Code</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Created By</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Changed By</th>
                    </tr>
                </thead>
                <tbody>
                    @if(isset($result))
                    @foreach ($result as $data)
                    <tr>
                        <td data-id="{{ $data->ag_office_id }}" class="text-center epMasterDetailAg"> <a>{{ $data->ag_office_id }}</a> </td>
                        <td class="text-center">{{ $data->office_code }}</td>
                        <td class="text-center">{{ $data->record_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                        <td class="text-center">{{ $data->created_by }}</td>
                        <td class="text-center">{{ $data->changed_date }}</td>
                        <td class="text-center">{{ $data->changed_by }}</td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table> <br/><br/>
            <table class="table table-vcenter table-condensed table-bordered" id="ag-detail-table" style="display: none">
                <thead>
                    <tr>
                        <th class="text-center">Ag Validity Id</th>
                        <th class="text-center">Ag Office Id</th>
                        <th class="text-center">Office Name</th>
                        <th class="text-center">Eff Date</th>
                        <th class="text-center">Exp Date</th>
                        <th class="text-center">Record Status</th>
                        <th class="text-center">Created Date</th>
                        <th class="text-center">Created By</th>
                        <th class="text-center">Changed Date</th>
                        <th class="text-center">Changed By</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
            @endif
        </div>
    </div>
</div>
</div>
@endsection

@section('jsprivate')
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {
        TablesDatatables.init();
    });
    App.datatables();
</script>
<script>
    $('.typeSelect').change(function () {
        var task = $('#type option:selected').val();
        console.log(task);
        $("#div_dynamic").show();
        if (task === 'vot') {
            console.log('vot');
            $("#labelDynamic").text('Code');
            $('input:text').attr('value', '');
            $('input:text').attr('placeholder', 'Vot|Dana|Prg Activity| Project Code');
        } else if (task === 'ptj') {
            $("#labelDynamic").text('Org Code');
            $('input:text').attr('value', '');
            $('input:text').attr('placeholder', 'Ptj|Kptj|Pp Code');
        } else {
            $("#labelDynamic").text('Ag Code');
            $('input:text').attr('value', '');
            $('input:text').attr('placeholder', 'Ag Code');
        }
    });

    $('.epMasterDetailVot').click(function () {

        $("#vot-detail-table").DataTable().destroy();
        document.getElementById('vot-detail-table').style.display = 'none';
        var id = $(this).attr('data-id');
        var vottype = $(this).attr('data-type');
        var valuecode = $(this).attr('data-value');
        var setiacode = $(this).attr('data-setia');
        var csrf = $("input[name=_token]").val();

        $.ajax({
            type: "GET",
            url: "/find/masterdata/ep/epdetail",
            mimeType: 'json',
            data: {"_token": csrf, "id": id, "vottype": vottype, "type": "vot", "valuecode": valuecode, "setiacode": setiacode},
            success: function (data) {
                if (vottype === 'B' || vottype === 'T') {
                    $('#vot-detail-table thead').empty();
                    $('#vot-detail-table tbody').empty();
                    var html = '<tr>';
                    html += '<th class="text-center">Program Activity Id</th>';
                    html += '<th class="text-center">Vot Fund Id</th>';
                    html += '<th class="text-center">Program Activity Code</th>';
                    html += '<th class="text-center">Description</th>';
                    html += '<th class="text-center">Eff Date</th>';
                    html += '<th class="text-center">Exp Date</th>';
                    html += '<th class="text-center">Created Date</th>';
                    html += '<th class="text-center">Changed Date</th></tr>';
                    $("#vot-detail-table thead").append(html);

                    $.each(data, function (i, data) {
                        var body = "<tr>";
                        body += "<td class='text-center'>" + data.prg_activity_id + "</td>";
                        body += "<td class='text-center'>" + data.vot_fund_id + "</td>";
                        body += "<td class='text-center'>" + data.prg_activity_code + "</td>";
                        body += "<td class='text-center'>" + data.description + "</td>";
                        body += "<td class='text-center'>" + data.eff_date + "</td>";
                        body += "<td class='text-center'>" + data.exp_date + "</td>";
                        body += "<td class='text-center'>" + data.created_date + "</td>";
                        body += "<td class='text-center'>" + data.changed_date + "</td>";
                        body += "</tr>";
                        $("#vot-detail-table tbody").append(body);
                    });

                } else if (vottype === 'P' || vottype === 'S') {
                    $('#vot-detail-table thead').empty();
                    $('#vot-detail-table tbody').empty();
                    var html = '<tr>';
                    html += '<th class="text-center">Sub Setia Id</th>';
                    html += '<th class="text-center">Vot Fund Id</th>';
                    html += '<th class="text-center">Project Code</th>';
                    html += '<th class="text-center">Setia Code</th>';
                    html += '<th class="text-center">Sub Setia Code</th>';
                    html += '<th class="text-center">Description</th>';
                    html += '<th class="text-center">Eff Date</th>';
                    html += '<th class="text-center">Exp Date</th>';
                    html += '<th class="text-center">Created Date</th>';
                    html += '<th class="text-center">Changed Date</th></tr>';
                    $("#vot-detail-table thead").append(html);

                    $.each(data, function (i, data) {
                        var body = "<tr>";
                        body += "<td class='text-center'>" + data.sub_setia_id + "</td>";
                        body += "<td class='text-center'>" + data.vot_fund_id + "</td>";
                        body += "<td class='text-center'>" + data.project_code + "</td>";
                        body += "<td class='text-center'>" + data.setia_code + "</td>";
                        body += "<td class='text-center'>" + data.sub_setia_code + "</td>";
                        body += "<td class='text-center'>" + data.description + "</td>";
                        body += "<td class='text-center'>" + data.eff_date + "</td>";
                        body += "<td class='text-center'>" + data.exp_date + "</td>";
                        body += "<td class='text-center'>" + data.created_date + "</td>";
                        body += "<td class='text-center'>" + data.changed_date + "</td>";
                        body += "</tr>";
                        $("#vot-detail-table tbody").append(body);
                    });
                }
                $("#vot-detail-table").DataTable();
                document.getElementById('vot-detail-table').style.display = 'block';
            },
            error: function (xhr, status, error) {
            }
        });
    });
    $('.epMasterDetailOrg').click(function () {
        $("#org-detail-table").DataTable().destroy();
        document.getElementById('org-detail-table').style.display = 'none';

        var id = $(this).attr('data-id');
        var csrf = $("input[name=_token]").val();

        $.ajax({
            type: "GET",
            url: "/find/masterdata/ep/epdetail",
            mimeType: 'json',
            data: {"_token": csrf, "id": id, "type": "org"},
            success: function (data) {
                $('#org-detail-table tbody').empty();
                $.each(data, function (i, data) {
                    var body = "<tr>";
                    body += "<td class='text-center'>" + data.org_profile_id + "</td>";
                    body += "<td class='text-center'>" + data.record_status + "</td>";
                    body += "<td class='text-center'>" + data.created_date + "</td>";
                    body += "<td class='text-center'>" + data.changed_date + "</td>";
                    body += "<td class='text-center'>" + data.changed_by + "</td>";
                    body += "<td class='text-center'>" + data.org_validity_id + "</td>";
                    body += "<td class='text-center'>" + data.org_code + "</td>";
                    body += "<td class='text-center'>" + data.org_name + "</td>";
                    body += "<td class='text-center'>" + data.ov_record_status + "</td>";
                    body += "<td class='text-center'>" + data.ag_office_id + "</td>";
                    body += "</tr>";
                    $("#org-detail-table tbody").append(body);
                });
                $("#org-detail-table").DataTable();
                document.getElementById('org-detail-table').style.display = "block";
            },
            error: function (xhr, status, error) {
            }
        });
    });
    $('.epMasterDetailAg').click(function () {
        $("#ag-detail-table").DataTable().destroy();
        document.getElementById('ag-detail-table').style.display = 'none';

        var id = $(this).attr('data-id');
        var csrf = $("input[name=_token]").val();

        $.ajax({
            type: "GET",
            url: "/find/masterdata/ep/epdetail",
            mimeType: 'json',
            data: {"_token": csrf, "id": id, "type": "ag"},
            success: function (data) {
                $('#ag-detail-table tbody').empty();
                $.each(data, function (i, data) {
                    var body = "<tr>";
                    body += "<td class='text-center'>" + data.ag_validity_id + "</td>";
                    body += "<td class='text-center'>" + data.ag_office_id + "</td>";
                    body += "<td class='text-center'>" + data.office_name + "</td>";
                    body += "<td class='text-center'>" + data.eff_date + "</td>";
                    body += "<td class='text-center'>" + data.exp_date + "</td>";
                    body += "<td class='text-center'>" + data.record_status + "</td>";
                    body += "<td class='text-center'>" + data.created_by + "</td>";
                    body += "<td class='text-center'>" + data.created_date + "</td>";
                    body += "<td class='text-center'>" + data.changed_by + "</td>";
                    body += "<td class='text-center'>" + data.changed_date + "</td>";
                    body += "</tr>";
                    $("#ag-detail-table tbody").append(body);
                });
                $("#ag-detail-table").DataTable();
                document.getElementById('ag-detail-table').style.display = "block";
            },
            error: function (xhr, status, error) {
            }
        });
    });
</script>
@endsection