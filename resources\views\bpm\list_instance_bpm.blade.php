@extends('layouts.guest-dash')

@section('header')

@endsection


@section('content')


    
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li>
                <a href="{{ url('/bpm/task/find') }}"><i class="fa fa-tasks"></i> Find Task</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/query') }}"><i class="fa fa-list-alt"></i> Instance Query</a>
            </li>
            <li>
                <a href="{{ url('/bpm/instance/find') }}"><i class="fa fa-cubes"></i> Process Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/worklist/find') }}"><i class="fa fa-list-ol"></i> Worklist Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/service/manager') }}"><i class="fa fa-code"></i> Service Manager</a>
            </li>
            <li>
                <a href="{{ url('/bpm/errhandler/find') }}"><i class="fa fa-bug"></i> Error Handler List</a>
            </li>
            <li class="active">
                <a href="{{ url('/find/bpm/instanceid') }}"><i class="gi gi-package"></i> Track Comp. Instance</a>
            </li>
        </ul>
    </div>
    <div class="block">
        <div class="block-title">
            <h2><strong>Search</strong></h2>
        </div>

        <div class="block">

            @if ($errors->any())
                <div class="alert alert-danger">
                    <ul>
                        @foreach ($errors->all() as $error)
                            <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
            @endif
            <form id="form-search-mminf" action="{{url("/find/bpm/instanceid")}}" method="get" class="form-horizontal" >
                {{ csrf_field() }}
                <input name="_method" id="_method"  type="hidden" value="POST">

                <div class="form-group">
                    <label class="col-md-3 control-label" for="date_start">Document ID <span class="text-danger">*</span></label>

                    <div class="col-md-5">
                        <input type="text" id="doc_id" name="doc_id" class="form-control" 
                                placeholder="Document ID " 
                                value="@if(old('doc_id') != null){{old('doc_id')}}@endif">
                    </div>
                </div>
                <div class="form-group hide">
                    <label class="col-md-3 control-label" for="module">Module <span class="text-danger">*</span></label>
                    <div class="col-md-5    ">
                        <div class="input-group">
                            <select id="module" name="module" class="form-control">
                                <option value="">Please select</option>
                                @foreach(App\Services\EPService::$BPM_COMPOSITE_MODULE as  $key => $value)

                                <option value="{{$key}}" @if(old('module') == $key) selected @endif>{{$value}}</option>

                                @endforeach
                            </select>
                            <span class="input-group-addon"><i class="gi gi-certificate"></i></span>
                        </div>
                    </div>
                </div>
                
                <div class="form-group form-actions">
                    <div class="col-md-9 col-md-offset-3">
                        <button type="submit" class="btn btn-sm btn-default"><i class="gi gi-search"></i> Search</button>
                        <small>Search Doc ID. This search take more longer to get result. BPM SOA Refresh start on 7PM - 11PM (There will be no result around this time)</small>
                    </div>
                </div>
            </form>
                      
        </div>
    </div>

    @if($listdata == null || count($listdata) == 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Result List </strong> </h1>
            </div>
            <div class="row">
              <div class="col-sm-6">
                  <p>Tiada rekod</p>
              </div>
            </div>
        </div>
    </div>
    @endif
    @if($listdata != null && count($listdata) > 0)
    <div class="block block-alt-noborder full">
        <!-- Customer Addresses Block -->
        <div class="block">
            <div class="block-title panel-heading epss-title-s1">
                <h1><i class="fa fa-building-o"></i> <strong>Result List </strong> </h1>
            </div>
            <p class="text-info bolder">Data carian tugasan adalah lewat sehari dari data production.
            <br />
            
            
            
            @if($listdata [0]->composite_instance_id == '' || strlen($listdata [0]->composite_instance_id ) == 0)
            <div class="alert alert-warning">
                <h4><i class="fa fa-exclamation-triangle"></i> BPM COMPOSITE INSTANCE IS NULL WHEN RESULT FOUND</h4> 
                This happen cause of record process instance already purge in CUBE_INSTANCE.
                To disable Active Unique Doc ID in subscription composite. Kindly do datapatch to update STATE STATUS = 3 (COMPLETED). 
                After patch is done, then you may proceed to recreate BPM Task again.
            </div>
            <pre class="line-numbers"><code class="language-php">/**UPDATE STATE AS 3 (COMPLETED) to allow new service create a new composite instance**/
UPDATE DLV_SUBSCRIPTION set state = 3 where cikey = 'REFER PROCESS INSTANCE KEY';</code>
            </pre>
            @endif
            <div class="table-responsive">
                <table id="bpm-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                    <tr>
                        <th class="text-center">COMPOSITE INSTANCE ID</th>
                        <th class="text-center">COMPOSITE</th>
                        <th class="text-center">PROCESS INSTANCE KEY</th>
                        <th class="text-center">COMPOSITE NAME</th>
                        <th class="text-center">COMPONENT NAME</th>
                        <th class="text-center">SUBSCRIPTION CREATION DATE</th>
                         <th class="text-center">STATE STATUS</th>
                    </tr>
                    </thead>
                    <tbody>
                    @foreach ($listdata as $data)
                        <tr>
                            <td class="text-center">{{ $data->composite_instance_id }}</td>
                            <td class="text-center">{{ $data->composite }}</td>
                            <td class="text-center">{{ $data->cikey }}</td>
                            <td class="text-center">{{ $data->composite_name }}</td>
                            <td class="text-center">{{ $data->component_name }}</td>
                            <td class="text-center">{{ $data->subscription_date }}</td>
                            <td class="text-center">{{ $data->state }}</td>
                        </tr>
                    @endforeach
                    </tbody>
                </table>
            </div>
        </div>
        <!-- END Customer Addresses Block -->
    </div>
    @endif


@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script>$('#page-container').removeAttr('class');</script>
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
@endsection



