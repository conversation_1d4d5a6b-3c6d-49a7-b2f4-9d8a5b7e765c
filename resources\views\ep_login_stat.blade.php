@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
            <i class="gi gi-charts"></i>Login eP Statistic<br><small></small>
        </h1>        
    </div>
@endsection

@section('content')
      <div class="widget">
                   <head>
              <meta http-equiv="refresh" content="300">
              <script src="https://canvasjs.com/assets/script/canvasjs.min.js"></script>
          </head>

            <div class="widget-extra-full">
                <div class="row">
                <div class="row text-center">
                    <div class="col-md-12">
                        <div class="widget-extra themed-background-dark">

                            <h5 class="widget-content-light">
                                Today traffic in eP  <strong>{{$resultTime}}</strong>
                            </h5>
                        </div>
                       <ul class="nav-horizontal text-center">

                         <li>
                             <a href="javascript:void(0)">                                  
                                 <h1><strong>{{$sumPTJ}}</strong></h1>
                                 <strong>TOTAL PTJ</strong><br>
                             </a>
                         </li>
                         <li>
                             <a href="javascript:void(0)">                                  
                                 <h1><strong>{{$sumSupp}}</strong></h1>
                                 <strong>TOTAL SUPPLIER</strong><br>
                             </a>
                         </li>
                         <li>
                             <a href="javascript:void(0)">                                  
                                 <h1><strong>{{$sumUserLogin}}</strong></h1>
                                 <strong>TOTAL USER LOGIN</strong><br>
                             </a>
                         </li>
                     </ul>
                        </div> 
                    <div class="col-md-6 hide" >
                        <div class="widget-extra themed-background-dark">

                            <h5 class="widget-content-light">
                                Today eP Login by Session
                            </h5>
                        </div> 
                        <ul class="nav-horizontal text-center">

                            <li>
                                <a href="javascript:void(0)">                                  
                                    <h1><strong>{{$totalSessionPTJ}}</strong></h1>
                                    <strong>TOTAL PTJ</strong><br>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0)">                                  
                                    <h1><strong>{{$totalSessionSupp}}</strong></h1>
                                    <strong>TOTAL SUPPLIER</strong><br>
                                </a>
                            </li>
                            <li>
                                <a href="javascript:void(0)">                                  
                                    <h1><strong>{{$totalSessionPTJ+$totalSessionSupp}}</strong></h1>
                                    <strong>TOTAL USER LOGIN</strong><br>
                                </a>
                            </li>
                        </ul>
                    </div> 
                </div> 
            </div>
          </div>
        </div>
    <div class="row">
<form id="form-search-login" action="{{url("/report/loginSummaryReport")}}" method="post" class="form-horizontal" onsubmit="return true;">
{{ csrf_field() }}
<div class="col-md-12">            
    <div class="widget" class="inset">
        <center><div class="block">                
                    <input id="searchType" name="searchType" type="hidden" value="search_date">                    
                    <strong> <label class="col-md-3 control-label" for="entry_date">Search by Date :</label></strong>
                    <div class="col-md-5">
                        <input class="form-control input-datepicker-close" id ="entry_date" name="entry_date" type="text" 
                               data-date-format="yyyy-mm-dd" placeholder="yyyy-mm-dd" 
                               value="{{$ActualDate}}">
                    </div>
                    
                    <div class="form-group form-actions form-actions-button text-center"> 
                       
                        <div class="col-md-11">
                            </br>
                            <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                        </div>
                    </div>
                
        </div></center>
        <div id="chartContainerStack" style="height: 300px; width: 100%; border-style: groove;"></div>
        </br>
        <div id="chartContainerStackUserType" style="height: 400px; width: 100%; border-style: groove;"></div>
    </div>
</div>
<div class="col-md-12">
    <div class="widget">
        <center><div class="block">
                <strong> <label class="col-md-3 control-label" for="entry_month" >Search by Month :</label></strong>
                <div  class="col-md-5">
                    <select class="form-control"  id ="entry_month" name="entry_month">
                        <option value="00">Please select</option>
                        <option value="01" {{ old('entry_month') == '01' ? 'selected' : '' }}>January</option>
                        <option value="02" {{ old('entry_month') == '02' ? 'selected' : '' }}>February</option>
                        <option value="03" {{ old('entry_month') == '03' ? 'selected' : '' }}>March</option>
                        <option value="04" {{ old('entry_month') == '04' ? 'selected' : '' }}>April</option>
                        <option value="05" {{ old('entry_month') == '05' ? 'selected' : '' }}>May</option>
                        <option value="06" {{ old('entry_month') == '06' ? 'selected' : '' }}>June</option>
                        <option value="07" {{ old('entry_month') == '07' ? 'selected' : '' }}>July</option>
                        <option value="08" {{ old('entry_month') == '08' ? 'selected' : '' }}>August</option>
                        <option value="09" {{ old('entry_month') == '09' ? 'selected' : '' }}>September</option>
                        <option value="10" {{ old('entry_month') == '10' ? 'selected' : '' }}>October</option>
                        <option value="11" {{ old('entry_month') == '11' ? 'selected' : '' }}>November</option>
                        <option value="12" {{ old('entry_month') == '12' ? 'selected' : '' }}>December</option>
                    </select>                   
<!--                    <select class="form-control"  id="entry_year" name="entry_year" />  
                    <option value="00">Please select</option>
                        <option value="2020" {{ old('entry_year') == '2020' ? 'selected' : '' }}>2020</option>
                        <option value="2019" {{ old('entry_year') == '2019' ? 'selected' : '' }}>2019</option> 
                        <option value="2018" {{ old('entry_year') == '2018' ? 'selected' : '' }}>2018</option> 
                      </select> -->
                    <select class="form-control"  id ="entry_year" name="entry_year">
                        @foreach (range( $latest_year, $earliest_year ) as $i)
                        <option value="{{$i}}" @if(old('entry_year') == $i) selected @endif>{{$i}}</option>
                        @endforeach
                    </select>
                </div>
                <div class="form-group form-actions form-actions-button text-center">
                    <div class="col-md-11">
                            </br>
                            <button type="submit" class="btn btn-sm btn-info"><i class="fa fa-search"></i>Search</button>
                        </div>
                </div>            
            </div></center>
        <div id="chartContainerStackUserTypeDaily" style="height: 300px; width: 100%; border-style: groove;"></div>
        </br>
    </div>
    <div class="col-md-12">            
    <div class="widget" class="inset">
        <center><div class="block">                
                    <input id="searchType" name="searchType" type="hidden" value="search_date">               
                    <div class="form-group form-actions form-actions-button text-center">                       
                        <a href="{{ url('/report/login/download') }}/{{ $latest_year }}" target='_blank' id='downloadExcel' class='btn btn-sm btn-info'><i class="fa fa-download"></i>Download to Excel</a>
                      
                    </div>                
        </div></center>
        <div id="chartContainerStackDownload" style="height: 300px; width: 100%; border-style: groove;"></div>
        </br>
    </div>
</div>
</form>
    </div>
    <div class="row">
         @if($dataePHourDetails)
         <div class="col-md-6">           
             <div class="table-responsive" style="background: white;">                
                 <div id="response-msg" class="text-center text-light" colspan="6"></div>
                 <h3 class="widget-content text-left pull-left animation-pullDown">
                     <small>&nbsp;&nbsp;eP Login by Hour : <strong>({{$today}})</strong></small><br>                            
                 </h3> 
                 <table id="datatable-summary-daily-ptj" class="table table-vcenter table-striped">
                     <thead>
                         <tr>
                             <th class="text-center">No.</th>
                             <th class="text-center">Group</th>
                             <th class="text-center">Name</th>
                             <th class="text-center">Time(24Hour)</th>
                             <th class="text-center">Total</th>
                         </tr>
                     </thead>
                     <tbody>
                         @foreach ($dataePHourDetails as $indexKey => $data)
                         <tr>
                             <td class="text-center">{{ ++$indexKey }}</td>
                             <td class="text-center">{{ $data->user_group }}</td>
                             <td class="text-center">{{ $data->user_name }}</td>
                            <td class="text-center">{{ $data->time_login  }}</td> 
                             <td class="text-center">{{ $data->login_total }}</td>
                         </tr>
                         @endforeach
                     </tbody>
                 </table>
             </div>
         </div> 
         @endif
          @if($dataePDailyDetails)
        <div class="col-md-6">           
             <div class="table-responsive" style="background: white;">                
                 <div id="response-msg" class="text-center text-light" colspan="6"></div>
                 <h3 class="widget-content text-left pull-left animation-pullDown">
                     <small>&nbsp;&nbsp;Daily Total eP Login</small><br>                            
                 </h3> 
                 <table id="datatable-summary-daily-supplier" class="table table-vcenter table-striped">
                     <thead>
                         <tr>
                             <th class="text-center">No.</th>
                             <th class="text-center">Group</th>
                             <th class="text-center">Name</th>
                             <th class="text-center">Date</th>
                             <th class="text-center">Total</th>
                         </tr>
                     </thead>
                     <tbody>
                         @foreach ($dataePDailyDetails as $indexKey => $dataeP)
                         <tr>
                             <td class="text-center">{{ ++$indexKey }}</td>
                             <td class="text-center">{{ $dataeP->userGroup }}</td>
                             <td class="text-center">{{ $dataeP->userName }}</td>                             
                             <td class="text-center">{{ $dataeP->LogDate }}</td>
                             <td class="text-center">{{ $dataeP->totalPtjLogin }}</td>
                         </tr>
                       @endforeach
                     </tbody>
                 </table>
             </div>
         </div> 
        @endif
    </div>
 
@endsection

@section('jsprivate')
<script type="text/javascript">

    
    $('#page-container').removeAttr('class');


    App.datatables();
    /* Initialize Datatables */
    $('#datatable-summary-daily-ptj').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']]
    });
    
    $('#datatable-summary-daily-supplier').DataTable({
        columnDefs: [{orderable: false, targets: [0]}],
        pageLength: 5,
        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']]
    });
    
   var miniChartBarOptions = {
                type: 'bar',
                barWidth: 22,
                barSpacing: 12,
                height: '80px',
                tooltipOffsetX: -25,
                tooltipOffsetY: 20,
                barColor: '#555555',
                tooltipPrefix: '+ ',
                tooltipSuffix: ' Sales'
            };
            $('#widget-mini-chart-bar1').sparkline('html', miniChartBarOptions);
            
            
   
</script>
<script>
window.onload = function () {
    
    var today = new Date();
    var dd = today.getDate();
    var mm = today.getMonth()+1; //January is 0!
    var yyyy = today.getFullYear();
    if(dd<10) {
        dd = '0'+dd
    } 

    if(mm<10) {
        mm = '0'+mm
    } 
    today = yyyy + '-' + mm + '-' + dd;
    
    var months    = ['January','February','March','April','May','June','July','August','September','October','November','December'];
    var now       = new Date();
    var thisMonth = months[now.getMonth()]; // getMonth method returns the month of the date (0-January :: 11-December)
    var output = document.getElementById('output');
     
var chart1 = new CanvasJS.Chart("chartContainerStackUserType", {
	title: {
		text: "eP Login Summary Report by User Type - Hourly"+' on '+ {!! json_encode($ActualDate) !!}
	},
	theme: "light2",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
	axisY: {
		title: "Total users Login eP",
                interval : 2000
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries1
	},
	data: [
		{
			type: "stackedColumn",
			name: "Bahagian Perolehan Kerajaan",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat6) !!}
		},{
			type: "stackedColumn",
			name: "Pusat Tanggungjawab (PTJ)",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat5) !!}
		},{
			type: "stackedColumn",
			name: "Ministry",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat2) !!}
		},{
			type: "stackedColumn",
			name: "Factoring Company",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat7) !!}
		},{
			type: "stackedColumn",
			name: "Government Link Companies",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat10) !!}
		},{
			type: "stackedColumn",
			name: "State Government",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat13) !!}
		},{
			type: "stackedColumn",
			name: "Kumpulan PTJ",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat4) !!}
		},{
			type: "stackedColumn",
			name: "Federal Statutory Body",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat11) !!}
		},{
			type: "stackedColumn",
			name: "eP Profile",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJHourlyStat1) !!}
		},{     
                        type: "stackedColumn",
			name: "Supplier",
			showInLegend: true,
                        indexLabel: "#total",
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
			dataPoints : {!! json_encode($dataSupplierHourlyStat) !!}			
		}

	]
});
 
chart1.render();
 
function toggleDataSeries1(e) {
	if (typeof (e.dataSeries1.visible) === "undefined" || e.dataSeries1.visible) {
		e.dataSeries1.visible = false;
	} else {
		e.dataSeries1.visible = true;
	}
	e.chart1.render();
}

// second chart1
var chart = new CanvasJS.Chart("chartContainerStack", {
   
	title: {
		text: "eP Login Summary Report - Hourly"+' on '+{!! json_encode($ActualDate) !!}
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
	axisY: {
		title: "Total users Login eP",
                interval : 2000
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [
		{
			type: "stackedColumn",
			name: "PTJ",
			showInLegend: true,
                        dataPoints : {!! json_encode($dataPTJHourlyStat) !!}
		},{
			type: "stackedColumn",
			name: "Supplier",
			showInLegend: true,			
                        indexLabel: "#total",
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                       dataPoints : {!! json_encode($dataSupplierHourlyStat) !!}
		}

	]
});
 
chart.render();
 
function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;    
	}
	e.chart.render();
}

// latest third chart
var chartUserTypeDaily = new CanvasJS.Chart("chartContainerStackUserTypeDaily", {
	title: {
		text: "eP Login Summary Report by User Type - Daily"+' : '+{!! json_encode($exactMonth) !!}
	},
	theme: "light2",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
	axisY: {
		title: "Total users Login eP",
                interval : 2000
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries1
	},
	data: [
		{
			type: "stackedColumn",
			name: "Bahagian Perolehan Kerajaan",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat6) !!}
		},{
			type: "stackedColumn",
			name: "Pusat Tanggungjawab (PTJ)",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat5) !!}
		},{
			type: "stackedColumn",
			name: "Ministry",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat2) !!}
		},{
			type: "stackedColumn",
			name: "Factoring Company",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat7) !!}
		},{
			type: "stackedColumn",
			name: "Government Link Companies",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat10) !!}
		},{
			type: "stackedColumn",
			name: "State Government",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat13) !!}
		},{
			type: "stackedColumn",
			name: "Kumpulan PTJ",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat4) !!}
		},{
			type: "stackedColumn",
			name: "Federal Statutory Body",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat11) !!}
		},{
			type: "stackedColumn",
			name: "eP Profile",
			showInLegend: true,
			dataPoints : {!! json_encode($dataUserTypePTJDailyStat1) !!}
		},{     
                        type: "stackedColumn",
			name: "Supplier",
			showInLegend: true,
                        indexLabel: "#total",
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
			dataPoints : {!! json_encode($displaySuppLoginDaily) !!}			
		}

	]
});

chartUserTypeDaily.render();
 
function toggleDataSeries1(e) {
	if (typeof (e.dataSeries2.visible) === "undefined" || e.dataSeries2.visible) {
		e.dataSeries2.visible = false;
	} else {
		e.dataSeries2.visible = true;
	}
	e.chartUserTypeDaily.render();
}
 
 // latest fourth chart
var chartDownload = new CanvasJS.Chart("chartContainerStackDownload", {
   
	title: {
		text: "eP Login Summary Report - Monthly"+' on '+{!! json_encode($latest_year) !!}
	},
	theme: "light3",
	animationEnabled: true,
	toolTip:{
		shared: true,
		reversed: true
	},
	axisY: {
		title: "Total users Login eP",
                interval : 100000
	},
	legend: {
		cursor: "pointer",
		itemclick: toggleDataSeries
	},
	data: [
		{
			type: "stackedColumn",
			name: "PTJ",
			showInLegend: true,
                        dataPoints : {!! json_encode($dataptjMonthlyStat) !!}
		},{
			type: "stackedColumn",
			name: "Supplier",
			showInLegend: true,			
                        indexLabel: "#total",
                        indexLabelPlacement: "outside",
                        indexLabelFontSize: 15,
                        indexLabelFontWeight: "bold",
                       dataPoints : {!! json_encode($dataSupplierMonthlyStat) !!}
		}

	]
});
 
chartDownload.render();
 
function toggleDataSeries(e) {
	if (typeof (e.dataSeries.visible) === "undefined" || e.dataSeries.visible) {
		e.dataSeries.visible = false;
	} else {
		e.dataSeries.visible = true;    
	}
	e.chartDownload.render();
}
 
}
</script>

@endsection