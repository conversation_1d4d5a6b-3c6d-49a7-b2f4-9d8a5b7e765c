@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carianform" action="{{url('/find/gfmas')}}/{{$type}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ... ">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

@if($listdata == null)
<div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
                   
                </div>
                <div class="row">
                  <div class="col-sm-6">
                      <p>Tidak dijumpai!</p>
                  </div>
                </div>
            </div>
</div>
@endif

@if($listdata && count($listdata) > 0)
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <!-- Log Block -->
        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>Log</strong> 1GFMAS Info</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td>GFM-020</td>
                            <td class="text-center">MMINF</td>
                            <td class="text-success"><strong>MasterDataMaterial</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-080</td>
                            <td class="text-center">MM503</td>
                            <td class="text-info"><strong>DebitAdviceNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-100</td>
                            <td class="text-center">MM501</td>
                            <td class="text-success"><strong>POContractForGoodsAndServices</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-110</td>
                            <td class="text-center">MM504</td>
                            <td class="text-danger"><strong>FulfillmentReceivingNote</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-120</td>
                            <td class="text-center">MM506</td>
                            <td><strong>PaymentInstruction</strong></td>
                        </tr>
                        <tr>
                            <td>GFM-170</td>
                            <td class="text-center">MM513</td>
                            <td><strong>StopInstruction</strong></td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!-- END Log Content -->
        </div>
        <!-- END Log Block -->
        <ul class="text-info">
            <li>IBReq : Refering to request</li>
            <li>IBRes : Refering to response  </li>
            <li>Click link in column service code to view each payload for transaction type.  </li>
        </ul>
        <div class="table-responsive">
            <table id="basic-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr>
                        <th class="text-center">TRANS ID</th>
                        <th class="text-center">TRANS TYPE</th>
                        <th class="text-center">SERVICE CODE</th>
                        <th class="text-center">TRANS DATE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">STATUS CODE</th>
                        <th class="text-center">STATUS</th>
                        <th class="text-center">REMARKS 1</th>
                        <th class="text-center">REMARKS 2</th>
                        <th class="text-center">REMARKS 3</th>
                    </tr>
                </thead>
                <tbody>
                @foreach ($listdata as $data)
                    <tr>
                        <td class="text-center">
                            <a href="javascript:void(0)" data-toggle="collapse" data-target="#osb_{{$data->trans_id}}" >
                                {{$data->trans_id }}</a>
                        </td>
                        <td class="text-center"><span  data-toggle="tooltip" title="" data-original-title="@if($data->trans_type=='IBRes')Response from 1GFMAS @endif">{{ $data->trans_type }}</span></td>
                        <td class="text-center">{{ $data->service_code }}</td>
                        <td class="text-center">{{ $data->trans_date }}</td>
                        <td class="text-left">{{ App\Services\EPService::$OSB_STATUS[$data->status] }}</td>
                        <td class="text-left">{{ $data->status_code }}</td>
                        <td class="text-left">@include('_shared._infoDetailErrorDescription')</td>
                        <td class="text-center">{{ $data->remarks_1 }}</td>
                        <td class="text-center">{{ $data->remarks_2 }}</td>
                        <td class="text-center">{{ $data->remarks_3 }}</td>
                    </tr>
                @endforeach
                </tbody>
            </table>
        </div>
        
    </div>
    <!-- END List OSB Block -->
    @if($listXml && count($listXml) > 0 )
    @foreach($listXml as $xml)
    <div class="block collapse panel-xml" id="osb_{{$xml->trans_id}}">
        <div class="block-title">
            <h2><i class="fa fa-file-text-o"></i> <strong>XML</strong> Sent to 1GFMAS ({{$xml->service_code}})</h2>
            <small>{{$xml->trans_id}}</small>
            <div class="block-options pull-right">
                <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                    onclick="$(this).parentsUntil('div.panel-xml').parent().hide();" >Close</span>
            </div>
        </div>
        <pre class="line-numbers">
            <code class="language-markup">{{$xml->payload_body}}</code>
        </pre>
    </div>
    @endforeach
    @endif


</div>

@include('_shared._modalDetailInfo')
@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>
<script src="/js/pages/modalDetailInfoDatatable.js"></script>
<script>$(function(){ ModalDetailInfoDatatable.init(); });</script>
@endif

@endsection
