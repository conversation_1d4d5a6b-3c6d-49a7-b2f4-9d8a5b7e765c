<?php

namespace App\Console\Commands\AppSupport;

use Illuminate\Console\Command;
use App\Services\Traits\SupplierFullGrantService;
use App\Migrate\MigrateUtils;
use App\Migrate\AppSupport\SmSupplierUpdateChangedDateToToday;
use Carbon\Carbon;

class HandleSmSupplierUpdateChangedDateToToday extends Command
{
    use SupplierFullGrantService;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'HandleSmSupplierUpdateChangedDateToToday';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handle issue supplier active still not exist sap_vendor_code. Reupdate changed_date to resend apive ';

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        MigrateUtils::logDump(__METHOD__ . ' Starting ' . __FUNCTION__ . ' ...');
        $dtStartTime = Carbon::now();

        $updater = new SmSupplierUpdateChangedDateToToday();

        $updater->fixSupplierChangedDateToToday();

        MigrateUtils::logDump(__METHOD__ . ' Completed   --- Taken Time: ' . MigrateUtils::getTakenTime($dtStartTime)['TakenTime']);
    }
}
