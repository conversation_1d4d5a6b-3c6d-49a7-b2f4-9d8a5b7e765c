@extends('layouts.guest-dash')

@section('content')


    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i></h1>
            <h1><strong> Monitoring: </strong>{{ $listdata['table_name'] }}</h1>
        </div>

        @if (isset($listdata))
            @if ($listdata['data'])
                <div class="table-responsive">
                    <table id="tr-monitoring-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                @foreach ($listdata['header'] as $data)
                                    <th class="text-center">{{ $data }}</th>
                                @endforeach
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($listdata['data'] as $item)
                                <tr>
                                    @foreach ($listdata['body'] as $body)
                                        @if (in_array($body, $listdata['doc_type']))
                                            <td class="text-center">
                                                <a href="{{ url('/find/trans/track/docno/' . $item->$body) }}"
                                                    target="_blank">
                                                    {{ $item->$body }}
                                                </a>
                                            </td>
                                        @elseif($body === 'instance_id')
                                            @if ($item->is_instance_found)
                                                <td class="text-center">
                                                    <a href="{{ url('/bpm/instance/find/?composite_module=default/' . $item->composite_name . '!' . $item->composite_version . '&composite_instance_id=' . $item->$body) }}"
                                                        target="_blank">
                                                        {{ $item->$body }}
                                                    </a>
                                                </td>
                                            @else
                                                <td class="text-center"></td>
                                            @endif
                                        @elseif($body === 'instance_created_date')
                                            <td class="text-center">
                                                @if ($item->is_instance_found)
                                                    {{ $item->$body }}
                                                @endif
                                            </td>
                                        @else
                                            <td class="text-center">{{ $item->$body }}</td>
                                        @endif
                                    @endforeach
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div><h3>No data</h3></div>
            @endif
        @endif
    </div>

@endsection

@section('jsprivate')
    <!-- Load and execute javascript code used only in this page -->
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            App.datatables();

            $('#tr-monitoring-datatable').dataTable({
                order: [
                    [1, "desc"]
                ],
                columnDefs: [],
                pageLength: 20,
                lengthMenu: [
                    [10, 20, 30, 50, -1],
                    [10, 20, 30, 50, 'All']
                ]
            });
        });
        App.datatables();
    </script>
@endsection
