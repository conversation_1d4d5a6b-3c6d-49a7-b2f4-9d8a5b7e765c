@extends('layouts.guest-dash')

@section('header')
<div class="block log-header">
    <div class="row">
        <div class="col-md-5 log-header-title">
            <span>OSB Log Batch File<br /></span>
            <small>Search file name only / Search by Filename & Service Code. </small>
        </div>
        <div class="col-md-6 log-header-menu">
            <a href="{{url('find/osb/log')}}"><span class="{{ Request::is('find/osb/log') ? 'active' : '' }}">Log</span></a> |
            <a href="{{url('find/osb/detail/log')}}"><span class="{{ Request::is('find/osb/detail/log') ? 'active' : '' }}">Log Detail</span></a> |
            <a href="{{url('find/osb/detail-rquid/log')}}"><span class="{{ Request::is('find/osb/detail-rquid/log') ? 'active' : '' }}">Log RQ-UID</span></a> | 
            <a href="{{url('find/osb/batch/file')}}"><span class="{{ Request::is('find/osb/batch/file') ? 'active' : '' }}">Batch File Log</span></a> | 
            <a href="{{url('osb/file/content/search')}}"><span class="{{ Request::is('osb/file/content/search') ? 'active' : '' }}">Find Content File</span></a> |
            <a href="{{url('find/osb/error')}}"><span class="{{ Request::is('find/osb/error') ? 'active' : '' }}">Error Log</span></a> | 
            <a href="{{url('find/1gfmas/ws')}}"><span class="{{ Request::is('find/1gfmas/ws') ? 'active' : '' }}">Log (IGFMAS)</span></a> | 
            <a href="{{url('find/phis/ws')}}"><span class="{{ Request::is('find/phis/ws') ? 'active' : '' }}">Log (PHIS)</span></a> 
        </div>
    </div>
</div>
@endsection


@section('content')
    <div class="content-header">
        <!-- Search Form -->
        <div class="panel panel-body">
            <form action="{{ url('/find/osb/batch/file') }}" method="post">
                {{ csrf_field() }}
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group form-inline">
                            <label class="col-md-3 control-label text-right">Filename<span
                                    style="color: red;">*</span></label>
                            <input type="text" id="fileName" name="fileName" value="{{ $carian }}"
                                class="col-md-6" onfocus="this.select();" placeholder="Batch Filename" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group form-inline">
                            <label class="control-label col-md-3 text-right">Service Code</label>
                            <input type="text" id="serviceCode" name="serviceCode" value="{{ $carian2 }}"
                                class="col-md-6" placeholder="Service Code">
                        </div>
                    </div>
                </div><br/>
                <div class="row">
                    <div class="col-md-6"></div>
                    <div class="col-md-6">
                        <div class="col-md-9">
                            <span class="pull-right"><button type="submit" name="searchlog"
                                    class="btn btn-sm btn-success"><i class="fa fa-search"></i>
                                    Search</button>
                            </span>
                        </div>
                        <div class="col-md-3"></div>
                    </div>
                </div>
            </form>
            <!-- END Search Form -->
        </div>
    </div>

    <!-- Log Block -->
    @if ($objFile != null)

        <div class="block">
            <!-- Log Title -->
            <div class="block-title">
                <h2><i class="fa fa-file-text-o"></i> <strong>{{ $carian }} {{ $carian2 }}</strong> Details
                    Information</h2>
            </div>
            <!-- END Log Title -->

            <!-- Log Content -->
            <div class="table-responsive">
                <table class="table table-bordered table-vcenter">
                    <tbody>
                        <tr>
                            <td width="350">OSB Batch File Created? </td>
                            <td class="text-info">{{ $objFile->is_batch_file }}</td>
                        </tr>
                        <tr>
                            <td width="350">Service Code</td>
                            <td class="text-info">{{ $objFile->service_code }}</td>
                        </tr>
                        <tr>
                            <td>Created Date</td>
                            <td class="text-info">{{ $objFile->created_date }}</td>
                        </tr>
                        <tr>
                            <td>Content File</td>
                            <td class="text-success content-file">

                                <a href="javascript:void(0)" data-toggle="collapse"
                                    data-target="#{{ $objFile->batch_file_id }}">
                                    <strong style="color:forestgreen;">Show</strong></a>

                                &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;

                                <a href='#modal-file-decrypt' id="link_decrypt_file" class='modal-list-data-action'
                                    data-toggle='modal'
                                    data-url='{{ url('/find/osb/decrypt') }}/{{ $objFile->file_name }}'
                                    data-title='Decrypt Content File'>
                                    <strong style="color:forestgreen;">Show Decrypt</strong>
                                </a>

                                @if ($listDataJson != null && count($listDataJson) > 0)
                                    &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                                    <a href='javascript:void(0)' data-toggle="collapse"
                                        data-target="#jsondata_{{ $objFile->batch_file_id }}" data-toggle='tooltip'
                                        data-title='List of File Data in {{ $objFile->file_name }}'>
                                        <strong style="color:forestgreen;">Show Decrypt Data in Table</strong>
                                    </a>
                                @endif

                                @if ($listDataJson != null && count($listDataJson) > 0)
                                    &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                                    <a href='javascript:void(0)' data-toggle="collapse"
                                        data-target="#json_data_{{ $objFile->batch_file_id }}" data-toggle='tooltip'
                                        data-title='List Data in Json Format >> {{ $objFile->file_name }}'>
                                        <strong style="color:forestgreen;">Show Decrypt Data in JSON</strong>
                                    </a>
                                @endif

                                &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;

                                <a href='{{ url('/find/osb/decrypt-to-file') }}/{{ $objFile->file_name }}'
                                    target="_blank" data-toggle='tooltip' data-title='Get Decrypt File'>
                                    <strong style="color:forestgreen;">Get Decrypt File</strong>
                                </a>

                                &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;

                                <a href='{{ url('/find/osb/get-file') }}/{{ $objFile->file_name }}' target="_blank"
                                    data-toggle='tooltip' data-title='Get Decrypt File'>
                                    <strong style="color:forestgreen;">Get File</strong>
                                </a>

                                &nbsp;&nbsp;&nbsp;|&nbsp;&nbsp;&nbsp;
                                @if ($listInvoices != null && count($listInvoices) > 0)
                                    <a href="javascript:void(0)" data-toggle="collapse"
                                        data-target="#invoices_{{ $objFile->batch_file_id }}"
                                        data-title='List Invoices in {{ $objFile->file_name }}' data-toggle='tooltip'>
                                        <strong style="color:forestgreen;">List Invoices</strong></a>
                                @endif
                            </td>
                        </tr>
                        <tr>
                            <td>Checking file successfully success and updated in eP (DI_INTERFACE_LOG)</td>
                            @if (isset($objFileCompletedProcess) && $objFileCompletedProcess != null)
                                <td class="text-info">Process Completed at
                                    {{ $objFileCompletedProcess->changed_date }}</td>
                            @else
                                <td class="text-danger"><strong>No record found! Failed process and updated in
                                        eP</strong>
                                    &nbsp;&nbsp;<a
                                        href="{{ url('triggerProcessFileOSB') }}?file_name={{ $objFile->file_name }}&service_code={{ $objFile->service_code }}"
                                        target="_blank">Trigger Process This File</a>
                                </td>
                            @endif
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- END Log Content -->
            <div class="block collapse panel-xml" id="{{ $objFile->batch_file_id }}">
                <div class="block-title">
                    <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >> {{ $objFile->file_name }}</h2>
                    <div class="block-options pull-right">
                        <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                            onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');">Close</span>
                    </div>
                </div>
                <pre class="line-numbers">
                    <code class="language-markup text-left">{{ $objFile->file_data }}</code>
                </pre>
            </div>

            <!-- List Invoices -->
            <div class="block collapse panel-xml" id="invoices_{{ $objFile->batch_file_id }}">
                <div class="block-title">
                    <h2><i class="fa fa-file-text-o"></i> <strong>List Invoices In </strong> >> {{ $objFile->file_name }}
                    </h2>
                    <div class="block-options pull-right">
                        <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                            onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');">Close</span>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-12">
                        @if ($listInvoices != null && count($listInvoices) > 0)
                            <div class="table-responsive">
                                <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">INVOICE NO.</th>
                                            <th class="text-center">POCO NO.</th>
                                            <th class="text-center">LATEST POCO STATUS</th>
                                            <th class="text-center">POCO STATUS DATE</th>
                                            <th class="text-center">PAYMENT AMOUNT</th>
                                            <th class="text-center">TOTAL PAYMENT AMOUNT</th>
                                            <th class="text-center">PTJ CODE</th>
                                            <th class="text-center">SAP VENDOR CODE</th>
                                            <th class="text-center">SUPPLIER NAME</th>
                                            <th class="text-center">BANK NAME</th>
                                            <th class="text-center">IGFMAS PAYMENT ID</th>
                                            <th class="text-center">PAYMENT REFERENCE NO.</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($listInvoices as $data)
                                            <tr>
                                                <td class="text-center"><a
                                                        href="{{ url('/find/trans/track/docno') }}/{{ $data->invoice_no }}"
                                                        target="_blank">{{ $data->invoice_no }}</a></td>
                                                <td class="text-center">{{ $data->poco_no }}</td>
                                                <td class="text-center">{{ $data->poco_status }}</td>
                                                <td class="text-center">{{ $data->poco_status_date }}</td>
                                                <td class="text-center">{{ $data->payment_amount }}</td>
                                                <td class="text-center">{{ $data->total_payment_amount }}</td>
                                                <td class="text-center"><a
                                                        href="{{ url('/find/orgcode') }}/{{ $data->ptj_code }}"
                                                        target="_blank">{{ $data->ptj_code }}</a></td>
                                                <td class="text-center"><a
                                                        href="{{ url('/find/mofno') }}/{{ $data->supplier_name }}"
                                                        target="_blank">{{ $data->sap_vendor_code }}</a></td>
                                                <td class="text-center"><a
                                                        href="{{ url('/find/byname') }}/?nama_pembekal={{ $data->supplier_name }}"
                                                        target="_blank">{{ $data->supplier_name }}</a></td>
                                                <td class="text-center">{{ $data->bank_name }}</td>
                                                <td class="text-center">{{ $data->{'1gfmas_payment_id'} }}</td>
                                                <td class="text-center">{{ $data->payment_reference_no }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- MODAL: DECRYPT -->
            <div id="modal-file-decrypt" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true"
                style="display: none;">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header text-center">
                            <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">
                                    Title</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                        <i class="fa fa-spinner fa-4x fa-spin"></i>
                                    </div>
                                    <pre class="line-numbers" style="word-break: unset;">
                                                                                                        <code class="text-left language-css" style = "white-space: pre-wrap;display:block;"><div class="osb-detail"></div></code>
                                                                                                    </pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- List JSON Data -->
            <div class="block collapse panel-xml" id="jsondata_{{ $objFile->batch_file_id }}">
                @include('osb.list_file_data_json')
            </div>

            <!-- IDD Data in JSON -->
            <div class="block collapse panel-xml" id="json_data_{{ $objFile->batch_file_id }}">
                @include('osb.idd_data_in_json')
            </div>
        </div>
        <!-- END Log Block -->

    @endif

    @if ($listdata == null || count($listdata) == 0)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Carian : {{ $carian }}</strong></h1>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <p>Carian Log OSB tidak dijumpai!</p>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if ($listdata && count($listdata) > 0)

        <div class="block block-alt-noborder full">
            <!-- List OSB Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Logs Transaction : {{ $carian }}</strong></h1>
                </div>

                @if ($listdata && count($listdata) > 0)
                    @foreach ($listdata as $xml)
                        <div class="block collapse panel-xml"
                            id="{{ $xml->trans_type }}_{{ $xml->service_code }}_{{ $xml->trans_id }}">
                            <div class="block-title">
                                <h2><i class="fa fa-file-text-o"></i> <strong>Content Body</strong> >>
                                    {{ $xml->trans_type }} | ({{ $xml->service_code }}) | {{ $xml->trans_id }}</h2>
                                <div class="block-options pull-right">
                                    <span class="btn btn-alt btn-sm btn-default" id="closeTaskForm"
                                        onclick="$(this).parentsUntil('div.panel-xml').parent().addClass('out'); $(this).parentsUntil('div.panel-xml').parent().removeClass('in');">Close</span>
                                </div>
                            </div>
                            <pre class="line-numbers">
                                                                                <code class="language-markup">{{ $xml->payload_body }}</code>
                                                                            </pre>
                        </div>
                    @endforeach
                @endif

                <div class="table-responsive">
                    <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                            <tr>
                                <th class="text-center">TRANS ID</th>
                                <th class="text-center">TRANS TYPE</th>
                                <th class="text-center">SERVICE CODE</th>
                                <th class="text-center">TRANS DATE</th>
                                <th class="text-center">STATUS</th>
                                <th class="text-center">REMARKS 1</th>
                                <th class="text-center">REMARKS 2</th>
                                <th class="text-center">REMARKS 3</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($listdata as $data)
                                <tr>
                                    <td class="text-center">{{ $data->trans_id }}</td>
                                    <td class="text-center">{{ $data->trans_type }}</td>
                                    <td class="text-center">
                                        <a href="javascript:void(0)" data-toggle="collapse"
                                            data-target="#{{ $data->trans_type }}_{{ $data->service_code }}_{{ $data->trans_id }}">
                                            {{ $data->service_code }}</a>
                                    </td>
                                    <td class="text-center">{{ $data->trans_date }}</td>
                                    <td class="text-left">{{ $data->status_desc }}</td>
                                    <td class="text-center">{{ $data->remarks_1 }}</td>
                                    <td class="text-center">{{ $data->remarks_2 }}</td>
                                    <td class="text-center">@if ($data->trans_type == 'Status-BATCH' && $data->service_code == 'GFM-010')<a href="{{ url('/find/epno') }}/{{ $data->remarks_3 }}" target="_blank">{{ $data->remarks_3 }}</a>@else{{ $data->remarks_3 }}@endif</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
            <!-- END List OSB Block -->
        </div>
    @endif
@endsection

@section('jsprivate')
    <script src="/js/pages/tablesDatatables.js"></script>
    <script>
        $(function() {
            TablesDatatables.init();
        });
    </script>
    <script>
        $(document).ready(function() {
            var APP_URL = {!! json_encode(url('/')) !!}
            $('td.content-file').on("click", 'a', function() {

                $('.spinner-loading').show();
                $('.osb-detail').html('').fadeIn();

                $('#modal-list-data-header').text($(this).attr('data-title'));
                console.log($(this).attr('data-url'));
                $.ajax({
                    url: $(this).attr('data-url'),
                    type: "GET",
                    success: function(data) {
                        $('.spinner-loading').hide();
                        $('.osb-detail').html(data[0]).fadeIn();
                    }
                });

            });
        });
    </script>
@endsection
