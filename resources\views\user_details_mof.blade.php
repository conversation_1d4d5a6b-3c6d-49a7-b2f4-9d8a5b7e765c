@extends('layouts.guest-dash')

@section('header')
<style>
    .modal-dialog {
        width: 1050px !important;
    }
</style>
<!-- Search Form -->
<form id="carianform" action="{{url('/find')}}/{{$type}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="cari" name="cari" value="{{$carian}}" class="form-control" onfocus="this.select();" placeholder="Klik carian di sini ...">
    </div>
</form>
<!-- END Search Form -->
@endsection


@section('content')

@if ($listdata == null)
<div class="content-header">
    <div class="header-section">
        <h1>
            @if($result == 'notfound')
            <i class="gi gi-ban"></i>Carian: {{$carian}}<br>
            <small>Rekod tidak dijumpai!</small>
            @else
            <i class="gi gi-search"></i><PERSON><PERSON><br>
            <small>Masukkan <span class="text-info">MOF No., EP No. , IC No. , Nama <PERSON>, SSM No. </span> pada carian diatas...</small>
            @endif
        </h1>
    </div>
</div>
@endif

@if($listdata != null)
@if(Auth::user()->isCodiUsersEp())
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : {{ $listdata[0]->company_name }}</strong></h1>
    </div>
    <div class="row">
        @if ($isNotAppointedAdmin == true)
        <div class="col-sm-12">
            <div class="notify-alert alert alert-warning alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4>
            </div>
        </div>
        @endif
        <div class="col-sm-3">
            <div class="block">
                <div class="block-title">
                    <h2>Maklumat Syarikat </h2>
                </div>
                <address>
                    <strong>Company Name</strong> : <strong>{{ $listdata[0]->company_name }}
                        @if($isData->is_name_hq_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Company name has special character Non-ASCII"></i>@endif </strong><br />
                    <strong>SSM No </strong> :
                    @if($listdata[0]->business_type === 'I' || $listdata[0]->business_type === 'J' || $listdata[0]->business_type === 'C' || $listdata[0]->business_type === 'F')
                    <a href="{{url('/find/ssmno')}}/{{ $listdata[0]->reg_no }}/{{ App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type] }}" target="blank">{{ $listdata[0]->reg_no }}</a>
                    @else
                    {{ $listdata[0]->reg_no }}
                    @endif
                    <br />
                    <strong>eP No </strong> : {{ $listdata[0]->ep_no }}<br />
                    @if($basicCompInfo)
                    <span><strong>Country Origin </strong> :
                        @if( strlen($basicCompInfo->ssm_company_country) > 0)
                        {{ $basicCompInfo->ssm_company_country }}
                        @if( array_key_exists($basicCompInfo->ssm_company_country, App\Services\EPService::$CPTPP_COUNTRY) == true)
                        - {{App\Services\EPService::$CPTPP_COUNTRY[$basicCompInfo->ssm_company_country]}}
                        <i class="fa fa-info-circle" title='CPTPP'></i> (CPTTP Country)
                        @endif

                        @endif
                        <br />
                    </span>
                    @endif
                    <strong>Business Type </strong> : {{ $listdata[0]->business_type }} &raquo; ({{ App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type] }})<br />
                    <strong>Supplier Type </strong> : {{ $listdata[0]->supplier_type }} &raquo; {{ App\Services\EPService::$SUPPLIER_TYPE[$listdata[0]->supplier_type] }}<br />
                    <strong>Supplier ID </strong> :
                    @if(Auth::user()->isAdvRolesEp())
                    <a target="_blank" href="{{url('/find/supplier/')}}/{{ $listdata[0]->appl_id }}/{{ $listdata[0]->supplier_id }}">{{ $listdata[0]->supplier_id }}</a>
                    @else
                    {{ $listdata[0]->supplier_id }}
                    @endif
                    <br />
                    <strong>Appl ID </strong> : {{ $listdata[0]->latest_appl_id }}<br />
                    <strong>Appl No </strong> : {{ $listdata[0]->appl_no }}<br />
                    <strong>Appl Type </strong> : {{ $listdata[0]->appl_type }} &raquo; {{ App\Services\EPService::$APPL_TYPE[$listdata[0]->appl_type] }}<br />

                </address>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="block">
                <div class="block-title">
                    <h2>Maklumat MOF</h2>
                </div>

                <address>
                    <strong>MOF NO </strong> : {{ $listdata[0]->mof_no }}<br />
                    <strong>Effective Date </strong> : {{ $listdata[0]->ma_eff_date }}<br />
                    <strong>Expired Date </strong> : <span class=" @if($listdata[0]->is_mof_expired) label label-danger @endif">{{ $listdata[0]->ma_exp_date }}</span><br />
                    <strong>Record Status </strong> : {{ $listdata[0]->ma_record_status }}<br />
                    @if($isData->is_mof_error_activate)
                    <i class="gi gi-circle_exclamation_mark text-danger" title="MOF Certificate not activate with latest" style="font-size: 10pt;"></i>
                    <br /> <span class="text-danger bolder">Need to fix MOF expired date! </span>
                    @endif

                    @if($suppMofStatus)
                    <br />
                    <strong>Bumi Status </strong> : {{ $suppMofStatus->bumi_status }}<br />
                    <strong>Registration Type </strong> : {{ $suppMofStatus->type }}<br />
                    @endif


                    <br />
                    <strong>List Category Code</strong> : @if(count($listSupplierCategoryCode) > 0 )
                    <a href="#modal-category-code" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierCategoryCode)}}</a> @else Tiada @endif<br />
                    <br />
                </address>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Application Status</h2>
                </div>
                @if(count($listWorkFlow)>0)
                <strong>Appl ID</strong> : {{ $listWorkFlow[0]->appl_id  }}<br />
                <strong>Appl No</strong> : {{ $listWorkFlow[0]->appl_no  }}<br />
                <strong>Created </strong> : {{ $listWorkFlow[0]->appl_created_date  }} <br />
                <strong>Changed </strong> : {{ $listWorkFlow[0]->appl_change_date  }} <br />
                <strong>Appl Reg Status ID</strong> : {{ $listWorkFlow[0]->reg_status_id  }}<br />
                <strong>Soal Selidik</strong> : {{ App\Services\EPService::$YES_NO[$listWorkFlow[0]->is_questionnaire]  }}<br />
                <strong>Supporting Documents</strong> : {{ App\Services\EPService::$SUPPORTING_DOC_MODE[$listWorkFlow[0]->supporting_doc_mode]  }}<br />
                <strong>Appl Category</strong> : {{ $listWorkFlow[0]->appl_category  }}<br />
                <strong>Appl Is Resubmit</strong> : {{ $listWorkFlow[0]->is_resubmit  }}<br />
                <strong>Appl Original</strong> : {{ $listWorkFlow[0]->original_appl_id  }}<br />
                <strong>Appl Valid SSM</strong> : {{ $listWorkFlow[0]->is_appl_valid_with_ssm  }}<br />
                <strong>Appl No</strong> : {{ $listWorkFlow[0]->appl_no  }}<br />
                <strong>Appl Record Status</strong> : {{ App\Services\EPService::$RECORD_STATUS[$listWorkFlow[0]->record_status]  }} &nbsp;
                @if($listWorkFlow[0]->record_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" title="Ask Technical to review this issue!" style="font-size: 10pt;"></i>@endif<br /><br />
                @endif

                @foreach ($listWorkFlow as $datawf)
                <address>
                    <strong>Is Current</strong> : {{ $datawf->is_current  }} &nbsp; , &nbsp;
                    <strong>Status </strong> : {{ $datawf->wf_status  }}<br />
                    <strong>StatusID </strong> : {{ $datawf->wf_status_id  }}<br />
                    <strong>Created Date</strong> : {{ $datawf->wf_created_date  }}<br />
                </address>
                @endforeach

                @if(isset($listApplSectionReview) && count($listApplSectionReview) > 0)
                <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                @foreach($listApplSectionReview as $key => $objRev )
                <strong>{{$key+1}} : Role Type: {{$objRev->role_type}}</strong> :
                <strong>Recommendation: {{$objRev->recommendation}}</strong> on {{$objRev->remark_date}} <br />
                <strong>Remark:</strong><span style="font-style: italic">{{ $objRev->remark_to_approver  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listApplRejectReason) && count($listApplRejectReason) > 0)
                <strong><span class="bolder">Reject Reason </span></strong><br />
                @foreach($listApplRejectReason as $key => $objRej )
                <strong>{{$key+1}} : on {{$objRev->changed_date}} </strong><br />
                <strong>Remark:</strong><span style="font-style: italic">{{ $objRej->reason_desc  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listRemarksCancelReject) && count($listRemarksCancelReject) > 0)
                <strong><span class="bolder">Remarks </span></strong><br />
                @foreach($listRemarksCancelReject as $objRemark)
                <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listAttachmentCancelReject) && count($listAttachmentCancelReject) > 0)
                <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                @foreach($listAttachmentCancelReject as $objAttReject)
                <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank">{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                @endforeach
                <br /><br />
                @endif
                @if($basicCompInfo && $basicCompInfo->appl_id)
                <div class='widget'>
                    <div id="count-application-inquiry-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div id="count-application-rejected-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
    <div class="block">
        <div class="block-title epss-title-s2">
            <h2><i class="fa fa-users"></i> <strong>Admin Pembekal</strong></h2>
        </div>
        <div class="row">
            <div class="col-sm-12">

                <div class="table-responsive">
                    <table id="" class="table table-striped table-vcenter">
                        <thead>
                            <tr>
                                <th class="text-center">No.</th>
                                <th class="text-center">Name</th>
                                <th class="text-center">Identification No.</th>
                                <th class="text-center">Email</th>
                                <th class="text-center">Role</th>
                                <th class="text-center">Softcert Status</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach ($listdata as $indexKey => $data)
                            @if($data->p_ep_role === 'BASIC_SUPPLIER_ADMIN' || $data->p_ep_role === 'MOF_SUPPLIER_ADMIN' || $data->p_ep_role === 'G2G_ADMIN' ||
                            $data->p_ep_role === 'SUPPLIER_TEMP' || $data->p_ep_role === 'GOVT_SELLER')
                            <tr>
                                <td class="text-center">{{ ++$indexKey }}</td>
                                <td class="text-center">{{ $data->p_name }}</td>
                                <td class="text-center">
                                    @if(Auth::user()->isAdvRolesEp())
                                    <a target="_blank" href="{{url('/find/userpersonnel/')}}/{{ $data->appl_id }}/{{ $data->personnel_id }}">{{ $data->p_identification_no }}</a>
                                    @else
                                    {{ $data->p_identification_no }}
                                    @endif
                                </td>
                                <td class="text-center">{{ $data->p_email }}</td>
                                <td class="text-center">{{ $data->p_ep_role }}</td>
                                <td class="text-center">{{ $data->p_is_softcert }}</td>
                                <td class="text-center">
                                    <!--                                                <button class="btn btn-info btn-xs"
                                                                                       data-toggle="collapse" data-target="#row_{{$data->personnel_id}}" >
                                                                                        Details</button>-->
                                </td>
                            </tr>
                            <tr id="row_{{$data->personnel_id}}" @if(strlen($data->p_ep_role) > 0)class="collapsed" @else class="collapse" @endif>
                                <td class="text-center" colspan="7">
                                    <!-- Customer Addresses Block -->
                                    <div class="block" @if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN')style="background-color: inherit;"@endif>
                                        <div class="row">
                                            @if ($data->is_activate_key == true)
                                            <div class="col-sm-12" class="text-left">
                                                <div class="notify-alert alert alert-success alert-dismissable">
                                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                    <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4>
                                                    Sila semak <a href="javascript:void(0)" class="alert-link">e-mel {{$data->p_email}} di INBOX / SPAM</a> untuk aktifkan login ID.
                                                    <br /> <br />
                                                    <strong>Link Activation </strong> : <a target="_blank" href="{{  $data->link }}">{{ $data->link }}</a> <br />
                                                    <strong>Activation Key </strong> : {{ $data->activation_key }}<br />
                                                    <strong>Status Success Send </strong> : {{ $data->is_sent }} <br />
                                                    <strong>Last Date Changed </strong> : {{ $data->activation_changed_date }} <br />

                                                </div>

                                            </div>
                                            @endif

                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat User Login </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                        <strong>Login ID </strong> : {{ $data->login_id  }}<br />
                                                        <strong>Name </strong> : {{ $data->fullname  }}<br />
                                                        <strong>Email </strong> : {{ $data->email  }}<br />
                                                        <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                                                        <strong>Mobile </strong> : {{ $data->mobile_country }}{{ $data->mobile_area }}{{ $data->mobile_no }}<br />
                                                        <strong>Record Status </strong> : {{ $data->u_record_status }}<br />
                                                        <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->changed_date }} <br />

                                                        <br />
                                                        <strong>Peranan</strong> : <br />
                                                        @if($data->roles && count($data->roles) > 0 )
                                                        @foreach ($data->roles as $role)
                                                        {{ $role->role_code }} <br />
                                                        @endforeach
                                                        @else
                                                        Tiada
                                                        @endif
                                                    </address>
                                                </div>
                                            </div>

                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat Diri Staf Syarikat </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                                                        <strong>Name </strong> : {{ $data->p_name  }}<br />
                                                        <strong>Designation </strong> : {{ $data->p_designation  }}<br />
                                                        <strong>Email </strong> : {{ $data->p_email  }}<br />
                                                        <strong>Role </strong> : {{ $data->p_ep_role }}<br />
                                                        <strong>Mobile </strong> : {{ $data->p_mobile_country }}{{ $data->p_mobile_area }}{{ $data->p_mobile_no }}<br />
                                                        <strong>Race </strong> : {{ $data->race_name }}<br />
                                                        <strong>Identity_Resident Status </strong> : {{ $data->identity_resident_status }}<br />
                                                        <strong>SoftCert Status </strong> : {{ $data->p_is_softcert  }}<br />
                                                        <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                                                        <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                                                        <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                                                        <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Record Status </strong> : {{ $data->p_record_status }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->p_changed_date }} <br />
                                                        <strong>Rev. No. </strong> : {{ $data->p_rev_no }} &nbsp;&nbsp;
                                                        @if($data->p_rev_no == 0)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Possibility problem on view details page in eP.  By default value should be as 1"></i>@endif </strong><br />
                                                        <div id="total-address" class="widget-extra-full">
                                                            <strong>Total Address </strong> :
                                                            <a href='#modal-list-data-total-address' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/find/supplier/address/{{$data->personnel_id}}' data-title='List Personnel Address'>
                                                                {{ $data->total_address }}
                                                            </a>
                                                            @if($data->total_address == 0 && strlen($data->p_ep_role) > 0 ) <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="This required if apply softcert"></i>
                                                            <span style="font-weight: bolder; color:red;">Required apply softcert!</span @endif <br />
                                                        </div>
                                                    </address>
                                                </div>
                                            </div>

                                            @if(count($data->listSoftCert ) > 0)
                                            <div class="col-sm-4">
                                                <div class="block softcert">
                                                    <div class="block-title">
                                                        <h2>SoftCert Request</h2>
                                                    </div>

                                                    @foreach ($data->listSoftCert as $sCert)
                                                    <address class="text-left">
                                                        <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br />
                                                        <strong>SoftCert Provider </strong> : {{ $sCert->softcert_provider  }}<br />
                                                        <strong>Request Mode </strong> : {{ $sCert->request_mode  }}<br />
                                                        <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                                                        @if($sCert->record_status == 1 && $sCert->softcert_provider == 'TG') 
                                                            <a href="https://www.msctrustgate.com/mytrustid/client/cdc_support?token=sign" target="_blank">
                                                                RESEND UPDATE SOFTCERT <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Check on TG Portal if already issued! "></i>
                                                            </a>
                                                            <br />
                                                        @endif
                                                        <strong>Date Created </strong> : {{ $sCert->created_date }} <br />
                                                        <strong>Last Date Changed </strong> : {{ $sCert->changed_date }} <br />
                                                        <strong>Using Free SoftCert</strong> : {{ $sCert->is_free }} <br />
                                                        <strong>Apply Document</strong> : {{ $sCert->type_apply }} <br />
                                                        {{--
                                                                    <strong>Response Status</strong> :  {{  $sCert->response_status }} <br />
                                                        <strong>Request Mode</strong> : {{ $sCert->request_mode }} <br />
                                                        <strong>Remark </strong> : {{ $sCert->remark  }}<br />
                                                        <strong>Reason Code </strong> : {{ $sCert->reason_code  }}<br />
                                                        --}}
                                                        @if(strlen($sCert->cert_serial_no)>0)
                                                        <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                                                        <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') TrustGate @else Digicert @endif<br />
                                                        <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                                                        <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}
                                                        @if($sCert->is_expired != null && $sCert->is_expired == true)
                                                        <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Expired! "></i>
                                                        <span style="font-weight: bolder; color:red;"> EXPIRED </span>@endif
                                                        <br />
                                                        <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                                                        <strong>Cert Last Date Changed </strong> : {{ $sCert->pdc_changed_date }} <br />
                                                        @else
                                                        <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                                                        @endif
                                                    </address>
                                                    @endforeach
                                                </div>
                                            </div>
                                            @endif

                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endif
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@else
<div class="block">
    <div class="block-title panel-heading epss-title-s1">
        <h1><i class="fa fa-building-o"></i> <strong>ePerolehan (Pembekal) : {{ $listdata[0]->company_name }}</strong></h1>
    </div>
    <div class="row">
        @if ($isNotAppointedAdmin == true)
        <div class="col-sm-12">
            <div class="notify-alert alert alert-warning alert-dismissable">
                <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                <h4><i class="fa fa-exclamation-triangle"></i> Admin Pembekal masih belum dilantik!</h4>
            </div>
        </div>
        @endif
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Maklumat Syarikat </h2>
                </div>

                <address>
                    <strong>Company Name</strong> : <strong>{{ $listdata[0]->company_name }}
                        @if($isData->is_name_hq_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Company name has special character Non-ASCII"></i>@endif </strong><br />
                    <strong>SSM No </strong> :
                    @if($listdata[0]->business_type === 'I' || $listdata[0]->business_type === 'J' || $listdata[0]->business_type === 'C' || $listdata[0]->business_type === 'F')
                    <a href="{{url('/find/ssmno')}}/{{ $listdata[0]->reg_no }}/{{ App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type] }}" target="blank">{{ $listdata[0]->reg_no }}</a>
                    @else
                    {{ $listdata[0]->reg_no }}
                    @endif<br />
                    <strong>eP No </strong> : {{ $listdata[0]->ep_no }} @if( Auth::user()->isAdvRolesEp() ) <a target="_blank" class="text-info" style="text-decoration: underline;" href="{{ url('/find/gfmas/apive/') }}/{{ $listdata[0]->ep_no }}"><i class="fa fa-info-circle"></i> Check Apive</a> @endif<br />
                    @if($basicCompInfo)
                    <span><strong>Country Origin </strong> :
                        @if( strlen($basicCompInfo->ssm_company_country) > 0)
                        {{ $basicCompInfo->ssm_company_country }}
                        @if( array_key_exists($basicCompInfo->ssm_company_country, App\Services\EPService::$CPTPP_COUNTRY) == true)
                        - {{App\Services\EPService::$CPTPP_COUNTRY[$basicCompInfo->ssm_company_country]}}
                        <i class="fa fa-info-circle" title='CPTPP'></i> (CPTTP Country)
                        @endif

                        @endif
                        <br />
                    </span>
                    @endif
                    <strong>Business Type </strong> : {{ $listdata[0]->business_type }} &raquo; ({{ App\Services\EPService::$BUSINESS_TYPE[$listdata[0]->business_type] }})<br />
                    <strong>Supplier Type </strong> : {{ $listdata[0]->supplier_type }} &raquo; {{ App\Services\EPService::$SUPPLIER_TYPE[$listdata[0]->supplier_type] }}<br />
                    <strong>Supplier ID </strong> :
                    @if(Auth::user()->isAdvRolesEp())
                    <a target="_blank" href="{{url('/find/supplier/')}}/{{ $listdata[0]->appl_id }}/{{ $listdata[0]->supplier_id }}">{{ $listdata[0]->supplier_id }}</a>
                    <br />
                    <strong>Company Basic ID </strong> : {{ $basicCompInfo->company_basic_id }} <br />
                    @else
                    {{ $listdata[0]->supplier_id }}<br />
                    @endif
                    
                    <strong>Appl ID </strong> : {{ $listdata[0]->latest_appl_id }}<br />
                    <strong>Appl No </strong> : {{ $listdata[0]->appl_no }}<br />
                    <strong>Appl Type </strong> : {{ $listdata[0]->appl_type }} &raquo; {{ App\Services\EPService::$APPL_TYPE[$listdata[0]->appl_type] }}<br />
                    <strong>Establish Date </strong> : {{ $listdata[0]->establish_date }} <br />
                    <strong>Last Date Changed </strong> : {{ $listdata[0]->s_changed_date }} <br />
                    <strong>Local Authority ID </strong> : {{ $listdata[0]->local_authority_id }} <br />
                    <strong>Record Status </strong> : <span class="bolder" @if($listdata[0]->supp_record_status == 9) style="color:red; font-weight:bolder" @endif>{{ $listdata[0]->s_record_status }} </span><br />

                    @if($basicCompInfo)
                    
                    <strong>Address </strong> : <span @if($isData->is_address_hq_non_ascii) style="color:red; font-weight:bolder" @endif >( Address ID: {{ $basicCompInfo->address_id }} )<br />
                        {{ $basicCompInfo->address_1 }}<br />
                        {{ $basicCompInfo->address_2 }} {{ $basicCompInfo->address_3 }}<br />
                        {{ $basicCompInfo->postcode }} {{ $basicCompInfo->city_name }}, {{ $basicCompInfo->district_name }}<br />
                        {{ $basicCompInfo->state_name }}, {{ $basicCompInfo->country_name }}
                    </span>
                    @if($isData->is_address_hq_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Address has special character Non-ASCII"></i>@endif
                    <br /><br />

                    <strong>Phone No: </strong> : {{ $basicCompInfo->phone_country }}{{ $basicCompInfo->phone_area }}{{ $basicCompInfo->phone_no }}
                    @if(strlen(trim($basicCompInfo->phone_no)) == 0)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Phone No. Company is Mandatory. Kindly do a patch data or Ask Supplier update profile"></i>@endif
                    <br />

                    @endif



                    <strong>Fax No: </strong> : {{ $basicCompInfo->fax_country }}{{ $basicCompInfo->fax_area }}{{ $basicCompInfo->fax_no }} <br /> <br />

                    @if($hqGstInfo)
                    <strong>GST Registration No. : </strong> {{ $hqGstInfo->gst_reg_no }}<br />
                    <strong>GST Effective Date : </strong> {{ $hqGstInfo->gst_eff_date }}<br /><br />
                    @else
                    <strong>GST Registration No. : </strong> Not Registered <br /><br />
                    @endif

                    <strong>Items (Approved & Published) </strong> :
                    <div class="supplier-items-catalog" style="display:inline;">
                        <a href='#modal_list_supplier_items' class='modal-list-data-action btn btn-info btn-xs' data-toggle='modal' data-url='/find/items-supplier/{{ $listdata[0]->supplier_id }}' data-title='Query List Items Supplier'>
                            {{ $totalItems }}
                        </a>
                    </div>
                    <br />

                    <br />
                    <strong>SAP Vendor Code</strong> : @if($sapVendorCode){{ $sapVendorCode->sap_vendor_code }}@else Tiada @endif<br />

                    <br />
                    <strong>In Progress Application History</strong> : @if(count($listinProgressSuppProcessAppl) > 0)
                    <a href="#modal-application-inprogress" data-toggle="modal" class="btn btn-info btn-xs">{{ $listinProgressSuppProcessAppl[0]->appl_no }}</a> @else Tiada @endif<br />
                    @if(count($listinProgressSuppProcessAppl) > 0)
                    <strong>Supporting Document</strong> : {{ App\Services\EPService::$SUPPORTING_DOC_MODE[$listinProgressSuppProcessAppl[0]->supporting_doc_mode]  }}
                    <br />
                    @endif

                    <br />
                    <strong>Payment History</strong> : @if($listSuppPayment)
                    <a href="#modal-list-payment-history" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSuppPayment)}}</a> @else Tiada @endif<br /><br />

                    <div class="historyapplid">
                        <a href='#modal_list_history_supplier' class='modal-list-data-action1' data-toggle='modal' data-url='/find/list-transaction/{{ $listdata[0]->supplier_id }}' data-title='List History Appl ID'>
                            <strong style="color:purple;font-weight: bolder;text-decoration: underline;">
                                <i class="fa fa-users"></i>
                                List History Appl ID</strong><br />
                        </a>
                    </div>
                    <br />





                </address>
            </div>
        </div>
        <div class="col-sm-3">
            <div class="col-sm-12">
                <div class="block">

                    <div class="block-title">
                        <h2>Maklumat MOF</h2>
                    </div>

                    <address>
                        <strong>MOF NO </strong> : {{ $listdata[0]->mof_no }}<br />
                        <strong>Effective Date </strong> : {{ $listdata[0]->ma_eff_date }}<br />
                        <strong>Expired Date </strong> : <span class=" @if($listdata[0]->is_mof_expired) label label-danger @endif">{{ $listdata[0]->ma_exp_date }}</span><br />
                        <strong>Record Status </strong> : {{ $listdata[0]->ma_record_status }}
                        @if($isData->is_mof_error_activate)
                        <i class="gi gi-circle_exclamation_mark text-danger" title="MOF Certificate not activate with latest" style="font-size: 10pt;"></i>
                        <br /> <span class="text-danger bolder">Need to fix MOF expired date! </span>
                        @endif
                        <br />
                        @if($suppMofStatus)
                        <br />
                        <strong>Bumi Status </strong> : {{ $suppMofStatus->bumi_status }}<br />
                        <strong>Registration Type </strong> : {{ $suppMofStatus->type }}<br />
                        @endif


                        <br />
                        <strong>Cert Mof</strong> : <br />
                        @if($listSuppMofVirtCert && count($listSuppMofVirtCert) > 0)
                        @foreach ($listSuppMofVirtCert as $cerVirt)
                        {{ $cerVirt->cert_serial_no  }} ({{ $cerVirt->cert_type  }}) #{{ $cerVirt->appl_id  }}
                        <a href="{{url('/download/mofcert')}}/{{$cerVirt->mof_cert_id}}" target="_blank">Download</a>
                        @if($cerVirt->appl_id != $listdata[0]->latest_appl_id)
                        <span class="text-danger">(Data Fix!) Appl ID tidak sama</span>@endif
                        <br />
                        @endforeach
                        @else
                        Tiada
                        @endif

                        <br />
                        <strong>List branch</strong> : @if(count($listSupplierBranch) > 0 )
                        <a href="#modal-list-branch" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierBranch)}}</a>
                        @if($isData->is_address_branch_non_ascii || $isData->is_branch_name_non_ascii)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Branch Name or Address has special character Non-ASCII"></i>@endif
                        @else Tiada @endif<br />

                        <br />
                        <strong>List Bank</strong> : @if($listSupplierBank)
                        <a href="#modal-list-bank" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierBank)}}</a>
                        @foreach($listSupplierBank as $bank) @if($bank->bank_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" style="font-size: 10pt;" title="Bank is not active in 1GFMAS"></i> @break @endif @endforeach
                        @else Tiada @endif<br />

                        <br />
                        <strong>List Category Code</strong> : @if(count($listSupplierCategoryCode) > 0 )
                        <a href="#modal-category-code" data-toggle="modal" class="btn btn-info btn-xs">{{count($listSupplierCategoryCode)}}</a> @else Tiada @endif<br />
                        <br />

                        <!--Akmal add link to verify Latest Pending Transaction-->
                        @if( Auth::user()->isAdvRolesEp() )
                        <div class="ptransact">
                            <a class="modal-list-data-action" href="{{url('/find/sm/verifydel')}}/?supp_id={{ $listdata[0]->supplier_id }}" target='_blank'>
                                <strong style="color:purple;font-weight: bolder;text-decoration: underline;">
                                    <i class="fa fa-info-circle"></i>
                                    Total Pending Transaction</strong><br />
                            </a>
                        </div>
                        @endif
                        <!--                                <div class="ptransact">
                                                            <a href='#modal-pendingtransact_'
                                                            class='modal-list-data-action'
                                                            data-toggle='modal'  
                                                            data-url='/find/pending-transaction/{{ $listdata[0]->supplier_id }}'
                                                            data-title='Query List Pending Transaction'>
                                                                <strong style="color:purple;font-weight: bolder;text-decoration: underline;">
                                                                    <i class="fa fa-info-circle"></i> 
                                                                Total Pending Transaction</strong><br />
                                                            </a>
                                                        </div>-->
                        <br />
                        <div class="pnlTransSuppCurYear">
                            <a href='#modal_list_supplier_transactions' class='modal-list-data-action' data-toggle='modal' data-url='/find/list-transaction/{{ $listdata[0]->supplier_id }}/{{Carbon\Carbon::now()->format('Y')}}' data-title='List Transaction Current Year'>
                                <strong style="color:purple;font-weight: bolder;text-decoration: underline;">
                                    <i class="fa fa-list"></i>
                                    List Transaction Current Year</strong><br />
                            </a>
                        </div>
                        <br />




                    </address>
                </div>
            </div>
            @if($supplierDisciplinearyAction != null)
            <div class="col-sm-12">
                <div class="block">

                    <div class="block-title">
                        <h2>Disciplinary Action</h2>
                    </div>
                    <address>
                        <strong>ID</strong> : {{ $supplierDisciplinearyAction->disciplinary_action_id }}<br />
                        <strong>Created Date</strong> : {{ $supplierDisciplinearyAction->created_date }}<br />
                        <strong>Start Date </strong> : {{ $supplierDisciplinearyAction->start_date}}<br />
                        <strong>End Date </strong> : {{ $supplierDisciplinearyAction->end_date}}<br />
                        <strong>Remark</strong> : {{ $supplierDisciplinearyAction->remark}}<br />
                        @php 
                            $dateNow = Carbon\Carbon::now();
                            $endDateDisc = Carbon\Carbon::parse($supplierDisciplinearyAction->end_date);
                        @endphp
                        </br />
                        @if($dateNow->gt($endDateDisc))
                            <i class="gi gi-circle_ok text-info"></i>
                            <strong class="text-info">This supplier is no longer under disciplinary action. There is no issue with activating the supplier's record status if needed</strong>
                        @else
                            <i class="gi gi-circle_exclamation_mark text-danger"></i>
                            <strong class="text-danger">This supplier is still under disciplinary action. Please do not activate the supplier's record status.</strong>
                        @endif
                    </address>
                </div>
            </div>
            @endif 
            
        </div>
        @if($basicCompInfo)
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Business Network</h2>
                </div>

                <address>
                    <strong>Federal? </strong> : {{ $basicCompInfo->is_with_federal  }}<br />
                    <strong>State? </strong> : {{ $basicCompInfo->is_with_state  }}<br />
                    <strong>Local Council? </strong> : {{ $basicCompInfo->is_with_statutory  }}<br />
                    <strong>GLC? </strong> : {{ $basicCompInfo->is_with_glc  }}<br />
                    <strong>Others? </strong> : {{ $basicCompInfo->is_with_others  }}<br />
                </address>
            </div>
        </div>
        @endif

        @if(count($listWorkFlow) > 0)
        <div class="col-sm-3">
            <div class="block">

                <div class="block-title">
                    <h2>Application Status</h2>
                </div>
                @if(count($listWorkFlow)>0)
                <strong>Appl ID</strong> : {{ $listWorkFlow[0]->appl_id  }}<br />
                <strong>Appl No</strong> : <a href="#modal-application-tracking-diary" data-toggle="modal" class="btn btn-info btn-xs">{{ $listWorkFlow[0]->appl_no  }}</a><br />
                <strong>Created </strong> : {{ $listWorkFlow[0]->appl_created_date  }} <br />
                <strong>Changed </strong> : {{ $listWorkFlow[0]->appl_change_date  }} <br />
                <strong>Appl Status </strong> : ({{$listWorkFlow[0]->appl_status_id}}) - {{ $listWorkFlow[0]->appl_status  }} <br />
                <strong>Appl Reg Status ID</strong> : {{ $listWorkFlow[0]->reg_status_id  }}<br />
                <strong>Soal Selidik</strong> : {{ App\Services\EPService::$YES_NO[$listWorkFlow[0]->is_questionnaire]  }}<br />
                <strong>Supporting Documents</strong> : {{ App\Services\EPService::$SUPPORTING_DOC_MODE[$listWorkFlow[0]->supporting_doc_mode]  }}<br />
                <strong>Appl Category</strong> : {{ $listWorkFlow[0]->appl_category  }}<br />
                <strong>Appl Is Resubmit</strong> : {{ App\Services\EPService::$YES_NO[$listWorkFlow[0]->is_resubmit]  }}<br />
                <strong>Appl Original</strong> : {{ $listWorkFlow[0]->original_appl_id  }}<br />
                <strong>Appl Valid SSM</strong> : {{ App\Services\EPService::$YES_NO[$listWorkFlow[0]->is_appl_valid_with_ssm]  }}<br />
                <strong>Appl Record Status</strong> : {{ App\Services\EPService::$RECORD_STATUS[$listWorkFlow[0]->record_status]  }} &nbsp;
                @if($listWorkFlow[0]->record_status == 9) <i class="gi gi-circle_exclamation_mark text-danger" title="Ask Technical to review this issue!" style="font-size: 10pt;"></i>@endif<br /><br />
                @endif
                <div class="block">
                    @foreach ($listWorkFlow as $datawf)
                    <address>
                        <strong>Is Current</strong> : {{ $datawf->is_current  }} &nbsp; , &nbsp;
                        <strong>Status </strong> : {{ $datawf->wf_status  }}<br />
                        <strong>StatusID </strong> : {{ $datawf->wf_status_id  }}<br />
                        <strong>Created Date</strong> : {{ $datawf->wf_created_date  }}<br />
                    </address>
                    @endforeach
                </div>

                @if(isset($listApplSectionReview) && count($listApplSectionReview) > 0)
                <strong><span class="bolder">Remarks Application Section Review </span></strong><br />
                @foreach($listApplSectionReview as $key => $objRev )
                <strong>{{$key+1}} : Role Type: {{$objRev->role_type}}</strong> :
                <strong>Recommendation: {{$objRev->recommendation}}</strong> on {{$objRev->remark_date}} <br />
                <strong>Remark:</strong><span style="font-style: italic">{{ $objRev->remark_to_approver  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listApplRejectReason) && count($listApplRejectReason) > 0)
                <strong><span class="bolder">Reject Reason </span></strong><br />
                @foreach($listApplRejectReason as $key => $objRej )
                <strong>{{$key+1}} : on {{$objRev->changed_date}} </strong><br />
                <strong>Remark:</strong><span style="font-style: italic">{{ $objRej->reason_desc  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listRemarksCancelReject) && count($listRemarksCancelReject) > 0)
                <strong><span class="bolder">Remarks </span></strong><br />
                @foreach($listRemarksCancelReject as $objRemark)
                <strong>{{$objRemark->doc_type}}</strong> : <span style="font-style: italic">{{ $objRemark->remark  }}</span> <br />
                @endforeach
                <br /><br />
                @endif

                @if(isset($listAttachmentCancelReject) && count($listAttachmentCancelReject) > 0)
                <strong><span class="bolder">Lampiran Cancellation or Rejection </span></strong><br />
                @foreach($listAttachmentCancelReject as $objAttReject)
                <strong><a href="{{url('/download/attachment/cancel-reject/')}}/{{$objAttReject->attachment_id}}" target="_blank">{{$objAttReject->doc_type}} - {{$objAttReject->file_name}}</a> </strong>
                @endforeach
                <br /><br />
                @endif
                @if($basicCompInfo && $basicCompInfo->appl_id)
                <div class='widget'>
                    <div id="count-application-inquiry-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                <div class='widget'>
                    <div id="count-application-rejected-{{$basicCompInfo->appl_id}}">
                        <div class="text-center"><i class="fa fa-spinner fa-4x fa-spin"></i></div>
                    </div>
                </div>
                @endif
            </div>
        </div>
        @endif
    </div>

    @if(isset($listPendingTransaction) && count($listPendingTransaction) > 0)
    <div id="pendingtransact_{{$listdata[0]->supplier_id }}" class="block collapse">
        <div class="block-title epss-title-s2">
            <h2><i class="fa fa-users"></i> <strong>List Pending Transaction</strong></h2>
        </div>
        <div class="row">
            <div class="col-sm-12">
                <div class="table-responsive">
                    <table id="basic-datatable3" class="table table-bordered table-vcenter">
                        <thead>
                            <tr>

                                <th class="text-center">Module</th>
                                <th class="text-center">Total Pending Transaction</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($listPendingTransaction as $indexKey => $data)
                            <tr>


                                <td class="text-center">{{ $data['transaction'] }}</td>
                                <td class="text-center">{{ $data['total'] }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="block">
        <div class="block-title epss-title-s2">
            <h2><i class="fa fa-users"></i> <strong>Jumlah Pengguna Pembekal : {{ count($listdata) }}</strong></h2>
        </div>
        <div class="row">
            <div class="col-sm-12">

                <div class="table-responsive">
                    <table id="" class="table table-striped table-vcenter">
                        <thead>
                            <tr>
                                <th class="text-center">No.</th>
                                <th class="text-center">Name</th>
                                <th class="text-center">Identification No.</th>
                                <th class="text-center">Email</th>
                                <th class="text-center">Role</th>
                                <th class="text-center">Softcert Status</th>
                                <th class="text-center"></th>
                            </tr>
                        </thead>

                        <tbody>
                            @foreach ($listdata as $indexKey => $data)
                            <tr>
                                <td class="text-center">{{ ++$indexKey }}</td>
                                <td class="text-center">{{ $data->p_name }}</td>
                                <td class="text-center">
                                    @if(Auth::user()->isAdvRolesEp())
                                    <a target="_blank" href="{{url('/find/userpersonnel/')}}/{{ $data->appl_id }}/{{ $data->personnel_id }}">{{ $data->p_identification_no }}</a>
                                    @else
                                    {{ $data->p_identification_no }}
                                    @endif
                                </td>
                                <td class="text-center">{{ $data->p_email }}</td>
                                <td class="text-center">{{ $data->p_ep_role }}
                                    @if($data->p_ep_role != null && $data->user_id != null)
                                    @if(Auth::user()->isPatcherRolesEp())
                                    <a class="btn btn-warning btn-xs" href="{{url('/find/userpersonnel-sync-role')}}/{{ $data->personnel_id }}" target="_blank">
                                        Sync Role</a>
                                    @endif
                                    @endif
                                </td>
                                <td class="text-center">{{ $data->p_is_softcert }}</td>
                                <td class="text-center">

                                    <button class="btn btn-info btn-xs" data-toggle="collapse" data-target="#row_{{$data->personnel_id}}">
                                        Details</button>
                                </td>
                            </tr>
                            <tr id="row_{{$data->personnel_id}}" @if(strlen($data->p_ep_role) > 0)class="collapsed" @else class="collapse" @endif>
                                <td class="text-center" colspan="7">
                                    <!-- Customer Addresses Block -->
                                    <div class="block" @if($data->p_ep_role == 'MOF_SUPPLIER_ADMIN')style="background-color: inherit;"@endif>
                                        <div class="row">
                                            @if ($data->is_activate_key == true)
                                            <div class="col-sm-12" class="text-left">
                                                <div class="notify-alert alert alert-success alert-dismissable">
                                                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                                                    <h4><i class="fa fa-exclamation-triangle"></i> Pembekal masih belum aktifkan aktivation link login ID</h4>
                                                    Sila semak <a href="javascript:void(0)" class="alert-link">e-mel {{$data->p_email}} di INBOX / SPAM</a> untuk aktifkan login ID.
                                                    <br /> <br />
                                                    <strong>Link Activation </strong> : <a target="_blank" href="{{  $data->link }}">{{ $data->link }}</a> <br />
                                                    <strong>Activation Key </strong> : {{ $data->activation_key }}<br />
                                                    <strong>Status Success Send </strong> : {{ $data->is_sent }} <br />
                                                    <strong>Last Date Changed </strong> : {{ $data->activation_changed_date }} <br />

                                                </div>

                                            </div>
                                            @endif

                                            <div class="col-sm-4">
                                                <div class="block">
                                                    <div class="block-title">
                                                        <h2>Maklumat User Login </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>User ID </strong> : {{ $data->user_id  }}<br />
                                                        <strong>Login ID </strong> : <a class="" href="{{url('find/userlogin')}}?login_id={{ $data->login_id  }}" target="_blank">{{ $data->login_id  }}</a><br />
                                                        <strong>Name </strong> : {{ $data->fullname  }}<br />
                                                        <strong>Email </strong> : {{ $data->email  }}<br />
                                                        <strong>ICNO </strong> : {{ $data->identification_no  }}<br />
                                                        <strong>Mobile </strong> : {{ $data->mobile_country }}{{ $data->mobile_area }}{{ $data->mobile_no }}<br />
                                                        <strong>Record Status </strong> : {{ $data->u_record_status }}<br />
                                                        <strong>Last Login Date </strong> : {{ $data->login_date }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->changed_date }} <br />

                                                        <br />
                                                        <strong>Peranan</strong> : <br />
                                                        @if($data->roles && count($data->roles) > 0 )
                                                        @foreach ($data->roles as $role)
                                                        {{ $role->role_code }} <br />
                                                        @endforeach
                                                        @else
                                                        Tiada
                                                        @endif
                                                    </address>
                                                </div>
                                            </div>

                                            <div class="col-sm-4">
                                                <div class="block maklumat-diri">
                                                    <div class="block-title">
                                                        <h2>Maklumat Diri Staf Syarikat </h2>
                                                    </div>
                                                    <address class="text-left">
                                                        <strong>Personal ID </strong> : {{ $data->personnel_id  }}<br />
                                                        <strong>Name </strong> : {{ $data->p_name  }}<br />
                                                        <strong>Designation </strong> : {{ $data->p_designation  }}<br />
                                                        <strong>Email </strong> : {{ $data->p_email  }}<br />
                                                        <strong>Role </strong> : {{ $data->p_ep_role }}<br />
                                                        <strong>Mobile </strong> : {{ $data->p_mobile_country }}{{ $data->p_mobile_area }}{{ $data->p_mobile_no }}<br />
                                                        <strong>Race </strong> : {{ $data->race_name }}<br />
                                                        <strong>Identity_Resident Status </strong> : {{ $data->identity_resident_status }}<br />
                                                        <strong>SoftCert Status </strong> : {{ $data->p_is_softcert  }}<br />
                                                        <strong>Is Equity Owner? </strong> : {{ $data->is_equity_owner  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contract Signer? </strong> : {{ $data->is_contract_signer  }}<br />
                                                        <strong>Is Authorized? </strong> : {{ $data->is_authorized  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Contact Person? </strong> : {{ $data->is_contact_person  }}<br />
                                                        <strong>Is Management? </strong> : {{ $data->is_mgt  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Is Director? </strong> : {{ $data->is_director  }}<br />
                                                        <strong>Is Bumi? </strong> : {{ $data->is_bumi  }}&nbsp;&nbsp;,&nbsp;&nbsp;
                                                        <strong>Record Status </strong> : {{ $data->p_record_status }}<br />
                                                        <strong>Last Date Changed </strong> : {{ $data->p_changed_date }} <br />
                                                        <strong>Rev. No. </strong> : {{ $data->p_rev_no }} &nbsp;&nbsp;
                                                        @if($data->p_rev_no == 0)<i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Possibility problem on view details page in eP.  By default value should be as 1"></i>@endif </strong><br />
                                                        <div id="total-address" class="widget-extra-full">
                                                            <strong>Total Address </strong> :
                                                            <a href='#modal-list-data-total-address' class='modal-list-data-action label label-danger' data-toggle='modal' data-url='/find/supplier/address/{{$data->personnel_id}}' data-title='List Personnel Address'>
                                                                {{ $data->total_address }}
                                                            </a>
                                                            @if($data->total_address == 0 && strlen($data->p_ep_role) > 0 ) <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="This required if apply softcert"></i>
                                                            <span style="font-weight: bolder; color:red;">Required apply softcert!</span @endif <br />
                                                        </div>
                                                    </address>
                                                </div>
                                            </div>

                                            @if(count($data->listSoftCert ) > 0)
                                            <div class="col-sm-4">
                                                <div class="block softcert">
                                                    <div class="block-title">
                                                        <h2>SoftCert Request</h2>
                                                    </div>

                                                    <a href='#modal-osbdetail-spki' class='pull-left modal-list-data-action' data-toggle='modal' data-url='/find/success-signing/{{$data->user_id}}/SPKI' data-title='Last Successful Signing' style="color:darkblue;font-weight: bolder;text-decoration: underline;">
                                                        <i class="fa fa-send"></i>
                                                        Last Successful Signing
                                                    </a>
                                                    <br /><br />

                                                    @foreach ($data->listSoftCert as $sCert)



                                                    <address class="text-left">
                                                        <strong>SoftCert Request ID </strong> : {{ $sCert->softcert_request_id  }}<br />
                                                        @if($sCert->softcert_provider == 'TG' && Auth::user()->isAdvRolesEp() )
                                                        <a target='_blank' style="color:darkblue;font-weight: bolder;text-decoration: underline;" href="https://digitalid.msctrustgate.com/v2/ePRA/request/detail_request?requestid={{$sCert->softcert_request_id}}">
                                                            Integrate Trustgate Portal</a>
                                                        <br />
                                                        @endif
                                                        <strong>SoftCert Provider </strong> : {{ $sCert->softcert_provider  }}<br />
                                                        <strong>Request Mode </strong> : {{ $sCert->request_mode  }}<br />
                                                        <strong>Record Status </strong> : {{ $sCert->record_status }}<br />
                                                        @if($sCert->record_status == 1 && $sCert->softcert_provider == 'TG' && $data->p_is_softcert == 3) 
                                                            <a target="_blank" href="https://www.msctrustgate.com/mytrustid/client/cdc_support?token=sign" 
                                                                style="color:darkblue;font-weight: bolder;text-decoration: underline;" >
                                                                RESEND UPDATE SOFTCERT <i class="gi gi-circle_exclamation_mark  text-info" style="font-size: 10pt;" title="Check on TG Portal if already issued! "></i>
                                                            </a>
                                                            <br />
                                                        @endif
                                                        <strong>Date Created </strong> : {{ $sCert->created_date }} <br />
                                                        <strong>Last Date Changed </strong> : {{ $sCert->changed_date }} <br />
                                                        <strong>Using Free SoftCert</strong> : {{ $sCert->is_free }} <br />
                                                        <strong>Apply Document</strong> : {{ $sCert->type_apply }} <br />
                                                        {{--
                                                                    <strong>Response Status</strong> :  {{  $sCert->response_status }} <br />
                                                        <strong>Request Mode</strong> : {{ $sCert->request_mode }} <br />
                                                        <strong>Remark </strong> : {{ $sCert->remark  }}<br />
                                                        <strong>Reason Code </strong> : {{ $sCert->reason_code  }}<br />
                                                        --}}
                                                        @if(strlen($sCert->cert_serial_no)>0)
                                                        <strong>Cert Serial NO. </strong> : {{ $sCert->cert_serial_no  }}<br />
                                                        <strong>Cert Issuer </strong> : @if($sCert->cert_issuer=='T') <a class="" href="{{url('/spki/trustgate/challengequestion')}}?ic_no={{ $data->identification_no }}&ep_no={{ $listdata[0]->ep_no }}" target="_blank" style="color:darkblue;font-weight: bolder;text-decoration: underline;">TrustGate</a>
                                                        @else <a class="" href="{{url('/spki/digicert/challengequestion')}}?ic_no={{ $data->identification_no }}&ep_no={{ $listdata[0]->ep_no }}" target="_blank" style="color:darkblue;font-weight: bolder;text-decoration: underline;">Digicert</a> @endif<br />
                                                        <strong>Cert Valid From </strong> : {{ $sCert->valid_from  }}<br />
                                                        <strong>Cert Valid To</strong> : {{ $sCert->valid_to  }}
                                                        @if($sCert->is_expired != null && $sCert->is_expired == true)
                                                        <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Expired! "></i>
                                                        <span style="font-weight: bolder; color:red;"> EXPIRED </span>@endif
                                                        <br />
                                                        <strong>Cert Record Status </strong> : {{ $sCert->pdc_record_status }}<br />
                                                        <strong>Cert Last Date Changed </strong> : {{ $sCert->pdc_changed_date }} <br />
                                                        @else

                                                        @if($sCert->is_success_SPK020 > 0)
                                                        <a href='#modal-osbdetail-spki' class='modal-list-data-action' data-toggle='modal' data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-020/' data-title='Check request softcert to SPKI'>
                                                            <strong style="color:forestgreen;">Check request softcert to SPKI</strong><br />
                                                        </a>
                                                        @endif
                                                        @if($sCert->is_success_SPK010 > 0)
                                                        <a href='#modal-osbdetail-spki' class='modal-list-data-action' data-toggle='modal' data-url='/find/osblog/{{ $sCert->softcert_request_id }}/SPK-010/' data-title='Check update softcert from SPKI'>
                                                            <strong style="color:forestgreen;">Check update softcert from SPKI</strong><br />
                                                        </a>
                                                        <span style="font-style: italic">{{ $sCert->remark }}</span><br />
                                                        @endif

                                                        <strong style="color:lawngreen;font-weight: bolder;">There is no digital certificate on this request.</strong><br />

                                                        @endif
                                                    </address>
                                                    @endforeach
                                                </div>
                                            </div>
                                            @endif

                                        </div>
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
</div>
@endif

<!-- MODAL: Application In Progress -->
<div id="modal-application-inprogress" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>In Progress Application History: (@if(count($listinProgressSuppProcessAppl) > 0){{ $listinProgressSuppProcessAppl[0]->appl_no }}@endif)</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        @if($listinProgressSuppProcessAppl != null && count($listinProgressSuppProcessAppl) > 0)
                        <div class="table-responsive">
                            <table class="table table-bordered table-vcenter">
                                <tbody>
                                    <tr>
                                        <td>Appl ID</td>
                                        <td class=""><strong><a href="{{url('/find/appl-id')}}/{{ $listinProgressSuppProcessAppl[0]->appl_id }}"  target="_blank">{{ $listinProgressSuppProcessAppl[0]->appl_id }}</a></strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl Type</td>
                                        <td class=""><strong>{{ $listinProgressSuppProcessAppl[0]->appl_type }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl No.</td>
                                        <td class="text-success"><strong><a href="{{url('/find/trans/track/docno/')}}/{{ $listinProgressSuppProcessAppl[0]->appl_no }}" target="_blank">{{ $listinProgressSuppProcessAppl[0]->appl_no }}</a></strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl Status</td>
                                        <td class=""><strong>{{ $listinProgressSuppProcessAppl[0]->appl_status }} ({{ $listinProgressSuppProcessAppl[0]->appl_status_id }})</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Is Active Appl</td>
                                        <td class=""><strong>{{ $listinProgressSuppProcessAppl[0]->is_active_appl }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl Created By</td>
                                        <td><strong>{{ $listinProgressSuppProcessAppl[0]->appl_created_by }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl Changed Date</td>
                                        <td><strong>{{ $listinProgressSuppProcessAppl[0]->changed_date }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Is Resubmit</td>
                                        <td><strong>{{ $listinProgressSuppProcessAppl[0]->is_resubmit }}</strong></td>
                                    </tr>
                                    @if($facilityInfoInProgress != null && count($facilityInfoInProgress) > 0)
                                    <tr>
                                        <td>Paid Up Capital</td>
                                        <td><strong>{{ $facilityInfoInProgress[0]->paid_up_capital }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Paid Up Capital SSM</td>
                                        <td><strong>{{ $facilityInfoInProgress[0]->paid_up_capital_ssm }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Facility Type</td>
                                        <td><strong>{{ $facilityInfoInProgress[0]->facility_type }}</strong></td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </div>
                        @endif
                        <div class="modal-header text-left">
                            <h2 class="modal-title">List Workflow SM</h2>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">WF ID</th>
                                        <th class="text-center">WF Created Date</th>
                                        <th class="text-center">WF Changed Date</th>
                                        <th class="text-center">WF Status</th>
                                        <th class="text-center">WF Is Current</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listinProgressSuppProcessAppl as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->workflow_status_id }}</td>
                                        <td class="text-center">{{ $data->wf_created_date }}</td>
                                        <td class="text-center">{{ $data->wf_changed_date }}</td>
                                        <td class="text-center">({{ $data->wf_status_id }}) - {{ $data->wf_status }}</td>
                                        <td class="text-center">{{ $data->is_current }}</td>

                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($listInProgressSuppTrackDiary != null && count($listInProgressSuppTrackDiary) > 0)
                        <div class="modal-header text-left">
                            <h2 class="modal-title">List Tracking Diary</h2>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-2" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Doc Type</th>
                                        <th class="text-center">Doc No.</th>
                                        <th class="text-center">Description</th>
                                        <th class="text-center">Action Date</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listInProgressSuppTrackDiary as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ $data->tracking_diary_id}}</td>
                                        <td class="text-center">{{ $data->doc_type }}</td>
                                        <td class="text-center">{{ $data->doc_no }}</td>
                                        <td class="text-center">{{ $data->action_desc }}</td>
                                        <td class="text-center">{{ $data->actioned_date }}</td>
                                        <td class="text-center">{{ $data->status_id }} - {{ $data->status_name }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- MODAL: Application In Progress -->
<div id="modal-application-tracking-diary" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>List Workflow & Tracking Diary: (@if(count($listSuppTrackDiary) > 0){{ $listSuppTrackDiary[0]->doc_no }}@endif)</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        @if($listWorkFlow != null && count($listWorkFlow) > 0)
                        <div class="table-responsive" style="display:none;">
                            <table class="table table-bordered table-vcenter">
                                <tbody>
                                    <tr>
                                        <td class="title">Appl ID</td>
                                        <td class=""><strong>{{ $listWorkFlow[0]->appl_id }}</strong></td>
                                        <td>Appl Type</td>
                                        <td class=""><strong>{{ $listWorkFlow[0]->appl_type }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl No.</td>
                                        <td class="text-success"><strong><a href="{{url('/find/trans/track/docno/')}}/{{ $listWorkFlow[0]->appl_no }}" target="_blank">{{ $listWorkFlow[0]->appl_no }}</a></strong></td>
                                        <td>Appl Status</td>
                                        <td class=""><strong>{{ $listWorkFlow[0]->appl_status }} ({{ $listWorkFlow[0]->appl_status_id }})</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Is Active Appl</td>
                                        <td class=""><strong>{{ $listWorkFlow[0]->is_active_appl }}</strong></td>
                                        <td>Appl Created By</td>
                                        <td><strong>{{ $listWorkFlow[0]->appl_created_by }}</strong></td>
                                    </tr>
                                    <tr>
                                        <td>Appl Changed Date</td>
                                        <td><strong>{{ $listWorkFlow[0]->appl_change_date }}</strong></td>
                                        <td>Is Resubmit</td>
                                        <td><strong>{{ $listWorkFlow[0]->is_resubmit }}</strong></td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        @endif
                        <div class="modal-header text-left">
                            <h2 class="modal-title">List Workflow SM</h2>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-3" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">WF ID</th>
                                        <th class="text-center">WF Created Date</th>
                                        <th class="text-center">WF Changed Date</th>
                                        <th class="text-center">WF Status</th>
                                        <th class="text-center">WF Is Current</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listWorkFlow as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->workflow_status_id }}</td>
                                        <td class="text-center">{{ $data->wf_created_date }}</td>
                                        <td class="text-center">{{ $data->wf_changed_date }}</td>
                                        <td class="text-center">({{ $data->wf_status_id }}) - {{ $data->wf_status }}</td>
                                        <td class="text-center">{{ $data->is_current }}</td>

                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @if($listSuppTrackDiary != null && count($listSuppTrackDiary) > 0)
                        <div class="modal-header text-left">
                            <h2 class="modal-title">List Tracking Diary</h2>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-4" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">ID</th>
                                        <th class="text-center">Doc Type</th>
                                        <th class="text-center">Doc No.</th>
                                        <th class="text-center">Description</th>
                                        <th class="text-center">Action Date</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listSuppTrackDiary as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ $data->tracking_diary_id}}</td>
                                        <td class="text-center">{{ $data->doc_type }}</td>
                                        <td class="text-center">{{ $data->doc_no }}</td>
                                        <td class="text-center">{{ $data->action_desc }}</td>
                                        <td class="text-center">{{ $data->actioned_date }}</td>
                                        <td class="text-center">{{ $data->status_id }} - {{ $data->status_name }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                        @endif
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>


<!-- MODAL: LIST PAYMENT HISTORY -->
<div id="modal-list-payment-history" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>Payment History : {{ count($listSuppPayment) }}</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <div class="table-responsive">
                            <table id="basic-datatable2" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">Bill No.</th>
                                        <th class="text-center">Bill ID</th>
                                        <th class="text-center">Bill Type</th>
                                        <th class="text-center">Bill Date</th>
                                        <th class="text-center">Bill Amount</th>
                                        <th class="text-center">Order ID</th>
                                        <th class="text-center">Pending Process ID</th>
                                        <th class="text-center">Payment Date</th>
                                        <th class="text-center">Payment Mode</th>
                                        <th class="text-center">Status</th>
                                        <th class="text-center">Personnel</th>
                                        <th class="text-center">Receipt No.</th>
                                        <th class="text-center">Download Receipt</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listSuppPayment as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->bill_no }}</td>
                                        <td class="text-center">{{ $data->bill_id }}</td>
                                        <td class="text-center">{{ $data->bill_type }}</td>
                                        <td class="text-center">{{ $data->bill_date }}</td>
                                        <td class="text-center">{{ $data->payment_amt }}</td>
                                        <td class="text-center">{{ $data->payment_id }} - {{ $data->payment_gateway }}</td>
                                        <td class="text-center">{{ $data->pending_process_id }}
                                            @if(Auth::user()->isAdvRolesEp() && $data->pending_process_id != null)
                                            - <a target='_blank' href='{{url("/find/patch-ep")}}?patch=SM_PENDING_PROCESS&record={{ $data->pending_process_id }}'>Status: {{ $data->pending_process_status }} || Attempt: {{ $data->pending_process_attempt }} || ErrMsg: {{ $data->pending_process_err_msg }}</a>
                                            @endif
                                        </td>

                                        <td class="text-center">{{ $data->payment_date }}</td>
                                        <td class="text-center">{{ $data->payment_mode }}</td>
                                        <td class="text-center">
                                            <a href="#modal-list-payment-response" data-toggle="modal" class="btn btn-info btn-xs" data-payment-id="{{$data->payment_id}}">{{$data->status}}</a>
                                        </td>
                                        <td class="text-center">{{ $data->personnel }}</td>
                                        <td class="text-center">{{ $data->receipt_no }} </td>
                                        <td class="text-center">
                                            @if($data->receipt_no && strlen($data->receipt_no) > 5)
                                            @php $urlReport = env('EP_REPORT_URL','http://*************:5701/reports/rwservlet?ngepsit') @endphp
                                            @if($data->bill_type == 'P' || $data->bill_type == 'R')
                                            <a target="_blank" href="{{$urlReport}}&report={{$data->receipt_filename}}&pmntid={{$data->payment_id}}&destype=cache&desformat=pdf"><i class="fa fa-download"></i></a>
                                            @elseif($data->bill_type == 'S')
                                            <a target="_blank" href="{{$urlReport}}&report={{$data->receipt_filename}}&pmntid={{$data->payment_id}}&destype=cache&desformat=pdf"><i class="fa fa-download"></i></a>
                                            @endif
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<div id="modal-list-payment-response" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>Payment Response (Order ID)</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <div class="table-responsive">
                            <table id="basic-datatable-payment-response" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">CHANNEL_NAME</th>
                                        <th class="text-center">TRANSACTION_ID</th>
                                        <th class="text-center">TRANSACTION_DATE</th>
                                        <th class="text-center">PAYMENT_STATUS</th>
                                        <th class="text-center">CREATED_DATE</th>
                                        <th class="text-center">ERROR_CODE</th>
                                        <th class="text-center">ERROR_DESC</th>
                                        <th class="text-center">BANK_DATE</th>
                                        <th class="text-center">MOLPAY_TRANSACTION_ID</th>
                                        <th class="text-center">FPX_TRAN_ID</th>
                                    </tr>
                                </thead>
                                <tbody>
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- MODAL: List Branch-->
<div id="modal-list-branch" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>List Branch: @if(count($listSupplierBranch) > 0){{ count($listSupplierBranch) }}@endif</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">

                        <div class="table-responsive">
                            <table id="basic-datatable3" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">Branch ID</th>
                                        <th class="text-center">Branch Name</th>
                                        <th class="text-center">Branch Code</th>
                                        <th class="text-center">SAP Vendor Code</th>
                                        <th class="text-center">Changed Date</th>
                                        <th class="text-left">GST</th>
                                        <th class="text-center">Address</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listSupplierBranch as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ $data->supplier_branch_id }}</td>
                                        <td class="text-center" @if($data->is_branch_name_non_ascii) style="color:red; font-weight:bolder" @endif>{{ $data->branch_name }}</td>
                                        <td class="text-center">{{ $data->branch_code }}</td>
                                        <td class="text-center">{{ $data->sap_vendor_code }}</td>
                                        <td class="text-center">{{ $data->changed_date }}</td>
                                        <td class="text-left">
                                            <strong>Registration No. :</strong> {{ $data->gst_reg_no }}<br />
                                            <strong>Effective Date :</strong> {{ $data->gst_eff_date }}<br />
                                        </td>
                                        <td class="text-center" @if($data->is_non_ascii) style="color:red; font-weight:bolder" @endif>
                                            (Address ID : {{ $data->address_id }}),<br />
                                            {{ $data->address_1 }},
                                            {{ $data->address_2 }},
                                            {{ $data->address_3 }}<br />
                                            {{ $data->postcode }} {{ $data->city_name }}, {{ $data->district_name }}<br />
                                            {{ $data->state_name }}, {{ $data->country_name }}<br />
                                            Phone : {{ $data->phone_country }}{{ $data->phone_area }}{{ $data->phone_no }}
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>


<!-- MODAL: List Bank -->
<div id="modal-list-bank" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2><i class="fa fa-users"></i> <strong>List Bank: @if(count($listSupplierBank) > 0){{ count($listSupplierBank) }}@endif</strong></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="table-responsive">
                            <table id="basic-datatable-5" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-center">Bank Name</th>
                                        @if(Auth::user()->isAdvRolesEp())
                                        <th class="text-center">Supplier Bank ID</th>
                                        @endif
                                        <th class="text-center">Acc. No.</th>
                                        <th class="text-center">Acc. Purpose</th>
                                        <th class="text-center">Is Default</th>
                                        <th class="text-center">Is HQ</th>
                                        <th class="text-center">Bank Branch</th>
                                        <th class="text-center">Changed Date</th>
                                        <th class="text-center">Supplier Branch Name</th>
                                        <th class="text-center">Supplier Branch Code</th>
                                        <th class="text-center">Supplier Bank Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listSupplierBank as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->fin_org_name }} <span @if($data->bank_status == 9) class="text-danger" @endif>({{ App\Services\EPService::$RECORD_STATUS[$data->bank_status] }})</span></td>
                                        @if(Auth::user()->isAdvRolesEp())
                                        <td class="text-center"><a href='{{url("/find/patch-ep")}}?patch=SM_SUPPLIER_BANK&record={{ $data->supplier_bank_id }}' target="_blank" >
                                            {{ $data->supplier_bank_id }}
                                       
                                        </td>
                                        @endif
                                        <td class="text-center">{{ $data->account_no }} @if($data->is_invalid_bank_no== 1) <i class="gi gi-circle_exclamation_mark  text-danger" style="font-size: 10pt;" title="Invalid Format Value For Bank Number."> </i>@endif</td>
                                        <td class="text-center">{{ $data->account_purpose }}</td>
                                        <td class="text-center">{{ $data->is_default_account }}</td>
                                        <td class="text-center">{{ $data->is_for_hq }}</td>
                                        <td class="text-center">{{ $data->bank_branch }}</td>
                                        <td class="text-center">{{ $data->changed_date }}</td>
                                        <td class="text-center">{{ $data->branch_name }}</td>
                                        <td class="text-center">{{ $data->branch_code }}</td>
                                        <td class="text-center">{{ $data->supplier_bank_status }} ({{ App\Services\EPService::$RECORD_STATUS[$data->supplier_bank_status] }})</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- MODAL: KOD BIDANG -->
<div id="modal-category-code" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> List Category Code</h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="table-responsive">
                            <table id="datatable-category-code" class="table table-striped table-vcenter">
                                <thead>
                                    <tr>
                                        <th class="text-center">No.</th>
                                        <th class="text-left">Category Code</th>
                                        <th class="text-left">Category</th>
                                        <th class="text-center">Category Type</th>
                                        <th class="text-left">Officer Decision</th>
                                        <th class="text-center" style="display: none;">Remarks</th>
                                        <th class="text-center">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($listSupplierCategoryCode as $indexKey => $data)
                                    <tr>
                                        <td class="text-center">{{ ++$indexKey }}</td>
                                        <td class="text-center">{{ $data->category_code }}</td>
                                        <td class="text-left">
                                            {{ $data->category_l1_code }} - {{ $data->category_l1_name }}<br />
                                            {{ $data->category_l2_code }} - {{ $data->category_l2_name }}<br />
                                            {{ $data->category_l3_code }} - {{ $data->category_l3_name }}<br />
                                        </td>
                                        <td class="text-center">{{ $data->is_special_category == 1 ? "Special" : "Normal" }}</td>
                                        <td class="text-left">
                                            <strong>Approved Date :</strong> {{ $data->approved_date }}<br />
                                            <strong>PO :</strong> {{ $data->is_approved_by_po }}<br />
                                            <strong>Approver :</strong> {{ $data->is_approved_by_ap }}
                                        </td>
                                        <td class="text-left" style="display: none;">
                                            <strong>PO :</strong> {{ $data->previous_po_remark }}<br />
                                            <strong>Approver :</strong> {{ $data->previous_ap_remark }}
                                        </td>
                                        <td class="text-center">{{ $data->record_status }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- MODAL: SPKI SOFTCERT -->
<div id="modal-osbdetail-spki" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="osb-detail"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MODAL: SPKI SOFTCERT - RESEND REQUEST SOFTCERT -->
<div id="modal-spki-send-request-softcert" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Re-send Softcert Request to Digicert</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="osb-detail">
                            This scenario happen if Renewal Trustgate is failed!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MODAL: PENDING TRANSACTION-->
<div id="modal-pendingtransact_" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Pending Transaction</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="transact-detail"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<!-- MODAL: LIST SUPPLIER ITEMS-->
<div id="modal_list_supplier_items" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header">List Supplier Items</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="list-supplier-items-detail"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MODAL: LIST Transaction DocNo for Supplier in CurrentYear-->
<div id="modal_list_supplier_transactions" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Transaction This Year (Latest 100) </span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="list_supplier_transactions_detail"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modal_list_history_supplier" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List History Appl ID</span></h2>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="applid_history"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- MODAL: LIST Address -->
<div id="modal-list-data-total-address" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-total-address">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-total-address" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>

<!-- MODAL: Inquiries / Rejected Modal -->
<div id="modal-list-data-inquiries-reject" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <!-- Modal Header -->
            <div class="modal-header text-center">
                <h2 class="modal-title"><i class="fa fa-list"></i> <span id="modal-list-data-header-inquiries-reject">List Title</span></h2>
            </div>
            <!-- END Modal Header -->

            <!-- Modal Body -->
            <div class="modal-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="text-center spinner-loading" style="padding: 20px;">
                            <i class="fa fa-spinner fa-4x fa-spin"></i>
                        </div>
                        <div class="table-responsive">
                            <table id="basic-datatable-inquiries-reject" class="table table-striped table-vcenter">
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <!-- END Modal Body -->
        </div>
    </div>
</div>


@endif


@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>
    $(function() {
        TablesDatatables.init();
    });
</script>
<script>
    var APP_URL = {!!json_encode(url('/')) !!}

    /** initialize datatable **/
    App.datatables();

    var tableListData = $('#basic-datatable-total-address').DataTable({
        columnDefs: [{
            orderable: false,
            targets: [0]
        }],
        pageLength: 10,
        lengthMenu: [
            [10, 20, 30, -1],
            [10, 20, 30, 'All']
        ]
    });


    $(document).ready(function() {

        $('.maklumat-diri').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('#basic-datatable-total-address').html('').fadeIn();

            $('#modal-list-data-header-total-address').text($(this).attr('data-title'));

            /* Destroy ID Datatable, To make sure reRun again ID */
            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-total-address').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-total-address').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });

        });

        $('.softcert').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('.osb-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.osb-detail').html($data).fadeIn();
                }
            });

        });

        $('.supplier-items-catalog').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.list-supplier-items-detail').html($data).fadeIn();
                    $('#items-supplier-datatable').dataTable({
                        order: [
                            [2, "desc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

        });

        $('.pnlTransSuppCurYear').on("click", '.modal-list-data-action', function() {

            $('.spinner-loading').show();
            $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.list_supplier_transactions_detail').html($data).fadeIn();
                    $('#items-transactions-supplier-datatable').dataTable({
                        order: [
                            [0, "desc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

        });

        $('.historyapplid').on("click", '.modal-list-data-action1', function() {
            $('.spinner-loading').show();
            $('.transact-detail').html('Please wait.. this searching take more than 20 seconds').fadeIn();

            $('#modal-list-data-header').text($(this).attr('data-title'));


            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data)
                    $('.spinner-loading').hide();
                    $('.applid_history').html($data).fadeIn();
                    $('#summary-appl-history-datatable').dataTable({
                        order: [
                            [0, "desc"]
                        ],
                        columnDefs: [],
                        pageLength: 5,
                        lengthMenu: [
                            [5, 10, 20, 30, 50, -1],
                            [10, 20, 30, 50, 'All']
                        ]
                    });
                }
            });

        });

    });
</script>
<script>
    $('#modal-list-payment-response').on('show.bs.modal', function(event) {
        var button = $(event.relatedTarget); 
        var paymentId = button.data('payment-id');

        $.ajax({
            url: APP_URL + '/payment/history',
            method: 'GET',
            data: {
                payment_id: paymentId
            },
            success: function(response) {
                console.log('data', response)
                var tbody = $('#basic-datatable-payment-response tbody');
                tbody.empty(); 

                $.each(response, function(i, item) {
                    console.log('item', item)
                    var tr = $('<tr>').append(
                        $('<td>').text(item.channel_name),
                        $('<td>').text(item.transaction_id),
                        $('<td>').text(item.transaction_date),
                        $('<td>').text(item.payment_status),
                        $('<td>').text(item.created_date),
                        $('<td>').text(item.error_code || 'N/A'), 
                        $('<td>').text(item.error_desc || 'N/A'), 
                        $('<td>').text(item.bank_date || 'N/A'), 
                        $('<td>').text(item.molpay_transaction_id || 'N/A'), 
                        $('<td>').text(item.fpx_tran_id || 'N/A') 
                    );
                    tbody.append(tr);
                });
            }
        });
    });
</script>
@if($listdata != null)
@if($basicCompInfo && $basicCompInfo->appl_id)
<script>
    $(document).ready(function() {
        App.datatables();

        let tableListData = $('#basic-datatable-inquiries-reject').DataTable({
            columnDefs: [{
                orderable: false,
                targets: [0]
            }],
            pageLength: 10,
            lengthMenu: [
                [10, 20, 30, -1],
                [10, 20, 30, 'All']
            ]
        });

        $('.widget').on("click", '.modal-list-data-action', function() {
            $('.spinner-loading').show();
            $('#basic-datatable-inquiries-reject').html('').fadeIn();
            $('#modal-list-data-header-inquiries-reject').text($(this).attr('data-title'));

            tableListData.destroy();

            $.ajax({
                url: APP_URL + $(this).attr('data-url'),
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#basic-datatable-inquiries-reject').html($data).fadeIn();

                    /* Re-Initialize Datatable */
                    tableListData = $('#basic-datatable-inquiries-reject').DataTable({
                        columnDefs: [{
                            orderable: false,
                            targets: [0]
                        }],
                        pageLength: 10,
                        lengthMenu: [
                            [10, 20, 30, -1],
                            [10, 20, 30, 'All']
                        ]
                    });

                    $('.spinner-loading').hide();
                }
            });
            $.ajax({
                url: APP_URL + "/find/supplier/application-inquiry/count/{{$basicCompInfo->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-inquiry-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
                }
            });

            $.ajax({
                url: APP_URL + "/find/supplier/application-rejected/count/{{$basicCompInfo->appl_id}}",
                type: "GET",
                success: function(data) {
                    $data = $(data);
                    $('#count-application-rejected-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
                }
            });

        })


        $.ajax({
            url: APP_URL + "/find/supplier/application-inquiry/count/{{$basicCompInfo->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-inquiry-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
            }
        });

        $.ajax({
            url: APP_URL + "/find/supplier/application-rejected/count/{{$basicCompInfo->appl_id}}",
            type: "GET",
            success: function(data) {
                $data = $(data);
                $('#count-application-rejected-{{$basicCompInfo->appl_id}}').hide().html($data).fadeIn();
            }
        });
    });
</script>
@endif
@endif


@endsection