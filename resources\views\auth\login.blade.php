<!DOCTYPE html>
<html lang="en">
<head>
    <title>Login | eP Support System</title>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <!--===============================================================================================-->
    <link rel="icon" type="image/png" href="img/favicon.png"/>
    <!--===============================================================================================-->
    <link rel="stylesheet" href="/css/bootstrap.min.css">
    <!--===============================================================================================-->
    <link rel="stylesheet" type="text/css" href="css/fonts/font-awesome-4.7.0/css/font-awesome.min.css">
    <!--===============================================================================================-->
    <link rel="stylesheet" type="text/css" href="vendor/animate/animate.css">
    <!--===============================================================================================-->
    <link rel="stylesheet" type="text/css" href="vendor/css-hamburgers/hamburgers.min.css">
    <!--===============================================================================================-->
    <link rel="stylesheet" type="text/css" href="vendor/select2/select2.min.css">
    <!--===============================================================================================-->
    <link rel="stylesheet" type="text/css" href="css/util.css">
    <link rel="stylesheet" type="text/css" href="css/login.css">
    <!--===============================================================================================-->
    <!-- Related styles of various icon packs and plugins -->
    <link rel="stylesheet" href="/css/plugins-backend.css">
</head>
<body>

<div class="limiter">
    <div class="container-login100">
        <div class="wrap-login100 animation-fadeInQuickInv">
            <div class="login100-pic js-tilt" data-tilt>
                <img src="img/epps_login.svg" alt="EPSS">
            </div>

            <form class="login100-form validate-form" role="form" method="POST" action="{{ url('/login') }}">
                {{ csrf_field() }}
                <span class="login100-form-title">
						Welcome
					</span>
                <span class="login100-form-desc">
						eP Support System
					</span>

                <div class="wrap-input100 validate-input" data-validate="Please enter valid username">
                    {{--<input class="input100" type="text" name="email" placeholder="Username">--}}
                    <input id="user_name" type="text" class="input100" name="user_name" value="{{ old('user_name') }}"
                           placeholder="Username" required>
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
							<i class="fa fa-user" aria-hidden="true"></i>
						</span>
                </div>

                <div class="wrap-input100 validate-input" data-validate="Password is required">
                    {{--<input class="input100" type="password" name="pass" placeholder="Password">--}}
                    <input id="password" type="password" class="input100" name="password" placeholder="Password"
                           required>
                    <span class="focus-input100"></span>
                    <span class="symbol-input100">
							<i class="fa fa-lock" aria-hidden="true"></i>
						</span>
                </div>

                <div class="wrap-error100">
                    @if ($errors->has('user_name'))
                        <span class="help-block">
                            <strong>{{ $errors->first('user_name') }}</strong>
                        </span>
                    @endif
                    @if ($errors->has('password'))
                        <span class="help-block">
                            <strong>{{ $errors->first('password') }}</strong>
                        </span>
                    @endif
                </div>

                {{--<div class="container-login100-form-btn">--}}
                {{--<button class="login100-form-btn">--}}
                {{--Login--}}
                {{--</button>--}}
                {{--</div>--}}

                <div class="container-login100-form-btn">
                    <button type="submit" class="login100-form-btn">
                        Login
                    </button>
                </div>

                {{--<div class="text-center p-t-12">--}}
                {{--<span class="txt1">--}}
                {{--Forgot--}}
                {{--</span>--}}
                {{--<a class="txt2" href="#">--}}
                {{--Username / Password?--}}
                {{--</a>--}}
                {{--</div>--}}

                {{--<div class="text-center p-t-136">--}}
                {{--<span id="year-copy">2016-18</span> &copy; <a href="#" target="_blank">Direkacipta oleh Commerce Dot Com Sdn Bhd</a>--}}
                {{--</div>--}}
            </form>
        </div>
        {{--<span class="login100-form-footer">2016-19 © Direkacipta oleh Commerce Dot Com Sdn Bhd</span>--}}
    </div>
    {{--<span id="year-copy"></span> &copy; <a href="#" target="_blank">Direkacipta oleh Commerce Dot Com Sdn Bhd</a>--}}
</div>


<!--===============================================================================================-->
<script src="vendor/jquery/jquery-3.2.1.min.js"></script>
<!--===============================================================================================-->
<script src="vendor/bootstrap/js/popper.js"></script>
<script src="/js/vendor/bootstrap.min.js"></script>
<!--===============================================================================================-->
<script src="vendor/select2/select2.min.js"></script>
<!--===============================================================================================-->
<script src="vendor/tilt/tilt.jquery.min.js"></script>
<script>
    $('.js-tilt').tilt({
        scale: 1.1
    })
</script>
<!--===============================================================================================-->
<script src="js/main.js"></script>

</body>
</html>

