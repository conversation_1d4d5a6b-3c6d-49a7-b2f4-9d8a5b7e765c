@extends('layouts.guest-dash')

@section('content')
    <!--menu-->
    <div class="content-header">
        <ul class="nav-horizontal text-center">
            <li class="active">
                <a href="{{ url('/it_support/dba_morning/checklist') }}"><i class="fa fa-tasks"></i>Check List</a>
            </li>
            <li>
                <a href="{{ url('/it_support/dba_morning/checklist/history') }}"><i class="fa fa-history"></i>CheckList
                    History</a>
            </li>
            <li>
                <a href="{{ url('/it_support/dba_morning/data_lookup') }}"><i class="fa fa-cubes"></i>Data Lookup</a>
            </li>
        </ul>
    </div>
    <div class="block">
        <form id="dba_morning_checklist" action="{{ url('/it_support/dba_morning/checklist') }}" method="post">
            {{ csrf_field() }}
            @if (session()->has('failed'))
                <div class="alert alert-danger alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Validation Error! </h4>
                    {{ session('failed') }}
                </div>
            @endif

            @if (session()->has('success'))
                <div class="alert alert-info alert-dismissable">
                    <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
                    <h4><i class="fa fa-info-circle"></i> Success! </h4>
                    {{ session('success') }}
                </div>
            @endif

            <div id="customDiv">
                <label class="col-md-12 text-center">Date : @if (isset($dateNote)) {{$dateNote}} @endif</label>  
                </div> 

            <div class="table-options clearfix" id="dba_morning_list">
                <div class="btn-group btn-group-sm pull-left" data-toggle="buttons">
                    @if (isset($categoryName))
                        @foreach ($categoryName as $key => $list)
                            <label id="menu_button" class="btn btn-primary" data-toggle="tooltip"
                                title="{{ $list->dba_data_lookup_category_name }}"
                                group="{{ $list->dba_data_lookup_category_name }}">
                                <input type="radio" name="menu" value="">
                                {{ $list->dba_data_lookup_category_name }}
                            </label>
                        @endforeach
                    @endif
                </div>
            </div>

            <div class="text-center spinner-loading" style="padding: 20px;">
                <i class="fa fa-spinner fa-4x fa-spin"></i>
            </div>

            <div class="table-responsive">
                <table id="list_datatable" class="table table-vcenter table-condensed table-bordered"></table>
            </div>
            @if(isset($listdata) == false || $listdata[0] == null || 
                (isset($listdata) && $listdata[0] && ($listdata[0]->ack_status != 'Pending Endorsement' 
                    && $listdata[0]->ack_status != 'Completed') )
            )
            <div class="text-center">
                <button type="submit" style="background-color: #414770; color: white"
                    class="btn btn btn-primary editSave">Update</button>
            </div>
            @endif
        </form>
    </div>
    <div class="block panel-heading">@include('it_support.page_status', ['page' => 'dba'])</div>
@endsection
@section('jsprivate')
    <script>
        $('#page-container').removeAttr('class');
    </script>
    <script>
        App.datatables();
    </script>
    <script>
        var customDiv = document.getElementById('customDiv');
        customDiv.style.width = '95vw'; // Set the width
        customDiv.style.height = '50px'; // Set the height
        customDiv.style.backgroundColor = '#414770'; // Set the background color
        customDiv.style.color = 'white';
        customDiv.style.textAlign = 'center';
        customDiv.style.fontSize = '16px';
        customDiv.style.display = 'flex';
        customDiv.style.alignItems = 'center';
    </script>
    <script>
        function selectAll(ele) {
            console.log('siniii')
            var checkboxes = document.querySelectorAll('#radio_for_status_okay');
            if (ele.checked) {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = true;
                    }
                }
            } else {
                for (var i = 0; i < checkboxes.length; i++) {
                    if (checkboxes[i].type === 'radio') {
                        checkboxes[i].checked = false;
                    }
                }
            }
        }

        $(document).ready(function() {
            $('#menu_button').click();
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {
            } else {
                $('#remarks').val("");
            }
            var ackRemarks = $('#remarks').val();
            if (ackRemarks !== null && ackRemarks.trim() !== '') {
            } else {
                $('#remarks').val("{{ $listNote[0]->ack_remarks ?? '' }}");
            }
            var ackStatus = "{{ $listNote[0]->ack_status ?? '' }}";
            if (ackStatus === 'Pending Endorsement' || ackStatus === 'Completed') {
                $('.editSave').hide();
            } else {
                $('.editSave').show();
            }

        });

        function changeMenu(a) {
            var dateSelection = null;
            let
                group = $(a).attr('group');

            if ($.fn.DataTable.isDataTable('#list_datatable')) {
                $('#list_datatable').DataTable().destroy();
            }

            $('#list_datatable thead').empty();
            $('#list_datatable tbody').empty();

            $('.spinner-loading').show();

            $.ajax({
                type: "GET",
                url: "/it_support/dba_morning/find_by/" + group + "/" + dateSelection,
            }).done(function(data) {
                $('.spinner-loading').hide();
                $('#list_datatable').html(data).fadeIn();
                Tabledatatable = $('#list_datatable').DataTable({
                    ordering: false,
                    lengthMenu: [
                        [10, 20, 30, -1],
                        [10, 20, 30, 'All']
                    ]
                });

            })
        }
        $(".btn-group").on("change", "#menu_button", function(e) {
            changeMenu(this)
        });
    </script>
@endsection
