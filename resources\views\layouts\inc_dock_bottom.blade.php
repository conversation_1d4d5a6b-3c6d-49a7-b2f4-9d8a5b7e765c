{{-- GROUP SPECIALIST TECHNICAL SUPPORT ONLY. PLEASE DO NOT PUT ANY ROLE HERE.  --}}
@if (Auth::user()->isAdvRolesEp())
    <div class="footer footer--animated">
        <button id="pinButton" class="pin-button">Pin</button>
        <div class="bottomMenu">
            <button class="bottomMenu__item {{ Request::is('home') ? 'bottomMenu__item--active' : '' }}">
                <a href="{{ url('home') }}">
                    <span class="title"> Home </span>
                    <i class="gi gi-home"></i>
                </a>
            </button>
            <span class="seperator"></span>
            <button class="bottomMenu__item {{ Request::is('dashboard/main') ? 'bottomMenu__item--active' : '' }}">
                <a href="{{ url('dashboard/main') }}">
                    <span class="title"> Dashboard </span>
                    <i class="gi gi-stopwatch"></i>
                </a>
            </button>
            <button class="bottomMenu__item {{ Request::is('stl') ? 'bottomMenu__item--active' : '' }}"">
                <a href="{{ url('stl') }}">
                    <span class="title"> Dashboard STL </span>
                    <i class="gi gi-sort"></i>
                </a>
            </button>
            <button class="bottomMenu__item {{ Request::is('bpm*') ? 'bottomMenu__item--active' : '' }}"">
                <a href="{{ url('bpm/task/find') }}">
                    <span class="title"> BPM API </span>
                    <i class="gi ico-text">BPM</i>
                </a>
            </button>
            <button class="bottomMenu__item {{ Request::is('find/osb*') ? 'bottomMenu__item--active' : '' }}"">
                <a href="{{ url('find/osb/log') }}">
                    <span class="title"> OSB </span>
                    <i class="gi ico-text">OSB</i>
                </a>
            </button>
            <button class="bottomMenu__item {{ Request::is('log/dashboard*') ? 'bottomMenu__item--active' : '' }}"">
                <a href="{{ url('log/dashboard') }}">
                    <span class="title"> eP Log Trace </span>
                    <i class="gi gi-search"></i>
                </a>
            </button>
        </div>
    </div>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Tektur:wght@600&display=swap" rel="stylesheet">

    @yield('jsprivate')
    <script>
        document.addEventListener('DOMContentLoaded', (event) => {
            const footer = document.querySelector('.footer');
            const pinButton = document.getElementById('pinButton');

            // Check if the footer is pinned
            let isPinned = document.cookie.split(';').some((item) => item.trim().startsWith('footerPinned='));

            if (!isPinned) {
                isPinned = true;
                document.cookie = "footerPinned=true; path=/"; // Store the state in a cookie
            } else {
                isPinned = document.cookie.replace(/(?:(?:^|.*;\s*)footerPinned\s*\=\s*([^;]*).*$)|^.*$/, "$1") ===
                    "true";
            }

            function updateFooterState() {
                if (isPinned) {
                    footer.style.bottom = '0';
                    pinButton.innerHTML =
                        '<i class="gi gi-pushpin" style="color: #ff7654;"></i>'; // Icon for pinned state
                } else {
                    footer.style.bottom = '-65px';
                    pinButton.innerHTML =
                        '<i class="gi gi-pushpin" style="color: #ccc;"></i>'; // Icon for unpinned state
                }
            }

            updateFooterState();

            pinButton.addEventListener('click', () => {
                if (isPinned) {
                    footer.style.bottom = '-65px'; // Hide the footer
                } else {
                    footer.style.bottom = '0'; // Show the footer
                }
                isPinned = !isPinned;

                // Update Font Awesome icon based on pinned state
                updateFooterState();

                document.cookie = `footerPinned=${isPinned}; path=/`; // Store the state in a cookie
            });

            footer.addEventListener('mouseleave', () => {
                if (!isPinned) {
                    footer.style.bottom = '-65px'; // Hide the footer when not hovered
                }
            });

            footer.addEventListener('mouseenter', () => {
                if (!isPinned) {
                    footer.style.bottom = '0px'; // Show the footer when hovered
                }
            });
        });
    </script>

    <style>
        .footer {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translate(-50%, 0);
            width: auto;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 12px;
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(0, 0, 0, 0.112);
        }

        .footer {
            transition: bottom 0.25s ease-in-out;
            position: fixed;
            bottom: -65px;
            /* Other necessary styling */
        }

        .footer:hover {
            bottom: 0;
        }

        .footer--animated {
            /* animation: slideUp 0.5s ease-out forwards; */
        }

        .footer .bottomMenu {
            display: flex;
            gap: 12px;
            align-items: center;
            justify-content: center;
        }

        .footer .bottomMenu .seperator {
            width: 1px;
            height: 24px;
            background: rgba(0, 0, 0, 0.3);
            margin: 0px -4px;
        }

        .footer .bottomMenu__item {
            position: relative;
            height: 48px;
            width: 48px;
            border: none;
            background: #fff;
            border-radius: 8px;
            transition: all 0.20s;
        }

        .footer .bottomMenu__item--active:after {
            content: "";
            position: absolute;
            bottom: -8px;
            left: 50%;
            width: 5px;
            height: 5px;
            background: #f1f1f1;
            border-radius: 12px;
        }

        .footer .bottomMenu__item .title {
            position: absolute;
            white-space: nowrap;
            top: -10px;
            left: 50%;
            transform: translate(-50%, -50%);
            display: none;
            border-radius: 4px;
            padding: 2px 4px;
            font-size: 0.8rem;
            color: rgba(255, 255, 255, 1);
            background: rgba(0, 0, 0, 0.714);
        }

        .footer .bottomMenu__item .gi {
            /* width: 24px;
        height: 24px; */
            font-size: 24px;
            color: #414770;
            stroke: currentColor;
            fill: currentColor;
            transition: all 0.20s;
        }

        .footer .bottomMenu__item:hover,
        .footer .bottomMenu__item:focus {
            z-index: 999;
            transform: scale(1.3);
            margin: 0 10px;
            color: #57b846;
        }

        .footer .bottomMenu__item:hover .title,
        .footer .bottomMenu__item:focus .title {
            display: block;
        }

        .footer .bottomMenu__item:hover .gi,
        .footer .bottomMenu__item:focus .gi {
            font-size: 32px;
            color: #57b846;
        }

        .footer .bottomMenu__item .ico-text {
            font-family: 'Tektur', sans-serif;
            font-size: 16px;
        }

        .footer .bottomMenu__item:hover .ico-text {
            font-size: 16px;
        }

        .pin-button {
            background: rgba(255, 255, 255, 0.5);
            border: none;
            border-radius: 50%;
            padding: 5px;
            cursor: pointer;
            width: 25px;
            height: 25px;
            margin: 0 6px 0 -6px
        }
    </style>
@endif
{{-- END SPECIALIST TECHNICAL SUPPORT GROUP --}}
