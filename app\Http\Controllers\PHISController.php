<?php

namespace App\Http\Controllers;

use App\Services\Traits\SupplierService;
use App\Services\Traits\OSBService;
use App\Services\Traits\ProfileService;
use App\Services\Traits\PhisService;
use Illuminate\Http\Request;
use DB;
use Carbon\Carbon;
use GuzzleHttp\Client;
use App\EpSupportActionLog;
use Log;
use App\Services\Traits\FulfilmentService;

class PHISController extends Controller {

    use SupplierService;
    use OSBService;
    use PhisService;
    use ProfileService;
    use FulfilmentService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }
    
    public function wsOSBLog(){
        return view('list_phis', [
            'listdata' => null,
            'listXml' => null,
            'carian' => '']);
    }
    public function searchWsOSBLog(Request $request){

        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'doc_no' => 'required'
            ]);
        }
            $docNo = $request->doc_no;
            $serviceCode = $request->service_code;
            $list = $this->getListOSBPHISWebServiceDetails($docNo ,$serviceCode );
            
            return view('list_phis', [
            'listdata' => $list,
            'carian' => $docNo,
            'formSearch' => $request->all()]);
        
        
    }
    
    public function phisView() {
        return view('phis_details', [
            'result' => null
        ]);
    }
    
    public function PhisSearchOrderDetails(Request $request){
        $searchType = $request->searchType;
        $tabName = '';
        
        if ($searchType == 'search_docno'){
            $docNo = $request->doc_no;
            $phisNo = $request->phis_no;
            $tabName = 'search-tab-docno';
            
            if ($docNo) {
                $listDetailDoc = $this->getDocNoCRCO($docNo);
                $result = $listDetailDoc;
            }            
            
            if($phisNo) {
                $listDetailPhisNo = $this->getPhisNo($phisNo);
                $result = $listDetailPhisNo;
            }
        }
        
        if($result == null) {
            $result = 'notfound';
        }
        
        return view('phis_details', [
            
            'result' => $result,            
            'msg' => null,
            'tabName' => $tabName,
            'formSearch' => $request->all()
        ]);
        
    }  
    
    public function searchListPHISStuck(){
        $list = $list = DB::connection('mysql_ep_support')->table('ep_phis_stuck')->get();
        return view('list_phis_stuck', ['listdata' => $list]);
    }
    
    public function itemPhis150() {
        $carianContractNo = request()->contract_no;
        $carianSkuNo = request()->sku_no;

        $contractInfo = $this->checkContract($carianContractNo);
        $noOfZones = $this->checkZonal($carianContractNo);
        $noOfItems = $this->checkItems($carianContractNo);
        $listZonalArray = '';
        $itemArray = '';
        $payloadFull = '';

        foreach ($noOfZones as $zones) {
            $totalZone = $zones->zonalcount;
        }

        foreach ($noOfItems as $items) {
            $totalItems = $items->itemcount;
        }

        if ($carianContractNo != null && $carianSkuNo != null) {
            $itemArray = $this->getPhisItems($carianContractNo, $carianSkuNo);

            $payloadItem = '';
            $payloadContract = '';

            foreach ($itemArray as $item) {
                // check item status. if suspend, add tag suspendfrom, suspendto. else exclude tag
                $payloadItem = $payloadItem . '
            <con:Item>
            <con:Action>ADJ</con:Action>
            <con:ItemCode>' . $item->extensioncode . '</con:ItemCode> 
            <con:SKU>' . $item->sku . '</con:SKU>
            <con:ItemDescription>' . $item->itemname . '</con:ItemDescription>
            <con:AltUnitOfMeasurement>' . $item->uom . '</con:AltUnitOfMeasurement>
            <con:MinQuantity>' . number_format((float) $item->minqty, 3, '.', '') . '</con:MinQuantity>
            <con:MaxQuantity>' . number_format((float) $item->maxqty, 3, '.', '') . '</con:MaxQuantity>';


                $listZonalArray = $this->getZonal($carianContractNo, $item->sku, $item->uom, $item->itemsuspend);

                $payloadZonal = '';

                foreach ($listZonalArray as $zone) {


                    $payloadZonal = $payloadZonal . '
             <con:ZonalDefinition>
                <con:ZonalName>' . $zone->zonename . '</con:ZonalName>
                <con:ZonalUnitPrice>' . number_format((float) $zone->unitprice, 2, '.', '') . '</con:ZonalUnitPrice>
                <con:ValidFrom>' . Carbon::parse($contractInfo->effdate)->format('Y-m-d') . '</con:ValidFrom>
                <con:ValidTo>' . Carbon::parse($contractInfo->expdate)->format('Y-m-d') . '</con:ValidTo>
            </con:ZonalDefinition>';
                }

                if ($item->itemsuspend == 0) {
                    $payloadItem = $payloadItem . ' 
                 <con:SuspendFrom>'.Carbon::parse($item->suspendfrom)->format('Y-m-d').'</con:SuspendFrom>
                <con:SuspendTo>'.Carbon::parse($item->suspendto)->format('Y-m-d').'</con:SuspendTo>
            <con:ZonalDefinitionArray>' . $payloadZonal .
            '</con:ZonalDefinitionArray></con:Item>';
                } else {
                    $payloadItem = $payloadItem . 
                '<con:ZonalDefinitionArray>' . $payloadZonal .
                '</con:ZonalDefinitionArray>
            </con:Item>';
                }
            }

            if ($contractInfo && $contractInfo != '') {

                $payloadContract = ' 
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:con="http://www.ep.gov.my/Schema/1-0/ContractInformation" xmlns:epmf="http://www.ep.gov.my/Schema/1-0/epmf">
            <soapenv:Header/>
            <soapenv:Body>
            <con:EPMFRq>
            <epmf:RqHeader>
                <epmf:ConsumerID>EPP-001</epmf:ConsumerID>
                <epmf:UID>
                    <epmf:RqUID>303d29a3-1bcf-4012-b7a9-cd7d6454fe47</epmf:RqUID>
                    <epmf:AsyncRqUID>7177e3b3-aa0d-4b0b-b744-afbad90766a3</epmf:AsyncRqUID>
                </epmf:UID>
            </epmf:RqHeader>
            <con:ContractInformationRq>
                <con:Action>ADJ</con:Action>
                <con:EPContractNo>' . $contractInfo->contractno . '</con:EPContractNo>
                <con:PhysicalContactNo>' . $contractInfo->physicalno . '</con:PhysicalContactNo>
                <con:ContractEffectiveDate>' . Carbon::parse($contractInfo->effdate)->format('Y-m-d') . '</con:ContractEffectiveDate>
                <con:ContractExpiryDate>' . Carbon::parse($contractInfo->expdate)->format('Y-m-d') . '</con:ContractExpiryDate>
                <con:ContractStatus>' . $contractInfo->contractstatus . '</con:ContractStatus>
                <con:IsZonal>' . $contractInfo->iszonal . '</con:IsZonal>
                <con:NoOfZones>' . $totalZone . '</con:NoOfZones>
                <con:NoOfItem>' . $totalItems . '</con:NoOfItem>
                <con:AgencyArray/>
                <con:ZonalArray>
                <con:Zonal>
                    <con:Action>ADJ</con:Action>
                    <con:ZonalName>SABAH / SARAWAK / LABUAN</con:ZonalName>
                    <con:ZonalTerm>10</con:ZonalTerm>
                </con:Zonal>
                <con:Zonal>
                <con:Action>ADJ</con:Action>
                    <con:ZonalName>SEMENANJUNG</con:ZonalName>
                     <con:ZonalTerm>7</con:ZonalTerm>
                </con:Zonal>
                </con:ZonalArray>
            <con:ItemArray>';
            }
            $payloadFull = $payloadContract . $payloadItem . ' 
            </con:ItemArray>
            </con:ContractInformationRq>
            </con:EPMFRq>
            </soapenv:Body>
            </soapenv:Envelope> ';
        }

        return view('item_phis_150', [
            'contractNo' => $carianContractNo,
            'skuNo' => $carianSkuNo,
            'contractinfo' => $contractInfo,
            'itemArray' => $itemArray,
            'payload' => $payloadFull
        ]);
    }

    public function updateItemPhis150(Request $request) {

//          http://192.168.120.14:8011/ContractInformation/v1.2?wsdl (Dev)
//          http://192.168.63.205:7011/ContractInformation/v1.2?wsdl (Prod)
        $contractNo = $request->contract_no;
        $payload = $request->payload_full;


        $client = new Client([
            'base_uri' => 'http://192.168.63.205:7011',
        ]);
        $payloadCompleted = $payload;
        $responseCompleted = $client->post('/ContractInformation/v1.2', [
            //'debug' => TRUE,
            'body' => $payloadCompleted,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'updatePhis150',
            ]
        ]);

        // save action in log table
          EpSupportActionLog::saveActionLog('Trigger Item PHIS-150','Soap UI',$payloadCompleted,$contractNo,('Completed'));
          
        $bodyPOCompleted = $responseCompleted->getBody();
        return array('contractNo' => $contractNo, 'payload' => $bodyPOCompleted);
    }
    
    public function itemPhis160() {
        session()->flashInput(request()->input());
        
        $carianDocNo = request()->doc_no;
        $action = request()->triggeraction;
        $payload = '';
        $phisNo = '';
        
        $contractOrderInfo = $this->getContractInfoPhis160($carianDocNo);
        $itemArray = $this->getItemPhis160($carianDocNo);
        $workflow = $this->getListFulfilmenRequestByPrCr($carianDocNo);
        
        if (count($contractOrderInfo) > 0) {
            $phisNo = $phisNo . $contractOrderInfo[0]->phisno;
            $header = '
            <soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" xmlns:con="http://www.ep.gov.my/Schema/1-0/ContractOrderInformation" xmlns:epmf="http://www.ep.gov.my/Schema/1-0/epmf">
            <soapenv:Header/>
            <soapenv:Body>
            <con:EPMFRq>
            <epmf:RqHeader>
                <epmf:ConsumerID>EPP-002</epmf:ConsumerID>
                <epmf:OrgUnit>1</epmf:OrgUnit>
                <epmf:Segment>1</epmf:Segment>
                <epmf:IpAddress>1</epmf:IpAddress>
                <epmf:UID>
                    <epmf:RqUID>34383836303638343532383339343935</epmf:RqUID>
                    <epmf:AsyncRqUID>35363433363633303536383837373633</epmf:AsyncRqUID>
                </epmf:UID>
            </epmf:RqHeader>
            <con:ContractOrderInformationRq>';
            if ($action === 'CRE') {
                
                $newAction = '';
                $order = '';
                
                if ($contractOrderInfo[0]->contractorderno !== null) {
                    $newAction = 'CRE';
                    $order = '
                <con:ContractOrderNo>' . $contractOrderInfo[0]->contractorderno . '</con:ContractOrderNo>';
                }else {
                    $newAction = 'REJ';
                }
                $header = $header . '
                <con:Action>' . $newAction . '</con:Action> 
                <con:ContractRequestNo>' . $contractOrderInfo[0]->contractreqno . '</con:ContractRequestNo>'
                .$order.'
                <con:PHISNo>' . $contractOrderInfo[0]->phisno . '</con:PHISNo>
                <con:ActionDate>' . Carbon::parse($contractOrderInfo[0]->actioneddate)->format('Y-m-d') . '</con:ActionDate>
                <con:EPContractNo>' . $contractOrderInfo[0]->contractphysicalno . '</con:EPContractNo>
                <con:PTJCode>' . $contractOrderInfo[0]->orgcode . '</con:PTJCode>
                <con:COAmount>' . number_format((float) $contractOrderInfo[0]->coamount, 2, '.', '') . '</con:COAmount>
                <con:NoOfChargelines>' . count($itemArray) . '</con:NoOfChargelines> ';
                $items = '';
                if ($contractOrderInfo[0]->contractorderno !== null) {
                    $header = $header . '<con:LineItemArray>';

                    foreach ($itemArray as $item) {
                        $items = $items . '
                <con:LineItem>
                    <con:DeliveryAddressID>' . $item->deliveryaddrid . '</con:DeliveryAddressID>
                    <con:PTJDipertangungkan>' . $item->ptjdipertanggung . '</con:PTJDipertangungkan>
                    <con:Vot>' . $item->vot . '</con:Vot>
                    <con:ProgramAktiviti>' . $item->programactivity . '</con:ProgramAktiviti>
                    <con:GLAccounts>' . $item->glaccount . '</con:GLAccounts>
                    <con:Indicator>' . $item->indicator . '</con:Indicator>
                    <con:ItemCode>' . $item->itemcode . '</con:ItemCode>
                    <con:Quantity>' . number_format((float) $item->quantity, 3, '.', '') . '</con:Quantity>
                    <con:UnitPrice>' . number_format((float) $item->unitprice, 2, '.', '') . '</con:UnitPrice>
                    <con:AltUnitOfMeasurement>' . $item->uom . '</con:AltUnitOfMeasurement>
                </con:LineItem>';
                    }
                    $items = $items . '</con:LineItemArray>';
                }

                $payload = $payload . $header . $items . ' 
            </con:ContractOrderInformationRq>
        </con:EPMFRq>
        </soapenv:Body>
        </soapenv:Envelope> ';
            } else if ($action === 'REJ') {
                $amount = $this->getTotalAmountCr($contractOrderInfo[0]->fulfilment_req_id);

                $actionDate = '';
                foreach ($workflow as $data) {
                    if ($data->is_current == 1 && $data->status_id == 40910) {
                        $actionDate = $data->fws_date_created;
                    }
                }
                $ctOrderNo = '';
                if($contractOrderInfo[0]->contractorderno != '') {
                    $ctOrderNo = '<con:ContractOrderNo>' . $contractOrderInfo[0]->contractorderno . '</con:ContractOrderNo>';
                }
                $header = $header . '  
                <con:Action>' . $action . '</con:Action> 
                <con:ContractRequestNo>' . $contractOrderInfo[0]->contractreqno . '</con:ContractRequestNo>
                '.$ctOrderNo.'
                <con:PHISNo>' . $contractOrderInfo[0]->phisno . '</con:PHISNo>
                <con:ActionDate>' . Carbon::parse($actionDate)->format('Y-m-d') . '</con:ActionDate>
                <con:EPContractNo>' . $contractOrderInfo[0]->contractphysicalno . '</con:EPContractNo>
                <con:PTJCode>' . $contractOrderInfo[0]->orgcode . '</con:PTJCode>
                <con:COAmount>' . number_format((float) $amount[0]->coamount, 2, '.', '') . '</con:COAmount>
                <con:NoOfChargelines>0</con:NoOfChargelines> ';

                $payload = $payload . $header . ' 
            </con:ContractOrderInformationRq>
        </con:EPMFRq>
        </soapenv:Body>
        </soapenv:Envelope> ';
            }
        }

        return view('item_phis_160', [
            'docNo' => $carianDocNo,
            'action' => $action,
            'contractOrderInfo' => $contractOrderInfo,
            'payload' => $payload,
            'phis' =>$phisNo
        ]);
        
        

        
    }
    
    public function updateItemPhis160(Request $request) {

//          http://192.168.120.14:8011/ContractInformation/v1.2?wsdl (Dev)
//          http://192.168.63.205:7011/ContractInformation/v1.2?wsdl (Prod)
        $docCoNo = $request->doc_co_no;
        $payload = $request->payload_full;


        $client = new Client([
            'base_uri' => 'http://192.168.63.205:7011',
        ]);
        $payloadCompleted = $payload;
        $responseCompleted = $client->post('/ContractOrderInformation/v1.1', [
            //'debug' => TRUE,
            'body' => $payloadCompleted,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'updatePhis160',
            ]
        ]);

        // save action in log table
          EpSupportActionLog::saveActionLog('Trigger Item PHIS-160','Soap UI',$payloadCompleted,$docCoNo,('Completed'));
          
        $bodyPOCompleted = $responseCompleted->getBody();
        return array('docCoNo' => $docCoNo, 'payload' => $bodyPOCompleted);
    }

}
