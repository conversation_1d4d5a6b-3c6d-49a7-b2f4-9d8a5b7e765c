<?php

namespace App\Http\Controllers;

use App\Services\Traits\OSBService;
use App\Services\Traits\OSBWebService;
use App\Services\Traits\SSHService;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\EpSupportTaskService;
use SSH;
use Response;
use Illuminate\Http\Request;
use Validator;
use Carbon\Carbon;
use DB;
use Guzzle;
use Storage;
use Exception;
use App\EpSupportActionLog;
use Illuminate\Support\Facades\Log;
use stdClass;

class OSBController extends Controller {

    use OSBService;
    use SSHService;
    use FulfilmentService;
    use OSBWebService;
    use EpSupportTaskService;


    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function OSBLog() {
        return view('list_osb', [
            'listdata' => null,
            'carian' => '']);
    }
    
    public function searchOSBLog($search) {
        $list = array();
        
        /** Ignored search 18 digit refer Item Kod **/
//        if(strlen(trim($search)) == 18){
//            return view('list_osb', [
//                        'listdata' => $list,
//                        'carian' => $search]);
//        }
        
        $checkPRCR = substr($search, 0, 2);
        if($checkPRCR == 'PR' || $checkPRCR == 'CR'){
            $search = $this->getDocNoByPrCr($search);
            if($search == null){
                return view('list_osb', [
                        'listdata' => $list,
                        'carian' => $search]);
            }
        }
        
        $listTransId = $this->getTransIdOSBLoggingByRemarks($search);
        if(count($listTransId) > 0){
            $list = $this->searchOSBLogDetailsReqResByTransId($listTransId);
        }
        return view('list_osb', [
            'listdata' => $list,
            'carian' => $search]);
    }

    public function searchOSBLogDetail() {
        $list = array();

        $searchTransID = request()->cari;
        if($searchTransID != null && strlen($searchTransID) > 10){
            $list = $this->searchOSBLogDetailsByTransId($searchTransID);
        }

        return view('osb.list_osb_detail', [
            'listdata' => $list,
            'carian' => $searchTransID]);
    }
    
    public function searchOSBLogDetailByRqUid() {
        $list = array();

        $searchRqUidID = request()->cari;
        if($searchRqUidID != null && strlen($searchRqUidID) > 10){
            $list = $this->searchOSBLogDetailsByRqUid($searchRqUidID);
        }

        return view('osb.list_osb_detail_rquid', [
            'listdata' => $list,
            'carian' => $searchRqUidID]);
    }
    
    
    
    
    public function batchOSBLog() {
        return view('list_batch_file', [
            'listdata' => null,
            'objFile' => null,
            'listInvoices' => null,
            'carian' => '', 'carian2' => '']);
    }
    
    /** 
     * Trigger Files for from 1GFMAS. 
     * @return type
     */
    public function triggerProcessFileOSB() {
        $fileName = request()->file_name;
        $serviceCode = request()->service_code;
        if(strlen($fileName) < 30 &&  str($serviceCode) < 6){
            return "Invalid";
        }
        $result = collect([]);
        $result->put("file_name",$fileName);  
        $result->put("service_code",$serviceCode );  
        
        $interfaceLog = $this->getDetailDiInterfaceLog($fileName);
        if($interfaceLog == null){
            //2nd check filename is exist in BatchFile Log
            $objFile = $this->getBatchFileLog($fileName);
            if($objFile != null){
                $totalFound = $this->countFiles1GFMASFolderIN($fileName);
                if($totalFound > 0){
                    $listTransId = $this->getTransIDInterfaceLog($serviceCode);
                    if(count($listTransId) > 0){
                        $transInterfaceLog = $listTransId->random();
                        $result->put("trans_id",$transInterfaceLog->trans_id ); 
                        //$xmlContents = $thisClass->callCurlWSCallBackGfmasIN($interfaceLog->trans_id, $serviceCode, $fileName); 
                        $xmlContents = $this->callWSCallBackGfmasIN($transInterfaceLog->trans_id, $serviceCode, $fileName); 
                       
                        sleep(15);
                        $result->put("success","trigger process file in 'batch>>1GFMAS>>IN' " );  
                        $result->put("xml",$xmlContents );  
                        $result->put('data',  $this->getDetailDiInterfaceLog($fileName)); // Get response after success in di_interface_log
                    }else{
                       $result->put("error","Not trans_id available to pickup in di_interface_log " );    
                    }
                }else{
                  $result->put("error","Not found in folder 'batch/1GFMAS/IN' " );  
                }
            }else{
                $result->put("error","Not found in osb_batch_file" );
            }
        }else{
            $result->put("error","Data already exist in di_interface_log" );
        }

        return $result;
    }

    public function findOsbBatchByName($fileName){
        $request = new Request;
        $request->fileName = $fileName;
        return $this->searchBatchOSBLog($request);
    }
    
    public function searchBatchOSBLog(Request $request) {
        $fileName = $request->fileName;
        $serviceCode = isset($request->serviceCode)?$request->serviceCode: '';
        Log::info($fileName.'-'.$serviceCode);
        
        $list = array();
        $objFile = $this->getBatchFileLog($fileName);
        $listData = array();
        $objFileCompletedProcess = $this->getDetailDiInterfaceLog($fileName);
        if($objFile){
            $objFile->is_batch_file = "Created";
            $listTransId = array($objFile->trans_id);
            $list = $this->searchOSBLogDetailsAllByTransId($listTransId);
            $listData = $this->decryptFileAndConvertToJSON($fileName, $objFile->service_code);
        } else {
            $objFile = new stdClass;
            $objFile->service_code = $objFileCompletedProcess ? $objFileCompletedProcess->service_code : 'N/A';
            $objFile->created_date = $objFileCompletedProcess ? $objFileCompletedProcess->changed_date :'N/A';
            $objFile->batch_file_id = explode(".",$fileName)[0];
            $objFile->file_name = $fileName;
            $objFile->file_data = '';
            $objFile->is_batch_file = "It doesn't exist"; 

            $list = [];
            $listData = $this->decryptFileAndConvertToJSON($fileName, $serviceCode);
        }

        $listInvoice = $this->findDetailsInvoicesByAp511($fileName);

        if($listInvoice){
            foreach($listInvoice as $invObj){
                $statusObj = $this->getLatestStatusPOCOByInvoiceNo($invObj->invoice_no);
                $invObj->poco_no = $statusObj ? $statusObj->doc_no : null;
                $invObj->poco_status = $statusObj ? $statusObj->status_name : null;
                $invObj->poco_status_date = $statusObj ? $statusObj->status_created_date : null;
            }
        }

        return view('list_batch_file', [
            'listdata' => $list,
            'objFile' => $objFile,
            'objFileCompletedProcess' => $objFileCompletedProcess,
            'listInvoices' => $listInvoice,
            'listDataJson' => $listData,
            'carian' => $fileName, 'carian2' => $serviceCode]);
    }
    
    
    public function searchDocNoFileIGFMAS() {

        $listData = array();
        $docNo = request()->cari;
        if (substr($docNo, 0, 2) == 'LA') {
            //Search DocNo in AP516
            $listData = $this->findDocNoInAP516File($docNo);
        } else if (strlen($docNo) > 8) {
            //Search DocNo in AP511
            $listData = $this->findInvoiceNoInOSBFile($docNo);
        }
        return view('osb.list_file_data', [
            'listdata' => $listData,
            'carian' => $docNo]);
    }

    public function decryptFile($fileName) {

        $objFile = $this->getBatchFileLog($fileName);
        
        if($objFile){
            //return $result = $this->wsDecryptFile1GFMAS($objFile);
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
            $response = Guzzle::get($urlMiddleware."/decrypt-file?filename=$fileName");
            $res = collect([]);
            $res->push(utf8_encode($response->getBody()));
            return $res;
        }
        $result = collect([]);
        $result->push('Fail not found!  '.$fileName);
        return $result;
    }

    public function decryptFileAndConvertToJSON($filename, $service_code) {
        Log::info(__METHOD__." >>> find file to decrypt $filename  with service code $service_code ");
        $objFile = $this->getBatchFileLog($filename);
        $token = base64_encode(strtotime("now"));
        $res = collect([]);
        if($objFile){
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
            //$urlMiddleware = "http://192.168.62.132:8080/ep-support-middleware";
            $response = Guzzle::get($urlMiddleware."/batch/osb/find/idd?idd=".$filename."&service_code=".$service_code."&token=".$token);
            $res->push(utf8_encode($response->getBody()));
            
            $new_data = json_decode($res[0]);
            if($new_data && isset($new_data->result) && isset($new_data->result->list)){
               return $new_data->result->list; 
            }
            
        } else {
            Log::info('get data even osb batch file not exists!');
            $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
            //$urlMiddleware = "http://192.168.62.132:8080/ep-support-middleware";
            $response = Guzzle::get($urlMiddleware."/batch/osb/find/idd?idd=".$filename."&service_code=".$service_code."&token=".$token);
            $res->push(utf8_encode($response->getBody()));

            $new_data = json_decode($res[0]);
            if($new_data && isset($new_data->result) && isset($new_data->result->list)){
               return $new_data->result->list; 
            }
        }
        
        return null; 
        
    }
    
    public function decryptDataPendingFile($processID,$fileName) {
        
        if($processID == 'GLSEG'){
            $list = $this->getListDataPendingGLSEG($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLPTJ'){
            $list = $this->getListDataPendingGLPTJ($fileName);
            $dataList = collect($list);
        }elseif($processID == 'CMBNK'){
            $list = $this->getListDataPendingCMBNK($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLPRJ'){
            $list = $this->getListDataPendingGLPRJ($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLVOT'){
            $list = $this->getListDataPendingGLVOT($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLPRG'){
            $list = $this->getListDataPendingGLPRG($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLGLC'){
            $list = $this->getListDataPendingGLGLC($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLPCG'){
            $list = $this->getListDataPendingGLPCG($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLBAC'){
            $list = $this->getListDataPendingGLBAC($fileName);
            $dataList = collect($list);
        }elseif($processID == 'GLDNA'){
            $list = $this->getListDataPendingGLDNA($fileName);
            $dataList = collect($list);
        }
        
       // $processid = $dataList2;
      //  dump($test);
        
        return view('list_batch_pending_file', [
            'dataList' => $dataList,
            'processID'=>$processID,
        ]);
    }

    public function decryptFileToFile($fileName) {
        if($fileName == null or strlen(trim($fileName)) == 0){
            return 'Invalid';
        }

        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
        
        $responseUrl = Guzzle::get($urlMiddleware."/decrypt-file?filename=$fileName");

        //return response($response->getBody(), 200)->header('Content-Type', 'text/plain');
        
        $response = Response::make($responseUrl->getBody(), 200);
        $response->header('Content-Type', 'text/plain');
        $response->header("Content-Disposition", "attachment; filename='$fileName'");
        return $response;
        
    }
    
    public function encryptContent($content){
        $data = collect([]);
        // Check in BEGIN PGP MESSAGE
        if($content == null or strlen(trim($content)) == 0){
            $data->put("status", "failed");
            $data->put("content", "Content is EMPTY!");
            return $data;
        }
        $urlMiddleware = env("JAVA_MIDDLEWARE_RESTFUL","http://192.168.62.132:8080/ep-support-middleware");
        
        try {
            $responseUrl = Guzzle::request('POST', $urlMiddleware."/encrypt-content", [
                'form_params' => [
                    'content' => $content
                ]
            ]);
            $response = Response::make($responseUrl->getBody(), 200);
            $data->put("status", "success");
            $data->put("content", $response->content());
            return $data;
        } catch (Exception $ex) {
            $data->put("status", "failed");
            $data->put("content", $ex->getMessage());
            return $data;
        }
        
    }
    
    public function contentToFile($fileName) {
        if($fileName == null or strlen(trim($fileName)) == 0){
            return 'Invalid';
        }

        $objFile = $this->getBatchFileLog($fileName);

        //return response($response->getBody(), 200)->header('Content-Type', 'text/plain');
        
        $response = Response::make($objFile->file_data, 200);
        $response->header('Content-Type', 'text/plain');
        $response->header("Content-Disposition", "attachment; filename='$fileName'");
        return $response;
        
    }
    
    
    public function searchListErrorTransactionOSB(Request $request){
        //Ignored carian
        //return "Maaf! Carian ini dibatalkan. Gangguan, proses query ini sangat lambat.";
        
        $listServiceCode = DB::connection('oracle_nextgen_rpt')->table('osb_service')
                ->whereNotIn('service_code',['NA'])
                ->where('record_status',1)->get();
        
        $formRequest = collect($request->all());
        $dataSearch = collect([]);
        if(count($formRequest) == 0){
            return view('list_osb_error', [
            'listdata' => array(),
            'listServiceCode' => $listServiceCode
                ]);
        }else{
            Validator::make($request->all(), [
                'service_code' => 'required',
                'date_start' => 'required | date_format:"Y-m-d"|after:"2017-12-31"'
            ])->validate();
            
            $dateStartObj =  Carbon::parse($request->date_start);
            $dateStart =  $dateStartObj->format('Y-m-d');
            $dateEnd =  $dateStartObj->addDay()->format('Y-m-d');
            $dataSearch->put('service_code', $request->service_code);
            $dataSearch->put('date_start', $dateStart);
            $dataSearch->put('date_end', $dateEnd);
        }

        $list = $this->getListWsTransactionErrorBySearch($dataSearch);
        
        /** Enable this, to show error with not resolve **/
        /*
        $listRemark = collect($list)->pluck('trans_id')->unique();
        $listSuccessTrans = DB::connection('oracle_nextgen_rpt')->table('osb_logging')
                ->whereIn('trans_id',$listRemark)
                ->where('status','S')
                ->where('service_code',$request->service_code)
                ->where('trans_type','IBRes')
                ->get();
      
        if(count($listSuccessTrans)> 0){
            $listExclude = $listSuccessTrans->pluck('trans_id')->unique();
            //dd($listExclude) ;       
            $filtered = $listRemark->diffKeys($listExclude);
            $list = collect($list)->whereIn('trans_id',$filtered);
        }
        */
        
        
        session()->flashInput($request->input());
        return view('list_osb_error', [
            'listdata' => $list,
            'listServiceCode' => $listServiceCode  
                ]);
        
    }
    
    
    public function initiateUploadFile(Request $request) {

        if ($request->isMethod('post')) {
            dump($request->all());
        }
        session()->flashInput($request->input());
        return view('osb.transfer_file', [
            'data' => null,
        ]);
    }

    public function uploadFile(Request $request) {
        $data = collect([]);
        if ($request->isMethod('post')) {
            $fileName = $request->uploadFile->getClientOriginalName();
            $contentTemp =   file_get_contents($request->uploadFile->getRealPath());
            $encryptedSample = "BEGIN PGP MESSAGE";
            if (strpos($contentTemp, $encryptedSample) !== false) {  // This mean , we detect content has "BEGIN PGP MESSAGE" 
                $data->put("status_upload", "invalid");
            }else{
                $data->put("status_upload", "valid");

                $path = $request->uploadFile->storeAs(
                        $this->getPathFileClearText(), $fileName
                );
                $content = Storage::get($path);
                $data->put("file_name", $fileName);
                $data->put("content", $content);
                $data->put("path_clear_text", $path);
                
                // Start to decrypt content
                $encryptContentObj = $this->encryptContent($content);
                $data->put("is_encrypted", false);
                if($encryptContentObj->get('status') == 'success'){
                    $contentEncrypted = $encryptContentObj->get('content');
                    $data->put("is_encrypted", true);
                    $data->put("content_encrypted",$contentEncrypted );
                    
                    $pathFileEncrypted = $this->getPathFileEncryptedText().$fileName;
                    Storage::put($pathFileEncrypted, $contentEncrypted);
                   
                    $data->put("path_encrypted_text", $pathFileEncrypted);
                }else{
                    $data->put("content_encrypted", $encryptContentObj->get('content'));  //result content display as Failed.
                }
                
                return $data;
            }
        }
        return $data;
    }
    public function getPathFileClearText() {
        return "imports/osb-files/clear-text/";
    }
    public function getPathFileEncryptedText() {
        return "imports/osb-files/encrypted-text/";
    }
    public function transferFile(Request $request) {
        $data = collect([]); 
        Validator::make($request->all(), [
                'filename' => 'required'
            ])->validate();
        
        if ($request->isMethod('post')) {
            $fileName = $request->filename;
            
            $collectTypeFile = collect(['1102AR502' => 'AR502','1000AP516' => 'AP516','1000APIVE' => 'APIVE']);
            $typeFile   = substr($fileName, 0, 9);
            
            if($collectTypeFile->has($typeFile) == true ){
                
                $checkExist = EpSupportActionLog::where('action_name','TransferFile')->where('action_parameter',$fileName)->count();
                if($checkExist > 0){
                    $data->put('status', "failed");
                    $urlCheck = url('find/osb/batch/file/'.$fileName );
                    $data->put('status_description', "Failed! Could not proceed send this file because this $fileName has been transferred to 1GFMAS!. Kindly check at $urlCheck");
                    return $data; 
                }
                
                $pathFileClearText = 'app/'.$this->getPathFileClearText().$fileName;
                $pathFileEncrypted = 'app/'.$this->getPathFileEncryptedText().$fileName;

                if(Storage::exists($this->getPathFileClearText().$fileName) == false ||  Storage::exists($this->getPathFileEncryptedText().$fileName) == false){
                    $data->put('status', "failed");
                    $data->put('status_description', "Checking both files clear-text and encrypted-text is not exist in server.");
                    return $data; 
                }
                
                $fullLocalPathFileClearText = storage_path($pathFileClearText);
                $fullLocalPathFileEncrypted = storage_path($pathFileEncrypted);
                
                

                $data->put('path_encrypted_text', $fullLocalPathFileEncrypted);
                $data->put('path_clear_text', $fullLocalPathFileClearText);

                /** SEND file clear text to Temp Folder **/
                $remoteTempPath =  "/batch/Temp/".$fileName;
                $this->sendFileToEpPortalServer($remoteTempPath, $fullLocalPathFileClearText);

                /** SEND file encrypted text to OUT Folder **/
                $remoteOutPath =  "/batch/1GFMAS/OUT/".$fileName;
                $this->sendFileToEpPortalServer($remoteOutPath, $fullLocalPathFileEncrypted);
                
                /* Call web service to trigger OSB pickup file based on type */
                $this->callWSProcessFileTransfer($collectTypeFile->get($typeFile), "eP");
                
                $actionData = collect([]);
                $actionData->put("clear_text", $fullLocalPathFileClearText);
                $actionData->put("encrypted_text", $fullLocalPathFileEncrypted);
                $actionData->put("batch_temp", $remoteTempPath);
                $actionData->put("batch_1gfmas_out", $remoteOutPath);
                
                EpSupportActionLog::saveActionLog("TransferFile", "Script", $actionData, $fileName, "Completed");
                
                $data->put('status', "success");
                $data->put('filename', $fileName);
                $data->put('status_description', "Successfully Transferred File!");
                return $data;
            }else{
                $data->put('status', "failed");
                $data->put('status_description', "Failed! Type of this file is not allowed!");
                return $data;
            }
        }
        $data->put('status', "failed");
        $data->put('status_description', "Failed! Error on validation.");
        return $data;
    }
    
    protected function sendFileToEpPortalServer($remotePath,$fullLocalPathFileClearText){

        SSH::into('portal')->put($fullLocalPathFileClearText, $remotePath);

        SSH::into('portal')->run([
            "chmod 666 $remotePath"
        ]);
    }
    
    public function transferFile1GFMASAndEp($sourcetarget,$typefile) {
         $result = $this->callWSProcessFileTransfer($typefile, $sourcetarget);
         return $result;
    }
    
    public function callBackEPToProcessFileFolderIN() {
        $transID = request()->trans_id;
        $serviceCode = request()->service_code; 
        $fileName  =request()->file_name ;
        $result = $this->callWSCallBackEPProcessFileInFolder($transID,$serviceCode,$fileName);
         return $result;
    }
    
     public function osbTransIDPayload($transID) {
        $list = array();
       
        if($transID != null && strlen($transID) > 10){
            $list = $this->searchOSBLogPayloadByTransId($transID);
        }

        return view('osb.list_payload_updatesoftcert', [
            'listdata' => $list
        ]);
    }
    
    
    /** 
     * Trigger OSB to pickup files in OUT Folder eP.
     * @return type
     */
    public function triggerOSBPickupFileOutFolder() {
        $fileName = request()->file_name;
        $serviceCode = request()->service_code;
        $uuid  = \Ramsey\Uuid\Uuid::uuid4();
        
        $result = collect([]);
        $result->put("uuid",$uuid);  
        $result->put("file_name",$fileName);  
        $result->put("service_code",$serviceCode );  

        $resp = $this->callWSPickupFileOutFolderEP($uuid,$serviceCode,$fileName);

        $result->put('data',$resp);
            
        dump($result);

    }
    
    public function triggerOSBPickupEperunding() {
        $folder = request()->folder;
        
        if(strlen($folder) != 8){
            return 'invalid folder';
        }
        /*
        EPE01	ConsultantProfiles.txt
        EPE02	AssociatedCompany.txt
        EPE03	CompanyOwner.txt
        EPE04	EquityOwner.txt
        EPE05	BoardOfDirector.txt
        EPE06	ManagementPersonnel.txt
        EPE07	Signatory.txt
        EPE08	BranchCompany.txt
        EPE09	RegisteredCategory.txt
        EPE10	GovernmentProject.txt
        EPE11	DeletedCompany.txt
        */
                
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE01',$folder.'/ConsultantProfiles.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE02',$folder.'/AssociatedCompany.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE03',$folder.'/CompanyOwner.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE04',$folder.'/EquityOwner.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE05',$folder.'/BoardOfDirector.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE06',$folder.'/ManagementPersonnel.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE07',$folder.'/Signatory.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE08',$folder.'/BranchCompany.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE09',$folder.'/RegisteredCategory.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE10',$folder.'/GovernmentProject.txt');
        dump($resp);
        
        $resp = $this->callWSPickupFileOutFolderEP(\Ramsey\Uuid\Uuid::uuid4(),'EPE11',$folder.'/DeletedCompany.txt');
        dump($resp);
        
        dump('Completed');
    }
   
    public function getTotalOsbByTarget(Request $request) {
        
        $serviceType = $request->serviceType;
        $targetName = $request->targetName;
        $total = 0;
        
        $listTrigger = $this->getTotalTarget($serviceType,$targetName);

        $dataTrigger = collect($listTrigger);
      
        foreach($dataTrigger as $val) {
            if($val->target_system === $targetName) {
                $total = $val->total;
            }
        }
        return $total;
    }
    
    public function getTotalOsbByService(Request $request) {
        
        $serviceType = $request->serviceType;
        $serviceName = $request->serviceName;
        $total = 0;
        
        $listTrigger = $this->getTotalService($serviceType,$serviceName);

        $dataTrigger = collect($listTrigger);
      
        foreach($dataTrigger as $val) {
            if($val->service_name === $serviceName) {
                $total = $val->total;
            }
        }
        return $total;
    }

}
