<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\Traits\OSBService;
use DB;
use Log;
use Carbon\Carbon;

class MasterDataController extends Controller {

    use OSBService;

    public function project(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->project_code !== null){
            $result = $this->masterData('PROJECT', $request->project_code, null, null);
        }
        return view('masterdata.project', [
            'date' => null,
            'result' => $result
        ]);
    }
    public function projectDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){

            $type = $request->master_data_type;
            $projectCode = $request->project_code;
            $projetDesc = $request->project_desc;
            $createdDate = $request->created_date;

            if ($projectCode === null && $projetDesc === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $projectCode, $projetDesc, $createdDate);
        }

        return view('masterdata.project', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function interfacelog(Request $request) {
        //dump($request->all());
        session()->flashInput(request()->input());
        $listServiceCode = DB::connection('oracle_nextgen_rpt')
                ->select("select distinct service_code from DI_INTERFACE_LOG order by service_code asc");
        //dump($listServiceCode);
        $listProcessId = DB::connection('oracle_nextgen_rpt')
                ->select("select distinct process_id from DI_INTERFACE_LOG order by process_id asc");
        //dump($listProcessId);
        $filename = $request->filename;
        
        $result = null;
        if ($request->method()=='POST') {
            $type = $request->master_data_type;
            $serviceCode = $request->service_code;
            $processId = $request->process_id;
            $filename = $request->filename;
            $createdDate = $request->created_date;
            
            $result = $this->masterData('INTERFACE_LOG', $serviceCode, $processId, $filename, $createdDate);
        }
        $data = [
            'listServiceCode' => $listServiceCode,
            'listProcessId' => $listProcessId,
            'result' => $result
        ];
    
        return view('masterdata.interface_log', $data);
    }

    public function ptj(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->ptjcode !== null){
            $result = $this->masterData('PTJ', $request->ptjcode, null, null);
        }
        return view('masterdata.ptj', [
            'date' => null,
            'result' => $result
        ]);
    }
    
    public function ptjDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $ptjCode = $request->ptjcode;
            $ptjName = $request->ptjname;
            $createdDate = $request->created_date;

            if ($ptjCode === null && $ptjName === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $ptjCode, $ptjName, $createdDate);
        }
        
        return view('masterdata.ptj', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function kumpptj(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->kumpptjcode !== null){
            $result = $this->masterData('KUMP_PTJ', $request->kumpptjcode, null, null);
        }
        return view('masterdata.kump_ptj', [
            'date' => null,
            'result' => $result]);
    }
    public function kumpptjDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $kumpPtjCode = $request->kumpptjcode;
            $kumpPtjName = $request->kumpptjname;
            $createdDate = $request->created_date;

            if ($kumpPtjCode === null && $kumpPtjName === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }

            $result = $this->masterData($type, $kumpPtjCode, $kumpPtjName, $createdDate);
        }
        return view('masterdata.kump_ptj', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }
    
    public function pegpengawal(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->ppcode !== null){
            $result = $this->masterData('PEG_PENGAWAL', $request->ppcode, null, null);
        }
        return view('masterdata.peg_pengawal', [
            'date' => null,
            'result' => $result]);
    }

    public function pegpengawalDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $ppcode = $request->ppcode;
            $ppname = $request->ppname;
            $createdDate = $request->created_date;

            if ($ppcode === null && $ppname === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }

            $result = $this->masterData($type, $ppcode, $ppname, $createdDate);
        }
        return view('masterdata.peg_pengawal', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function vot(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->votcode !== null){
            $result = $this->masterData('VOT', $request->votcode, null, null);
        }
        return view('masterdata.vot', [
            'date' => null,
            'result' => $result]);
    }
    public function votDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $votcode = $request->votcode;
            $votname = $request->votname;
            $createdDate = $request->created_date;

            if ($votcode === null && $votname === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }

            $result = $this->masterData($type, $votcode, $votname, $createdDate);
        }
        return view('masterdata.vot', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function votType($fundType, $fundId) {

        $result = $this->votFundDetails($fundType, $fundId);

        return $result;
    }
    
    public function glaccount(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->glcode !== null){
            $result = $this->masterData('GL_ACCOUNT', $request->glcode, null, null, null);
        }
        return view('masterdata.gl_account', [
            'date' => null,
            'result' => $result]);
    }

    public function glaccountDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $glcode = $request->glcode;
            $glname = $request->glname;
            $kptjcode = $request->kptjcode;
            $createdDate = $request->created_date;

            if ($glcode === null && $glname === null && $kptjcode === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $glcode, $glname, $kptjcode, $createdDate);
        }

        return view('masterdata.gl_account', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function programactivity(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->prgcode !== null) {
            $result = $this->masterData('PROGRAM_ACTIVITY', $request->prgcode, null, null);
        }
        return view('masterdata.program_activity', [
            'date' => null,
            'result' => $result
        ]);
    }
    
    public function programactivityDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $prgcode = $request->prgcode;
            $prgname = $request->kptjcode;
            $createdDate = $request->created_date;

            if ($prgcode === null && $prgname === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $prgcode, $prgname, $createdDate);
        }
        return view('masterdata.program_activity', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function agoffice(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->officecode !== null){
            $result = $this->masterData('AG_OFFICE', $request->officecode, null, null);
        }
        return view('masterdata.ag_office', [
            'date' => null,
            'result' => $result
        ]);
    }
    public function agofficeDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $officecode = $request->officecode;
            $officename = $request->officename;
            $createdDate = $request->created_date;

            if ($officecode === null && $officename === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $officecode, $officename, $createdDate);
        }

        return view('masterdata.ag_office', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }
    
    public function dana(Request $request) {
        session()->flashInput(request()->input());
        $result = null;
        
        if($request->danacode !== null){
            $result = $this->masterData('DANA', $request->danacode, null, null);
        }
        return view('masterdata.dana', [
            'date' => null,
            'result' => $result
        ]);
    }

    public function danaDetail(Request $request) {

        session()->flashInput(request()->input());
        $createdDate = null;
        $result = null;
        if ($request->method()=='POST'){
            $type = $request->master_data_type;
            $danacode = $request->danacode;
            $dananame = $request->dananame;
            $createdDate = $request->created_date;

            if ($danacode === null && $dananame === null && $createdDate === null) {
                $createdDate = Carbon::now()->format('Y-m-d');
            }
            $result = $this->masterData($type, $danacode, $dananame, $createdDate);
        }
        return view('masterdata.dana', [
            'result' => $result,
            'date' => $createdDate
        ]);
    }

    public function epData(Request $req) {

        $result = array();
        $projectCode = null;
        $setiaCode = null;
        $type = null;
        session()->flashInput(request()->input());

        if ($req->code !== null && $req->type !== null) {
            $code = $req->code;
            $type = $req->type;
            $result = $this->epMasterData($type, $code);
        }

        return view('masterdata.epdata', [
            'type' => $type,
            'result' => $result,
            'valueCode' => $projectCode,
            'setiaCode' => $setiaCode
        ]);
    }

    public function epDataDetail(Request $req) {

        $id = $req->id;
        $vottype = $req->vottype;
        $type = $req->type;
        $valueCode = $req->valuecode;
        $setiaCode = $req->setiacode;
        
        $result = array();

        if ($type === 'vot') {
            if ($vottype === 'B' || $vottype === 'T') {
                $query = DB::connection('oracle_nextgen_rpt')->table('pm_prg_activity')
                        ->where('vot_fund_id', $id)
                        ->where('record_status', '=', 1);
                if ($valueCode !== '') {
                    $query->where('prg_activity_code', 'like', '%' . $valueCode . '%');
                }
                $result = $query->get();
            } else if ($vottype === 'P' || $vottype === 'S') {
                $query = DB::connection('oracle_nextgen_rpt')->table('pm_sub_setia')
                        ->where('vot_fund_id', $id)
                        ->where('record_status', '=', 1);
                if ($valueCode !== '') {
                    $query->where('project_code', 'like', '%' . $valueCode . '%');
                }
                if ($setiaCode !== '') {
                    $query->where('setia_code', 'like', '%' . $setiaCode . '%');
                }
                $result = $query->get();
            }
        } else if ($type === 'org') {
            $result = DB::connection('oracle_nextgen_rpt')->table('pm_org_profile op')
                    ->join('pm_org_validity ov', 'op.org_profile_id', '=', 'ov.org_profile_id')
                    ->where('op.org_profile_id', $id)
                    ->select('op.*', 'ov.org_validity_id', 'ov.org_code', 'ov.org_name', 'ov.record_status as ov_record_status')
                    ->addSelect('ov.ag_office_id')
                    ->get();
        } else if ($type === 'ag') {
            $result = DB::connection('oracle_nextgen_rpt')->table('pm_ag_validity')
                    ->where('ag_validity_id', $id)
                    ->get();
        }

        return $result;
    }

}
