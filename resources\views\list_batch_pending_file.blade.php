@extends('layouts.guest-dash')
@section('content')
<div class="block block-alt-noborder full">
    <!-- List OSB Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Data Files Pending in Processing to eP</strong></h1>
        </div>
        <div class="table-responsive">
            <table id="ws-datatable" class="table table-vcenter table-condensed table-bordered">             
                @if($processID == 'GLSEG')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">PP DESC</th>
                        <th class="text-center">PP CODE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">RECORD STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>                
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center">{{ $data->pp_desc }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/pegpengawal?ppcode=")}}{{ $data->pp_code }}" target="_blank">{{ $data->pp_code }}</a></td>
                        <td class="text-left">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->record_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                     @endforeach
                </tbody>
                @elseif($processID == 'GLPTJ')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">PTJ DESC</th>
                        <th class="text-center">PTJ CODE</th>
                        <th class="text-center">AG CODE</th>
                        <th class="text-center">PP CODE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">RECORD STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center">{{ $data->ptj_desc }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/ptj?ptjcode=")}}{{ $data->ptj_code }}" target="_blank">{{ $data->ptj_code }}</a></td>
                        <td class="text-center">{{ $data->ag_code }}</td>
                        <td class="text-center">{{ $data->pp_code }}</td>
                        <td class="text-left">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->record_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'CMBNK')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">BANK CODE</th>
                        <th class="text-center">BANK NAME</th>
                        <th class="text-center">INDICATOR</th>
                        <th class="text-center">ACTION</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center">{{ $data->bank_code }}</td>
                        <td class="text-center">{{ $data->bank_name }}</td>
                        <td class="text-center">{{ $data->indicator }}</td>
                        <td class="text-center">{{ $data->action }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLPRJ')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">PROJECT CODE</th>
                        <th class="text-center">PROJECT NAME</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">EFFECTIVE DATE</th>
                        <th class="text-center">EXPIRY DATE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/project?project_code=")}}{{ $data->project_code }}" target="_blank">{{ $data->project_code }}</a></td>
                        <td class="text-center">{{ $data->project_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->eff_date }}</td>
                        <td class="text-center">{{ $data->exp_date }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLVOT')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">VOT CODE</th>
                        <th class="text-center">VOT DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">EFFECTIVE DATE</th>
                        <th class="text-center">EXPIRY DATE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/vot?votcode=")}}{{ $data->vot_code }}" target="_blank">{{ $data->vot_code }}</a></td>
                        <td class="text-center">{{ $data->vot_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->eff_date }}</td>
                        <td class="text-center">{{ $data->exp_date }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLPRG')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">PROGRAM ACTIVITY CODE</th>
                        <th class="text-center">PROGRAM ACTIVITY DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">EFFECTIVE DATE</th>
                        <th class="text-center">EXPIRY DATE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/programactivity?prgcode=")}}{{ $data->prg_act_code }}" target="_blank">{{ $data->prg_act_code }}</a></td>
                        <td class="text-center">{{ $data->prg_act_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->eff_date }}</td>
                        <td class="text-center">{{ $data->exp_date }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLGLC')<thead>
                    <tr>
                        <th class="text-center">NO.</th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">GL ACCOUNT CODE</th>
                        <th class="text-center">KPTJ CODE</th>
                        <th class="text-center">GL ACCOUNT DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/glaccount?glcode=")}}{{ $data->gl_account_code }}" target="_blank">{{ $data->gl_account_code }}</a></td>
                        <td class="text-center">{{ $data->kptj_code }}</td>
                        <td class="text-center">{{ $data->gl_account_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>                
                @elseif($processID == 'GLPCG')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">KPTJ CODE</th>
                        <th class="text-center">KPTJ DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">EFFECTIVE DATE</th>
                        <th class="text-center">EXPIRY DATE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/kumpptj?kumpptjcode=")}}{{ $data->kptj_code }}" target="_blank">{{ $data->kptj_code }}</a></td>
                        <td class="text-center">{{ $data->kptj_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->eff_date }}</td>
                        <td class="text-center">{{ $data->exp_date }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLBAC')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">OFFICE CODE</th>
                        <th class="text-center">OFFICE DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/agoffice?officecode=")}}{{ $data->office_code }}" target="_blank">{{ $data->office_code }}</a></td>
                        <td class="text-center">{{ $data->office_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @elseif($processID == 'GLDNA')
                <thead>
                    <tr>
                        <th class="text-center">NO. </th>
                        <th class="text-center">PROCESS ID</th>
                        <th class="text-center">FILENAME</th>
                        <th class="text-center">DANA CODE</th>
                        <th class="text-center">DANA DESCRIPTION</th>
                        <th class="text-center">FINANCIAL YEAR</th>
                        <th class="text-center">EFFECTIVE DATE</th>
                        <th class="text-center">EXPIRY DATE</th>
                        <th class="text-center">CODE STATUS</th>
                        <th class="text-center">CREATED DATE</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach ($dataList as $indexKey => $data)
                    <tr>
                        <td class="text-center">{{ ++$indexKey }}</td>
                        <td class="text-center">{{ $data->process_id }}</td>
                        <td class="text-center"><a href="{{url("/find/masterdata/interfacelog?filename=")}}{{ $data->file_name }}" target="_blank">{{ $data->file_name }}</a></td>
                        <td class="text-center"><a href="{{url("/find/masterdata/dana?danacode=")}}{{ $data->dana_code }}" target="_blank">{{ $data->dana_code }}</a></td>
                        <td class="text-center">{{ $data->dana_desc }}</td>
                        <td class="text-center">{{ $data->financial_year }}</td>
                        <td class="text-center">{{ $data->eff_date }}</td>
                        <td class="text-center">{{ $data->exp_date }}</td>
                        <td class="text-center">{{ $data->code_status }}</td>
                        <td class="text-center">{{ $data->created_date }}</td>
                    </tr>
                    @endforeach
                </tbody>
                @else
                @endif
            </table>
        </div>        
    </div>
    <!-- END List OSB Block -->
    


</div>

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });</script>


@endsection
