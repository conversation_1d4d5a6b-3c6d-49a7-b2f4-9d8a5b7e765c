@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/logtrace-v1.0.css') }}" rel="stylesheet" />
@endsection

@section('content')
    <div class="block log-header">
        <div class="row">
            <div class="col-md-1">
                <div class="log-trace-icon">eP</div>
            </div>
            <div class="col-md-4 log-header-title">
                <span>Login History Log<br /></span>
                <small>To Track Historical Login History User</small>
            </div>
            <div class="col-md-6 log-header-menu">
                <a href="/home"><i class="fa fa-home"></i></a> |
                <a href="/log/dashboard">Dashboard</a> |
                <a href="/log">User Activity</a> | 
                <span class="active">Login History</span> | 
                @if (Auth::user()->isDevUsersEp()) <a href="/log/patch">Patch
                        Management</a> @endif
            </div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="block log-search-panel">
        <div class="card-header">
            <i class="fa fa-search"></i> <strong>Search</strong> Log
        </div>
        <div class="card-body">
            <form class="form-horizontal">
                {{ csrf_field() }}
                <div class="row">
                    <div class="col-md-6">
                       
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="user_id">Login Name </label>
                                <div class="col-md-6">
                                    <input type="text" id="user_id" name="user_id" class="form-control" @if ($user_id !== null) value="{{ $user_id }}" @else value="{{ old('user_id') }}" @endif required=""><br />
                                    <small id="fail_status" style="display: none; color: red;"><b><i
                                                class="fa fa-times-circle"></i></i> Oops! <span
                                                id="fail_msg"></span></b></small>
                                                <span class="pull-right">
                                        <button type="button" name="searchlog" id="searchlog" class="btn btn-sm"><i
                                                class="fa fa-search"></i>
                                            Search</button>
                                    </span>
                                </div>
                            </div>
                     
                    </div>
                    <div class="col-md-6">
                        <fieldset>
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="year_search">Year </label>
                                <div class="col-md-6">
                                    <input type="text" id="year_search" placeholder="2019 or onwards" name="year_search" class="form-control" @if ($year_search !== null) value="{{ $year_search }}" @else value="{{ old('year_search') }}" @endif>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="block" id="log-bg-table">
        <table id="log_table" class="table table-condensed">
            <thead>
                <tr>
                    <th class="text-left">Log Date</th>
                    <th class="text-left">Login Name</th>
                    <th class="text-left">Transaction Method</th>
                    <th class="text-left">Duration</th>
                </tr>
            </thead>
        </table>
    </div>
@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        function search_logs(req) {
            $('#log_table').dataTable({
                "destroy": true,
                "processing": true,
                "serverSide": true,
                "order": [
                    [0, "asc"]
                ],
                "lengthMenu": [
                    [10, 20, 50, 100, -1],
                    [10, 20, 50, 100, 'All']
                ],
                "ajax": {
                    "url": "/log/login-history/",
                    "data": {
                        "_token": $('input[name=_token]').val(),
                        "user_id": req.user_id,
                        "year_search": req.year_search,
                    },
                    "type": "POST"
                },
                "columns": [{
                        "data": "date_time"
                    },
                    {
                        "data": "user_details"
                    },
                    {
                        "data": "transaction"
                    },
                    {
                        "data": "duration_result"
                    }
                ],
                columnDefs: [{
                        targets: [1],
                        render: function(data, type, row) {
                            return `<a href="{{ url('/find/userlogin?login_id=${data}') }}" target="_blank">${data }</a>`
                        }
                    }
                ]
            });
        }


        function showHideAlertMessage(id, element, msg) {
            $('#' + id).css('display', 'block');
            document.getElementById(element).innerHTML = msg;
            setTimeout(function() {
                $('#' + id).css('display', 'none');
            }, 3000);
        }

        $(function() {
            $('#page-container').removeAttr('class');

            $("#dt_from").datepicker({
                format: 'yyyy-mm-dd'
            });

            $("#dt_to").datepicker({
                format: 'yyyy-mm-dd'
            });

            $('#dt_from').on('change', function() {
                let select_dt = $(this).val();

                $('#dt_to').val(select_dt);
            })

            $('#searchlog').on('click', function() {
                let request = {
                    user_id: $('#user_id').val(),
                    year_search: $('#year_search').val(),
                }

                if (request.user_id === '' || request.user_id == null) {
                    showHideAlertMessage('fail_status', 'fail_msg', 'Please fill in User Id.');
                    return;
                }

                
                if (request.year_search === '' || request.year_search == null) {
                    showHideAlertMessage('fail_status', 'fail_msg', 'Please fill in Year Search.');
                    return;
                }

                search_logs(request);
            })

        });
    </script>
@endsection
