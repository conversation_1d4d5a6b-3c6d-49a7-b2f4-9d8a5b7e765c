<?php

/*
 * To change this license header, choose License Headers in Project Properties.
 * To change this template file, choose Tools | Templates
 * and open the template in the editor.
 */

namespace App\Migrate\ProdSupport;

use Carbon\Carbon;
use DB;
use App\Migrate\MigrateUtils;
use App\Services\Traits\ProdSupport\RptStatTransactionEpService;

class MigrateReportStatTransactionEp {

    use RptStatTransactionEpService;

    public static function run($year = null, $month = null) {
        ini_set('memory_limit', '-1');
        MigrateUtils::logDump(self::class . ' Starting ... ' . __FUNCTION__);
        $dtStartTime = Carbon::now();

        $reportStat = new MigrateReportStatTransactionEp;
        $reportStat->executeReportStatistic($year, $month);

        MigrateUtils::logDump(self::class . ' Completed ' . __FUNCTION__ . ' --- Taken Time :', [ 'Time' => json_encode(MigrateUtils::getTakenTime($dtStartTime))]);
    }

    /**
     * Execution parameter for last month.(Start Date to End Date)
     * @return type
     */
    protected function executeReportStatistic($year = null, $month = null) {
        $startExecutionScript = Carbon::now();

        //Set as Default
        $dateStart = Carbon::now()->subMonth()->startOfMonth();
        $dateEnd = Carbon::now()->subMonth()->endOfMonth();

        //Overwrite default of parameter not null
        if ($year != null && strlen(intval($year)) === 4 && $month != null && strlen(intval($month)) <= 2) {
            $dateStart = Carbon::create($year, $month, 1);
            $dateEnd = Carbon::create($year, $month, 1)->endOfMonth();
        }


        MigrateUtils::logDump('startExecutionScript :' . $startExecutionScript->format('Y-m-d H:i:s'));
        MigrateUtils::logDump('dateStart :' . $dateStart->format('d-F-Y'));
        MigrateUtils::logDump('dateEnd :' . $dateEnd->format('d-F-Y'));

        try {
            
            $dataSuppNew = $this->getTotalStatTS01SupplierApplication(1, $dateStart, $dateEnd, "N");
            $this->migrateDataIntoLogTableReport($dataSuppNew, $startExecutionScript);

            $dataSuppRenew = $this->getTotalStatTS01SupplierApplication(2, $dateStart, $dateEnd, "R");
            $this->migrateDataIntoLogTableReport($dataSuppRenew, $startExecutionScript);

            $dataSuppBumi = $this->getTotalStatTS01SupplierApplication(3, $dateStart, $dateEnd, "B");
            $this->migrateDataIntoLogTableReport($dataSuppBumi, $startExecutionScript);

            $dataSuppAddBidang = $this->getTotalStatTS01SupplierApplication(4, $dateStart, $dateEnd, "A");
            $this->migrateDataIntoLogTableReport($dataSuppAddBidang, $startExecutionScript);

            $dataSuppUpdProfile = $this->getTotalStatTS01SupplierApplication(5, $dateStart, $dateEnd, "U");
            $this->migrateDataIntoLogTableReport($dataSuppUpdProfile, $startExecutionScript);

            $dataDirectPurchase = $this->getTotalStatTS06DirectPurchase(6, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataDirectPurchase, $startExecutionScript);

            $dataQTQuotation = $this->getTotalStatTS07QuotationTenderByProcurementMode(7, $dateStart, $dateEnd, 185);
            $this->migrateDataIntoLogTableReport($dataQTQuotation, $startExecutionScript);

            $dataQTTender = $this->getTotalStatTS07QuotationTenderByProcurementMode(8, $dateStart, $dateEnd, 186);
            $this->migrateDataIntoLogTableReport($dataQTTender, $startExecutionScript);

            $dataFlContract = $this->getTotalStatTS09FulfilmentContract(9, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataFlContract, $startExecutionScript);

            $dataQTBidding = $this->getTotalStatTS10QuotationTenderByEbidding(10, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataQTBidding, $startExecutionScript);

            $dataMedAccessBySupp = $this->getTotalStatTS11MediumAccessEpBySupplier(11, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataMedAccessBySupp, $startExecutionScript);

            $dataSuppCatalog = $this->getTotalStatTS12SupplierCatalog(12, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataSuppCatalog, $startExecutionScript);

            $dataApplAccessCompProfil = $this->getTotalStatTS13ApplAccessCompanyProfil(13, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataApplAccessCompProfil, $startExecutionScript);

            $dataSvySatisfactionCompReg = $this->getTotalStatTS14SurveySatisfactionCompReg(14, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataSvySatisfactionCompReg, $startExecutionScript);

            $dataApplAccessMarketResearch = $this->getTotalStatTS15ApplAccessMarketResearch(15, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataApplAccessMarketResearch, $startExecutionScript);

            $dataMarketResearch = $this->getTotalStatTS16MarketResearch(16, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataMarketResearch, $startExecutionScript);

            $dataNotifyQTCancellation = $this->getTotalStatTS17NotificationQuotationTenderCancellation(17, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataNotifyQTCancellation, $startExecutionScript);
            
            
            $dataStatEpolByUser = $this->getTotalStatTS18EpolByUser(18, $dateStart, $dateEnd);
            $this->migrateDataIntoLogTableReport($dataStatEpolByUser, $startExecutionScript);
            

            /**
             * RECHECK ETL LOG REPORT IS VALID . RUN 18 run scripts
             */
            $yearData = $dateStart->year;
            $monthData = $dateStart->month;

            $totalReportETL = $this->checkTotalETLTransactionReportValid($yearData, $monthData, $startExecutionScript);
            if ($totalReportETL === 18) {
                /**
                 * Check on Fact Table. If not exist. then insect into fact table.
                 */
                $countStatExist = $this->checkTotalStatFactTransaction($yearData, $monthData);
                if ($countStatExist === 0) {
                    MigrateUtils::logDump('Check not exist in fact table. start to copy data.');
                    //Copy result on Log Table into Fact Table.
                    $this->copyStatisticETLLogToFactTable($yearData, $monthData, $startExecutionScript);
                    MigrateUtils::logDump('Completed copy file.');
                } else {
                    MigrateUtils::logDump('Already have data in fact table. No need copy data.');
                }
            } else {
                MigrateUtils::logDump('Kindly check, Script run not equal as 18 script. ');
            }
        } catch (Exception $ex) {
            MigrateUtils::logErrorDump($ex->getMessage());
            MigrateUtils::logErrorDump($ex->getTraceAsString());
            MigrateUtils::logErrorDump(json_encode($ex->getTrace()));
        }
    }

    protected function migrateDataIntoLogTableReport($data, $startExecutionScript) {
        MigrateUtils::logDump($data);
        DB::connection('mysql_ep_prod_support')
                ->table('ps_ep_service_log_rpt')
                ->insert(
                        [
                            "execution_datetime" => $startExecutionScript,
                            "execution_duration" => $data->get('duration'),
                            "year" => $data->get('data_year'),
                            "month" => strtoupper($data->get('data_month_full')),
                            "bil_no" => $data->get('bil_no'),
                            "service_name_bm" => self::$SERVICE_NAME_BM[$data->get('bil_no')],
                            "service_name_bi" => self::$SERVICE_NAME_BI[$data->get('bil_no')],
                            "online_offline" => 'ONLINE',
                            "total" => $data->get('total'),
                            "month_no" => $data->get('data_month_no'),
                            "created_at" => Carbon::now(),
                            "created_by" => "ETL Program",
                            "script" => json_encode($data->get('script')),
                        ]
        );
    }

    public static $SERVICE_NAME_BM = array(
        '' => '',
        1 => 'Permohonan baru pendaftaran syarikat',
        2 => 'Permohonan pembaharuan pendaftaran syarikat',
        3 => 'Permohonan status Bumiputra',
        4 => 'Permohonan tambah bidang',
        5 => 'Permohonan kemaskini profil',
        6 => 'Perolehan barangan/Perkhidmatan melalui kaedah Pembelian Terus',
        7 => 'Perolehan barangan/Perkhidmatan melalui kaedah Tender',
        8 => 'Perolehan barangan/Perkhidmatan melalui kaedah Sebut Harga',
        9 => 'Perolehan barangan/Perkhidmatan dibelanjakan melalui kaedah Kontrak Kementerian',
        10 => 'Perolehan barangan/Perkhidmatan melalui kaedah eBidding',
        11 => 'Permohonan Medium Akses eP (untuk pembekal)',
        12 => 'Perkhidmatan memaparkan/mengemaskini katalog',
        13 => 'Permohonan ID dan Kata Laluan untuk akses profil syarikat',
        14 => 'Perkhidmatan kajian kepuasan pelanggan dalam pendaftaran syarikat',
        15 => 'Permohonan ID dan Kata Laluan untuk capaian modul kajian pasaran',
        16 => 'Perkhidmatan Kajian Pasaran',
        17 => 'Perkhidmatan pemberitahuan berhubung pembatalan sebutharga /tender/eBidding',
        18 => 'Sistem pembelajaran kepada pengguna kerajaan dan pembekal (ePOL)',
    );
    public static $SERVICE_NAME_BI = array(
        '' => '',
        1 => 'New Application For Registration',
        2 => 'Application for Renewal of Registration of Company',
        3 => 'Application for Bumiputera Status',
        4 => 'Application to add Registration Codes',
        5 => 'Application to Update MOF Account Profile',
        6 => 'Direct Purchase of Supplies and Services',
        7 => 'Procurement of Supplies and Services Through/Vide Tender',
        8 => 'Procurement of Supplies and Services Through/Vide Quotation',
        9 => 'Procurement of Supplies and Services Through/Vide Ministrial Contract',
        10 => 'Procurement of Supplies and Services Through/Vide eBidding',
        11 => 'Application to Access the eP System (For Suppliers)',
        12 => 'Displaying Catalogues And Updating of Catalouges',
        13 => 'Application to Obtain ID and Password to Access Company Profile',
        14 => 'Supplier Satisfaction Survey in Company Registration',
        15 => 'Application of ID and Password to Access Market Survey Module',
        16 => 'Market Survey Service',
        17 => 'Cancellation of Quotation/Tender/eBidding Notification',
        18 => 'eProcurement Online Learning for Government Users and Suppliers'
    );

}
