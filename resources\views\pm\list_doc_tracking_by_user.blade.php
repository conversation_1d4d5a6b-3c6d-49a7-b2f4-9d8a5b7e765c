@extends('layouts.guest-dash')

@section('header')

@endsection


@section('content')

    <div class="content-header">
        <div class="header-section">
            <h1>
                <i class="gi gi-search"></i>Tracking Diary By User<br>
            </h1>
        </div>
    </div>
    <div class="block block-alt-noborder full">
        <div class="block">
            <div class="row" >
                <div class="col-sm-12">
                    <div class="block">
                        <div class="block-title">
                            <h2><strong>Search</strong></h2>
                        </div>

                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        <form action="{{url('/find/trans/track/user')}}" method="post" class="form-horizontal form-bordered" >
                            {{ csrf_field() }}
                            <div class="form-group">
                                <div class="input-group">
                                    <input type="text" id="login_id" name="login_id" class="form-control" placeholder="LOGIN ID PENGGUNA" 
                                           required="required" value="{{old('login_id')}}">
                                    <span class="input-group-addon"><i class="fa fa-file-text"></i>  LOGIN ID</span>       
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="input-group input-daterange" data-date-format="yyyy/mm/dd">
                                    <input type="text" id="date_from" name="date_from" class="form-control text-center" placeholder="DATE FROM" 
                                        value="{{old('date_from')}}" >
                                    <span class="input-group-addon"><i class="fa fa-angle-right"></i></span>
                                    <input type="text" id="date_to" name="date_to" class="form-control text-center" placeholder="DATE TO"
                                        value="{{old('date_from')}}" >
                                </div>
                            </div> 
                            <div class="form-group form-actions">
                                <div class="col-md-12">
                                    <button type="submit" class="btn btn-sm btn-info pull-right">Search</button>
                                </div>
                            </div>
                        </form>
                    </div>

                </div>
            </div>

        </div>
    </div>
    @if($listdata != null)
        <div class="block block-alt-noborder full">
            <!-- Customer Addresses Block -->
            <div class="block">
                <div class="block-title panel-heading epss-title-s1">
                    <h1><i class="fa fa-building-o"></i> <strong>Result</strong>
                        <small>Maximum record to show : 1000 records.</small>
                    </h1>
                </div>
                
                
                @if(count($listdata) > 0)
                <div class="row">
                    <div class="col-lg-6">
                        <div class="table-responsive">
                            <table class="table table-bordered table-vcenter" style="width:100%;">
                                <tbody>
                                    <tr>
                                        <td width="10%">Login ID</td>
                                        <td><strong><a href="{{url('find/userlogin')}}/?login_id={{$listdata[0]->login_id}}" 
                                            target="_blank" > {{$listdata[0]->login_id}}</a></strong></td>
                                    </tr>
                                    <tr>
                                        <td width="10%">Name</td>
                                        <td><strong>{{$listdata[0]->user_name}}</strong></td>
                                    </tr>
                                    <tr>
                                        <td width="10%">Identification No.</td>
                                        <td><strong>{{$listdata[0]->identification_no}}</strong></td>
                                    </tr>
                                    <tr>
                                        <td width="10%">Email</td>
                                        <td><strong>{{$listdata[0]->email}}</strong></td>
                                    </tr>
                                    @if(count($user) > 0)
                                    <tr>
                                        <td width="10%">Last Login Date</td>
                                        <td><strong>{{$user[0]->last_login_date}}</strong></td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>  
                        </div> 
                    </div>
                    <div class="col-lg-6">
                        <div class="table-responsive">
                            <table class="table table-bordered table-vcenter" style="width:100%;">
                                <thead>
                                <tr>
                                    <th class="text-center">ROLES</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($listRoles as $data)
                                    <tr>
                                        <td class="text-left">{{ $data->role_desc }} ({{ $data->role_code }})</td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>  
                        </div> 
                    </div>
                </div>
                @endif
                
                <div class="table-responsive">
                    <table id="tracking-diary-datatable" class="table table-vcenter table-condensed table-bordered">
                        <thead>
                        <tr>
                            <th class="text-center">ID</th>
                            <th class="text-center">DOC TYPE</th>
                            <th class="text-center">DOC NO.</th>
                            <th class="text-center">ACTION DESCRIPTION</th>
                            <th class="text-center">ACTION DATE</th>
                            <th class="text-center">ROLE</th>
                            <th class="text-center">STATUS</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach ($listdata as $data)
                            <tr>
                                <td class="text-center">{{ $data->tracking_diary_id }}</td>
                                <td class="text-center">{{ $data->doc_type }}</td>
                                <td class="text-center list-trans-dofn">
                                <a href='#modal-list-trans-dofn'
                                    class='modal-list-data-action ' 
                                    data-toggle='modal'  
                                    data-url='/find/trans/docno/workflow/{{ $data->doc_no }}'
                                    data-title='Status WorkFlow Search By {{ $data->doc_no }}'>
                                     <strong style="font-weight: bolder;">
                                     {{ $data->doc_no }} </strong><br />
                                 </a>
                                
                                </td>
                                <td class="text-left">{{ $data->action_desc }}</td>
                                <td class="text-center">{{ $data->actioned_date }}</td>
                                <td class="text-center">{{ $data->role_code }}</td>
                                <td class="text-center">{{ $data->status_name }} ({{ $data->status_id }})</td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- END Customer Addresses Block -->
        </div>
    @endif
    
    <!-- MODAL: TRANS DO/FN  -->
    <div id="modal-list-trans-dofn" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">List Title</span></h2>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-sm-12">
                            <div class="text-center spinner-loading" style="padding: 20px; display: none;">
                                <i class="fa fa-spinner fa-4x fa-spin"></i>
                            </div>
                            <div class="trans-div-detail"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    

@endsection

@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<!-- Load and execute javascript code used only in this page -->
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function(){ TablesDatatables.init(); });
App.datatables();
$('#tracking-diary-datatable').dataTable({
                order: [[ 4, "desc" ],[ 0, "desc" ]],
                columnDefs: [  ],
                pageLength: 100,
                lengthMenu: [[10, 20, 30, 50, 100,  -1], [10, 20, 30, 50, 100, 'All']]
            });
            
var APP_URL = {!! json_encode(url('/')) !!}

$(document).ready(function () {

    $('.list-trans-dofn').on("click", '.modal-list-data-action', function () {

        $('.spinner-loading').show();
        $('.trans-div-detail').html('Please wait ...').fadeIn();

        $('#modal-list-data-header').text($(this).attr('data-title'));

        $.ajax({
            url: APP_URL + $(this).attr('data-url'),
            type: "GET",
            success: function (data) {
                $data = $(data)
                $('.spinner-loading').hide();
                $('.trans-div-detail').html($data).fadeIn();
            }
        });

    });
            
});            
</script>
@endsection



