/**
 * 
 * Author @aminah
 * Created at 01/09/2020
 */
$(document).ready(function () {
    $('#next_page').on('click', function () {

        $('#div_instance').hide();

        var compositeChk = $("input[name=isCompositeChecked]").val();
        var createdChk = $("input[name=isCreatedChecked]").val();
        var stateChk = $("input[name=isStateChecked]").val();

        var composite = '';
        if (compositeChk === '1') {
            composite = $('#composite option:selected').text();
        }

        var dateFrom = '';
        var hourFrom = '';
        var minFrom = '';
        var dateTo = '';
        var hourTo = '';
        var minTo = '';

        if (createdChk === '1') {
            dateFrom = $("#datefrom").val();
            hourFrom = $("#time_from_hour").val();
            minFrom = $("#time_from_min").val();
            dateTo = $("#dateto").val();
            hourTo = $("#time_to_hour").val();
            minTo = $("#time_to_min").val();
        }

        var state = '';
        if (stateChk === '1') {
            state = $("#states").val();
        }

        var csrf = $("input[name=_token]").val();
        var offset = $("input[name=offset]").val();
        var limit = $("input[name=limit]").val();

        var newOffset = +offset + 1;

        $("input[name=offset]").val(newOffset);
        $('#previous_page').attr("disabled", false);

        $('#modal_spinner').modal('show');
        $.ajax({
            type: "POST",
            url: "/bpm/instance/query/page",
            data: {"_token": csrf, "dateFrom": dateFrom, "hourFrom": hourFrom, "minFrom": minFrom, "dateTo": dateTo, "hourTo": hourTo, "minTo": minTo,
                "composite": composite, "state": state, "newOffset": newOffset, "limit": limit},
            error: function (xhr, status, error) {
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
            }
        }).done(function (resp) {
            $('#modal_spinner').modal('hide');
            if (resp.status === 'success') {
                $('#bodyTable').empty();
                $('#div_instance').show();

                populateTable(resp.listdata, newOffset);

            } else {
                document.getElementById("failed").innerHTML = resp.statusApi;
            }
        });

    });

    $('#previous_page').on('click', function () {

        $('#div_instance').hide();

        var compositeChk = $("input[name=isCompositeChecked]").val();
        var createdChk = $("input[name=isCreatedChecked]").val();
        var stateChk = $("input[name=isStateChecked]").val();

        var composite = '';
        if (compositeChk === '1') {
            composite = $('#composite option:selected').text();
        }

        var dateFrom = '';
        var hourFrom = '';
        var minFrom = '';
        var dateTo = '';
        var hourTo = '';
        var minTo = '';

        if (createdChk === '1') {
            dateFrom = $("#datefrom").val();
            hourFrom = $("#time_from_hour").val();
            minFrom = $("#time_from_min").val();
            dateTo = $("#dateto").val();
            hourTo = $("#time_to_hour").val();
            minTo = $("#time_to_min").val();
        }

        var state = '';
        if (stateChk === '1') {
            state = $("#states").val();
        }

        var csrf = $("input[name=_token]").val();
        var offset = $("input[name=offset]").val();
        var limit = $("input[name=limit]").val();

        var newOffset = +offset - 1;

        $("input[name=offset]").val(newOffset);

        if (newOffset < 1) {
            $('#previous_page').attr("disabled", true);
        } else {
            $('#previous_page').attr("disabled", false);
        }

        $('#modal_spinner').modal('show');
        $.ajax({
            type: "POST",
            url: "/bpm/instance/query/page",
            data: {"_token": csrf, "dateFrom": dateFrom, "hourFrom": hourFrom, "minFrom": minFrom, "dateTo": dateTo, "hourTo": hourTo, "minTo": minTo,
                "composite": composite, "state": state, "newOffset": newOffset, "limit": limit},
            error: function (xhr, status, error) {
                $('#failed').show();
                document.getElementById("failed").innerHTML = xhr.status + ': ' + xhr.statusText;
            }
        }).done(function (resp) {
            $('#modal_spinner').modal('hide');
            
            if (resp.status === 'success') {
                $('#bodyTable').empty();
                $('#div_instance').show();

                populateTable(resp.listdata, newOffset);

            } else {
                document.getElementById("failed").innerHTML = resp.statusApi;
            }
        });

    });
});