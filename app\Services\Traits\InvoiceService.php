<?php

namespace App\Services\Traits;

use DB;
use Auth;
use Carbon\Carbon;
use App\Services\Traits\SupplierService;

trait InvoiceService {

    protected function getInvoiceDetails($documentNo) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->table('fl_fulfilment_order as a');
        $query->join('fl_fulfilment_request as pr', 'a.FULFILMENT_REQ_ID', '=', 'pr.FULFILMENT_REQ_ID');
        $query->join('fl_workflow_status as b', 'a.fulfilment_order_id', '=', 'b.doc_id');
        $query->join('pm_status as c', 'b.status_id', '=', 'c.status_id');
        $query->join('pm_status_desc as d', 'c.status_id', '=', 'd.status_id');
        $query->where('d.language_code', '=', 'en');
        $query->whereIn('b.doc_type', ['PO', 'CO']);
        $query->where('b.is_current', '=', '1');
        $query->where('a.doc_no', '=', $documentNo);
//        $query->where('TO_CHAR (a.Created_DATE, "yyyy")', '=','2018');
        $query->select('a.FULFILMENT_REQ_ID as requestid', 'a.fulfilment_order_id as orderid', 'pr.TITLE as ordername', 'a.DOC_TYPE as ordertype', 'a.DOC_NO as orderdocnmber','pr.supplier_id');
        $query->orderBy('b.CREATED_DATE', 'desc');

        return $query->first();
    }

    protected function getApproverDetails($documentNo) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->table('pm_tracking_diary as td');
        $query->join('pm_user as pu', 'pu.USER_ID', '=', 'td.ACTIONED_BY');
        $query->where('td.DOC_NO', '=', $documentNo);
        $query->where('td.ROLE_CODE', '=', 'ACKNOWLEDGE_OFFICER');
        $query->select('pu.USER_ID as userid', 'pu.LOGIN_ID as loginid', 'pu.USER_NAME as username', 'pu.DESIGNATION as designation');
        $query->orderBy('ACTIONED_DATE', 'desc');

        return $query->first();
    }

    protected function getSupplierDetails($documentNo) {
        $query = DB::connection('oracle_nextgen_rpt')
                ->table('pm_tracking_diary as td');
        $query->join('pm_user as pu', 'pu.USER_ID', '=', 'td.ACTIONED_BY');
        $query->join('sm_personnel as sp', 'sp.USER_ID', '=', 'pu.USER_ID');
        $query->where('td.DOC_NO', '=', $documentNo);
        $query->whereNotIn('td.ROLE_CODE', ['ACKNOWLEDGE_OFFICER']);
        $query->select('pu.USER_ID as userid', 'pu.LOGIN_ID as loginid', 'pu.USER_NAME as username', 'pu.DESIGNATION as designation','pu.record_status as recordstatus');
        $query->orderBy('ACTIONED_DATE', 'desc');


        return $query->first();
    }

    protected function getOrder($documentNo) {
        $query = DB::connection('oracle_nextgen_rpt')->select("select  ago.OFFICE_CODE as businessarea,  a.SAP_ORDER_NO as orderno, pr.PHIS_NO as phis, sd.STOP_INSTR_NO as stopinst
from fl_fulfilment_order a, fl_fulfilment_request pr ,pm_ag_validity agv ,pm_ag_office ago , fl_stop_instr sd
where a.FULFILMENT_req_id = pr.FULFILMENT_REQ_ID
and pr.AG_OFFICE_ID = agv.AG_OFFICE_ID
and agv.AG_OFFICE_ID = ago.AG_OFFICE_ID
and pr.FULFILMENT_REQ_ID = sd.FULFILMENT_REQ_ID(+)
and a.doc_no = ?
", array($documentNo));

        return $query;
    }

    protected function getListSupplier($documentNo) {

        $type = substr($documentNo, 0, 2);

        $query = DB::connection('oracle_nextgen_rpt')
                ->table('FL_FULFILMENT_REQUEST as a');
        $query->join('FL_FULFILMENT_ORDER as b', 'a.FULFILMENT_REQ_ID', '=', 'b.FULFILMENT_REQ_ID');
        $query->join('SM_SUPPLIER as c', 'c.SUPPLIER_ID', '=', 'a.SUPPLIER_ID');
        $query->join('SM_MOF_ACCOUNT as d', 'd.SUPPLIER_ID', '=', 'c.SUPPLIER_ID');
        if ($type == 'PR' || $type == 'CR') {
            $query->where('A.DOC_NO', $documentNo);
        }
        if ($type == 'PO' || $type == 'CO') {
            $query->where('B.DOC_NO', $documentNo);
        }
        $query->select('D.MOF_NO as mofno');

        return $query->first();
    }

    protected function getListAllSupplier($mofno) {

        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT b.login_id, ROW_NUMBER()OVER (ORDER BY b.login_id ) rn,COUNT(*) OVER () cnt
        FROM sm_personnel a, pm_user b WHERE
        a.user_id = b.user_id AND a.appl_id = '1846896'
           and a.ep_role IN ('MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER','FL_USER','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','GOVT_SELLER')

                ", array($mofno));
        return $query;
    }
    
    protected function getListSupplierUsers($supplierId){
        $query = DB::connection('oracle_nextgen_rpt')->select("SELECT b.*
            FROM sm_personnel a, pm_user b, sm_supplier c WHERE
            a.user_id = b.user_id AND a.appl_id = c.latest_appl_id
            and a.ep_role IN ('MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER','FL_USER','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','GOVT_SELLER')
            and c.supplier_id = ? 
            and a.record_status = 1 and b.record_status = 1", array($supplierId));
                
        return $query;
    }
    
    protected function getActiveSupplier($supplierId){
        
        $query = DB::connection('oracle_nextgen_rpt')
                ->table('sm_personnel as a');
        $query->join('pm_user as b', 'a.user_id', '=', 'b.user_id');
        $query->join('sm_supplier as c', 'a.appl_id', '=', 'c.latest_appl_id');
        $query->where('c.supplier_id', '=', $supplierId);
        $query->where('a.record_status','=','1');
        $query->where('b.record_status','=','1');
        $query->whereIn('a.ep_role', ['MOF_SUPPLIER_ADMIN','MOF_SUPPLIER_USER','FL_USER','BASIC_SUPPLIER_ADMIN','BASIC_SUPPLIER_USER','GOVT_SELLER']);
        $query->select('b.USER_ID as userid', 'b.LOGIN_ID as loginid', 'b.USER_NAME as username', 'b.DESIGNATION as designation');

        return $query->first();
    }
    
    protected function getListStuckCreateInvoiceByDocNo($docNo){
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "select fo.created_date, fo.fulfilment_req_id, fo.fulfilment_order_id, fo.doc_no  , ws.status_id , td.status_id as tracking_status_id
                    from FL_FULFILMENT_ORDER fo, fl_workflow_status ws, pm_tracking_diary td
                  where fo.fulfilment_order_id = ws.doc_id and ws.is_current=1 and ws.status_id = 41505
                  and fo.fulfilment_order_id = td.doc_id and td.status_id = 41515 and td.actioned_date = (select max(actioned_date) from pm_tracking_diary where doc_id=td.doc_id and doc_type=td.doc_type)
                  and fo.doc_no = ? ", array($docNo));
                
        return $query;
    }
    
    
    protected function getListStuckCOCreateInvoice($total){
        //-- *41515 - Pending Invoice  (TRACKING DIARY)*
        //-- *41505 - Pending Fulfilment (WORKFLOW Order)*
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "select a.created_date, a.fulfilment_req_id, a.fulfilment_order_id, a.doc_no from FL_FULFILMENT_ORDER a
                    where a.fulfilment_order_id in (
                    select doc_id from fl_workflow_status where status_id=41505  and is_current=1 and created_date>='01-Jan-18'
                    and doc_id in (
                        select z.doc_id from pm_tracking_diary z
                          where z.status_id=41515
                                and z.actioned_date=(select max(actioned_date) from pm_tracking_diary where doc_id=z.doc_id and doc_type=z.doc_type)
                        )
                    )
                    -- and a.fulfilment_req_id in (select fulfilment_req_id from FL_FULFILMENT_REQUEST where supplier_id = 37074 )    -- PHIS Supplier
                    and rownum <= ?
                    order by a.created_date", array($total));
                
        return $query;
    }
    
    protected function getListStuckPOCreateInvoice($total){
        //-- *41015 - Pending Invoice  (TRACKING DIARY)*
        //-- *41005 - Pending Fulfilment (WORKFLOW Order)*
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "select a.created_date, a.fulfilment_req_id, a.fulfilment_order_id, a.doc_no from FL_FULFILMENT_ORDER a
                    where a.fulfilment_order_id in (
                    select doc_id from fl_workflow_status where status_id=41005   and is_current=1 and created_date>='01-Jan-18'
                    and doc_id in (
                        select z.doc_id from pm_tracking_diary z
                          where z.status_id=41015
                                and z.actioned_date=(select max(actioned_date) from pm_tracking_diary where doc_id=z.doc_id and doc_type=z.doc_type)
                        )
                    )
                    -- and a.fulfilment_req_id in (select fulfilment_req_id from FL_FULFILMENT_REQUEST where supplier_id = 37074 )    -- PHIS Supplier
                     and rownum <= ?
                    order by a.created_date", array($total));
                
        return $query;
    }
    
    protected function getListStuckPOPartialCreateInvoice($total){
        // -- *41015  - Pending Invoice  (TRACKING DIARY)*
        // -- *41010 - Pending Fulfilment (WORKFLOW Order)*
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "select a.created_date, a.fulfilment_req_id, a.fulfilment_order_id, a.doc_no from FL_FULFILMENT_ORDER a
                    where a.fulfilment_order_id in (
                    select doc_id from fl_workflow_status where status_id=41010   and is_current=1 and created_date>='01-Jan-18'
                    and doc_id in (
                        select z.doc_id from pm_tracking_diary z
                          where z.status_id=41015
                                and z.actioned_date=(select max(actioned_date) from pm_tracking_diary where doc_id=z.doc_id and doc_type=z.doc_type)
                        )
                    )
                    -- and a.fulfilment_req_id in (select fulfilment_req_id from FL_FULFILMENT_REQUEST where supplier_id = 37074 )    -- PHIS Supplier
                     and rownum <= ?
                    order by a.created_date", array($total));
                
        return $query;
    }
    
    protected function isStuckCreateInvoiceCOInEP($docNo){
        // *41515 - Pending Invoice  (TRACKING DIARY)*
        $checkDataTracDiary = DB::connection('oracle_nextgen_rpt')->select(
                "select b.* from pm_tracking_diary b where b.doc_no = ? 
                    and b.status_id = 41515  
                    and b.actioned_date = (select max(actioned_date) from pm_tracking_diary  s where s.doc_no = b.doc_no ) ",
                    array($docNo));
        
        if(count($checkDataTracDiary) > 0 ){
            // *41505 - Pending Fulfilment (WORKFLOW Order)*
            $checkDataWorkFlowInvoice = DB::connection('oracle_nextgen_rpt')->select(
                "select fo.created_date, fo.fulfilment_req_id, fo.fulfilment_order_id, fo.doc_no  , ws.status_id
                    from FL_FULFILMENT_ORDER fo, fl_workflow_status ws
                  where fo.fulfilment_order_id = ws.doc_id
                  and fo.doc_no = ?  and ws.is_current=1 and ws.status_id = 41505  ",
                    array($docNo));
            if(count($checkDataWorkFlowInvoice) > 0 ){
                return true;
            }
        }
        
        return false;
    }
    
    protected function isStuckCreateInvoicePOInEP($docNo){
        // *41015 - Pending Invoice  (TRACKING DIARY)*
        $checkDataTracDiary = DB::connection('oracle_nextgen_rpt')->select(
                "select b.* from pm_tracking_diary b where b.doc_no = ? 
                    and b.status_id = 41015  
                    and b.actioned_date = (select max(actioned_date) from pm_tracking_diary  s where s.doc_no = b.doc_no ) ",
                    array($docNo));
        
        if(count($checkDataTracDiary) > 0 ){
            // *41005 - Pending Fulfilment (WORKFLOW Order)*
            $checkDataWorkFlowInvoice = DB::connection('oracle_nextgen_rpt')->select(
                "select fo.created_date, fo.fulfilment_req_id, fo.fulfilment_order_id, fo.doc_no  , ws.status_id
                    from FL_FULFILMENT_ORDER fo, fl_workflow_status ws
                  where fo.fulfilment_order_id = ws.doc_id
                  and fo.doc_no = ?  and ws.is_current=1 and ws.status_id = 41005  ",
                    array($docNo));
            if(count($checkDataWorkFlowInvoice) > 0 ){
                return true;
            }
        }
        
        return false;
    }
    
    protected function isStuckCreateInvoicePOPartialInEP($docNo){
        // *41015 - Pending Invoice  (TRACKING DIARY)*
        $checkDataTracDiary = DB::connection('oracle_nextgen_rpt')->select(
                "select b.* from pm_tracking_diary b where b.doc_no = ? 
                    and b.status_id = 41015  
                    and b.actioned_date = (select max(actioned_date) from pm_tracking_diary  s where s.doc_no = b.doc_no ) ",
                    array($docNo));
        
        if(count($checkDataTracDiary) > 0 ){
            // *41010 - Pending Fulfilment >>> Partial (WORKFLOW Order)*
            $checkDataWorkFlowInvoice = DB::connection('oracle_nextgen_rpt')->select(
                "select fo.created_date, fo.fulfilment_req_id, fo.fulfilment_order_id, fo.doc_no  , ws.status_id
                    from FL_FULFILMENT_ORDER fo, fl_workflow_status ws
                  where fo.fulfilment_order_id = ws.doc_id
                  and fo.doc_no = ?  and ws.is_current=1 and ws.status_id = 41010  ",
                    array($docNo));
            if(count($checkDataWorkFlowInvoice) > 0 ){
                return true;
            }
        }
        
        return false;
    }
    
    protected function getStuckPendingInvoice() {
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT distinct -- ffr.doc_no, fws.status_id, ptd.status_id
                    ffr.FULFILMENT_REQ_ID as doc_id, ffr.DOC_TYPE,ffr.DOC_NO ,fws.STATUS_ID,  fws.IS_CURRENT, ffr.RECORD_STATUS , ffr.FULFILMENT_ORDER_ID, fws.created_date, status.STATUS_NAME
                    FROM fl_fulfilment_order ffr,
                         fl_workflow_status fws,
                         pm_tracking_diary ptd,
                         pm_status_desc status,
                         FL_FULFILMENT_REQUEST req
                   WHERE ffr.fulfilment_order_id = fws.doc_id
                     AND ffr.fulfilment_req_id = req.fulfilment_req_id
                     AND req.financial_year = to_char(sysdate, 'YYYY') 
                     AND fws.doc_id = ptd.doc_id
                     AND fws.status_id IN (41005,41505,41010,41510) 
                     AND ptd.DOC_no = ffr.doc_NO 
                     AND ptd.status_id IN (41515,41015)
                     AND ptd.doc_type IN ('CO','PO')
                     AND fws.doc_type = ptd.doc_type
                     AND fws.is_current = 1
                     AND fws.STATUS_ID = status.STATUS_ID
                     AND status.LANGUAGE_CODE = 'en'
                     AND ffr.FULFILMENT_REQ_ID in (select frn.fulfilment_req_id from fl_fulfilment_note frn ,fl_workflow_status frs where frn.FULFILMENT_NOTE_ID = frs.DOC_ID 
                     and frs.DOC_TYPE in ('FN') and frn.FULFILMENT_REQ_ID = ffr.FULFILMENT_REQ_ID and frs.IS_CURRENT = 1 and frs.STATUS_ID in (43010 ) )
                     AND ptd.ACTIONED_DATE in  (select max(td2.ACTIONED_DATE) from pm_tracking_diary td2 where td2.DOC_NO = ptd.DOC_NO)
                 ");
        
        return $query;
    }
    
    protected function getStuckPendingPaymentMatch() {
        
        $query = DB::connection('oracle_nextgen_rpt')->select(
                "SELECT
                    fr.FULFILMENT_REQ_ID AS doc_id,
                    fr.DOC_TYPE,
                    fr.DOC_NO,
                    fr.DOC_NO as search_bpm,
                    s.STATUS_ID,
                    s.IS_CURRENT,
                    fr.RECORD_STATUS,
                    fr.FULFILMENT_ORDER_ID,
                    s.created_date,
                    status.STATUS_NAME
                  FROM fl_workflow_status s, fl_fulfilment_order fr, FL_FULFILMENT_REQUEST req, pm_status_desc status
                  WHERE s.doc_type IN ('PO', 'CO')
                        AND fr.fulfilment_req_id = req.fulfilment_req_id
                        AND fr.fulfilment_order_id = s.doc_id
                        AND s.STATUS_ID = status.STATUS_ID
                        AND status.LANGUAGE_CODE = 'en'
                        AND s.is_current = 1
                        AND s.status_id IN (41515, 41015)
                        and fr.DOC_NO not in ('PO190000000930857','CO190000000744696','CO190000000877550','PO190000001016065','PO190000000983192','CO190000000494606','PO200000000081453')
                        AND req.financial_year = to_char(sysdate, 'YYYY')
                        AND EXISTS(
                            SELECT tt.tracking_diary_id
                            FROM pm_tracking_diary tt
                            WHERE tt.tracking_diary_id IN (SELECT MAX(t.tracking_diary_id)
                                                           FROM pm_tracking_diary t
                                                           WHERE t.doc_no = fr.doc_no)
                                  AND tt.status_id IN (41525, 41025))");
        
        return $query;
    }
}
