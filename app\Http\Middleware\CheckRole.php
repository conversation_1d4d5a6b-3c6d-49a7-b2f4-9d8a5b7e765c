<?php

namespace App\Http\Middleware;

// First copy this file into your middleware directoy
use Closure;

class CheckRole {

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next) {
        // Get the required roles from the route
        $roles = $this->getRequiredRoleForRoute($request->route());
        // Check if a role is required for the route, and
        // if so, ensure that the user has that role.
        if ($request->user()->hasRole($roles) || !$roles) {
            return $next($request);
        } elseif ($request->user()->selectedUser($roles) || !$roles) {
            return $next($request);
        }
        return response([
            'error' => [
                'code' => 'INSUFFICIENT_ROLE',
                'activity' => 'Your activity has been save.',
                'description' => 'You are not authorized to access this resource.'
            ]
                ], 401);
    }

    private function getRequiredRoleForRoute($route) {
        $actions = $route->getAction();
        if (isset($actions['roles'])) {
            return $actions['roles'];
        } elseif (isset($actions['users'])) {
            return $actions['users'];
        } else {
            return null;
        }
//		return isset($actions['roles']) ? $actions['roles'] : null;
    }

}
