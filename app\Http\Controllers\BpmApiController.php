<?php

namespace App\Http\Controllers;

use App\Services\Traits\BpmApiService;
use Validator;
use DB;
use Illuminate\Http\Request;
use Log;
use App\EpSupportActionLog;
use Carbon\Carbon;
use DateTime;
use App\Services\Traits\FulfilmentService;
use App\Services\Traits\BPMService;
use App\Services\Traits\InvoiceService;
use GuzzleHttp\Client;
use App\Services\Traits\PayloadGeneratorService;
use App\Migrate\MigrateUtils;
use App\Migrate\BPMTaskServiceProgram;

class BpmApiController extends Controller {

    use BpmApiService;
    use FulfilmentService;
    use BPMService;
    use InvoiceService;
    use PayloadGeneratorService;
    
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function searchListTaskBpm(Request $request) {

        $listdata = collect();
        $statusAPI = null;
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {
            $this->validate($request, [
                'doc_no' => 'required',
                'module' => 'required'
            ]);
        }
        $listDataResult = null;
        if ($request->created_date != null) {
            $listDataResult = $this->findAPITaskBPMList($request->doc_no, $request->module, $request->created_date);
        } else {
            $listDataResult = $this->findAPITaskBPMListDocAndModule($request->doc_no, $request->module);
        }
        //dump($listDataResult);
        if ($listDataResult != null && $listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
            foreach ($listdata as $key => $row) {
                $listComposite = explode("*", $row['compositeDN']);
                $listdata[$key]['composite'] = $listComposite[0];
            }
        } elseif($listDataResult != null && $listDataResult["status"] != null) {
            $statusAPI = $listDataResult["result"];
        } else {
            $statusAPI = 'Failed connected API';
        }

        return view('bpm_api.list_tasks', [
            'status_api' => $statusAPI,
            'listdata' => $listdata]);
    }

    /**
     * 
     * @param Request $request
     * @return type
     * 
     * Search Task BPM By Composite Instance Id and/or Composite Module
     */
    public function searchListTaskBpmByCompositeInstanceId(Request $request) {

        $listdata = collect();
        $statusAPI = null;
        $listdataComposite = collect();
        $new_composite = [];
        $listDocNo = collect();
        $flowId = null;
        $listDlvSubscribCorr = null;
        
        $listComposite = $this->findApiBPMGetListComposite();
        if ($listComposite["status"] != null && $listComposite["status"] === 'Success') {
            $listdataComposite = $listComposite["result"];
        }else{
            $statusAPI = $listComposite["result"];
        }

        if (isset($listdataComposite["data"])) {
            foreach ($listdataComposite["data"] as $data) {
                $new_composite[$data['composite']] = $data['composite'];
            }
        } 

        session()->flashInput(request()->input());

        if ($request->isMethod("POST")) {

            $this->validate($request, [
                'composite_instance_id' => 'required'
            ]);
        }
        $instance = $request->composite_instance_id;
        $module = $request->composite_module;
        
        if($instance !==  null){
            $listDocNo = $this->getDocNoByInstanceId($instance);
            $flow = $this->findBpmFlowId($instance); 
            $listDlvSubscribCorr = $this->findDlvSubcscriptionCorrelation($instance);
            if($flow != null && count($flow) > 0) {
                $flowId = $flow[0]->flow_id;
            } 
        }
        
        if ($module !== null) {
            $listDataResult = $this->findAPIProcessManagerBPMByInstanceModule($instance, $module);
        } else {
            $listDataResult = $this->findAPIProcessManagerBPMByInstance($instance);
        }

        //dump($listDataResult);
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
    //                dump($listdata);              
        } else {
            $statusAPI = $listDataResult["result"];
        }
         
        return view('bpm_api.list_process_instance', [
            'instanceId' => $instance,
            'status_api' => $statusAPI,
            'listdata' => $listdata,
            'listdataComposite' => $new_composite,
            'listDocNo' => $listDocNo,
            'listDlvSubscribCorr' => $listDlvSubscribCorr,
            'flowId' => $flowId]);
    }

    /**
     * 
     * @param type $processId
     * @param type $status
     * @param Request $request
     * @return type
     * 
     * Call API to do resume/suspend bpmn/task
     */
    public function suspendResumeWorkflowAction($id, $status, Request $request) {
        $data = collect([]);
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $actionTask  = $request->action;
            $compositeId = $request->composite_id;
            $actionTypeLog = 'Web Service';
            
            $parameters =  collect([]);   
            $parameters->put("instance_id", $compositeId);
            $parameters->put("id", $id);
            $parameters->put("status", $status);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            //resume bpmn
            if ($actionTask == 'resume') {
                
                $resumeAction = $this->suspendResumeBpmn($id, $actionTask);
                $actionName = 'BPM-Resume-Bpmn-Flow';
                
                if ($resumeAction['status'] == 'Success') {
                    $status = 'Success';
                    $data->put('status', 'success');
                } else {
                    $status = 'Failed';
                    $data->put('status', $resumeAction['result']);
                }

                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
                return $data;
                
            //suspend bpmn
            } else if ($actionTask == 'suspend'){
                
                $suspendAction = $this->suspendResumeBpmn($id,$actionTask);
                $actionName = 'BPM-Suspend-Bpmn-Flow';
                
                if ($suspendAction['status'] == 'Success') { 
                    $status = 'Success';
                    $data->put('status', 'success');
                }else{
                    $status = 'Failed';
                    $data->put('status', $suspendAction['result']);
                }   
                
                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
                return $data;
                
            //resume task
            } else if($actionTask == 'resumeTask'){

                $resumeAction = $this->suspendResumeTask($id,$actionTask);
                $actionName = 'BPM-Resume-Task-Flow';
                
                if ($resumeAction['status'] == 'Success') {
                    $status = 'Success';
                    $data->put('status', 'success');
                } else{
                    $status = 'Failed';
                    $data->put('status', $resumeAction['result']);
                }
                
                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
                return $data;
                
            //suspend task
            } else{
                
                $suspendAction = $this->suspendResumeTask($id,$actionTask);
                $actionName = 'BPM-Suspend-Task-Flow';
                
                if ($suspendAction['status'] == 'Success') {
                    $status = 'Success';
                    $data->put('status', 'success');
                } else{
                    $status = 'Failed';
                    $data->put('status', $suspendAction['result']);
                }
                
                EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, $status);
                return $data;
            }
        }
    }
    
    public function withdrawTaskAction($id, $status, Request $request){
        $data = collect([]);
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $actionTask  = $request->action;
            $compositeId = $request->composite_id;
            $actionTypeLog = 'Web Service';

            $parameters =  collect([]);   
            $parameters->put('instance_id', $compositeId);
            $parameters->put("id", $id);
            $parameters->put("status", $status);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $actionName = 'BPM-Withdraw-Task-Flow';
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            
            $status = null;
            
            if ($actionTask == 'withdrawTask') {

                $withdrawAction = $this->withdrawTask($id, $actionTask);
                
                if ($withdrawAction['status'] == 'Success') {
                    $status = 'Success';
                    $data->put('status', $status);
                } else {
                    $status = 'Failed';
                    $data->put('status', $withdrawAction["result"]);
                }

                EpSupportActionLog::updateActionLog($actionLog, $status); 
                return $data;
            }
        }
    }
    
    public function reassignTaskAction($id, $status, Request $request){
        $data = collect([]);
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $actionTask  = $request->action;
            $actionTypeLog = 'Web Service';
            $assignee = $request->assignee;
            $compositeId = $request->composite_id;
            
            $parameters =  collect([]);    
            $parameters->put('instance_id', $compositeId);
            $parameters->put("id", $id);
            $parameters->put("status", $status);
            $parameters->put("action_task", $actionTask);
            $parameters->put("assignee", $assignee);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $actionName = 'BPM-Reassign-Task-Flow';
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            
            $status = null;
            
            if ($actionTask == 'reassignTask') {

                $reassignTask = $this->reassignTask($id, $assignee);
                
                if ($reassignTask['status'] == 'Success') {
                     $status = 'Success';
                    $data->put('status', $status);
                } else {
                    $status = 'Failed';
                    $data->put('status', $reassignTask["result"]);
                }

                EpSupportActionLog::updateActionLog($actionLog, $status); 
                return $data;
            }
        }
    }
    
    public function executeTaskAction($taskid, Request $request){
        $data = collect([]);
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $actionTask = $request->action;
            $user  = $request->user;
            $executeAction = $request->taskaction;
            $payload = $request->updatedpayload;
            $param = $request->param;
            $compositeId = $request->composite_id;
            
            $actionTypeLog = 'Web Service';

            $parameters =  collect([]);   
            $parameters->put("instance_id", $compositeId);
            $parameters->put("taskid", $taskid);
            $parameters->put("user", $user);
            $parameters->put("execute_action", $executeAction);
            $parameters->put("payload", $payload);
            $parameters->put("param", $param);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $actionName = 'BPM-Execute-Task-Flow';
            
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            
            $status = null;
            
            if ($actionTask == 'executeTask') {

                $executeData = $this->updateExecuteActionAPI($taskid, $user, $payload, $executeAction, $param);
                if ($executeData['status'] == 'Success') {
                     $status = 'Success';
                    $data->put('status', $status);
                } else {
                    $status = 'Failed';
                    $data->put('status', $executeData["result"]);
                }

                EpSupportActionLog::updateActionLog($actionLog, $status); 
                return $data;
            }
        }
    }

    public function isValidToken() {
        $listDataResult = $this->doAPIIsValidToken();

        return $listDataResult;
        
    }
    
    public function searchSMTaskApplication(Request $request){
        $listdata = collect([]);
        $appl_no = null;

        if($request->has('appl_no')) {
            $appl_no = $request->query('appl_no');
        }
        
        return view('sm.bpm_sm_task',[
            'listdata' => $listdata,
            'appl_no' => $appl_no
        ]);
    }
    public function doActionSMTaskApplication() {

        $listdata = collect([]);
        $statusAPI = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'appl_no' => 'required',
                'action_task_sm' => 'required',
                'is_trigger_bpm' => 'required',
                "is_payment_required" => "required_if:action_task_sm,==,refire-task",
            ]);

            $applNo = request()->appl_no;
            $actionTask  = request()->action_task_sm;
            $isPayment = request()->is_payment_required;
            $isTriggerBpm = request()->is_trigger_bpm;
               
            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-SM-CreateTask';

            $parameters =  collect([]);            
            $parameters->put("appl_no", $applNo);
            $parameters->put("is_payment", $isPayment);
            $parameters->put("trigger_bpm", $isTriggerBpm);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Supplier_Management');
            
         
            $actionLog = EpSupportActionLog::saveActionLog($actionName,$actionTypeLog,$parameters,$parameters,'Processing');
            $payload = null;
            if($isTriggerBpm === 'false') {
                if(strlen($isPayment)===0){
                    $isPayment = 'false';
                }

                $listDataResult = $this->doAPIgetPayloadSMTaskApplication($applNo, $isPayment);
                $listdata = $listDataResult;
                if($listDataResult['status']=='Success'  && isset($listDataResult['result']) && isset($listDataResult['result']['payload']) ){
                    EpSupportActionLog::updateActionLog($actionLog, 'Completed'); 
                    $payload = str_replace('\"', '"', $listdata['result']['payload']);
                 }else{
                    EpSupportActionLog::updateActionLog($actionLog, 'Failed');  
                 }
            }elseif($actionTask === 'initiate-task'){
               // $listDataResult = $this->doAPIInitiateSMTaskApplication($applNo); 
               $listDataResult  = BPMTaskServiceProgram::initiateTaskSmApplication($applNo);
               $listdata = $listDataResult;
               if($listDataResult['status']=='Success'){
                  EpSupportActionLog::updateActionLog($actionLog, 'Completed'); 
               }else{
                  EpSupportActionLog::updateActionLog($actionLog, 'Failed');  
               }
            }elseif($actionTask === 'refire-task' && ( $isPayment === 'true' || $isPayment === 'false') ){
               $listDataResult = $this->doAPIRefireSMTaskApplication($applNo, $isPayment);
               $listdata = $listDataResult;
               if($listDataResult['status']=='Success'){
                  EpSupportActionLog::updateActionLog($actionLog, 'Completed'); 
               }else{
                  EpSupportActionLog::updateActionLog($actionLog, 'Failed');  
               }
            }elseif($actionTask === 'refire-payment-softcert-task'  ){
                $res =  BPMTaskServiceProgram::checkAndrefirePaidSoftcertTaskSmApplication($applNo);
                $listdata = collect([$res]);
                EpSupportActionLog::updateActionLog($actionLog, $res); 
            }
            else{
                EpSupportActionLog::updateActionLog($actionLog, 'Failed'); 
            }
        }

        return view('sm.bpm_sm_task', [
            'status_api' => $statusAPI,
            'payload' => $payload,
            'appl_no' => $applNo,
            'listdata' => $listdata]);
    }
    
    /**
     * 
     * @param Request $request
     * @return type
     * 
     * Call API to show process flow by process id and status
     */
    public function detailProcessWorkflow(Request $request) {
        
        $data = collect([]); 
        $processId = $request->process_id;
        $status = $request->status;
        $compositeId = $request->composite_id;
        $assignees = null;
        $statusAPI = null;
        $payload = null;
        $listdata = null;
        $listAction = null;
        $history = null;
        $taskGroup = null;
        $getList = null;
        
        $listDataResult = $this->findAPITaskIDBPMList($processId);
        //Log::info(__METHOD__ .' data '.json_encode($listDataResult));
        
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $listdata = $listDataResult["result"];
            $assignees = str_replace("\n ", ",", $listdata["assigneesString"]);

            //get list of action

            if (isset($listdata)) {

                $cdcadmin = array('Contract_Management.ProcessOwner',
                    'Fulfilment.ProcessOwner',
                    'Order.ProcessOwner',
                    'SourcingDP.ProcessOwner',
                    'SourcingQT.ProcessOwner',
                    'Supplier_Management.ProcessOwner',
                    'Profile_Management.ProcessOwner',
                    'SCBusinessRule.ProcessOwner',
                    'Codification.ProcessOwner');

                if ($listdata["taskId"] !== null && $assignees !== null) {
                    $taskId = $listdata["taskId"];

                    if (in_array($assignees, $cdcadmin)) {
                        $getList = $this->findAPIListTaskAction($taskId, 'cdcadmin');
                    } else {
                        $arr = explode(",", $assignees, 2);
                        $first = $arr[0];
                        $getList = $this->findAPIListTaskAction($taskId, $first);
                    }
                }

                if (isset($getList)) {
                    if ($getList['status'] == 'Success') {
                        $data->put('statusListTask', 'success');
                        $listAction = $getList["result"];
                    } else {
                        $statusListTask = $getList["result"];
                        $data->put('statusListTask', $statusListTask);
                    }
                }

                if ($listdata["taskId"] !== null) {
                    $getHistory = $this->taskHistory($listdata["taskId"]);
                    if ($getHistory != null && count(array($getHistory)) > 0 && $getHistory['status'] == 'Success') {
                        $history = $getHistory["result"];
                        $data->put('statusHistory', 'success');
                    } else {
                        $statusHistory = ($getHistory != null && isset($getHistory["result"]) ) ? $getHistory["result"] : 'Failed' ;
                        $data->put('statusHistory', $statusHistory);
                    }
                    
                    $getTaskGroup = $this->findApiBPMGetTaskGroupByTaskId($taskId);
                    if ($getTaskGroup != null && $getTaskGroup['status'] == 'Success') {
                        $taskGroup = $getTaskGroup["result"];
                        $data->put('statusTaskGroup', 'success');
                        
                    } else {
                        $statusTaskGroup = ($getTaskGroup != null && isset($getTaskGroup["result"]) ) ? $getTaskGroup["result"]: 'Failed' ;
                        $data->put('statusTaskGroup', $statusTaskGroup);
                    }
                }
            }

            if ($listdata["payload"]) {
                foreach ($listdata["payload"] as $value) {
                    $payload = preg_replace('!^[^>]+>!', '', $value);
                }
            }
        } else {
            $statusAPI = $listDataResult["result"];
        }
        $data->put('instance_id', $compositeId);
        $data->put('process_id', $processId);
        $data->put('status', $status);
        $data->put('listdata', $listdata);
        $data->put('assignee', $assignees);
        $data->put('payload', $payload);
        $data->put('listaction',$listAction);
        $data->put('history', $history);
        $data->put('statusAPI', $statusAPI);
        $data->put('taskGroup', $taskGroup);
       
        return $data;
    }
    
    /**
     * 
     * @param Request $request
     * @return type
     * 
     * Call API to show bpmn flow by process id and status
     */
    public function detailProcessBpmn(Request $request) {
        
        $data = collect([]); 
        $processId = $request->process_id;
        $status = $request->status;
        $compositeId = $request->composite_id;
        $statusAPI = null;
        $new_result = [];
        $faults = [];
        
        $listDataResult = $this->findAPIBPMWorkflowByProcessId($processId);
            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];
            } else {
                $statusAPI = $listDataResult["result"];
            }
       
            $operationFlow = array(
                'INSTANCE_CREATED',
                'INSTANCE_RESUMED',
                'INSTANCE_SUSPENDED',
                'INSTANCE_TERMINATED',
                'INSTANCE_ABORTED',
                'INSTANCE_SYSTEM_FAULT');
            
            if (isset($listdata["audits"])) {
            foreach ($listdata["audits"] as $result) {

                
                $date = date("d/m/Y H:i", substr($result["create_time_long"], 0, 10));
                $newArrayDate = [$result["operation_flow"],$date];
                
                
                if (in_array($result["operation_flow"], $operationFlow)){

                    $itemname = $result["operation_flow"] .$result["create_time_long"];
                    $new_result[$itemname]["name"] = $result["name"];
                    $new_result[$itemname]["operation_flow"] = $result["operation_flow"];
                    $new_result[$itemname]["activity_id"] = $result["activity_id"];
                    $new_result[$itemname]["create_time"][] = $result["create_time"];
                    $new_result[$itemname]["create_time_long"][] = $newArrayDate;
                    
                } else if($result["name"] == 'Subprocess1' && !in_array($result["operation_flow"], $operationFlow)){
                    
                   $itemname = $result["name"] .$date;
                    $new_result[$itemname]["name"] = $result["name"];
                    $new_result[$itemname]["operation_flow"] = $result["operation_flow"];
                    $new_result[$itemname]["activity_id"] = $result["activity_id"];
                    $new_result[$itemname]["create_time"][] = $result["create_time"];
                    $new_result[$itemname]["create_time_long"][] = $newArrayDate;
                    
                }else {
                    $itemname = $result["scope_id"];
                    
                    $new_result[$itemname]["name"] = $result["name"];
                    $new_result[$itemname]["operation_flow"] = $result["operation_flow"];
                    $new_result[$itemname]["activity_id"] = $result["activity_id"];
                    $new_result[$itemname]["create_time"][] = $result["create_time"];
                    $new_result[$itemname]["create_time_long"][] = $newArrayDate;
                }
            }
        }

        if(isset($listdata["faults"]) && $listdata["faults"] != null){
            $faults = $listdata["faults"][0];
        }

        $data->put('instance_id', $compositeId);
        $data->put('process_id', $processId);
        $data->put('status', $status);
        $data->put('audits',$new_result);
        $data->put('faults',$faults);
        $data->put('statusAPI', $statusAPI);
        
        return $data;
    }
    
    //Search BPM Initiate Purchase Request
    public function searchTaskPR(){
        $listdata = collect([]);
        return view('bpm_api.order.bpm_initiate_pr',[
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }
    
    //Refire BPM Initiate Purchase Request
    public function doActionTaskPR() {

        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        $listDataResult = null;

        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'pr_no' => 'required',
                'action_task_order_pr' => 'required',
                'is_trigger_bpm_pr' => 'required'
            ]);

            $docNo = request()->pr_no;
            $actionTask = request()->action_task_order_pr;
            $triggerBpm = request()->is_trigger_bpm_pr;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-PR-CreateTask';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Order');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Order-' . $actionTask;
            $status = 'Completed';

            $typeDoc = substr($docNo, 0, 2);
            
            if ($typeDoc == 'PR' && strlen($docNo) == 17) {
                // check document exist or not
                $getRNNo = $this->getRnNoByPR($docNo);

                //search assigned task using rn no
                if (count((array)$getRNNo) > 0) {
                    $checkAssignedTaskRNBPM = $this->getTaskAssignedPR($getRNNo->rn_no);

                    //if no assigned task search by rn, try search using pr no
                    if (count($checkAssignedTaskRNBPM) == 0) {
                        $checkAssignedTaskPRBPM = $this->getTaskAssignedPR($docNo);
                        $checkAssignedTaskBPM = $checkAssignedTaskPRBPM;
                    } else {
                        $checkAssignedTaskBPM = $checkAssignedTaskRNBPM;
                    }

                    //if not found, search assigned task using pr no 
                } else {
                    $checkAssignedTaskPRBPM = $this->getTaskAssignedPR($docNo);
                    $checkAssignedTaskBPM = $checkAssignedTaskPRBPM;
                }

                //if trigger bpm is true, check for assigned 
                if ($triggerBpm == 'true') {
                    //checking for assigned task
                    if (count($checkAssignedTaskBPM) > 0) {
                        $listDataResult = $this->doAPIInitiatePurchaseRequest($docNo, $actionTask, 'false');
                        $statusAPI = 'Running Instance Detected. Instance ID : ' . $checkAssignedTaskBPM[0]->composite_id;
                        //no assigned task.. can refire if trigger bpm is true
                    } else {
                        $listDataResult = $this->doAPIInitiatePurchaseRequest($docNo, $actionTask, $triggerBpm);
                    }

                    // if trigger bpm is false 
                } else {
                    $listDataResult = $this->doAPIInitiatePurchaseRequest($docNo, $actionTask, $triggerBpm);
                }

                // if call bpm api success, check trigger action
                if ($listDataResult['status'] == 'Success') {
                    if ($actionTask == 'initiate-update-task-pr') {
                        if (isset($listDataResult["result"]["result"])) {
                            $listdata = $listDataResult;
                        } else {
                            $listdata = '';
                            $statusAPI = $listDataResult["status"] . '!!' . $listDataResult["result"]["remarks"];
                        }
                    } else {
                        $listdata = $listDataResult;
                    }

                } else {
                    $statusAPI = $listDataResult["result"];
                    $listdata = '';
                    $status = 'Failed';
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct PR Number';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
        }

        return view('bpm_api.order.bpm_initiate_pr', [
            'status_api' => $statusAPI,
            'task_assigned' => $checkAssignedTaskBPM,
            'listdata' => $listdata,
            'action' => $actionTask]);
    }

    //Search BPM Initiate Contract Request
    public function searchTaskCR(){
        $listdata = collect([]);
        return view('bpm_api.order.bpm_initiate_cr',[
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }
    
    //Refire BPM Initiate Contract Request
    public function doActionTaskCR() {

        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        $listDataResult = null;

        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'cr_no' => 'required',
                'action_task_order_cr' => 'required',
                'is_trigger_bpm_cr' => 'required'
            ]);

            $docNo = request()->cr_no;
            $actionTask = request()->action_task_order_cr;
            $triggerBpm = request()->is_trigger_bpm_cr;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-CR-CreateTask';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Order');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Order-' . $actionTask;
            $status = 'Completed';

            $checkAssignedTaskBPM = $this->getTaskAssignedCR($docNo);

            $typeDoc = substr($docNo, 0, 2);
            
            if ($typeDoc == 'CR' && strlen($docNo) == 17) {
                //if trigger bpm is true, check for assigned 
                if ($triggerBpm == 'true') {

                    //checking for assigned task
                    if (count($checkAssignedTaskBPM) > 0) {
                        $listDataResult = $this->doAPIInitiateContractRequest($docNo, $actionTask, 'false');
                        $statusAPI = 'Running Instance Detected. Instance ID : ' . $checkAssignedTaskBPM[0]->composite_id;

                        //no assigned task.. can refire if trigger bpm is true
                    } else {
                        $listDataResult = $this->doAPIInitiateContractRequest($docNo, $actionTask, $triggerBpm);
                    }

                    // if trigger bpm is false 
                } else {
                    $listDataResult = $this->doAPIInitiateContractRequest($docNo, $actionTask, $triggerBpm);
                }

                // if call bpm api success, check trigger action
                if ($listDataResult['status'] == 'Success') {
                    if ($actionTask == 'initiate-update-task-cr') {
                        if (isset($listDataResult["result"]["result"])) {
                            $listdata = $listDataResult;
                        } else {
                            $listdata = '';
                            $statusAPI = $listDataResult["status"] . '!!' . $listDataResult["result"]["remarks"];
                            $status = 'Failed';
                        }
                    } else {
                        $listdata = $listDataResult;
                    }
                    
                } else {
                    $statusAPI = $listDataResult["result"];
                    $listdata = '';

                    $status = 'Failed';
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct CR Number';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);

            return view('bpm_api.order.bpm_initiate_cr', [
                'status_api' => $statusAPI,
                'task_assigned' => $checkAssignedTaskBPM,
                'listdata' => $listdata,
                'action' => $actionTask]);
        }
    }

    //Search BPM Initiate Contract Request
    public function searchTaskDO() {
        $listdata = collect([]);
        return view('bpm_api.fl.bpm_initiate_do', [
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }

    //Refire BPM Initiate Delivery Order
    public function doActionTaskDO() {

        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        $loginId = null;
        $listDataResult = null;
//        Log::info('doActionTaskDO');
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'do_no' => 'required',
                'action_task_fl_do' => 'required',
                'is_trigger_bpm_do' => 'required'
            ]);
//            Log::info('doActionTaskDO if post');
            $docNo = request()->do_no;
            $actionTask = request()->action_task_fl_do;
            $triggerBpm = request()->is_trigger_bpm_do;
            
            if($actionTask == 'acknowledge-receiving-note-officer') {
                $loginId = request()->officer_login_id;
            }

            $actionTypeLog = 'Web Service';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Fulfilment');

            $actionName = 'BPM-DOFulfilment-' . $actionTask;
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $status = 'Completed';

            $typeDoc = substr($docNo, 0, 2);
            
            if (strlen($docNo) == 17) {

                // checking workflow status po/co before refire
                $currentWorkflowStatus = $this->getPOCOWorkflowByDoNumber($docNo);

//                Log::info('doActionTaskDO if do ');
                //start checking assigned task if bpm trigger is true
                if ($triggerBpm == 'true') {
//                    Log::info('doActionTaskDO if trigger true');
                    // allow refire for status fulfilment/ partial fulfilment only
                    $wfStatus = array('41505', '41005', '41010', '41510');
//                    if ($currentWorkflowStatus && in_array($currentWorkflowStatus[0]->statusid, $wfStatus)) {
                        //start checking if assigned task
                        $checkAssignedTaskBPM = $this->getTaskAssignedDOFN($docNo);
//                        Log::info('doActionTaskDO if current ws fulfilment');
                        //if assigned task exist, set bpm trigger to false
                        if (count($checkAssignedTaskBPM) > 0) {
                            $triggerBpm = 'false';
//                            Log::info('doActionTaskDO if assigned exist');
                        }

                        //end checking current workflow status
//                    } else {
////                        Log::info('doActionTaskDO current ws not fulfilment');
//                        $triggerBpm = 'false';
//                        $statusAPI = 'Current PO/CO status : ' . $currentWorkflowStatus[0]->statusname . ' .Please check again..';
//                    }

                    //refire using do number
                    if ($actionTask == 'initiate-update-dofrn') {
//                        Log::info('doActionTaskDO action initiate update do');
                        $listDataResult = $this->doAPIInitiateDoFulfilment($docNo, $actionTask, $loginId, $triggerBpm);

                        if ($listDataResult['status'] == 'Success') {
//                            Log::info('doActionTaskDO listdataresult success');
                            if (isset($listDataResult["result"]["result"])) {
                                $listdata = $listDataResult;
                            } else {
//                                Log::info('doActionTaskDO listdataresult not result result');
                                $listdata = '';
                                $statusAPI = $listDataResult["result"]["remarks"];
                                $status = 'Failed';
                            }
                        } else {
//                            Log::info('doActionTaskDO listdataresult not success');
                            $statusAPI = $listDataResult["result"] . '!!';
                            $listdata = '';
                            $status = 'Failed';
                        }
                        //refire using do id     
                    } else {
//                        Log::info('doActionTaskDO action else initiate update do');
                        $listDataResult = $this->doAPIInitiateDoFulfilment($currentWorkflowStatus[0]->doid, $actionTask, $loginId, $triggerBpm);

                        if ($listDataResult['status'] == 'Success') {
//                            Log::info('doActionTaskDO action else initiate update do success');
                            $listdata = $listDataResult;
                        } else {
//                            Log::info('doActionTaskDO action else initiate update do not success');
                            $statusAPI = $listDataResult["result"] . '!!';
                            $listdata = '';
                            $status = 'Failed';
                        }
                    }
                    //trigger action false
                } else {
                    //refire using do number
                    if ($actionTask == 'initiate-update-dofrn') {
                        $listDataResult = $this->doAPIInitiateDoFulfilment($docNo, $actionTask, $loginId, $triggerBpm);

                        if ($listDataResult['status'] == 'Success') {
                            if (isset($listDataResult["result"]["result"])) {
                                $listdata = $listDataResult;
                            } else {
                                $listdata = '';
                                $statusAPI = $listDataResult["result"]["remarks"];
                                $status = 'Failed';
                            }
                        } else {
                            $statusAPI = $listDataResult["result"] . '!!';
                            $listdata = '';
                            $status = 'Failed';
                        }
                        //refire using do id     
                    } else {
                        $listDataResult = $this->doAPIInitiateDoFulfilment($currentWorkflowStatus[0]->doid, $actionTask, $loginId, $triggerBpm);
                        if ($listDataResult['status'] == 'Success') {
                            $listdata = $listDataResult;
                        } else {
                            $statusAPI = $listDataResult["result"] . '!!';
                            $listdata = '';
                            $status = 'Failed';
                        }
                    }
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct DO Number (eg : 60000176581901839)';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);

            return view('bpm_api.fl.bpm_initiate_do', [
                'status_api' => $statusAPI,
                'task_assigned' => $checkAssignedTaskBPM,
                'listdata' => $listdata,
                'action' => $actionTask]);
        }
    }
    
    //Search BPM Initiate Contract Request
    public function searchTaskCreateSubmitInvoicePA() {
        return view('bpm_api.fl.bpm_create_submit_invoice_pa', [
            'status_api' => null,
            'dataTask' => null,
            'listdata' => null
        ]);
    }
    
    //Search BPM Initiate Contract Request
    public function doActionTaskCreateSubmitInvoicePA() {

        $statusAPI = null;
        $dataTask = null;
        $listdata = collect([]);
        $listDataResult = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'doc_no' => 'required',
                'action_task_create_submit_invoice_pa' => 'required',
                'is_trigger_bpm_create_submit_invoice_pa' => 'required'
            ]);

            $docNo = request()->doc_no;
            $actionTask = request()->action_task_create_submit_invoice_pa;
            $triggerBpm = request()->is_trigger_bpm_create_submit_invoice_pa;
            $loginId = request()->login_id;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Fulfilment';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Fulfilment');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Fulfilment-' . $actionTask;
            $status = 'Completed';
            
            $typeDoc = substr($docNo, 0, 2);
            
            if (($typeDoc == 'PO' || $typeDoc == 'CO') && strlen($docNo) == 17) {
                // Start action create-invoice, call service instead of bpm api
                if ($actionTask == 'create-invoice') {
                    $typeDoc = substr($docNo, 0, 2);
                    $stuckCreateInv = false;

                    if ($typeDoc == 'PO' && $this->isStuckCreateInvoicePOInEP($docNo) == true) {
                        $stuckCreateInv = true;
                    }
                    if ($typeDoc == 'CO' && $this->isStuckCreateInvoiceCOInEP($docNo) == true) {
                        $stuckCreateInv = true;
                    }

                    if ($stuckCreateInv == true) {
                        $xmlData = self::getXMLCreateInvoice($docNo);
                        if ($xmlData != null) {
                            self::triggerPOSTCreateInvoice($xmlData, $docNo);
                            sleep(10);
                            $dataTask = $this->findAPITaskBPMListDocAndModule($docNo, 'Fulfilment');
                            if (count($dataTask['result']) > 0) {
                                Log::debug($dataTask['result'][0]['taskId'] . ' -- instance_id : ' . $dataTask['result'][0]['instanceId'] . ' -- version : ' . $dataTask['result'][0]['compositeDN']);
                            } else {
                                Log::debug('##### need to check');
                                Log::debug(self::class . ' refire createinvoice not return success result ... ' . __FUNCTION__);
                                Log::debug($docNo);
                            }
                        }
                    } else {
                        $status = 'Failed';
                        $statusAPI = $docNo . ' >> Is not related in stuck task create invoice';
                    }
                }
                // End action create-invoice, call service instead of bpm api
                else {

                    //get fl_order_id 
                    $getDocDetails = DB::connection('oracle_nextgen_rpt')
                            ->table('fl_fulfilment_order a')
                            ->where('a.doc_no', $docNo)
                            ->select('a.fulfilment_order_id as fl_order_id', 'a.doc_no as doc_no')
                            ->first();

                    if ($getDocDetails && $getDocDetails->fl_order_id != null) {

                        if ($actionTask == 'submit-invoice') {
                            $listDataResult = $this->doAPICreateSubmitInvoicePA($getDocDetails->fl_order_id, $actionTask, $loginId, $triggerBpm);
                        } else {
                            $listDataResult = $this->doAPICreateSubmitInvoicePA($getDocDetails->fl_order_id, $actionTask, $loginId, $triggerBpm);
                        }
                    }

                    // if call bpm api success, check trigger action
                    if ($listDataResult['status'] == 'Success') {
                        $listdata = $listDataResult["result"];
                    } else {
                        $statusAPI = $listDataResult["result"] . '!!';
                        $listdata = '';
                        $status = 'Failed';
                    }
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct PO/CO Number';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
            
        }
        return view('bpm_api.fl.bpm_create_submit_invoice_pa', [
            'status_api' => $statusAPI,
            'dataTask' => $dataTask,
            'listdata' =>$listdata]);
    }
   
    public function searchTaskModifyCancelInvoice(){
        $listdata = collect([]);
        return view('bpm_api.fl.bpm_modify_cancel_invoice', [
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }
    
    public function doActionTaskModifyCancelInvoice(){
        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'invoice_no' => 'required',
                'action_task_modify_cancel_invoice' => 'required',
                'is_trigger_bpm_modify_cancel_invoice' => 'required'
            ]);

            $docNo = request()->invoice_no;
            $actionTask = request()->action_task_modify_cancel_invoice;
            $triggerBpm = request()->is_trigger_bpm_modify_cancel_invoice;
            
            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Fulfilment';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Fulfilment');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Fulfilment-' . $actionTask;
            $status = 'Completed';
            
            $typeDoc = substr($docNo, 0, 2);
            
            if ($typeDoc == '60' && strlen($docNo) == 17) {
                //get invoice_id 
                $getDocDetails = DB::connection('oracle_nextgen_rpt')
                        ->table('FL_INVOICE a')
                        ->where('a.INVOICE_NO', $docNo)
                        ->select('a.INVOICE_ID as invoice_id', 'a.INVOICE_NO as doc_no')
                        ->first();

                if ($getDocDetails && $getDocDetails->invoice_id != null) {
                    $listDataResult = $this->doAPIModifyCancelInvoice($getDocDetails->invoice_id, $actionTask, $triggerBpm);

                    // if call bpm api success, check trigger action
                    if ($listDataResult && $listDataResult['status'] == 'Success') {
                        $listdata = $listDataResult;
                    } else {
                        $statusAPI = $listDataResult["result"] . '!!';
                        $listdata = '';
                        $status = 'Failed';
                    }
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct Invoice Number (eg: 60000176581901636)';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
        }
        return view('bpm_api.fl.bpm_modify_cancel_invoice', [ 
                'status_api' => $statusAPI,
                'task_assigned' => $checkAssignedTaskBPM,
                'listdata' => $listdata,
                'action' => $actionTask]);
    }
    
    public function searchTaskModifyCancelDO(){
        $listdata = collect([]);
        return view('bpm_api.fl.bpm_modify_cancel_do', [
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }
    
    public function doActionTaskModifyCancelDO(){
        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'do_no' => 'required',
                'action_task_modify_cancel_do' => 'required',
                'is_trigger_bpm_modify_cancel_do' => 'required'
            ]);

            $docNo = request()->do_no;
            $actionTask = request()->action_task_modify_cancel_do;
            $triggerBpm = request()->is_trigger_bpm_modify_cancel_do;
            
            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Fulfilment';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Fulfilment');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Fulfilment-' . $actionTask;
            $status = 'Completed';
            
            $typeDoc = substr($docNo, 0, 2);
            
            if ($typeDoc == '60' && strlen($docNo) == 17) {
                //get invoice_id 
                $getDocDetails = DB::connection('oracle_nextgen_rpt')
                        ->table('FL_DELIVERY_ORDER a')
                        ->where('a.DELIVERY_ORDER_NO', $docNo)
                        ->select('a.DELIVERY_ORDER_ID as order_id')
                        ->first();

                //start checking assigned task if bpm trigger is true
                if ($getDocDetails && $getDocDetails->order_id != null) {

                    $listDataResult = $this->doAPIModifyCancelDO($getDocDetails->order_id, $actionTask, $triggerBpm);

                    // if call bpm api success, check trigger action
                    if ($listDataResult['status'] == 'Success') {
                        $listdata = $listDataResult;
                    } else {
                        $statusAPI = $listDataResult["result"] . '!!';
                        $listdata = '';
                        $status = 'Failed';
                    }
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct DO Number (eg : 60000176581901839)';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
        }
        return view('bpm_api.fl.bpm_modify_cancel_do', [ 
                'status_api' => $statusAPI,
                'task_assigned' => $checkAssignedTaskBPM,
                'listdata' => $listdata,
                'action' => $actionTask]);
    }
    
    public function searchTaskStopInstruction(){
        $listdata = collect([]);
        return view('bpm_api.fl.bpm_stop_instruction', [
            'listdata' => $listdata,
            'status_api' => null,
            'task_assigned' => null
        ]);
    }
    
    public function doActionTaskStopInstruction(){
        $listdata = collect([]);
        $statusAPI = null;
        $checkAssignedTaskBPM = null;
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'sd_no' => 'required',
                'action_task_stop_instr' => 'required',
                'is_trigger_stop_instr' => 'required',
                'supplier_login_id' => 'required'
            ]);

            $docNo = request()->sd_no;
            $actionTask = request()->action_task_stop_instr;
            $loginId = request()->supplier_login_id;
            $triggerBpm = request()->is_trigger_stop_instr;
            
            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-Fulfilment';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("supplier_login_id", $loginId);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'Fulfilment');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-Fulfilment-' . $actionTask;
            $status = 'Completed';
            
            $typeDoc = substr($docNo, 0, 2);
            
            if ($typeDoc == 'SD' && strlen($docNo) == 17) {
                //get invoice_id 
                $getDocDetails = DB::connection('oracle_nextgen_rpt')
                        ->table('FL_STOP_INSTR a')
                        ->where('a.STOP_INSTR_NO', $docNo)
                        ->select('a.STOP_INSTR_ID as sd_id', 'a.STOP_INSTR_NO as doc_no')
                        ->first();

                //start checking assigned task if bpm trigger is true
                if ($getDocDetails && $getDocDetails->sd_id != null) {

                    $listDataResult = $this->doAPIStopInstr($getDocDetails->sd_id, $loginId, $triggerBpm);

                    // if call bpm api success, check trigger action
                    if ($listDataResult['status'] == 'Success') {
                        $listdata = $listDataResult;
                    } else {
                        $statusAPI = $listDataResult["result"] . '!!';
                        $listdata = '';
                        $status = 'Failed';
                    }
                }
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct SD Number';
                $status = 'Failed';
            }

            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
        }
        return view('bpm_api.fl.bpm_stop_instruction', [ 
                'status_api' => $statusAPI,
                'task_assigned' => $checkAssignedTaskBPM,
                'listdata' => $listdata,
                'action' => $actionTask]);
    }
    
    public function getXMLCreateInvoice($docNo) {
                
        $invoiceDetails = $this->getInvoiceDetails($docNo);
        $approverDetails = $this->getApproverDetails($docNo);
        $supplierDetails = $this->getSupplierDetails($docNo);
        $supplierUsers = $this->getListSupplierUsers($invoiceDetails->supplier_id);
        $orderDetails = $this->getOrder($docNo);
        
        $orderDet = null;
        $isPHIS = 'false';
        $stopInst = 'false';
        if(count($orderDetails) > 0){
            $orderDet =   $orderDetails[0];
            if($orderDet->phis != ''){
                $isPHIS = 'true';
            }
            if($orderDet->stopinst != ''){
                $stopInst = 'true';
            }
            
            if($orderDet->orderno == '' || $orderDet->orderno == null){
                dump('ERROR: SAPOrderNo is NULL');
                return null;
            }
        }
       
        /** Skip if null **/
        if($invoiceDetails == null){
            dump('ERROR: Invoice Detail is NULL');
            return null;
        }
        if($approverDetails == null){
            dump('ERROR: Approver Detail is NULL');
            return null;
        }
        if($supplierDetails == null){
            dump('ERROR: Supplier Detail is NULL');
            return null;
        }
        if(count($supplierUsers) == 0){
            dump('ERROR: Supplier Users  is NULL');
            return null;
        }
        if(count($orderDetails)  == 0){
            dump('ERROR: Order Detail  is NULL');
            return null;
        }
        
        
        $ordername = str_replace("&","&amp;",$invoiceDetails->ordername);
        $approverDesignation = str_replace("&","&amp;",$approverDetails->designation);

        
        $xmlSupplierUsers = '';
        foreach ($supplierUsers as $suppUser) {
                $suppUserDesignation = str_replace("&","&amp;",$suppUser->designation);
                $strUserInfo = 
                    '<ns2:supplierList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">'.
                        '<ns4:userId>' . $suppUser->user_id . '</ns4:userId>'.
                        '<ns4:userLoginId>' . $suppUser->login_id . '</ns4:userLoginId>'.
                        '<ns4:userName>' . $suppUser->user_name . '</ns4:userName>'.
                        '<ns4:calendarName>PUTRAJAYA</ns4:calendarName>'.
                        '<ns4:designation>' . $suppUserDesignation . '</ns4:designation>'.
                    '</ns2:supplierList>';
                
                $xmlSupplierUsers = $xmlSupplierUsers.$strUserInfo;
        }
            
        $xmlCreateInvoice    = 
        '<x:Envelope xmlns:x="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns1="http://xmlns.oracle.com/bpmn/bpmnProcess/InvoiceAndPaymentCreation" xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/Order/FL_Order_Data" xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data" xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/GFMAS/SD/FL_Stop_Delivery_Req_Data" xmlns:ns5="http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Item_Data">'.        
            '<x:Header />'.
            '<x:Body>'.
                '<ns1:startInvoice>'.
                    '<ns2:FL_Order_Data xmlns:ns2="http://xmlns.oracle.com/bpm/bpmobject/Data/Order/FL_Order_Data">'.
                        '<ns2:requestId>'.$invoiceDetails->requestid.'</ns2:requestId>'.
                        '<ns2:orderId>'.$invoiceDetails->orderid.'</ns2:orderId>'.
                        '<ns2:orderName>'.$ordername.'</ns2:orderName>'.
                        '<ns2:orderType>'.$invoiceDetails->ordertype.'</ns2:orderType>'.
                        '<ns2:reqDocNo />'.
                        '<ns2:orderDocNumber>'.$invoiceDetails->orderdocnmber.'</ns2:orderDocNumber>'.
                        '<ns2:approverList xmlns:ns4="http://xmlns.oracle.com/bpm/bpmobject/Data/FL_User_Data">'.
                            '<ns4:userId>' . $approverDetails->userid . '</ns4:userId>'.
                            '<ns4:userLoginId>' . $approverDetails->loginid . '</ns4:userLoginId>'.
                            '<ns4:userName>' . $approverDetails->username . '</ns4:userName>'.
                            '<ns4:calendarName>PUTRAJAYA</ns4:calendarName>'.
                            '<ns4:designation>' . $approverDesignation . '</ns4:designation>'.
                        '</ns2:approverList>'.
                        $xmlSupplierUsers.
                        '<ns2:sapOrderNo>'.$orderDet->orderno.'</ns2:sapOrderNo>'.
                        '<ns2:businessArea>'.$orderDet->businessarea.'</ns2:businessArea>'.
                    '</ns2:FL_Order_Data>'.
                    '<ns1:createdBy>'.$supplierDetails->loginid.'</ns1:createdBy>'.
                    '<ns1:isPhis>'.$isPHIS.'</ns1:isPhis>'.
                    '<ns1:hasStopFulfillment>'.$stopInst.'</ns1:hasStopFulfillment>'.
                    '<ns3:FL_Stop_Delivery_Req_Data xmlns:ns3="http://xmlns.oracle.com/bpm/bpmobject/Data/GFMAS/SD/FL_Stop_Delivery_Req_Data">'.
                        '<ns3:stopInstrutionReqId xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:orderNo />'.
                        '<ns3:stopInstructionNo />'.
                        '<ns3:postingDate xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:instructionItemList xmlns:ns5="http://xmlns.oracle.com/bpm/bpmobject/Data/Common/FL_Item_Data">'.
                            '<ns5:itemCode xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:lineItem xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:amt xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:quantity xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                            '<ns5:unitOfMeasure xmlns:ns6="http://www.w3.org/2001/XMLSchema-instance" ns6:nil="true" />'.
                        '</ns3:instructionItemList>'.
                        '<ns3:stopInstructionDate xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:createdBy />'.
                        '<ns3:approvedBy />'.
                        '<ns3:sapPOCONo />'.
                        '<ns3:orderId xmlns:ns5="http://www.w3.org/2001/XMLSchema-instance" ns5:nil="true" />'.
                        '<ns3:uuid />'.
                        '<ns3:businessArea />'.
                    '</ns3:FL_Stop_Delivery_Req_Data>'.
                    '<ns1:taxInvoice>true</ns1:taxInvoice>'.
                    '<ns1:taxRelief>false</ns1:taxRelief>'.
                    '<ns1:skipTax>true</ns1:skipTax>'.
                '</ns1:startInvoice>'.
            '</x:Body>'.
        '</x:Envelope>';
        
        //dump($xmlCreateInvoice);
        
        return $xmlCreateInvoice;
    }
    
    public function triggerPOSTCreateInvoice($xmlData, $docNo) {
        //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePNo</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        //$xmlContents = '"'.$xmlData.'"';

        $client = new Client([
            'base_uri' => 'http://*************:9003',
        ]);
        $payload = $xmlData;
        $response = $client->post('http://*************:9003/soa-infra/services/default/Fulfilment/InvoiceAndPaymentCreation.service', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction' => 'startInvoice',
            ]
        ]);
        $body = $response->getStatusCode();

    }
    
    public function searchTaskSourcingSQCreation(){
        $listdata = collect([]);
        return view('bpm_api.sourcing.sqcreation', [
           'status_api' => null,
            'dataTask' => null,
            'checkTask' => null,
            'listdata' => $listdata]);
    }

    public function doActionTaskSourcingSQCreation() {
        $statusAPI = null;
        $dataTask = null;
        $listdata = collect([]);
        $checkTask = null; 
        
        session()->flashInput(request()->input());
        if (request()->isMethod("POST")) {
            $this->validate(request(), [
                'sq_no' => 'required',
                'action_task_sqcreation' => 'required',
                'is_trigger_bpm_sqcreation' => 'required'
            ]);

            $docNo = request()->sq_no;
            $actionTask = request()->action_task_sqcreation;
            $triggerBpm = request()->is_trigger_bpm_sqcreation;

            $actionTypeLog = 'Web Service';
            $actionName = 'BPM-SourcingDP';

            $parameters = collect([]);
            $parameters->put("doc_no", $docNo);
            $parameters->put("action_task", $actionTask);
            $parameters->put("trigger_bpm", $triggerBpm);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            $parameters->put("module", 'SourcingDP');

            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $actionName = 'BPM-SourcingDP-' . $actionTask;
            $status = 'Completed';

            $typeDoc = substr($docNo, 0, 2);

            if ($typeDoc == 'SQ' && strlen($docNo) == 17) { 
                
                //check task
                //$checkTask = $this->findAPITaskBPMListDocAndModule($docNo, 'SourcingDP');
                
                $isRefire = false;
                $checkTask = null;
                $dataTask = null;
                $checkTaskListAll = $this->findAPITaskBPMListDocAndModule($docNo, 'SourcingDP'); 
                if(count($checkTaskListAll['result']) > 0){
                    $cdataTasks = collect($checkTaskListAll['result']);
                    $checkTaskList = $cdataTasks->where('state','ASSIGNED')->all();
                    if(collect($checkTaskList)->count() == 0){
                        $isRefire = true;
                    }else{
                        $checkTask = collect($checkTaskList)->first();
                        Log::debug('Checking '.$docNo.' already has task created. TASK >> STATE: '.$checkTask['state'] .' TaskID: '.$checkTask['taskId'] . ' -- instance_id : '.$checkTask['instanceId']. ' -- version : '.$checkTask['compositeDN']);
                    }
                }

                if (count($checkTaskListAll['result']) == 0) {
                    $isRefire = true;
                }

                if ($isRefire === true) { 
                    $elements = self::getXMLCreateSQ($docNo);
                  
                    if ($elements != null) { 

                        $compositeName = 'SourcingDP';
                        $process = 'SimpleQuoteCreation';
                        $trigger = 'start';
                        $createService = $this->findApiBPMCreateServiceManager($compositeName, $process, $trigger, $elements);

                        Log::info(__METHOD__.'  >> findApiBPMCreateServiceManagers :: '.json_encode($createService).' , DocNo: '.$docNo);
                        sleep(3);
                        if($createService  != null && $createService['status'] === 'Success'){

                            $dataTaskList = $this->findAPITaskBPMListDocAndModule($docNo, 'SourcingDP');
                            if(count($dataTaskList['result']) > 0 && $dataTaskList['result'][0] != null){
                                $dataTask = collect($dataTaskList['result']);
                                Log::info(' TASK >> STATE: '.$dataTaskList['result'][0]['state'] .' TaskID: '.$dataTaskList['result'][0]['taskId'] . ' -- instance_id : '.$dataTaskList['result'][0]['instanceId']. ' -- version : '.$dataTaskList['result'][0]['compositeDN']);
                            } else {
                                $dataTask = array('remarks' => 'Done refire service SQ Creation but not found new instance.  Kindly check manual>> '.json_encode($createService));
                                Log::info('  Done refire service SQ Creation but not found new instance.  Kindly check manual.  >> '.json_encode($createService));
                            }
                        
                        }
                    }
                }  
            } else {
                $statusAPI = 'Document Does Not Exist.. Make Sure to Enter Correct SQ Number';
                $status = 'Failed';
            }
            EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
        }
        return view('bpm_api.sourcing.sqcreation', [
            'status_api' => $statusAPI,
            'dataTask' => $dataTask,
            'checkTask' => $checkTask,
            'listdata' => $listdata]);
    }

    public function getXMLCreateSQ($docNo) {
        $simpleQuoteDetails = $this->getSimpleQuoteDetails($docNo);

        if ($simpleQuoteDetails != null) {
            $orgDetails = $this->getOrgProfileDetails($simpleQuoteDetails->org_profile_id);
            $taskPerformerDetails = $this->getTaskPerformerdetails($simpleQuoteDetails->created_by);
            $simpleQuoteDetails->start_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->start_date));
            $simpleQuoteDetails->end_date = date('Y-m-d\TH:i:s', strtotime($simpleQuoteDetails->end_date));
            
            $title = str_replace("&", "&amp;", $simpleQuoteDetails->title);
            $ptjName = str_replace("&", "&amp;", $orgDetails[0]->ptjname);
            $ptjAddress = str_replace("&", "&amp;", $orgDetails[0]->ptjaddress);
            
            $xmlDirectPurchaseData = '<SC_DirectPurchase_Data xmlns="http://xmlns.oracle.com/bpm/bpmobject/Data/SC_DirectPurchase_Data">
                <title>' . $title . '</title>
                <dp_id>' . $simpleQuoteDetails->dp_id . '</dp_id>
                <rn_id>' . $simpleQuoteDetails->rn_id . '</rn_id>
                <pid_id/>
                <dp_mode>SQ</dp_mode>
                <url_link/>
                <ptj_name>' . $ptjName . '</ptj_name>
                <ptj_address>' . $ptjAddress . '</ptj_address>
                <recommend>user011</recommend>
                <contract_id/>
                <contract_type/>
                <frequency/>
                <loi_loa_creator/>
                <start_date>' . $simpleQuoteDetails->start_date . '+08:00</start_date>
                <end_date>' . $simpleQuoteDetails->end_date . '+08:00</end_date>
                <rn_document_number>' . $simpleQuoteDetails->request_note_no . '</rn_document_number>
                <contract_number/>
                <contract_request/>
                <sq_request/>
                <pid_expiry_date/>
                <pid_expiry_duration/>
                <pid_approver/>
                <kpi_value/>
                <sq_document_number/>
                <pid_document_number/>
                <desk_officer/>
                <sq_expiry_date/>
                <sq_expiry_duration/>
            </SC_DirectPurchase_Data>';
            $documentNumber = $simpleQuoteDetails->quote_no;
            $taskPerformer = $taskPerformerDetails->login_id;

            $elements = [
                [
                    "name"=>"SC_DirectPurchase_Data",
                    "value"=>$xmlDirectPurchaseData,
                    "type"=>"string"
                ],
                [
                    "name"=>"document_number",
                    "value"=>$documentNumber,
                    "type"=>"string"
                ],
                [
                    "name"=>"task_performer",
                    "value"=>$taskPerformer,
                    "type"=>"string"
                ],
            ]; 
            return $elements;
        }
        
        return null;
    }
    
    public function triggerPOSTCsreateSQ($xmlData) {
        //$xmlContents = "<soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/'><soap:Header xmlns:soap='http://schemas.xmlsoap.org/soap/envelope/'></soap:Header><soapenv:Body><trig:TriggerAPIVEInput xmlns:trig='http://xmlns.oracle.com/pcbpel/adapter/db/TriggerAPIVE'><trig:ep_no>$ePNo</trig:ep_no></trig:TriggerAPIVEInput></soapenv:Body></soapenv:Envelope>";
        //$xmlContents = '"'.$xmlData.'"';

        $client = new Client([
            'base_uri' => 'http://*************:9004',
          ]);
          $payload = $xmlData;
          $response = $client->post('http://*************:9004/soa-infra/services/default/SourcingDP/SimpleQuoteCreation.service', [
            //'debug' => TRUE,
            'body' => $payload,
            'headers' => [
                'Content-Type' => 'text/xml; charset=utf-8',
                'SOAPAction'   => 'start',
            ]
          ]);
          $body = $response->getStatusCode();
          dd($body);
//          Log::info('triggerPOSTCsreateSQ    status: '.$body);

    }
    
    public function alterflowProcessBpmn($processId, $status, Request $request) {
        $data = collect([]);
        $listdata = collect([]);
        $statusApi = null;
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $processId = $processId;
            $actionTask  = $request->action;
            $compositeId = $request->composite_id;
            
            $actionTypeLog = 'Web Service';

            $parameters =  collect([]);   
            $parameters->put('instance_id', $compositeId);
            $parameters->put("process_id", $processId);
            $parameters->put("status", $status);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            if ($actionTask == 'alterflow') {
                
                $actionName = 'BPM-Get-AlterFlow'; 
                $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
                
                $listDataResult = $this->alterflowBpmnProcessId($request->process_id);
                if($listDataResult != null){
                    if ($listDataResult['status'] == 'Success') {   
                        $listdata = $listDataResult["result"];
                        $data->put('status', 'success alter flow');
                        $data->put('listdata', $listdata);
                        
                        $status = 'Completed';
                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
                        
                        return $data;
                    }else{
                        $statusApi = $listDataResult["result"];
                        $data->put('status', 'failed alter flow');
                        $data->put('statusAPI', $statusApi);
                        
                        $status = 'Failed';
                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
                        
                        return $data;
                    }  
                }else{
                    $data->put('status', 'failed alter flow');
                    $data->put('statusApi', $listDataResult);
                    
                    $status = 'Failed';
                    EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status); 
                    
                    return $data;
                }            
            }
        }
    }
    
    public function variableProcessBpmn($processId, $status, Request $request) {
        $data = collect([]);
        $listdata = collect([]);
        $statusApi = null;
        
        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {

            $processId = $processId;
            $actionTask  = $request->action;
            $compositeId = $request->composite_id;
            
            $actionTypeLog = 'Web Service';

            $parameters =  collect([]); 
            $parameters->put('instance_id', $compositeId);
            $parameters->put("process_id", $processId);
            $parameters->put("status", $status);
            $parameters->put("action_task", $actionTask);
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            if ($actionTask == 'variable') {
                
                $actionName = 'BPM-Get-Variable';
                $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
                
                $listDataResult = $this->variableBpmnProcessId($request->process_id);
                
                if($listDataResult != null){
                    if ($listDataResult['status'] == 'Success') {   
                        $listdata = $listDataResult["result"];
                        $data->put('status', 'success variable');
                        $data->put('listdata', $listdata);
                        
                        $status = 'Completed';
                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status); 
                        
                        return $data;
                        
                    }else{
                        $statusApi = $listDataResult["result"];
                        $data->put('status', 'failed variable');
                        $data->put('statusApi', $statusApi);
                        
                        $status = 'Failed';
                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status); 
                        
                        return $data;
                    }    
                }else{
                    $data->put('status', 'failed variable');
                    $data->put('statusApi', $listDataResult);
                    
                    $status = 'Failed';
                    EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status); 
                    
                    return $data;
                }          
            }
        }
    }
    
    public function submitAlterflowProcessBpmn($processId,$source,$target,Request $request){
        $data = collect([]);
        $listdata = collect([]);
        $statusAPI = null;
        
        if ($request->isMethod("POST")) {            
            
            $actionTypeLog = 'Web Service';

            $parameters =  collect([]);   
            $parameters->put("instance_id", $request->composite_id);
            $parameters->put("process_id", $processId);
            $parameters->put("source", $source);
            $parameters->put("target", $target);
            $parameters->put("action_task", 'submit-alterflow');
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            $actionName = 'BPM-Submit-Alterflow';
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            
            $listDataResult = $this->submitAlterflowBpmnProcessId($processId,$source,$target);
            
            if ($listDataResult['status'] == 'Success') {
                $listdata = $listDataResult;                
                $data->put('status', 'Success Submit Alter Flow');
                $data->put('listdata', $listdata);
                
                $status = 'Completed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);        
                    
                return $data;
            } else {
                $statusAPI = $listDataResult["result"];
                $data->put('status', 'Failed Submit Alter Flow');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusAPI);
                
                $status = 'Failed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);
                
                return $data;
            }
        }
    }
    
    public function submitVariableProcessBpmn($processId,Request $request){
        $data = collect([]);
        $listdata = collect([]);
        $statusAPI = null;
            
        if ($request->isMethod("POST")) {
            
            $actionTypeLog = 'Web Service';
            $key = $request->key;
            $value = $request->value;
            $compositeId = $request->composite_id; 
            
            $parameters =  collect([]);    
            $parameters->put('instance_id', $compositeId);
            $parameters->put("process_id", $processId);
            $parameters->put("key", $key);
            $parameters->put("value", $value);
            $parameters->put("action_task", 'submit-variable');
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            $actionName = 'BPM-Submit-Variable';
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
           
            $listDataResult = $this->submitVariableBpmnProcessId($processId,$key,$value);

            if ($listDataResult['status'] == 'Success') {
                $listdata = $listDataResult;                
                $data->put('status', 'Success Submit Variable');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusAPI);
                
                $status = 'Completed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);           
                    
                return $data;
                
            } else {
                $statusAPI = $listDataResult["result"];
                $data->put('status', 'Failed Submit Variable');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusAPI);
                
                $status = 'Failed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);      
                
                return $data;
            }
        }
    }
    
    public function terminateInstance($instanceId, Request $request){
        $data = collect([]);
        $listdata = collect([]);
        $statusAPI = null;
        
        if ($request->isMethod("POST")) {
            $module = $request->module;
            $flowId = $request->flow_id;
            $docNo = $request->doc_no;
            
            $actionTypeLog = 'Web Service';
    
            $parameters = collect([]);            
            $parameters->put("instance_id", $instanceId);
            $parameters->put("module", $module);
            $parameters->put("action_task", 'terminate-task');
            $parameters->put("host_ws", env("JAVA_MIDDLEWARE_RESTFUL"));
            
            $actionName = 'BPM-Terminate-Task';
            $actionLog = EpSupportActionLog::saveActionLog($actionName, $actionTypeLog, $parameters, $parameters, 'Processing');
            $listDataResult = $this->submitTerminateInstance($module, $instanceId);
            
            if ($listDataResult['status'] == 'Success') {
                $listdata = $listDataResult;
                
                if (strpos($module, 'SourcingQT') !== false && $flowId && $docNo) {
                    try {
                        // Only modify if it starts with QT
                        if (strpos($docNo, 'QT') === 0) {
                            DB::connection('oracle_nextgen_soa_fullgrant')
                                ->table('wftask')
                                ->where('flow_id', $flowId)
                                ->where('COMPOSITENAME', 'SourcingQT')
                                ->where('CUSTOMATTRIBUTESTRING1', $docNo)
                                ->update(['CUSTOMATTRIBUTESTRING1' => 'QTX' . substr($docNo, 2)]);
                        }
                    } catch (\Exception $e) {
                        Log::error('Error updating WFTASK table: ' . $e->getMessage());
                    }
                }
                    
                $data->put('status', 'Success Terminate Instance');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusAPI);
                
                $status = 'Completed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);           
                    
                return $data;
                
            } else {       
                $statusAPI = $listDataResult["result"];
                $data->put('status', 'Failed Terminate Instance');
                $data->put('listdata', $listdata);
                $data->put('statusApi', $statusAPI);
                
                $status = 'Failed';
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, $status);      
                
                return $data;
            }
        }
    }
    
    public function getDetailTask($taskId){
        $listdata = null;
        $data = collect([]);
        $payload = null;
        $history = null;
        $listAction = null;
        $assignees = null;
        $totalAssignee = 0;
        $statusApi = null;
        
        if ($taskId !== '') {
            $listDataResult = $this->findApiWorklistTaskDetail($taskId);

            if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
                $listdata = $listDataResult["result"];
                $assignees = str_replace("\n ", ",", $listdata["assigneesString"]);

                if ($listdata["payload"]) {
                    foreach ($listdata["payload"] as $value) {
                        $payload = preg_replace('!^[^>]+>!', '', $value);
                    }
                }

                $data->put('statusApiTaskDetail', 'success');
                $data->put('listdata', $listdata);
                $data->put('payload', $payload);
            } else {
                $statusApiTaskDetail = $listDataResult["result"];
                $data->put('listdata', $listdata);
                $data->put('payload', $payload);
                $data->put('statusApiTaskDetail', $statusApiTaskDetail);
            }

            //get list of action
            $cdcadmin = array('Contract_Management.ProcessOwner',
                'Fulfilment.ProcessOwner',
                'Order.ProcessOwner',
                'SourcingDP.ProcessOwner',
                'SourcingQT.ProcessOwner',
                'Supplier_Management.ProcessOwner',
                'Profile_Management.ProcessOwner');


            if ($listdata["taskId"] !== null && $assignees !== null) {
                $taskId = $listdata["taskId"];

                $getList = null;
                if (in_array($assignees, $cdcadmin)) {
                    $getList = $this->findAPIListTaskAction($taskId, 'cdcadmin');
                } else {
                    $arr = explode(",", $assignees, 2);
                    $first = $arr[0];
                    $getList = $this->findAPIListTaskAction($taskId, $first);
                }
            }

            if (isset($getList)) {
                if ($getList['status'] == 'Success') {
                    $data->put('statusListTask', 'success');
                    $listAction = $getList["result"];
                }else{
                    $statusListTask = $getList["result"];
                    $data->put('statusListTask', $statusListTask);
                }
            }

            //get task history
            $getHistory = $this->taskHistory($listdata["taskId"]);
            if ($getHistory['status'] == 'Success') {
                $history = $getHistory["result"];
                $data->put('statusHistory', 'success');
            } else {
                $statusHistory = $getHistory["result"];
                $data->put('statusHistory', $statusHistory);
            }
            
            $totalAssignee = count($listdata["assignees"]);
        }
        $data->put('listaction', $listAction);
        $data->put('history', $history);
        $data->put('totalassignee', $totalAssignee);
        $data->put('statusApi', $statusApi);
        $data->put('taskid',$taskId);

        return $data;
    }
}
