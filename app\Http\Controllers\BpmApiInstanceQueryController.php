<?php

namespace App\Http\Controllers;

use App\Services\Traits\BpmApiService;
use DB;
use Illuminate\Http\Request;
use Log;
use App\EpSupportActionLog;
use Carbon\Carbon;

class BpmApiInstanceQueryController extends Controller {

    use BpmApiService;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct() {
        $this->middleware('auth');
    }

    public function searchInstanceQuery(Request $request) {
        $statusApi = null;
        $listdataComposite = collect();
        $new_composite = [];
        $composite = null;
        $listdata = null;
        $state = null;
        $states = null;

        $dateFrom = null;
        $dateTo = null;
        $timeFrom = null;
        $timeTo = null;
        $isCompositeChecked = 0;
        $isCreatedChecked = 0;
        $isStateChecked = 0;
        $offset = 0;
        $limit = 50;
        
        $listComposite = $this->findApiBPMGetListComposite();
        if ($listComposite["status"] != null && $listComposite["status"] === 'Success') {
            $listdataComposite = $listComposite["result"];
        }else{
            $statusApi = $listComposite["result"];
        }

        if (isset($listdataComposite["data"])) {
            foreach ($listdataComposite["data"] as $data) {
                $new_composite[$data['composite']] = $data['composite'];
            }
        }

        session()->flashInput(request()->input());
        if ($request->isMethod("POST")) {   
            
            $isCompositeChecked = $request->has('composite_cbx');
            $isCreatedChecked = $request->has('created_cbx');
            $isStateChecked = $request->has('state_cbx');
            
            if($isCompositeChecked == 1){
                $composite = $request->composite;
            }
            
            if($isCreatedChecked == 1){
                $dateFrom = Carbon::parse($request->datefrom)->format('d-m-Y');
                $dateTo = Carbon::parse($request->dateto)->format('d-m-Y');
                $timeFrom = $request->time_from_hour . ':' . $request->time_from_min;
                $timeTo = $request->time_to_hour . ':' . $request->time_to_min;
            }
            
            if($isStateChecked == 1){
                $state = $request->input('state');
                if(isset($state)){
                    $states = implode(",",$state);
                }
                
            }
                       
            $listDataResult = $this->findApiBPMGetInstanceQuery($composite, $dateFrom, $dateTo, $timeFrom, $timeTo, $states, $offset, $limit);

            if($listDataResult["status"] != null && $listDataResult["status"] === 'Success'){
                $listdata = $listDataResult["result"];
            }else{
                $statusApi = $listDataResult["result"];
            }
        }
        
        return view('bpm_api.instance_query', [
            'status_api' => $statusApi,
            'composite' => $composite,
            'listComposite' => $new_composite,
            'composite' => $composite,
            'dateFrom' => Carbon::parse($dateFrom)->format('d-m-Y'),
            'dateTo' => Carbon::parse($dateTo)->format('d-m-Y'),
            'timeFromHour' => $request->time_from_hour,
            'timeFromMin' => $request->time_from_min,
            'timeToHour' => $request->time_to_hour,
            'timeToMin' => $request->time_to_min,
            'isCompositeChecked' => $isCompositeChecked,
            'isCreatedChecked' => $isCreatedChecked,
            'isStateChecked' => $isStateChecked,
            'state' => $state,
            'states' => $states,
            'listdata' => $listdata,
            'offset' => $offset,
            'limit' => $limit]);
    }
    
    public function searchNextInstanceQuery(Request $request) {

        $data = collect([]);
        $dateFrom = $request->dateFrom;
        $dateTo = $request->dateTo;
        $hourFrom = $request->hourFrom;
        $hourTo = $request->hourTo;
        $minFrom = $request->minFrom;
        $minTo = $request->minTo;
        $composite = $request->composite;
        $state = $request->state;
        $timeFrom = $hourFrom . ':' . $minFrom;
        $timeTo = $hourTo . ':' . $minTo;
        $offset = $request->newOffset;
        $limit = $request->limit;

        $listDataResult = $this->findApiBPMGetInstanceQuery($composite, $dateFrom, $dateTo, $timeFrom, $timeTo, $state, $offset, $limit);
        
        if ($listDataResult["status"] != null && $listDataResult["status"] === 'Success') {
            $data->put('status', 'success');
            $data->put('listdata',$listDataResult["result"]);
        } else {
            $data->put('status', 'failed');
            $data->put('statusApi', $listDataResult["result"]);
        }

        return $data;
    }

}
