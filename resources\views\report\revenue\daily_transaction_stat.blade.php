@extends('layouts.guest-dash')
@section('header')
    <div class="header-section">
        <h1>
            <i class="gi gi-charts"></i>Statistic Transaction Today PV / TR<br><small>Request date on {{$dateRequest}}</small>
        </h1>
    </div>
@endsection

@section('content')
<div class="row">
   <div class="col-md-12">
       <!-- Row Styles Block -->
    <div class="block">
        <!-- Row Styles Title -->
        <div class="block-title">
            <h2><strong>&nbsp;</strong> &nbsp;</h2>
        </div>
        <!-- END Row Styles Title -->

        <!-- Row Styles Content -->
        <div class="table-responsive">
            <table class="table table-vcenter table-bordered">
                <thead>
                    <tr style="background-color : blue; color:#fff; height:40px;">
                        <th class="text-center">&nbsp;</th>
                        <th class="text-center">Name</th>
                        <th class="text-center">Amount (RM)</th>
                        <th class="text-center">Total Docs.</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($dataResult as $indexKey => $row)
                    <tr class="active">
                        <td class="text-center bolder">{{ ++$indexKey }}</td>
                        <td class="text-center">{{$row->name}}</td>
                        <td class="text-center">{{number_format($row->sum_amount)}}</td>
                        <td class="text-center">{{number_format($row->total_doc)}}</td>
                    </tr>
                    @endforeach
                    
                </tbody>
            </table>
        </div>
        <!-- END Row Styles Content -->
    </div>
    <!-- END Row Styles Block -->

    </div> 
</div>
@endsection

@section('jsprivate')
<script>
    // Clear 
    $('#page-container').removeAttr('class');
</script>
            
</script>
@endsection