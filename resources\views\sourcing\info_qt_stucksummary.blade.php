@extends('layouts.guest-dash')

@section('header')
<!-- Search Form -->
<form id="carian_qt_form" action="{{url('find/qt/stucksummary')}}/" method="get" class="navbar-form-custom">
    <div class="form-group">
        <input type="text" id="ic_no" name="ic_no" value="{{$carian}}" class="form-control" onfocus="this.select();"
               placeholder="Masukkan No Kad Pengenalan & Mulakan Carian ...">
    </div>
</form>
<!-- END Search Form -->
@endsection

@section('content')
<div class="content-header">
</div>
@if($stuckinfo == null)
<div class="block block-alt-noborder full">
    <!-- Customer Addresses Block -->
    <div class="block">
        <div class="block-title panel-heading epss-title-s1">
            <h1><i class="fa fa-building-o"></i> <strong>Carian : {{$carian}}</strong></h1>
        </div>
        <div class="row">
            <div class="col-sm-6">
                <p>Tidak dijumpai!</p>
            </div>
        </div>
    </div>
</div>
@endif

<!--Akmal Region QT INFO & BRIEFING SITE VISIT-->
@if($stuckinfo)
<div class="block block-alt-noborder full">
    <!--AKMAL RSTUCK INFO-->
    <h4><strong> SENARAI SEMAK 'PENDING' TUGASAN </strong></h4>
    <div class="block">   
        <div class="block-title panel-heading epss-title-s1">
            <h5><i class="fa fa-building-o"></i><strong> USER INFO | NAME: <font color="yellow">{{ $stuckinfo->user_name }}</font></strong></h5>
            <h5><strong> USER ID: <font color="yellow">{{ $stuckinfo->user_id }}</font></strong></h5>
            <h5><strong> LOGIN ID: <font color="yellow">{{ $stuckinfo->login_id }}</font></strong></h5>
            <h5><strong> EMAIL: <font color="yellow">{{ $stuckinfo->email }}</font></strong></h5>
        </div>
        <div class="row">
            <div class="col-md-12">
                <h5><strong> USER ROLE </strong></h5>
                <table id="bsv-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">Org Code</th>
                            <th class="text-center">Org Name</th>
                            <th class="text-center">PTJ Code</th>      
                            <th class="text-center">Role Name</th>    
                            <th class="text-center">Create Date</th>    
                            <th class="text-center">Change Date</th>  
                        </tr>
                    </thead>
                    @foreach ($stuckrole as $key=>$srole)
                    <tr>
                        <td class="text-center">{{ $srole->org_code }}</td>
                        <td class="text-left">{{ $srole->org_name }}</td>
                        <td class="text-center">{{ $srole->code_name }}</td>
                        <td class="text-left">{{ $srole->role_name }}</td>
                        <td class="text-center">{{ $srole->created_date }}</td>
                        <td class="text-center">{{ $srole->changed_date }}</td>
                    </tr>
                    @endforeach                        
                </table>
                <h5><strong> PENDING TASK LIST </strong></h5>
                <table id="tracking-datatable" class="table table-vcenter table-condensed table-bordered">
                    <thead>
                        <tr>
                            <th class="text-center">QT NO</th>
                            <th class="text-center">JAWATANKUASA</th>
                            <!--<th class="text-center">SAHLAKU</th>-->
                            <th class="text-center">STATUS</th>
                            <th class="text-center">URUSETIA</th>
                            <th class="text-center">KEMENTERIAN</th>
                            <!--<th class="text-center">KUMPULAN_PTJ</th>-->
                            <th class="text-center">PEMILIK_QT</th>
                            <th class="text-center">CIPTA_UNTUK</th>
                            <th class="text-center">TAJUK</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($stucktask as $key=>$stask)
                        <tr>
                            <td class="text-center">{{ $stask->qtno }}</td>
                             <td class="text-center">{{ $stask->jawatankuasa }}</td>
                            <!--<td class="text-center">{{ $stask->validity }}</td>-->
                            <td class="text-center">{{ $stask->status_qt }}</td>
                            <td class="text-center">{{ $stask->deskofficer }}</td>
                            <td class="text-center">{{ $stask->kementerian }}</td>
                            <!--<td class="text-center">{{ $stask->kumpulanptj }}</td>-->
                            <td class="text-center">{{ $stask->milikptj }}</td>
                            <td class="text-center">{{ $stask->untukptj }}</td>
                            <td class="text-center">
                                <span class="less{{ $key }}">{{ str_limit($stask->qt_title, $limit = 28, $end = '...') }}</span>
                                <span class="details{{ $key }}" style="display:none">{{ $stask->qt_title }}</span>
                                <a id="more" href="#" onclick="$('.details{{ $key }}').slideToggle(function () {
                                                    $('#more').html($('.details{{ $key }}').is(':visible') ? 'See Less' : 'See More');$('.less{{ $key }}').slideToggle();
                                                                        });">See More</a>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
@section('jsprivate')
<script src="/js/pages/tablesDatatables.js"></script>
<script>$(function () {TablesDatatables.init(); });</script>

@endsection