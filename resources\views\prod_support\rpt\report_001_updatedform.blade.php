@extends('layouts.guest-dash')

@section('content')
<!--menu-->
<div class="block">
    <div class="content-header">
        <div class="header-section">
            <ul class="nav-horizontal text-center">
                <li>
                    <a href="{{ url('/prod-support/rpt/report_001_byyear') }}"><i class="fa fa-list-alt"></i><PERSON><PERSON><PERSON></a>
                </li>
                <li class="active">
                    <a href="{{ url('/prod-support/rpt/report_001_updatedform') }}"><i class="fa fa-cubes"></i>Kemaskini Data</a>
                </li>
                <li>
                    <a href="{{ url('/prod-support/rpt/report_001_statistic') }}"><i class="fa fa-area-chart"></i>Statistik</a>
                </li>
            </ul>
        </div>
    </div>
    <form action="{{ url('/prod-support/rpt/report_001_updatedform') }}" method="post">
        {{ csrf_field() }}

        <div class="block-title epss-title-s1">
            <h6><i class="fa fa-building-o"></i> KEMASKINI LAPORAN | PILIH TAHUN & BULAN UNTUK MULAKAN KEMASKINI DATA</h6>
        </div>
        <div class='row'>
            <div class="col-md-3">
                <select id="by_year" name = "by_year" required class="form-control" style="width: 200px;">
                    <option value="">SILA PILIH TAHUN</option>
                    @foreach($listYear as  $list)
                    <option value="{{$list->year}}">{{$list->year}}</option>
                    @endforeach
                </select>
            </div> 
            <div class="col-md-3">
                <select id="pickmonth" name = "pickmonth" required class="form-control" style="width: 200px;">  
                    <option value="Select">SILA PILIH BULAN</option>}  
                    @foreach($listMonth as  $list)
                    <option value="{{$list->month_no}}">{{$list->month}}</option>
                    @endforeach
                </select>
            </div>
            <div class="col-md-3">
                <div>
                    <button type="submit" class="form-control" size='4' ><strong>CARI</strong></button>
                </div>
            </div>
        </div>
    </form>

    <!--AKMAL REGION DETAILS INFO-->
    <!--<div class="modal-content">-->

    <div class="modal-header text-center">
        <font color="black"><h5 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header"><strong>KEMASKINI LAPORAN PERBENDAHARAAN SISTEM EPEROLEHAN (DALAM TALIAN)</strong></span></h5>
    </div>
    <div class="modal-body">
        <div class="row">
            <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                <thead>
                    <tr style="background-color:#B4EBFF">   
                        <th class="text-center">TAHUN | BULAN</th>
                        <th class="text-left">KATEGORI PERKHIDMATAN</th>
                        <th class="text-right">TOTAL</th>
                        <th class="text-center">TINDAKAN</th>
                    </tr>
                </thead>
                <tbody>
                    @if($readAllonline != null)
                    @foreach ($readAllonline as $data)
                    <tr>
                        <td class="text-center">{{ $data->year }} | {{ $data->month }}</td>
                        <td class="text-left">{{ $data->bil_no }} | {{ $data->service_name_bm }}</td>
                        <td class="text-right">{{ $data->total }}</td>
                        <td class="text-center">
                            <a href='#modal_list_edit_total'
                               class='modal-list-data-action'
                               idno ="{{$data->id}}" 
                               total ="{{$data->total}}" 
                               year ="{{ $data->year }}"
                               month ="{{ $data->month }}"
                               month_no ="{{ $data->month_no }}"
                               service_name_bm ="{{ $data->service_name_bm }}"
                               bil_no ="{{ $data->bil_no }}"
                               data-toggle='modal'  
                               data-toggle="tooltip" title="Edit" class="btn btn-default editbutton"><i class="fa fa-edit"></i></a>
                        </td>
                    </tr>
                    @endforeach
                    @endif
                </tbody>
            </table>
            <div id="modal_list_edit_total" class="modal fade" tabindex="-1" role="dialog" aria-hidden="true" style="display: none;" >
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <input type="hidden" id="editidno" name="" value="" class="form-control">
                        <div class="modal-header text-center">
                            <font color="black"><h2 class="modal-title"><i class="fa fa-info-circle"></i> <span id="modal-list-data-header">Kemaskini Data Dalam Talian</span></h2>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <table id="statuswork-datatable" class="table table-vcenter table-condensed table-bordered">
                                    <thead>
                                        <tr>
                                            <th class="text-center">Tahun</th>
                                            <th class="text-center">Bulan</th>
                                            <th class="text-center">Jenis Perkhidmatan</th>
                                            <th class="text-center">Data Asal</th>
                                            <th class="text-center">Data Baru</th>
                                            <th class="text-center">Tindakan</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-center"><span id='Dyear'></span></td>
                                            <td class="text-center"><span id='Dmonth'></span></td>
                                            <td class="text-center"><span id='Ddesc'></span></td>
                                            <td class="text-center"><span id='total_val'></span></td>
                                            <td class="text-center"><input id='total_val_new' name="total_val_new" value="" class="text-center" ></td>
                                            <td class="text-center"><button type="button" id="serahbutton" class="form-control" size='4'><strong>SIMPAN</strong></button></td>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('jsprivate')
<script type="text/javascript">
    $(".editbutton").on("click", function () {
        $('#modal_list_edit_total').show();
    });

    $(document).ready(function () {
        $('.modal-list-data-action').click(function () {
            $('#total_val_new').val("");
            let
            id = $(this).attr('idno');
            let
            year = $(this).attr('year');
            let
            month = $(this).attr('month');
            let
            month_no = $(this).attr('month_no');
            let
            service_name_bm = $(this).attr('service_name_bm');
            let
            bil_no = $(this).attr('bil_no');
            let
            total = $(this).attr('total');

            document.getElementById('Dyear').textContent = year;
            document.getElementById('Dmonth').textContent = month;
            document.getElementById('Ddesc').textContent = service_name_bm;
            document.getElementById('total_val').textContent = total;
            
            $('#editidno').val(id);
        });

    });
    
    $('#serahbutton').click(function () {
           var idno = $('#editidno').val();
           var newtotal = $('#total_val_new').val();
           console.log(newtotal);
            $.ajax({
                type: "POST",
                url: "/prod-support/rpt/report_001_updatedform1/" + idno,
                data: {
                    '_token': $('input[name=_token]').val(),
                    'idno': idno,
                    'newtotal' : newtotal
                },
            }).done(function (data) {
                console.log(data);
                location.reload();
            });
        });


</script>
@endsection        

