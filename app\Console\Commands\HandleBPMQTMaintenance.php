<?php

namespace App\Console\Commands;

use App\EpSupportActionLog;
use App\Services\Traits\BpmApiService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class HandleBPMQTMaintenance extends Command
{
    use BpmApiService;

    protected $signature = 'bpm:terminate-qt-maintenance-stuck {--execute : Actually execute the termination}';
    protected $description = 'Terminate stuck Quotation Tender Maintenance BPM';
    private $processableRecords = [];

    public function handle()
    {
        $this->info('Starting quotation workflow processing...');

        try {
            // First Query - Get Flow IDs
            $flowInstances = DB::connection('oracle_bpm_rpt')
                ->select("SELECT DISTINCT
                    c.CIKEY,
                    c.COMPONENT_NAME,
                    c.CMPST_ID,
                    c.COMPOSITE_NAME,
                    c.COMPOSITE_REVISION,
                    c.FLOW_ID,
                    c.TITLE,
                    c.STATE,
                    c.MODIFY_DATE
                FROM cube_instance c, SCA_FLOW_INSTANCE s
                WHERE c.FLOW_ID = s.FLOW_ID
                AND s.ACTIVE_COMPONENT_INSTANCES <> 0
                AND COMPOSITE_NAME = 'SourcingQT'
                AND c.STATE NOT IN (5, 8)
                AND c.FLOW_ID NOT IN (-1,-2)
                AND c.COMPONENT_NAME = 'QuotationTenderMaintenance'
                AND NOT EXISTS (
                    SELECT 1
                    FROM wftask w
                    WHERE w.COMPOSITENAME = c.COMPOSITE_NAME
                    AND w.STATE IN ('ASSIGNED', 'SUSPENDED')
                    AND w.COMPOSITEINSTANCEID = c.CMPST_ID
                )
                ORDER BY C.MODIFY_DATE DESC");

            foreach ($flowInstances as $instance) {
                $this->processFlowInstance($instance);
            }

            $this->displayResults();

        } catch (\Exception $e) {
            $this->error('Error occurred: ' . $e->getMessage());
            Log::error('Error in quotation workflow processing: ' . $e->getMessage());
        }
    }

    private function processFlowInstance($instance)
    {
        // Second Query - Get Document Numbers
        $documents = DB::connection('oracle_bpm_rpt')
            ->select("SELECT DISTINCT 
                CUSTOMATTRIBUTESTRING1 AS doc_no,
                CUSTOMATTRIBUTESTRING2 AS doc_type,
                PROTECTEDTEXTATTRIBUTE1 AS doc_no_other
            FROM wftask
            WHERE flow_id = ?
            AND (CUSTOMATTRIBUTESTRING1 IS NOT NULL
            OR PROTECTEDTEXTATTRIBUTE1 IS NOT NULL)",
                [$instance->flow_id]
            );

        if (empty($documents)) {
            Log::info("No documents found for Flow ID: {$instance->flow_id}");
            return;
        }

        // Validate that all documents have matching document numbers
        $firstDoc = $documents[0];
        $firstDocNo = $firstDoc->doc_no ?? $firstDoc->doc_no_other;

        foreach ($documents as $document) {
            $currentDocNo = $document->doc_no ?? $document->doc_no_other;

            if ($currentDocNo !== $firstDocNo) {
                Log::warning("Flow ID {$instance->flow_id} has inconsistent document numbers", [
                    'first_doc_no' => $firstDocNo,
                    'current_doc_no' => $currentDocNo
                ]);
                return;
            }
        }

        $this->processDocument($documents[0], $instance);
    }

    private function processDocument($document, $instance)
    {
        try {
            $docNo = $document->doc_no ?? $document->doc_no_other;

            if (!$docNo) {
                Log::info('No document number found, skipping...');
                return;
            }

            // Query to get Status
            $status = DB::connection('oracle_nextgen_rpt')
                ->select("SELECT
                    B.DOC_TYPE,
                    B.DOC_ID,
                    A.QT_NO,
                    A.CLOSING_DATE,
                    C.STATUS_ID,
                    D.STATUS_NAME,
                    A.QT_ID
                FROM SC_QT A
                INNER JOIN SC_WORKFLOW_STATUS B ON A.QT_ID = B.DOC_ID
                INNER JOIN PM_STATUS C ON B.STATUS_ID = C.STATUS_ID
                INNER JOIN PM_STATUS_DESC D ON C.STATUS_ID = D.STATUS_ID
                WHERE D.LANGUAGE_CODE = 'en'
                AND B.IS_CURRENT = 1
                AND B.DOC_TYPE = 'QT'
                AND A.QT_NO = ?
                ORDER BY B.CREATED_DATE DESC",
                    [$docNo]
                );

            if (empty($status)) {
                Log::info("No status found for document {$docNo}", [
                    'flow_id' => $instance->flow_id,
                    'instance_id' => $instance->cmpst_id
                ]);
                return;
            }

            $currentStatus = $status[0];

            // Skip if status 60008 or 60009
            if (in_array($currentStatus->status_id, [60008, 60009])) {
                Log::info("Skipping document {$docNo} due to status", [
                    'status_id' => $currentStatus->status_id,
                    'status_name' => $currentStatus->status_name
                ]);
                return;
            }

            // Only process if status is 60014 or 60015
            if (!in_array($currentStatus->status_id, [60014, 60015])) {
                Log::info("Skipping document {$docNo} as status is not 60014 or 60015", [
                    'status_id' => $currentStatus->status_id,
                    'status_name' => $currentStatus->status_name
                ]);
                return;
            }

            // Format dates for display
            $modifyDate = $instance->modify_date ?
                Carbon::parse($instance->modify_date)->format('Y-m-d H:i:s') :
                null;

            // If we reach here, this record needs processing
            $this->processableRecords[] = [
                'cmpst_id' => $instance->cmpst_id,
                'flow_id' => $instance->flow_id,
                'component_name' => $instance->component_name,
                'composite_name' => $instance->composite_name,
                'doc_no' => $docNo,
                'doc_id' => $currentStatus->doc_id,
                'qt_id' => $currentStatus->qt_id,
                'state' => $instance->state,
                'modify_date' => $modifyDate,
                'status_id' => $currentStatus->status_id,
                'status_name' => $currentStatus->status_name,
                'closing_date' => null,
                'eligible_for_termination' => false // Default to false, will be set later
            ];

            Log::info("Added document for processing", [
                'doc_no' => $docNo,
                'flow_id' => $instance->flow_id,
                'instance_id' => $instance->cmpst_id,
                'status_name' => $currentStatus->status_name,
                'status_id' => $currentStatus->status_id
            ]);

        } catch (\Exception $e) {
            Log::error("Error processing document", [
                'doc_no' => $docNo ?? 'unknown',
                'flow_id' => $instance->flow_id ?? 'unknown',
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    /**
     * Check if instance details contain the ApproveSupplierAdd component
     */
    private function hasApproveSupplierAddComponent($instanceDetails)
    {
        // Check if response is valid
        if (!isset($instanceDetails['status']) || $instanceDetails['status'] !== 'Success') {
            return false;
        }

        // Check if childs array exists
        if (!isset($instanceDetails['result']['childs']) || !is_array($instanceDetails['result']['childs'])) {
            return false;
        }

        // Function to recursively search for component name in the hierarchy
        $findComponent = function ($items, $targetComponent) use (&$findComponent) {
            foreach ($items as $item) {
                // Check current item
                if (isset($item['componentName']) && $item['componentName'] === $targetComponent) {
                    return true;
                }

                // Check children of current item if they exist
                if (isset($item['childs']) && is_array($item['childs']) && count($item['childs']) > 0) {
                    if ($findComponent($item['childs'], $targetComponent)) {
                        return true;
                    }
                }
            }
            return false;
        };

        // Search for ApproveSupplierAdd in the component hierarchy
        return $findComponent($instanceDetails['result']['childs'], 'ApproveSupplierAdd');
    }

    /**
     * Display component hierarchy in readable format
     */
    private function displayComponentHierarchy($components, $level = 0)
    {
        $indent = str_repeat("  ", $level);
        foreach ($components as $component) {
            $this->line($indent . "▸ " . ($component['componentName'] ?? 'Unknown') .
                " (" . ($component['componentType'] ?? 'Unknown') . ")" .
                " [Status: " . ($component['componentStatus'] ?? 'Unknown') . "]");

            if (isset($component['childs']) && is_array($component['childs']) && count($component['childs']) > 0) {
                $this->displayComponentHierarchy($component['childs'], $level + 1);
            }
        }
    }

    private function displayResults()
    {
        if (empty($this->processableRecords)) {
            $this->info('No records require processing.');
            Log::info('No records found for processing');
            return;
        }

        $this->info("\nFound " . count($this->processableRecords) . " records that require processing:");
        Log::info("Found records for processing", [
            'count' => count($this->processableRecords)
        ]);

        // First, get module information for all records
        foreach ($this->processableRecords as &$record) {
            try {
                $instanceDetails = $this->findAPIProcessManagerBPMByInstance($record['cmpst_id']);

                // Log the raw instance details response
                Log::info("Instance details response for {$record['doc_no']}", [
                    'instanceDetails' => $instanceDetails
                ]);

                $record['module'] = $instanceDetails['status'] === 'Success'
                    ? ($instanceDetails['result']['compositeModule'] ?? 'Unknown')
                    : 'Error getting module';

                // Check if this instance contains the ApproveSupplierAdd component
                $record['eligible_for_termination'] = $this->hasApproveSupplierAddComponent($instanceDetails);

                // Log eligibility
                Log::info("Termination eligibility for {$record['doc_no']}", [
                    'eligible' => $record['eligible_for_termination'] ? 'Yes' : 'No',
                    'reason' => $record['eligible_for_termination'] ?
                        'Contains ApproveSupplierAdd component' :
                        'Missing ApproveSupplierAdd component'
                ]);

                // Log each record's complete information
                Log::info("Processing record details", [
                    'record' => [
                        'composite_id' => $record['cmpst_id'],
                        'flow_id' => $record['flow_id'],
                        'component_name' => $record['component_name'],
                        'composite_name' => $record['composite_name'],
                        'document_no' => $record['doc_no'],
                        'document_id' => $record['doc_id'],
                        'qt_id' => $record['qt_id'],
                        'state' => $record['state'],
                        'modified_date' => $record['modify_date'],
                        'status_id' => $record['status_id'],
                        'status_name' => $record['status_name'],
                        'closing_date' => $record['closing_date'],
                        'module' => $record['module'],
                        'eligible_for_termination' => $record['eligible_for_termination']
                    ]
                ]);

            } catch (\Exception $e) {
                $record['module'] = 'Error: ' . $e->getMessage();
                $record['eligible_for_termination'] = false;
                Log::error("Error getting module information", [
                    'composite_id' => $record['cmpst_id'],
                    'document_no' => $record['doc_no'],
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
            }
        }
        unset($record); // Fix the reference issue

        $this->table(
            [
                'Composite ID',
                'Flow ID',
                'Component Name',
                'Document No',
                'State',
                'Modified Date',
                'Status',
                'Module',
                'Eligible',
                'Closing Date'
            ],
            array_map(function ($record) {
                return [
                    $record['cmpst_id'],
                    $record['flow_id'],
                    $record['component_name'],
                    $record['doc_no'],
                    $record['state'],
                    $record['modify_date'],
                    $record['status_name'],
                    $record['module'],
                    $record['eligible_for_termination'] ? 'Yes' : 'No',
                    $record['closing_date']
                ];
            }, $this->processableRecords)
        );

        // Add enhanced debug information
        if (!$this->option('execute')) {
            $this->info("\n=== DETAILED INSTANCE INFORMATION (DEBUG MODE) ===\n");

            foreach ($this->processableRecords as $index => $record) {
                $this->info("\nRecord #" . ($index + 1) . " - " . $record['doc_no']);
                $this->line("----------------------------------------");

                try {
                    // Get detailed instance information again for display
                    $instanceDetails = $this->findAPIProcessManagerBPMByInstance($record['cmpst_id']);

                    if ($instanceDetails['status'] === 'Success') {
                        $this->line("Composite ID: " . $record['cmpst_id']);
                        $this->line("Flow ID: " . $record['flow_id']);

                        // Display all instance details properties
                        $this->info("Instance Details:");
                        foreach ($instanceDetails['result'] as $key => $value) {
                            if (is_array($value) || is_object($value)) {
                                $this->line(" - {$key}: " . json_encode($value));
                            } else {
                                $this->line(" - {$key}: {$value}");
                            }
                        }

                        // Include additional document information
                        $this->info("Document Information:");
                        $this->line(" - Document No: " . $record['doc_no']);
                        $this->line(" - Document ID: " . $record['doc_id']);
                        $this->line(" - QT ID: " . $record['qt_id']);
                        $this->line(" - Status ID: " . $record['status_id']);
                        $this->line(" - Status Name: " . $record['status_name']);

                        // Add component analysis
                        $this->info("\nComponent Analysis:");
                        $this->line(" - Contains ApproveSupplierAdd: " .
                            ($record['eligible_for_termination'] ? 'Yes ✓' : 'No ✗'));

                        if (!$record['eligible_for_termination']) {
                            $this->line(" - Action: Will skip termination (missing ApproveSupplierAdd component)");
                        } else {
                            $this->line(" - Action: Will terminate if --execute is provided");
                        }

                        // Component hierarchy visualization
                        $this->info("\nComponent Hierarchy:");
                        if (isset($instanceDetails['result']['childs'])) {
                            $this->displayComponentHierarchy($instanceDetails['result']['childs']);
                        } else {
                            $this->line(" No child components found");
                        }

                    } else {
                        $this->error("Failed to get instance details: " . json_encode($instanceDetails['result']));
                    }
                } catch (\Exception $e) {
                    $this->error("Error retrieving instance details: " . $e->getMessage());
                }

                $this->line("----------------------------------------\n");
            }

            $this->warn("\nThis is a dry run. Use --execute flag to actually terminate the instances.");
            $this->info("Example: php artisan bpm:terminate-qt-maintenance-stuck --execute");
            Log::info("Dry run completed - no actions taken");
        } else {
            if ($this->confirm('Are you sure you want to terminate these instances?')) {
                $this->info("\nProcessing records...");
                Log::info("Starting termination process", [
                    'record_count' => count($this->processableRecords)
                ]);

                $bar = $this->output->createProgressBar(count($this->processableRecords));
                $bar->start();

                foreach ($this->processableRecords as $record) {
                    try {
                        Log::info("Attempting to terminate instance", [
                            'composite_id' => $record['cmpst_id'],
                            'document_no' => $record['doc_no'],
                            'module' => $record['module'],
                            'eligible' => $record['eligible_for_termination']
                        ]);

                        $this->processRequiredDocument($record);

                        $this->info("\nProcessed document: {$record['doc_no']} (Module: {$record['module']})");
                        Log::info("Processed instance", [
                            'composite_id' => $record['cmpst_id'],
                            'document_no' => $record['doc_no'],
                            'module' => $record['module'],
                            'eligible' => $record['eligible_for_termination']
                        ]);
                    } catch (\Exception $e) {
                        $this->error("\nFailed to process document {$record['doc_no']} (Module: {$record['module']}): {$e->getMessage()}");
                        Log::error("Failed to terminate instance", [
                            'composite_id' => $record['cmpst_id'],
                            'document_no' => $record['doc_no'],
                            'module' => $record['module'],
                            'error' => $e->getMessage(),
                            'trace' => $e->getTraceAsString()
                        ]);
                    }
                    $bar->advance();
                }

                $bar->finish();
                $this->info("\nProcessing completed!");
                Log::info("Termination process completed");
            } else {
                $this->info('Operation cancelled by user.');
                Log::info("Operation cancelled by user");
            }
        }
    }

    private function processRequiredDocument($record)
    {
        try {
            $instanceId = $record['cmpst_id'];

            // Check if record is eligible for termination
            if (!isset($record['eligible_for_termination']) || !$record['eligible_for_termination']) {
                Log::info("Skipping termination for instance - doesn't contain ApproveSupplierAdd component", [
                    'instance_id' => $instanceId,
                    'doc_no' => $record['doc_no']
                ]);

                $this->line("\nSkipping termination for {$record['doc_no']} - missing ApproveSupplierAdd component");
                return;
            }

            // First get the composite module
            $compositeInfo = $this->findApiBPMGetListComposite();
            if ($compositeInfo['status'] !== 'Success') {
                Log::error('Failed to get composite info', [
                    'instance_id' => $instanceId,
                    'error' => $compositeInfo['result']
                ]);
                return;
            }

            // Get instance details to determine the module
            $instanceDetails = $this->findAPIProcessManagerBPMByInstance($instanceId);
            if ($instanceDetails['status'] !== 'Success') {
                Log::error('Failed to get instance details', [
                    'instance_id' => $instanceId,
                    'error' => $instanceDetails['result']
                ]);
                return;
            }

            Log::info('Instance details retrieved', [
                'instance_id' => $instanceId,
                'instance_details' => $instanceDetails
            ]);

            $module = $instanceDetails['result']['compositeModule'] ?? null;
            if (!$module) {
                Log::error('No composite module found for instance', [
                    'instance_id' => $instanceId
                ]);
                return;
            }

            $flowId = $record['flow_id'];
            $docNo = $record['doc_no'];
            $actionTypeLog = 'Console Command';

            // Prepare parameters for logging
            $parameters = collect([
                'instance_id' => $instanceId,
                'module' => $module,
                'action_task' => 'terminate-task',
                'host_ws' => env('JAVA_MIDDLEWARE_RESTFUL')
            ]);

            // Log the action
            $actionName = 'BPM-Terminate-Task';
            $actionLog = EpSupportActionLog::saveActionLog(
                $actionName,
                $actionTypeLog,
                $parameters,
                $parameters,
                'Processing'
            );

            // Submit termination request
            $terminationResult = $this->submitTerminateInstance($module, $instanceId);

            if ($terminationResult['status'] === 'Success') {
                // Update document number in WFTASK table if it's a QT document
                if (strpos($docNo, 'QT') === 0) {
                    try {
                        DB::connection('oracle_nextgen_soa_fullgrant')
                            ->table('wftask')
                            ->where('flow_id', $flowId)
                            ->where('COMPOSITENAME', 'SourcingQT')
                            ->where('CUSTOMATTRIBUTESTRING1', $docNo)
                            ->update([
                                'CUSTOMATTRIBUTESTRING1' => 'QTX' . substr($docNo, 2)
                            ]);

                        Log::info("Successfully terminated and updated instance", [
                            'instance_id' => $instanceId,
                            'doc_no' => $docNo,
                            'flow_id' => $flowId,
                            'module' => $module
                        ]);

                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, 'Completed');
                    } catch (\Exception $e) {
                        Log::error('Error updating WFTASK table: ' . $e->getMessage());
                        EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, 'Failed');
                        throw $e;
                    }
                }
            } else {
                Log::error('Failed to terminate instance', [
                    'instance_id' => $instanceId,
                    'module' => $module,
                    'result' => $terminationResult['result']
                ]);
                EpSupportActionLog::updateActionNameLogData($actionLog, $actionName, 'Failed');
            }

        } catch (\Exception $e) {
            Log::error('Error in processRequiredDocument: ' . $e->getMessage());
            throw $e;
        }
    }
}