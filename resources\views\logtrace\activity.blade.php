@extends('layouts.guest-dash')

@section('cssprivate')
    <link href="{{ asset('css/logtrace-v1.0.css') }}" rel="stylesheet" />
@endsection

@section('content')
    <div class="block log-header">
        <div class="row">
            <div class="col-md-1">
                <div class="log-trace-icon">eP</div>
            </div>
            <div class="col-md-4 log-header-title">
                <span>User Activity Log<br /></span>
                <small>To Track Historical User Activity Logs To Identify Anomalies And Usage Patterns.</small>
            </div>
            <div class="col-md-6 log-header-menu">
                <a href="/home"><i class="fa fa-home"></i></a> |
                <a href="/log/dashboard">Dashboard</a> |
                <span class="active">User Activity</span> |
                <a href="/log/login-history">Login History</a> | 
                @if (Auth::user()->isDevUsersEp()) <a href="/log/patch">Patch
                        Management</a> @endif
            </div>
        </div>
    </div>
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="block log-search-panel">
        <div class="card-header">
            <i class="fa fa-search"></i> <strong>Search</strong> Log
        </div>
        <div class="card-body">
            <form class="form-horizontal">
                {{ csrf_field() }}
                <div class="row">
                    <div class="col-md-6">
                        <fieldset>
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="user">User ID </label>
                                <div class="col-md-6">
                                    <input type="text" id="user_id" name="user_id" class="form-control" @if ($user_id !== null) value="{{ $user_id }}" @else value="{{ old('user_id') }}" @endif required=""><br />
                                    <small id="fail_status" style="display: none; color: red;"><b><i
                                                class="fa fa-times-circle"></i></i> Oops! <span
                                                id="fail_msg"></span></b></small>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="col-md-6">
                        <fieldset>
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="user">Session ID </label>
                                <div class="col-md-6">
                                    <input type="text" id="session_id" name="session_id" class="form-control" @if ($session_id !== null) value="{{ $session_id }}" @else value="{{ old('session_id') }}" @endif>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="row">
                    <?php $now = Carbon\Carbon::now()->format('d-m-Y'); ?>
                    <div class="col-md-6">
                        <fieldset>
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="code">Log From </label>
                                <div class="col-md-3">
                                    <input type="text" id="dt_from" class="form-control" name="dt_from"
                                        style="padding: 5px; text-align: center;" @if ($dt_from !== null) value="{{ $dt_from }}" @else value="{{ $now }}" @endif>
                                </div>
                                <div class="col-md-3">
                                    <div class="input-group bootstrap-timepicker">
                                        <input id="ts_from" name="ts_from" type="text" style="text-align: center;"
                                            class="form-control input-timepicker24" @if ($ts_from !== null) value="{{ $ts_from }}" @else value="{{ $now }}" @endif>
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                    <div class="col-md-6">
                        <fieldset>
                            <div class="form-group">
                                <label class="col-md-4 control-label" for="code">Log To </label>
                                <div class="col-md-3">
                                    <input type="text" id="dt_to" class="form-control" name="dt_to"
                                        style="padding: 5px; text-align: center;" @if ($dt_to !== null) value="{{ $dt_to }}" @else value="{{ $now }}" @endif disabled>
                                </div>
                                <div class="col-md-3">
                                    <div class="input-group bootstrap-timepicker">
                                        <input id="ts_to" name="ts_to" type="text" style="text-align: center;"
                                            class="form-control input-timepicker24" @if ($ts_to !== null) value="{{ $ts_to }}" @else value="{{ $now }}" @endif>
                                        <span class="input-group-addon"><span class="fa fa-clock-o"></span></span>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
                <div class="row" style="padding-right: 200px;">
                    <span class="pull-right">
                        <button type="button" name="searchlog" id="searchlog" class="btn btn-sm"><i
                                class="fa fa-search"></i>
                            Search</button>
                    </span>
                </div>
            </form>
        </div>
    </div>
    <div class="block" id="log-bg-table">
        <table id="log_table" class="table table-condensed">
            <thead>
                <tr>
                    <th class="text-center">Log Date</th>
                    <th class="text-center">User ID</th>
                    <th class="text-center">Request Method</th>
                    <th class="text-center">Friendly URL</th>
                    <th class="text-center">Portlet ID</th>
                    <th class="text-center">Request URL</th>
                    <th class="text-center">Server Node</th>
                    <th class="text-center"></th>
                </tr>
            </thead>
        </table>
    </div>
    <div class="modal fade" id="log_details" tabindex="-1" role="dialog" aria-hidden="true">
        <div class="modal-dialog modal-md" role="document">
            <div class="modal-content">
                <div class="modal-header text-center">
                    <h2 class="modal-title"><i class="fa fa-info-circle"></i> Log Details</h2>
                </div>
                <div class="modal-body">
                    <table id="log_det" class="table table-bordered table-condensed table-sm"></table>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-sm" type="button" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('jsprivate')
    <script>
        var APP_URL = {!! json_encode(url('/')) !!}

        function search_logs(req) {
            $('#log_table').dataTable({
                "destroy": true,
                "processing": true,
                "serverSide": true,
                "order": [
                    [0, "asc"]
                ],
                "lengthMenu": [
                    [10, 20, 50, 100, -1],
                    [10, 20, 50, 100, 'All']
                ],
                "ajax": {
                    "url": "/log/",
                    "data": {
                        "_token": $('input[name=_token]').val(),
                        "user_id": req.user_id,
                        "session_id": req.session_id,
                        "dt_from": req.dt_from,
                        "ts_from": req.ts_from,
                        "dt_to": req.dt_to,
                        "ts_to": req.ts_to,
                    },
                    "type": "POST"
                },
                "columns": [{
                        "data": "log_dt"
                    },
                    {
                        "data": "login_id"
                    },
                    {
                        "data": "request_method"
                    },
                    {
                        "data": "friendly_url"
                    },
                    {
                        "data": "portlet_id"
                    },
                    {
                        "data": "request_url"
                    },
                    {
                        "data": "server_node"
                    },
                    {
                        "data": null
                    }
                ],
                columnDefs: [{
                        targets: [1],
                        render: function(data, type, row) {
                            return `<a href="{{ url('/find/userlogin?login_id=${data}') }}" target="_blank">${data }</a>`
                        }
                    },
                    {
                        targets: [2],
                        render: function(data, type, row) {
                            if (data === 'GET')
                                return `<span class="label label-success">${data}</span>`
                            else
                                return `<span class="label label-info">${data}</span>`
                        }
                    },
                    {
                        targets: [7],
                        render: function(data, type, row) {
                            let userAgent = row.user_agent.replaceAll(" ", "_");
                            return `<div class='btn-group btn-group-xs'><a data-target='#log_details' 
                            data-logdt=${row.log_dt} 
                            data-loginid=${row.login_id} 
                            data-sessionid=${row.session_id} 
                            data-requestmethod=${row.request_method} 
                            data-friendlyurl=${row.friendly_url} 
                            data-requesturl=${row.request_url} 
                            data-responsestatus=${row.response_status} 
                            data-portletid=${row.portlet_id} 
                            data-portletpage=${row.portlet_page} 
                            data-useragent=${userAgent} 
                            data-servername=${row.server_name} 
                            data-servernode=${row.server_node} 
                            data-environment=${row.environment} 
                            data-toggle='modal' title='More Info' class='js_log_dtl badge badge-info'>more</a></div>`;
                        }
                    },
                    {
                        targets: [5],
                        className: "log_word_break",
                    }, {
                        targets: [0, 1, 2, 3, 4, 6, 7],
                        className: "log_text_center",
                    }
                ]
            });

            $('#log_table tbody').on('click', 'td a.js_log_dtl', function(e) {
                data = {
                    "log_dt": $(this).attr('data-logdt'),
                    "login_id": $(this).attr('data-loginid'),
                    "session_id": $(this).attr('data-sessionid'),
                    "request_method": $(this).attr('data-requestmethod'),
                    "friendly_url": $(this).attr('data-friendlyurl'),
                    "request_url": $(this).attr('data-requesturl'),
                    "response_status": $(this).attr('data-responsestatus'),
                    "portlet_id": $(this).attr('data-portletid'),
                    "portlet_page": $(this).attr('data-portletpage'),
                    "user_agent": $(this).attr('data-useragent'),
                    "server_name": $(this).attr('data-servername'),
                    "server_node": $(this).attr('data-servernode'),
                    "environment": $(this).attr('data-environment'),
                }

                populate_log_detail(data);
            });
        }

        function populate_log_detail(log) {
            if (log) {
                var log_tbl = `<tbody>`;
                log_tbl += `<tr><td style="width:30%;">Log Date</td><td>${log.log_dt}</td></tr>`;
                log_tbl += `<tr><td>Login ID</td><td>${log.login_id}</td></tr>`;
                log_tbl += `<tr><td>Session ID</td><td>${log.session_id}</td></tr>`;
                log_tbl += `<tr><td>Request Method</td><td>${log.request_method}</td></tr>`;
                log_tbl += `<tr><td>Friendly URL</td><td>${log.friendly_url}</td></tr>`;
                log_tbl +=
                    `<tr style="word-break: break-all;"><td>Request URL</td><td>${log.request_url}</td></tr>`;

                if (log.response_status == '200')
                    log_tbl +=
                    `<tr><td>Response Status</td><td><span class="label label-success">${log.response_status}</span></td></tr>`;
                else
                    log_tbl +=
                    `<tr><td>Response Status</td><td><span class="label label-danger">${log.response_status}</span></td></tr>`;


                log_tbl += `<tr><td>Portlet ID</td><td>${log.portlet_id}</td></tr>`;
                log_tbl +=
                    `<tr style="word-break: break-all;"><td>Portlet Page</td><td>${log.portlet_page}</td></tr>`;
                log_tbl += `<tr style="word-break: break-all;"><td>User-Agent</td><td>${log.user_agent}</td></tr>`;
                log_tbl += `<tr><td>Server Name</td><td>${log.server_name}</td></tr>`;
                log_tbl += `<tr><td>Server Node</td><td>${log.server_node}</td></tr>`;
                log_tbl += `<tr><td>Environment</td><td>${log.environment}</td></tr>`;
                log_tbl += `</tbody>`;

                $('#log_det').html(log_tbl).fadeIn();
            }
        }

        function showHideAlertMessage(id, element, msg) {
            $('#' + id).css('display', 'block');
            document.getElementById(element).innerHTML = msg;
            setTimeout(function() {
                $('#' + id).css('display', 'none');
            }, 3000);
        }

        $(function() {
            $('#page-container').removeAttr('class');

            $("#dt_from").datepicker({
                format: 'yyyy-mm-dd'
            });

            $("#dt_to").datepicker({
                format: 'yyyy-mm-dd'
            });

            $('#dt_from').on('change', function() {
                let select_dt = $(this).val();

                $('#dt_to').val(select_dt);
            })

            $('#searchlog').on('click', function() {
                let request = {
                    user_id: $('#user_id').val(),
                    session_id: $('#session_id').val(),
                    dt_from: $('#dt_from').val(),
                    ts_from: $('#ts_from').val(),
                    dt_to: $('#dt_to').val(),
                    ts_to: $('#ts_to').val(),
                }

                if (request.user_id === '' || request.user_id == null) {
                    showHideAlertMessage('fail_status', 'fail_msg', 'Please fill in User Id.');
                    return;
                }

                search_logs(request);
            })

        });
    </script>
@endsection
