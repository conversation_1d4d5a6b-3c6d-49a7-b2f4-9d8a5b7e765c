@extends('layouts.guest-dash')

@section('header')
@endsection

@section('content')
<div class="content-header">
    <ul class="nav-horizontal text-center">
        <li>
            <a href="{{ url('/spki/digicert/challengequestion') }}"><i class="fa fa-tasks"></i> Challenge Question</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/updatechallengequestion') }}"><i class="fa fa-pencil-square-o"></i> Update Challenge Question</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/changepin') }}"><i class="fa fa-pencil"></i> Change PIN</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/resetpin') }}"><i class="fa fa-refresh"></i> Reset PIN</a>
        </li>
        <li class="active">
            <a href="{{ url('/spki/digicert/signing') }}"><i class="fa fa-sign-in"></i> Sign In</a>
        </li>
        <li>
            <a href="{{ url('/spki/digicert/revoke') }}"><i class="fa fa-ban"></i> Revoke Cert</a>
        </li>
    </ul>
</div>
<div class="block block-alt-noborder full">
    <div class="row">
        <div class="col-lg-6">
            <div class="block">
                <div class="block-title">
                    <h2>DIGICERT : SIGN IN</h2>
                    <h2 class="pull-right"><span><a style="color:darkblue;font-weight: bolder;text-decoration: underline;" href="{{ url('/spki/trustgate/challengequestion') }}"> SWITCH TRUSTGATE  </a></span></h2>
                </div>
                <h5 class="alert alert-danger" id="failed" style="display:none;"></h5>
                <div class="block">
                    <form class="form-horizontal form-bordered" id="form_sign" name="form_sign" action="post" onsubmit="return false;"> 
                        {{ csrf_field() }}  
                        <div class="form-group">
                            <label class="col-lg-3 control-label" for="ic_no">IC Number</label>
                            <div class="col-lg-9">
                                <input type="text" id="ic_no" name="ic_no" class="form-control" required="true" placeholder="5050505050506" value="{{ old('ic_no') }}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-3 control-label" for="ep_no">EP Number</label>
                            <div class="col-lg-9">
                                <input type="text" id="ep_no" name="ep_no" class="form-control" required="true" placeholder="gombak2026" value="{{ old('ep_no') }}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-lg-3 control-label" for="selected_question">Selected Question</label>
                            <div class="col-lg-9">
                                <input type="text" id="selected_question" name="selected_question" class="form-control" required="true" placeholder="2" value="{{ old('selected_question') }}"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <center>
                                <div class="input-group">
                                    <input type="button" id="sign_in_btn" name="sign_in_btn" value="Sign In" class="btn btn-sm btn-primary"/>
                                </div>
                            </center>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-6" id="sign_in_div" style="display: none">
            <div class="block">
                <div class="block-title">
                    <h2>Sign In </h2>
                </div>
                <h5 class="alert alert-success" id="sign_status_success" style="display:none;"></h5>
                <h5 class="alert alert-danger" id="sign_status_error" style="display:none;"></h5>
            </div>
        </div>
    </div>
</div>
<!--MODAL BOX CHALLENGE QUESTION-->
<div id="modal_challenge_question" class="modal fade">
    <div class="modal-dialog modal-md">
        <div class="modal-content">
            <div class="modal-header text-center">
                <h5> Please answer the challenge response and key in softcert PIN number</h5>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <input type="hidden" id="question" name="question"/>
                    <label id="label_challenge_question" name="label_challenge_question" class="col-lg-4 control-label" for="answer"></label>
                    <div class="col-lg-8">
                        <input type="text" id="answer" name="answer" class="form-control" placeholder="f"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg-4 control-label" for="pin_no">Soft Cert PIN Number :</label>
                    <div class="col-lg-8">
                        <input type="text" id="pin_no" name="pin_no" class="form-control" placeholder="a1234567"/>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg-4 control-label" for="data">Data :</label>
                    <div class="col-lg-8">
                        <textarea id="data" name="data" class="form-control" placeholder="Test on functionality" required="true"></textarea>
                    </div>
                </div>
            </div> <br/><br/><br/><br/><br/><br/><br/><br/>
            <div class="modal-footer">
                <button type="button" id="sign_btn" class="btn btn-sm btn-info pull-right">SIGN</button>
            </div>
        </div>
    </div>
</div>
<!--MODAL BOX CHALLENGE QUESTION-->
@endsection


@section('jsprivate')
<!-- Load and execute javascript code used only in this page -->
<script>

    $(document).ready(function () {

        $('#sign_in_btn').on('click', function () {

            var valid = $("#form_sign").valid();
            if (valid === true) {
                var csrf = $("input[name=_token]").val();
                var icNumber = $("input[name=ic_no]").val();
                var epNumber = $("input[name=ep_no]").val();
                var selectedQuestion = $("input[name=selected_question]").val();

                $.ajax({
                    type: "POST",
                    url: "/spki/digicert/signing",
                    data: {"_token": csrf, "icNumber": icNumber, "epNumber": epNumber, "selectedQuestion": selectedQuestion}
                }).done(function (resp) {
                    // console.log(resp);
                    if (resp["status"] === 'Success') {

                        $('#label_challenge_question').html(resp["result"][selectedQuestion]);
                        
                        if(resp["result"][selectedQuestion]) {
                            $('#modal_challenge_question').modal('show');
                            $("input[name=question]").val(resp["result"][selectedQuestion]);

                            $('#sign_btn').on('click', function () {
                                //call api sign in here
                                $('#modal_challenge_question').modal('hide');
                                $('#sign_in_div').show();
                            });
                            $('#failed').hide();
                        }else{
                            $('#failed').show();
                            document.getElementById("failed").innerHTML = "Selected Question Not Exist.. Please Select Another Question Between 0 to " + (resp["result"].length - 1) ;
                        }
                        
                    } else {
                        $('#failed').show();
                        document.getElementById("failed").innerHTML = resp["result"];
                    }
                });
            }

        });
        $('#sign_btn').on('click', function () {
            $('#sign_in_div').hide();
            
            var csrf = $("input[name=_token]").val();
            var icNumber = $("input[name=ic_no]").val();
            var epNumber = $("input[name=ep_no]").val();
            var question = $("input[name=question]").val();
            var answer = $("input[name=answer]").val();
            var pin = $("input[name=pin_no]").val();
            var data = $("textarea#data").val();

//            console.log("icNumber " + icNumber + " epNumber " + epNumber + " question " + question + " answer " + answer + " pin " + pin + " data " + data);

            $.ajax({
                type: "POST",
                url: "/spki/digicert/signing/data",
                data: {"_token": csrf, "icNumber": icNumber, "epNumber": epNumber, "question": question, "answer": answer, "pin": pin, "data": data}
            }).done(function (resp) {
                if (resp["result"]["code"] === 'null') {
                    $('#sign_status_success').hide();
                    $('#sign_status_error').show();
                    $('#sign_status_error').html(resp["result"]["message"]);
                } else {
                    $('#sign_status_error').hide();
                    $('#sign_status_success').show();
                    $('#sign_status_success').html(resp["result"]["message"]);
                }

            });
        });
    });

</script>
@endsection
